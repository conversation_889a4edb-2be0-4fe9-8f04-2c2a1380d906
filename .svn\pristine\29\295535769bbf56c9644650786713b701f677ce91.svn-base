﻿//using OfficeOpenXml;
//using OfficeOpenXml.Style;

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace OCRTools.Common
{
    public class ExcelHelper
    {
        private const string START_EXCEL_XML =
            "<xml version><Workbook xmlns=\"urn:schemas-microsoft-com:office:spreadsheet\" xmlns:o=\"urn:schemas-microsoft-com:office:office\" xmlns:x=\"urn:schemas-microsoft-com:office:excel\" xmlns:ss=\"urn:schemas-microsoft-com:office:spreadsheet\"><Styles><Style ss:ID=\"Default\" ss:Name=\"Normal\"><Alignment ss:Vertical=\"Bottom\"/><Borders/><Font/><Interior/><NumberFormat/><Protection/></Style><Style ss:ID=\"BoldColumn\"><Font x:Family=\"Swiss\" ss:Bold=\"1\"/></Style><Style ss:ID=\"StringLiteral\"><NumberFormat ss:Format=\"@\"/></Style><Style ss:ID=\"Decimal\"><NumberFormat ss:Format=\"0.0000\"/></Style><Style ss:ID=\"Integer\"><NumberFormat ss:Format=\"0\"/></Style><Style ss:ID=\"DateLiteral\"><NumberFormat ss:Format=\"mm/dd/yyyy;@\"/></Style></Styles>";

        public static void ExportCsv(DataTable table, string fileName)
        {
            if (table != null)
            {
                if (fileName.EndsWith("csv"))
                    ExportToCsv(table, fileName);
                else
                    ExportToExcel(table, fileName);
                //ExportExcel(table, fileName);
            }
        }

        public static void ExportCsv(DataGridView dataGridView, string fileName)
        {
            if (dataGridView.DataSource is DataTable table)
            {
                if (fileName.EndsWith("csv"))
                    ExportToCsv(table, fileName);
                else
                    ExportToExcel(table, fileName);
                //ExportExcel(table, fileName);
            }
        }

        /// <summary>
        ///     导出CSV格式文件
        /// </summary>
        /// <param name="dataTable">数据表</param>
        /// <param name="fileName">文件名</param>
        private static void ExportToCsv(DataTable dataTable, string fileName)
        {
            using (var streamWriter = new StreamWriter(fileName, false, Encoding.GetEncoding("gb2312")))
            {
                streamWriter.WriteLine(GetCsvFormatData(dataTable).ToString());
                streamWriter.Flush();
                streamWriter.Close();
            }
        }

        /// <summary>
        ///     通过DataTable获得CSV格式数据
        /// </summary>
        /// <param name="dataTable">数据表</param>
        /// <returns>CSV字符串数据</returns>
        private static StringBuilder GetCsvFormatData(DataTable dataTable)
        {
            var stringBuilder = new StringBuilder();
            // 写出数据
            var count = 0;
            foreach (DataRowView dataRowView in dataTable.DefaultView)
            {
                count++;
                foreach (DataColumn dataColumn in dataTable.Columns)
                {
                    var field = dataRowView[dataColumn.ColumnName].ToString();

                    if (field.IndexOf('"') >= 0) field = field.Replace("\"", "\"\"");
                    field = field.Replace("  ", " ");
                    if (field.IndexOf(',') >= 0 || field.IndexOf('"') >= 0 || field.IndexOf('<') >= 0 ||
                        field.IndexOf('>') >= 0 || field.IndexOf("'") >= 0) field = "\"" + field + "\"";
                    stringBuilder.Append(field + ",");
                }

                if (count != dataTable.Rows.Count) stringBuilder.Append("\n");
            }

            return stringBuilder;
        }

        /// <summary>
        ///     List转DataTable
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="data"></param>
        /// <returns></returns>
        public static DataTable ListToDataTable<T>(List<T> data)
        {
            var properties = TypeDescriptor.GetProperties(typeof(T));
            var dataTable = new DataTable();
            for (var i = 0; i < properties.Count; i++)
            {
                var property = properties[i];
                dataTable.Columns.Add(property.Name,
                    Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType);
            }

            var values = new object[properties.Count];
            foreach (var item in data)
            {
                for (var i = 0; i < values.Length; i++) values[i] = properties[i].GetValue(item);
                dataTable.Rows.Add(values);
            }

            return dataTable;
        }

        private static void ExportToExcel(DataTable source, string fileName)
        {
            var excelDoc = new StreamWriter(fileName);
            const string endExcelXml = "</Workbook>";

            var rowCount = 0;
            var sheetCount = 1;

            #region ExcelModel

            /*
           <xml version>
           <Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
           xmlns:o="urn:schemas-microsoft-com:office:office"
           xmlns:x="urn:schemas-microsoft-com:office:excel"
           xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet">
           <Styles>
           <Style ss:ID="Default" ss:Name="Normal">
             <Alignment ss:Vertical="Bottom"/>
             <Borders/>
             <Font/>
             <Interior/>
             <NumberFormat/>
             <Protection/>
           </Style>
           <Style ss:ID="BoldColumn">
             <Font x:Family="Swiss" ss:Bold="1"/>
           </Style>
           <Style ss:ID="StringLiteral">
             <NumberFormat ss:Format="@"/>
           </Style>
           <Style ss:ID="Decimal">
             <NumberFormat ss:Format="0.0000"/>
           </Style>
           <Style ss:ID="Integer">
             <NumberFormat ss:Format="0"/>
           </Style>
           <Style ss:ID="DateLiteral">
             <NumberFormat ss:Format="mm/dd/yyyy;@"/>
           </Style>
           </Styles>
           <Worksheet ss:Name="Sheet1">
           </Worksheet>
           </Workbook>
           */

            #endregion

            excelDoc.Write(START_EXCEL_XML);
            excelDoc.Write("<Worksheet ss:Name=\"Sheet" + sheetCount + "\">");
            excelDoc.Write("<Table>");
            //excelDoc.Write("<Row>");
            //for (int x = 0; x < source.Columns.Count; x++)
            //{
            //    excelDoc.Write("<Cell ss:StyleID=\"BoldColumn\"><Data ss:Type=\"String\">");
            //    excelDoc.Write(source.Columns[x].ColumnName);
            //    excelDoc.Write("</Data></Cell>");
            //}
            //excelDoc.Write("</Row>");
            foreach (DataRow x in source.Rows)
            {
                rowCount++;
                //if the number of rows is > 64000 create a new page to continue output
                if (rowCount == 64000)
                {
                    rowCount = 0;
                    sheetCount++;
                    excelDoc.Write("</Table>");
                    excelDoc.Write(" </Worksheet>");
                    excelDoc.Write("<Worksheet ss:Name=\"Sheet" + sheetCount + "\">");
                    excelDoc.Write("<Table>");
                }

                excelDoc.Write("<Row>"); //ID=" + rowCount + "
                for (var y = 0; y < source.Columns.Count; y++)
                {
                    var rowType = x[y].GetType();
                    var xmLstring = x[y].ToString();
                    switch (rowType.ToString())
                    {
                        case "System.String":
                            xmLstring = xmLstring.Trim();
                            xmLstring = xmLstring.Replace("&", "&");
                            xmLstring = xmLstring.Replace(">", ">");
                            xmLstring = xmLstring.Replace("<", "<");
                            excelDoc.Write("<Cell ss:StyleID=\"StringLiteral\">" +
                                           "<Data ss:Type=\"String\">");
                            excelDoc.Write(xmLstring);
                            excelDoc.Write("</Data></Cell>");
                            break;
                        case "System.DateTime":
                            //Excel has a specific Date Format of YYYY-MM-DD followed by  
                            //the letter 'T' then hh:mm:sss.lll Example 2005-01-31T24:01:21.000
                            //The Following Code puts the date stored in XMLDate 
                            //to the format above
                            var xmlDate = (DateTime) x[y];
                            var xmlDatetoString = xmlDate.Year +
                                                  "-" +
                                                  (xmlDate.Month < 10
                                                      ? "0" +
                                                        xmlDate.Month
                                                      : xmlDate.Month.ToString()) +
                                                  "-" +
                                                  (xmlDate.Day < 10
                                                      ? "0" +
                                                        xmlDate.Day
                                                      : xmlDate.Day.ToString()) +
                                                  "T" +
                                                  (xmlDate.Hour < 10
                                                      ? "0" +
                                                        xmlDate.Hour
                                                      : xmlDate.Hour.ToString()) +
                                                  ":" +
                                                  (xmlDate.Minute < 10
                                                      ? "0" +
                                                        xmlDate.Minute
                                                      : xmlDate.Minute.ToString()) +
                                                  ":" +
                                                  (xmlDate.Second < 10
                                                      ? "0" +
                                                        xmlDate.Second
                                                      : xmlDate.Second.ToString()) +
                                                  ".000";
                            excelDoc.Write("<Cell ss:StyleID=\"DateLiteral\">" +
                                           "<Data ss:Type=\"DateTime\">");
                            excelDoc.Write(xmlDatetoString);
                            excelDoc.Write("</Data></Cell>");
                            break;
                        case "System.Boolean":
                            excelDoc.Write("<Cell ss:StyleID=\"StringLiteral\">" +
                                           "<Data ss:Type=\"String\">");
                            excelDoc.Write(x[y].ToString());
                            excelDoc.Write("</Data></Cell>");
                            break;
                        case "System.Int16":
                        case "System.Int32":
                        case "System.Int64":
                        case "System.Byte":
                            excelDoc.Write("<Cell ss:StyleID=\"Integer\">" +
                                           "<Data ss:Type=\"Number\">");
                            excelDoc.Write(x[y].ToString());
                            excelDoc.Write("</Data></Cell>");
                            break;
                        case "System.Decimal":
                        case "System.Double":
                            excelDoc.Write("<Cell ss:StyleID=\"Decimal\">" +
                                           "<Data ss:Type=\"Number\">");
                            excelDoc.Write(x[y].ToString());
                            excelDoc.Write("</Data></Cell>");
                            break;
                        case "System.DBNull":
                            excelDoc.Write("<Cell ss:StyleID=\"StringLiteral\">" +
                                           "<Data ss:Type=\"String\">");
                            excelDoc.Write("");
                            excelDoc.Write("</Data></Cell>");
                            break;
                        default:
                            throw new Exception(rowType + " not handled.");
                    }
                }

                excelDoc.Write("</Row>");
            }

            excelDoc.Write("</Table>");
            excelDoc.Write(" </Worksheet>");
            excelDoc.Write(endExcelXml);
            excelDoc.Close();
        }
    }
}