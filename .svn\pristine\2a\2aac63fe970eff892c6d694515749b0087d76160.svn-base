using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class STANDARDWIDTH : Record
	{
		public ushort DefaultColumnWidth;

		public STANDARDWIDTH(Record record)
			: base(record)
		{
		}

		public STANDARDWIDTH()
		{
			Type = 153;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			DefaultColumnWidth = binaryReader.ReadUInt16();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(DefaultColumnWidth);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
