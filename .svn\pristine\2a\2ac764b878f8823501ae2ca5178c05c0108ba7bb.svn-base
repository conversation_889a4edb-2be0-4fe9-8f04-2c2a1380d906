﻿using mshtml;
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;

namespace OCRTools
{
    public class CommonToImage
    {
        private static readonly int IMG_WIDTH = Screen.PrimaryScreen.WorkingArea.Width / 2 * 1;
        private static readonly int IMG_HEIGHT = Screen.PrimaryScreen.WorkingArea.Height / 3 * 1;

        private static RichTextBoxPrintCtrl RichTextBox;
        private static WebBrowser Browser;

        private static bool IsOnHtmlDraw;
        private static bool IsOnRtfDraw;

        public static Bitmap ByRTF(string strRtf, Size forceSize)
        {
            Bitmap img = null;
            if (string.IsNullOrEmpty(strRtf)) return img;
            try
            {
                if (IsOnRtfDraw) return img;
                IsOnRtfDraw = true;
                if (RichTextBox == null)
                {
                    RichTextBox = new RichTextBoxPrintCtrl
                    {
                        ScrollBars = RichTextBoxScrollBars.None,
                        BackColor = CommonSetting.贴图背景颜色,
                        ForeColor = CommonSetting.贴图文字颜色,
                        Font = CommonSetting.贴图文字字体,
                        WordWrap = false
                    };
                    RichTextBox.ContentsResized += RichTextBox_ContentsResized;
                }

                //RichTextBox.Refresh();

                if (!forceSize.IsEmpty)
                {
                    RichTextBox.Tag = "";
                    RichTextBox.Rtf = strRtf;
                    RichTextBox.Size = forceSize;
                }
                else
                {
                    RichTextBox.Tag = null;
                    RichTextBox.Rtf = strRtf;
                }

                img = new Bitmap(RichTextBox.Width, RichTextBox.Height, PixelFormat.Format32bppArgb);
                RichTextBox.DrawToBitmap(img, new Rectangle(new Point(0, 0), RichTextBox.Size));
            }
            catch
            {
            }
            finally
            {
                IsOnRtfDraw = false;
            }

            //img = richTextBox.DrawToBitmap1();
            return img;
        }

        private static void RichTextBox_ContentsResized(object sender, ContentsResizedEventArgs e)
        {
            var richTextBox = (RichTextBox)sender;
            if (richTextBox.Tag == null)
            {
                richTextBox.Width = e.NewRectangle.Width;
                richTextBox.Height = e.NewRectangle.Height;
            }

            richTextBox.Width += richTextBox.Margin.Horizontal +
                                 SystemInformation.HorizontalResizeBorderThickness +
                                 SystemInformation.HorizontalScrollBarThumbWidth;

            richTextBox.Height += richTextBox.Margin.Vertical +
                                  SystemInformation.VerticalResizeBorderThickness;
        }

        public static Bitmap ByHtml_WebBroswer(string html, Size forceSize)
        {
            Bitmap img = null;
            if (string.IsNullOrEmpty(html)) return img;
            try
            {
                if (IsOnHtmlDraw) return img;
                IsOnHtmlDraw = true;
                var finalSize = new Size(forceSize.IsEmpty ? IMG_WIDTH : forceSize.Width,
                    forceSize.IsEmpty ? IMG_HEIGHT : forceSize.Height)
                {
                    Width = (int)Math.Max(forceSize.Width, CommonSetting.贴图图片最大宽度)
                };
                if (Browser == null)
                    Browser = new WebBrowser
                    {
                        ScrollBarsEnabled = false,
                        ScriptErrorsSuppressed = true,
                        Dock = DockStyle.Fill,
                        Size = finalSize
                    };
                Browser.Navigate("about:blank");
                Browser.Document.OpenNew(false);
                Browser.Document.Write(html);
                //Browser.Document.Body.Style = "overflow:hidden";
                //Browser.Refresh();
                if (forceSize.IsEmpty)
                    try
                    {
                        ResizeBroswer(Browser);
                        finalSize = Browser.Size;
                    }
                    catch
                    {
                        for (var i = 0; i < 10; i++)
                        {
                            //Browser.Refresh();
                            finalSize = Browser.Document?.Body?.ScrollRectangle.Size ??
                                        Browser.Document?.Window?.Size ?? Browser.GetPreferredSize(Browser.Size);
                            if (Equals(Browser.Size, finalSize)) break;
                            Browser.Size = new Size(finalSize.Width, finalSize.Height);
                            Thread.Sleep(50);
                        }

                        AdjWidth(Browser);
                        finalSize = Browser.Document?.Body?.ScrollRectangle.Size ?? Browser.Document?.Window?.Size ??
                            Browser.GetPreferredSize(Browser.Size);
                    }
                else
                    Browser.Size = finalSize;

                img = new Bitmap(finalSize.Width, finalSize.Height, PixelFormat.Format32bppArgb);
                Browser.DrawToBitmap(img, new Rectangle(new Point(0, 0), finalSize));
            }
            catch
            {
            }
            finally
            {
                IsOnHtmlDraw = false;
            }

            return img;
        }

        protected static void ResizeBroswer(WebBrowser wb)
        {
            var docs2 = (IHTMLDocument2)wb.Document.DomDocument;
            var docs3 = (IHTMLDocument3)wb.Document.DomDocument;
            var body2 = (IHTMLElement2)docs2.body;
            var root2 = (IHTMLElement2)docs3.documentElement;

            var width = Math.Max(body2.scrollWidth, root2.scrollWidth);
            var height = Math.Max(root2.scrollHeight, body2.scrollHeight);

            // Resize the control to the exact size to display the page. Also, make sure scroll bars are disabled
            wb.Width = width;
            wb.Height = height;
        }

        private static Size GetSize(Size size, bool isConvert = false)
        {
            if (size.Width > CommonSetting.贴图图片最大宽度)
            {
                size.Width = (int)CommonSetting.贴图图片最大宽度;
                if (isConvert) size.Height = (int)(size.Width * size.Height / CommonSetting.贴图图片最大宽度);
            }

            return size;
        }

        private static void AdjWidth(WebBrowser browser)
        {
            var oldSize = browser.Size;
            while (browser.Width > 300)
            {
                browser.Width -= 50;
                //Browser.Refresh();
                var finalSize = Browser.Document?.Body?.ScrollRectangle.Size ??
                                Browser.Document?.Window?.Size ?? Browser.GetPreferredSize(Browser.Size);
                if (Equals(Browser.Size.Width, finalSize.Width) && Math.Abs(browser.Height - finalSize.Height) <= 5)
                {
                    oldSize = finalSize;
                    browser.Size = finalSize;
                }
                else
                {
                    browser.Size = oldSize;
                    //Browser.Refresh();
                    break;
                }

                Thread.Sleep(50);
            }
        }

        public static Bitmap AutoCrop(Bitmap bmp, int borderWidth = 5)
        {
            if (Image.GetPixelFormatSize(bmp.PixelFormat) != 32)
                throw new InvalidOperationException("Autocrop currently only supports 32 bits per pixel images.");

            var cropColor = bmp.GetPixel(2, 2);

            var bottom = 0;
            var
                left = bmp.Width; // Set the left crop point to the width so that the logic below will set the left value to the first non crop color pixel it comes across.
            var right = 0;
            var
                top = bmp.Height; // Set the top crop point to the height so that the logic below will set the top value to the first non crop color pixel it comes across.

            left = _sdfdsfsdfsd(bmp, cropColor, left, ref top, ref right, ref bottom);

            if (left < right && top < bottom)
            {
                var src_rect = new Rectangle(Math.Max(left - borderWidth, 0), Math.Max(top - borderWidth, 0),
                    right - left + borderWidth * 2, bottom - top + borderWidth * 2);
                var dest_rect = new Rectangle(borderWidth, borderWidth, src_rect.Width, src_rect.Height);

                var bm = new Bitmap(src_rect.Width + borderWidth * 2, src_rect.Height + borderWidth * 2);
                using (var gr = Graphics.FromImage(bm))
                {
                    gr.Clear(CommonSetting.贴图背景颜色);
                    gr.DrawImage(bmp, dest_rect, src_rect, GraphicsUnit.Pixel);
                }

                bmp = bm;
            }

            return bmp; // Entire image should be cropped, so just return null
        }

        [Obfuscation]
        private static unsafe int _sdfdsfsdfsd(Bitmap bmp, Color cropColor, int left, ref int top, ref int right,
            ref int bottom)
        {
            var bmpData = bmp.LockBits(new Rectangle(0, 0, bmp.Width, bmp.Height), ImageLockMode.ReadOnly,
                bmp.PixelFormat);

            unsafe
            {
                var dataPtr = (byte*) bmpData.Scan0;

                for (var y = 0; y < bmp.Height; y++)
                {
                    for (var x = 0; x < bmp.Width; x++)
                    {
                        var rgbPtr = dataPtr + x * 4;

                        var a = rgbPtr[3];

                        if (a == 0 || a != cropColor.A) continue;

                        var b = rgbPtr[0];
                        var g = rgbPtr[1];
                        var r = rgbPtr[2];

                        // If any of the pixel RGBA values don't match and the crop color is not transparent
                        //or if the crop color is transparent and the pixel A value is not transparent
                        if (b != cropColor.B || g != cropColor.G || r != cropColor.R)
                            // || (cropColor.A == 0 && a != 0))
                        {
                            left = left == -1 ? x : Math.Min(x, left);
                            top = top == -1 ? y : Math.Min(y, top);

                            right = right == -1 ? x + 1 : Math.Max(x + 1, right);
                            bottom = bottom == -1 ? y + 1 : Math.Max(y + 1, bottom);
                        }
                    }

                    dataPtr += bmpData.Stride;
                }

                left = Math.Max(0, left);
                top = Math.Max(0, top);
                right = Math.Min(bmp.Width, right);
                bottom = Math.Min(bmp.Height, bottom);
            }

            bmp.UnlockBits(bmpData);
            return left;
        }

        //public static Image ByHtml_HtmlRender(string _html, Color backColor, bool isGdi = false, TextRenderingHint gdiHint = TextRenderingHint.AntiAlias)
        //{
        //    Image img;
        //    EventHandler<HtmlRenderer.Core.Entities.HtmlStylesheetLoadEventArgs> stylesheetLoad = null;
        //    EventHandler<HtmlRenderer.Core.Entities.HtmlImageLoadEventArgs> imageLoad = null;
        //    var size = new Size(500, 500);
        //    if (isGdi)
        //    {
        //        img = HtmlRender.RenderToImageGdiPlus(_html, size, gdiHint, null, stylesheetLoad, imageLoad);
        //    }
        //    else
        //    {
        //        img = HtmlRender.RenderToImage(_html, 500, 500, backColor, null, stylesheetLoad, imageLoad);
        //    }
        //    return img;
        //}
    }

    public class RichTextBoxPrintCtrl : RichTextBox
    {
        //Convert the unit used by the .NET framework (1/100 inch)
        //and the unit used by Win32 API calls (twips 1/1440 inch)
        private const double anInch = 14.4;

        private const int WM_USER = 0x0400;

        private const int EM_FORMATRANGE = WM_USER + 57;

        protected override CreateParams CreateParams
        {
            get
            {
                var cparams = base.CreateParams;
                if (LoadLibrary("msftedit.dll") != IntPtr.Zero) cparams.ClassName = "RICHEDIT50W";
                return cparams;
            }
        }

        [DllImport("USER32.dll")]
        private static extern IntPtr SendMessage(IntPtr hWnd, int msg, IntPtr wp, IntPtr lp);

        // Render the contents of the RichTextBox for printing

        // Return the last character printed + 1 (printing start from this point for next page)

        [DllImport("kernel32.dll", CharSet = CharSet.Auto)]
        private static extern IntPtr LoadLibrary(string lpFileName);

        public Image DrawToBitmap1()
        {
            // Set area to render to be entire bitmap
            RECT rect;
            //rect.Left = 0;
            //rect.Top = 0;
            //rect.Right = (int)(bitmap.Width * anInch);
            //rect.Bottom = (int)(bitmap.Height * anInch);

            var bitmap = new Bitmap(Size.Width, Size.Height);
            using (var g = Graphics.FromImage(bitmap))
            {
                rect.Top = 0; //(int)(bounds.Top * anInch);
                rect.Bottom = (int)(g.ClipBounds.Height * anInch); //(int)(bounds.Bottom * anInch);
                rect.Left = 0; //(int)(bounds.Left * anInch);
                rect.Right = (int)(g.ClipBounds.Right * anInch); //(int)(bounds.Right * anInch);

                var hdc = g.GetHdc();

                FORMATRANGE fmtRange;
                fmtRange.chrg.cpMin = GetCharIndexFromPosition(new Point(0, 0));
                fmtRange.chrg.cpMax = GetCharIndexFromPosition(new Point(bitmap.Width, bitmap.Height));
                fmtRange.hdc = hdc; // Use the same DC for measuring and rendering
                fmtRange.hdcTarget = hdc;
                fmtRange.rc = rect;
                fmtRange.rcPage = rect;

                var lparam = Marshal.AllocCoTaskMem(Marshal.SizeOf(fmtRange));
                Marshal.StructureToPtr(fmtRange, lparam, false);

                // Render the control to the bitmap
                SendMessage(Handle, EM_FORMATRANGE, new IntPtr(1), lparam);

                // Clean up
                Marshal.FreeCoTaskMem(lparam);
                g.ReleaseHdc(hdc);
            }

            //Return last + 1 character printer
            return bitmap;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct RECT
        {
            public int Left;

            public int Top;

            public int Right;

            public int Bottom;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct CHARRANGE
        {
            public int cpMin; //First character of range (0 for start of doc)

            public int cpMax; //Last character of range (-1 for end of doc)
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct FORMATRANGE
        {
            public IntPtr hdc; //Actual DC to draw on

            public IntPtr hdcTarget; //Target DC for determining text formatting

            public RECT rc; //Region of the DC to draw to (in twips)

            public RECT rcPage; //Region of the whole DC (page size) (in twips)

            public CHARRANGE chrg; //Range of text to draw (see earlier declaration)
        }
    }
}