﻿using OCRTools;
using OCRTools.Common;
using System;
using System.Collections.Specialized;
using System.IO;

namespace ImageLib
{
    /// <summary>
    /// </summary>
    internal class WebResizerUpload
    {
        internal static bool Enable { get; set; } = true;

        private const string strFileNameSpilt = ").attr(\"src\",'";

        internal static string GetResult(byte[] content)
        {
            var result = "";
            var url = "http://webresizer.com/resizer/action-z-shivani.ajax.php";
            var file = new UploadFileInfo()
            {
                Name = "files[]",
                Filename = "1.png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            var vaules = new NameValueCollection() {
                    { "filenames", "1.png" }
                };
            var html = string.Empty;
            try
            {
                html = UploadFileRequest.Post(url, new[] { file }, vaules);
            }
            catch { }
            if (html.Contains(strFileNameSpilt))
            {
                result = html.Substring(html.IndexOf(strFileNameSpilt) + strFileNameSpilt.Length);
                result = result.Substring(0, result.IndexOf("'")).Replace("\\/", "/");
                result = "http://webresizer.com/resizer/" + result;
            }
            return result;
        }

        internal static byte[] GetZipResult(byte[] content, ref string strUrl)
        {
            strUrl = GetResult(content);
            return CommonMethod.GetUrlBytes(strUrl);
        }
    }
}
