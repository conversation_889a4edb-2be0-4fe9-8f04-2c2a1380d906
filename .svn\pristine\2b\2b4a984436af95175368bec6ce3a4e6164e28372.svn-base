﻿using MetroFramework.Components;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
/**
 * MetroFramework - Modern UI for WinForms
 * 
 * The MIT License (MIT)
 * Copyright (c) 2011 <PERSON>, http://github.com/viperneo
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of 
 * this software and associated documentation files (the "Software"), to deal in the 
 * Software without restriction, including without limitation the rights to use, copy, 
 * modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, 
 * and to permit persons to whom the Software is furnished to do so, subject to the 
 * following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in 
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
 * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT 
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF 
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE 
 * OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace MetroFramework.Controls
{
    [ToolboxBitmap(typeof(LinkLabel))]
    [DefaultEvent("Click")]
    [Designer(typeof(Design.MetroLinkDesigner), typeof(System.Windows.Forms.Design.ParentControlDesigner))]
    public class MetroLink : Button, IMetroControl
    {
        private bool displayFocusRectangle;

        private MetroColorStyle metroStyle;

        private MetroThemeStyle metroTheme;

        private MetroStyleManager metroStyleManager;

        private bool useCustomBackColor;

        private bool useCustomForeColor;

        private bool useStyleColors;

        private Image _image;

        private Image _nofocus;

        private int _imagesize = 16;

        private MetroLinkSize metroLinkSize;

        private MetroLinkWeight metroLinkWeight = MetroLinkWeight.Bold;

        private bool isHovered;

        private bool isPressed;

        private bool isFocused;

        private Color foreColor;

        private Image _lightlightimg;

        private Image _darklightimg;

        private Image _lightimg;

        private Image _darkimg;

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool DisplayFocus
        {
            get
            {
                return displayFocusRectangle;
            }
            set
            {
                displayFocusRectangle = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroColorStyle.Blue)]
        public MetroColorStyle Style
        {
            get
            {
                if (base.DesignMode || metroStyle != 0)
                {
                    return metroStyle;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Style;
                }
                if (StyleManager == null)
                {
                    return MetroColorStyle.Blue;
                }
                return metroStyle;
            }
            set
            {
                metroStyle = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroThemeStyle.Light)]
        public MetroThemeStyle Theme
        {
            get
            {
                if (base.DesignMode || metroTheme != 0)
                {
                    return metroTheme;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Theme;
                }
                if (StyleManager == null)
                {
                    return MetroThemeStyle.Light;
                }
                return metroTheme;
            }
            set
            {
                metroTheme = value;
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        public MetroStyleManager StyleManager
        {
            get
            {
                return metroStyleManager;
            }
            set
            {
                metroStyleManager = value;
            }
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseCustomBackColor
        {
            get
            {
                return useCustomBackColor;
            }
            set
            {
                useCustomBackColor = value;
            }
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseCustomForeColor
        {
            get
            {
                return useCustomForeColor;
            }
            set
            {
                useCustomForeColor = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseStyleColors
        {
            get
            {
                return useStyleColors;
            }
            set
            {
                useStyleColors = value;
            }
        }

        [Category("Metro Behaviour")]
        [DefaultValue(false)]
        [Browsable(false)]
        public bool UseSelectable
        {
            get
            {
                return GetStyle(ControlStyles.Selectable);
            }
            set
            {
                SetStyle(ControlStyles.Selectable, value);
            }
        }

        [DefaultValue(null)]
        [Category("Metro Appearance")]
        public new virtual Image Image
        {
            get
            {
                return _image;
            }
            set
            {
                _image = value;
                createimages();
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(null)]
        public Image NoFocusImage
        {
            get
            {
                return _nofocus;
            }
            set
            {
                _nofocus = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(16)]
        public int ImageSize
        {
            get
            {
                return _imagesize;
            }
            set
            {
                _imagesize = value;
                Invalidate();
            }
        }

        public override string Text
        {
            get
            {
                return base.Text;
            }
            set
            {
                base.Text = value;
                if (AutoSize && _image != null)
                {
                    base.Width = TextRenderer.MeasureText(value, MetroFonts.Link(metroLinkSize, metroLinkWeight)).Width;
                    base.Width += _imagesize + 2;
                }
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroLinkSize.Small)]
        public MetroLinkSize FontSize
        {
            get
            {
                return metroLinkSize;
            }
            set
            {
                metroLinkSize = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroLinkWeight.Bold)]
        public MetroLinkWeight FontWeight
        {
            get
            {
                return metroLinkWeight;
            }
            set
            {
                metroLinkWeight = value;
            }
        }

        [Browsable(false)]
        public override Font Font
        {
            get
            {
                return base.Font;
            }
            set
            {
                base.Font = value;
            }
        }

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintBackground != null)
            {
                this.CustomPaintBackground(this, e);
            }
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaint != null)
            {
                this.CustomPaint(this, e);
            }
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintForeground != null)
            {
                this.CustomPaintForeground(this, e);
            }
        }

        public MetroLink()
        {
            SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.SupportsTransparentBackColor | ControlStyles.OptimizedDoubleBuffer, value: true);
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            try
            {
                Color color = BackColor;
                if (!useCustomBackColor)
                {
                    color = MetroPaint.BackColor.Form(Theme);
                }
                if (color.A == byte.MaxValue && BackgroundImage == null)
                {
                    e.Graphics.Clear(color);
                }
                else
                {
                    base.OnPaintBackground(e);
                    OnCustomPaintBackground(new MetroPaintEventArgs(color, Color.Empty, e.Graphics));
                }
            }
            catch
            {
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            try
            {
                if (GetStyle(ControlStyles.AllPaintingInWmPaint))
                {
                    OnPaintBackground(e);
                }
                OnCustomPaint(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
                OnPaintForeground(e);
            }
            catch
            {
                Invalidate();
            }
        }

        protected virtual void OnPaintForeground(PaintEventArgs e)
        {
            if (useCustomForeColor)
            {
                foreColor = ForeColor;
            }
            else if (isHovered && !isPressed && base.Enabled)
            {
                foreColor = MetroPaint.ForeColor.Link.Normal(Theme);
            }
            else if (isHovered && isPressed && base.Enabled)
            {
                foreColor = MetroPaint.ForeColor.Link.Press(Theme);
            }
            else if (!base.Enabled)
            {
                foreColor = MetroPaint.ForeColor.Link.Disabled(Theme);
            }
            else
            {
                foreColor = ((!useStyleColors) ? MetroPaint.ForeColor.Link.Hover(Theme) : MetroPaint.GetStyleColor(Style));
            }
            TextRenderer.DrawText(e.Graphics, Text, MetroFonts.Link(metroLinkSize, metroLinkWeight), base.ClientRectangle, foreColor, MetroPaint.GetTextFormatFlags(TextAlign));
            OnCustomPaintForeground(new MetroPaintEventArgs(Color.Empty, foreColor, e.Graphics));
            if (displayFocusRectangle && isFocused)
            {
                ControlPaint.DrawFocusRectangle(e.Graphics, base.ClientRectangle);
            }
            if (_image != null)
            {
                DrawIcon(e.Graphics);
            }
        }

        private void DrawIcon(Graphics g)
        {
            if (Image == null)
            {
                return;
            }
            int num = _imagesize;
            int num2 = _imagesize;
            if (_imagesize == 0)
            {
                num = _image.Width;
                num2 = _image.Height;
            }
            Point location = new Point(2, (base.ClientRectangle.Height - _imagesize) / 2);
            int num3 = 0;
            switch (base.ImageAlign)
            {
                case ContentAlignment.BottomCenter:
                    location = new Point((base.ClientRectangle.Width - num) / 2, base.ClientRectangle.Height - num2 - num3);
                    break;
                case ContentAlignment.BottomLeft:
                    location = new Point(num3, base.ClientRectangle.Height - num2 - num3);
                    break;
                case ContentAlignment.BottomRight:
                    location = new Point(base.ClientRectangle.Width - num - num3, base.ClientRectangle.Height - num2 - num3);
                    break;
                case ContentAlignment.MiddleCenter:
                    location = new Point((base.ClientRectangle.Width - num) / 2, (base.ClientRectangle.Height - num2) / 2);
                    break;
                case ContentAlignment.MiddleLeft:
                    location = new Point(num3, (base.ClientRectangle.Height - num2) / 2);
                    break;
                case ContentAlignment.MiddleRight:
                    location = new Point(base.ClientRectangle.Width - num - num3, (base.ClientRectangle.Height - num2) / 2);
                    break;
                case ContentAlignment.TopCenter:
                    location = new Point((base.ClientRectangle.Width - num) / 2, num3);
                    break;
                case ContentAlignment.TopLeft:
                    location = new Point(num3, num3);
                    break;
                case ContentAlignment.TopRight:
                    location = new Point(base.ClientRectangle.Width - num - num3, num3);
                    break;
            }
            location.Y++;
            if (_nofocus == null)
            {
                if (Theme == MetroThemeStyle.Dark)
                {
                    g.DrawImage((isHovered && !isPressed) ? _darkimg : _darklightimg, new Rectangle(location, new Size(num, num2)));
                }
                else
                {
                    g.DrawImage((isHovered && !isPressed) ? _lightimg : _lightlightimg, new Rectangle(location, new Size(num, num2)));
                }
            }
            else if (Theme == MetroThemeStyle.Dark)
            {
                g.DrawImage((isHovered && !isPressed) ? _darkimg : _nofocus, new Rectangle(location, new Size(num, num2)));
            }
            else
            {
                g.DrawImage((isHovered && !isPressed) ? _image : _nofocus, new Rectangle(location, new Size(num, num2)));
            }
        }

        private void createimages()
        {
            if (_image != null)
            {
                _lightimg = _image;
                _darkimg = ApplyInvert(new Bitmap(_image));
                _darklightimg = ApplyLight(new Bitmap(_darkimg));
                _lightlightimg = ApplyLight(new Bitmap(_lightimg));
            }
        }

        public Bitmap ApplyInvert(Bitmap bitmapImage)
        {
            for (int i = 0; i < bitmapImage.Height; i++)
            {
                for (int j = 0; j < bitmapImage.Width; j++)
                {
                    Color pixel = bitmapImage.GetPixel(j, i);
                    byte a = pixel.A;
                    byte red = (byte)(255 - pixel.R);
                    byte green = (byte)(255 - pixel.G);
                    byte blue = (byte)(255 - pixel.B);
                    bitmapImage.SetPixel(j, i, Color.FromArgb(a, red, green, blue));
                }
            }
            return bitmapImage;
        }

        public Bitmap ApplyLight(Bitmap bitmapImage)
        {
            for (int i = 0; i < bitmapImage.Height; i++)
            {
                for (int j = 0; j < bitmapImage.Width; j++)
                {
                    Color pixel = bitmapImage.GetPixel(j, i);
                    byte alpha = pixel.A;
                    if (pixel.A <= byte.MaxValue && pixel.A >= 100)
                    {
                        alpha = 90;
                    }
                    byte r = pixel.R;
                    byte g = pixel.G;
                    byte b = pixel.B;
                    bitmapImage.SetPixel(j, i, Color.FromArgb(alpha, r, g, b));
                }
            }
            return bitmapImage;
        }

        protected override void OnGotFocus(EventArgs e)
        {
            isFocused = true;
            isPressed = false;
            Invalidate();
            base.OnGotFocus(e);
        }

        protected override void OnLostFocus(EventArgs e)
        {
            isFocused = false;
            isHovered = false;
            isPressed = false;
            Invalidate();
            base.OnLostFocus(e);
        }

        protected override void OnEnter(EventArgs e)
        {
            isFocused = true;
            isPressed = true;
            Invalidate();
            base.OnEnter(e);
        }

        protected override void OnLeave(EventArgs e)
        {
            isFocused = false;
            isHovered = false;
            isPressed = false;
            Invalidate();
            base.OnLeave(e);
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Space)
            {
                isHovered = true;
                isPressed = true;
                Invalidate();
            }
            base.OnKeyDown(e);
        }

        protected override void OnKeyUp(KeyEventArgs e)
        {
            if (!isFocused)
            {
                isHovered = false;
                isPressed = false;
            }
            Invalidate();
            base.OnKeyUp(e);
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            isHovered = true;
            Invalidate();
            base.OnMouseEnter(e);
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                isPressed = true;
                Invalidate();
                if (base.Name == "lnkClear" && base.Parent.GetType().Name == "MetroTextBox")
                {
                    PerformClick();
                }
                if (base.Name == "lnkClear" && base.Parent.GetType().Name == "SearchControl")
                {
                    PerformClick();
                }
            }
            base.OnMouseDown(e);
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            isPressed = false;
            Invalidate();
            base.OnMouseUp(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            isHovered = false;
            isPressed = false;
            Invalidate();
            base.OnMouseLeave(e);
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);
            Invalidate();
        }
    }
}
