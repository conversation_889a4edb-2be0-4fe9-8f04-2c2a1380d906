namespace TianruoOCR
{
	public class SaveStatus
	{
		public class OCR
		{
			public string OCRFontSize
			{
				get;
				set;
			}

			public string OCRFontStyle
			{
				get;
				set;
			}

			public string OCRSpace
			{
				get;
				set;
			}

			public string OCRCenter
			{
				get;
				set;
			}

			public string OCRLanguagle
			{
				get;
				set;
			}

			public string OCRPuncuntion
			{
				get;
				set;
			}

			public string OCRInterface
			{
				get;
				set;
			}

			public string OCRStatus
			{
				get;
				set;
			}
		}

		public class Trans
		{
			public string TransFontSize
			{
				get;
				set;
			}

			public string TransFontStyle
			{
				get;
				set;
			}

			public string TransSpace
			{
				get;
				set;
			}

			public string TransCenter
			{
				get;
				set;
			}

			public string FromLanguagle
			{
				get;
				set;
			}

			public string ToLanguagle
			{
				get;
				set;
			}

			public string TransPuncuntion
			{
				get;
				set;
			}
		}

		public class Form
		{
			public OCR OCR
			{
				get;
				set;
			}

			public Trans Trans
			{
				get;
				set;
			}
		}

		public class Set
		{
			public bool DrawRectangle
			{
				get;
				set;
			} = true;


			public bool DrawEllipse
			{
				get;
				set;
			} = true;


			public bool DrawUpload
			{
				get;
				set;
			} = true;


			public bool DrawText
			{
				get;
				set;
			} = true;


			public bool DrawStep
			{
				get;
				set;
			} = true;


			public bool DrawPolygon
			{
				get;
				set;
			} = true;


			public bool DrawGaus
			{
				get;
				set;
			} = true;


			public bool DrawRecoder
			{
				get;
				set;
			} = true;


			public bool DrawMosaic
			{
				get;
				set;
			} = true;


			public bool DrawHighlight
			{
				get;
				set;
			} = true;


			public bool DrawArrow
			{
				get;
				set;
			} = true;


			public bool DrawRectangleFill
			{
				get;
				set;
			} = true;


			public bool DrawLine
			{
				get;
				set;
			} = true;


			public bool CatchShowOCR
			{
				get;
				set;
			} = true;


			public bool OCRIsZoom
			{
				get;
				set;
			} = true;


			public bool BeforeStart
			{
				get;
				set;
			}

			public bool BeforeShow
			{
				get;
				set;
			}

			public bool AfterTopmost
			{
				get;
				set;
			}

			public bool AfterShow
			{
				get;
				set;
			}

			public bool AfterCopy
			{
				get;
				set;
			}

			public bool AfterTrans
			{
				get;
				set;
			}

			public bool CopyWithFormat
			{
				get;
				set;
			}

			public bool CatchIsShowCross
			{
				get;
				set;
			}

			public bool CopyWithFile
			{
				get;
				set;
			}

			public bool OCRIsCross
			{
				get;
				set;
			}

			public string Loadinganimation
			{
				get;
				set;
			}

			public string MarkLocation
			{
				get;
				set;
			}

			public string Compose
			{
				get;
				set;
			}

			public string GetColor
			{
				get;
				set;
			}

			public bool ISSound
			{
				get;
				set;
			}

			public string SoundPath
			{
				get;
				set;
			}

			public string ProxyUrl
			{
				get;
				set;
			}

			public string ProxyPort
			{
				get;
				set;
			}

			public string ProxyName
			{
				get;
				set;
			}

			public string ProxyPassWord
			{
				get;
				set;
			}

			public bool ProxySystem
			{
				get;
				set;
			}

			public bool ProxyNo
			{
				get;
				set;
			}

			public bool ProxyCustom
			{
				get;
				set;
			}

			public string SearchApi
			{
				get;
				set;
			}

			public string CountModel
			{
				get;
				set;
			}

			public bool actorBaiduBoy
			{
				get;
				set;
			}

			public string CatchRect
			{
				get;
				set;
			}

			public bool actorBaiduGirl
			{
				get;
				set;
			}

			public bool actorBoy
			{
				get;
				set;
			}

			public bool actorGirl
			{
				get;
				set;
			}

			public int VoiceSpeed
			{
				get;
				set;
			}

			public int VoiceTone
			{
				get;
				set;
			}

			public bool IsCheckupdate
			{
				get;
				set;
			}

			public string CheckTime
			{
				get;
				set;
			}

			public string IsClosing
			{
				get;
				set;
			}

			public string DoubleTray
			{
				get;
				set;
			}

			public bool IsCover
			{
				get;
				set;
			}

			public string Recodermode
			{
				get;
				set;
			}

			public string ThemeColor
			{
				get;
				set;
			}

			public string AnimationPath
			{
				get;
				set;
			}

			public int Animationfps
			{
				get;
				set;
			}

			public bool Updateini
			{
				get;
				set;
			}
		}

		public class HotkeyData
		{
			public string Text
			{
				get;
				set;
			}

			public string Hotkeys
			{
				get;
				set;
			}
		}

		public class Interface
		{
			public string TxtOCR
			{
				get;
				set;
			}

			public string TableOCR
			{
				get;
				set;
			}

			public string MathOCR
			{
				get;
				set;
			}

			public string Trans
			{
				get;
				set;
			}
		}

		public class TxtOCR
		{
			public string baiduAppid
			{
				get;
				set;
			}

			public string baiduKey
			{
				get;
				set;
			}

			public string baiduAppidSplit
			{
				get;
				set;
			}

			public string baiduKeySplit
			{
				get;
				set;
			}

			public string baiduaccurateAppid
			{
				get;
				set;
			}

			public string baiduaccurateKey
			{
				get;
				set;
			}

			public string sougouAppid
			{
				get;
				set;
			}

			public string sougouKey
			{
				get;
				set;
			}

			public string Sougouapikey
			{
				get;
				set;
			}

			public string Sougousecretkey
			{
				get;
				set;
			}

			public string TencentAppid
			{
				get;
				set;
			}

			public string TencentKey
			{
				get;
				set;
			}
		}

		public class TableOCR
		{
			public string baiduAppid
			{
				get;
				set;
			}

			public string baiduKey
			{
				get;
				set;
			}

			public string aliKey
			{
				get;
				set;
			}

			public string tencent_appid
			{
				get;
				set;
			}

			public string tencent_secretId
			{
				get;
				set;
			}

			public string tencent_secretKey
			{
				get;
				set;
			}

			public string tencent_userid
			{
				get;
				set;
			}
		}

		public class MathOCR
		{
			public string mathpixAppid
			{
				get;
				set;
			}

			public string mathpixKey
			{
				get;
				set;
			}
		}

		public class Translate
		{
			public string baiduAppid
			{
				get;
				set;
			}

			public string baiduKey
			{
				get;
				set;
			}

			public string sougouAppid
			{
				get;
				set;
			}

			public string sougouKey
			{
				get;
				set;
			}

			public string tencentAppid
			{
				get;
				set;
			}

			public string tencentKey
			{
				get;
				set;
			}

			public string caiyuntoken
			{
				get;
				set;
			}
		}

		public class CustomKey
		{
			public TxtOCR TxtOCR
			{
				get;
				set;
			}

			public TableOCR TableOCR
			{
				get;
				set;
			}

			public MathOCR MathOCR
			{
				get;
				set;
			}

			public Translate Translate
			{
				get;
				set;
			}
		}

		public class Hotkey
		{
			public HotkeyData HotKeyOCRex
			{
				get;
				set;
			}

			public HotkeyData HotKeyOCR
			{
				get;
				set;
			}

			public HotkeyData HotKeyShow
			{
				get;
				set;
			}

			public HotkeyData HotKeyMuliti
			{
				get;
				set;
			}

			public HotkeyData HotKeyRecoder
			{
				get;
				set;
			}

			public HotkeyData HotKeyPaste
			{
				get;
				set;
			}

			public HotkeyData HotKeyCatch
			{
				get;
				set;
			}

			public HotkeyData HotKeyOCRRect
			{
				get;
				set;
			}

			public HotkeyData HotKeyQuicktrans
			{
				get;
				set;
			}
		}

		public class Information
		{
			public string Account
			{
				get;
				set;
			}

			public string Password
			{
				get;
				set;
			}
		}

		public class Root
		{
			public Information Information
			{
				get;
				set;
			}

			public Form Form
			{
				get;
				set;
			}

			public Set Set
			{
				get;
				set;
			}

			public Hotkey Hotkey
			{
				get;
				set;
			}

			public Interface Interface
			{
				get;
				set;
			}

			public CustomKey CustomKey
			{
				get;
				set;
			}

			public Custrans Custrans
			{
				get;
				set;
			}
		}

		public class Custrans
		{
			public string Custrans_法语
			{
				get;
				set;
			}

			public string Custrans_德语
			{
				get;
				set;
			}

			public string Custrans_俄语
			{
				get;
				set;
			}

			public string Custrans_韩语
			{
				get;
				set;
			}

			public string Custrans_英语
			{
				get;
				set;
			}

			public string Custrans_日语
			{
				get;
				set;
			}

			public string Custrans_中文
			{
				get;
				set;
			}

			public string Custrans_json
			{
				get;
				set;
			}

			public string Custrans_url
			{
				get;
				set;
			}
		}
	}
}
