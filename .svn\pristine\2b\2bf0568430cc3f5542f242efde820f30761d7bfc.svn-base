using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools
{
    public class PanelPictureView : ImageBox
    {
        private readonly Color IMG_MODE_FOCUS_COLOR = Color.FromArgb(50, Color.Red);
        private readonly Color IMG_MODE_CLICK_COLOR = Color.FromArgb(90, Color.Red);

        // 添加事件 - 用于通知文本块选择状态变化
        public event EventHandler<TextCellEventArgs> TextCellStateChanged;
        
        // 事件参数类
        public class TextCellEventArgs : EventArgs
        {
            public TextCellInfo Cell { get; private set; }
            public TextCellSelectionType SelectionType { get; private set; }
            
            public TextCellEventArgs(TextCellInfo cell, TextCellSelectionType type)
            {
                Cell = cell;
                SelectionType = type;
            }
        }
        
        // 选择类型枚举
        public enum TextCellSelectionType
        {
            None,       // 无选择
            Hover,      // 悬停
            Click       // 点击
        }

        public PanelPictureView()
        {
            Margin = Padding.Empty;
            AutoScroll = true;
            TabStop = false;
            AutoCenter = false;

            MouseMove += PanelPictureView_MouseMove;
            MouseClick += PanelPictureView_MouseClick;
            MouseWheel += PanelPictureView_MouseWheel;
        }

        /// <summary>
        /// 是否已经绑定过图片文字
        /// </summary>
        public bool IsBindImageMode { get; set; }

        List<TextCellInfo> lstCells = new List<TextCellInfo>();

        private TextCellInfo CurrentCell;

        private Rectangle drawRegion;

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            if (IsBindImageMode && !drawRegion.IsEmpty)
            {
                using (var contentBrush = new SolidBrush(IMG_MODE_CLICK_COLOR))
                {
                    e.Graphics.FillRectangle(contentBrush, drawRegion);
                }
            }
        }

        private void PanelPictureView_MouseWheel(object sender, MouseEventArgs e)
        {
            PanelPictureView_MouseMove(sender, e);
        }

        private void PanelPictureView_MouseMove(object sender, MouseEventArgs e)
        {
            DrawCell(false, e.Button != MouseButtons.None || e.Delta != 0);
        }

        private void PanelPictureView_MouseClick(object sender, MouseEventArgs e)
        {
            CurrentCell = null;
            DrawCell(true);
        }

        private TextCellInfo GetCurrentCell()
        {
            var mouseLoc = PointToClient(Cursor.Position);
            mouseLoc = new Point(mouseLoc.X - AutoScrollPosition.X, mouseLoc.Y - AutoScrollPosition.Y);
            var cell = lstCells.Where(item => item.location != null && item.location.Rectangle.Zoom(ZoomFactor).Contains(mouseLoc)).FirstOrDefault();
            return cell;
        }

        private void DrawCell(bool isClick = false, bool isOnMove = false)
        {
            if (!IsBindImageMode)
            {
                return;
            }
            var cell = isOnMove ? null : GetCurrentCell();
            if (cell != null && Equals(CurrentCell, cell))
            {
                return;
            }
            if (!drawRegion.IsEmpty)
            {
                Invalidate(drawRegion);
                drawRegion = Rectangle.Empty;
            }
            
            if (cell != null)
            {
                var cellRect = cell.location.Rectangle.SizeOffset(2).Zoom(ZoomFactor);
                cellRect.Location = cellRect.Location.Add(AutoScrollPosition);
                drawRegion = new Rectangle(cellRect.Location, cellRect.Size);
                var tipLoc = new Point(Math.Max(0, drawRegion.Location.X), drawRegion.Location.Y + drawRegion.Height + 1);
                if (!ClientRectangle.Contains(tipLoc))
                {
                    tipLoc = new Point(Math.Max(ClientRectangle.X, tipLoc.X), Math.Min(ClientRectangle.Y, tipLoc.Y));
                }
                
                // 触发事件
                TextCellStateChanged?.Invoke(this, new TextCellEventArgs(
                    cell, 
                    isClick ? TextCellSelectionType.Click : TextCellSelectionType.Hover
                ));
                
                if (isClick)
                {
                    Invalidate(drawRegion);
                    CommonMethod.ShowTxtToolTipContextMenu(this, cell.TipText, tipLoc);
                    if (CommonSetting.点击图片复制结果)
                    {
                        try
                        {
                            ClipboardService.SetText(cell.TipText);
                        }
                        catch { }
                    }
                }
                else
                {
                    using (var g = CreateGraphics())
                    {
                        using (var contentBrush = new SolidBrush(IMG_MODE_FOCUS_COLOR))
                        {
                            g.FillRectangle(contentBrush, drawRegion);
                        }
                    }
                    CommonMethod.ShowTxtToolTip(this, cell.TipText, tipLoc);
                }
            }
            else
            {
                // 触发无选择事件
                TextCellStateChanged?.Invoke(this, new TextCellEventArgs(null, TextCellSelectionType.None));
                
                CommonMethod.HideTxtToolTip(this);
                CommonMethod.HideTxtToolTipContextMenu();
            }
            CurrentCell = cell;
        }

        object objLock = "";

        protected override void OnMouseLeave(EventArgs e)
        {
            base.OnMouseLeave(e);
            
            // 鼠标离开时清除高亮并触发事件
            if (CurrentCell != null)
            {
                TextCellStateChanged?.Invoke(this, new TextCellEventArgs(null, TextCellSelectionType.None));
                CurrentCell = null;
                
                if (!drawRegion.IsEmpty)
                {
                    Invalidate(drawRegion);
                    drawRegion = Rectangle.Empty;
                }
                
                CommonMethod.HideTxtToolTip(this);
            }
        }

        public void BindPicTxt(UcContent content, bool isShowTxt = false)
        {
            lstCells = content.OcrContent?.result?.verticalText?.DeserializeJson<List<TextCellInfo>>() ?? new List<TextCellInfo>();
            if (content.Image != null && lstCells.Count > 0)
            {
                var image = new Bitmap(content.Image);
                using (var g = Graphics.FromImage(image))
                {
                    g.InterpolationMode = InterpolationMode.HighQualityBilinear;
                    g.CompositingQuality = CompositingQuality.HighQuality;
                    g.SmoothingMode = SmoothingMode.HighQuality;
                    g.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;

                    using (var brush = new SolidBrush(CommonSetting.Get默认文字颜色()))
                    {
                        using (var backgroundBrush = new SolidBrush(CommonSetting.Get默认背景颜色()))
                        {
                            Parallel.ForEach(lstCells, item =>
                            {
                                if ((string.IsNullOrEmpty(item.words) && string.IsNullOrEmpty(item.trans)) || item.location == null)
                                    return;
                                var rectangle = item.location.Rectangle;
                                lock (objLock)
                                {
                                    g.DrawRectangle(Pens.Red, rectangle.LocationOffset(-1, -1).SizeOffset(2));
                                }
                                item.TipText = content.GetTextByContent(item);
                                if (isShowTxt && !string.IsNullOrEmpty(item.TipText))
                                {
                                    var normalFont = CommonMethod.ScaleLabelByHeight(item.TipText, CommonString.GetUserNormalFont(5F), rectangle.Size);
                                    lock (objLock)
                                    {
                                        g.FillRectangle(backgroundBrush, rectangle);
                                        g.DrawString(item.TipText, normalFont, brush, rectangle);
                                    }
                                }
                            });
                        }
                    }
                }
                Image = image;
            }
            else if (Equals(Image, content.Image))
            {
                Image = content.Image;
            }

            IsBindImageMode = true;
            this.BringToFront();
        }
    }
}