﻿using System.ComponentModel;
using System.Drawing;

namespace OCRTools
{
    public enum LoadingType
    {
        蓝色箭头 = 0,
        蓝色圆圈 = 1,
        红色圆圈 = 2,
        旋风 = 3,
        Win10 = 4,
        Win11 = 5,
        时钟 = 6,
        箭头 = 7,
        自定义 = 8
    }

    public class LoadingTypeConfig
    {
        public int Interval { get; set; }

        public int ImgCount { get; set; }

        public string ImgName { get; set; }

        public bool IsRound { get; set; }

        public bool IsIngoreTheme { get; set; }
    }

    public class LoadingTypeHelper
    {
        public static Image GetImageByConfig(LoadingType type, int index = 0)
        {
            var config = GetTypeConfig(type);
            return GetImageByConfig(config, index, true);
        }

        private static ComponentResourceManager LoadingResource;

        public static Bitmap GetImageByConfig(LoadingTypeConfig config, int index, bool ingoreTheme)
        {
            if (LoadingResource == null)
            {
                LoadingResource = new ComponentResourceManager(typeof(UcLoading));
            }
            var objName = index + config.ImgName;
            var result = Equals(config.ImgName, LoadingType.自定义.ToString()) ?
                CommonSetting.Get图标效果(CommonSetting.GetStrSize(CommonSetting.工具栏图标尺寸), string.IsNullOrEmpty(CommonSetting.加载动画图片) ? CommonSetting.工具栏图片 : CommonSetting.加载动画图片,
                    CommonSetting.圆形图标, CommonSetting.阴影效果, CommonSetting.工具栏阴影宽度, false)
                : (Bitmap)LoadingResource.GetObject(objName);
            if (!ingoreTheme && CommonSetting.夜间模式)
            {
                result = Common.ImageProcessHelper.InverseImage(result);
            }
            return result;
        }

        public static LoadingTypeConfig GetTypeConfig(LoadingType type)
        {
            var config = new LoadingTypeConfig() { IsRound = true, Interval = 50, ImgCount = 15 };
            switch (type)
            {
                case LoadingType.蓝色箭头:
                case LoadingType.蓝色圆圈:
                case LoadingType.红色圆圈:
                case LoadingType.自定义:
                    config.IsIngoreTheme = true;
                    break;
            }
            config.ImgName = (Equals(type, LoadingType.自定义) ? "" : "_") + type.ToString();

            return config;
        }
    }

    public enum ToolDoubleClickEnum
    {
        显示主窗体 = 1,
        不做任何操作 = 2,
        截图识别 = 0,
        快速截图 = 3,
        截图编辑 = 4,
        快速贴图 = 5,
        截图贴图 = 6,
        粘贴贴图 = 7,
        显隐贴图 = 8,
    }
}