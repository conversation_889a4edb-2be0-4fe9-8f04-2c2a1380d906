﻿using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Language;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class ColorPickerForm : MetroForm
    {
        private bool _controlChangingColor;

        private bool _oldColorExist;

        private ColorPickerForm(Color currentColor, bool isColorPick)
        {
            Font = CommonString.GetSysNormalFont(13F);
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;

            PrepareColorPalette();
            SetCurrentColor(currentColor, true);

            if (isColorPick)
                btnOK.Visible = btnCancel.Visible = true;
            else
                btnCopy.Visible = btnClose.Visible = true;
        }

        public MyColor NewColor { get; private set; }
        public MyColor OldColor { get; private set; }

        public static bool PickColor(Color currentColor, out Color newColor, bool isDialog, Form owner = null)
        {
            AddRecentColor(currentColor);
            using (var dialog = new ColorPickerForm(currentColor, isDialog))
            {
                dialog.Icon = FrmMain.FrmTool.Icon;
                if (dialog.ShowDialog(owner) == DialogResult.OK)
                {
                    newColor = dialog.NewColor;
                    return true;
                }
            }

            newColor = currentColor;
            return false;
        }

        private void PrepareColorPalette()
        {
            flpColorPalette.Controls.Clear();

            var colors = rbRecentColors.Checked ? CommonString.RecentColors.ToArray() : ColorHelper.StandardColors;

            var length = Math.Min(colors.Length, CommonString.RecentColorsMax);

            var previousColor = Color.Empty;

            for (var i = 0; i < length; i++)
            {
                var colorButton = new ColorButton
                {
                    Color = colors[i],
                    Size = new Size(16, 16),
                    Margin = new Padding(1),
                    BorderColor = Color.FromArgb(100, 100, 100),
                    Offset = 0,
                    HoverEffect = true,
                    ManualButtonClick = true
                };

                colorButton.MouseClick += (sender, e) =>
                {
                    if (e.Button == MouseButtons.Left)
                    {
                        SetCurrentColor(colorButton.Color, true);

                        if (!previousColor.IsEmpty && previousColor == colorButton.Color)
                            CloseOk();
                        else
                            previousColor = colorButton.Color;
                    }
                };

                flpColorPalette.Controls.Add(colorButton);
                if ((i + 1) % 16 == 0) flpColorPalette.SetFlowBreak(colorButton, true);
            }
        }

        public static void AddRecentColor(Color color)
        {
            CommonString.RecentColors.Remove(color);

            if (CommonString.RecentColors.Count >= CommonString.RecentColorsMax)
                CommonString.RecentColors.RemoveRange(CommonString.RecentColorsMax - 1,
                    CommonString.RecentColors.Count - CommonString.RecentColorsMax + 1);

            CommonString.RecentColors.Insert(0, color);
        }

        public void SetCurrentColor(Color currentColor, bool keepPreviousColor)
        {
            _oldColorExist = keepPreviousColor;
            lblOld.Visible = _oldColorExist;
            NewColor = OldColor = currentColor;
            colorPicker.ChangeColor(currentColor);
            nudAlpha.SetValue(currentColor.A);
            DrawPreviewColors();
        }

        private void UpdateControls(MyColor color, ColorType type)
        {
            DrawPreviewColors();
            _controlChangingColor = true;

            if (type != ColorType.HSB)
            {
                nudHue.SetValue((decimal)Math.Round(color.HSB.Hue360));
                nudSaturation.SetValue((decimal)Math.Round(color.HSB.Saturation100));
                nudBrightness.SetValue((decimal)Math.Round(color.HSB.Brightness100));
            }

            if (type != ColorType.RGBA)
            {
                nudRed.SetValue(color.RGBA.Red);
                nudGreen.SetValue(color.RGBA.Green);
                nudBlue.SetValue(color.RGBA.Blue);
                nudAlpha.SetValue(color.RGBA.Alpha);
            }

            if (type != ColorType.CMYK)
            {
                nudCyan.SetValue((decimal)color.CMYK.Cyan100);
                nudMagenta.SetValue((decimal)color.CMYK.Magenta100);
                nudYellow.SetValue((decimal)color.CMYK.Yellow100);
                nudKey.SetValue((decimal)color.CMYK.Key100);
            }

            if (type != ColorType.Hex) txtHex.Text = ColorHelper.ColorToHex(color);

            lblNameValue.Text = ColorHelper.GetColorName(color);

            _controlChangingColor = false;
        }

        private void DrawPreviewColors()
        {
            var bmp = new Bitmap(pbColorPreview.ClientSize.Width, pbColorPreview.ClientSize.Height);

            using (var g = Graphics.FromImage(bmp))
            {
                var bmpHeight = bmp.Height;

                if (_oldColorExist)
                {
                    bmpHeight /= 2;

                    using (var oldColorBrush = new SolidBrush(OldColor))
                    {
                        g.FillRectangle(oldColorBrush, new Rectangle(0, bmpHeight, bmp.Width, bmpHeight));
                    }
                }

                using (var newColorBrush = new SolidBrush(NewColor))
                {
                    g.FillRectangle(newColorBrush, new Rectangle(0, 0, bmp.Width, bmpHeight));
                }
            }

            using (bmp)
            {
                pbColorPreview.LoadImage(bmp);
            }
        }

        private void CloseOk()
        {
            AddRecentColor(NewColor);
            DialogResult = DialogResult.OK;
            Close();
        }

        #region Events

        private void ColorPickerForm_Shown(object sender, EventArgs e)
        {
            this.ForceActivate();
        }

        private void colorPicker_ColorChanged(object sender, ColorEventArgs e)
        {
            NewColor = e.Color;
            UpdateControls(NewColor, e.ColorType);
        }

        private void rbRecentColors_CheckedChanged(object sender, EventArgs e)
        {
            PrepareColorPalette();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            CloseOk();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void rbHue_CheckedChanged(object sender, EventArgs e)
        {
            if (rbHue.Checked) colorPicker.DrawStyle = DrawStyle.Hue;
        }

        private void rbSaturation_CheckedChanged(object sender, EventArgs e)
        {
            if (rbSaturation.Checked) colorPicker.DrawStyle = DrawStyle.Saturation;
        }

        private void rbBrightness_CheckedChanged(object sender, EventArgs e)
        {
            if (rbBrightness.Checked) colorPicker.DrawStyle = DrawStyle.Brightness;
        }

        private void rbRed_CheckedChanged(object sender, EventArgs e)
        {
            if (rbRed.Checked) colorPicker.DrawStyle = DrawStyle.Red;
        }

        private void rbGreen_CheckedChanged(object sender, EventArgs e)
        {
            if (rbGreen.Checked) colorPicker.DrawStyle = DrawStyle.Green;
        }

        private void rbBlue_CheckedChanged(object sender, EventArgs e)
        {
            if (rbBlue.Checked) colorPicker.DrawStyle = DrawStyle.Blue;
        }

        private void RGB_ValueChanged(object sender, EventArgs e)
        {
            if (!_controlChangingColor)
                colorPicker.ChangeColor(
                    Color.FromArgb((int)nudAlpha.Value, (int)nudRed.Value, (int)nudGreen.Value, (int)nudBlue.Value),
                    ColorType.RGBA);
        }

        private void cbTransparent_Click(object sender, EventArgs e)
        {
            nudAlpha.Value = nudAlpha.Value == 0 ? 255 : 0;
        }

        private void HSB_ValueChanged(object sender, EventArgs e)
        {
            if (!_controlChangingColor)
                colorPicker.ChangeColor(
                    new HSB((int)nudHue.Value, (int)nudSaturation.Value, (int)nudBrightness.Value,
                        (int)nudAlpha.Value).ToColor(), ColorType.HSB);
        }

        private void CMYK_ValueChanged(object sender, EventArgs e)
        {
            if (!_controlChangingColor)
                colorPicker.ChangeColor(new Cmyk((double)nudCyan.Value / 100, (double)nudMagenta.Value / 100,
                    (double)nudYellow.Value / 100,
                    (double)nudKey.Value / 100, (int)nudAlpha.Value).ToColor(), ColorType.CMYK);
        }

        private void txtHex_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (!_controlChangingColor) colorPicker.ChangeColor(ColorHelper.HexToColor(txtHex.Text), ColorType.Hex);
            }
            catch
            {
                // ignored
            }
        }

        private void pbColorPreview_MouseClick(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left && _oldColorExist) colorPicker.ChangeColor(OldColor);
        }

        private void BtnCopy_Click(object sender, EventArgs e)
        {
            CommonMethod.ShowColorInfo(colorPicker.SelectedColor.RGBA, "调色板".CurrentText());
        }

        #endregion Events
    }

    public enum DrawStyle
    {
        Hue,
        Saturation,
        Brightness,
        Red,
        Green,
        Blue
    }

    public class MenuButton : Button
    {
        public MenuButton()
        {
            TextChanged += CheckBoxWithTip_TextChanged;
        }

        private void CheckBoxWithTip_TextChanged(object sender, EventArgs e)
        {
            InitPicBox();
        }

        [DefaultValue(null)] public ContextMenuStrip Menu { get; set; }

        public string Value { get; set; }

        [DefaultValue(false)] public bool ShowMenuUnderCursor { get; set; }

        protected override void OnMouseDown(MouseEventArgs mevent)
        {
            base.OnMouseDown(mevent);

            if (Menu != null && mevent.Button == MouseButtons.Left)
            {
                var menuLocation = ShowMenuUnderCursor ? mevent.Location : new Point(0, Height);

                Menu.Show(this, menuLocation);
            }
        }

        protected override void OnPaint(PaintEventArgs pevent)
        {
            base.OnPaint(pevent);

            if (Menu != null)
            {
                var arrowX = (pictureBox != null ? pictureBox.Left : ClientRectangle.Width) - 14;
                var arrowY = ClientRectangle.Height / 2 - 1;

                var color = Enabled ? ForeColor : SystemColors.ControlDark;
                using (Brush brush = new SolidBrush(color))
                {
                    Point[] arrows =
                        {new Point(arrowX, arrowY), new Point(arrowX + 7, arrowY), new Point(arrowX + 3, arrowY + 4)};
                    pevent.Graphics.FillPolygon(brush, arrows);
                }
            }
        }

        private string tipText;

        public string TipText { get => tipText; set { tipText = value; InitPicBox(); } }

        public ToolTip TipControl { get; set; }
        public Bitmap TipIcon { get; set; }

        private PictureBox pictureBox;

        private int btnWidth;

        private void InitPicBox()
        {
            if (string.IsNullOrEmpty(tipText) || TipControl == null)
            {
                return;
            }
            if (pictureBox == null)
            {
                pictureBox = new PictureBox
                {
                    BackColor = this.BackColor,
                    Image = TipIcon ?? Properties.Resources.帮助,
                    SizeMode = PictureBoxSizeMode.CenterImage,
                    TabStop = false,
                    Padding = new Padding(-3, 0, 0, 0)
                };
                pictureBox.Size = new Size(this.Height, this.Height);
                Controls.Add(pictureBox);
                pictureBox.Left = btnWidth = Width;
                this.Width += pictureBox.Width;
            }
            pictureBox.Top = (int)((this.ClientRectangle.Height - pictureBox.Height) / 2 * CommonTheme.DpiScale);
            this.BringToFront();
            pictureBox.BringToFront();

            if (TipControl != null)
            {
                TipControl.SetToolTip(this, Text);
                TipControl.SetToolTip(pictureBox, TipText.CurrentText());
            }
        }
    }

    [DefaultEvent("ColorChanged")]
    public class ColorButton : Button
    {
        public delegate void ColorChangedEventHandler(Color color);

        private Color _color;

        private bool _isMouseHover;

        public Color Color
        {
            get => _color;
            set
            {
                _color = value;

                OnColorChanged(_color);

                Invalidate();
            }
        }

        [DefaultValue(typeof(Color), "DarkGray")]
        public Color BorderColor { get; set; } = Color.DarkGray;

        [DefaultValue(3)] public int Offset { get; set; } = 3;

        [DefaultValue(false)] public bool HoverEffect { get; set; }

        [DefaultValue(false)] public bool ManualButtonClick { get; set; }

        public event ColorChangedEventHandler ColorChanged;

        protected void OnColorChanged(Color color)
        {
            ColorChanged?.Invoke(color);
        }

        protected override void OnMouseClick(MouseEventArgs mevent)
        {
            base.OnMouseClick(mevent);

            if (!ManualButtonClick)
            {
                var currentColor = Color;
                if (ColorPickerForm.PickColor(currentColor, out var newColor, true))
                {
                    Color = newColor;
                }
            }
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            _isMouseHover = true;

            base.OnMouseEnter(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            _isMouseHover = false;

            base.OnMouseLeave(e);
        }

        protected override void OnPaint(PaintEventArgs pevent)
        {
            base.OnPaint(pevent);

            var boxSize = ClientRectangle.Height - Offset * 2;
            var boxRectangle = new Rectangle(ClientRectangle.Width - Offset - boxSize, Offset, boxSize, boxSize);

            var g = pevent.Graphics;

            if (Color.A < 255)
                using (Image checker = ImageProcessHelper.CreateCheckerPattern(boxSize, boxSize))
                {
                    g.DrawImage(checker, boxRectangle);
                }

            if (Color.A > 0)
                using (Brush brush = new SolidBrush(Color))
                {
                    g.FillRectangle(brush, boxRectangle);
                }

            if (HoverEffect && _isMouseHover)
                using (Brush hoverBrush = new SolidBrush(Color.FromArgb(100, 255, 255, 255)))
                {
                    g.FillRectangle(hoverBrush, boxRectangle);
                }

            using (var borderPen = new Pen(BorderColor))
            {
                g.DrawRectangleProper(borderPen, boxRectangle);
            }
        }
    }
}