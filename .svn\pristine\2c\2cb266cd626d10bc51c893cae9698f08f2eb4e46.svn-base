﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32014.148
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OcrMain", "OcrMain\OcrMain.csproj", "{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OcrLib", "OcrLib\OcrLib.csproj", "{949A6CBC-80B3-42DA-A606-82D628096C0E}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|Mixed Platforms = Debug|Mixed Platforms
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|Mixed Platforms = Release|Mixed Platforms
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}.Debug|x64.ActiveCfg = Debug|Any CPU
		{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}.Debug|x64.Build.0 = Debug|Any CPU
		{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}.Debug|x86.ActiveCfg = Debug|Any CPU
		{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}.Debug|x86.Build.0 = Debug|Any CPU
		{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}.Release|Any CPU.Build.0 = Release|Any CPU
		{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}.Release|x64.ActiveCfg = Release|Any CPU
		{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}.Release|x64.Build.0 = Release|Any CPU
		{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}.Release|x86.ActiveCfg = Release|Any CPU
		{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}.Release|x86.Build.0 = Release|Any CPU
		{949A6CBC-80B3-42DA-A606-82D628096C0E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{949A6CBC-80B3-42DA-A606-82D628096C0E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{949A6CBC-80B3-42DA-A606-82D628096C0E}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{949A6CBC-80B3-42DA-A606-82D628096C0E}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{949A6CBC-80B3-42DA-A606-82D628096C0E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{949A6CBC-80B3-42DA-A606-82D628096C0E}.Debug|x64.Build.0 = Debug|Any CPU
		{949A6CBC-80B3-42DA-A606-82D628096C0E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{949A6CBC-80B3-42DA-A606-82D628096C0E}.Debug|x86.Build.0 = Debug|Any CPU
		{949A6CBC-80B3-42DA-A606-82D628096C0E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{949A6CBC-80B3-42DA-A606-82D628096C0E}.Release|Any CPU.Build.0 = Release|Any CPU
		{949A6CBC-80B3-42DA-A606-82D628096C0E}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{949A6CBC-80B3-42DA-A606-82D628096C0E}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{949A6CBC-80B3-42DA-A606-82D628096C0E}.Release|x64.ActiveCfg = Release|Any CPU
		{949A6CBC-80B3-42DA-A606-82D628096C0E}.Release|x64.Build.0 = Release|Any CPU
		{949A6CBC-80B3-42DA-A606-82D628096C0E}.Release|x86.ActiveCfg = Release|Any CPU
		{949A6CBC-80B3-42DA-A606-82D628096C0E}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {0D78471A-2E8B-4745-9404-B8AFFC757936}
	EndGlobalSection
EndGlobal
