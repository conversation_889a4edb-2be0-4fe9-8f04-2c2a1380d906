﻿using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;

namespace OcrMain
{
    public static class IniHelper
    {
        static string IniFileName;
        static IniHelper()
        {
            IniFileName = AppDomain.CurrentDomain.BaseDirectory + "config.ini";
        }

        [DllImport("kernel32")]
        private static extern int GetPrivateProfileString(string sectionName, string key, string defaultValue,
            byte[] returnBuffer, int size, string filePath);

        public static string GetValue(string sectionName, string key, string defaultValue = null)
        {
            string result = null;
            try
            {
                if (!string.IsNullOrEmpty(key))
                {
                    InitIniFile(IniFileName);
                    var array = new byte[2048];
                    var privateProfileString =
                        GetPrivateProfileString(sectionName, key, string.Empty, array, 999, IniFileName);
                    result = Encoding.Default.GetString(array, 0, privateProfileString);
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine("读取配置文件出错！" + oe.Message);
            }

            if (string.IsNullOrEmpty(result) && !string.IsNullOrEmpty(defaultValue)) result = defaultValue;
            return result;
        }

        private static void InitIniFile(string fileName)
        {
            if (!File.Exists(fileName))
                using (File.Create(fileName))
                {
                }
        }
    }
}
