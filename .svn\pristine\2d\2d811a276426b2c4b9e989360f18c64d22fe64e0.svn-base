﻿using OCRTools.Units;
using System;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class CalibrationForm : Form
    {
        private readonly RulerPainter painter;
        private readonly Settings previewSettings;

        public CalibrationForm(Settings settings)
        {
            InitializeComponent();
            painter = new RulerPainter(panPreview);
            // we only copy relevant settings
            previewSettings = new Settings
            {
                MeasuringUnit = settings.MeasuringUnit,
                MonitorDpi = settings.MonitorDpi,
                MonitorScaling = settings.MonitorScaling
            };
        }

        public float MonitorDpi => previewSettings.MonitorDpi;
        public int MonitorScaling => previewSettings.MonitorScaling;

        private void CalibrationForm_Load(object sender, EventArgs e)
        {
            // Set initial states
            numDPI.Value = (decimal) previewSettings.MonitorDpi;
            numScaling.Value = previewSettings.MonitorScaling;
            foreach (Enum item in Enum.GetValues(typeof(MeasuringUnit))) comUnits.Items.Add(item.ToString());
            comUnits.SelectedIndex = (int) previewSettings.MeasuringUnit;
        }

        private void panPreview_Paint(object sender, PaintEventArgs e)
        {
            painter.Update(e.Graphics, previewSettings, RulerFormResizeMode.Horizontal);
            painter.PaintRuler();
        }

        private void numDPI_ValueChanged(object sender, EventArgs e)
        {
            previewSettings.MonitorDpi = (float) Math.Round(numDPI.Value, 2);
            panPreview.Invalidate();
        }

        private void numScaling_ValueChanged(object sender, EventArgs e)
        {
            previewSettings.MonitorScaling = (int) numScaling.Value;
            panPreview.Invalidate();
        }

        private void comUnits_SelectedIndexChanged(object sender, EventArgs e)
        {
            previewSettings.MeasuringUnit = (MeasuringUnit) comUnits.SelectedIndex;
            panPreview.Invalidate();
        }

        private void butSubmit_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
            Close();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
    }
}