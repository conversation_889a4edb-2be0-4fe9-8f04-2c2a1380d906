using System;
using System.IO;
using System.Text;
using QiHe.CodeLib;

namespace ExcelLibrary.BinaryDrawingFormat
{
	public class MsofbtBSE : EscherRecord
	{
		public byte BlipTypeWin32;

		public byte BlipTypeMacOS;

		public Guid UID;

		public ushort Tag;

		public uint BlipSize;

		public int Ref;

		public int Offset;

		public byte Usage;

		public byte NameLength;

		public byte Unused2;

		public byte Unused3;

		public string BlipName;

		public MsofbtBlip BlipRecord;

		public byte[] ImageData;

		public byte[] ExtraData;

		public MsofbtBSE(EscherRecord record)
			: base(record)
		{
		}

		public MsofbtBSE()
		{
			Type = 61447;
		}

		public override void Decode()
		{
			MemoryStream memoryStream = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(memoryStream);
			BlipTypeWin32 = binaryReader.ReadByte();
			BlipTypeMacOS = binaryReader.ReadByte();
			UID = new Guid(binaryReader.ReadBytes(16));
			Tag = binaryReader.ReadUInt16();
			BlipSize = binaryReader.ReadUInt32();
			Ref = binaryReader.ReadInt32();
			Offset = binaryReader.ReadInt32();
			Usage = binaryReader.ReadByte();
			NameLength = binaryReader.ReadByte();
			Unused2 = binaryReader.ReadByte();
			Unused3 = binaryReader.ReadByte();
			if (NameLength > 0)
			{
				BlipName = Encoding.Unicode.GetString(binaryReader.ReadBytes(NameLength));
			}
			if (memoryStream.Position < memoryStream.Length)
			{
				BlipRecord = EscherRecord.Read(memoryStream) as MsofbtBlip;
				if (BlipRecord == null)
				{
					throw new Exception("Image Type Not supported.");
				}
				BlipRecord.Decode();
				ImageData = BlipRecord.ImageData;
			}
			if (memoryStream.Position < memoryStream.Length)
			{
				ExtraData = StreamHelper.ReadToEnd(memoryStream);
			}
		}

		public override void Encode()
		{
			BlipRecord.Encode();
			BlipSize = BlipRecord.Size + 8;
			if (BlipName != null)
			{
				NameLength = (byte)BlipName.Length;
			}
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(BlipTypeWin32);
			binaryWriter.Write(BlipTypeMacOS);
			binaryWriter.Write(UID.ToByteArray());
			binaryWriter.Write(Tag);
			binaryWriter.Write(BlipSize);
			binaryWriter.Write(Ref);
			binaryWriter.Write(Offset);
			binaryWriter.Write(Usage);
			binaryWriter.Write(NameLength);
			binaryWriter.Write(Unused2);
			binaryWriter.Write(Unused3);
			if (NameLength > 0)
			{
				binaryWriter.Write(Encoding.Unicode.GetBytes(BlipName));
			}
			BlipRecord.Write(binaryWriter);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
