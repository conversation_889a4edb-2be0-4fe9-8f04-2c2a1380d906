// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAComWrapperInternal;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public abstract class Condition
    {
        public static readonly Condition TrueCondition = BoolCondition.Wrap(true);

        internal abstract IUIAutomationCondition NativeCondition { get; }

        internal static Condition Wrap(IUIAutomationCondition obj)
        {
            if (obj is IUIAutomationBoolCondition condition)
                return new BoolCondition(condition);
            if (obj is IUIAutomationAndCondition andCondition)
                return new AndCondition(andCondition);
            if (obj is IUIAutomationOrCondition orCondition)
                return new OrCondition(orCondition);
            if (obj is IUIAutomationNotCondition notCondition)
                return new NotCondition(notCondition);
            if (obj is IUIAutomationPropertyCondition propertyCondition)
                return new PropertyCondition(propertyCondition);
            throw new ArgumentException("obj");
        }


        private class BoolCondition : Condition
        {
            private readonly IUIAutomationBoolCondition _obj;

            internal BoolCondition(IUIAutomationBoolCondition obj)
            {
                Debug.Assert(obj != null);
                _obj = obj;
            }

            internal override IUIAutomationCondition NativeCondition => _obj;

            internal static BoolCondition Wrap(bool b)
            {
                var obj = (IUIAutomationBoolCondition) (b
                    ? Automation.Factory.CreateTrueCondition()
                    : Automation.Factory.CreateFalseCondition());
                return new BoolCondition(obj);
            }
        }
    }

    public class NotCondition : Condition
    {
        internal IUIAutomationNotCondition _obj;


        internal NotCondition(IUIAutomationNotCondition obj)
        {
            Debug.Assert(obj != null);
            _obj = obj;
        }

        internal override IUIAutomationCondition NativeCondition => _obj;
    }

    public class AndCondition : Condition
    {
        internal IUIAutomationAndCondition _obj;


        internal AndCondition(IUIAutomationAndCondition obj)
        {
            Debug.Assert(obj != null);
            _obj = obj;
        }

        internal override IUIAutomationCondition NativeCondition => _obj;
    }

    public class OrCondition : Condition
    {
        internal IUIAutomationOrCondition _obj;


        internal OrCondition(IUIAutomationOrCondition obj)
        {
            Debug.Assert(obj != null);
            _obj = obj;
        }

        internal override IUIAutomationCondition NativeCondition => _obj;
    }

    [Flags]
    public enum PropertyConditionFlags
    {
        None,
        IgnoreCase
    }

    public class PropertyCondition : Condition
    {
        internal IUIAutomationPropertyCondition _obj;


        internal PropertyCondition(IUIAutomationPropertyCondition obj)
        {
            Debug.Assert(obj != null);
            _obj = obj;
        }

        public PropertyCondition(AutomationProperty property, object value)
        {
            Init(property, value, PropertyConditionFlags.None);
        }

        internal override IUIAutomationCondition NativeCondition => _obj;


        public object Value => _obj.PropertyValue;

        private void Init(AutomationProperty property, object val, PropertyConditionFlags flags)
        {
            Utility.ValidateArgumentNonNull(property, "property");

            _obj = (IUIAutomationPropertyCondition)
                Automation.Factory.CreatePropertyConditionEx(
                    property.Id,
                    Utility.UnwrapObject(val),
                    (UIAutomationClient.PropertyConditionFlags) flags);
        }
    }
}