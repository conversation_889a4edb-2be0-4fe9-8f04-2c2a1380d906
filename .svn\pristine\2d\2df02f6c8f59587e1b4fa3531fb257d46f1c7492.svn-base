using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class TablePattern : GridPattern
    {
        public new static readonly AutomationPattern Pattern = TablePatternIdentifiers.Pattern;

        public static readonly AutomationProperty RowOrColumnMajorProperty =
            TablePatternIdentifiers.RowOrColumnMajorProperty;


        private TablePattern(AutomationElement el, IUIAutomationTablePattern tablePattern,
            IUIAutomationGridPattern gridPattern, bool cached)
            : base(el, gridPattern, cached)
        {
            Debug.Assert(tablePattern != null);
        }

        internal new static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            TablePattern result = null;
            if (pattern != null)
            {
                var gridPattern =
                    (IUIAutomationGridPattern)el.GetRawPattern(GridPattern.Pattern, cached);
                if (gridPattern != null)
                    result = new TablePattern(el, (IUIAutomationTablePattern)pattern,
                        gridPattern, cached);
            }

            return result;
        }
    }

    public class TableItemPattern : GridItemPattern
    {
        public new static readonly AutomationPattern Pattern = TableItemPatternIdentifiers.Pattern;

        private TableItemPattern(AutomationElement el, IUIAutomationTableItemPattern tablePattern,
            IUIAutomationGridItemPattern gridPattern, bool cached)
            : base(el, gridPattern, cached)
        {
            Debug.Assert(tablePattern != null);
        }

        internal new static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            TableItemPattern result = null;
            if (pattern != null)
            {
                var gridPattern =
                    (IUIAutomationGridItemPattern)el.GetRawPattern(GridItemPattern.Pattern, cached);
                if (gridPattern != null)
                    result = new TableItemPattern(el, (IUIAutomationTableItemPattern)pattern,
                        gridPattern, cached);
            }

            return result;
        }
    }
}