﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace ImageLib
{
    public class ImageHelper
    {
        private const string StrUrlStart = "http";
        static List<int> listProcessEntity = new List<int>() { 1, 2, 3, 4, 5, 6 };

        public static string GetResult(byte[] content, string fileExt)
        {
            var result = string.Empty;
            Parallel.ForEach(listProcessEntity, new ParallelOptions() { MaxDegreeOfParallelism = 2 }
            , imgType =>
            {
                if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
                {
                    var strTmp = string.Empty; ;
                    try
                    {
                        switch (imgType)
                        {
                            case 1:
                                strTmp = ALiYunUpload.GetResult(content);
                                break;
                            case 2:
                                strTmp = SouGouImageUpload.GetResult(content);
                                break;
                            case 3:
                                strTmp = _360ImageUpload.GetResult(content);
                                break;
                            case 4:
                                break;
                            case 5:
                                break;
                            case 6:
                                break;
                        }
                    }
                    catch { }
                }
            });
            if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
            {
                result = TinyPngUpload.GetResultUrl(content);
            }
            if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
            {
                result = WebResizerUpload.GetResult(content);
            }
            if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
            {
                result = Net126Upload.GetResult(content);
            }
            return result;
        }
    }
    public class RefCount
    {
        public int TotalCount;

        public int MaxCount;

        public event EventHandler AchieveMaxEvent;

        public int Increment()
        {
            Interlocked.Increment(ref TotalCount);
            if (TotalCount >= MaxCount)
            {
                AchieveMaxEvent?.Invoke(null, null);
            }
            return TotalCount;
        }
    }
}
