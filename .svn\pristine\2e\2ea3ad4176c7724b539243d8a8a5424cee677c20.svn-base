﻿using OCRTools.Common;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools.ScreenCaptureLib
{
    public struct MouseState
    {
        public MouseButtons Buttons { get; private set; }
        public Point Position { get; private set; }
        public Point ClientPosition { get; private set; }

        public void Update(Control control)
        {
            Buttons = Control.MouseButtons;
            Position = Control.MousePosition;

            if (control != null)
            {
                ClientPosition = control.PointToClient(Position);
            }
            else
            {
                ClientPosition = NativeMethods.ScreenToClient(Position);
            }
        }
    }
}