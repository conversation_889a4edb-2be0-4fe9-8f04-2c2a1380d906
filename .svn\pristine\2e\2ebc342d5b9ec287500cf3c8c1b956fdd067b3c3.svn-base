using System;
using System.Runtime.InteropServices;

namespace TianruoOCR
{
	public static class Gdi32
	{
		public enum TernaryRasterOperations : uint
		{
			SRCCOPY = 13369376u,
			SRCPAINT = 15597702u,
			SRCAND = 8913094u,
			SRCINVERT = 6684742u,
			SRCERASE = 4457256u,
			NOTSRCCOPY = 3342344u,
			NOTSRCERASE = 1114278u,
			MERGECOPY = 12583114u,
			MERGEPAINT = 12255782u,
			PATCOPY = 15728673u,
			PATPAINT = 16452105u,
			PATINVERT = 5898313u,
			DSTINVERT = 5570569u,
			BLACKNESS = 66u,
			WHITENESS = 16711778u,
			CAPTUREBLT = 0x40000000
		}

		public const int SRCCOPY = 13369376;

		[DllImport("gdi32.dll")]
		public static extern bool BitBlt(IntPtr hObject, int nXDest, int nYDest, int nWidth, int nHeight, IntPtr hObjectSource, int nXSrc, int nYSrc, int dwRop);

		[DllImport("gdi32.dll")]
		public static extern IntPtr CreateCompatibleBitmap(IntPtr hDC, int nWidth, int nHeight);

		[DllImport("gdi32.dll")]
		public static extern IntPtr CreateCompatibleDC(IntPtr hDC);

		[DllImport("gdi32.dll")]
		public static extern bool DeleteDC(IntPtr hDC);

		[DllImport("gdi32.dll")]
		public static extern bool DeleteObject(IntPtr hObject);

		[DllImport("gdi32.dll")]
		public static extern IntPtr SelectObject(IntPtr hDC, IntPtr hObject);

		[DllImport("gdi32.dll")]
		[return: MarshalAs(UnmanagedType.Bool)]
		public static extern bool BitBlt(IntPtr hdc, int nXDest, int nYDest, int nWidth, int nHeight, IntPtr hdcSrc, int nXSrc, int nYSrc, TernaryRasterOperations dwRop);
	}
}
