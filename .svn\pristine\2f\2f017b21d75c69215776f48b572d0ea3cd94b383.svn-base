﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Threading.Tasks;
using OCRTools.Common;
using OCRTools.Common.SNTP;

namespace OCRTools
{
    /// <summary>
    ///     Represents a client which can obtain accurate time via NTP protocol.
    /// </summary>
    public class SNtpClient
    {
        private static SNtpClient _myInstance;

        /// <summary>
        ///     Not reallu used. I put this here so that I had a list of other NTP servers that could be used. I'll integrate this
        ///     information later and will provide method to allow some one to choose an NTP server.
        /// </summary>
        private List<string> _ntpServerList = new List<string>
        {
            "ntp1.aliyun.com",
            "ntp2.aliyun.com",
            "cn.ntp.org.cn",
            "edu.ntp.org.cn",
            "time.windows.com",
            "asia.pool.ntp.org",
            "ntp3.aliyun.com",
            "ntp4.aliyun.com",
            "ntp5.aliyun.com",
            "ntp6.aliyun.com",
            "ntp7.aliyun.com",
            "hk.ntp.org.cn",
            "tw.ntp.org.cn",
            "pool.ntp.org"
        };

        private string _strNtpServer;

        static SNtpClient()
        {
            Task.Factory.StartNew(InitNtpServers);
        }

        public static SNtpClient Instance
        {
            get
            {
                if (_myInstance == null)
                {
                    _myInstance = new SNtpClient();
                    _myInstance._strNtpServer = _myInstance._ntpServerList[0];
                }

                return _myInstance;
            }
        }

        private static void InitNtpServers()
        {
            var lstResult = new List<string>();
            var html = WebClientExt.GetHtml("http://www.ntppod.com/pod.php", CommonMethod.ExecTimeOutSecond);
            if (!string.IsNullOrEmpty(html)) lstResult.AddRange(InitNtpServerByHtml(html));
            html = WebClientExt.GetHtml("http://www.ntp.org.cn/pool", CommonMethod.ExecTimeOutSecond);
            if (!string.IsNullOrEmpty(html)) lstResult.AddRange(InitNtpServerByHtml(html));
            html = WebClientExt.GetHtml("https://ntp.mzr.me/index.html", CommonMethod.ExecTimeOutSecond);
            if (!string.IsNullOrEmpty(html)) lstResult.AddRange(InitNtpServerByHtml(html, "[", "]"));
            lstResult.AddRange(Instance._ntpServerList);
            lstResult = lstResult.Distinct().ToList();
            lock (Instance._ntpServerList)
            {
                Instance._ntpServerList = lstResult;
            }
        }

        private static List<string> InitNtpServerByHtml(string html, string strSpiltStart = ">",
            string strSpiltEnd = "<")
        {
            var lstResult = new List<string>();
            while (html.Contains(strSpiltStart))
            {
                html = html.Substring(html.IndexOf(strSpiltStart) + strSpiltStart.Length);
                if (html.IndexOf(strSpiltEnd) > 0)
                {
                    var ip = html.Substring(0, html.IndexOf(strSpiltEnd)).Trim();
                    if (!string.IsNullOrEmpty(ip) && DnsHelper.IsIPv4(ip))
                        if (!lstResult.Contains(ip))
                            lstResult.Add(ip);
                }
            }

            return lstResult;
        }


        public long GetNetworkTimeOffset()
        {
            long result = -9999;
            try
            {
                var syncResult = new QueryServerCompletedEventArgs();
                try
                {
                    using (var socket = new UdpClient())
                    {
                        //var strIp = DnsHelper.GetDnsIp(StrNtpServer);
                        //var ipEndPoint = new IPEndPoint(Dns.GetHostAddresses(strIp)[0], 123);
                        socket.Client.SendTimeout = 3000;
                        socket.Client.ReceiveTimeout = 1000;
                        socket.Connect(_strNtpServer, 123);
                        IPEndPoint ipEndPoint = null;
                        // Send and receive the data, and save the completion DateTime.
                        var request = SntpData.GetClientRequestPacket(VersionNumber.Version3);
                        socket.Send(request, request.Length);
                        syncResult.Data = socket.Receive(ref ipEndPoint);
                        syncResult.Data.DestinationDateTime = DateTime.Now.ToUniversalTime();

                        // Check the data
                        if (syncResult.Data.Mode == Mode.Server) syncResult.Succeeded = true;
                    }
                }
                catch
                {
                }

                if (!syncResult.Succeeded)
                {
                    var index = _ntpServerList.IndexOf(_strNtpServer);
                    index = _ntpServerList.Count - 1 <= index ? 0 : index + 1;
                    if (_ntpServerList?.Count <= 0) InitNtpServers();
                    _strNtpServer = _ntpServerList[index];
                }
                else
                {
                    result = syncResult.Data.LocalClockOffsetTicks;
                }
            }
            catch (Exception)
            {
                result = -9999;
            }

            return result;
        }

        //public DateTime GetNetworkTime()
        //{
        //    var result = DateTime.MinValue;
        //    try
        //    {
        //        var ticks = GetNetworkTimeOffset();
        //        result = ServerTime.DateTime.AddTicks(ticks);
        //    }
        //    catch (Exception)
        //    {
        //        result = DateTime.MinValue;
        //    }
        //    return result;
        //}
    }
}