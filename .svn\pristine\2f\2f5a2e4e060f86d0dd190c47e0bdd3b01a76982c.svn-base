﻿using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Drawing.Text;
using System.Security.Permissions;
using System.Windows.Forms;

namespace OCRTools.UserControlEx
{
    [ToolboxBitmap(typeof(TabControl))]
    public class TabControlExtra : TabControl
    {
        public const int TabCloserButtonSize = 15;

        private const int AnyRightAlign = 1092;

        private const int AnyLeftAlign = 273;

        private const int AnyTopAlign = 7;

        private const int AnyBottomAlign = 1792;

        private const int AnyMiddleAlign = 112;

        private const int AnyCenterAlign = 546;

        private Bitmap _BackImage;

        private Bitmap _BackBuffer;

        private Graphics _BackBufferGraphics;

        private Bitmap _TabBuffer;

        private Graphics _TabBufferGraphics;

        private GraphicsPath _PrevTabCloserButtonPath;

        private int _oldValue;

        private Point _dragStartPosition = Point.Empty;

        private TabStyle _Style;

        private TabStyleProvider _StyleProvider;

        private List<TabPage> _TabPages;

        private bool _SuspendDrawing;

        protected override CreateParams CreateParams
        {
            [SecurityPermission(SecurityAction.LinkDemand, Flags = SecurityPermissionFlag.UnmanagedCode)]
            get
            {
                CreateParams createParams = base.CreateParams;
                if (EffectiveRightToLeft)
                {
                    createParams.ExStyle = createParams.ExStyle | 0x400000 | 0x100000;
                }
                return createParams;
            }
        }

        [Category("Appearance")]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Content)]
        public TabStyleProvider DisplayStyleProvider
        {
            get
            {
                if (_StyleProvider == null)
                {
                    DisplayStyle = TabStyle.Default;
                }
                return _StyleProvider;
            }
            set
            {
                _StyleProvider = value;
            }
        }

        [Category("Appearance")]
        [DefaultValue(typeof(TabStyle), "Default")]
        [RefreshProperties(RefreshProperties.All)]
        public TabStyle DisplayStyle
        {
            get
            {
                return _Style;
            }
            set
            {
                if (_Style != value)
                {
                    _Style = value;
                    _StyleProvider = TabStyleProvider.CreateProvider(this);
                }
            }
        }

        [Browsable(false)]
        [EditorBrowsable(EditorBrowsableState.Never)]
        public new Point MousePosition
        {
            get
            {
                Point point = PointToClient(Control.MousePosition);
                return AdjustPointForRightToLeft(point);
            }
        }

        [Category("Appearance")]
        [RefreshProperties(RefreshProperties.All)]
        public new bool Multiline
        {
            get
            {
                return base.Multiline;
            }
            set
            {
                base.Multiline = value;
            }
        }

        [Browsable(false)]
        [EditorBrowsable(EditorBrowsableState.Never)]
        public new Point Padding
        {
            get
            {
                return DisplayStyleProvider.Padding;
            }
            set
            {
                DisplayStyleProvider.Padding = value;
            }
        }

        [Category("Appearance")]
        [RefreshProperties(RefreshProperties.All)]
        public override bool RightToLeftLayout
        {
            get
            {
                return base.RightToLeftLayout;
            }
            set
            {
                base.RightToLeftLayout = value;
                UpdateStyles();
            }
        }

        [Browsable(false)]
        [EditorBrowsable(EditorBrowsableState.Never)]
        public new bool HotTrack
        {
            get
            {
                return DisplayStyleProvider.HotTrack;
            }
            set
            {
                DisplayStyleProvider.HotTrack = value;
            }
        }

        [Category("Appearance")]
        public new TabAlignment Alignment
        {
            get
            {
                return base.Alignment;
            }
            set
            {
                base.Alignment = value;
                switch (value)
                {
                    case TabAlignment.Top:
                    case TabAlignment.Bottom:
                        Multiline = false;
                        break;
                    case TabAlignment.Left:
                    case TabAlignment.Right:
                        Multiline = true;
                        break;
                }
            }
        }

        [Browsable(false)]
        [EditorBrowsable(EditorBrowsableState.Never)]
        public new TabAppearance Appearance
        {
            get
            {
                return base.Appearance;
            }
            set
            {
                base.Appearance = TabAppearance.Normal;
            }
        }

        public override Rectangle DisplayRectangle
        {
            get
            {
                if (_Style == TabStyle.None)
                {
                    return new Rectangle(0, 0, base.Width, base.Height);
                }
                int num = 0;
                int num2 = 0;
                num2 = ((Alignment > TabAlignment.Bottom) ? base.ItemSize.Width : base.ItemSize.Height);
                num = 5 + num2 * base.RowCount;
                Rectangle result = new Rectangle(4, num, base.Width - 8, base.Height - num - 4);
                switch (Alignment)
                {
                    case TabAlignment.Top:
                        result = new Rectangle(4, num, base.Width - 8, base.Height - num - 4);
                        break;
                    case TabAlignment.Bottom:
                        result = new Rectangle(4, 4, base.Width - 8, base.Height - num - 4);
                        break;
                    case TabAlignment.Left:
                        result = new Rectangle(num, 4, base.Width - num - 4, base.Height - 8);
                        break;
                    case TabAlignment.Right:
                        result = new Rectangle(4, 4, base.Width - num - 4, base.Height - 8);
                        break;
                }
                return result;
            }
        }

        private bool EffectiveRightToLeft
        {
            get
            {
                if (RightToLeft == RightToLeft.Yes || (RightToLeft == RightToLeft.Inherit && base.Parent.RightToLeft == RightToLeft.Yes))
                {
                    return RightToLeftLayout;
                }
                return false;
            }
        }

        [Category("Action")]
        public event ScrollEventHandler HScroll;

        [Category("Action")]
        public event EventHandler<TabControlEventArgs> TabImageClick;

        [Category("Action")]
        public event EventHandler<TabControlCancelEventArgs> TabClosing;

        [Category("Action")]
        public event EventHandler<TabControlEventArgs> TabClosed;

        public TabControlExtra()
        {
            SetStyle(ControlStyles.UserPaint | ControlStyles.Opaque | ControlStyles.ResizeRedraw | ControlStyles.AllPaintingInWmPaint, value: true);
            _BackBuffer = new Bitmap(base.Width, base.Height);
            _BackBufferGraphics = Graphics.FromImage(_BackBuffer);
            _TabBuffer = new Bitmap(base.Width, base.Height);
            _TabBufferGraphics = Graphics.FromImage(_TabBuffer);
            SuspendLayout();
            DisplayStyle = TabStyle.Default;
            ResumeLayout();
        }

        protected override void OnCreateControl()
        {
            base.OnCreateControl();
            OnFontChanged(EventArgs.Empty);
        }

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);
            if (disposing)
            {
                if (_BackImage != null)
                {
                    _BackImage.Dispose();
                }
                if (_BackBufferGraphics != null)
                {
                    _BackBufferGraphics.Dispose();
                }
                if (_BackBuffer != null)
                {
                    _BackBuffer.Dispose();
                }
                if (_TabBufferGraphics != null)
                {
                    _TabBufferGraphics.Dispose();
                }
                if (_TabBuffer != null)
                {
                    _TabBuffer.Dispose();
                }
                if (_StyleProvider != null)
                {
                    _StyleProvider.Dispose();
                }
            }
        }

        [DebuggerStepThrough]
        public int GetActiveIndex(Point mousePosition)
        {
            int num = SendMessage(lParam: NativeMethods.ToIntPtr(new NativeMethods.TCHITTESTINFO(mousePosition)), msg: 4877, wParam: IntPtr.Zero).ToInt32();
            if (num == -1)
            {
                return -1;
            }
            if (base.TabPages[num].Enabled)
            {
                return num;
            }
            return -1;
        }

        public TabPage GetActiveTab(Point mousePosition)
        {
            int activeIndex = GetActiveIndex(mousePosition);
            if (activeIndex > -1)
            {
                return base.TabPages[activeIndex];
            }
            return null;
        }

        public void HideTab(TabPage page)
        {
            if (page != null && base.TabPages.Contains(page))
            {
                BackupTabPages();
                base.TabPages.Remove(page);
            }
        }

        public void HideTab(int index)
        {
            if (IsValidTabIndex(index))
            {
                HideTab(_TabPages[index]);
            }
        }

        public void HideTab(string key)
        {
            if (base.TabPages.ContainsKey(key))
            {
                HideTab(base.TabPages[key]);
            }
        }

        public void ShowTab(TabPage page)
        {
            if (page == null)
            {
                return;
            }
            if (_TabPages != null)
            {
                if (base.TabPages.Contains(page) || !_TabPages.Contains(page))
                {
                    return;
                }
                int num = _TabPages.IndexOf(page);
                if (num > 0)
                {
                    for (int num2 = num - 1; num2 >= 0; num2--)
                    {
                        if (base.TabPages.Contains(_TabPages[num2]))
                        {
                            num = base.TabPages.IndexOf(_TabPages[num2]) + 1;
                            break;
                        }
                    }
                }
                if (num >= 0 && num < base.TabPages.Count)
                {
                    base.TabPages.Insert(num, page);
                }
                else
                {
                    base.TabPages.Add(page);
                }
            }
            else if (!base.TabPages.Contains(page))
            {
                base.TabPages.Add(page);
            }
        }

        public void ShowTab(int index)
        {
            if (IsValidTabIndex(index))
            {
                ShowTab(_TabPages[index]);
            }
        }

        public void ShowTab(string key)
        {
            if (_TabPages != null)
            {
                TabPage page2 = _TabPages.Find((TabPage page) => page.Name.Equals(key, StringComparison.OrdinalIgnoreCase));
                ShowTab(page2);
            }
        }

        public void ResumeDrawing()
        {
            _SuspendDrawing = false;
        }

        public void SuspendDrawing()
        {
            _SuspendDrawing = true;
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            Point point = new Point(e.X, e.Y);
            int activeIndex = GetActiveIndex(point);
            if (!base.DesignMode && activeIndex > -1 && _StyleProvider.ShowTabCloser && GetTabCloserButtonRect(activeIndex).Contains(point))
            {
                TabControlCancelEventArgs e2 = new TabControlCancelEventArgs(GetActiveTab(point), activeIndex, cancel: false, TabControlAction.Deselecting);
                OnTabClosing(e2);
                return;
            }
            base.OnMouseDown(e);
            if (AllowDrop)
            {
                _dragStartPosition = new Point(e.X, e.Y);
            }
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            base.OnMouseUp(e);
            if (AllowDrop)
            {
                _dragStartPosition = Point.Empty;
            }
        }

        protected override void OnDragOver(DragEventArgs drgevent)
        {
            base.OnDragOver(drgevent);
            if (drgevent.Data.GetDataPresent(typeof(TabPage)))
            {
                TabPage tabPage = (TabPage)drgevent.Data.GetData(typeof(TabPage));
                Cursor = Cursors.Arrow;
                tabPage.Cursor = Cursors.Arrow;
                if (GetActiveTab(new Point(drgevent.X, drgevent.Y)) != tabPage)
                {
                    int activeIndex = GetActiveIndex(new Point(drgevent.X, drgevent.Y));
                    if (activeIndex >= 0)
                    {
                        SuspendDrawing();
                        ((TabControl)tabPage.Parent).TabPages.Remove(tabPage);
                        base.TabPages.Insert(activeIndex, tabPage);
                        base.SelectedTab = tabPage;
                        ResumeDrawing();
                        Invalidate();
                    }
                }
            }
            else
            {
                drgevent.Effect = DragDropEffects.None;
            }
        }

        private void StartDragDrop()
        {
            if (_dragStartPosition.IsEmpty)
            {
                return;
            }
            TabPage selectedTab = base.SelectedTab;
            if (selectedTab != null)
            {
                Rectangle rectangle = new Rectangle(_dragStartPosition, Size.Empty);
                rectangle.Inflate(SystemInformation.DragSize);
                Point pt = PointToClient(Control.MousePosition);
                if (!rectangle.Contains(pt))
                {
                    DoDragDrop(selectedTab, DragDropEffects.Move);
                    _dragStartPosition = Point.Empty;
                }
            }
        }

        protected override void OnFontChanged(EventArgs e)
        {
            UpdateStyles();
        }

        private void CreateGraphicsBuffers()
        {
            if (base.Width > 0 && base.Height > 0)
            {
                if (_BackImage != null)
                {
                    _BackImage.Dispose();
                    _BackImage = null;
                }
                if (_BackBufferGraphics != null)
                {
                    _BackBufferGraphics.Dispose();
                }
                if (_BackBuffer != null)
                {
                    _BackBuffer.Dispose();
                }
                _BackBuffer = new Bitmap(base.Width, base.Height);
                _BackBufferGraphics = Graphics.FromImage(_BackBuffer);
                if (_TabBufferGraphics != null)
                {
                    _TabBufferGraphics.Dispose();
                }
                if (_TabBuffer != null)
                {
                    _TabBuffer.Dispose();
                }
                _TabBuffer = new Bitmap(base.Width, base.Height);
                _TabBufferGraphics = Graphics.FromImage(_TabBuffer);
                if (_BackImage != null)
                {
                    _BackImage.Dispose();
                    _BackImage = null;
                }
            }
        }

        protected override void OnResize(EventArgs e)
        {
            CreateGraphicsBuffers();
            base.OnResize(e);
        }

        protected override void OnParentBackColorChanged(EventArgs e)
        {
            if (_BackImage != null)
            {
                _BackImage.Dispose();
                _BackImage = null;
            }
            base.OnParentBackColorChanged(e);
        }

        protected override void OnParentBackgroundImageChanged(EventArgs e)
        {
            if (_BackImage != null)
            {
                _BackImage.Dispose();
                _BackImage = null;
            }
            base.OnParentBackgroundImageChanged(e);
        }

        protected override void OnSelecting(TabControlCancelEventArgs e)
        {
            if (e.Action == TabControlAction.Selecting && e.TabPage != null && !e.TabPage.Enabled)
            {
                e.Cancel = true;
            }
            base.OnSelecting(e);
        }

        protected override void OnMove(EventArgs e)
        {
            if (base.Width > 0 && base.Height > 0 && _BackImage != null)
            {
                _BackImage.Dispose();
                _BackImage = null;
            }
            base.OnMove(e);
            Invalidate();
        }

        protected override void OnEnter(EventArgs e)
        {
            base.OnEnter(e);
            if (base.Visible)
            {
                OnPaint(new PaintEventArgs(CreateGraphics(), base.ClientRectangle));
            }
        }

        protected override void OnLeave(EventArgs e)
        {
            base.OnLeave(e);
            if (base.Visible)
            {
                OnPaint(new PaintEventArgs(CreateGraphics(), base.ClientRectangle));
            }
        }

        [UIPermission(SecurityAction.LinkDemand, Window = UIPermissionWindow.AllWindows)]
        protected override bool ProcessMnemonic(char charCode)
        {
            foreach (TabPage tabPage in base.TabPages)
            {
                if (Control.IsMnemonic(charCode, tabPage.Text))
                {
                    base.SelectedTab = tabPage;
                    return true;
                }
            }
            return base.ProcessMnemonic(charCode);
        }

        protected override void OnSelectedIndexChanged(EventArgs e)
        {
            base.OnSelectedIndexChanged(e);
        }

        [DebuggerStepThrough]
        [SecurityPermission(SecurityAction.LinkDemand, Flags = SecurityPermissionFlag.UnmanagedCode)]
        protected override void WndProc(ref Message m)
        {
            if (m.Msg == 276)
            {
                base.WndProc(ref m);
                OnHScroll(new ScrollEventArgs((ScrollEventType)NativeMethods.LoWord(m.WParam), _oldValue, NativeMethods.HiWord(m.WParam), ScrollOrientation.HorizontalScroll));
            }
            else
            {
                base.WndProc(ref m);
            }
        }

        protected override void OnMouseClick(MouseEventArgs e)
        {
            int activeIndex = GetActiveIndex(new Point(e.X, e.Y));
            if (activeIndex > -1 && this.TabImageClick != null && TabHasImage(activeIndex) && GetTabImageRect(activeIndex).Contains(MousePosition))
            {
                OnTabImageClick(new TabControlEventArgs(base.TabPages[activeIndex], activeIndex, TabControlAction.Selected));
            }
            base.OnMouseClick(e);
        }

        protected virtual void OnTabImageClick(TabControlEventArgs e)
        {
            this.TabImageClick?.Invoke(this, e);
        }

        protected virtual void OnTabClosed(TabControlEventArgs e)
        {
            this.TabClosed?.Invoke(this, e);
        }

        protected virtual void OnTabClosing(TabControlCancelEventArgs e)
        {
            this.TabClosing?.Invoke(this, e);
            if (!e.Cancel)
            {
                int num = base.SelectedIndex;
                base.TabPages.Remove(e.TabPage);
                e.TabPage.Dispose();
                if (num == base.TabPages.Count)
                {
                    base.SelectedIndex = num - 1;
                }
                else
                {
                    base.SelectedIndex = num;
                }
                OnTabClosed(new TabControlEventArgs(e.TabPage, e.TabPageIndex, e.Action));
            }
        }

        protected virtual void OnHScroll(ScrollEventArgs e)
        {
            Invalidate();
            this.HScroll?.Invoke(this, e);
            if (e.Type == ScrollEventType.EndScroll)
            {
                _oldValue = e.NewValue;
            }
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            base.OnMouseMove(e);
            Point mousePosition = MousePosition;
            if (_PrevTabCloserButtonPath == null || !_PrevTabCloserButtonPath.IsVisible(mousePosition))
            {
                bool flag = false;
                if (_PrevTabCloserButtonPath != null)
                {
                    _PrevTabCloserButtonPath.Dispose();
                    _PrevTabCloserButtonPath = null;
                    flag = true;
                }
                _PrevTabCloserButtonPath = GetTabCloserButtonPathAtPosition(mousePosition);
                if (_PrevTabCloserButtonPath != null)
                {
                    flag = true;
                }
                if (flag)
                {
                    CustomPaint(mousePosition);
                }
            }
            if (AllowDrop && e.Button == MouseButtons.Left)
            {
                StartDragDrop();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            if (!_SuspendDrawing)
            {
                _ = DateTime.Now;
                Point mousePosition = MousePosition;
                CustomPaint(mousePosition);
            }
        }

        private void CustomPaint(Point mousePosition)
        {
            if (base.Width <= 0 || base.Height <= 0)
            {
                return;
            }
            if (_BackImage == null)
            {
                _BackImage = new Bitmap(base.Width, base.Height);
                Graphics graphics = Graphics.FromImage(_BackImage);
                graphics.Clear(Color.Transparent);
                PaintTransparentBackground(graphics, base.ClientRectangle);
            }
            _BackBufferGraphics.Clear(Color.Transparent);
            _BackBufferGraphics.DrawImageUnscaled(_BackImage, 0, 0);
            if (EffectiveRightToLeft)
            {
                Matrix matrix = new Matrix();
                matrix.Translate(_TabBuffer.Width, 0f);
                matrix.Scale(-1f, 1f);
                _TabBufferGraphics.Transform = matrix;
                matrix.Dispose();
            }
            _TabBufferGraphics.Clear(Color.Transparent);
            if (base.TabCount > 0)
            {
                if (Alignment <= TabAlignment.Bottom && !Multiline)
                {
                    Rectangle clientRectangle = base.ClientRectangle;
                    _TabBufferGraphics.Clip = new Region(new RectangleF(clientRectangle.X + 4 - _StyleProvider.TabPageMargin.Left, clientRectangle.Y, clientRectangle.Width - 8 + _StyleProvider.TabPageMargin.Left + _StyleProvider.TabPageMargin.Right, clientRectangle.Height));
                }
                if (Multiline)
                {
                    for (int i = 0; i < base.RowCount; i++)
                    {
                        for (int num = base.TabCount - 1; num >= 0; num--)
                        {
                            if (num != base.SelectedIndex && (base.RowCount == 1 || GetTabRow(num) == i))
                            {
                                DrawTabPage(num, mousePosition, _TabBufferGraphics);
                            }
                        }
                    }
                }
                else
                {
                    for (int num2 = base.TabCount - 1; num2 >= 0; num2--)
                    {
                        if (num2 != base.SelectedIndex)
                        {
                            DrawTabPage(num2, mousePosition, _TabBufferGraphics);
                        }
                    }
                }
                if (base.SelectedIndex > -1)
                {
                    DrawTabPage(base.SelectedIndex, mousePosition, _TabBufferGraphics);
                }
            }
            _TabBufferGraphics.Flush();
            ColorMatrix colorMatrix = new ColorMatrix();
            float num4 = (colorMatrix.Matrix44 = 1f);
            float num6 = (colorMatrix.Matrix22 = num4);
            float num9 = (colorMatrix.Matrix00 = (colorMatrix.Matrix11 = num6));
            colorMatrix.Matrix33 = _StyleProvider.Opacity;
            using (ImageAttributes imageAttributes = new ImageAttributes())
            {
                imageAttributes.SetColorMatrix(colorMatrix);
                _BackBufferGraphics.DrawImage(_TabBuffer, new Rectangle(0, 0, _TabBuffer.Width, _TabBuffer.Height), 0, 0, _TabBuffer.Width, _TabBuffer.Height, GraphicsUnit.Pixel, imageAttributes);
            }
            _BackBufferGraphics.Flush();
            using (Graphics graphics2 = CreateGraphics())
            {
                if (EffectiveRightToLeft)
                {
                    graphics2.DrawImageUnscaled(_BackBuffer, -1, 0);
                }
                else
                {
                    graphics2.DrawImageUnscaled(_BackBuffer, 0, 0);
                }
            }
        }

        protected void PaintTransparentBackground(Graphics graphics, Rectangle clipRect)
        {
            if (base.Parent != null)
            {
                clipRect.Offset(base.Location);
                GraphicsState gstate = graphics.Save();
                graphics.TranslateTransform(-base.Location.X, -base.Location.Y);
                graphics.SmoothingMode = SmoothingMode.HighSpeed;
                PaintEventArgs e = new PaintEventArgs(graphics, clipRect);
                try
                {
                    InvokePaintBackground(base.Parent, e);
                    InvokePaint(base.Parent, e);
                }
                finally
                {
                    graphics.Restore(gstate);
                    clipRect.Offset(-base.Location.X, -base.Location.Y);
                }
            }
        }

        private void DrawTabPage(int index, Point mousePosition, Graphics graphics)
        {
            graphics.SmoothingMode = SmoothingMode.AntiAlias;
            Rectangle baseTabRect = GetBaseTabRect(index);
            Rectangle pageBounds = GetPageBounds(index);
            Rectangle tabRect = _StyleProvider.GetTabRect(baseTabRect, pageBounds, base.SelectedIndex == index);
            Rectangle rectangle = Rectangle.Intersect(baseTabRect, tabRect);
            TabState tabState = GetTabState(index, mousePosition);
            bool enabled = base.TabPages[index].Enabled;
            bool flag = _Style != 0 && IsTabVisible(tabRect, pageBounds);
            using (GraphicsPath path = GetTabPageBorder(pageBounds, tabRect))
            {
                using (GraphicsPath graphicsPath = _StyleProvider.GetTabBorder(tabRect))
                {
                    Rectangle rectangle2 = Rectangle.Empty;
                    if (_StyleProvider.ShowTabCloser)
                    {
                        rectangle2 = GetTabCloserButtonRect(rectangle, graphicsPath);
                    }
                    Image tabImage = null;
                    Rectangle rectangle3 = Rectangle.Empty;
                    if (TabHasImage(index))
                    {
                        tabImage = GetTabImage(index);
                        rectangle3 = GetTabImageRect(rectangle, graphicsPath);
                    }
                    Rectangle tabTextRect = GetTabTextRect(graphicsPath, rectangle, rectangle2, rectangle3);
                    using (Brush brush = _StyleProvider.GetPageBackgroundBrush(tabState))
                    {
                        graphics.FillPath(brush, path);
                    }
                    if (flag)
                    {
                        PaintTab(graphicsPath, rectangle2, tabState, graphics, mousePosition);
                        if (rectangle3 != Rectangle.Empty)
                        {
                            DrawTabImage(tabImage, rectangle3, graphics, enabled);
                        }
                        DrawTabText(base.TabPages[index].Text, tabState, graphics, tabTextRect);
                    }
                }
                DrawTabPageBorder(path, tabState, graphics);
            }
        }

        private void PaintTab(GraphicsPath tabBorder, Rectangle tabCloserButtonRect, TabState state, Graphics graphics, Point mousePosition)
        {
            _StyleProvider.PaintTabBackground(tabBorder, state, graphics);
            _StyleProvider.DrawTabFocusIndicator(tabBorder, state, graphics);
            _StyleProvider.DrawTabCloser(tabCloserButtonRect, graphics, state, mousePosition);
        }

        private void DrawTabPageBorder(GraphicsPath path, TabState state, Graphics graphics)
        {
            graphics.SmoothingMode = SmoothingMode.AntiAlias;
            Color color = Color.Empty;
            switch (state)
            {
                case TabState.Disabled:
                    color = _StyleProvider.BorderColorDisabled;
                    break;
                case TabState.Focused:
                    color = _StyleProvider.BorderColorFocused;
                    break;
                case TabState.Highlighted:
                    color = _StyleProvider.BorderColorHighlighted;
                    break;
                case TabState.Selected:
                    color = _StyleProvider.BorderColorSelected;
                    break;
                case TabState.None:
                    color = _StyleProvider.BorderColorUnselected;
                    break;
            }
            if (color != Color.Empty)
            {
                using (Pen pen = new Pen(color))
                    graphics.DrawPath(pen, path);
            }
        }

        private void DrawTabText(string text, TabState state, Graphics graphics, Rectangle textBounds)
        {
            graphics.SmoothingMode = SmoothingMode.AntiAlias;
            graphics.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
            Color color = Color.Empty;
            switch (state)
            {
                case TabState.Disabled:
                    color = _StyleProvider.TextColorDisabled;
                    break;
                case TabState.Focused:
                    color = _StyleProvider.TextColorFocused;
                    break;
                case TabState.Highlighted:
                    color = _StyleProvider.TextColorHighlighted;
                    break;
                case TabState.Selected:
                    color = _StyleProvider.TextColorSelected;
                    break;
                case TabState.None:
                    color = _StyleProvider.TextColorUnselected;
                    break;
            }
            using (Brush brush = new SolidBrush(color))
            using (StringFormat format = GetStringFormat())
            {
                if (EffectiveRightToLeft)
                {
                    using (Matrix transform = graphics.Transform)
                    using (Matrix matrix = new Matrix())
                    {
                        matrix.Translate(base.Width - textBounds.Right - textBounds.Left, 0f);
                        graphics.Transform = matrix;
                        graphics.DrawString(text, Font, brush, textBounds, format);
                        graphics.Transform = transform;
                    }
                }
                else
                {
                    graphics.DrawString(text, Font, brush, textBounds, format);
                }
            }
        }

        private void DrawTabImage(Image tabImage, Rectangle imageRect, Graphics graphics, bool isTabEnabled)
        {
            if (tabImage != null)
            {
                if (EffectiveRightToLeft)
                {
                    tabImage.RotateFlip(RotateFlipType.RotateNoneFlipX);
                }
                if (isTabEnabled)
                {
                    graphics.DrawImage(tabImage, imageRect);
                }
                else
                {
                    ControlPaint.DrawImageDisabled(graphics, tabImage, imageRect.X, imageRect.Y, Color.Transparent);
                }
            }
        }

        private StringFormat GetStringFormat()
        {
            StringFormat stringFormat = null;
            switch (Alignment)
            {
                case TabAlignment.Top:
                case TabAlignment.Bottom:
                    stringFormat = new StringFormat(StringFormatFlags.NoWrap);
                    break;
                case TabAlignment.Left:
                case TabAlignment.Right:
                    stringFormat = new StringFormat(StringFormatFlags.DirectionVertical | StringFormatFlags.NoWrap);
                    break;
            }
            stringFormat.Alignment = StringAlignment.Center;
            stringFormat.LineAlignment = StringAlignment.Center;
            if (FindForm() != null && FindForm().KeyPreview)
            {
                stringFormat.HotkeyPrefix = HotkeyPrefix.Show;
            }
            else
            {
                stringFormat.HotkeyPrefix = HotkeyPrefix.Hide;
            }
            if (RightToLeft == RightToLeft.Yes)
            {
                stringFormat.FormatFlags |= StringFormatFlags.DirectionRightToLeft;
            }
            return stringFormat;
        }

        private void AdjustPoint(ref Point point, bool adjustHorizontally, int increment)
        {
            if (adjustHorizontally)
            {
                point.X += increment;
            }
            else
            {
                point.Y += increment;
            }
        }

        private Point AdjustPointForRightToLeft(Point point)
        {
            Point result = new Point(point.X, point.Y);
            switch (Alignment)
            {
                case TabAlignment.Top:
                case TabAlignment.Bottom:
                    if (EffectiveRightToLeft)
                    {
                        result.X = base.Width - result.X;
                    }
                    break;
                case TabAlignment.Left:
                case TabAlignment.Right:
                    if (EffectiveRightToLeft)
                    {
                        result.Y = base.Height - result.Y;
                    }
                    break;
            }
            return result;
        }

        private void BackupTabPages()
        {
            if (_TabPages != null)
            {
                return;
            }
            _TabPages = new List<TabPage>();
            foreach (TabPage tabPage in base.TabPages)
            {
                _TabPages.Add(tabPage);
            }
        }

        private Rectangle AdjustRectangleDimensionsToFitInPath(Rectangle rect, GraphicsPath path)
        {
            switch (Alignment)
            {
                case TabAlignment.Bottom:
                    {
                        int offsetToEnsurePointIsWithinPath = GetOffsetToEnsurePointIsWithinPath(path, rect.Right, rect.Bottom, adjustHorizontally: true, -1, (Point p) => p.X > rect.X);
                        rect.Width += offsetToEnsurePointIsWithinPath;
                        offsetToEnsurePointIsWithinPath = GetOffsetToEnsurePointIsWithinPath(path, rect.X, rect.Bottom, adjustHorizontally: true, 1, (Point p) => p.X < rect.Right);
                        rect.X += offsetToEnsurePointIsWithinPath;
                        rect.Width -= offsetToEnsurePointIsWithinPath;
                        break;
                    }
                case TabAlignment.Top:
                    {
                        int offsetToEnsurePointIsWithinPath = GetOffsetToEnsurePointIsWithinPath(path, rect.Right, rect.Y, adjustHorizontally: true, -1, (Point p) => p.X > rect.X);
                        rect.Width += offsetToEnsurePointIsWithinPath;
                        offsetToEnsurePointIsWithinPath = GetOffsetToEnsurePointIsWithinPath(path, rect.X, rect.Y, adjustHorizontally: true, 1, (Point p) => p.X < rect.Right);
                        rect.X += offsetToEnsurePointIsWithinPath;
                        rect.Width -= offsetToEnsurePointIsWithinPath;
                        break;
                    }
                case TabAlignment.Left:
                    {
                        int offsetToEnsurePointIsWithinPath = GetOffsetToEnsurePointIsWithinPath(path, rect.X, rect.Bottom, adjustHorizontally: false, -1, (Point p) => p.Y > rect.Y);
                        rect.Height += offsetToEnsurePointIsWithinPath;
                        offsetToEnsurePointIsWithinPath = GetOffsetToEnsurePointIsWithinPath(path, rect.X, rect.Top, adjustHorizontally: false, 1, (Point p) => p.Y < rect.Bottom);
                        rect.Y += offsetToEnsurePointIsWithinPath;
                        rect.Height -= offsetToEnsurePointIsWithinPath;
                        break;
                    }
                case TabAlignment.Right:
                    {
                        int offsetToEnsurePointIsWithinPath = GetOffsetToEnsurePointIsWithinPath(path, rect.Right, rect.Bottom, adjustHorizontally: false, -1, (Point p) => p.Y > rect.Y);
                        rect.Height += offsetToEnsurePointIsWithinPath;
                        offsetToEnsurePointIsWithinPath = GetOffsetToEnsurePointIsWithinPath(path, rect.Right, rect.Top, adjustHorizontally: false, 1, (Point p) => p.Y < rect.Bottom);
                        rect.Y += offsetToEnsurePointIsWithinPath;
                        rect.Height -= offsetToEnsurePointIsWithinPath;
                        break;
                    }
            }
            return rect;
        }

        private void AddPageBorder(GraphicsPath path, Rectangle pageBounds, Rectangle tabBounds)
        {
            int tabPageRadius = _StyleProvider.TabPageRadius;
            if (!IsTabVisible(tabBounds, pageBounds))
            {
                AddRoundedRectangle(path, pageBounds, tabPageRadius);
                return;
            }
            int num = Math.Min(2 * tabPageRadius, pageBounds.Width);
            int num2 = num / 2;
            int num3 = Math.Min(2 * tabPageRadius, pageBounds.Height);
            int num4 = num3 / 2;
            switch (Alignment)
            {
                case TabAlignment.Top:
                    if (tabBounds.Right <= pageBounds.Right || tabBounds.Left >= pageBounds.Right)
                    {
                        if (tabBounds.Right > pageBounds.Right - num2)
                        {
                            path.AddLine(tabBounds.Right, pageBounds.Top, pageBounds.Right, pageBounds.Top + num4);
                        }
                        else
                        {
                            path.AddLine(tabBounds.Right, pageBounds.Top, pageBounds.Right - num2, pageBounds.Top);
                            if (tabPageRadius != 0)
                            {
                                path.AddArc(pageBounds.Right - num, pageBounds.Top, num, num3, 270f, 90f);
                            }
                        }
                    }
                    path.AddLine(pageBounds.Right, pageBounds.Top + num4, pageBounds.Right, pageBounds.Bottom - num4);
                    if (tabPageRadius != 0)
                    {
                        path.AddArc(pageBounds.Right - num, pageBounds.Bottom - num3, num, num3, 0f, 90f);
                    }
                    path.AddLine(pageBounds.Right - num2, pageBounds.Bottom, pageBounds.Left + num2, pageBounds.Bottom);
                    if (tabPageRadius != 0)
                    {
                        path.AddArc(pageBounds.Left, pageBounds.Bottom - num3, num, num3, 90f, 90f);
                    }
                    path.AddLine(pageBounds.Left, pageBounds.Bottom - num4, pageBounds.Left, pageBounds.Top + num4);
                    if (tabBounds.Left < pageBounds.Left && tabBounds.Right > pageBounds.Left)
                    {
                        break;
                    }
                    if (tabBounds.Left < pageBounds.Left + num2)
                    {
                        path.AddLine(pageBounds.Left, pageBounds.Top + num4, tabBounds.Left, pageBounds.Top);
                        break;
                    }
                    if (tabPageRadius != 0)
                    {
                        path.AddArc(pageBounds.Left, pageBounds.Top, num, num3, 180f, 90f);
                    }
                    path.AddLine(pageBounds.Left + num2, pageBounds.Top, tabBounds.Left, pageBounds.Top);
                    break;
                case TabAlignment.Bottom:
                    if (tabBounds.Left >= pageBounds.Left || tabBounds.Right <= pageBounds.Left)
                    {
                        if (tabBounds.Left < pageBounds.Left + num2)
                        {
                            path.AddLine(tabBounds.Left, pageBounds.Bottom, pageBounds.Left, pageBounds.Bottom - num4);
                        }
                        else
                        {
                            path.AddLine(tabBounds.Left, pageBounds.Bottom, pageBounds.Left + num2, pageBounds.Bottom);
                            if (tabPageRadius != 0)
                            {
                                path.AddArc(pageBounds.Left, pageBounds.Bottom - num3, num, num3, 90f, 90f);
                            }
                        }
                    }
                    path.AddLine(pageBounds.Left, pageBounds.Bottom - num4, pageBounds.Left, pageBounds.Top + num4);
                    if (tabPageRadius != 0)
                    {
                        path.AddArc(pageBounds.Left, pageBounds.Top, num, num3, 180f, 90f);
                    }
                    path.AddLine(pageBounds.Left + num2, pageBounds.Top, pageBounds.Right - num2, pageBounds.Top);
                    if (tabPageRadius != 0)
                    {
                        path.AddArc(pageBounds.Right - num, pageBounds.Top, num, num3, 270f, 90f);
                    }
                    path.AddLine(pageBounds.Right, pageBounds.Top + num4, pageBounds.Right, pageBounds.Bottom - num4);
                    if (tabBounds.Right > pageBounds.Right && tabBounds.Left < pageBounds.Right)
                    {
                        break;
                    }
                    if (tabBounds.Right > pageBounds.Right - num2)
                    {
                        path.AddLine(pageBounds.Right, pageBounds.Bottom - num4, tabBounds.Right, pageBounds.Bottom);
                        break;
                    }
                    if (tabPageRadius != 0)
                    {
                        path.AddArc(pageBounds.Right - num, pageBounds.Bottom - num3, num, num3, 0f, 90f);
                    }
                    path.AddLine(pageBounds.Right - num2, pageBounds.Bottom, tabBounds.Right, pageBounds.Bottom);
                    break;
                case TabAlignment.Left:
                    path.AddLine(pageBounds.Left, tabBounds.Top, pageBounds.Left, pageBounds.Top);
                    path.AddLine(pageBounds.Left, pageBounds.Top, pageBounds.Right, pageBounds.Top);
                    path.AddLine(pageBounds.Right, pageBounds.Top, pageBounds.Right, pageBounds.Bottom);
                    path.AddLine(pageBounds.Right, pageBounds.Bottom, pageBounds.Left, pageBounds.Bottom);
                    path.AddLine(pageBounds.Left, pageBounds.Bottom, pageBounds.Left, tabBounds.Bottom);
                    break;
                case TabAlignment.Right:
                    path.AddLine(pageBounds.Right, tabBounds.Bottom, pageBounds.Right, pageBounds.Bottom);
                    path.AddLine(pageBounds.Right, pageBounds.Bottom, pageBounds.Left, pageBounds.Bottom);
                    path.AddLine(pageBounds.Left, pageBounds.Bottom, pageBounds.Left, pageBounds.Top);
                    path.AddLine(pageBounds.Left, pageBounds.Top, pageBounds.Right, pageBounds.Top);
                    path.AddLine(pageBounds.Right, pageBounds.Top, pageBounds.Right, tabBounds.Top);
                    break;
            }
        }

        private void AddRoundedRectangle(GraphicsPath path, Rectangle pageBounds, int radius)
        {
            if (radius == 0)
            {
                path.AddRectangle(pageBounds);
                return;
            }
            Size size = new Size(Math.Min(2 * radius, pageBounds.Width), Math.Min(2 * radius, pageBounds.Height));
            path.AddArc(pageBounds.Left, pageBounds.Top, size.Width, size.Height, 180f, 90f);
            path.AddArc(pageBounds.Right - size.Width, pageBounds.Top, size.Width, size.Height, 270f, 90f);
            path.AddArc(pageBounds.Right - size.Width, pageBounds.Bottom - size.Height, size.Width, size.Height, 0f, 90f);
            path.AddArc(pageBounds.Left, pageBounds.Bottom - size.Height, size.Width, size.Height, 90f, 90f);
        }

        private Rectangle EnsureRectIsInPath(GraphicsPath tabBorder, Rectangle rect, bool increaseCoordinate)
        {
            Rectangle result = rect;
            switch (Alignment)
            {
                case TabAlignment.Top:
                    if (increaseCoordinate)
                    {
                        result.X += 4 + GetOffsetToEnsurePointIsWithinPath(tabBorder, result.X, result.Y, adjustHorizontally: true, 1, (Point p) => p.X < base.Width);
                    }
                    else
                    {
                        result.X += -4 + GetOffsetToEnsurePointIsWithinPath(tabBorder, result.Right, result.Y, adjustHorizontally: true, -1, (Point p) => p.X > 0);
                    }
                    break;
                case TabAlignment.Bottom:
                    if (increaseCoordinate)
                    {
                        result.X += 4 + GetOffsetToEnsurePointIsWithinPath(tabBorder, result.X, result.Bottom, adjustHorizontally: true, 1, (Point p) => p.X < base.Width);
                    }
                    else
                    {
                        result.X += -4 + GetOffsetToEnsurePointIsWithinPath(tabBorder, result.Right, result.Bottom, adjustHorizontally: true, -1, (Point p) => p.X > 0);
                    }
                    break;
                case TabAlignment.Left:
                    if (increaseCoordinate)
                    {
                        result.Y += 4 + GetOffsetToEnsurePointIsWithinPath(tabBorder, result.Left, result.Y, adjustHorizontally: false, 1, (Point p) => p.Y < base.Height);
                    }
                    else
                    {
                        result.Y += -4 + GetOffsetToEnsurePointIsWithinPath(tabBorder, result.Left, result.Bottom, adjustHorizontally: false, -1, (Point p) => p.Y > 0);
                    }
                    break;
                case TabAlignment.Right:
                    if (increaseCoordinate)
                    {
                        result.Y += 4 + GetOffsetToEnsurePointIsWithinPath(tabBorder, result.Right, result.Y, adjustHorizontally: false, 1, (Point p) => p.Y < base.Height);
                    }
                    else
                    {
                        result.Y += -4 + GetOffsetToEnsurePointIsWithinPath(tabBorder, result.Right, result.Bottom, adjustHorizontally: false, -1, (Point p) => p.Y > 0);
                    }
                    break;
            }
            return result;
        }

        private Rectangle GetBaseTabRect(int index)
        {
            Rectangle tabRect = GetTabRect(index);
            switch (Alignment)
            {
                case TabAlignment.Top:
                case TabAlignment.Bottom:
                    if (EffectiveRightToLeft)
                    {
                        tabRect.X = base.Width - tabRect.Right;
                    }
                    break;
                case TabAlignment.Left:
                case TabAlignment.Right:
                    if (EffectiveRightToLeft)
                    {
                        tabRect.Y = base.Height - tabRect.Bottom;
                    }
                    break;
            }
            return tabRect;
        }

        private int GetOffsetToEnsurePointIsWithinPath(GraphicsPath path, int X, int Y, bool adjustHorizontally, int increment, Func<Point, bool> constraint)
        {
            Point point = new Point(X, Y);
            while (!path.IsVisible(point) && constraint(point))
            {
                AdjustPoint(ref point, adjustHorizontally, increment);
            }
            if (!adjustHorizontally)
            {
                return point.Y - Y;
            }
            return point.X - X;
        }

        public Rectangle GetPageBounds(int index)
        {
            if (index < 0)
            {
                return default(Rectangle);
            }
            Rectangle bounds = base.TabPages[index].Bounds;
            bounds.Width += _StyleProvider.TabPageMargin.Left + _StyleProvider.TabPageMargin.Right - 1;
            bounds.Height += _StyleProvider.TabPageMargin.Top + _StyleProvider.TabPageMargin.Bottom - 1;
            bounds.X -= _StyleProvider.TabPageMargin.Left;
            bounds.Y -= _StyleProvider.TabPageMargin.Top;
            return bounds;
        }

        public Rectangle GetTabBounds(int index)
        {
            return _StyleProvider.GetTabRect(GetTabRect(index), GetPageBounds(index), index == base.SelectedIndex);
        }

        private GraphicsPath GetTabCloserButtonPathAtPosition(Point position)
        {
            if (base.DesignMode || !_StyleProvider.ShowTabCloser)
            {
                return null;
            }
            for (int i = 0; i < base.TabCount; i++)
            {
                Rectangle tabCloserButtonRect = GetTabCloserButtonRect(i);
                GraphicsPath tabCloserButtonPath = _StyleProvider.GetTabCloserButtonPath(tabCloserButtonRect);
                if (tabCloserButtonPath.IsVisible(position))
                {
                    return tabCloserButtonPath;
                }
            }
            return null;
        }

        public Rectangle GetTabCloserButtonRect(int index)
        {
            Rectangle tabRect = GetTabRect(index);
            Rectangle pageBounds = GetPageBounds(index);
            Rectangle tabRect2 = _StyleProvider.GetTabRect(tabRect, pageBounds, base.SelectedIndex == index);
            Rectangle tabContentRect = Rectangle.Intersect(tabRect, tabRect2);
            return GetTabCloserButtonRect(tabContentRect, _StyleProvider.GetTabBorder(tabRect2));
        }

        private Rectangle GetTabCloserButtonRect(Rectangle tabContentRect, GraphicsPath tabBorder)
        {
            Rectangle rect = default(Rectangle);
            bool increaseCoordinate = false;
            switch (Alignment)
            {
                case TabAlignment.Top:
                case TabAlignment.Bottom:
                    if (EffectiveRightToLeft)
                    {
                        rect = RectangleUtils.GetRectangleWithinRectangle(tabContentRect, 15, ContentAlignment.MiddleLeft);
                        increaseCoordinate = true;
                    }
                    else
                    {
                        rect = RectangleUtils.GetRectangleWithinRectangle(tabContentRect, 15, ContentAlignment.MiddleRight);
                        increaseCoordinate = false;
                    }
                    break;
                case TabAlignment.Left:
                case TabAlignment.Right:
                    if (EffectiveRightToLeft)
                    {
                        rect = RectangleUtils.GetRectangleWithinRectangle(tabContentRect, 15, ContentAlignment.TopCenter);
                        increaseCoordinate = true;
                    }
                    else
                    {
                        rect = RectangleUtils.GetRectangleWithinRectangle(tabContentRect, 15, ContentAlignment.BottomCenter);
                        increaseCoordinate = false;
                    }
                    break;
            }
            return EnsureRectIsInPath(tabBorder, rect, increaseCoordinate);
        }

        private Image GetTabImage(int index)
        {
            Image result = null;
            if (base.ImageList != null)
            {
                if (base.TabPages[index].ImageIndex > -1 && base.ImageList.Images.Count > base.TabPages[index].ImageIndex)
                {
                    result = base.ImageList.Images[base.TabPages[index].ImageIndex];
                }
                else if (!string.IsNullOrEmpty(base.TabPages[index].ImageKey) && !base.TabPages[index].ImageKey.Equals("(none)", StringComparison.OrdinalIgnoreCase) && base.ImageList.Images.ContainsKey(base.TabPages[index].ImageKey))
                {
                    result = base.ImageList.Images[base.TabPages[index].ImageKey];
                }
            }
            return result;
        }

        private Rectangle GetTabImageRect(int index)
        {
            Rectangle tabRect = _StyleProvider.GetTabRect(GetTabRect(index), GetPageBounds(index), index == base.SelectedIndex);
            using (GraphicsPath tabBorderPath = _StyleProvider.GetTabBorder(tabRect))
                return GetTabImageRect(tabRect, tabBorderPath);
        }

        private Rectangle GetTabImageRect(Rectangle tabRect, GraphicsPath tabBorderPath)
        {
            Rectangle rectangle = default(Rectangle);
            Size imageSize = base.ImageList.ImageSize;
            rectangle = RectangleUtils.GetRectangleWithinRectangle(tabRect, imageSize, _StyleProvider.ImageAlign);
            ContentAlignment imageAlign = _StyleProvider.ImageAlign;
            bool flag = Alignment == TabAlignment.Top || Alignment == TabAlignment.Bottom;
            bool num = (flag && (IsLeftAligned(imageAlign) || IsRightAligned(imageAlign))) || (!flag && (IsBottomAligned(imageAlign) || IsTopAligned(imageAlign)));
            bool increaseCoordinate = (flag && IsLeftAligned(imageAlign)) || (!flag && IsTopAligned(imageAlign));
            if (num)
            {
                rectangle = EnsureRectIsInPath(tabBorderPath, rectangle, increaseCoordinate);
            }
            if (_StyleProvider.ShowTabCloser)
            {
                if (EffectiveRightToLeft)
                {
                    if (flag && IsLeftAligned(imageAlign))
                    {
                        rectangle.X += 19;
                    }
                    if (!flag && IsTopAligned(imageAlign))
                    {
                        rectangle.Y += 19;
                    }
                }
                else
                {
                    if (flag && IsRightAligned(imageAlign))
                    {
                        rectangle.X -= 19;
                    }
                    if (!flag && IsBottomAligned(imageAlign))
                    {
                        rectangle.Y -= 19;
                    }
                }
            }
            return rectangle;
        }

        private GraphicsPath GetTabPageBorder(Rectangle pageBounds, Rectangle tabBounds)
        {
            GraphicsPath graphicsPath = new GraphicsPath();
            if (IsTabVisible(tabBounds, pageBounds))
            {
                _StyleProvider.AddTabBorder(graphicsPath, tabBounds);
            }
            AddPageBorder(graphicsPath, pageBounds, tabBounds);
            graphicsPath.CloseFigure();
            return graphicsPath;
        }

        public Point GetTabPosition(int index)
        {
            if (!Multiline)
            {
                return new Point(0, index);
            }
            if (base.RowCount == 1)
            {
                return new Point(0, index);
            }
            int tabRow = GetTabRow(index);
            Rectangle tabRect = GetTabRect(index);
            int num = -1;
            for (int i = 0; i < base.TabCount; i++)
            {
                Rectangle tabRect2 = GetTabRect(i);
                if (Alignment <= TabAlignment.Bottom)
                {
                    if (tabRect2.Y == tabRect.Y)
                    {
                        num++;
                    }
                }
                else if (tabRect2.X == tabRect.X)
                {
                    num++;
                }
                if (tabRect2.Location.Equals(tabRect.Location))
                {
                    return new Point(tabRow, num);
                }
            }
            return new Point(0, 0);
        }

        public int GetTabRow(int index)
        {
            Rectangle tabRect = GetTabRect(index);
            int result = -1;
            switch (Alignment)
            {
                case TabAlignment.Top:
                    result = (tabRect.Y - 2) / tabRect.Height;
                    break;
                case TabAlignment.Bottom:
                    result = (base.Height - tabRect.Y - 2) / tabRect.Height - 1;
                    break;
                case TabAlignment.Left:
                    result = (tabRect.X - 2) / tabRect.Width;
                    break;
                case TabAlignment.Right:
                    result = (base.Width - tabRect.X - 2) / tabRect.Width - 1;
                    break;
            }
            return result;
        }

        private TabState GetTabState(int index, Point mousePosition)
        {
            if (base.SelectedIndex == index)
            {
                if (base.ContainsFocus)
                {
                    return TabState.Focused;
                }
                return TabState.Selected;
            }
            if (!base.TabPages[index].Enabled)
            {
                return TabState.Disabled;
            }
            if (DisplayStyleProvider.HotTrack && index == GetActiveIndex(mousePosition))
            {
                return TabState.Highlighted;
            }
            return TabState.None;
        }

        private Rectangle GetTabTextRect(GraphicsPath tabBorder, Rectangle tabBounds, Rectangle closerRect, Rectangle imageRect)
        {
            int num = tabBounds.X + 1;
            int num2 = tabBounds.Right - 1;
            int num3 = tabBounds.Y + 1;
            int num4 = tabBounds.Bottom - 1;
            ContentAlignment imageAlign = _StyleProvider.ImageAlign;
            switch (Alignment)
            {
                case TabAlignment.Top:
                case TabAlignment.Bottom:
                    if (closerRect != Rectangle.Empty)
                    {
                        if (EffectiveRightToLeft)
                        {
                            num = closerRect.Right + 4;
                        }
                        else
                        {
                            num2 = closerRect.X - 4;
                        }
                    }
                    if (imageRect != Rectangle.Empty)
                    {
                        if (IsLeftAligned(imageAlign))
                        {
                            num = imageRect.Right + 4;
                        }
                        else if (IsRightAligned(imageAlign))
                        {
                            num2 = imageRect.X - 4;
                        }
                    }
                    break;
                case TabAlignment.Left:
                case TabAlignment.Right:
                    if (closerRect != Rectangle.Empty)
                    {
                        if (EffectiveRightToLeft)
                        {
                            num3 = closerRect.Bottom + 4;
                        }
                        else
                        {
                            num4 = closerRect.Y - 4;
                        }
                    }
                    if (imageRect != Rectangle.Empty)
                    {
                        if (IsTopAligned(imageAlign))
                        {
                            num3 = imageRect.Bottom + 4;
                        }
                        else if (IsBottomAligned(imageAlign))
                        {
                            num4 = imageRect.Y - 4;
                        }
                    }
                    break;
            }
            Rectangle rect = new Rectangle(num, num3, num2 - num, num4 - num3);
            return AdjustRectangleDimensionsToFitInPath(rect, tabBorder);
        }

        protected internal bool IsTabVisible(Rectangle tabBounds, Rectangle pageBounds)
        {
            switch (Alignment)
            {
                case TabAlignment.Top:
                case TabAlignment.Bottom:
                    if (tabBounds.Right > pageBounds.Left + _StyleProvider.TabPageMargin.Left)
                    {
                        return tabBounds.Left < pageBounds.Right - _StyleProvider.TabPageMargin.Right;
                    }
                    return false;
                case TabAlignment.Left:
                case TabAlignment.Right:
                    if (tabBounds.Bottom > pageBounds.Top + _StyleProvider.TabPageMargin.Top)
                    {
                        return tabBounds.Top < pageBounds.Bottom - _StyleProvider.TabPageMargin.Bottom;
                    }
                    return false;
                default:
                    return false;
            }
        }

        private bool IsValidTabIndex(int index)
        {
            BackupTabPages();
            if (index >= 0)
            {
                return index < _TabPages.Count;
            }
            return false;
        }

        private bool TabHasImage(int index)
        {
            if (base.ImageList != null)
            {
                if (base.TabPages[index].ImageIndex <= -1)
                {
                    if (!string.IsNullOrEmpty(base.TabPages[index].ImageKey))
                    {
                        return !base.TabPages[index].ImageKey.Equals("(none)", StringComparison.OrdinalIgnoreCase);
                    }
                    return false;
                }
                return true;
            }
            return false;
        }

        public static bool IsLeftAligned(ContentAlignment alignment)
        {
            return (alignment & (ContentAlignment)273) != 0;
        }

        public static bool IsRightAligned(ContentAlignment alignment)
        {
            return (alignment & (ContentAlignment)1092) != 0;
        }

        public static bool IsTopAligned(ContentAlignment alignment)
        {
            return (alignment & (ContentAlignment)7) != 0;
        }

        public static bool IsBottomAligned(ContentAlignment alignment)
        {
            return (alignment & (ContentAlignment)1792) != 0;
        }

        public static bool IsMiddleAligned(ContentAlignment alignment)
        {
            return (alignment & (ContentAlignment)112) != 0;
        }

        public static bool IsCenterAligned(ContentAlignment alignment)
        {
            return (alignment & (ContentAlignment)546) != 0;
        }

        public IntPtr SendMessage(int msg, IntPtr wParam, IntPtr lParam)
        {
            Message message = default(Message);
            message.HWnd = base.Handle;
            message.LParam = lParam;
            message.WParam = wParam;
            message.Msg = msg;
            Message m = message;
            WndProc(ref m);
            return m.Result;
        }
    }
    public enum TabState
    {
        None = 0,
        Unselected = 0,
        Disabled = 1,
        Highlighted = 2,
        Selected = 3,
        Focused = 4
    }
    public enum TabStyle
    {
        None,
        Default,
        VisualStudio,
        Rounded,
        Angled,
        Chrome,
        IE8,
        VS2010,
        Rectangular,
        VS2012
    }
    internal static class RectangleUtils
    {
        internal static Rectangle GetRectangleWithinRectangle(RectangleF rect, Size size, ContentAlignment alignment)
        {
            return GetRectangleWithinRectangle(rect, size.Width, size.Height, alignment);
        }

        internal static Rectangle GetRectangleWithinRectangle(RectangleF rect, int size, ContentAlignment alignment)
        {
            return GetRectangleWithinRectangle(rect, size, size, alignment);
        }

        internal static Rectangle GetRectangleWithinRectangle(RectangleF rect, int width, int height, ContentAlignment alignment)
        {
            RectangleF value = RectangleF.Empty;
            switch (alignment)
            {
                case ContentAlignment.BottomCenter:
                    value = new RectangleF(rect.X + GetOffset(rect.X, rect.Right, width), rect.Bottom - (float)height, width, height);
                    break;
                case ContentAlignment.BottomLeft:
                    value = new RectangleF(rect.X, rect.Bottom - (float)height, width, height);
                    break;
                case ContentAlignment.BottomRight:
                    value = new RectangleF(rect.Right - (float)width, rect.Bottom - (float)height, width, height);
                    break;
                case ContentAlignment.MiddleCenter:
                    value = new RectangleF(rect.X + GetOffset(rect.X, rect.Right, width), rect.Y + GetOffset(rect.Y, rect.Bottom, height), width, height);
                    break;
                case ContentAlignment.MiddleLeft:
                    value = new RectangleF(rect.X, rect.Y + GetOffset(rect.Y, rect.Bottom, height), width, height);
                    break;
                case ContentAlignment.MiddleRight:
                    value = new RectangleF(rect.Right - (float)width, rect.Y + GetOffset(rect.Y, rect.Bottom, height), width, height);
                    break;
                case ContentAlignment.TopCenter:
                    value = new RectangleF(rect.X + GetOffset(rect.X, rect.Right, width), rect.Y, width, height);
                    break;
                case ContentAlignment.TopLeft:
                    value = new RectangleF(rect.X, rect.Y, width, height);
                    break;
                case ContentAlignment.TopRight:
                    value = new RectangleF(rect.Right - (float)width, rect.Y, width, height);
                    break;
            }
            return Rectangle.Round(value);
        }

        private static float GetOffset(float start, float end, int length)
        {
            return (end - start - (float)length) / 2f;
        }
    }
    internal sealed class ThemedColors
    {
        public enum ColorScheme
        {
            NormalColor,
            HomeStead,
            Metallic,
            NoTheme
        }

        private const string NormalColor = "NormalColor";

        private const string HomeStead = "HomeStead";

        private const string Metallic = "Metallic";

        private const string NoTheme = "NoTheme";

        private static Color[] _toolBorder;

        public static ColorScheme CurrentThemeIndex => GetCurrentThemeIndex();

        public static Color ToolBorder => _toolBorder[(int)CurrentThemeIndex];

        static ThemedColors()
        {
            _toolBorder = new Color[4]
            {
            Color.FromArgb(127, 157, 185),
            Color.FromArgb(164, 185, 127),
            Color.FromArgb(165, 172, 178),
            Color.FromArgb(132, 130, 132)
            };
        }

        private ThemedColors()
        {
        }

        private static ColorScheme GetCurrentThemeIndex()
        {
            ColorScheme result = ColorScheme.NoTheme;
            if (System.Windows.Forms.VisualStyles.VisualStyleInformation.IsSupportedByOS && System.Windows.Forms.VisualStyles.VisualStyleInformation.IsEnabledByUser && Application.RenderWithVisualStyles)
            {
                switch (System.Windows.Forms.VisualStyles.VisualStyleInformation.ColorScheme)
                {
                    case "NormalColor":
                        result = ColorScheme.NormalColor;
                        break;
                    case "HomeStead":
                        result = ColorScheme.HomeStead;
                        break;
                    case "Metallic":
                        result = ColorScheme.Metallic;
                        break;
                    default:
                        result = ColorScheme.NoTheme;
                        break;
                }
            }
            return result;
        }
    }
}
