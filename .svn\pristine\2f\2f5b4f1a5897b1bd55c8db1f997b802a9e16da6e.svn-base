﻿using OCRTools;
using System;
using System.Windows.Forms;

namespace ShareX.HelpersLib
{
    public partial class LabeledComboBox : UserControl
    {
        public new string Text
        {
            get
            {
                return lblText.Text;
            }
            set
            {
                lblText.Text = value;
            }
        }

        public int SelectedIndex
        {
            get
            {
                return cbList.SelectedIndex;
            }
            set
            {
                cbList.SelectedIndex = value;
            }
        }

        public event EventHandler SelectedIndexChanged
        {
            add { cbList.SelectedIndexChanged += value; }
            remove { cbList.SelectedIndexChanged -= value; }
        }

        public LabeledComboBox()
        {
            InitializeComponent();
            Margin = CommonString.PaddingZero;
        }

        public void Add(object item)
        {
            cbList.Items.Add(item);
        }

        public void AddRange(object[] items)
        {
            cbList.Items.AddRange(items);
            cbList.AutoSizeDropDown();
        }
    }
}