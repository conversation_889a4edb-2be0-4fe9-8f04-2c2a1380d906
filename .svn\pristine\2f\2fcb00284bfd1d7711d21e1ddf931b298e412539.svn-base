using System;

namespace QiHe.CodeLib
{
	public class RedBlackTree<TItem> : BinarySearchTreeBase<TItem, RedBlackTreeNode<TItem>> where TItem : IComparable<TItem>
	{
		public override RedBlackTreeNode<TItem> Nil => RedBlackTreeNode<TItem>.Nil;

		public override void Insert(RedBlackTreeNode<TItem> node)
		{
			base.Insert(node);
			node.Color = NodeColor.Red;
			FixupAfterInsert(node);
		}

		private void FixupAfterInsert(RedBlackTreeNode<TItem> node)
		{
			while (node.Parent.Color == NodeColor.Red)
			{
				if (node.Parent.IsLeftChild)
				{
					RedBlackTreeNode<TItem> right = node.Parent.Parent.Right;
					if (right.Color == NodeColor.Red)
					{
						right.Color = NodeColor.Black;
						node.Parent.Color = NodeColor.Black;
						node.Parent.Parent.Color = NodeColor.Red;
						node = node.Parent.Parent;
						continue;
					}
					if (node.IsRightChild)
					{
						node = node.Parent;
						RotateLeft(node);
					}
					node.Parent.Color = NodeColor.Black;
					node.Parent.Parent.Color = NodeColor.Red;
					RotateRight(node.Parent.Parent);
					continue;
				}
				RedBlackTreeNode<TItem> left = node.Parent.Parent.Left;
				if (left.Color == NodeColor.Red)
				{
					left.Color = NodeColor.Black;
					node.Parent.Color = NodeColor.Black;
					node.Parent.Parent.Color = NodeColor.Red;
					node = node.Parent.Parent;
					continue;
				}
				if (node.IsLeftChild)
				{
					node = node.Parent;
					RotateRight(node);
				}
				node.Parent.Color = NodeColor.Black;
				node.Parent.Parent.Color = NodeColor.Red;
				RotateLeft(node.Parent.Parent);
			}
			Root.Color = NodeColor.Black;
		}

		public override RedBlackTreeNode<TItem> Delete(RedBlackTreeNode<TItem> node)
		{
			RedBlackTreeNode<TItem> redBlackTreeNode = base.Delete(node);
			RedBlackTreeNode<TItem> node2 = Nil;
			RedBlackTreeNode<TItem> parent = redBlackTreeNode.Parent;
			if (parent != Nil)
			{
				node2 = (redBlackTreeNode.IsLeftChild ? parent.Left : parent.Right);
			}
			if (redBlackTreeNode.Color == NodeColor.Black)
			{
				FixupAfterDelete(node2);
			}
			return redBlackTreeNode;
		}

		private void FixupAfterDelete(RedBlackTreeNode<TItem> node)
		{
			while (node != Root && node.Color == NodeColor.Black)
			{
				if (node.IsLeftChild)
				{
					RedBlackTreeNode<TItem> right = node.Parent.Right;
					if (right.Color == NodeColor.Red)
					{
						right.Color = NodeColor.Black;
						node.Parent.Color = NodeColor.Red;
						RotateLeft(node.Parent);
						right = node.Parent.Right;
					}
					if (right.Left.Color == NodeColor.Black && right.Right.Color == NodeColor.Black)
					{
						right.Color = NodeColor.Red;
						node = node.Parent;
						continue;
					}
					if (right.Right.Color == NodeColor.Black)
					{
						right.Left.Color = NodeColor.Black;
						right.Color = NodeColor.Red;
						RotateRight(right);
						right = node.Parent.Right;
					}
					right.Color = node.Parent.Color;
					node.Parent.Color = NodeColor.Black;
					right.Right.Color = NodeColor.Black;
					RotateLeft(node.Parent);
					node = Root;
					continue;
				}
				RedBlackTreeNode<TItem> left = node.Parent.Left;
				if (left.Color == NodeColor.Red)
				{
					left.Color = NodeColor.Black;
					node.Parent.Color = NodeColor.Red;
					RotateRight(node.Parent);
					left = node.Parent.Left;
				}
				if (left.Left.Color == NodeColor.Black && left.Right.Color == NodeColor.Black)
				{
					left.Color = NodeColor.Red;
					node = node.Parent;
					continue;
				}
				if (left.Left.Color == NodeColor.Black)
				{
					left.Right.Color = NodeColor.Black;
					left.Color = NodeColor.Red;
					RotateLeft(left);
					left = node.Parent.Left;
				}
				left.Color = node.Parent.Color;
				node.Parent.Color = NodeColor.Black;
				left.Left.Color = NodeColor.Black;
				RotateRight(node.Parent);
				node = Root;
			}
			node.Color = NodeColor.Black;
		}
	}
}
