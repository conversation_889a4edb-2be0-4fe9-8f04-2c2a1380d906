﻿using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class UCPictureBox : PictureBox
    {
        public UCPictureBox()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.OptimizedDoubleBuffer | ControlStyles.SupportsTransparentBackColor, true);
            DoubleBuffered = true;
        }

        public Size OriSize { get; set; }

        public Point OrgLocation { get; set; }
    }
}
