﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace OcrMain
{
    public class LineProcess
    {
        /**
         * 识别结果列表(rect blocks list)
         */
        public List<LineInfo> wordsResult;

        /**
         * 所有文字，不含换行
         */
        public string textAll = string.Empty;

        /**
         * 行宽总和
         */
        public int sumWidth = 0;

        /**
         * 字数总和
         */
        public int sumWordsCount = 0;

        /**
         * 最大行宽
         */
        public int maxLineWidth = 0;

        /**
         * 最大行字数
         */
        public int maxLineWordsCount = 0;

        /**
         * 平均字宽
         */
        public int avgWordWidth = 0;

        /**
         * 总行数
         */
        public int sumLinesCount = 0;

        /**
         * 段落总数
         */
        public int paragraphCount => wordsResult.Count(p => p.isBreak);

        public LineProcess(List<LineInfo> lines, bool isAutoFull2Half, bool isAutoSpace, bool isAutoSymbol, bool isAutoDuplicateSymbol)
        {
            wordsResult = lines;
            if (wordsResult.Count <= 0)
            {
                return;
            }
            prepareProperties(isAutoFull2Half, isAutoSpace, isAutoSymbol, isAutoDuplicateSymbol);
            parseWordsResult();
        }

        private string GetSpiltStartStr()
        {
            return wordsResult?.Count > 1 ? "\t" : "";
        }

        public string GetMergeResult(bool isAppendTab = false)
        {
            var sb = new StringBuilder(isAppendTab ? GetSpiltStartStr() : "");
            foreach (var line in wordsResult)
            {
                if (string.IsNullOrEmpty(line.words))
                {
                    continue;
                }
                sb.Append(line.isBreak ? "\n" + (isAppendTab ? GetSpiltStartStr() : "") : line.separator);
            }
            return sb.ToString().TrimEnd();
        }

        /**
    * 对识别结果的预处理，获取一些属性信息
*/
        public void prepareProperties(bool isAutoFull2Half, bool isAutoSpace, bool isAutoSymbol, bool isAutoDuplicateSymbol)
        {
            foreach (var rect in wordsResult)
            {
                rect.lang = OcrResultUtil.GetLang(rect.words);
                rect.separator = lineSeparator(rect.lang);
                rect.words = CommonStyle.ReplacePunctuationAuto(rect.words, rect.lang, isAutoFull2Half, isAutoSpace, isAutoSymbol, isAutoDuplicateSymbol);
                textAll += rect.words;
                sumWidth += rect.rectangle.Width;
                sumWordsCount += rect.words.Length;
                maxLineWidth = Math.Max(maxLineWidth, rect.rectangle.Width);
                maxLineWordsCount = Math.Max(maxLineWordsCount, rect.words.Length);
            }

            avgWordWidth = sumWordsCount > 0 ? sumWidth / sumWordsCount : 0;
            sumLinesCount = wordsResult.Count;
        }

        /**
     * 解析段落换行
     * @param  {String} reason 原因
     * @return {Array} wordsResult
     */
        public void parseWordsResult()
        {
            // 根据标点符号数量比例判断是否需要换行
            if (ifHasFewSymbol(textAll))
            {
                breakAllLines("rect: hasFewSymbol");
                return;
            }

            // 每行字数过少（小于 12）
            if (ifLineWordsFewCount(this.maxLineWordsCount))
            {
                breakAllLines("rect: lineWordsFewCount");
                return;
            }

            // 符合列表特征，以列表形式开头
            if (ifStartWithNumAll())
            {
                breakAllLines("rect: startWithNumAll");
                return;
            }

            for (int i = 0; i < wordsResult.Count; i++)
            {
                var rect = wordsResult[i];
                var rectLast = i > 0 ? wordsResult[i - 1] : null;
                var rectLastLast = i > 1 ? wordsResult[i - 2] : null;
                //上一行是否已经判断为换行，首行默认为 true
                var isLastBreak = rectLast?.isBreak;
                //上上行是否已经判断为换行，首行、第一行默认为 true
                var isLastLastBreak = rectLastLast?.isBreak;

                // 有道接口返回的 region 列表，每个 region 默认是一个段落
                // 百度接口默认值为 undefined
                if (rect.isBreak)
                {
                    rect.breakReason.Add("rect: regionBreak");
                }

                // 本行以 数字/罗马数字 开头
                if (ifStartWithNum(rect.words) && rectLast != null)
                {
                    rectLast.isBreak = true;
                    rectLast.breakReason.Add("nextRect: startWithNum");
                }

                // 全部文字的首行是否需要换行
                if (ifFirstLineBreak(i, rect, maxLineWordsCount))
                {
                    rect.isBreak = true;
                    rect.breakReason.Add("rect: firstLineBreak");
                }

                /**
                 * 使用 段首缩进 和 断尾缩进 判断分段
                 * 排除：[全文第一行]全文第一行无对比对象
                 * 排除：[本行是段首的情况]当前段落的列宽、位置可能与前一段有较大差别，比如图文混排的情况，所以忽略这种情况
                 */
                var indent = indentSize(rect.lang) * this.avgWordWidth;
                if (rectLast != null && isLastBreak == false)
                {
                    /**
                     * 段末缩进特征：通过比较本行与上一行行末距离判断本行是否是段落最后一行
                     */
                    var endDist = Math.Sqrt(Math.Pow(rect.rectangle.Right - rectLast.rectangle.Right, 2) + Math.Pow(rect.rectangle.Top - rectLast.rectangle.Bottom, 2));
                    var isEndIndent = endDist >= indent;
                    var isEndShort = rect.rectangle.Right - rectLast.rectangle.Right < 0;
                    // 本行比上一行短，本行是段末
                    if (isEndIndent && isEndShort)
                    {
                        rect.isBreak = true;
                        rect.breakReason.Add("rect: endDist");
                    }

                    // 本行比上一行长，上一行是段末
                    if (isEndIndent && !isEndShort)
                    {
                        rectLast.isBreak = true;
                        rectLast.breakReason.Add("nextRect: endDist");
                    }

                    /**
                     * 段首缩进特征：通过比较本行与上一行的行首距离判断本行是否是新段落第一行
                     */
                    var startDist = Math.Sqrt(Math.Pow(rect.rectangle.Left - rectLast.rectangle.Left, 2) + Math.Pow(rect.rectangle.Top - rectLast.rectangle.Bottom, 2));
                    var isStartIndent = startDist >= indent;
                    var isStartShort = rect.rectangle.Left - rectLast.rectangle.Left > 0;
                    // 本行是段首缩进，上一行应该换行
                    if (isStartIndent && isStartShort)
                    {
                        rectLast.isBreak = true;
                        rectLast.breakReason.Add("nextRect: startDist");
                    }

                    // 上一行是段首缩进，上上行应该换行
                    if (isStartIndent && !isStartShort && rectLastLast != null)
                    {
                        rectLastLast.isBreak = true;
                        rectLastLast.breakReason.Add("nextNextRect: startDist");
                    }
                }
            }
        }

        Dictionary<string, int> langIndents = new Dictionary<string, int>() { { "zh", 2 }, { "jp", 2 }, { "ko", 2 } };
        /**
        * 段首缩进字宽
        * 2 字宽：中文，日语
        * 6 字宽：其他
        * @param  {String} lang franc-min 识别的语言类型字符串
        * @return {Number}      缩进字宽数量
*/
        public int indentSize(string lang)
        {
            return langIndents.ContainsKey(lang) ? langIndents[lang] : 6;
        }

        Dictionary<string, string> langSeparators = new Dictionary<string, string>() { { "zh", "" }, { "jp", "" }, { "kr", "" } };
        /**
        * 行间连字符
        * 空白：中文，日语
        * 一个空格：其他
        * @param  {String} lang franc-min 识别的语言类型字符串
        * @return {Number}      行间连字符
*/
        public string lineSeparator(string lang)
        {
            return langSeparators.ContainsKey(lang) ? langSeparators[lang] : " ";
        }

        Dictionary<string, double> percOfSymbols = new Dictionary<string, double>() { { "zh", 0.03 }, { "jp", 0.03 }, { "kr", 0.03 } };
        /**
        * 标点符号最低占比
        * 0.03：中文，日语
        * 0：其他
        * @param  {String} lang franc-min 识别的语言类型字符串
        * @return {Number}      标点符号占比比例
*/
        public double minPercOfSymbol(string lang)
        {
            return percOfSymbols.ContainsKey(lang) ? percOfSymbols[lang] : 0;
        }

        /**
        * 是否标点符号占比过低
        * @param  {String} str 字符串内容
        * @return {Boolean}
*/
        public bool ifHasFewSymbol(string str)
        {
            if (string.IsNullOrEmpty(str)) return false;

            var lang = wordsResult.Select(p => p.lang).FirstOrDefault();
            if (string.IsNullOrEmpty(lang) || !percOfSymbols.ContainsKey(lang))
            {
                return false;
            }
            var minPerc = minPercOfSymbol(lang);
            var symbols = new[] {
    ",", ".", ";", ":", "?", "!", "…", "-", "~", "(", ")", "，", "。", "、", "；", "：", "？", "！", "……", "—", "～", "（", "）"};
            var regStr = "[" + Regex.Replace(string.Join("", symbols), @"[.*+?^${}\-()|[\]\\]", "\\$&") + "]+";
            var matches = Regex.Matches(str, regStr).Count;
            var perc = matches * 1.0d / str.Length;
            // console.log("ifHasFewSymbol", str.length, matches, perc, minPerc)

            return perc <= minPerc;
        }

        /**
        * 是否每行字数过少
        * @param  {Number} maxLineWordsCount
        * @return {Boolean}
*/
        public bool ifLineWordsFewCount(int maxLineWordsCount)
        {
            //特征：最长行不超过 12 个字
            return maxLineWordsCount <= 12;
        }

        /**
            * 是否是以数字/罗马数字开头的列表格式
*/
        public bool ifStartWithNumAll()
        {
            var startWithNumCount = 0;
            foreach (var rect in wordsResult)
            {
                var str = Regex.Replace(rect.words, @"[\s\(\[（]+", "");
                if (Regex.IsMatch(str, @"^[\d\*·ⅠⅡⅢⅣⅤⅥⅦⅧⅨⅩ]+"))
                {
                    startWithNumCount++;
                }
            }
            // 70% 以上行数数字开头
            return (startWithNumCount * 1.0d / wordsResult.Count) > 0.7;
        }

        /**
        * 行文字是否以 数字/罗马数字开头的列表格式
        * @param  {String} str  单行文字
        * @return {Booean}
*/
        public bool ifStartWithNum(string str)
        {
            var strLtrim = Regex.Replace(str, @"[\s\(\[（]+", "");
            return Regex.IsMatch(strLtrim, @"^[\*·ⅠⅡⅢⅣⅤⅥⅦⅧⅨⅩ]+");
        }

        /**
        * 是否是首行 并且 首行字数过少，需要换行
        * @param  {Number} i    wordsResult index
        * @param  {Object} rect
        * @param  {Number} maxLineWordsCount
        * @return {Boolean}
*/
        public bool ifFirstLineBreak(int i, LineInfo rect, int maxLineWordsCount)
        {
            if (i != 0) return false;
            if (maxLineWordsCount <= 0) return false;

            return rect.words.Length * 1.0d / maxLineWordsCount < 0.60;
        }

        /**
        * 强制所有行分段
        * @param  {Object} wordsResult
        * @param  {String} reason 原因
        * @return {Array} wordsResult
*/
        public void breakAllLines(string reason)
        {
            foreach (var rect in wordsResult)
            {
                rect.isBreak = true;
                rect.breakReason.Add(reason);
            }
        }
    }

    public class OcrLineProcess
    {
        public static Rectangle ToRectangle(LocationInfo location)
        {
            Rectangle rectangle = Rectangle.Empty;
            if (location.width > 0 && location.height > 0)
            {
                return new Rectangle((int)location.left, (int)location.top, (int)location.width, (int)location.height);
            }
            return rectangle;
        }
        public static Rectangle GetMergeRectangle(LineInfo line)
        {
            Rectangle result = Rectangle.Empty;
            line?.lstCell?.ForEach(p =>
            {
                if (p.location != null)
                {
                    if (result.IsEmpty)
                    {
                        result = ToRectangle(p.location);
                    }
                    else
                    {
                        result = Rectangle.Union(result, ToRectangle(p.location));
                    }
                }
            });
            return result;
        }

        public static int GetCharLength(string str)
        {
            var charNum = 0; //统计字节位数
            var charArray = str.ToCharArray();
            foreach (var eachChar in charArray)
            {
                if (eachChar >= 0x4e00 && eachChar <= 0x9fa5) //判断中文字符
                    charNum += 2;
                else
                    charNum += 1;
            }

            return charNum;
        }

        private const int MinWidth = 5;

        public static void ProcessTextByLineLocation(List<LineInfo> lstLine)
        {
            if (lstLine.Count() <= 0)
            {
                return;
            }

            lstLine.ForEach(p => p.Init());
            var nMaxRight = lstLine.Max(p => p.rectangle.Right);
            var averageWidth = lstLine.Where(p => p.AverageWidth > 0).Average(p => p.AverageWidth);
            if (averageWidth == 0)
            {
                averageWidth = MinWidth;
            }

            averageWidth = averageWidth * 2.5;
            for (var j = 0; j < lstLine.Count - 1; j++)
            {

                var thisLine = lstLine[j];
                var nextLine = lstLine[j + 1];

                if (string.IsNullOrEmpty(thisLine.words) && string.IsNullOrEmpty(nextLine.words))
                {
                    continue;
                }

                var isNextLine = false;
                var isSpace = false;

                //总体误差小于1个字节，算对齐
                if (Math.Abs(thisLine.rectangle.X + thisLine.rectangle.Width - nextLine.rectangle.X -
                             nextLine.rectangle.Width) > averageWidth)
                {
                    //左边误差1个字节，判断右边
                    if (Math.Abs(thisLine.rectangle.X - nextLine.rectangle.X) < averageWidth)
                    {
                        if (thisLine.rectangle.Right + averageWidth < nMaxRight)
                        {
                            isNextLine = true;
                        }
                        else
                        {
                            isNextLine = thisLine.rectangle.Right < nextLine.rectangle.Right;
                        }
                    }
                    else
                    {
                        if (Math.Abs(thisLine.rectangle.Right - nextLine.rectangle.Right) > averageWidth)
                        {
                            if (thisLine.rectangle.Right + averageWidth < nextLine.rectangle.Right)
                            {
                                isNextLine = true;
                            }
                            else
                            {
                                isNextLine = thisLine.rectangle.X < nextLine.rectangle.X;
                            }
                        }
                    }
                }

                thisLine.isBreak = isNextLine;
            }

            var strTmp = string.Join("",
                lstLine.Select(p => string.Format("{0}{1}", p.words, p.isBreak ? "\n" : "")));
        }

        private static List<string> LstEnPunctuation = new List<string>()
            {":", ";", ",", "?", "!", "[", "]", "(", ")", "\'", "\""};

        public static bool Contain_EN(string str)
        {
            return Regex.IsMatch(str, "[a-zA-Z]");
        }

        public static bool Contain_CN(string str)
        {
            return Regex.IsMatch(str, "[\\u4e00-\\u9fa5]");
        }

        private static bool Is_punctuation(string text)
        {
            return ",;:，（）、；".IndexOf(text) != -1;
        }

        private static bool has_punctuation(string text)
        {
            return ",;，；、<>《》()-（）".IndexOf(text) != -1;
        }

        private static bool split_paragraph(string text)
        {
            return "。？！?!：".IndexOf(text, StringComparison.Ordinal) != -1;
        }

        private static bool contain_en(string str)
        {
            return Regex.IsMatch(str, "[a-zA-Z]");
        }

        private static bool contain_ch(string str)
        {
            return Regex.IsMatch(str, "[\\u4e00-\\u9fa5]");
        }

        private static bool IsNum(string str)
        {
            return Regex.IsMatch(str, "^\\d+$");
        }

        private static bool punctuation_has_punctuation(string str)
        {
            string pattern = (!contain_ch(str)) ? "[\\;\\,\\.\\!\\?]" : "[\\；\\，\\。\\！\\？]";
            return Regex.IsMatch(str, pattern);
        }

        private static string check_str(string text)
        {
            if (contain_ch(text.Trim()))
            {
                text = punctuation_en_ch(text.Trim());
                text = check_ch_en(text.Trim());
            }
            else
            {
                text = punctuation_ch_en(text.Trim());
                if (text.Contains(".") && (text.Contains(",") || text.Contains("!") || text.Contains("(") ||
                                           text.Contains(")") || text.Contains("'")))
                {
                    text = punctuation_Del_space(text);
                }
            }

            return text;
        }

        private static string check_ch_en(string text)
        {
            char[] array = text.ToCharArray();
            for (int i = 0; i < array.Length; i++)
            {
                int num = "：".IndexOf(array[i]);
                if (num != -1 && i - 1 >= 0 && i + 1 < array.Length && contain_en(array[i - 1].ToString()) &&
                    contain_en(array[i + 1].ToString()))
                {
                    array[i] = ":"[num];
                }

                if (num != -1 && i - 1 >= 0 && i + 1 < array.Length && contain_en(array[i - 1].ToString()) &&
                    contain_punctuation(array[i + 1].ToString()))
                {
                    array[i] = ":"[num];
                }
            }

            return new string(array);
        }

        private static bool contain_punctuation(string str)
        {
            return Regex.IsMatch(str, "\\p{P}");
        }

        private static string punctuation_ch_en(string text)
        {
            char[] array = text.ToCharArray();
            for (int i = 0; i < array.Length; i++)
            {
                int num = "：。；，？！“”‘’【】（）".IndexOf(array[i]);
                if (num != -1)
                {
                    array[i] = ":.;,?!\"\"''[]()"[num];
                }
            }

            return new string(array);
        }

        private static string punctuation_en_ch(string text)
        {
            char[] array = text.ToCharArray();
            for (int i = 0; i < array.Length; i++)
            {
                int num = ":;,?![]()".IndexOf(array[i]);
                if (num != -1)
                {
                    array[i] = "：；，？！【】（）"[num];
                }
            }

            return new string(array);
        }

        private static string Del_Space(string text)
        {
            text = Regex.Replace(text, "([\\p{P}]+)", "**&&**$1**&&**");
            char[] trimChars = null;
            text = text.TrimEnd(trimChars).Replace(" **&&**", "").Replace("**&&** ", "")
                .Replace("**&&**", "");
            return text;
        }

        public static string punctuation_Del_space(string text)
        {
            string pattern = "(?<=.)([^\\*]+)(?=.)";
            if (Regex.Match(text, pattern).ToString().IndexOf(" ") >= 0)
            {
                text = Regex.Replace(text, "(?<=[\\p{P}*])([a-zA-Z])(?=[a-zA-Z])", " $1");
                char[] trimChars = null;
                text = text.TrimEnd(trimChars).Replace("- ", "-").Replace("_ ", "_")
                    .Replace("( ", "(")
                    .Replace("/ ", "/")
                    .Replace("\" ", "\"");
                return text;
            }

            return text;
        }
    }

    public class TextLine
    {

        public String mTitle;

        public ChapterBook mBook;
        public int mBegin;
        public int mEnd;
        public int mIndex;

        public int mContentLength;

        public int mTitleStart;
        public int mTitleEnd;

        public TextLine(ChapterBook book, int begin, int end, int index)
        {

            this.mBook = book;
            this.mBegin = begin;
            this.mEnd = end;
            this.mContentLength = (end - begin - 1);

            this.mIndex = index;

            this.mTitleStart = -1;
            this.mTitleEnd = -1;
        }

        public int contentLength()
        {
            return mContentLength;
        }


        public int wordLength()
        {
            int start = this.getStart(true);
            int end = this.getEnd(true);

            if (start >= end)
            {
                return 0;
            }

            return (end - start);
        }

        public String getTitle()
        {

            TextLine line = this;
            if (!line.isTitle())
            {
                return "";
            }

            if (!string.IsNullOrEmpty(mTitle))
            {
                return mTitle;
            }

            String text = mBook.mText;

            int start = line.mTitleStart;
            int end = line.mTitleEnd;
            mTitle = text.Substring(start, end);

            return mTitle;
        }

        public int getStart(bool trim)
        {
            if (!trim)
            {
                return this.mBegin;
            }

            String text = mBook.mText;

            TextLine line = this;
            int start = line.mBegin;
            int end = line.mEnd;
            int last = end - 1;

            for (int i = start; i < last; i++)
            {
                char c = text[i];
                if (!ChapterBook.isWhitespace(c))
                {
                    return i;
                }
            }

            return end;
        }

        public int getEnd(bool trim)
        {
            if (!trim)
            {
                return this.mEnd;
            }

            String text = mBook.mText;

            TextLine line = this;
            int start = line.mBegin;
            int end = line.mEnd;
            int last = end - 1;

            for (int i = last; i > start; i--)
            {
                char c = text[i];
                if (!ChapterBook.isWhitespace(c))
                {
                    return (i + 1);
                }
            }

            return start;
        }

        public void update(BoundaryString text)
        {
            int start = getStart(true);
            int end = getEnd(true);

            text.mStart = start;
            text.mEnd = end;
        }

        public bool isEmpty()
        {
            return (this.mBegin == getEnd(true));
        }

        public bool isTitle()
        {
            return (mTitleStart >= 0 && mTitleEnd >= 0);
        }

        public String getText()
        {
            int start = this.getStart(true);
            int end = this.getEnd(true);

            if (start >= end)
            {
                return "";
            }

            return mBook.mText.Substring(start, end);
        }


        public String toString()
        {
            return "(" + mBegin + ", " + mEnd + ")";
        }
    }

    public class BoundaryString
    {

        String mText;
        public int mStart;
        public int mEnd;

        int mLength;

        public BoundaryString(String text)
        {
            this.mText = text;
            this.mStart = 0;
            this.mEnd = text.Length;

            this.mLength = text.Length;
        }

        public String getString()
        {
            return this.mText;
        }

        public int length()
        {
            if (isEmpty())
            {
                return 0;
            }

            return mEnd - mStart;
        }

        public bool isEmpty()
        {
            return (mStart >= mEnd);
        }

        public void set(int start, int end)
        {
            this.mStart = start;
            mStart = (mStart > mLength) ? mLength : mStart;

            this.mEnd = end;
            mEnd = (mEnd > mLength) ? mLength : mEnd;
        }

        public void trim()
        {

            int start = mStart;
            for (int i = start; i < mEnd; i++)
            {
                char c = mText[i];
                if (!ChapterBook.isWhitespace(c))
                {
                    start = i;
                    break;
                }
            }

            int end = mEnd;
            for (int i = end - 1; i >= mStart; i--)
            {
                char c = mText[i];
                if (!ChapterBook.isWhitespace(c))
                {
                    end = i + 1;
                    break;
                }
            }

            this.mStart = start;
            this.mEnd = end;
        }

        public int getStart()
        {
            return mStart;
        }

        public int getEnd()
        {
            return mEnd;
        }

        public bool isEnd()
        {
            return (mEnd >= mText.Length);
        }

        /**
         * 只适合短长度
         *
         * @param ch
         * @return
         */
        public int indexOf(int ch)
        {
            return this.indexOf(ch, mStart, mEnd);
        }

        /**
         * 只适合短长度
         *
         * @param ch
         * @param fromIndex
         * @return
         */
        public int indexOf(int ch, int fromIndex)
        {
            return this.indexOf(ch, fromIndex, mEnd);
        }

        private int indexOf(int ch, int fromIndex, int endIndex)
        {
            int max = endIndex;
            if (fromIndex < 0)
            {
                fromIndex = 0;
            }
            else if (fromIndex >= max)
            {
                // Note: fromIndex might be near -1>>>1.
                return -1;
            }

            if (ch < 0x010000)
            {
                // handle most cases here (ch is a BMP code point or a
                // negative value (invalid code point))
                for (int i = fromIndex; i < max; i++)
                {
                    if (mText[i] == ch)
                    {
                        return i;
                    }
                }
                return -1;
            }
            else
            {
                return indexOfSupplementary(ch, fromIndex, endIndex);
            }
        }
        public bool isValidCodePoint(int codePoint)
        {
            // Optimized form of:
            //     codePoint >= MIN_CODE_POINT && codePoint <= MAX_CODE_POINT
            int plane = codePoint >> 16;
            return plane < ((0X10FFFF + 1) >> 16);
        }

        public char highSurrogate(int codePoint)
        {
            return (char)((codePoint >> 10)
                          + ('\uD800' - (0x010000 >> 10)));
        }

        public char lowSurrogate(int codePoint)
        {
            return (char)((codePoint & 0x3ff) + '\uDC00');
        }

        private int indexOfSupplementary(int ch, int fromIndex, int endIndex)
        {
            if (isValidCodePoint(ch))
            {
                char hi = highSurrogate(ch);
                char lo = lowSurrogate(ch);
                int max = endIndex - 1;

                for (int i = fromIndex; i < max; i++)
                {
                    if (mText[i] == hi && mText[i + 1] == lo)
                    {
                        return i;
                    }
                }
            }
            return -1;
        }

        public String toString()
        {
            return mText.Substring(mStart, mEnd);
        }
    }

    public class Chapter
    {

        private static char[] APPENDABLE = new char[] { '\uff0c', '\u201c', '\u2018', '\u300c', '\u3001' }; // ，“‘「、
        private static char[] FORCE = new char[] { '\uff0c', '\u201d', '\u2019', '\u300d', '\u3001' }; // ，”’」、

        private static char[] CONCATABLE = new char[]
            {'\uff0c', '\u3002', '\u201c', '\uff1b', '\u3001', '\u2014', '\uff01', '\uff1f'}; // ，。“；、—！？

        string mName;

        string mTitle;
        string mSubtitle;

        ChapterBook mBook;
        string mText;
        public List<TextLine> mList;

        public Chapter(ChapterBook book, int initialCapacity)
        {
            this.mName = null;

            this.mBook = book;
            this.mText = book.mText;
            this.mList = new List<TextLine>(initialCapacity);
        }

        public string getTitle()
        {
            if (mTitle != null)
            {
                return mTitle;
            }

            this.buildTitle();
            return mTitle;
        }

        public string getSubtitle()
        {
            if (mSubtitle != null)
            {
                return mSubtitle;
            }

            this.buildTitle();
            return mSubtitle;
        }

        public string getName(bool trim)
        {

            if (mList?.Count <= 0)
            {
                return "";
            }

            if (!string.IsNullOrEmpty(mName))
            {
                return mName;
            }

            var line = this.getTitleLine(true);
            int start = line.mTitleStart;
            if (start < 0)
            {
                start = line.mBegin;
            }

            if (!trim)
            {
                start = line.mBegin;
            }

            int length = 0;
            if (line.mTitleEnd > 0)
            {
                length = line.mTitleEnd - line.mTitleStart;
            }

            mName = mText.Substring(start, line.mEnd);
            mName = mName.Trim();

            if (trim)
            {
                if (length > 0 && length < mName.Length)
                {
                    var sb = new StringBuilder(mName.Length + 1);
                    sb.Append(mName.Substring(0, length));
                    sb.Append('\n');
                    sb.Append(mName.Substring(length).Trim());

                    mName = sb.ToString();
                }
            }

            return mName;
        }

        public string getContent()
        {
            int start = this.getStart();
            int end = this.getEnd();
            if (start == 0 && end == mText.Length)
            {
                return mText;
            }

            return mText.Substring(start, end);
        }

        //public string getPrettyContent(bool concat, bool beginSeparator, bool endSeparator)
        //{
        //    var list = new List<TextLine>(mList);

        //    this.trimAll();
        //    string text;
        //    if (mBook.size() == 1 && this.indexOfTitle() < 0)
        //    {
        //        text = this.getSingleContent(concat, beginSeparator, endSeparator);
        //    }
        //    else
        //    {
        //        text = this.getFormattedContent(concat, beginSeparator, endSeparator);
        //    }

        //    mList = list;

        //    return text;
        //}

        public TextLine getTitleLine(bool useFirst)
        {
            if (mList?.Count <= 0)
            {
                return null;
            }

            foreach (var textLine in mList)
            {
                if (textLine.isTitle())
                {
                    return textLine;
                }
            }

            if (useFirst)
            {
                return mList[0];
            }

            return null;
        }

        public void buildTitle()
        {
            if (mList?.Count <= 0)
            {
                this.mTitle = "";
                this.mSubtitle = "";

                return;
            }

            if (mTitle != null)
            {
                return;
            }

            var line = getTitleLine(false);
            if (line == null)
            {
                this.mTitle = "";
                this.mSubtitle = "";

                return;
            }

            int start = line.mTitleStart;
            int end = line.mTitleEnd;

            string s1 = line.mBook.mText.Substring(start, end);
            string s2 = line.mBook.mText.Substring(end + 1, line.mEnd - 1);
            s2 = ChapterBook.trim(s2);

            this.mTitle = s2;
            this.mSubtitle = s1;
        }

        public void trim()
        {

            while (mList.Count > 0)
            {
                var line = mList[0];
                if (line.isEmpty())
                {
                    mList.RemoveAt(0);
                    continue;
                }

                break;
            }

        }

        public void trimAll()
        {

            for (int i = mList.Count - 1; i >= 0; i--)
            {
                var line = mList[i];
                if (line.isEmpty())
                {
                    mList.RemoveAt(i);
                }
            }

        }

        public void add(Chapter chapter)
        {
            mList.AddRange(chapter.mList);
        }

        public bool hasTitle()
        {
            if (mList.Count <= 0)
            {
                return false;
            }

            foreach (var line in mList)
            {
                if (line.isTitle())
                {
                    return true;
                }
            }

            return false;
        }

        public bool beginWithTitle()
        {
            if (mList.Count <= 0)
            {
                return false;
            }

            var line = mList[0];
            return (line.isTitle());
        }

        public int indexOfTitle()
        {
            if (mList.Count <= 0)
            {
                return -1;
            }

            int size = mList.Count;
            for (int i = 0; i < size; i++)
            {
                var line = mList[i];
                if (line.isTitle())
                {
                    return i;
                }
            }

            return -1;
        }

        public int length()
        {
            return (getEnd() - getStart());
        }

        public int getStart()
        {
            if (mList.Count <= 0)
            {
                return 0;
            }

            return mList[0].mBegin;
        }

        public int getEnd()
        {
            if (mList.Count <= 0)
            {
                return mText.Length;
            }

            return mList[mList.Count - 1].mEnd;
        }

        public string getSingleContent(bool concat, bool beginSeparator, bool endSeparator)
        {
            if (mList.Count <= 0)
            {
                return "";
            }

            int min = -2;
            int max = 2;

            string prefix = "\u3000\u3000"; // 2个空白字符
            string separator = "\n\n";

            var ssb = new StringBuilder(mText);
            ssb.Clear();

            bool isEmpty;

            var text = new BoundaryString(mText);
            int size = mList.Count;
            for (int i = 0; i < size; i++)
            {
                var line = mList[i];
                line.update(text);

                // 空行，不要了
                isEmpty = text.isEmpty();
                if (isEmpty)
                {
                    continue;
                }

                // 内容
                string str = text.ToString();

                // 分割符
                bool canAppend = false;
                bool shouldConcat = false;
                if (concat)
                {
                    bool force = Chapter.isForceConcat(str);

                    canAppend = force ? force : Chapter.isAppendable(ssb.ToString());
                    shouldConcat = force ? force : Chapter.isConcatable(str);
                    if (i >= min && i <= max)
                    {
                        shouldConcat = false;
                    }
                }

                if (canAppend && shouldConcat)
                {

                }
                else
                {

                    if (ssb.Length != 0)
                    {
                        ssb.Append(separator);
                    }

                    if (beginSeparator && ssb.Length == 0)
                    {
                        ssb.Append('\n');
                    }

                    // 前置留白
                    if (i > 0)
                    {
                        ssb.Append(prefix);
                    }

                }

                // 内容
                if (i == 0)
                {
                    var ss = this.createTitle(str, true);
                    ssb.Append(ss);
                }
                else
                {
                    ssb.Append(str);
                }

            }

            if (endSeparator)
            {
                ssb.Append('\n');
            }

            return ssb.ToString();
        }

        public string getFormattedContent(bool concat, bool beginSeparator, bool endSeparator)
        {
            if (mList.Count <= 0)
            {
                return "";
            }

            int indexOfTitle = this.indexOfTitle();
            int min = indexOfTitle - 2;
            int max = indexOfTitle + 2;

            string prefix = "\u3000\u3000"; // 2个空白字符
            string separator = "\n\n";

            var ssb = new StringBuilder(mText);
            ssb.Clear();

            bool isEmpty;

            var text = new BoundaryString(mText);
            int size = mList.Count;
            for (int i = 0; i < size; i++)
            {
                var line = mList[i];
                line.update(text);

                // 空行，不要了
                isEmpty = text.isEmpty();
                if (isEmpty)
                {
                    continue;
                }

                // 内容
                string str = text.ToString();

                // 分割符
                bool canAppend = false;
                bool shouldConcat = false;
                if (concat)
                {
                    bool force = Chapter.isForceConcat(str);

                    canAppend = force ? force : Chapter.isAppendable(ssb.ToString());
                    shouldConcat = force ? force : Chapter.isConcatable(str);
                    if (i >= min && i <= max)
                    {
                        shouldConcat = false;
                    }
                }

                if (canAppend && shouldConcat)
                {

                }
                else
                {
                    if (ssb.Length != 0)
                    {
                        ssb.Append(separator);
                    }

                    if (beginSeparator && ssb.Length == 0)
                    {
                        ssb.Append('\n');
                    }

                    // 前置留白
                    if (mBook.isArbitrary())
                    {
                        if (!line.isTitle())
                        {
                            ssb.Append(prefix);
                        }
                    }
                    else
                    {
                        if (indexOfTitle >= 0)
                        {
                            if (i > indexOfTitle)
                            {
                                ssb.Append(prefix);
                            }
                        }
                    }
                }

                // 内容
                if (mBook.isArbitrary())
                {
                    if (line.isTitle())
                    {
                        var ss = this.createTitle(str, false);
                        ssb.Append(ss);
                    }
                    else
                    {
                        ssb.Append(str);
                    }
                }
                else
                {
                    if (i == indexOfTitle)
                    {
                        str = this.getTitle(line);
                        var ss = this.createTitle(str, true);
                        ssb.Append(ss);
                    }
                    else
                    {
                        ssb.Append(str);
                    }
                }

            }

            if (endSeparator)
            {
                ssb.Append('\n');
            }

            return ssb.ToString();

        }

        public string getTitle(TextLine line)
        {
            var sb = new StringBuilder(line.mContentLength + 1);

            var text = new BoundaryString(line.mBook.mText);
            text.set(line.mBegin, line.mTitleEnd);
            text.trim();
            sb.Append(text.ToString());

            sb.Append('\n');

            text.set(line.mTitleEnd + 1, line.mEnd);
            text.trim();
            sb.Append(text.ToString());

            return sb.ToString();
        }

        public string createTitle(string text, bool bigger)
        {
            return text;
        }

        public string lastLine()
        {
            int size = mList.Count;
            for (int i = size - 1; i >= 0; i--)
            {
                var line = mList[i];
                if (!line.isEmpty())
                {
                    return line.getText();
                }
            }

            return "";
        }

        public static bool isAppendable(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                return false;
            }

            int length = text.Length;
            char c = text[length - 1];

            // 中文结束，可以考虑
            if (ChapterBook.isChinese(c))
            {
                return true;
            }

            // 可以连接的符号
            char[] array = APPENDABLE;
            foreach (var v in array)
            {
                if (c == v)
                {
                    return true;
                }
            }

            return false;

        }

        public static bool isForceConcat(string text)
        {

            {
                char c = text[0];
                char[] array = FORCE;
                foreach (var v in array)
                {
                    if (c == v)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        public static bool isConcatable(string text)
        {

            if (text.Length >= 48)
            {
                return false;
            }

            {
                int count = 0;

                char[] array = CONCATABLE;
                foreach (var v in array)
                {
                    count += countOf(text, v);
                }

                int length = text.Length;
                if (length >= 12)
                {
                    return (count >= 2);
                }

                return (count >= 1);
            }
        }

        public static int countOf(string text, char c)
        {

            int count = 0;

            int index;
            int pos = 0;
            while (true)
            {
                index = text.IndexOf(c, pos);
                if (index < 0)
                {
                    break;
                }

                ++count;
                pos = index + 1;
            }

            return count;
        }

        public string tostring()
        {
            return this.getName(false);
        }
    }

    public class ChapterBook
    {
        public static String TAG = "ChapterBook";
        public static char NULL_SEPARATOR = ' ';

        public String mText;
        public char mSeparator;
        public List<Chapter> mChapters;

        public ChapterInflater mChapterInflater;
        public ArbitraryInflater mArbitraryInflater;

        public ConditionSet mCondition;
        public BoundaryString mBoundary;

        public bool mAutoDetect;

        public ChapterBook(String text)
        {
            this.mText = text;
            this.mSeparator = NULL_SEPARATOR;

            this.mBoundary = new BoundaryString(mText);

            this.mAutoDetect = true;
        }

        public ChapterBook(String text, bool autoDetect)
        {
            this.mText = text;
            this.mSeparator = NULL_SEPARATOR;

            this.mBoundary = new BoundaryString(mText);

            this.mAutoDetect = autoDetect;
        }

        public String getText()
        {
            return mText;
        }

        public List<Chapter> getList()
        {
            return mChapters;
        }

        public int size()
        {
            if (mChapters == null)
            {
                return 0;
            }

            return mChapters.Count;
        }

        public Chapter get(int index)
        {
            return mChapters[index];
        }

        public bool isArbitrary()
        {
            return (mArbitraryInflater != null);
        }

        public bool isDone()
        {
            if (mChapters == null || mChapters.Count <= 0)
            {
                return false;
            }

            Chapter last = mChapters[mChapters.Count - 1];
            return (last.getEnd() >= mText.Length);
        }

        public void next()
        {
            if (isDone())
            {
                return;
            }

            // 任意分段
            {
                if (mArbitraryInflater != null)
                {
                    mArbitraryInflater.next();
                    return;
                }
            }

            // 智能分段
            {
                if (mChapterInflater == null)
                {
                    mChapterInflater = new ChapterInflater(this);
                }

                mChapterInflater.next();

                if (!mChapterInflater.isDone())
                {

                    // 禁用章节检测
                    if (!mAutoDetect)
                    {
                        if (mChapters != null)
                        {
                            mChapters.Clear();
                        }
                    }

                    // 第一次分段后，没有出现章节，切换到任意分段
                    if (mChapters == null || mChapters.Count <= 2)
                    {

                        // 清除旧数据，重新来过
                        if (mChapters != null)
                        {
                            mChapters.Clear();
                        }

                        this.mArbitraryInflater = new ArbitraryInflater(mChapterInflater);
                        mArbitraryInflater.next();
                    }
                }
            }
        }

        public void inflate()
        {
            List<TextLine> lines = null;
            List<TextLine> filter;

            if (string.IsNullOrEmpty(mText))
            {
                this.mChapters = new List<Chapter>();
            }
            else
            {
                lines = this.getLines(mText);

                this.mCondition = ConditionSet.create(mText.Length);
                filter = this.filterLines(mText, lines, true, true);

                this.mChapters = this.getChapters(mText, lines, filter, true);

            }

            // 随意断章
            if (mChapters.Count <= 0)
            {
                var c = new Chapter(this, 11);
                if (lines != null)
                {
                    c.mList.AddRange(lines);
                }

                mChapters.Add(c);
            }
        }

        private List<Chapter> getChapters(String text, List<TextLine> all, List<TextLine> list, bool filter)
        {
            var chapters = new List<Chapter>(list.Count + 2);

            if (list.Count <= 0)
            {
                return chapters;
            }

            // 添加章节
            {
                // 前
                if (list[0].mIndex != 0)
                {
                    int start = 0;
                    int end = list[0].mIndex;

                    Chapter e = new Chapter(this, (end - start + 11));
                    for (int j = start; j < end; j++)
                    {
                        e.mList.Add(all[j]);
                    }

                    chapters.Add(e);
                }

                // 中
                int last = list.Count - 1;
                for (int i = 0; i < last; i++)
                {
                    int start = list[i].mIndex;
                    int end = list[i + 1].mIndex;

                    Chapter e = new Chapter(this, (end - start + 11));
                    for (int j = start; j < end; j++)
                    {
                        e.mList.Add(all[j]);
                    }

                    chapters.Add(e);
                }

                // 后
                {
                    int start = list[last].mIndex;
                    int end = all.Count;

                    Chapter e = new Chapter(this, (end - start + 11));
                    for (int j = start; j < end; j++)
                    {
                        e.mList.Add(all[j]);
                    }

                    chapters.Add(e);
                }
            }

            // 过滤掉空白章节
            if (filter)
            {
                int start = getStart();
                while (chapters.Count > 0)
                {
                    var e = chapters[0];
                    if (e.getEnd() > start)
                    {
                        break;
                    }

                    chapters.Remove(e);
                }

            }

            return chapters;
        }

        private List<TextLine> filterLines(String text, List<TextLine> lines, bool filterEmpty, bool filterSame)
        {
            var list = new List<TextLine>();

            // 过滤出标题
            foreach (var line in lines)
            {
                if (mCondition.accept(line))
                {
                    list.Add(line);
                }
            }

            // 过滤没有内容的标题
            if (filterEmpty)
            {
                var array = new List<TextLine>(list.Count);

                int size = list.Count;
                int last = size - 1;
                for (int i = 0; i < size; i++)
                {
                    var line = list[i];

                    int begin = line.mIndex;
                    int end = (i == last) ? lines.Count : list[i + 1].mIndex;

                    bool result = hasContent(lines, begin + 1, end);
                    if (result)
                    {
                        array.Add(line);
                    }
                }

                list = array;
            }

            // 过滤出相同关键字
            if (filterSame)
            {
                var array = new List<TextLine>(list.Count);
                if (list.Count > 0)
                {
                    array.Add(list[0]);
                }

                int size = list.Count;
                for (int i = 1; i < size; i++)
                {
                    var pre = array[array.Count - 1];
                    var line = list[i];

                    String t1 = pre.getTitle();
                    String t2 = line.getTitle();
                    if (!t1.Equals(t2))
                    {
                        array.Add(line);
                    }
                }

                list = array;
            }

            return list;
        }

        private List<TextLine> getLines(String text)
        {

            char separator;

            // 获取分隔符
            {
                this.mSeparator = getSeparator(text);
                separator = mSeparator;
            }

            int length = text.Length;

            int capacity = length;
            capacity /= 200;
            capacity = (capacity < 10) ? 10 : capacity;

            var list = new List<TextLine>(capacity);

            // 执行分行
            if (separator == NULL_SEPARATOR)
            {
                list.Add(new TextLine(this, 0, length, 0));
            }
            else
            {

                int sl = 1;

                int index = 0;

                int begin = 0;
                while (true)
                {
                    int pos = text.IndexOf(separator, begin);

                    if (pos >= 0)
                    {
                        var line = new TextLine(this, begin, pos + sl, index);
                        list.Add(line);

                        begin = line.mEnd;

                        if (begin == text.Length)
                        {
                            break;
                        }

                    }
                    else
                    {
                        var line = new TextLine(this, begin, text.Length, index);
                        list.Add(line);

                        break;
                    }

                    index++;
                }
            }

            return list;
        }

        private bool hasContent(List<TextLine> lines, int begin, int end)
        {
            bool result = false;

            for (int i = begin; i < end; i++)
            {
                var line = lines[i];
                if (!line.isEmpty())
                {
                    result = true;
                    break;
                }
            }

            return result;
        }

        public int getStart()
        {
            String text = this.mText;

            int start = 0;
            int end = text.Length;

            for (int i = start; i < end; i++)
            {
                char c = text[i];
                if (!isWhitespace(c))
                {
                    return i;
                }
            }

            return end;
        }

        public static char getSeparator(String text)
        {
            char separator = ' ';

            int newLinePos = text.IndexOf('\n');
            int charReturnPos = text.IndexOf('\r');
            if (newLinePos >= 0 && charReturnPos >= 0)
            {
                separator = '\n';
            }
            else if (newLinePos >= 0 && charReturnPos < 0)
            {

                separator = '\n';

            }
            else if (newLinePos < 0 && charReturnPos >= 0)
            {
                separator = '\r';
            }

            return separator;
        }

        public static String trim(String str)
        {
            int start = str.Length;
            int end = 0;
            int length = str.Length;

            for (int i = 0; i < length; i++)
            {
                char c = str[i];
                if (!isWhitespace(c))
                {
                    start = i;
                    break;
                }
            }

            for (int i = length - 1; i >= 0; i--)
            {
                char c = str[i];
                if (!isWhitespace(c))
                {
                    end = i + 1;
                    break;
                }
            }

            if (start >= end)
            {
                return "";
            }

            return str.Substring(start, end);
        }

        public static bool isWhitespace(char c)
        {
            return c == ' '
                   || c == '\u3000'
                   || c == 0xA0 // macos将rtfd转html后，出现的空白字符
                   || c == '\r'
                   || c == '\n';
        }

        public static bool isChinese(char c)
        {
            if (c < '\u4e00' || c > '\u9fa5')
            {
                // 中文范围
                return false;
            }

            return true;
        }

    }

    public class ChapterInflater
    {

        public static String TAG = "ChapterInflater";

        public static int STEP_CHARS = 50 * 1024;

        public ChapterBook mBook;

        public char mSeparator;

        public List<TextLine> mTitles;    // 记录需要处理的标题
        public List<TextLine> mLines;     // 记录所有的分行

        public int mCursor;                    // 记录已处理完毕的文本位置
        public BoundaryString mBounday;

        public ConditionSet mCondition;
        public List<TextLine> mTmpList;

        public bool mChapterOnce = false;   // 特殊处理第一章节

        public ChapterInflater(ChapterBook book)
        {
            String text = book.mText;

            this.mBook = book;

            this.mCursor = 0;
            this.mBounday = new BoundaryString(text);

            this.mSeparator = ChapterBook.getSeparator(text);

            // 如果找不到分隔符，直接结束
            if (mSeparator == ChapterBook.NULL_SEPARATOR)
            {

                this.mCursor = text.Length;
                this.mLines = new List<TextLine>();
                mLines.Add(new TextLine(book, 0, mCursor, 0));

                book.mChapters = new List<Chapter>();

                Chapter c = new Chapter(book, 11);
                c.mList.AddRange(mLines);
                book.mChapters.Add(c);

            }
            else
            {
                int length = text.Length;

                int capacity = length;
                capacity /= 200;
                capacity = (capacity < 10) ? 10 : capacity;

                this.mLines = new List<TextLine>(capacity);

                capacity /= 200;
                capacity = (capacity < 10) ? 10 : capacity;

                this.mTitles = new List<TextLine>(capacity);

                this.mCondition = ConditionSet.create(text.Length);

                this.mTmpList = new List<TextLine>(capacity);
            }
        }

        public bool isDone()
        {
            return (mCursor >= mBounday.getString().Length);
        }

        public void next()
        {
            if (isDone())
            {
                return;
            }

            int lineStart = mLines.Count;  // 行开始位置

            // 提取分行
            {
                //            long time = System.currentTimeMillis();

                {
                    this.retrieveLines(STEP_CHARS);
                }

                //            long ellapse = System.currentTimeMillis() - time;
                //            Log.w(TAG, "retrieveLines = " + ellapse);
            }

            List<TextLine> titles = this.mTmpList;     // 新增加的标题
            titles.Clear();

            // 提取标题
            {
                //            long time = System.currentTimeMillis();

                {
                    this.retrieveTitle(lineStart, titles);
                }

                //            long ellapse = System.currentTimeMillis() - time;
                //            Log.w(TAG, "retrieveTitle = " + ellapse);
            }

            // 过滤没有内容的标题
            {
                //            long time = System.currentTimeMillis();

                {
                    this.mTitles.AddRange(titles);
                    titles.Clear();

                    List<TextLine> input = mTitles;
                    List<TextLine> output = titles;

                    this.filterEmptyTitle(input, output);

                    mTitles.Clear();
                    titles = output;
                }

                //            long ellapse = System.currentTimeMillis() - time;
                //            Log.w(TAG, "filterEmptyTitle = " + ellapse);
            }

            // 过滤重复关键字的标题
            {
                //            long time = System.currentTimeMillis();

                {
                    this.mTitles.AddRange(titles);
                    titles.Clear();

                    List<TextLine> input = mTitles;
                    List<TextLine> output = titles;

                    this.filterSameTitle(input, output);

                    mTitles.Clear();
                    titles = output;
                }

                //            long ellapse = System.currentTimeMillis() - time;
                //            Log.w(TAG, "filterSameTitle = " + ellapse);
            }

            // 提取章节
            {
                //            long time = System.currentTimeMillis();

                {
                    this.retrieveChapters(titles);

                    // 保留最后一个标题下次处理
                    mTitles.Clear();
                    if (!mBounday.isEnd())
                    {
                        if (titles.Count > 0)
                        {
                            mTitles.Add(titles[titles.Count - 1]);
                        }
                    }
                }

                //            long ellapse = System.currentTimeMillis() - time;
                //            Log.w(TAG, "retrieveChapters = " + ellapse);
            }

            // 处理第一个章节，字数不够，第二个章节来凑
            if (!mChapterOnce)
            {
                List<Chapter> list = mBook.mChapters;
                if (list != null && list.Count >= 2)
                {
                    mChapterOnce = true;

                    Chapter c1 = list[0];
                    Chapter c2 = list[1];
                    if (!c1.beginWithTitle() && c1.length() < 512)
                    {
                        c1.add(c2);
                        list.RemoveAt(1);
                    }
                }
            }

            // 删除文章前的空白内容
            if (mBounday.isEnd())
            {
                List<Chapter> chapters = mBook.mChapters;
                if (chapters == null)
                {
                    chapters = new List<Chapter>();

                    Chapter c = new Chapter(mBook, mLines.Count);
                    c.mList.AddRange(mLines);
                    chapters.Add(c);

                    mBook.mChapters = chapters;
                }

                if (chapters != null)
                {

                    foreach (Chapter c in chapters)
                    {
                        c.trim();
                    }

                }
            }

            if (mBounday.isEnd())
            {
                //Log.e(TAG, "[Nothing happen]IMPORTANT: chapter number = " + mBook.size());
            }

        }

        public void retrieveChapters(List<TextLine> list)
        {
            if (list.Count <= 0)
            {
                return;
            }

            if (!mBounday.isEnd() && list.Count == 1)
            {
                return;
            }

            String text = mBounday.getString();

            List<Chapter> chapters = mBook.mChapters;
            bool isNull = (chapters == null);
            if (chapters == null)
            {
                int capacity = text.Length;
                capacity /= 200;
                capacity /= 200;
                capacity = (capacity < 10) ? 10 : capacity;

                mBook.mChapters = new List<Chapter>(capacity);
                chapters = mBook.mChapters;
            }

            // 添加章节
            {
                // 前
                if (isNull && list[0].mIndex != 0)
                {
                    int start = 0;
                    int end = list[0].mIndex;

                    Chapter e = new Chapter(mBook, (end - start + 11));
                    for (int j = start; j < end; j++)
                    {
                        e.mList.Add(mLines[j]);
                    }

                    chapters.Add(e);
                }

                // 中
                int last = list.Count - 1;
                for (int i = 0; i < last; i++)
                {
                    int start = list[i].mIndex;
                    int end = list[i + 1].mIndex;

                    Chapter e = new Chapter(mBook, (end - start + 11));
                    for (int j = start; j < end; j++)
                    {
                        e.mList.Add(mLines[j]);
                    }
                    chapters.Add(e);
                }

                // 后
                if (mBounday.isEnd())
                {
                    int start = list[last].mIndex;
                    int end = mLines.Count;

                    Chapter e = new Chapter(mBook, (end - start + 11));
                    for (int j = start; j < end; j++)
                    {
                        e.mList.Add(mLines[j]);
                    }
                    chapters.Add(e);
                }
            }

            // 过滤掉空白章节
            if (true)
            {
                int start = getStart();
                while (chapters.Count > 0)
                {
                    Chapter e = chapters[0];
                    if (e.getEnd() > start)
                    {
                        break;
                    }

                    chapters.Remove(e);
                }

            }
        }

        public void filterSameTitle(List<TextLine> input, List<TextLine> output)
        {

            if (input.Count > 0)
            {
                output.Add(input[0]);
            }

            int size = input.Count;
            for (int i = 1; i < size; i++)
            {
                TextLine pre = output[output.Count - 1];
                TextLine line = input[i];

                String t1 = pre.getTitle();
                String t2 = line.getTitle();
                if (!t1.Equals(t2))
                {
                    output.Add(line);
                }
                else
                {
                    line.mTitleStart = line.mTitleEnd = -1;
                }
            }

        }

        public void filterEmptyTitle(List<TextLine> input, List<TextLine> output)
        {

            int size = input.Count;
            if (size == 0)
            {
                return;
            }

            int last = size - 1;
            int length = (mBounday.isEnd()) ? size : last;

            for (int i = 0; i < length; i++)
            {
                TextLine line = input[i];

                int begin = line.mIndex;
                int end = (i == last) ? mLines.Count : input[i + 1].mIndex;

                bool result = hasContent(begin + 1, end);
                if (result)
                {
                    output.Add(line);
                }
                else
                {
                    line.mTitleStart = line.mTitleEnd = -1;
                }
            }

            // 没有到结束，最后一个标题总是不处理
            if (!mBounday.isEnd())
            {
                output.Add(input[last]);
            }
        }

        public void retrieveTitle(int fromIndex, List<TextLine> output)
        {

            int start = fromIndex;
            int end = mLines.Count;

            for (int i = start; i < end; i++)
            {
                TextLine line = mLines[i];
                if (mCondition.accept(line))
                {
                    output.Add(line);
                }
            }

        }

        public void retrieveLines(int step)
        {

            int start = mCursor;
            int end = start;
            int lineNum = mLines.Count;
            do
            {
                end += step;

                mBounday.set(start, end);
                this.retrieveLines(mBounday);

                // 如果已经移动到最后，结束之
                if (mBounday.isEnd())
                {
                    break;
                }

            } while (lineNum == mLines.Count); // 一定要找到新的分行

            // 更新指针位置
            if (mBounday.isEnd())
            {
                mCursor = mBounday.getEnd();
            }
            else
            {
                int last = mLines.Count - 1;
                mCursor = mLines[last].mEnd;
            }
        }

        public void retrieveLines(BoundaryString text)
        {

            char separator = this.mSeparator;
            int sl = 1;

            int index = 0;
            if (mLines.Count > 0)
            {
                int last = mLines.Count - 1;
                index = mLines[last].mIndex;

                index++;
            }

            String str = text.getString();
            int begin = text.getStart();
            while (true)
            {
                int pos = str.IndexOf(separator, begin);

                if (pos >= 0 && pos < text.getEnd())
                {
                    TextLine line = new TextLine(mBook, begin, pos + sl, index);
                    mLines.Add(line);

                    begin = line.mEnd;

                    if (begin == text.getEnd())
                    {
                        break;
                    }

                }
                else
                {
                    if (text.isEnd())
                    {
                        TextLine line = new TextLine(mBook, begin, text.getEnd(), index);
                        mLines.Add(line);
                    }

                    break;
                }

                index++;
            }


        }

        public int getStart()
        {
            String text = mBounday.getString();

            int start = 0;
            int end = text.Length;

            for (int i = start; i < end; i++)
            {
                char c = text[i];
                if (!ChapterBook.isWhitespace(c))
                {
                    return i;
                }
            }

            return end;
        }

        public bool hasContent(int begin, int end)
        {
            bool result = false;

            for (int i = begin; i < end; i++)
            {
                TextLine line = mLines[i];
                if (!line.isEmpty())
                {
                    result = true;
                    break;
                }
            }

            return result;
        }
    }

    public class ArbitraryInflater
    {

        public static String TAG = "ArbitraryInflater";

        public static int STEP_CHARS = 20 * 1024;
        public static int CHAPTER_SIZE = 8 * 1024;
        public static int MIN_SIZE = 2 * 1024; // 最后一段，不少于这个字数

        public ChapterBook mBook;

        public char mSeparator;

        public int mLineCursor;
        public List<TextLine> mLines;     // 记录所有的分行

        public int mCursor;                    // 记录已处理完毕的文本位置
        public BoundaryString mBounday;

        public ConditionSet mCondition;
        public List<TextLine> mTmpList;

        public ArbitraryInflater(ChapterInflater inflater)
        {

            this.mBook = inflater.mBook;
            if (mBook.mChapters == null)
            {
                mBook.mChapters = new List<Chapter>();
            }

            this.mSeparator = inflater.mSeparator;

            this.mLineCursor = 0;
            this.mLines = inflater.mLines;

            this.mCursor = inflater.mCursor;
            this.mBounday = inflater.mBounday;

            mCondition = inflater.mCondition;
            this.mTmpList = inflater.mTmpList;

            if (mCondition != null)
            {
                int start = 0;
                int end = mLines.Count;
                for (int i = start; i < end; i++)
                {
                    TextLine line = mLines[i];
                    mCondition.accept(line);
                }
            }

        }

        public bool isDone()
        {
            if (mLines == null)
            {
                return true;
            }

            bool lineEnd = (mLineCursor >= mLines.Count);
            bool cursorEnd = (mCursor >= mBounday.getString().Length);

            return (cursorEnd && lineEnd);
        }

        public void next()
        {
            if (isDone())
            {
                return;
            }

            // Arbitrary从Chapter创建，自带内容，每次先处理上次的数据

            while (true)
            {

                int max = CHAPTER_SIZE;
                int chapterSize = mBook.size();
                bool hasRetrieveLines = false;

                int wordCount = 0;
                Chapter c = new Chapter(mBook, 113);

                int start = mLineCursor;
                int end;

                // 循环内，至少添加一个标题直到结束
                while (true)
                {

                    end = mLines.Count;
                    for (int i = start; i < end; i++)
                    {
                        TextLine line = mLines[i];

                        int length = line.wordLength();
                        if (length == 0)
                        {
                            c.mList.Add(line);

                            continue;
                        }

                        wordCount += line.wordLength();
                        if (wordCount < max)
                        {
                            c.mList.Add(line);

                            continue;
                        }

                        {
                            String last = c.lastLine();
                            String current = line.getText();

                            bool force = Chapter.isForceConcat(current);
                            bool appendable = force ? force : Chapter.isAppendable(last);
                            bool concatable = force ? force : Chapter.isConcatable(current);
                            if (appendable && concatable)
                            {

                                c.mList.Add(line);

                            }
                            else
                            {
                                int remain = (mBounday.getString().Length - c.getEnd());

                                if (remain < MIN_SIZE)
                                { // 剩余内容太少，合并之
                                    c.mList.Add(line);
                                }
                                else
                                {

                                    mBook.mChapters.Add(c);

                                    c = null;
                                    wordCount = 0;
                                    mLineCursor = i; // 记录位置

                                    // 留给下一次来处理
                                    if (hasRetrieveLines)
                                    {
                                        break;
                                    }

                                    // 新起一章
                                    c = new Chapter(mBook, 113);
                                    c.mList.Add(line);
                                }
                            }

                        }
                    }

                    start = end;

                    // 全部结束了
                    if (endOfText())
                    {
                        break;
                    }

                    // 章节已发生变化，并且已更新过一次数据，留给下一次处理
                    if (chapterSize != mBook.size() && hasRetrieveLines)
                    {
                        break;
                    }

                    this.retrieveLines(STEP_CHARS);
                    hasRetrieveLines = true;
                }


                if (endOfText())
                {
                    if (c != null)
                    {
                        mBook.mChapters.Add(c);
                        mLineCursor = mLines.Count;
                    }
                }

                break;
            }

        }

        public bool endOfText()
        {
            return (mCursor >= mBounday.getString().Length);
        }

        public void retrieveLines(int step)
        {

            int start = mCursor;
            int end = start;
            int lineNum = mLines.Count;
            do
            {
                end += step;

                mBounday.set(start, end);
                this.retrieveLines(mBounday);

                // 如果已经移动到最后，结束之
                if (mBounday.isEnd())
                {
                    break;
                }

            } while (lineNum == mLines.Count); // 一定要找到新的分行

            // 更新指针位置
            if (mBounday.isEnd())
            {
                mCursor = mBounday.getEnd();
            }
            else
            {
                int last = mLines.Count - 1;
                mCursor = mLines[last].mEnd;
            }

            // 生成标题
            if (mCondition != null)
            {
                start = lineNum;
                end = mLines.Count;
                for (int i = start; i < end; i++)
                {
                    TextLine line = mLines[i];
                    mCondition.accept(line);
                }
            }
        }

        public void retrieveLines(BoundaryString text)
        {

            char separator = this.mSeparator;
            int sl = 1;

            int index = 0;
            if (mLines.Count > 0)
            {
                int last = mLines.Count - 1;
                index = mLines[last].mIndex;

                index++;
            }

            String str = text.getString();
            int begin = text.getStart();
            while (true)
            {
                int pos = str.IndexOf(separator, begin);

                if (pos >= 0 && pos < text.getEnd())
                {
                    TextLine line = new TextLine(mBook, begin, pos + sl, index);
                    mLines.Add(line);

                    begin = line.mEnd;

                    if (begin == text.getEnd())
                    {
                        break;
                    }

                }
                else
                {
                    if (text.isEnd())
                    {
                        TextLine line = new TextLine(mBook, begin, text.getEnd(), index);
                        mLines.Add(line);
                    }

                    break;
                }

                index++;
            }


        }


    }

    public class ConditionSet
    {

        public int mMin;
        public int mMax;

        public List<Condition> mList;

        public ConditionSet()
        {
            this.mMin = 2;
            this.mMax = 2;

            this.mList = new List<Condition>();
        }

        public void add(Condition c)
        {
            mList.Add(c);

            if (c.mMin > this.mMin)
            {
                this.mMin = c.mMin;
            }

            if (c.mMax > this.mMax)
            {
                this.mMax = c.mMax;
            }
        }

        public bool accept(TextLine line)
        {
            int length = line.contentLength();
            if (length > mMax || length < mMin)
            {
                return false;
            }

            foreach (Condition c in mList)
            {
                if (c.accept(line))
                {
                    return true;
                }
            }

            return false;
        }

        public static ConditionSet create(int length)
        {
            ConditionSet set = new ConditionSet();

            if (true)
            {
                Condition c = new Condition(
                        // 第
                        new char[] { '\u7b2c' },
                        // 章、回、节、集、卷、品
                        new char[] { '\u7ae0', '\u56de', '\u8282', '\u96c6', '\u5377', '\u54c1' },
                        12,
                        12,
                        3,
                        48,
                        32
                );

                set.add(c);
            }

            if (length <= 5 * 1024 * 1024)
            {
                Condition c = new Condition(
                        new char[] { '\u9644' },    // 附
                        new char[] { '\u5f55' },    // 录
                        12,
                        6,  // 对齐时，中间会插入字符
                        2,
                        48,
                        32
                );

                set.add(c);
            }

            if (length <= 5 * 1024 * 1024)
            {
                Condition c = new Condition(
                        new char[] { '\u6954' },    // 楔
                        new char[] { '\u5b50' },    // 子
                        12,
                        6,  // 对齐时，中间会插入字符
                        2,
                        48,
                        32
                );

                set.add(c);
            }

            return set;
        }
    }

    public class Condition
    {

        public char[] mStart;  // 起始关键字
        public char[] mEnd;    // 结束关键字

        public int mOffset;    // 起始关键字出现位置
        public int mLength;    // 结束关键字出现位置

        public int mMin;       // 最小长度
        public int mMax;       // 最大长度
        public int mCount;     // 文本有效长度

        public int[] mBounds;

        public Condition(char[] start, char[] end, int offset, int length, int min, int max, int count)
        {
            this.mStart = start;
            this.mEnd = end;

            this.mOffset = offset;
            this.mLength = length;

            this.mMin = min;
            this.mMax = max;
            this.mCount = count;

            this.mBounds = new int[2];
        }

        public bool accept(TextLine line)
        {
            BoundaryString text = line.mBook.mBoundary;

            int[] pos = this.mBounds;

            // 太长或太短，都不视为标题
            int length = line.contentLength();
            if (length > this.mMax || length < this.mMin)
            {
                return false;
            }

            // 去掉前置的空白，重新测量
            int start = line.getStart(true);
            int end;
            length = line.mContentLength - (start - line.mBegin);
            if (length > mCount || length < this.mMin)
            {
                return false;
            }

            // 开始关键字匹配
            for (int i = 0; i < mBounds.Length; i++)
            {
                mBounds[i] = -1;
            }
            // 开始位置
            {
                end = start + mOffset;
                end = (end > line.mEnd) ? line.mEnd : end; // 不允许越过边界

                text.set(start, end); // 限定搜索范围

                char[] chars = mStart;
                foreach (char c in chars)
                {
                    int index = text.indexOf(c);
                    if (index >= 0)
                    {

                        if (index == line.mBegin)
                        {
                            pos[0] = index;

                        }
                        else
                        {

                            char tmp = text.getString()[index - 1];
                            if (!ChapterBook.isChinese(tmp))
                            { // 第一个字符前，不允许再出现中文
                                pos[0] = index;
                            }

                        }

                        break;
                    }
                }
            }

            // 找不到开始位置，不视为标题
            if (pos[0] < 0)
            {
                return false;
            }

            // 结束位置
            {
                start = pos[0] + 1;

                end = start + mLength;
                end = (end > line.mEnd) ? line.mEnd : end; // 不允许越过边界

                text.set(start, end); // 限定搜索范围

                char[] chars = mEnd;
                foreach (char c in chars)
                {
                    int index = text.indexOf(c);
                    if (index >= 0)
                    {
                        pos[1] = index;
                        break;
                    }
                }
            }

            // 找不到结束位置，或者离得太远，不视为标题
            if (pos[1] < 0)
            {
                return false;
            }

            // 判断是否以中文结束
            if (true)
            {
                int index = line.getEnd(true);
                index -= 1;
                if (index >= 0 && index < text.getString().Length)
                {
                    char c = text.getString()[index];
                    // '\uff0c' --> '，'
                    // '\u3002' --> '。'

                    //                if (c < '\u4e00' || c > '\u9fa5') { // 中文范围
                    //                    return false;
                    //                }

                    // 不能接受'，'、'。'结尾的标题
                    if (c == '\uff0c' || c == '\u3002')
                    {
                        return false;
                    }

                }
            }

            line.mTitleStart = pos[0];
            line.mTitleEnd = pos[1] + 1;

            return true;
        }
    }

    [Serializable]
    public class LineInfo
    {
        public bool IsFromLeftToRight { get; set; }

        public bool IsFromTopToDown { get; set; }

        public string words { get; set; }

        public Rectangle rectangle { get; set; }

        public int AverageWidth { get; set; }

        public List<TextCellInfo> lstCell { get; set; }

        public bool isBreak { get; set; }

        public string lang { get; set; }

        public string separator { get; set; }

        public List<string> breakReason { get; set; } = new List<string>();

        public void Init()
        {
            words = words?.TrimEnd('\n') ?? string.Empty;
            rectangle = OcrLineProcess.GetMergeRectangle(this);
        }
    }
}
