using System.Text;
using UtfUnknown.Core.Models.MultiByte.Chinese;

namespace UtfUnknown.Core.Probers
{
    public class EscCharsetProber : CharsetProber
    {
        private readonly CodingStateMachine[] _codingSm;

        private int _activeSm;

        private string _detectedCharset;

        public EscCharsetProber()
        {
            _codingSm = new CodingStateMachine[4];
            _codingSm[0] = new CodingStateMachine(new HzGb2312SmModel());
            _codingSm[1] = new CodingStateMachine(new Iso2022CnSmModel());
            Reset();
        }

        public override void Reset()
        {
            state = ProbingState.Detecting;
            for (var i = 0; i < 4; i++) _codingSm[i].Reset();
            _activeSm = 4;
            _detectedCharset = null;
        }

        public override ProbingState HandleData(byte[] buf, int offset, int len)
        {
            var num = offset + len;
            for (var i = offset; i < num; i++)
            {
                if (state != 0) break;
                for (var num2 = _activeSm - 1; num2 >= 0; num2--)
                    switch (_codingSm[num2].NextState(buf[i]))
                    {
                        case 1:
                            _activeSm--;
                            if (_activeSm == 0)
                            {
                                state = ProbingState.NotMe;
                                return state;
                            }

                            if (num2 != _activeSm)
                            {
                                var codingStateMachine = _codingSm[_activeSm];
                                _codingSm[_activeSm] = _codingSm[num2];
                                _codingSm[num2] = codingStateMachine;
                            }

                            break;
                        case 2:
                            state = ProbingState.FoundIt;
                            _detectedCharset = _codingSm[num2].ModelName;
                            return state;
                    }
            }

            return state;
        }

        public override string GetCharsetName()
        {
            return _detectedCharset;
        }

        public override float GetConfidence(StringBuilder status = null)
        {
            return 0.99f;
        }
    }
}