using System.Collections.Generic;

namespace ExcelLibrary.BinaryFileFormat
{
	public class RichTextFormat
	{
		public List<ushort> CharIndexes;

		public List<ushort> FontIndexes;

		public RichTextFormat()
		{
			CharIndexes = new List<ushort>();
			FontIndexes = new List<ushort>();
		}

		public RichTextFormat(int numberOfFormattingRuns)
		{
			CharIndexes = new List<ushort>(numberOfFormattingRuns);
			FontIndexes = new List<ushort>(numberOfFormattingRuns);
		}
	}
}
