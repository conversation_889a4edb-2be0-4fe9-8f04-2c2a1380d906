using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class UpDownButtonEx : PictureBox
    {
        public delegate void TextChangeHandler(object sender, EventArgs e);

        //private DrawArea drawAreaT;

        private readonly IContainer components = null;

        private UpDownButton _down;

        private Panel _panel;

        private string _text;

        private TextboxEx _textBox1;

        private UpDownButton _up;

        public UpDownButtonEx()
        {
            SetStyle(ControlStyles.Selectable, true);
            InitializeComponent();
            _textBox1.Init(this);
        }

        public new string Text
        {
            get => _text;
            set
            {
                _text = value;
                if (_textBox1 != null) _textBox1.Text = _text;
            }
        }

        public new event TextChangeHandler TextChanged;

        public event TextChangeHandler TextMouseLeave;

        private void textBox1_TextChanged(object sender, EventArgs e)
        {
        }

        public void Change()
        {
            if (TextChanged != null) TextChanged(null, EventArgs.Empty);
        }

        public void Mouse()
        {
            if (TextMouseLeave != null) TextMouseLeave(null, EventArgs.Empty);
        }

        private void Up_Click(object sender, EventArgs e)
        {
            _textBox1.Text = (int.Parse(_textBox1.Text) + 1).ToString();
        }

        private void Down_Click(object sender, EventArgs e)
        {
            _textBox1.Text = (int.Parse(_textBox1.Text) - 1).ToString();
        }

        private void customButton1_Paint(object sender, PaintEventArgs e)
        {
            var graphics = e.Graphics;
            var clipRectangle = e.ClipRectangle;
            clipRectangle = new Rectangle(clipRectangle.X, clipRectangle.Y, clipRectangle.Width - 1,
                clipRectangle.Height - 1);
            var pen = new Pen(Color.Red, 1f);
            graphics.DrawRectangle(pen, clipRectangle);
            pen.Dispose();
        }

        private void textBox1_MouseLeave(object sender, EventArgs e)
        {
            Mouse();
        }

        private void panel_Paint(object sender, PaintEventArgs e)
        {
            ControlPaint.DrawBorder(e.Graphics, _panel.ClientRectangle, Color.FromArgb(192, 192, 192), 1,
                ButtonBorderStyle.Solid, Color.FromArgb(192, 192, 192), 1, ButtonBorderStyle.Solid,
                Color.FromArgb(192, 192, 192), 1, ButtonBorderStyle.Solid, Color.FromArgb(192, 192, 192), 1,
                ButtonBorderStyle.Solid);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null) components.Dispose();
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            _down = new UpDownButton();
            _up = new UpDownButton();
            _textBox1 = new TextboxEx();
            _panel = new Panel();
            _panel.SuspendLayout();
            ((ISupportInitialize)this).BeginInit();
            SuspendLayout();
            _down.FlatStyle = FlatStyle.Flat;
            _down.Font = new Font("微软雅黑", 9f);
            _down.IsBorder = true;
            _down.IsUp = false;
            _down.ListItems = null;
            _down.Location = new Point(28, 18);
            _down.Name = "_down";
            _down.Size = new Size(20, 10);
            _down.TabIndex = 2;
            _down.TabStop = false;
            _down.UseVisualStyleBackColor = true;
            _down.Click += Down_Click;
            _up.FlatStyle = FlatStyle.Flat;
            _up.Font = new Font("微软雅黑", 9f);
            _up.IsBorder = true;
            _up.IsUp = true;
            _up.ListItems = null;
            _up.Location = new Point(28, 7);
            _up.Name = "_up";
            _up.Size = new Size(20, 10);
            _up.TabIndex = 1;
            _up.TabStop = false;
            _up.UseVisualStyleBackColor = true;
            _up.Click += Up_Click;
            _up.Paint += customButton1_Paint;
            _textBox1.BackColor = Color.White;
            _textBox1.BorderStyle = BorderStyle.None;
            _textBox1.Dock = DockStyle.Fill;
            _textBox1.Font = new Font("微软雅黑", 9f);
            _textBox1.Location = new Point(1, 1);
            _textBox1.Multiline = true;
            _textBox1.Name = "_textBox1";
            _textBox1.Size = new Size(16, 19);
            _textBox1.TabIndex = 0;
            _textBox1.TabStop = false;
            _textBox1.Text = "1";
            _textBox1.TextChanged += textBox1_TextChanged;
            _textBox1.MouseLeave += textBox1_MouseLeave;
            _panel.BackColor = Color.White;
            _panel.Controls.Add(_textBox1);
            _panel.Location = new Point(8, 7);
            _panel.Name = "_panel";
            _panel.Padding = new Padding(1);
            _panel.Size = new Size(18, 21);
            _panel.TabIndex = 3;
            _panel.Paint += panel_Paint;
            BackColor = Color.White;
            Controls.Add(_down);
            Controls.Add(_up);
            Controls.Add(_panel);
            MaximumSize = new Size(50, 35);
            MinimumSize = new Size(50, 35);
            Size = new Size(50, 35);
            _panel.ResumeLayout(false);
            _panel.PerformLayout();
            ((ISupportInitialize)this).EndInit();
            ResumeLayout(false);
        }
    }
}