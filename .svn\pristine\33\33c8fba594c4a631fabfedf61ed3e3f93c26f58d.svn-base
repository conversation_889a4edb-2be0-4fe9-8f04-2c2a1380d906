// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using System.Runtime.InteropServices;
using UIAComWrapperInternal;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class TextChildPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = TextChildPatternIdentifiers.Pattern;
        private readonly IUIAutomationTextChildPattern _pattern;

        private TextChildPattern(AutomationElement el, IUIAutomationTextChildPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
            _pattern = pattern;
        }

        public AutomationElement TextContainer
        {
            get
            {
                try
                {
                    return AutomationElement.Wrap(_pattern.TextContainer);
                }
                catch (COMException e)
                {
                    Exception newEx;
                    if (Utility.ConvertException(e, out newEx))
                        throw newEx;
                    throw;
                }
            }
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new TextChildPattern(el, (IUIAutomationTextChildPattern) pattern, cached);
        }
    }
}