using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolStep : ToolObject
    {
        private DrawStep drawStep;

        public string textlist;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.current_ToolType = drawArea.ActiveTool;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else if (drawArea.GraphicsList.Count < 99 && drawArea.StepNum < 99)
            {
                drawStep = new DrawStep(e.X, e.Y, drawArea);
                AddNewObject(drawArea, drawStep);
                var num = 0;
                foreach (var graphics in drawArea.GraphicsList.graphicsList)
                    if (graphics.NoteType == DrawToolType.Step)
                        num++;
                if (num == 1) drawArea.StepNum = 0;
                drawArea.StepNum++;
                var num2 = drawArea.NumberArry[drawArea.StepNum - 1];
                drawStep.Text = num2.ToString();
            }
        }

        protected new void AddNewObject(DrawArea drawArea, DrawObject o)
        {
            drawArea.GraphicsList.UnselectAll();
            drawArea.GraphicsList.Add(o);
            drawArea.Capture = true;
            o.Selected = true;
            drawArea.Refresh();
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left)
            {
                if (drawStep == null)
                    drawArea.ActiveTool = DrawToolType.Text;
                else
                    drawStep.IsSelected = true;
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawStep != null)
            {
                drawArea.upDownButtonEx.Text = drawStep.Text;
                StaticValue.current_Rectangle = drawStep.Rectangle;
                if (!drawStep.Rectangle.IsLimt())
                {
                    drawArea.GraphicsList.RemoveAt(0);
                    return;
                }

                var obj = drawStep;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    drawStep.Normalize();
                    drawArea.AddCommandToHistory(new CommandAdd(drawStep));
                }
            }
        }
    }
}