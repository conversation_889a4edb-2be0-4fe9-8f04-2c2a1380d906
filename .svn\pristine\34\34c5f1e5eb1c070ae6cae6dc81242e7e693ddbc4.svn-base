﻿using System;
using System.Reflection;

namespace OCRTools.Common.Entity
{
    [Obfuscation]
    public class UserEntity
    {
        [Obfuscation] public bool IsLogined => !string.IsNullOrEmpty(Account) && !string.IsNullOrEmpty(Token);
        //True|***********|普通会员|***********|2019-09-26 15:53:15|2020-09-26 15:53:15|

        [Obfuscation] public string Account { get; set; }

        [Obfuscation] public string NickName { get; set; }

        [Obfuscation] public int UserType { get; set; }

        [Obfuscation] public string UserTypeName { get; set; }

        [Obfuscation] public string Token { get; set; }

        [Obfuscation] public DateTime DtLogin { get; set; }

        [Obfuscation] public string Remark { get; set; }

        [Obfuscation] public DateTime DtExpired { get; set; }

        [Obfuscation] public DateTime DtReg { get; set; }

        /// <summary>
        ///是否显示其他处理结果
        /// </summary>
        [Obfuscation]
        public bool IsSetOtherResult { get; set; }

        /// <summary>
        ///是否支持文本识别
        /// </summary>
        [Obfuscation]
        public bool IsSupportTxt { get; set; } = true;

        /// <summary>
        ///是否支持文档翻译
        /// </summary>
        [Obfuscation]
        public bool IsSupportDocFile { get; set; }

        /// <summary>
        ///是否支持图片翻译
        /// </summary>
        [Obfuscation]
        public bool IsSupportTranslate { get; set; }

        /// <summary>
        ///是否支持图片文件识别
        /// </summary>
        [Obfuscation]
        public bool IsSupportImageFile { get; set; }

        /// <summary>
        ///能否批量处理文件
        /// </summary>
        [Obfuscation]
        public bool IsSupportBatch { get; set; }

        /// <summary>
        ///是否支持竖排识别
        /// </summary>
        [Obfuscation]
        public bool IsSupportVertical { get; set; }

        /// <summary>
        ///是否支持数学公式识别
        /// </summary>
        [Obfuscation]
        public bool IsSupportMath { get; set; }

        /// <summary>
        ///是否支持表格识别
        /// </summary>
        [Obfuscation]
        public bool IsSupportTable { get; set; }

        /// <summary>
        /// 是否支持通道切换
        /// </summary>
        [Obfuscation]
        public bool IsSupportPassage { get; set; }

        /// <summary>
        ///最大可上传的识别文件
        /// </summary>
        [Obfuscation]
        public int MaxUploadSize { get; set; } = 100;

        /// <summary>
        /// 指定时间段内执行次数
        /// </summary>
        [Obfuscation]
        public int PerTimeSpanExecCount { get; set; } = 1;

        /// <summary>
        /// 指定时间段内（毫秒）
        /// </summary>
        [Obfuscation]
        public int PerTimeSpan { get; set; } = 30000;

        /// <summary>
        /// 是否支持本地OCR识别
        /// </summary>
        [Obfuscation]
        public bool IsSupportLocalOcr { get; set; }
    }
}