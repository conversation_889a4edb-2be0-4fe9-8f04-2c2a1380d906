// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Runtime.InteropServices;

namespace System.Windows
{
    [Serializable]
    [StructLayout(LayoutKind.Sequential)]
    public struct Rect : IFormattable
    {
        internal double _x;
        internal double _y;
        internal double _width;
        internal double _height;

        public static bool operator ==(Rect rect1, Rect rect2)
        {
            return rect1.X == rect2.X && rect1.Y == rect2.Y && rect1.Width == rect2.Width &&
                   rect1.Height == rect2.Height;
        }

        public static bool operator !=(Rect rect1, Rect rect2)
        {
            return !(rect1 == rect2);
        }

        public static bool Equals(Rect rect1, Rect rect2)
        {
            if (rect1.IsEmpty) return rect2.IsEmpty;
            return rect1.X.Equals(rect2.X) && rect1.Y.Equals(rect2.Y) && rect1.Width.Equals(rect2.Width) &&
                   rect1.Height.Equals(rect2.Height);
        }

        public override bool Equals(object o)
        {
            if (o == null || !(o is Rect)) return false;
            var rect = (Rect) o;
            return Equals(this, rect);
        }

        public bool Equals(Rect value)
        {
            return Equals(this, value);
        }

        public override int GetHashCode()
        {
            if (IsEmpty) return 0;
            return X.GetHashCode() ^ Y.GetHashCode() ^ Width.GetHashCode() ^ Height.GetHashCode();
        }

        public Rect(Point location, Size size)
        {
            if (size.IsEmpty)
            {
                this = Empty;
            }
            else
            {
                _x = location._x;
                _y = location._y;
                _width = size._width;
                _height = size._height;
            }
        }

        public Rect(double x, double y, double width, double height)
        {
            if (width < 0.0 || height < 0.0) throw new ArgumentException("Size_WidthAndHeightCannotBeNegative");
            _x = x;
            _y = y;
            _width = width;
            _height = height;
        }

        public Rect(Point point1, Point point2)
        {
            _x = Math.Min(point1._x, point2._x);
            _y = Math.Min(point1._y, point2._y);
            _width = Math.Max(Math.Max(point1._x, point2._x) - _x, 0.0);
            _height = Math.Max(Math.Max(point1._y, point2._y) - _y, 0.0);
        }

        public Rect(Size size)
        {
            if (size.IsEmpty)
            {
                this = Empty;
            }
            else
            {
                _x = _y = 0.0;
                _width = size.Width;
                _height = size.Height;
            }
        }

        public static Rect Empty { get; } = CreateEmptyRect();

        public bool IsEmpty => _width < 0.0;

        public Point Location
        {
            get => new Point(_x, _y);
            set
            {
                if (IsEmpty) throw new InvalidOperationException("Rect_CannotModifyEmptyRect");
                _x = value._x;
                _y = value._y;
            }
        }

        public Size Size
        {
            get
            {
                if (IsEmpty) return Size.Empty;
                return new Size(_width, _height);
            }
            set
            {
                if (value.IsEmpty)
                {
                    this = Empty;
                }
                else
                {
                    if (IsEmpty) throw new InvalidOperationException("Rect_CannotModifyEmptyRect");
                    _width = value._width;
                    _height = value._height;
                }
            }
        }

        public double X
        {
            get => _x;
            set
            {
                if (IsEmpty) throw new InvalidOperationException("Rect_CannotModifyEmptyRect");
                _x = value;
            }
        }

        public double Y
        {
            get => _y;
            set
            {
                if (IsEmpty) throw new InvalidOperationException("Rect_CannotModifyEmptyRect");
                _y = value;
            }
        }

        public double Width
        {
            get => _width;
            set
            {
                if (IsEmpty) throw new InvalidOperationException("Rect_CannotModifyEmptyRect");
                if (value < 0.0) throw new ArgumentException("Size_WidthCannotBeNegative");
                _width = value;
            }
        }

        public double Height
        {
            get => _height;
            set
            {
                if (IsEmpty) throw new InvalidOperationException("Rect_CannotModifyEmptyRect");
                if (value < 0.0) throw new ArgumentException("Size_HeightCannotBeNegative");
                _height = value;
            }
        }

        public double Left => _x;
        public double Top => _y;

        public double Right
        {
            get
            {
                if (IsEmpty) return double.NegativeInfinity;
                return _x + _width;
            }
        }

        public double Bottom
        {
            get
            {
                if (IsEmpty) return double.NegativeInfinity;
                return _y + _height;
            }
        }

        public Point TopLeft => new Point(Left, Top);
        public Point TopRight => new Point(Right, Top);
        public Point BottomLeft => new Point(Left, Bottom);
        public Point BottomRight => new Point(Right, Bottom);

        public bool Contains(Point point)
        {
            return Contains(point._x, point._y);
        }

        public bool Contains(double x, double y)
        {
            if (IsEmpty) return false;
            return ContainsInternal(x, y);
        }

        public bool Contains(Rect rect)
        {
            if (IsEmpty || rect.IsEmpty) return false;
            return _x <= rect._x && _y <= rect._y && _x + _width >= rect._x + rect._width &&
                   _y + _height >= rect._y + rect._height;
        }

        public bool IntersectsWith(Rect rect)
        {
            if (IsEmpty || rect.IsEmpty) return false;
            return rect.Left <= Right && rect.Right >= Left && rect.Top <= Bottom && rect.Bottom >= Top;
        }

        public void Intersect(Rect rect)
        {
            if (!IntersectsWith(rect))
            {
                this = Empty;
            }
            else
            {
                var num2 = Math.Max(Left, rect.Left);
                var num = Math.Max(Top, rect.Top);
                _width = Math.Max(Math.Min(Right, rect.Right) - num2, 0.0);
                _height = Math.Max(Math.Min(Bottom, rect.Bottom) - num, 0.0);
                _x = num2;
                _y = num;
            }
        }

        public static Rect Intersect(Rect rect1, Rect rect2)
        {
            rect1.Intersect(rect2);
            return rect1;
        }

        public void Union(Rect rect)
        {
            if (IsEmpty)
            {
                this = rect;
            }
            else if (!rect.IsEmpty)
            {
                var num2 = Math.Min(Left, rect.Left);
                var num = Math.Min(Top, rect.Top);
                if (rect.Width == double.PositiveInfinity || Width == double.PositiveInfinity)
                {
                    _width = double.PositiveInfinity;
                }
                else
                {
                    var num4 = Math.Max(Right, rect.Right);
                    _width = Math.Max(num4 - num2, 0.0);
                }

                if (rect.Height == double.PositiveInfinity || Height == double.PositiveInfinity)
                {
                    _height = double.PositiveInfinity;
                }
                else
                {
                    var num3 = Math.Max(Bottom, rect.Bottom);
                    _height = Math.Max(num3 - num, 0.0);
                }

                _x = num2;
                _y = num;
            }
        }

        public static Rect Union(Rect rect1, Rect rect2)
        {
            rect1.Union(rect2);
            return rect1;
        }

        public void Union(Point point)
        {
            Union(new Rect(point, point));
        }

        public static Rect Union(Rect rect, Point point)
        {
            rect.Union(new Rect(point, point));
            return rect;
        }

        public void Offset(double offsetX, double offsetY)
        {
            if (IsEmpty) throw new InvalidOperationException("Rect_CannotCallMethod");
            _x += offsetX;
            _y += offsetY;
        }

        public static Rect Offset(Rect rect, double offsetX, double offsetY)
        {
            rect.Offset(offsetX, offsetY);
            return rect;
        }

        public void Inflate(Size size)
        {
            Inflate(size._width, size._height);
        }

        public void Inflate(double width, double height)
        {
            if (IsEmpty) throw new InvalidOperationException("Rect_CannotCallMethod");
            _x -= width;
            _y -= height;
            _width += width;
            _width += width;
            _height += height;
            _height += height;
            if (_width < 0.0 || _height < 0.0) this = Empty;
        }

        public static Rect Inflate(Rect rect, Size size)
        {
            rect.Inflate(size._width, size._height);
            return rect;
        }

        public static Rect Inflate(Rect rect, double width, double height)
        {
            rect.Inflate(width, height);
            return rect;
        }

        public void Scale(double scaleX, double scaleY)
        {
            if (!IsEmpty)
            {
                _x *= scaleX;
                _y *= scaleY;
                _width *= scaleX;
                _height *= scaleY;
                if (scaleX < 0.0)
                {
                    _x += _width;
                    _width *= -1.0;
                }

                if (scaleY < 0.0)
                {
                    _y += _height;
                    _height *= -1.0;
                }
            }
        }

        private bool ContainsInternal(double x, double y)
        {
            return x >= _x && x - _width <= _x && y >= _y && y - _height <= _y;
        }

        public override string ToString()
        {
            return ConvertToString(null, null);
        }

        public string ToString(IFormatProvider provider)
        {
            return ConvertToString(null, provider);
        }

        string IFormattable.ToString(string format, IFormatProvider provider)
        {
            return ConvertToString(format, provider);
        }

        internal string ConvertToString(string format, IFormatProvider provider)
        {
            if (IsEmpty) return "Empty";

            var numericListSeparator = ',';
            return string.Format(provider,
                "{1:" + format + "}{0}{2:" + format + "}{0}{3:" + format + "}{0}{4:" + format + "}",
                numericListSeparator,
                _x,
                _y,
                _width,
                _height);
        }

        private static Rect CreateEmptyRect()
        {
            var rect = new Rect
            {
                _x = double.PositiveInfinity,
                _y = double.PositiveInfinity,
                _width = double.NegativeInfinity,
                _height = double.NegativeInfinity
            };
            return rect;
        }
    }
}