﻿using System;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace OCRTools
{
    internal class MouseHook
    {
        private const int WM_MOUSEMOVE = 0x200;
        private const int WM_LBUTTONDOWN = 0x201;
        private const int WM_RBUTTONDOWN = 0x204;
        private const int WM_MBUTTONDOWN = 0x207;
        private const int WM_LBUTTONUP = 0x202;
        private const int WM_RBUTTONUP = 0x205;
        private const int WM_MBUTTONUP = 0x208;
        private const int WM_LBUTTONDBLCLK = 0x203;
        private const int WM_RBUTTONDBLCLK = 0x206;
        private const int WM_MBUTTONDBLCLK = 0x209;

        public event MouseEventHandler MouseSelected;

        public event EventHandler DoubleClick;

        static int hMouseHook = 0;

        public const int WH_MOUSE_LL = 14;

        HookProc MouseHookProcedure;

        [StructLayout(LayoutKind.Sequential)]
        public class POINT
        {
            public int x;
            public int y;
        }

        [DllImport("user32.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.StdCall)]
        public static extern int SetWindowsHookEx(int idHook, HookProc lpfn, IntPtr hInstance, int threadId);

        [DllImport("kernel32.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.StdCall)]
        public static extern int GetLastError();

        [DllImport("kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr GetModuleHandle(string lpModuleName);

        [DllImport("user32.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.StdCall)]
        public static extern bool UnhookWindowsHookEx(int idHook);

        [DllImport("user32.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.StdCall)]
        public static extern int CallNextHookEx(int idHook, int nCode, Int32 wParam, IntPtr lParam);

        public delegate int HookProc(int nCode, Int32 wParam, IntPtr lParam);

        [DllImport("user32.dll")]
        public static extern int GetDoubleClickTime();


        public MouseHook()
        {
        }

        ~MouseHook()
        {
            Stop();
        }

        public void Start()
        {
            if (hMouseHook == 0)
            {
                MouseHookProcedure = new HookProc(MouseHookProc);
                hMouseHook = SetWindowsHookEx(WH_MOUSE_LL, MouseHookProcedure, GetModuleHandle("user32"), 0);

                if (hMouseHook == 0)
                {
                    int errorCode = GetLastError();
                    Stop();
                }
            }
        }

        public void Stop()
        {
            bool retMouse = true;
            if (hMouseHook != 0)
            {
                retMouse = UnhookWindowsHookEx(hMouseHook);
                hMouseHook = 0;
            }

            if (!(retMouse))
            {
            }
        }
        private enum MouseEventType
        {
            None,
            MouseDown,
            MouseUp,
            DoubleClick,
            MouseWheel,
            MouseMove
        }

        [StructLayout(LayoutKind.Sequential)]
        protected class MouseLLHookStruct
        {
            public POINT pt;

            public int mouseData;

            public int flags;

            public int time;

            public int dwExtraInfo;
        }

        private bool isDown;
        private bool isMove;
        DateTime lastClickTime;
        int clickCount;

        private int MouseHookProc(int nCode, int wParam, IntPtr lParam)
        {
            if ((nCode >= 0) && (MouseSelected != null || DoubleClick != null))
            {
                try
                {
                    MouseLLHookStruct mouseLLHookStruct = (MouseLLHookStruct)Marshal.PtrToStructure(lParam, typeof(MouseLLHookStruct));
                    MouseButtons button = GetButton(wParam);
                    MouseEventType mouseEventType = GetEventType(wParam);
                    MouseEventArgs e = new MouseEventArgs(button, (mouseEventType != MouseEventType.DoubleClick) ? 1 : 2, mouseLLHookStruct.pt.x, mouseLLHookStruct.pt.y, (mouseEventType == MouseEventType.MouseWheel) ? ((short)((mouseLLHookStruct.mouseData >> 16) & 0xFFFF)) : 0);
                    if (button == MouseButtons.Right && mouseLLHookStruct.flags != 0)
                    {
                        mouseEventType = MouseEventType.None;
                    }
                    bool isDoubleClick = false;
                    switch (mouseEventType)
                    {
                        case MouseEventType.MouseDown:
                            isDown = button == MouseButtons.Left;
                            break;
                        case MouseEventType.MouseMove:
                            clickCount = 0;
                            isMove = isDown;
                            break;
                        case MouseEventType.MouseUp:
                            if (e.Button == MouseButtons.Left && isDown && isMove)
                            {
                                isMove = false;
                                isDown = false;
                                this.MouseSelected?.Invoke(this, e);
                            }
                            break;
                        case MouseEventType.DoubleClick:
                            isDoubleClick = true;
                            this.DoubleClick?.Invoke(this, new EventArgs());
                            break;
                        case MouseEventType.MouseWheel:
                            break;
                    }
                    if (mouseEventType == MouseEventType.MouseUp && !isDoubleClick)
                    {
                        var deltaMs = DateTime.Now - lastClickTime;
                        lastClickTime = DateTime.Now;

                        if (deltaMs.TotalMilliseconds <= GetDoubleClickTime())
                        {
                            clickCount++;
                        }
                        else
                        {
                            clickCount = 1;
                        }
                        if (clickCount == 2)
                        {
                            clickCount = 0;
                            DoubleClick?.Invoke(this, new EventArgs());
                        }
                    }
                }
                catch { }
            }
            return CallNextHookEx(hMouseHook, nCode, wParam, lParam);
        }

        private MouseButtons GetButton(int wParam)
        {
            switch (wParam)
            {
                case 513:
                case 514:
                case 515:
                    return MouseButtons.Left;
                case 516:
                case 517:
                case 518:
                    return MouseButtons.Right;
                case 519:
                case 520:
                case 521:
                    return MouseButtons.Middle;
                default:
                    return MouseButtons.None;
            }
        }

        private MouseEventType GetEventType(int wParam)
        {
            switch (wParam)
            {
                case 513:
                case 516:
                case 519:
                    return MouseEventType.MouseDown;
                case 514:
                case 517:
                case 520:
                    return MouseEventType.MouseUp;
                case 515:
                case 518:
                case 521:
                    return MouseEventType.DoubleClick;
                case 522:
                    return MouseEventType.MouseWheel;
                case 512:
                    return MouseEventType.MouseMove;
                default:
                    return MouseEventType.None;
            }
        }
    }
}
