﻿using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Globalization;
using System.IO;
using System.Net;
using System.Text;

namespace OCRTools.Common
{
    public class UploadFileInfo
    {
        public UploadFileInfo()
        {
            ContentType = "application/octet-stream";
        }

        public string Name { get; set; }
        public string Filename { get; set; }
        public string ContentType { get; set; }
        public Stream Stream { get; set; }
    }

    public class UploadFileRequest
    {
        public static string PostFile(string url, byte[] content, string fileExt, NameValueCollection values = null,
            NameValueCollection headers = null)
        {
            var result = "";
            try
            {
                url = CommonString.HostCode?.FullUrl + url;
                var file = content == null || content.Length <= 0
                    ? null
                    : new UploadFileInfo
                    {
                        Name = "image",
                        Filename = "1." + fileExt,
                        ContentType = "image/png",
                        Stream = new MemoryStream(content)
                    };
                result = Post(url, file == null ? null : new[] { file }, values, headers);
            }
            catch (Exception)
            {
            }

            return result;
        }

        public static string Post(string url, IEnumerable<UploadFileInfo> files, NameValueCollection values,
            NameValueCollection headers = null)
        {
            var result = "";
            try
            {
                var resultByt = PostByte(url, files, values, headers);
                if (resultByt != null && resultByt.Length > 0)
                    result = Encoding.UTF8.GetString(resultByt);
            }
            catch (Exception)
            {
            }

            return result;
        }

        private static byte[] PostByte(string url, IEnumerable<UploadFileInfo> files, NameValueCollection values,
            NameValueCollection headers = null)
        {
            byte[] result = null;
            var address = new Uri(url, false);
            var host = address.Host;
            var selfHost = CommonString.IsSelfHost(host);
            if (!Equals(selfHost, SiteType.Default))
            {
                address = CommonString.SetAddress(address, selfHost);
            }

            var request = WebRequest.Create(address) as HttpWebRequest;
            if (selfHost != 0)
            {
                CommonString.SetUserAgent(request);
                request.Host = host;
            }
            else
            {
                request.UserAgent = CommonString.StrDefaultAgent;
            }
            try
            {
                request.Method = "POST";
                request.ReadWriteTimeout = CommonMethod.ExecTimeOutSecond * 1000;
                request.Timeout = CommonMethod.ExecTimeOutSecond * 1000;
                var boundary = "---------------------------" +
                               ServerTime.DateTime.Ticks.ToString("x", NumberFormatInfo.InvariantInfo);
                request.ContentType = "multipart/form-data; boundary=" + boundary;
                request.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;
                //request.Referer = url;
                boundary = "--" + boundary;
                request.KeepAlive = true;
                if (headers != null)
                    foreach (string name in headers.Keys)
                        request.Headers.Add(name, headers[name]);
                using (var requestStream = request.GetRequestStream())
                {
                    if (values != null)
                        // Write the values
                        foreach (string name in values.Keys)
                        {
                            var buffer = Encoding.ASCII.GetBytes(boundary + Environment.NewLine);
                            requestStream.Write(buffer, 0, buffer.Length);
                            buffer = Encoding.ASCII.GetBytes(string.Format(
                                "Content-Disposition: form-data; name=\"{0}\"{1}{1}", name, Environment.NewLine));
                            requestStream.Write(buffer, 0, buffer.Length);
                            buffer = Encoding.UTF8.GetBytes(values[name] + Environment.NewLine);
                            requestStream.Write(buffer, 0, buffer.Length);
                        }

                    // Write the files
                    if (files != null)
                        foreach (var file in files)
                        {
                            if (file == null) continue;
                            var buffer = Encoding.ASCII.GetBytes(boundary + Environment.NewLine);
                            requestStream.Write(buffer, 0, buffer.Length);

                            buffer = Encoding.UTF8.GetBytes(string.Format(
                                "Content-Disposition: form-data; name=\"{0}\"; filename=\"{1}\"{2}", file.Name,
                                file.Filename, Environment.NewLine));
                            requestStream.Write(buffer, 0, buffer.Length);

                            buffer = Encoding.ASCII.GetBytes(string.Format("Content-Type: {0}{1}{1}", file.ContentType,
                                Environment.NewLine));
                            requestStream.Write(buffer, 0, buffer.Length);

                            //buffer = Encoding.ASCII.GetBytes(string.Format("Content-Type: {0}{1}", file.ContentType, Environment.NewLine));
                            //requestStream.Write(buffer, 0, buffer.Length);

                            file.Stream.CopyTo(requestStream);
                            buffer = Encoding.ASCII.GetBytes(Environment.NewLine);
                            requestStream.Write(buffer, 0, buffer.Length);
                        }

                    var boundaryBuffer = Encoding.ASCII.GetBytes(boundary + "--");
                    requestStream.Write(boundaryBuffer, 0, boundaryBuffer.Length);
                }

                using (var response = request.GetResponse())
                using (var responseStream = response.GetResponseStream())
                {
                    if (responseStream != null)
                    {
                        using (var stream = new MemoryStream())
                        {
                            responseStream.CopyTo(stream);
                            result = stream.ToArray();
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                //log4net.LogManager.GetLogger("Order").Error(oe);
                if (oe is WebException exception)
                {
                    var response = exception.Response as HttpWebResponse;
                    try
                    {
                        if (response != null)
                        {
                            switch (response.StatusCode)
                            {
                                case HttpStatusCode.NotFound: //404
                                    DnsHelper.ReportError(request.Host, request.Address.Host);
                                    break;
                            }
                        }
                        else if (!string.IsNullOrEmpty(exception.Message))
                        {
                            if (exception.Message.Contains("(404)"))
                            {
                                DnsHelper.ReportError(request.Host, request.Address.Host);
                            }
                        }
                    }
                    catch { }
                    finally
                    {
                        try
                        {
                            response?.Close();
                        }
                        catch
                        {
                        }
                    }
                }
                else if (!string.IsNullOrEmpty(oe.Message))
                {
                    if (oe.Message.Contains("(404)"))
                    {
                        DnsHelper.ReportError(request.Host, request.Address.Host);
                    }
                }
            }

            return result;
        }
    }
}