using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;

[assembly: AssemblyTitle("OcrLib")]
[assembly: AssemblyDescription("OCR助手本地识别服务(2023-01-03)")]
[assembly: AssemblyProduct("OCR助手本地识别服务")]
[assembly: AssemblyCopyright("版权 © 2019-2023 SmartOldfish")]
[assembly: ComVisible(false)]
[assembly: Guid("ab10d356-2d95-4460-96ca-9a7129ad0a76")]
[assembly: AssemblyVersion("1.4.0")]
[assembly: AssemblyFileVersion("1.4.0")]
