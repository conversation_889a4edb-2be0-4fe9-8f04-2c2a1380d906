using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class SubRecord : Record
	{
		public SubRecord()
		{
		}

		public SubRecord(SubRecord record)
			: base(record)
		{
		}

		public new static SubRecord ReadBase(Stream stream)
		{
			BinaryReader binaryReader = new BinaryReader(stream);
			SubRecord subRecord = new SubRecord();
			subRecord.Type = binaryReader.ReadUInt16();
			subRecord.Size = binaryReader.ReadUInt16();
			subRecord.Data = binaryReader.ReadBytes(subRecord.Size);
			return subRecord;
		}

		public new static SubRecord Read(Stream stream)
		{
			SubRecord subRecord = ReadBase(stream);
			switch (subRecord.Type)
			{
			case 21:
				return new CommonObjectData(subRecord);
			case 0:
				return new End(subRecord);
			case 6:
				return new GroupMarker(subRecord);
			case 7:
				return new ClipboardFormat(subRecord);
			case 8:
				return new PictureOption(subRecord);
			default:
				return subRecord;
			}
		}
	}
}
