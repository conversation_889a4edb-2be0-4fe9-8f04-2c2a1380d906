using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolArrow : ToolObject
    {
        private DrawArrow _drawArrow;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.CurrentToolType = drawArea.ActiveTool;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else
            {
                _drawArrow = new DrawArrow(e.X, e.Y, e.X + 1, e.Y + 1);
                AddNewObject(drawArea, _drawArrow);
            }
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left)
            {
                if (_drawArrow == null)
                {
                    drawArea.ActiveTool = DrawToolType.Text;
                    return;
                }

                _drawArrow.IsSelected = true;
                var obj = _drawArrow;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    _drawArrow.MoveHandleTo(e.Location, 2, drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (_drawArrow != null)
            {
                StaticValue.CurrentRectangle = _drawArrow.Rectangle;
                if (!_drawArrow.Rectangle.IsLimt())
                {
                    drawArea.GraphicsList.RemoveAt(0);
                    return;
                }

                var obj = _drawArrow;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    _drawArrow.Normalize();
                    drawArea.AddCommandToHistory(new CommandAdd(_drawArrow));
                }
            }
        }
    }
}