﻿using OCRTools.Common;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class UCPicView : UserControl
    {
        public UCPicView()
        {
            InitializeComponent();
            imageBox.ZoomChanged += ImageBox_ZoomChanged;
            imageBox.MouseDoubleClick += ImageBox_MouseDoubleClick;
        }

        public Image OriginImage => imageBox.Image;

        public int Zoom
        {
            get => imageBox.Zoom;
            set => imageBox.Zoom = value;
        }

        private void ImageBox_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            var form = FindForm();
            if (form.WindowState == FormWindowState.Normal)
                form.WindowState = FormWindowState.Maximized;
            else
                form.WindowState = FormWindowState.Normal;
        }

        public void HideButton()
        {
            tsmFullScreen.Visible = true;
            //tsbReOcr.Visible = false;
            tsmPicView.Visible = false;
        }

        public bool ShowOrHideToolBox()
        {
            toolPicOperate.Visible = !toolPicOperate.Visible;
            return toolPicOperate.Visible;
        }

        public void ShowToolBox()
        {
            toolPicOperate.Visible = false;
        }

        public void HideToolBox()
        {
            toolPicOperate.Visible = false;
        }

        public void SetPicImage(Image img, string imgUrl = null, bool isAutoGetLine = false)
        {
            imageBox.OriginImage = img;
            imageBox.ZoomToFit();
            imageBox.Tag = imgUrl;
            UCPicView_SizeChanged(null, null);
            if (imageBox.Zoom > 100) imageBox.Zoom = 100;
            //if (isAutoGetLine)
            //{
            //    InitLines();
            //}
        }

        public void SetPicImageUrl(string imgUrl)
        {
            imageBox.Image.Tag = imgUrl;
        }

        public void OCR()
        {
            imageBox.Image?.Ocr();
        }

        private void tsbReOcr_Click(object sender, EventArgs e)
        {
            OCR();
        }

        private void ImageBox_ZoomChanged(object sender, EventArgs e)
        {
            txtPicZoomPercent.Text = string.Format("{0}%", imageBox.Zoom);
        }

        private void tsmPicView_Click(object sender, EventArgs e)
        {
            this.ViewImage(imageBox.Image);
        }

        private void tsmPicOrigin_Click(object sender, EventArgs e)
        {
            imageBox.Zoom = 100;
        }

        private void tsmPicSmall_Click(object sender, EventArgs e)
        {
            imageBox.Zoom -= imageBox.ZoomIncrement;
        }

        private void tsmPicBig_Click(object sender, EventArgs e)
        {
            imageBox.Zoom += imageBox.ZoomIncrement;
        }

        private void tsbSaveImg_Click(object sender, EventArgs e)
        {
            if (imageBox.Image != null) SaveFile(imageBox.Image);
        }

        public void SaveFile(Image bmp = null)
        {
            if (bmp == null) bmp = imageBox.Image;
            bmp.SaveFile(this);
        }

        private void tsmPicType_DropDownItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            var item = e.ClickedItem;
            if (item == null || item.Tag == null) return;
            if (imageBox.OriginImage != null)
            {
                tsmPicType.Text = item.Text;
                var imgType = (ImageProcessType) BoxUtil.GetInt32FromObject(item.Tag?.ToString());
                if (imgType == ImageProcessType.原始图片)
                    imageBox.Image = imageBox.OriginImage;
                else
                    try
                    {
                        imageBox.Image = ImageProcessHelper.ProcessImage(new Bitmap(imageBox.OriginImage), imgType);
                        imageBox.Tag = null;
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
            }
        }

        private void UCPicView_SizeChanged(object sender, EventArgs e)
        {
            toolPicOperate.Location = new Point((int) ((ClientRectangle.Width - toolPicOperate.Width) * 1.0 / 2)
                , ClientRectangle.Height - toolPicOperate.Height - (imageBox.HorizontalScroll.Visible ? SystemInformation.HorizontalScrollBarHeight : 0) - 15);
        }

        private void tsmFullScreen_Click(object sender, EventArgs e)
        {
            var form = FindForm();
            if (form == null) return;
            if (form.WindowState == FormWindowState.Normal)
                form.WindowState = FormWindowState.Maximized;
            else
                form.WindowState = FormWindowState.Normal;
            imageBox.ZoomToFit();
        }
    }
}