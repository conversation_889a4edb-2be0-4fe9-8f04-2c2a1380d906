﻿using System.ComponentModel;

namespace OCRTools.Language
{
    public enum SupportedLanguage
    {
        [MenuCategory("zh-CN")]
        [Description("中文-简体(Simplified Chinese)")]
        SimplifiedChinese,
        [MenuCategory("zh-TW")]
        [Description("中文-繁體(Traditional Chinese)")]
        TraditionalChinese,
        [MenuCategory("en-US")]
        [Description("English(United States)")]
        English,
        //[MenuCategory("ru-RU")]
        //[Description("Русский(Russian)")]
        //Russian,
        [MenuCategory("ja-JP")]
        [Description("日本語(Japanese)")]
        Japanese,
        [MenuCategory("ko-KR")]
        [Description("한국어(Korean)")]
        Korean,
        [MenuCategory("fr-FR")]
        [Description("Français(French)")]
        French,
        [MenuCategory("de-DE")]
        [Description("Deutsch(German)")]
        German,
        //[Description("Português(Portuguese)")]
        //Portuguese,
    }
}
