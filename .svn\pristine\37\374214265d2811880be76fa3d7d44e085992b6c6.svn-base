﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class DataGridViewEx : DataGridView
    {
        private Label _labPrompt;

        public DataGridViewEx()
        {
            SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            BackgroundColor = Color.White;
            AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.None;
            BorderStyle = BorderStyle.None;
            ScrollBars = ScrollBars.Both;
            RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.AutoSizeToAllHeaders;

            DataError += CheckDataGridView_DataError;
            BackgroundColorChanged += CheckDataGridView_BackgroundColorChanged;
            DataBindingComplete += CheckDataGridView_DataBindingComplete;
            CellMouseEnter += CheckDataGridView_CellMouseEnter;
            CellMouseLeave += CheckDataGridView_CellMouseLeave;
            //this.MouseLeave += CheckDataGridView_MouseLeave;
            CellMouseClick += CheckDataGridView_CellMouseClick;
            RowPostPaint += CheckDataGridView_RowPostPaint;

            //this.CellMouseDown += CheckDataGridView_CellMouseDown;
            RowTemplate.Height = 30;
            ColumnHeadersHeight = 30;
            //EnableHeadersVisualStyles = false;
            SetHanderStyle();
            InitLabPrompt();

            SetStyle(
                ControlStyles.DoubleBuffer | ControlStyles.AllPaintingInWmPaint | ControlStyles.OptimizedDoubleBuffer,
                true);

            UpdateStyles();

            //RowsRemoved += MyDataGridView_RowsRemoved;
            //UserDeletingRow += DataGridViewEx_UserDeletingRow;
            //KeyUp += DataGridViewEx_KeyUp;
        }

        //private void DataGridViewEx_KeyUp(object sender, KeyEventArgs e)
        //{
        //    //throw new NotImplementedException();
        //}

        //private DataGridViewRow _deleteRow;
        //private void DataGridViewEx_UserDeletingRow(object sender, DataGridViewRowCancelEventArgs e)
        //{
        //    e.Row.DefaultCellStyle.Font = new Font(Font.Name, Font.Size, FontStyle.Strikeout);
        //    _deleteRow = e.Row;
        //    e.Cancel = true;
        //}

        //void MyDataGridView_RowsRemoved(object sender, DataGridViewRowsRemovedEventArgs e)
        //{
        //    if (_deleteRow != null)
        //    {
        //        Rows.Insert(e.RowIndex, _deleteRow);
        //        Rows[e.RowIndex].DefaultCellStyle.Font = new Font(Font.Name, Font.Size, FontStyle.Strikeout);
        //    }
        //}

        public void FastLoadDataGrid<T>(IEnumerable<T> objList)
        {
            // Cache old values
            DataGridViewAutoSizeColumnsMode oldAutoSizeCols = AutoSizeColumnsMode;
            DataGridViewAutoSizeRowsMode oldAutoSizeRows = AutoSizeRowsMode;
            DataGridViewRowHeadersWidthSizeMode oldRowHeader = RowHeadersWidthSizeMode;
            DataGridViewColumnHeadersHeightSizeMode oldCol = ColumnHeadersHeightSizeMode;

            SuspendLayout(); // off for performance 

            // switch off stuff for performance
            AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
            AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.None;
            RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;

            // flush and load data source 
            DataSource = null;
            DataSource = new List<T>(objList); //wrap in sortable bindinglist to allow user to sort via column header click

            // revert back to old values
            AutoSizeColumnsMode = oldAutoSizeCols;
            AutoSizeRowsMode = oldAutoSizeRows;
            RowHeadersWidthSizeMode = oldRowHeader;
            ColumnHeadersHeightSizeMode = oldCol;

            ResumeLayout(false); // turn back on
        }

        /// <summary>
        ///     鼠标释放在 可见区域
        /// </summary>
        private bool IsCellMouse { get; set; }

        public bool IsShowSequence { get; set; }

        private void CheckDataGridView_BackgroundColorChanged(object sender, EventArgs e)
        {
            _labPrompt.BackColor = BackgroundColor;
            CheckDataGridView_DataBindingComplete(sender, null);
        }

        private void CheckDataGridView_RowPostPaint(object sender, DataGridViewRowPostPaintEventArgs e)
        {
            try
            {
                if (IsShowSequence)
                {
                    var rectangle = new Rectangle(e.RowBounds.Location.X, e.RowBounds.Location.Y, RowHeadersWidth - 4,
                        e.RowBounds.Height);
                    TextRenderer.DrawText(e.Graphics, (e.RowIndex + 1).ToString(), RowHeadersDefaultCellStyle.Font,
                        rectangle, Color.DimGray, TextFormatFlags.VerticalCenter | TextFormatFlags.Right);
                }
            }
            catch
            {
            }
        }

        //protected override void OnMouseUp(MouseEventArgs e)
        //{
        //    base.OnMouseUp(e);

        //    LabPrompt.Invalidate();
        //}

        //void CheckDataGridView_MouseLeave(object sender, EventArgs e)
        //{
        //    EndEdit();
        //}

        private void CheckDataGridView_DataError(object sender, DataGridViewDataErrorEventArgs e)
        {
            //Console.WriteLine(string.Format("输入错误,列名:{0},第{1}行,错误信息:{2}", Columns[e.ColumnIndex].HeaderText, e.RowIndex, e.Exception.Message));
            e.Cancel = false;
        }

        //void CheckDataGridView_CellMouseDown(object sender, DataGridViewCellMouseEventArgs e)
        //{
        //    if (e.Button == MouseButtons.Right)
        //    {
        //        if (e.RowIndex >= 0)
        //        {
        //            ClearSelection();
        //            if (Rows.Count > e.RowIndex)
        //            {
        //                try
        //                {
        //                    Rows[e.RowIndex].Selected = true;
        //                    CurrentCell = Rows[e.RowIndex].Cells[e.ColumnIndex];
        //                }
        //                catch { }
        //            }
        //        }
        //    }
        //}

        private void SetHanderStyle()
        {
            ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(244, 244, 244);
        }

        /// <summary>
        /// 是否第一次初始化
        /// </summary>
        //private bool IsFirstInit = true;
        protected override void OnPaint(PaintEventArgs e)
        {
            try
            {
                if (_labPrompt != null && _labPrompt.Visible)
                    _labPrompt.Location = new Point((Size.Width - _labPrompt.Size.Width) / 2,
                        (Size.Height - _labPrompt.Size.Height) / 2);
                base.OnPaint(e);
            }
            catch
            {
            }
        }

        private void CheckDataGridView_DataBindingComplete(object sender, DataGridViewBindingCompleteEventArgs e)
        {
            _labPrompt.Visible = Rows.Count <= 0;
            if (Rows.Count <= 0) return;
            InitBackColor();
            try
            {
                if (Rows.Count > 0)
                {
                    Rows[0].DefaultCellStyle.SelectionBackColor = SystemColors.Highlight;
                    Rows[0].DefaultCellStyle.SelectionForeColor = SystemColors.HighlightText;
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        public void InitLabPrompt()
        {
            _labPrompt = new Label
            {
                AutoSize = true,
                BackColor = Color.White,
                Font = new Font("微软雅黑", 26F, GraphicsUnit.Pixel),
                ForeColor = SystemColors.GrayText,

                Name = "labPrompt",
                Size = new Size(204, 35),
                TabIndex = 12,
                Text = "没有数据！"
            };
            _labPrompt.Location = new Point((Size.Width - _labPrompt.Size.Width) / 2,
                (Size.Height - _labPrompt.Size.Height) / 2);
            _labPrompt.Visible = true;
            Controls.Add(_labPrompt);
        }

        /// <summary>
        /// 单击一行时 发生
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void CheckDataGridView_CellMouseClick(object sender, DataGridViewCellMouseEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0)
                {
                    if (_selectRowIndex >= 0 && _selectRowIndex < Rows.Count)
                        Rows[_selectRowIndex].DefaultCellStyle.SelectionForeColor = Color.Black;
                    _selectRowIndex = e.RowIndex;
                    if (_selectRowIndex > 0)
                    {
                        var item = Rows[_selectRowIndex];
                        if (_selectRowIndex % 2 == 0)
                            item.DefaultCellStyle.BackColor = _normalColor;
                        else
                            item.DefaultCellStyle.BackColor = BackgroundColor;
                    }

                    _selectRowIndex = e.RowIndex;
                    Rows[_selectRowIndex].DefaultCellStyle.SelectionBackColor = _rowSelect;
                    Rows[_selectRowIndex].DefaultCellStyle.SelectionForeColor = Color.White;
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        /// <summary>
        ///     鼠标离开单元格 时发生
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void CheckDataGridView_CellMouseLeave(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0 && _selectRowIndex != e.RowIndex)
                {
                    if (e.RowIndex % 2 == 0)
                        Rows[e.RowIndex].DefaultCellStyle.BackColor = _normalColor;
                    else
                        Rows[e.RowIndex].DefaultCellStyle.BackColor = BackgroundColor;
                    //Rows[e.RowIndex].DefaultCellStyle.BackColor = singleRow;
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }

            IsCellMouse = false;
        }

        /// <summary>
        ///     鼠标进入单元格的时候 发生
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void CheckDataGridView_CellMouseEnter(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && _selectRowIndex != e.RowIndex)
            {
                //Rows[e.RowIndex].DefaultCellStyle.BackColor = rowHover;
                Rows[e.RowIndex].DefaultCellStyle.BackColor = _rowHover;
                IsCellMouse = true;
            }
        }

        /// <summary>
        ///     初始化背景色，隔行变色
        /// </summary>
        private void InitBackColor()
        {
            for (var i = 0; i < Rows.Count; i++)
            {
                var item = Rows[i];
                if (i % 2 == 0)
                    item.DefaultCellStyle.BackColor = _normalColor;
                else
                    item.DefaultCellStyle.BackColor = BackgroundColor;
            }
        }

        #region 常量

        /// <summary>
        ///     隔行变色-双行颜色
        /// </summary>
        private readonly Color _normalColor = Color.FromArgb(242, 242, 242);

        /// <summary>
        ///     鼠标悬浮
        /// </summary>
        private readonly Color _rowHover = Color.FromArgb(208, 227, 255);

        /// <summary>
        ///     选择
        /// </summary>
        private readonly Color _rowSelect = Color.FromArgb(44, 130, 255);

        /// <summary>
        ///     选中的行
        /// </summary>
        private int _selectRowIndex;

        #endregion
    }
}