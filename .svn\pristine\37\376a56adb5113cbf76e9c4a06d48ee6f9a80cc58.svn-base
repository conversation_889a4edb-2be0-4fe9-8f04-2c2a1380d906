﻿using OCRTools.Common;
using System.Collections.Specialized;
using System.IO;

namespace ImageLib
{
    /// <summary>
    /// </summary>
    public class _163Upload
    {
        public static bool Enable { get; set; } = true;

        private const string strYanXuanFileNameSpilt = "\"data\":[\"";

        public const int MaxSize = (int)(1024 * 1024 * 9d);

        /// <summary>
        /// 网易严选，商品反馈底部接口
        /// </summary>
        /// <param name="content"></param>
        /// <returns></returns>
        public static string GetResult(byte[] content)
        {
            var result = "";
            var url = "http://you.163.com/xhr/file/upload.json";
            var file = new UploadFileInfo()
            {
                Name = "file",
                Filename = "1.png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            var vaules = new NameValueCollection()
            {
            };
            var html = string.Empty;
            try
            {
                html = UploadFileRequest.Post(url, new[] { file }, vaules);
            }
            catch { }
            if (html.Contains(strYanXuanFileNameSpilt))
            {
                result = html.Substring(html.IndexOf(strYanXuanFileNameSpilt) + strYanXuanFileNameSpilt.Length);
                result = result.Substring(0, result.IndexOf("\"")).Replace("\\/", "/");
            }
            return result;
        }
    }
}
