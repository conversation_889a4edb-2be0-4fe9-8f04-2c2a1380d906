using System.Collections.Generic;
using System.IO;
using ExcelLibrary.SpreadSheet;

namespace ExcelLibrary.BinaryFileFormat
{
	public class WorksheetDecoder
	{
		public static Worksheet Decode(Workbook book, Stream stream, SharedResource sharedResource)
		{
			Worksheet worksheet = new Worksheet();
			worksheet.Book = book;
			List<Record> list = ReadRecords(stream, out worksheet.Drawing);
			worksheet.Cells = PopulateCells(list, sharedResource);
			worksheet.Book.Records.AddRange(list);
			return worksheet;
		}

		private static List<Record> ReadRecords(Stream stream, out MSODRAWING drawingRecord)
		{
			List<Record> list = new List<Record>();
			drawingRecord = null;
			Record record = Record.Read(stream);
			Record record2 = record;
			Record record3 = null;
			record2.Decode();
			if (record is BOF && ((BOF)record).StreamType == 16)
			{
				while (record.Type != 10)
				{
					if (record.Type == 60)
					{
						record2.ContinuedRecords.Add(record);
					}
					else
					{
						switch (record.Type)
						{
						case 519:
							if (record3 is FORMULA)
							{
								record.Decode();
								(record3 as FORMULA).StringRecord = record as STRING;
							}
							break;
						case 236:
							if (drawingRecord == null)
							{
								drawingRecord = record as MSODRAWING;
								list.Add(record);
							}
							else
							{
								drawingRecord.ContinuedRecords.Add(record);
							}
							break;
						default:
							list.Add(record);
							break;
						}
						if (record.Type == 6)
						{
							record3 = record;
						}
						else if (record.Type != 1212 && record.Type != 545)
						{
							record3 = null;
						}
						record2 = record;
					}
					record = Record.Read(stream);
				}
				list.Add(record);
			}
			return list;
		}

		private static CellCollection PopulateCells(List<Record> records, SharedResource sharedResource)
		{
			CellCollection cellCollection = new CellCollection();
			cellCollection.SharedResource = sharedResource;
			foreach (Record record in records)
			{
				record.Decode();
				switch (record.Type)
				{
				case 517:
				{
					BOOLERR bOOLERR = record as BOOLERR;
					cellCollection.CreateCell(bOOLERR.RowIndex, bOOLERR.ColIndex, bOOLERR.GetValue(), bOOLERR.XFIndex);
					break;
				}
				case 516:
				{
					LABEL lABEL = record as LABEL;
					cellCollection.CreateCell(lABEL.RowIndex, lABEL.ColIndex, lABEL.Value, lABEL.XFIndex);
					break;
				}
				case 253:
				{
					LABELSST lABELSST = record as LABELSST;
					Cell cell = cellCollection.CreateCell(lABELSST.RowIndex, lABELSST.ColIndex, sharedResource.GetStringFromSST(lABELSST.SSTIndex), lABELSST.XFIndex);
					cell.Style.RichTextFormat = sharedResource.SharedStringTable.RichTextFormatting[lABELSST.SSTIndex];
					break;
				}
				case 515:
				{
					NUMBER nUMBER = record as NUMBER;
					cellCollection.CreateCell(nUMBER.RowIndex, nUMBER.ColIndex, nUMBER.Value, nUMBER.XFIndex);
					break;
				}
				case 638:
				{
					RK rK = record as RK;
					cellCollection.CreateCell(rK.RowIndex, rK.ColIndex, Record.DecodeRK(rK.Value), rK.XFIndex);
					break;
				}
				case 189:
				{
					MULRK mULRK = record as MULRK;
					int rowIndex = mULRK.RowIndex;
					for (int i = mULRK.FirstColIndex; i <= mULRK.LastColIndex; i++)
					{
						int index = i - mULRK.FirstColIndex;
						object value = Record.DecodeRK(mULRK.RKList[index]);
						int xFindex = mULRK.XFList[index];
						cellCollection.CreateCell(rowIndex, i, value, xFindex);
					}
					break;
				}
				case 6:
				{
					FORMULA fORMULA = record as FORMULA;
					cellCollection.CreateCell(fORMULA.RowIndex, fORMULA.ColIndex, fORMULA.DecodeResult(), fORMULA.XFIndex);
					break;
				}
				}
			}
			return cellCollection;
		}

		private static FONT getFontRecord(SharedResource sharedResource, ushort index)
		{
			if (index >= 0 && index <= 3)
			{
				return sharedResource.Fonts[index];
			}
			if (index >= 5)
			{
				return sharedResource.Fonts[index - 1];
			}
			return null;
		}

		public static FONT getFontForCharacter(Cell cell, ushort charIndex)
		{
			FONT result = null;
			int num = cell.Style.RichTextFormat.CharIndexes.BinarySearch(charIndex);
			List<ushort> fontIndexes = cell.Style.RichTextFormat.FontIndexes;
			if (num >= 0)
			{
				result = getFontRecord(cell.SharedResource, fontIndexes[num]);
			}
			else if (~num != 0)
			{
				result = getFontRecord(cell.SharedResource, fontIndexes[~num - 1]);
			}
			return result;
		}
	}
}
