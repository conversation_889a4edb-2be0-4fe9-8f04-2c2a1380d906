﻿using System;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows;
using System.Windows.Forms;

namespace OCRTools.Common
{
    internal sealed class RetryClipboardWriter
    {
        //private static readonly int cannotOpenClipboard = -2147221040;

        private static readonly int maximalTries = 3;

        private static readonly TimeSpan wait = TimeSpan.FromMilliseconds(20.0);

        public static void Copy(string text)
        {
            if (text != null)
            {
                int num = 0;
                while (num++ < maximalTries)
                {
                    try
                    {
                        Clipboard.SetDataObject(text);
                        return;
                    }
                    catch (COMException)// when (ex.HResult == cannotOpenClipboard)
                    {
                        if (num >= maximalTries)
                        {
                            throw;
                        }
                    }
                    Thread.Sleep(wait);
                }
            }
        }
    }
}
