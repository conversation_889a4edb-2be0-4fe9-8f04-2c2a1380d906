﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;

namespace OCRTools
{
    public class SNtpClient
    {

        private static SNtpClient myInstance;

        public static SNtpClient Instance
        {
            get
            {
                if (myInstance == null)
                {
                    myInstance = new SNtpClient();
                    myInstance.StrNtpServer = myInstance.NtpServerList[0];
                }
                return myInstance;
            }
        }

        public List<string> NtpServerList = new List<string>()
            {
                "ntp1.aliyun.com",
                "ntp2.aliyun.com",
                "time.windows.com",
                "cn.ntp.org.cn",
                "edu.ntp.org.cn",
                "ntp3.aliyun.com",
                "ntp4.aliyun.com",
                "asia.pool.ntp.org",
                "time.nist.gov",
                "ntp5.aliyun.com",
                "ntp6.aliyun.com",
                "ntp7.aliyun.com",
                "hk.ntp.org.cn",
                "pool.ntp.org",
            };

        public string StrNtpServer;


        public long GetNetworkTimeOffset()
        {
            long result = -9999;
            try
            {
                var client = new NTPClient(StrNtpServer);
                client.Connect();
                if (client.IsResponseValid)
                {
                    result = client.LocalClockOffset;
                }
            }
            catch (Exception oe)
            {
                if (oe.Message.Contains("失败") || oe.Message.Contains("主机")
                    || oe.Message.ToLower().Contains("server") || oe.Message.ToLower().Contains("timeout"))
                {
                    try
                    {
                        var index = NtpServerList.IndexOf(StrNtpServer);
                        index = NtpServerList.Count - 1 <= index ? 0 : index + 1;
                        StrNtpServer = NtpServerList[index];
                        //Console.WriteLine($"NTP Server:{StrNtpServer}");
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                    }
                }
                else
                {
                    Console.WriteLine(oe.Message);
                }
                result = -9999;
            }
            return result;
        }

    }

    public class NTPClient
    {
        /// <summary>
        /// SNTP Data Structure Length
        /// </summary>
        private const byte SNTPDataLength = 48;

        /// <summary>
        /// SNTP Data Structure (as described in RFC 2030)
        /// </summary>
        byte[] SNTPData = new byte[SNTPDataLength];

        //Offset constants for timestamps in the data structure
        private const byte offReferenceID = 12;
        private const byte offReferenceTimestamp = 16;
        private const byte offOriginateTimestamp = 24;
        private const byte offReceiveTimestamp = 32;
        private const byte offTransmitTimestamp = 40;

        /// <summary>
        /// Version Number Version number of the protocol (3 or 4) NTP的版本号
        /// </summary>
        public byte VersionNumber
        {
            get
            {
                // Isolate bits 3 - 5
                byte val = (byte)((SNTPData[0] & 0x38) >> 3);
                return val;
            }
        }

        /// <summary>
        /// Mode 长度为3比特，表示NTP的工作模式。不同的值所表示的含义分别是：0未定义、1表示主动对等体模式、2表示被动对等体模式、3表示客户模式、4表示服务器模式、5表示广播模式或组播模式、6表示此报文为NTP控制报文、7预留给内部使用
        /// </summary>
        public _Mode Mode
        {
            get
            {
                // Isolate bits 0 - 3
                byte val = (byte)(SNTPData[0] & 0x7);
                switch (val)
                {
                    case 0:
                        return _Mode.Unknown;
                    case 6:
                        return _Mode.Unknown;
                    case 7:
                        return _Mode.Unknown;
                    default:
                        return _Mode.Unknown;
                    case 1:
                        return _Mode.SymmetricActive;
                    case 2:
                        return _Mode.SymmetricPassive;
                    case 3:
                        return _Mode.Client;
                    case 4:
                        return _Mode.Server;
                    case 5:
                        return _Mode.Broadcast;
                }
            }
        }

        /// <summary>
        /// Stratum 系统时钟的层数，取值范围为1～16，它定义了时钟的准确度。层数为1的时钟准确度最高，准确度从1到16依次递减，层数为16的时钟处于未同步状态，不能作为参考时钟
        /// </summary>
        public _Stratum Stratum
        {
            get
            {
                byte val = SNTPData[1];
                if (val == 0) return _Stratum.Unspecified;
                if (val == 1) return _Stratum.PrimaryReference;
                if (val <= 15) return _Stratum.SecondaryReference;
                return _Stratum.Reserved;
            }
        }

        /// <summary>
        /// Originate Timestamp (T1)  The time at which the request departed the client for the server. 发送报文时的本机时间
        /// </summary>
        public DateTime OriginateTimestamp
        {
            get
            {
                return ComputeDate(GetMilliSeconds(offOriginateTimestamp));
            }
        }

        /// <summary>
        /// Receive Timestamp (T2) The time at which the request arrived at the server. 报文到达NTP服务器时的服务器时间
        /// </summary>
        public DateTime ReceiveTimestamp
        {
            get
            {
                DateTime time = ComputeDate(GetMilliSeconds(offReceiveTimestamp));
                // Take care of the time zone
                TimeSpan offspan = TimeZone.CurrentTimeZone.GetUtcOffset(DateTime.Now);
                return time + offspan;
            }
        }

        /// <summary>
        /// Transmit Timestamp (T3) The time at which the reply departed the server for client.  报文从NTP服务器离开时的服务器时间
        /// </summary>
        public DateTime TransmitTimestamp
        {
            get
            {
                DateTime time = ComputeDate(GetMilliSeconds(offTransmitTimestamp));
                // Take care of the time zone
                TimeSpan offspan = TimeZone.CurrentTimeZone.GetUtcOffset(DateTime.Now);
                return time + offspan;
            }
            set
            {
                SetDate(offTransmitTimestamp, value);
            }
        }

        /// <summary>
        /// Destination Timestamp (T4) The time at which the reply arrived at the client. 接收到来自NTP服务器返回报文时的本机时间
        /// </summary>
        public DateTime DestinationTimestamp;


        /// <summary>
        /// Local clock offset (in milliseconds)  The offset of the local clock relative to the primary reference source.本机相对于NTP服务器（主时钟）的时间差
        /// </summary>
        public long LocalClockOffset
        {
            get
            {
                // Thanks to DNH <<EMAIL>>
                TimeSpan span = (ReceiveTimestamp - OriginateTimestamp) + (TransmitTimestamp - DestinationTimestamp);
                return span.Ticks / 2;
            }
        }

        /// <summary>
        /// Compute date, given the number of milliseconds since January 1, 1900
        /// </summary>
        /// <param name="milliseconds"></param>
        /// <returns></returns>
        private DateTime ComputeDate(ulong milliseconds)
        {
            TimeSpan span = TimeSpan.FromMilliseconds(milliseconds);
            DateTime time = new DateTime(1900, 1, 1);
            time += span;
            return time;
        }

        /// <summary>
        /// Compute the number of milliseconds, given the offset of a 8-byte array
        /// </summary>
        /// <param name="offset"></param>
        /// <returns></returns>
        private ulong GetMilliSeconds(byte offset)
        {
            ulong intpart = 0, fractpart = 0;

            for (int i = 0; i <= 3; i++)
            {
                intpart = 256 * intpart + SNTPData[offset + i];
            }
            for (int i = 4; i <= 7; i++)
            {
                fractpart = 256 * fractpart + SNTPData[offset + i];
            }
            ulong milliseconds = intpart * 1000 + (fractpart * 1000) / 0x100000000L;
            return milliseconds;
        }

        /// <summary>
        /// Compute the 8-byte array, given the date
        /// </summary>
        /// <param name="offset"></param>
        /// <param name="date"></param>
        private void SetDate(byte offset, DateTime date)
        {
            var startOfCentury = new DateTime(1900, 1, 1, 0, 0, 0);    // January 1, 1900 12:00 AM

            var milliseconds = (ulong)(date - startOfCentury).TotalMilliseconds;
            var intpart = milliseconds / 1000;
            var fractpart = ((milliseconds % 1000) * 0x100000000L) / 1000;

            ulong temp = intpart;
            for (int i = 3; i >= 0; i--)
            {
                SNTPData[offset + i] = (byte)(temp % 256);
                temp = temp / 256;
            }

            temp = fractpart;
            for (int i = 7; i >= 4; i--)
            {
                SNTPData[offset + i] = (byte)(temp % 256);
                temp = temp / 256;
            }
        }

        /// <summary>
        /// Initialize the NTPClient data
        /// </summary>
        private void Initialize()
        {
            // Set version number to 4 and Mode to 3 (client)
            SNTPData[0] = 0x1B;
            // Initialize all other fields with 0
            for (int i = 1; i < 48; i++)
            {
                SNTPData[i] = 0;
            }
            // Initialize the transmit timestamp
            TransmitTimestamp = DateTime.Now;
        }

        /// <summary>
        /// The IPAddress of the time server we're connecting to
        /// </summary>
        private IPAddress serverAddress;


        /// <summary>
        /// Constractor with HostName
        /// </summary>
        /// <param name="host"></param>
        public NTPClient(string host)
        {
            if (DnsHelper.IsIPv4(host))
            {
                serverAddress = IPAddress.Parse(host);
            }
            else
            {
                try
                {
                    serverAddress = IPAddress.Parse(DnsHelper.GetDnsIp(host));
                }
                catch
                {
                    // Resolve server address
                    IPHostEntry hostadd = Dns.GetHostEntry(host);
                    foreach (IPAddress address in hostadd.AddressList)
                    {
                        if (address.AddressFamily == AddressFamily.InterNetwork) //只支持IPV4协议的IP地址
                        {
                            serverAddress = address;
                            break;
                        }
                    }
                }
            }

            if (serverAddress == null)
                throw new Exception("Can't get any ipaddress infomation");
        }

        /// <summary>
        /// Connect to the time server and update system time
        /// </summary>
        public void Connect(int timeout = 3000)
        {
            IPEndPoint EPhost = new IPEndPoint(serverAddress, 123);

            //Connect the time server
            using (UdpClient TimeSocket = new UdpClient())
            {
                TimeSocket.Connect(EPhost);

                // Initialize data structure
                Initialize();
                TimeSocket.Send(SNTPData, SNTPData.Length);
                TimeSocket.Client.ReceiveTimeout = timeout;
                SNTPData = TimeSocket.Receive(ref EPhost);
                if (!IsResponseValid)
                    throw new Exception("Invalid response from " + serverAddress);
            }
            DestinationTimestamp = DateTime.Now;
        }

        /// <summary>
        /// Check if the response from server is valid
        /// </summary>
        /// <returns></returns>
        public bool IsResponseValid
        {
            get
            {
                return !(SNTPData.Length < SNTPDataLength || Mode != _Mode.Server);
            }
        }
    }

    /// <summary>
    /// Mode field values
    /// </summary>
    public enum _Mode
    {
        SymmetricActive,    // 1 - Symmetric active
        SymmetricPassive,    // 2 - Symmetric pasive
        Client,                // 3 - Client
        Server,                // 4 - Server
        Broadcast,            // 5 - Broadcast
        Unknown                // 0, 6, 7 - Reserved
    }

    /// <summary>
    /// Stratum field values
    /// </summary>
    public enum _Stratum
    {
        Unspecified,            // 0 - unspecified or unavailable
        PrimaryReference,        // 1 - primary reference (e.g. radio-clock)
        SecondaryReference,        // 2-15 - secondary reference (via NTP or SNTP)
        Reserved                // 16-255 - reserved
    }
}