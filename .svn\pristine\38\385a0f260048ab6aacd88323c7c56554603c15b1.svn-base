using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools
{
    /// <summary>
    /// 图文模式处理器
    /// 完全基于PanelPictureView的逻辑，提供简单的悬停高亮和点击选择功能
    /// </summary>
    public class ImageTextModeHandler : IImageModeHandler
    {
        #region 常量定义
        
        private readonly Color IMG_MODE_FOCUS_COLOR = Color.FromArgb(50, Color.Red);
        private readonly Color IMG_MODE_CLICK_COLOR = Color.FromArgb(90, Color.Red);
        
        #endregion
        
        #region 私有字段
        
        private MultiModeImageViewer _viewer;
        private List<TextCellInfo> _lstCells = new List<TextCellInfo>();
        private TextCellInfo _currentCell;
        private Rectangle _drawRegion;
        private object _objLock = "";
        
        #endregion
        
        #region 公共属性 - 图文模式特有的属性
        
        /// <summary>
        /// 是否显示提示 - 图文模式特有的属性
        /// </summary>
        public bool IsShowTip { get; set; } = true;
        
        #endregion
        
        #region 事件定义
        
        /// <summary>
        /// 文本块选择状态变化事件
        /// </summary>
        public event EventHandler<TextCellEventArgs> TextCellStateChanged;
        
        /// <summary>
        /// 模式特定事件（实现接口要求）
        /// </summary>
        public event EventHandler<EventArgs> ModeSpecificEvent;
        
        /// <summary>
        /// 事件参数类
        /// </summary>
        public class TextCellEventArgs : EventArgs
        {
            public TextCellInfo Cell { get; private set; }
            public TextCellSelectionType SelectionType { get; private set; }

            public TextCellEventArgs(TextCellInfo cell, TextCellSelectionType type)
            {
                Cell = cell;
                SelectionType = type;
            }
        }

        /// <summary>
        /// 选择类型枚举
        /// </summary>
        public enum TextCellSelectionType
        {
            None,       // 无选择
            Hover,      // 悬停
            Click       // 点击
        }
        
        #endregion
        
        #region IImageModeHandler 实现
        
        public void Initialize(MultiModeImageViewer viewer)
        {
            _viewer = viewer;
        }

        public void Activate()
        {
            // 图文模式不需要特殊的事件绑定，使用基类的事件分发即可
        }

        public void Deactivate()
        {
            // 图文模式不需要特殊的事件解绑
        }
        
        public void BindData(Image image, List<TextCellInfo> textCells)
        {
            _lstCells = textCells ?? new List<TextCellInfo>();
            
            // 清除当前高亮
            if (!_drawRegion.IsEmpty)
            {
                _viewer.Invalidate(_drawRegion);
                _drawRegion = Rectangle.Empty;
            }

            // 重置当前单元格
            _currentCell = null;
            
            // 如果有图片和文字区域，异步处理图像
            if (image != null && _lstCells.Count > 0)
            {
                // 这里暂时直接使用原始图像，后续可以添加预处理
                _viewer.Image = image;
            }
            else
            {
                _viewer.Image = image;
            }
            
            _viewer.BringToFront();
        }
        
        public void HandleMouseDown(MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                _currentCell = null;
                DrawCell(true);
            }
        }
        
        public void HandleMouseMove(MouseEventArgs e)
        {
            DrawCell(false, e.Button != MouseButtons.None || e.Delta != 0);
        }
        
        public void HandleMouseUp(MouseEventArgs e)
        {
            // 图文模式不需要特殊的MouseUp处理
        }
        
        public void HandleMouseLeave(EventArgs e)
        {
            // 鼠标离开时清除高亮并触发事件
            if (_currentCell != null)
            {
                TextCellStateChanged?.Invoke(this, new TextCellEventArgs(null, TextCellSelectionType.None));
                _currentCell = null;

                if (!_drawRegion.IsEmpty)
                {
                    _viewer.Invalidate(_drawRegion);
                    _drawRegion = Rectangle.Empty;
                }

                CommonMethod.HideTxtToolTip(_viewer);
            }
        }
        
        public void HandlePaint(PaintEventArgs e)
        {
            if (!_drawRegion.IsEmpty)
            {
                using (var contentBrush = new SolidBrush(IMG_MODE_CLICK_COLOR))
                {
                    e.Graphics.FillRectangle(contentBrush, _drawRegion);
                }
            }
        }
        
        public void HandleZoomChanged(EventArgs e)
        {
            if (!_drawRegion.IsEmpty)
            {
                _viewer.Invalidate(_drawRegion);
                _drawRegion = Rectangle.Empty;
            }
        }
        
        public void HandleScroll(ScrollEventArgs e)
        {
            HandleZoomChanged(e);
        }

        public void HandleMouseWheel(MouseEventArgs e)
        {
            HandleMouseMove(e);
        }
        
        public void ClearState()
        {
            _currentCell = null;
            if (!_drawRegion.IsEmpty)
            {
                _viewer?.Invalidate(_drawRegion);
                _drawRegion = Rectangle.Empty;
            }
        }
        
        public void Dispose()
        {
            ClearState();
            _lstCells?.Clear();
            _viewer = null;
        }
        
        #endregion

        #region 核心逻辑方法

        /// <summary>
        /// 获取当前鼠标位置的文字区域
        /// </summary>
        private TextCellInfo GetCurrentCell()
        {
            var mouseLoc = _viewer.PointToClient(Cursor.Position);
            mouseLoc = new Point(mouseLoc.X - _viewer.AutoScrollPosition.X, mouseLoc.Y - _viewer.AutoScrollPosition.Y);
            var cell = _lstCells.Where(item =>
                item.location != null &&
                item.location.Rectangle.Zoom(_viewer.ZoomFactor).Contains(mouseLoc)
            ).FirstOrDefault();
            return cell;
        }

        /// <summary>
        /// 绘制文字区域高亮
        /// </summary>
        private void DrawCell(bool isClick = false, bool isOnMove = false)
        {
            var cell = isOnMove ? null : GetCurrentCell();
            if (cell != null && Equals(_currentCell, cell))
            {
                return;
            }

            if (!_drawRegion.IsEmpty)
            {
                if (IsShowTip || cell != null)
                {
                    _viewer.Invalidate(_drawRegion);
                    _drawRegion = Rectangle.Empty;
                }
            }

            if (cell != null)
            {
                var cellRect = cell.location.Rectangle.SizeOffset(2).Zoom(_viewer.ZoomFactor);
                cellRect.Location = cellRect.Location.Add(_viewer.AutoScrollPosition);
                _drawRegion = new Rectangle(cellRect.Location, cellRect.Size);
                var tipLoc = new Point(Math.Max(0, _drawRegion.Location.X), _drawRegion.Location.Y + _drawRegion.Height + 1);
                if (!_viewer.ClientRectangle.Contains(tipLoc))
                {
                    tipLoc = new Point(Math.Max(_viewer.ClientRectangle.X, tipLoc.X), Math.Min(_viewer.ClientRectangle.Y, tipLoc.Y));
                }

                // 触发事件
                TextCellStateChanged?.Invoke(this, new TextCellEventArgs(
                    cell,
                    isClick ? TextCellSelectionType.Click : TextCellSelectionType.Hover
                ));

                if (isClick)
                {
                    _viewer.Invalidate(_drawRegion);

                    if (IsShowTip)
                        CommonMethod.ShowTxtToolTipContextMenu(_viewer, cell.TipText, tipLoc);

                    if (CommonSetting.点击图片复制结果)
                    {
                        try
                        {
                            ClipboardService.SetText(cell.TipText);
                        }
                        catch { }
                    }
                }
                else
                {
                    using (var g = _viewer.CreateGraphics())
                    {
                        using (var contentBrush = new SolidBrush(IMG_MODE_FOCUS_COLOR))
                        {
                            g.FillRectangle(contentBrush, _drawRegion);
                        }
                    }

                    if (IsShowTip)
                        CommonMethod.ShowTxtToolTip(_viewer, cell.TipText, tipLoc);
                }
            }
            else
            {
                // 触发无选择事件
                TextCellStateChanged?.Invoke(this, new TextCellEventArgs(null, TextCellSelectionType.None));

                CommonMethod.HideTxtToolTip(_viewer);
                CommonMethod.HideTxtToolTipContextMenu();
            }
            _currentCell = cell;
        }

        /// <summary>
        /// 绑定图文数据
        /// </summary>
        public void BindPicTxt(UcContent content, bool isShowTxt = false)
        {
            _lstCells = content.OcrContent?.result?.verticalText?.DeserializeJson<List<TextCellInfo>>() ?? new List<TextCellInfo>();

            // 清除当前高亮
            if (!_drawRegion.IsEmpty)
            {
                _viewer.Invalidate(_drawRegion);
                _drawRegion = Rectangle.Empty;
            }

            // 重置当前单元格
            _currentCell = null;

            // 不立即处理图像，而是在需要时异步处理
            if (content.Image != null && _lstCells.Count > 0)
            {
                // 异步处理图像绘制，避免UI阻塞
                Task.Run(() => ProcessImageAsync(content, isShowTxt));
            }
            else
            {
                // 如果没有处理需求，直接使用原始图像
                _viewer.Image = content.Image;
            }

            _viewer.BringToFront();
        }

        #endregion

        #region 图像处理方法

        /// <summary>
        /// 异步处理图像绘制
        /// </summary>
        private async Task ProcessImageAsync(UcContent content, bool isShowTxt)
        {
            try
            {
                var processedImage = await Task.Run(() => CreateProcessedImage(content, isShowTxt));

                // 切换到UI线程更新图像
                _viewer.Invoke(new Action(() =>
                {
                    _viewer.Image = processedImage;
                }));
            }
            catch (Exception ex)
            {
                // 出错时使用原始图像
                _viewer.Invoke(new Action(() =>
                {
                    _viewer.Image = content.Image;
                }));
            }
        }

        /// <summary>
        /// 创建处理后的图像
        /// </summary>
        private Bitmap CreateProcessedImage(UcContent content, bool isShowTxt)
        {
            // 安全检查图片状态
            if (content?.Image == null)
            {
                throw new ArgumentException("Content image is null");
            }

            // 检查图片是否已被释放
            try
            {
                // 尝试访问图片属性来检查是否有效
                var width = content.Image.Width;
                var height = content.Image.Height;
                if (width <= 0 || height <= 0)
                {
                    throw new ArgumentException("Content image has invalid dimensions");
                }
            }
            catch (Exception ex)
            {
                throw new ArgumentException($"Content image is invalid: {ex.Message}");
            }

            // 安全创建图片副本
            Bitmap image;
            try
            {
                image = new Bitmap(content.Image);
            }
            catch (Exception ex)
            {
                throw new ArgumentException($"Failed to create bitmap copy: {ex.Message}");
            }

            // 批量预处理文本内容，避免重复调用
            PreprocessTextContent(content, _lstCells);

            // 预处理所有绘制操作，减少锁竞争
            var drawingOperations = PrepareDrawingOperations(_lstCells, isShowTxt);

            // 串行执行GDI+操作
            using (var g = Graphics.FromImage(image))
            {
                // 优化渲染质量设置，平衡性能和质量
                g.InterpolationMode = InterpolationMode.Bilinear;
                g.CompositingQuality = CompositingQuality.HighSpeed;
                g.SmoothingMode = SmoothingMode.HighSpeed;
                g.TextRenderingHint = TextRenderingHint.SystemDefault;

                using (var brush = new SolidBrush(CommonSetting.Get默认文字颜色()))
                using (var backgroundBrush = new SolidBrush(CommonSetting.Get默认背景颜色()))
                {
                    // 执行所有绘制操作
                    foreach (var operation in drawingOperations)
                    {
                        ExecuteDrawingOperation(g, operation, brush, backgroundBrush);
                    }
                }
            }

            return image;
        }

        /// <summary>
        /// 批量预处理文本内容
        /// </summary>
        private void PreprocessTextContent(UcContent content, List<TextCellInfo> cells)
        {
            // 并行处理文本内容，避免重复调用
            System.Threading.Tasks.Parallel.ForEach(cells, item =>
            {
                if (!string.IsNullOrEmpty(item.words) || !string.IsNullOrEmpty(item.trans))
                {
                    item.TipText = content.GetTextByContent(item);
                }
            });
        }

        /// <summary>
        /// 绘制操作数据结构
        /// </summary>
        private class DrawingOperation
        {
            public Rectangle Rectangle { get; set; }
            public string Text { get; set; }
            public Font Font { get; set; }
            public bool DrawText { get; set; }
        }

        /// <summary>
        /// 准备绘制操作
        /// </summary>
        private List<DrawingOperation> PrepareDrawingOperations(List<TextCellInfo> cells, bool isShowTxt)
        {
            // 并行预处理绘制数据
            return cells.AsParallel()
                .Where(item => !(string.IsNullOrEmpty(item.words) && string.IsNullOrEmpty(item.trans)) && item.location != null)
                .Select(item => PrepareDrawingOperation(item, isShowTxt))
                .Where(op => op != null)
                .ToList();
        }

        /// <summary>
        /// 准备单个绘制操作
        /// </summary>
        private DrawingOperation PrepareDrawingOperation(TextCellInfo item, bool isShowTxt)
        {
            var rectangle = item.location.Rectangle;
            var operation = new DrawingOperation
            {
                Rectangle = rectangle,
                DrawText = false
            };

            if (isShowTxt && !string.IsNullOrEmpty(item.TipText))
            {
                operation.Text = item.TipText;
                operation.Font = CommonMethod.ScaleLabelByHeight(item.TipText, CommonString.GetUserNormalFont(5F), rectangle.Size);
                operation.DrawText = true;
            }

            return operation;
        }

        /// <summary>
        /// 执行绘制操作
        /// </summary>
        private void ExecuteDrawingOperation(Graphics g, DrawingOperation operation, SolidBrush brush, SolidBrush backgroundBrush)
        {
            // 绘制红色边框
            g.DrawRectangle(Pens.Red, operation.Rectangle.LocationOffset(-1, -1).SizeOffset(2));

            // 绘制文本（如果需要）
            if (operation.DrawText)
            {
                g.FillRectangle(backgroundBrush, operation.Rectangle);
                g.DrawString(operation.Text, operation.Font, brush, operation.Rectangle);
            }
        }

        #endregion
    }
}
