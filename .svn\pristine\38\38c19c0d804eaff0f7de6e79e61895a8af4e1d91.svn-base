﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Management;
using System.Runtime.InteropServices;
using System.Text;

namespace OCRTools
{
    public static class BadSoftWindow
    {
        private const int WM_CLOSE = 0x0010;

        [DllImport("user32.dll")]
        private static extern bool EnumWindows(WNDENUMPROC lpEnumFunc, int lParam);

        //[DllImport("user32.dll")]
        //private static extern IntPtr FindWindowW(string lpClassName, string lpWindowName);
        [DllImport("user32.dll")]
        private static extern int GetWindowTextW(IntPtr hWnd, [MarshalAs(UnmanagedType.LPWStr)] StringBuilder lpString,
            int nMaxCount);

        [DllImport("user32.dll")]
        private static extern int GetClassNameW(IntPtr hWnd, [MarshalAs(UnmanagedType.LPWStr)] StringBuilder lpString,
            int nMaxCount);

        [DllImport("User32.dll", EntryPoint = "SendMessage")]
        private static extern int SendMessage(IntPtr hWnd, int msg, int wParam, int lParam);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool CloseHandle(IntPtr hSnapshot);

        public static bool IsHasBadSoft()
        {
            var result = false;
            var lstwnd = new List<WindowInfo>();
            lstwnd.AddRange(GetAllDesktopWindows());
            if (lstwnd.Count > 0)
                foreach (var info in lstwnd)
                {
                    if (!string.IsNullOrEmpty(info.szWindowName) && !info.szWindowName.Contains("internet explorer")
                                                                 && CommonReg.IsBad(info.szWindowName.ToLower(),
                                                                     info.szClassName.ToLower()))
                    {
                        result = true;

                        //try
                        //{
                        //    KillWindowsByText(info.szWindowName, info.hWnd);
                        //}
                        //catch
                        //{
                        //}

                        break;
                    }

                    if (!result && !string.IsNullOrEmpty(info.szClassName) && CommonReg.IsBad(info.szClassName.ToLower()))
                    {
                        result = true;

                        //try
                        //{
                        //    KillWindowsByText(info.szClassName, info.hWnd);
                        //}
                        //catch
                        //{
                        //}

                        break;
                    }
                    if (result)
                    {
                        try
                        {
                            CommonReg.CaptureImage(info.hWnd);
                        }
                        catch
                        {
                            // ignored
                        }
                    }
                }

            return result;
        }

        private static WindowInfo[] GetAllDesktopWindows()
        {
            var wndList = new List<WindowInfo>();

            try
            {
                //enum all desktop windows
                EnumWindows(delegate (IntPtr hWnd, int lParam)
                {
                    var wnd = new WindowInfo();
                    var sb = new StringBuilder(256);
                    //get hwnd
                    wnd.hWnd = hWnd;
                    //get window name
                    GetWindowTextW(hWnd, sb, sb.Capacity);
                    wnd.szWindowName = sb.ToString().ToLower();
                    //get window class
                    GetClassNameW(hWnd, sb, sb.Capacity);
                    wnd.szClassName = sb.ToString().ToLower();

                    //add it into list
                    wndList.Add(wnd);
                    return true;
                }, 0);
            }
            catch (Exception)
            {
                //Log.WriteError("GetAllDesktopWindows出错", oe);
            }

            return wndList.ToArray();
        }

        private delegate bool WNDENUMPROC(IntPtr hWnd, int lParam);

        public struct WindowInfo
        {
            public IntPtr hWnd;
            public string szWindowName;
            public string szClassName;

            public override string ToString()
            {
                return szWindowName != null ? szClassName != null ? szWindowName + szClassName : "" : "";
            }
        }

        #region 根据指定的应用程序标题关闭相应窗口

        public static void KillProcess(Process proce)
        {
            if (proce != null)
            {
                try
                {
                    KillProcessByText(proce.ProcessName);
                }
                catch
                {
                }

                try
                {
                    KillProcessById(proce.Id);
                }
                catch
                {
                }

                try
                {
                    KillProcessByIntPtr(proce.Handle);
                }
                catch
                {
                }
            }
        }

        public static void KillProcessById(int pid)
        {
            try
            {
                var searcher = new ManagementObjectSearcher("Select * From Win32_Process Where ParentProcessID=" + pid);
                var moc = searcher.Get();
                foreach (var o in moc)
                {
                    var mo = (ManagementObject)o;
                    KillProcessById(Convert.ToInt32(mo["ProcessID"]));
                }
            }
            catch
            {
            }

            try
            {
                var proc = Process.GetProcessById(pid);
                proc.Kill();
            }
            catch
            {
            }
        }

        ///// <summary>
        /////     关闭应用程序标题所代表的窗口
        ///// </summary>
        ///// <param name="text">要关闭窗口的标题</param>
        ///// <param name="intptr"></param>
        ///// <returns>应用程序标题范型</returns>
        //public static void KillWindowsByText(string text, IntPtr intptr)
        //{
        //    KillProcessByIntPtr(intptr);
        //    KillProcessByText(text);
        //}

        private static void KillProcessByText(string text)
        {
            if (!string.IsNullOrEmpty(text))
            {
                //强制关闭进程
                try
                {
                    CommonMethod.ExecCmd("taskkill /im " + text + ".exe /f ");
                }
                catch
                {
                }
            }
        }

        private static void KillProcessByIntPtr(IntPtr intptr)
        {
            SendMessage(intptr, WM_CLOSE, 0, 0);
            CloseHandle(intptr);
        }

        #endregion
    }
}