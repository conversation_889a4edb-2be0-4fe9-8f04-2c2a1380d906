﻿using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Drawing.Text;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;

namespace OCRTools.Common
{
    public class ImageProcessHelper
    {
        private const string STR_DOC_IMG_SPILT = "\"image\":\"";
        private const string STR_IMG_SPILT = "\"image\":\"";

        public static readonly string[] ImageFileExtensions =
            {"jpg", "jpeg", "png", "gif", "bmp", "ico", "tif", "tiff"};

        public static bool IsImagesEqual(Bitmap bmp1, Bitmap bmp2)
        {
            if (bmp1 != null && bmp2 != null && bmp1.Width == bmp2.Width && bmp1.Height == bmp2.Height)
            {
                BitmapData bd1 = bmp1.LockBits(new Rectangle(0, 0, bmp1.Width, bmp1.Height), ImageLockMode.ReadOnly, PixelFormat.Format32bppArgb);
                BitmapData bd2 = bmp2.LockBits(new Rectangle(0, 0, bmp2.Width, bmp2.Height), ImageLockMode.ReadOnly, PixelFormat.Format32bppArgb);

                try
                {
                    return NativeMethods.memcmp(bd1.Scan0, bd2.Scan0, bd1.Stride * bmp1.Height) == 0;
                }
                finally
                {
                    bmp1.UnlockBits(bd1);
                    bmp2.UnlockBits(bd2);
                }
            }

            return false;
            //using (var unsafeBitmap1 = new UnsafeBitmap(bmp1))
            //using (var unsafeBitmap2 = new UnsafeBitmap(bmp2))
            //{
            //    return unsafeBitmap1 == unsafeBitmap2;
            //}
        }

        public static Bitmap ResizeImage(Bitmap bmp, Size size, bool allowEnlarge, bool centerImage = true)
        {
            return ResizeImage(bmp, size.Width, size.Height, allowEnlarge, centerImage);
        }

        public static Bitmap ResizeImage(Bitmap bmp, int width, int height, bool allowEnlarge, bool centerImage = true)
        {
            return ResizeImage(bmp, width, height, allowEnlarge, centerImage, Color.Transparent);
        }

        public static Bitmap ResizeImage(Bitmap bmp, int width, int height, bool allowEnlarge, bool centerImage,
            Color backColor)
        {
            double ratio;
            int newWidth, newHeight;

            if (!allowEnlarge && bmp.Width <= width && bmp.Height <= height)
            {
                ratio = 1.0;
                newWidth = bmp.Width;
                newHeight = bmp.Height;
            }
            else
            {
                var ratioX = (double)width / bmp.Width;
                var ratioY = (double)height / bmp.Height;
                ratio = ratioX < ratioY ? ratioX : ratioY;
                newWidth = (int)(bmp.Width * ratio);
                newHeight = (int)(bmp.Height * ratio);
            }

            var newX = 0;
            var newY = 0;

            if (centerImage)
            {
                newX += (int)((width - bmp.Width * ratio) / 2);
                newY += (int)((height - bmp.Height * ratio) / 2);
            }

            var bmpResult = new Bitmap(width, height, PixelFormat.Format32bppArgb);
            bmpResult.SetResolution(bmp.HorizontalResolution, bmp.VerticalResolution);

            using (var g = Graphics.FromImage(bmpResult))
            {
                if (backColor.A > 0) g.Clear(backColor);

                g.SetHighQuality();
                g.DrawImage(bmp, newX, newY, newWidth, newHeight);
            }

            return bmpResult;
        }

        public static Bitmap ResizeImage(Bitmap bmp, int width, int height, Color color, bool isToCircle = true,
            bool isShadow = false, decimal shadowWidth = 8, bool isActive = false)
        {
            if (width < 1 || height < 1) // || (bmp.Width == width && bmp.Height == height))
                return bmp;

            var bmpResult = new Bitmap(width, height, PixelFormat.Format32bppArgb);
            bmpResult.SetResolution(bmp.HorizontalResolution, bmp.VerticalResolution);

            using (bmp)
            using (var g = Graphics.FromImage(bmpResult))
            {
                g.Clear(color);
                g.SetHighQuality();

                using (var ia = new ImageAttributes())
                {
                    ia.SetWrapMode(WrapMode.TileFlipXY);
                    g.DrawImage(bmp, new Rectangle(0, 0, width, height), 0, 0, bmp.Width, bmp.Height,
                        GraphicsUnit.Pixel, ia);
                }
            }

            if (isToCircle) bmpResult = ToCircle(bmpResult, color);

            if (isShadow || isActive)
                return DrawImageShadow(bmpResult, isToCircle, isShadow, (int)shadowWidth, isActive);
            return bmpResult;
        }

        private static Bitmap DrawImageShadow(Bitmap bmpResult, bool isToCircle, bool isShadow = false,
            int shadowWidth = 8, bool isActive = false)
        {
            var bmpTmp = new Bitmap(bmpResult.Width + (isShadow ? shadowWidth : 0) * 2,
                bmpResult.Height + (isShadow ? shadowWidth : 0) * 2);
            using (var g = Graphics.FromImage(bmpTmp))
            {
                g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                g.SmoothingMode = SmoothingMode.HighQuality;
                g.PixelOffsetMode = PixelOffsetMode.HighQuality;
                g.CompositingQuality = CompositingQuality.HighQuality;
                g.CompositingMode = CompositingMode.SourceOver;
                g.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
                var shadowMore = isActive ? 2 : 0;

                if (isShadow)
                {
                    var normalBaseColor =
                        isToCircle ? bmpResult.GetPixel(bmpResult.Width / 2, 1) : bmpResult.GetPixel(1, 1);
                    if (normalBaseColor.A == 255)
                    {
                        var baseColor = isActive ? StaticValue.ShadowActiveColor : normalBaseColor;
                        for (var i = 1; i <= shadowWidth + shadowMore; i++)
                        {
                            var borderColor =
                                Color.FromArgb((int)Math.Min(255 / shadowWidth * (isActive ? 3 : 1.5), 255),
                                    baseColor);
                            using (var pen = new Pen(borderColor, 1))
                            {
                                var rect = new Rectangle(i, i, bmpTmp.Width - i * 2, bmpTmp.Height - i * 2);
                                if (isToCircle)
                                    g.DrawEllipse(pen, rect);
                                else
                                    g.DrawRectangle(pen, rect);
                            }
                        }
                    }
                }

                g.DrawImage(bmpResult
                    , new Rectangle((isShadow ? shadowWidth : 0) + shadowMore,
                        (isShadow ? shadowWidth : 0) + shadowMore, bmpResult.Width - shadowMore * 2,
                        bmpResult.Height - shadowMore * 2)
                    , 0, 0, bmpResult.Width, bmpResult.Height, GraphicsUnit.Pixel);
                return bmpTmp;
            }
        }

        public static Bitmap ToCircle(Bitmap bitmap, Color color)
        {
            var bmpResult = new Bitmap(bitmap.Width, bitmap.Height, PixelFormat.Format32bppArgb);
            using (var g = Graphics.FromImage(bmpResult))
            {
                g.Clear(color);

                g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                g.SmoothingMode = SmoothingMode.HighQuality;
                g.PixelOffsetMode = PixelOffsetMode.HighQuality;
                g.CompositingQuality = CompositingQuality.HighQuality;
                g.CompositingMode = CompositingMode.SourceOver;
                g.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;

                using (var br = new TextureBrush(bitmap, WrapMode.Clamp,
                    new RectangleF(0, 0, bitmap.Width, bitmap.Height)))
                {
                    br.ScaleTransform(1, 1);
                    g.FillEllipse(br, new Rectangle(Point.Empty, bitmap.Size));
                }
            }

            return bmpResult;
        }

        public static Bitmap CreateCheckerPattern(int width, int height, Color checkerColor1, Color checkerColor2)
        {
            var bmp = new Bitmap(width * 2, height * 2);

            using (var g = Graphics.FromImage(bmp))
            using (Brush brush1 = new SolidBrush(checkerColor1))
            using (Brush brush2 = new SolidBrush(checkerColor2))
            {
                g.FillRectangle(brush1, 0, 0, width, height);
                g.FillRectangle(brush1, width, height, width, height);
                g.FillRectangle(brush2, width, 0, width, height);
                g.FillRectangle(brush2, 0, height, width, height);
            }

            return bmp;
        }

        public static Bitmap CreateCheckerPattern(int width, int height)
        {
            return CreateCheckerPattern(width, height, SystemColors.ControlLight, SystemColors.ControlLightLight);
        }

        public static Bitmap DrawCheckers(int width, int height)
        {
            return DrawCheckers(width, height, 10, SystemColors.ControlLight, SystemColors.ControlLightLight);
        }

        public static Bitmap DrawCheckers(int width, int height, int checkerSize, Color checkerColor1,
            Color checkerColor2)
        {
            var bmp = new Bitmap(width, height);

            using (var g = Graphics.FromImage(bmp))
            using (Image checker = CreateCheckerPattern(checkerSize, checkerSize, checkerColor1, checkerColor2))
            using (Brush checkerBrush = new TextureBrush(checker, WrapMode.Tile))
            {
                g.FillRectangle(checkerBrush, new Rectangle(0, 0, bmp.Width, bmp.Height));
            }

            return bmp;
        }

        public static Image ZoomAndCropImage(Image inputImage, float zoomFactor)
        {
            // 计算放大后的图片大小
            int newWidth = (int)(inputImage.Width * zoomFactor);
            int newHeight = (int)(inputImage.Height * zoomFactor);

            // 对输入图片进行放大处理
            using (Bitmap scaledImage = new Bitmap(inputImage, newWidth, newHeight))
            {
                // 计算截取区域位置
                int left = (newWidth - inputImage.Width) / 2;
                int top = (newHeight - inputImage.Height) / 2;

                // 创建新的图像对象，并截取与输入图片大小相同的区域
                Bitmap croppedImage = new Bitmap(inputImage.Width, inputImage.Height);
                using (Graphics graphics = Graphics.FromImage(croppedImage))
                {
                    graphics.DrawImage(scaledImage, new Rectangle(0, 0, croppedImage.Width, croppedImage.Height),
                        new Rectangle(left, top, inputImage.Width, inputImage.Height), GraphicsUnit.Pixel);
                }

                // 返回截取后的图片
                return croppedImage;
            }
        }

        public static Bitmap ResizeImageLimit(Bitmap bmp, Size size, bool isAlph = false)
        {
            return ResizeImageLimit(bmp, size.Width, size.Height, isAlph);
        }

        /// <summary>If image size is bigger than specified size then resize it and keep aspect ratio else return image.</summary>
        public static Bitmap ResizeImageLimit(Bitmap bmp, int width, int height, bool isAlph = false)
        {
            if (bmp.Width <= width && bmp.Height <= height) return bmp;

            var ratioX = (double)width / bmp.Width;
            var ratioY = (double)height / bmp.Height;

            if (ratioX < ratioY)
                height = (int)Math.Round(bmp.Height * ratioX);
            else if (ratioX > ratioY) width = (int)Math.Round(bmp.Width * ratioY);

            return ResizeImage(bmp, width, height, isAlph);
        }

        public static Bitmap LoadImage(string filePath)
        {
            try
            {
                if (!string.IsNullOrEmpty(filePath) && IsImageFile(filePath) && File.Exists(filePath))
                {
                    return (Bitmap)Image.FromStream(new MemoryStream(File.ReadAllBytes(filePath)));
                }
            }
            catch { }

            return null;
        }

        public static Bitmap RotateImage(Image image, float angle)
        {
            if (image == null)
                throw new ArgumentNullException("image");
            float dx = image.Width / 2.0f;
            float dy = image.Height / 2.0f;

            Bitmap rotatedBmp = new Bitmap(image.Width, image.Height);
            rotatedBmp.SetResolution(image.HorizontalResolution, image.VerticalResolution);
            using (Graphics g = Graphics.FromImage(rotatedBmp))
            {
                g.TranslateTransform(dx, dy);
                g.RotateTransform(angle);
                g.TranslateTransform(-dx, -dy);
                g.DrawImage(image, new PointF(0, 0));
            }
            return rotatedBmp;
        }

        public static string GetFilenameExtension(string filePath, bool includeDot = false,
            bool checkSecondExtension = true)
        {
            var extension = "";

            if (!string.IsNullOrEmpty(filePath))
            {
                var pos = filePath.LastIndexOf('.');

                if (pos >= 0)
                {
                    extension = filePath.Substring(pos + 1);

                    if (checkSecondExtension)
                    {
                        filePath = filePath.Remove(pos);
                        var extension2 = GetFilenameExtension(filePath, false, false);

                        if (!string.IsNullOrEmpty(extension2))
                            foreach (var knownExtension in new[] { "tar" })
                                if (extension2.Equals(knownExtension, StringComparison.OrdinalIgnoreCase))
                                {
                                    extension = extension2 + "." + extension;
                                    break;
                                }
                    }

                    if (includeDot) extension = "." + extension;
                }
            }

            return extension;
        }

        public static bool CheckExtension(string filePath, IEnumerable<string> extensions)
        {
            var ext = GetFilenameExtension(filePath);

            if (!string.IsNullOrEmpty(ext))
                return extensions.Any(x => ext.Equals(x, StringComparison.OrdinalIgnoreCase));

            return false;
        }

        public static bool IsImageFile(string filePath)
        {
            return CheckExtension(filePath, ImageFileExtensions);
        }

        /// <summary>
        /// 获取图像
        /// </summary>
        public static Bitmap GetResourceImage(string name)
        {
            try
            {
                return Properties.Resources.ResourceManager.GetObject(name) as Bitmap;
            }
            catch (Exception oe)
            {
                Log.WriteError("GetResourceImage:" + name, oe);
            }
            return null;
        }

        public static Bitmap ScaleImage(Image image, float dpiScale = 1)
        {
            if (image == null)
            {
                return null;
            }
            if (Equals(dpiScale, 1f))
            {
                return new Bitmap(image);
            }
            var newSize = new Size((int)(image.Size.Width * dpiScale), (int)(image.Size.Height * dpiScale));
            var newBitmap = new Bitmap(newSize.Width, newSize.Height);

            using (var g = Graphics.FromImage(newBitmap))
            {
                g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                g.CompositingQuality = CompositingQuality.HighQuality;
                g.SmoothingMode = SmoothingMode.HighQuality;
                g.PixelOffsetMode = PixelOffsetMode.HighQuality;

                g.CompositingQuality = CompositingQuality.HighQuality;

                g.CompositingMode = CompositingMode.SourceCopy;
                g.DrawImage(image, new Rectangle(new Point(), newSize));
            }

            //image.Dispose();

            return newBitmap;
        }

        public static Bitmap GetImageByBase64AndReverse(string base64, bool isReverse = true)
        {
            Bitmap image = null;
            if (!string.IsNullOrEmpty(base64))
            {
                image = Base64StringToImage(base64);
                if (isReverse && CommonSetting.夜间模式)
                    image = InverseImage(new Bitmap(image));
            }
            return image;
        }

        public static Bitmap InverseImage(Bitmap source)
        {
            //create a blank bitmap the same size as original
            Bitmap newBitmap = new Bitmap(source.Width, source.Height);

            //get a graphics object from the new image
            using (Graphics g = Graphics.FromImage(newBitmap))
            {
                // create the negative color matrix
                ColorMatrix colorMatrix = new ColorMatrix(
                   new[]
                   {
                  new float[] {-1, 0, 0, 0, 0},
                  new float[] {0, -1, 0, 0, 0},
                  new float[] {0, 0, -1, 0, 0},
                  new float[] {0, 0, 0, 1, 0},
                  new float[] {1, 1, 1, 0, 1}
                   });

                //colorMatrix.Matrix00 = colorMatrix.Matrix11 = colorMatrix.Matrix22 = -1f;
                //colorMatrix.Matrix33 = colorMatrix.Matrix44 = 1f;

                // create some image attributes
                ImageAttributes attributes = new ImageAttributes();

                attributes.SetColorMatrix(colorMatrix);

                g.DrawImage(source, new Rectangle(0, 0, source.Width, source.Height),
                            0, 0, source.Width, source.Height, GraphicsUnit.Pixel, attributes);
            }

            return newBitmap;
        }

        /// <summary>
        ///     反色（指针）
        /// </summary>
        /// <param name="curBitmpap"></param>
        /// <returns></returns>
        public static Bitmap BitInverseByPointer(Bitmap curBitmpap)
        {
            if (curBitmpap != null)
                try
                {
                    var srcdat = curBitmpap.LockBits(new Rectangle(Point.Empty, curBitmpap.Size),
                        ImageLockMode.ReadWrite, PixelFormat.Format24bppRgb); // 锁定位图
                    unsafe // 不安全代码
                    {
                        var pix = (byte*)srcdat.Scan0; // 像素首地址
                        for (var i = 0; i < srcdat.Stride * srcdat.Height; i++) pix[i] = (byte)(255 - pix[i]);
                        curBitmpap.UnlockBits(srcdat); // 解锁
                    }
                }
                catch (Exception oe)
                {
                    Log.WriteError("BitInverseByPointer", oe);
                }
            return curBitmpap;
        }

        /// <summary>
        ///     对比度增强（指针）
        /// </summary>
        /// <returns></returns>
        public static unsafe Bitmap BitContrastByPointer(Bitmap bitmap, int degree = 90)
        {
            if (bitmap != null)
                try
                {
                    var num2 = (100.0 + degree) / 100.0;
                    num2 *= num2;
                    var width = bitmap.Width;
                    var height = bitmap.Height;
                    var bitmapdata = bitmap.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.ReadWrite,
                        PixelFormat.Format24bppRgb);
                    var numPtr = (byte*)bitmapdata.Scan0;

                    var offset = bitmapdata.Stride - width * 3;
                    for (var i = 0; i < height; i++)
                    {
                        for (var j = 0; j < width; j++)
                        {
                            for (var k = 0; k < 3; k++)
                            {
                                var num = ((numPtr[k] / 255.0 - 0.5) * num2 + 0.5) * 255.0;
                                if (num < 0.0) num = 0.0;
                                if (num > 255.0) num = 255.0;
                                numPtr[k] = (byte)num;
                            }

                            numPtr += 3;
                        }

                        numPtr += offset;
                    }

                    bitmap.UnlockBits(bitmapdata);
                }
                catch (Exception oe)
                {
                    Log.WriteError("BitContrastByPointer", oe);
                }

            return bitmap;
        }

        public static Bitmap CropBitmap(Bitmap bmp, Rectangle rect)
        {
            if (bmp != null && rect.X >= 0 && rect.Y >= 0 && rect.Width > 0 && rect.Height > 0 &&
                new Rectangle(0, 0, bmp.Width, bmp.Height).Contains(rect)) return bmp.Clone(rect, bmp.PixelFormat);

            return null;
        }

        public static Image ProcessImage(Bitmap originImage, ImageProcessType imgType)
        {
            Image img = originImage;
            switch (imgType)
            {
                case ImageProcessType.高对比度:
                    img = BitContrastByPointer(originImage);
                    break;
                case ImageProcessType.图像反色:
                    img = BitInverseByPointer(originImage);
                    break;
                case ImageProcessType.文档矫正:
                    img = EnhanceDocImage(ImageToBase64(originImage));
                    if (img == null)
                        img = EnhanceDocImage1(ImageToBase64(originImage));
                    if (img == null)
                    {
                        img = DetectImageLocal(originImage);
                    }
                    break;
                case ImageProcessType.图像增强:
                    img = EnhanceImage(ImageToBase64(originImage));
                    break;
                case ImageProcessType.去屏幕纹:
                    img = DemoireImage(ImageToBase64(originImage));
                    break;
                case ImageProcessType.老照片修复:
                    img = PhotoRestoration(ImageToBase64(originImage));
                    break;
            }

            return img ?? originImage;
        }

        public enum ImageDectType
        {
            TextIn,
            DocScanner,
            助手文档
        }

        public static bool DectImage(string fileName, string toPath, ImageDectType dectType)
        {
            using (var image = Image.FromFile(fileName))
            {
                using (var bitmap = new Bitmap(image))
                {
                    switch (dectType)
                    {
                        case ImageDectType.TextIn:
                            EnhanceDocImage(ImageToBase64(bitmap))?.SaveFileWithOutConfirm(toPath);
                            break;
                        case ImageDectType.DocScanner:
                            EnhanceDocImage1(ImageToBase64(bitmap))?.SaveFileWithOutConfirm(toPath);
                            break;
                        case ImageDectType.助手文档:
                            DetectImageLocal(bitmap, ref fileName, ref toPath);
                            break;
                    }
                }
            }
            return File.Exists(toPath);
        }

        public static Bitmap DetectImageLocal(Bitmap oriImage)
        {
            var strLocalFile = "";
            var strNewFile = "";
            return DetectImageLocal(oriImage, ref strLocalFile, ref strNewFile);
        }

        public static Bitmap DetectImageLocal(Bitmap oriImage, ref string strLocalFile, ref string strNewFile)
        {
            if (CommonUpdate.CheckInstallLocalDectExe())
            {
                try
                {
                    strLocalFile = string.IsNullOrEmpty(strLocalFile) ? oriImage.SaveFileWithOutConfirm(null, true) : strLocalFile;
                    strNewFile = string.IsNullOrEmpty(strNewFile) ? strLocalFile : strNewFile;
                    CommonMethod.ExecCmd(string.Format("{0} -g c -o \"{1}\" \"{2}\"", CommonString.DefaultLocalDectExePath, strNewFile, strLocalFile));
                    if (File.Exists(strNewFile))
                        return new Bitmap(Image.FromFile(strNewFile));
                }
                catch (Exception oe)
                {
                    Log.WriteError("DetectImageLocal", oe);
                }
            }
            return null;
        }

        /// <summary>
        ///去屏幕纹
        /// </summary>
        /// <param name="strBase64"></param>
        /// <returns></returns>
        public static Bitmap DemoireImage(string strBase64)
        {
            var result = "";

            var strTmp = WebClientExt.GetHtml("https://api.textin.com/home/<USER>", "", "", strBase64, "", 10);

            if (!string.IsNullOrEmpty(strTmp) && strTmp.Contains(STR_DOC_IMG_SPILT))
            {
                result = strTmp.Substring(strTmp.IndexOf(STR_DOC_IMG_SPILT) + STR_DOC_IMG_SPILT.Length);
                result = result.Substring(0, result.IndexOf("\""));
            }

            if (!string.IsNullOrEmpty(result)) return Base64StringToImage(System.Text.RegularExpressions.Regex.Unescape(result));
            return null;
        }

        /// <summary>
        ///老照片修复
        /// </summary>
        /// <param name="strBase64"></param>
        /// <returns></returns>
        public static Bitmap PhotoRestoration(string strBase64)
        {
            var result = "";

            var strTmp = WebClientExt.GetHtml("https://api.textin.com/home/<USER>/photo_restoration", "", "", strBase64, "", 10);

            if (!string.IsNullOrEmpty(strTmp) && strTmp.Contains(STR_DOC_IMG_SPILT))
            {
                result = strTmp.Substring(strTmp.IndexOf(STR_DOC_IMG_SPILT) + STR_DOC_IMG_SPILT.Length);
                result = result.Substring(0, result.IndexOf("\""));
            }

            if (!string.IsNullOrEmpty(result)) return Base64StringToImage(System.Text.RegularExpressions.Regex.Unescape(result));
            return null;
        }

        /// <summary>
        ///文档图像矫正
        ///https://doctrp.top/
        ///https://zhuanlan.zhihu.com/p/626528562
        /// </summary>
        /// <param name="strBase64"></param>
        /// <returns></returns>
        public static Bitmap EnhanceDocImage1(string strBase64)
        {
            var file = new UploadFileInfo()
            {
                Name = "image",
                Filename = "1.png",
                ContentType = "image/png",
                Stream = new MemoryStream(Convert.FromBase64String(strBase64))
            };
            var byts = UploadFileRequest.PostByte("https://doctrp.top/api/rectification", new[] { file }, null);

            if (byts?.Length > 0) return ByteToImage(byts);
            return null;
        }

        /// <summary>
        ///文档图像矫正
        ///https://www.textin.com/experience/dewarp
        /// </summary>
        /// <param name="strBase64"></param>
        /// <returns></returns>
        public static Bitmap EnhanceDocImage(string strBase64)
        {
            var result = "";

            var strTmp = WebClientExt.GetHtml("https://api.textin.com/home/<USER>", "", "", strBase64, "", 10);

            if (!string.IsNullOrEmpty(strTmp) && strTmp.Contains(STR_DOC_IMG_SPILT))
            {
                result = strTmp.Substring(strTmp.IndexOf(STR_DOC_IMG_SPILT) + STR_DOC_IMG_SPILT.Length);
                result = result.Substring(0, result.IndexOf("\""));
            }

            if (!string.IsNullOrEmpty(result)) return Base64StringToImage(System.Text.RegularExpressions.Regex.Unescape(result));
            return null;
        }

        /// <summary>
        ///照片图像增强
        /// </summary>
        /// <param name="strBase64"></param>
        /// <returns></returns>
        public static Bitmap EnhanceImage(string strBase64)
        {
            var result = "";
            var strPost = strBase64;
            var strTmp = WebClientExt.GetHtml("https://api.textin.com/home/<USER>", "", "", strPost, "", 10);

            if (!string.IsNullOrEmpty(strTmp) && strTmp.Contains(STR_DOC_IMG_SPILT))
            {
                result = strTmp.Substring(strTmp.IndexOf(STR_DOC_IMG_SPILT) + STR_DOC_IMG_SPILT.Length);
                result = result.Substring(0, result.IndexOf("\""));
            }

            if (!string.IsNullOrEmpty(result)) return Base64StringToImage(System.Text.RegularExpressions.Regex.Unescape(result));
            return null;
        }

        public static string ImageToBase64(Image image)
        {
            var byts = ImageToByte(image);
            return byts?.Length > 0 ? Convert.ToBase64String(byts) : null;
        }

        public static byte[] ImageToByte(Image image)
        {
            using (var ms = new MemoryStream())
            {
                if (image.RawFormat.Guid == ImageFormat.Gif.Guid)
                    image.Save(ms, ImageFormat.Gif);
                else if (image.RawFormat.Guid == ImageFormat.Bmp.Guid)
                    image.Save(ms, ImageFormat.Bmp);
                else if (image.RawFormat.Guid == ImageFormat.Png.Guid)
                    image.Save(ms, ImageFormat.Png);
                else if (image.RawFormat.Guid == ImageFormat.Tiff.Guid)
                    image.Save(ms, ImageFormat.Tiff);
                else
                    image.Save(ms, ImageFormat.Jpeg);

                return ms.ToArray();
            }
        }

        public static Bitmap UrlToBitmap(string url)
        {
            var file = string.Format("{0}{1}", CommonString.DataPath, CommonMethod.SubString(url.Substring(url.IndexOf("://") + 3), "/").Replace("/", "\\"));
            try
            {
                if (!File.Exists(file))
                {
                    var byts = WebClientExt.GetOneClient().DownloadData(url);
                    if (byts != null && byts.Length > 0)
                    {
                        var dict = Path.GetDirectoryName(file);
                        if (!Directory.Exists(dict))
                            Directory.CreateDirectory(dict);
                        File.WriteAllBytes(file, byts);
                    }
                }
            }
            catch { }
            return File.Exists(file) ? ByteToImage(File.ReadAllBytes(file)) : null;
        }

        public static Bitmap ByteToImage(byte[] bytes)
        {
            Bitmap bmp = null;
            try
            {
                using (var ms = new MemoryStream())
                {
                    ms.Write(bytes, 0, bytes.Length);
                    bmp = new Bitmap(ms);
                }
            }
            catch { }

            return bmp;
        }

        public static Bitmap Base64StringToImage(string base64Img)
        {
            Bitmap bmp = null;
            try
            {
                if (!string.IsNullOrEmpty(base64Img))
                {
                    using (var ms = new MemoryStream())
                    {
                        var bytes = Convert.FromBase64String(base64Img);
                        ms.Write(bytes, 0, bytes.Length);
                        bmp = new Bitmap(ms);
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("Base64StringToImage:" + base64Img, oe);
            }

            return bmp;
        }

    }

    public enum ImageProcessType
    {
        原始图像 = 0,
        图像反色 = 1,
        高对比度 = 2,
        文档矫正 = 50,
        图像增强 = 51,
        去屏幕纹 = 52,
        老照片修复 = 53,
    }
}