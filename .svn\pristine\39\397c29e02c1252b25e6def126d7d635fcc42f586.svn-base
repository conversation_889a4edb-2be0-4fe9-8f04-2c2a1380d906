﻿using MetroFramework;
using MetroFramework.Components;
using MetroFramework.Forms;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    class CommonTheme
    {

        public static MetroStyleManager StyleManager { get; set; }

        public static void InitTheme()
        {
            if (StyleManager == null)
            {
                StyleManager = new MetroStyleManager();
            }
            Enum.TryParse(CommonSetting.主题样式, out MetroColorStyle style);
            StyleManager.Style = style;
            StyleManager.Theme = CommonSetting.夜间模式 ? MetroThemeStyle.Dark : MetroThemeStyle.Light;
        }

        public static Color ReveseColor(Color color)
        {
            return Color.FromArgb(color.A, 255 - color.R, 255 - color.G, 255 - color.B);
        }

        public static void RefreshTheme()
        {
            InitTheme();
            try
            {
                foreach (Form ff in Application.OpenForms)
                    if (!ff.IsDisposed && ff is MetroForm metroForm)
                    {
                        //metroForm.StyleManager = StyleManager;
                        metroForm.UpdateWindowButtonImage();

                        metroForm.OnThemeChange();

                        metroForm.UpdateWindowButtonPosition();

                        metroForm.Invalidate(true);
                    }
            }
            catch { }
        }
    }
}
