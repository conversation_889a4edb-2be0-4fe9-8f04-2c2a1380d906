﻿using MetroFramework.Controls;
using OCRTools.UserControlEx;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    partial class FormSetting
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormSetting));
            this.tbConfig = new System.Windows.Forms.TabControl();
            this.tbNormal = new System.Windows.Forms.TabPage();
            this.grpNormal = new System.Windows.Forms.GroupBox();
            this.cmsNotifyDoubleClick = new MenuButton();
            this.btnLanguage = new MenuButton();
            this.lnkHelpTrans = new LinkLabel();
            this.cmbDoubleClick = new MenuButton();
            this.lblDbClickTray = new System.Windows.Forms.Label();
            this.chkShowNotify = new System.Windows.Forms.CheckBox();
            this.chkShowTool = new System.Windows.Forms.CheckBox();
            this.chkAutoStart = new CheckBoxWithTip();
            this.lblDbClickToolBar = new System.Windows.Forms.Label();
            this.chkRegisteMenu = new CheckBoxWithTip();
            this.chkStartMainWindow = new CheckBoxWithTip();
            this.chkFormTopMost = new System.Windows.Forms.CheckBox();
            this.grpJieMianFont = new System.Windows.Forms.GroupBox();
            this.btnContentDefault = new SkinButton();
            this.btnContentBackColor = new SkinButton();
            this.btnContentFont = new SkinButton();
            this.btnContentFontColor = new SkinButton();
            this.lblContentLable = new System.Windows.Forms.Label();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.tabUI = new System.Windows.Forms.TabPage();
            this.gpDayNightMode = new System.Windows.Forms.GroupBox();
            this.cmbDark = new MenuButton();
            this.lblDayNithtMode = new System.Windows.Forms.Label();
            this.chkAutoColorInversion = new CheckBoxWithTip();
            this.gpUINormal = new System.Windows.Forms.GroupBox();
            this.btnImageViewBackColor = new SkinButton();
            this.picLoadingImage = new System.Windows.Forms.PictureBox();
            this.cmbImageViewBackStyle = new MenuButton();
            this.cmbMouseMove = new MenuButton();
            this.cmbStyles = new MenuButton();
            this.chkFollowSysDPI = new CheckBoxWithTip();
            this.chkWhiteBoardUseColor = new CheckBoxWithTip();
            this.chkClickCopy = new CheckBoxWithTip();
            this.cmbLoadingType = new MenuButton();
            this.lblThemeStyle = new System.Windows.Forms.Label();
            this.lblImageBack = new System.Windows.Forms.Label();
            this.lblMouseMove = new System.Windows.Forms.Label();
            this.cmbVoice = new MenuButton();
            this.lblVoice = new System.Windows.Forms.Label();
            this.lblLoadingGif = new System.Windows.Forms.Label();
            this.cmbSearch = new MenuButton();
            this.lblSearch = new System.Windows.Forms.Label();
            this.grpToolSet = new System.Windows.Forms.GroupBox();
            this.numToolShadowWidth = new System.Windows.Forms.NumericUpDown();
            this.cmbToolBarSize = new System.Windows.Forms.ComboBox();
            this.cmbWeatherIcon = new MenuButton();
            this.btnDefaultToolBarPicture = new SkinButton();
            this.chkWeatherImage = new System.Windows.Forms.CheckBox();
            this.txtToolBarPicLocation = new System.Windows.Forms.TextBox();
            this.btnToolBarPicture = new SkinButton();
            this.picToolBar = new System.Windows.Forms.PictureBox();
            this.lblDiyToolbarImage = new System.Windows.Forms.Label();
            this.chkToolBarCircle = new System.Windows.Forms.CheckBox();
            this.btnQQ = new SkinButton();
            this.lblToolBarShadowUnit = new System.Windows.Forms.Label();
            this.chkToolShadow = new CheckBoxWithTip();
            this.tbOcr = new System.Windows.Forms.TabPage();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tbOcrSetting = new System.Windows.Forms.TabPage();
            this.gpOcrProcing = new System.Windows.Forms.GroupBox();
            this.cmbOcrType = new MenuButton();
            this.lblOcrStrategy = new System.Windows.Forms.Label();
            this.chkNotShowUIWhenOcr = new CheckBoxWithTip();
            this.chkAutoCompressImg = new CheckBoxWithTip();
            this.gpOcrResult = new System.Windows.Forms.GroupBox();
            this.chkShowTextPreview = new CheckBoxWithTip();
            this.gpCopyResult = new System.Windows.Forms.GroupBox();
            this.cmbCopyMode = new MenuButton();
            this.chkCopyResult = new CheckBoxWithTip();
            this.lblCopyMode = new System.Windows.Forms.Label();
            this.chkCopyOldWhenTrans = new CheckBoxWithTip();
            this.chkTipCopy = new CheckBoxWithTip();
            this.gpTypography = new System.Windows.Forms.GroupBox();
            this.chkRemoveDuplicatePunctuation = new CheckBoxWithTip();
            this.cmbFenDuan = new MenuButton();
            this.chkAutoBiaoDian = new CheckBoxWithTip();
            this.chkTextIndent = new CheckBoxWithTip();
            this.lblSegment = new System.Windows.Forms.Label();
            this.chkAutoSpace = new CheckBoxWithTip();
            this.chkAutoFull2Half = new CheckBoxWithTip();
            this.checkBox11 = new System.Windows.Forms.CheckBox();
            this.chkAutoScaleImg = new CheckBoxWithTip();
            this.tbLocalOcr = new System.Windows.Forms.TabPage();
            this.grpOperate = new System.Windows.Forms.GroupBox();
            this.lnkOpenLocalOcr = new System.Windows.Forms.LinkLabel();
            this.lnkTestLocalOcr = new System.Windows.Forms.LinkLabel();
            this.grpLocalOcrSetting = new System.Windows.Forms.GroupBox();
            this.lnkInstallLocalOcr = new System.Windows.Forms.LinkLabel();
            this.numLocalOcrThread = new System.Windows.Forms.NumericUpDown();
            this.numLocalOcrPort = new System.Windows.Forms.NumericUpDown();
            this.lblMaxOcrThread = new System.Windows.Forms.Label();
            this.lblLocalOcrPort = new System.Windows.Forms.Label();
            this.lblLocalOcrMaxThread = new LabelWithTip();
            this.lblLocalOcrPortHelp = new LabelWithTip();
            this.chkLocalOcrEnable = new CheckBoxWithTip();
            this.grpLocalOcr = new System.Windows.Forms.GroupBox();
            this.tbCapture = new System.Windows.Forms.TabPage();
            this.tabControl2 = new System.Windows.Forms.TabControl();
            this.tbCaptureSetting = new System.Windows.Forms.TabPage();
            this.grpCaptureNormalSetting = new System.Windows.Forms.GroupBox();
            this.numericUpDown1 = new System.Windows.Forms.NumericUpDown();
            this.numCaptureBorderWidth = new System.Windows.Forms.NumericUpDown();
            this.lblCaptureDelay = new System.Windows.Forms.Label();
            this.numMagnifierCount = new System.Windows.Forms.NumericUpDown();
            this.lblMagnifierCount = new System.Windows.Forms.Label();
            this.lblDelay_Second = new LabelWithTip();
            this.btnCaptureBorderColor = new SkinButton();
            this.btnWhiteBoardColor = new SkinButton();
            this.lblWhiteBoardColor = new System.Windows.Forms.Label();
            this.lblCaptureBorder = new System.Windows.Forms.Label();
            this.lblCaptureMouseWheel = new System.Windows.Forms.Label();
            this.chkZoom = new System.Windows.Forms.CheckBox();
            this.lblCaptureBorderWidthUnit = new System.Windows.Forms.Label();
            this.chkCaptureAnimal = new System.Windows.Forms.CheckBox();
            this.chkCircleFangDa = new System.Windows.Forms.CheckBox();
            this.chkCrocessLine = new System.Windows.Forms.CheckBox();
            this.chkAutoWindow = new System.Windows.Forms.CheckBox();
            this.chkAutoControl = new CheckBoxWithTip();
            this.lblCaptureBorderWidth = new System.Windows.Forms.Label();
            this.grpAfterCapture = new System.Windows.Forms.GroupBox();
            this.btnChangeCaptureLocation = new SkinButton();
            this.btnOpenCaptureLocation = new SkinButton();
            this.txtCaptureFileName = new System.Windows.Forms.TextBox();
            this.lblCaptureFileName = new System.Windows.Forms.Label();
            this.lblMaxHistoryCount = new System.Windows.Forms.Label();
            this.numMaxHistoryCount = new System.Windows.Forms.NumericUpDown();
            this.lblFileNameParam = new System.Windows.Forms.Label();
            this.lblFileName = new System.Windows.Forms.Label();
            this.txtCaptureLocation = new System.Windows.Forms.TextBox();
            this.lblFilePath = new System.Windows.Forms.Label();
            this.chkCaptureTip = new CheckBoxWithTip();
            this.chkAutoSaveCapture = new System.Windows.Forms.CheckBox();
            this.chkSaveToClipborad = new System.Windows.Forms.CheckBox();
            this.tbScrollCatpure = new System.Windows.Forms.TabPage();
            this.gbWhileCapturing = new System.Windows.Forms.GroupBox();
            this.cbScrollMethod = new MenuButton();
            this.cbCatpureScroll = new MenuButton();
            this.nudScrollDelay = new System.Windows.Forms.NumericUpDown();
            this.lblScrollDelay = new System.Windows.Forms.Label();
            this.lblScrollMethod = new System.Windows.Forms.Label();
            this.lblScrollSpeed = new Label();
            this.nudScrollSpeed = new NumericUpDown();
            this.gbBeforeCapture = new System.Windows.Forms.GroupBox();
            this.cbScrollTopMethodBeforeCapture = new MenuButton();
            this.nudStartDelay = new System.Windows.Forms.NumericUpDown();
            this.lblScrollTopMethodBeforeCapture = new System.Windows.Forms.Label();
            this.lblStartDelay = new System.Windows.Forms.Label();
            this.lblSelectedRectangle = new System.Windows.Forms.Label();
            this.lblControlText = new System.Windows.Forms.Label();
            this.tbPin = new System.Windows.Forms.TabPage();
            this.grpPinNormalSetting = new System.Windows.Forms.GroupBox();
            this.btnTieTuShadowColor = new SkinButton();
            this.lblPinShadowColor = new System.Windows.Forms.Label();
            this.lblPinShadowBorderUnit = new System.Windows.Forms.Label();
            this.chkTieTuFocus = new System.Windows.Forms.CheckBox();
            this.chkShowTieTuShadow = new System.Windows.Forms.CheckBox();
            this.lblPinShadowBorderWidth = new System.Windows.Forms.Label();
            this.chkTieTuShark = new System.Windows.Forms.CheckBox();
            this.lblNewWindowAction = new System.Windows.Forms.Label();
            this.numShadowWidth = new System.Windows.Forms.NumericUpDown();
            this.chkTieTuUseCaptureLocation = new System.Windows.Forms.CheckBox();
            this.grpTextToImage = new System.Windows.Forms.GroupBox();
            this.numTieTuMaxWidth = new System.Windows.Forms.NumericUpDown();
            this.numTieTuBorderWidth = new System.Windows.Forms.NumericUpDown();
            this.btnTieTuDefaultFont = new SkinButton();
            this.btnTieTuBackColor = new SkinButton();
            this.btnTieTuFont = new SkinButton();
            this.btnTieTuFontColor = new SkinButton();
            this.chkUseContentColor = new System.Windows.Forms.CheckBox();
            this.chkIngoreHtml = new System.Windows.Forms.CheckBox();
            this.lblPinMaxWidth = new System.Windows.Forms.Label();
            this.lblPinMaxWidthUnit = new System.Windows.Forms.Label();
            this.lblPinBorderMargin = new System.Windows.Forms.Label();
            this.lblPinBorderMarginUnit = new System.Windows.Forms.Label();
            this.lblTieTuLabel = new System.Windows.Forms.Label();
            this.tbTools = new System.Windows.Forms.TabPage();
            this.grpColorPicker = new System.Windows.Forms.GroupBox();
            this.grpWhiteBoard = new System.Windows.Forms.GroupBox();
            this.grpMagnifier = new System.Windows.Forms.GroupBox();
            this.cmbHex = new System.Windows.Forms.ComboBox();
            this.lblColorPickerValue = new System.Windows.Forms.Label();
            this.chkHexUpper = new System.Windows.Forms.CheckBox();
            this.grpRuler = new System.Windows.Forms.GroupBox();
            this.chkRuleTopMost = new System.Windows.Forms.CheckBox();
            this.cmbRulerOpacity = new System.Windows.Forms.ComboBox();
            this.lblRulerOpaque = new System.Windows.Forms.Label();
            this.cmbRulerUnit = new MenuButton();
            this.lblDefaultRulerUnit = new System.Windows.Forms.Label();
            this.tbShortKey = new System.Windows.Forms.TabPage();
            this.tabHotKeys = new System.Windows.Forms.TabControl();
            this.tbAbout = new System.Windows.Forms.TabPage();
            this.picIcon = new System.Windows.Forms.PictureBox();
            this.picScrollSpeed = new System.Windows.Forms.PictureBox();
            this.groupBox9 = new System.Windows.Forms.GroupBox();
            this.pictureBox4 = new System.Windows.Forms.PictureBox();
            this.pictureBox7 = new System.Windows.Forms.PictureBox();
            this.pictureBox6 = new System.Windows.Forms.PictureBox();
            this.pictureBox5 = new System.Windows.Forms.PictureBox();
            this.linkLabel2 = new System.Windows.Forms.LinkLabel();
            this.lnkSuggestions = new System.Windows.Forms.LinkLabel();
            this.lnkFeedback = new System.Windows.Forms.LinkLabel();
            this.linkLabel1 = new System.Windows.Forms.LinkLabel();
            this.gpAutoUpdate = new System.Windows.Forms.GroupBox();
            this.gpProxy = new System.Windows.Forms.GroupBox();
            this.numUpdateHour = new System.Windows.Forms.NumericUpDown();
            this.btnCheckUpdate = new SkinButton();
            this.lblUpdateInteval = new System.Windows.Forms.Label();
            this.lblUpdateTimeUnit = new System.Windows.Forms.Label();
            this.chkExpireBeta = new CheckBoxWithTip();
            this.rdoSysProxy = new RadioWithTip();
            this.rdoNoProxy = new RadioWithTip();
            this.chkAutoUpdate = new System.Windows.Forms.CheckBox();
            this.lblWebSite = new System.Windows.Forms.Label();
            this.lblCopyright = new System.Windows.Forms.Label();
            this.btnUpgrade = new SkinButton();
            this.lblVersion = new System.Windows.Forms.Label();
            this.lnkWebSite = new System.Windows.Forms.LinkLabel();
            this.lblName = new System.Windows.Forms.Label();
            this.tipMsg = new System.Windows.Forms.ToolTip(this.components);
            this.tbConfig.SuspendLayout();
            this.tbNormal.SuspendLayout();
            this.grpNormal.SuspendLayout();
            this.grpJieMianFont.SuspendLayout();
            this.tabUI.SuspendLayout();
            this.gpDayNightMode.SuspendLayout();
            this.gpUINormal.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.picLoadingImage)).BeginInit();
            this.grpToolSet.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numToolShadowWidth)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.picToolBar)).BeginInit();
            this.tbOcr.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.tbOcrSetting.SuspendLayout();
            this.gpOcrProcing.SuspendLayout();
            this.gpOcrResult.SuspendLayout();
            this.gpCopyResult.SuspendLayout();
            this.gpTypography.SuspendLayout();
            this.tbLocalOcr.SuspendLayout();
            this.grpOperate.SuspendLayout();
            this.grpLocalOcrSetting.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLocalOcrThread)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numLocalOcrPort)).BeginInit();
            this.tbCapture.SuspendLayout();
            this.tabControl2.SuspendLayout();
            this.tbCaptureSetting.SuspendLayout();
            this.grpCaptureNormalSetting.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDown1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCaptureBorderWidth)).BeginInit();
            this.grpAfterCapture.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxHistoryCount)).BeginInit();
            this.tbScrollCatpure.SuspendLayout();
            this.gbWhileCapturing.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudScrollDelay)).BeginInit();
            this.gbBeforeCapture.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudStartDelay)).BeginInit();
            this.tbPin.SuspendLayout();
            this.grpPinNormalSetting.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numShadowWidth)).BeginInit();
            this.grpTextToImage.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numTieTuMaxWidth)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTieTuBorderWidth)).BeginInit();
            this.tbTools.SuspendLayout();
            this.grpColorPicker.SuspendLayout();
            this.grpWhiteBoard.SuspendLayout();
            this.grpMagnifier.SuspendLayout();
            this.grpRuler.SuspendLayout();
            this.tbShortKey.SuspendLayout();
            this.tbAbout.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.picIcon)).BeginInit();
            this.groupBox9.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox5)).BeginInit();
            this.gpAutoUpdate.SuspendLayout();
            this.gpProxy.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numUpdateHour)).BeginInit();
            this.SuspendLayout();
            // 
            // tbConfig
            // 
            this.tbConfig.Controls.Add(this.tbNormal);
            this.tbConfig.Controls.Add(this.tabUI);
            this.tbConfig.Controls.Add(this.tbOcr);
            this.tbConfig.Controls.Add(this.tbCapture);
            this.tbConfig.Controls.Add(this.tbPin);
            this.tbConfig.Controls.Add(this.tbTools);
            this.tbConfig.Controls.Add(this.tbShortKey);
            this.tbConfig.Controls.Add(this.tbAbout);
            this.tbConfig.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tbConfig.Location = new System.Drawing.Point(10, 60);
            this.tbConfig.BackColor = Color.White;
            this.tbConfig.Name = "tbConfig";
            this.tbConfig.SelectedIndex = 0;
            this.tbConfig.Size = new System.Drawing.Size(395, 426);
            this.tbConfig.TabIndex = 0;
            this.tbConfig.SelectedIndexChanged += new System.EventHandler(this.tbConfig_SelectedIndexChanged);
            this.tbConfig.Font = CommonString.GetSysNormalFont(12F);
            // 
            // tabPage1
            // 
            this.tbNormal.Controls.Add(this.grpNormal);
            this.tbNormal.Controls.Add(this.grpJieMianFont);
            this.tbNormal.Location = new System.Drawing.Point(4, 26);
            this.tbNormal.Padding = new System.Windows.Forms.Padding(3);
            this.tbNormal.Size = new System.Drawing.Size(387, 364);
            this.tbNormal.TabIndex = 0;
            this.tbNormal.Text = "常规";
            this.tbNormal.UseVisualStyleBackColor = true;
            // 
            // grpNormal
            // 
            this.grpNormal.Controls.Add(this.cmsNotifyDoubleClick);
            this.grpNormal.Controls.Add(this.btnLanguage);
            this.grpNormal.Controls.Add(this.lnkHelpTrans);
            this.grpNormal.Controls.Add(this.cmbDoubleClick);
            this.grpNormal.Controls.Add(this.lblDbClickTray);
            this.grpNormal.Controls.Add(this.chkShowNotify);
            this.grpNormal.Controls.Add(this.chkShowTool);
            this.grpNormal.Controls.Add(this.chkAutoStart);
            this.grpNormal.Controls.Add(this.lblDbClickToolBar);
            this.grpNormal.Controls.Add(this.chkRegisteMenu);
            this.grpNormal.Controls.Add(this.chkStartMainWindow);
            this.grpNormal.Controls.Add(this.chkFormTopMost);
            this.grpNormal.Controls.Add(this.textBox2);
            this.grpNormal.Controls.Add(this.textBox1);
            this.grpNormal.Location = new System.Drawing.Point(3, 6);
            this.grpNormal.Size = new System.Drawing.Size(374, 152);
            this.grpNormal.TabIndex = 5;
            this.grpNormal.TabStop = false;
            this.grpNormal.Text = "通用";
            // 
            // cmsNotifyDoubleClick
            // 
            this.cmsNotifyDoubleClick.TextAlign = ContentAlignment.MiddleLeft;
            this.cmsNotifyDoubleClick.Location = new System.Drawing.Point(261, 122);
            this.cmsNotifyDoubleClick.Size = new System.Drawing.Size(95, 25);
            this.cmsNotifyDoubleClick.TabIndex = 28;
            this.cmsNotifyDoubleClick.Tag = "双击托盘操作";
            // 
            // btnLanguage
            // 
            this.btnLanguage.Location = new System.Drawing.Point(13, 15);
            this.btnLanguage.Size = new System.Drawing.Size(220, 28);
            this.btnLanguage.TabIndex = 28;
            this.btnLanguage.Tag = "语言";
            this.btnLanguage.Name = "btnLanguage";
            this.btnLanguage.TextImageRelation = TextImageRelation.ImageBeforeText;
            this.btnLanguage.ImageAlign = ContentAlignment.MiddleLeft;
            this.btnLanguage.TextAlign = ContentAlignment.MiddleLeft;
            this.btnLanguage.AutoSizeMode = AutoSizeMode.GrowOnly;
            this.btnLanguage.AccessibleDescription = "";// 
            // lnkHelpTrans
            // 
            this.lnkHelpTrans.AutoSize = true;
            this.lnkHelpTrans.Font = CommonString.GetSysBoldFont(12F);
            this.lnkHelpTrans.Location = new System.Drawing.Point(235, 20);
            this.lnkHelpTrans.Size = new System.Drawing.Size(57, 12);
            this.lnkHelpTrans.TabIndex = 0;
            this.lnkHelpTrans.TabStop = true;
            this.lnkHelpTrans.Text = "帮助我们翻译";
            this.lnkHelpTrans.Click += new System.EventHandler(this.btnHelpTransLanguage_Click);
            // 
            // cmbDoubleClick
            // 
            this.cmbDoubleClick.TextAlign = ContentAlignment.MiddleLeft;
            this.cmbDoubleClick.Location = new System.Drawing.Point(261, 96);
            this.cmbDoubleClick.Size = new System.Drawing.Size(95, 25);
            this.cmbDoubleClick.TabIndex = 28;
            this.cmbDoubleClick.Tag = "双击工具栏操作";
            // 
            // lblDbClickToolBar
            // 
            this.lblDbClickToolBar.AutoSize = false;
            this.lblDbClickToolBar.Location = new System.Drawing.Point(135, 100);
            this.lblDbClickToolBar.Size = new System.Drawing.Size(125, 17);
            this.lblDbClickToolBar.TabIndex = 27;
            this.lblDbClickToolBar.TextAlign = ContentAlignment.MiddleRight;
            this.lblDbClickToolBar.Text = "双击工具栏:";
            // 
            // lblDbClickTray
            // 
            this.lblDbClickTray.AutoSize = false;
            this.lblDbClickTray.Location = new System.Drawing.Point(135, 126);
            this.lblDbClickTray.Size = new System.Drawing.Size(125, 17);
            this.lblDbClickTray.TabIndex = 27;
            this.lblDbClickTray.Text = "双击托盘:";
            this.lblDbClickTray.TextAlign = ContentAlignment.MiddleRight;
            // 
            // chkShowNotify
            // 
            this.chkShowNotify.AutoSize = true;
            this.chkShowNotify.Checked = true;
            this.chkShowNotify.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkShowNotify.Location = new System.Drawing.Point(13, 124);
            this.chkShowNotify.Size = new System.Drawing.Size(99, 21);
            this.chkShowNotify.TabIndex = 29;
            this.chkShowNotify.Text = "显示系统托盘";
            this.chkShowNotify.UseVisualStyleBackColor = true;
            this.chkShowNotify.CheckedChanged += new System.EventHandler(this.chkShowTool_CheckedChanged);
            // 
            // chkShowTool
            // 
            this.chkShowTool.AutoSize = true;
            this.chkShowTool.Checked = true;
            this.chkShowTool.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkShowTool.Location = new System.Drawing.Point(13, 98);
            this.chkShowTool.Size = new System.Drawing.Size(87, 21);
            this.chkShowTool.TabIndex = 29;
            this.chkShowTool.Text = "显示工具栏";
            this.chkShowTool.UseVisualStyleBackColor = true;
            this.chkShowTool.CheckedChanged += new System.EventHandler(this.chkShowTool_CheckedChanged);
            // 
            // chkAutoStart
            // 
            this.chkAutoStart.AutoSize = true;
            this.chkAutoStart.Location = new System.Drawing.Point(13, 46);
            this.chkAutoStart.Size = new System.Drawing.Size(75, 21);
            this.chkAutoStart.TabIndex = 0;
            this.chkAutoStart.Text = "开机启动";
            this.chkAutoStart.UseVisualStyleBackColor = true;
            this.chkAutoStart.TipControl = tipMsg;
            this.chkAutoStart.TipIcon = OCRTools.Properties.Resources.uac;
            this.chkAutoStart.TipText = "功能：设置是否开机自启动。\r\n说明：\r\n      如果杀软拦截，请允许或者添加排除！\r\n      如果权限不足，请尝试右键->以管理员方式打开程序";
            this.chkAutoStart.CheckedChanged += new System.EventHandler(this.chkAutoStart_CheckedChanged);
            // 
            // chkRegisteMenu
            // 
            this.chkRegisteMenu.AutoSize = true;
            this.chkRegisteMenu.Checked = true;
            this.chkRegisteMenu.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkRegisteMenu.Location = new System.Drawing.Point(192, 72);
            this.chkRegisteMenu.Size = new System.Drawing.Size(99, 21);
            this.chkRegisteMenu.TabIndex = 4;
            this.chkRegisteMenu.Text = "注册右键菜单";
            this.chkRegisteMenu.UseVisualStyleBackColor = true;
            this.chkRegisteMenu.TipControl = tipMsg;
            this.chkRegisteMenu.TipText = "功能：给图片文件添加助手相关的右键菜单\r\n说明：注册后，在图片文件上点击右键，可直接操作助手相关功能。\r\n如：使用助手识别文件，预览图片，钉在桌面等。\r\n无论助手" +
        "是否正在运行，随时唤醒，随时生效！\r\n";
            // 
            // chkStartMainWindow
            // 
            this.chkStartMainWindow.AutoSize = true;
            this.chkStartMainWindow.Checked = true;
            this.chkStartMainWindow.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkStartMainWindow.Location = new System.Drawing.Point(13, 72);
            this.chkStartMainWindow.Size = new System.Drawing.Size(123, 21);
            this.chkStartMainWindow.TabIndex = 0;
            this.chkStartMainWindow.Text = "启动后打开主窗体";
            this.chkStartMainWindow.UseVisualStyleBackColor = true;
            this.chkStartMainWindow.TipControl = tipMsg;
            this.chkStartMainWindow.TipText = "功能：设置程序启动后，是否默认打开主窗体。\r\n说明：\r\n      设置不打开，则以静默方式启动，可以双击工具栏/右下角图标打开！";
            // 
            // chkFormTopMost
            // 
            this.chkFormTopMost.AutoSize = true;
            this.chkFormTopMost.Location = new System.Drawing.Point(192, 46);
            this.chkFormTopMost.Size = new System.Drawing.Size(75, 21);
            this.chkFormTopMost.TabIndex = 0;
            this.chkFormTopMost.Text = "窗体置顶";
            this.chkFormTopMost.UseVisualStyleBackColor = true;
            // 
            // grpJieMianFont
            // 
            this.grpJieMianFont.Controls.Add(this.btnContentDefault);
            this.grpJieMianFont.Controls.Add(this.btnContentBackColor);
            this.grpJieMianFont.Controls.Add(this.btnContentFont);
            this.grpJieMianFont.Controls.Add(this.btnContentFontColor);
            this.grpJieMianFont.Controls.Add(this.lblContentLable);
            this.grpJieMianFont.Location = new System.Drawing.Point(3, 170);
            this.grpJieMianFont.Size = new System.Drawing.Size(374, 143);
            this.grpJieMianFont.TabIndex = 26;
            this.grpJieMianFont.TabStop = false;
            this.grpJieMianFont.Text = "通用字体";
            // 
            // btnContentDefault
            // 
            this.btnContentDefault.Location = new System.Drawing.Point(283, 106);
            this.btnContentDefault.Size = new System.Drawing.Size(85, 26);
            this.btnContentDefault.TabIndex = 39;
            this.btnContentDefault.Tag = "";
            this.btnContentDefault.Text = "默认字体";
            this.btnContentDefault.UseVisualStyleBackColor = false;
            this.btnContentDefault.Click += new System.EventHandler(this.btnContentDefault_Click);
            // 
            // btnContentBackColor
            // 
            this.btnContentBackColor.IsColorButton = true;
            this.btnContentBackColor.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.btnContentBackColor.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnContentBackColor.ImageColor = System.Drawing.Color.Black;
            this.btnContentBackColor.Location = new System.Drawing.Point(283, 78);
            this.btnContentBackColor.Size = new System.Drawing.Size(85, 26);
            this.btnContentBackColor.TabIndex = 38;
            this.btnContentBackColor.Tag = "默认背景颜色";
            this.btnContentBackColor.Text = "背景色";
            this.btnContentBackColor.Name = "btnContentBackColor";
            this.btnContentBackColor.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnContentBackColor.UseVisualStyleBackColor = false;
            this.btnContentBackColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // btnContentFont
            // 
            this.btnContentFont.Location = new System.Drawing.Point(283, 22);
            this.btnContentFont.Size = new System.Drawing.Size(85, 26);
            this.btnContentFont.TabIndex = 37;
            this.btnContentFont.TabStop = false;
            this.btnContentFont.Tag = "默认文字字体";
            this.btnContentFont.Text = "字体";
            this.btnContentFont.UseVisualStyleBackColor = false;
            this.btnContentFont.Click += new System.EventHandler(this.btnFont_Click);
            // 
            // btnContentFontColor
            // 
            this.btnContentFontColor.IsColorButton = true;
            this.btnContentFontColor.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.btnContentFontColor.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnContentFontColor.ImageColor = System.Drawing.Color.Black;
            this.btnContentFontColor.Location = new System.Drawing.Point(283, 50);
            this.btnContentFontColor.Size = new System.Drawing.Size(85, 26);
            this.btnContentFontColor.TabIndex = 37;
            this.btnContentFontColor.Tag = "默认文字颜色";
            this.btnContentFontColor.Text = "文本色";
            this.btnContentFontColor.Name = "btnContentFontColor";
            this.btnContentFontColor.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnContentFontColor.UseVisualStyleBackColor = false;
            this.btnContentFontColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // lblContentLable
            // 
            this.lblContentLable.BackColor = Color.White;
            this.lblContentLable.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.lblContentLable.Location = new System.Drawing.Point(12, 21);
            this.lblContentLable.Size = new System.Drawing.Size(266, 111);
            this.lblContentLable.TabIndex = 36;
            this.lblContentLable.Text = "预览\r\nAaOo012345";
            this.lblContentLable.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // textBox2
            // 
            this.textBox2.Location = new System.Drawing.Point(274, 0);
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(100, 23);
            this.textBox2.TabIndex = 42;
            this.textBox2.Tag = "用户名";
            this.textBox2.Visible = false;
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(274, 27);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(100, 23);
            this.textBox1.TabIndex = 41;
            this.textBox1.Tag = "密码";
            this.textBox1.Visible = false;
            // 
            // tabUI
            // 
            this.tabUI.Controls.Add(this.gpDayNightMode);
            this.tabUI.Controls.Add(this.gpUINormal);
            this.tabUI.Controls.Add(this.grpToolSet);
            this.tabUI.Location = new System.Drawing.Point(4, 22);
            this.tabUI.Padding = new System.Windows.Forms.Padding(3);
            this.tabUI.Size = new System.Drawing.Size(387, 368);
            this.tabUI.TabIndex = 3;
            this.tabUI.Text = "界面";
            this.tabUI.UseVisualStyleBackColor = true;
            // 
            // gpDayNightMode
            // 
            this.gpDayNightMode.Controls.Add(this.cmbDark);
            this.gpDayNightMode.Controls.Add(this.lblDayNithtMode);
            this.gpDayNightMode.Controls.Add(this.chkAutoColorInversion);
            this.gpDayNightMode.Location = new System.Drawing.Point(3, 143);
            this.gpDayNightMode.Size = new System.Drawing.Size(374, 53);
            this.gpDayNightMode.TabIndex = 48;
            this.gpDayNightMode.TabStop = false;
            this.gpDayNightMode.Text = "日间/夜间模式";
            // 
            // cmbDark
            // 
            this.cmbDark.TextAlign = ContentAlignment.MiddleLeft;
            this.cmbDark.Location = new System.Drawing.Point(49, 19);
            this.cmbDark.Size = new System.Drawing.Size(98, 25);
            this.cmbDark.TabIndex = 28;
            this.cmbDark.TabStop = false;
            this.cmbDark.Tag = "日夜间模式";
            this.cmbDark.TipControl = tipMsg;
            this.cmbDark.TipText = "功能：\r\n      日间模式，即浅色模式，系统默认配置；\r\n      夜间模式，即深色模式，对比度更强；\r\n      跟随系统，根据当前系统主题设定，自动调整为相同模式；\r\n      跟随日落，根据当前位置，计算日出日落时间，自动调整模式；\r\n\r\n说明：\r\n      默认为日间模式；\r\n      选择日间 / 夜间模式，则一直固定使用该模式；\r\n      跟随系统，仅支持Win10 / Win11以上版本，仅启动或更改设置时生效，【非实时】；\r\n      跟随日落，基于IP定位，可能有稍许误差；";
            // 
            // lblDayNithtMode
            // 
            this.lblDayNithtMode.AutoSize = true;
            this.lblDayNithtMode.Location = new System.Drawing.Point(12, 24);
            this.lblDayNithtMode.Size = new System.Drawing.Size(35, 17);
            this.lblDayNithtMode.TabIndex = 46;
            this.lblDayNithtMode.Text = "模式:";
            // 
            // chkAutoColorInversion
            // 
            this.chkAutoColorInversion.AutoSize = true;
            this.chkAutoColorInversion.Location = new System.Drawing.Point(213, 20);
            this.chkAutoColorInversion.Size = new System.Drawing.Size(75, 21);
            this.chkAutoColorInversion.TabIndex = 40;
            this.chkAutoColorInversion.Text = "自动反色";
            this.chkAutoColorInversion.UseVisualStyleBackColor = true;
            this.chkAutoColorInversion.TipControl = tipMsg;
            this.chkAutoColorInversion.TipText = "功能：配合夜间模式使用，使用用户设定的文字及背景颜色，进行反色处理。\r\n说明：\r\n      默认不勾选，使用夜间模式内置颜色，选中后，使用用户设置的背景色进行反" +
        "色。\r\n      可在【常规】选项卡->【通用字体】中设置。\r\n";
            // 
            // gpUINormal
            // 
            this.gpUINormal.Controls.Add(this.btnImageViewBackColor);
            this.gpUINormal.Controls.Add(this.picLoadingImage);
            this.gpUINormal.Controls.Add(this.cmbImageViewBackStyle);
            this.gpUINormal.Controls.Add(this.cmbMouseMove);
            this.gpUINormal.Controls.Add(this.cmbStyles);
            this.gpUINormal.Controls.Add(this.chkFollowSysDPI);
            this.gpUINormal.Controls.Add(this.cmbLoadingType);
            this.gpUINormal.Controls.Add(this.lblThemeStyle);
            this.gpUINormal.Controls.Add(this.lblImageBack);
            this.gpUINormal.Controls.Add(this.lblMouseMove);
            this.gpUINormal.Controls.Add(this.cmbVoice);
            this.gpUINormal.Controls.Add(this.lblVoice);
            this.gpUINormal.Controls.Add(this.lblLoadingGif);
            this.gpUINormal.Controls.Add(this.cmbSearch);
            this.gpUINormal.Controls.Add(this.lblSearch);
            this.gpUINormal.Location = new System.Drawing.Point(3, 6);
            this.gpUINormal.Size = new System.Drawing.Size(374, 131);
            this.gpUINormal.TabIndex = 45;
            this.gpUINormal.TabStop = false;
            this.gpUINormal.Text = "通用";
            // 
            // btnImageViewBackColor
            // 
            this.btnImageViewBackColor.IsColorButton = true;
            this.btnImageViewBackColor.BackColor = System.Drawing.Color.White;
            this.btnImageViewBackColor.ImageColor = System.Drawing.Color.Black;
            this.btnImageViewBackColor.Location = new System.Drawing.Point(172, 70);
            this.btnImageViewBackColor.Size = new System.Drawing.Size(26, 26);
            this.btnImageViewBackColor.TabIndex = 45;
            this.btnImageViewBackColor.Tag = "图片预览背景颜色";
            this.btnImageViewBackColor.UseVisualStyleBackColor = false;
            this.btnImageViewBackColor.Visible = false;
            this.btnImageViewBackColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // picLoadingImage
            // 
            this.picLoadingImage.BackColor = Color.White;
            this.picLoadingImage.Location = new System.Drawing.Point(172, 42);
            this.picLoadingImage.Name = "picLoadingImage";
            this.picLoadingImage.Size = new System.Drawing.Size(25, 25);
            this.picLoadingImage.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.picLoadingImage.TabIndex = 25;
            this.picLoadingImage.TabStop = false;
            // 
            // cmbImageViewBackStyle
            // 
            this.cmbImageViewBackStyle.Location = new System.Drawing.Point(78, 71);
            this.cmbImageViewBackStyle.TextAlign = ContentAlignment.MiddleLeft;
            this.cmbImageViewBackStyle.Size = new System.Drawing.Size(86, 25);
            this.cmbImageViewBackStyle.TabIndex = 28;
            this.cmbImageViewBackStyle.TabStop = false;
            this.cmbImageViewBackStyle.Tag = "图片预览背景";
            // 
            // cmbMouseMove
            // 
            this.cmbMouseMove.Location = new System.Drawing.Point(78, 100);
            this.cmbMouseMove.TextAlign = ContentAlignment.MiddleLeft;
            this.cmbMouseMove.Size = new System.Drawing.Size(86, 25);
            this.cmbMouseMove.TabIndex = 28;
            this.cmbMouseMove.TabStop = false;
            this.cmbMouseMove.Tag = "图片动效";
            this.cmbMouseMove.TipControl = tipMsg;
            this.cmbMouseMove.TipText = "功能：设置鼠标移动到图片上的动态效果。\r\n说明：默认为旋转，选择“无”可关闭！";
            // 
            // cmbStyles
            // 
            this.cmbStyles.TextAlign = ContentAlignment.MiddleLeft;
            this.cmbStyles.Location = new System.Drawing.Point(78, 17);
            this.cmbStyles.Size = new System.Drawing.Size(86, 25);
            this.cmbStyles.TabIndex = 3;
            this.cmbStyles.Tag = "主题样式";
            // 
            // chkFollowSysDPI
            // 
            this.chkFollowSysDPI.AutoSize = true;
            this.chkFollowSysDPI.Location = new System.Drawing.Point(217, 74);
            this.chkFollowSysDPI.Size = new System.Drawing.Size(119, 21);
            this.chkFollowSysDPI.TabIndex = 40;
            this.chkFollowSysDPI.Text = "跟随系统DPI缩放";
            this.chkFollowSysDPI.UseVisualStyleBackColor = true;
            this.chkFollowSysDPI.TipControl = tipMsg;
            this.chkFollowSysDPI.TipText = "功能：针对高DPI时，主窗体窗口过小，针对性优化主界面大小。\r\n说明：\r\n      勾选后，自动跟随当前系统DPI设定，放大/缩小主窗口大小。\r\n";
            // 
            // cmbLoadingType
            // 
            this.cmbLoadingType.TextAlign = ContentAlignment.MiddleLeft;
            this.cmbLoadingType.Location = new System.Drawing.Point(78, 44);
            this.cmbLoadingType.Size = new System.Drawing.Size(86, 25);
            this.cmbLoadingType.TabIndex = 3;
            this.cmbLoadingType.Tag = "加载动画";
            // 
            // lblThemeStyle
            // 
            this.lblThemeStyle.AutoSize = true;
            this.lblThemeStyle.Location = new System.Drawing.Point(12, 20);
            this.lblThemeStyle.Size = new System.Drawing.Size(59, 17);
            this.lblThemeStyle.TabIndex = 2;
            this.lblThemeStyle.Text = "主题样式:";
            // 
            // lblImageBack
            // 
            this.lblImageBack.AutoSize = true;
            this.lblImageBack.Location = new System.Drawing.Point(12, 75);
            this.lblImageBack.Size = new System.Drawing.Size(59, 17);
            this.lblImageBack.TabIndex = 27;
            this.lblImageBack.Text = "图片背景:";
            // 
            // lblMouseMove
            // 
            this.lblMouseMove.AutoSize = true;
            this.lblMouseMove.Location = new System.Drawing.Point(12, 103);
            this.lblMouseMove.Size = new System.Drawing.Size(59, 17);
            this.lblMouseMove.TabIndex = 27;
            this.lblMouseMove.Text = "界面动效:";
            // 
            // cmbVoice
            // 
            this.cmbVoice.TextAlign = ContentAlignment.MiddleLeft;
            this.cmbVoice.Location = new System.Drawing.Point(254, 43);
            this.cmbVoice.Size = new System.Drawing.Size(97, 25);
            this.cmbVoice.TabIndex = 6;
            this.cmbVoice.Tag = "朗读配置";
            // 
            // lblVoice
            // 
            this.lblVoice.AutoSize = false;
            this.lblVoice.Location = new System.Drawing.Point(196, 46);
            this.lblVoice.Size = new System.Drawing.Size(60, 17);
            this.lblVoice.TabIndex = 5;
            this.lblVoice.Text = "语音:";
            this.lblVoice.TextAlign = ContentAlignment.MiddleRight;
            // 
            // lblLoadingGif
            // 
            this.lblLoadingGif.AutoSize = true;
            this.lblLoadingGif.Location = new System.Drawing.Point(12, 47);
            this.lblLoadingGif.Size = new System.Drawing.Size(59, 17);
            this.lblLoadingGif.TabIndex = 2;
            this.lblLoadingGif.Text = "识别动画:";
            // 
            // cmbSearch
            // 
            this.cmbSearch.TextAlign = ContentAlignment.MiddleLeft;
            this.cmbSearch.Location = new System.Drawing.Point(254, 17);
            this.cmbSearch.Size = new System.Drawing.Size(96, 25);
            this.cmbSearch.TabIndex = 3;
            this.cmbSearch.Tag = "搜索引擎";
            // 
            // lblSearch
            // 
            this.lblSearch.AutoSize = false;
            this.lblSearch.Location = new System.Drawing.Point(196, 20);
            this.lblSearch.Size = new System.Drawing.Size(60, 17);
            this.lblSearch.TextAlign = ContentAlignment.MiddleRight;
            this.lblSearch.TabIndex = 2;
            this.lblSearch.Text = "搜索:";
            // 
            // grpToolSet
            // 
            this.grpToolSet.Controls.Add(this.numToolShadowWidth);
            this.grpToolSet.Controls.Add(this.cmbToolBarSize);
            this.grpToolSet.Controls.Add(this.cmbWeatherIcon);
            this.grpToolSet.Controls.Add(this.btnDefaultToolBarPicture);
            this.grpToolSet.Controls.Add(this.chkWeatherImage);
            this.grpToolSet.Controls.Add(this.txtToolBarPicLocation);
            this.grpToolSet.Controls.Add(this.btnToolBarPicture);
            this.grpToolSet.Controls.Add(this.picToolBar);
            this.grpToolSet.Controls.Add(this.lblDiyToolbarImage);
            this.grpToolSet.Controls.Add(this.chkToolBarCircle);
            this.grpToolSet.Controls.Add(this.btnQQ);
            this.grpToolSet.Controls.Add(this.lblToolBarShadowUnit);
            this.grpToolSet.Controls.Add(this.chkToolShadow);
            this.grpToolSet.Location = new System.Drawing.Point(3, 204);
            this.grpToolSet.Size = new System.Drawing.Size(374, 155);
            this.grpToolSet.TabIndex = 26;
            this.grpToolSet.TabStop = false;
            this.grpToolSet.Text = "工具栏样式";
            // 
            // numToolShadowWidth
            // 
            this.numToolShadowWidth.Location = new System.Drawing.Point(110, 44);
            this.numToolShadowWidth.Maximum = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numToolShadowWidth.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numToolShadowWidth.Name = "numToolShadowWidth";
            this.numToolShadowWidth.Size = new System.Drawing.Size(42, 23);
            this.numToolShadowWidth.TabIndex = 42;
            this.numToolShadowWidth.TabStop = false;
            this.numToolShadowWidth.Tag = "工具栏阴影宽度";
            this.numToolShadowWidth.Value = new decimal(new int[] {
            8,
            0,
            0,
            0});
            // 
            // cmbToolBarSize
            // 
            this.cmbToolBarSize.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbToolBarSize.FormattingEnabled = true;
            this.cmbToolBarSize.Items.AddRange(new object[] {
            "自动",
            "12*12",
            "24*24",
            "36*36",
            "48*48",
            "60*60",
            "72*72",
            "84*84",
            "96*96",
            "108*108",
            "120*120"});
            this.cmbToolBarSize.Location = new System.Drawing.Point(90, 17);
            this.cmbToolBarSize.Name = "cmbToolBarSize";
            this.cmbToolBarSize.Size = new System.Drawing.Size(89, 25);
            this.cmbToolBarSize.TabIndex = 28;
            this.cmbToolBarSize.Tag = "工具栏图标尺寸";
            // 
            // cmbWeatherIcon
            // 
            this.cmbWeatherIcon.Location = new System.Drawing.Point(89, 122);
            this.cmbWeatherIcon.TextAlign = ContentAlignment.MiddleLeft;
            this.cmbWeatherIcon.Name = "cmbWeatherIcon";
            this.cmbWeatherIcon.Size = new System.Drawing.Size(64, 25);
            this.cmbWeatherIcon.TabIndex = 28;
            this.cmbWeatherIcon.TabStop = false;
            this.cmbWeatherIcon.Tag = "天气图标样式";
            this.cmbWeatherIcon.TipControl = tipMsg;
            this.cmbWeatherIcon.TipText = "功能：以当前IP所在位置的天气设置为图标。\r\n说明：\r\n      天气更新间隔为30分钟。\r\n      气象预警信息，以弹框方式提示。";
            // 
            // btnDefaultToolBarPicture
            // 
            this.btnDefaultToolBarPicture.Location = new System.Drawing.Point(148, 94);
            this.btnDefaultToolBarPicture.Size = new System.Drawing.Size(50, 25);
            this.btnDefaultToolBarPicture.TabIndex = 39;
            this.btnDefaultToolBarPicture.Tag = "";
            this.btnDefaultToolBarPicture.Text = "重置";
            this.btnDefaultToolBarPicture.UseVisualStyleBackColor = false;
            this.btnDefaultToolBarPicture.Click += new System.EventHandler(this.btnDefaultToolBarPicture_Click);
            // 
            // chkWeatherImage
            // 
            this.chkWeatherImage.AutoSize = true;
            this.chkWeatherImage.Location = new System.Drawing.Point(13, 124);
            this.chkWeatherImage.Margin = new System.Windows.Forms.Padding(0);
            this.chkWeatherImage.Size = new System.Drawing.Size(75, 21);
            this.chkWeatherImage.TabIndex = 30;
            this.chkWeatherImage.TabStop = false;
            this.chkWeatherImage.Text = "天气图标";
            this.chkWeatherImage.UseVisualStyleBackColor = true;
            // 
            // txtToolBarPicLocation
            // 
            this.txtToolBarPicLocation.Location = new System.Drawing.Point(48, -4);
            this.txtToolBarPicLocation.Name = "txtToolBarPicLocation";
            this.txtToolBarPicLocation.Size = new System.Drawing.Size(100, 23);
            this.txtToolBarPicLocation.TabIndex = 40;
            this.txtToolBarPicLocation.Tag = "工具栏图片";
            this.txtToolBarPicLocation.Visible = false;
            // 
            // btnToolBarPicture
            // 
            this.btnToolBarPicture.Location = new System.Drawing.Point(15, 94);
            this.btnToolBarPicture.Size = new System.Drawing.Size(58, 25);
            this.btnToolBarPicture.TabIndex = 37;
            this.btnToolBarPicture.TabStop = false;
            this.btnToolBarPicture.Text = "自定义";
            this.btnToolBarPicture.UseVisualStyleBackColor = false;
            this.btnToolBarPicture.Click += new System.EventHandler(this.btnToolBarPicture_Click);
            // 
            // picToolBar
            // 
            this.picToolBar.BackColor = Color.White;
            this.picToolBar.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.picToolBar.Location = new System.Drawing.Point(218, 20);
            this.picToolBar.Name = "picToolBar";
            this.picToolBar.Size = new System.Drawing.Size(120, 120);
            this.picToolBar.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            this.picToolBar.TabIndex = 25;
            this.picToolBar.TabStop = false;
            // 
            // lblDiyToolbarImage
            // 
            this.lblDiyToolbarImage.AutoSize = true;
            this.lblDiyToolbarImage.Location = new System.Drawing.Point(13, 75);
            this.lblDiyToolbarImage.Size = new System.Drawing.Size(107, 17);
            this.lblDiyToolbarImage.TabIndex = 27;
            this.lblDiyToolbarImage.Text = "自定义工具栏图片";
            // 
            // chkToolBarCircle
            // 
            this.chkToolBarCircle.AutoSize = true;
            this.chkToolBarCircle.Checked = true;
            this.chkToolBarCircle.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkToolBarCircle.Location = new System.Drawing.Point(13, 20);
            this.chkToolBarCircle.Margin = new System.Windows.Forms.Padding(0);
            this.chkToolBarCircle.Size = new System.Drawing.Size(75, 21);
            this.chkToolBarCircle.TabIndex = 30;
            this.chkToolBarCircle.Text = "圆形图标";
            this.chkToolBarCircle.UseVisualStyleBackColor = true;
            // 
            // btnQQ
            // 
            this.btnQQ.Location = new System.Drawing.Point(77, 94);
            this.btnQQ.Size = new System.Drawing.Size(67, 25);
            this.btnQQ.TabIndex = 37;
            this.btnQQ.TabStop = false;
            this.btnQQ.Text = "QQ头像";
            this.btnQQ.UseVisualStyleBackColor = false;
            this.btnQQ.Click += new System.EventHandler(this.btnQQ_Click);
            // 
            // lblPiexUnitName
            // 
            this.lblToolBarShadowUnit.AutoSize = true;
            this.lblToolBarShadowUnit.Location = new System.Drawing.Point(152, 45);
            this.lblToolBarShadowUnit.Size = new System.Drawing.Size(32, 17);
            this.lblToolBarShadowUnit.TabIndex = 41;
            this.lblToolBarShadowUnit.Text = "像素";
            // 
            // chkToolShadow
            // 
            this.chkToolShadow.AutoSize = true;
            this.chkToolShadow.Checked = true;
            this.chkToolShadow.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkToolShadow.Location = new System.Drawing.Point(13, 46);
            this.chkToolShadow.Margin = new System.Windows.Forms.Padding(0);
            this.chkToolShadow.Size = new System.Drawing.Size(75, 21);
            this.chkToolShadow.TabIndex = 30;
            this.chkToolShadow.TabStop = false;
            this.chkToolShadow.Text = "阴影效果";
            this.chkToolShadow.UseVisualStyleBackColor = true;
            this.chkToolShadow.TipControl = tipMsg;
            this.chkToolShadow.TipText = "功能：工具栏图标阴影效果。\r\n说明：\r\n      支持异形透明图片(非圆形图标，不支持阴影)！\r\n      不透明图片，支持阴影。";
            // 
            // tbRec
            // 
            this.tbOcr.Controls.Add(this.tabControl1);
            this.tbOcr.Location = new System.Drawing.Point(4, 22);
            this.tbOcr.Padding = new System.Windows.Forms.Padding(3);
            this.tbOcr.Size = new System.Drawing.Size(387, 368);
            this.tbOcr.TabIndex = 8;
            this.tbOcr.Text = "识别";
            this.tbOcr.UseVisualStyleBackColor = true;
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tbOcrSetting);
            this.tabControl1.Controls.Add(this.tbLocalOcr);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(3, 3);
            this.tabControl1.BackColor = Color.White;
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(381, 380);
            this.tabControl1.TabIndex = 48;
            // 
            // tbOcrSetting
            // 
            this.tbOcrSetting.Controls.Add(this.gpOcrProcing);
            this.tbOcrSetting.Controls.Add(this.gpOcrResult);
            this.tbOcrSetting.Location = new System.Drawing.Point(4, 26);
            this.tbOcrSetting.Padding = new System.Windows.Forms.Padding(3);
            this.tbOcrSetting.Size = new System.Drawing.Size(373, 380);
            this.tbOcrSetting.TabIndex = 0;
            this.tbOcrSetting.Text = "识别设置";
            this.tbOcrSetting.UseVisualStyleBackColor = true;
            // 
            // gpOcrProcing
            // 
            this.gpOcrProcing.Controls.Add(this.cmbOcrType);
            this.gpOcrProcing.Controls.Add(this.lblOcrStrategy);
            this.gpOcrProcing.Controls.Add(this.chkNotShowUIWhenOcr);
            this.gpOcrProcing.Controls.Add(this.chkAutoCompressImg);
            this.gpOcrProcing.Location = new System.Drawing.Point(3, 6);
            this.gpOcrProcing.Size = new System.Drawing.Size(361, 71);
            this.gpOcrProcing.TabIndex = 45;
            this.gpOcrProcing.TabStop = false;
            this.gpOcrProcing.Text = "识别过程";
            // 
            // cmbOcrType
            // 
            this.cmbOcrType.TextAlign = ContentAlignment.MiddleLeft;
            this.cmbOcrType.Location = new System.Drawing.Point(74, 15);
            this.cmbOcrType.Size = new System.Drawing.Size(142, 25);
            this.cmbOcrType.TabIndex = 6;
            this.cmbOcrType.Tag = "识别策略";
            this.cmbOcrType.TipControl = tipMsg;
            this.cmbOcrType.TipText = "功能：选择本地+网络识别的优先级。\r\n说明：\r\n本地识别：速度更稳定，但受限于特征库，识别结果没网络识别精确！\r\n网络识别：速度受限于本地网络及服务商接口，结果精" +
        "度有保证。\r\n\r\n可根据实际需求，进行选择。";
            // 
            // lblOcrStrategy
            // 
            this.lblOcrStrategy.AutoSize = true;
            this.lblOcrStrategy.Location = new System.Drawing.Point(13, 19);
            this.lblOcrStrategy.Size = new System.Drawing.Size(59, 17);
            this.lblOcrStrategy.TabIndex = 5;
            this.lblOcrStrategy.Text = "识别策略:";
            // 
            // chkNotShowUIWhenOcr
            // 
            this.chkNotShowUIWhenOcr.AutoSize = true;
            this.chkNotShowUIWhenOcr.Location = new System.Drawing.Point(186, 43);
            this.chkNotShowUIWhenOcr.Size = new System.Drawing.Size(135, 21);
            this.chkNotShowUIWhenOcr.TabIndex = 4;
            this.chkNotShowUIWhenOcr.Text = "识别时不显示主界面";
            this.chkNotShowUIWhenOcr.UseVisualStyleBackColor = true;
            this.chkNotShowUIWhenOcr.CheckedChanged += new System.EventHandler(this.checkBox9_CheckedChanged);
            this.chkNotShowUIWhenOcr.TipControl = tipMsg;
            this.chkNotShowUIWhenOcr.TipText = "功能：识别时，不自动弹出主窗体。\r\n说明：请配合【自动复制结果】使用！！！\r\n";
            // 
            // chkAutoCompressImg
            // 
            this.chkAutoCompressImg.AutoSize = true;
            this.chkAutoCompressImg.Checked = true;
            this.chkAutoCompressImg.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoCompressImg.Location = new System.Drawing.Point(13, 43);
            this.chkAutoCompressImg.Size = new System.Drawing.Size(99, 21);
            this.chkAutoCompressImg.TabIndex = 4;
            this.chkAutoCompressImg.Text = "图片自动压缩";
            this.chkAutoCompressImg.UseVisualStyleBackColor = true;
            this.chkAutoCompressImg.TipControl = tipMsg;
            this.chkAutoCompressImg.TipText = "功能：识别前自动压缩图片大小。\r\n说明：如果开启，可以加快识别速度，图越大效果越明显。\r\n可自行选择是否开启！";
            // 
            // gpOcrResult
            // 
            this.gpOcrResult.Controls.Add(this.chkShowTextPreview);
            this.gpOcrResult.Controls.Add(this.gpCopyResult);
            this.gpOcrResult.Controls.Add(this.gpTypography);
            this.gpOcrResult.Controls.Add(this.checkBox11);
            this.gpOcrResult.Controls.Add(this.chkAutoScaleImg);
            this.gpOcrResult.Location = new System.Drawing.Point(3, 84);
            this.gpOcrResult.Size = new System.Drawing.Size(363, 260);
            this.gpOcrResult.TabIndex = 45;
            this.gpOcrResult.TabStop = false;
            this.gpOcrResult.Text = "识别结果";
            // 
            // chkShowTextPreview
            // 
            this.chkShowTextPreview.AutoSize = true;
            this.chkShowTextPreview.Location = new System.Drawing.Point(108, 20);
            this.chkShowTextPreview.Size = new System.Drawing.Size(99, 21);
            this.chkShowTextPreview.TabIndex = 1;
            this.chkShowTextPreview.Text = "显示文字预览";
            this.chkShowTextPreview.UseVisualStyleBackColor = true;
            this.chkShowTextPreview.TipControl = tipMsg;
            this.chkShowTextPreview.TipText = "功能：图文模式下，在图上显示文字预览。\r\n说明：\r\n      此操作会增加性能消耗，可酌情打开！";
            // 
            // gpCopyResult
            // 
            this.gpCopyResult.Controls.Add(this.cmbCopyMode);
            this.gpCopyResult.Controls.Add(this.chkCopyResult);
            this.gpCopyResult.Controls.Add(this.lblCopyMode);
            this.gpCopyResult.Controls.Add(this.chkCopyOldWhenTrans);
            this.gpCopyResult.Controls.Add(this.chkClickCopy);
            this.gpCopyResult.Controls.Add(this.chkTipCopy);
            this.gpCopyResult.Location = new System.Drawing.Point(4, 147);
            this.gpCopyResult.Size = new System.Drawing.Size(355, 100);
            this.gpCopyResult.TabIndex = 45;
            this.gpCopyResult.TabStop = false;
            this.gpCopyResult.Text = "结果复制";
            // 
            // cmbCopyMode
            // 
            this.cmbCopyMode.TextAlign = ContentAlignment.MiddleLeft;
            this.cmbCopyMode.Location = new System.Drawing.Point(226, 16);
            this.cmbCopyMode.Size = new System.Drawing.Size(93, 25);
            this.cmbCopyMode.Tag = "复制模式";
            this.cmbCopyMode.TipControl = tipMsg;
            this.cmbCopyMode.TipText = "功能：选择复制哪一个识别结果。\r\n说明：体验版以上版本，会有多个识别结果，可以选择复制哪一个！\r\n\r\n选项说明\r\n最快：第一个\r\n最新：最后一个\r\n所有：所有结果累加";
            // 
            // chkCopyResult
            // 
            this.chkCopyResult.AutoSize = true;
            this.chkCopyResult.Location = new System.Drawing.Point(5, 19);
            this.chkCopyResult.Size = new System.Drawing.Size(147, 21);
            this.chkCopyResult.TabIndex = 4;
            this.chkCopyResult.Text = "自动复制结果到粘贴板";
            this.chkCopyResult.UseVisualStyleBackColor = true;
            this.chkCopyResult.TipControl = tipMsg;
            this.chkCopyResult.TipText = "功能：OCR完成后，自动复制识别结果到粘贴板。\r\n说明：如果不需要复制，请不要勾选！";
            // 
            // lblCopyMode
            // 
            this.lblCopyMode.AutoSize = true;
            this.lblCopyMode.Location = new System.Drawing.Point(188, 19);
            this.lblCopyMode.Size = new System.Drawing.Size(35, 17);
            this.lblCopyMode.TabIndex = 5;
            this.lblCopyMode.Text = "模式:";
            // 
            // chkCopyOldWhenTrans
            // 
            this.chkCopyOldWhenTrans.AutoSize = true;
            this.chkCopyOldWhenTrans.Location = new System.Drawing.Point(5, 46);
            this.chkCopyOldWhenTrans.Size = new System.Drawing.Size(111, 21);
            this.chkCopyOldWhenTrans.TabIndex = 1;
            this.chkCopyOldWhenTrans.Text = "翻译时复制原文";
            this.chkCopyOldWhenTrans.UseVisualStyleBackColor = true;
            this.chkCopyOldWhenTrans.TipControl = tipMsg;
            this.chkCopyOldWhenTrans.TipText = "功能：翻译模式时，是否复制原文。\r\n说明：不勾选，仅复制译文，否则，复制原文+译文！";
            // 
            // chkClickCopy
            // 
            this.chkClickCopy.AutoSize = true;
            this.chkClickCopy.Location = new System.Drawing.Point(5, 74);
            this.chkClickCopy.Size = new System.Drawing.Size(111, 21);
            this.chkClickCopy.TabIndex = 1;
            this.chkClickCopy.Text = "点击图片复制结果";
            this.chkClickCopy.UseVisualStyleBackColor = true;
            this.chkClickCopy.TipControl = tipMsg;
            this.chkClickCopy.TipText = "功能：图文模式下，点击图片中的识别块后，自动复制选择的识别结果。";
            // 
            // chkTipCopy
            // 
            this.chkTipCopy.AutoSize = true;
            this.chkTipCopy.Location = new System.Drawing.Point(191, 46);
            this.chkTipCopy.Size = new System.Drawing.Size(147, 21);
            this.chkTipCopy.TabIndex = 1;
            this.chkTipCopy.Text = "复制成功后提示";
            this.chkTipCopy.UseVisualStyleBackColor = true;
            this.chkTipCopy.TipControl = tipMsg;
            this.chkTipCopy.TipText = "功能：复制成功后是否提示。";
            // 
            // gpTypography
            // 
            this.gpTypography.Controls.Add(this.chkRemoveDuplicatePunctuation);
            this.gpTypography.Controls.Add(this.cmbFenDuan);
            this.gpTypography.Controls.Add(this.chkAutoBiaoDian);
            this.gpTypography.Controls.Add(this.chkTextIndent);
            this.gpTypography.Controls.Add(this.lblSegment);
            this.gpTypography.Controls.Add(this.chkAutoSpace);
            this.gpTypography.Controls.Add(this.chkAutoFull2Half);
            this.gpTypography.Location = new System.Drawing.Point(4, 44);
            this.gpTypography.Size = new System.Drawing.Size(355, 98);
            this.gpTypography.TabIndex = 44;
            this.gpTypography.TabStop = false;
            this.gpTypography.Text = "排版展示";
            // 
            // chkRemoveDuplicatePunctuation
            // 
            this.chkRemoveDuplicatePunctuation.AutoSize = true;
            this.chkRemoveDuplicatePunctuation.Checked = true;
            this.chkRemoveDuplicatePunctuation.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkRemoveDuplicatePunctuation.Location = new System.Drawing.Point(182, 72);
            this.chkRemoveDuplicatePunctuation.Size = new System.Drawing.Size(99, 21);
            this.chkRemoveDuplicatePunctuation.TabIndex = 45;
            this.chkRemoveDuplicatePunctuation.Text = "去除重复标点";
            this.chkRemoveDuplicatePunctuation.UseVisualStyleBackColor = true;
            this.chkRemoveDuplicatePunctuation.TipControl = tipMsg;
            this.chkRemoveDuplicatePunctuation.TipText = "功能：重复标点校正.\r\n\r\n正确：\r\n德国队竟然战胜了巴西队！\r\n她竟然对你说「喵」？！\r\n\r\n错误：\r\n德国队竟然战胜了巴西队！！\r\n她竟然对你说「喵」？？！！";
            // 
            // cmbFenDuan
            // 
            this.cmbFenDuan.TextAlign = ContentAlignment.MiddleLeft;
            this.cmbFenDuan.Location = new System.Drawing.Point(74, 16);
            this.cmbFenDuan.Size = new System.Drawing.Size(82, 25);
            this.cmbFenDuan.TabIndex = 6;
            this.cmbFenDuan.Tag = "分段模式";
            // 
            // chkAutoBiaoDian
            // 
            this.chkAutoBiaoDian.AutoSize = true;
            this.chkAutoBiaoDian.Checked = true;
            this.chkAutoBiaoDian.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoBiaoDian.Location = new System.Drawing.Point(11, 72);
            this.chkAutoBiaoDian.Size = new System.Drawing.Size(111, 21);
            this.chkAutoBiaoDian.TabIndex = 46;
            this.chkAutoBiaoDian.Text = "自动中英文标点";
            this.chkAutoBiaoDian.UseVisualStyleBackColor = true;
            this.chkAutoBiaoDian.TipControl = tipMsg;
            this.chkAutoBiaoDian.TipText = "功能：\r\n自动根据语境，把文字中的标点符号转换为中/英文标点";
            // 
            // chkTextIndent
            // 
            this.chkTextIndent.AutoSize = true;
            this.chkTextIndent.Location = new System.Drawing.Point(182, 20);
            this.chkTextIndent.Size = new System.Drawing.Size(75, 21);
            this.chkTextIndent.TabIndex = 4;
            this.chkTextIndent.Text = "首行缩进";
            this.chkTextIndent.UseVisualStyleBackColor = true;
            this.chkTextIndent.TipControl = tipMsg;
            this.chkTextIndent.TipText = "功能：段落开头是否缩进。\r\n说明：对于文档排版更友好，如果只需要识别文字，可关闭";
            // 
            // lblSegment
            // 
            this.lblSegment.AutoSize = true;
            this.lblSegment.Location = new System.Drawing.Point(13, 20);
            this.lblSegment.Size = new System.Drawing.Size(59, 17);
            this.lblSegment.TabIndex = 5;
            this.lblSegment.Text = "分段模式:";
            // 
            // chkAutoSpace
            // 
            this.chkAutoSpace.AutoSize = true;
            this.chkAutoSpace.Checked = true;
            this.chkAutoSpace.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoSpace.Location = new System.Drawing.Point(182, 46);
            this.chkAutoSpace.Size = new System.Drawing.Size(75, 21);
            this.chkAutoSpace.TabIndex = 4;
            this.chkAutoSpace.Text = "自动空格";
            this.chkAutoSpace.UseVisualStyleBackColor = true;
            this.chkAutoSpace.TipControl = tipMsg;
            this.chkAutoSpace.TipText = "功能：\r\n在中文与英文字母 /用于数学、科学和工程的希腊字母/数字之间添加空格。\r\n\r\n中英文之间需要增加空格\r\n例如：\r\n在 LeanCloud 上，数据存储是围绕 AVObject 进行的。\r\n\r\n中文与数字之间需要增加空格\r\n例如：\r\n今天出去买菜花了 5000 元。\r\n另外，度／百分比与数字之间不需要增加空格。\r\n例如：\r\n今天是 233° 的高温。\r\n新 MacBook Pro 有 15% 的 CPU 性能提升。\r\n\r\n全角标点与其他字符之间不加空格\r\n例如：\r\n刚刚买了一部 iPhone，好开心！";
            // 
            // chkAutoFull2Half
            // 
            this.chkAutoFull2Half.AutoSize = true;
            this.chkAutoFull2Half.Checked = true;
            this.chkAutoFull2Half.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoFull2Half.Location = new System.Drawing.Point(11, 46);
            this.chkAutoFull2Half.Size = new System.Drawing.Size(111, 21);
            this.chkAutoFull2Half.TabIndex = 4;
            this.chkAutoFull2Half.Text = "自动全半角转换";
            this.chkAutoFull2Half.UseVisualStyleBackColor = true;
            this.chkAutoFull2Half.TipControl = tipMsg;
            this.chkAutoFull2Half.TipText = "功能：\r\n根据当前行的语境，自动转换全角 / 半角标点。\r\n\r\n使用全角中文标点\r\n例如：\r\n嗨！你知道嘛？今天前台的小妹跟我说「喵」了哎！\r\n核磁共振成像（NMRI）是什么原理都不知道？JFGI！\r\n\r\n数字使用半角字符\r\n例如：\r\n这件蛋糕只卖 1000 元。\r\n例外：在设计稿、宣传海报中如出现极少量数字的情形时，为方便文字对齐，是可以使用全角数字的。\r\n\r\n遇到完整的英文整句、特殊名词，其內容使用半角标点。\r\n例如：\r\n乔布斯那句话是怎么说的？「Stay hungry, stay foolish.」";
            // 
            // checkBox11
            // 
            this.checkBox11.AutoSize = true;
            this.checkBox11.Checked = true;
            this.checkBox11.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox11.Location = new System.Drawing.Point(10, 20);
            this.checkBox11.Size = new System.Drawing.Size(75, 21);
            this.checkBox11.TabIndex = 4;
            this.checkBox11.Text = "图文模式";
            this.checkBox11.UseVisualStyleBackColor = true;
            // 
            // checkBox1
            // 
            this.chkAutoScaleImg.AutoSize = true;
            this.chkAutoScaleImg.Checked = true;
            this.chkAutoScaleImg.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoScaleImg.Location = new System.Drawing.Point(235, 20);
            this.chkAutoScaleImg.Size = new System.Drawing.Size(99, 21);
            this.chkAutoScaleImg.TabIndex = 1;
            this.chkAutoScaleImg.Text = "图片自动缩放";
            this.chkAutoScaleImg.UseVisualStyleBackColor = true;
            this.chkAutoScaleImg.TipControl = tipMsg;
            this.chkAutoScaleImg.TipText = "功能：图文模式下，自动根据窗口大小缩放图片。\r\n说明：\r\n      如果要显示原尺寸，请去掉勾选！\r\n";
            // 
            // tbLocalOcr
            // 
            this.tbLocalOcr.Controls.Add(this.grpOperate);
            this.tbLocalOcr.Controls.Add(this.grpLocalOcrSetting);
            this.tbLocalOcr.Controls.Add(this.grpLocalOcr);
            this.tbLocalOcr.Location = new System.Drawing.Point(4, 22);
            this.tbLocalOcr.Padding = new System.Windows.Forms.Padding(3);
            this.tbLocalOcr.Size = new System.Drawing.Size(373, 336);
            this.tbLocalOcr.TabIndex = 1;
            this.tbLocalOcr.Text = "本地识别";
            this.tbLocalOcr.UseVisualStyleBackColor = true;
            // 
            // grpOperate
            // 
            this.grpOperate.Controls.Add(this.lnkOpenLocalOcr);
            this.grpOperate.Controls.Add(this.lnkTestLocalOcr);
            this.grpOperate.Location = new System.Drawing.Point(3, 80);
            this.grpOperate.Size = new System.Drawing.Size(368, 44);
            this.grpOperate.TabIndex = 45;
            this.grpOperate.TabStop = false;
            this.grpOperate.Text = "操作";
            // 
            // lnkOpenLocalOcr
            // 
            this.lnkOpenLocalOcr.AutoSize = true;
            this.lnkOpenLocalOcr.Location = new System.Drawing.Point(11, 19);
            this.lnkOpenLocalOcr.Size = new System.Drawing.Size(133, 17);
            this.lnkOpenLocalOcr.TabIndex = 45;
            this.lnkOpenLocalOcr.TabStop = true;
            this.lnkOpenLocalOcr.Text = "启动/重启本地识别服务";
            this.lnkOpenLocalOcr.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lnkOpenLocalOcr_LinkClicked);
            // 
            // lnkTestLocalOcr
            // 
            this.lnkTestLocalOcr.AutoSize = true;
            this.lnkTestLocalOcr.Location = new System.Drawing.Point(164, 19);
            this.lnkTestLocalOcr.Size = new System.Drawing.Size(80, 17);
            this.lnkTestLocalOcr.TabIndex = 45;
            this.lnkTestLocalOcr.TabStop = true;
            this.lnkTestLocalOcr.Text = "检测服务状态";
            this.lnkTestLocalOcr.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lnkTestLocalOcr_LinkClicked);
            // 
            // grpLocalOcrSetting
            // 
            this.grpLocalOcrSetting.Controls.Add(this.lnkInstallLocalOcr);
            this.grpLocalOcrSetting.Controls.Add(this.numLocalOcrThread);
            this.grpLocalOcrSetting.Controls.Add(this.numLocalOcrPort);
            this.grpLocalOcrSetting.Controls.Add(this.lblMaxOcrThread);
            this.grpLocalOcrSetting.Controls.Add(this.lblLocalOcrPort);
            this.grpLocalOcrSetting.Controls.Add(this.lblLocalOcrMaxThread);
            this.grpLocalOcrSetting.Controls.Add(this.lblLocalOcrPortHelp);
            this.grpLocalOcrSetting.Controls.Add(this.chkLocalOcrEnable);
            this.grpLocalOcrSetting.Location = new System.Drawing.Point(3, 6);
            this.grpLocalOcrSetting.Size = new System.Drawing.Size(368, 67);
            this.grpLocalOcrSetting.TabIndex = 45;
            this.grpLocalOcrSetting.TabStop = false;
            this.grpLocalOcrSetting.Text = "本地识别配置";
            // 
            // lnkInstallLocalOcr
            // 
            this.lnkInstallLocalOcr.AutoSize = true;
            this.lnkInstallLocalOcr.Location = new System.Drawing.Point(149, 19);
            this.lnkInstallLocalOcr.Size = new System.Drawing.Size(133, 17);
            this.lnkInstallLocalOcr.TabIndex = 45;
            this.lnkInstallLocalOcr.TabStop = true;
            this.lnkInstallLocalOcr.Text = "安装/更新本地识别服务";
            this.lnkInstallLocalOcr.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lnkLocalOcr_LinkClicked);
            // 
            // numLocalOcrThread
            // 
            this.numLocalOcrThread.Location = new System.Drawing.Point(261, 39);
            this.numLocalOcrThread.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numLocalOcrThread.Name = "numLocalOcrThread";
            this.numLocalOcrThread.Size = new System.Drawing.Size(46, 23);
            this.numLocalOcrThread.TabIndex = 47;
            this.numLocalOcrThread.TabStop = false;
            this.numLocalOcrThread.Tag = "本地识别线程数";
            this.numLocalOcrThread.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.numLocalOcrThread.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // numLocalOcrPort
            // 
            this.numLocalOcrPort.Location = new System.Drawing.Point(74, 40);
            this.numLocalOcrPort.Maximum = new decimal(new int[] {
            9999,
            0,
            0,
            0});
            this.numLocalOcrPort.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numLocalOcrPort.Name = "numLocalOcrPort";
            this.numLocalOcrPort.Size = new System.Drawing.Size(52, 23);
            this.numLocalOcrPort.TabIndex = 47;
            this.numLocalOcrPort.Tag = "本地识别端口";
            this.numLocalOcrPort.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.numLocalOcrPort.Value = new decimal(new int[] {
            8080,
            0,
            0,
            0});
            // 
            // lblMaxOcrThread
            // 
            this.lblMaxOcrThread.AutoSize = true;
            this.lblMaxOcrThread.Location = new System.Drawing.Point(186, 44);
            this.lblMaxOcrThread.Size = new System.Drawing.Size(71, 17);
            this.lblMaxOcrThread.TabIndex = 46;
            this.lblMaxOcrThread.Text = "最大线程数:";
            // 
            // lblLocalOcrPort
            // 
            this.lblLocalOcrPort.AutoSize = true;
            this.lblLocalOcrPort.Location = new System.Drawing.Point(11, 45);
            this.lblLocalOcrPort.Size = new System.Drawing.Size(59, 17);
            this.lblLocalOcrPort.TabIndex = 46;
            this.lblLocalOcrPort.Text = "识别端口:";
            // 
            // lblLocalOcrMaxThread
            // 
            this.lblLocalOcrMaxThread.AutoSize = true;
            this.lblLocalOcrMaxThread.Location = new System.Drawing.Point(314, 45);
            this.lblLocalOcrMaxThread.Size = new System.Drawing.Size(20, 17);
            this.lblLocalOcrMaxThread.TabIndex = 2;
            this.lblLocalOcrMaxThread.Text = ".";
            this.lblLocalOcrMaxThread.TipControl = tipMsg;
            this.lblLocalOcrMaxThread.TipText = "功能：设置本地识别最大同时处理线程数量。\r\n说明：并不是越大越好！！！\r\n可以设置1-100之间的数字。\r\n\r\n注意：本地识别非常耗费性能，数字越大，电脑越卡！！！";
            // 
            // lblLocalOcrMaxThread
            // 
            this.lblLocalOcrPortHelp.AutoSize = true;
            this.lblLocalOcrPortHelp.Location = new System.Drawing.Point(132, 45);
            this.lblLocalOcrPortHelp.Size = new System.Drawing.Size(20, 17);
            this.lblLocalOcrPortHelp.Text = ".";
            this.lblLocalOcrPortHelp.TipControl = tipMsg;
            this.lblLocalOcrPortHelp.TipText = "功能：设置本地识别对外提供服务的端口。\r\n说明：一般不建议调整！！！\r\n可以设置1000-9999之间的端口！";
            // 
            // chkLocalOcrEnable
            // 
            this.chkLocalOcrEnable.AutoSize = true;
            this.chkLocalOcrEnable.Checked = true;
            this.chkLocalOcrEnable.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkLocalOcrEnable.Location = new System.Drawing.Point(13, 19);
            this.chkLocalOcrEnable.Size = new System.Drawing.Size(99, 21);
            this.chkLocalOcrEnable.TabIndex = 4;
            this.chkLocalOcrEnable.Text = "启用本地识别";
            this.chkLocalOcrEnable.UseVisualStyleBackColor = true;
            this.chkLocalOcrEnable.TipControl = tipMsg;
            this.chkLocalOcrEnable.TipText = "功能：是否开启本地库识别。\r\n说明：开启后，可通过本地库进行OCR识别。\r\n本地识别：速度更稳定，但受限于特征库，识别结果没网络识别精确！\r\n\r\n可根据实际需求，进行选择！";
            // 
            // grpLocalOcr
            // 
            this.grpLocalOcr.Location = new System.Drawing.Point(3, 130);
            this.grpLocalOcr.Size = new System.Drawing.Size(370, 170);
            this.grpLocalOcr.TabIndex = 45;
            this.grpLocalOcr.TabStop = false;
            this.grpLocalOcr.Text = "选择启用的本地识别引擎";
            // 
            // tbCapture
            // 
            this.tbCapture.Controls.Add(this.tabControl2);
            this.tbCapture.Location = new System.Drawing.Point(4, 22);
            this.tbCapture.Padding = new System.Windows.Forms.Padding(3);
            this.tbCapture.Size = new System.Drawing.Size(387, 400);
            this.tbCapture.TabIndex = 1;
            this.tbCapture.Text = "截屏";
            this.tbCapture.UseVisualStyleBackColor = true;
            // 
            // tabControl2
            // 
            this.tabControl2.Controls.Add(this.tbCaptureSetting);
            this.tabControl2.Controls.Add(this.tbScrollCatpure);
            this.tabControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl2.Location = new System.Drawing.Point(3, 3);
            this.tabControl2.BackColor = Color.White;
            this.tabControl2.SelectedIndex = 0;
            this.tabControl2.Size = new System.Drawing.Size(381, 380);
            this.tabControl2.TabIndex = 47;
            // 
            // tbCaptureSetting
            // 
            this.tbCaptureSetting.Controls.Add(this.grpCaptureNormalSetting);
            this.tbCaptureSetting.Controls.Add(this.grpAfterCapture);
            this.tbCaptureSetting.Location = new System.Drawing.Point(4, 26);
            this.tbCaptureSetting.Padding = new System.Windows.Forms.Padding(3);
            this.tbCaptureSetting.Size = new System.Drawing.Size(373, 395);
            this.tbCaptureSetting.TabIndex = 0;
            this.tbCaptureSetting.Text = "截屏配置";
            this.tbCaptureSetting.UseVisualStyleBackColor = true;
            // 
            // grpCaptureNormalSetting
            // 
            this.grpCaptureNormalSetting.Controls.Add(this.numericUpDown1);
            this.grpCaptureNormalSetting.Controls.Add(this.numCaptureBorderWidth);
            this.grpCaptureNormalSetting.Controls.Add(this.lblCaptureDelay);
            this.grpCaptureNormalSetting.Controls.Add(this.lblDelay_Second);
            this.grpCaptureNormalSetting.Controls.Add(this.btnCaptureBorderColor);
            this.grpCaptureNormalSetting.Controls.Add(this.lblCaptureBorder);
            this.grpCaptureNormalSetting.Controls.Add(this.lblCaptureMouseWheel);
            this.grpCaptureNormalSetting.Controls.Add(this.cbCatpureScroll);
            this.grpCaptureNormalSetting.Controls.Add(this.chkZoom);
            this.grpCaptureNormalSetting.Controls.Add(this.lblCaptureBorderWidthUnit);
            this.grpCaptureNormalSetting.Controls.Add(this.chkCaptureAnimal);
            this.grpCaptureNormalSetting.Controls.Add(this.chkCircleFangDa);
            this.grpCaptureNormalSetting.Controls.Add(this.chkCrocessLine);
            this.grpCaptureNormalSetting.Controls.Add(this.chkAutoWindow);
            this.grpCaptureNormalSetting.Controls.Add(this.chkAutoControl);
            this.grpCaptureNormalSetting.Controls.Add(this.lblCaptureBorderWidth);
            this.grpCaptureNormalSetting.Location = new System.Drawing.Point(3, 3);
            this.grpCaptureNormalSetting.Size = new System.Drawing.Size(364, 156);
            this.grpCaptureNormalSetting.TabIndex = 46;
            this.grpCaptureNormalSetting.TabStop = false;
            this.grpCaptureNormalSetting.Text = "通用";
            // 
            // numCaptureBorderWidth
            // 
            this.numCaptureBorderWidth.Location = new System.Drawing.Point(239, 96);
            this.numCaptureBorderWidth.Name = "numCaptureBorderWidth";
            this.numCaptureBorderWidth.Size = new System.Drawing.Size(46, 23);
            this.numCaptureBorderWidth.TabIndex = 27;
            this.numCaptureBorderWidth.Tag = "截图边框宽度";
            this.numCaptureBorderWidth.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // lblCaptureDelay
            // 
            this.lblCaptureDelay.AutoSize = true;
            this.lblCaptureDelay.Location = new System.Drawing.Point(180, 129);
            this.lblCaptureDelay.Size = new System.Drawing.Size(35, 17);
            this.lblCaptureDelay.TabIndex = 2;
            this.lblCaptureDelay.Text = "延时截图";
            // 
            // numericUpDown1
            // 
            this.numericUpDown1.Location = new System.Drawing.Point(239, 127);
            this.numericUpDown1.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numericUpDown1.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numericUpDown1.Name = "numericUpDown1";
            this.numericUpDown1.Size = new System.Drawing.Size(46, 23);
            this.numericUpDown1.TabIndex = 27;
            this.numericUpDown1.Tag = "截图延时";
            this.numericUpDown1.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // lblDelay_Second
            // 
            this.lblDelay_Second.AutoSize = true;
            this.lblDelay_Second.Location = new System.Drawing.Point(288, 129);
            this.lblDelay_Second.Size = new System.Drawing.Size(20, 17);
            this.lblDelay_Second.TabIndex = 2;
            this.lblDelay_Second.Text = "秒";
            this.lblDelay_Second.TipControl = tipMsg;
            this.lblDelay_Second.TipText = "功能：延时截图前，等待设定的时间后再触发截图。\r\n说明：仅针对延时截图功能";
            // 
            // imageButton1
            // 
            this.btnCaptureBorderColor.IsColorButton = true;
            this.btnCaptureBorderColor.BackColor = System.Drawing.Color.White;
            this.btnCaptureBorderColor.ImageColor = System.Drawing.Color.Black;
            this.btnCaptureBorderColor.Location = new System.Drawing.Point(85, 96);
            this.btnCaptureBorderColor.Size = new System.Drawing.Size(26, 26);
            this.btnCaptureBorderColor.TabIndex = 44;
            this.btnCaptureBorderColor.Tag = "截图边框颜色";
            this.btnCaptureBorderColor.UseVisualStyleBackColor = false;
            this.btnCaptureBorderColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // lblCaptureBorder
            // 
            this.lblCaptureBorder.AutoSize = true;
            this.lblCaptureBorder.Location = new System.Drawing.Point(13, 101);
            this.lblCaptureBorder.Size = new System.Drawing.Size(71, 17);
            this.lblCaptureBorder.TabIndex = 43;
            this.lblCaptureBorder.Text = "截图边框色:";
            // 
            // lblCaptureMouseWheel
            // 
            this.lblCaptureMouseWheel.AutoSize = true;
            this.lblCaptureMouseWheel.Location = new System.Drawing.Point(13, 129);
            this.lblCaptureMouseWheel.Size = new System.Drawing.Size(71, 17);
            this.lblCaptureMouseWheel.TabIndex = 43;
            this.lblCaptureMouseWheel.Text = "滚动鼠标";
            // 
            // cbCatpureScroll
            // 
            this.cbCatpureScroll.TextAlign = ContentAlignment.MiddleLeft;
            this.cbCatpureScroll.Location = new System.Drawing.Point(72, 125);
            this.cbCatpureScroll.Size = new System.Drawing.Size(80, 25);
            this.cbCatpureScroll.TabIndex = 5;
            this.cbCatpureScroll.Tag = "滚动鼠标";
            this.cbCatpureScroll.TipControl = tipMsg;
            this.cbCatpureScroll.TipText = "功能：选择截图模式下，滚动鼠标时的操作，默认为切换区域。\r\n说明：\n选择切换区域：鼠标向上滚动，放大选择区域，向下滚动，缩小选择区域。\n选择放大镜：鼠标向上滚动，放大放大镜，反之，缩小放大镜。";
            // 
            // chkZoom
            // 
            this.chkZoom.AutoSize = true;
            this.chkZoom.Checked = true;
            this.chkZoom.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkZoom.Location = new System.Drawing.Point(13, 20);
            this.chkZoom.Size = new System.Drawing.Size(87, 21);
            this.chkZoom.TabIndex = 26;
            this.chkZoom.Text = "显示放大镜";
            this.chkZoom.UseVisualStyleBackColor = true;
            // 
            // lblCaptureBorderWidthUnit
            // 
            this.lblCaptureBorderWidthUnit.AutoSize = true;
            this.lblCaptureBorderWidthUnit.Location = new System.Drawing.Point(287, 101);
            this.lblCaptureBorderWidthUnit.Size = new System.Drawing.Size(32, 17);
            this.lblCaptureBorderWidthUnit.TabIndex = 2;
            this.lblCaptureBorderWidthUnit.Text = "像素";
            // 
            // chkCaptureAnimal
            // 
            this.chkCaptureAnimal.AutoSize = true;
            this.chkCaptureAnimal.Checked = true;
            this.chkCaptureAnimal.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkCaptureAnimal.Location = new System.Drawing.Point(13, 46);
            this.chkCaptureAnimal.Size = new System.Drawing.Size(99, 21);
            this.chkCaptureAnimal.TabIndex = 0;
            this.chkCaptureAnimal.Text = "截图动画效果";
            this.chkCaptureAnimal.UseVisualStyleBackColor = true;
            // 
            // chkCircleFangDa
            // 
            this.chkCircleFangDa.AutoSize = true;
            this.chkCircleFangDa.Location = new System.Drawing.Point(182, 20);
            this.chkCircleFangDa.Size = new System.Drawing.Size(87, 21);
            this.chkCircleFangDa.TabIndex = 26;
            this.chkCircleFangDa.Text = "圆形放大镜";
            this.chkCircleFangDa.UseVisualStyleBackColor = true;
            // 
            // chkCrocessLine
            // 
            this.chkCrocessLine.AutoSize = true;
            this.chkCrocessLine.Location = new System.Drawing.Point(182, 46);
            this.chkCrocessLine.Size = new System.Drawing.Size(111, 21);
            this.chkCrocessLine.TabIndex = 26;
            this.chkCrocessLine.Text = "显示全屏十字线";
            this.chkCrocessLine.UseVisualStyleBackColor = true;
            // 
            // chkAutoWindow
            // 
            this.chkAutoWindow.AutoSize = true;
            this.chkAutoWindow.Checked = true;
            this.chkAutoWindow.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoWindow.Location = new System.Drawing.Point(13, 74);
            this.chkAutoWindow.Size = new System.Drawing.Size(99, 21);
            this.chkAutoWindow.TabIndex = 26;
            this.chkAutoWindow.Text = "自动检测窗口";
            this.chkAutoWindow.UseVisualStyleBackColor = true;
            // 
            // chkAutoControl
            // 
            this.chkAutoControl.AutoSize = true;
            this.chkAutoControl.Checked = true;
            this.chkAutoControl.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoControl.Location = new System.Drawing.Point(182, 74);
            this.chkAutoControl.Size = new System.Drawing.Size(123, 21);
            this.chkAutoControl.TabIndex = 26;
            this.chkAutoControl.Text = "自动检测窗口元素";
            this.chkAutoControl.UseVisualStyleBackColor = true;
            this.chkAutoControl.TipControl = tipMsg;
            this.chkAutoControl.TipText = "功能：自动检测窗口所有可见的元素。\r\n说明：可在截图过程中按【Ctrl键】切换窗口模式";
            // 
            // lblCaptureBorderWidth
            // 
            this.lblCaptureBorderWidth.AutoSize = true;
            this.lblCaptureBorderWidth.Location = new System.Drawing.Point(180, 101);
            this.lblCaptureBorderWidth.Size = new System.Drawing.Size(59, 17);
            this.lblCaptureBorderWidth.TabIndex = 2;
            this.lblCaptureBorderWidth.Text = "边框宽度:";
            // 
            // grpAfterCapture
            // 
            this.grpAfterCapture.Controls.Add(this.btnChangeCaptureLocation);
            this.grpAfterCapture.Controls.Add(this.btnOpenCaptureLocation);
            this.grpAfterCapture.Controls.Add(this.txtCaptureFileName);
            this.grpAfterCapture.Controls.Add(this.lblCaptureFileName);
            this.grpAfterCapture.Controls.Add(this.lblMaxHistoryCount);
            this.grpAfterCapture.Controls.Add(this.numMaxHistoryCount);
            this.grpAfterCapture.Controls.Add(this.lblFileNameParam);
            this.grpAfterCapture.Controls.Add(this.lblFileName);
            this.grpAfterCapture.Controls.Add(this.txtCaptureLocation);
            this.grpAfterCapture.Controls.Add(this.lblFilePath);
            this.grpAfterCapture.Controls.Add(this.chkCaptureTip);
            this.grpAfterCapture.Controls.Add(this.chkAutoSaveCapture);
            this.grpAfterCapture.Controls.Add(this.chkSaveToClipborad);
            this.grpAfterCapture.Location = new System.Drawing.Point(3, 163);
            this.grpAfterCapture.Size = new System.Drawing.Size(364, 172);
            this.grpAfterCapture.TabIndex = 28;
            this.grpAfterCapture.TabStop = false;
            this.grpAfterCapture.Text = "截图完成时";
            // 
            // btnChangeCaptureLocation
            // 
            this.btnChangeCaptureLocation.Location = new System.Drawing.Point(302, 117);
            this.btnChangeCaptureLocation.Size = new System.Drawing.Size(52, 25);
            this.btnChangeCaptureLocation.TabIndex = 7;
            this.btnChangeCaptureLocation.Text = "更改";
            this.btnChangeCaptureLocation.UseVisualStyleBackColor = true;
            this.btnChangeCaptureLocation.Click += new System.EventHandler(this.btnChangeCaptureLocation_Click);
            // 
            // btnOpenCaptureLocation
            // 
            this.btnOpenCaptureLocation.Location = new System.Drawing.Point(246, 117);
            this.btnOpenCaptureLocation.Size = new System.Drawing.Size(52, 25);
            this.btnOpenCaptureLocation.TabIndex = 8;
            this.btnOpenCaptureLocation.Text = "打开";
            this.btnOpenCaptureLocation.UseVisualStyleBackColor = true;
            this.btnOpenCaptureLocation.Click += new System.EventHandler(this.btnOpenCaptureLocation_Click);
            // 
            // txtCaptureFileName
            // 
            this.txtCaptureFileName.Location = new System.Drawing.Point(54, 49);
            this.txtCaptureFileName.Name = "txtCaptureFileName";
            this.txtCaptureFileName.Size = new System.Drawing.Size(185, 23);
            this.txtCaptureFileName.TabIndex = 6;
            this.txtCaptureFileName.Tag = "截图文件名";
            this.txtCaptureFileName.Text = CommonString.DefaultImageFormat;
            this.txtCaptureFileName.TextChanged += new System.EventHandler(this.txtCaptureFileName_TextChanged);
            // 
            // lblCaptureFileName
            // 
            this.lblCaptureFileName.Location = new System.Drawing.Point(51, 75);
            this.lblCaptureFileName.Name = "lblCaptureFileName";
            this.lblCaptureFileName.Size = new System.Drawing.Size(193, 40);
            this.lblCaptureFileName.TabIndex = 5;
            this.lblCaptureFileName.Font = CommonString.GetSysNormalFont(10f);
            this.lblCaptureFileName.Text = "示例";
            // 
            // lblMaxHistoryCount
            // 
            this.lblMaxHistoryCount.AutoSize = true;
            this.lblMaxHistoryCount.Location = new System.Drawing.Point(11, 148);
            this.lblMaxHistoryCount.Size = new System.Drawing.Size(107, 17);
            this.lblMaxHistoryCount.TabIndex = 2;
            this.lblMaxHistoryCount.Text = "最大历史记录数量:";
            // 
            // numMaxHistoryCount
            // 
            this.numMaxHistoryCount.Location = new System.Drawing.Point(129, 143);
            this.numMaxHistoryCount.Name = "numMaxHistoryCount";
            this.numMaxHistoryCount.Size = new System.Drawing.Size(44, 23);
            this.numMaxHistoryCount.TabIndex = 27;
            this.numMaxHistoryCount.Tag = "最大历史记录数量";
            this.numMaxHistoryCount.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // lblFileNameParam
            // 
            this.lblFileNameParam.AutoSize = true;
            this.lblFileNameParam.Location = new System.Drawing.Point(243, 44);
            this.lblFileNameParam.Size = new System.Drawing.Size(122, 68);
            this.lblFileNameParam.TabIndex = 5;
            this.lblFileNameParam.Text = "%n 年份；%y 月份\r\n%r 天数；%s 小时\r\n%f 分钟；%m 秒钟\r\n%t 时间戳；%g 随机";
            // 
            // lblFileName
            // 
            this.lblFileName.AutoSize = true;
            this.lblFileName.Location = new System.Drawing.Point(6, 51);
            this.lblFileName.Size = new System.Drawing.Size(47, 17);
            this.lblFileName.TabIndex = 5;
            this.lblFileName.Text = "文件名";
            // 
            // txtCaptureLocation
            // 
            this.txtCaptureLocation.Location = new System.Drawing.Point(53, 118);
            this.txtCaptureLocation.Name = "txtCaptureLocation";
            this.txtCaptureLocation.ReadOnly = true;
            this.txtCaptureLocation.Size = new System.Drawing.Size(186, 23);
            this.txtCaptureLocation.TabIndex = 6;
            this.txtCaptureLocation.Tag = "截图文件保存路径";
            // 
            // lblFilePath
            // 
            this.lblFilePath.AutoSize = true;
            this.lblFilePath.Location = new System.Drawing.Point(17, 121);
            this.lblFilePath.Size = new System.Drawing.Size(35, 17);
            this.lblFilePath.TabIndex = 5;
            this.lblFilePath.Text = "路径";
            // 
            // chkCaptureTip
            // 
            this.chkCaptureTip.AutoSize = true;
            this.chkCaptureTip.Checked = true;
            this.chkCaptureTip.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkCaptureTip.Location = new System.Drawing.Point(96, 23);
            this.chkCaptureTip.Size = new System.Drawing.Size(107, 21);
            this.chkCaptureTip.TabIndex = 0;
            this.chkCaptureTip.Text = "显示Toast通知";
            this.chkCaptureTip.UseVisualStyleBackColor = true;
            this.chkCaptureTip.TipControl = tipMsg;
            this.chkCaptureTip.TipText = "功能：截图成功后，右下角提示。";
            // 
            // chkAutoSaveCapture
            // 
            this.chkAutoSaveCapture.AutoSize = true;
            this.chkAutoSaveCapture.Checked = true;
            this.chkAutoSaveCapture.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoSaveCapture.Location = new System.Drawing.Point(13, 23);
            this.chkAutoSaveCapture.Size = new System.Drawing.Size(75, 21);
            this.chkAutoSaveCapture.TabIndex = 0;
            this.chkAutoSaveCapture.Text = "自动保存";
            this.chkAutoSaveCapture.UseVisualStyleBackColor = true;
            // 
            // chkSaveToClipborad
            // 
            this.chkSaveToClipborad.AutoSize = true;
            this.chkSaveToClipborad.Checked = true;
            this.chkSaveToClipborad.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkSaveToClipborad.Location = new System.Drawing.Point(233, 23);
            this.chkSaveToClipborad.Size = new System.Drawing.Size(99, 21);
            this.chkSaveToClipborad.TabIndex = 0;
            this.chkSaveToClipborad.Text = "复制到粘贴板";
            this.chkSaveToClipborad.UseVisualStyleBackColor = true;
            // 
            // tbScrollCatpure
            // 
            this.tbScrollCatpure.Controls.Add(this.gbWhileCapturing);
            this.tbScrollCatpure.Controls.Add(this.gbBeforeCapture);
            this.tbScrollCatpure.Controls.Add(this.lblSelectedRectangle);
            this.tbScrollCatpure.Controls.Add(this.lblControlText);
            this.tbScrollCatpure.Location = new System.Drawing.Point(4, 22);
            this.tbScrollCatpure.Padding = new System.Windows.Forms.Padding(3);
            this.tbScrollCatpure.Size = new System.Drawing.Size(373, 336);
            this.tbScrollCatpure.TabIndex = 1;
            this.tbScrollCatpure.Text = "滚动截屏";
            this.tbScrollCatpure.UseVisualStyleBackColor = true;
            // 
            // gbWhileCapturing
            // 
            this.gbWhileCapturing.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.gbWhileCapturing.Controls.Add(this.cbScrollMethod);
            this.gbWhileCapturing.Controls.Add(this.nudScrollDelay);
            this.gbWhileCapturing.Controls.Add(this.lblScrollDelay);
            this.gbWhileCapturing.Controls.Add(this.lblScrollMethod);
            this.gbWhileCapturing.Controls.Add(this.lblScrollSpeed);
            this.gbWhileCapturing.Controls.Add(this.nudScrollSpeed);
            this.gbWhileCapturing.Controls.Add(this.picScrollSpeed);
            this.gbWhileCapturing.Location = new System.Drawing.Point(5, 56);
            this.gbWhileCapturing.Size = new System.Drawing.Size(362, 85);
            this.gbWhileCapturing.TabIndex = 29;
            this.gbWhileCapturing.TabStop = false;
            this.gbWhileCapturing.Text = "捕捉中";
            // 
            // lblScrollSpeed
            // 
            this.lblScrollSpeed.AutoSize = true;
            this.lblScrollSpeed.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblScrollSpeed.Location = new System.Drawing.Point(27, 52);
            this.lblScrollSpeed.Size = new System.Drawing.Size(68, 17);
            this.lblScrollSpeed.TabIndex = 4;
            this.lblScrollSpeed.Text = "倍速滚动：";
            // 
            // nudScrollSpeed
            // 
            this.nudScrollSpeed.Location = new System.Drawing.Point(92, 52);
            this.nudScrollSpeed.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.nudScrollSpeed.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.nudScrollSpeed.Name = "nudScrollDelay";
            this.nudScrollSpeed.Size = new System.Drawing.Size(53, 23);
            this.nudScrollSpeed.TabIndex = 9;
            this.nudScrollSpeed.Tag = "ScrollAmount";
            this.nudScrollSpeed.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nudScrollSpeed.Value = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.nudScrollSpeed.BringToFront();
            // 
            // picScrollSpeed
            // 
            this.picScrollSpeed.BackColor = Color.White;
            this.picScrollSpeed.Image = global::OCRTools.Properties.Resources.帮助;
            this.picScrollSpeed.Location = new System.Drawing.Point(150, 52);
            this.picScrollSpeed.Name = "picScrollSpeed";
            this.picScrollSpeed.Size = new System.Drawing.Size(28, 24);
            this.picScrollSpeed.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.picScrollSpeed.TabIndex = 44;
            this.picScrollSpeed.TabStop = false;
            this.tipMsg.SetToolTip(this.picScrollSpeed, "功能：滚动截图时，倍速滚动。\r\n说明：\r\n默认倍数为1，滚动越快越容易丢帧！");
            // 
            // cbScrollMethod
            // 
            this.cbScrollMethod.TextAlign = ContentAlignment.MiddleLeft;
            this.cbScrollMethod.Location = new System.Drawing.Point(92, 18);
            this.cbScrollMethod.Size = new System.Drawing.Size(133, 25);
            this.cbScrollMethod.TabIndex = 5;
            this.cbScrollMethod.Tag = "ScrollMethod";
            // 
            // nudScrollDelay
            // 
            this.nudScrollDelay.Location = new System.Drawing.Point(284, 19);
            this.nudScrollDelay.Maximum = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.nudScrollDelay.Minimum = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.nudScrollDelay.Name = "nudScrollDelay";
            this.nudScrollDelay.Size = new System.Drawing.Size(53, 23);
            this.nudScrollDelay.TabIndex = 9;
            this.nudScrollDelay.Tag = "ScrollDelay";
            this.nudScrollDelay.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nudScrollDelay.Value = new decimal(new int[] {
            500,
            0,
            0,
            0});
            // 
            // lblScrollDelay
            // 
            this.lblScrollDelay.AutoSize = true;
            this.lblScrollDelay.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblScrollDelay.Location = new System.Drawing.Point(246, 22);
            this.lblScrollDelay.Size = new System.Drawing.Size(44, 17);
            this.lblScrollDelay.TabIndex = 8;
            this.lblScrollDelay.Text = "延迟：";
            // 
            // lblScrollMethod
            // 
            this.lblScrollMethod.AutoSize = true;
            this.lblScrollMethod.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblScrollMethod.Location = new System.Drawing.Point(27, 22);
            this.lblScrollMethod.Size = new System.Drawing.Size(68, 17);
            this.lblScrollMethod.TabIndex = 4;
            this.lblScrollMethod.Text = "滚动方式：";
            // 
            // gbBeforeCapture
            // 
            this.gbBeforeCapture.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.gbBeforeCapture.Controls.Add(this.cbScrollTopMethodBeforeCapture);
            this.gbBeforeCapture.Controls.Add(this.nudStartDelay);
            this.gbBeforeCapture.Controls.Add(this.lblScrollTopMethodBeforeCapture);
            this.gbBeforeCapture.Controls.Add(this.lblStartDelay);
            this.gbBeforeCapture.Location = new System.Drawing.Point(5, 6);
            this.gbBeforeCapture.Size = new System.Drawing.Size(362, 48);
            this.gbBeforeCapture.TabIndex = 28;
            this.gbBeforeCapture.TabStop = false;
            this.gbBeforeCapture.Text = "捕捉前";
            // 
            // cbScrollTopMethodBeforeCapture
            // 
            this.cbScrollTopMethodBeforeCapture.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.cbScrollTopMethodBeforeCapture.Location = new System.Drawing.Point(92, 17);
            this.cbScrollTopMethodBeforeCapture.Size = new System.Drawing.Size(133, 25);
            this.cbScrollTopMethodBeforeCapture.TabIndex = 22;
            this.cbScrollTopMethodBeforeCapture.Tag = "ScrollTopMethodBeforeCapture";
            // 
            // nudStartDelay
            // 
            this.nudStartDelay.Location = new System.Drawing.Point(284, 17);
            this.nudStartDelay.Maximum = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.nudStartDelay.Minimum = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.nudStartDelay.Name = "nudStartDelay";
            this.nudStartDelay.Size = new System.Drawing.Size(53, 23);
            this.nudStartDelay.TabIndex = 7;
            this.nudStartDelay.Tag = "StartDelay";
            this.nudStartDelay.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nudStartDelay.Value = new decimal(new int[] {
            500,
            0,
            0,
            0});
            // 
            // lblScrollTopMethodBeforeCapture
            // 
            this.lblScrollTopMethodBeforeCapture.AutoSize = true;
            this.lblScrollTopMethodBeforeCapture.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblScrollTopMethodBeforeCapture.Location = new System.Drawing.Point(15, 21);
            this.lblScrollTopMethodBeforeCapture.Size = new System.Drawing.Size(80, 17);
            this.lblScrollTopMethodBeforeCapture.TabIndex = 21;
            this.lblScrollTopMethodBeforeCapture.Text = "滚动到顶部：";
            // 
            // lblStartDelay
            // 
            this.lblStartDelay.AutoSize = true;
            this.lblStartDelay.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblStartDelay.Location = new System.Drawing.Point(246, 21);
            this.lblStartDelay.Size = new System.Drawing.Size(44, 17);
            this.lblStartDelay.TabIndex = 6;
            this.lblStartDelay.Text = "延迟：";
            // 
            // lblSelectedRectangle
            // 
            this.lblSelectedRectangle.AutoSize = true;
            this.lblSelectedRectangle.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblSelectedRectangle.Location = new System.Drawing.Point(367, 42);
            this.lblSelectedRectangle.Name = "lblSelectedRectangle";
            this.lblSelectedRectangle.Size = new System.Drawing.Size(0, 17);
            this.lblSelectedRectangle.TabIndex = 27;
            // 
            // lblControlText
            // 
            this.lblControlText.AutoSize = true;
            this.lblControlText.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblControlText.Location = new System.Drawing.Point(367, 19);
            this.lblControlText.Name = "lblControlText";
            this.lblControlText.Size = new System.Drawing.Size(0, 17);
            this.lblControlText.TabIndex = 26;
            // 
            // tbPin
            // 
            this.tbPin.Controls.Add(this.grpPinNormalSetting);
            this.tbPin.Controls.Add(this.grpTextToImage);
            this.tbPin.Location = new System.Drawing.Point(4, 22);
            this.tbPin.Padding = new System.Windows.Forms.Padding(3);
            this.tbPin.Size = new System.Drawing.Size(387, 368);
            this.tbPin.TabIndex = 4;
            this.tbPin.Text = "贴图";
            this.tbPin.UseVisualStyleBackColor = true;
            // 
            // grpPinNormalSetting
            // 
            this.grpPinNormalSetting.Controls.Add(this.btnTieTuShadowColor);
            this.grpPinNormalSetting.Controls.Add(this.lblPinShadowColor);
            this.grpPinNormalSetting.Controls.Add(this.lblPinShadowBorderUnit);
            this.grpPinNormalSetting.Controls.Add(this.chkTieTuFocus);
            this.grpPinNormalSetting.Controls.Add(this.chkShowTieTuShadow);
            this.grpPinNormalSetting.Controls.Add(this.lblPinShadowBorderWidth);
            this.grpPinNormalSetting.Controls.Add(this.chkTieTuShark);
            this.grpPinNormalSetting.Controls.Add(this.lblNewWindowAction);
            this.grpPinNormalSetting.Controls.Add(this.numShadowWidth);
            this.grpPinNormalSetting.Controls.Add(this.chkTieTuUseCaptureLocation);
            this.grpPinNormalSetting.Location = new System.Drawing.Point(3, 6);
            this.grpPinNormalSetting.Size = new System.Drawing.Size(374, 102);
            this.grpPinNormalSetting.TabIndex = 45;
            this.grpPinNormalSetting.TabStop = false;
            this.grpPinNormalSetting.Text = "通用";
            // 
            // btnTieTuShadowColor
            // 
            this.btnTieTuShadowColor.IsColorButton = true;
            this.btnContentFontColor.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.btnTieTuShadowColor.BackColor = System.Drawing.Color.White;
            this.btnTieTuShadowColor.ImageColor = System.Drawing.Color.Black;
            this.btnTieTuShadowColor.Location = new System.Drawing.Point(88, 21);
            this.btnTieTuShadowColor.Size = new System.Drawing.Size(26, 26);
            this.btnTieTuShadowColor.TabIndex = 4;
            this.btnTieTuShadowColor.Tag = "贴图窗口阴影色";
            this.btnTieTuShadowColor.UseVisualStyleBackColor = false;
            this.btnTieTuShadowColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // lblPinShadowColor
            // 
            this.lblPinShadowColor.AutoSize = true;
            this.lblPinShadowColor.Location = new System.Drawing.Point(13, 26);
            this.lblPinShadowColor.Size = new System.Drawing.Size(71, 17);
            this.lblPinShadowColor.TabIndex = 3;
            this.lblPinShadowColor.Text = "窗口阴影色:";
            // 
            // lblPinShadowBorderUnit
            // 
            this.lblPinShadowBorderUnit.AutoSize = true;
            this.lblPinShadowBorderUnit.Location = new System.Drawing.Point(254, 26);
            this.lblPinShadowBorderUnit.Size = new System.Drawing.Size(32, 17);
            this.lblPinShadowBorderUnit.TabIndex = 29;
            this.lblPinShadowBorderUnit.Text = "像素";
            // 
            // chkTieTuFocus
            // 
            this.chkTieTuFocus.AutoSize = true;
            this.chkTieTuFocus.Checked = true;
            this.chkTieTuFocus.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkTieTuFocus.Location = new System.Drawing.Point(137, 77);
            this.chkTieTuFocus.Size = new System.Drawing.Size(75, 21);
            this.chkTieTuFocus.TabIndex = 27;
            this.chkTieTuFocus.Text = "激活窗口";
            this.chkTieTuFocus.UseVisualStyleBackColor = true;
            // 
            // chkShowTieTuShadow
            // 
            this.chkShowTieTuShadow.AutoSize = true;
            this.chkShowTieTuShadow.Checked = true;
            this.chkShowTieTuShadow.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkShowTieTuShadow.Location = new System.Drawing.Point(221, 77);
            this.chkShowTieTuShadow.Size = new System.Drawing.Size(75, 21);
            this.chkShowTieTuShadow.TabIndex = 27;
            this.chkShowTieTuShadow.Text = "贴图阴影";
            this.chkShowTieTuShadow.UseVisualStyleBackColor = true;
            // 
            // lblPinShadowBorderWidth
            // 
            this.lblPinShadowBorderWidth.AutoSize = true;
            this.lblPinShadowBorderWidth.Location = new System.Drawing.Point(137, 26);
            this.lblPinShadowBorderWidth.Size = new System.Drawing.Size(59, 17);
            this.lblPinShadowBorderWidth.TabIndex = 28;
            this.lblPinShadowBorderWidth.Text = "阴影宽度:";
            // 
            // chkTieTuShark
            // 
            this.chkTieTuShark.AutoSize = true;
            this.chkTieTuShark.Checked = true;
            this.chkTieTuShark.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkTieTuShark.Location = new System.Drawing.Point(79, 77);
            this.chkTieTuShark.Size = new System.Drawing.Size(51, 21);
            this.chkTieTuShark.TabIndex = 27;
            this.chkTieTuShark.Text = "闪烁";
            this.chkTieTuShark.UseVisualStyleBackColor = true;
            // 
            // lblNewWindowAction
            // 
            this.lblNewWindowAction.AutoSize = true;
            this.lblNewWindowAction.Location = new System.Drawing.Point(13, 78);
            this.lblNewWindowAction.Size = new System.Drawing.Size(59, 17);
            this.lblNewWindowAction.TabIndex = 3;
            this.lblNewWindowAction.Text = "新建窗口:";
            // 
            // numShadowWidth
            // 
            this.numShadowWidth.Location = new System.Drawing.Point(201, 22);
            this.numShadowWidth.Name = "numShadowWidth";
            this.numShadowWidth.Size = new System.Drawing.Size(52, 23);
            this.numShadowWidth.TabIndex = 30;
            this.numShadowWidth.Tag = "贴图窗口阴影宽度";
            this.numShadowWidth.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // chkTieTuUseCaptureLocation
            // 
            this.chkTieTuUseCaptureLocation.AutoSize = true;
            this.chkTieTuUseCaptureLocation.Checked = true;
            this.chkTieTuUseCaptureLocation.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkTieTuUseCaptureLocation.Location = new System.Drawing.Point(13, 52);
            this.chkTieTuUseCaptureLocation.Size = new System.Drawing.Size(171, 21);
            this.chkTieTuUseCaptureLocation.TabIndex = 27;
            this.chkTieTuUseCaptureLocation.Text = "截图贴图时使用截屏的位置";
            this.chkTieTuUseCaptureLocation.UseVisualStyleBackColor = true;
            // 
            // grpTextToImage
            // 
            this.grpTextToImage.Controls.Add(this.numTieTuMaxWidth);
            this.grpTextToImage.Controls.Add(this.numTieTuBorderWidth);
            this.grpTextToImage.Controls.Add(this.btnTieTuDefaultFont);
            this.grpTextToImage.Controls.Add(this.btnTieTuBackColor);
            this.grpTextToImage.Controls.Add(this.btnTieTuFont);
            this.grpTextToImage.Controls.Add(this.btnTieTuFontColor);
            this.grpTextToImage.Controls.Add(this.chkUseContentColor);
            this.grpTextToImage.Controls.Add(this.chkIngoreHtml);
            this.grpTextToImage.Controls.Add(this.lblPinMaxWidth);
            this.grpTextToImage.Controls.Add(this.lblPinMaxWidthUnit);
            this.grpTextToImage.Controls.Add(this.lblPinBorderMargin);
            this.grpTextToImage.Controls.Add(this.lblPinBorderMarginUnit);
            this.grpTextToImage.Controls.Add(this.lblTieTuLabel);
            this.grpTextToImage.Location = new System.Drawing.Point(3, 115);
            this.grpTextToImage.Size = new System.Drawing.Size(374, 209);
            this.grpTextToImage.TabIndex = 28;
            this.grpTextToImage.TabStop = false;
            this.grpTextToImage.Text = "文字转图片";
            // 
            // numTieTuMaxWidth
            // 
            this.numTieTuMaxWidth.Location = new System.Drawing.Point(250, 41);
            this.numTieTuMaxWidth.Maximum = new decimal(new int[] {
            2000,
            0,
            0,
            0});
            this.numTieTuMaxWidth.Minimum = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numTieTuMaxWidth.Name = "numTieTuMaxWidth";
            this.numTieTuMaxWidth.Size = new System.Drawing.Size(52, 23);
            this.numTieTuMaxWidth.TabIndex = 30;
            this.numTieTuMaxWidth.Tag = "贴图图片最大宽度";
            this.numTieTuMaxWidth.Value = new decimal(new int[] {
            600,
            0,
            0,
            0});
            // 
            // numTieTuBorderWidth
            // 
            this.numTieTuBorderWidth.Location = new System.Drawing.Point(65, 41);
            this.numTieTuBorderWidth.Name = "numTieTuBorderWidth";
            this.numTieTuBorderWidth.Size = new System.Drawing.Size(52, 23);
            this.numTieTuBorderWidth.TabIndex = 30;
            this.numTieTuBorderWidth.Tag = "贴图页边距宽度";
            this.numTieTuBorderWidth.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // btnTieTuDefaultFont
            // 
            this.btnTieTuDefaultFont.Location = new System.Drawing.Point(286, 175);
            this.btnTieTuDefaultFont.Size = new System.Drawing.Size(85, 26);
            this.btnTieTuDefaultFont.TabIndex = 43;
            this.btnTieTuDefaultFont.Tag = "";
            this.btnTieTuDefaultFont.Text = "默认字体";
            this.btnTieTuDefaultFont.UseVisualStyleBackColor = false;
            this.btnTieTuDefaultFont.Click += new System.EventHandler(this.btnContentDefault_Click);
            // 
            // btnTieTuBackColor
            // 
            this.btnTieTuBackColor.IsColorButton = true;
            this.btnTieTuBackColor.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.btnTieTuBackColor.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnTieTuBackColor.ImageColor = System.Drawing.Color.Black;
            this.btnTieTuBackColor.Location = new System.Drawing.Point(286, 147);
            this.btnTieTuBackColor.Size = new System.Drawing.Size(85, 26);
            this.btnTieTuBackColor.TabIndex = 42;
            this.btnTieTuBackColor.Tag = "贴图背景颜色";
            this.btnTieTuBackColor.Text = "背景色";
            this.btnTieTuBackColor.Name = "btnTieTuBackColor";
            this.btnTieTuBackColor.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnTieTuBackColor.UseVisualStyleBackColor = false;
            this.btnTieTuBackColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // btnTieTuFont
            // 
            this.btnTieTuFont.Location = new System.Drawing.Point(286, 91);
            this.btnTieTuFont.Size = new System.Drawing.Size(85, 26);
            this.btnTieTuFont.TabIndex = 40;
            this.btnTieTuFont.TabStop = false;
            this.btnTieTuFont.Tag = "贴图文字字体";
            this.btnTieTuFont.Text = "字体";
            this.btnTieTuFont.UseVisualStyleBackColor = false;
            this.btnTieTuFont.Click += new System.EventHandler(this.btnFont_Click);
            // 
            // btnTieTuFontColor
            // 
            this.btnTieTuFontColor.IsColorButton = true;
            this.btnTieTuFontColor.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnTieTuFontColor.ImageColor = System.Drawing.Color.Black;
            this.btnTieTuFontColor.Location = new System.Drawing.Point(286, 119);
            this.btnTieTuFontColor.Size = new System.Drawing.Size(85, 26);
            this.btnTieTuFontColor.TabIndex = 41;
            this.btnTieTuFontColor.Tag = "贴图文字颜色";
            this.btnTieTuFontColor.Text = "文本色";
            this.btnTieTuFontColor.Name = "btnTieTuFontColor";
            this.btnTieTuFontColor.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnTieTuFontColor.UseVisualStyleBackColor = false;
            this.btnTieTuFontColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // chkUseContentColor
            // 
            this.chkUseContentColor.AutoSize = true;
            this.chkUseContentColor.Checked = true;
            this.chkUseContentColor.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkUseContentColor.Location = new System.Drawing.Point(15, 69);
            this.chkUseContentColor.Size = new System.Drawing.Size(147, 21);
            this.chkUseContentColor.TabIndex = 27;
            this.chkUseContentColor.Text = "使用界面设置中的字体";
            this.chkUseContentColor.UseVisualStyleBackColor = true;
            this.chkUseContentColor.CheckedChanged += new System.EventHandler(this.chkUseContentColor_CheckedChanged);
            // 
            // chkIngoreHtml
            // 
            this.chkIngoreHtml.AutoSize = true;
            this.chkIngoreHtml.Location = new System.Drawing.Point(16, 20);
            this.chkIngoreHtml.Size = new System.Drawing.Size(210, 21);
            this.chkIngoreHtml.TabIndex = 27;
            this.chkIngoreHtml.Text = "忽略Html格式以文字方式生成图片";
            this.chkIngoreHtml.UseVisualStyleBackColor = true;
            // 
            // lblPinMaxWidth
            // 
            this.lblPinMaxWidth.AutoSize = true;
            this.lblPinMaxWidth.Location = new System.Drawing.Point(187, 45);
            this.lblPinMaxWidth.Size = new System.Drawing.Size(59, 17);
            this.lblPinMaxWidth.TabIndex = 28;
            this.lblPinMaxWidth.Text = "最大宽度:";
            // 
            // lblPinMaxWidthUnit
            // 
            this.lblPinMaxWidthUnit.AutoSize = true;
            this.lblPinMaxWidthUnit.Location = new System.Drawing.Point(309, 45);
            this.lblPinMaxWidthUnit.Size = new System.Drawing.Size(32, 17);
            this.lblPinMaxWidthUnit.TabIndex = 29;
            this.lblPinMaxWidthUnit.Text = "像素";
            // 
            // lblPinBorderMargin
            // 
            this.lblPinBorderMargin.AutoSize = true;
            this.lblPinBorderMargin.Location = new System.Drawing.Point(14, 45);
            this.lblPinBorderMargin.Size = new System.Drawing.Size(47, 17);
            this.lblPinBorderMargin.TabIndex = 28;
            this.lblPinBorderMargin.Text = "页边距:";
            // 
            // lblPinBorderMarginUnit
            // 
            this.lblPinBorderMarginUnit.AutoSize = true;
            this.lblPinBorderMarginUnit.Location = new System.Drawing.Point(129, 45);
            this.lblPinBorderMarginUnit.Size = new System.Drawing.Size(32, 17);
            this.lblPinBorderMarginUnit.TabIndex = 29;
            this.lblPinBorderMarginUnit.Text = "像素";
            // 
            // lblTieTuLabel
            // 
            this.lblTieTuLabel.BackColor = Color.White;
            this.lblTieTuLabel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.lblTieTuLabel.Location = new System.Drawing.Point(16, 92);
            this.lblTieTuLabel.Size = new System.Drawing.Size(266, 111);
            this.lblTieTuLabel.TabIndex = 19;
            this.lblTieTuLabel.Text = "预览\r\nAaOo012345";
            this.lblTieTuLabel.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tbTools
            // 
            this.tbTools.Controls.Add(this.grpColorPicker);
            this.tbTools.Controls.Add(this.grpMagnifier);
            this.tbTools.Controls.Add(this.grpWhiteBoard);
            this.tbTools.Controls.Add(this.grpRuler);
            this.tbTools.Location = new System.Drawing.Point(4, 22);
            this.tbTools.Padding = new System.Windows.Forms.Padding(3);
            this.tbTools.Size = new System.Drawing.Size(387, 368);
            this.tbTools.TabIndex = 7;
            this.tbTools.Text = "工具";
            this.tbTools.UseVisualStyleBackColor = true;
            // 
            // grpColorPicker
            // 
            this.grpColorPicker.Controls.Add(this.cmbHex);
            this.grpColorPicker.Controls.Add(this.lblColorPickerValue);
            this.grpColorPicker.Controls.Add(this.chkHexUpper);
            this.grpColorPicker.Location = new System.Drawing.Point(3, 94);
            this.grpColorPicker.Size = new System.Drawing.Size(374, 50);
            this.grpColorPicker.TabIndex = 31;
            this.grpColorPicker.TabStop = false;
            this.grpColorPicker.Text = "取色器";
            // 
            // lblCaptureDelay
            // 
            this.lblMagnifierCount.AutoSize = true;
            this.lblMagnifierCount.Location = new System.Drawing.Point(13, 20);
            this.lblMagnifierCount.Size = new System.Drawing.Size(35, 17);
            this.lblMagnifierCount.TabIndex = 2;
            this.lblMagnifierCount.Text = "放大倍数";
            // 
            // numericUpDown1
            // 
            this.numMagnifierCount.Location = new System.Drawing.Point(76, 16);
            this.numMagnifierCount.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numMagnifierCount.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numMagnifierCount.Size = new System.Drawing.Size(46, 23);
            this.numMagnifierCount.Tag = "默认放大倍数";
            this.numMagnifierCount.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            //
            // grpMagnifier
            // 
            this.grpMagnifier.Location = new System.Drawing.Point(3, 152);
            this.grpMagnifier.Controls.Add(this.lblMagnifierCount);
            this.grpMagnifier.Controls.Add(this.numMagnifierCount);
            this.numMagnifierCount.BringToFront();
            this.grpMagnifier.Size = new System.Drawing.Size(374, 50);
            this.grpMagnifier.TabStop = false;
            this.grpMagnifier.Text = "放大镜";
            // 
            // chkWhiteBoardUseColor
            // 
            this.chkWhiteBoardUseColor.AutoSize = true;
            this.chkWhiteBoardUseColor.Location = new System.Drawing.Point(13, 20);
            this.chkWhiteBoardUseColor.Size = new System.Drawing.Size(119, 21);
            this.chkWhiteBoardUseColor.Text = "使用纯色背景";
            this.chkWhiteBoardUseColor.UseVisualStyleBackColor = true;
            this.chkWhiteBoardUseColor.TipControl = tipMsg;
            this.chkWhiteBoardUseColor.TipText = "功能：打开白板时，是否使用纯色背景，默认白色。\r\n说明：\r\n      勾选后，白板背景为纯色；\r\n      不勾选，使用桌面背景图。";
            // 
            // lblWhiteBoardColor
            // 
            this.lblWhiteBoardColor.AutoSize = true;
            this.lblWhiteBoardColor.Location = new System.Drawing.Point(182, 20);
            this.lblWhiteBoardColor.Size = new System.Drawing.Size(71, 17);
            this.lblWhiteBoardColor.Text = "背景色";
            // 
            // btnWhiteBoardColor
            // 
            this.btnWhiteBoardColor.IsColorButton = true;
            this.btnWhiteBoardColor.BackColor = System.Drawing.Color.White;
            this.btnWhiteBoardColor.ImageColor = System.Drawing.Color.Black;
            this.btnWhiteBoardColor.Location = new System.Drawing.Point(235, 16);
            this.btnWhiteBoardColor.Size = new System.Drawing.Size(26, 26);
            this.btnWhiteBoardColor.Tag = "默认白板背景色";
            this.btnWhiteBoardColor.UseVisualStyleBackColor = false;
            this.btnWhiteBoardColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // grpWhiteBoard
            // 
            this.grpWhiteBoard.Location = new System.Drawing.Point(3, 208);
            this.grpWhiteBoard.Controls.Add(this.chkWhiteBoardUseColor);
            this.grpWhiteBoard.Controls.Add(this.btnWhiteBoardColor);
            this.grpWhiteBoard.Controls.Add(this.lblWhiteBoardColor);
            this.grpWhiteBoard.Size = new System.Drawing.Size(374, 50);
            this.grpWhiteBoard.TabStop = false;
            this.grpWhiteBoard.Text = "白板";
            // 
            // cmbHex
            // 
            this.cmbHex.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbHex.FormattingEnabled = true;
            this.cmbHex.Items.AddRange(new object[] {
            "RGB",
            "HEX"});
            this.cmbHex.Location = new System.Drawing.Point(76, 16);
            this.cmbHex.Name = "cmbHex";
            this.cmbHex.Size = new System.Drawing.Size(82, 25);
            this.cmbHex.Tag = "取色器文字样式";
            // 
            // lblColorPickerValue
            // 
            this.lblColorPickerValue.AutoSize = true;
            this.lblColorPickerValue.Location = new System.Drawing.Point(13, 20);
            this.lblColorPickerValue.Size = new System.Drawing.Size(59, 17);
            this.lblColorPickerValue.Text = "取色器值:";
            // 
            // chkHexUpper
            // 
            this.chkHexUpper.AutoSize = true;
            this.chkHexUpper.Location = new System.Drawing.Point(181, 18);
            this.chkHexUpper.Size = new System.Drawing.Size(111, 21);
            this.chkHexUpper.Text = "HEX颜色值大写";
            this.chkHexUpper.UseVisualStyleBackColor = true;
            // 
            // grpRuler
            // 
            this.grpRuler.Controls.Add(this.chkRuleTopMost);
            this.grpRuler.Controls.Add(this.cmbRulerOpacity);
            this.grpRuler.Controls.Add(this.lblRulerOpaque);
            this.grpRuler.Controls.Add(this.cmbRulerUnit);
            this.grpRuler.Controls.Add(this.lblDefaultRulerUnit);
            this.grpRuler.Location = new System.Drawing.Point(3, 6);
            this.grpRuler.Size = new System.Drawing.Size(374, 80);
            this.grpRuler.TabIndex = 27;
            this.grpRuler.TabStop = false;
            this.grpRuler.Text = "标尺";
            // 
            // chkRuleTopMost
            // 
            this.chkRuleTopMost.AutoSize = true;
            this.chkRuleTopMost.Checked = true;
            this.chkRuleTopMost.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkRuleTopMost.Location = new System.Drawing.Point(13, 18);
            this.chkRuleTopMost.Size = new System.Drawing.Size(75, 21);
            this.chkRuleTopMost.TabIndex = 30;
            this.chkRuleTopMost.Text = "标尺置顶";
            this.chkRuleTopMost.UseVisualStyleBackColor = true;
            // 
            // lblRulerOpaque
            // 
            this.lblRulerOpaque.AutoSize = true;
            this.lblRulerOpaque.Location = new System.Drawing.Point(17, 46);
            this.lblRulerOpaque.Size = new System.Drawing.Size(55, 17);
            this.lblRulerOpaque.TabIndex = 27;
            this.lblRulerOpaque.Text = "透明度";
            // 
            // cmbRulerOpacity
            // 
            this.cmbRulerOpacity.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbRulerOpacity.FormattingEnabled = true;
            this.cmbRulerOpacity.Location = new System.Drawing.Point(76, 43);
            this.cmbRulerOpacity.Size = new System.Drawing.Size(80, 25);
            this.cmbRulerOpacity.TabIndex = 28;
            this.cmbRulerOpacity.Tag = "标尺透明度";
            // 
            // lblDefaultRulerUnit
            // 
            this.lblDefaultRulerUnit.AutoSize = true;
            this.lblDefaultRulerUnit.Location = new System.Drawing.Point(182, 46);
            this.lblDefaultRulerUnit.Size = new System.Drawing.Size(59, 17);
            this.lblDefaultRulerUnit.TabIndex = 27;
            this.lblDefaultRulerUnit.Text = "计量单位";
            // 
            // cmbRulerUnit
            // 
            this.cmbRulerUnit.TextAlign = ContentAlignment.MiddleLeft;
            this.cmbRulerUnit.Location = new System.Drawing.Point(240, 43);
            this.cmbRulerUnit.Size = new System.Drawing.Size(80, 25);
            this.cmbRulerUnit.TabIndex = 28;
            this.cmbRulerUnit.Tag = "标尺计量单位";
            // 
            // tbShortKey
            // 
            this.tbShortKey.Controls.Add(this.tabHotKeys);
            this.tbShortKey.Location = new System.Drawing.Point(4, 22);
            this.tbShortKey.Padding = new System.Windows.Forms.Padding(3);
            this.tbShortKey.Size = new System.Drawing.Size(387, 368);
            this.tbShortKey.TabIndex = 2;
            this.tbShortKey.Text = "快捷键";
            this.tbShortKey.UseVisualStyleBackColor = true;
            // 
            // tabHotKeys
            // 
            this.tabHotKeys.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabHotKeys.Location = new System.Drawing.Point(3, 3);
            this.tabHotKeys.Name = "tabHotKeys";
            this.tabHotKeys.BackColor = Color.White;
            this.tabHotKeys.SelectedIndex = 0;
            this.tabHotKeys.Size = new System.Drawing.Size(381, 362);
            this.tabHotKeys.TabIndex = 0;
            // 
            // tbAbout
            // 
            this.tbAbout.Controls.Add(this.picIcon);
            this.tbAbout.Controls.Add(this.groupBox9);
            this.tbAbout.Controls.Add(this.gpAutoUpdate);
            this.tbAbout.Controls.Add(this.gpProxy);
            this.tbAbout.Controls.Add(this.lblWebSite);
            this.tbAbout.Controls.Add(this.lblCopyright);
            this.tbAbout.Controls.Add(this.btnUpgrade);
            this.tbAbout.Controls.Add(this.lblVersion);
            this.tbAbout.Controls.Add(this.lnkWebSite);
            this.tbAbout.Controls.Add(this.lblName);
            this.tbAbout.Location = new System.Drawing.Point(4, 26);
            this.tbAbout.Padding = new System.Windows.Forms.Padding(3);
            this.tbAbout.Size = new System.Drawing.Size(387, 400);
            this.tbAbout.TabIndex = 5;
            this.tbAbout.Text = "关于";
            this.tbAbout.UseVisualStyleBackColor = true;
            // 
            // picIcon
            // 
            this.picIcon.BackColor = Color.White;
            this.picIcon.Location = new System.Drawing.Point(20, 20);
            this.picIcon.Name = "picIcon";
            this.picIcon.Size = new System.Drawing.Size(45, 45);
            this.picIcon.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.picIcon.TabIndex = 26;
            this.picIcon.TabStop = false;
            // 
            // groupBox9
            // 
            this.groupBox9.Controls.Add(this.pictureBox4);
            this.groupBox9.Controls.Add(this.pictureBox7);
            this.groupBox9.Controls.Add(this.pictureBox6);
            this.groupBox9.Controls.Add(this.pictureBox5);
            this.groupBox9.Controls.Add(this.linkLabel2);
            this.groupBox9.Controls.Add(this.lnkSuggestions);
            this.groupBox9.Controls.Add(this.lnkFeedback);
            this.groupBox9.Controls.Add(this.linkLabel1);
            this.groupBox9.Location = new System.Drawing.Point(3, 148);
            this.groupBox9.Size = new System.Drawing.Size(374, 85);
            this.groupBox9.TabIndex = 34;
            this.groupBox9.TabStop = false;
            this.groupBox9.Text = "联系我们";
            // 
            // pictureBox4
            // 
            this.pictureBox4.BackColor = Color.White;
            this.pictureBox4.Image = global::OCRTools.Properties.Resources.qqQun;
            this.pictureBox4.Location = new System.Drawing.Point(21, 49);
            this.pictureBox4.Name = "pictureBox4";
            this.pictureBox4.Size = new System.Drawing.Size(20, 20);
            this.pictureBox4.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox4.TabIndex = 43;
            this.pictureBox4.TabStop = false;
            // 
            // pictureBox7
            // 
            this.pictureBox7.BackColor = Color.White;
            this.pictureBox7.Image = global::OCRTools.Properties.Resources.fankui;
            this.pictureBox7.Location = new System.Drawing.Point(243, 53);
            this.pictureBox7.Name = "pictureBox7";
            this.pictureBox7.Size = new System.Drawing.Size(16, 16);
            this.pictureBox7.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox7.TabIndex = 44;
            this.pictureBox7.TabStop = false;
            // 
            // pictureBox6
            // 
            this.pictureBox6.BackColor = Color.White;
            this.pictureBox6.Image = global::OCRTools.Properties.Resources.traffic_cone;
            this.pictureBox6.Location = new System.Drawing.Point(243, 26);
            this.pictureBox6.Name = "pictureBox6";
            this.pictureBox6.Size = new System.Drawing.Size(16, 16);
            this.pictureBox6.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox6.TabIndex = 44;
            this.pictureBox6.TabStop = false;
            // 
            // pictureBox5
            // 
            this.pictureBox5.BackColor = Color.White;
            this.pictureBox5.Image = global::OCRTools.Properties.Resources.qqKeFu;
            this.pictureBox5.Location = new System.Drawing.Point(21, 21);
            this.pictureBox5.Name = "pictureBox5";
            this.pictureBox5.Size = new System.Drawing.Size(20, 20);
            this.pictureBox5.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox5.TabIndex = 44;
            this.pictureBox5.TabStop = false;
            // 
            // linkLabel2
            // 
            this.linkLabel2.AutoSize = true;
            this.linkLabel2.Font = CommonString.GetSysBoldFont(12F);
            this.linkLabel2.Location = new System.Drawing.Point(42, 55);
            this.linkLabel2.Name = "linkLabel2";
            this.linkLabel2.Size = new System.Drawing.Size(135, 12);
            this.linkLabel2.TabIndex = 0;
            this.linkLabel2.TabStop = true;
            this.linkLabel2.Text = "QQ交流群(100029010)";
            this.linkLabel2.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel2_LinkClicked);
            // 
            // lnkSuggestions
            // 
            this.lnkSuggestions.AutoSize = true;
            this.lnkSuggestions.Font = CommonString.GetSysBoldFont(12F);
            this.lnkSuggestions.Location = new System.Drawing.Point(264, 55);
            this.lnkSuggestions.Size = new System.Drawing.Size(57, 12);
            this.lnkSuggestions.TabIndex = 0;
            this.lnkSuggestions.TabStop = true;
            this.lnkSuggestions.Text = "意见建议";
            this.lnkSuggestions.Click += new System.EventHandler(this.btnSupport_Click);
            // 
            // lnkFeedback
            // 
            this.lnkFeedback.AutoSize = true;
            this.lnkFeedback.Font = CommonString.GetSysBoldFont(12F);
            this.lnkFeedback.Location = new System.Drawing.Point(264, 28);
            this.lnkFeedback.Size = new System.Drawing.Size(57, 12);
            this.lnkFeedback.TabIndex = 0;
            this.lnkFeedback.TabStop = true;
            this.lnkFeedback.Text = "问题反馈";
            this.lnkFeedback.Click += new System.EventHandler(this.btnSupport_Click);
            // 
            // linkLabel1
            // 
            this.linkLabel1.AutoSize = true;
            this.linkLabel1.Font = CommonString.GetSysBoldFont(12F);
            this.linkLabel1.Location = new System.Drawing.Point(42, 28);
            this.linkLabel1.Name = "linkLabel1";
            this.linkLabel1.Size = new System.Drawing.Size(155, 12);
            this.linkLabel1.TabIndex = 0;
            this.linkLabel1.TabStop = true;
            this.linkLabel1.Text = "在线客服(QQ:365833440)";
            this.linkLabel1.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel1_LinkClicked);
            // 
            // gpAutoUpdate
            // 
            this.gpAutoUpdate.Controls.Add(this.numUpdateHour);
            this.gpAutoUpdate.Controls.Add(this.btnCheckUpdate);
            this.gpAutoUpdate.Controls.Add(this.lblUpdateInteval);
            this.gpAutoUpdate.Controls.Add(this.lblUpdateTimeUnit);
            this.gpAutoUpdate.Controls.Add(this.chkExpireBeta);
            this.gpAutoUpdate.Controls.Add(this.chkAutoUpdate);
            this.gpAutoUpdate.Location = new System.Drawing.Point(3, 242);
            this.gpAutoUpdate.Size = new System.Drawing.Size(374, 85);
            this.gpAutoUpdate.TabIndex = 29;
            this.gpAutoUpdate.TabStop = false;
            this.gpAutoUpdate.Text = "自动更新";
            // 
            // gpProxy
            // 
            this.gpProxy.Controls.Add(this.rdoSysProxy);
            this.gpProxy.Controls.Add(this.rdoNoProxy);
            this.gpProxy.Location = new System.Drawing.Point(3, 338);
            this.gpProxy.Size = new System.Drawing.Size(374, 55);
            this.gpProxy.TabIndex = 29;
            this.gpProxy.TabStop = false;
            this.gpProxy.Text = "代理";
            // 
            // rdoSysProxy
            // 
            this.rdoSysProxy.AutoSize = true;
            this.rdoSysProxy.Location = new System.Drawing.Point(12, 20);
            this.rdoSysProxy.Size = new System.Drawing.Size(113, 21);
            this.rdoSysProxy.TabIndex = 0;
            this.rdoSysProxy.Text = "使用系统代理";
            this.rdoSysProxy.Tag = "使用系统代理";
            this.rdoSysProxy.UseVisualStyleBackColor = true;
            this.rdoSysProxy.TipControl = tipMsg;
            this.rdoSysProxy.TipText = "功能：使用当前系统设置的代理。";
            // 
            // rdoNoProxy
            // 
            this.rdoNoProxy.AutoSize = true;
            this.rdoNoProxy.Location = new System.Drawing.Point(168, 20);
            this.rdoNoProxy.Size = new System.Drawing.Size(113, 21);
            this.rdoNoProxy.TabIndex = 0;
            this.rdoNoProxy.Text = "不使用代理";
            this.rdoNoProxy.Tag = "不使用代理";
            this.rdoNoProxy.UseVisualStyleBackColor = true;
            this.rdoNoProxy.TipControl = tipMsg;
            this.rdoNoProxy.TipText = "功能：强制不使用代理，直接访问网络。";
            // 
            // numUpdateHour
            // 
            this.numUpdateHour.Location = new System.Drawing.Point(54, 48);
            this.numUpdateHour.Maximum = new decimal(new int[] {
            24,
            0,
            0,
            0});
            this.numUpdateHour.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numUpdateHour.Name = "numUpdateHour";
            this.numUpdateHour.Size = new System.Drawing.Size(52, 23);
            this.numUpdateHour.TabIndex = 33;
            this.numUpdateHour.Tag = "自动更新间隔";
            this.numUpdateHour.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // btnCheckUpdate
            // 
            this.btnCheckUpdate.Font = CommonString.GetSysNormalFont(15F);
            this.btnCheckUpdate.Image = global::OCRTools.Properties.Resources.LoadingSmallBlack;
            this.btnCheckUpdate.ImageSize = btnCheckUpdate.Image.Size;
            this.btnCheckUpdate.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnCheckUpdate.Location = new System.Drawing.Point(168, 43);
            this.btnCheckUpdate.Size = new System.Drawing.Size(115, 32);
            this.btnCheckUpdate.TabIndex = 40;
            this.btnCheckUpdate.TabStop = false;
            this.btnCheckUpdate.Text = "检查更新";
            this.btnCheckUpdate.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnCheckUpdate.UseVisualStyleBackColor = false;
            this.btnCheckUpdate.Click += new System.EventHandler(this.btnCheckUpdate_Click);
            this.btnCheckUpdate.AutoSize = true;
            // 
            // lblUpdateInteval
            // 
            this.lblUpdateInteval.AutoSize = true;
            this.lblUpdateInteval.Location = new System.Drawing.Point(12, 52);
            this.lblUpdateInteval.Size = new System.Drawing.Size(35, 17);
            this.lblUpdateInteval.TabIndex = 31;
            this.lblUpdateInteval.Text = "间隔:";
            // 
            // lblUpdateTimeUnit
            // 
            this.lblUpdateTimeUnit.AutoSize = true;
            this.lblUpdateTimeUnit.Location = new System.Drawing.Point(109, 52);
            this.lblUpdateTimeUnit.Size = new System.Drawing.Size(32, 17);
            this.lblUpdateTimeUnit.TabIndex = 32;
            this.lblUpdateTimeUnit.Text = "小时";
            // 
            // lblExpireBeta
            // 
            this.chkExpireBeta.AutoSize = true;
            this.chkExpireBeta.Location = new System.Drawing.Point(168, 20);
            this.chkExpireBeta.Size = new System.Drawing.Size(113, 21);
            this.chkExpireBeta.TabIndex = 0;
            this.chkExpireBeta.Text = "体验Beta测试版";
            this.chkExpireBeta.UseVisualStyleBackColor = true;
            this.chkExpireBeta.TipControl = tipMsg;
            this.chkExpireBeta.TipText = "功能：是否愿意体验测试版本。\r\n说明：\r\n默认不勾选，勾选后，有测试版本，也会提示更新！";
            // 
            // chkAutoUpdate
            // 
            this.chkAutoUpdate.AutoSize = true;
            this.chkAutoUpdate.Checked = true;
            this.chkAutoUpdate.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoUpdate.Location = new System.Drawing.Point(12, 20);
            this.chkAutoUpdate.Size = new System.Drawing.Size(111, 21);
            this.chkAutoUpdate.TabIndex = 0;
            this.chkAutoUpdate.Text = "启动时检查更新";
            this.chkAutoUpdate.UseVisualStyleBackColor = true;
            // 
            // lblWebSite
            // 
            this.lblWebSite.AutoSize = true;
            this.lblWebSite.Font = CommonString.GetSysNormalFont(13F);
            this.lblWebSite.Location = new System.Drawing.Point(18, 120);
            this.lblWebSite.Size = new System.Drawing.Size(33, 13);
            this.lblWebSite.TabIndex = 28;
            this.lblWebSite.Text = "官网";
            // 
            // lblCopyright
            // 
            this.lblCopyright.AutoSize = true;
            this.lblCopyright.Font = CommonString.GetSysNormalFont(13F);
            this.lblCopyright.Location = new System.Drawing.Point(18, 98);
            this.lblCopyright.Size = new System.Drawing.Size(33, 13);
            this.lblCopyright.TabIndex = 28;
            this.lblCopyright.Text = "版权";
            // 
            // btnUpgrade
            // 
            this.btnUpgrade.Font = CommonString.GetSysBoldFont(15F);
            this.btnUpgrade.Location = new System.Drawing.Point(230, 21);
            this.btnUpgrade.Size = new System.Drawing.Size(153, 45);
            this.btnUpgrade.TabIndex = 7;
            this.btnUpgrade.Text = "升级为…";
            this.btnUpgrade.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnUpgrade.UseVisualStyleBackColor = true;
            this.btnUpgrade.Click += new System.EventHandler(this.btnUpgrade_Click);
            // 
            // lblVersion
            // 
            this.lblVersion.AutoSize = true;
            this.lblVersion.Font = CommonString.GetSysNormalFont(13F);
            this.lblVersion.Location = new System.Drawing.Point(18, 76);
            this.lblVersion.Size = new System.Drawing.Size(33, 13);
            this.lblVersion.TabIndex = 28;
            // 
            // lnkWebSite
            // 
            this.lnkWebSite.AutoSize = true;
            this.lnkWebSite.Font = CommonString.GetSysNormalFont(15F);
            this.lnkWebSite.Location = new System.Drawing.Point(54, 119);
            this.lnkWebSite.Name = "lnkWebSite";
            this.lnkWebSite.Size = new System.Drawing.Size(66, 19);
            this.lnkWebSite.TabIndex = 0;
            this.lnkWebSite.TabStop = true;
            this.lnkWebSite.Text = "https://";
            this.lnkWebSite.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lnkWebSite_LinkClicked);
            // 
            // lblName
            // 
            this.lblName.AutoSize = true;
            this.lblName.Font = CommonString.GetSysBoldFont(18F);
            this.lblName.Location = new System.Drawing.Point(65, 30);
            this.lblName.Name = "lblName";
            this.lblName.Size = new System.Drawing.Size(22, 27);
            this.lblName.TabIndex = 27;
            this.lblName.Text = "*";
            // 
            // FormSetting
            // 
            this.ClientSize = new System.Drawing.Size(415, 464);
            this.Controls.Add(this.tbConfig);
            this.Padding = new System.Windows.Forms.Padding(10, 60, 10, 10);
            this.Text = "系统设置";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FormSetting_FormClosing);
            this.Load += new System.EventHandler(this.FormSetting_Load);
            this.tbConfig.ResumeLayout(false);
            this.tbNormal.ResumeLayout(false);
            this.grpNormal.ResumeLayout(false);
            this.grpNormal.PerformLayout();
            this.grpJieMianFont.ResumeLayout(false);
            this.tabUI.ResumeLayout(false);
            this.gpDayNightMode.ResumeLayout(false);
            this.gpDayNightMode.PerformLayout();
            this.gpUINormal.ResumeLayout(false);
            this.gpUINormal.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.picLoadingImage)).EndInit();
            this.grpToolSet.ResumeLayout(false);
            this.grpToolSet.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numToolShadowWidth)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.picToolBar)).EndInit();
            this.tbOcr.ResumeLayout(false);
            this.tabControl1.ResumeLayout(false);
            this.tbOcrSetting.ResumeLayout(false);
            this.gpOcrProcing.ResumeLayout(false);
            this.gpOcrProcing.PerformLayout();
            this.gpOcrResult.ResumeLayout(false);
            this.gpOcrResult.PerformLayout();
            this.gpCopyResult.ResumeLayout(false);
            this.gpCopyResult.PerformLayout();
            this.gpTypography.ResumeLayout(false);
            this.gpTypography.PerformLayout();
            this.tbLocalOcr.ResumeLayout(false);
            this.grpOperate.ResumeLayout(false);
            this.grpOperate.PerformLayout();
            this.grpLocalOcrSetting.ResumeLayout(false);
            this.grpLocalOcrSetting.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLocalOcrThread)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numLocalOcrPort)).EndInit();
            this.tbCapture.ResumeLayout(false);
            this.tabControl2.ResumeLayout(false);
            this.tbCaptureSetting.ResumeLayout(false);
            this.grpCaptureNormalSetting.ResumeLayout(false);
            this.grpCaptureNormalSetting.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDown1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCaptureBorderWidth)).EndInit();
            this.grpAfterCapture.ResumeLayout(false);
            this.grpAfterCapture.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxHistoryCount)).EndInit();
            this.tbScrollCatpure.ResumeLayout(false);
            this.tbScrollCatpure.PerformLayout();
            this.gbWhileCapturing.ResumeLayout(false);
            this.gbWhileCapturing.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudScrollDelay)).EndInit();
            this.gbBeforeCapture.ResumeLayout(false);
            this.gbBeforeCapture.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudStartDelay)).EndInit();
            this.tbPin.ResumeLayout(false);
            this.grpPinNormalSetting.ResumeLayout(false);
            this.grpPinNormalSetting.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numShadowWidth)).EndInit();
            this.grpTextToImage.ResumeLayout(false);
            this.grpTextToImage.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numTieTuMaxWidth)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTieTuBorderWidth)).EndInit();
            this.tbTools.ResumeLayout(false);
            this.grpColorPicker.ResumeLayout(false);
            this.grpColorPicker.PerformLayout();
            this.grpWhiteBoard.ResumeLayout(false);
            this.grpWhiteBoard.PerformLayout();
            this.grpMagnifier.ResumeLayout(false);
            this.grpMagnifier.PerformLayout();
            this.grpRuler.ResumeLayout(false);
            this.grpRuler.PerformLayout();
            this.tbShortKey.ResumeLayout(false);
            this.tbAbout.ResumeLayout(false);
            this.tbAbout.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.picIcon)).EndInit();
            this.groupBox9.ResumeLayout(false);
            this.groupBox9.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox5)).EndInit();
            this.gpAutoUpdate.ResumeLayout(false);
            this.gpAutoUpdate.PerformLayout();
            this.gpProxy.ResumeLayout(false);
            this.gpProxy.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numUpdateHour)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tbConfig;
        private System.Windows.Forms.TabPage tbNormal;
        private System.Windows.Forms.TabPage tbCapture;
        private CheckBoxWithTip chkAutoStart;
        private System.Windows.Forms.TabPage tabUI;
        private System.Windows.Forms.TabPage tbShortKey;
        private System.Windows.Forms.CheckBox chkCaptureAnimal;
        private CheckBoxWithTip chkStartMainWindow;
        private MenuButton cmbStyles;
        private System.Windows.Forms.Label lblThemeStyle;
        private MenuButton cmbLoadingType;
        private System.Windows.Forms.Label lblLoadingGif;
        private System.Windows.Forms.PictureBox picLoadingImage;
        private System.Windows.Forms.NumericUpDown numCaptureBorderWidth;
        private System.Windows.Forms.CheckBox chkHexUpper;
        private System.Windows.Forms.Label lblCaptureBorderWidth;
        private CheckBoxWithTip chkAutoControl;
        private System.Windows.Forms.CheckBox chkAutoWindow;
        private System.Windows.Forms.CheckBox chkCrocessLine;
        private System.Windows.Forms.Label lblColorPickerValue;
        private System.Windows.Forms.Label lblCaptureBorderWidthUnit;
        private System.Windows.Forms.CheckBox chkZoom;
        private System.Windows.Forms.ComboBox cmbHex;
        private System.Windows.Forms.NumericUpDown numMaxHistoryCount;
        private System.Windows.Forms.Label lblMaxHistoryCount;
        private System.Windows.Forms.GroupBox grpAfterCapture;
        private CheckBoxWithTip chkCaptureTip;
        private System.Windows.Forms.CheckBox chkSaveToClipborad;
        private SkinButton btnChangeCaptureLocation;
        private SkinButton btnOpenCaptureLocation;
        private System.Windows.Forms.TextBox txtCaptureLocation;
        private System.Windows.Forms.Label lblFilePath;
        private System.Windows.Forms.TextBox txtCaptureFileName;
        private System.Windows.Forms.Label lblFileName;
        private System.Windows.Forms.CheckBox chkAutoSaveCapture;
        private System.Windows.Forms.TabPage tbPin;
        private System.Windows.Forms.TabPage tbAbout;
        private System.Windows.Forms.GroupBox grpJieMianFont;
        private System.Windows.Forms.GroupBox grpTextToImage;
        private System.Windows.Forms.NumericUpDown numTieTuBorderWidth;
        private System.Windows.Forms.Label lblPinBorderMargin;
        private System.Windows.Forms.Label lblPinBorderMarginUnit;
        private System.Windows.Forms.Label lblTieTuLabel;
        private System.Windows.Forms.CheckBox chkTieTuFocus;
        private System.Windows.Forms.CheckBox chkShowTieTuShadow;
        private System.Windows.Forms.CheckBox chkTieTuShark;
        private System.Windows.Forms.CheckBox chkTieTuUseCaptureLocation;
        private SkinButton btnTieTuShadowColor;
        private System.Windows.Forms.Label lblNewWindowAction;
        private System.Windows.Forms.Label lblPinShadowColor;
        private System.Windows.Forms.CheckBox chkIngoreHtml;
        private System.Windows.Forms.NumericUpDown numTieTuMaxWidth;
        private System.Windows.Forms.Label lblPinMaxWidth;
        private System.Windows.Forms.Label lblPinMaxWidthUnit;
        private System.Windows.Forms.Label lblContentLable;
        private System.Windows.Forms.NumericUpDown numShadowWidth;
        private System.Windows.Forms.Label lblPinShadowBorderWidth;
        private System.Windows.Forms.Label lblPinShadowBorderUnit;
        private System.Windows.Forms.GroupBox gpAutoUpdate;
        private System.Windows.Forms.GroupBox gpProxy;
        private System.Windows.Forms.CheckBox chkAutoUpdate;
        private System.Windows.Forms.PictureBox picIcon;
        private System.Windows.Forms.PictureBox picScrollSpeed;
        private System.Windows.Forms.Label lblCopyright;
        private System.Windows.Forms.Label lblVersion;
        private System.Windows.Forms.Label lblName;
        private System.Windows.Forms.NumericUpDown numUpdateHour;
        private System.Windows.Forms.Label lblUpdateInteval;
        private System.Windows.Forms.Label lblUpdateTimeUnit;
        private SkinButton btnUpgrade;
        private System.Windows.Forms.CheckBox chkUseContentColor;
        private System.Windows.Forms.Label lblFileNameParam;
        private System.Windows.Forms.Label lblCaptureFileName;
        private SkinButton btnContentBackColor;
        private SkinButton btnContentFontColor;
        private SkinButton btnContentFont;
        private SkinButton btnContentDefault;
        private SkinButton btnTieTuDefaultFont;
        private SkinButton btnTieTuBackColor;
        private SkinButton btnTieTuFont;
        private SkinButton btnTieTuFontColor;
        private System.Windows.Forms.TabControl tabHotKeys;
        private System.Windows.Forms.ToolTip tipMsg;
        private MenuButton cmbDoubleClick;
        private System.Windows.Forms.CheckBox chkShowTool;
        private MenuButton cmbSearch;
        private System.Windows.Forms.Label lblSearch;
        private System.Windows.Forms.CheckBox chkFormTopMost;
        private System.Windows.Forms.TabPage tbTools;
        private System.Windows.Forms.GroupBox grpToolSet;
        private System.Windows.Forms.PictureBox picToolBar;
        private SkinButton btnDefaultToolBarPicture;
        private SkinButton btnToolBarPicture;
        private System.Windows.Forms.CheckBox chkToolBarCircle;
        private System.Windows.Forms.ComboBox cmbToolBarSize;
        private System.Windows.Forms.TextBox txtToolBarPicLocation;
        private SkinButton btnQQ;
        private System.Windows.Forms.GroupBox grpRuler;
        private System.Windows.Forms.CheckBox chkRuleTopMost;
        private MenuButton cmbRulerUnit;
        private System.Windows.Forms.Label lblDefaultRulerUnit;
        private System.Windows.Forms.ComboBox cmbRulerOpacity;
        private System.Windows.Forms.Label lblRulerOpaque;
        private System.Windows.Forms.TabPage tbOcr;
        private MenuButton cmbFenDuan;
        private System.Windows.Forms.Label lblSegment;
        private System.Windows.Forms.GroupBox gpTypography;
        private MenuButton cmbVoice;
        private System.Windows.Forms.Label lblOcrStrategy;
        private System.Windows.Forms.Label lblVoice;
        private System.Windows.Forms.GroupBox groupBox9;
        private System.Windows.Forms.PictureBox pictureBox4;
        private System.Windows.Forms.PictureBox pictureBox5;
        private LinkLabel linkLabel2;
        private LinkLabel linkLabel1;
        private System.Windows.Forms.PictureBox pictureBox6;
        private LinkLabel lnkFeedback;
        private System.Windows.Forms.Label lblDiyToolbarImage;
        private System.Windows.Forms.PictureBox pictureBox7;
        private LinkLabel lnkSuggestions;
        private CheckBoxWithTip chkToolShadow;
        private System.Windows.Forms.NumericUpDown numToolShadowWidth;
        private SkinButton btnCheckUpdate;
        private System.Windows.Forms.CheckBox chkWeatherImage;
        private MenuButton cmbWeatherIcon;
        private System.Windows.Forms.GroupBox gpOcrProcing;
        private System.Windows.Forms.GroupBox gpUINormal;
        private System.Windows.Forms.GroupBox grpCaptureNormalSetting;
        private System.Windows.Forms.GroupBox grpColorPicker;
        private System.Windows.Forms.GroupBox grpWhiteBoard;
        private System.Windows.Forms.GroupBox grpMagnifier;
        private LabelWithTip lblDelay_Second;
        private System.Windows.Forms.NumericUpDown numericUpDown1;
        private System.Windows.Forms.Label lblCaptureDelay;
        private System.Windows.Forms.NumericUpDown numMagnifierCount;
        private System.Windows.Forms.Label lblMagnifierCount;
        private System.Windows.Forms.GroupBox grpPinNormalSetting;
        private System.Windows.Forms.GroupBox grpNormal;
        private System.Windows.Forms.GroupBox gpOcrResult;
        private CheckBoxWithTip chkTextIndent;
        private CheckBoxWithTip chkShowTextPreview;
        private System.Windows.Forms.CheckBox checkBox11;
        private System.Windows.Forms.GroupBox gpCopyResult;
        private CheckBoxWithTip chkCopyResult;
        private MenuButton cmbCopyMode;
        private System.Windows.Forms.Label lblCopyMode;
        private CheckBoxWithTip chkCopyOldWhenTrans;
        private CheckBoxWithTip chkTipCopy;
        private System.Windows.Forms.CheckBox chkCircleFangDa;
        private System.Windows.Forms.Label lblWebSite;
        private System.Windows.Forms.LinkLabel lnkWebSite;
        private CheckBoxWithTip chkAutoCompressImg;
        private Label lblDbClickToolBar;
        private Label lblToolBarShadowUnit;
        private Label lblCaptureBorder;
        private Label lblCaptureMouseWheel;
        private SkinButton btnCaptureBorderColor;
        private SkinButton btnWhiteBoardColor;
        private Label lblWhiteBoardColor;
        private CheckBoxWithTip chkAutoFull2Half;
        private CheckBoxWithTip chkRemoveDuplicatePunctuation;
        private CheckBoxWithTip chkAutoBiaoDian;
        private CheckBoxWithTip chkAutoSpace;
        private MenuButton cmbOcrType;
        private CheckBoxWithTip chkLocalOcrEnable;
        private MenuButton cmsNotifyDoubleClick;
        private MenuButton btnLanguage;
        private LinkLabel lnkHelpTrans;
        private Label lblDbClickTray;
        private GroupBox grpLocalOcrSetting;
        private GroupBox grpLocalOcr;
        private LinkLabel lnkInstallLocalOcr;
        private LinkLabel lnkOpenLocalOcr;
        private NumericUpDown numLocalOcrPort;
        private Label lblLocalOcrPort;
        private TabControl tabControl1;
        private TabPage tbOcrSetting;
        private TabPage tbLocalOcr;
        private LinkLabel lnkTestLocalOcr;
        private GroupBox grpOperate;
        private NumericUpDown numLocalOcrThread;
        private Label lblMaxOcrThread;
        private LabelWithTip lblLocalOcrMaxThread;
        private LabelWithTip lblLocalOcrPortHelp;
        private CheckBoxWithTip chkAutoScaleImg;
        private CheckBoxWithTip chkRegisteMenu;
        private CheckBox chkShowNotify;
        private MenuButton cmbImageViewBackStyle;
        private Label lblImageBack;
        private Label lblMouseMove;
        private MenuButton cmbMouseMove;
        private SkinButton btnImageViewBackColor;
        private TextBox textBox2;
        private TextBox textBox1;
        private TabControl tabControl2;
        private TabPage tbCaptureSetting;
        private TabPage tbScrollCatpure;
        private GroupBox gbWhileCapturing;
        private Label lblScrollSpeed;
        private NumericUpDown nudScrollSpeed;
        private MenuButton cbScrollMethod;
        private NumericUpDown nudScrollDelay;
        private Label lblScrollDelay;
        private Label lblScrollMethod;
        private GroupBox gbBeforeCapture;
        private MenuButton cbScrollTopMethodBeforeCapture;
        private NumericUpDown nudStartDelay;
        private Label lblScrollTopMethodBeforeCapture;
        private Label lblStartDelay;
        private Label lblSelectedRectangle;
        private Label lblControlText;
        private CheckBoxWithTip chkNotShowUIWhenOcr;
        private GroupBox gpDayNightMode;
        private CheckBoxWithTip chkAutoColorInversion;
        private MenuButton cmbDark;
        private Label lblDayNithtMode;
        private CheckBoxWithTip chkFollowSysDPI;
        private CheckBoxWithTip chkWhiteBoardUseColor;
        private CheckBoxWithTip chkExpireBeta;
        private CheckBoxWithTip chkClickCopy;
        private MenuButton cbCatpureScroll;
        private RadioWithTip rdoSysProxy;
        private RadioWithTip rdoNoProxy;
    }
}