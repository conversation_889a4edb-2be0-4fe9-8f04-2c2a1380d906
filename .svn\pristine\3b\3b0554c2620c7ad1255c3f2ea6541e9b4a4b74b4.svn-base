﻿using OCRTools.Shadow;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmPasteImage : ShadowForm
    {
        private bool _isShadowVisiable = true;
        private Size _lastSize;

        private Timer _tmrResize;

        public FrmPasteImage()
        {
            InitializeComponent();
            BackgroundImageLayout = ImageLayout.None;
            Padding = CommonString.PaddingZero;
            BackColor = CommonSetting.默认背景颜色;
            KeyPreview = true;
            KeyDown += FrmPasteImage_KeyDown;
            MouseWheel += FrmPasteImage_MouseWheel;
        }

        public ClipboardTextEntity Data { get; set; }

        private void InitTmrResize()
        {
            if (_tmrResize == null)
            {
                _tmrResize = new Timer
                {
                    Interval = 300
                };
                _tmrResize.Tick += TmrResize_Tick;
            }
        }

        private void TmrResize_Tick(object sender, EventArgs e)
        {
            if (Equals(_lastSize, Size))
            {
                _tmrResize.Stop();
                //end Resize
                switch (Data.Type)
                {
                    case ClipboardDataType.Html:
                    case ClipboardDataType.Rtf:
                        if (!Equals(Data.ForceSize, Size))
                        {
                            Data.ForceSize = Size;
                            InitImage();
                        }

                        break;
                }

                BindImage();
            }
            else
            {
                _lastSize = Size;
            }
        }

        private void FrmPasteImage_SizeChanged(object sender, EventArgs e)
        {
            InitTmrResize();
            if (!_tmrResize.Enabled) _tmrResize.Start();
        }

        private void BindImage()
        {
            if (Data.Image != null)
            {
                BackgroundImage = Data.Image;
                Size = new Size(Data.Image.Size.Width, Data.Image.Size.Height);
                Data.ForceSize = Size;
            }

            //Console.WriteLine(string.Format("BindImage ImgSize:{0},forceSize:{1},windowSize:{2}", Data.Image.Size, Data.ForceSize, Size));
            Refresh();
            DrawShadow(ShadowColor);
        }

        public void SetImageData()
        {
            tsmCopyText.Visible = !string.IsNullOrEmpty(Data.OriginalText);
            tsmOCR.Visible = string.IsNullOrEmpty(Data.OriginalText);
            if (Data.Location.IsEmpty || !CommonSetting.截图贴图时使用截屏的位置)
                StartPosition = FormStartPosition.WindowsDefaultLocation;
            else
                Location = Data.Location;
            if (Data.Image == null && !string.IsNullOrEmpty(Data.Content)) InitImage();
            if (Data.Image == null)
            {
                //this.Close();
            }

            BindImage();
        }

        public void BingSizeChangeEvent(bool isAdd)
        {
            SizeChanged -= FrmPasteImage_SizeChanged;
            if (isAdd)
                SizeChanged += FrmPasteImage_SizeChanged;
        }

        private void InitImage()
        {
            switch (Data.Type)
            {
                case ClipboardDataType.Html:
                    Data.Image = CommonToImage.ByHtml_WebBroswer(Data.Content, Data.ForceSize);
                    //img = CommonToImage.ByHtml_HtmlRender(strText, Color.白色);
                    break;
                case ClipboardDataType.Rtf:
                    Data.Image = CommonToImage.ByRtf(Data.Content, Data.ForceSize);
                    break;
            }

            if (Data.Image != null && Data.ForceSize.IsEmpty)
            {
                var crop = CommonToImage.AutoCrop((Bitmap)Data.Image, (int)CommonSetting.贴图页边距宽度);
                if (crop != null)
                {
                    Console.WriteLine("oldImgSize:{0},forceSize:{1},cropSize:{2}", Data.Image.Size, Data.ForceSize,
                        crop.Size);
                    Data.Image = crop;
                }
            }
        }

        private void 原始尺寸ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Data.Image = null;
            Data.ForceSize = Size.Empty;
            SetImageData();
        }

        private void frmPasteImage_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape) cmsClose_Click(sender, null);
        }

        private void 复制图像ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ClipboardService.ClipSetImage(BackgroundImage, false);
            CommonMethod.ShowHelpMsg("贴图已成功复制到剪切板！");
        }

        private void 阴影ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            _isShadowVisiable = !_isShadowVisiable;
            Visibility(_isShadowVisiable);
            阴影ToolStripMenuItem.Checked = _isShadowVisiable;
        }

        private void cmsTop_Click(object sender, EventArgs e)
        {
            TopMost = !TopMost;
            cmsTop.Text = TopMost ? "取消置顶" : "置顶窗体";
        }

        private void cmsClose_Click(object sender, EventArgs e)
        {
            Visibility(false, false);
            Close();
        }

        private void cmsSave_Click(object sender, EventArgs e)
        {
            BackgroundImage.SaveFile(this);
        }

        private void cmsOCR_Click(object sender, EventArgs e)
        {
            BackgroundImage.Ocr();
        }

        private void frmPasteImage_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            cmsClose_Click(sender, null);
        }

        private int smallStep = 10;

        private void FrmPasteImage_KeyDown(object sender, KeyEventArgs e)
        {
            var point = Location;
            var step = smallStep * (e.Modifiers.CompareTo(Keys.Shift) == 0 ? 5 : 1);
            switch (e.KeyCode)
            {
                case Keys.Up:
                    point.Y -= step;
                    break;
                case Keys.Down:
                    point.Y += step;
                    break;
                case Keys.Left:
                    point.X -= step;
                    break;
                case Keys.Right:
                    point.X += step;
                    break;
                case Keys.Escape:
                    Close();
                    break;
            }
            Location = point;
        }

        private void 上ShiftToolStripMenuItem_Click(object sender, EventArgs e)
        {
            FrmPasteImage_KeyDown(sender, new KeyEventArgs(Keys.Up));
        }

        private void 下ShiftToolStripMenuItem_Click(object sender, EventArgs e)
        {
            FrmPasteImage_KeyDown(sender, new KeyEventArgs(Keys.Down));
        }

        private void 左ShiftToolStripMenuItem_Click(object sender, EventArgs e)
        {
            FrmPasteImage_KeyDown(sender, new KeyEventArgs(Keys.Left));
        }

        private void 右ShiftToolStripMenuItem_Click(object sender, EventArgs e)
        {
            FrmPasteImage_KeyDown(sender, new KeyEventArgs(Keys.Right));
        }

        private int opacity = 100;
        private void FrmPasteImage_MouseWheel(object sender, MouseEventArgs e)
        {
            if (e.Delta > 0)
                opacity += 5;
            else
                opacity -= 5;
            if (opacity > 100)
            {
                opacity = 100;
            }
            else if (opacity < 30)
            {
                opacity = 30;
            }
            if (opacity % 10 == 0)
            {
                Opacity = opacity * 1.0d / 100;
            }
        }

        private void tsmOpacity_Click(object sender, EventArgs e)
        {
            var str = (sender as ToolStripMenuItem)?.Text?.TrimEnd('%');
            if (string.IsNullOrEmpty(str))
            {
                return;
            }
            Opacity = BoxUtil.GetInt32FromObject(str) * 1.0d / 100;
        }
    }
}