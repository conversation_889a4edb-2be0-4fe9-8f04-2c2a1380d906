﻿using System;
using System.Threading;

namespace OCRTools
{
    /// <summary>
    ///     定时任务委托方法
    /// </summary>
    public delegate void TimerTaskDelegate();

    /// <summary>
    ///     定时任务接口类
    /// </summary>
    public interface ITimerTask
    {
        void Run();
    }

    /// <summary>
    ///     定时任务服务类
    ///     作者：Duyong
    ///     编写日期：2010-07-25
    /// </summary>
    public class TimerTaskService
    {
        #region 创建定时任务静态方法

        /// <summary>
        ///     使用委托方法创建定时任务
        /// </summary>
        /// <param name="info"></param>
        /// <param name="trd"></param>
        /// <returns></returns>
        public static Thread CreateTimerTaskService(TimerInfo info, TimerTaskDelegate trd)
        {
            var tus = new TimerTaskService(info, trd);
            //创建启动线程
            var threadTimerTaskService = new Thread(tus.Start);
            return threadTimerTaskService;
        }

        #endregion

        #region 定时任务实例成员

        private readonly TimerInfo _timerInfo; //定时信息

        private DateTime _nextRunTime; //下一次执行时间

        private readonly TimerTaskDelegate _timerTaskDelegateFun; //执行具体任务的委托方法

        /// <summary>
        ///     根据定时信息和执行具体任务的委托方法构造定时任务服务
        /// </summary>
        /// <param name="timer">定时信息</param>
        /// <param name="trd">执行具体任务的委托方法</param>
        private TimerTaskService(TimerInfo timer, TimerTaskDelegate trd)
        {
            _timerInfo = timer;
            _timerTaskDelegateFun = trd;
        }


        /// <summary>
        ///     启动定时任务
        /// </summary>
        public void Start()
        {
            //检查定时器
            CheckTimer(_timerInfo);
        }

        /// <summary>
        ///     检查定时器
        /// </summary>
        private void CheckTimer(TimerInfo timerInfo)
        {
            //计算下次执行时间
            if (timerInfo.IsExecFirst)
                _nextRunTime = DateTime.Now.AddSeconds(3);
            else
                GetNextRunTime();

            while (!CommonString.IsExit)
            {
                var dateTimeNow = DateTime.Now;

                //时间比较
                var dateComp = dateTimeNow.Year == _nextRunTime.Year && dateTimeNow.Month == _nextRunTime.Month &&
                               dateTimeNow.Day == _nextRunTime.Day;

                var timeComp = dateTimeNow.Hour == _nextRunTime.Hour && dateTimeNow.Minute == _nextRunTime.Minute &&
                               dateTimeNow.Second == _nextRunTime.Second;

                //如果当前时间等式下次运行时间,则调用线程执行方法
                if (dateComp && timeComp)
                {
                    //调用执行处理方法
                    if (_timerTaskDelegateFun != null)
                        _timerTaskDelegateFun();
                    else
                        Run();
                    //重新计算下次执行时间
                    GetNextRunTime();
                }

                Thread.Sleep(10);
            }
        }

        /// <summary>
        ///     执行方法
        /// </summary>
        protected void Run()
        {
            //TODO.....
        }

        /// <summary>
        ///     计算下一次执行时间
        /// </summary>
        /// <returns></returns>
        private void GetNextRunTime()
        {
            var now = DateTime.Now;
            var nowHh = now.Hour;
            var nowMm = now.Minute;
            var nowSs = now.Second;

            var timeHh = _timerInfo.Hour;
            var timeMm = _timerInfo.Minute;
            var timeSs = _timerInfo.Second;

            //设置执行时间对当前时间进行比较
            var nowTimeComp = nowHh < timeHh || nowHh <= _timerInfo.Hour && nowMm < timeMm ||
                              nowHh <= _timerInfo.Hour && nowMm <= timeMm && nowSs < timeSs;

            //每天
            if ("EveryDay".Equals(_timerInfo.TimerType))
            {
                if (nowTimeComp)
                    _nextRunTime = new DateTime(now.Year, now.Month, now.Day, timeHh, timeMm, timeSs);
                else
                    _nextRunTime = new DateTime(now.Year, now.Month, now.Day, timeHh, timeMm, timeSs).AddDays(1);
            }
            //间隔几小时
            else if ("LoopHours".Equals(_timerInfo.TimerType))
            {
                _nextRunTime =
                    new DateTime(now.Year, now.Month, now.Day, nowHh, timeMm, timeSs).AddHours(_timerInfo.DateValue);
            }
            //间隔几分钟
            else if ("LoopMinutes".Equals(_timerInfo.TimerType))
            {
                _nextRunTime =
                    new DateTime(now.Year, now.Month, now.Day, nowHh, nowMm, timeSs).AddMinutes(_timerInfo.DateValue);
            }
            //每周一次
            else if ("DayOfWeek".Equals(_timerInfo.TimerType))
            {
                var ofweek = DateTime.Now.DayOfWeek;

                var dayOfweek = Convert.ToInt32(DateTime.Now.DayOfWeek);

                if (ofweek == DayOfWeek.Sunday) dayOfweek = 7;

                if (dayOfweek < _timerInfo.DateValue)
                {
                    var addDays = _timerInfo.DateValue - dayOfweek;
                    _nextRunTime = new DateTime(now.Year, now.Month, now.Day, timeHh, timeMm, timeSs).AddDays(addDays);
                }
                else if (dayOfweek == _timerInfo.DateValue && nowTimeComp)
                {
                    _nextRunTime = new DateTime(now.Year, now.Month, now.Day, timeHh, timeMm, timeSs);
                }
                else
                {
                    var addDays = 7 - (dayOfweek - _timerInfo.DateValue);
                    _nextRunTime = new DateTime(now.Year, now.Month, now.Day, timeHh, timeMm, timeSs).AddDays(addDays);
                }
            }
            //每月一次
            else if ("DayOfMonth".Equals(_timerInfo.TimerType))
            {
                if (now.Day < _timerInfo.DateValue)
                    _nextRunTime = new DateTime(now.Year, now.Month, _timerInfo.DateValue, timeHh, timeMm, timeSs);
                else if (now.Day == _timerInfo.DateValue && nowTimeComp)
                    _nextRunTime = new DateTime(now.Year, now.Month, now.Day, timeHh, timeMm, timeSs);
                else
                    _nextRunTime = new DateTime(now.Year, now.Month, _timerInfo.DateValue, timeHh, timeMm, timeSs)
                        .AddMonths(1);
            }
            //指定日期
            else if ("DesDate".Equals(_timerInfo.TimerType))
            {
                _nextRunTime = new DateTime(_timerInfo.Year, _timerInfo.Month, _timerInfo.Day, timeHh, timeMm, timeSs);
            }
            //循环指定天数
            else if ("LoopDays".Equals(_timerInfo.TimerType))
            {
                _nextRunTime =
                    new DateTime(now.Year, now.Month, now.Day, timeHh, timeMm, timeSs).AddDays(_timerInfo.DateValue);
            }
        }

        #endregion
    }

    /// <summary>
    ///     定时信息类
    ///     TimerType   类型：EveryDay(每天),DayOfWeek(每周),DayOfMonth(每月),DesDate(指定日期),LoopDays(循环天数)
    ///     DateValue 日期值：TimerType="DayOfWeek"时,值为1-7表示周一到周日;TimerType="DayOfMonth"时,值为1-31表示1号到31号,
    ///     TimerType="LoopDays"时,值为要循环的天数,TimerType为其它值时,此值无效
    ///     Year   年：TimerType="DesDate"时,此值有效
    ///     Month  月：TimerType="DesDate"时,此值有效
    ///     Day    日：TimerType="DesDate"时,此值有效
    ///     Hour   时：]
    ///     Minute 分： > 设置的执行时间
    ///     Second 秒：]
    /// </summary>
    public class TimerInfo
    {
        public int DateValue;
        public int Day;
        public int Hour = 00;
        public bool IsExecFirst;
        public int Minute = 00;
        public int Month;
        public int Second = 00;
        public string TimerType;
        public int Year;
    }
}