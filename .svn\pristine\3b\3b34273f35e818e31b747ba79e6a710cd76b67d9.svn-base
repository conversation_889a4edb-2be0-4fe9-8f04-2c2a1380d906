using System.Collections.Generic;
using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class EXTSST : Record
	{
		public ushort NumStrings;

		public List<StringOffset> Offsets;

		public EXTSST(Record record)
			: base(record)
		{
		}

		public EXTSST()
		{
			Type = 255;
			Offsets = new List<StringOffset>();
		}

		public void decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			NumStrings = binaryReader.ReadUInt16();
			int numStrings = NumStrings;
			Offsets = new List<StringOffset>(numStrings);
			for (int i = 0; i < numStrings; i++)
			{
				Offsets.Add(ReadStringOffset(binaryReader));
			}
		}

		public void encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(NumStrings);
			foreach (StringOffset offset in Offsets)
			{
				WriteStringOffset(binaryWriter, offset);
			}
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}

		public override void Decode()
		{
			MemoryStream memoryStream = new MemoryStream(base.AllData);
			BinaryReader binaryReader = new BinaryReader(memoryStream);
			NumStrings = binaryReader.ReadUInt16();
			Offsets = new List<StringOffset>();
			while (memoryStream.Position < memoryStream.Length)
			{
				Offsets.Add(ReadStringOffset(binaryReader));
			}
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(NumStrings);
			foreach (StringOffset offset in Offsets)
			{
				WriteStringOffset(binaryWriter, offset);
			}
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}

		private static StringOffset ReadStringOffset(BinaryReader reader)
		{
			StringOffset stringOffset = new StringOffset();
			stringOffset.AbsolutePosition = reader.ReadUInt32();
			stringOffset.RelativePosition = reader.ReadUInt16();
			stringOffset.NotUsed = reader.ReadUInt16();
			return stringOffset;
		}

		private static void WriteStringOffset(BinaryWriter writer, StringOffset stringoffset)
		{
			writer.Write(stringoffset.AbsolutePosition);
			writer.Write(stringoffset.RelativePosition);
			writer.Write(stringoffset.NotUsed);
		}
	}
}
