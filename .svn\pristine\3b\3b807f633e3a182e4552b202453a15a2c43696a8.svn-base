﻿#region License Information (GPL v3)

/*
    ShareX - A program that allows you to take screenshots and share any file type
    Copyright (c) 2007-2023 ShareX Team

    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License
    as published by the Free Software Foundation; either version 2
    of the License, or (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.

    Optionally you can also view the license at <http://www.gnu.org/licenses/>.
*/

#endregion License Information (GPL v3)

using System;
using System.Diagnostics;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools.ScrollingCapture
{
    public partial class ScrollingCaptureRegionForm : Form
    {
        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams createParams = base.CreateParams;
                createParams.ExStyle |= 0x00000008 | 0x00000080;
                return createParams;
            }
        }

        private Rectangle borderRectangle;
        private Rectangle borderRectangle0Based;

        public ScrollingCaptureRegionForm(Rectangle regionRectangle)
        {
            InitializeComponent();
            timerStart = Stopwatch.StartNew();

            borderRectangle = regionRectangle.Offset(1);
            borderRectangle0Based = new Rectangle(0, 0, borderRectangle.Width, borderRectangle.Height);

            Location = borderRectangle.Location;
            Size = borderRectangle.Size;

            Region region = new Region(ClientRectangle);
            region.Exclude(borderRectangle0Based.Offset(-1));
            Region = region;
        }

        private Stopwatch timerStart;

        protected override void OnPaint(PaintEventArgs e)
        {
            using (var borderPen = new Pen(Color.White))
            {
                using (var borderDotPen = new Pen(CommonSetting.截图边框颜色, (float)Math.Max(1, CommonSetting.截图边框宽度)) { DashPattern = new float[] { 5, 2 } })
                {
                    borderDotPen.DashOffset = (float)timerStart.Elapsed.TotalSeconds * -15;
                    e.Graphics.DrawRectangleProper(borderPen, borderRectangle0Based);
                    e.Graphics.DrawRectangleProper(borderDotPen, borderRectangle0Based);
                }
            }

            base.OnPaint(e);
        }
    }
}