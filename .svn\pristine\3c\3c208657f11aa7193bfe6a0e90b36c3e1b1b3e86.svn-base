using System;
using System.Drawing;
using System.Runtime.InteropServices;

namespace OCRTools
{
    public static class PrimaryScreen
    {
        //private const int HORZRES = 8;

        //private const int VERTRES = 10;

        //private const int LOGPIXELSX = 88;

        //private const int LOGPIXELSY = 90;

        //private const int DESKTOPVERTRES = 117;

        //private const int DESKTOPHORZRES = 118;

        //public static Size WorkingArea
        //{
        //    get
        //    {
        //        IntPtr dC = GetDC(IntPtr.Zero);
        //        Size result = default(Size);
        //        result.Width = GetDeviceCaps(dC, 8);
        //        result.Height = GetDeviceCaps(dC, 10);
        //        ReleaseDC(IntPtr.Zero, dC);
        //        return result;
        //    }
        //}

        public static int DpiX
        {
            get
            {
                var dC = GetDC(IntPtr.Zero);
                var deviceCaps = GetDeviceCaps(dC, 88);
                ReleaseDC(IntPtr.Zero, dC);
                return deviceCaps;
            }
        }

        public static float GetScreenScalingFactor()
        {
            float scalingFactor;
            using (Graphics g = Graphics.FromHwnd(IntPtr.Zero))
            {
                IntPtr desktop = g.GetHdc();
                int LogicalScreenHeight = GetDeviceCaps(desktop, 10);//(int)DeviceCap.VERTRES);
                int PhysicalScreenHeight = GetDeviceCaps(desktop, 117);// (int)DeviceCap.DESKTOPVERTRES);
                int logpixelsy = GetDeviceCaps(desktop, 90);// (int)DeviceCap.LOGPIXELSY);
                float screenScalingFactor = (float)PhysicalScreenHeight / LogicalScreenHeight;
                float dpiScalingFactor = logpixelsy / 96f;
                scalingFactor = Math.Max(screenScalingFactor, dpiScalingFactor);
                g.ReleaseHdc(desktop);
            }
            return scalingFactor;
        }

        public static float DpiZoom => (float)(DpiX * 1.0f / 96.0);

        public static Size DESKTOP
        {
            get
            {
                IntPtr dC = GetDC(IntPtr.Zero);
                var result = new Size();
                result.Width = GetDeviceCaps(dC, 118);
                result.Height = GetDeviceCaps(dC, 117);
                ReleaseDC(IntPtr.Zero, dC);
                return result;
            }
        }

        [DllImport("user32.dll")]
        private static extern IntPtr GetDC(IntPtr ptr);

        [DllImport("gdi32.dll")]
        private static extern int GetDeviceCaps(IntPtr hdc, int nIndex);

        [DllImport("user32.dll")]
        private static extern IntPtr ReleaseDC(IntPtr hWnd, IntPtr hDc);

        public static int DpiValue(this int num)
        {
            return num;
            //return Convert.ToInt32(Convert.ToDouble(num) * DpiZoom);
        }
    }
}