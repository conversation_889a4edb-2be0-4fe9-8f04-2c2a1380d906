// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class SynchronizedInputPattern : BasePattern
    {
        public static readonly AutomationEvent InputReachedTargetEvent =
            SynchronizedInputPatternIdentifiers.InputReachedTargetEvent;

        public static readonly AutomationEvent InputReachedOtherElementEvent =
            SynchronizedInputPatternIdentifiers.InputReachedOtherElementEvent;

        public static readonly AutomationEvent InputDiscardedEvent =
            SynchronizedInputPatternIdentifiers.InputDiscardedEvent;

        public static readonly AutomationPattern Pattern = SynchronizedInputPatternIdentifiers.Pattern;
        private IUIAutomationSynchronizedInputPattern _pattern;

        private SynchronizedInputPattern(AutomationElement el, IUIAutomationSynchronizedInputPattern pattern,
            bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
            _pattern = pattern;
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null
                ? null
                : new SynchronizedInputPattern(el, (IUIAutomationSynchronizedInputPattern) pattern, cached);
        }
    }
}