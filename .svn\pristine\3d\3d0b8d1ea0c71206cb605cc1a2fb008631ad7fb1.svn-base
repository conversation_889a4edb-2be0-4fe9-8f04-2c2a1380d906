using System;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace OCRTools
{
    public class AutoTextBox : RichTextBox
    {
        private const int WM_LBUTTONDBLCLK = 515;

        public bool OnIsEdit
        {
            get;
            set;
        }

        public bool IsMove
        {
            get;
            set;
        }

        public bool IsWidth
        {
            get;
            set;
        } = true;


        public event Mydo<PERSON>leclick MyMousedoubleclick;

        public AutoTextBox()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.DoubleBuffer, value: true);
            UpdateStyles();
            base.ScrollBars = RichTextBoxScrollBars.None;
            base.ContentsResized += delegate (object a, ContentsResizedEventArgs e)
            {
                base.Height = e.NewRectangle.Height;
                SetSpace.SetLineSpace(this, 300);
            };
            Cursor = Cursors.IBeam;
        }

        protected override void OnHandleCreated(EventArgs e)
        {
            base.OnHandleCreated(e);
            if (!base.AutoWordSelection)
            {
                base.AutoWordSelection = true;
            }
        }

        public void SetControlEnabled(bool enabled)
        {
            if (enabled)
            {
                Win32.SetWindowLong(base.Handle, -16, -134217729 & Win32.GetWindowLong(base.Handle, -16));
            }
            else
            {
                Win32.SetWindowLong(base.Handle, -16, 134217728 + Win32.GetWindowLong(base.Handle, -16));
            }
        }

        protected override void WndProc(ref Message m)
        {
            if (m.Msg == 515)
            {
                MouseEventArgs e = new MouseEventArgs(MouseButtons.Left, 2, Control.MousePosition.X, Control.MousePosition.Y, 0);
                if (this.MyMousedoubleclick != null)
                {
                    this.MyMousedoubleclick(this, e);
                }
            }
            else if (m.Msg == 32)
            {
                DefWndProc(ref m);
                if (!Cursor.Equals(Cursors.WaitCursor))
                {
                    m.Result = (IntPtr)1;
                }
                else
                {
                    base.WndProc(ref m);
                }
            }
            else
            {
                base.WndProc(ref m);
            }
        }

        [DllImport("user32.dll", CharSet = CharSet.Auto, ExactSpelling = true)]
        public static extern IntPtr SetCursor(HandleRef hcursor);

        public void ArrangeTextBoxSize()
        {
            if (IsWidth)
            {
                GetmaxLength();
            }
        }

        public int wid()
        {
            if (IsWidth)
            {
                string text = Text;
                StringFormat stringFormat = new StringFormat(StringFormatFlags.NoClip);
                using (Graphics graphics = CreateGraphics())
                {
                    return (int)(graphics.MeasureString(text, Font, PointF.Empty, stringFormat).Width + 1f);
                }
            }
            return 0;
        }

        public void GetmaxLength()
        {
            string text = Text;
            StringFormat stringFormat = new StringFormat(StringFormatFlags.NoClip);
            using (Graphics graphics = CreateGraphics())
            {
                int num2 = base.Width = (int)(graphics.MeasureString(text, Font, PointF.Empty, stringFormat).Width + 1f);
            }
        }

        protected override void OnTextChanged(EventArgs e)
        {
            ArrangeTextBoxSize();
            base.OnTextChanged(e);
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            base.OnKeyDown(e);
            if (e.Control && e.KeyCode == Keys.A && OnIsEdit)
            {
                SelectAll();
                e.SuppressKeyPress = true;
            }
        }
    }
}
