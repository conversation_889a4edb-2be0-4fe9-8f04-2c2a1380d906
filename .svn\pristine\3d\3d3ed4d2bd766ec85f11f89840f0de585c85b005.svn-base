﻿using OCRTools.Common;
using OCRTools.Language;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FormViewImageWX : BaseForm
    {
        private TextSelectableImageViewer imageViewer;
        private List<TextCellInfo> textRegions;
        private Label statusLabel;

        public FormViewImageWX()
        {
            Icon = FrmMain.FrmTool.Icon;
            Text = $"{"图像预览".CurrentText()}-{CommonString.FullName.CurrentText()}";
            ShowIcon = true;
            ShowInTaskbar = true;

            InitializeControls();

            // 设置窗口默认大小和居中显示
            Size = new Size(900, 700);
            StartPosition = FormStartPosition.CenterScreen;
            MinimumSize = new Size(600, 400);
        }

        private void InitializeControls()
        {
            // 创建状态栏
            statusLabel = new Label
            {
                Text = "点击或拖拽选择文字",
                Dock = DockStyle.Bottom,
                Height = 25,
                TextAlign = ContentAlignment.MiddleLeft,
                BackColor = SystemColors.Control,
                BorderStyle = BorderStyle.FixedSingle
            };
            Controls.Add(statusLabel);

            // 创建自定义图片查看器（直接填充整个窗体）
            imageViewer = new TextSelectableImageViewer
            {
                Dock = DockStyle.Fill
            };

            // 订阅文字选择事件
            imageViewer.TextSelectionChanged += OnTextSelectionChanged;

            Controls.Add(imageViewer);
        }

        private void OnTextSelectionChanged(object sender, TextSelectionEventArgs e)
        {
            if (!string.IsNullOrEmpty(e.SelectedText))
            {
                var displayText = e.SelectedText.Length > 50 ? e.SelectedText.Substring(0, 50) + "..." : e.SelectedText;
                statusLabel.Text = $"已选择 {e.SelectedCharacterCount} 个字符: {displayText}";

                // 复制到剪贴板
                try
                {
                    ClipboardService.SetText(e.SelectedText);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"复制到剪贴板失败: {ex.Message}");
                }
            }
            else
            {
                statusLabel.Text = "点击或拖拽选择文字";
            }
        }

        internal void Bind(Image image, List<TextCellInfo> regions)
        {
            textRegions = regions ?? new List<TextCellInfo>();
            Text = $"{"图像预览".CurrentText()} {"尺寸".CurrentText()}:{image?.Width}×{image?.Height} - {CommonString.FullName.CurrentText()}";

            // 绑定图片和文字区域到查看器
            imageViewer.BindImageAndTextRegions(image, textRegions);

            // 根据图片大小动态调整窗口大小
            AdjustWindowSizeForImage(image);
        }

        /// <summary>
        /// 根据图片大小动态调整窗口大小
        /// </summary>
        private void AdjustWindowSizeForImage(Image image)
        {
            if (image == null) return;

            // 计算合适的窗口大小
            var screenSize = Screen.PrimaryScreen.WorkingArea.Size;
            var maxWidth = (int)(screenSize.Width * 0.90); // 最大宽度为屏幕的90%
            var maxHeight = (int)(screenSize.Height * 0.85); // 最大高度为屏幕的85%

            // 基础UI元素的空间
            var uiElementsHeight = 50; // 状态栏等UI元素的高度
            var padding = 40; // 内边距

            // 计算图片的合适显示尺寸
            var imageDisplayWidth = image.Width;
            var imageDisplayHeight = image.Height;

            // 如果图片太大，按比例缩小
            var maxImageWidth = maxWidth - padding;
            var maxImageHeight = maxHeight - uiElementsHeight - padding;

            if (imageDisplayWidth > maxImageWidth || imageDisplayHeight > maxImageHeight)
            {
                var scaleX = (double)maxImageWidth / imageDisplayWidth;
                var scaleY = (double)maxImageHeight / imageDisplayHeight;
                var scale = Math.Min(scaleX, scaleY);

                imageDisplayWidth = (int)(imageDisplayWidth * scale);
                imageDisplayHeight = (int)(imageDisplayHeight * scale);
            }

            // 根据图片实际显示尺寸计算窗口大小
            var targetWidth = Math.Max(imageDisplayWidth + padding, 600); // 最小宽度600
            var targetHeight = Math.Max(imageDisplayHeight + uiElementsHeight + padding, 400); // 最小高度400

            // 限制在屏幕范围内
            targetWidth = Math.Min(targetWidth, maxWidth);
            targetHeight = Math.Min(targetHeight, maxHeight);

            // 设置窗口大小
            Size = new Size(targetWidth, targetHeight);

            // 重新居中显示
            CenterToScreen();
        }
    }
}
