﻿using System;
using MetroFramework.Forms;
using OCRTools.Common;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmPicCompare : MetroForm
    {

        private bool _isImageMode;

        public bool IsImageMode
        {
            get => _isImageMode;
            set
            {
                _isImageMode = value;
                content.SetImageMode(_isImageMode);
            }
        }

        public FrmPicCompare()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            Load += FrmPicCompare_Load;
        }

        private void FrmPicCompare_Load(object sender, EventArgs e)
        {
            if (IsImageMode)
            {
                SetImageMode(content.Image);
            }
            content.ShowImageTool();
        }

        internal void Init(SpiltMode spiltMode, bool isShowOld)
        {
            content.SpiltModel = spiltMode;
            content.IsShowOldContent = isShowOld;
        }

        internal void Bind(Image image, OcrContent ocrContent = null)
        {
            content.Image = image;
            content.NowDisplayMode = DisplayModel.图文模式;
            if (ocrContent != null)
            {
                content.BindContentByOcr(ocrContent);
                if (string.IsNullOrEmpty(ocrContent.processName))
                {
                    IsImageMode = true;
                }
                Text = IsImageMode ? "图像预览" : $"图文模式-【{ocrContent.processName}】";
            }
        }

        private const int CONTROL_WIDTH = 45;
        private const int CONTROL_HEIGHT = 85;

        private void SetImageMode(Image image)
        {
            Padding = new Padding(20, 60, 20, 20);
            StartPosition = FormStartPosition.CenterScreen;
            DisplayHeader = true;
            var width = image.Width + CONTROL_WIDTH;
            var height = image.Height + CONTROL_HEIGHT;
            var minWidth = Screen.PrimaryScreen.WorkingArea.Width * 0.367;
            var minHeight = Screen.PrimaryScreen.WorkingArea.Height * 0.542;
            width = (int)Math.Max(width, minWidth);
            height = (int)Math.Max(height, minHeight);
            width = (int)Math.Min(width, Screen.PrimaryScreen.WorkingArea.Width);
            height = (int)Math.Min(height, Screen.PrimaryScreen.WorkingArea.Height);
            Size = new Size(width, height);
            Location = new Point((Screen.PrimaryScreen.WorkingArea.Width - Width) / 2,
                (Screen.PrimaryScreen.WorkingArea.Height - Height) / 2);
        }

        private void FrmPicCompare_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape) Close();
        }
    }
}