// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class DragPattern : BasePattern
    {
        public static readonly AutomationProperty IsGrabbedProperty = DragPatternIdentifiers.IsGrabbedProperty;
        public static readonly AutomationProperty GrabbedItemsProperty = DragPatternIdentifiers.GrabbedItemsProperty;
        public static readonly AutomationProperty DropEffectProperty = DragPatternIdentifiers.DropEffectProperty;
        public static readonly AutomationProperty DropEffectsProperty = DragPatternIdentifiers.DropEffectsProperty;
        public static readonly AutomationEvent DragStartEvent = DragPatternIdentifiers.DragStartEvent;
        public static readonly AutomationEvent DragCancelEvent = DragPatternIdentifiers.DragCancelEvent;
        public static readonly AutomationEvent DragCompleteEvent = DragPatternIdentifiers.DragCompleteEvent;
        public static readonly AutomationPattern Pattern = DragPatternIdentifiers.Pattern;

        private IUIAutomationDragPattern _pattern;


        private DragPattern(AutomationElement el, IUIAutomationDragPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
            _pattern = pattern;
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new DragPattern(el, (IUIAutomationDragPattern) pattern, cached);
        }
    }
}