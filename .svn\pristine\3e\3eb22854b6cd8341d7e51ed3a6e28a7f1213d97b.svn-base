using OCRTools.Language;
using OCRTools.ScreenCaptureLib;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;
using static OCRTools.Common.GuideThemeManager;

namespace OCRTools.Common
{
    public class GuidePresenterForm : Form
    {
        private GuideThemeManager _themeManager;

        private Form _mainForm;
        private GuideEntity guide;
        private int _currentGuideIndex = -1;
        private Panel pnlMain;
        private string _titleText;
        private string _descText;
        private Font _titleFont;
        private Font _descFont;
        private Rectangle _titleRect;
        private Rectangle _descRect;
        private Button btnPrev;
        private Button btnNext;
        private LinkLabel lnkSkip;
        private double wScale = 1d;
        private double hScale = 1d;

        private Rectangle _currentHighlightRect = Rectangle.Empty;
        private ArrowDirection _currentArrowDirection;

        private Timer _animationTimer;
        private Point _targetPanelLocation;
        private Point _startPanelLocation;
        private int _animationStep = 0;
        private const int ANIMATION_STEPS = 10;

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern int SendMessage(IntPtr hWnd, Int32 wMsg, bool wParam, Int32 lParam);
        private const int WM_SETREDRAW = 10;

        // 开始禁止窗体重绘
        private void BeginUpdate()
        {
            SendMessage(this.Handle, WM_SETREDRAW, false, 0);
        }

        // 结束禁止窗体重绘并强制刷新
        private void EndUpdate()
        {
            // 启用重绘
            SendMessage(this.Handle, WM_SETREDRAW, true, 0);

            this.Invalidate();
        }

        public GuidePresenterForm(Form mainForm, GuideEntity entity)
        {
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.SetStyle(ControlStyles.SupportsTransparentBackColor, true);
            this.BackColor = Color.Transparent;
            this.Opacity = 1.0;

            // 初始化双缓冲绘图
            SetStyle(ControlStyles.AllPaintingInWmPaint |
                    ControlStyles.UserPaint |
                    ControlStyles.DoubleBuffer |
                    ControlStyles.OptimizedDoubleBuffer, true);
            UpdateStyles();

            this.Region = mainForm.Region;

            // 初始化动画计时器
            _animationTimer = new Timer();
            _animationTimer.Interval = 15; // 15毫秒一帧，约60fps
            _animationTimer.Tick += AnimationTimer_Tick;

            guide = entity;
            if (guide.ShowSummary && !guide.Items[0].Rect.IsEmpty)
            {
                var strSummary = guide.Desc;
                if (string.IsNullOrEmpty(strSummary))
                {
                    strSummary = string.Join("\n", guide.Items.Where(p => p.Summary).Select(p => p.Title.CurrentText(true))).Trim();
                    if (string.IsNullOrEmpty(strSummary))
                    {
                        strSummary = guide.Title;
                    }
                }
                guide.Items.Insert(0, new GuideItem()
                {
                    Title = guide.Title,
                    Desc = strSummary,
                    Rect = Rectangle.Empty,
                    Exec = guide.Items[0].Exec
                });
                guide.Items[1].Exec = null;
            }
            _mainForm = mainForm;
            if (!mainForm.Visible)
            {
                mainForm.Show();
                Update();
            }
            if (mainForm.WindowState != FormWindowState.Normal)
            {
                mainForm.WindowState = FormWindowState.Normal;
                Update();
            }
            wScale = CommonTheme.DpiScale;
            hScale = CommonTheme.DpiScale;
            FormBorderStyle = FormBorderStyle.None;
            ShowInTaskbar = false;
            Margin = CommonString.PaddingZero;
            Padding = CommonString.PaddingZero;
            BackgroundImageLayout = ImageLayout.Stretch;
            InitializeControls();
            CommonMethod.EnableDoubleBuffering(this);
            Shown += GuidePresenterForm_Shown;
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            if (keyData == Keys.Escape)
            {
                CloseGuide();
                return true;
            }
            if (keyData == Keys.Left || keyData == Keys.Up)
            {
                ShowPreviousGuide();
                return true;
            }
            if (keyData == Keys.Right || keyData == Keys.Down)
            {
                ShowNextGuide(false);
                return true;
            }
            return base.ProcessCmdKey(ref msg, keyData);
        }

        private void GuidePresenterForm_Shown(object sender, EventArgs e)
        {
            dicImage = new Dictionary<string, Bitmap>();
            RefreshBackImg(guide.Items[0].Exec);
            Bounds = _mainForm.Bounds;
            Location = _mainForm.Location;
            if (guide?.Items?.Count > 0)
            {
                ShowNextGuide(false);
            }
        }

        private Dictionary<string, Bitmap> dicImage;

        private Bitmap _backgroundImage; // 存储背景截图

        private void RefreshBackImg(string exec)
        {
            var strKey = string.IsNullOrEmpty(exec) ? "DEFAULT" : exec;
            if (!dicImage.TryGetValue(strKey, out _backgroundImage) || _backgroundImage == null)
            {
                var start = ServerTime.DateTime.Ticks;
                if (!string.IsNullOrEmpty(exec))
                    _mainForm.ExecuteScript(exec);

                var rect = new Rectangle();
                var image = Screenshot.CaptureHandle(_mainForm.Handle, ref rect);
                dicImage[strKey] = image;
                _backgroundImage = image;
                Console.Write(strKey + ":" + new TimeSpan(ServerTime.DateTime.Ticks - start).TotalMilliseconds.ToString("F0") + "ms");
            }
        }

        private float _MaxTitleFontSize;
        private float _MaxDescFontSize;
        private int cornerRadius = 12;

        private void InitializeControls()
        {
            // 初始化主题管理器
            _themeManager = GuideThemeManager.Create(CommonSetting.夜间模式);

            pnlMain = new Panel
            {
                Size = new Size(Math.Min(_mainForm.Width, (int)(450 * wScale)), Math.Min((int)(210 * hScale), _mainForm.Height)),
                Location = new Point(100, 100),
                Dock = DockStyle.None,
            };

            pnlMain.Paint += PnlMain_Paint;

            pnlMain.Region = new Region(CreateRoundedRectanglePath(
                new Rectangle(0, 0, pnlMain.Width, pnlMain.Height), cornerRadius));

            Controls.Add(pnlMain);

            _titleFont = CommonString.GetSysBoldFont(20);
            _descFont = CommonString.GetSysNormalFont(15);

            _titleRect = new Rectangle(24, 18, pnlMain.Width - 48, (int)(45 * hScale));
            _descRect = new Rectangle(24, _titleRect.Bottom, _titleRect.Width, pnlMain.Height - _titleRect.Bottom - (int)(55 * hScale));

            lnkSkip = new LinkLabel
            {
                BackColor = Color.Transparent, // Transparent to show panel gradient
                Text = "跳过".CurrentText(),
                Font = CommonString.GetSysBoldFont(13),
                Location = new Point(24, pnlMain.Height - (int)(42 * hScale)),
                AutoSize = true,
                LinkBehavior = LinkBehavior.NeverUnderline,
                TabStop = false,
                Cursor = Cursors.Hand
            };

            var btnWidth = (int)(100 * wScale);
            var btnFont = CommonString.GetSysBoldFont(16);
            btnPrev = new Button
            {
                Text = "上一步".CurrentText(),
                Size = new Size(btnWidth, (int)(36 * hScale)),
                Font = btnFont,
                Visible = false,
                TabStop = false,
                Location = new Point(pnlMain.Width - 30 - btnWidth * 2, pnlMain.Height - (int)(50 * hScale)),
                Cursor = Cursors.Hand
            };

            // 上一步按钮样式
            ProcessButtonStyle(_themeManager.PrevButtonStyle, btnPrev);
            btnPrev.Click += (s, e) => ShowPreviousGuide();

            btnNext = new Button
            {
                Text = "下一步".CurrentText(),
                Size = new Size(btnWidth, (int)(36 * hScale)),
                Font = btnFont,
                Visible = false,
                TabStop = false,
                Cursor = Cursors.Hand,
                Location = new Point(btnPrev.Left + btnWidth + 5, pnlMain.Height - (int)(50 * hScale)) // Reduced gap from 10 to 5
            };

            // 下一步按钮样式
            ProcessButtonStyle(_themeManager.NextButtonStyle, btnNext);
            btnNext.Click += (s, e) => ShowNextGuide(true);

            // 链接样式 - 跳过链接
            lnkSkip.LinkBehavior = _themeManager.SkipLinkStyle.Behavior;
            lnkSkip.ForeColor = _themeManager.SkipLinkStyle.ForeColor;
            lnkSkip.LinkColor = _themeManager.SkipLinkStyle.LinkColor;
            lnkSkip.ActiveLinkColor = _themeManager.SkipLinkStyle.ActiveLinkColor;
            lnkSkip.Click += (s, e) => CloseGuide();

            pnlMain.Controls.Add(btnPrev);
            pnlMain.Controls.Add(btnNext);
            pnlMain.Controls.Add(lnkSkip);

            CommonMethod.SetStyle(lnkSkip, ControlStyles.Selectable, false);

            _MaxTitleFontSize = _titleFont.Size;
            _MaxDescFontSize = _descFont.Size;
        }

        private void ProcessButtonStyle(ButtonStyle style, Button btn)
        {
            btn.FlatStyle = style.Style;
            if (btn.FlatStyle == FlatStyle.Flat)
            {
                btn.FlatAppearance.BorderSize = style.BorderSize;
                btn.FlatAppearance.MouseOverBackColor = style.HoverColor;
                btn.FlatAppearance.MouseDownBackColor = style.ClickColor;
            }
            btn.BackColor = style.BackColor;
            btn.ForeColor = style.ForeColor;
            if (style.IsRound)
                btn.Region = new Region(CreateRoundedRectanglePath(
                    new Rectangle(0, 0, btn.Width, btn.Height), 8));
        }

        private void PnlMain_Paint(object s, PaintEventArgs e)
        {
            e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
            e.Graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
            e.Graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;
            e.Graphics.CompositingQuality = CompositingQuality.HighQuality;

            Rectangle rect = new Rectangle(0, 0, pnlMain.Width, pnlMain.Height);
            GraphicsPath path = CreateRoundedRectanglePath(rect, cornerRadius);

            // 1. 背景渐变 - 使用主题管理器中的颜色配置
            // 从主题管理器获取渐变色配置
            using (LinearGradientBrush gradientBrush = new LinearGradientBrush(rect, Color.Transparent, Color.Transparent, LinearGradientMode.Vertical))
            {
                gradientBrush.InterpolationColors = new ColorBlend
                {
                    Colors = _themeManager.PanelBlendColors,
                    Positions = _themeManager.PanelBlendPositions
                };
                e.Graphics.FillPath(gradientBrush, path);
            }

            // 2. 微妙的内边框效果 - 使用主题管理器
            using (Pen innerBorderPen = new Pen(_themeManager.InnerBorderColor, 1.0f))
            {
                innerBorderPen.Alignment = PenAlignment.Inset;
                e.Graphics.DrawPath(innerBorderPen, path);
            }

            // 3. 轻微的顶部高光线 - 使用主题管理器
            Rectangle topLineRect = new Rectangle(5, 1, pnlMain.Width - 10, 1);
            using (SolidBrush topLineBrush = new SolidBrush(_themeManager.TopLineColor))
            {
                e.Graphics.FillRectangle(topLineBrush, topLineRect);
            }

            if (_animationStep == 0)
                // 4. 绘制月光/日光效果 (DrawSunLightEffect 已由用户恢复)
                DrawSunLightEffect(e.Graphics, rect);

            // 5. 文本绘制 - 使用主题管理器中的文本颜色
            if (!string.IsNullOrEmpty(_titleText))
            {
                e.Graphics.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;
                using (SolidBrush titleBrush = new SolidBrush(_themeManager.TitleTextColor))
                {
                    StringFormat titleFormat = new StringFormat
                    {
                        Alignment = StringAlignment.Near,
                        LineAlignment = StringAlignment.Center, // 将标题文本垂直居中显示
                        Trimming = StringTrimming.EllipsisCharacter
                    };
                    e.Graphics.DrawString(_titleText, _titleFont, titleBrush, _titleRect, titleFormat);
                }
            }

            if (!string.IsNullOrEmpty(_descText))
            {
                using (SolidBrush descBrush = new SolidBrush(_themeManager.DescTextColor))
                {
                    StringFormat descFormat = new StringFormat
                    {
                        Alignment = StringAlignment.Near,
                        LineAlignment = StringAlignment.Center, // 将描述文本垂直居中显示
                        Trimming = StringTrimming.EllipsisCharacter
                    };
                    e.Graphics.DrawString(_descText, _descFont, descBrush, _descRect, descFormat);
                }
            }
            path.Dispose(); // Dispose path after all drawing operations using it are complete
        }

        private Random _sunEffectRand = new Random();

        private void DrawSunLightEffect(Graphics g, Rectangle bounds)
        {
            g.SmoothingMode = SmoothingMode.AntiAlias;
            g.InterpolationMode = InterpolationMode.HighQualityBicubic;
            g.PixelOffsetMode = PixelOffsetMode.HighQuality;
            g.CompositingQuality = CompositingQuality.HighQuality;

            PointF sunOrigin = new PointF(bounds.Width - (float)(8 * wScale), (float)(8 * hScale)); // Sun/Moon position
            float maxDimension = Math.Max(bounds.Width, bounds.Height);

            // Theme manager colors (used for sun/moon and night flares/stars)
            Color coreCenterColor = _themeManager.CoreCenterColor;
            Color coreSurroundColor = _themeManager.CoreSurroundColor;
            Color wideGlowCenterColor = _themeManager.WideGlowCenterColor; // Alpha is different in ThemeManager for day/night
            Color flareBase1 = _themeManager.FlareBase1; // Night mode flare/star base color
            Color flareBase2 = _themeManager.FlareBase2; // Night mode flare/star base color
            Color flareBase3 = _themeManager.FlareBase3; // Night mode flare/star base color

            // --- Sun/Moon Core Glow (Same for Day/Night, colors differ via ThemeManager) ---
            float coreGlowRadius = (float)(maxDimension * 0.07 * Math.Min(wScale, hScale));
            if (coreGlowRadius > 0)
            {
                using (GraphicsPath coreGlowPath = new GraphicsPath())
                {
                    coreGlowPath.AddEllipse(sunOrigin.X - coreGlowRadius, sunOrigin.Y - coreGlowRadius, coreGlowRadius * 2, coreGlowRadius * 2);
                    using (PathGradientBrush coreGlowBrush = new PathGradientBrush(coreGlowPath))
                    {
                        coreGlowBrush.CenterPoint = sunOrigin;
                        coreGlowBrush.CenterColor = coreCenterColor;
                        coreGlowBrush.SurroundColors = new Color[] { coreSurroundColor };
                        g.FillPath(coreGlowBrush, coreGlowPath);
                    }
                }
            }

            // --- Sun/Moon Outer Wide Glow --- 
            // GlowRadiusFactor is significantly different for day/night in ThemeManager (e.g., 25.0 for day, smaller for night)
            float outerGlowRadiusBase = (float)(maxDimension * 0.12f * Math.Min(wScale, hScale));
            if (outerGlowRadiusBase > coreGlowRadius)
            {
                float actualWideGlowRadiusValue = outerGlowRadiusBase * _themeManager.GlowRadiusFactor;
                int wideGlowSteps = CommonSetting.夜间模式 ? _sunEffectRand.Next(2, 5) : 3; // Day: 1 very large diffuse glow; Night: a few smaller steps

                for (int i = 0; i < wideGlowSteps; i++)
                {
                    using (GraphicsPath wideGlowPath = new GraphicsPath())
                    {
                        float currentStepRadius = actualWideGlowRadiusValue;
                        if (CommonSetting.夜间模式 && wideGlowSteps > 1)
                        {
                            // For night, if multiple steps, slightly vary radius or alpha. Here, fixed radius per step is fine.
                            currentStepRadius = actualWideGlowRadiusValue * (1.0f - i * 0.15f); // Example: decreasing radius for night steps
                        }

                        wideGlowPath.AddEllipse(sunOrigin.X - currentStepRadius, sunOrigin.Y - currentStepRadius, currentStepRadius * 2, currentStepRadius * 2);
                        using (PathGradientBrush wideGlowBrush = new PathGradientBrush(wideGlowPath))
                        {
                            wideGlowBrush.CenterPoint = sunOrigin;
                            wideGlowBrush.CenterColor = wideGlowCenterColor; // Alpha is managed by ThemeManager (e.g., low for day sun, higher for moon)
                            wideGlowBrush.SurroundColors = new Color[] { Color.FromArgb(0, wideGlowCenterColor.R, wideGlowCenterColor.G, wideGlowCenterColor.B) };
                            g.FillPath(wideGlowBrush, wideGlowPath);
                        }
                    }
                }
            }

            // --- Effect Definitions (Clouds for Day, Flares/Stars for Night) ---
            var effectDefinitions = new[]
            {
                new { DistFactor = 0.2f, SizeFactor = 0.05f, BaseClr = flareBase1, Alpha = CommonSetting.夜间模式 ? 120 : 240, Shape = CommonSetting.夜间模式 ? 3 : 0 }, // Cloud Alpha: 110 -> 240 (for opaque clouds)
                new { DistFactor = 0.4f, SizeFactor = 0.035f, BaseClr = flareBase2, Alpha = CommonSetting.夜间模式 ? 100 : 245, Shape = CommonSetting.夜间模式 ? 3 : 1 }, // Cloud Alpha: 130 -> 245
                new { DistFactor = 0.6f, SizeFactor = 0.045f, BaseClr = flareBase3, Alpha = CommonSetting.夜间模式 ? 140 : 235, Shape = CommonSetting.夜间模式 ? 3 : 1 }, // Cloud Alpha: 100 -> 235
                //// Add a few more for better cloud cover / more stars if desired
                 new { DistFactor = 0.0f, SizeFactor = 0.04f, BaseClr = flareBase1, Alpha = CommonSetting.夜间模式 ? 90 : 240, Shape = CommonSetting.夜间模式 ? 3 : 0 },  // Cloud Alpha: 120 -> 240
                 new { DistFactor = 0.0f, SizeFactor = 0.055f, BaseClr = flareBase2, Alpha = CommonSetting.夜间模式 ? 110 : 230, Shape = CommonSetting.夜间模式 ? 3 : 1 }  // Cloud Alpha: 110 -> 230
            };

            PointF flareDirectionTarget = new PointF(bounds.Width * 0.25f, bounds.Height * 0.70f);
            float pathLength = 1;
            flareDirectionTarget = new PointF(bounds.Width * 0.25f, bounds.Height * 0.70f);
            pathLength = (float)Math.Sqrt(Math.Pow(flareDirectionTarget.X - sunOrigin.X, 2) + Math.Pow(flareDirectionTarget.Y - sunOrigin.Y, 2));
            if (pathLength == 0) pathLength = 1;

            if (!CommonSetting.夜间模式)
            {
                foreach (var def in effectDefinitions)
                {
                    float distVariation = (float)(_sunEffectRand.NextDouble() * 0.03 - 0.015);
                    float flareDistOnPath = pathLength * (def.DistFactor + distVariation);

                    PointF flareCenter = new PointF(
                        sunOrigin.X + (flareDirectionTarget.X - sunOrigin.X) * (flareDistOnPath / pathLength) + (_sunEffectRand.Next(-2, 3) * (float)wScale),
                        sunOrigin.Y + (flareDirectionTarget.Y - sunOrigin.Y) * (flareDistOnPath / pathLength) + (_sunEffectRand.Next(-2, 3) * (float)hScale)
                    );

                    float baseFlareDim = maxDimension * def.SizeFactor * (float)Math.Min(wScale, hScale);
                    float flareW = baseFlareDim * (float)(0.9f + _sunEffectRand.NextDouble() * 0.2f);
                    float flareH = baseFlareDim * (float)(0.9f + _sunEffectRand.NextDouble() * 0.2f);

                    if (def.Shape == 1) flareW *= 2.0f;
                    if (def.Shape == 2) flareH *= 2.0f;

                    if (flareW < 1f) flareW = 1f;
                    if (flareH < 1f) flareH = 1f;

                    int currentAlpha = Math.Max(10, Math.Min(255, def.Alpha + _sunEffectRand.Next(-5, 6)));
                    Color flareColor = Color.FromArgb(currentAlpha, def.BaseClr);

                    using (GraphicsPath flarePath = new GraphicsPath())
                    {
                        if (def.Shape == 0 || def.Shape == 1)
                        {
                            flarePath.AddEllipse(flareCenter.X - flareW / 2, flareCenter.Y - flareH / 2, flareW, flareH);
                        }

                        using (PathGradientBrush flareBrush = new PathGradientBrush(flarePath))
                        {
                            flareBrush.CenterPoint = flareCenter;
                            flareBrush.CenterColor = flareColor;
                            flareBrush.SurroundColors = new Color[] { Color.FromArgb(0, flareColor.R, flareColor.G, flareColor.B) };
                            g.FillPath(flareBrush, flarePath);
                        }
                    }
                }
            }
            else
                foreach (var def in effectDefinitions)
                {
                    PointF itemCenter;
                    float itemBaseDim = maxDimension * def.SizeFactor * (float)Math.Min(wScale, hScale);
                    float itemW, itemH;
                    int finalItemAlpha;
                    Color itemEffectBaseColor;

                    itemEffectBaseColor = def.BaseClr;
                    finalItemAlpha = Math.Max(8, Math.Min(25, def.Alpha + _sunEffectRand.Next(-2, 3)));

                    itemCenter = new PointF((float)(_sunEffectRand.NextDouble() * bounds.Width), (float)(_sunEffectRand.NextDouble() * bounds.Height));
                    itemW = itemBaseDim * (float)(0.9f + _sunEffectRand.NextDouble() * 0.2f);
                    itemH = itemBaseDim * (float)(0.9f + _sunEffectRand.NextDouble() * 0.2f);
                    if (def.Shape == 1) itemW *= 2.0f;
                    if (def.Shape == 2) itemH *= 2.0f;

                    if (itemW < 5f * wScale) itemW = 5f * (float)wScale;
                    if (itemH < 5f * hScale) itemH = 5f * (float)hScale;

                    using (GraphicsPath nightEffectPath = new GraphicsPath())
                    {
                        int points = 6;
                        float outerRadiusStar = Math.Max(itemW, itemH) / 2;
                        if (outerRadiusStar < 1.2f * (float)Math.Min(wScale, hScale)) outerRadiusStar = 1.2f * (float)Math.Min(wScale, hScale); // Increased minimum star size for better visibility 
                        float innerRadiusStar = outerRadiusStar * 0.45f;
                        PointF[] polyPoints = new PointF[points * 2];
                        for (int k = 0; k < points; k++)
                        {
                            double angleOuter = (Math.PI * 2.0 / points * k) - (Math.PI / 2.0);
                            polyPoints[k * 2] = new PointF(itemCenter.X + (float)(Math.Cos(angleOuter) * outerRadiusStar), itemCenter.Y + (float)(Math.Sin(angleOuter) * outerRadiusStar));
                            double angleInner = angleOuter + (Math.PI / points);
                            polyPoints[k * 2 + 1] = new PointF(itemCenter.X + (float)(Math.Cos(angleInner) * innerRadiusStar), itemCenter.Y + (float)(Math.Sin(angleInner) * innerRadiusStar));
                        }
                        nightEffectPath.AddPolygon(polyPoints);

                        Color finalNightEffectColor = Color.FromArgb(finalItemAlpha, itemEffectBaseColor);
                        using (PathGradientBrush nightEffectBrush = new PathGradientBrush(nightEffectPath))
                        {
                            nightEffectBrush.CenterPoint = itemCenter;
                            nightEffectBrush.CenterColor = finalNightEffectColor;
                            nightEffectBrush.SurroundColors = new Color[] { Color.FromArgb(0, finalNightEffectColor.R, finalNightEffectColor.G, finalNightEffectColor.B) };
                            g.FillPath(nightEffectBrush, nightEffectPath);
                        }
                    }
                }
        }

        private bool _isChangingGuide = false;
        private void ShowNextGuide(bool isUser)
        {
            if (_isChangingGuide) return; // 防止重复调用

            _isChangingGuide = true;
            try
            {
                if (_currentGuideIndex < guide.Items.Count - 1)
                {
                    _currentGuideIndex++;
                    ShowGuide(guide.Items[_currentGuideIndex]);
                    btnNext.Focus();
                }
                else if (isUser && _currentGuideIndex == guide.Items.Count - 1)
                {
                    CloseGuide();
                }
            }
            finally
            {
                _isChangingGuide = false;
            }
        }

        private void ShowPreviousGuide()
        {
            if (_isChangingGuide) return; // 防止重复调用

            _isChangingGuide = true;
            try
            {
                if (_currentGuideIndex > 0)
                {
                    _currentGuideIndex--;
                    ShowGuide(guide.Items[_currentGuideIndex]);
                    if (btnPrev.Visible)
                        btnPrev.Focus();
                    else
                        btnNext.Focus();
                }
            }
            finally
            {
                _isChangingGuide = false;
            }
        }

        private void ShowGuide(GuideItem item)
        {
            this.BeginUpdate();
            try
            {
                RefreshBackImg(item.Exec);

                var rect = item.Rect;
                if (!string.IsNullOrEmpty(item.Ctrl))
                {
                    var targetRect = FindTargetControlRectangle(item.Ctrl);
                    if (!targetRect.IsEmpty)
                    {
                        rect = targetRect.ZoomBig(10);
                    }
                }
                else if (!rect.IsEmpty)
                {
                    if (!guide.BaseSize.IsEmpty)
                    {
                        var widthScale = Width * 1d / guide.BaseSize.Width;
                        var heightScale = Height * 1d / guide.BaseSize.Height;
                        rect.X = (int)(rect.X * widthScale);
                        rect.Y = (int)(rect.Y * heightScale);
                        rect.Width = (int)(rect.Width * widthScale);
                        rect.Height = (int)(rect.Height * heightScale);
                    }

                    if (rect.X < 0) rect.X += Width;
                    else if (rect.Y < 0) rect.Y += Height;
                }

                SuspendLayout();

                _titleFont = CommonMethod.ScaleLabelByHeight(item.Title, CommonString.GetUserNormalFont(8F, FontStyle.Bold), new Size(_titleRect.Width, _titleRect.Height), _MaxTitleFontSize);
                _titleText = item.Title;
                _descFont = CommonMethod.ScaleLabelByHeight(item.Desc, CommonString.GetUserNormalFont(8F), new Size(_descRect.Width, _descRect.Height), _MaxDescFontSize);
                _descText = item.Desc;

                var index = guide.Items.IndexOf(item);
                if (guide.ShowSummary)
                    lnkSkip.Text = "跳过".CurrentText() + " [Esc]" + (index > 0 ? $" ({index}/{guide.Items.Count - 1})" : "");
                else
                    lnkSkip.Text = "跳过".CurrentText() + $" [Esc] ({index + 1}/{guide.Items.Count})";

                var arrow = SetGuidePanelPosition(rect);
                UpdateButtonVisibility();

                _currentHighlightRect = rect;
                _currentArrowDirection = arrow;

                ResumeLayout(false);
            }
            finally
            {
                this.EndUpdate();
            }
        }

        // 创建真正一体化的气泡形状，箭头和矩形完美融合
        private GraphicsPath CreateUnifiedBubblePath(Rectangle rect, int cornerRadius, Point arrowBase, Point arrowTip, int arrowSize, ArrowDirection direction)
        {
            GraphicsPath path = new GraphicsPath();

            // 控制圆角大小，确保圆角不会超过矩形尺寸的一半
            cornerRadius = Math.Min(cornerRadius, Math.Min(rect.Width, rect.Height) / 2);
            int diameter = cornerRadius * 2;

            // 定义矩形边缘的四个圆角区域
            Rectangle topLeft = new Rectangle(rect.X, rect.Y, diameter, diameter);
            Rectangle topRight = new Rectangle(rect.X + rect.Width - diameter, rect.Y, diameter, diameter);
            Rectangle bottomLeft = new Rectangle(rect.X, rect.Y + rect.Height - diameter, diameter, diameter);
            Rectangle bottomRight = new Rectangle(rect.X + rect.Width - diameter, rect.Y + rect.Height - diameter, diameter, diameter);

            // 程序优化适用于窄的矩形，确保矩形宽度小于箭头宽度时仍能正确显示
            int minRectWidth = Math.Max(rect.Width, arrowSize * 2 + cornerRadius);

            // 计算箭头的两个基点坐标
            // 这些点拉宽了，确保箭头和矩形的连接处不会有异常
            Point arrowPoint1, arrowPoint2;

            switch (direction)
            {
                // 每个方向分开管理，确保气泡形状的整体连续性
                case ArrowDirection.Up:
                    // 上箭头，箭头在矩形上方
                    arrowPoint1 = new Point(arrowBase.X - arrowSize, arrowBase.Y);
                    arrowPoint2 = new Point(arrowBase.X + arrowSize, arrowBase.Y);

                    // 检查箭头位置是否会与圆角重叠
                    if (arrowBase.X - arrowSize < rect.X + cornerRadius)
                    {
                        // 如果箭头左侧会夹在圆角内，调整坐标
                        arrowPoint1 = new Point(rect.X + cornerRadius, rect.Y);
                    }
                    if (arrowBase.X + arrowSize > rect.X + rect.Width - cornerRadius)
                    {
                        // 如果箭头右侧会夹在圆角内，调整坐标
                        arrowPoint2 = new Point(rect.X + rect.Width - cornerRadius, rect.Y);
                    }

                    // 从左上角开始绘制路径
                    if (arrowBase.X < rect.X + cornerRadius)
                    {
                        // 当箭头在左上角附近时
                        path.AddLine(arrowPoint1, arrowTip);  // 箭头左侧
                        path.AddLine(arrowTip, arrowPoint2);   // 箭头右侧
                    }
                    else if (arrowBase.X > rect.X + rect.Width - cornerRadius)
                    {
                        // 当箭头在右上角附近时
                        path.AddArc(topLeft, 180, 90);      // 左上角圆弧
                        path.AddLine(arrowPoint1, arrowTip);  // 箭头左侧
                        path.AddLine(arrowTip, arrowPoint2);   // 箭头右侧
                    }
                    else
                    {
                        // 箭头在上边中间
                        path.AddArc(topLeft, 180, 90);      // 左上角圆弧
                        path.AddLine(new Point(rect.X + cornerRadius, rect.Y), arrowPoint1);
                        path.AddLine(arrowPoint1, arrowTip);  // 箭头左侧
                        path.AddLine(arrowTip, arrowPoint2);   // 箭头右侧
                        path.AddLine(arrowPoint2, new Point(rect.X + rect.Width - cornerRadius, rect.Y));
                    }

                    path.AddArc(topRight, 270, 90);     // 右上角圆弧
                    path.AddArc(bottomRight, 0, 90);    // 右下角圆弧
                    path.AddArc(bottomLeft, 90, 90);    // 左下角圆弧
                    break;

                case ArrowDirection.Down:
                    // 下箭头，箭头在矩形下方
                    arrowPoint1 = new Point(arrowBase.X - arrowSize, arrowBase.Y);
                    arrowPoint2 = new Point(arrowBase.X + arrowSize, arrowBase.Y);

                    // 检查箭头位置是否会与圆角重叠
                    if (arrowBase.X - arrowSize < rect.X + cornerRadius)
                    {
                        arrowPoint1 = new Point(rect.X + cornerRadius, rect.Y + rect.Height);
                    }
                    if (arrowBase.X + arrowSize > rect.X + rect.Width - cornerRadius)
                    {
                        arrowPoint2 = new Point(rect.X + rect.Width - cornerRadius, rect.Y + rect.Height);
                    }

                    // 从左上角开始绘制路径
                    path.AddArc(topLeft, 180, 90);      // 左上角圆弧
                    path.AddArc(topRight, 270, 90);     // 右上角圆弧
                    path.AddArc(bottomRight, 0, 90);    // 右下角圆弧

                    // 判断箭头位置
                    if (arrowBase.X < rect.X + cornerRadius)
                    {
                        // 如果箭头在左下角附近
                        path.AddArc(bottomLeft, 90, 90);
                    }
                    else if (arrowBase.X > rect.X + rect.Width - cornerRadius)
                    {
                        // 如果箭头在右下角附近
                        path.AddArc(bottomLeft, 90, 90);
                    }
                    else
                    {
                        // 箭头在下边中间
                        path.AddLine(new Point(rect.X + rect.Width - cornerRadius, rect.Y + rect.Height), arrowPoint2);
                        path.AddLine(arrowPoint2, arrowTip);
                        path.AddLine(arrowTip, arrowPoint1);
                        path.AddLine(arrowPoint1, new Point(rect.X + cornerRadius, rect.Y + rect.Height));
                        path.AddArc(bottomLeft, 90, 90);
                    }
                    break;
                default:
                    // 其他情况下，只绘制圆角矩形
                    path.AddArc(topLeft, 180, 90);
                    path.AddArc(topRight, 270, 90);
                    path.AddArc(bottomRight, 0, 90);
                    path.AddArc(bottomLeft, 90, 90);
                    break;
            }

            // 闭合路径并设置填充模式
            path.CloseAllFigures();
            path.FillMode = FillMode.Winding;

            return path;
        }

        // 获取箭头起点位置
        private Point GetArrowBasePoint(Rectangle rect, ArrowDirection arrow)
        {
            switch (arrow)
            {
                case ArrowDirection.Up:
                    return new Point(rect.X + rect.Width / 2, rect.Y);
                case ArrowDirection.Down:
                    return new Point(rect.X + rect.Width / 2, rect.Bottom);
                case ArrowDirection.Left:
                    return new Point(rect.X, rect.Y + rect.Height / 2);
                case ArrowDirection.Right:
                    return new Point(rect.Right, rect.Y + rect.Height / 2);
                default:
                    return new Point(rect.X + rect.Width / 2, rect.Bottom);
            }
        }

        // 获取箭头终点位置
        private Point GetArrowTipPoint(Point basePoint, ArrowDirection arrow, int length)
        {
            switch (arrow)
            {
                case ArrowDirection.Up:
                    return new Point(basePoint.X, basePoint.Y - length);
                case ArrowDirection.Down:
                    return new Point(basePoint.X, basePoint.Y + length);
                case ArrowDirection.Left:
                    return new Point(basePoint.X - length, basePoint.Y);
                case ArrowDirection.Right:
                    return new Point(basePoint.X + length, basePoint.Y);
                default:
                    return new Point(basePoint.X, basePoint.Y + length);
            }
        }

        private GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius * 2, radius * 2, 180, 90);
            path.AddArc(rect.Right - radius * 2, rect.Y, radius * 2, radius * 2, 270, 90);
            path.AddArc(rect.Right - radius * 2, rect.Bottom - radius * 2, radius * 2, radius * 2, 0, 90);
            path.AddArc(rect.X, rect.Bottom - radius * 2, radius * 2, radius * 2, 90, 90);
            path.CloseFigure();
            return path;
        }

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            if (_animationStep >= ANIMATION_STEPS)
            {
                _animationStep = 0;
                _animationTimer.Stop();
                pnlMain.Location = _targetPanelLocation;
                pnlMain.Refresh();
                return;
            }

            float progress = (float)_animationStep / ANIMATION_STEPS;
            float easedProgress = 1 - (float)Math.Pow(1 - progress, 3);

            int newX = (int)(_startPanelLocation.X + ((_targetPanelLocation.X - _startPanelLocation.X) * easedProgress));
            int newY = (int)(_startPanelLocation.Y + ((_targetPanelLocation.Y - _startPanelLocation.Y) * easedProgress));

            pnlMain.Location = new Point(newX, newY);
            _animationStep++;
        }

        private ArrowDirection SetGuidePanelPosition(Rectangle targetArea)
        {
            ArrowDirection arrow;
            // 根据高亮区域的位置自适应调整引导提示框的位置
            int guidePanelWidth = pnlMain.Width;
            int guidePanelHeight = pnlMain.Height;
            int targetControlX = targetArea.X;
            int targetControlY = targetArea.Y;
            int targetControlWidth = targetArea.Width;
            int targetControlHeight = targetArea.Height;
            int screenWidth = Width;
            int screenHeight = Height;

            int guidePanelX, guidePanelY;
            if (targetArea.IsEmpty)
            {
                arrow = ArrowDirection.Down;
                // 在高亮区域下方显示
                guidePanelX = (screenWidth - guidePanelWidth) / 2;
                guidePanelY = (screenHeight - guidePanelHeight) / 2;
            }
            else
            {
                if (targetControlY + targetControlHeight + guidePanelHeight <= screenHeight)
                {
                    arrow = ArrowDirection.Down;
                    // 在高亮区域下方显示
                    guidePanelX = targetControlX + (targetControlWidth - guidePanelWidth) / 2;
                    guidePanelY = targetControlY + targetControlHeight + 20;
                }
                else
                {
                    arrow = ArrowDirection.Up;
                    // 在高亮区域上方显示
                    guidePanelX = targetControlX + (targetControlWidth - guidePanelWidth) / 2;
                    guidePanelY = targetControlY - guidePanelHeight - 20;
                }
            }

            guidePanelX = Math.Max(0, guidePanelX);
            guidePanelY = Math.Max(0, guidePanelY);

            if (guidePanelX + guidePanelWidth > Width)
            {
                guidePanelX = Width - guidePanelWidth - 5;
            }
            if (guidePanelY + guidePanelHeight > Height)
            {
                guidePanelY = Height - guidePanelHeight - 5;
            }

            // 保存目标位置
            _targetPanelLocation = new Point(guidePanelX, guidePanelY);

            // 如果是第一次显示，直接设置位置
            if (pnlMain.Location.IsEmpty)
            {
                pnlMain.Location = _targetPanelLocation;
            }
            else
            {
                // 否则使用动画过渡
                _startPanelLocation = pnlMain.Location;
                _animationStep = 0;
                _animationTimer.Start();
            }

            return arrow;
        }

        private void UpdateButtonVisibility()
        {
            btnPrev.Visible = (_currentGuideIndex > 0);
            btnNext.Visible = (_currentGuideIndex <= guide.Items.Count - 1);
            lnkSkip.Visible = guide.Items.Count > 1; // Show skip if more than one item
            btnNext.Text = (_currentGuideIndex == guide.Items.Count - 1) ? "完成".CurrentText() : "下一步".CurrentText();
        }

        private Control FindControl(Control.ControlCollection controls, string controlName)
        {
            var control = controls.Find(controlName, true).FirstOrDefault();
            if (control != null) return control;

            control = controls.OfType<Control>()
                .FirstOrDefault(p => Equals(p.AccessibleDescription, controlName) || Equals(p.AccessibleDefaultActionDescription, controlName) || Equals(p.Tag?.ToString(), controlName));
            if (control != null) return control;

            return null;
        }

        private Rectangle FindSingleControlRect(string controlName)
        {
            var rect = Rectangle.Empty;
            var strParam = string.Empty;
            if (controlName.Contains("&"))
            {
                strParam = CommonMethod.SubString(controlName, "&");
                controlName = CommonMethod.SubString(controlName, "", "&");
            }
            var lstNames = controlName.Split(new string[] { "." }, StringSplitOptions.RemoveEmptyEntries);
            if (lstNames.Length > 1)
            {
                controlName = lstNames[0];
            }

            var ctrl = FindControl(_mainForm.Controls, controlName);
            if (ctrl != null)
            {
                rect = ctrl.Bounds;
                if (lstNames.Length > 1)
                {
                    controlName = lstNames[1];
                    if (ctrl != null)
                    {
                        if (ctrl is ToolStrip toolStrip)
                        {
                            ToolStripItem item = toolStrip.Items[controlName];
                            if (item != null)
                            {
                                rect = _mainForm.RectangleToClient(item.Owner.RectangleToScreen(item.Bounds));
                            }
                        }
                        else if (ctrl is TabControl tabControl)
                        {
                            for (int i = 0; i < tabControl.TabCount; i++)
                            {
                                if (Equals(tabControl.TabPages[i].Name, controlName)
                                    || Equals(tabControl.TabPages[i].AccessibleDescription, controlName)
                                    || Equals(tabControl.TabPages[i].AccessibleDefaultActionDescription, controlName))
                                {
                                    if (lstNames.Length > 2)
                                    {
                                        var newCtrl = FindControl(tabControl.TabPages[i].Controls, lstNames[2]);
                                        if (newCtrl != null)
                                        {
                                            rect = _mainForm.RectangleToClient(tabControl.TabPages[i].RectangleToScreen(newCtrl.Bounds));
                                        }
                                    }
                                    else
                                    {
                                        rect = _mainForm.RectangleToClient(tabControl.RectangleToScreen(tabControl.GetTabRect(i)));
                                    }
                                }
                            }
                        }
                    }
                }
                if (ctrl.Parent != null && !Equals(ctrl.Parent, _mainForm))
                {
                    rect = _mainForm.RectangleToClient(ctrl.Parent.RectangleToScreen(rect));
                }
            }

            // 根据规则处理结果区域
            if (!string.IsNullOrEmpty(strParam))
            {
                //如 strParam = Top_30，含义为解析到区域之后，只保留顶部的30%
                //如 strParam = Left_30，含义为解析到区域之后，只保留左边的30%
                //如 strParam = Right_30，含义为解析到区域之后，只保留右边的30%
                //如 strParam = Bottom_30，含义为解析到区域之后，只保留顶部的30%
                //如 strParam = Center_20，含义为解析到区域之后，只保留中间的20%
                if (strParam.Contains("_"))
                {
                    string[] parts = strParam.Split('_');
                    if (parts.Length == 2 && int.TryParse(parts[1], out int percentage))
                    {
                        switch (parts[0].ToLower())
                        {
                            case "top":
                                // 只保留顶部指定百分比
                                rect.Height = (int)(rect.Height * percentage / 100.0);
                                break;

                            case "bottom":
                                // 只保留底部指定百分比
                                int bottomHeight = (int)(rect.Height * percentage / 100.0);
                                rect.Y = rect.Bottom - bottomHeight;
                                rect.Height = bottomHeight;
                                break;

                            case "left":
                                // 只保留左侧指定百分比
                                rect.Width = (int)(rect.Width * percentage / 100.0);
                                break;

                            case "right":
                                // 只保留右侧指定百分比
                                int rightWidth = (int)(rect.Width * percentage / 100.0);
                                rect.X = rect.Right - rightWidth;
                                rect.Width = rightWidth;
                                break;

                            case "center":
                                // 只保留中间指定百分比
                                int centerWidth = (int)(rect.Width * percentage / 100.0);
                                int centerHeight = (int)(rect.Height * percentage / 100.0);
                                rect.X = rect.X + (rect.Width - centerWidth) / 2;
                                rect.Y = rect.Y + (rect.Height - centerHeight) / 2;
                                rect.Width = centerWidth;
                                rect.Height = centerHeight;
                                break;
                        }
                    }
                }

            }
            return rect;
        }

        private Rectangle FindTargetControlRectangle(string controlName)
        {
            List<Rectangle> rects = new List<Rectangle>();
            var lstNames = controlName.Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var item in lstNames)
            {
                var rect = FindSingleControlRect(item);
                if (!rect.IsEmpty)
                    rects.Add(rect);
            }
            return rects.Count <= 0 ? Rectangle.Empty : rects.Aggregate(rects.First(), (current, rect) => Rectangle.Union(current, rect));
        }

        private void CloseGuide()
        {
            _currentGuideIndex = -1;
            Controls.Clear();

            if (guide.Items != null && guide.Items.Count > 0 && !string.IsNullOrEmpty(guide.Items[0].Exec))
                _mainForm.ExecuteScript(guide.Items[0].Exec);

            this.Close();
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            // Do nothing here to support transparency and custom painting.
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            try
            {
                e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
                e.Graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                e.Graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;
                e.Graphics.CompositingQuality = CompositingQuality.HighQuality;

                if (_backgroundImage != null)
                {
                    e.Graphics.CompositingMode = CompositingMode.SourceOver;
                    e.Graphics.DrawImage(_backgroundImage, 0, 0, this.Width, this.Height);

                    using (SolidBrush maskBrush = new SolidBrush(_themeManager.MaskColor))
                    {
                        if (_currentHighlightRect.IsEmpty)
                        {
                            e.Graphics.FillRectangle(maskBrush, ClientRectangle);
                        }
                        else
                        {
                            Rectangle expandedRect = new Rectangle(
                                _currentHighlightRect.X - 2, _currentHighlightRect.Y - 2,
                                _currentHighlightRect.Width + 4, _currentHighlightRect.Height + 4);

                            GraphicsPath highlightPath = CreateUnifiedBubblePath(
                                expandedRect, 8, // 圆角半径调小一点，与效果图接近
                                GetArrowBasePoint(expandedRect, _currentArrowDirection),
                                GetArrowTipPoint(GetArrowBasePoint(expandedRect, _currentArrowDirection),
                                                _currentArrowDirection, 12),
                                8, _currentArrowDirection);

                            // 创建遮罩区域（除了高亮区域外的全部区域）
                            using (Region formRegion = new Region(ClientRectangle))
                            {
                                formRegion.Exclude(highlightPath);
                                e.Graphics.FillRegion(maskBrush, formRegion);
                            }

                            // 正常模式下，添加边缘增亮效果，更加清晰
                            if (!CommonSetting.夜间模式)
                            {
                                using (Pen innerLightPen = new Pen(Color.FromArgb(255, 255, 255, 255), 1.0f))
                                {
                                    innerLightPen.LineJoin = LineJoin.Round;
                                    e.Graphics.DrawPath(innerLightPen, highlightPath);
                                }
                            }

                            // 暗黑模式下不做额外处理，保持高亮区域原样
                            // 添加高亮区域边框 - 对于亮色模式用纯白，更加清晰相划
                            using (Pen borderPen = new Pen(_themeManager.BubbleBorderColor, _themeManager.BorderWidth))
                            {
                                borderPen.LineJoin = LineJoin.Round;
                                e.Graphics.DrawPath(borderPen, highlightPath);
                            }
                            highlightPath.Dispose();
                        }
                    }
                }
                base.OnPaint(e);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"OnPaint 异常: {ex.Message}");
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (_backgroundImage != null)
                {
                    _backgroundImage.Dispose();
                    _backgroundImage = null;
                }
                if (_animationTimer != null)
                {
                    _animationTimer.Stop();
                    _animationTimer.Dispose();
                    _animationTimer = null;
                }
                // Dispose panel and its controls if not done automatically
                if (pnlMain != null)
                {
                    pnlMain.Dispose();
                    pnlMain = null;
                }
            }
            base.Dispose(disposing);
        }
    }

    /// <summary>
    /// 集中管理主题色彩和样式
    /// </summary>
    internal class GuideThemeManager
    {
        public Color[] PanelBlendColors { get; private set; }
        public float[] PanelBlendPositions { get; private set; }

        // 边框与高光
        public Color InnerBorderColor { get; private set; }
        public Color TopLineColor { get; private set; }

        // 遮罩与高亮区域
        public Color MaskColor { get; private set; }
        public Color BubbleBorderColor { get; private set; }
        public float BorderWidth { get; private set; }

        // 太阳/月亮效果颜色
        public Color CoreCenterColor { get; private set; }
        public Color CoreSurroundColor { get; private set; }
        public Color OuterCenterBaseColor { get; private set; }
        public Color OuterSurroundColor { get; private set; }
        public Color WideGlowCenterColor { get; private set; }
        public Color FlareBase1 { get; private set; }
        public Color FlareBase2 { get; private set; }
        public Color FlareBase3 { get; private set; }
        public float GlowRadiusFactor { get; private set; }

        // 文本颜色
        public Color TitleTextColor { get; private set; }
        public Color DescTextColor { get; private set; }

        // 按钮颜色
        public ButtonStyle PrevButtonStyle { get; private set; }
        public ButtonStyle NextButtonStyle { get; private set; }
        public LinkStyle SkipLinkStyle { get; private set; }

        public class ButtonStyle
        {
            public bool IsRound { get; set; }
            public FlatStyle Style { get; set; }
            public int BorderSize { get; set; }
            public Color BackColor { get; set; }
            public Color ForeColor { get; set; }
            public Color HoverColor { get; set; }
            public Color ClickColor { get; set; }
        }

        public class LinkStyle
        {
            public LinkBehavior Behavior { get; set; }
            public Color ForeColor { get; set; }
            public Color LinkColor { get; set; }
            public Color ActiveLinkColor { get; set; }
        }

        // 初始化主题管理器，根据当前模式设置所有相关样式
        public static GuideThemeManager Create(bool isDarkMode)
        {
            var manager = new GuideThemeManager();

            if (isDarkMode) // 夜间模式
            {
                // 面板背景
                var PanelBaseColor = Color.FromArgb(255, 20, 25, 55);
                var PanelGradientEndColor = Color.FromArgb(255, 35, 70, 190); // 较亮深夜天空蓝

                // 渐变色配置
                manager.PanelBlendColors = new Color[] {
                        PanelBaseColor,
                        Color.FromArgb(255, (PanelBaseColor.R + PanelGradientEndColor.R) / 2,
                                          (PanelBaseColor.G + PanelGradientEndColor.G) / 2,
                                          (PanelBaseColor.B + PanelGradientEndColor.B) / 2),
                        PanelGradientEndColor
                    };
                manager.PanelBlendPositions = new float[] { 0.0f, 0.3f, 1.0f };

                // 边框与高光
                manager.InnerBorderColor = Color.FromArgb(25, 200, 210, 230); // 低透明度淡蓝白
                manager.TopLineColor = Color.FromArgb(20, 210, 220, 240); // 低透明度淡蓝白

                // 遮罩与高亮区域
                manager.MaskColor = Color.FromArgb(220, 10, 10, 20); // 较深微蓝遮罩
                manager.BubbleBorderColor = Color.FromArgb(255, 0, 122, 204); // 科技蓝边框
                manager.BorderWidth = 1.5f; // 稍厚以增强可见性

                // 月亮效果颜色
                manager.CoreCenterColor = Color.Yellow;// Color.FromArgb(255, 255, 250, 180); // 黄色核心
                manager.CoreSurroundColor = Color.FromArgb(170, 255, 250, 170); // 黄色包围
                manager.OuterCenterBaseColor = Color.FromArgb(255, 200, 210, 230);
                manager.OuterSurroundColor = Color.FromArgb(0, 180, 190, 220);
                manager.WideGlowCenterColor = Color.FromArgb(50, 255, 245, 150); // 柔和黄晕
                manager.FlareBase1 = Color.FromArgb(180, 215, 255, 240);
                manager.FlareBase2 = Color.Yellow;// Color.FromArgb(255, 240, 180, 230);
                manager.FlareBase3 = Color.FromArgb(255, 200, 120, 220);
                manager.GlowRadiusFactor = 0.70f; // 夜间模式的月晕半径因子

                // 文本颜色
                manager.TitleTextColor = Color.White;
                manager.DescTextColor = Color.FromArgb(255, 230, 230, 230);

                // 链接样式
                manager.SkipLinkStyle = new LinkStyle
                {
                    Behavior = LinkBehavior.NeverUnderline,
                    ForeColor = Color.FromArgb(255, 130, 150, 180),
                    LinkColor = Color.FromArgb(255, 130, 150, 180),
                    ActiveLinkColor = Color.FromArgb(255, 160, 180, 210)
                };

                // 按钮样式 - 前一步
                manager.PrevButtonStyle = new ButtonStyle
                {
                    IsRound = true,
                    Style = FlatStyle.Flat,
                    BorderSize = 0,
                    BackColor = Color.FromArgb(255, 70, 90, 135),
                    ForeColor = Color.FromArgb(255, 220, 230, 245),
                    HoverColor = Color.FromArgb(255, 85, 105, 150),
                    ClickColor = Color.FromArgb(255, 60, 80, 125)
                };

                // 按钮样式 - 下一步
                manager.NextButtonStyle = new ButtonStyle
                {
                    IsRound = true,
                    Style = FlatStyle.Flat,
                    BorderSize = 0,
                    BackColor = Color.FromArgb(255, 0, 60, 145),
                    ForeColor = Color.FromArgb(255, 210, 215, 220),
                    HoverColor = Color.FromArgb(255, 0, 80, 165),
                    ClickColor = Color.FromArgb(255, 0, 45, 125)
                };
            }
            else // 日间模式
            {
                // 渐变色配置 - 调整为更饱和的蓝色系列
                manager.PanelBlendColors = new Color[] {
                        Color.FromArgb(255, 25, 105, 230),   // 左上角深蓝
                        Color.FromArgb(255, 30, 110, 235),   // 左下角中蓝
                        Color.FromArgb(255, 35, 115, 238),   // 右上角中蓝
                        Color.FromArgb(255, 38, 118, 240)    // 右下角浅蓝
                    };
                manager.PanelBlendPositions = new float[] { 0.0f, 0.4f, 0.6f, 1.0f };

                // 边框与高光
                manager.InnerBorderColor = Color.FromArgb(70, 255, 255, 255);
                manager.TopLineColor = Color.FromArgb(50, 255, 255, 255);

                // 遮罩与高亮区域
                manager.MaskColor = Color.FromArgb(100, 0, 0, 0); // 将透明度从190降低到100
                manager.BubbleBorderColor = Color.White;
                manager.BorderWidth = 1.0f;

                // 太阳效果颜色
                manager.CoreCenterColor = Color.FromArgb(255, 248, 220, 80);
                manager.CoreSurroundColor = Color.FromArgb(80, 255, 255, 60);
                manager.OuterCenterBaseColor = Color.FromArgb(255, 255, 240, 80);
                manager.OuterSurroundColor = Color.FromArgb(0, 255, 240, 120);
                manager.WideGlowCenterColor = Color.FromArgb(15, 255, 220, 100); // 顺滑更弥散的太阳晕 (Alpha 15)
                manager.FlareBase1 = Color.FromArgb(255, 220, 200);
                manager.FlareBase2 = Color.FromArgb(180, 220, 255);
                manager.FlareBase3 = Color.FromArgb(255, 240, 200);
                manager.GlowRadiusFactor = 1.1f; // 日间模式的太阳晕极大扩展因子

                // 文本颜色
                manager.TitleTextColor = Color.White;
                manager.DescTextColor = Color.White;

                // 链接样式
                manager.SkipLinkStyle = new LinkStyle
                {
                    Behavior = LinkBehavior.NeverUnderline,
                    ForeColor = Color.FromArgb(255, 165, 205, 253),
                    LinkColor = Color.FromArgb(255, 165, 205, 253),
                    ActiveLinkColor = Color.DarkBlue
                };

                // 按钮样式 - 前一步
                manager.PrevButtonStyle = new ButtonStyle
                {
                    Style = FlatStyle.Standard,
                    BorderSize = 1,
                    BackColor = Color.FromArgb(255, 0, 92, 230),
                    ForeColor = Color.White,
                    HoverColor = Color.FromArgb(255, 0, 102, 240),
                    ClickColor = Color.FromArgb(255, 0, 82, 220)
                };

                // 按钮样式 - 下一步
                manager.NextButtonStyle = new ButtonStyle
                {
                    Style = FlatStyle.Standard,
                    BorderSize = 1,
                    BackColor = Color.White,
                    ForeColor = Color.FromArgb(255, 0, 95, 235),
                    HoverColor = Color.FromArgb(255, 240, 240, 240),
                    ClickColor = Color.FromArgb(255, 230, 230, 230)
                };
            }

            return manager;
        }
    }
}
