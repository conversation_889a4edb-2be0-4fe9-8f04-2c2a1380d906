using System;
using System.Windows.Forms;

namespace OCRTools.Common.Hook
{
    /// <summary>
    ///     This class monitors all mouse activities globally (also outside of the application)
    ///     and provides appropriate events.
    /// </summary>
    public static partial class HookManager
    {
        //################################################################

        #region Mouse events

        ///// <summary>
        ///// Occurs when the mouse pointer is moved. 
        ///// </summary>
        //public static event MouseEventHandler MouseMove;

        ///// <summary>
        ///// Occurs when a click was performed by the mouse. 
        ///// </summary>
        //public static event MouseEventHandler MouseClick;

        ///// <summary>
        ///// Occurs when the mouse a mouse button is pressed. 
        ///// </summary>
        //public static event MouseEventHandler MouseDown;

        /// <summary>
        ///     Occurs when a mouse button is released.
        /// </summary>
        public static event MouseEventHandler MouseUp;

        /// <summary>
        ///     click+move+up
        /// </summary>
        public static event <PERSON><PERSON><PERSON><PERSON>and<PERSON> MouseSelected;

        ///// <summary>
        ///// Occurs when the mouse wheel moves. 
        ///// </summary>
        //public static event MouseEventHandler MouseWheel;

        private static event MouseEventHandler s_MouseDoubleClick;

        //The double click event will not be provided directly from hook.
        //To fire the double click event wee need to monitor mouse up event and when it occures 
        //Two times during the time interval which is defined in Windows as a doble click time
        //we fire this event.

        /// <summary>
        ///     Occurs when a double clicked was performed by the mouse.
        /// </summary>
        public static event MouseEventHandler MouseDoubleClick
        {
            add
            {
                if (s_MouseDoubleClick == null)
                {
                    //We create a timer to monitor interval between two clicks
                    s_DoubleClickTimer = new Timer
                    {
                        //This interval will be set to the value we retrive from windows. This is a windows setting from contro planel.
                        Interval = GetDoubleClickTime(),
                        //We do not start timer yet. It will be start when the click occures.
                        Enabled = false
                    };
                    //We define the callback function for the timer
                    s_DoubleClickTimer.Tick += DoubleClickTimeElapsed;
                    //We start to monitor mouse up event.
                    MouseUp += OnMouseUp;
                }

                s_MouseDoubleClick += value;
            }
            remove
            {
                if (s_MouseDoubleClick != null)
                {
                    s_MouseDoubleClick -= value;
                    if (s_MouseDoubleClick == null)
                    {
                        //Stop monitoring mouse up
                        MouseUp -= OnMouseUp;
                        //Dispose the timer
                        s_DoubleClickTimer.Tick -= DoubleClickTimeElapsed;
                        s_DoubleClickTimer = null;
                    }
                }
            }
        }

        //This field remembers mouse button pressed because in addition to the short interval it must be also the same button.
        private static MouseButtons s_PrevClickedButton;

        //The timer to monitor time interval between two clicks.
        private static Timer s_DoubleClickTimer;

        private static void DoubleClickTimeElapsed(object sender, EventArgs e)
        {
            //Timer is alapsed and no second click ocuured
            s_DoubleClickTimer.Enabled = false;
            s_PrevClickedButton = MouseButtons.None;
        }

        /// <summary>
        ///     This method is designed to monitor mouse clicks in order to fire a double click event if interval between
        ///     clicks was short enaugh.
        /// </summary>
        /// <param name="sender">Is always null</param>
        /// <param name="e">Some information about click heppened.</param>
        private static void OnMouseUp(object sender, MouseEventArgs e)
        {
            //This should not heppen
            if (e.Clicks < 1) return;
            //If the secon click heppened on the same button
            if (e.Button.Equals(s_PrevClickedButton))
            {
                Console.WriteLine("MouseDoubleClick");
                //Fire double click
                s_MouseDoubleClick?.Invoke(null, e);
                //Stop timer
                s_DoubleClickTimer.Enabled = false;
                s_PrevClickedButton = MouseButtons.None;
            }
            else
            {
                //If it was the firts click start the timer
                s_DoubleClickTimer.Enabled = true;
                s_PrevClickedButton = e.Button;
            }
        }

        #endregion

        //################################################################

        #region Keyboard events

        /// <summary>
        ///     Occurs when a key is pressed.
        /// </summary>
        /// <remarks>
        ///     Key events occur in the following order:
        ///     <list type="number">
        ///         <item>KeyDown</item>
        ///         <item>KeyPress</item>
        ///         <item>KeyUp</item>
        ///     </list>
        ///     The KeyPress event is not raised by noncharacter keys; however, the noncharacter keys do raise the KeyDown and
        ///     KeyUp events.
        ///     Use the KeyChar property to sample keystrokes at run time and to consume or modify a subset of common keystrokes.
        ///     To handle keyboard events only in your application and not enable other applications to receive keyboard events,
        ///     set the KeyPressEventArgs.Handled property in your form's KeyPress event-handling method to <b>true</b>.
        /// </remarks>
        public static event KeyPressEventHandler KeyPress;

        /// <summary>
        ///     Occurs when a key is released.
        /// </summary>
        public static event KeyEventHandler KeyUp;

        /// <summary>
        ///     Occurs when a key is preseed.
        /// </summary>
        public static event KeyEventHandler KeyDown;

        #endregion
    }
}