﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Automation;

namespace OCRTools.Common
{
    public class CommonAutomation
    {
        public static List<WindowInfo> InitChildHandle(WindowInfo window, TreeScope scope)
        {
            var lstWindows = new List<WindowInfo>();

            if (!StaticValue.IsCatchScreen)
                return lstWindows;

            try
            {
                // 优化1：使用HashSet提高去重性能
                var existingRectangles = new HashSet<Rectangle>();

                // 优化2：设置超时避免长时间阻塞
                var element = AutomationElement.FromHandle(window.Handle);
                if (element == null) return lstWindows;

                // 保持原始查询条件，只检查是否屏幕外
                var condition = new PropertyCondition(AutomationElement.IsOffscreenProperty, false);

                var foundElements = element.FindAll(scope, condition);
                if (foundElements == null || !StaticValue.IsCatchScreen) return lstWindows;

                // 优化4：预分配容量
                if (foundElements.Count > 0)
                {
                    lstWindows.Capacity = foundElements.Count;
                }

                foreach (AutomationElement item in foundElements)
                {
                    if (!StaticValue.IsCatchScreen)
                        break;

                    try
                    {
                        var boundingRect = item.Current.BoundingRectangle;

                        // 优化5：避免无效矩形的进一步处理
                        if (boundingRect.IsEmpty || boundingRect.Width <= 0 || boundingRect.Height <= 0)
                            continue;

                        var rect = new Rectangle(
                            (int)boundingRect.X,
                            (int)boundingRect.Y,
                            (int)boundingRect.Width,
                            (int)boundingRect.Height);

                        // 优化6：使用HashSet快速去重
                        if (rect.IsValid() && existingRectangles.Add(rect))
                        {
                            var nativeHandle = item.Current.NativeWindowHandle;
                            if (nativeHandle != 0) // 确保句柄有效
                            {
                                lstWindows.Add(new WindowInfo
                                {
                                    IsSmallControl = true,
                                    ParentHandle = window.Handle,
                                    Handle = new IntPtr(nativeHandle),
                                    Rectangle = rect,
                                    ZIndex = window.ZIndex
                                });
                            }
                        }
                    }
                    catch (ElementNotAvailableException)
                    {
                        // 元素已不可用，跳过
                        continue;
                    }
                    catch (InvalidOperationException)
                    {
                        // 操作无效，跳过
                        continue;
                    }
                }
            }
            catch (ElementNotAvailableException)
            {
                // 父元素不可用
                System.Diagnostics.Debug.WriteLine($"父窗口不可用: {window.Handle}");
            }
            catch (Exception ex)
            {
                // 记录其他异常但不中断流程
                System.Diagnostics.Debug.WriteLine($"获取子控件时出错: {ex.Message}");
            }

            return lstWindows;
        }
    }
}
