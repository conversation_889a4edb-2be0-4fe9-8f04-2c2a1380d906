﻿using MetroFramework.Forms;
using MetroFramework.Native;
using OCRTools.Common;
using OCRTools.Properties;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Cache;
using System.Reflection;
using System.Security;
using System.Security.Principal;
using System.Web.Script.Serialization;
using System.Windows.Forms;

// ReSharper disable All

namespace OCRTools
{
    public class CommonString
    {
        public const string StrDefaultDesc = "请按下快捷键";
        public const string StrDefaultImgType = "png";
        public const string StrDefaultTxtType = "txt";

        public static string StrDefaultFontName = "微软雅黑";

        public static void SetScaleModel()
        {
            CommonAutoScaleMode = CommonSetting.跟随系统DPI缩放 ? AutoScaleMode.Font : AutoScaleMode.Dpi;
            CommonSetting.默认文字字体 = CommonString.ConvertFont(CommonSetting.默认文字字体);
            CommonSetting.贴图文字字体 = CommonString.ConvertFont(CommonSetting.贴图文字字体);
        }

        public static AutoScaleMode CommonAutoScaleMode = AutoScaleMode.Dpi;

        public static GraphicsUnit CommonGraphicsUnit() { return CommonAutoScaleMode == AutoScaleMode.Dpi ? GraphicsUnit.Pixel : GraphicsUnit.Point; }

        public static Font GetSysNormalFont(float size, bool isConvertSize = true)
        {
            return GetFont(StrDefaultFontName, size, FontStyle.Regular, CommonGraphicsUnit(), isConvertSize);
        }

        public static Font GetSysBoldFont(float size, bool isConvertSize = true)
        {
            return GetFont(StrDefaultFontName, size, FontStyle.Bold, CommonGraphicsUnit(), isConvertSize);
        }

        public static Font GetUserNormalFont(float size)
        {
            return GetFont(CommonSetting.默认文字字体.FontFamily.Name, size, FontStyle.Regular, CommonGraphicsUnit(), false);
        }

        public static Font ConvertFont(Font font)
        {
            return GetFont(font.FontFamily.Name, font.Size, font.Style, CommonGraphicsUnit(), false);
        }

        public static Font GetFont(string fontName, float size, FontStyle style = FontStyle.Regular, GraphicsUnit unit = GraphicsUnit.Pixel, bool isConvertSize = true)
        {
            var newSize = !isConvertSize || unit == GraphicsUnit.Pixel ? size : size / 1.333f;
            return new Font(fontName, newSize, style, unit);
        }

        public static bool IsAutoLogin { get; set; } = true;

        ////连接超时  
        //public const int INTERNET_OPTION_CONNECT_TIMEOUT = 2;
        //public const int INTERNET_OPTION_CONNECT_RETRIES = 3;
        ////送信超时时间  
        //public const int INTERNET_OPTION_SEND_TIMEOUT = 5;
        ////受信超时时间  
        //public const int INTERNET_OPTION_RECEIVE_TIMEOUT = 6;
        //public const int INTERNET_OPEN_TYPE_PRECONFIG = 0;//使用 IE 中的连接设置
        //public const int INTERNET_OPEN_TYPE_DIRECT = 1;//直接连接到服务器
        //public const int INTERNET_OPEN_TYPE_PROXY = 3;//通过代理服务器进行连接
        //public const int INTERNET_OPTION_MAX_CONNS_PER_SERVER = 73;//最大连接数
        //public const int INTERNET_OPTION_MAX_CONNS_PER_1_0_SERVER = 74;//最大连接数

        /// <summary>
        /// 今日已识别次数
        /// </summary>
        public static long TodayCount { get; set; }

        /// <summary>
        /// 识别限额
        /// </summary>
        public static long LimitCount { get; set; }

        public const string StrServerHost = "ocr.oldfish.cn";
        public const string StrServerHostUrl = "https://" + StrServerHost + "/";

        public static string DataPath =
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + "\\OCR\\";

        public static string DefaultRecPath = string.Format("{0}Rec\\", DataPath);

        public static string DefaultDLLPath = string.Format("{0}Widget\\", DataPath);

        public static string DefaultImagePath = string.Format("{0}Image\\", DataPath);

        public static string HeadImagePath = string.Format("{0}Head\\", DataPath);

        public static string DefaultTmpPath = string.Format("{0}Tmp\\", DataPath);

        public static string DefaultLocalRecPath = string.Format("{0}OcrService\\", DataPath);

        public static string DefaultLocalRecModelsPath = string.Format("{0}Models\\", DefaultLocalRecPath);

        public static string DefaultLocalRecExePath = string.Format("{0}OcrMain.exe", DefaultLocalRecPath);

        public static string DefaultLocalDectExePath = string.Format("{0}deskew32.exe", DefaultDLLPath);
        public static string DectServiceUpdateFileUrl => HostUpdate?.FullUrl + "update/deskew32.exe";

        public static MetroFormShadowType CommonShadowType;

        public static StringFormat BaseStringFormat =
            new StringFormat(StringFormatFlags.MeasureTrailingSpaces |
                             StringFormatFlags.LineLimit); //StringFormatFlags.NoWrap |

        public static TextFormatFlags BaseTextFormatFlags =
            TextFormatFlags.NoPadding | TextFormatFlags.TextBoxControl | TextFormatFlags.SingleLine |
            TextFormatFlags.NoPrefix;

        public static Point PointZero = new Point(0, 0);

        public static Padding PaddingZero = Padding.Empty;

        //public static double NowFontLineHeight = 60;

        public static JavaScriptSerializer JavaScriptSerializer = new JavaScriptSerializer();

        public static List<string> LstCanProcessFilesExt = new List<string>
            {"png", "jpg", "jpeg", "bmp", "gif", "pdf", "doc", "docx", "txt"}; //, "xls", "xlsx", "ppt", "pptx"

        public static List<string> LstCanProcessImageFilesExt = new List<string> { "png", "jpg", "jpeg", "bmp", "gif" };

        public static List<string>
            LstCanProcessDocFilesExt = new List<string> { "pdf", "doc", "docx", "txt" }; //, "xls", "xlsx", "ppt", "pptx"

        public static bool IsOnLine = true;

        public static Random RndTmp = new Random();

        private static readonly List<string> LstRndHost = new List<string>
            {"sina.cn", "aliyun.com", "baidu.com", "qq.com", "360.cn", "bing.cn"};

        private static List<string> lstNotFindDll = new List<string>()
            {"Could not load file or assembly", "未能加载文件或程序集"};

        public const string StrReminder = "温馨提示";

        private static Dictionary<string, byte[]> dicAssemblyDictionary = new Dictionary<string, byte[]>();

        static CommonString()
        {
            //CommonShadowType = MetroFormShadowType.SystemShadow;
            InitShadow();
            try
            {
                InitPath();
            }
            catch
            {
            }
        }

        public static bool IsBeta { get; set; }

        public static bool IsAdministrator { get; set; }

        public static string StrCommonEncryptKey => "!(*_^%$#";

        public static WebInfo HostAccount { get; set; } = new WebInfo() { IsDefault = true, Host = "ocr.oldfish.cn:2020" };

        public static WebInfo HostUpdate = new WebInfo() { Host = "cdn.oldfish.cn" };

        public static WebInfo HostCode { get; set; } = new WebInfo() { IsDefault = true, Host = "ocr.oldfish.cn:2020" };

        public static int IsSelfHost(string host)
        {
            return string.IsNullOrEmpty(host) ? 0 : (HostAccount?.Host?.Contains(host) == true ? 1 : (HostCode?.Host?.Contains(host) == true ? 2 : 0));
        }

        //public static string strApplicationPath => Application.StartupPath.TrimEnd('\\');

        public static DateTime DtNowDate { get; set; }
        public static string StrNowVersion { get; set; }

        private static string productName;
        public static string ProductName
        {
            get
            {
                if (string.IsNullOrEmpty(productName))
                {
                    try
                    {
                        productName = Application.ProductName;
                    }
                    catch { }
                }
                return productName;
            }
            set
            {
                productName = value;
            }
        }

        private static string fullName;
        public static string FullName
        {
            get
            {
                if (string.IsNullOrEmpty(fullName))
                {
                    try
                    {
                        fullName = Application.ProductName;
                    }
                    catch { }
                }
                return fullName;
            }
            set
            {
                fullName = value;
            }
        }

        //public static bool Is64 = Environment.Is64BitOperatingSystem;

        public static bool IsExit { get; set; }
        public static string StrBadSoft { get; set; }
        public static string StrBadSoftDesc { get; set; }

        //public static readonly Semaphore OcrMutex = new Semaphore(1, 1);
        //public static readonly Mutex OcrMutex = new Mutex(true, "Ocr");

        public static bool IsOnRec { get; set; }

        /// <summary>
        /// 鼠标是否正在划词翻译按钮上
        /// </summary>
        public static bool IsOnOcrButton { get; set; }

        /// <summary>
        /// 鼠标是否正在划词翻译弹出框中
        /// </summary>
        public static bool IsOnOcrSearch { get; set; }

        public static string UpdateFileName => "update.xml";

        public static string BetaUpdateFileName => "updateBeta.xml";

        public static string UpdateFileUrl => HostUpdate?.FullUrl + "update/" + UpdateFileName;

        public static string BetaUpdateFileUrl => HostUpdate?.FullUrl + "update/" + BetaUpdateFileName;

        public static string GetBetaUrl(string oldUrl)
        {
            return oldUrl.Substring(0, oldUrl.LastIndexOf('.')) + "Beta" + oldUrl.Substring(oldUrl.LastIndexOf('.'));
        }

        public static string OcrServiceUpdateFileName => "ocr.xml";

        public static string OcrServiceUpdateFileUrl => HostUpdate?.FullUrl + "update/" + OcrServiceUpdateFileName;

        public static string BrowserPath { get; set; } = "";

        private static string strOcrAgent;

        public const string StrDefaultAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Safari/537.36";

        public static Uri SetAddress(Uri address, int selfHost)
        {
            var strIp = selfHost == 1 ? HostAccount?.Ip : (selfHost == 2 ? HostCode?.Ip : "");
            if (!string.IsNullOrEmpty(strIp))
                address = new Uri(address.Scheme + "://" + strIp + (address.Port != 80 ? ":" + address.Port : "") + address.PathAndQuery, false);
            return address;
        }

        public static void SetUserAgent(HttpWebRequest request)
        {
            if (string.IsNullOrEmpty(strOcrAgent))
            {
                strOcrAgent = string.Format("OcrAgent/{0}", StrNowVersion);
            }
            request.UserAgent = strOcrAgent;
        }

        public static void InitPath()
        {
            if (!Directory.Exists(DataPath)) Directory.CreateDirectory(DataPath);
            if (!Directory.Exists(DefaultImagePath)) Directory.CreateDirectory(DefaultImagePath);
            if (!Directory.Exists(DefaultTmpPath)) Directory.CreateDirectory(DefaultTmpPath);
            if (!Directory.Exists(HeadImagePath)) Directory.CreateDirectory(HeadImagePath);
            if (!Directory.Exists(DefaultDLLPath)) Directory.CreateDirectory(DefaultDLLPath);
        }

        private static void InitShadow()
        {
            try
            {
                if (Environment.OSVersion.Version.Major <= 5)
                    CommonShadowType = MetroFormShadowType.Flat;
                else if (IsAeroThemeEnabled() && IsDropShadowSupported())
                    CommonShadowType = MetroFormShadowType.AeroShadow;
                else
                    CommonShadowType = MetroFormShadowType.DropShadow;
            }
            catch
            {
            }
        }

        [SecuritySafeCritical]
        private static bool IsAeroThemeEnabled()
        {
            DwmApi.DwmIsCompositionEnabled(out var pfEnabled);
            return pfEnabled;
        }

        private static bool IsDropShadowSupported()
        {
            return SystemInformation.IsDropShadowEnabled;
        }

        public static void InitRuntimeSetttings()
        {
            IsAdministrator = IsAdmini();
            //CommonString.InitCurl();
            try
            {
                //CommonMethod.CheckFrameWork();
                ServicePointManager.ServerCertificateValidationCallback =
                    (sender, certificate, chain, sslPolicyErrors) => true;
                //ServicePointManager.Expect100Continue = false;
                ServicePointManager.DefaultConnectionLimit = int.MaxValue;
                //if (ServicePointManager.DefaultConnectionLimit < 200)
                //{
                //    ServicePointManager.DefaultConnectionLimit = 200;
                //}
                //ServicePointManager.MaxServicePoints = int.MaxValue;
                ServicePointManager.MaxServicePointIdleTime = int.MaxValue;
                ServicePointManager.UseNagleAlgorithm = false;
                //ServicePointManager.CheckCertificateRevocationList = false;
                ServicePointManager.Expect100Continue = false;
                //ServicePointManager.SetTcpKeepAlive(true, 1000, 200);
                WebRequest.DefaultWebProxy = null;
                WebRequest.DefaultCachePolicy = new HttpRequestCachePolicy(HttpRequestCacheLevel.NoCacheNoStore);
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3
                                                       | SecurityProtocolType.Tls
                                                       | (SecurityProtocolType)0x300 //Tls11
                                                       | (SecurityProtocolType)0xC00 //Tls12
                                                       | (SecurityProtocolType)0x3000; //Tls13
            }
            catch
            {
                // ignored
            }

            //try
            //{
            //    if (!CommonString.isDebug)
            //        NetworkSetting.SetDNS(new string[] { "***************" });
            //}
            //catch (Exception oe)
            //{
            //    Log.WriteError(oe);
            //}

            try
            {
                var asm = Assembly.GetExecutingAssembly(); //如果是当前程序集
                var asmdis =
                    (AssemblyDescriptionAttribute)Attribute.GetCustomAttribute(asm,
                        typeof(AssemblyDescriptionAttribute));
                FullName = SubString(asmdis.Description, "", "(");
                DtNowDate = DateTime.Parse(SubString(asmdis.Description, "(").Replace("(", "").Replace(")", ""));
                StrNowVersion = asm.GetName().Version.ToString();
            }
            catch
            {
                // ignored
            }

            try
            {
                string path = Environment.GetEnvironmentVariable("PATH");
                if (string.IsNullOrEmpty(path) || !path.Contains(DefaultDLLPath))
                    Environment.SetEnvironmentVariable("PATH", DefaultDLLPath + ";" + path);
            }
            catch { }

            DnsHelper.InitServerService();
            //SetMaxNetWork();

            ////result = string.Format("{0},{1},{2},{3}", Type.GetHashCode(), StrName, StrUrl, IsDefault.ToString());
            //string str = "1,打码1";//1,打码1|2,打码2
            ////str = CommonEncryptHelper.DES3Encrypt(str, CommonString.StrCommonEncryptKey);
            //str = "http://***************:9001/";// "http://yzm.oldfish.cn/";//http://ymz.oldfish.cn/ // //http://img.jzjiu.com/
            //////str = "http://mzy.oldfish.cn/";//http://myz.oldfish.cn/
            //////str = "http://zym.oldfish.cn/";//http://zmy.oldfish.cn/
            //str = CommonEncryptHelper.DES3Encrypt(str, CommonString.StrCommonEncryptKey);

            ////抓包软件加密Key[Soft.txt]
            //var str = "[=====]360安全浏览器\r\nspoon\r\nspy\r\n-未完成订单";
            ////string str = "http://{0}.oldfish.cn/[a1]|\n121.42.84.169\n443";//http://img.jingyangchun.com/customerpic/{0}/|\n114.215.143.92\n21";//\nhttp://{0}.chinacloudsites.cn/
            //str = CommonMethod.Encrypto(str);
            //str = CommonEncryptHelper.DESEncrypt(str, CommonString.StrCommonEncryptKey);
            //str = "";

            //str = "http://{0}.oldfish.cn/[a2]|\n121.42.106.227\n443";
            //str = CommonMethod.Encrypto(str);
            //str = CommonEncryptHelper.DESEncrypt(str, CommonString.StrCommonEncryptKey);
            //str = "";
            //str = "http://{0}.oldfish.cn/[a3]|\n121.42.93.212\n443";
            //str = CommonMethod.Encrypto(str);
            //str = CommonEncryptHelper.DESEncrypt(str, CommonString.StrCommonEncryptKey);
            //str = "";
        }

        public static string SubString(string strSource, string strSpilt, string strEnd = "")
        {
            return strSource.SubStringHorspool(strSpilt, strEnd).Trim();
        }

        public static bool IsAdmini()
        {
            var result = false;
            try
            {
                var current = WindowsIdentity.GetCurrent();
                var windowsPrincipal = new WindowsPrincipal(current);
                result = windowsPrincipal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch (Exception)
            {
                //Log.WriteError("判断是否以管理员运行出错！", oe);
            }

            return result;
        }

        public static void RunAsAdmin(string exeFile, string arguments = null, bool isAdmin = true, bool isShowWindow = true)
        {
            try
            {
                using (var process = new Process())
                {
                    if (string.IsNullOrEmpty(exeFile)) exeFile = Application.ExecutablePath;
                    var psi = new ProcessStartInfo
                    {
                        FileName = exeFile,
                        Arguments = arguments ?? string.Empty,
                        UseShellExecute = true,
                        WindowStyle = isShowWindow ? ProcessWindowStyle.Normal : ProcessWindowStyle.Minimized,
                        Verb = isAdmin ? "runas" : string.Empty
                    };

                    process.StartInfo = psi;
                    process.Start();
                }
            }
            catch
            {
            }
        }

        public static string StrUpdateFile(bool isCreate = true)
        {
            var updateFilePath = string.Format("{0}\\{2}-自动更新-{1:yyyyMMddHHmmssfff}.exe", Path.GetTempPath().TrimEnd('\\'), ServerTime.DateTime, CommonString.ProductName);
            if (File.Exists(updateFilePath))
                try
                {
                    File.Delete(updateFilePath);
                }
                catch
                {
                }

            if (isCreate && !File.Exists(updateFilePath))
                try
                {
                    File.WriteAllBytes(updateFilePath, Resources.UpdateFile);
                }
                catch
                {
                }

            return updateFilePath;
        }

        public static Assembly LoadDllByName(string strMessage, bool isDownLoad = false, bool isNative = false)
        {
            var dllName = GetNotFindDll(strMessage);
            if (!string.IsNullOrEmpty(dllName))
            {
                if (!isDownLoad)
                {
                    isDownLoad = lstNotFindDll.Exists(p => strMessage.Contains(p));
                }

                var byts = GetDllByName(dllName, isDownLoad);
                if (byts != null && byts.Length > 0)
                {
                    return isNative ? null : Assembly.Load(byts);
                }
            }

            return null;
        }

        private static string GetNotFindDll(string expMessage)
        {
            //未能加载文件或程序集“O2S.Components.PDFRender4NET, Version=5.0.3.0, Culture=neutral, PublicKeyToken=6753860be21d84fb”或它的某一个依赖项。系统找不到指定的文件。
            //Could not load file or assembly 'System.Core, Version=3.5​.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089'
            var result = string.Empty;
            result = CommonMethod.SubString(expMessage, "“", ",")?.Trim();
            if (string.IsNullOrEmpty(result))
                result = CommonMethod.SubString(expMessage, "'", ",")?.Trim();
            if (string.IsNullOrEmpty(result))
                result = CommonMethod.SubString(expMessage, "\"", ",")?.Trim();
            if (string.IsNullOrEmpty(result))
                result = CommonMethod.SubString(expMessage, "", ",")?.Trim();
            return result;
        }

        private static byte[] GetDllByName(string fileName, bool isCreate = true)
        {
            if (string.IsNullOrEmpty(fileName))
            {
                return null;
            }

            if (dicAssemblyDictionary.ContainsKey(fileName))
            {
                return dicAssemblyDictionary[fileName];
            }

            byte[] result = null;
            var dllName = string.Format("{0}\\{1}.dll", DefaultDLLPath, fileName);

            if (File.Exists(dllName))
            {
                try
                {
                    result = File.ReadAllBytes(dllName);
                    //File.WriteAllBytes("zip.dat", CommonEncryptHelper.Compress(result));
                }
                catch
                {
                }
            }

            if (isCreate && (result == null || result.Length <= 0))
            {
                try
                {
                    dicAssemblyDictionary.Add(fileName, result);
                    var urlPath = string.Format("{0}ext/{1}.txt?t=" + ServerTime.DateTime.Ticks, HostUpdate?.FullUrl, fileName);
                    result = WebClientExt.GetOneClient().DownloadData(urlPath);
                    if (result != null && result.Length > 0)
                    {
                        result = CommonEncryptHelper.Decompress(result);
                        File.WriteAllBytes(dllName, result);
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine(e.Message);
                }
                finally
                {
                    lock (dicAssemblyDictionary)
                    {
                        if (result == null || result.Length <= 0)
                            dicAssemblyDictionary.Remove(fileName);
                        else
                            dicAssemblyDictionary[fileName] = result;
                    }
                }
            }

            return result;
        }

        #region 取色板相关

        public const int RecentColorsMax = 32;
        public static List<Color> RecentColors { get; set; } = new List<Color>();

        #endregion

    }

    public static class ListExtensions
    {
        public static T GetRndItem<T>(this ICollection<T> lstItem)
        {
            return lstItem.OrderBy(arg => Guid.NewGuid()).FirstOrDefault();
        }
    }
}