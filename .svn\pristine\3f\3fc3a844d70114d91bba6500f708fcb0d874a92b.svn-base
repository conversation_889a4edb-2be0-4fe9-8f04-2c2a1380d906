﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Design;
using System.Windows.Forms;

namespace OCRTools
{
    public class BlackStyleLabel : Control
    {
        private bool _autoEllipsis;

        private bool _drawBorder;
        private string _text;

        private ContentAlignment _textAlign;

        private Color _textShadowColor;

        public BlackStyleLabel()
        {
            DoubleBuffered = true;
            SetStyle(
                ControlStyles.UserPaint | ControlStyles.AllPaintingInWmPaint | ControlStyles.ResizeRedraw |
                ControlStyles.OptimizedDoubleBuffer | ControlStyles.SupportsTransparentBackColor, true);
            TextAlign = ContentAlignment.TopLeft;
            BackColor = Color.Transparent;
            ForeColor = Color.White;
            TextShadowColor = Color.Black;
            Font = new Font("Arial", 12);
        }

        [Editor(
            "System.ComponentModel.Design.MultilineStringEditor, System.Design, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a",
            typeof(UITypeEditor))]
        [SettingsBindable(true)]
        public override string Text
        {
            get => _text;
            set
            {
                if (value == null) value = "";

                if (_text != value)
                {
                    _text = value;

                    OnTextChanged(EventArgs.Empty);

                    Invalidate();
                }
            }
        }

        [DefaultValue(ContentAlignment.TopLeft)]
        public ContentAlignment TextAlign
        {
            get => _textAlign;
            set
            {
                _textAlign = value;

                Invalidate();
            }
        }

        [DefaultValue(typeof(Color), "Black")]
        public Color TextShadowColor
        {
            get => _textShadowColor;
            set
            {
                _textShadowColor = value;

                Invalidate();
            }
        }

        [DefaultValue(false)]
        public bool AutoEllipsis
        {
            get => _autoEllipsis;
            set
            {
                _autoEllipsis = value;

                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs pe)
        {
            var g = pe.Graphics;

            if (!string.IsNullOrEmpty(Text))
            {
                DrawText(g);

                if (_drawBorder) g.DrawRectangleProper(Pens.Black, ClientRectangle);
            }
        }

        private void DrawText(Graphics g)
        {
            TextFormatFlags tff;

            switch (TextAlign)
            {
                case ContentAlignment.TopLeft:
                    tff = TextFormatFlags.Top | TextFormatFlags.Left;
                    break;
                case ContentAlignment.MiddleLeft:
                    tff = TextFormatFlags.VerticalCenter | TextFormatFlags.Left;
                    break;
                case ContentAlignment.BottomLeft:
                    tff = TextFormatFlags.Bottom | TextFormatFlags.Left;
                    break;
                case ContentAlignment.TopCenter:
                    tff = TextFormatFlags.Top | TextFormatFlags.HorizontalCenter;
                    break;
                default:
                    tff = TextFormatFlags.VerticalCenter | TextFormatFlags.HorizontalCenter;
                    break;
                case ContentAlignment.BottomCenter:
                    tff = TextFormatFlags.Bottom | TextFormatFlags.HorizontalCenter;
                    break;
                case ContentAlignment.TopRight:
                    tff = TextFormatFlags.Top | TextFormatFlags.Right;
                    break;
                case ContentAlignment.MiddleRight:
                    tff = TextFormatFlags.VerticalCenter | TextFormatFlags.Right;
                    break;
                case ContentAlignment.BottomRight:
                    tff = TextFormatFlags.Bottom | TextFormatFlags.Right;
                    break;
            }

            if (AutoEllipsis) tff |= TextFormatFlags.EndEllipsis;

            if (TextShadowColor.A > 0)
                TextRenderer.DrawText(g, Text, Font, ClientRectangle.LocationOffset(0, 1), TextShadowColor, tff);

            TextRenderer.DrawText(g, Text, Font, ClientRectangle, ForeColor, tff);
        }
    }
}