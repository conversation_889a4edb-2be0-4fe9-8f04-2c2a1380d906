using System;
using System.IO;

namespace TianruoOCR
{
	public class LzwEncoder
	{
		private const int Eof = -1;

		private readonly int _imgW;

		private readonly int _imgH;

		private readonly byte[] _pixAry;

		private readonly int _initCodeSize;

		private int _curPixel;

		private const int Bits = 12;

		private const int HSize = 5003;

		private int _numBits;

		private readonly int _maxBits = 12;

		private int _maxCode;

		private const int MaxMaxCode = 4096;

		private int[] htab = new int[5003];

		private readonly int[] _codeTab = new int[5003];

		private int _hSize = 5003;

		private int _freeEntry = 0;

		private bool clear_flg = false;

		private int g_init_bits;

		private int ClearCode;

		private int EOFCode;

		private int cur_accum = 0;

		private int cur_bits = 0;

		private int[] masks = new int[17]
		{
			0,
			1,
			3,
			7,
			15,
			31,
			63,
			127,
			255,
			511,
			1023,
			2047,
			4095,
			8191,
			16383,
			32767,
			65535
		};

		private int _charCount;

		private readonly byte[] _accumulator = new byte[256];

		public LzwEncoder(int width, int height, byte[] pixels, int colorDepth)
		{
			_pixAry = pixels;
			_initCodeSize = Math.Max(2, colorDepth);
		}

		private void Add(byte c, Stream outs)
		{
			_accumulator[_charCount++] = c;
			if (_charCount >= 254)
			{
				Flush(outs);
			}
		}

		private void ClearTable(Stream outs)
		{
			ResetCodeTable(_hSize);
			_freeEntry = ClearCode + 2;
			clear_flg = true;
			Output(ClearCode, outs);
		}

		private void ResetCodeTable(int hsize)
		{
			for (int i = 0; i < hsize; i++)
			{
				htab[i] = -1;
			}
		}

		private void Compress(int initBits, Stream outs)
		{
			g_init_bits = initBits;
			clear_flg = false;
			_numBits = g_init_bits;
			_maxCode = MaxCode(_numBits);
			ClearCode = 1 << initBits - 1;
			EOFCode = ClearCode + 1;
			_freeEntry = ClearCode + 2;
			_charCount = 0;
			int num = NextPixel();
			int num2 = 0;
			for (int num3 = _hSize; num3 < 65536; num3 *= 2)
			{
				num2++;
			}
			num2 = 8 - num2;
			int hSize = _hSize;
			ResetCodeTable(hSize);
			Output(ClearCode, outs);
			int num4;
			while ((num4 = NextPixel()) != -1)
			{
				int num3 = (num4 << _maxBits) + num;
				int num5 = (num4 << num2) ^ num;
				if (htab[num5] == num3)
				{
					num = _codeTab[num5];
					continue;
				}
				if (htab[num5] >= 0)
				{
					int num6 = hSize - num5;
					if (num5 == 0)
					{
						num6 = 1;
					}
					while (true)
					{
						if ((num5 -= num6) < 0)
						{
							num5 += hSize;
						}
						if (htab[num5] == num3)
						{
							break;
						}
						if (htab[num5] >= 0)
						{
							continue;
						}
						goto IL_0167;
					}
					num = _codeTab[num5];
					continue;
				}
				goto IL_0167;
				IL_0167:
				Output(num, outs);
				num = num4;
				if (_freeEntry < 4096)
				{
					_codeTab[num5] = _freeEntry++;
					htab[num5] = num3;
				}
				else
				{
					ClearTable(outs);
				}
			}
			Output(num, outs);
			Output(EOFCode, outs);
		}

		public void Encode(Stream os)
		{
			os.WriteByte(Convert.ToByte(_initCodeSize));
			_curPixel = 0;
			Compress(_initCodeSize + 1, os);
			os.WriteByte(0);
		}

		private void Flush(Stream outs)
		{
			if (_charCount > 0)
			{
				outs.WriteByte(Convert.ToByte(_charCount));
				outs.Write(_accumulator, 0, _charCount);
				_charCount = 0;
			}
		}

		private int MaxCode(int numBits)
		{
			return (1 << numBits) - 1;
		}

		private int NextPixel()
		{
			if (_curPixel <= _pixAry.GetUpperBound(0))
			{
				byte b = _pixAry[_curPixel++];
				return b & 0xFF;
			}
			return -1;
		}

		private void Output(int code, Stream outs)
		{
			cur_accum &= masks[cur_bits];
			if (cur_bits > 0)
			{
				cur_accum |= code << cur_bits;
			}
			else
			{
				cur_accum = code;
			}
			cur_bits += _numBits;
			while (cur_bits >= 8)
			{
				Add((byte)(cur_accum & 0xFF), outs);
				cur_accum >>= 8;
				cur_bits -= 8;
			}
			if (_freeEntry > _maxCode || clear_flg)
			{
				if (clear_flg)
				{
					_maxCode = MaxCode(_numBits = g_init_bits);
					clear_flg = false;
				}
				else
				{
					_numBits++;
					_maxCode = ((_numBits == _maxBits) ? 4096 : MaxCode(_numBits));
				}
			}
			if (code == EOFCode)
			{
				while (cur_bits > 0)
				{
					Add((byte)(cur_accum & 0xFF), outs);
					cur_accum >>= 8;
					cur_bits -= 8;
				}
				Flush(outs);
			}
		}
	}
}
