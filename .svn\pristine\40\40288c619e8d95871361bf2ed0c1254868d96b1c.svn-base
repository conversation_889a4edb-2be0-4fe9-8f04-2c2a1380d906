using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class SynchronizedInputPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = SynchronizedInputPatternIdentifiers.Pattern;

        private SynchronizedInputPattern(AutomationElement el, IUIAutomationSynchronizedInputPattern pattern,
            bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        public static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null
                ? null
                : new SynchronizedInputPattern(el, (IUIAutomationSynchronizedInputPattern)pattern, cached);
        }
    }
}