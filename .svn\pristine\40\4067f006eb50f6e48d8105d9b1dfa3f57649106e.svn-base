﻿using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Web;

namespace OCRTools
{
    internal enum WeatherIconType
    {
        QQ = 1,
        墨迹 = 2
    }

    public class CommonWeather
    {
        private const string STR_QQIP_URL =
            "https://apis.map.qq.com/ws/location/v1/ip?key=3BFBZ-ZKD3X-LW54A-ZT76D-E7AHO-4RBD5&&output=jsonp&callback=weather";

        private const string STR_QQ_WEATHER_URL =
            "https://wis.qq.com/weather/common?source=pc&weather_type=observe%7Calarm&province={0}&city={1}&county={2}&callback=weather";

        private const string Str360WeatherUrl =
            "http://weather.kjjs.360.cn/freshcalendar/weather?ver=1.0.0.1125&callback=callback";
        private const string STR_QQ_PROVINCE = "\"province\":\"";
        private const string STR_QQ_CITY = "\"city\":\"";
        private const string STR_QQ_COUNTY = "\"district\":\"";
        private const string STR_QQ_WEATHER_UPDATE_TIME = "\"update_time\":\"";
        private const string STR_QQ_WEATHER_IMAGE = "\"weather_code\":\"";
        private const string STR_QQ_WEATHER_TIPMSG = "\"detail\":\"";

        private const string Str360WeatherSection = "\"realtime\":";
        private const string Str360WeatherImage = "\"img\":\"";
        private const string Str360WeatherCity = "\"city_name\":\"";
        private const string Str360WeatherUpdateTime = "\"dataUptime\":\"";
        private const string Str360WeatherTipMsg = "\"content\":\"";

        private const string STR_QQ_WEATHER_IMG_URL =
            //"https://mat1.gtimg.com/pingjs/ext2020/weather/pc/icon/weather/day/{0}.png";//小图
            "https://mat1.gtimg.com/pingjs/ext2020/weather/pc/icon/currentweather/day/{0}.png"; //大图

        private const string STR_MO_JI_WEATHER_IMG_URL =
            "https://h5tq.moji.com/tianqi/assets/images/weather/w{0}.png";

        public static void InitWeather()
        {
            try
            {
                var timerInfo = new TimerInfo
                {
                    TimerType = "LoopMinutes",
                    DateValue = 30,
                    IsExecFirst = true
                };
                TimerTaskDelegate update = UpdateMethod;
                var updateTimeTaskService = TimerTaskService.CreateTimerTaskService(timerInfo, update);
                updateTimeTaskService.Start();
            }
            catch
            {
            }
        }

        public static void UpdateMethod()
        {
            try
            {
                if (!CommonSetting.启用天气图标) return;
                WeatherIconType weatherIcon = WeatherIconType.QQ;
                if (Enum.TryParse(CommonSetting.天气图标样式, out WeatherIconType weatherIconType))
                {
                    weatherIcon = weatherIconType;
                }
                var strImage = GetWeather(weatherIcon);
                if (!string.IsNullOrEmpty(strImage))
                    try
                    {
                        CommonSetting.工具栏图片 = strImage;
                        FrmMain.FrmTool.RefreshImage();
                    }
                    catch
                    {
                    }
            }
            catch (Exception oe)
            {
                Log.WriteError("更新天气失败", oe);
            }
        }

        internal static string GetWeatherIcon(string data, WeatherIconType iconType, bool isAuto = true)
        {
            var url = string.Empty;
            switch (iconType)
            {
                case WeatherIconType.QQ:
                    url = string.Format(STR_QQ_WEATHER_IMG_URL, data);
                    break;
                case WeatherIconType.墨迹:
                    url = string.Format(STR_MO_JI_WEATHER_IMG_URL, data.TrimStart('0').PadRight(1, '0'));
                    break;
            }

            var result = CommonSetting.SetHeadImageByUrl(url);
            if (string.IsNullOrEmpty(result) && isAuto)
                return GetWeatherIcon(data, iconType == WeatherIconType.QQ ? WeatherIconType.墨迹 : WeatherIconType.QQ,
                    false);
            return result;
        }

        internal static string GetWeather(WeatherIconType iconType)
        {
            var qqWeather = GetQqWeather();
            var _360Weather = Get360Weather();
            WeatherDetail weather;
            if (string.IsNullOrEmpty(qqWeather.code) || string.IsNullOrEmpty(_360Weather.code))
            {
                weather = string.IsNullOrEmpty(qqWeather.code) ? _360Weather : qqWeather;
            }
            else
            {
                weather = qqWeather.updateTime > _360Weather.updateTime ? qqWeather : _360Weather;
            }
            var result = string.Empty;
            if (!string.IsNullOrEmpty(weather.code))
                result = GetWeatherIcon(weather.code, iconType);
            if (string.IsNullOrEmpty(weather.code))
            {
                CommonMethod.ShowHelpMsg("更新天气信息失败！");
            }
            else
            {
                CommonMethod.ShowHelpMsg(string.Format("更新{0}天气-[{2}]成功！发布时间:{1:HH:mm:ss}", weather.city, weather.updateTime, weather.type));
                if (!string.IsNullOrEmpty(weather.tipMsg))
                {
                    CommonMethod.ShowNotificationTip(weather.tipMsg, null, 5 * 1000);
                }
            }
            return result;
        }

        internal static WeatherDetail Get360Weather()
        {
            var result = new WeatherDetail { type = "360" };
            try
            {
                var html = WebClientExt.GetHtml(Str360WeatherUrl).Replace(" ", "").Trim();
                if (!string.IsNullOrEmpty(html) && html.Contains(Str360WeatherSection))
                {
                    html = html.Substring(html.IndexOf(Str360WeatherSection) + Str360WeatherSection.Length);
                    result.code = CommonMethod.SubString(html, Str360WeatherImage, "\"").Trim();
                    result.city = Regex.Unescape(CommonMethod.SubString(html, Str360WeatherCity, "\"").Trim());
                    var strTime = CommonMethod.SubString(html, Str360WeatherUpdateTime, "\"").Trim();
                    if (!string.IsNullOrEmpty(strTime))
                    {
                        result.updateTime = ConvertToDateTime(strTime);
                    }
                    result.tipMsg = Regex.Unescape(CommonMethod.SubString(html, Str360WeatherTipMsg, "\"").Trim());
                }
            }
            catch { }
            return result;
        }

        internal static DateTime ConvertToDateTime(string timestamp)
        {
            DateTime time = DateTime.MinValue;
            DateTime startTime = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
            if (timestamp.Length == 10)//精确到秒
            {
                time = startTime.AddSeconds(double.Parse(timestamp));
            }
            else if (timestamp.Length == 13)//精确到毫秒
            {
                time = startTime.AddMilliseconds(double.Parse(timestamp));
            }
            return time;
        }

        internal static WeatherDetail GetQqWeather()
        {
            var result = new WeatherDetail { type = "QQ" };
            try
            {
                var lstLocation = GetLocation();
                if (lstLocation.Count <= 0) return result;
                var html = WebClientExt.GetHtml(string.Format(STR_QQ_WEATHER_URL, HttpUtility.UrlEncode(lstLocation[0]),
                    HttpUtility.UrlEncode(lstLocation[1]), HttpUtility.UrlEncode(lstLocation[2]))
                ).Replace(" ", "").Trim();
                result.code = CommonMethod.SubString(html, STR_QQ_WEATHER_IMAGE, "\"").Trim();
                var strTime = CommonMethod.SubString(html, STR_QQ_WEATHER_UPDATE_TIME, "\"").Trim();
                if (!string.IsNullOrEmpty(strTime))
                {
                    result.updateTime = DateTime.ParseExact(strTime, "yyyyMMddHHmm", null);
                }
                result.tipMsg = CommonMethod.SubString(html, STR_QQ_WEATHER_TIPMSG, "\"").Trim();

                if (!string.IsNullOrEmpty(result.code))
                    result.city = string.IsNullOrEmpty(lstLocation[2]) ? lstLocation[1] : lstLocation[2];
            }
            catch { }
            return result;
        }

        private static List<string> GetLocation()
        {
            var lstResult = new List<string>();
            var html = WebClientExt.GetHtml(STR_QQIP_URL).Replace(" ", "").Trim();
            if (!string.IsNullOrEmpty(html) && html.Contains(STR_QQ_PROVINCE))
            {
                lstResult.Add(CommonMethod.SubString(html, STR_QQ_PROVINCE, "\"").Trim());
                lstResult.Add(CommonMethod.SubString(html, STR_QQ_CITY, "\"").Trim());
                lstResult.Add(CommonMethod.SubString(html, STR_QQ_COUNTY, "\"").Trim());
            }

            return lstResult;
        }
    }

    internal class WeatherDetail
    {
        public string type { get; set; }

        public string code { get; set; }

        public string city { get; set; }

        public DateTime updateTime { get; set; }

        public string tipMsg { get; set; }
    }
}