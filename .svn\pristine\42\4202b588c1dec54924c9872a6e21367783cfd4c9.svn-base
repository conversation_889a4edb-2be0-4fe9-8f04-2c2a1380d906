using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;

namespace OCRTools.Common
{
    /// <summary>
    /// 图像资源管理器 - 用于统一管理图像资源，避免多处引用同一图像资源时被错误释放
    /// </summary>
    public static class ImageResourceManager
    {
        // 图像资源引用计数字典
        private static readonly Dictionary<int, ImageResourceInfo> _imageResources = new Dictionary<int, ImageResourceInfo>();

        // 用于线程安全操作的锁对象
        private static readonly object _lockObj = new object();

        // 是否启用调试输出
        private static bool _enableDebugOutput = true;

        /// <summary>
        /// 启用或禁用调试输出
        /// </summary>
        /// <param name="enable">是否启用</param>
        public static void SetDebugOutput(bool enable)
        {
            _enableDebugOutput = enable;
        }

        /// <summary>
        /// 输出调试信息
        /// </summary>
        /// <param name="message">调试信息</param>
        private static void DebugOutput(string message)
        {
            if (_enableDebugOutput)
            {
                Console.WriteLine($"[ImageResourceManager] {message}");
            }
        }

        /// <summary>
        /// 获取一个图像的副本，并由资源管理器进行管理
        /// </summary>
        /// <param name="source">源图像</param>
        /// <param name="caller">调用者标识</param>
        /// <returns>图像副本</returns>
        public static Image GetManagedImage(Image source, string caller = null)
        {
            if (source == null) return null;

            try
            {
                // 创建图像副本
                Image imageCopy = CreateImageCopy(source);

                // 如果创建失败则返回null
                if (imageCopy == null) return null;

                // 注册到资源管理器
                RegisterImage(imageCopy, caller ?? "GetManagedImage");

                return imageCopy;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GetManagedImage error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建图像副本
        /// </summary>
        private static Image CreateImageCopy(Image source)
        {
            if (source == null) return null;
            
            try
            {
                // 尝试使用Clone方法
                return (Image)source.Clone();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Clone image failed: {ex.Message}");
                
                try
                {
                    // 如果Clone失败，尝试使用Bitmap方式复制
                    return new Bitmap(source);
                }
                catch (Exception ex2)
                {
                    Console.WriteLine($"Create bitmap copy failed: {ex2.Message}");
                    return null;
                }
            }
        }

        /// <summary>
        /// 智能注册图像到资源管理器
        /// 如果图像已被管理，则增加引用计数；如果未被管理，则新建管理记录
        /// </summary>
        /// <param name="image">要注册的图像</param>
        /// <param name="caller">调用者标识，用于调试跟踪</param>
        /// <returns>是否成功注册（包括增加引用计数）</returns>
        public static bool RegisterImage(Image image, string caller = null)
        {
            if (image == null) return false;

            lock (_lockObj)
            {
                int hashCode = image.GetHashCode();

                if (_imageResources.ContainsKey(hashCode))
                {
                    var info = _imageResources[hashCode];

                    // 检查是否是同一个图像对象的引用
                    if (ReferenceEquals(info.Image, image))
                    {
                        // 增加引用计数
                        info.ReferenceCount++;

                        // 记录调用者（用于调试）
                        if (!string.IsNullOrEmpty(caller))
                        {
                            info.Callers.Add(caller);
                        }

                        DebugOutput($"RegisterImage: Image {hashCode} reference count increased to {info.ReferenceCount}, caller: {caller ?? "unknown"}");
                        return true;
                    }
                    else
                    {
                        // HashCode相同但不是同一对象，记录警告
                        DebugOutput($"Warning: HashCode collision detected for image {hashCode}");
                        // 仍然增加引用计数，但记录警告
                        info.ReferenceCount++;
                        return true;
                    }
                }
                else
                {
                    // 如果不存在，添加新条目
                    var newInfo = new ImageResourceInfo
                    {
                        Image = image,
                        ReferenceCount = 1
                    };

                    if (!string.IsNullOrEmpty(caller))
                    {
                        newInfo.Callers.Add(caller);
                    }

                    _imageResources.Add(hashCode, newInfo);
                    DebugOutput($"RegisterImage: New image {hashCode} registered with reference count 1, caller: {caller ?? "unknown"}");
                    return true;
                }
            }
        }

        /// <summary>
        /// 释放对图像的引用
        /// </summary>
        /// <param name="image">要释放的图像</param>
        /// <param name="forceDispose">是否强制释放（无论引用计数）</param>
        /// <param name="caller">调用者标识</param>
        public static void ReleaseImage(Image image, bool forceDispose = false, string caller = null)
        {
            if (image == null) return;

            lock (_lockObj)
            {
                int hashCode = image.GetHashCode();

                if (_imageResources.ContainsKey(hashCode))
                {
                    var info = _imageResources[hashCode];

                    // 检查是否是同一个图像对象的引用
                    if (!ReferenceEquals(info.Image, image))
                    {
                        DebugOutput($"Warning: Attempting to release different image object with same hashcode {hashCode}");
                        // 对于不同对象，仍然尝试直接释放
                        TryDisposeImageDirectly(image, "different object with same hashcode");
                        return;
                    }

                    // 如果指定了调用者，从调用者列表中移除
                    if (!string.IsNullOrEmpty(caller) && info.Callers.Contains(caller))
                    {
                        info.Callers.Remove(caller);
                    }

                    // 减少引用计数
                    info.ReferenceCount--;
                    DebugOutput($"ReleaseImage: Image {hashCode} reference count decreased to {info.ReferenceCount}, caller: {caller ?? "unknown"}");

                    // 如果引用计数为0或强制释放，则真正释放资源
                    if (info.ReferenceCount <= 0 || forceDispose)
                    {
                        try
                        {
                            // 移除引用
                            _imageResources.Remove(hashCode);

                            // 释放资源
                            info.Image.Dispose();
                            DebugOutput($"ReleaseImage: Image {hashCode} disposed successfully");
                        }
                        catch (Exception ex)
                        {
                            DebugOutput($"Error disposing managed image {hashCode}: {ex.Message}");
                            // 即使释放失败，也要从管理器中移除，避免内存泄漏
                            _imageResources.Remove(hashCode);
                        }
                    }
                    else if (info.ReferenceCount < 0)
                    {
                        // 引用计数异常，记录错误并修正
                        DebugOutput($"Error: Reference count for image {hashCode} became negative ({info.ReferenceCount}), correcting to 0");
                        info.ReferenceCount = 0;
                    }
                }
                else
                {
                    // 如果管理器中没有该图像的记录，则直接释放
                    DebugOutput($"ReleaseImage: Image {hashCode} not found in manager, disposing directly");
                    TryDisposeImageDirectly(image, "unmanaged image");
                }
            }
        }

        /// <summary>
        /// 尝试直接释放图像资源
        /// </summary>
        /// <param name="image">要释放的图像</param>
        /// <param name="reason">释放原因</param>
        private static void TryDisposeImageDirectly(Image image, string reason)
        {
            try
            {
                image.Dispose();
                DebugOutput($"Direct dispose successful for {reason}");
            }
            catch (Exception ex)
            {
                DebugOutput($"Error disposing {reason}: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理所有未使用的图像资源
        /// </summary>
        public static void CleanupUnusedResources()
        {
            lock (_lockObj)
            {
                List<int> keysToRemove = new List<int>();

                // 找出所有引用计数为0的资源
                foreach (var pair in _imageResources.Where(p => p.Value.ReferenceCount <= 0))
                {
                    keysToRemove.Add(pair.Key);

                    try
                    {
                        // 释放资源
                        pair.Value.Image.Dispose();
                        DebugOutput($"CleanupUnusedResources: Disposed image {pair.Key}");
                    }
                    catch (Exception ex)
                    {
                        DebugOutput($"Error disposing unused image {pair.Key}: {ex.Message}");
                    }
                }

                // 从字典中移除
                foreach (int key in keysToRemove)
                {
                    _imageResources.Remove(key);
                }

                if (keysToRemove.Count > 0)
                {
                    DebugOutput($"CleanupUnusedResources: Cleaned up {keysToRemove.Count} unused images");
                }
            }
        }

        /// <summary>
        /// 检查图像是否已被管理
        /// </summary>
        /// <param name="image">要检查的图像</param>
        /// <returns>是否已被管理</returns>
        public static bool IsImageManaged(Image image)
        {
            if (image == null) return false;

            lock (_lockObj)
            {
                int hashCode = image.GetHashCode();
                return _imageResources.ContainsKey(hashCode) &&
                       ReferenceEquals(_imageResources[hashCode].Image, image);
            }
        }

        /// <summary>
        /// 确保图像被正确管理
        /// 这是外部调用的统一入口，内部会自动判断是否需要注册
        /// </summary>
        /// <param name="image">要管理的图像</param>
        /// <param name="caller">调用者标识</param>
        /// <returns>是否成功管理</returns>
        public static bool EnsureImageManaged(Image image, string caller = null)
        {
            if (image == null) return false;

            // 统一在这里处理注册逻辑，外部调用者无需判断
            return RegisterImage(image, caller);
        }

        /// <summary>
        /// 获取当前资源管理器中的图像数量
        /// </summary>
        public static int GetImageCount()
        {
            lock (_lockObj)
            {
                return _imageResources.Count;
            }
        }
        
        /// <summary>
        /// 打印当前资源管理情况（调试用）
        /// </summary>
        public static string GetResourceInfo()
        {
            lock (_lockObj)
            {
                string info = $"Total managed images: {_imageResources.Count}\n";
                foreach (var pair in _imageResources)
                {
                    var imageInfo = pair.Value;
                    var callersInfo = imageInfo.Callers.Count > 0 ? string.Join(", ", imageInfo.Callers) : "unknown";
                    var ageInfo = (DateTime.Now - imageInfo.RegisterTime).TotalMinutes.ToString("F1");

                    info += $"Image {pair.Key}: {imageInfo.ReferenceCount} references, " +
                           $"Size: {imageInfo.Image.Width}x{imageInfo.Image.Height}, " +
                           $"Callers: [{callersInfo}], " +
                           $"Age: {ageInfo} minutes\n";
                }
                return info;
            }
        }

        /// <summary>
        /// 检查是否存在引用计数异常的图像
        /// </summary>
        public static void ValidateReferenceCount()
        {
            lock (_lockObj)
            {
                var problematicImages = _imageResources.Where(p => p.Value.ReferenceCount <= 0).ToList();

                if (problematicImages.Any())
                {
                    DebugOutput($"Found {problematicImages.Count} images with invalid reference count:");
                    foreach (var item in problematicImages)
                    {
                        DebugOutput($"  Image {item.Key}: count={item.Value.ReferenceCount}, callers=[{string.Join(", ", item.Value.Callers)}]");
                    }
                }
            }
        }

        /// <summary>
        /// 强制清理所有资源（应急使用）
        /// </summary>
        public static void ForceCleanupAll()
        {
            lock (_lockObj)
            {
                DebugOutput($"Force cleanup: disposing {_imageResources.Count} managed images");

                foreach (var pair in _imageResources.ToList())
                {
                    try
                    {
                        pair.Value.Image.Dispose();
                        DebugOutput($"Force disposed image {pair.Key}");
                    }
                    catch (Exception ex)
                    {
                        DebugOutput($"Error force disposing image {pair.Key}: {ex.Message}");
                    }
                }

                _imageResources.Clear();
                DebugOutput("Force cleanup completed");
            }
        }
    }

    /// <summary>
    /// 图像资源信息
    /// </summary>
    internal class ImageResourceInfo
    {
        /// <summary>
        /// 图像对象
        /// </summary>
        public Image Image { get; set; }

        /// <summary>
        /// 引用计数
        /// </summary>
        public int ReferenceCount { get; set; }

        /// <summary>
        /// 调用者列表，用于跟踪谁注册了这个图像
        /// </summary>
        public HashSet<string> Callers { get; set; } = new HashSet<string>();

        /// <summary>
        /// 注册时间
        /// </summary>
        public DateTime RegisterTime { get; set; } = DateTime.Now;
    }
} 