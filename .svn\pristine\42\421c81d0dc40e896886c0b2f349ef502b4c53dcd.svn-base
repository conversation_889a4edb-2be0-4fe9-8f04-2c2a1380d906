﻿using OCRTools.Common;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class NotificationForm : Form
    {
        private readonly float _opacityDecrement;
        private readonly int fadeInterval = 50;
        private readonly int titleSpace = 3;
        private readonly int urlPadding = 3;
        private bool _isDurationEnd;

        private bool _isMouseInside;
        private Size _textRenderSize;
        private Size _titleRenderSize;

        private NotificationForm(NotificationFormConfig config)
        {
            InitializeComponent();
            SetStyle(ControlStyles.OptimizedDoubleBuffer | ControlStyles.UserPaint | ControlStyles.AllPaintingInWmPaint,
                true);

            Config = config;
            _opacityDecrement = (float)fadeInterval / Config.FadeDuration;

            if (Config.Image != null)
            {
                Config.ViewImage = ImageProcessHelper.ResizeImageLimit(Config.Image, Config.Size);
                Config.Size = new Size(Math.Max(Config.ViewImage.Width + 2, 200), Config.ViewImage.Height + 2);
            }
            else if (!string.IsNullOrEmpty(Config.Text))
            {
                var size = Config.Size.Offset(-Config.TextPadding * 2);
                _textRenderSize = TextRenderer.MeasureText(Config.Text, Config.TextFont, size,
                    TextFormatFlags.WordBreak | TextFormatFlags.TextBoxControl | TextFormatFlags.EndEllipsis);
                _textRenderSize = new Size(_textRenderSize.Width, Math.Min(_textRenderSize.Height, size.Height));

                Config.Size = new Size(_textRenderSize.Width + Config.TextPadding * 2,
                    _textRenderSize.Height + Config.TextPadding * 2 + 2);
            }

            if (!string.IsNullOrEmpty(Config.Title))
            {
                _titleRenderSize = TextRenderer.MeasureText(Config.Title, Config.TitleFont,
                    Config.Size.Offset(-Config.TextPadding * 2),
                    TextFormatFlags.Left | TextFormatFlags.EndEllipsis);

                Config.Size = new Size(Math.Max(Config.Size.Width, _titleRenderSize.Width + Config.TextPadding * 2)
                    , _titleRenderSize.Height + titleSpace + Config.Size.Height + Config.TextPadding * 2 + 2);
            }

            var position = GetPosition(Config.Placement, Config.Offset, Screen.PrimaryScreen.WorkingArea.Size,
                Config.Size);

            NativeMethods.SetWindowPos(Handle, (IntPtr)SpecialWindowHandles.HWND_TOPMOST,
                position.X + Screen.PrimaryScreen.WorkingArea.X,
                position.Y + Screen.PrimaryScreen.WorkingArea.Y, Config.Size.Width, Config.Size.Height,
                SetWindowPosFlags.SWP_NOACTIVATE);

            if (Config.Duration <= 0)
            {
                DurationEnd();
            }
            else
            {
                tDuration.Interval = Config.Duration;
                tDuration.Start();
            }
        }

        public NotificationFormConfig Config { get; }

        protected override CreateParams CreateParams
        {
            get
            {
                var createParams = base.CreateParams;
                createParams.ExStyle |= 0x00000080;
                return createParams;
            }
        }

        public static Point GetPosition(ContentAlignment placement, int offset, Size backgroundSize, Size objectSize)
        {
            return GetPosition(placement, new Point(offset, offset), backgroundSize, objectSize);
        }

        public static Point GetPosition(ContentAlignment placement, Point offset, Size backgroundSize, Size objectSize)
        {
            var midX = (int)Math.Round(backgroundSize.Width / 2f - objectSize.Width / 2f);
            var midY = (int)Math.Round(backgroundSize.Height / 2f - objectSize.Height / 2f);
            var right = backgroundSize.Width - objectSize.Width;
            var bottom = backgroundSize.Height - objectSize.Height;

            switch (placement)
            {
                default:
                    return new Point(offset.X, offset.Y);
                case ContentAlignment.TopCenter:
                    return new Point(midX, offset.Y);
                case ContentAlignment.TopRight:
                    return new Point(right - offset.X, offset.Y);
                case ContentAlignment.MiddleLeft:
                    return new Point(offset.X, midY);
                case ContentAlignment.MiddleCenter:
                    return new Point(midX, midY);
                case ContentAlignment.MiddleRight:
                    return new Point(right - offset.X, midY);
                case ContentAlignment.BottomLeft:
                    return new Point(offset.X, bottom - offset.Y);
                case ContentAlignment.BottomCenter:
                    return new Point(midX, bottom - offset.Y);
                case ContentAlignment.BottomRight:
                    return new Point(right - offset.X, bottom - offset.Y);
            }
        }

        public static void Show(NotificationFormConfig config)
        {
            if (config.Image == null && !string.IsNullOrEmpty(config.FilePath))
                config.Image = ImageProcessHelper.LoadImage(config.FilePath);

            if (config.Image != null || !string.IsNullOrEmpty(config.Text))
            {
                var form = new NotificationForm(config);
                form.Name = Guid.NewGuid().ToString();
                NativeMethods.ShowWindow(form.Handle, 8);
                //form.Show();
            }
        }

        private void tDuration_Tick(object sender, EventArgs e)
        {
            DurationEnd();
        }

        private void DurationEnd()
        {
            _isDurationEnd = true;
            tDuration.Stop();

            if (!_isMouseInside) StartClosing();
        }

        private void StartClosing()
        {
            if (Config.FadeDuration <= 0)
            {
                Close();
            }
            else
            {
                Opacity = 1;
                tOpacity.Interval = fadeInterval;
                tOpacity.Start();
            }
        }

        private void tOpacity_Tick(object sender, EventArgs e)
        {
            if (Opacity > _opacityDecrement)
                Opacity -= _opacityDecrement;
            else
                Close();
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            var g = e.Graphics;
            g.Clear(Config.BackgroundColor);

            var rect = ClientRectangle;
            var titleRect = Rectangle.Empty;
            if (!string.IsNullOrEmpty(Config.Title))
            {
                titleRect = new Rectangle(Config.TextPadding, Config.TextPadding, _titleRenderSize.Width + 2,
                    _titleRenderSize.Height + 2);
                TextRenderer.DrawText(g, Config.Title, Config.TitleFont, titleRect, Config.TitleColor,
                    TextFormatFlags.Left | TextFormatFlags.EndEllipsis);
            }

            if (Config.ViewImage != null)
            {
                var imgRect = new Rectangle((Width - Config.ViewImage.Width) / 2 + 1,
                    (titleRect.IsEmpty ? 0 : titleRect.Y + titleRect.Height + titleSpace) + 1, Config.ViewImage.Width,
                    Config.ViewImage.Height);
                g.DrawImage(Config.ViewImage, imgRect);

                if (_isMouseInside && !string.IsNullOrEmpty(Config.Url))
                {
                    Rectangle textRect;
                    if (!titleRect.IsEmpty)
                        textRect = new Rectangle(1, titleRect.Height + titleSpace + 1, rect.Width, 40);
                    else
                        textRect = new Rectangle(1, 1, rect.Width, 40);

                    using (var brush = new SolidBrush(Color.FromArgb(100, 0, 0, 0)))
                    {
                        g.FillRectangle(brush, textRect);
                    }

                    TextRenderer.DrawText(g, Config.Url, Config.TextFont, textRect.Offset(-urlPadding), Color.White,
                        TextFormatFlags.Left | TextFormatFlags.EndEllipsis);
                }
            }
            else if (!string.IsNullOrEmpty(Config.Text))
            {
                var textRect = new Rectangle(Config.TextPadding,
                    (titleRect.IsEmpty ? 0 : titleRect.Y + titleRect.Height + titleSpace) + Config.TextPadding,
                    _textRenderSize.Width + 2, _textRenderSize.Height + 2);

                TextRenderer.DrawText(g, Config.Text, Config.TextFont, textRect, Config.TextColor,
                    TextFormatFlags.WordBreak | TextFormatFlags.TextBoxControl | TextFormatFlags.EndEllipsis);
            }

            using (var borderPen = new Pen(Config.BorderColor))
            {
                g.DrawRectangleProper(borderPen, rect);
            }
        }

        private void NotificationForm_MouseClick(object sender, MouseEventArgs e)
        {
            tDuration.Stop();

            Close();

            var action = ToastClickAction.CloseNotification;

            if (e.Button == MouseButtons.Left)
                action = Config.LeftClickAction;
            else if (e.Button == MouseButtons.Right)
                action = Config.RightClickAction;
            else if (e.Button == MouseButtons.Middle) action = Config.MiddleClickAction;

            ExecuteAction(action);
        }

        private void ExecuteAction(ToastClickAction action)
        {
            switch (action)
            {
                case ToastClickAction.ViewImage:
                    if (!string.IsNullOrEmpty(Config.FilePath))
                        this.ViewImage(null, Config.FilePath);
                    else if (Config.Image != null)
                        this.ViewImage(Config.Image);
                    break;
                case ToastClickAction.CopyImageToClipboard:
                    ClipboardService.CopyImageFromFile(Config.FilePath);
                    break;
                case ToastClickAction.CopyUrl:
                    ClipboardService.SetText(Config.Url);
                    break;
                case ToastClickAction.OpenUrl:
                    CommonMethod.OpenUrl(Config.Url);
                    break;
                case ToastClickAction.OpenFile:
                    CommonMethod.OpenFile(Config.FilePath);
                    break;
                case ToastClickAction.OpenFolder:
                    CommonMethod.OpenFolderWithFile(Config.FilePath);
                    break;
                case ToastClickAction.OpenForm:
                    CommonMethod.OpenForm(Config.Data);
                    break;
            }
        }

        private void NotificationForm_MouseEnter(object sender, EventArgs e)
        {
            _isMouseInside = true;
            tOpacity.Stop();

            if (!IsDisposed)
            {
                Refresh();
                Opacity = 1;
            }
        }

        private void NotificationForm_MouseLeave(object sender, EventArgs e)
        {
            _isMouseInside = false;
            Refresh();

            if (_isDurationEnd) StartClosing();
        }

        #region Windows Form Designer generated code

        private Timer tDuration;
        private Timer tOpacity;

        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }

            if (Config != null)
            {
                Config.Dispose();
            }

            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            tDuration = new Timer(components);
            tOpacity = new Timer(components);
            SuspendLayout();
            //
            // tDuration
            //
            tDuration.Tick += new EventHandler(tDuration_Tick);
            //
            // tOpacity
            //
            tOpacity.Tick += new EventHandler(tOpacity_Tick);
            //
            // NotificationForm
            //
            ClientSize = new Size(400, 300);
            Cursor = Cursors.Hand;
            FormBorderStyle = FormBorderStyle.None;
            Name = "NotificationForm";
            ShowInTaskbar = false;
            StartPosition = FormStartPosition.Manual;
            Text = "NotificationForm";
            MouseClick += new MouseEventHandler(NotificationForm_MouseClick);
            MouseEnter += new EventHandler(NotificationForm_MouseEnter);
            MouseLeave += new EventHandler(NotificationForm_MouseLeave);
            ResumeLayout(false);
        }

        #endregion Windows Form Designer generated code
    }

    public class NotificationFormConfig : IDisposable
    {
        public int Duration { get; set; } = 3000;
        public int FadeDuration { get; set; }
        public ContentAlignment Placement { get; set; }
        public int Offset { get; set; } = 5;
        public Size Size { get; set; }
        public Color BackgroundColor { get; set; } = Color.FromArgb(50, 50, 50);
        public Color BorderColor { get; set; } = Color.FromArgb(40, 40, 40);
        public int TextPadding { get; set; } = 10;
        public Font TextFont { get; set; } = CommonString.GetFont("Arial", 17);
        public Color TextColor { get; set; } = Color.FromArgb(210, 210, 210);
        public Font TitleFont { get; set; } = CommonString.GetFont("Arial", 17, FontStyle.Bold);
        public Color TitleColor { get; set; } = Color.FromArgb(240, 240, 240);

        public Bitmap Image { get; set; }

        public Bitmap ViewImage { get; set; }
        public string Title { get; set; }
        public string Text { get; set; }
        public string FilePath { get; set; }
        public string Url { get; set; }
        public string Data { get; set; }
        public ToastClickAction LeftClickAction { get; set; }
        public ToastClickAction RightClickAction { get; set; }
        public ToastClickAction MiddleClickAction { get; set; }

        public void Dispose()
        {
            TextFont?.Dispose();

            TitleFont?.Dispose();

            ViewImage?.Dispose();
        }
    }

    /// <summary>
    ///     Special window handles
    /// </summary>
    public enum SpecialWindowHandles
    {
        /// <summary>
        ///     Places the window at the top of the Z order.
        /// </summary>
        HWND_TOPMOST = -1
    }

    [DefaultValue(None)]
    public enum ToastClickAction
    {
        None,
        [Description("Close notification")] CloseNotification,
        [Description("View image")] ViewImage,

        [Description("Copy image to clipboard")]
        CopyImageToClipboard,
        [Description("Copy URL")] CopyUrl,
        [Description("Open file")] OpenFile,
        [Description("Open folder")] OpenFolder,
        [Description("Open URL")] OpenUrl,
        [Description("Open Form")] OpenForm
    }

    public enum BalloonTipClickAction
    {
        None,
        OpenUrl,
        OpenForm
    }

    public class BalloonTipAction
    {
        public BalloonTipClickAction ClickAction { get; set; }
        public string Text { get; set; }
    }

    [Flags]
    public enum SetWindowPosFlags : uint
    {
        /// <summary>
        ///     Does not activate the window. If this flag is not set, the window is activated and moved to the top of either the
        ///     topmost or non-topmost group (depending on the setting of the hWndInsertAfter parameter).
        /// </summary>
        SWP_NOACTIVATE = 0x0010
    }

    public class DefaultToastSetting
    {
        private float _toastWindowDuration = 3;

        private float _toastWindowFadeDuration = 1;

        private Size _toastWindowSize = new Size(400, 300);

        [DefaultValue(3f)]
        public float ToastWindowDuration
        {
            get => _toastWindowDuration;
            set => _toastWindowDuration = value.Clamp(0, 30);
        }

        [DefaultValue(1f)]
        public float ToastWindowFadeDuration
        {
            get => _toastWindowFadeDuration;
            set => _toastWindowFadeDuration = value.Clamp(0, 30);
        }

        [DefaultValue(ContentAlignment.BottomRight)]
        public ContentAlignment ToastWindowPlacement { get; set; } = ContentAlignment.BottomRight;

        [DefaultValue(typeof(Size), "400, 300")]
        public Size ToastWindowSize
        {
            get => _toastWindowSize;
            set => _toastWindowSize = new Size(Math.Max(value.Width, 100), Math.Max(value.Height, 100));
        }
    }
}