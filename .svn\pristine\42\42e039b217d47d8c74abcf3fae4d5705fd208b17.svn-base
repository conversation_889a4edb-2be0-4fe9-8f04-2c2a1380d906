// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class GridPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = GridPatternIdentifiers.Pattern;
        public static readonly AutomationProperty ColumnCountProperty = GridPatternIdentifiers.ColumnCountProperty;
        public static readonly AutomationProperty RowCountProperty = GridPatternIdentifiers.RowCountProperty;

        private IUIAutomationGridPattern _pattern;


        protected GridPattern(AutomationElement el, IUIAutomationGridPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
            _pattern = pattern;
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new GridPattern(el, (IUIAutomationGridPattern) pattern, cached);
        }
    }

    public class GridItemPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = GridItemPatternIdentifiers.Pattern;
        public static readonly AutomationProperty ColumnProperty = GridItemPatternIdentifiers.ColumnProperty;
        public static readonly AutomationProperty ColumnSpanProperty = GridItemPatternIdentifiers.ColumnSpanProperty;

        public static readonly AutomationProperty ContainingGridProperty =
            GridItemPatternIdentifiers.ContainingGridProperty;

        public static readonly AutomationProperty RowProperty = GridItemPatternIdentifiers.RowProperty;
        public static readonly AutomationProperty RowSpanProperty = GridItemPatternIdentifiers.RowSpanProperty;

        private IUIAutomationGridItemPattern _pattern;

        protected GridItemPattern(AutomationElement el, IUIAutomationGridItemPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
            _pattern = pattern;
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new GridItemPattern(el, (IUIAutomationGridItemPattern) pattern, cached);
        }
    }
}