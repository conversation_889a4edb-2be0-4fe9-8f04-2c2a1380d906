﻿using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Language;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmPicCompare : MetroForm
    {
        public FrmPicCompare()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            Load += FrmPicCompare_Load;
            FormClosing += FrmPicCompare_FormClosing;
        }

        private void FrmPicCompare_Load(object sender, EventArgs e)
        {
            content.ShowImageTool();
        }

        private void FrmPicCompare_FormClosing(object sender, FormClosingEventArgs e)
        {
            MemoryManager.ClearMemory();
        }

        internal void Init(SpiltMode spiltMode, bool isShowOld)
        {
            content.SpiltModel = spiltMode;
            content.IsShowOldContent = isShowOld;
        }

        internal void Bind(Image image, OcrContent ocrContent, DisplayModel displayModel)
        {
            content.ScalingStrategy = ImageBox.ImageScalingStrategy.FitComplete;
            content.BindImage(image, true, CommonSetting.图片自动缩放);
            content.NowDisplayMode = displayModel;
            if (ocrContent != null)
            {
                content.RefreshStyle();
                content.BindContentByOcr(ocrContent);
                Text = $"{content.NowDisplayMode.ToString().CurrentText()}-【{ocrContent.processName.CurrentText()}】";
            }
        }

        private void FrmPicCompare_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape) Close();
        }
    }
}