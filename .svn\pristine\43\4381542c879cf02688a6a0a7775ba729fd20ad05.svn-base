﻿using OCRTools.Common;
using System;
using System.Diagnostics;
using System.IO;
using System.Linq;

namespace OCRTools
{
    class LocalOcrService
    {
        public static void CloseService()
        {
            // KillProcess
            try
            {
                var process = Process.GetProcessesByName(Path.GetFileNameWithoutExtension(CommonString.DefaultLocalRecExePath)).FirstOrDefault();
                if (process != null)
                    BadSoftWindow.KillProcess(process);
            }
            catch { }
            MemoryManager.ClearMemory();
        }

        public static void OpenOcrService(int port, int thread, bool isShowMsg = true)
        {
            if (!File.Exists(CommonString.DefaultLocalRecExePath))
            {
                return;
            }

            if (port < 1000 || port > 9999)
            {
                port = 8080;
            }
            if (thread < 1 || thread > 100)
            {
                thread = 5;
            }

            // SetIni
            if (isShowMsg)
                CommonMethod.ShowHelpMsg("正在更新本地识别程序配置信息…");
            var iniPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + "\\OcrService\\config.ini";
            if (!Directory.Exists(Path.GetDirectoryName(iniPath)))
            {
                Directory.CreateDirectory(Path.GetDirectoryName(iniPath));
            }
            IniHelper.SetValue("配置", "端口", port.ToString("F0"), iniPath);
            IniHelper.SetValue("配置", "线程", thread.ToString("F0"), iniPath);

            if (isShowMsg)
                CommonMethod.ShowHelpMsg("正在启动本地识别服务进程…");
            CloseService();

            // StartProcess
            //CommonMethod.ExecCmdWithNoResult("start /min cmd /c \"" + CommonString.DefaultLocalRecExePath + "\"");
            CommonMethod.OpenFile(CommonString.DefaultLocalRecExePath, false);
            if (isShowMsg)
                CommonMethod.ShowHelpMsg("启动本地识别服务完成！");
        }
    }
}
