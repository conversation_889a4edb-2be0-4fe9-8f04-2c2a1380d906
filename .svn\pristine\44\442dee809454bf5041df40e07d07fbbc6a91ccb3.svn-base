using System;
using System.Collections.Generic;
using ExcelLibrary.BinaryFileFormat;
using QiHe.CodeLib;

namespace ExcelLibrary.SpreadSheet
{
	public class CellCollection
	{
		public Dictionary<int, Row> Rows = new Dictionary<int, Row>();

		public int FirstRowIndex = int.MaxValue;

		public int FirstColIndex = int.MaxValue;

		public int LastRowIndex;

		public int LastColIndex;

		internal SharedResource SharedResource;

		public ColumnWidth ColumnWidth = new ColumnWidth();

		public Cell this[int row, int col]
		{
			get
			{
				if (Rows.ContainsKey(row))
				{
					return GetRow(row).GetCell(col);
				}
				return Cell.EmptyCell;
			}
			set
			{
				FirstRowIndex = Math.Min(FirstRowIndex, row);
				FirstColIndex = Math.Min(FirstColIndex, col);
				LastRowIndex = Math.Max(LastRowIndex, row);
				LastColIndex = Math.Max(LastColIndex, col);
				value.SharedResource = SharedResource;
				GetRow(row).SetCell(col, value);
			}
		}

		public Cell CreateCell(int row, int col, object value, int XFindex)
		{
			XF xF = SharedResource.ExtendedFormats[XFindex];
			CellFormat format = SharedResource.CellFormats[xF.FormatIndex];
			Cell cell = new Cell(value, format);
			cell.SharedResource = SharedResource;
			cell.Style = CreateStyleFromXF(xF);
			this[row, col] = cell;
			return cell;
		}

		private CellStyle CreateStyleFromXF(XF xf)
		{
			CellStyle cellStyle = new CellStyle();
			cellStyle.BackColor = SharedResource.ColorPalette[xf.PatternColorIndex];
			return cellStyle;
		}

		public Row GetRow(int rowIndex)
		{
			if (!Rows.ContainsKey(rowIndex))
			{
				Rows[rowIndex] = new Row();
			}
			return Rows[rowIndex];
		}

		public IEnumerator<Pair<Pair<int, int>, Cell>> GetEnumerator()
		{
			foreach (KeyValuePair<int, Row> row in Rows)
			{
				KeyValuePair<int, Row> keyValuePair = row;
				foreach (KeyValuePair<int, Cell> cell in keyValuePair.Value)
				{
					KeyValuePair<int, Row> keyValuePair2 = row;
					int key = keyValuePair2.Key;
					KeyValuePair<int, Cell> keyValuePair3 = cell;
					Pair<int, int> left = new Pair<int, int>(key, keyValuePair3.Key);
					KeyValuePair<int, Cell> keyValuePair4 = cell;
					yield return new Pair<Pair<int, int>, Cell>(left, keyValuePair4.Value);
				}
			}
		}
	}
}
