﻿using MetroFramework.Forms;
using OCRTools.Language;
using System;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FormRecent : MetroForm
    {
        public FormRecent()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            ucTaskThumbnailView.TitleVisible = true;
            ucTaskThumbnailView.TitleLocation = ThumbnailTitleLocation.Top;
            ucTaskThumbnailView.ThumbnailSize = new Size(200, 150);
            Load += FormRecent_Load;
            this.ContextMenuStrip = cmsTaskInfo;
        }

        private void FormRecent_Load(object sender, EventArgs e)
        {
            AddRecentTasksToMainWindow();
        }

        private TaskThumbnailPanel SelectedPanel => ucTaskThumbnailView.SelectedPanel;

        private HistoryTask SelectedTask => ucTaskThumbnailView.SelectedPanel?.Task;

        private void AddRecentTasksToMainWindow()
        {
            foreach (var recentTask in Program.RecentTasks) AddTask(recentTask);
            if (ucTaskThumbnailView.Panels.Count == 0)
            {
                ucTaskThumbnailView.Visible = false;
                var _labPrompt = new Label
                {
                    AutoSize = false,
                    BackColor = Color.White,
                    Font = CommonString.GetSysNormalFont(26F),
                    ForeColor = SystemColors.GrayText,
                    TabIndex = 12,
                    Text = "没有数据！".CurrentText(),
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleCenter
                };
                _labPrompt.Visible = true;
                Controls.Add(_labPrompt);
                _labPrompt.BringToFront();
            }
        }

        private void AddTask(HistoryTask task)
        {
            if (task != null) ucTaskThumbnailView.AddPanel(task).UpdateThumbnail();
        }

        private void ucTaskThumbnailView_ContextMenuRequested(object sender, MouseEventArgs e)
        {
            cmsTaskInfo.Show(sender as Control, e.X + 1, e.Y + 1);
        }

        private void tsmiThumbnailTitleShow_Click(object sender, EventArgs e)
        {
            ucTaskThumbnailView.TitleVisible = true;
            tsmiThumbnailTitleShow.Check();
            UpdateMainWindowLayout();
        }

        private void tsmiThumbnailTitleHide_Click(object sender, EventArgs e)
        {
            ucTaskThumbnailView.TitleVisible = false;
            tsmiThumbnailTitleHide.Check();
            UpdateMainWindowLayout();
        }

        private void tsmiThumbnailTitleTop_Click(object sender, EventArgs e)
        {
            ucTaskThumbnailView.TitleLocation = ThumbnailTitleLocation.Top;
            tsmiThumbnailTitleTop.Check();
            UpdateMainWindowLayout();
        }

        private void tsmiThumbnailTitleBottom_Click(object sender, EventArgs e)
        {
            ucTaskThumbnailView.TitleLocation = ThumbnailTitleLocation.Bottom;
            tsmiThumbnailTitleBottom.Check();
            UpdateMainWindowLayout();
        }

        private void UpdateMainWindowLayout()
        {
            Refresh();
        }

        private void tsmiThumbnailSize_Click(object sender, EventArgs e)
        {
            using (var form = new ThumbnailSizeForm(ucTaskThumbnailView.ThumbnailSize) { Icon = Icon })
            {
                if (form.ShowDialog(this) == DialogResult.OK)
                {
                    ucTaskThumbnailView.ThumbnailSize = form.ThumbnailSize;
                    UpdateMainWindowLayout();
                }
            }
        }

        private void tsmiOpenFile_Click(object sender, EventArgs e)
        {
            SelectedPanel?.OpenFile(false);
        }

        private void tsmiOpenFolder_Click(object sender, EventArgs e)
        {
            SelectedPanel?.OpenFile(true);
        }

        private void tsmiOpenURL_Click(object sender, EventArgs e)
        {
            SelectedPanel?.OpenUrl();
        }

        private void tsmiEdit_Click(object sender, EventArgs e)
        {
            SelectedPanel?.EditImage();
        }

        private void tsmiCopyURL_Click(object sender, EventArgs e)
        {
            ClipboardService.SetText(SelectedTask?.Info?.Url);
        }

        private void tsmiCopyFile_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(SelectedTask?.Info.FilePath))
                try
                {
                    var files = new StringCollection
                    {
                        SelectedTask?.Info?.FilePath
                    };
                    Clipboard.SetFileDropList(files);
                }
                catch
                {
                }
        }

        private void tsmiCopyImage_Click(object sender, EventArgs e)
        {
            var img = SelectedPanel?.GetImage();
            if (img != null) ClipboardService.ClipSetImage(img, false);
        }

        private void tsmiCopyFilePath_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(SelectedTask?.Info.FilePath))
                try
                {
                    Clipboard.SetText(SelectedTask?.Info?.FilePath);
                }
                catch
                {
                }
        }

        private void tsmiCopyFileName_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(SelectedTask?.Info.FilePath))
                try
                {
                    Clipboard.SetText(Path.GetFileNameWithoutExtension(SelectedTask?.Info?.FilePath));
                }
                catch
                {
                }
        }

        private void tsmiCopyFileNameWithExtension_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(SelectedTask?.Info.FilePath))
                try
                {
                    Clipboard.SetText(Path.GetFileName(SelectedTask?.Info?.FilePath));
                }
                catch
                {
                }
        }

        private void tsmiCopyFolder_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(SelectedTask?.Info.FilePath))
                try
                {
                    Clipboard.SetText(Path.GetDirectoryName(SelectedTask.Info.FilePath));
                }
                catch
                {
                }
        }

        private void tsmiDeleteSelectedFile_Click(object sender, EventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(SelectedTask?.Info?.FilePath)) File.Delete(SelectedTask?.Info?.FilePath);
            }
            catch
            {
            }
            CommonMethod.RemoveRecentTask(SelectedTask);
            ucTaskThumbnailView.RemovePanel(SelectedTask);
        }

        private void tsmiClearList_Click(object sender, EventArgs e)
        {
            CommonMethod.ClearAllTask();
            ucTaskThumbnailView.RemoveAllPanel();
        }

        private void cmsTaskInfo_Opening(object sender, CancelEventArgs e)
        {
            if (SelectedTask != null)
            {
                tsmiOpen.Visible = true;
                tsmiCopy.Visible = true;
                toolStripSeparator1.Visible = true;
                tsmiDeleteSelectedFile.Visible = true;
                tsmiClearList.Visible = true;
                tssUploadInfo1.Visible = true;

                tsmiOpenFile.Visible = !string.IsNullOrEmpty(SelectedTask.Info.FilePath);
                tsmiOpenFolder.Visible = !string.IsNullOrEmpty(SelectedTask.Info.FilePath);

                tsmiCopyFile.Visible = !string.IsNullOrEmpty(SelectedTask.Info.FilePath);
                tsmiCopyImage.Visible = !string.IsNullOrEmpty(SelectedTask.Info.FilePath);

                tsmiCopyURL.Visible = !string.IsNullOrEmpty(SelectedTask.Info.Url);
                tssCopy3.Visible = !string.IsNullOrEmpty(SelectedTask.Info.Url);

                tssOpen1.Visible = !string.IsNullOrEmpty(SelectedTask.Info.Url);
                tsmiOpenURL.Visible = !string.IsNullOrEmpty(SelectedTask.Info.Url);

                tsmiCopyFilePath.Visible = !string.IsNullOrEmpty(SelectedTask.Info.FilePath);
                tsmiCopyFileName.Visible = !string.IsNullOrEmpty(SelectedTask.Info.FilePath);
                tsmiCopyFileNameWithExtension.Visible = !string.IsNullOrEmpty(SelectedTask.Info.FilePath);
                tsmiCopyFolder.Visible = !string.IsNullOrEmpty(SelectedTask.Info.FilePath);
            }
            else
            {
                tsmiOpen.Visible = false;
                tsmiCopy.Visible = false;
                toolStripSeparator1.Visible = false;
                tsmiDeleteSelectedFile.Visible = false;
                tsmiClearList.Visible = false;
                tssUploadInfo1.Visible = false;
            }
        }
    }
}