﻿#region License Information (GPL v3)

/*
    ShareX - A program that allows you to take screenshots and share any file type
    Copyright (c) 2007-2020 ShareX Team

    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License
    as published by the Free Software Foundation; either version 2
    of the License, or (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.

    Optionally you can also view the license at <http://www.gnu.org/licenses/>.
*/

#endregion License Information (GPL v3)

using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    [DefaultEvent("ColorChanged")]
    public class ColorPicker : UserControl
    {
        private ColorBox colorBox;
        private ColorSlider colorSlider;

        private DrawStyle drawStyle;

        private MyColor selectedColor;

        public ColorPicker()
        {
            InitializeComponent();
            DrawStyle = DrawStyle.Hue;
            colorBox.ColorChanged += colorBox_ColorChanged;
            colorSlider.ColorChanged += colorSlider_ColorChanged;
        }

        public MyColor SelectedColor
        {
            get => selectedColor;
            private set
            {
                if (selectedColor != value)
                {
                    selectedColor = value;
                    colorBox.SelectedColor = selectedColor;
                    colorSlider.SelectedColor = selectedColor;
                }
            }
        }

        public DrawStyle DrawStyle
        {
            get => drawStyle;
            set
            {
                if (drawStyle != value)
                {
                    drawStyle = value;
                    colorBox.DrawStyle = value;
                    colorSlider.DrawStyle = value;
                }
            }
        }

        public bool CrosshairVisible
        {
            set
            {
                colorBox.CrosshairVisible = value;
                colorSlider.CrosshairVisible = value;
            }
        }

        public event ColorEventHandler ColorChanged;

        private void colorBox_ColorChanged(object sender, ColorEventArgs e)
        {
            selectedColor = e.Color;
            colorSlider.SelectedColor = SelectedColor;
            OnColorChanged();
        }

        private void colorSlider_ColorChanged(object sender, ColorEventArgs e)
        {
            selectedColor = e.Color;
            colorBox.SelectedColor = SelectedColor;
            OnColorChanged();
        }

        public void ChangeColor(Color color, ColorType colorType = ColorType.None)
        {
            SelectedColor = color;
            OnColorChanged(colorType);
        }

        private void OnColorChanged(ColorType colorType = ColorType.None)
        {
            if (ColorChanged != null) ColorChanged(this, new ColorEventArgs(SelectedColor, colorType));
        }

        #region Component Designer generated code

        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }

            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            colorBox = new ColorBox();
            colorSlider = new ColorSlider();
            SuspendLayout();
            //
            // colorBox
            //
            colorBox.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            colorBox.DrawStyle = DrawStyle.Hue;
            colorBox.Location = new System.Drawing.Point(0, 0);
            colorBox.Name = "colorBox";
            colorBox.Size = new System.Drawing.Size(258, 258);
            colorBox.TabIndex = 0;
            //
            // colorSlider
            //
            colorSlider.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            colorSlider.DrawStyle = DrawStyle.Hue;
            colorSlider.Location = new System.Drawing.Point(257, 0);
            colorSlider.Name = "colorSlider";
            colorSlider.Size = new System.Drawing.Size(32, 258);
            colorSlider.TabIndex = 1;
            //
            // ColorPicker
            //
            AutoSize = true;
            Controls.Add(colorBox);
            Controls.Add(colorSlider);
            Name = "ColorPicker";
            Size = new System.Drawing.Size(292, 261);
            ResumeLayout(false);
        }

        #endregion Component Designer generated code
    }
}