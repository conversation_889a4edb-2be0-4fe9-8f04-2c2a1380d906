// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class TablePattern : GridPattern
    {
        public new static readonly AutomationPattern Pattern = TablePatternIdentifiers.Pattern;
        public static readonly AutomationProperty ColumnHeadersProperty = TablePatternIdentifiers.ColumnHeadersProperty;
        public static readonly AutomationProperty RowHeadersProperty = TablePatternIdentifiers.RowHeadersProperty;

        public static readonly AutomationProperty RowOrColumnMajorProperty =
            TablePatternIdentifiers.RowOrColumnMajorProperty;

        private IUIAutomationTablePattern _pattern;


        private TablePattern(AutomationElement el, IUIAutomationTablePattern tablePattern,
            IUIAutomationGridPattern gridPattern, bool cached)
            : base(el, gridPattern, cached)
        {
            Debug.Assert(tablePattern != null);
            _pattern = tablePattern;
        }

        internal new static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            TablePattern result = null;
            if (pattern != null)
            {
                var gridPattern =
                    (IUIAutomationGridPattern) el.GetRawPattern(GridPattern.Pattern, cached);
                if (gridPattern != null)
                    result = new TablePattern(el, (IUIAutomationTablePattern) pattern,
                        gridPattern, cached);
            }

            return result;
        }
    }

    public class TableItemPattern : GridItemPattern
    {
        public new static readonly AutomationPattern Pattern = TableItemPatternIdentifiers.Pattern;

        public static readonly AutomationProperty ColumnHeaderItemsProperty =
            TableItemPatternIdentifiers.ColumnHeaderItemsProperty;

        public static readonly AutomationProperty RowHeaderItemsProperty =
            TableItemPatternIdentifiers.RowHeaderItemsProperty;

        private IUIAutomationTableItemPattern _pattern;


        private TableItemPattern(AutomationElement el, IUIAutomationTableItemPattern tablePattern,
            IUIAutomationGridItemPattern gridPattern, bool cached)
            : base(el, gridPattern, cached)
        {
            Debug.Assert(tablePattern != null);
            _pattern = tablePattern;
        }

        internal new static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            TableItemPattern result = null;
            if (pattern != null)
            {
                var gridPattern =
                    (IUIAutomationGridItemPattern) el.GetRawPattern(GridItemPattern.Pattern, cached);
                if (gridPattern != null)
                    result = new TableItemPattern(el, (IUIAutomationTableItemPattern) pattern,
                        gridPattern, cached);
            }

            return result;
        }
    }
}