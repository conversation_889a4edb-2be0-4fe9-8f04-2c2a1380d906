﻿using OCRTools.Common;
using System;
using System.Collections.Specialized;
using System.IO;

namespace ImageLib
{
    public class SouGouImageUpload
    {
        public const int MaxSize = 1024 * 1024 * 1;

        public static bool Enable { get; set; } = true;

        public static string GetResult(byte[] content)
        {
            var result = GetFromSouGou2(content);
            if (string.IsNullOrEmpty(result))
                result = GetFromSouGou(content);
            if (!string.IsNullOrEmpty(result))
            {
                result += "?1.png";
            }
            return result;
        }

        private static string GetFromSouGou2(byte[] content)
        {
            var result = "";
            var url = "https://proxy.jianzhuxuezhang.com/upload2";
            var file = new UploadFileInfo()
            {
                Name = "file",
                Filename = Guid.NewGuid().ToString().ToLower().Replace("-", "") + ".png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            try
            {
                result = UploadFileRequest.Post(url, new[] { file }, null, null);
            }
            catch { }
            return result;
        }

        private static string GetFromSouGou(byte[] content)
        {
            var result = "";
            var url = "https://pic.sogou.com/pic/upload_pic.jsp?uuid=" + Guid.NewGuid().ToString().ToLower();
            var file = new UploadFileInfo()
            {
                Name = "pic_path",
                Filename = Guid.NewGuid().ToString().ToLower() + ".png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            try
            {
                result = UploadFileRequest.Post(url, new[] { file }, null);
            }
            catch { }
            return result;
        }

    }
}
