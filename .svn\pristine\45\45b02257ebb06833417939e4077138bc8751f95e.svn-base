using System.IO;
using QiHe.CodeLib;

namespace ExcelLibrary.BinaryFileFormat
{
	public class SST : Record
	{
		public RichTextFormat[] RichTextFormatting;

		public int TotalOccurance;

		public int NumStrings;

		public FastSearchList<string> StringList;

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			TotalOccurance = binaryReader.ReadInt32();
			NumStrings = binaryReader.ReadInt32();
			StringList = new FastSearchList<string>(NumStrings);
			RichTextFormatting = new RichTextFormat[NumStrings];
			StringDecoder stringDecoder = new StringDecoder(this, binaryReader);
			for (int i = 0; i < NumStrings; i++)
			{
				StringList.Add(stringDecoder.ReadString(16, out RichTextFormatting[i]));
			}
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			NumStrings = StringList.Count;
			binaryWriter.Write(TotalOccurance);
			binaryWriter.Write(NumStrings);
			ContinuedRecords.Clear();
			Record record = this;
			foreach (string @string in StringList)
			{
				int stringDataLength = Record.GetStringDataLength(@string);
				if (memoryStream.Length + stringDataLength > 8224)
				{
					record.Data = memoryStream.ToArray();
					record.Size = (ushort)record.Data.Length;
					memoryStream = new MemoryStream();
					binaryWriter = new BinaryWriter(memoryStream);
					CONTINUE cONTINUE = new CONTINUE();
					ContinuedRecords.Add(cONTINUE);
					record = cONTINUE;
				}
				Record.WriteString(binaryWriter, @string, 16);
			}
			record.Data = memoryStream.ToArray();
			record.Size = (ushort)record.Data.Length;
		}

		public SST(Record record)
			: base(record)
		{
		}

		public SST()
		{
			Type = 252;
			StringList = new FastSearchList<string>();
		}

		public void decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			TotalOccurance = binaryReader.ReadInt32();
			NumStrings = binaryReader.ReadInt32();
			int numStrings = NumStrings;
			StringList = new FastSearchList<string>(numStrings);
			for (int i = 0; i < numStrings; i++)
			{
				StringList.Add(ReadString(binaryReader, 16));
			}
		}

		public void encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(TotalOccurance);
			binaryWriter.Write(NumStrings);
			foreach (string @string in StringList)
			{
				binaryWriter.Write(@string);
			}
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
