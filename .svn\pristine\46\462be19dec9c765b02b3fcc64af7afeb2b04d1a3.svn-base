﻿using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace OCRTools
{
    public class ButtonEx : Button
    {
        private bool m_bMouseDown;

        private bool m_bMouseOn;

        public ButtonEx()
        {
            SetStyle(ControlStyles.UserPaint, true);
            SetStyle(ControlStyles.ResizeRedraw, true);
            SetStyle(ControlStyles.AllPaintingInWmPaint, true);
            SetStyle(ControlStyles.OptimizedDoubleBuffer, true);

            SetStyle(ControlStyles.SupportsTransparentBackColor, true);
            base.FlatStyle = FlatStyle.Flat;
            BackColor = Color.Transparent;
            ForeColor = Color.FromArgb(125, 125, 125);
            FlatAppearance.BorderSize = 0;
            FlatAppearance.MouseOverBackColor = Color.Transparent;
            FlatAppearance.MouseDownBackColor = Color.Transparent;
        }

        public new FlatStyle FlatStyle
        {
            get => FlatStyle.Flat;
            set { }
        }

        protected override void OnMouseHover(EventArgs e)
        {
            if (!m_bMouseOn)
            {
                m_bMouseOn = true;
                Invalidate();
            }

            base.OnMouseHover(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            if (m_bMouseOn)
            {
                m_bMouseOn = false;
                Invalidate();
            }

            base.OnMouseLeave(e);
        }

        protected override void OnMouseDown(MouseEventArgs mevent)
        {
            if (!m_bMouseDown)
            {
                m_bMouseDown = true;
                Invalidate();
            }

            base.OnMouseDown(mevent);
        }

        protected override void OnMouseUp(MouseEventArgs mevent)
        {
            if (m_bMouseDown)
            {
                m_bMouseDown = false;
                Invalidate();
            }

            base.OnMouseUp(mevent);
        }

        //override onba
        protected override void OnAutoSizeChanged(EventArgs e)
        {
            BackColor = Color.Transparent;
            base.OnAutoSizeChanged(e);
        }

        protected override void OnPaint(PaintEventArgs pevent)
        {
            base.OnPaint(pevent);
            base.OnPaintBackground(pevent);
            var g = pevent.Graphics;
            g.SmoothingMode = SmoothingMode.HighQuality;
            var sf = new StringFormat
            {
                Alignment = StringAlignment.Center,
                LineAlignment = StringAlignment.Center
            };
            using (var lgb = new LinearGradientBrush(
                new Point(0, 1), new Point(0, Height),
                Color.White, Color.FromArgb(127, 0, 0, 0)))
            {
                g.DrawPath(
                    new Pen(lgb),
                    RenderHelper.CreateRoundedRectanglePath(new Rectangle(0, 1, Width - 1, Height - 2), 5)
                );
            }

            using (var lgb = new LinearGradientBrush(
                new Point(0, 1), new Point(0, Height),
                Color.FromArgb(255, 255, 255), Color.FromArgb(240, 240, 240)))
            {
                if (m_bMouseDown) lgb.LinearColors = new[] {Color.LightGray, Color.White};
                g.FillPath(
                    lgb,
                    RenderHelper.CreateRoundedRectanglePath(new Rectangle(0, 1, Width - 1, Height - 2), 5)
                );
            }

            using (var lgb = new LinearGradientBrush(
                new Point(0, 0), new Point(0, Height - 1),
                Color.FromArgb(200, 200, 200), m_bMouseOn ? Color.DodgerBlue : Color.FromArgb(127, 127, 127)))
            {
                g.DrawPath(
                    new Pen(lgb),
                    RenderHelper.CreateRoundedRectanglePath(new Rectangle(0, 0, Width - 1, Height - 2), 5)
                );
            }

            using (var sb = new SolidBrush(ForeColor))
            {
                g.DrawString(Text, Font, sb, DisplayRectangle, sf);
            }
        }
    }
}