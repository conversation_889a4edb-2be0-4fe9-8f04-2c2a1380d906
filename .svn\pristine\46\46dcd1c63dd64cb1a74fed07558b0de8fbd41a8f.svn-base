﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Web.Script.Serialization;

namespace TencentLocalOCR
{
    class Program
    {
        [DllImport("WxOcr.dll", CallingConvention = CallingConvention.Cdecl)]
        static extern bool wechat_ocr([MarshalAs(UnmanagedType.LPWStr)] string ocr_exe, [MarshalAs(UnmanagedType.LPWStr)] string wechat_dir, [MarshalAs(UnmanagedType.LPStr)] string imgfn, SetResultDelegate set_res);

        // 定义委托类型
        delegate void SetResultDelegate(IntPtr result);

        class StringResult
        {
            public string result_ = "";
            public void SetResult(IntPtr dt)
            {
                int length = 0;
                while (Marshal.ReadByte(dt, length) != 0) length++;
                byte[] byteArray = new byte[length];
                Marshal.Copy(dt, byteArray, 0, length);
                result_ = Encoding.UTF8.GetString(byteArray);
            }
            public string GetResult()
            {
                return result_;
            }
        }

        static JavaScriptSerializer JavaScriptSerializer = new JavaScriptSerializer();
        static void Main(string[] args)
        {
            OcrResult result = new OcrResult() { TextBlocks = new List<TextBlock>() };

            //#if DEBUG
            //args = new string[2];
            //args[0] = "WXWork";
            ////args[1] = "config";
            //args[1] = "C:\\Users\\<USER>\\Desktop\\table.jpg";
            //#endif

            //args = new string[3];
            //args[0] = "C:\\Users\\<USER>\\AppData\\Roaming\\Tencent\\WeChat\\XPlugin\\Plugins\\WeChatOCR\\7079\\extracted\\WeChatOCR.exe";
            //args[1] = "C:\\Program Files\\Tencent\\WeChat\\[*********]";

            ////args[0] = "C:\\Program Files\\Tencent\\QQNT\\resources\\app\\versions\\9.9.15-27254\\QQScreenShot\\Bin\\TencentOCR.exe";
            ////args[1] = "C:\\Program Files\\Tencent\\QQNT\\resources\\app\\versions\\9.9.15-27254";

            if (args.Length != 2)
            {
                result.StrRes = "参数异常，请联系技术支持！";
                var block = new TextBlock()
                {
                    Text = result.StrRes,
                    BoundingRect = new Rectangle(0, 0, 100, 30)
                };
                result.TextBlocks.Add(block);
            }
            else
            {
                var type = Equals(args[0], "QQ") ? 0 : (Equals(args[0], "WeiXin") ? 1 : (Equals(args[0], "WXWork") ? 2 : -1));
                if (type < 0)
                {
                    return;
                }
                var strOcrFile = type == 0 ? Properties.Settings.Default.StrQQNTOcr : (type == 1 ? Properties.Settings.Default.StrWxOcr : Properties.Settings.Default.StrWXWorkOcr);
                var strAppPath = type == 0 ? Properties.Settings.Default.StrQQNTApp : (type == 1 ? Properties.Settings.Default.StrWxApp : Properties.Settings.Default.StrWXWorkApp);

                try
                {
                    if (string.IsNullOrEmpty(strOcrFile) || string.IsNullOrEmpty(strAppPath)
                        || !File.Exists(strOcrFile) || !Directory.Exists(strAppPath))
                    {
                        new FormSetting() { NType = type }.Init();
                    }

                    strOcrFile = type == 0 ? Properties.Settings.Default.StrQQNTOcr : (type == 1 ? Properties.Settings.Default.StrWxOcr : Properties.Settings.Default.StrWXWorkOcr);
                    strAppPath = type == 0 ? Properties.Settings.Default.StrQQNTApp : (type == 1 ? Properties.Settings.Default.StrWxApp : Properties.Settings.Default.StrWXWorkApp);
                    if (Equals(args[1], "config") || string.IsNullOrEmpty(strOcrFile) || string.IsNullOrEmpty(strAppPath))
                    {
                        new FormSetting() { NType = type }.ShowDialog();
                        return;
                    }
                }
                catch (Exception oe)
                {
                }
                StringResult stringResult = new StringResult();
                SetResultDelegate setRes = new SetResultDelegate(stringResult.SetResult);
                bool success = wechat_ocr(strOcrFile, strAppPath, args[1], setRes);
                try
                {
                    if (success)
                    {
                        result.StrRes = stringResult.GetResult();
                        var objTmp = JavaScriptSerializer.Deserialize<QQRoot>(result.StrRes);
                        foreach (var qq in objTmp?.ocr_response)
                        {
                            var block = new TextBlock()
                            {
                                Text = qq.text,
                                BoundingRect = new Rectangle((int)qq.left, (int)qq.top, (int)(qq.right - qq.left), (int)(qq.bottom - qq.top))
                            };
                            result.TextBlocks.Add(block);
                        }
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine($"<Error>{oe.Message}</Error>");
                }
            }
            if (string.IsNullOrEmpty(result.StrRes))
            {
                result.StrRes = "处理失败，请检查参数配置！";
                var block = new TextBlock()
                {
                    Text = result.StrRes,
                    BoundingRect = new Rectangle(0, 0, 100, 30)
                };
                result.TextBlocks.Add(block);
            }
            Console.WriteLine($"<OcrResult>{JavaScriptSerializer.Serialize(result)}</OcrResult>");
        }

        [Serializable]
        class QQOcrResponse
        {
            [Obfuscation]
            public double left { get; set; }
            [Obfuscation]
            public double top { get; set; }
            [Obfuscation]
            public double right { get; set; }
            [Obfuscation]
            public double bottom { get; set; }
            [Obfuscation]
            public double rate { get; set; }
            [Obfuscation]
            public string text { get; set; }
        }
        [Serializable]
        class QQRoot
        {
            [Obfuscation]
            public int width { get; set; }
            [Obfuscation]
            public int height { get; set; }
            [Obfuscation]
            public List<QQOcrResponse> ocr_response { get; set; }
        }
        [Serializable]
        sealed class OcrResult
        {
            [Obfuscation]
            public List<TextBlock> TextBlocks { get; set; }

            [Obfuscation]
            public string StrRes { get; set; }
        }
        [Serializable]
        sealed class TextBlock
        {
            [Obfuscation]
            public Rectangle BoundingRect { get; set; }

            [Obfuscation]
            public string Text { get; set; }
        }
    }
}
