﻿using System.Drawing;

namespace OCRTools
{
    partial class FormUpdate
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.bgUpdate = new System.ComponentModel.BackgroundWorker();
            this.lblNowVersion = new System.Windows.Forms.Label();
            this.lblNew = new System.Windows.Forms.Label();
            this.lblDate = new System.Windows.Forms.Label();
            this.lblNewDate = new System.Windows.Forms.Label();
            this.proProcess = new System.Windows.Forms.ProgressBar();
            this.lblProcess = new System.Windows.Forms.Label();
            this.lnkNoUpdate = new System.Windows.Forms.LinkLabel();
            this.btnOK = new MAutoUpdate.Control.YButton();
            this.btnUpdateLater = new MAutoUpdate.Control.YButton();
            this.pnlUpdate = new System.Windows.Forms.Panel();
            this.rtbCon = new System.Windows.Forms.Label();
            this.pnlUpdate.SuspendLayout();
            this.SuspendLayout();
            // 
            // bgUpdate
            // 
            this.bgUpdate.WorkerReportsProgress = true;
            this.bgUpdate.WorkerSupportsCancellation = true;
            this.bgUpdate.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bgUpdate_DoWork);
            this.bgUpdate.RunWorkerCompleted += new System.ComponentModel.RunWorkerCompletedEventHandler(this.bgUpdate_RunWorkerCompleted);
            // 
            // lblNowVersion
            // 
            this.lblNowVersion.AutoSize = true;
            this.lblNowVersion.BackColor = System.Drawing.Color.Transparent;
            this.lblNowVersion.Font = CommonString.GetSysNormalFont(13F);
            this.lblNowVersion.ForeColor = System.Drawing.Color.DimGray;
            this.lblNowVersion.Location = new System.Drawing.Point(92, 75);
            this.lblNowVersion.Name = "lblNowVersion";
            this.lblNowVersion.Size = new System.Drawing.Size(74, 19);
            this.lblNowVersion.TabIndex = 3;
            this.lblNowVersion.Text = "最新版本：";
            // 
            // lblNew
            // 
            this.lblNew.AutoSize = true;
            this.lblNew.BackColor = System.Drawing.Color.Transparent;
            this.lblNew.Font = CommonString.GetSysNormalFont(17F);
            this.lblNew.ForeColor = System.Drawing.Color.Black;
            this.lblNew.Location = new System.Drawing.Point(167, 74);
            this.lblNew.Name = "lblNew";
            this.lblNew.Size = new System.Drawing.Size(0, 23);
            this.lblNew.TabIndex = 6;
            // 
            // lblDate
            // 
            this.lblDate.AutoSize = true;
            this.lblDate.BackColor = System.Drawing.Color.Transparent;
            this.lblDate.Font = CommonString.GetSysNormalFont(17F);
            this.lblDate.ForeColor = System.Drawing.Color.Black;
            this.lblDate.Location = new System.Drawing.Point(373, 74);
            this.lblDate.Name = "lblDate";
            this.lblDate.Size = new System.Drawing.Size(0, 23);
            this.lblDate.TabIndex = 9;
            // 
            // lblNewDate
            // 
            this.lblNewDate.AutoSize = true;
            this.lblNewDate.BackColor = System.Drawing.Color.Transparent;
            this.lblNewDate.Font = CommonString.GetSysNormalFont(13F);
            this.lblNewDate.ForeColor = System.Drawing.Color.DimGray;
            this.lblNewDate.Location = new System.Drawing.Point(298, 75);
            this.lblNewDate.Name = "lblNewDate";
            this.lblNewDate.Size = new System.Drawing.Size(74, 19);
            this.lblNewDate.TabIndex = 8;
            this.lblNewDate.Text = "更新日期：";
            // 
            // proProcess
            // 
            this.proProcess.BackColor = System.Drawing.SystemColors.ActiveCaptionText;
            this.proProcess.Location = new System.Drawing.Point(15, 13);
            this.proProcess.Name = "proProcess";
            this.proProcess.Size = new System.Drawing.Size(442, 29);
            this.proProcess.TabIndex = 13;
            this.proProcess.Visible = false;
            // 
            // lblProcess
            // 
            this.lblProcess.Font = CommonString.GetSysNormalFont(17F);
            this.lblProcess.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.lblProcess.Location = new System.Drawing.Point(15, 45);
            this.lblProcess.Name = "lblProcess";
            this.lblProcess.Size = new System.Drawing.Size(442, 36);
            this.lblProcess.TabIndex = 14;
            this.lblProcess.Text = "已下载0%";
            this.lblProcess.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.lblProcess.Visible = false;
            // 
            // lnkNoUpdate
            // 
            this.lnkNoUpdate.AutoSize = true;
            this.lnkNoUpdate.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lnkNoUpdate.Font = CommonString.GetSysBoldFont(12F);
            this.lnkNoUpdate.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline;
            this.lnkNoUpdate.LinkColor = System.Drawing.Color.Red;
            this.lnkNoUpdate.Location = new System.Drawing.Point(440, 359);
            this.lnkNoUpdate.Name = "lnkNoUpdate";
            this.lnkNoUpdate.Size = new System.Drawing.Size(68, 34);
            this.lnkNoUpdate.TabIndex = 15;
            this.lnkNoUpdate.TabStop = true;
            this.lnkNoUpdate.Text = "升级失败？\r\n手动升级！";
            this.lnkNoUpdate.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lnkNoUpdate_LinkClicked);
            // 
            // btnOK
            // 
            this.btnOK.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(26)))), ((int)(((byte)(173)))), ((int)(((byte)(25)))));
            this.btnOK.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnOK.EnterImage = null;
            this.btnOK.Font = CommonString.GetSysBoldFont(15F);
            this.btnOK.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.btnOK.IsColorChange = true;
            this.btnOK.IsFontChange = false;
            this.btnOK.Location = new System.Drawing.Point(293, 351);
            this.btnOK.MoveColor = System.Drawing.Color.FromArgb(((int)(((byte)(26)))), ((int)(((byte)(173)))), ((int)(((byte)(25)))));
            this.btnOK.MoveFontColor = System.Drawing.Color.White;
            this.btnOK.Name = "btnOK";
            this.btnOK.NormalColor = System.Drawing.Color.FromArgb(((int)(((byte)(26)))), ((int)(((byte)(173)))), ((int)(((byte)(25)))));
            this.btnOK.NormalFontColor = System.Drawing.Color.White;
            this.btnOK.Size = new System.Drawing.Size(141, 45);
            this.btnOK.TabIndex = 34;
            this.btnOK.Tag = "down";
            this.btnOK.Text = "立即更新";
            this.btnOK.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnUpdateLater
            // 
            this.btnUpdateLater.BackColor = System.Drawing.Color.Silver;
            this.btnUpdateLater.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnUpdateLater.EnterImage = null;
            this.btnUpdateLater.Font = CommonString.GetSysNormalFont(15F);
            this.btnUpdateLater.ForeColor = System.Drawing.Color.Black;
            this.btnUpdateLater.IsColorChange = true;
            this.btnUpdateLater.IsFontChange = false;
            this.btnUpdateLater.Location = new System.Drawing.Point(135, 351);
            this.btnUpdateLater.MoveColor = System.Drawing.Color.Silver;
            this.btnUpdateLater.MoveFontColor = System.Drawing.Color.Black;
            this.btnUpdateLater.Name = "btnUpdateLater";
            this.btnUpdateLater.NormalColor = System.Drawing.Color.Silver;
            this.btnUpdateLater.NormalFontColor = System.Drawing.Color.Black;
            this.btnUpdateLater.Size = new System.Drawing.Size(141, 45);
            this.btnUpdateLater.TabIndex = 33;
            this.btnUpdateLater.Text = "下次提醒我";
            this.btnUpdateLater.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnUpdateLater.Click += new System.EventHandler(this.btnUpdateLater_Click);
            // 
            // pnlUpdate
            // 
            this.pnlUpdate.Controls.Add(this.proProcess);
            this.pnlUpdate.Controls.Add(this.lblProcess);
            this.pnlUpdate.Location = new System.Drawing.Point(65, 314);
            this.pnlUpdate.Name = "pnlUpdate";
            this.pnlUpdate.Size = new System.Drawing.Size(468, 87);
            this.pnlUpdate.TabIndex = 35;
            // 
            // rtbCon
            // 
            this.rtbCon.Font = CommonString.GetSysNormalFont(20F);
            this.rtbCon.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right | System.Windows.Forms.AnchorStyles.Bottom;
            this.rtbCon.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.rtbCon.Location = new System.Drawing.Point(99, 107);
            this.rtbCon.Name = "rtbCon";
            this.rtbCon.Size = new System.Drawing.Size(386, 202);
            this.rtbCon.TabIndex = 36;
            this.rtbCon.Text = "label1";
            this.rtbCon.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // FormUpdate
            // 
            this.ClientSize = new System.Drawing.Size(576, 428);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnUpdateLater);
            this.Controls.Add(this.lnkNoUpdate);
            this.Controls.Add(this.lblDate);
            this.Controls.Add(this.lblNew);
            this.Controls.Add(this.lblNewDate);
            this.Controls.Add(this.lblNowVersion);
            this.Controls.Add(this.pnlUpdate);
            this.Controls.Add(this.rtbCon);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FormUpdate";
            this.Resizable = false;
            this.Text = "发现新版本！";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FormUpdate_FormClosing);
            this.Load += new System.EventHandler(this.FormUpdate_Load);
            this.pnlUpdate.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.ComponentModel.BackgroundWorker bgUpdate;
        private System.Windows.Forms.Label lblNowVersion;
        private System.Windows.Forms.Label lblNew;
        private System.Windows.Forms.Label lblDate;
        private System.Windows.Forms.Label lblNewDate;
        private System.Windows.Forms.ProgressBar proProcess;
        private System.Windows.Forms.Label lblProcess;
        private System.Windows.Forms.LinkLabel lnkNoUpdate;
        private MAutoUpdate.Control.YButton btnOK;
        private MAutoUpdate.Control.YButton btnUpdateLater;
        private System.Windows.Forms.Panel pnlUpdate;
        private System.Windows.Forms.Label rtbCon;
    }
}