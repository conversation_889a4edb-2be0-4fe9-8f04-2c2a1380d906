﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace ImageLib
{
    public class ImageHelper
    {
        private const string StrUrlStart = "http";
        static List<int> listProcessEntity = new List<int>() { 1, 2, 3, 4, 5, 6 };

        public static string GetResult(byte[] content, string fileExt)
        {
            var result = string.Empty;
            if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
            {
                result = ALiYunUpload.GetResult(content);
            }
            if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
            {
                result = SouGouImageUpload.GetResult(content);
            }
            if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
            {
                result = _360ImageUpload.GetResult(content);
            }
            if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
            {
                result = TinyPngUpload.GetResultUrl(content);
            }
            if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
            {
                result = WebResizerUpload.GetResult(content);
            }
            if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
            {
                result = Net126Upload.GetResult(content);
            }
            return result;
        }
    }
}
