﻿[Setup]
AIVersion=8.5
AIEdition=0
GUID={1CBE9515-01A7-41B8-85E0-E8705CF9D29D}
AppName=SmartOldFish - OCR文字识别助手
AppVersion=
AppDescription=一款集截图、贴图、文字识别于一体的高效率生产力工具！
CompanyName=SmartOldFish       官网：https://ocr.oldfish.cn
WebSite=
SupportLink=
InstallLevel=0
UpgradeMode=0
IfInstalled=2
RunAsAdmin=0
Windows XP=1
Windows Vista=1
Windows 7=1
Windows 8=1
Windows 8.1=1
Windows 10=1
Windows 11=1
Windows Server 2008=1
Windows Server 2008 R2=1
Windows Server 2012=1
Windows Server 2012 R2=1
Windows Server 2016=1
Windows Server 2019=1
Windows Server 2022=1
SystemType=0
Internet=0
CloseApp=0
CloseAppFile=File.exe
CloseAppText=App Name
PackageType=0
SetupFolder=D:\Code\CatchTools\bin\Target\package
SetupFileName=Setup-360
IconFile=D:\Code\CatchTools\ico.ico
SourceDir=D:\Code\CatchTools\bin\Release\Confused
InstallDir=<AppData>\OCR
MainExe=<InstallDir>\OCR Recognition Assistant.exe
ProgramGroup=<AppName>
Uninstall=1
ShowAddRemove=1
SilentUninstall=0
UninstallForce=0
UninstallRestart=0
UninstallSettings=1
UninstallSettingsCommand=rmdir /S /Q "<InstallDir>"
VisitUninstallPage=0
UninstallURL=http://www.website.com/uninstall.html
UninstallCloseMainExe=1
UninstallCloseApp=0
UninstallCloseAppFile=File.exe
UninstallCloseAppText=App
Updater=0
UpdateURL=http://www.website.com/update.txt
CheckUpdateBefore=0
CheckUpdaterTitle=0
CheckUseDownloader=0
UpdaterNewDialog=0
AllowChangeUpdate=0
AutoCheckUpdate=0
UpdateFrequency=0
AutoSilentUpdate=0
CheckUpdateOnLaunch=0
CheckSmartNotification=0
UpdateSmartMethod=0
CheckUpdatePassParam=0
CheckUpdaterParam=?id=<AppVersion>
UpdaterParameters=
LaunchOnStatup=0
SelectFolderMode=0
AltInstallDir=<AppData>\<AppName>
DataExtractParam=-o"<InstallDir>" -aoa
UninstallFileName=Uninstall.exe
LangIDMethod=0
AllowInstallIfNoSpace=0
AllowInstallIfPrereqFailed=0
SetupParameters=
Theme=2
WizardBitmap=<AIDir>\Bitmaps\Wizard\01.bmp
HeaderBitmap=<AIDir>\Bitmaps\Header\01.bmp
LogoBitmap=D:\Code\CatchTools\bin\Target\package\01.bmp
DialogWelcome=1
DialogLicense=0
DialogReadme=0
DialogUserInfo=0
DialogDestinationFolder=1
DialogAdditionalTasks=1
DialogReady=0
DialogFinish=1
ShowPublisher=1
HideFileNames=1
Beep=1
License=
Readme=
AcceptAgreement=0
Languages=Chinese,
DefaultLanguage=Chinese
AutoDetectLanguage=0
InfoUserName=0
InfoUserNameReq=0
InfoCompany=0
InfoCompanyReq=0
InfoCustom=0
InfoCustomReq=0
InfoCustomName=
InfoCustomType=0
InfoSerial=0
InfoSerialReq=0
SerialsCount=0
InfoCustomBox=0
InfoCustomBoxReq=0
InfoCustomBoxName=
CustomComboBox=0
CustomComboBoxName=
CustomComboBoxItems=
CustomComboDefItem=
ComboBoxDisableOnUpgrade=0
CustomComponents=0
CustomComponentsText=
CustomComponentsDesc=
CustomComponentsFull=0
CalcCompoSize=0
CalcReqSizeMethod=0
CompoMustSelect=0
RestartComputer=0
LaunchFile=1
LaunchFileName=<InstallDir>\OCR Recognition Assistant.exe
LaunchFileChecked=1
LaunchFileAdmin=0
CustomAction=0
CustomActionName=Visit Web Site
CustomActionCommand=<WebSite>
CustomActionChecked=0
CustomActionAdmin=0
FinishCurrentDir=0
TotalFiles=1

[GUI]
LogoAutoSize=0
LogoTransparent=0
LogoStretch=0
LogoProportional=1
FontName=Verdana

[Options]
OptionUpdateFileVersion=0
OptionCompressEngine=0
OptionEncryptData=0
OptionAutoVersionUpdate=0
OptionAutoVersionUseFile=0
OptionAutoVersionSet=0
OptionExcludeHiddenFiles=1
OptionExcludeEmptyFolders=1
OptionExcludeExtensions=
OptionNoTreeView=0
OptionRelativePath=0
OptionSignFile=0
OptionSignFileAll=0
OptionSignCommand=/K signtool sign /f MyCert.pfx "<SetupExe>"  

[Files]
0=<AIDir>\Uninstall.exe?<InstallDir>\Uninstall.exe?1?1

[Shortcuts]
0=<ProgramGroup>*?SmartOldFish - OCR文字识别助手*?<InstallDir>\OCR Recognition Assistant.exe*?*?*?<InstallDir>*?*?0*?Normal
1=<Desktop>*?SmartOldFish - OCR文字识别助手*?<InstallDir>\OCR Recognition Assistant.exe*?*?*?<InstallDir>*?*?0*?Normal
2=<ProgramGroup>*?卸载 SmartOldFish - OCR文字识别助手*?<InstallDir>\Uninstall.exe*?*?*?<InstallDir>*?*?0*?Normal

[FileOptions]
0=OCR Recognition Assistant.pdb?D

