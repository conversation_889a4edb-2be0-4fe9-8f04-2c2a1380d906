﻿using mshtml;
using OCRTools;
using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Linq;
using System.Reflection;

namespace ImageLib
{
    public class ImageHelper
    {
        private const string StrUrlStart = "http";
        private const int maxTimeOut = 10000;
        //private static List<string> lstUploadType = new List<string>() { "SouGou" };
        private static List<string> lstUploadType = new List<string>() { "BaiDu", "360", "Tencent", "SouGou", "ALiYun", "TinyPng", "WebResizer", "126" };

        public static void InitImageValidateParam()
        {
            //var header = new Dictionary<string, string>() { { "Referer", "https://servicewechat.com/wx38ffc01ad1452578/96/page-frame.html" } };
            //var value = new Dictionary<string, string>() { };
            //var param = new ImgValidateParam()
            //{
            //    url = "https://wx1.scan.dockerwork.com/scanner/api/check/image?origin=scanner",
            //    header = CommonString.JavaScriptSerializer.Serialize(header),
            //    post = CommonString.JavaScriptSerializer.Serialize(value),
            //    name = "file",
            //    success = "合规",
            //    validate = "\"合规\"",
            //    maxSize = 1024
            //};
            //var html = CommonString.JavaScriptSerializer.Serialize(param);
            //Console.WriteLine(html);

            CommonString.UploadParam = OcrHelper.GetServerConfig<List<ImgValidateParam>>("imgScanner");
        }

        public static bool IsImageSafe(OcrProcessEntity processEntity)
        {
            var result = true;
            var lstParam = CommonString.UploadParam?.Where(p => !string.IsNullOrEmpty(p.url) && p.enable)
                .Select(param => new TaskParam() { Param2 = param, Param3 = processEntity.Byts }).ToList();
            if (lstParam?.Count > 0)
                result = CommonTask<bool>.GetFastestValidResult(lstParam, GetValidateResultByType, 3, maxTimeOut, null);
            return result;
        }

        private static bool GetValidateResultByType(TaskParam param)
        {
            var result = true;
            try
            {
                var validate = (ImgValidateParam)param.Param2;
                var byts = (byte[])param.Param3;
                if (validate.maxSize > 0 && byts.Length / 1024 > validate.maxSize)
                {
                    return result;
                }

                var file = new UploadFileInfo()
                {
                    Name = validate.name,
                    Filename = "1.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(byts)
                };
                NameValueCollection heder = new NameValueCollection();
                if (!string.IsNullOrEmpty(validate.header))
                {
                    var dicTmp = CommonString.JavaScriptSerializer.Deserialize<Dictionary<string, string>>(validate.header);
                    foreach (var item in dicTmp)
                    {
                        heder.Add(item.Key, item.Value);
                    }
                }
                NameValueCollection value = new NameValueCollection();
                if (!string.IsNullOrEmpty(validate.post))
                {
                    var dicTmp = CommonString.JavaScriptSerializer.Deserialize<Dictionary<string, string>>(validate.post);
                    foreach (var item in dicTmp)
                    {
                        value.Add(item.Key, item.Value);
                    }
                }
                var html = UploadFileRequest.Post(validate.url, new[] { file }, value, heder);
                if (html?.Contains(validate.success) == true)
                    result = html.Contains(validate.validate);
            }
            catch { }
            return result;
        }

        static bool IsValidResult(string result) => !string.IsNullOrEmpty(result) && result.ToLower().StartsWith(StrUrlStart);

        //public static void TestMax()
        //{
        //    var lstFile = Directory.GetFiles("C:\\Users\\<USER>\\Desktop\\QQ\\Test").ToList().OrderBy(p => p);
        //    foreach (var item in lstFile)
        //    {
        //        Console.WriteLine("==============" + item.Replace("C:\\Users\\<USER>\\Desktop\\Test\\", "").Replace(".png", "MB") + "==============");
        //        var byts = File.ReadAllBytes(item);
        //        var lstParam = lstUploadType.Select(dns => new TaskParam() { Param1 = dns, Param2 = byts }).ToList();
        //        foreach (var param in lstParam)
        //        {
        //            var result = GetResultByType(param);
        //            Console.WriteLine(string.Format("{0}:{1}", param.Param1, result));
        //        }
        //    }
        //}

        private static string GetResultByType(TaskParam param)
        {
            string strTmp = string.Empty;
            var byts = (byte[])param.Param2;
            try
            {
                switch (param.Param1)
                {
                    case "ALiYun":
                        if (byts.Length < ALiYunUpload.MaxSize)
                            strTmp = ALiYunUpload.GetResult(byts);
                        break;
                    case "Tencent":
                        strTmp = TencentUpload.GetResult(byts);
                        break;
                    case "SouGou":
                        if (byts.Length < SouGouImageUpload.MaxSize)
                            strTmp = SouGouImageUpload.GetResult(byts);
                        break;
                    case "360":
                        if (byts.Length < _360ImageUpload.MaxSize)
                            strTmp = _360ImageUpload.GetResult(byts);
                        break;
                    case "TinyPng":
                        strTmp = OCRTools.ImageLib.TinyPngUpload.GetResultUrl(byts);
                        break;
                    case "WebResizer":
                        if (byts.Length < WebResizerUpload.MaxSize)
                            strTmp = WebResizerUpload.GetResult(byts);
                        break;
                    case "126":
                        strTmp = Net126Upload.GetResult(byts);
                        break;
                    case "BaiDu":
                        strTmp = BaiDuUpload.GetResult(byts);
                        break;
                }
                //Console.WriteLine(param.Param1 + ":" + strTmp);
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return strTmp;
        }

        public static string GetResult(byte[] content, string fileExt)
        {
            var lstParam = lstUploadType.Select(dns => new TaskParam() { Param1 = dns, Param2 = content }).ToList();
            var result = CommonTask<string>.GetFastestValidResult(lstParam, GetResultByType, 3, maxTimeOut, IsValidResult);
            return result;
        }
    }
}

[Obfuscation]
public class ImgValidateParam
{
    [Obfuscation]
    public string url { get; set; }

    [Obfuscation]
    public string name { get; set; }

    [Obfuscation]
    public string header { get; set; }

    [Obfuscation]
    public string post { get; set; }

    [Obfuscation]
    public string success { get; set; }

    [Obfuscation]
    public string validate { get; set; }

    [Obfuscation]
    public int maxSize { get; set; }

    [Obfuscation]
    public bool enable { get; set; }
}
