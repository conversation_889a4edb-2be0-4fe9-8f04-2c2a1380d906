using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Globalization;
using System.Runtime.Serialization;
using System.Windows.Forms;

namespace OCRTools
{
    internal class DrawCatch : DrawObject
    {
        private Rectangle _rectangle;

        public DrawCatch()
            : this(0, 0, 1, 1)
        {
        }

        public DrawCatch(int x, int y, int width, int height)
        {
            _rectangle.X = x;
            _rectangle.Y = y;
            _rectangle.Width = width;
            _rectangle.Height = height;
            Initialize();
        }

        public DrawCatch(Rectangle rect)
        {
            _rectangle = rect;
            Initialize();
        }

        public override Rectangle Rectangle
        {
            get => _rectangle;
            set => _rectangle = value;
        }

        public override int HandleCount => 8;

        public override DrawToolType NoteType => DrawToolType.Catch;

        public bool IsCatchMove { get; set; } = false;


        public override Rectangle GetBoundingBox()
        {
            var normalizedRectangle = Rectangle.GetNormalizedRectangle();
            if (IsSelected)
            {
                var num = 3.DpiValue();
                normalizedRectangle.Inflate(num, num);
            }

            return normalizedRectangle;
        }

        public Rectangle GetAddBound()
        {
            var normalizedRectangle = AddRectangle.GetNormalizedRectangle();
            normalizedRectangle.Inflate(1, 1);
            return normalizedRectangle;
        }

        public void ChangeRect(int x, int y, int width, int height)
        {
            _rectangle.X = x;
            _rectangle.Y = y;
            _rectangle.Width = width;
            _rectangle.Height = height;
        }

        public override DrawObject Clone()
        {
            var drawCatch = new DrawCatch
            {
                _rectangle = _rectangle
            };
            FillDrawObjectFields(drawCatch);
            return drawCatch;
        }

        protected Brush CreateBackBrush()
        {
            return new SolidBrush(Color.FromArgb(80, 40, 40, 40));
        }

        public override void Draw(Graphics g)
        {
            g.SmoothingMode = SmoothingMode.HighSpeed;
            var normalizedRectangle = Rectangle.GetNormalizedRectangle();
            if (normalizedRectangle.IsLimt())
            {
                StaticValue.catchRectangle = normalizedRectangle;
                if (!StaticValue.CanCatch)
                {
                    using (var brush = CreateBackBrush())
                    {
                        using (var pen = new Pen(CustomColor.CheckColor, 5f))
                        {
                            using (Brush brush2 = new SolidBrush(CustomColor.CheckColor))
                            {
                                using (var graphicsPath = new GraphicsPath())
                                {
                                    graphicsPath.AddRectangle(normalizedRectangle);
                                    graphicsPath.AddRectangle(StaticValue.ScreenRectangle);
                                    g.FillPath(brush, graphicsPath);
                                    g.DrawRectangle(pen, normalizedRectangle);
                                    for (var i = 1; i <= HandleCount; i++)
                                        g.FillRectangle(brush2, GetHandleRectangle2(i));
                                }
                            }
                        }
                    }

                    DrawAreaText(g, normalizedRectangle.SizeOffset(1));
                }
                else
                {
                    StaticValue.CanCatch = false;
                }
            }
        }

        private void DrawAreaText(Graphics g, Rectangle area)
        {
            g.InterpolationMode = InterpolationMode.HighQualityBilinear;
            g.CompositingQuality = CompositingQuality.HighQuality;
            g.SmoothingMode = SmoothingMode.HighQuality;
            g.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
            var text = string.Format("宽:{0} px,高:{1} px", area.Width, area.Height);
            //string text = $"X: {area.X} Y: {area.Y} W: {area.Width} H: {area.Height}";
            var font = new Font("Arial", 10f, FontStyle.Bold);
            var num = 5.DpiValue();
            var size = g.MeasureString(text, font).ToSize();
            var point = area.Y - num - size.Height < StaticValue.ScreenRectangle.Y
                ? new Point(area.X + num, area.Y)
                : new Point(area.X, area.Y - num - size.Height);
            if (point.X + size.Width >= StaticValue.ScreenRectangle.Width)
                point.X = StaticValue.ScreenRectangle.Width - size.Width;
            if (point.Y + size.Height >= StaticValue.ScreenRectangle.Height)
                point.Y = StaticValue.ScreenRectangle.Height - size.Height;
            g.DrawTextWithShadow(text,
                (AddRectangle = new Rectangle(point.X, point.Y, size.Width, size.Height)).Location, font, Brushes.White,
                Brushes.Black);
        }

        protected void SetRectangle(int x, int y, int width, int height)
        {
            _rectangle.X = x;
            _rectangle.Y = y;
            _rectangle.Width = width;
            _rectangle.Height = height;
        }

        public override Point GetHandle(int handleNumber)
        {
            var num = _rectangle.X + _rectangle.Width / 2;
            var num2 = _rectangle.Y + _rectangle.Height / 2;
            var x = _rectangle.X;
            var y = _rectangle.Y;
            switch (handleNumber)
            {
                case 1:
                    x = _rectangle.X;
                    y = _rectangle.Y;
                    break;
                case 2:
                    x = num;
                    y = _rectangle.Y;
                    break;
                case 3:
                    x = _rectangle.Right;
                    y = _rectangle.Y;
                    break;
                case 4:
                    x = _rectangle.Right;
                    y = num2;
                    break;
                case 5:
                    x = _rectangle.Right;
                    y = _rectangle.Bottom;
                    break;
                case 6:
                    x = num;
                    y = _rectangle.Bottom;
                    break;
                case 7:
                    x = _rectangle.X;
                    y = _rectangle.Bottom;
                    break;
                case 8:
                    x = _rectangle.X;
                    y = num2;
                    break;
            }

            return new Point(x, y);
        }

        public override int HitTest(Point point)
        {
            if (Selected)
                for (var i = 1; i <= HandleCount; i++)
                    if (GetHandleRectangle(i).Contains(point))
                        return i;
            if (PointInObject(point)) return 0;
            return -1;
        }

        public override int HitCatch(Point point)
        {
            for (var i = 1; i <= HandleCount; i++)
                if (GetHandleRectangle(i).Contains(point))
                    return i;
            if (PointInObject(point)) return 0;
            return -1;
        }

        public override bool PointInObject(Point point)
        {
            var normalizedRectangle = Rectangle.GetNormalizedRectangle();
            var rectangle = new Rectangle(normalizedRectangle.X - 5, normalizedRectangle.Y - 5,
                normalizedRectangle.Width + 10, normalizedRectangle.Height + 10);
            var rectangle2 = new Rectangle(normalizedRectangle.X + 5, normalizedRectangle.Y + 5,
                normalizedRectangle.Width - 10, normalizedRectangle.Height - 10);
            if (IsCatchMove)
            {
                if (rectangle.Contains(point)) return true;
                return false;
            }

            if (rectangle.Contains(point) && !rectangle2.Contains(point)) return true;
            return false;
        }

        public override Cursor GetHandleCursor(int handleNumber)
        {
            switch (handleNumber)
            {
                case 1:
                    return CursorEx.NWSE;
                case 2:
                    return CursorEx.NS;
                case 3:
                    return CursorEx.NESW;
                case 4:
                    return CursorEx.EW;
                case 5:
                    return CursorEx.NWSE;
                case 6:
                    return CursorEx.NS;
                case 7:
                    return CursorEx.NESW;
                case 8:
                    return CursorEx.EW;
                default:
                    return CursorEx.Cross;
            }
        }

        public override void MoveHandleTo(Point point, int handleNumber)
        {
            var num = Rectangle.Left;
            var num2 = Rectangle.Top;
            var num3 = Rectangle.Right;
            var num4 = Rectangle.Bottom;
            switch (handleNumber)
            {
                case 1:
                    num = point.X;
                    num2 = point.Y;
                    break;
                case 2:
                    num2 = point.Y;
                    break;
                case 3:
                    num3 = point.X;
                    num2 = point.Y;
                    break;
                case 4:
                    num3 = point.X;
                    break;
                case 5:
                    num3 = point.X;
                    num4 = point.Y;
                    break;
                case 6:
                    num4 = point.Y;
                    break;
                case 7:
                    num = point.X;
                    num4 = point.Y;
                    break;
                case 8:
                    num = point.X;
                    break;
            }

            SetRectangle(num, num2, num3 - num, num4 - num2);
        }

        public override bool IntersectsWith(Rectangle rectangle)
        {
            return Rectangle.IntersectsWith(rectangle);
        }

        public override void Move(int deltaX, int deltaY)
        {
            _rectangle.X += deltaX;
            _rectangle.Y += deltaY;
        }

        public override void Normalize()
        {
            _rectangle = DrawRectangle.GetNormalizedRectangle(_rectangle);
        }

        public override void SaveToStream(SerializationInfo info, int orderNumber)
        {
            info.AddValue(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "Rect", orderNumber), _rectangle);
            base.SaveToStream(info, orderNumber);
        }

        public override void LoadFromStream(SerializationInfo info, int orderNumber)
        {
            _rectangle = (Rectangle)info.GetValue(
                string.Format(CultureInfo.InvariantCulture, "{0}{1}", "Rect", orderNumber), typeof(Rectangle));
            base.LoadFromStream(info, orderNumber);
        }
    }
}