﻿using OCRTools.Common;

namespace OCRTools
{
    public class OcrProcessEntity
    {
        public byte[] Byts { get; set; }
        internal OcrType OcrType { get; set; }
        internal int GroupType { get; set; }
        public bool IsFile { get; set; }
        public string FileExt { get; set; }
        public bool IsFromLeftToRight { get; set; }
        public bool IsFromTopToDown { get; set; }
        internal TransLanguageTypeEnum From { get; set; }
        internal TransLanguageTypeEnum To { get; set; }
        public string ImgUrl { get; set; }
        public int? ProcessId { get; set; }

        public bool IsImgUrl => !string.IsNullOrEmpty(ImgUrl);

        public bool IsNeedUpload { get; set; }
        public bool IsShowLoading { get; internal set; }
        public bool IsSearch { get; internal set; }

        public ProcessBy ProcessBy { get; set; }
        public string Identity { get; internal set; }

        public bool IsLocal { get; set; }

        public int LocalOcrType { get; set; }

        public bool? IsSupportVertical { get; set; }
        public bool IsLongImage { get; set; }
    }
}