﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Drawing.Text;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;

namespace OCRTools.Common
{
    public class ImageProcessHelper
    {
        private const string STR_DOC_IMG_SPILT = "\"image\":\"";
        private const string STR_IMG_SPILT = "\"image\":\"";

        public static readonly string[] ImageFileExtensions =
            {"jpg", "jpeg", "png", "gif", "bmp", "ico", "tif", "tiff"};

        public static bool IsImagesEqual(Bitmap bmp1, Bitmap bmp2)
        {
            using (var unsafeBitmap1 = new UnsafeBitmap(bmp1))
            using (var unsafeBitmap2 = new UnsafeBitmap(bmp2))
            {
                return unsafeBitmap1 == unsafeBitmap2;
            }
        }

        public static Bitmap ResizeImage(Bitmap bmp, Size size, bool allowEnlarge, bool centerImage = true)
        {
            return ResizeImage(bmp, size.Width, size.Height, allowEnlarge, centerImage);
        }

        public static Bitmap ResizeImage(Bitmap bmp, int width, int height, bool allowEnlarge, bool centerImage = true)
        {
            return ResizeImage(bmp, width, height, allowEnlarge, centerImage, Color.Transparent);
        }

        public static Bitmap ResizeImage(Bitmap bmp, int width, int height, bool allowEnlarge, bool centerImage,
            Color backColor)
        {
            double ratio;
            int newWidth, newHeight;

            if (!allowEnlarge && bmp.Width <= width && bmp.Height <= height)
            {
                ratio = 1.0;
                newWidth = bmp.Width;
                newHeight = bmp.Height;
            }
            else
            {
                var ratioX = (double)width / bmp.Width;
                var ratioY = (double)height / bmp.Height;
                ratio = ratioX < ratioY ? ratioX : ratioY;
                newWidth = (int)(bmp.Width * ratio);
                newHeight = (int)(bmp.Height * ratio);
            }

            var newX = 0;
            var newY = 0;

            if (centerImage)
            {
                newX += (int)((width - bmp.Width * ratio) / 2);
                newY += (int)((height - bmp.Height * ratio) / 2);
            }

            var bmpResult = new Bitmap(width, height, PixelFormat.Format32bppArgb);
            bmpResult.SetResolution(bmp.HorizontalResolution, bmp.VerticalResolution);

            using (var g = Graphics.FromImage(bmpResult))
            {
                if (backColor.A > 0) g.Clear(backColor);

                g.SetHighQuality();
                g.DrawImage(bmp, newX, newY, newWidth, newHeight);
            }

            return bmpResult;
        }

        public static Bitmap ResizeImage(Bitmap bmp, int width, int height, Color color, bool isToCircle = true,
            bool isShadow = false, decimal shadowWidth = 8, bool isActive = false)
        {
            if (width < 1 || height < 1) // || (bmp.Width == width && bmp.Height == height))
                return bmp;

            var bmpResult = new Bitmap(width, height, PixelFormat.Format32bppArgb);
            bmpResult.SetResolution(bmp.HorizontalResolution, bmp.VerticalResolution);

            using (bmp)
            using (var g = Graphics.FromImage(bmpResult))
            {
                g.Clear(color);
                g.SetHighQuality();

                using (var ia = new ImageAttributes())
                {
                    ia.SetWrapMode(WrapMode.TileFlipXY);
                    g.DrawImage(bmp, new Rectangle(0, 0, width, height), 0, 0, bmp.Width, bmp.Height,
                        GraphicsUnit.Pixel, ia);
                }
            }

            if (isToCircle) bmpResult = ToCircle(bmpResult, color);

            if (isShadow || isActive)
                return DrawImageShadow(bmpResult, isToCircle, isShadow, (int)shadowWidth, isActive);
            return bmpResult;
        }

        private static Bitmap DrawImageShadow(Bitmap bmpResult, bool isToCircle, bool isShadow = false,
            int shadowWidth = 8, bool isActive = false)
        {
            var bmpTmp = new Bitmap(bmpResult.Width + (isShadow ? shadowWidth : 0) * 2,
                bmpResult.Height + (isShadow ? shadowWidth : 0) * 2);
            using (var g = Graphics.FromImage(bmpTmp))
            {
                g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                g.SmoothingMode = SmoothingMode.HighQuality;
                g.PixelOffsetMode = PixelOffsetMode.HighQuality;
                g.CompositingQuality = CompositingQuality.HighQuality;
                g.CompositingMode = CompositingMode.SourceOver;
                g.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
                var shadowMore = isActive ? 2 : 0;

                if (isShadow)
                {
                    var normalBaseColor =
                        isToCircle ? bmpResult.GetPixel(bmpResult.Width / 2, 1) : bmpResult.GetPixel(1, 1);
                    if (normalBaseColor.A == 255)
                    {
                        var baseColor = isActive ? StaticValue.ShadowActiveColor : normalBaseColor;
                        for (var i = 1; i <= shadowWidth + shadowMore; i++)
                        {
                            var borderColor =
                                Color.FromArgb((int)Math.Min(255 / shadowWidth * (isActive ? 3 : 1.5), 255),
                                    baseColor);
                            using (var pen = new Pen(borderColor, 1))
                            {
                                var rect = new Rectangle(i, i, bmpTmp.Width - i * 2, bmpTmp.Height - i * 2);
                                if (isToCircle)
                                    g.DrawEllipse(pen, rect);
                                else
                                    g.DrawRectangle(pen, rect);
                            }
                        }
                    }
                }

                g.DrawImage(bmpResult
                    , new Rectangle((isShadow ? shadowWidth : 0) + shadowMore,
                        (isShadow ? shadowWidth : 0) + shadowMore, bmpResult.Width - shadowMore * 2,
                        bmpResult.Height - shadowMore * 2)
                    , 0, 0, bmpResult.Width, bmpResult.Height, GraphicsUnit.Pixel);
                return bmpTmp;
            }
        }

        public static Bitmap ToCircle(Bitmap bitmap, Color color)
        {
            var bmpResult = new Bitmap(bitmap.Width, bitmap.Height, PixelFormat.Format32bppArgb);
            using (var g = Graphics.FromImage(bmpResult))
            {
                g.Clear(color);

                g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                g.SmoothingMode = SmoothingMode.HighQuality;
                g.PixelOffsetMode = PixelOffsetMode.HighQuality;
                g.CompositingQuality = CompositingQuality.HighQuality;
                g.CompositingMode = CompositingMode.SourceOver;
                g.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;

                using (var br = new TextureBrush(bitmap, WrapMode.Clamp,
                    new RectangleF(0, 0, bitmap.Width, bitmap.Height)))
                {
                    br.ScaleTransform(1, 1);
                    g.FillEllipse(br, new Rectangle(Point.Empty, bitmap.Size));
                }
            }

            return bmpResult;
        }

        public static Bitmap CreateCheckerPattern(int width, int height, Color checkerColor1, Color checkerColor2)
        {
            var bmp = new Bitmap(width * 2, height * 2);

            using (var g = Graphics.FromImage(bmp))
            using (Brush brush1 = new SolidBrush(checkerColor1))
            using (Brush brush2 = new SolidBrush(checkerColor2))
            {
                g.FillRectangle(brush1, 0, 0, width, height);
                g.FillRectangle(brush1, width, height, width, height);
                g.FillRectangle(brush2, width, 0, width, height);
                g.FillRectangle(brush2, 0, height, width, height);
            }

            return bmp;
        }

        public static Bitmap CreateCheckerPattern(int width, int height)
        {
            return CreateCheckerPattern(width, height, SystemColors.ControlLight, SystemColors.ControlLightLight);
        }

        public static Bitmap DrawCheckers(int width, int height)
        {
            return DrawCheckers(width, height, 10, SystemColors.ControlLight, SystemColors.ControlLightLight);
        }

        public static Bitmap DrawCheckers(int width, int height, int checkerSize, Color checkerColor1,
            Color checkerColor2)
        {
            var bmp = new Bitmap(width, height);

            using (var g = Graphics.FromImage(bmp))
            using (Image checker = CreateCheckerPattern(checkerSize, checkerSize, checkerColor1, checkerColor2))
            using (Brush checkerBrush = new TextureBrush(checker, WrapMode.Tile))
            {
                g.FillRectangle(checkerBrush, new Rectangle(0, 0, bmp.Width, bmp.Height));
            }

            return bmp;
        }

        public static Bitmap ResizeImageLimit(Bitmap bmp, Size size, bool isAlph = false)
        {
            return ResizeImageLimit(bmp, size.Width, size.Height, isAlph);
        }

        /// <summary>If image size is bigger than specified size then resize it and keep aspect ratio else return image.</summary>
        public static Bitmap ResizeImageLimit(Bitmap bmp, int width, int height, bool isAlph = false)
        {
            if (bmp.Width <= width && bmp.Height <= height) return bmp;

            var ratioX = (double)width / bmp.Width;
            var ratioY = (double)height / bmp.Height;

            if (ratioX < ratioY)
                height = (int)Math.Round(bmp.Height * ratioX);
            else if (ratioX > ratioY) width = (int)Math.Round(bmp.Width * ratioY);

            return ResizeImage(bmp, width, height, isAlph);
        }

        public static Bitmap LoadImage(string filePath)
        {
            try
            {
                if (!string.IsNullOrEmpty(filePath))
                {
                    filePath = GetAbsolutePath(filePath);

                    if (!string.IsNullOrEmpty(filePath) && IsImageFile(filePath) && File.Exists(filePath))
                    {
                        // http://stackoverflow.com/questions/788335/why-does-image-fromfile-keep-a-file-handle-open-sometimes
                        var bmp = (Bitmap)Image.FromStream(new MemoryStream(File.ReadAllBytes(filePath)));

                        RotateImageByExifOrientationData(bmp);

                        return bmp;
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            return null;
        }

        public static RotateFlipType RotateImageByExifOrientationData(Bitmap bmp, bool removeExifOrientationData = true)
        {
            var orientationId = 0x0112;
            var rotateType = RotateFlipType.RotateNoneFlipNone;

            if (bmp.PropertyIdList.Contains(orientationId))
            {
                var propertyItem = bmp.GetPropertyItem(orientationId);
                rotateType = GetRotateFlipTypeByExifOrientationData(propertyItem.Value[0]);

                if (rotateType != RotateFlipType.RotateNoneFlipNone)
                {
                    bmp.RotateFlip(rotateType);

                    if (removeExifOrientationData) bmp.RemovePropertyItem(orientationId);
                }
            }

            return rotateType;
        }

        private static RotateFlipType GetRotateFlipTypeByExifOrientationData(int orientation)
        {
            switch (orientation)
            {
                default:
                    return RotateFlipType.RotateNoneFlipNone;
                case 2:
                    return RotateFlipType.RotateNoneFlipX;
                case 3:
                    return RotateFlipType.Rotate180FlipNone;
                case 4:
                    return RotateFlipType.Rotate180FlipX;
                case 5:
                    return RotateFlipType.Rotate90FlipX;
                case 6:
                    return RotateFlipType.Rotate90FlipNone;
                case 7:
                    return RotateFlipType.Rotate270FlipX;
                case 8:
                    return RotateFlipType.Rotate270FlipNone;
            }
        }

        public static string GetAbsolutePath(string path)
        {
            path = ExpandFolderVariables(path);

            if (!Path.IsPathRooted(path)) // Is relative path?
                path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, path);

            return Path.GetFullPath(path);
        }

        public static T[] GetEnums<T>()
        {
            return (T[])Enum.GetValues(typeof(T));
        }

        public static string ExpandFolderVariables(string path)
        {
            if (!string.IsNullOrEmpty(path))
                try
                {
                    foreach (var specialFolder in GetEnums<Environment.SpecialFolder>())
                        path = path.Replace($"%{specialFolder}%", Environment.GetFolderPath(specialFolder),
                            StringComparison.OrdinalIgnoreCase);

                    path = Environment.ExpandEnvironmentVariables(path);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }

            return path;
        }

        public static string GetFilenameExtension(string filePath, bool includeDot = false,
            bool checkSecondExtension = true)
        {
            var extension = "";

            if (!string.IsNullOrEmpty(filePath))
            {
                var pos = filePath.LastIndexOf('.');

                if (pos >= 0)
                {
                    extension = filePath.Substring(pos + 1);

                    if (checkSecondExtension)
                    {
                        filePath = filePath.Remove(pos);
                        var extension2 = GetFilenameExtension(filePath, false, false);

                        if (!string.IsNullOrEmpty(extension2))
                            foreach (var knownExtension in new[] { "tar" })
                                if (extension2.Equals(knownExtension, StringComparison.OrdinalIgnoreCase))
                                {
                                    extension = extension2 + "." + extension;
                                    break;
                                }
                    }

                    if (includeDot) extension = "." + extension;
                }
            }

            return extension;
        }

        public static bool CheckExtension(string filePath, IEnumerable<string> extensions)
        {
            var ext = GetFilenameExtension(filePath);

            if (!string.IsNullOrEmpty(ext))
                return extensions.Any(x => ext.Equals(x, StringComparison.OrdinalIgnoreCase));

            return false;
        }

        public static bool IsImageFile(string filePath)
        {
            return CheckExtension(filePath, ImageFileExtensions);
        }

        /// <summary>
        ///     反色（指针）
        /// </summary>
        /// <param name="curBitmpap"></param>
        /// <returns></returns>
        public static Bitmap BitInverseByPointer(Bitmap curBitmpap)
        {
            if (curBitmpap != null)
                try
                {
                    var srcdat = curBitmpap.LockBits(new Rectangle(Point.Empty, curBitmpap.Size),
                        ImageLockMode.ReadWrite, PixelFormat.Format24bppRgb); // 锁定位图
                    unsafe // 不安全代码
                    {
                        var pix = (byte*)srcdat.Scan0; // 像素首地址
                        for (var i = 0; i < srcdat.Stride * srcdat.Height; i++) pix[i] = (byte)(255 - pix[i]);
                        curBitmpap.UnlockBits(srcdat); // 解锁
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }

            return curBitmpap;
        }

        /// <summary>
        ///     对比度增强（指针）
        /// </summary>
        /// <returns></returns>
        public static unsafe Bitmap BitContrastByPointer(Bitmap bitmap, int degree = 90)
        {
            if (bitmap != null)
                try
                {
                    var num2 = (100.0 + degree) / 100.0;
                    num2 *= num2;
                    var width = bitmap.Width;
                    var height = bitmap.Height;
                    var bitmapdata = bitmap.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.ReadWrite,
                        PixelFormat.Format24bppRgb);
                    var numPtr = (byte*)bitmapdata.Scan0;

                    var offset = bitmapdata.Stride - width * 3;
                    for (var i = 0; i < height; i++)
                    {
                        for (var j = 0; j < width; j++)
                        {
                            for (var k = 0; k < 3; k++)
                            {
                                var num = ((numPtr[k] / 255.0 - 0.5) * num2 + 0.5) * 255.0;
                                if (num < 0.0) num = 0.0;
                                if (num > 255.0) num = 255.0;
                                numPtr[k] = (byte)num;
                            }

                            numPtr += 3;
                        }

                        numPtr += offset;
                    }

                    bitmap.UnlockBits(bitmapdata);
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }

            return bitmap;
        }

        public static Bitmap CropBitmap(Bitmap bmp, Rectangle rect)
        {
            if (bmp != null && rect.X >= 0 && rect.Y >= 0 && rect.Width > 0 && rect.Height > 0 &&
                new Rectangle(0, 0, bmp.Width, bmp.Height).Contains(rect)) return bmp.Clone(rect, bmp.PixelFormat);

            return null;
        }

        public static Bitmap CombineImages(IEnumerable<Image> images, Orientation orientation = Orientation.Vertical,
            ImageCombinerAlignment alignment = ImageCombinerAlignment.LeftOrTop, int space = 0)
        {
            int width, height;
            var imageCount = images.Count();
            var spaceSize = space * (imageCount - 1);

            if (orientation == Orientation.Vertical)
            {
                width = images.Max(x => x.Width);
                height = images.Sum(x => x.Height) + spaceSize;
            }
            else
            {
                width = images.Sum(x => x.Width) + spaceSize;
                height = images.Max(x => x.Height);
            }

            var bmp = new Bitmap(width, height);

            using (var g = Graphics.FromImage(bmp))
            {
                g.SetHighQuality();
                var position = 0;

                foreach (var image in images)
                {
                    Rectangle rect;

                    if (orientation == Orientation.Vertical)
                    {
                        int x;
                        switch (alignment)
                        {
                            default:
                                x = 0;
                                break;
                            case ImageCombinerAlignment.Center:
                                x = width / 2 - image.Width / 2;
                                break;
                            case ImageCombinerAlignment.RightOrBottom:
                                x = width - image.Width;
                                break;
                        }

                        rect = new Rectangle(x, position, image.Width, image.Height);
                        position += image.Height + space;
                    }
                    else
                    {
                        int y;
                        switch (alignment)
                        {
                            default:
                                y = 0;
                                break;
                            case ImageCombinerAlignment.Center:
                                y = height / 2 - image.Height / 2;
                                break;
                            case ImageCombinerAlignment.RightOrBottom:
                                y = height - image.Height;
                                break;
                        }

                        rect = new Rectangle(position, y, image.Width, image.Height);
                        position += image.Width + space;
                    }

                    g.DrawImage(image, rect);
                }
            }

            return bmp;
        }

        public static Image ProcessImage(Bitmap originImage, ImageProcessType imgType)
        {
            Image img = originImage;
            switch (imgType)
            {
                case ImageProcessType.对比度:
                    img = BitContrastByPointer(originImage);
                    break;
                case ImageProcessType.反色:
                    img = BitInverseByPointer(originImage);
                    break;
                case ImageProcessType.倾斜矫正:
                    img = EnhanceDocImage(ImageToBase64(originImage));
                    if (img == null && CommonUpdate.CheckInstallLocalDectExe())
                    {
                        img = DetectImageLocal(originImage);
                    }
                    break;
                case ImageProcessType.图片增强:
                    img = EnhanceImage(ImageToBase64(originImage));
                    break;
                case ImageProcessType.去屏幕纹:
                    img = DemoireImage(ImageToBase64(originImage));
                    break;
                case ImageProcessType.老照片修复:
                    img = PhotoRestoration(ImageToBase64(originImage));
                    break;
            }

            return img ?? originImage;
        }

        public enum ImageDectType
        {
            TextIn,
            助手文档
        }

        public static bool DectImage(string fileName, string toPath, ImageDectType dectType)
        {
            using (var image = Image.FromFile(fileName))
            {
                using (var bitmap = new Bitmap(image))
                {
                    switch (dectType)
                    {
                        case ImageDectType.TextIn:
                            var img = EnhanceDocImage(ImageToBase64(bitmap))?.SaveFileWithOutConfirm(toPath);
                            break;
                        case ImageDectType.助手文档:
                            DetectImageLocal(bitmap, ref fileName, ref toPath);
                            break;
                    }
                }
            }
            return File.Exists(toPath);
        }

        public static Bitmap DetectImageLocal(Bitmap oriImage)
        {
            var strLocalFile = "";
            var strNewFile = "";
            return DetectImageLocal(oriImage, ref strLocalFile, ref strNewFile);
        }

        public static Bitmap DetectImageLocal(Bitmap oriImage, ref string strLocalFile, ref string strNewFile)
        {
            try
            {
                strLocalFile = string.IsNullOrEmpty(strLocalFile) ? oriImage.SaveFileWithOutConfirm(null, true) : strLocalFile;
                strNewFile = string.IsNullOrEmpty(strNewFile) ? strLocalFile : strNewFile;
                var result = CommonMethod.ExecCmd(string.Format("{0} -g c -o \"{1}\" \"{2}\"", CommonString.DefaultLocalDectExePath, strNewFile, strLocalFile));
                if (File.Exists(strNewFile))
                    return new Bitmap(Image.FromFile(strNewFile));
            }
            catch (Exception oe)
            {
            }
            return null;
        }

        /// <summary>
        ///去屏幕纹
        /// </summary>
        /// <param name="strBase64"></param>
        /// <returns></returns>
        public static Bitmap DemoireImage(string strBase64)
        {
            var result = "";

            var strTmp = WebClientExt.GetHtml("https://api.textin.com/home/<USER>", "", "", strBase64, "", 10);

            if (!string.IsNullOrEmpty(strTmp) && strTmp.Contains(STR_DOC_IMG_SPILT))
            {
                result = strTmp.Substring(strTmp.IndexOf(STR_DOC_IMG_SPILT) + STR_DOC_IMG_SPILT.Length);
                result = result.Substring(0, result.IndexOf("\""));
            }

            if (!string.IsNullOrEmpty(result)) return Base64StringToImage(System.Text.RegularExpressions.Regex.Unescape(result));
            return null;
        }

        /// <summary>
        ///老照片修复
        /// </summary>
        /// <param name="strBase64"></param>
        /// <returns></returns>
        public static Bitmap PhotoRestoration(string strBase64)
        {
            var result = "";

            var strTmp = WebClientExt.GetHtml("https://api.textin.com/home/<USER>/photo_restoration", "", "", strBase64, "", 10);

            if (!string.IsNullOrEmpty(strTmp) && strTmp.Contains(STR_DOC_IMG_SPILT))
            {
                result = strTmp.Substring(strTmp.IndexOf(STR_DOC_IMG_SPILT) + STR_DOC_IMG_SPILT.Length);
                result = result.Substring(0, result.IndexOf("\""));
            }

            if (!string.IsNullOrEmpty(result)) return Base64StringToImage(System.Text.RegularExpressions.Regex.Unescape(result));
            return null;
        }

        /// <summary>
        ///文档图像矫正
        /// </summary>
        /// <param name="strBase64"></param>
        /// <returns></returns>
        public static Bitmap EnhanceDocImage(string strBase64)
        {
            var result = "";

            var strTmp = WebClientExt.GetHtml("https://api.textin.com/home/<USER>", "", "", strBase64, "", 10);

            if (!string.IsNullOrEmpty(strTmp) && strTmp.Contains(STR_DOC_IMG_SPILT))
            {
                result = strTmp.Substring(strTmp.IndexOf(STR_DOC_IMG_SPILT) + STR_DOC_IMG_SPILT.Length);
                result = result.Substring(0, result.IndexOf("\""));
            }

            if (!string.IsNullOrEmpty(result)) return Base64StringToImage(System.Text.RegularExpressions.Regex.Unescape(result));
            return null;
        }

        /// <summary>
        ///照片图像增强
        /// </summary>
        /// <param name="strBase64"></param>
        /// <returns></returns>
        public static Bitmap EnhanceImage(string strBase64)
        {
            var result = "";
            var strPost = strBase64;
            var strTmp = WebClientExt.GetHtml("https://api.textin.com/home/<USER>", "", "", strPost, "", 10);

            if (!string.IsNullOrEmpty(strTmp) && strTmp.Contains(STR_DOC_IMG_SPILT))
            {
                result = strTmp.Substring(strTmp.IndexOf(STR_DOC_IMG_SPILT) + STR_DOC_IMG_SPILT.Length);
                result = result.Substring(0, result.IndexOf("\""));
            }

            if (!string.IsNullOrEmpty(result)) return Base64StringToImage(System.Text.RegularExpressions.Regex.Unescape(result));
            return null;
        }

        private static string ImageToBase64(Image image)
        {
            var byts = ImageToByte(image);
            return byts?.Length > 0 ? Convert.ToBase64String(byts) : null;
        }

        public static byte[] ImageToByte(Image image)
        {
            using (var ms = new MemoryStream())
            {
                if (image.RawFormat.Guid == ImageFormat.Gif.Guid)
                    image.Save(ms, ImageFormat.Gif);
                else if (image.RawFormat.Guid == ImageFormat.Bmp.Guid)
                    image.Save(ms, ImageFormat.Bmp);
                else if (image.RawFormat.Guid == ImageFormat.Png.Guid)
                    image.Save(ms, ImageFormat.Png);
                else if (image.RawFormat.Guid == ImageFormat.Tiff.Guid)
                    image.Save(ms, ImageFormat.Tiff);
                else
                    image.Save(ms, ImageFormat.Jpeg);

                return ms.ToArray();
            }
        }

        public static Bitmap UrlToBitmap(string url)
        {
            var byts = WebClientExt.GetOneClient().DownloadData(url);
            if (byts != null && byts.Length > 0)
            {
                return ByteToImage(byts);
            }
            return null;
        }

        public static Bitmap ByteToImage(byte[] bytes)
        {
            Bitmap bmp = null;
            try
            {
                using (var ms = new MemoryStream())
                {
                    ms.Write(bytes, 0, bytes.Length);
                    bmp = new Bitmap(ms);
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }

            return bmp;
        }

        private static Bitmap Base64StringToImage(string base64Img)
        {
            Bitmap bmp = null;
            try
            {
                using (var ms = new MemoryStream())
                {
                    var bytes = Convert.FromBase64String(base64Img);
                    ms.Write(bytes, 0, bytes.Length);
                    bmp = new Bitmap(ms);
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }

            return bmp;
        }

    }

    public enum ImageCombinerAlignment
    {
        LeftOrTop,
        Center,
        RightOrBottom
    }

    public enum ImageProcessType
    {
        原始图片 = 0,
        对比度 = 1,
        反色 = 2,
        倾斜矫正 = 50,
        图片增强 = 51,
        去屏幕纹 = 52,
        老照片修复 = 53,
    }
}