﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="ttMain.TrayLocation" type="System.Drawing.Point, System.Drawing">
    <value>17, 17</value>
  </data>
  <data name="lblTitle.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 11.25pt</value>
  </data>
  <data name="lblTitle.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="lblTitle.Size" type="System.Drawing.Size, System.Drawing">
    <value>256, 20</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="lblTitle.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="&gt;&gt;lblTitle.Name" xml:space="preserve">
    <value>lblTitle</value>
  </data>
  <data name="&gt;&gt;lblTitle.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblTitle.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="lblError.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="lblError.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 12pt</value>
  </data>
  <data name="lblError.Location" type="System.Drawing.Point, System.Drawing">
    <value>96, 6</value>
  </data>
  <data name="lblError.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 24</value>
  </data>
  <data name="lblError.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="lblError.Text" xml:space="preserve">
    <value>出错啦</value>
  </data>
  <data name="lblError.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;lblError.Name" xml:space="preserve">
    <value>lblError</value>
  </data>
  <data name="&gt;&gt;lblError.Parent" xml:space="preserve">
    <value>pThumbnail</value>
  </data>
  <data name="&gt;&gt;lblError.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="pbThumbnail.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="pbThumbnail.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 4</value>
  </data>
  <data name="pbThumbnail.Size" type="System.Drawing.Size, System.Drawing">
    <value>248, 228</value>
  </data>
  <data name="pbThumbnail.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;pbThumbnail.Name" xml:space="preserve">
    <value>pbThumbnail</value>
  </data>
  <data name="&gt;&gt;pbThumbnail.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pbThumbnail.Parent" xml:space="preserve">
    <value>pThumbnail</value>
  </data>
  <data name="&gt;&gt;pbThumbnail.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pThumbnail.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 22</value>
  </data>
  <data name="pThumbnail.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="pThumbnail.Size" type="System.Drawing.Size, System.Drawing">
    <value>256, 236</value>
  </data>
  <data name="pThumbnail.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;pThumbnail.Name" xml:space="preserve">
    <value>pThumbnail</value>
  </data>
  <data name="&gt;&gt;pThumbnail.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pThumbnail.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="$this.Localizable" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="$this.Size" type="System.Drawing.Size, System.Drawing">
    <value>256, 258</value>
  </data>
  <data name="&gt;&gt;ttMain.Name" xml:space="preserve">
    <value>ttMain</value>
  </data>
  <data name="&gt;&gt;ttMain.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolTip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>TaskThumbnailPanel</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.UserControl, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>