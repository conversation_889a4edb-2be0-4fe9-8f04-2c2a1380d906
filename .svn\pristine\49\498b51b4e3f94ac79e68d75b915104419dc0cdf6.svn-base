using System;
using System.Collections.Generic;
using System.IO;
using ExcelLibrary.BinaryDrawingFormat;
using QiHe.CodeLib;

namespace ExcelLibrary.BinaryFileFormat
{
	public class MSOCONTAINER : Record
	{
		public List<EscherRecord> EscherRecords = new List<EscherRecord>();

		public MSOCONTAINER()
		{
		}

		public MSOCONTAINER(Record record)
			: base(record)
		{
		}

		public override void Decode()
		{
			MemoryStream memoryStream = new MemoryStream(base.AllData);
			EscherRecords.Clear();
			while (memoryStream.Position < memoryStream.Length)
			{
				EscherRecord escherRecord = EscherRecord.Read(memoryStream);
				escherRecord.Decode();
				EscherRecords.Add(escherRecord);
			}
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter writer = new BinaryWriter(memoryStream);
			foreach (EscherRecord escherRecord in EscherRecords)
			{
				escherRecord.Encode();
				escherRecord.Write(writer);
			}
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			ContinuedRecords.Clear();
			if (Size > 0 && Data.Length > 8224)
			{
				int num;
				for (int i = 8224; i < Data.Length; i += num)
				{
					num = Math.Min(8224, Data.Length - i);
					Record record = new Record();
					record.Type = Type;
					record.Size = (ushort)num;
					record.Data = Algorithm.ArraySection(Data, i, num);
					ContinuedRecords.Add(record);
				}
				Size = 8224;
				Data = Algorithm.ArraySection(Data, 0, 8224);
			}
		}

		public TRecord FindChild<TRecord>() where TRecord : EscherRecord
		{
			foreach (EscherRecord escherRecord in EscherRecords)
			{
				if (escherRecord is TRecord)
				{
					return escherRecord as TRecord;
				}
			}
			return null;
		}
	}
}
