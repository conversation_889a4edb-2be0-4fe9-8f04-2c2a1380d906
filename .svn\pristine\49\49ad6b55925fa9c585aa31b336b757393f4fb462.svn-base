﻿using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Web;

namespace OCRTools
{
    internal enum WeatchIconType
    {
        QQ = 1,
        墨迹 = 2,
    }

    public class CommonWeather
    {
        public static void InitWeather()
        {
            try
            {
                var timerInfo = new TimerInfo
                {
                    TimerType = "LoopMinutes",
                    DateValue = 30,
                    IsExecFirst = true
                };
                TimerTaskDelegate update = UpdateMethod;
                var updateTimeTaskService = TimerTaskService.CreateTimerTaskService(timerInfo, update);
                updateTimeTaskService.Start();
            }
            catch
            {
            }
        }

        public static void UpdateMethod()
        {
            try
            {
                if (!CommonSetting.启用天气图标)
                {
                    return;
                }
                var strImage = GetWeather((WeatchIconType)Enum.Parse(typeof(WeatchIconType), CommonSetting.天气图标样式));
                if (!string.IsNullOrEmpty(strImage))
                {
                    try
                    {
                        CommonSetting.工具栏图片 = strImage;
                        FrmMain.FrmTool.RefreshImage();
                    }
                    catch
                    {
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("更新天气失败", oe);
            }
        }

        internal static string GetWeatherIcon(string data, WeatchIconType iconType, bool isAuto = true)
        {
            var url = string.Empty;
            switch (iconType)
            {
                case WeatchIconType.QQ:
                    url = string.Format(STR_QQ_WEATHER_IMG_URL, data);
                    break;
                case WeatchIconType.墨迹:
                    url = string.Format(STR_MO_JI_WEATHER_IMG_URL, data.TrimStart('0').PadRight(1, '0'));
                    break;
            }
            var result = CommonSetting.SetHeadImageByUrl(url);
            if (string.IsNullOrEmpty(result) && isAuto)
            {
                return GetWeatherIcon(data, iconType == WeatchIconType.QQ ? WeatchIconType.墨迹 : WeatchIconType.QQ, false);
            }
            return result;
        }

        internal static string GetWeather(WeatchIconType iconType)
        {
            var city = string.Empty;
            var result = string.Empty;
            var strWeather = GetQqWeather(ref city);
            if (!string.IsNullOrEmpty(strWeather))
            {
                result = GetWeatherIcon(strWeather, iconType);
            }
            CommonMethod.ShowHelpMsg(string.Format("{0:HH:mm:ss} {1}更新天气{2}！", ServerTime.DateTime, city, string.IsNullOrEmpty(strWeather) ? "失败" : "成功"));
            return result;
        }

        //public static string Get360Weather()
        //{
        //    var result = string.Empty;
        //    var html = WebClientExt.GetHtml(Str360WeatherUrl).Replace(" ", "").Trim();
        //    if (!string.IsNullOrEmpty(html) && html.Contains(Str360WeatherSection))
        //    {
        //        html = html.Substring(html.IndexOf(Str360WeatherSection) + Str360WeatherSection.Length);
        //        result = CommonMethod.SubString(html, Str360WeatherImage, "\"").Trim();
        //    }
        //    return result;
        //}

        public static string GetQqWeather(ref string strCity)
        {
            var result = string.Empty;
            var lstLocation = GetLocation();
            if (lstLocation.Count <= 0)
            {
                return result;
            }
            var html = WebClientExt.GetHtml(string.Format(STR_QQ_WEATHER_URL, HttpUtility.UrlEncode(lstLocation[0]), HttpUtility.UrlEncode(lstLocation[1]), HttpUtility.UrlEncode(lstLocation[2]))
                ).Replace(" ", "").Trim();
            if (!string.IsNullOrEmpty(html) && html.Contains(STR_QQ_WEATHER_IMAGE))
            {
                result = CommonMethod.SubString(html, STR_QQ_WEATHER_IMAGE, "\"").Trim();
            }

            if (!string.IsNullOrEmpty(result))
            {
                strCity = string.IsNullOrEmpty(lstLocation[2]) ? lstLocation[1] : lstLocation[2];
            }
            return result;
        }

        private const string STR_QQIP_URL =
            "https://apis.map.qq.com/ws/location/v1/ip?key=3BFBZ-ZKD3X-LW54A-ZT76D-E7AHO-4RBD5&&output=jsonp&callback=weather";
        private const string STR_QQ_WEATHER_URL =
            "https://wis.qq.com/weather/common?source=pc&weather_type=observe&province={0}&city={1}&county={2}&callback=weather";
        //private const string Str360WeatherUrl =
        //    "http://weather.kjjs.360.cn/freshcalendar/weather?ver=1.0.0.1125&callback=callback";
        private const string STR_QQ_PROVINCE = "\"province\":\"";
        private const string STR_QQ_CITY = "\"city\":\"";
        private const string STR_QQ_COUNTY = "\"district\":\"";
        private const string STR_QQ_WEATHER_IMAGE = "\"weather_code\":\"";
        //private const string Str360WeatherSection = "\"realtime\":";
        //private const string Str360WeatherImage = "\"img\":\"";

        private const string STR_QQ_WEATHER_IMG_URL =
            //"https://mat1.gtimg.com/pingjs/ext2020/weather/pc/icon/weather/day/{0}.png";//小图
            "https://mat1.gtimg.com/pingjs/ext2020/weather/pc/icon/currentweather/day/{0}.png";//大图

        private const string STR_MO_JI_WEATHER_IMG_URL =
            "https://h5tq.moji.com/tianqi/assets/images/weather/w{0}.png";

        private static List<string> GetLocation()
        {
            var lstResult = new List<string>();
            var html = WebClientExt.GetHtml(STR_QQIP_URL).Replace(" ", "").Trim();
            if (!string.IsNullOrEmpty(html) && html.Contains(STR_QQ_PROVINCE))
            {
                lstResult.Add(CommonMethod.SubString(html, STR_QQ_PROVINCE, "\"").Trim());
                lstResult.Add(CommonMethod.SubString(html, STR_QQ_CITY, "\"").Trim());
                lstResult.Add(CommonMethod.SubString(html, STR_QQ_COUNTY, "\"").Trim());
            }
            return lstResult;
        }
    }
}
