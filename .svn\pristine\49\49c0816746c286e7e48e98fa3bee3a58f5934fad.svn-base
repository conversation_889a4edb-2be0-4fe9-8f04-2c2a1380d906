﻿using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    partial class FrmViewUrl
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.pnlView = new System.Windows.Forms.Panel();
            this.wbView = new WebBrowser2();
            this.pnlView.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlView
            // 
            this.pnlView.BackColor = System.Drawing.Color.White;
            this.pnlView.Controls.Add(this.wbView);
            this.pnlView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlView.Location = new System.Drawing.Point(20, 60);
            this.pnlView.Name = "pnlView";
            this.pnlView.Size = new System.Drawing.Size(970, 636);
            this.pnlView.TabIndex = 8;
            this.pnlView.Visible = false;
            // 
            // wbView
            // 
            this.wbView.AllowWebBrowserDrop = false;
            this.wbView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.wbView.IsWebBrowserContextMenuEnabled = false;
            this.wbView.Location = new System.Drawing.Point(0, 0);
            this.wbView.MinimumSize = new System.Drawing.Size(20, 20);
            this.wbView.Name = "wbView";
            this.wbView.Size = new System.Drawing.Size(970, 636);
            this.wbView.TabIndex = 0;
            this.wbView.TabStop = false;
            this.wbView.WebBrowserShortcutsEnabled = false;
            this.wbView.DocumentCompleted += new System.Windows.Forms.WebBrowserDocumentCompletedEventHandler(this.wbView_DocumentCompleted);
            // 
            // FrmViewUrl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.ClientSize = new System.Drawing.Size(1010, 716);
            this.Controls.Add(this.pnlView);
            this.Name = "FrmViewUrl";
            this.Text = "OCR助手";
            this.Load += new System.EventHandler(this.FrmGoBuy_Load);
            this.pnlView.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion
        private Panel pnlView;
        private WebBrowser2 wbView;
    }
}