using OCRTools.Common;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class Screenshot
    {
        public bool CaptureCursor { get; set; } = false;
        public bool CaptureClientArea { get; set; } = false;
        public bool RemoveOutsideScreenArea { get; set; } = true;
        public bool AutoHideTaskbar { get; set; } = false;

        public Bitmap CaptureRectangle(Rectangle srcRect, Rectangle destRect)
        {
            if (RemoveOutsideScreenArea)
            {
                var bounds = StaticValue.ScreenRectangle;
                srcRect = Rectangle.Intersect(bounds, srcRect);
            }
            return CaptureRectangleNative(srcRect, destRect, CaptureCursor);
        }

        public Bitmap CaptureFullscreen(ref Rectangle srcRect, bool isNeedScal = false)
        {
            srcRect = StaticValue.ScreenRectangle;
            Rectangle destRect = srcRect;
            if (isNeedScal && PrimaryScreen.GetScreenScalingFactor() > 1)
            {
                destRect = SystemInformation.VirtualScreen;
            }
            var bitMap = CaptureRectangle(srcRect, destRect);
            srcRect = destRect;
            return bitMap;
        }

        public Bitmap CaptureWindow(IntPtr handle, ref Rectangle rect)
        {
            if (handle.ToInt32() > 0)
            {
                rect = handle.GetRectangle(!CaptureClientArea);

                var isTaskbarHide = false;

                try
                {
                    if (AutoHideTaskbar) isTaskbarHide = NativeMethods.SetTaskbarVisibilityIfIntersect(false, rect);

                    return CaptureRectangle(rect, rect);
                }
                finally
                {
                    if (isTaskbarHide) NativeMethods.SetTaskbarVisibility(true);
                }
            }

            return null;
        }

        public Bitmap CaptureActiveWindow(ref Rectangle rect)
        {
            var handle = NativeMethods.GetForegroundWindow();

            return CaptureWindow(handle, ref rect);
        }

        public Bitmap CaptureActiveMonitor(ref Rectangle rect)
        {
            rect = NativeMethods.GetActiveScreenBounds();

            return CaptureRectangle(rect, rect);
        }

        private Bitmap CaptureRectangleNative(Rectangle srcRect, Rectangle destRect, bool captureCursor = false)
        {
            var handle = NativeMethods.GetDesktopWindow();
            return CaptureRectangleNative(handle, srcRect, destRect, captureCursor);
        }

        private Bitmap CaptureRectangleNative(IntPtr handle, Rectangle srcRect, Rectangle destRect, bool captureCursor = false)
        {
            if (srcRect.Width == 0 || srcRect.Height == 0) return null;

            var hdcSrc = NativeMethods.GetWindowDC(handle);
            var hdcDest = NativeMethods.CreateCompatibleDC(hdcSrc);
            var hBitmap = NativeMethods.CreateCompatibleBitmap(hdcSrc, destRect.Width, destRect.Height);
            var hOld = NativeMethods.SelectObject(hdcDest, hBitmap);
            if (srcRect.Width.Equals(destRect.Width))
            {
                NativeMethods.BitBlt(hdcDest, 0, 0, destRect.Width, destRect.Height
                    , hdcSrc, srcRect.X, srcRect.Y,
                    CopyPixelOperation.SourceCopy | CopyPixelOperation.CaptureBlt);
            }
            else
            {
                NativeMethods.SetStretchBltMode(hdcDest, 4);
                NativeMethods.StretchBlt(hdcDest, 0, 0, destRect.Width, destRect.Height
                    , hdcSrc, srcRect.X, srcRect.Y, srcRect.Width, srcRect.Height,
                    CopyPixelOperation.SourceCopy | CopyPixelOperation.CaptureBlt);
            }

            if (captureCursor)
                try
                {
                    var cursorData = new CursorData();
                    cursorData.DrawCursor(hdcDest, srcRect.Location);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }

            NativeMethods.SelectObject(hdcDest, hOld);
            NativeMethods.DeleteDC(hdcDest);
            NativeMethods.ReleaseDC(handle, hdcSrc);
            var bmp = Image.FromHbitmap(hBitmap);
            NativeMethods.DeleteObject(hBitmap);
            return bmp;
        }
    }
}