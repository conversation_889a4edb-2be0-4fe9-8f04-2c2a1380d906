using ExcelLibrary.BinaryFileFormat;
using ExcelLibrary.CompoundDocumentFormat;
using System.Collections.Generic;
using System.IO;

namespace ExcelLibrary.SpreadSheet
{
    public class Workbook
    {
        public List<Worksheet> Worksheets = new List<Worksheet>();

        public MSODRAWINGGROUP DrawingGroup;

        public List<Record> Records;

        public void Save(string file)
        {
            using (CompoundDocument compoundDocument = CompoundDocument.Create(file))
            {
                using (MemoryStream memoryStream = new MemoryStream())
                {
                    WorkbookEncoder.Encode(this, memoryStream);
                    compoundDocument.WriteStreamData(new string[1] { "Workbook" }, memoryStream.ToArray());
                    compoundDocument.Save();
                }
            }
        }
    }
}
