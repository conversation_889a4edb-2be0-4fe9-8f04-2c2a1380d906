﻿using System;
using System.Data;
using System.IO;
using OCRTools.Common;

namespace OCRTools
{
    internal class CommonResult
    {
        public static void SaveFile(OcrContent content, string filePath, string fileName)
        {
            var fullFileName = Path.Combine(filePath,
                fileName + "-" + content.processName + "-" + ServerTime.DateTime.Millisecond);
            if (!Directory.Exists(Path.GetDirectoryName(fullFileName)))
                Directory.CreateDirectory(Path.GetDirectoryName(fullFileName) ?? string.Empty);
            switch (content.result.resultType)
            {
                case ResutypeEnum.文本:
                    var txtContent = content.result.GetTextResult(CommonSetting.首行缩进, false, true, SpiltMode.自动分段,
                        content.processName);
                    ExportToTxt(txtContent, fullFileName + ".txt");
                    break;
                case ResutypeEnum.网页:
                    break;
                case ResutypeEnum.表格:
                    var dt = GetTableContent(content.result);
                    ExcelHelper.ExportCsv(dt, fullFileName + ".xls");
                    break;
            }
        }

        /// <summary>
        ///     导出CSV格式文件
        /// </summary>
        /// <param name="txt"></param>
        /// <param name="fileName">文件名</param>
        public static void ExportToTxt(string txt, string fileName)
        {
            txt = txt.Replace("\n", Environment.NewLine);
            using (var streamWriter = File.AppendText(fileName))
            {
                streamWriter.WriteLine(txt);
                streamWriter.Flush();
                streamWriter.Close();
            }
        }

        public static DataTable GetTableContent(ResultEntity result)
        {
            var tableContent = CommonString.JavaScriptSerializer.Deserialize<TableContentInfo>(result.autoText);
            var dt = new DataTable();
            foreach (var unused in tableContent?.rows[0]?.cells) dt.Columns.Add("", typeof(string));
            foreach (var row in tableContent.rows)
                try
                {
                    var dr = dt.NewRow();

                    foreach (var cell in row.cells)
                        dr[cell.index - 1] = cell.content?.Replace("\n", Environment.NewLine);

                    dt.Rows.Add(dr);
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }

            return dt;
        }
    }
}