using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolLine : ToolObject
    {
        private DrawLine drawLine;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.current_ToolType = drawArea.ActiveTool;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else
            {
                drawLine = new DrawLine(e.X, e.Y, e.X + 1, e.Y + 1);
                AddNewObject(drawArea, drawLine);
            }
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left)
            {
                if (drawLine == null)
                {
                    drawArea.ActiveTool = DrawToolType.Text;
                    return;
                }

                drawLine.IsSelected = true;
                var obj = drawLine;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    drawLine.MoveHandleTo(e.Location, 2, drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawLine != null)
            {
                StaticValue.current_Rectangle = drawLine.Rectangle;
                if (!drawLine.Rectangle.IsLimt())
                {
                    drawArea.GraphicsList.RemoveAt(0);
                    return;
                }

                var obj = drawLine;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    drawLine.Normalize();
                    drawArea.AddCommandToHistory(new CommandAdd(drawLine));
                }
            }
        }
    }
}