﻿using System;
using System.Drawing;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using MetroFramework.Forms;

namespace OCRTools
{
    public partial class FrmUserInfo : MetroForm
    {
        private UserTypeEnum _nextUserType;

        public FrmUserInfo()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            lnkAccount.Text = Program.NowUser?.Account;
            lnkNickName.Text = Program.NowUser?.NickName;
            lnkUserType.Text = Program.NowUser?.UserType.ToString();
            lnkRegDate.Text = string.Format("{0:yyyy-MM-dd}({1})", Program.NowUser?.DtExpired,
                Program.NowUser != null && ServerTime.DateTime < Program.NowUser.DtExpired
                    ? string.Format("剩{0:F2}天",
                        new TimeSpan(Program.NowUser.DtExpired.Ticks - ServerTime.DateTime.Ticks).TotalDays)
                    : "已过期");
            InitBtnText();
            CommonMsg.ShowToWindow(this, new Point(136, 20));
        }

        private void InitBtnText()
        {
            btnOpenVip.Text = CommonMethod.GetNextTypeStr(out _nextUserType);
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            if (CommonString.IsOnLine)
            {
                if (!Enum.IsDefined(typeof(UserTypeEnum), _nextUserType))
                {
                    _nextUserType = UserTypeEnum.旗舰版;
                    CommonMethod.ShowHelpMsg("如需要更多功能，请联系我们定制！");
                }

                var frmBuy = new FrmGoBuy
                {
                    Icon = Icon,
                    Theme = Theme,
                    Style = Style,
                    StyleManager = StyleManager,
                    NextUserType = _nextUserType
                };
                frmBuy.ShowDialog(this);
                CommonMsg.ShowToWindow(this, new Point(136, 20));
                //MessageBox.Show(this, "欢迎开通会员服务！\n识别速度更快，精度更高，功能更全面……\n敬请期待！", "温馨提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                CommonMethod.ShowHelpMsg("当前网络异常，请等待网络恢复后重试！");
            }
        }

        private void lnkLogout_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            Program.NowUser = null;
            Close();
        }

        private void lnkEditNickName_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            if (Program.NowUser == null || !Program.NowUser.IsLogined) return;
            if (!CommonString.IsOnLine)
            {
                CommonMethod.ShowHelpMsg("当前网络异常，请等待网络恢复后重试！");
                return;
            }

            if (lnkEditNickName.Text.Equals("编辑"))
            {
                lnkEditNickName.Text = "保存";
                txtNickName.Visible = true;
                lnkNickName.Visible = false;
                txtNickName.Text = lnkNickName.Text;
                txtNickName.SelectAll();
                txtNickName.Focus();
            }
            else
            {
                var nickName = txtNickName.Text.Trim();
                if (nickName.Length < 2 || nickName.Length > 20 ||
                    !new Regex(@"^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z0-9_])").IsMatch(nickName))
                {
                    MessageBox.Show(this, "昵称为2-15位的中英文数字字母或下划线！", "温馨提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    txtNickName.Focus();
                    return;
                }

                if (Program.NowUser == null || Program.NowUser.NickName.Equals(nickName)) return;
                var strMsg = "";
                var result = OcrHelper.EditNickName(Program.NowUser?.Account, nickName, ref strMsg);
                if (result)
                {
                    Program.NowUser.NickName = nickName;
                    lnkEditNickName.Text = "编辑";
                    txtNickName.Visible = false;
                    lnkNickName.Visible = true;
                    lnkNickName.Text = txtNickName.Text;
                }
                else
                {
                    if (string.IsNullOrEmpty(strMsg)) strMsg = "注册失败，请稍候重试！";
                    MessageBox.Show(this, strMsg, "温馨提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
}