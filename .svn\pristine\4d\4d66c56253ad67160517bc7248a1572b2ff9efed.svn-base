using System;
using System.Drawing;
using System.Runtime.InteropServices;

namespace OCRTools
{
    public static class BitDrawImage
    {
        [DllImport("gdi32.dll")]
        private static extern bool BitBlt(IntPtr hdcDest, int nXDest, int nYDest, int nWidth, int nHeight,
            IntPtr hdcSrc, int nXSrc, int nYSrc, int dwRop);

        [DllImport("gdi32.dll", ExactSpelling = true, SetLastError = true)]
        private static extern IntPtr CreateCompatibleDC(IntPtr hdcPtr);

        [DllImport("gdi32.dll", ExactSpelling = true)]
        private static extern IntPtr SelectObject(IntPtr hdcPtr, IntPtr hObject);

        [DllImport("gdi32.dll", ExactSpelling = true)]
        private static extern bool DeleteDC(IntPtr hdcPtr);

        [DllImport("gdi32.dll")]
        private static extern bool DeleteObject(IntPtr hObject);

        public static void DrawImage(Bitmap bmp, Graphics clientDc)
        {
            var hdc = clientDc.GetHdc();
            var intPtr = CreateCompatibleDC(hdc);
            var hbitmap = bmp.GetHbitmap();
            SelectObject(intPtr, hbitmap);
            BitBlt(hdc, 0, 0, bmp.Width, bmp.Height, intPtr, 0, 0, 13369376);
            DeleteDC(intPtr);
            DeleteObject(hbitmap);
            clientDc.ReleaseHdc(hdc);
        }
    }
}