﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class UnitScale
    {
        /// <summary>
        ///     The size of one step in the corresponding unit.
        /// </summary>
        public float StepSize { get; set; }

        /// <summary>
        ///     After how many steps to set a medium tick.
        /// </summary>
        public int MTickSteps { get; set; }

        /// <summary>
        ///     After how many steps to set a big tick.
        /// </summary>
        public int BTickSteps { get; set; }
    }

    /// <summary>
    ///     Holds methods for painting the ruler form.
    /// </summary>
    public class RulerPainter
    {
        public const int RulerWidthWide = 95;
        public const int RulerWidthSlim = 55;

        private const string MARKER_SYMBOL_CENTER_LINE = "\u00BD";
        private const string MARKER_SYMBOL_MOUSE_LINE = "\u271C"; // u271B u271C
        private const string MARKER_SYMBOL_THIRD_LINES = "\u2153";
        private const string MARKER_SYMBOL_GOLDEN_LINE = "\u03D5"; // u03C6 u03A6 u03D5

        private const int
            MARKER_SYMBOL_CUSTOM_LINE1 = 0x2460 - 1; // Circled 1, add for up to 20 (-1 for correct behaviour post 20)

        /// <summary>
        ///     Defines the position where ticks should be drawn dependent on the chosen unit.
        ///     (To deal with integers, all values are multiplied by 10 (e.g. 50 means 5px)).
        /// </summary>
        public static Dictionary<MeasuringUnit, UnitScale> Ticks = new Dictionary<MeasuringUnit, UnitScale>
        {
            {MeasuringUnit.像素, new UnitScale {StepSize = 5, MTickSteps = 2, BTickSteps = 10}},
            {MeasuringUnit.英寸, new UnitScale {StepSize = 0.0625f, MTickSteps = 4, BTickSteps = 16}},
            {MeasuringUnit.厘米, new UnitScale {StepSize = 0.1f, MTickSteps = 5, BTickSteps = 10}},
            {MeasuringUnit.点, new UnitScale {StepSize = 5, MTickSteps = 2, BTickSteps = 10}},
            {MeasuringUnit.百分比, new UnitScale {StepSize = 0.5f, MTickSteps = 2, BTickSteps = 10}}
        };

        private readonly Control _c;
        private readonly float _phi;
        private UnitConverter _converter;
        private int _drawWidth;
        private Graphics _g;
        private RulerFormResizeMode _resizeMode = RulerFormResizeMode.Horizontal;
        private Settings _settings;

        public RulerPainter(Control control)
        {
            _c = control;
            _phi = (float)(2 / (1 + Math.Sqrt(5)));
        }

        /// <summary>
        ///     Updates relevant properties before repainting the ruler.
        /// </summary>
        /// <param name="g">The graphics to be painted on.</param>
        /// <param name="settings">The settings.</param>
        public void Update(Graphics g, Settings settings, RulerFormResizeMode resizeMode)
        {
            _g = g;
            _settings = settings;
            _resizeMode = resizeMode;
            var screenSize = Screen.FromControl(_c).Bounds.Size;
            var virtualDpi = settings.MonitorDpi / (settings.MonitorScaling / 100.0f);
            _converter = new UnitConverter(settings.MeasuringUnit, screenSize, virtualDpi);
            _drawWidth = settings.SlimMode ? RulerWidthSlim : RulerWidthWide;
        }

        /// <summary>
        ///     Paints the ruler scale onto the given Graphics object.
        /// </summary>
        public void PaintRuler()
        {
            // ----- Draw the ruler background -----
            using (Brush brush = new SolidBrush(_settings.Theme.Background))
            {
                if (_resizeMode.HasFlag(RulerFormResizeMode.Horizontal))
                    _g.FillRectangle(brush, 0, 0, _c.Width, _drawWidth);
                if (_resizeMode.HasFlag(RulerFormResizeMode.Vertical))
                    _g.FillRectangle(brush, 0, 0, _drawWidth, _c.Height);
            }

            using (Brush brush = new SolidBrush(_settings.Theme.TickColor))
            using (var pen = new Pen(brush, 1))
            {
                if (_resizeMode.HasFlag(RulerFormResizeMode.Horizontal) &&
                    _resizeMode.HasFlag(RulerFormResizeMode.Vertical))
                {
                    var point1 = new Point(0, 0);
                    var point2 = new Point(_c.Width - 1, 0);
                    var point3 = new Point(_c.Width - 1, _drawWidth);
                    var point4 = new Point(_drawWidth, _drawWidth);
                    var point5 = new Point(_drawWidth, _c.Height - 1);
                    var point6 = new Point(0, _c.Height - 1);
                    _g.DrawLines(pen, new[] { point1, point2, point3, point4, point5, point6, point1 });
                }
                else if (_resizeMode.HasFlag(RulerFormResizeMode.Horizontal))
                {
                    _g.DrawRectangle(pen, 0, 0, _c.Width - 1, _drawWidth - 1);
                }
                else if (_resizeMode.HasFlag(RulerFormResizeMode.Vertical))
                {
                    _g.DrawRectangle(pen, 0, 0, _drawWidth - 1, _c.Height - 1);
                }
            }

            // ----- Draw the ruler scale -----
            if (_resizeMode.HasFlag(RulerFormResizeMode.Horizontal))
                PaintRulerScale(false);
            if (_resizeMode.HasFlag(RulerFormResizeMode.Vertical))
                PaintRulerScale(true);
        }

        protected void PaintRulerScale(bool vertical)
        {
            var max = vertical ? _c.Size.Height : _c.Size.Width;
            // ----- Draw the ruler scale -----
            var scale = Ticks[_settings.MeasuringUnit];
            // valUnit: the current position in the chosen unit.
            // valPixel: the current position in pixels.
            float valUnit = 0, valPixel = 0, i = 0;
            while (valPixel <= max)
            {
                valPixel = _converter.ConvertToPixel(valUnit, vertical);
                var length = i % scale.BTickSteps == 0 ? _drawWidth / 4 :
                    i % scale.MTickSteps == 0 ? _drawWidth / 6 : _drawWidth / 16;
                using (Brush brush = new SolidBrush(_settings.Theme.TickColor))
                using (var pen = new Pen(brush, 1))
                using (var font = CommonString.GetFont("Arial", 12f))
                {
                    var pos = valPixel;
                    if (!vertical)
                    {
                        if (_resizeMode == RulerFormResizeMode.Horizontal || pos > length)
                            _g.DrawLine(pen, pos, 0, pos, length);
                        if (!_settings.SlimMode &&
                            (_resizeMode == RulerFormResizeMode.Horizontal || valPixel > _drawWidth))
                            _g.DrawLine(pen, pos, _drawWidth - length, pos, _drawWidth);
                        if (i % scale.BTickSteps == 0)
                        {
                            var text = Math.Round(valUnit, 2).ToString();
                            if (valPixel > 0)
                            {
                                var size = _g.MeasureString(text, font);
                                _g.DrawString(text, font, brush, pos - size.Width / 2, length + 3);
                            }
                            else if (valPixel == 0 && _resizeMode != RulerFormResizeMode.TwoDimensional)
                                _g.DrawString(text, font, brush, pos, length + 3);
                        }
                    }
                    else
                    {
                        if (_resizeMode == RulerFormResizeMode.Vertical || pos > length)
                            _g.DrawLine(pen, 0, pos, length, pos);
                        if (!_settings.SlimMode &&
                            (_resizeMode == RulerFormResizeMode.Vertical || valPixel > _drawWidth))
                            _g.DrawLine(pen, _drawWidth - length, pos, _drawWidth, pos);
                        if (i % scale.BTickSteps == 0)
                        {
                            var text = Math.Round(valUnit, 2).ToString();
                            if (valPixel > 0)
                                _g.DrawString(text, font, brush, length + 3, pos - 7);
                            else if (valPixel == 0 && _resizeMode != RulerFormResizeMode.TwoDimensional)
                                _g.DrawString(text, font, brush, length + 3, pos);
                        }
                    }
                }

                valUnit += scale.StepSize;
                i += 1;
            }

            // ----- Optionally, draw total length bound to right border and start offset -----
            if (_settings.ShowOffsetLengthLabels)
            {
                var roundingDigits = _settings.SlimMode ? 1 : 2;
                var lblLength = string.Format("{0}{1}",
                    Math.Round(_converter.ConvertFromPixel(max, vertical), roundingDigits), _converter.UnitString);
                var offset = vertical ? _c.Location.Y : _c.Location.X;
                var lblOffset = string.Format("{0}{1}",
                    Math.Round(_converter.ConvertFromPixel(offset, vertical), roundingDigits), _converter.UnitString);
                using (Brush brush = new SolidBrush(_settings.Theme.LengthLabelColor))
                using (var font = CommonString.GetFont("Arial", 12))
                {
                    var format = new StringFormat { Alignment = StringAlignment.Far };
                    if (!vertical)
                    {
                        float y = _drawWidth;
                        // adjust the label text based on ruler width
                        if (_settings.SlimMode) y -= 14;
                        else y /= 2.0f;
                        _g.DrawString(lblLength, font, brush, max, y, format);
                        // only draw offset label if not in two-dimensional mode, otherwise it would look messy
                        if (_resizeMode != RulerFormResizeMode.TwoDimensional)
                            _g.DrawString(lblOffset, font, brush, 0, y);
                    }
                    else
                    {
                        float x = _drawWidth;
                        if (!_settings.SlimMode) x *= 7.0f / 8.0f;
                        _g.DrawString(lblLength, font, brush, x, max - 14, format);
                        // only draw offset label if not in two-dimensional mode, otherwise it would look messy
                        if (_resizeMode != RulerFormResizeMode.TwoDimensional)
                            _g.DrawString(lblOffset, font, brush, x, 0, format);
                    }
                }
            }
        }

        /// <summary>
        ///     Draws the markers onto the given Graphics object.
        /// </summary>
        public void PaintMarkers(RulerMarkerCollection markers, Point mouse)
        {
            if (_resizeMode.HasFlag(RulerFormResizeMode.Horizontal))
                PaintMarkers(false, markers.Horizontal, mouse.X);
            if (_resizeMode.HasFlag(RulerFormResizeMode.Vertical))
                PaintMarkers(true, markers.Vertical, mouse.Y);
        }

        protected void PaintMarkers(bool vertical, IEnumerable<RulerMarker> markers, float mouseLine)
        {
            var rulerLength = vertical ? _c.Size.Height : _c.Size.Width;
            // Draw line showing the ruler's center
            if (_settings.ShowCenterLine)
            {
                var pos = (float)rulerLength / 2;
                var col = _settings.Theme.CenterLineColor;
                DrawMarker(new RulerMarker(pos, vertical), MARKER_SYMBOL_CENTER_LINE, col);
            }

            // Draw line showing the position of the cursor
            if (_settings.ShowMouseLine)
            {
                var col = _settings.Theme.MouseLineColor;
                DrawMarker(new RulerMarker(mouseLine, vertical), MARKER_SYMBOL_MOUSE_LINE, col, true);
            }

            // Draw the lines showing the thirds of the ruler
            if (_settings.ShowThirdLines)
            {
                var third = (float)rulerLength / 3;
                var col = _settings.Theme.ThirdsLinesColor;
                DrawMarker(new RulerMarker(third, vertical), MARKER_SYMBOL_THIRD_LINES, col);
                DrawMarker(new RulerMarker(2 * third, vertical), MARKER_SYMBOL_THIRD_LINES, col);
            }

            // Draw the line showing the Golden Ratio  
            // Golden Ratio: A/B = (A+B)/A, where A > B > 0
            // The marker | shows A of Golden ratio: <---A---|-B->
            // Add a second marker showing same thing but from right/bottom instead, called B
            if (_settings.ShowGoldenLine)
            {
                var goldenA = _phi * rulerLength;
                var goldenB = rulerLength - goldenA;
                var col = _settings.Theme.GoldenLineColor;
                DrawMarker(new RulerMarker(goldenA, vertical), MARKER_SYMBOL_GOLDEN_LINE, col);
                DrawMarker(new RulerMarker(goldenB, vertical), MARKER_SYMBOL_GOLDEN_LINE, col);
            }

            // Draw all given custom markers
            var x = 0;
            foreach (var marker in markers)
            {
                // Symbols for 1-20 using unicode. More than 20 - skip symbol
                var symbol = x < 20 ? char.ConvertFromUtf32(MARKER_SYMBOL_CUSTOM_LINE1 + ++x) : $"({++x}) ";
                var col = _settings.Theme.CustomLinesColor;
                DrawMarker(marker, symbol, col);
            }
        }

        private void DrawMarker(RulerMarker marker, string symbol, Color col, bool moveToRight = false)
        {
            if (marker.Value < 0 || !marker.Vertical && marker.Value > _c.Width ||
                marker.Vertical && marker.Value > _c.Height) return;
            // Number format with or without symbol depending on settings
            var numberFormat = _settings.ShowMarkerSymbol ? $"'{symbol}'.##" : ".##";
            // Note: StringFormatFlags.DirectionRightToLeft won't work with some symbols since it's intended for right-to-left languages.
            // Symbols gets placed before or after depending on category of language it belong to. 
            var format = new StringFormat { Alignment = StringAlignment.Far };
            if (marker.Vertical) format.LineAlignment = StringAlignment.Far;
            var text = string.Format("{0}{1}",
                _converter.ConvertFromPixel(marker).ToString(numberFormat), _converter.UnitString);

            using (Brush brush = new SolidBrush(col))
            using (var pen = new Pen(brush, _settings.MarkerThickness))
            using (var font = CommonString.GetFont("Arial", 12))
            {
                // If the marker is too far to the left/ top, the label would be cut. Therefore, move it to the right/ bottom.
                if (moveToRight)
                {
                    var size = _g.MeasureString(text, font);
                    if (marker.Vertical && marker.Value < size.Height) format.LineAlignment = StringAlignment.Near;
                    else if (!marker.Vertical && marker.Value < size.Width) format.Alignment = StringAlignment.Near;
                }

                var pos = marker.Value;
                if (!marker.Vertical)
                {
                    var height = _drawWidth;
                    if (_resizeMode.HasFlag(RulerFormResizeMode.Horizontal) &&
                        _resizeMode.HasFlag(RulerFormResizeMode.Vertical))
                        if (pos < _drawWidth)
                            height = _c.Height;
                    _g.DrawLine(pen, pos, 0, pos, height);
                    // only draw labels if we have enough space
                    if (!_settings.SlimMode)
                        _g.DrawString(text, font, brush, pos, _drawWidth / 2, format);
                }
                else
                {
                    var width = _drawWidth;
                    if (_resizeMode.HasFlag(RulerFormResizeMode.Horizontal) &&
                        _resizeMode.HasFlag(RulerFormResizeMode.Vertical))
                        if (pos < _drawWidth)
                            width = _c.Width;
                    _g.DrawLine(pen, 0, pos, width, pos);
                    if (!_settings.SlimMode)
                        _g.DrawString(text, font, brush, _drawWidth * (7.0f / 8.0f), pos, format);
                }
            }
        }
    }
}