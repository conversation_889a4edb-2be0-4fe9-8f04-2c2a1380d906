﻿using MetroFramework.Forms;
using OCRTools.Properties;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Text;
using System.Windows.Forms;
using System.Linq;
using OCRTools.Common;
using System.Web;
using System.Collections;
using System.ComponentModel;

namespace OCRTools.Language
{
    public static class LanguageHelper
    {
        public static string NowLanguage = "zh-CN";

        #region Init Form Text

        public static void InitLanguage(this Form form)
        {
            InitFormText(form, true);
        }

        public static void InitFormText(Form form, bool isSet)
        {
            InitTextInfo(form, new List<Control> { form }, isSet);
        }

        public static void InitTextInfo(Form form, IEnumerable controls, bool isSet)
        {
            foreach (Control control in controls)
            {
                if (control is NumericUpDown)
                {
                    continue;
                }
                //if (!isSet && control.AccessibleDescription == null)
                if (control.AccessibleDescription == null)
                {
                    control.AccessibleDescription = control.Text;
                }
                if (isSet && !(control is WebBrowser) && !(control is TextBox))
                {
                    var strNewText = string.Empty;
                    if (control is ComboBox || control is RichTextBox)
                    {
                        if (!string.IsNullOrWhiteSpace(control.Text))
                        {
                            strNewText = control.Text.CurrentText();
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrWhiteSpace(control.Name))
                        {
                            strNewText = form.CurrentText(control);
                        }
                        else if (!string.IsNullOrWhiteSpace(control.AccessibleDescription))
                        {
                            strNewText = control.AccessibleDescription.CurrentText();
                        }
                    }
                    if (!string.IsNullOrWhiteSpace(strNewText))
                    {
                        control.Text = strNewText;
                    }
                }
                if (control is ToolStrip strip)
                {
                    InitToolStripMenuItem(form, strip.Items, isSet);
                }
                else if (control is DataGridView grid)
                {
                    InitDataGridViewColumn(form, grid.Columns, isSet);
                }
                else if (control is MenuButton mbtn && mbtn.Menu != null)
                {
                    InitToolStripMenuItem(form, mbtn.Menu.Items, isSet);
                }
                //else if (control is ComboBox combox)
                //{
                //    InitComboBoxItem(combox, isSet);
                //}
                if (control.ContextMenuStrip != null)
                {
                    InitToolStripMenuItem(form, control.ContextMenuStrip.Items, isSet);
                }

                // Then recurse
                InitTextInfo(form, control.Controls, isSet);
            }
        }

        private static void InitComboBoxItem(ComboBox items, bool isSet)
        {
            if (isSet)
            {
                for (int i = 0; i < items.Items.Count; i++)
                {
                    var strNewText = items.Items[i].ToString().CurrentText();
                    if (!string.IsNullOrWhiteSpace(strNewText))
                    {
                        if (!Equals(items.SelectedItem, items.Items[i]))
                            items.Items[i] = strNewText;
                    }
                }
            }
        }

        private static void InitDataGridViewColumn(Form form, DataGridViewColumnCollection items, bool isSet)
        {
            foreach (DataGridViewColumn item in items)
            {
                //if (!isSet)
                {
                    if (item.Tag == null)
                    {
                        item.Tag = item.HeaderText;
                    }
                }
                //else
                {
                    if (item.Tag != null && !string.IsNullOrWhiteSpace(item.Tag.ToString()))
                    {
                        item.HeaderText = item.Tag.ToString().CurrentText();
                    }
                }
            }
        }

        private static void InitToolStripMenuItem(Form form, ToolStripItemCollection items, bool isSet)
        {
            foreach (ToolStripItem item in items)
            {
                //if (!isSet)
                {
                    if (item.AccessibleDescription == null)
                    {
                        item.AccessibleDescription = item.Text;
                    }
                    if (item.AccessibleName == null && !string.IsNullOrWhiteSpace(item.ToolTipText) && !Equals(item.Text, item.ToolTipText))
                    {
                        if (item.ToolTipText.Length < 20)
                            item.AccessibleName = item.ToolTipText;
                        else
                        {

                        }
                    }
                }
                //else
                {
                    var strNewText = "";
                    if (!string.IsNullOrWhiteSpace(item.Name))
                    {
                        strNewText = form.CurrentText(item);
                    }
                    else if (!string.IsNullOrWhiteSpace(item.AccessibleDescription))
                    {
                        strNewText = item.AccessibleDescription.CurrentText();
                    }
                    if (!string.IsNullOrWhiteSpace(strNewText))
                    {
                        item.Text = strNewText;
                    }

                    if (!string.IsNullOrWhiteSpace(item.AccessibleName))
                    {
                        item.ToolTipText = item.AccessibleName.CurrentText();
                    }
                    //else if (!string.IsNullOrWhiteSpace(item.Text))
                    //{
                    //    item.Text = item.Text.CurrentText();
                    //}
                }
                if (item is ToolStripDropDownItem tsm)
                {
                    InitToolStripMenuItem(form, tsm.DropDownItems, isSet);
                }
            }
        }

        #endregion

        public static void InstallLanguage(SupportedLanguage languageType, string language)
        {
            var strName = languageType.GetValue<DescriptionAttribute>("Description") + " " + LanguagePack.GetStaticText(language);
            var item = new ObjectTypeItem
            {
                Name = strName,
                AppPath = CommonString.DefaultLanguagePath,
                UpdateUrl = string.Format("{0}lang/{1}.lang?t=" + ServerTime.DateTime.Ticks, CommonString.HostUpdate?.FullUrl, language),
                Date = CommonMethod.GetLastWriteDate(CommonString.DefaultLanguagePath + language + ".lang"),
                Desc = strName
            };
            var result = CommonUpdate.Install(item.UpdateUrl, item.Date, item.Desc, item.Name, item.AppPath, false, true, true, false, null, true);
        }

        public static string GetStaticText(this string text, string language)
        {
            return DicStaticCulture[text].ContainsKey(language) ? DicStaticCulture[text][language] : text;
        }


        static ConcurrentDictionary<string, Dictionary<string, string>> DicNowCulture = new ConcurrentDictionary<string, Dictionary<string, string>>();
        static ConcurrentDictionary<string, Dictionary<string, string>> DicStaticCulture = new ConcurrentDictionary<string, Dictionary<string, string>>();

        static string LanguagePack = "语言包";
        static string StrInstall = "安装";
        static string StrUpdate = "更新";
        static LanguageHelper()
        {
#if DEBUG
            LoadBaseCulture();
#endif
            DicStaticCulture[LanguagePack] = new Dictionary<string, string>();
            DicStaticCulture[LanguagePack]["zh-CN"] = "语言包";
            DicStaticCulture[LanguagePack]["zh-TW"] = "語言包";
            DicStaticCulture[LanguagePack]["ja-JP"] = "言語パック";
            DicStaticCulture[LanguagePack]["ko-KR"] = "언어 팩";
            DicStaticCulture[LanguagePack]["de-DE"] = "Sprachpaket";
            DicStaticCulture[LanguagePack]["fr-FR"] = "Pack de langues";
            DicStaticCulture[LanguagePack]["en-US"] = "Language Pack";

            DicStaticCulture[StrInstall] = new Dictionary<string, string>();
            DicStaticCulture[StrInstall]["zh-CN"] = "安装";
            DicStaticCulture[StrInstall]["zh-TW"] = "安裝";
            DicStaticCulture[StrInstall]["ja-JP"] = "インストール";
            DicStaticCulture[StrInstall]["ko-KR"] = "설치";
            DicStaticCulture[StrInstall]["de-DE"] = "Installieren";
            DicStaticCulture[StrInstall]["fr-FR"] = "Installer";
            DicStaticCulture[StrInstall]["en-US"] = "Install";

            DicStaticCulture[StrUpdate] = new Dictionary<string, string>();
            DicStaticCulture[StrUpdate]["zh-CN"] = "更新";
            DicStaticCulture[StrUpdate]["zh-TW"] = "更新";
            DicStaticCulture[StrUpdate]["ja-JP"] = "更新";
            DicStaticCulture[StrUpdate]["ko-KR"] = "업데이트";
            DicStaticCulture[StrUpdate]["de-DE"] = "Update";
            DicStaticCulture[StrUpdate]["fr-FR"] = "Mise à jour";
            DicStaticCulture[StrUpdate]["en-US"] = "Update";
        }

        public static void InitLanguage(string strLanguage)
        {
#if DEBUG
            InitAllCulture(DicNowCulture, NowLanguage);
#endif

            SupportedLanguage type;
            if (string.IsNullOrWhiteSpace(strLanguage))
            {
                //根据系统语言查找支持的语言
                type = Enum.GetValues(typeof(SupportedLanguage)).OfType<SupportedLanguage>()
                    .FirstOrDefault(p => Equals(p.GetValue<MenuCategoryAttribute>(), CultureInfo.InstalledUICulture.Name));
            }
            else
            {
                Enum.TryParse(strLanguage, out type);
            }

            DicNowCulture = new ConcurrentDictionary<string, Dictionary<string, string>>();
            NowLanguage = type.GetValue<MenuCategoryAttribute>();

            if (!Equals(SupportedLanguage.SimplifiedChinese, type))
            {
                var langFile = CommonString.DefaultLanguagePath + NowLanguage + ".lang";

                if (!File.Exists(langFile) || Application.OpenForms.Count <= 0)
                    InstallLanguage(type, NowLanguage);

                if (File.Exists(langFile))
                {
                    try
                    {
                        DicNowCulture = CommonString.JavaScriptSerializer.Deserialize<ConcurrentDictionary<string, Dictionary<string, string>>>(File.ReadAllText(langFile, Encoding.UTF8));
                    }
                    catch (Exception oe)
                    {
                        try
                        {
                            File.Delete(langFile);
                        }
                        catch { }
                    }
                }
                if (DicNowCulture.Count <= 0)
                {
                    type = SupportedLanguage.SimplifiedChinese;
                    NowLanguage = type.GetValue<MenuCategoryAttribute>();
                }
            }

            CommonSetting.SetValue("语言", type.ToString());

#if DEBUG
            InitAllCulture(DicNowCulture, NowLanguage);
            SaveBaseCulture();
#endif
        }

#if DEBUG
        static ConcurrentDictionary<string, Dictionary<string, Dictionary<string, string>>> DicAllCulture = new ConcurrentDictionary<string, Dictionary<string, Dictionary<string, string>>>();
        private static List<string> lstBingKeys = new List<string>() { "3DAEE5B978BA031557E739EE1E2A68CB1FAD5909", "708BEDCB01828123DC7B6C6A6AB12EF82DFBB611", "ABB1C5A823DC3B7B1D5F4BDB886ED308B50D1919", "F84955C82256C25518548EE0C161B0BF87681F2F", "AC56E0F30DC3119A55994244361E06DC1B777049", "6844AE3580856D2EC7A64C79F55F11AA47CB961B", "78280AF4DFA1CE1676AFE86340C690023A5AC139", "76518BFCEBBF18E107C7073FBD4A735001B56BB1", "73B027BB51D74FB461C097BCCF841DB5678FDBB3", "A4D660A48A6A97CCA791C34935E4C02BBB1BEC1C", "AFC76A66CF4F434ED080D245C30CF1E71C22959C", "3D8D4E1888B88B975484F0CA25CDD24AAC457ED8", "C21742C60D890BCA6B3819EDAD45B74C77A25658", "3C9778666C5BA4B406FFCBEE64EF478963039C51", "FF274CA911390CAF2E950A1930E5700DB887D8D7", "C9739C3837CBBD166870AF1C6EFFDEBE433DC2A8", "DB50E2E9FBE2E92B103E696DCF4E3E512A8826FB", "47D78E7A59A91431BD06A3D8D5496E6308634F46" };

        private static void InitAllCulture(ConcurrentDictionary<string, Dictionary<string, string>> dicTmp, string language)
        {
            foreach (var form in dicTmp.Keys)
            {
                if (!DicAllCulture.ContainsKey(form))
                {
                    DicAllCulture[form] = new Dictionary<string, Dictionary<string, string>>();
                }
                var items = dicTmp[form];
                foreach (var key in items.Keys)
                {
                    if (!DicAllCulture[form].ContainsKey(key))
                    {
                        DicAllCulture[form][key] = new Dictionary<string, string>();
                    }
                    DicAllCulture[form][key][language] = items[key];
                }
            }
        }

        class BingTranslateInfo
        {
            public string TranslatedText { get; set; }
        }

        public static void LoadBaseCulture()
        {
            try
            {
                if (File.Exists("all.lang"))
                {
                    DicAllCulture = CommonString.JavaScriptSerializer.Deserialize<ConcurrentDictionary<string, Dictionary<string, Dictionary<string, string>>>>(File.ReadAllText("all.lang", Encoding.UTF8));
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        public static void SaveAllCulture()
        {
            if (DicAllCulture.Count <= 0) return;
            Dictionary<string, ConcurrentDictionary<string, Dictionary<string, string>>> dicAll = new Dictionary<string, ConcurrentDictionary<string, Dictionary<string, string>>>();
            dicAll["zh-CN"] = new ConcurrentDictionary<string, Dictionary<string, string>>();
            dicAll["en-US"] = new ConcurrentDictionary<string, Dictionary<string, string>>();
            dicAll["fr-FR"] = new ConcurrentDictionary<string, Dictionary<string, string>>();
            dicAll["de-DE"] = new ConcurrentDictionary<string, Dictionary<string, string>>();
            dicAll["ja-JP"] = new ConcurrentDictionary<string, Dictionary<string, string>>();
            dicAll["ko-KR"] = new ConcurrentDictionary<string, Dictionary<string, string>>();
            dicAll["zh-TW"] = new ConcurrentDictionary<string, Dictionary<string, string>>();

            foreach (var culName in dicAll.Keys)
            {
                var language = dicAll[culName];
                foreach (var form in DicAllCulture.Keys)
                {
                    if (!language.ContainsKey(form))
                    {
                        language[form] = new Dictionary<string, string>();
                    }
                    foreach (var key in DicAllCulture[form].Keys)
                    {
                        if (DicAllCulture[form][key].ContainsKey(culName))
                        {
                            language[form][key] = DicAllCulture[form][key][culName];
                        }
                    }
                }
            }
            var fromZhTo = new List<string> { "zh-TW", "ja-JP", "ko-KR", "en-US" };
            var zhCN = dicAll["zh-CN"];
            //foreach (var form in zhCN.Keys)
            //{
            //    foreach (var key in zhCN[form].Keys)
            //    {
            //        var lstKey = new List<string>();
            //        foreach (var language in dicAll.Keys)
            //        {
            //            if (language.Equals("zh-CN")) continue;
            //            if (dicAll[language].ContainsKey(form) && dicAll[language][form].ContainsKey(key) && Equals(zhCN[form][key], dicAll[language][form][key]))
            //            {
            //                dicAll[language][form].Remove(key);
            //                lstKey.Add(language);
            //            }
            //        }
            //    }
            //}
            foreach (var culture in fromZhTo)
            {
                var language = dicAll[culture];
                var tansType = culture.Substring(0, 2);
                if (Equals(tansType, "zh"))
                {
                    tansType = culture.Contains("zh-CN") ? "zh-Hans" : "zh-Hant";
                }
                foreach (var form in zhCN.Keys)
                {
                    var lstToTrans = zhCN[form].Where(p => !language[form].ContainsKey(p.Key)).ToList();
                    if (lstToTrans.Count <= 0) continue;
                    var dicTrans = TransLanguage(lstToTrans, tansType);
                    foreach (var item in dicTrans.Keys)
                    {
                        language[form][item] = dicTrans[item];
                    }
                }
            }


            var frmEnTo = new List<string> { "fr-FR", "de-DE" };
            var en = dicAll["en-US"];
            foreach (var culture in frmEnTo)
            {
                var toLanguage = dicAll[culture];
                var tansType = culture.Substring(0, 2);
                if (Equals(tansType, "zh"))
                {
                    tansType = culture.Contains("zh-CN") ? "zh-Hans" : "zh-Hant";
                }
                foreach (var form in en.Keys)
                {
                    var lstToTrans = en[form].Where(p => !toLanguage[form].ContainsKey(p.Key)).ToList();
                    if (lstToTrans.Count <= 0) continue;
                    var dicTrans = TransLanguage(lstToTrans, tansType);
                    foreach (var item in dicTrans.Keys)
                    {
                        toLanguage[form][item] = dicTrans[item];
                    }
                }
            }

            foreach (var cultureName in dicAll.Keys)
            {
                File.WriteAllText(Application.StartupPath + "\\Lang\\" + cultureName + ".lang", CommonString.JavaScriptSerializer.Serialize(dicAll[cultureName]), Encoding.UTF8);
                NowLanguage = cultureName;
                InitAllCulture(dicAll[cultureName], cultureName);
            }
            SaveBaseCulture();
        }

        private static Dictionary<string, string> TransLanguage(List<KeyValuePair<string, string>> dicTmp, string tansType)
        {
            var dicResult = new Dictionary<string, string>();
            var values = dicTmp.Select(p => p.Value).ToList();
            var batchSize = 100;
            var notTransValues = values.Select((x, index) => new { x, index })
                 .GroupBy(item => item.index / batchSize)
                 .Select(group => group.Select(item => item.x).ToArray())
                 .ToList();
            notTransValues.ForEach(val =>
            {
                var url = "https://api.microsofttranslator.com/V2/Ajax.svc/TranslateArray2?oncomplete=&appId=【【Key】】&to=" + tansType + "&texts=" + HttpUtility.UrlEncode(CommonString.JavaScriptSerializer.Serialize(val));
                var html = "";
                for (int i = 0; i < lstBingKeys.Count; i++)
                {
                    html = WebClientExt.GetHtml(url.Replace("【【Key】】", lstBingKeys.GetRndItem()));
                    if (!string.IsNullOrWhiteSpace(html) && html.StartsWith("["))
                    {
                        break;
                    }
                }

                if (string.IsNullOrWhiteSpace(html) || !html.StartsWith("["))
                {
                    html = WebClientExt.GetHtml(url.Replace("【【Key】】", lstBingKeys.GetRndItem()));
                }
                if (!string.IsNullOrWhiteSpace(html) && html.StartsWith("["))
                {
                    var lstTrans = CommonString.JavaScriptSerializer.Deserialize<List<BingTranslateInfo>>(html);
                    if (lstTrans.Count > 0 && lstTrans.Count == val.Length)
                    {
                        int index = 0;
                        foreach (var strItem in val)
                        {
                            var key = dicTmp.FirstOrDefault(p => Equals(p.Value, strItem)).Key;
                            dicResult[key] = lstTrans[index].TranslatedText;
                            index++;
                        }
                    }
                    //dicTmp[key][strItem] = html.Replace("\"", "").Trim();
                }
            });
            return dicResult;
        }

        public static void SaveBaseCulture()
        {
            File.WriteAllText("all.lang", CommonString.JavaScriptSerializer.Serialize(DicAllCulture), Encoding.UTF8);
        }

#endif

        private static readonly string CONST_KEY = "const";

        public static string CurrentText(this Form form, Control ctrl)
        {
            //未加载，不处理!form.IsLanguageInit() ||
            if (ctrl is TextBox || ctrl is RichTextBox)
                return ctrl.Text;
            var formName = form.GetType().Name;
            //已加载，无对应语言
            if (!DicNowCulture.ContainsKey(formName) || !DicNowCulture[formName].ContainsKey(ctrl.Name))
            {
                if (!DicNowCulture.ContainsKey(formName))
                    DicNowCulture[formName] = new Dictionary<string, string>();
                var value = ctrl.AccessibleDescription ?? ctrl.Text;
                if (!string.IsNullOrWhiteSpace(value))
                    DicNowCulture[formName][ctrl.Name] = value;
            }
            return DicNowCulture.ContainsKey(formName) && DicNowCulture[formName].ContainsKey(ctrl.Name) ? DicNowCulture[formName][ctrl.Name] : ctrl.Text;
        }

        public static string CurrentText(this Form form, ToolStripItem ctrl)
        {
            ////未加载，不处理
            //if (!form.IsLanguageInit())
            //    return ctrl.Text;
            var formName = form.GetType().Name;
            //已加载，无对应语言
            if (!DicNowCulture.ContainsKey(formName) || !DicNowCulture[formName].ContainsKey(ctrl.Name))
            {
                if (!DicNowCulture.ContainsKey(formName))
                    DicNowCulture[formName] = new Dictionary<string, string>();
                var value = ctrl.AccessibleDescription ?? ctrl.Text;
                if (!string.IsNullOrWhiteSpace(value))
                    DicNowCulture[formName][ctrl.Name] = value;
            }
            return DicNowCulture.ContainsKey(formName) && DicNowCulture[formName].ContainsKey(ctrl.Name) ? DicNowCulture[formName][ctrl.Name] : ctrl.Text;
        }

        public static string CurrentText(this string key)
        {
            if (!DicNowCulture.ContainsKey(CONST_KEY))
                DicNowCulture[CONST_KEY] = new Dictionary<string, string>();
            if (!DicNowCulture[CONST_KEY].ContainsKey(key))
                DicNowCulture[CONST_KEY][key] = key;
            return DicNowCulture.ContainsKey(CONST_KEY) && DicNowCulture[CONST_KEY].ContainsKey(key) ? DicNowCulture[CONST_KEY][key] : key;
        }

        public static string OriginText(this MetroForm form, Control ctrl)
        {
            return ctrl.AccessibleDescription ?? ctrl.Text;
        }

        public static string OriginText(this MetroForm form, ToolStripItem ctrl)
        {
            return ctrl.AccessibleDescription ?? ctrl.Text;
        }

        public static Image GetLanguageIcon(SupportedLanguage language)
        {
            Image icon;

            switch (language)
            {
                case SupportedLanguage.English:
                    icon = Resources.英文;
                    break;
                case SupportedLanguage.French:
                    icon = Resources.法语;
                    break;
                case SupportedLanguage.German:
                    icon = Resources.德语;
                    break;
                case SupportedLanguage.Japanese:
                    icon = Resources.日语;
                    break;
                case SupportedLanguage.Korean:
                    icon = Resources.韩语;
                    break;
                case SupportedLanguage.SimplifiedChinese:
                    icon = Resources.中文;
                    break;
                case SupportedLanguage.TraditionalChinese:
                    icon = Resources.香港繁体;
                    break;
                default:
                    icon = Resources.自动;
                    break;
            }

            if (icon != null)
            {
                icon = Common.ImageProcessHelper.ResizeImage(new Bitmap(icon), new Size(21, 21), true);
            }

            return icon;
        }
    }
}
