﻿using System;
using System.Drawing;

namespace OCRTools.Colors
{
    /// <summary>
    ///     A collection of colors that specify the appearance of the ruler.
    /// </summary>
    [Serializable]
    public class Theme
    {
        /// <summary>
        ///     The background color of the ruler.
        /// </summary>
        public Color Background { get; set; }

        /// <summary>
        ///     The color of the ticks on the ruler scale.
        /// </summary>
        public Color TickColor { get; set; }

        /// <summary>
        ///     The color of the label that shows the total length of the ruler.
        /// </summary>
        public Color LengthLabelColor { get; set; }

        /// <summary>
        ///     The color of the marker that indicates the cursor position.
        /// </summary>
        public Color MouseLineColor { get; set; }

        /// <summary>
        ///     The color of the marker that indicates the center of the ruler.
        /// </summary>
        public Color CenterLineColor { get; set; }

        /// <summary>
        ///     The color of the markers that divide the ruler into three parts.
        /// </summary>
        public Color ThirdsLinesColor { get; set; }

        /// <summary>
        ///     The color of the marker that indicates the golden ratio on the ruler.
        /// </summary>
        public Color GoldenLineColor { get; set; }

        /// <summary>
        ///     The color of custom markers.
        /// </summary>
        public Color CustomLinesColor { get; set; }
    }
}