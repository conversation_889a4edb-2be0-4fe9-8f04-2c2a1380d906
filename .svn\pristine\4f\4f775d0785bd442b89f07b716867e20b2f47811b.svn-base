﻿using MetroFramework.Components;
using MetroFramework.Design;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using System.Windows.Forms.Design;

/**
 * MetroFramework - Modern UI for WinForms
 * 
 * The MIT License (MIT)
 * Copyright (c) 2011 <PERSON>, http://github.com/viperneo
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of 
 * this software and associated documentation files (the "Software"), to deal in the 
 * Software without restriction, including without limitation the rights to use, copy, 
 * modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, 
 * and to permit persons to whom the Software is furnished to do so, subject to the 
 * following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in 
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
 * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT 
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF 
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE 
 * OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

namespace MetroFramework.Controls
{
    [ToolboxBitmap(typeof(LinkLabel))]
    [DefaultEvent("Click")]
    [Designer(typeof(MetroLinkDesigner), typeof(ParentControlDesigner))]
    public class MetroLink : Button, IMetroControl
    {
        private Image _darkimg;

        private Image _darklightimg;

        private Image _image;

        private int _imagesize = 16;

        private Image _lightimg;

        private Image _lightlightimg;

        private Color foreColor;

        private bool isFocused;

        private bool isHovered;

        private bool isPressed;

        private MetroColorStyle metroStyle;

        private MetroThemeStyle metroTheme;

        public MetroLink()
        {
            SetStyle(
                ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.SupportsTransparentBackColor |
                ControlStyles.OptimizedDoubleBuffer, true);
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool DisplayFocus { get; set; }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseCustomBackColor { get; set; }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseCustomForeColor { get; set; }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseStyleColors { get; set; }

        [Category("Metro Behaviour")]
        [DefaultValue(false)]
        [Browsable(false)]
        public bool UseSelectable
        {
            get => GetStyle(ControlStyles.Selectable);
            set => SetStyle(ControlStyles.Selectable, value);
        }

        [DefaultValue(null)]
        [Category("Metro Appearance")]
        public new virtual Image Image
        {
            get => _image;
            set
            {
                _image = value;
                createimages();
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(null)]
        public Image NoFocusImage { get; set; }

        [Category("Metro Appearance")]
        [DefaultValue(16)]
        public int ImageSize
        {
            get => _imagesize;
            set
            {
                _imagesize = value;
                Invalidate();
            }
        }

        public override string Text
        {
            get => base.Text;
            set
            {
                base.Text = value;
                if (AutoSize && _image != null)
                {
                    Width = TextRenderer.MeasureText(value, MetroFonts.Link(FontSize, FontWeight)).Width;
                    Width += _imagesize + 2;
                }
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroLinkSize.Small)]
        public MetroLinkSize FontSize { get; set; }

        [Category("Metro Appearance")]
        [DefaultValue(MetroLinkWeight.Bold)]
        public MetroLinkWeight FontWeight { get; set; } = MetroLinkWeight.Bold;

        [Browsable(false)]
        public override Font Font
        {
            get => base.Font;
            set => base.Font = value;
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroColorStyle.Blue)]
        public MetroColorStyle Style
        {
            get
            {
                if (DesignMode || metroStyle != 0) return metroStyle;
                if (StyleManager != null) return StyleManager.Style;
                if (StyleManager == null) return MetroColorStyle.Blue;
                return metroStyle;
            }
            set => metroStyle = value;
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroThemeStyle.Light)]
        public MetroThemeStyle Theme
        {
            get
            {
                if (DesignMode || metroTheme != 0) return metroTheme;
                if (StyleManager != null) return StyleManager.Theme;
                if (StyleManager == null) return MetroThemeStyle.Light;
                return metroTheme;
            }
            set => metroTheme = value;
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        public MetroStyleManager StyleManager { get; set; }

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintBackground != null) CustomPaintBackground(this, e);
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaint != null) CustomPaint(this, e);
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintForeground != null) CustomPaintForeground(this, e);
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            try
            {
                var color = BackColor;
                if (!UseCustomBackColor) color = MetroPaint.BackColor.Form(Theme);
                if (color.A == byte.MaxValue && BackgroundImage == null)
                {
                    e.Graphics.Clear(color);
                }
                else
                {
                    base.OnPaintBackground(e);
                    OnCustomPaintBackground(new MetroPaintEventArgs(color, Color.Empty, e.Graphics));
                }
            }
            catch
            {
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            try
            {
                if (GetStyle(ControlStyles.AllPaintingInWmPaint)) OnPaintBackground(e);
                OnCustomPaint(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
                OnPaintForeground(e);
            }
            catch
            {
                Invalidate();
            }
        }

        protected virtual void OnPaintForeground(PaintEventArgs e)
        {
            if (UseCustomForeColor)
                foreColor = ForeColor;
            else if (isHovered && !isPressed && Enabled)
                foreColor = MetroPaint.ForeColor.Link.Normal(Theme);
            else if (isHovered && isPressed && Enabled)
                foreColor = MetroPaint.ForeColor.Link.Press(Theme);
            else if (!Enabled)
                foreColor = MetroPaint.ForeColor.Link.Disabled(Theme);
            else
                foreColor = !UseStyleColors ? MetroPaint.ForeColor.Link.Hover(Theme) : MetroPaint.GetStyleColor(Style);
            TextRenderer.DrawText(e.Graphics, Text, MetroFonts.Link(FontSize, FontWeight), ClientRectangle, foreColor,
                MetroPaint.GetTextFormatFlags(TextAlign));
            OnCustomPaintForeground(new MetroPaintEventArgs(Color.Empty, foreColor, e.Graphics));
            if (DisplayFocus && isFocused) ControlPaint.DrawFocusRectangle(e.Graphics, ClientRectangle);
            if (_image != null) DrawIcon(e.Graphics);
        }

        private void DrawIcon(Graphics g)
        {
            if (Image == null) return;
            var num = _imagesize;
            var num2 = _imagesize;
            if (_imagesize == 0)
            {
                num = _image.Width;
                num2 = _image.Height;
            }

            var location = new Point(2, (ClientRectangle.Height - _imagesize) / 2);
            var num3 = 0;
            switch (ImageAlign)
            {
                case ContentAlignment.BottomCenter:
                    location = new Point((ClientRectangle.Width - num) / 2, ClientRectangle.Height - num2 - num3);
                    break;
                case ContentAlignment.BottomLeft:
                    location = new Point(num3, ClientRectangle.Height - num2 - num3);
                    break;
                case ContentAlignment.BottomRight:
                    location = new Point(ClientRectangle.Width - num - num3, ClientRectangle.Height - num2 - num3);
                    break;
                case ContentAlignment.MiddleCenter:
                    location = new Point((ClientRectangle.Width - num) / 2, (ClientRectangle.Height - num2) / 2);
                    break;
                case ContentAlignment.MiddleLeft:
                    location = new Point(num3, (ClientRectangle.Height - num2) / 2);
                    break;
                case ContentAlignment.MiddleRight:
                    location = new Point(ClientRectangle.Width - num - num3, (ClientRectangle.Height - num2) / 2);
                    break;
                case ContentAlignment.TopCenter:
                    location = new Point((ClientRectangle.Width - num) / 2, num3);
                    break;
                case ContentAlignment.TopLeft:
                    location = new Point(num3, num3);
                    break;
                case ContentAlignment.TopRight:
                    location = new Point(ClientRectangle.Width - num - num3, num3);
                    break;
            }

            location.Y++;
            if (NoFocusImage == null)
            {
                if (Theme == MetroThemeStyle.Dark)
                    g.DrawImage(isHovered && !isPressed ? _darkimg : _darklightimg,
                        new Rectangle(location, new Size(num, num2)));
                else
                    g.DrawImage(isHovered && !isPressed ? _lightimg : _lightlightimg,
                        new Rectangle(location, new Size(num, num2)));
            }
            else if (Theme == MetroThemeStyle.Dark)
            {
                g.DrawImage(isHovered && !isPressed ? _darkimg : NoFocusImage,
                    new Rectangle(location, new Size(num, num2)));
            }
            else
            {
                g.DrawImage(isHovered && !isPressed ? _image : NoFocusImage,
                    new Rectangle(location, new Size(num, num2)));
            }
        }

        private void createimages()
        {
            if (_image != null)
            {
                _lightimg = _image;
                _darkimg = ApplyInvert(new Bitmap(_image));
                _darklightimg = ApplyLight(new Bitmap(_darkimg));
                _lightlightimg = ApplyLight(new Bitmap(_lightimg));
            }
        }

        public Bitmap ApplyInvert(Bitmap bitmapImage)
        {
            for (var i = 0; i < bitmapImage.Height; i++)
            for (var j = 0; j < bitmapImage.Width; j++)
            {
                var pixel = bitmapImage.GetPixel(j, i);
                var a = pixel.A;
                var red = (byte) (255 - pixel.R);
                var green = (byte) (255 - pixel.G);
                var blue = (byte) (255 - pixel.B);
                bitmapImage.SetPixel(j, i, Color.FromArgb(a, red, green, blue));
            }

            return bitmapImage;
        }

        public Bitmap ApplyLight(Bitmap bitmapImage)
        {
            for (var i = 0; i < bitmapImage.Height; i++)
            for (var j = 0; j < bitmapImage.Width; j++)
            {
                var pixel = bitmapImage.GetPixel(j, i);
                var alpha = pixel.A;
                if (pixel.A <= byte.MaxValue && pixel.A >= 100) alpha = 90;
                var r = pixel.R;
                var g = pixel.G;
                var b = pixel.B;
                bitmapImage.SetPixel(j, i, Color.FromArgb(alpha, r, g, b));
            }

            return bitmapImage;
        }

        protected override void OnGotFocus(EventArgs e)
        {
            isFocused = true;
            isPressed = false;
            Invalidate();
            base.OnGotFocus(e);
        }

        protected override void OnLostFocus(EventArgs e)
        {
            isFocused = false;
            isHovered = false;
            isPressed = false;
            Invalidate();
            base.OnLostFocus(e);
        }

        protected override void OnEnter(EventArgs e)
        {
            isFocused = true;
            isPressed = true;
            Invalidate();
            base.OnEnter(e);
        }

        protected override void OnLeave(EventArgs e)
        {
            isFocused = false;
            isHovered = false;
            isPressed = false;
            Invalidate();
            base.OnLeave(e);
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Space)
            {
                isHovered = true;
                isPressed = true;
                Invalidate();
            }

            base.OnKeyDown(e);
        }

        protected override void OnKeyUp(KeyEventArgs e)
        {
            if (!isFocused)
            {
                isHovered = false;
                isPressed = false;
            }

            Invalidate();
            base.OnKeyUp(e);
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            isHovered = true;
            Invalidate();
            base.OnMouseEnter(e);
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                isPressed = true;
                Invalidate();
                if (Name == "lnkClear" && Parent.GetType().Name == "MetroTextBox") PerformClick();
                if (Name == "lnkClear" && Parent.GetType().Name == "SearchControl") PerformClick();
            }

            base.OnMouseDown(e);
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            isPressed = false;
            Invalidate();
            base.OnMouseUp(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            isHovered = false;
            isPressed = false;
            Invalidate();
            base.OnMouseLeave(e);
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);
            Invalidate();
        }
    }
}