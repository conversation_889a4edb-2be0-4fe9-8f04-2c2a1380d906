﻿using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;

namespace OCRTools
{
    public static class IniHelper
    {
        [DllImport("kernel32", CharSet = CharSet.Ansi)]
        private static extern int GetPrivateProfileString(string sectionName, string key, string defaultValue,
            byte[] returnBuffer, int size, string filePath);

        [DllImport("kernel32", CharSet = CharSet.Ansi)]
        private static extern long WritePrivateProfileString(string sectionName, string key, string value,
            string filePath);

        public static string GetValue(string sectionName, string key, string defaultValue = null)
        {
            string result = null;
            try
            {
                if (!string.IsNullOrEmpty(key))
                {
                    InitIniFile(CommonSetting.IniFileName);
                    var array = new byte[1024 * 5];
                    var privateProfileString =
                        GetPrivateProfileString(sectionName, key, string.Empty, array, array.Length, CommonSetting.IniFileName);
                    result = Encoding.Default.GetString(array, 0, privateProfileString);
                    array = null;
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine("读取配置文件出错！" + oe.Message);
            }

            if (string.IsNullOrEmpty(result) && !string.IsNullOrEmpty(defaultValue)) result = defaultValue;
            return result;
        }

        //public static List<string> GetListValue(string sectionName, string key, string strSpilt = ",")
        //{
        //    var lstTmp = new List<string>();
        //    var str = GetValue(sectionName, key);
        //    if (!string.IsNullOrEmpty(str) && !string.IsNullOrEmpty(strSpilt) && str.Contains(strSpilt))
        //        lstTmp.AddRange(str.Split(new[] {strSpilt}, StringSplitOptions.RemoveEmptyEntries));
        //    return lstTmp;
        //}

        public static bool SetValue(string sectionName, string key, string value, string iniFileName = null)
        {
            if (string.IsNullOrEmpty(iniFileName))
            {
                iniFileName = CommonSetting.IniFileName;
            }
            InitIniFile(iniFileName);
            var result = (int)WritePrivateProfileString(sectionName, key, value, iniFileName) > 0;

            return result;
        }

        private static void InitIniFile(string fileName)
        {
            if (!File.Exists(fileName))
                using (File.Create(fileName))
                {
                }
        }
    }
}