﻿using MetroFramework.Native;
using OCRTools.Common;
using OCRTools.HelpersLib;
using OCRTools.Language;
using OCRTools.Properties;
using OCRTools.UserControlEx;
using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace OCRTools.ScreenCaptureLib
{
    internal partial class ShapeManager
    {
        public bool ToolbarCreated { get; private set; }
        public bool ToolbarCollapsed { get; private set; }

        internal TextAnimation MenuTextAnimation = new TextAnimation()
        {
            FadeInDuration = TimeSpan.FromMilliseconds(0),
            Duration = TimeSpan.FromMilliseconds(5000),
            FadeOutDuration = TimeSpan.FromMilliseconds(500)
        };

        private Form menuForm;
        private ToolStripEx tsMain;
        private ToolStripEx tsChild;
        private ToolStripButton tsbBorderColor, tsbFillColor, tsbHighlightColor;
        private ToolStripMenuItem tsmiShadowColor;
        //private ToolStripMenuItem tsmiUndo, tsmiDuplicate, tsmiDelete, tsmiDeleteAll,
        //    tsmiMoveTop, tsmiMoveUp, tsmiMoveDown, tsmiMoveBottom;
        private ToolStripLabeledNumericUpDown tslnudPixelateSize, tslnudStepFontSize,//tslnudCenterPoints,
            tslnudStartingStepValue, tslnudMagnifyStrength;
        private ToolStripLabel tslDragLeft, tslDragRight;
        private ToolStripLabeledComboBox tslnudBorderSize, tscbBorderStyle, tscbArrowHeadDirection, tscbStepType;//tscbImageInterpolationMode, 

        internal void CreateToolbar()
        {
            if (menuForm != null)
            {
                menuForm.Controls.Clear();
                menuForm.Close();
                menuForm.Dispose();
                menuForm = null;
            }

            menuForm = new Form
            {
                Font = CommonString.GetSysNormalFont(9F, false),
                AutoScaleMode = CommonString.CommonAutoScaleMode,
                AutoSize = true,
                AutoSizeMode = AutoSizeMode.GrowAndShrink,
                ClientSize = new Size(759, 509),
                FormBorderStyle = FormBorderStyle.None,
                Location = new Point(200, 200),
                ShowInTaskbar = false,
                StartPosition = FormStartPosition.Manual,
                Text = CommonString.FullName,
                TopMost = true,
                BackColor = Color.Gray,
                TransparencyKey = Color.Gray,
            };

            menuForm.Shown += MenuForm_Shown;
            menuForm.KeyDown += MenuForm_KeyDown;
            menuForm.KeyUp += MenuForm_KeyUp;
            menuForm.LocationChanged += MenuForm_LocationChanged;
            menuForm.GotFocus += MenuForm_GotFocus;
            menuForm.LostFocus += MenuForm_LostFocus;

            menuForm.SuspendLayout();

            tsMain = new ToolStripEx
            {
                Font = menuForm.Font,
                AutoSize = true,
                CanOverflow = true,
                ClickThrough = true,
                Dock = DockStyle.None,
                GripStyle = ToolStripGripStyle.Hidden,
                Location = new Point(0, 0),
                MinimumSize = new Size(10, 30),
                Padding = Form.IsEditorMode ? new Padding(5, 1, 3, 0) : new Padding(2, 1, 0, 0),
                //Renderer = new ToolStripRoundedEdgeRenderer(),
                TabIndex = 0,
                ShowItemToolTips = false
            };

            tsMain.MouseLeave += TsMain_MouseLeave;
            tsMain.SuspendLayout();
            menuForm.Controls.Add(tsMain);

            tsChild = new ToolStripEx
            {
                Font = menuForm.Font,
                AutoSize = true,
                CanOverflow = true,
                ClickThrough = true,
                Dock = DockStyle.None,
                GripStyle = ToolStripGripStyle.Hidden,
                Location = new Point(0, tsMain.Bottom + 5),
                MinimumSize = new Size(10, 30),
                Padding = new Padding(5, 1, 3, 0),
                //Renderer = new ToolStripRoundedEdgeRenderer(),
                TabIndex = 1,
                ShowItemToolTips = false,
                Visible = false
            };

            if (Form.IsEditorMode)
            {
                tsChild.MouseLeave += TsMain_MouseLeave;
                tsChild.SuspendLayout();
                menuForm.Controls.Add(tsChild);
            }

            tslDragLeft = new ToolStripLabel("鼠标左键拖动，右键隐藏".CurrentText())
            {
                DisplayStyle = ToolStripItemDisplayStyle.Image,
                ImageScaling = ToolStripItemImageScaling.None,
                Image = ImageHelp.DrawGrip(Color.FromArgb(255, 22, 26, 31), Color.FromArgb(255, 56, 64, 75)),
                Margin = new Padding(0, 0, 2, 0),
                Padding = new Padding(2),
                Visible = true
            };

            tsMain.Items.Add(tslDragLeft);

            #region Tools

            foreach (ShapeType shapeType in Enum.GetValues(typeof(ShapeType)))
            {
                if (Form.IsAnnotationMode)
                {
                    var isRegionType = IsShapeTypeRegion(shapeType);
                    if (Form.IsEditorMode && isRegionType)
                    {
                        continue;
                    }
                    if (!Form.IsEditorMode && !isRegionType)
                    {
                        continue;
                    }
                }
                //if (!Form.IsEditorMode)
                //{
                //    continue;
                //}

                ToolStripButton tsbShapeType = new ToolStripButton(shapeType.ToString().CurrentText())
                {
                    DisplayStyle = ToolStripItemDisplayStyle.Image
                };

                Image img = null;

                switch (shapeType)
                {
                    case ShapeType.矩形区域:
                        img = Resources.layer_shape;
                        break;
                    case ShapeType.圆形区域:
                        img = Resources.layer_shape_ellipse;
                        break;
                    case ShapeType.自由截图:
                        img = Resources.layer_shape_polygon;
                        break;
                    case ShapeType.截图问题反馈:
                        img = Resources.bug;
                        break;

                    case ShapeType.矩形:
                        img = Resources.layer_shape;
                        break;
                    case ShapeType.圆形:
                        img = Resources.layer_shape_ellipse;
                        break;
                    case ShapeType.画笔:
                        img = Resources.pencil;
                        break;
                    case ShapeType.直线:
                        img = Resources.layer_shape_line;
                        break;
                    case ShapeType.箭头:
                        img = Resources.layer_shape_arrow;
                        break;
                    case ShapeType.文字:
                        img = Resources.edit_shade;
                        break;
                    case ShapeType.文字描边:
                        img = Resources.edit_outline;
                        break;
                    case ShapeType.气泡:
                        img = Resources.balloon_box_left;
                        break;
                    case ShapeType.序号:
                        img = Resources.counter_reset;
                        break;
                    case ShapeType.放大镜:
                        img = Resources.搜索;
                        break;
                    case ShapeType.橡皮擦:
                        img = Resources.eraser;
                        break;
                    case ShapeType.马赛克:
                        img = Resources.grid;
                        break;
                    case ShapeType.高亮:
                        img = Resources.highlighter_text;
                        break;
                    //case ShapeType.裁剪:
                    //    img = Resources.image_crop;
                    //    break;
                    case ShapeType.选择并移动:
                        img = Resources.cursor;
                        break;
                }

                tsbShapeType.Image = img;
                tsbShapeType.ImageScaling = ToolStripItemImageScaling.None;
                tsbShapeType.Checked = shapeType == CurrentTool;
                tsbShapeType.Tag = shapeType;

                tsbShapeType.MouseDown += (sender, e) =>
                {
                    if (Equals(shapeType, ShapeType.截图问题反馈))
                    {
                        BugReport();
                        return;
                    }
                    tsbShapeType.RadioCheck();
                    CurrentTool = shapeType;
                };

                tsMain.Items.Add(tsbShapeType);
            }

            #endregion Tools

            if (Form.IsEditorMode)
            {
                #region Shape options

                tsbBorderColor = new ToolStripButton("颜色".CurrentText())
                {
                    DisplayStyle = ToolStripItemDisplayStyle.Image,
                    ImageScaling = ToolStripItemImageScaling.None,
                };
                tsbBorderColor.Click += (sender, e) =>
                {
                    Form.Pause();

                    ShapeType shapeType = CurrentShapeTool;

                    Color borderColor;

                    if (shapeType == ShapeType.文字描边 || shapeType == ShapeType.气泡)
                    {
                        borderColor = AnnotationOptions.TextBorderColor;
                    }
                    else if (shapeType == ShapeType.文字)
                    {
                        borderColor = AnnotationOptions.TextOutlineBorderColor;
                    }
                    else if (shapeType == ShapeType.序号)
                    {
                        borderColor = AnnotationOptions.StepBorderColor;
                    }
                    else
                    {
                        borderColor = AnnotationOptions.BorderColor;
                    }

                    if (PickColor(borderColor, out Color newColor))
                    {
                        if (shapeType == ShapeType.文字描边 || shapeType == ShapeType.气泡)
                        {
                            AnnotationOptions.TextBorderColor = newColor;
                        }
                        else if (shapeType == ShapeType.文字)
                        {
                            AnnotationOptions.TextOutlineBorderColor = newColor;
                        }
                        else if (shapeType == ShapeType.序号)
                        {
                            AnnotationOptions.StepBorderColor = newColor;
                        }
                        else
                        {
                            AnnotationOptions.BorderColor = newColor;
                        }

                        UpdateMenu();
                        UpdateCurrentShape();
                    }

                    Form.Resume();
                };
                tsChild.Items.Add(tsbBorderColor);

                tsbFillColor = new ToolStripButton("填充颜色(鼠标右键清空)".CurrentText())
                {
                    DisplayStyle = ToolStripItemDisplayStyle.Image,
                    ImageScaling = ToolStripItemImageScaling.None,
                };
                tsbFillColor.MouseUp += (sender, e) =>
                {
                    Form.Pause();

                    ShapeType shapeType = CurrentShapeTool;

                    Color newColor;
                    bool isUpdate = true;

                    if (e.Button == MouseButtons.Right)
                    {
                        newColor = Color.Transparent;
                    }
                    else
                    {
                        Color fillColor;
                        if (shapeType == ShapeType.文字描边 || shapeType == ShapeType.气泡)
                        {
                            fillColor = AnnotationOptions.TextFillColor;
                        }
                        else if (shapeType == ShapeType.序号)
                        {
                            fillColor = AnnotationOptions.StepFillColor;
                        }
                        else
                        {
                            fillColor = AnnotationOptions.FillColor;
                        }

                        if (!PickColor(fillColor, out newColor))
                        {
                            isUpdate = false;
                        }
                    }

                    if (isUpdate)
                    {
                        if (shapeType == ShapeType.文字描边 || shapeType == ShapeType.气泡)
                        {
                            AnnotationOptions.TextFillColor = newColor;
                        }
                        else if (shapeType == ShapeType.序号)
                        {
                            AnnotationOptions.StepFillColor = newColor;
                        }
                        else
                        {
                            AnnotationOptions.FillColor = newColor;
                        }

                        UpdateMenu();
                        UpdateCurrentShape();
                    }

                    Form.Resume();
                };
                tsChild.Items.Add(tsbFillColor);

                tsmiShadowColor = new ToolStripMenuItem("阴影颜色(鼠标右键清空)".CurrentText())
                {
                    DisplayStyle = ToolStripItemDisplayStyle.Image,
                    ImageScaling = ToolStripItemImageScaling.None,
                };
                tsmiShadowColor.MouseUp += (sender, e) =>
                {
                    Form.Pause();

                    Color newColor = Color.Transparent;

                    if (e.Button == MouseButtons.Right || (PickColor(AnnotationOptions.ShadowColor, out newColor)))
                    {
                        if (Equals(newColor, Color.Transparent))
                        {
                            AnnotationOptions.Shadow = false;
                        }
                        else
                        {
                            AnnotationOptions.ShadowColor = newColor;
                        }
                        AnnotationOptions.ShadowColor = newColor;
                        UpdateMenu();
                        UpdateCurrentShape();
                    }

                    Form.Resume();
                };
                tsChild.Items.Add(tsmiShadowColor);

                tsbHighlightColor = new ToolStripButton("高亮颜色".CurrentText())
                {
                    Visible = false,
                    DisplayStyle = ToolStripItemDisplayStyle.Image,
                    ImageScaling = ToolStripItemImageScaling.None,
                };
                tsbHighlightColor.Click += (sender, e) =>
                {
                    Form.Pause();
                    if (PickColor(AnnotationOptions.HighlightColor, out Color newColor))
                    {
                        AnnotationOptions.HighlightColor = newColor;
                        UpdateMenu();
                        UpdateCurrentShape();
                    }

                    Form.Resume();
                };
                tsChild.Items.Add(tsbHighlightColor);

                tslnudMagnifyStrength = new ToolStripLabeledNumericUpDown("放大:".CurrentText()) { Visible = false };
                tslnudMagnifyStrength.Content.Text2 = "%";
                tslnudMagnifyStrength.Content.Minimum = 100;
                tslnudMagnifyStrength.Content.Maximum = 1000;
                tslnudMagnifyStrength.Content.Increment = 100;
                tslnudMagnifyStrength.Content.ValueChanged = (sender, e) =>
                {
                    AnnotationOptions.MagnifyStrength = (int)tslnudMagnifyStrength.Content.Value;
                    UpdateCurrentShape();
                };
                tsChild.Items.Add(tslnudMagnifyStrength);

                tslnudBorderSize = new ToolStripLabeledComboBox("边框宽度:".CurrentText(), 4);
                for (int i = 0; i < 20; i++)
                {
                    tslnudBorderSize.Content.Items.Add(i.ToString());
                }
                tslnudBorderSize.Content.SelectedIndexChanged += (sender, e) =>
                {
                    int borderSize = tslnudBorderSize.Content.SelectedIndex + 1;
                    if (borderSize <= 0)
                        return;

                    ShapeType shapeType = CurrentShapeTool;

                    if (shapeType == ShapeType.文字描边 || shapeType == ShapeType.气泡)
                    {
                        AnnotationOptions.TextBorderSize = borderSize;
                    }
                    else if (shapeType == ShapeType.文字)
                    {
                        AnnotationOptions.TextOutlineBorderSize = borderSize;
                    }
                    else if (shapeType == ShapeType.序号)
                    {
                        AnnotationOptions.StepBorderSize = borderSize;
                    }
                    else
                    {
                        AnnotationOptions.BorderSize = borderSize;
                    }

                    tslnudBorderSize.Invalidate();
                    UpdateCurrentShape();
                };
                tsChild.Items.Add(tslnudBorderSize);

                tscbBorderStyle = new ToolStripLabeledComboBox("", 1);
                tscbBorderStyle.Content.Items.AddRange(Enum.GetNames(typeof(BorderStyle)).Select(p => p.CurrentText()).ToArray());
                tscbBorderStyle.Content.SelectedIndexChanged += (sender, e) =>
                {
                    AnnotationOptions.BorderStyle = (BorderStyle)tscbBorderStyle.Content.SelectedIndex;
                    tscbBorderStyle.Invalidate();
                    UpdateCurrentShape();
                };
                tsChild.Items.Add(tscbBorderStyle);

                //tscbImageInterpolationMode = new ToolStripLabeledComboBox("插值模式:") { Visible = false }; ;
                //tscbImageInterpolationMode.Content.AddRange(Enum.GetNames(typeof(ImageInterpolationMode)));
                //tscbImageInterpolationMode.Content.SelectedIndexChanged += (sender, e) =>
                //{
                //    AnnotationOptions.ImageInterpolationMode = (ImageInterpolationMode)tscbImageInterpolationMode.Content.SelectedIndex;
                //    tscbImageInterpolationMode.Invalidate();
                //    UpdateCurrentShape();
                //};
                //tsChild.Items.Add(tscbImageInterpolationMode);

                tslnudPixelateSize = new ToolStripLabeledNumericUpDown("像素:".CurrentText()) { Visible = false };
                tslnudPixelateSize.Content.Minimum = 2;
                tslnudPixelateSize.Content.Maximum = 10000;
                tslnudPixelateSize.Content.ValueChanged = (sender, e) =>
                {
                    AnnotationOptions.PixelateSize = (int)tslnudPixelateSize.Content.Value;
                    UpdateCurrentShape();
                };
                tsChild.Items.Add(tslnudPixelateSize);

                //tslnudCenterPoints = new ToolStripLabeledNumericUpDown("点数:") { Visible = false }; ;
                //tslnudCenterPoints.Content.Minimum = 0;
                //tslnudCenterPoints.Content.Maximum = LineDrawingShape.MaximumCenterPointCount;
                //tslnudCenterPoints.Content.ValueChanged = (sender, e) =>
                //{
                //    AnnotationOptions.LineCenterPointCount = (int)tslnudCenterPoints.Content.Value;
                //    UpdateCurrentShape();
                //};
                //tsChild.Items.Add(tslnudCenterPoints);

                tscbArrowHeadDirection = new ToolStripLabeledComboBox("", 2) { Visible = false };
                tscbArrowHeadDirection.Content.Items.AddRange(Enum.GetNames(typeof(ArrowHeadDirection)).Select(p => p.CurrentText()).ToArray());
                tscbArrowHeadDirection.Content.SelectedIndexChanged += (sender, e) =>
                {
                    AnnotationOptions.ArrowHeadDirection = (ArrowHeadDirection)tscbArrowHeadDirection.Content.SelectedIndex;
                    tscbArrowHeadDirection.Invalidate();
                    UpdateCurrentShape();
                };
                tsChild.Items.Add(tscbArrowHeadDirection);

                tslnudStepFontSize = new ToolStripLabeledNumericUpDown("字号:".CurrentText()) { Visible = false };
                tslnudStepFontSize.Content.Minimum = 10;
                tslnudStepFontSize.Content.Maximum = 100;
                tslnudStepFontSize.Content.ValueChanged = (sender, e) =>
                {
                    AnnotationOptions.StepFontSize = (int)tslnudStepFontSize.Content.Value;
                    UpdateCurrentShape();
                };
                tsChild.Items.Add(tslnudStepFontSize);

                tslnudStartingStepValue = new ToolStripLabeledNumericUpDown("初始值:".CurrentText()) { Visible = false };
                tslnudStartingStepValue.Content.Minimum = 0;
                tslnudStartingStepValue.Content.Maximum = 10000;
                tslnudStartingStepValue.Content.ValueChanged = (sender, e) =>
                {
                    StartingStepNumber = (int)tslnudStartingStepValue.Content.Value;
                    UpdateCurrentShape();
                };
                tsChild.Items.Add(tslnudStartingStepValue);

                tscbStepType = new ToolStripLabeledComboBox("", 3) { Visible = false };
                tscbStepType.Content.Items.AddRange(Enum.GetNames(typeof(StepType)).Select(p => p.CurrentText()).ToArray());
                tscbStepType.Content.SelectedIndexChanged += (sender, e) =>
                {
                    AnnotationOptions.StepType = (StepType)tscbStepType.Content.SelectedIndex;
                    tscbStepType.Invalidate();
                    UpdateCurrentShape();
                };
                tsChild.Items.Add(tscbStepType);

                // In dropdown menu if only last item is visible then menu opens at 0, 0 position on first open, so need to add dummy item to solve this weird bug...
                tsChild.Items.Add(new ToolStripSeparator() { Visible = false });

                #endregion Shape options

                #region Edit

                //ToolStripDropDownButton tsddbEdit = new ToolStripDropDownButton("编辑")
                //{
                //    DisplayStyle = ToolStripItemDisplayStyle.Image,
                //    Image = Resources.wrench_screwdriver
                //};
                //tsMain.Items.Add(tsddbEdit);

                //tsmiUndo = new ToolStripMenuItem("撤销")
                //{
                //    Image = Resources.arrow_circle_225_left,
                //    ShortcutKeyDisplayString = "Ctrl+Z"
                //};
                //tsmiUndo.Click += (sender, e) => UndoShape();
                //tsddbEdit.DropDownItems.Add(tsmiUndo);

                //ToolStripMenuItem tsmiPaste = new ToolStripMenuItem("粘贴图像/文本")
                //{
                //    Image = Resources.clipboard,
                //    ShortcutKeyDisplayString = "Ctrl+V"
                //};
                //tsmiPaste.Click += (sender, e) => PasteFromClipboard(false);
                //tsddbEdit.DropDownItems.Add(tsmiPaste);

                //tsmiDuplicate = new ToolStripMenuItem("重复")
                //{
                //    Image = Resources.document_copy,
                //    ShortcutKeyDisplayString = "Ctrl+D"
                //};
                //tsmiDuplicate.Click += (sender, e) => DuplicateCurrrentShape(false);
                //tsddbEdit.DropDownItems.Add(tsmiDuplicate);

                //tsddbEdit.DropDownItems.Add(new ToolStripSeparator());

                //tsmiDelete = new ToolStripMenuItem("删除")
                //{
                //    Image = Resources.layer__minus,
                //    ShortcutKeyDisplayString = "Del"
                //};
                //tsmiDelete.Click += (sender, e) => DeleteCurrentShape();
                //tsddbEdit.DropDownItems.Add(tsmiDelete);

                //tsmiDeleteAll = new ToolStripMenuItem("删除所有")
                //{
                //    Image = Resources.eraser,
                //    ShortcutKeyDisplayString = "Shift+Del"
                //};
                //tsmiDeleteAll.Click += (sender, e) => DeleteAllShapes();
                //tsddbEdit.DropDownItems.Add(tsmiDeleteAll);

                //tsddbEdit.DropDownItems.Add(new ToolStripSeparator());

                //tsmiMoveTop = new ToolStripMenuItem("置顶")
                //{
                //    Image = Resources.layers_stack_arrange,
                //    ShortcutKeyDisplayString = "Home"
                //};
                //tsmiMoveTop.Click += (sender, e) => MoveCurrentShapeTop();
                //tsddbEdit.DropDownItems.Add(tsmiMoveTop);

                //tsmiMoveUp = new ToolStripMenuItem("置底")
                //{
                //    Image = Resources.layers_arrange,
                //    ShortcutKeyDisplayString = "Page up"
                //};
                //tsmiMoveUp.Click += (sender, e) => MoveCurrentShapeUp();
                //tsddbEdit.DropDownItems.Add(tsmiMoveUp);

                //tsmiMoveDown = new ToolStripMenuItem("向上")
                //{
                //    Image = Resources.layers_arrange_back,
                //    ShortcutKeyDisplayString = "Page down"
                //};
                //tsmiMoveDown.Click += (sender, e) => MoveCurrentShapeDown();
                //tsddbEdit.DropDownItems.Add(tsmiMoveDown);

                //tsmiMoveBottom = new ToolStripMenuItem("向下")
                //{
                //    Image = Resources.layers_stack_arrange_back,
                //    ShortcutKeyDisplayString = "End"
                //};
                //tsmiMoveBottom.Click += (sender, e) => MoveCurrentShapeBottom();
                //tsddbEdit.DropDownItems.Add(tsmiMoveBottom);

                #endregion Edit

                tsMain.Items.Add(new ToolStripSeparator());

                #region Editor mode

                ToolStripButton tsbSaveImageAs = new ToolStripButton("图片另存为".CurrentText())
                {
                    DisplayStyle = ToolStripItemDisplayStyle.Image,
                    Image = Resources.disks_black,
                    ImageScaling = ToolStripItemImageScaling.None,
                };
                tsbSaveImageAs.Click += (sender, e) => Form.OnSaveImageAsRequested();
                tsMain.Items.Add(tsbSaveImageAs);

                ToolStripButton tsbUndoShape = new ToolStripButton("撤销操作".CurrentText())
                {
                    DisplayStyle = ToolStripItemDisplayStyle.Image,
                    Image = Resources.Undo,
                    ImageScaling = ToolStripItemImageScaling.None,
                };
                tsbUndoShape.Click += (sender, e) => UndoShape();
                tsMain.Items.Add(tsbUndoShape);

                if (Form.Options?.CustomInfoTitle?.Contains("贴图") == false)
                {
                    ToolStripButton tsbDing = new ToolStripButton("钉在屏幕上".CurrentText())
                    {
                        DisplayStyle = ToolStripItemDisplayStyle.Image,
                        Image = Resources.图钉,
                        ImageScaling = ToolStripItemImageScaling.None,
                    };
                    tsbDing.Click += (sender, e) =>
                    {
                        FrmMain.FrmTool.ViewImageWithLocation(Form.GetResultImage(), Form.ShapeManager.LastAllScreenRectangle.Location);
                        Form.CloseWindow();
                    };
                    tsMain.Items.Add(tsbDing);
                }

                ToolStripButton tsbCompleteEdit = new ToolStripButton("完成".CurrentText())
                {
                    DisplayStyle = ToolStripItemDisplayStyle.Image,
                    Image = Resources.finish,
                    ImageScaling = ToolStripItemImageScaling.None,
                };

                tsbCompleteEdit.Click += (sender, e) => Form.CloseWindow(RegionResult.Region);
                tsMain.Items.Add(tsbCompleteEdit);

                #endregion Editor mode
            }

            tslDragRight = new ToolStripLabel("鼠标左键拖动，右键隐藏".CurrentText())
            {
                Alignment = ToolStripItemAlignment.Right,
                DisplayStyle = ToolStripItemDisplayStyle.Image,
                ImageScaling = ToolStripItemImageScaling.None,
                Image = ImageHelp.DrawGrip(Color.Black, Color.AliceBlue),
                Margin = new Padding(0, 0, 2, 0),
                Padding = new Padding(2),
                Visible = true
            };

            tsMain.Items.Add(tslDragRight);

            tslDragLeft.MouseDown += TslDrag_MouseDown;
            tslDragRight.MouseDown += TslDrag_MouseDown;
            tslDragLeft.MouseEnter += TslDrag_MouseEnter;
            tslDragRight.MouseEnter += TslDrag_MouseEnter;
            tslDragLeft.MouseLeave += TslDrag_MouseLeave;
            tslDragRight.MouseLeave += TslDrag_MouseLeave;

            foreach (var tsi in tsMain.Items.OfType<ToolStripItem>())
            {
                if (!string.IsNullOrEmpty(tsi.Text))
                {
                    tsi.MouseEnter += (sender, e) =>
                    {
                        var pos = Form.PointToClient(menuForm.PointToScreen(tsi.Bounds.Location));
                        pos.Y += tsMain.Bottom + 5;

                        MenuTextAnimation.Text = tsi.Text;
                        MenuTextAnimation.Position = pos;
                        MenuTextAnimation.Start();
                    };

                    tsi.MouseLeave += TsMain_MouseLeave;
                }

                tsi.Padding = new Padding(4);
            }

            foreach (var tsi in tsChild?.Items.OfType<ToolStripItem>())
            {
                if (!string.IsNullOrEmpty(tsi.Text))
                {
                    tsi.MouseEnter += (sender, e) =>
                    {
                        var pos = Form.PointToClient(menuForm.PointToScreen(tsi.Bounds.Location));
                        pos.Y += tsChild.Bounds.Bottom + 10;

                        MenuTextAnimation.Text = tsi.Text;
                        MenuTextAnimation.Position = pos;
                        MenuTextAnimation.Start();
                    };

                    tsi.MouseLeave += TsMain_MouseLeave;
                }

                tsi.Padding = new Padding(4);
            }

            tsMain.ResumeLayout(false);
            tsMain.PerformLayout();

            tsChild?.ResumeLayout(false);
            tsChild?.PerformLayout();

            menuForm.ResumeLayout(false);

            menuForm.Show(Form);

            //UpdateMenu();

            CurrentShapeChanged -= MenuChanged;
            CurrentShapeTypeChanged -= MenuTypeChanged;
            ShapeCreated -= MenuChanged;

            CurrentShapeChanged += MenuChanged;
            CurrentShapeTypeChanged += MenuTypeChanged;
            ShapeCreated += MenuChanged;

            ConfigureMenuState();

            Form.Activate();

            ToolbarCreated = true;
        }

        private void BugReport()
        {
            var report = new FrmReport
            {
                Icon = FrmMain.FrmTool.Icon,
                Text = ShapeType.截图问题反馈.ToString().CurrentText()
            };
            report.SetContent(Screenshot.GetWorkAreaInfo());
            report.AddImage(null, Form.Canvas);
            report.TopMost = true;
            report.StartPosition = FormStartPosition.CenterScreen;
            report.Show();
        }

        private void MenuTypeChanged(ShapeType shape)
        {
            UpdateMenu();
        }

        private void MenuChanged(BaseShape shape)
        {
            UpdateMenu();
        }

        private void MenuForm_Shown(object sender, EventArgs e)
        {
            //Form.ToolbarHeight = menuForm.Height;
            Form.CenterCanvas();
            if (Form.IsEditorMode)
            {
                UpdateMenu();
            }
        }

        private void MenuForm_KeyDown(object sender, KeyEventArgs e)
        {
            form_KeyDown(sender, e);
            Form.RegionCaptureForm_KeyDown(sender, e);

            e.Handled = true;
        }

        private void MenuForm_KeyUp(object sender, KeyEventArgs e)
        {
            form_KeyUp(sender, e);

            e.Handled = true;
        }

        private void MenuForm_LocationChanged(object sender, EventArgs e)
        {
            CheckMenuPosition();
        }

        private void MenuForm_GotFocus(object sender, EventArgs e)
        {
            Form.Resume();
        }

        private void MenuForm_LostFocus(object sender, EventArgs e)
        {
            Form.Pause();
        }

        private void TsMain_MouseLeave(object sender, EventArgs e)
        {
            MenuTextAnimation.Stop();
        }

        private void TslDrag_MouseEnter(object sender, EventArgs e)
        {
            menuForm.Cursor = Cursors.SizeAll;
        }

        private void TslDrag_MouseLeave(object sender, EventArgs e)
        {
            menuForm.Cursor = Cursors.Default;
        }

        private void TslDrag_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                WinApi.ReleaseCapture();
                NativeMethods.DefWindowProc(menuForm.Handle, 0x0112, (UIntPtr)0xF012, IntPtr.Zero);
            }
            else if (e.Button == MouseButtons.Right)
            {
                SetMenuCollapsed(!ToolbarCollapsed);
                CheckMenuPosition();
            }
        }

        private void ConfigureMenuState()
        {
            UpdateMenuPosition();
        }

        internal void UpdateMenuPosition()
        {
            Rectangle rectScreen = NativeMethods.GetActiveScreenBounds();

            if (tsMain.Width < rectScreen.Width)
            {
                menuForm.Location = new Point(rectScreen.X + (rectScreen.Width / 2) - (tsMain.Width / 2), rectScreen.Y);
            }
            else
            {
                menuForm.Location = rectScreen.Location;
            }
        }

        internal void UpdateMenuMaxWidth(int width)
        {
            tsMain.MaximumSize = new Size(width, 0);
        }

        private void CheckMenuPosition()
        {
            Rectangle rectMenu = menuForm.Bounds;
            Rectangle rectScreen = NativeMethods.GetScreenBounds();
            Point pos = rectMenu.Location;

            if (rectMenu.Width < rectScreen.Width)
            {
                if (rectMenu.X < rectScreen.X)
                {
                    pos.X = rectScreen.X;
                }
                else if (rectMenu.Right > rectScreen.Right)
                {
                    pos.X = rectScreen.Right - rectMenu.Width;
                }
            }

            if (rectMenu.Height < rectScreen.Height)
            {
                if (rectMenu.Y < rectScreen.Y)
                {
                    pos.Y = rectScreen.Y;
                }
                else if (rectMenu.Bottom > rectScreen.Bottom)
                {
                    pos.Y = rectScreen.Bottom - rectMenu.Height;
                }
            }

            if (pos != rectMenu.Location)
            {
                menuForm.Location = pos;
            }
        }

        private void SetMenuCollapsed(bool isCollapsed)
        {
            if (ToolbarCollapsed == isCollapsed)
            {
                return;
            }

            ToolbarCollapsed = isCollapsed;

            if (ToolbarCollapsed)
            {
                foreach (ToolStripItem tsi in tsMain.Items.OfType<ToolStripItem>())
                {
                    if (tsi == tslDragLeft)
                    {
                        continue;
                    }

                    tsi.Visible = false;
                }
                tsChild.Visible = false;
            }
            else
            {
                foreach (ToolStripItem tsi in tsMain.Items.OfType<ToolStripItem>())
                {
                    tsi.Visible = true;
                }

                UpdateMenu();
            }
        }

        private Color lastShadowColor;
        private Color lastBorderColor;
        private Color lastFillColor;
        private Color lastHighlightColor;
        private string lastSharp;

        private void UpdateMenu()
        {
            if (Form.IsClosing || menuForm == null || menuForm.IsDisposed || !Form.IsEditorMode) return;

            ShapeType shapeType = CurrentTool;
            if (Equals(shapeType.ToString(), lastSharp))
            {
                return;
            }
            lastSharp = shapeType.ToString();

            foreach (ToolStripButton tsb in tsMain.Items.OfType<ToolStripButton>().Where(x => x.Tag is ShapeType))
            {
                if ((ShapeType)tsb.Tag == shapeType)
                {
                    tsb.RadioCheck();
                    break;
                }
            }

            // use menu of current shape in case of an active select tool.
            shapeType = CurrentShapeTool;
            tsChild.Visible = false;

            var isChildVisible = shapeType != ShapeType.橡皮擦 && shapeType != ShapeType.选择并移动 && !IsShapeTypeRegion(shapeType);//shapeType != ShapeType.裁剪 &&

            if (!isChildVisible)
            {
                return;
            }

            tsbFillColor.Visible = false;
            switch (shapeType)
            {
                case ShapeType.矩形:
                case ShapeType.圆形:
                case ShapeType.文字描边:
                case ShapeType.气泡:
                case ShapeType.序号:
                case ShapeType.放大镜:
                    tsbFillColor.Visible = true;
                    {
                        Color fillColor;

                        if (shapeType == ShapeType.文字描边 || shapeType == ShapeType.气泡)
                        {
                            fillColor = AnnotationOptions.TextFillColor;
                        }
                        else if (shapeType == ShapeType.序号)
                        {
                            fillColor = AnnotationOptions.StepFillColor;
                        }
                        else
                        {
                            fillColor = AnnotationOptions.FillColor;
                        }

                        if (!Equals(lastFillColor, fillColor))
                        {
                            var dpiScale = Form.GetDpiScale();
                            tsbFillColor.Image = ImageHelp.CreateColorPickerIcon(fillColor,
                                new Rectangle(0, 0, (int)(16 * dpiScale), (int)(16 * dpiScale)));
                            lastFillColor = fillColor;
                        }
                    }
                    break;
            }

            //tscbImageInterpolationMode.Content.SelectedIndex = (int)AnnotationOptions.ImageInterpolationMode;

            tsbHighlightColor.Visible = false;
            tslnudPixelateSize.Visible = false;
            tslnudStepFontSize.Visible = false;
            tscbStepType.Visible = false;
            tslnudStartingStepValue.Visible = false;
            tslnudMagnifyStrength.Visible = false;
            tscbArrowHeadDirection.Visible = false;
            switch (shapeType)
            {
                case ShapeType.马赛克:
                    tslnudPixelateSize.Visible = true;
                    tslnudPixelateSize.Content.Value = AnnotationOptions.PixelateSize;
                    break;
                case ShapeType.高亮:
                    tsbHighlightColor.Visible = true;
                    if (!Equals(lastHighlightColor, AnnotationOptions.HighlightColor))
                    {

                        var dpiScale = Form.GetDpiScale();
                        tsbHighlightColor.Image = ImageHelp.CreateColorPickerIcon(AnnotationOptions.HighlightColor,
                            new Rectangle(0, 0, (int)(16 * dpiScale), (int)(16 * dpiScale)));
                        lastHighlightColor = AnnotationOptions.HighlightColor;
                    }
                    break;
                case ShapeType.序号:
                    tslnudStepFontSize.Visible = true;
                    tscbStepType.Visible = true;
                    tslnudStartingStepValue.Visible = true;
                    tslnudStepFontSize.Content.Value = AnnotationOptions.StepFontSize;
                    tscbStepType.Content.SelectedIndex = (int)AnnotationOptions.StepType;
                    tslnudStartingStepValue.Content.Value = StartingStepNumber;
                    break;
                case ShapeType.放大镜:
                    tslnudMagnifyStrength.Visible = true;
                    tslnudMagnifyStrength.Content.Value = AnnotationOptions.MagnifyStrength;
                    break;
                case ShapeType.箭头:
                    tscbArrowHeadDirection.Visible = true;
                    tscbArrowHeadDirection.Content.SelectedIndex = (int)AnnotationOptions.ArrowHeadDirection;
                    break;
            }

            //tslnudCenterPoints.Content.Value = AnnotationOptions.LineCenterPointCount;

            //tsmiUndo.Enabled = tsmiDeleteAll.Enabled = Shapes.Count > 0;
            //tsmiDuplicate.Enabled = tsmiDelete.Enabled = tsmiMoveTop.Enabled = tsmiMoveUp.Enabled = tsmiMoveDown.Enabled = tsmiMoveBottom.Enabled = CurrentShape != null;

            switch (shapeType)
            {
                default:
                    tsbBorderColor.Visible = false;
                    tslnudBorderSize.Visible = false;
                    //tsmiShadow.Visible = false;
                    tsmiShadowColor.Visible = false;
                    break;
                case ShapeType.矩形:
                case ShapeType.圆形:
                case ShapeType.画笔:
                case ShapeType.直线:
                case ShapeType.箭头:
                case ShapeType.文字:
                case ShapeType.文字描边:
                case ShapeType.气泡:
                case ShapeType.序号:
                case ShapeType.放大镜:
                    tsbBorderColor.Visible = true;
                    tslnudBorderSize.Visible = true;
                    //tsmiShadow.Visible = true;
                    tsmiShadowColor.Visible = true;
                    if (!Equals(lastShadowColor, AnnotationOptions.ShadowColor))
                    {
                        var dpiScale = Form.GetDpiScale();
                        tsmiShadowColor.Image = ImageHelp.CreateColorPickerIcon(AnnotationOptions.ShadowColor,
                            new Rectangle(0, 0, (int)(16 * dpiScale), (int)(16 * dpiScale)));
                        lastShadowColor = AnnotationOptions.ShadowColor;
                    }
                    {
                        int borderSize;

                        if (shapeType == ShapeType.文字描边 || shapeType == ShapeType.气泡)
                        {
                            borderSize = AnnotationOptions.TextBorderSize;
                        }
                        else if (shapeType == ShapeType.文字)
                        {
                            borderSize = AnnotationOptions.TextOutlineBorderSize;
                        }
                        else if (shapeType == ShapeType.序号)
                        {
                            borderSize = AnnotationOptions.StepBorderSize;
                        }
                        else
                        {
                            borderSize = AnnotationOptions.BorderSize;
                        }
                        borderSize = Math.Max(borderSize, RegionCaptureOptions.BorderSizeMinimum);
                        borderSize = Math.Min(borderSize, RegionCaptureOptions.BorderSizeMaximum);

                        if (tslnudBorderSize.Content.SelectedIndex != borderSize)
                            tslnudBorderSize.Content.SelectedIndex = borderSize - 1;
                    }
                    {
                        Color borderColor;

                        if (shapeType == ShapeType.文字描边 || shapeType == ShapeType.气泡)
                        {
                            borderColor = AnnotationOptions.TextBorderColor;
                        }
                        else if (shapeType == ShapeType.文字)
                        {
                            borderColor = AnnotationOptions.TextOutlineBorderColor;
                        }
                        else if (shapeType == ShapeType.序号)
                        {
                            borderColor = AnnotationOptions.StepBorderColor;
                        }
                        else
                        {
                            borderColor = AnnotationOptions.BorderColor;
                        }

                        if (!Equals(lastBorderColor, borderColor))
                        {
                            var dpiScale = Form.GetDpiScale();
                            tsbBorderColor.Image = ImageHelp.CreateColorPickerIcon(borderColor, new Rectangle(0, 0, (int)(16 * dpiScale), (int)(16 * dpiScale)), (int)(8 * dpiScale));
                            lastBorderColor = borderColor;
                        }
                    }

                    break;
            }

            switch (shapeType)
            {
                default:
                    tscbBorderStyle.Visible = false;
                    break;
                case ShapeType.矩形:
                case ShapeType.圆形:
                case ShapeType.画笔:
                case ShapeType.直线:
                case ShapeType.箭头:
                case ShapeType.放大镜:
                    tscbBorderStyle.Visible = true;
                    tscbBorderStyle.Content.SelectedIndex = (int)AnnotationOptions.BorderStyle;
                    break;
            }

            tsChild.Top = tsMain.Bottom + 5;
            tsChild.Visible = true;
            //tslnudCenterPoints.Visible = shapeType == ShapeType.箭头;
            //tscbImageInterpolationMode.Visible = shapeType == ShapeType.放大镜;
        }

        public void ShowMenuTooltip(string text)
        {
            MenuTextAnimation.Text = text;
            MenuTextAnimation.Start();
        }
    }
    public enum ImageInterpolationMode
    {
        HighQualityBicubic,
        Bicubic,
        HighQualityBilinear,
        Bilinear,
        NearestNeighbor
    }
    public enum ArrowHeadDirection // Localized
    {
        终点箭头,
        起点箭头,
        双向箭头
    }
    public enum StepType // Localized
    {
        数字,
        大写字母,
        小写字母,
        大写罗马数字,
        小写罗马数字
    }
}