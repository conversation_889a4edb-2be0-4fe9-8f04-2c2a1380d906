using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolEllipse : ToolRectangle
    {
        private DrawObject drawEllipse;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.current_ToolType = drawArea.ActiveTool;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else
            {
                drawEllipse = new DrawEllipse(e.X, e.Y, 1, 1);
                AddNewObject(drawArea, drawEllipse);
            }
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left)
            {
                if (drawEllipse == null)
                {
                    drawArea.ActiveTool = DrawToolType.Text;
                    return;
                }

                drawEllipse.IsSelected = true;
                var drawObject = drawEllipse;
                using (new AutomaticCanvasRefresher(drawArea, drawObject.GetBoundingBox))
                {
                    drawEllipse.MoveHandleTo(e.Location, 5, drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawEllipse != null)
            {
                StaticValue.current_Rectangle = drawEllipse.Rectangle;
                if (!drawEllipse.Rectangle.IsLimt())
                {
                    drawArea.GraphicsList.RemoveAt(0);
                    return;
                }

                var drawObject = drawEllipse;
                using (new AutomaticCanvasRefresher(drawArea, drawObject.GetBoundingBox))
                {
                    drawEllipse.Normalize();
                    drawArea.AddCommandToHistory(new CommandAdd(drawEllipse));
                }
            }
        }
    }
}