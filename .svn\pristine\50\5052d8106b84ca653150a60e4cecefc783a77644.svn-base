﻿using OCRTools.Common;
using System;
using System.Collections.Specialized;
using System.Reflection;

namespace OCRTools.ImageLib
{
    /// <summary>
    /// https://tinypng.com/
    /// </summary>
    public class TinyPngUpload
    {
        private static readonly Random RndNext = new Random();

        public static string GetResultUrl(byte[] content)
        {
            var result = string.Empty;
            var html = WebClientExt.GetHtml("https://tinypng.com/backend/opt/shrink", Convert.ToBase64String(content), 30, new NameValueCollection
                {
                    {"X-Forwarded-For",string.Format("{0}.{1}.{2}.{3}",RndNext.Next(1,255),RndNext.Next(1,255),RndNext.Next(1,255),RndNext.Next(1,255))}
                });
            if (!string.IsNullOrEmpty(html) && html.Length > 2)
            {
                ImageCompressResult compressResult = html.DeserializeJson<ImageCompressResult>();
                if (compressResult.output != null && !string.IsNullOrEmpty(compressResult.output.url))
                {
                    result = compressResult.output.url.TrimEnd('/') + "/1.jpg";
                }
            }
            return result;
        }

        public static byte[] GetZipResult(byte[] content, ref string strUrl)
        {
            strUrl = GetResultUrl(content);
            return CommonMethod.GetUrlBytes(strUrl);
        }
    }

    [Obfuscation]
    public class ImageCompressResult
    {
        [Obfuscation]
        public string FileName { get; set; }

        [Obfuscation]
        public ImageCompressInfo input { get; set; }

        [Obfuscation]
        public ImageCompressInfo output { get; set; }
    }

    [Obfuscation]
    public class ImageCompressInfo
    {
        [Obfuscation]
        public string url { get; set; }

        [Obfuscation]
        public string size { get; set; }
    }
}
