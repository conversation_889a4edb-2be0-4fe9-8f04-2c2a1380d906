﻿/*******************************************************************************
 * You may amend and distribute as you like, but don't remove this header!
 *
 * EPPlus provides server-side generation of Excel 2007/2010 spreadsheets.
 * See https://github.com/JanKallman/EPPlus for details.
 *
 * Copyright (C) 2011  <PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  
 * See the GNU Lesser General Public License for more details.
 *
 * The GNU Lesser General Public License can be viewed at http://www.opensource.org/licenses/lgpl-license.php
 * If you unfamiliar with this license or have questions about it, here is an http://www.gnu.org/licenses/gpl-faq.html
 *
 * All code and executables are provided "as is" with no warranty either express or implied. 
 * The author accepts no liability for any damage or loss of business that this product may cause.
 *
 * Code change notes:
 * 
 * Author							Change						Date
 * ******************************************************************************
 * Mats Alm 		    Initial Release		        2011-05-01
 * Jan Källman		    License changed GPL-->LGPL  2011-12-27
 *******************************************************************************/
using System.Xml;

namespace OfficeOpenXml
{
    internal class XmlHelperInstance : XmlHelper
    {
        internal XmlHelperInstance(XmlNamespaceManager namespaceManager)
            : base(namespaceManager)
        { }

        internal XmlHelperInstance(XmlNamespaceManager namespaceManager, XmlNode topNode)
            : base(namespaceManager, topNode)
        { }

    }

    internal static class XmlHelperFactory
    {
        internal static XmlHelper Create(XmlNamespaceManager namespaceManager)
        {
            return new XmlHelperInstance(namespaceManager);
        }

        internal static XmlHelper Create(XmlNamespaceManager namespaceManager, XmlNode topNode)
        {
            return new XmlHelperInstance(namespaceManager, topNode);
        }
    }
}
