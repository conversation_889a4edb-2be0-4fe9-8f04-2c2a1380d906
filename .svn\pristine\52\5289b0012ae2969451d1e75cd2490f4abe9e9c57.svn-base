// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class SelectionPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = SelectionPatternIdentifiers.Pattern;

        private SelectionPattern(AutomationElement el, IUIAutomationSelectionPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new SelectionPattern(el, (IUIAutomationSelectionPattern)pattern, cached);
        }
    }

    public class SelectionItemPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = SelectionItemPatternIdentifiers.Pattern;

        private SelectionItemPattern(AutomationElement el, IUIAutomationSelectionItemPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null
                ? null
                : new SelectionItemPattern(el, (IUIAutomationSelectionItemPattern)pattern, cached);
        }
    }
}