﻿using System;
using System.Drawing;
using System.Globalization;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;

namespace OCRTools.Common
{
    public static class NativeMethods
    {
        #region IDocHostUIHandler

        /// <summary>Determines whether a window is maximized.</summary>
        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool IsZoomed(IntPtr hWnd);

        [DllImport("dwmapi.dll")]
        public static extern int DwmGetWindowAttribute(IntPtr hwnd, int dwAttribute, out RECT pvAttribute,
            int cbAttribute);

        public static bool GetExtendedFrameBounds(IntPtr handle, out Rectangle rectangle)
        {
            var result = DwmGetWindowAttribute(handle, 9, out RECT rect, Marshal.SizeOf(typeof(RECT)));
            rectangle = rect;
            return result == 0;
        }

        [DllImport("user32.dll", SetLastError = true)]
        internal static extern IntPtr SetWindowsHookEx(int idHook, CbtProc lpfn, IntPtr hMod, int dwThreadId);

        [DllImport("kernel32.dll")]
        internal static extern int GetCurrentThreadId();

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool GetWindowInfo(IntPtr hwnd, ref WINDOWINFO pwi);

        /// <summary>Determines whether the specified window is minimized (iconic).</summary>
        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool IsIconic(IntPtr hWnd);

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        internal static extern bool MoveWindow(IntPtr hWnd, int x, int y, int nWidth, int nHeight,
            [MarshalAs(UnmanagedType.Bool)] bool bRepaint);

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        internal static extern bool UnhookWindowsHookEx(IntPtr hhk);

        [DllImport("user32.dll")]
        public static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

        [DllImport("user32.dll")]
        public static extern IntPtr FindWindowEx(IntPtr parentHwnd, IntPtr childAfterHwnd, IntPtr className,
            string windowText);

        public static bool SetTaskbarVisibilityIfIntersect(bool visible, Rectangle rect)
        {
            var result = false;

            var taskbarHandle = FindWindow("Shell_TrayWnd", null);

            if (taskbarHandle != IntPtr.Zero)
            {
                var taskbarRect = GetWindowRect(taskbarHandle);

                if (rect.IntersectsWith(taskbarRect))
                {
                    ShowWindow(taskbarHandle, visible ? (int)WindowShowStyle.Show : (int)WindowShowStyle.Hide);
                    result = true;
                }

                if (IsWindowsVista() || IsWindows7())
                {
                    var startHandle = FindWindowEx(IntPtr.Zero, IntPtr.Zero, (IntPtr)0xC017, null);

                    if (startHandle != IntPtr.Zero)
                    {
                        var startRect = GetWindowRect(startHandle);

                        if (rect.IntersectsWith(startRect))
                        {
                            ShowWindow(startHandle, visible ? (int)WindowShowStyle.Show : (int)WindowShowStyle.Hide);
                            result = true;
                        }
                    }
                }
            }

            return result;
        }

        [DllImport("user32.dll")]
        public static extern IntPtr GetForegroundWindow();

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool GetCursorPos(out POINT lpPoint);

        public static Rectangle GetActiveScreenBounds()
        {
            return Screen.FromPoint(GetCursorPosition()).Bounds;
        }

        public static Point GetCursorPosition()
        {
            if (GetCursorPos(out var point)) return (Point)point;

            return Point.Empty;
        }

        [DllImport("user32.dll")]
        public static extern IntPtr GetDesktopWindow();

        public static bool SetTaskbarVisibility(bool visible)
        {
            var taskbarHandle = FindWindow("Shell_TrayWnd", null);

            if (taskbarHandle != IntPtr.Zero)
            {
                ShowWindow(taskbarHandle, visible ? (int)WindowShowStyle.Show : (int)WindowShowStyle.Hide);

                if (IsWindowsVista() || IsWindows7())
                {
                    var startHandle = FindWindowEx(IntPtr.Zero, IntPtr.Zero, (IntPtr)0xC017, null);

                    if (startHandle != IntPtr.Zero)
                        ShowWindow(startHandle, visible ? (int)WindowShowStyle.Show : (int)WindowShowStyle.Hide);
                }

                return true;
            }

            return false;
        }

        public static Rectangle GetWindowRect(IntPtr handle)
        {
            GetWindowRect(handle, out var rect);
            return rect;
        }

        public static bool GetBorderSize(IntPtr handle, out Size size)
        {
            var wi = new WINDOWINFO();

            var result = GetWindowInfo(handle, ref wi);

            size = result ? new Size((int)wi.cxWindowBorders, (int)wi.cyWindowBorders) : Size.Empty;

            return result;
        }

        public static Rectangle MaximizedWindowFix(IntPtr handle, Rectangle windowRect)
        {
            if (GetBorderSize(handle, out var size))
                windowRect = new Rectangle(windowRect.X + size.Width, windowRect.Y + size.Height,
                    windowRect.Width - size.Width * 2, windowRect.Height - size.Height * 2);

            return windowRect;
        }

        [StructLayout(LayoutKind.Sequential)]
        internal struct WinPoint
        {
            public int X;
            public int Y;

            public static explicit operator Point(WinPoint point)
            {
                return new Point(point.X, point.Y);
            }

            public static explicit operator WinPoint(Point point)
            {
                var pt = new WinPoint
                {
                    X = point.X,
                    Y = point.Y
                };
                return pt;
            }
        }

        [DllImport("user32.dll")]
        internal static extern IntPtr WindowFromPoint(WinPoint point);

        /// <summary>
        ///     Retrieves the bounding rectangle of the window at the specified point.
        /// </summary>
        public static Rectangle GetWindowRectangle(Point point)
        {
            var hWnd = WindowFromPoint((WinPoint)point);
            if (!GetExtendedFrameBounds(hWnd, out var rect))
            {
                GetWindowRect(hWnd, out var rect1);
                rect = rect1;
            }

            return rect;
        }

        public static Rectangle GetWindowRectangle(IntPtr handle)
        {
            var rect = Rectangle.Empty;

            if (IsDWMEnabled() && GetExtendedFrameBounds(handle, out var rectangle2)) rect = rectangle2;

            if (rect.IsEmpty) rect = GetWindowRect(handle);

            if (!IsWindows10OrGreater() && IsZoomed(handle)) rect = MaximizedWindowFix(handle, rect);

            return rect;
        }

        [DllImport("user32.dll", CharSet = CharSet.Auto)]
        public static extern int GetClassName(IntPtr hWnd, StringBuilder lpClassName, int nMaxCount);

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool EnumChildWindows(IntPtr hwndParent, EnumWindowsProc lpEnumFunc, IntPtr lParam);

        public delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool EnumWindows(EnumWindowsProc lpEnumFunc, IntPtr lParam);

        /// <summary>Determines the visibility state of the specified window.</summary>
        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool IsWindowVisible(IntPtr hWnd);

        public static Icon GetApplicationIcon(IntPtr handle)
        {
            return GetSmallApplicationIcon(handle) ?? GetBigApplicationIcon(handle);
        }

        [DllImport("user32.dll")]
        public static extern bool GetClientRect(IntPtr hWnd, out RECT lpRect);

        [DllImport("user32.dll")]
        public static extern bool ClientToScreen(IntPtr hWnd, ref Point lpPoint);

        public static Rectangle GetClientRect(IntPtr handle)
        {
            GetClientRect(handle, out var lpRect);
            var lpPoint = lpRect.Location;
            ClientToScreen(handle, ref lpPoint);
            return new Rectangle(lpPoint, lpRect.Size);
        }


        private static Icon GetBigApplicationIcon(IntPtr handle)
        {
            SendMessageTimeout(handle, 0x007F, 1, 0, 0x2, 1000, out var iconHandle);

            if (iconHandle == IntPtr.Zero) iconHandle = GetClassLongPtrSafe(handle, -14);

            if (iconHandle != IntPtr.Zero) return Icon.FromHandle(iconHandle);

            return null;
        }

        [DllImport("user32.dll", CharSet = CharSet.Auto)]
        public static extern IntPtr SendMessageTimeout(IntPtr hWnd, int msg, int wParam, int lParam, uint fuFlags,
            uint uTimeout, out IntPtr lpdwResult);

        [DllImport("user32.dll")]
        public static extern uint GetClassLong(IntPtr hWnd, int nIndex);

        [DllImport("user32.dll")]
        public static extern IntPtr GetClassLongPtr(IntPtr hWnd, int nIndex);

        public static IntPtr GetClassLongPtrSafe(IntPtr hWnd, int nIndex)
        {
            if (IntPtr.Size > 4) return GetClassLongPtr(hWnd, nIndex);

            return new IntPtr(GetClassLong(hWnd, nIndex));
        }

        private static Icon GetSmallApplicationIcon(IntPtr handle)
        {
            SendMessageTimeout(handle, 0x007F, 2, 0, 0x2, 1000, out var iconHandle);

            if (iconHandle == IntPtr.Zero)
            {
                SendMessageTimeout(handle, 0x007F, 0, 0, 0x2, 1000, out iconHandle);

                if (iconHandle == IntPtr.Zero)
                {
                    iconHandle = GetClassLongPtrSafe(handle, -34);

                    if (iconHandle == IntPtr.Zero) SendMessageTimeout(handle, 0x0037, 0, 0, 0x2, 1000, out iconHandle);
                }
            }

            if (iconHandle != IntPtr.Zero) return Icon.FromHandle(iconHandle);

            return null;
        }

        [DllImport("user32.dll", CharSet = CharSet.Auto)]
        public static extern int GetWindowTextLength(IntPtr hWnd);

        [DllImport("user32.dll", CharSet = CharSet.Auto)]
        public static extern int GetWindowText(IntPtr hWnd, [Out] StringBuilder lpString, int nMaxCount);

        public static string GetWindowText(IntPtr handle)
        {
            if (handle.ToInt32() > 0)
                try
                {
                    var length = GetWindowTextLength(handle);

                    if (length > 0)
                    {
                        var sb = new StringBuilder(length + 1);

                        if (GetWindowText(handle, sb, sb.Capacity) > 0) return sb.ToString();
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }

            return null;
        }

        [DllImport("dwmapi.dll", PreserveSig = false)]
        public static extern bool DwmIsCompositionEnabled();

        [DllImport("dwmapi.dll")]
        public static extern int DwmGetWindowAttribute(IntPtr hwnd, int dwAttribute, out int pvAttribute,
            int cbAttribute);

        //[DllImport("user32.dll")]
        //internal static extern bool SetProcessDPIAware();

        //internal enum ProcessDPIAwareness
        //{
        //    ProcessDPIUnaware = 0,
        //    ProcessSystemDPIAware = 1,
        //    ProcessPerMonitorDPIAware = 2
        //}

        //[DllImport("shcore.dll")]
        //internal static extern int SetProcessDpiAwareness(ProcessDPIAwareness value);

        public static bool IsDWMEnabled()
        {
            return IsWindowsVistaOrGreater() && DwmIsCompositionEnabled();
        }

        public static readonly Version OsVersion = Environment.OSVersion.Version;

        public static bool IsWindowsXp()
        {
            return OsVersion.Major == 5 && OsVersion.Minor == 1;
        }

        public static bool IsWindowsXpOrGreater()
        {
            return OsVersion.Major == 5 && OsVersion.Minor >= 1 || OsVersion.Major > 5;
        }

        public static bool IsWindowsVista()
        {
            return OsVersion.Major == 6;
        }

        public static bool IsWindowsVistaOrGreater()
        {
            return OsVersion.Major >= 6;
        }

        public static bool IsWindows7()
        {
            return OsVersion.Major == 6 && OsVersion.Minor == 1;
        }

        public static bool IsWindows7OrGreater()
        {
            return OsVersion.Major == 6 && OsVersion.Minor >= 1 || OsVersion.Major > 6;
        }

        public static bool IsWindows8()
        {
            return OsVersion.Major == 6 && OsVersion.Minor == 2;
        }

        public static bool IsWindows8OrGreater()
        {
            return OsVersion.Major == 6 && OsVersion.Minor >= 2 || OsVersion.Major > 6;
        }

        public static bool IsWindows10OrGreater(int build = -1)
        {
            return OsVersion.Major >= 10 && OsVersion.Build >= build;
        }

        public static bool IsWindowCloaked(IntPtr handle)
        {
            if (IsDWMEnabled())
            {
                var result = DwmGetWindowAttribute(handle, 14, out int cloaked, sizeof(int));
                return result == 0 && cloaked != 0;
            }

            return false;
        }

        public static string GetClassName(IntPtr handle)
        {
            if (handle.ToInt32() > 0)
            {
                var sb = new StringBuilder(256);

                if (GetClassName(handle, sb, sb.Capacity) > 0) return sb.ToString();
            }

            return null;
        }

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool SetForegroundWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        internal static extern bool LockWindowUpdate(IntPtr hWndLock);

        [DllImport("user32.dll")]
        public static extern int ReleaseDC(IntPtr hWnd, IntPtr hDc);

        [DllImport("user32.dll")]
        public static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int x, int y, int cx, int cy,
            SetWindowPosFlags uFlags);

        [DllImport("user32.dll")]
        public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        [DllImport("shell32.dll", CharSet = CharSet.Unicode)]
        public static extern IntPtr ILCreateFromPathW(string pszPath);

        [DllImport("shell32.dll")]
        public static extern int SHOpenFolderAndSelectItems(IntPtr pidlFolder, int cild, IntPtr apidl, int dwFlags);

        [DllImport("shell32.dll")]
        public static extern void ILFree(IntPtr pidl);

        public static void OpenFolderAndSelectFile(string filePath)
        {
            var pidl = ILCreateFromPathW(filePath);

            try
            {
                SHOpenFolderAndSelectItems(pidl, 0, IntPtr.Zero, 0);
            }
            finally
            {
                ILFree(pidl);
            }
        }

        [DllImport("user32.dll", CharSet = CharSet.Auto)]
        public static extern IntPtr SendMessage(IntPtr hWnd, int msg, int wParam, int lParam);

        [DllImport("user32.dll")]
        public static extern uint SendInput(uint nInputs, [MarshalAs(UnmanagedType.LPArray)] [In]
            INPUT[] pInputs, int cbSize);

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool GetScrollInfo(IntPtr hwnd, int fnBar, ref SCROLLINFO lpsi);

        [DllImport("user32.dll")]
        public static extern int GetSystemMetrics(int smIndex);

        [DllImport("user32.dll")]
        public static extern IntPtr GetWindowDC(IntPtr hWnd);

        [DllImport("gdi32.dll")]
        public static extern IntPtr CreateCompatibleDC(IntPtr hdc);

        [DllImport("gdi32.dll")]
        public static extern IntPtr SelectObject(IntPtr hdc, IntPtr hgdiobj);

        /**
         * private enum StretchBltMode : int
{
     STRETCH_ANDSCANS = 1,
     STRETCH_ORSCANS = 2,
     STRETCH_DELETESCANS = 3,
     STRETCH_HALFTONE = 4,
}
         */
        [DllImport("gdi32.dll")]
        public static extern int SetStretchBltMode(IntPtr hdc, int iStretchMode);

        [DllImport("gdi32.dll")]
        public static extern bool BitBlt(IntPtr hdc, int nXDest, int nYDest, int nWidth, int nHeight, IntPtr hdcSrc,
            int nXSrc, int nYSrc, CopyPixelOperation dwRop);

        [DllImport("gdi32", EntryPoint = "StretchBlt")]
        public static extern int StretchBlt(
            IntPtr hdc,
            int xDest,
            int yDest,
            int nWidthDest,
            int nHeightDest,
            IntPtr hSrcDC,
            int xSrc,
            int ySrc,
            int nSrcWidth,
            int nSrcHeight,
            CopyPixelOperation dwRop
        );

        [DllImport("gdi32.dll")]
        public static extern bool DeleteDC(IntPtr hDc);

        [DllImport("gdi32.dll")]
        public static extern bool DeleteObject(IntPtr hObject);

        [DllImport("gdi32.dll")]
        public static extern IntPtr CreateCompatibleBitmap(IntPtr hdc, int nWidth, int nHeight);

        [DllImport("user32.dll")]
        public static extern int SetCursorPos(int x, int y);

        [DllImport("user32.dll")]
        public static extern IntPtr GetWindow(IntPtr hWnd, uint wWcmd);

        #endregion
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct WINDOWINFO
    {
        public uint cbSize;
        public RECT rcWindow;
        public RECT rcClient;
        public uint dwStyle;
        public uint dwExStyle;
        public uint dwWindowStatus;
        public uint cxWindowBorders;
        public uint cyWindowBorders;
        public ushort atomWindowType;
        public ushort wCreatorVersion;

        public WINDOWINFO(bool? filler) :
            this() // Allows automatic initialization of "cbSize" with "new WINDOWINFO(null/true/false)".
        {
            cbSize = (uint)Marshal.SizeOf(typeof(WINDOWINFO));
        }
    }


    public struct WINDOWPLACEMENT
    {
        public int length;
        public int flags;
        public WindowShowStyle showCmd;
        public POINT ptMinPosition;
        public POINT ptMaxPosition;
        public RECT rcNormalPosition;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct POINT
    {
        public int X;
        public int Y;

        public POINT(int x, int y)
        {
            X = x;
            Y = y;
        }

        public static explicit operator Point(POINT p)
        {
            return new Point(p.X, p.Y);
        }

        public static explicit operator POINT(Point p)
        {
            return new POINT(p.X, p.Y);
        }
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct RECT
    {
        public int Left;
        public int Top;
        public int Right;
        public int Bottom;

        public int X
        {
            get => Left;
            set
            {
                Right -= Left - value;
                Left = value;
            }
        }

        public int Y
        {
            get => Top;
            set
            {
                Bottom -= Top - value;
                Top = value;
            }
        }

        public int Width
        {
            get => Right - Left;
            set => Right = value + Left;
        }

        public int Height
        {
            get => Bottom - Top;
            set => Bottom = value + Top;
        }

        public Point Location
        {
            get => new Point(Left, Top);
            set
            {
                X = value.X;
                Y = value.Y;
            }
        }

        public Size Size
        {
            get => new Size(Width, Height);
            set
            {
                Width = value.Width;
                Height = value.Height;
            }
        }

        public RECT(int left, int top, int right, int bottom)
        {
            Left = left;
            Top = top;
            Right = right;
            Bottom = bottom;
        }

        public RECT(Rectangle r) : this(r.Left, r.Top, r.Right, r.Bottom)
        {
        }

        public static implicit operator Rectangle(RECT r)
        {
            return new Rectangle(r.Left, r.Top, r.Width, r.Height);
        }

        public static implicit operator RECT(Rectangle r)
        {
            return new RECT(r);
        }

        public static bool operator ==(RECT r1, RECT r2)
        {
            return r1.Equals(r2);
        }

        public static bool operator !=(RECT r1, RECT r2)
        {
            return !r1.Equals(r2);
        }

        public bool Equals(RECT r)
        {
            return r.Left == Left && r.Top == Top && r.Right == Right && r.Bottom == Bottom;
        }

        public override bool Equals(object obj)
        {
            if (obj is RECT rect) return Equals(rect);

            if (obj is Rectangle rectangle) return Equals(new RECT(rectangle));

            return false;
        }

        public override int GetHashCode()
        {
            return ((Rectangle)this).GetHashCode();
        }

        public override string ToString()
        {
            return string.Format(CultureInfo.CurrentCulture, "{{Left={0},Top={1},Right={2},Bottom={3}}}", Left, Top,
                Right, Bottom);
        }
    }

    public enum ScrollBarCommands
    {
        SB_PAGEDOWN = 3,
        SB_TOP = 6,
        SB_BOTTOM = 7
    }

    [Serializable]
    [StructLayout(LayoutKind.Sequential)]
    public struct SCROLLINFO
    {
        public uint cbSize;
        public uint fMask;
        public int nMin;
        public int nMax;
        public uint nPage;
        public int nPos;
        public int nTrackPos;
    }

    /// <summary>Enumeration of the different ways of showing a window using ShowWindow</summary>
    public enum WindowShowStyle : uint
    {
        /// <summary>Hides the window and activates another window.</summary>
        /// <remarks>See SW_HIDE</remarks>
        Hide = 0,

        /// <summary>
        ///     Activates and displays a window. If the window is minimized
        ///     or maximized, the system restores it to its original size and
        ///     position. An application should specify this flag when displaying
        ///     the window for the first time.
        /// </summary>
        /// <remarks>See SW_SHOWNORMAL</remarks>
        ShowNormal = 1,

        /// <summary>Activates the window and displays it as a minimized window.</summary>
        /// <remarks>See SW_SHOWMINIMIZED</remarks>
        ShowMinimized = 2,

        /// <summary>Activates the window and displays it as a maximized window.</summary>
        /// <remarks>See SW_SHOWMAXIMIZED</remarks>
        ShowMaximized = 3,

        /// <summary>Maximizes the specified window.</summary>
        /// <remarks>See SW_MAXIMIZE</remarks>
        Maximize = 3,

        /// <summary>
        ///     Displays a window in its most recent size and position.
        ///     This value is similar to "ShowNormal", except the window is not
        ///     actived.
        /// </summary>
        /// <remarks>See SW_SHOWNOACTIVATE</remarks>
        ShowNormalNoActivate = 4,

        /// <summary>
        ///     Activates the window and displays it in its current size
        ///     and position.
        /// </summary>
        /// <remarks>See SW_SHOW</remarks>
        Show = 5,

        /// <summary>
        ///     Minimizes the specified window and activates the next
        ///     top-level window in the Z order.
        /// </summary>
        /// <remarks>See SW_MINIMIZE</remarks>
        Minimize = 6,

        /// <summary>
        ///     Displays the window as a minimized window. This value is
        ///     similar to "ShowMinimized", except the window is not activated.
        /// </summary>
        /// <remarks>See SW_SHOWMINNOACTIVE</remarks>
        ShowMinNoActivate = 7,

        /// <summary>
        ///     Displays the window in its current size and position. This
        ///     value is similar to "Show", except the window is not activated.
        /// </summary>
        /// <remarks>See SW_SHOWNA</remarks>
        ShowNoActivate = 8,

        /// <summary>
        ///     Activates and displays the window. If the window is
        ///     minimized or maximized, the system restores it to its original size
        ///     and position. An application should specify this flag when restoring
        ///     a minimized window.
        /// </summary>
        /// <remarks>See SW_RESTORE</remarks>
        Restore = 9,

        /// <summary>
        ///     Sets the show state based on the SW_ value specified in the
        ///     STARTUPINFO structure passed to the CreateProcess function by the
        ///     program that started the application.
        /// </summary>
        /// <remarks>See SW_SHOWDEFAULT</remarks>
        ShowDefault = 10,

        /// <summary>
        ///     Windows 2000/XP: Minimizes a window, even if the thread
        ///     that owns the window is hung. This flag should only be used when
        ///     minimizing windows from a different thread.
        /// </summary>
        /// <remarks>See SW_FORCEMINIMIZE</remarks>
        ForceMinimized = 11
    }
}