using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;

namespace OCRTools.Common
{
    /// <summary>
    /// 控件浏览器 - 用于查找和浏览各种控件，包括特殊控件（TabPage、ToolStripItem等）
    /// </summary>
    public static class ControlExplorer
    {
        /// <summary>
        /// 获取控件的所有子控件，包括特殊控件
        /// </summary>
        /// <param name="parent">父控件</param>
        /// <param name="includeInvisible">是否包含不可见控件</param>
        /// <returns>所有子控件列表</returns>
        public static List<Control> GetAllControls(Control parent, bool includeInvisible = false)
        {
            List<Control> controls = new List<Control>();

            if (parent == null)
                return controls;

            foreach (Control control in parent.Controls)
            {
                if (includeInvisible || control.Visible)
                {
                    controls.Add(control);

                    // 递归获取子控件
                    controls.AddRange(GetAllControls(control, includeInvisible));

                    // 特殊处理TabControl
                    if (control is TabControl)
                    {
                        TabControl tabControl = (TabControl)control;
                        foreach (TabPage page in tabControl.TabPages)
                        {
                            if (!controls.Contains(page))
                                controls.Add(page);

                            controls.AddRange(GetAllControls(page, includeInvisible));
                        }
                    }
                }
            }

            return controls;
        }

        /// <summary>
        /// 获取特殊子项（如ToolStripItem、ListViewItem等）
        /// </summary>
        /// <param name="parent">父控件</param>
        /// <returns>特殊子项列表</returns>
        public static List<object> GetSpecialItems(Control parent)
        {
            List<object> items = new List<object>();

            if (parent == null)
                return items;

            // 处理ToolStrip
            if (parent is ToolStrip)
            {
                ToolStrip toolStrip = (ToolStrip)parent;
                foreach (ToolStripItem item in toolStrip.Items)
                {
                    items.Add(item);

                    // 处理嵌套的工具条项
                    if (item is ToolStripDropDownItem)
                    {
                        ToolStripDropDownItem dropDownItem = (ToolStripDropDownItem)item;
                        foreach (ToolStripItem dropDownChild in dropDownItem.DropDownItems)
                        {
                            items.Add(dropDownChild);
                        }
                    }
                }
            }

            // 处理MenuStrip
            if (parent is MenuStrip)
            {
                MenuStrip menuStrip = (MenuStrip)parent;
                foreach (ToolStripItem item in menuStrip.Items)
                {
                    items.Add(item);

                    if (item is ToolStripMenuItem)
                    {
                        AddMenuItems(items, (ToolStripMenuItem)item);
                    }
                }
            }

            // 处理ContextMenuStrip
            if (parent is ContextMenuStrip)
            {
                ContextMenuStrip contextMenu = (ContextMenuStrip)parent;
                foreach (ToolStripItem item in contextMenu.Items)
                {
                    items.Add(item);

                    if (item is ToolStripMenuItem)
                    {
                        AddMenuItems(items, (ToolStripMenuItem)item);
                    }
                }
            }

            // 处理ListView
            if (parent is ListView)
            {
                ListView listView = (ListView)parent;

                // 添加列头
                foreach (ColumnHeader column in listView.Columns)
                {
                    items.Add(column);
                }

                // 添加列表项
                foreach (ListViewItem item in listView.Items)
                {
                    items.Add(item);

                    foreach (ListViewItem.ListViewSubItem subItem in item.SubItems)
                    {
                        items.Add(subItem);
                    }
                }
            }

            // 处理TreeView
            if (parent is TreeView)
            {
                TreeView treeView = (TreeView)parent;
                foreach (TreeNode node in treeView.Nodes)
                {
                    items.Add(node);
                    AddTreeNodes(items, node);
                }
            }

            // 处理DataGridView
            if (parent is DataGridView)
            {
                DataGridView grid = (DataGridView)parent;

                // 添加列
                foreach (DataGridViewColumn column in grid.Columns)
                {
                    items.Add(column);
                    items.Add(column.HeaderCell);
                }

                // 添加可见单元格
                int displayedRowCount = grid.DisplayedRowCount(true);
                int displayedColumnCount = grid.DisplayedColumnCount(true);
                int firstDisplayedRowIndex = grid.FirstDisplayedCell?.RowIndex ?? 0;
                int firstDisplayedColumnIndex = grid.FirstDisplayedCell?.ColumnIndex ?? 0;

                for (int i = 0; i < displayedRowCount; i++)
                {
                    int rowIndex = firstDisplayedRowIndex + i;
                    if (rowIndex >= grid.RowCount)
                        break;

                    for (int j = 0; j < displayedColumnCount; j++)
                    {
                        int columnIndex = firstDisplayedColumnIndex + j;
                        if (columnIndex >= grid.ColumnCount)
                            break;

                        if (grid.Rows[rowIndex].Cells[columnIndex].Visible)
                            items.Add(grid.Rows[rowIndex].Cells[columnIndex]);
                    }
                }
            }

            // 处理ComboBox
            if (parent is ComboBox)
            {
                ComboBox comboBox = (ComboBox)parent;

                if (comboBox.DropDownStyle != ComboBoxStyle.Simple && comboBox.DroppedDown)
                {
                    for (int i = 0; i < comboBox.Items.Count; i++)
                    {
                        items.Add(comboBox.Items[i]);
                    }
                }
            }

            return items;
        }

        /// <summary>
        /// 找到指定位置的最深层控件
        /// </summary>
        /// <param name="parent">父控件</param>
        /// <param name="point">客户端坐标</param>
        /// <returns>找到的控件</returns>
        public static Control FindDeepestControlAt(Control parent, Point point)
        {
            if (parent == null || !parent.Visible || !parent.ClientRectangle.Contains(point))
                return null;

            // 子控件采用逆序遍历，因为后面的控件通常会覆盖前面的控件
            for (int i = parent.Controls.Count - 1; i >= 0; i--)
            {
                Control child = parent.Controls[i];
                if (!child.Visible)
                    continue;

                // 转换坐标到子控件
                Point childPoint = new Point(point.X - child.Left, point.Y - child.Top);

                if (child.ClientRectangle.Contains(childPoint))
                {
                    // 特殊处理TabControl
                    if (child is TabControl)
                    {
                        TabControl tabControl = (TabControl)child;
                        TabPage selectedPage = tabControl.SelectedTab;

                        if (selectedPage != null)
                        {
                            // 首先检查当前选中的Tab页
                            Control deeperControl = FindDeepestControlAt(selectedPage, childPoint);
                            if (deeperControl != null)
                                return deeperControl;
                        }
                    }

                    // 常规处理
                    Control deepControl = FindDeepestControlAt(child, childPoint);
                    if (deepControl != null)
                        return deepControl;

                    return child;
                }
            }

            // 特殊处理TabControl的选项卡区域
            if (parent is TabControl)
            {
                TabControl tabControl = (TabControl)parent;

                // 检查点是否在选项卡标题区域
                for (int i = 0; i < tabControl.TabCount; i++)
                {
                    Rectangle tabRect = tabControl.GetTabRect(i);
                    if (tabRect.Contains(point))
                    {
                        // 找到了选项卡标题
                        return tabControl.TabPages[i];
                    }
                }
            }

            return parent;
        }

        /// <summary>
        /// 获取指定位置处的ToolStripItem
        /// </summary>
        /// <param name="toolStrip">工具条</param>
        /// <param name="point">客户端坐标</param>
        /// <returns>找到的工具条项</returns>
        public static ToolStripItem GetToolStripItemAt(ToolStrip toolStrip, Point point)
        {
            if (toolStrip == null)
                return null;

            return toolStrip.GetItemAt(point);
        }

        /// <summary>
        /// 获取控件的完整路径
        /// </summary>
        /// <param name="control">控件</param>
        /// <returns>控件路径</returns>
        public static string GetControlPath(Control control)
        {
            if (control == null)
                return string.Empty;

            List<string> parts = new List<string>();
            Control current = control;

            while (current != null)
            {
                string name = current.Name;

                // 处理TabPage特殊情况
                if (current is TabPage && current.Parent is TabControl)
                {
                    TabControl tabControl = (TabControl)current.Parent;
                    int index = tabControl.TabPages.IndexOf((TabPage)current);
                    name = string.Format("{0}[{1}]", name, index);
                }

                parts.Add(name);
                current = current.Parent;
            }

            parts.Reverse();
            return string.Join(".", parts);
        }

        /// <summary>
        /// 获取特殊项目的路径
        /// </summary>
        /// <param name="item">特殊项目</param>
        /// <param name="parent">父控件</param>
        /// <returns>项目路径</returns>
        public static string GetSpecialItemPath(object item, Control parent)
        {
            if (item == null || parent == null)
                return string.Empty;

            string parentPath = GetControlPath(parent);

            // ToolStripItem
            if (item is ToolStripItem)
            {
                ToolStripItem toolStripItem = (ToolStripItem)item;
                ToolStrip toolStrip = toolStripItem.Owner;

                if (toolStrip != null)
                {
                    int index = toolStrip.Items.IndexOf(toolStripItem);
                    return string.Format("{0}.Items[{1}]", parentPath, index);
                }
            }

            // TreeNode
            if (item is TreeNode)
            {
                TreeNode node = (TreeNode)item;
                string path = parentPath;

                // 获取节点完整路径
                List<int> indices = new List<int>();
                TreeNode current = node;
                TreeNode parent2 = node.Parent;

                while (parent2 != null)
                {
                    indices.Add(parent2.Nodes.IndexOf(current));
                    current = parent2;
                    parent2 = current.Parent;
                }

                if (indices.Count == 0)
                {
                    // 顶级节点
                    return string.Format("{0}.Nodes[{1}]", path, ((TreeView)parent).Nodes.IndexOf(node));
                }
                else
                {
                    // 子节点
                    indices.Reverse();

                    path += ".Nodes";
                    foreach (int index in indices)
                    {
                        path += string.Format("[{0}]", index);
                    }

                    return path;
                }
            }

            // ListViewItem
            if (item is ListViewItem)
            {
                ListViewItem listItem = (ListViewItem)item;
                return string.Format("{0}.Items[{1}]", parentPath, ((ListView)parent).Items.IndexOf(listItem));
            }

            // DataGridViewCell
            if (item is DataGridViewCell)
            {
                DataGridViewCell cell = (DataGridViewCell)item;
                return string.Format("{0}.Rows[{1}].Cells[{2}]", parentPath, cell.RowIndex, cell.ColumnIndex);
            }

            return parentPath;
        }

        /// <summary>
        /// 根据路径查找控件
        /// </summary>
        /// <param name="rootForm">根窗体</param>
        /// <param name="path">控件路径</param>
        /// <returns>找到的控件</returns>
        public static Control FindControlByPath(Form rootForm, string path)
        {
            if (rootForm == null || string.IsNullOrEmpty(path))
                return null;

            string[] parts = path.Split('.');

            if (parts.Length == 0)
                return null;

            Control current = rootForm;

            // 跳过第一个部分（通常是表单名称）
            for (int i = 1; i < parts.Length; i++)
            {
                string part = parts[i];

                // 处理TabPage特殊情况 - 形如：tabPage1[0]
                if (part.Contains("[") && part.EndsWith("]"))
                {
                    int bracketIndex = part.IndexOf('[');
                    string name = part.Substring(0, bracketIndex);
                    string indexStr = part.Substring(bracketIndex + 1, part.Length - bracketIndex - 2);

                    if (int.TryParse(indexStr, out int index))
                    {
                        // 处理TabControl的TabPages
                        if (current is TabControl)
                        {
                            TabControl tabControl = (TabControl)current;
                            if (index >= 0 && index < tabControl.TabPages.Count)
                            {
                                current = tabControl.TabPages[index];
                                continue;
                            }
                        }
                    }
                }

                // 常规处理
                bool found = false;
                foreach (Control child in current.Controls)
                {
                    if (child.Name == part)
                    {
                        current = child;
                        found = true;
                        break;
                    }
                }

                if (!found)
                    return null;
            }

            return current;
        }

        /// <summary>
        /// 根据名称查找控件（递归）
        /// </summary>
        /// <param name="parent">父控件</param>
        /// <param name="name">控件名称</param>
        /// <returns>找到的控件</returns>
        public static Control FindControlByName(Control parent, string name)
        {
            if (parent == null || string.IsNullOrEmpty(name))
                return null;

            if (parent.Name == name)
                return parent;

            foreach (Control child in parent.Controls)
            {
                Control found = FindControlByName(child, name);
                if (found != null)
                    return found;
            }

            // 特殊处理TabControl
            if (parent is TabControl)
            {
                TabControl tabControl = (TabControl)parent;
                foreach (TabPage page in tabControl.TabPages)
                {
                    if (page.Name == name)
                        return page;

                    Control found = FindControlByName(page, name);
                    if (found != null)
                        return found;
                }
            }

            return null;
        }

        /// <summary>
        /// 获取控件在窗体中的矩形区域
        /// </summary>
        /// <param name="control">控件</param>
        /// <param name="form">窗体</param>
        /// <returns>控件在窗体中的矩形</returns>
        public static Rectangle GetControlRectInForm(Control control, Form form)
        {
            if (control == null || form == null || Equals(control, form))
                return Rectangle.Empty;

            try
            {
                // 获取控件相对于窗体的位置
                Point location = form.PointToClient(control.Parent.PointToScreen(control.Location));

                // 返回控件在窗体中的矩形
                return new Rectangle(location, control.Size);
            }
            catch
            {
                return Rectangle.Empty;
            }
        }

        /// <summary>
        /// 获取ToolStripItem在窗体中的矩形区域
        /// </summary>
        /// <param name="item">工具条项</param>
        /// <param name="form">窗体</param>
        /// <returns>工具条项在窗体中的矩形</returns>
        public static Rectangle GetToolStripItemRectInForm(ToolStripItem item, Form form)
        {
            if (item == null || form == null)
                return Rectangle.Empty;

            try
            {
                ToolStrip owner = item.Owner;
                if (owner == null)
                    return Rectangle.Empty;

                // 获取项目在工具条中的矩形
                Rectangle itemRect = item.Bounds;

                // 转换为屏幕坐标
                Point topLeft = owner.PointToScreen(itemRect.Location);

                // 转换为窗体坐标
                Point formPoint = form.PointToClient(topLeft);

                return new Rectangle(formPoint, itemRect.Size);
            }
            catch
            {
                return Rectangle.Empty;
            }
        }

        /// <summary>
        /// 获取特殊项目在窗体中的矩形区域
        /// </summary>
        /// <param name="item">特殊项目</param>
        /// <param name="parent">父控件</param>
        /// <param name="form">窗体</param>
        /// <returns>特殊项目在窗体中的矩形</returns>
        public static Rectangle GetSpecialItemRectInForm(object item, Control parent, Form form)
        {
            if (item == null || parent == null || form == null)
                return Rectangle.Empty;

            try
            {
                // ToolStripItem
                if (item is ToolStripItem)
                {
                    return GetToolStripItemRectInForm((ToolStripItem)item, form);
                }

                // TreeNode
                if (item is TreeNode && parent is TreeView)
                {
                    TreeNode node = (TreeNode)item;
                    TreeView treeView = (TreeView)parent;

                    Rectangle nodeRect = node.Bounds;
                    if (nodeRect.IsEmpty)
                        return Rectangle.Empty;

                    // 转换为屏幕坐标
                    Point topLeft = treeView.PointToScreen(nodeRect.Location);

                    // 转换为窗体坐标
                    Point formPoint = form.PointToClient(topLeft);

                    return new Rectangle(formPoint, nodeRect.Size);
                }

                // ListViewItem
                if (item is ListViewItem && parent is ListView)
                {
                    ListViewItem listItem = (ListViewItem)item;
                    ListView listView = (ListView)parent;

                    Rectangle itemRect = listItem.Bounds;
                    if (itemRect.IsEmpty)
                        return Rectangle.Empty;

                    // 转换为屏幕坐标
                    Point topLeft = listView.PointToScreen(itemRect.Location);

                    // 转换为窗体坐标
                    Point formPoint = form.PointToClient(topLeft);

                    return new Rectangle(formPoint, itemRect.Size);
                }

                // DataGridViewCell
                if (item is DataGridViewCell && parent is DataGridView)
                {
                    DataGridViewCell cell = (DataGridViewCell)item;
                    DataGridView grid = (DataGridView)parent;

                    Rectangle cellRect = grid.GetCellDisplayRectangle(cell.ColumnIndex, cell.RowIndex, false);
                    if (cellRect.IsEmpty)
                        return Rectangle.Empty;

                    // 转换为屏幕坐标
                    Point topLeft = grid.PointToScreen(cellRect.Location);

                    // 转换为窗体坐标
                    Point formPoint = form.PointToClient(topLeft);

                    return new Rectangle(formPoint, cellRect.Size);
                }

                return Rectangle.Empty;
            }
            catch
            {
                return Rectangle.Empty;
            }
        }

        /// <summary>
        /// 获取点下的可视控件和特殊项目
        /// </summary>
        /// <param name="parent">父控件</param>
        /// <param name="point">客户端坐标</param>
        /// <returns>找到的项目</returns>
        public static object GetItemAtPoint(Control parent, Point point)
        {
            if (parent == null)
                return null;

            // 先查找常规控件
            Control control = FindDeepestControlAt(parent, point);
            if (control != null && control != parent)
                return control;

            // 再查找特殊项目
            List<object> specialItems = GetSpecialItems(parent);
            foreach (object item in specialItems)
            {
                Rectangle itemRect = GetSpecialItemRectInForm(item, parent, parent.FindForm());
                if (itemRect.Contains(point))
                    return item;
            }

            return parent;
        }

        #region 辅助方法

        /// <summary>
        /// 添加菜单项
        /// </summary>
        private static void AddMenuItems(List<object> items, ToolStripMenuItem menuItem)
        {
            foreach (ToolStripItem child in menuItem.DropDownItems)
            {
                items.Add(child);

                if (child is ToolStripMenuItem)
                {
                    AddMenuItems(items, (ToolStripMenuItem)child);
                }
            }
        }

        /// <summary>
        /// 添加树节点
        /// </summary>
        private static void AddTreeNodes(List<object> items, TreeNode node)
        {
            foreach (TreeNode child in node.Nodes)
            {
                items.Add(child);
                AddTreeNodes(items, child);
            }
        }

        #endregion
    }
}