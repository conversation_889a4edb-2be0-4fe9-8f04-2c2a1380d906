using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class ComboxbtnRenderer : ToolStripProfessionalRenderer
    {
        private readonly bool _fontCenter;

        public ComboxbtnRenderer(bool fontCenter)
        {
            _fontCenter = fontCenter;
        }

        protected override void OnRenderToolStripBorder(ToolStripRenderEventArgs e)
        {
            var toolStrip = e.ToolStrip;
            if (toolStrip is ToolStripDropDown)
                using (var pen = new Pen(Color.FromArgb(192, 192, 192)))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, e.ToolStrip.Width - 1, e.ToolStrip.Height - 1);
                }
        }

        protected override void OnRenderMenuItemBackground(ToolStripItemRenderEventArgs e)
        {
            if (e.Item.IsOnDropDown && e.Item.Selected && e.Item.Enabled) DrawMenuDropDownItemHighlight(e);
        }

        protected override void OnRenderToolStripBackground(ToolStripRenderEventArgs e)
        {
            var toolStrip = e.ToolStrip;
            if (toolStrip is ToolStripDropDown) RenderToolStripDropDownBackground(e);
        }

        private void RenderToolStripDropDownBackground(ToolStripRenderEventArgs e)
        {
            var rect = new Rectangle(Point.Empty, e.ToolStrip.Size);
            using (Brush brush = new SolidBrush(Color.White))
            {
                e.Graphics.FillRectangle(brush, rect);
            }
        }

        public void DrawMenuDropDownItemHighlight(ToolStripItemRenderEventArgs e)
        {
            var rect = new Rectangle(0, 0, e.ToolStrip.Size.Width, (int) e.Graphics.VisibleClipBounds.Height);
            e.Graphics.FillRectangle(new SolidBrush(Color.FromArgb(240, 240, 240)), rect);
        }

        protected override void OnRenderItemText(ToolStripItemTextRenderEventArgs e)
        {
            if (e.Item.IsOnDropDown)
            {
                if (e.Item.Selected && e.Item.Enabled) e.TextColor = Color.OrangeRed;
                e.TextRectangle =
                    new Rectangle(0, 0, e.ToolStrip.Size.Width, (int) e.Graphics.VisibleClipBounds.Height);
                if (_fontCenter) e.TextFormat |= TextFormatFlags.HorizontalCenter;
                e.TextFormat |= TextFormatFlags.VerticalCenter;
            }

            base.OnRenderItemText(e);
        }
    }
}