﻿using System.Collections.Generic;
using System.Drawing;
using System.Reflection;

namespace OCRTools.Common
{
    internal class CommonPlug
    {

        internal static List<PlugEntity> GetAllPlug()
        {
            List<PlugEntity> lstAds = new List<PlugEntity>();
            try
            {
                //var plug = new PlugEntity()
                //{
                //    Name = "ChatGPT",
                //    FontSize = 15F,
                //    Desc = "ChatGPT - OCR助手",
                //    Url = "https://ocr.oldfish.cn/gpt/",
                //    Type = PlugType.Url,
                //    ButtonType = ButtonType.Image,
                //    Image = ImageProcessHelper.ImageToBase64(Image.FromFile(@"C:\Users\<USER>\Downloads\chatgpt.png")),
                //    Top = 3,
                //    Width = 600,
                //    Height = 700
                //};
                //lstAds.Add(plug);
                //System.Console.WriteLine(CommonString.JavaScriptSerializer.Serialize(lstAds));

                var result =
                    WebClientExt.GetHtml(CommonString.HostUpdate?.FullUrl + "update/uPlug.txt?t=" + ServerTime.DateTime.Ticks, 5);
                if (!string.IsNullOrEmpty(result) && result.Length > 2)
                    lstAds = CommonString.JavaScriptSerializer.Deserialize<List<PlugEntity>>(result);
            }
            catch { }
            return lstAds;
        }
    }

    [Obfuscation]
    internal class PlugEntity
    {
        [Obfuscation]
        public string Name { get; set; }

        [Obfuscation]
        public string Desc { get; set; }

        [Obfuscation]
        public ButtonType ButtonType { get; set; }

        [Obfuscation]
        public PlugType Type { get; set; }

        [Obfuscation]
        public string Image { get; set; }

        [Obfuscation]
        public string Url { get; set; }

        [Obfuscation]
        public string ForeColor { get; set; }

        [Obfuscation]
        public float FontSize { get; set; } = 15F;

        [Obfuscation]
        public int Top { get; set; }

        [Obfuscation]
        public int Width { get; set; }

        [Obfuscation]
        public int Height { get; set; }

        public Image GetImage()
        {
            var image = ImageProcessHelper.Base64StringToImage(Image);
            if (CommonSetting.夜间模式)
                image = ImageProcessHelper.InverseImage(new Bitmap(image));
            return image;
        }

        public void Click(object sender, System.EventArgs e)
        {
            try
            {
                switch (Type)
                {
                    case PlugType.Url:
                        CommonMethod.DetermineCall(FrmMain.FrmTool, () =>
                        {
                            var view = new FrmViewUrl
                            {
                                Url = Url,
                                WindowState = System.Windows.Forms.FormWindowState.Maximized,
                                StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen,
                                Icon = FrmMain.FrmTool.Icon,
                                Text = Desc ?? Name
                            };
                            if (Width > 0 && Height > 0)
                            {
                                view.WindowState = System.Windows.Forms.FormWindowState.Normal;
                                view.Size = new Size(Width, Height);
                                //var scale = view.GetDpiScale();
                                //view.Size = new Size((int)(Width / scale), (int)(Height / scale));
                            }
                            view.Show();
                        });
                        break;
                    case PlugType.Text:
                        CommonMethod.ShowNotificationTip(Desc, null, 10 * 1000);
                        break;
                }
            }
            catch { }
        }
    }

    [Obfuscation]
    internal enum ButtonType
    {
        Image = 0,
        ImageAndText = 1,
    }

    [Obfuscation]
    internal enum PlugType
    {
        Url = 0,
        Text = 1,
    }
}
