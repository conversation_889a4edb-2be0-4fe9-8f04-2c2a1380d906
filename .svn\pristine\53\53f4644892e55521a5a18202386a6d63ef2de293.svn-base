using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Emgu.CV;
using Emgu.CV.CvEnum;

namespace OcrLib
{
    public class Ocr
    {
        public static int NMaxDegreeOfParallelism = 5;

        private DbNet dbNet;

        private AngleNet angleNet;

        private CrnnNet crnnNet;

        private string OcrType;

        public Ocr(string ocrType = "")
        {
            OcrType = ocrType;
            dbNet = new DbNet();
            angleNet = new AngleNet();
            crnnNet = new CrnnNet();
        }

        public void InitModels(string detPath, string clsPath, string recPath, string keysPath, int numThread)
        {
            try
            {
                dbNet.InitModel(detPath, numThread);
                angleNet.InitModel(clsPath, numThread);
                crnnNet.InitModel(recPath, keysPath, numThread);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message + ex.StackTrace);
                throw ex;
            }
        }

        public OcrResult Detect(byte[] img, int padding, int maxSideLen, float boxScoreThresh, float boxThresh, float unClipRatio, bool doAngle, bool mostAngle, bool isPaddle)
        {
            Mat mat = new Mat();
            CvInvoke.Imdecode(img, ImreadModes.Color, mat);
            return DetectByMat(padding, maxSideLen, boxScoreThresh, boxThresh, unClipRatio, doAngle, mostAngle, mat, isPaddle);
        }

        private OcrResult DetectByMat(int padding, int maxSideLen, float boxScoreThresh, float boxThresh, float unClipRatio, bool doAngle, bool mostAngle, Mat brgSrc, bool isPaddle)
        {
            Mat paddingSrc = OcrUtils.MakePadding(brgSrc, padding);

            int originMaxSide = Math.Max(brgSrc.Cols, brgSrc.Rows);
            int resize;
            if (maxSideLen <= 0 || maxSideLen > originMaxSide)
            {
                resize = originMaxSide;
            }
            else
            {
                resize = maxSideLen;
            }
            resize += 2 * padding;
            ScaleParam scaleParam = ScaleParam.GetScaleParam(paddingSrc, resize);
            return DetectOnce(paddingSrc, scaleParam, boxScoreThresh, boxThresh, unClipRatio, doAngle, mostAngle, isPaddle);
        }

        private OcrResult DetectOnce(Mat src, ScaleParam scale, float boxScoreThresh, float boxThresh, float unClipRatio, bool doAngle, bool mostAngle, bool isPaddle)
        {
            Console.WriteLine("=====" + OcrType + "Start detect=====");
            long nStart = DateTime.Now.Ticks;
            List<TextBox> textBoxes = dbNet.GetTextBoxes(src, scale, boxScoreThresh, boxThresh, unClipRatio, isPaddle);
            var dbNetTime = DateTime.Now.Ticks;
            Console.WriteLine($"DbNetTime({(dbNetTime - nStart) / 10000F}ms)");
            List<Mat> partImages = OcrUtils.GetPartImages(src, textBoxes);
            List<Angle> angles = angleNet.GetAngles(partImages, isPaddle, doAngle, mostAngle);
            Parallel.For(0, partImages.Count, new ParallelOptions
            {
                MaxDegreeOfParallelism = 5
            }, delegate (int index)
            {
                if (angles[index].Index == (isPaddle ? 1 : 0))
                {
                    partImages[index] = OcrUtils.MatRotateClockWise180(partImages[index]);
                }
            });
            var angleTime = DateTime.Now.Ticks;
            Console.WriteLine($"AngleTime({(angleTime - dbNetTime) / 10000F}ms)");

            List<TextLine> textLines = crnnNet.GetTextLines(partImages, isPaddle);
            var crnnNetTime = DateTime.Now.Ticks;
            Console.WriteLine($"CrnnNet({(crnnNetTime - angleTime) / 10000F}ms)");

            List<TextBlock> textBlocks = new List<TextBlock>();
            for (int index = 0; index < textBoxes.Count; index++)
            {
                textBlocks.Add(new TextBlock
                {
                    BoxPoints = textBoxes[index].Points,
                    Text = textLines[index].Text,
                });
            }
            Console.WriteLine(OcrType + $"TotalTime=============({(DateTime.Now.Ticks - nStart) / 10000F}ms)");
            return new OcrResult
            {
                TextBlocks = textBlocks,
                StrRes = string.Join("\n", textBlocks.Select((TextBlock p) => p.Text))
            };
        }
    }
}
