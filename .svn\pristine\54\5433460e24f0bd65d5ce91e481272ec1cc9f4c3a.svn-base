﻿using System.ComponentModel;
using System.Drawing;

namespace OCRTools
{
    internal enum LoadingType
    {
        蓝色箭头 = 0,
        蓝色圆圈 = 1,
        红色圆圈 = 2,
        电脑 = 3,
        信号量 = 4,
        花瓣 = 5
    }

    internal class LoadingTypeConfig
    {
        public int Interval { get; set; }

        public int ImgCount { get; set; }

        public string ImgName { get; set; }
    }

    internal class LoadingTypeHelper
    {
        public static Image GetImageByConfig(LoadingType type, int index = 0)
        {
            var config = GetTypeConfig(type);
            return GetImageByConfig(config, index);
        }

        public static Image GetImageByConfig(LoadingTypeConfig config, int index)
        {
            return (Image)new ComponentResourceManager(typeof(UcLoading)).GetObject(index + config.ImgName);
        }

        public static LoadingTypeConfig GetTypeConfig(LoadingType type)
        {
            var config = new LoadingTypeConfig();
            switch (type)
            {
                case LoadingType.蓝色圆圈:
                    config.Interval = 50;
                    config.ImgCount = 8;
                    config.ImgName = "_bl";
                    break;
                case LoadingType.红色圆圈:
                    config.Interval = 60;
                    config.ImgCount = 10;
                    config.ImgName = "_red";
                    break;
                case LoadingType.电脑:
                    config.Interval = 60;
                    config.ImgCount = 10;
                    config.ImgName = "_sc";
                    break;
                case LoadingType.信号量:
                    config.Interval = 90;
                    config.ImgCount = 5;
                    config.ImgName = "_sg";
                    break;
                case LoadingType.花瓣:
                    config.Interval = 50;
                    config.ImgCount = 8;
                    config.ImgName = "_load";
                    break;
                default:
                    config.Interval = 50;
                    config.ImgCount = 8;
                    config.ImgName = "_qq";
                    break;
            }

            return config;
        }
    }

    internal enum ToolDoubleClickEnum
    {
        显示主窗体 = 1,
        不做任何操作 = 2,
        截图识别 = 0,
        快速截图 = 3,
        截图编辑 = 4,
        快速贴图 = 5,
        截图贴图 = 6,
        粘贴贴图 = 7,
        显隐贴图 = 8,
    }
}