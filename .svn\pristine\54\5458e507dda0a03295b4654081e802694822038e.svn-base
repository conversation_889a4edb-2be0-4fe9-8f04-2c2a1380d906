﻿using System.ComponentModel;
using System.Drawing;

namespace OCRTools
{
    internal enum LoadingType
    {
        蓝色箭头 = 0,
        蓝色圆圈 = 1,
        红色圆圈 = 2,
        电脑 = 3,
        信号量 = 4,
        花瓣 = 5
    }

    internal class LoadintTypeConfig
    {
        public int interval { get; set; }

        public int imgCount { get; set; }

        public string imgName { get; set; }
    }

    internal class LoadingTypeHelper
    {
        public static Image GetImageByConfig(LoadingType type, int index = 0)
        {
            var config = GetTypeConfig(type);
            return GetImageByConfig(config, index);
        }

        public static Image GetImageByConfig(LoadintTypeConfig config, int index)
        {
            return (Image) new ComponentResourceManager(typeof(ucLoading)).GetObject(index + config.imgName);
        }

        public static LoadintTypeConfig GetTypeConfig(LoadingType type)
        {
            var config = new LoadintTypeConfig();
            switch (type)
            {
                case LoadingType.蓝色圆圈:
                    config.interval = 50;
                    config.imgCount = 8;
                    config.imgName = "_bl";
                    break;
                case LoadingType.红色圆圈:
                    config.interval = 60;
                    config.imgCount = 10;
                    config.imgName = "_red";
                    break;
                case LoadingType.电脑:
                    config.interval = 60;
                    config.imgCount = 10;
                    config.imgName = "_sc";
                    break;
                case LoadingType.信号量:
                    config.interval = 90;
                    config.imgCount = 5;
                    config.imgName = "_sg";
                    break;
                case LoadingType.花瓣:
                    config.interval = 50;
                    config.imgCount = 8;
                    config.imgName = "_load";
                    break;
                default:
                    config.interval = 50;
                    config.imgCount = 8;
                    config.imgName = "_qq";
                    break;
            }

            return config;
        }
    }

    internal enum ToolDoubleClickEnum
    {
        截图并识别文字 = 0,
        显示主窗体 = 1,
        不做任何操作 = 2
    }
}