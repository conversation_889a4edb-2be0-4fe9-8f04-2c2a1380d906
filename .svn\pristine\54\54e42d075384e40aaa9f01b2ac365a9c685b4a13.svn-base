using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class ScrollPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = ScrollPatternIdentifiers.Pattern;

        private ScrollPattern(AutomationElement el, IUIAutomationScrollPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        public static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new ScrollPattern(el, (IUIAutomationScrollPattern)pattern, cached);
        }
    }


    public class ScrollItemPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = ScrollItemPatternIdentifiers.Pattern;


        private ScrollItemPattern(AutomationElement el, IUIAutomationScrollItemPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        public static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new ScrollItemPattern(el, (IUIAutomationScrollItemPattern)pattern, cached);
        }
    }
}