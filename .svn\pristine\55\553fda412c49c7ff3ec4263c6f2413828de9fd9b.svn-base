using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.IO;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace OCRTools
{
    public static class ImageHelp
    {
        [DllImport("User32.dll")]
        private static extern IntPtr LoadCursorFromFile(string str);

        public static Image CreateCloseIm(Color color)
        {
            var bitmap = new Bitmap(28.DPIValue(), 28.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var brush = new SolidBrush(color))
                {
                    using (var pen = new Pen(Color.FromArgb(163, 163, 163), 2.DPIValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighQuality;
                        var point = new Point(bitmap.Width / 2, bitmap.Height / 2);
                        var num = 5.DPIValue();
                        graphics.FillRectangle(brush, -1, -1, bitmap.Size.Width + 1, bitmap.Size.Height + 1);
                        graphics.DrawLine(pen, point.X - num, point.Y - num, point.X + num, point.Y + num);
                        graphics.DrawLine(pen, point.X + num, point.Y - num, point.X - num, point.Y + num);
                    }
                }
            }

            return bitmap;
        }

        public static Cursor SetCursor(byte[] resourceName)
        {
            var folderPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var text = folderPath + "\\temp.cur";
            File.WriteAllBytes(text, resourceName);
            var result = new Cursor(LoadCursorFromFile(text));
            File.Delete(text);
            return result;
        }

        public static Image EditEllipse(Color color, int num)
        {
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var solidBrush = new SolidBrush(color))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                    switch (num)
                    {
                        case 1:
                            graphics.FillEllipseCenter(solidBrush, center, 4.DPIValue());
                            break;
                        case 2:
                            graphics.FillEllipseCenter(solidBrush, center, 6.DPIValue());
                            break;
                        case 3:
                            graphics.FillEllipseCenter(solidBrush, center, 8.DPIValue());
                            break;
                        case 4:
                            graphics.FillEllipseCenter(solidBrush, center, 10.DPIValue());
                            break;
                    }
                }
            }

            return bitmap;
        }

        public static Image CreateRecoder(Color color)
        {
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var solidBrush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
                {
                    using (var pen = new Pen(color, 1.DPIValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighQuality;
                        var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                        graphics.DrawEllipseCenter(pen, center, 8.DPIValue());
                        graphics.FillEllipseCenter(solidBrush, center, 4.DPIValue());
                        graphics.DrawEllipseCenter(pen, center, 4.DPIValue());
                    }
                }
            }

            return bitmap;
        }

        public static Image CreateStartRecoder()
        {
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var solidBrush = new SolidBrush(Color.FromArgb(197, 73, 73)))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                    graphics.FillEllipseCenter(solidBrush, center, 7.DPIValue());
                }
            }

            return bitmap;
        }

        public static Image CreateMoveRecoder()
        {
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(Color.Gray, 1f))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                    graphics.DrawEllipseCenter(pen, center, 6.DPIValue());
                    var num = 8.DPIValue();
                    graphics.DrawLine(pen, new Point(center.X, center.Y - num),
                        new Point(center.X, center.Y - num / 4));
                    graphics.DrawLine(pen, new Point(center.X, center.Y + num / 4),
                        new Point(center.X, center.Y + num));
                    graphics.DrawLine(pen, new Point(center.X - num, center.Y),
                        new Point(center.X - num / 4, center.Y));
                    graphics.DrawLine(pen, new Point(center.X + num / 4, center.Y),
                        new Point(center.X + num, center.Y));
                }
            }

            return bitmap;
        }

        public static Image CreateBtnClose()
        {
            var rectangle = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(Color.FromArgb(197, 73, 73), 2.DPIValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.DrawLine(pen,
                        new Point(rectangle.X + rectangle.Height / 6, rectangle.Y + rectangle.Height / 6),
                        new Point(rectangle.X + rectangle.Width - rectangle.Height / 6,
                            rectangle.Y + rectangle.Height - rectangle.Height / 6));
                    graphics.DrawLine(pen,
                        new Point(rectangle.X + rectangle.Height / 6,
                            rectangle.Y + rectangle.Height - rectangle.Height / 6),
                        new Point(rectangle.X + rectangle.Width - rectangle.Height / 6,
                            rectangle.Y + rectangle.Height / 6));
                }
            }

            return bitmap;
        }

        public static Image CreateStopRecoder()
        {
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var solidBrush = new SolidBrush(Color.FromArgb(197, 73, 73)))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                    graphics.FillRectangleCenter(solidBrush, center, 7.DPIValue());
                }
            }

            return bitmap;
        }

        public static Image CreateBtnText(bool isred = false)
        {
            var rectangle = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(Color.Gray, 2.DPIValue()))
                {
                    pen.Color = Color.FromArgb(197, 73, 73);
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    graphics.DrawLine(pen,
                        new Point(rectangle.X + rectangle.Width / 6, rectangle.Y + rectangle.Width / 6),
                        new Point(rectangle.X + rectangle.Width - rectangle.Width / 6,
                            rectangle.Y + rectangle.Width / 6));
                    graphics.DrawLine(pen,
                        new Point(rectangle.X + rectangle.Width / 2, rectangle.Y + rectangle.Width / 6),
                        new Point(rectangle.X + rectangle.Width / 2,
                            rectangle.Y + rectangle.Height - rectangle.Width / 6));
                }
            }

            return bitmap;
        }

        public static Image CreateStep(Color color)
        {
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DPIValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                    var rect = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
                    graphics.DrawRectangle(pen, rect);
                    graphics.DrawStringCenter(pen, center, 6.DPIValue(), color);
                }
            }

            return bitmap;
        }

        public static void DrawStringCenter(this Graphics graphics, Pen pen, Point Center, int width, Color color)
        {
            var r = new Rectangle(Center.X - width + 1.DPIValue(), Center.Y - width, width * 2, width * 2);
            using (var format = new StringFormat
            {
                Alignment = StringAlignment.Center,
                LineAlignment = StringAlignment.Near
            })
            {
                using (var brush = new SolidBrush(color))
                {
                    graphics.TextRenderingHint = TextRenderingHint.AntiAlias;
                    graphics.DrawString("1", new Font("Times New Romans", 10f), brush, r, format);
                }
            }
        }

        public static void DrawTextCenter(this Graphics graphics, Pen pen, Point Center, int width, Color color)
        {
            var r = new Rectangle(Center.X - width + 1.DPIValue(), Center.Y - width, width * 2, width * 2);
            using (var format = new StringFormat
            {
                Alignment = StringAlignment.Center,
                LineAlignment = StringAlignment.Near
            })
            {
                using (var brush = new SolidBrush(color))
                {
                    graphics.TextRenderingHint = TextRenderingHint.AntiAlias;
                    graphics.DrawString("T", new Font("Times New Romans", 10f), brush, r, format);
                }
            }
        }

        public static void DrawEllipseCenter(this Graphics graphics, Pen pen, Point Center, int width)
        {
            var rect = new Rectangle(Center.X - width, Center.Y - width, width * 2, width * 2);
            graphics.DrawEllipse(pen, rect);
        }

        public static void FillEllipseCenter(this Graphics graphics, SolidBrush solidBrush1, Point Center, int width)
        {
            var rect = new Rectangle(Center.X - width, Center.Y - width, width * 2, width * 2);
            graphics.FillEllipse(solidBrush1, rect);
        }

        public static void DrawRectangleCenter(this Graphics graphics, Pen pen, Point Center, int width)
        {
            var rect = new Rectangle(Center.X - width, Center.Y - width, width * 2, width * 2);
            graphics.DrawRectangle(pen, rect);
        }

        public static void DrawLineCenter(this Graphics graphics, Pen pen, Point Center, int width)
        {
            graphics.DrawLine(pen, new Point(Center.X, Center.Y - width), new Point(Center.X, Center.Y + width));
        }

        public static void FillRectangleCenter(this Graphics graphics, SolidBrush solidBrush1, Point Center, int width)
        {
            var rect = new Rectangle(Center.X - width, Center.Y - width, width * 2, width * 2);
            graphics.FillRectangle(solidBrush1, rect);
        }

        public static Image CreateEllipse(Color color, bool Isdot = false, bool Ischeck = false)
        {
            var rect = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var brush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
                {
                    using (var pen = new Pen(color, 1.DPIValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighQuality;
                        if (Isdot)
                            pen.DashPattern = new float[2]
                            {
                                3f,
                                3f
                            };
                        if (Ischeck) graphics.FillEllipse(brush, rect);
                        graphics.DrawEllipse(pen, rect);
                    }
                }
            }

            return bitmap;
        }

        public static Image CreateRectangle(Color color, bool Isdot = false, bool Ischeck = false)
        {
            var rect = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var brush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
                {
                    using (var pen = new Pen(color, 1.DPIValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighSpeed;
                        if (Isdot)
                            pen.DashPattern = new float[2]
                            {
                                3f,
                                3f
                            };
                        if (Ischeck) graphics.FillRectangle(brush, rect);
                        graphics.DrawRectangle(pen, rect);
                    }
                }
            }

            return bitmap;
        }

        public static Image CreateHighlight(Color color)
        {
            var rect = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var brush = new SolidBrush(Color.FromArgb(40, color)))
                {
                    using (var pen = new Pen(color, 1.DPIValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighSpeed;
                        graphics.FillRectangle(brush, rect);
                        graphics.DrawRectangle(pen, rect);
                        var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                        graphics.DrawTextCenter(pen, center, 6.DPIValue(), color);
                    }
                }
            }

            return bitmap;
        }

        public static Image CreateRectangleFill(Color color)
        {
            var rect = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var brush = new SolidBrush(Color.FromArgb(40, color)))
                {
                    using (var pen = new Pen(color, 1.DPIValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighSpeed;
                        graphics.FillRectangle(brush, rect);
                        graphics.DrawRectangle(pen, rect);
                    }
                }
            }

            return bitmap;
        }

        public static Image CreateRectangleImage(Color color, int width, int height, int left, int top)
        {
            var bitmap = new Bitmap(width.DPIValue(), height.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var brush = new SolidBrush(color))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    graphics.FillRectangle(brush,
                        new RectangleF(new Point(left, top),
                            new Size(bitmap.Width - 2 * left, bitmap.Height - 2 * top)));
                }
            }

            return bitmap;
        }

        public static void DrawLineEx(this Graphics graphics, Pen pen, Point point1, Point point2)
        {
            var points = new Point[2]
            {
                point1,
                point2
            };
            graphics.DrawLines(pen, points);
        }

        public static Image CreateTextDot(bool Ischeck = false)
        {
            var rectangle = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                var color = Color.FromArgb(150, CustomColor.UnCheckColor);
                if (Ischeck) color = CustomColor.CheckColor;
                using (var pen = new Pen(color, 1.DPIValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    graphics.DrawPolygon(pen, new Point[9]
                    {
                        new Point(rectangle.X, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y + 1.DPIValue() + 1.DPIValue()),
                        new Point(rectangle.X + rectangle.Width / 2 + 1.DPIValue(),
                            rectangle.Y + 1.DPIValue() + 1.DPIValue()),
                        new Point(rectangle.X + rectangle.Width / 2 + 1.DPIValue(), rectangle.Y + rectangle.Height),
                        new Point(rectangle.X + rectangle.Width / 2 - 1.DPIValue(), rectangle.Y + rectangle.Height),
                        new Point(rectangle.X + rectangle.Width / 2 - 1.DPIValue(),
                            rectangle.Y + 1.DPIValue() + 1.DPIValue()),
                        new Point(rectangle.X, rectangle.Y + 1.DPIValue() + 1.DPIValue()),
                        new Point(rectangle.X, rectangle.Y)
                    });
                }
            }

            return bitmap;
        }

        public static Image CreateCopy(Color color)
        {
            var rectangle = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DPIValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    var rect = new Rectangle(rectangle.X + rectangle.Width / 5, rectangle.Y + rectangle.Width / 5,
                        rectangle.Height * 4 / 5, rectangle.Height * 4 / 5);
                    graphics.DrawLines(pen, new Point[5]
                    {
                        new Point(rectangle.X + rectangle.Width / 5, rectangle.Y + rectangle.Width * 4 / 5),
                        new Point(rectangle.X, rectangle.Y + rectangle.Width * 4 / 5),
                        new Point(rectangle.X, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width * 4 / 5, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width * 4 / 5, rectangle.Y + rectangle.Width / 5)
                    });
                    graphics.DrawRectangle(pen, rect);
                }
            }

            return bitmap;
        }

        public static Image CreatePaste(Color color)
        {
            var rectangle = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DPIValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    var rect = new Rectangle(rectangle.X + rectangle.Width / 5, rectangle.Y + rectangle.Width / 5,
                        rectangle.Height * 4 / 5, rectangle.Height * 4 / 5);
                    graphics.DrawLines(pen, new Point[5]
                    {
                        new Point(rectangle.X + rectangle.Width / 5, rectangle.Y + rectangle.Width * 4 / 5),
                        new Point(rectangle.X, rectangle.Y + rectangle.Width * 4 / 5),
                        new Point(rectangle.X, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width * 4 / 5, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width * 4 / 5, rectangle.Y + rectangle.Width / 5)
                    });
                    graphics.DrawRectangle(pen, rect);
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.DrawLine(pen, new Point(rect.X + rect.Height / 4, rect.Y + rect.Height - rect.Height / 4),
                        new Point(rect.X + rect.Width - rect.Height / 4, rect.Y + rect.Height / 4));
                    var rectangle2 = new Rectangle(rect.X, rect.Y, rect.Width / 2, rect.Height / 2);
                    var rectangle3 = new Rectangle(rect.X + rect.Width / 2, rect.Y + rect.Height / 2, rect.Width / 2,
                        rect.Height / 2);
                    graphics.DrawLine(pen,
                        new Point(rectangle2.X + rectangle2.Height / 3,
                            rectangle2.Y + rectangle2.Height - rectangle2.Height / 3),
                        new Point(rectangle2.X + rectangle2.Width - rectangle2.Height / 3,
                            rectangle2.Y + rectangle2.Height / 3));
                    graphics.DrawLine(pen,
                        new Point(rectangle3.X + rectangle3.Height / 3,
                            rectangle3.Y + rectangle3.Height - rectangle3.Height / 3),
                        new Point(rectangle3.X + rectangle3.Width - rectangle3.Height / 3,
                            rectangle3.Y + rectangle3.Height / 3));
                }
            }

            return bitmap;
        }

        public static Image Createinput(Color color, Color Background)
        {
            var rectangle = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var brush = new SolidBrush(Background))
            {
                using (var graphics = Graphics.FromImage(bitmap))
                {
                    using (var pen = new Pen(color, 1.DPIValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighQuality;
                        graphics.FillRectangle(brush, -1, -1, bitmap.Size.Width + 1, bitmap.Size.Height + 1);
                        var rectangle2 = new Rectangle(rectangle.X + rectangle.Width / 2, rectangle.Y,
                            rectangle.Width / 2, rectangle.Height / 2);
                        graphics.DrawLine(pen, new Point(rectangle2.X, rectangle.Y + 1),
                            new Point(rectangle2.X, rectangle.Y + rectangle.Height - 2));
                        graphics.DrawLine(pen, new Point(rectangle2.X, rectangle.Y),
                            new Point(rectangle2.X + rectangle2.Width * 3 / 4, rectangle.Y + rectangle.Height / 2));
                        graphics.DrawLine(pen, new Point(rectangle2.X, rectangle.Y),
                            new Point(rectangle2.X - rectangle2.Width * 3 / 4, rectangle.Y + rectangle.Height / 2));
                        graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Height),
                            new Point(rectangle.X + rectangle.Width, rectangle.Y + rectangle.Height));
                        graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Height),
                            new Point(rectangle.X, rectangle.Y + rectangle.Height - rectangle2.Width * 2 / 3));
                        graphics.DrawLine(pen, new Point(rectangle.X + rectangle.Width, rectangle.Y + rectangle.Height),
                            new Point(rectangle.X + rectangle.Width,
                                rectangle.Y + rectangle.Height - rectangle2.Width * 2 / 3));
                    }
                }
            }

            return bitmap;
        }

        public static Image CreateOCR(Color color)
        {
            var rectangle = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DPIValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    var rect = new Rectangle(rectangle.X, rectangle.Y, rectangle.Height * 2 / 3,
                        rectangle.Height * 2 / 3);
                    graphics.DrawEllipse(pen, rect);
                    graphics.DrawLine(pen,
                        new Point(rectangle.X + rectangle.Height * 2 / 3 - 1,
                            rectangle.Y + rectangle.Height * 2 / 3 - 1),
                        new Point(rectangle.X + rectangle.Height, rectangle.Y + rectangle.Height));
                }
            }

            return bitmap;
        }

        public static Image CreateMosaic(Color color)
        {
            var rect = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var brush = new SolidBrush(Color.FromArgb(100, color)))
                {
                    using (var brush2 = new SolidBrush(Color.FromArgb(40, color)))
                    {
                        using (var pen = new Pen(color, 1.DPIValue()))
                        {
                            graphics.SmoothingMode = SmoothingMode.HighSpeed;
                            graphics.FillRectangle(brush,
                                new Rectangle(rect.X, rect.Y, rect.Width / 2, rect.Height / 2));
                            graphics.FillRectangle(brush2,
                                new Rectangle(rect.X + rect.Width / 2, rect.Y + rect.Height / 2, rect.Width / 2,
                                    rect.Height / 2));
                            graphics.DrawRectangle(pen, rect);
                        }
                    }
                }
            }

            return bitmap;
        }

        public static Image CreateGaus(Color color)
        {
            var rect = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DPIValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    graphics.DrawRectangle(pen, rect);
                    graphics.DrawLine(pen, new Point(rect.X + rect.Height / 4, rect.Y + rect.Height - rect.Height / 4),
                        new Point(rect.X + rect.Width - rect.Height / 4, rect.Y + rect.Height / 4));
                    var rectangle = new Rectangle(rect.X, rect.Y, rect.Width / 2, rect.Height / 2);
                    var rectangle2 = new Rectangle(rect.X + rect.Width / 2, rect.Y + rect.Height / 2, rect.Width / 2,
                        rect.Height / 2);
                    graphics.DrawLine(pen,
                        new Point(rectangle.X + rectangle.Height / 3,
                            rectangle.Y + rectangle.Height - rectangle.Height / 3),
                        new Point(rectangle.X + rectangle.Width - rectangle.Height / 3,
                            rectangle.Y + rectangle.Height / 3));
                    graphics.DrawLine(pen,
                        new Point(rectangle2.X + rectangle2.Height / 3,
                            rectangle2.Y + rectangle2.Height - rectangle2.Height / 3),
                        new Point(rectangle2.X + rectangle2.Width - rectangle2.Height / 3,
                            rectangle2.Y + rectangle2.Height / 3));
                }
            }

            return bitmap;
        }

        public static Image CreateUndo(Color color)
        {
            var rectangle = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DPIValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.DrawLine(pen, new Point(rectangle.X + 1, rectangle.Y + rectangle.Height / 2),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y + rectangle.Height / 2));
                    var rectangle2 = new Rectangle(rectangle.X, rectangle.Y, rectangle.Width / 2, rectangle.Height / 2);
                    graphics.DrawLine(pen, new Point(rectangle2.X, rectangle2.Y + rectangle2.Height),
                        new Point(rectangle2.X + rectangle2.Width - rectangle2.Height / 3,
                            rectangle2.Y + rectangle2.Height / 3));
                    graphics.DrawLine(pen, new Point(rectangle2.X, rectangle2.Y + rectangle2.Height),
                        new Point(rectangle2.X + rectangle2.Width - rectangle2.Height / 3,
                            rectangle2.Y + rectangle.Height - rectangle2.Height / 3));
                    new Rectangle(rectangle.X, rectangle.Y + rectangle.Height / 2, rectangle.Width, rectangle.Height);
                }
            }

            return bitmap;
        }

        public static Image CreateRedo(Color color)
        {
            var rectangle = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DPIValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Height / 2),
                        new Point(rectangle.X + rectangle.Width - 2, rectangle.Y + rectangle.Height / 2));
                    var rectangle2 = new Rectangle(rectangle.X + rectangle.Width / 2, rectangle.Y, rectangle.Width / 2,
                        rectangle.Height / 2);
                    graphics.DrawLine(pen,
                        new Point(rectangle2.X + rectangle2.Height / 3, rectangle2.Y + rectangle2.Height / 3),
                        new Point(rectangle2.X + rectangle2.Width, rectangle2.Y + rectangle2.Width));
                    graphics.DrawLine(pen,
                        new Point(rectangle2.X + rectangle2.Height / 3,
                            rectangle.Y + rectangle.Height - rectangle2.Height / 3),
                        new Point(rectangle2.X + rectangle2.Width, rectangle2.Y + rectangle2.Width));
                    new Rectangle(rectangle.X, rectangle.Y + rectangle.Height / 2, rectangle.Width, rectangle.Height);
                }
            }

            return bitmap;
        }

        public static Image CreateSave(Color color)
        {
            var rectangle = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DPIValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    var points = new Point[6]
                    {
                        new Point(rectangle.X, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width * 3 / 4, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y + rectangle.Height / 4),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y + rectangle.Height),
                        new Point(rectangle.X, rectangle.Y + rectangle.Height),
                        new Point(rectangle.X, rectangle.Y)
                    };
                    graphics.DrawLines(pen, points);
                    graphics.DrawLine(pen,
                        new Point(rectangle.X + rectangle.Width / 4, rectangle.Y + rectangle.Height / 5),
                        new Point(rectangle.X + rectangle.Width * 3 / 4, rectangle.Y + rectangle.Height / 5));
                    var points2 = new Point[4]
                    {
                        new Point(rectangle.X + rectangle.Width / 5, rectangle.Y + rectangle.Height),
                        new Point(rectangle.X + rectangle.Width / 5, rectangle.Y + rectangle.Height / 2),
                        new Point(rectangle.X + rectangle.Width * 4 / 5, rectangle.Y + rectangle.Height / 2),
                        new Point(rectangle.X + rectangle.Width * 4 / 5, rectangle.Y + rectangle.Height)
                    };
                    graphics.DrawLines(pen, points2);
                }
            }

            return bitmap;
        }

        public static Image CreateClose(Color color)
        {
            var rectangle = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DPIValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.DrawLine(pen,
                        new Point(rectangle.X + rectangle.Height / 8, rectangle.Y + rectangle.Height / 8),
                        new Point(rectangle.X + rectangle.Width - rectangle.Height / 8,
                            rectangle.Y + rectangle.Height - rectangle.Height / 8));
                    graphics.DrawLine(pen,
                        new Point(rectangle.X + rectangle.Height / 8,
                            rectangle.Y + rectangle.Height - rectangle.Height / 8),
                        new Point(rectangle.X + rectangle.Width - rectangle.Height / 8,
                            rectangle.Y + rectangle.Height / 8));
                }
            }

            return bitmap;
        }

        public static Image CreatePolygon(Color color, bool Isdot = false, bool Ischeck = false)
        {
            if (color == Color.FromArgb(120, 120, 120)) color = Color.FromArgb(165, color);
            var rectangle = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen2 = new Pen(Color.White, 2.DPIValue()))
                {
                    using (var pen = new Pen(color, 2.DPIValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighQuality;
                        graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Width),
                            new Point(rectangle.X + rectangle.Width, rectangle.Y));
                        graphics.DrawLine(pen2, new Point(rectangle.X, rectangle.Y + rectangle.Height / 2),
                            new Point(rectangle.X + rectangle.Width,
                                rectangle.Y + rectangle.Height + rectangle.Height / 2));
                    }
                }

                return bitmap;
            }
        }

        public static Image CreateLine(Color color, bool Isdot = false, bool Ischeck = false)
        {
            var rectangle = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen2 = new Pen(Color.FromArgb(20, 0, 0, 0), 6.DPIValue()))
                {
                    using (var pen = new Pen(color, 1.DPIValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighQuality;
                        if (Isdot)
                            pen.DashPattern = new float[2]
                            {
                                3f,
                                3f
                            };
                        if (Ischeck)
                            graphics.DrawLine(pen2, new Point(rectangle.X, rectangle.Y + rectangle.Height),
                                new Point(rectangle.X + rectangle.Width, rectangle.Y));
                        graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Height),
                            new Point(rectangle.X + rectangle.Width, rectangle.Y));
                    }
                }

                return bitmap;
            }
        }

        public static Image CreateLineArrow(Color color, bool Isdot = false, bool Ischeck = false)
        {
            var rectangle = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen3 = new Pen(Color.FromArgb(152, 152, 152), 1.DPIValue()))
                {
                    using (var pen2 = new Pen(Color.FromArgb(20, 0, 0, 0), 6.DPIValue()))
                    {
                        using (var pen = new Pen(color, 1.DPIValue()))
                        {
                            graphics.SmoothingMode = SmoothingMode.HighQuality;
                            if (Isdot)
                                pen.DashPattern = new float[2]
                                {
                                    3f,
                                    3f
                                };
                            if (Ischeck)
                            {
                                graphics.DrawLine(pen2, new Point(rectangle.X, rectangle.Y + rectangle.Height),
                                    new Point(rectangle.X + rectangle.Width, rectangle.Y));
                                graphics.DrawLine(pen2, new Point(rectangle.X + rectangle.Width * 2 / 3, rectangle.Y),
                                    new Point(rectangle.X + rectangle.Width, rectangle.Y));
                                graphics.DrawLine(pen2, new Point(rectangle.X + rectangle.Width, rectangle.Y),
                                    new Point(rectangle.X + rectangle.Width, rectangle.Y + rectangle.Height / 3));
                                graphics.DrawLine(pen2, new Point(rectangle.X, rectangle.Y + rectangle.Height),
                                    new Point(rectangle.X + rectangle.Width / 3, rectangle.Y + rectangle.Height));
                                graphics.DrawLine(pen2, new Point(rectangle.X, rectangle.Y + rectangle.Height * 2 / 3),
                                    new Point(rectangle.X, rectangle.Y + rectangle.Height));
                            }

                            graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Height),
                                new Point(rectangle.X + rectangle.Width, rectangle.Y));
                            graphics.DrawLine(pen3, new Point(rectangle.X + rectangle.Width * 2 / 3, rectangle.Y),
                                new Point(rectangle.X + rectangle.Width, rectangle.Y));
                            graphics.DrawLine(pen3, new Point(rectangle.X + rectangle.Width, rectangle.Y),
                                new Point(rectangle.X + rectangle.Width, rectangle.Y + rectangle.Height / 3));
                            graphics.DrawLine(pen3, new Point(rectangle.X, rectangle.Y + rectangle.Height),
                                new Point(rectangle.X + rectangle.Width / 3, rectangle.Y + rectangle.Height));
                            graphics.DrawLine(pen3, new Point(rectangle.X, rectangle.Y + rectangle.Height * 2 / 3),
                                new Point(rectangle.X, rectangle.Y + rectangle.Height));
                        }
                    }
                }

                return bitmap;
            }
        }

        public static Image CreateArrow(Color color)
        {
            var rectangle = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DPIValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Height),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y));
                    graphics.DrawLine(pen, new Point(rectangle.X + rectangle.Width / 2, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y));
                    graphics.DrawLine(pen, new Point(rectangle.X + rectangle.Width, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y + rectangle.Height / 2));
                }
            }

            return bitmap;
        }

        public static Image CreateArrowBoth(bool Ischeck)
        {
            var rectangle = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            var color = Color.FromArgb(150, 120, 120, 120);
            if (Ischeck) color = CustomColor.CheckColor;
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DPIValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Height),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y));
                    graphics.DrawLine(pen, new Point(rectangle.X + rectangle.Width * 2 / 3, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y));
                    graphics.DrawLine(pen, new Point(rectangle.X + rectangle.Width, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y + rectangle.Height / 3));
                    graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Height * 2 / 3),
                        new Point(rectangle.X, rectangle.Y + rectangle.Height));
                    graphics.DrawLine(pen, new Point(rectangle.X + rectangle.Width / 3, rectangle.Y + rectangle.Height),
                        new Point(rectangle.X, rectangle.Y + rectangle.Height));
                }
            }

            return bitmap;
        }

        public static Image CreateText(Color color)
        {
            var rectangle = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DPIValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y));
                    graphics.DrawLine(pen, new Point(rectangle.X + rectangle.Width / 2, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width / 2, rectangle.Y + rectangle.Height));
                }
            }

            return bitmap;
        }

        public static Image EditRectangle(Color color, bool ischeck = false)
        {
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var solidBrush2 = new SolidBrush(Color.White))
                {
                    using (var pen = new Pen(Color.Black))
                    {
                        using (var solidBrush = new SolidBrush(color))
                        {
                            graphics.SmoothingMode = SmoothingMode.HighSpeed;
                            var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                            graphics.FillRectangleCenter(solidBrush, center, 8.DPIValue());
                            graphics.DrawRectangleCenter(pen, center, 8.DPIValue());
                            if (ischeck)
                            {
                                graphics.FillRectangleCenter(solidBrush2, center, 4.DPIValue());
                                graphics.DrawRectangleCenter(pen, center, 4.DPIValue());
                            }
                        }
                    }
                }
            }

            return bitmap;
        }

        public static Image EditCross(bool ischeck = false)
        {
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                var color = Color.FromArgb(150, CustomColor.UnCheckColor);
                if (ischeck) color = CustomColor.CheckColor;
                using (var pen = new Pen(color))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    var point = new Point(bitmap.Width / 2, bitmap.Height / 2);
                    var rectangle = new Rectangle(point.X - 8.DPIValue(), point.Y - 8.DPIValue(), 8.DPIValue() * 2,
                        8.DPIValue() * 2);
                    graphics.DrawPolygon(pen, new Point[13]
                    {
                        new Point(point.X - 1.DPIValue(), rectangle.Y),
                        new Point(point.X + 1.DPIValue(), rectangle.Y),
                        new Point(point.X + 1.DPIValue(), point.Y - 1.DPIValue()),
                        new Point(rectangle.X + rectangle.Width, point.Y - 1.DPIValue()),
                        new Point(rectangle.X + rectangle.Width, point.Y + 1.DPIValue()),
                        new Point(point.X + 1.DPIValue(), point.Y + 1.DPIValue()),
                        new Point(point.X + 1.DPIValue(), rectangle.Y + rectangle.Height),
                        new Point(point.X - 1.DPIValue(), rectangle.Y + rectangle.Height),
                        new Point(point.X - 1.DPIValue(), point.Y + 1.DPIValue()),
                        new Point(rectangle.X, point.Y + 1.DPIValue()),
                        new Point(rectangle.X, point.Y - 1.DPIValue()),
                        new Point(point.X - 1.DPIValue(), point.Y - 1.DPIValue()),
                        new Point(point.X - 1.DPIValue(), rectangle.Y)
                    });
                }
            }

            return bitmap;
        }

        public static Image EditRectangleCus(Color color, bool ischeck = false)
        {
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(Color.Black))
                {
                    using (var brush = new SolidBrush(Color.FromArgb(100, color)))
                    {
                        using (var brush2 = new SolidBrush(Color.FromArgb(40, color)))
                        {
                            graphics.SmoothingMode = SmoothingMode.HighSpeed;
                            var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                            var rectangle = new Rectangle(center.X - 8.DPIValue(), center.Y - 8.DPIValue(),
                                8.DPIValue() * 2, 8.DPIValue() * 2);
                            graphics.FillRectangle(brush,
                                new Rectangle(rectangle.X, rectangle.Y, rectangle.Width / 2, rectangle.Height / 2));
                            graphics.FillRectangle(brush2,
                                new Rectangle(rectangle.X + rectangle.Width / 2, rectangle.Y + rectangle.Height / 2,
                                    rectangle.Width / 2, rectangle.Height / 2));
                            graphics.DrawRectangleCenter(pen, center, 8.DPIValue());
                        }
                    }
                }
            }

            return bitmap;
        }

        public static Image EditRectangleTool(Color color, bool ischeck = false)
        {
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(Color.Black))
                {
                    using (var solidBrush2 = new SolidBrush(Color.White))
                    {
                        using (var solidBrush = new SolidBrush(color))
                        {
                            graphics.SmoothingMode = SmoothingMode.HighSpeed;
                            var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                            graphics.FillRectangleCenter(solidBrush, center, 8.DPIValue());
                            graphics.DrawRectangleCenter(pen, center, 8.DPIValue());
                            if (ischeck)
                            {
                                graphics.FillRectangleCenter(solidBrush2, center, 4.DPIValue());
                                graphics.DrawRectangleCenter(pen, center, 4.DPIValue());
                            }
                        }
                    }
                }
            }

            return bitmap;
        }

        public static Image CreateTextDotTool(bool Ischeck = false)
        {
            var rectangle = new Rectangle(8.DPIValue(), 8.DPIValue(), 15.DPIValue(), 15.DPIValue());
            var bitmap = new Bitmap(30.DPIValue(), 30.DPIValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                var color = Color.FromArgb(150, CustomColor.UnCheckColor);
                if (Ischeck) color = CustomColor.CheckColor;
                using (new Pen(Color.Silver, 1f))
                {
                    using (var pen = new Pen(color, 1.DPIValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighSpeed;
                        graphics.DrawPolygon(pen, new Point[9]
                        {
                            new Point(rectangle.X, rectangle.Y),
                            new Point(rectangle.X + rectangle.Width, rectangle.Y),
                            new Point(rectangle.X + rectangle.Width, rectangle.Y + 1.DPIValue() + 1.DPIValue()),
                            new Point(rectangle.X + rectangle.Width / 2 + 1.DPIValue(),
                                rectangle.Y + 1.DPIValue() + 1.DPIValue()),
                            new Point(rectangle.X + rectangle.Width / 2 + 1.DPIValue(), rectangle.Y + rectangle.Height),
                            new Point(rectangle.X + rectangle.Width / 2 - 1.DPIValue(), rectangle.Y + rectangle.Height),
                            new Point(rectangle.X + rectangle.Width / 2 - 1.DPIValue(),
                                rectangle.Y + 1.DPIValue() + 1.DPIValue()),
                            new Point(rectangle.X, rectangle.Y + 1.DPIValue() + 1.DPIValue()),
                            new Point(rectangle.X, rectangle.Y)
                        });
                    }
                }
            }

            return bitmap;
        }
    }
}