﻿using OCRTools.Common;
using System;
using System.Collections.Specialized;
using System.IO;

namespace ImageLib
{
    public class SouGouImageUpload
    {
        public static bool Enable { get; set; } = true;

        public static string GetResult(byte[] content)
        {
            var result = GetFromSouGou(content);
            if (!string.IsNullOrEmpty(result))
            {
                result += "?1.png";
            }
            return result;
        }

        private static string GetFromSouGou(byte[] content)
        {
            var result = "";
            var cookie = string.Empty;
            WebClientExt.GetHtml("https://pic.sogou.com", ref cookie, "", "", 1, 5);
            var url = "https://pic.sogou.com/pic/upload_pic.jsp?uuid=" + Guid.NewGuid().ToString().ToLower();
            var file = new UploadFileInfo()
            {
                Name = "pic_path",
                Filename = "test.png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            try
            {
                result = UploadFileRequest.Post(url, new[] { file }, null, new NameValueCollection { { "Cookie", cookie } });
            }
            catch { }
            return result;
        }

    }
}
