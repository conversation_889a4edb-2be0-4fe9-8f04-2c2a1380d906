﻿using System.Drawing;
using System.Reflection;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class ColorComboBox : ComboBox
    {
        /// <summary>
        /// 当前选中色
        /// </summary>
        public Color SelectedColor
        {
            get { return Color.FromName(Text); }
        }

        /// <summary>
        /// 构造函数，构造颜色下拉列表
        /// </summary>
        public ColorComboBox()
        {
            DrawMode = DrawMode.OwnerDrawFixed;
            DropDownStyle = ComboBoxStyle.DropDownList;
            ItemHeight = 25;
            var propInfoList = typeof(Color).GetProperties(BindingFlags.Static | BindingFlags.DeclaredOnly | BindingFlags.Public);
            foreach (PropertyInfo c in propInfoList)
            {
                Items.Add(c.Name);
            }
            Text = "Black"; //设置默认色
        }

        protected override void OnDrawItem(DrawItemEventArgs e)
        {
            Rectangle rect = e.Bounds;
            if (e.Index >= 0)
            {
                string colorName = Items[e.Index].ToString();
                Color c = Color.FromName(colorName);
                using (Brush b = new SolidBrush(c)) //预留下拉项间距
                {
                    e.Graphics.FillRectangle(b, rect.X, rect.Y + 2, rect.Width, rect.Height - 4);
                }
            }
        }
    }
}
