﻿using MetroFramework.Components;
using MetroFramework.Interfaces;
using System;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Windows.Forms;

namespace MetroFramework.Design
{
    internal class MetroStyleManagerDesigner : ComponentDesigner
    {
        private DesignerVerbCollection designerVerbs;

        private IDesignerHost designerHost;

        private IComponentChangeService componentChangeService;

        public override DesignerVerbCollection Verbs
        {
            get
            {
                if (designerVerbs != null)
                {
                    return designerVerbs;
                }
                designerVerbs = new DesignerVerbCollection();
                designerVerbs.Add(new DesignerVerb("Reset Styles to Default", OnResetStyles));
                return designerVerbs;
            }
        }

        public IDesignerHost DesignerHost
        {
            get
            {
                if (designerHost != null)
                {
                    return designerHost;
                }
                designerHost = (IDesignerHost)GetService(typeof(IDesignerHost));
                return designerHost;
            }
        }

        public IComponentChangeService ComponentChangeService
        {
            get
            {
                if (componentChangeService != null)
                {
                    return componentChangeService;
                }
                componentChangeService = (IComponentChangeService)GetService(typeof(IComponentChangeService));
                return componentChangeService;
            }
        }

        private void OnResetStyles(object sender, EventArgs args)
        {
            MetroStyleManager metroStyleManager = base.Component as MetroStyleManager;
            if (metroStyleManager != null && metroStyleManager.Owner == null)
            {
                MessageBox.Show("StyleManager needs the Owner property assigned to before it can reset styles.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Hand);
            }
            else
            {
                ResetStyles(metroStyleManager, metroStyleManager.Owner);
            }
        }

        private void ResetStyles(MetroStyleManager styleManager, Control control)
        {
            IMetroForm metroForm = control as IMetroForm;
            if (metroForm == null || object.ReferenceEquals(styleManager, metroForm.StyleManager))
            {
                if (control is IMetroControl)
                {
                    ResetProperty(control, "Style", MetroColorStyle.Blue);
                    ResetProperty(control, "Theme", MetroThemeStyle.Light);
                }
                else if (control is IMetroComponent)
                {
                    ResetProperty(control, "Style", MetroColorStyle.Blue);
                    ResetProperty(control, "Theme", MetroThemeStyle.Light);
                }
                if (control.ContextMenuStrip != null)
                {
                    ResetStyles(styleManager, control.ContextMenuStrip);
                }
                TabControl tabControl = control as TabControl;
                if (tabControl != null)
                {
                    foreach (TabPage tabPage in tabControl.TabPages)
                    {
                        ResetStyles(styleManager, tabPage);
                    }
                }
                if (control.Controls != null)
                {
                    foreach (Control control2 in control.Controls)
                    {
                        ResetStyles(styleManager, control2);
                    }
                }
            }
        }

        private void ResetProperty(Control control, string name, object newValue)
        {
            PropertyDescriptor propertyDescriptor = TypeDescriptor.GetProperties(control)[name];
            if (propertyDescriptor != null)
            {
                object value = propertyDescriptor.GetValue(control);
                if (!newValue.Equals(value))
                {
                    ComponentChangeService.OnComponentChanging(control, propertyDescriptor);
                    propertyDescriptor.SetValue(control, newValue);
                    ComponentChangeService.OnComponentChanged(control, propertyDescriptor, value, newValue);
                }
            }
        }
    }
}
