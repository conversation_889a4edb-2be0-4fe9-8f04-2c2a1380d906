﻿using System;
using System.Collections.Specialized;
using System.IO;

namespace OCRTools.Common.ImageLib
{
    public class JueJinImageUpload : BaseImageUpload
    {
        private const string strFileNameSpilt = "\"imgurl\":\"";

        public JueJinImageUpload()
        {
            ImageType = ImageTypeEnum.JueJin;
        }

        public override string GetResult(byte[] content, string ext)
        {
            var result = "";
            try
            {
                var url = "https://api.uomg.com/api/image.juejin";
                var file = new UploadFileInfo
                {
                    Name = "Filedata",
                    Filename = "test.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                var vaules = new NameValueCollection
                {
                    {"file", "multipart"}
                };
                var html = UploadFileRequest.Post(url, new[] {file}, vaules);
                //{
                //"ret": 0,
                //"msg": "success",
                //"fileName": "9be96996-9f80-4ed2-a59c-d513520178e0.jpg"}
                if (html.Contains(strFileNameSpilt))
                {
                    result = html.Substring(html.IndexOf(strFileNameSpilt) + strFileNameSpilt.Length);
                    result = result.Substring(0, result.IndexOf("\"")).Replace("\\/", "/");
                }
            }
            catch (Exception)
            {
            }

            return result;
        }
    }
}