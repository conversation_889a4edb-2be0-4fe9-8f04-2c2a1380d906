﻿using System;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;

namespace OCRTools.Common
{
    public static class FlashWindowHelper
    {
        public static void Flash(int times, int millliseconds, IntPtr handle)
        {
            Task.Factory.StartNew(() =>
            {
                try
                {
                    for (var i = 0; i < times; i++)
                    {
                        Win32.FlashWindow(handle, i % 2 == 0);
                        Thread.Sleep(millliseconds);
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
            });
        }
    }

    public static class Win32
    {
        [DllImport("user32.dll")]
        public static extern bool FlashWindow(IntPtr hwnd, bool bInvert);
    }
}