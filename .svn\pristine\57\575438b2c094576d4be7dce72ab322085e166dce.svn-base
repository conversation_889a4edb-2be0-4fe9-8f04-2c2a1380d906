﻿using ImageLib;
using nQuant;
using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;

namespace OCRTools
{
    public class ImageCompress
    {
        internal static string CompressImageFile(string fileName, string strPath, CompressType? type)
        {
            var byts = File.ReadAllBytes(fileName);
            var result = type.HasValue ? CompressImage(byts, type.Value) : GetCompressResult(byts, true);
            if (result.Result == null || result.Result.Length <= 0) return string.Empty;

            var filePath = Path.Combine(strPath,
                Path.GetFileNameWithoutExtension(fileName) + "-" +
                ServerTime.DateTime.Millisecond + Path.GetExtension(fileName));
            File.WriteAllBytes(filePath, result.Result);
            return filePath;
        }

        private static List<CompressType> lstType = new List<CompressType>() { CompressType.助手压缩, CompressType.TinyPng, CompressType.Tencent, CompressType.WebResizer };
        private const int maxTimeOut = 30000;

        internal static CompressResult GetCompressResult(byte[] bytes, bool isContainsLocal = false)
        {
            CompressResult result = null;
            try
            {
                var lstParam = isContainsLocal ?
                    lstType.Select(type => new TaskParam() { Param2 = bytes, Param3 = type }).ToList()
                    : lstType.Where(p => !Equals(p, CompressType.助手压缩)).Select(type => new TaskParam() { Param2 = bytes, Param3 = type }).ToList();
                if (lstParam.Count > 0)
                    result = CommonTask<CompressResult>.GetFastestValidResult(lstParam, GetValidateResultByType, 3, maxTimeOut, IsValidateResult);
            }
            catch (Exception oe)
            {
                Log.WriteError("GetCompressResult", oe);
            }
            return result;
        }

        private static CompressResult GetValidateResultByType(TaskParam param)
        {
            return CompressImage((byte[])param.Param2, (CompressType)param.Param3);
        }

        private static bool IsValidateResult(CompressResult result)
        {
            return result != null && result.Result != null && result.Result.Length > 0;
        }

        internal static CompressResult CompressImage(byte[] byts, CompressType type)
        {
            CompressResult result = new CompressResult() { Type = type };
            try
            {
                var strUrl = string.Empty;
                switch (type)
                {
                    case CompressType.TinyPng:
                        result.Result = ImageLib.TinyPngUpload.GetZipResult(byts, ref strUrl);
                        break;
                    case CompressType.WebResizer:
                        result.Result = WebResizerUpload.GetZipResult(byts, ref strUrl);
                        break;
                    case CompressType.Tencent:
                        result.Result = TencentUpload.GetZipResult(byts, ref strUrl);
                        break;
                    default:
                        using (var stream = new MemoryStream(byts))
                        {
                            using (var tmp = new Bitmap(stream))
                            {
                                using (var resultImg = WuQuantizer.QuantizeImage(tmp))
                                {
                                    if (resultImg != null)
                                        result.Result = ImageProcessHelper.ImageToByte(resultImg);
                                }
                            }
                        }
                        break;
                }
                result.Url = strUrl;
            }
            catch (Exception oe)
            {
                //Log.WriteError("CompressImage", oe);
            }
            return result;
        }

        internal class CompressResult
        {
            public CompressType Type { get; set; }

            public byte[] Result { get; set; }

            public string Url { get; set; }
        }
    }

    public enum CompressType
    {
        TinyPng,
        WebResizer,
        Tencent,
        //ImgTop,
        助手压缩
        //Pnn压缩
    }
}
