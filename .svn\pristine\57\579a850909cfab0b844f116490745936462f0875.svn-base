﻿using MetroFramework.Components;
using MetroFramework.Controls;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using MetroFramework.Native;
/**
 * MetroFramework - Modern UI for WinForms
 * 
 * The MIT License (MIT)
 * Copyright (c) 2011 <PERSON>, http://github.com/viperneo
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of 
 * this software and associated documentation files (the "Software"), to deal in the 
 * Software without restriction, including without limitation the rights to use, copy, 
 * modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, 
 * and to permit persons to whom the Software is furnished to do so, subject to the 
 * following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in 
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
 * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT 
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF 
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE 
 * OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Security;
using System.Windows.Forms;

namespace MetroFramework.Forms
{
    public class MetroForm : Form, IDisposable, IMetroForm
    {
        private enum WindowButtons
        {
            Minimize,
            Maximize,
            Close
        }

        private class MetroFormButton : Button, IMetroControl
        {
            private MetroColorStyle metroStyle;

            private MetroThemeStyle metroTheme;
            private bool isHovered;

            private bool isPressed;

            [Category("Metro Appearance")]
            [DefaultValue(MetroColorStyle.Blue)]
            public MetroColorStyle Style
            {
                get
                {
                    if (base.DesignMode || metroStyle != 0)
                    {
                        return metroStyle;
                    }
                    if (StyleManager != null)
                    {
                        return StyleManager.Style;
                    }
                    if (StyleManager == null)
                    {
                        return MetroColorStyle.Blue;
                    }
                    return metroStyle;
                }
                set
                {
                    metroStyle = value;
                }
            }

            [Category("Metro Appearance")]
            [DefaultValue(MetroThemeStyle.Light)]
            public MetroThemeStyle Theme
            {
                get
                {
                    if (base.DesignMode || metroTheme != 0)
                    {
                        return metroTheme;
                    }
                    if (StyleManager != null)
                    {
                        return StyleManager.Theme;
                    }
                    if (StyleManager == null)
                    {
                        return MetroThemeStyle.Light;
                    }
                    return metroTheme;
                }
                set
                {
                    metroTheme = value;
                }
            }

            [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
            [Browsable(false)]
            public MetroStyleManager StyleManager { get; set; }

            [DefaultValue(false)]
            [Category("Metro Appearance")]
            public bool UseCustomBackColor { get; set; }

            [DefaultValue(false)]
            [Category("Metro Appearance")]
            public bool UseCustomForeColor { get; set; }

            [DefaultValue(false)]
            [Category("Metro Appearance")]
            public bool UseStyleColors { get; set; }

            [Browsable(false)]
            [Category("Metro Behaviour")]
            [DefaultValue(false)]
            public bool UseSelectable
            {
                get
                {
                    return GetStyle(ControlStyles.Selectable);
                }
                set
                {
                    SetStyle(ControlStyles.Selectable, value);
                }
            }

            [Category("Metro Appearance")]
            public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

            [Category("Metro Appearance")]
            public event EventHandler<MetroPaintEventArgs> CustomPaint;

            [Category("Metro Appearance")]
            public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

            protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
            {
                if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintBackground != null)
                {
                    this.CustomPaintBackground(this, e);
                }
            }

            protected virtual void OnCustomPaint(MetroPaintEventArgs e)
            {
                if (GetStyle(ControlStyles.UserPaint) && this.CustomPaint != null)
                {
                    this.CustomPaint(this, e);
                }
            }

            protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
            {
                if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintForeground != null)
                {
                    this.CustomPaintForeground(this, e);
                }
            }

            public MetroFormButton()
            {
                SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.AllPaintingInWmPaint | ControlStyles.OptimizedDoubleBuffer, value: true);
            }

            protected override void OnPaint(PaintEventArgs e)
            {
                MetroThemeStyle theme = Theme;
                Color color;
                if (base.Parent != null)
                {
                    if (!(base.Parent is IMetroForm))
                    {
                        color = ((!(base.Parent is IMetroControl)) ? base.Parent.BackColor : MetroPaint.GetStyleColor(Style));
                    }
                    else
                    {
                        theme = ((IMetroForm)base.Parent).Theme;
                        color = MetroPaint.BackColor.Form(theme);
                    }
                }
                else
                {
                    color = MetroPaint.BackColor.Form(theme);
                }
                Color foreColor;
                if (isHovered && !isPressed && base.Enabled)
                {
                    foreColor = MetroPaint.ForeColor.Button.Normal(theme);
                    color = MetroPaint.BackColor.Button.Normal(theme);
                }
                else if (isHovered && isPressed && base.Enabled)
                {
                    foreColor = MetroPaint.ForeColor.Button.Press(theme);
                    color = MetroPaint.GetStyleColor(Style);
                }
                else if (!base.Enabled)
                {
                    foreColor = MetroPaint.ForeColor.Button.Disabled(theme);
                    color = MetroPaint.BackColor.Button.Disabled(theme);
                }
                else
                {
                    foreColor = MetroPaint.ForeColor.Button.Normal(theme);
                }
                e.Graphics.Clear(color);
                Font font = new Font("Webdings", 9.25f);
                TextRenderer.DrawText(e.Graphics, Text, font, base.ClientRectangle, foreColor, color, TextFormatFlags.EndEllipsis | TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
            }

            protected override void OnMouseEnter(EventArgs e)
            {
                isHovered = true;
                Invalidate();
                base.OnMouseEnter(e);
            }

            protected override void OnMouseDown(MouseEventArgs e)
            {
                if (e.Button == MouseButtons.Left)
                {
                    isPressed = true;
                    Invalidate();
                }
                base.OnMouseDown(e);
            }

            protected override void OnMouseUp(MouseEventArgs e)
            {
                isPressed = false;
                Invalidate();
                base.OnMouseUp(e);
            }

            protected override void OnMouseLeave(EventArgs e)
            {
                isHovered = false;
                Invalidate();
                base.OnMouseLeave(e);
            }
        }

        protected abstract class MetroShadowBase : Form
        {
            protected const int WS_EX_TRANSPARENT = 32;

            protected const int WS_EX_LAYERED = 524288;

            protected const int WS_EX_NOACTIVATE = 134217728;

            private const int TICKS_PER_MS = 10000;

            private const long RESIZE_REDRAW_INTERVAL = 10000000L;

            private readonly int shadowSize;

            private readonly int wsExStyle;

            private bool isBringingToFront;

            private long lastResizedOn;

            protected Form TargetForm
            {
                get;
                private set;
            }

            protected override CreateParams CreateParams
            {
                get
                {
                    CreateParams createParams = base.CreateParams;
                    createParams.ExStyle |= wsExStyle;
                    return createParams;
                }
            }

            private bool IsResizing => lastResizedOn > 0;

            protected MetroShadowBase(Form targetForm, int shadowSize, int wsExStyle)
            {
                TargetForm = targetForm;
                this.shadowSize = shadowSize;
                this.wsExStyle = wsExStyle;
                TargetForm.Activated += OnTargetFormActivated;
                TargetForm.ResizeBegin += OnTargetFormResizeBegin;
                TargetForm.ResizeEnd += OnTargetFormResizeEnd;
                TargetForm.VisibleChanged += OnTargetFormVisibleChanged;
                TargetForm.SizeChanged += OnTargetFormSizeChanged;
                TargetForm.Move += OnTargetFormMove;
                TargetForm.Resize += OnTargetFormResize;
                if (TargetForm.Owner != null)
                {
                    base.Owner = TargetForm.Owner;
                }
                TargetForm.Owner = this;
                base.MaximizeBox = false;
                base.MinimizeBox = false;
                base.ShowInTaskbar = false;
                base.ShowIcon = false;
                base.FormBorderStyle = FormBorderStyle.None;
                base.Bounds = GetShadowBounds();
            }

            private Rectangle GetShadowBounds()
            {
                Rectangle bounds = TargetForm.Bounds;
                bounds.Inflate(shadowSize, shadowSize);
                return bounds;
            }

            protected abstract void PaintShadow();

            protected abstract void ClearShadow();

            protected override void OnDeactivate(EventArgs e)
            {
                base.OnDeactivate(e);
                isBringingToFront = true;
            }

            private void OnTargetFormActivated(object sender, EventArgs e)
            {
                if (base.Visible)
                {
                    Update();
                }
                if (isBringingToFront)
                {
                    base.Visible = true;
                    isBringingToFront = false;
                }
                else
                {
                    BringToFront();
                }
            }

            private void OnTargetFormVisibleChanged(object sender, EventArgs e)
            {
                base.Visible = (TargetForm.Visible && TargetForm.WindowState != FormWindowState.Minimized);
                Update();
            }

            private void OnTargetFormResizeBegin(object sender, EventArgs e)
            {
                lastResizedOn = DateTime.Now.Ticks;
            }

            private void OnTargetFormMove(object sender, EventArgs e)
            {
                if (!TargetForm.Visible || TargetForm.WindowState != 0)
                {
                    base.Visible = false;
                }
                else
                {
                    base.Bounds = GetShadowBounds();
                }
            }

            private void OnTargetFormResize(object sender, EventArgs e)
            {
                ClearShadow();
            }

            private void OnTargetFormSizeChanged(object sender, EventArgs e)
            {
                base.Bounds = GetShadowBounds();
                if (!IsResizing)
                {
                    PaintShadowIfVisible();
                }
            }

            private void OnTargetFormResizeEnd(object sender, EventArgs e)
            {
                lastResizedOn = 0L;
                PaintShadowIfVisible();
            }

            private void PaintShadowIfVisible()
            {
                if (TargetForm.Visible && TargetForm.WindowState != FormWindowState.Minimized)
                {
                    PaintShadow();
                }
            }
        }

        protected class MetroAeroDropShadow : MetroShadowBase
        {
            public MetroAeroDropShadow(Form targetForm)
                : base(targetForm, 0, 134217760)
            {
                base.FormBorderStyle = FormBorderStyle.SizableToolWindow;
            }

            protected override void SetBoundsCore(int x, int y, int width, int height, BoundsSpecified specified)
            {
                if (specified != BoundsSpecified.Size)
                {
                    base.SetBoundsCore(x, y, width, height, specified);
                }
            }

            protected override void PaintShadow()
            {
                base.Visible = true;
            }

            protected override void ClearShadow()
            {
            }
        }

        protected class MetroFlatDropShadow : MetroShadowBase
        {
            private Point Offset = new Point(-6, -6);

            public MetroFlatDropShadow(Form targetForm)
                : base(targetForm, 6, 134742048)
            {
            }

            protected override void OnLoad(EventArgs e)
            {
                base.OnLoad(e);
                PaintShadow();
            }

            protected override void OnPaint(PaintEventArgs e)
            {
                base.Visible = true;
                PaintShadow();
            }

            protected override void PaintShadow()
            {
                using (Bitmap bitmap = DrawBlurBorder())
                {
                    SetBitmap(bitmap, byte.MaxValue);
                }
            }

            protected override void ClearShadow()
            {
                Bitmap bitmap = new Bitmap(base.Width, base.Height, PixelFormat.Format32bppArgb);
                Graphics graphics = Graphics.FromImage(bitmap);
                graphics.Clear(Color.Transparent);
                graphics.Flush();
                graphics.Dispose();
                SetBitmap(bitmap, byte.MaxValue);
                bitmap.Dispose();
            }

            [SecuritySafeCritical]
            private void SetBitmap(Bitmap bitmap, byte opacity)
            {
                if (bitmap.PixelFormat != PixelFormat.Format32bppArgb)
                {
                    throw new ApplicationException("The bitmap must be 32ppp with alpha-channel.");
                }
                IntPtr dC = WinApi.GetDC(IntPtr.Zero);
                IntPtr intPtr = WinApi.CreateCompatibleDC(dC);
                IntPtr intPtr2 = IntPtr.Zero;
                IntPtr hObject = IntPtr.Zero;
                try
                {
                    intPtr2 = bitmap.GetHbitmap(Color.FromArgb(0));
                    hObject = WinApi.SelectObject(intPtr, intPtr2);
                    WinApi.SIZE psize = new WinApi.SIZE(bitmap.Width, bitmap.Height);
                    WinApi.POINT pprSrc = new WinApi.POINT(0, 0);
                    WinApi.POINT pptDst = new WinApi.POINT(base.Left, base.Top);
                    WinApi.BLENDFUNCTION pblend = default(WinApi.BLENDFUNCTION);
                    pblend.BlendOp = 0;
                    pblend.BlendFlags = 0;
                    pblend.SourceConstantAlpha = opacity;
                    pblend.AlphaFormat = 1;
                    WinApi.UpdateLayeredWindow(base.Handle, dC, ref pptDst, ref psize, intPtr, ref pprSrc, 0, ref pblend, 2);
                }
                finally
                {
                    WinApi.ReleaseDC(IntPtr.Zero, dC);
                    if (intPtr2 != IntPtr.Zero)
                    {
                        WinApi.SelectObject(intPtr, hObject);
                        WinApi.DeleteObject(intPtr2);
                    }
                    WinApi.DeleteDC(intPtr);
                }
            }

            private Bitmap DrawBlurBorder()
            {
                return (Bitmap)DrawOutsetShadow(Color.Black, new Rectangle(0, 0, base.ClientRectangle.Width, base.ClientRectangle.Height));
            }

            private Image DrawOutsetShadow(Color color, Rectangle shadowCanvasArea)
            {
                Rectangle rect = shadowCanvasArea;
                Rectangle rect2 = new Rectangle(shadowCanvasArea.X + (-Offset.X - 1), shadowCanvasArea.Y + (-Offset.Y - 1), shadowCanvasArea.Width - (-Offset.X * 2 - 1), shadowCanvasArea.Height - (-Offset.Y * 2 - 1));
                Bitmap bitmap = new Bitmap(rect.Width, rect.Height, PixelFormat.Format32bppArgb);
                Graphics graphics = Graphics.FromImage(bitmap);
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                using (Brush brush = new SolidBrush(Color.FromArgb(30, Color.Black)))
                {
                    graphics.FillRectangle(brush, rect);
                }
                using (Brush brush2 = new SolidBrush(Color.FromArgb(60, Color.Black)))
                {
                    graphics.FillRectangle(brush2, rect2);
                }
                graphics.Flush();
                graphics.Dispose();
                return bitmap;
            }
        }

        protected class MetroRealisticDropShadow : MetroShadowBase
        {
            public MetroRealisticDropShadow(Form targetForm)
                : base(targetForm, 15, 134742048)
            {
            }

            protected override void OnLoad(EventArgs e)
            {
                base.OnLoad(e);
                PaintShadow();
            }

            protected override void OnPaint(PaintEventArgs e)
            {
                base.Visible = true;
                PaintShadow();
            }

            protected override void PaintShadow()
            {
                using (Bitmap bitmap = DrawBlurBorder())
                {
                    SetBitmap(bitmap, byte.MaxValue);
                }
            }

            protected override void ClearShadow()
            {
                Bitmap bitmap = new Bitmap(base.Width, base.Height, PixelFormat.Format32bppArgb);
                Graphics graphics = Graphics.FromImage(bitmap);
                graphics.Clear(Color.Transparent);
                graphics.Flush();
                graphics.Dispose();
                SetBitmap(bitmap, byte.MaxValue);
                bitmap.Dispose();
            }

            [SecuritySafeCritical]
            private void SetBitmap(Bitmap bitmap, byte opacity)
            {
                if (bitmap.PixelFormat != PixelFormat.Format32bppArgb)
                {
                    throw new ApplicationException("The bitmap must be 32ppp with alpha-channel.");
                }
                IntPtr dC = WinApi.GetDC(IntPtr.Zero);
                IntPtr intPtr = WinApi.CreateCompatibleDC(dC);
                IntPtr intPtr2 = IntPtr.Zero;
                IntPtr hObject = IntPtr.Zero;
                try
                {
                    intPtr2 = bitmap.GetHbitmap(Color.FromArgb(0));
                    hObject = WinApi.SelectObject(intPtr, intPtr2);
                    WinApi.SIZE psize = new WinApi.SIZE(bitmap.Width, bitmap.Height);
                    WinApi.POINT pprSrc = new WinApi.POINT(0, 0);
                    WinApi.POINT pptDst = new WinApi.POINT(base.Left, base.Top);
                    WinApi.BLENDFUNCTION bLENDFUNCTION = default(WinApi.BLENDFUNCTION);
                    bLENDFUNCTION.BlendOp = 0;
                    bLENDFUNCTION.BlendFlags = 0;
                    bLENDFUNCTION.SourceConstantAlpha = opacity;
                    bLENDFUNCTION.AlphaFormat = 1;
                    WinApi.BLENDFUNCTION pblend = bLENDFUNCTION;
                    WinApi.UpdateLayeredWindow(base.Handle, dC, ref pptDst, ref psize, intPtr, ref pprSrc, 0, ref pblend, 2);
                }
                finally
                {
                    WinApi.ReleaseDC(IntPtr.Zero, dC);
                    if (intPtr2 != IntPtr.Zero)
                    {
                        WinApi.SelectObject(intPtr, hObject);
                        WinApi.DeleteObject(intPtr2);
                    }
                    WinApi.DeleteDC(intPtr);
                }
            }

            private Bitmap DrawBlurBorder()
            {
                return (Bitmap)DrawOutsetShadow(0, 0, 40, 1, Color.Black, new Rectangle(1, 1, base.ClientRectangle.Width, base.ClientRectangle.Height));
            }

            private Image DrawOutsetShadow(int hShadow, int vShadow, int blur, int spread, Color color, Rectangle shadowCanvasArea)
            {
                Rectangle rectangle = shadowCanvasArea;
                Rectangle rectangle2 = shadowCanvasArea;
                rectangle2.Offset(hShadow, vShadow);
                rectangle2.Inflate(-blur, -blur);
                rectangle.Inflate(spread, spread);
                rectangle.Offset(hShadow, vShadow);
                Rectangle rectangle3 = rectangle;
                Bitmap bitmap = new Bitmap(rectangle3.Width, rectangle3.Height, PixelFormat.Format32bppArgb);
                Graphics graphics = Graphics.FromImage(bitmap);
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                int cornerRadius = 0;
                do
                {
                    double num = (rectangle.Height - rectangle2.Height) / (double)(blur * 2 + spread * 2);
                    Color fillColor = Color.FromArgb((int)(200.0 * (num * num)), color);
                    Rectangle bounds = rectangle2;
                    bounds.Offset(-rectangle3.Left, -rectangle3.Top);
                    DrawRoundedRectangle(graphics, bounds, cornerRadius, Pens.Transparent, fillColor);
                    rectangle2.Inflate(1, 1);
                    cornerRadius = (int)(blur * (1.0 - num * num));
                }
                while (rectangle.Contains(rectangle2));
                graphics.Flush();
                graphics.Dispose();
                return bitmap;
            }

            private void DrawRoundedRectangle(Graphics g, Rectangle bounds, int cornerRadius, Pen drawPen, Color fillColor)
            {
                int num = Convert.ToInt32(Math.Ceiling(drawPen.Width));
                bounds = Rectangle.Inflate(bounds, -num, -num);
                GraphicsPath graphicsPath = new GraphicsPath();
                if (cornerRadius > 0)
                {
                    graphicsPath.AddArc(bounds.X, bounds.Y, cornerRadius, cornerRadius, 180f, 90f);
                    graphicsPath.AddArc(bounds.X + bounds.Width - cornerRadius, bounds.Y, cornerRadius, cornerRadius, 270f, 90f);
                    graphicsPath.AddArc(bounds.X + bounds.Width - cornerRadius, bounds.Y + bounds.Height - cornerRadius, cornerRadius, cornerRadius, 0f, 90f);
                    graphicsPath.AddArc(bounds.X, bounds.Y + bounds.Height - cornerRadius, cornerRadius, cornerRadius, 90f, 90f);
                }
                else
                {
                    graphicsPath.AddRectangle(bounds);
                }
                graphicsPath.CloseAllFigures();
                if (cornerRadius > 5)
                {
                    using (SolidBrush brush = new SolidBrush(fillColor))
                    {
                        g.FillPath(brush, graphicsPath);
                    }
                }
                if (drawPen != Pens.Transparent)
                {
                    using (Pen pen = new Pen(drawPen.Color))
                    {
                        LineCap lineCap3 = pen.EndCap = (pen.StartCap = LineCap.Round);
                        g.DrawPath(pen, graphicsPath);
                    }
                }
            }
        }

        private const int borderWidth = 5;

        private const int CS_DROPSHADOW = 131072;

        private const int WS_MINIMIZEBOX = 131072;

        private MetroColorStyle metroStyle = MetroColorStyle.Blue;

        private MetroThemeStyle metroTheme = MetroThemeStyle.Light;

        private MetroStyleManager metroStyleManager;

        private MetroFormTextAlign textAlign;

        private MetroFormBorderStyle formBorderStyle;

        private bool isMovable = true;

        private bool displayHeader = true;

        private bool isResizable = true;

        private MetroFormShadowType shadowType = MetroFormShadowType.Flat;

        private Bitmap _image;

        private Image backImage;

        private Padding backImagePadding;

        private int backMaxSize;

        private BackLocation backLocation;

        private bool _imageinvert;

        private Dictionary<WindowButtons, MetroFormButton> windowButtonList;

        private Form shadowForm;

        public event EventHandler<EventArgs> ThemeChange;

        public event EventHandler<EventArgs> StyleChange;

        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                if (StyleManager != null)
                {
                    return StyleManager.Style;
                }
                return metroStyle;
            }
            set
            {
                metroStyle = value;
            }
        }

        [Category("Metro Appearance")]
        public MetroThemeStyle Theme
        {
            get
            {
                if (StyleManager != null)
                {
                    return StyleManager.Theme;
                }
                return metroTheme;
            }
            set
            {
                metroTheme = value;
            }
        }

        [Browsable(false)]
        public MetroStyleManager StyleManager
        {
            get
            {
                return metroStyleManager;
            }
            set
            {
                metroStyleManager = value;
            }
        }

        [Category("Metro Appearance")]
        [Browsable(true)]
        public MetroFormTextAlign TextAlign
        {
            get
            {
                return textAlign;
            }
            set
            {
                textAlign = value;
            }
        }

        [Browsable(false)]
        public override Color BackColor => MetroPaint.BackColor.Form(Theme);

        [Browsable(true)]
        [Category("Metro Appearance")]
        [DefaultValue(MetroFormBorderStyle.None)]
        public MetroFormBorderStyle BorderStyle
        {
            get
            {
                return formBorderStyle;
            }
            set
            {
                formBorderStyle = value;
            }
        }

        [Category("Metro Appearance")]
        public bool Movable
        {
            get
            {
                return isMovable;
            }
            set
            {
                isMovable = value;
            }
        }

        public new Padding Padding
        {
            get
            {
                return base.Padding;
            }
            set
            {
                value.Top = Math.Max(value.Top, DisplayHeader ? 60 : 30);
                base.Padding = value;
            }
        }

        protected override Padding DefaultPadding => new Padding(20, DisplayHeader ? 60 : 20, 20, 20);

        [Category("Metro Appearance")]
        [DefaultValue(true)]
        public bool DisplayHeader
        {
            get
            {
                return displayHeader;
            }
            set
            {
                if (value != displayHeader)
                {
                    Padding padding = base.Padding;
                    padding.Top += (value ? 30 : (-30));
                    base.Padding = padding;
                }
                displayHeader = value;
            }
        }

        [Category("Metro Appearance")]
        public bool Resizable
        {
            get
            {
                return isResizable;
            }
            set
            {
                isResizable = value;
            }
        }

        [DefaultValue(MetroFormShadowType.Flat)]
        [Category("Metro Appearance")]
        public MetroFormShadowType ShadowType
        {
            get
            {
                if (!base.IsMdiChild)
                {
                    return shadowType;
                }
                return MetroFormShadowType.None;
            }
            set
            {
                shadowType = value;
            }
        }

        [Browsable(false)]
        public new FormBorderStyle FormBorderStyle
        {
            get
            {
                return base.FormBorderStyle;
            }
            set
            {
                base.FormBorderStyle = value;
            }
        }

        public new Form MdiParent
        {
            get
            {
                return base.MdiParent;
            }
            set
            {
                if (value != null)
                {
                    RemoveShadow();
                    shadowType = MetroFormShadowType.None;
                }
                base.MdiParent = value;
            }
        }

        [DefaultValue(null)]
        [Category("Metro Appearance")]
        public Image BackImage
        {
            get
            {
                return backImage;
            }
            set
            {
                backImage = value;
                if (value != null)
                {
                    _image = ApplyInvert(new Bitmap(value));
                }
                Refresh();
            }
        }

        [Category("Metro Appearance")]
        public Padding BackImagePadding
        {
            get
            {
                return backImagePadding;
            }
            set
            {
                backImagePadding = value;
                Refresh();
            }
        }

        [Category("Metro Appearance")]
        public int BackMaxSize
        {
            get
            {
                return backMaxSize;
            }
            set
            {
                backMaxSize = value;
                Refresh();
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(BackLocation.TopLeft)]
        public BackLocation BackLocation
        {
            get
            {
                return backLocation;
            }
            set
            {
                backLocation = value;
                Refresh();
            }
        }

        [DefaultValue(true)]
        [Category("Metro Appearance")]
        public bool ApplyImageInvert
        {
            get
            {
                return _imageinvert;
            }
            set
            {
                _imageinvert = value;
                Refresh();
            }
        }

        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams createParams = base.CreateParams;
                createParams.Style |= 131072;
                if (ShadowType == MetroFormShadowType.SystemShadow)
                {
                    createParams.ClassStyle |= 131072;
                }
                return createParams;
            }
        }

        public MetroForm()
        {
            SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.AllPaintingInWmPaint | ControlStyles.OptimizedDoubleBuffer, value: true);
            FormBorderStyle = FormBorderStyle.None;
            base.Name = "MetroForm";
            base.StartPosition = FormStartPosition.CenterScreen;
            base.TransparencyKey = Color.Lavender;
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                RemoveShadow();
            }
            base.Dispose(disposing);
        }

        public Bitmap ApplyInvert(Bitmap bitmapImage)
        {
            for (int i = 0; i < bitmapImage.Height; i++)
            {
                for (int j = 0; j < bitmapImage.Width; j++)
                {
                    Color pixel = bitmapImage.GetPixel(j, i);
                    _ = pixel.A;
                    byte b = (byte)(255 - pixel.R);
                    byte b2 = (byte)(255 - pixel.G);
                    byte b3 = (byte)(255 - pixel.B);
                    if (b <= 0)
                    {
                        b = 17;
                    }
                    if (b2 <= 0)
                    {
                        b2 = 17;
                    }
                    if (b3 <= 0)
                    {
                        b3 = 17;
                    }
                    bitmapImage.SetPixel(j, i, Color.FromArgb(b, b2, b3));
                }
            }
            return bitmapImage;
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            Color color = MetroPaint.BackColor.Form(Theme);
            Color foreColor = MetroPaint.ForeColor.Title(Theme);
            e.Graphics.Clear(color);
            using (SolidBrush brush = MetroPaint.GetStyleBrush(Style))
            {
                Rectangle rect = new Rectangle(0, 0, base.Width, 5);
                e.Graphics.FillRectangle(brush, rect);
            }
            if (BorderStyle != 0)
            {
                Color color2 = MetroPaint.BorderColor.Form(Theme);
                using (Pen pen = new Pen(color2))
                {
                    e.Graphics.DrawLines(pen, new Point[4]
                    {
                    new Point(0, 5),
                    new Point(0, base.Height - 1),
                    new Point(base.Width - 1, base.Height - 1),
                    new Point(base.Width - 1, 5)
                    });
                }
            }
            if (backImage != null && backMaxSize != 0)
            {
                Image image = MetroImage.ResizeImage(backImage, new Rectangle(0, 0, backMaxSize, backMaxSize));
                if (_imageinvert)
                {
                    image = MetroImage.ResizeImage((Theme == MetroThemeStyle.Dark) ? _image : backImage, new Rectangle(0, 0, backMaxSize, backMaxSize));
                }
                switch (backLocation)
                {
                    case BackLocation.TopLeft:
                        e.Graphics.DrawImage(image, backImagePadding.Left, backImagePadding.Top);
                        break;
                    case BackLocation.TopRight:
                        e.Graphics.DrawImage(image, base.ClientRectangle.Right - (backImagePadding.Right + image.Width), backImagePadding.Top);
                        break;
                    case BackLocation.BottomLeft:
                        e.Graphics.DrawImage(image, backImagePadding.Left, base.ClientRectangle.Bottom - (image.Height + backImagePadding.Bottom));
                        break;
                    case BackLocation.BottomRight:
                        e.Graphics.DrawImage(image, base.ClientRectangle.Right - (backImagePadding.Right + image.Width), base.ClientRectangle.Bottom - (image.Height + backImagePadding.Bottom));
                        break;
                }
            }
            if (displayHeader)
            {
                Rectangle bounds = new Rectangle(20, 20, base.ClientRectangle.Width - 40, 40);
                TextFormatFlags flags = TextFormatFlags.EndEllipsis | GetTextFormatFlags();
                TextRenderer.DrawText(e.Graphics, Text, MetroFonts.Title, bounds, foreColor, flags);
            }
            if (Resizable && (base.SizeGripStyle == SizeGripStyle.Auto || base.SizeGripStyle == SizeGripStyle.Show))
            {
                using (SolidBrush brush2 = new SolidBrush(MetroPaint.ForeColor.Button.Disabled(Theme)))
                {
                    Size size = new Size(2, 2);
                    e.Graphics.FillRectangles(brush2, new Rectangle[6]
                    {
                    new Rectangle(new Point(base.ClientRectangle.Width - 6, base.ClientRectangle.Height - 6), size),
                    new Rectangle(new Point(base.ClientRectangle.Width - 10, base.ClientRectangle.Height - 10), size),
                    new Rectangle(new Point(base.ClientRectangle.Width - 10, base.ClientRectangle.Height - 6), size),
                    new Rectangle(new Point(base.ClientRectangle.Width - 6, base.ClientRectangle.Height - 10), size),
                    new Rectangle(new Point(base.ClientRectangle.Width - 14, base.ClientRectangle.Height - 6), size),
                    new Rectangle(new Point(base.ClientRectangle.Width - 6, base.ClientRectangle.Height - 14), size)
                    });
                }
            }
        }

        private TextFormatFlags GetTextFormatFlags()
        {
            switch (TextAlign)
            {
                case MetroFormTextAlign.Left:
                    return TextFormatFlags.Default;
                case MetroFormTextAlign.Center:
                    return TextFormatFlags.HorizontalCenter;
                case MetroFormTextAlign.Right:
                    return TextFormatFlags.Right;
                default:
                    throw new InvalidOperationException();
            }
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            base.OnClosing(e);
        }

        protected override void OnClosed(EventArgs e)
        {
            if (base.Owner != null)
            {
                base.Owner = null;
            }
            RemoveShadow();
            base.OnClosed(e);
        }

        [SecuritySafeCritical]
        public bool FocusMe()
        {
            return WinApi.SetForegroundWindow(base.Handle);
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            if (base.DesignMode)
            {
                return;
            }
            switch (base.StartPosition)
            {
                case FormStartPosition.CenterParent:
                    CenterToParent();
                    break;
                case FormStartPosition.CenterScreen:
                    if (base.IsMdiChild)
                    {
                        CenterToParent();
                    }
                    else
                    {
                        CenterToScreen();
                    }
                    break;
            }
            RemoveCloseButton();
            if (base.ControlBox)
            {
                AddWindowButton(WindowButtons.Close);
                if (base.MaximizeBox)
                {
                    AddWindowButton(WindowButtons.Maximize);
                }
                if (base.MinimizeBox)
                {
                    AddWindowButton(WindowButtons.Minimize);
                }
                UpdateWindowButtonPosition();
            }
            CreateShadow();
        }

        protected override void OnActivated(EventArgs e)
        {
            base.OnActivated(e);
            if (shadowType == MetroFormShadowType.AeroShadow && IsAeroThemeEnabled() && IsDropShadowSupported())
            {
                int attrValue = 2;
                DwmApi.DwmSetWindowAttribute(base.Handle, 2, ref attrValue, 4);
                DwmApi.MARGINS mARGINS = default(DwmApi.MARGINS);
                mARGINS.cyBottomHeight = 1;
                mARGINS.cxLeftWidth = 0;
                mARGINS.cxRightWidth = 0;
                mARGINS.cyTopHeight = 0;
                DwmApi.MARGINS marInset = mARGINS;
                DwmApi.DwmExtendFrameIntoClientArea(base.Handle, ref marInset);
            }
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);
            Invalidate();
        }

        protected override void OnResizeEnd(EventArgs e)
        {
            base.OnResizeEnd(e);
            UpdateWindowButtonPosition();
        }

        protected override void WndProc(ref Message m)
        {
            if (base.DesignMode)
            {
                base.WndProc(ref m);
                return;
            }
            switch (m.Msg)
            {
                case 274:
                    switch (m.WParam.ToInt32() & 0xFFF0)
                    {
                        case 61456:
                            if (!Movable)
                            {
                                return;
                            }
                            break;
                    }
                    break;
                case 163:
                case 515:
                    if (!base.MaximizeBox)
                    {
                        return;
                    }
                    break;
                case 132:
                    {
                        WinApi.HitTest hitTest = HitTestNCA(m.HWnd, m.WParam, m.LParam);
                        if (hitTest != WinApi.HitTest.HTCLIENT)
                        {
                            m.Result = (IntPtr)(long)hitTest;
                            return;
                        }
                        break;
                    }
            }
            base.WndProc(ref m);
            switch (m.Msg)
            {
                case 36:
                    OnGetMinMaxInfo(m.HWnd, m.LParam);
                    break;
                case 5:
                    {
                        if (windowButtonList == null)
                        {
                            break;
                        }
                        windowButtonList.TryGetValue(WindowButtons.Maximize, out MetroFormButton value);
                        if (value == null)
                        {
                            break;
                        }
                        if (base.WindowState == FormWindowState.Normal)
                        {
                            if (shadowForm != null)
                            {
                                shadowForm.Visible = true;
                            }
                            value.Text = "1";
                        }
                        if (base.WindowState == FormWindowState.Maximized)
                        {
                            value.Text = "2";
                        }
                        break;
                    }
            }
        }

        [SecuritySafeCritical]
        private unsafe void OnGetMinMaxInfo(IntPtr hwnd, IntPtr lParam)
        {
            WinApi.MINMAXINFO* ptr = (WinApi.MINMAXINFO*)(void*)lParam;
            Screen screen = Screen.FromHandle(hwnd);
            ptr->ptMaxSize.x = screen.WorkingArea.Width;
            ptr->ptMaxSize.y = screen.WorkingArea.Height;
            ptr->ptMaxPosition.x = Math.Abs(screen.WorkingArea.Left - screen.Bounds.Left);
            ptr->ptMaxPosition.y = Math.Abs(screen.WorkingArea.Top - screen.Bounds.Top);
        }

        private WinApi.HitTest HitTestNCA(IntPtr hwnd, IntPtr wparam, IntPtr lparam)
        {
            Point pt = new Point((short)(int)lparam, (short)((int)lparam >> 16));
            int num = Math.Max(Padding.Right, Padding.Bottom);
            if (Resizable && RectangleToScreen(new Rectangle(base.ClientRectangle.Width - num, base.ClientRectangle.Height - num, num, num)).Contains(pt))
            {
                return WinApi.HitTest.HTBOTTOMRIGHT;
            }
            if (RectangleToScreen(new Rectangle(5, 5, base.ClientRectangle.Width - 10, 50)).Contains(pt))
            {
                return WinApi.HitTest.HTCAPTION;
            }
            return WinApi.HitTest.HTCLIENT;
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            base.OnMouseDown(e);
            if (e.Button == MouseButtons.Left && Movable && base.WindowState != FormWindowState.Maximized && base.Width - 5 > e.Location.X && e.Location.X > 5 && e.Location.Y > 5)
            {
                MoveControl();
            }
        }

        [SecuritySafeCritical]
        private void MoveControl()
        {
            WinApi.ReleaseCapture();
            WinApi.SendMessage(base.Handle, 161, 2, 0);
        }

        [SecuritySafeCritical]
        private static bool IsAeroThemeEnabled()
        {
            if (Environment.OSVersion.Version.Major <= 5)
            {
                return false;
            }
            DwmApi.DwmIsCompositionEnabled(out bool pfEnabled);
            return pfEnabled;
        }

        private static bool IsDropShadowSupported()
        {
            if (Environment.OSVersion.Version.Major > 5)
            {
                return SystemInformation.IsDropShadowEnabled;
            }
            return false;
        }

        private void AddWindowButton(WindowButtons button)
        {
            if (windowButtonList == null)
            {
                windowButtonList = new Dictionary<WindowButtons, MetroFormButton>();
            }
            if (windowButtonList.ContainsKey(button))
            {
                return;
            }
            MetroFormButton metroFormButton = new MetroFormButton();
            switch (button)
            {
                case WindowButtons.Close:
                    metroFormButton.Text = "r";
                    break;
                case WindowButtons.Minimize:
                    metroFormButton.Text = "0";
                    break;
                case WindowButtons.Maximize:
                    if (base.WindowState == FormWindowState.Normal)
                    {
                        metroFormButton.Text = "1";
                    }
                    else
                    {
                        metroFormButton.Text = "2";
                    }
                    break;
            }
            metroFormButton.Style = Style;
            metroFormButton.Theme = Theme;
            metroFormButton.Tag = button;
            metroFormButton.Size = new Size(25, 20);
            metroFormButton.Anchor = (AnchorStyles.Top | AnchorStyles.Right);
            metroFormButton.TabStop = false;
            metroFormButton.Click += WindowButton_Click;
            base.Controls.Add(metroFormButton);
            windowButtonList.Add(button, metroFormButton);
        }

        private void WindowButton_Click(object sender, EventArgs e)
        {
            MetroFormButton metroFormButton = sender as MetroFormButton;
            if (metroFormButton == null)
            {
                return;
            }
            switch ((WindowButtons)metroFormButton.Tag)
            {
                case WindowButtons.Close:
                    Close();
                    break;
                case WindowButtons.Minimize:
                    base.WindowState = FormWindowState.Minimized;
                    break;
                case WindowButtons.Maximize:
                    if (base.WindowState == FormWindowState.Normal)
                    {
                        base.WindowState = FormWindowState.Maximized;
                        metroFormButton.Text = "2";
                    }
                    else
                    {
                        base.WindowState = FormWindowState.Normal;
                        metroFormButton.Text = "1";
                    }
                    break;
            }
        }

        private void UpdateWindowButtonPosition()
        {
            if (base.ControlBox)
            {
                Dictionary<int, WindowButtons> dictionary = new Dictionary<int, WindowButtons>(3);
                dictionary.Add(0, WindowButtons.Close);
                dictionary.Add(1, WindowButtons.Maximize);
                dictionary.Add(2, WindowButtons.Minimize);
                Dictionary<int, WindowButtons> dictionary2 = dictionary;
                Point location = new Point(base.ClientRectangle.Width - 5 - 25, 5);
                int num = location.X - 25;
                MetroFormButton metroFormButton = null;
                if (windowButtonList.Count == 1)
                {
                    foreach (KeyValuePair<WindowButtons, MetroFormButton> windowButton in windowButtonList)
                    {
                        windowButton.Value.Location = location;
                    }
                }
                else
                {
                    foreach (KeyValuePair<int, WindowButtons> item in dictionary2)
                    {
                        bool flag = windowButtonList.ContainsKey(item.Value);
                        if (metroFormButton == null && flag)
                        {
                            metroFormButton = windowButtonList[item.Value];
                            metroFormButton.Location = location;
                        }
                        else if (metroFormButton != null && flag)
                        {
                            windowButtonList[item.Value].Location = new Point(num, 5);
                            num -= 25;
                        }
                    }
                }
                Refresh();
            }
        }

        private void CreateShadow()
        {
            switch (ShadowType)
            {
                case MetroFormShadowType.None:
                    break;
                case MetroFormShadowType.Flat:
                    shadowForm = new MetroFlatDropShadow(this);
                    break;
                case MetroFormShadowType.DropShadow:
                    shadowForm = new MetroRealisticDropShadow(this);
                    break;
            }
        }

        private void RemoveShadow()
        {
            if (shadowForm != null && !shadowForm.IsDisposed)
            {
                shadowForm.Visible = false;
                base.Owner = shadowForm.Owner;
                shadowForm.Owner = null;
                shadowForm.Dispose();
                shadowForm = null;
            }
        }

        [SecuritySafeCritical]
        public void RemoveCloseButton()
        {
            IntPtr systemMenu = WinApi.GetSystemMenu(base.Handle, bRevert: false);
            if (!(systemMenu == IntPtr.Zero))
            {
                int menuItemCount = WinApi.GetMenuItemCount(systemMenu);
                if (menuItemCount > 0)
                {
                    WinApi.RemoveMenu(systemMenu, (uint)(menuItemCount - 1), 5120u);
                    WinApi.RemoveMenu(systemMenu, (uint)(menuItemCount - 2), 5120u);
                    WinApi.DrawMenuBar(base.Handle);
                }
            }
        }

        private Rectangle MeasureText(Graphics g, Rectangle clientRectangle, Font font, string text, TextFormatFlags flags)
        {
            Size proposedSize = new Size(int.MaxValue, int.MinValue);
            Size size = TextRenderer.MeasureText(g, text, font, proposedSize, flags);
            return new Rectangle(clientRectangle.X, clientRectangle.Y, size.Width, size.Height);
        }
    }
    public enum MetroFormBorderStyle
    {
        None,
        FixedSingle
    }
    public enum MetroFormTextAlign
    {
        Left,
        Center,
        Right
    }
    public enum BackLocation
    {
        TopLeft,
        TopRight,
        BottomLeft,
        BottomRight
    }
}