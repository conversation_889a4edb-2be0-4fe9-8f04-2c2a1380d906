using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Timers;
using System.Windows.Forms;

namespace OCRTools
{
    public class GifTool : Form
    {
        public delegate void SetControlValue(long value);

        private System.Timers.Timer Numtimer;

        private System.Timers.Timer Capturetimer;

        private long TimeCount;

        private int fps = 13;

        private int Fminterval;

        private GifFm _gifFm;

        private bool recoderstatus;

        //private bool IsEdit;

        //private Task<Image> _captureTask;

        private Size _size;

        private int _left = 0;

        //private bool _stopRequested;

        private int _top = 0;

        private List<FrameInfo> ListFrameInfo = new List<FrameInfo>();

        private Color transparent = Color.FromArgb(byte.MaxValue, 0, 254, 0);

        private Image Drawimage;

        public static List<Task> TaskList = new List<Task>();

        private string PathName;

        private bool exitcatch = false;

        private FmEdit Fmedit;

        private DrawArea drawArea;

        private SaveFileDialog saveFileDialog1;

        private const int WM_NCLBUTTONDOWN = 161;

        private const int HTCAPTION = 2;

        private IContainer components = null;

        private ListButton Btn_Picture;

        private ListButton Btn_Exit;

        private ListButton Btn_Timer;

        public ProgressbarEX customProgressBar1;

        private ListButton listButton1;

        private PictureBox RecoderStart;

        private PictureBox Btn_Move;

        public string MarkLocation = "右下";

        public string GifTextDataPath
        {
            get;
            set;
        }

        public int FrameCount
        {
            get;
            private set;
        }

        public string FullPath
        {
            get;
            private set;
        }

        private void SetPanellocation()
        {
            _gifFm.Location = new Point(base.Left, base.Top - _gifFm.Height - 2.DPIValue());
            SetGetScreen();
        }

        private void SetGetScreen()
        {
            Point location = _gifFm.PointToScreen(_gifFm.pictureBox1.Location);
            Size size = _gifFm.pictureBox1.Size;
            Rectangle rectangle = new Rectangle(location, size);
            _size = rectangle.Size;
            _left = rectangle.X;
            _top = rectangle.Y;
        }

        public GifTool(GifFm gifFm)
        {
            _gifFm = gifFm;
            Point location = gifFm.PointToScreen(gifFm.pictureBox1.Location);
            Size size = gifFm.pictureBox1.Size;
            Rectangle rectangle = new Rectangle(location, size);
            base.Opacity = 0.0;
            base.Shown += delegate
            {
                base.Opacity = 1.0;
            };
            Fminterval = 1000 / fps;
            _size = rectangle.Size;
            _left = rectangle.X;
            _top = rectangle.Y;
            int num = 1000;
            Numtimer = new System.Timers.Timer(num)
            {
                AutoReset = true
            };
            Numtimer.Elapsed += Numtimer_tick;
            Capturetimer = new System.Timers.Timer(Fminterval)
            {
                AutoReset = true
            };
            Capturetimer.Elapsed += Capturetimer_tick;
            InitializeComponent();
            drawArea = new DrawArea
            {
                TopLevel = false,
                IsShowCross = false,
                IsEdit = false,
                IsWindows = true,
                ActiveTool = DrawToolType.Text,
                Size = _size
            };
            //drawArea.InitFm(this);
            Setlocation();
            string folderPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            GifTextDataPath = folderPath + "\\Gif_Data\\";
            if (!Directory.Exists(GifTextDataPath))
            {
                Directory.CreateDirectory(GifTextDataPath);
            }
            GifTextDataPath += "temp.txt";
            StaticValue.current_ToolType = DrawToolType.Text;
            Read();
            ToolText toolText = (ToolText)drawArea.tools[7];
            toolText.ValueChanged += delegate
            {
                Write();
            };
            Fmedit = new FmEdit(drawArea);
            base.FormClosing += delegate
            {
                exitcatch = true;
                Capturetimer.Stop();
                Numtimer.Stop();
                FrameRate.Stop();
                Write();
                StaticValue.isCatchScreen = false;
                Empty(FullPath);
                Fmedit.Close();
                drawArea.Close();
            };
            base.LocationChanged += delegate
            {
                if (base.Location.Y > base.Owner.Location.Y)
                {
                    Fmedit.Location = new Point(base.Location.X, base.Location.Y + base.Height + 2.DPIValue());
                }
                else
                {
                    Fmedit.Location = new Point(base.Location.X, base.Owner.Location.Y - base.Height - Fmedit.Height - 2.DPIValue() * 2);
                }
            };
            Fmedit.Shown += delegate
            {
                Fmedit.Opacity = 1.0;
            };
            if (base.Location.Y > drawArea.Location.Y)
            {
                Fmedit.Location = new Point(base.Location.X, base.Location.Y + base.Height + 2.DPIValue());
            }
            else
            {
                Fmedit.Location = new Point(base.Location.X, drawArea.Location.Y - base.Height - Fmedit.Height - 2.DPIValue() * 2);
            }
            saveFileDialog1 = new SaveFileDialog();
            RecoderStart.Image = ImageHelp.CreateStartRecoder();
            Btn_Move.Image = ImageHelp.CreateMoveRecoder();
            RecoderStart.MouseDown += delegate
            {
                if (recoderstatus)
                {
                    Stop();
                }
                else
                {
                    Start();
                }
            };
        }

        private void Start()
        {
            if (SaveFile())
            {
                _gifFm.StatusColor = GifFm.StatusColors.recoder;
                recoderstatus = true;
                RecoderStart.Image = ImageHelp.CreateStopRecoder();
                string folderPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                FullPath = folderPath + "\\Gif_Copy\\";
                if (!Directory.Exists(FullPath))
                {
                    Directory.CreateDirectory(FullPath);
                }
                FrameCount = 0;
                if (Btn_Picture.Text == "关闭水印")
                {
                    Btn_Picture.Text = "已添加";
                    Fmedit.Hide();
                    drawArea.Hide();
                    drawArea.GraphicsList.UnselectAll();
                    Drawimage = new Bitmap(_size.Width, _size.Height);
                    using (Graphics g = Graphics.FromImage(Drawimage))
                    {
                        if (drawArea.GraphicsList != null)
                        {
                            drawArea.GraphicsList.Draw(g);
                        }
                    }
                }
                customProgressBar1.Value = 0;
                exitcatch = false;
                Btn_Picture.Enabled = false;
                TimeCount = 0L;
                Numtimer.Start();
                FrameRate.Start(Fminterval);
                Capturetimer.Start();
            }
        }

        private void Stop()
        {
            _gifFm.StatusColor = GifFm.StatusColors.loading;
            exitcatch = true;
            RecoderStart.Enabled = false;
        }

        public void ClearMemory()
        {
            GC.Collect();
            GC.WaitForPendingFinalizers();
            if (Environment.OSVersion.Platform == PlatformID.Win32NT)
            {
                SetProcessWorkingSetSize(Process.GetCurrentProcess().Handle, -1, -1);
            }
        }

        [DllImport("kernel32.dll")]
        public static extern int SetProcessWorkingSetSize(IntPtr process, int minSize, int maxSize);

        private void Capturetimer_tick(object sender, ElapsedEventArgs e)
        {
        }

        private void Empty(string path)
        {
            try
            {
                if (Directory.Exists(path))
                {
                    DirectoryInfo directoryInfo = new DirectoryInfo(path);
                    FileInfo[] files = directoryInfo.GetFiles();
                    foreach (FileInfo fileInfo in files)
                    {
                        fileInfo.Delete();
                    }
                    DirectoryInfo[] directories = directoryInfo.GetDirectories();
                    foreach (DirectoryInfo directoryInfo2 in directories)
                    {
                        directoryInfo2.Delete(recursive: true);
                    }
                }
            }
            catch
            {
            }
        }

        private void Numtimer_tick(object sender, ElapsedEventArgs e)
        {
            Invoke(new SetControlValue(ShowTime), TimeCount);
            TimeCount++;
        }

        private void ShowTime(long t)
        {
            TimeSpan timeSpan = new TimeSpan(0, 0, (int)t);
            Btn_Timer.Text = $"{timeSpan.Minutes:00}:{timeSpan.Seconds:00}";
        }

        public void Setlocation()
        {
            int num = 2.DPIValue();
            int x = _gifFm.Location.X;
            int y = _gifFm.Location.Y + _gifFm.Height + num;
            base.Location = new Point(x, y);
        }

        private bool SaveFile()
        {
            string text = DateTime.Now.ToString("yyyyMMddhhmmss");
            string text2 = null;
            string text3 = text;
            for (int i = 0; i < text3.Length; i++)
            {
                char c = text3[i];
                if (c != '/' && c != ':' && c != ' ')
                {
                    text2 += c.ToString();
                }
            }
            saveFileDialog1.Filter = "gif文件|*.gif";
            saveFileDialog1.Title = "保存位置";
            saveFileDialog1.FileName = StaticValue.CatchName + "_" + text2;
            saveFileDialog1.FilterIndex = 0;
            this.CenterChild();
            if (saveFileDialog1.ShowDialog(this) == DialogResult.OK)
            {
                PathName = saveFileDialog1.FileName;
                return true;
            }
            return false;
        }

        private void FmClose()
        {
            Form owner = base.Owner;
            owner.Close();
            Close();
        }

        private void Write()
        {
            List<ListData> list = new List<ListData>();
            Root root = new Root();
            foreach (DrawObject graphics in drawArea.GraphicsList.graphicsList)
            {
                if (graphics is DrawText)
                {
                    DrawText drawText = (DrawText)graphics;
                    ListData listData = new ListData
                    {
                        Rectangle = drawText.Rectangle,
                        Margin = new Point(drawArea.Width - drawText.Rectangle.X, drawArea.Height - drawText.Rectangle.Y),
                        Text = drawText.Text,
                        IsOutline = drawText.IsOutline,
                        Color = drawText.Color,
                        FontSize = drawText.FontSize,
                        Fontstyle = drawText.Fontstyle
                    };
                    list.Add(listData);
                }
            }
            root.listData = list;
            string content = CommonString.JavaScriptSerializer.Serialize(root);
            TxtHelper.Write(GifTextDataPath, content);
        }

        public void Read()
        {
            if (!File.Exists(GifTextDataPath))
            {
                return;
            }
            string value = TxtHelper.ReadAllText(GifTextDataPath);
            Root root2 = CommonString.JavaScriptSerializer.Deserialize<Root>(value);
            for (int i = 0; i < root2.listData.Count; i++)
            {
                DrawText drawText = new DrawText();
                switch (MarkLocation)
                {
                    case "左上":
                        drawText.Rectangle = new Rectangle(root2.listData[i].Rectangle.X, root2.listData[i].Rectangle.Y, root2.listData[i].Rectangle.Width, root2.listData[i].Rectangle.Height);
                        break;
                    case "左下":
                        drawText.Rectangle = new Rectangle(root2.listData[i].Rectangle.X, drawArea.Height - root2.listData[i].Margin.Y, root2.listData[i].Rectangle.Width, root2.listData[i].Rectangle.Height);
                        break;
                    case "右上":
                        drawText.Rectangle = new Rectangle(drawArea.Width - root2.listData[i].Margin.X, root2.listData[i].Rectangle.Y, root2.listData[i].Rectangle.Width, root2.listData[i].Rectangle.Height);
                        break;
                    case "右下":
                        drawText.Rectangle = new Rectangle(drawArea.Width - root2.listData[i].Margin.X, drawArea.Height - root2.listData[i].Margin.Y, root2.listData[i].Rectangle.Width, root2.listData[i].Rectangle.Height);
                        break;
                }
                drawText.Text = root2.listData[i].Text;
                drawText.IsOutline = root2.listData[i].IsOutline;
                drawText.Color = root2.listData[i].Color;
                drawText.FontSize = root2.listData[i].FontSize;
                drawText.Fontstyle = root2.listData[i].Fontstyle;
                drawArea.GraphicsList.UnselectAll();
                drawArea.GraphicsList.Add(drawText);
                drawText.Selected = false;
                drawText.IsCache = true;
                drawText.Normalize();
                drawArea.AddCommandToHistory(new CommandAdd(drawText));
                drawArea.Refresh();
            }
        }


        public void ReadTool()
        {
            string value = TxtHelper.ReadAllText(GifTextDataPath);
            drawArea.GraphicsList.SelectAll();
            drawArea.GraphicsList.DeleteSelection();
            Root root2 = CommonString.JavaScriptSerializer.Deserialize<Root>(value);
            for (int i = 0; i < root2.listData.Count; i++)
            {
                DrawText drawText = new DrawText();
                switch (MarkLocation)
                {
                    case "左上":
                        drawText.Rectangle = new Rectangle(root2.listData[i].Rectangle.X, root2.listData[i].Rectangle.Y, root2.listData[i].Rectangle.Width, root2.listData[i].Rectangle.Height);
                        break;
                    case "左下":
                        drawText.Rectangle = new Rectangle(root2.listData[i].Rectangle.X, drawArea.Height - root2.listData[i].Margin.Y, root2.listData[i].Rectangle.Width, root2.listData[i].Rectangle.Height);
                        break;
                    case "右上":
                        drawText.Rectangle = new Rectangle(drawArea.Width - root2.listData[i].Margin.X, root2.listData[i].Rectangle.Y, root2.listData[i].Rectangle.Width, root2.listData[i].Rectangle.Height);
                        break;
                    case "右下":
                        drawText.Rectangle = new Rectangle(drawArea.Width - root2.listData[i].Margin.X, drawArea.Height - root2.listData[i].Margin.Y, root2.listData[i].Rectangle.Width, root2.listData[i].Rectangle.Height);
                        break;
                }
                drawText.Text = root2.listData[i].Text;
                drawText.IsOutline = root2.listData[i].IsOutline;
                drawText.Color = root2.listData[i].Color;
                drawText.FontSize = root2.listData[i].FontSize;
                drawText.Fontstyle = root2.listData[i].Fontstyle;
                drawText.IsSelected = false;
                drawText.Selected = false;
                drawText.IsCache = true;
                drawArea.GraphicsList.Add(drawText);
            }
            drawArea.GraphicsList.UnselectAll();
            drawArea.Refresh();
        }

        private void Btn_Exit_Click(object sender, EventArgs e)
        {
            if (recoderstatus)
            {
                DialogResult dialogResult = MessageBox.Show("当前录制文件未生成，是否退出？", "提醒", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
                if (dialogResult == DialogResult.OK)
                {
                    Task.WaitAll(TaskList.ToArray());
                    TaskList.Clear();
                    FmClose();
                }
            }
            else
            {
                Task.WaitAll(TaskList.ToArray());
                TaskList.Clear();
                FmClose();
            }
        }

        public void FmRefresh()
        {
            SetGetScreen();
            _gifFm.pictureBox1.Controls.Remove(drawArea);
            //drawArea.BackgroundImageEx = Native.Capture(_size, _left, _top);
            _gifFm.pictureBox1.Controls.Add(drawArea);
        }

        public void Fmdelete()
        {
            drawArea.GraphicsList.DeleteSelection();
        }

        private void Btn_Picture_Click(object sender, EventArgs e)
        {
            if (Btn_Picture.Text == "文字水印")
            {
                SetGetScreen();
                Btn_Picture.Text = "关闭水印";
                //drawArea.BackgroundImageEx = Native.CaptureWithCursor(_size, _left, _top);
                drawArea.Dock = DockStyle.Fill;
                drawArea.Show();
                Fmedit.Show();
                _gifFm.pictureBox1.Controls.Add(drawArea);
                ReadTool();
            }
            else if (Btn_Picture.Text == "关闭水印")
            {
                Btn_Picture.Text = "文字水印";
                _gifFm.pictureBox1.Controls.Remove(drawArea);
                Fmedit.Hide();
            }
        }

        private void customProgressBar1_ValueChanged(object sender, EventArgs e)
        {
            listButton1.Text = "进度：" + customProgressBar1.Value.ToString() + "%";
        }

        private void Btn_Move_MouseDown(object sender, MouseEventArgs e)
        {
            ReleaseCapture();
            SendMessage((int)base.Handle, 161, 2, 0);
        }

        [DllImport("user32.dll", EntryPoint = "SendMessageA")]
        private static extern int SendMessage(int hwnd, int wMsg, int wParam, int lParam);

        [DllImport("user32.dll")]
        private static extern int ReleaseCapture();

        private void GifTool_LocationChanged(object sender, EventArgs e)
        {
            SetPanellocation();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null)
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            RecoderStart = new System.Windows.Forms.PictureBox();
            Btn_Move = new System.Windows.Forms.PictureBox();
            Btn_Exit = new OCRTools.ListButton();
            customProgressBar1 = new OCRTools.ProgressbarEX();
            listButton1 = new OCRTools.ListButton();
            Btn_Picture = new OCRTools.ListButton();
            Btn_Timer = new OCRTools.ListButton();
            ((System.ComponentModel.ISupportInitialize)RecoderStart).BeginInit();
            ((System.ComponentModel.ISupportInitialize)Btn_Move).BeginInit();
            SuspendLayout();
            RecoderStart.BackColor = System.Drawing.Color.White;
            RecoderStart.Dock = System.Windows.Forms.DockStyle.Left;
            RecoderStart.Location = new System.Drawing.Point(30, 1);
            RecoderStart.Name = "RecoderStart";
            RecoderStart.Size = new System.Drawing.Size(29, 28);
            RecoderStart.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            RecoderStart.TabIndex = 6;
            RecoderStart.TabStop = false;
            Btn_Move.BackColor = System.Drawing.Color.White;
            Btn_Move.Dock = System.Windows.Forms.DockStyle.Left;
            Btn_Move.Location = new System.Drawing.Point(1, 1);
            Btn_Move.Name = "Btn_Move";
            Btn_Move.Size = new System.Drawing.Size(29, 28);
            Btn_Move.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            Btn_Move.TabIndex = 7;
            Btn_Move.TabStop = false;
            Btn_Move.MouseDown += new System.Windows.Forms.MouseEventHandler(Btn_Move_MouseDown);
            Btn_Exit.AutoSize = true;
            Btn_Exit.BackColor = System.Drawing.Color.White;
            Btn_Exit.Dock = System.Windows.Forms.DockStyle.Fill;
            Btn_Exit.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            Btn_Exit.Font = new System.Drawing.Font("微软雅黑", 9f);
            Btn_Exit.IsBorder = false;
            Btn_Exit.ListItems = null;
            Btn_Exit.Location = new System.Drawing.Point(345, 1);
            Btn_Exit.Name = "Btn_Exit";
            Btn_Exit.Size = new System.Drawing.Size(73, 28);
            Btn_Exit.TabIndex = 2;
            Btn_Exit.Text = "退出";
            Btn_Exit.UseVisualStyleBackColor = false;
            Btn_Exit.Click += new System.EventHandler(Btn_Exit_Click);
            customProgressBar1.BackColor = System.Drawing.Color.White;
            customProgressBar1.Dock = System.Windows.Forms.DockStyle.Left;
            customProgressBar1.ForeColor = System.Drawing.Color.LightGray;
            customProgressBar1.Location = new System.Drawing.Point(258, 1);
            customProgressBar1.Margin = new System.Windows.Forms.Padding(10);
            customProgressBar1.Name = "customProgressBar1";
            customProgressBar1.Size = new System.Drawing.Size(87, 28);
            customProgressBar1.TabIndex = 4;
            customProgressBar1.ValueChanged += new OCRTools.ProgressbarEX.ValueChangeHandler(customProgressBar1_ValueChanged);
            listButton1.BackColor = System.Drawing.Color.White;
            listButton1.Dock = System.Windows.Forms.DockStyle.Left;
            listButton1.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            listButton1.Font = new System.Drawing.Font("微软雅黑", 9f);
            listButton1.IsBorder = false;
            listButton1.ListItems = null;
            listButton1.Location = new System.Drawing.Point(178, 1);
            listButton1.Name = "listButton1";
            listButton1.Size = new System.Drawing.Size(80, 28);
            listButton1.TabIndex = 5;
            listButton1.Text = "进度：0%";
            listButton1.UseVisualStyleBackColor = false;
            Btn_Picture.AutoSize = true;
            Btn_Picture.BackColor = System.Drawing.Color.White;
            Btn_Picture.Dock = System.Windows.Forms.DockStyle.Left;
            Btn_Picture.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            Btn_Picture.Font = new System.Drawing.Font("微软雅黑", 9f);
            Btn_Picture.IsBorder = false;
            Btn_Picture.ListItems = null;
            Btn_Picture.Location = new System.Drawing.Point(110, 1);
            Btn_Picture.Name = "Btn_Picture";
            Btn_Picture.Size = new System.Drawing.Size(68, 28);
            Btn_Picture.TabIndex = 1;
            Btn_Picture.Text = "文字水印";
            Btn_Picture.UseVisualStyleBackColor = false;
            Btn_Picture.Click += new System.EventHandler(Btn_Picture_Click);
            Btn_Timer.AutoSize = true;
            Btn_Timer.BackColor = System.Drawing.Color.White;
            Btn_Timer.Dock = System.Windows.Forms.DockStyle.Left;
            Btn_Timer.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            Btn_Timer.Font = new System.Drawing.Font("微软雅黑", 9f);
            Btn_Timer.IsBorder = false;
            Btn_Timer.ListItems = null;
            Btn_Timer.Location = new System.Drawing.Point(59, 1);
            Btn_Timer.Name = "Btn_Timer";
            Btn_Timer.Size = new System.Drawing.Size(51, 28);
            Btn_Timer.TabIndex = 3;
            Btn_Timer.Text = "00:00";
            Btn_Timer.UseVisualStyleBackColor = false;
            base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 12f);
            base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            BackColor = System.Drawing.Color.FromArgb(28, 160, 224);
            base.ClientSize = new System.Drawing.Size(419, 30);
            base.Controls.Add(Btn_Exit);
            base.Controls.Add(customProgressBar1);
            base.Controls.Add(listButton1);
            base.Controls.Add(Btn_Picture);
            base.Controls.Add(Btn_Timer);
            base.Controls.Add(RecoderStart);
            base.Controls.Add(Btn_Move);
            base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            MaximumSize = new System.Drawing.Size(800, 30);
            MinimumSize = new System.Drawing.Size(288, 30);
            base.Name = "GifTool";
            base.Padding = new System.Windows.Forms.Padding(1);
            base.ShowIcon = false;
            base.ShowInTaskbar = false;
            base.StartPosition = System.Windows.Forms.FormStartPosition.Manual;
            Text = "Form1";
            base.TopMost = true;
            base.LocationChanged += new System.EventHandler(GifTool_LocationChanged);
            ((System.ComponentModel.ISupportInitialize)RecoderStart).EndInit();
            ((System.ComponentModel.ISupportInitialize)Btn_Move).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }
    }
}
