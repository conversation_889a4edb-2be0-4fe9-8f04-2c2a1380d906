﻿using OCRTools.Common;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Text;
using System.Web;
using UtfUnknown;

namespace OCRTools
{
    public enum ProcessBy
    {
        主界面 = 0,
        划词翻译 = 1,
        批量识别 = 2,
        固定区域 = 3
    }

    public class OcrPoolProcess
    {
        public static BlockingCollection<OcrProcessEntity> OcrProcessPool = new BlockingCollection<OcrProcessEntity>();

        public static Image ProcessByFile(OcrType ocrType, List<int> groupType
            , bool isFromLeftToRight, bool isFromTopToDown, TransLanguageTypeEnum from, TransLanguageTypeEnum to
            , string fileName, string imgUrl, string fileExt
            , bool isSearch, ProcessBy processBy, string fileIdentity)
        {
            byte[] byts = null;
            Image bitmap = null;

            if (CommonString.IsValidateFileExt(fileExt))
            {
                if (fileName.StartsWith("data:"))
                {
                    fileName = CommonMethod.SubString(fileName, ",");
                    byts = Convert.FromBase64String(fileName);
                }
                else if (fileName.StartsWith("http"))
                {
                    imgUrl = fileName;
                    try
                    {
                        byts = WebClientExt.GetOneClient().DownloadData(imgUrl);
                    }
                    catch (Exception oe)
                    {
                        Log.WriteError("ProcessByFile:" + imgUrl, oe);
                        imgUrl = null;
                    }
                }
                else
                {
                    bitmap = Image.FromFile(fileName);
                    byts = File.ReadAllBytes(fileName);
                }

                if (byts != null)
                {
                    bitmap = Image.FromStream(new MemoryStream(byts));
                }
            }
            else
            {
                if (fileExt?.Equals(CommonString.StrDefaultTxtType) == true)
                {
                    string text;
                    if (fileName.StartsWith("data:txt"))
                    {
                        text = fileName.Substring(fileName.IndexOf("data:txt") + "data:txt".Length).Trim();
                        to = GetAutoToLanguage(text, to);
                    }
                    else
                    {
                        var encoding = CharsetDetector.DetectFromFile(fileName)?.Detected?.Encoding ?? Encoding.UTF8;
                        text = File.ReadAllText(fileName, encoding).Trim();
                    }

                    byts = Encoding.UTF8.GetBytes(text);
                }
                else
                {
                    byts = File.ReadAllBytes(fileName);
                }
            }

            if (byts != null && byts.Length > 0 || !string.IsNullOrEmpty(imgUrl))
                ProcessByBytes(ocrType, groupType, isFromLeftToRight, isFromTopToDown, from, to, byts,
                    imgUrl, fileExt, fileName, isSearch, processBy, fileIdentity, null);
            return bitmap;
        }

        public static void ProcessByImage(OcrType ocrType, List<int> groupType
            , bool isFromLeftToRight, bool isFromTopToDown, TransLanguageTypeEnum from, TransLanguageTypeEnum to
            , Image bitmap, string imgUrl, string fileExt, string fileName
            , bool isSearch, ProcessBy processBy, string fileIdentity
            , bool? isSupportVertical)
        {
            var byts = ImageProcessHelper.ImageToByte(bitmap);
            ProcessByBytes(ocrType, groupType, isFromLeftToRight, isFromTopToDown, from, to, byts, imgUrl,
                fileExt, fileName, isSearch, processBy, fileIdentity
                , isSupportVertical);
        }

        private static TransLanguageTypeEnum GetAutoToLanguage(string strTxt, TransLanguageTypeEnum to)
        {
            if (to.Equals(TransLanguageTypeEnum.中文))
            {
                if (CommonMethod.IsContainsChinese(strTxt))
                {
                    to = TransLanguageTypeEnum.英文;
                }
            }
            return to;
        }

        public static void ProcessByText(List<int> groupType
            , TransLanguageTypeEnum from, TransLanguageTypeEnum to
            , string strTxt, ProcessBy processBy, string fileIdentity)
        {
            if (string.IsNullOrEmpty(strTxt))
                return;
            to = GetAutoToLanguage(strTxt, to);
            var byts = Encoding.UTF8.GetBytes(strTxt);
            ProcessByBytes(OcrType.翻译, groupType, false, true, from, to, byts, "",
                CommonString.StrDefaultTxtType, "文本翻译", false, processBy, fileIdentity, null);
        }

        private static void ProcessByBytes(OcrType ocrType, List<int> groupType
            , bool isFromLeftToRight, bool isFromTopToDown, TransLanguageTypeEnum from, TransLanguageTypeEnum to
            , byte[] byts, string imgUrl, string fileExt, string fileName
            , bool isSearch, ProcessBy processBy, string fileIdentity
            , bool? isSupportVertical)
        {
            var entity = GetProcessEntityByBytes(ocrType, groupType, isFromLeftToRight, isFromTopToDown,
                from, to, byts, imgUrl, fileExt, isSearch, processBy, fileIdentity
                , isSupportVertical);

            if ((Equals(ocrType, OcrType.文本) || Equals(ocrType, OcrType.竖排))
                && CommonSetting.长截图分段识别 && CommonString.IsValidateFileExt(fileExt)
                && byts?.Length > 0 && processBy == ProcessBy.主界面)
            {
                try
                {
                    using (var stream = new MemoryStream(byts))
                    {
                        using (var image = Image.FromStream(stream))
                        {
                            if (image.Height > 3000)
                                entity.IsLongImage = true;
                        }
                    }
                }
                catch { }
            }
            OcrProcessPool.Add(entity);
            if (Equals(fileExt, CommonString.StrDefaultImgType))
            {
                var task = new HistoryTask
                {
                    Status = TaskStatus.History,
                    Info = new HistoryItem
                    {
                        DataType = EDataType.Image,
                        Url = imgUrl,
                        FilePath = fileName,
                        CreateTime = ServerTime.DateTime
                    }
                };
                if (string.IsNullOrEmpty(task.Info.FileName)) task.Info.FileName = fileName;
                CommonMethod.AddRecentTask(task);
            }
        }

        public static OcrProcessEntity GetProcessEntityByBytes(OcrType ocrType, List<int> groupType
            , bool isFromLeftToRight, bool isFromTopToDown, TransLanguageTypeEnum from, TransLanguageTypeEnum to
            , byte[] byts, string imgUrl, string fileExt
            , bool isSearch, ProcessBy processBy, string fileIdentity
                , bool? isSupportVertical)
        {
            if (ocrType != OcrType.翻译 && Equals(fileExt, CommonString.StrDefaultTxtType)) ocrType = OcrType.翻译;
            var entity = new OcrProcessEntity
            {
                ProcessBy = processBy,
                IsShowLoading = true,
                Byts = byts,
                FileExt = fileExt,
                ImgUrl = imgUrl,
                OcrType = ocrType,
                GroupType = groupType,
                IsFromLeftToRight = ocrType != OcrType.竖排 || isFromLeftToRight,
                IsFromTopToDown = ocrType != OcrType.竖排 || isFromTopToDown,
                From = ocrType == OcrType.翻译 ? from : TransLanguageTypeEnum.自动,
                To = ocrType == OcrType.翻译 ? to : TransLanguageTypeEnum.自动,
                IsSearch = isSearch,
                Identity = fileIdentity,
                IsSupportVertical = isSupportVertical
            };
            if (Equals(ocrType, OcrType.翻译) && Equals(entity.From, entity.To))
            {
                if (Equals(entity.From, TransLanguageTypeEnum.自动))
                    entity.To = TransLanguageTypeEnum.英文;
                else
                    entity.To = TransLanguageTypeEnum.中文;
            }
            return entity;
        }
    }
}