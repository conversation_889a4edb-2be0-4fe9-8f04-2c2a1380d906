using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class CommonObjectData : SubRecord
	{
		public ushort ObjectType;

		public ushort ObjectID;

		public ushort OptionFlags;

		public uint Reserved1;

		public uint Reserved2;

		public uint Reserved3;

		public CommonObjectData(SubRecord record)
			: base(record)
		{
		}

		public CommonObjectData()
		{
			Type = 21;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			ObjectType = binaryReader.ReadUInt16();
			ObjectID = binaryReader.ReadUInt16();
			OptionFlags = binaryReader.ReadUInt16();
			Reserved1 = binaryReader.ReadUInt32();
			Reserved2 = binaryReader.ReadUInt32();
			Reserved3 = binaryReader.ReadUInt32();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(ObjectType);
			binaryWriter.Write(ObjectID);
			binaryWriter.Write(OptionFlags);
			binaryWriter.Write(Reserved1);
			binaryWriter.Write(Reserved2);
			binaryWriter.Write(Reserved3);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
