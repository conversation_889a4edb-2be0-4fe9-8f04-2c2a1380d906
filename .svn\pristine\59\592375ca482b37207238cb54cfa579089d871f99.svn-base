﻿using ImageLib;
using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Remoting.Contexts;
using System.Web;
using System.Web.UI.WebControls;

namespace OCRTools
{
    public class OcrHelper
    {
        public static bool ImagePreProcess(OcrProcessEntity processEntity)
        {
            var processd = false;
            try
            {
                var imgProcessMode = CommonSetting.ConvertToEnum<ImageProcessType>(CommonSetting.图片预处理);
                if (!Equals(imgProcessMode, ImageProcessType.原始图像))
                {
                    using (var memory = new MemoryStream(processEntity.Byts))
                    {
                        using (var bitmap = new Bitmap(memory))
                        {
                            var result = ImageProcessHelper.ProcessImage(new Bitmap(bitmap), imgProcessMode);
                            if (result != null)
                            {
                                processEntity.Byts = ImageProcessHelper.ImageToByte(new Bitmap(result));
                                processd = true;
                            }
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("ImagePreProcess", oe);
            }
            return processd;
        }

        public static void QuantizeOcrProcessEntity(OcrProcessEntity processEntity)
        {
            if (!CommonSetting.图片自动压缩 || processEntity.OcrType == OcrType.公式)
            {
                return;
            }

            try
            {
                if (processEntity.Byts.Length / 1024 > (Program.NowUser?.MaxUploadSize ?? 300))
                {
                    var image = ImageCompress.CompressImage(processEntity.Byts, CompressType.助手压缩);
                    if (image.Result != null && image.Result.Length > 0)
                    {
                        processEntity.Byts = image.Result;
                        processEntity.ImgUrl = image.Url;
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("QuantizeOcrProcessEntity", oe);
            }
        }

        public static string LocalOcrState()
        {
            var result = string.Empty;
            try
            {
                result = WebClientExt.GetHtml("http://127.0.0.1:" + CommonSetting.本地识别端口.ToString("F0") + "/Code.do?type=state&t=" + ServerTime.DateTime.Ticks, 10);
            }
            catch (Exception oe)
            {
                Log.WriteError("LocalOcrState", oe);
            }
            return result;
        }

        public static string LocalOcrConfig(int code)
        {
            var result = string.Empty;
            try
            {
                result = WebClientExt.GetHtml("http://127.0.0.1:" + CommonSetting.本地识别端口.ToString("F0") + "/Code.do?type=config&ocr=" + code + "&t=" + ServerTime.DateTime.Ticks, 10);
            }
            catch (Exception oe)
            {
                Log.WriteError("LocalOcrState", oe);
            }
            return result;
        }

        public static OcrContent GetLocalOcrResult(OcrProcessEntity processEntity)
        {
            OcrContent content = null;

            var strPost = "img=" + HttpUtility.UrlEncode(Convert.ToBase64String(processEntity.Byts));
            var url = "http://127.0.0.1:" + CommonSetting.本地识别端口.ToString("F0") + "/Code.do?type=" + processEntity.LocalOcrType
                + "&autofull2half=" + (CommonSetting.自动全半角转换 ? "1" : "0")
                + "&autospace=" + (CommonSetting.自动空格 ? "1" : "0")
                + "&autosymbol=" + (CommonSetting.自动中英文标点 ? "1" : "0")
                + "&autoduplicatesymbol=" + (CommonSetting.去除重复标点 ? "1" : "0")
                + "&ocr=" + processEntity.OcrType.GetHashCode() + "&id=" + processEntity.Identity;

            try
            {
                var result = WebClientExt.GetHtml(url, "", "", strPost, "", 30);
                if (!string.IsNullOrEmpty(result) && result.Length > 2)
                    content = result.DeserializeJson<OcrContent>();
            }
            catch (Exception oe)
            {
                Log.WriteError("GetLocalOcrResult", oe);
            }

            return content;
        }

        private const string Str_Ocr_Result = "code.ashx?op=codeFile&type={0}&left={1}&top={2}&group={3}&pid={4}&ext={5}&from={6}&to={7}&half={8}&space={9}&symbol={10}&duplicate={11}&vertical={12}&app={13}&token={14}&uid={15}&ticks={16}";
        public static OcrContent GetResult(OcrProcessEntity processEntity)
        {
            OcrContent content = null;
            var result = string.Empty;
            try
            {
                var url = string.Format(
                    Str_Ocr_Result
                    , processEntity.OcrType.GetHashCode()
                    , processEntity.IsFromLeftToRight ? "1" : "0"
                    , processEntity.IsFromTopToDown ? "1" : "0"
                    , processEntity.GroupType
                    , processEntity.ProcessId
                    , processEntity.FileExt
                    , processEntity.From.GetHashCode()
                    , processEntity.To.GetHashCode()
                    , CommonSetting.自动全半角转换 ? "1" : "0"
                    , CommonSetting.自动空格 ? "1" : "0"
                    , CommonSetting.自动中英文标点 ? "1" : "0"
                    , CommonSetting.去除重复标点 ? "1" : "0"
                    , processEntity.IsSupportVertical
                    , Program.NowUser?.Account
                    , Program.NowUser?.Token
                    , CommonMethod.Identity
                    , ServerTime.DateTime.Ticks);

                if (CommonSetting.上传到图床 && string.IsNullOrEmpty(processEntity.ImgUrl)
                                           && CommonString.LstCanProcessImageFilesExt.Contains(processEntity.FileExt)
                                           && processEntity.Byts != null && processEntity.Byts.Length > 0)
                {
                    processEntity.ImgUrl = ImageHelper.GetResult(processEntity.Byts, processEntity.FileExt);
                    if (!string.IsNullOrEmpty(processEntity.ImgUrl))
                    {
                        processEntity.Byts = null;
                    }
                }

                try
                {
                    NameValueCollection values = null;
                    if (processEntity.IsImgUrl)
                        values = new NameValueCollection
                        {
                            {"url", processEntity.ImgUrl}
                        };
                    result = UploadFileRequest.PostFile(url, processEntity.IsImgUrl ? null : processEntity.Byts,
                      processEntity.FileExt, values, CommonMethod.GetRequestHeader());
                    //CommonMethod.ShowHelpMsg(result);
                    if (!string.IsNullOrEmpty(result) && result.Length > 2)
                        content = result.DeserializeJson<OcrContent>();
                }
                catch (Exception oe)
                {
                    Log.WriteError("GetResult:" + result, oe);
                }
            }
            catch (Exception)
            {
            }

            return content;
        }

        public static string GetVoiceResultUrl(string strContent, string speaker, string voiceSpeed)
        {
            var url = string.Format(CommonString.HostCode?.FullUrl + "voice/view.html?speed={1}&speaker={2}&text={0}"
                , HttpUtility.UrlEncode(strContent)
                , voiceSpeed
                , speaker);
            return url;
        }

        public static string GetFileResultUrl(OcrContent content)
        {
            var url = string.Format(CommonString.HostCode?.FullUrl + "code.ashx?op=htmlfile&param={0}"
                , HttpUtility.UrlEncode(CommonString.JavaScriptSerializer.Serialize(content)));
            return url;
        }

        public static List<OcrContent> GetResultById(string id)
        {
            return GetServerResult<List<OcrContent>>("code.ashx?op=idcode", CommonString.HostAccount?.FullUrl, "id=" + id);
        }

        public static List<string> GetOcrTrace(string id)
        {
            return GetServerResult<List<string>>("code.ashx?op=ocrprocessinfo&id=" + id, CommonString.HostCode?.FullUrl) ?? new List<string>();
        }

        public static void SendReportInfo(string content, List<UploadFileInfo> lstFiles, ref string strMsg)
        {
            var result = string.Empty;
            try
            {
                var url = CommonString.HostCode?.FullUrl + "code.ashx?op=report";
                var values = new NameValueCollection { { "content", content } };
                result = UploadFileRequest.Post(url, lstFiles.ToArray(), values, CommonMethod.GetRequestHeader());
                if (!result?.ToLower().Contains("true") == true) strMsg = result;
            }
            catch (Exception oe)
            {
                Log.WriteError("SendReportInfo:" + result, oe);
            }
        }

        public static bool SendRegInfo(string account, string pwd, string nickName, string code, ref string strMsg)
        {
            var url = string.Format("code.aspx?op=reg&account={0}&pwd={1}&code={2}&nick={3}", account, pwd, code, nickName);
            return MatchServerResult(url, CommonString.HostAccount?.FullUrl, "true", false, ref strMsg);
        }

        public static bool SendRegMsg(string mobileNo, bool isMobile, ref string strMsg)
        {
            var url = string.Format("mail.aspx?op=regaccount&{1}={0}", mobileNo, isMobile ? "mobile" : "email");
            return MatchServerResult(url, CommonString.HostAccount?.FullUrl, "true", true, ref strMsg);
        }

        public static bool SendResetPwdMsg(string email, bool isMobile, ref string strMsg)
        {
            var url = string.Format("mail.aspx?op=forgetpwd&{1}={0}", email, isMobile ? "mobile" : "email");
            return MatchServerResult(url, CommonString.HostAccount?.FullUrl, "true", true, ref strMsg);
        }

        public static bool SendResetPwdInfo(string account, string pwd, string code, ref string strMsg)
        {
            var url = string.Format("code.aspx?op=resetpwd&account={0}&pwd={1}&code={2}", account, pwd, code);
            return MatchServerResult(url, CommonString.HostAccount?.FullUrl, "true", false, ref strMsg);
        }

        public static bool EditNickName(string account, string strNickName, ref string strMsg)
        {
            var url = string.Format("code.aspx?op=resetnickname&account={0}&nick={1}", account, strNickName);
            return MatchServerResult(url, CommonString.HostAccount?.FullUrl, "true", false, ref strMsg);
        }

        static string LOGIN_CACHE = "login";
        public static bool DoLogin(string account, string pwd, ref string strMsg)
        {
            var result = false;
            var html = string.Empty;
            try
            {
                var user = CommonMethod.GetCache<UserEntity>(LOGIN_CACHE);
                if (Equals(user?.Account?.ToLower(), account?.ToLower()))
                {
                    Program.NowUser = user;
                    result = true;
                }
                else
                {
                    var url = string.Format("code.aspx?op=login&account={0}&pwd={1}", account, pwd);
                    html = CommonMethod.GetServerHtml(url, CommonString.HostAccount?.FullUrl, false);
                    if (string.IsNullOrEmpty(html) || html.StartsWith("<"))
                    {
                    }
                    else
                    {
                        if (html?.ToLower().StartsWith("true") == false)
                        {
                            //"True|" + user.StrAppCode + "|" + user.StrType + "|" + user.StrNickName + "|" + user.DtReg.ToString("yyyy-MM-dd HH:mm:ss") + "|" + user.DtExpire.ToString("yyyy-MM-dd HH:mm:ss") + "|" + user.StrRemark
                            strMsg = html;
                        }
                        else
                        {
                            if (!string.IsNullOrEmpty(html))
                            {
                                html = html.Substring(html.IndexOf("|") + 1);
                                Program.NowUser = html.DeserializeJson<UserEntity>();
                            }
                            result = true;
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("DoLogin:" + html, oe);
            }

            if (!result)
            {
                if (string.IsNullOrEmpty(strMsg))
                {
                    strMsg = CommonString.StrNetWorkError;
                }
                CommonMethod.ClearCache(LOGIN_CACHE);
            }
            else
            {
                CommonMethod.SaveCache(Program.NowUser, LOGIN_CACHE);
            }
            return result;
        }

        public static bool IsLogouted()
        {
            var strMsg = string.Empty;
            var url = string.Format("code.aspx?op=heart&account={0}&token={1}&uid={2}", Program.NowUser?.Account, Program.NowUser?.Token, CommonMethod.Identity);
            var result = MatchServerResult(url, CommonString.HostAccount?.FullUrl, "false", false, ref strMsg);
            if (result)
            {
                CommonMethod.ClearCache(LOGIN_CACHE);
            }
            return result;
        }

        public static bool HealthCheck()
        {
            var strMsg = string.Empty;
            return MatchServerResult("code.ashx?op=health", CommonString.HostAccount?.FullUrl, "true", false, ref strMsg);
        }

        public static UserCodeCount GetCodeCount()
        {
            return GetServerResult<UserCodeCount>("code.aspx?op=count", CommonString.HostAccount?.FullUrl);
        }

        public static List<UserType> GetCanRegUserTypes()
        {
            var lstTmp = GetServerResult<List<UserType>>("code.aspx?op=regtype", CommonString.HostAccount?.FullUrl);
            if (lstTmp?.Count <= 0)
            {
                lstTmp = CommonMethod.GetCache<List<UserType>>("regType");
            }
            else
            {
                CommonMethod.SaveCache(lstTmp, "regType");
            }
            return lstTmp;
        }

        public static bool MatchServerResult(string url, string host, string match, bool isDecodeUrl, ref string strMsg)
        {
            var result = false;
            var html = string.Empty;
            try
            {
                html = CommonMethod.GetServerHtml(url, host, isDecodeUrl);
                if (!string.IsNullOrEmpty(html))
                {
                    if (html.ToLower().Contains(match))
                        result = true;
                    else
                        strMsg = html;
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("MatchResult Url:" + url + ",Result:" + html, oe);
            }
            return result;
        }

        public static T GetServerResult<T>(string url, string host, string strPost = null)
        {
            T content = default;
            var result = string.Empty;
            try
            {
                result = CommonMethod.GetServerHtml(url, host, false, !string.IsNullOrEmpty(strPost), strPost);
                if (!string.IsNullOrEmpty(result))
                    content = result.DeserializeJson<T>();
            }
            catch (Exception oe)
            {
                Log.WriteError("GetServerResult Url:" + url + ",Result:" + result, oe);
            }
            return content;
        }

        public static void InitOcrGroup()
        {
            var lstTmp = GetServerConfigs<List<ObjectTypeItem>>("serverGroup,localGroup,localImage");
            if (lstTmp.Count > 0)
            {
                for (int i = 0; i < lstTmp.Count; i++)
                {
                    if (i == 0 && lstTmp[i]?.Count > 0)
                    {
                        LstServerOcrGroup = lstTmp[i];
                    }
                    else if (i == 1 && lstTmp[i]?.Count > 0)
                    {
                        LstLocalOcrType = lstTmp[i];
#if DEBUG
                        //LstLocalOcrType.Add(new ObjectTypeItem()
                        //{
                        //    Code = 90012,
                        //    Name = "RapidOCR",
                        //    Config = true,
                        //    Desc = "基于 RapidOcrOnnx ，支持离线",
                        //    NeedInstall = true,
                        //    DescUrl = "https://github.com/hiroi-sora/RapidOCR-json",
                        //    UpdateUrl = "123213"
                        //});
                        //LstLocalOcrType.Add(new ObjectTypeItem()
                        //{
                        //    Code = 90013,
                        //    Name = "PaddleOCR",
                        //    Config = true,
                        //    Desc = "基于 PaddleOCR ，支持离线",
                        //    NeedInstall = true,
                        //    DescUrl = "https://github.com/hiroi-sora/PaddleOCR-json",
                        //    UpdateUrl = "123213"
                        //});
#endif
                    }
                    else if (i == 2 && lstTmp[i]?.Count > 0)
                    {
                        LstLocalImageType = lstTmp[i];
                    }
                }
                CommonMethod.SaveCache(LstServerOcrGroup, "serverGroup");
                CommonMethod.SaveCache(LstLocalOcrType, "localGroup");
                CommonMethod.SaveCache(LstLocalImageType, "localImage");
            }
            else
            {
                LstServerOcrGroup = CommonMethod.GetCache<List<ObjectTypeItem>>("serverGroup");
                LstLocalOcrType = CommonMethod.GetCache<List<ObjectTypeItem>>("localGroup");
                LstLocalImageType = CommonMethod.GetCache<List<ObjectTypeItem>>("localImage");
            }
        }

        static List<T> GetServerConfigs<T>(string types)
        {
            var content = new List<T>();
            var result = string.Empty;
            try
            {
                result = CommonMethod.GetServerHtml("code.ashx?op=configs&type=" + types, CommonString.HostCode?.FullUrl, false, true);
                if (!string.IsNullOrEmpty(result))
                {
                    var lstStr = result.DeserializeJson<List<string>>();
                    foreach (var item in lstStr)
                    {
                        content.Add(item.DeserializeJson<T>());
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("GetServerConfig:" + result, oe);
            }
            return content;
        }

        public static T GetServerConfig<T>(string type)
        {
            var content = default(T);
            var result = string.Empty;
            try
            {
                result = CommonMethod.GetServerHtml("code.ashx?op=config&type=" + type, CommonString.HostCode?.FullUrl, false, true);
                if (!string.IsNullOrEmpty(result))
                    content = result.DeserializeJson<T>();
                var count = content == null ? 0 : BoxUtil.GetInt32FromObject(content.GetType().GetProperty("Count").GetValue(content)?.ToString());
                if (count <= 0)
                {
                    //加载本地缓存
                    content = CommonMethod.GetCache<T>(type);
                }
                else
                {
                    // 缓存到本地
                    CommonMethod.SaveCache(content, type);
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("GetServerConfig:" + result, oe);
            }
            return content;
        }

        public static List<ObjectTypeItem> LstLocalOcrType = new List<ObjectTypeItem>();

        public static List<ObjectTypeItem> LstLocalImageType = new List<ObjectTypeItem>();

        public static List<ObjectTypeItem> LstServerOcrGroup = new List<ObjectTypeItem>
        {
            new ObjectTypeItem{Code =0,Name ="不限"},
            new ObjectTypeItem{Code =1,Name ="百度"},
            new ObjectTypeItem{Code =2,Name ="腾讯"},
            new ObjectTypeItem{Code =3,Name ="阿里"},
            new ObjectTypeItem{Code =4,Name ="有道"},
            new ObjectTypeItem{Code =5,Name ="搜狗"},
            new ObjectTypeItem{Code =6,Name ="讯飞"},
            new ObjectTypeItem{Code =7,Name ="迅捷"},
            new ObjectTypeItem{Code =8,Name ="VIVO"},
            new ObjectTypeItem{Code =10,Name ="学而思"},
            new ObjectTypeItem{Code =11,Name ="汉王"},
            new ObjectTypeItem{Code =12,Name ="合合"},
            new ObjectTypeItem{Code =13,Name ="字节"},
            new ObjectTypeItem{Code =15,Name ="MathPix"},
            new ObjectTypeItem{Code =16,Name ="AWS"}
        };

        public static int GetGroupByName(string name)
        {
            return LstServerOcrGroup.FirstOrDefault(p => Equals(p.Name, name))?.Code ?? 0;
        }

        public static List<ObjectTypeItem> InitInstalledOcr()
        {
            var lstInstalled = new List<ObjectTypeItem>();
            if (lstInstalled.Count > 0)
            {
                var lstOld = lstInstalled.Where(p => !LstLocalOcrType.Any(q => Equals(p.Code, q.Code))).ToList();
                if (lstOld.Count > 0)
                    LstLocalOcrType.AddRange(lstOld);
            }
            return lstInstalled;
        }
    }

    [Obfuscation]
    public class ObjectTypeItem
    {
        [Obfuscation]
        public string Name { get; set; }
        [Obfuscation]
        public int Code { get; set; }
        [Obfuscation]
        public string Desc { get; set; }
        [Obfuscation]
        public string Remark { get; set; }
        [Obfuscation]
        public string DescUrl { get; set; }
        [Obfuscation]
        public string UpdateUrl { get; set; }
        [Obfuscation]
        public string AppPath { get; set; }
        [Obfuscation]
        public DateTime Date { get; set; }
        [Obfuscation]
        public bool NeedInstall { get; set; } = true;
        [Obfuscation]
        public bool Config { get; set; }
    }

    public enum OcrType
    {
        文本 = 0,
        竖排 = 1,
        表格 = 2,
        公式 = 3,
        翻译 = 4
    }

    public enum OcrModel
    {
        本地加网络 = 0,
        仅网络识别 = 1,
        仅本地识别 = 2,
    }
}