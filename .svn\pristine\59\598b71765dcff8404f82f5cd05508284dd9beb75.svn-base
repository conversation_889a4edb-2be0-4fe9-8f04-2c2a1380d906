﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Design;
using System.Drawing.Imaging;
using System.Drawing.Text;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace OCRTools
{

    [ToolboxBitmap(typeof(TabControl))]
    [Designer("DSkin.Design.MyTabControlDesigner,DSkin.Design, Version=16.6.3.21, Culture=neutral, PublicKeyToken=null")]
    [ToolboxItem(true)]
    public class MyTabControl : TabControl, ILayered, ILayeredContainer
    {
        private bool bool_0 = false;

        private int int_0 = -1;

        private int int_1 = -1;

        private TabControlButton gclass3_0;

        internal IntPtr intptr_0 = IntPtr.Zero;

        private Class40 class40_0;

        private Color color_0 = Color.Black;

        private Color color_1 = Color.Gray;

        private Color color_2 = Color.White;

        private Color color_3 = Color.Black;

        private List<Control> list_0 = new List<Control>();

        private TextRenderingHint textRenderingHint_0 = TextRenderingHint.SystemDefault;

        private Color[] color_4 = new Color[1]
        {
        Color.FromArgb(100, Color.Gray)
        };

        private Color[] color_5 = new Color[1]
        {
        Color.FromArgb(224, 224, 224)
        };

        private Color[] color_6 = new Color[1]
        {
        Color.White
        };

        private Rectangle rectangle_0 = default(Rectangle);

        private Size size_0 = new Size(20, 20);

        private ePageImagePosition ePageImagePosition_0 = ePageImagePosition.Left;

        private ContentAlignment contentAlignment_0 = ContentAlignment.MiddleCenter;

        private int int_2 = 4;

        private Point point_0 = new Point(0, 0);

        private Color color_7 = Color.Black;

        private Color color_8 = Color.Black;

        private Color color_9 = Color.Black;

        private bool bool_1 = false;

        private Point wOfgwPienT = default(Point);

        private StringFormat stringFormat_0 = new StringFormat
        {
            LineAlignment = StringAlignment.Center,
            Alignment = StringAlignment.Center,
            Trimming = StringTrimming.EllipsisCharacter
        };

        private DuiBaseControl duiBaseControl_0;

        private Image image_0;

        private Image image_1;

        private Image image_2;

        private int int_3 = -1;

        private Color color_10 = Color.Transparent;

        private int int_4 = 1;


        [DesignerSerializationVisibility(DesignerSerializationVisibility.Content)]
        [Description("背景渲染")]
        public DuiBackgroundRender DuiBackgroundRender => InnerDuiControl.BackgroundRender;

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public IImageEffect ImageEffect
        {
            get
            {
                return InnerDuiControl.ImageEffect;
            }
            set
            {
                InnerDuiControl.ImageEffect = value;
            }
        }


        [DefaultValue(false)]
        [Description("是否启用关闭按钮")]
        public bool EnabledCloseButton
        {
            get
            {
                return bool_0;
            }
            set
            {
                if (bool_0 != value)
                {
                    bool_0 = value;
                    Invalidate();
                }
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Content)]
        [Description("关闭按钮")]
        public TabControlButton CloseButton
        {
            get
            {
                if (gclass3_0 == null)
                {
                    gclass3_0 = new TabControlButton(this);
                }
                return gclass3_0;
            }
        }


        [Description("选项卡切换按钮箭头颜色")]
        public Color UpdownBtnArrowNormalColor
        {
            get
            {
                return color_0;
            }
            set
            {
                if (color_0 != value)
                {
                    color_0 = value;
                }
            }
        }

        [Description("选项卡切换按钮箭头颜色")]

        public Color UpdownBtnArrowPressColor
        {
            get
            {
                return color_1;
            }
            set
            {
                color_1 = value;
            }
        }

        [Description("选项卡切换按钮背景颜色")]

        public Color UpdownBtnBackColor
        {
            get
            {
                return color_2;
            }
            set
            {
                color_2 = value;
            }
        }

        [Description("选项卡切换按钮边框颜色")]

        public Color UpdownBtnBorderColor
        {
            get
            {
                return color_3;
            }
            set
            {
                color_3 = value;
            }
        }

        [DefaultValue(typeof(TextRenderingHint), "SystemDefault")]
        [Description("文本渲染模式")]
        public TextRenderingHint TextRenderMode
        {
            get
            {
                return textRenderingHint_0;
            }
            set
            {
                if (textRenderingHint_0 != value)
                {
                    textRenderingHint_0 = value;
                    Invalidate();
                }
            }
        }

        [Description("正常状态下的选项卡渐变背景色")]
        public Color[] NormalBackColors
        {
            get
            {
                return color_4;
            }
            set
            {
                if (color_4 != value)
                {
                    color_4 = value;
                    Invalidate();
                }
            }
        }

        [Description("鼠标移入选项卡的渐变背景色")]
        public Color[] HoverBackColors
        {
            get
            {
                return color_5;
            }
            set
            {
                color_5 = value;
            }
        }


        [Description("被选中的选项卡渐变背景色")]
        public Color[] SelectedBackColors
        {
            get
            {
                return color_6;
            }
            set
            {
                if (color_6 != value)
                {
                    color_6 = value;
                    Invalidate();
                }
            }
        }

        [DefaultValue(typeof(Rectangle), "0,0,0,0")]
        [Description("图片绘制区域")]
        public Rectangle ImageRectangle
        {
            get
            {
                return rectangle_0;
            }
            set
            {
                if (rectangle_0 != value)
                {
                    rectangle_0 = value;
                    Invalidate();
                }
            }
        }

        [DefaultValue(typeof(Size), "20,20")]
        [Description("选项卡上图标的大小")]
        public Size ImgSize
        {
            get
            {
                return size_0;
            }
            set
            {
                if (size_0 != value)
                {
                    size_0 = value;
                    Invalidate();
                }
            }
        }

        [Description("指定选项卡上图像与文本的对齐方式")]
        [DefaultValue(typeof(ePageImagePosition), "Overlay")]
        public ePageImagePosition PageImagePosition
        {
            get
            {
                return ePageImagePosition_0;
            }
            set
            {
                ePageImagePosition_0 = value;
                Invalidate();
            }
        }

        [Description("将在选项卡标签上显示的文本的对齐方式")]
        [DefaultValue(typeof(ContentAlignment), "MiddleCenter")]
        public ContentAlignment PageTextAlign
        {
            get
            {
                return contentAlignment_0;
            }
            set
            {
                contentAlignment_0 = value;
                switch (contentAlignment_0)
                {
                    case ContentAlignment.MiddleCenter:
                        stringFormat_0.LineAlignment = StringAlignment.Center;
                        stringFormat_0.Alignment = StringAlignment.Center;
                        break;
                    case ContentAlignment.MiddleLeft:
                        stringFormat_0.LineAlignment = StringAlignment.Center;
                        stringFormat_0.Alignment = StringAlignment.Near;
                        break;
                    case ContentAlignment.TopLeft:
                        stringFormat_0.LineAlignment = StringAlignment.Near;
                        stringFormat_0.Alignment = StringAlignment.Near;
                        break;
                    case ContentAlignment.TopCenter:
                        stringFormat_0.LineAlignment = StringAlignment.Near;
                        stringFormat_0.Alignment = StringAlignment.Center;
                        break;
                    case ContentAlignment.TopRight:
                        stringFormat_0.LineAlignment = StringAlignment.Near;
                        stringFormat_0.Alignment = StringAlignment.Far;
                        break;
                    case ContentAlignment.BottomLeft:
                        stringFormat_0.LineAlignment = StringAlignment.Far;
                        stringFormat_0.Alignment = StringAlignment.Near;
                        break;
                    case ContentAlignment.MiddleRight:
                        stringFormat_0.LineAlignment = StringAlignment.Center;
                        stringFormat_0.Alignment = StringAlignment.Far;
                        break;
                    case ContentAlignment.BottomRight:
                        stringFormat_0.LineAlignment = StringAlignment.Far;
                        stringFormat_0.Alignment = StringAlignment.Far;
                        break;
                    case ContentAlignment.BottomCenter:
                        stringFormat_0.LineAlignment = StringAlignment.Far;
                        stringFormat_0.Alignment = StringAlignment.Center;
                        break;
                }
                Invalidate(method_14());
            }
        }


        [Description("选项卡文本与图标之间的间隙")]
        [DefaultValue(4)]
        public int ImgTxtSpace
        {
            get
            {
                return int_2;
            }
            set
            {
                int_2 = value;
                Invalidate();
            }
        }


        [Description("Page图标文本整体偏移。")]
        [DefaultValue(typeof(Point), "0,0")]
        public Point ImgTxtOffset
        {
            get
            {
                return point_0;
            }
            set
            {
                point_0 = value;
                Invalidate();
            }
        }

        [Description("Page标签默认时字体色")]
        [Category("PageTxt")]
        [DefaultValue(typeof(Color), "0,0,0")]
        public Color PageNormlTxtColor
        {
            get
            {
                return color_7;
            }
            set
            {
                if (color_7 != value)
                {
                    color_7 = value;
                    Invalidate();
                }
            }
        }

        [Description("Page标签悬浮时字体色")]
        [DefaultValue(typeof(Color), "0,0,0")]
        [Category("PageTxt")]
        public Color PageHoverTxtColor
        {
            get
            {
                return color_8;
            }
            set
            {
                if (color_8 != value)
                {
                    color_8 = value;
                    Invalidate();
                }
            }
        }

        [Description("Page标签按下时字体色")]
        [DefaultValue(typeof(Color), "0,0,0")]
        [Category("PageTxt")]
        public Color PageDownTxtColor
        {
            get
            {
                return color_9;
            }
            set
            {
                if (color_9 != value)
                {
                    color_9 = value;
                    Invalidate();
                }
            }
        }

        [DefaultValue(false)]
        [Description("是否开启Tab图标三效状态切换")]

        public bool IcoStateSwitch
        {
            get
            {
                return bool_1;
            }
            set
            {
                if (bool_1 != value)
                {
                    bool_1 = value;
                    Invalidate();
                }
            }
        }

        [Description("文本偏移")]
        [DefaultValue(typeof(Point), "0,0")]
        [Category("PageTxt")]
        public Point TextOffset
        {
            get
            {
                return wOfgwPienT;
            }
            set
            {
                wOfgwPienT = value;
            }
        }

        [Browsable(false)]
        public DuiBaseControl InnerDuiControl
        {
            get
            {
                if (duiBaseControl_0 == null)
                {
                    duiBaseControl_0 = DuiControl;
                }
                return duiBaseControl_0;
            }
        }

        protected virtual DuiBaseControl DuiControl => new DuiBaseControl();


        [DesignerSerializationVisibility(DesignerSerializationVisibility.Content)]
        [Description("DirectUI控件集合")]
        public DuiControlCollection DuiControlCollection_0 => InnerDuiControl.Controls;

        [Editor(typeof(DSkinTabPageCollectionEditor), typeof(UITypeEditor))]
        [Description("tabPage集合")]
        public new TabPageCollection TabPages => base.TabPages;

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public virtual Bitmap Canvas
        {
            get
            {
                return InnerDuiControl.Canvas;
            }
            set
            {
                InnerDuiControl.Canvas = value;
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        public ImageAttributes ImageAttribute
        {
            get
            {
                return InnerDuiControl.ImageAttribute;
            }
            set
            {
                InnerDuiControl.ImageAttribute = value;
            }
        }

        [Browsable(false)]
        public bool IsLayeredMode { get; set; }

        [Description("项目背景图片")]

        public Image ItemBackgroundImage
        {
            get
            {
                return image_0;
            }
            set
            {
                if (image_0 != value)
                {
                    image_0 = value;
                    Invalidate();
                }
            }
        }

        [Description("鼠标移入时项目背景图片")]

        public Image ItemBackgroundImageHover
        {
            get
            {
                return image_1;
            }
            set
            {
                image_1 = value;
            }
        }


        [Description("被选中的项目背景图片")]
        public Image ItemBackgroundImageSelected
        {
            get
            {
                return image_2;
            }
            set
            {
                if (image_2 != value)
                {
                    image_2 = value;
                    Invalidate();
                }
            }
        }

        public new Cursor Cursor
        {
            get
            {
                return InnerDuiControl.Cursor;
            }
            set
            {
                Cursor cursor3 = base.Cursor = (InnerDuiControl.Cursor = value);
            }
        }

        [EditorBrowsable(EditorBrowsableState.Always)]
        [Browsable(true)]
        [DefaultValue(typeof(Color), "Transparent")]
        public override Color BackColor
        {
            get
            {
                return InnerDuiControl.BackColor;
            }
            set
            {
                if (InnerDuiControl.BackColor != value)
                {
                    InnerDuiControl.BackColor = value;
                    base.Invalidate();
                }
            }
        }

        [Browsable(true)]
        [EditorBrowsable(EditorBrowsableState.Always)]
        public override Color ForeColor
        {
            get
            {
                return InnerDuiControl.ForeColor;
            }
            set
            {
                Color color3 = InnerDuiControl.ForeColor = (base.ForeColor = value);
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        public new int SelectedIndex
        {
            get
            {
                return base.SelectedIndex;
            }
            set
            {
                base.SelectedIndex = value;
            }
        }

        [EditorBrowsable(EditorBrowsableState.Always)]
        [Browsable(true)]
        public override Image BackgroundImage
        {
            get
            {
                return InnerDuiControl.BackgroundImage;
            }
            set
            {
                InnerDuiControl.BackgroundImage = value;
            }
        }

        [Browsable(true)]
        [EditorBrowsable(EditorBrowsableState.Always)]
        public override ImageLayout BackgroundImageLayout
        {
            get
            {
                return InnerDuiControl.BackgroundImageLayout;
            }
            set
            {
                InnerDuiControl.BackgroundImageLayout = value;
            }
        }

        [EditorBrowsable(EditorBrowsableState.Always)]
        [Browsable(true)]
        public override Font Font
        {
            get
            {
                return InnerDuiControl.Font;
            }
            set
            {
                Font font3 = InnerDuiControl.Font = (base.Font = value);
            }
        }

        [TypeConverter(typeof(BordersPropertyOrderConverter))]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Content)]

        [Description("控件边框")]
        public Borders Borders => InnerDuiControl.Borders;


        [Description("位图缓存，位图缓存不适合大尺寸的控件")]
        public bool BitmapCache
        {
            get
            {
                return InnerDuiControl.BitmapCache;
            }
            set
            {
                InnerDuiControl.BitmapCache = value;
            }
        }

        public override Rectangle DisplayRectangle
        {
            get
            {
                Rectangle displayRectangle = base.DisplayRectangle;
                return new Rectangle(displayRectangle.Left - 4, displayRectangle.Top - 4, displayRectangle.Width + 8, displayRectangle.Height + 8);
            }
        }

        [DefaultValue(typeof(Color), "Transparent")]
        [Description("分割线颜色")]
        public Color DividingLineColor
        {
            get
            {
                return color_10;
            }
            set
            {
                color_10 = value;
                Invalidate();
            }
        }

        [Description("分割线宽度")]
        [DefaultValue(1)]
        public int DividingLineWidth
        {
            get
            {
                return int_4;
            }
            set
            {
                int_4 = value;
                Invalidate();
            }
        }

        [Description("层控件需要重绘时发生")]
        public event EventHandler<PaintEventArgs> LayeredPaintEvent;

        public event EventHandler<InvalidateEventArgs> LayeredInvalidated;

        public event EventHandler<DSkinPageRemovedEventArgs> PageRemoved;

        public MyTabControl()
        {
            SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.SupportsTransparentBackColor | ControlStyles.AllPaintingInWmPaint | ControlStyles.OptimizedDoubleBuffer, value: true);
            UpdateStyles();
            base.SizeMode = TabSizeMode.Fixed;
            BackColor = Color.Transparent;
            InnerDuiControl.ParentInvalidate = false;
            InnerDuiControl.PrePaint += method_3;
            InnerDuiControl.Invalidated += method_0;
        }

        private void method_0(object sender, InvalidateEventArgs e)
        {
            OnLayeredInvalidated(e);
            if (!base.DesignMode && !IsLayeredMode)
            {
                base.Invalidate(e.InvalidRect);
            }
        }

        private int AhPpzgMoyA()
        {
            return int_1;
        }

        private void method_1(int value)
        {
            if (int_1 != value)
            {
                int_1 = value;
                if (int_1 != -1)
                {
                    Invalidate(GetTabRectClient(int_1));
                }
                else
                {
                    Invalidate(method_14());
                }
            }
        }

        internal Class40 method_2()
        {
            if (class40_0 == null)
            {
                class40_0 = new Class40(this);
            }
            return class40_0;
        }

        private void method_3(object sender, PaintEventArgs e)
        {
            Graphics graphics = e.Graphics;
            graphics.SetClip(e.ClipRectangle);
            if (base.ItemSize.Width > 1 && base.ItemSize.Height > 1)
            {
                for (int i = 0; i < base.TabCount; i++)
                {
                    Rectangle tabRectClient = GetTabRectClient(i);
                    TabPage tabPage = TabPages[i];
                    Rectangle tabRect = GetTabRect(i);
                    if (!tabRectClient.IntersectsWith(e.ClipRectangle))
                    {
                        continue;
                    }
                    Image image = image_2;
                    if (image_2 != null)
                    {
                        image = ((SelectedIndex == i) ? image_2 : image_0);
                    }
                    if (image_1 != null && int_3 == i && i != SelectedIndex)
                    {
                        image = image_1;
                    }
                    Color baseColor = PageNormlTxtColor;
                    bool bool_ = false;
                    bool bool_2 = false;
                    if (image != null)
                    {
                        graphics.DrawImage(image, tabRectClient);
                    }
                    else if (SelectedIndex == i)
                    {
                        ControlRender.DrawGradientColor(graphics, SelectedBackColors, 90f, tabRectClient);
                    }
                    else if (int_3 != i)
                    {
                        ControlRender.DrawGradientColor(graphics, NormalBackColors, 90f, tabRectClient);
                    }
                    else
                    {
                        ControlRender.DrawGradientColor(graphics, HoverBackColors, 90f, tabRectClient);
                    }
                    if (i != base.TabCount - 1)
                    {
                        using (Pen pen = new Pen(color_10, int_4))
                        {
                            switch (base.Alignment)
                            {
                                case TabAlignment.Top:
                                    graphics.DrawLine(pen, new Point(tabRectClient.Right, tabRectClient.Top), new Point(tabRectClient.Right, tabRectClient.Bottom));
                                    break;
                                case TabAlignment.Bottom:
                                    graphics.DrawLine(pen, new Point(tabRectClient.Right, tabRectClient.Top), new Point(tabRectClient.Right, tabRectClient.Bottom));
                                    break;
                                case TabAlignment.Left:
                                    graphics.DrawLine(pen, new Point(tabRectClient.X, tabRectClient.Bottom), new Point(tabRectClient.Right, tabRectClient.Bottom));
                                    break;
                                case TabAlignment.Right:
                                    graphics.DrawLine(pen, new Point(tabRectClient.X, tabRectClient.Bottom), new Point(tabRectClient.Right, tabRectClient.Bottom));
                                    break;
                            }
                        }
                    }
                    if (SelectedIndex == i)
                    {
                        bool_ = true;
                        baseColor = PageDownTxtColor;
                    }
                    else if (int_3 == i)
                    {
                        bool_2 = true;
                        baseColor = PageHoverTxtColor;
                    }
                    Image image2 = null;
                    if (tabPage is DSkinTabPage)
                    {
                        DSkinTabPage dSkinTabPage = (DSkinTabPage)tabPage;
                        if (dSkinTabPage.TabItemImage != null)
                        {
                            image2 = dSkinTabPage.TabItemImage;
                        }
                    }
                    if (image2 == null)
                    {
                        if (tabPage.ImageIndex != -1)
                        {
                            if (base.ImageList != null)
                            {
                                image2 = base.ImageList.Images[tabPage.ImageIndex];
                            }
                        }
                        else if (tabPage.ImageKey != null && base.ImageList != null)
                        {
                            image2 = base.ImageList.Images[tabPage.ImageKey];
                        }
                    }
                    method_5(tabPage, tabRect, out Rectangle rectangle_, out Rectangle rectangle_2, image2);
                    rectangle_.X += ImgTxtOffset.X;
                    rectangle_.Y += ImgTxtOffset.Y;
                    rectangle_2.X += ImgTxtOffset.X + wOfgwPienT.X;
                    rectangle_2.Y += ImgTxtOffset.Y + wOfgwPienT.Y;
                    method_4(graphics, tabPage, rectangle_, bool_, bool_2, image2);
                    graphics.TextRenderingHint = textRenderingHint_0;
                    using (SolidBrush brush = new SolidBrush(Color.FromArgb((baseColor.A == byte.MaxValue) ? 254 : baseColor.A, baseColor)))
                    {
                        Rectangle r = rectangle_2;
                        graphics.TextRenderingHint = textRenderingHint_0;
                        graphics.DrawString(tabPage.Text, tabPage.Font, brush, r, stringFormat_0);
                    }
                    if (!bool_0)
                    {
                        continue;
                    }
                    bool flag = true;
                    if (tabPage is DSkinTabPage)
                    {
                        flag = (tabPage as DSkinTabPage).CloseButtonVisble;
                    }
                    if (flag)
                    {
                        Rectangle rect = new Rectangle(tabRectClient.Right - CloseButton.LocationOffset.X - gclass3_0.Size.Width - 1, tabRectClient.Top + gclass3_0.LocationOffset.Y, gclass3_0.Size.Width, gclass3_0.Size.Height);
                        ControlStates state = ControlStates.Normal;
                        if (int_1 == i)
                        {
                            state = ControlStates.Hover;
                        }
                        if (int_0 == i)
                        {
                            state = ControlStates.Pressed;
                        }
                        CloseButton.DrawButton(graphics, rect, state);
                    }
                }
            }
            if (!base.DesignMode && IsLayeredMode && base.SelectedTab != null)
            {
                TabPage selectedTab = base.SelectedTab;
                Control.ControlCollection controls = base.SelectedTab.Controls;
                if (base.SelectedTab.BackColor != Color.Transparent)
                {
                    using (SolidBrush brush2 = new SolidBrush(base.SelectedTab.BackColor))
                    {
                        graphics.FillRectangle(brush2, base.SelectedTab.Bounds);
                    }
                }
                if (selectedTab.BackgroundImage != null)
                {
                    ControlRender.DrawBackgroundImage(graphics, selectedTab.BackgroundImageLayout, selectedTab.BackgroundImage, selectedTab.Bounds);
                }
                for (int num = controls.Count - 1; num >= 0; num--)
                {
                    Control control = controls[num];
                    Rectangle bounds = control.Bounds;
                    bounds.Offset(base.SelectedTab.Location);
                    ControlRender.smethod_0(control, bounds, graphics, e.ClipRectangle, base.SelectedTab.Bounds);
                }
            }
            if (base.ItemSize.Width > 1 && base.ItemSize.Height > 1)
            {
                method_6(graphics, e.ClipRectangle);
            }
            OnLayeredPaint(e);
        }

        private void method_4(Graphics graphics_0, TabPage tabPage_0, Rectangle rectangle_1, bool bool_2, bool bool_3, Image image_3)
        {
            if (image_3 != null)
            {
                if (IcoStateSwitch)
                {
                    int num = image_3.Width / 3;
                    int height = image_3.Height;
                    Rectangle srcRect = bool_2 ? new Rectangle(2 * num, 0, num, height) : (bool_3 ? new Rectangle(num, 0, num, height) : new Rectangle(0, 0, num, height));
                    graphics_0.DrawImage(image_3, rectangle_1, srcRect, GraphicsUnit.Pixel);
                }
                else
                {
                    graphics_0.DrawImage(image_3, rectangle_1);
                }
            }
        }

        private void method_5(TabPage tabPage_0, Rectangle rectangle_1, out Rectangle rectangle_2, out Rectangle rectangle_3, Image image_3)
        {
            Size size;
            using (Graphics graphics = Graphics.FromHwnd(IntPtr.Zero))
            {
                SizeF sizeF = graphics.MeasureString(tabPage_0.Text, tabPage_0.Font);
                size = new Size((int)sizeF.Width, (int)sizeF.Height);
            }
            int num = (tabPage_0.Text.Length != 0) ? ImgTxtSpace : 0;
            rectangle_2 = Rectangle.Empty;
            rectangle_3 = Rectangle.Empty;
            if (image_3 == null)
            {
                rectangle_3 = rectangle_1;
                return;
            }
            switch (PageImagePosition)
            {
                case ePageImagePosition.Left:
                    if (PageTextAlign == ContentAlignment.BottomLeft || PageTextAlign == ContentAlignment.MiddleLeft || PageTextAlign == ContentAlignment.TopLeft)
                    {
                        rectangle_2 = new Rectangle(rectangle_1.X, rectangle_1.Y + (rectangle_1.Height - ImgSize.Height) / 2, ImgSize.Width, ImgSize.Height);
                        rectangle_3 = new Rectangle(rectangle_2.Right + num, rectangle_1.Y + (rectangle_1.Height - size.Height) / 2, rectangle_1.Width - ImgSize.Width - (rectangle_2.X - rectangle_1.X) - num, size.Height);
                    }
                    else if (PageTextAlign == ContentAlignment.BottomCenter || PageTextAlign == ContentAlignment.MiddleCenter || PageTextAlign == ContentAlignment.TopCenter)
                    {
                        rectangle_2 = new Rectangle(rectangle_1.X + (rectangle_1.Width - (ImgSize.Width + size.Width + num)) / 2, rectangle_1.Y + (rectangle_1.Height - ImgSize.Height) / 2, ImgSize.Width, ImgSize.Height);
                        rectangle_3 = new Rectangle(rectangle_2.Right + num, rectangle_1.Y + (rectangle_1.Height - size.Height) / 2, rectangle_1.Width - ImgSize.Width - (rectangle_2.X - rectangle_1.X) - num, size.Height);
                    }
                    else
                    {
                        rectangle_2 = new Rectangle(rectangle_1.X + rectangle_1.Width - (size.Width + num) - ImgSize.Width, rectangle_1.Y + (rectangle_1.Height - ImgSize.Height) / 2, ImgSize.Width, ImgSize.Height);
                        rectangle_3 = new Rectangle(rectangle_2.Right + num, rectangle_1.Y + (rectangle_1.Height - size.Height) / 2, size.Width, size.Height);
                    }
                    break;
                case ePageImagePosition.Right:
                    if (PageTextAlign == ContentAlignment.BottomLeft || PageTextAlign == ContentAlignment.MiddleLeft || PageTextAlign == ContentAlignment.TopLeft)
                    {
                        rectangle_2 = new Rectangle(rectangle_1.X + size.Width + num, rectangle_1.Y + (rectangle_1.Height - ImgSize.Height) / 2, ImgSize.Width, ImgSize.Height);
                        rectangle_3 = new Rectangle(rectangle_1.X, rectangle_1.Y + (rectangle_1.Height - size.Height) / 2, size.Width, size.Height);
                    }
                    else if (PageTextAlign == ContentAlignment.BottomCenter || PageTextAlign == ContentAlignment.MiddleCenter || PageTextAlign == ContentAlignment.TopCenter)
                    {
                        rectangle_2 = new Rectangle(rectangle_1.X + (rectangle_1.Width - (ImgSize.Width + size.Width + num)) / 2 + (size.Width + num), rectangle_1.Y + (rectangle_1.Height - ImgSize.Height) / 2, ImgSize.Width, ImgSize.Height);
                        rectangle_3 = new Rectangle(rectangle_2.X - size.Width - num, rectangle_1.Y + (rectangle_1.Height - size.Height) / 2, size.Width, size.Height);
                    }
                    else
                    {
                        rectangle_2 = new Rectangle(rectangle_1.X + rectangle_1.Width - ImgSize.Width, rectangle_1.Y + (rectangle_1.Height - ImgSize.Height) / 2, ImgSize.Width, ImgSize.Height);
                        rectangle_3 = new Rectangle(rectangle_2.X - size.Width - num, rectangle_1.Y + (rectangle_1.Height - size.Height) / 2, size.Width, size.Height);
                    }
                    break;
                case ePageImagePosition.Top:
                    rectangle_2 = new Rectangle(rectangle_1.X + (rectangle_1.Width - ImgSize.Width) / 2, rectangle_1.Y + (rectangle_1.Height - (ImgSize.Height + size.Height + num)) / 2, ImgSize.Width, ImgSize.Height);
                    rectangle_3 = new Rectangle(rectangle_1.X, rectangle_2.Bottom + num, rectangle_1.Width, rectangle_1.Height - (rectangle_2.Bottom + num - rectangle_1.Top));
                    break;
                case ePageImagePosition.Bottom:
                    rectangle_2 = new Rectangle(rectangle_1.X + (rectangle_1.Width - ImgSize.Width) / 2, rectangle_1.Y + (rectangle_1.Height - (ImgSize.Height + size.Height + num)) / 2 + (size.Height + num), ImgSize.Width, ImgSize.Height);
                    rectangle_3 = new Rectangle(rectangle_1.X, rectangle_1.Y, rectangle_1.Width, rectangle_2.Y - rectangle_1.Y - num);
                    break;
            }
        }

        private void method_6(Graphics graphics_0, Rectangle rectangle_1)
        {
            if (intptr_0 != IntPtr.Zero && rectangle_1.IntersectsWith(method_2().method_8()) && base.ItemSize.Width * base.TabCount > base.Width && !base.Multiline)
            {
                class40_0.method_1(color_0);
                class40_0.method_3(color_1);
                class40_0.method_5(color_2);
                class40_0.method_7(color_3);
                Point location = method_2().method_8().Location;
                graphics_0.TranslateTransform(location.X, location.Y);
                method_2().method_10(graphics_0);
                graphics_0.TranslateTransform(-location.X, -location.Y);
            }
        }

        public void PaintControl(Graphics g, Rectangle invalidateRect)
        {
            InnerDuiControl.PaintControl(g, invalidateRect);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            if (base.DesignMode || !IsLayeredMode)
            {
                if (BitmapCache)
                {
                    Graphics graphics = e.Graphics;
                    graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighSpeed;
                    graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighSpeed;
                    graphics.SetClip(e.ClipRectangle);
                    graphics.DrawImage(Canvas, 0, 0);
                }
                else
                {
                    PaintControl(e.Graphics, e.ClipRectangle);
                }
            }
        }

        public new void Invalidate()
        {
            InnerDuiControl.Invalidate();
        }

        public new void Invalidate(Rectangle rect)
        {
            InnerDuiControl.Invalidate(rect);
        }

        protected override void OnSelectedIndexChanged(EventArgs e)
        {
            if (base.SelectedTab != null)
            {
                Control.ControlCollection controls = base.SelectedTab.Controls;
                method_13(controls);
                method_12(controls);
            }
            base.OnSelectedIndexChanged(e);
            Invalidate();
        }

        private void method_7(object sender, InvalidateEventArgs e)
        {
            Control control = sender as Control;
            if (!base.IsDisposed && base.IsHandleCreated && IsLayeredMode && !base.DesignMode && control.Visible)
            {
                Rectangle rect = default(Rectangle);
                if (!base.IsDisposed && base.IsHandleCreated && !base.Disposing && base.SelectedTab != null && base.SelectedTab.Controls.Contains(sender as Control))
                {
                    rect = e.InvalidRect;
                    rect.Offset(control.Location);
                    rect.Offset(base.SelectedTab.Location);
                }
                if (!rect.IsEmpty)
                {
                    Invalidate(rect);
                }
            }
        }

        private void method_8(object sender, EventArgs e)
        {
            if (IsLayeredMode && !base.DesignMode)
            {
                Control control = sender as Control;
                Rectangle bounds = control.Bounds;
                bounds.Offset(base.SelectedTab.Location);
                Invalidate(bounds);
            }
        }

        private void method_9(object sender, EventArgs e)
        {
            if (IsLayeredMode && !base.DesignMode)
            {
                Invalidate();
            }
        }

        public Rectangle GetTabRectClient(int index)
        {
            Rectangle tabRect = GetTabRect(index);
            int x = (base.Alignment == TabAlignment.Right) ? 1 : (-2);
            int y = (base.Alignment == TabAlignment.Bottom) ? 1 : (-2);
            tabRect.Offset(x, y);
            return tabRect;
        }

        private void method_10(object sender, ControlEventArgs e)
        {
            method_12(((Control)sender).Controls);
        }

        private void method_11()
        {
            if (!base.DesignMode && base.IsHandleCreated)
            {
                intptr_0 = NativeMethods.FindWindowExA(base.Handle, IntPtr.Zero, "msctls_updown32", null);
            }
        }

        protected override void OnControlAdded(ControlEventArgs e)
        {
            base.OnControlAdded(e);
            e.Control.ControlAdded += method_10;
            method_12(e.Control.Controls);
            method_11();
            Invalidate();
        }

        protected override void OnControlRemoved(ControlEventArgs e)
        {
            base.OnControlRemoved(e);
            e.Control.ControlAdded -= method_10;
            Control.ControlCollection controls = e.Control.Controls;
            method_13(controls);
            method_11();
            Invalidate();
        }

        private void method_12(Control.ControlCollection controlCollection_0)
        {
            if (!base.DesignMode)
            {
                foreach (Control item in controlCollection_0)
                {
                    if (list_0.IndexOf(item) < 0)
                    {
                        list_0.Add(item);
                        if (item is ILayeredContainer)
                        {
                            ((ILayeredContainer)item).LayeredInvalidated += method_7;
                        }
                        else
                        {
                            item.Invalidated += method_7;
                        }
                        item.Move += method_9;
                        item.VisibleChanged += method_8;
                    }
                }
            }
        }

        private void method_13(Control.ControlCollection controlCollection_0)
        {
            if (!base.DesignMode)
            {
                foreach (Control item in controlCollection_0)
                {
                    list_0.Remove(item);
                    if (item is ILayeredContainer)
                    {
                        ((ILayeredContainer)item).LayeredInvalidated -= method_7;
                    }
                    else
                    {
                        item.Invalidated -= method_7;
                    }
                    item.Move -= method_9;
                    item.VisibleChanged -= method_8;
                }
            }
        }

        protected override void OnSizeChanged(EventArgs e)
        {
            InnerDuiControl.Size = base.Size;
            base.OnSizeChanged(e);
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            base.OnMouseMove(e);
            InnerDuiControl.TriggerMouseMove(e);
            int num = 0;
            while (true)
            {
                if (num < base.TabCount)
                {
                    Rectangle tabRectClient = GetTabRectClient(num);
                    if (tabRectClient.Contains(e.Location) && int_3 != num)
                    {
                        int_3 = num;
                        Rectangle rect = method_14();
                        Invalidate(rect);
                    }
                    Rectangle rectangle = new Rectangle(tabRectClient.Right - CloseButton.LocationOffset.X - gclass3_0.Size.Width - 1, tabRectClient.Top + gclass3_0.LocationOffset.Y, gclass3_0.Size.Width, gclass3_0.Size.Height);
                    if (rectangle.Contains(e.Location))
                    {
                        break;
                    }
                    num++;
                    continue;
                }
                method_1(-1);
                return;
            }
            method_1(num);
        }

        private Rectangle method_14()
        {
            Rectangle result = default(Rectangle);
            switch (base.Alignment)
            {
                case TabAlignment.Top:
                    result = new Rectangle(0, 0, base.Width, base.ItemSize.Height);
                    break;
                case TabAlignment.Bottom:
                    result = new Rectangle(0, base.Height - base.ItemSize.Height, base.Width, base.ItemSize.Height);
                    break;
                case TabAlignment.Left:
                    result = new Rectangle(0, 0, base.ItemSize.Height, base.Height);
                    break;
                case TabAlignment.Right:
                    result = new Rectangle(base.Width - base.ItemSize.Height, 0, base.ItemSize.Height, base.Height);
                    break;
            }
            return result;
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            InnerDuiControl.TriggerMouseLeave(e);
            base.OnMouseLeave(e);
            method_1(-1);
            if (int_3 != -1)
            {
                int_3 = -1;
                Rectangle rect = method_14();
                Invalidate(rect);
            }
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            InnerDuiControl.TriggerMouseDown(e);
            base.OnMouseDown(e);
            if (bool_0 && !base.DesignMode && int_1 != -1)
            {
                int_0 = AhPpzgMoyA();
                Invalidate(GetTabRectClient(int_1));
            }
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            InnerDuiControl.TriggerMouseUp(e);
            base.OnMouseUp(e);
            if (!bool_0 || base.DesignMode)
            {
                return;
            }
            int_0 = -1;
            if (AhPpzgMoyA() == -1)
            {
                return;
            }
            TabPage tabPage = TabPages[AhPpzgMoyA()];
            bool flag = true;
            if (tabPage is DSkinTabPage)
            {
                flag = (tabPage as DSkinTabPage).CloseButtonVisble;
            }
            if (tabPage != null && flag)
            {
                DSkinPageRemovedEventArgs dSkinPageRemovedEventArgs = new DSkinPageRemovedEventArgs(tabPage);
                OnPageRemoved(dSkinPageRemovedEventArgs);
                if (dSkinPageRemovedEventArgs.Handled)
                {
                    return;
                }
                TabPages.Remove(tabPage);
            }
            int_1 = -1;
        }

        protected override void OnMouseClick(MouseEventArgs e)
        {
            InnerDuiControl.TriggerMouseClick(e);
            base.OnMouseClick(e);
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            InnerDuiControl.TriggerMouseEnter(e);
            base.OnMouseEnter(e);
        }

        protected override void OnMouseDoubleClick(MouseEventArgs e)
        {
            InnerDuiControl.TriggerMouseDoubleClick(e);
            base.OnMouseDoubleClick(e);
        }

        protected override void OnVisibleChanged(EventArgs e)
        {
            base.OnVisibleChanged(e);
            Invalidate();
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            base.OnKeyDown(e);
            duiBaseControl_0.method_9(e);
        }

        protected override void OnKeyPress(KeyPressEventArgs e)
        {
            base.OnKeyPress(e);
            duiBaseControl_0.method_8(e);
        }

        protected override void OnKeyUp(KeyEventArgs e)
        {
            base.OnKeyUp(e);
            duiBaseControl_0.method_7(e);
        }

        protected override void OnPreviewKeyDown(PreviewKeyDownEventArgs e)
        {
            base.OnPreviewKeyDown(e);
            duiBaseControl_0.method_6(e);
        }

        protected override void OnMouseWheel(MouseEventArgs e)
        {
            base.OnMouseWheel(e);
            duiBaseControl_0.method_5(e);
        }

        protected override void OnCreateControl()
        {
            base.OnCreateControl();
            method_11();
        }

        protected virtual void OnLayeredPaint(PaintEventArgs e)
        {
            if (LayeredPaintEvent != null)
            {
                LayeredPaintEvent(this, e);
            }
        }

        protected virtual void OnLayeredInvalidated(InvalidateEventArgs e)
        {
            if (LayeredInvalidated != null)
            {
                LayeredInvalidated(this, e);
            }
        }

        public virtual void DisposeCanvas()
        {
            InnerDuiControl.DisposeCanvas();
        }

        protected override void Dispose(bool disposing)
        {
            InnerDuiControl.Dispose();
            base.Dispose(disposing);
        }

        protected virtual void OnPageRemoved(DSkinPageRemovedEventArgs e)
        {
            if (PageRemoved != null)
            {
                PageRemoved(this, e);
            }
        }
    }
    public class DSkinPageRemovedEventArgs : EventArgs
    {
        public TabPage Page
        {
            get;
            set;
        }

        public bool Handled
        {
            get;
            set;
        }

        public DSkinPageRemovedEventArgs(TabPage page)
        {
            Page = page;
            Handled = false;
        }
    }

    public interface ILayered
    {
        bool IsLayeredMode
        {
            get;
        }

        Bitmap Canvas
        {
            get;
            set;
        }

        ImageAttributes ImageAttribute
        {
            get;
            set;
        }

        bool BitmapCache
        {
            get;
            set;
        }

        IImageEffect ImageEffect
        {
            get;
            set;
        }

        event EventHandler<PaintEventArgs> LayeredPaintEvent;

        void DisposeCanvas();

        void PaintControl(Graphics g, Rectangle invalidateRect);
    }
    public interface IImageEffect
    {
        Bitmap DoImageEffect(Rectangle rect, Bitmap bmp);
    }
    public interface ILayeredContainer
    {
        event EventHandler<InvalidateEventArgs> LayeredInvalidated;
    }
    public enum ePageImagePosition
    {
        Left,
        Right,
        Top,
        Bottom
    }

    //public interface IDuiContainer
    //{
    //    DuiBaseControl InnerDuiControl
    //    {
    //        get;
    //    }
    //}
    [Designer(typeof(ScrollableControlDesigner))]
    public class DSkinTabPage : TabPage
    {
        private Rectangle rectangle_0 = default(Rectangle);

        private bool bool_0 = true;

        private Image image_0 = null;

        private bool bool_1 = true;

        [Browsable(false)]
        public bool IsLayeredMode { get; set; }

        [Browsable(false)]
        [Description("图片绘制区域")]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public Rectangle ImageRectangle
        {
            get
            {
                return rectangle_0;
            }
            set
            {
                if (rectangle_0 != value)
                {
                    rectangle_0 = value;
                    if (base.Parent != null)
                    {
                        base.Parent.Invalidate();
                    }
                }
            }
        }


        [Description("是否显示关闭按钮")]
        [DefaultValue(true)]
        public bool CloseButtonVisble
        {
            get
            {
                return bool_0;
            }
            set
            {
                if (bool_0 != value)
                {
                    bool_0 = value;
                    if (base.Parent != null)
                    {
                        base.Parent.Invalidate();
                    }
                }
            }
        }


        [Description("当前Tab选项卡图标")]
        public Image TabItemImage
        {
            get
            {
                return image_0;
            }
            set
            {
                if (image_0 != value)
                {
                    image_0 = value;
                    if (base.Parent != null)
                    {
                        base.Parent.Invalidate();
                    }
                }
            }
        }

        [DefaultValue(typeof(bool), "true")]
        [Description("是否能选中当前项")]
        public bool IsSelectTab
        {
            get
            {
                return bool_1;
            }
            set
            {
                bool_1 = value;
            }
        }

        [Browsable(true)]
        [DefaultValue(true)]
        public new bool Enabled
        {
            get
            {
                return base.Enabled;
            }
            set
            {
                base.Enabled = value;
            }
        }

        public DSkinTabPage()
        {
            Init();
            BackColor = Color.Transparent;
            base.UseVisualStyleBackColor = false;
            Dock = DockStyle.Fill;
        }

        public void Init()
        {
            SetStyle(ControlStyles.ContainerControl | ControlStyles.SupportsTransparentBackColor | ControlStyles.AllPaintingInWmPaint | ControlStyles.OptimizedDoubleBuffer, value: true);
        }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    public class TabControlButton
    {
        private MyTabControl owner;

        private Image image_0;

        private Image image_1;

        private Image image_2;

        private Size size_0 = new Size(15, 15);

        private Point point_0 = default(Point);

        private Color JaBpOqrEjb = Color.Black;

        private Color color_0 = Color.Black;

        private Color color_1 = Color.Black;

        private Color color_2 = Color.Transparent;

        private Color color_3 = Color.White;

        private Color color_4 = Color.FromArgb(250, 200, 0, 0);

        private int int_0 = 8;

        [Description("按钮图片")]
        [DefaultValue(null)]
        public Image NormalImage
        {
            get
            {
                return image_0;
            }
            set
            {
                if (image_0 != value)
                {
                    image_0 = value;
                    owner.Invalidate();
                }
            }
        }

        [Description("按钮鼠标移入图片")]
        [DefaultValue(null)]
        public Image HoverImage
        {
            get
            {
                return image_1;
            }
            set
            {
                image_1 = value;
            }
        }

        [DefaultValue(null)]
        [Description("按钮鼠标按下时的图片")]
        public Image PressImage
        {
            get
            {
                return image_2;
            }
            set
            {
                image_2 = value;
            }
        }

        [Description("按钮大小")]
        [DefaultValue(typeof(Size), "15,15")]
        public Size Size
        {
            get
            {
                return size_0;
            }
            set
            {
                if (size_0 != value)
                {
                    size_0 = value;
                    owner.Invalidate();
                }
            }
        }

        [Description("按钮位置偏移")]
        [DefaultValue(typeof(Point), "0,0")]
        public Point LocationOffset
        {
            get
            {
                return point_0;
            }
            set
            {
                if (point_0 != value)
                {
                    point_0 = value;
                    owner.Invalidate();
                }
            }
        }

        [Description("按钮颜色")]
        [DefaultValue(typeof(Color), "Black")]
        public Color NormalColor
        {
            get
            {
                return JaBpOqrEjb;
            }
            set
            {
                if (JaBpOqrEjb != value)
                {
                    JaBpOqrEjb = value;
                    owner.Invalidate();
                }
            }
        }

        [Description("按钮颜色")]
        [DefaultValue(typeof(Color), "Black")]
        public Color HoverColor
        {
            get
            {
                return color_0;
            }
            set
            {
                color_0 = value;
            }
        }

        [Description("按钮颜色")]
        [DefaultValue(typeof(Color), "Black")]
        public Color PressColor
        {
            get
            {
                return color_1;
            }
            set
            {
                color_1 = value;
            }
        }

        [DefaultValue(typeof(Color), "Transparent")]
        [Description("按钮背景色")]
        public Color NormalBackColor
        {
            get
            {
                return color_2;
            }
            set
            {
                if (color_2 != value)
                {
                    color_2 = value;
                    owner.Invalidate();
                }
            }
        }

        [Description("按钮鼠标移入背景色")]
        [DefaultValue(typeof(Color), "White")]
        public Color HoverBackColor
        {
            get
            {
                return color_3;
            }
            set
            {
                color_3 = value;
            }
        }

        [Description("按钮鼠标按下背景色")]
        [DefaultValue(typeof(Color), "250, 200, 0, 0")]
        public Color PressBackColor
        {
            get
            {
                return color_4;
            }
            set
            {
                color_4 = value;
            }
        }

        public TabControlButton(MyTabControl owner)
        {
            this.owner = owner;
        }

        public void DrawButton(Graphics g, Rectangle rect, ControlStates state)
        {
            Color jaBpOqrEjb = JaBpOqrEjb;
            Color color = color_2;
            Image image = image_0;
            switch (state)
            {
                case ControlStates.Hover:
                    jaBpOqrEjb = color_0;
                    color = color_3;
                    image = image_1;
                    break;
                case ControlStates.Pressed:
                    jaBpOqrEjb = color_1;
                    color = color_4;
                    image = image_2;
                    break;
                case ControlStates.Normal:
                case ControlStates.Focused:
                    jaBpOqrEjb = JaBpOqrEjb;
                    color = color_2;
                    image = image_0;
                    break;
            }
            if (image != null)
            {
                g.DrawImage(image, rect);
                return;
            }
            using (SolidBrush brush = new SolidBrush(color))
            {
                g.FillRectangle(brush, rect);
            }
            using (Pen pen = new Pen(jaBpOqrEjb, 2f))
            {
                g.DrawLine(pen, new Point(rect.X + (size_0.Width - int_0) / 2, rect.Y + (size_0.Height - int_0) / 2), new Point(rect.X + size_0.Width - (size_0.Width - int_0) / 2, rect.Y + size_0.Height - (size_0.Height - int_0) / 2));
                g.DrawLine(pen, new Point(rect.X + (size_0.Width - int_0) / 2, rect.Y + size_0.Height - (size_0.Height - int_0) / 2), new Point(rect.X + size_0.Width - (size_0.Width - int_0) / 2, rect.Y + (size_0.Height - int_0) / 2));
            }
        }
    }
    public enum ControlStates
    {
        Normal,
        Hover,
        Pressed,
        Focused
    }

}
