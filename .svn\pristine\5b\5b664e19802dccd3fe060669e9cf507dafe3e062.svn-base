// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System;
using System.Globalization;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Automation;
using UIAutomationClient;

namespace UIAComWrapperInternal
{
    internal class Utility
    {
        internal static int ConvertToInt(bool b)
        {
            return b ? 1 : 0;
        }

        internal static AutomationElement[] ConvertToElementArray(IUIAutomationElementArray array)
        {
            AutomationElement[] elementArray;
            if (array != null)
            {
                elementArray = new AutomationElement[array.Length];
                for (var i = 0; i < array.Length; i++) elementArray[i] = AutomationElement.Wrap(array.GetElement(i));
            }
            else
            {
                elementArray = null;
            }

            return elementArray;
        }

        internal static bool ConvertException(COMException e, out Exception uiaException)
        {
            var handled = true;
            switch (e.ErrorCode)
            {
                case UiaCoreIds.UIA_E_ELEMENTNOTAVAILABLE:
                    uiaException = new ElementNotAvailableException(e);
                    break;

                case UiaCoreIds.UIA_E_ELEMENTNOTENABLED:
                    uiaException = new ElementNotEnabledException(e);
                    break;

                case UiaCoreIds.UIA_E_NOCLICKABLEPOINT:
                    uiaException = new NoClickablePointException(e);
                    break;

                case UiaCoreIds.UIA_E_PROXYASSEMBLYNOTLOADED:
                    uiaException = new ProxyAssemblyNotLoadedException(e);
                    break;

                default:
                    uiaException = null;
                    handled = false;
                    break;
            }

            return handled;
        }

        internal static tagPOINT PointManagedToNative(Point pt)
        {
            var nativePoint = new tagPOINT
            {
                x = (int) pt.X,
                y = (int) pt.Y
            };
            return nativePoint;
        }

        internal static void ValidateArgument(bool cond, string reason)
        {
            if (!cond) throw new ArgumentException(reason);
        }

        internal static void ValidateArgumentNonNull(object obj, string argName)
        {
            if (obj == null) throw new ArgumentNullException(argName);
        }

        internal static object WrapObjectAsPattern(AutomationElement el, object nativePattern,
            AutomationPattern pattern, bool cached)
        {
            PatternTypeInfo info;
            if (!Schema.GetPatternInfo(pattern, out info)) throw new ArgumentException("Unsupported pattern");
            if (info.ClientSideWrapper == null) return null;
            return info.ClientSideWrapper(el, nativePattern, cached);
        }

        internal static object WrapObjectAsProperty(AutomationProperty property, object obj)
        {
            PropertyTypeInfo info;

            // Handle the cases that we know.
            if (obj == AutomationElement.NotSupported)
            {
                // No-op
            }
            else if (obj is IUIAutomationElement)
            {
                obj = AutomationElement.Wrap((IUIAutomationElement) obj);
            }
            else if (obj is IUIAutomationElementArray)
            {
                obj = ConvertToElementArray((IUIAutomationElementArray) obj);
            }
            else if (Schema.GetPropertyTypeInfo(property, out info))
            {
                // Well known properties
                if (obj != null && info.ObjectConverter != null) obj = info.ObjectConverter(obj);
            }

            return obj;
        }

        // Unwrap an object from API representationt to what the native client will expect
        internal static object UnwrapObject(object val)
        {
            if (val != null)
            {
                if (val is ControlType)
                {
                    val = ((ControlType) val).Id;
                }
                else if (val is Rect)
                {
                    var rect = (Rect) val;
                    val = new[] {rect.Left, rect.Top, rect.Width, rect.Height};
                }
                else if (val is Point)
                {
                    var point = (Point) val;
                    val = new[] {point.X, point.Y};
                }
                else if (val is CultureInfo)
                {
                    val = ((CultureInfo) val).LCID;
                }
                else if (val is AutomationElement)
                {
                    val = ((AutomationElement) val).NativeElement;
                }
            }

            return val;
        }
    }
}