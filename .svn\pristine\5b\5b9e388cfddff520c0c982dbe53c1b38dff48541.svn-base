﻿namespace ImageLib
{
    public class ImageHelper
    {
        private const string StrUrlStart = "http";

        public static string GetResult(byte[] content, string fileExt)
        {
            var result = string.Empty;
            if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
            {
                result = ALiYunUpload.GetResult(content);
            }
            if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
            {
                result = SouGouImageUpload.GetResult(content);
            }
            if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
            {
                result = _360ImageUpload.GetResult(content);
            }
            if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
            {
                result = TinyPngUpload.GetResultUrl(content);
            }
            if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
            {
                result = WebResizerUpload.GetResult(content);
            }
            if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
            {
                result = Net126Upload.GetResult(content);
            }
            return result;
        }
    }
}
