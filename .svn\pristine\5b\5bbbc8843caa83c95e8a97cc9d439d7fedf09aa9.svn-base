﻿using System;
using System.Threading;

namespace OCRTools
{
    /// <summary>
    ///     定时任务委托方法
    /// </summary>
    public delegate void TimerTaskDelegate();

    /// <summary>
    ///     有参数的定时任务委托方法
    /// </summary>
    public delegate void ParmTimerTaskDelegate(object[] parm);

    /// <summary>
    ///     定时任务接口类
    /// </summary>
    public interface ITimerTask
    {
        void Run();
    }

    /// <summary>
    ///     定时任务服务类
    ///     作者：Duyong
    ///     编写日期：2010-07-25
    /// </summary>
    public class TimerTaskService
    {
        #region 定时任务实例成员

        private readonly TimerInfo timerInfo; //定时信息

        private DateTime NextRunTime; //下一次执行时间

        private readonly TimerTaskDelegate TimerTaskDelegateFun; //执行具体任务的委托方法

        private readonly ParmTimerTaskDelegate ParmTimerTaskDelegateFun; //带参数的执行具体任务的委托方法
        private object[] parm; //参数

        private readonly ITimerTask TimerTaskInstance; //执行具体任务的实例

        /// <summary>
        ///     根据定时信息构造定时任务服务
        /// </summary>
        /// <param name="_timer"></param>
        private TimerTaskService(TimerInfo _timer)
        {
            timerInfo = _timer;
        }

        /// <summary>
        ///     根据定时信息和执行具体任务的实例构造定时任务服务
        /// </summary>
        /// <param name="_timer">定时信息</param>
        /// <param name="_interface">执行具体任务的实例</param>
        private TimerTaskService(TimerInfo _timer, ITimerTask _interface)
        {
            timerInfo = _timer;
            TimerTaskInstance = _interface;
        }

        /// <summary>
        ///     根据定时信息和执行具体任务的委托方法构造定时任务服务
        /// </summary>
        /// <param name="_timer">定时信息</param>
        /// <param name="trd">执行具体任务的委托方法</param>
        private TimerTaskService(TimerInfo _timer, TimerTaskDelegate trd)
        {
            timerInfo = _timer;
            TimerTaskDelegateFun = trd;
        }

        /// <summary>
        ///     根据定时信息和执行具体任务的委托方法构造定时任务服务
        /// </summary>
        /// <param name="_timer">定时信息</param>
        /// <param name="ptrd">带参数执行具体任务的委托方法</param>
        private TimerTaskService(TimerInfo _timer, ParmTimerTaskDelegate ptrd)
        {
            timerInfo = _timer;
            ParmTimerTaskDelegateFun = ptrd;
        }

        /// <summary>
        ///     设置参数
        /// </summary>
        /// <param name="_parm"></param>
        private void setParm(object[] _parm)
        {
            parm = _parm;
        }


        /// <summary>
        ///     启动定时任务
        /// </summary>
        public void Start()
        {
            //检查定时器
            CheckTimer(timerInfo);
        }

        /// <summary>
        ///     检查定时器
        /// </summary>
        private void CheckTimer(TimerInfo timerInfo)
        {
            //计算下次执行时间
            if (timerInfo.IsExecFirst)
                NextRunTime = DateTime.Now.AddSeconds(3);
            else
                getNextRunTime();

            while (!CommonString.isExit)
            {
                var DateTimeNow = DateTime.Now;

                //时间比较
                var dateComp = DateTimeNow.Year == NextRunTime.Year && DateTimeNow.Month == NextRunTime.Month &&
                               DateTimeNow.Day == NextRunTime.Day;

                var timeComp = DateTimeNow.Hour == NextRunTime.Hour && DateTimeNow.Minute == NextRunTime.Minute &&
                               DateTimeNow.Second == NextRunTime.Second;

                //如果当前时间等式下次运行时间,则调用线程执行方法
                if (dateComp && timeComp)
                {
                    //调用执行处理方法
                    if (TimerTaskDelegateFun != null)
                        TimerTaskDelegateFun();
                    else if (ParmTimerTaskDelegateFun != null)
                        ParmTimerTaskDelegateFun(parm);
                    else if (TimerTaskInstance != null)
                        TimerTaskInstance.Run();
                    else
                        Run();
                    //重新计算下次执行时间
                    getNextRunTime();
                }

                Thread.Sleep(10);
            }
        }

        /// <summary>
        ///     执行方法
        /// </summary>
        protected void Run()
        {
            //TODO.....
        }

        /// <summary>
        ///     计算下一次执行时间
        /// </summary>
        /// <returns></returns>
        private void getNextRunTime()
        {
            var now = DateTime.Now;
            var nowHH = now.Hour;
            var nowMM = now.Minute;
            var nowSS = now.Second;

            var timeHH = timerInfo.Hour;
            var timeMM = timerInfo.Minute;
            var timeSS = timerInfo.Second;

            //设置执行时间对当前时间进行比较
            var nowTimeComp = nowHH < timeHH || nowHH <= timerInfo.Hour && nowMM < timeMM ||
                              nowHH <= timerInfo.Hour && nowMM <= timeMM && nowSS < timeSS;

            //每天
            if ("EveryDay".Equals(timerInfo.TimerType))
            {
                if (nowTimeComp)
                    NextRunTime = new DateTime(now.Year, now.Month, now.Day, timeHH, timeMM, timeSS);
                else
                    NextRunTime = new DateTime(now.Year, now.Month, now.Day, timeHH, timeMM, timeSS).AddDays(1);
            }
            //间隔几小时
            else if ("LoopHours".Equals(timerInfo.TimerType))
            {
                NextRunTime =
                    new DateTime(now.Year, now.Month, now.Day, nowHH, timeMM, timeSS).AddHours(timerInfo.DateValue);
            }
            //间隔几分钟
            else if ("LoopMinutes".Equals(timerInfo.TimerType))
            {
                NextRunTime =
                    new DateTime(now.Year, now.Month, now.Day, nowHH, nowMM, timeSS).AddMinutes(timerInfo.DateValue);
            }
            //每周一次
            else if ("DayOfWeek".Equals(timerInfo.TimerType))
            {
                var ofweek = DateTime.Now.DayOfWeek;

                var dayOfweek = Convert.ToInt32(DateTime.Now.DayOfWeek);

                if (ofweek == DayOfWeek.Sunday) dayOfweek = 7;

                if (dayOfweek < timerInfo.DateValue)
                {
                    var addDays = timerInfo.DateValue - dayOfweek;
                    NextRunTime = new DateTime(now.Year, now.Month, now.Day, timeHH, timeMM, timeSS).AddDays(addDays);
                }
                else if (dayOfweek == timerInfo.DateValue && nowTimeComp)
                {
                    NextRunTime = new DateTime(now.Year, now.Month, now.Day, timeHH, timeMM, timeSS);
                }
                else
                {
                    var addDays = 7 - (dayOfweek - timerInfo.DateValue);
                    NextRunTime = new DateTime(now.Year, now.Month, now.Day, timeHH, timeMM, timeSS).AddDays(addDays);
                }
            }
            //每月一次
            else if ("DayOfMonth".Equals(timerInfo.TimerType))
            {
                if (now.Day < timerInfo.DateValue)
                    NextRunTime = new DateTime(now.Year, now.Month, timerInfo.DateValue, timeHH, timeMM, timeSS);
                else if (now.Day == timerInfo.DateValue && nowTimeComp)
                    NextRunTime = new DateTime(now.Year, now.Month, now.Day, timeHH, timeMM, timeSS);
                else
                    NextRunTime = new DateTime(now.Year, now.Month, timerInfo.DateValue, timeHH, timeMM, timeSS)
                        .AddMonths(1);
            }
            //指定日期
            else if ("DesDate".Equals(timerInfo.TimerType))
            {
                NextRunTime = new DateTime(timerInfo.Year, timerInfo.Month, timerInfo.Day, timeHH, timeMM, timeSS);
            }
            //循环指定天数
            else if ("LoopDays".Equals(timerInfo.TimerType))
            {
                NextRunTime =
                    new DateTime(now.Year, now.Month, now.Day, timeHH, timeMM, timeSS).AddDays(timerInfo.DateValue);
            }
        }

        #endregion


        #region 创建定时任务静态方法

        /// <summary>
        ///     使用委托方法创建定时任务
        ///     <param name="info"></param>
        ///     <param name="_trd"></param>
        ///     <returns></returns>
        public static Thread CreateTimerTaskService(TimerInfo info, TimerTaskDelegate _trd)
        {
            var tus = new TimerTaskService(info, _trd);
            //创建启动线程
            var ThreadTimerTaskService = new Thread(tus.Start);
            return ThreadTimerTaskService;
        }

        /// <summary>
        ///     使用带参数的委托方法创建定时任务
        /// </summary>
        /// <param name="info"></param>
        /// <param name="_ptrd"></param>
        /// <param name="parm"></param>
        /// <returns></returns>
        public static Thread CreateTimerTaskService(TimerInfo info, ParmTimerTaskDelegate _ptrd, object[] parm)
        {
            var tus = new TimerTaskService(info, _ptrd);
            tus.setParm(parm);

            //创建启动线程
            var ThreadTimerTaskService = new Thread(tus.Start);
            return ThreadTimerTaskService;
        }

        /// <summary>
        ///     使用实现定时接口ITimerTask的实例创建定时任务
        /// </summary>
        /// <param name="info"></param>
        /// <param name="_ins"></param>
        /// <returns></returns>
        public static Thread CreateTimerTaskService(TimerInfo info, ITimerTask _ins)
        {
            var tus = new TimerTaskService(info, _ins);
            //创建启动线程
            var ThreadTimerTaskService = new Thread(tus.Start);
            return ThreadTimerTaskService;
        }

        #endregion
    }

    /// <summary>
    ///     定时信息类
    ///     TimerType   类型：EveryDay(每天),DayOfWeek(每周),DayOfMonth(每月),DesDate(指定日期),LoopDays(循环天数)
    ///     DateValue 日期值：TimerType="DayOfWeek"时,值为1-7表示周一到周日;TimerType="DayOfMonth"时,值为1-31表示1号到31号,
    ///     TimerType="LoopDays"时,值为要循环的天数,TimerType为其它值时,此值无效
    ///     Year   年：TimerType="DesDate"时,此值有效
    ///     Month  月：TimerType="DesDate"时,此值有效
    ///     Day    日：TimerType="DesDate"时,此值有效
    ///     Hour   时：]
    ///     Minute 分： > 设置的执行时间
    ///     Second 秒：]
    /// </summary>
    public class TimerInfo
    {
        public int DateValue;
        public int Day;
        public int Hour = 00;
        public bool IsExecFirst;
        public int Minute = 00;
        public int Month;
        public int Second = 00;
        public string TimerType;
        public int Year;
    }
}