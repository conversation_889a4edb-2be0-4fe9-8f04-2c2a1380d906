using OCRTools.Language;
using OCRTools.ScreenCaptureLib;
using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;
using static MetroFramework.Drawing.MetroPaint;

namespace OCRTools.Common
{
    public class GuidePresenterForm : Form
    {
        private Form _mainForm;
        private GuideEntity guide;
        private int _currentGuideIndex = -1;
        private Panel pnlMain;
        private Label lblTitle;
        private Label lblDesc;
        private Button btnPrev;
        private Button btnNext;
        private LinkLabel lnkSkip;
        private Color _guidePanelColor = Color.FromArgb(0, 112, 249);
        private double wScale = 1d;
        private double hScale = 1d;

        private BufferedGraphics _bufferedGraphics;
        private BufferedGraphicsContext _context;
        private Rectangle _currentHighlightRect = Rectangle.Empty;
        private ArrowDirection _currentArrowDirection;

        private Timer _animationTimer;
        private Point _targetPanelLocation;
        private Point _startPanelLocation;
        private int _animationStep = 0;
        private const int ANIMATION_STEPS = 10;

        public GuidePresenterForm(Form mainForm, GuideEntity entity)
        {
            // 确保窗体支持透明背景
            this.SetStyle(ControlStyles.SupportsTransparentBackColor, true);
            this.BackColor = Color.Transparent;
            this.Opacity = 1.0; // 确保窗体完全不透明

            // 初始化双缓冲绘图
            _context = BufferedGraphicsManager.Current;
            SetStyle(ControlStyles.AllPaintingInWmPaint |
                    ControlStyles.UserPaint |
                    ControlStyles.DoubleBuffer |
                    ControlStyles.OptimizedDoubleBuffer, true);
            UpdateStyles();

            // 初始化动画计时器
            _animationTimer = new Timer();
            _animationTimer.Interval = 15; // 15毫秒一帧，约60fps
            _animationTimer.Tick += AnimationTimer_Tick;

            guide = entity;
            if (guide.ShowSummary && !guide.Items[0].Rect.IsEmpty)
            {
                var strSummary = guide.Desc;
                if (string.IsNullOrEmpty(strSummary))
                {
                    strSummary = string.Join("\n", guide.Items.Where(p => p.Summary).Select(p => p.Title.CurrentText(true))).Trim();
                    if (string.IsNullOrEmpty(strSummary))
                    {
                        strSummary = guide.Title;
                    }
                }
                guide.Items.Insert(0, new GuideItem()
                {
                    Title = guide.Title,
                    Desc = strSummary,
                    Rect = Rectangle.Empty
                });
            }
            _mainForm = mainForm;
            if (!mainForm.Visible)
            {
                mainForm.Show();
                Update();
            }
            if (mainForm.WindowState != FormWindowState.Normal)
            {
                mainForm.WindowState = FormWindowState.Normal;
                Update();
            }
            wScale = _mainForm.Width * 1d / guide.BaseSize.Width;
            hScale = _mainForm.Height * 1d / guide.BaseSize.Height;
            FormBorderStyle = FormBorderStyle.None;
            ShowInTaskbar = false;
            Margin = CommonString.PaddingZero;
            Padding = CommonString.PaddingZero;
            BackgroundImageLayout = ImageLayout.Stretch;
            InitializeControls();
            CommonMethod.EnableDoubleBuffering(this);
            Shown += GuidePresenterForm_Shown;
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            if (keyData == Keys.Escape)
            {
                CloseGuide();
                return true;
            }
            if (keyData == Keys.Left || keyData == Keys.Up)
            {
                ShowPreviousGuide();
                return true;
            }
            if (keyData == Keys.Right || keyData == Keys.Down)
            {
                ShowNextGuide(false);
                return true;
            }
            return base.ProcessCmdKey(ref msg, keyData);
        }

        private void GuidePresenterForm_Shown(object sender, EventArgs e)
        {
            Bounds = _mainForm.Bounds;
            Location = _mainForm.Location;
            RefreshBackImg();
            if (guide?.Items?.Count > 0)
            {
                ShowNextGuide(false);
            }
        }

        private Bitmap _backgroundImage; // 存储背景截图
        private bool _needRefreshBackground;

        private void RefreshBackImg()
        {
            if (_needRefreshBackground || _backgroundImage == null)
            {
                // 保存当前可见状态
                bool wasVisible = this.Visible;
                bool isFirst = _backgroundImage == null;

                // 临时将窗体设为透明而不是隐藏
                if (!isFirst && wasVisible)
                {
                    this.Opacity = 0;
                    Application.DoEvents();
                }

                var rect = Rectangle.Empty;

                _backgroundImage = Screenshot.CaptureWindow(_mainForm.Handle, ref rect);

                if (_backgroundImage != null)
                {
                    BackgroundImage = _backgroundImage;
                    BackgroundImageLayout = ImageLayout.Stretch;
                }

                // 平滑恢复透明度
                if (!isFirst && wasVisible)
                {
                    this.Opacity = 1;
                    Application.DoEvents();
                    //// 平滑恢复透明度
                    //FadeInForm();
                }
            }
        }

        // 平滑淡入窗体
        private void FadeInForm()
        {
            double opacity = 0.0;
            Timer fadeTimer = new Timer
            {
                Interval = 15
            };
            fadeTimer.Tick += (s, e) =>
            {
                opacity += 0.1;
                if (opacity >= 1.0)
                {
                    this.Opacity = 1.0;
                    fadeTimer.Stop();
                    fadeTimer.Dispose();
                }
                else
                {
                    this.Opacity = opacity;
                }
            };
            fadeTimer.Start();
        }

        private float _MaxTitleFontSize;
        private float _MaxDescFontSize;

        private void InitializeControls()
        {
            // 创建引导提示框及其内部控件 - 使用Win11风格
            pnlMain = new Panel
            {
                BackColor = _guidePanelColor,
                Size = new Size(Math.Min(_mainForm.Width, (int)(450 * wScale)), Math.Min((int)(196 * hScale), _mainForm.Width)),
                Location = new Point(100, 100),
                Dock = DockStyle.None,
            };

            // 添加圆角和阴影效果 - 使用Region实现圆角
            int cornerRadius = 12;
            int shadowSize = 3; // 阴影大小
            // Win11使用更大的圆角
            pnlMain.Paint += (s, e) =>
            {
                // 使用高质量绘制模式
                e.Graphics.SmoothingMode = SmoothingMode.HighQuality;
                e.Graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                e.Graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;
                e.Graphics.CompositingQuality = CompositingQuality.HighQuality;

                // 阴影矩形，稍微扩大
                Rectangle shadowRect = new Rectangle(
                    2,  // 水平偏移
                    2,  // 垂直偏移
                    pnlMain.Width,
                    pnlMain.Height
                );
                GraphicsPath shadowPath = CreateRoundedRectanglePath(shadowRect, cornerRadius);

                // 绘制阴影
                using (PathGradientBrush shadowBrush = new PathGradientBrush(shadowPath))
                {
                    shadowBrush.CenterColor = Color.FromArgb(30, 0, 0, 0); // 半透明阴影
                    shadowBrush.SurroundColors = new Color[] { Color.FromArgb(0, 0, 0, 0) }; // 边缘透明
                    shadowBrush.FocusScales = new PointF(0.95f, 0.95f);
                    e.Graphics.FillPath(shadowBrush, shadowPath);
                }

                // 绘制背景
                Rectangle rect = new Rectangle(0, 0, pnlMain.Width, pnlMain.Height);
                GraphicsPath path = CreateRoundedRectanglePath(rect, cornerRadius);

                // 绘制微妙的边框
                using (Pen borderPen = new Pen(Color.FromArgb(100, 255, 255, 255), 1))
                {
                    borderPen.Alignment = PenAlignment.Inset;
                    borderPen.LineJoin = LineJoin.Round;
                    e.Graphics.DrawPath(borderPen, path);
                }
            };

            // 设置面板区域为圆角矩形
            pnlMain.Region = new Region(CreateRoundedRectanglePath(
                new Rectangle(0, 0, pnlMain.Width, pnlMain.Height), cornerRadius));

            Controls.Add(pnlMain);

            // 标题标签 - 保留样式调整但恢复原始颜色
            lblTitle = new Label
            {
                BackColor = Color.Transparent,
                Font = CommonString.GetSysBoldFont(20),
                ForeColor = Color.White,
                Padding = new Padding(24, 18, 24, 0), // 保留调整的间距
                AutoSize = false,
                Size = new Size(pnlMain.Width, (int)(45 * hScale))
            };
            pnlMain.Controls.Add(lblTitle);

            // 描述标签 - 恢复原始颜色
            lblDesc = new Label
            {
                BackColor = Color.Transparent,
                Font = CommonString.GetSysNormalFont(15),
                ForeColor = Color.White, // 原始的白色
                Padding = new Padding(24, 0, 24, 0), // 保留调整的间距
                AutoSize = false,
                TextAlign = ContentAlignment.MiddleLeft,
                Size = new Size(pnlMain.Width, (int)(90 * hScale)),
                Top = lblTitle.Top + lblTitle.Height,
            };
            pnlMain.Controls.Add(lblDesc);

            // 创建引导提示框中的按钮 - 恢复原始颜色方案
            lnkSkip = new LinkLabel
            {
                BackColor = Color.Transparent,
                Text = "跳过".CurrentText(),
                ForeColor = Color.FromArgb(165, 205, 253), // 原始的颜色
                LinkColor = Color.FromArgb(165, 205, 253), // 原始的颜色
                //BackColor = _guidePanelColor, // 原始的背景色
                Font = CommonString.GetSysBoldFont(13),
                Location = new Point(24, pnlMain.Height - (int)(42 * hScale)), // 保留调整的位置
                AutoSize = true,
                LinkBehavior = LinkBehavior.NeverUnderline,
                TabStop = false,
                Cursor = Cursors.Hand // 保留手型光标
            };

            var btnWidth = (int)(100 * wScale);
            var btnHeight = (int)(36 * hScale); // 保留调整的高度
            var btnFont = CommonString.GetSysBoldFont(16); // 恢复原始字体大小
            int btnCornerRadius = 4; // 保留圆角效果

            // 上一步按钮 - 恢复原始颜色
            btnPrev = new Button
            {
                Text = "上一步".CurrentText(),
                Size = new Size(btnWidth, btnHeight),
                ForeColor = Color.White,              // 原始颜色
                BackColor = _guidePanelColor,         // 恢复原始颜色
                Font = btnFont,
                Visible = false,
                TabStop = false,
                FlatStyle = FlatStyle.Flat,           // 保留样式调整
                FlatAppearance = { BorderSize = 0 },
                Location = new Point(pnlMain.Width - 30 - btnWidth * 2, pnlMain.Height - (int)(50 * hScale)),
                Cursor = Cursors.Hand // 保留手型光标
            };

            // 保留按钮圆角效果
            btnPrev.Region = new Region(CreateRoundedRectanglePath(
                new Rectangle(0, 0, btnPrev.Width, btnPrev.Height), btnCornerRadius));

            // 下一步按钮 - 恢复原始颜色
            btnNext = new Button
            {
                Text = "下一步".CurrentText(),
                Size = new Size(btnWidth, btnHeight),
                ForeColor = _guidePanelColor,         // 原始颜色
                BackColor = Color.White,              // 原始颜色
                Font = btnFont,
                Visible = false,
                TabStop = false,
                FlatStyle = FlatStyle.Flat,           // 保留样式调整
                FlatAppearance = { BorderSize = 0 },
                Cursor = Cursors.Hand,                // 保留手型光标
                Location = new Point(btnPrev.Left + btnWidth + 10, pnlMain.Height - (int)(50 * hScale))
            };

            // 保留按钮圆角效果
            btnNext.Region = new Region(CreateRoundedRectanglePath(
                new Rectangle(0, 0, btnNext.Width, btnNext.Height), btnCornerRadius));

            btnPrev.Click += (s, e) => ShowPreviousGuide();
            btnNext.Click += (s, e) => ShowNextGuide(true);
            lnkSkip.Click += (s, e) => CloseGuide();

            pnlMain.Controls.Add(btnPrev);
            pnlMain.Controls.Add(btnNext);
            pnlMain.Controls.Add(lnkSkip);
            CommonMethod.SetStyle(lnkSkip, ControlStyles.Selectable, false);
            _MaxTitleFontSize = lblTitle.Font.Size;
            _MaxDescFontSize = lblDesc.Font.Size;
        }

        private void ShowNextGuide(bool isUser)
        {
            if (_currentGuideIndex < guide.Items.Count - 1)
            {
                _currentGuideIndex++;
                ShowGuide(guide.Items[_currentGuideIndex]);
                btnNext.Focus();
            }
            else if (isUser && _currentGuideIndex == guide.Items.Count - 1)
            {
                CloseGuide();
            }
        }

        private void ShowPreviousGuide()
        {
            if (_currentGuideIndex > 0)
            {
                _currentGuideIndex--;
                ShowGuide(guide.Items[_currentGuideIndex]);
                if (btnPrev.Visible)
                    btnPrev.Focus();
                else
                    btnNext.Focus();
            }
        }

        private void ShowGuide(GuideItem item)
        {
            if (!string.IsNullOrEmpty(item.Exec))
            {
                _mainForm.ExecuteScript(item.Exec);
                _needRefreshBackground = true;
            }

            RefreshBackImg();

            var rect = item.Rect;
            if (!string.IsNullOrEmpty(item.Ctrl))
            {
                var targetRect = FindTargetControl(item.Ctrl);
                if (!targetRect.IsEmpty)
                {
                    rect = targetRect;
                    var frmPadding = _mainForm.Padding;
                    rect.Y += frmPadding.Top;
                    rect.X += frmPadding.Left;
                    rect = rect.ZoomBig(10);
                }
            }
            if (!rect.IsEmpty)
            {
                if (!guide.BaseSize.IsEmpty)
                {
                    // 等比例缩放
                    var widthScale = Width * 1d / guide.BaseSize.Width;
                    var heightScale = Height * 1d / guide.BaseSize.Height;
                    rect.X = (int)(rect.X * widthScale);
                    rect.Y = (int)(rect.Y * heightScale);
                    rect.Width = (int)(rect.Width * widthScale);
                    rect.Height = (int)(rect.Height * heightScale);
                }
                if (rect.X < 0)
                {
                    rect.X += Width;
                }
                else if (rect.Y < 0)
                {
                    rect.Y += Height;
                }
            }
            //if (!rect.IsEmpty)
            {
                lblTitle.Font = CommonMethod.ScaleLabelByHeight(item.Title, CommonString.GetUserNormalFont(8F, FontStyle.Bold), lblTitle.Size, _MaxTitleFontSize);
                lblTitle.Text = item.Title;
                lblDesc.Font = CommonMethod.ScaleLabelByHeight(item.Desc, CommonString.GetUserNormalFont(8F), lblDesc.Size, _MaxDescFontSize);
                lblDesc.Text = item.Desc;
                var index = guide.Items.IndexOf(item);
                if (guide.ShowSummary)
                    lnkSkip.Text = "跳过".CurrentText() + " [Esc]" + (index > 0 ? $" ({index}/{guide.Items.Count - 1})" : "");
                else
                    lnkSkip.Text = "跳过".CurrentText() + $" [Esc] ({index + 1}/{guide.Items.Count})";

                //rect = rect.Zoom(1.5);
                var arrow = SetGuidePanelPosition(rect);
                UpdateButtonVisibility();
                _currentHighlightRect = rect;
                _currentArrowDirection = arrow;
                //SetHighlight();
            }
        }

        private void DrawHighlightWithBuffer(Graphics g)
        {
            // 创建或更新缓冲区
            if (_bufferedGraphics == null || _bufferedGraphics.Graphics == null)
            {
                _context.MaximumBuffer = new Size(this.Width + 1, this.Height + 1);
                _bufferedGraphics = _context.Allocate(g, new Rectangle(0, 0, this.Width, this.Height));
            }

            // 清除缓冲区 - 使用完全透明的颜色
            _bufferedGraphics.Graphics.Clear(Color.Transparent);

            // 先确保背景图像正确显示
            if (_backgroundImage != null)
            {
                // 手动绘制背景图像，确保它显示在最底层
                _bufferedGraphics.Graphics.DrawImage(_backgroundImage, ClientRectangle);
            }

            // 在缓冲区中绘制高亮效果
            DrawHighlightEffect(_bufferedGraphics.Graphics, _currentHighlightRect, _currentArrowDirection);

            // 将缓冲区内容渲染到屏幕 - 使用Alpha混合确保透明效果正确
            _bufferedGraphics.Render(g);
        }

        /// <summary>
        /// 绘制高亮区域效果 - 创建一个气泡形状的高亮区域和箭头
        /// </summary>
        /// <param name="g">绘图对象</param>
        /// <param name="rectangle">要高亮的矩形区域</param>
        /// <param name="arrow">箭头方向</param>
        private void DrawHighlightEffect(Graphics g, Rectangle rectangle, ArrowDirection arrow)
        {
            // 使用双缓冲区技术实现最高质量抗锐齿渲染
            using (Bitmap buffer = new Bitmap(Width, Height, System.Drawing.Imaging.PixelFormat.Format32bppPArgb))
            using (Graphics bufferG = Graphics.FromImage(buffer))
            {
                // 调整绘图属性到最高质量
                ConfigureGraphicsForHighQuality(bufferG);

                // 创建整个窗体的遮罩区域
                Region formRegion = new Region(ClientRectangle);

                if (!rectangle.IsEmpty)
                {
                    // 缩放和调整参数
                    float dpiScale = bufferG.DpiX / 96.0f;  // DPI缩放比例
                    float borderWidth = 2.5f * dpiScale;    // 边框宽度
                    int cornerRadius = (int)(12 * dpiScale);  // 圆角半径
                    Color borderColor = Color.White;

                    // 精确对齐到像素网格，避免半像素问题
                    rectangle = GetPixelAlignedRectangle(rectangle);

                    // 根据箭头方向计算箭头基点和尖端
                    Point basePoint, tipPoint;
                    GetArrowPoints(
                        rectangle, arrow, (int)(12 * dpiScale), (int)(14 * dpiScale), dpiScale,
                        out basePoint, out tipPoint);

                    // 创建完全一体化的气泡形状
                    GraphicsPath bubblePath = CreateUnifiedBubblePath(
                        rectangle, cornerRadius, basePoint, tipPoint, (int)(12 * dpiScale), arrow);

                    // 从遮罩区域中排除气泡形状
                    formRegion.Exclude(new Region(bubblePath));

                    // 绘制阴影和边框
                    DrawBubbleBorderAndShadow(bufferG, bubblePath, borderWidth, borderColor);
                }

                // 绘制半透明遮罩区域
                using (SolidBrush maskBrush = new SolidBrush(Color.FromArgb(150, 0, 0, 0)))
                {
                    bufferG.FillRegion(maskBrush, formRegion);
                }

                // 将缓冲区内容绘制到目标Graphics对象
                g.DrawImage(buffer, 0, 0);
            }
        }

        /// <summary>
        /// 调整绘图对象的属性到最高质量
        /// </summary>
        private void ConfigureGraphicsForHighQuality(Graphics g)
        {
            g.Clear(Color.Transparent);
            g.SmoothingMode = SmoothingMode.AntiAlias;
            g.InterpolationMode = InterpolationMode.HighQualityBicubic;
            g.PixelOffsetMode = PixelOffsetMode.HighQuality;
            g.CompositingQuality = CompositingQuality.HighQuality;
            g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.AntiAlias;
        }

        /// <summary>
        /// 将矩形坐标对齐到像素网格
        /// </summary>
        private Rectangle GetPixelAlignedRectangle(Rectangle rect)
        {
            return new Rectangle(
                (int)Math.Round((double)rect.X),
                (int)Math.Round((double)rect.Y),
                (int)Math.Round((double)rect.Width),
                (int)Math.Round((double)rect.Height)
            );
        }

        /// <summary>
        /// 计算箭头基点和尖端坐标
        /// </summary>
        private void GetArrowPoints(
            Rectangle rectangle, ArrowDirection arrow, int arrowSize, int arrowLength, float dpiScale,
            out Point basePoint, out Point tipPoint)
        {
            // 计算基线点和箭头尖端点
            basePoint = GetArrowBasePoint(rectangle, arrow);
            tipPoint = GetArrowTipPoint(basePoint, arrow, arrowLength);

            // 对齐到像素网格
            basePoint = new Point(
                (int)Math.Round((double)basePoint.X),
                (int)Math.Round((double)basePoint.Y));
            tipPoint = new Point(
                (int)Math.Round((double)tipPoint.X),
                (int)Math.Round((double)tipPoint.Y));
        }

        /// <summary>
        /// 绘制气泡的阴影和边框
        /// </summary>
        private void DrawBubbleBorderAndShadow(Graphics g, GraphicsPath bubblePath, float borderWidth, Color borderColor)
        {
            // 外层模糊阴影
            using (Pen outerShadowPen = new Pen(Color.FromArgb(12, 0, 0, 0), borderWidth + 5.0f))
            {
                outerShadowPen.LineJoin = LineJoin.Round;
                outerShadowPen.StartCap = LineCap.Round;
                outerShadowPen.EndCap = LineCap.Round;
                g.DrawPath(outerShadowPen, bubblePath);
            }

            // 白色边框
            using (Pen borderPen = new Pen(borderColor, borderWidth))
            {
                borderPen.LineJoin = LineJoin.Round;
                borderPen.StartCap = LineCap.Round;
                borderPen.EndCap = LineCap.Round;
                g.DrawPath(borderPen, bubblePath);
            }
        }

        // 创建真正一体化的气泡形状，箭头和矩形完美融合
        private GraphicsPath CreateUnifiedBubblePath(Rectangle rect, int cornerRadius, Point arrowBase, Point arrowTip, int arrowSize, ArrowDirection direction)
        {
            GraphicsPath path = new GraphicsPath();

            // 控制圆角大小，确保圆角不会超过矩形尺寸的一半
            cornerRadius = Math.Min(cornerRadius, Math.Min(rect.Width, rect.Height) / 2);
            int diameter = cornerRadius * 2;

            // 定义矩形边缘的四个圆角区域
            Rectangle topLeft = new Rectangle(rect.X, rect.Y, diameter, diameter);
            Rectangle topRight = new Rectangle(rect.X + rect.Width - diameter, rect.Y, diameter, diameter);
            Rectangle bottomLeft = new Rectangle(rect.X, rect.Y + rect.Height - diameter, diameter, diameter);
            Rectangle bottomRight = new Rectangle(rect.X + rect.Width - diameter, rect.Y + rect.Height - diameter, diameter, diameter);

            // 程序优化适用于窄的矩形，确保矩形宽度小于箭头宽度时仍能正确显示
            int minRectWidth = Math.Max(rect.Width, arrowSize * 2 + cornerRadius);

            // 计算箭头的两个基点坐标
            // 这些点拉宽了，确保箭头和矩形的连接处不会有异常
            Point arrowPoint1, arrowPoint2;

            switch (direction)
            {
                // 每个方向分开管理，确保气泡形状的整体连续性
                case ArrowDirection.Up:
                    // 上箭头，箭头在矩形上方
                    arrowPoint1 = new Point(arrowBase.X - arrowSize, arrowBase.Y);
                    arrowPoint2 = new Point(arrowBase.X + arrowSize, arrowBase.Y);

                    // 检查箭头位置是否会与圆角重叠
                    if (arrowBase.X - arrowSize < rect.X + cornerRadius)
                    {
                        // 如果箭头左侧会夹在圆角内，调整坐标
                        arrowPoint1 = new Point(rect.X + cornerRadius, rect.Y);
                    }
                    if (arrowBase.X + arrowSize > rect.X + rect.Width - cornerRadius)
                    {
                        // 如果箭头右侧会夹在圆角内，调整坐标
                        arrowPoint2 = new Point(rect.X + rect.Width - cornerRadius, rect.Y);
                    }

                    // 从左上角开始绘制路径
                    if (arrowBase.X < rect.X + cornerRadius)
                    {
                        // 当箭头在左上角附近时
                        path.AddLine(arrowPoint1, arrowTip);  // 箭头左侧
                        path.AddLine(arrowTip, arrowPoint2);   // 箭头右侧
                    }
                    else if (arrowBase.X > rect.X + rect.Width - cornerRadius)
                    {
                        // 当箭头在右上角附近时
                        path.AddArc(topLeft, 180, 90);      // 左上角圆弧
                        path.AddLine(arrowPoint1, arrowTip);  // 箭头左侧
                        path.AddLine(arrowTip, arrowPoint2);   // 箭头右侧
                    }
                    else
                    {
                        // 箭头在上边中间
                        path.AddArc(topLeft, 180, 90);      // 左上角圆弧
                        path.AddLine(new Point(rect.X + cornerRadius, rect.Y), arrowPoint1);
                        path.AddLine(arrowPoint1, arrowTip);  // 箭头左侧
                        path.AddLine(arrowTip, arrowPoint2);   // 箭头右侧
                        path.AddLine(arrowPoint2, new Point(rect.X + rect.Width - cornerRadius, rect.Y));
                    }

                    path.AddArc(topRight, 270, 90);     // 右上角圆弧
                    path.AddArc(bottomRight, 0, 90);    // 右下角圆弧
                    path.AddArc(bottomLeft, 90, 90);    // 左下角圆弧
                    break;

                case ArrowDirection.Down:
                    // 下箭头，箭头在矩形下方
                    arrowPoint1 = new Point(arrowBase.X - arrowSize, arrowBase.Y);
                    arrowPoint2 = new Point(arrowBase.X + arrowSize, arrowBase.Y);

                    // 检查箭头位置是否会与圆角重叠
                    if (arrowBase.X - arrowSize < rect.X + cornerRadius)
                    {
                        arrowPoint1 = new Point(rect.X + cornerRadius, rect.Y + rect.Height);
                    }
                    if (arrowBase.X + arrowSize > rect.X + rect.Width - cornerRadius)
                    {
                        arrowPoint2 = new Point(rect.X + rect.Width - cornerRadius, rect.Y + rect.Height);
                    }

                    // 从左上角开始绘制路径
                    path.AddArc(topLeft, 180, 90);      // 左上角圆弧
                    path.AddArc(topRight, 270, 90);     // 右上角圆弧
                    path.AddArc(bottomRight, 0, 90);    // 右下角圆弧

                    // 判断箭头位置
                    if (arrowBase.X < rect.X + cornerRadius)
                    {
                        // 如果箭头在左下角附近
                        path.AddArc(bottomLeft, 90, 90);
                    }
                    else if (arrowBase.X > rect.X + rect.Width - cornerRadius)
                    {
                        // 如果箭头在右下角附近
                        path.AddArc(bottomLeft, 90, 90);
                    }
                    else
                    {
                        // 箭头在下边中间
                        path.AddLine(new Point(rect.X + rect.Width - cornerRadius, rect.Y + rect.Height), arrowPoint2);
                        path.AddLine(arrowPoint2, arrowTip);
                        path.AddLine(arrowTip, arrowPoint1);
                        path.AddLine(arrowPoint1, new Point(rect.X + cornerRadius, rect.Y + rect.Height));
                        path.AddArc(bottomLeft, 90, 90);
                    }
                    break;

                case ArrowDirection.Left:
                    // 左箭头，箭头在矩形左侧
                    arrowPoint1 = new Point(arrowBase.X, arrowBase.Y - arrowSize);
                    arrowPoint2 = new Point(arrowBase.X, arrowBase.Y + arrowSize);

                    // 检查箭头位置是否会与圆角重叠
                    if (arrowBase.Y - arrowSize < rect.Y + cornerRadius)
                    {
                        arrowPoint1 = new Point(rect.X, rect.Y + cornerRadius);
                    }
                    if (arrowBase.Y + arrowSize > rect.Y + rect.Height - cornerRadius)
                    {
                        arrowPoint2 = new Point(rect.X, rect.Y + rect.Height - cornerRadius);
                    }

                    // 从左上角开始绘制路径
                    path.AddArc(topLeft, 180, 90);

                    // 判断箭头位置
                    if (arrowBase.Y < rect.Y + cornerRadius)
                    {
                        // 箭头靠近左上角
                        path.AddLine(new Point(rect.X, rect.Y + cornerRadius), arrowPoint1);
                        path.AddLine(arrowPoint1, arrowTip);
                        path.AddLine(arrowTip, arrowPoint2);
                    }
                    else if (arrowBase.Y > rect.Y + rect.Height - cornerRadius)
                    {
                        // 箭头靠近左下角
                        path.AddLine(new Point(rect.X, rect.Y + cornerRadius), arrowPoint1);
                        path.AddLine(arrowPoint1, arrowTip);
                        path.AddLine(arrowTip, arrowPoint2);
                    }
                    else
                    {
                        // 箭头在左边中间
                        path.AddLine(new Point(rect.X, rect.Y + cornerRadius), arrowPoint1);
                        path.AddLine(arrowPoint1, arrowTip);
                        path.AddLine(arrowTip, arrowPoint2);
                        path.AddLine(arrowPoint2, new Point(rect.X, rect.Y + rect.Height - cornerRadius));
                    }

                    path.AddArc(bottomLeft, 90, 90);
                    path.AddArc(bottomRight, 0, 90);
                    path.AddArc(topRight, 270, 90);
                    break;

                case ArrowDirection.Right:
                    // 右箭头，箭头在矩形右侧
                    arrowPoint1 = new Point(arrowBase.X, arrowBase.Y - arrowSize);
                    arrowPoint2 = new Point(arrowBase.X, arrowBase.Y + arrowSize);

                    // 检查箭头位置是否会与圆角重叠
                    if (arrowBase.Y - arrowSize < rect.Y + cornerRadius)
                    {
                        arrowPoint1 = new Point(rect.X + rect.Width, rect.Y + cornerRadius);
                    }
                    if (arrowBase.Y + arrowSize > rect.Y + rect.Height - cornerRadius)
                    {
                        arrowPoint2 = new Point(rect.X + rect.Width, rect.Y + rect.Height - cornerRadius);
                    }

                    // 从左上角开始绘制路径
                    path.AddArc(topLeft, 180, 90);
                    path.AddArc(topRight, 270, 90);

                    // 判断箭头位置
                    if (arrowBase.Y < rect.Y + cornerRadius)
                    {
                        // 箭头靠近右上角
                        path.AddLine(new Point(rect.X + rect.Width, rect.Y + cornerRadius), arrowPoint1);
                        path.AddLine(arrowPoint1, arrowTip);
                        path.AddLine(arrowTip, arrowPoint2);
                    }
                    else if (arrowBase.Y > rect.Y + rect.Height - cornerRadius)
                    {
                        // 箭头靠近右下角
                        path.AddLine(new Point(rect.X + rect.Width, rect.Y + cornerRadius), arrowPoint1);
                        path.AddLine(arrowPoint1, arrowTip);
                        path.AddLine(arrowTip, arrowPoint2);
                    }
                    else
                    {
                        // 箭头在右边中间
                        path.AddLine(new Point(rect.X + rect.Width, rect.Y + cornerRadius), arrowPoint1);
                        path.AddLine(arrowPoint1, arrowTip);
                        path.AddLine(arrowTip, arrowPoint2);
                        path.AddLine(arrowPoint2, new Point(rect.X + rect.Width, rect.Y + rect.Height - cornerRadius));
                    }

                    path.AddArc(bottomRight, 0, 90);
                    path.AddArc(bottomLeft, 90, 90);
                    break;

                default:
                    // 其他情况下，只绘制圆角矩形
                    path.AddArc(topLeft, 180, 90);
                    path.AddArc(topRight, 270, 90);
                    path.AddArc(bottomRight, 0, 90);
                    path.AddArc(bottomLeft, 90, 90);
                    break;
            }

            // 闭合路径并设置填充模式
            path.CloseAllFigures();
            path.FillMode = FillMode.Winding;

            return path;
        }

        // 创建圆角矩形路径
        private GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius * 2, radius * 2, 180, 90); // 左上角圆弧
            path.AddArc(rect.Right - radius * 2, rect.Y, radius * 2, radius * 2, 270, 90); // 右上角圆弧
            path.AddArc(rect.Right - radius * 2, rect.Bottom - radius * 2, radius * 2, radius * 2, 0, 90); // 右下角圆弧
            path.AddArc(rect.X, rect.Bottom - radius * 2, radius * 2, radius * 2, 90, 90); // 左下角圆弧
            path.CloseFigure();
            return path;
        }

        // 获取箭头起点位置
        private Point GetArrowBasePoint(Rectangle rect, ArrowDirection arrow)
        {
            switch (arrow)
            {
                case ArrowDirection.Up:
                    return new Point(rect.X + rect.Width / 2, rect.Y);
                case ArrowDirection.Down:
                    return new Point(rect.X + rect.Width / 2, rect.Bottom);
                case ArrowDirection.Left:
                    return new Point(rect.X, rect.Y + rect.Height / 2);
                case ArrowDirection.Right:
                    return new Point(rect.Right, rect.Y + rect.Height / 2);
                default:
                    return new Point(rect.X + rect.Width / 2, rect.Bottom);
            }
        }

        // 获取箭头终点位置
        private Point GetArrowTipPoint(Point basePoint, ArrowDirection arrow, int length)
        {
            switch (arrow)
            {
                case ArrowDirection.Up:
                    return new Point(basePoint.X, basePoint.Y - length);
                case ArrowDirection.Down:
                    return new Point(basePoint.X, basePoint.Y + length);
                case ArrowDirection.Left:
                    return new Point(basePoint.X - length, basePoint.Y);
                case ArrowDirection.Right:
                    return new Point(basePoint.X + length, basePoint.Y);
                default:
                    return new Point(basePoint.X, basePoint.Y + length);
            }
        }

        // 设置高亮区域
        private void SetHighlight()
        {
            using (var g = CreateGraphics())
            {
                DrawHighlightWithBuffer(g);
            }
        }

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            if (_animationStep >= ANIMATION_STEPS)
            {
                _animationTimer.Stop();
                pnlMain.Location = _targetPanelLocation;
                _animationStep = 0;
                SetHighlight();
                return;
            }

            // 计算当前步骤的位置（使用缓动函数使动画更自然）
            float progress = (float)_animationStep / ANIMATION_STEPS;
            // 使用缓动函数：Cubic ease-out
            float easedProgress = 1 - (float)Math.Pow(1 - progress, 3);

            int newX = (int)(_startPanelLocation.X + ((_targetPanelLocation.X - _startPanelLocation.X) * easedProgress));
            int newY = (int)(_startPanelLocation.Y + ((_targetPanelLocation.Y - _startPanelLocation.Y) * easedProgress));

            pnlMain.Location = new Point(newX, newY);
            _animationStep++;
        }

        private ArrowDirection SetGuidePanelPosition(Rectangle targetArea)
        {
            ArrowDirection arrow;
            // 根据高亮区域的位置自适应调整引导提示框的位置
            int guidePanelWidth = pnlMain.Width;
            int guidePanelHeight = pnlMain.Height;
            int targetControlX = targetArea.X;
            int targetControlY = targetArea.Y;
            int targetControlWidth = targetArea.Width;
            int targetControlHeight = targetArea.Height;
            int screenWidth = Width;
            int screenHeight = Height;

            int guidePanelX, guidePanelY;
            if (targetArea.IsEmpty)
            {
                arrow = ArrowDirection.Down;
                // 在高亮区域下方显示
                guidePanelX = 0;
                guidePanelY = (screenHeight - guidePanelHeight) / 2;
            }
            else
            {
                if (targetControlX + targetControlWidth + guidePanelWidth <= screenWidth)
                {
                    arrow = ArrowDirection.Right;
                    // 在高亮区域右侧显示
                    guidePanelX = targetControlX + targetControlWidth + 20;
                    guidePanelY = targetControlY + (targetControlHeight - guidePanelHeight) / 2;
                }
                else if (targetControlX - guidePanelWidth >= 0)
                {
                    arrow = ArrowDirection.Left;
                    // 在高亮区域左侧显示
                    guidePanelX = targetControlX - guidePanelWidth - 20;
                    guidePanelY = targetControlY + (targetControlHeight - guidePanelHeight) / 2;
                }
                else if (targetControlY + targetControlHeight + guidePanelHeight <= screenHeight)
                {
                    arrow = ArrowDirection.Down;
                    // 在高亮区域下方显示
                    guidePanelX = targetControlX + (targetControlWidth - guidePanelWidth) / 2;
                    guidePanelY = targetControlY + targetControlHeight + 20;
                }
                else
                {
                    arrow = ArrowDirection.Up;
                    // 在高亮区域上方显示
                    guidePanelX = targetControlX + (targetControlWidth - guidePanelWidth) / 2;
                    guidePanelY = targetControlY - guidePanelHeight - 20;
                }
            }

            guidePanelX = Math.Max(0, guidePanelX);
            guidePanelY = Math.Max(0, guidePanelY);

            if (guidePanelX + guidePanelWidth > Width)
            {
                guidePanelX = Width - guidePanelWidth - 5;
            }
            if (guidePanelY + guidePanelHeight > Height)
            {
                guidePanelY = Height - guidePanelHeight - 5;
            }

            // 保存目标位置
            _targetPanelLocation = new Point(guidePanelX, guidePanelY);

            // 如果是第一次显示，直接设置位置
            if (pnlMain.Location.IsEmpty)
            {
                pnlMain.Location = _targetPanelLocation;
            }
            else
            {
                // 否则使用动画过渡
                _startPanelLocation = pnlMain.Location;
                _animationStep = 0;
                _animationTimer.Start();
            }

            return arrow;
        }

        private void UpdateButtonVisibility()
        {
            btnPrev.Visible = (_currentGuideIndex > 0);
            btnNext.Visible = (_currentGuideIndex <= guide.Items.Count - 1);
            lnkSkip.Visible = guide.Items.Count > 1;
            btnNext.Text = (_currentGuideIndex == guide.Items.Count - 1) ? "完成".CurrentText() : "下一步".CurrentText();
        }

        private Rectangle FindTargetControl(string controlName)
        {
            var rectangle = _mainForm.Controls.Find(controlName, true).FirstOrDefault()?.Bounds ?? Rectangle.Empty;
            if (rectangle.IsEmpty)
            {
                rectangle = _mainForm.Controls.OfType<Control>().FirstOrDefault(p => Equals(p.AccessibleDescription, controlName))?.Bounds ?? Rectangle.Empty;
            }
            return rectangle;
        }

        private void CloseGuide()
        {
            _currentGuideIndex = -1;
            Controls.Clear();

            this.Close();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // 释放托管资源
                if (_bufferedGraphics != null)
                {
                    _bufferedGraphics.Dispose();
                    _bufferedGraphics = null;
                }

                if (_backgroundImage != null)
                {
                    _backgroundImage.Dispose();
                    _backgroundImage = null;
                }

                if (_animationTimer != null)
                {
                    _animationTimer.Stop();
                    _animationTimer.Dispose();
                    _animationTimer = null;
                }
            }

            base.Dispose(disposing);
        }
    }
}
