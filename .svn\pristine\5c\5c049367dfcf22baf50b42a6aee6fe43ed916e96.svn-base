﻿using Microsoft.Win32;
using OCRTools.Common;
using ShareX.ScreenCaptureLib;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Management;
using System.Net;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Security.AccessControl;
using System.Security.Cryptography;
using System.Security.Principal;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using System.Windows.Forms;

namespace OCRTools
{
    public class CommonConfig
    {
        public static ServerConfigEntity ServerConfig = new ServerConfigEntity();

        static CommonConfig()
        {
            InitServerConfig();
        }

        public static void InitServerConfig()
        {
            var lstMsg = new List<UserMsgEntity>();
            try
            {
                var result =
                    WebClientExt.GetHtml(CommonString.HostUpdate?.FullUrl + "update/umsg.txt?t=" + ServerTime.DateTime.Ticks, 5);
                if (!string.IsNullOrEmpty(result) && result.Length > 2)
                    ServerConfig = CommonString.JavaScriptSerializer.Deserialize<ServerConfigEntity>(result);
            }
            catch { }
        }
    }


    [Obfuscation]
    public class ServerConfigEntity
    {
        /// <summary>
        /// 引导注册Msg
        /// </summary>
        [Obfuscation] public string RegTipMsg { get; set; }

        /// <summary>
        /// 引导升级Vip Msg
        /// </summary>
        [Obfuscation] public string UpgradeMsg { get; set; }

        /// <summary>
        /// 过期提醒文字描述
        /// </summary>
        [Obfuscation]
        public string ReminderMsg { get; set; }
    }
}