using System;
using System.Collections.Generic;
using System.Linq;
using OCRTools.Common;
using static OCRTools.UserControlEx.TextFlowDirectionDetector;

namespace OCRTools.UserControlEx
{
    /// <summary>
    /// 文本流方向检测器，负责分析文本块的排列方向。
    /// </summary>
    public class TextFlowDirectionDetector
    {
        // 文档类型枚举 - 用于动态权重调整
        public enum TextFlowDocumentType
        {
            Normal,             // 普通文本文档
            HighStructure,      // 高结构化文档
            DefinitiveStructure, // 明确结构文档
            LowQuality          // 低质量文档
        }

        private readonly List<TextCellInfo> cells;

        // 【新增】特征权重管理器实例
        private readonly FeatureWeightManager weightManager;

        // 自适应分组阈值
        private double groupingThresholdVertical = 10;
        private double groupingThresholdHorizontal = 10;

        public TextFlowDirectionDetector(List<TextCellInfo> cells)
        {
            this.cells = cells;

            // 【新增】初始化特征权重管理器
            this.weightManager = new FeatureWeightManager();
        }

        /// <summary>
        /// 检测文本方向（兼容旧版本）
        /// </summary>
        /// <returns>主要文本方向</returns>
        public TextFlowDirection Detect()
        {
            var result = DetectDirections();
            return result.GetPrimaryDirection();
        }

        /// <summary>
        /// 检测文本流方向
        /// </summary>
        public TextDirectionResult DetectDirections()
        {
            if (cells == null || cells.Count < 2)
            {
                return new TextDirectionResult
                {
                    IsVerticalLayout = false,
                    FlowDirection = TextFlowDirection.LeftToRight,
                    LayoutConfidence = 50,
                    DirectionConfidence = 50
                };
            }

            try
            {
                // 1. 使用统一的特征提取和整合方法
                TextLayoutFeatures features;
                DirectionEvidence evidence;
                ExtractAndIntegrateFeatures(cells, out features, out evidence);

                // 2. 创建结果对象
                var result = new TextDirectionResult();

                // 4. 确定布局类型 (横排/竖排)
                DetermineLayoutType(result, evidence);

                // 5. 确定文本流方向
                if (result.IsVerticalLayout)
                {
                    // 竖排布局 - 确定是从上到下还是从下到上
                    DetermineVerticalFlowDirection(result, evidence);
                }
                else
                {
                    // 横排布局 - 确定是从左到右还是从右到左
                    DetermineHorizontalFlowDirection(result, evidence);
                }

                // 6. 优化方向检测结果
                OptimizeDirectionDetection(cells, result);

                // 7. 确保水平和垂直方向与主方向一致
                if (result.IsVerticalLayout)
                {
                    result.VerticalDirection = result.FlowDirection;
                    result.VerticalConfidence = result.DirectionConfidence;

                    // 【新增】确保竖排文本的水平方向（列排序）与IsRightToLeft一致
                    if (result.IsRightToLeft && result.HorizontalDirection != TextFlowDirection.RightToLeft)
                    {
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        // 如果水平方向置信度为0或过低，设置一个合理的值
                        if (result.HorizontalConfidence < 70)
                        {
                            // 检查是否为中文竖排文本
                            bool likelyCJKText = false;
                            int cjkCharCount = 0;
                            int totalCharCount = 0;

                            foreach (var cell in cells)
                            {
                                if (string.IsNullOrEmpty(cell.words)) continue;

                                foreach (char c in cell.words)
                                {
                                    totalCharCount++;
                                    // 检查是否为中日韩文字（粗略判断）
                                    if (c >= 0x4E00 && c <= 0x9FFF) // 基本汉字范围
                                    {
                                        cjkCharCount++;
                                    }
                                }
                            }

                            // 如果大部分是中日韩文字，增加置信度
                            if (totalCharCount > 0 && (double)cjkCharCount / totalCharCount > 0.6)
                            {
                                likelyCJKText = true;
                            }

                            result.HorizontalConfidence = likelyCJKText ? 85 : 70; // 中文竖排文本更高的置信度
                            System.Diagnostics.Debug.WriteLine($"最终确保水平方向一致性，设置RightToLeft置信度为: {result.HorizontalConfidence}%");
                        }
                    }
                    else if (!result.IsRightToLeft && result.HorizontalDirection == TextFlowDirection.RightToLeft)
                    {
                        // 保持一致性，如果水平方向是从右到左，IsRightToLeft也应该为true
                        result.IsRightToLeft = true;
                    }
                }
                else
                {
                    result.HorizontalDirection = result.FlowDirection;
                    result.HorizontalConfidence = result.DirectionConfidence;

                    // 【新增】确保水平方向与IsRightToLeft一致
                    result.IsRightToLeft = (result.HorizontalDirection == TextFlowDirection.RightToLeft);
                }

                // 【新增】解决形状特征与直方图特征矛盾的问题
                ResolveFeatureContradictions(result, evidence);

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"文本方向检测异常: {ex.Message}");

                // 返回默认结果
                return new TextDirectionResult
                {
                    IsVerticalLayout = false,
                    FlowDirection = TextFlowDirection.LeftToRight,
                    LayoutConfidence = 50,
                    DirectionConfidence = 50
                };
            }
        }

        /// <summary>
        /// 优化竖排布局的方向判断
        /// </summary>
        /// <param name="result">方向结果</param>
        /// <param name="features">文本布局特征</param>
        /// <param name="evidence">方向证据</param>
        private void OptimizeVerticalLayoutDirections(TextDirectionResult result, TextLayoutFeatures features, DirectionEvidence evidence)
        {
            // 针对竖排文本的特殊优化
            if (features.VerticalTextCount <= features.HorizontalTextCount) return;

            // 1. 多列竖排文本的水平方向判断
            if (features.VerticalColumnCount >= 2)
            {
                // 计算右到左和左到右的证据比例
                double totalHorizontalEvidence = evidence.LeftToRightCount + evidence.RightToLeftCount;
                if (totalHorizontalEvidence > 0)
                {
                    double rtlRatio = evidence.RightToLeftCount / totalHorizontalEvidence;

                    // 【修改】降低阈值，更倾向于判断为从右到左
                    // 如果右到左证据占比较高，调整方向和置信度
                    if (rtlRatio > 0.35) // 阈值降低，从0.4降低到0.35
                    {
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.IsRightToLeft = true; // 同步设置IsRightToLeft属性

                        // 置信度基于证据比例
                        int confidenceBoost = (int)(Math.Min(30, features.VerticalColumnCount * 6) *
                                                   Math.Min(1.0, rtlRatio + 0.25));

                        result.HorizontalConfidence = Math.Min(95,
                            Math.Max(result.HorizontalConfidence, result.HorizontalConfidence + confidenceBoost));
                    }
                }

                // 2. 基于内容分布的判断
                if (features.ContentDensityRightHalf > 0 && features.ContentDensityLeftHalf > 0)
                {
                    double densityRatio = features.ContentDensityRightHalf / features.ContentDensityLeftHalf;

                    // 【修改】降低阈值，更敏感地检测右半页密度高于左半页的情况
                    // 右半页密度明显高于左半页，增强从右到左的可能性
                    if (densityRatio > 1.1) // 阈值降低，从1.2降低到1.1
                    {
                        // 如果已经是RightToLeft，增强置信度
                        if (result.HorizontalDirection == TextFlowDirection.RightToLeft)
                        {
                            result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 15);
                            System.Diagnostics.Debug.WriteLine($"右半页密度明显高于左半页(比例:{densityRatio:F2})，增强RightToLeft置信度");
                        }
                        // 如果是LeftToRight但置信度不高，考虑切换
                        else if (result.HorizontalConfidence < 75) // 提高阈值，从70提高到75
                        {
                            result.HorizontalDirection = TextFlowDirection.RightToLeft;
                            result.HorizontalConfidence = Math.Max(result.HorizontalConfidence, 75);
                            System.Diagnostics.Debug.WriteLine("基于内容分布，切换到RightToLeft方向");
                        }
                    }
                    // 左半页密度明显高于右半页，增强从左到右的可能性
                    else if (densityRatio < 0.8)
                    {
                        // 如果已经是LeftToRight，增强置信度
                        if (result.HorizontalDirection == TextFlowDirection.LeftToRight)
                        {
                            result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 10);
                        }
                        // 如果是RightToLeft但置信度不高，考虑切换
                        else if (result.HorizontalConfidence < 70)
                        {
                            result.HorizontalDirection = TextFlowDirection.LeftToRight;
                            result.HorizontalConfidence = Math.Max(70, result.HorizontalConfidence);
                            System.Diagnostics.Debug.WriteLine("基于内容分布，切换到LeftToRight方向");
                        }
                    }
                }

                // 【新增】3. 分析列的排列顺序
                if (features.VerticalColumns.Count >= 2)
                {
                    // 计算每列的平均字符数或内容量
                    List<double> columnContentAmounts = new List<double>();
                    foreach (var column in features.VerticalColumns)
                    {
                        double contentAmount = column.Sum(c => c.words?.Length ?? 0);
                        columnContentAmounts.Add(contentAmount);
                    }

                    // 检查是否第一列（最左侧或最右侧）内容量最大
                    if (columnContentAmounts.Count >= 2)
                    {
                        double firstColumnAmount = columnContentAmounts.First();
                        double lastColumnAmount = columnContentAmounts.Last();
                        double avgColumnAmount = columnContentAmounts.Average();

                        // 如果最右侧列内容量明显大于平均值，可能是从右到左阅读
                        if (firstColumnAmount > avgColumnAmount * 1.2 &&
                            features.VerticalColumns.First().Average(c => c.location.left) > features.PageCenter_X)
                        {
                            if (result.HorizontalDirection == TextFlowDirection.RightToLeft)
                            {
                                result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 10);
                                System.Diagnostics.Debug.WriteLine("最右侧列内容量最大，增强RightToLeft置信度");
                            }
                            else if (result.HorizontalConfidence < 70)
                            {
                                result.HorizontalDirection = TextFlowDirection.RightToLeft;
                                result.HorizontalConfidence = Math.Max(70, result.HorizontalConfidence);
                                System.Diagnostics.Debug.WriteLine("基于列内容分布，切换到RightToLeft方向");
                            }
                        }
                        // 如果最左侧列内容量明显大于平均值，可能是从左到右阅读
                        else if (lastColumnAmount > avgColumnAmount * 1.2 &&
                                features.VerticalColumns.Last().Average(c => c.location.left) < features.PageCenter_X)
                        {
                            if (result.HorizontalDirection == TextFlowDirection.LeftToRight)
                            {
                                result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 10);
                            }
                            else if (result.HorizontalConfidence < 70)
                            {
                                result.HorizontalDirection = TextFlowDirection.LeftToRight;
                                result.HorizontalConfidence = Math.Max(70, result.HorizontalConfidence);
                                System.Diagnostics.Debug.WriteLine("基于列内容分布，切换到LeftToRight方向");
                            }
                        }
                    }
                }
            }

            // 3. 单列竖排文本的特殊处理
            else if (features.VerticalTextCount >= 3)
            {
                // 由于在ExtractTextDirectionFeatures中已检测了CJK文本特征
                // 这里直接通过features中的LeftToRightEvidence和RightToLeftEvidence判断
                bool likelyCJKText = features.RightToLeftEvidence > features.LeftToRightEvidence;

                // 【修改】增强中日韩单列竖排文本的从右到左判断
                // 对于中日韩文字的单列竖排，增强从右到左的可能性
                if (likelyCJKText)
                {
                    // 【修改】无论当前置信度如何，都优先考虑从右到左方向
                    result.HorizontalDirection = TextFlowDirection.RightToLeft;
                    result.IsRightToLeft = true; // 同步设置IsRightToLeft属性
                    result.HorizontalConfidence = Math.Max(result.HorizontalConfidence, 85); // 提高置信度到85
                    System.Diagnostics.Debug.WriteLine("检测到疑似中日韩单列竖排文本，强制设置为RightToLeft方向，置信度: 85%");

                    // 【新增】检查文本位置，如果偏向页面右侧，进一步增强置信度
                    double avgTextCenter = this.cells.Average(c => c.location.left + c.location.width / 2);
                    if (avgTextCenter > features.PageCenter_X)
                    {
                        result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 10);
                        System.Diagnostics.Debug.WriteLine("文本位于页面右侧，进一步增强RightToLeft置信度到: " + result.HorizontalConfidence + "%");
                    }
                }
                // 【新增】即使不是CJK文本，如果是单列竖排，也应该有默认的水平方向置信度
                else if (result.HorizontalConfidence < 60)
                {
                    // 设置一个适中的默认置信度，避免置信度为0
                    result.HorizontalConfidence = 60;
                    System.Diagnostics.Debug.WriteLine("单列竖排文本，设置默认水平方向置信度: 60%");
                }
            }

            // 4. 确保垂直方向置信度较高
            if (result.VerticalDirection == TextFlowDirection.TopToBottom)
            {
                result.VerticalConfidence = Math.Max(result.VerticalConfidence, 85);
            }
        }

        /// <summary>
        /// 检查文本是否主要为中日韩文字
        /// </summary>
        /// <param name="cells">文本单元格列表</param>
        /// <returns>是否为中日韩文字</returns>
        private bool IsCJKText(List<TextCellInfo> cells)
        {
            int cjkCharCount = 0;
            int totalCharCount = 0;

            foreach (var cell in cells)
            {
                if (string.IsNullOrEmpty(cell.words)) continue;

                foreach (char c in cell.words)
                {
                    totalCharCount++;
                    // 【修改】扩大中日韩文字的检测范围
                    // 检查是否为中日韩文字（扩展判断范围）
                    if ((c >= 0x4E00 && c <= 0x9FFF) || // 基本汉字范围
                        (c >= 0x3400 && c <= 0x4DBF) || // 扩展A区
                        (c >= 0x20000 && c <= 0x2A6DF) || // 扩展B区
                        (c >= 0x2A700 && c <= 0x2B73F) || // 扩展C区
                        (c >= 0x2B740 && c <= 0x2B81F) || // 扩展D区
                        (c >= 0x3040 && c <= 0x309F) || // 平假名
                        (c >= 0x30A0 && c <= 0x30FF) || // 片假名
                        (c >= 0xAC00 && c <= 0xD7AF))   // 韩文音节
                    {
                        cjkCharCount++;
                    }
                }
            }

            // 【修改】降低阈值，从0.7降低到0.6
            // 如果60%以上是中日韩文字，认为是中日韩文本
            return totalCharCount > 0 && (double)cjkCharCount / totalCharCount > 0.6;
        }

        /// <summary>
        /// 对复杂文档应用高级置信度优化
        /// </summary>
        private void ApplyAdvancedConfidenceOptimization(TextDirectionResult result, DirectionEvidence evidence, List<TextCellInfo> cells)
        {
            // 1. 检查布局和主方向的一致性
            // 检查是否存在强烈的一致性证据
            bool hasStrongConsistency = false;

            // 横排文本与从左到右方向一致性
            if (!result.IsVerticalLayout && result.HorizontalDirection == TextFlowDirection.LeftToRight &&
                evidence.LeftToRightCount > evidence.RightToLeftCount * 2 &&
                evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.5)
            {
                hasStrongConsistency = true;
            }

            // 竖排文本与从上到下方向一致性
            else if (result.IsVerticalLayout && result.VerticalDirection == TextFlowDirection.TopToBottom &&
                    evidence.TopToBottomCount > evidence.BottomToTopCount * 2 &&
                    evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5)
            {
                hasStrongConsistency = true;
            }

            // 如果有强烈一致性，适当提高置信度
            if (hasStrongConsistency)
            {
                // 同时提高布局和方向置信度
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + 5);

                if (!result.IsVerticalLayout)
                {
                    result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 5);
                }
                else
                {
                    result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + 5);
                }
            }

            // 2. 一致性特征分析
            // 计算各种证据的互相支持程度
            int consistencyScore = 0;

            // 文本形状与对齐特征一致性
            if ((evidence.HorizontalTextCount > evidence.VerticalTextCount &&
                 evidence.LeftAlignedCount > evidence.TopAlignedCount) ||
                (evidence.VerticalTextCount > evidence.HorizontalTextCount &&
                 evidence.TopAlignedCount > evidence.LeftAlignedCount))
            {
                consistencyScore += 2;
            }

            // 边界变异与方向一致性
            if ((evidence.LeftEdgeVariance < evidence.RightEdgeVariance &&
                 result.HorizontalDirection == TextFlowDirection.LeftToRight) ||
                (evidence.RightEdgeVariance < evidence.LeftEdgeVariance &&
                 result.HorizontalDirection == TextFlowDirection.RightToLeft) ||
                (evidence.TopEdgeVariance < evidence.BottomEdgeVariance &&
                 result.VerticalDirection == TextFlowDirection.TopToBottom) ||
                (evidence.BottomEdgeVariance < evidence.TopEdgeVariance &&
                 result.VerticalDirection == TextFlowDirection.BottomToTop))
            {
                consistencyScore += 2;
            }

            // 段落布局与布局类型一致性
            if ((evidence.ParagraphCount >= 3 && !result.IsVerticalLayout) ||
                (evidence.IsSequentialParagraphs && evidence.LeftAlignmentRatio > 0.7 && !result.IsVerticalLayout))
            {
                consistencyScore += 2;
            }

            // 应用一致性评分
            if (consistencyScore >= 4)
            {
                // 高一致性得分提高整体置信度
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + consistencyScore / 2);
                result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + consistencyScore / 2);
                result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + consistencyScore / 2);
            }
        }

        /// <summary>
        /// 确定文档的布局类型（横排或竖排）
        /// </summary>
        /// <remarks>
        /// 这个方法是布局类型判断的核心算法，使用多维度特征的累加得分系统来判断文档是横排还是竖排布局。
        /// 该方法考虑多种布局证据，包括文本形状、行列结构、对齐特征等，适用于各种语言的文档。
        /// </remarks>
        /// <param name="result">输出的文本方向结果，将设置布局类型</param>
        /// <param name="evidence">收集的方向证据</param>
        private void DetermineLayoutType(TextDirectionResult result, DirectionEvidence evidence)
        {
            // ====================== 直方图特征分析 ======================
            // 首先分析直方图特征，如果直方图特征非常明显，可以直接决定布局类型
            int histogramLayoutScore = AnalyzeHistogramLayoutEvidence(evidence);

            // 【修改】保留对明显直方图特征的优先处理，但调整阈值和置信度计算
            if (Math.Abs(histogramLayoutScore) > 20) // 提高阈值，确保只有非常明显的直方图特征才会直接决定
            {
                // 【新增】检查形状特征是否与直方图特征严重矛盾
                bool hasStrongContradiction = false;

                // 如果直方图暗示竖排，但横向文本块数量远多于竖向文本块
                if (histogramLayoutScore > 0 && evidence.HorizontalTextCount > evidence.VerticalTextCount * 3)
                {
                    hasStrongContradiction = true;
                    System.Diagnostics.Debug.WriteLine($"警告：直方图特征暗示竖排布局，但形状特征显示横向文本块数量远多于竖向文本块({evidence.HorizontalTextCount} vs {evidence.VerticalTextCount})");
                }
                // 如果直方图暗示横排，但竖向文本块数量远多于横向文本块
                else if (histogramLayoutScore < 0 && evidence.VerticalTextCount > evidence.HorizontalTextCount * 3)
                {
                    hasStrongContradiction = true;
                    System.Diagnostics.Debug.WriteLine($"警告：直方图特征暗示横排布局，但形状特征显示竖向文本块数量远多于横向文本块({evidence.VerticalTextCount} vs {evidence.HorizontalTextCount})");
                }

                // 【新增】检查行列结构是否与直方图特征矛盾
                // 如果直方图暗示竖排，但行数明显多于列数
                if (histogramLayoutScore > 0 && evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns * 2 && evidence.HorizontalAlignedRows >= 3)
                {
                    hasStrongContradiction = true;
                    System.Diagnostics.Debug.WriteLine($"警告：直方图特征暗示竖排布局，但行列结构显示行数明显多于列数({evidence.HorizontalAlignedRows} vs {evidence.VerticalAlignedColumns})");
                }
                // 如果直方图暗示横排，但列数明显多于行数
                else if (histogramLayoutScore < 0 && evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows * 2 && evidence.VerticalAlignedColumns >= 3)
                {
                    hasStrongContradiction = true;
                    System.Diagnostics.Debug.WriteLine($"警告：直方图特征暗示横排布局，但行列结构显示列数明显多于行数({evidence.VerticalAlignedColumns} vs {evidence.HorizontalAlignedRows})");
                }

                // 如果没有严重矛盾，可以直接根据直方图结果确定布局类型
                if (!hasStrongContradiction)
                {
                    // 直接根据直方图结果确定布局类型
                    result.IsVerticalLayout = histogramLayoutScore > 0;
                    result.LayoutConfidence = Math.Min(95, 65 + Math.Abs(histogramLayoutScore) / 2);

                    // 【新增】根据布局类型设置合理的默认方向置信度
                    if (result.IsVerticalLayout)
                    {
                        // 竖排布局，设置垂直方向默认置信度
                        if (result.VerticalConfidence <= 0)
                        {
                            result.VerticalConfidence = 70;
                            System.Diagnostics.Debug.WriteLine("设置竖排布局的垂直方向默认置信度: 70%");
                        }
                    }
                    else
                    {
                        // 横排布局，设置水平方向默认置信度
                        if (result.HorizontalConfidence <= 0)
                        {
                            result.HorizontalConfidence = 70;
                            System.Diagnostics.Debug.WriteLine("设置横排布局的水平方向默认置信度: 70%");
                        }

                        // 【新增】同时设置垂直方向默认置信度
                        if (result.VerticalConfidence <= 0)
                        {
                            result.VerticalConfidence = 65;
                            System.Diagnostics.Debug.WriteLine("设置横排布局的垂直方向默认置信度: 65%");
                        }
                    }

                    // 记录决策依据
                    System.Diagnostics.Debug.WriteLine($"基于极强直方图特征确定布局: {(result.IsVerticalLayout ? "竖排" : "横排")}，置信度: {result.LayoutConfidence}");
                    return;
                }
                else
                {
                    // 有严重矛盾，降低直方图特征的权重，继续进行综合分析
                    histogramLayoutScore = (int)(histogramLayoutScore * 0.3); // 更大幅度降低直方图特征权重
                    System.Diagnostics.Debug.WriteLine($"直方图特征与形状特征严重矛盾，降低直方图特征权重至30%，调整后得分: {histogramLayoutScore}");
                }
            }

            // 默认为横排布局
            result.IsVerticalLayout = false;
            int verticalLayoutScore = 0;
            int horizontalLayoutScore = 0;

            // 【改进】使用特征权重管理器动态计算形状特征权重
            // 1. 基于文本块形状的判断（使用特征权重管理器）
            TextLayoutFeatures features = new TextLayoutFeatures();
            features.VerticalTextCount = evidence.VerticalTextCount;
            features.HorizontalTextCount = evidence.HorizontalTextCount;
            features.DocumentTypeCategory = TextFlowDocumentType.Normal; // 默认使用普通文档类型

            // 使用权重管理器获取形状特征权重
            double shapeWeight = weightManager.GetFeatureWeight(
                FeatureWeightManager.FeatureType.Shape,
                FeatureWeightManager.FeatureDirection.Both,
                features.DocumentTypeCategory);

            System.Diagnostics.Debug.WriteLine($"形状特征权重: {shapeWeight:F2}");

            if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 2)
            {
                // 横向文本块数量显著多于竖向文本块，强烈暗示横排布局
                int baseScore = 15;
                int weightedScore = (int)(baseScore * shapeWeight);
                horizontalLayoutScore += weightedScore;
                System.Diagnostics.Debug.WriteLine($"横向文本块显著多于竖向文本块 ({evidence.HorizontalTextCount} vs {evidence.VerticalTextCount})，横排布局得分 +{weightedScore} (基础分:{baseScore}, 权重:{shapeWeight:F2})");
            }
            else if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 2)
            {
                // 竖向文本块数量显著多于横向文本块，强烈暗示竖排布局
                int baseScore = 15;
                int weightedScore = (int)(baseScore * shapeWeight);
                verticalLayoutScore += weightedScore;
                System.Diagnostics.Debug.WriteLine($"竖向文本块显著多于横向文本块 ({evidence.VerticalTextCount} vs {evidence.HorizontalTextCount})，竖排布局得分 +{weightedScore} (基础分:{baseScore}, 权重:{shapeWeight:F2})");
            }
            else if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.5)
            {
                // 横向文本块数量较多，中等程度暗示横排布局
                int baseScore = 10;
                int weightedScore = (int)(baseScore * shapeWeight);
                horizontalLayoutScore += weightedScore;
                System.Diagnostics.Debug.WriteLine($"横向文本块较多 ({evidence.HorizontalTextCount} vs {evidence.VerticalTextCount})，横排布局得分 +{weightedScore} (基础分:{baseScore}, 权重:{shapeWeight:F2})");
            }
            else if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5)
            {
                // 竖向文本块数量较多，中等程度暗示竖排布局
                int baseScore = 10;
                int weightedScore = (int)(baseScore * shapeWeight);
                verticalLayoutScore += weightedScore;
                System.Diagnostics.Debug.WriteLine($"竖向文本块较多 ({evidence.VerticalTextCount} vs {evidence.HorizontalTextCount})，竖排布局得分 +{weightedScore} (基础分:{baseScore}, 权重:{shapeWeight:F2})");
            }
            else if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.2)
            {
                // 横向文本块略多，弱暗示横排布局
                int baseScore = 5;
                int weightedScore = (int)(baseScore * shapeWeight);
                horizontalLayoutScore += weightedScore;
                System.Diagnostics.Debug.WriteLine($"横向文本块略多 ({evidence.HorizontalTextCount} vs {evidence.VerticalTextCount})，横排布局得分 +{weightedScore} (基础分:{baseScore}, 权重:{shapeWeight:F2})");
            }
            else if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.2)
            {
                // 竖向文本块略多，弱暗示竖排布局
                int baseScore = 5;
                int weightedScore = (int)(baseScore * shapeWeight);
                verticalLayoutScore += weightedScore;
                System.Diagnostics.Debug.WriteLine($"竖向文本块略多 ({evidence.VerticalTextCount} vs {evidence.HorizontalTextCount})，竖排布局得分 +{weightedScore} (基础分:{baseScore}, 权重:{shapeWeight:F2})");
            }

            // 【新增】极端情况处理 - 如果一种文本块类型占据绝对优势
            if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 5)
            {
                // 横向文本块占绝对优势，几乎可以确定是横排布局
                int baseScore = 25;
                // 极端情况下，提高权重
                double extremeWeight = shapeWeight * 1.2;
                int weightedScore = (int)(baseScore * extremeWeight);
                horizontalLayoutScore += weightedScore;
                System.Diagnostics.Debug.WriteLine($"横向文本块占绝对优势 ({evidence.HorizontalTextCount} vs {evidence.VerticalTextCount})，横排布局得分大幅提升 +{weightedScore} (基础分:{baseScore}, 权重:{extremeWeight:F2})");
            }
            else if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 5)
            {
                // 竖向文本块占绝对优势，几乎可以确定是竖排布局
                int baseScore = 25;
                // 极端情况下，提高权重
                double extremeWeight = shapeWeight * 1.2;
                int weightedScore = (int)(baseScore * extremeWeight);
                verticalLayoutScore += weightedScore;
                System.Diagnostics.Debug.WriteLine($"竖向文本块占绝对优势 ({evidence.VerticalTextCount} vs {evidence.HorizontalTextCount})，竖排布局得分大幅提升 +{weightedScore} (基础分:{baseScore}, 权重:{extremeWeight:F2})");
            }

            // 2. 基于边缘对齐的判断
            if (evidence.LeftAlignedCount > evidence.TopAlignedCount * 1.5)
            {
                // 左对齐文本块显著多于顶部对齐，暗示横排布局
                horizontalLayoutScore += 8;
                System.Diagnostics.Debug.WriteLine($"左对齐文本块显著多于顶部对齐 ({evidence.LeftAlignedCount} vs {evidence.TopAlignedCount})，横排布局得分 +8");
            }
            else if (evidence.TopAlignedCount > evidence.LeftAlignedCount * 1.5)
            {
                // 顶部对齐文本块显著多于左对齐，暗示竖排布局
                verticalLayoutScore += 8;
                System.Diagnostics.Debug.WriteLine($"顶部对齐文本块显著多于左对齐 ({evidence.TopAlignedCount} vs {evidence.LeftAlignedCount})，竖排布局得分 +8");
            }

            // 3. 基于边缘变异性的判断
            if (evidence.LeftEdgeVariance < evidence.TopEdgeVariance * 0.5)
            {
                // 左边缘变异性显著小于顶部边缘，暗示横排布局
                horizontalLayoutScore += 6;
                System.Diagnostics.Debug.WriteLine($"左边缘变异性显著小于顶部边缘 ({evidence.LeftEdgeVariance:F2} vs {evidence.TopEdgeVariance:F2})，横排布局得分 +6");
            }
            else if (evidence.TopEdgeVariance < evidence.LeftEdgeVariance * 0.5)
            {
                // 顶部边缘变异性显著小于左边缘，暗示竖排布局
                verticalLayoutScore += 6;
                System.Diagnostics.Debug.WriteLine($"顶部边缘变异性显著小于左边缘 ({evidence.TopEdgeVariance:F2} vs {evidence.LeftEdgeVariance:F2})，竖排布局得分 +6");
            }

            // 4. 基于行列结构的判断
            if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns * 1.5)
            {
                // 水平对齐行显著多于垂直对齐列，暗示横排布局
                horizontalLayoutScore += 10;
                System.Diagnostics.Debug.WriteLine($"水平对齐行显著多于垂直对齐列 ({evidence.HorizontalAlignedRows} vs {evidence.VerticalAlignedColumns})，横排布局得分 +10");
            }
            else if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows * 1.5)
            {
                // 垂直对齐列显著多于水平对齐行，暗示竖排布局
                verticalLayoutScore += 10;
                System.Diagnostics.Debug.WriteLine($"垂直对齐列显著多于水平对齐行 ({evidence.VerticalAlignedColumns} vs {evidence.HorizontalAlignedRows})，竖排布局得分 +10");
            }

            // 【改进】使用特征权重管理器动态调整直方图特征权重
            // 5. 基于直方图分析的判断
            if (histogramLayoutScore != 0)
            {
                // 更新特征信息用于权重计算
                features.VerticalPeakShapeQuality = evidence.VerticalPeakShapeQuality;
                features.HorizontalPeakShapeQuality = evidence.HorizontalPeakShapeQuality;
                features.VerticalPeakCount = evidence.VerticalPeakCount;
                features.HorizontalPeakCount = evidence.HorizontalPeakCount;
                features.HistogramFeatureReliability = Math.Min(1.0, Math.Max(0.0,
                    (evidence.VerticalPeakShapeQuality + evidence.HorizontalPeakShapeQuality) / 2));

                // 使用权重管理器获取直方图特征权重
                double baseHistogramWeight = weightManager.GetFeatureWeight(
                    FeatureWeightManager.FeatureType.Histogram,
                    FeatureWeightManager.FeatureDirection.Both,
                    features.DocumentTypeCategory);

                // 根据直方图特征的强度动态调整权重
                double histogramQuality = features.HistogramFeatureReliability;

                // 【保留】根据形状特征与直方图特征的一致性调整权重
                bool isConsistent = false;
                if (histogramLayoutScore > 0 && evidence.VerticalTextCount >= evidence.HorizontalTextCount)
                {
                    // 直方图暗示竖排 + 形状特征支持竖排 = 一致
                    isConsistent = true;
                }
                else if (histogramLayoutScore < 0 && evidence.HorizontalTextCount >= evidence.VerticalTextCount)
                {
                    // 直方图暗示横排 + 形状特征支持横排 = 一致
                    isConsistent = true;
                }

                // 【保留】根据形状特征的强度调整一致性权重
                double shapeStrength = 0.0;
                if (evidence.HorizontalTextCount > 0 || evidence.VerticalTextCount > 0)
                {
                    double totalTextCount = evidence.HorizontalTextCount + evidence.VerticalTextCount;
                    double horizontalRatio = evidence.HorizontalTextCount / totalTextCount;
                    double verticalRatio = evidence.VerticalTextCount / totalTextCount;

                    // 计算形状偏向的强度（0.5表示均衡，接近0或1表示强烈偏向）
                    shapeStrength = Math.Abs(horizontalRatio - 0.5) * 2; // 将0-0.5范围映射到0-1
                }

                // 【改进】在基础权重上应用一致性和质量调整
                double histogramWeight = baseHistogramWeight;

                if (isConsistent)
                {
                    // 一致时，提升权重
                    histogramWeight = Math.Min(histogramWeight * 1.3, histogramWeight + 0.3);
                    System.Diagnostics.Debug.WriteLine($"直方图特征与形状特征一致，权重提升至 {histogramWeight:F2}");
                }
                else
                {
                    // 不一致时，降低权重，形状特征越强，降低越多
                    double reductionFactor = Math.Min(0.7, 0.3 + shapeStrength * 0.4);
                    histogramWeight = histogramWeight * (1.0 - reductionFactor);
                    System.Diagnostics.Debug.WriteLine($"直方图特征与形状特征不一致，权重降低至 {histogramWeight:F2} (降低因子:{reductionFactor:F2})");

                    // 如果形状特征非常强（如文本块数量差异巨大），进一步降低直方图权重
                    if (shapeStrength > 0.8)
                    {
                        histogramWeight *= 0.7;
                        System.Diagnostics.Debug.WriteLine($"形状特征非常强，直方图特征权重进一步降低至 {histogramWeight:F2}");
                    }
                }

                // 根据直方图质量调整权重
                histogramWeight = histogramWeight * (0.7 + histogramQuality * 0.3);

                System.Diagnostics.Debug.WriteLine($"直方图特征基础权重: {baseHistogramWeight:F2}, 质量: {histogramQuality:F2}, 与形状特征一致性: {isConsistent}, 形状强度: {shapeStrength:F2}, 最终权重: {histogramWeight:F2}");

                if (histogramLayoutScore > 0)
                {
                    // 直方图分析暗示竖排布局
                    int baseScore = histogramLayoutScore;
                    int weightedScore = (int)(baseScore * histogramWeight);
                    verticalLayoutScore += weightedScore;
                    System.Diagnostics.Debug.WriteLine($"直方图分析暗示竖排布局，竖排布局得分 +{weightedScore} (基础分:{baseScore}, 权重:{histogramWeight:F2})");
                }
                else
                {
                    // 直方图分析暗示横排布局
                    int baseScore = Math.Abs(histogramLayoutScore);
                    int weightedScore = (int)(baseScore * histogramWeight);
                    horizontalLayoutScore += weightedScore;
                    System.Diagnostics.Debug.WriteLine($"直方图分析暗示横排布局，横排布局得分 +{weightedScore} (基础分:{baseScore}, 权重:{histogramWeight:F2})");
                }
            }

            // 【新增】6. 基于内容分布的判断
            if (evidence.ContentDensityLeftHalf > 0 && evidence.ContentDensityRightHalf > 0)
            {
                double leftRightRatio = Math.Max(evidence.ContentDensityLeftHalf, evidence.ContentDensityRightHalf) /
                                       Math.Min(evidence.ContentDensityLeftHalf, evidence.ContentDensityRightHalf);

                if (leftRightRatio > 2.0)
                {
                    // 左右内容密度差异大，暗示竖排布局
                    verticalLayoutScore += 7;
                    System.Diagnostics.Debug.WriteLine($"左右内容密度差异大 (比例={leftRightRatio:F2})，竖排布局得分 +7");
                }
            }

            if (evidence.ContentDensityTopHalf > 0 && evidence.ContentDensityBottomHalf > 0)
            {
                double topBottomRatio = Math.Max(evidence.ContentDensityTopHalf, evidence.ContentDensityBottomHalf) /
                                       Math.Min(evidence.ContentDensityTopHalf, evidence.ContentDensityBottomHalf);

                if (topBottomRatio > 2.0)
                {
                    // 上下内容密度差异大，暗示横排布局
                    horizontalLayoutScore += 7;
                    System.Diagnostics.Debug.WriteLine($"上下内容密度差异大 (比例={topBottomRatio:F2})，横排布局得分 +7");
                }
            }

            // 7. 最终决策
            System.Diagnostics.Debug.WriteLine($"\n最终得分 - 横排: {horizontalLayoutScore}, 竖排: {verticalLayoutScore}");

            // 根据最终得分确定布局类型
            if (verticalLayoutScore > horizontalLayoutScore)
            {
                result.IsVerticalLayout = true;
                int scoreDiff = verticalLayoutScore - horizontalLayoutScore;
                result.LayoutConfidence = Math.Min(95, 50 + scoreDiff / 2);
                System.Diagnostics.Debug.WriteLine($"最终确定为竖排布局，置信度: {result.LayoutConfidence}%");
            }
            else
            {
                result.IsVerticalLayout = false;
                int scoreDiff = horizontalLayoutScore - verticalLayoutScore;
                result.LayoutConfidence = Math.Min(95, 50 + scoreDiff / 2);
                System.Diagnostics.Debug.WriteLine($"最终确定为横排布局，置信度: {result.LayoutConfidence}%");
            }

            // 3. 边缘变异特征 - 分析边缘的整齐程度
            System.Diagnostics.Debug.WriteLine("\n--- 3. 边缘变异特征得分 ---");

            int edgeScore = 0;

            // 左右边缘变异比较
            if (evidence.LeftEdgeVariance > 0 && evidence.RightEdgeVariance > 0)
            {
                double leftRightRatio = evidence.LeftEdgeVariance / evidence.RightEdgeVariance;
                System.Diagnostics.Debug.WriteLine($"左边缘变异: {evidence.LeftEdgeVariance:F2}, 右边缘变异: {evidence.RightEdgeVariance:F2}, 比率: {leftRightRatio:F2}");

                // 左边缘比右边缘更整齐 - 横排从左到右特征
                if (leftRightRatio < 0.7)
                {
                    int score = 5;
                    horizontalLayoutScore += score;
                    edgeScore += score;
                    System.Diagnostics.Debug.WriteLine($"左边缘明显比右边缘更整齐 => 横排得分 +{score}");
                }
                // 右边缘比左边缘更整齐 - 可能是横排从右到左
                else if (leftRightRatio > 1.5)
                {
                    int score = 3;
                    // 这里不增加竖排得分，因为右边缘整齐也可能是横排从右到左
                    edgeScore += score;
                    System.Diagnostics.Debug.WriteLine($"右边缘明显比左边缘更整齐 => 可能是从右到左横排");
                }
            }

            // 上下边缘变异比较
            if (evidence.TopEdgeVariance > 0 && evidence.BottomEdgeVariance > 0)
            {
                double topBottomRatio = evidence.TopEdgeVariance / evidence.BottomEdgeVariance;
                System.Diagnostics.Debug.WriteLine($"上边缘变异: {evidence.TopEdgeVariance:F2}, 下边缘变异: {evidence.BottomEdgeVariance:F2}, 比率: {topBottomRatio:F2}");

                // 上边缘比下边缘更整齐 - 竖排从上到下特征
                if (topBottomRatio < 0.7)
                {
                    int score = 5;
                    verticalLayoutScore += score;
                    edgeScore += score;
                    System.Diagnostics.Debug.WriteLine($"上边缘明显比下边缘更整齐 => 竖排得分 +{score}");
                }
            }

            // 4. 段落特征
            if (evidence.ParagraphCount >= 2)
            {
                System.Diagnostics.Debug.WriteLine($"\n--- 4. 段落特征得分 --- (段落数: {evidence.ParagraphCount})");

                if (evidence.IsSequentialParagraphs)
                {
                    // 连续段落通常是横排布局
                    int score = Math.Min(8, evidence.ParagraphCount * 2);
                    horizontalLayoutScore += score;
                    System.Diagnostics.Debug.WriteLine($"检测到连续段落布局 => 横排得分 +{score}");
                }
            }

            // 5. 最终布局决策
            System.Diagnostics.Debug.WriteLine("\n--- 5. 布局决策 ---");
            System.Diagnostics.Debug.WriteLine($"横排总得分: {horizontalLayoutScore}, 竖排总得分: {verticalLayoutScore}");

            // 确定布局类型
            if (verticalLayoutScore > horizontalLayoutScore)
            {
                result.IsVerticalLayout = true;

                // 计算置信度 - 基于得分差异和总得分
                int totalScore = verticalLayoutScore + horizontalLayoutScore;
                int scoreDiff = verticalLayoutScore - horizontalLayoutScore;

                result.LayoutConfidence = CalculateLayoutConfidence(verticalLayoutScore, horizontalLayoutScore, totalScore);

                // 【修改】根据左右方向证据初步设置水平方向
                // 这里只进行初步设置，后续在DetermineVerticalFlowDirection中会基于更多特征进行详细计算
                if (evidence.RightToLeftCount > evidence.LeftToRightCount)
                {
                    result.IsRightToLeft = true;
                    result.HorizontalDirection = TextFlowDirection.RightToLeft;
                    // 置信度将在DetermineVerticalFlowDirection中详细计算
                }
                else if (evidence.LeftToRightCount > evidence.RightToLeftCount)
                {
                    result.IsRightToLeft = false;
                    result.HorizontalDirection = TextFlowDirection.LeftToRight;
                    // 置信度将在DetermineVerticalFlowDirection中详细计算
                }
                else
                {
                    // 证据相等，使用内容分布特征
                    if (evidence.ContentDensityRightHalf > evidence.ContentDensityLeftHalf)
                    {
                        result.IsRightToLeft = true;
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                    }
                    else
                    {
                        result.IsRightToLeft = false;
                        result.HorizontalDirection = TextFlowDirection.LeftToRight;
                    }
                    // 置信度将在DetermineVerticalFlowDirection中详细计算
                }

                System.Diagnostics.Debug.WriteLine($"布局决策: 竖排 (置信度: {result.LayoutConfidence}%)，默认水平方向: 从右到左");
            }
            else
            {
                result.IsVerticalLayout = false;

                // 计算置信度
                int totalScore = verticalLayoutScore + horizontalLayoutScore;
                int scoreDiff = horizontalLayoutScore - verticalLayoutScore;

                result.LayoutConfidence = CalculateLayoutConfidence(horizontalLayoutScore, verticalLayoutScore, totalScore);

                System.Diagnostics.Debug.WriteLine($"布局决策: 横排 (置信度: {result.LayoutConfidence}%)");
            }

            // 应用特殊布局规则 - 处理极端情况
            ApplySpecialLayoutTypeRules(evidence, result);

            System.Diagnostics.Debug.WriteLine("============ 布局类型分析结束 ============\n");
        }

        /// <summary>
        /// 分析直方图布局证据
        /// </summary>
        /// <param name="evidence">方向证据</param>
        /// <returns>布局得分，正值表示竖排倾向，负值表示横排倾向，绝对值表示置信度</returns>
        private int AnalyzeHistogramLayoutEvidence(DirectionEvidence evidence)
        {
            // 如果直方图特征未设置，返回0
            if (evidence.HistogramColumnScore == 0 && evidence.HistogramRowScore == 0)
            {
                return 0;
            }

            System.Diagnostics.Debug.WriteLine("\n--- 直方图特征分析 ---");
            System.Diagnostics.Debug.WriteLine($"列结构得分: {evidence.HistogramColumnScore}, 行结构得分: {evidence.HistogramRowScore}");

            // 计算直方图布局得分
            int histogramLayoutScore = 0;

            // 【修改】考虑结构强度和方向一致性
            bool hasStrongVerticalStructure = evidence.VerticalStructureStrength > 0.8;
            bool hasStrongHorizontalStructure = evidence.HorizontalStructureStrength > 0.8;
            bool hasHighDirectionalConsistency = evidence.DirectionalConsistency > 0.8;

            // 【新增】检查形状特征与直方图特征是否一致
            bool isConsistentWithShape = false;

            // 如果垂直文本块数量多于水平文本块，且直方图显示列结构强于行结构，则一致
            if (evidence.VerticalTextCount > evidence.HorizontalTextCount &&
                evidence.HistogramColumnScore > evidence.HistogramRowScore)
            {
                isConsistentWithShape = true;
            }
            // 如果水平文本块数量多于垂直文本块，且直方图显示行结构强于列结构，则一致
            else if (evidence.HorizontalTextCount > evidence.VerticalTextCount &&
                     evidence.HistogramRowScore > evidence.HistogramColumnScore)
            {
                isConsistentWithShape = true;
            }
            // 如果形状特征与直方图特征明显矛盾，降低直方图特征的权重
            else if ((evidence.HorizontalTextCount > evidence.VerticalTextCount * 2 &&
                     evidence.HistogramColumnScore > evidence.HistogramRowScore * 2) ||
                     (evidence.VerticalTextCount > evidence.HorizontalTextCount * 2 &&
                     evidence.HistogramRowScore > evidence.HistogramColumnScore * 2))
            {
                // 严重矛盾，显著降低直方图特征权重
                double reductionFactor = 0.5;
                evidence.HistogramColumnScore = (int)(evidence.HistogramColumnScore * reductionFactor);
                evidence.HistogramRowScore = (int)(evidence.HistogramRowScore * reductionFactor);
                System.Diagnostics.Debug.WriteLine($"形状特征与直方图特征严重矛盾，降低直方图特征权重至{reductionFactor:F1}倍");
            }

            // 列结构明显强于行结构 - 竖排布局
            if (evidence.HistogramColumnScore > evidence.HistogramRowScore * 2 && evidence.HistogramColumnScore >= 10)
            {
                // 【修改】增强得分上限
                int baseScore = Math.Min(30, evidence.HistogramColumnScore);

                // 【新增】如果结构强度高且方向一致性高，进一步提升得分
                if (hasStrongVerticalStructure && hasHighDirectionalConsistency)
                {
                    baseScore = Math.Min(40, baseScore + 10);
                    System.Diagnostics.Debug.WriteLine($"列结构明显强于行结构，且结构强度和方向一致性高，直方图特征强烈暗示竖排布局，得分: +{baseScore}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"列结构明显强于行结构，直方图特征强烈暗示竖排布局，得分: +{baseScore}");
                }

                histogramLayoutScore = baseScore;
            }
            // 行结构明显强于列结构 - 横排布局
            else if (evidence.HistogramRowScore > evidence.HistogramColumnScore * 2 && evidence.HistogramRowScore >= 10)
            {
                // 【修改】增强得分上限
                int baseScore = Math.Min(30, evidence.HistogramRowScore);

                // 【新增】如果结构强度高且方向一致性高，进一步提升得分
                if (hasStrongHorizontalStructure && hasHighDirectionalConsistency)
                {
                    baseScore = Math.Min(40, baseScore + 10);
                    System.Diagnostics.Debug.WriteLine($"行结构明显强于列结构，且结构强度和方向一致性高，直方图特征强烈暗示横排布局，得分: -{baseScore}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"行结构明显强于列结构，直方图特征强烈暗示横排布局，得分: -{baseScore}");
                }

                histogramLayoutScore = -baseScore;
            }
            // 列结构略强于行结构
            else if (evidence.HistogramColumnScore > evidence.HistogramRowScore * 1.3)
            {
                // 【修改】增强得分计算
                int baseScore = Math.Min(20, evidence.HistogramColumnScore / 2);

                // 【新增】如果结构强度高，增加得分
                if (hasStrongVerticalStructure)
                {
                    baseScore = Math.Min(25, baseScore + 5);
                    System.Diagnostics.Debug.WriteLine($"列结构略强于行结构，且垂直结构强度高，直方图特征倾向于竖排布局，得分: +{baseScore}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"列结构略强于行结构，直方图特征倾向于竖排布局，得分: +{baseScore}");
                }

                histogramLayoutScore = baseScore;
            }
            // 行结构略强于列结构
            else if (evidence.HistogramRowScore > evidence.HistogramColumnScore * 1.3)
            {
                // 【修改】增强得分计算
                int baseScore = Math.Min(20, evidence.HistogramRowScore / 2);

                // 【新增】如果结构强度高，增加得分
                if (hasStrongHorizontalStructure)
                {
                    baseScore = Math.Min(25, baseScore + 5);
                    System.Diagnostics.Debug.WriteLine($"行结构略强于列结构，且水平结构强度高，直方图特征倾向于横排布局，得分: -{baseScore}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"行结构略强于列结构，直方图特征倾向于横排布局，得分: -{baseScore}");
                }

                histogramLayoutScore = -baseScore;
            }
            // 【修改】行列结构接近，但有一个方向的结构强度明显高
            else if (hasStrongVerticalStructure && evidence.VerticalStructureStrength > evidence.HorizontalStructureStrength * 1.5)
            {
                histogramLayoutScore = 15;
                System.Diagnostics.Debug.WriteLine($"行列结构接近，但垂直结构强度明显更高(强度={evidence.VerticalStructureStrength:F2})，倾向于竖排布局，得分: +15");
            }
            else if (hasStrongHorizontalStructure && evidence.HorizontalStructureStrength > evidence.VerticalStructureStrength * 1.5)
            {
                histogramLayoutScore = -15;
                System.Diagnostics.Debug.WriteLine($"行列结构接近，但水平结构强度明显更高(强度={evidence.HorizontalStructureStrength:F2})，倾向于横排布局，得分: -15");
            }
            // 行列结构接近，无法确定
            else
            {
                System.Diagnostics.Debug.WriteLine("行列结构接近，直方图特征无法确定布局类型");
            }

            // 【新增】如果形状特征与直方图特征矛盾，进一步调整得分
            if (!isConsistentWithShape && histogramLayoutScore != 0)
            {
                // 如果水平文本块明显占优，但直方图暗示竖排布局
                if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 3 && histogramLayoutScore > 0)
                {
                    int originalScore = histogramLayoutScore;
                    histogramLayoutScore = (int)(histogramLayoutScore * 0.4); // 大幅降低直方图特征权重
                    System.Diagnostics.Debug.WriteLine($"水平文本块明显占优，但直方图暗示竖排布局，降低直方图得分: {originalScore} -> {histogramLayoutScore}");
                }
                // 如果垂直文本块明显占优，但直方图暗示横排布局
                else if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 3 && histogramLayoutScore < 0)
                {
                    int originalScore = histogramLayoutScore;
                    histogramLayoutScore = (int)(histogramLayoutScore * 0.4); // 大幅降低直方图特征权重
                    System.Diagnostics.Debug.WriteLine($"垂直文本块明显占优，但直方图暗示横排布局，降低直方图得分: {originalScore} -> {histogramLayoutScore}");
                }
                // 其他矛盾情况，适度降低直方图特征权重
                else if ((evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.5 && histogramLayoutScore > 0) ||
                         (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5 && histogramLayoutScore < 0))
                {
                    int originalScore = histogramLayoutScore;
                    histogramLayoutScore = (int)(histogramLayoutScore * 0.7); // 适度降低直方图特征权重
                    System.Diagnostics.Debug.WriteLine($"形状特征与直方图特征存在矛盾，适度降低直方图得分: {originalScore} -> {histogramLayoutScore}");
                }
            }

            return histogramLayoutScore;
        }

        /// <summary>
        /// 基于一致性调整方向置信度，使用简化的基于证据的启发式规则
        /// </summary>
        /// <param name="result">方向检测结果</param>
        /// <param name="evidence">方向证据</param>
        private void AdjustConfidenceBasedOnConsistency(TextDirectionResult result, DirectionEvidence evidence)
        {
            System.Diagnostics.Debug.WriteLine("\n=== 一致性调整开始 ===");
            System.Diagnostics.Debug.WriteLine($"调整前 - 布局: {(result.IsVerticalLayout ? "竖排" : "横排")}, 置信度: {result.LayoutConfidence}");
            System.Diagnostics.Debug.WriteLine($"水平方向: {result.HorizontalDirection}, 置信度: {result.HorizontalConfidence}");
            System.Diagnostics.Debug.WriteLine($"垂直方向: {result.VerticalDirection}, 置信度: {result.VerticalConfidence}");

            // 【新增】先评估直方图特征的可靠性
            double histogramReliability = 1.0;
            if (evidence.VerticalPeakCount >= 2 || evidence.HorizontalPeakCount >= 2)
            {
                // 计算峰值形状质量和峰谷比的组合得分
                double verticalQualityScore = evidence.VerticalPeakShapeQuality * evidence.VerticalPeakValleyMeanRatio;
                double horizontalQualityScore = evidence.HorizontalPeakShapeQuality * evidence.HorizontalPeakValleyMeanRatio;

                // 根据结构强度确定可靠性
                if (result.IsVerticalLayout)
                {
                    // 竖排布局，关注垂直结构特征
                    histogramReliability = Math.Min(1.25, 0.75 + verticalQualityScore / 2);
                    System.Diagnostics.Debug.WriteLine($"直方图可靠性评估 - 竖排布局，峰形质量与峰谷比组合评分:{verticalQualityScore:F2}，可靠性系数:{histogramReliability:F2}");
                }
                else
                {
                    // 横排布局，关注水平结构特征
                    histogramReliability = Math.Min(1.25, 0.75 + horizontalQualityScore / 2);
                    System.Diagnostics.Debug.WriteLine($"直方图可靠性评估 - 横排布局，峰形质量与峰谷比组合评分:{horizontalQualityScore:F2}，可靠性系数:{histogramReliability:F2}");
                }
            }

            // 【新增】评估段落结构特征的可靠性
            double paragraphReliability = 1.0;
            if (evidence.ParagraphCount > 1)
            {
                if (evidence.IsSequentialParagraphs)
                {
                    // 连续段落布局更可靠
                    paragraphReliability = 1.15;
                    System.Diagnostics.Debug.WriteLine($"段落特征评估 - 检测到{evidence.ParagraphCount}个连续段落，可靠性系数:{paragraphReliability:F2}");
                }
                else
                {
                    // 非连续段落布局
                    paragraphReliability = 1.05;
                    System.Diagnostics.Debug.WriteLine($"段落特征评估 - 检测到{evidence.ParagraphCount}个非连续段落，可靠性系数:{paragraphReliability:F2}");
                }
            }

            // 检查布局类型和方向是否一致
            bool isConsistent = false;

            // 增强一致性检查，使用基本启发式规则
            if (!result.IsVerticalLayout)
            {
                // 横排布局一致性检查
                if (result.HorizontalDirection == TextFlowDirection.LeftToRight ||
                    result.HorizontalDirection == TextFlowDirection.RightToLeft)
                {
                    isConsistent = true;

                    // 计算一致性得分 - 基于证据强度
                    int consistencyScore = 0;

                    // 形状特征支持
                    if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.5)
                        consistencyScore += 2;

                    // 行列结构支持
                    if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns)
                        consistencyScore += 2;

                    // 【新增】直方图特征支持
                    if (evidence.HorizontalPeakCount >= 2 && evidence.HorizontalStructureStrength > 0.6)
                        consistencyScore += 2;

                    // 【新增】段落特征支持
                    if (evidence.ParagraphCount >= 2 && evidence.IsSequentialParagraphs)
                        consistencyScore += 1;

                    // 边界变异支持
                    if ((result.HorizontalDirection == TextFlowDirection.LeftToRight &&
                         evidence.LeftEdgeVariance < evidence.RightEdgeVariance) ||
                        (result.HorizontalDirection == TextFlowDirection.RightToLeft &&
                         evidence.RightEdgeVariance < evidence.LeftEdgeVariance))
                    {
                        consistencyScore += 1;
                    }

                    // 应用一致性增强，考虑直方图和段落特征可靠性
                    int consistencyBonus = (int)(Math.Min(15, consistencyScore * 3) * histogramReliability * paragraphReliability);
                    result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + consistencyBonus);
                    result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + consistencyBonus / 2);

                    System.Diagnostics.Debug.WriteLine($"横排布局与水平方向一致性检查: 得分={consistencyScore}, 增加置信度 +{consistencyBonus}");
                }
            }
            else // 竖排布局
            {
                // 竖排布局一致性检查 - 与横排类似但针对垂直方向
                if (result.VerticalDirection == TextFlowDirection.TopToBottom ||
                    result.VerticalDirection == TextFlowDirection.BottomToTop)
                {
                    isConsistent = true;

                    // 计算一致性得分
                    int consistencyScore = 0;

                    // 形状特征支持
                    if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.2)
                        consistencyScore += 3;

                    // 行列结构支持
                    if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows)
                        consistencyScore += 2;
                    else if (evidence.VerticalAlignedColumns >= 2)
                        consistencyScore += 3;

                    // 【新增】直方图特征支持
                    if (evidence.VerticalPeakCount >= 2)
                    {
                        // 峰值形状质量和峰谷比评分
                        int structureQualityScore = (int)((evidence.VerticalPeakShapeQuality +
                                                         (evidence.VerticalPeakValleyMeanRatio / 5)) * 5);
                        consistencyScore += Math.Min(3, structureQualityScore);

                        System.Diagnostics.Debug.WriteLine($"竖排布局 - 列结构质量评分:{structureQualityScore}，增加一致性得分:{Math.Min(3, structureQualityScore)}");
                    }

                    // 【新增】水平方向一致性支持
                    if ((result.IsRightToLeft && evidence.RightToLeftCount > evidence.LeftToRightCount) ||
                        (!result.IsRightToLeft && evidence.LeftToRightCount > evidence.RightToLeftCount))
                    {
                        consistencyScore += 2;
                        System.Diagnostics.Debug.WriteLine("水平方向与垂直布局一致，增加一致性得分:+2");
                    }

                    // 边界变异支持
                    if ((result.VerticalDirection == TextFlowDirection.TopToBottom &&
                         evidence.TopEdgeVariance < evidence.BottomEdgeVariance) ||
                        (result.VerticalDirection == TextFlowDirection.BottomToTop &&
                         evidence.BottomEdgeVariance < evidence.TopEdgeVariance))
                    {
                        consistencyScore += 1;
                    }

                    // 应用一致性增强，考虑直方图和段落特征可靠性
                    int consistencyBonus = (int)(Math.Min(20, consistencyScore * 3) * histogramReliability * paragraphReliability);
                    result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + consistencyBonus);
                    result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + consistencyBonus / 2);

                    System.Diagnostics.Debug.WriteLine($"竖排布局与垂直方向一致性检查: 得分={consistencyScore}, 增加置信度 +{consistencyBonus}");
                }
            }

            // 处理不一致情况
            if (!isConsistent)
            {
                // 检查不一致程度
                int primaryConfidence = result.IsVerticalLayout ?
                    result.VerticalConfidence : result.HorizontalConfidence;

                // 如果主方向置信度高但布局置信度低，调整布局以匹配主方向
                if (primaryConfidence > result.LayoutConfidence + 25)
                {
                    // 重新评估布局方向
                    if (result.HorizontalConfidence > result.VerticalConfidence + 20)
                    {
                        // 水平方向明显更可信
                        result.IsVerticalLayout = false;
                        result.LayoutConfidence = (result.LayoutConfidence + result.HorizontalConfidence) / 2;
                        System.Diagnostics.Debug.WriteLine($"方向不一致修正: 水平方向置信度明显更高，调整为横排布局");
                    }
                    else if (result.VerticalConfidence > result.HorizontalConfidence + 20)
                    {
                        // 垂直方向明显更可信
                        result.IsVerticalLayout = true;
                        result.LayoutConfidence = (result.LayoutConfidence + result.VerticalConfidence) / 2;
                        System.Diagnostics.Debug.WriteLine($"方向不一致修正: 垂直方向置信度明显更高，调整为竖排布局");
                    }
                }
            }

            // 【新增】输出一致性调整后的结果
            System.Diagnostics.Debug.WriteLine("\n=== 一致性调整后 ===");
            System.Diagnostics.Debug.WriteLine($"布局: {(result.IsVerticalLayout ? "竖排" : "横排")}, 置信度: {result.LayoutConfidence}");
            System.Diagnostics.Debug.WriteLine($"水平方向: {result.HorizontalDirection}, 置信度: {result.HorizontalConfidence}");
            System.Diagnostics.Debug.WriteLine($"垂直方向: {result.VerticalDirection}, 置信度: {result.VerticalConfidence}");

            // 【优化】特殊情况处理：竖排布局中的垂直方向检查 - 基于统计特征
            if (result.IsVerticalLayout)
            {
                // 1. 基于方向证据评估垂直方向
                if (evidence.TopToBottomCount > evidence.BottomToTopCount * 1.5)
                {
                    // 有明显的从上到下的证据
                    if (result.VerticalDirection != TextFlowDirection.TopToBottom)
                    {
                        // 修正垂直方向
                        result.VerticalDirection = TextFlowDirection.TopToBottom;
                        result.VerticalConfidence = Math.Max(70, result.VerticalConfidence);
                        System.Diagnostics.Debug.WriteLine($"竖排布局中检测到强烈的从上到下证据，修正垂直方向为TopToBottom");
                    }
                }
                else if (evidence.BottomToTopCount > evidence.TopToBottomCount * 1.5)
                {
                    // 有明显的从下到上的证据
                    if (result.VerticalDirection != TextFlowDirection.BottomToTop)
                    {
                        // 修正垂直方向
                        result.VerticalDirection = TextFlowDirection.BottomToTop;
                        result.VerticalConfidence = Math.Max(70, result.VerticalConfidence);
                        System.Diagnostics.Debug.WriteLine($"竖排布局中检测到强烈的从下到上证据，修正垂直方向为BottomToTop");
                    }
                }
                else
                {
                    // 【修改】结合直方图峰值特征进行判断
                    if (evidence.VerticalPeakCount >= 2 && evidence.VerticalPeakShapeQuality > 0.7)
                    {
                        // 使用峰值特征判断
                        // 在多列竖排文本中，峰形质量高通常意味着更规整的结构
                        // 这种情况下我们默认使用从上到下方向（传统阅读习惯）
                        result.VerticalDirection = TextFlowDirection.TopToBottom;
                        result.VerticalConfidence = Math.Max(75, result.VerticalConfidence);
                        System.Diagnostics.Debug.WriteLine($"垂直方向证据不明确，但列结构清晰(峰形质量:{evidence.VerticalPeakShapeQuality:F2})，使用TopToBottom");
                    }
                    else if (evidence.VerticalAlignedColumns >= 2)
                    {
                        // 有多列，默认使用从上到下（通用性更好）
                        result.VerticalDirection = TextFlowDirection.TopToBottom;
                        result.VerticalConfidence = Math.Max(60, result.VerticalConfidence);
                        System.Diagnostics.Debug.WriteLine($"垂直方向证据不明确，但检测到多列，默认使用TopToBottom");
                    }
                    else
                    {
                        // 无明显证据，使用通用默认方向
                        result.VerticalDirection = TextFlowDirection.TopToBottom;
                        result.VerticalConfidence = Math.Max(55, result.VerticalConfidence);
                        System.Diagnostics.Debug.WriteLine($"垂直方向证据不明确，使用默认TopToBottom方向");
                    }
                }

                // 3. 基于形状特征增强布局置信度
                if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5)
                {
                    // 竖排文本明显占优，增强竖排布局置信度
                    int verticalBonus = Math.Min(15, (evidence.VerticalTextCount - evidence.HorizontalTextCount) / 2);
                    result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + verticalBonus);
                    result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + verticalBonus / 2);

                    System.Diagnostics.Debug.WriteLine($"竖排文本明显占优({evidence.VerticalTextCount}:{evidence.HorizontalTextCount})，增强竖排布局置信度 +{verticalBonus}");
                }

                // 【新增】4. 使用直方图结构增强水平方向（列排序方向）的置信度
                if (evidence.VerticalPeakCount >= 2)
                {
                    // 多列结构，评估列特征质量
                    double structureQuality = (evidence.VerticalPeakShapeQuality + evidence.VerticalPeakValleyMeanRatio / 3) / 2;

                    // 如果列结构质量高且检测到中日韩文本，强化从右到左的判断
                    if (structureQuality > 0.7 && result.IsRightToLeft)
                    {
                        // 增强右到左的置信度
                        int rtlBonus = (int)(15 * structureQuality);
                        result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + rtlBonus);
                        System.Diagnostics.Debug.WriteLine($"竖排多列结构清晰(结构质量:{structureQuality:F2})，" +
                                                          $"增强从右到左水平方向置信度 +{rtlBonus}，最终:{result.HorizontalConfidence}%");
                    }
                    // 如果水平方向是从左到右，但置信度不高，考虑转换到从右到左
                    else if (!result.IsRightToLeft &&
                             result.HorizontalConfidence < 75 &&
                             structureQuality > 0.8 &&
                             evidence.VerticalPeakCount >= 3)
                    {
                        // 结构非常清晰的多列布局，优先考虑从右到左
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.IsRightToLeft = true;
                        result.HorizontalConfidence = 75;
                        System.Diagnostics.Debug.WriteLine($"列结构非常清晰且列数多(结构质量:{structureQuality:F2})," +
                                                          $"转换水平方向为从右到左，置信度:75%");
                    }
                }
            }

            System.Diagnostics.Debug.WriteLine($"调整后 - 布局: {(result.IsVerticalLayout ? "竖排" : "横排")}, 置信度: {result.LayoutConfidence}");
            System.Diagnostics.Debug.WriteLine($"水平方向: {result.HorizontalDirection}, 置信度: {result.HorizontalConfidence}");
            System.Diagnostics.Debug.WriteLine($"垂直方向: {result.VerticalDirection}, 置信度: {result.VerticalConfidence}");
            System.Diagnostics.Debug.WriteLine("=== 一致性调整结束 ===\n");
        }

        /// <summary>
        /// 计算布局置信度
        /// 基于得分差异比例和总分动态计算置信度，适用于任何得分分布
        /// </summary>
        /// <param name="winningScore">获胜方向的得分</param>
        /// <param name="opposingScore">相反方向的得分</param>
        /// <param name="totalScore">总得分</param>
        /// <returns>计算得出的置信度（55-95）</returns>
        private int CalculateLayoutConfidence(int winningScore, int opposingScore, int totalScore)
        {
            // 基础置信度基于分数差异比例
            // 使用相对差异比例而非绝对差异，使结果更通用
            double scoreDiff = winningScore - opposingScore;
            double diffRatio = scoreDiff / Math.Max(1, totalScore);

            // 映射到置信度范围：55-95
            // 下限保证最低可信度，上限避免过于武断
            int baseConfidence = 55 + (int)(diffRatio * 40);

            // 高总分提升置信度 - 更多证据支持时提升置信度
            // 使用平方根函数使增长合理
            int scoreBoost = (int)Math.Min(10, Math.Sqrt(totalScore));

            return Math.Min(95, baseConfidence + scoreBoost);
        }

        /// <summary>
        /// 应用特殊布局类型规则
        /// 处理各种特殊布局场景，增强系统的通用性
        /// </summary>
        /// <param name="evidence">收集的方向证据</param>
        /// <param name="result">文本方向结果，可能被调整</param>
        private void ApplySpecialLayoutTypeRules(DirectionEvidence evidence, TextDirectionResult result)
        {
            // 特殊布局规则用于处理一些极端或特殊情况
            // 这些规则基于具体的布局特征，适用范围广

            System.Diagnostics.Debug.WriteLine("\n--- 应用特殊布局规则 ---");

            // 1. 多列竖排文本布局
            // 垂直排列的文本列是非常明显的竖排特征
            if (evidence.VerticalAlignedColumns >= 2 &&
                evidence.VerticalTextCount > evidence.HorizontalTextCount &&
                evidence.HorizontalAlignedRows <= 1)
            {
                result.IsVerticalLayout = true;
                int oldConfidence = result.LayoutConfidence;
                // 提高置信度，但不超过95
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + 10);

                System.Diagnostics.Debug.WriteLine($"特殊规则1: 检测到多列竖排布局 => 置信度从{oldConfidence}提升到{result.LayoutConfidence}");
            }

            // 2. 多行横排文本布局
            // 水平排列的文本行是非常明显的横排特征
            if (evidence.HorizontalAlignedRows >= 2 &&
                evidence.HorizontalTextCount > evidence.VerticalTextCount &&
                evidence.VerticalAlignedColumns <= 1)
            {
                result.IsVerticalLayout = false;
                int oldConfidence = result.LayoutConfidence;
                // 提高置信度，但不超过95
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + 10);

                System.Diagnostics.Debug.WriteLine($"特殊规则2: 检测到多行横排布局 => 置信度从{oldConfidence}提升到{result.LayoutConfidence}");
            }

            // 5. 文本内容极少的情况
            if (evidence.HorizontalTextCount + evidence.VerticalTextCount < 5 &&
                result.LayoutConfidence > 60)
            {
                int oldConfidence = result.LayoutConfidence;
                // 文本内容很少时，降低我们的判断置信度
                result.LayoutConfidence = Math.Min(result.LayoutConfidence, 70);

                if (oldConfidence != result.LayoutConfidence)
                {
                    System.Diagnostics.Debug.WriteLine($"特殊规则5: 文本内容极少 => 降低置信度从{oldConfidence}到{result.LayoutConfidence}");
                }
            }
        }

        /// <summary>
        /// 将相似的值分组
        /// </summary>
        private List<List<double>> GroupSimilarValues(List<double> values, double threshold)
        {
            var groups = new List<List<double>>();

            foreach (var value in values)
            {
                bool addedToExisting = false;

                foreach (var group in groups)
                {
                    if (Math.Abs(group[0] - value) <= threshold)
                    {
                        group.Add(value);
                        addedToExisting = true;
                        break;
                    }
                }

                if (!addedToExisting)
                {
                    groups.Add(new List<double> { value });
                }
            }

            return groups;
        }

        /// <summary>
        /// 计算标准差
        /// </summary>
        /// <param name="values">数值列表</param>
        /// <param name="useSampleVariance">是否使用样本方差（除以n-1）</param>
        /// <returns>标准差</returns>
        private double CalculateStandardDeviation(List<double> values, bool useSampleVariance = false)
        {
            if (values.Count <= 1) return 0;

            double avg = values.Average();
            double sumOfSquaredDifferences = values.Sum(x => Math.Pow(x - avg, 2));

            // 使用样本方差(除以n-1)或总体方差(除以n)
            int divisor = useSampleVariance ? values.Count - 1 : values.Count;
            double variance = sumOfSquaredDifferences / divisor;

            return Math.Sqrt(variance);
        }

        /// <summary>
        /// 计算整数列表的标准差
        /// </summary>
        private double CalculateStandardDeviation(List<int> values, bool useSampleVariance = false)
        {
            if (values.Count <= 1) return 0;

            double avg = values.Average();
            double sumOfSquaredDifferences = values.Sum(x => Math.Pow(x - avg, 2));

            // 使用样本方差(除以n-1)或总体方差(除以n)
            int divisor = useSampleVariance ? values.Count - 1 : values.Count;
            double variance = sumOfSquaredDifferences / divisor;

            return Math.Sqrt(variance);
        }

        /// <summary>
        /// 提取统一的文本布局特征
        /// </summary>
        /// <param name="cells">文本单元格列表</param>
        /// <returns>文本布局特征对象</returns>
        private TextLayoutFeatures ExtractTextFeatures(List<TextCellInfo> cells)
        {
            var features = new TextLayoutFeatures();

            // 设置总文本块数
            features.TotalTextBlocks = cells.Count;

            // 过滤有效的文本块
            var validCells = cells.Where(c =>
                c.location.width >= 5 &&
                c.location.height >= 5 &&
                !string.IsNullOrWhiteSpace(c.words)).ToList();

            features.ValidTextBlocks = validCells.Count;

            if (validCells.Count < 2) return features;

            // 1. 首先提取页面边界特征，确保其他特征提取可以使用页面边界信息
            ExtractPageBoundaryFeatures(validCells, features);

            // 2. 提取直方图特征 - 这将提供关键的行列结构信息
            ExtractHistogramFeatures(validCells, features);

            // 3. 提取形状特征
            ExtractShapeFeatures(validCells, features);

            // 4. 提取边缘特征
            ExtractEdgeFeatures(validCells, features);

            // 5. 提取行列结构特征 - 现在可以利用直方图信息
            ExtractRowColumnFeatures(validCells, features);

            // 6. 提取对齐特征
            ExtractAlignmentFeatures(validCells, features);

            // 7. 提取段落特征
            ExtractParagraphFeatures(validCells, features);

            // 8. 提取内容分布特征
            ExtractContentDistributionFeatures(validCells, features);

            // 9. 提取文本方向特征
            ExtractTextDirectionFeatures(validCells, features);

            // 10. 计算特征比率和关系
            CalculateFeatureRelationships(features);

            // 11. 【新增】确定文档类型
            DetermineDocumentType(features);

            return features;
        }

        /// <summary>
        /// 【新增】根据文档特征确定文档类型，用于动态权重调整
        /// </summary>
        private void DetermineDocumentType(TextLayoutFeatures features)
        {
            // 默认为普通文档
            features.DocumentTypeCategory = TextFlowDocumentType.Normal;

            // 计算结构清晰度得分 (0-100)
            int structureScore = 0;

            // 1. 基于直方图特征评估
            // 峰值规律性和峰谷比是结构清晰度的重要指标
            double peakRegularityScore = Math.Max(features.VerticalPeakRegularity, features.HorizontalPeakRegularity) * 30;
            double peakValleyRatioScore = Math.Min(10, Math.Max(features.VerticalPeakValleyMeanRatio,
                                                               features.HorizontalPeakValleyMeanRatio)) * 3;

            // 2. 基于边缘对齐特征评估
            double alignmentScore = 0;
            if (features.LeftAlignedCount >= 3 || features.RightAlignedCount >= 3 ||
                features.TopAlignedCount >= 3 || features.BottomAlignedCount >= 3)
            {
                // 计算最大对齐数量
                int maxAlignedCount = Math.Max(
                    Math.Max(features.LeftAlignedCount, features.RightAlignedCount),
                    Math.Max(features.TopAlignedCount, features.BottomAlignedCount)
                );

                // 对齐得分 (0-25)
                alignmentScore = Math.Min(25, maxAlignedCount * 5);
            }

            // 3. 基于边缘变异性评估
            double edgeVarianceScore = 0;
            // 计算最小边缘变异性
            double minEdgeVariance = Math.Min(
                Math.Min(features.LeftEdgeVariance, features.RightEdgeVariance),
                Math.Min(features.TopEdgeVariance, features.BottomEdgeVariance)
            );

            // 边缘整齐度得分 (0-20)
            if (minEdgeVariance < 5)
            {
                edgeVarianceScore = 20;
            }
            else if (minEdgeVariance < 10)
            {
                edgeVarianceScore = 15;
            }
            else if (minEdgeVariance < 20)
            {
                edgeVarianceScore = 10;
            }
            else if (minEdgeVariance < 40)
            {
                edgeVarianceScore = 5;
            }

            // 4. 基于结构强度评估
            double structureStrengthScore = Math.Max(features.VerticalStructureStrength,
                                                   features.HorizontalStructureStrength) * 25;

            // 综合得分
            structureScore = (int)(peakRegularityScore + peakValleyRatioScore +
                                 alignmentScore + edgeVarianceScore + structureStrengthScore);

            System.Diagnostics.Debug.WriteLine($"文档结构评分: 峰值规律性={peakRegularityScore:F1}, " +
                                             $"峰谷比={peakValleyRatioScore:F1}, 对齐={alignmentScore:F1}, " +
                                             $"边缘整齐度={edgeVarianceScore:F1}, 结构强度={structureStrengthScore:F1}, " +
                                             $"总分={structureScore}");

            // 根据结构得分确定文档类型
            if (structureScore >= 80)
            {
                // 结构非常清晰的文档
                features.DocumentTypeCategory = TextFlowDocumentType.DefinitiveStructure;
                System.Diagnostics.Debug.WriteLine($"文档类型: 明确结构文档 (得分={structureScore})");
            }
            else if (structureScore >= 60)
            {
                // 结构较清晰的文档
                features.DocumentTypeCategory = TextFlowDocumentType.HighStructure;
                System.Diagnostics.Debug.WriteLine($"文档类型: 高结构化文档 (得分={structureScore})");
            }
            else if (structureScore < 30)
            {
                // 结构不清晰的文档
                features.DocumentTypeCategory = TextFlowDocumentType.LowQuality;
                System.Diagnostics.Debug.WriteLine($"文档类型: 低质量文档 (得分={structureScore})");
            }
            else
            {
                // 普通文档
                features.DocumentTypeCategory = TextFlowDocumentType.Normal;
                System.Diagnostics.Debug.WriteLine($"文档类型: 普通文本文档 (得分={structureScore})");
            }
        }

        /// <summary>
        /// 提取页面边界特征 - 这是其他特征提取的基础
        /// </summary>
        private void ExtractPageBoundaryFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 计算页面范围
            features.PageLeft = cells.Min(c => c.location.left);
            features.PageRight = cells.Max(c => c.location.left + c.location.width);
            features.PageTop = cells.Min(c => c.location.top);
            features.PageBottom = cells.Max(c => c.location.top + c.location.height);
            features.PageWidth = features.PageRight - features.PageLeft;
            features.PageHeight = features.PageBottom - features.PageTop;
            features.PageCenter_X = features.PageLeft + features.PageWidth / 2;
            features.PageCenter_Y = features.PageTop + features.PageHeight / 2;
        }

        /// <summary>
        /// 提取文本方向特征 - 特别关注竖排文本的水平方向判断
        /// </summary>
        /// <param name="cells">文本单元格列表</param>
        /// <param name="features">文本布局特征</param>
        private void ExtractTextDirectionFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            if (cells.Count < 3) return;

            // 检测是否为竖排文本
            bool isLikelyVerticalText = features.VerticalTextCount > features.HorizontalTextCount * 1.5;

            if (!isLikelyVerticalText) return;

            // 对于竖排文本，分析文本的排列方向

            // 1. 检查多列情况下的阅读顺序
            if (features.VerticalColumnCount >= 2)
            {
                // 按X坐标排序列
                var sortedColumns = features.VerticalColumns.OrderBy(col =>
                    col.Average(c => c.location.left)).ToList();

                // 检查首列和末列的文本密度
                if (sortedColumns.Count >= 2)
                {
                    var firstColumn = sortedColumns.First();
                    var lastColumn = sortedColumns.Last();

                    double firstColumnDensity = firstColumn.Sum(c => c.location.width * c.location.height);
                    double lastColumnDensity = lastColumn.Sum(c => c.location.width * c.location.height);

                    // 首列密度明显高于末列，可能是从左到右阅读
                    if (firstColumnDensity > lastColumnDensity * 1.3)
                    {
                        features.LeftToRightEvidence += 3;
                    }
                    // 末列密度明显高于首列，可能是从右到左阅读
                    else if (lastColumnDensity > firstColumnDensity * 1.3)
                    {
                        features.RightToLeftEvidence += 3;
                    }
                }

                // 2. 检查列间距的规律性
                var columnCenters = features.VerticalColumns.Select(col =>
                    col.Average(c => c.location.left + c.location.width / 2)).OrderBy(x => x).ToList();

                if (columnCenters.Count >= 3)
                {
                    // 计算列间距
                    List<double> columnGaps = new List<double>();
                    for (int i = 1; i < columnCenters.Count; i++)
                    {
                        columnGaps.Add(columnCenters[i] - columnCenters[i - 1]);
                    }

                    // 检查列间距是否从左到右递增（可能表示从右到左阅读）
                    bool increasingGaps = true;
                    for (int i = 1; i < columnGaps.Count; i++)
                    {
                        if (columnGaps[i] < columnGaps[i - 1] * 0.8)
                        {
                            increasingGaps = false;
                            break;
                        }
                    }

                    // 检查列间距是否从左到右递减（可能表示从左到右阅读）
                    bool decreasingGaps = true;
                    for (int i = 1; i < columnGaps.Count; i++)
                    {
                        if (columnGaps[i] > columnGaps[i - 1] * 0.8)
                        {
                            decreasingGaps = false;
                            break;
                        }
                    }

                    if (increasingGaps)
                    {
                        features.RightToLeftEvidence += 2;
                    }
                    else if (decreasingGaps)
                    {
                        features.LeftToRightEvidence += 2;
                    }
                }
            }

            // 3. 分析单个竖排文本块的内部特征
            var verticalTextBlocks = cells.Where(c => c.location.height > c.location.width * 2).ToList();
            if (verticalTextBlocks.Count >= 3)
            {
                // 检查字符分布 - 对于中日韩文字，可以通过分析Unicode范围判断
                int cjkCharCount = 0;
                int totalCharCount = 0;

                foreach (var cell in verticalTextBlocks)
                {
                    if (string.IsNullOrEmpty(cell.words)) continue;

                    foreach (char c in cell.words)
                    {
                        totalCharCount++;
                        // 检查是否为中日韩文字（粗略判断）
                        if (c >= 0x4E00 && c <= 0x9FFF) // 基本汉字范围
                        {
                            cjkCharCount++;
                        }
                    }
                }

                // 如果大部分是中日韩文字，增加从右到左的证据
                // 但这只是一个统计特征，不是绝对规则
                if (totalCharCount > 0)
                {
                    double cjkRatio = (double)cjkCharCount / totalCharCount;
                    if (cjkRatio > 0.7)
                    {
                        // 【修改】增加更强的从右到左证据，特别是当CJK字符比例很高时
                        int evidenceBoost = (int)(3 * Math.Min(1.0, cjkRatio));
                        features.RightToLeftEvidence += evidenceBoost;
                        System.Diagnostics.Debug.WriteLine($"检测到中日韩文字比例较高({cjkRatio:F2})，增加从右到左证据: +{evidenceBoost}");
                    }
                    else if (cjkRatio > 0.5)
                    {
                        // 中等比例的CJK字符，增加适中的证据
                        features.RightToLeftEvidence += 1;
                        System.Diagnostics.Debug.WriteLine($"检测到中等比例的中日韩文字({cjkRatio:F2})，增加从右到左证据: +1");
                    }
                }
            }

            // 4. 分析页面整体布局
            // 计算页面中心
            double pageWidth = cells.Max(c => c.location.left + c.location.width);
            double pageCenter = pageWidth / 2;

            // 计算文本块在页面左右两侧的分布
            int leftSideBlocks = 0;
            int rightSideBlocks = 0;

            foreach (var cell in verticalTextBlocks)
            {
                double cellCenter = cell.location.left + cell.location.width / 2;
                if (cellCenter < pageCenter)
                {
                    leftSideBlocks++;
                }
                else
                {
                    rightSideBlocks++;
                }
            }

            // 如果右侧文本块明显多于左侧，可能是从右到左阅读
            if (rightSideBlocks > leftSideBlocks * 1.5 && rightSideBlocks >= 3)
            {
                features.RightToLeftEvidence += 2;
            }
            // 如果左侧文本块明显多于右侧，可能是从左到右阅读
            else if (leftSideBlocks > rightSideBlocks * 1.5 && leftSideBlocks >= 3)
            {
                features.LeftToRightEvidence += 2;
            }
        }

        /// <summary>
        /// 计算特征之间的关系
        /// </summary>
        /// <param name="features">文本布局特征</param>
        private void CalculateFeatureRelationships(TextLayoutFeatures features)
        {
            // 计算边缘变异比率
            if (features.RightEdgeVariance > 0)
            {
                features.LeftRightEdgeVarianceRatio = features.LeftEdgeVariance / features.RightEdgeVariance;
            }

            if (features.BottomEdgeVariance > 0)
            {
                features.TopBottomEdgeVarianceRatio = features.TopEdgeVariance / features.BottomEdgeVariance;
            }

            // 计算行列比例
            if (features.VerticalColumnCount > 0)
            {
                features.RowColumnRatio = (double)features.HorizontalRowCount / features.VerticalColumnCount;
            }

            // 计算形状比例
            if (features.HorizontalTextCount > 0)
            {
                features.VerticalHorizontalRatio = (double)features.VerticalTextCount / features.HorizontalTextCount;
            }

            // 计算对齐比例
            double totalHorizontalAligned = features.LeftAlignedCount + features.RightAlignedCount;
            if (totalHorizontalAligned > 0)
            {
                features.LeftAlignmentRatio = features.LeftAlignedCount / totalHorizontalAligned;
            }

            double totalVerticalAligned = features.TopAlignedCount + features.BottomAlignedCount;
            if (totalVerticalAligned > 0)
            {
                features.TopAlignmentRatio = features.TopAlignedCount / totalVerticalAligned;
            }
        }

        /// <summary>
        /// 提取文本形状特征
        /// </summary>
        private void ExtractShapeFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 计算所有文本块的高宽比和宽高比
            foreach (var cell in cells)
            {
                double hwRatio = cell.location.height / (double)cell.location.width;
                double whRatio = cell.location.width / (double)cell.location.height;

                features.HeightWidthRatios.Add(hwRatio);
                features.WidthHeightRatios.Add(whRatio);
            }

            // 按高宽比排序，用于计算百分位数
            var sortedHWRatios = features.HeightWidthRatios.OrderBy(r => r).ToList();
            var sortedWHRatios = features.WidthHeightRatios.OrderBy(r => r).ToList();

            // 计算统计特征
            if (sortedHWRatios.Count > 0)
            {
                // 中位数
                int midIndex = sortedHWRatios.Count / 2;
                features.MedianHeightWidthRatio = sortedHWRatios[midIndex];
                features.MedianWidthHeightRatio = sortedWHRatios[midIndex];

                // 方差
                features.HeightWidthRatioVariance = CalculateVariance(sortedHWRatios);
                features.WidthHeightRatioVariance = CalculateVariance(sortedWHRatios);

                // 百分位数
                if (sortedHWRatios.Count >= 4)
                {
                    // 90百分位
                    int p90Index = (int)(sortedHWRatios.Count * 0.9);
                    features.VerticalRatio_P90 = sortedHWRatios[p90Index];
                    features.HorizontalRatio_P90 = sortedWHRatios[p90Index];

                    // 75百分位
                    int p75Index = (int)(sortedHWRatios.Count * 0.75);
                    features.VerticalRatio_P75 = sortedHWRatios[p75Index];
                    features.HorizontalRatio_P75 = sortedWHRatios[p75Index];

                    // 50百分位（中位数）
                    int p50Index = (int)(sortedHWRatios.Count * 0.5);
                    features.VerticalRatio_P50 = sortedHWRatios[p50Index];
                    features.HorizontalRatio_P50 = sortedWHRatios[p50Index];
                }
                else
                {
                    // 如果样本太少，使用合理的默认值
                    features.VerticalRatio_P90 = Math.Max(7.0, features.MedianHeightWidthRatio * 2);
                    features.VerticalRatio_P75 = Math.Max(5.0, features.MedianHeightWidthRatio * 1.5);
                    features.VerticalRatio_P50 = Math.Max(1.5, features.MedianHeightWidthRatio);

                    features.HorizontalRatio_P90 = Math.Max(7.0, features.MedianWidthHeightRatio * 2);
                    features.HorizontalRatio_P75 = Math.Max(5.0, features.MedianWidthHeightRatio * 1.5);
                    features.HorizontalRatio_P50 = Math.Max(1.5, features.MedianWidthHeightRatio);
                }
            }

            // 基于动态阈值计算文本形状计数
            double verticalThreshold = Math.Max(1.2, features.VerticalRatio_P50 * 0.7); // 动态调整竖排阈值
            double horizontalThreshold = Math.Max(1.2, features.HorizontalRatio_P50 * 0.7); // 动态调整横排阈值

            foreach (var cell in cells)
            {
                double hwRatio = cell.location.height / (double)cell.location.width;
                double whRatio = cell.location.width / (double)cell.location.height;

                if (hwRatio >= verticalThreshold)
                {
                    features.VerticalTextCount++;
                }
                else if (whRatio >= horizontalThreshold)
                {
                    features.HorizontalTextCount++;
                }
                else
                {
                    features.SquareTextCount++;
                }
            }

            // 计算文本块大小统计
            var widths = cells.Select(c => (double)c.location.width).OrderBy(w => w).ToList();
            var heights = cells.Select(c => (double)c.location.height).OrderBy(h => h).ToList();

            if (widths.Count > 0 && heights.Count > 0)
            {
                features.MedianWidth = widths[widths.Count / 2];
                features.MedianHeight = heights[heights.Count / 2];
                features.WidthVariance = CalculateVariance(widths);
                features.HeightVariance = CalculateVariance(heights);
            }
        }

        /// <summary>
        /// 提取边缘特征并计算边缘评分
        /// </summary>
        private void ExtractEdgeFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 提取边缘坐标
            foreach (var cell in cells)
            {
                features.LeftEdges.Add(cell.location.left);
                features.RightEdges.Add(cell.location.left + cell.location.width);
                features.TopEdges.Add(cell.location.top);
                features.BottomEdges.Add(cell.location.top + cell.location.height);
            }

            // 计算边缘变异
            if (features.LeftEdges.Count > 1)
            {
                // 【新增】使用更精确的边缘变异计算方法
                // 1. 先对边缘坐标进行聚类，识别出对齐的边缘
                double clusterThreshold = Math.Max(5.0, features.MedianWidth * 0.02); // 动态聚类阈值

                var leftEdgeClusters = GroupSimilarValues(features.LeftEdges, clusterThreshold);
                var rightEdgeClusters = GroupSimilarValues(features.RightEdges, clusterThreshold);
                var topEdgeClusters = GroupSimilarValues(features.TopEdges, clusterThreshold);
                var bottomEdgeClusters = GroupSimilarValues(features.BottomEdges, clusterThreshold);

                System.Diagnostics.Debug.WriteLine($"边缘聚类 - 左:{leftEdgeClusters.Count}, 右:{rightEdgeClusters.Count}, 上:{topEdgeClusters.Count}, 下:{bottomEdgeClusters.Count}");

                // 2. 计算主要边缘的变异性，而不是所有边缘
                // 找出最大的聚类（包含最多对齐边缘的聚类）
                var largestLeftCluster = leftEdgeClusters.OrderByDescending(c => c.Count).FirstOrDefault();
                var largestRightCluster = rightEdgeClusters.OrderByDescending(c => c.Count).FirstOrDefault();
                var largestTopCluster = topEdgeClusters.OrderByDescending(c => c.Count).FirstOrDefault();
                var largestBottomCluster = bottomEdgeClusters.OrderByDescending(c => c.Count).FirstOrDefault();

                // 计算主要边缘的变异性
                features.LeftEdgeVariance = largestLeftCluster != null && largestLeftCluster.Count >= features.LeftEdges.Count * 0.3 ?
                    CalculateVariance(largestLeftCluster) : CalculateVariance(features.LeftEdges);

                features.RightEdgeVariance = largestRightCluster != null && largestRightCluster.Count >= features.RightEdges.Count * 0.3 ?
                    CalculateVariance(largestRightCluster) : CalculateVariance(features.RightEdges);

                features.TopEdgeVariance = largestTopCluster != null && largestTopCluster.Count >= features.TopEdges.Count * 0.3 ?
                    CalculateVariance(largestTopCluster) : CalculateVariance(features.TopEdges);

                features.BottomEdgeVariance = largestBottomCluster != null && largestBottomCluster.Count >= features.BottomEdges.Count * 0.3 ?
                    CalculateVariance(largestBottomCluster) : CalculateVariance(features.BottomEdges);

                // 【新增】计算边缘对齐比例
                double leftAlignmentRatio = largestLeftCluster != null ?
                    (double)largestLeftCluster.Count / features.LeftEdges.Count : 0.0;

                double rightAlignmentRatio = largestRightCluster != null ?
                    (double)largestRightCluster.Count / features.RightEdges.Count : 0.0;

                double topAlignmentRatio = largestTopCluster != null ?
                    (double)largestTopCluster.Count / features.TopEdges.Count : 0.0;

                double bottomAlignmentRatio = largestBottomCluster != null ?
                    (double)largestBottomCluster.Count / features.BottomEdges.Count : 0.0;

                System.Diagnostics.Debug.WriteLine($"边缘对齐比例 - 左:{leftAlignmentRatio:F2}, 右:{rightAlignmentRatio:F2}, 上:{topAlignmentRatio:F2}, 下:{bottomAlignmentRatio:F2}");

                // 3. 边缘梯度分析 - 检测边缘是否有梯度变化（如阶梯状排列）
                bool leftEdgeHasGradient = DetectEdgeGradient(features.LeftEdges);
                bool rightEdgeHasGradient = DetectEdgeGradient(features.RightEdges);
                bool topEdgeHasGradient = DetectEdgeGradient(features.TopEdges);
                bool bottomEdgeHasGradient = DetectEdgeGradient(features.BottomEdges);

                System.Diagnostics.Debug.WriteLine($"边缘梯度 - 左:{leftEdgeHasGradient}, 右:{rightEdgeHasGradient}, 上:{topEdgeHasGradient}, 下:{bottomEdgeHasGradient}");

                // 计算边缘变异比例（避免除零）
                features.LeftRightEdgeVarianceRatio = features.RightEdgeVariance > 0 ?
                    features.LeftEdgeVariance / features.RightEdgeVariance : 1.0;

                features.TopBottomEdgeVarianceRatio = features.BottomEdgeVariance > 0 ?
                    features.TopEdgeVariance / features.BottomEdgeVariance : 1.0;

                // 【新增】预计算边缘整齐度评分 - 使用指数函数增强显著差异
                // 动态阈值 - 基于平均边缘变异
                double edgeVarianceThreshold = 0.7; // 默认阈值
                double avgEdgeVariance = (features.LeftEdgeVariance + features.RightEdgeVariance +
                                          features.TopEdgeVariance + features.BottomEdgeVariance) / 4;

                // 根据边缘变异调整阈值
                if (avgEdgeVariance < features.MedianWidth * 0.05) // 边缘变异很小，高质量文档
                {
                    edgeVarianceThreshold = 0.8; // 高质量文档阈值更严格
                }
                else if (avgEdgeVariance > features.MedianWidth * 0.2) // 边缘变异较大，低质量文档
                {
                    edgeVarianceThreshold = 0.6; // 低质量文档阈值更宽松
                }

                // 计算左边缘整齐度评分
                if (features.LeftEdgeVariance > 0 && features.RightEdgeVariance > 0)
                {
                    double varianceRatio = features.LeftEdgeVariance / Math.Max(features.LeftEdgeVariance, features.RightEdgeVariance);
                    double alignmentBonus = leftAlignmentRatio * 0.5; // 对齐比例奖励
                    double gradientPenalty = leftEdgeHasGradient ? 0.3 : 0.0; // 梯度惩罚

                    if (features.LeftEdgeVariance < features.RightEdgeVariance * edgeVarianceThreshold)
                    {
                        // 使用指数函数增强显著差异
                        features.LeftEdgeScore = 10 * Math.Pow(1 - varianceRatio, 1.5) + alignmentBonus * 10 - gradientPenalty * 10;
                        features.LeftEdgeScore = Math.Max(0, Math.Min(10, features.LeftEdgeScore)); // 限制在0-10范围内
                    }
                }

                // 计算右边缘整齐度评分
                if (features.RightEdgeVariance > 0 && features.LeftEdgeVariance > 0)
                {
                    double varianceRatio = features.RightEdgeVariance / Math.Max(features.LeftEdgeVariance, features.RightEdgeVariance);
                    double alignmentBonus = rightAlignmentRatio * 0.5; // 对齐比例奖励
                    double gradientPenalty = rightEdgeHasGradient ? 0.3 : 0.0; // 梯度惩罚

                    if (features.RightEdgeVariance < features.LeftEdgeVariance * edgeVarianceThreshold)
                    {
                        features.RightEdgeScore = 10 * Math.Pow(1 - varianceRatio, 1.5) + alignmentBonus * 10 - gradientPenalty * 10;
                        features.RightEdgeScore = Math.Max(0, Math.Min(10, features.RightEdgeScore));
                    }
                }

                // 计算顶部边缘整齐度评分
                if (features.TopEdgeVariance > 0 && features.BottomEdgeVariance > 0)
                {
                    double varianceRatio = features.TopEdgeVariance / Math.Max(features.TopEdgeVariance, features.BottomEdgeVariance);
                    double alignmentBonus = topAlignmentRatio * 0.5; // 对齐比例奖励
                    double gradientPenalty = topEdgeHasGradient ? 0.3 : 0.0; // 梯度惩罚

                    if (features.TopEdgeVariance < features.BottomEdgeVariance * edgeVarianceThreshold)
                    {
                        features.TopEdgeScore = 10 * Math.Pow(1 - varianceRatio, 1.5) + alignmentBonus * 10 - gradientPenalty * 10;
                        features.TopEdgeScore = Math.Max(0, Math.Min(10, features.TopEdgeScore));
                    }
                }

                // 计算底部边缘整齐度评分
                if (features.BottomEdgeVariance > 0 && features.TopEdgeVariance > 0)
                {
                    double varianceRatio = features.BottomEdgeVariance / Math.Max(features.TopEdgeVariance, features.BottomEdgeVariance);
                    double alignmentBonus = bottomAlignmentRatio * 0.5; // 对齐比例奖励
                    double gradientPenalty = bottomEdgeHasGradient ? 0.3 : 0.0; // 梯度惩罚

                    if (features.BottomEdgeVariance < features.TopEdgeVariance * edgeVarianceThreshold)
                    {
                        features.BottomEdgeScore = 10 * Math.Pow(1 - varianceRatio, 1.5) + alignmentBonus * 10 - gradientPenalty * 10;
                        features.BottomEdgeScore = Math.Max(0, Math.Min(10, features.BottomEdgeScore));
                    }
                }

                // 【新增】计算边缘特征可靠性
                // 基于最高得分和对比度计算可靠性
                double maxEdgeScore = Math.Max(
                    Math.Max(features.LeftEdgeScore, features.RightEdgeScore),
                    Math.Max(features.TopEdgeScore, features.BottomEdgeScore));

                // 计算对比度 - 水平边缘得分与垂直边缘得分的差异
                double horizontalEdgeScoreSum = features.LeftEdgeScore + features.RightEdgeScore;
                double verticalEdgeScoreSum = features.TopEdgeScore + features.BottomEdgeScore;
                double edgeScoreContrast = Math.Abs(horizontalEdgeScoreSum - verticalEdgeScoreSum) /
                                          (horizontalEdgeScoreSum + verticalEdgeScoreSum + 0.1);

                // 可靠性基于最高得分和对比度
                features.EdgeFeatureReliability = Math.Min(1.5, 0.5 + maxEdgeScore / 10.0 + edgeScoreContrast);

                System.Diagnostics.Debug.WriteLine($"边缘得分 - 左:{features.LeftEdgeScore:F1}, 右:{features.RightEdgeScore:F1}, " +
                                                $"上:{features.TopEdgeScore:F1}, 下:{features.BottomEdgeScore:F1}");
                System.Diagnostics.Debug.WriteLine($"边缘可靠性:{features.EdgeFeatureReliability:F2}");

                // 基于边缘评分添加方向证据
                if (features.LeftEdgeScore > 5.0 && features.LeftEdgeScore > features.RightEdgeScore * 1.5)
                {
                    features.LeftToRightEvidence += (int)(features.LeftEdgeScore / 2);
                    System.Diagnostics.Debug.WriteLine($"左边缘整齐度高({features.LeftEdgeScore:F1})，增加LeftToRight证据 +{(int)(features.LeftEdgeScore / 2)}");
                }
                else if (features.RightEdgeScore > 5.0 && features.RightEdgeScore > features.LeftEdgeScore * 1.5)
                {
                    features.RightToLeftEvidence += (int)(features.RightEdgeScore / 2);
                    System.Diagnostics.Debug.WriteLine($"右边缘整齐度高({features.RightEdgeScore:F1})，增加RightToLeft证据 +{(int)(features.RightEdgeScore / 2)}");
                }

                if (features.TopEdgeScore > 5.0 && features.TopEdgeScore > features.BottomEdgeScore * 1.5)
                {
                    features.TopToBottomEvidence += (int)(features.TopEdgeScore / 2);
                    System.Diagnostics.Debug.WriteLine($"顶部边缘整齐度高({features.TopEdgeScore:F1})，增加TopToBottom证据 +{(int)(features.TopEdgeScore / 2)}");
                }
                else if (features.BottomEdgeScore > 5.0 && features.BottomEdgeScore > features.TopEdgeScore * 1.5)
                {
                    features.BottomToTopEvidence += (int)(features.BottomEdgeScore / 2);
                    System.Diagnostics.Debug.WriteLine($"底部边缘整齐度高({features.BottomEdgeScore:F1})，增加BottomToTop证据 +{(int)(features.BottomEdgeScore / 2)}");
                }
            }

            // 【新增】设置边缘评分已计算标志
            features.IsEdgeScoresComputed = true;
        }

        /// <summary>
        /// 【新增】检测边缘是否存在梯度变化（如阶梯状排列）
        /// </summary>
        /// <param name="edges">边缘坐标列表</param>
        /// <returns>是否存在梯度</returns>
        private bool DetectEdgeGradient(List<double> edges)
        {
            if (edges.Count < 5) return false;

            // 排序边缘坐标
            var sortedEdges = new List<double>(edges);
            sortedEdges.Sort();

            // 计算相邻边缘的差值
            List<double> diffs = new List<double>();
            for (int i = 1; i < sortedEdges.Count; i++)
            {
                double diff = sortedEdges[i] - sortedEdges[i - 1];
                if (diff > 0.1) // 忽略非常小的差异
                {
                    diffs.Add(diff);
                }
            }

            if (diffs.Count < 4) return false;

            // 计算差值的平均值和标准差
            double avgDiff = diffs.Average();
            double stdDiff = CalculateStandardDeviation(diffs);

            // 计算变异系数 (CV)
            double cv = stdDiff / avgDiff;

            // 检查是否存在一致的梯度模式
            // 如果变异系数较小，说明差值较为一致，可能存在梯度
            return cv < 0.5 && diffs.Count >= sortedEdges.Count * 0.4;
        }

        /// <summary>
        /// 提取行列结构特征
        /// </summary>
        private void ExtractRowColumnFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 动态计算分组阈值
            double verticalGroupThreshold = this.groupingThresholdVertical;
            double horizontalGroupThreshold = this.groupingThresholdHorizontal;

            // 按水平位置分组形成列
            var columnGroups = cells
                .GroupBy(c => Math.Round(c.location.left / horizontalGroupThreshold) * horizontalGroupThreshold)
                .OrderBy(g => g.Key)
                .ToList();

            features.VerticalColumnCount = columnGroups.Count;

            // 存储列数据
            foreach (var column in columnGroups)
            {
                features.VerticalColumns.Add(column.ToList());
            }

            // 计算列间距
            if (columnGroups.Count >= 2)
            {
                for (int i = 1; i < columnGroups.Count; i++)
                {
                    double currentColCenter = columnGroups[i].Average(c => c.location.left + c.location.width / 2);
                    double prevColCenter = columnGroups[i - 1].Average(c => c.location.left + c.location.width / 2);
                    double gap = currentColCenter - prevColCenter;

                    if (gap > 0)
                    {
                        features.ColumnGaps.Add(gap);
                    }
                }

                if (features.ColumnGaps.Count > 0)
                {
                    features.MedianColumnGap = features.ColumnGaps.OrderBy(g => g).ElementAt(features.ColumnGaps.Count / 2);
                    features.ColumnGapVariance = CalculateVariance(features.ColumnGaps);

                    // 计算规律性（变异系数：标准差/平均值）
                    double columnGapMean = features.ColumnGaps.Average();
                    if (columnGapMean > 0)
                    {
                        features.ColumnGapRegularity = Math.Sqrt(features.ColumnGapVariance) / columnGapMean;
                    }

                    // 【新增】分析列间距的规律性
                    // 检查列间距是否从右到左递增
                    bool increasingGaps = true;
                    for (int i = 1; i < features.ColumnGaps.Count; i++)
                    {
                        if (features.ColumnGaps[i] < features.ColumnGaps[i - 1] * 0.8)
                        {
                            increasingGaps = false;
                            break;
                        }
                    }

                    // 检查列间距是否从右到左递减
                    bool decreasingGaps = true;
                    for (int i = 1; i < features.ColumnGaps.Count; i++)
                    {
                        if (features.ColumnGaps[i] > features.ColumnGaps[i - 1] * 1.2)
                        {
                            decreasingGaps = false;
                            break;
                        }
                    }

                    // 列间距从右到左递增，通常暗示从右到左阅读
                    if (increasingGaps && features.ColumnGaps.Count >= 2)
                    {
                        features.RightToLeftEvidence += 2;
                    }
                    // 列间距从右到左递减，通常暗示从左到右阅读
                    else if (decreasingGaps && features.ColumnGaps.Count >= 2)
                    {
                        features.LeftToRightEvidence += 2;
                    }
                }

                // 【新增】分析列宽的一致性
                List<double> columnWidths = new List<double>();
                foreach (var column in features.VerticalColumns)
                {
                    double minLeft = column.Min(c => c.location.left);
                    double maxRight = column.Max(c => c.location.left + c.location.width);
                    columnWidths.Add(maxRight - minLeft);
                }

                // 计算列宽的变异系数
                if (columnWidths.Count >= 2)
                {
                    double avgWidth = columnWidths.Average();
                    double stdDevWidth = Math.Sqrt(columnWidths.Sum(w => Math.Pow(w - avgWidth, 2)) / columnWidths.Count);
                    double columnWidthCV = stdDevWidth / avgWidth;

                    // 列宽一致性高，增加方向证据
                    if (columnWidthCV < 0.2) // 变异系数小于20%，列宽较一致
                    {
                        // 竖排文本的列宽一致性是重要的方向指标
                        if (features.VerticalTextCount > features.HorizontalTextCount)
                        {
                            // 对于竖排文本，列宽一致通常暗示更规范的排版
                            // 规范排版更可能遵循传统从右到左的阅读顺序
                            features.RightToLeftEvidence += 2;
                        }
                    }
                }
            }

            // 按垂直位置分组形成行
            var rowGroups = cells
                .GroupBy(c => Math.Round(c.location.top / verticalGroupThreshold) * verticalGroupThreshold)
                .OrderBy(g => g.Key)
                .ToList();

            features.HorizontalRowCount = rowGroups.Count;

            // 存储行数据
            foreach (var row in rowGroups)
            {
                features.HorizontalRows.Add(row.ToList());
            }

            // 计算行间距
            if (rowGroups.Count >= 2)
            {
                for (int i = 1; i < rowGroups.Count; i++)
                {
                    double currentRowCenter = rowGroups[i].Average(c => c.location.top + c.location.height / 2);
                    double prevRowCenter = rowGroups[i - 1].Average(c => c.location.top + c.location.height / 2);
                    double gap = currentRowCenter - prevRowCenter;

                    if (gap > 0)
                    {
                        features.RowGaps.Add(gap);
                    }
                }

                if (features.RowGaps.Count > 0)
                {
                    features.MedianRowGap = features.RowGaps.OrderBy(g => g).ElementAt(features.RowGaps.Count / 2);
                    features.RowGapVariance = CalculateVariance(features.RowGaps);

                    // 计算规律性（变异系数）
                    double rowGapMean = features.RowGaps.Average();
                    if (rowGapMean > 0)
                    {
                        features.RowGapRegularity = Math.Sqrt(features.RowGapVariance) / rowGapMean;
                    }
                }
            }
        }

        /// <summary>
        /// 提取对齐特征
        /// </summary>
        private void ExtractAlignmentFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 检查是否已计算过对齐特征，避免重复计算
            if (features.IsAlignmentScoresComputed)
            {
                System.Diagnostics.Debug.WriteLine("对齐特征已计算，跳过重复计算");
                return;
            }

            // 动态确定对齐阈值
            double alignmentThreshold = Math.Min(10.0, features.MedianWidth * 0.1); // 默认10像素或宽度的10%

            // 统计左对齐
            var leftGroups = GroupSimilarValues(features.LeftEdges, alignmentThreshold);
            features.LeftAlignedCount = leftGroups.Count > 0 ? leftGroups.Max(g => g.Count) : 0;

            // 统计右对齐
            var rightGroups = GroupSimilarValues(features.RightEdges, alignmentThreshold);
            features.RightAlignedCount = rightGroups.Count > 0 ? rightGroups.Max(g => g.Count) : 0;

            // 统计顶部对齐
            var topGroups = GroupSimilarValues(features.TopEdges, alignmentThreshold);
            features.TopAlignedCount = topGroups.Count > 0 ? topGroups.Max(g => g.Count) : 0;

            // 统计底部对齐
            var bottomGroups = GroupSimilarValues(features.BottomEdges, alignmentThreshold);
            features.BottomAlignedCount = bottomGroups.Count > 0 ? bottomGroups.Max(g => g.Count) : 0;

            // 【新增】增强边缘对齐特征分析
            // 分析左右边缘的对齐精确度
            List<double> leftEdgeDeviations = new List<double>();
            List<double> rightEdgeDeviations = new List<double>();
            List<double> topEdgeDeviations = new List<double>();
            List<double> bottomEdgeDeviations = new List<double>();

            // 计算每个文本块与其最近邻居的边缘对齐偏差
            for (int i = 0; i < cells.Count; i++)
            {
                for (int j = i + 1; j < cells.Count; j++)
                {
                    // 计算左边缘对齐偏差
                    double leftDeviation = Math.Abs(cells[i].location.left - cells[j].location.left);
                    if (leftDeviation < features.MedianWidth * 0.3) // 如果偏差小于宽度的30%，认为有对齐倾向
                    {
                        leftEdgeDeviations.Add(leftDeviation);
                    }

                    // 计算右边缘对齐偏差
                    double rightDeviation = Math.Abs(
                        (cells[i].location.left + cells[i].location.width) -
                        (cells[j].location.left + cells[j].location.width));
                    if (rightDeviation < features.MedianWidth * 0.3)
                    {
                        rightEdgeDeviations.Add(rightDeviation);
                    }

                    // 计算顶部边缘对齐偏差
                    double topDeviation = Math.Abs(cells[i].location.top - cells[j].location.top);
                    if (topDeviation < features.MedianHeight * 0.3)
                    {
                        topEdgeDeviations.Add(topDeviation);
                    }

                    // 计算底部边缘对齐偏差
                    double bottomDeviation = Math.Abs(
                        (cells[i].location.top + cells[i].location.height) -
                        (cells[j].location.top + cells[j].location.height));
                    if (bottomDeviation < features.MedianHeight * 0.3)
                    {
                        bottomEdgeDeviations.Add(bottomDeviation);
                    }
                }
            }

            // 分析边缘对齐的精确度
            if (leftEdgeDeviations.Count > 0)
            {
                features.LeftEdgeAlignmentPrecision = 1.0 - (leftEdgeDeviations.Average() / features.MedianWidth);
            }
            if (rightEdgeDeviations.Count > 0)
            {
                features.RightEdgeAlignmentPrecision = 1.0 - (rightEdgeDeviations.Average() / features.MedianWidth);
            }
            if (topEdgeDeviations.Count > 0)
            {
                features.TopEdgeAlignmentPrecision = 1.0 - (topEdgeDeviations.Average() / features.MedianHeight);
            }
            if (bottomEdgeDeviations.Count > 0)
            {
                features.BottomEdgeAlignmentPrecision = 1.0 - (bottomEdgeDeviations.Average() / features.MedianHeight);
            }

            // 边缘对齐精确度比较 - 用于方向判断
            if (features.LeftEdgeAlignmentPrecision > 0 && features.RightEdgeAlignmentPrecision > 0)
            {
                if (features.LeftEdgeAlignmentPrecision > features.RightEdgeAlignmentPrecision * 1.2)
                {
                    features.LeftToRightEvidence += 2;
                }
                else if (features.RightEdgeAlignmentPrecision > features.LeftEdgeAlignmentPrecision * 1.2)
                {
                    features.RightToLeftEvidence += 2;
                }
            }

            if (features.TopEdgeAlignmentPrecision > 0 && features.BottomEdgeAlignmentPrecision > 0)
            {
                if (features.TopEdgeAlignmentPrecision > features.BottomEdgeAlignmentPrecision * 1.2)
                {
                    features.TopToBottomEvidence += 2;
                }
                else if (features.BottomEdgeAlignmentPrecision > features.TopEdgeAlignmentPrecision * 1.2)
                {
                    features.BottomToTopEvidence += 2;
                }
            }

            // 【新增】预计算边缘对齐评分 - 使用非线性计分
            // 左对齐评分
            if (features.LeftAlignedCount >= 3)
            {
                // 前3个对齐块每个1.5分，之后每个2.5分
                features.LeftAlignmentScore = Math.Min(10, 3 * 1.5 + Math.Max(0, features.LeftAlignedCount - 3) * 2.5);
            }
            else if (features.LeftAlignedCount > 0)
            {
                features.LeftAlignmentScore = features.LeftAlignedCount * 1.5;
            }

            // 右对齐评分
            if (features.RightAlignedCount >= 3)
            {
                // 前3个对齐块每个1.5分，之后每个2.5分
                features.RightAlignmentScore = Math.Min(10, 3 * 1.5 + Math.Max(0, features.RightAlignedCount - 3) * 2.5);
            }
            else if (features.RightAlignedCount > 0)
            {
                features.RightAlignmentScore = features.RightAlignedCount * 1.5;
            }

            // 顶部对齐评分
            if (features.TopAlignedCount >= 3)
            {
                features.TopAlignmentScore = Math.Min(10, 3 * 1.5 + Math.Max(0, features.TopAlignedCount - 3) * 2.5);
            }
            else if (features.TopAlignedCount > 0)
            {
                features.TopAlignmentScore = features.TopAlignedCount * 1.5;
            }

            // 底部对齐评分
            if (features.BottomAlignedCount >= 3)
            {
                features.BottomAlignmentScore = Math.Min(10, 3 * 1.5 + Math.Max(0, features.BottomAlignedCount - 3) * 2.5);
            }
            else if (features.BottomAlignedCount > 0)
            {
                features.BottomAlignmentScore = features.BottomAlignedCount * 1.5;
            }

            // 【新增】计算对齐特征可靠性
            CalculateAlignmentFeatureReliability(features);

            System.Diagnostics.Debug.WriteLine($"对齐评分 - 左:{features.LeftAlignmentScore:F1}, 右:{features.RightAlignmentScore:F1}, " +
                                             $"上:{features.TopAlignmentScore:F1}, 下:{features.BottomAlignmentScore:F1}");
            System.Diagnostics.Debug.WriteLine($"对齐精确度 - 左:{features.LeftEdgeAlignmentPrecision:F2}, 右:{features.RightEdgeAlignmentPrecision:F2}, " +
                                             $"上:{features.TopEdgeAlignmentPrecision:F2}, 下:{features.BottomEdgeAlignmentPrecision:F2}");
            System.Diagnostics.Debug.WriteLine($"对齐可靠性:{features.AlignmentFeatureReliability:F2}");

            // 标记对齐特征已计算
            features.IsAlignmentScoresComputed = true;
        }

        /// <summary>
        /// 计算对齐特征可靠性
        /// </summary>
        private void CalculateAlignmentFeatureReliability(TextLayoutFeatures features)
        {
            // 默认可靠性中等
            double reliability = 0.5;

            // 1. 基于对齐数量的可靠性评估
            int maxHorizontalAligned = Math.Max(features.LeftAlignedCount, features.RightAlignedCount);
            int maxVerticalAligned = Math.Max(features.TopAlignedCount, features.BottomAlignedCount);

            // 对齐数量越多，可靠性越高
            if (maxHorizontalAligned >= 5 || maxVerticalAligned >= 5)
            {
                reliability += 0.3; // 大量对齐，明显的结构，高可靠性
            }
            else if (maxHorizontalAligned >= 3 || maxVerticalAligned >= 3)
            {
                reliability += 0.2; // 中等对齐，一般结构，中等可靠性
            }
            else if (maxHorizontalAligned <= 1 && maxVerticalAligned <= 1)
            {
                reliability -= 0.1; // 几乎无对齐，低可靠性
            }

            // 2. 基于对齐精确度的可靠性评估
            double bestAlignmentPrecision = Math.Max(
                Math.Max(features.LeftEdgeAlignmentPrecision, features.RightEdgeAlignmentPrecision),
                Math.Max(features.TopEdgeAlignmentPrecision, features.BottomEdgeAlignmentPrecision)
            );

            if (bestAlignmentPrecision > 0.8)
            {
                reliability += 0.3; // 高精确度对齐，高可靠性
            }
            else if (bestAlignmentPrecision > 0.6)
            {
                reliability += 0.2; // 中等精确度对齐，中等可靠性
            }
            else if (bestAlignmentPrecision < 0.4)
            {
                reliability -= 0.1; // 低精确度对齐，低可靠性
            }

            // 3. 水平与垂直对齐对比的可靠性评估
            double horizontalAlignmentScore = Math.Max(features.LeftAlignmentScore, features.RightAlignmentScore);
            double verticalAlignmentScore = Math.Max(features.TopAlignmentScore, features.BottomAlignmentScore);

            // 如果水平与垂直对齐有明显差异，增加可靠性（更清晰的方向性）
            if (Math.Abs(horizontalAlignmentScore - verticalAlignmentScore) > 3.0)
            {
                reliability += 0.2;
            }

            // 确保可靠性在合理范围内
            features.AlignmentFeatureReliability = Math.Min(1.5, Math.Max(0.2, reliability));
        }

        /// <summary>
        /// 提取段落特征
        /// </summary>
        private void ExtractParagraphFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            if (cells.Count < 3)
            {
                features.ParagraphCount = 1;
                return;
            }

            // 按垂直位置排序
            var sortedCells = cells.OrderBy(c => c.location.top).ToList();

            // 计算段落间距阈值 - 动态计算
            features.ParagraphGapThreshold = features.MedianHeight * 1.5; // 默认为中位数高度的1.5倍

            // 统计段落数量
            int paragraphCount = 1;
            bool hasConsistentParagraphGaps = true; // 段落间距是否一致
            double previousGap = 0;

            for (int i = 1; i < sortedCells.Count; i++)
            {
                double gap = sortedCells[i].location.top - (sortedCells[i - 1].location.top + sortedCells[i - 1].location.height);

                if (gap > features.ParagraphGapThreshold)
                {
                    paragraphCount++;
                    features.ParagraphGaps.Add(gap);

                    // 检查段落间距是否一致
                    if (previousGap > 0)
                    {
                        // 如果段落间距差异超过30%，认为不一致
                        if (Math.Abs(gap - previousGap) / previousGap > 0.3)
                        {
                            hasConsistentParagraphGaps = false;
                        }
                    }
                    previousGap = gap;
                }
            }

            features.ParagraphCount = paragraphCount;

            // 判断是否为连续段落布局
            // 连续段落布局的条件：
            // 1. 有多个段落
            // 2. 段落间距相对一致
            // 3. 段落数量大于等于3，更可靠
            if (paragraphCount >= 2 && hasConsistentParagraphGaps)
            {
                features.IsSequentialParagraphs = true;
            }
        }

        /// <summary>
        /// 提取内容分布特征
        /// </summary>
        private void ExtractContentDistributionFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 页面范围已在ExtractPageBoundaryFeatures中计算
            // 不需要重新计算PageLeft, PageRight, PageCenter_X等值

            // 计算内容面积和位置
            double totalArea = 0;
            double leftHalfArea = 0;
            double rightHalfArea = 0;
            double topHalfArea = 0;
            double bottomHalfArea = 0;

            foreach (var cell in cells)
            {
                double area = cell.location.width * cell.location.height;
                totalArea += area;

                // 计算中心点
                double centerX = cell.location.left + cell.location.width / 2;
                double centerY = cell.location.top + cell.location.height / 2;

                // 按位置分配面积
                if (centerX < features.PageCenter_X)
                {
                    leftHalfArea += area;
                }
                else
                {
                    rightHalfArea += area;
                }

                if (centerY < features.PageCenter_Y)
                {
                    topHalfArea += area;
                }
                else
                {
                    bottomHalfArea += area;
                }
            }

            // 计算内容密度比例
            if (totalArea > 0)
            {
                features.ContentDensityLeftHalf = leftHalfArea / totalArea;
                features.ContentDensityRightHalf = rightHalfArea / totalArea;
                features.ContentDensityTopHalf = topHalfArea / totalArea;
                features.ContentDensityBottomHalf = bottomHalfArea / totalArea;

                // 【新增】计算内容分布得分，避免后续重复计算
                features.LeftContentScore = features.ContentDensityLeftHalf * 10;  // 转换为0-10分
                features.RightContentScore = features.ContentDensityRightHalf * 10;
                features.TopContentScore = features.ContentDensityTopHalf * 10;
                features.BottomContentScore = features.ContentDensityBottomHalf * 10;

                // 【新增】计算左右和上下内容比率，方便后续分析
                if (features.ContentDensityRightHalf > 0)
                    features.LeftRightContentRatio = features.ContentDensityLeftHalf / features.ContentDensityRightHalf;
                if (features.ContentDensityBottomHalf > 0)
                    features.TopBottomContentRatio = features.ContentDensityTopHalf / features.ContentDensityBottomHalf;

                // 【新增】计算内容可靠性 - 基于内容分布不均匀程度
                double horizontalImbalance = Math.Abs(features.ContentDensityLeftHalf - features.ContentDensityRightHalf);
                double verticalImbalance = Math.Abs(features.ContentDensityTopHalf - features.ContentDensityBottomHalf);
                features.ContentFeatureReliability = Math.Min(1.2, 0.8 + Math.Max(horizontalImbalance, verticalImbalance));

                System.Diagnostics.Debug.WriteLine($"内容分布 - 左:{features.ContentDensityLeftHalf:P1}, 右:{features.ContentDensityRightHalf:P1}, " +
                                                $"上:{features.ContentDensityTopHalf:P1}, 下:{features.ContentDensityBottomHalf:P1}");
                System.Diagnostics.Debug.WriteLine($"内容比率 - 左右比:{features.LeftRightContentRatio:F2}, 上下比:{features.TopBottomContentRatio:F2}");
                System.Diagnostics.Debug.WriteLine($"内容可靠性:{features.ContentFeatureReliability:F2}");
            }

            // 【新增】文本块分布模式分析
            // 将页面水平方向划分为多个区域，分析文本块分布
            int horizontalSections = 5; // 水平方向划分为5个区域
            int[] sectionCounts = new int[horizontalSections];
            double sectionWidth = features.PageWidth / horizontalSections;

            foreach (var cell in cells)
            {
                double centerX = cell.location.left + cell.location.width / 2;
                int sectionIndex = Math.Min(horizontalSections - 1,
                    (int)((centerX - features.PageLeft) / sectionWidth));
                sectionCounts[sectionIndex]++;
            }

            // 分析分布趋势
            bool increasingTrend = true; // 从左到右递增
            bool decreasingTrend = true; // 从左到右递减

            for (int i = 1; i < horizontalSections; i++)
            {
                if (sectionCounts[i] < sectionCounts[i - 1])
                {
                    increasingTrend = false;
                }
                if (sectionCounts[i] > sectionCounts[i - 1])
                {
                    decreasingTrend = false;
                }
            }

            // 根据分布趋势添加方向证据
            if (increasingTrend && sectionCounts[horizontalSections - 1] > sectionCounts[0] * 1.5)
            {
                features.LeftToRightEvidence += 2;
                // 【新增】保存分布模式信息
                features.TextDistributionPattern = "LeftToRightIncreasing";
            }
            else if (decreasingTrend && sectionCounts[0] > sectionCounts[horizontalSections - 1] * 1.5)
            {
                features.RightToLeftEvidence += 2;
                // 【新增】保存分布模式信息
                features.TextDistributionPattern = "RightToLeftDecreasing";
            }
            else
            {
                // 【新增】保存分布模式信息
                features.TextDistributionPattern = "Mixed";
            }

            // 【新增】设置缓存标志，标记内容评分已计算
            features.IsContentScoresComputed = true;
        }

        /// <summary>
        /// 计算方差
        /// </summary>
        private double CalculateVariance(List<double> values)
        {
            if (values.Count <= 1) return 0;

            double avg = values.Average();
            double sumOfSquaredDifferences = values.Sum(x => (x - avg) * (x - avg));
            return sumOfSquaredDifferences / values.Count;
        }

        /// <summary>
        /// 提取直方图特征 - 分析文本块在水平和垂直方向的分布
        /// </summary>
        private void ExtractHistogramFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 检查特征是否已计算，避免重复计算
            if (features.IsHistogramFeaturesComputed)
            {
                System.Diagnostics.Debug.WriteLine("直方图特征已计算，跳过重复计算");
                return;
            }

            if (cells.Count < 3) return; // 至少需要3个文本块才能进行有效的直方图分析

            // 计算页面范围
            double pageWidth = features.PageRight - features.PageLeft;
            double pageHeight = features.PageBottom - features.PageTop;

            // 1. 确定合适的bin大小
            // 使用文本块的平均尺寸作为参考，以捕捉行列结构
            double avgWidth = cells.Average(c => c.location.width);
            double avgHeight = cells.Average(c => c.location.height);

            // 【修改】计算中位数宽高，避免极端值影响
            double medianWidth = features.MedianWidth > 0 ? features.MedianWidth : avgWidth;
            double medianHeight = features.MedianHeight > 0 ? features.MedianHeight : avgHeight;

            // 【修改】输出文本块尺寸信息
            System.Diagnostics.Debug.WriteLine($"文本块尺寸: 中位数宽度={medianWidth:F1}, 中位数高度={medianHeight:F1}");

            // 【修改】计算文本密度 - 文本块数量与页面面积的比值
            double textDensity = cells.Count / (pageWidth * pageHeight) * 10000; // 缩放因子，避免数值过小
            System.Diagnostics.Debug.WriteLine($"文本密度: {textDensity:F1}");

            // 【新增】考虑文本块形状特征分布
            double horizontalTextRatio = 0.5;
            if (features.ValidTextBlocks > 0)
            {
                horizontalTextRatio = (double)features.HorizontalTextCount / features.ValidTextBlocks;
                System.Diagnostics.Debug.WriteLine($"形状特征分布: 横向文本={features.HorizontalTextCount}个, 竖向文本={features.VerticalTextCount}个, 横向比例={horizontalTextRatio:F2}");
            }

            // 【新增】基于形状特征的自适应因子
            // 当横向文本块占绝对优势时，使用更小的水平bin以更好地捕捉行结构
            double horizontalBinFactor = 1.0;
            double verticalBinFactor = 1.0;

            // 【修改】更强的形状特征适应性
            if (features.HorizontalTextCount > features.VerticalTextCount * 2)
            {
                // 横向文本块占优势
                horizontalBinFactor = 0.7; // 减小水平bin大小，更好地捕捉行结构
                verticalBinFactor = 1.0;   // 保持垂直bin大小
                System.Diagnostics.Debug.WriteLine("横向文本占优势，减小水平bin大小");
            }
            else if (features.VerticalTextCount > features.HorizontalTextCount * 2)
            {
                // 竖向文本块占优势
                horizontalBinFactor = 1.0; // 保持水平bin大小
                verticalBinFactor = 0.7;   // 减小垂直bin大小，更好地捕捉列结构
                System.Diagnostics.Debug.WriteLine("竖向文本占优势，减小垂直bin大小");
            }
            else
            {
                // 形状特征分布较均衡，微调比例
                double shapeBias = (horizontalTextRatio - 0.5) * 0.4; // -0.2到0.2的范围
                horizontalBinFactor = Math.Max(0.8, Math.Min(1.2, 1.0 - shapeBias));
                verticalBinFactor = Math.Max(0.8, Math.Min(1.2, 1.0 + shapeBias));
                System.Diagnostics.Debug.WriteLine($"形状特征分布均衡，微调bin大小，水平因子={horizontalBinFactor:F2}，垂直因子={verticalBinFactor:F2}");
            }

            // 【修改】基于页面尺寸比例的调整
            double pageAspectRatio = pageWidth / pageHeight;
            System.Diagnostics.Debug.WriteLine($"页面尺寸: 宽={pageWidth:F1}, 高={pageHeight:F1}, 宽高比={pageAspectRatio:F2}");

            // 【修改】更合理的基准bin大小计算
            // 水平方向bin大小应该基于行间距，垂直方向bin大小应该基于列间距
            // 使用文本块的高度作为行间距的估计，宽度作为列间距的估计
            double baseHorizontalBinSize = Math.Max(5, medianHeight * 0.8);
            double baseVerticalBinSize = Math.Max(5, medianWidth * 0.8);

            // 【修改】应用密度调整因子 - 文本密度越高，bin越小
            double densityFactor = Math.Min(1.2, Math.Max(0.6, 1.0 - textDensity / 50.0));
            baseHorizontalBinSize *= densityFactor;
            baseVerticalBinSize *= densityFactor;

            // 【新增】确保bin大小比例在合理范围内
            double binRatio = baseVerticalBinSize / Math.Max(1.0, baseHorizontalBinSize);
            if (binRatio > 2.0)
            {
                // 垂直bin过大，调整为合理比例
                baseVerticalBinSize = baseHorizontalBinSize * 2.0;
                System.Diagnostics.Debug.WriteLine($"垂直bin过大，调整比例: {binRatio:F1} -> 2.0");
            }
            else if (binRatio < 0.5)
            {
                // 水平bin过大，调整为合理比例
                baseHorizontalBinSize = baseVerticalBinSize * 2.0;
                System.Diagnostics.Debug.WriteLine($"水平bin过大，调整比例: {binRatio:F1} -> 0.5");
            }

            // 应用形状特征调整因子
            features.HorizontalBinSize = baseHorizontalBinSize * horizontalBinFactor;
            features.VerticalBinSize = baseVerticalBinSize * verticalBinFactor;

            // 【新增】确保bin大小不会过大或过小
            features.HorizontalBinSize = Math.Max(3, Math.Min(features.HorizontalBinSize, medianHeight * 1.2));
            features.VerticalBinSize = Math.Max(3, Math.Min(features.VerticalBinSize, medianWidth * 1.2));

            // 【新增】再次检查最终bin大小比例
            double finalBinRatio = features.VerticalBinSize / Math.Max(1.0, features.HorizontalBinSize);
            if (finalBinRatio > 2.0)
            {
                features.VerticalBinSize = features.HorizontalBinSize * 2.0;
                System.Diagnostics.Debug.WriteLine($"最终垂直bin过大，强制调整比例: {finalBinRatio:F1} -> 2.0");
            }
            else if (finalBinRatio < 0.5)
            {
                features.HorizontalBinSize = features.VerticalBinSize * 2.0;
                System.Diagnostics.Debug.WriteLine($"最终水平bin过大，强制调整比例: {finalBinRatio:F1} -> 0.5");
            }

            // 【新增】输出最终bin大小
            System.Diagnostics.Debug.WriteLine($"最终bin大小 - 水平:{features.HorizontalBinSize:F1}, 垂直:{features.VerticalBinSize:F1}, 比例:{features.VerticalBinSize / features.HorizontalBinSize:F2}");

            // 2. 计算需要的bin数量
            int horizontalBinCount = (int)Math.Ceiling(features.PageHeight / features.HorizontalBinSize);
            int verticalBinCount = (int)Math.Ceiling(features.PageWidth / features.VerticalBinSize);

            // 初始化直方图数组
            features.HorizontalHistogram = new List<int>(new int[horizontalBinCount]);
            features.VerticalHistogram = new List<int>(new int[verticalBinCount]);

            // 3. 构建直方图
            foreach (var cell in cells)
            {
                // 计算文本块中心点
                double centerX = cell.location.left + cell.location.width / 2;
                double centerY = cell.location.top + cell.location.height / 2;

                // 计算对应的bin索引
                int verticalBin = (int)((centerX - features.PageLeft) / features.VerticalBinSize);
                int horizontalBin = (int)((centerY - features.PageTop) / features.HorizontalBinSize);

                // 确保索引在有效范围内
                verticalBin = Math.Max(0, Math.Min(verticalBinCount - 1, verticalBin));
                horizontalBin = Math.Max(0, Math.Min(horizontalBinCount - 1, horizontalBin));

                // 增加对应bin的计数
                features.VerticalHistogram[verticalBin]++;
                features.HorizontalHistogram[horizontalBin]++;
            }

            // 4. 平滑直方图，减少噪声影响
            features.VerticalHistogram = SmoothHistogram(features.VerticalHistogram);
            features.HorizontalHistogram = SmoothHistogram(features.HorizontalHistogram);

            // 5. 检测峰值
            DetectHistogramPeaks(features.VerticalHistogram, features.VerticalBinSize, features.VerticalPeakIndices);
            DetectHistogramPeaks(features.HorizontalHistogram, features.HorizontalBinSize, features.HorizontalPeakIndices);

            // 6. 分析峰值特征
            AnalyzeHistogramPeaks(features);

            // 7. 计算直方图得分
            CalculateHistogramScores(features);

            // 8. 计算直方图特征可靠性
            CalculateHistogramReliability(features);

            System.Diagnostics.Debug.WriteLine($"直方图分析结果 - 列数: {features.VerticalPeakCount}, 行数: {features.HorizontalPeakCount}");
            System.Diagnostics.Debug.WriteLine($"列规律性: {features.VerticalPeakRegularity:F2}, 行规律性: {features.HorizontalPeakRegularity:F2}");
            System.Diagnostics.Debug.WriteLine($"列结构得分: {features.HistogramColumnScore}, 行结构得分: {features.HistogramRowScore}");
            System.Diagnostics.Debug.WriteLine($"直方图可靠性: {features.HistogramFeatureReliability:F2}");

            // 标记直方图特征已计算
            features.IsHistogramFeaturesComputed = true;
        }

        /// <summary>
        /// 计算直方图特征可靠性 - 增强版
        /// </summary>
        private void CalculateHistogramReliability(TextLayoutFeatures features)
        {
            // 检查是否有足够的文本块进行分析
            if (features.ValidTextBlocks < 5)
            {
                // 文本块太少，直方图特征可靠性低
                features.HistogramFeatureReliability = 0.3;
                System.Diagnostics.Debug.WriteLine("文本块数量不足，直方图特征可靠性设为低值: 0.3");
                return;
            }

            // 默认可靠性中等
            double reliability = 0.5;

            // 1. 基于峰值质量评估可靠性
            if (features.VerticalPeakCount >= 2 || features.HorizontalPeakCount >= 2)
            {
                // 获取最佳的峰值质量
                double bestPeakQuality = Math.Max(
                    features.VerticalPeakShapeQuality,
                    features.HorizontalPeakShapeQuality);

                // 获取最佳的峰谷比
                double bestValleyRatio = Math.Max(
                    features.VerticalPeakValleyMeanRatio,
                    features.HorizontalPeakValleyMeanRatio);

                // 规范化峰谷比（通常为1.0-10.0范围）到0.0-1.0范围
                double normalizedRatio = Math.Min(1.0, (bestValleyRatio - 1.0) / 9.0);

                // 结合峰值质量和峰谷比计算可靠性
                reliability = 0.4 + (bestPeakQuality * 0.3) + (normalizedRatio * 0.3);
                System.Diagnostics.Debug.WriteLine($"峰值质量评估: 峰值质量={bestPeakQuality:F2}, 峰谷比={bestValleyRatio:F2}, 初始可靠性={reliability:F2}");
            }

            // 2. 基于峰值数量调整可靠性
            int maxPeakCount = Math.Max(features.VerticalPeakCount, features.HorizontalPeakCount);
            double peakCountBonus = 1.0;

            if (maxPeakCount >= 5)
            {
                // 大量峰值（多行或多列）显著增加可靠性
                peakCountBonus = 1.3;
                System.Diagnostics.Debug.WriteLine($"大量峰值 ({maxPeakCount} >= 5): 可靠性提升因子 1.3");
            }
            else if (maxPeakCount >= 3)
            {
                // 多峰值（多行或多列）增加可靠性
                peakCountBonus = 1.2;
                System.Diagnostics.Debug.WriteLine($"多个峰值 ({maxPeakCount} >= 3): 可靠性提升因子 1.2");
            }
            else if (maxPeakCount <= 1)
            {
                // 峰值太少，降低可靠性
                peakCountBonus = 0.8;
                System.Diagnostics.Debug.WriteLine($"峰值太少 ({maxPeakCount} <= 1): 可靠性降低因子 0.8");
            }

            reliability *= peakCountBonus;

            // 3. 基于峰值规律性调整可靠性
            double bestRegularity = Math.Min(
                features.VerticalPeakRegularity > 0 ? features.VerticalPeakRegularity : 1.0,
                features.HorizontalPeakRegularity > 0 ? features.HorizontalPeakRegularity : 1.0);

            double regularityFactor = 1.0;

            // 规律性越高（越接近0），可靠性越高
            if (bestRegularity < 0.15)
            {
                // 非常规律的结构
                regularityFactor = 1.3;
                System.Diagnostics.Debug.WriteLine($"非常规律的结构 (规律性={bestRegularity:F2}): 可靠性提升因子 1.3");
            }
            else if (bestRegularity < 0.25)
            {
                // 较规律的结构
                regularityFactor = 1.2;
                System.Diagnostics.Debug.WriteLine($"较规律的结构 (规律性={bestRegularity:F2}): 可靠性提升因子 1.2");
            }
            else if (bestRegularity > 0.6)
            {
                // 非常不规则的行列结构
                regularityFactor = 0.7;
                System.Diagnostics.Debug.WriteLine($"非常不规则的结构 (规律性={bestRegularity:F2}): 可靠性降低因子 0.7");
            }
            else if (bestRegularity > 0.4)
            {
                // 不规则的行列结构
                regularityFactor = 0.85;
                System.Diagnostics.Debug.WriteLine($"不规则的结构 (规律性={bestRegularity:F2}): 可靠性降低因子 0.85");
            }

            reliability *= regularityFactor;

            // 4. 【新增】基于结构强度调整可靠性
            double bestStructureStrength = Math.Max(
                features.VerticalStructureStrength,
                features.HorizontalStructureStrength);

            if (bestStructureStrength > 0.8)
            {
                // 非常强的结构特征
                reliability *= 1.2;
                System.Diagnostics.Debug.WriteLine($"非常强的结构特征 (强度={bestStructureStrength:F2}): 可靠性提升因子 1.2");
            }
            else if (bestStructureStrength < 0.3)
            {
                // 非常弱的结构特征
                reliability *= 0.8;
                System.Diagnostics.Debug.WriteLine($"非常弱的结构特征 (强度={bestStructureStrength:F2}): 可靠性降低因子 0.8");
            }

            // 5. 【新增】基于文档类型调整可靠性
            if (features.DocumentTypeCategory == TextFlowDocumentType.DefinitiveStructure)
            {
                // 明确结构文档的直方图特征更可靠
                reliability *= 1.15;
                System.Diagnostics.Debug.WriteLine("明确结构文档: 可靠性提升因子 1.15");
            }
            else if (features.DocumentTypeCategory == TextFlowDocumentType.LowQuality)
            {
                // 低质量文档的直方图特征不太可靠
                reliability *= 0.85;
                System.Diagnostics.Debug.WriteLine("低质量文档: 可靠性降低因子 0.85");
            }

            // 6. 【新增】检查水平和垂直方向的一致性
            bool hasHorizontalFeatures = features.HorizontalPeakCount >= 2 && features.HorizontalPeakRegularity < 0.5;
            bool hasVerticalFeatures = features.VerticalPeakCount >= 2 && features.VerticalPeakRegularity < 0.5;

            if (hasHorizontalFeatures && hasVerticalFeatures)
            {
                // 同时具有良好的水平和垂直特征，增加可靠性
                reliability *= 1.1;
                System.Diagnostics.Debug.WriteLine("同时具有良好的水平和垂直特征: 可靠性提升因子 1.1");
            }

            // 确保可靠性在合理范围内
            features.HistogramFeatureReliability = Math.Min(1.5, Math.Max(0.2, reliability));
            System.Diagnostics.Debug.WriteLine($"最终直方图特征可靠性: {features.HistogramFeatureReliability:F2}");
        }

        /// <summary>
        /// 平滑直方图，减少噪声影响
        /// </summary>
        private List<int> SmoothHistogram(List<int> histogram)
        {
            if (histogram.Count <= 3) return histogram; // 太短无需平滑

            List<int> smoothed = new List<int>(new int[histogram.Count]);

            // 使用简单的移动平均平滑
            for (int i = 0; i < histogram.Count; i++)
            {
                int sum = histogram[i];
                int count = 1;

                // 考虑前一个bin
                if (i > 0)
                {
                    sum += histogram[i - 1];
                    count++;
                }

                // 考虑后一个bin
                if (i < histogram.Count - 1)
                {
                    sum += histogram[i + 1];
                    count++;
                }

                // 计算平均值
                smoothed[i] = (int)Math.Round((double)sum / count);
            }

            return smoothed;
        }

        /// <summary>
        /// 检测直方图中的峰值
        /// </summary>
        private void DetectHistogramPeaks(List<int> histogram, double binSize, List<int> peakIndices)
        {
            if (histogram.Count < 3) return;

            peakIndices.Clear();

            // 【改进1】计算直方图的统计特性，用于自适应阈值设置
            double avgHeight = histogram.Average();
            double maxHeight = histogram.Max();
            double stdDev = CalculateStandardDeviation(histogram);

            // 【修复】输出直方图基本信息，便于调试
            System.Diagnostics.Debug.WriteLine($"直方图基本信息: 长度={histogram.Count}, 平均高度={avgHeight:F1}, 最大高度={maxHeight:F1}, 标准差={stdDev:F1}");

            // 【改进】计算直方图的非零比例和分布特性
            int nonZeroCount = 0;
            double sumNonZero = 0;
            for (int i = 0; i < histogram.Count; i++)
            {
                if (histogram[i] > 0)
                {
                    nonZeroCount++;
                    sumNonZero += histogram[i];
                }
            }

            // 非零值比例 - 反映数据的稀疏程度
            double nonZeroRatio = nonZeroCount / (double)Math.Max(1, histogram.Count);
            // 平均非零高度 - 反映有效数据的平均高度
            double avgNonZeroHeight = nonZeroCount > 0 ? sumNonZero / nonZeroCount : 0;

            // 计算信噪比 (SNR) - 峰值信号与背景噪声的比值
            // 使用更精确的噪声水平估计
            double noiseLevel = Math.Max(1.0, stdDev * (nonZeroRatio < 0.3 ? 0.3 : 0.5));
            double signalToNoiseRatio = maxHeight / noiseLevel;

            // 【改进2】使用自适应阈值系统，根据直方图特性动态调整
            // 根据非零比例调整Z-分数阈值 - 数据越稀疏，阈值越低
            double densityFactor = Math.Max(0.5, Math.Min(1.5, 1.0 / (nonZeroRatio + 0.2)));

            // 【修改】根据信噪比和密度因子动态确定Z-分数阈值，降低阈值使检测更敏感
            double zScoreThreshold = Math.Max(0.1, Math.Min(0.8,
                                    1.5 / (Math.Sqrt(signalToNoiseRatio) * densityFactor)));

            // 【修改】根据直方图特性动态调整最小峰值高度，降低阈值使检测更敏感
            double heightRatio = maxHeight / Math.Max(1.0, avgHeight);
            double distributionFactor = nonZeroRatio < 0.2 ? 0.5 : (nonZeroRatio < 0.5 ? 0.7 : 0.9);

            // 【修改】综合考虑多种因素的自适应阈值，降低阈值使检测更敏感
            double minPeakHeight = heightRatio < 3.0 ?
                Math.Max(1, avgNonZeroHeight * 0.4 * distributionFactor) : // 当峰不明显时，使用更低的阈值
                Math.Max(1, avgHeight + zScoreThreshold * stdDev * distributionFactor);

            // 【修改】动态确定峰值间距，考虑bin大小和直方图特性，降低间距使检测更敏感
            double peakDistanceFactor = Math.Max(0.2, Math.Min(0.8, 0.3 + signalToNoiseRatio / 25.0));
            // 【修改】降低最小峰值间距，使峰值检测更敏感
            int minPeakDistance = Math.Max(1, (int)(binSize * peakDistanceFactor / binSize));

            // 【新增】根据直方图长度动态调整最小峰值间距
            // 对于较长的直方图，允许更小的峰值间距
            if (histogram.Count > 50)
            {
                minPeakDistance = Math.Max(1, minPeakDistance / 2);
            }

            System.Diagnostics.Debug.WriteLine($"直方图统计: 平均={avgHeight:F1}, 最大={maxHeight:F1}, 标准差={stdDev:F1}");
            System.Diagnostics.Debug.WriteLine($"峰值检测参数: SNR={signalToNoiseRatio:F1}, Z阈值={zScoreThreshold:F1}, 最小峰高={minPeakHeight:F1}, 最小峰距={minPeakDistance}");

            // 【改进4】多尺度峰值检测 - 在不同平滑级别下检测峰值
            List<List<int>> multiScaleHistograms = new List<List<int>>();
            List<List<int>> multiScalePeaks = new List<List<int>>();

            // 原始直方图
            multiScaleHistograms.Add(new List<int>(histogram));

            // 轻度平滑直方图
            List<int> lightSmoothed = SmoothHistogram(histogram);
            multiScaleHistograms.Add(lightSmoothed);

            // 中度平滑直方图 (二次平滑)
            List<int> mediumSmoothed = SmoothHistogram(lightSmoothed);
            multiScaleHistograms.Add(mediumSmoothed);

            // 在每个尺度上检测峰值
            foreach (var histScale in multiScaleHistograms)
            {
                List<int> scalePeaks = new List<int>();
                List<double> scalePeakHeights = new List<double>();

                // 【修改】使用自适应峰值检测条件
                for (int i = 1; i < histScale.Count - 1; i++)
                {
                    // 【优化】使用更灵活的峰值检测条件，动态适应不同类型的直方图
                    bool isPotentialPeak = false;

                    // 计算局部信噪比 - 当前点与周围点的对比度
                    double localAvg = 0;
                    int localCount = 0;
                    // 【修改】使用更小的局部窗口，提高对密集峰值的敏感度
                    int localWindow = Math.Max(2, Math.Min(3, (int)(histScale.Count * 0.03)));

                    for (int j = Math.Max(0, i - localWindow); j <= Math.Min(histScale.Count - 1, i + localWindow); j++)
                    {
                        if (j != i)
                        {
                            localAvg += histScale[j];
                            localCount++;
                        }
                    }

                    localAvg = localCount > 0 ? localAvg / localCount : 0;
                    double localSNR = localAvg > 0 ? histScale[i] / localAvg : 1.0;

                    // 【修改】动态阈值 - 根据局部信噪比调整，降低阈值使检测更敏感
                    double dynamicThreshold = minPeakHeight * Math.Max(0.4, Math.Min(0.9, 1.0 - (localSNR - 1.0) * 0.25));

                    // 1. 标准峰值条件 - 比两侧都高
                    if (histScale[i] > dynamicThreshold &&
                        histScale[i] > histScale[i - 1] &&
                        histScale[i] >= histScale[i + 1])
                    {
                        isPotentialPeak = true;
                    }
                    // 2. 平顶峰值条件 - 连续几个相同高度的点形成平顶
                    else if (histScale[i] > dynamicThreshold * 0.8 && i > 1 && i < histScale.Count - 2) // 降低平顶峰值的阈值要求
                    {
                        // 如果当前点与右侧点相等，且比左侧点高，右侧第二个点低于当前点
                        if (histScale[i] == histScale[i + 1] &&
                            histScale[i] > histScale[i - 1] &&
                            (i + 2 >= histScale.Count || histScale[i] > histScale[i + 2]))
                        {
                            isPotentialPeak = true;
                        }
                        // 如果当前点与左侧点相等，且比右侧点高，左侧第二个点低于当前点
                        else if (histScale[i] == histScale[i - 1] &&
                                 histScale[i] > histScale[i + 1] &&
                                 (i - 2 < 0 || histScale[i] > histScale[i - 2]))
                        {
                            isPotentialPeak = true;
                        }
                    }
                    // 3. 局部最大值条件 - 在较宽范围内是局部最大值
                    else if (histScale[i] > dynamicThreshold * 0.7 && localSNR > 1.1) // 降低局部最大值的阈值要求
                    {
                        // 【修改】检查是否在较宽窗口内是局部最大值，使用更小的窗口
                        int windowSize = Math.Max(2, Math.Min(5, (int)(histScale.Count * 0.04)));
                        int leftBound = Math.Max(0, i - windowSize);
                        int rightBound = Math.Min(histScale.Count - 1, i + windowSize);

                        bool isLocalMax = true;
                        // 【修改】降低局部最大值的阈值要求
                        double localMaxThreshold = histScale[i] * (0.85 - (localSNR - 1.0) * 0.05); // 动态阈值

                        for (int j = leftBound; j <= rightBound; j++)
                        {
                            if (j != i && histScale[j] > localMaxThreshold)
                            {
                                isLocalMax = false;
                                break;
                            }
                        }

                        if (isLocalMax)
                        {
                            isPotentialPeak = true;
                            System.Diagnostics.Debug.WriteLine($"检测到局部最大值峰: 位置={i}, 高度={histScale[i]}, 局部SNR={localSNR:F2}");
                        }
                    }

                    if (isPotentialPeak)
                    {
                        scalePeaks.Add(i);
                        scalePeakHeights.Add(histScale[i]);
                    }
                }

                multiScalePeaks.Add(scalePeaks);
            }

            // 【改进5】峰值一致性分析 - 在多个尺度上出现的峰值更可靠
            Dictionary<int, int> peakFrequency = new Dictionary<int, int>();
            Dictionary<int, double> peakScores = new Dictionary<int, double>();

            // 统计每个峰值在不同尺度上出现的频率
            foreach (var scalePeaks in multiScalePeaks)
            {
                foreach (int peak in scalePeaks)
                {
                    // 【修改】考虑邻近位置的峰值可能是同一个峰，使用更大的邻近范围
                    bool found = false;
                    foreach (var existingPeak in peakFrequency.Keys.ToList())
                    {
                        if (Math.Abs(peak - existingPeak) <= 2) // 增大邻近范围
                        {
                            peakFrequency[existingPeak]++;
                            found = true;
                            break;
                        }
                    }

                    if (!found)
                    {
                        peakFrequency[peak] = 1;
                        // 初始峰值得分 = 峰高 / 平均高度
                        peakScores[peak] = histogram[peak] / avgHeight;
                    }
                }
            }

            // 【改进6】综合评分系统 - 结合峰值高度、多尺度一致性和峰谷比
            List<int> potentialPeaks = new List<int>();
            List<double> peakHeights = new List<double>();
            List<double> peakWidths = new List<double>();
            List<double> peakValleyRatios = new List<double>();
            List<double> finalScores = new List<double>();

            // 计算每个潜在峰值的综合评分
            foreach (var peak in peakFrequency.Keys)
            {
                // 一致性得分 - 在多个尺度上出现的峰值得分更高
                double consistencyScore = peakFrequency[peak] / (double)multiScaleHistograms.Count;

                // 峰高得分
                double heightScore = peakScores[peak];

                // 计算峰值宽度和峰谷比
                double peakWidth = 0;
                double valleyRatio = 1.0;
                CalculatePeakWidthAndValleyRatio(histogram, peak, out peakWidth, out valleyRatio);

                // 峰谷比得分
                double valleyRatioScore = Math.Min(10.0, valleyRatio) / 10.0;

                // 峰宽得分 - 适中宽度的峰值更可能是真实结构
                double idealWidth = binSize * 0.5;
                double widthScore = 1.0 / (1.0 + Math.Abs(peakWidth - idealWidth) / idealWidth);

                // 【修改】综合得分 - 各因素加权和，降低一致性要求，增加高度和峰谷比的权重
                double finalScore = heightScore * 0.5 + consistencyScore * 0.2 + valleyRatioScore * 0.2 + widthScore * 0.1;

                potentialPeaks.Add(peak);
                peakHeights.Add(histogram[peak]);
                peakWidths.Add(peakWidth);
                peakValleyRatios.Add(valleyRatio);
                finalScores.Add(finalScore);

                System.Diagnostics.Debug.WriteLine($"峰值分析: 位置={peak}, 高度={histogram[peak]}, 宽度={peakWidth:F1}, 峰谷比={valleyRatio:F1}, 一致性={consistencyScore:F1}, 最终得分={finalScore:F2}");
            }

            // 按综合得分排序，选择得分最高的峰值
            var sortedPeaks = potentialPeaks.Select((index, i) => new
            {
                Index = index,
                Height = peakHeights[i],
                Width = peakWidths[i],
                ValleyRatio = peakValleyRatios[i],
                Score = finalScores[i]
            }).OrderByDescending(p => p.Score).ToList();

            // 【改进7】自适应峰值选择 - 根据直方图特性决定保留多少峰值
            // 计算峰值数量的自适应上限
            // 【修改】进一步增加最小阈值，确保能检测到足够的峰值
            int maxPeaks = Math.Max(30, (int)(histogram.Count / binSize));

            // 【新增】根据文本块数量调整最大峰值数
            int textBlockCount = 0;
            for (int i = 0; i < histogram.Count; i++)
            {
                textBlockCount += histogram[i];
            }

            // 如果文本块数量很多，允许更多的峰值
            if (textBlockCount > 50)
            {
                maxPeaks = Math.Max(maxPeaks, textBlockCount / 5);
            }

            System.Diagnostics.Debug.WriteLine($"文本块总数: {textBlockCount}, 最大允许峰值数: {maxPeaks}");

            // 选择最终峰值，确保峰值间距足够
            List<int> finalPeakIndices = new List<int>();
            List<double> finalPeakWidths = new List<double>();
            List<double> finalPeakValleyRatios = new List<double>();

            foreach (var peak in sortedPeaks)
            {
                // 检查是否与已选择的峰值距离太近
                bool tooClose = false;
                foreach (int existingPeak in finalPeakIndices)
                {
                    if (Math.Abs(peak.Index - existingPeak) < minPeakDistance)
                    {
                        tooClose = true;
                        break;
                    }
                }

                if (!tooClose && finalPeakIndices.Count < maxPeaks)
                {
                    finalPeakIndices.Add(peak.Index);
                    finalPeakWidths.Add(peak.Width);
                    finalPeakValleyRatios.Add(peak.ValleyRatio);
                }
            }

            // 返回结果 - 按位置排序
            var orderedResults = finalPeakIndices.Select((index, i) => new
            {
                Index = index,
                Width = finalPeakWidths[i],
                ValleyRatio = finalPeakValleyRatios[i]
            }).OrderBy(p => p.Index).ToList();

            // 清除原有列表并填充排序后的结果
            peakIndices.Clear();
            foreach (var peak in orderedResults)
            {
                peakIndices.Add(peak.Index);
            }

            System.Diagnostics.Debug.WriteLine($"最终选择了 {peakIndices.Count} 个峰值");
        }

        /// <summary>
        /// 计算峰值的宽度和峰谷比
        /// </summary>
        private void CalculatePeakWidthAndValleyRatio(List<int> histogram, int peakIndex, out double peakWidth, out double valleyRatio)
        {
            double peakHeight = histogram[peakIndex];
            peakWidth = 0;
            valleyRatio = 1.0;

            // 1. 计算峰值宽度 - 找到左右两侧第一个低于峰高一半的点
            int leftWidth = 0;
            int rightWidth = 0;
            double halfHeight = peakHeight / 2;

            // 向左寻找
            for (int j = peakIndex - 1; j >= 0; j--)
            {
                if (histogram[j] < halfHeight)
                {
                    leftWidth = peakIndex - j;
                    break;
                }
                // 防止遍历到边界但仍未找到半高点
                if (j == 0)
                {
                    leftWidth = peakIndex;
                }
            }

            // 向右寻找
            for (int j = peakIndex + 1; j < histogram.Count; j++)
            {
                if (histogram[j] < halfHeight)
                {
                    rightWidth = j - peakIndex;
                    break;
                }
                // 防止遍历到边界但仍未找到半高点
                if (j == histogram.Count - 1)
                {
                    rightWidth = j - peakIndex;
                }
            }

            // 总宽度 = 左宽度 + 右宽度
            peakWidth = leftWidth + rightWidth;

            // 2. 计算峰谷比 - 峰值高度与左右相邻谷值的平均高度之比
            double leftValleyHeight = double.MaxValue;
            double rightValleyHeight = double.MaxValue;

            // 找左侧谷值
            for (int j = peakIndex - 1; j > 0; j--)
            {
                if (histogram[j] < histogram[j - 1] && histogram[j] < histogram[j + 1])
                {
                    leftValleyHeight = histogram[j];
                    break;
                }
                // 如果没有找到明确的谷值点，使用最小值
                if (j == 1)
                {
                    leftValleyHeight = Math.Min(histogram[0], histogram[1]);
                }
            }

            // 找右侧谷值
            for (int j = peakIndex + 1; j < histogram.Count - 1; j++)
            {
                if (histogram[j] < histogram[j - 1] && histogram[j] < histogram[j + 1])
                {
                    rightValleyHeight = histogram[j];
                    break;
                }
                // 如果没有找到明确的谷值点，使用最小值
                if (j == histogram.Count - 2)
                {
                    rightValleyHeight = Math.Min(histogram[histogram.Count - 1], histogram[histogram.Count - 2]);
                }
            }

            // 确保谷值合理
            if (leftValleyHeight == double.MaxValue) leftValleyHeight = 0;
            if (rightValleyHeight == double.MaxValue) rightValleyHeight = 0;

            // 计算峰谷比
            double avgValleyHeight = (leftValleyHeight + rightValleyHeight) / 2;
            if (avgValleyHeight > 0)
            {
                valleyRatio = peakHeight / avgValleyHeight;
            }
            else
            {
                // 避免除零
                valleyRatio = peakHeight > 0 ? 10.0 : 1.0;
            }
        }

        /// <summary>
        /// 【新增】峰值合并算法 - 合并过于接近的峰值
        /// </summary>
        /// <param name="histogram">直方图数据</param>
        /// <param name="peakIndices">原始峰值索引列表</param>
        /// <param name="minPeakDistance">最小峰值间距</param>
        /// <returns>合并后的峰值索引列表</returns>
        private List<int> MergePeaks(List<int> histogram, List<int> peakIndices, int minPeakDistance)
        {
            if (peakIndices.Count <= 1) return new List<int>(peakIndices);

            // 按峰值高度排序
            var sortedPeaks = peakIndices
                .Select(idx => new { Index = idx, Height = histogram[idx] })
                .OrderByDescending(p => p.Height)
                .ToList();

            List<int> mergedPeaks = new List<int>();
            List<bool> merged = new List<bool>(new bool[peakIndices.Count]);

            System.Diagnostics.Debug.WriteLine($"开始峰值合并，原始峰值数量: {peakIndices.Count}");

            // 从最高的峰值开始处理
            for (int i = 0; i < sortedPeaks.Count; i++)
            {
                if (merged[i]) continue;

                int currentPeakIdx = sortedPeaks[i].Index;
                int mergedPeakIdx = currentPeakIdx;
                int maxHeight = histogram[currentPeakIdx];
                double weightedSum = currentPeakIdx * maxHeight;
                double weightSum = maxHeight;

                // 查找需要合并的相邻峰值
                List<int> peaksToMerge = new List<int>();

                for (int j = 0; j < sortedPeaks.Count; j++)
                {
                    if (i == j || merged[j]) continue;

                    int otherPeakIdx = sortedPeaks[j].Index;
                    int distance = Math.Abs(currentPeakIdx - otherPeakIdx);

                    // 如果两个峰值距离小于最小间距，合并它们
                    if (distance < minPeakDistance)
                    {
                        peaksToMerge.Add(j);
                        merged[j] = true;

                        // 使用加权平均计算合并后的峰值位置
                        int height = histogram[otherPeakIdx];
                        weightedSum += otherPeakIdx * height;
                        weightSum += height;

                        if (height > maxHeight)
                        {
                            maxHeight = height;
                        }
                    }
                }

                // 如果有峰值需要合并
                if (peaksToMerge.Count > 0)
                {
                    // 计算合并后的峰值位置（加权平均）
                    mergedPeakIdx = (int)Math.Round(weightedSum / weightSum);

                    // 确保合并后的峰值位置在有效范围内
                    mergedPeakIdx = Math.Max(0, Math.Min(histogram.Count - 1, mergedPeakIdx));

                    System.Diagnostics.Debug.WriteLine($"合并峰值: {currentPeakIdx} 与 {string.Join(", ", peaksToMerge.Select(j => sortedPeaks[j].Index))} -> {mergedPeakIdx}");
                }

                mergedPeaks.Add(mergedPeakIdx);
                merged[i] = true;
            }

            // 按索引排序
            mergedPeaks.Sort();

            System.Diagnostics.Debug.WriteLine($"峰值合并完成，合并后峰值数量: {mergedPeaks.Count}");
            return mergedPeaks;
        }

        /// <summary>
        /// 【新增】峰值分割算法 - 分割过宽的峰值
        /// </summary>
        /// <param name="histogram">直方图数据</param>
        /// <param name="peakIndices">峰值索引列表</param>
        /// <param name="binSize">直方图bin大小</param>
        /// <returns>分割后的峰值索引列表</returns>
        private List<int> SplitPeaks(List<int> histogram, List<int> peakIndices, double binSize)
        {
            if (peakIndices.Count == 0) return new List<int>();

            List<int> splitPeaks = new List<int>();

            foreach (int peakIdx in peakIndices)
            {
                // 计算峰值宽度
                double peakWidth;
                double valleyRatio;
                CalculatePeakWidthAndValleyRatio(histogram, peakIdx, out peakWidth, out valleyRatio);

                // 如果峰值宽度超过阈值，考虑分割
                double widthThreshold = 2.5 * binSize; // 阈值设为2.5倍bin大小

                if (peakWidth > widthThreshold)
                {
                    // 检查峰值形状，寻找可能的分割点
                    bool shouldSplit = false;
                    int splitPoint = -1;

                    // 确定峰值范围
                    int leftBound = Math.Max(0, peakIdx - (int)(peakWidth / binSize));
                    int rightBound = Math.Min(histogram.Count - 1, peakIdx + (int)(peakWidth / binSize));

                    // 在峰值范围内寻找局部最小值作为分割点
                    for (int i = leftBound + 1; i < rightBound; i++)
                    {
                        // 检查是否为局部最小值
                        if (i > 0 && i < histogram.Count - 1 &&
                            histogram[i] < histogram[i - 1] && histogram[i] <= histogram[i + 1])
                        {
                            // 计算分割点的深度
                            double leftHeight = 0;
                            double rightHeight = 0;

                            // 向左寻找局部最高点
                            for (int j = i - 1; j >= leftBound; j--)
                            {
                                if (histogram[j] > leftHeight)
                                {
                                    leftHeight = histogram[j];
                                }
                                else if (j < i - 1 && histogram[j] < histogram[j + 1])
                                {
                                    // 找到局部最高点后停止
                                    break;
                                }
                            }

                            // 向右寻找局部最高点
                            for (int j = i + 1; j <= rightBound; j++)
                            {
                                if (histogram[j] > rightHeight)
                                {
                                    rightHeight = histogram[j];
                                }
                                else if (j > i + 1 && histogram[j] < histogram[j - 1])
                                {
                                    // 找到局部最高点后停止
                                    break;
                                }
                            }

                            // 计算分割点深度
                            double minHeight = Math.Min(leftHeight, rightHeight);
                            double valleyDepth = minHeight > 0 ? (minHeight - histogram[i]) / minHeight : 0;

                            // 如果分割点足够深，认为应该分割
                            if (valleyDepth > 0.25) // 深度阈值
                            {
                                shouldSplit = true;
                                splitPoint = i;
                                break;
                            }
                        }
                    }

                    if (shouldSplit && splitPoint >= 0)
                    {
                        // 寻找左侧峰值
                        int leftPeakIdx = -1;
                        int maxLeftHeight = 0;
                        for (int i = leftBound; i < splitPoint; i++)
                        {
                            if (histogram[i] > maxLeftHeight)
                            {
                                maxLeftHeight = histogram[i];
                                leftPeakIdx = i;
                            }
                        }

                        // 寻找右侧峰值
                        int rightPeakIdx = -1;
                        int maxRightHeight = 0;
                        for (int i = splitPoint + 1; i <= rightBound; i++)
                        {
                            if (histogram[i] > maxRightHeight)
                            {
                                maxRightHeight = histogram[i];
                                rightPeakIdx = i;
                            }
                        }

                        // 添加分割后的峰值
                        if (leftPeakIdx >= 0)
                        {
                            splitPeaks.Add(leftPeakIdx);
                        }
                        if (rightPeakIdx >= 0)
                        {
                            splitPeaks.Add(rightPeakIdx);
                        }

                        System.Diagnostics.Debug.WriteLine($"分割峰值 {peakIdx} (宽度: {peakWidth:F1}) -> 左峰: {leftPeakIdx}, 右峰: {rightPeakIdx}");
                    }
                    else
                    {
                        // 不需要分割，保留原峰值
                        splitPeaks.Add(peakIdx);
                    }
                }
                else
                {
                    // 峰值宽度正常，不需要分割
                    splitPeaks.Add(peakIdx);
                }
            }

            // 按索引排序
            splitPeaks.Sort();

            return splitPeaks;
        }

        /// <summary>
        /// 分析直方图峰值特征
        /// </summary>
        private void AnalyzeHistogramPeaks(TextLayoutFeatures features)
        {
            // 【新增】应用峰值合并和分割算法
            if (features.VerticalPeakIndices.Count > 0)
            {
                // 先分割过宽的峰值
                List<int> splitVerticalPeaks = SplitPeaks(features.VerticalHistogram, features.VerticalPeakIndices, features.VerticalBinSize);

                // 再合并过近的峰值
                int minVerticalPeakDistance = Math.Max(2, (int)(features.VerticalBinSize * 1.5));
                List<int> optimizedVerticalPeaks = MergePeaks(features.VerticalHistogram, splitVerticalPeaks, minVerticalPeakDistance);

                // 更新峰值索引
                features.VerticalPeakIndices = optimizedVerticalPeaks;
                System.Diagnostics.Debug.WriteLine($"垂直峰值优化: 原始={features.VerticalPeakCount} -> 分割={splitVerticalPeaks.Count} -> 合并={optimizedVerticalPeaks.Count}");
            }

            if (features.HorizontalPeakIndices.Count > 0)
            {
                // 先分割过宽的峰值
                List<int> splitHorizontalPeaks = SplitPeaks(features.HorizontalHistogram, features.HorizontalPeakIndices, features.HorizontalBinSize);

                // 再合并过近的峰值
                int minHorizontalPeakDistance = Math.Max(2, (int)(features.HorizontalBinSize * 1.5));
                List<int> optimizedHorizontalPeaks = MergePeaks(features.HorizontalHistogram, splitHorizontalPeaks, minHorizontalPeakDistance);

                // 更新峰值索引
                features.HorizontalPeakIndices = optimizedHorizontalPeaks;
                System.Diagnostics.Debug.WriteLine($"水平峰值优化: 原始={features.HorizontalPeakCount} -> 分割={splitHorizontalPeaks.Count} -> 合并={optimizedHorizontalPeaks.Count}");
            }

            // 设置峰值数量
            features.VerticalPeakCount = features.VerticalPeakIndices.Count;
            features.HorizontalPeakCount = features.HorizontalPeakIndices.Count;

            // 【新增】重新分析峰值，获取峰值宽度和峰谷比
            if (features.VerticalHistogram.Count > 0)
            {
                CalculatePeakQualityMetrics(
                    features.VerticalHistogram,
                    features.VerticalPeakIndices,
                    features.VerticalPeakWidths,
                    features.VerticalPeakValleyRatios,
                    out double meanWidth,
                    out double meanValleyRatio,
                    out double shapeQuality);

                features.VerticalPeakMeanWidth = meanWidth;
                features.VerticalPeakValleyMeanRatio = meanValleyRatio;
                features.VerticalPeakShapeQuality = shapeQuality;
            }

            if (features.HorizontalHistogram.Count > 0)
            {
                CalculatePeakQualityMetrics(
                    features.HorizontalHistogram,
                    features.HorizontalPeakIndices,
                    features.HorizontalPeakWidths,
                    features.HorizontalPeakValleyRatios,
                    out double meanWidth,
                    out double meanValleyRatio,
                    out double shapeQuality);

                features.HorizontalPeakMeanWidth = meanWidth;
                features.HorizontalPeakValleyMeanRatio = meanValleyRatio;
                features.HorizontalPeakShapeQuality = shapeQuality;
            }

            // 分析垂直方向(列)峰值
            if (features.VerticalPeakIndices.Count >= 2)
            {
                // 计算峰值间距
                List<double> peakDistances = new List<double>();
                for (int i = 1; i < features.VerticalPeakIndices.Count; i++)
                {
                    peakDistances.Add(features.VerticalPeakIndices[i] - features.VerticalPeakIndices[i - 1]);
                }

                // 计算峰值规律性 - 使用变异系数(标准差/均值)的倒数
                // 变异系数越小，规律性越高
                if (peakDistances.Count > 0)
                {
                    double avgDistance = peakDistances.Average();
                    if (avgDistance > 0)
                    {
                        double stdDev = CalculateStandardDeviation(peakDistances);
                        double cv = stdDev / avgDistance; // 变异系数

                        // 规律性 = 1 / (1 + 变异系数)，范围在0-1之间，越大越规律
                        features.VerticalPeakRegularity = 1.0 / (1.0 + cv);
                    }
                }

                // 计算峰值强度 - 峰值高度与平均高度的比值
                double avgHeight = features.VerticalHistogram.Average();
                double peakHeightSum = 0;
                foreach (int peakIndex in features.VerticalPeakIndices)
                {
                    peakHeightSum += features.VerticalHistogram[peakIndex];
                }

                if (avgHeight > 0 && features.VerticalPeakIndices.Count > 0)
                {
                    double avgPeakHeight = peakHeightSum / features.VerticalPeakIndices.Count;
                    features.VerticalPeakStrength = avgPeakHeight / avgHeight;
                }
            }

            // 分析水平方向(行)峰值
            if (features.HorizontalPeakIndices.Count >= 2)
            {
                // 计算峰值间距
                List<double> peakDistances = new List<double>();
                for (int i = 1; i < features.HorizontalPeakIndices.Count; i++)
                {
                    peakDistances.Add(features.HorizontalPeakIndices[i] - features.HorizontalPeakIndices[i - 1]);
                }

                // 计算峰值规律性
                if (peakDistances.Count > 0)
                {
                    double avgDistance = peakDistances.Average();
                    if (avgDistance > 0)
                    {
                        double stdDev = CalculateStandardDeviation(peakDistances);
                        double cv = stdDev / avgDistance;
                        features.HorizontalPeakRegularity = 1.0 / (1.0 + cv);
                    }
                }

                // 计算峰值强度
                double avgHeight = features.HorizontalHistogram.Average();
                double peakHeightSum = 0;
                foreach (int peakIndex in features.HorizontalPeakIndices)
                {
                    peakHeightSum += features.HorizontalHistogram[peakIndex];
                }

                if (avgHeight > 0 && features.HorizontalPeakIndices.Count > 0)
                {
                    double avgPeakHeight = peakHeightSum / features.HorizontalPeakIndices.Count;
                    features.HorizontalPeakStrength = avgPeakHeight / avgHeight;
                }
            }

            // 【新增】计算结构强度 - 综合考虑峰值数量、规律性、峰谷比和强度
            CalculateStructureStrength(features);
        }

        /// <summary>
        /// 计算峰值质量指标 - 峰值宽度和峰谷比
        /// </summary>
        private void CalculatePeakQualityMetrics(
            List<int> histogram,
            List<int> peakIndices,
            List<double> peakWidths,
            List<double> peakValleyRatios,
            out double meanWidth,
            out double meanValleyRatio,
            out double shapeQuality)
        {
            // 清空现有列表
            peakWidths.Clear();
            peakValleyRatios.Clear();

            meanWidth = 0;
            meanValleyRatio = 1.0;
            shapeQuality = 0.5;

            if (peakIndices.Count == 0 || histogram.Count < 3)
                return;

            // 计算每个峰值的宽度和峰谷比
            foreach (int peakIndex in peakIndices)
            {
                if (peakIndex <= 0 || peakIndex >= histogram.Count - 1)
                    continue;

                double peakHeight = histogram[peakIndex];

                // 1. 计算峰值宽度 - 找到左右两侧第一个低于峰高一半的点
                int leftWidth = 0;
                int rightWidth = 0;
                double halfHeight = peakHeight / 2;

                // 向左寻找
                for (int j = peakIndex - 1; j >= 0; j--)
                {
                    if (histogram[j] < halfHeight)
                    {
                        leftWidth = peakIndex - j;
                        break;
                    }
                    // 防止遍历到边界但仍未找到半高点
                    if (j == 0)
                    {
                        leftWidth = peakIndex;
                    }
                }

                // 向右寻找
                for (int j = peakIndex + 1; j < histogram.Count; j++)
                {
                    if (histogram[j] < halfHeight)
                    {
                        rightWidth = j - peakIndex;
                        break;
                    }
                    // 防止遍历到边界但仍未找到半高点
                    if (j == histogram.Count - 1)
                    {
                        rightWidth = j - peakIndex;
                    }
                }

                // 总宽度 = 左宽度 + 右宽度
                double totalWidth = leftWidth + rightWidth;
                peakWidths.Add(totalWidth);

                // 2. 计算峰谷比 - 峰值高度与左右相邻谷值的平均高度之比
                double leftValleyHeight = double.MaxValue;
                double rightValleyHeight = double.MaxValue;

                // 找左侧谷值
                for (int j = peakIndex - 1; j > 0; j--)
                {
                    if (histogram[j] < histogram[j - 1] && histogram[j] < histogram[j + 1])
                    {
                        leftValleyHeight = histogram[j];
                        break;
                    }
                    // 如果没有找到明确的谷值点，使用最小值
                    if (j == 1)
                    {
                        leftValleyHeight = Math.Min(histogram[0], histogram[1]);
                    }
                }

                // 找右侧谷值
                for (int j = peakIndex + 1; j < histogram.Count - 1; j++)
                {
                    if (histogram[j] < histogram[j - 1] && histogram[j] < histogram[j + 1])
                    {
                        rightValleyHeight = histogram[j];
                        break;
                    }
                    // 如果没有找到明确的谷值点，使用最小值
                    if (j == histogram.Count - 2)
                    {
                        rightValleyHeight = Math.Min(histogram[histogram.Count - 1], histogram[histogram.Count - 2]);
                    }
                }

                // 确保谷值合理
                if (leftValleyHeight == double.MaxValue) leftValleyHeight = 0;
                if (rightValleyHeight == double.MaxValue) rightValleyHeight = 0;

                // 计算峰谷比
                double avgValleyHeight = (leftValleyHeight + rightValleyHeight) / 2;
                if (avgValleyHeight > 0)
                {
                    peakValleyRatios.Add(peakHeight / avgValleyHeight);
                }
                else
                {
                    // 避免除零
                    peakValleyRatios.Add(peakHeight > 0 ? 10.0 : 1.0);
                }
            }

            // 计算平均值
            if (peakWidths.Count > 0)
            {
                meanWidth = peakWidths.Average();
            }

            if (peakValleyRatios.Count > 0)
            {
                meanValleyRatio = peakValleyRatios.Average();

                // 计算峰值形状质量分数 (0-1)
                // 峰谷比越高，形状质量越好；同时考虑宽度相对均匀性
                double valleyRatioScore = Math.Min(1.0, meanValleyRatio / 5.0); // 峰谷比达到5或以上视为满分

                double widthVariability = 0;
                if (peakWidths.Count > 1)
                {
                    double stdDevWidth = CalculateStandardDeviation(peakWidths);
                    double meanWidth2 = peakWidths.Average();
                    if (meanWidth2 > 0)
                    {
                        widthVariability = stdDevWidth / meanWidth2;
                    }
                }

                // 宽度变异性得分（越均匀越高分）
                double widthUniformityScore = 1.0 / (1.0 + widthVariability);

                // 综合评分
                shapeQuality = 0.7 * valleyRatioScore + 0.3 * widthUniformityScore;
            }
        }

        /// <summary>
        /// 计算结构强度 - 用于动态权重调整
        /// </summary>
        private void CalculateStructureStrength(TextLayoutFeatures features)
        {
            // 垂直结构强度计算
            double verticalStrength = 0.0;
            if (features.VerticalPeakCount >= 2)
            {
                // 基于峰值数量、规律性、峰谷比和峰值强度计算
                double countScore = Math.Min(1.0, features.VerticalPeakCount / 5.0); // 最多5列视为满分
                double regularityScore = features.VerticalPeakRegularity;
                double valleyRatioScore = Math.Min(1.0, features.VerticalPeakValleyMeanRatio / 5.0);
                double strengthScore = Math.Min(1.0, features.VerticalPeakStrength / 3.0);

                // 加权平均
                verticalStrength = 0.3 * countScore + 0.3 * regularityScore + 0.2 * valleyRatioScore + 0.2 * strengthScore;
            }

            // 水平结构强度计算
            double horizontalStrength = 0.0;
            if (features.HorizontalPeakCount >= 2)
            {
                // 行数通常比列数多，用8作为满分基准
                double countScore = Math.Min(1.0, features.HorizontalPeakCount / 8.0);
                double regularityScore = features.HorizontalPeakRegularity;
                double valleyRatioScore = Math.Min(1.0, features.HorizontalPeakValleyMeanRatio / 5.0);
                double strengthScore = Math.Min(1.0, features.HorizontalPeakStrength / 3.0);

                // 加权平均
                horizontalStrength = 0.3 * countScore + 0.3 * regularityScore + 0.2 * valleyRatioScore + 0.2 * strengthScore;
            }

            // 设置结构强度
            features.VerticalStructureStrength = verticalStrength;
            features.HorizontalStructureStrength = horizontalStrength;

            // 计算方向一致性 - 判断是否有一个方向明显强于另一个方向
            if (verticalStrength > 0 || horizontalStrength > 0)
            {
                double maxStrength = Math.Max(verticalStrength, horizontalStrength);
                double minStrength = Math.Min(verticalStrength, horizontalStrength);

                if (maxStrength > 0)
                {
                    // 方向一致性 = 最大强度方向相对于最小强度方向的优势度
                    // 当两个方向强度接近时，一致性较低；当一个方向明显强于另一个方向时，一致性高
                    features.DirectionalConsistency = maxStrength / (maxStrength + Math.Max(0.1, minStrength));
                }
            }
        }

        /// <summary>
        /// 计算直方图结构得分
        /// </summary>
        private void CalculateHistogramScores(TextLayoutFeatures features)
        {
            // 初始化得分
            int columnScore = 0;
            int rowScore = 0;

            // 1. 基于峰值数量的得分
            // 竖排布局通常有多列结构
            if (features.VerticalPeakCount >= 3)
            {
                columnScore += 8; // 3列或以上，强烈暗示竖排
                System.Diagnostics.Debug.WriteLine($"检测到{features.VerticalPeakCount}列，列结构得分 +8");
            }
            else if (features.VerticalPeakCount == 2)
            {
                columnScore += 5; // 2列，中等暗示竖排
                System.Diagnostics.Debug.WriteLine($"检测到2列，列结构得分 +5");
            }

            // 横排布局通常有多行结构
            if (features.HorizontalPeakCount >= 5)
            {
                rowScore += 8; // 5行或以上，强烈暗示横排
                System.Diagnostics.Debug.WriteLine($"检测到{features.HorizontalPeakCount}行，行结构得分 +8");
            }
            else if (features.HorizontalPeakCount >= 3)
            {
                rowScore += 5; // 3-4行，中等暗示横排
                System.Diagnostics.Debug.WriteLine($"检测到{features.HorizontalPeakCount}行，行结构得分 +5");
            }

            // 2. 基于峰值规律性的得分
            // 规律性高的结构更可能是有意义的排版
            if (features.VerticalPeakRegularity > 0.8 && features.VerticalPeakCount >= 2)
            {
                // 【修改】增加规律性权重
                int regScore = (int)(8 * features.VerticalPeakRegularity);
                columnScore += regScore;
                System.Diagnostics.Debug.WriteLine($"列间距非常规律(规律性={features.VerticalPeakRegularity:F2})，列结构得分 +{regScore}");
            }
            else if (features.VerticalPeakRegularity > 0.6 && features.VerticalPeakCount >= 2)
            {
                // 【修改】增加规律性权重
                int regScore = (int)(6 * features.VerticalPeakRegularity);
                columnScore += regScore;
                System.Diagnostics.Debug.WriteLine($"列间距较规律(规律性={features.VerticalPeakRegularity:F2})，列结构得分 +{regScore}");
            }

            if (features.HorizontalPeakRegularity > 0.8 && features.HorizontalPeakCount >= 2)
            {
                // 【修改】增加规律性权重
                int regScore = (int)(8 * features.HorizontalPeakRegularity);
                rowScore += regScore;
                System.Diagnostics.Debug.WriteLine($"行间距非常规律(规律性={features.HorizontalPeakRegularity:F2})，行结构得分 +{regScore}");
            }
            else if (features.HorizontalPeakRegularity > 0.6 && features.HorizontalPeakCount >= 2)
            {
                // 【修改】增加规律性权重
                int regScore = (int)(6 * features.HorizontalPeakRegularity);
                rowScore += regScore;
                System.Diagnostics.Debug.WriteLine($"行间距较规律(规律性={features.HorizontalPeakRegularity:F2})，行结构得分 +{regScore}");
            }

            // 3. 基于峰值强度的得分
            // 峰值越明显，结构越清晰
            if (features.VerticalPeakStrength > 2.0 && features.VerticalPeakCount >= 2)
            {
                // 【修改】增加强度权重
                int strengthScore = (int)(7 * Math.Min(3.0, features.VerticalPeakStrength) / 3.0);
                columnScore += strengthScore;
                System.Diagnostics.Debug.WriteLine($"列结构非常明显(强度={features.VerticalPeakStrength:F2})，列结构得分 +{strengthScore}");
            }

            if (features.HorizontalPeakStrength > 2.0 && features.HorizontalPeakCount >= 2)
            {
                // 【修改】增加强度权重
                int strengthScore = (int)(7 * Math.Min(3.0, features.HorizontalPeakStrength) / 3.0);
                rowScore += strengthScore;
                System.Diagnostics.Debug.WriteLine($"行结构非常明显(强度={features.HorizontalPeakStrength:F2})，行结构得分 +{strengthScore}");
            }

            // 【新增】4. 基于峰谷比的得分
            // 峰谷比越高，表示峰值越清晰
            if (features.VerticalPeakValleyMeanRatio > 3.0 && features.VerticalPeakCount >= 2)
            {
                int valleyScore = (int)(6 * Math.Min(5.0, features.VerticalPeakValleyMeanRatio) / 5.0);
                columnScore += valleyScore;
                System.Diagnostics.Debug.WriteLine($"列峰谷比非常高(比值={features.VerticalPeakValleyMeanRatio:F2})，列结构得分 +{valleyScore}");
            }

            if (features.HorizontalPeakValleyMeanRatio > 3.0 && features.HorizontalPeakCount >= 2)
            {
                int valleyScore = (int)(6 * Math.Min(5.0, features.HorizontalPeakValleyMeanRatio) / 5.0);
                rowScore += valleyScore;
                System.Diagnostics.Debug.WriteLine($"行峰谷比非常高(比值={features.HorizontalPeakValleyMeanRatio:F2})，行结构得分 +{valleyScore}");
            }

            // 【新增】5. 基于峰值形状质量的得分
            if (features.VerticalPeakShapeQuality > 0.7 && features.VerticalPeakCount >= 2)
            {
                int shapeScore = (int)(5 * features.VerticalPeakShapeQuality);
                columnScore += shapeScore;
                System.Diagnostics.Debug.WriteLine($"列峰值形状质量高(质量={features.VerticalPeakShapeQuality:F2})，列结构得分 +{shapeScore}");
            }

            if (features.HorizontalPeakShapeQuality > 0.7 && features.HorizontalPeakCount >= 2)
            {
                int shapeScore = (int)(5 * features.HorizontalPeakShapeQuality);
                rowScore += shapeScore;
                System.Diagnostics.Debug.WriteLine($"行峰值形状质量高(质量={features.HorizontalPeakShapeQuality:F2})，行结构得分 +{shapeScore}");
            }

            // 6. 考虑峰值数量比例
            // 如果列数明显多于行数，或行数明显多于列数，进一步增强对应得分
            if (features.VerticalPeakCount > features.HorizontalPeakCount * 1.5 && features.VerticalPeakCount >= 3)
            {
                columnScore += 4;
                System.Diagnostics.Debug.WriteLine($"列数({features.VerticalPeakCount})明显多于行数({features.HorizontalPeakCount})，列结构得分 +4");
            }

            if (features.HorizontalPeakCount > features.VerticalPeakCount * 1.5 && features.HorizontalPeakCount >= 3)
            {
                rowScore += 4;
                System.Diagnostics.Debug.WriteLine($"行数({features.HorizontalPeakCount})明显多于列数({features.VerticalPeakCount})，行结构得分 +4");
            }

            // 【新增】7. 结构强度极高时的特殊处理
            // 当某个方向的结构强度非常高时，显著提升其权重
            if (features.VerticalStructureStrength > 0.8)
            {
                int boostScore = (int)(20 * (features.VerticalStructureStrength - 0.8) * 5);
                columnScore += boostScore;
                System.Diagnostics.Debug.WriteLine($"列结构强度极高(强度={features.VerticalStructureStrength:F2})，列结构得分额外 +{boostScore}");

                // 如果结构强度极端高（接近1），则直接给予非常高的分数
                if (features.VerticalStructureStrength > 0.9)
                {
                    columnScore = Math.Max(columnScore, 50);
                    System.Diagnostics.Debug.WriteLine($"列结构强度接近完美，确保列结构得分至少为50");
                }
            }

            if (features.HorizontalStructureStrength > 0.8)
            {
                int boostScore = (int)(20 * (features.HorizontalStructureStrength - 0.8) * 5);
                rowScore += boostScore;
                System.Diagnostics.Debug.WriteLine($"行结构强度极高(强度={features.HorizontalStructureStrength:F2})，行结构得分额外 +{boostScore}");

                // 如果结构强度极端高（接近1），则直接给予非常高的分数
                if (features.HorizontalStructureStrength > 0.9)
                {
                    rowScore = Math.Max(rowScore, 50);
                    System.Diagnostics.Debug.WriteLine($"行结构强度接近完美，确保行结构得分至少为50");
                }
            }

            // 【新增】8. 方向一致性高时的特殊处理
            // 当方向一致性很高时，进一步增强主导方向的权重
            if (features.DirectionalConsistency > 0.8)
            {
                // 确定主导方向
                if (features.VerticalStructureStrength > features.HorizontalStructureStrength)
                {
                    int consistencyBoost = (int)(10 * (features.DirectionalConsistency - 0.8) * 5);
                    columnScore += consistencyBoost;
                    System.Diagnostics.Debug.WriteLine($"方向一致性高且垂直结构占优(一致性={features.DirectionalConsistency:F2})，列结构得分额外 +{consistencyBoost}");
                }
                else
                {
                    int consistencyBoost = (int)(10 * (features.DirectionalConsistency - 0.8) * 5);
                    rowScore += consistencyBoost;
                    System.Diagnostics.Debug.WriteLine($"方向一致性高且水平结构占优(一致性={features.DirectionalConsistency:F2})，行结构得分额外 +{consistencyBoost}");
                }
            }

            // 设置最终得分
            features.HistogramColumnScore = columnScore;
            features.HistogramRowScore = rowScore;
        }

        /// <summary>
        /// 从布局特征中提取方向证据
        /// </summary>
        private DirectionEvidence ExtractDirectionEvidence(TextLayoutFeatures features)
        {
            var evidence = new DirectionEvidence();

            // 1. 提取文本块形状特征
            evidence.VerticalTextCount = features.VerticalTextCount;
            evidence.HorizontalTextCount = features.HorizontalTextCount;
            evidence.SquareTextCount = features.SquareTextCount;

            // 2. 提取边缘变异特征
            evidence.LeftEdgeVariance = features.LeftEdgeVariance;
            evidence.RightEdgeVariance = features.RightEdgeVariance;
            evidence.TopEdgeVariance = features.TopEdgeVariance;
            evidence.BottomEdgeVariance = features.BottomEdgeVariance;

            // 3. 提取行列结构特征
            evidence.HorizontalAlignedRows = features.HorizontalRowCount;
            evidence.VerticalAlignedColumns = features.VerticalColumnCount;

            // 4. 提取对齐特征
            evidence.LeftAlignedCount = features.LeftAlignedCount;
            evidence.RightAlignedCount = features.RightAlignedCount;
            evidence.TopAlignedCount = features.TopAlignedCount;
            evidence.BottomAlignedCount = features.BottomAlignedCount;

            // 5. 提取段落特征
            evidence.ParagraphCount = features.ParagraphCount;
            evidence.IsSequentialParagraphs = features.IsSequentialParagraphs;

            // 6. 提取内容分布特征
            evidence.ContentDensityLeftHalf = features.ContentDensityLeftHalf;
            evidence.ContentDensityRightHalf = features.ContentDensityRightHalf;
            evidence.ContentDensityTopHalf = features.ContentDensityTopHalf;
            evidence.ContentDensityBottomHalf = features.ContentDensityBottomHalf;

            // 7. 提取直方图特征
            evidence.HistogramColumnScore = features.HistogramColumnScore;
            evidence.HistogramRowScore = features.HistogramRowScore;
            evidence.VerticalPeakCount = features.VerticalPeakCount;
            evidence.HorizontalPeakCount = features.HorizontalPeakCount;
            evidence.VerticalPeakRegularity = features.VerticalPeakRegularity;
            evidence.HorizontalPeakRegularity = features.HorizontalPeakRegularity;

            // 【新增】8. 提取峰值质量特征
            evidence.VerticalPeakValleyMeanRatio = features.VerticalPeakValleyMeanRatio;
            evidence.HorizontalPeakValleyMeanRatio = features.HorizontalPeakValleyMeanRatio;
            evidence.VerticalPeakShapeQuality = features.VerticalPeakShapeQuality;
            evidence.HorizontalPeakShapeQuality = features.HorizontalPeakShapeQuality;

            // 【新增】9. 提取结构强度特征
            evidence.VerticalStructureStrength = features.VerticalStructureStrength;
            evidence.HorizontalStructureStrength = features.HorizontalStructureStrength;
            evidence.DirectionalConsistency = features.DirectionalConsistency;

            return evidence;
        }

        /// <summary>
        /// 确定横排文本流方向
        /// </summary>
        private void DetermineHorizontalFlowDirection(TextDirectionResult result, DirectionEvidence evidence)
        {
            // 默认从左到右
            result.FlowDirection = TextFlowDirection.LeftToRight;
            result.DirectionConfidence = 70; // 默认置信度

            System.Diagnostics.Debug.WriteLine("\n--- 横排文本流方向分析 ---");

            // 初始化证据标志
            bool hasContentDistributionEvidence = false;
            bool hasAlignmentEvidence = false;

            // 【新增】0. 结构强度特征 - 如果有极高的结构强度，优先考虑
            if (evidence.HorizontalStructureStrength > 0.85)
            {
                // 如果水平结构强度非常高，增强置信度
                result.DirectionConfidence = Math.Max(result.DirectionConfidence, 80);
                System.Diagnostics.Debug.WriteLine($"水平结构强度极高(强度={evidence.HorizontalStructureStrength:F2}) => 增强横排布局置信度至{result.DirectionConfidence}%");
            }

            // 1. 内容分布特征
            if (evidence.ContentDensityRightHalf > evidence.ContentDensityLeftHalf * 1.5)
            {
                // 右半页内容明显多于左半页，可能是从右到左
                result.FlowDirection = TextFlowDirection.RightToLeft;
                result.DirectionConfidence = 75;
                result.IsRightToLeft = true;
                hasContentDistributionEvidence = true;
                System.Diagnostics.Debug.WriteLine($"右半页内容密度明显高于左半页({evidence.ContentDensityRightHalf:F2} vs {evidence.ContentDensityLeftHalf:F2}) => 从右到左，置信度: {result.DirectionConfidence}%");
            }
            else if (evidence.ContentDensityLeftHalf > evidence.ContentDensityRightHalf * 1.5)
            {
                // 左半页内容明显多于右半页，可能是从左到右
                result.FlowDirection = TextFlowDirection.LeftToRight;
                result.DirectionConfidence = 75;
                result.IsRightToLeft = false;
                hasContentDistributionEvidence = true;
                System.Diagnostics.Debug.WriteLine($"左半页内容密度明显高于右半页({evidence.ContentDensityLeftHalf:F2} vs {evidence.ContentDensityRightHalf:F2}) => 从左到右，置信度: {result.DirectionConfidence}%");
            }
            else if (evidence.RightAlignedCount > evidence.LeftAlignedCount * 2 && evidence.RightAlignedCount >= 3)
            {
                // 右对齐明显多于左对齐，强烈暗示从右到左
                result.FlowDirection = TextFlowDirection.RightToLeft;
                result.DirectionConfidence = Math.Max(result.DirectionConfidence, 85);
                result.IsRightToLeft = true;
                hasAlignmentEvidence = true;
                System.Diagnostics.Debug.WriteLine($"右对齐文本块明显多于左对齐({evidence.RightAlignedCount} vs {evidence.LeftAlignedCount}) => 从右到左，置信度: {result.DirectionConfidence}%");
            }
            // 【新增】边缘对齐中等差异的情况
            else if (evidence.LeftAlignedCount > evidence.RightAlignedCount * 1.5 && evidence.LeftAlignedCount >= 2)
            {
                // 左对齐略多于右对齐，暗示从左到右
                if (!hasAlignmentEvidence || result.FlowDirection == TextFlowDirection.LeftToRight)
                {
                    result.FlowDirection = TextFlowDirection.LeftToRight;
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, 75);
                    result.IsRightToLeft = false;
                    hasAlignmentEvidence = true;
                    System.Diagnostics.Debug.WriteLine($"左对齐文本块略多于右对齐({evidence.LeftAlignedCount} vs {evidence.RightAlignedCount}) => 倾向于从左到右，置信度: {result.DirectionConfidence}%");
                }
            }
            else if (evidence.RightAlignedCount > evidence.LeftAlignedCount * 1.5 && evidence.RightAlignedCount >= 2)
            {
                // 右对齐略多于左对齐，暗示从右到左
                if (!hasAlignmentEvidence || result.FlowDirection == TextFlowDirection.RightToLeft)
                {
                    result.FlowDirection = TextFlowDirection.RightToLeft;
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, 75);
                    result.IsRightToLeft = true;
                    hasAlignmentEvidence = true;
                    System.Diagnostics.Debug.WriteLine($"右对齐文本块略多于左对齐({evidence.RightAlignedCount} vs {evidence.LeftAlignedCount}) => 倾向于从右到左，置信度: {result.DirectionConfidence}%");
                }
            }

            // 3. 边缘变异特征
            bool hasEdgeVarianceEvidence = false;
            if (evidence.LeftEdgeVariance < evidence.RightEdgeVariance * 0.5)
            {
                // 左边缘比右边缘更整齐，暗示从左到右
                if (!hasEdgeVarianceEvidence || result.FlowDirection == TextFlowDirection.LeftToRight)
                {
                    result.FlowDirection = TextFlowDirection.LeftToRight;
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, 80);
                    result.IsRightToLeft = false;
                    hasEdgeVarianceEvidence = true;
                    System.Diagnostics.Debug.WriteLine($"左边缘明显比右边缘更整齐(变异:{evidence.LeftEdgeVariance:F2} vs {evidence.RightEdgeVariance:F2}) => 从左到右，置信度: {result.DirectionConfidence}%");
                }
            }
            else if (evidence.RightEdgeVariance < evidence.LeftEdgeVariance * 0.5)
            {
                // 右边缘比左边缘更整齐，暗示从右到左
                if (!hasEdgeVarianceEvidence || result.FlowDirection == TextFlowDirection.RightToLeft)
                {
                    result.FlowDirection = TextFlowDirection.RightToLeft;
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, 80);
                    result.IsRightToLeft = true;
                    hasEdgeVarianceEvidence = true;
                    System.Diagnostics.Debug.WriteLine($"右边缘明显比左边缘更整齐(变异:{evidence.RightEdgeVariance:F2} vs {evidence.LeftEdgeVariance:F2}) => 从右到左，置信度: {result.DirectionConfidence}%");
                }
            }
            // 【新增】边缘变异中等差异的情况
            else if (evidence.LeftEdgeVariance < evidence.RightEdgeVariance * 0.7)
            {
                // 左边缘比右边缘略整齐，暗示从左到右
                if (!hasEdgeVarianceEvidence && (!hasAlignmentEvidence || result.FlowDirection == TextFlowDirection.LeftToRight))
                {
                    result.FlowDirection = TextFlowDirection.LeftToRight;
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, 70);
                    result.IsRightToLeft = false;
                    hasEdgeVarianceEvidence = true;
                    System.Diagnostics.Debug.WriteLine($"左边缘略比右边缘更整齐(变异:{evidence.LeftEdgeVariance:F2} vs {evidence.RightEdgeVariance:F2}) => 倾向于从左到右，置信度: {result.DirectionConfidence}%");
                }
            }
            else if (evidence.RightEdgeVariance < evidence.LeftEdgeVariance * 0.7)
            {
                // 右边缘比左边缘略整齐，暗示从右到左
                if (!hasEdgeVarianceEvidence && (!hasAlignmentEvidence || result.FlowDirection == TextFlowDirection.RightToLeft))
                {
                    result.FlowDirection = TextFlowDirection.RightToLeft;
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, 70);
                    result.IsRightToLeft = true;
                    hasEdgeVarianceEvidence = true;
                    System.Diagnostics.Debug.WriteLine($"右边缘略比左边缘更整齐(变异:{evidence.RightEdgeVariance:F2} vs {evidence.LeftEdgeVariance:F2}) => 倾向于从右到左，置信度: {result.DirectionConfidence}%");
                }
            }

            // 【新增】4. 方向一致性特征 - 如果方向一致性高，进一步提升置信度
            if (evidence.DirectionalConsistency > 0.8 && evidence.HorizontalStructureStrength > 0.7)
            {
                int confidenceBoost = (int)(10 * (evidence.DirectionalConsistency - 0.8) * 5);
                result.DirectionConfidence = Math.Min(100, result.DirectionConfidence + confidenceBoost);
                System.Diagnostics.Debug.WriteLine($"方向一致性高(一致性={evidence.DirectionalConsistency:F2}) => 增加置信度{confidenceBoost}点，最终置信度: {result.DirectionConfidence}%");
            }

            // 【新增】5. 综合证据分析 - 当证据相互冲突时的处理
            if (hasContentDistributionEvidence && hasAlignmentEvidence && hasEdgeVarianceEvidence)
            {
                // 所有证据都存在，检查是否一致
                bool isContentRTL = evidence.ContentDensityRightHalf > evidence.ContentDensityLeftHalf;
                bool isAlignmentRTL = evidence.RightAlignedCount > evidence.LeftAlignedCount;
                bool isEdgeRTL = evidence.RightEdgeVariance < evidence.LeftEdgeVariance;

                // 计算从右到左的证据数量
                int rtlEvidenceCount = (isContentRTL ? 1 : 0) + (isAlignmentRTL ? 1 : 0) + (isEdgeRTL ? 1 : 0);

                if (rtlEvidenceCount >= 2)
                {
                    // 多数证据支持从右到左
                    result.FlowDirection = TextFlowDirection.RightToLeft;
                    result.IsRightToLeft = true;
                    // 根据证据一致性增加置信度
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, 80 + (rtlEvidenceCount == 3 ? 10 : 0));
                    System.Diagnostics.Debug.WriteLine($"综合分析: {rtlEvidenceCount}/3的证据支持从右到左 => 确定为从右到左，置信度: {result.DirectionConfidence}%");
                }
                else
                {
                    // 多数证据支持从左到右
                    result.FlowDirection = TextFlowDirection.LeftToRight;
                    result.IsRightToLeft = false;
                    // 根据证据一致性增加置信度
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, 80 + (rtlEvidenceCount == 0 ? 10 : 0));
                    System.Diagnostics.Debug.WriteLine($"综合分析: {3 - rtlEvidenceCount}/3的证据支持从左到右 => 确定为从左到右，置信度: {result.DirectionConfidence}%");
                }
            }

            System.Diagnostics.Debug.WriteLine($"最终横排文本流方向: {result.FlowDirection}，置信度: {result.DirectionConfidence}%");
        }

        /// <summary>
        /// 确定竖排布局的文本流方向
        /// </summary>
        private void DetermineVerticalFlowDirection(TextDirectionResult result, DirectionEvidence evidence)
        {
            // 默认从上到下
            result.FlowDirection = TextFlowDirection.TopToBottom;
            result.DirectionConfidence = 80; // 竖排默认从上到下的置信度较高

            // 【新增】设置垂直方向默认置信度
            if (result.VerticalConfidence <= 0)
            {
                result.VerticalConfidence = 70;
                System.Diagnostics.Debug.WriteLine("设置竖排布局的垂直方向默认置信度: 70%");
            }

            // 【修改】基于左右方向证据计算水平方向
            // 计算左右方向的证据比例
            int totalHorizontalEvidence = evidence.LeftToRightCount + evidence.RightToLeftCount;

            // 【新增】获取特征对象，用于动态权重调整
            TextLayoutFeatures features = ExtractTextFeatures(cells);

            // 【新增】根据文档类型确定特征权重
            double histogramWeight = 0.40; // 默认直方图特征权重
            double edgeWeight = 0.30;      // 默认边缘特征权重
            double alignmentWeight = 0.20; // 默认对齐特征权重
            double contentWeight = 0.10;   // 默认内容分布特征权重

            // 根据文档类型动态调整权重
            switch (features.DocumentTypeCategory)
            {
                case TextFlowDocumentType.DefinitiveStructure:
                    // 明确结构文档 - 直方图特征权重最高
                    histogramWeight = 0.60;
                    edgeWeight = 0.25;
                    alignmentWeight = 0.10;
                    contentWeight = 0.05;
                    System.Diagnostics.Debug.WriteLine("明确结构文档 - 使用直方图特征主导的权重系统");
                    break;

                case TextFlowDocumentType.HighStructure:
                    // 高结构化文档 - 直方图和边缘特征权重较高
                    histogramWeight = 0.50;
                    edgeWeight = 0.35;
                    alignmentWeight = 0.10;
                    contentWeight = 0.05;
                    System.Diagnostics.Debug.WriteLine("高结构化文档 - 使用直方图和边缘特征平衡的权重系统");
                    break;

                case TextFlowDocumentType.LowQuality:
                    // 低质量文档 - 边缘特征和内容分布特征权重较高
                    histogramWeight = 0.20;
                    edgeWeight = 0.55;
                    alignmentWeight = 0.10;
                    contentWeight = 0.15;
                    System.Diagnostics.Debug.WriteLine("低质量文档 - 使用边缘特征主导的权重系统");
                    break;

                default: // DocumentType.Normal
                    // 普通文档 - 均衡的权重分配
                    histogramWeight = 0.40;
                    edgeWeight = 0.30;
                    alignmentWeight = 0.20;
                    contentWeight = 0.10;
                    System.Diagnostics.Debug.WriteLine("普通文本文档 - 使用均衡的权重系统");
                    break;
            }

            // 【新增】对于中文竖排文本，默认偏向从右到左
            // 这是基于中文竖排传统排版规则
            bool isCJKVerticalText = evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5;

            // 【新增】基于峰值形状和峰谷比计算直方图结构质量评分
            int histogramStructureScore = 0;
            if (evidence.VerticalPeakCount >= 2)
            {
                // 峰值形状质量评分 (0-10)
                int shapeQualityScore = (int)(evidence.VerticalPeakShapeQuality * 10);

                // 峰谷比评分 (0-10)，峰谷比越大表示列结构越清晰
                int valleyRatioScore = (int)(Math.Min(10, evidence.VerticalPeakValleyMeanRatio * 2));

                // 列间距规律性评分 (0-10)
                int regularityScore = (int)(evidence.VerticalPeakRegularity * 10);

                // 结构强度评分 (0-10)
                int strengthScore = (int)(evidence.VerticalStructureStrength * 10);

                // 计算综合得分 - 加权平均
                histogramStructureScore = (int)((shapeQualityScore * 0.3) +
                                                (valleyRatioScore * 0.3) +
                                                (regularityScore * 0.2) +
                                                (strengthScore * 0.2));

                System.Diagnostics.Debug.WriteLine($"直方图结构评分 - 形状质量:{shapeQualityScore}, 峰谷比:{valleyRatioScore}, " +
                                                 $"规律性:{regularityScore}, 强度:{strengthScore}, 综合:{histogramStructureScore}");
            }

            // 根据直方图结构质量调整基础加分
            int baseRTLBonus = isCJKVerticalText ? 10 : 0; // 中文竖排文本基础加分
            if (histogramStructureScore > 7)
            {
                // 结构非常清晰，显著增加从右到左的可能性
                baseRTLBonus += Math.Min(10, histogramStructureScore - 5);
                System.Diagnostics.Debug.WriteLine($"列结构非常清晰(评分:{histogramStructureScore})，增加从右到左基础加分至:{baseRTLBonus}");
            }

            if (totalHorizontalEvidence > 0)
            {
                // 根据证据比例确定水平方向
                if (evidence.RightToLeftCount > evidence.LeftToRightCount)
                {
                    result.HorizontalDirection = TextFlowDirection.RightToLeft;
                    result.IsRightToLeft = true;

                    // 计算置信度基于证据比例
                    double rtlRatio = (double)evidence.RightToLeftCount / totalHorizontalEvidence;
                    // 【修改】提高基础置信度，并为中文竖排文本增加额外分数
                    result.HorizontalConfidence = (int)(65 + 30 * rtlRatio) + baseRTLBonus; // 65-95之间的置信度，加上可能的额外分数
                    result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence); // 确保不超过95

                    System.Diagnostics.Debug.WriteLine($"基于方向证据比例({rtlRatio:F2})，水平方向为从右到左，置信度: {result.HorizontalConfidence}%");
                }
                else if (evidence.LeftToRightCount > evidence.RightToLeftCount)
                {
                    // 【修改】移除中文竖排文本强制设置右到左的处理规则，完全依靠特征评分系统
                    // 使用动态权重系统评估方向
                    {
                        result.HorizontalDirection = TextFlowDirection.LeftToRight;
                        result.IsRightToLeft = false;

                        // 计算置信度基于证据比例
                        double ltrRatio = (double)evidence.LeftToRightCount / totalHorizontalEvidence;
                        result.HorizontalConfidence = (int)(60 + 35 * ltrRatio); // 60-95之间的置信度

                        System.Diagnostics.Debug.WriteLine($"基于方向证据比例({ltrRatio:F2})，水平方向为从左到右，置信度: {result.HorizontalConfidence}%");
                    }
                }
                else
                {
                    // 证据相等，分析直方图特征
                    AnalyzeHistogramForHorizontalDirection(result, evidence, histogramStructureScore, baseRTLBonus, isCJKVerticalText);
                }
            }
            else
            {
                // 无方向证据，分析直方图特征
                AnalyzeHistogramForHorizontalDirection(result, evidence, histogramStructureScore, baseRTLBonus, isCJKVerticalText);
            }

            System.Diagnostics.Debug.WriteLine("\n--- 竖排文本流方向分析 ---");

            // 【新增】0. 结构强度特征 - 如果有极高的结构强度，优先考虑
            if (evidence.VerticalStructureStrength > 0.85)
            {
                // 如果垂直结构强度非常高，增强置信度
                result.DirectionConfidence = Math.Max(result.DirectionConfidence, 85);
                System.Diagnostics.Debug.WriteLine($"垂直结构强度极高(强度={evidence.VerticalStructureStrength:F2}) => 增强竖排布局置信度至{result.DirectionConfidence}%");
            }

            // 【新增】分析文本块形状比例分布
            double verticalTextRatio = 0;
            if (evidence.VerticalTextCount + evidence.HorizontalTextCount > 0)
            {
                verticalTextRatio = evidence.VerticalTextCount / (double)(evidence.VerticalTextCount + evidence.HorizontalTextCount);
                System.Diagnostics.Debug.WriteLine($"竖直文本块比例: {verticalTextRatio:F2} ({evidence.VerticalTextCount}个竖直/{evidence.HorizontalTextCount}个水平)");

                // 当竖直文本块占比非常高时，这是一个强烈的竖排布局指标
                if (verticalTextRatio > 0.7 && evidence.VerticalTextCount >= 3)
                {
                    // 增加竖排布局方向的置信度
                    int shapeBoost = (int)(10 * Math.Min(1.0, verticalTextRatio));
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, 80 + shapeBoost);
                    System.Diagnostics.Debug.WriteLine($"竖直文本块比例很高({verticalTextRatio:F2})，增强竖排布局置信度 +{shapeBoost}");
                }
            }

            // 1. 内容分布特征
            // 【修改】内容分布阈值从1.2提高到1.5，使用非线性函数增强显著差异
            double contentThreshold = 1.5; // 提高基础阈值

            // 计算内容分布比例，并使用平方函数增强差异
            double bottomTopRatio = evidence.ContentDensityBottomHalf / Math.Max(0.001, evidence.ContentDensityTopHalf);
            double topBottomRatio = evidence.ContentDensityTopHalf / Math.Max(0.001, evidence.ContentDensityBottomHalf);

            // 使用平方函数增强显著差异
            double enhancedBottomTopRatio = bottomTopRatio * Math.Sqrt(bottomTopRatio);
            double enhancedTopBottomRatio = topBottomRatio * Math.Sqrt(topBottomRatio);

            if (enhancedBottomTopRatio > contentThreshold)
            {
                // 下半页内容明显多于上半页，可能是从下到上
                result.FlowDirection = TextFlowDirection.BottomToTop;
                // 根据差异程度调整置信度
                int confidenceBoost = (int)(70 + Math.Min(15, (enhancedBottomTopRatio - contentThreshold) * 10));
                result.DirectionConfidence = confidenceBoost;
                System.Diagnostics.Debug.WriteLine($"下半页内容密度明显高于上半页(增强比例:{enhancedBottomTopRatio:F2}) => 从下到上，置信度: {result.DirectionConfidence}%");
            }
            else if (enhancedTopBottomRatio > contentThreshold)
            {
                // 上半页内容明显多于下半页，可能是从上到下
                // 根据差异程度调整置信度
                int confidenceBoost = (int)(80 + Math.Min(10, (enhancedTopBottomRatio - contentThreshold) * 8));
                result.DirectionConfidence = confidenceBoost;
                System.Diagnostics.Debug.WriteLine($"上半页内容密度明显高于下半页(增强比例:{enhancedTopBottomRatio:F2}) => 从上到下，置信度: {result.DirectionConfidence}%");
            }

            // 2. 边缘对齐特征
            // 【修改】边缘对齐阈值从2提高到3，使用动态阈值
            double alignmentThreshold = features.DocumentTypeCategory == TextFlowDocumentType.DefinitiveStructure ? 2.0 :
                                       features.DocumentTypeCategory == TextFlowDocumentType.HighStructure ? 2.5 : 3.0;

            if (evidence.TopAlignedCount > evidence.BottomAlignedCount * alignmentThreshold && evidence.TopAlignedCount >= 3)
            {
                // 顶部对齐明显多于底部对齐，强烈暗示从上到下
                result.FlowDirection = TextFlowDirection.TopToBottom;
                // 根据文档类型调整置信度
                int confidenceBoost = features.DocumentTypeCategory == TextFlowDocumentType.DefinitiveStructure ? 90 :
                                     features.DocumentTypeCategory == TextFlowDocumentType.HighStructure ? 88 : 85;
                result.DirectionConfidence = Math.Max(result.DirectionConfidence, confidenceBoost);
                System.Diagnostics.Debug.WriteLine($"顶部对齐文本块明显多于底部对齐(比例:{(double)evidence.TopAlignedCount / evidence.BottomAlignedCount:F1}) => 从上到下，置信度: {result.DirectionConfidence}%");
            }
            else if (evidence.BottomAlignedCount > evidence.TopAlignedCount * alignmentThreshold && evidence.BottomAlignedCount >= 3)
            {
                // 底部对齐明显多于顶部对齐，可能是从下到上
                result.FlowDirection = TextFlowDirection.BottomToTop;
                // 根据文档类型调整置信度
                int confidenceBoost = features.DocumentTypeCategory == TextFlowDocumentType.DefinitiveStructure ? 85 :
                                     features.DocumentTypeCategory == TextFlowDocumentType.HighStructure ? 80 : 75;
                result.DirectionConfidence = Math.Max(result.DirectionConfidence, confidenceBoost);
                System.Diagnostics.Debug.WriteLine($"底部对齐文本块明显多于顶部对齐(比例:{(double)evidence.BottomAlignedCount / evidence.TopAlignedCount:F1}) => 从下到上，置信度: {result.DirectionConfidence}%");
            }

            // 【修复】3. 确保垂直方向也被设置，并且垂直方向置信度不为0
            result.VerticalDirection = result.FlowDirection;

            // 确保垂直方向置信度不为0
            if (result.VerticalConfidence <= 0)
            {
                // 如果垂直方向置信度为0，使用方向置信度或设置默认值
                result.VerticalConfidence = result.DirectionConfidence > 0 ? result.DirectionConfidence : 70;
                System.Diagnostics.Debug.WriteLine($"垂直方向置信度为0，设置为: {result.VerticalConfidence}%");
            }

            // 【新增】4. 综合评分 - 整合多种特征评分
            // 只有在特征可靠性足够高时才应用综合评分
            if (features.HistogramFeatureReliability > 0.5 ||
                features.EdgeFeatureReliability > 0.5 ||
                features.AlignmentFeatureReliability > 0.5)
            {
                // 计算从上到下和从下到上的综合评分
                double topToBottomScore = 0;
                double bottomToTopScore = 0;

                // 直方图特征评分
                if (evidence.VerticalPeakCount >= 2)
                {
                    // 根据峰值分布和结构强度评分
                    if (result.FlowDirection == TextFlowDirection.TopToBottom)
                    {
                        topToBottomScore += histogramWeight * evidence.VerticalStructureStrength * 10;
                    }
                    else
                    {
                        bottomToTopScore += histogramWeight * evidence.VerticalStructureStrength * 10;
                    }
                }

                // 边缘特征评分
                double topEdgeVar = evidence.TopEdgeVariance;
                double bottomEdgeVar = evidence.BottomEdgeVariance;
                if (topEdgeVar > 0 && bottomEdgeVar > 0)
                {
                    if (topEdgeVar < bottomEdgeVar)
                    {
                        // 顶部边缘变异小，支持从上到下
                        topToBottomScore += edgeWeight * (1.0 - (topEdgeVar / (topEdgeVar + bottomEdgeVar))) * 10;
                    }
                    else
                    {
                        // 底部边缘变异小，支持从下到上
                        bottomToTopScore += edgeWeight * (1.0 - (bottomEdgeVar / (topEdgeVar + bottomEdgeVar))) * 10;
                    }
                }

                // 对齐特征评分
                double topAlignRatio = evidence.TopAlignedCount / (double)Math.Max(1, evidence.TopAlignedCount + evidence.BottomAlignedCount);
                double bottomAlignRatio = 1.0 - topAlignRatio;
                if (evidence.TopAlignedCount > 0 || evidence.BottomAlignedCount > 0)
                {
                    topToBottomScore += alignmentWeight * topAlignRatio * 10;
                    bottomToTopScore += alignmentWeight * bottomAlignRatio * 10;
                }

                // 内容分布特征评分
                if (evidence.ContentDensityTopHalf > 0 || evidence.ContentDensityBottomHalf > 0)
                {
                    double contentRatio = evidence.ContentDensityTopHalf / Math.Max(0.001, evidence.ContentDensityTopHalf + evidence.ContentDensityBottomHalf);
                    topToBottomScore += contentWeight * contentRatio * 10;
                    bottomToTopScore += contentWeight * (1.0 - contentRatio) * 10;
                }

                System.Diagnostics.Debug.WriteLine($"综合评分 - 从上到下: {topToBottomScore:F2}, 从下到上: {bottomToTopScore:F2}");

                // 根据综合评分确定最终方向和置信度
                if (topToBottomScore > bottomToTopScore)
                {
                    result.FlowDirection = TextFlowDirection.TopToBottom;
                    result.VerticalDirection = TextFlowDirection.TopToBottom;
                    double scoreDiff = topToBottomScore - bottomToTopScore;
                    int confidenceBoost = (int)(70 + Math.Min(25, scoreDiff * 2));
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, confidenceBoost);
                    result.VerticalConfidence = Math.Max(result.VerticalConfidence, confidenceBoost);
                    System.Diagnostics.Debug.WriteLine($"综合评分支持从上到下(差异:{scoreDiff:F2})，置信度: {result.DirectionConfidence}%");
                }
                else if (bottomToTopScore > topToBottomScore)
                {
                    result.FlowDirection = TextFlowDirection.BottomToTop;
                    result.VerticalDirection = TextFlowDirection.BottomToTop;
                    double scoreDiff = bottomToTopScore - topToBottomScore;
                    int confidenceBoost = (int)(70 + Math.Min(25, scoreDiff * 2));
                    result.DirectionConfidence = Math.Max(result.DirectionConfidence, confidenceBoost);
                    result.VerticalConfidence = Math.Max(result.VerticalConfidence, confidenceBoost);
                    System.Diagnostics.Debug.WriteLine($"综合评分支持从下到上(差异:{scoreDiff:F2})，置信度: {result.DirectionConfidence}%");
                }
            }
        }

        /// <summary>
        /// 【新增】辅助方法：根据直方图特征分析水平方向
        /// </summary>
        private void AnalyzeHistogramForHorizontalDirection(TextDirectionResult result, DirectionEvidence evidence,
                                                           int histogramStructureScore, int baseRTLBonus, bool isCJKVerticalText)
        {
            System.Diagnostics.Debug.WriteLine("\n--- 基于直方图特征分析水平方向 ---");

            // 【新增】分析峰值形状质量和峰谷比
            double peakShapeScore = evidence.VerticalPeakShapeQuality * 10; // 0-10分
            double valleyRatioScore = Math.Min(10, Math.Pow(evidence.VerticalPeakValleyMeanRatio, 2)); // 使用平方函数，0-10分
            double regularityScore = evidence.VerticalPeakRegularity * 10; // 0-10分

            // 【新增】高规律性奖励
            if (evidence.VerticalPeakRegularity > 0.8)
            {
                regularityScore = Math.Min(10, regularityScore * 1.5);
                System.Diagnostics.Debug.WriteLine($"高规律性奖励: 规律性评分提升至{regularityScore:F1}");
            }

            System.Diagnostics.Debug.WriteLine($"峰值形状质量评分: {peakShapeScore:F1}, 峰谷比评分: {valleyRatioScore:F1}, 规律性评分: {regularityScore:F1}");

            // 【新增】判断是否为明确结构
            bool isDefinitiveStructure = evidence.VerticalPeakShapeQuality > 0.8 &&
                                         evidence.VerticalPeakValleyMeanRatio > 2.0 &&
                                         evidence.VerticalPeakRegularity > 0.8;

            // 【新增】判断文档类型，动态调整权重
            double alignmentWeight, edgeWeight, contentWeight, shapeWeight, valleyWeight, regularityWeight;

            if (isDefinitiveStructure)
            {
                // 明确结构文档 - 直方图特征权重大幅提升
                alignmentWeight = 0.15;
                edgeWeight = 0.10;
                contentWeight = 0.15;
                shapeWeight = 0.25;
                valleyWeight = 0.20;
                regularityWeight = 0.15;
                System.Diagnostics.Debug.WriteLine("检测到明确结构文档，直方图特征权重大幅提升");
            }
            else if (evidence.VerticalPeakShapeQuality > 0.7 ||
                     evidence.VerticalPeakValleyMeanRatio > 1.8 ||
                     evidence.VerticalPeakRegularity > 0.7)
            {
                // 高结构化文档
                alignmentWeight = 0.20;
                edgeWeight = 0.15;
                contentWeight = 0.15;
                shapeWeight = 0.20;
                valleyWeight = 0.15;
                regularityWeight = 0.15;
                System.Diagnostics.Debug.WriteLine("检测到高结构化文档，直方图特征权重提升");
            }
            else if (evidence.VerticalPeakShapeQuality < 0.4 &&
                     evidence.VerticalPeakValleyMeanRatio < 1.2)
            {
                // 低质量文档
                alignmentWeight = 0.30;
                edgeWeight = 0.25;
                contentWeight = 0.25;
                shapeWeight = 0.10;
                valleyWeight = 0.05;
                regularityWeight = 0.05;
                System.Diagnostics.Debug.WriteLine("检测到低质量文档，边缘特征权重提升");
            }
            else
            {
                // 普通文本文档 - 默认权重
                alignmentWeight = 0.25;
                edgeWeight = 0.20;
                contentWeight = 0.20;
                shapeWeight = 0.15;
                valleyWeight = 0.10;
                regularityWeight = 0.10;
                System.Diagnostics.Debug.WriteLine("检测到普通文本文档，使用默认权重");
            }

            // 【新增】计算右侧峰值比例
            double rightPeakRatio = 0.5; // 默认均匀分布
            if (evidence.VerticalPeakCount >= 2)
            {
                // 这里需要访问原始的峰值索引数据，但我们可以通过其他特征间接推断
                if (evidence.ContentDensityRightHalf > evidence.ContentDensityLeftHalf * 1.2)
                {
                    rightPeakRatio = 0.7; // 估计右侧峰值比例较高
                }
                else if (evidence.ContentDensityLeftHalf > evidence.ContentDensityRightHalf * 1.2)
                {
                    rightPeakRatio = 0.3; // 估计左侧峰值比例较高
                }
            }

            // 【修改】计算边缘对齐特征得分 - 提高阈值并使用非线性计分
            double rightAlignmentScore = 0;
            double leftAlignmentScore = 0;

            if (evidence.RightAlignedCount >= 3)
            {
                // 前3个对齐块每个1.5分，之后每个2.5分
                rightAlignmentScore = Math.Min(10, 3 * 1.5 + Math.Max(0, evidence.RightAlignedCount - 3) * 2.5);
            }
            else if (evidence.RightAlignedCount > 0)
            {
                rightAlignmentScore = evidence.RightAlignedCount * 1.5;
            }

            if (evidence.LeftAlignedCount >= 3)
            {
                // 前3个对齐块每个1.5分，之后每个2.5分
                leftAlignmentScore = Math.Min(10, 3 * 1.5 + Math.Max(0, evidence.LeftAlignedCount - 3) * 2.5);
            }
            else if (evidence.LeftAlignedCount > 0)
            {
                leftAlignmentScore = evidence.LeftAlignedCount * 1.5;
            }

            System.Diagnostics.Debug.WriteLine($"右边缘对齐评分: {rightAlignmentScore:F1}, 左边缘对齐评分: {leftAlignmentScore:F1}");

            // 【修改】计算边缘变异特征得分 - 使用指数函数增强显著差异
            double rightEdgeScore = 0;
            double leftEdgeScore = 0;

            // 动态阈值 - 根据峰值质量调整
            double edgeVarianceThreshold = 0.7; // 默认阈值
            if (evidence.VerticalPeakShapeQuality > 0.7)
            {
                edgeVarianceThreshold = 0.8; // 高质量文档阈值更严格
            }
            else if (evidence.VerticalPeakShapeQuality < 0.4)
            {
                edgeVarianceThreshold = 0.6; // 低质量文档阈值更宽松
            }

            if (evidence.RightEdgeVariance > 0 && evidence.LeftEdgeVariance > 0)
            {
                // 边缘变异越小，边缘越整齐，得分越高
                if (evidence.RightEdgeVariance < evidence.LeftEdgeVariance * edgeVarianceThreshold)
                {
                    double varianceRatio = evidence.RightEdgeVariance / Math.Max(evidence.LeftEdgeVariance, evidence.RightEdgeVariance);
                    // 使用指数函数增强显著差异
                    rightEdgeScore = 10 * Math.Pow(1 - varianceRatio, 1.5);
                }

                if (evidence.LeftEdgeVariance < evidence.RightEdgeVariance * edgeVarianceThreshold)
                {
                    double varianceRatio = evidence.LeftEdgeVariance / Math.Max(evidence.LeftEdgeVariance, evidence.RightEdgeVariance);
                    // 使用指数函数增强显著差异
                    leftEdgeScore = 10 * Math.Pow(1 - varianceRatio, 1.5);
                }
            }

            System.Diagnostics.Debug.WriteLine($"右边缘整齐度评分: {rightEdgeScore:F1}, 左边缘整齐度评分: {leftEdgeScore:F1}");

            // 【修改】计算内容分布特征得分 - 提高阈值并使用平方函数
            double rightContentScore = 0;
            double leftContentScore = 0;

            if (evidence.ContentDensityRightHalf > evidence.ContentDensityLeftHalf * 1.5)
            {
                double ratio = evidence.ContentDensityRightHalf / evidence.ContentDensityLeftHalf;
                // 使用平方函数放大差异
                rightContentScore = Math.Min(10, Math.Pow(ratio - 1, 2) * 20);
            }
            else if (evidence.ContentDensityLeftHalf > evidence.ContentDensityRightHalf * 1.5)
            {
                double ratio = evidence.ContentDensityLeftHalf / evidence.ContentDensityRightHalf;
                // 使用平方函数放大差异
                leftContentScore = Math.Min(10, Math.Pow(ratio - 1, 2) * 20);
            }

            System.Diagnostics.Debug.WriteLine($"右侧内容密度评分: {rightContentScore:F1}, 左侧内容密度评分: {leftContentScore:F1}");

            // 【修改】计算综合得分 - 使用动态权重
            double rtlScore = (rightAlignmentScore * alignmentWeight) +
                             (rightEdgeScore * edgeWeight) +
                             (rightContentScore * contentWeight) +
                             (peakShapeScore * shapeWeight) +
                             (valleyRatioScore * valleyWeight) +
                             (regularityScore * regularityWeight);

            double ltrScore = (leftAlignmentScore * alignmentWeight) +
                             (leftEdgeScore * edgeWeight) +
                             (leftContentScore * contentWeight) +
                             (peakShapeScore * shapeWeight) +
                             (valleyRatioScore * valleyWeight) +
                             (regularityScore * regularityWeight);

            // 对于中文竖排文本，如果直方图结构清晰，适当提升RTL得分
            if (isCJKVerticalText && histogramStructureScore >= 7)
            {
                // 【修改】使用非线性加分
                double bonus = 0;
                if (histogramStructureScore >= 9)
                {
                    bonus = histogramStructureScore * 0.4;
                }
                else if (histogramStructureScore >= 8)
                {
                    bonus = histogramStructureScore * 0.3;
                }
                else
                {
                    bonus = histogramStructureScore * 0.2;
                }

                rtlScore += bonus;
                System.Diagnostics.Debug.WriteLine($"中文竖排文本且直方图结构清晰，RTL得分+{bonus:F1}");
            }

            System.Diagnostics.Debug.WriteLine($"从右到左综合评分: {rtlScore:F1}, 从左到右综合评分: {ltrScore:F1}");

            // 【修改】多级阈值与置信度计算
            double scoreDiffRatio = 0;
            if (ltrScore > 0)
                scoreDiffRatio = rtlScore / ltrScore;
            else if (rtlScore > 0)
                scoreDiffRatio = double.MaxValue;

            double reversedScoreDiffRatio = 0;
            if (rtlScore > 0)
                reversedScoreDiffRatio = ltrScore / rtlScore;
            else if (ltrScore > 0)
                reversedScoreDiffRatio = double.MaxValue;

            // 根据得分差异确定方向和置信度
            if (scoreDiffRatio >= 2.0)
            {
                // 从右到左得分明显更高 (>2.0倍)
                result.HorizontalDirection = TextFlowDirection.RightToLeft;
                result.IsRightToLeft = true;

                // 计算置信度，基于得分差异
                double scoreDiff = rtlScore - ltrScore;
                result.HorizontalConfidence = 85 + (int)Math.Min(10, scoreDiff);

                System.Diagnostics.Debug.WriteLine($"从右到左得分极高(是从左到右的{scoreDiffRatio:F1}倍)，水平方向设为从右到左，置信度: {result.HorizontalConfidence}%");
            }
            else if (scoreDiffRatio >= 1.5)
            {
                // 从右到左得分明显更高 (1.5-2.0倍)
                result.HorizontalDirection = TextFlowDirection.RightToLeft;
                result.IsRightToLeft = true;

                // 计算置信度，基于得分差异
                double scoreDiff = rtlScore - ltrScore;
                result.HorizontalConfidence = 80 + (int)Math.Min(15, scoreDiff);

                System.Diagnostics.Debug.WriteLine($"从右到左得分很高(是从左到右的{scoreDiffRatio:F1}倍)，水平方向设为从右到左，置信度: {result.HorizontalConfidence}%");
            }
            else if (scoreDiffRatio >= 1.2)
            {
                // 从右到左得分明显更高 (1.2-1.5倍)
                result.HorizontalDirection = TextFlowDirection.RightToLeft;
                result.IsRightToLeft = true;

                // 计算置信度，基于得分差异
                double scoreDiff = rtlScore - ltrScore;
                result.HorizontalConfidence = 70 + (int)Math.Min(20, scoreDiff);

                System.Diagnostics.Debug.WriteLine($"从右到左得分较高(是从左到右的{scoreDiffRatio:F1}倍)，水平方向设为从右到左，置信度: {result.HorizontalConfidence}%");
            }
            else if (reversedScoreDiffRatio >= 2.0)
            {
                // 从左到右得分极高 (>2.0倍)
                result.HorizontalDirection = TextFlowDirection.LeftToRight;
                result.IsRightToLeft = false;

                // 计算置信度，基于得分差异
                double scoreDiff = ltrScore - rtlScore;
                result.HorizontalConfidence = 85 + (int)Math.Min(10, scoreDiff);

                System.Diagnostics.Debug.WriteLine($"从左到右得分极高(是从右到左的{reversedScoreDiffRatio:F1}倍)，水平方向设为从左到右，置信度: {result.HorizontalConfidence}%");
            }
            else if (reversedScoreDiffRatio >= 1.5)
            {
                // 从左到右得分很高 (1.5-2.0倍)
                result.HorizontalDirection = TextFlowDirection.LeftToRight;
                result.IsRightToLeft = false;

                // 计算置信度，基于得分差异
                double scoreDiff = ltrScore - rtlScore;
                result.HorizontalConfidence = 80 + (int)Math.Min(15, scoreDiff);

                System.Diagnostics.Debug.WriteLine($"从左到右得分很高(是从右到左的{reversedScoreDiffRatio:F1}倍)，水平方向设为从左到右，置信度: {result.HorizontalConfidence}%");
            }
            else if (reversedScoreDiffRatio >= 1.2)
            {
                // 从左到右得分较高 (1.2-1.5倍)
                result.HorizontalDirection = TextFlowDirection.LeftToRight;
                result.IsRightToLeft = false;

                // 计算置信度，基于得分差异
                double scoreDiff = ltrScore - rtlScore;
                result.HorizontalConfidence = 70 + (int)Math.Min(20, scoreDiff);

                System.Diagnostics.Debug.WriteLine($"从左到右得分较高(是从右到左的{reversedScoreDiffRatio:F1}倍)，水平方向设为从左到右，置信度: {result.HorizontalConfidence}%");
            }
            else if (scoreDiffRatio >= 1.1)
            {
                // 从右到左得分略高 (1.1-1.2倍)
                result.HorizontalDirection = TextFlowDirection.RightToLeft;
                result.IsRightToLeft = true;

                // 计算置信度，基于得分差异
                double scoreDiff = rtlScore - ltrScore;
                result.HorizontalConfidence = 65 + (int)Math.Min(15, scoreDiff * 2);

                System.Diagnostics.Debug.WriteLine($"从右到左得分略高(是从左到右的{scoreDiffRatio:F1}倍)，水平方向设为从右到左，置信度: {result.HorizontalConfidence}%");
            }
            else if (reversedScoreDiffRatio >= 1.1)
            {
                // 从左到右得分略高 (1.1-1.2倍)
                result.HorizontalDirection = TextFlowDirection.LeftToRight;
                result.IsRightToLeft = false;

                // 计算置信度，基于得分差异
                double scoreDiff = ltrScore - rtlScore;
                result.HorizontalConfidence = 65 + (int)Math.Min(15, scoreDiff * 2);

                System.Diagnostics.Debug.WriteLine($"从左到右得分略高(是从右到左的{reversedScoreDiffRatio:F1}倍)，水平方向设为从左到右，置信度: {result.HorizontalConfidence}%");
            }
            else
            {
                // 得分接近，需要进一步分析
                if (isCJKVerticalText && evidence.VerticalPeakCount >= 2)
                {
                    // 中文竖排文本，如果有明显的列结构，倾向于从右到左
                    if (evidence.VerticalPeakValleyMeanRatio > 1.5 && evidence.VerticalPeakRegularity > 0.7)
                    {
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.IsRightToLeft = true;
                        result.HorizontalConfidence = 75;
                        System.Diagnostics.Debug.WriteLine("中文竖排文本，列结构明显，倾向于从右到左，置信度: 75%");
                    }
                    else
                    {
                        // 中文竖排文本，但列结构不明显，仍然倾向于从右到左但置信度较低
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.IsRightToLeft = true;
                        result.HorizontalConfidence = 65;
                        System.Diagnostics.Debug.WriteLine("中文竖排文本，列结构不明显，仍然倾向于从右到左，置信度: 65%");
                    }
                }
                else
                {
                    // 非中文竖排或无明显列结构，根据轻微优势确定
                    if (rtlScore >= ltrScore)
                    {
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.IsRightToLeft = true;
                        result.HorizontalConfidence = 60 + (int)(10 * (rtlScore / Math.Max(1, ltrScore) - 1));
                        System.Diagnostics.Debug.WriteLine($"从右到左略占优势，水平方向设为从右到左，置信度: {result.HorizontalConfidence}%");
                    }
                    else
                    {
                        result.HorizontalDirection = TextFlowDirection.LeftToRight;
                        result.IsRightToLeft = false;
                        result.HorizontalConfidence = 60 + (int)(10 * (ltrScore / Math.Max(1, rtlScore) - 1));
                        System.Diagnostics.Debug.WriteLine($"从左到右略占优势，水平方向设为从左到右，置信度: {result.HorizontalConfidence}%");
                    }
                }
            }

            // 明确结构情况下，额外提升置信度
            if (isDefinitiveStructure)
            {
                result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 5);
                System.Diagnostics.Debug.WriteLine($"检测到明确结构，额外提升置信度至{result.HorizontalConfidence}%");
            }
        }

        /// <summary>
        /// 优化方向检测结果
        /// </summary>
        private void OptimizeDirectionDetection(List<TextCellInfo> cells, TextDirectionResult result)
        {
            System.Diagnostics.Debug.WriteLine("\n=== 方向优化开始 ===");
            System.Diagnostics.Debug.WriteLine($"优化前 - 布局: {(result.IsVerticalLayout ? "竖排" : "横排")}, 置信度: {result.LayoutConfidence}");
            System.Diagnostics.Debug.WriteLine($"主方向: {result.FlowDirection}, 置信度: {result.DirectionConfidence}");

            // 1. 中文竖排文本特殊处理
            bool isCJKVertical = IsCJKText(cells) && result.IsVerticalLayout;
            if (isCJKVertical)
            {
                System.Diagnostics.Debug.WriteLine("检测到中文竖排文本，应用特殊优化规则");

                // 重新提取特征进行深度优化 - 使用统一的特征提取方法
                TextLayoutFeatures features;
                DirectionEvidence evidence;
                ExtractAndIntegrateFeatures(cells, out features, out evidence);

                // 专门针对竖排文本方向的优化
                OptimizeVerticalLayoutDirections(result, features, evidence);

                // 应用高级置信度优化
                ApplyAdvancedConfidenceOptimization(result, evidence, cells);

                // 【新增】分析直方图峰值分布模式
                if (features.VerticalPeakCount >= 2)
                {
                    // 分析峰值间距变化趋势
                    AnalyzePeakDistributionPattern(features, result);
                }

                // 【新增】中文竖排文本特殊处理
                if (result.IsVerticalLayout)
                {
                    // 计算竖排文本占比
                    double verticalTextRatio = (double)evidence.VerticalTextCount /
                        (evidence.VerticalTextCount + evidence.HorizontalTextCount + evidence.SquareTextCount);

                    System.Diagnostics.Debug.WriteLine($"竖排文本占比: {verticalTextRatio:P2}");

                    // 如果竖排文本占比很高，进一步增强竖排布局置信度
                    if (verticalTextRatio > 0.8)
                    {
                        result.LayoutConfidence = Math.Max(result.LayoutConfidence, 90);
                        System.Diagnostics.Debug.WriteLine($"竖排文本占比极高({verticalTextRatio:P2}) => 增强竖排布局置信度至{result.LayoutConfidence}%");

                        // 对于高竖排占比的中文文本，强制设置为从右到左
                        // 这是基于传统中文竖排排版规则
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.IsRightToLeft = true;
                        result.HorizontalConfidence = 90;
                        System.Diagnostics.Debug.WriteLine("高竖排占比中文文本 => 强制设置为从右到左，置信度调整为90%");
                    }
                    else
                    {
                        // 即使竖排文本占比不是特别高，对于中文竖排文本也默认从右到左
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.IsRightToLeft = true;
                        result.HorizontalConfidence = 80;
                        System.Diagnostics.Debug.WriteLine("中文竖排文本 => 默认设置为从右到左，置信度调整为80%");
                    }

                    // 分析内容分布 - 中文竖排文本通常右侧内容更多
                    if (evidence.ContentDensityRightHalf > evidence.ContentDensityLeftHalf * 1.2)
                    {
                        // 右半页内容明显多于左半页，强烈暗示从右到左
                        if (result.HorizontalDirection != TextFlowDirection.RightToLeft || result.HorizontalConfidence < 85)
                        {
                            result.HorizontalDirection = TextFlowDirection.RightToLeft;
                            result.IsRightToLeft = true;
                            result.HorizontalConfidence = Math.Max(result.HorizontalConfidence, 85);
                            System.Diagnostics.Debug.WriteLine($"中文竖排文本右侧内容密度更高({evidence.ContentDensityRightHalf:F2} vs {evidence.ContentDensityLeftHalf:F2}) => 强烈暗示从右到左，置信度提升至{result.HorizontalConfidence}%");
                        }
                    }

                    // 分析边缘对齐 - 中文竖排文本通常右边缘对齐更整齐
                    if (evidence.RightAlignedCount > evidence.LeftAlignedCount && evidence.RightAlignedCount >= 2)
                    {
                        // 右对齐文本块多于左对齐，暗示从右到左
                        if (result.HorizontalDirection == TextFlowDirection.RightToLeft)
                        {
                            // 增强已有判断
                            result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 5);
                            System.Diagnostics.Debug.WriteLine($"中文竖排文本右对齐文本块较多({evidence.RightAlignedCount} vs {evidence.LeftAlignedCount}) => 增强从右到左置信度至{result.HorizontalConfidence}%");
                        }
                        else if (result.HorizontalConfidence < 75)
                        {
                            // 修正方向判断
                            result.HorizontalDirection = TextFlowDirection.RightToLeft;
                            result.IsRightToLeft = true;
                            result.HorizontalConfidence = 75;
                            System.Diagnostics.Debug.WriteLine($"中文竖排文本右对齐文本块较多({evidence.RightAlignedCount} vs {evidence.LeftAlignedCount}) => 修正为从右到左，置信度设为75%");
                        }
                    }

                    // 检查列间距规律性 - 中文竖排文本通常有较高的列间距规律性
                    if (evidence.VerticalPeakRegularity > 0.8 && evidence.VerticalPeakCount >= 3)
                    {
                        // 列间距规律性高，增强竖排布局置信度
                        result.LayoutConfidence = Math.Max(result.LayoutConfidence, 85);
                        System.Diagnostics.Debug.WriteLine($"列间距规律性高({evidence.VerticalPeakRegularity:F2}) => 增强竖排布局置信度至{result.LayoutConfidence}%");

                        // 对于规律性高的多列结构，增强水平方向置信度
                        if (result.HorizontalDirection == TextFlowDirection.RightToLeft)
                        {
                            result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 5);
                            System.Diagnostics.Debug.WriteLine($"规律性高的多列结构 => 增强从右到左置信度至{result.HorizontalConfidence}%");
                        }
                    }
                }

                // 确保置信度不低于最低阈值
                EnsureBasicLayoutConsistency(result);

                // 最后一致性调整
                AdjustConfidenceBasedOnConsistency(result, evidence);
            }
            else
            {
                // 非中文竖排文本的通用优化
                // ...原有代码保持不变...
            }
        }

        /// <summary>
        /// 【新增】分析直方图峰值分布模式，用于优化方向判断
        /// </summary>
        /// <param name="features">文本布局特征</param>
        /// <param name="result">方向结果</param>
        private void AnalyzePeakDistributionPattern(TextLayoutFeatures features, TextDirectionResult result)
        {
            if (features.VerticalPeakIndices.Count < 2)
                return;

            System.Diagnostics.Debug.WriteLine("\n--- 直方图峰值分布模式分析 ---");

            // 分析峰值间距的变化趋势
            List<double> peakSpacings = new List<double>();
            for (int i = 1; i < features.VerticalPeakIndices.Count; i++)
            {
                int currentPeakIndex = features.VerticalPeakIndices[i];
                int previousPeakIndex = features.VerticalPeakIndices[i - 1];
                double spacing = (currentPeakIndex - previousPeakIndex) * features.VerticalBinSize;
                peakSpacings.Add(spacing);
            }

            // 计算峰值间距统计特征
            double avgSpacing = peakSpacings.Average();
            double minSpacing = peakSpacings.Min();
            double maxSpacing = peakSpacings.Max();
            double spacingRange = maxSpacing - minSpacing;

            System.Diagnostics.Debug.WriteLine($"峰值间距: 平均={avgSpacing:F2}, 最小={minSpacing:F2}, 最大={maxSpacing:F2}, 范围={spacingRange:F2}");

            // 计算峰值间距的变化趋势
            bool isSpacingIncreasing = false;
            bool isSpacingDecreasing = false;
            bool isSpacingConstant = false;

            if (peakSpacings.Count >= 2)
            {
                int increasingCount = 0;
                int decreasingCount = 0;
                for (int i = 1; i < peakSpacings.Count; i++)
                {
                    if (peakSpacings[i] > peakSpacings[i - 1] * 1.1)
                        increasingCount++;
                    else if (peakSpacings[i] < peakSpacings[i - 1] * 0.9)
                        decreasingCount++;
                }

                double increasingRatio = (double)increasingCount / (peakSpacings.Count - 1);
                double decreasingRatio = (double)decreasingCount / (peakSpacings.Count - 1);

                isSpacingIncreasing = increasingRatio > 0.6;
                isSpacingDecreasing = decreasingRatio > 0.6;
                isSpacingConstant = (increasingCount + decreasingCount) < (peakSpacings.Count - 1) * 0.4;

                System.Diagnostics.Debug.WriteLine($"间距变化趋势: 递增={isSpacingIncreasing}, 递减={isSpacingDecreasing}, 恒定={isSpacingConstant}");
            }

            // 分析峰值高度的变化趋势
            List<int> peakHeights = new List<int>();
            foreach (int peakIndex in features.VerticalPeakIndices)
            {
                if (peakIndex < features.VerticalHistogram.Count)
                {
                    peakHeights.Add(features.VerticalHistogram[peakIndex]);
                }
            }

            bool isHeightIncreasing = false;
            bool isHeightDecreasing = false;
            bool isHeightConstant = false;

            if (peakHeights.Count >= 2)
            {
                int increasingCount = 0;
                int decreasingCount = 0;
                for (int i = 1; i < peakHeights.Count; i++)
                {
                    if (peakHeights[i] > peakHeights[i - 1] * 1.1)
                        increasingCount++;
                    else if (peakHeights[i] < peakHeights[i - 1] * 0.9)
                        decreasingCount++;
                }

                double increasingRatio = (double)increasingCount / (peakHeights.Count - 1);
                double decreasingRatio = (double)decreasingCount / (peakHeights.Count - 1);

                isHeightIncreasing = increasingRatio > 0.6;
                isHeightDecreasing = decreasingRatio > 0.6;
                isHeightConstant = (increasingCount + decreasingCount) < (peakHeights.Count - 1) * 0.4;

                System.Diagnostics.Debug.WriteLine($"高度变化趋势: 递增={isHeightIncreasing}, 递减={isHeightDecreasing}, 恒定={isHeightConstant}");
            }

            // 【新增】分析峰值位置分布 - 检查是否集中在右侧或左侧
            bool peaksConcentratedOnRight = false;
            bool peaksConcentratedOnLeft = false;

            if (features.VerticalPeakIndices.Count >= 2 && features.VerticalHistogram.Count > 0)
            {
                int histogramMidPoint = features.VerticalHistogram.Count / 2;
                int peaksOnRightHalf = 0;
                int peaksOnLeftHalf = 0;

                foreach (int peakIndex in features.VerticalPeakIndices)
                {
                    if (peakIndex > histogramMidPoint)
                        peaksOnRightHalf++;
                    else
                        peaksOnLeftHalf++;
                }

                double rightRatio = (double)peaksOnRightHalf / features.VerticalPeakIndices.Count;
                double leftRatio = (double)peaksOnLeftHalf / features.VerticalPeakIndices.Count;

                peaksConcentratedOnRight = rightRatio > 0.7;
                peaksConcentratedOnLeft = leftRatio > 0.7;

                System.Diagnostics.Debug.WriteLine($"峰值位置分布: 右侧集中={peaksConcentratedOnRight} ({rightRatio:P0}), 左侧集中={peaksConcentratedOnLeft} ({leftRatio:P0})");
            }

            // 【新增】分析峰值形状质量和峰谷比
            double peakQualityScore = features.VerticalPeakShapeQuality * features.VerticalPeakValleyMeanRatio;
            bool hasHighQualityPeaks = peakQualityScore > 1.0;

            System.Diagnostics.Debug.WriteLine($"峰值质量评分: {peakQualityScore:F2}, 高质量峰值={hasHighQualityPeaks}");

            // 根据峰值分布模式调整方向置信度
            if (result.IsVerticalLayout)
            {
                // 针对竖排布局的水平方向判断（从右到左还是从左到右）

                // 1. 间距从大到小 + 高度递减 => 强烈暗示从右到左
                if (isSpacingDecreasing && isHeightDecreasing)
                {
                    // 从右到左的强烈证据 - 列间距和列高度都从右到左递减
                    // 这是传统中文竖排排版的典型特征
                    if (result.HorizontalDirection != TextFlowDirection.RightToLeft || result.HorizontalConfidence < 85)
                    {
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.IsRightToLeft = true;
                        result.HorizontalConfidence = 90;
                        System.Diagnostics.Debug.WriteLine("峰值分布模式分析: 列间距和高度从右到左递减 => 强烈暗示从右到左, 置信度提升至90%");
                    }
                }
                // 【新增】1.1 峰值集中在右侧 + 高质量峰值 => 强烈暗示从右到左
                else if (peaksConcentratedOnRight && hasHighQualityPeaks)
                {
                    // 传统中文竖排文本通常从右到左，峰值集中在右侧是重要特征
                    if (result.HorizontalDirection != TextFlowDirection.RightToLeft || result.HorizontalConfidence < 85)
                    {
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.IsRightToLeft = true;
                        result.HorizontalConfidence = 88;
                        System.Diagnostics.Debug.WriteLine("峰值分布模式分析: 高质量峰值集中在右侧 => 强烈暗示从右到左, 置信度提升至88%");
                    }
                }
                // 2. 间距从小到大 + 高度递增 => 暗示从左到右
                else if (isSpacingIncreasing && isHeightIncreasing)
                {
                    // 从左到右的强烈证据
                    if (result.HorizontalDirection != TextFlowDirection.LeftToRight || result.HorizontalConfidence < 80)
                    {
                        result.HorizontalDirection = TextFlowDirection.LeftToRight;
                        result.IsRightToLeft = false;
                        result.HorizontalConfidence = 80;
                        System.Diagnostics.Debug.WriteLine("峰值分布模式分析: 列间距和高度从左到右递增 => 暗示从左到右, 置信度提升至80%");
                    }
                }
                // 【新增】2.1 峰值集中在左侧 + 高质量峰值 => 暗示从左到右
                else if (peaksConcentratedOnLeft && hasHighQualityPeaks)
                {
                    // 非传统排版的竖排文本，峰值集中在左侧
                    if (result.HorizontalDirection != TextFlowDirection.LeftToRight || result.HorizontalConfidence < 80)
                    {
                        result.HorizontalDirection = TextFlowDirection.LeftToRight;
                        result.IsRightToLeft = false;
                        result.HorizontalConfidence = 80;
                        System.Diagnostics.Debug.WriteLine("峰值分布模式分析: 高质量峰值集中在左侧 => 暗示从左到右, 置信度提升至80%");
                    }
                }
                // 3. 间距均匀 + 高度相近 => 常规排版，增强已有判断
                else if (isSpacingConstant && isHeightConstant && spacingRange / avgSpacing < 0.3)
                {
                    // 列间距和高度均匀 - 增强已有判断的置信度
                    if (result.HorizontalConfidence < 85)
                    {
                        result.HorizontalConfidence = Math.Min(85, result.HorizontalConfidence + 10);
                        System.Diagnostics.Debug.WriteLine($"峰值分布模式分析: 列间距和高度均匀稳定 => 增强已有判断置信度至{result.HorizontalConfidence}%");
                    }

                    // 【新增】对于均匀分布的情况，如果峰值质量高，默认倾向于传统中文竖排（从右到左）
                    if (hasHighQualityPeaks && result.HorizontalDirection == TextFlowDirection.LeftToRight && result.HorizontalConfidence < 75)
                    {
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.IsRightToLeft = true;
                        result.HorizontalConfidence = 75;
                        System.Diagnostics.Debug.WriteLine("峰值分布模式分析: 均匀分布高质量峰值 => 倾向于传统中文竖排(从右到左), 置信度调整至75%");
                    }
                }
                // 4. 仅间距变化有明显模式时的弱判断
                else if (isSpacingDecreasing && features.VerticalPeakCount >= 3)
                {
                    // 仅列间距有规律减小，倾向于从右到左
                    if (result.HorizontalDirection == TextFlowDirection.RightToLeft && result.HorizontalConfidence < 80)
                    {
                        result.HorizontalConfidence = Math.Min(80, result.HorizontalConfidence + 8);
                        System.Diagnostics.Debug.WriteLine($"峰值分布模式分析: 列间距从右到左递减 => 增强从右到左置信度至{result.HorizontalConfidence}%");
                    }
                    else if (result.HorizontalDirection == TextFlowDirection.LeftToRight && result.HorizontalConfidence < 75)
                    {
                        // 当前判断与峰值分布模式不符，降低置信度
                        result.HorizontalConfidence = 70;
                        System.Diagnostics.Debug.WriteLine("峰值分布模式分析: 列间距模式与当前从左到右判断不符 => 降低置信度至70%");
                    }
                }

                // 【新增】5. 针对中文竖排文本的特殊处理 - 基于峰值质量和峰值数量
                if (features.VerticalPeakCount >= 3 && features.VerticalPeakShapeQuality > 0.7 && features.VerticalPeakValleyMeanRatio > 2.0)
                {
                    // 高质量峰值 + 多列 => 强烈暗示传统中文竖排文本
                    // 传统中文竖排文本几乎总是从右到左
                    if (result.HorizontalDirection != TextFlowDirection.RightToLeft || result.HorizontalConfidence < 85)
                    {
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.IsRightToLeft = true;
                        result.HorizontalConfidence = 85;
                        System.Diagnostics.Debug.WriteLine("峰值分布模式分析: 多列高质量峰值特征 => 强烈暗示传统中文竖排(从右到左), 置信度提升至85%");
                    }
                }

                // 【新增】6. 基于内容密度和峰值质量的综合判断
                if (features.ContentDensityRightHalf > features.ContentDensityLeftHalf * 1.3 && hasHighQualityPeaks)
                {
                    // 右侧内容密度更高 + 高质量峰值 => 从右到左的强烈证据
                    if (result.HorizontalDirection != TextFlowDirection.RightToLeft || result.HorizontalConfidence < 88)
                    {
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.IsRightToLeft = true;
                        result.HorizontalConfidence = 88;
                        System.Diagnostics.Debug.WriteLine("峰值分布模式分析: 右侧内容密度更高 + 高质量峰值 => 强烈暗示从右到左, 置信度提升至88%");
                    }
                }
                else if (features.ContentDensityLeftHalf > features.ContentDensityRightHalf * 1.3 && hasHighQualityPeaks)
                {
                    // 左侧内容密度更高 + 高质量峰值 => 从左到右的强烈证据
                    if (result.HorizontalDirection != TextFlowDirection.LeftToRight || result.HorizontalConfidence < 85)
                    {
                        result.HorizontalDirection = TextFlowDirection.LeftToRight;
                        result.IsRightToLeft = false;
                        result.HorizontalConfidence = 85;
                        System.Diagnostics.Debug.WriteLine("峰值分布模式分析: 左侧内容密度更高 + 高质量峰值 => 强烈暗示从左到右, 置信度提升至85%");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"峰值分布模式分析后: 水平方向={result.HorizontalDirection}, 置信度={result.HorizontalConfidence}%");
            }
        }

        /// <summary>
        /// 确保方向检测结果的置信度不低于最低阈值
        /// </summary>
        private void EnsureBasicLayoutConsistency(TextDirectionResult result)
        {
            // 确保最低置信度
            if (result.LayoutConfidence < 60)
            {
                result.LayoutConfidence = 60;
            }

            if (result.DirectionConfidence < 60)
            {
                result.DirectionConfidence = 60;
            }

            // 【新增】确保垂直方向置信度不为0
            if (result.VerticalConfidence <= 0)
            {
                result.VerticalConfidence = Math.Max(60, result.DirectionConfidence);
                System.Diagnostics.Debug.WriteLine($"垂直方向置信度为0，设置为: {result.VerticalConfidence}%");
            }

            // 【新增】确保水平方向置信度不为0
            if (result.HorizontalConfidence <= 0)
            {
                result.HorizontalConfidence = Math.Max(60, result.DirectionConfidence);
                System.Diagnostics.Debug.WriteLine($"水平方向置信度为0，设置为: {result.HorizontalConfidence}%");
            }

            // 确保结果的一致性
            // 【优化】如果是竖排布局，确保流向是TopToBottom或BottomToTop，并确保垂直方向与流向一致
            if (result.IsVerticalLayout)
            {
                if (result.FlowDirection != TextFlowDirection.TopToBottom &&
                    result.FlowDirection != TextFlowDirection.BottomToTop)
                {
                    result.FlowDirection = TextFlowDirection.TopToBottom; // 默认从上到下
                    result.DirectionConfidence = 70;
                    System.Diagnostics.Debug.WriteLine("竖排布局方向不一致，调整为从上到下");
                }

                // 【新增】确保垂直方向与流向一致
                if (result.VerticalDirection != result.FlowDirection)
                {
                    result.VerticalDirection = result.FlowDirection;
                    System.Diagnostics.Debug.WriteLine($"垂直方向与流向不一致，调整垂直方向为: {result.FlowDirection}");
                }
            }
            // 【优化】如果是横排布局，确保流向是LeftToRight或RightToLeft，并确保水平方向与流向一致
            else
            {
                if (result.FlowDirection != TextFlowDirection.LeftToRight &&
                    result.FlowDirection != TextFlowDirection.RightToLeft)
                {
                    result.FlowDirection = TextFlowDirection.LeftToRight; // 默认从左到右
                    result.DirectionConfidence = 70;
                    System.Diagnostics.Debug.WriteLine("横排布局方向不一致，调整为从左到右");
                }

                // 【新增】确保水平方向与流向一致
                if (result.HorizontalDirection != result.FlowDirection)
                {
                    result.HorizontalDirection = result.FlowDirection;
                    System.Diagnostics.Debug.WriteLine($"水平方向与流向不一致，调整水平方向为: {result.FlowDirection}");
                }

                // 【新增】确保IsRightToLeft标志与水平方向一致
                bool shouldBeRTL = (result.HorizontalDirection == TextFlowDirection.RightToLeft);
                if (result.IsRightToLeft != shouldBeRTL)
                {
                    result.IsRightToLeft = shouldBeRTL;
                    System.Diagnostics.Debug.WriteLine($"RTL标志与水平方向不一致，调整RTL标志为: {shouldBeRTL}");
                }
            }

            // 【优化】对于特殊情况的处理
            // 例如：如果检测到文本块数量很少，降低置信度
            if (cells != null && cells.Count < 5)
            {
                result.LayoutConfidence = Math.Min(result.LayoutConfidence, 70);
                result.DirectionConfidence = Math.Min(result.DirectionConfidence, 70);
                result.HorizontalConfidence = Math.Min(result.HorizontalConfidence, 70);
                result.VerticalConfidence = Math.Min(result.VerticalConfidence, 70);
                System.Diagnostics.Debug.WriteLine($"文本块数量较少({cells.Count})，降低所有置信度至最高70%");
            }

            // 【新增】输出最终一致性检查结果
            System.Diagnostics.Debug.WriteLine("\n--- 最终一致性检查结果 ---");
            System.Diagnostics.Debug.WriteLine($"布局类型: {(result.IsVerticalLayout ? "竖排" : "横排")}, 置信度: {result.LayoutConfidence}%");
            System.Diagnostics.Debug.WriteLine($"主流向: {result.FlowDirection}, 置信度: {result.DirectionConfidence}%");
            System.Diagnostics.Debug.WriteLine($"水平方向: {result.HorizontalDirection}, 置信度: {result.HorizontalConfidence}%");
            System.Diagnostics.Debug.WriteLine($"垂直方向: {result.VerticalDirection}, 置信度: {result.VerticalConfidence}%");
            System.Diagnostics.Debug.WriteLine($"是否从右到左: {result.IsRightToLeft}");
        }

        /// <summary>
        /// 【新增】解决形状特征与直方图特征矛盾的问题
        /// </summary>
        private void ResolveFeatureContradictions(TextDirectionResult result, DirectionEvidence evidence)
        {
            System.Diagnostics.Debug.WriteLine("\n=== 解决特征矛盾 ===");

            // 检查形状特征与布局判断是否矛盾
            bool shapeContradiction = false;

            // 1. 检查横排布局但垂直文本块显著多于水平文本块的情况
            if (!result.IsVerticalLayout &&
                evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5 &&
                evidence.VerticalTextCount >= 5)
            {
                shapeContradiction = true;
                System.Diagnostics.Debug.WriteLine($"矛盾检测: 横排布局但垂直文本块显著多({evidence.VerticalTextCount} vs {evidence.HorizontalTextCount})");
            }

            // 2. 检查竖排布局但水平文本块显著多于垂直文本块的情况
            else if (result.IsVerticalLayout &&
                     evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.5 &&
                     evidence.HorizontalTextCount >= 5)
            {
                shapeContradiction = true;
                System.Diagnostics.Debug.WriteLine($"矛盾检测: 竖排布局但水平文本块显著多({evidence.HorizontalTextCount} vs {evidence.VerticalTextCount})");
            }

            // 【新增】计算形状特征矛盾的严重程度
            int shapeContradictionSeverity = 0;
            if (shapeContradiction)
            {
                double ratio = !result.IsVerticalLayout ?
                    (double)evidence.VerticalTextCount / Math.Max(1, evidence.HorizontalTextCount) :
                    (double)evidence.HorizontalTextCount / Math.Max(1, evidence.VerticalTextCount);

                if (ratio > 5.0) shapeContradictionSeverity = 3; // 非常严重
                else if (ratio > 3.0) shapeContradictionSeverity = 2; // 严重
                else shapeContradictionSeverity = 1; // 轻微

                System.Diagnostics.Debug.WriteLine($"形状特征矛盾严重程度: {shapeContradictionSeverity}/3 (比例: {ratio:F1})");
            }

            // 3. 检查直方图特征与布局判断是否矛盾
            bool histogramContradiction = false;
            int contradictionSeverity = 0; // 矛盾严重程度，0-3

            // 【改进】更精细地判断峰值数量矛盾
            if (!result.IsVerticalLayout)
            {
                // 横排布局但垂直峰值数量明显多于水平峰值数量
                if (evidence.VerticalPeakCount > evidence.HorizontalPeakCount)
                {
                    histogramContradiction = true;

                    // 计算矛盾严重程度
                    if (evidence.VerticalPeakCount > evidence.HorizontalPeakCount * 2)
                    {
                        contradictionSeverity = 3; // 非常严重
                        System.Diagnostics.Debug.WriteLine($"矛盾检测: 横排布局但垂直峰值数量极度多于水平峰值({evidence.VerticalPeakCount} vs {evidence.HorizontalPeakCount})");
                    }
                    else if (evidence.VerticalPeakCount > evidence.HorizontalPeakCount * 1.5)
                    {
                        contradictionSeverity = 2; // 严重
                        System.Diagnostics.Debug.WriteLine($"矛盾检测: 横排布局但垂直峰值数量显著多于水平峰值({evidence.VerticalPeakCount} vs {evidence.HorizontalPeakCount})");
                    }
                    else
                    {
                        contradictionSeverity = 1; // 轻微
                        System.Diagnostics.Debug.WriteLine($"矛盾检测: 横排布局但垂直峰值数量略多于水平峰值({evidence.VerticalPeakCount} vs {evidence.HorizontalPeakCount})");
                    }
                }

                // 【新增】检查峰值质量差异
                if (evidence.VerticalPeakShapeQuality > evidence.HorizontalPeakShapeQuality * 1.5)
                {
                    histogramContradiction = true;
                    contradictionSeverity = Math.Max(contradictionSeverity, 2);
                    System.Diagnostics.Debug.WriteLine($"矛盾检测: 横排布局但垂直峰值质量显著高于水平峰值({evidence.VerticalPeakShapeQuality:F2} vs {evidence.HorizontalPeakShapeQuality:F2})");
                }

                // 【新增】检查结构强度差异
                if (evidence.VerticalStructureStrength > evidence.HorizontalStructureStrength * 1.5 &&
                    evidence.VerticalStructureStrength > 0.7)
                {
                    histogramContradiction = true;
                    contradictionSeverity = Math.Max(contradictionSeverity, 2);
                    System.Diagnostics.Debug.WriteLine($"矛盾检测: 横排布局但垂直结构强度显著高于水平结构({evidence.VerticalStructureStrength:F2} vs {evidence.HorizontalStructureStrength:F2})");
                }
            }
            else // 竖排布局
            {
                // 竖排布局但水平峰值数量明显多于垂直峰值数量
                if (evidence.HorizontalPeakCount > evidence.VerticalPeakCount)
                {
                    histogramContradiction = true;

                    // 计算矛盾严重程度
                    if (evidence.HorizontalPeakCount > evidence.VerticalPeakCount * 2)
                    {
                        contradictionSeverity = 3; // 非常严重
                        System.Diagnostics.Debug.WriteLine($"矛盾检测: 竖排布局但水平峰值数量极度多于垂直峰值({evidence.HorizontalPeakCount} vs {evidence.VerticalPeakCount})");
                    }
                    else if (evidence.HorizontalPeakCount > evidence.VerticalPeakCount * 1.5)
                    {
                        contradictionSeverity = 2; // 严重
                        System.Diagnostics.Debug.WriteLine($"矛盾检测: 竖排布局但水平峰值数量显著多于垂直峰值({evidence.HorizontalPeakCount} vs {evidence.VerticalPeakCount})");
                    }
                    else
                    {
                        contradictionSeverity = 1; // 轻微
                        System.Diagnostics.Debug.WriteLine($"矛盾检测: 竖排布局但水平峰值数量略多于垂直峰值({evidence.HorizontalPeakCount} vs {evidence.VerticalPeakCount})");
                    }
                }

                // 【新增】检查峰值质量差异
                if (evidence.HorizontalPeakShapeQuality > evidence.VerticalPeakShapeQuality * 1.5)
                {
                    histogramContradiction = true;
                    contradictionSeverity = Math.Max(contradictionSeverity, 2);
                    System.Diagnostics.Debug.WriteLine($"矛盾检测: 竖排布局但水平峰值质量显著高于垂直峰值({evidence.HorizontalPeakShapeQuality:F2} vs {evidence.VerticalPeakShapeQuality:F2})");
                }

                // 【新增】检查结构强度差异
                if (evidence.HorizontalStructureStrength > evidence.VerticalStructureStrength * 1.5 &&
                    evidence.HorizontalStructureStrength > 0.7)
                {
                    histogramContradiction = true;
                    contradictionSeverity = Math.Max(contradictionSeverity, 2);
                    System.Diagnostics.Debug.WriteLine($"矛盾检测: 竖排布局但水平结构强度显著高于垂直结构({evidence.HorizontalStructureStrength:F2} vs {evidence.VerticalStructureStrength:F2})");
                }
            }

            // 4. 检查行列结构与布局判断是否矛盾
            bool rowColumnContradiction = false;
            int rowColumnContradictionSeverity = 0;

            // 横排布局但列数明显多于行数
            if (!result.IsVerticalLayout &&
                evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows * 1.5 &&
                evidence.VerticalAlignedColumns >= 3)
            {
                rowColumnContradiction = true;
                double ratio = (double)evidence.VerticalAlignedColumns / Math.Max(1, evidence.HorizontalAlignedRows);
                if (ratio > 3.0) rowColumnContradictionSeverity = 2;
                else rowColumnContradictionSeverity = 1;

                System.Diagnostics.Debug.WriteLine($"矛盾检测: 横排布局但列数明显多于行数({evidence.VerticalAlignedColumns} vs {evidence.HorizontalAlignedRows})");
            }

            // 竖排布局但行数明显多于列数
            else if (result.IsVerticalLayout &&
                     evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns * 1.5 &&
                     evidence.HorizontalAlignedRows >= 3)
            {
                rowColumnContradiction = true;
                double ratio = (double)evidence.HorizontalAlignedRows / Math.Max(1, evidence.VerticalAlignedColumns);
                if (ratio > 3.0) rowColumnContradictionSeverity = 2;
                else rowColumnContradictionSeverity = 1;

                System.Diagnostics.Debug.WriteLine($"矛盾检测: 竖排布局但行数明显多于列数({evidence.HorizontalAlignedRows} vs {evidence.VerticalAlignedColumns})");
            }

            // 【新增】检查形状特征与直方图特征是否相互支持但与布局判断矛盾
            bool combinedContradiction = false;
            if (shapeContradiction && histogramContradiction)
            {
                // 如果形状特征和直方图特征都与布局判断矛盾，且它们相互支持
                bool shapeSupportHorizontal = evidence.HorizontalTextCount > evidence.VerticalTextCount;
                bool histogramSupportHorizontal = evidence.HorizontalPeakCount > evidence.VerticalPeakCount ||
                                                 evidence.HorizontalStructureStrength > evidence.VerticalStructureStrength;

                if ((shapeSupportHorizontal && histogramSupportHorizontal && result.IsVerticalLayout) ||
                    (!shapeSupportHorizontal && !histogramSupportHorizontal && !result.IsVerticalLayout))
                {
                    combinedContradiction = true;
                    System.Diagnostics.Debug.WriteLine("严重矛盾: 形状特征和直方图特征相互支持但与布局判断矛盾");
                }
            }

            // 5. 根据矛盾情况进行调整
            if (shapeContradiction || histogramContradiction || rowColumnContradiction)
            {
                // 计算矛盾程度
                int contradictionLevel = 0;
                if (shapeContradiction) contradictionLevel++;
                if (histogramContradiction) contradictionLevel++;
                if (rowColumnContradiction) contradictionLevel++;

                // 【改进】考虑矛盾严重程度
                int effectiveContradictionLevel = contradictionLevel;
                if (shapeContradiction && shapeContradictionSeverity >= 2)
                {
                    effectiveContradictionLevel++; // 严重的形状特征矛盾增加矛盾级别
                }
                if (histogramContradiction && contradictionSeverity >= 2)
                {
                    effectiveContradictionLevel++; // 严重的直方图矛盾增加矛盾级别
                }
                if (rowColumnContradiction && rowColumnContradictionSeverity >= 2)
                {
                    effectiveContradictionLevel++; // 严重的行列矛盾增加矛盾级别
                }
                if (combinedContradiction)
                {
                    effectiveContradictionLevel += 2; // 形状和直方图特征相互支持但与布局矛盾，大幅增加矛盾级别
                }

                System.Diagnostics.Debug.WriteLine($"矛盾级别: {contradictionLevel}/3, 有效矛盾级别: {effectiveContradictionLevel}/7");

                // 【改进】根据矛盾级别和严重程度调整置信度
                if (effectiveContradictionLevel == 1)
                {
                    // 轻微矛盾
                    int confidenceReduction = 5;
                    System.Diagnostics.Debug.WriteLine($"轻微矛盾: 略微降低置信度至{result.LayoutConfidence - confidenceReduction}%");
                    result.LayoutConfidence -= confidenceReduction;
                }
                else if (effectiveContradictionLevel == 2)
                {
                    // 中度矛盾
                    int confidenceReduction = 10;
                    System.Diagnostics.Debug.WriteLine($"中度矛盾: 降低置信度至{result.LayoutConfidence - confidenceReduction}%");
                    result.LayoutConfidence -= confidenceReduction;
                }
                else if (effectiveContradictionLevel == 3)
                {
                    // 严重矛盾
                    int confidenceReduction = 20;
                    System.Diagnostics.Debug.WriteLine($"严重矛盾: 显著降低置信度至{result.LayoutConfidence - confidenceReduction}%");
                    result.LayoutConfidence -= confidenceReduction;
                }
                else if (effectiveContradictionLevel >= 4)
                {
                    // 极度严重矛盾，考虑调整布局类型
                    System.Diagnostics.Debug.WriteLine($"极度严重矛盾: 考虑翻转布局判断");

                    // 如果当前置信度不是特别高，则翻转布局判断
                    if (result.LayoutConfidence < 85)
                    {
                        // 【新增】形状特征与直方图特征一致性检查
                        bool shouldFlipLayout = false;

                        // 当前是横排布局，但形状和直方图特征都支持竖排
                        if (!result.IsVerticalLayout &&
                            evidence.VerticalTextCount > evidence.HorizontalTextCount &&
                            (evidence.VerticalPeakCount > evidence.HorizontalPeakCount ||
                             evidence.VerticalStructureStrength > evidence.HorizontalStructureStrength))
                        {
                            shouldFlipLayout = true;
                        }
                        // 当前是竖排布局，但形状和直方图特征都支持横排
                        else if (result.IsVerticalLayout &&
                                evidence.HorizontalTextCount > evidence.VerticalTextCount &&
                                (evidence.HorizontalPeakCount > evidence.VerticalPeakCount ||
                                 evidence.HorizontalStructureStrength > evidence.VerticalStructureStrength))
                        {
                            shouldFlipLayout = true;
                        }

                        if (shouldFlipLayout)
                        {
                            // 翻转布局判断
                            result.IsVerticalLayout = !result.IsVerticalLayout;

                            // 重新计算置信度，但不要太高
                            result.LayoutConfidence = Math.Min(70, 40 + effectiveContradictionLevel * 5);

                            System.Diagnostics.Debug.WriteLine($"布局判断已翻转为: {(result.IsVerticalLayout ? "竖排" : "横排")}, 新置信度: {result.LayoutConfidence}%");

                            // 确保方向一致性
                            EnsureBasicLayoutConsistency(result);
                        }
                        else
                        {
                            // 如果不翻转，则大幅降低置信度
                            result.LayoutConfidence = Math.Max(10, result.LayoutConfidence - 40);
                            System.Diagnostics.Debug.WriteLine($"矛盾严重但证据不足以翻转布局，大幅降低置信度至{result.LayoutConfidence}%");
                        }
                    }
                    else
                    {
                        // 置信度很高，仅降低置信度
                        result.LayoutConfidence -= 30;
                        System.Diagnostics.Debug.WriteLine($"矛盾严重但原置信度很高，降低置信度至{result.LayoutConfidence}%");
                    }
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("未检测到特征矛盾");
            }
        }

        /// <summary>
        /// 【新增】统一特征提取和整合方法 - 减少重复计算并提高特征整合效率
        /// </summary>
        /// <param name="cells">文本单元格列表</param>
        /// <param name="features">输出参数：提取的文本布局特征</param>
        /// <param name="evidence">输出参数：提取的方向证据</param>
        private void ExtractAndIntegrateFeatures(List<TextCellInfo> cells, out TextLayoutFeatures features, out DirectionEvidence evidence)
        {
            System.Diagnostics.Debug.WriteLine("\n=== 开始统一特征提取流程 ===");

            // 1. 提取基础特征
            features = ExtractTextFeatures(cells);

            // 2. 确保TotalTextBlocks被正确设置
            features.TotalTextBlocks = cells.Count;
            features.ValidTextBlocks = cells.Count(c => c.location != null && c.location.width > 0 && c.location.height > 0);
            System.Diagnostics.Debug.WriteLine($"有效文本块: {features.ValidTextBlocks}/{features.TotalTextBlocks}");

            // 3. 提取页面边界特征 - 确保在其他特征提取前完成
            ExtractPageBoundaryFeatures(cells, features);
            System.Diagnostics.Debug.WriteLine("已提取页面边界特征");

            // 4. 提取形状特征
            ExtractShapeFeatures(cells, features);
            System.Diagnostics.Debug.WriteLine($"已提取形状特征: 垂直文本={features.VerticalTextCount}, 水平文本={features.HorizontalTextCount}, 正方形文本={features.SquareTextCount}");

            // 5. 提取边缘特征并计算可靠性
            ExtractEdgeFeatures(cells, features);
            System.Diagnostics.Debug.WriteLine($"已提取边缘特征: 左边缘方差={features.LeftEdgeVariance:F2}, 右边缘方差={features.RightEdgeVariance:F2}");

            // 6. 提取行列结构特征
            ExtractRowColumnFeatures(cells, features);
            System.Diagnostics.Debug.WriteLine($"已提取行列特征: 行数={features.HorizontalRowCount}, 列数={features.VerticalColumnCount}");

            // 7. 提取对齐特征并计算可靠性
            ExtractAlignmentFeatures(cells, features);
            CalculateAlignmentFeatureReliability(features);
            System.Diagnostics.Debug.WriteLine($"已提取对齐特征: 左对齐={features.LeftAlignedCount}, 右对齐={features.RightAlignedCount}, 可靠性={features.AlignmentFeatureReliability:F2}");

            // 8. 提取段落特征
            ExtractParagraphFeatures(cells, features);
            System.Diagnostics.Debug.WriteLine($"已提取段落特征: 段落数={features.ParagraphCount}, 连续段落={features.IsSequentialParagraphs}");

            // 9. 提取内容分布特征并计算可靠性
            ExtractContentDistributionFeatures(cells, features);
            System.Diagnostics.Debug.WriteLine($"已提取内容分布特征: 左右比={features.LeftRightContentRatio:F2}, 上下比={features.TopBottomContentRatio:F2}, 可靠性={features.ContentFeatureReliability:F2}");

            // 10. 提取直方图特征并计算可靠性（可能计算密集）
            ExtractHistogramFeatures(cells, features);
            CalculateHistogramReliability(features);
            System.Diagnostics.Debug.WriteLine($"已提取直方图特征: 水平峰数={features.HorizontalPeakCount}, 垂直峰数={features.VerticalPeakCount}, 可靠性={features.HistogramFeatureReliability:F2}");

            // 11. 分析直方图峰值
            AnalyzeHistogramPeaks(features);
            System.Diagnostics.Debug.WriteLine($"已分析直方图峰值: 水平峰质量={features.HorizontalPeakShapeQuality:F2}, 垂直峰质量={features.VerticalPeakShapeQuality:F2}");

            // 12. 计算结构强度
            CalculateStructureStrength(features);
            System.Diagnostics.Debug.WriteLine($"已计算结构强度: 水平={features.HorizontalStructureStrength:F2}, 垂直={features.VerticalStructureStrength:F2}");

            // 13. 计算直方图得分
            CalculateHistogramScores(features);
            System.Diagnostics.Debug.WriteLine($"已计算直方图得分: 行得分={features.HistogramRowScore}, 列得分={features.HistogramColumnScore}");

            // 14. 提取文本方向特征
            ExtractTextDirectionFeatures(cells, features);
            System.Diagnostics.Debug.WriteLine($"已提取文本方向特征: 左到右={features.LeftToRightEvidence}, 右到左={features.RightToLeftEvidence}");

            // 15. 计算特征关系
            CalculateFeatureRelationships(features);
            System.Diagnostics.Debug.WriteLine($"已计算特征关系: 行列比={features.RowColumnRatio:F2}, 垂直水平比={features.VerticalHorizontalRatio:F2}");

            // 16. 确定文档类型
            DetermineDocumentType(features);
            System.Diagnostics.Debug.WriteLine($"已确定文档类型: {features.DocumentTypeCategory}");

            // 17. 【新增】更新特征权重管理器的特征可靠性
            weightManager.UpdateAllFeatureReliability(features);

            // 18. 【新增】输出权重配置信息
            weightManager.DebugPrintWeights(features.DocumentTypeCategory);

            // 19. 【新增】应用特征互补机制
            weightManager.ApplyFeatureComplementation();

            // 20. 特征互补优化
            OptimizeFeatureComplementation(features);
            System.Diagnostics.Debug.WriteLine("已完成特征互补优化");

            // 21. 提取方向证据
            evidence = ExtractDirectionEvidence(features);
            System.Diagnostics.Debug.WriteLine("已提取方向证据");

            System.Diagnostics.Debug.WriteLine("=== 特征提取流程完成 ===\n");
        }

        /// <summary>
        /// 【新增】特征互补优化：根据可靠性对特征进行互补和权重调整
        /// </summary>
        /// <param name="features">文本布局特征</param>
        private void OptimizeFeatureComplementation(TextLayoutFeatures features)
        {
            // 1. 计算各特征类型的可靠性得分
            Dictionary<string, double> featureReliabilityScores = new Dictionary<string, double>();
            featureReliabilityScores.Add("Alignment", features.AlignmentFeatureReliability);
            featureReliabilityScores.Add("Edge", features.EdgeFeatureReliability);
            featureReliabilityScores.Add("Histogram", features.HistogramFeatureReliability);
            featureReliabilityScores.Add("Content", features.ContentFeatureReliability);

            // 2. 按可靠性降序排序特征
            List<KeyValuePair<string, double>> sortedFeatures = new List<KeyValuePair<string, double>>(
                featureReliabilityScores.OrderByDescending(f => f.Value));

            System.Diagnostics.Debug.WriteLine("\n--- 特征互补优化 ---");
            foreach (KeyValuePair<string, double> feature in sortedFeatures)
            {
                System.Diagnostics.Debug.WriteLine(string.Format("{0} 特征可靠性: {1:F2}", feature.Key, feature.Value));
            }

            // 3. 特征互补逻辑
            // 3.1 如果直方图特征不可靠但边缘特征可靠，增强边缘特征权重
            if (features.HistogramFeatureReliability < 0.6 && features.EdgeFeatureReliability > 0.8)
            {
                double boostFactor = 1.0 + (1.0 - features.HistogramFeatureReliability) * 0.5;
                features.LeftEdgeScore *= boostFactor;
                features.RightEdgeScore *= boostFactor;
                features.TopEdgeScore *= boostFactor;
                features.BottomEdgeScore *= boostFactor;

                System.Diagnostics.Debug.WriteLine(string.Format("直方图特征不可靠，边缘特征权重提升 {0:F2} 倍", boostFactor));
            }

            // 3.2 如果对齐特征不可靠但内容分布特征可靠，增强内容分布特征权重
            if (features.AlignmentFeatureReliability < 0.6 && features.ContentFeatureReliability > 0.8)
            {
                double boostFactor = 1.0 + (1.0 - features.AlignmentFeatureReliability) * 0.5;
                features.LeftContentScore *= boostFactor;
                features.RightContentScore *= boostFactor;
                features.TopContentScore *= boostFactor;
                features.BottomContentScore *= boostFactor;

                System.Diagnostics.Debug.WriteLine(string.Format("对齐特征不可靠，内容分布特征权重提升 {0:F2} 倍", boostFactor));
            }

            // 3.3 【新增】如果边缘特征不可靠但对齐特征可靠，增强对齐特征权重
            if (features.EdgeFeatureReliability < 0.6 && features.AlignmentFeatureReliability > 0.8)
            {
                double boostFactor = 1.0 + (1.0 - features.EdgeFeatureReliability) * 0.5;
                features.LeftAlignmentScore *= boostFactor;
                features.RightAlignmentScore *= boostFactor;
                features.TopAlignmentScore *= boostFactor;
                features.BottomAlignmentScore *= boostFactor;

                System.Diagnostics.Debug.WriteLine(string.Format("边缘特征不可靠，对齐特征权重提升 {0:F2} 倍", boostFactor));
            }

            // 3.4 【新增】如果内容分布特征不可靠但直方图特征可靠，增强直方图特征权重
            if (features.ContentFeatureReliability < 0.6 && features.HistogramFeatureReliability > 0.8)
            {
                double boostFactor = 1.0 + (1.0 - features.ContentFeatureReliability) * 0.5;
                features.HistogramRowScore = (int)(features.HistogramRowScore * boostFactor);
                features.HistogramColumnScore = (int)(features.HistogramColumnScore * boostFactor);

                System.Diagnostics.Debug.WriteLine(string.Format("内容分布特征不可靠，直方图特征权重提升 {0:F2} 倍", boostFactor));
            }

            // 4. 特征一致性评估和奖励
            // 4.1 评估水平方向特征一致性
            bool horizontalConsistency = IsHorizontalFeaturesConsistent(features);
            bool verticalConsistency = IsVerticalFeaturesConsistent(features);

            // 4.2 对一致性高的方向给予奖励
            if (horizontalConsistency)
            {
                features.DirectionalConsistency = Math.Min(1.0, features.DirectionalConsistency + 0.2);
                System.Diagnostics.Debug.WriteLine("水平方向特征一致性高，方向一致性评分提升");

                // 【新增】水平方向特征一致性高，增强水平方向相关特征权重
                features.LeftEdgeScore *= 1.15;
                features.RightEdgeScore *= 1.15;
                features.LeftAlignmentScore *= 1.15;
                features.RightAlignmentScore *= 1.15;
                features.HistogramRowScore = (int)(features.HistogramRowScore * 1.15);

                System.Diagnostics.Debug.WriteLine("水平方向特征一致性高，增强水平方向相关特征权重 (×1.15)");
            }

            if (verticalConsistency)
            {
                features.DirectionalConsistency = Math.Min(1.0, features.DirectionalConsistency + 0.2);
                System.Diagnostics.Debug.WriteLine("垂直方向特征一致性高，方向一致性评分提升");

                // 【新增】垂直方向特征一致性高，增强垂直方向相关特征权重
                features.TopEdgeScore *= 1.15;
                features.BottomEdgeScore *= 1.15;
                features.TopAlignmentScore *= 1.15;
                features.BottomAlignmentScore *= 1.15;
                features.HistogramColumnScore = (int)(features.HistogramColumnScore * 1.15);

                System.Diagnostics.Debug.WriteLine("垂直方向特征一致性高，增强垂直方向相关特征权重 (×1.15)");
            }

            // 5. 【新增】特征奖励机制：如果某个特征非常可靠（>0.9），增强其影响
            foreach (KeyValuePair<string, double> feature in sortedFeatures)
            {
                if (feature.Value > 0.9)
                {
                    System.Diagnostics.Debug.WriteLine(string.Format("{0}特征可靠性极高({1:F2})，应用特征奖励", feature.Key, feature.Value));

                    if (feature.Key == "Alignment")
                    {
                        // 对齐特征高度可靠，增强其影响
                        features.LeftAlignmentScore *= 1.25;
                        features.RightAlignmentScore *= 1.25;
                        features.TopAlignmentScore *= 1.25;
                        features.BottomAlignmentScore *= 1.25;
                        System.Diagnostics.Debug.WriteLine("对齐特征奖励: 增强对齐评分 (×1.25)");
                    }
                    else if (feature.Key == "Edge")
                    {
                        // 边缘特征高度可靠，增强其影响
                        features.LeftEdgeScore *= 1.25;
                        features.RightEdgeScore *= 1.25;
                        features.TopEdgeScore *= 1.25;
                        features.BottomEdgeScore *= 1.25;
                        System.Diagnostics.Debug.WriteLine("边缘特征奖励: 增强边缘评分 (×1.25)");
                    }
                    else if (feature.Key == "Histogram")
                    {
                        // 直方图特征高度可靠，增强其影响
                        features.HistogramRowScore = (int)(features.HistogramRowScore * 1.25);
                        features.HistogramColumnScore = (int)(features.HistogramColumnScore * 1.25);
                        System.Diagnostics.Debug.WriteLine("直方图特征奖励: 增强直方图评分 (×1.25)");
                    }
                    else if (feature.Key == "Content")
                    {
                        // 内容分布特征高度可靠，增强其影响
                        features.LeftContentScore *= 1.25;
                        features.RightContentScore *= 1.25;
                        features.TopContentScore *= 1.25;
                        features.BottomContentScore *= 1.25;
                        System.Diagnostics.Debug.WriteLine("内容分布特征奖励: 增强内容评分 (×1.25)");
                    }

                    // 每个高可靠性特征只应用一次奖励
                    break;
                }
            }

            // 6. 【新增】特征互补：使用最可靠特征补充最不可靠特征
            if (sortedFeatures.Count >= 2)
            {
                KeyValuePair<string, double> mostReliable = sortedFeatures[0];
                KeyValuePair<string, double> leastReliable = sortedFeatures[sortedFeatures.Count - 1];

                // 只有当最可靠和最不可靠特征差异显著时才进行互补
                if (mostReliable.Value > 0.8 && leastReliable.Value < 0.6)
                {
                    System.Diagnostics.Debug.WriteLine(string.Format("使用{0}特征(可靠性:{1:F2})补充{2}特征(可靠性:{3:F2})",
                        mostReliable.Key, mostReliable.Value, leastReliable.Key, leastReliable.Value));

                    // 根据最可靠特征调整最不可靠特征
                    if (leastReliable.Key == "Histogram" && mostReliable.Key == "Alignment")
                    {
                        // 使用对齐特征来调整直方图特征
                        if (features.LeftAlignedCount > features.RightAlignedCount * 1.5)
                        {
                            // 左对齐明显多于右对齐，增强行结构得分
                            features.HistogramRowScore = (int)(features.HistogramRowScore * 1.2);
                            System.Diagnostics.Debug.WriteLine("左对齐明显多于右对齐，增强行结构得分 (×1.2)");
                        }
                        else if (features.RightAlignedCount > features.LeftAlignedCount * 1.5)
                        {
                            // 右对齐明显多于左对齐，可能是RTL文本，调整直方图评分
                            features.HistogramRowScore = (int)(features.HistogramRowScore * 1.2);
                            features.RightToLeftEvidence += 5;
                            System.Diagnostics.Debug.WriteLine("右对齐明显多于左对齐，增强行结构得分 (×1.2) 并增加RTL证据(+5)");
                        }
                    }
                    else if (leastReliable.Key == "Alignment" && mostReliable.Key == "Histogram")
                    {
                        // 使用直方图特征来调整对齐特征
                        if (features.HistogramRowScore > features.HistogramColumnScore * 1.5)
                        {
                            // 行结构明显强于列结构，增强左右对齐评分
                            features.LeftAlignmentScore *= 1.2;
                            features.RightAlignmentScore *= 1.2;
                            System.Diagnostics.Debug.WriteLine("行结构明显强于列结构，增强左右对齐评分 (×1.2)");
                        }
                        else if (features.HistogramColumnScore > features.HistogramRowScore * 1.5)
                        {
                            // 列结构明显强于行结构，增强上下对齐评分
                            features.TopAlignmentScore *= 1.2;
                            features.BottomAlignmentScore *= 1.2;
                            System.Diagnostics.Debug.WriteLine("列结构明显强于行结构，增强上下对齐评分 (×1.2)");
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 【新增】检查水平方向特征是否一致
        /// </summary>
        private bool IsHorizontalFeaturesConsistent(TextLayoutFeatures features)
        {
            // 判断左右对齐和内容分布是否一致
            bool leftDominant = features.LeftAlignmentScore > features.RightAlignmentScore * 1.5;
            bool rightDominant = features.RightAlignmentScore > features.LeftAlignmentScore * 1.5;
            bool leftContentDominant = features.ContentDensityLeftHalf > features.ContentDensityRightHalf * 1.3;
            bool rightContentDominant = features.ContentDensityRightHalf > features.ContentDensityLeftHalf * 1.3;

            // 检查一致性
            return (leftDominant && leftContentDominant) || (rightDominant && rightContentDominant) ||
                   (!leftDominant && !rightDominant && !leftContentDominant && !rightContentDominant);
        }

        /// <summary>
        /// 【新增】检查垂直方向特征是否一致
        /// </summary>
        private bool IsVerticalFeaturesConsistent(TextLayoutFeatures features)
        {
            // 判断上下对齐和内容分布是否一致
            bool topDominant = features.TopAlignmentScore > features.BottomAlignmentScore * 1.5;
            bool bottomDominant = features.BottomAlignmentScore > features.TopAlignmentScore * 1.5;
            bool topContentDominant = features.ContentDensityTopHalf > features.ContentDensityBottomHalf * 1.3;
            bool bottomContentDominant = features.ContentDensityBottomHalf > features.ContentDensityTopHalf * 1.3;

            // 检查一致性
            return (topDominant && topContentDominant) || (bottomDominant && bottomContentDominant) ||
                   (!topDominant && !bottomDominant && !topContentDominant && !bottomContentDominant);
        }
    }

    /// <summary>
    /// 文本流方向枚举
    /// </summary>
    public enum TextFlowDirection
    {
        /// <summary>从左到右（常见于拉丁语系、中文横排等）</summary>
        LeftToRight,
        /// <summary>从右到左（常见于阿拉伯语、希伯来语等）</summary>
        RightToLeft,
        /// <summary>从上到下（常见于中文竖排等）</summary>
        TopToBottom,
        /// <summary>从下到上（常见于车道提示语等）</summary>
        BottomToTop
    }

    /// <summary>
    /// 【新增】特征权重管理器 - 集中管理所有特征权重，提供动态权重调整
    /// </summary>
    public class FeatureWeightManager
    {
        // 特征类型枚举
        public enum FeatureType
        {
            Shape,      // 形状特征
            Histogram,  // 直方图特征
            Edge,       // 边缘特征
            Alignment,  // 对齐特征
            Content,    // 内容分布特征
            Paragraph   // 段落特征
        }

        // 特征方向枚举
        public enum FeatureDirection
        {
            Horizontal, // 水平方向
            Vertical,   // 垂直方向
            Both        // 两个方向
        }

        // 基础权重配置
        private Dictionary<FeatureType, double> baseWeights;

        // 方向特定权重调整
        private Dictionary<FeatureType, Dictionary<FeatureDirection, double>> directionAdjustments;

        // 文档类型特定权重调整
        private Dictionary<TextFlowDocumentType, Dictionary<FeatureType, double>> documentTypeAdjustments;

        // 特征可靠性
        private Dictionary<FeatureType, double> featureReliability;

        /// <summary>
        /// 构造函数 - 初始化默认权重配置
        /// </summary>
        public FeatureWeightManager()
        {
            InitializeBaseWeights();
            InitializeDirectionAdjustments();
            InitializeDocumentTypeAdjustments();
            InitializeFeatureReliability();
        }

        /// <summary>
        /// 初始化基础权重
        /// </summary>
        private void InitializeBaseWeights()
        {
            baseWeights = new Dictionary<FeatureType, double>
            {
                { FeatureType.Shape, 1.2 },      // 【修改】形状特征基础权重，增加权重因为它通常更可靠
                { FeatureType.Histogram, 0.9 },  // 【修改】直方图特征基础权重，降低权重避免过度依赖
                { FeatureType.Edge, 0.8 },       // 边缘特征基础权重
                { FeatureType.Alignment, 0.9 },  // 对齐特征基础权重
                { FeatureType.Content, 0.7 },    // 内容分布特征基础权重
                { FeatureType.Paragraph, 0.6 }   // 段落特征基础权重
            };
        }

        /// <summary>
        /// 初始化方向特定权重调整
        /// </summary>
        private void InitializeDirectionAdjustments()
        {
            directionAdjustments = new Dictionary<FeatureType, Dictionary<FeatureDirection, double>>
            {
                {
                    FeatureType.Shape, new Dictionary<FeatureDirection, double>
                    {
                        { FeatureDirection.Horizontal, 1.0 },
                        { FeatureDirection.Vertical, 1.0 },
                        { FeatureDirection.Both, 1.0 }
                    }
                },
                {
                    FeatureType.Histogram, new Dictionary<FeatureDirection, double>
                    {
                        { FeatureDirection.Horizontal, 1.0 },
                        { FeatureDirection.Vertical, 1.0 },
                        { FeatureDirection.Both, 1.0 }
                    }
                },
                {
                    FeatureType.Edge, new Dictionary<FeatureDirection, double>
                    {
                        { FeatureDirection.Horizontal, 1.0 },
                        { FeatureDirection.Vertical, 1.0 },
                        { FeatureDirection.Both, 1.0 }
                    }
                },
                {
                    FeatureType.Alignment, new Dictionary<FeatureDirection, double>
                    {
                        { FeatureDirection.Horizontal, 1.0 },
                        { FeatureDirection.Vertical, 1.0 },
                        { FeatureDirection.Both, 1.0 }
                    }
                },
                {
                    FeatureType.Content, new Dictionary<FeatureDirection, double>
                    {
                        { FeatureDirection.Horizontal, 1.0 },
                        { FeatureDirection.Vertical, 1.0 },
                        { FeatureDirection.Both, 1.0 }
                    }
                },
                {
                    FeatureType.Paragraph, new Dictionary<FeatureDirection, double>
                    {
                        { FeatureDirection.Horizontal, 1.2 }, // 段落特征在水平方向更重要
                        { FeatureDirection.Vertical, 0.8 },   // 段落特征在垂直方向较弱
                        { FeatureDirection.Both, 1.0 }
                    }
                }
            };
        }

        /// <summary>
        /// 初始化文档类型特定权重调整
        /// </summary>
        private void InitializeDocumentTypeAdjustments()
        {
            documentTypeAdjustments = new Dictionary<TextFlowDocumentType, Dictionary<FeatureType, double>>
            {
                {
                    TextFlowDocumentType.DefinitiveStructure, new Dictionary<FeatureType, double>
                    {
                        { FeatureType.Shape, 0.8 },       // 明确结构文档中形状特征权重降低
                        { FeatureType.Histogram, 1.3 },   // 明确结构文档中直方图特征权重提高
                        { FeatureType.Edge, 1.1 },        // 明确结构文档中边缘特征权重略提高
                        { FeatureType.Alignment, 1.2 },   // 明确结构文档中对齐特征权重提高
                        { FeatureType.Content, 0.9 },     // 明确结构文档中内容分布特征权重略降低
                        { FeatureType.Paragraph, 0.8 }    // 明确结构文档中段落特征权重降低
                    }
                },
                {
                    TextFlowDocumentType.HighStructure, new Dictionary<FeatureType, double>
                    {
                        { FeatureType.Shape, 0.9 },       // 高结构文档中形状特征权重略降低
                        { FeatureType.Histogram, 1.2 },   // 高结构文档中直方图特征权重提高
                        { FeatureType.Edge, 1.1 },        // 高结构文档中边缘特征权重略提高
                        { FeatureType.Alignment, 1.1 },   // 高结构文档中对齐特征权重略提高
                        { FeatureType.Content, 1.0 },     // 高结构文档中内容分布特征权重保持不变
                        { FeatureType.Paragraph, 0.9 }    // 高结构文档中段落特征权重略降低
                    }
                },
                {
                    TextFlowDocumentType.Normal, new Dictionary<FeatureType, double>
                    {
                        { FeatureType.Shape, 1.0 },       // 普通文档中形状特征权重保持不变
                        { FeatureType.Histogram, 1.0 },   // 普通文档中直方图特征权重保持不变
                        { FeatureType.Edge, 1.0 },        // 普通文档中边缘特征权重保持不变
                        { FeatureType.Alignment, 1.0 },   // 普通文档中对齐特征权重保持不变
                        { FeatureType.Content, 1.0 },     // 普通文档中内容分布特征权重保持不变
                        { FeatureType.Paragraph, 1.0 }    // 普通文档中段落特征权重保持不变
                    }
                },
                {
                    TextFlowDocumentType.LowQuality, new Dictionary<FeatureType, double>
                    {
                        { FeatureType.Shape, 1.2 },       // 低质量文档中形状特征权重提高
                        { FeatureType.Histogram, 0.8 },   // 低质量文档中直方图特征权重降低
                        { FeatureType.Edge, 0.7 },        // 低质量文档中边缘特征权重降低
                        { FeatureType.Alignment, 0.8 },   // 低质量文档中对齐特征权重降低
                        { FeatureType.Content, 1.1 },     // 低质量文档中内容分布特征权重略提高
                        { FeatureType.Paragraph, 1.2 }    // 低质量文档中段落特征权重提高
                    }
                }
            };
        }

        /// <summary>
        /// 初始化特征可靠性
        /// </summary>
        private void InitializeFeatureReliability()
        {
            featureReliability = new Dictionary<FeatureType, double>
            {
                { FeatureType.Shape, 1.0 },      // 形状特征初始可靠性
                { FeatureType.Histogram, 1.0 },  // 直方图特征初始可靠性
                { FeatureType.Edge, 1.0 },       // 边缘特征初始可靠性
                { FeatureType.Alignment, 1.0 },  // 对齐特征初始可靠性
                { FeatureType.Content, 1.0 },    // 内容分布特征初始可靠性
                { FeatureType.Paragraph, 1.0 }   // 段落特征初始可靠性
            };
        }

        /// <summary>
        /// 更新特征可靠性
        /// </summary>
        /// <param name="featureType">特征类型</param>
        /// <param name="reliability">可靠性值(0-2)</param>
        public void UpdateFeatureReliability(FeatureType featureType, double reliability)
        {
            // 确保可靠性在合理范围内
            reliability = Math.Max(0.1, Math.Min(2.0, reliability));
            featureReliability[featureType] = reliability;

            System.Diagnostics.Debug.WriteLine($"特征可靠性更新: {featureType} = {reliability:F2}");
        }

        /// <summary>
        /// 从TextLayoutFeatures对象中更新所有特征可靠性
        /// </summary>
        /// <param name="features">文本布局特征</param>
        public void UpdateAllFeatureReliability(TextLayoutFeatures features)
        {
            UpdateFeatureReliability(FeatureType.Histogram, features.HistogramFeatureReliability);
            UpdateFeatureReliability(FeatureType.Edge, features.EdgeFeatureReliability);
            UpdateFeatureReliability(FeatureType.Alignment, features.AlignmentFeatureReliability);
            UpdateFeatureReliability(FeatureType.Content, features.ContentFeatureReliability);

            // 形状特征可靠性基于文本块数量和分布
            double shapeReliability = 1.0;
            if (features.ValidTextBlocks > 10)
            {
                // 形状特征分布越均衡，可靠性越低
                double balanceRatio = Math.Min(features.HorizontalTextCount, features.VerticalTextCount) /
                                     (double)Math.Max(1, Math.Max(features.HorizontalTextCount, features.VerticalTextCount));

                if (balanceRatio < 0.1)
                    shapeReliability = 1.3; // 形状特征非常不平衡，可靠性高
                else if (balanceRatio < 0.3)
                    shapeReliability = 1.1; // 形状特征较不平衡，可靠性略高
                else if (balanceRatio > 0.7)
                    shapeReliability = 0.8; // 形状特征较平衡，可靠性降低
            }
            else
            {
                // 文本块数量少，形状特征可靠性降低
                shapeReliability = 0.7;
            }

            UpdateFeatureReliability(FeatureType.Shape, shapeReliability);

            // 段落特征可靠性基于段落数量和连续性
            double paragraphReliability = 1.0;
            if (features.ParagraphCount > 1)
            {
                paragraphReliability = features.IsSequentialParagraphs ? 1.2 : 0.9;
            }
            else
            {
                paragraphReliability = 0.7; // 只有一个段落，可靠性降低
            }

            UpdateFeatureReliability(FeatureType.Paragraph, paragraphReliability);
        }

        /// <summary>
        /// 获取特征权重
        /// </summary>
        /// <param name="featureType">特征类型</param>
        /// <param name="direction">特征方向</param>
        /// <param name="documentType">文档类型</param>
        /// <returns>计算后的特征权重</returns>
        public double GetFeatureWeight(FeatureType featureType, FeatureDirection direction, TextFlowDocumentType documentType)
        {
            try
            {
                // 基础权重
                double weight = 1.0;
                if (baseWeights.ContainsKey(featureType))
                {
                    weight = baseWeights[featureType];
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"警告：未找到特征类型 {featureType} 的基础权重，使用默认值 1.0");
                }

                // 应用方向调整
                if (directionAdjustments.ContainsKey(featureType) && directionAdjustments[featureType].ContainsKey(direction))
                {
                    weight *= directionAdjustments[featureType][direction];
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"警告：未找到特征类型 {featureType} 方向 {direction} 的调整，使用默认值 1.0");
                }

                // 应用文档类型调整
                if (documentTypeAdjustments.ContainsKey(documentType) && documentTypeAdjustments[documentType].ContainsKey(featureType))
                {
                    weight *= documentTypeAdjustments[documentType][featureType];
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"警告：未找到文档类型 {documentType} 特征类型 {featureType} 的调整，使用默认值 1.0");
                }

                // 应用可靠性调整
                if (featureReliability.ContainsKey(featureType))
                {
                    weight *= featureReliability[featureType];
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"警告：未找到特征类型 {featureType} 的可靠性，使用默认值 1.0");
                }

                return weight;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取特征权重时发生异常: {ex.Message}");
                return 1.0; // 发生异常时返回默认权重
            }
        }

        /// <summary>
        /// 获取特征权重，使用默认方向(Both)
        /// </summary>
        /// <param name="featureType">特征类型</param>
        /// <param name="documentType">文档类型</param>
        /// <returns>计算后的特征权重</returns>
        public double GetFeatureWeight(FeatureType featureType, TextFlowDocumentType documentType)
        {
            return GetFeatureWeight(featureType, FeatureDirection.Both, documentType);
        }

        /// <summary>
        /// 应用特征互补机制 - 当某些特征可靠性低时，提升其他可靠特征的权重
        /// </summary>
        public void ApplyFeatureComplementation()
        {
            // 找出可靠性最低和最高的特征
            var minReliability = featureReliability.Min(kv => kv.Value);
            var maxReliability = featureReliability.Max(kv => kv.Value);

            // 如果最低可靠性低于阈值，应用互补
            if (minReliability < 0.7 && maxReliability > 1.0)
            {
                var lowReliabilityFeatures = featureReliability.Where(kv => kv.Value < 0.7)
                                                              .Select(kv => kv.Key).ToList();
                var highReliabilityFeatures = featureReliability.Where(kv => kv.Value > 1.0)
                                                               .Select(kv => kv.Key).ToList();

                foreach (var lowFeature in lowReliabilityFeatures)
                {
                    System.Diagnostics.Debug.WriteLine($"特征互补: {lowFeature}(可靠性:{featureReliability[lowFeature]:F2})需要补充");

                    // 寻找最适合互补的高可靠性特征
                    FeatureType? bestComplementFeature = null;
                    double bestComplementScore = 0;

                    foreach (var highFeature in highReliabilityFeatures)
                    {
                        double complementScore = CalculateComplementScore(lowFeature, highFeature);
                        if (complementScore > bestComplementScore)
                        {
                            bestComplementScore = complementScore;
                            bestComplementFeature = highFeature;
                        }
                    }

                    if (bestComplementFeature.HasValue)
                    {
                        System.Diagnostics.Debug.WriteLine($"使用{bestComplementFeature}(可靠性:{featureReliability[bestComplementFeature.Value]:F2})补充{lowFeature}");
                        // 实际的互补逻辑将在特征提取和决策过程中应用
                    }
                }
            }
        }

        /// <summary>
        /// 计算两种特征之间的互补得分
        /// </summary>
        /// <param name="lowFeature">低可靠性特征</param>
        /// <param name="highFeature">高可靠性特征</param>
        /// <returns>互补得分</returns>
        private double CalculateComplementScore(FeatureType lowFeature, FeatureType highFeature)
        {
            // 基础得分 - 基于高可靠性特征的可靠性
            double score = featureReliability[highFeature];

            // 特征兼容性调整
            switch (lowFeature)
            {
                case FeatureType.Shape:
                    if (highFeature == FeatureType.Histogram)
                        score *= 0.9; // 形状和直方图特征有一定重叠
                    else if (highFeature == FeatureType.Alignment)
                        score *= 1.2; // 形状和对齐特征互补性强
                    break;

                case FeatureType.Histogram:
                    if (highFeature == FeatureType.Shape)
                        score *= 0.9; // 直方图和形状特征有一定重叠
                    else if (highFeature == FeatureType.Edge)
                        score *= 1.1; // 直方图和边缘特征互补性较强
                    break;

                case FeatureType.Edge:
                    if (highFeature == FeatureType.Alignment)
                        score *= 0.8; // 边缘和对齐特征有较多重叠
                    else if (highFeature == FeatureType.Histogram)
                        score *= 1.1; // 边缘和直方图特征互补性较强
                    break;

                case FeatureType.Alignment:
                    if (highFeature == FeatureType.Edge)
                        score *= 0.8; // 对齐和边缘特征有较多重叠
                    else if (highFeature == FeatureType.Shape)
                        score *= 1.2; // 对齐和形状特征互补性强
                    break;

                case FeatureType.Content:
                    if (highFeature == FeatureType.Paragraph)
                        score *= 0.9; // 内容和段落特征有一定重叠
                    else if (highFeature == FeatureType.Histogram)
                        score *= 1.1; // 内容和直方图特征互补性较强
                    break;

                case FeatureType.Paragraph:
                    if (highFeature == FeatureType.Content)
                        score *= 0.9; // 段落和内容特征有一定重叠
                    else if (highFeature == FeatureType.Shape)
                        score *= 1.1; // 段落和形状特征互补性较强
                    break;
            }

            return score;
        }

        /// <summary>
        /// 获取特征可靠性
        /// </summary>
        /// <param name="featureType">特征类型</param>
        /// <returns>特征可靠性</returns>
        public double GetFeatureReliability(FeatureType featureType)
        {
            try
            {
                if (featureReliability.ContainsKey(featureType))
                {
                    return featureReliability[featureType];
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"警告：未找到特征类型 {featureType} 的可靠性，使用默认值 1.0");
                    return 1.0;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取特征可靠性时发生异常: {ex.Message}");
                return 1.0;
            }
        }

        /// <summary>
        /// 输出当前权重配置
        /// </summary>
        public void DebugPrintWeights(TextFlowDocumentType documentType)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("\n=== 特征权重配置 ===");
                System.Diagnostics.Debug.WriteLine($"文档类型: {documentType}");

                // 显式列出所有已定义的特征类型，避免使用Enum.GetValues可能导致的问题
                FeatureType[] definedFeatureTypes = new FeatureType[]
                {
                    FeatureType.Shape,
                    FeatureType.Histogram,
                    FeatureType.Edge,
                    FeatureType.Alignment,
                    FeatureType.Content,
                    FeatureType.Paragraph
                };

                foreach (var featureType in definedFeatureTypes)
                {
                    System.Diagnostics.Debug.WriteLine($"{featureType} 特征:");

                    double reliability = GetFeatureReliability(featureType);
                    System.Diagnostics.Debug.WriteLine($"  可靠性: {reliability:F2}");

                    double horizontalWeight = GetFeatureWeight(featureType, FeatureDirection.Horizontal, documentType);
                    System.Diagnostics.Debug.WriteLine($"  水平方向权重: {horizontalWeight:F2}");

                    double verticalWeight = GetFeatureWeight(featureType, FeatureDirection.Vertical, documentType);
                    System.Diagnostics.Debug.WriteLine($"  垂直方向权重: {verticalWeight:F2}");

                    double bothWeight = GetFeatureWeight(featureType, FeatureDirection.Both, documentType);
                    System.Diagnostics.Debug.WriteLine($"  通用权重: {bothWeight:F2}");
                }

                System.Diagnostics.Debug.WriteLine("=== 特征权重配置结束 ===\n");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"输出权重配置时发生异常: {ex.Message}");
            }
        }
    }
    /// <summary>
    /// 统一的文本布局特征数据结构
    /// </summary>
    public class TextLayoutFeatures
    {
        // 文本块统计
        public int TotalTextBlocks { get; set; } = 0;
        public int ValidTextBlocks { get; set; } = 0;

        // 形状特征
        public List<double> HeightWidthRatios { get; set; } = new List<double>();
        public List<double> WidthHeightRatios { get; set; } = new List<double>();

        public double MedianHeightWidthRatio { get; set; } = 1.0;
        public double MedianWidthHeightRatio { get; set; } = 1.0;
        public double HeightWidthRatioVariance { get; set; } = 0;
        public double WidthHeightRatioVariance { get; set; } = 0;

        // 比例百分位数 - 用于自适应阈值
        public double VerticalRatio_P90 { get; set; } = 7.0; // 90百分位高宽比，替代固定阈值
        public double VerticalRatio_P75 { get; set; } = 5.0; // 75百分位高宽比
        public double VerticalRatio_P50 { get; set; } = 1.5; // 中位数高宽比

        public double HorizontalRatio_P90 { get; set; } = 7.0; // 90百分位宽高比
        public double HorizontalRatio_P75 { get; set; } = 5.0; // 75百分位宽高比
        public double HorizontalRatio_P50 { get; set; } = 1.5; // 中位数宽高比

        // 文本块形状统计
        public int VerticalTextCount { get; set; } = 0;   // 高>宽的文本块
        public int HorizontalTextCount { get; set; } = 0; // 宽>高的文本块
        public int SquareTextCount { get; set; } = 0;     // 近似正方形的文本块

        // 尺寸特征
        public double MedianWidth { get; set; } = 0;
        public double MedianHeight { get; set; } = 0;
        public double WidthVariance { get; set; } = 0;
        public double HeightVariance { get; set; } = 0;

        // 边缘特征
        public List<double> LeftEdges { get; set; } = new List<double>();
        public List<double> RightEdges { get; set; } = new List<double>();
        public List<double> TopEdges { get; set; } = new List<double>();
        public List<double> BottomEdges { get; set; } = new List<double>();

        // 边缘变异度
        public double LeftEdgeVariance { get; set; } = 0;
        public double RightEdgeVariance { get; set; } = 0;
        public double TopEdgeVariance { get; set; } = 0;
        public double BottomEdgeVariance { get; set; } = 0;

        // 边缘变异比率
        public double LeftRightEdgeVarianceRatio { get; set; } = 1.0;
        public double TopBottomEdgeVarianceRatio { get; set; } = 1.0;

        // 行列特征
        public int HorizontalRowCount { get; set; } = 0;
        public int VerticalColumnCount { get; set; } = 0;
        public List<List<TextCellInfo>> HorizontalRows { get; set; } = new List<List<TextCellInfo>>();
        public List<List<TextCellInfo>> VerticalColumns { get; set; } = new List<List<TextCellInfo>>();

        // 间距特征
        public List<double> RowGaps { get; set; } = new List<double>();
        public List<double> ColumnGaps { get; set; } = new List<double>();
        public double MedianRowGap { get; set; } = 0;
        public double MedianColumnGap { get; set; } = 0;
        public double RowGapVariance { get; set; } = 0;
        public double ColumnGapVariance { get; set; } = 0;

        // 规律性特征
        public double RowGapRegularity { get; set; } = 1.0;
        public double ColumnGapRegularity { get; set; } = 1.0;

        // 对齐特征
        public int LeftAlignedCount { get; set; } = 0;
        public int RightAlignedCount { get; set; } = 0;
        public int TopAlignedCount { get; set; } = 0;
        public int BottomAlignedCount { get; set; } = 0;

        // 对齐精确度
        public double LeftEdgeAlignmentPrecision { get; set; } = 0;  // 左边缘对齐精确度
        public double RightEdgeAlignmentPrecision { get; set; } = 0; // 右边缘对齐精确度
        public double TopEdgeAlignmentPrecision { get; set; } = 0;   // 顶部边缘对齐精确度
        public double BottomEdgeAlignmentPrecision { get; set; } = 0; // 底部边缘对齐精确度

        // 段落特征
        public int ParagraphCount { get; set; } = 1;
        public double ParagraphGapThreshold { get; set; } = 0;
        public List<double> ParagraphGaps { get; set; } = new List<double>();
        public bool IsSequentialParagraphs { get; set; } = false; // 是否为连续段落布局

        // 内容分布特征
        public double ContentDensityLeftHalf { get; set; } = 0; // 左半页内容密度
        public double ContentDensityRightHalf { get; set; } = 0; // 右半页内容密度
        public double ContentDensityTopHalf { get; set; } = 0;   // 上半页内容密度
        public double ContentDensityBottomHalf { get; set; } = 0; // 下半页内容密度

        // 【新增】内容分布比率 - 用于简化后续计算
        public double LeftRightContentRatio { get; set; } = 1.0; // 左半页与右半页内容密度比率
        public double TopBottomContentRatio { get; set; } = 1.0; // 上半页与下半页内容密度比率

        // 【新增】文本分布模式描述
        public string TextDistributionPattern { get; set; } = "Unknown"; // Unknown, LeftToRightIncreasing, RightToLeftDecreasing, Mixed等

        // 页面特征
        public double PageLeft { get; set; } = 0;
        public double PageRight { get; set; } = 0;
        public double PageTop { get; set; } = 0;
        public double PageBottom { get; set; } = 0;
        public double PageWidth { get; set; } = 0;
        public double PageHeight { get; set; } = 0;
        public double PageCenter_X { get; set; } = 0;
        public double PageCenter_Y { get; set; } = 0;

        // 特征关系
        public double RowColumnRatio { get; set; } = 1.0;
        public double VerticalHorizontalRatio { get; set; } = 1.0;
        public double LeftAlignmentRatio { get; set; } = 0.5;
        public double TopAlignmentRatio { get; set; } = 0.5;

        // 方向证据
        public int LeftToRightEvidence { get; set; } = 0;
        public int RightToLeftEvidence { get; set; } = 0;
        public int TopToBottomEvidence { get; set; } = 0;
        public int BottomToTopEvidence { get; set; } = 0;

        // 直方图特征 - 新增
        // 直方图原始数据
        public List<int> HorizontalHistogram { get; set; } = new List<int>();
        public List<int> VerticalHistogram { get; set; } = new List<int>();

        // bin大小
        public double HorizontalBinSize { get; set; } = 0;
        public double VerticalBinSize { get; set; } = 0;

        // 峰值位置
        public List<int> HorizontalPeakIndices { get; set; } = new List<int>();
        public List<int> VerticalPeakIndices { get; set; } = new List<int>();

        // 峰值特征
        public int HorizontalPeakCount { get; set; } = 0;  // 行数
        public int VerticalPeakCount { get; set; } = 0;    // 列数
        public double HorizontalPeakRegularity { get; set; } = 0; // 行间距规律性
        public double VerticalPeakRegularity { get; set; } = 0;   // 列间距规律性
        public double HorizontalPeakStrength { get; set; } = 0;   // 行结构强度
        public double VerticalPeakStrength { get; set; } = 0;     // 列结构强度

        // 【新增】峰值质量评估相关属性
        public List<double> VerticalPeakWidths { get; set; } = new List<double>();   // 垂直方向峰值宽度列表
        public List<double> HorizontalPeakWidths { get; set; } = new List<double>(); // 水平方向峰值宽度列表
        public double VerticalPeakMeanWidth { get; set; } = 0;    // 垂直方向峰值平均宽度
        public double HorizontalPeakMeanWidth { get; set; } = 0;  // 水平方向峰值平均宽度

        public List<double> VerticalPeakValleyRatios { get; set; } = new List<double>();   // 垂直方向峰谷比列表
        public List<double> HorizontalPeakValleyRatios { get; set; } = new List<double>(); // 水平方向峰谷比列表
        public double VerticalPeakValleyMeanRatio { get; set; } = 1.0;    // 垂直方向平均峰谷比
        public double HorizontalPeakValleyMeanRatio { get; set; } = 1.0;  // 水平方向平均峰谷比

        public double VerticalPeakShapeQuality { get; set; } = 0.5;   // 垂直方向峰值形状质量（0-1）
        public double HorizontalPeakShapeQuality { get; set; } = 0.5; // 水平方向峰值形状质量（0-1）

        // 【新增】方向强度和一致性特征 - 用于动态权重调整
        public double VerticalStructureStrength { get; set; } = 0.0;  // 垂直结构强度（0-1）
        public double HorizontalStructureStrength { get; set; } = 0.0; // 水平结构强度（0-1）
        public double DirectionalConsistency { get; set; } = 0.5;      // 方向一致性（0-1）

        // 【新增】预计算评分字段 - 减少重复计算
        public double LeftAlignmentScore { get; set; } = 0;    // 左对齐评分
        public double RightAlignmentScore { get; set; } = 0;   // 右对齐评分
        public double TopAlignmentScore { get; set; } = 0;     // 顶部对齐评分
        public double BottomAlignmentScore { get; set; } = 0;  // 底部对齐评分

        public double LeftEdgeScore { get; set; } = 0;         // 左边缘整齐度评分
        public double RightEdgeScore { get; set; } = 0;        // 右边缘整齐度评分
        public double TopEdgeScore { get; set; } = 0;          // 顶部边缘整齐度评分
        public double BottomEdgeScore { get; set; } = 0;       // 底部边缘整齐度评分

        public double LeftContentScore { get; set; } = 0;      // 左侧内容密度评分
        public double RightContentScore { get; set; } = 0;     // 右侧内容密度评分
        public double TopContentScore { get; set; } = 0;       // 顶部内容密度评分
        public double BottomContentScore { get; set; } = 0;    // 底部内容密度评分

        // 【新增】特征可靠性指标 - 用于动态权重调整
        public double AlignmentFeatureReliability { get; set; } = 1.0;   // 对齐特征可靠性
        public double EdgeFeatureReliability { get; set; } = 1.0;        // 边缘特征可靠性
        public double HistogramFeatureReliability { get; set; } = 1.0;   // 直方图特征可靠性
        public double ContentFeatureReliability { get; set; } = 1.0;     // 内容分布特征可靠性

        // 【新增】文档类型识别字段
        public TextFlowDirectionDetector.TextFlowDocumentType DocumentTypeCategory { get; set; } = TextFlowDirectionDetector.TextFlowDocumentType.Normal;  // 文档类型分类

        // 直方图结构得分
        public int HistogramRowScore { get; set; } = 0;    // 行结构得分
        public int HistogramColumnScore { get; set; } = 0; // 列结构得分

        // 【新增】缓存计算结果的标志
        public bool IsHistogramFeaturesComputed { get; set; } = false; // 直方图特征是否已计算标志
        public bool IsEdgeScoresComputed { get; set; } = false;       // 边缘评分是否已计算标志
        public bool IsContentScoresComputed { get; set; } = false;    // 内容评分是否已计算标志
        public bool IsAlignmentScoresComputed { get; set; } = false;  // 对齐评分是否已计算标志
    }

    /// <summary>
    /// 方向证据 - 用于存储各种布局方向的证据
    /// </summary>
    public class DirectionEvidence
    {
        // 文本块形状特征
        public int VerticalTextCount { get; set; } = 0;   // 高>宽的文本块
        public int HorizontalTextCount { get; set; } = 0; // 宽>高的文本块
        public int SquareTextCount { get; set; } = 0;     // 近似正方形的文本块

        // 边缘变异特征
        public double LeftEdgeVariance { get; set; } = 0;
        public double RightEdgeVariance { get; set; } = 0;
        public double TopEdgeVariance { get; set; } = 0;
        public double BottomEdgeVariance { get; set; } = 0;

        // 行列结构特征
        public int HorizontalAlignedRows { get; set; } = 0;
        public int VerticalAlignedColumns { get; set; } = 0;

        // 对齐特征
        public int LeftAlignedCount { get; set; } = 0;
        public int RightAlignedCount { get; set; } = 0;
        public int TopAlignedCount { get; set; } = 0;
        public int BottomAlignedCount { get; set; } = 0;

        // 段落特征
        public int ParagraphCount { get; set; } = 1;
        public bool IsSequentialParagraphs { get; set; } = false;

        // 内容分布特征
        public double ContentDensityLeftHalf { get; set; } = 0;
        public double ContentDensityRightHalf { get; set; } = 0;
        public double ContentDensityTopHalf { get; set; } = 0;
        public double ContentDensityBottomHalf { get; set; } = 0;

        // 直方图特征 - 新增
        public int HistogramColumnScore { get; set; } = 0; // 列结构得分
        public int HistogramRowScore { get; set; } = 0;    // 行结构得分
        public int VerticalPeakCount { get; set; } = 0;    // 列数
        public int HorizontalPeakCount { get; set; } = 0;  // 行数
        public double VerticalPeakRegularity { get; set; } = 0;  // 列间距规律性
        public double HorizontalPeakRegularity { get; set; } = 0; // 行间距规律性

        // 【新增】峰值质量评估相关证据
        public double VerticalPeakValleyMeanRatio { get; set; } = 1.0;  // 垂直方向平均峰谷比
        public double HorizontalPeakValleyMeanRatio { get; set; } = 1.0; // 水平方向平均峰谷比
        public double VerticalPeakShapeQuality { get; set; } = 0.5;     // 垂直方向峰值形状质量（0-1）
        public double HorizontalPeakShapeQuality { get; set; } = 0.5;   // 水平方向峰值形状质量（0-1）

        // 【新增】结构强度证据
        public double VerticalStructureStrength { get; set; } = 0.0;  // 垂直结构强度（0-1）
        public double HorizontalStructureStrength { get; set; } = 0.0; // 水平结构强度（0-1）
        public double DirectionalConsistency { get; set; } = 0.5;      // 方向一致性（0-1）

        // 方向计数 - 添加回缺失的属性
        public int LeftToRightCount { get; set; } = 0;
        public int RightToLeftCount { get; set; } = 0;
        public int TopToBottomCount { get; set; } = 0;
        public int BottomToTopCount { get; set; } = 0;

        // 对齐比率 - 添加回缺失的属性
        public double LeftAlignmentRatio { get; set; } = 0.5;
        public double TopAlignmentRatio { get; set; } = 0.5;
    }
    /// <summary>
    /// 文本方向检测结果
    /// </summary>
    public class TextDirectionResult
    {
        /// <summary>水平方向</summary>
        public TextFlowDirection HorizontalDirection { get; set; } = TextFlowDirection.LeftToRight;

        /// <summary>垂直方向</summary>
        public TextFlowDirection VerticalDirection { get; set; } = TextFlowDirection.TopToBottom;

        /// <summary>水平方向置信度</summary>
        public int HorizontalConfidence { get; set; } = 0;

        /// <summary>垂直方向置信度</summary>
        public int VerticalConfidence { get; set; } = 0;

        /// <summary>是否为竖排布局</summary>
        public bool IsVerticalLayout { get; set; } = false;

        /// <summary>布局置信度</summary>
        public int LayoutConfidence { get; set; } = 0;

        /// <summary>是否从右到左</summary>
        public bool IsRightToLeft { get; set; } = false;

        /// <summary>主要流向</summary>
        public TextFlowDirection FlowDirection
        {
            get
            {
                return IsVerticalLayout ? VerticalDirection : HorizontalDirection;
            }
            set
            {
                if (IsVerticalLayout)
                {
                    VerticalDirection = value;
                }
                else
                {
                    HorizontalDirection = value;
                }
            }
        }

        /// <summary>主要方向置信度</summary>
        public int DirectionConfidence
        {
            get
            {
                return IsVerticalLayout ? VerticalConfidence : HorizontalConfidence;
            }
            set
            {
                if (IsVerticalLayout)
                {
                    VerticalConfidence = value;
                }
                else
                {
                    HorizontalConfidence = value;
                }
            }
        }

        /// <summary>
        /// 获取主要方向
        /// </summary>
        public TextFlowDirection GetPrimaryDirection()
        {
            if (IsVerticalLayout)
            {
                return VerticalDirection;
            }
            else
            {
                return HorizontalDirection;
            }
        }

        /// <summary>
        /// 结果字符串表示
        /// </summary>
        public override string ToString()
        {
            return $"布局: {(IsVerticalLayout ? "竖排" : "横排")} (置信度: {LayoutConfidence}%), " +
                   $"水平: {HorizontalDirection} ({HorizontalConfidence}%), " +
                   $"垂直: {VerticalDirection} ({VerticalConfidence}%)";
        }
    }
}