﻿using MetroFramework.Components;
using MetroFramework.Design;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using MetroFramework.Native;
using OCRTools;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Security;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace MetroFramework.Controls
{
    public enum MetroScrollOrientation
    {
        Horizontal,
        Vertical
    }

    [DefaultEvent("Scroll")]
    [DefaultProperty("Value")]
    [Designer(typeof(MetroScrollBarDesigner), typeof(ParentControlDesigner))]
    public class MetroScrollBar : Control, IMetroControl
    {
        public delegate void ScrollValueChangedDelegate(object sender, int newValue);

        private readonly int _mouseWheelBarPartitions = 10;

        private readonly Timer _progressTimer = new Timer();

        private Timer _autoHoverTimer;

        private bool _bottomBarClicked;

        private Rectangle _clickedBarRectangle;

        private int _curValue;

        private bool _dontUpdateColor;

        private bool _inUpdate;

        private bool _isFirstScrollEventHorizontal = true;

        private bool _isFirstScrollEventVertical = true;

        private bool _isHovered;

        private bool _isPressed;

        private int _largeChange = 10;

        private int _maximum = 100;

        private MetroScrollOrientation _metroOrientation = MetroScrollOrientation.Vertical;

        private MetroColorStyle _metroStyle;

        private MetroThemeStyle _metroTheme;

        private int _minimum;

        private ScrollOrientation _scrollOrientation = ScrollOrientation.VerticalScroll;

        private int _smallChange = 1;

        private int _thumbBottomLimitBottom;

        private int _thumbBottomLimitTop;

        private bool _thumbClicked;

        private int _thumbHeight;

        private int _thumbPosition;

        private Rectangle _thumbRectangle;

        private int _thumbTopLimit;

        private int _thumbWidth = 6;

        private bool _topBarClicked;

        private int _trackPosition;

        public MetroScrollBar()
        {
            SetStyle(
                ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.Selectable |
                ControlStyles.SupportsTransparentBackColor | ControlStyles.OptimizedDoubleBuffer, true);
            Width = 10;
            Height = 200;
            SetupScrollBar();
            _progressTimer.Interval = 20;
            _progressTimer.Tick += ProgressTimerTick;
        }

        public MetroScrollBar(MetroScrollOrientation orientation)
            : this()
        {
            Orientation = orientation;
        }

        public MetroScrollBar(MetroScrollOrientation orientation, int width)
            : this(orientation)
        {
            Width = width;
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomBackColor { get; set; }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseBarColor { get; set; }

        [Category("Metro Appearance")]
        public int ScrollbarSize
        {
            get
            {
                if (Orientation != MetroScrollOrientation.Vertical) return Height;
                return Width;
            }
            set
            {
                if (Orientation == MetroScrollOrientation.Vertical)
                    Width = value;
                else
                    Height = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool HighlightOnWheel { get; set; }

        public MetroScrollOrientation Orientation
        {
            get => _metroOrientation;
            set
            {
                if (value != _metroOrientation)
                {
                    _metroOrientation = value;
                    _scrollOrientation = value == MetroScrollOrientation.Vertical
                        ? ScrollOrientation.VerticalScroll
                        : ScrollOrientation.HorizontalScroll;
                    Size = new Size(Height, Width);
                    SetupScrollBar();
                }
            }
        }

        public int Minimum
        {
            get => _minimum;
            set
            {
                if (_minimum != value && value >= 0 && value < _maximum)
                {
                    _minimum = value;
                    if (_curValue < value) _curValue = value;
                    if (_largeChange > _maximum - _minimum) _largeChange = _maximum - _minimum;
                    SetupScrollBar();
                    if (_curValue < value)
                    {
                        _dontUpdateColor = true;
                        Value = value;
                    }
                    else
                    {
                        ChangeThumbPosition(GetThumbPosition());
                        Refresh();
                    }
                }
            }
        }

        public int Maximum
        {
            get => _maximum;
            set
            {
                if (value != _maximum && value >= 1 && value > _minimum)
                {
                    _maximum = value;
                    if (_largeChange > _maximum - _minimum) _largeChange = _maximum - _minimum;
                    SetupScrollBar();
                    if (_curValue > value)
                    {
                        _dontUpdateColor = true;
                        Value = _maximum;
                    }
                    else
                    {
                        ChangeThumbPosition(GetThumbPosition());
                        Refresh();
                    }
                }
            }
        }

        [DefaultValue(1)]
        public int SmallChange
        {
            get => _smallChange;
            set
            {
                if (value != _smallChange && value >= 1 && value < _largeChange)
                {
                    _smallChange = value;
                    SetupScrollBar();
                }
            }
        }

        [DefaultValue(5)]
        public int LargeChange
        {
            get => _largeChange;
            set
            {
                if (value != _largeChange && value >= _smallChange && value >= 2)
                {
                    if (value > _maximum - _minimum)
                        _largeChange = _maximum - _minimum;
                    else
                        _largeChange = value;
                    SetupScrollBar();
                }
            }
        }

        [Browsable(false)]
        [DefaultValue(0)]
        public int Value
        {
            get => _curValue;
            set
            {
                if (_curValue == value || value < _minimum || value > _maximum) return;
                _curValue = value;
                ChangeThumbPosition(GetThumbPosition());
                OnScroll(ScrollEventType.ThumbPosition, -1, value, _scrollOrientation);
                if (!_dontUpdateColor && HighlightOnWheel)
                {
                    if (!_isHovered) _isHovered = true;
                    if (_autoHoverTimer == null)
                    {
                        _autoHoverTimer = new Timer
                        {
                            Interval = 1000
                        };
                        _autoHoverTimer.Tick += autoHoverTimer_Tick;
                        _autoHoverTimer.Start();
                    }
                    else
                    {
                        _autoHoverTimer.Stop();
                        _autoHoverTimer.Start();
                    }
                }
                else
                {
                    _dontUpdateColor = false;
                }

                Refresh();
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroColorStyle.蓝色)]
        public MetroColorStyle Style
        {
            get
            {
                if (DesignMode || _metroStyle != 0) return _metroStyle;
                if (StyleManager != null) return StyleManager.Style;
                if (StyleManager == null) return MetroColorStyle.蓝色;
                return _metroStyle;
            }
            set => _metroStyle = value;
        }

        [DefaultValue(MetroThemeStyle.Light)]
        [Category("Metro Appearance")]
        public MetroThemeStyle Theme
        {
            get
            {
                if (DesignMode || _metroTheme != 0) return _metroTheme;
                if (StyleManager != null) return StyleManager.Theme;
                if (StyleManager == null) return MetroThemeStyle.Light;
                return _metroTheme;
            }
            set => _metroTheme = value;
        }

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public MetroStyleManager StyleManager { get; set; }

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        public event ScrollEventHandler Scroll;

        public event ScrollValueChangedDelegate ValueChanged;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintBackground != null) CustomPaintBackground(this, e);
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaint != null) CustomPaint(this, e);
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintForeground != null) CustomPaintForeground(this, e);
        }

        private void OnScroll(ScrollEventType type, int oldValue, int newValue, ScrollOrientation orientation)
        {
            if (oldValue != newValue && ValueChanged != null) ValueChanged(this, _curValue);
            if (Scroll == null) return;
            if (orientation == ScrollOrientation.HorizontalScroll)
            {
                if (type != ScrollEventType.EndScroll && _isFirstScrollEventHorizontal)
                    type = ScrollEventType.First;
                else if (!_isFirstScrollEventHorizontal && type == ScrollEventType.EndScroll)
                    _isFirstScrollEventHorizontal = true;
            }
            else if (type != ScrollEventType.EndScroll && _isFirstScrollEventVertical)
            {
                type = ScrollEventType.First;
            }
            else if (!_isFirstScrollEventHorizontal && type == ScrollEventType.EndScroll)
            {
                _isFirstScrollEventVertical = true;
            }

            Scroll(this, new ScrollEventArgs(type, oldValue, newValue, orientation));
        }

        private void autoHoverTimer_Tick(object sender, EventArgs e)
        {
            _isHovered = false;
            Invalidate();
            _autoHoverTimer.Stop();
        }

        public bool HitTest(Point point)
        {
            return _thumbRectangle.Contains(point);
        }

        [SecuritySafeCritical]
        public void BeginUpdate()
        {
            WinApi.SendMessage(Handle, 11, false, 0);
            _inUpdate = true;
        }

        [SecuritySafeCritical]
        public void EndUpdate()
        {
            WinApi.SendMessage(Handle, 11, true, 0);
            _inUpdate = false;
            SetupScrollBar();
            Refresh();
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            try
            {
                var color = BackColor;
                if (!UseCustomBackColor)
                    color = Parent == null ? MetroPaint.BackColor.Form(Theme) :
                        !(Parent is IMetroControl) ? Parent.BackColor : MetroPaint.BackColor.Form(Theme);
                if (color.A == byte.MaxValue)
                {
                    e.Graphics.Clear(color);
                }
                else
                {
                    base.OnPaintBackground(e);
                    OnCustomPaintBackground(new MetroPaintEventArgs(color, Color.Empty, e.Graphics));
                }
            }
            catch
            {
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            try
            {
                if (GetStyle(ControlStyles.AllPaintingInWmPaint)) OnPaintBackground(e);
                OnCustomPaint(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
                OnPaintForeground(e);
            }
            catch
            {
                Invalidate();
            }
        }

        protected virtual void OnPaintForeground(PaintEventArgs e)
        {
            var backColor = UseCustomBackColor ? BackColor :
                Parent == null ? MetroPaint.BackColor.Form(Theme) :
                !(Parent is IMetroControl) ? Parent.BackColor : MetroPaint.BackColor.Form(Theme);
            Color color;
            Color barColor;
            if (_isHovered && !_isPressed && Enabled)
            {
                color = MetroPaint.BackColor.ScrollBar.Thumb.Hover(Theme);
                barColor = MetroPaint.BackColor.ScrollBar.Bar.Hover(Theme);
            }
            else if (_isHovered && _isPressed && Enabled)
            {
                color = MetroPaint.BackColor.ScrollBar.Thumb.Press(Theme);
                barColor = MetroPaint.BackColor.ScrollBar.Bar.Press(Theme);
            }
            else if (!Enabled)
            {
                color = MetroPaint.BackColor.ScrollBar.Thumb.Disabled(Theme);
                barColor = MetroPaint.BackColor.ScrollBar.Bar.Disabled(Theme);
            }
            else
            {
                color = MetroPaint.BackColor.ScrollBar.Thumb.Normal(Theme);
                barColor = MetroPaint.BackColor.ScrollBar.Bar.Normal(Theme);
            }

            DrawScrollBar(e.Graphics, backColor, color, barColor);
            OnCustomPaintForeground(new MetroPaintEventArgs(backColor, color, e.Graphics));
        }

        private void DrawScrollBar(Graphics g, Color backColor, Color thumbColor, Color barColor)
        {
            if (UseBarColor)
                using (var brush = new SolidBrush(barColor))
                {
                    g.FillRectangle(brush, ClientRectangle);
                }

            using (var brush2 = new SolidBrush(backColor))
            {
                var rect = new Rectangle(_thumbRectangle.X - 1, _thumbRectangle.Y - 1, _thumbRectangle.Width + 2,
                    _thumbRectangle.Height + 2);
                g.FillRectangle(brush2, rect);
            }

            using (var brush3 = new SolidBrush(thumbColor))
            {
                g.FillRectangle(brush3, _thumbRectangle);
            }
        }

        protected override void OnGotFocus(EventArgs e)
        {
            Invalidate();
            base.OnGotFocus(e);
        }

        protected override void OnLostFocus(EventArgs e)
        {
            _isHovered = false;
            _isPressed = false;
            Invalidate();
            base.OnLostFocus(e);
        }

        protected override void OnEnter(EventArgs e)
        {
            Invalidate();
            base.OnEnter(e);
        }

        protected override void OnLeave(EventArgs e)
        {
            _isHovered = false;
            _isPressed = false;
            Invalidate();
            base.OnLeave(e);
        }

        protected override void OnMouseWheel(MouseEventArgs e)
        {
            base.OnMouseWheel(e);
            var num = e.Delta / 120 * (_maximum - _minimum) / _mouseWheelBarPartitions;
            if (Orientation == MetroScrollOrientation.Vertical)
                Value -= num;
            else
                Value += num;
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                _isPressed = true;
                Invalidate();
            }

            base.OnMouseDown(e);
            Focus();
            if (e.Button == MouseButtons.Left)
            {
                var location = e.Location;
                if (_thumbRectangle.Contains(location))
                {
                    _thumbClicked = true;
                    _thumbPosition = _metroOrientation == MetroScrollOrientation.Vertical
                        ? location.Y - _thumbRectangle.Y
                        : location.X - _thumbRectangle.X;
                    Invalidate(_thumbRectangle);
                    return;
                }

                _trackPosition = _metroOrientation == MetroScrollOrientation.Vertical ? location.Y : location.X;
                if (_trackPosition < (_metroOrientation == MetroScrollOrientation.Vertical
                    ? _thumbRectangle.Y
                    : _thumbRectangle.X))
                    _topBarClicked = true;
                else
                    _bottomBarClicked = true;
                ProgressThumb(true);
            }
            else if (e.Button == MouseButtons.Right)
            {
                _trackPosition = _metroOrientation == MetroScrollOrientation.Vertical ? e.Y : e.X;
            }
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            _isPressed = false;
            base.OnMouseUp(e);
            if (e.Button == MouseButtons.Left)
            {
                if (_thumbClicked)
                {
                    _thumbClicked = false;
                    OnScroll(ScrollEventType.EndScroll, -1, _curValue, _scrollOrientation);
                }
                else if (_topBarClicked)
                {
                    _topBarClicked = false;
                    StopTimer();
                }
                else if (_bottomBarClicked)
                {
                    _bottomBarClicked = false;
                    StopTimer();
                }

                Invalidate();
            }
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            _isHovered = true;
            Invalidate();
            base.OnMouseEnter(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            _isHovered = false;
            Invalidate();
            base.OnMouseLeave(e);
            ResetScrollStatus();
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            base.OnMouseMove(e);
            if (e.Button == MouseButtons.Left)
            {
                if (!_thumbClicked) return;
                var num = _curValue;
                var num2 = _metroOrientation == MetroScrollOrientation.Vertical ? e.Location.Y : e.Location.X;
                var num3 = _metroOrientation == MetroScrollOrientation.Vertical
                    ? num2 / Height / _thumbHeight
                    : num2 / Width / _thumbWidth;
                if (num2 <= _thumbTopLimit + _thumbPosition)
                {
                    ChangeThumbPosition(_thumbTopLimit);
                    _curValue = _minimum;
                    Invalidate();
                }
                else if (num2 >= _thumbBottomLimitTop + _thumbPosition)
                {
                    ChangeThumbPosition(_thumbBottomLimitTop);
                    _curValue = _maximum;
                    Invalidate();
                }
                else
                {
                    ChangeThumbPosition(num2 - _thumbPosition);
                    int num4;
                    int num5;
                    if (Orientation == MetroScrollOrientation.Vertical)
                    {
                        num4 = Height - num3;
                        num5 = _thumbRectangle.Y;
                    }
                    else
                    {
                        num4 = Width - num3;
                        num5 = _thumbRectangle.X;
                    }

                    var num6 = 0f;
                    if (num4 != 0) num6 = num5 / (float)num4;
                    _curValue = Convert.ToInt32(num6 * (_maximum - _minimum) + _minimum);
                }

                if (num != _curValue)
                {
                    OnScroll(ScrollEventType.ThumbTrack, num, _curValue, _scrollOrientation);
                    Refresh();
                }
            }
            else if (!ClientRectangle.Contains(e.Location))
            {
                ResetScrollStatus();
            }
            else if (e.Button == MouseButtons.None)
            {
                if (_thumbRectangle.Contains(e.Location))
                    Invalidate(_thumbRectangle);
                else if (ClientRectangle.Contains(e.Location)) Invalidate();
            }
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            _isHovered = true;
            _isPressed = true;
            Invalidate();
            base.OnKeyDown(e);
        }

        protected override void OnKeyUp(KeyEventArgs e)
        {
            _isHovered = false;
            _isPressed = false;
            Invalidate();
            base.OnKeyUp(e);
        }

        protected override void SetBoundsCore(int x, int y, int width, int height, BoundsSpecified specified)
        {
            base.SetBoundsCore(x, y, width, height, specified);
            if (DesignMode) SetupScrollBar();
        }

        protected override void OnSizeChanged(EventArgs e)
        {
            base.OnSizeChanged(e);
            SetupScrollBar();
        }

        protected override bool ProcessDialogKey(Keys keyData)
        {
            var keys = Keys.Up;
            var keys2 = Keys.Down;
            if (Orientation == MetroScrollOrientation.Horizontal)
            {
                keys = Keys.Left;
                keys2 = Keys.Right;
            }

            if (keyData == keys)
            {
                Value -= _smallChange;
                return true;
            }

            if (keyData == keys2)
            {
                Value += _smallChange;
                return true;
            }

            switch (keyData)
            {
                case Keys.Prior:
                    Value = GetValue(false, true);
                    return true;
                case Keys.Next:
                    if (_curValue + _largeChange > _maximum)
                        Value = _maximum;
                    else
                        Value += _largeChange;
                    return true;
                case Keys.Home:
                    Value = _minimum;
                    return true;
                case Keys.End:
                    Value = _maximum;
                    return true;
                default:
                    return base.ProcessDialogKey(keyData);
            }
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);
            Invalidate();
        }

        private void SetupScrollBar()
        {
            if (!_inUpdate)
            {
                if (Orientation == MetroScrollOrientation.Vertical)
                {
                    _thumbWidth = Width > 0 ? Width : 10;
                    _thumbHeight = GetThumbSize();
                    _clickedBarRectangle = ClientRectangle;
                    _clickedBarRectangle.Inflate(-1, -1);
                    _thumbRectangle = new Rectangle(ClientRectangle.X, ClientRectangle.Y, _thumbWidth, _thumbHeight);
                    _thumbPosition = _thumbRectangle.Height / 2;
                    _thumbBottomLimitBottom = ClientRectangle.Bottom;
                    _thumbBottomLimitTop = _thumbBottomLimitBottom - _thumbRectangle.Height;
                    _thumbTopLimit = ClientRectangle.Y;
                }
                else
                {
                    _thumbHeight = Height > 0 ? Height : 10;
                    _thumbWidth = GetThumbSize();
                    _clickedBarRectangle = ClientRectangle;
                    _clickedBarRectangle.Inflate(-1, -1);
                    _thumbRectangle = new Rectangle(ClientRectangle.X, ClientRectangle.Y, _thumbWidth, _thumbHeight);
                    _thumbPosition = _thumbRectangle.Width / 2;
                    _thumbBottomLimitBottom = ClientRectangle.Right;
                    _thumbBottomLimitTop = _thumbBottomLimitBottom - _thumbRectangle.Width;
                    _thumbTopLimit = ClientRectangle.X;
                }

                ChangeThumbPosition(GetThumbPosition());
                Refresh();
            }
        }

        private void ResetScrollStatus()
        {
            _bottomBarClicked = _topBarClicked = false;
            StopTimer();
            Refresh();
        }

        private void ProgressTimerTick(object sender, EventArgs e)
        {
            ProgressThumb(true);
        }

        private int GetValue(bool smallIncrement, bool up)
        {
            int num;
            if (up)
            {
                num = _curValue - (smallIncrement ? _smallChange : _largeChange);
                if (num < _minimum) num = _minimum;
            }
            else
            {
                num = _curValue + (smallIncrement ? _smallChange : _largeChange);
                if (num > _maximum) num = _maximum;
            }

            return num;
        }

        private int GetThumbPosition()
        {
            if (_thumbHeight == 0 || _thumbWidth == 0) return 0;
            var num = _metroOrientation == MetroScrollOrientation.Vertical
                ? _thumbPosition / Height / _thumbHeight
                : _thumbPosition / Width / _thumbWidth;
            var num2 = Orientation != MetroScrollOrientation.Vertical ? Width - num : Height - num;
            var num3 = _maximum - _minimum;
            var num4 = 0f;
            if (num3 != 0) num4 = (_curValue - (float)_minimum) / num3;
            return Math.Max(_thumbTopLimit, Math.Min(_thumbBottomLimitTop, Convert.ToInt32(num4 * num2)));
        }

        private int GetThumbSize()
        {
            var num = _metroOrientation == MetroScrollOrientation.Vertical ? Height : Width;
            if (_maximum == 0 || _largeChange == 0) return num;
            var val = _largeChange * (float)num / _maximum;
            return Convert.ToInt32(Math.Min(num, Math.Max(val, 10f)));
        }

        private void EnableTimer()
        {
            if (!_progressTimer.Enabled)
            {
                _progressTimer.Interval = 600;
                _progressTimer.Start();
            }
            else
            {
                _progressTimer.Interval = 10;
            }
        }

        private void StopTimer()
        {
            _progressTimer.Stop();
        }

        private void ChangeThumbPosition(int position)
        {
            if (Orientation == MetroScrollOrientation.Vertical)
                _thumbRectangle.Y = position;
            else
                _thumbRectangle.X = position;
        }

        private void ProgressThumb(bool enableTimer)
        {
            var num = _curValue;
            var type = ScrollEventType.First;
            int num2;
            int num3;
            if (Orientation == MetroScrollOrientation.Vertical)
            {
                num2 = _thumbRectangle.Y;
                num3 = _thumbRectangle.Height;
            }
            else
            {
                num2 = _thumbRectangle.X;
                num3 = _thumbRectangle.Width;
            }

            if (_bottomBarClicked && num2 + num3 < _trackPosition)
            {
                type = ScrollEventType.LargeIncrement;
                _curValue = GetValue(false, false);
                if (_curValue == _maximum)
                {
                    ChangeThumbPosition(_thumbBottomLimitTop);
                    type = ScrollEventType.Last;
                }
                else
                {
                    ChangeThumbPosition(Math.Min(_thumbBottomLimitTop, GetThumbPosition()));
                }
            }
            else if (_topBarClicked && num2 > _trackPosition)
            {
                type = ScrollEventType.LargeDecrement;
                _curValue = GetValue(false, true);
                if (_curValue == _minimum)
                {
                    ChangeThumbPosition(_thumbTopLimit);
                    type = ScrollEventType.First;
                }
                else
                {
                    ChangeThumbPosition(Math.Max(_thumbTopLimit, GetThumbPosition()));
                }
            }

            if (num != _curValue)
            {
                OnScroll(type, num, _curValue, _scrollOrientation);
                Invalidate();
                if (enableTimer) EnableTimer();
            }
        }
    }
}