using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using ClipperLib;
using Emgu.CV;
using Emgu.CV.CvEnum;
using Emgu.CV.Structure;
using Emgu.CV.Util;
using Microsoft.ML.OnnxRuntime;
using Microsoft.ML.OnnxRuntime.Tensors;

namespace OcrLib
{
	internal class DbNet
	{
		private readonly float[] MeanValues = new float[3] { 123.675f, 116.28f, 103.53f };

		private readonly float[] NormValues = new float[3] { 0.0171247534f, 0.0175070018f, 0.0174291953f };

		private InferenceSession dbNet;

		private List<string> inputNames;

		~DbNet()
		{
			dbNet.Dispose();
		}

		public void InitModel(string path, int numThread)
		{
			try
			{
				SessionOptions options = new SessionOptions
				{
					GraphOptimizationLevel = GraphOptimizationLevel.ORT_ENABLE_EXTENDED,
					InterOpNumThreads = numThread,
					IntraOpNumThreads = numThread
				};
				dbNet = new InferenceSession(path, options);
				inputNames = dbNet.InputMetadata.Keys.ToList();
			}
			catch (Exception ex)
			{
				Console.WriteLine(ex.Message + ex.StackTrace);
				throw ex;
			}
		}

		public List<TextBox> GetTextBoxes(Mat src, ScaleParam scale, float boxScoreThresh, float boxThresh, float unClipRatio, bool isPaddle)
		{
			Mat mat = new Mat();
			CvInvoke.Resize(src, mat, new Size(scale.DstWidth, scale.DstHeight));
			Tensor<float> value = OcrUtils.SubstractMeanNormalize(mat, MeanValues, NormValues);
			List<NamedOnnxValue> inputs = new List<NamedOnnxValue> { NamedOnnxValue.CreateFromTensor(inputNames[0], value) };
			try
			{
				using (IDisposableReadOnlyCollection<DisposableNamedOnnxValue> source = dbNet.Run(inputs))
				{
					DisposableNamedOnnxValue[] outputTensor = source.ToArray();
					return isPaddle ? GetPaddleTextBoxes(outputTensor, mat.Rows, mat.Cols, scale, boxScoreThresh, boxThresh, unClipRatio) : GetChineseTextBoxes(outputTensor, mat.Rows, mat.Cols, scale, boxScoreThresh, boxThresh, unClipRatio);
				}
			}
			catch (Exception ex)
			{
				Console.WriteLine(ex.Message + ex.StackTrace);
			}
			return null;
		}

		private static List<TextBox> GetPaddleTextBoxes(DisposableNamedOnnxValue[] outputTensor, int rows, int cols, ScaleParam s, float boxScoreThresh, float boxThresh, float unClipRatio)
		{
			float maxSideThresh = 3f;
			float[] array = outputTensor[0].AsEnumerable<float>().ToArray();
			Mat predMat = new Mat(rows, cols, DepthType.Cv32F, 1);
			predMat.SetTo(array);
			Mat mat = new Mat(rows, cols, DepthType.Cv8U, 1);
			byte[] to = array.Select((float p) => Convert.ToByte(p * 255f)).ToArray();
			mat.SetTo(to);
			Mat mat2 = new Mat();
			CvInvoke.Threshold(mat, mat2, (double)boxThresh * 255.0, 255.0, ThresholdType.Binary);
			Mat mat3 = new Mat();
			Mat structuringElement = CvInvoke.GetStructuringElement(ElementShape.Rectangle, new Size(2, 2), new Point(-1, -1));
			CvInvoke.Dilate(mat2, mat3, structuringElement, new Point(-1, -1), 1, BorderType.Reflect101, new MCvScalar(128.0, 128.0, 128.0));
			VectorOfVectorOfPoint contours = new VectorOfVectorOfPoint();
			CvInvoke.FindContours(mat3, contours, null, RetrType.List, ChainApproxMethod.ChainApproxSimple);
			TextBox[] rsBoxes = new TextBox[contours.Size];
			Parallel.For(0, contours.Size, new ParallelOptions
			{
				MaxDegreeOfParallelism = -1
			}, delegate(int i)
			{
				if (contours[i].Size > 2)
				{
					float minEdgeSize;
					List<PointF> miniBox = GetMiniBox(contours[i], out minEdgeSize);
					if (!(minEdgeSize < maxSideThresh))
					{
						double score = GetScore(contours[i], predMat);
						if (!(score < (double)boxScoreThresh))
						{
							List<Point> list2 = Unclip(miniBox, unClipRatio);
							if (list2 != null)
							{
								List<PointF> miniBox2 = GetMiniBox(list2, out minEdgeSize);
								if (!(minEdgeSize < maxSideThresh + 2f))
								{
									List<Point> list3 = new List<Point>();
									foreach (PointF item2 in miniBox2)
									{
										int val = (int)(item2.X / s.ScaleWidth);
										int x = Math.Min(Math.Max(val, 0), s.SrcWidth);
										int val2 = (int)(item2.Y / s.ScaleHeight);
										int y = Math.Min(Math.Max(val2, 0), s.SrcHeight);
										Point item = new Point(x, y);
										list3.Add(item);
									}
									TextBox textBox = new TextBox
									{
										Score = (float)score,
										Points = list3
									};
									rsBoxes[i] = textBox;
								}
							}
						}
					}
				}
			});
			List<TextBox> list = rsBoxes.Where((TextBox p) => p != null).ToList();
			list.Reverse();
			return list;
		}

		private static List<TextBox> GetChineseTextBoxes(DisposableNamedOnnxValue[] outputTensor, int rows, int cols, ScaleParam s, float boxScoreThresh, float boxThresh, float unClipRatio)
		{
			float minArea = 3f;
			float[] array = outputTensor[0].AsEnumerable<float>().ToArray();
			Mat fMapMat = new Mat(rows, cols, DepthType.Cv32F, 1);
			fMapMat.SetTo(array);
			Mat mat = new Mat(rows, cols, DepthType.Cv8U, 1);
			List<byte> list = array.Select((float p) => (byte)((p > boxThresh) ? 255u : 0u)).ToList();
			mat.SetTo(list.ToArray());
			VectorOfVectorOfPoint contours = new VectorOfVectorOfPoint();
			CvInvoke.FindContours(mat, contours, null, RetrType.List, ChainApproxMethod.ChainApproxSimple);
			TextBox[] rsBoxes = new TextBox[contours.Size];
			Parallel.For(0, contours.Size, new ParallelOptions
			{
				MaxDegreeOfParallelism = -1
			}, delegate(int i)
			{
				float minEdgeSize;
				List<PointF> miniBox = GetMiniBox(contours[i], out minEdgeSize);
				if (!(minEdgeSize < minArea))
				{
					double score = GetScore(contours[i], fMapMat);
					if (!(score < (double)boxScoreThresh))
					{
						List<Point> list3 = Unclip(miniBox, unClipRatio);
						if (list3 != null)
						{
							List<PointF> miniBox2 = GetMiniBox(list3, out minEdgeSize);
							if (!(minEdgeSize < minArea + 2f))
							{
								List<Point> list4 = new List<Point>();
								foreach (PointF item2 in miniBox2)
								{
									int val = (int)(item2.X / s.ScaleWidth);
									int x = Math.Min(Math.Max(val, 0), s.SrcWidth);
									int val2 = (int)(item2.Y / s.ScaleHeight);
									int y = Math.Min(Math.Max(val2, 0), s.SrcHeight);
									Point item = new Point(x, y);
									list4.Add(item);
								}
								TextBox textBox = new TextBox
								{
									Score = (float)score,
									Points = list4
								};
								rsBoxes[i] = textBox;
							}
						}
					}
				}
			});
			List<TextBox> list2 = rsBoxes.Where((TextBox p) => p != null).ToList();
			list2.Reverse();
			return list2;
		}

		private static List<PointF> GetMiniBox(List<Point> contours, out float minEdgeSize)
		{
			VectorOfPoint vectorOfPoint = new VectorOfPoint();
			vectorOfPoint.Push(Enumerable.ToArray(contours));
			return GetMiniBox(vectorOfPoint, out minEdgeSize);
		}

		private static List<PointF> GetMiniBox(VectorOfPoint contours, out float minEdgeSize)
		{
			List<PointF> list = new List<PointF>();
			RotatedRect box = CvInvoke.MinAreaRect(contours);
			PointF[] collection = CvInvoke.BoxPoints(box);
			minEdgeSize = Math.Min(box.Size.Width, box.Size.Height);
			List<PointF> list2 = new List<PointF>(collection);
			list2.Sort(CompareByX);
			int index;
			int index2;
			if (list2[1].Y > list2[0].Y)
			{
				index = 0;
				index2 = 1;
			}
			else
			{
				index = 1;
				index2 = 0;
			}
			int index3;
			int index4;
			if (list2[3].Y > list2[2].Y)
			{
				index3 = 2;
				index4 = 3;
			}
			else
			{
				index3 = 3;
				index4 = 2;
			}
			list.Add(list2[index]);
			list.Add(list2[index3]);
			list.Add(list2[index4]);
			list.Add(list2[index2]);
			return list;
		}

		public static int CompareByX(PointF left, PointF right)
		{
			if (left.X > right.X)
			{
				return 1;
			}
			if (left.X - right.X == 0f)
			{
				return 0;
			}
			return -1;
		}

		private static double GetScore(VectorOfPoint contours, Mat fMapMat)
		{
			short num = 9999;
			short num2 = 0;
			short num3 = 9999;
			short num4 = 0;
			try
			{
				Point[] array = contours.ToArray();
				for (int i = 0; i < array.Length; i++)
				{
					Point point = array[i];
					if (point.X < num)
					{
						num = (short)point.X;
					}
					if (point.X > num2)
					{
						num2 = (short)point.X;
					}
					if (point.Y < num3)
					{
						num3 = (short)point.Y;
					}
					if (point.Y > num4)
					{
						num4 = (short)point.Y;
					}
				}
				int num5 = num2 - num + 1;
				int num6 = num4 - num3 + 1;
				Image<Gray, float> image = fMapMat.ToImage<Gray, float>();
				Image<Gray, float> image2 = new Image<Gray, float>(num5, num6);
				float[,,] data = image.Data;
				float[,,] data2 = image2.Data;
				for (int j = num3; j < num3 + num6; j++)
				{
					for (int k = num; k < num + num5; k++)
					{
						try
						{
							data2[j - num3, k - num, 0] = data[j, k, 0];
						}
						catch (Exception ex)
						{
							Console.WriteLine(ex.Message);
						}
					}
				}
				Mat mat = Mat.Zeros(num6, num5, DepthType.Cv8U, 1);
				List<Point> list = new List<Point>();
				Point[] array2 = contours.ToArray();
				for (int l = 0; l < array2.Length; l++)
				{
					Point point2 = array2[l];
					list.Add(new Point(point2.X - num, point2.Y - num3));
				}
				using (VectorOfPoint vectorOfPoint = new VectorOfPoint(Enumerable.ToArray(list)))
				{
					using (VectorOfVectorOfPoint points = new VectorOfVectorOfPoint(vectorOfPoint))
					{
						CvInvoke.FillPoly(mat, points, new MCvScalar(1.0));
					}
				}
				return CvInvoke.Mean(image2, mat).V0;
			}
			catch (Exception ex2)
			{
				Console.WriteLine(ex2.Message + ex2.StackTrace);
			}
			return 0.0;
		}

		private static List<Point> Unclip(List<PointF> box, float unclip_ratio)
		{
			RotatedRect rotatedRect = CvInvoke.MinAreaRect(box.ToArray());
			if ((double)rotatedRect.Size.Height < 1.001 && (double)rotatedRect.Size.Width < 1.001)
			{
				return null;
			}
			List<IntPoint> list = new List<IntPoint>();
			foreach (PointF item2 in box)
			{
				IntPoint item = new IntPoint((int)item2.X, (int)item2.Y);
				list.Add(item);
			}
			float num = Math.Abs(SignedPolygonArea(Enumerable.ToArray(box)));
			double num2 = LengthOfPoints(box);
			double delta = (double)(num * unclip_ratio) / num2;
			ClipperOffset clipperOffset = new ClipperOffset();
			clipperOffset.AddPath(list, JoinType.jtRound, EndType.etClosedPolygon);
			List<List<IntPoint>> solution = new List<List<IntPoint>>();
			clipperOffset.Execute(ref solution, delta);
			if (solution.Count == 0)
			{
				return null;
			}
			List<Point> list2 = new List<Point>();
			foreach (IntPoint item3 in solution[0])
			{
				list2.Add(new Point((int)item3.X, (int)item3.Y));
			}
			return list2;
		}

		private static float SignedPolygonArea(PointF[] points)
		{
			int num = points.Length;
			PointF[] array = new PointF[num + 1];
			points.CopyTo(array, 0);
			array[num] = points[0];
			float num2 = 0f;
			for (int i = 0; i < num; i++)
			{
				num2 += (array[i + 1].X - array[i].X) * (array[i + 1].Y + array[i].Y) / 2f;
			}
			return num2;
		}

		private static double LengthOfPoints(IList<PointF> box)
		{
			double num = 0.0;
			PointF item = box[0];
			double num2 = item.X;
			double num3 = item.Y;
			box.Add(item);
			int count = box.Count;
			for (int i = 1; i < count; i++)
			{
				PointF pointF = box[i];
				double num4 = pointF.X;
				double num5 = pointF.Y;
				double num6 = num4 - num2;
				double num7 = num5 - num3;
				num += Math.Sqrt(num6 * num6 + num7 * num7);
				num2 = num4;
				num3 = num5;
			}
			box.RemoveAt(count - 1);
			return num;
		}
	}
}
