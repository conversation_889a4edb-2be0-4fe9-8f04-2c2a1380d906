// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using UIAutomationClient;

namespace System.Windows.Automation
{
    public sealed class CacheRequest
    {
        public static readonly CacheRequest DefaultCacheRequest = new CacheRequest();

        public CacheRequest()
        {
            NativeCacheRequest = Automation.Factory.CreateCacheRequest();
        }

        public static CacheRequest Current => DefaultCacheRequest;

        public static IUIAutomationCacheRequest CurrentNativeCacheRequest => Current.NativeCacheRequest;

        internal IUIAutomationCacheRequest NativeCacheRequest { get; }
    }
}