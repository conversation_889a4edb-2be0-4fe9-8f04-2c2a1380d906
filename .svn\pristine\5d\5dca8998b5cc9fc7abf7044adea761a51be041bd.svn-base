﻿using System;
using System.Collections;
using System.Windows.Forms;

namespace MetroFramework.Controls
{
    public class ListViewColumnSorter : IComparer
    {
        public enum SortModifiers
        {
            SortByImage,
            SortByCheckbox,
            SortByText
        }

        public int ColumnToSort;

        public SortOrder OrderOfSort;

        private CaseInsensitiveComparer ObjectCompare;

        public SortModifiers _SortModifier { get; set; } = SortModifiers.SortByText;

        public int SortColumn
        {
            get
            {
                return ColumnToSort;
            }
            set
            {
                ColumnToSort = value;
            }
        }

        public SortOrder Order
        {
            get
            {
                return OrderOfSort;
            }
            set
            {
                OrderOfSort = value;
            }
        }

        public ListViewColumnSorter()
        {
            ColumnToSort = 0;
            ObjectCompare = new CaseInsensitiveComparer();
        }

        public int Compare(object x, object y)
        {
            ListViewItem listViewItem = (ListViewItem)x;
            ListViewItem listViewItem2 = (ListViewItem)y;
            DateTime result;
            DateTime result2;
            int num = (!DateTime.TryParse(listViewItem.SubItems[ColumnToSort].Text, out result) || !DateTime.TryParse(listViewItem2.SubItems[ColumnToSort].Text, out result2)) ? ObjectCompare.Compare(listViewItem.SubItems[ColumnToSort].Text, listViewItem2.SubItems[ColumnToSort].Text) : ObjectCompare.Compare(result, result2);
            if (OrderOfSort == SortOrder.Ascending)
            {
                return num;
            }
            if (OrderOfSort == SortOrder.Descending)
            {
                return -num;
            }
            return 0;
        }
    }
}
