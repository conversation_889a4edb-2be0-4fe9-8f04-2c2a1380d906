﻿using MetroFramework.Forms;
using System;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmForgetPwd : MetroForm
    {
        private const int N_TOTAL_SECOND = 120;

        private int _nowSeconds;

        public FrmForgetPwd()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            CommonMethod.SetStyle(lnkValidateCode, ControlStyles.Selectable, false);
            lnkValidateCode.TabStop = false;
            Load += FrmForgetPwd_Load;
            this.AddContactUserBtn();
        }

        public string Account { get; internal set; }
        public string Pwd { get; internal set; }

        private void FrmForgetPwd_Load(object sender, EventArgs e)
        {
            if (Account != null) txtAccount.Text = Account;
        }

        private void lnkValidateCode_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            if (!lnkValidateCode.Text.Equals("验证码")) return;
            var account = txtAccount.Text.Trim();
            if (string.IsNullOrEmpty(account))
            {
                MessageBox.Show(this, "账号不能为空！", CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtAccount.Focus();
                return;
            }

            var isEmail = Regex.IsMatch(account, "^\\s*([A-Za-z0-9_-]+(\\.\\w+)*@(\\w+\\.)+\\w{2,5})\\s*$");
            var isMobile = Regex.IsMatch(account, @"^[1]\d{10}");
            if (!isEmail && !isMobile)
            {
                MessageBox.Show(this, "账号格式不正确，必须为手机号或者邮箱！", CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtAccount.SelectAll();
                txtAccount.Focus();
                return;
            }

            var strMsg = "";
            var result = OcrHelper.SendResetPwdMsg(account, isMobile, ref strMsg);
            if (!result)
            {
                if (string.IsNullOrEmpty(strMsg)) strMsg = "获取验证码，请稍候重试！";
                MessageBox.Show(this, strMsg, CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            else
            {
                _nowSeconds = 0;
                tmrTick.Enabled = true;
            }
        }

        private void tmrTick_Tick(object sender, EventArgs e)
        {
            _nowSeconds++;
            if (N_TOTAL_SECOND - _nowSeconds > 0)
            {
                lnkValidateCode.Text = string.Format("{0}秒", N_TOTAL_SECOND - _nowSeconds);
            }
            else
            {
                lnkValidateCode.Text = "验证码";
                tmrTick.Enabled = false;
            }
        }

        private void btnReg_Click(object sender, EventArgs e)
        {
            var account = txtAccount.Text.Trim();
            var isEmail = Regex.IsMatch(account, "^\\s*([A-Za-z0-9_-]+(\\.\\w+)*@(\\w+\\.)+\\w{2,5})\\s*$");
            var isMobile = Regex.IsMatch(account, @"^[1]\d{10}");
            if (!isEmail && !isMobile)
            {
                MessageBox.Show(this, "账号格式不正确，必须为手机号或者邮箱！", CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtAccount.SelectAll();
                txtAccount.Focus();
                return;
            }

            var code = txtCode.Text.Trim();
            if (string.IsNullOrEmpty(code))
            {
                MessageBox.Show(this, "请填写验证码，如果收不到，请尝试用更换账号类型(手机/邮箱)！", CommonString.StrReminder, MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
                txtCode.Focus();
                return;
            }

            var pwd = txtPwd.Text.Trim();
            if (string.IsNullOrEmpty(pwd))
            {
                MessageBox.Show(this, "新密码不能为空，必须为6-15位的数字或大小写字母！", CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtPwd.Focus();
                return;
            }

            //使用regex进行格式设置 至少有数字、大小写字母，最少8个字符、最长30个字符
            if (!new Regex(@"[0-9A-Za-z].{6,15}").IsMatch(pwd)) //判断密码格式是否符合要求
            {
                MessageBox.Show(this, "新密码必须为6-15位的数字或大小写字母！", CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtPwd.Focus();
                return;
            }

            var strMsg = "";
            var result = OcrHelper.SendResetPwdInfo(account, pwd, code, ref strMsg);
            if (result)
            {
                Account = account;
                Pwd = pwd;
                DialogResult = DialogResult.OK;
                Close();
            }
            else
            {
                if (string.IsNullOrEmpty(strMsg)) strMsg = "重置密码失败，请稍候重试！";
                MessageBox.Show(this, strMsg, CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}