﻿using MetroFramework.Forms;
using OCRTools.Common;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmBatch : MetroForm
    {
        private const string STR_PROCESS_FINISHED = "处理成功";
        private const string STR_PROCESS_FAILED = "处理失败";
        private const string STR_PROCESSING = "处理中…";

        internal static BlockingCollection<OcrContent> OcrResultPool = new BlockingCollection<OcrContent>();

        private readonly List<BatchOcrItem> _ocrItems = new List<BatchOcrItem>();

        private readonly string _strOutDir =
            Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), Application.ProductName);

        private BindingList<BatchOcrItem> _bindingItems;

        public FrmBatch()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            dgContent.AutoGenerateColumns = false;
            CheckForIllegalCrossThreadCalls = false;
        }

        private void FrmBatch_Load(object sender, EventArgs e)
        {
            InitOcrType();
            InitOcrOutType();
            OcrBatchResultProcessThread();
        }

        private void InitOcrType()
        {
            try
            {
                foreach (OcrType type in Enum.GetValues(typeof(OcrType)))
                {
                    if (CheckIsForbidOperate(type)) continue;
                    cmbOcrTypes.Items.Add(type.ToString());
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine("加载OCR类型失败！" + oe.Message);
            }

            if (cmbOcrTypes.SelectedIndex < 0) cmbOcrTypes.SelectedIndex = 0;
        }

        private bool CheckIsForbidOperate(OcrType ocrType)
        {
            var isForbid = Equals(ocrType, OcrType.竖排) && Program.NowUser?.IsSupportVertical != true
                           || Equals(ocrType, OcrType.表格) && Program.NowUser?.IsSupportTable != true
                           || Equals(ocrType, OcrType.公式) && Program.NowUser?.IsSupportMath != true
                           || Equals(ocrType, OcrType.翻译) && Program.NowUser?.IsSupportTranslate != true;

            return isForbid;
        }

        private void InitOcrOutType()
        {
            try
            {
                foreach (OcrFileType type in Enum.GetValues(typeof(OcrFileType)))
                    cmbOutExt.Items.Add(type.ToString().ToUpper());
            }
            catch (Exception oe)
            {
                Console.WriteLine("加载输出类型失败！" + oe.Message);
            }

            if (cmbOutExt.SelectedIndex < 0) cmbOutExt.SelectedIndex = cmbOcrTypes.Items.Count;
            cmbOutExt.Enabled = false;
        }

        private void btnClearFiles_Click(object sender, EventArgs e)
        {
            _ocrItems.Clear();
            ReBindGridView();
        }

        private void btnClearSuccess_Click(object sender, EventArgs e)
        {
            _ocrItems.RemoveAll(p => Equals(p.State, STR_PROCESS_FINISHED));
            ReBindGridView();
        }

        private void btnAddFiles_Click(object sender, EventArgs e)
        {
            var openFile = new OpenFileDialog
            {
                Title = "请选择文件",
                Filter = "图片或文档|*.png;*.jpg;*.jpeg;*.bmp;*.pdf;*.doc;*.docx;*.txt;",
                RestoreDirectory = true,
                Multiselect = true
            };
            if (openFile.ShowDialog(this) == DialogResult.OK && openFile.FileNames.Length > 0)
                foreach (var item in openFile.FileNames)
                {
                    if (_ocrItems.Exists(p => Equals(p.FullName, item))) continue;
                    var ocrItem = new BatchOcrItem
                    {
                        Id = Guid.NewGuid().ToString(),
                        FullName = item,
                        FileName = Path.GetFileName(item),
                        FileType = (OcrFileType)Enum.Parse(typeof(OcrFileType), cmbOutExt.Text, true),
                        OcrType = (OcrType)Enum.Parse(typeof(OcrType), cmbOcrTypes.Text, true),
                        State = "待处理"
                    };
                    _ocrItems.Add(ocrItem);
                }

            ReBindGridView();
        }

        private void ReBindGridView()
        {
            _bindingItems = new BindingList<BatchOcrItem>(_ocrItems);
            _bindingItems.ListChanged += (sender, e) =>
            {
                if (e.ListChangedType == ListChangedType.ItemDeleted
                    || e.ListChangedType == ListChangedType.ItemAdded)
                    _bindingItems.ResetBindings();
            };
            dgContent.DataSource = _bindingItems;
            dgContent.Refresh();
        }

        private void btnProcess_Click(object sender, EventArgs e)
        {
            btnProcess.Text = "正在处理…";
            btnProcess.Enabled = false;
            Task.Factory.StartNew(() =>
            {
                try
                {
                    Parallel.ForEach(_ocrItems,
                        new ParallelOptions { MaxDegreeOfParallelism = BoxUtil.GetInt32FromObject(txtPerCount.Text) },
                        item =>
                        {
                            if (Equals(item.State, STR_PROCESS_FINISHED)) return;
                            item.State = STR_PROCESSING;
                            dgContent.InvalidateColumn(3);

                            FrmMain.DragDropEventDelegate.Invoke(new List<string> { item.FullName }, null, item.OcrType,
                                ProcessBy.批量识别, item.Id);

                            Thread.Sleep(new Random().Next(500, 2000));
                        });
                }
                catch
                {
                }

                btnProcess.Text = "开始识别(&S)";
                btnProcess.Enabled = true;
            });
        }

        private void OcrBatchResultProcessThread()
        {
            new Thread(t =>
                {
                    try
                    {
                        foreach (var content in OcrResultPool.GetConsumingEnumerable())
                            try
                            {
                                if (!string.IsNullOrEmpty(content.Identity))
                                {
                                    var strState = content.result?.HasResult == true
                                        ? STR_PROCESS_FINISHED
                                        : STR_PROCESS_FAILED;
                                    _ocrItems.FindAll(p => Equals(p.Id, content.Identity)).ForEach(p =>
                                    {
                                        CommonResult.SaveFile(content, _strOutDir, Path.GetFileName(p.FullName));
                                        if (!Equals(p.State, STR_PROCESS_FINISHED))
                                            p.State = strState;
                                    });
                                    dgContent.InvalidateColumn(3);
                                }
                                else
                                {
                                    Console.WriteLine("");
                                }
                            }
                            catch (Exception oe)
                            {
                                Console.WriteLine(oe.Message);
                            }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
                })
            { IsBackground = true, Priority = ThreadPriority.Highest }.Start();
        }

        private void linkLabel1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            Process.Start(_strOutDir);
        }
    }

    [Obfuscation]
    internal class BatchOcrItem
    {
        private string _state;

        [Obfuscation] public string Id { get; set; }

        [Obfuscation] public string FileName { get; set; }

        [Obfuscation] public string FullName { get; set; }

        [Obfuscation] public OcrType OcrType { get; set; }

        [Obfuscation] public OcrFileType FileType { get; set; }

        [Obfuscation]
        public string State
        {
            get => _state;
            set
            {
                _state = value;
                NotifyPropertyChanged("State");
            }
        }

        [Obfuscation] public event PropertyChangedEventHandler PropertyChanged;

        [Obfuscation]
        private void NotifyPropertyChanged(string propertyName)
        {
            if (PropertyChanged != null)
                try
                {
                    PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
                }
                catch
                {
                    // ignored
                }
        }
    }
}