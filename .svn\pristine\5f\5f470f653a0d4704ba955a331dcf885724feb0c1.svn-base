﻿using OCRTools.Language;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools.UserControlEx
{
    public class LabelWithTip : Label
    {
        public LabelWithTip()
        {
            TextChanged += CheckBoxWithTip_TextChanged;
        }

        private void CheckBoxWithTip_TextChanged(object sender, EventArgs e)
        {
            InitPicBox();
        }

        private string tipText;

        public string TipText { get => tipText; set { tipText = value; InitPicBox(); } }

        public ToolTip TipControl { get; set; }
        public Bitmap TipIcon { get; set; }

        private PictureBox pictureBox;

        public bool LimitMaxWidth { get; set; } = true;

        private void InitPicBox()
        {
            if (string.IsNullOrEmpty(tipText) || TipControl == null)
            {
                return;
            }
            AutoSize = true;
            if (pictureBox == null)
            {
                pictureBox = new PictureBox
                {
                    BackColor = this.BackColor,
                    Image = TipIcon ?? Properties.Resources.帮助,
                    SizeMode = PictureBoxSizeMode.CenterImage,
                    TabStop = false,
                    Padding = new Padding(-3, 0, 0, 0)
                };
                pictureBox.Size = new Size(this.Height, this.Height);
                Controls.Add(pictureBox);
            }
            pictureBox.Top = (int)((this.ClientRectangle.Height - pictureBox.Height) / 2 * CommonTheme.DpiScale);
            pictureBox.Left = TextRenderer.MeasureText(Text, Font).Width;
            AutoSize = false;
            Width = pictureBox.Left + pictureBox.Width;
            if (LimitMaxWidth && Parent != null)
            {
                var maxWidth = (Math.Max(Parent.PreferredSize.Width, this.Parent.Width) - 22 * 2) / 2;
                if (Width > maxWidth)
                {
                    pictureBox.Left -= Width - maxWidth;
                    this.Width = maxWidth;
                }
            }
            this.BringToFront();
            pictureBox.BringToFront();

            if (TipControl != null)
            {
                TipControl.SetToolTip(this, Text);
                TipControl.SetToolTip(pictureBox, TipText.CurrentText());
            }
        }
    }
}
