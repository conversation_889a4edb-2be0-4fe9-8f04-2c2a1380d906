using UtfUnknown.Core.Models;

namespace UtfUnknown.Core.Probers
{
    public class CodingStateMachine
    {
        private readonly StateMachineModel model;
        private int currentState;

        public CodingStateMachine(StateMachineModel model)
        {
            currentState = 0;
            this.model = model;
        }

        public int CurrentCharLen { get; private set; }

        public string ModelName => model.Name;

        public int NextState(byte b)
        {
            var @class = model.GetClass(b);
            if (currentState == 0) CurrentCharLen = model.charLenTable[@class];
            currentState = model.stateTable.Unpack(currentState * model.ClassFactor + @class);
            return currentState;
        }

        public void Reset()
        {
            currentState = 0;
        }
    }
}