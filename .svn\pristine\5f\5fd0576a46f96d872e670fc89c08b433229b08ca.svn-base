﻿using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;

namespace OCRTools
{
    public static class IniHelper
    {
        [DllImport("kernel32", CharSet = CharSet.Ansi)]
        private static extern int GetPrivateProfileString(byte[] sectionName, byte[] key, string defaultValue, byte[] returnBuffer, int size, string filePath);

        [DllImport("kernel32", CharSet = CharSet.Ansi)]
        private static extern long WritePrivateProfileString(byte[] sectionName, byte[] key, byte[] value, string filePath);

        public static string GetValue(string sectionName, string key, string defaultValue = null)
        {
            string result = null;
            try
            {
                if (!string.IsNullOrEmpty(key))
                {
                    InitIniFile(CommonString.IniFileName);
                    var array = new byte[1024 * 5];
                    var privateProfileString =
                        GetPrivateProfileString(Encoding.UTF8.GetBytes(sectionName), Encoding.UTF8.GetBytes(key), string.Empty, array, array.Length, CommonString.IniFileName);
                    result = Encoding.UTF8.GetString(array, 0, privateProfileString);
                    array = null;
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("IniHelper.GetValue", oe);
            }

            if (string.IsNullOrEmpty(result) && !string.IsNullOrEmpty(defaultValue)) result = defaultValue;
            return result;
        }

        public static bool SetValue(string sectionName, string key, string value, string iniFileName = null)
        {
            if (string.IsNullOrEmpty(iniFileName))
            {
                iniFileName = CommonString.IniFileName;
            }
            InitIniFile(iniFileName);
            var result = (int)WritePrivateProfileString(Encoding.UTF8.GetBytes(sectionName), Encoding.UTF8.GetBytes(key), Encoding.UTF8.GetBytes(value), iniFileName) > 0;
            return result;
        }

        private static void InitIniFile(string fileName)
        {
            if (!File.Exists(fileName))
                using (File.Create(fileName))
                {
                }
        }
    }
}