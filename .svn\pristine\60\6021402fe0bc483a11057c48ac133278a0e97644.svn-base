using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace CatchTools.Gif
{
	public class FmHide : Form
	{
		private IContainer components = null;

		public FmHide()
		{
			InitializeComponent();
			base.Shown += delegate
			{
				Close();
			};
		}

		protected override void Dispose(bool disposing)
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
			SuspendLayout();
			base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 12f);
			base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			base.ClientSize = new System.Drawing.Size(69, 30);
			base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
			base.Name = "FmHide";
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.StartPosition = System.Windows.Forms.FormStartPosition.Manual;
			Text = "Fmhide";
			ResumeLayout(false);
		}
	}
}
