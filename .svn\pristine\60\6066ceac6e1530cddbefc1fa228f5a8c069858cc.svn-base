// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Collections;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public sealed class CacheRequest
    {
        [ThreadStatic] private static Stack _cacheStack;

        internal static readonly CacheRequest DefaultCacheRequest = new CacheRequest();
        private int _cRef;
        private object _lock;

        public CacheRequest()
        {
            NativeCacheRequest = Automation.Factory.CreateCacheRequest();
            _lock = new object();
        }

        public static CacheRequest Current
        {
            get
            {
                if (_cacheStack != null && _cacheStack.Count != 0) return (CacheRequest) _cacheStack.Peek();
                return DefaultCacheRequest;
            }
        }

        internal static IUIAutomationCacheRequest CurrentNativeCacheRequest => Current.NativeCacheRequest;

        internal IUIAutomationCacheRequest NativeCacheRequest { get; }
    }
}