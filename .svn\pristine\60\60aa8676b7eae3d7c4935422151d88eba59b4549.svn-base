﻿using OCRTools.Common;
using OCRTools.Language;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Reflection;
using System.Windows.Forms;

namespace OCRTools
{
    public class ScrollMsg
    {
        private static List<ScrollEntity> _lstMsg = new List<ScrollEntity>();

        private static ScrollingText _srcTxt;

        public static void ShowToWindow(Control owner, string text, Point location, Size size)
        {
            if (_srcTxt == null || _srcTxt.IsDisposed) Init();
            if (_srcTxt != null)
            {
                _srcTxt.Visible = false;
                _srcTxt.ScrollText = text;
                _srcTxt.Parent = owner;
                _srcTxt.Location = location;
                _srcTxt.Size = size;
                _srcTxt.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
                _srcTxt.IsCanClose = false;
                _srcTxt.BackColor = owner.BackColor;
                _srcTxt.Visible = true;
                _srcTxt.BringToFront();
            }
        }

        private static void Init()
        {
            _srcTxt = new ScrollingText
            {
                NowVerticleTextPosition = VerticleTextPosition.Center,
                TextScrollSpeed = 60,
                MaxLoopTimes = 3,
                StopScrollOnMouseOver = false,
                ShowBorder = false,
                Font = CommonString.GetSysNormalFont(24F),
                ForeColor = Color.Red
            };
        }
    }

    public class UserMsgHelper
    {
        public static void GetUserMsg()
        {
            var lstMsg = GetMsg();
            for (int i = 0; i < lstMsg.Count; i++)
            {
                var msg = lstMsg[i];
                if (!string.IsNullOrEmpty(msg.Title))
                    msg.Title = msg.Title.CurrentText(true);
                if (!string.IsNullOrEmpty(msg.Desc))
                    msg.Desc = msg.Desc.CurrentText(true);
                if (!string.IsNullOrEmpty(msg.Content))
                    msg.Content = msg.Content.CurrentText(true);
            }
            ProcessUserLevelMsg(lstMsg);
            //var msg = new UserMsgEntity()
            //{
            //    Id = Guid.NewGuid().ToString().Replace("-", ""),
            //    Content = "【过年啦】新春快乐，龙年龘（dà）吉！\n\n祝您在新的一年里：\n龙行龘龘（dá），威震八荒！\r\n前程朤朤（lǎng ），光照万丈！\r\n生活䲜䲜（yè），富饶丰裕！\r\n福禄穰穰（ráng），满门喜庆！\r\n人生璨璨（càn），尽显辉煌！\n\n2024，助手给您最美好诚挚的祝福！\n感谢您一如既往的支持！\n　",
            //    ShowSecond = 600,
            //    IntervalHour = 18
            //};
            //lstMsg = new List<UserMsgEntity>() { msg };
            //Console.WriteLine(CommonString.JavaScriptSerializer.Serialize(lstMsg));
            lstMsg?.ForEach(ProcessMsg);
        }

        private static void ProcessUserLevelMsg(List<UserMsgEntity> lstMsg)
        {
            var config = CommonConfig.GetServerConfig();
            if (config == null || config.Count <= 0)
            {
                return;
            }

            var isLogined = Program.IsLogined();
            var isUserExpired = isLogined && Program.NowUser.DtExpired <= ServerTime.DateTime;
            var isReged = isLogined || !string.IsNullOrEmpty(CommonSetting.用户名);

            config.ForEach(mConfig =>
            {
                if (string.IsNullOrEmpty(mConfig.Content) || !mConfig.IsTipMsg || !CommonMethod.IsMatchUserLevel(mConfig.ShowLevel))
                {
                    return;
                }
                switch (mConfig.Id)
                {
                    case "ExpTipMsg":
                        if (isUserExpired || !isLogined)
                        {
                            return;
                        }
                        try
                        {
                            var ts = new TimeSpan(Program.NowUser.DtExpired.Ticks - ServerTime.DateTime.Ticks);
                            if (ts.TotalDays <= 0 || ts.TotalDays > 15)
                            {
                                return;
                            }
                            mConfig.Content = mConfig.Content
                                .Replace("[版本]", Program.NowUser.UserTypeName.CurrentText())
                                .Replace("[天数]", ts.TotalDays > 0 ? ts.TotalDays.ToString("F0") + "天后" : "");
                        }
                        catch (Exception oe)
                        {
                            Log.WriteError("ExpTipMsg", oe);
                            return;
                        }
                        break;
                    case "RegTipMsg":
                        if (isReged)
                        {
                            return;
                        }
                        break;
                    case "UpgradeMsg":
                        if (!isUserExpired)
                        {
                            return;
                        }
                        break;
                }
                lstMsg.Add(mConfig);
            });
        }

        private static void ProcessMsg(UserMsgEntity msg)
        {
            //获取消息失败
            if (msg == null || string.IsNullOrEmpty(msg.Id))
            {
                return;
            }
            //不满足会员等级条件，不展示
            if (!CommonMethod.IsMatchUserLevel(msg.ShowLevel))
            {
                return;
            }
            //获取上次展示时间
            var lastShowTimeStr = IniHelper.GetValue("UserMsg", msg.Id);
            if (!string.IsNullOrEmpty(lastShowTimeStr))
            {
                DateTime.TryParse(lastShowTimeStr, out DateTime dtLast);
                var nextTime = dtLast.AddHours(msg.IntervalHour <= 0 ? 6 : msg.IntervalHour);
                //未到展示时间，跳过
                if (nextTime > ServerTime.DateTime)
                {
                    return;
                }
            }
            msg.ShowSecond = msg.ShowSecond < 5 ? 5 : msg.ShowSecond;
            msg.Title = msg.Title + (string.IsNullOrEmpty(msg.Title) ? "" : "-") + CommonString.FullName.CurrentText();
            IniHelper.SetValue("UserMsg", msg.Id, ServerTime.DateTime.ToDateStr("yyyy-MM-dd HH:mm:ss"));
            CommonMethod.ShowNotificationTip(msg.Content, msg.Title, msg.ShowSecond * 1000
                , msg.Font, msg.FontSize, msg.ForeColor, msg.BackColor, msg.Link);
        }

        private static List<UserMsgEntity> GetMsg()
        {
            var lstMsg = new List<UserMsgEntity>();
            var result = string.Empty;
            try
            {
                result =
                   WebClientExt.GetHtml(CommonString.HostUpdate?.FullUrl + "update/umsg.txt?t=" + ServerTime.DateTime.Ticks, 5);
                if (!string.IsNullOrEmpty(result) && result.Length > 2)
                    lstMsg = result.DeserializeJson<List<UserMsgEntity>>();
            }
            catch (Exception oe)
            {
                Log.WriteError("GetMsg:" + result, oe);
            }
            return lstMsg;
        }
    }

    [Obfuscation]
    public class UserMsgEntity : BaseMsgEntity
    {
        [Obfuscation] public bool IsTipMsg { get; set; } = true;

        /// <summary>
        /// 内容
        /// </summary>
        [Obfuscation] public string Content { get; set; }

        /// <summary>
        /// 链接
        /// </summary>
        [Obfuscation] public string Link { get; set; }

        /// <summary>
        /// 展示时长
        /// </summary>
        [Obfuscation] public int ShowSecond { get; set; }

        /// <summary>
        /// 下次展示间隔小时数
        /// </summary>
        [Obfuscation] public double IntervalHour { get; set; }

    }
}