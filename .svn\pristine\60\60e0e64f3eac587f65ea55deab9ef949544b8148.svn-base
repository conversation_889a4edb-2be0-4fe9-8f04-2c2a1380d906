namespace OCRTools
{
    internal class CommandAdd : Command
    {
        private DrawObject drawObject;

        public CommandAdd(DrawObject drawObject)
        {
            this.drawObject = drawObject.Clone();
        }

        public override void Undo(GraphicsList list)
        {
            list.DeleteLastAddedObject();
        }

        public void ChangeText(DrawObject drawObject, GraphicsList list)
        {
            this.drawObject = drawObject;
        }

        public override void Redo(GraphicsList list)
        {
            list.UnselectAll();
            list.Add(drawObject);
        }
    }
}