﻿using System.Drawing;
using System.Drawing.Drawing2D;
using System.Reflection;

namespace ShareX.ScreenCaptureLib
{
    [Obfuscation]
    public class TextDrawingOptions
    {
        [Obfuscation]
        public string Font { get; set; } = OCRTools.CommonString.StrDefaultFontName;

        [Obfuscation]
        public int Size { get; set; } = 18;

        [Obfuscation]
        public Color Color { get; set; } = Color.White;

        [Obfuscation]
        public bool Bold { get; set; } = false;

        [Obfuscation]
        public bool Italic { get; set; } = false;

        [Obfuscation]
        public bool Underline { get; set; } = false;

        [Obfuscation]
        public StringAlignment AlignmentHorizontal { get; set; } = StringAlignment.Center;

        [Obfuscation]
        public StringAlignment AlignmentVertical { get; set; } = StringAlignment.Center;

        [Obfuscation]
        public FontStyle Style
        {
            get
            {
                FontStyle style = FontStyle.Regular;

                if (Bold)
                {
                    style |= FontStyle.Bold;
                }

                if (Italic)
                {
                    style |= FontStyle.Italic;
                }

                if (Underline)
                {
                    style |= FontStyle.Underline;
                }

                return style;
            }
        }

        [Obfuscation]
        public bool Gradient { get; set; } = false;

        [Obfuscation]
        public Color Color2 { get; set; } = Color.FromArgb(240, 240, 240);

        [Obfuscation]
        public LinearGradientMode GradientMode { get; set; } = LinearGradientMode.Vertical;

        [Obfuscation]
        public bool EnterKeyNewLine { get; set; } = false;
    }
}