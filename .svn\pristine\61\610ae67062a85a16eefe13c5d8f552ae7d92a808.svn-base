﻿using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace OCRTools.UserControlEx
{
	[ToolboxItem(false)]
	public class TabStyleRoundedProvider : TabStyleDefaultProvider
	{
		public TabStyleRoundedProvider(TabControlExtra tabControl)
			: base(tabControl)
		{
			base.Radius = 10;
			base.SelectedTabIsLarger = false;
			base.Padding = new Point(6, 3);
		}

		public override void AddTabBorder(GraphicsPath path, Rectangle tabBounds)
		{
			switch (base.TabControl.Alignment)
			{
				case TabAlignment.Top:
					path.AddLine(tabBounds.X, tabBounds.Bottom, tabBounds.X, tabBounds.Y + base.Radius);
					path.AddArc(tabBounds.X, tabBounds.Y, base.Radius * 2, base.Radius * 2, 180f, 90f);
					path.AddLine(tabBounds.X + base.Radius, tabBounds.Y, tabBounds.Right - base.Radius, tabBounds.Y);
					path.AddArc(tabBounds.Right - base.Radius * 2, tabBounds.Y, base.Radius * 2, base.Radius * 2, 270f, 90f);
					path.AddLine(tabBounds.Right, tabBounds.Y + base.Radius, tabBounds.Right, tabBounds.Bottom);
					break;
				case TabAlignment.Bottom:
					path.AddLine(tabBounds.Right, tabBounds.Y, tabBounds.Right, tabBounds.Bottom - base.Radius);
					path.AddArc(tabBounds.Right - base.Radius * 2, tabBounds.Bottom - base.Radius * 2, base.Radius * 2, base.Radius * 2, 0f, 90f);
					path.AddLine(tabBounds.Right - base.Radius, tabBounds.Bottom, tabBounds.X + base.Radius, tabBounds.Bottom);
					path.AddArc(tabBounds.X, tabBounds.Bottom - base.Radius * 2, base.Radius * 2, base.Radius * 2, 90f, 90f);
					path.AddLine(tabBounds.X, tabBounds.Bottom - base.Radius, tabBounds.X, tabBounds.Y);
					break;
				case TabAlignment.Left:
					path.AddLine(tabBounds.Right, tabBounds.Bottom, tabBounds.X + base.Radius, tabBounds.Bottom);
					path.AddArc(tabBounds.X, tabBounds.Bottom - base.Radius * 2, base.Radius * 2, base.Radius * 2, 90f, 90f);
					path.AddLine(tabBounds.X, tabBounds.Bottom - base.Radius, tabBounds.X, tabBounds.Y + base.Radius);
					path.AddArc(tabBounds.X, tabBounds.Y, base.Radius * 2, base.Radius * 2, 180f, 90f);
					path.AddLine(tabBounds.X + base.Radius, tabBounds.Y, tabBounds.Right, tabBounds.Y);
					break;
				case TabAlignment.Right:
					path.AddLine(tabBounds.X, tabBounds.Y, tabBounds.Right - base.Radius, tabBounds.Y);
					path.AddArc(tabBounds.Right - base.Radius * 2, tabBounds.Y, base.Radius * 2, base.Radius * 2, 270f, 90f);
					path.AddLine(tabBounds.Right, tabBounds.Y + base.Radius, tabBounds.Right, tabBounds.Bottom - base.Radius);
					path.AddArc(tabBounds.Right - base.Radius * 2, tabBounds.Bottom - base.Radius * 2, base.Radius * 2, base.Radius * 2, 0f, 90f);
					path.AddLine(tabBounds.Right - base.Radius, tabBounds.Bottom, tabBounds.X, tabBounds.Bottom);
					break;
			}
		}
	}

}
