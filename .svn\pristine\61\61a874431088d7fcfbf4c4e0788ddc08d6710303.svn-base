﻿using System;
using System.Data;
using System.IO;
using System.Threading;
using System.Windows.Forms;
using System.Xml;
using OCRTools.Common;

namespace OCRTools
{
    internal class CommonUpdate
    {
        private static Thread _updateThread;

        private static bool _isOnUpdate;
        public static bool isAlertUpdate;

        public static void InitUpdate()
        {
            if (_updateThread != null)
                try
                {
                    _updateThread.Abort();
                    _updateThread = null;
                }
                catch
                {
                }

            try
            {
                InitUpdateService();
            }
            catch
            {
            }
        }

        private static void InitUpdateService()
        {
            var timerInfo = new TimerInfo
            {
                TimerType = "LoopHours",
                DateValue = (int) Math.Max(CommonSetting.自动更新间隔, 1),
                IsExecFirst = CommonSetting.启动时检查更新
            };
            TimerTaskDelegate update = UpdateMethod;
            var updateTimeTaskService = TimerTaskService.CreateTimerTaskService(timerInfo, update);
            updateTimeTaskService.Start();
        }

        public static void UpdateMethod()
        {
            try
            {
                if (_isOnUpdate)
                {
                    if (isAlertUpdate) CommonMethod.ShowHelpMsg("正在检查更新中，请稍候重试！");
                    return;
                }

                _isOnUpdate = true;
                UpdateEntity updateEntity = null;
                if (IsHasNew(ref updateEntity))
                {
                    var form = ControlExtension.GetMetroForm();
                    CommonMethod.ShowHelpMsg("发现新版本,更新前请关闭360等杀毒软件，避免被误杀！");
                    var update = new FormUpdate
                    {
                        Icon = form.Icon,
                        Theme = form.Theme,
                        Style = form.Style,
                        StyleManager = form.StyleManager,
                        UpdateInfo = updateEntity,
                        TopMost = true,
                        StartPosition = FormStartPosition.CenterScreen
                    };
                    CommonMethod.DetermineCall(form, delegate { update.Show(); });
                }
                else
                {
                    if (isAlertUpdate)
                    {
                        isAlertUpdate = false;
                        CommonMethod.ShowHelpMsg(string.Format("当前版本:V{0}，已经是最新版本！", CommonString.StrNowVersion));
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("检查更新错误", oe);
            }
            finally
            {
                _isOnUpdate = false;
            }
        }

        internal static bool IsHasNew(ref UpdateEntity updateNew)
        {
            var result = false;
            try
            {
                updateNew = LoadVersionInfo();
                result = IsHasNewVersion(CommonString.DtNowDate, updateNew);
            }
            catch
            {
            }

            return result;
        }

        private static bool IsHasNewVersion(DateTime dtDate, UpdateEntity updateNew)
        {
            var result = dtDate < updateNew?.dtNewDate;
            return result;
        }

        /// <summary>
        ///     将Xml内容字符串转换成DataSet对象
        /// </summary>
        /// <param name="xmlStr"></param>
        /// <returns></returns>
        private static DataSet CXmlToDataSet(string xmlStr)
        {
            if (string.IsNullOrEmpty(xmlStr)) return null;
            StringReader strStream = null;
            XmlTextReader xmlrdr = null;
            try
            {
                var ds = new DataSet();
                //读取字符串中的信息
                strStream = new StringReader(xmlStr);
                //获取StrStream中的数据
                xmlrdr = new XmlTextReader(strStream);
                //ds获取Xmlrdr中的数据                
                ds.ReadXml(xmlrdr);
                return ds;
            }
            finally
            {
                //释放资源
                if (xmlrdr != null)
                {
                    xmlrdr.Close();
                    strStream.Close();
                    strStream.Dispose();
                }
            }
        }

        private static UpdateEntity LoadVersionInfo()
        {
            UpdateEntity updateNew = null;
            var strUpdate =
                WebClientExt.GetHtml(
                    CommonString.StrUpdateUrl + "update/" + CommonString.UpdateFileName + "?t=" + DateTime.Now.Ticks,
                    3);
            try
            {
                var dsTemp = CXmlToDataSet(strUpdate);
                if (dsTemp != null)
                {
                    if (dsTemp.Tables.Count > 0 && dsTemp.Tables[0].Rows.Count > 0)
                        updateNew = new UpdateEntity
                        {
                            strNewVersion = dsTemp.Tables[0].Rows[0]["strNowVersion"].ToString(),
                            dtNewDate = DateTime.Parse(dsTemp.Tables[0].Rows[0]["dtNowDate"].ToString()),
                            strContext = dsTemp.Tables[0].Rows[0]["strContext"].ToString()
                                .Replace("|", Environment.NewLine),
                            strURL = dsTemp.Tables[0].Rows[0]["strURL"].ToString(),
                            strFullURL = dsTemp.Tables[0].Rows[0]["strFullURL"].ToString(),
                            IsForceUpdate =
                                BoxUtil.GetBooleanFromObject(dsTemp.Tables[0].Rows[0]["IsNowForce"].ToString(), false)
                        };
                    dsTemp.Dispose();
                }
            }
            catch
            {
            }

            return updateNew;
        }
    }

    internal class UpdateEntity
    {
        public string strNewVersion { get; set; }
        public DateTime dtNewDate { get; set; }
        public string strContext { get; set; }
        public string strURL { get; set; }
        public string strFullURL { get; set; }
        public bool IsForceUpdate { get; set; }
    }
}