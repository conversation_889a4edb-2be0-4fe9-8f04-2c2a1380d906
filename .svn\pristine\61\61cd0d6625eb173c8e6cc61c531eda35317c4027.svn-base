﻿using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.IO;
using System.Web;

namespace OCRTools
{
    internal enum WeatchIconType
    {
        QQ = 1,
        墨迹 = 2,
    }

    public class CommonWeather
    {
        public static void InitWeather()
        {
            try
            {
                var timerInfo = new TimerInfo
                {
                    TimerType = "LoopMinutes",
                    DateValue = 30,
                    IsExecFirst = true
                };
                TimerTaskDelegate update = UpdateMethod;
                var updateTimeTaskService = TimerTaskService.CreateTimerTaskService(timerInfo, update);
                updateTimeTaskService.Start();
            }
            catch
            {
            }
        }

        public static void UpdateMethod()
        {
            try
            {
                if (!CommonSetting.启用天气图标)
                {
                    return;
                }
                var strImage = GetWeather((WeatchIconType)Enum.Parse(typeof(WeatchIconType), CommonSetting.天气图标样式));
                if (!string.IsNullOrEmpty(strImage))
                {
                    try
                    {
                        CommonSetting.工具栏图片 = strImage;
                        FrmMain.FrmTool.RefreshImage();
                    }
                    catch
                    {
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("更新天气失败", oe);
            }
        }

        internal static string GetWeatherIcon(string data, WeatchIconType iconType, bool isAuto = true)
        {
            var result = string.Empty;
            var url = string.Empty;
            switch (iconType)
            {
                case WeatchIconType.QQ:
                    url = string.Format(StrQQWeatherImgUrl, data);
                    break;
                case WeatchIconType.墨迹:
                    url = string.Format(StrMoJiWeatherImgUrl, data.TrimStart('0').PadRight(1, '0'));
                    break;
            }
            result = CommonSetting.SetHeadImageByUrl(url);
            if (string.IsNullOrEmpty(result) && isAuto)
            {
                return GetWeatherIcon(data, iconType == WeatchIconType.QQ ? WeatchIconType.墨迹 : WeatchIconType.QQ, false);
            }
            return result;
        }

        internal static string GetWeather(WeatchIconType iconType)
        {
            var city = string.Empty;
            var result = string.Empty;
            var strWeather = GetQQWeather(ref city);
            if (!string.IsNullOrEmpty(strWeather))
            {
                result = GetWeatherIcon(strWeather, iconType);
            }
            CommonMethod.ShowHelpMsg(string.Format("{0} {1}更新天气{2}！", ServerTime.DateTime.ToString("HH:mm:ss"), city, string.IsNullOrEmpty(strWeather) ? "失败" : "成功"));
            return result;
        }

        //public static string Get360Weather()
        //{
        //    var result = string.Empty;
        //    var html = WebClientExt.GetHtml(Str360WeatherUrl).Replace(" ", "").Trim();
        //    if (!string.IsNullOrEmpty(html) && html.Contains(Str360WeatherSection))
        //    {
        //        html = html.Substring(html.IndexOf(Str360WeatherSection) + Str360WeatherSection.Length);
        //        result = CommonMethod.SubString(html, Str360WeatherImage, "\"").Trim();
        //    }
        //    return result;
        //}

        public static string GetQQWeather(ref string strCity)
        {
            var result = string.Empty;
            var lstLocation = GetLocation();
            if (lstLocation.Count <= 0)
            {
                return result;
            }
            var html = WebClientExt.GetHtml(string.Format(StrQQWeatherUrl, HttpUtility.UrlEncode(lstLocation[0]), HttpUtility.UrlEncode(lstLocation[1]), HttpUtility.UrlEncode(lstLocation[2]))
                ).Replace(" ", "").Trim();
            if (!string.IsNullOrEmpty(html) && html.Contains(StrQQWeatherImage))
            {
                result = CommonMethod.SubString(html, StrQQWeatherImage, "\"").Trim();
            }

            if (!string.IsNullOrEmpty(result))
            {
                strCity = string.IsNullOrEmpty(lstLocation[2]) ? lstLocation[1] : lstLocation[2];
            }
            return result;
        }

        private const string StrQQIPUrl =
            "https://apis.map.qq.com/ws/location/v1/ip?key=3BFBZ-ZKD3X-LW54A-ZT76D-E7AHO-4RBD5&&output=jsonp&callback=weather";
        private const string StrQQWeatherUrl =
            "https://wis.qq.com/weather/common?source=pc&weather_type=observe&province={0}&city={1}&county={2}&callback=weather";
        //private const string Str360WeatherUrl =
        //    "http://weather.kjjs.360.cn/freshcalendar/weather?ver=1.0.0.1125&callback=callback";
        private const string StrQQProvince = "\"province\":\"";
        private const string StrQQCity = "\"city\":\"";
        private const string StrQQCounty = "\"district\":\"";
        private const string StrQQWeatherImage = "\"weather_code\":\"";
        //private const string Str360WeatherSection = "\"realtime\":";
        //private const string Str360WeatherImage = "\"img\":\"";

        private const string StrQQWeatherImgUrl =
            //"https://mat1.gtimg.com/pingjs/ext2020/weather/pc/icon/weather/day/{0}.png";//小图
            "https://mat1.gtimg.com/pingjs/ext2020/weather/pc/icon/currentweather/day/{0}.png";//大图

        private const string StrMoJiWeatherImgUrl =
            "https://h5tq.moji.com/tianqi/assets/images/weather/w{0}.png";

        private static List<string> GetLocation()
        {
            var lstResult = new List<string>();
            var html = WebClientExt.GetHtml(StrQQIPUrl).Replace(" ", "").Trim();
            if (!string.IsNullOrEmpty(html) && html.Contains(StrQQProvince))
            {
                lstResult.Add(CommonMethod.SubString(html, StrQQProvince, "\"").Trim());
                lstResult.Add(CommonMethod.SubString(html, StrQQCity, "\"").Trim());
                lstResult.Add(CommonMethod.SubString(html, StrQQCounty, "\"").Trim());
            }
            return lstResult;
        }
    }
}
