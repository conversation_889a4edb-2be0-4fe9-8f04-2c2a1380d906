using System;
using System.Drawing;
using System.Drawing.Imaging;

namespace OCRTools
{
    public unsafe class UnsafeBitmap : IDisposable
    {
        private readonly Bitmap _bitmap;
        private BitmapData _bitmapData;

        public UnsafeBitmap(Bitmap bitmap, bool lockBitmap = false,
            ImageLockMode imageLockMode = ImageLockMode.ReadWrite)
        {
            this._bitmap = bitmap;
            Width = bitmap.Width;
            Height = bitmap.Height;

            if (lockBitmap) Lock(imageLockMode);
        }

        public ColorBgra* Pointer { get; private set; }
        public bool IsLocked { get; private set; }
        public int Width { get; }
        public int Height { get; }

        public int PixelCount => Width * Height;

        public void Dispose()
        {
            Unlock();
        }

        public void Lock(ImageLockMode imageLockMode = ImageLockMode.ReadWrite)
        {
            if (!IsLocked)
            {
                IsLocked = true;
                _bitmapData = _bitmap.LockBits(new Rectangle(0, 0, Width, Height), imageLockMode,
                    PixelFormat.Format32bppArgb);
                Pointer = (ColorBgra*) _bitmapData.Scan0.ToPointer();
            }
        }

        public void Unlock()
        {
            if (IsLocked)
            {
                _bitmap.UnlockBits(_bitmapData);
                _bitmapData = null;
                Pointer = null;
                IsLocked = false;
            }
        }

        public static bool operator ==(UnsafeBitmap bmp1, UnsafeBitmap bmp2)
        {
            return ReferenceEquals(bmp1, bmp2) || bmp1.Equals(bmp2);
        }

        public static bool operator !=(UnsafeBitmap bmp1, UnsafeBitmap bmp2)
        {
            return !(bmp1 == bmp2);
        }

        public override bool Equals(object obj)
        {
            return obj is UnsafeBitmap bitmap && Compare(bitmap, this);
        }

        public override int GetHashCode()
        {
            return PixelCount;
        }

        public static bool Compare(UnsafeBitmap bmp1, UnsafeBitmap bmp2)
        {
            var pixelCount = bmp1.PixelCount;

            if (pixelCount != bmp2.PixelCount) return false;

            bmp1.Lock(ImageLockMode.ReadOnly);
            bmp2.Lock(ImageLockMode.ReadOnly);

            var pointer1 = bmp1.Pointer;
            var pointer2 = bmp2.Pointer;

            for (var i = 0; i < pixelCount; i++)
            {
                if (pointer1->Bgra != pointer2->Bgra) return false;

                pointer1++;
                pointer2++;
            }

            return true;
        }

        public ColorBgra GetPixel(int x, int y)
        {
            return Pointer[x + y * Width];
        }

        public void SetPixel(int x, int y, ColorBgra color)
        {
            Pointer[x + y * Width] = color;
        }
    }
}