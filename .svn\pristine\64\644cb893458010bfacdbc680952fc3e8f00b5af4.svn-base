﻿using System;
using System.Net;

namespace ImageLib
{
    public class ImageHelper
    {
        private const string StrUrlStart = "http";

        public static string GetResult(byte[] content, string fileExt)
        {
            var result = string.Empty;
            if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
            {
                result = SouGouImageUpload.GetResult(content);
            }
            if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
            {
                result = TouTiaoUpload.GetResult(content);
            }
            if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
            {
                result = _360ImageUpload.GetResult(content);
            }
            if (string.IsNullOrEmpty(result) || !result.ToLower().StartsWith(StrUrlStart))
            {
                result = Net126Upload.GetResult(content);
            }
            return result;
        }
    }
    public class BadApiException : Exception
    {
        public BadApiException() { }

        public BadApiException(string msg) : base(msg)
        {
        }

        public BadApiException(string msg, Exception innerException, HttpStatusCode code) : base(msg, innerException)
        {
            Code = code;
        }

        public HttpStatusCode Code { get; set; }
    }
}
