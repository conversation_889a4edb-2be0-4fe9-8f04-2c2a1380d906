﻿using System;

namespace OCRTools.GZip
{
    /// <summary>
    /// 要压缩的文件信息
    /// </summary>
    public class GZipFileInfo
    {
        /// <summary>
        /// 文件索引
        /// </summary>
        public int Index;
        /// <summary>
        /// 文件相对路径，'/'
        /// </summary>
        public string RelativePath;
        public DateTime ModifiedDate;
        /// <summary>
        /// 文件内容长度
        /// </summary>
        public int Length;
        public bool AddedToTempFile = false;
        public bool RestoreRequested = false;
        public bool Restored = false;
        /// <summary>
        /// 文件绝对路径,'\'
        /// </summary>
        public string LocalPath = null;
        public string Folder = null;

        public bool ParseFileInfo(string fileInfo)
        {
            bool success = false;
            try
            {
                if (!string.IsNullOrEmpty(fileInfo))
                {
                    // get the file information
                    var info = fileInfo.Split(',');
                    if (info.Length == 4)
                    {
                        Index = Convert.ToInt32(info[0]);
                        RelativePath = info[1].Replace("/", "\\");
                        ModifiedDate = Convert.ToDateTime(info[2]);
                        Length = Convert.ToInt32(info[3]);
                        success = true;
                    }
                }
            }
            catch
            {
                success = false;
            }
            return success;
        }
    }
}
