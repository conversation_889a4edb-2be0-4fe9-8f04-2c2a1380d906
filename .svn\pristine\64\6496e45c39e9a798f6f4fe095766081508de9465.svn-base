﻿using OCRTools.Language;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Threading.Tasks;
using System.Web;
using System.Linq;
using System.Threading;
using System.Text;

namespace OCRTools.Common
{
    internal class TransAction
    {
        public string source { get; set; }

        public Action<string, string> action { get; set; }
    }

    internal static class CommonTranslate
    {
#if DEBUG
        public static Dictionary<string, Dictionary<string, string>> dicTransCache = new Dictionary<string, Dictionary<string, string>>();
#endif
#if !DEBUG
        private static Dictionary<string, Dictionary<string, string>> dicTransCache = new Dictionary<string, Dictionary<string, string>>();
#endif
        private static ConcurrentBag<TransAction> DicActions = new ConcurrentBag<TransAction>();

        internal static void InitTranslate()
        {
            try
            {
                dicTransCache = (LanguageHelper.IsChinese ? null : OcrHelper.GetServerConfig<Dictionary<string, Dictionary<string, string>>>("TransCache")) ?? new Dictionary<string, Dictionary<string, string>>();
            }
            catch { }
        }

        internal static string Translate(string strSource, bool isNeedTrans = false)
        {
            var result = strSource;
            if (dicTransCache.ContainsKey(strSource) && dicTransCache[strSource].ContainsKey(LanguageHelper.NowLanguage))
                result = dicTransCache[strSource][LanguageHelper.NowLanguage];
#if DEBUG
            else if (isNeedTrans)// || (!isNeedTrans && Equals(LanguageHelper.NowLanguage, LanguageHelper.StrBaseLanguage)))
            {
                Translate(strSource, null, false);
            }
#endif
            return result;
        }

        internal static void RefreshLanguage()
        {
            if (dicTransCache.Count == 0 || !dicTransCache[dicTransCache.Keys.FirstOrDefault()].ContainsKey(LanguageHelper.NowLanguage))
            {
                InitTranslate();
            }
            Task.Factory.StartNew(() =>
            {
                foreach (var item in DicActions)
                {
                    Translate(item.source, item.action, false);
                }
            });
        }

        internal static void TransText(this string key, Action<string, string> action = null, bool isUserAdd = true)
        {
            Translate(key, action, isUserAdd);
        }

        internal static void Translate(string strSource, Action<string, string> action = null, bool isUserAdd = true)
        {
            var isExec = false;
            try
            {
                if (string.IsNullOrEmpty(strSource))
                    return;
                if (isUserAdd)
                    DicActions.Add(new TransAction() { source = strSource, action = action });
                if (Equals(LanguageHelper.NowLanguage, LanguageHelper.StrBaseLanguage))
                {
                    return;
                }
                if (dicTransCache.ContainsKey(strSource) && dicTransCache[strSource].ContainsKey(LanguageHelper.NowLanguage))
                {
                    isExec = true;
                    action?.Invoke(strSource, dicTransCache[strSource][LanguageHelper.NowLanguage]);
                }
#if DEBUG
                else
                {
                    //DEBUG模式上传翻译
                    if (System.Text.RegularExpressions.Regex.IsMatch(strSource, @"[\u4e00-\u9fa5]"))
                    {
                        foreach (var item in lstToTransLanguage)
                        {
                            TranslateProcessPool.Add(new TranslateEntity()
                            {
                                lang = item,
                                source = strSource
                            });
                        }
                    }
                }
#endif
            }
            catch { }
            finally
            {
                if (!isExec)
                {
                    try
                    {
                        action?.Invoke(strSource, strSource);
                    }
                    catch { }
                }
            }
        }

#if DEBUG

        private static List<string> lstToTransLanguage = new List<string>();

        public static string TranslageKey = "TransCache";

        public static BlockingCollection<TranslateEntity> TranslateProcessPool = new BlockingCollection<TranslateEntity>();

        static CommonTranslate()
        {
            foreach (SupportedLanguage language in Enum.GetValues(typeof(SupportedLanguage)))
            {
                var cultureName = language.GetValue<MenuCategoryAttribute>();
                if (Equals(cultureName, LanguageHelper.StrBaseLanguage))
                {
                    continue;
                }
                lstToTransLanguage.Add(cultureName);
            }
            new Thread(p =>
            {
                try
                {
                    foreach (var processEntity in TranslateProcessPool.GetConsumingEnumerable())
                        try
                        {
                            var dicTrans = LanguageHelper.TransLanguage(new List<string>() { processEntity.source }, processEntity.lang);
                            foreach (var key in dicTrans.Keys)
                            {
                                dicTransCache = OcrHelper.GetServerResult<Dictionary<string, Dictionary<string, string>>>("code.ashx?op=trans", CommonString.HostAccount?.FullUrl, "lang=" + processEntity.lang + "&source=" + HttpUtility.UrlEncode(key) + "&trans=" + HttpUtility.UrlEncode(dicTrans[key]));
                            }
                        }
                        catch { }
                }
                catch { }
            })
            { IsBackground = true, Priority = ThreadPriority.Highest }.Start();
        }

        internal class TranslateEntity
        {
            public string lang { get; set; }

            public string source { get; set; }
        }
#endif
    }

}
