﻿using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace OCRTools
{
    public class TaskRoundedCornerPanel : RoundedCornerPanel
    {
        private bool selected;

        public bool Selected
        {
            get => selected;
            set
            {
                if (selected != value)
                {
                    selected = value;

                    Invalidate();
                }
            }
        }

        public Color StatusColor { get; private set; } = Color.Transparent;
        public ThumbnailTitleLocation StatusLocation { get; set; }

        public void UpdateStatusColor(TaskStatus status)
        {
            var previousStatusColor = StatusColor;

            switch (status)
            {
                case TaskStatus.Completed:
                case TaskStatus.Stopped:
                    StatusColor = Color.CornflowerBlue;
                    break;
                case TaskStatus.Failed:
                    StatusColor = Color.Red;
                    break;
                case TaskStatus.History:
                    StatusColor = Color.Transparent;
                    break;
                default:
                    StatusColor = Color.PaleGreen;
                    break;
            }

            if (previousStatusColor != StatusColor) Invalidate();
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);

            var g = e.Graphics;

            if (Selected)
            {
                g.PixelOffsetMode = PixelOffsetMode.Default;

                using (var pen = new Pen(CustomColor.black) {DashStyle = DashStyle.Dot})
                {
                    g.DrawRoundedRectangle(pen, ClientRectangle, Radius);
                }
            }

            if (StatusColor.A > 0)
            {
                g.PixelOffsetMode = PixelOffsetMode.Half;

                int y;

                if (StatusLocation == ThumbnailTitleLocation.Top)
                    y = 0;
                else
                    y = ClientRectangle.Height;

                using (var brush = new LinearGradientBrush(new Rectangle(0, 0, ClientRectangle.Width, 1), Color.Black,
                    Color.Black,
                    LinearGradientMode.Horizontal))
                {
                    var cb = new ColorBlend
                    {
                        Positions = new[] {0, 0.3f, 0.7f, 1},
                        Colors = new[] {Color.Transparent, StatusColor, StatusColor, Color.Transparent}
                    };
                    brush.InterpolationColors = cb;

                    using (var pen = new Pen(brush))
                    {
                        g.DrawLine(pen, new Point(0, y), new Point(ClientRectangle.Width - 1, y));
                    }
                }
            }
        }
    }

    public class RoundedCornerPanel : Panel
    {
        private Color panelColor;

        private float radius;

        public RoundedCornerPanel()
        {
            BackColor = Color.Transparent;

            SetStyle(
                ControlStyles.UserPaint | ControlStyles.AllPaintingInWmPaint | ControlStyles.ResizeRedraw |
                ControlStyles.OptimizedDoubleBuffer | ControlStyles.SupportsTransparentBackColor, true);
        }

        public float Radius
        {
            get => radius;
            set
            {
                radius = value;

                Invalidate();
            }
        }

        public Color PanelColor
        {
            get => panelColor;
            set
            {
                panelColor = value;

                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            var g = e.Graphics;

            g.PixelOffsetMode = PixelOffsetMode.Half;
            g.SmoothingMode = SmoothingMode.HighQuality;

            using (var brush = new SolidBrush(PanelColor))
            {
                g.DrawRoundedRectangle(brush, ClientRectangle.SizeOffset(1), Radius);
            }
        }
    }
}