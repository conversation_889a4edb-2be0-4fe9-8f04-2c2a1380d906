using System.Collections.Generic;

namespace OCRTools
{
    internal class CommandChangeState : Command
    {
        private readonly List<DrawObject> listBefore;
        private List<DrawObject> listAfter;

        public CommandChangeState(GraphicsList graphicsList)
        {
            FillList(graphicsList, ref listBefore);
        }

        public void NewState(GraphicsList graphicsList)
        {
            FillList(graphicsList, ref listAfter);
        }

        public override void Undo(GraphicsList list)
        {
            ReplaceObjects(list, listBefore);
        }

        public override void Redo(GraphicsList list)
        {
            ReplaceObjects(list, listAfter);
        }

        private void ReplaceObjects(GraphicsList graphicsList, List<DrawObject> list)
        {
            for (var i = 0; i < graphicsList.Count; i++)
            {
                DrawObject drawObject = null;
                foreach (var item in list)
                    if (item.ID == graphicsList[i].ID)
                    {
                        drawObject = item;
                        break;
                    }

                if (drawObject != null) graphicsList.Replace(i, drawObject);
            }
        }

        private void FillList(GraphicsList graphicsList, ref List<DrawObject> listToFill)
        {
            listToFill = new List<DrawObject>();
            foreach (var item in graphicsList.Selection) listToFill.Add(item.Clone());
        }
    }
}