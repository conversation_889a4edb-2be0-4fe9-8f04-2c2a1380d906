namespace UtfUnknown.Core.Models.MultiByte
{
	public class UCS2BE_SMModel : StateMachineModel
	{
		private static readonly int[] UCS2BE_cls = new int[32]
		{
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 1, 0, 0, 2, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 3, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 3, 3, 3, 3, 3, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 0, 0),
			BitPackage.Pack4bits(0, 0, 0, 0, 0, 0, 4, 5)
		};

		private static readonly int[] UCS2BE_st = new int[7]
		{
			BitPackage.Pack4bits(5, 7, 7, 1, 4, 3, 1, 1),
			BitPackage.Pack4bits(1, 1, 1, 1, 2, 2, 2, 2),
			BitPackage.Pack4bits(2, 2, 6, 6, 6, 6, 1, 1),
			BitPackage.Pack4bits(6, 6, 6, 6, 6, 2, 6, 6),
			BitPackage.Pack4bits(6, 6, 6, 6, 5, 7, 7, 1),
			BitPackage.Pack4bits(5, 8, 6, 6, 1, 6, 6, 6),
			BitPackage.Pack4bits(6, 6, 6, 6, 1, 1, 0, 0)
		};

		private static readonly int[] UCS2BECharLenTable = new int[6]
		{
			2,
			2,
			2,
			0,
			2,
			2
		};

		public UCS2BE_SMModel()
			: base(new BitPackage(BitPackage.INDEX_SHIFT_4BITS, BitPackage.SHIFT_MASK_4BITS, BitPackage.BIT_SHIFT_4BITS, BitPackage.UNIT_MASK_4BITS, UCS2BE_cls), 6, new BitPackage(BitPackage.INDEX_SHIFT_4BITS, BitPackage.SHIFT_MASK_4BITS, BitPackage.BIT_SHIFT_4BITS, BitPackage.UNIT_MASK_4BITS, UCS2BE_st), UCS2BECharLenTable, "utf-16be")
		{
		}
	}
}
