﻿//using OfficeOpenXml;
//using OfficeOpenXml.Style;

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace OCRTools.Common
{
    public class ExcelHelper
    {
        private const string startExcelXML =
            "<xml version><Workbook xmlns=\"urn:schemas-microsoft-com:office:spreadsheet\" xmlns:o=\"urn:schemas-microsoft-com:office:office\" xmlns:x=\"urn:schemas-microsoft-com:office:excel\" xmlns:ss=\"urn:schemas-microsoft-com:office:spreadsheet\"><Styles><Style ss:ID=\"Default\" ss:Name=\"Normal\"><Alignment ss:Vertical=\"Bottom\"/><Borders/><Font/><Interior/><NumberFormat/><Protection/></Style><Style ss:ID=\"BoldColumn\"><Font x:Family=\"Swiss\" ss:Bold=\"1\"/></Style><Style ss:ID=\"StringLiteral\"><NumberFormat ss:Format=\"@\"/></Style><Style ss:ID=\"Decimal\"><NumberFormat ss:Format=\"0.0000\"/></Style><Style ss:ID=\"Integer\"><NumberFormat ss:Format=\"0\"/></Style><Style ss:ID=\"DateLiteral\"><NumberFormat ss:Format=\"mm/dd/yyyy;@\"/></Style></Styles>";

        public static void ExportCSV(DataTable table, string fileName)
        {
            if (table != null)
            {
                if (fileName.EndsWith("csv"))
                    ExportToCSV(table, fileName);
                else
                    exportToExcel(table, fileName);
                //ExportExcel(table, fileName);
            }
        }

        public static void ExportCSV(DataGridView dataGridView, string fileName)
        {
            var table = dataGridView.DataSource as DataTable;
            if (table != null)
            {
                if (fileName.EndsWith("csv"))
                    ExportToCSV(table, fileName);
                else
                    exportToExcel(table, fileName);
                //ExportExcel(table, fileName);
            }
        }

        /// <summary>
        ///     导出CSV格式文件
        /// </summary>
        /// <param name="dataTable">数据表</param>
        /// <param name="fileName">文件名</param>
        private static void ExportToCSV(DataTable dataTable, string fileName)
        {
            var StreamWriter = new StreamWriter(fileName, false, Encoding.GetEncoding("gb2312"));
            StreamWriter.WriteLine(GetCSVFormatData(dataTable).ToString());
            StreamWriter.Flush();
            StreamWriter.Close();
        }

        /// <summary>
        ///     通过DataTable获得CSV格式数据
        /// </summary>
        /// <param name="dataTable">数据表</param>
        /// <returns>CSV字符串数据</returns>
        private static StringBuilder GetCSVFormatData(DataTable dataTable)
        {
            var StringBuilder = new StringBuilder();
            // 写出数据
            var count = 0;
            foreach (DataRowView dataRowView in dataTable.DefaultView)
            {
                count++;
                foreach (DataColumn DataColumn in dataTable.Columns)
                {
                    var field = dataRowView[DataColumn.ColumnName].ToString();

                    if (field.IndexOf('"') >= 0) field = field.Replace("\"", "\"\"");
                    field = field.Replace("  ", " ");
                    if (field.IndexOf(',') >= 0 || field.IndexOf('"') >= 0 || field.IndexOf('<') >= 0 ||
                        field.IndexOf('>') >= 0 || field.IndexOf("'") >= 0) field = "\"" + field + "\"";
                    StringBuilder.Append(field + ",");
                    field = string.Empty;
                }

                if (count != dataTable.Rows.Count) StringBuilder.Append("\n");
            }

            return StringBuilder;
        }

        /// <summary>
        ///     List转DataTable
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="data"></param>
        /// <returns></returns>
        public static DataTable ListToDataTable<T>(List<T> data)
        {
            var properties = TypeDescriptor.GetProperties(typeof(T));
            var dataTable = new DataTable();
            for (var i = 0; i < properties.Count; i++)
            {
                var property = properties[i];
                dataTable.Columns.Add(property.Name,
                    Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType);
            }

            var values = new object[properties.Count];
            foreach (var item in data)
            {
                for (var i = 0; i < values.Length; i++) values[i] = properties[i].GetValue(item);
                dataTable.Rows.Add(values);
            }

            return dataTable;
        }

        private static void exportToExcel(DataTable source, string fileName)
        {
            var excelDoc = new StreamWriter(fileName);
            const string endExcelXML = "</Workbook>";

            var rowCount = 0;
            var sheetCount = 1;

            #region ExcelModel

            /*
           <xml version>
           <Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
           xmlns:o="urn:schemas-microsoft-com:office:office"
           xmlns:x="urn:schemas-microsoft-com:office:excel"
           xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet">
           <Styles>
           <Style ss:ID="Default" ss:Name="Normal">
             <Alignment ss:Vertical="Bottom"/>
             <Borders/>
             <Font/>
             <Interior/>
             <NumberFormat/>
             <Protection/>
           </Style>
           <Style ss:ID="BoldColumn">
             <Font x:Family="Swiss" ss:Bold="1"/>
           </Style>
           <Style ss:ID="StringLiteral">
             <NumberFormat ss:Format="@"/>
           </Style>
           <Style ss:ID="Decimal">
             <NumberFormat ss:Format="0.0000"/>
           </Style>
           <Style ss:ID="Integer">
             <NumberFormat ss:Format="0"/>
           </Style>
           <Style ss:ID="DateLiteral">
             <NumberFormat ss:Format="mm/dd/yyyy;@"/>
           </Style>
           </Styles>
           <Worksheet ss:Name="Sheet1">
           </Worksheet>
           </Workbook>
           */

            #endregion

            excelDoc.Write(startExcelXML);
            excelDoc.Write("<Worksheet ss:Name=\"Sheet" + sheetCount + "\">");
            excelDoc.Write("<Table>");
            //excelDoc.Write("<Row>");
            //for (int x = 0; x < source.Columns.Count; x++)
            //{
            //    excelDoc.Write("<Cell ss:StyleID=\"BoldColumn\"><Data ss:Type=\"String\">");
            //    excelDoc.Write(source.Columns[x].ColumnName);
            //    excelDoc.Write("</Data></Cell>");
            //}
            //excelDoc.Write("</Row>");
            foreach (DataRow x in source.Rows)
            {
                rowCount++;
                //if the number of rows is > 64000 create a new page to continue output
                if (rowCount == 64000)
                {
                    rowCount = 0;
                    sheetCount++;
                    excelDoc.Write("</Table>");
                    excelDoc.Write(" </Worksheet>");
                    excelDoc.Write("<Worksheet ss:Name=\"Sheet" + sheetCount + "\">");
                    excelDoc.Write("<Table>");
                }

                excelDoc.Write("<Row>"); //ID=" + rowCount + "
                for (var y = 0; y < source.Columns.Count; y++)
                {
                    Type rowType;
                    rowType = x[y].GetType();
                    switch (rowType.ToString())
                    {
                        case "System.String":
                            var XMLstring = x[y].ToString();
                            XMLstring = XMLstring.Trim();
                            XMLstring = XMLstring.Replace("&", "&");
                            XMLstring = XMLstring.Replace(">", ">");
                            XMLstring = XMLstring.Replace("<", "<");
                            excelDoc.Write("<Cell ss:StyleID=\"StringLiteral\">" +
                                           "<Data ss:Type=\"String\">");
                            excelDoc.Write(XMLstring);
                            excelDoc.Write("</Data></Cell>");
                            break;
                        case "System.DateTime":
                            //Excel has a specific Date Format of YYYY-MM-DD followed by  
                            //the letter 'T' then hh:mm:sss.lll Example 2005-01-31T24:01:21.000
                            //The Following Code puts the date stored in XMLDate 
                            //to the format above
                            var XMLDate = (DateTime) x[y];
                            var XMLDatetoString = ""; //Excel Converted Date
                            XMLDatetoString = XMLDate.Year +
                                              "-" +
                                              (XMLDate.Month < 10
                                                  ? "0" +
                                                    XMLDate.Month
                                                  : XMLDate.Month.ToString()) +
                                              "-" +
                                              (XMLDate.Day < 10
                                                  ? "0" +
                                                    XMLDate.Day
                                                  : XMLDate.Day.ToString()) +
                                              "T" +
                                              (XMLDate.Hour < 10
                                                  ? "0" +
                                                    XMLDate.Hour
                                                  : XMLDate.Hour.ToString()) +
                                              ":" +
                                              (XMLDate.Minute < 10
                                                  ? "0" +
                                                    XMLDate.Minute
                                                  : XMLDate.Minute.ToString()) +
                                              ":" +
                                              (XMLDate.Second < 10
                                                  ? "0" +
                                                    XMLDate.Second
                                                  : XMLDate.Second.ToString()) +
                                              ".000";
                            excelDoc.Write("<Cell ss:StyleID=\"DateLiteral\">" +
                                           "<Data ss:Type=\"DateTime\">");
                            excelDoc.Write(XMLDatetoString);
                            excelDoc.Write("</Data></Cell>");
                            break;
                        case "System.Boolean":
                            excelDoc.Write("<Cell ss:StyleID=\"StringLiteral\">" +
                                           "<Data ss:Type=\"String\">");
                            excelDoc.Write(x[y].ToString());
                            excelDoc.Write("</Data></Cell>");
                            break;
                        case "System.Int16":
                        case "System.Int32":
                        case "System.Int64":
                        case "System.Byte":
                            excelDoc.Write("<Cell ss:StyleID=\"Integer\">" +
                                           "<Data ss:Type=\"Number\">");
                            excelDoc.Write(x[y].ToString());
                            excelDoc.Write("</Data></Cell>");
                            break;
                        case "System.Decimal":
                        case "System.Double":
                            excelDoc.Write("<Cell ss:StyleID=\"Decimal\">" +
                                           "<Data ss:Type=\"Number\">");
                            excelDoc.Write(x[y].ToString());
                            excelDoc.Write("</Data></Cell>");
                            break;
                        case "System.DBNull":
                            excelDoc.Write("<Cell ss:StyleID=\"StringLiteral\">" +
                                           "<Data ss:Type=\"String\">");
                            excelDoc.Write("");
                            excelDoc.Write("</Data></Cell>");
                            break;
                        default:
                            throw new Exception(rowType + " not handled.");
                    }
                }

                excelDoc.Write("</Row>");
            }

            excelDoc.Write("</Table>");
            excelDoc.Write(" </Worksheet>");
            excelDoc.Write(endExcelXML);
            excelDoc.Close();
        }
    }
}