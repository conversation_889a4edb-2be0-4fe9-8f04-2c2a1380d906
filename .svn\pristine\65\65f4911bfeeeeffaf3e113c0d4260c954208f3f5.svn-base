using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class ROW : Record
	{
		public ushort RowIndex;

		public ushort FirstColIndex;

		public ushort LastColIndex;

		public ushort RowHeight;

		public ushort UnUsed;

		public ushort UnUsed2;

		public uint Flags;

		public ROW(Record record)
			: base(record)
		{
		}

		public ROW()
		{
			Type = 520;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			RowIndex = binaryReader.ReadUInt16();
			FirstColIndex = binaryReader.ReadUInt16();
			LastColIndex = binaryReader.ReadUInt16();
			RowHeight = binaryReader.ReadUInt16();
			UnUsed = binaryReader.ReadUInt16();
			UnUsed2 = binaryReader.ReadUInt16();
			Flags = binaryReader.ReadUInt32();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(RowIndex);
			binaryWriter.Write(FirstColIndex);
			binaryWriter.Write(LastColIndex);
			binaryWriter.Write(RowHeight);
			binaryWriter.Write(UnUsed);
			binaryWriter.Write(UnUsed2);
			binaryWriter.Write(Flags);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
