using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolHighlight : ToolObject
    {
        private DrawHighlight drawHighlight;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.current_ToolType = drawArea.ActiveTool;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else
            {
                drawHighlight = new DrawHighlight(e.X, e.Y, 1, 1)
                {
                    BackgroundImageEx = drawArea.BackgroundImageEx
                };
                AddNewObject(drawArea, drawHighlight);
            }
        }

        protected new void AddNewObject(DrawArea drawArea, DrawObject o)
        {
            drawArea.GraphicsList.UnselectAll();
            drawArea.GraphicsList.Add(o);
            drawArea.Capture = true;
            o.Selected = true;
            drawArea.Refresh();
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left)
            {
                if (drawHighlight == null)
                {
                    drawArea.ActiveTool = DrawToolType.Text;
                    return;
                }

                drawHighlight.IsSelected = true;
                var obj = drawHighlight;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    drawHighlight.MoveHandleTo(e.Location, 5, drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawHighlight != null)
            {
                StaticValue.current_Rectangle = drawHighlight.Rectangle;
                drawHighlight.IsCache = true;
                if (!drawHighlight.Rectangle.IsLimt())
                {
                    drawArea.GraphicsList.RemoveAt(0);
                    return;
                }

                var obj = drawHighlight;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    drawHighlight.Normalize();
                    drawArea.AddCommandToHistory(new CommandAdd(drawHighlight));
                }
            }
        }
    }
}