﻿using System;
using System.IO;

namespace OCRTools.Common.ImageLib
{
    /// <summary>
    ///     https://www.niupic.com/
    /// </summary>
    public class NiuTuImageUpload : BaseImageUpload
    {
        private const string STR_FILE_NAME_SPILT = "\"img_puburl\":\"";

        public NiuTuImageUpload()
        {
            ImageType = ImageTypeEnum.NiuTu;
        }

        public virtual string GetResult(byte[] content, string ext)
        {
            var result = "";
            try
            {
                var url = "https://www.niupic.com/api/upload";
                var file = new UploadFileInfo
                {
                    Name = "image_field",
                    Filename = "test.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                var html = UploadFileRequest.Post(url, new[] {file}, null);
                if (html.Contains(STR_FILE_NAME_SPILT))
                {
                    result = html.Substring(html.IndexOf(STR_FILE_NAME_SPILT) + STR_FILE_NAME_SPILT.Length);
                    result = result.Substring(0, result.IndexOf("\"")).Replace("\\/", "/");
                    if (!string.IsNullOrEmpty(result) && !result.StartsWith("http")) result = "https://" + result;
                }
            }
            catch (Exception)
            {
            }

            return result;
        }
    }
}