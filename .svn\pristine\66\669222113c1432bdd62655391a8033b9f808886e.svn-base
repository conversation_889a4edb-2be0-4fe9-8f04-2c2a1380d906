﻿using OCRTools.Common;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace OCRTools
{
    public class DnsHelper
    {
        private static readonly Dictionary<string, string> DicServers = new Dictionary<string, string>();

        public static string GetDnsIp(string host)
        {
            var result = "";
            try
            {
                if (!string.IsNullOrEmpty(host) && !IsIPv4(host))
                {
                    result = GetDnsCache(host);

                    if (string.IsNullOrEmpty(result))
                    {
                        result = ToIpAddress(host);

                        if (!IsIPv4(result))
                            result = string.Empty;
                    }
                }
            }
            catch
            {
            }

            return result;
        }

        public static void InitServerService()
        {
            var timerInfo = new TimerInfo
            {
                TimerType = "LoopMinutes",
                DateValue = 1,
                IsExecFirst = true,
                TaskName = "InitServerTask"
            };
            var thread = TimerTaskService.CreateTimerTaskService(timerInfo, (a, b, c) =>
            {
                Init();
                return true;
            });
            thread.Start();
        }

        public static bool Init()
        {
            GetRealIp();
            SetServerUrl();
            FrmMain.IpInitFinishEvent?.Invoke(null, null);
            return lstAllSite?.Count > 0;
        }

        private static List<WebInfo> lstAllSite = new List<WebInfo>();

        private static void SetServerUrl()
        {
            if (lstAllSite.Count > 0
                && (CommonString.HostAccount == null
                || !lstAllSite.Any(p => Equals(CommonString.HostAccount.Ip, p.Ip) && Equals(CommonString.HostAccount.Host, p.Host) && Equals(CommonString.HostAccount.Https, p.Https))))
            {
                CommonString.HostAccount = GetNewSite(SiteType.Account);
            }
            if (lstAllSite.Count > 0
                && (CommonString.HostCode == null
                || !lstAllSite.Any(p => Equals(CommonString.HostCode.Ip, p.Ip) && Equals(CommonString.HostCode.Host, p.Host) && Equals(CommonString.HostCode.Https, p.Https))))
            {
                CommonString.HostCode = GetNewSite(SiteType.Code);
            }
            if (lstAllSite.Count > 0
                && (CommonString.HostUpdate == null
                || !lstAllSite.Any(p => Equals(CommonString.HostUpdate.Ip, p.Ip) && Equals(CommonString.HostUpdate.Host, p.Host) && Equals(CommonString.HostUpdate.Https, p.Https))))
            {
                CommonString.HostUpdate = GetNewSite(SiteType.Update);
            }
        }

        public static void ReportError(string host, string ip)
        {
            var siteType = CommonString.IsSelfHost(host);
            if (Equals(siteType, SiteType.Default))
            {
                return;
            }
            switch (siteType)
            {
                case SiteType.Account:
                    //如果跟当前Site一致，特殊处理，否则跳过
                    if (Equals(CommonString.HostAccount.Ip, ip) && CommonString.HostAccount.Host.Contains(host))
                    {
                        var site = GetNewSite(siteType, host, ip);
                        if (site != null && !string.IsNullOrEmpty(site.Host))
                        {
                            CommonString.HostAccount = site;
                        }
                    }
                    break;
                case SiteType.Code:
                    //如果跟当前Site一致，特殊处理，否则跳过
                    if (Equals(CommonString.HostCode.Ip, ip) && CommonString.HostCode.Host.Contains(host))
                    {
                        var site = GetNewSite(siteType, host, ip);
                        if (site != null && !string.IsNullOrEmpty(site.Host))
                        {
                            CommonString.HostCode = site;
                        }
                    }
                    break;
                case SiteType.Update:
                    //如果跟当前Site一致，特殊处理，否则跳过
                    if (Equals(CommonString.HostUpdate.Ip, ip) && CommonString.HostUpdate.Host.Contains(host))
                    {
                        var site = GetNewSite(siteType, host, ip);
                        if (site != null && !string.IsNullOrEmpty(site.Host))
                        {
                            CommonString.HostUpdate = site;
                        }
                    }
                    break;
            }
        }

        private static WebInfo GetNewSite(SiteType type, string host = null, string ip = null)
        {
            var site = new WebInfo();
            var lstSite = lstAllSite.Where(p => Equals(p.Type, type)).ToList();
            if (lstSite.Count <= 0)
            {
                lstSite = lstAllSite.Where(p => Equals(p.Type, SiteType.Default)).ToList();
            }
            //如果池子中不存在，重新设置Host
            if ((string.IsNullOrEmpty(host) && string.IsNullOrEmpty(ip)) || !lstSite.Any(p => p.Host.Contains(host) && Equals(p.Ip, ip)))
            {
                site = lstSite.FirstOrDefault();
            }
            else
            {
                site = lstSite.FirstOrDefault(p => !p.Host.Contains(host) || !Equals(p.Ip, ip));
            }
            return site;
        }

        private static void GetRealIp()
        {
            //Stopwatch stopwatch = Stopwatch.StartNew();
            ConcurrentBag<SiteMain> sites = new ConcurrentBag<SiteMain>();
            Parallel.For(1, 3, (index) =>
            {
                var tmp = GetSiteFromWeb(index);
                if (tmp != null && tmp.web != null && tmp.web.Count > 0)
                    sites.Add(tmp);
            });
            if (sites.Count <= 0)
            {
                return;
            }
            var site = sites.OrderByDescending(p => DateTime.Parse(p.update)).ToList()[0];
            //Console.WriteLine("======耗时：" + stopwatch.ElapsedMilliseconds.ToString("F0") + "ms");
            if (site != null)
            {
                site.web.ForEach(p =>
                {
                    if (string.IsNullOrEmpty(p.Host))
                    {
                        p.Host = site.defaultHost;
                    }
                });
                //site.web[0].Host = "oldfish.azurewebsites.net";
                //site.web[0].Ip = null;
                lstAllSite = site.web;
            }
        }

        private static SiteMain GetSiteFromWeb(int type)
        {
            switch (type)
            {
                case 1:
                    return InitByCName("ocr.oldfish.cn");
                case 2:
                    return InitFromCDN();
                default:
                    return null;
            }
        }

        private static SiteMain InitFromCDN()
        {
            var html = WebClientExt.GetHtml(CommonString.HostUpdate?.FullUrl + "site.json?t=" + DateTime.Now.Ticks, 5);
            if (!string.IsNullOrEmpty(html))
            {
                html = Regex.Unescape(html);
            }
            return GetSiteFromStr(html);
        }

        private static List<string> lstDns = new List<string>() { "************", "***************", "*********", "*******", "*******", "" };

        public static SiteMain InitByCName(string host)
        {
            var result = string.Empty;

            var lstTask = lstDns.AsParallel().Select(dns => Task.Factory.StartNew(() =>
            {
                var strUrl = GetCNameFromNsLookUp(host, dns);

                if (string.IsNullOrEmpty(strUrl) || !strUrl.Contains("."))
                {
                    System.Threading.Thread.Sleep(5000);
                }
                else
                {
                    result = strUrl.Replace("\"\r\n\t\"", "");
                }
            })).ToArray();
            Task.WaitAny(lstTask);
            return GetSiteFromStr(result);
        }

        private static SiteMain GetSiteFromStr(string html)
        {
            SiteMain site = null;
            try
            {
                site = CommonString.JavaScriptSerializer.Deserialize<SiteMain>(html);
            }
            catch (Exception oe) { }
            return site;
        }

        static string ToIpAddress(string hostNameOrAddress)
        {
            var addrs = Dns.GetHostAddresses(hostNameOrAddress);
            return addrs.FirstOrDefault(addr => addr.AddressFamily == AddressFamily.InterNetwork)?.ToString();
        }

        private const string strCName = "\"";

        static string GetCNameFromNsLookUp(string strHost, string strNsServer)
        {
            var result = "";
            var strTmp = ExecCmd(string.Format("nslookup -qt=TXT {0} {1}", strHost, strNsServer));
            if (strTmp.Contains(strHost))
                if (strTmp.IndexOf(strCName) > 0)
                {
                    result = CommonMethod.SubString(strTmp, strCName);
                    result = result.Substring(0, result.LastIndexOf(strCName));
                }

            return result;
        }

        static string GetDnsCache(string strHost)
        {
            var cache = "";
            if (DicServers.ContainsKey(strHost)) cache = DicServers[strHost];
            return cache;
        }

        static string ExecCmd(string str)
        {
            //process用于调用外部程序
            using (var p = new Process())
            {
                //调用cmd.exe
                p.StartInfo.FileName = "cmd.exe";
                //不显示程序窗口
                p.StartInfo.CreateNoWindow = true;
                //是否指定操作系统外壳进程启动程序
                p.StartInfo.UseShellExecute = false;
                //可能接受来自调用程序的输入信息
                //重定向标准输入
                p.StartInfo.RedirectStandardInput = true;
                //重定向标准输出
                p.StartInfo.RedirectStandardOutput = true;
                //重定向错误输出
                p.StartInfo.RedirectStandardError = true;
                //启动程序
                p.Start();
                //输入命令
                p.StandardInput.WriteLine(str);
                //一定要关闭。
                p.StandardInput.WriteLine("exit");
                return p.StandardOutput.ReadToEnd();
            }
        }

        public static bool IsIPv4(string input)
        {
            var array = input.Split('.');
            if (array.Length != 4) return false;
            foreach (var t in array)
            {
                if (!IsMatch("^\\d+$", t)) return false;
                if (Convert.ToUInt16(t) > 255) return false;
            }

            return true;
        }

        static bool IsMatch(string pattern, string input)
        {
            if (string.IsNullOrEmpty(input)) return false;
            var regex = new Regex(pattern);
            return regex.IsMatch(input);
        }
    }
}