﻿using System;
using System.Drawing;
using System.Threading;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class ucLoading : UserControl
    {
        private Image bgImg;

        private LoadintTypeConfig config;

        public int i_c;

        private bool isStop = true;
        private Thread thread;

        public ucLoading()
        {
            InitializeComponent();
        }

        public void InitLoading(Size size, Point locaion)
        {
            Visible = false;
            Size = size;
            Location = locaion;
            BackColor = Color.Transparent;
            BackgroundImageLayout = ImageLayout.Center;
            MouseDoubleClick += UcLoading_MouseDoubleClick;
        }

        private void UcLoading_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            var form = FindForm();
            if (form == null) return;
            form.WindowState = form.WindowState == FormWindowState.Maximized
                ? FormWindowState.Normal
                : FormWindowState.Maximized;
        }

        public void ShowLoading(string strText = "", bool isShowLoading = true)
        {
            ShowText(strText);
            Visible = isShowLoading;
            if (isShowLoading)
            {
                BringToFront();
                if (isStop)
                {
                    isStop = false;
                    thread = new Thread(() => { Start(); });
                    thread.Start();
                }
            }
        }

        private void Start()
        {
            while (!isStop)
            {
                tmrTick();
                Thread.Sleep(config.interval);
            }

            thread = null;
        }

        public void ShowText(string strText)
        {
            CommonString.IsOnRec = true;
            lblText.Text = strText;
            lblText.ForeColor = CommonString.IsDarkModel ? Color.White : Color.Black;
        }

        public void CloseLoading(int seconds = 0)
        {
            if (seconds > 0)
                for (var i = 0; i < seconds * 2; i++)
                {
                    Thread.Sleep(500);
                    Application.DoEvents();
                }

            Visible = false;
            isStop = true;
            CommonString.IsOnRec = false;
        }

        private void tmrTick()
        {
            try
            {
                if (i_c >= config.imgCount) i_c = 0;
                bgImg = LoadingTypeHelper.GetImageByConfig(config, i_c);
                //SetBits((Bitmap)bgImg);
                picImage.Image = bgImg;
                i_c++;
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        internal void SetLoadingType(LoadingType loadingType)
        {
            i_c = 0;
            config = LoadingTypeHelper.GetTypeConfig(loadingType);
        }
    }
}