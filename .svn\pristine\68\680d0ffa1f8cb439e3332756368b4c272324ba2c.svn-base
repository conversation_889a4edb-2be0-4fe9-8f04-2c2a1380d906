using System;
using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class FORMULA : Record
	{
		public ushort RowIndex;

		public ushort ColIndex;

		public ushort XFIndex;

		public ulong Result;

		public ushort OptionFlags;

		public uint Unused;

		public byte[] FormulaData;

		public STRING StringRecord;

		public FORMULA(Record record)
			: base(record)
		{
		}

		public FORMULA()
		{
			Type = 6;
		}

		public override void Decode()
		{
			MemoryStream memoryStream = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(memoryStream);
			RowIndex = binaryReader.ReadUInt16();
			ColIndex = binaryReader.ReadUInt16();
			XFIndex = binaryReader.ReadUInt16();
			Result = binaryReader.ReadUInt64();
			OptionFlags = binaryReader.ReadUInt16();
			Unused = binaryReader.ReadUInt32();
			FormulaData = binaryReader.ReadBytes((int)(memoryStream.Length - memoryStream.Position));
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(RowIndex);
			binaryWriter.Write(ColIndex);
			binaryWriter.Write(XFIndex);
			binaryWriter.Write(Result);
			binaryWriter.Write(OptionFlags);
			binaryWriter.Write(Unused);
			binaryWriter.Write(FormulaData);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}

		public object DecodeResult()
		{
			byte[] bytes = BitConverter.GetBytes(Result);
			if (bytes[6] == byte.MaxValue && bytes[7] == byte.MaxValue)
			{
				switch (bytes[0])
				{
				case 0:
					if (StringRecord != null)
					{
						return StringRecord.Value;
					}
					break;
				case 1:
					return Convert.ToBoolean(bytes[2]);
				case 2:
					return ErrorCode.ErrorCodes[bytes[2]];
				case 3:
					return string.Empty;
				}
			}
			return BitConverter.ToDouble(bytes, 0);
		}
	}
}
