﻿using OCRTools.UserControlEx;
using System.Windows.Forms;

namespace OCRTools
{
    partial class FrmBatchDect
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle6 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle7 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle8 = new System.Windows.Forms.DataGridViewCellStyle();
            this.dgContent = new OCRTools.DataGridViewEx();
            this.文件名 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.处理引擎 = new System.Windows.Forms.DataGridViewComboBoxColumn();
            this.原始大小 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.状态 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.查看 = new System.Windows.Forms.DataGridViewLinkColumn();
            this.btnProcess = new MetroFramework.Controls.MetroButton();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.btnAddFiles = new MetroFramework.Controls.MetroButton();
            this.btnAddFolder = new MetroFramework.Controls.MetroButton();
            this.btnClearFiles = new MetroFramework.Controls.MetroButton();
            this.btnClearSuccess = new MetroFramework.Controls.MetroButton();
            this.btnRemove = new MetroFramework.Controls.MetroButton();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label2 = new System.Windows.Forms.Label();
            this.txtSaveToPath = new System.Windows.Forms.TextBox();
            this.btnResultFolder = new System.Windows.Forms.Button();
            this.btnSelectedPath = new System.Windows.Forms.Button();
            this.grpOperate = new System.Windows.Forms.GroupBox();
            this.lblEngine = new System.Windows.Forms.Label();
            this.nFailedCount = new System.Windows.Forms.NumericUpDown();
            this.nMaxThread = new System.Windows.Forms.NumericUpDown();
            this.nTimeOutSecond = new System.Windows.Forms.NumericUpDown();
            this.lblRetryTimes = new System.Windows.Forms.Label();
            this.lblParTaskCount = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.lblTimeOut = new System.Windows.Forms.Label();
            this.chkSameFolder = new CheckBox();
            this.cmbCompressEngine = new System.Windows.Forms.ComboBox();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.panel2 = new System.Windows.Forms.Panel();
            ((System.ComponentModel.ISupportInitialize)(this.dgContent)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.grpOperate.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nFailedCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nMaxThread)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nTimeOutSecond)).BeginInit();
            this.panel2.SuspendLayout();
            this.SuspendLayout();
            // 
            // dgContent
            // 
            this.dgContent.AllowDrop = true;
            this.dgContent.AllowUserToAddRows = false;
            this.dgContent.AllowUserToDeleteRows = false;
            dataGridViewCellStyle5.ForeColor = System.Drawing.Color.Black;
            this.dgContent.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle5;
            this.dgContent.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgContent.BackgroundColor = System.Drawing.Color.White;
            this.dgContent.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.dgContent.CellClick += dgv_table_CellClick;
            this.dgContent.CellValueChanged += dgv_table_CellValueChanged;
            this.dgContent.CurrentCellDirtyStateChanged += dgv_table_CurrentCellDirtyStateChanged;
            dataGridViewCellStyle6.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(244)))), ((int)(((byte)(244)))), ((int)(((byte)(244)))));
            dataGridViewCellStyle6.Font = CommonString.GetSysNormalFont(12F);
            dataGridViewCellStyle6.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle6.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle6.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle6.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgContent.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle6;
            this.dgContent.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgContent.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.文件名,
            this.处理引擎,
            this.原始大小,
            this.状态,
            this.查看});
            dataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle7.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle7.Font = CommonString.GetSysNormalFont(12F);
            dataGridViewCellStyle7.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle7.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle7.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle7.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgContent.DefaultCellStyle = dataGridViewCellStyle7;
            this.dgContent.IsShowSequence = true;
            this.dgContent.Location = new System.Drawing.Point(0, 0);
            this.dgContent.Margin = new System.Windows.Forms.Padding(0);
            this.dgContent.Name = "dgContent";
            dataGridViewCellStyle8.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle8.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle8.Font = CommonString.GetSysNormalFont(12F);
            dataGridViewCellStyle8.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle8.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle8.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle8.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgContent.RowHeadersDefaultCellStyle = dataGridViewCellStyle8;
            this.dgContent.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.AutoSizeToAllHeaders;
            this.dgContent.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.Black;
            this.dgContent.RowTemplate.Height = 30;
            this.dgContent.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgContent.Size = new System.Drawing.Size(806, 618);
            this.dgContent.TabIndex = 3;
            this.dgContent.TabStop = false;
            this.dgContent.DragDrop += new System.Windows.Forms.DragEventHandler(this.dgContent_DragDrop);
            this.dgContent.DragEnter += new System.Windows.Forms.DragEventHandler(this.dgContent_DragEnter);
            // 
            // 文件名
            // 
            this.文件名.DataPropertyName = "FileName";
            this.文件名.FillWeight = 30F;
            this.文件名.HeaderText = "文件名";
            this.文件名.Name = "文件名";
            this.文件名.ReadOnly = true;
            this.文件名.Width = 300;
            // 
            // 处理引擎
            // 
            this.处理引擎.DataPropertyName = "CompressType";
            this.处理引擎.FillWeight = 20F;
            this.处理引擎.HeaderText = "处理方式";
            this.处理引擎.Name = "处理方式";
            // 
            // 原始大小
            // 
            this.原始大小.DataPropertyName = "InPutSize";
            this.原始大小.FillWeight = 10F;
            this.原始大小.HeaderText = "文件大小";
            this.原始大小.Name = "原始大小";
            this.原始大小.ReadOnly = true;
            // 
            // 状态
            // 
            this.状态.DataPropertyName = "State";
            this.状态.FillWeight = 15F;
            this.状态.HeaderText = "状态";
            this.状态.Name = "状态";
            this.状态.ReadOnly = true;
            // 
            // 查看
            // 
            this.查看.FillWeight = 20F;
            this.查看.HeaderText = "查看";
            this.查看.Name = "查看";
            this.查看.ReadOnly = true;
            this.查看.Text = "查看结果";
            this.查看.Width = 120;
            // 
            // btnProcess
            // 
            this.btnProcess.Font = CommonString.GetSysBoldFont(12F);
            this.btnProcess.Location = new System.Drawing.Point(24, 180);
            this.btnProcess.Size = new System.Drawing.Size(103, 38);
            this.btnProcess.Style = MetroColorStyle.黑色;
            this.btnProcess.TabIndex = 0;
            this.btnProcess.Text = "开始(&S)";
            this.btnProcess.UseSelectable = true;
            this.btnProcess.UseVisualStyleBackColor = true;
            this.btnProcess.Click += new System.EventHandler(this.btnProcess_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.BackColor = System.Drawing.Color.White;
            this.groupBox1.Controls.Add(this.btnAddFiles);
            this.groupBox1.Controls.Add(this.btnAddFolder);
            this.groupBox1.Location = new System.Drawing.Point(817, 10);
            this.groupBox1.Size = new System.Drawing.Size(147, 98);
            this.groupBox1.TabIndex = 7;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "添加";
            // 
            // btnAddFiles
            // 
            this.btnAddFiles.Location = new System.Drawing.Point(31, 21);
            this.btnAddFiles.Size = new System.Drawing.Size(88, 29);
            this.btnAddFiles.Style = MetroColorStyle.黑色;
            this.btnAddFiles.TabIndex = 6;
            this.btnAddFiles.TabStop = false;
            this.btnAddFiles.Text = "文件";
            this.btnAddFiles.UseSelectable = true;
            this.btnAddFiles.UseVisualStyleBackColor = true;
            this.btnAddFiles.Click += new System.EventHandler(this.btnAddFiles_Click);
            // 
            // btnAddFolder
            // 
            this.btnAddFolder.Location = new System.Drawing.Point(31, 55);
            this.btnAddFolder.Size = new System.Drawing.Size(88, 29);
            this.btnAddFolder.Style = MetroColorStyle.黑色;
            this.btnAddFolder.TabIndex = 6;
            this.btnAddFolder.TabStop = false;
            this.btnAddFolder.Text = "文件夹";
            this.btnAddFolder.UseSelectable = true;
            this.btnAddFolder.UseVisualStyleBackColor = true;
            this.btnAddFolder.Click += new System.EventHandler(this.btnAddFolder_Click);
            // 
            // btnClearFiles
            // 
            this.btnClearFiles.Location = new System.Drawing.Point(32, 79);
            this.btnClearFiles.Size = new System.Drawing.Size(88, 26);
            this.btnClearFiles.Style = MetroColorStyle.黑色;
            this.btnClearFiles.TabIndex = 6;
            this.btnClearFiles.TabStop = false;
            this.btnClearFiles.Text = "清空所有";
            this.btnClearFiles.UseSelectable = true;
            this.btnClearFiles.UseVisualStyleBackColor = true;
            this.btnClearFiles.Click += new System.EventHandler(this.btnClearFiles_Click);
            // 
            // btnClearSuccess
            // 
            this.btnClearSuccess.Location = new System.Drawing.Point(32, 48);
            this.btnClearSuccess.Size = new System.Drawing.Size(88, 26);
            this.btnClearSuccess.Style = MetroColorStyle.黑色;
            this.btnClearSuccess.TabIndex = 6;
            this.btnClearSuccess.TabStop = false;
            this.btnClearSuccess.Text = "移除已完成";
            this.btnClearSuccess.UseSelectable = true;
            this.btnClearSuccess.UseVisualStyleBackColor = true;
            this.btnClearSuccess.Click += new System.EventHandler(this.btnClearSuccess_Click);
            // 
            // btnRemove
            // 
            this.btnRemove.Location = new System.Drawing.Point(32, 17);
            this.btnRemove.Size = new System.Drawing.Size(88, 26);
            this.btnRemove.Style = MetroColorStyle.黑色;
            this.btnRemove.TabIndex = 6;
            this.btnRemove.TabStop = false;
            this.btnRemove.Text = "移除选择项";
            this.btnRemove.UseSelectable = true;
            this.btnRemove.UseVisualStyleBackColor = true;
            this.btnRemove.Click += new System.EventHandler(this.btnRemoveSelected_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox2.BackColor = System.Drawing.Color.White;
            this.groupBox2.Controls.Add(this.btnClearSuccess);
            this.groupBox2.Controls.Add(this.btnRemove);
            this.groupBox2.Controls.Add(this.btnClearFiles);
            this.groupBox2.Location = new System.Drawing.Point(817, 488);
            this.groupBox2.Size = new System.Drawing.Size(147, 116);
            this.groupBox2.TabIndex = 8;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "移除";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.ForeColor = System.Drawing.Color.Red;
            this.label2.Location = new System.Drawing.Point(12, 240);
            this.label2.Size = new System.Drawing.Size(107, 12);
            this.label2.TabIndex = 8;
            this.label2.Text = "将文件保存到:";
            // 
            // txtSaveToPath
            // 
            this.txtSaveToPath.BackColor = System.Drawing.SystemColors.ControlLightLight;
            this.txtSaveToPath.Location = new System.Drawing.Point(12, 261);
            this.txtSaveToPath.Name = "txtSaveToPath";
            this.txtSaveToPath.ReadOnly = true;
            this.txtSaveToPath.Size = new System.Drawing.Size(97, 21);
            this.txtSaveToPath.TabIndex = 10;
            // 
            // btnResultFolder
            // 
            this.btnResultFolder.Location = new System.Drawing.Point(23, 312);
            this.btnResultFolder.Size = new System.Drawing.Size(104, 30);
            this.btnResultFolder.TabIndex = 9;
            this.btnResultFolder.Text = "打开结果目录";
            this.btnResultFolder.UseVisualStyleBackColor = true;
            this.btnResultFolder.Click += new System.EventHandler(this.btnResultFolder_Click);
            // 
            // btnSelectedPath
            // 
            this.btnSelectedPath.Location = new System.Drawing.Point(110, 260);
            this.btnSelectedPath.Size = new System.Drawing.Size(31, 23);
            this.btnSelectedPath.TabIndex = 9;
            this.btnSelectedPath.Text = "...";
            this.btnSelectedPath.UseVisualStyleBackColor = true;
            this.btnSelectedPath.Click += new System.EventHandler(this.btnSelectedPath_Click);
            // 
            // groupBox3
            // 
            this.grpOperate.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.grpOperate.BackColor = System.Drawing.Color.White;
            this.grpOperate.Controls.Add(this.lblEngine);
            this.grpOperate.Controls.Add(this.nFailedCount);
            this.grpOperate.Controls.Add(this.nMaxThread);
            this.grpOperate.Controls.Add(this.nTimeOutSecond);
            this.grpOperate.Controls.Add(this.lblRetryTimes);
            this.grpOperate.Controls.Add(this.lblParTaskCount);
            this.grpOperate.Controls.Add(this.label3);
            this.grpOperate.Controls.Add(this.lblTimeOut);
            this.grpOperate.Controls.Add(this.chkSameFolder);
            this.grpOperate.Controls.Add(this.btnProcess);
            this.grpOperate.Controls.Add(this.label2);
            this.grpOperate.Controls.Add(this.btnSelectedPath);
            this.grpOperate.Controls.Add(this.btnResultFolder);
            this.grpOperate.Controls.Add(this.cmbCompressEngine);
            this.grpOperate.Controls.Add(this.txtSaveToPath);
            this.grpOperate.Location = new System.Drawing.Point(817, 120);
            this.grpOperate.Size = new System.Drawing.Size(147, 363);
            this.grpOperate.TabIndex = 9;
            this.grpOperate.TabStop = false;
            this.grpOperate.Text = "操作";
            // 
            // lblEngine
            // 
            this.lblEngine.AutoSize = true;
            this.lblEngine.Font = CommonString.GetSysBoldFont(12);
            this.lblEngine.Location = new System.Drawing.Point(5, 21);
            this.lblEngine.Size = new System.Drawing.Size(99, 19);
            this.lblEngine.TabIndex = 48;
            this.lblEngine.Text = "选择处理方式";
            // 
            // nFailedCount
            // 
            this.nFailedCount.Location = new System.Drawing.Point(91, 123);
            this.nFailedCount.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.nFailedCount.Name = "nFailedCount";
            this.nFailedCount.Size = new System.Drawing.Size(38, 21);
            this.nFailedCount.TabIndex = 11;
            this.nFailedCount.TabStop = false;
            this.nFailedCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nFailedCount.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // nMaxThread
            // 
            this.nMaxThread.Location = new System.Drawing.Point(103, 98);
            this.nMaxThread.Maximum = new decimal(new int[] {
            30,
            0,
            0,
            0});
            this.nMaxThread.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.nMaxThread.Size = new System.Drawing.Size(38, 21);
            this.nMaxThread.TabIndex = 11;
            this.nMaxThread.TabStop = false;
            this.nMaxThread.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nMaxThread.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // nTimeOutSecond
            // 
            this.nTimeOutSecond.Location = new System.Drawing.Point(65, 73);
            this.nTimeOutSecond.Maximum = new decimal(new int[] {
            360,
            0,
            0,
            0});
            this.nTimeOutSecond.Minimum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.nTimeOutSecond.Size = new System.Drawing.Size(46, 21);
            this.nTimeOutSecond.TabIndex = 11;
            this.nTimeOutSecond.TabStop = false;
            this.nTimeOutSecond.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nTimeOutSecond.Value = new decimal(new int[] {
            60,
            0,
            0,
            0});
            // 
            // lblRetryTimes
            // 
            this.lblRetryTimes.AutoSize = true;
            this.lblRetryTimes.Location = new System.Drawing.Point(6, 127);
            this.lblRetryTimes.Size = new System.Drawing.Size(77, 12);
            this.lblRetryTimes.TabIndex = 12;
            this.lblRetryTimes.Text = "失败重试次数";
            // 
            // lblParTaskCount
            // 
            this.lblParTaskCount.AutoSize = true;
            this.lblParTaskCount.Location = new System.Drawing.Point(6, 102);
            this.lblParTaskCount.Size = new System.Drawing.Size(89, 12);
            this.lblParTaskCount.TabIndex = 12;
            this.lblParTaskCount.Text = "同时处理任务数";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(108, 77);
            this.label3.Size = new System.Drawing.Size(17, 12);
            this.label3.TabIndex = 12;
            this.label3.Text = "秒";
            // 
            // label1
            // 
            this.lblTimeOut.AutoSize = true;
            this.lblTimeOut.Location = new System.Drawing.Point(6, 77);
            this.lblTimeOut.Size = new System.Drawing.Size(53, 12);
            this.lblTimeOut.TabIndex = 12;
            this.lblTimeOut.Text = "处理超时";
            // 
            // chkSameFolder
            // 
            this.chkSameFolder.AutoSize = true;
            this.chkSameFolder.Location = new System.Drawing.Point(12, 289);
            this.chkSameFolder.Size = new System.Drawing.Size(108, 16);
            this.chkSameFolder.TabIndex = 8;
            this.chkSameFolder.Text = "保存在图片目录";
            this.chkSameFolder.UseVisualStyleBackColor = true;
            this.chkSameFolder.ToolTip(toolTip1, "功能：压缩结果，是否存放在与图片相同的目录。\r\n说明：\r\n      如果图片来源文件夹比较多，使用时请慎重！结果将分散在各个目录下边！\r\n");
            // 
            // cmbCompressEngine
            // 
            this.cmbCompressEngine.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbCompressEngine.Font = CommonString.GetSysNormalFont(10F);
            this.cmbCompressEngine.FormattingEnabled = true;
            this.cmbCompressEngine.ItemHeight = 13;
            this.cmbCompressEngine.Location = new System.Drawing.Point(10, 44);
            this.cmbCompressEngine.Name = "cmbCompressEngine";
            this.cmbCompressEngine.Size = new System.Drawing.Size(128, 21);
            this.cmbCompressEngine.TabIndex = 5;
            this.cmbCompressEngine.TabStop = false;
            // 
            // panel2
            // 
            this.panel2.BackColor = System.Drawing.Color.White;
            this.panel2.Controls.Add(this.grpOperate);
            this.panel2.Controls.Add(this.dgContent);
            this.panel2.Controls.Add(this.groupBox2);
            this.panel2.Controls.Add(this.groupBox1);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel2.Location = new System.Drawing.Point(20, 60);
            this.panel2.Size = new System.Drawing.Size(976, 621);
            this.panel2.TabIndex = 46;
            // 
            // FrmBatchDect
            // 
            this.ClientSize = new System.Drawing.Size(1016, 701);
            this.Controls.Add(this.panel2);
            this.Name = "FrmBatchDect";
            this.Text = "批量文档矫正";
            this.Padding = new System.Windows.Forms.Padding(10, 60, 10, 10);
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FrmBatch_FormClosing);
            this.Load += new System.EventHandler(this.FrmBatch_Load);
            ((System.ComponentModel.ISupportInitialize)(this.dgContent)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            this.grpOperate.ResumeLayout(false);
            this.grpOperate.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nFailedCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nMaxThread)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nTimeOutSecond)).EndInit();
            this.panel2.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DataGridViewEx dgContent;
        private MetroFramework.Controls.MetroButton btnProcess;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private MetroFramework.Controls.MetroButton btnClearFiles;
        private MetroFramework.Controls.MetroButton btnAddFolder;
        private MetroFramework.Controls.MetroButton btnClearSuccess;
        private MetroFramework.Controls.MetroButton btnAddFiles;
        private MetroFramework.Controls.MetroButton btnRemove;
        private System.Windows.Forms.GroupBox grpOperate;
        private System.Windows.Forms.TextBox txtSaveToPath;
        private System.Windows.Forms.Button btnSelectedPath;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btnResultFolder;
        private System.Windows.Forms.NumericUpDown nMaxThread;
        private System.Windows.Forms.NumericUpDown nTimeOutSecond;
        private System.Windows.Forms.Label lblParTaskCount;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label lblTimeOut;
        private System.Windows.Forms.NumericUpDown nFailedCount;
        private System.Windows.Forms.Label lblRetryTimes;
        private CheckBox chkSameFolder;
        private ToolTip toolTip1;
        private ComboBox cmbCompressEngine;
        private DataGridViewTextBoxColumn 文件名;
        private DataGridViewComboBoxColumn 处理引擎;
        private DataGridViewTextBoxColumn 原始大小;
        private DataGridViewTextBoxColumn 状态;
        private DataGridViewLinkColumn 查看;
        private Label lblEngine;
        private Panel panel2;
    }
}