using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Globalization;
using System.Runtime.Serialization;
using System.Windows.Forms;

namespace OCRTools
{
    internal class DrawArrow : DrawObject
    {
        private Point _endPoint;
        private Point _startPoint;

        public DrawArrow()
            : this(0, 0, 1, 0)
        {
        }

        public DrawArrow(int x1, int y1, int x2, int y2)
        {
            _startPoint.X = x1;
            _startPoint.Y = y1;
            _endPoint.X = x2;
            _endPoint.Y = y2;
            Initialize();
        }

        public override int HandleCount => 2;

        public override DrawToolType NoteType => DrawToolType.Arrow;

        protected GraphicsPath AreaPath { get; set; }

        protected Pen AreaPen { get; set; }

        protected Region AreaRegion { get; set; }

        public override Rectangle GetBoundingBox()
        {
            using (var graphicsPath = CreateArrowPath())
            {
                var bounds = graphicsPath.GetBounds();
                if (IsSelected)
                {
                    var num = 8.DpiValue();
                    bounds.Inflate(num, num);
                }

                return Rectangle.Ceiling(bounds);
            }
        }

        private GraphicsPath CreateArrowPathBoth()
        {
            var graphicsPath = new GraphicsPath();
            var points = new[]
            {
                _startPoint,
                _endPoint
            };
            graphicsPath.AddCurve(points, 0.7f);
            return graphicsPath;
        }

        private GraphicsPath CreateArrowPath()
        {
            var graphicsPath = new GraphicsPath();
            if (LastIsArrowBoth)
            {
                graphicsPath = CreateArrowPathBoth();
            }
            else
            {
                var p = new Pen(Color, PenWidth);
                DrawArrowPath(graphicsPath, p, _startPoint, _endPoint);
            }

            return graphicsPath;
        }

        public override DrawObject Clone()
        {
            var drawArrow = new DrawArrow
            {
                _startPoint = _startPoint,
                _endPoint = _endPoint
            };
            FillDrawObjectFields(drawArrow);
            return drawArrow;
        }

        protected Pen CreatePen(Color borderColor, int borderSize)
        {
            using (var graphicsPath = new GraphicsPath())
            {
                var num = 2;
                var num2 = 6;
                var num3 = 1;
                graphicsPath.AddLine(new Point(0, 0), new Point(-num, -num2));
                graphicsPath.AddCurve(new[]
                {
                    new Point(-num, -num2),
                    new Point(0, -num2 + num3),
                    new Point(num, -num2)
                });
                graphicsPath.CloseFigure();
                var customLineCap = new CustomLineCap(graphicsPath, null)
                {
                    BaseInset = num2 - num3
                };
                var pen = new Pen(borderColor, borderSize)
                {
                    CustomEndCap = customLineCap
                };
                if (MathHelpers.Distance(_startPoint, _endPoint) > num2 * borderSize * 2)
                    pen.CustomStartCap = customLineCap;
                pen.LineJoin = LineJoin.Round;
                return pen;
            }
        }

        public void DrawBoth(Graphics g)
        {
            g.SmoothingMode = SmoothingMode.HighQuality;
            var point = _startPoint;
            var point2 = _endPoint;
            var value = Math.Sqrt(Math.Abs(point.X - point2.X) * Math.Abs(point.X - point2.X) +
                                  Math.Abs(point.Y - point2.Y) * Math.Abs(point.Y - point2.Y));
            var num = Convert.ToInt32(value);
            var num2 = Convert.ToInt32(value);
            StartPoint = _startPoint;
            EndPoint = _endPoint;
            Rectangle = new Rectangle(0, 0, num, num2);
            if (num > 6 || num2 > 6)
            {
                var point3 = new Point(_startPoint.X, _startPoint.Y);
                var point4 = new Point(_endPoint.X, _endPoint.Y);
                var points = new[]
                {
                    point3,
                    point4
                };
                DrawLineEx(g, Color, PenWidth, points);
            }
        }

        public void DrawLineEx(Graphics g, Color borderColor, int borderSize, Point[] points)
        {
            var borderSize2 = 1;
            switch (borderSize)
            {
                case 1:
                    borderSize2 = 2;
                    break;
                case 2:
                    borderSize2 = 3;
                    break;
                case 3:
                    borderSize2 = 4;
                    break;
            }

            if (borderSize > 0 && borderColor.A > 0)
                using (var pen = CreatePen(borderColor, borderSize2))
                {
                    g.DrawLine(pen, points[0], points[1]);
                }
        }

        public override void Draw(Graphics g)
        {
            if (IsArrowBoth)
            {
                DrawBoth(g);
                return;
            }

            g.SmoothingMode = SmoothingMode.HighQuality;
            var point = _startPoint;
            var point2 = _endPoint;
            var value = Math.Sqrt(Math.Abs(point.X - point2.X) * Math.Abs(point.X - point2.X) +
                                  Math.Abs(point.Y - point2.Y) * Math.Abs(point.Y - point2.Y));
            var num = Convert.ToInt32(value);
            var num2 = Convert.ToInt32(value);
            StartPoint = _startPoint;
            EndPoint = _endPoint;
            Rectangle = new Rectangle(0, 0, num, num2);
            if (num > 6 || num2 > 6)
            {
                var pen = new Pen(Color, PenWidth.DpiValue());
                DrawUArrowF(g, pen, _startPoint, _endPoint);
                pen.Dispose();
            }
        }

        private void DrawArrowPath(GraphicsPath g, Pen p, Point start, Point end)
        {
            var num = 8;
            num = ((int) p.Width - 1) * 5 + num;
            var num2 = Math.Sqrt(Math.Abs(start.X - end.X) * Math.Abs(start.X - end.X) +
                                 Math.Abs(start.Y - end.Y) * Math.Abs(start.Y - end.Y));
            if (num2 > 5.0)
            {
                var num3 = Math.Atan2(end.Y - start.Y, end.X - start.X);
                var num4 = Math.Sin(num3);
                var num5 = Math.Cos(num3);
                var pointF = new PointF(end.X + (float) (-num * num5 - num / 2.0 * num4),
                    end.Y + (float) (-num * num4 + num / 2.0 * num5));
                var pointF2 = new PointF(end.X + (float) (-num * num5 + num / 2.0 * num4),
                    end.Y - (float) (num / 2.0 * num5 + num * num4));
                num -= num / 10;
                var num6 = (float) (num * num4 / 4.5);
                var num7 = (float) (num * num5 / 4.5);
                var pointF3 = new PointF(end.X + (float) (-num * num5 - num / 2.0 * num4) + num6,
                    end.Y + (float) (-num * num4 + num / 2.0 * num5) - num7);
                var pointF4 = new PointF(end.X + (float) (-num * num5 + num / 2.0 * num4 - num6),
                    end.Y - (float) (num / 2.0 * num5 + num * num4) + num7);
                var points = new[]
                {
                    start,
                    pointF3,
                    pointF,
                    end,
                    pointF2,
                    pointF4
                };
                g.AddPolygon(points);
            }
        }

        private void DrawUArrowF(Graphics g, Pen p, Point start, Point end)
        {
            var num = 8;
            num = ((int) p.Width - 1) * 5 + num;
            var num2 = Math.Sqrt(Math.Abs(start.X - end.X) * Math.Abs(start.X - end.X) +
                                 Math.Abs(start.Y - end.Y) * Math.Abs(start.Y - end.Y));
            if (num2 > 5.0)
            {
                var num3 = Math.Atan2(end.Y - start.Y, end.X - start.X);
                var num4 = Math.Sin(num3);
                var num5 = Math.Cos(num3);
                var pointF = new PointF(end.X + (float) (-num * num5 - num / 2.0 * num4),
                    end.Y + (float) (-num * num4 + num / 2.0 * num5));
                var pointF2 = new PointF(end.X + (float) (-num * num5 + num / 2.0 * num4),
                    end.Y - (float) (num / 2.0 * num5 + num * num4));
                num -= num / 10;
                var num6 = (float) (num * num4 / 4.5);
                var num7 = (float) (num * num5 / 4.5);
                var pointF3 = new PointF(end.X + (float) (-num * num5 - num / 2.0 * num4) + num6,
                    end.Y + (float) (-num * num4 + num / 2.0 * num5) - num7);
                var pointF4 = new PointF(end.X + (float) (-num * num5 + num / 2.0 * num4 - num6),
                    end.Y - (float) (num / 2.0 * num5 + num * num4) + num7);
                var fillMode = FillMode.Alternate;
                var brush = new SolidBrush(p.Color);
                var points = new[]
                {
                    start,
                    pointF3,
                    pointF,
                    end,
                    pointF2,
                    pointF4
                };
                g.FillPolygon(brush, points, fillMode);
                var pen = new Pen(p.Color, 1f);
                g.DrawLine(pen, start, end);
                pen.Dispose();
            }
        }

        public override Point GetHandle(int handleNumber)
        {
            if (handleNumber == 1) return _startPoint;
            return _endPoint;
        }

        public override int HitTest(Point point)
        {
            if (Selected)
                for (var i = 1; i <= HandleCount; i++)
                    if (GetHandleRectangle(i).Contains(point))
                        return i;
            if (PointInObject(point)) return 0;
            return -1;
        }

        public override bool PointInObject(Point point)
        {
            if (_startPoint == _endPoint) return false;
            CreateObjects();
            var graphicsPath = new GraphicsPath();
            var pen = new Pen(Color.Black, 20f);
            graphicsPath.AddLine(_startPoint.X, _startPoint.Y, _endPoint.X, _endPoint.Y);
            graphicsPath.Widen(pen);
            return graphicsPath.IsVisible(point);
        }

        public override bool IntersectsWith(Rectangle rectangle)
        {
            if (AreaRegion == null) return false;
            CreateObjects();
            return AreaRegion.IsVisible(rectangle);
        }

        public override Cursor GetHandleCursor(int handleNumber)
        {
            switch (handleNumber)
            {
                case 1:
                    return CursorEx.Arrow;
                case 2:
                    return CursorEx.Arrow;
                default:
                    return CursorEx.Arrow;
            }
        }

        public override void MoveHandleTo(Point point, int handleNumber)
        {
            if (handleNumber == 1)
                _startPoint = point;
            else
                _endPoint = point;
            Invalidate();
        }

        public override void MoveHandleTo(Point point, int handleNumber, bool shiftPressed)
        {
            var degree = 45f;
            var startDegree = 0f;
            switch (handleNumber)
            {
                case 1:
                    _startPoint = shiftPressed ? SnapPositionToDegree(_endPoint, point, degree, startDegree) : point;
                    break;
                case 2:
                    _endPoint = shiftPressed ? SnapPositionToDegree(_startPoint, point, degree, startDegree) : point;
                    break;
            }
        }

        public override void Move(int deltaX, int deltaY)
        {
            _startPoint.X += deltaX;
            _startPoint.Y += deltaY;
            _endPoint.X += deltaX;
            _endPoint.Y += deltaY;
            Invalidate();
        }

        public override void SaveToStream(SerializationInfo info, int orderNumber)
        {
            info.AddValue(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "Start", orderNumber), _startPoint);
            info.AddValue(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "End", orderNumber), _endPoint);
            base.SaveToStream(info, orderNumber);
        }

        public override void LoadFromStream(SerializationInfo info, int orderNumber)
        {
            _startPoint =
                (Point) info.GetValue(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "Start", orderNumber),
                    typeof(Point));
            _endPoint = (Point) info.GetValue(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "End", orderNumber),
                typeof(Point));
            base.LoadFromStream(info, orderNumber);
        }

        protected void Invalidate()
        {
            if (AreaPath != null)
            {
                AreaPath.Dispose();
                AreaPath = null;
            }

            if (AreaPen != null)
            {
                AreaPen.Dispose();
                AreaPen = null;
            }

            if (AreaRegion != null)
            {
                AreaRegion.Dispose();
                AreaRegion = null;
            }
        }

        protected virtual void CreateObjects()
        {
            if (AreaPath == null && _startPoint != _endPoint)
            {
                AreaPath = new GraphicsPath();
                AreaPen = new Pen(Color.Black, 7f);
                AreaPath.AddLine(_startPoint.X, _startPoint.Y, _endPoint.X, _endPoint.Y);
                AreaPath.Widen(AreaPen);
                AreaRegion = new Region(AreaPath);
            }
        }
    }
}