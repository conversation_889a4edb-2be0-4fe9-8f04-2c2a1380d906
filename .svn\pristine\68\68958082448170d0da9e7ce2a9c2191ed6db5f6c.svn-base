using System.Runtime.InteropServices;

namespace OCRTools
{
    [StructLayout(LayoutKind.Explicit)]
    public struct ColorBgra
    {
        [FieldOffset(0)] public uint Bgra;

        [FieldOffset(0)] public byte Blue;

        [FieldOffset(1)] public byte Green;

        [FieldOffset(2)] public byte Red;

        [FieldOffset(3)] public byte Alpha;

        public ColorBgra(uint bgra)
        {
            this = default(ColorBgra);
            Bgra = bgra;
        }

        public ColorBgra(byte b, byte g, byte r, byte a = byte.MaxValue)
        {
            this = default(ColorBgra);
            Blue = b;
            Green = g;
            Red = r;
            Alpha = a;
        }

        public static bool operator ==(ColorBgra c1, ColorBgra c2)
        {
            return c1.Bgra == c2.Bgra;
        }

        public static bool operator !=(ColorBgra c1, ColorBgra c2)
        {
            return c1.Bgra != c2.Bgra;
        }

        public override bool Equals(object obj)
        {
            return obj is ColorBgra bgra && bgra.Bgra == Bgra;
        }

        public override int GetHashCode()
        {
            return (int) Bgra;
        }

        public static implicit operator ColorBgra(uint color)
        {
            return new ColorBgra(color);
        }

        public static implicit operator uint(ColorBgra color)
        {
            return color.Bgra;
        }

        public override string ToString()
        {
            return $"B: {Blue}, G: {Green}, R: {Red}, A: {Alpha}";
        }
    }
}