﻿using OCRTools.Common;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace OCRTools
{
    internal class CommonCDNRequest<T>
    {
        static readonly List<string> lstDns = new List<string>() { "119.29.29.29", "223.5.5.5", "114.114.114.114", "8.8.8.8", "" };

        public static ConcurrentBag<T> GetInfo(string url, Func<string, T> ParseText, string cdn = null, int maxTimeOut = 5)
        {
            //Stopwatch stopwatch = Stopwatch.StartNew();
            ConcurrentBag<T> sites = new ConcurrentBag<T>();
            Parallel.For(1, 3, (index) =>
            {
                T tmp = default;
                switch (index)
                {
                    case 1:
                        string strTmp = cdn;
                        if (string.IsNullOrEmpty(strTmp))
                        {
                            //没传默认按URL截取
                            strTmp = url.Substring(url.LastIndexOf("/") + 1);
                            strTmp = strTmp.Substring(0, strTmp.IndexOf('.'));

                            if (!string.IsNullOrEmpty(strTmp))
                                strTmp = "TXT-" + strTmp + CommonString.StrBaseServerHost;
                        }
                        if (!string.IsNullOrEmpty(strTmp))
                        {
                            tmp = InitByCName(strTmp, ParseText, maxTimeOut);
                        }
                        break;
                    case 2:
                        tmp = InitFromCDN(url, ParseText, maxTimeOut);
                        break;
                }
                if (tmp != null)
                    sites.Add(tmp);
            });
            //Console.WriteLine("======耗时：" + stopwatch.ElapsedMilliseconds.ToString("F0") + "ms");
            return sites;
        }

        private static T InitFromCDN(string url, Func<string, T> ParseText, int maxTimeOut)
        {
            var html = WebClientExt.GetHtml(url + "?t=" + DateTime.Now.Ticks, maxTimeOut);
            if (!string.IsNullOrEmpty(html))
            {
                html = Regex.Unescape(html);
            }
            return ParseText(html);
        }

        public static T InitByCName(string host, Func<string, T> ParseText, int maxTimeOut)
        {
            var result = string.Empty;
            maxTimeOut *= 1000;

            var lstParam = lstDns.Select(dns => new TaskParam() { Param1 = host, Param2 = dns }).ToList();

            result = CommonTask<string>.GetFastestValidResult(lstParam, GetCNameResult, 3, maxTimeOut, IsValidResult);

            return ParseText(result);
        }

        static bool IsValidResult(string result) => !string.IsNullOrEmpty(result);

        private static string GetCNameResult(TaskParam param)
        {
            var strResult = string.Empty;
            var strTmp = GetCNameFromNsLookUp(param.Param1, (string)param.Param2);

            if (!(string.IsNullOrEmpty(strTmp) || (!strTmp.Contains(".") && !strTmp.Contains("+"))))
            {
                strResult = strTmp.Replace("\"\r\n\t\"", "");
            }
            return strResult;
        }

        private const string strCName = "\"";

        static string GetCNameFromNsLookUp(string strHost, string strNsServer)
        {
            var result = "";
            var strTmp = ExecCmd(string.Format("set qt=TXT \n{0}\n{1}", strHost, strNsServer));
            if (strTmp.Contains(strHost))
                if (strTmp.IndexOf(strCName) > 0)
                {
                    result = CommonMethod.SubString(strTmp, strCName);
                    result = result.Substring(0, result.LastIndexOf(strCName));
                }

            return result;
        }

        public static string GetCNameByNsLook(string host, string server)
        {
            var dnsServers = new IPAddress[] { IPAddress.Parse(server) };
            var hostEntry = Dns.GetHostEntry(host);

            var txtRecords = hostEntry.Aliases
                .Where(r => r.StartsWith("\"") && r.EndsWith("\""))
                .Select(r => r.Trim('"'))
                .ToArray();
            return "";
        }

        static string ExecCmd(string str)
        {
            //process用于调用外部程序
            using (var p = new Process())
            {
                //调用cmd.exe
                p.StartInfo.FileName = "nslookup.exe";
                //不显示程序窗口
                p.StartInfo.CreateNoWindow = true;
                //是否指定操作系统外壳进程启动程序
                p.StartInfo.UseShellExecute = false;
                //可能接受来自调用程序的输入信息
                //重定向标准输入
                p.StartInfo.RedirectStandardInput = true;
                //重定向标准输出
                p.StartInfo.RedirectStandardOutput = true;
                //重定向错误输出
                p.StartInfo.RedirectStandardError = true;
                //启动程序
                p.Start();
                //输入命令
                p.StandardInput.WriteLine(str);
                //一定要关闭。
                p.StandardInput.WriteLine("exit");
                return p.StandardOutput.ReadToEnd();
            }
        }
    }
}
