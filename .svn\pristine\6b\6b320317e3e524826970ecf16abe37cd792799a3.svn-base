﻿#region License Information (GPL v3)

/*
    ShareX - A program that allows you to take screenshots and share any file type
    Copyright (c) 2007-2020 ShareX Team

    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License
    as published by the Free Software Foundation; either version 2
    of the License, or (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.

    Optionally you can also view the license at <http://www.gnu.org/licenses/>.
*/

#endregion License Information (GPL v3)

using System.Drawing;

namespace OCRTools
{
    public struct CMYK
    {
        private double cyan;
        private double magenta;
        private double yellow;
        private double key;
        private int alpha;

        public double Cyan
        {
            get => cyan;
            set => cyan = ColorHelper.ValidColor(value);
        }

        public double Cyan100
        {
            get => cyan * 100;
            set => cyan = ColorHelper.ValidColor(value / 100);
        }

        public double Magenta
        {
            get => magenta;
            set => magenta = ColorHelper.ValidColor(value);
        }

        public double Magenta100
        {
            get => magenta * 100;
            set => magenta = ColorHelper.ValidColor(value / 100);
        }

        public double Yellow
        {
            get => yellow;
            set => yellow = ColorHelper.ValidColor(value);
        }

        public double Yellow100
        {
            get => yellow * 100;
            set => yellow = ColorHelper.ValidColor(value / 100);
        }

        public double Key
        {
            get => key;
            set => key = ColorHelper.ValidColor(value);
        }

        public double Key100
        {
            get => key * 100;
            set => key = ColorHelper.ValidColor(value / 100);
        }

        public int Alpha
        {
            get => alpha;
            set => alpha = ColorHelper.ValidColor(value);
        }

        public CMYK(double cyan, double magenta, double yellow, double key, int alpha = 255) : this()
        {
            Cyan = cyan;
            Magenta = magenta;
            Yellow = yellow;
            Key = key;
            Alpha = alpha;
        }

        public CMYK(int cyan, int magenta, int yellow, int key, int alpha = 255) : this()
        {
            Cyan100 = cyan;
            Magenta100 = magenta;
            Yellow100 = yellow;
            Key100 = key;
            Alpha = alpha;
        }

        public CMYK(Color color)
        {
            this = ColorHelper.ColorToCMYK(color);
        }

        public static implicit operator CMYK(Color color)
        {
            return ColorHelper.ColorToCMYK(color);
        }

        public static implicit operator Color(CMYK color)
        {
            return color.ToColor();
        }

        public static implicit operator RGBA(CMYK color)
        {
            return color.ToColor();
        }

        public static implicit operator HSB(CMYK color)
        {
            return color.ToColor();
        }

        public static bool operator ==(CMYK left, CMYK right)
        {
            return left.Cyan == right.Cyan && left.Magenta == right.Magenta && left.Yellow == right.Yellow &&
                   left.Key == right.Key;
        }

        public static bool operator !=(CMYK left, CMYK right)
        {
            return !(left == right);
        }

        public override string ToString()
        {
            return string.Format("青色: {0:0.0}%，品红: {1:0.0}%，黄色: {2:0.0}%, 键: {3:0.0}%", Cyan100, Magenta100, Yellow100,
                Key100);
        }

        public Color ToColor()
        {
            return ColorHelper.CMYKToColor(this);
        }

        public override int GetHashCode()
        {
            return base.GetHashCode();
        }

        public override bool Equals(object obj)
        {
            return base.Equals(obj);
        }
    }
}