﻿using MetroFramework.Forms;
using OCRTools.Language;
using System;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmLogin : MetroForm
    {
        public FrmLogin()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            CommonMethod.SetStyle(lnkPwd, ControlStyles.Selectable, false);
            CommonMethod.SetStyle(lnkReg, ControlStyles.Selectable, false);
            txtAccount.Text = CommonSetting.用户名;
            txtPwd.Text = CommonSetting.密码;
            txtAccount.KeyDown += TxtPwd_KeyDown;
            txtPwd.KeyDown += TxtPwd_KeyDown;
            //AnimationManager.EnableAnimation(txtAccount);
            //AnimationManager.EnableAnimation(txtPwd);
            this.AddContactUserBtn();
        }

        private void TxtPwd_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter && !string.IsNullOrEmpty(txtAccount.Text.Trim()) && !string.IsNullOrEmpty(txtPwd.Text))
                btnLogin_Click(sender, null);
        }

        private void lnkReg_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            var reg = new FrmReg { Icon = Icon };
            var result = reg.ShowDialog(this);
            if (result == DialogResult.OK)
            {
                CommonMethod.ShowHelpMsg("注册成功！使用过程中，如有任何问题，请联系客服！祝您使用愉快！".CurrentText(), 5000);
                txtAccount.Text = reg.Account;
                txtPwd.Text = reg.Pwd;
                btnLogin_Click(sender, null);
            }
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            var account = txtAccount.Text.Trim();
            var isEmail = account.IsEmail();
            var isMobile = account.IsMobile();
            if (!isEmail && !isMobile)
            {
                MessageBox.Show(this, "账号格式不正确，必须为手机号或者邮箱！".CurrentText(), CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtAccount.SelectAll();
                txtAccount.Focus();
                return;
            }

            var pwd = txtPwd.Text.Trim();
            if (string.IsNullOrEmpty(pwd))
            {
                MessageBox.Show(this, "密码不能为空".CurrentText() + "！", CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtPwd.Focus();
                return;
            }

            //使用regex进行格式设置 至少有数字、大小写字母，最少8个字符、最长30个字符
            if (!new Regex(@"[0-9A-Za-z].{5,15}").IsMatch(pwd)) //判断密码格式是否符合要求
            {
                MessageBox.Show(this, "密码必须为6-15位的数字或大小写字母！".CurrentText(), CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtPwd.Focus();
                return;
            }

            var strMsg = "";
            var result = OcrHelper.DoLogin(account, pwd, ref strMsg);
            if (result)
            {
                CommonSetting.SetValue("用户名", account);
                CommonSetting.SetValue("密码", pwd);
                CommonString.IsAutoLogin = true;
                DialogResult = DialogResult.OK;
                Close();
            }
            else
            {
                if (string.IsNullOrEmpty(strMsg)) strMsg = "登录失败".CurrentText() + "，" + CommonString.StrRetry.CurrentText();
                MessageBox.Show(this, strMsg, CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void lnkPwd_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            var reg = new FrmForgetPwd { Icon = Icon };
            if (!string.IsNullOrEmpty(txtAccount.Text.Trim())) reg.Account = txtAccount.Text.Trim();
            var result = reg.ShowDialog(this);
            if (result == DialogResult.OK)
            {
                txtAccount.Text = reg.Account;
                txtPwd.Text = reg.Pwd;
                btnLogin_Click(sender, null);
            }
        }
    }
}