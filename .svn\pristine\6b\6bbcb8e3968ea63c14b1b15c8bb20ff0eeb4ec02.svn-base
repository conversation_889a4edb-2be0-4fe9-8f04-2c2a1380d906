﻿using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace OCRTools
{
    public enum ClipboardDataType
    {
        Image = 1,
        Html = 2,
        Rtf = 3
    }

    public class ClipboardTextEntity
    {
        public ClipboardDataType Type { get; set; }

        public string Content { get; set; }

        public string OriginalText { get; set; }

        public Image Image { get; set; }

        public Point Location { get; set; } = Point.Empty;

        public Size ForceSize { get; set; }
    }

    public static class ClipboardService
    {
        private const int RETRY_TIMES = 5;

        private const int RETRY_DELAY = 200;

        private const string CONST_HTML =
            "<html>{0}\r\n<body style=\"background-color:rgb({3},{4},{5})\"><pre style=\"font-family:{1},宋体,Arial,Helvetica;font-size:{2}px;word-wrap:break-word;\">{6}</pre></body>\r\n</html>";

        private const string CONST_HEADER =
            "<head><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge,chrome=1\"></head>";

        public static void ClipSetImage(Image image, bool isfile)
        {
            if (image == null) throw new ArgumentNullException(nameof(image));
            if (isfile)
            {
                SetImageWithFile(image);
                return;
            }

            try
            {
                var dataObject = new DataObject();
                dataObject.SetData(DataFormats.Bitmap, image);
                SetData(dataObject);
            }
            catch { }
        }

        public static bool CopyImageFromFile(string path)
        {
            if (!string.IsNullOrEmpty(path) && File.Exists(path))
                try
                {
                    using (var bmp = ImageProcessHelper.LoadImage(path))
                    {
                        ClipSetImage(bmp, false);
                    }
                }
                catch (Exception)
                {
                    Console.WriteLine(@"Clipboard copy image from file failed.");
                }

            return false;
        }

        public static void SetImageWithFile(Image image)
        {
            var tempPath = Path.GetTempPath();
            var fileExt = "";
            var imageFormat = image.RawFormat;
            if (imageFormat.Equals(ImageFormat.Jpeg))
            {
                fileExt = "jpg";
            }
            else if (imageFormat.Equals(ImageFormat.Png))
            {
                fileExt = "png";
            }
            else if (imageFormat.Equals(ImageFormat.Bmp))
            {
                fileExt = "bmp";
            }
            else if (imageFormat.Equals(ImageFormat.Gif))
            {
                fileExt = "gif";
            }
            else if (fileExt == "")
            {
                fileExt = "png";
                imageFormat = ImageFormat.Png;
            }

            var filePath = tempPath + StaticValue.CatchName + "_" + ServerTime.DateTime.ToString("yyyyMMddhhmmss") + "." +
                           fileExt;
            image.Save(filePath, imageFormat);
            var dataObject = new DataObject();
            dataObject.SetData(DataFormats.FileDrop, false, new[] { filePath });
            dataObject.SetData(DataFormats.Bitmap, true, image);
            SetData(dataObject, false);
        }

        public static Image GetImage()
        {
            return Clipboard.ContainsImage() ? Clipboard.GetImage() : null;
        }

        public static string GetOneFile()
        {
            var result = string.Empty;
            if (Clipboard.ContainsFileDropList())
                foreach (var item in Clipboard.GetFileDropList())
                    if (CommonString.LstCanProcessFilesExt.Any(p => item.EndsWith(p)))
                    {
                        result = item;
                        break;
                    }

            return result;
        }

        public static string GetText(bool isRtf = false, int retryTimes = 0)
        {
            string text = null;
            try
            {
                if (Clipboard.ContainsText())
                    text = isRtf ? Clipboard.GetText(TextDataFormat.Text) : Clipboard.GetText();
            }
            catch
            {
            }

            if (string.IsNullOrEmpty(text) && retryTimes > 0) return GetText(isRtf, retryTimes - 1);
            return text;
        }

        private const string StrColorHtml =
            "<html><head><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge,chrome=1\"></head><body><html xmlns:x=\"tencent\"><meta charset=\"utf-8\"><body><table style=\"border-collapse:collapse\" selectType=\"cells\"><col width=\"55\" style=\"width: 55px;\"><col width=\"186\" style=\"width: 186px;\"><tr height=\"24\" style=\"mso-height-source:auto;height:24px;\"><td colspan=\"2\" rowspan=\"1\" style=\"background-color:rgb(#RGB#);;font-size:10pt;color:#000000;border-left: .5pt solid #000000;border-top: .5pt solid #000000;border-right: .5pt solid #000000;border-bottom: .5pt solid #000000;text-align:left;mso-number-format: 'General';\" ></td></tr>#Rows#</table></body></html></body></html>";
        private const string StrColorTRHtml =
            "<tr height=\"24\" style=\"mso-height-source:auto;height:24px;\"><td style=\"font-size:10pt;font-weight:bold;font-family:Microsoft YaHei;color:#000000;border-left: .5pt solid #000000;border-top: .5pt solid #000000;border-right: .5pt solid #000000;border-bottom: .5pt solid #000000;text-align:left;mso-number-format: 'General';\" ><font style=\"font-size:10pt;font-weight:bold;font-family:Microsoft YaHei;color:#000000;\">{0}</font></td><td style=\"font-size:10pt;border-left: .5pt solid #000000;border-top: .5pt solid #000000;border-right: .5pt solid #000000;border-bottom: .5pt solid #000000;\" ><font style=\"font-size:10pt;font-weight:bold;font-family:Microsoft YaHei;color:#000000;\">{1}</font></td></tr>";
        public static ClipboardTextEntity GetColorHtmlImage(Color color)
        {
            var mColor = new MyColor(color);
            var rgbStr = color.ToRgb();
            var hexStr = color.ToHex();
            var cmykStr = $"{mColor.CMYK.Cyan100:0.0}%,{mColor.CMYK.Magenta100:0.0}%,{mColor.CMYK.Yellow100:0.0}%,{mColor.CMYK.Key100:0.0}%";
            var hsbStr = $"{mColor.HSB.Hue360:0.0}°,{mColor.HSB.Saturation100:0.0}%,{mColor.HSB.Brightness100:0.0}%";
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat(StrColorTRHtml, "RGB", "#" + rgbStr);
            sb.AppendFormat(StrColorTRHtml, "HEX", hexStr);
            sb.AppendFormat(StrColorTRHtml, "CMYK", cmykStr);
            sb.AppendFormat(StrColorTRHtml, "HSB", hsbStr);
            var result = new ClipboardTextEntity
            {
                Type = ClipboardDataType.Html,
                OriginalText = $"RGB:#{rgbStr}\nHEX:{hexStr}\nCMYK:{cmykStr}\nHSB:{hsbStr}",
                Content = StrColorHtml.Replace("#RGB#", rgbStr).Replace("#Rows#", sb.ToString())
            };

            return result;
        }

        public static ClipboardTextEntity GetRtfHtmlImage()
        {
            var result = new ClipboardTextEntity
            {
                Type = ClipboardDataType.Html,
                OriginalText = Clipboard.GetText(TextDataFormat.UnicodeText)
            };
            var strText = string.Empty;
            if (!CommonSetting.忽略Html格式以文字方式生成图片 && Clipboard.ContainsText(TextDataFormat.Rtf))
                strText = Clipboard.GetText(TextDataFormat.Rtf);
            if (string.IsNullOrEmpty(strText))
            {
                if (!CommonSetting.忽略Html格式以文字方式生成图片 && Clipboard.ContainsText(TextDataFormat.Html))
                {
                    strText = GetObjectRichContent("Html Format");
                    if (strText.Contains("<HTML>"))
                        result.Content = "<HTML>" + CONST_HEADER + CommonMethod.SubString(strText, "<HTML>");
                    else
                        result.Content = "<html>" + CONST_HEADER + CommonMethod.SubString(strText, "<html>");
                }
                else
                {
                    strText = result.OriginalText;
                    if (!string.IsNullOrEmpty(strText))
                    {
                        var baseFont = CommonSetting.贴图文字字体;
                        var baseColor = CommonSetting.贴图背景颜色;
                        result.Content = string.Format(CONST_HTML, CONST_HEADER, baseFont.FontFamily.Name,
                            baseFont.Size, baseColor.R, baseColor.G, baseColor.B, strText);
                    }
                }
            }
            else
            {
                result.Type = ClipboardDataType.Rtf;
                result.Content = strText;
            }

            return result;
        }

        private static string GetObjectRichContent(string format)
        {
            if (Clipboard.GetData(format) is MemoryStream memory)
                using (memory)
                {
                    memory.Position = 0;
                    var vBytes = new byte[memory.Length];
                    memory.Read(vBytes, 0, (int)memory.Length);
                    return Encoding.UTF8.GetString(vBytes);
                }

            return null;
        }

        public static void SetText(string text)
        {
            if (!string.IsNullOrEmpty(text))
            {
                var dataObject = new DataObject();
                dataObject.SetData(DataFormats.Text, text);
                SetData(dataObject);
            }
        }

        private static void SetData(DataObject data, bool isCopy = true)
        {
            CommonMethod.DetermineCall(FrmMain.FrmTool, () =>
            {
                Clipboard.SetDataObject(data, isCopy, RETRY_TIMES, RETRY_DELAY);
            });
        }

        public static Dictionary<string, object> Backup()
        {
            var contents = new Dictionary<string, object>();
            try
            {
                var o = Clipboard.GetDataObject();
                if (o != null)
                    foreach (var format in o.GetFormats())
                        contents.Add(format, o.GetData(format));
            }
            catch
            {
            }

            return contents;
        }

        public static void Restore(Dictionary<string, object> contents)
        {
            try
            {
                Clipboard.Clear();
                if (contents != null && contents.Keys.Count > 0)
                {
                    var o = new DataObject();
                    foreach (var format in contents.Keys) o.SetData(format, contents[format]);
                    Clipboard.SetDataObject(o);
                }
            }
            catch
            {
            }
        }

        public static bool ContainsText()
        {
            try
            {
                return Clipboard.ContainsText();
            }
            catch (Exception)
            {
            }

            return false;
        }
    }
}