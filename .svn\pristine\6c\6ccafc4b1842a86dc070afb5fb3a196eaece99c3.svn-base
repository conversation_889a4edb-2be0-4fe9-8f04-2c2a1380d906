﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Xml.Serialization;

namespace OCRTools
{
    [Serializable]
    public class RulerMarkerCollection
    {
        private int _limit = int.MaxValue;

        [XmlIgnore]
        public int Limit
        {
            get => _limit;
            set
            {
                _limit = value;
                // remove additional markers
                while (Markers.Count > _limit)
                    RemoveFirstMarker();
            }
        }

        [XmlIgnore] public LinkedList<RulerMarker> Markers { get; private set; } = new LinkedList<RulerMarker>();

        // String property used for serialization
        [XmlElement("Markers")]
        public string MarkersString
        {
            get => string.Join(",", Markers);
            set => Markers = new LinkedList<RulerMarker>(
                value.Split(',').Where(s => !string.IsNullOrWhiteSpace(s))
                    .Select(RulerMarker.FromString)
            );
        }

        public IEnumerable<RulerMarker> Horizontal => Markers.Where(m => !m.Vertical);

        public IEnumerable<RulerMarker> Vertical => Markers.Where(m => m.Vertical);

        /// <summary>
        ///     This event is raised when a marker was added to the collection or if a marker was removed.
        /// </summary>
        public event EventHandler<MarkerCollectionEventArgs> MarkerCollectionChanged;

        /// <summary>
        ///     Adds a marker to the collection based on its location on the ruler form.
        /// </summary>
        /// <param name="pos">The location of the marker.</param>
        public void AddMarker(Point pos, int limit, bool verticalOnly = false)
        {
            // Add the marker as horizontal or vertical marker based on position
            var vertical = verticalOnly || pos.Y >= limit;
            RulerMarker newMarker;
            if (vertical)
                newMarker = new RulerMarker(pos.Y, true);
            else newMarker = new RulerMarker(pos.X, false);
            Markers.AddLast(newMarker);
            // remove first if we hit limit
            if (Markers.Count > Limit)
                RemoveFirstMarker();
            MarkerCollectionChanged?.Invoke(this, new MarkerCollectionEventArgs(true, newMarker));
        }

        /// <summary>
        ///     Returns the closest marker within a specified window if available.
        /// </summary>
        /// <param name="pos">The search location.</param>
        /// <param name="diff">The search window size.</param>
        /// <returns>The found marker if available, a default marker otherwise.</returns>
        public RulerMarker GetMarker(Point pos, int limit, int diff = 2)
        {
            var marker = RulerMarker.Default;
            // first search horizontal markers, then vertical markers
            if (pos.Y < limit) marker = Markers.FirstOrDefault(v => Math.Abs(pos.X - v.Value) <= diff);
            if (marker == RulerMarker.Default)
                return Markers.FirstOrDefault(v => Math.Abs(pos.Y - v.Value) <= diff);
            return marker;
        }

        public void RemoveMarker(RulerMarker marker)
        {
            Markers.Remove(marker);
            MarkerCollectionChanged?.Invoke(this, new MarkerCollectionEventArgs(false, marker));
        }

        public void RemoveFirstMarker()
        {
            var first = Markers.First.Value;
            Markers.RemoveFirst();
            MarkerCollectionChanged?.Invoke(this, new MarkerCollectionEventArgs(false, first));
        }

        public void Clear()
        {
            var removedItems = Markers.ToArray();
            Markers.Clear();
            MarkerCollectionChanged?.Invoke(this, new MarkerCollectionEventArgs(false, removedItems));
        }
    }

    public class MarkerCollectionEventArgs : EventArgs
    {
        public MarkerCollectionEventArgs(bool isAdded, params RulerMarker[] changedMarkers)
        {
            if (isAdded) AddedMarkers = changedMarkers;
            else RemovedMarkers = changedMarkers;
        }

        public RulerMarker[] AddedMarkers { get; }
        public RulerMarker[] RemovedMarkers { get; }
    }
}