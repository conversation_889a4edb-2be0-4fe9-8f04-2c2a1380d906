﻿using OCRTools.Common;
using System;
using System.Drawing;
using System.Windows.Forms;
using MetroFramework.Forms;

namespace OCRTools
{
    public partial class UcPicView : UserControl
    {
        public UcPicView()
        {
            InitializeComponent();
            imageBox.ZoomChanged += ImageBox_ZoomChanged;
        }

        public Image OriginImage => imageBox.Image;

        public int Zoom
        {
            get => imageBox.Zoom;
            set => imageBox.Zoom = value;
        }

        //只保留查看图片相关按钮
        public void HideButton()
        {
            tsmFullScreen.Visible = true;
            //tsbReOcr.Visible = false;
            tsmPicView.Visible = false;
        }

        public bool ShowOrHideToolBox()
        {
            toolPicOperate.Visible = !toolPicOperate.Visible;
            return toolPicOperate.Visible;
        }

        public void SetPicImage(Image img, string imgUrl = null, bool isAutoGetLine = false)
        {
            try
            {
                // 确保传入的图像安全设置到imageBox
                // 在 ImageBox 类的 OriginImage setter 中已经处理了图像的复制和资源释放
                // 这里直接设置，不需要额外复制
                imageBox.OriginImage = img;
                
                // 设置图像标签（URL信息）
                if (imgUrl != null && imageBox.Image != null)
                    imageBox.Tag = imgUrl;
                
                // 调整图像大小以适合控件
                imageBox.ZoomToFit();
                
                // 刷新控件布局
                UCPicView_SizeChanged(null, null);
                
                // 限制最大缩放比例为100%
                if (imageBox.Zoom > 100) 
                    imageBox.Zoom = 100;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"UCPicView.SetPicImage error: {ex.Message}");
            }
        }

        public void SetPicImageUrl(string imgUrl)
        {
            imageBox.Image.Tag = imgUrl;
        }

        public void Ocr()
        {
            imageBox.Image?.Ocr();
        }

        private void tsbReOcr_Click(object sender, EventArgs e)
        {
            Ocr();
        }

        private void ImageBox_ZoomChanged(object sender, EventArgs e)
        {
            txtPicZoomPercent.Text = string.Format("{0}%", imageBox.Zoom);
        }

        private void tsmPicView_Click(object sender, EventArgs e)
        {
            this.ViewImage(imageBox.Image);
        }

        private void tsmPicOrigin_Click(object sender, EventArgs e)
        {
            imageBox.Zoom = 100;
        }

        private void tsmPicSmall_Click(object sender, EventArgs e)
        {
            imageBox.Zoom -= imageBox.ZoomIncrement;
        }

        private void tsmPicBig_Click(object sender, EventArgs e)
        {
            imageBox.Zoom += imageBox.ZoomIncrement;
        }

        private void tsbSaveImg_Click(object sender, EventArgs e)
        {
            if (imageBox.Image != null) SaveFile(imageBox.Image);
        }

        public void SaveFile(Image bmp = null)
        {
            if (bmp == null) bmp = imageBox.Image;
            bmp.SaveFile(this);
        }

        private void tsmPicType_DropDownItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            var item = e.ClickedItem;
            if (item == null || item.Tag == null) return;
            if (imageBox.OriginImage != null)
            {
                tsmPicType.Text = item.Text;
                var imgType = (ImageProcessType)BoxUtil.GetInt32FromObject(item.Tag?.ToString());
                if (imgType == ImageProcessType.原始图片)
                    imageBox.Image = imageBox.OriginImage;
                else
                    try
                    {
                        imageBox.Image = ImageProcessHelper.ProcessImage(new Bitmap(imageBox.OriginImage), imgType);
                        imageBox.Tag = null;
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
            }
        }

        private void UCPicView_SizeChanged(object sender, EventArgs e)
        {
            toolPicOperate.Location = new Point((int)((ClientRectangle.Width - toolPicOperate.Width) * 1.0 / 2)
                , ClientRectangle.Height - toolPicOperate.Height - (imageBox.HorizontalScroll.Visible ? SystemInformation.HorizontalScrollBarHeight : 0) - 18);
            if (toolPicOperate.Visible)
            {
                toolPicOperate.BringToFront();
            }
        }

        private void tsmFullScreen_Click(object sender, EventArgs e)
        {
            var form = FindForm() as MetroForm;
            if (form == null) return;
            form.DisplayHeader = form.WindowState != FormWindowState.Normal;
            form.WindowState = form.WindowState == FormWindowState.Normal
                ? FormWindowState.Maximized
                : FormWindowState.Normal;
            //imageBox.ZoomToFit();
        }

        const int WM_PARENTNOTIFY = 0x0210;
        protected override void WndProc(ref Message m)
        {
            if (m.Msg == WM_PARENTNOTIFY)
            {
                var frm = FindForm();
                if (frm != null)
                {
                    if (!frm.Focused)
                        frm.Activate();
                }
            }
            base.WndProc(ref m);
        }

        private void tsmRotate_Click(object sender, EventArgs e)
        {
            if (imageBox.Image == null)
            {
                return;
            }
            imageBox.Image.RotateFlip(RotateFlipType.Rotate90FlipNone);
            imageBox.ZoomToFit();
            if (imageBox.Zoom > 100) imageBox.Zoom = 100;
        }
    }
}