using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using Emgu.CV;
using Emgu.CV.CvEnum;
using Emgu.CV.Structure;
using Microsoft.ML.OnnxRuntime.Tensors;

namespace OcrLib
{
	internal class OcrUtils
	{
		public static Tensor<float> SubstractMeanNormalize(Mat src, float[] meanVals, float[] normVals)
		{
			int cols = src.Cols;
			int rows = src.Rows;
			int numberOfChannels = src.NumberOfChannels;
			Image<Rgb, byte> image = src.ToImage<Rgb, byte>();
			byte[,,] data = image.Data;
			Tensor<float> tensor = new DenseTensor<float>(new int[4] { 1, numberOfChannels, rows, cols });
			for (int i = 0; i < rows; i++)
			{
				for (int j = 0; j < cols; j++)
				{
					for (int k = 0; k < numberOfChannels; k++)
					{
						byte b = data[i, j, k];
						float value = (float)(int)b * normVals[k] - meanVals[k] * normVals[k];
						tensor[new int[4] { 0, k, i, j }] = value;
					}
				}
			}
			return tensor;
		}

		public static Mat MakePadding(Mat src, int padding)
		{
			if (padding <= 0)
			{
				return src;
			}
			MCvScalar value = new MCvScalar(255.0, 255.0, 255.0);
			Mat mat = new Mat();
			CvInvoke.CopyMakeBorder(src, mat, padding, padding, padding, padding, BorderType.Isolated, value);
			return mat;
		}

		public static int GetThickness(Mat boxImg)
		{
			int num = ((boxImg.Cols > boxImg.Rows) ? boxImg.Rows : boxImg.Cols);
			return num / 1000 + 2;
		}

		public static void DrawTextBox(Mat boxImg, List<Point> box, int thickness)
		{
			if (box != null && box.Count != 0)
			{
				MCvScalar color = new MCvScalar(0.0, 0.0, 255.0);
				CvInvoke.Line(boxImg, box[0], box[1], color, thickness);
				CvInvoke.Line(boxImg, box[1], box[2], color, thickness);
				CvInvoke.Line(boxImg, box[2], box[3], color, thickness);
				CvInvoke.Line(boxImg, box[3], box[0], color, thickness);
			}
		}

		public static void DrawTextBoxes(Mat src, List<TextBox> textBoxes, int thickness)
		{
			for (int i = 0; i < textBoxes.Count; i++)
			{
				TextBox textBox = textBoxes[i];
				DrawTextBox(src, textBox.Points, thickness);
			}
		}

		public static List<Mat> GetPartImages(Mat src, List<TextBox> textBoxes)
		{
			Mat[] partImages = new Mat[textBoxes.Count];
			Parallel.For(0, textBoxes.Count, new ParallelOptions
			{
				MaxDegreeOfParallelism = -1
			}, delegate(int index)
			{
				Mat rotateCropImage = GetRotateCropImage(src, textBoxes[index].Points);
				partImages[index] = rotateCropImage;
			});
			return partImages.ToList();
		}

		public static Mat GetRotateCropImage(Mat src, List<Point> box)
		{
			List<Point> list = new List<Point>();
			list.AddRange(box);
			int[] source = new int[4]
			{
				box[0].X,
				box[1].X,
				box[2].X,
				box[3].X
			};
			int[] source2 = new int[4]
			{
				box[0].Y,
				box[1].Y,
				box[2].Y,
				box[3].Y
			};
			int num = source.Min();
			int num2 = source.Max();
			int num3 = source2.Min();
			int num4 = source2.Max();
			Rectangle roi = new Rectangle(num, num3, num2 - num, num4 - num3);
			Mat src2 = new Mat(src, roi);
			for (int i = 0; i < list.Count; i++)
			{
				Point value = list[i];
				value.X -= num;
				value.Y -= num3;
				list[i] = value;
			}
			int num5 = (int)Math.Sqrt(Math.Pow(list[0].X - list[1].X, 2.0) + Math.Pow(list[0].Y - list[1].Y, 2.0));
			int num6 = (int)Math.Sqrt(Math.Pow(list[0].X - list[3].X, 2.0) + Math.Pow(list[0].Y - list[3].Y, 2.0));
			PointF pointF = new PointF(0f, 0f);
			PointF pointF2 = new PointF(num5, 0f);
			PointF pointF3 = new PointF(num5, num6);
			PointF pointF4 = new PointF(0f, num6);
			PointF[] dest = new PointF[4] { pointF, pointF2, pointF3, pointF4 };
			PointF pointF5 = new PointF(list[0].X, list[0].Y);
			PointF pointF6 = new PointF(list[1].X, list[1].Y);
			PointF pointF7 = new PointF(list[2].X, list[2].Y);
			PointF pointF8 = new PointF(list[3].X, list[3].Y);
			PointF[] src3 = new PointF[4] { pointF5, pointF6, pointF7, pointF8 };
			Mat perspectiveTransform = CvInvoke.GetPerspectiveTransform(src3, dest);
			Mat mat = new Mat();
			CvInvoke.WarpPerspective(src2, mat, perspectiveTransform, new Size(num5, num6), Inter.Nearest, Warp.Default, BorderType.Replicate);
			if ((double)mat.Rows >= (double)mat.Cols * 1.5)
			{
				Mat mat2 = new Mat();
				CvInvoke.Transpose(mat, mat2);
				CvInvoke.Flip(mat2, mat2, 0);
				return mat2;
			}
			return mat;
		}

		public static Mat MatRotateClockWise180(Mat src)
		{
			CvInvoke.Flip(src, src, FlipType.Vertical);
			CvInvoke.Flip(src, src, FlipType.Horizontal);
			return src;
		}

		public static Mat MatRotateClockWise90(Mat src)
		{
			CvInvoke.Rotate(src, src, RotateFlags.Rotate90CounterClockwise);
			return src;
		}
	}
}
