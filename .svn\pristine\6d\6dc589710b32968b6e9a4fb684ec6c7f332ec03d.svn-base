using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class DIMENSIONS : Record
	{
		public int FirstRow;

		public int LastRow;

		public short FirstColumn;

		public short LastColumn;

		public short UnUsed;

		public DIMENSIONS(Record record)
			: base(record)
		{
		}

		public DIMENSIONS()
		{
			Type = 512;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			FirstRow = binaryReader.ReadInt32();
			LastRow = binaryReader.ReadInt32();
			FirstColumn = binaryReader.ReadInt16();
			LastColumn = binaryReader.ReadInt16();
			UnUsed = binaryReader.ReadInt16();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(FirstRow);
			binaryWriter.Write(LastRow);
			binaryWriter.Write(FirstColumn);
			binaryWriter.Write(LastColumn);
			binaryWriter.Write(UnUsed);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
