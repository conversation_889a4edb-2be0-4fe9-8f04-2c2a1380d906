﻿using MetroFramework;
using MetroFramework.Forms;
using OCRTools.Colors;
using OCRTools.Common;
using OCRTools.Properties;
using OCRTools.Units;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FormSetting : MetroForm
    {
        public readonly Regex intRegex = new Regex("^(-?[0-9]*[.]*[0-9]{0,3})$");

        private UserTypeInfo _nextUserType;

        public FormSetting()
        {
            InitializeComponent();
            lnkWebSite.Text = CommonString.StrServerHostUrl;
            CheckForIllegalCrossThreadCalls = false;

            CommonMethod.SetStyle(linkLabel2, ControlStyles.Selectable, false);
            CommonMethod.SetStyle(linkLabel1, ControlStyles.Selectable, false);
            CommonMethod.SetStyle(linkLabel3, ControlStyles.Selectable, false);
            CommonMethod.SetStyle(linkLabel4, ControlStyles.Selectable, false);
            CommonMethod.SetStyle(lnkWebSite, ControlStyles.Selectable, false);

            CommonMethod.SetStyle(btnUpgrade, ControlStyles.Selectable, false);

            tipMsg.AutoPopDelay = 5000;
            //设置鼠标停在该控件后，再停多长时间显示说眀性文字
            tipMsg.InitialDelay = 100;
            //设置鼠标从一个控件移到叧一个啌件再次显示该说明性文牸哋时间间隔
            tipMsg.ReshowDelay = 200;
            //蔎置是否显示窗体的说明性文字
            tipMsg.ShowAlways = true;
            tipMsg.ToolTipTitle = "功能描述";
            tipMsg.ToolTipIcon = ToolTipIcon.Info;

            ShowInTaskbar = true;
            MaximizeBox = false;
            MinimizeBox = false;
            ShadowType = CommonString.CommonShadowType;

            InitConfig();
        }

        public string OpenTab { get; set; }

        public string SharkGroup { get; set; }

        private void FormSetting_FormClosing(object sender, FormClosingEventArgs e)
        {
            SaveConfig();
        }

        private void FormSetting_Load(object sender, EventArgs e)
        {
            InitLeftCount();

            #region 其他配置

            ResetToolImg();

            picIcon.Image = Resources.ico;
            lblName.Text = CommonString.FullName;
            lblVersion.Text = string.Format("版本 {0}（{1}）", Application.ProductVersion,
                CommonString.DtNowDate.ToString("yyyy-MM-dd HH:mm:ss").Replace(" 00:00:00", ""));
            var asmcpr = (AssemblyCopyrightAttribute)Attribute.GetCustomAttribute(Assembly.GetExecutingAssembly(),
                typeof(AssemblyCopyrightAttribute));
            lblCopyright.Text = string.Format("{0}", asmcpr?.Copyright ?? Application.CompanyName);

            #endregion

            tbConfig_SelectedIndexChanged(sender, e);
            BingViewText();

            if (!string.IsNullOrEmpty(OpenTab))
            {
                var selectedTab = tbConfig.TabPages.OfType<TabPage>().FirstOrDefault(p => Equals(OpenTab, p.Text));
                if (selectedTab != null) tbConfig.SelectedTab = selectedTab;
            }

            if (!string.IsNullOrEmpty(SharkGroup))
            {
                var selectedGroupBox = tbConfig.SelectedTab.Controls.OfType<GroupBox>().FirstOrDefault(p => Equals(SharkGroup, p.Text));
                if (selectedGroupBox == null)
                {
                    foreach (var groupBox in tbConfig.SelectedTab.Controls.OfType<GroupBox>())
                    {
                        selectedGroupBox = groupBox.Controls.OfType<GroupBox>().FirstOrDefault(p => Equals(SharkGroup, p.Text));
                        if (selectedGroupBox != null)
                        {
                            break;
                        }
                    }
                }
                if (selectedGroupBox != null)
                {
                    Task.Factory.StartNew(() =>
                    {
                        SharkGroupBox(selectedGroupBox);
                    });
                }
            }

            _nextUserType = CommonUser.GetNextType();
            btnUpgrade.Text = _nextUserType.Name;
            btnUpgrade.Image = CommonUser.GetUserLevelImage(_nextUserType.Code);

            if (btnUpgrade.Image == null)
                btnUpgrade.Image = Resources.qqKeFu;

            chkToolShadow.CheckedChanged += cmbToolBarWidth_SelectedIndexChanged;
            cmbToolBarSize.SelectedIndexChanged += cmbToolBarWidth_SelectedIndexChanged;
            chkToolBarCircle.CheckedChanged += cmbToolBarWidth_SelectedIndexChanged;
            numToolShadowWidth.ValueChanged += cmbToolBarWidth_SelectedIndexChanged;
            chkWeatherImage.CheckedChanged += chkWeatherImage_CheckedChanged;
            cmbWeatherIcon.SelectedIndexChanged += cmbWeatherIcon_SelectedIndexChanged;
            ShowToolClick();
            //InitLocalOcr();
            InitLocalOcrControl();
        }

        private void SharkGroupBox(GroupBox box)
        {
            var baseColor = box.ForeColor;
            for (int i = 0; i < 10; i++)
            {
                var color = Color.FromArgb(0, new Random(Guid.NewGuid().GetHashCode()).Next(0, 256),
                    new Random(Guid.NewGuid().GetHashCode()).Next(0, 256),
                    new Random(Guid.NewGuid().GetHashCode()).Next(0, 256));
                box.ForeColor = color;
                Thread.Sleep(300);
            }
            box.ForeColor = baseColor;
        }

        private void InitConfig()
        {
            #region 初始化下拉框

            btnContentFontColor.ImageColor = StaticValue.DefaultForeColor;
            btnContentBackColor.ImageColor = StaticValue.DefaultBackColor;
            btnTieTuFontColor.ImageColor = StaticValue.DefaultForeColor;
            btnTieTuBackColor.ImageColor = StaticValue.DefaultBackColor;
            btnTieTuShadowColor.ImageColor = StaticValue.ShadowActiveColor;
            txtCaptureLocation.Text = CommonString.DefaultImagePath;

            InitEnums();
            InitShortKeys();

            #endregion

            foreach (TabPage item in tbConfig.TabPages)
            {
                var tabName = item.Text;
                ProcessItemByControl(item, tabName);
            }
        }

        #region 工具栏

        private void chkShowTool_CheckedChanged(object sender, EventArgs e)
        {
            if (!chkShowTool.Checked && !chkShowNotify.Checked)
            {
                (sender as CheckBox).Checked = true;
                CommonMethod.ShowHelpMsg("工具栏和系统托盘不能同时隐藏，请至少选择一项！");
                return;
            }
            if (Equals(sender, chkShowTool))
            {
                FrmMain.FrmTool.Visible = chkShowTool.Checked;
                ShowToolClick();
            }
            else
            {
                (Owner as FrmMain)?.ShowNotifyMain((sender as CheckBox).Checked);
            }
        }

        private void ShowToolClick()
        {
            foreach (Control control in grpToolSet.Controls)
            {
                if (!Equals(control, chkShowTool))
                {
                    control.Enabled = chkShowTool.Checked;
                }
            }
        }

        #endregion

        private void tbConfig_SelectedIndexChanged(object sender, EventArgs e)
        {
            var maxHeight = 415;
            if (!Equals(tbConfig.SelectedTab, tbShortKey))
            {
                var maxDownContol = tbConfig.SelectedTab.Controls[0];
                foreach (Control item in tbConfig.SelectedTab.Controls)
                    if (item.Top + item.Height > maxDownContol?.Top + maxDownContol?.Height)
                        maxDownContol = item;
                maxHeight = Math.Max(200, tbConfig.Location.Y + maxDownContol.Top + maxDownContol.PreferredSize.Height + 50);
                if (maxDownContol is TabControl)
                {
                    maxHeight += 10;
                }
            }

            Height = maxHeight;
        }

        private void cmbToolBarWidth_SelectedIndexChanged(object sender, EventArgs e)
        {
            ResetToolImg();
        }

        private void ResetToolImg()
        {
            CommonSetting.工具栏图片 = txtToolBarPicLocation.Text;
            CommonSetting.工具栏图标尺寸 = cmbToolBarSize.SelectedItem?.ToString();
            CommonSetting.工具栏阴影宽度 = numToolShadowWidth.Value;
            CommonSetting.阴影效果 = chkToolShadow.Checked;
            CommonSetting.圆形图标 = chkToolBarCircle.Checked;
            CommonSetting.SetValue("工具栏图片", CommonSetting.工具栏图片);
            var size = CommonSetting.GetStrSize(cmbToolBarSize.SelectedItem?.ToString());
            var image = CommonSetting.Get图标效果(size, CommonSetting.工具栏图片, CommonSetting.圆形图标,
                CommonSetting.阴影效果, CommonSetting.工具栏阴影宽度);
            picToolBar.Image = image;
            FrmMain.FrmTool.RefreshImage(image);
            //picToolBar.Top = (120 - image.Height) / 2;
            //picToolBar.Size = image.Size;
            //var left = picToolBar.Left + picToolBar.Width + 5;
            //chkToolBarCircle.Left = left;
            //btnToolBarPicture.Left = left;
            //btnQQ.Left = left;
            //btnDefaultToolBarPicture.Left = left;
        }

        private void btnToolBarPicture_Click(object sender, EventArgs e)
        {
            var open = new OpenFileDialog
            {
                Title = "请选择图片文件",
                Filter = "图片文件|*.png;*.jpg;*.jpeg;*.bmp",
                RestoreDirectory = true,
                Multiselect = false
            };
            if (open.ShowDialog(this) == DialogResult.OK)
            {
                txtToolBarPicLocation.Text = open.FileName;
                try
                {
                    var fileName = CommonString.HeadImagePath + Guid.NewGuid().ToString().Replace("-", "") + ".png";
                    File.Copy(open.FileName, fileName, true);
                    txtToolBarPicLocation.Text = fileName;
                }
                catch
                {
                }

                cmbToolBarWidth_SelectedIndexChanged(sender, e);
            }
        }

        private void btnDefaultToolBarPicture_Click(object sender, EventArgs e)
        {
            cmbToolBarSize.SelectedIndex = 0;
            txtToolBarPicLocation.Text = "";
            chkToolBarCircle.Checked = true;
            cmbToolBarWidth_SelectedIndexChanged(sender, e);
            chkWeatherImage.Checked = false;
        }

        private void chkWeatherImage_CheckedChanged(object sender, EventArgs e)
        {
            if (chkWeatherImage.Checked)
            {
                var weatherIcon = CommonSetting.ConvertToEnum(cmbWeatherIcon.Text, WeatherIconType.QQ);
                var strImage = CommonWeather.GetWeather(weatherIcon, true);
                if (!string.IsNullOrEmpty(strImage)) ChangeImageByUrl(strImage, false);
                cmbToolBarWidth_SelectedIndexChanged(sender, e);
            }
            else
            {
                btnDefaultToolBarPicture_Click(sender, e);
            }
        }

        private void cmbWeatherIcon_SelectedIndexChanged(object sender, EventArgs e)
        {
            chkWeatherImage_CheckedChanged(sender, e);
        }

        private void btnQQ_Click(object sender, EventArgs e)
        {
            var frm = new MetroForm
            {
                Icon = Icon,
                Theme = Theme,
                Style = Style,
                StyleManager = StyleManager,
                Width = 300,
                Height = 150,
                ControlBox = false,
                Text = "输入QQ号"
            };
            frm.Controls.Add(new TextBox
            {
                Name = "txtQQ",
                Width = frm.Width - 10,
                TabIndex = 0,
                TabStop = true,
                Location = new Point(0, 55),
                Left = 5,
                Font = new Font(CommonSetting.默认文字字体.FontFamily, 21, GraphicsUnit.Pixel)
            });
            var btn = new Button
            {
                Name = "btnQQ",
                Text = "确认",
                Width = frm.Width,
                Height = 40,
                Tag = frm.Controls[0],
                Location = new Point(0, 100)
            };
            btn.Click += Btn_Click;
            frm.Controls.Add(btn);
            frm.AcceptButton = btn;
            frm.ShadowType = ShadowType;
            var dialogRes = frm.ShowDialog(this);
            if (dialogRes == DialogResult.OK)
                if (frm.Tag != null && !string.IsNullOrEmpty(frm.Tag.ToString()) &&
                    intRegex.IsMatch(frm.Tag.ToString()))
                    ChangeImageByUrl(
                        string.Format("https://q2.qlogo.cn/g?b=qq&nk={0}&s=100", frm.Tag.ToString().Trim()));

            cmbToolBarWidth_SelectedIndexChanged(sender, e);
        }

        private void ChangeImageByUrl(string url, bool isNeedDownload = true)
        {
            if (isNeedDownload) url = CommonSetting.SetHeadImageByUrl(url);
            if (!string.IsNullOrEmpty(url))
                txtToolBarPicLocation.Text = url;
        }

        private void Btn_Click(object sender, EventArgs e)
        {
            var tag = (sender as Button)?.Tag as TextBox;
            if (tag != null && !intRegex.IsMatch(tag.Text))
            {
                MessageBox.Show(tag.FindForm(), "格式不正确，必须为全数字！", CommonString.StrReminder);
                tag.SelectAll();
                tag.Focus();
                return;
            }

            if (tag != null)
            {
                var frm = tag.FindForm();
                if (frm != null)
                {
                    frm.Tag = tag.Text;
                    frm.DialogResult = DialogResult.OK;
                    frm.Close();
                }
            }
        }

        private void btnUpgrade_Click(object sender, EventArgs e)
        {
            using (var frmBuy = new FrmGoBuy
            {
                Icon = Icon,
                Theme = Theme,
                Style = Style,
                StyleManager = StyleManager,
                NextUserType = _nextUserType
            })
            {
                frmBuy.ShowDialog(this);
            }
        }

        private void btnSupport_Click(object sender, EventArgs e)
        {
            FrmMain.ShowReort();
            //var report = new FrmReport
            //{
            //    Icon = Icon,
            //    Theme = Theme,
            //    Style = Style,
            //    StyleManager = StyleManager,
            //    Text = (sender as LinkLabel)?.Text
            //};
            //report.ShowDialog(this);
        }

        private void linkLabel1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            CommonMethod.OpenKeFuQ();
        }

        private void linkLabel2_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            CommonMethod.OpenQun();
        }

        private void btnCheckUpdate_Click(object sender, EventArgs e)
        {
            CommonUpdate.isAlertUpdate = true;
            CommonUpdate.UpdateMethod(true, CommonString.DtNowDate, CommonString.UpdateFileUrl);
        }

        #region 初始化配置

        private void ProcessItemByControl(Control item, string section, bool isSave = false)
        {
            foreach (Control control in item.Controls)
                if (control is CheckBox checkBox)
                {
                    if (isSave)
                    {
                        if (control.Tag != null && control.Tag is ObjectTypeItem)
                        {
                            CommonSetting.SetValue(section, checkBox.Name, checkBox.Checked ? (checkBox.Tag as ObjectTypeItem).Code : 0);
                        }
                        else
                        {
                            CommonSetting.SetValue(section, checkBox.Text, checkBox.Checked);
                        }
                    }
                    else
                        checkBox.Checked = CommonSetting.GetValue(section, checkBox.Text, checkBox.Checked);
                }
                else
                {
                    if (control is GroupBox || control is Panel || control is TabControl)
                    {
                        ProcessItemByControl(control, section, isSave);
                    }
                    else
                    {
                        var tagName = control.Tag?.ToString();
                        if (string.IsNullOrEmpty(tagName)) continue;
                        if (control is TextBox)
                        {
                            var txtValue = control.Text;
                            if (control.Tag is HotKeyEntity obj)
                            {
                                tagName = obj.KeyName;
                                txtValue = isSave ? ((int)obj.Hotkey).ToString() : obj.ToString();
                            }
                            else
                                txtValue = isSave ? txtValue : CommonSetting.GetValue(section, tagName, txtValue);
                            if (isSave)
                            {
                                CommonSetting.SetValue(section, tagName, txtValue);
                            }
                            else
                            {
                                control.Text = "";
                                control.Text = txtValue;
                            }
                        }
                        else if (control is ComboBox comboBox)
                        {
                            if (isSave)
                            {
                                CommonSetting.SetValue(section, tagName, comboBox.SelectedItem?.ToString());
                            }
                            else
                            {
                                comboBox.SelectedItem =
                                    CommonSetting.GetValue(section, tagName, comboBox.SelectedItem?.ToString());
                                if (comboBox.SelectedItem == null)
                                {
                                    if (Equals(comboBox, cmbStyles))
                                        comboBox.SelectedItem = "绿色";
                                    else if (comboBox.Items.Count > 0) comboBox.SelectedIndex = 0;
                                }
                            }
                        }
                        else if (control is NumericUpDown numericUpDown)
                        {
                            if (isSave)
                                CommonSetting.SetValue(section, tagName, numericUpDown.Value);
                            else
                                numericUpDown.Value = CommonSetting.GetValue(section, tagName, numericUpDown.Value);
                        }
                        else if (control is ImageButton imageButton)
                        {
                            if (isSave)
                                CommonSetting.SetValue(section, tagName, imageButton.ImageColor);
                            else
                                imageButton.ImageColor =
                                    CommonSetting.GetValue(section, tagName, imageButton.ImageColor);
                        }
                        else if (control is Button button)
                        {
                            var isFont = button.Name.ToLower().Contains("font");
                            if (isSave)
                            {
                                if (isFont)
                                    CommonSetting.SetValue(section, tagName, GetFont(button));
                                //value = TypeDescriptor.GetConverter(typeof(Font)).ConvertToInvariantString(GetFont(ctrl));
                            }
                            else
                            {
                                if (isFont)
                                {
                                    var ff = CommonSetting.GetValue(section, tagName, GetFont(button));
                                    //var font = string.IsNullOrEmpty(value) ? StaticValue.DefaultFont : TypeDescriptor.GetConverter(typeof(Font)).ConvertFromInvariantString(value) as Font;
                                    SetFont(button, ff);
                                }
                            }
                        }
                    }
                }
        }

        public void SaveConfig()
        {
            foreach (TabPage item in tbConfig.TabPages)
            {
                var tabName = item.Text;
                ProcessItemByControl(item, tabName, true);
            }
        }

        private void InitEnums()
        {
            foreach (MetroColorStyle type in Enum.GetValues(typeof(MetroColorStyle)))
                cmbStyles.Items.Add(type.ToString());
            foreach (LoadingType type in Enum.GetValues(typeof(LoadingType))) cmbLoadingType.Items.Add(type.ToString());
            foreach (ToolDoubleClickEnum type in Enum.GetValues(typeof(ToolDoubleClickEnum)))
                cmbDoubleClick.Items.Add(type.ToString());
            foreach (ToolDoubleClickEnum type in Enum.GetValues(typeof(ToolDoubleClickEnum)))
            {
                if (!Equals(type, ToolDoubleClickEnum.不做任何操作))
                    cmsNotifyDoubleClick.Items.Add(type.ToString());
            }
            foreach (var engine in FrmMain.LstSearchEngine) cmbSearch.Items.Add(engine.Name);
            foreach (Enum item in Enum.GetValues(typeof(MeasuringUnit))) cmbRulerUnit.Items.Add(item.ToString());
            foreach (Enum item in Enum.GetValues(typeof(ThemeOption))) cmbRulerTheme.Items.Add(item.ToString());
            for (var i = 100; i >= 40; i -= 20) cmbRulerOpacity.Items.Add(i.ToString());
            foreach (Enum item in Enum.GetValues(typeof(SpiltMode))) cmbFenDuan.Items.Add(item.ToString());
            foreach (Enum item in Enum.GetValues(typeof(Speaker))) cmbVoice.Items.Add(item.ToString());
            foreach (Enum item in Enum.GetValues(typeof(CopyResultEnum)))
                cmbCopyMode.Items.Add(item.ToString());

            foreach (Enum item in Enum.GetValues(typeof(WeatherIconType))) cmbWeatherIcon.Items.Add(item.ToString());
            foreach (Enum item in Enum.GetValues(typeof(OcrModel))) cmbOcrType.Items.Add(item.ToString());
            foreach (Enum item in Enum.GetValues(typeof(ImageBoxGridDisplayMode))) cmbImageViewBackStyle.Items.Add(item.ToString());
        }

        /// 设置按键不响应
        private void txtCaptureKey_KeyPress(object sender, KeyPressEventArgs e)
        {
            e.Handled = true;
        }

        private void txtBox_GotFocus(object sender, EventArgs e)
        {
            var textBox = sender as TextBox;
            var entity = textBox?.Tag as HotKeyEntity;
            BindTextBox(textBox, entity);
        }

        private void txtBox_LostFocus(object sender, EventArgs e)
        {
            var textBox = sender as TextBox;
            var entity = textBox?.Tag as HotKeyEntity;
            BindTextBox(textBox, entity, true);
        }

        private void txtBox_KeyUp(object sender, KeyEventArgs e)
        {
            e.SuppressKeyPress = true;

            // PrintScreen not trigger KeyDown event
            if (e.KeyCode == Keys.PrintScreen)
            {
                var textBox = sender as TextBox;
                var entity = textBox?.Tag as HotKeyEntity;
                entity.Hotkey = e.KeyData;
                BindTextBox(textBox, entity);
            }
        }

        private void txtBox_KeyDown(object sender, KeyEventArgs e)
        {
            e.SuppressKeyPress = true;

            var textBox = sender as TextBox;
            var entity = textBox?.Tag as HotKeyEntity;


            if (e.KeyCode == Keys.Delete || e.KeyCode == Keys.Back || e.KeyCode == Keys.Escape)
            {
                entity.Hotkey = Keys.None;
                entity.Win = false;
            }
            else if (e.KeyCode == Keys.LWin || e.KeyCode == Keys.RWin)
            {
                entity.Win = !entity.Win;
            }
            else
            {
                entity.Hotkey = e.KeyData;
            }

            BindTextBox(textBox, entity);
        }

        private readonly Regex _textRegex = new Regex("[一-龥]+");

        private void BindTextBox(TextBox textBox, HotKeyEntity entity, bool isEnd = false)
        {
            string strTmp;

            var str = "";
            foreach (var obj in _textRegex.Matches(textBox.Name)) str = ((Match)obj).ToString();

            if (isEnd)
            {
                //验证快捷键是否有效
                if (entity.IsValidHotkey)
                {
                    var exitsHotKey =
                        FrmMain.LstHotKeys.FirstOrDefault(
                            p => !p.KeyName.Equals(str) && Equals(p.StrKey, entity.StrKey));
                    if (exitsHotKey == null)
                    {
                        FrmMain.LstHotKeys.Where(p => p.KeyName.Equals(str)).ToList()
                            .ForEach(p => p.Hotkey = entity.Hotkey);
                    }
                    else
                    {
                        CommonMethod.ShowHelpMsg(string.Format("快捷键：{0}，已被[{1}]占用！", entity.StrKey,
                            exitsHotKey.KeyName.Trim()));
                        entity.Hotkey = Keys.None;
                    }
                }
                else
                {
                    entity.Hotkey = Keys.None;
                }
            }

            var pictureBox = (PictureBox)Controls.Find("pictureBox_" + str, true)[0];
            if (Equals(entity.Hotkey, Keys.None))
            {
                pictureBox.Image = Resources.Info_Error;
                strTmp = isEnd ? entity.StrKey : CommonString.StrDefaultDesc;
            }
            else
            {
                pictureBox.Image = isEnd ? Resources.Info_OK : Resources.Info_Info;
                strTmp = entity.StrKey;
            }

            textBox.Text = strTmp;
            textBox.Tag = entity;

            if (isEnd)
                CommonSetting.SetValue("快捷键", str, ((int)entity.Hotkey).ToString());
            else
                textBox.SelectionStart = textBox.Text.Length;
        }

        private void txtBox_PreviewKeyDown(object sender, PreviewKeyDownEventArgs e)
        {
            // For handle Tab key etc.
            e.IsInputKey = true;
        }

        private void InitShortKeys()
        {
            var hotTypeList = FrmMain.LstHotKeys.GroupBy(p => p.Group).ToList();
            hotTypeList.ForEach(p =>
            {
                //p.Key
                var tbTmp = new TabPage
                {
                    BackColor = Color.White,
                    Padding = new Padding(0, 5, 0, 0),
                    Text = p.Key.ToString()
                };

                foreach (var item in p.ToList())
                {
                    var pnl = new FlowLayoutPanel
                    {
                        BackColor = Color.White,
                        Width = tbShortKey.Width - 5,
                        Height = 32,
                        Padding = new Padding(0, 5, 0, 0),
                        Dock = DockStyle.Top
                    };
                    var lbl = new Label
                    {
                        Name = "lbl_" + item.KeyName.Trim(),
                        Text = item.KeyName + ":",
                        Font = chkAutoBackConfig.Font,
                        AutoSize = true,
                        BackColor = Color.White,
                        ForeColor = Color.Black,
                        Padding = new Padding(30, 6, 0, 0)
                    };
                    pnl.Controls.Add(lbl);
                    tipMsg.SetToolTip(lbl, item.Desc);

                    var textBox = new TextBox
                    {
                        Name = "txtBox_" + item.KeyName.Trim(),
                        Font = chkAutoBackConfig.Font,
                        BackColor = Color.White,
                        ForeColor = Color.Black,
                        Size = new Size(185, 30),
                        Padding = new Padding(0, 0, 0, 0),
                        ShortcutsEnabled = false,
                        Tag = item,
                        Text = item.ToString()
                    };

                    textBox.KeyDown += txtBox_KeyDown;
                    textBox.KeyUp += txtBox_KeyUp;
                    textBox.KeyPress += txtCaptureKey_KeyPress;
                    textBox.LostFocus += txtBox_LostFocus;
                    textBox.GotFocus += txtBox_GotFocus;
                    textBox.PreviewKeyDown += txtBox_PreviewKeyDown;


                    //textBox.KeyDown += new System.Windows.Forms.KeyEventHandler(btnHotkey_KeyDown);
                    //textBox.KeyUp += new System.Windows.Forms.KeyEventHandler(btnHotkey_KeyUp);
                    //textBox.Leave += new System.EventHandler(btnHotkey_Leave);
                    //textBox.MouseClick += new System.Windows.Forms.MouseEventHandler(btnHotkey_MouseClick);
                    //textBox.PreviewKeyDown += new System.Windows.Forms.PreviewKeyDownEventHandler(btnHotkey_PreviewKeyDown);

                    tipMsg.SetToolTip(textBox, item.Desc);

                    pnl.Controls.Add(textBox);

                    var picture = new PictureBox
                    {
                        Name = "pictureBox_" + item.KeyName.Trim(),
                        BackColor = Color.White,
                        Size = new Size(25, 25),
                        SizeMode = PictureBoxSizeMode.CenterImage,
                        Padding = new Padding(10, 5, 0, 0),
                        Image = string.IsNullOrEmpty(textBox.Text) || textBox.Text.Equals(CommonString.StrDefaultDesc)
                            ? Resources.Info_Error
                            : Resources.Info_OK
                    };
                    pnl.Controls.Add(picture);

                    tbTmp.Controls.Add(pnl);
                }

                tabHotKeys.TabPages.Add(tbTmp);
            });
        }

        #endregion

        #region 常规

        private void chkAutoStart_CheckedChanged(object sender, EventArgs e)
        {
            CommonMethod.AutoStart(chkAutoStart.Checked);
        }

        private void btnOpenConfigLocation_Click(object sender, EventArgs e)
        {
            CommonMethod.OpenFolderWithFile(CommonSetting.IniFileName);
        }

        private void btnClearConfig_Click(object sender, EventArgs e)
        {
            try
            {
                File.Delete(CommonSetting.IniFileName);
                //CommonSetting.InitSetting();
                CommonMethod.ShowHelpMsg("清理配置文件成功！");
                return;
            }
            catch { }
            CommonMethod.ShowHelpMsg("清理配置文件失败，请稍后重试！");
        }

        #endregion

        #region 界面

        /// <summary>
        ///     获取一个颜色的人眼感知亮度，并以 0~1 之间的小数表示。
        ///     灰度值小于0.5，推荐亮色，反之，推荐黑色
        /// </summary>
        private double GetGrayLevel(Color color)
        {
            return (0.299 * color.R + 0.587 * color.G + 0.114 * color.B) / 255;
        }

        private void btnContentDefault_Click(object sender, EventArgs e)
        {
            var btn = sender as Button;
            if (Equals(btn, btnContentDefault))
            {
                btnContentBackColor.ImageColor = StaticValue.DefaultBackColor;
                btnContentFontColor.ImageColor = StaticValue.DefaultForeColor;
                SetFont(btnContentFont, StaticValue.DefaultFont);
                BingViewText();
            }
            else
            {
                if (chkUseContentColor.Checked)
                {
                    chkUseContentColor_CheckedChanged(sender, e);
                }
                else
                {
                    btnTieTuBackColor.ImageColor = StaticValue.DefaultBackColor;
                    btnTieTuFontColor.ImageColor = StaticValue.DefaultForeColor;
                    SetFont(btnTieTuFont, StaticValue.DefaultFont);
                    BingViewText(false);
                }
            }
        }

        private void BingViewText(bool isContent = true)
        {
            if (isContent)
            {
                lblContentLable.Font = GetFont(btnContentFont);
                lblContentLable.ForeColor = btnContentFontColor.ImageColor;
                lblContentLable.BackColor = btnContentBackColor.ImageColor;
                chkUseContentColor_CheckedChanged(null, null);
            }
            else
            {
                lblTieTuLabel.Font = GetFont(btnTieTuFont);
                lblTieTuLabel.ForeColor = btnTieTuFontColor.ImageColor;
                lblTieTuLabel.BackColor = btnTieTuBackColor.ImageColor;
            }
        }

        private void btnColor_Click(object sender, EventArgs e)
        {
            var btn = sender as ImageButton;
            var dialog = new ColorDialog
            {
                Color = btn.ImageColor,
                CustomColors = new[]
                {
                    ColorTranslator.ToOle(Color.FromArgb(240, 255, 240)),
                    ColorTranslator.ToOle(Color.FromArgb(255, 255, 224)),
                    ColorTranslator.ToOle(StaticValue.ShadowActiveColor),
                    ColorTranslator.ToOle(StaticValue.ShadowNormalColor)
                }
            };
            if (dialog.ShowDialog(this) == DialogResult.OK)
            {
                btn.ImageColor = dialog.Color;
                var isContent = btn.Name.StartsWith("btnContent");
                BingViewText(isContent);
                if (!btn.Name.Contains("BackColor") && !btn.Name.Contains("FontColor")) return;
                double backColorGray;
                double foreColorGray;
                if (isContent)
                {
                    backColorGray = GetGrayLevel(btnContentBackColor.ImageColor);
                    foreColorGray = GetGrayLevel(btnContentFontColor.ImageColor);
                }
                else
                {
                    backColorGray = GetGrayLevel(btnTieTuBackColor.ImageColor);
                    foreColorGray = GetGrayLevel(btnTieTuFontColor.ImageColor);
                }

                if (backColorGray >= 0.5 && foreColorGray >= 0.5 || backColorGray < 0.5 && foreColorGray < 0.5)
                    MessageBox.Show(this, "文字颜色与背景色区分度不明显，容易造成眼部疲劳，建议调整！", CommonString.StrReminder, MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
            }
        }

        private void chkUseContentColor_CheckedChanged(object sender, EventArgs e)
        {
            if (chkUseContentColor.Checked)
            {
                SetFont(btnTieTuFont, GetFont(btnContentFont));
                btnTieTuFontColor.ImageColor = btnContentFontColor.ImageColor;
                btnTieTuBackColor.ImageColor = btnContentBackColor.ImageColor;
            }

            BingViewText(false);
        }

        public readonly Dictionary<string, Font> DicTmpFont = new Dictionary<string, Font>();

        private Font GetFont(Button button)
        {
            var key = button?.Tag?.ToString();
            if (string.IsNullOrEmpty(key)) return StaticValue.DefaultFont;
            if (!DicTmpFont.ContainsKey(key)) DicTmpFont.Add(key, StaticValue.DefaultFont);
            return DicTmpFont[key];
        }

        private void SetFont(Button button, Font font)
        {
            var key = button.Tag?.ToString();
            if (string.IsNullOrEmpty(key))
                return;
            if (!DicTmpFont.ContainsKey(key))
                DicTmpFont.Add(key, font);
            else
                DicTmpFont[key] = font;
        }

        private void btnFont_Click(object sender, EventArgs e)
        {
            var btn = sender as Button;
            var dialog = new FontDialog
            { Font = GetFont(btn), ShowColor = false, ShowEffects = true, AllowScriptChange = false };
            if (dialog.ShowDialog(this) == DialogResult.OK)
            {
                SetFont(btn, dialog.Font);
                BingViewText(btn.Name.StartsWith("btnContent"));
            }
        }

        private void cmbLoadingType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbLoadingType.SelectedIndex < 0) return;
            var selectedText = cmbLoadingType.SelectedItem.ToString();
            if (string.IsNullOrEmpty(selectedText)) return;
            var loadingType = CommonSetting.ConvertToEnum(selectedText, LoadingType.蓝色箭头);
            picLoadingImage.Image = LoadingTypeHelper.GetImageByConfig(loadingType);
        }

        private void chkDarkModel_CheckedChanged(object sender, EventArgs e)
        {
            if (StyleManager != null)
            {
                StyleManager.Theme = chkDarkModel.Checked ? MetroThemeStyle.Dark : MetroThemeStyle.Light;
                Invalidate(true);
            }
        }

        private void cmbStyles_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (StyleManager != null)
            {
                var selectedText = cmbStyles.SelectedItem.ToString();
                if (string.IsNullOrEmpty(selectedText)) return;
                if (Enum.TryParse(selectedText, out MetroColorStyle colorStyle))
                {
                    StyleManager.Style = colorStyle;
                }
                Invalidate(true);
            }
        }

        #endregion

        #region 截屏

        private void txtCaptureFileName_TextChanged(object sender, EventArgs e)
        {
            /*
             %n 年份；%y 月份
            %r 天数；%s 小时
            %f 分钟；%m 秒钟
            %t 时间戳；%g 随机
             */
            var fileName = txtCaptureFileName.Text.Trim()
                .Replace("%n", ServerTime.DateTime.Year.ToString())
                .Replace("%y", ServerTime.DateTime.Month.ToString().PadLeft(2, '0'))
                .Replace("%r", ServerTime.DateTime.Day.ToString().PadLeft(2, '0'))
                .Replace("%s", ServerTime.DateTime.Hour.ToString().PadLeft(2, '0'))
                .Replace("%f", ServerTime.DateTime.Minute.ToString().PadLeft(2, '0'))
                .Replace("%m", ServerTime.DateTime.Second.ToString().PadLeft(2, '0'))
                .Replace("%t", ServerTime.DateTime.ToTimeSpan())
                .Replace("%g", Guid.NewGuid().ToString().Replace("-", ""));
            lblCaptureFileName.Text = string.Format("示例：{0}", fileName);
        }

        private void btnOpenCaptureLocation_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtCaptureLocation.Text)) txtCaptureLocation.Text = CommonString.DefaultImagePath;
            CommonMethod.OpenFolder(txtCaptureLocation.Text.Trim());
        }

        private void btnChangeCaptureLocation_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtCaptureLocation.Text)) txtCaptureLocation.Text = CommonString.DefaultImagePath;
            var dialog = new FolderBrowserDialog
            { SelectedPath = txtCaptureLocation.Text, ShowNewFolderButton = true, Description = "选择截图文件存放目录" };
            if (dialog.ShowDialog(this) == DialogResult.OK) txtCaptureLocation.Text = dialog.SelectedPath;
        }

        #endregion

        private void lnkWebSite_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            CommonMethod.OpenUrl(CommonString.StrServerHostUrl);
        }

        private void lnkLeft_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            CommonUser.UpdateLimitInfo();
            InitLeftCount();
        }

        private void InitLeftCount()
        {
            lnkLeft.Text = string.Format("今日余量:{0}", CommonUser.GetTodayLimitInfo(true));
            CommonMethod.SetStyle(lnkLeft, ControlStyles.Selectable, false);
        }

        private bool IsLocalOcrChecked(int code)
        {
            var result = Equals(code, CommonSetting.本地识别1) || Equals(code, CommonSetting.本地识别2) ||
                         Equals(code, CommonSetting.本地识别3) || Equals(code, CommonSetting.本地识别4) ||
                         Equals(code, CommonSetting.本地识别5);
            return result;
        }

        private void InitLocalOcrControl()
        {
            tbLocalOcr.Enabled = Program.NowUser?.IsSupportLocalOcr == true;
            OcrHelper.InitOcrGroup();
            var index = 1;
            var top = 19;
            OcrHelper.LstLocalOcrType?.ForEach(p =>
            {
                var left = 13;

                var panel = new Panel { Left = left, Top = top, Height = 26, Width = grpLocalOcr.Width - 10, Margin = CommonString.PaddingZero };
                var checkBox = new CheckBox { Text = p.Name, Tag = p, Checked = IsLocalOcrChecked(p.Code), Left = 5, AutoSize = true, Top = 5, Name = "本地识别" + index };
                checkBox.CheckedChanged += (s, e) =>
                {
                    if (checkBox.Checked)
                    {
                        if (!CommonUpdate.CheckIfLocalOcrModuleInstall(ref p))
                        {
                            checkBox.Checked = false;
                            CommonMethod.ShowHelpMsg("请先安装识别库后再尝试启用！");
                        }
                    }
                };
                panel.Controls.Add(checkBox);
                left = checkBox.Left + checkBox.Width;
                if (!string.IsNullOrEmpty(p.Desc))
                {
                    var pictureBox = new PictureBox
                    {
                        Image = Resources.帮助,
                        SizeMode = PictureBoxSizeMode.StretchImage,
                        Size = new Size(28, 24),
                        Left = left + 5,
                        Top = 0
                    };
                    tipMsg.SetToolTip(pictureBox, p.Desc);
                    panel.Controls.Add(pictureBox);
                    left = pictureBox.Left + pictureBox.Width;
                }
                if (!string.IsNullOrEmpty(p.DescUrl))
                {
                    var lnkLblWebSite = new LinkLabel
                    {
                        Text = "官网",
                        AutoSize = true,
                        Left = left,
                        Tag = p,
                        Top = 6,
                        LinkBehavior = LinkBehavior.NeverUnderline
                    };
                    lnkLblWebSite.LinkClicked += (s, e) =>
                    {
                        CommonMethod.OpenUrl(p.DescUrl);
                    };
                    panel.Controls.Add(lnkLblWebSite);
                    left = lnkLblWebSite.Left + lnkLblWebSite.Width + 5;
                }
                if (!string.IsNullOrEmpty(p.UpdateUrl))
                {
                    var lnkLblInstall = new LinkLabel
                    {
                        Text = "安装/更新本地识别库",
                        AutoSize = true,
                        Left = left,
                        Tag = p,
                        Top = 6,
                        LinkBehavior = LinkBehavior.NeverUnderline
                    };
                    p.AppPath = CommonString.DefaultLocalRecModelsPath;// + p.Name;
                    p.Date = DateTime.MinValue;
                    lnkLblInstall.LinkClicked += (s, e) =>
                    {
                        CommonUpdate.InstallLocalOcrItem(p);
                    };
                    panel.Controls.Add(lnkLblInstall);
                }

                grpLocalOcr.Controls.Add(panel);

                index++;
                top += 26;
            });
        }

        private void lnkLocalOcr_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            CommonUpdate.InstallLocalOcrExe(true);
        }

        private void lnkTestLocalOcr_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            var strState = OcrHelper.LocalOcrState();
            if (string.IsNullOrEmpty(strState))
            {
                strState = "本地识别服务未启动或服务状态异常！";
            }

            MessageBox.Show(this, strState, "本地引擎信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void lnkOpenLocalOcr_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            if (!File.Exists(CommonString.DefaultLocalRecExePath))
            {
                if (MessageBox.Show(this, "检测到本地识别服务尚未安装，是否立即安装？", "温馨提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
                    == DialogResult.Yes)
                {
                    CommonUpdate.InstallLocalOcrExe(true);
                }
                return;
            }
            LocalOcrService.OpenOcrService((int)numLocalOcrPort.Value, (int)numLocalOcrThread.Value);
        }

        private void cmbImageViewBackStyle_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnImageViewBackColor.Visible = Equals(cmbImageViewBackStyle.Text, ImageBoxGridDisplayMode.自定义.ToString());
        }
    }
}