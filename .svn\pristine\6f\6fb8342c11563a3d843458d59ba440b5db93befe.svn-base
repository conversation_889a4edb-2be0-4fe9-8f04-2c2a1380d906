﻿using OCRTools.Common;
using System;
using System.Collections.Concurrent;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Threading;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class UcLoading : UserControl
    {
        private LoadingTypeConfig _config;

        private int iC;

        private bool _isStop = true;
        private Thread _thread;

        public UcLoading()
        {
            InitializeComponent();
            BackgroundImageLayout = ImageLayout.Center;
        }

        public void InitLoading(Size size, Point locaion)
        {
            Visible = false;
            Size = size;
            Location = locaion;
            BackColor = Color.Transparent;
            BackgroundImageLayout = ImageLayout.Center;
            MouseDoubleClick += UcLoading_MouseDoubleClick;
        }

        private void UcLoading_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            var form = FindForm();
            if (form == null) return;
            form.WindowState = form.WindowState == FormWindowState.Maximized
                ? FormWindowState.Normal
                : FormWindowState.Maximized;
        }

        public void ShowLoading(string strText = "", bool isShowLoading = true)
        {
            StrFont = CommonString.GetSysBoldFont(21F);
            StrForeColor = CommonSetting.Get默认文字颜色();
            ShowText(strText);
            Visible = isShowLoading;
            if (isShowLoading)
            {
                CommonString.IsOnRec = true;
                BringToFront();
                if (_isStop)
                {
                    _isStop = false;
                    _thread = new Thread(Start);
                    _thread.Start();
                }
            }
        }

        private void Start()
        {
            while (!_isStop)
            {
                TmrTick();
                Thread.Sleep(_config.Interval);
            }

            _thread = null;
        }

        public void ShowText(string strText)
        {
            StrText = strText;
        }

        public string StrText { get; set; }

        public Color StrForeColor { get; set; }

        public Font StrFont { get; set; }

        public void CloseLoading(int seconds = 0)
        {
            if (seconds > 0)
                for (var i = 0; i < seconds * 2; i++)
                {
                    Thread.Sleep(500);
                    Application.DoEvents();
                }

            Visible = false;
            _isStop = true;
            CommonString.IsOnRec = false;
        }

        ConcurrentDictionary<string, Bitmap> dicTickImage = new ConcurrentDictionary<string, Bitmap>();

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            if (!string.IsNullOrEmpty(StrText))
            {
                e.Graphics.InterpolationMode = InterpolationMode.HighQualityBilinear;
                e.Graphics.CompositingQuality = CompositingQuality.HighQuality;
                e.Graphics.SmoothingMode = SmoothingMode.HighQuality;
                e.Graphics.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;

                var strSize = TextRenderer.MeasureText(StrText, StrFont);
                e.Graphics.DrawString(StrText, StrFont, new SolidBrush(StrForeColor), new PointF((ClientRectangle.Width - strSize.Width) / 2, (ClientRectangle.Height - strSize.Height - BackgroundImage?.Width ?? 35) / 2 - 20));
            }
        }

        private void TmrTick()
        {
            if (IsDisposed || !Visible)
            {
                return;
            }
            try
            {
                if (iC >= _config.ImgCount) iC = 0;
                var scale = MetroFramework.Forms.HighDpiHelper.GetFormDpi(FindForm());
                var imgKey = iC + (CommonSetting.夜间模式 ? "_dark" : "") + ("_" + scale.ToString("F2")) + (_config.IsRound ? "_" + iC * 30 : "");
                Bitmap _bgImg = dicTickImage.TryGetValue(imgKey, out var value) ? value : null;
                if (_bgImg == null)
                {
                    _bgImg = LoadingTypeHelper.GetImageByConfig(_config, _config.IsRound ? 0 : iC, _config.IsIngoreTheme);
                    if (_config.IsRound)
                    {
                        _bgImg = ImageProcessHelper.RotateImage(_bgImg, iC * 360 / _config.ImgCount);
                    }
                    _bgImg = ImageProcessHelper.ScaleImage(_bgImg, scale);
                }
                try
                {
                    if (!dicTickImage.ContainsKey(imgKey))
                    {
                        dicTickImage.TryAdd(imgKey, _bgImg);
                    }
                }
                catch { }
                BackgroundImage = _bgImg;
                iC++;
                Application.DoEvents();
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        internal void SetLoadingType(LoadingType loadingType)
        {
            _config = LoadingTypeHelper.GetTypeConfig(loadingType);
            iC = 0;
            dicTickImage.Clear();
        }
    }
}