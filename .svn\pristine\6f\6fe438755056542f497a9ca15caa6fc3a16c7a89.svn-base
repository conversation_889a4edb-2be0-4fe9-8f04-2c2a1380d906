using System.Collections.Generic;

namespace OCRTools
{
    internal class UndoManager
    {
        private readonly GraphicsList _graphicsList;

        private List<Command> _historyList;

        private int _nextUndo;

        public UndoManager(GraphicsList graphicsList)
        {
            _graphicsList = graphicsList;
            ClearHistory();
        }

        public bool CanUndo
        {
            get
            {
                if (_nextUndo < 0 || _nextUndo > _historyList.Count - 1) return false;
                return true;
            }
        }

        public bool CanRedo
        {
            get
            {
                if (_nextUndo == _historyList.Count - 1) return false;
                return true;
            }
        }

        public void ClearHistory()
        {
            _historyList = new List<Command>();
            _nextUndo = -1;
        }

        public void AddCommandToHistory(Command command)
        {
            TrimHistoryList();
            _historyList.Add(command);
            _nextUndo++;
        }

        public void Undo()
        {
            if (CanUndo)
            {
                var command = _historyList[_nextUndo];
                command.Undo(_graphicsList);
                _nextUndo--;
            }
        }

        public void Redo()
        {
            if (CanRedo)
            {
                var index = _nextUndo + 1;
                var command = _historyList[index];
                command.Redo(_graphicsList);
                _nextUndo++;
            }
        }

        private void TrimHistoryList()
        {
            if (_historyList.Count != 0 && _nextUndo != _historyList.Count - 1)
                for (var num = _historyList.Count - 1; num > _nextUndo; num--)
                    _historyList.RemoveAt(num);
        }
    }
}