﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Drawing.Text;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;

namespace OCRTools.Common
{
    public class ImageProcessHelper
    {
        private const string STR_DOC_IMG_SPILT = "\"image\":\"";
        private const string STR_IMG_SPILT = "\"image\":\"";

        public static readonly string[] ImageFileExtensions =
            {"jpg", "jpeg", "png", "gif", "bmp", "ico", "tif", "tiff"};

        public static bool IsImagesEqual(Bitmap bmp1, Bitmap bmp2)
        {
            using (var unsafeBitmap1 = new UnsafeBitmap(bmp1))
            using (var unsafeBitmap2 = new UnsafeBitmap(bmp2))
            {
                return unsafeBitmap1 == unsafeBitmap2;
            }
        }

        public static Bitmap ResizeImage(Bitmap bmp, Size size, bool allowEnlarge, bool centerImage = true)
        {
            return ResizeImage(bmp, size.Width, size.Height, allowEnlarge, centerImage);
        }

        public static Bitmap ResizeImage(Bitmap bmp, int width, int height, bool allowEnlarge, bool centerImage = true)
        {
            return ResizeImage(bmp, width, height, allowEnlarge, centerImage, Color.Transparent);
        }

        public static Bitmap ResizeImage(Bitmap bmp, int width, int height, bool allowEnlarge, bool centerImage,
            Color backColor)
        {
            double ratio;
            int newWidth, newHeight;

            if (!allowEnlarge && bmp.Width <= width && bmp.Height <= height)
            {
                ratio = 1.0;
                newWidth = bmp.Width;
                newHeight = bmp.Height;
            }
            else
            {
                var ratioX = (double)width / bmp.Width;
                var ratioY = (double)height / bmp.Height;
                ratio = ratioX < ratioY ? ratioX : ratioY;
                newWidth = (int)(bmp.Width * ratio);
                newHeight = (int)(bmp.Height * ratio);
            }

            var newX = 0;
            var newY = 0;

            if (centerImage)
            {
                newX += (int)((width - bmp.Width * ratio) / 2);
                newY += (int)((height - bmp.Height * ratio) / 2);
            }

            var bmpResult = new Bitmap(width, height, PixelFormat.Format32bppArgb);
            bmpResult.SetResolution(bmp.HorizontalResolution, bmp.VerticalResolution);

            using (var g = Graphics.FromImage(bmpResult))
            {
                if (backColor.A > 0) g.Clear(backColor);

                g.SetHighQuality();
                g.DrawImage(bmp, newX, newY, newWidth, newHeight);
            }

            return bmpResult;
        }

        public static Bitmap ResizeImage(Bitmap bmp, int width, int height, Color color, bool isToCircle = true,
            bool isShadow = false, decimal shadowWidth = 8, bool isActive = false)
        {
            if (width < 1 || height < 1) // || (bmp.Width == width && bmp.Height == height))
                return bmp;

            var bmpResult = new Bitmap(width, height, PixelFormat.Format32bppArgb);
            bmpResult.SetResolution(bmp.HorizontalResolution, bmp.VerticalResolution);

            using (bmp)
            using (var g = Graphics.FromImage(bmpResult))
            {
                g.Clear(color);
                g.SetHighQuality();

                using (var ia = new ImageAttributes())
                {
                    ia.SetWrapMode(WrapMode.TileFlipXY);
                    g.DrawImage(bmp, new Rectangle(0, 0, width, height), 0, 0, bmp.Width, bmp.Height,
                        GraphicsUnit.Pixel, ia);
                }
            }

            if (isToCircle) bmpResult = ToCircle(bmpResult, color);

            if (isShadow || isActive)
                return DrawImageShadow(bmpResult, isToCircle, isShadow, (int)shadowWidth, isActive);
            return bmpResult;
        }

        private static Bitmap DrawImageShadow(Bitmap bmpResult, bool isToCircle, bool isShadow = false,
            int shadowWidth = 8, bool isActive = false)
        {
            var bmpTmp = new Bitmap(bmpResult.Width + (isShadow ? shadowWidth : 0) * 2,
                bmpResult.Height + (isShadow ? shadowWidth : 0) * 2);
            using (var g = Graphics.FromImage(bmpTmp))
            {
                g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                g.SmoothingMode = SmoothingMode.HighQuality;
                g.PixelOffsetMode = PixelOffsetMode.HighQuality;
                g.CompositingQuality = CompositingQuality.HighQuality;
                g.CompositingMode = CompositingMode.SourceOver;
                g.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
                var shadowMore = isActive ? 2 : 0;

                if (isShadow)
                {
                    var normalBaseColor =
                        isToCircle ? bmpResult.GetPixel(bmpResult.Width / 2, 1) : bmpResult.GetPixel(1, 1);
                    if (normalBaseColor.A == 255)
                    {
                        var baseColor = isActive ? StaticValue.ShadowActiveColor : normalBaseColor;
                        for (var i = 1; i <= shadowWidth + shadowMore; i++)
                        {
                            var borderColor =
                                Color.FromArgb((int)Math.Min(255 / shadowWidth * (isActive ? 3 : 1.5), 255),
                                    baseColor);
                            using (var pen = new Pen(borderColor, 1))
                            {
                                var rect = new Rectangle(i, i, bmpTmp.Width - i * 2, bmpTmp.Height - i * 2);
                                if (isToCircle)
                                    g.DrawEllipse(pen, rect);
                                else
                                    g.DrawRectangle(pen, rect);
                            }
                        }
                    }
                }

                g.DrawImage(bmpResult
                    , new Rectangle((isShadow ? shadowWidth : 0) + shadowMore,
                        (isShadow ? shadowWidth : 0) + shadowMore, bmpResult.Width - shadowMore * 2,
                        bmpResult.Height - shadowMore * 2)
                    , 0, 0, bmpResult.Width, bmpResult.Height, GraphicsUnit.Pixel);
                return bmpTmp;
            }
        }

        public static Bitmap ToCircle(Bitmap bitmap, Color color)
        {
            var bmpResult = new Bitmap(bitmap.Width, bitmap.Height, PixelFormat.Format32bppArgb);
            using (var g = Graphics.FromImage(bmpResult))
            {
                g.Clear(color);

                g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                g.SmoothingMode = SmoothingMode.HighQuality;
                g.PixelOffsetMode = PixelOffsetMode.HighQuality;
                g.CompositingQuality = CompositingQuality.HighQuality;
                g.CompositingMode = CompositingMode.SourceOver;
                g.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;

                using (var br = new TextureBrush(bitmap, WrapMode.Clamp,
                    new RectangleF(0, 0, bitmap.Width, bitmap.Height)))
                {
                    br.ScaleTransform(1, 1);
                    g.FillEllipse(br, new Rectangle(Point.Empty, bitmap.Size));
                }
            }

            return bmpResult;
        }

        public static Bitmap CreateCheckerPattern(int width, int height, Color checkerColor1, Color checkerColor2)
        {
            var bmp = new Bitmap(width * 2, height * 2);

            using (var g = Graphics.FromImage(bmp))
            using (Brush brush1 = new SolidBrush(checkerColor1))
            using (Brush brush2 = new SolidBrush(checkerColor2))
            {
                g.FillRectangle(brush1, 0, 0, width, height);
                g.FillRectangle(brush1, width, height, width, height);
                g.FillRectangle(brush2, width, 0, width, height);
                g.FillRectangle(brush2, 0, height, width, height);
            }

            return bmp;
        }

        public static Bitmap CreateCheckerPattern(int width, int height)
        {
            return CreateCheckerPattern(width, height, SystemColors.ControlLight, SystemColors.ControlLightLight);
        }

        public static Bitmap DrawCheckers(int width, int height)
        {
            return DrawCheckers(width, height, 10, SystemColors.ControlLight, SystemColors.ControlLightLight);
        }

        public static Bitmap DrawCheckers(int width, int height, int checkerSize, Color checkerColor1,
            Color checkerColor2)
        {
            var bmp = new Bitmap(width, height);

            using (var g = Graphics.FromImage(bmp))
            using (Image checker = CreateCheckerPattern(checkerSize, checkerSize, checkerColor1, checkerColor2))
            using (Brush checkerBrush = new TextureBrush(checker, WrapMode.Tile))
            {
                g.FillRectangle(checkerBrush, new Rectangle(0, 0, bmp.Width, bmp.Height));
            }

            return bmp;
        }

        public static Bitmap ResizeImageLimit(Bitmap bmp, Size size, bool isAlph = false)
        {
            return ResizeImageLimit(bmp, size.Width, size.Height, isAlph);
        }

        /// <summary>If image size is bigger than specified size then resize it and keep aspect ratio else return image.</summary>
        public static Bitmap ResizeImageLimit(Bitmap bmp, int width, int height, bool isAlph = false)
        {
            if (bmp.Width <= width && bmp.Height <= height) return bmp;

            var ratioX = (double)width / bmp.Width;
            var ratioY = (double)height / bmp.Height;

            if (ratioX < ratioY)
                height = (int)Math.Round(bmp.Height * ratioX);
            else if (ratioX > ratioY) width = (int)Math.Round(bmp.Width * ratioY);

            return ResizeImage(bmp, width, height, isAlph);
        }

        /// <summary>
        ///     灰度（指针）
        /// </summary>
        /// <param name="curBitmpap"></param>
        /// <returns></returns>
        public static Bitmap BitToGrayByPointer(Bitmap curBitmpap)
        {
            if (curBitmpap != null)
                try
                {
                    //位图矩形
                    var rect = new Rectangle(0, 0, curBitmpap.Width, curBitmpap.Height);
                    //以可读写的方式锁定全部位图像素
                    var bmpData = curBitmpap.LockBits(rect, ImageLockMode.ReadWrite, curBitmpap.PixelFormat);

                    //启用不安全模式
                    unsafe
                    {
                        //得到首地址
                        var ptr = (byte*)bmpData.Scan0;
                        //二维图像循环
                        for (var i = 0; i < bmpData.Height; i++)
                        {
                            for (var j = 0; j < bmpData.Width; j++)
                            {
                                //利用公式计算灰度值
                                var temp = (byte)(0.299 * ptr[2] + 0.587 * ptr[1] + 0.114 * ptr[0]);
                                //R=G=B
                                ptr[0] = ptr[1] = ptr[2] = temp;
                                //指向下一个像素
                                ptr += 3;
                            }

                            //指向下一行数组的首个字节
                            ptr += bmpData.Stride - bmpData.Width * 3;
                        }
                    }

                    //解锁位图像素
                    curBitmpap.UnlockBits(bmpData);
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }

            return curBitmpap;
        }

        public static Bitmap LoadImage(string filePath)
        {
            try
            {
                if (!string.IsNullOrEmpty(filePath))
                {
                    filePath = GetAbsolutePath(filePath);

                    if (!string.IsNullOrEmpty(filePath) && IsImageFile(filePath) && File.Exists(filePath))
                    {
                        // http://stackoverflow.com/questions/788335/why-does-image-fromfile-keep-a-file-handle-open-sometimes
                        var bmp = (Bitmap)Image.FromStream(new MemoryStream(File.ReadAllBytes(filePath)));

                        RotateImageByExifOrientationData(bmp);

                        return bmp;
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            return null;
        }

        public static RotateFlipType RotateImageByExifOrientationData(Bitmap bmp, bool removeExifOrientationData = true)
        {
            var orientationId = 0x0112;
            var rotateType = RotateFlipType.RotateNoneFlipNone;

            if (bmp.PropertyIdList.Contains(orientationId))
            {
                var propertyItem = bmp.GetPropertyItem(orientationId);
                rotateType = GetRotateFlipTypeByExifOrientationData(propertyItem.Value[0]);

                if (rotateType != RotateFlipType.RotateNoneFlipNone)
                {
                    bmp.RotateFlip(rotateType);

                    if (removeExifOrientationData) bmp.RemovePropertyItem(orientationId);
                }
            }

            return rotateType;
        }

        private static RotateFlipType GetRotateFlipTypeByExifOrientationData(int orientation)
        {
            switch (orientation)
            {
                default:
                    return RotateFlipType.RotateNoneFlipNone;
                case 2:
                    return RotateFlipType.RotateNoneFlipX;
                case 3:
                    return RotateFlipType.Rotate180FlipNone;
                case 4:
                    return RotateFlipType.Rotate180FlipX;
                case 5:
                    return RotateFlipType.Rotate90FlipX;
                case 6:
                    return RotateFlipType.Rotate90FlipNone;
                case 7:
                    return RotateFlipType.Rotate270FlipX;
                case 8:
                    return RotateFlipType.Rotate270FlipNone;
            }
        }

        public static string GetAbsolutePath(string path)
        {
            path = ExpandFolderVariables(path);

            if (!Path.IsPathRooted(path)) // Is relative path?
                path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, path);

            return Path.GetFullPath(path);
        }

        public static T[] GetEnums<T>()
        {
            return (T[])Enum.GetValues(typeof(T));
        }

        public static string ExpandFolderVariables(string path)
        {
            if (!string.IsNullOrEmpty(path))
                try
                {
                    foreach (var specialFolder in GetEnums<Environment.SpecialFolder>())
                        path = path.Replace($"%{specialFolder}%", Environment.GetFolderPath(specialFolder),
                            StringComparison.OrdinalIgnoreCase);

                    path = Environment.ExpandEnvironmentVariables(path);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }

            return path;
        }

        public static string GetFilenameExtension(string filePath, bool includeDot = false,
            bool checkSecondExtension = true)
        {
            var extension = "";

            if (!string.IsNullOrEmpty(filePath))
            {
                var pos = filePath.LastIndexOf('.');

                if (pos >= 0)
                {
                    extension = filePath.Substring(pos + 1);

                    if (checkSecondExtension)
                    {
                        filePath = filePath.Remove(pos);
                        var extension2 = GetFilenameExtension(filePath, false, false);

                        if (!string.IsNullOrEmpty(extension2))
                            foreach (var knownExtension in new[] { "tar" })
                                if (extension2.Equals(knownExtension, StringComparison.OrdinalIgnoreCase))
                                {
                                    extension = extension2 + "." + extension;
                                    break;
                                }
                    }

                    if (includeDot) extension = "." + extension;
                }
            }

            return extension;
        }

        internal static bool CheckExtension(string filePath, IEnumerable<string> extensions)
        {
            var ext = GetFilenameExtension(filePath);

            if (!string.IsNullOrEmpty(ext))
                return extensions.Any(x => ext.Equals(x, StringComparison.OrdinalIgnoreCase));

            return false;
        }

        internal static bool IsImageFile(string filePath)
        {
            return CheckExtension(filePath, ImageFileExtensions);
        }

        /// <summary>
        ///     对比度增强（指针）
        /// </summary>
        /// <returns></returns>
        internal static unsafe Bitmap BitContrastByPointer(Bitmap bitmap, int degree = 90)
        {
            if (bitmap != null)
                try
                {
                    var num2 = (100.0 + degree) / 100.0;
                    num2 *= num2;
                    var width = bitmap.Width;
                    var height = bitmap.Height;
                    var bitmapdata = bitmap.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.ReadWrite,
                        PixelFormat.Format24bppRgb);
                    var numPtr = (byte*)bitmapdata.Scan0;

                    var offset = bitmapdata.Stride - width * 3;
                    for (var i = 0; i < height; i++)
                    {
                        for (var j = 0; j < width; j++)
                        {
                            for (var k = 0; k < 3; k++)
                            {
                                var num = ((numPtr[k] / 255.0 - 0.5) * num2 + 0.5) * 255.0;
                                if (num < 0.0) num = 0.0;
                                if (num > 255.0) num = 255.0;
                                numPtr[k] = (byte)num;
                            }

                            numPtr += 3;
                        }

                        numPtr += offset;
                    }

                    bitmap.UnlockBits(bitmapdata);
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }

            return bitmap;
        }

        /// <summary>
        ///     反色（指针）
        /// </summary>
        /// <param name="curBitmpap"></param>
        /// <returns></returns>
        internal static Bitmap BitInverseByPointer(Bitmap curBitmpap)
        {
            if (curBitmpap != null)
                try
                {
                    var srcdat = curBitmpap.LockBits(new Rectangle(Point.Empty, curBitmpap.Size),
                        ImageLockMode.ReadWrite, PixelFormat.Format24bppRgb); // 锁定位图
                    unsafe // 不安全代码
                    {
                        var pix = (byte*)srcdat.Scan0; // 像素首地址
                        for (var i = 0; i < srcdat.Stride * srcdat.Height; i++) pix[i] = (byte)(255 - pix[i]);
                        curBitmpap.UnlockBits(srcdat); // 解锁
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }

            return curBitmpap;
        }

        internal static Bitmap CropBitmap(Bitmap bmp, Rectangle rect)
        {
            if (bmp != null && rect.X >= 0 && rect.Y >= 0 && rect.Width > 0 && rect.Height > 0 &&
                new Rectangle(0, 0, bmp.Width, bmp.Height).Contains(rect)) return bmp.Clone(rect, bmp.PixelFormat);

            return null;
        }

        internal static Bitmap CombineImages(IEnumerable<Image> images, Orientation orientation = Orientation.Vertical,
            ImageCombinerAlignment alignment = ImageCombinerAlignment.LeftOrTop, int space = 0)
        {
            int width, height;
            var imageCount = images.Count();
            var spaceSize = space * (imageCount - 1);

            if (orientation == Orientation.Vertical)
            {
                width = images.Max(x => x.Width);
                height = images.Sum(x => x.Height) + spaceSize;
            }
            else
            {
                width = images.Sum(x => x.Width) + spaceSize;
                height = images.Max(x => x.Height);
            }

            var bmp = new Bitmap(width, height);

            using (var g = Graphics.FromImage(bmp))
            {
                g.SetHighQuality();
                var position = 0;

                foreach (var image in images)
                {
                    Rectangle rect;

                    if (orientation == Orientation.Vertical)
                    {
                        int x;
                        switch (alignment)
                        {
                            default:
                                x = 0;
                                break;
                            case ImageCombinerAlignment.Center:
                                x = width / 2 - image.Width / 2;
                                break;
                            case ImageCombinerAlignment.RightOrBottom:
                                x = width - image.Width;
                                break;
                        }

                        rect = new Rectangle(x, position, image.Width, image.Height);
                        position += image.Height + space;
                    }
                    else
                    {
                        int y;
                        switch (alignment)
                        {
                            default:
                                y = 0;
                                break;
                            case ImageCombinerAlignment.Center:
                                y = height / 2 - image.Height / 2;
                                break;
                            case ImageCombinerAlignment.RightOrBottom:
                                y = height - image.Height;
                                break;
                        }

                        rect = new Rectangle(position, y, image.Width, image.Height);
                        position += image.Width + space;
                    }

                    g.DrawImage(image, rect);
                }
            }

            return bmp;
        }

        internal static Image ProcessImage(Bitmap originImage, ImageProcessType imgType)
        {
            Image img = originImage;
            switch (imgType)
            {
                case ImageProcessType.原图灰度:
                    img = BitToGrayByPointer(originImage);
                    break;
                case ImageProcessType.底片效果:
                    img = BitInverseByPointer(originImage);
                    break;
                case ImageProcessType.原图增强:
                    img = BitContrastByPointer(originImage);
                    break;
                case ImageProcessType.倾斜矫正:
                    img = DeskewImage(originImage);
                    //img = EnhanceDocImage(ImageToBase64(originImage));
                    break;
                case ImageProcessType.图片增强:
                    img = EnhanceImage(ImageToBase64(originImage));
                    break;
            }

            return img;
        }

        /// <summary>
        ///文档图像矫正
        /// </summary>
        /// <param name="strBase64"></param>
        /// <returns></returns>
        internal static Bitmap EnhanceDocImage(string strBase64)
        {
            var result = "";

            var strTmp = WebClientExt.GetHtml("https://api.textin.com/home/<USER>", "", "", strBase64, "", 10);

            if (!string.IsNullOrEmpty(strTmp) && strTmp.Contains(STR_DOC_IMG_SPILT))
            {
                result = strTmp.Substring(strTmp.IndexOf(STR_DOC_IMG_SPILT) + STR_DOC_IMG_SPILT.Length);
                result = result.Substring(0, result.IndexOf("\""));
            }

            if (!string.IsNullOrEmpty(result)) return Base64StringToImage(System.Text.RegularExpressions.Regex.Unescape(result));
            return null;
        }

        /// <summary>
        ///照片图像增强
        /// </summary>
        /// <param name="strBase64"></param>
        /// <returns></returns>
        internal static Bitmap EnhanceImage(string strBase64)
        {
            var result = "";
            var strPost = strBase64;

            var strTmp = WebClientExt.GetHtml("https://api.textin.com/home/<USER>", "", "", strPost, "", 10);

            //{"code":0,"message":"ok","request_id":"0.5384177315701995","data":{"image":"/9j/4AAQSkZJRgABAQAAAQ"}}
            if (!string.IsNullOrEmpty(strTmp) && strTmp.Contains(STR_IMG_SPILT))
            {
                result = strTmp.Substring(strTmp.IndexOf(STR_IMG_SPILT) + STR_IMG_SPILT.Length);
                result = result.Substring(0, result.IndexOf("\""));
            }

            if (!string.IsNullOrEmpty(result)) return Base64StringToImage(System.Text.RegularExpressions.Regex.Unescape(result));
            return null;
        }

        private static string ImageToBase64(Image image)
        {
            var byts = ImageToByte(image);
            return byts?.Length > 0 ? Convert.ToBase64String(byts) : null;
        }

        public static byte[] ImageToByte(Image image)
        {
            using (var ms = new MemoryStream())
            {
                if (image.RawFormat.Guid == ImageFormat.Gif.Guid)
                    image.Save(ms, ImageFormat.Gif);
                else if (image.RawFormat.Guid == ImageFormat.Bmp.Guid)
                    image.Save(ms, ImageFormat.Bmp);
                else if (image.RawFormat.Guid == ImageFormat.Png.Guid)
                    image.Save(ms, ImageFormat.Png);
                else if (image.RawFormat.Guid == ImageFormat.Tiff.Guid)
                    image.Save(ms, ImageFormat.Tiff);
                else
                    image.Save(ms, ImageFormat.Jpeg);

                return ms.ToArray();
            }
        }

        public static Bitmap ByteToImage(byte[] bytes)
        {
            Bitmap bmp = null;
            try
            {
                using (var ms = new MemoryStream())
                {
                    ms.Write(bytes, 0, bytes.Length);
                    bmp = new Bitmap(ms);
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }

            return bmp;
        }

        private static Bitmap Base64StringToImage(string base64Img)
        {
            Bitmap bmp = null;
            try
            {
                using (var ms = new MemoryStream())
                {
                    var bytes = Convert.FromBase64String(base64Img);
                    ms.Write(bytes, 0, bytes.Length);
                    bmp = new Bitmap(ms);
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }

            return bmp;
        }

        internal static Bitmap DeskewImage(Bitmap bmp)
        {
            Deskew deskew = new Deskew();
            Bitmap tempBmp = CropImage(bmp, bmp.Width / 4, bmp.Height / 4, bmp.Width / 2, bmp.Height / 2);
            deskew._internalBmp = BinarizeImage(tempBmp, 123);
            double angle = deskew.GetSkewAngle();
            return RotateImage(bmp, (float)(-angle));
        }

        /// <summary>
        /// 图像剪切
        /// </summary>
        /// <param name="bmp"></param>
        /// <param name="StartX"></param>
        /// <param name="StartY"></param>
        /// <param name="w"></param>
        /// <param name="h"></param>
        /// <returns></returns>
        private static Bitmap CropImage(Bitmap bmp, int StartX, int StartY, int w, int h)
        {
            try
            {
                Bitmap bmpOut = new Bitmap(w, h, PixelFormat.Format32bppArgb);

                Graphics g = Graphics.FromImage(bmpOut);
                g.DrawImage(bmp, new Rectangle(0, 0, w, h), new Rectangle(StartX, StartY, w, h), GraphicsUnit.Pixel);
                g.Dispose();

                return bmpOut;
            }
            catch
            {
                return null;
            }
        }


        /// <summary>
        /// 图像二值化
        /// </summary>
        /// <param name="b"></param>
        /// <param name="threshold">阈值</param>
        private static Bitmap BinarizeImage(Bitmap b, byte threshold)
        {
            int width = b.Width;
            int height = b.Height;
            BitmapData data = b.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.ReadWrite, PixelFormat.Format32bppRgb);
            unsafe
            {
                byte* p = (byte*)data.Scan0;
                int offset = data.Stride - width * 4;
                byte R, G, B, gray;
                for (int y = 0; y < height; y++)
                {
                    for (int x = 0; x < width; x++)
                    {
                        R = p[2];
                        G = p[1];
                        B = p[0];
                        gray = (byte)((R * 19595 + G * 38469 + B * 7472) >> 16);
                        if (gray >= threshold)
                        {
                            p[0] = p[1] = p[2] = 255;
                        }
                        else
                        {
                            p[0] = p[1] = p[2] = 0;
                        }
                        p += 4;
                    }
                    p += offset;
                }
                b.UnlockBits(data);
                return b;
            }
        }

        /// <summary>
        /// 图像旋转
        /// </summary>
        /// <param name="bmp"></param>
        /// <param name="angle">角度</param>
        /// <returns></returns>
        private static Bitmap RotateImage(Bitmap bmp, float angle)
        {
            PixelFormat pixelFormat = bmp.PixelFormat;
            PixelFormat pixelFormatOld = pixelFormat;
            if (bmp.Palette.Entries.Count() > 0)
            {
                pixelFormat = PixelFormat.Format24bppRgb;
            }

            Bitmap tmpBitmap = new Bitmap(bmp.Width, bmp.Height, pixelFormat);
            tmpBitmap.SetResolution(bmp.HorizontalResolution, bmp.VerticalResolution);
            Graphics g = Graphics.FromImage(tmpBitmap);
            try
            {
                g.FillRectangle(Brushes.White, 0, 0, bmp.Width, bmp.Height);
                g.RotateTransform(angle);
                g.DrawImage(bmp, 0, 0);
            }
            catch
            {
            }
            finally
            {
                g.Dispose();
            }

            if (pixelFormatOld == PixelFormat.Format8bppIndexed) tmpBitmap = CopyTo8bpp(tmpBitmap);
            else if (pixelFormatOld == PixelFormat.Format1bppIndexed) tmpBitmap = CopyTo1bpp(tmpBitmap);

            return tmpBitmap;
        }

        private static Bitmap CopyTo1bpp(Bitmap b)
        {
            int w = b.Width, h = b.Height; Rectangle r = new Rectangle(0, 0, w, h);
            if (b.PixelFormat != PixelFormat.Format32bppPArgb)
            {
                Bitmap temp = new Bitmap(w, h, PixelFormat.Format32bppPArgb);
                temp.SetResolution(b.HorizontalResolution, b.VerticalResolution);
                Graphics g = Graphics.FromImage(temp);
                g.DrawImage(b, r, 0, 0, w, h, GraphicsUnit.Pixel);
                g.Dispose();
                b = temp;
            }
            BitmapData bdat = b.LockBits(r, ImageLockMode.ReadOnly, b.PixelFormat);
            Bitmap b0 = new Bitmap(w, h, PixelFormat.Format1bppIndexed);
            b0.SetResolution(b.HorizontalResolution, b.VerticalResolution);
            BitmapData b0dat = b0.LockBits(r, ImageLockMode.ReadWrite, PixelFormat.Format1bppIndexed);
            for (int y = 0; y < h; y++)
            {
                for (int x = 0; x < w; x++)
                {
                    int index = y * bdat.Stride + (x * 4);
                    if (Color.FromArgb(Marshal.ReadByte(bdat.Scan0, index + 2), Marshal.ReadByte(bdat.Scan0, index + 1), Marshal.ReadByte(bdat.Scan0, index)).GetBrightness() > 0.5f)
                    {
                        int index0 = y * b0dat.Stride + (x >> 3);
                        byte p = Marshal.ReadByte(b0dat.Scan0, index0);
                        byte mask = (byte)(0x80 >> (x & 0x7));
                        Marshal.WriteByte(b0dat.Scan0, index0, (byte)(p | mask));
                    }
                }
            }
            b0.UnlockBits(b0dat);
            b.UnlockBits(bdat);
            return b0;
        }

        private static Bitmap CopyTo8bpp(Bitmap bmp)
        {
            if (bmp == null) return null;

            Rectangle rect = new Rectangle(0, 0, bmp.Width, bmp.Height);
            BitmapData bmpData = bmp.LockBits(rect, ImageLockMode.ReadOnly, bmp.PixelFormat);

            int width = bmpData.Width;
            int height = bmpData.Height;
            int stride = bmpData.Stride;
            int offset = stride - width * 3;
            IntPtr ptr = bmpData.Scan0;
            int scanBytes = stride * height;

            int posScan = 0, posDst = 0;
            byte[] rgbValues = new byte[scanBytes];
            Marshal.Copy(ptr, rgbValues, 0, scanBytes);
            byte[] grayValues = new byte[width * height];

            for (int i = 0; i < height; i++)
            {
                for (int j = 0; j < width; j++)
                {
                    double temp = rgbValues[posScan++] * 0.11 +
                        rgbValues[posScan++] * 0.59 +
                        rgbValues[posScan++] * 0.3;
                    grayValues[posDst++] = (byte)temp;
                }
                posScan += offset;
            }

            Marshal.Copy(rgbValues, 0, ptr, scanBytes);
            bmp.UnlockBits(bmpData);

            Bitmap bitmap = new Bitmap(width, height, PixelFormat.Format8bppIndexed);
            bitmap.SetResolution(bmp.HorizontalResolution, bmp.VerticalResolution);
            BitmapData bitmapData = bitmap.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.WriteOnly, PixelFormat.Format8bppIndexed);

            int offset0 = bitmapData.Stride - bitmapData.Width;
            int scanBytes0 = bitmapData.Stride * bitmapData.Height;
            byte[] rawValues = new byte[scanBytes0];

            int posSrc = 0;
            posScan = 0;
            for (int i = 0; i < height; i++)
            {
                for (int j = 0; j < width; j++)
                {
                    rawValues[posScan++] = grayValues[posSrc++];
                }
                posScan += offset0;
            }

            Marshal.Copy(rawValues, 0, bitmapData.Scan0, scanBytes0);
            bitmap.UnlockBits(bitmapData);

            ColorPalette palette;
            using (Bitmap bmp0 = new Bitmap(1, 1, PixelFormat.Format8bppIndexed))
            {
                palette = bmp0.Palette;
            }
            for (int i = 0; i < 256; i++)
            {
                palette.Entries[i] = Color.FromArgb(i, i, i);
            }
            bitmap.Palette = palette;

            return bitmap;
        }

    }

    public class Deskew
    {
        // Representation of a line in the image.
        private class HougLine
        {
            // Count of points in the line.
            public int Count;
            // Index in Matrix.
            public int Index;
            // The line is represented as all x,y that solve y*cos(alpha)-x*sin(alpha)=d
            public double Alpha;
        }


        // The Bitmap
        public Bitmap _internalBmp;

        // The range of angles to search for lines
        const double ALPHA_START = -20;
        const double ALPHA_STEP = 0.2;
        const int STEPS = 40 * 5;
        const double STEP = 1;

        // Precalculation of sin and cos.
        double[] _sinA;
        double[] _cosA;

        // Range of d
        double _min;


        int _count;
        // Count of points that fit in a line.
        int[] _hMatrix;

        // Calculate the skew angle of the image cBmp.
        public double GetSkewAngle()
        {
            // Hough Transformation
            Calc();

            // Top 20 of the detected lines in the image.
            HougLine[] hl = GetTop(20);

            // Average angle of the lines
            double sum = 0;
            int count = 0;
            for (int i = 0; i <= 19; i++)
            {
                sum += hl[i].Alpha;
                count += 1;
            }
            return sum / count;
        }

        // Calculate the Count lines in the image with most points.
        private HougLine[] GetTop(int count)
        {
            HougLine[] hl = new HougLine[count];

            for (int i = 0; i <= count - 1; i++)
            {
                hl[i] = new HougLine();
            }
            for (int i = 0; i <= _hMatrix.Length - 1; i++)
            {
                if (_hMatrix[i] > hl[count - 1].Count)
                {
                    hl[count - 1].Count = _hMatrix[i];
                    hl[count - 1].Index = i;
                    int j = count - 1;
                    while (j > 0 && hl[j].Count > hl[j - 1].Count)
                    {
                        HougLine tmp = hl[j];
                        hl[j] = hl[j - 1];
                        hl[j - 1] = tmp;
                        j -= 1;
                    }
                }
            }

            for (int i = 0; i <= count - 1; i++)
            {
                int dIndex = hl[i].Index / STEPS;
                int alphaIndex = hl[i].Index - dIndex * STEPS;
                hl[i].Alpha = GetAlpha(alphaIndex);
                //hl[i].D = dIndex + _min;
            }

            return hl;
        }


        // Hough Transforamtion:
        private void Calc()
        {
            int hMin = _internalBmp.Height / 4;
            int hMax = _internalBmp.Height * 3 / 4;

            Init();
            for (int y = hMin; y <= hMax; y++)
            {
                for (int x = 1; x <= _internalBmp.Width - 2; x++)
                {
                    // Only lower edges are considered.
                    if (IsBlack(x, y))
                    {
                        if (!IsBlack(x, y + 1))
                        {
                            Calc(x, y);
                        }
                    }
                }
            }
        }

        // Calculate all lines through the point (x,y).
        private void Calc(int x, int y)
        {
            int alpha;

            for (alpha = 0; alpha <= STEPS - 1; alpha++)
            {
                double d = y * _cosA[alpha] - x * _sinA[alpha];
                int calculatedIndex = (int)CalcDIndex(d);
                int index = calculatedIndex * STEPS + alpha;
                try
                {
                    _hMatrix[index] += 1;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine(ex.ToString());
                }
            }
        }

        private double CalcDIndex(double d)
        {
            return Convert.ToInt32(d - _min);
        }

        private bool IsBlack(int x, int y)
        {
            Color c = _internalBmp.GetPixel(x, y);
            double luminance = (c.R * 0.299) + (c.G * 0.587) + (c.B * 0.114);
            return luminance < 140;
        }

        private void Init()
        {
            // Precalculation of sin and cos.
            _cosA = new double[STEPS];
            _sinA = new double[STEPS];

            for (int i = 0; i < STEPS; i++)
            {
                double angle = GetAlpha(i) * Math.PI / 180.0;
                _sinA[i] = Math.Sin(angle);
                _cosA[i] = Math.Cos(angle);
            }

            // Range of d:
            _min = -_internalBmp.Width;
            _count = (int)(2 * (_internalBmp.Width + _internalBmp.Height) / STEP);
            _hMatrix = new int[_count * STEPS];

        }

        private double GetAlpha(int index)
        {
            return ALPHA_START + index * ALPHA_STEP;
        }
    }

    internal enum ImageCombinerAlignment
    {
        LeftOrTop,
        Center,
        RightOrBottom
    }

    internal enum ImageProcessType
    {
        原始图片 = 0,
        原图灰度 = 1,
        底片效果 = 2,
        原图增强 = 3,
        倾斜矫正 = 50,
        图片增强 = 51,
    }
}