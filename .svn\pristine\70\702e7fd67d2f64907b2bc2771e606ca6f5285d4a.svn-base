﻿using MetroFramework;
using OCRTools.Colors;
using OCRTools.Common;
using OCRTools.Properties;
using OCRTools.Units;
using System;
using System.Collections.Concurrent;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection;

namespace OCRTools
{
    public class CommonSetting
    {
        private const string 界面 = "界面";
        private const string 常规 = "常规";
        private const string 识别 = "识别";
        private const string 截屏 = "截屏";
        private const string 贴图 = "贴图";
        private const string 关于 = "关于";
        private const string 工具 = "工具";

        private static readonly Type BaseType = typeof(CommonSetting);

        private static readonly ConcurrentBag<ConfigDesc> LstAllConfig = new ConcurrentBag<ConfigDesc>();

        public static void InitSetting()
        {
            var properties = BaseType.GetProperties();
            if (properties.Length <= 0) return;
            var isFirst = LstAllConfig.Count <= 0;
            foreach (var item in properties)
            {
                if (!(item.GetCustomAttributes(typeof(DescriptionAttribute), false).FirstOrDefault() is
                    DescriptionAttribute customProperty)) continue;
                var value = GetObjectValue(customProperty.Description, item.Name);
                if (!LstAllConfig.Any(p => Equals(p.Section, customProperty.Description) && Equals(p.Name, item.Name)))
                    LstAllConfig.Add(new ConfigDesc
                    {
                        Name = item.Name,
                        Section = customProperty.Description,
                        PropertyInfo = item,
                        DefaultValue = item?.GetValue(null, null)
                    });
                if (value == null) continue;
                SetValue(customProperty.Description, item.Name, value, true);
            }
            if (isFirst)
            {
                if (string.IsNullOrEmpty(用户名))
                {
                    SetValue("用户名", IniHelper.GetValue("配置", "Account"));
                }
                if (string.IsNullOrEmpty(密码))
                {
                    SetValue("密码", IniHelper.GetValue("配置", "Password"));
                }
            }
        }

        private static ConfigDesc GetSection(string section, string key)
        {
            var configDesc = (LstAllConfig.FirstOrDefault(p => Equals(p.Name, key) && Equals(p.Section, section)) ??
                              LstAllConfig.FirstOrDefault(p => Equals(p.Name, key))) ?? new ConfigDesc
                              {
                                  Name = key,
                                  Section = section,
                                  PropertyInfo = BaseType?.GetProperty(key)
                              };
            return configDesc;
        }

        public static T ConvertToEnum<T>(string objValue, T defaultValue = default(T)) where T : struct
        {
            var result = defaultValue;
            try
            {
                if (!string.IsNullOrEmpty(objValue))
                {
                    if (!Enum.TryParse(objValue, true, out result))
                    {
                        result = defaultValue;
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return result;
        }

        public static T GetValue<T>(string section, string key, T defaultValue = default(T))
        {
            var value = GetObjectValue(section, key, defaultValue);
            try
            {
                return value == null && defaultValue != null ? defaultValue : (T)value;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public static object GetObjectValue(string section, string key, object defaultValue = null)
        {
            var config = GetSection(section, key);
            return GetObjectValue(config, defaultValue);
        }

        public static object GetObjectValue(ConfigDesc config, object defaultValue = null)
        {
            object value = null;
            var iniValue = IniHelper.GetValue(config.Section, config.Name);
            defaultValue = config.DefaultValue ?? defaultValue;
            if (!string.IsNullOrEmpty(iniValue))
            {
                var type = config.PropertyInfo?.PropertyType;
                value = type == null ? iniValue : TypeDescriptor.GetConverter(type).ConvertFromInvariantString(iniValue);
            }

            return value ?? defaultValue;
        }

        public static void SetValue(string section, string key, object value, bool isDefault = false)
        {
            if (string.IsNullOrEmpty(section)
                || string.IsNullOrEmpty(key))
                return;
            var config = GetSection(section, key);
            if (Equals(value, isDefault ? config.DefaultValue : GetObjectValue(config)))
                return;
            var type = config.PropertyInfo?.PropertyType;
            config.PropertyInfo?.SetValue(BaseType, value, null);
            string valueStr = type == null ? value?.ToString() : TypeDescriptor.GetConverter(type).ConvertToInvariantString(value);
            IniHelper.SetValue(section, key, valueStr);
        }

        public static void SetValue(string key, object value)
        {
            if (string.IsNullOrEmpty(key)) return;
            var config = GetSection(null, key);
            if (!string.IsNullOrEmpty(config.Section))
                SetValue(config.Section, key, value);
        }

        #region 配置文件相关

        public static string ConfigPath { get; set; } = CommonString.DataPath;

        public static string ConfigName { get; set; } = "config.ini";

        public static string IniFileName => string.Format("{0}{1}", ConfigPath, ConfigName);

        #endregion

        #region 常规

        [Description(常规)] [Obfuscation] public static bool 开机启动 { get; set; } = true;

        [Description(常规)] [Obfuscation] public static bool 启动后打开主窗体 { get; set; } = true;

        [Description(常规)] [Obfuscation] public static bool 自动备份 { get; set; } = true;

        [Description(常规)] [Obfuscation] public static bool 窗体置顶 { get; set; }

        [Description(常规)] [Obfuscation] public static bool 注册右键菜单 { get; set; }

        [Description(常规)] [Obfuscation] public static Font 默认文字字体 { get; set; } = StaticValue.DefaultFont;

        [Description(常规)] [Obfuscation] public static Color 默认文字颜色 { get; set; } = StaticValue.DefaultForeColor;

        public static Color Get默认文字颜色()
        {
            return 夜间模式 ? (自动反色 ? CommonTheme.ReveseColor(默认文字颜色) : Color.FromArgb(245, 245, 245)) : 默认文字颜色;
        }

        [Description(常规)] [Obfuscation] public static Color 默认背景颜色 { get; set; } = StaticValue.DefaultBackColor;

        public static Color Get默认背景颜色()
        {
            return 夜间模式 ? (自动反色 ? Color.FromArgb(默认背景颜色.A, 255 - 默认背景颜色.R, 255 - 默认背景颜色.G, 255 - 默认背景颜色.B) : Color.FromArgb(54, 54, 54)) : 默认背景颜色;
        }

        [Description(常规)] [Obfuscation] public static bool 显示工具栏 { get; set; } = true;

        [Description(常规)] [Obfuscation] public static string 双击工具栏操作 { get; set; } = ToolDoubleClickEnum.显示主窗体.ToString();

        [Description(常规)] [Obfuscation] public static bool 显示系统托盘 { get; set; } = true;

        [Description(常规)] [Obfuscation] public static string 双击托盘操作 { get; set; } = ToolDoubleClickEnum.显示主窗体.ToString();

        [Description(常规)] [Obfuscation] public static string 用户名 { get; set; }

        [Description(常规)] [Obfuscation] public static string 密码 { get; set; }

        #endregion

        #region 识别

        [Description(识别)] [Obfuscation] public static bool 图片自动压缩 { get; set; } = true;

        [Description(识别)] [Obfuscation] public static bool 上传到图床 { get; set; } = true;

        [Description(识别)] [Obfuscation] public static string 识别引擎 { get; set; } = "不限";

        [Description(识别)] [Obfuscation] public static string 识别策略 { get; set; } = OcrModel.本地加网络.ToString();

        [Description(识别)] [Obfuscation] public static bool 启用本地识别 { get; set; }

        [Description(识别)] [Obfuscation] public static decimal 本地识别端口 { get; set; } = 8080;

        [Description(识别)] [Obfuscation] public static decimal 本地识别线程数 { get; set; } = 5;

        [Description(识别)] [Obfuscation] public static int 本地识别1 { get; set; }

        [Description(识别)] [Obfuscation] public static int 本地识别2 { get; set; }

        [Description(识别)] [Obfuscation] public static int 本地识别3 { get; set; }

        [Description(识别)] [Obfuscation] public static int 本地识别4 { get; set; }

        [Description(识别)] [Obfuscation] public static int 本地识别5 { get; set; }

        [Description(识别)] [Obfuscation] public static string 分段模式 { get; set; } = SpiltMode.自动分段.ToString();

        [Description(识别)] [Obfuscation] public static bool 图文模式 { get; set; } = true;

        [Description(识别)] [Obfuscation] public static bool 图片自动缩放 { get; set; } = true;

        [Description(识别)] [Obfuscation] public static bool 显示文字预览 { get; set; }

        [Description(识别)] [Obfuscation] public static bool 首行缩进 { get; set; }

        [Description(识别)] [Obfuscation] public static bool 自动对齐 { get; set; }

        [Description(识别)] [Obfuscation] public static bool 自动复制结果到粘贴板 { get; set; }

        [Description(识别)] [Obfuscation] public static string 复制模式 { get; set; } = CopyResultEnum.最快.ToString();

        [Description(识别)] [Obfuscation] public static bool 翻译时复制原文 { get; set; }

        [Description(识别)] [Obfuscation] public static string 竖排方向 { get; set; }

        [Description(识别)] [Obfuscation] public static bool 显示原文 { get; set; } = true;

        [Description(识别)] [Obfuscation] public static bool 划词翻译 { get; set; }

        [Description(识别)] [Obfuscation] public static string 源语言 { get; set; } = TransLanguageTypeEnum.自动.ToString();

        [Description(识别)] [Obfuscation] public static string 目标语言 { get; set; } = TransLanguageTypeEnum.英文.ToString();

        [Description(识别)] [Obfuscation] public static bool 自动全半角转换 { get; set; } = true;

        [Description(识别)] [Obfuscation] public static bool 自动空格 { get; set; } = true;

        [Description(识别)] [Obfuscation] public static bool 自动中英文标点 { get; set; } = true;

        [Description(识别)] [Obfuscation] public static bool 去除重复标点 { get; set; } = true;

        [Description(识别)] [Obfuscation] public static bool 识别时不显示主界面 { get; set; }

        #endregion

        #region 界面

        [Description(界面)] [Obfuscation] public static string 主题样式 { get; set; } = MetroColorStyle.绿色.ToString();

        [Description(界面)] [Obfuscation] public static bool 夜间模式 { get; set; }

        [Description(界面)] [Obfuscation] public static bool 自动反色 { get; set; }

        [Description(界面)] [Obfuscation] public static string 日夜间模式 { get; set; } = ThemeStyle.日间模式.ToString();

        [Description(界面)] [Obfuscation] public static string 加载动画 { get; set; } = LoadingType.蓝色箭头.ToString();

        [Description(界面)] [Obfuscation] public static string 搜索引擎 { get; set; } = "百度";

        [Description(界面)] [Obfuscation] public static string 朗读配置 { get; set; } = Speaker.可爱正太.ToString();

        [Description(界面)] [Obfuscation] public static string 图片预览背景 { get; set; } = ImageBoxGridDisplayMode.马赛克.ToString();

        [Description(界面)] [Obfuscation] public static Color 图片预览背景颜色 { get; set; } = Color.FromArgb(220, Color.Gainsboro);

        [Description(界面)] [Obfuscation] public static bool 跟随系统DPI缩放 { get; set; } = false;

        #region 工具栏

        [Description(界面)] [Obfuscation] public static bool 阴影效果 { get; set; } = true;

        [Description(界面)] [Obfuscation] public static bool 天气图标 { get; set; }

        [Description(界面)] [Obfuscation] public static string 天气图标样式 { get; set; } = WeatherIconType.QQ.ToString();

        public static string SetHeadImageByUrl(string url)
        {
            var result = string.Empty;
            try
            {
                var fileName = CommonString.HeadImagePath + Guid.NewGuid().ToString().Replace("-", "") + ".png";
                var bytes = WebClientExt.GetOneClient().DownloadData(url);
                File.WriteAllBytes(fileName, bytes);
                result = fileName;
            }
            catch
            {
            }

            return result;
        }

        [Description(界面)] [Obfuscation] public static decimal 工具栏阴影宽度 { get; set; } = 8;

        [Description(界面)] [Obfuscation] public static string 工具栏图片 { get; set; }

        [Description(界面)] [Obfuscation] public static string 工具栏图标尺寸 { get; set; } = "自动";

        [Description(界面)] [Obfuscation] public static string 工具栏位置 { get; set; }

        private const int NMax_Width = 100;
        public const int NMinWidth = 12;

        public static Size GetStrSize(string str)
        {
            var size = new Size();
            if (!string.IsNullOrEmpty(str) && !Equals(str, "自动"))
            {
                var lstInt = BoxUtil.GetListIntFromString(str);
                if (lstInt.Count == 2) size = new Size(lstInt[0], lstInt[1]);
            }

            return size;
        }

        public static Bitmap Get图标效果(Size size, string imgStr, bool isCircle, bool isShadow, decimal shadowWidth,
            bool isActive = false)
        {
            Bitmap image = null;
            if (!string.IsNullOrEmpty(imgStr)) image = ImageProcessHelper.LoadImage(imgStr);
            if (image == null)
                image = ImageProcessHelper.ResizeImage(Resources.ico, 72, 72, Color.Transparent, false, false,
                    shadowWidth);
            if (size.Width < NMinWidth || size.Height < NMinWidth || size.Width > NMax_Width ||
                size.Height > NMax_Width)
            {
                size.Width = image.Width;
                size.Height = image.Height;
            }

            size.Width = Math.Max(size.Width, NMinWidth);
            size.Width = Math.Min(size.Width, NMax_Width);
            size.Height = Math.Max(size.Height, NMinWidth);
            size.Height = Math.Min(size.Height, NMax_Width);

            shadowWidth = (int)Math.Min(Math.Max(1, shadowWidth), 20);
            image = ImageProcessHelper.ResizeImage(image, size.Width, size.Height, Color.Transparent, isCircle,
                isShadow, shadowWidth, isActive);
            return image;
        }

        [Description(界面)] [Obfuscation] public static bool 圆形图标 { get; set; } = true;

        #endregion

        #endregion

        #region 截屏

        [Description(截屏)] [Obfuscation] public static bool 截图动画效果 { get; set; } = true;

        [Description(截屏)] [Obfuscation] public static bool 显示放大镜 { get; set; } = true;

        [Description(截屏)] [Obfuscation] public static bool 圆形放大镜 { get; set; }

        [Description(截屏)] [Obfuscation] public static bool 显示全屏十字线 { get; set; }

        [Description(截屏)] [Obfuscation] public static bool 自动检测窗口 { get; set; } = true;

        [Description(截屏)] [Obfuscation] public static bool 自动检测窗口元素 { get; set; } = true;

        [Description(截屏)] [Obfuscation] public static Color 截图边框颜色 { get; set; } = Color.FromArgb(30, 144, 255);

        [Description(截屏)] [Obfuscation] public static decimal 截图边框宽度 { get; set; } = 3;

        [Description(截屏)] [Obfuscation] public static decimal 最大历史记录数量 { get; set; } = 20;

        [Description(截屏)] [Obfuscation] public static bool 自动保存 { get; set; } = true;

        [Description(截屏)] [Obfuscation] public static bool 显示Toast通知 { get; set; } = true;

        [Description(截屏)] [Obfuscation] public static bool 复制到粘贴板 { get; set; } = true;

        [Description(截屏)] [Obfuscation] public static string 截图文件名 { get; set; } = "截图_%n-%y-%r_%s-%f-%m.png";

        [Description(截屏)] [Obfuscation] public static string 截图文件保存路径 { get; set; } = CommonString.DefaultImagePath;

        [Description(截屏)] [Obfuscation] public static decimal 截图延时 { get; set; } = 3;

        /// <summary>
        /// 截图前滚动到顶部
        /// </summary>
        [Description(截屏)]
        [Obfuscation]
        public static string ScrollTopMethodBeforeCapture { get; set; } = ScrollingCaptureScrollTopMethod.自动尝试所有方法直到某方法生效.ToString();

        /// <summary>
        /// 滚动截屏方式
        /// </summary>
        [Description(截屏)]
        [Obfuscation]
        public static string ScrollMethod { get; set; } = ScrollingCaptureScrollMethod.自动尝试所有方法直到某方法生效.ToString();

        /// <summary>
        /// 开始滚动延时
        /// </summary>
        [Description(截屏)]
        [Obfuscation]
        public static decimal StartDelay { get; set; } = 500;

        /// <summary>
        /// 滚动过程中延时
        /// </summary>
        [Description(截屏)]
        [Obfuscation]
        public static decimal ScrollDelay { get; set; } = 500;

        /// <summary>
        /// 最大滚动次数
        /// </summary>
        [Description(截屏)]
        [Obfuscation]
        public static decimal MaximumScrollCount { get; set; } = 20;

        /// <summary>
        /// 自动检测滚动结束
        /// </summary>
        [Description(截屏)]
        [Obfuscation]
        public static bool 检测滚动结束 { get; set; } = true;

        /// <summary>
        /// 删除重复的图像
        /// </summary>
        [Description(截屏)]
        [Obfuscation]
        public static bool 删除重复的图像 { get; set; } = true;

        /// <summary>
        /// 猜测偏移并合并图像
        /// </summary>
        [Description(截屏)]
        [Obfuscation]
        public static bool 猜测偏移并合并图像 { get; set; } = true;

        #endregion

        #region 贴图

        [Description(贴图)] [Obfuscation] public static Color 贴图窗口阴影色 { get; set; } = StaticValue.ShadowActiveColor;

        [Description(贴图)] [Obfuscation] public static decimal 贴图窗口阴影宽度 { get; set; } = 10;

        [Description(贴图)] [Obfuscation] public static bool 截图贴图时使用截屏的位置 { get; set; } = true;

        [Description(贴图)] [Obfuscation] public static bool 闪烁 { get; set; } = true;

        [Description(贴图)] [Obfuscation] public static bool 激活窗口 { get; set; } = true;

        [Description(贴图)] [Obfuscation] public static bool 忽略Html格式以文字方式生成图片 { get; set; }

        [Description(贴图)] [Obfuscation] public static decimal 贴图页边距宽度 { get; set; } = 10;

        [Description(贴图)] [Obfuscation] public static decimal 贴图图片最大宽度 { get; set; } = 600;

        [Description(贴图)] [Obfuscation] public static bool 使用界面设置中的字体 { get; set; } = true;

        [Description(贴图)] [Obfuscation] public static Font 贴图文字字体 { get; set; } = StaticValue.DefaultFont;

        [Description(贴图)] [Obfuscation] public static Color 贴图文字颜色 { get; set; } = StaticValue.DefaultForeColor;

        [Description(贴图)] [Obfuscation] public static Color 贴图背景颜色 { get; set; } = StaticValue.DefaultBackColor;

        #endregion

        #region 工具

        [Description(工具)] [Obfuscation] public static bool 标尺置顶 { get; set; } = true;

        [Description(工具)] [Obfuscation] public static string 标尺默认主题 { get; set; } = ThemeOption.白色.ToString();

        [Description(工具)] [Obfuscation] public static string 标尺透明度 { get; set; } = "100";

        [Description(工具)] [Obfuscation] public static string 标尺计量单位 { get; set; } = MeasuringUnit.像素.ToString();

        [Description(工具)] [Obfuscation] public static string 取色器文字样式 { get; set; } = "RGB";

        [Description(工具)] [Obfuscation] public static bool HEX颜色值大写 { get; set; }

        #endregion

        #region 关于

        [Description(关于)] [Obfuscation] public static bool 启动时检查更新 { get; set; } = true;

        [Description(关于)] [Obfuscation] public static bool 体验Beta测试版 { get; set; }

        [Description(关于)] [Obfuscation] public static decimal 自动更新间隔 { get; set; } = 6;

        #endregion
    }

    public class ConfigDesc
    {
        public string Name { get; set; }

        public string Section { get; set; }

        public PropertyInfo PropertyInfo { get; set; }

        public object DefaultValue { get; set; }
    }
}