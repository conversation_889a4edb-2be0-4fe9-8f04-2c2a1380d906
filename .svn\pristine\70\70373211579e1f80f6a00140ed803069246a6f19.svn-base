using System.Collections.Generic;

namespace OCRTools
{
    internal class CommandChangeState : Command
    {
        private readonly List<DrawObject> _listBefore;
        private List<DrawObject> _listAfter;

        public CommandChangeState(GraphicsList graphicsList)
        {
            FillList(graphicsList, ref _listBefore);
        }

        public void NewState(GraphicsList graphicsList)
        {
            FillList(graphicsList, ref _listAfter);
        }

        public override void Undo(GraphicsList list)
        {
            ReplaceObjects(list, _listBefore);
        }

        public override void Redo(GraphicsList list)
        {
            ReplaceObjects(list, _listAfter);
        }

        private void ReplaceObjects(GraphicsList graphicsList, List<DrawObject> list)
        {
            for (var i = 0; i < graphicsList.Count; i++)
            {
                DrawObject drawObject = null;
                foreach (var item in list)
                    if (item.Id == graphicsList[i].Id)
                    {
                        drawObject = item;
                        break;
                    }

                if (drawObject != null) graphicsList.Replace(i, drawObject);
            }
        }

        private void FillList(GraphicsList graphicsList, ref List<DrawObject> listToFill)
        {
            listToFill = new List<DrawObject>();
            foreach (var item in graphicsList.Selection) listToFill.Add(item.Clone());
        }
    }
}