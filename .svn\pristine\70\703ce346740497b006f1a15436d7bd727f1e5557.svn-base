﻿//************************************************************************
//      https://github.com/yuzhengyang
//      author:     yuzhengyang
//      date:       2017.4.27 - 2017.8.25
//      desc:       工具描述
//      Copyright (c) yuzhengyang. All rights reserved.
//************************************************************************

using OCRTools.MyShadowForm;
using System;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools.Shadow
{
    public partial class ShadowForm : Form
    {
        private bool m_bMouseOnClose;
        private bool m_bMouseOnMax;
        private bool m_bMouseOnMin;

        private Rectangle m_rectClose;
        private Rectangle m_rectMax;
        private Rectangle m_rectMin;
        private Rectangle m_rectTitle;

        public Color ShadowColor = StaticValue.ShadowActiveColor;
        private ShadowFormSkin Skin;

        public ShadowForm()
        {
            InitializeComponent();
            CheckForIllegalCrossThreadCalls = false;
            SetStyle(
                ControlStyles.UserPaint |
                ControlStyles.AllPaintingInWmPaint |
                ControlStyles.OptimizedDoubleBuffer |
                ControlStyles.ResizeRedraw |
                ControlStyles.SupportsTransparentBackColor |
                ControlStyles.DoubleBuffer, true);
            BackColor = Color.FromArgb(0, 240, 240, 240);
            Padding = new Padding(2, 35, 2, 2);
            base.FormBorderStyle = FormBorderStyle.None;
            FormClosed += ShadowForm_FormClosed;
            FormClosing += ShadowForm_FormClosing;
        }

        internal Point MouseLocation { get; private set; }

        public bool IsSharkWindow { get; set; } = true;
        public bool IsForceActive { get; set; } = true;
        public int ShadowWidth { get; set; } = 10;

        public bool IsUseCustomerShadowColor { get; set; }

        public new bool MaximizeBox
        {
            get => base.MaximizeBox;
            set
            {
                if (value == base.MaximizeBox) return;
                base.MaximizeBox = value;
                CheckTitleBarSize();
            }
        }

        public new bool MinimizeBox
        {
            get => base.MinimizeBox;
            set
            {
                if (value == base.MinimizeBox) return;
                base.MinimizeBox = value;
                CheckTitleBarSize();
            }
        }

        public new FormBorderStyle FormBorderStyle
        {
            get => FormBorderStyle.None;
            set { }
        }

        public override string Text
        {
            get => base.Text;
            set
            {
                if (value == base.Text) return;
                base.Text = value;
                Invalidate(m_rectTitle);
            }
        }

        public bool IsShowTitle { get; set; } = true;

        public bool Sizeable { get; set; } = true;

        private void ShadowForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            Visibility(false, false);
        }

        private void ShadowForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            if (Skin != null && !Skin.IsDisposed)
            {
                Skin.Dispose();
                Skin = null;
            }
        }

        private void ShadowFormSkin_LostFocus(object sender, EventArgs e)
        {
            DrawShadow(StaticValue.ShadowNormalColor);
        }

        private void ShadowFormSkin_GotFocus(object sender, EventArgs e)
        {
            if (IsUseCustomerShadowColor)
            {
                ShadowColor = CommonSetting.贴图窗口阴影色;
                ShadowWidth = (int) Math.Max(CommonSetting.贴图窗口阴影宽度, 1);
            }

            DrawShadow(ShadowColor);
        }

        private void IrregularForm_Load(object sender, EventArgs e)
        {
            if (!DesignMode)
            {
                Skin = new ShadowFormSkin(this)
                {
                    BackColor = Color.Red
                }; //创建皮肤层
                Skin.Show(); //显示皮肤层

                ShadowShark();
                GotFocus += ShadowFormSkin_GotFocus;
                LostFocus += ShadowFormSkin_LostFocus;
            }
        }

        public void ShadowShark()
        {
            //ForceActivate();
            Task.Factory.StartNew(() =>
            {
                if (IsSharkWindow)
                    for (var i = 0; i < 7; i++)
                    {
                        if (Skin == null || Skin.IsDisposed || !Skin.Visible) return;
                        var color = Color.FromArgb(0, new Random(Guid.NewGuid().GetHashCode()).Next(0, 256),
                            new Random(Guid.NewGuid().GetHashCode()).Next(0, 256),
                            new Random(Guid.NewGuid().GetHashCode()).Next(0, 256));
                        Skin.DrawShadow(color);
                        Thread.Sleep(100);
                    }

                if (IsForceActive) this.ForceActivate();
                ShadowFormSkin_GotFocus(null, null);
            });
        }

        public void DrawShadow(Color color)
        {
            if (Skin != null)
            {
                Skin.Location = new Point(Left - ShadowWidth, Top - ShadowWidth);
                Skin.DrawShadow(color);
            }
        }

        private void ShadowForm_LocationChanged(object sender, EventArgs e)
        {
            DrawShadow(ShadowColor);
        }

        /// <summary>
        ///     窗体显示状态
        /// </summary>
        /// <param name="value"></param>
        public void Visibility(bool value, bool isOnlyShadow = true)
        {
            if (value)
            {
                if (!isOnlyShadow) Show();
                Skin.Show();
            }
            else
            {
                if (!isOnlyShadow) Hide();
                Skin.Hide();
            }
        }

        protected override void OnSizeChanged(EventArgs e)
        {
            DrawShadow(ShadowColor);
            CheckTitleBarSize();
            base.OnSizeChanged(e);
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            if (m_rectTitle.Contains(e.Location))
            {
                FormStyleAPI.ReleaseCapture();
                FormStyleAPI.SendMessage(Handle, Win32.WM_SYSCOMMAND, Win32.SC_MOVE + (int) Win32.HTCAPTION, 0);
            }

            base.OnMouseDown(e);
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            if (IsShowTitle)
            {
                CheckRectToReDraw(m_rectClose, e.Location, ref m_bMouseOnClose);
                if (MaximizeBox) CheckRectToReDraw(m_rectMax, e.Location, ref m_bMouseOnMax);
                if (MinimizeBox) CheckRectToReDraw(m_rectMin, e.Location, ref m_bMouseOnMin);
            }

            if (Cursor == Cursors.Default && e.Button == MouseButtons.Left && !m_rectTitle.Contains(e.Location))
            {
                FormStyleAPI.ReleaseCapture();
                FormStyleAPI.SendMessage(Handle, FormStyleAPI.WM_NCLBUTTONDOWN, FormStyleAPI.HTCAPTION, 0);
            }

            base.OnMouseMove(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            m_bMouseOnClose = m_bMouseOnMax = m_bMouseOnMin = false;
            Invalidate();
            base.OnMouseLeave(e);
        }

        protected override void OnClick(EventArgs e)
        {
            if (m_bMouseOnClose) Close();
            if (m_bMouseOnMax)
                WindowState = WindowState != FormWindowState.Maximized
                    ? FormWindowState.Maximized
                    : FormWindowState.Normal;
            if (m_bMouseOnMin) WindowState = FormWindowState.Minimized;
            base.OnClick(e);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            var g = e.Graphics;
            if (IsShowTitle) OnDrawTitle(g);
            g.DrawRectangle(Pens.Gray, 0, 0, Width - 1, Height - 1);
            base.OnPaint(e);
        }

        protected virtual void OnDrawTitle(Graphics g)
        {
            var sf = new StringFormat
            {
                LineAlignment = StringAlignment.Center
            };
            g.FillRectangle(Brushes.White, 0, 0, Width, 35);
            g.DrawIcon(Icon, new Rectangle(6, 6, 23, 23));
            g.DrawString(Text, Font, Brushes.Black, m_rectTitle, sf);
            if (m_rectClose != Rectangle.Empty)
            {
                g.DrawLine(Pens.Gray, m_rectClose.Right - 1, 0, m_rectClose.Right - 1, 34);
                if (m_bMouseOnClose) g.FillRectangle(Brushes.Red, m_rectClose);
                g.DrawLine(Pens.Gray, m_rectClose.X + 14, 14, m_rectClose.X + 21, 21);
                g.DrawLine(Pens.Gray, m_rectClose.X + 14, 21, m_rectClose.X + 21, 14);
            }

            if (m_rectMax != Rectangle.Empty)
            {
                g.DrawLine(Pens.Gray, m_rectMax.Right - 1, 0, m_rectMax.Right - 1, 34);
                if (m_bMouseOnMax) g.FillRectangle(Brushes.LightGray, m_rectMax);
                g.DrawRectangle(Pens.Gray, m_rectMax.X + 14, 14, 7, 7);
            }

            if (m_rectMin != Rectangle.Empty)
            {
                g.DrawLine(Pens.Gray, m_rectMin.Right - 1, 0, m_rectMin.Right - 1, 34);
                if (m_bMouseOnMin) g.FillRectangle(Brushes.LightGray, m_rectMin);
                g.DrawLine(Pens.Gray, m_rectMin.X + 14, 21, m_rectMin.X + 21, 21);
            }
        }

        protected override void WndProc(ref Message m)
        {
            switch ((uint) m.Msg)
            {
                case Win32.WM_NCHITTEST:
                    m.Result = OnHitTest(m);
                    return;
                case Win32.WM_GETMINMAXINFO: //在无边窗体时确定窗体最大、最小、最大化尺寸
                    var rectArea = Screen.GetWorkingArea(MousePosition);
                    var rectBounds = Screen.GetBounds(MousePosition);
                    var stMinMaxInfo = (Win32.MINMAXINFO) m.GetLParam(typeof(Win32.MINMAXINFO));
                    stMinMaxInfo.ptMinTrackSize.X = 1;
                    stMinMaxInfo.ptMinTrackSize.Y = 1;
                    //窗体最大化坐标及宽高
                    stMinMaxInfo.ptMaxPosition.X = rectArea.X - rectBounds.X;
                    stMinMaxInfo.ptMaxPosition.Y = rectArea.Y - rectBounds.Y;
                    stMinMaxInfo.ptMaxSize.X = rectArea.Width;
                    stMinMaxInfo.ptMaxSize.Y = rectArea.Height;
                    Marshal.StructureToPtr(stMinMaxInfo, m.LParam, true);
                    return;
            }

            base.WndProc(ref m);
        }

        protected virtual IntPtr OnHitTest(Message msg)
        {
            var pt = new Point((int) msg.LParam);
            pt.Offset(-Left, -Top);
            if (Sizeable)
            {
                if (pt.X < 5 && pt.Y < 5)
                    return (IntPtr) Win32.HTTOPLEFT;
                if (pt.X > Width - 5 && pt.Y < 5)
                    return (IntPtr) Win32.HTTOPRIGHT;
                if (pt.X < 5 && pt.Y > Height - 5)
                    return (IntPtr) Win32.HTBOTTOMLEFT;
                if (pt.X > Width - 5 && pt.Y > Height - 5)
                    return (IntPtr) Win32.HTBOTTOMRIGHT;

                if (pt.X < 5) return (IntPtr) Win32.HTLEFT;
                if (pt.Y < 5) return (IntPtr) Win32.HTTOP;
                if (pt.X > Width - 5) return (IntPtr) Win32.HTRIGHT;
                if (pt.Y > Height - 5) return (IntPtr) Win32.HTBOTTOM;
            }

            if (IsShowTitle && m_rectTitle.Contains(pt)) return (IntPtr) Win32.HTCAPTION;
            return (IntPtr) Win32.HTCLIENT;
        }

        private void CheckTitleBarSize()
        {
            if (IsShowTitle)
            {
                m_rectClose = new Rectangle(Width - 35, 0, 35, 35);
                if (MaximizeBox)
                    m_rectMax = new Rectangle(Width - 70, 0, 35, 35);
                else
                    m_rectMax = Rectangle.Empty;
                if (MinimizeBox)
                    m_rectMin = new Rectangle(Width - 70 - m_rectMax.Width, 0, 35, 35);
                else
                    m_rectMin = Rectangle.Empty;
                m_rectTitle = new Rectangle(35, 0, Width - 70 - m_rectMax.Width - m_rectMin.Width, 35);
            }
            else
            {
                m_rectClose = Rectangle.Empty;
                m_rectMax = Rectangle.Empty;
                m_rectMin = Rectangle.Empty;
                m_rectTitle = Rectangle.Empty;
            }
        }

        private void CheckRectToReDraw(Rectangle rect, Point pt, ref bool bFlag)
        {
            if (rect.Contains(pt))
            {
                if (!bFlag)
                {
                    bFlag = true;
                    Invalidate(rect);
                }
            }
            else
            {
                if (bFlag)
                {
                    bFlag = false;
                    Invalidate(rect);
                }
            }
        }
    }
}