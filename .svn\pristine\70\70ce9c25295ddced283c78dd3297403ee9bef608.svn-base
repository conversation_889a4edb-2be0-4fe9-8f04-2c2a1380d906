using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class StylesPattern : BasePattern
    {
        public static readonly AutomationProperty StyleIdProperty = StylesPatternIdentifiers.StyleIdProperty;

        public static readonly AutomationPattern Pattern = StylesPatternIdentifiers.Pattern;


        private StylesPattern(AutomationElement el, IUIAutomationStylesPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        public static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new StylesPattern(el, (IUIAutomationStylesPattern)pattern, cached);
        }
    }
}