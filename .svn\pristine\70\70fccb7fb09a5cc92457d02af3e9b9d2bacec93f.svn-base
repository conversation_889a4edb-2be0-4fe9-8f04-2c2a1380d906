namespace UtfUnknown.Core.Models.MultiByte.Chinese
{
    public class Iso2022CnSmModel : StateMachineModel
    {
        private static readonly int[] Iso2022CnCls =
        {
            BitPackage.Pack4Bits(2, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 1, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 3, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 4, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2)
        };

        private static readonly int[] Iso2022CnSt =
        {
            BitPackage.Pack4Bits(0, 3, 1, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 1, 1, 1, 4, 1),
            BitPackage.Pack4Bits(1, 1, 1, 2, 1, 1, 1, 1),
            BitPackage.Pack4Bits(5, 6, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 2, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 2, 1, 0)
        };

        private static readonly int[] Iso2022CnCharLenTable = new int[9];

        public Iso2022CnSmModel()
            : base(
                new BitPackage(BitPackage.indexShift4Bits, BitPackage.shiftMask4Bits, BitPackage.bitShift4Bits,
                    BitPackage.unitMask4Bits, Iso2022CnCls), 9,
                new BitPackage(BitPackage.indexShift4Bits, BitPackage.shiftMask4Bits, BitPackage.bitShift4Bits,
                    BitPackage.unitMask4Bits, Iso2022CnSt), Iso2022CnCharLenTable, "iso-2022-cn")
        {
        }
    }
}