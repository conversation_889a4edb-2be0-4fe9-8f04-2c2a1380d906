using MetroFramework;
using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Common.Hook;
using OCRTools.Common.ImageLib;
using OCRTools.NewForms;
using OCRTools.Properties;
using OCRTools.Shadow;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Net.NetworkInformation;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Windows.Automation;
using System.Windows.Forms;

// ReSharper disable All

namespace OCRTools
{
    public partial class FrmMain : MetroForm
    {
        private const string StrProcessOcr = "正在识别，请稍后……";
        private const string StrProcessLater = "正在识别中，请稍后重试！";
        private const string StrUploadImg = "正在上传图片，请稍后……";

        private const int MinFixAreaWidth = 10;

        public static FormTool FrmTool = new FormTool() { IsImage = true };

        private static readonly int minWidth = 300;
        private static readonly int minHeight = 150;

        internal static Action<List<string>, string, OcrType?, ProcessBy, string> DragDropEventDelegate;

        internal static Action<OcrType?, ProcessBy, Image, string> RecByImageDelegate;

        internal static List<SearchEngine> LstSearchEngine = new List<SearchEngine>();

        private static TransLanguageTypeEnum CurrentTranslateFrom;

        private static TransLanguageTypeEnum CurrentTranslateTo;

        private static OcrType NowOcrType;
        private static OcrGroupType NowOcrGroupType;
        private static BiaoDianMode NowBiaoDianMode;

        private static bool IsFromLeftToRight;
        private static bool IsFromTopToDown = true;

        private readonly BlockingCollection<OcrContent> OcrResultPool = new BlockingCollection<OcrContent>();

        private Speaker CurrentSpeaker;

        private SpiltMode CurrentSpiltModel;

        private bool IsCopyTrans;

        private bool IsOcrForSearch;

        private bool IsShowOldContent;

        private ObjectForScriptingHelper JsHelper;

        private LoadingType nowLoadingType;

        Screenshot screenshot = new Screenshot();

        public FrmMain()
        {
            InitializeComponent();

            Opacity = 0;
            ShadowType = CommonString.CommonShadowType;
            CheckForIllegalCrossThreadCalls = false;
            FormClosing += FrmMain_FormClosing;
            msManager.StyleChange += MsManager_StyleChange;
            msManager.ThemeChange += MsManager_ThemeChange;
            SetStyle(
                ControlStyles.UserPaint | ControlStyles.DoubleBuffer | ControlStyles.OptimizedDoubleBuffer |
                ControlStyles.AllPaintingInWmPaint, true);
            DoubleBuffered = true;
            CommonMethod.EnableDoubleBuffering(this);
            Width = Width.DpiValue();
            Height = Height.DpiValue();
            SizeChanged += FrmMain_SizeChanged;

            notifyMain.Icon = Icon;
            notifyMain.ContextMenuStrip = cmsNotify;
            cmsNotify.Opening += CmsNotify_Opening;
            content.MenuStrip = cmsNotify;
            ImgLogHelper.TickEvent += (a, b) => { CheckLoginStatus(); };

            FrmTool.Icon = Icon;
            FrmTool.ContextMenuStrip = cmsNotify;
            FrmTool.MenuStart += FrmTool_MenuStart;
            FrmTool.MouseDoubleClick += FrmTool_MouseDoubleClick;


            DragDropEventDelegate = (lstStr, fileExt, ocrType, processBy, fileIdentity) =>
            {
                if (!ocrType.HasValue) ocrType = NowOcrType;
                var fileName = lstStr[0];
                ProcessFile(ocrType.Value, processBy, fileName, fileIdentity, fileExt);
            };
            RecByImageDelegate = (ocrType, processBy, img, url) =>
            {
                ProcessByImage(NowOcrType, processBy, img, false, url);
            };

            this.ControlUseDrop();
            tbMain.ControlUseDrop();
            content.SetDragDrop();
            FrmTool.SetDragDrop();

            content.TxtKeyDownEventDelegate = TxtContent_KeyDown;

            tsmAutoTrans = new ToolStripCheckBoxControl
            {
                Checked = false,
                ForeColor = Color.Black,
                Name = "tsmAutoTrans",
                Size = new Size(88, 29),
                Text = "划词翻译",
                ToolTipText = "鼠标拖选或者双击自动取词",
                Visible = false
            };
            stateStip.Items.Add(tsmAutoTrans);
            tsmAutoTrans.CheckedChanged += TsmAutoTrans_CheckedChanged;

            ////tsmVersion.Text = string.Format("版本:V{0}", Assembly.GetExecutingAssembly().GetName().Version.ToString());
            //tsmPicType.DropDownItems.Clear();
            //foreach (ImageProcessType v in Enum.GetValues(typeof(ImageProcessType)))
            //{
            //    tsmPicType.DropDownItems.Add(new ToolStripMenuItem() { Text = v.ToString(), Tag = v.GetHashCode(), Font = tsmContentType.Font });
            //}

            tsmTransFrom.DropDownItems.Clear();
            tsmTransTo.DropDownItems.Clear();
            foreach (TransLanguageTypeEnum v in Enum.GetValues(typeof(TransLanguageTypeEnum)))
            {
                tsmTransFrom.DropDownItems.Add(new ToolStripMenuItem
                {
                    Text = v.ToString(),
                    Tag = v.GetHashCode(),
                    Font = tsmContentType.Font,
                    Image = Resources.ResourceManager.GetObject(v.ToString()) as Bitmap
                });
                if (!v.Equals(TransLanguageTypeEnum.自动))
                    tsmTransTo.DropDownItems.Add(new ToolStripMenuItem
                    {
                        Text = v.ToString(),
                        Tag = v.GetHashCode(),
                        Font = tsmContentType.Font,
                        Image = Resources.ResourceManager.GetObject(v.ToString()) as Bitmap
                    });
            }

            btnVoice.DropDownItems.Clear();

            CurrentSpeaker = (Speaker)Enum.Parse(typeof(Speaker), CommonSetting.朗读配置);
            foreach (Speaker v in Enum.GetValues(typeof(Speaker)))
            {
                var item = new ToolStripMenuItem
                { Text = v.ToString(), Tag = v.GetHashCode(), Font = tsmContentType.Font };
                item.Click += SpeakerItem_Click;
                if (v.Equals(CurrentSpeaker)) item.Checked = true;
                btnVoice.DropDownItems.Add(item);
            }

            tsmAutoTrans.SetStyleManager(msManager);
            tsmShowOldContent.SetStyleManager(msManager);

            Application.ApplicationExit += Application_ApplicationExit;

            //MessageBox.Show(str);
        }

        private void FrmTool_MenuStart(object sender, EventArgs e)
        {

        }

        internal LoadingType NowLoadingType
        {
            get => nowLoadingType;
            set
            {
                nowLoadingType = value;
                ucLoading1.SetLoadingType(NowLoadingType);
            }
        }

        internal static SearchEngine NowSearchEngine { get; set; }
        private static DisplayModel NowDisplayMode { get; set; }

        public bool IsPlaying { get; set; }

        private void ProcessByImage(OcrType ocrType, ProcessBy processBy, Image image, bool isCapture,
            string tagUrl = null, bool isSearch = false)
        {
            var fileName = SaveImage(image, isCapture);
            image = ProcessImgSize(image);
            SetPicImage(image, tagUrl);
            OcrPoolProcess.ProcessByImage(ocrType, NowOcrGroupType, NowBiaoDianMode, IsFromLeftToRight, IsFromTopToDown,
                CurrentTranslateFrom, CurrentTranslateTo, image, tagUrl, CommonString.StrDefaultImgType, fileName,
                false, isSearch, processBy, null);
        }

        private string SaveImage(Image image, bool isCapture)
        {
            if (CommonSetting.自动保存)
            {
                var fileName = image.SaveFileWithOutConfirm(null, !isCapture);
                if (isCapture && CommonSetting.显示Toast通知) CommonMethod.ShowCaptureNotificationTip(fileName);
                return fileName;
            }

            return string.Empty;
        }

        private Image ProcessImgSize(Image image)
        {
            if (image != null && (image.Width < minWidth || image.Height < minHeight))
                using (var bitmap = new Bitmap(image.Width > minWidth ? image.Width : minWidth,
                    image.Height > minHeight ? image.Height : minHeight))
                {
                    using (var graphics = Graphics.FromImage(bitmap))
                    {
                        graphics.Clear(Color.Transparent);
                        // 设置画布的描绘质量         
                        graphics.CompositingQuality = CompositingQuality.HighQuality;
                        graphics.SmoothingMode = SmoothingMode.HighQuality;
                        graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                        graphics.DrawImage(image, (bitmap.Width - image.Width) / 2, (bitmap.Height - image.Height) / 2,
                            image.Width, image.Height);
                        graphics.Save();
                        graphics.Dispose();
                    }

                    image = new Bitmap(bitmap);
                }

            return image;
        }

        private void FrmMain_SizeChanged(object sender, EventArgs e)
        {
            BindUserImg();
        }

        private void ShowMsgControl()
        {
            CommonMsg.ShowToWindow(this, new Point(8, (Height - content.Top - 40) / 2), true);
        }

        private void FrmMain_Shown(object sender, EventArgs e)
        {
            StaticValue.Handles.Add(Handle);
            if (!CommonSetting.启动后打开主窗体) Hide();
            Opacity = 1;
            ShowMsgControl();
        }

        private void TsmAutoTrans_CheckedChanged(object sender, EventArgs e)
        {
            IsCopyTrans = tsmAutoTrans.Checked;
            if (IsCopyTrans)
            {
                HookManager.MouseSelected += mouseHook_MouseSelected;
                HookManager.MouseUp += HookManager_MouseClick;
                HookManager.MouseDoubleClick += mouseHook_DoubleClick;
                HookManager.SubscribedToGlobalMouseEvents();
            }
            else
            {
                HookManager.MouseSelected -= mouseHook_MouseSelected;
                HookManager.MouseUp -= HookManager_MouseClick;
                HookManager.MouseDoubleClick -= mouseHook_DoubleClick;
                HookManager.UnsunscribeFromGlobalMouseEvents();
                HookManager_MouseClick(null, null);
            }

            CommonSetting.SetValue("划词翻译", IsCopyTrans);
        }

        private void Application_ApplicationExit(object sender, EventArgs e)
        {
            HookManager.UnsunscribeFromGlobalMouseEvents();
        }

        private void TxtContent_KeyDown(object sender, KeyEventArgs e)
        {
            if (!e.Control || e.KeyCode != Keys.V) return;
            try
            {
                var img = ClipboardService.GetImage();
                if (img != null)
                {
                    ProcessByImage(NowOcrType, ProcessBy.主界面, img, false);
                }
                else
                {
                    var imgFile = ClipboardService.GetOneFile();
                    if (!string.IsNullOrEmpty(imgFile))
                    {
                        ProcessFile(NowOcrType, ProcessBy.主界面, imgFile, null, null);
                    }
                    else if (sender != null && sender is RichTextBox)
                    {
                        var txt = ClipboardService.GetText(true);
                        if (!string.IsNullOrEmpty(txt))
                            (sender as RichTextBox).AppendText(txt);
                    }
                }
            }
            catch
            {
                // ignored
            }

            e.Handled = true;
        }

        private void FrmMain_Load(object sender, EventArgs e)
        {

            InitScreenShot();
            InitAllConfig();

            ucLoading1.InitLoading(tbMain.Size, tbMain.Location);
            ucLoading1.Anchor = tbMain.Anchor;
            //ucLoading1.ShowLoading("正在识别，请稍后……");

            Task.Factory.StartNew(() =>
            {
                InitNetWorkInfo();
                InitOthersSync();
                NetWorkChangeEvent();
                CommonUpdate.InitUpdate();
                CommonWeather.InitWeather();

                InitSearchEngines();
                SetSearchEngine();
            });
        }

        private void InitOthersSync()
        {
            OcrProcessThread();
            OcrResultProcessThread();

            wbVoice.DocumentText = "";
            JsHelper = new ObjectForScriptingHelper(this);
            wbVoice.ObjectForScripting = JsHelper;

            //try
            //{
            //    CommonMethod.CheckShortcut();
            //}
            //catch (Exception)
            //{
            //    CommonMethod.ShowHelpMsg("创建快捷方式到桌面失败！", 5000);
            //}
        }

        private void ProcessFile(OcrType ocrType, ProcessBy processBy, string fileName, string fileIdentity,
            string fileExt)
        {
            if (Program.NowUser == null || string.IsNullOrEmpty(fileName))
            {
                CommonMethod.ShowHelpMsg("未登录用户不支持文件识别，请先注册！");
                return;
            }

            fileExt = CommonMethod.GetFileExt(fileName, fileExt);

            if (!CommonString.LstCanProcessFilesExt.Contains(fileExt))
            {
                MessageBox.Show(this,
                    "请选择以下类型的文件！\n【图片】" + string.Join("、", CommonString.LstCanProcessImageFilesExt) + "\n【文档】" +
                    string.Join("、", CommonString.LstCanProcessDocFilesExt), "温馨提示", MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
                return;
            }

            if (CommonString.LstCanProcessImageFilesExt.Contains(fileExt) &&
                Program.NowUser?.IsSupportImageFile == false)
            {
                CommonMethod.ShowHelpMsg("当前账户不支持图片文件识别！");
                return;
            }

            if (ocrType.Equals(OcrType.翻译) && Program.NowUser?.IsSupportTranslate == false)
            {
                CommonMethod.ShowHelpMsg("当前账户不支持翻译功能！");
                return;
            }

            if (CommonString.LstCanProcessDocFilesExt.Contains(fileExt) && !Equals(fileExt, "txt") &&
                Program.NowUser?.IsSupportDocFile == false)
            {
                CommonMethod.ShowHelpMsg("当前账户不支持文档文件识别！");
                return;
            }

            if (fileName.EndsWith(".pdf"))
            {
                using (var pdfProcess = new FormPdfProcess()
                { Icon = Icon, Theme = msManager.Theme, Style = msManager.Style, StyleManager = msManager })
                {
                    pdfProcess.TopMost = true;
                    if (pdfProcess.ShowDialog(this) != DialogResult.OK)
                    {
                        return;
                    }
                    if (pdfProcess.IsProcessByImage)
                    {
                        ProcessByPDFFile(fileName, pdfProcess.IsSpiltImage);
                        return;
                    }
                }
            }
            var imgUrl = "";
            var bitmap = OcrPoolProcess.ProcessByFile(ocrType, NowOcrGroupType, NowBiaoDianMode, IsFromLeftToRight,
                IsFromTopToDown, CurrentTranslateFrom, CurrentTranslateTo, fileName, imgUrl, fileExt, false, false,
                processBy, fileIdentity);
            if (Equals(processBy, ProcessBy.主界面)) SetPicImage(bitmap, imgUrl);
        }

        private void FrmMain_FormClosing(object sender, FormClosingEventArgs e)
        {
            WindowState = FormWindowState.Minimized;
            Visible = false;
            e.Cancel = true;
        }

        private void InitAllConfig()
        {
            FrmTool.TopMost = true;
            FrmTool.Visible = CommonSetting.显示工具栏;

            NowDisplayMode = CommonSetting.图文模式 ? DisplayModel.图文模式 : DisplayModel.文字模式;
            NowOcrGroupType = (OcrGroupType)Enum.Parse(typeof(OcrGroupType), CommonSetting.识别引擎);
            NowBiaoDianMode = (BiaoDianMode)Enum.Parse(typeof(BiaoDianMode), CommonSetting.标点模式);

            tsmBiaoDian.Image = Resources.ResourceManager.GetObject(NowBiaoDianMode.ToString() + "标点") as Bitmap;
            tsmBiaoDian.Text = NowBiaoDianMode + "标点";

            CurrentSpiltModel = (SpiltMode)Enum.Parse(typeof(SpiltMode), CommonSetting.分段模式);
            NowLoadingType = (LoadingType)Enum.Parse(typeof(LoadingType), CommonSetting.加载动画);

            InitSearchMenu();
            ProcessForbidControls();

            InitOcrGroupItems();
            InitSpiltModel();
            InitBiaoDian();
            BindSpiltModel(CurrentSpiltModel);

            IsShowOldContent = CommonSetting.显示原文;
            tsmShowOldContent.Checked = IsShowOldContent;
            IsCopyTrans = CommonSetting.划词翻译;
            tsmAutoTrans.Checked = IsCopyTrans;

            RefreshUcContent();
            SetTheme();

            SetTopMost(CommonSetting.窗体置顶);

            InitItemTypeByValue(tsmTransFrom, CommonSetting.源语言);
            InitItemTypeByValue(tsmTransTo, CommonSetting.目标语言);
            InitItemTypeByValue(tsmContentType, OcrType.文本.ToString());
            InitItemTypeByValue(tsmLeftToRight, CommonSetting.从左到右.ToString());
            InitItemTypeByValue(tsmTopToDown, CommonSetting.从上到下.ToString());

            UpdatePicViewModel(NowDisplayMode);
        }

        private void InitSearchEngines()
        {
            LstSearchEngine.Add(new SearchEngine
            {
                Name = "Doge",
                Url = "https://www.dogedoge.com/results?q={0}"
            });
            LstSearchEngine.Add(new SearchEngine
            {
                Name = "百度",
                Url = "https://www.baidu.com/s?wd={0}"
            });
            LstSearchEngine.Add(new SearchEngine
            {
                Name = "谷歌",
                Url = "https://www.google.com/search?q={0}"
            });
            LstSearchEngine.Add(new SearchEngine
            {
                Name = "必应",
                Url = "https://cn.bing.com/search?q={0}"
            });
            LstSearchEngine.Add(new SearchEngine
            {
                Name = "360",
                Url = "https://www.so.com/s?q={0}"
            });
            LstSearchEngine.Add(new SearchEngine
            {
                Name = "神马",
                Url = "https://m.sm.cn/s?q={0}"
            });
            var lstTmp = OcrHelper.GetGoogleUrl();
            LstSearchEngine.AddRange(lstTmp);
        }

        private void SetSearchEngine()
        {
            NowSearchEngine = LstSearchEngine.FirstOrDefault(p => p.Name.Equals(CommonSetting.搜索引擎));
            if (NowSearchEngine == null) NowSearchEngine = LstSearchEngine.FirstOrDefault();
        }

        private void RefreshUcContent()
        {
            var lstControl = GetNowBindUserControls();
            lstControl.ForEach(p => { p.RefreshStyle(); });
        }

        private void SetTheme()
        {
            var style = (MetroColorStyle)Enum.Parse(typeof(MetroColorStyle), CommonSetting.主题样式);
            msManager.Style = style;
            msManager.Theme = CommonSetting.深色模式 ? MetroThemeStyle.Dark : MetroThemeStyle.Light;
        }

        private void MsManager_ThemeChange(object sender, EventArgs e)
        {
            CommonString.IsDarkModel = msManager.Theme == MetroThemeStyle.Dark;
            tsmContentType.Image = ProcessImage(Resources.ResourceManager.GetObject(NowOcrType.ToString()) as Bitmap);
            tsmPicViewModel.Image =
                ProcessImage(Resources.ResourceManager.GetObject(NowDisplayMode.ToString()) as Bitmap);
        }

        private void MsManager_StyleChange(object sender, EventArgs e)
        {
        }

        private Image ProcessImage(Image image)
        {
            if (image == null) return null;
            if (CommonString.IsDarkModel)
                return Inverse(new Bitmap(image));
            return image;
        }

        /// <summary>
        ///     反色处理
        /// </summary>
        private Image Inverse(Bitmap bmp)
        {
            var srcdat = bmp.LockBits(new Rectangle(Point.Empty, bmp.Size), ImageLockMode.ReadWrite,
                PixelFormat.Format24bppRgb); // 锁定位图
            unsafe // 不安全代码
            {
                var pix = (byte*)srcdat.Scan0; // 像素首地址
                for (var i = 0; i < srcdat.Stride * srcdat.Height; i++)
                {
                    if (pix[i] == 0 || pix[i] == 255) continue;
                    pix[i] = (byte)(255 - pix[i]);
                }

                bmp.UnlockBits(srcdat); // 解锁
                return bmp;
            }
        }

        private void FrmTool_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            var strOperate = (ToolDoubleClickEnum)Enum.Parse(typeof(ToolDoubleClickEnum), CommonSetting.双击工具栏操作);
            switch (strOperate)
            {
                case ToolDoubleClickEnum.显示主窗体:
                    ShowWindow();
                    break;
                case ToolDoubleClickEnum.不做任何操作:
                    break;
                default:
                    截图识别ToolStripMenuItem_Click(sender, e);
                    break;
            }
        }

        private void CmsNotify_Opening(object sender, CancelEventArgs e)
        {
            tsmShowTool.Visible = false;
            if (sender != null)
            {
                tsmTrans.Visible = false;
                tsmSearch.Visible = false;
                tsmCopyTxt.Visible = false;
                tsmExportTxt.Visible = false;
                tsmExportExcel.Visible = false;
                tsmSpiltExport.Visible = false;

                tsmToolImage.Visible = false;

                tsmShowMain.Visible = !Visible;
                tsmShowMain.Text = (!Visible ? "显示" : "隐藏") + "窗体";
                tsmOcrGroupSpilt.Visible = true;

                var ctrl = (sender as ContextMenuStrip).SourceControl;
                if (ctrl is null || ctrl is Panel && ctrl.Name == "")
                {
                    tsmShowTool.Visible = true;
                    tsmShowTool.Text = (!FrmTool.Visible ? "显示" : "隐藏") + "工具栏";
                    //tsmToolImage.Visible = true;
                    //tsmToolImage.Text = ((!FrmTool.TopMost) ? "" : "取消") + "置顶工具栏";
                    //tsmTopWindow.Visible = true;
                    //tsmTopWindow.Text = ((!TopMost) ? "" : "取消") + "置顶窗体";
                }
                else if (ctrl is Form)
                {
                    tsmShowTool.Visible = true;
                    tsmShowTool.Text = (!FrmTool.Visible ? "显示" : "隐藏") + "工具栏";
                    tsmToolImage.Visible = true;
                }
                else
                {
                    tsmOcrGroupSpilt.Visible = !Visible;
                    if (ctrl is RichTextBox || ctrl is PanelPictureView)
                    {
                        if (ctrl is RichTextBox
                            && (ctrl as RichTextBox).SelectedText.Length > 0)
                        {
                            tsmSearch.Visible = true;
                            tsmTrans.Visible = Program.NowUser?.IsSupportTranslate == true;
                        }

                        tsmCopyTxt.Visible = true;
                        tsmExportTxt.Visible = true;
                        tsmSpiltExport.Visible = true;
                    }
                    else if (ctrl is DataGridViewEx)
                    {
                        tsmExportExcel.Visible = true;
                        tsmSpiltExport.Visible = true;
                    }
                }
            }
        }

        private void SetTopMost(bool result)
        {
            TopMost = result;
            tipMain.SetToolTip(pnlTop, TopMost ? "取消置顶" : "置顶窗体");
            pnlTop.BackgroundImage = TopMost ? Resources.top : Resources.untop;
        }

        private bool InitItemTypeByValue(ToolStripDropDownButton toolStripDropDownButton, string objValue)
        {
            if (string.IsNullOrEmpty(objValue)) return false;
            var result = false;
            foreach (ToolStripDropDownItem item in toolStripDropDownButton.DropDownItems)
                if (item.Text.Equals(objValue) || item.Tag?.ToString().Equals(objValue) == true)
                {
                    result = true;
                    tsmDDL_DropDownItemClicked(toolStripDropDownButton, new ToolStripItemClickedEventArgs(item));
                    break;
                }

            return result;
        }

        private List<UcContent> GetNowBindUserControls()
        {
            var lstUcContent = new List<UcContent>();
            foreach (TabPage tab in tbMain.TabPages)
                foreach (var ctrl in tab.Controls)
                    if (ctrl is UcContent ucContent)
                    {
                        lstUcContent.Add(ucContent);
                        break;
                    }

            return lstUcContent;
        }

        private void SetUcContentSpiltMode(SpiltMode spiltMode)
        {
            var lstControl = GetNowBindUserControls();
            lstControl.ForEach(p => { p.SpiltModel = spiltMode; });
        }

        private void SetUcContentTranslateMode(bool showOldContent)
        {
            var lstControl = GetNowBindUserControls();
            lstControl.ForEach(p => { p.IsShowOldContent = showOldContent; });
        }

        //private void SetUcContentOcrType(OCRType ocrType)
        //{
        //    var lstControl = GetNowBindUserControls();
        //    //lstControl.ForEach(p =>
        //    //{
        //    //    p.OcrType = ocrType;
        //    //});
        //}

        private void SetUcContentBiaoDianMode(BiaoDianMode biaoDianModel)
        {
            var lstControl = GetNowBindUserControls();
            lstControl.ForEach(p => { p.BiaoDianMode = biaoDianModel; });
        }

        private void SetUcContentDisplayMode(DisplayModel model)
        {
            var lstControl = GetNowBindUserControls();
            lstControl.ForEach(p => { p.NowDisplayMode = model; });
        }

        private void tsmDDL_DropDownItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            var item = e.ClickedItem;
            if (item == null || item.Tag == null) return;
            ToolStripDropDownButton ddlBtn = null;
            string strOpType;
            if (sender is string)
            {
                strOpType = sender?.ToString();
            }
            else
            {
                ddlBtn = sender as ToolStripDropDownButton;
                strOpType = ddlBtn?.Tag?.ToString();
            }

            if (string.IsNullOrEmpty(strOpType)) return;
            switch (strOpType)
            {
                case "识别方式":
                    ddlBtn.Text = item.Text;
                    ddlBtn.Image = ProcessImage(item.Image);
                    var selectedType = (OcrType)BoxUtil.GetInt32FromObject(item.Tag?.ToString());
                    if (!selectedType.Equals(NowOcrType))
                    {
                        SetOcrType(selectedType);
                        IniHelper.SetValue("配置", strOpType, item.Text);
                    }

                    break;
                case "左右方向":
                    ddlBtn.Text = item.Text;
                    ddlBtn.Image = ProcessImage(item.Image);
                    IsFromLeftToRight = Equals(item.Tag?.ToString(), "True");
                    CommonSetting.SetValue("从左到右", IsFromLeftToRight);
                    break;
                case "上下方向":
                    ddlBtn.Text = item.Text;
                    ddlBtn.Image = ProcessImage(item.Image);
                    IsFromTopToDown = Equals(item.Tag?.ToString(), "True");
                    CommonSetting.SetValue("从上到下", IsFromTopToDown);
                    break;
                case "翻译语言From":
                    ddlBtn.Image = item.Image;
                    BindTranslateLanguage(item.Tag, true);
                    CommonSetting.SetValue("源语言", CurrentTranslateFrom.ToString());
                    break;
                case "翻译语言To":
                    ddlBtn.Image = item.Image;
                    BindTranslateLanguage(item.Tag, false);
                    CommonSetting.SetValue("目标语言", CurrentTranslateTo.ToString());
                    break;
            }
        }

        private void SetOcrType(OcrType selectedType)
        {
            if (!selectedType.Equals(NowOcrType))
            {
                NowOcrType = selectedType;
                CloseAllTabs();

                tmsSpiltMode.Visible = NowOcrType != OcrType.翻译;
                tsmPicViewModel.Visible = NowOcrType != OcrType.表格;
                tsmLeftToRight.Visible = NowOcrType == OcrType.竖排;
                tsmTopToDown.Visible = NowOcrType == OcrType.竖排;
                tsmTransFrom.Visible = NowOcrType == OcrType.翻译;
                tsmTransTo.Visible = NowOcrType == OcrType.翻译;
                tsmAutoTrans.Visible = NowOcrType == OcrType.翻译;
                tsmShowOldContent.Visible = NowOcrType == OcrType.翻译;
                btnVoice.Visible = NowOcrType != OcrType.表格;
            }
        }

        private void BindSpiltModel(SpiltMode model)
        {
            CurrentSpiltModel = model;
            content.SpiltModel = model;
            tmsSpiltMode.Image = Resources.ResourceManager.GetObject(model.ToString()) as Bitmap;
            tmsSpiltMode.Text = model.ToString();
            tsmLeftToRight.Visible = NowOcrType == OcrType.竖排;
            tsmTopToDown.Visible = NowOcrType == OcrType.竖排;
            SetUcContentSpiltMode(model);
        }

        private void tsmShowOldContent_CheckedChanged(object sender, EventArgs e)
        {
            BindTranslateModel();
        }

        private void BindTranslateModel()
        {
            IsShowOldContent = tsmShowOldContent.Checked; // ? TranslateMode.显示译文 : TranslateMode.译文加原文;
            tsmShowOldContent.Checked = IsShowOldContent;
            tsmShowOldContent.ToolTipText = IsShowOldContent ? "显示原文+译文" : "仅显示译文";
            SetUcContentTranslateMode(IsShowOldContent);
            CommonSetting.SetValue("显示原文", IsShowOldContent);
        }

        private void BindTranslateLanguage(object obj, bool isFrom)
        {
            var type = (TransLanguageTypeEnum)BoxUtil.GetInt32FromObject(obj?.ToString(), 0);
            if (isFrom)
            {
                CurrentTranslateFrom = type;
                tsmTransFrom.Text = CurrentTranslateFrom.ToString();
            }
            else
            {
                CurrentTranslateTo = type;
                tsmTransTo.Text = CurrentTranslateTo.ToString();
            }
        }

        private UcContent GetCurrentContent()
        {
            foreach (var ctrl in tbMain.SelectedTab.Controls)
                if (ctrl is UcContent)
                    return ctrl as UcContent;
            return null;
        }

        private bool CheckIsOnRec()
        {
            if (CommonString.IsOnRec) CommonMethod.ShowHelpMsg(StrProcessLater);
            return CommonString.IsOnRec;
        }

        private void 批量识别ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var frmBatch = new FrmBatch
            { Icon = Icon, Theme = msManager.Theme, Style = msManager.Style, StyleManager = msManager };
            frmBatch.ShowDialog(this);
        }

        private void ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            cmsNotify.Hide();
            if (sender == null) return;
            var name = sender is ToolStripMenuItem
                ? ((ToolStripMenuItem)sender).Name
                : ((ToolStripButton)sender).Name;
            var control = GetCurrentContent();
            switch (name)
            {
                case "复制全部ToolStripMenuItem":
                case "tsmCopyTxt":
                    var strContent = control?.GetContentText();
                    if (!string.IsNullOrEmpty(strContent)) ClipboardService.SetText(strContent);
                    //txtContent.SelectAll();
                    //txtContent.Copy();
                    return;
                case "保存文本ToolStripMenuItem":
                case "tsmExportTxt":
                    SaveTxtFile(control?.GetContentText());
                    return;
                case "导出ExcelToolStripMenuItem":
                case "tsmExportExcel":
                    control?.ExportExcel();
                    return;
                case "文件识别ToolStripMenuItem":
                case "图片识别StripMenuItem":
                //case "文件ToolStripMenuItem":
                case "打开ToolStripMenuItem":
                    if (CheckIsOnRec()) return;
                    using (var imgFileDialog = new OpenFileDialog
                    {
                        Title = "请选择文件",
                        Filter = "图片或文档|*.png;*.jpg;*.jpeg;*.bmp;*.pdf;*.doc;*.docx;*.txt;",
                        RestoreDirectory = true,
                        Multiselect = false
                    })
                    {
                        if (imgFileDialog.ShowDialog(this) == DialogResult.OK)
                            ProcessFile(NowOcrType, ProcessBy.主界面, imgFileDialog.FileName, null, null);
                    }

                    return;
                case "PDF识别ToolStripMenuItem":
                case "PDF识别StripMenuItem":
                    if (CheckIsOnRec()) return;
                    using (var pdfFileDialog = new OpenFileDialog
                    {
                        Title = "请选择PDF文档",
                        Filter = "PDF文档|*.pdf;",
                        RestoreDirectory = true,
                        Multiselect = false
                    })
                    {
                        if (pdfFileDialog.ShowDialog(this) == DialogResult.OK)
                            ProcessFile(NowOcrType, ProcessBy.主界面, pdfFileDialog.FileName, null, null);
                    }

                    return;
                case "粘贴识别ToolStripMenuItem":
                    CopyTransAction();
                    return;
            }
        }

        private void CopyTransAction()
        {
            if (CheckIsOnRec()) return;
            TxtContent_KeyDown(null, new KeyEventArgs(Keys.Control | Keys.V));
        }

        private void ProcessByPDFFile(string fileName, bool isSpiltImage)
        {
            CloseAllTabs();
            ShowWindow();
            Task.Factory.StartNew(() =>
            {
                try
                {
                    //CommonMethod.ShowLoading();
                    var objLock = "";
                    ucLoading1.ShowLoading("正在解析PDF，请稍后……");
                    Dictionary<int, List<Bitmap>> dicImgContent = ConvertPdf2Image(fileName, isSpiltImage);
                    Dictionary<int, string> dicContent = new Dictionary<int, string>();
                    ucLoading1.ShowText(string.Format("正在识别第1/{0}页……", dicImgContent.Count));
                    bool isUplpadImg = false;
                    int minPerTime = 3000;
                    foreach (var pageIndex in dicImgContent.Keys)
                    {
                        var pageImages = dicImgContent[pageIndex];

                        for (int ii = 0; ii < pageImages.Count; ii++)
                        {
                            var img = pageImages[ii];
                            var byts = ImageProcessHelper.ImageToByte(img);
                            var imgUrl = isUplpadImg ? UploadByFile(byts, CommonString.StrDefaultImgType, false) : null;

                            OcrContent ocr = null;

                            var stop = Stopwatch.StartNew();
                            var processEntity = OcrPoolProcess.GetProcessEntityByBytes(OcrType.文本, NowOcrGroupType,
                                NowBiaoDianMode, IsFromLeftToRight, IsFromTopToDown, CurrentTranslateFrom, CurrentTranslateTo, byts,
                                imgUrl, CommonString.StrDefaultImgType, isUplpadImg, false, ProcessBy.主界面, null);
                            for (int i = 0; i < 10; i++)
                            {
                                if (ocr == null || ocr?.result?.HasResult == false)
                                {
                                    if (i == 0)
                                    {
                                        ucLoading1.ShowText(string.Format("正在识别第{0}/{1}页中第{2}/{3}张图片…", pageIndex, dicImgContent.Count, ii + 1, pageImages.Count));
                                    }
                                    ocr = GetOcrContentByBytes(processEntity);
                                    if (stop.ElapsedMilliseconds < minPerTime)
                                        Thread.Sleep((int)(minPerTime - stop.ElapsedMilliseconds));
                                }
                                else
                                {
                                    break;
                                }
                            }
                            lock (objLock)
                            {
                                var content =
                                    //string.Format("\n第{1}/{2}页\n", ocr?.processName, imgKey.Key, dicImgContent.Count) +
                                    ocr?.result?.spiltText;
                                // 处理节点：【{0}】
                                if (dicContent.ContainsKey(pageIndex))
                                {
                                    dicContent[pageIndex] += content;
                                }
                                else
                                {
                                    dicContent.Add(pageIndex, content);
                                }
                                ucLoading1.ShowText(string.Format("第{0}/{1}页中第{2}/{3}张图片识别完成！", pageIndex, dicImgContent.Count, ii + 1, pageImages.Count));
                            }
                            byts = null;
                            img.Dispose();
                            img = null;
                            ocr = null;
                        }
                    }
                    Parallel.ForEach(dicImgContent, new ParallelOptions() { MaxDegreeOfParallelism = 1 }, imgKey =>
                    {
                    });
                    ucLoading1.ShowText("识别完毕，开始合并文件……");
                    var sb = new StringBuilder();
                    for (int i = 1; i <= dicContent.Count; i++)
                    {
                        sb.AppendLine(dicContent[i]?.Replace("\n", Environment.NewLine));
                    }
                    string strFileName = CommonString.DefaultRecPath + Path.GetFileNameWithoutExtension(fileName) + "-" + NowOcrGroupType.ToString() + ".txt";
                    try
                    {
                        if (!Directory.Exists(CommonString.DefaultRecPath))
                        {
                            Directory.CreateDirectory(CommonString.DefaultRecPath);
                        }
                        if (File.Exists(strFileName))
                        {
                            File.Delete(strFileName);
                        }
                        File.WriteAllText(strFileName, sb.ToString());
                        ucLoading1.ShowText("PDF识别完成！\n" + strFileName);
                        CommonMethod.OpenFolderWithFile(strFileName);
                    }
                    catch
                    {
                        ucLoading1.ShowText("PDF识别完成！");
                        content.BindContentByStr(sb.ToString());
                    }
                    sb.Clear();
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
                finally
                {
                    ucLoading1.CloseLoading(3);
                }
            });
        }

        /// <summary>
        /// 将PDF文档转换为图片的方法
        /// </summary>
        /// <param name="pdfInputPath">PDF文件路径</param>
        /// <param name="isSpiltImage"></param>
        private Dictionary<int, List<Bitmap>> ConvertPdf2Image(string pdfInputPath, bool isSpiltImage)
        {
            Dictionary<int, List<Bitmap>> dicImgContent = new Dictionary<int, List<Bitmap>>();
            int definition = 3;
            //const string imgPath = @"C:\Users\<USER>\Desktop\pdf\";
            using (var pdfFile = O2S.Components.PDFRender4NET.PDFFile.Open(pdfInputPath))
            {
                int startPageNum = 1, endPageNum = pdfFile.PageCount;

                for (int i = startPageNum; i <= endPageNum; i++)
                {
                    Bitmap pageImage = pdfFile.GetPageImage(i - 1, 56 * definition);
                    var lstImage = new List<Bitmap>();
                    if (isSpiltImage)
                    {
                        Bitmap leftPageImage = new Bitmap(pageImage.Width / 2, pageImage.Height);
                        Bitmap rightPageImage = new Bitmap(pageImage.Width / 2, pageImage.Height);
                        using (Graphics g = Graphics.FromImage(leftPageImage))
                        {
                            g.InterpolationMode = InterpolationMode.High;
                            //重新画图的时候Y轴减去130，高度也减去130  这样水印就看不到了
                            g.DrawImage(pageImage, new Rectangle(0, 0, leftPageImage.Width, leftPageImage.Height),
                                new Rectangle(0, 130, pageImage.Width / 2, pageImage.Height - 130), GraphicsUnit.Pixel);
                        }
                        using (Graphics g = Graphics.FromImage(rightPageImage))
                        {
                            g.InterpolationMode = InterpolationMode.High;
                            //重新画图的时候Y轴减去130，高度也减去130  这样水印就看不到了
                            g.DrawImage(pageImage, new Rectangle(0, 0, rightPageImage.Width, rightPageImage.Height),
                                new Rectangle(pageImage.Width / 2, 130, pageImage.Width / 2, pageImage.Height - 130), GraphicsUnit.Pixel);
                        }
                        ////保存图片
                        //leftPageImage.Save(imgPath + i + "_1.jpg");
                        //rightPageImage.Save(imgPath + i + "_2.jpg");
                        lstImage.Add(leftPageImage);
                        lstImage.Add(rightPageImage);
                    }
                    else
                    {
                        Bitmap newPageImage = new Bitmap(pageImage.Width, pageImage.Height);
                        using (Graphics g = Graphics.FromImage(newPageImage))
                        {
                            g.InterpolationMode = InterpolationMode.High;
                            //重新画图的时候Y轴减去130，高度也减去130  这样水印就看不到了
                            g.DrawImage(pageImage, new Rectangle(0, 0, newPageImage.Width, newPageImage.Height),
                                new Rectangle(0, 130, pageImage.Width, pageImage.Height - 130), GraphicsUnit.Pixel);

                            //保存图片
                            //newPageImage.Save(imgPath + i + ".jpg");
                            g.Dispose();
                        }
                        lstImage.Add(newPageImage);
                    }
                    dicImgContent.Add(i, lstImage);
                }

                //pdfFile.Dispose();
            }
            return dicImgContent;
        }

        private string UploadByFile(byte[] byts, string ext, bool isShowLoading = true)
        {
            var imgUrl = string.Empty;
            try
            {
                if (isShowLoading)
                    ucLoading1.ShowLoading(StrUploadImg);

                if (string.IsNullOrEmpty(imgUrl)) imgUrl = new QQImageUpload().GetResult(byts, ext);
                if (string.IsNullOrEmpty(imgUrl)) imgUrl = new SouGouImageUpload().GetResult(byts, ext);
                if (string.IsNullOrEmpty(imgUrl)) imgUrl = new _360ImageUpload().GetResult(byts, ext);
                if (string.IsNullOrEmpty(imgUrl)) imgUrl = new SMMSImageUpload().GetResult(byts, ext);
                if (string.IsNullOrEmpty(imgUrl)) imgUrl = new ImgUrlImageUpload().GetResult(byts, ext);
            }
            catch
            {
            }

            return imgUrl;
        }

        private void SaveTxtFile(string txt)
        {
            using (var saveFileDialog1 = new SaveFileDialog
            {
                Filter = "txt文档| *.txt|Word文档| *.doc;*.docx|所有文件|*.*",
                Title = "选择保存位置",
                FileName = "文本_" + ServerTime.DateTime.ToString("yyyyMMddhhmmss") + ".txt",
                FilterIndex = 0
            })
            {
                if (saveFileDialog1.ShowDialog(this) == DialogResult.OK && saveFileDialog1.FileName != "")
                {
                    CommonResult.ExportToTxt(txt, saveFileDialog1.FileName);
                }
            }
        }

        private void 识别历史ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var frmBatch = new FormRecent
            { Icon = Icon, Theme = msManager.Theme, Style = msManager.Style, StyleManager = msManager };
            frmBatch.Show(this);
        }

        private void 截图识别ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var img = CaptureImageByType(false);
            if (img != null)
            {
                var ocrType = IsOcrForSearch ? OcrType.文本 : NowOcrType;
                ProcessByImage(ocrType, ProcessBy.主界面, img, true, null, IsOcrForSearch);
            }
            IsOcrForSearch = false;
        }

        private void 取色器ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var color = CatchImageColor();
            if (!Equals(Color.Transparent, color))
            {
                var content = string.Format("RGB:#{0}\nHEX:{1}\n{2}", color.ToRgb(), color.ToHex(), color.ToString());
                ClipboardService.SetText(content);
                CommonMethod.ShowNotificationTip(content + "\n\n已复制到粘贴板！", Application.ProductName + " - 取色器",
                    5 * 1000);
            }
        }

        private void 调色板ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            new ColorPickerForm(Color.Black)
            { Icon = Icon, Theme = msManager.Theme, Style = msManager.Style, StyleManager = msManager }.Show(this);
        }

        private Color CatchImageColor()
        {
            var result = Color.Transparent;
            var area = new DrawArea(false);
            try
            {
                area.TempshowZoom = true;
                area.UnshowZoom = false;
                area.IsShowCross = false;
                area.IsEdit = false;
                area.Prepare();
                area.IsRgb = Equals("RGB", CommonSetting.取色器文字样式);
                area.isAutoDraw = false;
                area.ActiveTool = DrawToolType.Pointer;
                area.Status = "取色器";
                if (area.ShowDialog(this) == DialogResult.OK)
                {
                    result = area.CurrentColorEx;
                    Console.WriteLine(result.ToString());
                }
            }
            catch
            {
            }
            finally
            {
                area.Close();
                area.Dispose();
                if (area != null) ((IDisposable)area).Dispose();
            }

            return result;
        }

        private void 标尺工具ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var scroll = new RulerForm { Icon = Icon };
            scroll.Show(this);
        }

        private void 截图ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var img = CaptureImageByType(true, "截图");
        }

        private void 文字贴图ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var data = new ClipboardTextEntity
            {
                Type = ClipboardDataType.Image,
                Image = ClipboardService.GetImage()
            };
            if (data.Image == null) data = ClipboardService.GetRtfHtmlImage();
            this.ViewTextImage(data);
        }

        private void InitScreenShot()
        {
            CommonEnumAction<CaptureActions>.AddEnumItemsContextMenu(cmsNotify, ProcessCaptureClickAction);
        }

        private bool HideFormTool(bool isHideMain = false)
        {
            var isShowTool = FrmTool.Visible;
            if (isShowTool)
                FrmTool.Visible = false;
            if (isHideMain)
            {
                Hide();
            }

            frmOCR?.HideWindow(true);
            return isShowTool;
        }

        private void ProcessCaptureClickAction(object sender, EventArgs e)
        {
            var clickItem = sender as ToolStripItem;
            var info = (EnumInfo)clickItem.Tag;
            var action = (CaptureActions)info.Value;
            Image image = null;
            Rectangle rectangle = Rectangle.Empty;
            var isShowTool = HideFormTool();
            switch (action)
            {
                case CaptureActions.截图:
                    image = CaptureImageByType(true, action.ToString());
                    break;
                case CaptureActions.截图贴图:
                    image = CaptureImageByType(true, action.ToString());
                    if (image != null)
                        this.ViewImageWithLocation(image,
                            new Point(StaticValue.catchRectangle.X, StaticValue.catchRectangle.Y));
                    break;
                case CaptureActions.活动窗口:
                    image = screenshot.CaptureActiveWindow(ref rectangle);
                    break;
                case CaptureActions.全屏:
                    image = screenshot.CaptureFullscreen(ref rectangle);
                    break;
                case CaptureActions.活动显示器:
                    image = screenshot.CaptureActiveMonitor(ref rectangle);
                    break;
                case CaptureActions.固定区域:
                    //var item = CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.固定区域);
                    FixAreaItemClick(clickItem);
                    break;
                case CaptureActions.滚动截屏:
                    var scroll = new ScrollingCaptureForm(new ScrollingCaptureOptions())
                    { Icon = Icon, Theme = msManager.Theme, Style = msManager.Style, StyleManager = msManager };
                    scroll.Show(this);
                    break;
            }

            if (isShowTool) FrmTool.Visible = true;
            if (image != null)
            {
                SaveImage(image, true);
            }
        }

        private void FixAreaItemClick(ToolStripItem item)
        {
            if (item == null)
            {
                return;
            }

            var text = item.Text.Replace("停止", "");
            if (item.Text.StartsWith("停止"))
            {
                item.Text = text;
                this.GetShadowForm(text)?.Close();
            }
            else
            {
                var img = CaptureImageByType(false, text);
                if (img == null)
                {
                    return;
                }
                item.Text = "停止" + text;

                FixAreaCaptureMethod(item);
            }
        }

        private void FixAreaCaptureMethod(ToolStripItem item)
        {
            var text = item.Text.Replace("停止", "");
            bool isOcr = text.Contains("识别");

            var rect = StaticValue.catchRectangle;
            var shadowForm = InitShadowForm(item, isOcr, new Size(rect.Size.Width + 4, rect.Size.Height + 4),
                new Point(rect.X - 2, rect.Y - 2));

            int delayMilSec = 3000;
            int intervalMilSec = 3500;
            int loopTimes = isOcr ? 100 : 1;

            SetShadowFormText(shadowForm, text + "\n等待操作…");
            var frmArea = new FormAreaCapture()
            { Icon = Icon, Theme = msManager.Theme, Style = msManager.Style, StyleManager = msManager };
            frmArea.CaptureLocation = rect.Location;
            frmArea.CaptureSize = rect.Size;
            frmArea.IntervalMilSecond = intervalMilSec;
            frmArea.DelayMilSecond = delayMilSec;
            frmArea.LoopTimes = loopTimes;
            frmArea.TopMost = true;
            frmArea.Text = text;
            shadowForm.TopMost = false;
            if (frmArea.ShowDialog(this) == DialogResult.OK)
            {
                if (!frmArea.CaptureLocation.IsEmpty)
                {
                    rect.Location = frmArea.CaptureLocation;
                }

                if (!frmArea.CaptureSize.IsEmpty)
                {
                    rect.Size = frmArea.CaptureSize;
                }

                delayMilSec = frmArea.DelayMilSecond;
                loopTimes = frmArea.LoopTimes;
                intervalMilSec = Math.Max(frmArea.IntervalMilSecond, 1000);
            }

            shadowForm.Location = new Point(rect.X - 2, rect.Y - 2);
            shadowForm.Size = new Size(rect.Size.Width + 4, rect.Size.Height + 4);
            shadowForm.TopMost = true;
            shadowForm?.ForceActivate();

            if (rect.Width < MinFixAreaWidth) rect.Width = MinFixAreaWidth;
            if (rect.Height < MinFixAreaWidth) rect.Height = MinFixAreaWidth;

            FixAreaCaptureEntity entity = new FixAreaCaptureEntity()
            {
                BaseRect = rect,
                DelayMilSec = delayMilSec,
                IntervalMilSecond = intervalMilSec,
                Item = item,
                LoopTimes = loopTimes,
                ShadowForm = shadowForm
            };
            FixedFieldCapture(entity);
        }

        private void FixedFieldCapture(FixAreaCaptureEntity entity)
        {
            Task.Factory.StartNew(() =>
            {
                while (!CommonString.IsExit && entity.Item.Text.StartsWith("停止") && entity.DelayMilSec > 0)
                {
                    SetShadowFormText(entity.ShadowForm, string.Format("{0}秒后开始", Math.Ceiling(entity.DelayMilSec * 1.0 / 1000)));
                    Thread.Sleep(300);
                    entity.DelayMilSec -= 300;
                }

                SetShadowFormText(entity.ShadowForm);
                var nowLoopTime = 0;
                while (!CommonString.IsExit && entity.Item.Text.StartsWith("停止") && nowLoopTime < entity.LoopTimes)
                    if (!CommonString.IsOnRec)
                    {
                        var rect = new Rectangle(entity.ShadowForm?.Location ?? entity.BaseRect.Location,
                            entity.ShadowForm?.Size ?? entity.BaseRect.Size);
                        if (rect.IsEmpty) rect = entity.BaseRect;
                        var image = screenshot.CaptureRectangle(rect);
                        nowLoopTime++;
                        SetShadowFormText(entity.ShadowForm, string.Format("第{0}/{1}次", nowLoopTime, entity.LoopTimes));
                        if (entity.IsOcr)
                        {
                            ProcessByImage(NowOcrType, ProcessBy.固定区域, image, true);
                        }
                        else
                        {
                            SaveImage(image, true);
                        }

                        if (nowLoopTime < entity.LoopTimes)
                        {
                            Thread.Sleep(entity.IntervalMilSecond);
                            SetShadowFormText(entity.ShadowForm);
                        }
                    }
                    else
                    {
                        Thread.Sleep(1000);
                    }

                entity.ShadowForm?.Close();
                entity.ShadowForm?.Dispose();
                entity.ShadowForm = null;
            });
        }

        private ShadowForm InitShadowForm(ToolStripItem parentItem, bool isOcr, Size size, Point location)
        {
            var shadowForm = new ShadowForm
            {
                Size = size,
                Location = location,
                StartPosition = FormStartPosition.Manual,
                BackColor = Color.FromArgb(255, 255, 254),
                TransparencyKey = Color.FromArgb(255, 255, 254),
                TopMost = true,
                Icon = Icon,
                Text = parentItem.Text.Replace("停止", ""),
                ShowInTaskbar = false,
                IsSharkWindow = false,
                ShadowWidth = 10,
                IsShowTitle = true,
                MinimizeBox = false,
                MaximizeBox = false
            };
            var item = new ToolStripMenuItem
            {
                Text = parentItem.Text,
                Image = parentItem.Image,
                Tag = parentItem.Tag
            };
            item.Click += (obj, e) =>
            {
                parentItem.PerformClick();
            };

            var contentMenu = new ContextMenuStrip();
            contentMenu.Items.Add(item);
            shadowForm.ContextMenuStrip = contentMenu;

            var label = new Label()
            {
                BackColor = Color.Transparent,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = CommonSetting.默认文字字体,
                ForeColor = CommonSetting.默认文字颜色
            };
            label.TextChanged += (object obj, EventArgs e) =>
            {
                label.Visible = !string.IsNullOrEmpty(label.Text);
                try
                {
                    if (shadowForm.IsShowTitle != label.Visible)
                    {
                        shadowForm.IsShowTitle = label.Visible;
                        shadowForm.Invalidate();
                    }
                }
                catch
                {
                }
            };
            shadowForm.Controls.Add(label);

            shadowForm.DoubleClick += (sender, e) =>
            {
                if (parentItem.Text.StartsWith("停止"))
                    parentItem.PerformClick();
            };
            shadowForm.FormClosed += (sender, e) =>
            {
                if (parentItem.Text.StartsWith("停止"))
                    parentItem.PerformClick();
            };
            shadowForm.Show();
            return shadowForm;
        }

        private void SetShadowFormText(ShadowForm shadowForm, string text = "")
        {
            try
            {
                if (shadowForm?.Visible == false || shadowForm.Controls.Count <= 0)
                    return;
                (shadowForm.Controls[0] as Label).Text = text;
            }
            catch
            {
            }
        }

        private Image CaptureImageByType(bool isEdit, string operate = null, bool isHex = false)
        {
            Image img = null;
            cmsNotify.Hide();
            if (CommonString.IsOnRec)
            {
                CommonMethod.ShowHelpMsg(StrProcessLater);
                return img;
            }

            if (!StaticValue.IsCatchScreen)
            {
                var isShowTool = HideFormTool(true);
                img = CatchImage(isEdit, operate, isHex);
                if (isShowTool) FrmTool.Visible = true;
            }

            return img;
        }

        private Image CatchImage(bool isEdit, string operate = null, bool isHex = false)
        {
            Image result = null;
            using (var area = new DrawArea())
            {
                try
                {
                    area.TempshowZoom = isHex;
                    area.UnshowZoom = !CommonSetting.显示放大镜;
                    area.IsShowCross = CommonSetting.显示全屏十字线;
                    area.IsEdit = isEdit;
                    area.Prepare();
                    area.IsRgb = Equals("RGB", CommonSetting.取色器文字样式);
                    area.isAutoDraw = true;
                    area.ActiveTool = isEdit ? DrawToolType.Catch : DrawToolType.QuickCatch;
                    StaticValue.CurrentToolType = isEdit ? DrawToolType.Catch : DrawToolType.QuickCatch;
                    area.Status = string.IsNullOrEmpty(operate) ? NowOcrType + "识别" : operate;
                    if (area.ShowDialog(this) == DialogResult.OK) result = area.GetResultImage();
                }
                catch
                {
                }
                finally
                {
                    area.Close();
                    area.Dispose();
                }
            }

            return result;
        }

        private void OcrProcessThread()
        {
            new Thread(p =>
                {
                    try
                    {
                        foreach (var content in OcrPoolProcess.OcrProcessPool.GetConsumingEnumerable())
                            try
                            {
                                if (CommonString.IsOnLine)
                                    OcrProcessMethod(content);
                                else
                                    CommonMethod.ShowHelpMsg("当前本地网络不可用，请稍后重试！");
                                if (Equals(content.ProcessBy, ProcessBy.主界面) || Equals(content.ProcessBy, ProcessBy.固定区域)) ShowWindow();
                            }
                            catch (Exception oe)
                            {
                                Console.WriteLine(oe.Message);
                            }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
                })
            { IsBackground = true, Priority = ThreadPriority.Highest }.Start();
        }

        private void OcrResultProcessThread()
        {
            new Thread(p =>
                {
                    try
                    {
                        foreach (var content in OcrResultPool.GetConsumingEnumerable())
                            try
                            {
                                CommonMethod.DetermineCall(this, delegate
                                 {
                                     CommonMsg.Hide(this);
                                     if (Equals(content.ProcessBy, ProcessBy.主界面)
                                     || Equals(content.ProcessBy, ProcessBy.固定区域))
                                     {
                                         NativeMethods.LockWindowUpdate(tbMain.Handle);
                                         BindResult(content);
                                         NativeMethods.LockWindowUpdate(IntPtr.Zero);
                                     }
                                     else if (Equals(content.ProcessBy, ProcessBy.划词翻译))
                                     {
                                         BindToolSearch(content);
                                     }
                                     else if (Equals(content.ProcessBy, ProcessBy.批量识别))
                                     {
                                         FrmBatch.OcrResultPool.Add(content);
                                     }
                                 });
                            }
                            catch (Exception oe)
                            {
                                Console.WriteLine(oe.Message);
                            }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
                })
            { IsBackground = true, Priority = ThreadPriority.Highest }.Start();
        }

        private void tsmSearch_Click(object sender, EventArgs e)
        {
            var txt = GetCurrentTxt();
            if (!string.IsNullOrEmpty(txt)) DoSearch(txt);
        }

        public static void DoSearch(string text)
        {
            var url = string.Format(NowSearchEngine.Url, HttpUtility.UrlEncode(text));
            Process.Start(url);
        }

        private void OcrProcessMethod(OcrProcessEntity processEntity)
        {
            try
            {
                //Console.WriteLine(string.Format("开始处理时间：{0}",DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss fff")));

                var isOnMainFormProcess = Equals(processEntity.ProcessBy, ProcessBy.主界面)
                                          || Equals(processEntity.ProcessBy, ProcessBy.固定区域);
                var isOnHuaCi = Equals(processEntity.ProcessBy, ProcessBy.划词翻译);

                if (processEntity.IsNeedUpload
                ) //&& NowOcrType != OCRType.表格 && NowOcrType != OCRType.公式 && NowOcrType != OCRType.翻译
                {
                    processEntity.ImgUrl = UploadByFile(processEntity.Byts, processEntity.FileExt, isOnMainFormProcess);
                    if (isOnMainFormProcess)
                        imageBox.SetPicImageUrl(processEntity.IsImgUrl ? processEntity.ImgUrl : null);
                }

                if (processEntity.IsShowLoading)
                {
                    if (isOnHuaCi)
                        frmOCR?.ShowLoading(StrProcessOcr, processEntity.IsShowLoading);
                    else if (isOnMainFormProcess)
                        ucLoading1.ShowLoading(StrProcessOcr, processEntity.IsShowLoading);
                }

                var ocrTask = Task.Factory.StartNew(() =>
                    {
                        //Console.WriteLine(string.Format("开始OCR时间：{0}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss fff")));
                        var ocr = GetOcrContentByBytes(processEntity);
                        return ocr;
                    })
                    .ContinueWith(task =>
                    {
                        var ocr = task.Result;
                        if (isOnMainFormProcess) CloseAllTabs();
                        if (string.IsNullOrEmpty(ocr?.id))
                        {
                            if (isOnHuaCi)
                                frmOCR.Tag = null;
                            else if (isOnMainFormProcess)
                                tbMain.Tag = null;
                            return;
                        }

                        if (isOnHuaCi)
                            frmOCR.Tag = ocr.id;
                        else if (isOnMainFormProcess)
                            tbMain.Tag = ocr.id;
                        var hasResult = ocr?.result?.HasResult == true;
                        if (hasResult)
                        {
                            if (processEntity.IsSearch && !string.IsNullOrEmpty(ocr.result.GetAutoText()))
                                DoSearch(ocr.result.GetAutoText());
                            else if (processEntity.OcrType == OcrType.翻译 && ocr.result.IsTransResult &&
                                 processEntity.FileExt.Equals(CommonString.StrDefaultTxtType))
                                try
                                {
                                    ocr.result.spiltText = Encoding.UTF8.GetString(processEntity.Byts);
                                }
                                catch (Exception oe)
                                {
                                    Console.WriteLine("处理翻译原文出错！" + oe.Message);
                                }

                            ocr.Identity = processEntity.Identity;
                            ocr.ProcessBy = processEntity.ProcessBy;
                            OcrResultPool.Add(ocr);
                        }

                        Console.WriteLine("开始绑定结果：" + hasResult + " " +
                                          ServerTime.DateTime.ToString("yyyy-MM-dd HH:mm:ss fff"));
                        LoadOtherOcrResult(ocr.id, processEntity.ProcessBy, processEntity.Identity, hasResult);
                        Console.WriteLine("获取其他结果完毕：" + hasResult + " " +
                                          ServerTime.DateTime.ToString("yyyy-MM-dd HH:mm:ss fff"));
                    })
                    .ContinueWith(task =>
                    {
                        Console.WriteLine("绑定结果完毕：" + ServerTime.DateTime.ToString("yyyy-MM-dd HH:mm:ss fff"));

                        if (isOnHuaCi)
                        {
                            frmOCR?.CloseLoading();
                        }
                        else if (isOnMainFormProcess)
                        {
                            ucLoading1.CloseLoading();
                            if (ActiveForm != this) FlashWindowHelper.Flash(3, 600, Handle);
                        }

                        processEntity.Byts = null;
                        processEntity = null;
                    });
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        private OcrContent GetOcrContentByBytes(OcrProcessEntity processEntity)
        {
            OcrContent ocr;
            processEntity.FileExt = string.IsNullOrEmpty(processEntity.FileExt)
                ? CommonString.StrDefaultImgType
                : processEntity.FileExt;
            if (processEntity.IsFile && Program.NowUser == null)
            {
                ocr = new OcrContent
                {
                    id = Guid.NewGuid().ToString(),
                    result = new ResultEntity
                    {
                        autoText = "未登录用户不支持文件识别！",
                        spiltText = "未登录用户不支持文件识别！"
                    },
                    processName = "温馨提示"
                };
                return ocr;
            }

            // 处理OcrType
            if (CheckIsForbidOperate(processEntity.OcrType)
                || !CommonString.LstCanProcessFilesExt.Contains(processEntity.FileExt)
                || processEntity.IsFile &&
                (Program.NowUser?.IsSupportDocFile == false &&
                 CommonString.LstCanProcessDocFilesExt.Contains(processEntity.FileExt)
                 || Program.NowUser?.IsSupportImageFile == false &&
                 CommonString.LstCanProcessImageFilesExt.Contains(processEntity.FileExt))
            )
            {
                ocr = new OcrContent
                {
                    id = Guid.NewGuid().ToString(),
                    result = new ResultEntity
                    {
                        autoText = "当前用户不支持此类型文件识别！",
                        spiltText = "当前用户不支持此类型文件识别！"
                    },
                    processName = "温馨提示"
                };
                return ocr;
            }

            if (!processEntity.IsImgUrl)
                //var ocr = OCRHelper.GetResult(byts);
                ocr = OcrHelper.GetResult(processEntity);
            else
                ocr = OcrHelper.GetResultByUrl(processEntity);

            return ocr;
        }

        private void ocrSpiltModelItem_Click(object sender, EventArgs e)
        {
            var clickedItem = sender as ToolStripMenuItem;
            if (clickedItem == null) return;
            foreach (ToolStripMenuItem item in tmsSpiltMode.DropDownItems) item.Checked = Equals(item, clickedItem);
            var selectedGroupType = (SpiltMode)BoxUtil.GetInt32FromObject(clickedItem.Tag?.ToString());
            if (!selectedGroupType.Equals(CurrentSpiltModel))
            {
                BindSpiltModel(selectedGroupType);
                CommonSetting.SetValue("分段模式", CurrentSpiltModel.ToString());
            }

            //tsmDDL_DropDownItemClicked("OCR分组", new ToolStripItemClickedEventArgs(clickedItem));
            //(sender as ToolStripMenuItem)?.OwnerItem?.PerformClick();
        }

        private void ocrGroupTypeItem_Click(object sender, EventArgs e)
        {
            var clickedItem = sender as ToolStripMenuItem;
            if (clickedItem == null) return;
            foreach (ToolStripMenuItem item in ocrGroupTypeToolStripMenuItem.DropDownItems)
                item.Checked = Equals(item, clickedItem);
            var selectedGroupType = (OcrGroupType)BoxUtil.GetInt32FromObject(clickedItem.Tag?.ToString());
            if (!selectedGroupType.Equals(NowOcrGroupType))
            {
                NowOcrGroupType = selectedGroupType;
                CommonSetting.SetValue("识别引擎", NowOcrGroupType.ToString());
            }

            //tsmDDL_DropDownItemClicked("OCR分组", new ToolStripItemClickedEventArgs(clickedItem));
            //(sender as ToolStripMenuItem)?.OwnerItem?.PerformClick();
        }

        private void ocrTypeItem_Click(object sender, EventArgs e)
        {
            tsmDDL_DropDownItemClicked(tsmContentType,
                new ToolStripItemClickedEventArgs(sender as ToolStripMenuItem));
            (sender as ToolStripMenuItem)?.OwnerItem?.PerformClick();
        }

        #region 图片预览保存

        private void SetPicImage(Image img, string imgUrl = null)
        {
            imageBox.SetPicImage(img, imgUrl);
            if (img == null)
                tbImageBox.Parent = null;
            else
                tbImageBox.Parent = tbMain;
        }

        #endregion

        private void btnVoice_ButtonClick(object sender, EventArgs e)
        {
            if (IsPlaying)
            {
                VoiceStateChange();
                return;
            }

            var txt = GetCurrentTxt();
            if (!string.IsNullOrEmpty(txt))
                try
                {
                    VoiceStateChange(true);
                    var url = OcrHelper.GetVoiceUrlResult(txt, CurrentSpeaker.GetHashCode().ToString(), "1");
                    //wbVoice.DocumentText = "<bgsound src=\"" + url + "\" loop=\"1\"/>";
                    wbVoice.DocumentText = "<audio src=\"" + url +
                                           "\" controls=\"true\" autoplay=\"true\" onended=\"window.external.VoiceStoped()\" />";
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
        }

        private string GetCurrentTxt(bool isAll = true)
        {
            foreach (Control item in tbMain.SelectedTab.Controls)
                if (item is UcContent)
                {
                    var tab = item as UcContent;
                    return tab.GetContentText(isAll);
                }

            return null;
        }

        public void VoiceStateChange(bool isPlaying = false)
        {
            IsPlaying = isPlaying;
            btnVoice.Text = isPlaying ? "停止" : "朗读";
            if (!isPlaying) wbVoice.DocumentText = "";
        }

        private void SpeakerItem_Click(object sender, EventArgs e)
        {
            var clickedItem = sender as ToolStripMenuItem;
            foreach (ToolStripMenuItem item in btnVoice.DropDownItems) item.Checked = Equals(item, clickedItem);
            CurrentSpeaker = (Speaker)BoxUtil.GetInt32FromObject(clickedItem.Tag?.ToString(), 0);
            CommonSetting.SetValue("朗读配置", CurrentSpeaker.ToString());
        }

        private void tsmPicViewModel_Click(object sender, EventArgs e)
        {
            NowDisplayMode = NowDisplayMode == DisplayModel.文字模式 ? DisplayModel.图文模式 : DisplayModel.文字模式;
            UpdatePicViewModel(NowDisplayMode);
            CommonSetting.SetValue("图文模式", NowDisplayMode == DisplayModel.图文模式);
        }

        private void UpdatePicViewModel(DisplayModel model)
        {
            tsmPicViewModel.Image = Resources.ResourceManager.GetObject(model.ToString()) as Bitmap;
            tsmPicViewModel.Text = string.Format("【{0}】", model);
            SetUcContentDisplayMode(model);
        }

        private void CheckNetWork()
        {
            tbMain.SelectedIndex = 0;

            content.BindContentByStr("【网络检测】");
            content.BindContentByStr("1、本地连接状态检测…", true);
            SetNowNetWork(CommonString.IsOnLine, true);
            var error = !CommonString.IsOnLine;
            content.BindContentByStr("本地网络连接" + (error ? "异常" : "正常"), true);
            content.BindContentByStr("", true);

            content.BindContentByStr("2、开始DNS检测…", true);
            var ip = DnsHelper.ToIpAddress(CommonString.StrServerHost);
            error = string.IsNullOrEmpty(ip) || !DnsHelper.IsIPv4(ip);
            content.BindContentByStr("本地DNS解析" + (error ? "异常" : "正常"), true);
            ip = DnsHelper.GetDnsFromNsLookUp(CommonString.StrServerHost, "***************");
            error = string.IsNullOrEmpty(ip) || !DnsHelper.IsIPv4(ip);
            content.BindContentByStr("***************解析" + (error ? "异常" : "正常"), true);
            ip = DnsHelper.GetDnsFromNsLookUp(CommonString.StrServerHost, "************");
            error = string.IsNullOrEmpty(ip) || !DnsHelper.IsIPv4(ip);
            content.BindContentByStr("************解析" + (error ? "异常" : "正常"), true);
            content.BindContentByStr("", true);

            content.BindContentByStr("3、开始上网检测…", true);
            var html = WebClientExt.GetHtml("http://www.baidu.com");
            error = string.IsNullOrEmpty(html);
            content.BindContentByStr("访问百度服务器" + (error ? "异常" : "正常"), true);
            var lstType = OcrHelper.GetCanRegUserTypes();
            error = !(lstType != null && lstType.Count > 0);
            content.BindContentByStr("访问助手服务器" + (error ? "异常" : "正常"), true);
            content.BindContentByStr("", true);

            content.BindContentByStr("网络检测完毕！", true);
        }

        private void pnlNetWork_Click(object sender, EventArgs e)
        {
            Task.Factory.StartNew(() =>
            {
                ShowWindow();
                CheckNetWork();
            });
        }

        #region MouseHook

        private void mouseHook_MouseSelected(object sender, MouseEventArgs e)
        {
            mouseHook_DoubleClick(sender, null);
        }

        private FormOcr frmOCR;

        private void HookManager_MouseClick(object sender, MouseEventArgs e)
        {
            //if (frmSearch?.IsLeave == false)
            //{
            //    return;
            //}
            if (!CommonString.IsOnOcrButton && !CommonString.IsOnOcrSearch) frmOCR?.HideWindow(true);
        }

        private readonly List<string> lstAllowCopyControls = new List<string>
            {"Pane", "Document", "Edit", "ListItem", "Text", "Window"};

        private readonly List<string> lstNotAllowCopyClasses = new List<string> { "IHWindowClass" };

        private object GetFocusElementText(out ClipboardContentType contentType)
        {
            object result = null;

            contentType = ClipboardContentType.文本;
            var isFile = false;
            var isImg = false;
            var mouse = Cursor.Position; // use Windows forms mouse code instead of WPF
            var element = AutomationElement.FromPoint(new System.Windows.Point(mouse.X, mouse.Y));

            //var element1 = AutomationElement.FocusedElement;
            if (element != null)
            {
                foreach (AutomationElement item in element.FindAll(TreeScope.Subtree, Condition.TrueCondition))
                    if (!item.Current.IsOffscreen)
                        Console.WriteLine(item.Current.Name);
                foreach (AutomationElement item in element.FindAll(TreeScope.Subtree,
                    new PropertyCondition(AutomationElement.IsOffscreenProperty, false)))
                    if (!item.Current.IsOffscreen)
                        Console.WriteLine(item.Current.Name);
                var controlName = element.Current.ControlType?.ProgrammaticName;
                var className = element.Current.ClassName;
                Console.WriteLine("Type:" + className
                                          + ",isContent:" + element.Current.IsContentElement
                                          + ",isControl:" + element.Current.IsControlElement
                                          + ",canFocus:" + element.Current.IsKeyboardFocusable
                                          + ",isFocus:" + element.Current.HasKeyboardFocus
                                          + ",isVisiable:" + !element.Current.IsOffscreen
                                          + ",isPassword:" + element.Current.IsPassword
                                          + ";Desc:" + controlName);
                //隐藏元素或者密码类，不捕获
                if (element.Current.IsPassword) return result;
                result = element.GetText(out var isIn);
                if (!isIn)
                {
                    if (!string.IsNullOrEmpty(className) && lstNotAllowCopyClasses.Any(p => className.Contains(p)))
                        return result;
                    if (!string.IsNullOrEmpty(controlName) && lstAllowCopyControls.Any(p => controlName.Contains(p)))
                        CommonMethod.DetermineCall(this, delegate
                         {
                             var oldText = ClipboardService.GetText()?.Trim();
                             var oldDataBackup = ClipboardService.Backup();
                             ////清空剪切板，避免脏读
                             //Clipboard.Clear();

                             //开始复制
                             SendKeys.SendWait("^c");
                             SendKeys.Flush();
                             //System.Threading.Thread.Sleep(500);
                             result = ClipboardService.GetText()?.Trim();
                             if (result == null)
                             {
                                 //result = ClipboardService.GetImage();
                                 //if (result != null)
                                 //{
                                 //    isImg = true;
                                 //}
                                 //else
                                 //{
                                 //    result = ClipboardService.GetOneFile();
                                 //    if (result != null && !string.IsNullOrEmpty(result.ToString()))
                                 //    {
                                 //        isFile = true;
                                 //    }
                                 //}
                             }
                             else
                             {
                                 if (Equals(result, oldText)) result = null;
                             }

                             ClipboardService.Restore(oldDataBackup);
                         });
                }
            }

            if (isFile)
                contentType = ClipboardContentType.文件;
            else if (isImg) contentType = ClipboardContentType.图片;
            return result;
        }

        private void mouseHook_DoubleClick(object sender, EventArgs e)
        {
            if (CommonString.IsOnOcrButton || CommonString.IsOnOcrSearch) return;
            Task.Factory.StartNew(() =>
            {
                try
                {
                    var result = GetFocusElementText(out var contentType);
                    if (result == null || string.IsNullOrEmpty(result?.ToString())) return;
                    //frmSearch?.Hide();
                    CommonMethod.DetermineCall(this, delegate
                     {
                         if (frmOCR == null) frmOCR = new FormOcr { Icon = Icon };
                         frmOCR.Content = result;
                         frmOCR.ContentType = contentType;

                         var location = new Point(Cursor.Position.X + 10, Cursor.Position.Y - 15 - 32);
                         frmOCR.Location = location;
                         frmOCR.ShowTool(true);
                         if (!Equals(frmOCR.Width, 32)) frmOCR.Size = new Size(32, 32);
                         if (!Equals(location, frmOCR.Location)) frmOCR.Location = location;
                         Application.DoEvents();
                     });
                    //Clipboard.Clear();
                }
                catch
                {
                }
            });
        }

        #endregion

        #region 权限相关

        private bool CheckIsForbidOperate(OcrType ocrType)
        {
            var isForbid = false;
            if (Equals(ocrType, OcrType.竖排) && !(Program.NowUser?.IsSupportVertical == true)
                || Equals(ocrType, OcrType.表格) && !(Program.NowUser?.IsSupportTable == true)
                || Equals(ocrType, OcrType.公式) && !(Program.NowUser?.IsSupportMath == true)
                || Equals(ocrType, OcrType.翻译) && !(Program.NowUser?.IsSupportTranslate == true)
            )
                isForbid = true;

            return isForbid;
        }

        private void ProcessForbidControls()
        {
            try
            {
                //PDF识别StripMenuItem.Visible = (Program.NowUser?.IsSupportDocFile == true);
                批量识别ToolStripMenuItem.Visible = Program.NowUser?.IsSupportBatch == true;
                文件识别ToolStripMenuItem.Visible = Program.NowUser?.IsSupportImageFile == true;

                ocrGroupTypeToolStripMenuItem.Visible = Program.NowUser?.IsSupportImageFile == true;
                tsmOcrGroupSpilt.Visible = Program.NowUser?.IsSupportImageFile == true;

                InitOcrTypes();
                tbMain.Focus();

                CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.固定区域).Visible = Program.NowUser?.IsSupportImageFile == true;
                CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, "固定区域识别").Visible = Program.NowUser?.IsSupportImageFile == true;
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        private void InitOcrTypes()
        {
            tsmContentType.DropDownItems.Clear();
            截图识别toolStripMenuItem.DropDownItems.Clear();
            粘贴识别ToolStripMenuItem.DropDownItems.Clear();
            文件识别ToolStripMenuItem.DropDownItems.Clear();
            foreach (OcrType ocrType in Enum.GetValues(typeof(OcrType)))
                if (!CheckIsForbidOperate(ocrType))
                {
                    var item = new ToolStripMenuItem
                    {
                        Text = ocrType.ToString(),
                        Tag = ocrType.GetHashCode(),
                        Font = tsmContentType.Font,
                        Image = Resources.ResourceManager.GetObject(ocrType.ToString()) as Bitmap
                    };
                    tsmContentType.DropDownItems.Add(item);

                    var 截图item = new ToolStripMenuItem
                    {
                        Text = ocrType.ToString(),
                        Tag = ocrType.GetHashCode(),
                        Font = tsmContentType.Font,
                        Image = Resources.ResourceManager.GetObject(ocrType.ToString()) as Bitmap
                    };
                    截图item.Click += ocrTypeItem_Click;
                    截图识别toolStripMenuItem.DropDownItems.Add(截图item);

                    var 粘贴item = new ToolStripMenuItem
                    {
                        Text = ocrType.ToString(),
                        Tag = ocrType.GetHashCode(),
                        Font = tsmContentType.Font,
                        Image = Resources.ResourceManager.GetObject(ocrType.ToString()) as Bitmap
                    };
                    粘贴item.Click += ocrTypeItem_Click;
                    粘贴识别ToolStripMenuItem.DropDownItems.Add(粘贴item);

                    var 图片识别item = new ToolStripMenuItem
                    {
                        Text = ocrType.ToString(),
                        Tag = ocrType.GetHashCode(),
                        Font = tsmContentType.Font,
                        Image = Resources.ResourceManager.GetObject(ocrType.ToString()) as Bitmap
                    };
                    图片识别item.Click += ocrTypeItem_Click;
                    文件识别ToolStripMenuItem.DropDownItems.Add(图片识别item);
                }
            var 搜索item = new ToolStripMenuItem
            {
                Text = "搜索",
                Tag = "搜索",
                Font = tsmContentType.Font,
                Image = Resources.ResourceManager.GetObject("搜索") as Bitmap
            };
            搜索item.Click += ocrTypeItem_Click;
            截图识别toolStripMenuItem.DropDownItems.Add(搜索item);
        }

        private void InitOcrGroupItems()
        {
            ocrGroupTypeToolStripMenuItem.DropDownItems.Clear();
            foreach (OcrGroupType groupType in Enum.GetValues(typeof(OcrGroupType)))
            {
                var item = new ToolStripMenuItem
                { Text = groupType.ToString(), Tag = groupType.GetHashCode(), Font = tsmContentType.Font };
                item.Click += ocrGroupTypeItem_Click;
                if (groupType.Equals(NowOcrGroupType)) item.Checked = true;
                ocrGroupTypeToolStripMenuItem.DropDownItems.Add(item);
            }
        }

        private void InitSearchMenu()
        {
            if (CommonEnumAction<string>.FindMeunItem(cmsNotify, "固定区域识别") == null)
            {
                var 固定区域ToolStripMenuItemitem = new ToolStripMenuItem
                {
                    Text = "固定区域识别",
                    Font = tsmContentType.Font,
                    Image = Resources.固定区域
                };
                固定区域ToolStripMenuItemitem.Click += (obj, e) =>
                {
                    FixAreaItemClick(固定区域ToolStripMenuItemitem);
                };
                识别合集toolStripMenuItem.DropDownItems.Add(固定区域ToolStripMenuItemitem);
            }
        }

        private void InitSpiltModel()
        {
            tmsSpiltMode.DropDownItems.Clear();
            foreach (SpiltMode model in Enum.GetValues(typeof(SpiltMode)))
            {
                var item = new ToolStripMenuItem
                {
                    Text = model.ToString(),
                    Tag = model.GetHashCode(),
                    Font = tsmContentType.Font,
                    Image = Resources.ResourceManager.GetObject(model.ToString()) as Bitmap,
                    ImageScaling = ToolStripItemImageScaling.None
                };
                item.Click += ocrSpiltModelItem_Click;
                if (model.Equals(CurrentSpiltModel)) item.Checked = true;
                tmsSpiltMode.DropDownItems.Add(item);
            }
        }

        private void InitBiaoDian()
        {
            tsmBiaoDian.DropDownItems.Clear();
            foreach (var model in Enum.GetValues(typeof(BiaoDianMode)))
            {
                var item = new ToolStripMenuItem
                {
                    Text = model.ToString() + "标点",
                    Tag = model.GetHashCode(),
                    Font = tsmContentType.Font,
                    Image = Resources.ResourceManager.GetObject(model.ToString() + "标点") as Bitmap,
                    ImageScaling = ToolStripItemImageScaling.None
                };
                item.Click += ocrBiaoDianTypeItem_Click;
                if (model.Equals(NowBiaoDianMode)) item.Checked = true;
                tsmBiaoDian.DropDownItems.Add(item);
            }
        }

        private void ocrBiaoDianTypeItem_Click(object sender, EventArgs e)
        {
            var item = sender as ToolStripMenuItem;
            NowBiaoDianMode = (BiaoDianMode)BoxUtil.GetInt32FromObject(item.Tag?.ToString());

            tsmBiaoDian.Image = item.Image;
            tsmBiaoDian.Text = item.Text;
            item.Checked = true;

            foreach (ToolStripMenuItem dropDownItem in tsmBiaoDian.DropDownItems)
            {
                if (!Equals(item, dropDownItem))
                {
                    dropDownItem.Checked = false;
                }
            }

            SetUcContentBiaoDianMode(NowBiaoDianMode);

            CommonSetting.SetValue("标点模式", NowBiaoDianMode.ToString());
        }

        #endregion

        #region 绑定识别结果

        private bool IsClosingTabs;

        private void CloseAllTabs()
        {
            IsClosingTabs = true;
            try
            {
                CommonMethod.DetermineCall(this, delegate
                 {
                     tbMain.Tag = null;
                     content.Tag = null;
                     tbMain.SelectedTab = tbContentText;
                     tbContentText.Text = "【识别内容】";
                     tbContentText.Name = "识别内容";
                     content.BindContentByStr("");
                     try
                     {
                         for (var i = 0; i < tbMain.TabCount; i++)
                         {
                             var itemPage = tbMain.TabPages[i];
                             if (itemPage == tbContentText || itemPage == tbImageBox) continue;
                             tbMain.TabPages.Remove(itemPage);
                             i--;
                         }
                     }
                     catch (Exception oe)
                     {
                         Console.WriteLine(oe.Message);
                     }
                 });
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe);
            }
            finally
            {
                IsClosingTabs = false;
            }
        }

        private void LoadOtherOcrResult(string tagId, ProcessBy processBy, string fileIdentity, bool sync = true)
        {
            if (!sync || Program.NowUser?.IsSetOtherResult == true)
            {
                if (sync)
                {
                    Task.Factory.StartNew(() => { GetResultProcess(tagId, processBy, fileIdentity); });
                }
                else
                {
                    Thread.Sleep(800);
                    GetResultProcess(tagId, processBy, fileIdentity, true);
                }
            }
        }

        private void GetResultProcess(string tagId, ProcessBy processBy, string fileIdentity,
            bool hasResultBreak = false)
        {
            var loopCount = hasResultBreak ? 5 : 20;
            for (var i = 0; i < loopCount; i++)
            {
                var id = Equals(processBy, ProcessBy.主界面) || Equals(processBy, ProcessBy.固定区域)
                    ? tbMain.Tag as string
                    : Equals(processBy, ProcessBy.划词翻译)
                        ? frmOCR.Tag as string
                        : tagId;
                if (string.IsNullOrEmpty(id) || !id.Equals(tagId)) return;
                var lstResult = OcrHelper.GetResultById(id);
                if (lstResult?.Count > 0)
                {
                    if (!hasResultBreak) loopCount = Math.Min(loopCount, i + 3);
                    lstResult.ForEach(ocr =>
                    {
                        ocr.Identity = fileIdentity;
                        ocr.ProcessBy = processBy;
                        OcrResultPool.Add(ocr);
                    });
                }

                if (hasResultBreak && lstResult?.Count > 0)
                    break;
                Thread.Sleep(3000);
            }
        }

        private void BindResult(OcrContent item)
        {
            if (IsClosingTabs) return;
            if (item?.result == null) return;
            var tabTitle = string.Format("【{0}】", item.processName);
            var tabId = item.processId.ToString();

            if (item.ocrType.Equals(OcrType.翻译)
                && imageBox.OriginImage == null
                && item.result.resultType == ResutypeEnum.文本)
            {
                tabTitle = "【文本翻译】";
                tabId = "TransResult";
                if (BindTransResult(tabId, item)) return;
            }

            if (content.Tag == null)
            {
                tbContentText.Padding = Padding.Empty;
                tbContentText.Text = tabTitle;
                tbContentText.Name = tabId;

                content.Tag = item.processName;

                BindUcContent(content, item);
            }
            else
            {
                var tabPag = new TabPage
                {
                    Padding = Padding.Empty,
                    Text = tabTitle,
                    Name = tabId
                };
                var ucContent = new UcContent
                {
                    Dock = DockStyle.Fill,
                    SpiltModel = CurrentSpiltModel,
                    IsShowOldContent = IsShowOldContent,
                    MenuStrip = content.MenuStrip
                };

                ucContent.TxtKeyDownEventDelegate = TxtContent_KeyDown;
                ucContent.SetDragDrop();
                tabPag.Controls.Add(ucContent);
                tbMain.TabPages.Add(tabPag);

                BindUcContent(ucContent, item);

                //ucContent.BindContentByOcr(item);
            }
        }

        private void BindToolSearch(OcrContent item)
        {
            var ucContent = frmOCR?.GetUcContent();
            ucContent.SpiltModel = CurrentSpiltModel;
            ucContent.IsShowOldContent = IsShowOldContent;
            //ucContent.ContentBackColor = DefaultTxtColor;
            //ucContent.MenuStrip = content.MenuStrip;

            BindUcContent(ucContent, item, Equals(item.id, frmOCR.Tag?.ToString()));
        }

        private bool BindTransResult(string transKey, OcrContent ocr)
        {
            var result = false;
            var controls = Controls.Find(transKey, true);
            if (controls.Length > 0)
                foreach (var item in controls[0].Controls)
                    if (item is UcContent)
                    {
                        BindUcContent(item as UcContent, ocr, true);
                        result = true;
                        break;
                    }

            return result;
        }

        private void BindUcContent(UcContent ucContent, OcrContent ocr, bool isAppend = false)
        {
            ucContent.Image = imageBox.OriginImage;
            //if (IsOnFixedFieldCapture)
            //{
            //    ucContent.NowDisplayMode = DisplayModel.文字模式;
            //}
            //else
            //{
            //    ucContent.NowDisplayMode = NowDisplayMode;
            //}
            ucContent.NowDisplayMode = NowDisplayMode;
            ucContent.BiaoDianMode = NowBiaoDianMode;
            ucContent.RefreshStyle();
            ucContent.BindContentByOcr(ocr, isAppend, Equals(ocr.ProcessBy, ProcessBy.固定区域) || CommonSetting.识别之后显示文字预览);
            //ucContent.Focus();
        }

        #endregion

        #region 设置相关

        [DllImport("user32.dll", CharSet = CharSet.Auto)]
        private static extern bool SwitchToThisWindow(IntPtr hWnd, bool fAltTab);

        private void tsmShowTool_Click(object sender, EventArgs e)
        {
            FrmTool.VisibleChange();
            CommonSetting.SetValue("显示工具栏", FrmTool.Visible);
        }

        private void tsmShowMain_Click(object sender, EventArgs e)
        {
            if (Visible)
                Visible = false;
            else
                ShowWindow();
        }

        private void notifyMain_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            ShowWindow();
        }

        private void NotifyMain_BalloonTipClicked(object sender, EventArgs e)
        {
            var action = notifyMain.Tag as BalloonTipAction;

            if (action != null && !string.IsNullOrEmpty(action.Text))
                switch (action.ClickAction)
                {
                    case BalloonTipClickAction.OpenUrl:
                        CommonMethod.OpenUrl(action.Text);
                        break;
                    case BalloonTipClickAction.OpenForm:
                        CommonMethod.OpenForm(action.Text);
                        break;
                }
        }

        private bool IsOverlapped(Form form)
        {
            return isoverlapped(form.Handle, form);
        }

        private bool isoverlapped(IntPtr win, Form form)
        {
            try
            {
                var preWin = NativeMethods.GetWindow(win, 3); //获取显示在Form之上的窗口

                if (preWin == null || preWin == IntPtr.Zero)
                    return false;

                if (!NativeMethods.IsWindowVisible(preWin))
                    return isoverlapped(preWin, form);

                var rect = new RECT();
                if (NativeMethods.GetWindowRect(preWin, out rect)) //获取窗体矩形
                {
                    var winrect = new Rectangle(rect.Left, rect.Top, rect.Right - rect.Left, rect.Bottom - rect.Top);

                    if (winrect.Width == Screen.PrimaryScreen.WorkingArea.Width &&
                        winrect.Y == Screen.PrimaryScreen.WorkingArea.Height) //菜单栏。不判断遮挡（可略过）
                        return isoverlapped(preWin, form);

                    if (winrect.X == 0 && winrect.Width == 54 && winrect.Height == 54) //开始按钮。不判断遮挡（可略过）
                        return isoverlapped(preWin, form);

                    var formRect = new Rectangle(form.Location, form.Size); //Form窗体矩形
                    if (formRect.IntersectsWith(winrect)) //判断是否遮挡
                        return true;
                }

                return isoverlapped(preWin, form);
            }
            catch
            {
            }

            return false;
        }

        private void ShowWindow()
        {
            CommonMethod.DetermineCall(this, delegate
             {
                 if (!Visible)
                 {
                     Visible = true;
                     Show();
                 }

                 if (WindowState == FormWindowState.Minimized) WindowState = FormWindowState.Normal;
                 Invalidate();
                 if (IsOverlapped(this))
                 {
                     BringToFront();
                     if (!TopMost)
                     {
                         TopMost = true;
                         TopMost = false;
                     }

                     SwitchToThisWindow(Handle, true);
                 }

                 Application.DoEvents();
             });
        }

        private void pnlTop_Click(object sender, EventArgs e)
        {
            SetTopMost(!TopMost);
            CommonSetting.SetValue("窗体置顶", TopMost);
        }

        private void pnlSetting_Click(object sender, EventArgs e)
        {
            cmsNotify.Show(pnlSetting, new Point(0, 32));
        }

        private void tsmExit_Click(object sender, EventArgs e)
        {
            UnRegAllHandle();
            FrmTool.Hide();
            Hide();
            notifyMain.Dispose();
            CommonMethod.Exit();
        }

        private const int WM_HOTKEY = 0x312; //窗口消息-热键
        private const int WM_CREATE = 0x1; //窗口消息-创建
        private const int WM_DESTROY = 0x2; //窗口消息-销毁

        private bool IsOnSetting;

        internal static List<HotKeyEntity> LstHotKeys = new List<HotKeyEntity>();
        private const ushort Hot_CopyKey = 0x3454;
        private const ushort Hot_OCRKey = 0x3455;
        private const ushort Hot_TextKey = 0x3456;
        private const ushort Hot_ShuKey = 0x3457;
        private const ushort Hot_MathKey = 0x3458;
        private const ushort Hot_TableKey = 0x3459;
        private const ushort Hot_TransKey = 0x3460;
        private const ushort Hot_SearchKey = 0x3461;
        private const ushort Hot_CapturePasteKey = 0x3462;
        private const ushort Hot_TxtPasteKey = 0x3463;
        private const ushort Hot_CaptureCopyKey = 0x3464;
        private const ushort Hot_GunDongCaptureKey = 0x3465;
        private const ushort Hot_CaptureKey = 0x3466;

        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m);
            switch (m.Msg)
            {
                case WM_HOTKEY: //窗口消息-热键ID
                    if (!IsOnSetting)
                        switch (m.WParam.ToInt32())
                        {
                            case Hot_TextKey:
                                截图识别ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_CaptureCopyKey:
                                var itemCaptureCopy = CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.截图);
                                ProcessCaptureClickAction(itemCaptureCopy, null);
                                break;
                            case Hot_CaptureKey:
                                var itemCapture = CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.截图);
                                ProcessCaptureClickAction(itemCapture, null);
                                break;
                            case Hot_CapturePasteKey:
                                var itemCapturePasteKey = CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.截图贴图);
                                ProcessCaptureClickAction(itemCapturePasteKey, null);
                                break;
                            case Hot_TxtPasteKey:
                                文字贴图ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_CopyKey:
                                CopyTransAction();
                                break;
                            case Hot_OCRKey:
                                InitItemTypeByValue(tsmContentType, OcrType.文本.GetHashCode().ToString());
                                截图识别ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_ShuKey:
                                InitItemTypeByValue(tsmContentType, OcrType.竖排.GetHashCode().ToString());
                                截图识别ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_MathKey:
                                InitItemTypeByValue(tsmContentType, OcrType.公式.GetHashCode().ToString());
                                截图识别ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_TableKey:
                                InitItemTypeByValue(tsmContentType, OcrType.表格.GetHashCode().ToString());
                                截图识别ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_TransKey:
                                InitItemTypeByValue(tsmContentType, OcrType.翻译.GetHashCode().ToString());
                                截图识别ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_SearchKey:
                                截图识别ToolStripMenuItem_Click("搜索", null);
                                break;
                        }

                    break;
                case WM_CREATE: //窗口消息-创建
                    //RegisterClipboard();
                    RegAll(true);
                    break;
                case WM_DESTROY: //窗口消息-销毁
                    UnRegAllHandle();
                    break;
                    //case WM_DRAWCLIPBOARD:
                    //    //将WM_DRAWCLIPBOARD消息传递到下一个观察链中的窗口
                    //    SendMessage(NextClipHwnd, m.Msg, m.WParam, m.LParam);
                    //    //获取Clipboard内容，并处理
                    //    var text = ClipboardService.GetText();
                    //    if (!string.IsNullOrEmpty(text))
                    //    {
                    //        ClipboardTexts.Add(text);
                    //    }
                    //    break;
            }
        }

        private void UnRegAllHandle()
        {
            //try
            //{
            //    UnRegisterClipboard();
            //}
            //catch { }
            try
            {
                UnRegAll();
            }
            catch
            {
            }
        }

        private void RegHotKey(List<HotKeyEntity> lstKeys)
        {
            foreach (var entity in lstKeys)
                try
                {
                    UnRegHotKey(entity.Id);
                    HotKeyHelper.RegKey(Handle, entity);
                }
                catch
                {
                }
        }

        private void UnRegHotKey(int keyCode)
        {
            try
            {
                HotKeyHelper.UnRegKey(Handle, keyCode);
            }
            catch
            {
            }
        }

        ////存放观察链中下一个窗口句柄   
        //IntPtr NextClipHwnd;

        //BlockingCollection<string> ClipboardTexts = new BlockingCollection<string>();

        //// SetClipboardViewer 用于往观察链中添加一个窗口句柄，这个窗口就可成为观察链中的一员了，返回值指向下一个观察者
        //[System.Runtime.InteropServices.DllImport("user32")]
        //private static extern IntPtr SetClipboardViewer(IntPtr hwnd);

        ////ChangeClipboardChain删除由hwnd指定的观察链成员，这是一个窗口句柄，第二个参数hWndNext是观察链中下一个窗口的句柄
        //[System.Runtime.InteropServices.DllImport("user32")]
        //private static extern IntPtr ChangeClipboardChain(IntPtr hwnd, IntPtr hWndNext);

        ////SendMessage 发送消息
        //[System.Runtime.InteropServices.DllImport("user32")]
        //private static extern int SendMessage(IntPtr hwnd, int wMsg, IntPtr wParam, IntPtr lParam);

        ////Clipboard内容变化消息
        //const int WM_DRAWCLIPBOARD = 0x308;
        ////Clipboard观察链变化消息
        //const int WM_CHANGECBCHAIN = 0x30D;

        ////private ClipboardListener ClipboardListener;
        ////public static ClipboardNative ClipboardNativeService;
        //private void RegisterClipboard()
        //{
        //    //获得观察链中下一个窗口句柄
        //    NextClipHwnd = SetClipboardViewer(this.Handle);
        //    //ClipboardNativeService = new ClipboardNative(new ActiveWindowListener());
        //    //ClipboardListener = new ClipboardListener(ClipboardNativeService);
        //    //ClipboardListener.Start(true);
        //    FileStatusResultProcess();
        //}

        //private void UnRegisterClipboard()
        //{
        //    //从观察链中删除本观察窗口
        //    ChangeClipboardChain(this.Handle, NextClipHwnd);
        //    //将变动消息WM_CHANGECBCHAIN消息传递到下一个观察链中的窗口
        //    SendMessage(NextClipHwnd, WM_CHANGECBCHAIN, this.Handle, NextClipHwnd);
        //    //if (ClipboardListener != null)
        //    //{
        //    //    ClipboardListener.Stop();
        //    //    ClipboardListener.Dispose();
        //    //}
        //}

        private void tsmTrans_Click(object sender, EventArgs e)
        {
            var txt = GetCurrentTxt(false);
            TransByText(txt);
        }

        private void TransByText(string content)
        {
            if (!CommonString.IsOnRec && !string.IsNullOrEmpty(content))
            {
                SetPicImage(null);
                OcrPoolProcess.ProcessByText(NowOcrGroupType, NowBiaoDianMode, CurrentTranslateFrom, CurrentTranslateTo,
                    content, ProcessBy.主界面, null);
            }
        }

        //private void FileStatusResultProcess()
        //{
        //    new Thread(p =>
        //    {
        //        try
        //        {
        //            foreach (var content in ClipboardTexts.GetConsumingEnumerable())
        //            {
        //                try
        //                {
        //                    //if (IsCopyTrans && NowOcrType == OCRType.翻译)
        //                    //{
        //                    //    TransByText(content);
        //                    //}
        //                    ////CodeProcessHelper.SetFileStatusResult(content);
        //                }
        //                catch (Exception oe)
        //                {
        //                    Console.WriteLine(oe.Message);
        //                }
        //            }
        //        }
        //        catch (Exception oe)
        //        {
        //            Console.WriteLine(oe.Message);
        //        }
        //    })
        //    { IsBackground = true, Priority = ThreadPriority.Highest }.Start();
        //}

        private void IniRegKey(HotKeyEntity entity)
        {
            var strKey = CommonSetting.GetValue<string>("快捷键", entity.KeyName.Trim())?.ToString();
            if (string.IsNullOrEmpty(strKey))
            {
                entity.Hotkey = entity.DefaultKey;
            }
            else
            {
                if (BoxUtil.IsInt(strKey))
                {
                    entity.Hotkey = (Keys)BoxUtil.GetInt32FromObject(strKey);
                }
                else
                {
                    if (Enum.TryParse(strKey, out Keys keys))
                    {
                        entity.Hotkey = keys;
                    }
                }
            }
        }

        private void InitHotKeyList()
        {
            var freeCapture = new HotKeyEntity(Hot_CaptureKey)
            {
                DefaultKey = Keys.PrintScreen,
                KeyName = "框选截图",
                Desc = "框选矩形截图",
                Group = HotKeyType.截图贴图
            };
            IniRegKey(freeCapture);
            LstHotKeys.Add(freeCapture);
            var capture = new HotKeyEntity(Hot_CaptureCopyKey)
            {
                DefaultKey = Keys.F1,
                KeyName = "截图复制",
                Desc = "截图并复制",
                Group = HotKeyType.截图贴图
            };
            IniRegKey(capture);
            LstHotKeys.Add(capture);
            var capturePaste = new HotKeyEntity(Hot_CapturePasteKey)
            {
                DefaultKey = Keys.F2,
                KeyName = "截图贴图",
                Desc = "截图并贴图到屏幕上",
                Group = HotKeyType.截图贴图
            };
            IniRegKey(capturePaste);
            LstHotKeys.Add(capturePaste);
            var txtPaste = new HotKeyEntity(Hot_TxtPasteKey)
            {
                DefaultKey = Keys.F6,
                KeyName = "一键贴图",
                Desc = "将复制的文字或图片贴到屏幕上",
                Group = HotKeyType.截图贴图
            };
            IniRegKey(txtPaste);
            LstHotKeys.Add(txtPaste);
            var txtGunDong = new HotKeyEntity(Hot_GunDongCaptureKey)
            {
                KeyName = "滚动截图",
                Desc = "滚动截屏",
                Group = HotKeyType.截图贴图
            };
            IniRegKey(txtGunDong);
            LstHotKeys.Add(txtGunDong);
            var captureEntity = new HotKeyEntity(Hot_TextKey)
            {
                DefaultKey = Keys.F3,
                KeyName = "截图识别",
                Desc = "截图并以【当前选择的模式】识别",
                Group = HotKeyType.OCR识别
            };
            IniRegKey(captureEntity);
            LstHotKeys.Add(captureEntity);
            var ocrEntity = new HotKeyEntity(Hot_OCRKey)
            {
                DefaultKey = Keys.F4,
                KeyName = "文字识别",
                Desc = "截图并以【文字模式】识别",
                Group = HotKeyType.OCR识别
            };
            IniRegKey(ocrEntity);
            LstHotKeys.Add(ocrEntity);
            var shuEntity = new HotKeyEntity(Hot_ShuKey)
            {
                KeyName = "竖排识别",
                Desc = "截图并以【竖排文字模式】识别",
                Group = HotKeyType.OCR识别
            };
            IniRegKey(shuEntity);
            LstHotKeys.Add(shuEntity);
            var mathEntity = new HotKeyEntity(Hot_MathKey)
            {
                KeyName = "公式识别",
                Desc = "截图并以【公式模式】识别",
                Group = HotKeyType.OCR识别
            };
            IniRegKey(mathEntity);
            LstHotKeys.Add(mathEntity);
            var tableEntity = new HotKeyEntity(Hot_TableKey)
            {
                KeyName = "表格识别",
                Desc = "截图并以【表格模式】识别",
                Group = HotKeyType.OCR识别
            };
            IniRegKey(tableEntity);
            LstHotKeys.Add(tableEntity);
            var transEntity = new HotKeyEntity(Hot_TransKey)
            {
                KeyName = "翻译识别",
                Desc = "截图并以【翻译模式】识别",
                Group = HotKeyType.OCR识别
            };
            IniRegKey(transEntity);
            LstHotKeys.Add(transEntity);
            var searchEntity = new HotKeyEntity(Hot_SearchKey)
            {
                KeyName = "搜索识别",
                Desc = "截图并以【搜索模式】识别",
                Group = HotKeyType.OCR识别
            };
            IniRegKey(searchEntity);
            LstHotKeys.Add(searchEntity);
            var copyOcrEntity = new HotKeyEntity(Hot_CopyKey)
            {
                KeyName = "粘贴识别",
                Desc = "粘贴并以【当前选择的模式】识别",
                Group = HotKeyType.OCR识别
            };
            IniRegKey(copyOcrEntity);
            LstHotKeys.Add(copyOcrEntity);
            LstHotKeys.Reverse();
        }

        private void RegAll(bool isFirst)
        {
            if (LstHotKeys.Count <= 0) InitHotKeyList();
            IsOnSetting = true;
            RegHotKey(LstHotKeys);
            //RegHotKey(StrKey_Trans);
            IsOnSetting = false;
            if (isFirst) ShowNotifyMessage();
        }

        private void ShowNotifyMessage()
        {
            var sb = new StringBuilder();
            foreach (var item in LstHotKeys)
                if (!string.IsNullOrEmpty(item.StrKey) && !Equals(item.StrKey, CommonString.StrDefaultDesc))
                    sb.AppendLine(string.Format("{0}:{1}", item.KeyName, item.StrKey));
            var strMsg = string.Empty;
            if (sb.Length <= 0)
                strMsg = "当前无快捷键。";
            else
                strMsg = string.Format("【当前已设置快捷键】\n{0}", sb);
            strMsg += "\n\n更多快捷键，请在\n系统设置->快捷键 中设置！";
            CommonMethod.ShowNotificationTip(strMsg, null, 5 * 1000);
            //notifyMain.ShowBalloonTip(5000, Application.ProductName, string.Format("小主!我在这里，有事双击我！\n\n{0}\n\n", strMsg), ToolTipIcon.Info);
        }

        private void UnRegAll()
        {
            foreach (var item in LstHotKeys) UnRegHotKey(item.Id);
            //UnRegHotKey(StrKey_Trans);
        }

        private void 系统设置SToolStripMenuItem_Click(object sender, EventArgs e)
        {
            IsOnSetting = true;
            var frmSetting = new FormSetting
            { Icon = Icon, Theme = msManager.Theme, Style = msManager.Style, StyleManager = msManager };
            frmSetting.TopMost = true;
            if (!Visible) frmSetting.StartPosition = FormStartPosition.CenterScreen;
            if (Equals(sender, 关于我们ToolStripMenuItem))
            {
                frmSetting.OpenTab = "关于";
            }
            else if (Equals(sender, tsmToolImage))
            {
                frmSetting.OpenTab = "界面";
            }
            frmSetting.ShowDialog(this);

            SetTopMost(CommonSetting.窗体置顶);

            CommonUpdate.InitUpdate();

            //SetTheme();
            CommonMsg.Refresh();

            RefreshUcContent();

            UnRegAll();
            RegAll(false);

            SetSearchEngine();
            NowLoadingType = (LoadingType)Enum.Parse(typeof(LoadingType), CommonSetting.加载动画);

            IsOnSetting = false;
            if (Visible) this.ForceActivate();
        }

        #endregion

        #region 检查更新相关

        private void CheckLoginStatus()
        {
            try
            {
                if (Program.NowUser?.IsLogined == true)
                    if (OcrHelper.IsLogouted(Program.NowUser?.Account, Program.NowUser?.Token))
                    {
                        Program.NowUser = null;
                        BindUserInfo();
                    }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        #endregion

        #region 登录相关

        private void InitNetWorkInfo()
        {
            SetNowNetWork(CommonString.IsOnLine, true);
            NetworkChange.NetworkAvailabilityChanged += NetworkChange_NetworkAvailabilityChanged;
        }

        private void NetworkChange_NetworkAvailabilityChanged(object sender, NetworkAvailabilityEventArgs e)
        {
            SetNowNetWork(e.IsAvailable);
        }

        private void SetNowNetWork(bool isAvailable, bool isUserChange = false)
        {
            CommonString.IsOnLine = isAvailable;
            if (isUserChange)
                CommonString.IsOnLine = NetworkInterface.GetIsNetworkAvailable();
            else
                NetWorkChangeEvent();
            //tipMain.SetToolTip(pnlNetWork, "本地网络" + (CommonString.IsOnLine ? "正常" : "异常"));
            //lnkLogin.Image = CommonString.IsOnLine ? Properties.Resources.Info_OK : Properties.Resources.Info_Error;
        }

        private void NetWorkChangeEvent()
        {
            BindUserInfo();
            if (CommonString.IsOnLine)
                if (Program.NowUser == null || !Program.NowUser.IsLogined)
                    DoLoginSilence();
        }

        private void DoLoginSilence()
        {
            var account = IniHelper.GetValue("配置", "Account");
            var pwd = IniHelper.GetValue("配置", "Password");
            if (!string.IsNullOrEmpty(account) && !string.IsNullOrEmpty(pwd))
            {
                var strMsg = "";
                var result = OcrHelper.DoLogin(account, pwd, ref strMsg);
                BindUserInfo();
            }
        }

        private void metroLink1_Click(object sender, EventArgs e)
        {
            if (Program.NowUser == null)
            {
                var frmLogin = new FrmLogin
                { Icon = Icon, Theme = msManager.Theme, Style = msManager.Style, StyleManager = msManager };
                frmLogin.ShowDialog(this);
            }
            else
            {
                // 显示用户信息
                var frmUser = new FrmUserInfo
                { Icon = Icon, Theme = msManager.Theme, Style = msManager.Style, StyleManager = msManager };
                frmUser.ShowDialog(this);
                ShowMsgControl();
            }

            BindUserInfo();
            SendKeys.Send("{Tab}");
        }

        private Bitmap lnkLoginImage;

        private void BindUserInfo()
        {
            lnkLogin.AutoSize = true;
            lnkLoginImage = null;
            if (Program.NowUser == null || Program.NowUser.IsLogined == false)
            {
                lnkLogin.Text = "登录/注册";
                tipMain.SetToolTip(lnkLogin, "  点击登录/注册账号   ");
            }
            else
            {
                lnkLogin.Text = Program.NowUser.NickName;
                switch (Program.NowUser.UserType)
                {
                    case UserTypeEnum.专业版:
                        lnkLoginImage = Resources.vip_1;
                        break;
                    case UserTypeEnum.企业版:
                        lnkLoginImage = Resources.vip_2;
                        break;
                    case UserTypeEnum.旗舰版:
                        lnkLoginImage = Resources.vip_3;
                        break;
                    default:
                        lnkLoginImage = Resources.vip_0;
                        break;
                }

                tipMain.SetToolTip(lnkLogin, string.Format("{0}-{1}"
                    , string.IsNullOrEmpty(Program.NowUser.NickName)
                        ? Program.NowUser.Account
                        : Program.NowUser.NickName
                    , Program.NowUser.UserType.ToString()
                ));
            }

            if (lnkLogin.Width > 150)
            {
                lnkLogin.AutoSize = false;
                lnkLogin.Width = 150;
            }

            lnkLogin.Location = new Point(pnlTop.Location.X - lnkLogin.Size.Width, 7);
            BindUserImg();
            ProcessForbidControls();
        }

        private void BindUserImg()
        {
            picUser.Image = lnkLoginImage ?? Resources.qqKeFu;
            picUser.Size = picUser.Image.Size;
            picUser.Location = new Point(lnkLogin.Location.X - picUser.Image.Width,
                lnkLogin.Location.Y + (lnkLogin.Height - picUser.Height) / 2);
            picUser.BringToFront();
            picUser.Invalidate();
        }

        #endregion
    }

    //internal class HotKeyEntity
    //{
    //    public string KeyName { get; set; }

    //    public string DefaultKey { get; set; }

    //    public string StrKey { get; set; }

    //    public int UserKey { get; set; }

    //    public int ID { get; set; }

    //    public string Desc { get; set; }

    //    public HotKeyType Group { get; set; }
    //}

    internal enum HotKeyType
    {
        OCR识别 = 0,
        截图贴图 = 1,
        其他功能 = 2
    }

    internal enum Speaker
    {
        可爱正太 = 0,
        磁性男声 = 1,
        时尚男声 = 4,
        萝莉女声 = 2,
        知性女声 = 3,
        亲切女声 = 6
    }

    internal class SearchEngine
    {
        public string Name { get; set; }

        public string Url { get; set; }
    }
}