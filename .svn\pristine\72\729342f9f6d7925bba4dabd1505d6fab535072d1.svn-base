// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Collections;
using System.Runtime.InteropServices;
using System.Runtime.Serialization;
using System.Security.Permissions;
using UIAComWrapperInternal;

namespace System.Windows.Automation
{
    #region Well-known properties

    public static class AutomationElementIdentifiers
    {
        public static readonly AutomationProperty BoundingRectangleProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.BoundingRectangle_Property,
                "AutomationElementIdentifiers.BoundingRectangleProperty");

        public static readonly AutomationProperty ClassNameProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.ClassName_Property,
                "AutomationElementIdentifiers.ClassNameProperty");

        public static readonly AutomationProperty ClickablePointProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.ClickablePoint_Property,
                "AutomationElementIdentifiers.ClickablePointProperty");

        public static readonly AutomationProperty ControlTypeProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.ControlType_Property,
                "AutomationElementIdentifiers.ControlTypeProperty");

        public static readonly AutomationProperty HasKeyboardFocusProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.HasKeyboardFocus_Property,
                "AutomationElementIdentifiers.HasKeyboardFocusProperty");

        public static readonly AutomationProperty IsContentElementProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.IsContentElement_Property,
                "AutomationElementIdentifiers.IsContentElementProperty");

        public static readonly AutomationProperty IsControlElementProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.IsControlElement_Property,
                "AutomationElementIdentifiers.IsControlElementProperty");

        public static readonly AutomationProperty IsKeyboardFocusableProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.IsKeyboardFocusable_Property,
                "AutomationElementIdentifiers.IsKeyboardFocusableProperty");

        public static readonly AutomationProperty IsOffscreenProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.IsOffscreen_Property,
                "AutomationElementIdentifiers.IsOffscreenProperty");

        public static readonly AutomationProperty IsPasswordProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.IsPassword_Property,
                "AutomationElementIdentifiers.IsPasswordProperty");

        public static readonly AutomationProperty NameProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.Name_Property,
                "AutomationElementIdentifiers.NameProperty");

        public static readonly AutomationProperty NativeWindowHandleProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.NewNativeWindowHandle_Property,
                "AutomationElementIdentifiers.NativeWindowHandleProperty");

        public static readonly object NotSupported = Automation.Factory.ReservedNotSupportedValue;

        public static readonly AutomationProperty OrientationProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.Orientation_Property,
                "AutomationElementIdentifiers.OrientationProperty");
    }

    public static class DockPatternIdentifiers
    {
        public static readonly AutomationProperty DockPositionProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.Dock_Position_Property,
                "DockPatternIdentifiers.DockPositionProperty");

        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.Dock_Pattern, "DockPatternIdentifiers.Pattern");
    }

    public static class ExpandCollapsePatternIdentifiers
    {
        public static readonly AutomationProperty ExpandCollapseStateProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.ExpandCollapse_State_Property,
                "ExpandCollapsePatternIdentifiers.ExpandCollapseStateProperty");

        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.ExpandCollapse_Pattern,
                "ExpandCollapsePatternIdentifiers.Pattern");
    }

    public static class GridItemPatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.GridItem_Pattern,
                "GridItemPatternIdentifiers.Pattern");
    }

    public static class GridPatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.Grid_Pattern, "GridPatternIdentifiers.Pattern");
    }

    public static class InvokePatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.Invoke_Pattern, "InvokePatternIdentifiers.Pattern");
    }

    public static class MultipleViewPatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.MultipleView_Pattern,
                "MultipleViewPatternIdentifiers.Pattern");
    }

    public static class RangeValuePatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.RangeValue_Pattern,
                "RangeValuePatternIdentifiers.Pattern");
    }

    public static class ScrollItemPatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.ScrollItem_Pattern,
                "ScrollItemPatternIdentifiers.Pattern");
    }

    public static class ScrollPatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.Scroll_Pattern, "ScrollPatternIdentifiers.Pattern");
    }

    public static class SelectionItemPatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.SelectionItem_Pattern,
                "SelectionItemPatternIdentifiers.Pattern");
    }

    public static class SelectionPatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.Selection_Pattern,
                "SelectionPatternIdentifiers.Pattern");
    }

    public static class TableItemPatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.TableItem_Pattern,
                "TableItemPatternIdentifiers.Pattern");
    }

    public static class TablePatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.Table_Pattern, "TablePatternIdentifiers.Pattern");

        public static readonly AutomationProperty RowOrColumnMajorProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.Table_RowOrColumnMajor_Property,
                "TablePatternIdentifiers.RowOrColumnMajorProperty");
    }

    public static class TextPatternIdentifiers
    {
        public static readonly AutomationTextAttribute AnimationStyleAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_AnimationStyle_Attribute,
                "TextPatternIdentifiers.AnimationStyleAttribute");

        public static readonly AutomationTextAttribute BackgroundColorAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_BackgroundColor_Attribute,
                "TextPatternIdentifiers.BackgroundColorAttribute");

        public static readonly AutomationTextAttribute BulletStyleAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_BulletStyle_Attribute,
                "TextPatternIdentifiers.BulletStyleAttribute");

        public static readonly AutomationTextAttribute CapStyleAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_CapStyle_Attribute,
                "TextPatternIdentifiers.CapStyleAttribute");

        public static readonly AutomationTextAttribute CultureAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_Culture_Attribute,
                "TextPatternIdentifiers.CultureAttribute");

        public static readonly AutomationTextAttribute FontNameAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_FontName_Attribute,
                "TextPatternIdentifiers.FontNameAttribute");

        public static readonly AutomationTextAttribute FontSizeAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_FontSize_Attribute,
                "TextPatternIdentifiers.FontSizeAttribute");

        public static readonly AutomationTextAttribute FontWeightAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_FontWeight_Attribute,
                "TextPatternIdentifiers.FontWeightAttribute");

        public static readonly AutomationTextAttribute ForegroundColorAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_ForegroundColor_Attribute,
                "TextPatternIdentifiers.ForegroundColorAttribute");

        public static readonly AutomationTextAttribute HorizontalTextAlignmentAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_HorizontalTextAlignment_Attribute,
                "TextPatternIdentifiers.HorizontalTextAlignmentAttribute");

        public static readonly AutomationTextAttribute IndentationFirstLineAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_IndentationFirstLine_Attribute,
                "TextPatternIdentifiers.IndentationFirstLineAttribute");

        public static readonly AutomationTextAttribute IndentationLeadingAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_IndentationLeading_Attribute,
                "TextPatternIdentifiers.IndentationLeadingAttribute");

        public static readonly AutomationTextAttribute IndentationTrailingAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_IndentationTrailing_Attribute,
                "TextPatternIdentifiers.IndentationTrailingAttribute");

        public static readonly AutomationTextAttribute IsHiddenAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_IsHidden_Attribute,
                "TextPatternIdentifiers.IsHiddenAttribute");

        public static readonly AutomationTextAttribute IsItalicAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_IsItalic_Attribute,
                "TextPatternIdentifiers.IsItalicAttribute");

        public static readonly AutomationTextAttribute IsReadOnlyAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_IsReadOnly_Attribute,
                "TextPatternIdentifiers.IsReadOnlyAttribute");

        public static readonly AutomationTextAttribute IsSubscriptAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_IsSubscript_Attribute,
                "TextPatternIdentifiers.IsSubscriptAttribute");

        public static readonly AutomationTextAttribute IsSuperscriptAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_IsSuperscript_Attribute,
                "TextPatternIdentifiers.IsSuperscriptAttribute");

        public static readonly AutomationTextAttribute MarginBottomAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_MarginBottom_Attribute,
                "TextPatternIdentifiers.MarginBottomAttribute");

        public static readonly AutomationTextAttribute MarginLeadingAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_MarginLeading_Attribute,
                "TextPatternIdentifiers.MarginLeadingAttribute");

        public static readonly AutomationTextAttribute MarginTopAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_MarginTop_Attribute,
                "TextPatternIdentifiers.MarginTopAttribute");

        public static readonly AutomationTextAttribute MarginTrailingAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_MarginTrailing_Attribute,
                "TextPatternIdentifiers.MarginTrailingAttribute");

        public static readonly AutomationTextAttribute OutlineStylesAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_OutlineStyles_Attribute,
                "TextPatternIdentifiers.OutlineStylesAttribute");

        public static readonly AutomationTextAttribute OverlineColorAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_OverlineColor_Attribute,
                "TextPatternIdentifiers.OverlineColorAttribute");

        public static readonly AutomationTextAttribute OverlineStyleAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_OverlineStyle_Attribute,
                "TextPatternIdentifiers.OverlineStyleAttribute");

        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.Text_Pattern, "TextPatternIdentifiers.Pattern");

        public static readonly AutomationTextAttribute StrikethroughColorAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_StrikethroughColor_Attribute,
                "TextPatternIdentifiers.StrikethroughColorAttribute");

        public static readonly AutomationTextAttribute StrikethroughStyleAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_StrikethroughStyle_Attribute,
                "TextPatternIdentifiers.StrikethroughStyleAttribute");

        public static readonly AutomationTextAttribute TabsAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_Tabs_Attribute,
                "TextPatternIdentifiers.TabsAttribute");

        public static readonly AutomationTextAttribute TextFlowDirectionsAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_FlowDirections_Attribute,
                "TextPatternIdentifiers.TextFlowDirectionsAttribute");

        public static readonly AutomationTextAttribute UnderlineColorAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_UnderlineColor_Attribute,
                "TextPatternIdentifiers.UnderlineColorAttribute");

        public static readonly AutomationTextAttribute UnderlineStyleAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_UnderlineStyle_Attribute,
                "TextPatternIdentifiers.UnderlineStyleAttribute");
    }

    public static class TogglePatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.Toggle_Pattern, "TogglePatternIdentifiers.Pattern");

        public static readonly AutomationProperty ToggleStateProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.Toggle_State_Property,
                "TogglePatternIdentifiers.ToggleStateProperty");
    }

    public static class TransformPatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.Transform_Pattern,
                "TransformPatternIdentifiers.Pattern");
    }

    public static class ValuePatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.Value_Pattern, "ValuePatternIdentifiers.Pattern");
    }

    public static class WindowPatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.Window_Pattern, "WindowPatternIdentifiers.Pattern");

        public static readonly AutomationProperty WindowInteractionStateProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.Window_InteractionState_Property,
                "WindowPatternIdentifiers.WindowInteractionStateProperty");

        public static readonly AutomationProperty WindowVisualStateProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.Window_VisualState_Property,
                "WindowPatternIdentifiers.WindowVisualStateProperty");
    }

    // New for Windows 7
    //
    public static class LegacyIAccessiblePatternIdentifiers
    {
        public static readonly AutomationPattern Pattern = AutomationPattern.Register(
            AutomationIdentifierGuids.LegacyIAccessible_Pattern, "LegacyIAccessiblePatternIdentifiers.Pattern");
    }

    public static class ItemContainerPatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.ItemContainer_Pattern,
                "ItemContainerPatternIdentifiers.Pattern");
    }

    public static class VirtualizedItemPatternIdentifiers
    {
        public static readonly AutomationPattern Pattern = AutomationPattern.Register(
            AutomationIdentifierGuids.VirtualizedItem_Pattern, "VirtualizedItemPatternIdentifiers.Pattern");
    }

    public static class SynchronizedInputPatternIdentifiers
    {
        public static readonly AutomationPattern Pattern = AutomationPattern.Register(
            AutomationIdentifierGuids.SynchronizedInput_Pattern, "SynchronizedInputPatternIdentifiers.Pattern");
    }

    // New for Windows 8
    //

    public static class ObjectModelPatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.ObjectModel_Pattern,
                "ObjectModelPatternIdentifiers.Pattern");
    }

    public static class AnnotationPatternIdentifiers
    {
        public static readonly AutomationProperty AnnotationTypeIdProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.Annotation_AnnotationTypeId_Property,
                "AnnotationPatternIdentifiers.AnnotationTypeIdProperty");

        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.Annotation_Pattern,
                "AnnotationPatternIdentifiers.Pattern");
    }

    public static class TextPattern2Identifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.Text_Pattern2, "TextPattern2Identifiers.Pattern");

        public static readonly AutomationTextAttribute AnnotationTypesAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_AnnotationTypes_Attribute,
                "TextPatternIdentifiers.AnnotationTypesAttribute");

        public static readonly AutomationTextAttribute AnnotationObjectsAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_AnnotationObjects_Attribute,
                "TextPatternIdentifiers.AnnotationObjectsAttribute");

        public static readonly AutomationTextAttribute StyleNameAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_StyleName_Attribute,
                "TextPatternIdentifiers.StyleNameAttribute");

        public static readonly AutomationTextAttribute StyleIdAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_StyleId_Attribute,
                "TextPatternIdentifiers.StyleIdAttribute");

        public static readonly AutomationTextAttribute LinkAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_Link_Attribute,
                "TextPatternIdentifiers.LinkAttribute");

        public static readonly AutomationTextAttribute IsActiveAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_IsActive_Attribute,
                "TextPatternIdentifiers.IsActiveAttribute");

        public static readonly AutomationTextAttribute SelectionActiveEndAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_SelectionActiveEnd_Attribute,
                "TextPatternIdentifiers.SelectionActiveEndAttribute");

        public static readonly AutomationTextAttribute CaretPositionAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_CaretPosition_Attribute,
                "TextPatternIdentifiers.CaretPositionAttribute");

        public static readonly AutomationTextAttribute CaretBidiModeAttribute =
            AutomationTextAttribute.Register(AutomationIdentifierGuids.Text_CaretBidiMode_Attribute,
                "TextPatternIdentifiers.CaretBidiModeAttribute");
    }

    public static class StylesPatternIdentifiers
    {
        public static readonly AutomationProperty StyleIdProperty =
            AutomationProperty.Register(AutomationIdentifierGuids.Styles_StyleId_Property,
                "StylesPatternIdentifiers.StyleIdProperty");

        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.Styles_Pattern, "StylesPatternIdentifiers.Pattern");
    }

    public static class SpreadsheetPatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.Spreadsheet_Pattern,
                "SpreadsheetPatternIdentifiers.Pattern");
    }

    public static class SpreadsheetItemPatternIdentifiers
    {
        public static readonly AutomationPattern Pattern = AutomationPattern.Register(
            AutomationIdentifierGuids.SpreadsheetItem_Pattern, "SpreadsheetItemPatternIdentifiers.Pattern");
    }

    public static class TransformPattern2Identifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.Tranform_Pattern2,
                "TransformPattern2Identifiers.Pattern");
    }

    public static class TextChildPatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.TextChild_Pattern,
                "TextChildPatternIdentifiers.Pattern");
    }

    public static class DragPatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.Drag_Pattern, "DragPatternIdentifiers.Pattern");
    }

    public static class DropTargetPatternIdentifiers
    {
        public static readonly AutomationPattern Pattern =
            AutomationPattern.Register(AutomationIdentifierGuids.DropTarget_Pattern,
                "DropTargetPatternIdentifiers.Pattern");
    }

    #endregion

    #region Identifier classes

    /// <summary>
    ///     Core Automation Identifier - essentially a wrapped integer
    /// </summary>
    public class AutomationIdentifier : IComparable
    {
        private static readonly Hashtable IdentifierDirectory = new Hashtable(200, 1f);
        private readonly UiaCoreIds.AutomationIdType _type;


        internal AutomationIdentifier(UiaCoreIds.AutomationIdType type, int id, Guid guid, string programmaticName)
        {
            Id = id;
            _type = type;
            ProgrammaticName = programmaticName;
        }


        public int Id { get; }

        public string ProgrammaticName { get; }

        public int CompareTo(object obj)
        {
            if (obj == null) throw new ArgumentNullException(nameof(obj));
            return GetHashCode() - obj.GetHashCode();
        }

        public override bool Equals(object obj)
        {
            AutomationIdentifier objAutomationIdentifier;
            if (obj == null || (objAutomationIdentifier = obj as AutomationIdentifier) == null) return false;

            return objAutomationIdentifier == this;
        }

        public override int GetHashCode()
        {
            return base.GetHashCode();
        }

        internal static AutomationIdentifier LookupById(UiaCoreIds.AutomationIdType type, int id)
        {
            AutomationIdentifier identifier;
            lock (IdentifierDirectory)
            {
                identifier = (AutomationIdentifier)IdentifierDirectory[id];
            }

            if (identifier == null) return null;
            if (identifier._type != type) return null;
            return identifier;
        }

        internal static AutomationIdentifier Register(UiaCoreIds.AutomationIdType type, Guid guid,
            string programmaticName)
        {
            var id = UiaCoreIds.UiaLookupId(type, ref guid);
            if (id == 0) return null;
            lock (IdentifierDirectory)
            {
                var identifier = (AutomationIdentifier)IdentifierDirectory[guid];
                if (identifier == null)
                {
                    switch (type)
                    {
                        case UiaCoreIds.AutomationIdType.Property:
                            identifier = new AutomationProperty(id, guid, programmaticName);
                            break;

                        case UiaCoreIds.AutomationIdType.Pattern:
                            identifier = new AutomationPattern(id, guid, programmaticName);
                            break;

                        case UiaCoreIds.AutomationIdType.Event:
                            identifier = new AutomationEvent(id, guid, programmaticName);
                            break;

                        case UiaCoreIds.AutomationIdType.ControlType:
                            identifier = new ControlType(id, guid, programmaticName);
                            break;

                        case UiaCoreIds.AutomationIdType.TextAttribute:
                            identifier = new AutomationTextAttribute(id, guid, programmaticName);
                            break;

                        default:
                            throw new InvalidOperationException("Invalid type specified for AutomationIdentifier");
                    }

                    IdentifierDirectory[id] = identifier;
                }

                return identifier;
            }
        }
    }

    public class AutomationEvent : AutomationIdentifier
    {
        internal AutomationEvent(int id, Guid guid, string programmaticName)
            : base(UiaCoreIds.AutomationIdType.Event, id, guid, programmaticName)
        {
        }

        internal static AutomationEvent Register(Guid guid, string programmaticName)
        {
            return (AutomationEvent)Register(UiaCoreIds.AutomationIdType.Event, guid, programmaticName);
        }
    }

    public class AutomationPattern : AutomationIdentifier
    {
        internal AutomationPattern(int id, Guid guid, string programmaticName)
            : base(UiaCoreIds.AutomationIdType.Pattern, id, guid, programmaticName)
        {
        }

        internal static AutomationPattern Register(Guid guid, string programmaticName)
        {
            return (AutomationPattern)Register(UiaCoreIds.AutomationIdType.Pattern, guid, programmaticName);
        }
    }


    public class AutomationProperty : AutomationIdentifier
    {
        internal AutomationProperty(int id, Guid guid, string programmaticName)
            : base(UiaCoreIds.AutomationIdType.Property, id, guid, programmaticName)
        {
        }

        internal static AutomationProperty Register(Guid guid, string programmaticName)
        {
            return (AutomationProperty)Register(UiaCoreIds.AutomationIdType.Property, guid, programmaticName);
        }
    }

    public class AutomationTextAttribute : AutomationIdentifier
    {
        internal AutomationTextAttribute(int id, Guid guid, string programmaticName)
            : base(UiaCoreIds.AutomationIdType.TextAttribute, id, guid, programmaticName)
        {
        }

        internal static AutomationTextAttribute Register(Guid guid, string programmaticName)
        {
            return (AutomationTextAttribute)Register(UiaCoreIds.AutomationIdType.TextAttribute, guid,
                programmaticName);
        }
    }

    public class ControlType : AutomationIdentifier
    {
        public static readonly ControlType Button = Register(AutomationIdentifierGuids.Button_Control,
            "ControlType.Button", new[] { new[] { InvokePatternIdentifiers.Pattern } });

        public static readonly ControlType Edit = Register(AutomationIdentifierGuids.Edit_Control, "ControlType.Edit",
            new[] { new[] { ValuePatternIdentifiers.Pattern } });

        public static readonly ControlType Group = Register(AutomationIdentifierGuids.Group_Control,
            "ControlType.Group");

        public static readonly ControlType Header = Register(AutomationIdentifierGuids.Header_Control,
            "ControlType.Header");

        public static readonly ControlType Image = Register(AutomationIdentifierGuids.Image_Control,
            "ControlType.Image");

        public static readonly ControlType List = Register(AutomationIdentifierGuids.List_Control, "ControlType.List",
            new[]
            {
                new[]
                {
                    SelectionPatternIdentifiers.Pattern, TablePatternIdentifiers.Pattern,
                    GridPatternIdentifiers.Pattern, MultipleViewPatternIdentifiers.Pattern
                }
            });

        public static readonly ControlType Menu = Register(AutomationIdentifierGuids.Menu_Control, "ControlType.Menu");

        public static readonly ControlType Tab = Register(AutomationIdentifierGuids.Tab_Control, "ControlType.Tab");

        public static readonly ControlType Table = Register(AutomationIdentifierGuids.Table_Control,
            "ControlType.Table",
            new[]
            {
                new[] {GridPatternIdentifiers.Pattern}, new[] {SelectionPatternIdentifiers.Pattern},
                new[] {TablePatternIdentifiers.Pattern}
            });

        public static readonly ControlType Text = Register(AutomationIdentifierGuids.Text_Control, "ControlType.Text");

        public static readonly ControlType Tree = Register(AutomationIdentifierGuids.Tree_Control, "ControlType.Tree");

        public static readonly ControlType Window = Register(AutomationIdentifierGuids.Window_Control,
            "ControlType.Window",
            new[] { new[] { TransformPatternIdentifiers.Pattern }, new[] { WindowPatternIdentifiers.Pattern } });

        /// <summary>
        ///     Ensures the types are not marked with the beforefieldinit flag (causing lazy initialization).
        ///     This ensures the registration (mapping) of the control GUIDs to the Uia_ControlIds is
        ///     guaranteed to exist during first access of the '_identifierDirectory' via ControlType.LookupById()
        ///     Note on the static constructor performance: The performance points measured typically depend on
        ///     the initialization that goes with the type initialization. So if that's within permissible limits,
        ///     in this case the Register() calls for the controltypes which aren't expensive, having static
        ///     constructor isn't that bad. Follow link for more details.
        ///     http://coderjournal.com/2009/08/static-constructors-in-net-3-5-still-a-bad-thing/
        /// </summary>
        static ControlType()
        {
        }


        internal ControlType(int id, Guid guid, string programmaticName)
            : base(UiaCoreIds.AutomationIdType.ControlType, id, guid, programmaticName)
        {
        }

        public static ControlType LookupById(int id)
        {
            return (ControlType)LookupById(UiaCoreIds.AutomationIdType.ControlType, id);
        }

        internal static ControlType Register(Guid guid, string programmaticName)
        {
            return (ControlType)Register(UiaCoreIds.AutomationIdType.ControlType, guid, programmaticName);
        }

        internal static ControlType Register(Guid guid, string programmaticName,
            AutomationPattern[][] requiredPatternsSets)
        {
            return Register(guid, programmaticName, new AutomationProperty[0], new AutomationPattern[0],
                requiredPatternsSets);
        }

        internal static ControlType Register(Guid guid, string programmaticName,
            AutomationProperty[] requiredProperties, AutomationPattern[] neverSupportedPatterns,
            AutomationPattern[][] requiredPatternsSets)
        {
            var type = (ControlType)Register(UiaCoreIds.AutomationIdType.ControlType, guid, programmaticName);
            return type;
        }
    }

    #endregion

    #region Exceptions

    [Serializable]
    public class ElementNotAvailableException : SystemException
    {
        public ElementNotAvailableException(Exception innerException)
            : base("Element not available", innerException)
        {
            HResult = UiaCoreIds.UIA_E_ELEMENTNOTAVAILABLE;
        }

        [SecurityPermission(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            base.GetObjectData(info, context);
        }
    }


    [Serializable]
    public class ElementNotEnabledException : InvalidOperationException
    {
        public ElementNotEnabledException(Exception innerException)
            : base("Element not enabled", innerException)
        {
            HResult = UiaCoreIds.UIA_E_ELEMENTNOTENABLED;
        }

        [SecurityPermission(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            base.GetObjectData(info, context);
        }
    }

    [Serializable]
    public class NoClickablePointException : Exception
    {
        public NoClickablePointException(Exception innerException) :
            base(string.Empty, innerException)
        {
        }

        [SecurityPermission(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            base.GetObjectData(info, context);
        }
    }

    [Serializable]
    public class ProxyAssemblyNotLoadedException : Exception
    {
        public ProxyAssemblyNotLoadedException(Exception innerException) :
            base(string.Empty, innerException)
        {
        }

        [SecurityPermission(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            base.GetObjectData(info, context);
        }
    }

    #endregion

    #region Enums

    [Guid("70d46e77-e3a8-449d-913c-e30eb2afecdb")]
    [ComVisible(true)]
    public enum DockPosition
    {
        Top,
        Left,
        Bottom,
        Right,
        Fill,
        None
    }

    [Guid("76d12d7e-b227-4417-9ce2-42642ffa896a")]
    [ComVisible(true)]
    public enum ExpandCollapseState
    {
        Collapsed,
        Expanded,
        PartiallyExpanded,
        LeafNode
    }

    [Guid("5F8A77B4-E685-48c1-94D0-8BB6AFA43DF9")]
    [ComVisible(true)]
    public enum OrientationType
    {
        None,
        Horizontal,
        Vertical
    }

    [ComVisible(true)]
    [Guid("15fdf2e2-9847-41cd-95dd-510612a025ea")]
    public enum RowOrColumnMajor
    {
        RowMajor,
        ColumnMajor,
        Indeterminate
    }

    [Flags]
    [ComVisible(true)]
    [Guid("3d9e3d8f-bfb0-484f-84ab-93ff4280cbc4")]
    public enum SupportedTextSelection
    {
        None,
        Single,
        Multiple
    }

    [Guid("ad7db4af-7166-4478-a402-ad5b77eab2fa")]
    [ComVisible(true)]
    public enum ToggleState
    {
        Off,
        On,
        Indeterminate
    }

    [Flags]
    public enum TreeScope
    {
        Element = 1,
        Children = 2,
        Descendants = 4,
        Subtree = 7,
        Parent = 8
    }

    [Guid("65101cc7-7904-408e-87a7-8c6dbd83a18b")]
    [ComVisible(true)]
    public enum WindowInteractionState
    {
        Running,
        Closing,
        ReadyForUserInteraction,
        BlockedByModalWindow,
        NotResponding
    }

    [ComVisible(true)]
    [Guid("fdc8f176-aed2-477a-8c89-ea04cc5f278d")]
    public enum WindowVisualState
    {
        Normal,
        Maximized,
        Minimized
    }

    // New for Windows 8
    //

    public enum AnnotationType
    {
        Unknown = 0xEA60,
        SpellingError,
        GrammarError,
        Comment,
        FormulaError,
        TrackChanges,
        Header,
        Footer,
        Highlighted
    }

    public enum StyleId
    {
        Normal
    }

    public enum ActiveEnd
    {
        None = 0,
        Start = 1,
        End = 2
    }

    public enum CaretPosition
    {
        Unknown = 0,
        EndOfLine = 1,
        BeginningOfLine = 2
    }

    public enum CaretBidiMode
    {
        LTR = 0,
        RTL = 1
    }

    #endregion
}