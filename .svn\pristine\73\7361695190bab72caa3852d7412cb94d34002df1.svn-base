using OCRTools.Common;
using OCRTools.Common.Entity;
using OCRTools.Properties;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Threading;
using System.Windows.Forms;

namespace OCRTools
{
    internal static class Program
    {

        public static List<HistoryTask> RecentTasks;

        public static UserEntity NowUser { get; internal set; }

        [STAThread]
        private static void Main(string[] args)
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            //ClipboardService.GetRtfHtml();

            //Application.Run(new RulerForm());

            //Application.Run(new ScrollingCaptureForm(new ScrollingCaptureOptions(), new RegionCaptureOptions(), false));

            //SelectRectangleList selectRectangleList = new SelectRectangleList
            //{
            //    IncludeChildWindows = true
            //};
            //var Windows = selectRectangleList.GetWindowInfoList();
            //var result = GZip.GZip.Compress(@"D:\Code\CatchTools\LocalOcrService\OcrMain\bin\models\飞浆Mobile\", @"D:\Code\CatchTools\LocalOcrService\OcrMain\bin\", "飞浆Mobile.zip");

            using (var mutex = new Mutex(true, Application.ProductName, out bool isFristInstance))
            {
                CommonString.IsAutoLogin = isFristInstance;
                var strMsg = args != null && args.Length > 0 ? args[0] : "";
                var isMsgProcess = !string.IsNullOrEmpty(strMsg);
                if (isMsgProcess)
                {
                    IniHelper.SetValue("系统", "右键菜单", strMsg);
                }
                if (isFristInstance || Path.GetFileNameWithoutExtension(Application.ExecutablePath).Equals(string.Format("{0}-多开版", Application.ProductName)))
                {
                    try
                    {
                        //Application.AddMessageFilter(new OcrMsgFilter());
                        RunMain();
                    }
                    catch { }
                    finally
                    {
                        if (isFristInstance)
                            mutex.ReleaseMutex();
                    }
                }
                else
                {
                    Application.ExitThread();
                }
            }
        }

        private static void RunMain()
        {
            //处理未捕获的异常
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);

            //处理UI线程异常
            Application.ThreadException += Application_ThreadException;

            //处理非UI线程异常
            AppDomain.CurrentDomain.UnhandledException += Backend_ThreadException;

            var uue = new UheHandler();
            try
            {
                uue.InstallUheHandler();
                uue.SendingErrorReport += uue_SendingErrorReport;

                //CommonString.StrServerIp = "127.0.0.1";

                CommonString.InitSetttingsBeforeStart();

                CommonSetting.InitSetting();

                if (!CommonMethod.IsAutoStart.Equals(CommonSetting.开机启动))
                {
                    CommonMethod.AutoStart(CommonSetting.开机启动);
                }

                try
                {
                    ShortcutHelpers.SetShortcut(true, Application.ProductName + ".lnk", Application.ExecutablePath, Application.ProductName);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }

                try
                {
                    InternetExplorerFeatureControl.Instance.BrowserEmulation = DocumentMode.DefaultRespectDocType;
                }
                catch { }

                //CommonString.HostCode?.FullUrl = "http://localhost:23205/";

                ImgLogHelper.Run();

                try
                {
                    var uiAutomation = Assembly.Load(Resources.Interop_UIAutomationClient);
                    AppDomain.CurrentDomain.AssemblyResolve += (sender, args) =>
                    {
                        if (args.Name.Contains("UIAutomationClient"))
                        {
                            return uiAutomation;
                        }
                        //else if (args.Name.Contains("PDFRender4NET"))
                        //{
                        //    return pdfRender4NET;
                        //}

                        return CommonString.LoadDllByName(args.Name);
                    };
                }
                catch
                {
                }

                Application.Run(new FrmMain());
            }
            catch { }
            finally
            {
                uue.UninstallUheHandler();
            }
        }

        private static void uue_SendingErrorReport(object sender, SendErrorReportArgs e)
        {
            if (!string.IsNullOrEmpty(e.Body)) Log.WriteLog(e.Body);
        }

        private static void Application_ThreadException(object sender, ThreadExceptionEventArgs e)
        {
            HandlerError(e.Exception);
        }

        private static void Backend_ThreadException(object sender, UnhandledExceptionEventArgs e)
        {
            HandlerError(e.ExceptionObject as Exception);
        }

        private static void HandlerError(Exception error)
        {
            Log.WriteError("运行时异常", error);
        }

        //private static void SetDpiAwareness()
        //{
        //    try
        //    {
        //        if (NativeMethods.OsVersion.Major >= 6)
        //        {
        //            //NativeMethods.SetProcessDPIAware();
        //NativeMethods.SetProcessDpiAwareness(NativeMethods.ProcessDPIAwareness.ProcessDPIUnaware);
        //        }
        //    }
        //    catch
        //    {
        //    }
        //}
    }
}