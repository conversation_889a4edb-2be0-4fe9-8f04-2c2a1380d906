﻿using System;

namespace OCRTools
{
    public class ScrollingCaptureOptions
    {
        internal int CombineAdjustmentLastVertical = 0;
        internal int CombineAdjustmentVertical = 0;
        internal int IgnoreLast = 0;
        internal int TrimBottomEdge = 0;

        internal int TrimLeftEdge = 0;
        internal int TrimRightEdge = 0;
        internal int TrimTopEdge = 0;
    }

    public enum ScrollingCaptureScrollMethod // Localized
    {
        自动尝试所有方法直到某方法生效,
        发送滚动消息至窗口或控件,
        模拟按下Page_Down按钮,
        模拟按下Down按键,
        模拟鼠标滚轮滚动
    }

    public enum ScrollingCaptureScrollTopMethod // Localized
    {
        自动尝试所有方法直到某方法生效,
        发送滚动消息至顶部,
        模拟按下Home按键,
        不自动滚动至顶部
    }

    [Flags]
    public enum ScrollInfoMask : uint
    {
        SIF_RANGE = 0x1,
        SIF_PAGE = 0x2,
        SIF_POS = 0x4,
        SIF_DISABLENOSCROLL = 0x8,
        SIF_TRACKPOS = 0x10,
        SIF_ALL = SIF_RANGE | SIF_PAGE | SIF_POS | SIF_TRACKPOS
    }

    public enum SBOrientation
    {
        SB_HORZ = 0x0,
        SB_VERT = 0x1,
        SB_CTL = 0x2,
        SB_BOTH = 0x3
    }
}