using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class CALCMODE : Record
	{
		public ushort Value;

		public CALCMODE(Record record)
			: base(record)
		{
		}

		public CALCMODE()
		{
			Type = 13;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			Value = binaryReader.ReadUInt16();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(Value);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
