﻿using OCRTools.Common;
using System;
using System.IO;

namespace ImageLib
{
    /// <summary>
    /// https://www.niupic.com/
    /// </summary>
    public class NiuTuImageUpload : BaseImageUpload
    {
        public NiuTuImageUpload()
        {
            ImageType = ImageTypeEnum.NiuTu;
        }

        private const string strFileNameSpilt = "\"img_puburl\":\"";

        internal override string GetResult(byte[] content, string ext)
        {
            var result = "";
            try
            {
                var url = "https://www.niupic.com/api/upload";
                var file = new UploadFileInfo()
                {
                    Name = "image_field",
                    Filename = "test.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                var html = UploadFileRequest.Post(url, new[] { file }, null);
                if (html.Contains(strFileNameSpilt))
                {
                    result = html.Substring(html.IndexOf(strFileNameSpilt) + strFileNameSpilt.Length);
                    result = result.Substring(0, result.IndexOf("\"")).Replace("\\/", "/");
                    if (!string.IsNullOrEmpty(result) && !result.StartsWith("http"))
                    {
                        result = "https://" + result;
                    }
                }
            }
            catch (Exception)
            {

            }
            return result;
        }
    }
}
