﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Net;
using System.Text;
using System.Web;

namespace OCRTools.Common
{
    public class WebClientExt
    {
        private const string NetWorkError = "网络异常，稍后重试！";
        private static ConcurrentBag<CnnWebClient> _pool = new ConcurrentBag<CnnWebClient>();

        public static CnnWebClient GetOneClient()
        {
            _pool.TryTake(out CnnWebClient client);
            while (client?.IsExipred == true)
                _pool.TryTake(out client);
            return client ?? new CnnWebClient();
        }

        public static void AddToPool(CnnWebClient client)
        {
            try
            {
                client.DtLast = ServerTime.DateTime.Ticks;
                _pool.Add(client);
            }
            catch { }
        }

        private static string GetNewCookie(string strCookie, string strNewCookie)
        {
            var strTmpCookie = strCookie ?? "";
            if (!string.IsNullOrEmpty(strNewCookie))
            {
                var lstTmp = new List<string>();
                lstTmp.AddRange(strNewCookie.Split(new[] { ",", ";" }, StringSplitOptions.RemoveEmptyEntries));
                foreach (var item in lstTmp)
                    if (!item.Trim().ToLower().StartsWith("path=")
                        && !item.Trim().ToLower().StartsWith("expires=")
                        && !item.Trim().ToLower().StartsWith("httponly=")
                        && !item.Trim().ToLower().StartsWith("domain=.")
                        && item.IndexOf("=") > 0)
                    {
                        var strItem = CommonMethod.SubString(item.Trim(), "", "=") + "=";
                        if (!strTmpCookie.Contains(strItem))
                            strTmpCookie += string.Format(";{0};", item.Trim());
                        else
                            strTmpCookie = strTmpCookie.Replace(
                                strItem + CommonMethod.SubString(strTmpCookie, strItem, ";"), item);
                    }
            }

            return strTmpCookie.Replace(" ", "").Replace(";;", ";").TrimStart(';');
        }


        #region Web

        public static string GetHtml(string url, double timeOut)
        {
            return GetHtml(url, "", "", "", 1, timeOut > 0 ? (int)timeOut : 2);
        }

        public static string GetHtml(string url, string cookie, string ipAddress, string post = "", int retryCount = 1,
            int timeOut = 2)
        {
            return GetHtml(url, ref cookie, ipAddress, post, retryCount, timeOut);
        }

        public static string GetHtml(string url, string strPost = "", int timeOut = 2,
            NameValueCollection headers = null)
        {
            return GetHtml(url, "", "", strPost, "", timeOut, headers);
        }

        public static string GetHtml(string url, ref string cookie, string ipAddress, string post = "",
            int retryCount = 1, int timeOut = 2)
        {
            var strTmp = string.Empty;
            retryCount = retryCount <= 0 ? 1 : retryCount;
            for (var i = 0; i < retryCount; i++)
            {
                strTmp = GetHtml(url, ref cookie, ipAddress, post, "", timeOut);
                if (!string.IsNullOrEmpty(strTmp))
                    break;
            }

            if (strTmp != null && strTmp.Equals(" "))
                strTmp = "";
            return strTmp;
        }

        public static string GetHtml(string url, string cookieStr, string ipAddress = "", string strPost = "",
            string referer = "", int timeOut = 2, NameValueCollection headers = null)
        {
            return GetHtml(url, ref cookieStr, ipAddress, strPost, referer, timeOut, headers);
        }

        public static string GetHtml(string url, ref string cookieStr, string ipAddress = "",
            string strPost = "", string referer = "", int timeOut = 2, NameValueCollection headers = null)
        {
            var result = "";
            var myClient = GetOneClient();
            try
            {
                myClient.Headers.Clear();
                //if (isMobile)
                //    myClient.Credentials = CredentialCache.DefaultCredentials;
                myClient.Timeout = timeOut;
                myClient.StrIpAddress = ipAddress;

                if (url.Contains("api.textin.com") || url.Contains("tinypng.com") || url.Contains(".myqcloud.com/users/"))
                {
                    myClient.Headers["Content-Type"] = "image/png";
                }
                else
                {
                    if (!string.IsNullOrEmpty(strPost) && (strPost.StartsWith("{") || strPost.StartsWith("[")))
                    {
                        myClient.Headers["Content-Type"] = "application/json;charset=UTF-8";
                    }
                    else
                        myClient.Headers["Content-Type"] = "application/x-www-form-urlencoded; charset=UTF-8";
                }

                try
                {
                    myClient.Headers["Accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8";
                    myClient.Headers["Accept-Language"] = "zh-CN,zh;q=0.8";
                    myClient.Headers["Cache-Control"] = "no-cache";
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }

                if (!string.IsNullOrEmpty(referer))
                    myClient.Headers.Add("Referer: " + referer);
                if (!string.IsNullOrEmpty(cookieStr))
                    myClient.Headers.Add("Cookie: " + cookieStr);
                myClient.Encoding = Encoding.UTF8;
                if (headers != null && headers.Count > 0)
                {
                    foreach (string key in headers)
                    {
                        try
                        {
                            myClient.Headers.Add(string.Format("{0}: {1}", key.Trim(),
                                headers[key] == null ? "" : headers[key].Trim()));
                        }
                        catch (Exception oe)
                        {
                            if (oe.Message.Contains("无效") || oe.Message.Contains("invalid"))
                            {
                                try
                                {
                                    myClient.Headers.Add(string.Format("{0}: {1}", key.Trim(),
                                        headers[key] == null ? "" : HttpUtility.UrlEncode(headers[key].Trim())));
                                }
                                catch (Exception e)
                                {
                                    Console.WriteLine(e);
                                }
                            }
                            else
                                Console.WriteLine(oe.Message);
                        }
                    }
                }

                var uri = new Uri(CommonMethod.PreProcessUrl(url), false);
                if ("image/png".Equals(myClient.Headers.Get("Content-Type")))
                {
                    result = Encoding.UTF8.GetString(myClient.UploadData(uri, url.Contains(".myqcloud.com/users/") ? "PUT" : "POST", Convert.FromBase64String(strPost)));
                }
                else
                {
                    if (string.IsNullOrEmpty(strPost))
                        result = myClient.DownloadString(uri);
                    else
                        result = myClient.UploadString(uri, strPost);
                }
                if (myClient.ResponseHeaders != null && !url.Contains("tinypng.com"))
                {
                    var loc = myClient.ResponseHeaders["Location"];
                    if (!string.IsNullOrEmpty(loc) && !url.Equals(loc))
                        return GetHtml(loc, ref cookieStr, ipAddress, strPost, referer, timeOut);
                    if (!ServerTime._hasGetNtpDate && !string.IsNullOrEmpty(myClient.ResponseHeaders["Date"]))
                        ServerTime.SetHttpDate(myClient.ResponseHeaders["Date"].ToDateTime());
                }

                if (string.IsNullOrEmpty(result)) result = " ";
            }
            catch (OutOfMemoryException)
            {
                GC.Collect();
            }
            catch (ObjectDisposedException)
            {
                return GetHtml(url, ref cookieStr, ipAddress, strPost, referer, timeOut, headers);
            }
            catch (Exception oe)
            {
                //log4net.LogManager.GetLogger("Order").Error(oe);
                if (oe is WebException exception)
                {
                    var response = exception.Response as HttpWebResponse;
                    try
                    {
                        if (response != null)
                        {
                            switch (response.StatusCode)
                            {
                                case HttpStatusCode.NotFound: //404
                                    DnsHelper.ReportError(myClient.StrHost, myClient.StrIpAddress);
                                    result = " ";
                                    break;
                            }
                        }
                        else if (!string.IsNullOrEmpty(exception.Message))
                        {
                            if (exception.Message.Contains("(404)"))
                            {
                                DnsHelper.ReportError(myClient.StrHost, myClient.StrIpAddress);
                                result = " ";
                            }
                        }
                    }
                    catch { }
                    finally
                    {
                        try
                        {
                            response?.Close();
                        }
                        catch { }
                    }
                }
                else if (!string.IsNullOrEmpty(oe.Message))
                {
                    if (oe.Message.Contains("(404)"))
                    {
                        DnsHelper.ReportError(myClient.StrHost, myClient.StrIpAddress);
                        result = " ";
                    }
                }
            }
            finally
            {
                if (myClient.ResponseHeaders != null && !string.IsNullOrEmpty(myClient.ResponseHeaders["Set-Cookie"]))
                    cookieStr = GetNewCookie(cookieStr, myClient.ResponseHeaders["Set-Cookie"].Trim());
                //CookieStr = myClient.ResponseHeaders["Set-Cookie"].Replace("Path=/otn", " ").Replace("Path=/", "").Replace("path=/", "").Replace(",", "").Trim();
                try
                {
                    if (myClient.IsBusy)
                        myClient.CancelAsync();
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }

                AddToPool(myClient);
            }

            return result;
        }

        #endregion
    }

    public class CnnWebClient : WebClient
    {
        private int _timeOut = 3;

        public string StrIpAddress { get; set; }

        public string StrHost { get; set; }

        /// <summary>
        ///     过期时间
        /// </summary>
        public int Timeout
        {
            get
            {
                if (_timeOut <= 0)
                    _timeOut = 3;
                return _timeOut;
            }
            set
            {
                if (value <= 0)
                    _timeOut = 3;
                _timeOut = value;
            }
        }

        public long DtLast { get; set; }

        public bool IsExipred { get { return new TimeSpan(ServerTime.DateTime.Ticks - DtLast).TotalMinutes > 1; } }

        ~CnnWebClient()
        {
            Dispose(false);
        }

        public new void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        public override string ToString()
        {
            return string.Format("{0}-{1}", StrHost, StrIpAddress);
        }

        //public bool RemoteCertificateValidationCallback(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors errors)
        //{
        //    return true;
        //}

        /// <summary>
        /// 重写GetWebRequest,添加WebRequest对象超时时间
        /// </summary>
        /// <param name="address"></param>
        /// <returns></returns>
        protected override WebRequest GetWebRequest(Uri address)
        {
            //if (DateTime.Now.Second % 2 == 0)
            //{
            //    //System.Threading.Thread.Sleep(1);
            //    System.GC.Collect();
            //}

            StrHost = address.Host;

            var selfHost = CommonString.IsSelfHost(StrHost);
            if (selfHost != 0)
            {
                address = CommonString.SetAddress(address, selfHost);
                StrIpAddress = address.Host;
            }

            HttpWebRequest nowRequest;
            try
            {
                nowRequest = (HttpWebRequest)base.GetWebRequest(address);
            }
            catch
            {
                nowRequest = (HttpWebRequest)WebRequest.Create(address);
            }

            if (selfHost != 0)
            {
                CommonString.SetUserAgent(nowRequest);
                nowRequest.Host = StrHost;
            }
            else
            {
                nowRequest.UserAgent = CommonString.StrDefaultAgent;
            }

            nowRequest.ProtocolVersion = HttpVersion.Version11;
            //数据是否缓冲 false 提高效率
            nowRequest.AllowWriteStreamBuffering = false;
            nowRequest.AllowAutoRedirect = false;
            nowRequest.Headers.Add("Accept-Encoding: gzip, deflate");
            nowRequest.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;
            try
            {
                nowRequest.Timeout = 1000 * Timeout;
                //NowRequest.ReadWriteTimeout = 1000;// *Timeout;
            }
            catch
            {
            }
            return nowRequest;
        }
    }
}