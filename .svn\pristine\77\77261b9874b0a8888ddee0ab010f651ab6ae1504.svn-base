using UIAutomationClient;

namespace System.Windows.Automation
{
    public sealed class CacheRequest
    {
        public static readonly CacheRequest DefaultCacheRequest = new CacheRequest();

        public static CacheRequest Current => DefaultCacheRequest;

        public static IUIAutomationCacheRequest CurrentNativeCacheRequest => Current.NativeCacheRequest;

        internal IUIAutomationCacheRequest NativeCacheRequest { get; } = Automation.Factory.CreateCacheRequest();
    }
}