using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class TextChildPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = TextChildPatternIdentifiers.Pattern;

        private TextChildPattern(AutomationElement el, IUIAutomationTextChildPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        public static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new TextChildPattern(el, (IUIAutomationTextChildPattern)pattern, cached);
        }
    }
}