// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class SpreadsheetPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = SpreadsheetPatternIdentifiers.Pattern;

        private IUIAutomationSpreadsheetPattern _pattern;

        private SpreadsheetPattern(AutomationElement el, IUIAutomationSpreadsheetPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
            _pattern = pattern;
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null
                ? null
                : new SpreadsheetPattern(el, (IUIAutomationSpreadsheetPattern) pattern, cached);
        }
    }

    public class SpreadsheetItemPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = SpreadsheetItemPatternIdentifiers.Pattern;
        public static readonly AutomationProperty FormulaProperty = SpreadsheetItemPatternIdentifiers.FormulaProperty;

        public static readonly AutomationProperty AnnotationObjectsProperty =
            SpreadsheetItemPatternIdentifiers.AnnotationObjectsProperty;

        public static readonly AutomationProperty AnnotationTypesProperty =
            SpreadsheetItemPatternIdentifiers.AnnotationTypesProperty;

        private IUIAutomationSpreadsheetItemPattern _pattern;

        private SpreadsheetItemPattern(AutomationElement el, IUIAutomationSpreadsheetItemPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
            _pattern = pattern;
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null
                ? null
                : new SpreadsheetItemPattern(el, (IUIAutomationSpreadsheetItemPattern) pattern, cached);
        }
    }
}