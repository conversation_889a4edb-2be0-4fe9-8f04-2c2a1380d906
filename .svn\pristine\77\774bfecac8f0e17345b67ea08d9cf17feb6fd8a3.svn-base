// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAComWrapperInternal;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public abstract class Condition
    {
        public static readonly Condition FalseCondition = BoolCondition.Wrap(false);
        public static readonly Condition TrueCondition = BoolCondition.Wrap(true);

        internal abstract IUIAutomationCondition NativeCondition { get; }

        internal static Condition Wrap(IUIAutomationCondition obj)
        {
            if (obj is IUIAutomationBoolCondition)
                return new BoolCondition((IUIAutomationBoolCondition) obj);
            if (obj is IUIAutomationAndCondition)
                return new AndCondition((IUIAutomationAndCondition) obj);
            if (obj is IUIAutomationOrCondition)
                return new OrCondition((IUIAutomationOrCondition) obj);
            if (obj is IUIAutomationNotCondition)
                return new NotCondition((IUIAutomationNotCondition) obj);
            if (obj is IUIAutomationPropertyCondition)
                return new PropertyCondition((IUIAutomationPropertyCondition) obj);
            throw new ArgumentException("obj");
        }

        internal static IUIAutomationCondition ConditionManagedToNative(
            Condition condition)
        {
            return condition == null ? null : condition.NativeCondition;
        }

        internal static IUIAutomationCondition[] ConditionArrayManagedToNative(
            Condition[] conditions)
        {
            var unwrappedConditions =
                new IUIAutomationCondition[conditions.Length];
            for (var i = 0; i < conditions.Length; ++i)
                unwrappedConditions[i] = ConditionManagedToNative(conditions[i]);
            return unwrappedConditions;
        }

        internal static Condition[] ConditionArrayNativeToManaged(
            Array conditions)
        {
            var wrappedConditions = new Condition[conditions.Length];
            for (var i = 0; i < conditions.Length; ++i)
                wrappedConditions[i] = Wrap((IUIAutomationCondition) conditions.GetValue(i));
            return wrappedConditions;
        }


        private class BoolCondition : Condition
        {
            internal readonly IUIAutomationBoolCondition _obj;

            internal BoolCondition(IUIAutomationBoolCondition obj)
            {
                Debug.Assert(obj != null);
                _obj = obj;
            }

            internal override IUIAutomationCondition NativeCondition => _obj;

            internal static BoolCondition Wrap(bool b)
            {
                var obj = (IUIAutomationBoolCondition) (b
                    ? Automation.Factory.CreateTrueCondition()
                    : Automation.Factory.CreateFalseCondition());
                return new BoolCondition(obj);
            }
        }
    }

    public class NotCondition : Condition
    {
        internal IUIAutomationNotCondition _obj;


        internal NotCondition(IUIAutomationNotCondition obj)
        {
            Debug.Assert(obj != null);
            _obj = obj;
        }

        public NotCondition(Condition condition)
        {
            _obj = (IUIAutomationNotCondition)
                Automation.Factory.CreateNotCondition(
                    ConditionManagedToNative(condition));
        }

        internal override IUIAutomationCondition NativeCondition => _obj;


        public Condition Condition => Wrap(_obj.GetChild());
    }

    public class AndCondition : Condition
    {
        internal IUIAutomationAndCondition _obj;


        internal AndCondition(IUIAutomationAndCondition obj)
        {
            Debug.Assert(obj != null);
            _obj = obj;
        }

        public AndCondition(params Condition[] conditions)
        {
            _obj = (IUIAutomationAndCondition)
                Automation.Factory.CreateAndConditionFromArray(
                    ConditionArrayManagedToNative(conditions));
        }

        internal override IUIAutomationCondition NativeCondition => _obj;

        public Condition[] GetConditions()
        {
            return ConditionArrayNativeToManaged(_obj.GetChildren());
        }
    }

    public class OrCondition : Condition
    {
        internal IUIAutomationOrCondition _obj;


        internal OrCondition(IUIAutomationOrCondition obj)
        {
            Debug.Assert(obj != null);
            _obj = obj;
        }

        public OrCondition(params Condition[] conditions)
        {
            _obj = (IUIAutomationOrCondition)
                Automation.Factory.CreateOrConditionFromArray(
                    ConditionArrayManagedToNative(conditions));
        }

        internal override IUIAutomationCondition NativeCondition => _obj;

        public Condition[] GetConditions()
        {
            return ConditionArrayNativeToManaged(_obj.GetChildren());
        }
    }

    [Flags]
    public enum PropertyConditionFlags
    {
        None,
        IgnoreCase
    }

    public class PropertyCondition : Condition
    {
        internal IUIAutomationPropertyCondition _obj;


        internal PropertyCondition(IUIAutomationPropertyCondition obj)
        {
            Debug.Assert(obj != null);
            _obj = obj;
        }

        public PropertyCondition(AutomationProperty property, object value)
        {
            Init(property, value, PropertyConditionFlags.None);
        }

        public PropertyCondition(AutomationProperty property, object value, PropertyConditionFlags flags)
        {
            Init(property, value, flags);
        }

        internal override IUIAutomationCondition NativeCondition => _obj;


        public PropertyConditionFlags Flags => (PropertyConditionFlags) _obj.PropertyConditionFlags;

        public AutomationProperty Property => AutomationProperty.LookupById(_obj.propertyId);

        public object Value => _obj.PropertyValue;

        private void Init(AutomationProperty property, object val, PropertyConditionFlags flags)
        {
            Utility.ValidateArgumentNonNull(property, "property");

            _obj = (IUIAutomationPropertyCondition)
                Automation.Factory.CreatePropertyConditionEx(
                    property.Id,
                    Utility.UnwrapObject(val),
                    (UIAutomationClient.PropertyConditionFlags) flags);
        }
    }
}