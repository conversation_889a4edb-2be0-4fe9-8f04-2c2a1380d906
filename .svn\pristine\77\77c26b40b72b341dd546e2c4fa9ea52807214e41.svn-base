using System.Text;
using UtfUnknown.Core.Analyzers.Chinese;
using UtfUnknown.Core.Models.MultiByte.Chinese;

namespace UtfUnknown.Core.Probers.MultiByte.Chinese
{
    public class Big5Prober : CharsetProber
    {
        private readonly CodingStateMachine _codingSm;

        private readonly Big5DistributionAnalyser _distributionAnalyser;

        private readonly byte[] _lastChar = new byte[2];

        public Big5Prober()
        {
            _codingSm = new CodingStateMachine(new Big5SmModel());
            _distributionAnalyser = new Big5DistributionAnalyser();
            Reset();
        }

        public override ProbingState HandleData(byte[] buf, int offset, int len)
        {
            var num = offset + len;
            for (var i = offset; i < num; i++)
            {
                switch (_codingSm.NextState(buf[i]))
                {
                    case 1:
                        state = ProbingState.NotMe;
                        break;
                    case 2:
                        state = ProbingState.FoundIt;
                        break;
                    case 0:
                        {
                            var currentCharLen = _codingSm.CurrentCharLen;
                            if (i == offset)
                            {
                                _lastChar[1] = buf[offset];
                                _distributionAnalyser.HandleOneChar(_lastChar, 0, currentCharLen);
                            }
                            else
                            {
                                _distributionAnalyser.HandleOneChar(buf, i - 1, currentCharLen);
                            }

                            continue;
                        }
                    default:
                        continue;
                }

                break;
            }

            _lastChar[0] = buf[num - 1];
            if (state == ProbingState.Detecting && _distributionAnalyser.GotEnoughData() && GetConfidence() > 0.95f)
                state = ProbingState.FoundIt;
            return state;
        }

        public override void Reset()
        {
            _codingSm.Reset();
            state = ProbingState.Detecting;
            _distributionAnalyser.Reset();
        }

        public override string GetCharsetName()
        {
            return "big5";
        }

        public override float GetConfidence(StringBuilder status = null)
        {
            return _distributionAnalyser.GetConfidence();
        }
    }
}