namespace UtfUnknown.Core.Models.MultiByte.Chinese
{
    public class HzGb2312SmModel : StateMachineModel
    {
        private static readonly int[] HzCls =
        {
            BitPackage.Pack4Bits(1, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 1, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 0, 0, 0, 0, 0),
            BitPackage.Pack4Bits(0, 0, 0, 4, 0, 5, 2, 0),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1)
        };

        private static readonly int[] HzSt =
        {
            BitPackage.Pack4Bits(0, 1, 3, 0, 0, 0, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 1, 1, 0, 0, 4, 1),
            BitPackage.Pack4Bits(5, 1, 6, 1, 5, 5, 4, 1),
            BitPackage.Pack4Bits(4, 1, 4, 4, 4, 1, 4, 1),
            BitPackage.Pack4Bits(4, 2, 0, 0, 0, 0, 0, 0)
        };

        private static readonly int[] HzCharLenTable = new int[6];

        public HzGb2312SmModel()
            : base(
                new BitPackage(BitPackage.indexShift4Bits, BitPackage.shiftMask4Bits, BitPackage.bitShift4Bits,
                    BitPackage.unitMask4Bits, HzCls), 6,
                new BitPackage(BitPackage.indexShift4Bits, BitPackage.shiftMask4Bits, BitPackage.bitShift4Bits,
                    BitPackage.unitMask4Bits, HzSt), HzCharLenTable, "hz-gb-2312")
        {
        }
    }
}