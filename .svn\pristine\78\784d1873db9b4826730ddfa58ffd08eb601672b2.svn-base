using System;
using System.IO;

namespace ExcelLibrary.BinaryDrawingFormat
{
	public class MsofbtBlipEnd : MsofbtBlip
	{
		public MsofbtBlipEnd(EscherRecord record)
			: base(record)
		{
		}

		public MsofbtBlipEnd()
		{
			Type = 61719;
		}

		public override void Decode()
		{
			MemoryStream memoryStream = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(memoryStream);
			UID = new Guid(binaryReader.ReadBytes(16));
			Marker = binaryReader.ReadByte();
			ImageData = binaryReader.ReadBytes((int)(memoryStream.Length - memoryStream.Position));
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(UID.ToByteArray());
			binaryWriter.Write(Marker);
			binaryWriter.Write(ImageData);
			Data = memoryStream.ToArray();
			Size = (uint)Data.Length;
			base.Encode();
		}
	}
}
