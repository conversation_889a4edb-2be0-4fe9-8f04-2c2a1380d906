﻿using System;
using System.IO;
using System.Text;

namespace OCRTools
{
    public static class Log
    {
        private static readonly object Obj = new object();

        /// <summary>
        ///     操作日志
        /// </summary>
        public static void WriteLog(string content)
        {
            WriteLogs(content, "操作日志");
        }

        /// <summary>
        ///     错误日志
        /// </summary>
        public static void WriteError(string strMsg, Exception oe)
        {
            var strTmp = "";
            if (!string.IsNullOrEmpty(strMsg)) strTmp = string.Format("\n{0}", strMsg);
            try
            {
                if (oe != null)
                    strTmp += string.Format("\nMessage:{0}\nStackTrace:{1}\nTargetSite:{2}", oe.Message, oe.StackTrace,
                        oe.TargetSite);
            }
            catch
            {
            }

            WriteLogs(strTmp, "错误日志");
        }

        /// <summary>
        ///     错误日志
        /// </summary>
        public static void WriteError(Exception oe)
        {
            var strTmp = "";
            try
            {
                if (oe != null)
                    strTmp = string.Format("\nMessage:{0}\nStackTrace:{1}\nTargetSite:{2}", oe.Message, oe.StackTrace,
                        oe.TargetSite);
            }
            catch
            {
            }

            WriteLogs(strTmp, "错误日志");
        }

        private static void WriteLogs(string content, string type)
        {
            if (string.IsNullOrEmpty(content))
                return;
            try
            {
                lock (Obj)
                {
                    var path = AppDomain.CurrentDomain.BaseDirectory;
                    if (!string.IsNullOrEmpty(path))
                    {
                        path = AppDomain.CurrentDomain.BaseDirectory + "Logs";
                        if (!Directory.Exists(path)) Directory.CreateDirectory(path);
                        path = path + "\\" + ServerTime.DateTime.ToString("yyMM");
                        if (!Directory.Exists(path)) Directory.CreateDirectory(path);
                        path = path + "\\" + ServerTime.DateTime.ToString("dd") + ".txt";
                        if (!File.Exists(path))
                        {
                            var fs = File.Create(path);
                            fs.Close();
                        }

                        if (File.Exists(path))
                        {
                            var sw = new StreamWriter(path, true, Encoding.Default);
                            sw.WriteLine(ServerTime.DateTime);
                            sw.WriteLine("日志类型：" + type);
                            sw.WriteLine("详情：" + content);
                            sw.WriteLine("----------------------------------------");
                            sw.Close();
                        }
                    }
                }
            }
            catch
            {
            }
        }
    }
}