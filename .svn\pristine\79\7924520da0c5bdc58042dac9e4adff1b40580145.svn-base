// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class AnnotationPattern : BasePattern
    {
        public static readonly AutomationProperty AnnotationTypeIdProperty =
            AnnotationPatternIdentifiers.AnnotationTypeIdProperty;

        public static readonly AutomationPattern Pattern = AnnotationPatternIdentifiers.Pattern;


        private AnnotationPattern(AutomationElement el, IUIAutomationAnnotationPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new AnnotationPattern(el, (IUIAutomationAnnotationPattern) pattern, cached);
        }
    }
}