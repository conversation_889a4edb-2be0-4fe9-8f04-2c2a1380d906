﻿using System.Reflection;
using System.Runtime.InteropServices;

// 有关程序集的常规信息通过以下
// 特性集控制。更改这些特性值可修改
// 与程序集关联的信息。
[assembly: AssemblyTitle("OCR助手更新程序")]
[assembly: AssemblyDescription("OCR助手更新程序(2021-09-03)")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("SmartOldfish")]
[assembly: AssemblyProduct("OCR助手更新程序")]
[assembly: AssemblyCopyright("版权 © 2019-2021 SmartOldfish")]

// 将 ComVisible 设置为 false 使此程序集中的类型
// 对 COM 组件不可见。如果需要从 COM 访问此程序集中的类型，
// 则将该类型上的 ComVisible 特性设置为 true。
[assembly: ComVisible(false)]

// 如果此项目向 COM 公开，则下列 GUID 用于类型库的 ID
[assembly: Guid("b3e99b62-6a6b-4701-aeb4-7839b9e466d1")]

[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
