﻿using ImageLib;
using nQuant;
using OCRTools.Common;
using System;
using System.Drawing;
using System.IO;

namespace OCRTools
{
    public class ImageCompress
    {
        public static string CompressImageFile(string fileName, string strPath, CompressType type)
        {
            string url = string.Empty;
            var bytes = CompressImage(File.ReadAllBytes(fileName), type, ref url);
            if (bytes == null || bytes.Length <= 0) return string.Empty;

            var filePath = Path.Combine(strPath,
                Path.GetFileNameWithoutExtension(fileName) + "-" +
                ServerTime.DateTime.Millisecond + Path.GetExtension(fileName));
            File.WriteAllBytes(filePath, bytes);
            return filePath;

        }

        public static byte[] CompressImage(byte[] byts, CompressType type, ref string strUrl)
        {
            byte[] result = null;
            try
            {
                switch (type)
                {
                    case CompressType.TinyPng:
                        result = ImageLib.TinyPngUpload.GetZipResult(byts, ref strUrl);
                        break;
                    case CompressType.ImgTop:
                        result = ImageTopUpload.GetZipResult(byts, ref strUrl);
                        break;
                    case CompressType.WebResizer:
                        result = WebResizerUpload.GetZipResult(byts, ref strUrl);
                        break;
                    //case CompressType.Pnn压缩:
                    //    using (var stream = new MemoryStream(byts))
                    //    {
                    //        using (var tmp = new Bitmap(stream))
                    //        {
                    //            var process = new PnnQuantizer();
                    //            using (var resultImg = process.QuantizeImage(tmp, PixelFormat.Undefined, 256, true))
                    //            {
                    //                result = ImageProcessHelper.ImageToByte(resultImg);
                    //            }
                    //        }
                    //    }
                    //    break;
                    default:
                        using (var stream = new MemoryStream(byts))
                        {
                            using (var tmp = new Bitmap(stream))
                            {
                                using (var resultImg = WuQuantizer.QuantizeImage(tmp))
                                {
                                    if (resultImg != null)
                                        result = ImageProcessHelper.ImageToByte(resultImg);
                                }
                            }
                        }
                        break;
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("CompressImage", oe);
            }
            return result;
        }
    }

    public enum CompressType
    {
        TinyPng,
        WebResizer,
        ImgTop,
        助手压缩,
        //Pnn压缩
    }
}
