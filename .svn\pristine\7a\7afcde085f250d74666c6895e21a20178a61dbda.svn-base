﻿namespace OCRTools
{
    partial class FrmPasteImage
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.cmsMain = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.复制图像ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmCopyText = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmOCR = new System.Windows.Forms.ToolStripMenuItem();
            this.cmsSave = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.移动ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.上ShiftToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.下ShiftToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.左ShiftToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.右ShiftToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.透明度ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem3 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem4 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem5 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem6 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem7 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem8 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem9 = new System.Windows.Forms.ToolStripMenuItem();
            this.原始尺寸ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.阴影ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.cmsTop = new System.Windows.Forms.ToolStripMenuItem();
            this.cmsClose = new System.Windows.Forms.ToolStripMenuItem();
            this.cmsMain.SuspendLayout();
            this.SuspendLayout();
            // 
            // cmsMain
            // 
            this.cmsMain.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.复制图像ToolStripMenuItem,
            this.tsmCopyText,
            this.tsmOCR,
            this.cmsSave,
            this.toolStripSeparator1,
            this.移动ToolStripMenuItem,
            this.透明度ToolStripMenuItem,
            this.原始尺寸ToolStripMenuItem,
            this.toolStripSeparator2,
            this.阴影ToolStripMenuItem,
            this.cmsTop,
            this.cmsClose});
            this.cmsMain.Name = "cmsMain";
            this.cmsMain.Size = new System.Drawing.Size(185, 258);
            // 
            // 复制图像ToolStripMenuItem
            // 
            this.复制图像ToolStripMenuItem.Image = global::OCRTools.Properties.Resources.复制;
            this.复制图像ToolStripMenuItem.Name = "复制图像ToolStripMenuItem";
            this.复制图像ToolStripMenuItem.Size = new System.Drawing.Size(184, 22);
            this.复制图像ToolStripMenuItem.Text = "复制图像";
            this.复制图像ToolStripMenuItem.Click += new System.EventHandler(this.复制图像ToolStripMenuItem_Click);
            // 
            // tsmCopyText
            // 
            this.tsmCopyText.Image = global::OCRTools.Properties.Resources.document_copy;
            this.tsmCopyText.Name = "tsmCopyText";
            this.tsmCopyText.Size = new System.Drawing.Size(184, 22);
            this.tsmCopyText.Text = "复制文本";
            // 
            // tsmOCR
            // 
            this.tsmOCR.Image = global::OCRTools.Properties.Resources.文本;
            this.tsmOCR.Name = "tsmOCR";
            this.tsmOCR.Size = new System.Drawing.Size(184, 22);
            this.tsmOCR.Text = "OCR识别";
            this.tsmOCR.Click += new System.EventHandler(this.cmsOCR_Click);
            // 
            // cmsSave
            // 
            this.cmsSave.Image = global::OCRTools.Properties.Resources.复制;
            this.cmsSave.Name = "cmsSave";
            this.cmsSave.Size = new System.Drawing.Size(184, 22);
            this.cmsSave.Text = "图像另存为";
            this.cmsSave.Click += new System.EventHandler(this.cmsSave_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(181, 6);
            // 
            // 移动ToolStripMenuItem
            // 
            this.移动ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.上ShiftToolStripMenuItem,
            this.下ShiftToolStripMenuItem,
            this.左ShiftToolStripMenuItem,
            this.右ShiftToolStripMenuItem});
            this.移动ToolStripMenuItem.Image = global::OCRTools.Properties.Resources.图文混排;
            this.移动ToolStripMenuItem.Name = "移动ToolStripMenuItem";
            this.移动ToolStripMenuItem.Size = new System.Drawing.Size(184, 22);
            this.移动ToolStripMenuItem.Text = "移动位置 (↑ ↓ ← →)";
            // 
            // 上ShiftToolStripMenuItem
            // 
            this.上ShiftToolStripMenuItem.Name = "上ShiftToolStripMenuItem";
            this.上ShiftToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.上ShiftToolStripMenuItem.Text = "上 (↑/ Shift + ↑)";
            this.上ShiftToolStripMenuItem.Click += new System.EventHandler(this.上ShiftToolStripMenuItem_Click);
            // 
            // 下ShiftToolStripMenuItem
            // 
            this.下ShiftToolStripMenuItem.Name = "下ShiftToolStripMenuItem";
            this.下ShiftToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.下ShiftToolStripMenuItem.Text = "下 (↓/ Shift + ↓)";
            this.下ShiftToolStripMenuItem.Click += new System.EventHandler(this.下ShiftToolStripMenuItem_Click);
            // 
            // 左ShiftToolStripMenuItem
            // 
            this.左ShiftToolStripMenuItem.Name = "左ShiftToolStripMenuItem";
            this.左ShiftToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.左ShiftToolStripMenuItem.Text = "左 (←/ Shift + ←)";
            this.左ShiftToolStripMenuItem.Click += new System.EventHandler(this.左ShiftToolStripMenuItem_Click);
            // 
            // 右ShiftToolStripMenuItem
            // 
            this.右ShiftToolStripMenuItem.Name = "右ShiftToolStripMenuItem";
            this.右ShiftToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.右ShiftToolStripMenuItem.Text = "右 (→/ Shift + →)";
            this.右ShiftToolStripMenuItem.Click += new System.EventHandler(this.右ShiftToolStripMenuItem_Click);
            // 
            // 透明度ToolStripMenuItem
            // 
            this.透明度ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem2,
            this.toolStripMenuItem3,
            this.toolStripMenuItem4,
            this.toolStripMenuItem5,
            this.toolStripMenuItem6,
            this.toolStripMenuItem7,
            this.toolStripMenuItem8,
            this.toolStripMenuItem9});
            this.透明度ToolStripMenuItem.Image = global::OCRTools.Properties.Resources.缩小;
            this.透明度ToolStripMenuItem.Name = "透明度ToolStripMenuItem";
            this.透明度ToolStripMenuItem.Size = new System.Drawing.Size(184, 22);
            this.透明度ToolStripMenuItem.Text = "透明度 (滚动鼠标)";
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(108, 22);
            this.toolStripMenuItem2.Text = "100%";
            this.toolStripMenuItem2.Click += new System.EventHandler(this.tsmOpacity_Click);
            // 
            // toolStripMenuItem3
            // 
            this.toolStripMenuItem3.Name = "toolStripMenuItem3";
            this.toolStripMenuItem3.Size = new System.Drawing.Size(108, 22);
            this.toolStripMenuItem3.Text = "90%";
            this.toolStripMenuItem3.Click += new System.EventHandler(this.tsmOpacity_Click);
            // 
            // toolStripMenuItem4
            // 
            this.toolStripMenuItem4.Name = "toolStripMenuItem4";
            this.toolStripMenuItem4.Size = new System.Drawing.Size(108, 22);
            this.toolStripMenuItem4.Text = "80%";
            this.toolStripMenuItem4.Click += new System.EventHandler(this.tsmOpacity_Click);
            // 
            // toolStripMenuItem5
            // 
            this.toolStripMenuItem5.Name = "toolStripMenuItem5";
            this.toolStripMenuItem5.Size = new System.Drawing.Size(108, 22);
            this.toolStripMenuItem5.Text = "70%";
            this.toolStripMenuItem5.Click += new System.EventHandler(this.tsmOpacity_Click);
            // 
            // toolStripMenuItem6
            // 
            this.toolStripMenuItem6.Name = "toolStripMenuItem6";
            this.toolStripMenuItem6.Size = new System.Drawing.Size(108, 22);
            this.toolStripMenuItem6.Text = "60%";
            this.toolStripMenuItem6.Click += new System.EventHandler(this.tsmOpacity_Click);
            // 
            // toolStripMenuItem7
            // 
            this.toolStripMenuItem7.Name = "toolStripMenuItem7";
            this.toolStripMenuItem7.Size = new System.Drawing.Size(108, 22);
            this.toolStripMenuItem7.Text = "50%";
            this.toolStripMenuItem7.Click += new System.EventHandler(this.tsmOpacity_Click);
            // 
            // toolStripMenuItem8
            // 
            this.toolStripMenuItem8.Name = "toolStripMenuItem8";
            this.toolStripMenuItem8.Size = new System.Drawing.Size(108, 22);
            this.toolStripMenuItem8.Text = "40%";
            this.toolStripMenuItem8.Click += new System.EventHandler(this.tsmOpacity_Click);
            // 
            // toolStripMenuItem9
            // 
            this.toolStripMenuItem9.Name = "toolStripMenuItem9";
            this.toolStripMenuItem9.Size = new System.Drawing.Size(108, 22);
            this.toolStripMenuItem9.Text = "30%";
            this.toolStripMenuItem9.Click += new System.EventHandler(this.tsmOpacity_Click);
            // 
            // 原始尺寸ToolStripMenuItem
            // 
            this.原始尺寸ToolStripMenuItem.Image = global::OCRTools.Properties.Resources.原始;
            this.原始尺寸ToolStripMenuItem.Name = "原始尺寸ToolStripMenuItem";
            this.原始尺寸ToolStripMenuItem.Size = new System.Drawing.Size(184, 22);
            this.原始尺寸ToolStripMenuItem.Text = "原始尺寸";
            this.原始尺寸ToolStripMenuItem.Click += new System.EventHandler(this.原始尺寸ToolStripMenuItem_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(181, 6);
            // 
            // 阴影ToolStripMenuItem
            // 
            this.阴影ToolStripMenuItem.Checked = true;
            this.阴影ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.阴影ToolStripMenuItem.Name = "阴影ToolStripMenuItem";
            this.阴影ToolStripMenuItem.Size = new System.Drawing.Size(184, 22);
            this.阴影ToolStripMenuItem.Text = "窗口阴影";
            this.阴影ToolStripMenuItem.Click += new System.EventHandler(this.阴影ToolStripMenuItem_Click);
            // 
            // cmsTop
            // 
            this.cmsTop.Image = global::OCRTools.Properties.Resources.untop;
            this.cmsTop.Name = "cmsTop";
            this.cmsTop.Size = new System.Drawing.Size(184, 22);
            this.cmsTop.Text = "取消置顶";
            this.cmsTop.Click += new System.EventHandler(this.cmsTop_Click);
            // 
            // cmsClose
            // 
            this.cmsClose.Image = global::OCRTools.Properties.Resources.退出;
            this.cmsClose.Name = "cmsClose";
            this.cmsClose.Size = new System.Drawing.Size(184, 22);
            this.cmsClose.Text = "关闭窗体(&C)";
            this.cmsClose.Click += new System.EventHandler(this.cmsClose_Click);
            // 
            // FrmPasteImage
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(110, 124);
            this.ContextMenuStrip = this.cmsMain;
            this.Name = "FrmPasteImage";
            this.StartPosition = System.Windows.Forms.FormStartPosition.Manual;
            this.Text = "贴图";
            this.TopMost = true;
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frmPasteImage_KeyUp);
            this.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.frmPasteImage_MouseDoubleClick);
            this.cmsMain.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip cmsMain;
        private System.Windows.Forms.ToolStripMenuItem 复制图像ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem cmsSave;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem 阴影ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem cmsTop;
        private System.Windows.Forms.ToolStripMenuItem cmsClose;
        private System.Windows.Forms.ToolStripMenuItem tsmOCR;
        private System.Windows.Forms.ToolStripMenuItem 原始尺寸ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem tsmCopyText;
        private System.Windows.Forms.ToolStripMenuItem 移动ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 上ShiftToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 下ShiftToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 左ShiftToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 右ShiftToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 透明度ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem3;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem4;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem5;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem6;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem7;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem8;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem9;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
    }
}