﻿using System;
using System.Collections.Specialized;
using System.IO;

namespace OCRTools.Common.ImageLib
{
    public class JueJinImageUpload : BaseImageUpload
    {
        private const string STR_FILE_NAME_SPILT = "\"imgurl\":\"";

        public JueJinImageUpload()
        {
            ImageType = ImageTypeEnum.JueJin;
        }

        public virtual string GetResult(byte[] content, string ext)
        {
            var result = "";
            try
            {
                var url = "https://api.uomg.com/api/image.juejin";
                var file = new UploadFileInfo
                {
                    Name = "Filedata",
                    Filename = "test.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                var vaules = new NameValueCollection
                {
                    {"file", "multipart"}
                };
                var html = UploadFileRequest.Post(url, new[] {file}, vaules);
                //{
                //"ret": 0,
                //"msg": "success",
                //"fileName": "9be96996-9f80-4ed2-a59c-d513520178e0.jpg"}
                if (html.Contains(STR_FILE_NAME_SPILT))
                {
                    result = html.Substring(html.IndexOf(STR_FILE_NAME_SPILT) + STR_FILE_NAME_SPILT.Length);
                    result = result.Substring(0, result.IndexOf("\"")).Replace("\\/", "/");
                }
            }
            catch (Exception)
            {
            }

            return result;
        }
    }
}