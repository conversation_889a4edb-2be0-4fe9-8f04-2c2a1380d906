using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace OCRTools.Common
{
    /// <summary>
    /// 增强型智能高亮区域 - 支持多控件组合、相对定位和自适应缩放
    /// </summary>
    [Serializable]
    public class EnhancedSmartArea
    {
        #region 枚举定义

        /// <summary>
        /// 区域类型枚举
        /// </summary>
        public enum AreaType
        {
            /// <summary>
            /// 基于控件 - 根据控件名称定位
            /// </summary>
            ControlBased,

            /// <summary>
            /// 绝对区域 - 使用固定坐标
            /// </summary>
            AbsoluteArea,

            /// <summary>
            /// 相对区域 - 使用窗体比例计算
            /// </summary>
            RelativeArea,

            /// <summary>
            /// 锚点定位 - 使用锚点和相对定位
            /// </summary>
            AnchorBased,

            /// <summary>
            /// 多控件组合 - 多个控件或区域的组合
            /// </summary>
            MultiControl,

            /// <summary>
            /// 混合缩放 - 使用混合计算方式
            /// </summary>
            MixedScaling,

            /// <summary>
            /// 自定义区域 - 支持嵌套和复杂形状
            /// </summary>
            CustomArea
        }

        /// <summary>
        /// 锚点位置枚举
        /// </summary>
        public enum AnchorPoint
        {
            TopLeft, TopCenter, TopRight,
            MiddleLeft, Center, MiddleRight,
            BottomLeft, BottomCenter, BottomRight
        }

        /// <summary>
        /// 大小模式枚举
        /// </summary>
        public enum SizeMode
        {
            /// <summary>
            /// 固定大小
            /// </summary>
            Fixed,

            /// <summary>
            /// 相对大小 - 基于百分比
            /// </summary>
            Relative,

            /// <summary>
            /// 自适应 - 根据内容调整
            /// </summary>
            Adaptive,

            /// <summary>
            /// 混合 - 部分固定部分自适应
            /// </summary>
            Mixed
        }

        /// <summary>
        /// 位置策略枚举
        /// </summary>
        public enum PositionStrategy
        {
            /// <summary>
            /// 自动 - 根据内容自动确定最佳策略
            /// </summary>
            Auto,

            /// <summary>
            /// 固定 - 使用绝对坐标
            /// </summary>
            Fixed,

            /// <summary>
            /// 相对 - 相对于参考点
            /// </summary>
            Relative,

            /// <summary>
            /// 锚点 - 使用锚点系统
            /// </summary>
            Anchored,

            /// <summary>
            /// 手动 - 根据用户指定位置
            /// </summary>
            Manual
        }

        #endregion

        #region 子类定义

        /// <summary>
        /// 控件锚点类
        /// </summary>
        [Serializable]
        public class ControlAnchor
        {
            /// <summary>
            /// 控件标识 - 可以是控件名或路径
            /// </summary>
            public string ControlId { get; set; }

            /// <summary>
            /// 控件全路径 (例如: "MainForm.tabControl1.tabPage2.button1")
            /// </summary>
            public string ControlPath { get; set; }

            /// <summary>
            /// 锚点位置
            /// </summary>
            public AnchorPoint AnchorPoint { get; set; } = AnchorPoint.Center;

            /// <summary>
            /// 偏移量
            /// </summary>
            public Point Offset { get; set; } = Point.Empty;

            /// <summary>
            /// 权重 - 在组合计算中的权重
            /// </summary>
            public float Weight { get; set; } = 1.0f;

            /// <summary>
            /// 原始矩形
            /// </summary>
            public Rectangle OriginalRect { get; set; }
        }

        /// <summary>
        /// 区域组件类
        /// </summary>
        [Serializable]
        public class AreaComponent
        {
            /// <summary>
            /// 组件ID
            /// </summary>
            public string Id { get; set; } = Guid.NewGuid().ToString();

            /// <summary>
            /// 组件类型
            /// </summary>
            public AreaType Type { get; set; }

            /// <summary>
            /// 关联的控件锚点 (如果有)
            /// </summary>
            public ControlAnchor Anchor { get; set; }

            /// <summary>
            /// 绝对矩形
            /// </summary>
            public Rectangle AbsoluteRect { get; set; }

            /// <summary>
            /// 相对矩形
            /// </summary>
            public RectangleF RelativeRect { get; set; }
        }

        #endregion

        #region 属性

        /// <summary>
        /// 区域类型
        /// </summary>
        public AreaType Type { get; set; } = AreaType.AnchorBased;

        /// <summary>
        /// 锚点列表 - 用于多锚点定位或组合
        /// </summary>
        public List<ControlAnchor> Anchors { get; set; } = new List<ControlAnchor>();

        /// <summary>
        /// 区域组件列表 - 用于组合区域
        /// </summary>
        public List<AreaComponent> Components { get; set; } = new List<AreaComponent>();

        /// <summary>
        /// 大小模式
        /// </summary>
        public SizeMode SizeType { get; set; } = SizeMode.Adaptive;

        /// <summary>
        /// 固定大小
        /// </summary>
        public Size FixedSize { get; set; } = Size.Empty;

        /// <summary>
        /// 相对大小
        /// </summary>
        public SizeF RelativeSize { get; set; } = SizeF.Empty;

        /// <summary>
        /// 原始矩形 (设计时捕获的原始区域)
        /// </summary>
        public Rectangle OriginalRect { get; set; }

        /// <summary>
        /// 相对矩形 (相对于窗体大小的比例)
        /// </summary>
        public RectangleF RelativeRect { get; set; }

        /// <summary>
        /// 自动检测UI变化
        /// </summary>
        public bool AutoDetectChanges { get; set; } = true;

        /// <summary>
        /// 自动适应内容
        /// </summary>
        public bool AdaptToContent { get; set; } = true;

        /// <summary>
        /// 位置策略
        /// </summary>
        public PositionStrategy PositionType { get; set; } = PositionStrategy.Auto;

        /// <summary>
        /// 主锚点位置 - 主参考点
        /// </summary>
        public AnchorPoint ReferencePoint { get; set; } = AnchorPoint.Center;

        /// <summary>
        /// 全局偏移量
        /// </summary>
        public Point Offset { get; set; } = Point.Empty;

        /// <summary>
        /// 嵌套区域列表
        /// </summary>
        public List<Rectangle> NestedRects { get; set; } = new List<Rectangle>();

        /// <summary>
        /// 父区域
        /// </summary>
        public Rectangle ParentRect { get; set; } = Rectangle.Empty;

        #endregion

        #region 构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public EnhancedSmartArea()
        {
        }

        /// <summary>
        /// 从矩形创建区域
        /// </summary>
        /// <param name="rect">矩形</param>
        /// <param name="formSize">窗体大小</param>
        public EnhancedSmartArea(Rectangle rect, Size formSize)
        {
            Type = AreaType.RelativeArea;
            OriginalRect = rect;

            // 计算相对位置
            RelativeRect = new RectangleF(
                (float)rect.X / formSize.Width,
                (float)rect.Y / formSize.Height,
                (float)rect.Width / formSize.Width,
                (float)rect.Height / formSize.Height);
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 添加控件
        /// </summary>
        /// <param name="control">控件</param>
        /// <param name="form">所在窗体</param>
        /// <param name="anchorPoint">锚点</param>
        /// <returns>添加的控件锚点</returns>
        public ControlAnchor AddControl(Control control, Form form, AnchorPoint anchorPoint = AnchorPoint.Center)
        {
            // 获取控件路径
            string controlPath = GetControlPath(control);

            // 获取控件在窗体中的位置
            Rectangle controlRect = GetControlRectInForm(control, form);

            // 创建锚点
            var anchor = new ControlAnchor
            {
                ControlId = control.Name,
                ControlPath = controlPath,
                AnchorPoint = anchorPoint,
                OriginalRect = controlRect
            };

            Anchors.Add(anchor);

            // 添加为组件
            Components.Add(new AreaComponent
            {
                Type = AreaType.ControlBased,
                Anchor = anchor,
                AbsoluteRect = controlRect
            });

            // 更新区域
            UpdateCombinedArea(form.ClientSize);

            return anchor;
        }

        /// <summary>
        /// 添加矩形区域
        /// </summary>
        /// <param name="rect">矩形</param>
        /// <param name="formSize">窗体大小</param>
        /// <returns>添加的区域组件</returns>
        public AreaComponent AddRectangle(Rectangle rect, Size formSize)
        {
            // 计算相对位置
            RectangleF relRect = new RectangleF(
                (float)rect.X / formSize.Width,
                (float)rect.Y / formSize.Height,
                (float)rect.Width / formSize.Width,
                (float)rect.Height / formSize.Height);

            // 创建组件
            var component = new AreaComponent
            {
                Type = AreaType.AbsoluteArea,
                AbsoluteRect = rect,
                RelativeRect = relRect
            };

            Components.Add(component);

            // 更新区域
            UpdateCombinedArea(formSize);

            return component;
        }

        /// <summary>
        /// 计算实际区域（核心功能）
        /// </summary>
        /// <param name="form">所在窗体</param>
        /// <returns>计算后的矩形</returns>
        public Rectangle CalculateActualRect(Form form)
        {
            Size currentSize = form.ClientSize;

            switch (Type)
            {
                case AreaType.ControlBased:
                    // 在ControlBased模式下，使用锚点集合
                    if (Anchors.Count > 0)
                    {
                        // 无论锚点数量是1个还是多个，都使用CombineAreas更可靠
                        return CombineAreas(form);
                    }
                    return OriginalRect;

                case AreaType.AnchorBased:
                    return CalculateAnchoredRect(form);

                case AreaType.MultiControl:
                    return CombineAreas(form);

                case AreaType.RelativeArea:
                    return GetRelativeRect(RelativeRect, currentSize);

                case AreaType.AbsoluteArea:
                    return OriginalRect;

                case AreaType.MixedScaling:
                    return CalculateMixedScalingRect(form);

                default:
                    return OriginalRect;
            }
        }

        /// <summary>
        /// 添加内部矩形（嵌套区域）
        /// </summary>
        /// <param name="innerRect">内部矩形</param>
        /// <param name="formSize">窗体尺寸</param>
        /// <param name="parentRect">父矩形</param>
        public void AddInnerRectangle(Rectangle innerRect, Size formSize, Rectangle parentRect)
        {
            Type = AreaType.CustomArea;
            ParentRect = parentRect;

            // 确保嵌套区域在父区域内部
            Rectangle nestedRect = new Rectangle(
                Math.Max(innerRect.X, parentRect.X),
                Math.Max(innerRect.Y, parentRect.Y),
                Math.Min(innerRect.Width, parentRect.Right - Math.Max(innerRect.X, parentRect.X)),
                Math.Min(innerRect.Height, parentRect.Bottom - Math.Max(innerRect.Y, parentRect.Y))
            );

            if (nestedRect.Width > 0 && nestedRect.Height > 0)
            {
                NestedRects.Add(nestedRect);

                // 计算相对比例
                float relX = (float)nestedRect.X / formSize.Width;
                float relY = (float)nestedRect.Y / formSize.Height;
                float relWidth = (float)nestedRect.Width / formSize.Width;
                float relHeight = (float)nestedRect.Height / formSize.Height;

                // 添加为组件
                AreaComponent component = new AreaComponent
                {
                    Type = AreaType.RelativeArea,
                    AbsoluteRect = nestedRect,
                    RelativeRect = new RectangleF(relX, relY, relWidth, relHeight)
                };
                Components.Add(component);

                // 设置最近使用的区域
                OriginalRect = nestedRect;
                RelativeRect = new RectangleF(relX, relY, relWidth, relHeight);
            }
        }

        /// <summary>
        /// 添加工具栏项目
        /// </summary>
        /// <param name="item">工具栏项目</param>
        /// <param name="parent">父控件</param>
        /// <param name="itemRect">项目矩形</param>
        /// <param name="form">所在窗体</param>
        public void AddToolStripItem(ToolStripItem item, Control parent, Rectangle itemRect, Form form)
        {
            Type = AreaType.ControlBased;

            // 始终重新计算精确位置，不依赖传入的itemRect
            Rectangle calculatedRect = Rectangle.Empty;

            try
            {
                ToolStrip strip = item.Owner;
                if (strip != null)
                {
                    // 获取ToolStrip在屏幕上的绝对位置
                    Point screenPos = strip.PointToScreen(Point.Empty);

                    // 获取项目在其所有者中的位置
                    Rectangle itemBounds = item.Bounds;

                    // 计算项目在屏幕上的绝对位置
                    Rectangle screenRect = new Rectangle(
                        screenPos.X + itemBounds.X,
                        screenPos.Y + itemBounds.Y,
                        Math.Max(itemBounds.Width, 5),  // 确保有最小宽度
                        Math.Max(itemBounds.Height, 5)); // 确保有最小高度

                    // 转换为窗体客户区坐标
                    calculatedRect = form.RectangleToClient(screenRect);

                    // 如果计算结果合理，使用计算结果
                    if (calculatedRect.Width > 0 && calculatedRect.Height > 0 &&
                        calculatedRect.X >= 0 && calculatedRect.Y >= 0)
                    {
                        itemRect = calculatedRect;
                    }
                    else if (itemRect.IsEmpty || itemRect.Width <= 0 || itemRect.Height <= 0)
                    {
                        // 如果传入的也不合理，创建一个默认大小的矩形
                        // 获取父控件在表单中的位置
                        Rectangle parentRect = GetControlRectInForm(parent, form);
                        itemRect = new Rectangle(parentRect.X, parentRect.Y, 20, 20);
                    }
                }
            }
            catch
            {
                // 如果计算失败，检查传入的矩形
                if (itemRect.IsEmpty || itemRect.Width <= 0 || itemRect.Height <= 0)
                {
                    // 使用默认矩形
                    Rectangle parentRect = GetControlRectInForm(parent, form);
                    itemRect = new Rectangle(parentRect.X, parentRect.Y, 20, 20);
                }
            }

            // 创建控件锚点
            ControlAnchor anchor = new ControlAnchor
            {
                ControlId = parent.Name + "." + item.Name,
                ControlPath = parent.Name + "." + item.Name,
                AnchorPoint = AnchorPoint.TopLeft,
                OriginalRect = itemRect
            };

            // 添加到锚点列表
            Anchors.Add(anchor);

            // 记录原始区域
            OriginalRect = itemRect;

            // 计算相对窗体的位置比例
            Size formSize = form.ClientSize;
            RelativeRect = new RectangleF(
                (float)itemRect.X / formSize.Width,
                (float)itemRect.Y / formSize.Height,
                (float)itemRect.Width / formSize.Width,
                (float)itemRect.Height / formSize.Height);

            // 添加为组件
            AreaComponent component = new AreaComponent
            {
                Id = Guid.NewGuid().ToString(),
                Type = AreaType.ControlBased,
                Anchor = anchor,
                AbsoluteRect = itemRect,
                RelativeRect = RelativeRect
            };

            Components.Add(component);
        }

        /// <summary>
        /// 根据控件锚点获取控件区域（在窗体上的位置）
        /// </summary>
        /// <param name="form">窗体</param>
        /// <param name="anchor">控件锚点</param>
        /// <returns>控件在窗体上的矩形区域</returns>
        public Rectangle GetControlBasedRect(Form form, ControlAnchor anchor)
        {
            return GetControlBasedRectInternal(form, anchor);
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取控件路径
        /// </summary>
        /// <param name="control">控件</param>
        /// <returns>控件路径</returns>
        private string GetControlPath(Control control)
        {
            if (control == null) return string.Empty;

            List<string> path = new List<string>();
            if (!string.IsNullOrEmpty(control.Name))
                path.Add(control.Name);
            else if (!string.IsNullOrEmpty(control.AccessibleDefaultActionDescription))
                path.Add(control.AccessibleDefaultActionDescription);

            Control parent = control.Parent;
            while (parent != null)
            {
                if (!string.IsNullOrEmpty(parent.Name))
                    path.Insert(0, parent.Name);
                else if (!string.IsNullOrEmpty(parent.AccessibleDefaultActionDescription))
                    path.Insert(0, parent.AccessibleDefaultActionDescription);

                parent = parent.Parent;
            }

            return string.Join(".", path);
        }

        /// <summary>
        /// 获取控件在窗体中的位置
        /// </summary>
        /// <param name="control">控件</param>
        /// <param name="form">窗体</param>
        /// <returns>控件在窗体中的矩形</returns>
        private Rectangle GetControlRectInForm(Control control, Form form)
        {
            if (control == null || form == null) return Rectangle.Empty;

            // 获取控件相对于窗体的位置
            Point location = form.PointToClient(control.Parent.PointToScreen(control.Location));

            // 返回控件在窗体中的矩形
            return new Rectangle(location, control.Size);
        }

        /// <summary>
        /// 计算基于锚点的区域
        /// </summary>
        private Rectangle CalculateAnchoredRect(Form form)
        {
            if (Anchors.Count == 0)
                return OriginalRect;

            // 如果只有一个锚点，使用它的区域加上偏移
            if (Anchors.Count == 1)
            {
                Rectangle baseRect = GetControlBasedRect(form, Anchors[0]);
                Point anchorPoint = GetAnchorPoint(baseRect, Anchors[0].AnchorPoint);

                // 添加全局偏移量
                anchorPoint.Offset(Offset);

                // 计算最终区域
                if (SizeType == SizeMode.Fixed && !FixedSize.IsEmpty)
                {
                    // 使用固定大小
                    int x = anchorPoint.X - FixedSize.Width / 2;
                    int y = anchorPoint.Y - FixedSize.Height / 2;
                    return new Rectangle(x, y, FixedSize.Width, FixedSize.Height);
                }
                else if (SizeType == SizeMode.Relative && !RelativeSize.IsEmpty)
                {
                    // 使用相对大小
                    Size formSize = form.ClientSize;
                    int width = (int)(formSize.Width * RelativeSize.Width);
                    int height = (int)(formSize.Height * RelativeSize.Height);
                    int x = anchorPoint.X - width / 2;
                    int y = anchorPoint.Y - height / 2;
                    return new Rectangle(x, y, width, height);
                }
                else
                {
                    // 使用原始大小
                    int x = anchorPoint.X - baseRect.Width / 2;
                    int y = anchorPoint.Y - baseRect.Height / 2;
                    return new Rectangle(x, y, baseRect.Width, baseRect.Height);
                }
            }

            // 多个锚点，使用组合方式
            return CombineAreas(form);
        }

        /// <summary>
        /// 组合多个区域
        /// </summary>
        /// <param name="form">窗体</param>
        /// <returns>组合后的区域矩形</returns>
        private Rectangle CombineAreas(Form form)
        {
            // 快速检查：如果没有任何要组合的元素，直接返回原始区域
            if (Components.Count == 0 && Anchors.Count == 0)
                return OriginalRect;

            List<Rectangle> areas = new List<Rectangle>();

            // 优先从Components收集区域（Components是最新且最完整的信息来源）
            if (Components.Count > 0)
            {
                foreach (var component in Components)
                {
                    Rectangle componentRect = Rectangle.Empty;

                    switch (component.Type)
                    {
                        case AreaType.ControlBased:
                            if (component.Anchor != null)
                                componentRect = GetControlBasedRect(form, component.Anchor);
                            break;

                        case AreaType.AbsoluteArea:
                            componentRect = component.AbsoluteRect;
                            break;

                        case AreaType.RelativeArea:
                            componentRect = GetRelativeRect(component.RelativeRect, form.ClientSize);
                            break;
                    }

                    if (!componentRect.IsEmpty)
                        areas.Add(componentRect);
                }
            }
            // 如果没有Components但有Anchors，使用Anchors
            else if (Anchors.Count > 0)
            {
                foreach (var anchor in Anchors)
                {
                    Rectangle anchorRect = GetControlBasedRect(form, anchor);
                    if (!anchorRect.IsEmpty)
                        areas.Add(anchorRect);
                }
            }

            // 如果没有收集到有效区域，返回原始区域（而不是空区域）
            if (areas.Count == 0)
                return OriginalRect;

            return GetBoundingBox(areas);
        }

        /// <summary>
        /// 计算混合缩放的区域
        /// </summary>
        private Rectangle CalculateMixedScalingRect(Form form)
        {
            // 基本计算同SmartHighlightArea
            // 这里只是占位，实际实现需要更复杂的逻辑
            return OriginalRect;
        }

        /// <summary>
        /// 获取包络矩形
        /// </summary>
        private Rectangle GetBoundingBox(List<Rectangle> rects)
        {
            if (rects == null || rects.Count == 0)
                return Rectangle.Empty;

            return rects.Aggregate(rects.First(), (current, rect) => Rectangle.Union(current, rect));
        }

        /// <summary>
        /// 基于控件获取区域
        /// </summary>
        private Rectangle GetControlBasedRectInternal(Form form, ControlAnchor anchor)
        {
            try
            {
                var rect = HighlightCalculator.GetControlRectangle(anchor.ControlPath, form);
                if (rect.IsEmpty)
                {
                    rect = HighlightCalculator.GetElementRect(anchor.ControlId, form);
                }
                return rect;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GetControlBasedRect 异常: {ex.Message}");
            }

            return Rectangle.Empty;
        }

        /// <summary>
        /// 获取相对区域
        /// </summary>
        private Rectangle GetRelativeRect(RectangleF relative, Size currentSize)
        {
            return new Rectangle(
                (int)(relative.X * currentSize.Width),
                (int)(relative.Y * currentSize.Height),
                (int)(relative.Width * currentSize.Width),
                (int)(relative.Height * currentSize.Height));
        }

        /// <summary>
        /// 获取锚点位置
        /// </summary>
        private Point GetAnchorPoint(Rectangle rect, AnchorPoint anchorPoint)
        {
            switch (anchorPoint)
            {
                case AnchorPoint.TopLeft:
                    return new Point(rect.Left, rect.Top);

                case AnchorPoint.TopCenter:
                    return new Point(rect.Left + rect.Width / 2, rect.Top);

                case AnchorPoint.TopRight:
                    return new Point(rect.Right, rect.Top);

                case AnchorPoint.MiddleLeft:
                    return new Point(rect.Left, rect.Top + rect.Height / 2);

                case AnchorPoint.Center:
                    return new Point(rect.Left + rect.Width / 2, rect.Top + rect.Height / 2);

                case AnchorPoint.MiddleRight:
                    return new Point(rect.Right, rect.Top + rect.Height / 2);

                case AnchorPoint.BottomLeft:
                    return new Point(rect.Left, rect.Bottom);

                case AnchorPoint.BottomCenter:
                    return new Point(rect.Left + rect.Width / 2, rect.Bottom);

                case AnchorPoint.BottomRight:
                    return new Point(rect.Right, rect.Bottom);

                default:
                    return new Point(rect.Left + rect.Width / 2, rect.Top + rect.Height / 2);
            }
        }

        /// <summary>
        /// 更新组合区域
        /// </summary>
        private void UpdateCombinedArea(Size formSize)
        {
            // 如果有组件，根据组合模式更新原始矩形
            if (Components.Count > 0)
            {
                List<Rectangle> rects = new List<Rectangle>();

                foreach (var component in Components)
                {
                    if (!component.AbsoluteRect.IsEmpty)
                        rects.Add(component.AbsoluteRect);
                }

                if (rects.Count > 0)
                {
                    OriginalRect = GetBoundingBox(rects);

                    // 更新相对矩形
                    RelativeRect = new RectangleF(
                        (float)OriginalRect.X / formSize.Width,
                        (float)OriginalRect.Y / formSize.Height,
                        (float)OriginalRect.Width / formSize.Width,
                        (float)OriginalRect.Height / formSize.Height);
                }
            }
        }

        #endregion
    }
}