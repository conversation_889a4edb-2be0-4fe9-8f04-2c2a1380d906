using System.Text;

namespace UtfUnknown.Core.Probers
{
    public class Latin1Prober : <PERSON><PERSON>tProber
    {
        private static readonly byte[] Latin1CharToClass =
        {
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            2,
            1,
            1,
            1,
            1,
            1,
            1,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            1,
            1,
            1,
            1,
            1,
            1,
            0,
            1,
            7,
            1,
            1,
            1,
            1,
            1,
            1,
            5,
            1,
            5,
            0,
            5,
            0,
            0,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            7,
            1,
            7,
            0,
            7,
            5,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            4,
            4,
            4,
            4,
            4,
            4,
            5,
            5,
            4,
            4,
            4,
            4,
            4,
            4,
            4,
            4,
            5,
            5,
            4,
            4,
            4,
            4,
            4,
            1,
            4,
            4,
            4,
            4,
            4,
            5,
            5,
            5,
            6,
            6,
            6,
            6,
            6,
            6,
            7,
            7,
            6,
            6,
            6,
            6,
            6,
            6,
            6,
            6,
            7,
            7,
            6,
            6,
            6,
            6,
            6,
            1,
            6,
            6,
            6,
            6,
            6,
            7,
            7,
            7
        };

        private static readonly byte[] Latin1ClassModel =
        {
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            0,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            0,
            3,
            3,
            3,
            1,
            1,
            3,
            3,
            0,
            3,
            3,
            3,
            1,
            2,
            1,
            2,
            0,
            3,
            3,
            3,
            3,
            3,
            3,
            3,
            0,
            3,
            1,
            3,
            1,
            1,
            1,
            3,
            0,
            3,
            1,
            3,
            1,
            1,
            3,
            3
        };

        private readonly int[] _freqCounter = new int[4];

        private byte _lastCharClass;

        public Latin1Prober()
        {
            Reset();
        }

        public override string GetCharsetName()
        {
            return "windows-1252";
        }

        public override void Reset()
        {
            state = ProbingState.Detecting;
            _lastCharClass = 1;
            for (var i = 0; i < 4; i++) _freqCounter[i] = 0;
        }

        public override ProbingState HandleData(byte[] buf, int offset, int len)
        {
            var array = FilterWithEnglishLetters(buf, offset, len);
            foreach (var i in array)
            {
                var b = Latin1CharToClass[i];
                var b2 = Latin1ClassModel[_lastCharClass * 8 + b];
                if (b2 == 0)
                {
                    state = ProbingState.NotMe;
                    break;
                }

                _freqCounter[b2]++;
                _lastCharClass = b;
            }

            return state;
        }

        public override float GetConfidence(StringBuilder status = null)
        {
            if (state == ProbingState.NotMe) return 0.01f;
            var num = 0;
            for (var i = 0; i < 4; i++) num += _freqCounter[i];
            float num2;
            if (num <= 0)
            {
                num2 = 0f;
            }
            else
            {
                num2 = _freqCounter[3] * 1f / num;
                num2 -= _freqCounter[1] * 20f / num;
            }

            if (!(num2 < 0f)) return num2 * 0.5f;
            return 0f;
        }

        public override string DumpStatus()
        {
            var stringBuilder = new StringBuilder();
            stringBuilder.AppendLine($" Latin1Prober: {GetConfidence()} [{GetCharsetName()}]");
            return stringBuilder.ToString();
        }
    }
}