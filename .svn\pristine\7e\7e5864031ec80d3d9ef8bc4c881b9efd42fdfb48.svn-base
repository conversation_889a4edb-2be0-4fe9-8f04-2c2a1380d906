﻿using OCRTools.Common;
using System;
using System.IO;

namespace ImageLib
{
    public class SouGouImageUpload : BaseImageUpload
    {
        public SouGouImageUpload()
        {
            ImageType = ImageTypeEnum.搜狗;
        }

        internal override string GetResult(byte[] content, string ext)
        {
            var result = GetFromSouGou(content);
            if (string.IsNullOrEmpty(result))
            {
                result = GetFromFourUomg(content);
            }
            if (!string.IsNullOrEmpty(result))
            {
                result += "?1.png";
            }
            return result;
        }

        private string GetFromSouGou(byte[] content)
        {
            var result = "";
            try
            {
                var url = "http://pic.sogou.com/pic/upload_pic.jsp";
                var file = new UploadFileInfo()
                {
                    Name = "pic_path",
                    Filename = "test.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                result = UploadFileRequest.Post(url, new[] { file }, null);
            }
            catch (Exception)
            {

            }
            return result;
        }

        private string GetFromFourUomg(byte[] content)
        {
            var result = "";
            try
            {
                var url = "https://api.uomg.com/api/image.sogou";
                var file = new UploadFileInfo()
                {
                    Name = "Filedata",
                    Filename = "test.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                var vaules = new System.Collections.Specialized.NameValueCollection() { { "file", "multipart" } };
                var html = UploadFileRequest.Post(url, new[] { file }, vaules);
                var strFileNameSpilt = "\"imgurl\":\"";
                if (html?.Contains(strFileNameSpilt) == true)
                {
                    result = html.Substring(html.IndexOf(strFileNameSpilt) + strFileNameSpilt.Length);
                    result = result.Substring(0, result.IndexOf("\"")).Replace("\\/", "/");
                }
            }
            catch (Exception)
            {

            }
            return result;
        }
    }
}
