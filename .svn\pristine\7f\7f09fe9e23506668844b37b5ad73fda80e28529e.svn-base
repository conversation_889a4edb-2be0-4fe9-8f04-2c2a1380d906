﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text.RegularExpressions;

namespace OCRTools
{
    public static class BoxUtil
    {
        #region 从对象中获取Int32

        /// <summary>
        ///     从对象中获取Int32
        ///     added by lwy 06-03-30 20:17
        /// </summary>
        /// <param name="o"></param>
        /// <returns></returns>
        public static int GetInt32FromObject(string o)
        {
            return GetInt32FromObject(o, 0);
        }

        /// <summary>
        ///     从对象中获取Int32
        ///     added by lwy 06-03-30 20:17
        /// </summary>
        /// <param name="o"></param>
        /// <param name="leap"></param>
        /// <returns></returns>
        public static int GetInt32FromObject(string o, int leap)
        {
            int rtn;
            if (string.IsNullOrEmpty(o)) return leap;

            try
            {
                rtn = Convert.ToInt32(o);
            }
            catch (Exception)
            {
                rtn = leap;
            }

            return rtn;
        }

        #endregion

        #region 从对象中获取Boolean

        public static bool GetBooleanFromObject(object o, bool leap)
        {
            bool rtn;
            if (o == null) return leap;

            try
            {
                rtn = Convert.ToBoolean(o);
            }
            catch (Exception)
            {
                rtn = leap;
            }

            return rtn;
        }

        internal static string GetColorStr(Color backColor)
        {
            return string.Format("{0},{1},{2},{3}", backColor.A, backColor.R, backColor.G, backColor.B);
        }

        internal static Color GetColorFromString(string o, Color backColor)
        {
            Color rtn;
            if (string.IsNullOrEmpty(o)) return backColor;
            try
            {
                var strs = o.Split(new[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                if (strs.Length == 4)
                {
                    rtn = Color.FromArgb(GetInt32FromObject(strs[0]),
                        GetInt32FromObject(strs[1]),
                        GetInt32FromObject(strs[2]),
                        GetInt32FromObject(strs[3]));
                }
                else
                {
                    rtn = Color.FromArgb(GetInt32FromObject(strs[0]), GetInt32FromObject(strs[1]),
                        GetInt32FromObject(strs[2]));
                }
            }
            catch (Exception)
            {
                rtn = backColor;
            }

            return rtn;
        }

        internal static string ToTimeSpan(this DateTime dateTime)
        {
            return ((ServerTime.DateTime.ToUniversalTime().Ticks - 621355968000000000) / 10000000).ToString();
        }

        public static List<int> GetListIntFromString(string o, string strSpilt = "*")
        {
            var rtn = new List<int>();
            if (string.IsNullOrEmpty(o)) return rtn;

            try
            {
                var tmps = o.Split(new[] { strSpilt }, StringSplitOptions.RemoveEmptyEntries);
                foreach (var item in tmps) rtn.Add(GetInt32FromObject(item));
            }
            catch
            {
            }

            return rtn;
        }

        //^开始，\d匹配一个数字字符，+出现至少一次，$结尾
        private static readonly Regex IntRegex = new Regex(@"^\d+$");

        internal static bool IsInt(string strKey)
        {
            return IntRegex.IsMatch(strKey);
        }

        #endregion
    }
}