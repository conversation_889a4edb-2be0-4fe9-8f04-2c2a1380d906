﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;

namespace OCRTools.Common
{
    public class WindowSnap
    {
        private readonly Bitmap _image;
        public IntPtr hWnd;

        private WindowSnap(IntPtr hWnd, bool specialCapturing)
        {
            IsMinimized = IsIconic(hWnd);
            this.hWnd = hWnd;

            if (specialCapturing)
                EnterSpecialCapturing(hWnd);

            #region Child Support (Enter)

            var wInfo = new WINDOWINFO
            {
                cbSize = WINDOWINFO.GetSize()
            };
            GetWindowInfo(hWnd, ref wInfo);

            var isChild = false;
            var parent = GetParent(hWnd);
            var pos = new Rectangle();
            var parentPos = new Rectangle();

            if (ForceMdiCapturing && parent != IntPtr.Zero && (wInfo.dwExStyle & ExtendedWindowStyles.WS_EX_MDICHILD) ==
                ExtendedWindowStyles.WS_EX_MDICHILD
            //&& ( 
            //(wInfo.dwStyle & WindowStyles.WS_CHILDWINDOW) == WindowStyles.WS_CHILDWINDOW|| 
            //(wInfo.dwStyle & WindowStyles.WS_CHILD) == WindowStyles.WS_CHILD) 
            ) //added 10 october 2007 

            {
                var name = new StringBuilder();
                GetClassName(parent, name, RUNDLL.Length + 1);
                if (name.ToString() != RUNDLL)
                {
                    isChild = true;
                    pos = GetWindowPlacement(hWnd);
                    MoveWindow(hWnd, int.MaxValue, int.MaxValue, pos.Width, pos.Height, true);

                    SetParent(hWnd, IntPtr.Zero);

                    parentPos = GetWindowPlacement(parent);
                }
            }

            #endregion

            var rect = GetWindowPlacement(hWnd);

            Size = rect.Size;
            Location = rect.Location;
            Text = GetWindowText(hWnd);
            _image = GetWindowImage(hWnd, Size);

            #region Child Support (Exit)

            if (isChild)
            {
                SetParent(hWnd, parent);

                //int x = pos.X - parentPos.X; 
                //int y = pos.Y - parentPos.Y; 

                var x = wInfo.rcWindow.Left - parentPos.X;
                var y = wInfo.rcWindow.Top - parentPos.Y;

                if ((wInfo.dwStyle & WindowStyles.WS_THICKFRAME) == WindowStyles.WS_THICKFRAME)
                {
                    x -= SystemInformation.Border3DSize.Width;
                    y -= SystemInformation.Border3DSize.Height;
                }

                MoveWindow(hWnd, x, y, pos.Width, pos.Height, true);
            }

            #endregion

            if (specialCapturing)
                ExitSpecialCapturing(hWnd);
        }

        /// <summary>
        ///     Get the Captured Image of the Window
        /// </summary>
        public Bitmap Image
        {
            get
            {
                if (_image != null)
                    return _image;
                return null;
            }
        }

        /// <summary>
        ///     Get Size of Snapped Window
        /// </summary>
        public Size Size { get; }

        /// <summary>
        ///     Get Location of Snapped Window
        /// </summary>
        public Point Location { get; }

        /// <summary>
        ///     Get Title of Snapped Window
        /// </summary>
        public string Text { get; }

        /// <summary>
        ///     Get Handle of Snapped Window
        /// </summary>
        public IntPtr Handle => hWnd;

        /// <summary>
        ///     if the state of the window is minimized return true otherwise returns false
        /// </summary>
        public bool IsMinimized { get; }

        /// <summary>
        ///     Gets the Name and Handle of the Snapped Window
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            var str = new StringBuilder();
            str.AppendFormat("Window Text: {0}, Handle: {1}", Text, hWnd.ToString());

            return str.ToString();
        }

        #region Win32

        private const string PROGRAMMANAGER = "Program Manager";
        private const string RUNDLL = "RunDLL";

        private const uint WM_PAINT = 0x000F;

        private const int GWL_EXSTYLE = -20;
        private const int WS_EX_LAYERED = 0x80000;
        private const int LWA_ALPHA = 0x2;

        private enum ShowWindowEnum
        {
            Hide = 0,
            ShowNormal = 1,
            ShowMinimized = 2,
            ShowMaximized = 3,
            Maximize = 3,
            ShowNormalNoActivate = 4,
            Show = 5,
            Minimize = 6,
            ShowMinNoActivate = 7,
            ShowNoActivate = 8,
            Restore = 9,
            ShowDefault = 10,
            ForceMinimized = 11
        }

        private struct RECT
        {
            private int right;
            private int bottom;

            public int Left { get; }

            public int Top { get; }

            public int Width => right - Left;

            public int Height => bottom - Top;

            public static implicit operator Rectangle(RECT rect)
            {
                return new Rectangle(rect.Left, rect.Top, rect.Width, rect.Height);
            }
        }

        [DllImport("user32")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool ShowWindow(IntPtr hWnd, ShowWindowEnum flags);

        [DllImport("user32.dll")]
        private static extern uint SendMessage(IntPtr hWnd, uint msg, uint wParam, uint lParam);

        [DllImport("user32")]
        private static extern int GetWindowRect(IntPtr hWnd, ref RECT rect);

        [DllImport("user32")]
        private static extern int PrintWindow(IntPtr hWnd, IntPtr dc, uint flags);

        [DllImport("user32")]
        private static extern int GetWindowText(IntPtr hWnd, StringBuilder text, int maxCount);

        [DllImport("user32")]
        private static extern int GetWindowTextLength(IntPtr hWnd);

        [DllImport("user32")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool IsWindowVisible(IntPtr hWnd);

        [DllImport("user32")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool IsIconic(IntPtr hWnd);

        private delegate bool EnumWindowsCallbackHandler(IntPtr hWnd, IntPtr lParam);

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool EnumWindows(EnumWindowsCallbackHandler lpEnumFunc, IntPtr lParam);

        [DllImport("user32")]
        private static extern int GetWindowLong(IntPtr hWnd, int index);

        [DllImport("user32")]
        private static extern int SetWindowLong(IntPtr hWnd, int index, int dwNewLong);

        [DllImport("user32")]
        private static extern int SetLayeredWindowAttributes(IntPtr hWnd, byte crey, byte alpha, int flags);

        #region Update for 4th October 2007

        [DllImport("user32")]
        private static extern int GetClassName(IntPtr hWnd, StringBuilder name, int maxCount);

        [DllImport("user32")]
        private static extern IntPtr GetParent(IntPtr hWnd);

        [DllImport("user32")]
        private static extern IntPtr SetParent(IntPtr child, IntPtr newParent);

        [DllImport("user32.dll")]
        private static extern bool MoveWindow(IntPtr hWnd, int x, int y, int nWidth, int nHeight, bool reDraw);

        [DllImport("user32.dll")]
        private static extern bool GetWindowInfo(IntPtr hwnd, ref WINDOWINFO pwi);

        [StructLayout(LayoutKind.Sequential)]
        private struct WINDOWINFO
        {
            public uint cbSize;
            public readonly RECT rcWindow;
            public readonly RECT rcClient;
            public readonly WindowStyles dwStyle;
            public readonly ExtendedWindowStyles dwExStyle;
            public readonly uint dwWindowStatus;
            public readonly uint cxWindowBorders;
            public readonly uint cyWindowBorders;
            public readonly ushort atomWindowType;
            public readonly ushort wCreatorVersion;

            public static uint GetSize()
            {
                return (uint)Marshal.SizeOf(typeof(WINDOWINFO));
            }
        }

        [Flags]
        private enum WindowStyles : uint
        {
            WS_THICKFRAME = 0x00040000
        }

        #endregion

        #region Update for 10October 2007

        [Flags]
        private enum ExtendedWindowStyles : uint
        {
            WS_EX_MDICHILD = 0x00000040
        }

        #endregion

        #endregion

        #region Statics

        #region Update for 10 Ocrober 2007

        /// <summary>
        ///     if is true ,will force the mdi child to be captured completely ,maybe incompatible with some programs
        /// </summary>
        public static bool ForceMdiCapturing { get; set; } = true;

        #endregion

        [ThreadStatic] private static bool _countMinimizedWindows;

        [ThreadStatic] private static bool _useSpecialCapturing;

        [ThreadStatic] private static WindowSnapCollection _windowSnaps;

        [ThreadStatic] public static int winLong;

        [ThreadStatic] private static bool _minAnimateChanged;


        private static bool EnumWindowsCallback(IntPtr hWnd, IntPtr lParam)
        {
            var specialCapturing = false;

            if (hWnd == IntPtr.Zero) return false;

            if (!IsWindowVisible(hWnd)) return true;

            if (!_countMinimizedWindows)
            {
                if (IsIconic(hWnd)) return true;
            }
            else if (IsIconic(hWnd) && _useSpecialCapturing)
            {
                specialCapturing = true;
            }

            if (GetWindowText(hWnd) == PROGRAMMANAGER) return true;

            _windowSnaps.Add(new WindowSnap(hWnd, specialCapturing));

            return true;
        }

        ///// <summary>
        /////     Get the collection of WindowSnap instances fro all available windows
        ///// </summary>
        ///// <param name="minimized">Capture a window even it's Minimized</param>
        ///// <param name="specialCapturring">use special capturing method to capture minmized windows</param>
        ///// <returns>return collections of WindowSnap instances</returns>
        //public static WindowSnapCollection GetAllWindows(bool minimized, bool specialCapturring)
        //{
        //    _windowSnaps = new WindowSnapCollection();
        //    _countMinimizedWindows = minimized; //set minimized flag capture 
        //    _useSpecialCapturing = specialCapturring; //set specialcapturing flag 

        //    var callback = new EnumWindowsCallbackHandler(EnumWindowsCallback);
        //    EnumWindows(callback, IntPtr.Zero);

        //    return new WindowSnapCollection(_windowSnaps.ToArray(), true);
        //}

        ///// <summary>
        /////     Get the collection of WindowSnap instances fro all available windows
        ///// </summary>
        ///// <returns>return collections of WindowSnap instances</returns>
        //public static WindowSnapCollection GetAllWindows()
        //{
        //    return GetAllWindows(false, false);
        //}

        /// <summary>
        ///     Take a Snap from the specific Window
        /// </summary>
        /// <param name="hWnd">Handle of the Window</param>
        /// <param name="useSpecialCapturing">if you need to capture from the minimized windows set it true,otherwise false</param>
        /// <returns></returns>
        public static WindowSnap GetWindowSnap(IntPtr hWnd, bool useSpecialCapturing)
        {
            if (!useSpecialCapturing)
                return new WindowSnap(hWnd, false);
            return new WindowSnap(hWnd, NeedSpecialCapturing(hWnd));
        }

        private static bool NeedSpecialCapturing(IntPtr hWnd)
        {
            if (IsIconic(hWnd)) return true;
            return false;
        }

        private static Bitmap GetWindowImage(IntPtr hWnd, Size size)
        {
            try
            {
                if (size.IsEmpty || size.Height < 0 || size.Width < 0) return null;

                var bmp = new Bitmap(size.Width, size.Height);
                var g = Graphics.FromImage(bmp);
                var dc = g.GetHdc();

                PrintWindow(hWnd, dc, 0);

                g.ReleaseHdc();
                g.Dispose();

                return bmp;
            }
            catch
            {
                return null;
            }
        }

        private static string GetWindowText(IntPtr hWnd)
        {
            var length = GetWindowTextLength(hWnd) + 1;
            var name = new StringBuilder(length);

            GetWindowText(hWnd, name, length);

            return name.ToString();
        }

        private static Rectangle GetWindowPlacement(IntPtr hWnd)
        {
            var rect = new RECT();

            GetWindowRect(hWnd, ref rect);

            return rect;
        }

        private static void EnterSpecialCapturing(IntPtr hWnd)
        {
            if (XpAppearance.MinAnimate)
            {
                XpAppearance.MinAnimate = false;
                _minAnimateChanged = true;
            }


            winLong = GetWindowLong(hWnd, GWL_EXSTYLE);
            SetWindowLong(hWnd, GWL_EXSTYLE, winLong | WS_EX_LAYERED);
            SetLayeredWindowAttributes(hWnd, 0, 1, LWA_ALPHA);

            ShowWindow(hWnd, ShowWindowEnum.Restore);

            SendMessage(hWnd, WM_PAINT, 0, 0);
        }

        private static void ExitSpecialCapturing(IntPtr hWnd)
        {
            ShowWindow(hWnd, ShowWindowEnum.Minimize);
            SetWindowLong(hWnd, GWL_EXSTYLE, winLong);

            if (_minAnimateChanged)
            {
                XpAppearance.MinAnimate = true;
                _minAnimateChanged = false;
            }
        }

        #endregion
    }

    public class WindowSnapCollection : List<WindowSnap>
    {
        private const string READONLYEXCEPTIONTEXT = "The Collection is marked as Read-Only and it cannot be modified";

        [ThreadStatic] private static IntPtr _checkHWnd;

        public WindowSnapCollection(WindowSnap[] items, bool asReadOnly)
        {
            AddRange(items);
            TrimExcess();
            ReadOnly = asReadOnly;
        }

        public WindowSnapCollection()
        {
            ReadOnly = false;
        }

        public bool ReadOnly { get; }

        private void ThrowAReadonlyException()
        {
            throw new Exception(READONLYEXCEPTIONTEXT);
        }

        public new void Add(WindowSnap item)
        {
            if (ReadOnly) ThrowAReadonlyException();
            base.Add(item);
        }

        public new void Clear()
        {
            if (ReadOnly) ThrowAReadonlyException();
            base.Clear();
        }

        public new void Remove(WindowSnap item)
        {
            if (ReadOnly) ThrowAReadonlyException();
            base.Remove(item);
        }

        public new void Sort()
        {
            if (ReadOnly) ThrowAReadonlyException();
            base.Sort();
        }

        public new void Sort(Comparison<WindowSnap> comparison)
        {
            if (ReadOnly) ThrowAReadonlyException();
            base.Sort(comparison);
        }

        public new void Sort(IComparer<WindowSnap> compare)
        {
            if (ReadOnly) ThrowAReadonlyException();
            base.Sort(compare);
        }

        public new void Sort(int index, int count, IComparer<WindowSnap> compare)
        {
            if (ReadOnly) ThrowAReadonlyException();
            base.Sort(index, count, compare);
        }


        public bool Contains(IntPtr hWnd)
        {
            if (GetWindowSnap(hWnd) != null) return true;
            return false;
        }

        public WindowSnap GetWindowSnap(IntPtr hWnd)
        {
            _checkHWnd = hWnd;
            return Find(IshWndPredict);
        }

        public void Update(WindowSnap item)
        {
            lock (this)
            {
                var oldSnap = GetWindowSnap(item.Handle);
                Remove(oldSnap);
                Add(item);
            }
        }

        public WindowSnapCollection GetAllMinimized()
        {
            var wsCol = (WindowSnapCollection)FindAll(IsMinimizedPredict);
            return wsCol;
        }

        private static bool IsMinimizedPredict(WindowSnap ws)
        {
            if (ws.IsMinimized) return true;
            return false;
        }

        private static bool IshWndPredict(WindowSnap ws)
        {
            if (ws.Handle == _checkHWnd)
                return true;
            return false;
        }
    }

    public static partial class XpAppearance
    {
        /// <summary>
        ///     Gets or Sets MinAnimate Effect
        /// </summary>
        public static bool MinAnimate
        {
            get
            {
                var animationInfo = new ANIMATIONINFO(false);

                SystemParametersInfo(SPI.GETANIMATION, ANIMATIONINFO.GetSize(), ref animationInfo, SPIF.None);
                return animationInfo.MinAnimate;
            }
            set
            {
                var animationInfo = new ANIMATIONINFO(value);
                SystemParametersInfo(SPI.SETANIMATION, ANIMATIONINFO.GetSize(), ref animationInfo, SPIF.SENDCHANGE);
            }
        }

        [DllImport("user32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool SystemParametersInfo(SPI uiAction, uint uiParam, ref ANIMATIONINFO pvParam,
            SPIF fWinIni);

        //When using the GETANIMATION or SETANIMATION actions, the uiParam value must be set to (System.UInt32)Marshal.SizeOf(typeof(ANIMATIONINFO)). 

        [Flags]
        private enum SPIF
        {
            None = 0x00,
            SENDCHANGE = 0x02 // Broadcasts the WM_SETTINGCHANGE message after updating the user profile. 
        }

        /// <summary>
        ///     ANIMATIONINFO specifies animation effects associated with user actions.
        ///     Used with SystemParametersInfo when GETANIMATION or SETANIMATION action is specified.
        /// </summary>
        /// <remark>
        ///     The uiParam value must be set to (System.UInt32)Marshal.SizeOf(typeof(ANIMATIONINFO)) when using this structure.
        /// </remark>
        [StructLayout(LayoutKind.Sequential)]
        private struct ANIMATIONINFO
        {
            /// <summary>
            ///     Creates an AMINMATIONINFO structure.
            /// </summary>
            /// <param name="iMinAnimate">If non-zero and SETANIMATION is specified, enables minimize/restore animation.</param>
            public ANIMATIONINFO(bool iMinAnimate)
            {
                cbSize = GetSize();

                this.iMinAnimate = iMinAnimate ? 1 : 0;
            }

            /// <summary>
            ///     Always must be set to (System.UInt32)Marshal.SizeOf(typeof(ANIMATIONINFO)).
            /// </summary>
            public readonly uint cbSize;

            /// <summary>
            ///     If non-zero, minimize/restore animation is enabled, otherwise disabled.
            /// </summary>
            private int iMinAnimate;

            public bool MinAnimate
            {
                get
                {
                    if (iMinAnimate == 0) return false;
                    return true;
                }
                set => iMinAnimate = value ? 1 : 0;
            }

            public static uint GetSize()
            {
                return (uint)Marshal.SizeOf(typeof(ANIMATIONINFO));
            }
        }
    }

    public static partial class XpAppearance
    {
        // The SPI enum code borrowed from www.pinvoke.net 

        #region SPI

        /// <summary>
        ///     System-wide parameter - Used in SystemParametersInfo function
        /// </summary>
        private enum SPI : uint
        {
            /// <summary>
            ///     Retrieves the animation effects associated with user actions. The pvParam parameter must point to an ANIMATIONINFO
            ///     structure
            ///     that receives the information. Set the cbSize member of this structure and the uiParam parameter to
            ///     sizeof(ANIMATIONINFO).
            /// </summary>
            GETANIMATION = 0x0048,

            /// <summary>
            ///     Sets the animation effects associated with user actions. The pvParam parameter must point to an ANIMATIONINFO
            ///     structure
            ///     that contains the new parameters. Set the cbSize member of this structure and the uiParam parameter to
            ///     sizeof(ANIMATIONINFO).
            /// </summary>
            SETANIMATION = 0x0049
        }

        #endregion // SPI 
    }
}