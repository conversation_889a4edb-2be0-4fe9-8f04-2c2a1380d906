﻿using System.Collections.Generic;
using System.Drawing;

namespace MetroFramework
{
    public sealed class MetroPens
    {
        private static Dictionary<string, Pen> _metroPens;

        public static Pen GetSavePen(string key, Color color)
        {
            if (_metroPens == null)
                _metroPens = new Dictionary<string, Pen>();

            if (!_metroPens.ContainsKey(key))
                _metroPens.Add(key, new Pen(color, 1f));

            return _metroPens[key].Clone() as Pen;
        }
    }
}