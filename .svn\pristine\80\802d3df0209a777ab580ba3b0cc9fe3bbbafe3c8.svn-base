﻿using MetroFramework.Forms;
using System;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmViewUrl : MetroForm
    {
        public string Url { get; set; }

        public FrmViewUrl()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            wbView.ScriptErrorsSuppressed = true;
            wbView.NavigateError += WbView_NavigateError;
        }

        private void WbView_NavigateError(object sender, WebBrowserNavigateErrorEventArgs e)
        {
            CommonMethod.ShowHelpMsg("网页加载失败，将直接在浏览器打开！");
            CommonMethod.OpenUrl(Url);
        }

        private void FrmGoBuy_Load(object sender, EventArgs e)
        {
            pnlView.Dock = DockStyle.Fill;
            pnlView.Visible = true;
            pnlView.BringToFront();
            wbView.NavigateWithAutoAgent(Url);
        }

        private void wbView_DocumentCompleted(object sender, WebBrowserDocumentCompletedEventArgs e)
        {
            Text = wbView.Document?.Title ?? "OCR助手";
            Invalidate();
        }
    }
}