﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="Info_Error" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Info_Error.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="no" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Crystal Clear no.cur;System.Byte[], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="放大" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\放大.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="menu" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\menu.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="vip_3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\vip_3.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="shadow" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAYAAAA4TnrqAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAB2dJREFUeF7tnPlvHUUQhE3CfcUECAQIEO4jEIggCrkE4hKI+z6FQCCEQEgc/7+p
        72XK6h33W8+LvbYXzUr1QwLervqme3Y2eS9r6+vry3TNCjo0A2W+lynjsZb9ZvbDVmbicNC1B1DRX+Y/
        y2kN2MRfZP8zijfOoFwXdH3QDfuo6CP6i74zgFl+tC2seJMIKIKxuRuDbiq6eR9lD9FXBFnD2w7aAFb9
        H5dBMiAbsLlbim6Vbgu6fR8U6+PH3uw1wjO4DFrNJIVVg/IqGJK7BgMRyhGJ646io0V37qFc0x648BUh
        Gpw7j1zOOAZsASv+RgYqdlOEhAEuTGH0rqJjRfcU3Vt0fEK5hmvagz3hz/AMztCyLkuBZbAyUKxCDYkV
        NBwMYvo+6X7pAelE0YNFD00o13BN6uMDP/gyQPzimytCI98yYCkskxwDdWRjY+Pw3KUcQKPTGNExYGaS
        wopdVYPiOpoVn5uUg5G9mzyE0lUDS7urhhVBMcsRFHN/LCs+NykHY8p4Mprk4jKwuOm7uwawYlfRht7M
        uQGtygpw4+NZ8blJOU5K7G0GRj6PpDd9OAy6q4ZVjx+bILNNy3LjE1nxuUk5npAeIU/JRT5ykrcexxSW
        RzB2FRebITNO657Mis9NynFKAhgdRi7ykZMrdtdgFGtYHkF3lcePx/DD0uNZ8blJOV6UniVPyUU+j6O7
        K45iCovWq7tqMX7So9JTWfG5STlelk6Tp+TyONbdBY8tsLiB9yvDYoahzcFu0VXSqaz43KQc56Uz5Cm5
        yEdO8pI7wvK+tYAFNcOKI8gjlZMvM81myCqczorPTcpxWTpLnpKLfOQkL7njKBrWoWWweIx6BHmNeExi
        xs9kxecm5XhNeoU8JRf5yOlRJP9SWHFz5481uPghWpN3L54ctOxLWfG5STnekC6Sp+QiHznJ630LDvDY
        3ORrWJwv4n7FU4LH65PS89LZrPjcpBxvSx5FcpGPnH4qet+CRzMs71dPSy9I57Lic5NyvCsxiudKLvJ5
        32qCFZ+EnDfY7HxkeEZivs9nxecm5XhPep08JRf5fIQgN/nhMDg+ZLB8GDWszc1dupAVn5uU4wOJfetC
        yeVNPsKCQxMsHp+8AvCEWJyvJDbDi1nxuUk5PpTeIk/J5fMWecnt48OOYF3Kis9NyvGRBKxLJdeOYXHm
        4HFqWLwiXM6Kz03K8bHkJyK5DIu85N4RrOek/xOsT6QIi3wdVibl6LBapRwdVquUo8NqlXJ0WK1Sjg6r
        VcrRYbVKOTqsVilHh9Uq5eiwWqUcHVarlKPDapVydFitUo4Oq1XK0WG1Sjk6rFYpR4fVKuXosFqlHB1W
        q5Sjw2qVckwKq//tzjaw+t8b7hBW/xvp8osIq3/WoRFW/xTNlfyjn6Lpn89a4fNZGaz+yb8RWP0zpQ2f
        KeUG/dPKV/KSe/TTyjUsHx/Y7LxvLT4HL9G6tPCbEhslp+GvpO+kH6VfpN+kP6Q/pb+kv6V/pH8nEPfl
        /tShHnWpjw/84At/+MQvvj2CK38OfqVvWEiLp6L0qsTsG9gX0rfSD9JPEmZ/lTD+exFBdlu+N3WoR13q
        4wM/+DIo/OL7qr9hsdJ3dyRWg1cEzii0s4FxKv5MYhUx+b2EYVYX8z9PKO5PHepRl/r4wA++DAq/+Mb/
        VX93p97kPYqcN/xUdHcx43yrijamMCtFa78jvS9h7lPpc+lL6WvpG4kAU4n7U4d61KU+PvCDL/zhE7/4
        3pVvhXnfqruLVwBmmsern4wUZIVoafYAVo2nDCvIOYbV5KTMqwXGeXllHHZb3Jf7U4d61KU+PvCDL/zh
        E7/49hOQPOQiX91V3q9SWHHf4nzh7mKGN7/JKrEZUoiVoZWZfVaLpwuPY4PjhIxZ3sEQ5qeSa1CPugaE
        H3zhD5/4xTf+yeHxW/mbrPxmHEV3F49Rj6OBsSK0MDPPKhka5xaMcTJmJWl7DPN2j/mpxP2pQz3qUh8f
        +DEkfOIX3/g3KI8fOWNXeQS3wKpHEar8AJQ9jjxSDYzWZdbZHA2NFcMQJ2JeITBpgIgRmEqu4ZrUxwd+
        8GVI+MU3/g2KXFzk9F7lrtocQVTDcnfV42hgrAAty4yzKbI6FKelOdjR3rw68K7Fy6kBWvyJ5G4r3p96
        1KU+PvCDL/zhE7/4xj85yMNlUHH8Bl2Falixu2jDGhitymyzGbrLOJ+wUpyAeWWgxTHHKmKUFd0rUY+6
        1McHfvCFP3y6m/BPDo9eBEXu2FUpLAOL3ZUBYxPkYlUMjZOvwbFymGMVEWYNciq5hmtSHx8GhD9DcjeR
        YwzUoKtQBmsZMG/6NTRWiLnHiOEZIKLlEaankmu4pj3YE/7wyRUheTNfBmoLrBZgyMBYhQjN4GhpLkwh
        VhBhdK/kmvbAhS8DipDIETfzUVBS/5fZJEMaA4X6v/lXFCGhmsWC0RgsK96Em0ZwEZ4BWja3H4o+or/o
        21liviw/2gLLyv5nK97YctEa4kFR9Jf5z3JaAzaDX1TKfniZMhMHTZnvZUp4rK/9B2Z7vt/sn8v3AAAA
        AElFTkSuQmCC
</value>
  </data>
  <data name="loginurl_close" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\loginurl-close.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="滚动截屏" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\ui-scroll-pane-image.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="帮助" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\帮助.jpg;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="中文标点" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\中文标点.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="导出" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\导出.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="untop" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\untop.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="batch" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\batch.PNG;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="top" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\top.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="tick" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\tick.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="document_copy" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\document-copy.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="tips_nolight_bkg" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\tips_nolight_bkg.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="竖排" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\竖排.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="工具栏" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\工具栏.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="英文" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\英文.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="从右往左" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\从右往左.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="从上往下" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\从上往下.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="vip_0" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\vip_0.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="excel" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\icomoon-free_2014-12-23_file-excel_24_0_858585_none.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="俄语" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\俄语.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="复制" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\resources\复制.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="翻译_Small" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\翻译_Small.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="script__minus" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\script--minus.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="文件" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\文件.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Info_Info" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Info_Info.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="搜索" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\搜索.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="预览" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\预览.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="loginurl_close_hover" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\loginurl-close-hover.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="葡萄牙语" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\葡萄牙语.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="图文模式" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\图文模式.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="normal" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Crystal Clear arrow.cur;System.Byte[], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="ui_thumbnail_title" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\ui-thumbnail-title.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="设置" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\mfg-labs-iconset_2014-07-29_settings_24_0_858585_none.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="英文标点" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\英文标点.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="强制合并" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\强制合并.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="窗体" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\窗体.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="缩小" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\缩小.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="keyboard" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\keyboard.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="vip_2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\vip_2.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="自动" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\自动.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="韩语" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\韩语.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="全屏" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\全屏.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="马赛克" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\马赛克.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="fankui" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\fankui.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="从下往上" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\从下往上.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="固定区域" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\固定区域.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Info_OK" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Info_OK.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="自动分段" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\自动分段.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="语音" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\语音.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="开始" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\语音按钮.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="原始" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\原始.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="公式" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\resources\公式.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="文字模式" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\文字模式.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="qqQun" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\qqQun.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="翻译" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\翻译.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="cross" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Crystal Clear cross.cur;System.Byte[], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="bin" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\bin.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="vip_1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\vip_1.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="图文混排" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\图文混排.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="中文" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\中文.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="recommond" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\recommond.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="eraser" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\eraser.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="_new" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\new.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="qqKeFu" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\qqKeFu.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="德语" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\德语.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="image_resize" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\image-resize.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="粘贴" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\粘贴.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="mini_search" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\mini-search.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="ico" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\ico56.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="从左往右" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\从左往右.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="hot" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\hot.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="截图" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\截图.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="法语" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\法语.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="folder_open_document" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\folder-open-document.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="表格" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\表格.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="move" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Crystal Clear move.cur;System.Byte[], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="pipette" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\pipette.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="文本" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\文字小.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="日语" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\日语.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="原始格式" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\原始格式.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="历史" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\application-icon-large.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="退出" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\font-awesome_4-7-0_power-off_24_0_858585_none.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="取色器" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\取色器.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="标尺" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\标尺.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="贴图" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\贴图.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="toolbox" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\toolbox.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="camera" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\camera.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="uac" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\uac.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Interop_UIAutomationClient" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\RefDll\Interop.UIAutomationClient.dll;System.Byte[], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="显示器" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\monitor.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="窗口" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\application-text-image.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="globe" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\globe.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="LoadingSmallBlack" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\LoadingSmallBlack.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="traffic_cone" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\traffic-cone.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="upload_cloud" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\upload-cloud.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="自动标点" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\自动标点.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="UpdateFile" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\RefDll\UpdateFile.exe;System.Byte[], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>