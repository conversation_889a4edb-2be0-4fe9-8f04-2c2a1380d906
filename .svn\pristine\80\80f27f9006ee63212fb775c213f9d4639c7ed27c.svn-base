﻿using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools.UserControlEx
{
    public class MenuButton : Button
    {
        [DefaultValue(null)]
        public ContextMenuStrip Menu { get; set; }

        [DefaultValue(false)]
        public bool ShowMenuUnderCursor { get; set; }

        public void OpenMenu()
        {
            if (Menu != null)
            {
                OpenMenu(new Point(0, Height));
            }
        }

        public void OpenMenu(Point menuPosition)
        {
            if (Menu != null)
            {
                Menu.Show(this, menuPosition);
            }
        }

        protected override void OnMouseDown(MouseEventArgs mevent)
        {
            base.OnMouseDown(mevent);

            if (Menu != null && mevent.Button == MouseButtons.Left)
            {
                if (ShowMenuUnderCursor)
                {
                    OpenMenu(mevent.Location);
                }
                else
                {
                    OpenMenu();
                }
            }
        }

        protected override void OnPaint(PaintEventArgs pevent)
        {
            base.OnPaint(pevent);

            if (Menu != null)
            {
                int arrowX = ClientRectangle.Width - Padding.Right - 14;
                int arrowY = (ClientRectangle.Height / 2) - 1;

                Color color = Enabled ? ForeColor : SystemColors.ControlDark;
                using (Brush brush = new SolidBrush(color))
                {
                    Point[] arrows = new Point[] { new Point(arrowX, arrowY), new Point(arrowX + 7, arrowY), new Point(arrowX + 3, arrowY + 4) };
                    pevent.Graphics.FillPolygon(brush, arrows);
                }
            }
        }
    }
}
