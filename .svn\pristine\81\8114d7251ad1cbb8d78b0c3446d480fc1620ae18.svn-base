// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class TransformPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = TransformPatternIdentifiers.Pattern;
        public static readonly AutomationProperty CanMoveProperty = TransformPatternIdentifiers.CanMoveProperty;
        public static readonly AutomationProperty CanResizeProperty = TransformPatternIdentifiers.CanResizeProperty;
        public static readonly AutomationProperty CanRotateProperty = TransformPatternIdentifiers.CanRotateProperty;

        private IUIAutomationTransformPattern _pattern;


        protected TransformPattern(AutomationElement el, IUIAutomationTransformPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
            _pattern = pattern;
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new TransformPattern(el, (IUIAutomationTransformPattern) pattern, cached);
        }
    }

    public class TransformPattern2 : TransformPattern
    {
        public static readonly AutomationProperty CanZoomProperty = TransformPattern2Identifiers.CanZoomProperty;
        public static readonly AutomationProperty ZoomLevelProperty = TransformPattern2Identifiers.ZoomLevelProperty;

        public static readonly AutomationProperty
            ZoomMinimumProperty = TransformPattern2Identifiers.ZoomMinimumProperty;

        public static readonly AutomationProperty
            ZoomMaximumProperty = TransformPattern2Identifiers.ZoomMaximumProperty;

        public new static readonly AutomationPattern Pattern = TransformPattern2Identifiers.Pattern;

        private IUIAutomationTransformPattern2 _pattern;

        private TransformPattern2(AutomationElement el, IUIAutomationTransformPattern2 pattern2,
            IUIAutomationTransformPattern pattern, bool cached)
            : base(el, pattern, cached)
        {
            Debug.Assert(pattern2 != null);
            _pattern = pattern2;
        }

        internal new static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            TransformPattern2 result = null;
            if (pattern != null)
            {
                var basePattern =
                    (IUIAutomationTransformPattern) el.GetRawPattern(TransformPattern.Pattern, cached);
                if (basePattern != null)
                    result = new TransformPattern2(el, (IUIAutomationTransformPattern2) pattern,
                        basePattern, cached);
            }

            return result;
        }
    }
}