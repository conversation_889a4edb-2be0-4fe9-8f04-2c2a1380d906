﻿#region License Information (GPL v3)

/*
    OCRTools - A program that allows you to take screenshots and share any file type
    Copyright (c) 2007-2020 OCRTools Team

    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License
    as published by the Free Software Foundation; either version 2
    of the License, or (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.

    Optionally you can also view the license at <http://www.gnu.org/licenses/>.
*/

#endregion License Information (GPL v3)

using System;
using System.Text;
using System.Windows.Forms;

namespace OCRTools
{
    [Flags]
    public enum Modifiers
    {
        None = 0,
        Alt = 1,
        Control = 2,
        Shift = 4,
        Win = 8
    }

    internal class HotKeyEntity
    {
        public HotKeyEntity(ushort id)
        {
            ID = id;
        }

        /// <summary>
        ///     唯一Id
        /// </summary>
        public ushort ID { get; set; }

        /// <summary>
        ///     名称
        /// </summary>
        public string KeyName { get; set; }

        /// <summary>
        ///     默认值
        /// </summary>
        public Keys DefaultKey { get; set; }

        /// <summary>
        ///     描述
        /// </summary>
        public string Desc { get; set; }

        /// <summary>
        ///     分组
        /// </summary>
        public HotKeyType Group { get; set; }

        public string StrKey => ToString();

        /// <summary>
        ///     设置快捷键
        /// </summary>
        public Keys Hotkey { get; set; }

        /// <summary>
        ///     UserKeyCode
        /// </summary>
        public Keys KeyCode => Hotkey & Keys.KeyCode;

        /// <summary>
        ///     ModifyKey
        /// </summary>
        public Keys ModifiersKeys => Hotkey & Keys.Modifiers;

        public bool Control => Hotkey.HasFlag(Keys.Control);

        public bool Shift => Hotkey.HasFlag(Keys.Shift);

        public bool Alt => Hotkey.HasFlag(Keys.Alt);

        public bool Win { get; set; }

        public Modifiers ModifiersEnum
        {
            get
            {
                var modifiers = Modifiers.None;

                if (Alt) modifiers |= Modifiers.Alt;
                if (Control) modifiers |= Modifiers.Control;
                if (Shift) modifiers |= Modifiers.Shift;
                if (Win) modifiers |= Modifiers.Win;

                return modifiers;
            }
        }

        public bool IsOnlyModifiers => KeyCode == Keys.ControlKey || KeyCode == Keys.ShiftKey || KeyCode == Keys.Menu ||
                                       KeyCode == Keys.None && Win;

        public bool IsValidHotkey => KeyCode != Keys.None && !IsOnlyModifiers;

        public override string ToString()
        {
            var text = "";

            if (Control) text += "Ctrl + ";

            if (Shift) text += "Shift + ";

            if (Alt) text += "Alt + ";

            if (Win) text += "Win + ";

            if (IsOnlyModifiers)
                text += "...";
            else if (KeyCode == Keys.Back)
                text += "Backspace";
            else if (KeyCode == Keys.Return)
                text += "Enter";
            else if (KeyCode == Keys.Capital)
                text += "Caps Lock";
            else if (KeyCode == Keys.Next)
                text += "Page Down";
            else if (KeyCode == Keys.Scroll)
                text += "Scroll Lock";
            else if (KeyCode >= Keys.D0 && KeyCode <= Keys.D9)
                text += (KeyCode - Keys.D0).ToString();
            else if (KeyCode >= Keys.NumPad0 && KeyCode <= Keys.NumPad9)
                text += "Numpad " + (KeyCode - Keys.NumPad0);
            else
                text += ToStringWithSpaces(KeyCode);

            return text;
        }

        private string ToStringWithSpaces(Keys key)
        {
            if (Equals(Keys.None, key)) return string.Empty;

            var name = key.ToString();

            var result = new StringBuilder();

            for (var i = 0; i < name.Length; i++)
                if (i > 0 && char.IsUpper(name[i]))
                    result.Append(" " + name[i]);
                else
                    result.Append(name[i]);

            return result.ToString();
        }
    }

    public enum VirtualKeyCode : ushort
    {
        /// <summary>
        ///     PAGE DOWN key
        /// </summary>
        NEXT = 0x22,

        /// <summary>
        ///     HOME key
        /// </summary>
        HOME = 0x24
    }
}