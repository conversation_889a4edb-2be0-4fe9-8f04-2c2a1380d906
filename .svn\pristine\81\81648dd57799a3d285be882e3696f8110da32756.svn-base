﻿namespace OCRTools
{
    partial class FrmBatch
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            this.dgContent = new OCRTools.DataGridViewEx();
            this.文件名 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.识别为 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.输出为 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.状态 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.查看 = new System.Windows.Forms.DataGridViewLinkColumn();
            this.cmbOcrTypes = new MetroFramework.Controls.MetroComboBox();
            this.btnProcess = new MetroFramework.Controls.MetroButton();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.txtPerCount = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.cmbOutExt = new MetroFramework.Controls.MetroComboBox();
            this.btnClearFiles = new MetroFramework.Controls.MetroButton();
            this.btnAddFiles = new MetroFramework.Controls.MetroButton();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.linkLabel1 = new System.Windows.Forms.LinkLabel();
            this.btnClearSuccess = new MetroFramework.Controls.MetroButton();
            ((System.ComponentModel.ISupportInitialize)(this.dgContent)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtPerCount)).BeginInit();
            this.groupBox2.SuspendLayout();
            this.SuspendLayout();
            // 
            // dgContent
            // 
            this.dgContent.AllowUserToAddRows = false;
            this.dgContent.AllowUserToDeleteRows = false;
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.Black;
            this.dgContent.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            this.dgContent.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgContent.BackgroundColor = System.Drawing.Color.White;
            this.dgContent.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(244)))), ((int)(((byte)(244)))), ((int)(((byte)(244)))));
            dataGridViewCellStyle2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgContent.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            this.dgContent.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgContent.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.文件名,
            this.识别为,
            this.输出为,
            this.状态,
            this.查看});
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle3.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgContent.DefaultCellStyle = dataGridViewCellStyle3;
            this.dgContent.EnableHeadersVisualStyles = false;
            this.dgContent.Location = new System.Drawing.Point(20, 60);
            this.dgContent.Margin = new System.Windows.Forms.Padding(0);
            this.dgContent.Name = "dgContent";
            this.dgContent.NotShowSequence = false;
            this.dgContent.ReadOnly = true;
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle4.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgContent.RowHeadersDefaultCellStyle = dataGridViewCellStyle4;
            this.dgContent.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.AutoSizeToAllHeaders;
            this.dgContent.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.Black;
            this.dgContent.RowTemplate.Height = 30;
            this.dgContent.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgContent.Size = new System.Drawing.Size(616, 468);
            this.dgContent.TabIndex = 3;
            this.dgContent.TabStop = false;
            // 
            // 文件名
            // 
            this.文件名.DataPropertyName = "FileName";
            this.文件名.FillWeight = 20F;
            this.文件名.HeaderText = "文件名";
            this.文件名.Name = "文件名";
            this.文件名.ReadOnly = true;
            this.文件名.Width = 220;
            // 
            // 识别为
            // 
            this.识别为.DataPropertyName = "OcrType";
            this.识别为.FillWeight = 10F;
            this.识别为.HeaderText = "识别为";
            this.识别为.Name = "识别为";
            this.识别为.ReadOnly = true;
            this.识别为.Width = 80;
            // 
            // 输出为
            // 
            this.输出为.DataPropertyName = "FileType";
            this.输出为.FillWeight = 10F;
            this.输出为.HeaderText = "输出为";
            this.输出为.Name = "输出为";
            this.输出为.ReadOnly = true;
            this.输出为.Width = 80;
            // 
            // 状态
            // 
            this.状态.DataPropertyName = "State";
            this.状态.FillWeight = 10F;
            this.状态.HeaderText = "状态";
            this.状态.Name = "状态";
            this.状态.ReadOnly = true;
            // 
            // 查看
            // 
            this.查看.FillWeight = 20F;
            this.查看.HeaderText = "查看";
            this.查看.Name = "查看";
            this.查看.ReadOnly = true;
            this.查看.Text = "查看结果";
            // 
            // cmbOcrTypes
            // 
            this.cmbOcrTypes.FormattingEnabled = true;
            this.cmbOcrTypes.ItemHeight = 23;
            this.cmbOcrTypes.Location = new System.Drawing.Point(56, 20);
            this.cmbOcrTypes.Name = "cmbOcrTypes";
            this.cmbOcrTypes.Size = new System.Drawing.Size(65, 29);
            this.cmbOcrTypes.Style = MetroFramework.MetroColorStyle.Black;
            this.cmbOcrTypes.TabIndex = 5;
            this.cmbOcrTypes.TabStop = false;
            this.cmbOcrTypes.UseSelectable = true;
            // 
            // btnProcess
            // 
            this.btnProcess.Location = new System.Drawing.Point(24, 45);
            this.btnProcess.Name = "btnProcess";
            this.btnProcess.Size = new System.Drawing.Size(103, 35);
            this.btnProcess.Style = MetroFramework.MetroColorStyle.Black;
            this.btnProcess.TabIndex = 0;
            this.btnProcess.Text = "开始识别(&S)";
            this.btnProcess.UseSelectable = true;
            this.btnProcess.UseVisualStyleBackColor = true;
            this.btnProcess.Click += new System.EventHandler(this.btnProcess_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.txtPerCount);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.cmbOutExt);
            this.groupBox1.Controls.Add(this.cmbOcrTypes);
            this.groupBox1.Controls.Add(this.btnClearFiles);
            this.groupBox1.Controls.Add(this.btnAddFiles);
            this.groupBox1.Location = new System.Drawing.Point(646, 60);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(147, 275);
            this.groupBox1.TabIndex = 7;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "设置";
            // 
            // txtPerCount
            // 
            this.txtPerCount.Font = new System.Drawing.Font("宋体", 11F);
            this.txtPerCount.Location = new System.Drawing.Point(56, 98);
            this.txtPerCount.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.txtPerCount.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.txtPerCount.Name = "txtPerCount";
            this.txtPerCount.Size = new System.Drawing.Size(65, 24);
            this.txtPerCount.TabIndex = 8;
            this.txtPerCount.TabStop = false;
            this.txtPerCount.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(19, 103);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(35, 12);
            this.label3.TabIndex = 7;
            this.label3.Text = "线程:";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(19, 65);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(35, 12);
            this.label2.TabIndex = 7;
            this.label2.Text = "输出:";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(19, 27);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(35, 12);
            this.label1.TabIndex = 7;
            this.label1.Text = "类型:";
            // 
            // cmbOutExt
            // 
            this.cmbOutExt.FormattingEnabled = true;
            this.cmbOutExt.ItemHeight = 23;
            this.cmbOutExt.Location = new System.Drawing.Point(56, 59);
            this.cmbOutExt.Name = "cmbOutExt";
            this.cmbOutExt.Size = new System.Drawing.Size(65, 29);
            this.cmbOutExt.Style = MetroFramework.MetroColorStyle.Black;
            this.cmbOutExt.TabIndex = 5;
            this.cmbOutExt.TabStop = false;
            this.cmbOutExt.UseSelectable = true;
            // 
            // btnClearFiles
            // 
            this.btnClearFiles.Location = new System.Drawing.Point(31, 200);
            this.btnClearFiles.Name = "btnClearFiles";
            this.btnClearFiles.Size = new System.Drawing.Size(84, 29);
            this.btnClearFiles.Style = MetroFramework.MetroColorStyle.Black;
            this.btnClearFiles.TabIndex = 6;
            this.btnClearFiles.TabStop = false;
            this.btnClearFiles.Text = "清空所有";
            this.btnClearFiles.UseSelectable = true;
            this.btnClearFiles.UseVisualStyleBackColor = true;
            this.btnClearFiles.Click += new System.EventHandler(this.btnClearFiles_Click);
            // 
            // btnAddFiles
            // 
            this.btnAddFiles.Location = new System.Drawing.Point(31, 156);
            this.btnAddFiles.Name = "btnAddFiles";
            this.btnAddFiles.Size = new System.Drawing.Size(84, 29);
            this.btnAddFiles.Style = MetroFramework.MetroColorStyle.Black;
            this.btnAddFiles.TabIndex = 6;
            this.btnAddFiles.TabStop = false;
            this.btnAddFiles.Text = "添加文件";
            this.btnAddFiles.UseSelectable = true;
            this.btnAddFiles.UseVisualStyleBackColor = true;
            this.btnAddFiles.Click += new System.EventHandler(this.btnAddFiles_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox2.Controls.Add(this.linkLabel1);
            this.groupBox2.Controls.Add(this.btnProcess);
            this.groupBox2.Controls.Add(this.btnClearSuccess);
            this.groupBox2.Location = new System.Drawing.Point(646, 341);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(147, 187);
            this.groupBox2.TabIndex = 8;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "操作";
            // 
            // linkLabel1
            // 
            this.linkLabel1.AutoSize = true;
            this.linkLabel1.Location = new System.Drawing.Point(32, 134);
            this.linkLabel1.Name = "linkLabel1";
            this.linkLabel1.Size = new System.Drawing.Size(77, 12);
            this.linkLabel1.TabIndex = 7;
            this.linkLabel1.TabStop = true;
            this.linkLabel1.Text = "打开输出目录";
            this.linkLabel1.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel1_LinkClicked);
            // 
            // btnClearSuccess
            // 
            this.btnClearSuccess.Location = new System.Drawing.Point(24, 86);
            this.btnClearSuccess.Name = "btnClearSuccess";
            this.btnClearSuccess.Size = new System.Drawing.Size(103, 35);
            this.btnClearSuccess.Style = MetroFramework.MetroColorStyle.Black;
            this.btnClearSuccess.TabIndex = 6;
            this.btnClearSuccess.TabStop = false;
            this.btnClearSuccess.Text = "移除已识别";
            this.btnClearSuccess.UseSelectable = true;
            this.btnClearSuccess.UseVisualStyleBackColor = true;
            this.btnClearSuccess.Click += new System.EventHandler(this.btnClearSuccess_Click);
            // 
            // FrmBatch
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(803, 551);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.dgContent);
            this.Name = "FrmBatch";
            this.Text = "批量识别";
            this.Load += new System.EventHandler(this.FrmBatch_Load);
            ((System.ComponentModel.ISupportInitialize)(this.dgContent)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtPerCount)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DataGridViewEx dgContent;
        private MetroFramework.Controls.MetroComboBox cmbOcrTypes;
        private MetroFramework.Controls.MetroButton btnProcess;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.LinkLabel linkLabel1;
        private MetroFramework.Controls.MetroButton btnClearFiles;
        private MetroFramework.Controls.MetroButton btnAddFiles;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private MetroFramework.Controls.MetroComboBox cmbOutExt;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.DataGridViewTextBoxColumn 文件名;
        private System.Windows.Forms.DataGridViewTextBoxColumn 识别为;
        private System.Windows.Forms.DataGridViewTextBoxColumn 输出为;
        private System.Windows.Forms.DataGridViewTextBoxColumn 状态;
        private System.Windows.Forms.DataGridViewLinkColumn 查看;
        private System.Windows.Forms.NumericUpDown txtPerCount;
        private MetroFramework.Controls.MetroButton btnClearSuccess;
    }
}