﻿#region License Information (GPL v3)

/*
    ShareX - A program that allows you to take screenshots and share any file type
    Copyright (c) 2007-2021 ShareX Team

    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License
    as published by the Free Software Foundation; either version 2
    of the License, or (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.

    Optionally you can also view the license at <http://www.gnu.org/licenses/>.
*/

#endregion License Information (GPL v3)


namespace ShareX.ScreenCaptureLib
{
    public enum RegionResult
    {
        Close,
        Region,
        Fullscreen,
        Monitor,
        ActiveMonitor,
    }

    internal enum NodePosition
    {
        TopLeft,
        Top,
        TopRight,
        Right,
        BottomRight,
        Bottom,
        BottomLeft,
        Left,
        Extra
    }

    internal enum NodeShape
    {
        Square, Circle, Diamond, CustomNode
    }

    public enum RegionCaptureMode
    {
        Annotation,
        ScreenColorPicker,
        Ruler,
        OneClick,
        Editor,
    }

    public enum ShapeCategory
    {
        Region,
        Drawing,
        Effect,
        Tool
    }

    public enum ShapeType
    {
        //主菜单
        圆形区域,
        矩形区域,
        自由截图,
        截图问题反馈,
        //二级菜单
        选择并移动,
        矩形,
        圆形,
        画笔,
        直线,
        箭头,
        文字,
        文字描边,
        气泡,
        序号,
        放大镜,
        橡皮擦,
        马赛克,
        高亮,
        //裁剪
    }

    public enum BorderStyle // Localized
    {
        实线,
        虚线,
        点线,
        点划线,
        双点划线
    }

}