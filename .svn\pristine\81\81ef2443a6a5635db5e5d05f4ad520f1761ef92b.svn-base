using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;

namespace OCRTools
{
    public static class ExtensionMethods
    {
        public static Rectangle GetGroupBoundingBox(this IEnumerable<DrawObject> canvasObjects)
        {
            return (from x in canvasObjects
                select x.GetBoundingBox()).UnionRectangles();
        }

        public static Rectangle LocationOffset(this Rectangle rect, int x, int y)
        {
            return new Rectangle(rect.X + x, rect.Y + y, rect.Width, rect.Height);
        }

        public static void SafeSave(this Image image, string path, ImageFormat format)
        {
            using (var memoryStream = new MemoryStream())
            {
                using (var stream = new FileStream(path, FileMode.Create))
                {
                    if (format == ImageFormat.Gif)
                    {
                        image.Save(memoryStream, format);
                        memoryStream.WriteTo(stream);
                    }
                    else
                    {
                        var bitmap = new Bitmap(image);
                        bitmap.Save(memoryStream, format);
                        memoryStream.WriteTo(stream);
                    }
                }
            }
        }

        public static bool IsLimt(this Rectangle rectangle)
        {
            var num = Math.Abs(rectangle.Width);
            var num2 = Math.Abs(rectangle.Height);
            if (num > 6 || num2 > 6)
            {
                if (num > 0 && num2 > 0) return true;
                return false;
            }

            return false;
        }

        public static bool IsLimtM(this Rectangle rectangle)
        {
            var num = Math.Abs(rectangle.Width);
            var num2 = Math.Abs(rectangle.Height);
            if (num > 25.DpiValue() && num2 > 25.DpiValue()) return true;
            return false;
        }

        public static Rectangle GetNormalizedRectangle(this Rectangle r)
        {
            return GetNormalizedRectangle(r.X, r.Y, r.X + r.Width, r.Y + r.Height);
        }

        public static Rectangle GetNormalizedRectangle(int x1, int y1, int x2, int y2)
        {
            if (x2 < x1)
            {
                var num = x2;
                x2 = x1;
                x1 = num;
            }

            if (y2 < y1)
            {
                var num2 = y2;
                y2 = y1;
                y1 = num2;
            }

            return new Rectangle(x1, y1, x2 - x1, y2 - y1);
        }

        public static bool IsValid(this Rectangle rect)
        {
            return rect.X >= 0 && rect.Y >= 0 && rect.Width > 0 && rect.Height > 0;
        }

        public static Rectangle UnionRectangles(this IEnumerable<Rectangle> rectangles)
        {
            var rectangle = Rectangle.Empty;
            var flag = true;
            foreach (var rectangle2 in rectangles)
                if (flag)
                {
                    rectangle = rectangle2;
                    flag = false;
                }
                else
                {
                    rectangle = Rectangle.Union(rectangle, rectangle2);
                }

            return rectangle;
        }

        public static void Move<T>(this List<T> list, int oldIndex, int newIndex)
        {
            var item = list[oldIndex];
            list.RemoveAt(oldIndex);
            list.Insert(newIndex, item);
        }

        public static Point Add(this Point point, int offsetX, int offsetY)
        {
            return new Point(point.X + offsetX, point.Y + offsetY);
        }

        public static Point Add(this Point point, Point offset)
        {
            return new Point(point.X + offset.X, point.Y + offset.Y);
        }

        public static Rectangle Offset(this Rectangle rect, int offset)
        {
            return new Rectangle(rect.X - offset, rect.Y - offset, rect.Width + offset * 2, rect.Height + offset * 2);
        }

        public static bool IsEvenNumber(this int num)
        {
            return num % 2 == 0;
        }

        public static void DrawRoundedRectangle(this Graphics g, Brush brush, Rectangle rect, float radius)
        {
            g.DrawRoundedRectangle(brush, null, rect, radius);
        }

        public static void DrawRoundedRectangle(this Graphics g, Pen pen, Rectangle rect, float radius)
        {
            g.DrawRoundedRectangle(null, pen, rect, radius);
        }

        public static void DrawRoundedRectangle(this Graphics g, Brush brush, Pen pen, Rectangle rect, float radius)
        {
            using (var graphicsPath = new GraphicsPath())
            {
                graphicsPath.AddRoundedRectangleProper(rect, radius);
                if (brush != null) g.FillPath(brush, graphicsPath);
                if (pen != null) g.DrawPath(pen, graphicsPath);
            }
        }

        public static void AddRoundedRectangleProper(this GraphicsPath graphicsPath, RectangleF rect, float radius,
            float penWidth = 1f)
        {
            if (penWidth == 1f) rect = new RectangleF(rect.X, rect.Y, rect.Width - 1f, rect.Height - 1f);
            if (rect.Width > 0f && rect.Height > 0f) graphicsPath.AddRoundedRectangle(rect, radius);
        }

        public static void AddRoundedRectangle(this GraphicsPath gp, RectangleF rect, float radius)
        {
            if (radius <= 0f)
            {
                gp.AddRectangle(rect);
                return;
            }

            if (radius >= Math.Min(rect.Width, rect.Height) / 2f)
            {
                gp.AddCapsule(rect);
                return;
            }

            var num = radius * 2f;
            var rect2 = new RectangleF(size: new SizeF(num, num), location: rect.Location);
            gp.AddArc(rect2, 180f, 90f);
            rect2.X = rect.Right - num;
            gp.AddArc(rect2, 270f, 90f);
            rect2.Y = rect.Bottom - num;
            gp.AddArc(rect2, 0f, 90f);
            rect2.X = rect.Left;
            gp.AddArc(rect2, 90f, 90f);
            gp.CloseFigure();
        }

        public static void AddCapsule(this GraphicsPath gp, RectangleF rect)
        {
            try
            {
                if (rect.Width > rect.Height)
                {
                    var height = rect.Height;
                    var rect2 = new RectangleF(size: new SizeF(height, height), location: rect.Location);
                    gp.AddArc(rect2, 90f, 180f);
                    rect2.X = rect.Right - height;
                    gp.AddArc(rect2, 270f, 180f);
                }
                else if (rect.Width < rect.Height)
                {
                    var width = rect.Width;
                    var rect3 = new RectangleF(size: new SizeF(width, width), location: rect.Location);
                    gp.AddArc(rect3, 180f, 180f);
                    rect3.Y = rect.Bottom - width;
                    gp.AddArc(rect3, 0f, 180f);
                }
                else
                {
                    gp.AddEllipse(rect);
                }
            }
            catch
            {
                gp.AddEllipse(rect);
            }

            gp.CloseFigure();
        }

        public static void DrawTextWithShadow(this Graphics g, string text, PointF position, Font font, Brush textBrush,
            Brush shadowBrush)
        {
            g.DrawTextWithShadow(text, position, font, textBrush, shadowBrush, new Point(1, 1));
        }

        public static void DrawTextWithShadow(this Graphics g, string text, PointF position, Font font, Brush textBrush,
            Brush shadowBrush, Point shadowOffset)
        {
            g.DrawString(text, font, shadowBrush, position.X + shadowOffset.X, position.Y + shadowOffset.Y);
            g.DrawString(text, font, textBrush, position.X, position.Y);
        }

        public static int Between(this int num, int min, int max)
        {
            if (num <= min) return min;
            if (num >= max) return max;
            return num;
        }

        public static void DrawRectangleProper(this Graphics g, Pen pen, Rectangle rect)
        {
            if (pen.Width == 1f) rect = rect.SizeOffset(-1);
            if (rect.Width > 0 && rect.Height > 0) g.DrawRectangle(pen, rect);
        }

        public static Rectangle SizeOffset(this Rectangle rect, int width, int height)
        {
            return new Rectangle(rect.X, rect.Y, rect.Width + width, rect.Height + height);
        }

        public static Rectangle SizeOffset(this Rectangle rect, int offset)
        {
            return rect.SizeOffset(offset, offset);
        }

        public static bool IsBetween(this int num, int min, int max)
        {
            return num >= min && num <= max;
        }

        public static void DrawRectangleProper(this Graphics g, Pen pen, int x, int y, int width, int height)
        {
            g.DrawRectangleProper(pen, new Rectangle(x, y, width, height));
        }
    }
}