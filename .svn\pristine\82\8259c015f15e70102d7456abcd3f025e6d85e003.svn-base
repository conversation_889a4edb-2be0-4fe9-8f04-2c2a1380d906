using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using QiHe.CodeLib;

namespace ExcelLibrary.CompoundDocumentFormat
{
	public class CompoundDocument : IDisposable
	{
		internal FileHeader Header;

		internal int SectorSize;

		internal int ShortSectorSize;

		private int TotalSectors;

		internal MasterSectorAllocation MasterSectorAllocation;

		internal SectorAllocation SectorAllocation;

		internal ShortSectorAllocation ShortSectorAllocation;

		private MemoryStream ShortStreamContainer;

		private MemoryStream DirectoryStream;

		private Dictionary<int, DirectoryEntry> DirectoryEntries;

		internal Stream FileStorage;

		private BinaryReader Reader;

		private BinaryWriter Writer;

		private bool disposed;

		public DirectoryEntry RootStorage => DirectoryEntries[0];

		private void WriteHeader()
		{
			FileStorage.Position = 0L;
			WriteHeader(Writer, Header);
		}

		private static void WriteHeader(BinaryWriter writer, FileHeader header)
		{
			writer.Write(header.FileTypeIdentifier);
			writer.Write(header.FileIdentifier.ToByteArray());
			writer.Write(header.RevisionNumber);
			writer.Write(header.VersionNumber);
			writer.Write(header.ByteOrderMark);
			writer.Write(header.SectorSizeInPot);
			writer.Write(header.ShortSectorSizeInPot);
			writer.Write(header.UnUsed10);
			writer.Write(header.NumberOfSATSectors);
			writer.Write(header.FirstSectorIDofDirectoryStream);
			writer.Write(header.UnUsed4);
			writer.Write(header.MinimumStreamSize);
			writer.Write(header.FirstSectorIDofShortSectorAllocationTable);
			writer.Write(header.NumberOfShortSectors);
			writer.Write(header.FirstSectorIDofMasterSectorAllocationTable);
			writer.Write(header.NumberOfMasterSectors);
			WriteArrayOfInt32(writer, header.MasterSectorAllocationTable);
		}

		private static void WriteDirectoryEntry(BinaryWriter writer, DirectoryEntry entry)
		{
			writer.Write(entry.NameBuffer);
			writer.Write(entry.NameDataSize);
			writer.Write(entry.EntryType);
			writer.Write(entry.NodeColor);
			writer.Write(entry.LeftChildDID);
			writer.Write(entry.RightChildDID);
			writer.Write(entry.MembersTreeNodeDID);
			writer.Write(entry.UniqueIdentifier.ToByteArray());
			writer.Write(entry.UserFlags);
			writer.Write(entry.CreationTime.ToFileTime());
			writer.Write(entry.LastModificationTime.ToFileTime());
			writer.Write(entry.FirstSectorID);
			writer.Write(entry.StreamLength);
			writer.Write(entry.UnUsed);
		}

		public void WriteStreamData(string[] streamPath, byte[] data)
		{
			DirectoryEntry orCreateDirectoryEntry = GetOrCreateDirectoryEntry(streamPath);
			orCreateDirectoryEntry.EntryType = 2;
			orCreateDirectoryEntry.StreamLength = data.Length;
			if (orCreateDirectoryEntry.StreamLength < Header.MinimumStreamSize)
			{
				if (orCreateDirectoryEntry.FirstSectorID == -2)
				{
					orCreateDirectoryEntry.FirstSectorID = AllocateShortSector();
				}
				WriteShortStreamData(orCreateDirectoryEntry.FirstSectorID, data);
			}
			else
			{
				if (orCreateDirectoryEntry.FirstSectorID == -2)
				{
					orCreateDirectoryEntry.FirstSectorID = AllocateDataSector();
				}
				WriteStreamData(orCreateDirectoryEntry.FirstSectorID, data);
			}
		}

		internal void WriteStreamData(int startSID, byte[] data)
		{
			int num = -2;
			int num2 = startSID;
			int num3 = 0;
			while (num3 < data.Length)
			{
				if (num2 == -2)
				{
					num2 = ((num != -2) ? AllocateDataSectorAfter(num) : AllocateDataSector());
				}
				int sectorOffset = GetSectorOffset(num2);
				Writer.BaseStream.Position = sectorOffset;
				if (num3 + SectorSize < data.Length)
				{
					Writer.Write(data, num3, SectorSize);
				}
				else
				{
					Writer.Write(data, num3, data.Length - num3);
				}
				num3 += SectorSize;
				num = num2;
				num2 = SectorAllocation.GetNextSectorID(num);
			}
			if (num2 != -2 && num != -2)
			{
				SectorAllocation.LinkSectorID(num, -2);
				while (num2 != -2)
				{
					int nextSectorID = SectorAllocation.GetNextSectorID(num2);
					SectorAllocation.LinkSectorID(num2, -1);
					num2 = nextSectorID;
				}
			}
		}

		private void AppendStreamData(int startSID, int streamLength, byte[] data)
		{
			int num = startSID;
			int nextSectorID = SectorAllocation.GetNextSectorID(num);
			int num2 = 0;
			while (nextSectorID != -2)
			{
				num = nextSectorID;
				nextSectorID = SectorAllocation.GetNextSectorID(num);
				num2 += SectorSize;
			}
			if (num2 < streamLength)
			{
				int num3 = streamLength - num2;
				int num4 = SectorSize - num3;
				if (data.Length <= num4)
				{
					WriteInSector(num, num3, data, 0, data.Length);
					return;
				}
				WriteInSector(num, num3, data, 0, num4);
				nextSectorID = AllocateDataSectorAfter(num);
				byte[] array = new byte[data.Length - num4];
				Array.Copy(data, num4, array, 0, array.Length);
				WriteStreamData(nextSectorID, array);
			}
			else
			{
				nextSectorID = AllocateDataSectorAfter(num);
				WriteStreamData(nextSectorID, data);
			}
		}

		internal void WriteShortStreamData(int startSID, byte[] data)
		{
			int num = -2;
			int num2 = startSID;
			int num3 = 0;
			while (num3 < data.Length)
			{
				if (num2 == -2)
				{
					num2 = ((num != -2) ? ShortSectorAllocation.AllocateSectorAfter(num) : ShortSectorAllocation.AllocateSector());
				}
				int shortSectorOffset = GetShortSectorOffset(num2);
				ShortStreamContainer.Position = shortSectorOffset;
				if (num3 + ShortSectorSize < data.Length)
				{
					ShortStreamContainer.Write(data, num3, ShortSectorSize);
				}
				else
				{
					ShortStreamContainer.Write(data, num3, data.Length - num3);
				}
				num3 += ShortSectorSize;
				num = num2;
				num2 = ShortSectorAllocation.GetNextSectorID(num);
			}
			if (num2 != -2 && num != -2)
			{
				ShortSectorAllocation.LinkSectorID(num, -2);
				while (num2 != -2)
				{
					int nextSectorID = ShortSectorAllocation.GetNextSectorID(num2);
					ShortSectorAllocation.LinkSectorID(num2, -1);
					num2 = nextSectorID;
				}
			}
		}

		private DirectoryEntry GetOrCreateDirectoryEntry(string[] streamPath)
		{
			DirectoryEntry directoryEntry = RootStorage;
			foreach (string text in streamPath)
			{
				if (!directoryEntry.Members.ContainsKey(text))
				{
					DirectoryEntry directoryEntry2 = new DirectoryEntry(this, text);
					directoryEntry2.ID = DirectoryEntries.Count;
					DirectoryEntries.Add(directoryEntry2.ID, directoryEntry2);
					directoryEntry.AddChild(directoryEntry2);
				}
				directoryEntry = directoryEntry.Members[text];
			}
			return directoryEntry;
		}

		public void DeleteDirectoryEntry(string[] streamPath)
		{
			DirectoryEntry orCreateDirectoryEntry = GetOrCreateDirectoryEntry(streamPath);
			DeleteDirectoryEntry(orCreateDirectoryEntry);
		}

		public void DeleteDirectoryEntry(DirectoryEntry entry)
		{
			entry.EntryType = 0;
			entry.StreamLength = 0;
			entry.Parent.Members.Remove(entry.Name);
		}

		public static CompoundDocument Load(string file)
		{
			CompoundDocument compoundDocument = Open(file);
			compoundDocument.ReadAllStreamData();
			compoundDocument.Close();
			return compoundDocument;
		}

		public static CompoundDocument Load(Stream stream)
		{
			CompoundDocument compoundDocument = Open(stream);
			compoundDocument.ReadAllStreamData();
			compoundDocument.Close();
			return compoundDocument;
		}

		private static FileHeader ReadHeader(BinaryReader reader)
		{
			FileHeader fileHeader = new FileHeader();
			fileHeader.FileTypeIdentifier = reader.ReadBytes(8);
			fileHeader.FileIdentifier = new Guid(reader.ReadBytes(16));
			fileHeader.RevisionNumber = reader.ReadUInt16();
			fileHeader.VersionNumber = reader.ReadUInt16();
			fileHeader.ByteOrderMark = reader.ReadBytes(2);
			fileHeader.SectorSizeInPot = reader.ReadUInt16();
			fileHeader.ShortSectorSizeInPot = reader.ReadUInt16();
			fileHeader.UnUsed10 = reader.ReadBytes(10);
			fileHeader.NumberOfSATSectors = reader.ReadInt32();
			fileHeader.FirstSectorIDofDirectoryStream = reader.ReadInt32();
			fileHeader.UnUsed4 = reader.ReadBytes(4);
			fileHeader.MinimumStreamSize = reader.ReadInt32();
			fileHeader.FirstSectorIDofShortSectorAllocationTable = reader.ReadInt32();
			fileHeader.NumberOfShortSectors = reader.ReadInt32();
			fileHeader.FirstSectorIDofMasterSectorAllocationTable = reader.ReadInt32();
			fileHeader.NumberOfMasterSectors = reader.ReadInt32();
			fileHeader.MasterSectorAllocationTable = ReadArrayOfInt32(reader, 109);
			return fileHeader;
		}

		private static DirectoryEntry ReadDirectoryEntry(BinaryReader reader)
		{
			DirectoryEntry directoryEntry = new DirectoryEntry();
			directoryEntry.NameBuffer = reader.ReadChars(32);
			directoryEntry.NameDataSize = reader.ReadUInt16();
			directoryEntry.EntryType = reader.ReadByte();
			directoryEntry.NodeColor = reader.ReadByte();
			directoryEntry.LeftChildDID = reader.ReadInt32();
			directoryEntry.RightChildDID = reader.ReadInt32();
			directoryEntry.MembersTreeNodeDID = reader.ReadInt32();
			directoryEntry.UniqueIdentifier = new Guid(reader.ReadBytes(16));
			directoryEntry.UserFlags = reader.ReadInt32();
			directoryEntry.CreationTime = DateTime.FromFileTime(reader.ReadInt64());
			directoryEntry.LastModificationTime = DateTime.FromFileTime(reader.ReadInt64());
			directoryEntry.FirstSectorID = reader.ReadInt32();
			directoryEntry.StreamLength = reader.ReadInt32();
			directoryEntry.UnUsed = reader.ReadInt32();
			return directoryEntry;
		}

		private static bool ArrayEqual(byte[] bytes1, byte[] bytes2)
		{
			if (bytes1.Length != bytes2.Length)
			{
				return false;
			}
			for (int i = 0; i < bytes1.Length; i++)
			{
				if (bytes1[i] != bytes2[i])
				{
					return false;
				}
			}
			return true;
		}

		private void ReadDirectoryEntries()
		{
			DirectoryStream = new MemoryStream(GetStreamDataAsBytes(Header.FirstSectorIDofDirectoryStream));
			BinaryReader reader = new BinaryReader(DirectoryStream, Encoding.Unicode);
			DirectoryEntries = new Dictionary<int, DirectoryEntry>();
			DirectoryEntry directoryEntry = ReadDirectoryEntry(reader);
			directoryEntry.Document = this;
			directoryEntry.ID = 0;
			DirectoryEntries.Add(0, directoryEntry);
			ShortStreamContainer = new MemoryStream(GetStreamDataAsBytes(directoryEntry.FirstSectorID, directoryEntry.StreamLength));
			ReadDirectoryEntry(reader, directoryEntry.MembersTreeNodeDID, directoryEntry);
		}

		private void ReadDirectoryEntry(BinaryReader reader, int DID, DirectoryEntry parent)
		{
			if (DID != -1 && !DirectoryEntries.ContainsKey(DID))
			{
				reader.BaseStream.Position = DID * 128;
				DirectoryEntry directoryEntry = ReadDirectoryEntry(reader);
				directoryEntry.Document = this;
				directoryEntry.ID = DID;
				DirectoryEntries[DID] = directoryEntry;
				parent.AddChild(directoryEntry);
				ReadDirectoryEntry(reader, directoryEntry.LeftChildDID, parent);
				ReadDirectoryEntry(reader, directoryEntry.RightChildDID, parent);
				ReadDirectoryEntry(reader, directoryEntry.MembersTreeNodeDID, directoryEntry);
			}
		}

		private void ReadAllStreamData()
		{
			foreach (DirectoryEntry value in DirectoryEntries.Values)
			{
				value.Data = GetStreamData(value);
			}
		}

		private int GetSectorOffset(int SID)
		{
			return 512 + SectorSize * SID;
		}

		private int GetShortSectorOffset(int SSID)
		{
			return ShortSectorSize * SSID;
		}

		internal int[] ReadSectorDataAsIntegers(int SID)
		{
			int sectorOffset = GetSectorOffset(SID);
			Reader.BaseStream.Position = sectorOffset;
			return ReadArrayOfInt32(Reader, SectorSize / 4);
		}

		private byte[] ReadSectorDataAsBytes(int SID)
		{
			int sectorOffset = GetSectorOffset(SID);
			Reader.BaseStream.Position = sectorOffset;
			return Reader.ReadBytes(SectorSize);
		}

		private byte[] ReadShortSectorDataAsBytes(int SSID)
		{
			int shortSectorOffset = GetShortSectorOffset(SSID);
			ShortStreamContainer.Seek(shortSectorOffset, SeekOrigin.Begin);
			return StreamHelper.ReadBytes(ShortStreamContainer, ShortSectorSize);
		}

		private byte[] GetStreamDataAsBytes(int StartSID)
		{
			List<int> sIDChain = SectorAllocation.GetSIDChain(StartSID);
			List<byte> list = new List<byte>();
			foreach (int item in sIDChain)
			{
				list.AddRange(ReadSectorDataAsBytes(item));
			}
			return list.ToArray();
		}

		private byte[] GetStreamDataAsBytes(int StartSID, int length)
		{
			List<int> sIDChain = SectorAllocation.GetSIDChain(StartSID);
			List<byte> list = new List<byte>();
			foreach (int item in sIDChain)
			{
				list.AddRange(ReadSectorDataAsBytes(item));
			}
			if (list.Count > length)
			{
				list.RemoveRange(length, list.Count - length);
			}
			return list.ToArray();
		}

		internal List<int> GetStreamDataAsIntegers(int StartSID)
		{
			List<int> sIDChain = SectorAllocation.GetSIDChain(StartSID);
			List<int> list = new List<int>();
			foreach (int item in sIDChain)
			{
				list.AddRange(ReadSectorDataAsIntegers(item));
			}
			return list;
		}

		private byte[] GetShortStreamDataAsBytes(int StartSSID)
		{
			List<int> sIDChain = ShortSectorAllocation.GetSIDChain(StartSSID);
			List<byte> list = new List<byte>();
			foreach (int item in sIDChain)
			{
				list.AddRange(ReadShortSectorDataAsBytes(item));
			}
			return list.ToArray();
		}

		private byte[] GetShortStreamDataAsBytes(int StartSSID, int length)
		{
			List<int> sIDChain = ShortSectorAllocation.GetSIDChain(StartSSID);
			List<byte> list = new List<byte>();
			foreach (int item in sIDChain)
			{
				list.AddRange(ReadShortSectorDataAsBytes(item));
			}
			if (list.Count > length)
			{
				list.RemoveRange(length, list.Count - length);
			}
			return list.ToArray();
		}

		public byte[] GetStreamData(DirectoryEntry entry)
		{
			if (entry.EntryType == 2)
			{
				if (entry.StreamLength < Header.MinimumStreamSize)
				{
					return GetShortStreamDataAsBytes(entry.FirstSectorID, entry.StreamLength);
				}
				return GetStreamDataAsBytes(entry.FirstSectorID, entry.StreamLength);
			}
			return null;
		}

		public DirectoryEntry FindDirectoryEntry(DirectoryEntry entry, string entryName)
		{
			if (entry.Members.ContainsKey(entryName))
			{
				return entry.Members[entryName];
			}
			using (Dictionary<string, DirectoryEntry>.ValueCollection.Enumerator enumerator = entry.Members.Values.GetEnumerator())
			{
				if (enumerator.MoveNext())
				{
					DirectoryEntry current = enumerator.Current;
					return FindDirectoryEntry(current, entryName);
				}
			}
			return null;
		}

		public byte[] GetStreamData(string streamName)
		{
			return FindDirectoryEntry(RootStorage, streamName)?.Data;
		}

		internal CompoundDocument(Stream stream, FileHeader header)
		{
			FileStorage = stream;
			Reader = new BinaryReader(FileStorage);
			if (stream.CanWrite)
			{
				Writer = new BinaryWriter(FileStorage, Encoding.Unicode);
			}
			Header = header;
			SectorSize = (int)Math.Pow(2.0, (int)Header.SectorSizeInPot);
			ShortSectorSize = (int)Math.Pow(2.0, (int)Header.ShortSectorSizeInPot);
			TotalSectors = ((stream.Length != 0) ? ((int)(stream.Length - 512) / SectorSize) : 0);
			MasterSectorAllocation = new MasterSectorAllocation(this);
			SectorAllocation = new SectorAllocation(this);
			ShortSectorAllocation = new ShortSectorAllocation(this);
		}

		public static CompoundDocument Create(string file)
		{
			FileStream stream = File.Open(file, FileMode.Create, FileAccess.ReadWrite, FileShare.Read);
			return Create(stream);
		}

		public static CompoundDocument Open(string file)
		{
			FileStream stream = File.Open(file, FileMode.Open, FileAccess.ReadWrite, FileShare.Read);
			return Open(stream);
		}

		public static CompoundDocument Create(Stream stream)
		{
			CompoundDocument compoundDocument = new CompoundDocument(stream, new CompoundFileHeader());
			compoundDocument.WriteHeader();
			compoundDocument.MasterSectorAllocation.AllocateSATSector();
			compoundDocument.InitializeDirectoryEntries();
			return compoundDocument;
		}

		public static CompoundDocument Open(Stream stream)
		{
			BinaryReader reader = new BinaryReader(stream);
			FileHeader header = ReadHeader(reader);
			CompoundDocument compoundDocument = new CompoundDocument(stream, header);
			if (!compoundDocument.CheckHeader())
			{
				return null;
			}
			compoundDocument.ReadDirectoryEntries();
			return compoundDocument;
		}

		public void Save()
		{
			SaveDirectoryEntries();
			SaveShortStreams();
			WriteHeader();
			Writer.Flush();
		}

		public void Close()
		{
			Dispose();
		}

		public void Dispose()
		{
			Dispose(disposing: true);
			GC.SuppressFinalize(this);
		}

		~CompoundDocument()
		{
			Dispose(disposing: false);
		}

		protected virtual void Dispose(bool disposing)
		{
			if (!disposed)
			{
				if (disposing && FileStorage != null)
				{
					FileStorage.Close();
					FileStorage = null;
				}
				disposed = true;
			}
		}

		private bool CheckHeader()
		{
			if (!ArrayEqual(Header.FileTypeIdentifier, CompoundFileHeader.FileTypeIdentifier))
			{
				throw new Exception("File header not recognized.");
			}
			if (!ArrayEqual(Header.ByteOrderMark, ByteOrderMarks.LittleEndian))
			{
				throw new Exception("Endian not implemented.");
			}
			return true;
		}

		private void InitializeDirectoryEntries()
		{
			Header.FirstSectorIDofDirectoryStream = AllocateDataSector();
			DirectoryEntries = new Dictionary<int, DirectoryEntry>();
			DirectoryEntry directoryEntry = new DirectoryEntry(this, "Root Entry");
			directoryEntry.EntryType = 5;
			directoryEntry.NodeColor = 1;
			directoryEntry.FirstSectorID = AllocateDataSector();
			directoryEntry.StreamLength = 0;
			DirectoryEntries.Add(0, directoryEntry);
			DirectoryStream = new MemoryStream();
			ShortStreamContainer = new MemoryStream();
		}

		private void SaveDirectoryEntries()
		{
			DirectoryTree.Build(RootStorage);
			DirectoryStream.Position = 0L;
			BinaryWriter writer = new BinaryWriter(DirectoryStream, Encoding.Unicode);
			for (int i = 0; i < DirectoryEntries.Count; i++)
			{
				WriteDirectoryEntry(writer, DirectoryEntries[i]);
			}
			WriteStreamData(Header.FirstSectorIDofDirectoryStream, DirectoryStream.ToArray());
		}

		private void SaveShortStreams()
		{
			ShortSectorAllocation.Save();
			WriteStreamData(RootStorage.FirstSectorID, ShortStreamContainer.ToArray());
		}

		internal int ReadInt32(long position)
		{
			FileStorage.Position = position;
			return Reader.ReadInt32();
		}

		internal int ReadInt32InSector(int secID, int position)
		{
			int sectorOffset = GetSectorOffset(secID);
			FileStorage.Position = sectorOffset + position;
			return Reader.ReadInt32();
		}

		internal void Write(long position, int integer)
		{
			FileStorage.Position = position;
			Writer.Write(integer);
		}

		internal void WriteInSector(int secID, int position, int integer)
		{
			int sectorOffset = GetSectorOffset(secID);
			FileStorage.Position = sectorOffset + position;
			Writer.Write(integer);
		}

		internal void WriteInSector(int secID, int position, int[] integers)
		{
			int sectorOffset = GetSectorOffset(secID);
			FileStorage.Position = sectorOffset + position;
			WriteArrayOfInt32(Writer, integers);
		}

		internal void WriteInSector(int secID, int position, byte[] data, int index, int count)
		{
			int sectorOffset = GetSectorOffset(secID);
			FileStorage.Position = sectorOffset + position;
			Writer.Write(data, index, count);
		}

		internal int AllocateNewSector()
		{
			int totalSectors = TotalSectors;
			FileStorage.Position = GetSectorOffset(totalSectors);
			Writer.Write(new byte[SectorSize]);
			TotalSectors++;
			return totalSectors;
		}

		internal int AllocateDataSector()
		{
			return SectorAllocation.AllocateSector();
		}

		internal int AllocateDataSectorAfter(int sectorID)
		{
			int num = SectorAllocation.AllocateSector();
			SectorAllocation.LinkSectorID(sectorID, num);
			return num;
		}

		internal int AllocateNewSector(int[] sectorData)
		{
			int totalSectors = TotalSectors;
			FileStorage.Position = GetSectorOffset(totalSectors);
			WriteArrayOfInt32(Writer, sectorData);
			TotalSectors++;
			return totalSectors;
		}

		internal int AllocateShortSector()
		{
			return ShortSectorAllocation.AllocateSector();
		}

		internal void AllocateNewShortSector()
		{
			ShortStreamContainer.Position = ShortStreamContainer.Length;
			byte[] array = new byte[ShortSectorSize];
			for (int i = 0; i < array.Length; i++)
			{
				array[i] = byte.MaxValue;
			}
			ShortStreamContainer.Write(array, 0, ShortSectorSize);
			RootStorage.StreamLength = (int)ShortStreamContainer.Length;
		}

		internal static int[] ReadArrayOfInt32(BinaryReader reader, int count)
		{
			int[] array = new int[count];
			for (int i = 0; i < array.Length; i++)
			{
				array[i] = reader.ReadInt32();
			}
			return array;
		}

		internal static void WriteArrayOfInt32(BinaryWriter writer, int[] data)
		{
			for (int i = 0; i < data.Length; i++)
			{
				writer.Write(data[i]);
			}
		}

		public static CompoundDocument CreateFromStream(Stream stream)
		{
			CompoundDocument compoundDocument = new CompoundDocument(stream, new CompoundFileHeader());
			compoundDocument.WriteHeader();
			compoundDocument.MasterSectorAllocation.AllocateSATSector();
			compoundDocument.InitializeDirectoryEntries();
			return compoundDocument;
		}
	}
}
