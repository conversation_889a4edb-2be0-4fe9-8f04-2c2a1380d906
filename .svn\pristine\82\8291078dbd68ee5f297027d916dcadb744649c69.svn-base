﻿using System;
using System.Linq;
using System.Threading;
using System.Windows.Forms;

namespace OCRTools.Common
{
    public sealed class MouseWheelMonitor : IDisposable
    {
        private readonly int _sensitivity;
        private readonly Control _ctrl;

        private bool _disposed;
        private volatile bool _inactive;
        private AutoResetEvent _resetMonitorEvent;
        private volatile bool _stopped;

        public MouseWheelMonitor(ImageBox canvas, int sensitivity)
        {
            _ctrl = canvas;
            canvas.ZoomChanged += RaiseMouseWheel;
            //canvas.Scroll += (s, e) => RaiseMouseWheel(s, e);

            _sensitivity = sensitivity;
            _resetMonitorEvent = new AutoResetEvent(false);

            _disposed = false;
            _inactive = true;
            _stopped = true;

            var monitor = new Thread(Monitor) {IsBackground = true};
            monitor.Start();
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
                DetachEventHandlers();
                if (_resetMonitorEvent != null)
                {
                    _resetMonitorEvent.Close();
                    _resetMonitorEvent = null;
                }
            }
        }

        public event EventHandler<EventArgs> MouseWheelStarted;
        public event EventHandler<EventArgs> MouseWheelStopped;

        private void Monitor()
        {
            while (!CommonString.IsExit && !_disposed)
            {
                if (_inactive) // if wheel is still inactive...
                {
                    _resetMonitorEvent.WaitOne(_sensitivity / 10); // ...wait negligibly small quantity of time...
                    continue; // ...and check again
                }

                // otherwise, if wheel is active...
                _inactive = true; // ...purposely change the state to inactive
                _resetMonitorEvent.WaitOne(_sensitivity); // wait...
                if (_inactive
                ) // ...and after specified time check if the state is still not re-activated inside mouse wheel event
                    RaiseMouseWheelStopped();
            }
        }

        private void RaiseMouseWheel(object sender, EventArgs e)
        {
            if (_stopped)
                RaiseMouseWheelStarted();
            _inactive = false;
        }

        private void RaiseMouseWheelStarted()
        {
            _stopped = false;
            MouseWheelStarted?.Invoke(_ctrl, new EventArgs());
        }

        private void RaiseMouseWheelStopped()
        {
            _stopped = true;
            MouseWheelStopped?.Invoke(_ctrl, new EventArgs());
        }

        private void DetachEventHandlers()
        {
            if (MouseWheelStarted != null)
                foreach (var handler in MouseWheelStarted.GetInvocationList().Cast<EventHandler<EventArgs>>())
                    MouseWheelStarted -= handler;
            if (MouseWheelStopped != null)
                foreach (var handler in MouseWheelStopped.GetInvocationList().Cast<EventHandler<EventArgs>>())
                    MouseWheelStopped -= handler;
        }
    }
}