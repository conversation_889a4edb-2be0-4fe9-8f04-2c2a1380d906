﻿#region License Information (GPL v3)

/*
    ShareX - A program that allows you to take screenshots and share any file type
    Copyright (c) 2007-2020 ShareX Team

    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License
    as published by the Free Software Foundation; either version 2
    of the License, or (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.

    Optionally you can also view the license at <http://www.gnu.org/licenses/>.
*/

#endregion License Information (GPL v3)

using MetroFramework.Forms;
using OCRTools.Common;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class ColorPickerForm : MetroForm
    {
        private readonly ControlHider _clipboardStatusHider;
        private bool _controlChangingColor;

        private bool _oldColorExist;

        public ColorPickerForm(Color currentColor, bool checkClipboard = true)
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            _clipboardStatusHider = new ControlHider(btnClipboardStatus, 2000);

            PrepareColorPalette();
            SetCurrentColor(currentColor, true);

            if (checkClipboard) CheckClipboard();

            btnOK.Visible = btnCancel.Visible = true;
            mbCopy.Visible = btnClose.Visible = true;
        }

        public Func<PointInfo> OpenScreenColorPicker;

        public void EnableScreenColorPickerButton(Func<PointInfo> openScreenColorPicker)
        {
            OpenScreenColorPicker = openScreenColorPicker;
            //btnScreenColorPicker.Visible = true;
        }

        public MyColor NewColor { get; private set; }
        public MyColor OldColor { get; private set; }

        public bool CheckClipboard()
        {
            var text = ClipboardService.GetText();

            if (!string.IsNullOrEmpty(text))
            {
                text = text.Trim();

                if (ColorHelper.ParseColor(text, out var clipboardColor))
                {
                    colorPicker.ChangeColor(clipboardColor);
                    btnClipboardStatus.Text = "Clipboard: " + text;
                    btnClipboardStatus.Location = new Point(
                        btnClipboardColorPicker.Left + btnClipboardColorPicker.Width / 2 - btnClipboardStatus.Width / 2,
                        btnClipboardColorPicker.Top - btnClipboardStatus.Height - 5);
                    _clipboardStatusHider.Show();
                    return true;
                }
            }

            return false;
        }

        //public static bool PickColor(Color currentColor, out Color newColor, Form owner = null,
        //    Func<PointInfo> openScreenColorPicker = null)
        //{
        //    using (var dialog = new ColorPickerForm(currentColor))
        //    {
        //        if (dialog.ShowDialog(owner) == DialogResult.OK)
        //        {
        //            newColor = dialog.NewColor;
        //            return true;
        //        }
        //    }

        //    newColor = currentColor;
        //    return false;
        //}

        private void PrepareColorPalette()
        {
            flpColorPalette.Controls.Clear();

            var colors = rbRecentColors.Checked ? CommonString.RecentColors.ToArray() : ColorHelper.StandardColors;

            var length = Math.Min(colors.Length, CommonString.RecentColorsMax);

            var previousColor = Color.Empty;

            for (var i = 0; i < length; i++)
            {
                var colorButton = new ColorButton
                {
                    Color = colors[i],
                    Size = new Size(16, 16),
                    Margin = new Padding(1),
                    BorderColor = Color.FromArgb(100, 100, 100),
                    Offset = 0,
                    HoverEffect = true,
                    ManualButtonClick = true
                };

                colorButton.MouseClick += (sender, e) =>
                {
                    if (e.Button == MouseButtons.Left)
                    {
                        SetCurrentColor(colorButton.Color, true);

                        if (!previousColor.IsEmpty && previousColor == colorButton.Color)
                            CloseOk();
                        else
                            previousColor = colorButton.Color;
                    }
                };

                flpColorPalette.Controls.Add(colorButton);
                if ((i + 1) % 16 == 0) flpColorPalette.SetFlowBreak(colorButton, true);
            }
        }

        private void AddRecentColor(Color color)
        {
            CommonString.RecentColors.Remove(color);

            if (CommonString.RecentColors.Count >= CommonString.RecentColorsMax)
                CommonString.RecentColors.RemoveRange(CommonString.RecentColorsMax - 1,
                    CommonString.RecentColors.Count - CommonString.RecentColorsMax + 1);

            CommonString.RecentColors.Insert(0, color);
        }

        public void SetCurrentColor(Color currentColor, bool keepPreviousColor)
        {
            _oldColorExist = keepPreviousColor;
            lblOld.Visible = _oldColorExist;
            NewColor = OldColor = currentColor;
            colorPicker.ChangeColor(currentColor);
            nudAlpha.SetValue(currentColor.A);
            DrawPreviewColors();
        }

        private void UpdateControls(MyColor color, ColorType type)
        {
            DrawPreviewColors();
            _controlChangingColor = true;

            if (type != ColorType.HSB)
            {
                nudHue.SetValue((decimal)Math.Round(color.HSB.Hue360));
                nudSaturation.SetValue((decimal)Math.Round(color.HSB.Saturation100));
                nudBrightness.SetValue((decimal)Math.Round(color.HSB.Brightness100));
            }

            if (type != ColorType.RGBA)
            {
                nudRed.SetValue(color.RGBA.Red);
                nudGreen.SetValue(color.RGBA.Green);
                nudBlue.SetValue(color.RGBA.Blue);
                nudAlpha.SetValue(color.RGBA.Alpha);
            }

            if (type != ColorType.CMYK)
            {
                nudCyan.SetValue((decimal)color.CMYK.Cyan100);
                nudMagenta.SetValue((decimal)color.CMYK.Magenta100);
                nudYellow.SetValue((decimal)color.CMYK.Yellow100);
                nudKey.SetValue((decimal)color.CMYK.Key100);
            }

            if (type != ColorType.Hex) txtHex.Text = ColorHelper.ColorToHex(color);

            if (type != ColorType.Decimal) txtDecimal.Text = ColorHelper.ColorToDecimal(color).ToString();

            lblNameValue.Text = ColorHelper.GetColorName(color);

            _controlChangingColor = false;
        }

        private void DrawPreviewColors()
        {
            var bmp = new Bitmap(pbColorPreview.ClientSize.Width, pbColorPreview.ClientSize.Height);

            using (var g = Graphics.FromImage(bmp))
            {
                var bmpHeight = bmp.Height;

                if (_oldColorExist)
                {
                    bmpHeight /= 2;

                    using (var oldColorBrush = new SolidBrush(OldColor))
                    {
                        g.FillRectangle(oldColorBrush, new Rectangle(0, bmpHeight, bmp.Width, bmpHeight));
                    }
                }

                using (var newColorBrush = new SolidBrush(NewColor))
                {
                    g.FillRectangle(newColorBrush, new Rectangle(0, 0, bmp.Width, bmpHeight));
                }
            }

            using (bmp)
            {
                pbColorPreview.LoadImage(bmp);
            }
        }

        private void CloseOk()
        {
            AddRecentColor(NewColor);
            DialogResult = DialogResult.OK;
            Close();
        }

        #region Events

        private void ColorPickerForm_Shown(object sender, EventArgs e)
        {
            this.ForceActivate();
        }

        private void colorPicker_ColorChanged(object sender, ColorEventArgs e)
        {
            NewColor = e.Color;
            UpdateControls(NewColor, e.ColorType);
        }

        private void rbRecentColors_CheckedChanged(object sender, EventArgs e)
        {
            PrepareColorPalette();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            CloseOk();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void rbHue_CheckedChanged(object sender, EventArgs e)
        {
            if (rbHue.Checked) colorPicker.DrawStyle = DrawStyle.Hue;
        }

        private void rbSaturation_CheckedChanged(object sender, EventArgs e)
        {
            if (rbSaturation.Checked) colorPicker.DrawStyle = DrawStyle.Saturation;
        }

        private void rbBrightness_CheckedChanged(object sender, EventArgs e)
        {
            if (rbBrightness.Checked) colorPicker.DrawStyle = DrawStyle.Brightness;
        }

        private void rbRed_CheckedChanged(object sender, EventArgs e)
        {
            if (rbRed.Checked) colorPicker.DrawStyle = DrawStyle.Red;
        }

        private void rbGreen_CheckedChanged(object sender, EventArgs e)
        {
            if (rbGreen.Checked) colorPicker.DrawStyle = DrawStyle.Green;
        }

        private void rbBlue_CheckedChanged(object sender, EventArgs e)
        {
            if (rbBlue.Checked) colorPicker.DrawStyle = DrawStyle.Blue;
        }

        private void RGB_ValueChanged(object sender, EventArgs e)
        {
            if (!_controlChangingColor)
                colorPicker.ChangeColor(
                    Color.FromArgb((int)nudAlpha.Value, (int)nudRed.Value, (int)nudGreen.Value, (int)nudBlue.Value),
                    ColorType.RGBA);
        }

        private void cbTransparent_Click(object sender, EventArgs e)
        {
            nudAlpha.Value = nudAlpha.Value == 0 ? 255 : 0;
        }

        private void HSB_ValueChanged(object sender, EventArgs e)
        {
            if (!_controlChangingColor)
                colorPicker.ChangeColor(
                    new HSB((int)nudHue.Value, (int)nudSaturation.Value, (int)nudBrightness.Value,
                        (int)nudAlpha.Value).ToColor(), ColorType.HSB);
        }

        private void CMYK_ValueChanged(object sender, EventArgs e)
        {
            if (!_controlChangingColor)
                colorPicker.ChangeColor(new Cmyk((double)nudCyan.Value / 100, (double)nudMagenta.Value / 100,
                    (double)nudYellow.Value / 100,
                    (double)nudKey.Value / 100, (int)nudAlpha.Value).ToColor(), ColorType.CMYK);
        }

        private void txtHex_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (!_controlChangingColor) colorPicker.ChangeColor(ColorHelper.HexToColor(txtHex.Text), ColorType.Hex);
            }
            catch
            {
                // ignored
            }
        }

        private void txtDecimal_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (!_controlChangingColor && int.TryParse(txtDecimal.Text, out var dec))
                    colorPicker.ChangeColor(ColorHelper.DecimalToColor(dec), ColorType.Decimal);
            }
            catch
            {
            }
        }

        private void pbColorPreview_MouseClick(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left && _oldColorExist) colorPicker.ChangeColor(OldColor);
        }

        private void tsmiCopyAll_Click(object sender, EventArgs e)
        {
            var colors = colorPicker.SelectedColor.ToString();
            SetText(colors);
        }

        private void tsmiCopyRGB_Click(object sender, EventArgs e)
        {
            var rgba = colorPicker.SelectedColor.RGBA;
            SetText($"{rgba.Red}, {rgba.Green}, {rgba.Blue}");
        }

        private void tsmiCopyHexadecimal_Click(object sender, EventArgs e)
        {
            var hex = ColorHelper.ColorToHex(colorPicker.SelectedColor);
            SetText("#" + hex);
        }

        private void tsmiCopyCMYK_Click(object sender, EventArgs e)
        {
            var cmyk = colorPicker.SelectedColor.CMYK;
            var text = $"{cmyk.Cyan100:0.0}%, {cmyk.Magenta100:0.0}%, {cmyk.Yellow100:0.0}%, {cmyk.Key100:0.0}%";
            SetText(text);
        }

        private void tsmiCopyHSB_Click(object sender, EventArgs e)
        {
            var hsb = colorPicker.SelectedColor.HSB;
            SetText($"{hsb.Hue360:0.0}°, {hsb.Saturation100:0.0}%, {hsb.Brightness100:0.0}%");
        }

        private void tsmiCopyDecimal_Click(object sender, EventArgs e)
        {
            var dec = ColorHelper.ColorToDecimal(colorPicker.SelectedColor);
            SetText(dec.ToString());
        }

        private void SetText(string text)
        {
            ClipboardService.SetText(text);
            CommonMethod.ShowNotificationTip("已复制到粘贴板：" + Environment.NewLine + text);
        }

        private void btnClipboardColorPicker_Click(object sender, EventArgs e)
        {
            CheckClipboard();
        }

        #endregion Events
    }

    public enum DrawStyle
    {
        Hue,
        Saturation,
        Brightness,
        Red,
        Green,
        Blue
    }

    public class MenuButton : Button
    {
        [DefaultValue(null)] public ContextMenuStrip Menu { get; set; }

        [DefaultValue(false)] public bool ShowMenuUnderCursor { get; set; }

        protected override void OnMouseDown(MouseEventArgs mevent)
        {
            base.OnMouseDown(mevent);

            if (Menu != null && mevent.Button == MouseButtons.Left)
            {
                var menuLocation = ShowMenuUnderCursor ? mevent.Location : new Point(0, Height);

                Menu.Show(this, menuLocation);
            }
        }

        protected override void OnPaint(PaintEventArgs pevent)
        {
            base.OnPaint(pevent);

            if (Menu != null)
            {
                var arrowX = ClientRectangle.Width - 14;
                var arrowY = ClientRectangle.Height / 2 - 1;

                var color = Enabled ? ForeColor : SystemColors.ControlDark;
                using (Brush brush = new SolidBrush(color))
                {
                    Point[] arrows =
                        {new Point(arrowX, arrowY), new Point(arrowX + 7, arrowY), new Point(arrowX + 3, arrowY + 4)};
                    pevent.Graphics.FillPolygon(brush, arrows);
                }
            }
        }
    }

    [DefaultEvent("ColorChanged")]
    public class ColorButton : Button
    {
        public delegate void ColorChangedEventHandler(Color color);

        private Color _color;

        private bool _isMouseHover;

        public Color Color
        {
            get => _color;
            set
            {
                _color = value;

                OnColorChanged(_color);

                Invalidate();
            }
        }

        [DefaultValue(typeof(Color), "DarkGray")]
        public Color BorderColor { get; set; } = Color.DarkGray;

        [DefaultValue(3)] public int Offset { get; set; } = 3;

        [DefaultValue(false)] public bool HoverEffect { get; set; }

        [DefaultValue(false)] public bool ManualButtonClick { get; set; }

        public event ColorChangedEventHandler ColorChanged;

        protected void OnColorChanged(Color color)
        {
            ColorChanged?.Invoke(color);
        }

        protected override void OnMouseClick(MouseEventArgs mevent)
        {
            base.OnMouseClick(mevent);

            //if (!ManualButtonClick) ShowColorDialog();
        }

        //public void ShowColorDialog()
        //{
        //    if (ColorPickerForm.PickColor(Color, out var newColor, FindForm())) Color = newColor;
        //}

        protected override void OnMouseEnter(EventArgs e)
        {
            _isMouseHover = true;

            base.OnMouseEnter(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            _isMouseHover = false;

            base.OnMouseLeave(e);
        }

        protected override void OnPaint(PaintEventArgs pevent)
        {
            base.OnPaint(pevent);

            var boxSize = ClientRectangle.Height - Offset * 2;
            var boxRectangle = new Rectangle(ClientRectangle.Width - Offset - boxSize, Offset, boxSize, boxSize);

            var g = pevent.Graphics;

            if (Color.A < 255)
                using (Image checker = ImageProcessHelper.CreateCheckerPattern(boxSize, boxSize))
                {
                    g.DrawImage(checker, boxRectangle);
                }

            if (Color.A > 0)
                using (Brush brush = new SolidBrush(Color))
                {
                    g.FillRectangle(brush, boxRectangle);
                }

            if (HoverEffect && _isMouseHover)
                using (Brush hoverBrush = new SolidBrush(Color.FromArgb(100, 255, 255, 255)))
                {
                    g.FillRectangle(hoverBrush, boxRectangle);
                }

            using (var borderPen = new Pen(BorderColor))
            {
                g.DrawRectangleProper(borderPen, boxRectangle);
            }
        }
    }
}