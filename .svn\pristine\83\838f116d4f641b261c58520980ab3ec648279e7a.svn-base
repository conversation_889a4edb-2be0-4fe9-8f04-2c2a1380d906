﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using OCRTools.Common;

namespace OCRTools
{
    public class PanelPictureView : ImageBox
    {
        public PanelPictureView()
        {
            Margin = Padding.Empty;
            AutoScroll = true;
            TabStop = false;
            AutoCenter = false;

            var monitor = new MouseWheelMonitor(this, 200);
            monitor.MouseWheelStopped += Monitor_MouseWheelStopped;
            monitor.MouseWheelStarted += Monitor_MouseWheelStarted;
        }

        private void Monitor_MouseWheelStarted(object sender, EventArgs e)
        {
            HideChild(this, true);
            Invalidate();
        }

        private void Monitor_MouseWheelStopped(object sender, EventArgs e)
        {
            InitChildZoom(this, true, Zoom * 1.0 / 100);
            Invalidate();
        }

        public void Clear()
        {
            try
            {
                foreach (Control item in Controls) item.Dispose();
            }
            catch
            {
            }

            try
            {
                Controls.Clear();
            }
            catch
            {
            }

            Image = null;
            Zoom = 100;
        }

        public void BindPicTxt(UcContent content, bool isShowTxt = false)
        {
            Image = content.Image;
            Size = content.Image.Size;
            //SendToBack();

            var lstCells =
                CommonString.JavaScriptSerializer.Deserialize<List<TextCellInfo>>(
                    content.OcrContent.result.verticalText);

            foreach (var item in lstCells)
            {
                if (string.IsNullOrEmpty(item.words) && string.IsNullOrEmpty(item.trans)) continue;
                var itemTxt = content.GetTextByContent(item);
                var baseSize = new Size((int)item.location.width, (int)item.location.height);

                var lbl = new TransParentLabel
                {
                    Location = new Point((int)Math.Floor(item.location.left), (int)Math.Floor(item.location.top)),
                    Font = CommonSetting.默认文字字体,
                    ForeColor = CommonSetting.默认文字颜色,
                    ContentBackColor = CommonSetting.默认背景颜色,
                    TabStop = false,
                    Size = new Size(baseSize.Width, baseSize.Height + 2),
                    Text = itemTxt,
                    TabIndex = lstCells.IndexOf(item) + 1,
                    IsShowText = isShowTxt
                }; // new Label() { BorderStyle = BorderStyle.Fixed3D, AutoSize = false, BackColor = Color.Transparent };

                ////lbl.Font = GetFontByGraphicsMeasure(gh, lbl.Text, BaseFont, ref baseSize);
                ////lbl.Font = GetFontByTextRendererMeasure(gh, lbl.Text, BaseFont, ref baseSize);

                lbl.OrgLocation = lbl.Location;
                lbl.OriSize = lbl.Size;
                CommonMethod.ShowTxtToolTip(content, lbl);
                //lbl.MouseMove += (object sender, MouseEventArgs e) =>
                //{
                //    tip.ToolTipTitle = itemTxt;
                //    //if (!tip.Active)
                //    tip.Show(" ", this, new Point(lbl.Location.X, lbl.Location.Y + lbl.Height));
                //};
                //lbl.MouseLeave += (object sender, EventArgs e) =>
                //{
                //    tip.Hide(lbl);
                //};

                Controls.Add(lbl);

                lbl.BringToFront();
            }

            lstCells.Clear();
            CommonMethod.EnableDoubleBuffering(this);
        }

        //private Font FindFont(Graphics g, string measuredString, Font BaseFont, Size baseSize)
        //{
        //    SizeF nowFontSize = g.MeasureString(measuredString, BaseFont, CommonString.PointZero, CommonString.BaseStringFormat);
        //    float HeightScaleRatio = baseSize.Height / nowFontSize.Height;
        //    float WidthScaleRatio = baseSize.Width / nowFontSize.Width;
        //    float ScaleRatio = (HeightScaleRatio < WidthScaleRatio) ? HeightScaleRatio : WidthScaleRatio;
        //    float ScaleFontSize = BaseFont.Size * ScaleRatio;
        //    return new Font(BaseFont.FontFamily, ScaleFontSize, BaseFont.Style, GraphicsUnit.Pixel);
        //}

        private void InitChildZoom(Control item, bool isSkip, double zoom)
        {
            if (!isSkip)
                if (item is TransParentLabel ctrl)
                {
                    ctrl.Location = new Point((int)(AutoScrollPosition.X + ctrl.OrgLocation.X * zoom),
                        (int)(AutoScrollPosition.Y + ctrl.OrgLocation.Y * zoom));
                    ctrl.Size = new Size((int)(ctrl.OriSize.Width * zoom), (int)(ctrl.OriSize.Height * zoom));
                    if (!ctrl.Visible) ctrl.Visible = true;
                }
            //else
            //{
            //    item.Location = new Point((int)(item.Location.X * zoom), (int)(item.Location.Y * zoom));
            //    item.Size = new Size((int)(item.Size.Width * zoom), (int)(item.Size.Height * zoom));
            //}

            if (item.Controls.Count > 0)
                foreach (Control child in item.Controls)
                    InitChildZoom(child, false, zoom);
        }

        private void HideChild(Control item, bool isSkip)
        {
            if (!isSkip)
                if (item is TransParentLabel)
                    item.Hide();
            if (item.Controls.Count > 0)
                foreach (Control child in item.Controls)
                    HideChild(child, false);
        }

        //private void PictureBox_MouseDoubleClick(object sender, MouseEventArgs e)
        //{
        //    var form = this.FindForm();
        //    if (form.WindowState == FormWindowState.Normal)
        //    {
        //        form.WindowState = FormWindowState.Maximized;
        //    }
        //    else
        //    {
        //        form.WindowState = FormWindowState.Normal;
        //    }
        //}

        //[DefaultValue(false), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden), Browsable(false)]
        //public bool IsPanning
        //{
        //    get => _isPanning;
        //    protected set
        //    {
        //        if (_isPanning != value)
        //        {
        //            _isPanning = value;
        //            _startScrollPosition = this.AutoScrollPosition;

        //            if (value)
        //            {
        //                this.Cursor = Cursors.SizeAll;
        //            }
        //            else
        //            {
        //                this.Cursor = Cursors.Default;
        //            }
        //        }
        //    }
        //}

        //public void BindPicTxt(UCContent content)
        //{
        //    var pictureBox = new UCPictureBox
        //    {
        //        Margin = CommonString.PaddingZero,
        //        Location = new Point(0, 0),
        //        SizeMode = PictureBoxSizeMode.StretchImage,
        //        Image = content.Image,
        //        Size = content.Image.Size,
        //        TabStop = false,
        //    };
        //    pictureBox.MouseDoubleClick += PictureBox_MouseDoubleClick;
        //    pictureBox.MouseDown += PictureBox_MouseDown;
        //    pictureBox.MouseMove += PictureBox_MouseMove;
        //    pictureBox.MouseUp += PictureBox_MouseUp;
        //    pictureBox.OrgLocation = pictureBox.Location;
        //    pictureBox.OriSize = pictureBox.Size;
        //    //pictureBox.BackColor = Color.Transparent;
        //    this.Controls.Add(pictureBox);
        //    pictureBox.SendToBack();

        //    List<TextCellInfo> lstCells = CommonString.JavaScriptSerializer.Deserialize<List<TextCellInfo>>(content.OcrContent.result.verticalText);

        //    using (Graphics gh = CreateGraphics())
        //    {
        //        foreach (var item in lstCells)
        //        {
        //            if (string.IsNullOrEmpty(item.words) && string.IsNullOrEmpty(item.trans))
        //            {
        //                continue;
        //            }
        //            var itemTxt = content.GetTextByContent(item);
        //            var baseSize = new Size((int)item.location.width, (int)item.location.height);

        //            var lbl = new TransParentLabel
        //            {
        //                Location = new Point((int)Math.Floor(item.location.left), (int)Math.Floor(item.location.top)),
        //                Font = content.ContentFont,
        //                TabStop = false,
        //                Size = new Size(baseSize.Width, baseSize.Height),
        //                Tag = itemTxt,
        //                TabIndex = lstCells.IndexOf(item) + 1,
        //            };// new Label() { BorderStyle = BorderStyle.Fixed3D, AutoSize = false, BackColor = Color.Transparent };

        //            //var BaseFont = new Font(lbl.Font.FontFamily, 5, FontStyle.Regular, GraphicsUnit.Pixel);

        //            //lbl.Font = FindFont(gh, measuredString, BaseFont, baseSize);
        //            ////lbl.Font = GetFontByGraphicsMeasure(gh, lbl.Text, BaseFont, ref baseSize);
        //            ////lbl.Font = GetFontByTextRendererMeasure(gh, lbl.Text, BaseFont, ref baseSize);

        //            lbl.OrgLocation = lbl.Location;
        //            lbl.OriSize = lbl.Size;
        //            CommonMethod.ShowTxtToolTip(content, lbl);
        //            //lbl.MouseMove += (object sender, MouseEventArgs e) =>
        //            //{
        //            //    tip.ToolTipTitle = itemTxt;
        //            //    //if (!tip.Active)
        //            //    tip.Show(" ", this, new Point(lbl.Location.X, lbl.Location.Y + lbl.Height));
        //            //};
        //            //lbl.MouseLeave += (object sender, EventArgs e) =>
        //            //{
        //            //    tip.Hide(lbl);
        //            //};

        //            this.Controls.Add(lbl);

        //            lbl.Parent = pictureBox;
        //            lbl.BringToFront();
        //        }
        //    }
        //    lstCells.Clear();
        //    CommonMethod.EnableDoubleBuffering(this);
        //}

        //private void PictureBox_MouseUp(object sender, MouseEventArgs e)
        //{
        //    OnMouseUp(e);
        //}

        //private void PictureBox_MouseDown(object sender, MouseEventArgs e)
        //{
        //    OnMouseDown(e);
        //}

        //private void PictureBox_MouseMove(object sender, MouseEventArgs e)
        //{
        //    var point = this.PointToClient(MousePosition);
        //    OnMouseMove(new MouseEventArgs(e.Button, e.Clicks, point.X, point.Y, e.Delta));
        //}

        //protected override void OnMouseDown(MouseEventArgs e)
        //{
        //    base.OnMouseDown(e);
        //    if (!Focused)
        //        Focus();
        //}

        //protected override void OnMouseUp(MouseEventArgs e)
        //{
        //    base.OnMouseUp(e);

        //    if (IsPanning)
        //        IsPanning = false;
        //}

        //protected override void OnMouseMove(MouseEventArgs e)
        //{
        //    base.OnMouseMove(e);

        //    if (e.Button == MouseButtons.Left)
        //    {
        //        if (!IsPanning)
        //        {
        //            _startMousePosition = e.Location;
        //            IsPanning = true;
        //        }

        //        if (IsPanning)
        //        {
        //            var x = -_startScrollPosition.X + (_startMousePosition.X - e.Location.X);
        //            var y = -_startScrollPosition.Y + (_startMousePosition.Y - e.Location.Y);
        //            var position = new Point(x, y);

        //            this.UpdateScrollPosition(position);
        //        }
        //    }
        //}

        //protected virtual void UpdateScrollPosition(Point position)
        //{
        //    this.AutoScrollPosition = position;
        //    this.Invalidate();
        //    this.OnScroll(new ScrollEventArgs(ScrollEventType.ThumbPosition, 0));
        //}
    }
}