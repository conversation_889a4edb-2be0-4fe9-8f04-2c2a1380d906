﻿namespace OCRTools
{
    partial class frmPasteImage
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.cmsMain = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.复制图像ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmCopyText = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmOCR = new System.Windows.Forms.ToolStripMenuItem();
            this.cmsSave = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.原始尺寸ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.阴影ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.cmsTop = new System.Windows.Forms.ToolStripMenuItem();
            this.cmsClose = new System.Windows.Forms.ToolStripMenuItem();
            this.cmsMain.SuspendLayout();
            this.SuspendLayout();
            // 
            // cmsMain
            // 
            this.cmsMain.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.复制图像ToolStripMenuItem,
            this.tsmCopyText,
            this.tsmOCR,
            this.cmsSave,
            this.toolStripSeparator1,
            this.原始尺寸ToolStripMenuItem,
            this.阴影ToolStripMenuItem,
            this.cmsTop,
            this.cmsClose});
            this.cmsMain.Name = "cmsMain";
            this.cmsMain.Size = new System.Drawing.Size(141, 186);
            // 
            // 复制图像ToolStripMenuItem
            // 
            this.复制图像ToolStripMenuItem.Image = global::OCRTools.Properties.Resources.复制;
            this.复制图像ToolStripMenuItem.Name = "复制图像ToolStripMenuItem";
            this.复制图像ToolStripMenuItem.Size = new System.Drawing.Size(140, 22);
            this.复制图像ToolStripMenuItem.Text = "复制图像";
            this.复制图像ToolStripMenuItem.Click += new System.EventHandler(this.复制图像ToolStripMenuItem_Click);
            // 
            // tsmCopyText
            // 
            this.tsmCopyText.Image = global::OCRTools.Properties.Resources.document_copy;
            this.tsmCopyText.Name = "tsmCopyText";
            this.tsmCopyText.Size = new System.Drawing.Size(140, 22);
            this.tsmCopyText.Text = "复制文本";
            // 
            // tsmOCR
            // 
            this.tsmOCR.Image = global::OCRTools.Properties.Resources.文本;
            this.tsmOCR.Name = "tsmOCR";
            this.tsmOCR.Size = new System.Drawing.Size(140, 22);
            this.tsmOCR.Text = "OCR识别";
            this.tsmOCR.Click += new System.EventHandler(this.cmsOCR_Click);
            // 
            // cmsSave
            // 
            this.cmsSave.Image = global::OCRTools.Properties.Resources.复制;
            this.cmsSave.Name = "cmsSave";
            this.cmsSave.Size = new System.Drawing.Size(140, 22);
            this.cmsSave.Text = "图像另存为";
            this.cmsSave.Click += new System.EventHandler(this.cmsSave_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(137, 6);
            // 
            // 原始尺寸ToolStripMenuItem
            // 
            this.原始尺寸ToolStripMenuItem.Image = global::OCRTools.Properties.Resources.原始;
            this.原始尺寸ToolStripMenuItem.Name = "原始尺寸ToolStripMenuItem";
            this.原始尺寸ToolStripMenuItem.Size = new System.Drawing.Size(140, 22);
            this.原始尺寸ToolStripMenuItem.Text = "原始尺寸";
            this.原始尺寸ToolStripMenuItem.Click += new System.EventHandler(this.原始尺寸ToolStripMenuItem_Click);
            // 
            // 阴影ToolStripMenuItem
            // 
            this.阴影ToolStripMenuItem.Checked = true;
            this.阴影ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.阴影ToolStripMenuItem.Name = "阴影ToolStripMenuItem";
            this.阴影ToolStripMenuItem.Size = new System.Drawing.Size(140, 22);
            this.阴影ToolStripMenuItem.Text = "窗口阴影";
            this.阴影ToolStripMenuItem.Click += new System.EventHandler(this.阴影ToolStripMenuItem_Click);
            // 
            // cmsTop
            // 
            this.cmsTop.Image = global::OCRTools.Properties.Resources.untop;
            this.cmsTop.Name = "cmsTop";
            this.cmsTop.Size = new System.Drawing.Size(140, 22);
            this.cmsTop.Text = "取消置顶";
            this.cmsTop.Click += new System.EventHandler(this.cmsTop_Click);
            // 
            // cmsClose
            // 
            this.cmsClose.Image = global::OCRTools.Properties.Resources.退出;
            this.cmsClose.Name = "cmsClose";
            this.cmsClose.Size = new System.Drawing.Size(140, 22);
            this.cmsClose.Text = "关闭窗体(&C)";
            this.cmsClose.Click += new System.EventHandler(this.cmsClose_Click);
            // 
            // frmPasteImage
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(110, 124);
            this.ContextMenuStrip = this.cmsMain;
            this.Name = "frmPasteImage";
            this.StartPosition = System.Windows.Forms.FormStartPosition.Manual;
            this.Text = "贴图";
            this.TopMost = true;
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frmPasteImage_KeyUp);
            this.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.frmPasteImage_MouseDoubleClick);
            this.cmsMain.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip cmsMain;
        private System.Windows.Forms.ToolStripMenuItem 复制图像ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem cmsSave;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem 阴影ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem cmsTop;
        private System.Windows.Forms.ToolStripMenuItem cmsClose;
        private System.Windows.Forms.ToolStripMenuItem tsmOCR;
        private System.Windows.Forms.ToolStripMenuItem 原始尺寸ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem tsmCopyText;
    }
}