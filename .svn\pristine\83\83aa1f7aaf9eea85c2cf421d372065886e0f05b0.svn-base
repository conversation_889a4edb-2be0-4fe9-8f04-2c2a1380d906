// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class StylesPattern : BasePattern
    {
        public static readonly AutomationProperty StyleIdProperty = StylesPatternIdentifiers.StyleIdProperty;
        public static readonly AutomationProperty StyleNameProperty = StylesPatternIdentifiers.StyleNameProperty;
        public static readonly AutomationProperty FillColorProperty = StylesPatternIdentifiers.FillColorProperty;

        public static readonly AutomationProperty FillPatternStyleProperty =
            StylesPatternIdentifiers.FillPatternStyleProperty;

        public static readonly AutomationProperty ShapeProperty = StylesPatternIdentifiers.ShapeProperty;

        public static readonly AutomationProperty FillPatternColorProperty =
            StylesPatternIdentifiers.FillPatternColorProperty;

        public static readonly AutomationProperty ExtendedPropertiesProperty =
            StylesPatternIdentifiers.ExtendedPropertiesProperty;

        public static readonly AutomationPattern Pattern = StylesPatternIdentifiers.Pattern;

        private IUIAutomationStylesPattern _pattern;


        private StylesPattern(AutomationElement el, IUIAutomationStylesPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
            _pattern = pattern;
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new StylesPattern(el, (IUIAutomationStylesPattern) pattern, cached);
        }
    }
}