﻿using OCRTools;
using System;
using System.Windows.Forms;

namespace ShareX.HelpersLib
{
    public partial class DoubleLabeledNumericUpDown : UserControl
    {
        public new string Text
        {
            get
            {
                return lblText.Text;
            }
            set
            {
                lblText.Text = value;
            }
        }

        public string Text2
        {
            get
            {
                return lblText2.Text;
            }
            set
            {
                lblText2.Text = value;
            }
        }

        public decimal Value
        {
            get
            {
                return nudValue.Value;
            }
            set
            {
                nudValue.Value = value.Clamp(Minimum, Maximum);
            }
        }

        public decimal Value2
        {
            get
            {
                return nudValue2.Value;
            }
            set
            {
                nudValue2.Value = value.Clamp(Minimum, Maximum);
            }
        }

        public decimal Maximum
        {
            get
            {
                return nudValue.Maximum;
            }
            set
            {
                nudValue.Maximum = nudValue2.Maximum = value;
            }
        }

        public decimal Minimum
        {
            get
            {
                return nudValue.Minimum;
            }
            set
            {
                nudValue.Minimum = nudValue2.Minimum = value;
            }
        }

        public decimal Increment
        {
            get
            {
                return nudValue.Increment;
            }
            set
            {
                nudValue.Increment = nudValue2.Increment = value;
            }
        }

        public EventHandler ValueChanged;

        public DoubleLabeledNumericUpDown()
        {
            InitializeComponent();
            nudValue.ValueChanged += OnValueChanged;
            nudValue2.ValueChanged += OnValueChanged;
        }

        private void OnValueChanged(object sender, EventArgs e)
        {
            ValueChanged?.Invoke(sender, e);
        }
    }
}