﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace OCRTools
{
    partial class ScrollingCaptureForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private IContainer components = null;

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ScrollingCaptureForm));
            this.captureTimer = new System.Windows.Forms.Timer(this.components);
            this.tcScrollingCapture = new System.Windows.Forms.TabControl();
            this.tpOutput = new System.Windows.Forms.TabPage();
            this.lnkSet = new System.Windows.Forms.LinkLabel();
            this.gbImages = new System.Windows.Forms.GroupBox();
            this.txtImagesCount = new System.Windows.Forms.TextBox();
            this.lblImageCount = new System.Windows.Forms.Label();
            this.nudIgnoreLast = new System.Windows.Forms.NumericUpDown();
            this.lblIgnoreLast = new System.Windows.Forms.Label();
            this.btnResetCombine = new System.Windows.Forms.Button();
            this.btnExport = new System.Windows.Forms.Button();
            this.btnGuessCombineAdjustments = new System.Windows.Forms.Button();
            this.btnGuessEdges = new System.Windows.Forms.Button();
            this.gbCombineAdjustments = new System.Windows.Forms.GroupBox();
            this.lblCombineLastVertical = new System.Windows.Forms.Label();
            this.lblCombineVertical = new System.Windows.Forms.Label();
            this.nudCombineVertical = new System.Windows.Forms.NumericUpDown();
            this.nudCombineLastVertical = new System.Windows.Forms.NumericUpDown();
            this.gbTrimEdges = new System.Windows.Forms.GroupBox();
            this.lblTrimBottom = new System.Windows.Forms.Label();
            this.lblTrimRight = new System.Windows.Forms.Label();
            this.lblTrimTop = new System.Windows.Forms.Label();
            this.lblTrimLeft = new System.Windows.Forms.Label();
            this.nudTrimLeft = new System.Windows.Forms.NumericUpDown();
            this.nudTrimBottom = new System.Windows.Forms.NumericUpDown();
            this.nudTrimTop = new System.Windows.Forms.NumericUpDown();
            this.nudTrimRight = new System.Windows.Forms.NumericUpDown();
            this.pOutput = new System.Windows.Forms.Panel();
            this.lblProcessing = new System.Windows.Forms.Label();
            this.pbOutput = new System.Windows.Forms.PictureBox();
            this.tcScrollingCapture.SuspendLayout();
            this.tpOutput.SuspendLayout();
            this.gbImages.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudIgnoreLast)).BeginInit();
            this.gbCombineAdjustments.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudCombineVertical)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudCombineLastVertical)).BeginInit();
            this.gbTrimEdges.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudTrimLeft)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudTrimBottom)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudTrimTop)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudTrimRight)).BeginInit();
            this.pOutput.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pbOutput)).BeginInit();
            this.SuspendLayout();
            // 
            // captureTimer
            // 
            this.captureTimer.Tick += new System.EventHandler(this.captureTimer_Tick);
            // 
            // tcScrollingCapture
            // 
            this.tcScrollingCapture.Controls.Add(this.tpOutput);
            resources.ApplyResources(this.tcScrollingCapture, "tcScrollingCapture");
            this.tcScrollingCapture.Name = "tcScrollingCapture";
            this.tcScrollingCapture.SelectedIndex = 0;
            // 
            // tpOutput
            // 
            this.tpOutput.BackColor = System.Drawing.Color.White;
            this.tpOutput.Controls.Add(this.lnkSet);
            this.tpOutput.Controls.Add(this.gbImages);
            this.tpOutput.Controls.Add(this.btnResetCombine);
            this.tpOutput.Controls.Add(this.btnExport);
            this.tpOutput.Controls.Add(this.btnGuessCombineAdjustments);
            this.tpOutput.Controls.Add(this.btnGuessEdges);
            this.tpOutput.Controls.Add(this.gbCombineAdjustments);
            this.tpOutput.Controls.Add(this.gbTrimEdges);
            this.tpOutput.Controls.Add(this.pOutput);
            resources.ApplyResources(this.tpOutput, "tpOutput");
            this.tpOutput.Name = "tpOutput";
            // 
            // lnkSet
            // 
            resources.ApplyResources(this.lnkSet, "lnkSet");
            this.lnkSet.Name = "lnkSet";
            this.lnkSet.TabStop = true;
            this.lnkSet.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lnkSet_LinkClicked);
            // 
            // gbImages
            // 
            this.gbImages.Controls.Add(this.txtImagesCount);
            this.gbImages.Controls.Add(this.lblImageCount);
            this.gbImages.Controls.Add(this.nudIgnoreLast);
            this.gbImages.Controls.Add(this.lblIgnoreLast);
            resources.ApplyResources(this.gbImages, "gbImages");
            this.gbImages.Name = "gbImages";
            this.gbImages.TabStop = false;
            // 
            // txtImagesCount
            // 
            resources.ApplyResources(this.txtImagesCount, "txtImagesCount");
            this.txtImagesCount.Name = "txtImagesCount";
            this.txtImagesCount.ReadOnly = true;
            // 
            // lblImageCount
            // 
            resources.ApplyResources(this.lblImageCount, "lblImageCount");
            this.lblImageCount.Name = "lblImageCount";
            // 
            // nudIgnoreLast
            // 
            resources.ApplyResources(this.nudIgnoreLast, "nudIgnoreLast");
            this.nudIgnoreLast.Maximum = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.nudIgnoreLast.Name = "nudIgnoreLast";
            this.nudIgnoreLast.ValueChanged += new System.EventHandler(this.nudIgnoreLast_ValueChanged);
            // 
            // lblIgnoreLast
            // 
            resources.ApplyResources(this.lblIgnoreLast, "lblIgnoreLast");
            this.lblIgnoreLast.Name = "lblIgnoreLast";
            // 
            // btnResetCombine
            // 
            resources.ApplyResources(this.btnResetCombine, "btnResetCombine");
            this.btnResetCombine.Name = "btnResetCombine";
            this.btnResetCombine.UseMnemonic = false;
            this.btnResetCombine.UseVisualStyleBackColor = true;
            this.btnResetCombine.AutoSize = true;
            this.btnResetCombine.Click += new System.EventHandler(this.btnResetCombine_Click);
            // 
            // btnExport
            // 
            resources.ApplyResources(this.btnExport, "btnExport");
            this.btnExport.Name = "btnExport";
            this.btnExport.UseMnemonic = false;
            this.btnExport.UseVisualStyleBackColor = true;
            this.btnExport.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // btnGuessCombineAdjustments
            // 
            resources.ApplyResources(this.btnGuessCombineAdjustments, "btnGuessCombineAdjustments");
            this.btnGuessCombineAdjustments.Name = "btnGuessCombineAdjustments";
            this.btnGuessCombineAdjustments.UseMnemonic = false;
            this.btnGuessCombineAdjustments.UseVisualStyleBackColor = true;
            this.btnGuessCombineAdjustments.AutoSize = true;
            this.btnGuessCombineAdjustments.Click += new System.EventHandler(this.btnGuessCombineAdjustments_Click);
            // 
            // btnGuessEdges
            // 
            resources.ApplyResources(this.btnGuessEdges, "btnGuessEdges");
            this.btnGuessEdges.Name = "btnGuessEdges";
            this.btnGuessEdges.AutoSize = true;
            this.btnGuessEdges.UseVisualStyleBackColor = true;
            this.btnGuessEdges.Click += new System.EventHandler(this.btnGuessEdges_Click);
            // 
            // gbCombineAdjustments
            // 
            this.gbCombineAdjustments.Controls.Add(this.lblCombineLastVertical);
            this.gbCombineAdjustments.Controls.Add(this.lblCombineVertical);
            this.gbCombineAdjustments.Controls.Add(this.nudCombineVertical);
            this.gbCombineAdjustments.Controls.Add(this.nudCombineLastVertical);
            resources.ApplyResources(this.gbCombineAdjustments, "gbCombineAdjustments");
            this.gbCombineAdjustments.Name = "gbCombineAdjustments";
            this.gbCombineAdjustments.TabStop = false;
            // 
            // lblCombineLastVertical
            // 
            resources.ApplyResources(this.lblCombineLastVertical, "lblCombineLastVertical");
            this.lblCombineLastVertical.Name = "lblCombineLastVertical";
            // 
            // lblCombineVertical
            // 
            resources.ApplyResources(this.lblCombineVertical, "lblCombineVertical");
            this.lblCombineVertical.Name = "lblCombineVertical";
            // 
            // nudCombineVertical
            // 
            resources.ApplyResources(this.nudCombineVertical, "nudCombineVertical");
            this.nudCombineVertical.Maximum = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.nudCombineVertical.Name = "nudCombineVertical";
            this.nudCombineVertical.ValueChanged += new System.EventHandler(this.nudCombineVertical_ValueChanged);
            // 
            // nudCombineLastVertical
            // 
            resources.ApplyResources(this.nudCombineLastVertical, "nudCombineLastVertical");
            this.nudCombineLastVertical.Maximum = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.nudCombineLastVertical.Name = "nudCombineLastVertical";
            this.nudCombineLastVertical.ValueChanged += new System.EventHandler(this.nudCombineLastVertical_ValueChanged);
            // 
            // gbTrimEdges
            // 
            this.gbTrimEdges.Controls.Add(this.lblTrimBottom);
            this.gbTrimEdges.Controls.Add(this.lblTrimRight);
            this.gbTrimEdges.Controls.Add(this.lblTrimTop);
            this.gbTrimEdges.Controls.Add(this.lblTrimLeft);
            this.gbTrimEdges.Controls.Add(this.nudTrimLeft);
            this.gbTrimEdges.Controls.Add(this.nudTrimBottom);
            this.gbTrimEdges.Controls.Add(this.nudTrimTop);
            this.gbTrimEdges.Controls.Add(this.nudTrimRight);
            resources.ApplyResources(this.gbTrimEdges, "gbTrimEdges");
            this.gbTrimEdges.Name = "gbTrimEdges";
            this.gbTrimEdges.TabStop = false;
            // 
            // lblTrimBottom
            // 
            resources.ApplyResources(this.lblTrimBottom, "lblTrimBottom");
            this.lblTrimBottom.Name = "lblTrimBottom";
            // 
            // lblTrimRight
            // 
            resources.ApplyResources(this.lblTrimRight, "lblTrimRight");
            this.lblTrimRight.Name = "lblTrimRight";
            // 
            // lblTrimTop
            // 
            resources.ApplyResources(this.lblTrimTop, "lblTrimTop");
            this.lblTrimTop.Name = "lblTrimTop";
            // 
            // lblTrimLeft
            // 
            resources.ApplyResources(this.lblTrimLeft, "lblTrimLeft");
            this.lblTrimLeft.Name = "lblTrimLeft";
            // 
            // nudTrimLeft
            // 
            resources.ApplyResources(this.nudTrimLeft, "nudTrimLeft");
            this.nudTrimLeft.Maximum = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.nudTrimLeft.Name = "nudTrimLeft";
            this.nudTrimLeft.ValueChanged += new System.EventHandler(this.nudTrimLeft_ValueChanged);
            // 
            // nudTrimBottom
            // 
            resources.ApplyResources(this.nudTrimBottom, "nudTrimBottom");
            this.nudTrimBottom.Maximum = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.nudTrimBottom.Name = "nudTrimBottom";
            this.nudTrimBottom.ValueChanged += new System.EventHandler(this.nudTrimBottom_ValueChanged);
            // 
            // nudTrimTop
            // 
            resources.ApplyResources(this.nudTrimTop, "nudTrimTop");
            this.nudTrimTop.Maximum = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.nudTrimTop.Name = "nudTrimTop";
            this.nudTrimTop.ValueChanged += new System.EventHandler(this.nudTrimTop_ValueChanged);
            // 
            // nudTrimRight
            // 
            resources.ApplyResources(this.nudTrimRight, "nudTrimRight");
            this.nudTrimRight.Maximum = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.nudTrimRight.Name = "nudTrimRight";
            this.nudTrimRight.ValueChanged += new System.EventHandler(this.nudTrimRight_ValueChanged);
            // 
            // pOutput
            // 
            resources.ApplyResources(this.pOutput, "pOutput");
            this.pOutput.Controls.Add(this.lblProcessing);
            this.pOutput.Controls.Add(this.pbOutput);
            this.pOutput.Name = "pOutput";
            // 
            // lblProcessing
            // 
            resources.ApplyResources(this.lblProcessing, "lblProcessing");
            this.lblProcessing.Name = "lblProcessing";
            // 
            // pbOutput
            // 
            resources.ApplyResources(this.pbOutput, "pbOutput");
            this.pbOutput.Name = "pbOutput";
            this.pbOutput.TabStop = false;
            // 
            // ScrollingCaptureForm
            // 
            resources.ApplyResources(this, "$this");
            this.Controls.Add(this.tcScrollingCapture);
            this.Name = "ScrollingCaptureForm";
            this.Load += new System.EventHandler(this.ScrollingCaptureForm_Load);
            this.tcScrollingCapture.ResumeLayout(false);
            this.tpOutput.ResumeLayout(false);
            this.tpOutput.PerformLayout();
            this.gbImages.ResumeLayout(false);
            this.gbImages.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudIgnoreLast)).EndInit();
            this.gbCombineAdjustments.ResumeLayout(false);
            this.gbCombineAdjustments.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudCombineVertical)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudCombineLastVertical)).EndInit();
            this.gbTrimEdges.ResumeLayout(false);
            this.gbTrimEdges.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudTrimLeft)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudTrimBottom)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudTrimTop)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudTrimRight)).EndInit();
            this.pOutput.ResumeLayout(false);
            this.pOutput.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pbOutput)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion
        private Timer captureTimer;
        private TabControl tcScrollingCapture;
        private TabPage tpOutput;
        private Panel pOutput;
        private PictureBox pbOutput;
        private GroupBox gbCombineAdjustments;
        private Label lblCombineVertical;
        private NumericUpDown nudCombineVertical;
        private NumericUpDown nudCombineLastVertical;
        private GroupBox gbTrimEdges;
        private Label lblTrimBottom;
        private Label lblTrimRight;
        private Label lblTrimTop;
        private Label lblTrimLeft;
        private NumericUpDown nudTrimLeft;
        private NumericUpDown nudTrimBottom;
        private NumericUpDown nudTrimTop;
        private NumericUpDown nudTrimRight;
        private Label lblCombineLastVertical;
        private Button btnGuessEdges;
        private Button btnGuessCombineAdjustments;
        private Button btnResetCombine;
        private Label lblProcessing;
        private Label lblImageCount;
        private NumericUpDown nudIgnoreLast;
        private Label lblIgnoreLast;
        private GroupBox gbImages;
        private TextBox txtImagesCount;
        private Button btnExport;
        private LinkLabel lnkSet;
    }
}