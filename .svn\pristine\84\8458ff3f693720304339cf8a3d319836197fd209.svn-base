﻿using OCRTools;
using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Linq;
using System.Reflection;

namespace ImageLib
{
    /// <summary>
    /// PnnLAB
    /// </summary>
    public class ALiYunUpload
    {
        public static bool Enable { get; set; } = true;

        private const string strFileNameSpilt = "origin_url\":\"";
        private const string strFileNameSpilt2 = "output_file\":\"";

        //https://jm189.cn/skin/js/oss.js
        private static AliYunToken GetToken1()
        {
            var html = WebClientExt.GetHtml("https://jm189.cn/user/lib/alisdk/oss/php/sign_bS.php?t=" + ServerTime.DateTime.Ticks);
            if (!string.IsNullOrEmpty(html) && html.Length > 2)
            {
                return html.DeserializeJson<AliYunToken>();
            }

            return null;
        }

        //https://www.hipdf.cn/image-compressor
        private static AliYunToken GetToken2()
        {
            var html = WebClientExt.GetHtml("https://www.hipdf.cn/middle/file/get-oss-policy?t=" + ServerTime.DateTime.Ticks);
            if (!string.IsNullOrEmpty(html) && html.Length > 2)
            {
                return html.DeserializeJson<AliYunTokenRoot>()?.data;
            }

            return null;
        }

        //https://www.hipdf.cn/image-compressor
        private static AliYunToken GetToken3()
        {
            var html = WebClientExt.GetHtml("https://web-api.hipdf.cn/middle/file/get-oss-policy?t=" + ServerTime.DateTime.Ticks);
            if (!string.IsNullOrEmpty(html) && html.Length > 2)
            {
                return html.DeserializeJson<AliYunTokenRoot>()?.data;
            }

            return null;
        }

        //金鸣OCR小程序
        private static AliYunToken GetToken4()
        {
            var html = WebClientExt.GetHtml("https://it.jm189.cn/user/lib/alisdk/oss/php/sign_bS.php?t=" + ServerTime.DateTime.Ticks);
            if (!string.IsNullOrEmpty(html) && html.Length > 2)
            {
                return html.DeserializeJson<AliYunToken>();
            }

            return null;
        }

        private static List<string> lstUploadType = new List<string>() { "1", "2", "3", "4" };
        private const int maxTimeOut = 10000;

        private static AliYunToken GetResultByType(TaskParam param)
        {
            AliYunToken token = null;
            switch (param.Param1)
            {
                case "1":
                    token = GetToken4();
                    break;
                case "2":
                    token = GetToken3();
                    break;
                case "3":
                    token = GetToken1();
                    break;
                case "4":
                    token = GetToken2();
                    break;
            }
            return token;
        }

        public static string GetResult(byte[] content)
        {
            var result = string.Empty;
            var lstParam = lstUploadType.Select(dns => new TaskParam() { Param1 = dns }).ToList();
            var token = CommonTask<AliYunToken>.GetFastestValidResult(lstParam, GetResultByType, 2, maxTimeOut, null);
            if (token == null || string.IsNullOrEmpty(token.accessid))
                return result;
            var file = new UploadFileInfo()
            {
                Name = "file",
                Filename = "1.png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            var filePath = token.dir + ServerTime.DateTime.ToDateStr("M-d") + "/" + Guid.NewGuid().ToString().ToLower().Replace("-", "") + ".png";
            var vaules = new NameValueCollection() {
                    { "ossAccessKeyId", token.accessid } ,
                    { "policy", token.policy } ,
                    { "signature", token.signature } ,
                    { "callback", token.callback } ,
                    { "key",filePath  }
                };
            try
            {
                var html = UploadFileRequest.Post(token.host, new[] { file
    }, vaules);
                if (html?.Contains(strFileNameSpilt) == true)
                {
                    result = CommonMethod.SubString(html, strFileNameSpilt, "\"").Replace("\\/", "/");
                }
                else if (html?.Contains(strFileNameSpilt2) == true)
                {
                    result = CommonMethod.SubString(html, strFileNameSpilt2, "\"").Replace("\\/", "/");
                }
                else if (html.Contains("\"Status\":\"Ok\""))
                {
                    result = filePath;
                }
                if (!string.IsNullOrEmpty(result))
                {
                    if (!result.StartsWith("http"))
                    {
                        result = token.host + "/" + result;
                    }
                }
            }
            catch { }
            return result;
        }

        [Obfuscation]
        public class AliYunTokenRoot
        {
            [Obfuscation]
            public AliYunToken data { get; set; }
        }

        [Obfuscation]
        public class AliYunToken
        {
            [Obfuscation]
            public string accessid { get; set; }

            [Obfuscation]
            public string host { get; set; }

            [Obfuscation]
            public string policy { get; set; }

            [Obfuscation]
            public string signature { get; set; }

            [Obfuscation]
            public string dir { get; set; }

            [Obfuscation]
            public string callback { get; set; }
        }
    }
}
