using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class AnnotationPattern : BasePattern
    {
        public static readonly AutomationProperty AnnotationTypeIdProperty =
            AnnotationPatternIdentifiers.AnnotationTypeIdProperty;

        public static readonly AutomationPattern Pattern = AnnotationPatternIdentifiers.Pattern;


        private AnnotationPattern(AutomationElement el, IUIAutomationAnnotationPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        public static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new AnnotationPattern(el, (IUIAutomationAnnotationPattern)pattern, cached);
        }
    }
}