﻿using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;

namespace OCRTools
{
    public static class IniHelper
    {
        private static readonly string IniFileName = CommonString.DataPath + "\\config.ini";

        [DllImport("kernel32")]
        private static extern int GetPrivateProfileString(string sectionName, string key, string defaultValue,
            byte[] returnBuffer, int size, string filePath);

        [DllImport("kernel32")]
        private static extern long WritePrivateProfileString(string sectionName, string key, string value,
            string filePath);

        public static string GetValue(string sectionName, string key, string defaultValue = null)
        {
            string result = null;
            try
            {
                if (!string.IsNullOrEmpty(key))
                {
                    InitIniFile(IniFileName);
                    var array = new byte[2048];
                    var privateProfileString =
                        GetPrivateProfileString(sectionName, key, string.Empty, array, 999, IniFileName);
                    result = Encoding.Default.GetString(array, 0, privateProfileString);
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine("读取配置文件出错！" + oe.Message);
            }

            if (string.IsNullOrEmpty(result) && !string.IsNullOrEmpty(defaultValue)) result = defaultValue;
            return result;
        }

        //public static List<string> GetListValue(string sectionName, string key, string strSpilt = ",")
        //{
        //    var lstTmp = new List<string>();
        //    var str = GetValue(sectionName, key);
        //    if (!string.IsNullOrEmpty(str) && !string.IsNullOrEmpty(strSpilt) && str.Contains(strSpilt))
        //        lstTmp.AddRange(str.Split(new[] {strSpilt}, StringSplitOptions.RemoveEmptyEntries));
        //    return lstTmp;
        //}

        public static bool SetValue(string sectionName, string key, string value)
        {
            InitIniFile(IniFileName);
            var result = (int) WritePrivateProfileString(sectionName, key, value, IniFileName) > 0;

            return result;
        }

        private static void InitIniFile(string fileName)
        {
            if (!File.Exists(fileName))
                using (File.Create(fileName))
                {
                }
        }
    }
}