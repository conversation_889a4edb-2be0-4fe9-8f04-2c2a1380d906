﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{CCCBF439-BBD2-41DC-A881-8CBD2E38587C}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>MetroFramework</RootNamespace>
    <AssemblyName>MetroFramework</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Animation\AnimationBase.cs" />
    <Compile Include="Animation\DelayedCall.cs" />
    <Compile Include="Animation\MoveAnimation.cs" />
    <Compile Include="Animation\TransitionType.cs" />
    <Compile Include="Components\MetroStyleManager.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Components\MetroToolTip.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\MetroButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\MetroCheckBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\MetroComboBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\MetroContextMenu.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\MetroLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\MetroLink.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\MetroPanel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\MetroProgressSpinner.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\MetroScrollBar.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\MetroTabControl.cs" />
    <Compile Include="Controls\MetroTabPage.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\MetroTextBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\MetroTile.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\MetroToggle.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Design\MetroButtonDesigner.cs" />
    <Compile Include="Design\MetroCheckBoxDesigner.cs" />
    <Compile Include="Design\MetroLabelDesigner.cs" />
    <Compile Include="Design\MetroLinkDesigner.cs" />
    <Compile Include="Design\MetroPanelDesigner.cs" />
    <Compile Include="Design\MetroProgressSpinnerDesigner.cs" />
    <Compile Include="Design\MetroRadioButtonDesigner.cs" />
    <Compile Include="Design\MetroScrollBarDesigner.cs" />
    <Compile Include="Design\MetroStyleManagerDesigner.cs" />
    <Compile Include="Design\MetroTabControlDesigner.cs" />
    <Compile Include="Design\MetroTabPageDesigner.cs" />
    <Compile Include="Design\MetroTextBoxDesigner.cs" />
    <Compile Include="Design\MetroTileDesigner.cs" />
    <Compile Include="Design\MetroToggleDesigner.cs" />
    <Compile Include="Drawing\MetroImage.cs" />
    <Compile Include="Drawing\MetroPaint.cs" />
    <Compile Include="Forms\MetroForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MetroFormShadowType.cs" />
    <Compile Include="Interfaces\IMetroComponent.cs" />
    <Compile Include="Interfaces\IMetroControl.cs" />
    <Compile Include="Interfaces\IMetroForm.cs" />
    <Compile Include="Localization\MetroLocalize.cs" />
    <Compile Include="MetroBrushes.cs" />
    <Compile Include="MetroColors.cs" />
    <Compile Include="MetroColorStyle.cs" />
    <Compile Include="MetroFonts.cs" />
    <Compile Include="MetroPens.cs" />
    <Compile Include="MetroThemeStyle.cs" />
    <Compile Include="Native\DwmApi.cs" />
    <Compile Include="Native\Taskbar.cs" />
    <Compile Include="Native\WinApi.cs" />
    <Compile Include="Native\WinCaret.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Service Include="{94E38DFF-614B-4cbd-B67C-F211BB35CE8B}" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Localization\zh\MetroToggle.xml" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>