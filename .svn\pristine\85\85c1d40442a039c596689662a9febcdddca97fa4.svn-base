﻿using MetroFramework.Components;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace MetroFramework.Controls
{
    public class MetroGrid : DataGridView, IMetroControl
    {
        private MetroColorStyle metroStyle;

        private MetroThemeStyle metroTheme;

        private MetroStyleManager metroStyleManager;

        private bool useCustomBackColor;

        private bool useCustomForeColor;

        private bool useStyleColors;

        private float _offset = 0.2f;

        private MetroDataGridHelper scrollhelper;

        private MetroDataGridHelper scrollhelperH;

        private IContainer components;

        private MetroScrollBar _horizontal;

        private MetroScrollBar _vertical;

        [Category("Metro Appearance")]
        [DefaultValue(MetroColorStyle.Blue)]
        public MetroColorStyle Style
        {
            get
            {
                if (base.DesignMode || metroStyle != 0)
                {
                    return metroStyle;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Style;
                }
                if (StyleManager == null)
                {
                    return MetroColorStyle.Blue;
                }
                return metroStyle;
            }
            set
            {
                metroStyle = value;
                StyleGrid();
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroThemeStyle.Light)]
        public MetroThemeStyle Theme
        {
            get
            {
                if (base.DesignMode || metroTheme != 0)
                {
                    return metroTheme;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Theme;
                }
                if (StyleManager == null)
                {
                    return MetroThemeStyle.Light;
                }
                return metroTheme;
            }
            set
            {
                metroTheme = value;
                StyleGrid();
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        public MetroStyleManager StyleManager
        {
            get
            {
                return metroStyleManager;
            }
            set
            {
                metroStyleManager = value;
                StyleGrid();
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomBackColor
        {
            get
            {
                return useCustomBackColor;
            }
            set
            {
                useCustomBackColor = value;
            }
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseCustomForeColor
        {
            get
            {
                return useCustomForeColor;
            }
            set
            {
                useCustomForeColor = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseStyleColors
        {
            get
            {
                return useStyleColors;
            }
            set
            {
                useStyleColors = value;
            }
        }

        [DefaultValue(true)]
        [Category("Metro Behaviour")]
        [Browsable(false)]
        public bool UseSelectable
        {
            get
            {
                return GetStyle(ControlStyles.Selectable);
            }
            set
            {
                SetStyle(ControlStyles.Selectable, value);
            }
        }

        [DefaultValue(0.2f)]
        public float HighLightPercentage
        {
            get
            {
                return _offset;
            }
            set
            {
                _offset = value;
            }
        }

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintBackground != null)
            {
                this.CustomPaintBackground(this, e);
            }
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaint != null)
            {
                this.CustomPaint(this, e);
            }
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintForeground != null)
            {
                this.CustomPaintForeground(this, e);
            }
        }

        public MetroGrid()
        {
            InitializeComponent();
            StyleGrid();
            base.Controls.Add(_vertical);
            base.Controls.Add(_horizontal);
            base.Controls.SetChildIndex(_vertical, 0);
            base.Controls.SetChildIndex(_horizontal, 1);
            _horizontal.Visible = false;
            _vertical.Visible = false;
            scrollhelper = new MetroDataGridHelper(_vertical, this);
            scrollhelperH = new MetroDataGridHelper(_horizontal, this, vertical: false);
            DoubleBuffered = true;
        }

        protected override void OnColumnStateChanged(DataGridViewColumnStateChangedEventArgs e)
        {
            base.OnColumnStateChanged(e);
            if (e.StateChanged == DataGridViewElementStates.Visible)
            {
                scrollhelper.UpdateScrollbar();
                scrollhelperH.UpdateScrollbar();
            }
        }

        protected override void OnMouseWheel(MouseEventArgs e)
        {
            base.OnMouseWheel(e);
            if (base.RowCount > 1)
            {
                if (e.Delta > 0 && base.FirstDisplayedScrollingRowIndex > 0)
                {
                    base.FirstDisplayedScrollingRowIndex--;
                }
                else if (e.Delta < 0)
                {
                    base.FirstDisplayedScrollingRowIndex++;
                }
            }
        }

        private void StyleGrid()
        {
            base.BorderStyle = BorderStyle.None;
            base.CellBorderStyle = DataGridViewCellBorderStyle.None;
            base.EnableHeadersVisualStyles = false;
            base.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            BackColor = MetroPaint.BackColor.Form(Theme);
            base.BackgroundColor = MetroPaint.BackColor.Form(Theme);
            base.GridColor = MetroPaint.BackColor.Form(Theme);
            ForeColor = MetroPaint.ForeColor.Button.Disabled(Theme);
            Font = new Font("Segoe UI", 11f, FontStyle.Regular, GraphicsUnit.Pixel);
            base.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            base.AllowUserToResizeRows = false;
            base.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;
            base.ColumnHeadersDefaultCellStyle.BackColor = MetroPaint.GetStyleColor(Style);
            base.ColumnHeadersDefaultCellStyle.ForeColor = MetroPaint.ForeColor.Button.Press(Theme);
            base.RowHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;
            base.RowHeadersDefaultCellStyle.BackColor = MetroPaint.GetStyleColor(Style);
            base.RowHeadersDefaultCellStyle.ForeColor = MetroPaint.ForeColor.Button.Press(Theme);
            base.DefaultCellStyle.BackColor = MetroPaint.BackColor.Form(Theme);
            base.DefaultCellStyle.SelectionBackColor = ControlPaint.Light(MetroPaint.GetStyleColor(Style), _offset);
            base.DefaultCellStyle.SelectionForeColor = Color.FromArgb(17, 17, 17);
            base.DefaultCellStyle.SelectionBackColor = ControlPaint.Light(MetroPaint.GetStyleColor(Style), _offset);
            base.DefaultCellStyle.SelectionForeColor = Color.FromArgb(17, 17, 17);
            base.RowHeadersDefaultCellStyle.SelectionBackColor = ControlPaint.Light(MetroPaint.GetStyleColor(Style), _offset);
            base.RowHeadersDefaultCellStyle.SelectionForeColor = Color.FromArgb(17, 17, 17);
            base.ColumnHeadersDefaultCellStyle.SelectionBackColor = ControlPaint.Light(MetroPaint.GetStyleColor(Style), _offset);
            base.ColumnHeadersDefaultCellStyle.SelectionForeColor = Color.FromArgb(17, 17, 17);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null)
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            _horizontal = new MetroFramework.Controls.MetroScrollBar();
            _vertical = new MetroFramework.Controls.MetroScrollBar();
            ((System.ComponentModel.ISupportInitialize)this).BeginInit();
            SuspendLayout();
            _horizontal.LargeChange = 10;
            _horizontal.Location = new System.Drawing.Point(0, 0);
            _horizontal.Maximum = 100;
            _horizontal.Minimum = 0;
            _horizontal.MouseWheelBarPartitions = 10;
            _horizontal.Name = "_horizontal";
            _horizontal.Orientation = MetroFramework.Controls.MetroScrollOrientation.Horizontal;
            _horizontal.ScrollbarSize = 50;
            _horizontal.Size = new System.Drawing.Size(200, 50);
            _horizontal.TabIndex = 0;
            _horizontal.UseSelectable = true;
            _vertical.LargeChange = 10;
            _vertical.Location = new System.Drawing.Point(0, 0);
            _vertical.Maximum = 100;
            _vertical.Minimum = 0;
            _vertical.MouseWheelBarPartitions = 10;
            _vertical.Name = "_vertical";
            _vertical.Orientation = MetroFramework.Controls.MetroScrollOrientation.Vertical;
            _vertical.ScrollbarSize = 50;
            _vertical.Size = new System.Drawing.Size(50, 200);
            _vertical.TabIndex = 0;
            _vertical.UseSelectable = true;
            ((System.ComponentModel.ISupportInitialize)this).EndInit();
            ResumeLayout(false);
        }
    }
}
