﻿using System.Drawing;
using System.Windows.Forms;

namespace MetroFramework.Drawing
{
    public sealed class MetroPaint
    {
        public static Color GetStyleColor(MetroColorStyle style)
        {
            switch (style)
            {
                case MetroColorStyle.黑色:
                    return MetroColors.Black;
                case MetroColorStyle.白色:
                    return MetroColors.White;
                case MetroColorStyle.银色:
                    return MetroColors.Silver;
                case MetroColorStyle.蓝色:
                    return MetroColors.Blue;
                case MetroColorStyle.绿色:
                    return MetroColors.Green;
                case MetroColorStyle.橙色:
                    return MetroColors.Lime;
                case MetroColorStyle.青色:
                    return MetroColors.Teal;
                case MetroColorStyle.橘色:
                    return MetroColors.Orange;
                case MetroColorStyle.棕色:
                    return MetroColors.Brown;
                case MetroColorStyle.粉色:
                    return MetroColors.Pink;
                case MetroColorStyle.品红:
                    return MetroColors.Magenta;
                case MetroColorStyle.紫色:
                    return MetroColors.Purple;
                case MetroColorStyle.红色:
                    return MetroColors.Red;
                case MetroColorStyle.黄色:
                    return MetroColors.Yellow;
                default:
                    return MetroColors.Blue;
            }
        }

        public static SolidBrush GetStyleBrush(MetroColorStyle style)
        {
            var color = GetStyleColor(style);
            return MetroBrushes.GetSaveBrush(style.ToString(), color);
        }

        public static Pen GetStylePen(MetroColorStyle style)
        {
            var color = GetStyleColor(style);
            return MetroPens.GetSavePen(style.ToString(), color);
        }

        public static TextFormatFlags GetTextFormatFlags(ContentAlignment textAlign)
        {
            return GetTextFormatFlags(textAlign, false);
        }

        public static TextFormatFlags GetTextFormatFlags(ContentAlignment textAlign, bool wrapToLine)
        {
            var textFormatFlags = wrapToLine ? TextFormatFlags.WordBreak : TextFormatFlags.EndEllipsis;

            switch (textAlign)
            {
                case ContentAlignment.TopLeft:
                    break;
                case ContentAlignment.TopCenter:
                    textFormatFlags |= TextFormatFlags.HorizontalCenter;
                    break;
                case ContentAlignment.TopRight:
                    textFormatFlags |= TextFormatFlags.Right;
                    break;
                case ContentAlignment.MiddleLeft:
                    textFormatFlags |= TextFormatFlags.VerticalCenter;
                    break;
                case ContentAlignment.MiddleCenter:
                    textFormatFlags |= TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter;
                    break;
                case ContentAlignment.MiddleRight:
                    textFormatFlags |= TextFormatFlags.Right | TextFormatFlags.VerticalCenter;
                    break;
                case ContentAlignment.BottomLeft:
                    textFormatFlags |= TextFormatFlags.Bottom;
                    break;
                case ContentAlignment.BottomCenter:
                    textFormatFlags |= TextFormatFlags.Bottom | TextFormatFlags.HorizontalCenter;
                    break;
                case ContentAlignment.BottomRight:
                    textFormatFlags |= TextFormatFlags.Bottom | TextFormatFlags.Right;
                    break;
            }

            return textFormatFlags;
        }

        public sealed class BorderColor
        {
            public static Color Form(MetroThemeStyle theme)
            {
                if (theme == MetroThemeStyle.Dark) return Color.FromArgb(68, 68, 68);
                return Color.FromArgb(204, 204, 204);
            }

            public static class Button
            {
                public static Color Normal(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(68, 68, 68);
                    return Color.FromArgb(204, 204, 204);
                }

                public static Color Hover(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(170, 170, 170);
                    return Color.FromArgb(102, 102, 102);
                }

                public static Color Press(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(238, 238, 238);
                    return Color.FromArgb(51, 51, 51);
                }

                public static Color Disabled(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(109, 109, 109);
                    return Color.FromArgb(155, 155, 155);
                }
            }

            public static class CheckBox
            {
                public static Color Normal(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(153, 153, 153);
                    return Color.FromArgb(153, 153, 153);
                }

                public static Color Hover(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(204, 204, 204);
                    return Color.FromArgb(51, 51, 51);
                }

                public static Color Press(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(153, 153, 153);
                    return Color.FromArgb(153, 153, 153);
                }

                public static Color Disabled(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(85, 85, 85);
                    return Color.FromArgb(204, 204, 204);
                }
            }

            public static class TabControl
            {
                public static Color Normal(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(68, 68, 68);
                    return Color.FromArgb(204, 204, 204);
                }

                public static Color Hover(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(68, 68, 68);
                    return Color.FromArgb(204, 204, 204);
                }

                public static Color Press(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(68, 68, 68);
                    return Color.FromArgb(204, 204, 204);
                }

                public static Color Disabled(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(109, 109, 109);
                    return Color.FromArgb(155, 155, 155);
                }
            }
        }

        public sealed class BackColor
        {
            public static Color Form(MetroThemeStyle theme)
            {
                if (theme == MetroThemeStyle.Dark) return Color.FromArgb(17, 17, 17);
                return Color.FromArgb(255, 255, 255);
            }

            public sealed class Button
            {
                public static Color Normal(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(34, 34, 34);
                    return Color.FromArgb(238, 238, 238);
                }

                public static Color Hover(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(170, 170, 170);
                    return Color.FromArgb(102, 102, 102);
                }

                public static Color Press(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(238, 238, 238);
                    return Color.FromArgb(51, 51, 51);
                }

                public static Color Disabled(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(80, 80, 80);
                    return Color.FromArgb(204, 204, 204);
                }
            }

            public sealed class ScrollBar
            {
                public sealed class Thumb
                {
                    public static Color Normal(MetroThemeStyle theme)
                    {
                        if (theme == MetroThemeStyle.Dark) return Color.FromArgb(51, 51, 51);
                        return Color.FromArgb(221, 221, 221);
                    }

                    public static Color Hover(MetroThemeStyle theme)
                    {
                        if (theme == MetroThemeStyle.Dark) return Color.FromArgb(204, 204, 204);
                        return Color.FromArgb(96, 96, 96);
                    }

                    public static Color Press(MetroThemeStyle theme)
                    {
                        if (theme == MetroThemeStyle.Dark) return Color.FromArgb(204, 204, 204);
                        return Color.FromArgb(96, 96, 96);
                    }

                    public static Color Disabled(MetroThemeStyle theme)
                    {
                        if (theme == MetroThemeStyle.Dark) return Color.FromArgb(51, 51, 51);
                        return Color.FromArgb(221, 221, 221);
                    }
                }

                public sealed class Bar
                {
                    public static Color Normal(MetroThemeStyle theme)
                    {
                        if (theme == MetroThemeStyle.Dark) return Color.FromArgb(38, 38, 38);
                        return Color.FromArgb(234, 234, 234);
                    }

                    public static Color Hover(MetroThemeStyle theme)
                    {
                        if (theme == MetroThemeStyle.Dark) return Color.FromArgb(38, 38, 38);
                        return Color.FromArgb(234, 234, 234);
                    }

                    public static Color Press(MetroThemeStyle theme)
                    {
                        if (theme == MetroThemeStyle.Dark) return Color.FromArgb(38, 38, 38);
                        return Color.FromArgb(234, 234, 234);
                    }

                    public static Color Disabled(MetroThemeStyle theme)
                    {
                        if (theme == MetroThemeStyle.Dark) return Color.FromArgb(38, 38, 38);
                        return Color.FromArgb(234, 234, 234);
                    }
                }
            }
        }

        public sealed class ForeColor
        {
            public static Color Title(MetroThemeStyle theme)
            {
                if (theme == MetroThemeStyle.Dark) return Color.FromArgb(255, 255, 255);
                return Color.FromArgb(0, 0, 0);
            }

            public sealed class Button
            {
                public static Color Normal(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(204, 204, 204);
                    return Color.FromArgb(0, 0, 0);
                }

                public static Color Hover(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(17, 17, 17);
                    return Color.FromArgb(255, 255, 255);
                }

                public static Color Press(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(17, 17, 17);
                    return Color.FromArgb(255, 255, 255);
                }

                public static Color Disabled(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(109, 109, 109);
                    return Color.FromArgb(136, 136, 136);
                }
            }

            public sealed class Label
            {
                public static Color Normal(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(170, 170, 170);
                    return Color.FromArgb(0, 0, 0);
                }

                public static Color Disabled(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(51, 51, 51);
                    return Color.FromArgb(209, 209, 209);
                }
            }

            public sealed class CheckBox
            {
                public static Color Normal(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(170, 170, 170);
                    return Color.FromArgb(17, 17, 17);
                }

                public static Color Hover(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(153, 153, 153);
                    return Color.FromArgb(153, 153, 153);
                }

                public static Color Press(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(153, 153, 153);
                    return Color.FromArgb(153, 153, 153);
                }

                public static Color Disabled(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(93, 93, 93);
                    return Color.FromArgb(136, 136, 136);
                }
            }

            public sealed class TabControl
            {
                public static Color Normal(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(170, 170, 170);
                    return Color.FromArgb(0, 0, 0);
                }

                public static Color Disabled(MetroThemeStyle theme)
                {
                    if (theme == MetroThemeStyle.Dark) return Color.FromArgb(51, 51, 51);
                    return Color.FromArgb(209, 209, 209);
                }
            }
        }
    }
}