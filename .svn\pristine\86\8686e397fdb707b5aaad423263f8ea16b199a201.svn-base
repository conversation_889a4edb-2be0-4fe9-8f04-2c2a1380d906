﻿using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Language;
using OCRTools.Shadow;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools.NewForms
{
    public partial class FormAreaCapture : MetroForm
    {
        public FormAreaCapture()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;

            tipMsg.AutoPopDelay = 5000;
            //设置鼠标停在该控件后，再停多长时间显示说眀性文字
            tipMsg.InitialDelay = 100;
            //设置鼠标从一个控件移到叧一个啌件再次显示该说明性文牸哋时间间隔
            tipMsg.ReshowDelay = 200;
            //蔎置是否显示窗体的说明性文字
            tipMsg.ShowAlways = true;
            tipMsg.ToolTipIcon = ToolTipIcon.Info;
        }

        public Size CaptureSize { get; set; }

        public Point CaptureLocation { get; set; }

        public int DelayMilSecond { get; set; } = 2000;

        public int IntervalMilSecond { get; set; } = 3500;

        public int LoopTimes { get; set; } = 1;

        public bool IsShowInMainWindow { get; set; }

        private void FormAreaCapture_Load(object sender, EventArgs e)
        {
            var maxSize = NativeMethods.GetScreenBounds();
            nWidth.Maximum = maxSize.Width;
            nHeight.Maximum = maxSize.Height;
            nLeft.Maximum = maxSize.Right;
            nTop.Maximum = maxSize.Bottom;

            if (!CaptureSize.IsEmpty)
            {
                chkSize.Checked = true;
                nWidth.Maximum = Math.Max(nWidth.Maximum, CaptureSize.Width);
                nWidth.Value = CaptureSize.Width;
                nHeight.Maximum = Math.Max(nHeight.Maximum, CaptureSize.Height);
                nHeight.Value = CaptureSize.Height;
            }

            if (!CaptureLocation.IsEmpty)
            {
                chkPoint.Checked = true;
                nLeft.Maximum = Math.Max(nLeft.Maximum, CaptureLocation.X);
                nTop.Maximum = Math.Max(nTop.Maximum, CaptureLocation.Y);
                nLeft.Value = CaptureLocation.X;
                nTop.Value = CaptureLocation.Y;
            }

            nSleep.Value = (decimal)(DelayMilSecond * 1.0 / 1000);
            nInterval.Value = (decimal)Math.Max(1, IntervalMilSecond * 1.0 / 1000);
            nLoopTimes.Value = LoopTimes;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            CaptureLocation = chkPoint.Checked ? new Point((int)nLeft.Value, (int)nTop.Value) : Point.Empty;
            CaptureSize = chkSize.Checked ? new Size((int)nWidth.Value, (int)nHeight.Value) : Size.Empty;

            LoopTimes = (int)nLoopTimes.Value;
            IntervalMilSecond = (int)(nInterval.Value * 1000);

            DelayMilSecond = chkSleep.Checked ? (int)(nSleep.Value * 1000) : 0;
            IsShowInMainWindow = chkShowInMainWindow.Checked;

            DialogResult = DialogResult.OK;
        }
    }

    public class FixAreaCaptureEntity
    {
        public ShadowForm ShadowForm { get; set; }
        public ToolStripItem Item { get; set; }
        public Rectangle BaseRect { get; set; }
        public int DelayMilSec { get; set; }
        public int LoopTimes { get; set; }
        public int IntervalMilSecond { get; set; }
        public bool IsOcr => Item?.AccessibleDescription.Contains("识别") == true || Item?.AccessibleDescription.Contains("翻译") == true;

        public OcrType OcrType => Item?.AccessibleDescription.Contains("翻译") == true ? OcrType.翻译 : OcrType.竖排;

        public bool IsRun => Item == null || Item.Text.StartsWith("停止".CurrentText());

        public bool IsShowInMainWindow { get; set; } = true;
    }
}