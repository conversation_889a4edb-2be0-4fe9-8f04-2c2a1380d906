﻿namespace OcrMain
{
    public static class StringExtension
    {
        //Horspool匹配算法
        public static string SubStringHorspool(this string str, string strStart, string strEnd = "")
        {
            var index = 0;
            if (!string.IsNullOrEmpty(strStart))
            {
                index = str.HorspoolIndex(strStart);
                str = index >= 0 ? str.Substring(index + strStart.Length) : "";
            }
            if (!string.IsNullOrEmpty(str) && !string.IsNullOrEmpty(strEnd))
            {
                index = str.HorspoolIndex(strEnd);
                str = index >= 0 ? str.Substring(0, index) : "";
            }
            strStart = null;
            strEnd = null;
            return str;
        }

        #region Horspool算法

        //Horspool匹配算法，平均时间复杂度O(n)
        public static int HorspoolIndex(this string source, string target, int pos = 0)
        {
            var sLen = source.Length;
            var tLen = target.Length;
            var Ti = tLen - 1; //目标字符串匹配位置
            var Si = pos + Ti; //源字符串匹配位置

            if (sLen - pos < tLen)
                return -1;

            while ((Ti > -1) && (Si < sLen))
            {
                if (source[Si] == target[Ti])
                {
                    Ti--;
                    Si--;
                }
                else
                {
                    while ((Ti > -1) && (source[Si] != target[Ti]))
                    {
                        Ti--;
                    }
                    Si += tLen - 1 - Ti;
                    Ti = tLen - 1;
                }
            }

            if (Si < sLen)
                return Si + 1;
            return -1;
        }

        #endregion

        #region Old

        //#region KMP算法

        //private static int[] GetNext(string target)
        //{
        //    int k = -1;
        //    int i = 0;

        //    int[] next = new int[target.Length];
        //    next[0] = -1;
        //    while (i < target.Length - 1)
        //    {
        //        if (k == -1 || target[k] == target[i])
        //        {
        //            k = k + 1;
        //            i = i + 1;
        //            next[i] = k;
        //        }
        //        else
        //        {
        //            k = next[k];
        //        }
        //    }
        //    return next;
        //}

        ////KMP匹配算法，时间复杂度为O(m+n)
        //private static int KMPIndex(this string source, string target, int pos = 0)
        //{
        //    int[] next = GetNext(target);
        //    int i = pos, j = 0;
        //    while (i < source.Length && j < target.Length)
        //    {
        //        if (j == -1 || source[i] == target[j])
        //        {
        //            i = i + 1;
        //            j = j + 1;
        //        }
        //        else
        //        {
        //            j = next[j];
        //        }
        //    }
        //    if (j >= target.Length)
        //    {
        //        return (i - target.Length);
        //    }
        //    else
        //    {
        //        return (-1);
        //    }
        //}

        //#endregion

        //#region BM算法
        //#endregion

        //#region Sunday

        ////Sunday匹配算法
        //public static int SundayIndex(this string str, string value)
        //{
        //    //return str.HorspoolIndex(value);
        //    int i, j, p, q, k;
        //    i = j = p = q = k = 0;
        //    while (i <= str.Length - value.Length && j < value.Length)
        //    {
        //        if (str[p] == value[j])
        //        {
        //            p++;
        //            j++;
        //        }
        //        else
        //        {
        //            k = value.Length;
        //            q = i + k;
        //            while (k > 0)
        //            {
        //                k--;
        //                if (value[k] == str[q])
        //                    break;
        //            }
        //            i += value.Length - k;
        //            p = i;
        //            j = 0;
        //        }
        //    }
        //    if (j == value.Length)
        //    {
        //        return i;
        //    }
        //    return -1;
        //}

        //#endregion
        //public static string SubStringByMethod(this string str, string strStart, MethodEmum method, string strEnd = "")
        //{
        //    int index = 0;
        //    if (!string.IsNullOrEmpty(strStart))
        //    {
        //        switch (method)
        //        {
        //            case MethodEmum.KMP:
        //                index = str.KMPIndex(strStart);
        //                break;
        //            case MethodEmum.Sunday:
        //                index = str.SundayIndex(strStart);
        //                break;
        //            case MethodEmum.Horspool:
        //                index = str.HorspoolIndex(strStart);
        //                break;
        //            case MethodEmum.Normal:
        //                index = str.HorspoolIndex(strStart);
        //                break;
        //            default:
        //                index = str.HorspoolIndex(strStart);
        //                break;
        //        }
        //        if (index >= 0)
        //        {
        //            str = str.Substring(index + strStart.Length);
        //        }
        //        else
        //            str = "";
        //    }
        //    if (!string.IsNullOrEmpty(str) && !string.IsNullOrEmpty(strEnd))
        //    {
        //        switch (method)
        //        {
        //            case MethodEmum.KMP:
        //                index = str.KMPIndex(strEnd);
        //                break;
        //            case MethodEmum.Sunday:
        //                index = str.SundayIndex(strEnd);
        //                break;
        //            case MethodEmum.Horspool:
        //                index = str.HorspoolIndex(strEnd);
        //                break;
        //            case MethodEmum.Normal:
        //                index = str.HorspoolIndex(strEnd);
        //                break;
        //            default:
        //                index = str.HorspoolIndex(strEnd);
        //                break;
        //        }
        //        if (index >= 0)
        //        {
        //            str = str.Substring(0, index);
        //        }
        //        else
        //            str = "";
        //    }
        //    strStart = null;
        //    strEnd = null;
        //    return str;
        //}
        ////Sunday匹配算法
        //public static string SubStringSunday(this string str, string strStart, string strEnd = "")
        //{
        //    int index = 0;
        //    if (!string.IsNullOrEmpty(strStart))
        //    {
        //        index = str.SundayIndex(strStart);
        //        if (index >= 0)
        //        {
        //            str = str.Substring(index + strStart.Length);
        //        }
        //        else
        //            str = "";
        //    }
        //    if (!string.IsNullOrEmpty(str) && !string.IsNullOrEmpty(strEnd))
        //    {
        //        index = str.SundayIndex(strEnd);
        //        if (index >= 0)
        //        {
        //            str = str.Substring(0, index);
        //        }
        //        else
        //            str = "";
        //    }
        //    strStart = null;
        //    strEnd = null;
        //    return str;
        //}

        ////KMP匹配算法
        //public static string SubStringKMP(this string str, string strStart, string strEnd = "")
        //{
        //    int index = 0;
        //    if (!string.IsNullOrEmpty(strStart))
        //    {
        //        index = str.KMPIndex(strStart);
        //        if (index >= 0)
        //        {
        //            str = str.Substring(index + strStart.Length);
        //        }
        //        else
        //            str = "";
        //    }
        //    if (!string.IsNullOrEmpty(str) && !string.IsNullOrEmpty(strEnd))
        //    {
        //        index = str.KMPIndex(strEnd);
        //        if (index >= 0)
        //        {
        //            str = str.Substring(0, index);
        //        }
        //        else
        //            str = "";
        //    }
        //    strStart = null;
        //    strEnd = null;
        //    return str;
        //}
        ///// <summary>
        ///// Splits a given string into an array based on character line breaks
        ///// </summary>
        ///// <param name="input"></param>
        ///// <returns>String array, each containing one line</returns>
        //public static string[] ToLineArray(this string input)
        //{
        //    if (input == null) return new string[] { };
        //    return input.Split(new string[] { "\n" }, StringSplitOptions.RemoveEmptyEntries);
        //}

        ///// <summary>
        ///// Splits a given string into a strongly-typed list based on character line breaks
        ///// </summary>
        ///// <param name="input"></param>
        ///// <returns>Strongly-typed string list, each containing one line</returns>
        //public static List<string> ToLineList(this string input)
        //{
        //    List<string> output = new List<string>();
        //    output.AddRange(input.ToLineArray());
        //    return output;
        //}

        #endregion
    }
}
