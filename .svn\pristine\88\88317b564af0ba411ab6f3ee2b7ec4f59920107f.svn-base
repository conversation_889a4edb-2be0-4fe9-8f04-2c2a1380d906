﻿using OCRTools.Shadow;
using System;
using System.Diagnostics;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class frmPasteImage : ShadowForm
    {
        private bool isShadowVisiable = true;
        private Size lastSize;

        private Timer tmrResize;

        public frmPasteImage()
        {
            InitializeComponent();
            BackgroundImageLayout = ImageLayout.None;
            Padding = CommonString.PaddingZero;
            BackColor = CommonSetting.默认背景颜色;
        }

        public ClipboardTextEntity Data { get; set; }

        private void InitTmrResize()
        {
            if (tmrResize == null)
            {
                tmrResize = new Timer
                {
                    Interval = 300
                };
                tmrResize.Tick += TmrResize_Tick;
            }
        }

        private void TmrResize_Tick(object sender, EventArgs e)
        {
            if (Equals(lastSize, Size))
            {
                tmrResize.Stop();
                //end Resize
                switch (Data.Type)
                {
                    case ClipboardDataType.Html:
                    case ClipboardDataType.Rtf:
                        if (!Equals(Data.ForceSize, Size))
                        {
                            Data.ForceSize = Size;
                            InitImage();
                        }

                        break;
                }

                BindImage();
            }
            else
            {
                lastSize = Size;
            }
        }

        private void FrmPasteImage_SizeChanged(object sender, EventArgs e)
        {
            InitTmrResize();
            if (!tmrResize.Enabled) tmrResize.Start();
        }

        private void BindImage()
        {
            if (Data.Image != null)
            {
                BackgroundImage = Data.Image;
                Size = new Size(Data.Image.Size.Width, Data.Image.Size.Height);
                Data.ForceSize = Size;
            }

            //Console.WriteLine(string.Format("BindImage ImgSize:{0},forceSize:{1},windowSize:{2}", Data.Image.Size, Data.ForceSize, Size));
            Refresh();
            DrawShadow(ShadowColor);
        }

        public void SetImageData()
        {
            tsmCopyText.Visible = !string.IsNullOrEmpty(Data.OriginalText);
            tsmOCR.Visible = string.IsNullOrEmpty(Data.OriginalText);
            if (Data.Location.IsEmpty || !CommonSetting.截图贴图时使用截屏的位置)
                StartPosition = FormStartPosition.WindowsDefaultLocation;
            else
                Location = Data.Location;
            if (Data.Image == null && !string.IsNullOrEmpty(Data.Content)) InitImage();
            if (Data.Image == null)
            {
                //this.Close();
            }

            BindImage();
        }

        public void BingSizeChangeEvent(bool isAdd)
        {
            SizeChanged -= FrmPasteImage_SizeChanged;
            if (isAdd)
                SizeChanged += FrmPasteImage_SizeChanged;
        }

        private void InitImage()
        {
            switch (Data.Type)
            {
                case ClipboardDataType.Html:
                    Data.Image = CommonToImage.ByHtml_WebBroswer(Data.Content, Data.ForceSize);
                    //img = CommonToImage.ByHtml_HtmlRender(strText, Color.White);
                    break;
                case ClipboardDataType.Rtf:
                    Data.Image = CommonToImage.ByRTF(Data.Content, Data.ForceSize);
                    break;
            }
            if (Data.Image != null && Data.ForceSize.IsEmpty)
            {
                var crop = CommonToImage.AutoCrop((Bitmap)Data.Image, (int)CommonSetting.贴图页边距宽度);
                if (crop != null)
                {
                    Console.WriteLine("oldImgSize:{0},forceSize:{1},cropSize:{2}", Data.Image.Size, Data.ForceSize,
                        crop.Size);
                    Data.Image = crop;
                }
            }
        }

        private void 原始尺寸ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            BingSizeChangeEvent(false);
            Size = BackgroundImage.Size;
            BingSizeChangeEvent(true);
        }

        private void frmPasteImage_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape) cmsClose_Click(sender, null);
        }

        private void 复制图像ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ClipboardService.ClipSetImage(BackgroundImage, false);
            CommonMethod.ShowHelpMsg("贴图已成功复制到剪切板！");
        }

        private void 阴影ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            isShadowVisiable = !isShadowVisiable;
            Visibility(isShadowVisiable);
            阴影ToolStripMenuItem.Checked = isShadowVisiable;
        }

        private void cmsTop_Click(object sender, EventArgs e)
        {
            TopMost = !TopMost;
            cmsTop.Text = TopMost ? "取消置顶" : "置顶窗体";
        }

        private void cmsClose_Click(object sender, EventArgs e)
        {
            Visibility(false, false);
            Close();
        }

        private void cmsSave_Click(object sender, EventArgs e)
        {
            BackgroundImage.SaveFile(this);
        }

        private void cmsOCR_Click(object sender, EventArgs e)
        {
            BackgroundImage.Ocr();
        }

        private void frmPasteImage_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            cmsClose_Click(sender, null);
        }
    }
}