﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectGuid>{18E9B1BE-B80D-4830-8170-C8EF08AAC267}</ProjectGuid>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x64</Platform>
    <OutputType>WinExe</OutputType>
    <AssemblyName>OCR助手</AssemblyName>
    <TargetFrameworkIdentifier>.NETFramework</TargetFrameworkIdentifier>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Platform)' == 'x64' ">
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <OutputPath>bin\Debug\</OutputPath>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <OutputPath>bin\Release\</OutputPath>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
  </PropertyGroup>
  <PropertyGroup>
    <RootNamespace>OCRTools</RootNamespace>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
    <LangVersion>7.1</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <OutputPath>bin\Debug\</OutputPath>
    <Prefer32Bit>false</Prefer32Bit>
    <Optimize>false</Optimize>
    <DefineConstants>DEBUG</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <OutputPath>bin\Release\</OutputPath>
    <Prefer32Bit>false</Prefer32Bit>
    <Optimize>true</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <Prefer32Bit>false</Prefer32Bit>
    <LangVersion>7.1</LangVersion>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>ico.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject>OCRTools.Program</StartupObject>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>false</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <DelaySign>false</DelaySign>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>false</SignManifests>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>CD0BDF2CB9186F0B0DE4379F02024E6AFF6B6262</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>OCRTools_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Interop.UIAutomationClient, Version=12.0.21213.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <HintPath>RefDll\Interop.UIAutomationClient.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.mshtml, Version=7.0.3300.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="O2S.Components.PDFRender4NET">
      <HintPath>RefDll\O2S.Components.PDFRender4NET.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Design" />
    <Reference Include="System.Management" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Colors\CMYK.cs" />
    <Compile Include="Colors\ColorBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Colors\ColorEventHandler.cs" />
    <Compile Include="Colors\ColorHelper.cs" />
    <Compile Include="Colors\ColorPicker.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Colors\ColorPickerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Colors\ColorPickerForm.designer.cs">
      <DependentUpon>ColorPickerForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Colors\ColorSlider.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Colors\ColorUserControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Colors\ControlHider.cs" />
    <Compile Include="Colors\HSB.cs" />
    <Compile Include="Colors\MyColor.cs" />
    <Compile Include="Colors\RGBA.cs" />
    <Compile Include="CommonToImage.cs" />
    <Compile Include="Common\ChinaDate.cs" />
    <Compile Include="Common\CommonEnumAction.cs" />
    <Compile Include="Common\CommonShortKey.cs" />
    <Compile Include="Common\CommonStyle.cs" />
    <Compile Include="Common\CommonWeather.cs" />
    <Compile Include="Common\FileDropHandler.cs" />
    <Compile Include="Common\IDropTargetHelper.cs" />
    <Compile Include="Common\LoadingTypeHelper.cs" />
    <Compile Include="NewForms\CommonSetting.cs" />
    <Compile Include="NewForms\FormPDFProcess.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NewForms\FormPDFProcess.Designer.cs">
      <DependentUpon>FormPDFProcess.cs</DependentUpon>
    </Compile>
    <Compile Include="NewForms\FormAreaCapture.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NewForms\FormAreaCapture.Designer.cs">
      <DependentUpon>FormAreaCapture.cs</DependentUpon>
    </Compile>
    <Compile Include="NewForms\FormSetting.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NewForms\FormSetting.Designer.cs">
      <DependentUpon>FormSetting.cs</DependentUpon>
    </Compile>
    <Compile Include="OCRTools\BaseAnimation.cs" />
    <Compile Include="OtherExt\UtfUnknown\CharsetDetector.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Analyzers\CharDistributionAnalyser.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Analyzers\Chinese\BIG5DistributionAnalyser.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Analyzers\Chinese\EUCTWDistributionAnalyser.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Analyzers\Chinese\GB18030DistributionAnalyser.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\BitPackage.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\CodepageName.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\InputState.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Models\MultiByte\Chinese\BIG5SMModel.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Models\MultiByte\Chinese\EUCTWSMModel.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Models\MultiByte\Chinese\GB18030_SMModel.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Models\MultiByte\Chinese\HZ_GB_2312_SMModel.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Models\MultiByte\Chinese\Iso_2022_CN_SMModel.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Models\MultiByte\UCS2BE_SMModel.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Models\MultiByte\UCS2LE_SMModel.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Models\MultiByte\UTF8_SMModel.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Models\SequenceModel.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Models\StateMachineModel.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\CharsetProber.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\CodingStateMachine.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\EscCharsetProber.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\HebrewProber.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\Latin1Prober.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\MBCSGroupProber.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\MultiByte\Chinese\Big5Prober.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\MultiByte\Chinese\EUCTWProber.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\MultiByte\Chinese\GB18030Prober.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\MultiByte\UTF8Prober.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\ProbingState.cs" />
    <Compile Include="OtherExt\UtfUnknown\Code\Probers\SingleByteCharSetProber.cs" />
    <Compile Include="OtherExt\UtfUnknown\DetectionDetail.cs" />
    <Compile Include="OtherExt\UtfUnknown\DetectionResult.cs" />
    <Compile Include="OtherExt\UIAComWrapper\AnnotationPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\Automation.cs" />
    <Compile Include="OtherExt\UIAComWrapper\AutomationElement.cs" />
    <Compile Include="OtherExt\UIAComWrapper\AutomationElementCollection.cs" />
    <Compile Include="OtherExt\UIAComWrapper\AutomationTypes.cs" />
    <Compile Include="OtherExt\UIAComWrapper\BasePattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\CacheRequest.cs" />
    <Compile Include="OtherExt\UIAComWrapper\Conditions.cs" />
    <Compile Include="OtherExt\UIAComWrapper\DockPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\DragPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\DropTargetPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\ExpandCollapsePattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\GridPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\InternalSchema.cs" />
    <Compile Include="OtherExt\UIAComWrapper\InternalTypes.cs" />
    <Compile Include="OtherExt\UIAComWrapper\InvokePattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\LegacyIAccessiblePattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\MultipleViewPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\ObjectModelPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\Point.cs" />
    <Compile Include="OtherExt\UIAComWrapper\ProviderInterfaces.cs" />
    <Compile Include="OtherExt\UIAComWrapper\Rect.cs" />
    <Compile Include="OtherExt\UIAComWrapper\ScrollPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\SelectionPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\Size.cs" />
    <Compile Include="OtherExt\UIAComWrapper\SpreadsheetPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\StylesPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\SynchronizedInput.cs" />
    <Compile Include="OtherExt\UIAComWrapper\TablePattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\TextChildPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\TextPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\TextRange.cs" />
    <Compile Include="OtherExt\UIAComWrapper\TextTypes.cs" />
    <Compile Include="OtherExt\UIAComWrapper\TogglePattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\TransformPattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\Utility.cs" />
    <Compile Include="OtherExt\UIAComWrapper\ValuePattern.cs" />
    <Compile Include="OtherExt\UIAComWrapper\VirtualizedPatterns.cs" />
    <Compile Include="OtherExt\UIAComWrapper\WindowPattern.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="RecentControl\BlackStyleLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Common\CommonMsg.cs" />
    <Compile Include="Common\CommonResult.cs" />
    <Compile Include="Common\AutomationExtensions.cs" />
    <Compile Include="Common\CommonUpdate.cs" />
    <Compile Include="Common\Hook\HookManager.Callbacks.cs" />
    <Compile Include="Common\Hook\HookManager.cs" />
    <Compile Include="Common\Hook\HookManager.Structures.cs" />
    <Compile Include="Common\Hook\HookManager.Windows.cs" />
    <Compile Include="Common\Hook\MouseEventExtArgs.cs" />
    <Compile Include="Common\Input\HotKeyEntity.cs" />
    <Compile Include="Common\Input\InputHelpers.cs" />
    <Compile Include="Common\Input\InputManager.cs" />
    <Compile Include="Common\UserEntity.cs" />
    <Compile Include="Common\FlashWindowHelper.cs" />
    <Compile Include="Common\MemoryManager.cs" />
    <Compile Include="Common\MouseWheelMonitor.cs" />
    <Compile Include="Common\NativeMethods.cs" />
    <Compile Include="Common\ObjectForScriptingHelper.cs" />
    <Compile Include="Common\WindowSnap.cs" />
    <Compile Include="Forms\FormOCR.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormOCR.designer.cs">
      <DependentUpon>FormOCR.cs</DependentUpon>
    </Compile>
    <Compile Include="RecentControl\FormRecent.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RecentControl\FormRecent.designer.cs">
      <DependentUpon>FormRecent.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormUpdate.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormUpdate.designer.cs">
      <DependentUpon>FormUpdate.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmBatch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmBatch.Designer.cs">
      <DependentUpon>FrmBatch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmPicCompare.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmPicCompare.Designer.cs">
      <DependentUpon>FrmPicCompare.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\OcrProcessEntity.cs" />
    <Compile Include="Common\ExcelHelper.cs" />
    <Compile Include="Common\ImageProcessHelper.cs" />
    <Compile Include="Common\Log.cs" />
    <Compile Include="Common\ControlExtension.cs" />
    <Compile Include="Common\TableContentInfo.cs" />
    <Compile Include="Expection\ExceptionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Expection\UHEHandler.cs" />
    <Compile Include="Forms\FormTool.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormTool.designer.cs">
      <DependentUpon>FormTool.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmReport.Designer.cs">
      <DependentUpon>FrmReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmSearch.Designer.cs">
      <DependentUpon>FrmSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmForgetPwd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmForgetPwd.Designer.cs">
      <DependentUpon>FrmForgetPwd.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmGoBuy.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmGoBuy.Designer.cs">
      <DependentUpon>FrmGoBuy.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmUserInfo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmUserInfo.Designer.cs">
      <DependentUpon>FrmUserInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmReg.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmReg.Designer.cs">
      <DependentUpon>FrmReg.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FrmLogin.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmLogin.Designer.cs">
      <DependentUpon>FrmLogin.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmMain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Common\ImgUpload\QQImageUpload.cs" />
    <Compile Include="NewForms\NotificationForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="OCRTools\CenterForm.cs" />
    <Compile Include="Ruler\Colors\CommonThemes.cs" />
    <Compile Include="Ruler\Colors\Theme.cs" />
    <Compile Include="Ruler\Forms\CalibrationForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Ruler\Forms\CalibrationForm.Designer.cs">
      <DependentUpon>CalibrationForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Ruler\Forms\RulerBaseForm.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Ruler\Forms\RulerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Ruler\Forms\RulerForm.Designer.cs">
      <DependentUpon>RulerForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Ruler\Forms\RulerFormResizeMode.cs" />
    <Compile Include="Ruler\Forms\RulerOverlayForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Ruler\Forms\SetSizeForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Ruler\Forms\SetSizeForm.Designer.cs">
      <DependentUpon>SetSizeForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Ruler\MouseTracker.cs" />
    <Compile Include="Ruler\RulerMarker.cs" />
    <Compile Include="Ruler\RulerMarkerCollection.cs" />
    <Compile Include="Ruler\RulerPainter.cs" />
    <Compile Include="Ruler\Settings.cs" />
    <Compile Include="Ruler\Units\MeasuringUnit.cs" />
    <Compile Include="Ruler\Units\UnitConverters.cs" />
    <Compile Include="ScrollingCapture\ScrollingCapture.cs" />
    <Compile Include="ScrollingCapture\ScrollingCaptureForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ScrollingCapture\ScrollingCaptureForm.designer.cs">
      <DependentUpon>ScrollingCaptureForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ScrollingCapture\ScrollingCaptureOptions.cs" />
    <Compile Include="ScrollingCapture\WindowsList.cs" />
    <Compile Include="ShadowForm\frmPasteImage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ShadowForm\frmPasteImage.Designer.cs">
      <DependentUpon>frmPasteImage.cs</DependentUpon>
    </Compile>
    <Compile Include="ShadowForm\FormStyleAPI.cs" />
    <Compile Include="ShadowForm\ShadowForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ShadowForm\ShadowForm.designer.cs">
      <DependentUpon>ShadowForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ShadowForm\ShadowFormSkin.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ShadowForm\ShadowFormSkin.designer.cs">
      <DependentUpon>ShadowFormSkin.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\OCRPoolProcess.cs" />
    <Compile Include="OCRTools\Effect.cs" />
    <Compile Include="OCRTools\PrimaryScreen.cs" />
    <Compile Include="OCRTools\RenderHelper.cs" />
    <Compile Include="OCRTools\AutomaticCanvasRefresher.cs" />
    <Compile Include="OCRTools\AutoSelectApi.cs" />
    <Compile Include="OCRTools\AutoSizeTextBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OCRTools\BitDrawImage.cs" />
    <Compile Include="Common\ClipboardService.cs" />
    <Compile Include="OCRTools\ColorBgra.cs" />
    <Compile Include="OCRTools\ComboxbtnRenderer.cs" />
    <Compile Include="OCRTools\Command.cs" />
    <Compile Include="OCRTools\CommandAdd.cs" />
    <Compile Include="OCRTools\CommandChangeState.cs" />
    <Compile Include="OCRTools\CommandDelete.cs" />
    <Compile Include="OCRTools\CommandDeleteAll.cs" />
    <Compile Include="OCRTools\ContextMenuHelp.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OCRTools\CursorData.cs" />
    <Compile Include="OCRTools\CursorEx.cs" />
    <Compile Include="OCRTools\CustomColor.cs" />
    <Compile Include="OCRTools\DrawArea.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="OCRTools\DrawArrow.cs" />
    <Compile Include="OCRTools\DrawCatch.cs" />
    <Compile Include="OCRTools\DrawEllipse.cs" />
    <Compile Include="OCRTools\DrawGaus.cs" />
    <Compile Include="OCRTools\DrawHelp.cs" />
    <Compile Include="OCRTools\DrawHighlight.cs" />
    <Compile Include="OCRTools\DrawLine.cs" />
    <Compile Include="OCRTools\DrawLinebase.cs" />
    <Compile Include="OCRTools\DrawMosaic.cs" />
    <Compile Include="OCRTools\DrawMultiCatch.cs" />
    <Compile Include="OCRTools\DrawObject.cs" />
    <Compile Include="OCRTools\DrawPolygon.cs" />
    <Compile Include="OCRTools\DrawQuickCatch.cs" />
    <Compile Include="OCRTools\DrawRectangle.cs" />
    <Compile Include="OCRTools\DrawRectangleFill.cs" />
    <Compile Include="OCRTools\DrawStep.cs" />
    <Compile Include="OCRTools\DrawText.cs" />
    <Compile Include="OCRTools\DrawToolType.cs" />
    <Compile Include="OCRTools\ExtensionMethods.cs" />
    <Compile Include="OCRTools\FmTip.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="OCRTools\GraphicsList.cs" />
    <Compile Include="OCRTools\GraphicsProperties.cs" />
    <Compile Include="OCRTools\HelpWin32.cs" />
    <Compile Include="OCRTools\HookAPI.cs" />
    <Compile Include="OCRTools\ImageHelp.cs" />
    <Compile Include="OCRTools\KeyModifiers.cs" />
    <Compile Include="OCRTools\ListButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OCRTools\MagnifierHelp.cs" />
    <Compile Include="OCRTools\MathHelpers.cs" />
    <Compile Include="Common\SpiltMode.cs" />
    <Compile Include="Common\UploadFileRequest.cs" />
    <Compile Include="FrmMain.designer.cs">
      <DependentUpon>FrmMain.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\ImgUpload\BaseImageUpload.cs" />
    <Compile Include="Common\ImgUpload\ImageTypeEnum.cs" />
    <Compile Include="Common\ImgUpload\JueJinImageUpload.cs" />
    <Compile Include="Common\ImgUpload\NiuTuImageUpload.cs" />
    <Compile Include="Common\ImgUpload\SMMSImageUpload.cs" />
    <Compile Include="Common\ImgUpload\SouGouImageUpload.cs" />
    <Compile Include="Common\ImgUpload\ImgUrlImageUpload.cs" />
    <Compile Include="Common\ImgUpload\_360ImageUpload.cs" />
    <Compile Include="OCRTools\UnsafeBitmap.cs" />
    <Compile Include="OtherExt\MetroFramework\Components\MetroStyleManager.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OtherExt\MetroFramework\Components\MetroToolTip.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OtherExt\MetroFramework\Controls\MetroButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OtherExt\MetroFramework\Controls\MetroCheckBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OtherExt\MetroFramework\Controls\MetroContextMenu.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OtherExt\MetroFramework\Controls\MetroLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OtherExt\MetroFramework\Controls\MetroScrollBar.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OtherExt\MetroFramework\Controls\MetroTabControl.cs" />
    <Compile Include="OtherExt\MetroFramework\Controls\MetroTabPage.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OtherExt\MetroFramework\Design\MetroButtonDesigner.cs" />
    <Compile Include="OtherExt\MetroFramework\Design\MetroCheckBoxDesigner.cs" />
    <Compile Include="OtherExt\MetroFramework\Design\MetroLabelDesigner.cs" />
    <Compile Include="OtherExt\MetroFramework\Design\MetroScrollBarDesigner.cs" />
    <Compile Include="OtherExt\MetroFramework\Design\MetroStyleManagerDesigner.cs" />
    <Compile Include="OtherExt\MetroFramework\Design\MetroTabControlDesigner.cs" />
    <Compile Include="OtherExt\MetroFramework\Design\MetroTabPageDesigner.cs" />
    <Compile Include="OtherExt\MetroFramework\Drawing\MetroImage.cs" />
    <Compile Include="OtherExt\MetroFramework\Drawing\MetroPaint.cs" />
    <Compile Include="OtherExt\MetroFramework\Forms\MetroForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="OtherExt\MetroFramework\Forms\MetroFormShadowType.cs" />
    <Compile Include="OtherExt\MetroFramework\Interfaces\IMetroComponent.cs" />
    <Compile Include="OtherExt\MetroFramework\Interfaces\IMetroControl.cs" />
    <Compile Include="OtherExt\MetroFramework\Interfaces\IMetroForm.cs" />
    <Compile Include="OtherExt\MetroFramework\MetroBrushes.cs" />
    <Compile Include="OtherExt\MetroFramework\MetroColors.cs" />
    <Compile Include="OtherExt\MetroFramework\MetroColorStyle.cs" />
    <Compile Include="OtherExt\MetroFramework\MetroFonts.cs" />
    <Compile Include="OtherExt\MetroFramework\MetroPens.cs" />
    <Compile Include="OtherExt\MetroFramework\MetroThemeStyle.cs" />
    <Compile Include="OtherExt\MetroFramework\Native\DwmApi.cs" />
    <Compile Include="OtherExt\MetroFramework\Native\WinApi.cs" />
    <Compile Include="OtherExt\MetroFramework\Native\WinCaret.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="OCRTools\Screenshot.cs" />
    <Compile Include="OCRTools\SelectRectangleList.cs" />
    <Compile Include="OCRTools\Spline.cs" />
    <Compile Include="OCRTools\StaticValue.cs" />
    <Compile Include="OCRTools\Status.cs" />
    <Compile Include="OCRTools\TextboxEx.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OCRTools\Tool.cs" />
    <Compile Include="OCRTools\ToolArrow.cs" />
    <Compile Include="OCRTools\ToolCatch.cs" />
    <Compile Include="OCRTools\ToolCatchPointer.cs" />
    <Compile Include="OCRTools\ToolEllipse.cs" />
    <Compile Include="OCRTools\ToolGaus.cs" />
    <Compile Include="OCRTools\ToolHighlight.cs" />
    <Compile Include="OCRTools\ToolLine.cs" />
    <Compile Include="OCRTools\ToolMosaic.cs" />
    <Compile Include="OCRTools\ToolMultiCatch.cs" />
    <Compile Include="OCRTools\ToolObject.cs" />
    <Compile Include="OCRTools\ToolPointer.cs" />
    <Compile Include="OCRTools\ToolPolygon.cs" />
    <Compile Include="OCRTools\ToolQuickCatch.cs" />
    <Compile Include="OCRTools\ToolRectangle.cs" />
    <Compile Include="OCRTools\ToolRectangleFill.cs" />
    <Compile Include="OCRTools\ToolStep.cs" />
    <Compile Include="OCRTools\ToolText.cs" />
    <Compile Include="OCRTools\UndoManager.cs" />
    <Compile Include="OCRTools\UpDownButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OCRTools\UpDownButtonEx.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="OCRTools\Vector.cs" />
    <Compile Include="OCRTools\Win32.cs" />
    <Compile Include="OCRTools\WindowInfo.cs" />
    <Compile Include="Common\HotKeyHelper.cs" />
    <Compile Include="Common\BadSoftWindow.cs" />
    <Compile Include="Common\BoxUtil.cs" />
    <Compile Include="Common\CommonEncryptHelper.cs" />
    <Compile Include="Common\CommonMethod.cs" />
    <Compile Include="Common\CommonReg.cs" />
    <Compile Include="Common\CommonString.cs" />
    <Compile Include="Common\DnsHelper.cs" />
    <Compile Include="Common\ImgLogHelper.cs" />
    <Compile Include="Common\IniHelper.cs" />
    <Compile Include="Common\OcrContent.cs" />
    <Compile Include="Common\ProcessExtensions.cs" />
    <Compile Include="Common\ServerTime.cs" />
    <Compile Include="Common\SNtpClient.cs" />
    <Compile Include="Common\SNTP\ErrorData.cs" />
    <Compile Include="Common\SNTP\LeapIndicator.cs" />
    <Compile Include="Common\SNTP\Mode.cs" />
    <Compile Include="Common\SNTP\QueryServerCompletedEventArgs.cs" />
    <Compile Include="Common\SNTP\ReferenceIdentifier.cs" />
    <Compile Include="Common\SNTP\RemoteSNTPServer.cs" />
    <Compile Include="Common\SNTP\SNTPData.cs" />
    <Compile Include="Common\SNTP\Stratum.cs" />
    <Compile Include="Common\StringExtension.cs" />
    <Compile Include="Common\WebClientExt.cs" />
    <Compile Include="Common\OCRHelper.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Common\TimerTaskService.cs" />
    <Compile Include="RecentControl\TaskRoundedCornerPanel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RecentControl\ThumbnailSizeForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RecentControl\ThumbnailSizeForm.designer.cs">
      <DependentUpon>ThumbnailSizeForm.cs</DependentUpon>
    </Compile>
    <Compile Include="UserControl\ButtonEx.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="UserControl\ImageButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="UserControl\MyPictureBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UserControl\MyPictureBox.designer.cs">
      <DependentUpon>MyPictureBox.cs</DependentUpon>
    </Compile>
    <Compile Include="UserControl\ScrollingText.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="UserControl\PanelPictureView.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RecentControl\TaskThumbnailPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="RecentControl\TaskThumbnailPanel.designer.cs">
      <DependentUpon>TaskThumbnailPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="RecentControl\TaskThumbnailView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="RecentControl\TaskThumbnailView.designer.cs">
      <DependentUpon>TaskThumbnailView.cs</DependentUpon>
    </Compile>
    <Compile Include="UserControl\UCPicView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UserControl\UCPicView.Designer.cs">
      <DependentUpon>UCPicView.cs</DependentUpon>
    </Compile>
    <Compile Include="UserControl\TransParentLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="UserControl\ToolStripCheckBoxControl.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="UserControl\DataGridViewEx.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Forms\FrmImageView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FrmImageView.Designer.cs">
      <DependentUpon>FrmImageView.cs</DependentUpon>
    </Compile>
    <Compile Include="UserControl\ImageBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="UserControl\ImageBox.designer.cs">
      <DependentUpon>ImageBox.cs</DependentUpon>
    </Compile>
    <Compile Include="UserControl\ImageBoxGridDisplayMode.cs" />
    <Compile Include="UserControl\ImageBoxGridScale.cs" />
    <Compile Include="UserControl\ucContent.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UserControl\ucContent.Designer.cs">
      <DependentUpon>ucContent.cs</DependentUpon>
    </Compile>
    <Compile Include="UserControl\ucLoading.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UserControl\ucLoading.Designer.cs">
      <DependentUpon>ucLoading.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Colors\ColorPickerForm.resx">
      <DependentUpon>ColorPickerForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Expection\ExceptionForm.resx">
      <DependentUpon>ExceptionForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormOCR.resx">
      <DependentUpon>FormOCR.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormTool.resx">
      <DependentUpon>FormTool.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NewForms\FormPDFProcess.resx">
      <DependentUpon>FormPDFProcess.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NewForms\FormAreaCapture.resx">
      <DependentUpon>FormAreaCapture.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NewForms\FormSetting.resx">
      <DependentUpon>FormSetting.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RecentControl\FormRecent.resx">
      <DependentUpon>FormRecent.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormUpdate.resx">
      <DependentUpon>FormUpdate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmBatch.resx">
      <DependentUpon>FrmBatch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmPicCompare.resx">
      <DependentUpon>FrmPicCompare.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmReport.resx">
      <DependentUpon>FrmReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmSearch.resx">
      <DependentUpon>FrmSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmForgetPwd.resx">
      <DependentUpon>FrmForgetPwd.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmGoBuy.resx">
      <DependentUpon>FrmGoBuy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmUserInfo.resx">
      <DependentUpon>FrmUserInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmReg.resx">
      <DependentUpon>FrmReg.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmLogin.resx">
      <DependentUpon>FrmLogin.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmMain.resx">
      <DependentUpon>FrmMain.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Ruler\Forms\CalibrationForm.resx">
      <DependentUpon>CalibrationForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ruler\Forms\RulerForm.resx">
      <DependentUpon>RulerForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ruler\Forms\SetSizeForm.resx">
      <DependentUpon>SetSizeForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ScrollingCapture\ScrollingCaptureForm.resx">
      <DependentUpon>ScrollingCaptureForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ShadowForm\frmPasteImage.resx">
      <DependentUpon>frmPasteImage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ShadowForm\ShadowForm.resx">
      <DependentUpon>ShadowForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ShadowForm\ShadowFormSkin.resx">
      <DependentUpon>ShadowFormSkin.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="OCRTools\DrawArea.resx">
      <DependentUpon>DrawArea.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <SubType>Designer</SubType>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="RecentControl\ThumbnailSizeForm.resx">
      <DependentUpon>ThumbnailSizeForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RecentControl\TaskThumbnailPanel.resx">
      <DependentUpon>TaskThumbnailPanel.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="RecentControl\TaskThumbnailView.resx">
      <DependentUpon>TaskThumbnailView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UserControl\UCPicView.resx">
      <DependentUpon>UCPicView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FrmImageView.resx">
      <DependentUpon>FrmImageView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UserControl\ucContent.resx">
      <DependentUpon>ucContent.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UserControl\ucLoading.resx">
      <DependentUpon>ucLoading.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="OCRTools_TemporaryKey.pfx" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\快捷键_0.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\快捷键_1.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\语音按钮.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\帮助.jpg" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="ico.ico" />
    <None Include="Resources\自动标点.png" />
    <None Include="Resources\贴图.png" />
    <None Include="Resources\取色器.png" />
    <None Include="Resources\标尺.png" />
    <None Include="Resources\划词.png" />
    <None Include="Resources\马赛克.png" />
    <None Include="Resources\固定区域.png" />
    <None Include="Resources\翻译_Small.png" />
    <None Include="Resources\图文模式.png" />
    <None Include="Resources\文字模式.png" />
    <None Include="Resources\复制.png" />
    <None Include="Resources\搜索.png" />
    <None Include="Resources\自动.png" />
    <None Include="Resources\英文标点.png" />
    <None Include="Resources\日语.png" />
    <None Include="Resources\葡萄牙语.png" />
    <None Include="Resources\韩语.png" />
    <None Include="Resources\法语.png" />
    <None Include="Resources\德语.png" />
    <None Include="Resources\强制合并.png" />
    <None Include="Resources\自动分段.png" />
    <None Include="Resources\原始格式.png" />
    <None Include="Resources\图文混排.png" />
    <None Include="Resources\英文.png" />
    <None Include="Resources\分段.png" />
    <None Include="Resources\语音.png" />
    <None Include="Resources\通道.png" />
    <None Include="Resources\预览.png" />
    <None Include="Resources\导出.png" />
    <None Include="Resources\文字小.png" />
    <None Include="Resources\图片.png" />
    <None Include="Resources\缩小.png" />
    <None Include="Resources\放大.png" />
    <None Include="Resources\原始.png" />
    <None Include="Resources\窗体.png" />
    <None Include="Resources\工具栏.png" />
    <None Include="Resources\文件.png" />
    <None Include="Resources\截图.png" />
    <None Include="Resources\译文.png" />
    <None Include="Resources\竖排.png" />
    <None Include="Resources\翻译.png" />
    <None Include="Resources\文本.png" />
    <None Include="Resources\ico56.png" />
    <None Include="Resources\cut.png" />
    <None Include="Resources\中文.png" />
    <None Include="Resources\从下往上.png" />
    <None Include="Resources\从左往右.png" />
    <None Include="Resources\从右往左.png" />
    <None Include="Resources\从上往下.png" />
    <None Include="Resources\7_qq.png" />
    <None Include="Resources\0_qq.png" />
    <None Include="Resources\1_qq.png" />
    <None Include="Resources\2_qq.png" />
    <None Include="Resources\3_qq.png" />
    <None Include="Resources\4_qq.png" />
    <None Include="Resources\5_qq.png" />
    <None Include="Resources\6_qq.png" />
    <None Include="Resources\俄语.png" />
    <None Include="Resources\中文标点.png" />
    <None Include="Resources\全屏.png" />
    <None Include="Resources\Info_Error.png" />
    <None Include="Resources\Info_Info.png" />
    <None Include="Resources\Info_OK.png" />
    <None Include="Resources\qqQun.png" />
    <None Include="Resources\qqKeFu.png" />
    <None Include="Resources\vip_1.png" />
    <None Include="Resources\vip_3.png" />
    <None Include="Resources\vip_0.png" />
    <None Include="Resources\vip_2.png" />
    <None Include="Resources\0_load.png" />
    <None Include="Resources\1_load.png" />
    <None Include="Resources\2_load.png" />
    <None Include="Resources\3_load.png" />
    <None Include="Resources\4_load.png" />
    <None Include="Resources\5_load.png" />
    <None Include="Resources\6_load.png" />
    <None Include="Resources\7_load.png" />
    <None Include="Resources\0_red.png" />
    <None Include="Resources\1_red.png" />
    <None Include="Resources\2_red.png" />
    <None Include="Resources\3_red.png" />
    <None Include="Resources\4_red.png" />
    <None Include="Resources\5_red.png" />
    <None Include="Resources\6_red.png" />
    <None Include="Resources\7_red.png" />
    <None Include="Resources\8_red.png" />
    <None Include="Resources\9_red.png" />
    <None Include="Resources\mini-search.png" />
    <None Include="Resources\mini-sprig-down-hover.png" />
    <None Include="Resources\loginurl-close.png" />
    <None Include="Resources\loginurl-close-hover.png" />
    <None Include="Resources\mini-sprig.png" />
    <None Include="Resources\fringe_bkg.png" />
    <None Include="Resources\window_small.png" />
    <None Include="Resources\tips_nolight_bkg.png" />
    <None Include="Resources\recommond.png" />
    <None Include="Resources\hot.png" />
    <None Include="Resources\new.png" />
    <None Include="Resources\batch.PNG" />
    <None Include="Resources\tietu.png" />
    <None Include="Resources\fankui.png" />
    <None Include="Resources\tick.png" />
    <None Include="Resources\clipboard-block.png" />
    <None Include="Resources\pipette.png" />
    <None Include="Resources\application-icon-large.png" />
    <None Include="Resources\image-resize.png" />
    <None Include="Resources\ui-thumbnail-title.png" />
    <None Include="Resources\eraser.png" />
    <None Include="Resources\script--minus.png" />
    <None Include="Resources\bin.png" />
    <None Include="Resources\document-copy.png" />
    <None Include="Resources\folder-open-document.png" />
    <None Include="Resources\keyboard.png" />
    <None Include="Resources\ui-scroll-pane-image.png" />
    <None Include="Resources\Snipaste_2021-03-16_14-22-38.png" />
    <None Include="Resources\toolbox.png" />
    <None Include="Resources\camera.png" />
    <None Include="Resources\uac.png" />
    <None Include="Resources\application-text-image.png" />
    <None Include="Resources\monitor.png" />
    <None Include="Resources\Rectangle.png" />
    <None Include="Resources\globe.png" />
    <None Include="Resources\traffic-cone.png" />
    <None Include="Resources\upload-cloud.png" />
    <None Include="Resources\LoadingSmallBlack.gif" />
    <Content Include="Resources\公式.png" />
    <None Include="Resources\iconic_1-1-1_text_24_0_858585_none.png" />
    <None Include="Resources\竖向布局.png" />
    <None Include="Resources\表格.png" />
    <None Include="Resources\icomoon-free_2014-12-23_file-excel_24_0_858585_none.png" />
    <None Include="Resources\save-file-option.png" />
    <None Include="Resources\符号-图片.png" />
    <None Include="Resources\font-awesome_4-7-0_desktop_24_0_858585_none.png" />
    <None Include="Resources\font-awesome_4-7-0_power-off_24_0_858585_none.png" />
    <None Include="Resources\mfg-labs-iconset_2014-07-29_settings_24_0_858585_none.png" />
    <None Include="Resources\ionicons_2-0-1_settings_24_0_858585_none.png" />
    <None Include="Resources\设置 %282%29.png" />
    <None Include="Resources\pdf.png" />
    <None Include="Resources\font-awesome_4-7-0_image_24_0_858585_none.png" />
    <None Include="Resources\粘贴.png" />
    <None Include="Resources\menu.png" />
    <None Include="Resources\top.png" />
    <None Include="Resources\untop.png" />
    <None Include="Resources\Crystal Clear no.cur" />
    <None Include="Resources\Crystal Clear help.cur" />
    <None Include="Resources\Crystal Clear move.cur" />
    <None Include="Resources\Crystal Clear cross.cur" />
    <None Include="Resources\Crystal Clear beam.cur" />
    <None Include="Resources\Crystal Clear hand.cur" />
    <None Include="Resources\Crystal Clear arrow.cur" />
    <None Include="Resources\Feedback_Bad_Point4.png" />
    <None Include="Resources\Feedback_Bad_Point3.png" />
    <None Include="Resources\Feedback_Bad_Point2.png" />
    <None Include="Resources\Feedback_Bad_Point1.png" />
    <None Include="Resources\Feedback_Bad_Point5.png" />
    <None Include="Resources\NetState_Point5.png" />
    <None Include="Resources\NetState_Point4.png" />
    <None Include="Resources\NetState_Point3.png" />
    <None Include="Resources\NetState_Point2.png" />
    <None Include="Resources\NetState_Point1.png" />
    <None Include="Resources\NetState_Point0.png" />
    <None Include="Resources\ShareScreen_logo.png" />
    <None Include="Resources\screen_loading_9.png" />
    <None Include="Resources\screen_loading_8.png" />
    <None Include="Resources\screen_loading_7.png" />
    <None Include="Resources\screen_loading_6.png" />
    <None Include="Resources\screen_loading_5.png" />
    <None Include="Resources\screen_loading_4.png" />
    <None Include="Resources\screen_loading_3.png" />
    <None Include="Resources\screen_loading_2.png" />
    <None Include="Resources\screen_loading_1.png" />
    <None Include="Resources\VideoOptimization8.png" />
    <None Include="Resources\VideoOptimization7.png" />
    <None Include="Resources\VideoOptimization6.png" />
    <None Include="Resources\VideoOptimization5.png" />
    <None Include="Resources\VideoOptimization4.png" />
    <None Include="Resources\VideoOptimization3.png" />
    <None Include="Resources\VideoOptimization2.png" />
    <None Include="Resources\VideoOptimization1.png" />
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="stdole">
      <Guid>{00020430-0000-0000-C000-000000000046}</Guid>
      <VersionMajor>2</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>