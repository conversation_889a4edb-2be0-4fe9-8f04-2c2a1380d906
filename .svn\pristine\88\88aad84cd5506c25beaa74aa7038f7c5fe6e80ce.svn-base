﻿using System.Drawing;

namespace OCRTools
{
    public struct HSB
    {
        private double _hue;
        private double _saturation;
        private double _brightness;
        private int _alpha;

        public double Hue
        {
            get => _hue;
            set => _hue = ColorHelper.ValidColor(value);
        }

        public double Hue360
        {
            get => _hue * 360;
            set => _hue = ColorHelper.ValidColor(value / 360);
        }

        public double Saturation
        {
            get => _saturation;
            set => _saturation = ColorHelper.ValidColor(value);
        }

        public double Saturation100
        {
            get => _saturation * 100;
            set => _saturation = ColorHelper.ValidColor(value / 100);
        }

        public double Brightness
        {
            get => _brightness;
            set => _brightness = ColorHelper.ValidColor(value);
        }

        public double Brightness100
        {
            get => _brightness * 100;
            set => _brightness = ColorHelper.ValidColor(value / 100);
        }

        public int Alpha
        {
            get => _alpha;
            set => _alpha = ColorHelper.ValidColor(value);
        }

        public HSB(double hue, double saturation, double brightness, int alpha = 255) : this()
        {
            Hue = hue;
            Saturation = saturation;
            Brightness = brightness;
            Alpha = alpha;
        }

        public HSB(int hue, int saturation, int brightness, int alpha = 255) : this()
        {
            Hue360 = hue;
            Saturation100 = saturation;
            Brightness100 = brightness;
            Alpha = alpha;
        }

        public HSB(Color color)
        {
            this = ColorHelper.ColorToHsb(color);
        }

        public static implicit operator HSB(Color color)
        {
            return ColorHelper.ColorToHsb(color);
        }

        public static implicit operator Color(HSB color)
        {
            return color.ToColor();
        }

        public static implicit operator RGBA(HSB color)
        {
            return color.ToColor();
        }

        public static implicit operator Cmyk(HSB color)
        {
            return color.ToColor();
        }

        public static bool operator ==(HSB left, HSB right)
        {
            return left.Hue == right.Hue && left.Saturation == right.Saturation && left.Brightness == right.Brightness;
        }

        public static bool operator !=(HSB left, HSB right)
        {
            return !(left == right);
        }

        public Color ToColor()
        {
            return ColorHelper.HSBToColor(this);
        }
    }
}