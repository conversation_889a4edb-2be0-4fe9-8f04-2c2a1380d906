﻿using OCRTools;
using OCRTools.Common;
using System;
using System.Collections.Specialized;
using System.IO;

namespace ImageLib
{
    /// <summary>
    /// </summary>
    public class ImageTopUpload
    {
        public static bool Enable { get; set; } = true;

        private const string strFileNameSpilt = "\"guid\":\"";

        public static byte[] GetZipResult(byte[] content, ref string strUrl)
        {
            var result = "";
            var url = "https://img.top/upload";
            var file = new UploadFileInfo()
            {
                Name = "image",
                Filename = "1.png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            var vaules = new NameValueCollection() {
                    { "guid", Guid.NewGuid().ToString() },
                    { "id", Guid.NewGuid().ToString() }
                };
            var strCookie = "Goimgsession=" + Guid.NewGuid().ToString() + "; Goimguuid=" + Guid.NewGuid().ToString();
            var headers = new NameValueCollection() {
                    { "Cookie",strCookie}
                };
            var html = string.Empty;
            try
            {
                html = UploadFileRequest.Post(url, new[] { file }, vaules, headers).Replace(" ", "");
            }
            catch { }
            if (html.Contains(strFileNameSpilt))
            {
                result = html.Substring(html.IndexOf(strFileNameSpilt) + strFileNameSpilt.Length);
                result = result.Substring(0, result.IndexOf("\"")).Replace("\\/", "/");
                if (!string.IsNullOrEmpty(result))
                {
                    result = string.Format("https://img.top/optimized/{0}", result);
                }
            }
            return CommonMethod.GetUrlBytes(result, strCookie);
        }
    }
}
