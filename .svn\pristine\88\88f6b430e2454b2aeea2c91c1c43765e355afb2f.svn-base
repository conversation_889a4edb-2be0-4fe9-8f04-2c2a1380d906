using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;

namespace OCRTools.Common
{
    /// <summary>
    /// 图像资源管理器 - 用于统一管理图像资源，避免多处引用同一图像资源时被错误释放
    /// </summary>
    public static class ImageResourceManager
    {
        // 图像资源引用计数字典
        private static readonly Dictionary<int, ImageResourceInfo> _imageResources = new Dictionary<int, ImageResourceInfo>();
        
        // 用于线程安全操作的锁对象
        private static readonly object _lockObj = new object();

        /// <summary>
        /// 获取一个图像的副本，并由资源管理器进行管理
        /// </summary>
        /// <param name="source">源图像</param>
        /// <returns>图像副本</returns>
        public static Image GetManagedImage(Image source)
        {
            if (source == null) return null;
            
            try
            {
                // 创建图像副本
                Image imageCopy = CreateImageCopy(source);
                
                // 如果创建失败则返回null
                if (imageCopy == null) return null;
                
                // 注册到资源管理器
                RegisterImage(imageCopy);
                
                return imageCopy;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GetManagedImage error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建图像副本
        /// </summary>
        private static Image CreateImageCopy(Image source)
        {
            if (source == null) return null;
            
            try
            {
                // 尝试使用Clone方法
                return (Image)source.Clone();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Clone image failed: {ex.Message}");
                
                try
                {
                    // 如果Clone失败，尝试使用Bitmap方式复制
                    return new Bitmap(source);
                }
                catch (Exception ex2)
                {
                    Console.WriteLine($"Create bitmap copy failed: {ex2.Message}");
                    return null;
                }
            }
        }

        /// <summary>
        /// 注册图像到资源管理器
        /// </summary>
        /// <param name="image">要注册的图像</param>
        public static void RegisterImage(Image image)
        {
            if (image == null) return;
            
            lock (_lockObj)
            {
                int hashCode = image.GetHashCode();
                
                if (_imageResources.ContainsKey(hashCode))
                {
                    // 如果已存在，增加引用计数
                    _imageResources[hashCode].ReferenceCount++;
                }
                else
                {
                    // 如果不存在，添加新条目
                    _imageResources.Add(hashCode, new ImageResourceInfo
                    {
                        Image = image,
                        ReferenceCount = 1
                    });
                }
            }
        }

        /// <summary>
        /// 释放对图像的引用
        /// </summary>
        /// <param name="image">要释放的图像</param>
        /// <param name="forceDispose">是否强制释放（无论引用计数）</param>
        public static void ReleaseImage(Image image, bool forceDispose = false)
        {
            if (image == null) return;
            
            lock (_lockObj)
            {
                int hashCode = image.GetHashCode();
                
                if (_imageResources.ContainsKey(hashCode))
                {
                    var info = _imageResources[hashCode];
                    
                    // 减少引用计数
                    info.ReferenceCount--;
                    
                    // 如果引用计数为0或强制释放，则真正释放资源
                    if (info.ReferenceCount <= 0 || forceDispose)
                    {
                        try
                        {
                            // 移除引用
                            _imageResources.Remove(hashCode);
                            
                            // 释放资源
                            info.Image.Dispose();
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error disposing image: {ex.Message}");
                        }
                    }
                }
                else
                {
                    // 如果管理器中没有该图像的记录，则直接释放
                    try
                    {
                        image.Dispose();
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error disposing unmanaged image: {ex.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// 清理所有未使用的图像资源
        /// </summary>
        public static void CleanupUnusedResources()
        {
            lock (_lockObj)
            {
                List<int> keysToRemove = new List<int>();
                
                // 找出所有引用计数为0的资源
                foreach (var pair in _imageResources.Where(p => p.Value.ReferenceCount <= 0))
                {
                    keysToRemove.Add(pair.Key);
                    
                    try
                    {
                        // 释放资源
                        pair.Value.Image.Dispose();
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error disposing unused image: {ex.Message}");
                    }
                }
                
                // 从字典中移除
                foreach (int key in keysToRemove)
                {
                    _imageResources.Remove(key);
                }
            }
        }

        /// <summary>
        /// 获取当前资源管理器中的图像数量
        /// </summary>
        public static int GetImageCount()
        {
            lock (_lockObj)
            {
                return _imageResources.Count;
            }
        }
        
        /// <summary>
        /// 打印当前资源管理情况（调试用）
        /// </summary>
        public static string GetResourceInfo()
        {
            lock (_lockObj)
            {
                string info = $"Total managed images: {_imageResources.Count}\n";
                foreach (var pair in _imageResources)
                {
                    info += $"Image {pair.Key}: {pair.Value.ReferenceCount} references, Size: {pair.Value.Image.Width}x{pair.Value.Image.Height}\n";
                }
                return info;
            }
        }
    }

    /// <summary>
    /// 图像资源信息
    /// </summary>
    internal class ImageResourceInfo
    {
        /// <summary>
        /// 图像对象
        /// </summary>
        public Image Image { get; set; }
        
        /// <summary>
        /// 引用计数
        /// </summary>
        public int ReferenceCount { get; set; }
    }
} 