﻿using MetroFramework.Forms;
using System;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Threading;
using System.Windows.Forms;

#pragma warning disable 1570

namespace OCRTools
{
    public partial class FormUpdate : MetroForm
    {
        private bool _isCancel;

        public FormUpdate()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            //不执行线程检查
            CheckForIllegalCrossThreadCalls = false;
        }

        internal UpdateEntity UpdateInfo { get; set; }

        private void FormUpdate_Load(object sender, EventArgs e)
        {
            Text = Application.ProductName + "更新";
            if (UpdateInfo == null) return;
            btnOK.Enabled = true;
            lblNew.Text = UpdateInfo.strNewVersion;
            lblDate.Text = UpdateInfo.dtNewDate.ToString("yyyy-MM-dd");
            rtbCon.Text = UpdateInfo.strContext;
            Opacity = 1;
            if (UpdateInfo.IsForceUpdate)
            {
                Text = Application.ProductName + "【强制更新】";
                btnOK_Click(sender, null);
            }
        }

        private bool _isOpenDownLoad;
        readonly string _tmpCacheFile = Path.GetTempFileName();

        private void bgUpdate_DoWork(object sender, DoWorkEventArgs e)
        {
            if (UpdateInfo == null) return;
            try
            {
                _isOpenDownLoad = false;
                try
                {
                    using (Stream so = new FileStream(_tmpCacheFile, FileMode.Create))
                    {
                    }
                }
                catch
                {
                    _isOpenDownLoad = true;
                    CommonMethod.ShowHelpMsg("无法创建更新用的临时文件，开始尝试以管理员方式更新！");
                }

                if (_isOpenDownLoad)
                {
                    proProcess.Value = proProcess.Maximum;
                }
                else
                {
                    DownloadFile(UpdateInfo.strURL, _tmpCacheFile, proProcess, lblProcess);
                }
                tmrTick.Enabled = false;
                lblLoading.Visible = false;
                if (proProcess.Value == proProcess.Maximum)
                {
                    lblDown.Text = "下载完成！\n点击安装开始更新！";
                    btnOK.Text = "开始安装(&O)";
                    btnOK.Tag = "open";
                }
                else
                {
                    lblDown.Text = "点击更新重试！";
                    btnOK.Enabled = true;
                    btnOK.Text = "立即更新(&O)";
                    btnOK.Tag = "down";
                }

                bgUpdate.CancelAsync();
            }
            catch
            {
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (_isCancel)
                return;
            var tagnow = btnOK.Tag.ToString();
            if (tagnow == "down")
            {
                bgUpdate.RunWorkerAsync();
                btnOK.Enabled = false;
                btnOK.Text = "正在下载";
                lblNowVersion.Visible = false;
                lblNewDate.Visible = false;
                lblDate.Visible = false;
                lblNew.Visible = false;
                rtbCon.Visible = false;
                lblDown.Visible = true;
                lblLoading.Visible = true;
                tmrTick.Enabled = true;
                lblProcess.Visible = true;
                proProcess.Visible = true;
            }
            else if (tagnow == "open")
            {
                btnOK.Enabled = false;
                var paramStr = string.Format("\"{0}\" \"{1}\" \"{2}\""
                    , Application.ExecutablePath
                    , _tmpCacheFile
                    , _isOpenDownLoad ? UpdateInfo.strURL : string.Empty
                    );
                var updateFileName = CommonString.StrUpdateFile();
                try
                {
                    CommonString.RunAsAdmin(updateFileName, paramStr);
                }
                catch
                {
                    MessageBox.Show(this, "无法以管理员方式启动更新程序，可能会更新失败，请知晓！\n正在尝试以非管理员方式启动更新程序……", "提示", MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
                    CommonString.RunAsAdmin(updateFileName, paramStr, false);
                }
                finally
                {
                    CommonMethod.Exit();
                }
            }
        }

        private void tmrTick_Tick(object sender, EventArgs e)
        {
            if (lblLoading.Text == "loading...")
                lblLoading.Text = "loading.";
            else if (lblLoading.Text == "loading.")
                lblLoading.Text = "loading..";
            else if (lblLoading.Text == "loading..") lblLoading.Text = "loading...";
        }

        /// <summary>
        ///     c#,.net 下载文件
        /// </summary>
        /// <param name="url">下载文件地址</param>
        /// <param name="filename"></param>
        /// <param name="prog"></param>
        /// <param name="label1"></param>
        public void DownloadFile(string url, string filename, ProgressBar prog, Label label1)
        {
            try
            {
                var client = new WebClient() { Proxy = null };
                client.DownloadProgressChanged += (sender, e) =>
                {
                    if (prog != null)
                        prog.Value = e.ProgressPercentage;
                    if (label1 != null)
                        label1.Text = "已下载" + e.ProgressPercentage + "%";
                    Application.DoEvents();
                };
                client.DownloadFileAsync(new Uri(url), filename);
                while (client.IsBusy)
                {
                    Thread.Sleep(1000);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
        }

        private void FormUpdate_FormClosing(object sender, FormClosingEventArgs e)
        {
            _isCancel = true;
            bgUpdate.CancelAsync();
            if (UpdateInfo.IsForceUpdate)
                CommonMethod.Exit();
        }

        private void lnkNoUpdate_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            var url = string.IsNullOrEmpty(UpdateInfo.strFullURL) ? UpdateInfo.strURL : UpdateInfo.strFullURL;
            if (!string.IsNullOrEmpty(url))
            {
                try
                {
                    ClipboardService.SetText(url);
                }
                catch { }
                MessageBox.Show(this, "已复制下载地址到粘贴板！\n助手将尝试自动打开更新网址…\n如果一直没有弹出下载框，请手动粘贴网址到浏览器重试！", "手动更新提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                Process.Start(url);
            }
        }

        private void bgUpdate_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            if (!btnOK.Enabled)
            {
                proProcess.Value = 100;
                Application.DoEvents();
                btnOK_Click(sender, null);
            }
        }
    }
}