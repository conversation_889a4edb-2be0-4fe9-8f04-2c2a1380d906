using OCRTools.Common;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace OCRTools
{
    public class SelectRectangleList
    {
        public IntPtr IgnoreHandle { get; set; }
        public bool IncludeChildWindows { get; set; }

        private List<WindowInfo> windows;
        private HashSet<IntPtr> parentHandles;

        public void SetWindowZOrder(ConcurrentBag<WindowInfo> lstTmp)
        {
            if (!lstTmp.Any(p => p.IsWindow)) return;
            Parallel.ForEach(lstTmp.Where(p => p.IsWindow), new ParallelOptions { MaxDegreeOfParallelism = 5 },
                p => { p.ZIndex = GetWindowZOrder(p.Handle); });
            Parallel.ForEach(lstTmp.Where(p => !p.IsWindow), new ParallelOptions { MaxDegreeOfParallelism = 5 },
                p =>
                {
                    var parent = lstTmp.Where(q => q.IsWindow
                                                   && (Equals(q.<PERSON>, p.Pa<PERSON>) || Equals(q.<PERSON><PERSON><PERSON>, p.Pa<PERSON>and<PERSON>))).FirstOrDefault();
                    if (parent != null)
                        p.ZIndex = parent.ZIndex;
                });
        }

        private int GetWindowZOrder(IntPtr hWnd)
        {
            var zOrder = -1;
            while ((hWnd = NativeMethods.GetWindow(hWnd, 2 /* GW_HWNDNEXT */)) != IntPtr.Zero) zOrder++;
            return zOrder;
        }

        public ConcurrentBag<WindowInfo> GetWindowInfoListAsync(int timeout)
        {
            ConcurrentBag<WindowInfo> windowInfoList = null;

            Thread t = new Thread(() =>
            {
                try
                {
                    windowInfoList = GetWindowInfoList();
                }
                catch
                {
                }
            });

            t.Start();

            if (!t.Join(timeout))
            {
                t.Abort();
            }

            return windowInfoList;
        }

        public ConcurrentBag<WindowInfo> GetWindowInfoList()
        {
            windows = new List<WindowInfo>();
            parentHandles = new HashSet<IntPtr>();

            NativeMethods.EnumWindowsProc ewp = EvalWindow;
            NativeMethods.EnumWindows(ewp, IntPtr.Zero);

            ConcurrentBag<WindowInfo> result = new ConcurrentBag<WindowInfo>();
            windows.ForEach(p => {
                p.Scal();
            });
            foreach (WindowInfo window in windows.Where(p => p.IsWindow))
            {
                if (!result.Any(q => q.Rectangle.Equals(window.Rectangle)))
                {
                    result.Add(window);
                }
            }
            foreach (WindowInfo window in windows.Where(p => !p.IsWindow))
            {
                if (!result.Any(q => q.Rectangle.Equals(window.Rectangle)))
                {
                    result.Add(window);
                }
            }
            return result;
        }

        private void AddWindowInfo(List<WindowInfo> lstT, WindowInfo wd)
        {
            if (!lstT.Any(q => q.Rectangle.Equals(wd.Rectangle)))
            {
                lstT.Add(wd);
            }
        }

        private bool EvalWindow(IntPtr handle, IntPtr lParam)
        {
            if (handle == IgnoreHandle || !NativeMethods.IsWindowVisible(handle) || NativeMethods.IsWindowCloaked(handle))
            {
                return true;
            }

            var windowInfo = new WindowInfo(handle) { IsWindow = true };

            if (!windowInfo.GetRectangle().IsValid())
            {
                return true;
            }

            if (IncludeChildWindows && !parentHandles.Contains(handle))
            {
                parentHandles.Add(handle);

                NativeMethods.EnumWindowsProc ewp = EvalControl;
                NativeMethods.EnumChildWindows(handle, ewp, handle);
            }

            Rectangle clientRect = NativeMethods.GetClientRect(handle);

            if (clientRect.IsValid() && clientRect != windowInfo.Rectangle)
            {
                var newWindow = new WindowInfo(handle, clientRect) { ParentHandle = handle, IsWindow = true };
                AddWindowInfo(windows, newWindow);
            }

            AddWindowInfo(windows, windowInfo);

            return true;
        }

        private bool EvalControl(IntPtr handle, IntPtr lParam)
        {
            if (!NativeMethods.IsWindowVisible(handle)) return true;
            var windowInfo = new WindowInfo(handle) { IsWindow = false, ParentHandle = lParam, IsSmallControl = true };
            if (!windowInfo.GetRectangle().IsValid()) return true;
            if (!parentHandles.Contains(handle))
            {
                parentHandles.Add(handle);
                NativeMethods.EnumWindowsProc lpEnumFunc = EvalControl;
                NativeMethods.EnumChildWindows(handle, lpEnumFunc, lParam);
            }

            AddWindowInfo(windows, windowInfo);
            return true;
        }
    }
}