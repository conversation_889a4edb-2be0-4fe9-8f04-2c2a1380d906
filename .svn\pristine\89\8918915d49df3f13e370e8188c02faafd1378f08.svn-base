﻿using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace OCRTools.UserControlEx
{
	[ToolboxItem(false)]
	public class TabStyleVS2010Provider : TabStyleRoundedProvider
	{
		public TabStyleVS2010Provider(TabControlExtra tabControl)
			: base(tabControl)
		{
			base.Radius = 3;
			base.ShowTabCloser = true;
			base.CloserColorFocused = Color.FromArgb(117, 99, 61);
			base.CloserColorFocusedActive = Color.Black;
			base.CloserColorSelected = Color.FromArgb(95, 102, 115);
			base.CloserColorSelectedActive = Color.Black;
			base.CloserColorHighlighted = Color.FromArgb(206, 212, 221);
			base.CloserColorHighlightedActive = Color.Black;
			base.CloserColorUnselected = Color.Empty;
			base.CloserButtonFillColorFocused = Color.Empty;
			base.CloserButtonFillColorFocusedActive = Color.White;
			base.CloserButtonFillColorSelected = Color.Empty;
			base.CloserButtonFillColorSelectedActive = Color.White;
			base.CloserButtonFillColorHighlighted = Color.Empty;
			base.CloserButtonFillColorHighlightedActive = Color.White;
			base.CloserButtonFillColorUnselected = Color.Empty;
			base.CloserButtonOutlineColorFocused = Color.Empty;
			base.CloserButtonOutlineColorFocusedActive = Color.FromArgb(229, 195, 101);
			base.CloserButtonOutlineColorSelected = Color.Empty;
			base.CloserButtonOutlineColorSelectedActive = Color.FromArgb(229, 195, 101);
			base.CloserButtonOutlineColorHighlighted = Color.Empty;
			base.CloserButtonOutlineColorHighlightedActive = Color.FromArgb(229, 195, 101);
			base.CloserButtonOutlineColorUnselected = Color.Empty;
			base.TextColorUnselected = Color.White;
			base.TextColorDisabled = Color.WhiteSmoke;
			base.BorderColorDisabled = Color.FromArgb(41, 57, 85);
			base.BorderColorFocused = Color.FromArgb(255, 243, 205);
			base.BorderColorHighlighted = Color.FromArgb(155, 167, 183);
			base.BorderColorSelected = Color.FromArgb(206, 212, 223);
			base.BorderColorUnselected = Color.Transparent;
			base.PageBackgroundColorDisabled = Color.FromArgb(41, 57, 85);
			base.PageBackgroundColorFocused = Color.FromArgb(229, 195, 101);
			base.PageBackgroundColorHighlighted = Color.FromArgb(75, 92, 116);
			base.PageBackgroundColorSelected = Color.FromArgb(206, 212, 223);
			base.PageBackgroundColorUnselected = Color.Transparent;
			base.TabColorDisabled1 = base.PageBackgroundColorDisabled;
			base.TabColorDisabled2 = base.TabColorDisabled1;
			base.TabColorFocused1 = base.PageBackgroundColorFocused;
			base.TabColorFocused2 = SystemColors.Window;
			base.TabColorHighLighted1 = base.PageBackgroundColorHighlighted;
			base.TabColorHighLighted2 = base.TabColorHighLighted1;
			base.TabColorSelected1 = base.PageBackgroundColorSelected;
			base.TabColorSelected2 = base.TabColorSelected1;
			base.TabColorUnSelected1 = Color.Transparent;
			base.TabColorUnSelected1 = Color.Transparent;
			base.Padding = new Point(6, 5);
			base.TabPageMargin = new Padding(0, 4, 0, 4);
			base.TabPageRadius = 2;
		}
	}

	[ToolboxItem(false)]
	public class TabStyleVS2012Provider : TabStyleRoundedProvider
	{
		public TabStyleVS2012Provider(TabControlExtra tabControl)
			: base(tabControl)
		{
			base.Radius = 3;
			base.ShowTabCloser = true;
			base.CloserColorFocused = Color.FromArgb(117, 99, 61);
			base.CloserColorFocusedActive = Color.Black;
			base.CloserColorSelected = Color.FromArgb(95, 102, 115);
			base.CloserColorSelectedActive = Color.Black;
			base.CloserColorHighlighted = Color.FromArgb(206, 212, 221);
			base.CloserColorHighlightedActive = Color.Black;
			base.CloserColorUnselected = Color.Empty;
			base.CloserButtonFillColorFocused = Color.Empty;
			base.CloserButtonFillColorFocusedActive = Color.White;
			base.CloserButtonFillColorSelected = Color.Empty;
			base.CloserButtonFillColorSelectedActive = Color.White;
			base.CloserButtonFillColorHighlighted = Color.Empty;
			base.CloserButtonFillColorHighlightedActive = Color.White;
			base.CloserButtonFillColorUnselected = Color.Empty;
			base.CloserButtonOutlineColorFocused = Color.Empty;
			base.CloserButtonOutlineColorFocusedActive = Color.FromArgb(229, 195, 101);
			base.CloserButtonOutlineColorSelected = Color.Empty;
			base.CloserButtonOutlineColorSelectedActive = Color.FromArgb(229, 195, 101);
			base.CloserButtonOutlineColorHighlighted = Color.Empty;
			base.CloserButtonOutlineColorHighlightedActive = Color.FromArgb(229, 195, 101);
			base.CloserButtonOutlineColorUnselected = Color.Empty;
			base.TextColorUnselected = Color.White;
			base.TextColorDisabled = Color.WhiteSmoke;
			base.BorderColorDisabled = Color.FromArgb(41, 57, 85);
			base.BorderColorFocused = Color.FromArgb(255, 243, 205);
			base.BorderColorHighlighted = Color.FromArgb(155, 167, 183);
			base.BorderColorSelected = Color.FromArgb(206, 212, 223);
			base.BorderColorUnselected = Color.Transparent;
			base.PageBackgroundColorDisabled = Color.FromArgb(41, 57, 85);
			base.PageBackgroundColorFocused = Color.FromArgb(255, 243, 205);
			base.PageBackgroundColorHighlighted = Color.FromArgb(75, 92, 116);
			base.PageBackgroundColorSelected = Color.FromArgb(206, 212, 223);
			base.PageBackgroundColorUnselected = Color.Transparent;
			base.TabColorDisabled1 = base.PageBackgroundColorDisabled;
			base.TabColorDisabled2 = base.TabColorDisabled1;
			base.TabColorFocused1 = base.PageBackgroundColorFocused;
			base.TabColorFocused2 = base.TabColorFocused1;
			base.TabColorHighLighted1 = base.PageBackgroundColorHighlighted;
			base.TabColorHighLighted2 = base.TabColorHighLighted1;
			base.TabColorSelected1 = base.PageBackgroundColorSelected;
			base.TabColorSelected2 = base.TabColorSelected1;
			base.TabColorUnSelected1 = Color.Transparent;
			base.TabColorUnSelected1 = Color.Transparent;
			base.Padding = new Point(6, 5);
			base.TabPageMargin = new Padding(0, 4, 0, 4);
			base.TabPageRadius = 2;
		}
	}

	[ToolboxItem(false)]
	public class TabStyleVisualStudioProvider : TabStyleProvider
	{
		public TabStyleVisualStudioProvider(TabControlExtra tabControl)
			: base(tabControl)
		{
			base.ImageAlign = ContentAlignment.MiddleRight;
			base.Overlap = 7;
			base.Padding = new Point(16, 1);
		}

		public override void AddTabBorder(GraphicsPath path, Rectangle tabBounds)
		{
			switch (base.TabControl.Alignment)
			{
				case TabAlignment.Top:
					path.AddLine(tabBounds.X, tabBounds.Bottom, tabBounds.X + tabBounds.Height - 4, tabBounds.Y + 2);
					path.AddLine(tabBounds.X + tabBounds.Height, tabBounds.Y, tabBounds.Right - 3, tabBounds.Y);
					path.AddArc(tabBounds.Right - 6, tabBounds.Y, 6, 6, 270f, 90f);
					path.AddLine(tabBounds.Right, tabBounds.Y + 3, tabBounds.Right, tabBounds.Bottom);
					break;
				case TabAlignment.Bottom:
					path.AddLine(tabBounds.Right, tabBounds.Y, tabBounds.Right, tabBounds.Bottom - 3);
					path.AddArc(tabBounds.Right - 6, tabBounds.Bottom - 6, 6, 6, 0f, 90f);
					path.AddLine(tabBounds.Right - 3, tabBounds.Bottom, tabBounds.X + tabBounds.Height, tabBounds.Bottom);
					path.AddLine(tabBounds.X + tabBounds.Height - 4, tabBounds.Bottom - 2, tabBounds.X, tabBounds.Y);
					break;
				case TabAlignment.Left:
					path.AddLine(tabBounds.Right, tabBounds.Bottom, tabBounds.X + 3, tabBounds.Bottom);
					path.AddArc(tabBounds.X, tabBounds.Bottom - 6, 6, 6, 90f, 90f);
					path.AddLine(tabBounds.X, tabBounds.Bottom - 3, tabBounds.X, tabBounds.Y + tabBounds.Width);
					path.AddLine(tabBounds.X + 2, tabBounds.Y + tabBounds.Width - 4, tabBounds.Right, tabBounds.Y);
					break;
				case TabAlignment.Right:
					path.AddLine(tabBounds.X, tabBounds.Y, tabBounds.Right - 2, tabBounds.Y + tabBounds.Width - 4);
					path.AddLine(tabBounds.Right, tabBounds.Y + tabBounds.Width, tabBounds.Right, tabBounds.Bottom - 3);
					path.AddArc(tabBounds.Right - 6, tabBounds.Bottom - 6, 6, 6, 0f, 90f);
					path.AddLine(tabBounds.Right - 3, tabBounds.Bottom, tabBounds.X, tabBounds.Bottom);
					break;
			}
		}
	}

}
