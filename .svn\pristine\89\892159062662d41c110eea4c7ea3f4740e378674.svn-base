﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using OCRTools.Properties;

namespace OCRTools
{
    internal class CommonUser
    {
        private static List<UserType> LstUserType { get; set; }

        public static Bitmap GetUserLevelImage(int userLevel)
        {
            Bitmap image;
            try
            {
                image = CommonMethod.GetBitmapFromResource("vip_" + userLevel);
            }
            catch (Exception e)
            {
                image = Resources.vip_0;
            }

            return image;
        }

        public static UserTypeInfo GetNextTypeStr()
        {
            if (LstUserType == null || LstUserType.Count <= 0)
            {
                LstUserType = OcrHelper.GetCanRegUserTypes();
            }
            var nNextType = Math.Max(Program.NowUser?.UserType ?? 0, 0) + 1;
            var nextUserType = LstUserType.FirstOrDefault(p => Equals(p.Type, nNextType));
            return new UserTypeInfo
            {
                Name = nextUserType == null ? "敬请期待" : "升级到" + nextUserType.Name,
                Code = nextUserType?.Type ?? 0
            };
        }
    }
}
