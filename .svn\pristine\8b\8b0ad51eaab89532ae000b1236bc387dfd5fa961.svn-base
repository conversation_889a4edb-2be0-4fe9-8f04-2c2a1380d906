using System.IO;

namespace ExcelLibrary.BinaryDrawingFormat
{
	public class MsofbtSpgr : EscherRecord
	{
		public int Left;

		public int Top;

		public int Right;

		public int Bottom;

		public MsofbtSpgr(<PERSON>scherRecord record)
			: base(record)
		{
		}

		public MsofbtSpgr()
		{
			Type = 61449;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			Left = binaryReader.ReadInt32();
			Top = binaryReader.ReadInt32();
			Right = binaryReader.ReadInt32();
			Bottom = binaryReader.ReadInt32();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(Left);
			binaryWriter.Write(Top);
			binaryWriter.Write(Right);
			binaryWriter.Write(Bottom);
			Data = memoryStream.ToArray();
			Size = (uint)Data.Length;
			base.Encode();
		}
	}
}
