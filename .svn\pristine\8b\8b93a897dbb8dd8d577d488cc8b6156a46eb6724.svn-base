﻿using System;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Windows.Forms;

namespace OCRTools
{
    public class EnumInfo
    {
        public EnumInfo(Enum value)
        {
            Value = value;
            Category = value.GetValue<MenuCategoryAttribute>();
            Description = value.GetValue<MenuDescriptionAttribute>();
            ImageValue = value.GetValue<MenuImageAttribute>();
            TipText = value.GetValue<MenuTipTextAttribute>();
        }

        public Enum Value { get; set; }

        /// <summary>
        /// 类别
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 图标
        /// </summary>
        public string ImageValue { get; set; }

        /// <summary>
        /// 提示信息
        /// </summary>
        public string TipText { get; set; }
    }

    public enum CaptureActions
    {
        [MenuCategory("贴图")]
        [MenuDescription("快速贴图")]
        [MenuImage("贴图编辑")]
        [MenuTipText("快速截图(不带编辑)+贴图")]
        快速贴图,

        [MenuCategory("贴图")]
        [MenuDescription("截图贴图")]
        [MenuImage("贴图")]
        [MenuTipText("截图编辑+贴图")]
        截图贴图,


        [MenuCategory("贴图")]
        [MenuDescription("粘贴贴图")]
        [MenuImage("粘贴贴图")]
        [MenuTipText("从粘贴板获取图片+贴图")]
        粘贴贴图,

        [MenuCategory("贴图")]
        [MenuDescription("显隐贴图")]
        [MenuImage("显示隐藏贴图")]
        [MenuTipText("显示隐藏当前所有的贴图")]
        显隐贴图,

        [MenuCategory("贴图")]
        [MenuDescription("贴图配置")]
        [MenuImage("设置")]
        [MenuTipText("打开贴图配置")]
        贴图配置,

        [MenuCategory("贴图")]
        [MenuDescription("快捷键配置")]
        [MenuImage("keyboard_enter")]
        [MenuTipText("设置快捷键")]
        贴图快捷键配置,



        [MenuCategory("截图")]
        [MenuDescription("快速截图")]
        [MenuImage("快速截图")]
        [MenuTipText("快速截图(不带编辑)")]
        快速截图,

        [MenuCategory("截图")]
        [MenuDescription("截图编辑")]
        [MenuImage("截图")]
        [MenuTipText("截图(带编辑)")]
        截图编辑,

        [MenuCategory("截图")]
        [MenuDescription("上次区域截图")]
        [MenuImage("上次区域")]
        [MenuTipText("使用上次区域截图(不带编辑)")]
        上次区域截图,

        [MenuCategory("截图")]
        [MenuDescription("活动窗口")]
        [MenuImage("窗口")]
        [MenuTipText("当前活动的窗口截图")]
        活动窗口,

        [MenuCategory("截图")]
        [MenuDescription("活动显示器")]
        [MenuImage("显示器")]
        [MenuTipText("当前活动的显示器截图")]
        活动显示器,

        [MenuCategory("截图")]
        [MenuDescription("全屏截图")]
        [MenuImage("全屏截图")]
        [MenuTipText("所有显示器截图")]
        全屏截图,

        [MenuCategory("截图")]
        [MenuDescription("固定区域截图")]
        [MenuImage("固定区域")]
        [MenuTipText("自定义区域截图")]
        固定区域截图,

        [MenuCategory("截图")]
        [MenuDescription("滚动截图")]
        [MenuImage("滚动截屏")]
        [MenuTipText("滚动截图")]
        滚动截屏,

        [MenuCategory("截图")]
        [MenuDescription("延时截图")]
        [MenuImage("延时")]
        [MenuTipText("延迟指定时间后截图")]
        延时截图,

        [MenuCategory("截图")]
        [MenuDescription("打开截图文件夹")]
        [MenuImage("图片文件夹")]
        [MenuTipText("打开截图文件夹")]
        打开截图文件夹,

        [MenuCategory("截图")]
        [MenuDescription("截屏配置")]
        [MenuImage("设置")]
        [MenuTipText("打开截屏配置")]
        截屏配置,

        [MenuCategory("截图")]
        [MenuDescription("滚动截屏配置")]
        [MenuImage("滚动截屏")]
        [MenuTipText("打开滚动截屏配置")]
        滚动截屏配置,

        [MenuCategory("截图")]
        [MenuDescription("快捷键配置")]
        [MenuImage("keyboard_enter")]
        [MenuTipText("设置快捷键")]
        截屏快捷键配置
    }

    public class CommonEnumAction<T>
    {
        public static ToolStripMenuItem FindMeunItem(ToolStripDropDown parent, T act)
        {
            var info = ((T[])Enum.GetValues(typeof(T))).OfType<Enum>().Select(x => new EnumInfo(x))
                .FirstOrDefault(p => Equals(p.Value.GetHashCode(), act.GetHashCode()));
            return FindMeunItem(parent, info);
        }

        public static ToolStripMenuItem FindMeunItem(ToolStripDropDown parent, EnumInfo info)
        {
            var tsmResult = parent.Items.OfType<ToolStripMenuItem>().FirstOrDefault(x => x.Tag != null && !(x.Tag is int) &&
                Equals(info.Value.GetHashCode(), ((EnumInfo)x.Tag)?.Value.GetHashCode()));
            if (tsmResult == null)
                foreach (ToolStripItem item in parent.Items)
                    if (item is ToolStripMenuItem tsm)
                    {
                        try
                        {
                            tsmResult = tsm.DropDownItems.OfType<ToolStripMenuItem>().FirstOrDefault(x => x.Tag != null && !(x.Tag is int) &&
                                Equals(info.Value.GetHashCode(), ((EnumInfo)x.Tag)?.Value.GetHashCode()));
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe.Message);
                        }
                        if (tsmResult != null) break;
                    }

            return tsmResult;
        }

        public static ToolStripMenuItem FindMeunItem(ToolStripDropDown parent, string text)
        {
            var tsmResult = parent.Items.OfType<ToolStripMenuItem>().FirstOrDefault(x => x.Text.Contains(text));
            if (tsmResult == null)
                foreach (ToolStripItem item in parent.Items)
                    if (item is ToolStripMenuItem tsm)
                    {
                        tsmResult = FindMenuItemByChild(tsm, text);
                        if (tsmResult != null) break;
                    }

            return tsmResult;
        }

        private static ToolStripMenuItem FindMenuItemByChild(ToolStripMenuItem parent, string text)
        {
            var lstItem = parent.DropDownItems.OfType<ToolStripMenuItem>();
            var tsmResult = lstItem.FirstOrDefault(x => x.Text.Contains(text));
            if (tsmResult != null)
                return tsmResult;
            foreach (var item in lstItem)
            {
                tsmResult = item.DropDownItems.OfType<ToolStripMenuItem>().FirstOrDefault(x => x.Text.Contains(text));
                if (tsmResult != null)
                    return tsmResult;
            }
            return null;
        }

        public static void AddEnumItemsContextMenu(ToolStripDropDown parent, EventHandler clickAction)
        {
            var enums = ((T[])Enum.GetValues(typeof(T))).OfType<Enum>().Select(x => new EnumInfo(x)).ToArray();

            foreach (var enumInfo in enums)
            {
                var img = FindMenuIcon(enumInfo);
                var tsmItem = new ToolStripMenuItem(enumInfo.Description)
                {
                    Image = img,
                    Tag = enumInfo,
                    AccessibleDescription = enumInfo.Description,
                    AccessibleDefaultActionDescription = enumInfo.ImageValue,
                    //Name = "cms" + enumInfo.Category + "_" + enumInfo.Description
                    //ToolTipText = enumInfo.TipText,
                };
                tsmItem.Click += clickAction;

                if (!string.IsNullOrEmpty(enumInfo.Category))
                {
                    var tsmParent = parent.Items.OfType<ToolStripMenuItem>()
                        .FirstOrDefault(x => x.Text == enumInfo.Category);

                    if (tsmParent == null)
                    {
                        tsmParent = new ToolStripMenuItem(enumInfo.Category);
                        parent.Items.Add(tsmParent);
                    }
                    else
                    {
                        tsmItem.Font = parent.Font;
                    }

                    tsmParent.DropDownItems.Add(tsmItem);
                }
                else
                {
                    parent.Items.Add(tsmItem);
                }
            }
        }

        public static Image FindMenuIcon(EnumInfo value)
        {
            if (!string.IsNullOrEmpty(value.ImageValue))
                return Common.ImageProcessHelper.GetResourceImage(value.ImageValue);
            return null;
        }
    }

    public static class EnumExtensions
    {
        public static string GetValue<T>(this Enum obj, string propertyName = "Value") where T : Attribute
        {
            var fi = obj.GetType().GetField(obj.ToString());

            if (fi != null)
            {
                if (fi.GetCustomAttributes(typeof(T), true).FirstOrDefault() is T attribute)
                {
                    return typeof(T).GetProperty(propertyName)?.GetValue(attribute)?.ToString();
                }
                else if (fi.GetCustomAttributes(typeof(T), false).FirstOrDefault() is T attrib)
                {
                    return typeof(T).GetProperty(propertyName)?.GetValue(attrib)?.ToString();
                }
            }
            return null;
        }

        public static int GetIndex(this Enum value)
        {
            return Array.IndexOf(Enum.GetValues(value.GetType()), value);
        }
    }

    public class MenuDescriptionAttribute : Attribute
    {
        public MenuDescriptionAttribute(string value)
        {
            Value = value;
        }

        [Obfuscation]
        public string Value { get; set; }
    }
    public class MenuCategoryAttribute : Attribute
    {
        public MenuCategoryAttribute(string value)
        {
            Value = value;
        }

        [Obfuscation]
        public string Value { get; set; }
    }
    public class MenuImageAttribute : Attribute
    {
        public MenuImageAttribute(string value)
        {
            Value = value;
        }

        [Obfuscation]
        public string Value { get; set; }
    }
    public class MenuTipTextAttribute : Attribute
    {
        public MenuTipTextAttribute(string value)
        {
            Value = value;
        }

        [Obfuscation]
        public string Value { get; set; }
    }
}