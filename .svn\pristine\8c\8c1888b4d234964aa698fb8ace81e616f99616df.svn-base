using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class TransformPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = TransformPatternIdentifiers.Pattern;

        protected TransformPattern(AutomationElement el, IUIAutomationTransformPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        public static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new TransformPattern(el, (IUIAutomationTransformPattern)pattern, cached);
        }
    }

    public class TransformPattern2 : TransformPattern
    {
        public new static readonly AutomationPattern Pattern = TransformPattern2Identifiers.Pattern;

        private TransformPattern2(AutomationElement el, IUIAutomationTransformPattern2 pattern2,
            IUIAutomationTransformPattern pattern, bool cached)
            : base(el, pattern, cached)
        {
            Debug.Assert(pattern2 != null);
        }

        internal new static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            TransformPattern2 result = null;
            if (pattern != null)
            {
                var basePattern =
                    (IUIAutomationTransformPattern)el.GetRawPattern(TransformPattern.Pattern, cached);
                if (basePattern != null)
                    result = new TransformPattern2(el, (IUIAutomationTransformPattern2)pattern,
                        basePattern, cached);
            }

            return result;
        }
    }
}