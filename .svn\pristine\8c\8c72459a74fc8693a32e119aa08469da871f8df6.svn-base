﻿/**
 * MetroFramework - Modern UI for WinForms
 * 
 * The MIT License (MIT)
 * Copyright (c) 2011 <PERSON>, http://github.com/viperneo
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of 
 * this software and associated documentation files (the "Software"), to deal in the 
 * Software without restriction, including without limitation the rights to use, copy, 
 * modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, 
 * and to permit persons to whom the Software is furnished to do so, subject to the 
 * following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in 
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
 * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT 
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF 
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE 
 * OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
using MetroFramework.Controls;
using MetroFramework.Interfaces;
using System;
using System.ComponentModel;
using System.Reflection;
using System.Windows.Forms;

namespace MetroFramework.Components
{
    [Designer(typeof(Design.MetroStyleManagerDesigner), typeof(System.Windows.Forms.Design.ParentControlDesigner))]
    public sealed class MetroStyleManager : Component, ICloneable, ISupportInitialize
    {
        public event EventHandler<EventArgs> ThemeChange;

        public event EventHandler<EventArgs> StyleChange;

        private readonly IContainer parentContainer;

        private MetroColorStyle metroStyle = MetroColorStyle.Blue;

        private MetroThemeStyle metroTheme = MetroThemeStyle.Light;

        private ContainerControl owner;

        private bool isInitializing;

        [DefaultValue(MetroColorStyle.Blue)]
        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                return metroStyle;
            }
            set
            {
                if (metroStyle != value)
                {
                    metroStyle = value;
                    StyleChange?.Invoke(null, null);
                    if (!isInitializing)
                    {
                        Update();
                    }
                }
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroThemeStyle.Light)]
        public MetroThemeStyle Theme
        {
            get
            {
                return metroTheme;
            }
            set
            {
                if (metroTheme != value)
                {
                    metroTheme = value;
                    ThemeChange?.Invoke(null, null);
                    if (!isInitializing)
                    {
                        Update();
                    }
                }
            }
        }

        public ContainerControl Owner
        {
            get
            {
                return owner;
            }
            set
            {
                if (owner != null)
                {
                    owner.ControlAdded -= ControlAdded;
                }
                owner = value;
                if (value != null)
                {
                    owner.ControlAdded += ControlAdded;
                    if (!isInitializing)
                    {
                        UpdateControl(value);
                    }
                }
            }
        }

        public MetroStyleManager()
        {
        }

        public MetroStyleManager(IContainer parentContainer)
            : this()
        {
            if (parentContainer != null)
            {
                this.parentContainer = parentContainer;
                this.parentContainer.Add(this);
            }
        }

        public object Clone()
        {
            MetroStyleManager metroStyleManager = new MetroStyleManager();
            metroStyleManager.metroTheme = Theme;
            metroStyleManager.metroStyle = Style;
            return metroStyleManager;
        }

        public object Clone(ContainerControl owner)
        {
            MetroStyleManager metroStyleManager = Clone() as MetroStyleManager;
            if (owner is IMetroForm)
            {
                metroStyleManager.Owner = owner;
                ((IMetroForm)owner).StyleManager = metroStyleManager;
                Type type = owner.GetType();
                FieldInfo field = type.GetField("components", BindingFlags.Instance | BindingFlags.NonPublic);
                if (field == null)
                {
                    return metroStyleManager;
                }
                IContainer container = (IContainer)field.GetValue(owner);
                if (container == null)
                {
                    return metroStyleManager;
                }
                {
                    foreach (Component component in container.Components)
                    {
                        if (component is IMetroComponent)
                        {
                            ApplyTheme((IMetroComponent)component);
                        }
                        if (component.GetType() == typeof(MetroContextMenu))
                        {
                            ApplyTheme((MetroContextMenu)component);
                        }
                    }
                    return metroStyleManager;
                }
            }
            return metroStyleManager;
        }

        void ISupportInitialize.BeginInit()
        {
            isInitializing = true;
        }

        void ISupportInitialize.EndInit()
        {
            isInitializing = false;
            Update();
        }

        private void ControlAdded(object sender, ControlEventArgs e)
        {
            if (!isInitializing)
            {
                UpdateControl(e.Control);
            }
        }

        public void Update()
        {
            if (owner != null)
            {
                UpdateControl(owner);
            }
            if (parentContainer != null && parentContainer.Components != null)
            {
                foreach (object component in parentContainer.Components)
                {
                    if (component is IMetroComponent)
                    {
                        ApplyTheme((IMetroComponent)component);
                    }
                    if (component.GetType() == typeof(MetroContextMenu))
                    {
                        ApplyTheme((MetroContextMenu)component);
                    }
                }
            }
        }

        private void UpdateControl(Control ctrl)
        {
            if (ctrl != null)
            {
                IMetroControl metroControl = ctrl as IMetroControl;
                if (metroControl != null)
                {
                    ApplyTheme(metroControl);
                }
                IMetroComponent metroComponent = ctrl as IMetroComponent;
                if (metroComponent != null)
                {
                    ApplyTheme(metroComponent);
                }
                TabControl tabControl = ctrl as TabControl;
                if (tabControl != null)
                {
                    foreach (TabPage tabPage in ((TabControl)ctrl).TabPages)
                    {
                        UpdateControl(tabPage);
                    }
                }
                if (ctrl.Controls != null)
                {
                    foreach (Control control in ctrl.Controls)
                    {
                        UpdateControl(control);
                    }
                }
                if (ctrl.ContextMenuStrip != null)
                {
                    UpdateControl(ctrl.ContextMenuStrip);
                }
                ctrl.Refresh();
            }
        }

        private void ApplyTheme(IMetroControl control)
        {
            control.StyleManager = this;
        }

        private void ApplyTheme(IMetroComponent component)
        {
            component.StyleManager = this;
        }
    }
}
