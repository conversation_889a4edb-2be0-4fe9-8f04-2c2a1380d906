﻿using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Properties;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Windows.Forms;

namespace OCRTools.Language
{
    public static class LanguageHelper
    {
        public static string NowLanguage = "zh-CN";

        #region Init Form Text

        public static void InitLanguage(this Form form)
        {
            InitFormText(form, true);
        }

        public static void InitFormText(Form form, bool isSet)
        {
            InitTextInfo(form, new List<Control> { form }, isSet);
        }

        public static void InitTextInfo(Form form, IEnumerable controls, bool isSet)
        {
            foreach (Control control in controls)
            {
                if (control is NumericUpDown)
                {
                    continue;
                }
                //if (!isSet && control.AccessibleDescription == null)
                if (control.AccessibleDescription == null)
                {
                    control.AccessibleDescription = control.Text;
                }
                if (isSet && !(control is WebBrowser) && !(control is TextBox))
                {
                    var strNewText = string.Empty;
                    if (control is ComboBox || control is RichTextBox)
                    {
                        if (!string.IsNullOrWhiteSpace(control.Text))
                        {
                            strNewText = control.Text.CurrentText();
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrWhiteSpace(control.AccessibleDescription))
                        {
                            strNewText = control.AccessibleDescription.CurrentText();
                        }
                    }
                    if (!string.IsNullOrWhiteSpace(strNewText))
                    {
                        control.Text = strNewText;
                    }
                }
                if (control is ToolStrip strip)
                {
                    InitToolStripMenuItem(form, strip.Items, isSet);
                }
                else if (control is DataGridView grid)
                {
                    InitDataGridViewColumn(form, grid.Columns, isSet);
                }
                else if (control is MenuButton mbtn && mbtn.Menu != null)
                {
                    InitToolStripMenuItem(form, mbtn.Menu.Items, isSet);
                }
                //else if (control is ComboBox combox)
                //{
                //    InitComboBoxItem(combox, isSet);
                //}
                if (control.ContextMenuStrip != null)
                {
                    InitToolStripMenuItem(form, control.ContextMenuStrip.Items, isSet);
                }

                if (control.Controls.Count > 0)
                    InitTextInfo(form, control.Controls, isSet);
            }
        }

        private static void InitComboBoxItem(ComboBox items, bool isSet)
        {
            if (isSet)
            {
                for (int i = 0; i < items.Items.Count; i++)
                {
                    var strNewText = items.Items[i].ToString().CurrentText();
                    if (!string.IsNullOrWhiteSpace(strNewText))
                    {
                        if (!Equals(items.SelectedItem, items.Items[i]))
                            items.Items[i] = strNewText;
                    }
                }
            }
        }

        private static void InitDataGridViewColumn(Form form, DataGridViewColumnCollection items, bool isSet)
        {
            foreach (DataGridViewColumn item in items)
            {
                //if (!isSet)
                {
                    if (item.Tag == null)
                    {
                        item.Tag = item.HeaderText;
                    }
                }
                //else
                {
                    if (item.Tag != null && !string.IsNullOrWhiteSpace(item.Tag.ToString()))
                    {
                        item.HeaderText = item.Tag.ToString().CurrentText();
                    }
                }
            }
        }

        private static void InitToolStripMenuItem(Form form, ToolStripItemCollection items, bool isSet)
        {
            foreach (ToolStripItem item in items)
            {
                //if (!isSet)
                {
                    if (item.AccessibleDescription == null)
                    {
                        item.AccessibleDescription = item.Text;
                    }
                    if (item.AccessibleName == null && !string.IsNullOrWhiteSpace(item.ToolTipText) && !Equals(item.Text, item.ToolTipText))
                    {
                        if (item.ToolTipText.Length < 20)
                            item.AccessibleName = item.ToolTipText;
                        else
                        {

                        }
                    }
                }
                //else
                {
                    var strNewText = "";
                    if (!string.IsNullOrWhiteSpace(item.AccessibleDescription))
                    {
                        strNewText = item.AccessibleDescription.CurrentText();
                    }
                    if (!string.IsNullOrWhiteSpace(strNewText))
                    {
                        item.Text = strNewText;
                    }

                    if (!string.IsNullOrWhiteSpace(item.AccessibleName))
                    {
                        item.ToolTipText = item.AccessibleName.CurrentText();
                    }
                    //else if (!string.IsNullOrWhiteSpace(item.Text))
                    //{
                    //    item.Text = item.Text.CurrentText();
                    //}
                }
                if (item is ToolStripDropDownItem tsm)
                {
                    InitToolStripMenuItem(form, tsm.DropDownItems, isSet);
                }
            }
        }

        #endregion

        public static void InstallLanguage(SupportedLanguage languageType, string language)
        {
            var strName = languageType.GetValue<DescriptionAttribute>("Description") + " " + LanguagePack.GetStaticText(language);
            var item = new ObjectTypeItem
            {
                Name = strName,
                AppPath = CommonString.DefaultLanguagePath,
                UpdateUrl = string.Format("{0}lang/{1}.lang?t=" + ServerTime.DateTime.Ticks, CommonString.HostUpdate?.FullUrl, language),
                Date = CommonMethod.GetLastWriteDate(CommonString.DefaultLanguagePath + language + ".lang"),
                Desc = strName
            };
            var result = CommonUpdate.Install(item.UpdateUrl, item.Date, item.Desc, item.Name, item.AppPath, false, true, true, false, null, true);
        }

        public static string GetStaticText(this string text, string language)
        {
            return DicStaticCulture[text].ContainsKey(language) ? DicStaticCulture[text][language] : text;
        }


        static Dictionary<string, string> DicNowCulture = new Dictionary<string, string>();
        static Dictionary<string, Dictionary<string, string>> DicStaticCulture = new Dictionary<string, Dictionary<string, string>>();

        static readonly string LanguagePack = "语言包";
        static readonly string StrInstall = "安装";
        static readonly string StrUpdate = "更新";
        static readonly string StrDownloaded = "已下载";

        static LanguageHelper()
        {
#if DEBUG
            LoadBaseCulture();
#endif
            DicStaticCulture[LanguagePack] = new Dictionary<string, string>
            {
                ["zh-TW"] = "語言包",
                ["ja-JP"] = "言語パック",
                ["ko-KR"] = "언어 팩",
                ["en-US"] = "Language Pack"
            };

            DicStaticCulture[StrInstall] = new Dictionary<string, string>
            {
                ["zh-TW"] = "安裝",
                ["ja-JP"] = "インストール",
                ["ko-KR"] = "설치",
                ["en-US"] = "Install"
            };

            DicStaticCulture[StrUpdate] = new Dictionary<string, string>
            {
                ["zh-TW"] = "更新",
                ["ja-JP"] = "更新",
                ["ko-KR"] = "업데이트",
                ["en-US"] = "Update"
            };

            DicStaticCulture[StrDownloaded] = new Dictionary<string, string>
            {
                ["zh-TW"] = "已下載",
                ["ja-JP"] = "ダウンロード済み",
                ["ko-KR"] = "다운로드 완료",
                ["en-US"] = "Downloaded"
            };
        }

        public static void InitLanguage(string strLanguage)
        {
#if DEBUG
            InitAllCulture(DicNowCulture, NowLanguage);
#endif

            SupportedLanguage type;
            if (string.IsNullOrWhiteSpace(strLanguage))
            {
                //根据系统语言查找支持的语言
                if (Enum.GetValues(typeof(SupportedLanguage)).OfType<SupportedLanguage>()
                    .Any(p => Equals(p.GetValue<MenuCategoryAttribute>(), CultureInfo.InstalledUICulture.Name)))
                {
                    type = Enum.GetValues(typeof(SupportedLanguage)).OfType<SupportedLanguage>()
                        .FirstOrDefault(p => Equals(p.GetValue<MenuCategoryAttribute>(), CultureInfo.InstalledUICulture.Name));
                }
                else
                {
                    //默认英语
                    type = SupportedLanguage.English;
                }
            }
            else
            {
                Enum.TryParse(strLanguage, out type);
            }

            DicNowCulture = new Dictionary<string, string>();
            NowLanguage = type.GetValue<MenuCategoryAttribute>();

            if (!Equals(SupportedLanguage.SimplifiedChinese, type))
            {
                var langFile = CommonString.DefaultLanguagePath + NowLanguage + ".lang";

                if (!File.Exists(langFile) || Application.OpenForms.Count <= 0)
                    InstallLanguage(type, NowLanguage);

                if (File.Exists(langFile))
                {
                    try
                    {
                        DicNowCulture = File.ReadAllText(langFile, Encoding.UTF8).DeserializeJson<Dictionary<string, string>>();
                    }
                    catch (Exception oe)
                    {
                        Log.WriteError("InitLanguage", oe);
                        try
                        {
                            File.Delete(langFile);
                        }
                        catch { }
                    }
                }
                if (DicNowCulture.Count <= 0)
                {
                    type = SupportedLanguage.SimplifiedChinese;
                    NowLanguage = type.GetValue<MenuCategoryAttribute>();
                }
            }

            CommonSetting.SetValue("语言", type.ToString());

#if DEBUG
            InitAllCulture(DicNowCulture, NowLanguage);
            SaveBaseCulture();
#endif
        }

#if DEBUG
        static Dictionary<string, Dictionary<string, string>> DicAllCulture = new Dictionary<string, Dictionary<string, string>>();
        private static List<string> lstBingKeys = new List<string>() { "3DAEE5B978BA031557E739EE1E2A68CB1FAD5909", "708BEDCB01828123DC7B6C6A6AB12EF82DFBB611", "ABB1C5A823DC3B7B1D5F4BDB886ED308B50D1919", "F84955C82256C25518548EE0C161B0BF87681F2F", "AC56E0F30DC3119A55994244361E06DC1B777049", "6844AE3580856D2EC7A64C79F55F11AA47CB961B", "78280AF4DFA1CE1676AFE86340C690023A5AC139", "76518BFCEBBF18E107C7073FBD4A735001B56BB1", "73B027BB51D74FB461C097BCCF841DB5678FDBB3", "A4D660A48A6A97CCA791C34935E4C02BBB1BEC1C", "AFC76A66CF4F434ED080D245C30CF1E71C22959C", "3D8D4E1888B88B975484F0CA25CDD24AAC457ED8", "C21742C60D890BCA6B3819EDAD45B74C77A25658", "3C9778666C5BA4B406FFCBEE64EF478963039C51", "FF274CA911390CAF2E950A1930E5700DB887D8D7", "C9739C3837CBBD166870AF1C6EFFDEBE433DC2A8", "DB50E2E9FBE2E92B103E696DCF4E3E512A8826FB", "47D78E7A59A91431BD06A3D8D5496E6308634F46" };

        private static void InitAllCulture(Dictionary<string, string> dicTmp, string language)
        {
            foreach (var key in dicTmp.Keys)
            {
                if (!DicAllCulture.ContainsKey(key))
                {
                    DicAllCulture[key] = new Dictionary<string, string>();
                }
                DicAllCulture[key][language] = dicTmp[key];
            }
        }

        class BingTranslateInfo
        {
            public string TranslatedText { get; set; }
        }

        public static void LoadBaseCulture()
        {
            try
            {
                if (File.Exists("all.lang"))
                {
                    DicAllCulture = new Dictionary<string, Dictionary<string, string>>();
                    var dicTmp = File.ReadAllText("all.lang", Encoding.UTF8).DeserializeJson<Dictionary<string, Dictionary<string, string>>>();
                    if (dicTmp != null)
                        foreach (var key in dicTmp.Keys.OrderBy(p => p))
                        {
                            DicAllCulture[key] = dicTmp[key];
                        }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("LoadBaseCulture", oe);
            }
        }

        public static void SaveAllCulture()
        {
            if (DicAllCulture.Count <= 0) return;
            Dictionary<string, Dictionary<string, string>> dicAll =
                new Dictionary<string, Dictionary<string, string>>
                {
                    ["zh-CN"] = new Dictionary<string, string>(),
                    ["en-US"] = new Dictionary<string, string>(),
                    ["ja-JP"] = new Dictionary<string, string>(),
                    ["ko-KR"] = new Dictionary<string, string>(),
                    ["zh-TW"] = new Dictionary<string, string>()
                };

            foreach (var culName in dicAll.Keys)
            {
                var language = dicAll[culName];
                foreach (var key in DicAllCulture.Keys)
                {
                    if (DicAllCulture[key].ContainsKey(culName))
                    {
                        language[key] = DicAllCulture[key][culName];
                    }
                }
            }
            var fromZhTo = new List<string> { "zh-TW", "ja-JP", "ko-KR", "en-US" };
            var zhCN = dicAll["zh-CN"];
            foreach (var culture in fromZhTo)
            {
                var language = dicAll[culture];
                var tansType = culture.Substring(0, 2);
                if (Equals(tansType, "zh"))
                {
                    tansType = culture.Contains("zh-CN") ? "zh-Hans" : "zh-Hant";
                }
                var lstToTrans = zhCN.Where(p => !language.ContainsKey(p.Key)).ToList();
                if (lstToTrans.Count <= 0) continue;
                var dicTrans = TransLanguage(lstToTrans, tansType);
                foreach (var item in dicTrans.Keys)
                {
                    language[item] = dicTrans[item];
                }
            }

            foreach (var cultureName in dicAll.Keys)
            {
                File.WriteAllText(Application.StartupPath + "\\Lang\\" + cultureName + ".lang"
                    , CommonString.JavaScriptSerializer.Serialize(dicAll[cultureName]), Encoding.UTF8);
                NowLanguage = cultureName;
                InitAllCulture(dicAll[cultureName], cultureName);
            }
            SaveBaseCulture();
        }

        private static Dictionary<string, string> TransLanguage(List<KeyValuePair<string, string>> dicTmp, string tansType)
        {
            var dicResult = new Dictionary<string, string>();
            var values = dicTmp.Select(p => p.Value).ToList();
            var batchSize = 100;
            var notTransValues = values.Select((x, index) => new { x, index })
                 .GroupBy(item => item.index / batchSize)
                 .Select(group => group.Select(item => item.x).ToArray())
                 .ToList();
            notTransValues.ForEach(val =>
            {
                var url = "https://api.microsofttranslator.com/V2/Ajax.svc/TranslateArray2?oncomplete=&appId=【【Key】】&to=" + tansType + "&texts=" + HttpUtility.UrlEncode(CommonString.JavaScriptSerializer.Serialize(val));
                var html = "";
                for (int i = 0; i < lstBingKeys.Count; i++)
                {
                    html = WebClientExt.GetHtml(url.Replace("【【Key】】", lstBingKeys.GetRndItem()));
                    if (!string.IsNullOrWhiteSpace(html) && html.StartsWith("["))
                    {
                        break;
                    }
                }

                if (string.IsNullOrWhiteSpace(html) || !html.StartsWith("["))
                {
                    html = WebClientExt.GetHtml(url.Replace("【【Key】】", lstBingKeys.GetRndItem()));
                }
                if (!string.IsNullOrWhiteSpace(html) && html.StartsWith("["))
                {
                    var lstTrans = html.DeserializeJson<List<BingTranslateInfo>>();
                    if (lstTrans.Count > 0 && lstTrans.Count == val.Length)
                    {
                        int index = 0;
                        foreach (var strItem in val)
                        {
                            var key = dicTmp.FirstOrDefault(p => Equals(p.Value, strItem)).Key;
                            dicResult[key] = lstTrans[index].TranslatedText;
                            index++;
                        }
                    }
                    //dicTmp[key][strItem] = html.Replace("\"", "").Trim();
                }
            });
            return dicResult;
        }

        public static void SaveBaseCulture()
        {
            File.WriteAllText("all.lang", CommonString.JavaScriptSerializer.Serialize(DicAllCulture), Encoding.UTF8);
        }

#endif

        public static string CurrentText(this string key)
        {
            if (!DicNowCulture.ContainsKey(key))
                DicNowCulture[key] = key;
            return DicNowCulture.TryGetValue(key, out var value) ? value : key;
        }

        public static string OriginText(this MetroForm form, Control ctrl)
        {
            return ctrl.AccessibleDescription ?? ctrl.Text;
        }

        public static string OriginText(this MetroForm form, ToolStripItem ctrl)
        {
            return ctrl.AccessibleDescription ?? ctrl.Text;
        }

        public static Image GetLanguageIcon(SupportedLanguage language)
        {
            Image icon;

            switch (language)
            {
                case SupportedLanguage.English:
                    icon = Resources.英文;
                    break;
                case SupportedLanguage.Japanese:
                    icon = Resources.日语;
                    break;
                case SupportedLanguage.Korean:
                    icon = Resources.韩语;
                    break;
                case SupportedLanguage.SimplifiedChinese:
                    icon = Resources.中文;
                    break;
                case SupportedLanguage.TraditionalChinese:
                    icon = Resources.香港繁体;
                    break;
                default:
                    icon = Resources.自动;
                    break;
            }

            if (icon != null)
            {
                icon = ImageProcessHelper.ResizeImage(new Bitmap(icon), new Size(21, 21), true);
            }

            return icon;
        }
    }
}
