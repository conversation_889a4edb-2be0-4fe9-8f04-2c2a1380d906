using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolGaus : ToolObject
    {
        private DrawGaus _drawGaus;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.CurrentToolType = drawArea.ActiveTool;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else
            {
                _drawGaus = new DrawGaus(e.X, e.Y, 1, 1)
                {
                    BackgroundImageEx = drawArea.BackgroundImageEx
                };
                AddNewObject(drawArea, _drawGaus);
            }
        }

        protected new void AddNewObject(DrawArea drawArea, DrawObject o)
        {
            drawArea.GraphicsList.UnselectAll();
            drawArea.GraphicsList.Add(o);
            drawArea.Capture = true;
            o.Selected = true;
            drawArea.Refresh();
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left)
            {
                if (_drawGaus == null)
                {
                    drawArea.ActiveTool = DrawToolType.Text;
                    return;
                }

                _drawGaus.IsSelected = true;
                var obj = _drawGaus;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    _drawGaus.MoveHandleTo(e.Location, 5, drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (_drawGaus != null)
            {
                StaticValue.CurrentRectangle = _drawGaus.Rectangle;
                _drawGaus.IsCache = true;
                if (!_drawGaus.Rectangle.IsLimt())
                {
                    drawArea.GraphicsList.RemoveAt(0);
                    return;
                }

                var obj = _drawGaus;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    _drawGaus.Normalize();
                    drawArea.AddCommandToHistory(new CommandAdd(_drawGaus));
                }
            }
        }
    }
}