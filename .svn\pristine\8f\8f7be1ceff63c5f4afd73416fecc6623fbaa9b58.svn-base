using System.Collections.Generic;

namespace OCRTools
{
    internal class UndoManager
    {
        private readonly GraphicsList graphicsList;

        private List<Command> historyList;

        private int nextUndo;

        public UndoManager(GraphicsList graphicsList)
        {
            this.graphicsList = graphicsList;
            ClearHistory();
        }

        public bool CanUndo
        {
            get
            {
                if (nextUndo < 0 || nextUndo > historyList.Count - 1) return false;
                return true;
            }
        }

        public bool CanRedo
        {
            get
            {
                if (nextUndo == historyList.Count - 1) return false;
                return true;
            }
        }

        public void ClearHistory()
        {
            historyList = new List<Command>();
            nextUndo = -1;
        }

        public void AddCommandToHistory(Command command)
        {
            TrimHistoryList();
            historyList.Add(command);
            nextUndo++;
        }

        public void Undo()
        {
            if (CanUndo)
            {
                var command = historyList[nextUndo];
                command.Undo(graphicsList);
                nextUndo--;
            }
        }

        public void Redo()
        {
            if (CanRedo)
            {
                var index = nextUndo + 1;
                var command = historyList[index];
                command.Redo(graphicsList);
                nextUndo++;
            }
        }

        private void TrimHistoryList()
        {
            if (historyList.Count != 0 && nextUndo != historyList.Count - 1)
                for (var num = historyList.Count - 1; num > nextUndo; num--)
                    historyList.RemoveAt(num);
        }
    }
}