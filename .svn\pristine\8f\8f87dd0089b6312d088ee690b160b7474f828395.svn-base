﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Windows.Globalization;
using Windows.Graphics.Imaging;
using Windows.Media.Ocr;
using Windows.Storage;

namespace OcrLib
{
    public class LocalOcrHelper
    {
        static Language Language;

        public static bool InitLanguage()
        {
            var result = false;
            try
            {
                if (OcrEngine.AvailableRecognizerLanguages.Count <= 0)
                {
                    return result;
                }
                var language = OcrEngine.AvailableRecognizerLanguages.FirstOrDefault(p => p.LanguageTag.StartsWith("zh-Hans"))?.LanguageTag;
                if (string.IsNullOrEmpty(language))
                {
                    language = OcrEngine.AvailableRecognizerLanguages.FirstOrDefault(p => p.LanguageTag.StartsWith("zh"))?.LanguageTag;
                    if (string.IsNullOrEmpty(language))
                    {
                        language = OcrEngine.AvailableRecognizerLanguages.FirstOrDefault(p => p.LanguageTag.StartsWith("en"))?.LanguageTag;
                        if (string.IsNullOrEmpty(language))
                        {
                            language = OcrEngine.AvailableRecognizerLanguages.FirstOrDefault()?.LanguageTag;
                        }
                    }
                }
                if (!string.IsNullOrEmpty(language))
                {
                    Language = new Language(language);
                }
                result = Language != null && OcrEngine.IsLanguageSupported(Language);
            }
            finally
            {
                Console.WriteLine("Windows OCR 初始化" + (result ? "成功" : "失败"));
            }
            return result;
        }

        public static OcrResult Detect(byte[] img)
        {
            if (Language == null)
            {
                return null;
            }

            var ocrTmp = RecognizeAsync(img, Language).Result;
            //var ss = new System.Web.Script.Serialization.JavaScriptSerializer().Serialize(ocrTmp);
            //Console.WriteLine(ss);
            var textBlocks = new List<TextBlock>();
            if (ocrTmp != null && ocrTmp.Lines != null)
            {
                foreach (var line in ocrTmp.Lines)
                {
                    var word = line.Text;
                    if (string.IsNullOrEmpty(word))
                    {
                        continue;
                    }
                    //替换中文之间的空格
                    word = Regex.Replace(word, "(?<=[\u4e00-\u9fa5])(\u0020)(?=[\u4e00-\u9fa5])", string.Empty);
                    //替换韩文之间的空格
                    word = Regex.Replace(word, "(?<=[\x3130-\x318F])(\u0020)(?=[\x3130-\x318F])", string.Empty);
                    word = Regex.Replace(word, "(?<=[\xAC00-\xD7A3])(\u0020)(?=[\xAC00-\xD7A3])", string.Empty);
                    //替换日文之间的空格
                    word = Regex.Replace(word, "(?<=[\u0800-\u4e00])(\u0020)(?=[\u0800-\u4e00])", string.Empty);

                    //if (Language.LanguageTag.StartsWith("zh", StringComparison.OrdinalIgnoreCase) || // Chinese
                    //        Language.LanguageTag.StartsWith("ja", StringComparison.OrdinalIgnoreCase) || // Japanese
                    //        Language.LanguageTag.StartsWith("ko", StringComparison.OrdinalIgnoreCase)) // Korean
                    //{
                    //    // If CJK language then remove spaces between words.
                    //    word = string.Concat(line.Words.Select(word => word.Text));
                    //}
                    //else 
                    //if (Language.LayoutDirection == LanguageLayoutDirection.Rtl)
                    //{
                    //    // If RTL language then reverse order of words.
                    //    word = string.Concat(line.Words.Reverse().Select(word => word.Text));
                    //}
                    //else
                    //{
                    //    word = string.Concat(line.Words.Select(word => word.Text));
                    //}
                    var rect = line.Words[0].BoundingRect;
                    for (int i = 1; i < line.Words.Count; i++)
                    {
                        rect.Union(line.Words[i].BoundingRect);
                    }
                    var text = new TextBlock()
                    {
                        Text = word,
                        BoundingRect = new System.Drawing.Rectangle((int)rect.X, (int)rect.Y, (int)rect.Width, (int)rect.Height),
                        BoxPoints = new List<System.Drawing.Point>()
                    };
                    if (!text.BoundingRect.IsEmpty)
                    {
                        text.BoxPoints.Add(new System.Drawing.Point { X = text.BoundingRect.X, Y = text.BoundingRect.Y });
                        text.BoxPoints.Add(new System.Drawing.Point { X = text.BoundingRect.Right, Y = text.BoundingRect.Y });
                        text.BoxPoints.Add(new System.Drawing.Point { X = text.BoundingRect.X, Y = text.BoundingRect.Bottom });
                        text.BoxPoints.Add(new System.Drawing.Point { X = text.BoundingRect.Right, Y = text.BoundingRect.Bottom });
                    }
                    textBlocks.Add(text);
                }
            }
            return new OcrResult
            {
                TextBlocks = textBlocks,
                StrRes = string.Join("\n", textBlocks.Select((TextBlock p) => p.Text))
            };
        }

        static async Task<Windows.Media.Ocr.OcrResult> RecognizeAsync(byte[] byts, Language lang)
        {
            var fileName = AppDomain.CurrentDomain.BaseDirectory + Guid.NewGuid().ToString() + ".png";
            try
            {
                File.WriteAllBytes(fileName, byts);
                var storageFile = await StorageFile.GetFileFromPathAsync(fileName);
                using (var randomAccessStream = await storageFile.OpenReadAsync())
                {
                    var decoder = await BitmapDecoder.CreateAsync(randomAccessStream);
                    using (var softwareBitmap = await decoder.GetSoftwareBitmapAsync(BitmapPixelFormat.Bgra8, BitmapAlphaMode.Premultiplied))
                    {
                        return await OcrEngine.TryCreateFromLanguage(lang)?.RecognizeAsync(softwareBitmap);
                    }
                }
            }
            finally
            {
                try
                {
                    File.Delete(fileName);
                }
                catch { }
            }
        }
    }
}
