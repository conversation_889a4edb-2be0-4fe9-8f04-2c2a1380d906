﻿using System;

namespace OCRTools
{
    /// <summary>
    ///     Models a marking line on the ruler.
    /// </summary>
    public struct RulerMarker
    {
        public static readonly RulerMarker Default = new RulerMarker();

        public float Value;
        public bool Vertical;

        public RulerMarker(float value, bool vertical)
        {
            Value = value;
            Vertical = vertical;
        }

        public static bool operator ==(RulerMarker x, RulerMarker y)
        {
            return x.Value == y.Value && x.Vertical == y.Vertical;
        }

        public static bool operator !=(RulerMarker x, RulerMarker y)
        {
            return !(x == y);
        }

        public override bool Equals(object obj)
        {
            if (obj is RulerMarker other)
                return Value == other.Value && Vertical == other.Vertical;
            return false;
        }

        public override int GetHashCode()
        {
            return Value.GetHashCode();
        }

        public string DisplayString
        {
            get
            {
                if (!Vertical) return string.Format("⇄ {0,6} px", Value);
                return string.Format("⇅ {0,6} px", Value);
            }
        }

        public static RulerMarker FromString(string s)
        {
            var v = s.Substring(0, s.Length - 1);
            var t = s.Substring(s.Length - 1, 1);
            var value = float.Parse(v);
            var vertical = t == "v" ? true : t == "h" ? false : throw new ArgumentException();
            return new RulerMarker(value, vertical);
        }

        public override string ToString()
        {
            var type = Vertical ? "v" : "h";
            return $"{Value}{type}";
        }
    }
}