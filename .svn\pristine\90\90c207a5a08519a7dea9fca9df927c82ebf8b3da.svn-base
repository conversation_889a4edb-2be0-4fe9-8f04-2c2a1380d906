﻿using MetroFramework.Controls;
using MetroFramework.Native;
using System;
using System.Collections;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace MetroFramework.Design
{
    public class MetroTabControlDesigner : ParentControlDesigner
    {
        private readonly DesignerVerbCollection _designerVerbs = new DesignerVerbCollection();

        private IDesignerHost _designerHost;

        private ISelectionService _selectionService;

        public MetroTabControlDesigner()
        {
            var designerVerb = new DesignerVerb("Add Tab", OnAddPage);
            var designerVerb2 = new DesignerVerb("Remove Tab", OnRemovePage);
            _designerVerbs.AddRange(new[]
            {
                designerVerb,
                designerVerb2
            });
        }

        public override SelectionRules SelectionRules
        {
            get
            {
                if (Control.Dock != DockStyle.Fill) return base.SelectionRules;
                return SelectionRules.Visible;
            }
        }

        public override DesignerVerbCollection Verbs
        {
            get
            {
                if (_designerVerbs.Count == 2)
                {
                    var metroTabControl = (MetroTabControl)Control;
                    _designerVerbs[1].Enabled = metroTabControl.TabCount != 0;
                }

                return _designerVerbs;
            }
        }

        public IDesignerHost DesignerHost =>
            _designerHost ?? (_designerHost = (IDesignerHost)GetService(typeof(IDesignerHost)));

        public ISelectionService SelectionService => _selectionService ??
                                                     (_selectionService =
                                                         (ISelectionService)GetService(typeof(ISelectionService)));

        private void OnAddPage(object sender, EventArgs e)
        {
            var metroTabControl = (MetroTabControl)Control;
            var controls = metroTabControl.Controls;
            RaiseComponentChanging(TypeDescriptor.GetProperties(metroTabControl)["TabPages"]);
            var metroTabPage = (MetroTabPage)DesignerHost.CreateComponent(typeof(MetroTabPage));
            metroTabPage.Text = metroTabPage.Name;
            metroTabControl.TabPages.Add(metroTabPage);
            RaiseComponentChanged(TypeDescriptor.GetProperties(metroTabControl)["TabPages"], controls,
                metroTabControl.TabPages);
            metroTabControl.SelectedTab = metroTabPage;
            SetVerbs();
        }

        private void OnRemovePage(object sender, EventArgs e)
        {
            var metroTabControl = (MetroTabControl)Control;
            var controls = metroTabControl.Controls;
            if (metroTabControl.SelectedIndex >= 0)
            {
                RaiseComponentChanging(TypeDescriptor.GetProperties(metroTabControl)["TabPages"]);
                DesignerHost.DestroyComponent(metroTabControl.TabPages[metroTabControl.SelectedIndex]);
                RaiseComponentChanged(TypeDescriptor.GetProperties(metroTabControl)["TabPages"], controls,
                    metroTabControl.TabPages);
                SelectionService.SetSelectedComponents(new[]
                {
                    metroTabControl
                }, SelectionTypes.Auto);
                SetVerbs();
            }
        }

        private void SetVerbs()
        {
            var metroTabControl = (MetroTabControl)Control;
            Verbs[1].Enabled = metroTabControl.TabPages.Count != 0;
        }

        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m);
            var msg = m.Msg;
            if (msg == 132 && m.Result.ToInt32() == -1) m.Result = (IntPtr)1L;
        }

        protected override bool GetHitTest(Point point)
        {
            if (SelectionService.PrimarySelection == Control)
            {
                var tChittestinfo = default(WinApi.TCHITTESTINFO);
                tChittestinfo.pt = Control.PointToClient(point);
                tChittestinfo.flags = 0u;
                var tChittestinfo2 = tChittestinfo;
                var message = default(Message);
                message.HWnd = Control.Handle;
                message.Msg = 4883;
                var m = message;
                var intPtr = Marshal.AllocHGlobal(Marshal.SizeOf(tChittestinfo2));
                Marshal.StructureToPtr(tChittestinfo2, intPtr, false);
                m.LParam = intPtr;
                base.WndProc(ref m);
                Marshal.FreeHGlobal(intPtr);
                if (m.Result.ToInt32() != -1) return tChittestinfo2.flags != 1;
            }

            return false;
        }

        protected override void PreFilterProperties(IDictionary properties)
        {
            properties.Remove("ImeMode");
            properties.Remove("Padding");
            properties.Remove("FlatAppearance");
            properties.Remove("FlatStyle");
            properties.Remove("AutoEllipsis");
            properties.Remove("UseCompatibleTextRendering");
            properties.Remove("Image");
            properties.Remove("ImageAlign");
            properties.Remove("ImageIndex");
            properties.Remove("ImageKey");
            properties.Remove("ImageList");
            properties.Remove("TextImageRelation");
            properties.Remove("BackgroundImage");
            properties.Remove("BackgroundImageLayout");
            properties.Remove("UseVisualStyleBackColor");
            properties.Remove("Font");
            properties.Remove("RightToLeft");
            base.PreFilterProperties(properties);
        }
    }

    #region MetroTabPageCollectionEditor

    public class MetroTabPageCollectionEditor : CollectionEditor
    {
        public MetroTabPageCollectionEditor(Type type)
            : base(type)
        {
        }

        protected override CollectionForm CreateCollectionForm()
        {
            var collectionForm = base.CreateCollectionForm();
            collectionForm.Text = "MetroTabPage Collection Editor";
            return collectionForm;
        }

        protected override Type CreateCollectionItemType()
        {
            return typeof(MetroTabPage);
        }

        protected override Type[] CreateNewItemTypes()
        {
            return new[]
            {
                typeof(MetroTabPage)
            };
        }
    }

    #endregion
}