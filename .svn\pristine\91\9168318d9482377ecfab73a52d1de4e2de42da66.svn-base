using Microsoft.Win32;
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;

namespace OCRTools
{
    internal class HookAPI
    {
        public delegate int HookProc(int nCode, int wParam, IntPtr lParam);

        public const int WH_KEYBOARD_LL = 13;

        private static int hHook;

        private static HookProc KeyBoardHookProcedure;

        [DllImport("user32.dll")]
        public static extern int SetWindowsHookEx(int idHook, HookProc lpfn, IntPtr hInstance, int threadId);

        [DllImport("user32.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto)]
        public static extern bool UnhookWindowsHookEx(int idHook);

        [DllImport("user32.dll")]
        public static extern int CallNextHookEx(int idHook, int nCode, int wParam, IntPtr lParam);

        [DllImport("kernel32.dll")]
        public static extern int GetCurrentThreadId();

        [DllImport("kernel32.dll")]
        public static extern IntPtr GetModuleHandle(string name);

        public static void Hook_Start()
        {
            if (hHook == 0)
            {
                KeyBoardHookProcedure = KeyBoardHookProc;
                hHook = SetWindowsHookEx(13, KeyBoardHookProcedure,
                    GetModuleHandle(Process.GetCurrentProcess().MainModule.ModuleName), 0);
                if (hHook == 0) Hook_Clear();
            }
        }

        public static void Hook_Clear()
        {
            var flag = true;
            if (hHook != 0)
            {
                flag = UnhookWindowsHookEx(hHook);
                hHook = 0;
            }

            if (!flag) throw new Exception("UnhookWindowsHookEx failed.");
        }

        private static int KeyBoardHookProc(int nCode, int wParam, IntPtr lParam)
        {
            if (nCode >= 0)
            {
                var keyBoardHookStruct =
                    (KeyBoardHookStruct) Marshal.PtrToStructure(lParam, typeof(KeyBoardHookStruct));
                if (keyBoardHookStruct.vkCode == 91) return 1;
                if (keyBoardHookStruct.vkCode == 92) return 1;
            }

            return CallNextHookEx(hHook, nCode, wParam, lParam);
        }

        public static void TaskMgrLocking(bool bLock)
        {
            if (bLock)
                try
                {
                    var registryKey =
                        Registry.CurrentUser.OpenSubKey(
                            "Software\\Microsoft\\Windows\\CurrentVersion\\Policies\\System", true);
                    registryKey.SetValue("DisableTaskmgr", "1");
                }
                catch
                {
                    var registryKey2 =
                        Registry.CurrentUser.CreateSubKey(
                            "Software\\Microsoft\\Windows\\CurrentVersion\\Policies\\System");
                    registryKey2.SetValue("DisableTaskmgr", "0");
                }
            else
                Registry.CurrentUser.DeleteSubKey("Software\\Microsoft\\Windows\\CurrentVersion\\Policies\\System");
        }

        [StructLayout(LayoutKind.Sequential)]
        public class KeyBoardHookStruct
        {
            public int dwExtraInfo;

            public int flags;

            public int scanCode;

            public int time;
            public int vkCode;
        }
    }
}