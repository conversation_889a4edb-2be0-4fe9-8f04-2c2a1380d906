using System;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace OCRTools
{
    public static class PrimaryScreen
    {
        public static float ScreenScalingFactor { get; set; }

        //public static int DpiX
        //{
        //    get
        //    {
        //        var dC = GetDC(IntPtr.Zero);
        //        var deviceCaps = GetDeviceCaps(dC, 88);
        //        ReleaseDC(IntPtr.Zero, dC);
        //        return deviceCaps;
        //    }
        //}

        [DllImport("user32.dll")]
        public static extern bool EnumDisplaySettings(string lpszDeviceName, int iModeNum, ref DEVMODE lpDevMode);

        [StructLayout(LayoutKind.Sequential)]
        public struct DEVMODE
        {
            private const int CCHDEVICENAME = 0x20;
            private const int CCHFORMNAME = 0x20;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 0x20)]
            public string dmDeviceName;
            public short dmSpecVersion;
            public short dmDriverVersion;
            public short dmSize;
            public short dmDriverExtra;
            public int dmFields;
            public int dmPositionX;
            public int dmPositionY;
            public ScreenOrientation dmDisplayOrientation;
            public int dmDisplayFixedOutput;
            public short dmColor;
            public short dmDuplex;
            public short dmYResolution;
            public short dmTTOption;
            public short dmCollate;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 0x20)]
            public string dmFormName;
            public short dmLogPixels;
            public int dmBitsPerPel;
            public int dmPelsWidth;
            public int dmPelsHeight;
            public int dmDisplayFlags;
            public int dmDisplayFrequency;
            public int dmICMMethod;
            public int dmICMIntent;
            public int dmMediaType;
            public int dmDitherType;
            public int dmReserved1;
            public int dmReserved2;
            public int dmPanningWidth;
            public int dmPanningHeight;
        }

        const int ENUM_CURRENT_SETTINGS = -1;
        public static Rectangle GetAllRectangle()
        {
            var size = Size.Empty;
            foreach (Screen screen in Screen.AllScreens)
            {
                DEVMODE dm = new DEVMODE { dmSize = (short)Marshal.SizeOf(typeof(DEVMODE)) };
                EnumDisplaySettings(screen.DeviceName, ENUM_CURRENT_SETTINGS, ref dm);
                size.Width += dm.dmPelsWidth;
                size.Height = Math.Max(size.Height, dm.dmPelsHeight);
            }
            return new Rectangle(Point.Empty, size);
        }

        public static Rectangle GetScreenWorkingArea()
        {
            return Screen.AllScreens.Select(x => x.WorkingArea).Combine();
        }

        //public static float GetScreenScalingFactor()
        //{
        //    using (Graphics g = Graphics.FromHwnd(IntPtr.Zero))
        //    {
        //        IntPtr desktop = g.GetHdc();
        //        int LogicalScreenHeight = GetDeviceCaps(desktop, 10);//(int)DeviceCap.VERTRES);
        //        int PhysicalScreenHeight = GetDeviceCaps(desktop, 117);// (int)DeviceCap.DESKTOPVERTRES);
        //        int logpixelsy = GetDeviceCaps(desktop, 90);// (int)DeviceCap.LOGPIXELSY);
        //        float screenScalingFactor = (float)PhysicalScreenHeight / LogicalScreenHeight;
        //        float dpiScalingFactor = logpixelsy / 96f;
        //        ScreenScalingFactor = Math.Max(screenScalingFactor, dpiScalingFactor);
        //        g.ReleaseHdc(desktop);
        //    }
        //    return ScreenScalingFactor;
        //}

        //public static float DpiZoom => (float)(DpiX * 1.0f / 96.0);

        //public static Size DESKTOP
        //{
        //    get
        //    {
        //        IntPtr dC = GetDC(IntPtr.Zero);
        //        var result = new Size();
        //        result.Width = GetDeviceCaps(dC, 118);
        //        result.Height = GetDeviceCaps(dC, 117);
        //        ReleaseDC(IntPtr.Zero, dC);
        //        return result;
        //    }
        //}

        [DllImport("user32.dll")]
        private static extern IntPtr GetDC(IntPtr ptr);

        [DllImport("gdi32.dll")]
        private static extern int GetDeviceCaps(IntPtr hdc, int nIndex);

        [DllImport("user32.dll")]
        private static extern IntPtr ReleaseDC(IntPtr hWnd, IntPtr hDc);

        public static int DpiValue(this int num)
        {
            return num;
            //return Convert.ToInt32(Convert.ToDouble(num) * DpiZoom);
        }
    }
}