﻿using MetroFramework.Forms;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace OCRTools
{
    // Cyotek ImageBox
    // Copyright (c) 2010 Cyotek. All Rights Reserved.
    // http://cyotek.com

    [DefaultProperty("Image")]
    [ToolboxBitmap(typeof(ImageBox))]
    public partial class ImageBox : ScrollableControl
    {
        #region  Public Constructors  

        public ImageBox()
        {
            InitializeComponent();

            SetStyle(
                ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | ControlStyles.OptimizedDoubleBuffer |
                ControlStyles.ResizeRedraw, true);
            //SetStyle(ControlStyles.StandardDoubleClick, false);
            UpdateStyles();

            BackColor = Color.White;
            AutoSize = true;
            GridScale = ImageBoxGridScale.Small;

            GridColor = CommonSetting.图片预览背景颜色;
            GridDisplayMode = CommonSetting.ConvertToEnum(CommonSetting.图片预览背景, ImageBoxGridDisplayMode.马赛克);
            BorderStyle = BorderStyle.FixedSingle;
            AutoPan = true;
            Zoom = 100;
            ZoomIncrement = 20;
            InterpolationMode = InterpolationMode.HighQualityBicubic;
            AutoCenter = true;
            DoubleClick += ImageBox_DoubleClick;
            KeyUp += ImageBox_KeyUp;

            InitBackGround();
        }

        private void ImageBox_KeyUp(object sender, KeyEventArgs e)
        {
            var form = FindForm();
            if (form == null)
            {
                return;
            }

            if (e.KeyCode == Keys.Escape)
            {
                if (form is FrmMain)
                {
                    if (form.WindowState == FormWindowState.Maximized)
                    {
                        SwitchMaxWindow();
                    }
                }
                else
                {
                    form.Close();
                }
            }
        }

        public void SwitchMaxWindow()
        {
            var form = FindForm();
            if (form == null)
            {
                return;
            }

            if (!(form is MetroForm))
            {
                if (form.FormBorderStyle == FormBorderStyle.None)
                {
                    form.FormBorderStyle = FormBorderStyle.Sizable;
                }
            }
            form.WindowState = form.WindowState == FormWindowState.Normal
                ? FormWindowState.Maximized
                : FormWindowState.Normal;
            if (!(form is MetroForm))
            {
                if (form.WindowState == FormWindowState.Maximized)
                {
                    form.FormBorderStyle = FormBorderStyle.None;
                }
            }
        }

        private void ImageBox_DoubleClick(object sender, EventArgs e)
        {
            SwitchMaxWindow();
        }

        #endregion  Public Constructors  

        #region  Public Overridden Methods 

        public override Size GetPreferredSize(Size proposedSize)
        {
            Size size;

            if (Image != null)
            {
                // get the size of the image
                var width = ScaledImageWidth;
                var height = ScaledImageHeight;

                // add an offset based on padding
                width += Padding.Horizontal;
                height += Padding.Vertical;

                // add an offset based on the border style
                width += GetBorderOffset();
                height += GetBorderOffset();

                size = new Size(width, height);
            }
            else
            {
                size = base.GetPreferredSize(proposedSize);
            }

            return size;
        }

        #endregion  Public Overridden Methods  

        #region  Private Class Member Declarations  

        public readonly int MinZoom = 10;
        public readonly int MaxZoom = 3500;

        #endregion  Private Class Member Declarations  

        #region  Private Member Declarations  

        private bool _autoCenter;
        private bool _autoPan;
        private BorderStyle _borderStyle;

        public Color GridColor { get; set; }

        private Bitmap _gridTile;
        private Image _image;
        private Image _originImage;
        private InterpolationMode _interpolationMode;
        private bool _invertMouse;
        private bool _isPanning;
        private bool _sizeToFit;
        private Point _startMousePosition;
        private Point _startScrollPosition;
        private TextureBrush _texture;
        private int _zoom;
        private int _zoomIncrement;

        #endregion  Private Member Declarations  

        #region  Events  

        [Category("Property Changed")] public event EventHandler AutoCenterChanged;

        [Category("Property Changed")] public event EventHandler AutoPanChanged;

        [Category("Property Changed")] public event EventHandler BorderStyleChanged;

        [Category("Property Changed")] public event EventHandler GridDisplayModeChanged;

        [Category("Property Changed")] public event EventHandler GridScaleChanged;

        [Category("Property Changed")] public event EventHandler ImageChanged;

        [Category("Property Changed")] public event EventHandler InterpolationModeChanged;

        [Category("Property Changed")] public event EventHandler InvertMouseChanged;

        [Category("Property Changed")] public event EventHandler PanEnd;

        [Category("Property Changed")] public event EventHandler PanStart;

        [Category("Property Changed")] public event EventHandler SizeToFitChanged;

        [Category("Property Changed")] public event EventHandler ZoomChanged;

        [Category("Property Changed")] public event EventHandler ZoomIncrementChanged;

        #endregion  Events  

        #region  Overriden Properties  

        [Browsable(true)]
        [EditorBrowsable(EditorBrowsableState.Always)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        [DefaultValue(true)]
        public override bool AutoSize
        {
            get => base.AutoSize;
            set
            {
                if (base.AutoSize != value)
                {
                    base.AutoSize = value;
                    AdjustLayout();
                }
            }
        }

        [DefaultValue(typeof(Color), "白色")]
        public override Color BackColor
        {
            get => base.BackColor;
            set => base.BackColor = value;
        }

        [Browsable(false)]
        [EditorBrowsable(EditorBrowsableState.Never)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public override Image BackgroundImage
        {
            get => base.BackgroundImage;
            set => base.BackgroundImage = value;
        }

        [Browsable(false)]
        [EditorBrowsable(EditorBrowsableState.Never)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public override ImageLayout BackgroundImageLayout
        {
            get => base.BackgroundImageLayout;
            set => base.BackgroundImageLayout = value;
        }

        [Browsable(false)]
        [EditorBrowsable(EditorBrowsableState.Never)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public override Font Font
        {
            get => base.Font;
            set => base.Font = value;
        }

        [Browsable(false)]
        [EditorBrowsable(EditorBrowsableState.Never)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public override string Text
        {
            get => base.Text;
            set => base.Text = value;
        }

        #endregion  Overriden Properties  

        #region  Protected Overridden Methods  

        /// <summary>
        ///     Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (components != null)
                    components.Dispose();

                if (_texture != null)
                {
                    _texture.Dispose();
                    _texture = null;
                }

                if (_gridTile != null)
                {
                    _gridTile.Dispose();
                    _gridTile = null;
                }
            }

            base.Dispose(disposing);
        }

        protected override bool IsInputKey(Keys keyData)
        {
            bool result;

            if (((keyData & Keys.Right) == Keys.Right) | ((keyData & Keys.Left) == Keys.Left) |
                ((keyData & Keys.Up) == Keys.Up) | ((keyData & Keys.Down) == Keys.Down))
                result = true;
            else
                result = base.IsInputKey(keyData);

            return result;
        }

        protected override void OnBackColorChanged(EventArgs e)
        {
            base.OnBackColorChanged(e);

            Invalidate();
        }

        protected override void OnDockChanged(EventArgs e)
        {
            base.OnDockChanged(e);

            if (Dock != DockStyle.None)
                AutoSize = false;
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            base.OnKeyDown(e);

            if (e.Modifiers == Keys.Control && (e.KeyCode == Keys.D0 || e.KeyCode == Keys.NumPad0))
                Zoom = 100;
            else
                switch (e.KeyCode)
                {
                    case Keys.Left:
                        AdjustScroll(
                            -(e.Modifiers == Keys.None ? HorizontalScroll.SmallChange : HorizontalScroll.LargeChange),
                            0);
                        break;
                    case Keys.Right:
                        AdjustScroll(
                            e.Modifiers == Keys.None ? HorizontalScroll.SmallChange : HorizontalScroll.LargeChange, 0);
                        break;
                    case Keys.Up:
                        AdjustScroll(0,
                            -(e.Modifiers == Keys.None ? VerticalScroll.SmallChange : VerticalScroll.LargeChange));
                        break;
                    case Keys.Down:
                        AdjustScroll(0,
                            e.Modifiers == Keys.None ? VerticalScroll.SmallChange : VerticalScroll.LargeChange);
                        break;
                }
        }

        //protected override void OnMouseClick(MouseEventArgs e)
        //{
        //    if (!this.IsPanning && !this.SizeToFit)
        //    {
        //        if (e.Button == MouseButtons.Left && Control.ModifierKeys == Keys.Control)
        //        {
        //            this.Zoom = 100;
        //        }
        //    }

        //    base.OnMouseClick(e);
        //}

        //protected override void OnMouseClick(MouseEventArgs e)
        //{
        //    if (!this.IsPanning && !this.SizeToFit)
        //    {
        //        if (e.Button == MouseButtons.Left && Control.ModifierKeys == Keys.无)
        //        {
        //            if (this.Zoom >= 100)
        //                this.Zoom = (int)Math.Round((double)(this.Zoom + 100) / 100) * 100;
        //            else if (this.Zoom >= 75)
        //                this.Zoom = 100;
        //            else
        //                this.Zoom = (int)(this.Zoom / 0.75F);
        //        }
        //        else if (e.Button == MouseButtons.Right || (e.Button == MouseButtons.Left && Control.ModifierKeys != Keys.无))
        //        {
        //            if (this.Zoom > 100 && this.Zoom <= 125)
        //                this.Zoom = 100;
        //            else if (this.Zoom > 100)
        //                this.Zoom = (int)Math.Round((double)(this.Zoom - 100) / 100) * 100;
        //            else
        //                this.Zoom = (int)(this.Zoom * 0.75F);
        //        }
        //    }

        //    base.OnMouseClick(e);
        //}

        protected override void OnMouseDown(MouseEventArgs e)
        {
            base.OnMouseDown(e);
            if (!Focused)
                Focus();
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            base.OnMouseMove(e);

            if (e.Button == MouseButtons.Left && AutoPan && Image != null)
            {
                if (!IsPanning)
                {
                    _startMousePosition = e.Location;
                    IsPanning = true;
                }

                if (IsPanning)
                {
                    int x;
                    int y;

                    if (!InvertMouse)
                    {
                        x = -_startScrollPosition.X + (_startMousePosition.X - e.Location.X);
                        y = -_startScrollPosition.Y + (_startMousePosition.Y - e.Location.Y);
                    }
                    else
                    {
                        x = -(_startScrollPosition.X + (_startMousePosition.X - e.Location.X));
                        y = -(_startScrollPosition.Y + (_startMousePosition.Y - e.Location.Y));
                    }

                    var position = new Point(x, y);

                    UpdateScrollPosition(position);
                }
            }
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            base.OnMouseUp(e);

            if (IsPanning)
                IsPanning = false;
        }

        protected override void OnMouseWheel(MouseEventArgs e)
        {
            if (!SizeToFit)
            {
                int increment;

                if (ModifierKeys == Keys.None)
                    increment = ZoomIncrement;
                else
                    increment = ZoomIncrement * 5;

                if (e.Delta < 0)
                    increment = -increment;

                Zoom += increment;
            }
        }

        protected override void OnPaddingChanged(EventArgs e)
        {
            base.OnPaddingChanged(e);
            AdjustLayout();
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            //// draw the borders
            //switch (this.BorderStyle)
            //{
            //    case BorderStyle.FixedSingle:
            //        ControlPaint.DrawBorder(e.Graphics, this.ClientRectangle, this.ForeColor, ButtonBorderStyle.实线);
            //        break;
            //    case BorderStyle.Fixed3D:
            //        ControlPaint.DrawBorder3D(e.Graphics, this.ClientRectangle, Border3DStyle.Sunken);
            //        break;
            //}

            var innerRectangle = GetInsideViewPort();

            // draw the background
            using (var brush = new SolidBrush(BackColor))
            {
                e.Graphics.FillRectangle(brush, innerRectangle);
            }

            if (_texture != null)
                e.Graphics.FillRectangle(_texture, innerRectangle);
            // draw the image
            if (Image != null)
                DrawImage(e.Graphics);

            base.OnPaint(e);
        }

        protected override void OnParentChanged(EventArgs e)
        {
            base.OnParentChanged(e);
            AdjustLayout();
        }

        protected override void OnResize(EventArgs e)
        {
            AdjustLayout();

            base.OnResize(e);
        }

        protected override void OnScroll(ScrollEventArgs se)
        {
            Invalidate();

            base.OnScroll(se);
        }

        #endregion  Protected Overridden Methods  

        #region  Public Methods  

        public virtual Rectangle GetImageViewPort()
        {
            var viewPort = Rectangle.Empty;

            if (Image != null)
                try
                {
                    Point offset;
                    var innerRectangle = GetInsideViewPort();

                    if (AutoCenter)
                    {
                        var x = !HScroll ? (innerRectangle.Width - (ScaledImageWidth + Padding.Horizontal)) / 2 : 0;
                        var y = !VScroll ? (innerRectangle.Height - (ScaledImageHeight + Padding.Vertical)) / 2 : 0;

                        offset = new Point(x, y);
                    }
                    else
                    {
                        offset = Point.Empty;
                    }

                    viewPort = new Rectangle(offset.X + innerRectangle.Left + Padding.Left,
                        offset.Y + innerRectangle.Top + Padding.Top,
                        innerRectangle.Width - (Padding.Horizontal + offset.X * 2),
                        innerRectangle.Height - (Padding.Vertical + offset.Y * 2));
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }

            return viewPort;
        }

        public Rectangle GetInsideViewPort()
        {
            return GetInsideViewPort(false);
        }

        public virtual Rectangle GetInsideViewPort(bool includePadding)
        {
            var borderOffset = GetBorderOffset();
            var left = borderOffset;
            var top = borderOffset;
            var width = ClientSize.Width - borderOffset * 2;
            var height = ClientSize.Height - borderOffset * 2;

            if (includePadding)
            {
                left += Padding.Left;
                top += Padding.Top;
                width -= Padding.Horizontal;
                height -= Padding.Vertical;
            }

            return new Rectangle(left, top, width, height);
        }

        public virtual Rectangle GetSourceImageRegion()
        {
            Rectangle region;

            if (Image != null)
            {
                var viewPort = GetImageViewPort();
                var sourceLeft = (int)(-AutoScrollPosition.X / ZoomFactor);
                var sourceTop = (int)(-AutoScrollPosition.Y / ZoomFactor);
                var sourceWidth = (int)(viewPort.Width / ZoomFactor);
                var sourceHeight = (int)(viewPort.Height / ZoomFactor);

                region = new Rectangle(sourceLeft, sourceTop, sourceWidth, sourceHeight);
            }
            else
            {
                region = Rectangle.Empty;
            }

            return region;
        }

        public virtual void SetImage(Image image, bool isAutoFit)
        {
            if (image != null)
            {
                _image = image;
                OriginImage = image;
                if (isAutoFit)
                {
                    double zoom;
                    double aspectRatio;

                    AutoScrollMinSize = Size.Empty;

                    var innerRectangle = GetInsideViewPort(true);

                    if (Image.Width > Image.Height)
                    {
                        aspectRatio = innerRectangle.Width / (double)Image.Width;
                        zoom = aspectRatio * 100.0;

                        if (innerRectangle.Height < Image.Height * zoom / 100.0)
                        {
                            aspectRatio = innerRectangle.Height / (double)Image.Height;
                            zoom = aspectRatio * 100.0;
                        }
                    }
                    else
                    {
                        aspectRatio = innerRectangle.Height / (double)Image.Height;
                        zoom = aspectRatio * 100.0;

                        if (innerRectangle.Width < Image.Width * zoom / 100.0)
                        {
                            aspectRatio = innerRectangle.Width / (double)Image.Width;
                            zoom = aspectRatio * 100.0;
                        }
                    }
                    _zoom = (int)Math.Round(Math.Floor(zoom));
                }
                else
                {
                    _zoom = 100;
                }
                OnImageChanged(EventArgs.Empty);
            }
        }

        public int GetFitZoom(bool isLimitMax = false)
        {
            double zoom;
            double aspectRatio;

            AutoScrollMinSize = Size.Empty;

            var innerRectangle = GetInsideViewPort(true);

            if (Image.Width > Image.Height)
            {
                aspectRatio = innerRectangle.Width / (double)Image.Width;
                zoom = aspectRatio * 100.0;

                if (innerRectangle.Height < Image.Height * zoom / 100.0)
                {
                    aspectRatio = innerRectangle.Height / (double)Image.Height;
                    zoom = aspectRatio * 100.0;
                }
            }
            else
            {
                aspectRatio = innerRectangle.Height / (double)Image.Height;
                zoom = aspectRatio * 100.0;

                if (innerRectangle.Width < Image.Width * zoom / 100.0)
                {
                    aspectRatio = innerRectangle.Width / (double)Image.Width;
                    zoom = aspectRatio * 100.0;
                }
            }

            if (isLimitMax)
            {
                zoom = Math.Min(100, zoom);
            }

            return (int)Math.Round(Math.Floor(zoom));
        }

        public virtual void ZoomToFit(bool isLimitMax = false)
        {
            if (Image != null)
            {
                Zoom = GetFitZoom(isLimitMax);
            }
        }

        #endregion  Public Methods  

        #region  Public Properties  

        [DefaultValue(true)]
        [Category("Appearance")]
        public bool AutoCenter
        {
            get => _autoCenter;
            set
            {
                if (_autoCenter != value)
                {
                    _autoCenter = value;
                    OnAutoCenterChanged(EventArgs.Empty);
                }
            }
        }

        [DefaultValue(true)]
        [Category("Behavior")]
        public bool AutoPan
        {
            get => _autoPan;
            set
            {
                if (_autoPan != value)
                {
                    _autoPan = value;
                    OnAutoPanChanged(EventArgs.Empty);

                    if (value)
                        SizeToFit = false;
                }
            }
        }

        [Browsable(false)]
        [EditorBrowsable(EditorBrowsableState.Never)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public new Size AutoScrollMinSize
        {
            get => base.AutoScrollMinSize;
            set => base.AutoScrollMinSize = value;
        }

        [Category("Appearance")]
        [DefaultValue(typeof(BorderStyle), "FixedSingle")]
        public BorderStyle BorderStyle
        {
            get => _borderStyle;
            set
            {
                if (_borderStyle != value)
                {
                    _borderStyle = value;
                    OnBorderStyleChanged(EventArgs.Empty);
                }
            }
        }

        [Category("Appearance")]
        public ImageBoxGridDisplayMode GridDisplayMode { get; set; }

        [DefaultValue(typeof(ImageBoxGridScale), "Small")]
        [Category("Appearance")]
        public ImageBoxGridScale GridScale { get; set; }

        [Category("Appearance")]
        [DefaultValue(null)]
        public virtual Image Image
        {
            get => _image;
            set
            {
                if (_image != value)
                {
                    _image = value;
                    _zoom = 100;
                    OnImageChanged(EventArgs.Empty);
                }
            }
        }

        [Category("Appearance")]
        [DefaultValue(null)]
        public virtual Image OriginImage
        {
            get => _originImage;
            set
            {
                if (_originImage != value)
                {
                    _originImage = value;
                    Image = _originImage;
                }
            }
        }

        [DefaultValue(InterpolationMode.Default)]
        [Category("Appearance")]
        public InterpolationMode InterpolationMode
        {
            get => _interpolationMode;
            set
            {
                if (value == InterpolationMode.Invalid)
                    value = InterpolationMode.Default;

                if (_interpolationMode != value)
                {
                    _interpolationMode = value;
                    OnInterpolationModeChanged(EventArgs.Empty);
                }
            }
        }

        [DefaultValue(false)]
        [Category("Behavior")]
        public bool InvertMouse
        {
            get => _invertMouse;
            set
            {
                if (_invertMouse != value)
                {
                    _invertMouse = value;
                    OnInvertMouseChanged(EventArgs.Empty);
                }
            }
        }

        [DefaultValue(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        public bool IsPanning
        {
            get => _isPanning;
            protected set
            {
                if (_isPanning != value)
                {
                    _isPanning = value;
                    _startScrollPosition = AutoScrollPosition;

                    if (value)
                    {
                        Cursor = Cursors.SizeAll;
                        OnPanStart(EventArgs.Empty);
                    }
                    else
                    {
                        Cursor = Cursors.Default;
                        OnPanEnd(EventArgs.Empty);
                    }
                }
            }
        }

        [DefaultValue(false)]
        [Category("Appearance")]
        public bool SizeToFit
        {
            get => _sizeToFit;
            set
            {
                if (_sizeToFit != value)
                {
                    _sizeToFit = value;
                    OnSizeToFitChanged(EventArgs.Empty);

                    if (value)
                        AutoPan = false;
                }
            }
        }

        [DefaultValue(100)]
        [Category("Appearance")]
        public int Zoom
        {
            get => _zoom;
            set
            {
                if (value < MinZoom)
                    value = MinZoom;
                else if (value > MaxZoom)
                    value = MaxZoom;

                if (_zoom != value)
                {
                    _zoom = value;
                    OnZoomChanged(EventArgs.Empty);
                }
            }
        }

        [DefaultValue(10)]
        [Category("Behavior")]
        public int ZoomIncrement
        {
            get => _zoomIncrement;
            set
            {
                if (_zoomIncrement != value)
                {
                    _zoomIncrement = value;
                    OnZoomIncrementChanged(EventArgs.Empty);
                }
            }
        }

        #endregion  Public Properties  

        #region  Private Methods  

        private int GetBorderOffset()
        {
            int offset;

            switch (BorderStyle)
            {
                case BorderStyle.Fixed3D:
                    offset = 2;
                    break;
                case BorderStyle.FixedSingle:
                    offset = 1;
                    break;
                default:
                    offset = 0;
                    break;
            }

            return offset;
        }

        public void InitBackGround()
        {
            _texture?.Dispose();

            _gridTile?.Dispose();
            if (GridDisplayMode == ImageBoxGridDisplayMode.马赛克)
            {
                GridColor = Color.FromArgb(220, Color.Gainsboro);
                _gridTile = CreateGridTileImage(8, GridColor, Color.White);
            }
            else
            {
                _gridTile = new Bitmap(1, 1);
                switch (GridDisplayMode)
                {
                    case ImageBoxGridDisplayMode.豆沙绿:
                        GridColor = Color.FromArgb(199, 237, 204);
                        break;
                    case ImageBoxGridDisplayMode.深沉黑:
                        GridColor = Color.FromArgb(42, 47, 56);
                        break;
                    case ImageBoxGridDisplayMode.办公灰:
                        GridColor = Color.FromArgb(238, 243, 250);
                        break;
                }
                _gridTile.SetPixel(0, 0, GridColor);
            }
            _texture = new TextureBrush(_gridTile);

            Invalidate();
        }

        protected virtual Bitmap CreateGridTileImage(int cellSize, Color firstColor, Color secondColor)
        {
            float scale;

            // rescale the cell size
            switch (GridScale)
            {
                case ImageBoxGridScale.Medium:
                    scale = 1.5F;
                    break;
                case ImageBoxGridScale.Large:
                    scale = 2;
                    break;
                default:
                    scale = 1;
                    break;
            }

            cellSize = (int)(cellSize * scale);

            // draw the tile
            var width = cellSize * 2;
            var height = cellSize * 2;
            var result = new Bitmap(width, height);
            using (var g = Graphics.FromImage(result))
            {
                using (var brush = new SolidBrush(firstColor))
                {
                    g.FillRectangle(brush, new Rectangle(0, 0, width, height));
                }

                using (var brush = new SolidBrush(secondColor))
                {
                    g.FillRectangle(brush, new Rectangle(0, 0, cellSize, cellSize));
                    g.FillRectangle(brush, new Rectangle(cellSize, cellSize, cellSize, cellSize));
                }
            }

            return result;
        }

        #endregion  Private Methods  

        #region  Protected Properties  

        protected virtual int ScaledImageHeight => Image != null ? (int)(Image.Size.Height * ZoomFactor) : 0;

        protected virtual int ScaledImageWidth => Image != null ? (int)(Image.Size.Width * ZoomFactor) : 0;

        protected virtual double ZoomFactor => (double)Zoom / 100;

        #endregion  Protected Properties  

        #region  Protected Methods  

        protected virtual void AdjustLayout()
        {
            if (Disposing || IsDisposed)
            {
                return;
            }
            if (AutoSize)
                AdjustSize();
            else if (SizeToFit)
                ZoomToFit();
            else if (AutoScroll)
                AdjustViewPort();
            Invalidate();
        }

        protected virtual void AdjustScroll(int x, int y)
        {
            var scrollPosition = new Point(HorizontalScroll.Value + x, VerticalScroll.Value + y);

            UpdateScrollPosition(scrollPosition);
        }

        protected virtual void AdjustSize()
        {
            if (AutoSize && Dock == DockStyle.None)
                Size = PreferredSize;
        }

        protected virtual void AdjustViewPort()
        {
            if (AutoScroll && Image != null)
                AutoScrollMinSize = new Size(ScaledImageWidth + Padding.Horizontal,
                    ScaledImageHeight + Padding.Vertical);
        }

        protected virtual void DrawImage(Graphics g)
        {
            g.InterpolationMode = InterpolationMode;
            g.DrawImage(Image, GetImageViewPort(), GetSourceImageRegion(), GraphicsUnit.Pixel);
        }

        protected virtual void OnAutoCenterChanged(EventArgs e)
        {
            Invalidate();

            AutoCenterChanged?.Invoke(this, e);
        }

        protected virtual void OnAutoPanChanged(EventArgs e)
        {
            AutoPanChanged?.Invoke(this, e);
        }

        protected virtual void OnBorderStyleChanged(EventArgs e)
        {
            AdjustLayout();

            BorderStyleChanged?.Invoke(this, e);
        }

        protected virtual void OnImageChanged(EventArgs e)
        {
            AdjustLayout();

            ImageChanged?.Invoke(this, e);
        }

        protected virtual void OnInterpolationModeChanged(EventArgs e)
        {
            Invalidate();

            InterpolationModeChanged?.Invoke(this, e);
        }

        protected virtual void OnInvertMouseChanged(EventArgs e)
        {
            InvertMouseChanged?.Invoke(this, e);
        }

        protected virtual void OnPanEnd(EventArgs e)
        {
            PanEnd?.Invoke(this, e);
        }

        protected virtual void OnPanStart(EventArgs e)
        {
            PanStart?.Invoke(this, e);
        }

        protected virtual void OnSizeToFitChanged(EventArgs e)
        {
            AdjustLayout();

            SizeToFitChanged?.Invoke(this, e);
        }

        protected virtual void OnZoomChanged(EventArgs e)
        {
            AdjustLayout();

            ZoomChanged?.Invoke(this, e);
        }

        protected virtual void OnZoomIncrementChanged(EventArgs e)
        {
            ZoomIncrementChanged?.Invoke(this, e);
        }

        protected virtual void UpdateScrollPosition(Point position)
        {
            AutoScrollPosition = position;
            Invalidate();
            OnScroll(new ScrollEventArgs(ScrollEventType.ThumbPosition, 0));
        }

        #endregion  Protected Methods  
    }
}