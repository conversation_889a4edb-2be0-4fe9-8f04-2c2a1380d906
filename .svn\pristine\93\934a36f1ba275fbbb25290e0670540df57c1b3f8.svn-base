using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolPointer : Tool
    {
        private CommandChangeState commandChangeState;

        private int Dx;

        private int Dy;

        private Point lastPoint = new Point(0, 0);

        private DrawObject resizedObject;

        private int resizedObjectHandle;

        private SelectionMode selectMode = SelectionMode.None;

        private Point startPoint = new Point(0, 0);

        private bool wasMove;

        public void MouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            commandChangeState = null;
            wasMove = false;
            selectMode = SelectionMode.None;
            var point = new Point(e.X, e.Y);
            foreach (var item in drawArea.GraphicsList.Selection)
            {
                var num = item.HitTest(point);
                if (num > 0)
                {
                    selectMode = SelectionMode.Size;
                    resizedObject = item;
                    resizedObjectHandle = num;
                    drawArea.GraphicsList.UnselectAll();
                    if (item.NoteType != DrawToolType.Step) item.Selected = true;
                    if (item.NoteType == DrawToolType.Mosaic || item.NoteType == DrawToolType.Gaus)
                        item.IsCache = false;
                    if (item.NoteType == DrawToolType.Catch) drawArea.HideTool();
                    if (item.NoteType == DrawToolType.MultiCatch) drawArea.HideMultiTool();
                    commandChangeState = new CommandChangeState(drawArea.GraphicsList);
                    break;
                }
            }

            var @catch = drawArea.GetCatch();
            if (@catch != null)
            {
                var num2 = @catch.HitCatch(point);
                if (num2 > 0)
                {
                    selectMode = SelectionMode.Size;
                    resizedObject = @catch;
                    resizedObjectHandle = num2;
                    drawArea.GraphicsList.UnselectAll();
                    @catch.Selected = true;
                    drawArea.HideTool();
                    drawArea.HideMultiTool();
                    commandChangeState = new CommandChangeState(drawArea.GraphicsList);
                }
            }

            if (selectMode == SelectionMode.None)
            {
                var count = drawArea.GraphicsList.Count;
                DrawObject drawObject = null;
                for (var i = 0; i < count; i++)
                    if (drawArea.GraphicsList[i].HitTest(point) == 0)
                    {
                        drawObject = drawArea.GraphicsList[i];
                        break;
                    }

                if (drawObject != null)
                {
                    selectMode = SelectionMode.Move;
                    if ((Control.ModifierKeys & Keys.Control) == 0 && !drawObject.Selected)
                        drawArea.GraphicsList.UnselectAll();
                    drawObject.Selected = true;
                    if (drawObject.NoteType == DrawToolType.Mosaic || drawObject.NoteType == DrawToolType.Gaus ||
                        drawObject.NoteType == DrawToolType.Highlight) drawObject.IsCache = false;
                    if (drawObject.NoteType == DrawToolType.Catch) drawArea.HideTool();
                    if (drawObject.NoteType == DrawToolType.MultiCatch) drawArea.HideMultiTool();
                    StaticValue.current_ToolType = drawObject.NoteType;
                    commandChangeState = new CommandChangeState(drawArea.GraphicsList);
                    drawArea.Cursor = CursorEx.Move;
                }
            }

            lastPoint.X = e.X;
            lastPoint.Y = e.Y;
            startPoint.X = e.X;
            startPoint.Y = e.Y;
            drawArea.Capture = true;
            drawArea.Refresh();
        }

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            MouseDown(drawArea, e);
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            var point = new Point(e.X, e.Y);
            if (e.Button == MouseButtons.None)
            {
                Cursor cursor = null;
                for (var i = 0; i < drawArea.GraphicsList.Count; i++)
                {
                    var num = drawArea.GraphicsList[i].HitTest(point);
                    if (num > 0)
                    {
                        cursor = drawArea.GraphicsList[i].GetHandleCursor(num);
                        break;
                    }
                }

                if (cursor == null) cursor = CursorEx.Cross;
                drawArea.Cursor = cursor;
                return;
            }

            var selected = drawArea.GetSelected(drawArea);
            if (e.Button != MouseButtons.Left) return;
            var num2 = e.X - lastPoint.X;
            var num3 = e.Y - lastPoint.Y;
            Dx = num2;
            Dy = num3;
            lastPoint.X = e.X;
            lastPoint.Y = e.Y;
            if (selectMode == SelectionMode.Size && resizedObject != null)
            {
                wasMove = true;
                var @object = drawArea.GraphicsList.SelectedObjects.ToList();
                if (selected != null && selected.NoteType == DrawToolType.MultiCatch)
                {
                    resizedObject.MoveHandleTo(e.Location, resizedObjectHandle);
                    return;
                }

                using (new AutomaticCanvasRefresher(drawArea, @object.GetGroupBoundingBox))
                {
                    var selected2 = drawArea.GetSelected(drawArea);
                    if (selected2 != null)
                        switch (selected2.NoteType)
                        {
                            case DrawToolType.Pointer:
                            case DrawToolType.Step:
                            case DrawToolType.Polygon:
                                break;
                            case DrawToolType.Line:
                                resizedObject.MoveHandleTo(e.Location, resizedObjectHandle,
                                    drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                                break;
                            case DrawToolType.Arrow:
                                resizedObject.MoveHandleTo(e.Location, resizedObjectHandle,
                                    drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                                break;
                            case DrawToolType.Rectangle:
                                resizedObject.MoveHandleTo(e.Location, resizedObjectHandle,
                                    drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                                break;
                            case DrawToolType.Ellipse:
                                resizedObject.MoveHandleTo(e.Location, resizedObjectHandle,
                                    drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                                break;
                            case DrawToolType.Text:
                                resizedObject.MoveHandleTo(e.Location, resizedObjectHandle,
                                    drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                                break;
                            case DrawToolType.Mosaic:
                                resizedObject.MoveHandleTo(e.Location, resizedObjectHandle,
                                    drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                                break;
                            case DrawToolType.Gaus:
                                resizedObject.MoveHandleTo(e.Location, resizedObjectHandle,
                                    drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                                break;
                            case DrawToolType.RectangleFill:
                                resizedObject.MoveHandleTo(e.Location, resizedObjectHandle,
                                    drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                                break;
                            case DrawToolType.Highlight:
                                resizedObject.MoveHandleTo(e.Location, resizedObjectHandle,
                                    drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                                break;
                            case DrawToolType.MultiCatch:
                                resizedObject.MoveHandleTo(e.Location, resizedObjectHandle);
                                break;
                        }
                }

                if (selected != null && selected.NoteType == DrawToolType.Catch && selected is DrawCatch)
                {
                    var drawCatch = (DrawCatch) selected;
                    using (new AutomaticCanvasRefresher(drawArea, drawCatch.GetAddBound))
                    {
                    }

                    using (new AutomaticCanvasRefresher(drawArea, () => drawCatch.GetBoundingBox()))
                    {
                        resizedObject.MoveHandleTo(e.Location, resizedObjectHandle);
                    }
                }
            }

            if (selectMode != SelectionMode.Move || !drawArea.GraphicsList.SelectedObjects.Any()) return;
            var list = drawArea.GraphicsList.SelectedObjects.ToList();
            var rectangle = drawArea.BaseVirtualScreen().SizeOffset(-2);
            wasMove = true;
            if (selected.NoteType == DrawToolType.MultiCatch)
            {
                foreach (var item in list)
                {
                    var rectangle2 = item.Rectangle.SizeOffset(-1);
                    var num4 = rectangle.X - rectangle2.X;
                    var num5 = rectangle.Y - rectangle2.Y;
                    var num6 = rectangle.X + rectangle.Width - rectangle2.X - rectangle2.Width;
                    var num7 = rectangle.Y + rectangle.Height - rectangle2.Y - rectangle2.Height;
                    if (num4 > num2) num2 = num4;
                    if (num6 < num2) num2 = num6;
                    if (num5 > num3) num3 = num5;
                    if (num7 < num3) num3 = num7;
                    item.Move(num2, num3);
                }

                return;
            }

            if (selected != null && selected.NoteType == DrawToolType.Catch && selected is DrawCatch)
            {
                var object2 = (DrawCatch) selected;
                using (new AutomaticCanvasRefresher(drawArea, object2.GetAddBound))
                {
                }
            }

            using (new AutomaticCanvasRefresher(drawArea, list.GetGroupBoundingBox))
            {
                foreach (var item2 in list)
                {
                    if (selected.NoteType == DrawToolType.Catch)
                    {
                        var rectangle3 = item2.Rectangle.SizeOffset(-1);
                        var num8 = rectangle.X - rectangle3.X;
                        var num9 = rectangle.Y - rectangle3.Y;
                        var num10 = rectangle.X + rectangle.Width - rectangle3.X - rectangle3.Width;
                        var num11 = rectangle.Y + rectangle.Height - rectangle3.Y - rectangle3.Height;
                        if (num8 > num2) num2 = num8;
                        if (num10 < num2) num2 = num10;
                        if (num9 > num3) num3 = num9;
                        if (num11 < num3) num3 = num11;
                    }

                    item2.Move(num2, num3);
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (resizedObject != null)
            {
                resizedObject.Normalize();
                resizedObject = null;
            }

            var graphicsList = drawArea.GraphicsList.graphicsList;
            foreach (var item in graphicsList) item.IsCache = true;
            var selected = drawArea.GetSelected(drawArea);
            if (selected != null)
            {
                if (selected.NoteType == DrawToolType.Catch) drawArea.ShowTool(selected);
                if (selected.NoteType == DrawToolType.MultiCatch) drawArea.ShowMultiTool(selected);
            }

            if (commandChangeState != null && wasMove)
            {
                commandChangeState.NewState(drawArea.GraphicsList);
                drawArea.AddCommandToHistory(commandChangeState);
                commandChangeState = null;
            }

            drawArea.Capture = false;
            drawArea.Refresh();
            if (StaticValue.current_ToolType != 0) drawArea.ActiveTool = StaticValue.current_ToolType;
        }

        private enum SelectionMode
        {
            None,
            Move,
            Size
        }
    }
}