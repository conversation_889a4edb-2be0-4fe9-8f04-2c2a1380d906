using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Reflection;
using System.Windows.Forms;
using OCRTools.Properties;

namespace OCRTools
{
    public class ScrollingText : Control
    {
        private readonly Timer _timer; // 文本动画计时器
        private DateTime _lastUpdateTime; // 用于基于时间的动画
        private RectangleF _lastKnownRect; // 文本的最后已知位置
        private bool _scrollOn = true; // 内部标志，用于停止/启动文本滚动
        private float _staticTextPos; // 文本的运行x位置
        private string _text = "Text"; // 滚动文本
        private float _yPos; // 文本的运行y位置
        private float _scrollSpeed = 50.0f; // 每秒像素数
        private PictureBox picClose;
        public bool Close { get; set; }
        public Control CloseOwner { get; set; }
        public bool IsCanClose { get; set; }

        public ScrollingText()
        {
            // 设置ScrollingText控件的默认属性
            InitializeComponent();

            SetStyle(ControlStyles.SupportsTransparentBackColor |
                     ControlStyles.OptimizedDoubleBuffer |
                     ControlStyles.DoubleBuffer |
					 ControlStyles.AllPaintingInWmPaint |
                     ControlStyles.UserPaint, true);

            // 设置计时器对象以实现平滑动画
            _timer = new Timer
            {
                Interval = 16, // ~60 FPS for smooth animation
                Enabled = true
            };
            _lastUpdateTime = DateTime.Now;
            _timer.Tick += Tick;
        }

        /// <summary>
        ///     Clean up any resources being used.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                //Make sure our brushes are cleaned up
                if (ForegroundBrush != null)
                    ForegroundBrush.Dispose();

                //Make sure our brushes are cleaned up
                if (BackgroundBrush != null)
                    BackgroundBrush.Dispose();

                //Make sure our timer is cleaned up
                if (_timer != null)
                    _timer.Dispose();
            }

            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary>
        ///     Required method for Designer support - do not modify
        ///     the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            //ScrollingText            
            this.Name = "ScrollingText";
            this.Size = new Size(216, 40);
            this.Click += new EventHandler(this.ScrollingText_Click);
            this.picClose = new PictureBox
            {
                Cursor = Cursors.Hand,
                Visible = false,
                Image = Resources.Full_close_hover,
                BackColor = Color.Transparent,
                SizeMode = PictureBoxSizeMode.StretchImage
            };
            this.SizeChanged += ScrollingText_SizeChanged;
            picClose.MouseDown += PicClose_MouseDown;
            this.Controls.Add(picClose);
        }

        private void ScrollingText_SizeChanged(object sender, EventArgs e)
        {
            var width = Math.Min(Height, picClose.Image.Height);
            picClose.Size = new Size(width, width);
            picClose.Location = new Point(Width - picClose.Width, 0);
        }

        private void PicClose_MouseDown(object sender, MouseEventArgs e)
        {
            CloseOwner = Parent;
            Close = true;
            this.Visible = false;
        }

        #endregion

        // 使用基于时间的平滑移动控制文本动画
        private void Tick(object sender, EventArgs e)
        {
            if (IsCanClose)
                picClose.Visible = RectangleToScreen(ClientRectangle).Contains(MousePosition);
            
            // 计算平滑动画的时间差
            var currentTime = DateTime.Now;
            var deltaTime = (float)(currentTime - _lastUpdateTime).TotalSeconds;
            _lastUpdateTime = currentTime;
            
            // 基于时间和速度更新文本位置
            if (_scrollOn && deltaTime > 0)
            {
                UpdateTextPosition(deltaTime);
            }
            
            // 简化刷新 - 只需使整个控件无效以获得更平滑的渲染
            Invalidate();
        }

        // 绘制滚动文本控件
        protected override void OnPaint(PaintEventArgs pe)
        {
            // 将文本绘制到新位置
            DrawScrollingText(pe.Graphics);

            // 将图形对象传递给基础Control类
            base.OnPaint(pe);
        }

        // 在控件上绘制滚动文本        
        public void DrawScrollingText(Graphics canvas)
        {
            canvas.InterpolationMode = InterpolationMode.HighQualityBilinear;
            canvas.CompositingQuality = CompositingQuality.HighQuality;
            canvas.SmoothingMode = SmoothingMode.HighQuality;
            canvas.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;

            // 测量字符串大小以进行位置计算
            var stringSize = canvas.MeasureString(_text, Font);

            // 计算滚动文本的垂直位置
            if (_scrollOn) CalcVerticalPosition(stringSize);

            // 用用户设置的背景色清除控件
            if (BackgroundBrush != null)
                canvas.FillRectangle(BackgroundBrush, 0, 0, ClientSize.Width, ClientSize.Height);
            else
                canvas.Clear(BackColor);

            // 绘制边框
            if (ShowBorder)
                using (var borderPen = new Pen(BorderColor))
                {
                    canvas.DrawRectangle(borderPen, 0, 0, ClientSize.Width - 1, ClientSize.Height - 1);
                }

            // 在内存中的位图中绘制文本字符串
            if (ForegroundBrush == null)
                using (Brush tempForeBrush = new SolidBrush(ForeColor))
                {
                    canvas.DrawString(_text, Font, tempForeBrush, _staticTextPos, _yPos);
                }
            else
                canvas.DrawString(_text, Font, ForegroundBrush, _staticTextPos, _yPos);

            _lastKnownRect = new RectangleF(_staticTextPos, _yPos, stringSize.Width, stringSize.Height);
            EnableTextLink(_lastKnownRect);
        }

        /// <summary>
        /// 基于时间差更新文本位置以实现平滑动画（仅支持从右到左）
        /// </summary>
        /// <param name="deltaTime">自上次更新以来经过的时间（秒）</param>
        private void UpdateTextPosition(float deltaTime)
        {
            // 基于滚动速度和时间从右向左移动文本
            _staticTextPos -= _scrollSpeed * deltaTime;
            
            // 获取文本大小以进行边界检查
            using (var g = CreateGraphics())
            {
                var stringSize = g.MeasureString(_text, Font);
                
                // 当文本完全移出左侧时，重置到控件中间重新开始
                // 文本从控件中间开始，立即可见，然后向左移动
                if (_staticTextPos < -stringSize.Width)
                {
                    _staticTextPos = ClientSize.Width / 2; // 重置到控件中间
                    LoopTimes++;
                }
            }
        }
        
        /// <summary>
        /// 计算滚动文本的垂直位置
        /// </summary>
        /// <param name="stringSize">文本字符串的大小</param>
        private void CalcVerticalPosition(SizeF stringSize)
        {
            switch (VerticleTextPosition)
            {
                case VerticleTextPosition.Top:
                    _yPos = 2;
                    break;
                case VerticleTextPosition.Center:
                    _yPos = ClientSize.Height * 1.0f / 2 - stringSize.Height / 2;
                    break;
                case VerticleTextPosition.Botom:
                    _yPos = ClientSize.Height - stringSize.Height;
                    break;
            }
        }

        #region 鼠标悬停和文本链接逻辑

        private void EnableTextLink(RectangleF textRect)
        {
            if (!StopScrollOnMouseOver)
            {
                _scrollOn = true;
                if (Cursor != Cursors.Default)
                    Cursor = Cursors.Default;
                return;
            }

            var curPt = PointToClient(Cursor.Position);

            if (textRect.Contains(curPt))
            {
                // 当用户鼠标悬停在文本上时停止文本滚动
                if (StopScrollOnMouseOver)
                    _scrollOn = false;
                Cursor = Cursors.Hand;
            }
            else
            {
                _scrollOn = true;
                // 确保当用户鼠标不在文本上时文本继续滚动
                Cursor = Cursors.Default;
            }
        }

        private void ScrollingText_Click(object sender, EventArgs e)
        {
            // 如果用户在鼠标悬停在文本上时点击，则触发文本点击事件
            // 这使得文本可以像超链接一样工作
            if (Cursor == Cursors.Hand)
                OnTextClicked(this, EventArgs.Empty);
        }

        public delegate void TextClickEventHandler(object sender, EventArgs args);

        public event TextClickEventHandler TextClicked;

        private void OnTextClicked(object sender, EventArgs args)
        {
            // 调用委托
            if (TextClicked != null)
            {
                TextClicked(sender, args);
            }
            else
            {
                if (string.IsNullOrEmpty(StrLink)) return;
                try
                {
                    CommonMethod.DetermineCall(this, () =>
                    {
                        new FrmViewUrl
                        {
                            Url = StrLink,
                            WindowState = FormWindowState.Maximized,
                            StartPosition = FormStartPosition.CenterScreen,
                            Icon = FrmMain.FrmTool.Icon,
                            Text = _text
                        }.ShowDialog(this);
                    });
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
            }
        }

        #endregion

        #region 属性

        /// <summary>
        /// 决定控件重绘频率的计时器间隔
        /// </summary>
        public int TextScrollSpeed
        {
            set => _timer.Interval = value;
            get => _timer.Interval;
        }

        /// <summary>
        /// 平滑动画的滚动速度（每秒像素数）
        /// </summary>
        public float ScrollSpeed 
        { 
            set => _scrollSpeed = value; 
            get => _scrollSpeed; 
        }

        /// <summary>
        /// 将从右到左在控件上滚动的文本
        /// </summary>
        public string ScrollText
        {
            set
            {
                _text = value;
                // 从控件中间开始文本，实现从右到左的滚动效果
                // 这样文本可以立即可见，然后向左移动
                _staticTextPos = ClientSize.Width / 2;
                _yPos = 0;
                LoopTimes = 0;
                Invalidate();
            }
            get => _text;
        }

        /// <summary>
        /// 文本的垂直对齐方式
        /// </summary>
        public VerticleTextPosition VerticleTextPosition { set; get; } = VerticleTextPosition.Center;

        /// <summary>
        /// 开启或关闭边框
        /// </summary>
        public bool ShowBorder { set; get; } = false;

        /// <summary>
        /// 此文本的链接URL
        /// </summary>
        public string StrLink { set; get; } = string.Empty;

        /// <summary>
        /// 边框的颜色
        /// </summary>
        public Color BorderColor { set; get; } = Color.Black;

        /// <summary>
        /// 决定当用户鼠标移动到文本上时是否停止滚动
        /// </summary>
        public bool StopScrollOnMouseOver { set; get; } = false;

        /// <summary>
        /// 指示控件是否启用
        /// </summary>
        public new bool Enabled
        {
            set
            {
                _timer.Enabled = value;
                base.Enabled = value;
            }
            get => base.Enabled;
        }

        public Brush ForegroundBrush { set; get; } = null;

        public Brush BackgroundBrush { set; get; } = null;

        public int LoopTimes { get; internal set; }
        public int MaxLoopTimes { get; internal set; }

        #endregion
    }

    public enum VerticleTextPosition
    {
        Top,
        Center,
        Botom
    }

    [Obfuscation]
    public class ScrollEntity
    {
        [Obfuscation] public string LnkUrl { get; set; } = string.Empty;

        [Obfuscation] public Color ForeColor { get; set; } = Color.Black;

        [Obfuscation] public string Text { get; set; } = string.Empty;
    }
}