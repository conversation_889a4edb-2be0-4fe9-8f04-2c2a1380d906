﻿using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace OCRTools
{
    public class UserMsgHelper
    {
        public static void GetUserMsg()
        {
            var lstMsg = GetMsg();
            ProcessUserLevelMsg(lstMsg);
            //var plug = new UserMsgEntity()
            //{
            //    Id = Guid.NewGuid().ToString().Replace("-", ""),
            //    Content = "6月17日更新内容：\n重构【ChatGpt】，效率神器来了！\n\n1、实时性：流式响应，对话秒回复！\n2、连贯性：支持上下文，对话更顺畅！\n3、灵活性：支持多指令，无需等待！\n\n6.18大促进行中，感谢您的支持！\n　",
            //    ShowSecond = 600,
            //    IntervalHour = 123456
            //};
            //lstMsg = new List<UserMsgEntity>() { plug };
            //Console.WriteLine(CommonString.JavaScriptSerializer.Serialize(lstMsg));
            lstMsg?.ForEach(p =>
            {
                ProcessMsg(p);
            });
        }

        private static void ProcessUserLevelMsg(List<UserMsgEntity> lstMsg)
        {
            var config = CommonConfig.GetServerConfig();
            if (config == null || config.Count <= 0)
            {
                return;
            }

            var isLogined = Program.IsLogined();
            var isUserExpired = isLogined && Program.NowUser.DtExpired <= ServerTime.DateTime;
            var isReged = isLogined || !string.IsNullOrEmpty(CommonSetting.用户名);

            config.ForEach(mConfig =>
            {
                if (string.IsNullOrEmpty(mConfig.Content) || !mConfig.IsTipMsg || !CommonMethod.IsMatchUserLevel(mConfig.ShowLevel))
                {
                    return;
                }
                switch (mConfig.Id)
                {
                    case "ExpTipMsg":
                        if (isUserExpired || !isLogined)
                        {
                            return;
                        }
                        try
                        {
                            var ts = new TimeSpan(Program.NowUser.DtExpired.Ticks - ServerTime.DateTime.Ticks);
                            if (ts.TotalDays <= 0 || ts.TotalDays > 15)
                            {
                                return;
                            }
                            mConfig.Content = mConfig.Content
                                .Replace("[版本]", Program.NowUser.UserTypeName)
                                .Replace("[天数]", ts.TotalDays > 0 ? ts.TotalDays.ToString("F0") + "天后" : "");
                        }
                        catch
                        {
                            return;
                        }
                        break;
                    case "RegTipMsg":
                        if (isReged)
                        {
                            return;
                        }
                        break;
                    case "UpgradeMsg":
                        if (!isUserExpired)
                        {
                            return;
                        }
                        break;
                }
                lstMsg.Add(mConfig);
            });
        }

        private static void ProcessMsg(UserMsgEntity msg)
        {
            //获取消息失败
            if (msg == null || string.IsNullOrEmpty(msg.Id))
            {
                return;
            }
            //不满足会员等级条件，不展示
            if (!CommonMethod.IsMatchUserLevel(msg.ShowLevel))
            {
                return;
            }
            //获取上次展示时间
            var lastShowTimeStr = IniHelper.GetValue("UserMsg", msg.Id);
            if (!string.IsNullOrEmpty(lastShowTimeStr))
            {
                DateTime.TryParse(lastShowTimeStr, out DateTime dtLast);
                var nextTime = dtLast.AddHours(msg.IntervalHour <= 0 ? 6 : msg.IntervalHour);
                //未到展示时间，跳过
                if (nextTime > ServerTime.DateTime)
                {
                    return;
                }
            }
            msg.ShowSecond = msg.ShowSecond < 5 ? 5 : msg.ShowSecond;
            msg.Title = msg.Title + (string.IsNullOrEmpty(msg.Title) ? "" : "-") + CommonString.FullName;
            CommonMethod.DetermineCall(FrmMain.FrmTool, () =>
            {
                IniHelper.SetValue("UserMsg", msg.Id, ServerTime.DateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                CommonMethod.ShowNotificationTip(msg.Content, msg.Title, msg.ShowSecond * 1000
                    , msg.Font, msg.FontSize, msg.ForeColor, msg.BackColor, msg.Link);
            });
        }

        private static List<UserMsgEntity> GetMsg()
        {
            var lstMsg = new List<UserMsgEntity>();
            try
            {
                var result =
                    WebClientExt.GetHtml(CommonString.HostUpdate?.FullUrl + "update/umsg.txt?t=" + ServerTime.DateTime.Ticks, 5);
                if (!string.IsNullOrEmpty(result) && result.Length > 2)
                    lstMsg = CommonString.JavaScriptSerializer.Deserialize<List<UserMsgEntity>>(result);
            }
            catch { }
            return lstMsg;
        }
    }

    [Obfuscation]
    public class UserMsgEntity : BaseMsgEntity
    {
        [Obfuscation] public bool IsTipMsg { get; set; } = true;

        /// <summary>
        /// 内容
        /// </summary>
        [Obfuscation] public string Content { get; set; }

        /// <summary>
        /// 链接
        /// </summary>
        [Obfuscation] public string Link { get; set; }

        /// <summary>
        /// 展示时长
        /// </summary>
        [Obfuscation] public int ShowSecond { get; set; }

        /// <summary>
        /// 下次展示间隔小时数
        /// </summary>
        [Obfuscation] public double IntervalHour { get; set; }

    }
}