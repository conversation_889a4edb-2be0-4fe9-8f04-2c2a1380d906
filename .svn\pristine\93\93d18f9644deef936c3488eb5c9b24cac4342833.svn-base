﻿using System;
using System.Threading;

namespace OCRTools
{
    public class ServerTime
    {
        public static long OffSet;
        private static long _lastOffSet;

        private static bool _hasGetNtpDate;

        static ServerTime()
        {
            new Thread(p => { ProcessTimeOffSet(); })
            { Priority = ThreadPriority.Highest, IsBackground = true }.Start();
        }

        public static DateTime DateTime => DateTime.UtcNow.AddHours(8).AddTicks(OffSet);

        private static void ProcessTimeOffSet()
        {
            int sleepTime = 30 * 1000;
            while (!CommonString.IsExit)
            {
                long off;
                if (CommonString.IsOnLine)
                    off = SNtpClient.Instance.GetNetworkTimeOffset();
                else
                    off = -9999;
                if (off != -9999)
                {
                    //Console.WriteLine(string.Format("OffSet:{0},off:{1}", OffSet, off));
                    if (_lastOffSet != 0)
                    {
                        OffSet = (_lastOffSet + OffSet + off) / 3;
                        _lastOffSet = off;
                    }
                    else
                    {
                        OffSet = off;
                        _lastOffSet = off;
                    }

                    if (!_hasGetNtpDate) _hasGetNtpDate = true;
                    Thread.Sleep(sleepTime);
                }
                else
                {
                    Thread.Sleep(1000);
                }
            }
        }

        public static void SetHttpDate(DateTime dtHttp)
        {
            if (_hasGetNtpDate || dtHttp.Year < 1900)
                return;
            try
            {
                OffSet = dtHttp.Ticks - DateTime.Now.Ticks;
            }
            catch
            {
            }
        }
    }
}