﻿using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools
{
    public class ScrollMsg
    {
        private static int _nowMsgid = -1;

        private static List<ScrollEntity> _lstMsg = new List<ScrollEntity>();

        private static ScrollingText _srcTxt;

        private static bool _isLoopAllOnce;

        private static int _nLoopTimes;

        public static void Refresh()
        {
            if (_srcTxt == null || _srcTxt.Parent == null) return;
            if (_srcTxt.Parent is FrmMain) _srcTxt.BackColor = CommonSetting.Get默认背景颜色();
        }

        public static void ShowToWindow(Form owner, Point location, bool isOnce = false)
        {
            if (_srcTxt == null || _srcTxt.IsDisposed) Init();
            if (_srcTxt.Close && Equals(owner, _srcTxt.CloseOwner))
            {
                return;
            }
            _srcTxt.Visible = false;
            _isLoopAllOnce = isOnce;
            if (_nLoopTimes >= 1 && _isLoopAllOnce) return;
            _srcTxt.Parent = owner;
            _srcTxt.Location = location;
            if (owner is FrmMain)
            {
                _srcTxt.Anchor = AnchorStyles.Left | AnchorStyles.Right;// | AnchorStyles.Top | AnchorStyles.Bottom;
                _srcTxt.IsCanClose = true;
                _srcTxt.BackColor = CommonSetting.Get默认背景颜色();
                _srcTxt.Size = new Size(owner.Width - location.X * 2, 40);
            }
            else
            {
                _srcTxt.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
                _srcTxt.IsCanClose = false;
                _srcTxt.BackColor = owner.BackColor;
                if (owner is MetroFramework.Forms.MetroForm form)
                    _srcTxt.Size = new Size(owner.Width - form.LeftButtonRectangle.Width - 50 - location.X, 34);
                else
                    _srcTxt.Size = new Size(owner.Width - location.X, 34);
            }

            _srcTxt.Visible = true;
            _srcTxt.BringToFront();
        }

        public static void Hide(Form owner)
        {
            if (_srcTxt == null) return;
            _srcTxt.Parent = null;
        }

        private static void Init()
        {
            _srcTxt = new ScrollingText
            {
                ScrollText = "欢迎使用OCR文字识别助手，使用过程中如有问题或意见建议，请联系客服反馈！",
                VerticleTextPosition = VerticleTextPosition.Center,
                TextScrollSpeed = 60,
                TextScrollDistance = 2,
                MaxLoopTimes = 3,
                StopScrollOnMouseOver = false,
                ShowBorder = false,
                ScrollDirection = ScrollDirection.RightToLeft,
                Font = CommonString.GetSysBoldFont(18F),
                ForeColor = Color.Blue
            };
            Task.Factory.StartNew(ProcessMsg);
        }

        private static void ProcessMsg()
        {
            while (!CommonString.IsExit)
            {
                try
                {
                    if (_lstMsg == null || _lstMsg.Count <= 0) _lstMsg = GetLstMsg();
                    if (_lstMsg != null && _lstMsg.Count > 0)
                    {
                        _nowMsgid++;
                        if (_nowMsgid >= _lstMsg.Count)
                        {
                            _nLoopTimes++;
                            if (_nLoopTimes == 1 && _isLoopAllOnce) _srcTxt.Hide();
                        }

                        _nowMsgid = _nowMsgid <= 0 ? 0 : _nowMsgid;
                        _nowMsgid = _nowMsgid >= _lstMsg.Count ? 0 : _nowMsgid;
                        _srcTxt.ForeColor = _lstMsg[_nowMsgid].ForeColor;
                        _srcTxt.StrLink = _lstMsg[_nowMsgid].LnkUrl;
                        _srcTxt.ScrollText =
                            _lstMsg[_nowMsgid].Text + (string.IsNullOrEmpty(_srcTxt.StrLink) ? "" : "(点击查看详情)");
                        _srcTxt.StopScrollOnMouseOver = !string.IsNullOrEmpty(_srcTxt.StrLink);
                    }
                }
                catch
                {
                    _nowMsgid = -1;
                }

                var maxSecond =
                    _nowMsgid >= 0 && _lstMsg != null && _lstMsg.Count > 0 && _lstMsg[_nowMsgid].Text.Length >= 50
                        ? 120
                        : 60;
                var nowSecond = 0;
                while (!CommonString.IsExit)
                {
                    Thread.Sleep(1000);
                    nowSecond++;
                    if (nowSecond >= maxSecond || _srcTxt.LoopTimes >= _srcTxt.MaxLoopTimes) break;
                }
            }
        }

        private static List<ScrollEntity> GetLstMsg()
        {
            var list = new List<ScrollEntity>();
            try
            {
                var responseText =
                    WebClientExt.GetHtml(CommonString.HostUpdate?.FullUrl + "update/msg.txt?t=" + ServerTime.DateTime.Ticks, 5);
                if (!string.IsNullOrEmpty(responseText) && !responseText.StartsWith("<html"))
                {
                    var array = responseText.Split(new[] { "\n" }, StringSplitOptions.RemoveEmptyEntries);
                    if (array.Length > 0)
                        foreach (var str in array)
                        {
                            var ss = str.Trim().Split(new[] { "|" }, StringSplitOptions.RemoveEmptyEntries);
                            if (ss.Length > 0)
                            {
                                var entity = new ScrollEntity
                                {
                                    Text = ss[0]
                                };
                                try
                                {
                                    entity.ForeColor = Color.FromName(ss.Length > 1 ? ss[1] : "black");
                                }
                                catch
                                {
                                    entity.ForeColor = Color.Black;
                                }

                                entity.LnkUrl = ss.Length > 2 ? ss[2] : "";
                                list.Add(entity);
                            }
                        }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }

            var strHoliday = ChinaDate.GetChinaDate(ServerTime.DateTime);
            if (!string.IsNullOrEmpty(strHoliday))
                list.Insert(0, new ScrollEntity
                {
                    ForeColor = Color.Blue,
                    Text = string.Format("今天是{0:MM月dd日}{1}，祝您生活愉快！", ServerTime.DateTime, strHoliday)
                });

            return list;
        }
    }

    public class UserMsgHelper
    {
        public static void GetUserMsg()
        {
            var lstMsg = GetMsg();
            ProcessUserLevelMsg(lstMsg);
            //var plug = new UserMsgEntity()
            //{
            //    Id = Guid.NewGuid().ToString().Replace("-", ""),
            //    Content = "6月17日更新内容：\n重构【ChatGpt】，效率神器来了！\n\n1、实时性：流式响应，对话秒回复！\n2、连贯性：支持上下文，对话更顺畅！\n3、灵活性：支持多指令，无需等待！\n\n6.18大促进行中，感谢您的支持！\n　",
            //    ShowSecond = 600,
            //    IntervalHour = 123456
            //};
            //lstMsg = new List<UserMsgEntity>() { plug };
            //Console.WriteLine(CommonString.JavaScriptSerializer.Serialize(lstMsg));
            lstMsg?.ForEach(p =>
            {
                ProcessMsg(p);
            });
        }

        private static void ProcessUserLevelMsg(List<UserMsgEntity> lstMsg)
        {
            var config = CommonConfig.GetServerConfig();
            if (config == null || config.Count <= 0)
            {
                return;
            }

            var isLogined = Program.IsLogined();
            var isUserExpired = isLogined && Program.NowUser.DtExpired <= ServerTime.DateTime;
            var isReged = isLogined || !string.IsNullOrEmpty(CommonSetting.用户名);

            config.ForEach(mConfig =>
            {
                if (string.IsNullOrEmpty(mConfig.Content) || !mConfig.IsTipMsg || !CommonMethod.IsMatchUserLevel(mConfig.ShowLevel))
                {
                    return;
                }
                switch (mConfig.Id)
                {
                    case "ExpTipMsg":
                        if (isUserExpired || !isLogined)
                        {
                            return;
                        }
                        try
                        {
                            var ts = new TimeSpan(Program.NowUser.DtExpired.Ticks - ServerTime.DateTime.Ticks);
                            if (ts.TotalDays <= 0 || ts.TotalDays > 15)
                            {
                                return;
                            }
                            mConfig.Content = mConfig.Content
                                .Replace("[版本]", Program.NowUser.UserTypeName)
                                .Replace("[天数]", ts.TotalDays > 0 ? ts.TotalDays.ToString("F0") + "天后" : "");
                        }
                        catch
                        {
                            return;
                        }
                        break;
                    case "RegTipMsg":
                        if (isReged)
                        {
                            return;
                        }
                        break;
                    case "UpgradeMsg":
                        if (!isUserExpired)
                        {
                            return;
                        }
                        break;
                }
                lstMsg.Add(mConfig);
            });
        }

        private static void ProcessMsg(UserMsgEntity msg)
        {
            //获取消息失败
            if (msg == null || string.IsNullOrEmpty(msg.Id))
            {
                return;
            }
            //不满足会员等级条件，不展示
            if (!CommonMethod.IsMatchUserLevel(msg.ShowLevel))
            {
                return;
            }
            //获取上次展示时间
            var lastShowTimeStr = IniHelper.GetValue("UserMsg", msg.Id);
            if (!string.IsNullOrEmpty(lastShowTimeStr))
            {
                DateTime.TryParse(lastShowTimeStr, out DateTime dtLast);
                var nextTime = dtLast.AddHours(msg.IntervalHour <= 0 ? 6 : msg.IntervalHour);
                //未到展示时间，跳过
                if (nextTime > ServerTime.DateTime)
                {
                    return;
                }
            }
            msg.ShowSecond = msg.ShowSecond < 5 ? 5 : msg.ShowSecond;
            msg.Title = msg.Title + (string.IsNullOrEmpty(msg.Title) ? "" : "-") + CommonString.FullName;
            CommonMethod.DetermineCall(FrmMain.FrmTool, () =>
            {
                IniHelper.SetValue("UserMsg", msg.Id, ServerTime.DateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                CommonMethod.ShowNotificationTip(msg.Content, msg.Title, msg.ShowSecond * 1000
                    , msg.Font, msg.FontSize, msg.ForeColor, msg.BackColor, msg.Link);
            });
        }

        private static List<UserMsgEntity> GetMsg()
        {
            var lstMsg = new List<UserMsgEntity>();
            try
            {
                var result =
                    WebClientExt.GetHtml(CommonString.HostUpdate?.FullUrl + "update/umsg.txt?t=" + ServerTime.DateTime.Ticks, 5);
                if (!string.IsNullOrEmpty(result) && result.Length > 2)
                    lstMsg = CommonString.JavaScriptSerializer.Deserialize<List<UserMsgEntity>>(result);
            }
            catch { }
            return lstMsg;
        }
    }

    [Obfuscation]
    public class UserMsgEntity : BaseMsgEntity
    {
        [Obfuscation] public bool IsTipMsg { get; set; } = true;

        /// <summary>
        /// 内容
        /// </summary>
        [Obfuscation] public string Content { get; set; }

        /// <summary>
        /// 链接
        /// </summary>
        [Obfuscation] public string Link { get; set; }

        /// <summary>
        /// 展示时长
        /// </summary>
        [Obfuscation] public int ShowSecond { get; set; }

        /// <summary>
        /// 下次展示间隔小时数
        /// </summary>
        [Obfuscation] public double IntervalHour { get; set; }

    }
}