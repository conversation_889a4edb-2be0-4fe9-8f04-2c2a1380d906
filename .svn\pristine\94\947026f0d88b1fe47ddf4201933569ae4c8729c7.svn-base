﻿using System;
using System.Drawing;

namespace OCRTools.ScreenCaptureLib
{
    public class RectangleAnimation : BaseAnimation
    {
        public Rectangle FromRectangle { get; set; }
        public Rectangle ToRectangle { get; set; }
        public TimeSpan Duration { get; set; }

        public Rectangle CurrentRectangle { get; private set; }

        public override bool Update()
        {
            if (IsActive)
            {
                base.Update();

                float amount = (float)Timer.Elapsed.Ticks / Duration.Ticks;
                amount = Math.Min(amount, 1);

                int x = (int)MathHelpers.Lerp(FromRectangle.X, ToRectangle.X, amount);
                int y = (int)MathHelpers.Lerp(FromRectangle.Y, ToRectangle.Y, amount);
                int width = (int)MathHelpers.Lerp(FromRectangle.Width, ToRectangle.Width, amount);
                int height = (int)MathHelpers.Lerp(FromRectangle.Height, ToRectangle.Height, amount);

                CurrentRectangle = new Rectangle(x, y, width, height);

                if (amount >= 1)
                {
                    Stop();
                }
            }

            return IsActive;
        }
    }
}