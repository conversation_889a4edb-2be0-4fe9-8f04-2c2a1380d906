﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    [DesignerCategory("")]
    public class RulerBaseForm : Form
    {
        private const int MINIMAL_LENGTH = 350;

        public RulerBaseForm()
        {
            MouseDown += Form_MouseDown;
            MouseMove += Form_MouseMove;
            MouseUp += Form_MouseUp;
        }

        #region Sizing Restriction

        private int restrictSize;
        private RulerFormResizeMode resizeMode;
        private int cachedWidth, cachedHeight;

        public int RestrictSize
        {
            get => restrictSize;
            set
            {
                restrictSize = value;
                MinimumSize = new Size(value, value);
            }
        }

        public RulerFormResizeMode ResizeMode
        {
            get => resizeMode;
            set => applyResizeMode(value);
        }

        private void applyResizeMode(RulerFormResizeMode resizeMode)
        {
            this.resizeMode = resizeMode;
            var wRest = MaximumSize.Width < int.MaxValue && !MaximumSize.IsEmpty;
            var hRest = MaximumSize.Height < int.MaxValue && !MaximumSize.IsEmpty;
            if (!wRest) cachedWidth = Math.Max(MINIMAL_LENGTH, Width);
            if (!hRest) cachedHeight = Math.Max(MINIMAL_LENGTH, Height);
            switch (resizeMode)
            {
                case RulerFormResizeMode.Horizontal:
                    MaximumSize = new Size(int.MaxValue, RestrictSize);
                    if (wRest) Width = cachedWidth;
                    break;
                case RulerFormResizeMode.Vertical:
                    MaximumSize = new Size(RestrictSize, int.MaxValue);
                    if (hRest) Height = cachedHeight;
                    break;
                case RulerFormResizeMode.TwoDimensional:
                    MaximumSize = Size.Empty;
                    if (wRest) Width = cachedWidth;
                    if (hRest) Height = cachedHeight;
                    break;
            }

            CheckOutOfBounds();
            Invalidate();
        }

        protected void CheckOutOfBounds()
        {
            var screenRect = Screen.FromRectangle(Bounds).WorkingArea;
            // If the ruler got out of the visible area, move it back in
            if (!screenRect.IntersectsWith(Bounds))
            {
                var newLocation = Location;
                if (Location.X < screenRect.X)
                    newLocation.X = screenRect.X;
                else if (Location.X > screenRect.Right)
                    newLocation.X = screenRect.Right - Width;
                if (Location.Y < screenRect.Y)
                    newLocation.Y = screenRect.Y;
                else if (Location.Y >= screenRect.Bottom)
                    newLocation.Y = screenRect.Bottom - Height;
                Location = newLocation;
            }
        }

        #endregion

        // Handles dragging of the form

        #region Form Dragging

        private bool mouseDown;
        private Point mouseLoc;

        private void Form_MouseDown(object sender, MouseEventArgs e)
        {
            mouseDown = true;
            mouseLoc = e.Location;
        }

        private void Form_MouseMove(object sender, MouseEventArgs e)
        {
            if (mouseDown) Location = new Point(Location.X - mouseLoc.X + e.X, Location.Y - mouseLoc.Y + e.Y);
        }

        private void Form_MouseUp(object sender, MouseEventArgs e)
        {
            mouseDown = false;
        }

        #endregion
    }
}