﻿using OCRTools.Properties;
using System;
using System.IO;
using System.Reflection;
using System.Windows.Forms;

namespace OCRTools
{
    public static class ShortcutHelpers
    {
        public static bool SetShortcut(bool create, string shortcutPath, string targetPath = "", string arguments = "")
        {
            try
            {
                if (create)
                {
                    CreateSendToShortCut(shortcutPath, targetPath, arguments);
                }
                else
                {
                    Delete(shortcutPath);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            return false;
        }

        private static void CreateShortCut(string shortCutFile, string targetPath, string description = "")
        {
            var type = Type.GetTypeFromProgID("WScript.Shell");
            object instance = Activator.CreateInstance(type);
            var result = type.InvokeMember("CreateShortCut", BindingFlags.InvokeMethod, null, instance, new object[] { shortCutFile });

            type = result.GetType();
            type.InvokeMember("TargetPath", BindingFlags.SetProperty, null, result, new object[] { targetPath });
            type.InvokeMember("Description", BindingFlags.SetProperty, null, result, new object[] { description });
            type.InvokeMember("Save", BindingFlags.InvokeMethod, null, result, null);
        }

        private static void CreateSendToShortCut(string shortCutFileName, string targetPath, string description = "")
        {
            var sendToFolderPath = Environment.GetFolderPath(Environment.SpecialFolder.SendTo);
            var shortCutFile = Path.Combine(sendToFolderPath, shortCutFileName);
            CreateShortCut(shortCutFile, targetPath, description);
        }

        private static bool Delete(string shortcutPath)
        {
            if (!string.IsNullOrEmpty(shortcutPath) && File.Exists(shortcutPath))
            {
                File.Delete(shortcutPath);
                return true;
            }

            return false;
        }

        public static void RegFileType()
        {
            var updateFilePath = string.Format("{0}\\{2}-注册右键菜单-{1:yyyyMMddHHmmssfff}.exe", Path.GetTempPath().TrimEnd('\\'), ServerTime.DateTime, CommonString.ProductName);
            try
            {
                if (File.Exists(updateFilePath))
                    try
                    {
                        File.Delete(updateFilePath);
                    }
                    catch { }

                if (!File.Exists(updateFilePath))
                    try
                    {
                        File.WriteAllBytes(updateFilePath, Resources.RegSysMenu);
                    }
                    catch { }
                CommonString.RunAsAdmin(updateFilePath, $"\"{Application.ExecutablePath}\" " + CommonSetting.注册右键菜单, true, false);
            }
            catch
            {
                CommonString.RunAsAdmin(updateFilePath, $"\"{Application.ExecutablePath}\" " + CommonSetting.注册右键菜单, false, false);
            }
        }
    }
}
