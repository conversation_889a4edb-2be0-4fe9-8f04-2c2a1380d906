using System;
using System.Drawing;

namespace OCRTools
{
    internal class AutomaticCanvasRefresher : IDisposable
    {
        private readonly DrawArea _canvas;

        private readonly Func<Rectangle> _getBoundsFunc;

        private Rectangle _clipRectangle;

        public AutomaticCanvasRefresher(DrawArea canvas, Func<Rectangle> getBoundsFunc)
        {
            _canvas = canvas;
            _getBoundsFunc = getBoundsFunc;
            _clipRectangle = getBoundsFunc();
        }

        public void Dispose()
        {
            _clipRectangle = Rectangle.Union(_getBoundsFunc(), _clipRectangle);
            _canvas.RefreshCanvas(_clipRectangle);
        }
    }
}