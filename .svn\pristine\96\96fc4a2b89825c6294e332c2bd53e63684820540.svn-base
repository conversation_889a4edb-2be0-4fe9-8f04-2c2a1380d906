// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Runtime.InteropServices;
using UIAComWrapperInternal;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public static class Automation
    {
        private static IUIAutomation factory;
        public static readonly Condition ContentViewCondition = Condition.Wrap(Factory.ContentViewCondition);
        public static readonly Condition ControlViewCondition = Condition.Wrap(Factory.ControlViewCondition);
        public static readonly Condition RawViewCondition = Condition.Wrap(Factory.RawViewCondition);

        // Explicit static constructor to tell C# compiler
        // not to mark type as beforefieldinit
        static Automation()
        {
        }

        internal static IUIAutomation Factory
        {
            get
            {
                // Try CUIAutomation8
                if (factory == null)
                    try
                    {
                        factory = new CUIAutomation8Class();
                    }
                    catch (COMException)
                    {
                    }

                // Fall back to CUIAutomation
                if (factory == null) factory = new CUIAutomationClass();

                return factory;
            }
        }

        internal static IUIAutomation2 Factory2
        {
            get
            {
                var factory2 = Factory as IUIAutomation2;
                if (factory2 == null)
                    throw new NotImplementedException(
                        "Operation is not available without IUIAutomation2 support on OS");
                return factory2;
            }
        }

        public static bool Compare(int[] runtimeId1, int[] runtimeId2)
        {
            if (runtimeId1 == null && runtimeId2 == null) return true;
            if (runtimeId1 == null || runtimeId2 == null) return false;
            try
            {
                return Factory.CompareRuntimeIds(runtimeId1, runtimeId2) != 0;
            }
            catch (COMException e)
            {
                Exception newEx;
                if (Utility.ConvertException(e, out newEx))
                    throw newEx;
                throw;
            }
        }
    }
}