﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace OCRTools
{
    public class EncodingDetector
    {
        public static Encoding GetEncoding(string srcFile)
        {
            Encoding enc = null;
            try
            {
                enc = GetEncoding(srcFile, true);
            }
            catch { }
            return enc;
        }

        public static Encoding GetEncoding(string srcFile, bool thorough)
        {
            byte[] b = new byte[5];

            Encoding fileEnc = null;
            using (System.IO.FileStream file = new System.IO.FileStream(srcFile, System.IO.FileMode.Open, System.IO.FileAccess.Read, System.IO.FileShare.Read))
            {
                int numRead = file.Read(b, 0, 5);
                if (numRead < 5)
                    Array.Resize(ref b, numRead);
                fileEnc = new StreamReader(file).CurrentEncoding;
                file.Close();
            } // End Using file 

            if (b.Length >= 4 && b[0] == 0x00 && b[1] == 0x00 && b[2] == 0xFE && b[3] == 0xFF)
                return Encoding.GetEncoding("utf-32BE"); // UTF-32, big-endian 
            else if (b.Length >= 4 && b[0] == 0xFF && b[1] == 0xFE && b[2] == 0x00 && b[3] == 0x00)
                return Encoding.UTF32; // UTF-32, little-endian
            else if (b.Length >= 2 && b[0] == 0xFE && b[1] == 0xFF)
                return Encoding.BigEndianUnicode; // UTF-16, big-endian
            else if (b.Length >= 2 && b[0] == 0xFF && b[1] == 0xFE)
                return Encoding.Unicode; // UTF-16, little-endian
            else if (b.Length >= 3 && b[0] == 0xEF && b[1] == 0xBB && b[2] == 0xBF)
                return Encoding.UTF8;  // UTF-8
            else if (b.Length >= 3 && b[0] == 0x2b && b[1] == 0x2f && b[2] == 0x76)
                return Encoding.UTF7;  // UTF-7

            // Maybe there is a future encoding ...
            // PS: The above yields more than this - this doesn't find UTF7 ...
            if (thorough)
            {
                foreach (var ei in Encoding.GetEncodings())
                {
                    var enc = ei.GetEncoding();
                    byte[] preamble = enc.GetPreamble();
                    if (preamble.Length == 0)
                        continue;
                    if (preamble.Length > b.Length)
                        continue;
                    for (int i = 0; i < preamble.Length; ++i)
                    {
                        if (b[i] != preamble[i])
                        {
                            goto NextEncoding;
                        }
                    } // Next i 
                    return enc;
                    NextEncoding:
                    continue;
                } // Next ei
            } // End if (thorough)

            return fileEnc ?? GetSystemEncoding();
        } // End Function BomInfo 

        public static Encoding GetSystemEncoding()
        {
            // The OEM code page for use by legacy console applications
            // int oem = System.Globalization.CultureInfo.CurrentCulture.TextInfo.OEMCodePage;

            // The ANSI code page for use by legacy GUI applications
            // int ansi = System.Globalization.CultureInfo.InstalledUICulture.TextInfo.ANSICodePage; // Machine 
            int ansi = System.Globalization.CultureInfo.CurrentCulture.TextInfo.ANSICodePage; // User 

            try
            {
                // https://stackoverflow.com/questions/38476796/how-to-set-net-core-in-if-statement-for-compilation
#if ( NETSTANDARD && !NETSTANDARD1_0 )  || NETCORE || NETCOREAPP3_0 || NETCOREAPP3_1 
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
#endif
                Encoding enc = Encoding.GetEncoding(ansi);
                return enc;
            }
            catch { }


            try
            {
                foreach (EncodingInfo ei in Encoding.GetEncodings())
                {
                    Encoding e = ei.GetEncoding();

                    // 20'127: US-ASCII 
                    if (e.WindowsCodePage == ansi && e.CodePage != 20127)
                    {
                        return e;
                    }
                }
            }
            catch { }
            // return Encoding.GetEncoding("iso-8859-1");
            return Encoding.UTF8;
        } // End Function GetSystemEncoding 


    } // End Class 

}
