using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using Emgu.CV;
using Emgu.CV.CvEnum;
using Emgu.CV.Structure;
using Microsoft.ML.OnnxRuntime;
using Microsoft.ML.OnnxRuntime.Tensors;

namespace OcrLib
{
    internal class AngleNet
    {
        private readonly float[] MeanValues = new float[3] { 127.5f, 127.5f, 127.5f };

        private readonly float[] NormValues = new float[3]
        {
            2f / 255f,
            2f / 255f,
            2f / 255f
        };

        private const int angleDstWidth = 192;

        private const int angleDstHeight_PaddleOcr = 48;

        private const int angleDstHeight_ChineseLite = 32;

        private const int angleCols = 2;

        private InferenceSession angleNet;

        private List<string> inputNames;

        ~AngleNet()
        {
            angleNet.Dispose();
        }

        public void InitModel(string path, int numThread)
        {
            try
            {
                SessionOptions sessionOptions = new SessionOptions
                {
                    GraphOptimizationLevel = GraphOptimizationLevel.ORT_ENABLE_EXTENDED,
                    InterOpNumThreads = numThread,
                    IntraOpNumThreads = numThread
                };
                angleNet = new InferenceSession(path);
                inputNames = angleNet.InputMetadata.Keys.ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message + ex.StackTrace);
                throw ex;
            }
        }

        public List<Angle> GetAngles(List<Mat> partImgs, bool isPaddle, bool doAngle, bool mostAngle)
        {
            Angle[] angles = new Angle[partImgs.Count];
            Parallel.For(0, partImgs.Count, new ParallelOptions
            {
                MaxDegreeOfParallelism = Ocr.NMaxDegreeOfParallelism
            }, delegate (int index)
            {
                Angle angle;
                if (doAngle)
                {
                    angle = GetAngle(partImgs[index], isPaddle);
                }
                else
                {
                    angle = new Angle
                    {
                        Index = -1,
                    };
                }
                angles[index] = angle;
            });
            if (doAngle && mostAngle)
            {
                int mostAngleIndex = ((!(angles.Sum((Angle p) => p.Index) < angles.Length / 2f)) ? 1 : 0);
                Array.ForEach(angles, delegate (Angle x)
                {
                    x.Index = mostAngleIndex;
                });
            }
            return angles.ToList();
        }

        private Angle GetAngle(Mat src, bool isPaddle)
        {
            Angle result = new Angle();
            Mat angleImg;
            if (isPaddle)
            {
                angleImg = new Mat();
                CvInvoke.Resize(src, angleImg, new Size(192, 48));
            }
            else
            {
                angleImg = AdjustTargetImg(src, 192, 32);
            }
            Tensor<float> inputTensors = OcrUtils.SubstractMeanNormalize(angleImg, MeanValues, NormValues);
            List<NamedOnnxValue> inputs = new List<NamedOnnxValue> { NamedOnnxValue.CreateFromTensor(inputNames[0], inputTensors) };
            try
            {
                using (IDisposableReadOnlyCollection<DisposableNamedOnnxValue> source = angleNet.Run(inputs))
                {
                    float[] srcData = source.ToArray()[0].AsEnumerable<float>().ToArray();
                    return ScoreToAngle(srcData, 2);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message + ex.StackTrace);
            }
            return result;
        }

        private Mat AdjustTargetImg(Mat src, int dstWidth, int dstHeight)
        {
            Mat mat = new Mat();
            float num = (float)dstHeight / (float)src.Rows;
            int num2 = (int)((float)src.Cols * num);
            CvInvoke.Resize(src, mat, new Size(num2, dstHeight));
            Mat mat2 = new Mat(dstHeight, dstWidth, DepthType.Cv8U, 3);
            if (num2 < dstWidth)
            {
                CvInvoke.CopyMakeBorder(mat, mat2, 0, 0, 0, dstWidth - num2, BorderType.Isolated, new MCvScalar(255.0, 255.0, 255.0));
            }
            else
            {
                Rectangle roi = new Rectangle(0, 0, dstWidth, dstHeight);
                Mat mat3 = new Mat(mat, roi);
                mat3.CopyTo(mat2);
            }
            return mat2;
        }

        private Angle ScoreToAngle(float[] srcData, int angleCols)
        {
            int angleIndex = 0;
            float maxValue = -1000f;
            for (int i = 0; i < angleCols; i++)
            {
                if (i == 0)
                {
                    maxValue = srcData[i];
                }
                else if (srcData[i] > maxValue)
                {
                    angleIndex = i;
                    maxValue = srcData[i];
                }
            }
            return new Angle
            {
                Index = angleIndex,
            };
        }
    }
}
