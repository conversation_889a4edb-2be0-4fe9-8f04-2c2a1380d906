﻿using OCRTools.Common;
using OCRTools.ImgUpload;
using System;
using System.Collections.Specialized;
using System.IO;
using System.Net;

namespace ImageLib
{
    /// <summary>
    /// </summary>
    internal class BaiDuUpload : BaseImageUpload
    {
        public BaiDuUpload()
        {
            Name = "BaiDu";
        }

        public override string GetResult(byte[] content, bool isZip = false)
        {
            var result = "";
            var url = "https://ocr-image.bj.bcebos.com/";
            var key = Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds * 1000).ToString() + "0."
                + Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds * 1000).ToString("F5").Replace(".", "")
                + ".png";
            var file = new UploadFileInfo()
            {
                Name = "file",
                Filename = "1.png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            var vaules = new NameValueCollection() {
                    { "key", key }
                };
            try
            {
                var html = UploadFileRequest.Post(url, new[] { file }, vaules);
                key = url + key;
                using (var client = new CnnWebClient() { Method = "HEAD", Timeout = 10000 })
                {
                    client.OpenRead(key);
                    result = key;
                }
            }
            catch { }
            return result;
        }
    }
}
