﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;

namespace ImageLib
{
    public class ImageHelper
    {
        private const string StrUrlStart = "http";
        static List<int> listProcessEntity = new List<int>() { 1, 2, 3, 4, 5, 6 };

        public static string GetResult(byte[] content, string fileExt)
        {
            var result = string.Empty;
            var tsTotal = new Stopwatch();
            tsTotal.Start();

            CancellationTokenSource cts = new CancellationTokenSource();

            var m_AutoResetEvent = new AutoResetEvent(true);
            var lstTask = listProcessEntity.AsParallel().Select(imgType => Task.Factory.StartNew(() =>
            {
                var strTmp = "";
                if (!cts.IsCancellationRequested)
                {
                    var ts = new Stopwatch();
                    ts.Start();
                    switch (imgType)
                    {
                        case 1:
                            strTmp = ALiYunUpload.GetResult(content);
                            break;
                        case 2:
                            strTmp = SouGouImageUpload.GetResult(content);
                            break;
                        case 3:
                            strTmp = _360ImageUpload.GetResult(content);
                            break;
                        case 4:
                            strTmp = TinyPngUpload.GetResultUrl(content);
                            break;
                        case 5:
                            strTmp = WebResizerUpload.GetResult(content);
                            break;
                        case 6:
                            strTmp = Net126Upload.GetResult(content);
                            break;
                    }
                    Console.WriteLine(imgType + ":" + ts.ElapsedMilliseconds.ToString("F0"));
                    if (!string.IsNullOrEmpty(strTmp) && strTmp.ToLower().StartsWith(StrUrlStart))
                    {
                        result = strTmp;
                        try
                        {
                            m_AutoResetEvent.Set();
                            if (!cts.IsCancellationRequested)
                                cts.Cancel();
                        }
                        catch { }
                    }
                }

            })).ToArray();
            Task.WhenAll(lstTask);
            m_AutoResetEvent.WaitOne(15000);
            Console.WriteLine("Result:" + tsTotal.ElapsedMilliseconds.ToString("F0"));
            return result;
        }

    }
}
