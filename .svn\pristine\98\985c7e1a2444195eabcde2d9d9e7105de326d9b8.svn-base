﻿using MetroFramework.Components;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using MetroFramework.Native;
/**
 * MetroFramework - Modern UI for WinForms
 * 
 * The MIT License (MIT)
 * Copyright (c) 2011 <PERSON>, http://github.com/viperneo
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of 
 * this software and associated documentation files (the "Software"), to deal in the 
 * Software without restriction, including without limitation the rights to use, copy, 
 * modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, 
 * and to permit persons to whom the Software is furnished to do so, subject to the 
 * following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in 
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
 * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT 
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF 
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE 
 * OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
using System;
using System.ComponentModel;
using System.Drawing;
using System.Security;
using System.Windows.Forms;

namespace MetroFramework.Controls
{
    [ToolboxItem(false)]
    [Designer(typeof(Design.MetroTabPageDesigner), typeof(System.Windows.Forms.Design.ParentControlDesigner))]
    public class MetroTabPage : TabPage, IMetroControl
    {
        private MetroColorStyle metroStyle;

        private MetroThemeStyle metroTheme;

        private MetroStyleManager metroStyleManager;

        private bool useCustomBackColor;

        private bool useCustomForeColor;

        private bool useStyleColors;

        private MetroScrollBar verticalScrollbar = new MetroScrollBar(MetroScrollOrientation.Vertical);

        private MetroScrollBar horizontalScrollbar = new MetroScrollBar(MetroScrollOrientation.Horizontal);

        private bool showHorizontalScrollbar;

        private bool showVerticalScrollbar;

        [DefaultValue(MetroColorStyle.Blue)]
        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                if (base.DesignMode || metroStyle != 0)
                {
                    return metroStyle;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Style;
                }
                if (StyleManager == null)
                {
                    return MetroColorStyle.Blue;
                }
                return metroStyle;
            }
            set
            {
                metroStyle = value;
            }
        }

        [DefaultValue(MetroThemeStyle.Light)]
        [Category("Metro Appearance")]
        public MetroThemeStyle Theme
        {
            get
            {
                if (base.DesignMode || metroTheme != 0)
                {
                    return metroTheme;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Theme;
                }
                if (StyleManager == null)
                {
                    return MetroThemeStyle.Light;
                }
                return metroTheme;
            }
            set
            {
                metroTheme = value;
            }
        }

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public MetroStyleManager StyleManager
        {
            get
            {
                return metroStyleManager;
            }
            set
            {
                metroStyleManager = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomBackColor
        {
            get
            {
                return useCustomBackColor;
            }
            set
            {
                useCustomBackColor = value;
            }
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseCustomForeColor
        {
            get
            {
                return useCustomForeColor;
            }
            set
            {
                useCustomForeColor = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseStyleColors
        {
            get
            {
                return useStyleColors;
            }
            set
            {
                useStyleColors = value;
            }
        }

        [DefaultValue(false)]
        [Category("Metro Behaviour")]
        [Browsable(false)]
        public bool UseSelectable
        {
            get
            {
                return GetStyle(ControlStyles.Selectable);
            }
            set
            {
                SetStyle(ControlStyles.Selectable, value);
            }
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool HorizontalScrollbar
        {
            get
            {
                return showHorizontalScrollbar;
            }
            set
            {
                showHorizontalScrollbar = value;
            }
        }

        [Category("Metro Appearance")]
        public int HorizontalScrollbarSize
        {
            get
            {
                return horizontalScrollbar.ScrollbarSize;
            }
            set
            {
                horizontalScrollbar.ScrollbarSize = value;
            }
        }

        [Category("Metro Appearance")]
        public bool HorizontalScrollbarBarColor
        {
            get
            {
                return horizontalScrollbar.UseBarColor;
            }
            set
            {
                horizontalScrollbar.UseBarColor = value;
            }
        }

        [Category("Metro Appearance")]
        public bool HorizontalScrollbarHighlightOnWheel
        {
            get
            {
                return horizontalScrollbar.HighlightOnWheel;
            }
            set
            {
                horizontalScrollbar.HighlightOnWheel = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool VerticalScrollbar
        {
            get
            {
                return showVerticalScrollbar;
            }
            set
            {
                showVerticalScrollbar = value;
            }
        }

        [Category("Metro Appearance")]
        public int VerticalScrollbarSize
        {
            get
            {
                return verticalScrollbar.ScrollbarSize;
            }
            set
            {
                verticalScrollbar.ScrollbarSize = value;
            }
        }

        [Category("Metro Appearance")]
        public bool VerticalScrollbarBarColor
        {
            get
            {
                return verticalScrollbar.UseBarColor;
            }
            set
            {
                verticalScrollbar.UseBarColor = value;
            }
        }

        [Category("Metro Appearance")]
        public bool VerticalScrollbarHighlightOnWheel
        {
            get
            {
                return verticalScrollbar.HighlightOnWheel;
            }
            set
            {
                verticalScrollbar.HighlightOnWheel = value;
            }
        }

        [Category("Metro Appearance")]
        public new bool AutoScroll
        {
            get
            {
                return base.AutoScroll;
            }
            set
            {
                if (value)
                {
                    showHorizontalScrollbar = true;
                    showVerticalScrollbar = true;
                }
                base.AutoScroll = value;
            }
        }

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintBackground != null)
            {
                this.CustomPaintBackground(this, e);
            }
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaint != null)
            {
                this.CustomPaint(this, e);
            }
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintForeground != null)
            {
                this.CustomPaintForeground(this, e);
            }
        }

        public MetroTabPage()
        {
            SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.SupportsTransparentBackColor | ControlStyles.AllPaintingInWmPaint | ControlStyles.OptimizedDoubleBuffer, value: true);
            base.Controls.Add(verticalScrollbar);
            base.Controls.Add(horizontalScrollbar);
            verticalScrollbar.UseBarColor = true;
            horizontalScrollbar.UseBarColor = true;
            verticalScrollbar.Scroll += VerticalScrollbarScroll;
            horizontalScrollbar.Scroll += HorizontalScrollbarScroll;
        }

        private void HorizontalScrollbarScroll(object sender, ScrollEventArgs e)
        {
            base.AutoScrollPosition = new Point(e.NewValue, verticalScrollbar.Value);
            UpdateScrollBarPositions();
        }

        private void VerticalScrollbarScroll(object sender, ScrollEventArgs e)
        {
            base.AutoScrollPosition = new Point(horizontalScrollbar.Value, e.NewValue);
            UpdateScrollBarPositions();
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            try
            {
                Color color = BackColor;
                if (!useCustomBackColor)
                {
                    color = MetroPaint.BackColor.Form(Theme);
                }
                if (color.A == byte.MaxValue && BackgroundImage == null)
                {
                    e.Graphics.Clear(color);
                }
                else
                {
                    base.OnPaintBackground(e);
                    OnCustomPaintBackground(new MetroPaintEventArgs(color, Color.Empty, e.Graphics));
                }
            }
            catch
            {
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            try
            {
                if (GetStyle(ControlStyles.AllPaintingInWmPaint))
                {
                    OnPaintBackground(e);
                }
                OnCustomPaint(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
                OnPaintForeground(e);
            }
            catch
            {
                Invalidate();
            }
        }

        protected virtual void OnPaintForeground(PaintEventArgs e)
        {
            if (base.DesignMode)
            {
                horizontalScrollbar.Visible = false;
                verticalScrollbar.Visible = false;
                return;
            }
            UpdateScrollBarPositions();
            if (HorizontalScrollbar)
            {
                horizontalScrollbar.Visible = base.HorizontalScroll.Visible;
            }
            if (base.HorizontalScroll.Visible)
            {
                horizontalScrollbar.Minimum = base.HorizontalScroll.Minimum;
                horizontalScrollbar.Maximum = base.HorizontalScroll.Maximum;
                horizontalScrollbar.SmallChange = base.HorizontalScroll.SmallChange;
                horizontalScrollbar.LargeChange = base.HorizontalScroll.LargeChange;
            }
            if (VerticalScrollbar)
            {
                verticalScrollbar.Visible = base.VerticalScroll.Visible;
            }
            if (base.VerticalScroll.Visible)
            {
                verticalScrollbar.Minimum = base.VerticalScroll.Minimum;
                verticalScrollbar.Maximum = base.VerticalScroll.Maximum;
                verticalScrollbar.SmallChange = base.VerticalScroll.SmallChange;
                verticalScrollbar.LargeChange = base.VerticalScroll.LargeChange;
            }
            OnCustomPaintForeground(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
        }

        protected override void OnMouseWheel(MouseEventArgs e)
        {
            base.OnMouseWheel(e);
            verticalScrollbar.Value = base.VerticalScroll.Value;
            horizontalScrollbar.Value = base.HorizontalScroll.Value;
        }

        [SecuritySafeCritical]
        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m);
            if (!base.DesignMode)
            {
                WinApi.ShowScrollBar(base.Handle, 3, 0);
            }
        }

        private void UpdateScrollBarPositions()
        {
            if (base.DesignMode)
            {
                return;
            }
            if (!AutoScroll)
            {
                verticalScrollbar.Visible = false;
                horizontalScrollbar.Visible = false;
                return;
            }
            verticalScrollbar.Location = new Point(base.ClientRectangle.Width - verticalScrollbar.Width, base.ClientRectangle.Y);
            verticalScrollbar.Height = base.ClientRectangle.Height;
            if (!VerticalScrollbar)
            {
                verticalScrollbar.Visible = false;
            }
            horizontalScrollbar.Location = new Point(base.ClientRectangle.X, base.ClientRectangle.Height - horizontalScrollbar.Height);
            horizontalScrollbar.Width = base.ClientRectangle.Width;
            if (!HorizontalScrollbar)
            {
                horizontalScrollbar.Visible = false;
            }
        }
    }
}
