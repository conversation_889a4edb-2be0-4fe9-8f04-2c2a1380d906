﻿using MetroFramework.Forms;
using OCRTools.Common;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmBatch : MetroForm
    {
        private const string StrProcessFinished = "处理成功";
        private const string StrProcessFailed = "处理失败";
        private const string StrProcessing = "处理中…";

        internal static BlockingCollection<OcrContent> OcrResultPool = new BlockingCollection<OcrContent>();

        private readonly List<BatchOcrItem> ocrItems = new List<BatchOcrItem>();

        private readonly string StrOutDir =
            Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), Application.ProductName);

        private BindingList<BatchOcrItem> BindingItems;

        public FrmBatch()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            dgContent.AutoGenerateColumns = false;
            CheckForIllegalCrossThreadCalls = false;
        }

        private void FrmBatch_Load(object sender, EventArgs e)
        {
            InitOcrType();
            InitOcrOutType();
            OcrBatchResultProcessThread();
        }

        private void InitOcrType()
        {
            try
            {
                foreach (OCRType type in Enum.GetValues(typeof(OCRType)))
                {
                    if (CheckIsForbidOperate(type)) continue;
                    cmbOcrTypes.Items.Add(type.ToString());
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine("加载OCR类型失败！" + oe.Message);
            }

            if (cmbOcrTypes.SelectedIndex < 0) cmbOcrTypes.SelectedIndex = 0;
        }

        private bool CheckIsForbidOperate(OCRType ocrType)
        {
            var isForbid = false;
            if (Equals(ocrType, OCRType.竖排) && !(Program.NowUser?.IsSupportVertical == true)
                || Equals(ocrType, OCRType.表格) && !(Program.NowUser?.IsSupportTable == true)
                || Equals(ocrType, OCRType.公式) && !(Program.NowUser?.IsSupportMath == true)
                || Equals(ocrType, OCRType.翻译) && !(Program.NowUser?.IsSupportTranslate == true)
            )
                isForbid = true;

            return isForbid;
        }

        private void InitOcrOutType()
        {
            try
            {
                foreach (OcrFileType type in Enum.GetValues(typeof(OcrFileType)))
                    cmbOutExt.Items.Add(type.ToString().ToUpper());
            }
            catch (Exception oe)
            {
                Console.WriteLine("加载输出类型失败！" + oe.Message);
            }

            if (cmbOutExt.SelectedIndex < 0) cmbOutExt.SelectedIndex = cmbOcrTypes.Items.Count;
            cmbOutExt.Enabled = false;
        }

        private void btnClearFiles_Click(object sender, EventArgs e)
        {
            ocrItems.Clear();
            ReBindGridView();
        }

        private void btnClearSuccess_Click(object sender, EventArgs e)
        {
            ocrItems.RemoveAll(p => Equals(p.State, StrProcessFinished));
            ReBindGridView();
        }

        private void btnAddFiles_Click(object sender, EventArgs e)
        {
            var text = DateTime.Now.ToString("yyyyMMddhhmmss");
            var openFile = new OpenFileDialog
            {
                Title = "请选择文件",
                Filter = "图片或文档|*.png;*.jpg;*.jpeg;*.bmp;*.pdf;*.doc;*.docx;*.txt;",
                RestoreDirectory = true,
                Multiselect = true
            };
            if (openFile.ShowDialog(this) == DialogResult.OK && openFile.FileNames.Length > 0)
                foreach (var item in openFile.FileNames)
                {
                    if (ocrItems.Exists(p => Equals(p.FullName, item))) continue;
                    var ocrItem = new BatchOcrItem
                    {
                        Id = Guid.NewGuid().ToString(),
                        FullName = item,
                        FileName = Path.GetFileName(item),
                        FileType = (OcrFileType) Enum.Parse(typeof(OcrFileType), cmbOutExt.Text, true),
                        OcrType = (OCRType) Enum.Parse(typeof(OCRType), cmbOcrTypes.Text, true),
                        State = "待处理"
                    };
                    ocrItems.Add(ocrItem);
                }

            ReBindGridView();
        }

        private void ReBindGridView()
        {
            BindingItems = new BindingList<BatchOcrItem>(ocrItems);
            BindingItems.ListChanged += (_sender, _e) =>
            {
                if (_e.ListChangedType == ListChangedType.ItemDeleted
                    || _e.ListChangedType == ListChangedType.ItemAdded)
                    BindingItems.ResetBindings();
            };
            dgContent.DataSource = BindingItems;
            dgContent.Refresh();
        }

        private void btnProcess_Click(object sender, EventArgs e)
        {
            btnProcess.Text = "正在处理…";
            btnProcess.Enabled = false;
            Task.Factory.StartNew(() =>
            {
                try
                {
                    Parallel.ForEach(ocrItems,
                        new ParallelOptions {MaxDegreeOfParallelism = BoxUtil.GetInt32FromObject(txtPerCount.Text)},
                        item =>
                        {
                            if (Equals(item.State, StrProcessFinished)) return;
                            item.State = StrProcessing;
                            dgContent.InvalidateColumn(3);

                            FrmMain.DragDropEventDelegate.Invoke(new List<string> {item.FullName}, null, item.OcrType,
                                ProcessBy.批量识别, item.Id);

                            Thread.Sleep(new Random().Next(500, 2000));
                        });
                }
                catch
                {
                }

                btnProcess.Text = "开始识别(&S)";
                btnProcess.Enabled = true;
            });
        }

        private void OcrBatchResultProcessThread()
        {
            new Thread(t =>
                {
                    try
                    {
                        foreach (var content in OcrResultPool.GetConsumingEnumerable())
                            try
                            {
                                if (!string.IsNullOrEmpty(content.Identity))
                                {
                                    var strState = content?.result?.HasResult == true
                                        ? StrProcessFinished
                                        : StrProcessFailed;
                                    ocrItems.FindAll(p => Equals(p.Id, content.Identity)).ForEach(p =>
                                    {
                                        CommonResult.SaveFile(content, StrOutDir, Path.GetFileName(p.FullName));
                                        if (!Equals(p.State, StrProcessFinished))
                                            p.State = strState;
                                    });
                                    dgContent.InvalidateColumn(3);
                                }
                                else
                                {
                                    Console.WriteLine("");
                                }
                            }
                            catch (Exception oe)
                            {
                                Console.WriteLine(oe.Message);
                            }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
                })
                {IsBackground = true, Priority = ThreadPriority.Highest}.Start();
        }

        private void linkLabel1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            Process.Start(StrOutDir);
        }
    }

    [Obfuscation]
    internal class BatchOcrItem
    {
        private string _state;

        [Obfuscation] public string Id { get; set; }

        [Obfuscation] public string FileName { get; set; }

        [Obfuscation] public string FullName { get; set; }

        [Obfuscation] public OCRType OcrType { get; set; }

        [Obfuscation] public OcrFileType FileType { get; set; }

        [Obfuscation]
        public string State
        {
            get => _state;
            set
            {
                _state = value;
                NotifyPropertyChanged("State");
            }
        }

        [Obfuscation] public event PropertyChangedEventHandler PropertyChanged;

        [Obfuscation]
        private void NotifyPropertyChanged(string propertyName)
        {
            if (PropertyChanged != null)
                try
                {
                    PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
                }
                catch
                {
                    // ignored
                }
        }
    }
}