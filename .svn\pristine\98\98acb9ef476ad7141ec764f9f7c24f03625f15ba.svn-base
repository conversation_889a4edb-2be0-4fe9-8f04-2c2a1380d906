using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;

namespace OCRTools
{
    internal class DrawPolygon : DrawLinebase
    {
        public List<Point> pointArray;

        public DrawPolygon()
        {
            pointArray = new List<Point>();
            Initialize();
        }

        public DrawPolygon(int x1, int y1, int x2, int y2)
        {
            pointArray = new List<Point>
            {
                new Point(x1, y1),
                new Point(x2, y2)
            };
            Initialize();
        }

        public override DrawToolType NoteType => DrawToolType.Polygon;

        public override int HandleCount => pointArray.Count;

        public override DrawObject Clone()
        {
            var drawPolygon = new DrawPolygon();
            foreach (var item in pointArray) drawPolygon.pointArray.Add(item);
            FillDrawObjectFields(drawPolygon);
            return drawPolygon;
        }

        public override void Draw(Graphics g)
        {
            g.SmoothingMode = SmoothingMode.HighQuality;
            using (var pen = new Pen(Color, PenWidth))
            {
                using (var graphicsPath = new GraphicsPath())
                {
                    graphicsPath.AddCurve(pointArray.ToArray());
                    g.DrawPath(pen, graphicsPath);
                }
            }
        }

        public override Rectangle GetBoundingBox()
        {
            using (var graphicsPath = new GraphicsPath())
            {
                graphicsPath.AddCurve(pointArray.ToArray(), 7f);
                var bounds = graphicsPath.GetBounds();
                if (IsSelected)
                {
                    var num = 5.DpiValue();
                    bounds.Inflate(num, num);
                }

                return Rectangle.Ceiling(bounds);
            }
        }

        public void AddPoint(Point point)
        {
            pointArray.Add(point);
        }

        public override bool PointInObject(Point point)
        {
            CreateObjects();
            return AreaRegion.IsVisible(point);
        }

        protected new void Invalidate()
        {
            if (AreaPath != null)
            {
                AreaPath.Dispose();
                AreaPath = null;
            }

            if (AreaPen != null)
            {
                AreaPen.Dispose();
                AreaPen = null;
            }

            if (AreaRegion != null)
            {
                AreaRegion.Dispose();
                AreaRegion = null;
            }
        }

        protected new virtual void CreateObjects()
        {
            if (AreaPath == null)
            {
                AreaPath = new GraphicsPath();
                AreaPen = new Pen(Color.Black, 10f);
                AreaPath.AddCurve(pointArray.ToArray());
                try
                {
                    AreaPath.Widen(AreaPen);
                }
                catch (Exception)
                {
                }

                AreaRegion = new Region(AreaPath);
            }
        }

        public override Point GetHandle(int handleNumber)
        {
            if (handleNumber < 1) handleNumber = 1;
            if (handleNumber > pointArray.Count) handleNumber = pointArray.Count;
            return pointArray[handleNumber - 1];
        }

        public override void MoveHandleTo(Point point, int handleNumber)
        {
            if (handleNumber < 1) handleNumber = 1;
            if (handleNumber > pointArray.Count) handleNumber = pointArray.Count;
            pointArray[handleNumber - 1] = point;
            Invalidate();
        }

        public override void Move(int deltaX, int deltaY)
        {
            var count = pointArray.Count;
            for (var i = 0; i < count; i++)
            {
                var value = new Point(pointArray[i].X + deltaX, pointArray[i].Y + deltaY);
                pointArray[i] = value;
            }

            Invalidate();
        }
    }
}