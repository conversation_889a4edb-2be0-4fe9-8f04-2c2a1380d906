﻿using OCRTools.Units;
using System;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class CalibrationForm : BaseForm
    {
        private readonly RulerPainter _painter;
        private readonly Settings _previewSettings;

        public CalibrationForm(Settings settings)
        {
            InitializeComponent();
            _painter = new RulerPainter(panPreview);
            // we only copy relevant settings
            _previewSettings = new Settings
            {
                MeasuringUnit = settings.MeasuringUnit,
                MonitorDpi = settings.MonitorDpi,
                MonitorScaling = settings.MonitorScaling
            };
        }

        public float MonitorDpi => _previewSettings.MonitorDpi;
        public int MonitorScaling => _previewSettings.MonitorScaling;

        private void CalibrationForm_Load(object sender, EventArgs e)
        {
            // Set initial states
            numDPI.Value = (decimal)_previewSettings.MonitorDpi;
            numScaling.Value = _previewSettings.MonitorScaling;
            foreach (Enum item in Enum.GetValues(typeof(MeasuringUnit))) comUnits.Items.Add(item.ToString());
            comUnits.SelectedIndex = (int)_previewSettings.MeasuringUnit;
        }

        private void panPreview_Paint(object sender, PaintEventArgs e)
        {
            _painter.Update(e.Graphics, _previewSettings, RulerFormResizeMode.Horizontal);
            _painter.PaintRuler();
        }

        private void numDPI_ValueChanged(object sender, EventArgs e)
        {
            _previewSettings.MonitorDpi = (float)Math.Round(numDPI.Value, 2);
            panPreview.Invalidate();
        }

        private void numScaling_ValueChanged(object sender, EventArgs e)
        {
            _previewSettings.MonitorScaling = (int)numScaling.Value;
            panPreview.Invalidate();
        }

        private void comUnits_SelectedIndexChanged(object sender, EventArgs e)
        {
            _previewSettings.MeasuringUnit = (MeasuringUnit)comUnits.SelectedIndex;
            panPreview.Invalidate();
        }

        private void butSubmit_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
            Close();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
    }
}