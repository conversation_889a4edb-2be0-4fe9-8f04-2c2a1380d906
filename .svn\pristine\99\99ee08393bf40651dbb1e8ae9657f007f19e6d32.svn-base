﻿using MetroFramework.Forms;
using System;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmUserInfo : MetroForm
    {
        private UserTypeInfo _nextUserType;

        public FrmUserInfo()
        {
            InitializeComponent();
            CommonMethod.SetStyle(btnOpenVip, ControlStyles.Selectable, false);
            CommonMethod.SetStyle(lnkLogout, ControlStyles.Selectable, false);
            CommonMethod.SetStyle(lnkEditNickName, ControlStyles.Selectable, false);
            CommonMethod.SetStyle(linkLabel1, ControlStyles.Selectable, false);
            ShadowType = CommonString.CommonShadowType;
            lnkAccount.Text = Program.NowUser?.Account;
            lnkNickName.Text = Program.NowUser?.NickName;
            lnkUserType.Text = Program.NowUser?.UserTypeName;
            lnkRegDate.Text = string.Format("{0:yyyy-MM-dd}({1})", Program.NowUser?.DtExpired,
                Program.NowUser != null && ServerTime.DateTime < Program.NowUser.DtExpired
                    ? string.Format("剩{0:F2}天",
                        new TimeSpan(Program.NowUser.DtExpired.Ticks - ServerTime.DateTime.Ticks).TotalDays)
                    : "已过期");
            InitBtnText();
            //CommonMsg.ShowToWindow(this, new Point(136, 20));
        }

        private void InitBtnText()
        {
            _nextUserType = CommonUser.GetNextTypeStr();
            btnOpenVip.Text = _nextUserType.Name;
            btnOpenVip.Image = CommonUser.GetUserLevelImage(_nextUserType.Code);
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            if (CommonString.IsOnLine)
            {
                var frmBuy = new FrmGoBuy
                {
                    Icon = Icon,
                    Theme = Theme,
                    Style = Style,
                    StyleManager = StyleManager,
                    NextUserType = _nextUserType
                };
                frmBuy.ShowDialog(this);
                //CommonMsg.ShowToWindow(this, new Point(136, 20));
                //MessageBox.Show(this, "欢迎开通会员服务！\n识别速度更快，精度更高，功能更全面……\n敬请期待！", CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                CommonMethod.ShowHelpMsg("当前网络异常，请等待网络恢复后重试！");
            }
        }

        private void lnkLogout_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            Program.NowUser = null;
            Close();
        }

        private void lnkEditNickName_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            if (Program.NowUser == null || !Program.NowUser.IsLogined) return;
            if (!CommonString.IsOnLine)
            {
                CommonMethod.ShowHelpMsg("当前网络异常，请等待网络恢复后重试！");
                return;
            }

            if (lnkEditNickName.Text.Equals("编辑"))
            {
                lnkEditNickName.Text = "保存";
                txtNickName.Visible = true;
                lnkNickName.Visible = false;
                txtNickName.Text = lnkNickName.Text;
                txtNickName.SelectAll();
                txtNickName.Focus();
            }
            else
            {
                var nickName = txtNickName.Text.Trim();
                if (nickName.Length < 2 || nickName.Length > 20 ||
                    !new Regex(@"^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z0-9_])").IsMatch(nickName))
                {
                    MessageBox.Show(this, "昵称为2-15位的中英文数字字母或下划线！", CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
                    txtNickName.Focus();
                    return;
                }

                if (Program.NowUser == null) return;
                var result = Program.NowUser.NickName.Equals(nickName);
                var strMsg = "";
                if (!result)
                    result = OcrHelper.EditNickName(Program.NowUser?.Account, nickName, ref strMsg);
                if (result)
                {
                    Program.NowUser.NickName = nickName;
                    lnkEditNickName.Text = "编辑";
                    txtNickName.Visible = false;
                    lnkNickName.Visible = true;
                    lnkNickName.Text = txtNickName.Text;
                }
                else
                {
                    if (string.IsNullOrEmpty(strMsg)) strMsg = "注册失败，请稍候重试！";
                    MessageBox.Show(this, strMsg, CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void linkLabel1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            CommonMethod.OpenUrl("http://wpa.qq.com/msgrd?v=3&uin=*********&site=qq&menu=yes");
            CommonMethod.ShowHelpMsg("正在打开客服QQ:*********,如果打开失败，请尝试手动添加好友！", 5000);
        }
    }
}