using OCRTools.Common;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace OCRTools
{
    public class SelectRectangleList
    {
        public IntPtr IgnoreHandle { get; set; }
        public bool IncludeChildWindows { get; set; }

        private List<WindowInfo> windows;
        private HashSet<IntPtr> parentHandles;

        public void SetWindowZOrder(ConcurrentBag<WindowInfo> lstTmp)
        {
            if (!lstTmp.Any(p => p.IsWindow)) return;
            Parallel.ForEach(lstTmp.Where(p => p.IsWindow), new ParallelOptions { MaxDegreeOfParallelism = 5 },
                p => { p.ZIndex = GetWindowZOrder(p.<PERSON>le); });
            Parallel.ForEach(lstTmp.Where(p => !p.IsWindow), new ParallelOptions { MaxDegreeOfParallelism = 5 },
                p =>
                {
                    var parent = lstTmp.FirstOrDefault(q => q.IsWindow
                                                            && (Equals(q.<PERSON><PERSON>, p.ParentHandle) || Equals(q.<PERSON><PERSON><PERSON><PERSON>, p.ParentHandle)));
                    if (parent != null)
                        p.ZIndex = parent.ZIndex;
                });
        }

        private int GetWindowZOrder(IntPtr hWnd)
        {
            var zOrder = -1;
            while ((hWnd = NativeMethods.GetWindow(hWnd, 2 /* GW_HWNDNEXT */)) != IntPtr.Zero) zOrder++;
            return zOrder;
        }

        public ConcurrentBag<WindowInfo> GetWindowInfoListAsync(int timeout, IntPtr parent)
        {
            windows = new List<WindowInfo>();
            parentHandles = new HashSet<IntPtr>();

            try
            {
                var t = new Thread(() =>
                {
                    NativeMethods.EnumWindowsProc ewp = EvalWindow;
                    NativeMethods.EnumWindows(ewp, parent);
                });
                t.Start();

                if (!t.Join(timeout))
                {
                    t.Abort();
                }
            }
            catch { }

            var result = new ConcurrentBag<WindowInfo>(windows);
            return result;
        }

        private void AddWindowInfo(List<WindowInfo> lstT, WindowInfo wd)
        {
            if (wd.Rectangle.IsValid())
            {
                //if (PrimaryScreen.ScreenScalingFactor > 1)
                //{
                //    wd.Rectangle = PrimaryScreen.GetDisplayRectangle(wd.Rectangle);
                //    if (!wd.Rectangle.IsValid())
                //    {
                //        return;
                //    }
                //}
                if (!lstT.Any(q => q.Rectangle.Equals(wd.Rectangle)))
                {
                    if (wd.IsWindow && wd.Rectangle.Y + wd.Rectangle.Height < 10)
                    {
                        return;
                    }
                    lstT.Add(wd);
                }
            }
        }

        private bool EvalWindow(IntPtr handle, IntPtr lParam)
        {
            if (handle == IgnoreHandle || !NativeMethods.IsWindowVisible(handle) || NativeMethods.IsWindowCloaked(handle))
            {
                return true;
            }

            var windowInfo = new WindowInfo(handle) { IsWindow = true };

            if (!windowInfo.GetRectangle().IsValid())
            {
                return true;
            }

            if (IncludeChildWindows && !parentHandles.Contains(handle))
            {
                parentHandles.Add(handle);

                NativeMethods.EnumWindowsProc ewp = EvalControl;
                NativeMethods.EnumChildWindows(handle, ewp, handle);
            }

            var clientRect = NativeMethods.GetClientRect(handle);

            if (clientRect.IsValid() && clientRect != windowInfo.Rectangle)
            {
                var newWindow = new WindowInfo(handle, clientRect) { ParentHandle = handle, IsWindow = true };
                AddWindowInfo(windows, newWindow);
            }

            AddWindowInfo(windows, windowInfo);

            return true;
        }

        private bool EvalControl(IntPtr handle, IntPtr lParam)
        {
            if (!NativeMethods.IsWindowVisible(handle)) return true;
            var windowInfo = new WindowInfo(handle) { IsWindow = false, ParentHandle = lParam};
            if (!windowInfo.GetRectangle().IsValid()) return true;
            if (!parentHandles.Contains(handle))
            {
                parentHandles.Add(handle);
                NativeMethods.EnumWindowsProc lpEnumFunc = EvalControl;
                NativeMethods.EnumChildWindows(handle, lpEnumFunc, lParam);
            }

            AddWindowInfo(windows, windowInfo);
            return true;
        }
    }
}