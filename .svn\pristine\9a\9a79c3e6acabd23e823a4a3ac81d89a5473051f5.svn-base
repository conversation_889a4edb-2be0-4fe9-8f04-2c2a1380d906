﻿using MetroFramework;
using MetroFramework.Components;
using MetroFramework.Drawing;
using OCRTools.Common;
using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class TaskThumbnailPanel : UserControl
    {
        private Rectangle _dragBoxFromMouseDown;

        private MetroColorStyle _metroStyle;

        private MetroThemeStyle _metroTheme;

        private bool _selected;

        private Size _thumbnailSize;

        private string _title;

        private ThumbnailTitleLocation _titleLocation;

        private bool _titleVisible = true;

        public TaskThumbnailPanel(HistoryTask task)
        {
            Task = task;

            InitializeComponent();
            UpdateTheme();
            UpdateTitle();
        }

        public HistoryTask Task { get; }

        public bool Selected
        {
            get => _selected;
            set
            {
                if (_selected != value)
                {
                    _selected = value;

                    pThumbnail.Selected = _selected;
                }
            }
        }

        public string Title
        {
            get => _title;
            set
            {
                _title = value;

                if (lblTitle.Text != _title) lblTitle.Text = _title;
            }
        }

        public bool TitleVisible
        {
            get => _titleVisible;
            set
            {
                if (_titleVisible != value)
                {
                    _titleVisible = value;
                    lblTitle.Visible = _titleVisible;
                    UpdateLayout();
                }
            }
        }

        public ThumbnailTitleLocation TitleLocation
        {
            get => _titleLocation;
            set
            {
                if (_titleLocation != value)
                {
                    _titleLocation = value;
                    pThumbnail.StatusLocation = value;
                    UpdateLayout();
                }
            }
        }

        public bool ThumbnailExists { get; private set; }

        public Size ThumbnailSize
        {
            get => _thumbnailSize;
            set
            {
                if (_thumbnailSize != value)
                {
                    _thumbnailSize = value;

                    UpdateLayout();
                }
            }
        }

        public bool ThumbnailSupportsClick { get; private set; }

        [Category("Metro Appearance")]
        [DefaultValue(MetroColorStyle.蓝色)]
        public MetroColorStyle Style
        {
            get
            {
                if (DesignMode || _metroStyle != 0) return _metroStyle;
                if (StyleManager != null) return StyleManager.Style;
                if (StyleManager == null) return MetroColorStyle.蓝色;
                return _metroStyle;
            }
            set => _metroStyle = value;
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroThemeStyle.Light)]
        public MetroThemeStyle Theme
        {
            get
            {
                if (DesignMode || _metroTheme != 0) return _metroTheme;
                if (StyleManager != null) return StyleManager.Theme;
                if (StyleManager == null) return MetroThemeStyle.Light;
                return _metroTheme;
            }
            set => _metroTheme = value;
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        public MetroStyleManager StyleManager { get; set; }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomBackColor { get; set; }

        public new event EventHandler MouseEnter
        {
            add
            {
                base.MouseEnter += value;
                lblTitle.MouseEnter += value;
                pThumbnail.MouseEnter += value;
                pbThumbnail.MouseEnter += value;
            }
            remove
            {
                base.MouseEnter -= value;
                lblTitle.MouseEnter -= value;
                pThumbnail.MouseEnter -= value;
                pbThumbnail.MouseEnter -= value;
            }
        }

        public new event MouseEventHandler MouseDown
        {
            add
            {
                base.MouseDown += value;
                lblTitle.MouseDown += value;
                pThumbnail.MouseDown += value;
                pbThumbnail.MouseDown += value;
            }
            remove
            {
                base.MouseDown -= value;
                lblTitle.MouseDown -= value;
                pThumbnail.MouseDown -= value;
                pbThumbnail.MouseDown -= value;
            }
        }

        public new event MouseEventHandler MouseUp
        {
            add
            {
                base.MouseUp += value;
                lblTitle.MouseUp += value;
                pThumbnail.MouseUp += value;
                pbThumbnail.MouseUp += value;
            }
            remove
            {
                base.MouseUp -= value;
                lblTitle.MouseUp -= value;
                pThumbnail.MouseUp -= value;
                pbThumbnail.MouseUp -= value;
            }
        }

        public void UpdateTheme()
        {
            // 设置主题背景色
            // 设置主题背景色
            if (!UseCustomBackColor)
            {
                lblTitle.ForeColor = MetroPaint.ForeColor.Button.Normal(Theme);
                lblTitle.TextShadowColor = Color.Transparent;
                pThumbnail.PanelColor = MetroPaint.BackColor.Button.Normal(Theme);
                ttMain.BackColor = MetroPaint.BackColor.Button.Hover(Theme);
                ttMain.ForeColor = MetroPaint.ForeColor.Button.Press(Theme);
            }
            else
            {
                lblTitle.ForeColor = SystemColors.ControlText;
                lblTitle.TextShadowColor = Color.Transparent;
                pThumbnail.PanelColor = SystemColors.ControlLight;
                ttMain.BackColor = SystemColors.Window;
                ttMain.ForeColor = SystemColors.ControlText;
            }
        }

        public void UpdateTitle()
        {
            Title = Task.Info?.FileName;

            if (Task.Info != null && !string.IsNullOrEmpty(Task.Info.ToString()))
            {
                lblTitle.Cursor = Cursors.Hand;
                ttMain.SetToolTip(lblTitle, Task.Info.ToString());
            }
            else
            {
                lblTitle.Cursor = Cursors.Default;
                ttMain.SetToolTip(lblTitle, null);
            }
        }

        private void UpdateLayout()
        {
            lblTitle.Width = pThumbnail.Padding.Horizontal + ThumbnailSize.Width;
            pThumbnail.Size = new Size(pThumbnail.Padding.Horizontal + ThumbnailSize.Width,
                pThumbnail.Padding.Vertical + ThumbnailSize.Height);
            var panelHeight = pThumbnail.Height;
            if (TitleVisible) panelHeight += lblTitle.Height + 2;
            Size = new Size(pThumbnail.Width, panelHeight);

            if (TitleLocation == ThumbnailTitleLocation.Top)
            {
                lblTitle.Location = new Point(0, 0);

                if (TitleVisible)
                    pThumbnail.Location = new Point(0, lblTitle.Height + 2);
                else
                    pThumbnail.Location = new Point(0, 0);

                lblError.Location = new Point((ClientSize.Width - lblError.Width) / 2, 1);
            }
            else
            {
                pThumbnail.Location = new Point(0, 0);
                lblTitle.Location = new Point(0, pThumbnail.Height + 2);
                lblError.Location = new Point((ClientSize.Width - lblError.Width) / 2,
                    pThumbnail.Height - lblError.Height - 1);
            }
        }

        public void UpdateThumbnail(Bitmap bmp = null)
        {
            ClearThumbnail();

            if (!ThumbnailSize.IsEmpty && Task.Info != null)
                try
                {
                    var filePath = Task.Info.FilePath;

                    if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                    {
                        ThumbnailSupportsClick = true;
                        pbThumbnail.Cursor = Cursors.Hand;
                    }

                    var bmpResult = CreateThumbnail(filePath, bmp);

                    if (bmpResult != null)
                    {
                        pbThumbnail.Image = bmpResult;

                        ThumbnailExists = true;
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
        }

        private Bitmap CreateThumbnail(string filePath, Bitmap bmp = null)
        {
            if (bmp != null) return ImageProcessHelper.ResizeImage(bmp, ThumbnailSize, false);

            if (string.IsNullOrEmpty(filePath)) filePath = Task.Info.FileName;
            if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                using (var bmpResult = ImageProcessHelper.LoadImage(filePath))
                {
                    if (bmpResult != null) return ImageProcessHelper.ResizeImage(bmpResult, ThumbnailSize, false);
                }

            return null;
        }

        public void ClearThumbnail()
        {
            var temp = pbThumbnail.Image;
            pbThumbnail.Image = null;

            if (temp != null && temp != pbThumbnail.ErrorImage && temp != pbThumbnail.InitialImage) temp.Dispose();

            ThumbnailSupportsClick = false;
            pbThumbnail.Cursor = Cursors.Default;

            ThumbnailExists = false;
        }

        private void LblTitle_MouseClick(object sender, MouseEventArgs e)
        {
            if (ModifierKeys == Keys.None && e.Button == MouseButtons.Left) MouseClickEvent(true);
        }

        private void lblError_MouseClick(object sender, MouseEventArgs e)
        {
            if (ModifierKeys == Keys.None && e.Button == MouseButtons.Left)
            {
                //if (!string.IsNullOrEmpty(Task?.ErrorMsg))
                //    CommonMethod.ShowHelpMsg(Task?.ErrorMsg);
            }
        }

        private void PbThumbnail_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                var dragSize = new Size(10, 10);
                _dragBoxFromMouseDown = new Rectangle(new Point(e.X - dragSize.Width / 2, e.Y - dragSize.Height / 2),
                    dragSize);
            }
        }

        private void PbThumbnail_MouseUp(object sender, MouseEventArgs e)
        {
            _dragBoxFromMouseDown = Rectangle.Empty;
        }

        private void PbThumbnail_MouseClick(object sender, MouseEventArgs e)
        {
            if (ThumbnailSupportsClick && ModifierKeys == Keys.None && e.Button == MouseButtons.Left)
                MouseClickEvent(false);
        }

        public Image GetImage()
        {
            Image image = null;
            if (!string.IsNullOrEmpty(Task.Info.Url))
                try
                {
                    image = Image.FromStream(new MemoryStream(Convert.FromBase64String(Task.Info.Url)));
                }
                catch
                {
                }
            else if (!string.IsNullOrEmpty(Task.Info.FilePath) && File.Exists(Task.Info.FilePath))
                try
                {
                    image = Image.FromFile(Task.Info.FilePath);
                }
                catch
                {
                }

            return image;
        }

        public void ViewImage()
        {
            try
            {
                var bitmap = GetImage();
                this.ViewImage(bitmap);
            }
            catch
            {
            }
        }

        public void EditImage()
        {
            try
            {
                var bitmap = GetImage();
                CommonMethod.EditImage(bitmap);
            }
            catch
            {
            }
        }

        public void OpenUrl()
        {
            if (!string.IsNullOrEmpty(Task.Info.Url))
                try
                {
                    Process.Start(Task.Info.Url);
                }
                catch
                {
                }
        }

        public void OpenFile(bool isOpenLocation)
        {
            if (!string.IsNullOrEmpty(Task.Info.FilePath) && File.Exists(Task.Info.FilePath))
            {
                if (isOpenLocation)
                    CommonMethod.OpenFolderWithFile(Task.Info.FilePath);
                else
                    CommonMethod.OpenFile(Task.Info.FilePath);
            }
        }

        private void MouseClickEvent(bool isTitle)
        {
            if (Task.Info != null)
                switch (Task.Info.DataType)
                {
                    case EDataType.Image:
                        ViewImage();
                        break;
                    case EDataType.Text:
                        break;
                    case EDataType.Url:
                        OpenUrl();
                        break;
                    default:
                        OpenFile(isTitle);
                        break;
                }
        }

        private void PbThumbnail_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left && _dragBoxFromMouseDown != Rectangle.Empty &&
                !_dragBoxFromMouseDown.Contains(e.X, e.Y))
            {
                if (Task.Info != null && !string.IsNullOrEmpty(Task.Info.FilePath) && File.Exists(Task.Info.FilePath))
                {
                    IDataObject dataObject = new DataObject(DataFormats.FileDrop, new[] { Task.Info.FilePath });
                    _dragBoxFromMouseDown = Rectangle.Empty;
                    pbThumbnail.DoDragDrop(dataObject, DragDropEffects.Copy | DragDropEffects.Move);
                }
                else
                {
                    _dragBoxFromMouseDown = Rectangle.Empty;
                }
            }
        }

        private void TtMain_Draw(object sender, DrawToolTipEventArgs e)
        {
            e.DrawBackground();
            e.DrawBorder();
            e.DrawText();
        }
    }

    public class HistoryTask
    {
        public HistoryItem Info { get; set; }
        public TaskStatus Status { get; set; }
    }

    public enum TaskStatus
    {
        InQueue,
        Preparing,
        Working,
        Stopping,
        Stopped,
        Failed,
        Completed,
        History
    }

    public class HistoryItem
    {
        private string _filePath;

        public string FilePath
        {
            get => _filePath;
            set
            {
                _filePath = value;

                FileName = string.IsNullOrEmpty(_filePath) ? "" : Path.GetFileName(_filePath);
            }
        }

        public string FileName { get; set; }

        public string Url { get; set; }

        public EDataType DataType { get; set; }

        public DateTime CreateTime { get; set; }

        public override string ToString()
        {
            return string.IsNullOrEmpty(FilePath) ? string.IsNullOrEmpty(FileName) ? Url : FileName : FilePath;
        }
    }

    public enum EDataType
    {
        Default,
        File,
        Image,
        Text,
        Url
    }
}