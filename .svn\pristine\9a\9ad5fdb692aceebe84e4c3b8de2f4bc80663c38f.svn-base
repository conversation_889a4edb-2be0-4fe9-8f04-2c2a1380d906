﻿using MetroFramework.Forms;
using OCRTools.Properties;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Reflection;
using System.Text;
using System.Web;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmGoBuy : MetroForm
    {
        private List<UserType> _lstUserTypes = new List<UserType>();

        public UserTypeEnum NextUserType;

        private ChargeViewToUser _nowSelectedChargeType;

        private UserType _nowSelectedType;

        public FrmGoBuy()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            if (!CommonString.IsOnLine)
            {
                CommonMethod.ShowHelpMsg("当前网络异常，请等待网络恢复后重试！");
                return;
            }

            if (_nowSelectedType == null)
            {
                CommonMethod.ShowHelpMsg("请选择要升级的账户类型后重试！");
                return;
            }

            //if (NowSelectedType.Type == Program.NowUser?.UserType)
            //{
            //    CommonMethod.ShowHelpMsg(string.Format("当前已经是{0}，祝您使用愉快！", NowSelectedType.Type.ToString()));
            //    return;
            //}
            //http://t.cn/AimZvjI8
            pnlPay.Dock = DockStyle.Fill;
            pnlPay.Visible = true;
            pnlPay.BringToFront();
            var url = CommonString.HostAccountUrl + "?t=" + ServerTime.DateTime.Ticks + "&desc=" +
                      HttpUtility.UrlEncode(string.Format("{0}{1},共{2}元", _nowSelectedChargeType?.Name,
                          _nowSelectedType.Type, _nowSelectedChargeType?.Price.ToString("F0") ?? ""));
            wbPay.Navigate(url);
            CommonMethod.ShowHelpMsg("支付完成后，发送付款截图及要开通的版本给客服，祝您使用愉快！", 4000);
        }

        private void FrmGoBuy_Load(object sender, EventArgs e)
        {
            // LoadInfo
            _lstUserTypes = OcrHelper.GetCanRegUserTypes();
            if (_lstUserTypes != null)
                //RadioButton rdoCheck = null;
                _lstUserTypes.ForEach(p =>
                {
                    var radioButton = new RadioButton
                    {
                        Text = p.Type.ToString(),
                        Tag = p,
                        AutoSize = true,
                        Font = new Font(btnOpenVip.Font.FontFamily, 10)
                    };
                    radioButton.CheckedChanged += RadioButton_CheckedChanged;
                    if (Equals(p.Type, NextUserType)) radioButton.Checked = true;
                    //if (Program.NowUser?.UserType == p.Type)
                    //{
                    //    radioButton.Enabled = false;
                    //}
                    pnlUserType.Controls.Add(radioButton);
                });
            //if (rdoCheck == null && pnlUserType.Controls.Count > 0)
            //{
            //    rdoCheck = (pnlUserType.Controls[0] as RadioButton);
            //}
            //if (rdoCheck != null)
            //    rdoCheck.Checked = true;
            CommonMsg.ShowToWindow(this, new Point(136, 20));
            var url = CommonString.HostAccountUrl + "/desc.aspx?t=" + ServerTime.DateTime.Ticks;
            wbDesc.Navigate(url);
        }

        private void RadioButton_CheckedChanged(object sender, EventArgs e)
        {
            if (sender == null) return;
            _nowSelectedType = (sender as RadioButton)?.Tag as UserType;
            pnlPayType.Controls.Clear();
            _nowSelectedChargeType = null;
            _nowSelectedType?.UserChargeType?.ForEach(p =>
            {
                var radio = new RadioButton
                {
                    Text = p.Name,
                    AutoSize = true,
                    Tag = p,
                    Font = new Font(btnOpenVip.Font.FontFamily, 9)
                };
                radio.CheckedChanged += rdoByYear_CheckedChanged;
                if (!string.IsNullOrEmpty(p.Tag))
                {
                    radio.TextImageRelation = TextImageRelation.TextBeforeImage;
                    try
                    {
                        radio.Image = Resources.ResourceManager.GetObject(p.Tag) as Bitmap;
                    }
                    catch
                    {
                    }
                }

                pnlPayType.Controls.Add(radio);
                if (p.IsDefault) radio.Checked = true;
            });
        }

        private void rdoByYear_CheckedChanged(object sender, EventArgs e)
        {
            _nowSelectedChargeType = (sender as RadioButton)?.Tag as ChargeViewToUser;
            btnOpenVip.Text = _nowSelectedChargeType?.Desc ?? "-";
        }
    }

    [Obfuscation]
    public class UserType
    {
        [Obfuscation] public UserTypeEnum Type { get; set; }

        /// <summary>
        ///     是否显示其他处理结果
        /// </summary>
        [Obfuscation]
        public bool IsSetOtherResult { get; set; }

        /// <summary>
        ///     是否支持文本识别
        /// </summary>
        [Obfuscation]
        public bool IsSupportTxt { get; set; } = true;

        ///// <summary>
        ///// 是否支持PDF识别
        ///// </summary>
        //public bool IsSupportPDF { get; set; }

        /// <summary>
        ///     是否支持图片翻译
        /// </summary>
        [Obfuscation]
        public bool IsSupportTranslate { get; set; }

        /// <summary>
        ///     是否支持图片文件识别
        /// </summary>
        [Obfuscation]
        public bool IsSupportImageFile { get; set; }

        /// <summary>
        ///     是否支持文档翻译
        /// </summary>
        [Obfuscation]
        public bool IsSupportDocFile { get; set; }

        /// <summary>
        ///     能否批量处理文件
        /// </summary>
        [Obfuscation]
        public bool IsSupportBatch { get; set; }

        /// <summary>
        ///     是否支持竖排识别
        /// </summary>
        [Obfuscation]
        public bool IsSupportVertical { get; set; }

        /// <summary>
        ///     是否支持数学公式识别
        /// </summary>
        [Obfuscation]
        public bool IsSupportMath { get; set; }

        /// <summary>
        ///     是否支持表格识别
        /// </summary>
        [Obfuscation]
        public bool IsSupportTable { get; set; }

        [Obfuscation] public List<ChargeViewToUser> UserChargeType { get; set; }

        [Obfuscation] public int MaxLoginCount { get; set; }

        public string ToDesc()
        {
            var sb = new StringBuilder();
            //sb.AppendFormat("【{0}】", Type.ToString());
            //sb.AppendLine("价格：");
            //sb.AppendFormat("{0}元/月，", PerPriceMonth.ToString("F0"));
            //sb.AppendFormat("{0}元/季，", (PerPriceMonth * 3 * QuarDiscount).ToString("F0"));
            //sb.AppendFormat("{0}元/年，", (PerPriceMonth * 12 * YearDiscount).ToString("F0"));
            sb.AppendLine("功能对比："); //√×
            if (IsSupportTxt) sb.AppendLine("截图：√");
            if (IsSupportImageFile) sb.AppendLine("图片：√");
            if (IsSupportVertical)
                sb.AppendLine("竖排：√");
            else
                sb.AppendLine("竖排：×");
            if (IsSupportMath)
                sb.AppendLine("公式：√");
            else
                sb.AppendLine("公式：×");
            if (IsSupportTranslate)
                sb.AppendLine("翻译：√");
            else
                sb.AppendLine("翻译：×");
            if (IsSupportTable)
                sb.AppendLine("表格：√");
            else
                sb.AppendLine("表格：×");
            if (IsSetOtherResult)
                sb.AppendLine("多结果：√");
            else
                sb.AppendLine("多结果：×");
            if (MaxLoginCount > 1)
                sb.AppendLine("多账号：√");
            else
                sb.AppendLine("多账号：×");
            //if (IsSupportPDF)
            //{
            //    sb.AppendLine("PDF：√");
            //}
            //else
            //{
            //    sb.AppendLine("PDF：×");
            //}
            if (IsSupportBatch)
                sb.AppendLine("批量：√");
            else
                sb.AppendLine("批量：×");
            if (IsSupportDocFile)
                sb.AppendLine("文档：√");
            else
                sb.AppendLine("文档：×");
            return sb.ToString();
        }
    }

    [Obfuscation]
    public class ChargeViewToUser
    {
        [Obfuscation] public string Name { get; set; }

        [Obfuscation] public string Desc { get; set; }

        [Obfuscation] public double Price { get; set; }

        [Obfuscation] public bool IsDefault { get; set; }

        [Obfuscation] public string Tag { get; set; }
    }

    [Obfuscation]
    public enum UserTypeEnum
    {
        内测版 = -1,
        体验版 = 0,
        专业版 = 1,
        企业版 = 2,
        旗舰版 = 3
    }
}