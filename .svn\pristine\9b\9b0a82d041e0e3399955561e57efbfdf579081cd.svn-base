﻿using MetroFramework.Components;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
/**
 * MetroFramework - Modern UI for WinForms
 * 
 * The MIT License (MIT)
 * Copyright (c) 2011 <PERSON>, http://github.com/viperneo
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of 
 * this software and associated documentation files (the "Software"), to deal in the 
 * Software without restriction, including without limitation the rights to use, copy, 
 * modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, 
 * and to permit persons to whom the Software is furnished to do so, subject to the 
 * following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in 
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
 * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT 
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF 
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE 
 * OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Windows.Forms;

namespace MetroFramework.Controls
{
    [Designer(typeof(Design.MetroTileDesigner), typeof(System.Windows.Forms.Design.ParentControlDesigner))]
    [ToolboxBitmap(typeof(Button))]
    public class MetroTile : Button, IMetroControl, IContainerControl
    {
        private MetroColorStyle metroStyle;

        private MetroThemeStyle metroTheme;

        private MetroStyleManager metroStyleManager;

        private bool useCustomBackColor;

        private bool useCustomForeColor;

        private bool useStyleColors;

        private Control activeControl;

        private bool paintTileCount = true;

        private int tileCount;

        private Image tileImage;

        private bool useTileImage;

        private ContentAlignment tileImageAlign = ContentAlignment.TopLeft;

        private MetroTileTextSize tileTextFontSize = MetroTileTextSize.Medium;

        private MetroTileTextWeight tileTextFontWeight;

        private bool isHovered;

        private bool isPressed;

        private bool isFocused;

        [DefaultValue(MetroColorStyle.Blue)]
        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                if (base.DesignMode || metroStyle != 0)
                {
                    return metroStyle;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Style;
                }
                if (StyleManager == null)
                {
                    return MetroColorStyle.Blue;
                }
                return metroStyle;
            }
            set
            {
                metroStyle = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroThemeStyle.Light)]
        public MetroThemeStyle Theme
        {
            get
            {
                if (base.DesignMode || metroTheme != 0)
                {
                    return metroTheme;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Theme;
                }
                if (StyleManager == null)
                {
                    return MetroThemeStyle.Light;
                }
                return metroTheme;
            }
            set
            {
                metroTheme = value;
            }
        }

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public MetroStyleManager StyleManager
        {
            get
            {
                return metroStyleManager;
            }
            set
            {
                metroStyleManager = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomBackColor
        {
            get
            {
                return useCustomBackColor;
            }
            set
            {
                useCustomBackColor = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomForeColor
        {
            get
            {
                return useCustomForeColor;
            }
            set
            {
                useCustomForeColor = value;
            }
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseStyleColors
        {
            get
            {
                return useStyleColors;
            }
            set
            {
                useStyleColors = value;
            }
        }

        [Category("Metro Behaviour")]
        [DefaultValue(false)]
        [Browsable(false)]
        public bool UseSelectable
        {
            get
            {
                return GetStyle(ControlStyles.Selectable);
            }
            set
            {
                SetStyle(ControlStyles.Selectable, value);
            }
        }

        [Browsable(false)]
        public Control ActiveControl
        {
            get
            {
                return activeControl;
            }
            set
            {
                activeControl = value;
            }
        }

        [DefaultValue(true)]
        [Category("Metro Appearance")]
        public bool PaintTileCount
        {
            get
            {
                return paintTileCount;
            }
            set
            {
                paintTileCount = value;
            }
        }

        [DefaultValue(0)]
        public int TileCount
        {
            get
            {
                return tileCount;
            }
            set
            {
                tileCount = value;
            }
        }

        [DefaultValue(ContentAlignment.BottomLeft)]
        public new ContentAlignment TextAlign
        {
            get
            {
                return base.TextAlign;
            }
            set
            {
                base.TextAlign = value;
            }
        }

        [DefaultValue(null)]
        [Category("Metro Appearance")]
        public Image TileImage
        {
            get
            {
                return tileImage;
            }
            set
            {
                tileImage = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseTileImage
        {
            get
            {
                return useTileImage;
            }
            set
            {
                useTileImage = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(ContentAlignment.TopLeft)]
        public ContentAlignment TileImageAlign
        {
            get
            {
                return tileImageAlign;
            }
            set
            {
                tileImageAlign = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroTileTextSize.Medium)]
        public MetroTileTextSize TileTextFontSize
        {
            get
            {
                return tileTextFontSize;
            }
            set
            {
                tileTextFontSize = value;
                Refresh();
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroTileTextWeight.Light)]
        public MetroTileTextWeight TileTextFontWeight
        {
            get
            {
                return tileTextFontWeight;
            }
            set
            {
                tileTextFontWeight = value;
                Refresh();
            }
        }

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintBackground != null)
            {
                this.CustomPaintBackground(this, e);
            }
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaint != null)
            {
                this.CustomPaint(this, e);
            }
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintForeground != null)
            {
                this.CustomPaintForeground(this, e);
            }
        }

        public bool ActivateControl(Control ctrl)
        {
            if (base.Controls.Contains(ctrl))
            {
                ctrl.Select();
                activeControl = ctrl;
                return true;
            }
            return false;
        }

        public MetroTile()
        {
            SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.AllPaintingInWmPaint | ControlStyles.OptimizedDoubleBuffer, value: true);
            TextAlign = ContentAlignment.BottomLeft;
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            try
            {
                Color color = BackColor;
                if (!useCustomBackColor)
                {
                    color = MetroPaint.GetStyleColor(Style);
                }
                if (color.A == byte.MaxValue)
                {
                    e.Graphics.Clear(color);
                }
                else
                {
                    base.OnPaintBackground(e);
                    OnCustomPaintBackground(new MetroPaintEventArgs(color, Color.Empty, e.Graphics));
                }
            }
            catch
            {
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            try
            {
                if (GetStyle(ControlStyles.AllPaintingInWmPaint))
                {
                    OnPaintBackground(e);
                }
                OnCustomPaint(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
                OnPaintForeground(e);
            }
            catch
            {
                Invalidate();
            }
        }

        protected virtual void OnPaintForeground(PaintEventArgs e)
        {
            Color color = MetroPaint.BorderColor.Button.Normal(Theme);
            Color foreColor = (isHovered && !isPressed && base.Enabled) ? MetroPaint.ForeColor.Tile.Hover(Theme) : ((isHovered && isPressed && base.Enabled) ? MetroPaint.ForeColor.Tile.Press(Theme) : (base.Enabled ? MetroPaint.ForeColor.Tile.Normal(Theme) : MetroPaint.ForeColor.Tile.Disabled(Theme)));
            if (useCustomForeColor)
            {
                foreColor = ForeColor;
            }
            if (isPressed || isHovered || isFocused)
            {
                using (Pen pen = new Pen(color))
                {
                    pen.Width = 3f;
                    Rectangle rect = new Rectangle(1, 1, base.Width - 3, base.Height - 3);
                    e.Graphics.DrawRectangle(pen, rect);
                }
            }
            e.Graphics.SmoothingMode = SmoothingMode.HighQuality;
            e.Graphics.CompositingQuality = CompositingQuality.HighQuality;
            if (useTileImage && tileImage != null)
            {
                Rectangle rect2;
                switch (tileImageAlign)
                {
                    case ContentAlignment.BottomLeft:
                        rect2 = new Rectangle(new Point(0, base.Height - TileImage.Height), new Size(TileImage.Width, TileImage.Height));
                        break;
                    case ContentAlignment.BottomCenter:
                        rect2 = new Rectangle(new Point(base.Width / 2 - TileImage.Width / 2, base.Height - TileImage.Height), new Size(TileImage.Width, TileImage.Height));
                        break;
                    case ContentAlignment.BottomRight:
                        rect2 = new Rectangle(new Point(base.Width - TileImage.Width, base.Height - TileImage.Height), new Size(TileImage.Width, TileImage.Height));
                        break;
                    case ContentAlignment.MiddleLeft:
                        rect2 = new Rectangle(new Point(0, base.Height / 2 - TileImage.Height / 2), new Size(TileImage.Width, TileImage.Height));
                        break;
                    case ContentAlignment.MiddleCenter:
                        rect2 = new Rectangle(new Point(base.Width / 2 - TileImage.Width / 2, base.Height / 2 - TileImage.Height / 2), new Size(TileImage.Width, TileImage.Height));
                        break;
                    case ContentAlignment.MiddleRight:
                        rect2 = new Rectangle(new Point(base.Width - TileImage.Width, base.Height / 2 - TileImage.Height / 2), new Size(TileImage.Width, TileImage.Height));
                        break;
                    case ContentAlignment.TopLeft:
                        rect2 = new Rectangle(new Point(0, 0), new Size(TileImage.Width, TileImage.Height));
                        break;
                    case ContentAlignment.TopCenter:
                        rect2 = new Rectangle(new Point(base.Width / 2 - TileImage.Width / 2, 0), new Size(TileImage.Width, TileImage.Height));
                        break;
                    case ContentAlignment.TopRight:
                        rect2 = new Rectangle(new Point(base.Width - TileImage.Width, 0), new Size(TileImage.Width, TileImage.Height));
                        break;
                    default:
                        rect2 = new Rectangle(new Point(0, 0), new Size(TileImage.Width, TileImage.Height));
                        break;
                }
                e.Graphics.DrawImage(TileImage, rect2);
            }
            if (TileCount > 0 && paintTileCount)
            {
                Size size = TextRenderer.MeasureText(TileCount.ToString(), MetroFonts.TileCount);
                e.Graphics.TextRenderingHint = TextRenderingHint.AntiAlias;
                TextRenderer.DrawText(e.Graphics, TileCount.ToString(), MetroFonts.TileCount, new Point(base.Width - size.Width, 0), foreColor);
                e.Graphics.TextRenderingHint = TextRenderingHint.SystemDefault;
            }
            TextRenderer.MeasureText(Text, MetroFonts.Tile(tileTextFontSize, tileTextFontWeight));
            TextFormatFlags flags = MetroPaint.GetTextFormatFlags(TextAlign) | TextFormatFlags.LeftAndRightPadding | TextFormatFlags.EndEllipsis;
            Rectangle clientRectangle = base.ClientRectangle;
            if (isPressed)
            {
                clientRectangle.Inflate(-4, -12);
            }
            else
            {
                clientRectangle.Inflate(-2, -10);
            }
            TextRenderer.DrawText(e.Graphics, Text, MetroFonts.Tile(tileTextFontSize, tileTextFontWeight), clientRectangle, foreColor, flags);
        }

        protected override void OnGotFocus(EventArgs e)
        {
            isFocused = true;
            isHovered = true;
            Invalidate();
            base.OnGotFocus(e);
        }

        protected override void OnLostFocus(EventArgs e)
        {
            isFocused = false;
            isHovered = false;
            isPressed = false;
            Invalidate();
            base.OnLostFocus(e);
        }

        protected override void OnEnter(EventArgs e)
        {
            isFocused = true;
            isHovered = true;
            Invalidate();
            base.OnEnter(e);
        }

        protected override void OnLeave(EventArgs e)
        {
            isFocused = false;
            isHovered = false;
            isPressed = false;
            Invalidate();
            base.OnLeave(e);
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Space)
            {
                isHovered = true;
                isPressed = true;
                Invalidate();
            }
            base.OnKeyDown(e);
        }

        protected override void OnKeyUp(KeyEventArgs e)
        {
            Invalidate();
            base.OnKeyUp(e);
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            isHovered = true;
            Invalidate();
            base.OnMouseEnter(e);
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                isPressed = true;
                Invalidate();
            }
            base.OnMouseDown(e);
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            isPressed = false;
            Invalidate();
            base.OnMouseUp(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            if (!isFocused)
            {
                isHovered = false;
            }
            Invalidate();
            base.OnMouseLeave(e);
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);
            Invalidate();
        }
    }
}
