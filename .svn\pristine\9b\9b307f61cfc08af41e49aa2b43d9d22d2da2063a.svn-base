﻿using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Properties;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class UCContent : UserControl
    {
        private BiaoDianMode _currentBiaoDianModel;

        private SpiltMode _currentSpiltModel;

        private bool _isShowOldContent;

        private Color contentBackColor = Color.FromArgb(240, 255, 240);
        private ContextMenuStrip menuStrip;

        internal UCContent()
        {
            SetStyle(
                ControlStyles.UserPaint | ControlStyles.DoubleBuffer | ControlStyles.OptimizedDoubleBuffer |
                ControlStyles.AllPaintingInWmPaint, true);
            DoubleBuffered = true;
            InitializeComponent();
            CommonMethod.EnableDoubleBuffering(this);

            dgContent.NotShowSequence = true;
            txtContent.LanguageOption = RichTextBoxLanguageOptions.UIFonts;
            txtContent.AllowDrop = true;
            txtContent.AutoWordSelection = false;

            wbContent.AllowWebBrowserDrop = true;
            wbContent.ScriptErrorsSuppressed = true; //禁用错误脚本提示
            wbContent.IsWebBrowserContextMenuEnabled = true; //禁用右键菜单
            wbContent.WebBrowserShortcutsEnabled = false; //禁用快捷键
            //wbContent.ScrollBarsEnabled = false;//禁止滚动条
            wbContent.Navigating += WbContent_Navigating;

            pnlPicText.Parent = this;
            pnlPicText.Dock = DockStyle.Fill;
            pnlPicText.ZoomChanged += PnlPicText_ZoomChanged;
            pnlPicText.SizeChanged += PnlPicText_SizeChanged;

            toolResize.Parent = this;
            //Scroll += PnlPicTxt_Scroll;
        }

        internal OcrContent OcrContent { get; set; }

        public Image Image { get; set; }

        internal SpiltMode SpiltModel
        {
            get => _currentSpiltModel;
            set
            {
                if (_currentSpiltModel != value)
                {
                    _currentSpiltModel = value;
                    BindContentByOcr(OcrContent);
                }
            }
        }

        internal BiaoDianMode BiaoDianMode
        {
            get => _currentBiaoDianModel;
            set
            {
                if (_currentBiaoDianModel != value)
                {
                    _currentBiaoDianModel = value;
                    BindContentByOcr(OcrContent);
                }
            }
        }

        public bool IsShowOldContent
        {
            get => _isShowOldContent;
            set
            {
                if (_isShowOldContent != value)
                {
                    _isShowOldContent = value;
                    BindContentByOcr(OcrContent);
                }
            }
        }

        internal KeyEventHandler TxtKeyDownEventDelegate
        {
            set
            {
                txtContent.KeyDown -= value;
                pnlPicText.KeyDown -= value;

                txtContent.KeyDown += value;
                pnlPicText.KeyDown += value;
            }
        }

        internal ContextMenuStrip MenuStrip
        {
            get => menuStrip;
            set
            {
                menuStrip = value;
                dgContent.ContextMenuStrip = menuStrip;
                txtContent.ContextMenuStrip = menuStrip;
                pnlPicText.ContextMenuStrip = menuStrip;
            }
        }

        private void PnlPicText_SizeChanged(object sender, EventArgs e)
        {
            toolResize.Location = new Point((int)((ClientRectangle.Width - toolResize.Width) * 1.0 / 2)
                , ClientRectangle.Height - toolResize.Height
                                         - (pnlPicText.HorizontalScroll.Visible
                                             ? SystemInformation.HorizontalScrollBarHeight
                                             : 0) - 5);
            toolResize.BringToFront();
        }

        private void PnlPicText_ZoomChanged(object sender, EventArgs e)
        {
            if (pnlPicText.Zoom == 100)
            {
                tsmPicOrigin.Image = Resources.固定区域;
                tsmPicOrigin.ToolTipText = "最佳缩放";
            }
            else
            {
                tsmPicOrigin.Image = Resources.原始;
                tsmPicOrigin.ToolTipText = "原始尺寸";
            }

            txtPicZoomPercent.Text = string.Format("{0}%", pnlPicText.Zoom);
        }

        private void WbContent_Navigating(object sender, WebBrowserNavigatingEventArgs e)
        {
            if (e.Url != null)
                if (e.Url.ToString().StartsWith("file:"))
                {
                    e.Cancel = true;
                    var files = new List<string> { e.Url.ToString().Replace("file:///", "") };
                    FrmMain.DragDropEventDelegate?.Invoke(files, null, null, ProcessBy.主界面, null);
                }
        }

        internal void SetDragDrop()
        {
            this.ControlUseDrop();
            pnlPicText.ControlUseDrop();
            txtContent.ControlUseDrop();
            dgContent.ControlUseDrop();
        }

        public void RefreshStyle()
        {
            txtContent.BackColor = CommonSetting.默认背景颜色;
            txtContent.Font = CommonSetting.默认文字字体;
            txtContent.ForeColor = CommonSetting.默认文字颜色;
            dgContent.BackgroundColor = CommonSetting.默认背景颜色;
            dgContent.Font = CommonSetting.默认文字字体;
            dgContent.ForeColor = CommonSetting.默认文字颜色;
        }

        internal void BindContentByStr(string strContent, bool isAppend = false)
        {
            if (isAppend)
                txtContent.AppendText(Environment.NewLine + strContent);
            else
                txtContent.Text = strContent;
            txtContent.BringToFront();
            Application.DoEvents();
        }

        internal void BindContentByOcr(OcrContent ocr, bool isAppend = false, bool? isShowTxt = false)
        {
            if (isShowTxt.HasValue) IsShowTxt = isShowTxt.Value;
            OcrContent = ocr;
            if (ocr?.result == null) return;
            var resuType = ocr.result.resultType;
            switch (resuType)
            {
                //ProcessShowControl(resuType);
                case ResutypeEnum.表格:
                    {
                        var dt = CommonResult.GetTableContent(OcrContent.result);
                        CommonMethod.DetermineCall(this, delegate
                         {
                             dgContent.DataSource = dt;
                             dgContent.AutoResizeColumns();
                             dgContent.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.DisplayedCellsExceptHeaders;
                             foreach (DataGridViewColumn column in dgContent.Columns)
                             {
                                 column.HeaderText = "";
                                 column.MinimumWidth = 80;
                             }

                             dgContent.BringToFront();
                         });
                        break;
                    }
                case ResutypeEnum.网页:
                    CommonMethod.DetermineCall(this, delegate
                    {
                        wbContent.DocumentText = "";
                        wbContent.BringToFront();
                        var url = OCRHelper.GetFileResultUrl(OcrContent);
                        wbContent.Url = new Uri(url);
                    });
                    break;
                default:
                    CommonMethod.DetermineCall(this, delegate
                    {
                        if (OcrContent.ocrType == OCRType.公式)
                        {
                            wbContent.DocumentText = "";
                            wbContent.BringToFront();
                            var url = OCRHelper.GetMathFileResultUrl(OcrContent);
                            wbContent.Url = new Uri(url);
                        }
                        else
                        {
                            if (!isAppend)
                                txtContent.BringToFront();
                            //txtContent.BackColor = ContentBackColor;
                            //txtContent.Font = ContentFont;
                            var contentStr = GetTextByContent(isAppend)?.TrimEnd() + Environment.NewLine + Environment.NewLine;
                            if (isAppend)
                                txtContent.Text += contentStr;
                            else
                                txtContent.Text = contentStr;
                            if (!isAppend)
                            {
                                pnlPicText.Clear();
                                var isVertical = IsCanVertical();
                                if (isVertical)
                                {
                                    tsmPicView.Visible = true;
                                    if (NowDisplayMode == DisplayModel.图文模式) BindPicTxt();
                                }

                                toolResize.Visible = isVertical;
                                if (isVertical) SetDisplayMode(NowDisplayMode);
                            }
                        }
                    });
                    break;
            }
        }

        //private SizeF GetFontSizeByGraphicsMeasure(string measuredString, Font BaseFont)
        //{
        //    using (Graphics gh = this.CreateGraphics())
        //    {
        //        SizeF sf = TextRenderer.MeasureText(gh, measuredString, BaseFont, new Size(10000000, 1000000), CommonString.BaseTextFormatFlags);
        //        //SizeF sf = gh.MeasureString(measuredString, BaseFont, CommonString.PointZero, CommonString.BaseStringFormat);
        //        return sf;
        //    }
        //}

        //private Font FindFont(Graphics g, string measuredString, Font PreferedFont, Size Room)
        //{
        //    SizeF RealSize = g.MeasureString(measuredString, PreferedFont, CommonString.PointZero, CommonString.BaseStringFormat);
        //    float HeightScaleRatio = Room.Height / RealSize.Height;
        //    float WidthScaleRatio = Room.Width / RealSize.Width;
        //    float ScaleRatio = (HeightScaleRatio < WidthScaleRatio) ? HeightScaleRatio : WidthScaleRatio;
        //    float ScaleFontSize = PreferedFont.Size * ScaleRatio;
        //    return new Font(PreferedFont.FontFamily, ScaleFontSize, PreferedFont.Style, GraphicsUnit.Pixel);
        //}

        //private Font GetFontByGraphicsMeasure(Graphics gh, string measuredString, Font BaseFont, ref Size baseSize)
        //{
        //    float size = 5;
        //    BaseFont = new Font(BaseFont.FontFamily, size, FontStyle.Regular, GraphicsUnit.Point);
        //    SizeF sf = gh.MeasureString(measuredString, BaseFont, CommonString.PointZero, CommonString.BaseStringFormat);
        //    if (sf.Width < baseSize.Width * (baseSize.Height * 1.0 / sf.Height))
        //    {
        //        while (sf.Width < baseSize.Width * (baseSize.Height * 1.0 / sf.Height))
        //        {
        //            size += 0.2F;
        //            BaseFont = new Font(BaseFont.FontFamily, size, BaseFont.Style, BaseFont.Unit);
        //            sf = gh.MeasureString(measuredString, BaseFont, CommonString.PointZero, CommonString.BaseStringFormat);
        //        }
        //        if (sf.Width > baseSize.Width * (baseSize.Height * 1.0 / sf.Height))
        //        {
        //            size -= 0.1F;
        //            BaseFont = new Font(BaseFont.FontFamily, size, BaseFont.Style, BaseFont.Unit);
        //        }
        //    }
        //    if (baseSize.Height < sf.Height)
        //    {
        //        baseSize.Height = (int)sf.Height;
        //    }

        //    return BaseFont;
        //}

        //private Font GetFontByTextRendererMeasure(Graphics gh, string measuredString, Font BaseFont, ref Size baseSize)
        //{
        //    float size = 5;
        //    BaseFont = new Font(BaseFont.FontFamily, size, FontStyle.Regular, GraphicsUnit.Point);
        //    SizeF sf = sf = TextRenderer.MeasureText(gh, measuredString, BaseFont, new Size(10000000, 1000000), CommonString.BaseTextFormatFlags);
        //    if (sf.Width < baseSize.Width * (baseSize.Height * 1.0 / sf.Height))
        //    {
        //        while (sf.Width < baseSize.Width * (baseSize.Height * 1.0 / sf.Height))
        //        {
        //            size += 0.2F;
        //            BaseFont = new Font(BaseFont.FontFamily, size, BaseFont.Style, BaseFont.Unit);
        //            sf = TextRenderer.MeasureText(gh, measuredString, BaseFont, new Size(10000000, 1000000), CommonString.BaseTextFormatFlags);
        //        }
        //        if (sf.Width > baseSize.Width * (baseSize.Height * 1.0 / sf.Height))
        //        {
        //            size -= 0.2F;
        //            BaseFont = new Font(BaseFont.FontFamily, size, BaseFont.Style, BaseFont.Unit);
        //        }
        //    }
        //    if (baseSize.Height < sf.Height)
        //    {
        //        baseSize.Height = (int)sf.Height;
        //    }

        //    return BaseFont;
        //}

        private string GetTextByContent(bool isAppend = false)
        {
            var result = OcrContent.result.GetTextResult(isAppend, IsShowOldContent, SpiltModel, OcrContent.processName);
            result = ProcessPunctuation(result);
            return result;
        }

        internal string GetTextByContent(TextCellInfo cell)
        {
            string result = cell.GetCellContent(OcrContent?.ocrType == OCRType.翻译, IsShowOldContent);
            result = ProcessPunctuation(result);
            return result;
        }

        private string ProcessPunctuation(string result)
        {
            if (!string.IsNullOrEmpty(result))
            {
                var arrays = result.Split(new[] { "\n" }, StringSplitOptions.None);
                Parallel.For(0, arrays.Length,
                    p => { arrays[p] = CommonStyle.ReplacePunctuationAuto(arrays[p], BiaoDianMode); });
                result = string.Join("\n", arrays);
            }

            return result;
        }

        public string GetContentText(bool isAll = true)
        {
            return isAll
                ? string.IsNullOrEmpty(txtContent.SelectedText) ? txtContent.Text : txtContent.SelectedText
                : txtContent.SelectedText;
        }

        public void ExportExcel()
        {
            if (dgContent.DataSource == null)
            {
                MessageBox.Show(this, "没有需要导出到的数据！", "温馨提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var text = DateTime.Now.ToString("yyyyMMddhhmmss");
            var saveFileDialog1 = new SaveFileDialog
            {
                Filter = "Excel文件| *.xlsx|Excel文件| *.xls|csv文件| *.csv|所有文件|*.*",
                Title = "选择保存位置",
                FileName = "Excel_" + text,
                FilterIndex = 1
            };
            if (saveFileDialog1.ShowDialog(this) != DialogResult.OK ||
                string.IsNullOrEmpty(saveFileDialog1.FileName)) return;
            ExcelHelper.ExportCSV(dgContent, saveFileDialog1.FileName);
        }

        #region 图文模式

        public bool IsCanVertical()
        {
            return !string.IsNullOrEmpty(OcrContent?.result?.verticalText) && Image != null;
        }

        public bool IsShowTxt { get; set; }

        public void BindPicTxt()
        {
            if (FindForm() is FrmMain)
            {
                tsmPicView.Visible = true;
                tsmFullScreen.Visible = false;
            }
            else
            {
                tsmPicView.Visible = false;
                tsmFullScreen.Visible = true;
            }

            pnlPicText.BindPicTxt(this, IsShowTxt);
        }

        private void tsmPicBig_Click(object sender, EventArgs e)
        {
            pnlPicText.Zoom += 10;
        }

        private void tsmPicOrigin_Click(object sender, EventArgs e)
        {
            if (pnlPicText.Zoom == 100)
                pnlPicText.ZoomToFit();
            else
                pnlPicText.Zoom = 100;
        }

        private void tsmPicSmall_Click(object sender, EventArgs e)
        {
            pnlPicText.Zoom -= 10;
        }

        private DisplayModel nowDisplayMode;

        internal DisplayModel NowDisplayMode
        {
            get => nowDisplayMode;
            set
            {
                nowDisplayMode = value;
                if (IsCanVertical())
                {
                    if (pnlPicText.Controls.Count <= 0) BindPicTxt();
                    SetDisplayMode(nowDisplayMode);
                }
            }
        }

        private void tsmModel_Click(object sender, EventArgs e)
        {
            NowDisplayMode = NowDisplayMode == DisplayModel.文字模式 ? DisplayModel.图文模式 : DisplayModel.文字模式;
            var mouseLocation = toolResize.PointToScreen(tsmModel.Bounds.Location);
            CommonMethod.SetCursorPos(mouseLocation.X + tsmModel.Width / 2, mouseLocation.Y + tsmModel.Height / 2);
        }

        private void SetDisplayMode(DisplayModel model)
        {
            if (model == DisplayModel.文字模式)
            {
                txtPicZoomPercent.Visible = false;
                tsmTrans.Visible = Program.NowUser?.IsSupportTranslate == true;
                tsmSearch.Visible = true;
                tsmPicBig.Visible = false;
                tsmPicOrigin.Visible = false;
                tsmPicSmall.Visible = false;
                //if (OcrContent.ocrType == OCRType.公式)
                //{
                //    wbContent.DocumentText = "";
                //    wbContent.BringToFront();
                //    var url = OCRHelper.GetMathFileResultUrl(new OcrContent() { result = new ResultEntity() { spiltText = string.Format("${0}$", OcrContent.result.spiltText) } });
                //    wbContent.Url = new Uri(url);
                //}
                //else
                {
                    txtContent.BringToFront();
                }
            }
            else
            {
                tsmSearch.Visible = false;
                txtPicZoomPercent.Visible = true;
                tsmTrans.Visible = false;
                tsmPicBig.Visible = true;
                tsmPicOrigin.Visible = true;
                tsmPicSmall.Visible = true;
                pnlPicText.BringToFront();
            }

            PnlPicText_ZoomChanged(null, null);
            PnlPicText_SizeChanged(null, null);
            var tModel = model == DisplayModel.图文模式 ? DisplayModel.文字模式 : DisplayModel.图文模式;
            tsmModel.Image = Resources.ResourceManager.GetObject(tModel.ToString()) as Bitmap;
            tsmModel.Text = string.Format("切换到【{0}】", tModel);
        }

        #endregion

        #region 其他按钮功能

        private void tsmFullScreen_Click(object sender, EventArgs e)
        {
            var form = FindForm();
            if (form == null) return;
            if (form.WindowState == FormWindowState.Normal)
                form.WindowState = FormWindowState.Maximized;
            else
                form.WindowState = FormWindowState.Normal;
        }

        private void tsmSearch_Click(object sender, EventArgs e)
        {
            var txt = GetContentText();
            if (!string.IsNullOrEmpty(txt)) FrmMain.DoSearch(txt);
        }

        private void tsmPicView_Click(object sender, EventArgs e)
        {
            var frm = FindForm() as MetroForm;
            var compare = new FrmPicCompare { Icon = frm.Icon, Theme = frm.Theme, StyleManager = frm.StyleManager };
            compare.Init(SpiltModel, IsShowOldContent);
            compare.Bind(Image, OcrContent);
            compare.Show();
        }

        private void tsmCopy_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(txtContent.Text))
                ClipboardService.SetText(txtContent.Text);
        }

        private void tsmTrans_Click(object sender, EventArgs e)
        {
            var transText = string.IsNullOrEmpty(txtContent.SelectedText) ? txtContent.Text : txtContent.SelectedText;
            if (string.IsNullOrEmpty(transText)) return;
            var files = new List<string> { "data:txt" + transText };
            FrmMain.DragDropEventDelegate?.Invoke(files, null, OCRType.翻译, ProcessBy.主界面, null);
        }

        #endregion
    }

    internal enum DisplayModel
    {
        文字模式 = 0,
        图文模式 = 1
    }
}