﻿using OCRTools.Common;
using System;
using System.Collections.Specialized;
using System.IO;

namespace ImageLib
{
    /// <summary>
    /// https://sm.ms/
    /// </summary>
    public class SMMSImageUpload : BaseImageUpload
    {
        public SMMSImageUpload()
        {
            ImageType = ImageTypeEnum.SMMS;
        }

        private const string strFileNameSpilt = "\"url\":\"";

        internal override string GetResult(byte[] content, string ext)
        {
            var result = "";
            try
            {
                var url = "https://sm.ms/api/v2/upload?inajax=1";
                var file = new UploadFileInfo()
                {
                    Name = "smfile",
                    Filename = "test.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                var vaules = new NameValueCollection() { { "file_id", "0" } };
                var html = UploadFileRequest.Post(url, new[] { file }, vaules);
                if (html.Contains(strFileNameSpilt))
                {
                    result = html.Substring(html.IndexOf(strFileNameSpilt) + strFileNameSpilt.Length);
                    result = result.Substring(0, result.IndexOf("\"")).Replace("\\/", "/");
                }
            }
            catch (Exception)
            {

            }
            return result;
        }
    }
}
