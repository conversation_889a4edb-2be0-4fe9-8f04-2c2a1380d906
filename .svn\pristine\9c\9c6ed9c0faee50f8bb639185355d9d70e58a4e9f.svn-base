// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System;
using System.Globalization;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Automation.Text;
using DockPosition = System.Windows.Automation.DockPosition;
using ExpandCollapseState = System.Windows.Automation.ExpandCollapseState;
using OrientationType = System.Windows.Automation.OrientationType;
using RowOrColumnMajor = System.Windows.Automation.RowOrColumnMajor;
using ToggleState = System.Windows.Automation.ToggleState;
using WindowInteractionState = System.Windows.Automation.WindowInteractionState;
using WindowVisualState = System.Windows.Automation.WindowVisualState;

namespace UIAComWrapperInternal
{
    internal delegate object PropertyConverter(object valueAsObject);

    internal delegate object PatternWrapper(AutomationElement el, object pattern, bool cached);

    internal class PropertyTypeInfo
    {
        internal PropertyTypeInfo(PropertyConverter converter, AutomationIdentifier id, Type type)
        {
            ID = id;
            Type = type;
            ObjectConverter = converter;
        }


        internal AutomationIdentifier ID { get; }

        internal PropertyConverter ObjectConverter { get; }

        internal Type Type { get; }
    }

    internal class PatternTypeInfo
    {
        public PatternTypeInfo(AutomationPattern id, PatternWrapper clientSideWrapper)
        {
            ID = id;
            ClientSideWrapper = clientSideWrapper;
        }


        internal PatternWrapper ClientSideWrapper { get; }

        internal AutomationPattern ID { get; }
    }

    internal class Schema
    {
        private static readonly PropertyConverter convertToControlType = ConvertToControlType;
        private static readonly PropertyConverter convertToCultureInfo = ConvertToCultureInfo;
        private static readonly PropertyConverter convertToDockPosition = ConvertToDockPosition;
        private static readonly PropertyConverter convertToExpandCollapseState = ConvertToExpandCollapseState;
        private static readonly PropertyConverter convertToOrientationType = ConvertToOrientationType;
        private static readonly PropertyConverter convertToPoint = ConvertToPoint;
        private static readonly PropertyConverter convertToRect = ConvertToRect;
        private static readonly PropertyConverter convertToRowOrColumnMajor = ConvertToRowOrColumnMajor;
        private static readonly PropertyConverter convertToToggleState = ConvertToToggleState;
        private static readonly PropertyConverter convertToWindowInteractionState = ConvertToWindowInteractionState;
        private static readonly PropertyConverter convertToWindowVisualState = ConvertToWindowVisualState;
        private static readonly PropertyConverter convertToAnnotationType = ConvertToAnnotationType;
        private static readonly PropertyConverter convertToStyleId = ConvertToStyleId;

        private static readonly PropertyTypeInfo[] _propertyInfoTable =
        {
            // Properties requiring conversion
            new PropertyTypeInfo(convertToRect, AutomationElement.BoundingRectangleProperty, typeof(Rect)),
            new PropertyTypeInfo(convertToControlType, AutomationElement.ControlTypeProperty, typeof(ControlType)),
            new PropertyTypeInfo(convertToPoint, AutomationElement.ClickablePointProperty, typeof(Point)),
            new PropertyTypeInfo(convertToOrientationType, AutomationElement.OrientationProperty,
                typeof(OrientationType)),
            new PropertyTypeInfo(convertToDockPosition, DockPattern.DockPositionProperty, typeof(DockPosition)),
            new PropertyTypeInfo(convertToExpandCollapseState, ExpandCollapsePattern.ExpandCollapseStateProperty,
                typeof(ExpandCollapseState)),
            new PropertyTypeInfo(convertToWindowVisualState, WindowPattern.WindowVisualStateProperty,
                typeof(WindowVisualState)),
            new PropertyTypeInfo(convertToWindowInteractionState, WindowPattern.WindowInteractionStateProperty,
                typeof(WindowInteractionState)),
            new PropertyTypeInfo(convertToRowOrColumnMajor, TablePattern.RowOrColumnMajorProperty,
                typeof(RowOrColumnMajor)),
            new PropertyTypeInfo(convertToToggleState, TogglePattern.ToggleStateProperty, typeof(ToggleState)),
            new PropertyTypeInfo(convertToAnnotationType, AnnotationPattern.AnnotationTypeIdProperty,
                typeof(AnnotationType)),
            new PropertyTypeInfo(convertToStyleId, StylesPattern.StyleIdProperty, typeof(StyleId)),

            // Text attributes 
            new PropertyTypeInfo(null, TextPattern.AnimationStyleAttribute, typeof(AnimationStyle)),
            new PropertyTypeInfo(null, TextPattern.BackgroundColorAttribute, typeof(int)),
            new PropertyTypeInfo(null, TextPattern.BulletStyleAttribute, typeof(BulletStyle)),
            new PropertyTypeInfo(null, TextPattern.CapStyleAttribute, typeof(CapStyle)),
            new PropertyTypeInfo(convertToCultureInfo, TextPattern.CultureAttribute, typeof(CultureInfo)),
            new PropertyTypeInfo(null, TextPattern.FontNameAttribute, typeof(string)),
            new PropertyTypeInfo(null, TextPattern.FontSizeAttribute, typeof(double)),
            new PropertyTypeInfo(null, TextPattern.FontWeightAttribute, typeof(int)),
            new PropertyTypeInfo(null, TextPattern.ForegroundColorAttribute, typeof(int)),
            new PropertyTypeInfo(null, TextPattern.HorizontalTextAlignmentAttribute, typeof(HorizontalTextAlignment)),
            new PropertyTypeInfo(null, TextPattern.IndentationFirstLineAttribute, typeof(double)),
            new PropertyTypeInfo(null, TextPattern.IndentationLeadingAttribute, typeof(double)),
            new PropertyTypeInfo(null, TextPattern.IndentationTrailingAttribute, typeof(double)),
            new PropertyTypeInfo(null, TextPattern.IsHiddenAttribute, typeof(bool)),
            new PropertyTypeInfo(null, TextPattern.IsItalicAttribute, typeof(bool)),
            new PropertyTypeInfo(null, TextPattern.IsReadOnlyAttribute, typeof(bool)),
            new PropertyTypeInfo(null, TextPattern.IsSubscriptAttribute, typeof(bool)),
            new PropertyTypeInfo(null, TextPattern.IsSuperscriptAttribute, typeof(bool)),
            new PropertyTypeInfo(null, TextPattern.MarginBottomAttribute, typeof(double)),
            new PropertyTypeInfo(null, TextPattern.MarginLeadingAttribute, typeof(double)),
            new PropertyTypeInfo(null, TextPattern.MarginTopAttribute, typeof(double)),
            new PropertyTypeInfo(null, TextPattern.MarginTrailingAttribute, typeof(double)),
            new PropertyTypeInfo(null, TextPattern.OutlineStylesAttribute, typeof(OutlineStyles)),
            new PropertyTypeInfo(null, TextPattern.OverlineColorAttribute, typeof(int)),
            new PropertyTypeInfo(null, TextPattern.OverlineStyleAttribute, typeof(TextDecorationLineStyle)),
            new PropertyTypeInfo(null, TextPattern.StrikethroughColorAttribute, typeof(int)),
            new PropertyTypeInfo(null, TextPattern.StrikethroughStyleAttribute, typeof(TextDecorationLineStyle)),
            new PropertyTypeInfo(null, TextPattern.TabsAttribute, typeof(double[])),
            new PropertyTypeInfo(null, TextPattern.TextFlowDirectionsAttribute, typeof(FlowDirections)),
            new PropertyTypeInfo(null, TextPattern.UnderlineColorAttribute, typeof(int)),
            new PropertyTypeInfo(null, TextPattern.UnderlineStyleAttribute, typeof(TextDecorationLineStyle)),
            new PropertyTypeInfo(null, TextPattern2.AnnotationTypesAttribute, typeof(AnnotationType[])),
            new PropertyTypeInfo(null, TextPattern2.AnnotationObjectsAttribute, typeof(AutomationElement[])),
            new PropertyTypeInfo(null, TextPattern2.StyleNameAttribute, typeof(string)),
            new PropertyTypeInfo(null, TextPattern2.StyleIdAttribute, typeof(StyleId)),
            new PropertyTypeInfo(null, TextPattern2.LinkAttribute, typeof(AutomationElement)),
            new PropertyTypeInfo(null, TextPattern2.IsActiveAttribute, typeof(bool)),
            new PropertyTypeInfo(null, TextPattern2.SelectionActiveEndAttribute, typeof(ActiveEnd)),
            new PropertyTypeInfo(null, TextPattern2.CaretPositionAttribute, typeof(CaretPosition)),
            new PropertyTypeInfo(null, TextPattern2.CaretBidiModeAttribute, typeof(CaretBidiMode))
        };

        private static readonly PatternTypeInfo[] _patternInfoTable =
        {
            new PatternTypeInfo(InvokePattern.Pattern, InvokePattern.Wrap),
            new PatternTypeInfo(SelectionPattern.Pattern, SelectionPattern.Wrap),
            new PatternTypeInfo(ValuePattern.Pattern, ValuePattern.Wrap),
            new PatternTypeInfo(RangeValuePattern.Pattern, RangeValuePattern.Wrap),
            new PatternTypeInfo(ScrollPattern.Pattern, ScrollPattern.Wrap),
            new PatternTypeInfo(ExpandCollapsePattern.Pattern, ExpandCollapsePattern.Wrap),
            new PatternTypeInfo(GridPattern.Pattern, GridPattern.Wrap),
            new PatternTypeInfo(GridItemPattern.Pattern, GridItemPattern.Wrap),
            new PatternTypeInfo(MultipleViewPattern.Pattern, MultipleViewPattern.Wrap),
            new PatternTypeInfo(WindowPattern.Pattern, WindowPattern.Wrap),
            new PatternTypeInfo(SelectionItemPattern.Pattern, SelectionItemPattern.Wrap),
            new PatternTypeInfo(DockPattern.Pattern, DockPattern.Wrap),
            new PatternTypeInfo(TablePattern.Pattern, TablePattern.Wrap),
            new PatternTypeInfo(TableItemPattern.Pattern, TableItemPattern.Wrap),
            new PatternTypeInfo(TextPattern.Pattern, TextPattern.Wrap),
            new PatternTypeInfo(TogglePattern.Pattern, TogglePattern.Wrap),
            new PatternTypeInfo(TransformPattern.Pattern, TransformPattern.Wrap),
            new PatternTypeInfo(ScrollItemPattern.Pattern, ScrollItemPattern.Wrap),
            new PatternTypeInfo(ItemContainerPattern.Pattern, ItemContainerPattern.Wrap),
            new PatternTypeInfo(VirtualizedItemPattern.Pattern, VirtualizedItemPattern.Wrap),
            new PatternTypeInfo(LegacyIAccessiblePattern.Pattern, LegacyIAccessiblePattern.Wrap),
            new PatternTypeInfo(SynchronizedInputPattern.Pattern, SynchronizedInputPattern.Wrap),
            new PatternTypeInfo(ObjectModelPattern.Pattern, ObjectModelPattern.Wrap),
            new PatternTypeInfo(AnnotationPattern.Pattern, AnnotationPattern.Wrap),
            new PatternTypeInfo(TextPattern2.Pattern, TextPattern2.Wrap),
            new PatternTypeInfo(StylesPattern.Pattern, StylesPattern.Wrap),
            new PatternTypeInfo(SpreadsheetPattern.Pattern, SpreadsheetPattern.Wrap),
            new PatternTypeInfo(SpreadsheetItemPattern.Pattern, SpreadsheetItemPattern.Wrap),
            new PatternTypeInfo(TransformPattern2.Pattern, TransformPattern2.Wrap),
            new PatternTypeInfo(TextChildPattern.Pattern, TextChildPattern.Wrap),
            new PatternTypeInfo(DragPattern.Pattern, DragPattern.Wrap),
            new PatternTypeInfo(DropTargetPattern.Pattern, DropTargetPattern.Wrap)
        };


        private Schema()
        {
        }

        private static object ConvertToControlType(object value)
        {
            if (value is ControlType) return value;
            return ControlType.LookupById((int) value);
        }

        private static object ConvertToCultureInfo(object value)
        {
            if (value is int i)
            {
                if (i == 0)
                    // Some providers return 0 to mean Invariant
                    return CultureInfo.InvariantCulture;
                return new CultureInfo(i);
            }

            return null;
        }

        private static object ConvertToDockPosition(object value)
        {
            return (DockPosition) value;
        }

        private static object ConvertToExpandCollapseState(object value)
        {
            return (ExpandCollapseState) value;
        }

        private static object ConvertToOrientationType(object value)
        {
            return (OrientationType) value;
        }

        private static object ConvertToPoint(object value)
        {
            var numArray = (double[]) value;
            return new Point(numArray[0], numArray[1]);
        }

        private static object ConvertToRect(object value)
        {
            var numArray = (double[]) value;
            var x = numArray[0];
            var y = numArray[1];
            var width = numArray[2];
            return new Rect(x, y, width, numArray[3]);
        }

        private static object ConvertToRowOrColumnMajor(object value)
        {
            return (RowOrColumnMajor) value;
        }

        private static object ConvertToToggleState(object value)
        {
            return (ToggleState) value;
        }

        private static object ConvertToWindowInteractionState(object value)
        {
            return (WindowInteractionState) value;
        }

        private static object ConvertToWindowVisualState(object value)
        {
            return (WindowVisualState) value;
        }

        private static object ConvertToAnnotationType(object value)
        {
            return (AnnotationType) value;
        }

        private static object ConvertToStyleId(object value)
        {
            return (StyleId) value;
        }

        internal static bool GetPatternInfo(AutomationPattern id, out PatternTypeInfo info)
        {
            foreach (var info2 in _patternInfoTable)
                if (info2.ID == id)
                {
                    info = info2;
                    return true;
                }

            info = null;
            return false;
        }

        internal static bool GetPropertyTypeInfo(AutomationIdentifier id, out PropertyTypeInfo info)
        {
            foreach (var info2 in _propertyInfoTable)
                if (info2.ID == id)
                {
                    info = info2;
                    return true;
                }

            info = null;
            return false;
        }
    }
}