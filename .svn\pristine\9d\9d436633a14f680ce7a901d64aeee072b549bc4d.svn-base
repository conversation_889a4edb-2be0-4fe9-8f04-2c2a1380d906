using System;
using System.Drawing;
using System.Windows.Forms;
using OCRTools.Common;

namespace OCRTools
{
    public class Screenshot
    {
        public bool CaptureCursor { get; set; } = false;
        public bool CaptureClientArea { get; set; } = false;
        public bool RemoveOutsideScreenArea { get; set; } = true;
        public bool AutoHideTaskbar { get; set; } = false;

        public Bitmap CaptureRectangle(Rectangle rect)
        {
            if (RemoveOutsideScreenArea)
            {
                var bounds = SystemInformation.VirtualScreen;
                rect = Rectangle.Intersect(bounds, rect);
            }

            return CaptureRectangleNative(rect, CaptureCursor);
        }

        public Bitmap CaptureFullscreen(ref Rectangle rect)
        {
            rect = SystemInformation.VirtualScreen;

            return CaptureRectangle(rect);
        }

        public Bitmap CaptureWindow(IntPtr handle, ref Rectangle rect)
        {
            if (handle.ToInt32() > 0)
            {
                rect = handle.GetRectangle(!CaptureClientArea);

                var isTaskbarHide = false;

                try
                {
                    if (AutoHideTaskbar) isTaskbarHide = NativeMethods.SetTaskbarVisibilityIfIntersect(false, rect);

                    return CaptureRectangle(rect);
                }
                finally
                {
                    if (isTaskbarHide) NativeMethods.SetTaskbarVisibility(true);
                }
            }

            return null;
        }

        public Bitmap CaptureActiveWindow(ref Rectangle rect)
        {
            var handle = NativeMethods.GetForegroundWindow();

            return CaptureWindow(handle, ref rect);
        }

        public Bitmap CaptureActiveMonitor(ref Rectangle rect)
        {
            rect = NativeMethods.GetActiveScreenBounds();

            return CaptureRectangle(rect);
        }

        private Bitmap CaptureRectangleNative(Rectangle rect, bool captureCursor = false)
        {
            var handle = NativeMethods.GetDesktopWindow();
            return CaptureRectangleNative(handle, rect, captureCursor);
        }

        private Bitmap CaptureRectangleNative(IntPtr handle, Rectangle rect, bool captureCursor = false)
        {
            if (rect.Width == 0 || rect.Height == 0) return null;

            var hdcSrc = NativeMethods.GetWindowDC(handle);
            var hdcDest = NativeMethods.CreateCompatibleDC(hdcSrc);
            var hBitmap = NativeMethods.CreateCompatibleBitmap(hdcSrc, rect.Width, rect.Height);
            var hOld = NativeMethods.SelectObject(hdcDest, hBitmap);
            NativeMethods.BitBlt(hdcDest, 0, 0, rect.Width, rect.Height, hdcSrc, rect.X, rect.Y,
                CopyPixelOperation.SourceCopy | CopyPixelOperation.CaptureBlt);

            if (captureCursor)
                try
                {
                    var cursorData = new CursorData();
                    cursorData.DrawCursor(hdcDest, rect.Location);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }

            NativeMethods.SelectObject(hdcDest, hOld);
            NativeMethods.DeleteDC(hdcDest);
            NativeMethods.ReleaseDC(handle, hdcSrc);
            var bmp = Image.FromHbitmap(hBitmap);
            NativeMethods.DeleteObject(hBitmap);

            return bmp;
        }
    }
}