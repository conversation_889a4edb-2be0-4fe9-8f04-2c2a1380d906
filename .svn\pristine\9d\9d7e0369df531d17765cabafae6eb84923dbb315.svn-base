﻿using MetroFramework;
using Microsoft.Win32;
using OCRTools.Properties;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class frmSetting : MetroFramework.Forms.MetroForm
    {
        public frmSetting()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
        }

        private void frmSetting_Load(object sender, System.EventArgs e)
        {
            tabSetting.StyleManager = StyleManager;
            var kvDictonary = new Dictionary<int, string>();
            foreach (ToolDoubleClickEnum item in Enum.GetValues(typeof(ToolDoubleClickEnum)))
            {
                kvDictonary.Add(item.GetHashCode(), item.ToString());
            }
            BindingSource bs = new BindingSource
            {
                DataSource = kvDictonary
            };
            cmbDoubleClick.DataSource = bs;
            cmbDoubleClick.ValueMember = "Key";
            cmbDoubleClick.DisplayMember = "Value";
            InitThemes();
            InitFonts();
            InitLoading();
            InitBackColors();
            InitSearch();
            readIniFile();
        }

        public void readIniFile()
        {
            chkAutoRun.Checked = IniHelper.GetBoolValue("配置", "开机自启");
            chkFrmToolVisiable.Checked = !IniHelper.GetBoolValue("工具栏", "隐藏");
            chkFrmToolVisiable.CheckedChanged += chkFrmToolVisiable_CheckedChanged;

            InitShortKeys();

            var strDoubleClick = IniHelper.GetIntValue("工具栏", "双击操作");
            if (strDoubleClick < 0 || strDoubleClick >= ToolDoubleClickEnum.不做任何操作.GetHashCode())
            {
                strDoubleClick = ToolDoubleClickEnum.显示主窗体.GetHashCode();
            }
            cmbDoubleClick.SelectedIndex = strDoubleClick;
        }

        private void InitShortKeys()
        {
            tipMsg.AutoPopDelay = 5000;
            //设置鼠标停在该控件后，再停多长时间显示说眀性文字
            tipMsg.InitialDelay = 100;
            //设置鼠标从一个控件移到叧一个啌件再次显示该说明性文牸哋时间间隔
            tipMsg.ReshowDelay = 200;
            //蔎置是否显示窗体的说明性文字
            tipMsg.ShowAlways = true;
            tipMsg.ToolTipTitle = "功能描述";
            tipMsg.ToolTipIcon = ToolTipIcon.Info;

            //FlowLayoutPanel pnlShortKey = new FlowLayoutPanel()
            //{
            //    BackColor = Color.White,
            //    Dock = DockStyle.Fill
            //};
            //tbShortKey.Controls.Add(pnlShortKey);

            var hotTypeList = FrmMain.LstHotKeys.GroupBy(p => p.Group).ToList();
            hotTypeList.ForEach(p =>
            {
                //p.Key
                var tbTmp = new TabPage()
                {
                    BackColor = Color.White,
                    Padding = new Padding(0, 5, 0, 0),
                    Text = p.Key.ToString()
                };

                foreach (var item in p.ToList())
                {
                    FlowLayoutPanel pnl = new FlowLayoutPanel()
                    {
                        BackColor = Color.White,
                        Width = tbShortKey.Width - 5,
                        Height = 37,
                        Padding = new Padding(0, 5, 0, 0),
                        Dock = DockStyle.Top,
                    };
                    var lbl = new Label()
                    {
                        Name = "lbl_" + item.KeyName.Trim(),
                        Text = item.KeyName + ":",
                        Font = new Font(lblDarkModel.Font.FontFamily, 12f, FontStyle.Bold),
                        AutoSize = true,
                        BackColor = Color.White,
                        ForeColor = Color.Black,
                        Padding = new Padding(30, 6, 0, 0),
                    };
                    pnl.Controls.Add(lbl);
                    tipMsg.SetToolTip(lbl, item.Desc);

                    var textBox = new TextBox()
                    {
                        Name = "txtBox_" + item.KeyName.Trim(),
                        Font = new Font(lblDarkModel.Font.FontFamily, 12f, FontStyle.Bold),
                        BackColor = Color.White,
                        ForeColor = Color.Black,
                        Size = new Size(185, 36),
                        Padding = new Padding(0, 0, 0, 0),
                        ShortcutsEnabled = false,
                    };

                    textBox.Text = item.StrKey;
                    textBox.KeyDown += txtCaptureKey_KeyDown;
                    textBox.KeyUp += txtCaptureKey_KeyUp;

                    tipMsg.SetToolTip(textBox, item.Desc);

                    pnl.Controls.Add(textBox);

                    var picture = new PictureBox()
                    {
                        Name = "pictureBox_" + item.KeyName.Trim(),
                        BackColor = Color.White,
                        Size = new Size(25, 25),
                        SizeMode = PictureBoxSizeMode.CenterImage,
                        Padding = new Padding(10, 5, 0, 0)
                    };
                    picture.Image = string.IsNullOrEmpty(textBox.Text) || textBox.Text.Equals(CommonString.StrDefaultDesc)
                        ? Resources.Info_Error : Resources.Info_OK;
                    pnl.Controls.Add(picture);

                    tbTmp.Controls.Add(pnl);
                }
                tabHotKeys.TabPages.Add(tbTmp);
            });

        }

        private Dictionary<string, float> DicFonts = new Dictionary<string, float>();

        private void txtCaptureKey_KeyDown(object sender, KeyEventArgs e)
        {
            e.SuppressKeyPress = true;
        }

        private void InitFonts()
        {
            try
            {
                DicFonts = new Dictionary<string, float>
                {
                    { "超大", 72 },
                    { "初号", 42 },
                    { "小初", 36 },
                    { "一号", 26 },
                    { "小一", 24 },
                    { "二号", 22 },
                    { "小二", 18 },
                    { "三号", 16 },
                    { "小三", 15 },
                    { "四号", 14 },
                    { "小四", 12 },

                    { "五号", 10.5F },
                    { "小五", 9 },
                    { "六号", 7.5F },
                    { "小六", 6.5F },
                    { "七号", 5.5F },
                    { "八号", 5 }
                };
                foreach (string name in DicFonts.Keys)
                {
                    this.cmbFontSize.Items.Add(name);
                }
                foreach (FontFamily font in FontFamily.Families)
                {
                    this.cmbFontType.Items.Add(font.Name);
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine("加载字体失败！" + oe.Message);
            }
            try
            {
                var font = IniHelper.GetValue("配置", "字体");
                if (cmbFontType.Items.IndexOf(font) < 0)
                {
                    font = "微软雅黑";
                }
                cmbFontType.SelectedItem = font;

                var fontSize = IniHelper.GetFloatValue("配置", "字号");
                if (!DicFonts.Values.Any(p => p.Equals(fontSize)))
                {
                    fontSize = 12;
                }
                cmbFontSize.SelectedItem = DicFonts.Keys.ToList()[DicFonts.Values.ToList().IndexOf(fontSize)];
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        private void InitBackColors()
        {
            //try
            //{
            //    foreach (string name in FrmMain.DicThems.Keys)
            //    {
            //        this.cmbThems.Items.Add(name);
            //    }
            //}
            //catch (Exception oe)
            //{
            //    Console.WriteLine("加载主题失败！" + oe.Message);
            //}
            //cmbThems.SelectedItem = IniHelper.GetValue("配置", "背景色");
            //if (cmbThems.SelectedIndex < 0)
            //{
            //    cmbThems.SelectedIndex = 0;
            //}
        }

        private void InitThemes()
        {
            try
            {
                togTheme.Checked = StyleManager.Theme == MetroThemeStyle.Dark;
                foreach (MetroColorStyle type in Enum.GetValues(typeof(MetroColorStyle)))
                {
                    cmbStyle.Items.Add(type.ToString());
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine("加载主题失败！" + oe.Message);
            }
            cmbStyle.SelectedItem = IniHelper.GetValue("配置", "主题", "Green");
            if (cmbStyle.SelectedIndex < 0)
            {
                cmbStyle.SelectedIndex = 0;
            }
        }

        private void togTheme_CheckedChanged(object sender, EventArgs e)
        {
            StyleManager.Theme = togTheme.Checked ? MetroThemeStyle.Dark : MetroThemeStyle.Light;
            tabSetting.Theme = StyleManager.Theme;

            this.Invalidate(true);

            IniHelper.SetValue("配置", "深色模式", togTheme.Checked.ToString());
        }

        private void cmbStyle_SelectedIndexChanged(object sender, EventArgs e)
        {
            var selectedText = cmbStyle.SelectedItem.ToString();
            if (string.IsNullOrEmpty(selectedText))
            {
                return;
            }
            var selectedItem = (MetroColorStyle)Enum.Parse(typeof(MetroColorStyle), selectedText, true);

            StyleManager.Style = selectedItem;
            tabSetting.Style = selectedItem;
            togTheme.Style = selectedItem;
            chkAutoRun.Style = selectedItem;
            chkFrmToolVisiable.Style = selectedItem;

            this.Invalidate(true);

            IniHelper.SetValue("配置", "主题", selectedItem.ToString());
        }

        private void InitLoading()
        {
            try
            {
                foreach (LoadingType type in Enum.GetValues(typeof(LoadingType)))
                {
                    this.cmbLoading.Items.Add(type.ToString());
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine("加载动画失败！" + oe.Message);
            }
            cmbLoading.SelectedItem = ((LoadingType)BoxUtil.GetInt32FromObject(IniHelper.GetValue("配置", "加载动画"))).ToString();
            if (cmbLoading.SelectedIndex < 0)
            {
                cmbLoading.SelectedIndex = 0;
            }
        }

        private void InitSearch()
        {
            foreach (var engine in FrmMain.LstSearchEngine)
            {
                this.cmbSearch.Items.Add(engine.Name);
            }
            cmbSearch.SelectedItem = IniHelper.GetValue("配置", "搜索引擎");
            if (cmbSearch.SelectedIndex < 0)
            {
                cmbSearch.SelectedIndex = 0;
            }
        }

        private void cmbSearch_SelectedIndexChanged(object sender, EventArgs e)
        {
            var selectedText = cmbSearch.SelectedItem.ToString();
            if (string.IsNullOrEmpty(selectedText))
            {
                return;
            }
            IniHelper.SetValue("配置", "搜索引擎", selectedText);
        }

        private void cmbFontType_SelectedIndexChanged(object sender, EventArgs e)   //字体ComboBox控件选项改变事件
        {
            this.richTextBoxPrintCtrl1.Font = new Font(cmbFontType.Text, richTextBoxPrintCtrl1.Font.Size, richTextBoxPrintCtrl1.Font.Style);
        }

        private void cmbFontSize_SelectedIndexChanged(object sender, EventArgs e)   //字号大小ComboBox控件选项改变事件
        {
            this.richTextBoxPrintCtrl1.Font = new Font(richTextBoxPrintCtrl1.Font.FontFamily, DicFonts.Values.ToList()[cmbFontSize.SelectedIndex], this.richTextBoxPrintCtrl1.Font.Style);
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            return keyData == Keys.Tab;
        }

        private void txtCaptureKey_KeyUp(object sender, KeyEventArgs e)
        {
            var textBox = sender as TextBox;
            var regex = new Regex("[一-龥]+");
            var str = "";
            foreach (var obj in regex.Matches(textBox.Name))
            {
                str = ((Match)obj).ToString();
            }
            var key = "pictureBox_" + str;
            var pictureBox = (PictureBox)Controls.Find(key, true)[0];
            if (e.KeyData == Keys.Back)
            {
                textBox.Text = CommonString.StrDefaultDesc;
                pictureBox.Image = Resources.Info_Error;
                //FrmMain.LstHotKeys.Where(p => p.KeyName.Equals(str)).ToList().ForEach(p => p.StrKey = CommonString.StrDefaultDesc);
                IniHelper.SetValue("快捷键", str, "");
            }
            else if (e.KeyValue != 16 && e.KeyValue != 17 && e.KeyValue != 18)
            {
                var array = e.KeyData.ToString().Replace(" ", "").Replace("Control", "Ctrl").Split(',');
                pictureBox.Image = Resources.Info_OK;
                if (array.Length == 1)
                {
                    textBox.Text = array[0];
                }
                if (array.Length == 2)
                {
                    textBox.Text = array[1] + "+" + array[0];
                }
                if (array.Length <= 2)
                {
                    var exitsHotKey = FrmMain.LstHotKeys.FirstOrDefault(p => !p.KeyName.Equals(str) && Equals(p.StrKey, textBox.Text));
                    if (exitsHotKey == null)
                    {
                        //FrmMain.LstHotKeys.Where(p => p.KeyName.Equals(str)).ToList().ForEach(p => p.StrKey = textBox.Text);
                        IniHelper.SetValue("快捷键", str, textBox.Text);
                    }
                    else
                    {
                        CommonMethod.ShowHelpMsg(string.Format("快捷键：{0}，已被[{1}]占用！", textBox.Text, exitsHotKey.KeyName.Trim()));
                        textBox.Text = CommonString.StrDefaultDesc;
                        pictureBox.Image = Resources.Info_Error;
                        IniHelper.SetValue("快捷键", str, textBox.Text);
                    }
                }
            }
        }

        private void frmSetting_FormClosed(object sender, FormClosedEventArgs e)
        {
            IniHelper.SetValue("配置", "开机自启", chkAutoRun.Checked.ToString());
            IniHelper.SetValue("工具栏", "双击操作", cmbDoubleClick.SelectedValue?.ToString());
            IniHelper.SetValue("工具栏", "隐藏", (!FrmMain.FrmTool.Visible).ToString());
            IniHelper.SetValue("配置", "字体", cmbFontType.Text);
            IniHelper.SetValue("配置", "字号", DicFonts.Values.ToList()[cmbFontSize.SelectedIndex].ToString());
            DialogResult = DialogResult.OK;
        }

        public static void AutoStart(bool isAuto)
        {
            try
            {
                var value = Application.ExecutablePath.Replace("/", "\\");
                if (isAuto)
                {
                    var currentUser = Registry.CurrentUser;
                    var registryKey = currentUser.CreateSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run");
                    registryKey.SetValue(Application.ProductName, value);
                    registryKey.Close();
                    currentUser.Close();
                }
                else
                {
                    var currentUser2 = Registry.CurrentUser;
                    var registryKey2 = currentUser2.CreateSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run");
                    registryKey2.DeleteValue(Application.ProductName, false);
                    registryKey2.Close();
                    currentUser2.Close();
                }
            }
            catch (Exception)
            {
                CommonMethod.ShowHelpMsg("需要管理员权限修改！右键->以管理员方式打开重试！");
            }
        }

        private void chkFrmToolVisiable_CheckedChanged(object sender, EventArgs e)
        {
            FrmMain.FrmTool.VisibleChange();
        }

        private void chkAutoRun_CheckedChanged(object sender, EventArgs e)
        {
            AutoStart(chkAutoRun.Checked);
        }

        private void cmbThems_SelectedIndexChanged(object sender, EventArgs e)
        {
            //var selectedText = cmbThems.SelectedItem.ToString();
            //if (string.IsNullOrEmpty(selectedText))
            //{
            //    return;
            //}

            //if (!FrmMain.DicThems.ContainsKey(selectedText))
            //{
            //    selectedText = "豆沙绿";
            //}
            //var array = FrmMain.DicThems[selectedText].Split(new string[] { " " }, StringSplitOptions.RemoveEmptyEntries);
            //if (array?.Length == 3)
            //{
            //    richTextBoxPrintCtrl1.BackColor = Color.FromArgb(BoxUtil.GetInt32FromObject(array[0]), BoxUtil.GetInt32FromObject(array[1]), BoxUtil.GetInt32FromObject(array[2]));
            //}
            //IniHelper.SetValue("配置", "背景色", selectedText);
        }

        private void cmbLoading_SelectedIndexChanged(object sender, EventArgs e)
        {
            var selectedText = cmbLoading.SelectedItem.ToString();
            if (string.IsNullOrEmpty(selectedText))
            {
                return;
            }
            var selectedItem = (LoadingType)Enum.Parse(typeof(LoadingType), selectedText, true);
            picImage.Image = LoadingTypeHelper.GetImageByConfig(selectedItem);
            IniHelper.SetValue("配置", "加载动画", selectedItem.GetHashCode().ToString());
        }

        private void tabSetting_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (tabSetting.SelectedTab.Equals(tbShortKey))
            {
                this.Invalidate(true);
            }
        }
    }
}
