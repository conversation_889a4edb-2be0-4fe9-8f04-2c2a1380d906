using System.Collections.Generic;
using QiHe.CodeLib;

namespace ExcelLibrary.SpreadSheet
{
	public class ColumnWidth
	{
		internal Dictionary<Pair<ushort, ushort>, ushort> columnWidth = new Dictionary<Pair<ushort, ushort>, ushort>();

		public ushort Default = 2048;

		public ushort this[ushort colIndex]
		{
			get
			{
				Pair<ushort, ushort> key = FindColumnRange(colIndex);
				if (columnWidth.ContainsKey(key))
				{
					return columnWidth[key];
				}
				return Default;
			}
			set
			{
				Pair<ushort, ushort> key = FindColumnRange(colIndex);
				columnWidth[key] = value;
			}
		}

		public ushort this[ushort firstColIndex, ushort lastColIndex]
		{
			get
			{
				return columnWidth[new Pair<ushort, ushort>(firstColIndex, lastColIndex)];
			}
			set
			{
				columnWidth[new Pair<ushort, ushort>(firstColIndex, lastColIndex)] = value;
			}
		}

		private Pair<ushort, ushort> FindColumnRange(ushort colIndex)
		{
			foreach (Pair<ushort, ushort> key in columnWidth.Keys)
			{
				if (key.Left <= colIndex && colIndex <= key.Right)
				{
					return key;
				}
			}
			return new Pair<ushort, ushort>(colIndex, colIndex);
		}

		public IEnumerator<KeyValuePair<Pair<ushort, ushort>, ushort>> GetEnumerator()
		{
			return columnWidth.GetEnumerator();
		}
	}
}
