using OCRTools.Common;
using OCRTools.Language;
using OCRTools.Properties;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using System.Windows.Forms;

namespace OCRTools
{
    public static class Program
    {

        public static List<HistoryTask> RecentTasks;

        public static UserEntity NowUser { get; set; }

        public static int GetExecSleepTime()
        {
            var sleepMilSecond = 15000;
            if (NowUser?.PerTimeSpan > 0 && NowUser?.PerTimeSpanExecCount > 0)
            {
                if (Equals(CommonSetting.识别策略, OcrModel.仅本地识别.ToString()))
                {
                    sleepMilSecond = 0;
                }
                else
                {
                    sleepMilSecond = NowUser.PerTimeSpan / NowUser.PerTimeSpanExecCount;
                }
            }
            return sleepMilSecond + 500;
        }

        public static bool IsFirstRun { get; set; }

        public static bool IsLogined()
        {
            return NowUser != null && NowUser.IsLogined;
        }

        [STAThread]
        private static void Main(string[] args)
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            //处理未捕获的异常
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);

            //处理UI线程异常
            Application.ThreadException += Application_ThreadException;

            //处理非UI线程异常
            AppDomain.CurrentDomain.UnhandledException += Backend_ThreadException;

            //var plug = new PlugEntity()
            //{
            //    Name = "ChatGPT",
            //    FontSize = 18.5F,
            //    Desc = "ChatGPT助手版",
            //    Url = "https://ocr.oldfish.cn",
            //    ButtonType = ButtonType.ImageAndText,
            //    Type = PlugType.Url,
            //    Image = ImageProcessHelper.ImageToBase64(Resources.chatgpt)
            //};
            //Console.WriteLine(CommonString.JavaScriptSerializer.Serialize(new List<PlugEntity>() { plug }));

            //Console.WriteLine();
            //ClipboardService.GetRtfHtml();

            //Application.Run(new RulerForm());

            //Application.Run(new ScrollingCaptureForm(new ScrollingCaptureOptions(), new RegionCaptureOptions(), false));

            //SelectRectangleList selectRectangleList = new SelectRectangleList
            //{
            //    IncludeChildWindows = true
            //};
            //var Windows = selectRectangleList.GetWindowInfoList();

            //GZip.GZip.Compress(@"D:\Code\CatchTools\LocalOcrService\OcrMain\bin\Target\", @"D:\Code\CatchTools\LocalOcrService\OcrMain\bin\Package\", "本地OCR.zip");
            //GZip.GZip.Compress(@"D:\Code\CatchTools\LocalOcrService\OcrMain\bin\models", @"D:\Code\CatchTools\LocalOcrService\OcrMain\bin\Package\", "飞浆Mobile.zip");
            //GZip.GZip.Compress(@"D:\Code\CatchTools\LocalOcrService\OcrMain\bin\models", @"D:\Code\CatchTools\LocalOcrService\OcrMain\bin\Package\", "飞浆Server.zip");
            //GZip.GZip.Compress(@"D:\Code\CatchTools\LocalOcrService\OcrMain\bin\models", @"D:\Code\CatchTools\LocalOcrService\OcrMain\bin\Package\", "中文识别Lite.zip");
            //GZip.GZip.Compress(@"D:\Code\CatchTools\LocalOcrService\OcrMain\bin\models", @"D:\Code\CatchTools\LocalOcrService\OcrMain\bin\Package\", "QQNT.zip");
            //GZip.GZip.Compress(@"D:\Code\CatchTools\LocalOcrService\OcrMain\bin\models", @"D:\Code\CatchTools\LocalOcrService\OcrMain\bin\Package\", "WeChat.zip");

            //var targetPath = CommonString.DataPath;
            //var sourcePath = CommonString.DefaultLanguagePath;
            //var langFiles = new List<string>() { "en-US.lang", "ja-JP.lang", "ko-KR.lang", "zh-TW.lang", "fr-FR.lang", "de-DE.lang" };
            //foreach (var lang in langFiles)
            //{
            //    File.Move(targetPath + lang, sourcePath + lang);
            //    var result = GZip.GZip.Compress(sourcePath, targetPath, lang);
            //    File.Delete(sourcePath + lang);
            //}

            //var byt = File.ReadAllBytes(@"D:\Code\CatchTools\bin\Debug\Microsoft.ApplicationInsights.dll");
            //File.WriteAllBytes("Microsoft.ApplicationInsights.txt", CommonEncryptHelper.Compress(byt));
            //byt = File.ReadAllBytes(@"D:\Code\CatchTools\bin\Debug\Microsoft.Web.WebView2.WinForms.dll");
            //File.WriteAllBytes("Microsoft.Web.WebView2.WinForms.txt", CommonEncryptHelper.Compress(byt));
            //byt = File.ReadAllBytes(@"D:\Code\CatchTools\bin\Debug\WebView2Loader.dll");
            //File.WriteAllBytes("WebView2Loader.txt", CommonEncryptHelper.Compress(byt));
            //byt = File.ReadAllBytes(@"D:\Code\CatchTools\bin\Debug\WebView2Loader.dll");
            //File.WriteAllBytes("WebView2Loader.dat", CommonEncryptHelper.Compress(byt));

            //var byt = File.ReadAllBytes(@"D:\Code\CatchTools\RefDll\O2S.Components.PDFRender4NET.dll");
            //File.WriteAllBytes("O2S.Components.PDFRender4NET.txt", CommonEncryptHelper.Compress(byt));

            //var nowLoc = Application.ExecutablePath;
            //var targetLoc = CommonString.DataPath + Path.GetFileName(nowLoc);
            //if (CheckMainLocation(nowLoc, targetLoc) && CommonMethod.OpenFile(targetLoc))
            //{
            //    return;
            //}

            var strMsg = args != null && args.Length > 0 ? args[0] : "";
            var isMsgProcess = !string.IsNullOrEmpty(strMsg);
            if (isMsgProcess)
            {
                CommonSetting.Set右键菜单(strMsg);
            }

            using (var mutex = new Mutex(true, CommonString.ProductName, out bool isFristInstance))
            {
                if (isFristInstance)
                {
                    new Thread(Log.InitInsight).Start();
                    try
                    {
                        RunMain();
                    }
                    catch { }
                    finally
                    {
                        if (isFristInstance)
                            mutex.ReleaseMutex();
                    }
                }
            }
        }

//        private static bool CheckMainLocation(string nowLoc, string targetLoc)
//        {
//            var result = false;
//#if !DEBUG
//            if (!Equals(nowLoc, targetLoc))
//            {
//                if (!File.Exists(targetLoc))
//                {
//                    try
//                    {
//                        File.Copy(nowLoc, targetLoc);
//                    }
//                    catch (Exception)
//                    {
//                    }
//                }
//                result = File.Exists(targetLoc);
//            }
//#endif
//            return result;
//        }

        private static void InitShortLink()
        {
            ShortcutHelpers.CreateShortCuts();
            ShortcutHelpers.ClearOldShortCuts();
        }

        private static void RunMain()
        {
            var uue = new UheHandler();
            try
            {
                uue.InstallUheHandler();
                uue.SendingErrorReport += uue_SendingErrorReport;

                //CommonString.StrServerIp = "127.0.0.1";

                CommonString.InitRuntimeSetttings();

                CommonSetting.InitSetting();
#if DEBUG
                //LanguageHelper.SaveAllCulture();
#endif

                LanguageHelper.InitLanguage(CommonSetting.语言);

                CommonTheme.InitTheme();

                CommonString.ClearTmpFile();

                if (!CommonMethod.IsAutoStart.Equals(CommonSetting.开机启动))
                {
                    CommonMethod.AutoStart(CommonSetting.开机启动);
                }

                InitShortLink();

                try
                {
                    InternetExplorerFeatureControl.Instance.BrowserEmulation = DocumentMode.DefaultRespectDocType;
                }
                catch { }

                //CommonString.HostCode?.FullUrl = "http://localhost:23205/";

                try
                {
                    var uiAutomation = Assembly.Load(Resources.Interop_UIAutomationClient);
                    AppDomain.CurrentDomain.AssemblyResolve += (sender, args) =>
                    {
                        if (args.Name.Contains("UIAutomationClient"))
                        {
                            return uiAutomation;
                        }
                        //else if (args.Name.Contains("PDFRender4NET"))
                        //{
                        //    return pdfRender4NET;
                        //}

                        return CommonString.LoadDllByName(args.Name);
                    };
                }
                catch (Exception oe)
                {
                    Log.WriteError("AssemblyResolve", oe);
                }

                //ImageHelper.TestMax();

                Application.Run(new FrmMain());
            }
            catch (Exception oe)
            {
                Log.WriteError("RunMain", oe);
            }
            finally
            {
                uue.UninstallUheHandler();
            }
        }

        private static void uue_SendingErrorReport(object sender, SendErrorReportArgs e)
        {
            if (!string.IsNullOrEmpty(e.Body)) Log.WriteLog(e.Body);
        }

        private static void Application_ThreadException(object sender, ThreadExceptionEventArgs e)
        {
            HandlerError(e.Exception);
        }

        private static void Backend_ThreadException(object sender, UnhandledExceptionEventArgs e)
        {
            HandlerError(e.ExceptionObject as Exception);
        }

        private static void HandlerError(Exception error)
        {
            Log.WriteError("运行时异常", error);
        }

        //private static void SetDpiAwareness()
        //{
        //    try
        //    {
        //        if (NativeMethods.OsVersion.Major >= 6)
        //        {
        //            //NativeMethods.SetProcessDPIAware();
        //NativeMethods.SetProcessDpiAwareness(NativeMethods.ProcessDPIAwareness.ProcessDPIUnaware);
        //        }
        //    }
        //    catch
        //    {
        //    }
        //}
    }

    [Serializable]
    [Obfuscation]
    public class SiteMain
    {
        [Obfuscation]
        public string update { get; set; }

        [Obfuscation]
        public List<WebInfo> web { get; set; }

        [Obfuscation]
        public string defaultHost { get; set; }
    }

    [Obfuscation]
    [Serializable]
    public class WebInfo
    {
        [Obfuscation]
        public string Host { get; set; }

        [Obfuscation]
        public SiteType Type { get; set; }

        [Obfuscation]
        public string Ip { get; set; }

        [Obfuscation]
        public string FullUrl { get { return string.Format("http{1}://{0}/", Host, Https ? "s" : ""); } }

        [Obfuscation]
        public bool Https { get; set; } = true;
    }

    public enum SiteType
    {
        Default = 0,
        Account = 1,
        Code = 2,
        Update = 3
    }
}