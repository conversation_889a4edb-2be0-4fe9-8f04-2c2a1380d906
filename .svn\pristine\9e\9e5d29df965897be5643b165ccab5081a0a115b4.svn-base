using OCRTools.Language;
using OCRTools.ScreenCaptureLib;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;

namespace OCRTools.Common
{
    public class GuidePresenterForm : Form
    {
        private Form _mainForm;
        private GuideEntity guide;
        private int _currentGuideIndex = -1;
        private Panel pnlMain;
        private string _titleText;
        private string _descText;
        private Font _titleFont;
        private Font _descFont;
        private Rectangle _titleRect;
        private Rectangle _descRect;
        private Button btnPrev;
        private Button btnNext;
        private LinkLabel lnkSkip;
        private Color _guidePanelColor = Color.FromArgb(0, 112, 249); // Default light mode color
        private double wScale = 1d;
        private double hScale = 1d;

        private BufferedGraphics _bufferedGraphics;
        private Rectangle _currentHighlightRect = Rectangle.Empty;
        private ArrowDirection _currentArrowDirection;

        private Timer _animationTimer;
        private Point _targetPanelLocation;
        private Point _startPanelLocation;
        private int _animationStep = 0;
        private const int ANIMATION_STEPS = 10;

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern int SendMessage(IntPtr hWnd, Int32 wMsg, bool wParam, Int32 lParam);
        private const int WM_SETREDRAW = 10;

        // 开始禁止窗体重绘
        private void BeginUpdate()
        {
            SendMessage(this.Handle, WM_SETREDRAW, false, 0);
        }

        // 结束禁止窗体重绘并强制刷新
        private void EndUpdate()
        {
            // 启用重绘
            SendMessage(this.Handle, WM_SETREDRAW, true, 0);

            // 刷新页面 - 使用更强制的方式
            this.Refresh();
        }

        public GuidePresenterForm(Form mainForm, GuideEntity entity)
        {
            // 确保窗体支持透明背景
            this.SetStyle(ControlStyles.SupportsTransparentBackColor, true);
            this.BackColor = Color.Transparent;
            this.Opacity = 1.0; // 确保窗体完全不透明

            // 初始化双缓冲绘图
            SetStyle(ControlStyles.AllPaintingInWmPaint |
                    ControlStyles.UserPaint |
                    ControlStyles.DoubleBuffer |
                    ControlStyles.OptimizedDoubleBuffer, true);
            UpdateStyles();

            this.Region = mainForm.Region;

            // 初始化动画计时器
            _animationTimer = new Timer();
            _animationTimer.Interval = 15; // 15毫秒一帧，约60fps
            _animationTimer.Tick += AnimationTimer_Tick;

            guide = entity;
            if (guide.ShowSummary && !guide.Items[0].Rect.IsEmpty)
            {
                var strSummary = guide.Desc;
                if (string.IsNullOrEmpty(strSummary))
                {
                    strSummary = string.Join("\n", guide.Items.Where(p => p.Summary).Select(p => p.Title.CurrentText(true))).Trim();
                    if (string.IsNullOrEmpty(strSummary))
                    {
                        strSummary = guide.Title;
                    }
                }
                guide.Items.Insert(0, new GuideItem()
                {
                    Title = guide.Title,
                    Desc = strSummary,
                    Rect = Rectangle.Empty,
                    Exec = guide.Items[0].Exec
                });
                guide.Items[1].Exec = null;
            }
            _mainForm = mainForm;
            if (!mainForm.Visible)
            {
                mainForm.Show();
                Update();
            }
            if (mainForm.WindowState != FormWindowState.Normal)
            {
                mainForm.WindowState = FormWindowState.Normal;
                Update();
            }
            wScale = _mainForm.Width * 1d / guide.BaseSize.Width;
            hScale = _mainForm.Height * 1d / guide.BaseSize.Height;
            FormBorderStyle = FormBorderStyle.None;
            ShowInTaskbar = false;
            Margin = CommonString.PaddingZero;
            Padding = CommonString.PaddingZero;
            BackgroundImageLayout = ImageLayout.Stretch;
            InitializeControls();
            CommonMethod.EnableDoubleBuffering(this);
            Shown += GuidePresenterForm_Shown;
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            if (keyData == Keys.Escape)
            {
                CloseGuide();
                return true;
            }
            if (keyData == Keys.Left || keyData == Keys.Up)
            {
                ShowPreviousGuide();
                return true;
            }
            if (keyData == Keys.Right || keyData == Keys.Down)
            {
                ShowNextGuide(false);
                return true;
            }
            return base.ProcessCmdKey(ref msg, keyData);
        }

        private void GuidePresenterForm_Shown(object sender, EventArgs e)
        {
            dicImage = new Dictionary<string, Bitmap>();
            RefreshBackImg(guide.Items[0].Exec);
            Bounds = _mainForm.Bounds;
            Location = _mainForm.Location;
            if (guide?.Items?.Count > 0)
            {
                ShowNextGuide(false);
            }
        }

        private Dictionary<string, Bitmap> dicImage;

        private Bitmap _backgroundImage; // 存储背景截图

        private void RefreshBackImg(string exec)
        {
            var strKey = string.IsNullOrEmpty(exec) ? "DEFAULT" : exec;
            if (!dicImage.TryGetValue(strKey, out _backgroundImage) || _backgroundImage == null)
            {
                var start = ServerTime.DateTime.Ticks;
                if (!string.IsNullOrEmpty(exec))
                    _mainForm.ExecuteScript(exec);

                var rect = new Rectangle();
                var image = Screenshot.CaptureHandle(_mainForm.Handle, ref rect);
                dicImage[strKey] = image;
                _backgroundImage = image;
                Console.Write(strKey + ":" + new TimeSpan(ServerTime.DateTime.Ticks - start).TotalMilliseconds.ToString("F0") + "ms");
            }
        }

        private float _MaxTitleFontSize;
        private float _MaxDescFontSize;

        private void InitializeControls()
        {
            if (CommonSetting.夜间模式)
            {
                // Celestial Clarity V1: Panel base color
                _guidePanelColor = Color.FromArgb(255, 20, 25, 55); // #141937 (Deep Indigo Night)
            }
            else
            {
                _guidePanelColor = Color.FromArgb(255, 30, 110, 235);
            }

            pnlMain = new Panel
            {
                // BackColor is effectively set by the Paint event's gradient
                Size = new Size(Math.Min(_mainForm.Width, (int)(450 * wScale)), Math.Min((int)(210 * hScale), _mainForm.Width)),
                Location = new Point(100, 100),
                Dock = DockStyle.None,
            };

            int cornerRadius = 12;
            pnlMain.Paint += (s, e) =>
            {
                e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
                e.Graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                e.Graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;
                e.Graphics.CompositingQuality = CompositingQuality.HighQuality;

                Rectangle rect = new Rectangle(0, 0, pnlMain.Width, pnlMain.Height);
                GraphicsPath path = CreateRoundedRectanglePath(rect, cornerRadius);

                // 1. 背景渐变 - Celestial Clarity V1 (极其微妙的渐变以保证清晰度)
                Color gradStartColor, gradEndColor;
                Color[] blendColors; // 颜色数组
                float[] blendPositions; // 位置数组，必须与颜色数组长度一致

                if (CommonSetting.夜间模式)
                {
                    gradStartColor = _guidePanelColor; // #141937 (深邃靛蓝之夜)
                    gradEndColor = Color.FromArgb(255, 25, 30, 60);   // #191E3C (极其细微的更亮靛蓝色)
                    blendColors = new Color[] {
                        gradStartColor,
                        Color.FromArgb(255, (gradStartColor.R + gradEndColor.R) / 2, (gradStartColor.G + gradEndColor.G) / 2, (gradStartColor.B + gradEndColor.B) / 2), // 中间过渡色
                        gradEndColor
                    };
                    blendPositions = new float[] { 0.0f, 0.5f, 1.0f }; // 对应3个颜色的3个位置
                }
                else // 日间模式渐变 (确保这里的颜色和位置数组也匹配)
                {
                    gradStartColor = Color.FromArgb(255, 220, 230, 255); // 示例浅蓝色
                    gradEndColor = Color.FromArgb(255, 240, 245, 255);   // 示例更浅的蓝色
                    Color midColor1 = Color.FromArgb(255, 225, 235, 255);
                    Color midColor2 = Color.FromArgb(255, 235, 240, 255);
                    blendColors = new Color[] { gradStartColor, midColor1, midColor2, gradEndColor }; // 日间模式用4种颜色示例
                    blendPositions = new float[] { 0.0f, 0.25f, 0.75f, 1.0f }; // 对应4个颜色的4个位置
                }

                // 创建并配置 ColorBlend 对象
                ColorBlend colorBlend = new ColorBlend();
                colorBlend.Colors = blendColors;
                colorBlend.Positions = blendPositions; // 确保 Positions 被正确赋值

                using (LinearGradientBrush gradientBrush = new LinearGradientBrush(rect, Color.Transparent, Color.Transparent, LinearGradientMode.Vertical))
                {
                    gradientBrush.InterpolationColors = colorBlend; // 应用自定义的颜色和位置
                    e.Graphics.FillPath(gradientBrush, path);
                }

                // 2. 微妙的内边框效果 - Celestial Clarity V1
                Color innerBorderColor = CommonSetting.夜间模式 ? Color.FromArgb(25, 200, 210, 230) : Color.FromArgb(70, 255, 255, 255); // Very low alpha, pale cool white for night
                using (Pen innerBorderPen = new Pen(innerBorderColor, 1.0f))
                {
                    innerBorderPen.Alignment = PenAlignment.Inset;
                    e.Graphics.DrawPath(innerBorderPen, path);
                }

                // 3. 轻微的顶部高光线 - Celestial Clarity V1
                Color topLineColor = CommonSetting.夜间模式 ? Color.FromArgb(20, 210, 220, 240) : Color.FromArgb(50, 255, 255, 255); // Very low alpha, pale cool white for night
                Rectangle topLineRect = new Rectangle(5, 1, pnlMain.Width - 10, 1);
                using (SolidBrush topLineBrush = new SolidBrush(topLineColor))
                {
                    e.Graphics.FillRectangle(topLineBrush, topLineRect);
                }

                // 4. 绘制月光/日光效果 (DrawSunLightEffect 已由用户恢复)
                DrawSunLightEffect(e.Graphics, rect);

                // 5. 文本绘制 - Celestial Clarity V1
                if (!string.IsNullOrEmpty(_titleText))
                {
                    e.Graphics.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;
                    using (SolidBrush titleBrush = new SolidBrush(CommonSetting.夜间模式 ? Color.FromArgb(255, 230, 235, 245) : Color.White)) // #E6EBF5
                    {
                        StringFormat titleFormat = new StringFormat
                        {
                            Alignment = StringAlignment.Near,
                            LineAlignment = StringAlignment.Near,
                            Trimming = StringTrimming.EllipsisCharacter
                        };
                        e.Graphics.DrawString(_titleText, _titleFont, titleBrush, _titleRect, titleFormat);
                    }
                }

                if (!string.IsNullOrEmpty(_descText))
                {
                    using (SolidBrush descBrush = new SolidBrush(CommonSetting.夜间模式 ? Color.FromArgb(255, 180, 190, 210) : Color.FromArgb(230, 230, 230))) // #B4BED2
                    {
                        StringFormat descFormat = new StringFormat
                        {
                            Alignment = StringAlignment.Near,
                            LineAlignment = StringAlignment.Near,
                            Trimming = StringTrimming.EllipsisCharacter
                        };
                        e.Graphics.DrawString(_descText, _descFont, descBrush, _descRect, descFormat);
                    }
                }
                path.Dispose(); // Dispose path after all drawing operations using it are complete
            };

            pnlMain.Region = new Region(CreateRoundedRectanglePath(
                new Rectangle(0, 0, pnlMain.Width, pnlMain.Height), cornerRadius));

            Controls.Add(pnlMain);

            _titleFont = CommonString.GetSysBoldFont(20);
            _descFont = CommonString.GetSysNormalFont(15);

            _titleRect = new Rectangle(24, 18, pnlMain.Width - 48, (int)(45 * hScale));
            _descRect = new Rectangle(24, _titleRect.Top + _titleRect.Height, pnlMain.Width - 48, pnlMain.Height - _titleRect.Height - (int)(50 * hScale));

            lnkSkip = new LinkLabel
            {
                BackColor = Color.Transparent, // Transparent to show panel gradient
                Text = "跳过".CurrentText(),
                Font = CommonString.GetSysBoldFont(13),
                Location = new Point(24, pnlMain.Height - (int)(42 * hScale)),
                AutoSize = true,
                LinkBehavior = LinkBehavior.NeverUnderline,
                TabStop = false,
                Cursor = Cursors.Hand
            };

            var btnWidth = (int)(100 * wScale);
            var btnFont = CommonString.GetSysBoldFont(16);
            btnPrev = new Button
            {
                Text = "上一步".CurrentText(),
                Size = new Size(btnWidth, (int)(36 * hScale)),
                Font = btnFont,
                Visible = false,
                TabStop = false,
                Location = new Point(pnlMain.Width - 30 - btnWidth * 2, pnlMain.Height - (int)(50 * hScale)),
                Cursor = Cursors.Hand
            };

            btnNext = new Button
            {
                Text = "下一步".CurrentText(),
                Size = new Size(btnWidth, (int)(36 * hScale)),
                Font = btnFont,
                Visible = false,
                TabStop = false,
                Cursor = Cursors.Hand,
                Location = new Point(btnPrev.Left + btnWidth + 5, pnlMain.Height - (int)(50 * hScale)) // Reduced gap from 10 to 5
            };

            if (CommonSetting.夜间模式)
            {
                // Celestial Clarity V1: Link and Button colors
                lnkSkip.ForeColor = Color.FromArgb(255, 140, 150, 170);      // #8C96AA
                lnkSkip.LinkColor = Color.FromArgb(255, 140, 150, 170);      // #8C96AA
                lnkSkip.ActiveLinkColor = Color.FromArgb(255, 160, 170, 190); // #A0AABE

                btnPrev.BackColor = Color.FromArgb(255, 80, 85, 90); // Steel Grey (#50555A)
                btnPrev.ForeColor = Color.FromArgb(255, 220, 225, 230); // Light grey text (#DCE1E6)

                btnNext.ForeColor = Color.White;
                btnNext.BackColor = Color.FromArgb(255, 0, 122, 255); // Azure Blue (#007AFF)
            }
            else
            {
                lnkSkip.ForeColor = Color.FromArgb(165, 205, 253); // 示例，请用实际日间模式颜色
                lnkSkip.LinkColor = Color.FromArgb(165, 205, 253);
                lnkSkip.ActiveLinkColor = Color.DarkBlue;

                btnPrev.BackColor = Color.FromArgb(0, 92, 230);
                btnPrev.ForeColor = Color.White;

                btnNext.BackColor = Color.White;
                btnNext.ForeColor = Color.FromArgb(0, 95, 235);
            }// Set button regions after color setup if Flatstyle is not Standard
            if (btnPrev.FlatStyle == FlatStyle.Flat)
            {
                btnPrev.Region = new Region(CreateRoundedRectanglePath(new Rectangle(0, 0, btnPrev.Width, btnPrev.Height), 8));
            }
            if (btnNext.FlatStyle == FlatStyle.Flat)
            {
                btnNext.Region = new Region(CreateRoundedRectanglePath(new Rectangle(0, 0, btnNext.Width, btnNext.Height), 8));
            }

            btnPrev.Click += (s, e) => ShowPreviousGuide();
            btnNext.Click += (s, e) => ShowNextGuide(true);
            lnkSkip.Click += (s, e) => CloseGuide();

            pnlMain.Controls.Add(btnPrev);
            pnlMain.Controls.Add(btnNext);
            pnlMain.Controls.Add(lnkSkip);
            CommonMethod.SetStyle(lnkSkip, ControlStyles.Selectable, false);
            _MaxTitleFontSize = _titleFont.Size;
            _MaxDescFontSize = _descFont.Size;
        }

        private Random _sunEffectRand = new Random();

        private void DrawSunLightEffect(Graphics g, Rectangle bounds)
        {
            g.SmoothingMode = SmoothingMode.AntiAlias;
            g.InterpolationMode = InterpolationMode.HighQualityBicubic;
            g.PixelOffsetMode = PixelOffsetMode.HighQuality;
            g.CompositingQuality = CompositingQuality.HighQuality;

            PointF sunOrigin = new PointF(bounds.Width - (float)(8 * wScale), (float)(8 * hScale));
            float maxDimension = Math.Max(bounds.Width, bounds.Height);

            Color coreCenterColor, coreSurroundColor, outerCenterBaseColor, outerSurroundColor, wideGlowCenterColor;
            Color flareBase1, flareBase2, flareBase3;

            if (CommonSetting.夜间模式) // Celestial Clarity V1 Effect
            {
                coreCenterColor = Color.FromArgb(255, 255, 250, 180); // 鲜明的淡黄色核心
                coreSurroundColor = Color.FromArgb(170, 255, 250, 170); // 略柔和的淡黄色边缘光
                outerCenterBaseColor = Color.FromArgb(255, 200, 210, 230); // Pale cool white (for flare calcs, if needed)
                outerSurroundColor = Color.FromArgb(0, 180, 190, 220);    // Transparent pale cool blue (for flare calcs, if needed)
                wideGlowCenterColor = Color.FromArgb(60, 255, 245, 160); // 夜间月晕: 更自然的光晕中心 (Alpha 60)

                // Flares drastically reduced for Celestial Clarity
                flareBase1 = Color.FromArgb(150, 210, 220, 240); // Very faint pale blue/white
                flareBase2 = Color.FromArgb(130, 200, 210, 230); // Very faint pale blue/white
                flareBase3 = Color.FromArgb(110, 190, 200, 220); // Very faint pale blue/white
            }
            else // Daytime Sun Effect (Original Colors)
            {
                coreCenterColor = Color.FromArgb(255, 255, 250, 120);
                coreSurroundColor = Color.FromArgb(50, 255, 230, 100);
                outerCenterBaseColor = Color.FromArgb(255, 255, 240, 80); // 日间太阳: 更黄的基色
                outerSurroundColor = Color.FromArgb(0, 255, 240, 120);
                wideGlowCenterColor = Color.FromArgb(5, 255, 245, 170);

                flareBase1 = Color.FromArgb(255, 220, 200);
                flareBase2 = Color.FromArgb(180, 220, 255);
                flareBase3 = Color.FromArgb(255, 240, 200);
            }

            float coreGlowRadius = (float)(maxDimension * 0.06 * Math.Min(wScale, hScale));
            if (coreGlowRadius > 0)
            {
                using (GraphicsPath coreGlowPath = new GraphicsPath())
                {
                    coreGlowPath.AddEllipse(sunOrigin.X - coreGlowRadius, sunOrigin.Y - coreGlowRadius, coreGlowRadius * 2, coreGlowRadius * 2);
                    using (PathGradientBrush coreGlowBrush = new PathGradientBrush(coreGlowPath))
                    {
                        coreGlowBrush.CenterPoint = sunOrigin;
                        coreGlowBrush.CenterColor = coreCenterColor;
                        coreGlowBrush.SurroundColors = new Color[] { coreSurroundColor };
                        g.FillPath(coreGlowBrush, coreGlowPath);
                    }
                }
            }

            float outerGlowRadius = (float)(maxDimension * 0.12f * Math.Min(wScale, hScale)); // Celestial Clarity: outer glow factor
            if (outerGlowRadius > coreGlowRadius)
            {
                var step = CommonSetting.夜间模式 ? 1 : _sunEffectRand.Next(3, 6); // Celestial Clarity: Single wide glow for night
                for (int i = 0; i < step; i++)
                {
                    using (GraphicsPath wideGlowPath = new GraphicsPath())
                    {
                        float actualWideGlowRadiusValue;
                        if (CommonSetting.夜间模式)
                        {
                            actualWideGlowRadiusValue = outerGlowRadius * 0.75f; // 夜间月晕: 调整后更自然的大小
                        }
                        else // Day mode
                        {
                            actualWideGlowRadiusValue = outerGlowRadius * 3.0f; // 日间太阳: 恢复原有较大、更弥散的光晕
                        }
                        wideGlowPath.AddEllipse(sunOrigin.X - actualWideGlowRadiusValue, sunOrigin.Y - actualWideGlowRadiusValue, actualWideGlowRadiusValue * 2, actualWideGlowRadiusValue * 2);
                        using (PathGradientBrush wideGlowBrush = new PathGradientBrush(wideGlowPath))
                        {
                            wideGlowBrush.CenterPoint = sunOrigin;
                            wideGlowBrush.CenterColor = wideGlowCenterColor;
                            wideGlowBrush.SurroundColors = new Color[] { Color.FromArgb(0, wideGlowCenterColor.R, wideGlowCenterColor.G, wideGlowCenterColor.B) }; // Ensure transparent edge
                            g.FillPath(wideGlowBrush, wideGlowPath);
                        }
                    }
                }

                var flareDefinitions = new[]
                {
                    // Celestial Clarity: Flares drastically reduced in visibility
                    new { DistFactor = 0.2f, SizeFactor = 0.02f, BaseColor = flareBase1, Alpha = CommonSetting.夜间模式 ? 10: 40, Shape = 1 },
                    new { DistFactor = 0.4f, SizeFactor = 0.03f, BaseColor = flareBase2, Alpha = CommonSetting.夜间模式 ? 8: 35, Shape = 3 },
                    new { DistFactor = 0.6f, SizeFactor = 0.025f, BaseColor = flareBase3, Alpha = CommonSetting.夜间模式 ? 9: 25, Shape = 2 }
                };

                PointF flareDirectionTarget = new PointF(bounds.Width * 0.25f, bounds.Height * 0.70f);
                float pathLength = (float)Math.Sqrt(Math.Pow(flareDirectionTarget.X - sunOrigin.X, 2) + Math.Pow(flareDirectionTarget.Y - sunOrigin.Y, 2));
                if (pathLength == 0) pathLength = 1;

                foreach (var def in flareDefinitions)
                {
                    float distVariation = (float)(_sunEffectRand.NextDouble() * 0.03 - 0.015);
                    float flareDistOnPath = pathLength * (def.DistFactor + distVariation);

                    PointF flareCenter = new PointF(
                        sunOrigin.X + (flareDirectionTarget.X - sunOrigin.X) * (flareDistOnPath / pathLength) + (_sunEffectRand.Next(-2, 3) * (float)wScale),
                        sunOrigin.Y + (flareDirectionTarget.Y - sunOrigin.Y) * (flareDistOnPath / pathLength) + (_sunEffectRand.Next(-2, 3) * (float)hScale)
                    );

                    float baseFlareDim = maxDimension * def.SizeFactor * (float)Math.Min(wScale, hScale);
                    float flareW = baseFlareDim * (float)(0.9f + _sunEffectRand.NextDouble() * 0.2f);
                    float flareH = baseFlareDim * (float)(0.9f + _sunEffectRand.NextDouble() * 0.2f);

                    if (def.Shape == 1) flareW *= 2.0f;
                    if (def.Shape == 2) flareH *= 2.0f;

                    if (flareW < 1f) flareW = 1f;
                    if (flareH < 1f) flareH = 1f;

                    int currentAlpha = Math.Max(10, Math.Min(255, def.Alpha + _sunEffectRand.Next(-5, 6)));
                    Color flareColor = Color.FromArgb(currentAlpha, def.BaseColor);

                    using (GraphicsPath flarePath = new GraphicsPath())
                    {
                        if (def.Shape == 0 || def.Shape == 1 || def.Shape == 2)
                        {
                            flarePath.AddEllipse(flareCenter.X - flareW / 2, flareCenter.Y - flareH / 2, flareW, flareH);
                        }
                        else if (def.Shape == 3)
                        {
                            int points = 6;
                            float outerRadius = Math.Max(flareW, flareH) / 2;
                            float innerRadius = outerRadius * 0.5f;
                            PointF[] polyPoints = new PointF[points * 2];
                            for (int k = 0; k < points; k++)
                            {
                                double angleOuter = (Math.PI * 2.0 / points * k) - (Math.PI / 2.0);
                                polyPoints[k * 2] = new PointF(
                                    flareCenter.X + (float)(Math.Cos(angleOuter) * outerRadius),
                                    flareCenter.Y + (float)(Math.Sin(angleOuter) * outerRadius)
                                );
                                double angleInner = angleOuter + (Math.PI / points);
                                polyPoints[k * 2 + 1] = new PointF(
                                   flareCenter.X + (float)(Math.Cos(angleInner) * innerRadius),
                                   flareCenter.Y + (float)(Math.Sin(angleInner) * innerRadius)
                               );
                            }
                            flarePath.AddPolygon(polyPoints);
                        }

                        using (PathGradientBrush flareBrush = new PathGradientBrush(flarePath))
                        {
                            flareBrush.CenterPoint = flareCenter;
                            flareBrush.CenterColor = flareColor;
                            flareBrush.SurroundColors = new Color[] { Color.FromArgb(0, flareColor.R, flareColor.G, flareColor.B) };
                            g.FillPath(flareBrush, flarePath);
                        }
                    }
                }
            }
        }

        private void ShowNextGuide(bool isUser)
        {
            if (_currentGuideIndex < guide.Items.Count - 1)
            {
                _currentGuideIndex++;
                ShowGuide(guide.Items[_currentGuideIndex]);
                btnNext.Focus();
            }
            else if (isUser && _currentGuideIndex == guide.Items.Count - 1)
            {
                CloseGuide();
            }
        }

        private void ShowPreviousGuide()
        {
            if (_currentGuideIndex > 0)
            {
                _currentGuideIndex--;
                ShowGuide(guide.Items[_currentGuideIndex]);
                if (btnPrev.Visible)
                    btnPrev.Focus();
                else
                    btnNext.Focus();
            }
        }

        private void ShowGuide(GuideItem item)
        {
            this.BeginUpdate();
            try
            {
                RefreshBackImg(item.Exec);

                var rect = item.Rect;
                if (!string.IsNullOrEmpty(item.Ctrl))
                {
                    var targetRect = FindTargetControl(item.Ctrl);
                    if (!targetRect.IsEmpty)
                    {
                        rect = targetRect;
                        var frmPadding = _mainForm.Padding;
                        rect.Y += frmPadding.Top;
                        rect.X += frmPadding.Left;
                        rect = rect.ZoomBig(10);
                    }
                }

                if (!rect.IsEmpty)
                {
                    if (!guide.BaseSize.IsEmpty)
                    {
                        var widthScale = Width * 1d / guide.BaseSize.Width;
                        var heightScale = Height * 1d / guide.BaseSize.Height;
                        rect.X = (int)(rect.X * widthScale);
                        rect.Y = (int)(rect.Y * heightScale);
                        rect.Width = (int)(rect.Width * widthScale);
                        rect.Height = (int)(rect.Height * heightScale);
                    }

                    if (rect.X < 0) rect.X += Width;
                    else if (rect.Y < 0) rect.Y += Height;
                }

                SuspendLayout();

                _titleFont = CommonMethod.ScaleLabelByHeight(item.Title, CommonString.GetUserNormalFont(8F, FontStyle.Bold), new Size(_titleRect.Width, _titleRect.Height), _MaxTitleFontSize);
                _titleText = item.Title;
                _descFont = CommonMethod.ScaleLabelByHeight(item.Desc, CommonString.GetUserNormalFont(8F), new Size(_descRect.Width, _descRect.Height), _MaxDescFontSize);
                _descText = item.Desc;

                var index = guide.Items.IndexOf(item);
                if (guide.ShowSummary)
                    lnkSkip.Text = "跳过".CurrentText() + " [Esc]" + (index > 0 ? $" ({index}/{guide.Items.Count - 1})" : "");
                else
                    lnkSkip.Text = "跳过".CurrentText() + $" [Esc] ({index + 1}/{guide.Items.Count})";

                var arrow = SetGuidePanelPosition(rect);
                UpdateButtonVisibility();

                _currentHighlightRect = rect;
                _currentArrowDirection = arrow;

                ResumeLayout(false);
            }
            finally
            {
                this.EndUpdate();
            }
        }

        private GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius * 2, radius * 2, 180, 90);
            path.AddArc(rect.Right - radius * 2, rect.Y, radius * 2, radius * 2, 270, 90);
            path.AddArc(rect.Right - radius * 2, rect.Bottom - radius * 2, radius * 2, radius * 2, 0, 90);
            path.AddArc(rect.X, rect.Bottom - radius * 2, radius * 2, radius * 2, 90, 90);
            path.CloseFigure();
            return path;
        }

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            if (_animationStep >= ANIMATION_STEPS)
            {
                _animationTimer.Stop();
                pnlMain.Location = _targetPanelLocation;
                _animationStep = 0;
                return;
            }

            float progress = (float)_animationStep / ANIMATION_STEPS;
            float easedProgress = 1 - (float)Math.Pow(1 - progress, 3);

            int newX = (int)(_startPanelLocation.X + ((_targetPanelLocation.X - _startPanelLocation.X) * easedProgress));
            int newY = (int)(_startPanelLocation.Y + ((_targetPanelLocation.Y - _startPanelLocation.Y) * easedProgress));

            pnlMain.Location = new Point(newX, newY);
            _animationStep++;
        }

        private ArrowDirection SetGuidePanelPosition(Rectangle targetArea)
        {
            ArrowDirection arrow;
            int guidePanelWidth = pnlMain.Width;
            int guidePanelHeight = pnlMain.Height;
            int targetControlX = targetArea.X;
            int targetControlY = targetArea.Y;
            int targetControlWidth = targetArea.Width;
            int targetControlHeight = targetArea.Height;
            int screenWidth = Width;
            int screenHeight = Height;
            int guidePanelX, guidePanelY;

            if (targetArea.IsEmpty)
            {
                arrow = ArrowDirection.Down; // No arrow if no target area (e.g. summary)
                guidePanelX = (screenWidth - guidePanelWidth) / 2; // Center it
                guidePanelY = (screenHeight - guidePanelHeight) / 2;
            }
            else
            {
                // Try right
                if (targetControlX + targetControlWidth + guidePanelWidth + 20 <= screenWidth)
                {
                    arrow = ArrowDirection.Left; // Arrow points left from panel to target
                    guidePanelX = targetControlX + targetControlWidth + 20;
                    guidePanelY = targetControlY + (targetControlHeight - guidePanelHeight) / 2;
                }
                // Try left
                else if (targetControlX - guidePanelWidth - 20 >= 0)
                {
                    arrow = ArrowDirection.Right; // Arrow points right from panel to target
                    guidePanelX = targetControlX - guidePanelWidth - 20;
                    guidePanelY = targetControlY + (targetControlHeight - guidePanelHeight) / 2;
                }
                // Try below
                else if (targetControlY + targetControlHeight + guidePanelHeight + 20 <= screenHeight)
                {
                    arrow = ArrowDirection.Up; // Arrow points up from panel to target
                    guidePanelX = targetControlX + (targetControlWidth - guidePanelWidth) / 2;
                    guidePanelY = targetControlY + targetControlHeight + 20;
                }
                // Default to above
                else
                {
                    arrow = ArrowDirection.Down; // Arrow points down from panel to target
                    guidePanelX = targetControlX + (targetControlWidth - guidePanelWidth) / 2;
                    guidePanelY = targetControlY - guidePanelHeight - 20;
                }
            }

            guidePanelX = Math.Max(5, Math.Min(guidePanelX, screenWidth - guidePanelWidth - 5));
            guidePanelY = Math.Max(5, Math.Min(guidePanelY, screenHeight - guidePanelHeight - 5));

            _targetPanelLocation = new Point(guidePanelX, guidePanelY);

            if (pnlMain.Location.IsEmpty || pnlMain.Location == new Point(0, 0) /* initial default might be 0,0 */ )
            {
                pnlMain.Location = _targetPanelLocation;
            }
            else if (pnlMain.Location != _targetPanelLocation) // Animate only if location changes
            {
                _startPanelLocation = pnlMain.Location;
                _animationStep = 0;
                _animationTimer.Start();
            }
            return arrow;
        }

        private void UpdateButtonVisibility()
        {
            btnPrev.Visible = (_currentGuideIndex > 0);
            btnNext.Visible = (_currentGuideIndex <= guide.Items.Count - 1);
            lnkSkip.Visible = guide.Items.Count > 1; // Show skip if more than one item
            btnNext.Text = (_currentGuideIndex == guide.Items.Count - 1) ? "完成".CurrentText() : "下一步".CurrentText();
        }

        private Rectangle FindTargetControl(string controlName)
        {
            var control = _mainForm.Controls.Find(controlName, true).FirstOrDefault();
            if (control != null) return control.Bounds;

            control = _mainForm.Controls.OfType<Control>().FirstOrDefault(p => Equals(p.AccessibleDescription, controlName));
            if (control != null) return control.Bounds;

            return Rectangle.Empty;
        }

        private void CloseGuide()
        {
            _currentGuideIndex = -1;
            Controls.Clear();

            if (guide.Items != null && guide.Items.Count > 0 && !string.IsNullOrEmpty(guide.Items[0].Exec))
                _mainForm.ExecuteScript(guide.Items[0].Exec);

            this.Close();
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            // Do nothing here to support transparency and custom painting.
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            try
            {
                e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
                e.Graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                e.Graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;
                e.Graphics.CompositingQuality = CompositingQuality.HighQuality;

                if (_backgroundImage != null)
                {
                    e.Graphics.CompositingMode = CompositingMode.SourceOver;
                    e.Graphics.DrawImage(_backgroundImage, 0, 0, this.Width, this.Height);

                    Color maskColor;
                    Color bubbleBorderColor; // Renamed from highlightBorderColor for clarity
                    float borderWidth;

                    if (CommonSetting.夜间模式)
                    {
                        maskColor = Color.FromArgb(220, 10, 10, 20); // Darker, slightly blue-tinted mask
                        bubbleBorderColor = Color.FromArgb(255, 0, 122, 204); // Techy blue border for highlight
                        borderWidth = 1.5f; // Slightly thicker for better visibility
                    }
                    else
                    {
                        maskColor = Color.FromArgb(190, 0, 0, 0);
                        bubbleBorderColor = Color.White;
                        borderWidth = 1.0f;
                    }

                    using (SolidBrush maskBrush = new SolidBrush(maskColor))
                    {
                        if (_currentHighlightRect.IsEmpty)
                        {
                            e.Graphics.FillRectangle(maskBrush, ClientRectangle);
                        }
                        else
                        {
                            Rectangle expandedRect = new Rectangle(
                                _currentHighlightRect.X - 2, _currentHighlightRect.Y - 2,
                                _currentHighlightRect.Width + 4, _currentHighlightRect.Height + 4);

                            // The arrow direction for the bubble path should be opposite to panel's arrow
                            // This part of logic might need adjustment based on how _currentArrowDirection for panel is set
                            // So the bubble's arrow should point *towards* the target.
                            // This needs careful review of how _currentArrowDirection is determined and used for pnlMain.
                            // For simplicity, let's assume the bubble itself doesn't draw an arrow, just highlights.
                            // If an arrow IS needed for the highlight bubble, its direction needs to be determined correctly.

                            // For a simple highlight without an arrow:
                            GraphicsPath highlightPath = CreateRoundedRectanglePath(expandedRect, 8);

                            // If an arrow is desired for the highlight bubble (pointing *to* the target):
                            // ArrowDirection highlightArrowDirection = InvertArrowDirection(_currentArrowDirection); // You'd need an InvertArrowDirection helper
                            // Point arrowBase = GetArrowBasePoint(expandedRect, highlightArrowDirection);
                            // Point arrowTip = GetArrowTipPoint(arrowBase, highlightArrowDirection, 12); // Tip points outwards
                            // GraphicsPath highlightPath = CreateUnifiedBubblePath(expandedRect, 8, arrowBase, arrowTip, 8, highlightArrowDirection);


                            using (Region formRegion = new Region(ClientRectangle))
                            {
                                formRegion.Exclude(highlightPath);
                                e.Graphics.FillRegion(maskBrush, formRegion);
                            }

                            // For dark mode, we use a distinct techy blue border.
                            // For light mode, a white border is fine.
                            using (Pen borderPen = new Pen(bubbleBorderColor, borderWidth))
                            {
                                borderPen.LineJoin = LineJoin.Round;
                                e.Graphics.DrawPath(borderPen, highlightPath);
                            }
                            highlightPath.Dispose();
                        }
                    }
                }
                base.OnPaint(e); // Draw child controls (like pnlMain)
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"OnPaint 异常: {ex.Message}");
            }
        }

        // Helper to invert arrow direction if needed for the highlight bubble
        private ArrowDirection InvertArrowDirection(ArrowDirection direction)
        {
            switch (direction)
            {
                case ArrowDirection.Up: return ArrowDirection.Down;
                case ArrowDirection.Down: return ArrowDirection.Up;
                case ArrowDirection.Left: return ArrowDirection.Right;
                case ArrowDirection.Right: return ArrowDirection.Left;
                default: return ArrowDirection.Down;
            }
        }


        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (_bufferedGraphics != null)
                {
                    _bufferedGraphics.Dispose();
                    _bufferedGraphics = null;
                }
                if (_backgroundImage != null)
                {
                    _backgroundImage.Dispose();
                    _backgroundImage = null;
                }
                if (_animationTimer != null)
                {
                    _animationTimer.Stop();
                    _animationTimer.Dispose();
                    _animationTimer = null;
                }
                // Dispose panel and its controls if not done automatically
                if (pnlMain != null)
                {
                    pnlMain.Dispose();
                    pnlMain = null;
                }
            }
            base.Dispose(disposing);
        }
    }

    // Assuming ArrowDirection and CommonSetting are defined elsewhere
    // public enum ArrowDirection { None, Up, Down, Left, Right }
    // public static class CommonSetting { public static bool 夜间模式 = false; } // Placeholder
    // public static class CommonString { /* ... */ } // Placeholder
    // public static class CommonMethod { /* ... */ } // Placeholder
    // public class GuideEntity { /* ... */ } // Placeholder
    // public class GuideItem { /* ... */ } // Placeholder
    // public static class ServerTime { public static DateTime DateTime => System.DateTime.Now; } // Placeholder
}
