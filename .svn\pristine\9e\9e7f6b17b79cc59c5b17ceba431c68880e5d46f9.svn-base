﻿using OCRTools;
using OCRTools.Language;
using System.Drawing;

namespace OCRTools.ScreenCaptureLib
{
    public class HighlightEffectShape : BaseEffectShape
    {
        public override ShapeType ShapeType { get; } = ShapeType.高亮;

        public override string OverlayText => "高亮".CurrentText();

        public Color HighlightColor { get; set; }

        public override void OnConfigLoad()
        {
            HighlightColor = AnnotationOptions.HighlightColor;
        }

        public override void OnConfigSave()
        {
            AnnotationOptions.HighlightColor = HighlightColor;
        }

        public override void ApplyEffect(Bitmap bmp)
        {
            ImageHelp.HighlightImage(bmp, HighlightColor);
        }
    }
}