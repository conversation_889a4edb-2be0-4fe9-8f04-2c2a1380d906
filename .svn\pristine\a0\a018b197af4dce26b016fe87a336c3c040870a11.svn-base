using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace OCRTools
{
    public class DesEncryption
    {
        public static string EncryptDES(string encryptString, string encryptKey = "10066100")
        {
            try
            {
                byte[] bytes = Encoding.ASCII.GetBytes(encryptKey.Substring(0, 8));
                byte[] rgbIV = bytes;
                byte[] bytes2 = Encoding.UTF8.GetBytes(encryptString);
                DESCryptoServiceProvider dESCryptoServiceProvider = new DESCryptoServiceProvider();
                MemoryStream memoryStream = new MemoryStream();
                CryptoStream cryptoStream = new CryptoStream(memoryStream, dESCryptoServiceProvider.CreateEncryptor(bytes, rgbIV), CryptoStreamMode.Write);
                cryptoStream.Write(bytes2, 0, bytes2.Length);
                cryptoStream.FlushFinalBlock();
                StringBuilder stringBuilder = new StringBuilder();
                byte[] array = memoryStream.ToArray();
                foreach (byte b in array)
                {
                    stringBuilder.AppendFormat("{0:X2}", b);
                }
                stringBuilder.ToString();
                return stringBuilder.ToString();
            }
            catch
            {
                return null;
            }
        }

        public static string DecryptDES(string decryptString, string decryptKey = "10066100")
        {
            try
            {
                byte[] bytes = Encoding.ASCII.GetBytes(decryptKey);
                byte[] rgbIV = bytes;
                byte[] array = new byte[decryptString.Length / 2];
                for (int i = 0; i < decryptString.Length / 2; i++)
                {
                    int num = Convert.ToInt32(decryptString.Substring(i * 2, 2), 16);
                    array[i] = (byte)num;
                }
                DESCryptoServiceProvider dESCryptoServiceProvider = new DESCryptoServiceProvider();
                MemoryStream memoryStream = new MemoryStream();
                CryptoStream cryptoStream = new CryptoStream(memoryStream, dESCryptoServiceProvider.CreateDecryptor(bytes, rgbIV), CryptoStreamMode.Write);
                cryptoStream.Write(array, 0, array.Length);
                cryptoStream.FlushFinalBlock();
                return Encoding.UTF8.GetString(memoryStream.ToArray());
            }
            catch
            {
                return null;
            }
        }
    }
}
