using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolQuickCatch : ToolObject
    {
        private DrawQuickCatch drawCatch;

        private bool isfill = true;

        private Point point;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            point = e.Location;
            drawCatch = new DrawQuickCatch(e.X, e.Y, 1, 1);
            drawArea.Catch = drawCatch;
            NewObject(drawArea, drawCatch);
            drawCatch.ChangeRect(e.X, e.Y, 1, 1);
            drawArea.isAutoDraw = false;
            if (drawArea.IsShowCross)
            {
                drawArea.Cursor = CursorEx.Cross;
                drawArea.IsShowCross = false;
            }
        }

        protected void NewObject(DrawArea drawArea, DrawObject o)
        {
            drawArea.GraphicsList.UnselectAll();
            drawArea.GraphicsList.graphicsList.Insert(0, o);
            drawArea.Capture = true;
            o.Selected = true;
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            var num = Math.Abs(e.X - point.X);
            var num2 = Math.Abs(e.Y - point.Y);
            if (e.Button != MouseButtons.Left) return;
            if (drawCatch == null)
            {
                drawArea.ActiveTool = DrawToolType.Text;
                return;
            }

            drawCatch.IsSelected = true;
            if (num > 10 && num2 > 10)
            {
                if (isfill)
                {
                    drawCatch.MoveHandleTo(e.Location, 5);
                    drawArea.Refresh();
                    isfill = false;
                }
                else
                {
                    using (new AutomaticCanvasRefresher(drawArea, drawCatch.GetAddBound))
                    {
                        var drawQuickCatch = drawCatch;
                        using (new AutomaticCanvasRefresher(drawArea, drawQuickCatch.GetBoundingBox))
                        {
                            drawCatch.MoveHandleTo(e.Location, 5);
                        }
                    }
                }
            }
            else if (!isfill)
            {
                using (new AutomaticCanvasRefresher(drawArea, drawCatch.GetAddBound))
                {
                    var drawQuickCatch2 = drawCatch;
                    using (new AutomaticCanvasRefresher(drawArea, drawQuickCatch2.GetBoundingBox))
                    {
                        drawCatch.MoveHandleTo(e.Location, 5);
                    }
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            drawArea.isShowZoom = false;
            if (drawCatch != null)
            {
                StaticValue.current_Rectangle = drawCatch.Rectangle;
                if (Math.Abs(e.X - point.X) < 10 && Math.Abs(e.Y - point.Y) < 10)
                {
                    drawCatch = new DrawQuickCatch(drawArea.AutoRect.SizeOffset(-1));
                    StaticValue.current_Rectangle = drawArea.AutoRect.SizeOffset(-1);
                    drawArea.GraphicsList.RemoveAt(0);
                    drawArea.GraphicsList.Add(drawCatch);
                    StaticValue.Catch_Rectangle = StaticValue.current_Rectangle;
                }

                drawCatch.Normalize();
                drawArea.AddCommandToHistory(new CommandAdd(drawCatch));
                StaticValue.current_ToolType = DrawToolType.QuickCatch;
                drawArea.Closing();
            }
        }
    }
}