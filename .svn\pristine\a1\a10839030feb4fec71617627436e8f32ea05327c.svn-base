using System;
using System.Drawing;
using System.Runtime.InteropServices;

namespace OCRTools
{
    internal static class DrawHelp
    {
        private static void DrawLineEx(IntPtr hdc, Point startpointX, Point endpointX)
        {
            MoveToEx(hdc, startpointX.X, startpointX.Y, IntPtr.Zero);
            LineTo(hdc, endpointX.X, endpointX.Y);
        }

        public static void DrawCrosshair(Graphics grfx, DrawArea drawArea)
        {
            var hdc = grfx.GetHdc();
            var obj = CreatePen(0, 1, ColorTranslator.ToWin32(Color.WhiteSmoke));
            var rop = SetROP2(hdc, 5);
            var obj2 = SelectObject(hdc, obj);
            var startpointX = new Point(0, drawArea.CurrentPosition.Y);
            var startpointX2 = new Point(drawArea.CurrentPosition.X, 0);
            var endpointX = new Point(drawArea.Width, drawArea.CurrentPosition.Y);
            var endpointX2 = new Point(drawArea.CurrentPosition.X, drawArea.Height);
            DrawLineEx(hdc, startpointX, endpointX);
            DrawLineEx(hdc, startpointX2, endpointX2);
            SelectObject(hdc, obj2);
            SetROP2(hdc, rop);
            grfx.ReleaseHdc(hdc);
        }

        [DllImport("Gdi32.dll")]
        private static extern IntPtr CreatePen(int fnPenStyle, int width, int color);

        [DllImport("Gdi32.dll")]
        private static extern int SetROP2(IntPtr hdc, int rop);

        [DllImport("Gdi32.dll")]
        private static extern int MoveToEx(IntPtr hdc, int x, int y, IntPtr lppoint);

        [DllImport("Gdi32.dll")]
        private static extern int LineTo(IntPtr hdc, int x, int y);

        [DllImport("Gdi32.dll")]
        private static extern IntPtr SelectObject(IntPtr hdc, IntPtr obj);
    }
}