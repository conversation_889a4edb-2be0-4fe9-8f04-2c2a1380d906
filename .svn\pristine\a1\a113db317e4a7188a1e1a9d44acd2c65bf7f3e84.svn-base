using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolMultiCatch : ToolObject
    {
        private DrawMultiCatch drawRectangle;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.current_ToolType = DrawToolType.MultiCatch;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else if (drawArea.GraphicsList.Count < 99)
            {
                drawRectangle = new DrawMultiCatch(e.X, e.Y, 1, 1);
                AddNewObject(drawArea, drawRectangle);
                var num = drawArea.NumberArry[drawArea.GraphicsList.Count - 1];
                drawRectangle.Text = num.ToString();
            }
        }

        protected new void AddNewObject(DrawArea drawArea, DrawObject o)
        {
            drawArea.GraphicsList.UnselectAll();
            drawArea.GraphicsList.Add(o);
            drawArea.Capture = true;
            o.Selected = true;
            drawArea.Refresh();
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left)
            {
                if (drawRectangle == null)
                {
                    drawArea.ActiveTool = DrawToolType.Text;
                    return;
                }

                drawRectangle.IsSelected = true;
                var drawMultiCatch = drawRectangle;
                using (new AutomaticCanvasRefresher(drawArea, drawMultiCatch.GetBoundingBox))
                {
                    drawRectangle.MoveHandleTo(e.Location, 5);
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawRectangle != null)
            {
                StaticValue.current_Rectangle = drawRectangle.Rectangle;
                if (!drawRectangle.Rectangle.IsLimt())
                {
                    drawRectangle.Selected = false;
                    drawArea.GraphicsList.RemoveAt(0);
                }
                else
                {
                    var drawMultiCatch = drawRectangle;
                    using (new AutomaticCanvasRefresher(drawArea, drawMultiCatch.GetBoundingBox))
                    {
                        drawRectangle.Normalize();
                        drawArea.AddCommandToHistory(new CommandAdd(drawRectangle));
                    }

                    drawArea.ShowMultiTool(drawRectangle);
                }

                drawArea.ActiveTool = DrawToolType.MultiCatch;
                StaticValue.current_ToolType = DrawToolType.MultiCatch;
            }
        }
    }
}