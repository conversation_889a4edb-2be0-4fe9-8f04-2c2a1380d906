﻿using System.ComponentModel;

namespace OCRTools
{
    public class ScrollingCaptureOptions
    {
        public int CombineAdjustmentLastVertical = 0;
        public int CombineAdjustmentVertical = 0;
        public int IgnoreLast = 0;
        public int TrimBottomEdge = 0;

        public int TrimLeftEdge = 0;
        public int TrimRightEdge = 0;
        public int TrimTopEdge = 0;

        [DefaultValue(ScrollingCaptureScrollMethod.Automatic)]
        public ScrollingCaptureScrollMethod ScrollMethod { get; set; } = ScrollingCaptureScrollMethod.Automatic;

        [DefaultValue(500)] public int StartDelay { get; set; } = 500;

        [DefaultValue(500)] public int ScrollDelay { get; set; } = 500;

        [DefaultValue(20)] public int MaximumScrollCount { get; set; } = 20;

        [DefaultValue(ScrollingCaptureScrollTopMethod.All)]
        public ScrollingCaptureScrollTopMethod ScrollTopMethodBeforeCapture { get; set; } =
            ScrollingCaptureScrollTopMethod.All;

        [DefaultValue(true)] public bool AutoDetectScrollEnd { get; set; } = true;

        [DefaultValue(true)] public bool RemoveDuplicates { get; set; } = true;

        [DefaultValue(true)] public bool AfterCaptureAutomaticallyCombine { get; set; } = true;
    }
}