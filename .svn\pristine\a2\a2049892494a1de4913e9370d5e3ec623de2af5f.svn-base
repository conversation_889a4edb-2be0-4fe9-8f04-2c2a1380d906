﻿using System.Drawing;

namespace MetroFramework
{
    public static class MetroFonts
    {
        private static readonly IMetroFontResolver FontResolver;

        static MetroFonts()
        {
            FontResolver = new DefaultFontResolver();
        }

        public static Font Title => DefaultLight(24f);

        public static Font Subtitle => Default(14f);

        public static Font TileCount => Default(44f);

        public static Font DefaultLight(float size)
        {
            return FontResolver.ResolveFont("Segoe UI Light", size, FontStyle.Regular, GraphicsUnit.Pixel);
        }

        public static Font Default(float size)
        {
            return FontResolver.ResolveFont("Segoe UI", size, FontStyle.Regular, GraphicsUnit.Pixel);
        }

        public static Font DefaultBold(float size)
        {
            return FontResolver.ResolveFont("Segoe UI", size, FontStyle.Bold, GraphicsUnit.Pixel);
        }

        public static Font TabControl(MetroTabControlSize labelSize, MetroTabControlWeight labelWeight)
        {
            switch (labelSize)
            {
                case MetroTabControlSize.Small:
                    switch (labelWeight)
                    {
                        case MetroTabControlWeight.Light:
                            return DefaultLight(12f);
                        case MetroTabControlWeight.Regular:
                            return Default(12f);
                        case MetroTabControlWeight.Bold:
                            return DefaultBold(12f);
                    }

                    break;
                case MetroTabControlSize.Medium:
                    switch (labelWeight)
                    {
                        case MetroTabControlWeight.Light:
                            return DefaultLight(14f);
                        case MetroTabControlWeight.Regular:
                            return Default(14f);
                        case MetroTabControlWeight.Bold:
                            return DefaultBold(14f);
                    }

                    break;
                case MetroTabControlSize.Tall:
                    switch (labelWeight)
                    {
                        case MetroTabControlWeight.Light:
                            return DefaultLight(18f);
                        case MetroTabControlWeight.Regular:
                            return Default(18f);
                        case MetroTabControlWeight.Bold:
                            return DefaultBold(18f);
                    }

                    break;
            }

            return DefaultLight(14f);
        }

        public static Font CheckBox(MetroCheckBoxSize linkSize, MetroCheckBoxWeight linkWeight)
        {
            switch (linkSize)
            {
                case MetroCheckBoxSize.Small:
                    switch (linkWeight)
                    {
                        case MetroCheckBoxWeight.Light:
                            return DefaultLight(12f);
                        case MetroCheckBoxWeight.Regular:
                            return Default(12f);
                        case MetroCheckBoxWeight.Bold:
                            return DefaultBold(12f);
                    }

                    break;
                case MetroCheckBoxSize.Medium:
                    switch (linkWeight)
                    {
                        case MetroCheckBoxWeight.Light:
                            return DefaultLight(14f);
                        case MetroCheckBoxWeight.Regular:
                            return Default(14f);
                        case MetroCheckBoxWeight.Bold:
                            return DefaultBold(14f);
                    }

                    break;
                case MetroCheckBoxSize.Tall:
                    switch (linkWeight)
                    {
                        case MetroCheckBoxWeight.Light:
                            return DefaultLight(18f);
                        case MetroCheckBoxWeight.Regular:
                            return Default(18f);
                        case MetroCheckBoxWeight.Bold:
                            return DefaultBold(18f);
                    }

                    break;
            }

            return Default(12f);
        }

        public static Font Button(MetroButtonSize linkSize, MetroButtonWeight linkWeight)
        {
            switch (linkSize)
            {
                case MetroButtonSize.Small:
                    switch (linkWeight)
                    {
                        case MetroButtonWeight.Light:
                            return DefaultLight(11f);
                        case MetroButtonWeight.Regular:
                            return Default(11f);
                        case MetroButtonWeight.Bold:
                            return DefaultBold(11f);
                    }

                    break;
                case MetroButtonSize.Medium:
                    switch (linkWeight)
                    {
                        case MetroButtonWeight.Light:
                            return DefaultLight(13f);
                        case MetroButtonWeight.Regular:
                            return Default(13f);
                        case MetroButtonWeight.Bold:
                            return DefaultBold(13f);
                    }

                    break;
                case MetroButtonSize.Tall:
                    switch (linkWeight)
                    {
                        case MetroButtonWeight.Light:
                            return DefaultLight(16f);
                        case MetroButtonWeight.Regular:
                            return Default(16f);
                        case MetroButtonWeight.Bold:
                            return DefaultBold(16f);
                    }

                    break;
            }

            return Default(11f);
        }

        internal interface IMetroFontResolver
        {
            Font ResolveFont(string familyName, float emSize, FontStyle fontStyle, GraphicsUnit unit);
        }

        private class DefaultFontResolver : IMetroFontResolver
        {
            public Font ResolveFont(string familyName, float emSize, FontStyle fontStyle, GraphicsUnit unit)
            {
                return new Font(familyName, emSize, fontStyle, unit);
            }
        }
    }

    public enum MetroTabControlSize
    {
        Small,
        Medium,
        Tall
    }

    public enum MetroTabControlWeight
    {
        Light,
        Regular,
        Bold
    }

    public enum MetroCheckBoxSize
    {
        Small,
        Medium,
        Tall
    }

    public enum MetroCheckBoxWeight
    {
        Light,
        Regular,
        Bold
    }

    public enum MetroButtonSize
    {
        Small,
        Medium,
        Tall
    }

    public enum MetroButtonWeight
    {
        Light,
        Regular,
        Bold
    }
}