using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class DATEMODE : Record
	{
		public short Mode;

		public DATEMODE(Record record)
			: base(record)
		{
		}

		public DATEMODE()
		{
			Type = 34;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			Mode = binaryReader.ReadInt16();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(Mode);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
