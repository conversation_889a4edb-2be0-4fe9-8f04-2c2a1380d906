namespace UtfUnknown.Core
{
    public class BitPackage
    {
        public static int indexShift4Bits = 3;

        public static int shiftMask4Bits = 7;

        public static int bitShift4Bits = 2;

        public static int unitMask4Bits = 15;

        private readonly int _bitShift;

        private readonly int[] _data;

        private readonly int _indexShift;

        private readonly int _shiftMask;

        private readonly int _unitMask;

        public BitPackage(int indexShift, int shiftMask, int bitShift, int unitMask, int[] data)
        {
            _indexShift = indexShift;
            _shiftMask = shiftMask;
            _bitShift = bitShift;
            _unitMask = unitMask;
            _data = data;
        }

        public static int Pack16Bits(int a, int b)
        {
            return (b << 16) | a;
        }

        public static int Pack8Bits(int a, int b, int c, int d)
        {
            return Pack16Bits((b << 8) | a, (d << 8) | c);
        }

        public static int Pack4Bits(int a, int b, int c, int d, int e, int f, int g, int h)
        {
            return Pack8Bits((b << 4) | a, (d << 4) | c, (f << 4) | e, (h << 4) | g);
        }

        public int Unpack(int i)
        {
            return (_data[i >> _indexShift] >> ((i & _shiftMask) << _bitShift)) & _unitMask;
        }
    }
}