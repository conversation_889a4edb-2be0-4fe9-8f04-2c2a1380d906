﻿using OcrLib;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;

namespace OcrMain
{
    public class MyHttpServer : HttpServer
    {
        public MyHttpServer(int port)
            : base(port)
        {
            Task.Factory.StartNew(InitModules);
        }

        private static OcrLib.Ocr ChineseLiteEngin;
        private static OcrLib.Ocr PaddleMobileOcrEngin;
        private static OcrLib.Ocr PaddleServerOcrEngin;

        static JavaScriptSerializer JavaScriptSerializer = new JavaScriptSerializer();

        private static int padding_PaddleOcrEngin = 50;
        private static int maxSideLen_PaddleOcrEngin = 1024;
        private static float boxScoreThresh_PaddleOcrEngin = 0.618f;
        private static float boxThresh_PaddleOcrEngin = 0.3f;
        private static float unClipRatio_PaddleOcrEngin = 2;
        private static bool doAngle_PaddleOcrEngin = true;
        private static bool mostAngle_PaddleOcrEngin = true;

        private int padding_ChineseLiteEngin = 50;
        private int maxSideLen_ChineseLiteEngin = 1024;
        private float boxScoreThresh_ChineseLiteEngin = 0.618f;
        private float boxThresh_ChineseLiteEngin = 0.3f;
        private float unClipRatio_ChineseLiteEngin = 2;
        private bool doAngle_ChineseLiteEngin = true;
        private bool mostAngle_ChineseLiteEngin = true;

        private static bool isFeiJiangMobileInit;
        private static bool isFeiJiangServerInit;
        private static bool isChineseLiteInit;
        private static bool isWindowsOcrInit;

        public static int NMaxThread { get; set; } = 5;

        private static void InitModules()
        {
            //模型1初始化 Chinese-lite
            isChineseLiteInit = InitModel(LocalOcrType.中文识别Lite, out ChineseLiteEngin);

            //模型2初始化 Paddle Ocr
            isFeiJiangMobileInit = InitModel(LocalOcrType.飞浆Mobile, out PaddleMobileOcrEngin);

            //模型3初始化 Paddle Ocr
            isFeiJiangServerInit = InitModel(LocalOcrType.飞浆Server, out PaddleServerOcrEngin);

            //Environment.OSVersion.Version >= new Version("10.0.18362.0") &&
            isWindowsOcrInit = LocalOcrHelper.InitLanguage();
        }

        private static bool InitModel(LocalOcrType ocrType, out OcrLib.Ocr ocrEngin)
        {
            var result = false;
            ocrEngin = null;
            var modelName = ocrType.ToString();
            var appPath = AppDomain.CurrentDomain.BaseDirectory;
            var modelsDir = appPath + "models" + "\\" + modelName;
            var clsPath = modelsDir + "\\" + "cls.onnx";
            var detPath = modelsDir + "\\" + "det.onnx";
            var recPath = modelsDir + "\\" + "rec.onnx";
            var keysPath = modelsDir + "\\" + "keys.txt";

            if (File.Exists(detPath) && File.Exists(clsPath) && File.Exists(recPath) && File.Exists(keysPath))
            {
                try
                {
                    ocrEngin = new OcrLib.Ocr(modelName);
                    ocrEngin.InitModels(detPath, clsPath, recPath, keysPath, NMaxThread);
                    result = true;
                    Console.WriteLine(modelName + " 初始化成功！");
                }
                catch (Exception e)
                {
                    Console.WriteLine(modelName + " 初始化失败：" + e.Message);
                }
            }
            else
            {
                Console.WriteLine(modelName + " 初始化失败");
            }

            return result;
        }

        static string SubString(string strSource, string strSpilt, string strEnd = "")
        {
            return strSource.SubStringHorspool(strSpilt, strEnd).Trim();
        }

        public override void handleGETRequest(HttpProcessor p)
        {
            try
            {
                p.writeSuccess();
                var resultStr = "no";
                if (p.http_url.StartsWith("/Code.do"))
                {
                    try
                    {
                        var type = SubString(p.http_url, "type=", "&");
                        if (Equals(type, "state"))
                        {
                            resultStr = $"本地识别引擎状态：\n1、飞浆Mobile:{(isFeiJiangMobileInit ? "✔" : "✘")}\n2、飞浆Server:{(isFeiJiangServerInit ? "✔" : "✘")}\n3、中文识别Lite:{(isChineseLiteInit ? "✔" : "✘")}\n4、Windows OCR:{(isWindowsOcrInit ? "✔" : "✘")}";
                        }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message + Environment.NewLine + oe.StackTrace + Environment.NewLine + oe.InnerException?.Message + Environment.NewLine + oe.InnerException?.StackTrace);
                    }
                }
                p.outputStream.Write(resultStr);
            }
            catch
            {
            }
        }

        public override void handlePOSTRequest(HttpProcessor p, StreamReader inputData)
        {
            try
            {
                p.writeSuccess();
                var resultStr = "no";
                if (p.http_url.StartsWith("/Code.do"))
                {
                    try
                    {
                        var img = SubString(inputData.ReadToEnd(), "img=", "");
                        var type = SubString(p.http_url, "type=", "&");
                        var strOcrType = SubString(p.http_url, "ocr=", "&");
                        var id = SubString(p.http_url, "id=", "");
                        if (!string.IsNullOrEmpty(type) && !string.IsNullOrEmpty(img))
                        {
                            if (!int.TryParse(strOcrType, out int ocrType))
                            {
                                ocrType = 0;
                            }
                            resultStr = processOcr(ocrType, type, img, id);
                        }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message + Environment.NewLine + oe.StackTrace + Environment.NewLine + oe.InnerException?.Message + Environment.NewLine + oe.InnerException?.StackTrace);
                    }
                }
                p.outputStream.Write(resultStr);
            }
            catch
            {
            }
        }

        private OcrLib.OcrResult GetOcrResult(byte[] byt, LocalOcrType ocrType)
        {
            var result = new OcrLib.OcrResult();
            switch (ocrType)
            {
                case LocalOcrType.飞浆Mobile:
                    if (PaddleMobileOcrEngin == null)
                    {
                        Console.WriteLine("Paddle Mobile Ocr 未初始化，无法执行!");
                    }
                    else
                    {
                        result = PaddleMobileOcrEngin.Detect(byt, padding_PaddleOcrEngin, maxSideLen_PaddleOcrEngin, boxScoreThresh_PaddleOcrEngin
                            , boxThresh_PaddleOcrEngin, unClipRatio_PaddleOcrEngin, doAngle_PaddleOcrEngin, mostAngle_PaddleOcrEngin, true);
                    }
                    break;
                case LocalOcrType.飞浆Server:
                    if (PaddleServerOcrEngin == null)
                    {
                        Console.WriteLine("Paddle Server Ocr 未初始化，无法执行!");
                    }
                    else
                    {
                        result = PaddleServerOcrEngin.Detect(byt, padding_PaddleOcrEngin, maxSideLen_PaddleOcrEngin, boxScoreThresh_PaddleOcrEngin
                            , boxThresh_PaddleOcrEngin, unClipRatio_PaddleOcrEngin, doAngle_PaddleOcrEngin, mostAngle_PaddleOcrEngin, true);
                    }
                    break;
                case LocalOcrType.中文识别Lite:
                    if (ChineseLiteEngin == null)
                    {
                        Console.WriteLine("ChineseLite 未初始化，无法执行!");
                    }
                    else
                    {
                        result = ChineseLiteEngin.Detect(byt, padding_ChineseLiteEngin, maxSideLen_ChineseLiteEngin, boxScoreThresh_ChineseLiteEngin
                            , boxThresh_ChineseLiteEngin, unClipRatio_ChineseLiteEngin, doAngle_ChineseLiteEngin, mostAngle_ChineseLiteEngin, false);
                    }
                    break;
                case LocalOcrType.WindowsOcr:
                    if (isWindowsOcrInit == false)
                    {
                        Console.WriteLine("本机不支持，无法执行!");
                    }
                    else
                    {
                        result = LocalOcrHelper.Detect(byt);
                    }
                    break;
            }
            return result;
        }

        private string processOcr(int ocrType, string strEngine, string img, string id)
        {
            var byt = Convert.FromBase64String(HttpUtility.UrlDecode(img));
            Enum.TryParse(strEngine, out LocalOcrType engineType);

            var ocrContent = new OcrContent
            {
                ocrType = ocrType,
                id = id,
                processId = engineType.GetHashCode(),
                processName = engineType.ToString(),
                Identity = id,
                result = new ResultEntity()
            };

            if (ocrType == 0)
            {
                var ocrResult = GetOcrResult(byt, engineType);
                if (ocrResult != null && !string.IsNullOrEmpty(ocrResult.StrRes))
                {
                    var lstCell = new List<TextCellInfo>();
                    ocrResult.TextBlocks?.ForEach(text =>
                    {
                        var cell = new TextCellInfo
                        {
                            words = text.Text
                        };
                        if (text.BoundingRect.IsEmpty)
                        {
                            cell.location = new LocationInfo()
                            {
                                left = Math.Min(text.BoxPoints[0].X, text.BoxPoints[3].X) - 50,
                                top = Math.Min(text.BoxPoints[0].Y, text.BoxPoints[1].Y) - 50,
                                width = Math.Min(text.BoxPoints[1].X - text.BoxPoints[0].X, text.BoxPoints[2].X - text.BoxPoints[3].X),
                                height = Math.Min(text.BoxPoints[3].Y - text.BoxPoints[0].Y, text.BoxPoints[2].Y - text.BoxPoints[1].Y),
                            };
                        }
                        else
                        {
                            cell.location = new LocationInfo()
                            {
                                left = text.BoundingRect.Left,
                                height = text.BoundingRect.Height,
                                top = text.BoundingRect.Top,
                                width = text.BoundingRect.Width
                            };
                        }
                        lstCell.Add(cell);
                    });

                    if (lstCell.Exists(p => p.location != null))
                    {
                        ocrContent.result.verticalText = JavaScriptSerializer.Serialize(lstCell);
                    }
                    var strStart = lstCell.Count > 1 ? "\t" : "";
                    ocrContent.result.spiltText = (strStart + string.Join("\n" + strStart, lstCell.Select(p => p.words?.TrimEnd()))).TrimEnd();
                    ocrContent.result.autoText = ocrContent.result.spiltText;
                }
            }
            else if (ocrType == 2)
            {
                var ocrResult = GetOcrResult(byt, engineType);
                var tableResult = OcrUtils.DetectTableFromOcrResult(ocrResult);
                if (tableResult != null && tableResult.rows?.Count > 0)
                {
                    ocrContent.result.autoText = ocrContent.result.spiltText = JavaScriptSerializer.Serialize(tableResult);
                    ocrContent.result.resultType = ocrType;
                }
            }

            var resultStr = JavaScriptSerializer.Serialize(ocrContent);
            return resultStr;
        }

    }
}
