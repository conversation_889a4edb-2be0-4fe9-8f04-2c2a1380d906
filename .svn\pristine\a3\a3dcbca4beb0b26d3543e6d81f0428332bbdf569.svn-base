using System;
using System.Diagnostics;
using System.Runtime.InteropServices;

namespace OCRTools
{
    internal class HookA<PERSON>
    {
        public delegate int HookProc(int nCode, int wParam, IntPtr lParam);

        private static int _hHook;

        private static HookProc _keyBoardHookProcedure;

        [DllImport("user32.dll")]
        public static extern int SetWindowsHookEx(int idHook, HookProc lpfn, IntPtr hInstance, int threadId);

        [DllImport("user32.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto)]
        public static extern bool UnhookWindowsHookEx(int idHook);

        [DllImport("user32.dll")]
        public static extern int CallNextHookEx(int idHook, int nCode, int wParam, IntPtr lParam);

        [DllImport("kernel32.dll")]
        public static extern IntPtr GetModuleHandle(string name);

        public static void Hook_Start()
        {
            if (_hHook == 0)
            {
                _keyBoardHookProcedure = KeyBoardHookProc;
                _hHook = SetWindowsHookEx(13, _keyBoardHookProcedure,
                    GetModuleHandle(Process.GetCurrentProcess().MainModule?.ModuleName), 0);
                if (_hHook == 0) Hook_Clear();
            }
        }

        public static void Hook_Clear()
        {
            var flag = true;
            if (_hHook != 0)
            {
                flag = UnhookWindowsHookEx(_hHook);
                _hHook = 0;
            }

            if (!flag) throw new Exception("UnhookWindowsHookEx failed.");
        }

        private static int KeyBoardHookProc(int nCode, int wParam, IntPtr lParam)
        {
            if (nCode >= 0)
            {
                var keyBoardHookStruct =
                    (KeyBoardHookStruct)Marshal.PtrToStructure(lParam, typeof(KeyBoardHookStruct));
                if (keyBoardHookStruct.vkCode == 91) return 1;
                if (keyBoardHookStruct.vkCode == 92) return 1;
            }

            return CallNextHookEx(_hHook, nCode, wParam, lParam);
        }

        [StructLayout(LayoutKind.Sequential)]
        public class KeyBoardHookStruct
        {
            public int dwExtraInfo;

            public int flags;

            public int scanCode;

            public int time;
            public int vkCode;
        }
    }
}