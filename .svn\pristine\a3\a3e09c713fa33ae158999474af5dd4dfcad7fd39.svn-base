﻿using OCRTools.Properties;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;

namespace OCRTools
{
    internal class CommonUser
    {
        private static List<UserType> LstUserType { get; set; } = new List<UserType>();

        public static List<UserType> GetUserTypes()
        {
            if (LstUserType == null || LstUserType.Count <= 0)
            {
                LstUserType = OcrHelper.GetCanRegUserTypes()?.OrderBy(p => p.Type).ToList();
            }

            return LstUserType;
        }

        public static Bitmap GetUserLevelImage(int userLevel)
        {
            Bitmap image;
            try
            {
                image = CommonMethod.GetBitmapFromResource("vip_" + userLevel);
            }
            catch (Exception e)
            {
                image = Resources.vip_0;
            }

            return image;
        }

        public static UserTypeInfo GetNextType()
        {
            GetUserTypes();
            var nNextType = Program.NowUser == null ? LstUserType.FirstOrDefault() : LstUserType.FirstOrDefault(p => p.Type > Program.NowUser.UserType);
            return new UserTypeInfo
            {
                Name = nNextType == null ? "敬请期待" : "升级到" + nNextType.Name,
                Code = nNextType?.Type ?? 0
            };
        }
    }
}
