using System;
using System.Collections.Generic;

namespace ExcelLibrary.CompoundDocumentFormat
{
	public class SectorAllocation
	{
		private CompoundDocument Document;

		private int SecIDCapacity;

		public SectorAllocation(CompoundDocument document)
		{
			Document = document;
			SecIDCapacity = document.SectorSize / 4;
		}

		public int AllocateSector()
		{
			int num = Document.AllocateNewSector();
			LinkSectorID(num, -2);
			return num;
		}

		public void LinkSectorID(int sectorID, int newSectorID)
		{
			if (sectorID < 0)
			{
				throw new ArgumentOutOfRangeException("sectorID");
			}
			int sATSectorIndex = sectorID / SecIDCapacity;
			int num = sectorID % SecIDCapacity;
			int sATSectorID = Document.MasterSectorAllocation.GetSATSectorID(sATSectorIndex);
			Document.WriteInSector(sATSectorID, num * 4, newSectorID);
		}

		public int GetNextSectorID(int sectorID)
		{
			if (sectorID < 0)
			{
				throw new ArgumentOutOfRangeException("sectorID");
			}
			int sATSectorIndex = sectorID / SecIDCapacity;
			int num = sectorID % SecIDCapacity;
			int sATSectorID = Document.MasterSectorAllocation.GetSATSectorID(sATSectorIndex);
			return Document.ReadInt32InSector(sATSectorID, num * 4);
		}

		public List<int> GetSIDChain(int StartSID)
		{
			List<int> list = new List<int>();
			for (int num = StartSID; num != -2; num = GetNextSectorID(num))
			{
				list.Add(num);
			}
			return list;
		}
	}
}
