﻿using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Language;
using OCRTools.Properties;
using OCRTools.UserControlEx;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Window;

namespace OCRTools
{
    public partial class FormSetting : MetroForm
    {
        public readonly Regex intRegex = new Regex("^(-?[0-9]*[.]*[0-9]{0,3})$");

        private UserTypeInfo _nextUserType;

        public FormSetting()
        {
            SetStyle(ControlStyles.ResizeRedraw, false);
            InitializeComponent();
            lnkWebSite.Text = CommonString.StrServerHostUrl;
            CheckForIllegalCrossThreadCalls = false;

            CommonMethod.SetStyle(linkLabel2, ControlStyles.Selectable, false);
            CommonMethod.SetStyle(linkLabel1, ControlStyles.Selectable, false);
            CommonMethod.SetStyle(lnkFeedback, ControlStyles.Selectable, false);
            CommonMethod.SetStyle(lnkSuggestions, ControlStyles.Selectable, false);
            CommonMethod.SetStyle(lnkWebSite, ControlStyles.Selectable, false);

            CommonMethod.SetStyle(btnUpgrade, ControlStyles.Selectable, false);

            tipMsg.AutoPopDelay = 10000;
            tipMsg.InitialDelay = 100;
            tipMsg.ReshowDelay = 200;
            tipMsg.ShowAlways = true;
            tipMsg.ToolTipIcon = ToolTipIcon.Info;

            ShowInTaskbar = true;
            MaximizeBox = false;
            MinimizeBox = false;
            ShadowType = CommonString.CommonShadowType;

            InitConfig();

            this.AddContactUserBtn("FormSetting");
            btnLimitInfo = AddCustomButton("", Resources.显示隐藏, 15F, (send, e) =>
            {
                InitLeftInfo();
            });
            Shown += FormSetting_Shown;
        }

        private void FormSetting_Shown(object sender, EventArgs e)
        {
            tbConfig_SelectedIndexChanged(sender, e);
        }

        private void InitLeftInfo()
        {
            CommonUser.UpdateLimitInfo();
            btnLimitInfo.Text = string.Format("{1}:{0}", CommonUser.GetTodayLimitInfo(), "今日余量".CurrentText());
            this.UpdateWindowButtonPosition();
        }

        private Button btnLimitInfo;

        public override void OnThemeChange()
        {
            lblName.Text = CommonString.FullName.CurrentText();
            lblVersion.Text = string.Format("{2} {0}（{1}）", Application.ProductVersion,
                CommonString.DtNowDate.ToDateStr("yyyy-MM-dd HH:mm:ss").Replace(" 00:00:00", ""), "版本".CurrentText());
            var asmcpr = (AssemblyCopyrightAttribute)Attribute.GetCustomAttribute(Assembly.GetExecutingAssembly(),
                typeof(AssemblyCopyrightAttribute));
            lblCopyright.Text = string.Format("{0}", asmcpr?.Copyright ?? Application.CompanyName).Replace("版权", "版权".CurrentText());
            InitLeftInfo();
            RebindCheckBoxTip(this.Controls);
        }

        public static void RebindCheckBoxTip(IEnumerable controls)
        {
            foreach (Control control in controls)
            {
                if (control is CheckBoxWithTip chk)
                {
                    chk.TipText = chk.TipText;
                }
                if (control.Controls.Count > 0)
                    RebindCheckBoxTip(control.Controls);
            }
        }

        public string OpenTab { get; set; }

        public string SharkGroup { get; set; }

        private void FormSetting_FormClosing(object sender, FormClosingEventArgs e)
        {
            SaveConfig();
        }

        private void FormSetting_Load(object sender, EventArgs e)
        {
            #region 其他配置

            ResetToolImg();
            picIcon.Image = Resources.ico;
            #endregion

            BingViewText();

            if (!string.IsNullOrEmpty(OpenTab))
            {
                var selectedTab = tbConfig.TabPages.OfType<TabPage>().FirstOrDefault(p => Equals(OpenTab, this.OriginText(p)));
                if (selectedTab == null)
                {
                    foreach (TabPage page in tbConfig.TabPages)
                    {
                        foreach (var tabControl in page.Controls.OfType<TabControl>())
                        {
                            var tmp = tabControl.TabPages.OfType<TabPage>().FirstOrDefault(q => Equals(OpenTab, this.OriginText(q)));
                            if (tmp != null)
                            {
                                tabControl.SelectedTab = tmp;
                                selectedTab = page;
                                break;
                            }
                        }
                        if (selectedTab != null)
                        {
                            break;
                        }
                    }
                }
                if (selectedTab != null) tbConfig.SelectedTab = selectedTab;
            }

            if (!string.IsNullOrEmpty(SharkGroup))
            {
                var selectedGroupBox = SharkTabPageControl(tbConfig.SelectedTab);
                if (selectedGroupBox != null)
                {
                    Task.Factory.StartNew(() =>
                    {
                        SharkGroupBox(selectedGroupBox);
                    });
                }
            }

            _nextUserType = CommonUser.GetNextType();
            btnUpgrade.Text = _nextUserType.Name;

            var image = btnUpgrade.SetResourceImage("vip_" + _nextUserType.Code) ?? btnUpgrade.SetResourceImage("qqKeFu");
            image = ImageProcessHelper.ScaleImage(image, this.GetDpiScale());

            btnUpgrade.ImageSize = image.Size;
            btnUpgrade.Image = image;

            chkToolShadow.CheckedChanged += cmbToolBarWidth_SelectedIndexChanged;
            chkToolBarCircle.CheckedChanged += cmbToolBarWidth_SelectedIndexChanged;
            numToolShadowWidth.ValueChanged += cmbToolBarWidth_SelectedIndexChanged;
            chkWeatherImage.CheckedChanged += chkWeatherImage_CheckedChanged;

            cmbToolBarSize.SelectedIndexChanged += cmbToolBarWidth_SelectedIndexChanged;

            rdoSysProxy.CheckedChanged += RdoNoProxy_CheckedChanged;

            ShowToolClick();
            //InitLocalOcr();
            InitLocalOcrControl();
        }

        private GroupBox SharkTabPageControl(TabPage tab)
        {
            var selectedGroupBox = tab.Controls.OfType<GroupBox>().FirstOrDefault(p => Equals(SharkGroup, this.OriginText(p)));
            if (selectedGroupBox == null)
            {
                foreach (var groupBox in tab.Controls.OfType<GroupBox>())
                {
                    selectedGroupBox = groupBox.Controls.OfType<GroupBox>().FirstOrDefault(p => Equals(SharkGroup, this.OriginText(p)));
                    if (selectedGroupBox != null)
                    {
                        break;
                    }
                }
            }
            if (selectedGroupBox == null)
            {
                foreach (var tabControl in tab.Controls.OfType<TabControl>())
                {
                    foreach (TabPage page in tabControl.TabPages)
                    {
                        selectedGroupBox = SharkTabPageControl(page);
                        if (selectedGroupBox != null)
                        {
                            tabControl.SelectedTab = page;
                            break;
                        }
                    }
                    if (selectedGroupBox != null)
                    {
                        break;
                    }
                }
            }
            return selectedGroupBox;
        }

        private void SharkGroupBox(GroupBox box)
        {
            var baseColor = box.ForeColor;
            for (int i = 0; i < 10; i++)
            {
                var color = Color.FromArgb(0, new Random(Guid.NewGuid().GetHashCode()).Next(0, 256),
                    new Random(Guid.NewGuid().GetHashCode()).Next(0, 256),
                    new Random(Guid.NewGuid().GetHashCode()).Next(0, 256));
                box.ForeColor = color;
                Thread.Sleep(300);
            }
            box.ForeColor = baseColor;
        }

        private void InitConfig()
        {
            #region 初始化下拉框

            btnContentFontColor.ImageColor = StaticValue.DefaultForeColor;
            btnContentBackColor.ImageColor = StaticValue.DefaultBackColor;
            btnTieTuFontColor.ImageColor = StaticValue.DefaultForeColor;
            btnTieTuBackColor.ImageColor = StaticValue.DefaultBackColor;
            btnTieTuShadowColor.ImageColor = StaticValue.ShadowActiveColor;
            txtCaptureLocation.Text = CommonString.DefaultImagePath;

            InitEnums();
            InitShortKeys();

            #endregion

            foreach (TabPage item in tbConfig.TabPages)
            {
                var tabName = this.OriginText(item);
                ProcessItemByControl(item, tabName);
            }
        }

        private void InitEnums()
        {
            cmbSearch.Menu = new ContextMenuStrip();
            foreach (var engine in FrmMain.LstSearchEngine)
            {
                var text = engine.Name;
                ToolStripMenuItem tsmi = new ToolStripMenuItem(text);
                tsmi.Click += (sender, e) =>
                {
                    MenuButton_SelectedIndexChanged(cmbSearch, text);
                };
                cmbSearch.Menu.Items.Add(tsmi);
            }

            for (var i = 100; i >= 40; i -= 20) cmbRulerOpacity.Items.Add(i.ToString());

            BindMeunButton(cmbWeatherIcon, typeof(WeatherIconType), null, obj =>
            {
                MenuButton_SelectedIndexChanged(cmbWeatherIcon, obj.ToString());
                chkWeatherImage_CheckedChanged(null, null);
            });

            BindMeunButton(cmbStyles, typeof(MetroColorStyle), null, obj =>
            {
                var themeType = obj.ToString();
                MenuButton_SelectedIndexChanged(cmbStyles, themeType);
                if (!Equals(CommonSetting.主题样式, themeType))
                {
                    CommonSetting.主题样式 = themeType;
                    FrmMain.ChangeThemeDelegate.Invoke();
                }
            });

            BindMeunButton(cmbLoadingType, typeof(LoadingType), null, obj =>
            {
                cmbLoadingImage_SelectedIndexChanged(obj?.ToString());
            });
            BindMeunButton(cmbDark, typeof(ThemeStyle), null, obj =>
            {
                MenuButton_SelectedIndexChanged(cmbDark, obj.ToString());
                cmbDark_SelectedIndexChanged((ThemeStyle)obj);
            });
            BindMeunButton(cmbImageViewBackStyle, typeof(ImageBoxGridDisplayMode), null, obj =>
            {
                cmbImageViewBackStyle_SelectedIndexChanged((ImageBoxGridDisplayMode)obj);
            });

            BindMeunButton(cmbDoubleClick, typeof(ToolDoubleClickEnum), null);
            BindMeunButton(cmsNotifyDoubleClick, typeof(ToolDoubleClickEnum), ToolDoubleClickEnum.不做任何操作);

            BindMeunButton(cmbRulerUnit, typeof(MeasuringUnit), null);
            BindMeunButton(cmbCopyMode, typeof(CopyResultEnum), null);
            BindMeunButton(cmbOcrType, typeof(OcrModel), null);

            BindMeunButton(cbScrollTopMethodBeforeCapture, typeof(ScrollingCaptureScrollTopMethod), null);
            BindMeunButton(cbScrollMethod, typeof(ScrollingCaptureScrollMethod), null);
            BindMeunButton(cmbFenDuan, typeof(SpiltMode), null);
            BindMeunButton(cmbVoice, typeof(Speaker), null);
            BindMeunButton(cmbMouseMove, typeof(MouseMoveActionEnum), null, obj =>
            {
                CommonSetting.图片动效 = obj.ToString();
                MenuButton_SelectedIndexChanged(cmbMouseMove, CommonSetting.图片动效);
            });

            btnLanguage.Menu = new ContextMenuStrip();
            foreach (SupportedLanguage language in Enum.GetValues(typeof(SupportedLanguage)))
            {
                ToolStripMenuItem tsmi = new ToolStripMenuItem(language.GetValue<DescriptionAttribute>("Description"))
                {
                    Image = LanguageHelper.GetLanguageIcon(language),
                    ImageScaling = ToolStripItemImageScaling.None
                };
                tsmi.Click += (sender, e) =>
                {
                    SetLanguage(language);
                };
                btnLanguage.Menu.Items.Add(tsmi);
            }
            BindMeunButton(cbCatpureScroll, typeof(ScrollingWhenCapture), null);
            BindMeunButton(cmbImageProcessMode, typeof(ImageProcessType), null);
        }

        private void BindMeunButton(MenuButton button, Type type, object expValue, Action<object> action = null)
        {
            button.Menu = new ContextMenuStrip();
            foreach (var item in Enum.GetValues(type))
            {
                if (expValue != null && Equals(item, expValue))
                {
                    continue;
                }
                var text = item.ToString();
                ToolStripMenuItem tsmi = new ToolStripMenuItem(text.CurrentText())
                {
                    AccessibleDescription = text
                };
                if (action == null)
                {
                    tsmi.Click += (sender, e) =>
                    {
                        MenuButton_SelectedIndexChanged(button, text);
                    };
                }
                else
                {
                    tsmi.Click += (sender, e) =>
                    {
                        action.Invoke(item);
                    };

                }
                button.Menu.Items.Add(tsmi);
            }
        }

        private void MenuButton_SelectedIndexChanged(MenuButton button, string text)
        {
            button.AccessibleDescription = text;
            button.Value = text;
            button.Text = text.CurrentText();
        }

        #region 工具栏

        private void chkShowTool_CheckedChanged(object sender, EventArgs e)
        {
            if (!chkShowTool.Checked && !chkShowNotify.Checked)
            {
                ((CheckBox)sender).Checked = true;
                CommonMethod.ShowHelpMsg("工具栏和系统托盘不能同时隐藏！".CurrentText());
                return;
            }
            if (Equals(sender, chkShowTool))
            {
                FrmMain.FrmTool.Visible = chkShowTool.Checked;
                ShowToolClick();
            }
            else
            {
                (Owner as FrmMain)?.ShowNotifyMain(((CheckBox)sender).Checked);
            }
        }

        private void ShowToolClick()
        {
            foreach (Control control in grpToolSet.Controls)
            {
                if (!Equals(control, chkShowTool))
                {
                    control.Enabled = chkShowTool.Checked;
                }
            }
        }

        #endregion

        private void RdoNoProxy_CheckedChanged(object sender, System.EventArgs e)
        {
            CommonSetting.使用系统代理 = rdoSysProxy.Checked;
            CommonSetting.不使用代理 = !CommonSetting.使用系统代理;
            CommonSetting.InitProxy();
        }

        private void tbConfig_SelectedIndexChanged(object sender, EventArgs e)
        {
            var maxDownContol = tbConfig.SelectedTab.Controls[0];
            foreach (Control item in tbConfig.SelectedTab.Controls)
                if (item.Top + item.Height > maxDownContol?.Top + maxDownContol?.Height)
                    maxDownContol = item;
            if (maxDownContol != null)
                Height = PointToClient(maxDownContol.PointToScreen(Point.Empty)).Y
                         + Math.Max(maxDownContol.Height, maxDownContol.PreferredSize.Height) + 13;
            //if (!Equals(tbConfig.SelectedTab, tbShortKey))
            //{
            //    if (!(maxDownContol is TabControl))
            //    {
            //        maxHeight += 10;
            //    }
            //}
        }

        private void cmbToolBarWidth_SelectedIndexChanged(object sender, EventArgs e)
        {
            ResetToolImg();
        }

        private void ResetToolImg()
        {
            CommonSetting.工具栏图片 = txtToolBarPicLocation.Text;
            CommonSetting.工具栏图标尺寸 = cmbToolBarSize.SelectedItem?.ToString();
            CommonSetting.工具栏阴影宽度 = numToolShadowWidth.Value;
            CommonSetting.阴影效果 = chkToolShadow.Checked;
            CommonSetting.圆形图标 = chkToolBarCircle.Checked;
            CommonSetting.SetValue("工具栏图片", CommonSetting.工具栏图片);
            var size = CommonSetting.GetStrSize(cmbToolBarSize.SelectedItem?.ToString());
            var image = CommonSetting.Get图标效果(size, CommonSetting.工具栏图片, CommonSetting.圆形图标,
                CommonSetting.阴影效果, CommonSetting.工具栏阴影宽度);
            picToolBar.Image = image;
            FrmMain.FrmTool.RefreshImage(image);
            //picToolBar.Top = (120 - image.Height) / 2;
            //picToolBar.Size = image.Size;
            //var left = picToolBar.Left + picToolBar.Width + 5;
            //chkToolBarCircle.Left = left;
            //btnToolBarPicture.Left = left;
            //btnQQ.Left = left;
            //btnDefaultToolBarPicture.Left = left;

            //LanguageHelper.SaveBaseCulture();
        }

        private void btnToolBarPicture_Click(object sender, EventArgs e)
        {
            var open = new OpenFileDialog
            {
                Title = "请选择文件".CurrentText(),
                Filter = CommonString.GetImgFilter(),
                RestoreDirectory = true,
                Multiselect = false
            };
            if (open.ShowDialog(this) == DialogResult.OK)
            {
                string strConfig;
                try
                {
                    var fileName = CommonString.HeadImagePath + Guid.NewGuid().ToString().Replace("-", "") + ".png";
                    File.Copy(open.FileName, fileName, true);
                    strConfig = fileName;
                }
                catch
                {
                    strConfig = open.FileName;
                }
                if (sender is PictureBox)
                {
                    txtLoadingPicLocation.Text = strConfig;
                    CommonSetting.加载动画图片 = strConfig;
                    cmbLoadingImage_SelectedIndexChanged(cmbLoadingType.Value);
                }
                else
                {
                    txtToolBarPicLocation.Text = strConfig;
                    CommonSetting.工具栏图片 = strConfig;
                    cmbToolBarWidth_SelectedIndexChanged(sender, e);
                }
            }
        }

        private void btnDefaultToolBarPicture_Click(object sender, EventArgs e)
        {
            cmbToolBarSize.SelectedIndex = 0;
            txtToolBarPicLocation.Text = "";
            chkToolBarCircle.Checked = true;
            cmbToolBarWidth_SelectedIndexChanged(sender, e);
            chkWeatherImage.Checked = false;
        }

        private void chkWeatherImage_CheckedChanged(object sender, EventArgs e)
        {
            if (chkWeatherImage.Checked)
            {
                var weatherIcon = CommonSetting.ConvertToEnum(cmbWeatherIcon.Text, WeatherIconType.QQ);
                var strImage = CommonWeather.GetWeather(weatherIcon, true);
                if (!string.IsNullOrEmpty(strImage)) ChangeImageByUrl(strImage, false);
                cmbToolBarWidth_SelectedIndexChanged(sender, e);
            }
            else
            {
                btnDefaultToolBarPicture_Click(sender, e);
            }
        }

        private void btnQQ_Click(object sender, EventArgs e)
        {
            var frm = new MetroForm
            {
                Icon = Icon,
                Width = 300,
                Height = 150,
                ControlBox = false,
                Text = "输入QQ号"
            };
            frm.Controls.Add(new TextBox
            {
                Name = "txtQQ",
                Width = frm.Width - 10,
                TabIndex = 0,
                TabStop = true,
                Location = new Point(0, 55),
                Left = 5,
                Font = CommonString.GetSysBoldFont(21F)
            });
            var btn = new Button
            {
                Name = "btnQQ",
                Text = "确认",
                Width = frm.Width,
                Height = 40,
                Tag = frm.Controls[0],
                Location = new Point(0, 100)
            };
            btn.Click += Btn_Click;
            frm.Controls.Add(btn);
            frm.AcceptButton = btn;
            frm.ShadowType = ShadowType;
            var dialogRes = frm.ShowDialog(this);
            if (dialogRes == DialogResult.OK)
                if (frm.Tag != null && !string.IsNullOrEmpty(frm.Tag.ToString()) &&
                    intRegex.IsMatch(frm.Tag.ToString()))
                    ChangeImageByUrl(
                        string.Format("https://q2.qlogo.cn/g?b=qq&nk={0}&s=100", frm.Tag.ToString().Trim()));

            cmbToolBarWidth_SelectedIndexChanged(sender, e);
        }

        private void ChangeImageByUrl(string url, bool isNeedDownload = true)
        {
            if (isNeedDownload) url = CommonSetting.SetHeadImageByUrl(url);
            if (!string.IsNullOrEmpty(url))
                txtToolBarPicLocation.Text = url;
        }

        private void Btn_Click(object sender, EventArgs e)
        {
            var tag = (sender as Button)?.Tag as TextBox;
            if (tag != null && !intRegex.IsMatch(tag.Text))
            {
                MessageBox.Show(tag.FindForm(), "格式不正确，必须为全数字！", CommonString.StrReminder);
                tag.SelectAll();
                tag.Focus();
                return;
            }

            if (tag != null)
            {
                var frm = tag.FindForm();
                if (frm != null)
                {
                    frm.Tag = tag.Text;
                    frm.DialogResult = DialogResult.OK;
                    frm.Close();
                }
            }
        }

        private void btnUpgrade_Click(object sender, EventArgs e)
        {
            using (var frmBuy = new FrmGoBuy())
            {
                frmBuy.Icon = Icon;
                frmBuy.NextUserType = _nextUserType;
                frmBuy.ShowDialog(this);
            }
        }

        private void btnSupport_Click(object sender, EventArgs e)
        {
            FrmMain.ShowReort();
            //var report = new FrmReport
            //{
            //    Icon = Icon,
            //    Theme = Theme,
            //    Style = Style,
            //    StyleManager = StyleManager,
            //    Text = (sender as LinkLabel)?.Text
            //};
            //report.ShowDialog(this);
        }

        private void btnHelpTransLanguage_Click(object sender, EventArgs e)
        {
#if DEBUG
            LanguageHelper.ProcessTrans();
#endif
#if !DEBUG
            var report = new FrmReport
            {
                Icon = FrmMain.FrmTool.Icon,
                Text = lnkHelpTrans.Text
            };
            report.ShowDialog(this);
#endif
        }

        private void linkLabel1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            CommonMethod.OpenKeFuQ();
        }

        private void linkLabel2_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            CommonMethod.OpenQun();
        }

        private void btnCheckUpdate_Click(object sender, EventArgs e)
        {
            CommonSetting.体验Beta测试版 = chkExpireBeta.Checked;
            CommonUpdate.UpdateMain();
        }

        #region 初始化配置

        private void ProcessItemByControl(Control item, string section, bool isSave = false)
        {
            foreach (Control control in item.Controls)
                if (control is CheckBox checkBox)
                {
                    if (isSave)
                    {
                        if (control.Tag != null && control.Tag is ObjectTypeItem)
                        {
                            CommonSetting.SetValue(section, checkBox.Name, checkBox.Checked ? (checkBox.Tag as ObjectTypeItem).Code : 0);
                        }
                        else
                        {
                            CommonSetting.SetValue(section, this.OriginText(checkBox), checkBox.Checked);
                        }
                    }
                    else
                        checkBox.Checked = CommonSetting.GetValue(section, this.OriginText(checkBox), checkBox.Checked);
                }
                else
                {
                    if (control is GroupBox || control is Panel || control is TabControl)
                    {
                        ProcessItemByControl(control, section, isSave);
                    }
                    else
                    {
                        var tagName = control.Tag?.ToString();
                        if (string.IsNullOrEmpty(tagName)) continue;
                        if (control is TextBox)
                        {
                            var txtValue = control.Text;
                            if (control.Tag is HotKeyEntity obj)
                            {
                                tagName = obj.KeyName;
                                txtValue = isSave ? ((int)obj.Hotkey).ToString() : obj.ToString();
                            }
                            else
                                txtValue = isSave ? txtValue : CommonSetting.GetValue(section, tagName, txtValue);
                            if (isSave)
                            {
                                CommonSetting.SetValue(section, tagName, txtValue);
                            }
                            else
                            {
                                control.Text = "";
                                control.Text = txtValue;
                            }
                        }
                        else if (control is ComboBox comboBox)
                        {
                            if (isSave)
                            {
                                CommonSetting.SetValue(section, tagName, comboBox.SelectedItem?.ToString());
                            }
                            else
                            {
                                comboBox.SelectedItem =
                                    CommonSetting.GetValue(section, tagName, comboBox.SelectedItem?.ToString()) ?? "绿色";
                            }
                        }
                        else if (control is NumericUpDown numericUpDown)
                        {
                            if (isSave)
                                CommonSetting.SetValue(section, tagName, numericUpDown.Value);
                            else
                                numericUpDown.Value = CommonSetting.GetValue(section, tagName, numericUpDown.Value);
                        }
                        else if (control is RadioButton radio)
                        {
                            if (isSave)
                                CommonSetting.SetValue(section, tagName, radio.Checked);
                            else
                                radio.Checked = CommonSetting.GetValue(section, tagName, radio.Checked);
                        }
                        else if (control is MenuButton menuButton)
                        {
                            if (isSave)
                            {
                                CommonSetting.SetValue(section, tagName, menuButton.Value);
                            }
                            else
                            {
                                if (Equals(tagName, "语言"))
                                {
                                    if (Enum.TryParse(CommonSetting.语言, out SupportedLanguage language))
                                        SetLanguage(language);
                                }
                                else if (Equals(tagName, "图片预览背景"))
                                {
                                    if (Enum.TryParse(CommonSetting.图片预览背景, out ImageBoxGridDisplayMode back))
                                        cmbImageViewBackStyle_SelectedIndexChanged(back);
                                }
                                else
                                {
                                    MenuButton_SelectedIndexChanged(menuButton, CommonSetting.GetValue(section, tagName, control.Text));
                                    if (Equals(tagName, "加载动画"))
                                    {
                                        cmbLoadingImage_SelectedIndexChanged(menuButton.Value);
                                    }
                                }
                            }
                        }
                        else if (control is SkinButton imageButton && imageButton.IsColorButton)
                        {
                            if (isSave)
                                CommonSetting.SetValue(section, tagName, imageButton.ImageColor);
                            else
                                imageButton.ImageColor =
                                    CommonSetting.GetValue(section, tagName, imageButton.ImageColor);
                        }
                        else if (control is Button button)
                        {
                            var isFont = button.Name.ToLower().Contains("font");
                            if (isSave)
                            {
                                if (isFont)
                                    CommonSetting.SetValue(section, tagName, GetFont(button));
                                //value = TypeDescriptor.GetConverter(typeof(Font)).ConvertToInvariantString(GetFont(ctrl));
                            }
                            else
                            {
                                if (isFont)
                                {
                                    var ff = CommonSetting.GetValue(section, tagName, GetFont(button));
                                    //var font = string.IsNullOrEmpty(value) ? StaticValue.DefaultFont : TypeDescriptor.GetConverter(typeof(Font)).ConvertFromInvariantString(value) as Font;
                                    SetFont(button, ff);
                                }
                            }
                        }
                    }
                }
        }

        public void SaveConfig()
        {
            foreach (TabPage item in tbConfig.TabPages)
            {
                var tabName = this.OriginText(item);
                ProcessItemByControl(item, tabName, true);
            }
        }

        private void SetLanguage(SupportedLanguage language)
        {
            btnLanguage.Value = language.ToString();
            btnLanguage.Text = language.GetValue<DescriptionAttribute>("Description");
            btnLanguage.Image = LanguageHelper.GetLanguageIcon(language);
            if (!Equals(CommonSetting.语言, btnLanguage.Value))
            {
                CommonSetting.语言 = btnLanguage.Value;
                LanguageHelper.InitLanguage(CommonSetting.语言);
                FrmMain.ChangeThemeDelegate.Invoke();
            }
        }

        /// 设置按键不响应
        private void txtCaptureKey_KeyPress(object sender, KeyPressEventArgs e)
        {
            e.Handled = true;
        }

        private void txtBox_GotFocus(object sender, EventArgs e)
        {
            var textBox = sender as TextBox;
            var entity = textBox?.Tag as HotKeyEntity;
            BindTextBox(textBox, entity);
        }

        private void txtBox_LostFocus(object sender, EventArgs e)
        {
            var textBox = sender as TextBox;
            var entity = textBox?.Tag as HotKeyEntity;
            BindTextBox(textBox, entity, true);
        }

        private void txtBox_KeyUp(object sender, KeyEventArgs e)
        {
            e.SuppressKeyPress = true;

            // PrintScreen not trigger KeyDown event
            if (e.KeyCode == Keys.PrintScreen)
            {
                var textBox = sender as TextBox;
                if (textBox?.Tag is HotKeyEntity entity)
                {
                    entity.Hotkey = e.KeyData;
                    BindTextBox(textBox, entity);
                }
            }
        }

        private void txtBox_KeyDown(object sender, KeyEventArgs e)
        {
            e.SuppressKeyPress = true;

            var textBox = sender as TextBox;
            var entity = textBox?.Tag as HotKeyEntity;
            if (entity == null)
            {
                return;
            }

            if (e.KeyCode == Keys.Delete || e.KeyCode == Keys.Back || e.KeyCode == Keys.Escape)
            {
                entity.Hotkey = Keys.None;
                entity.Win = false;
            }
            else if (e.KeyCode == Keys.LWin || e.KeyCode == Keys.RWin)
            {
                entity.Win = !entity.Win;
            }
            else
            {
                entity.Hotkey = e.KeyData;
            }

            BindTextBox(textBox, entity);
        }

        private readonly Regex _textRegex = new Regex("[一-龥]+");

        private void BindTextBox(TextBox textBox, HotKeyEntity entity, bool isEnd = false)
        {
            string strTmp;

            var str = "";
            foreach (var obj in _textRegex.Matches(textBox.Name)) str = ((Match)obj).ToString();

            if (isEnd)
            {
                //验证快捷键是否有效
                if (entity.IsValidHotkey)
                {
                    var exitsHotKey =
                        FrmMain.LstHotKeys.FirstOrDefault(
                            p => !p.KeyName.Equals(str) && Equals(p.StrKey, entity.StrKey));
                    if (exitsHotKey == null)
                    {
                        FrmMain.LstHotKeys.Where(p => p.KeyName.Equals(str)).ToList()
                            .ForEach(p => p.Hotkey = entity.Hotkey);
                    }
                    else
                    {
                        CommonMethod.ShowHelpMsg(string.Format("快捷键{0}被占用！".CurrentText() + " " + exitsHotKey.KeyName.Trim(), entity.StrKey));
                        entity.Hotkey = Keys.None;
                    }
                }
                else
                {
                    entity.Hotkey = Keys.None;
                }
            }

            var pictureBox = (PictureBox)Controls.Find("pictureBox_" + str, true)[0];
            if (Equals(entity.Hotkey, Keys.None))
            {
                pictureBox.Image = Resources.Info_Error;
                strTmp = isEnd ? entity.StrKey : CommonString.StrDefaultDesc.CurrentText();
            }
            else
            {
                pictureBox.Image = isEnd ? Resources.Info_OK : Resources.Info_Info;
                strTmp = entity.StrKey;
            }

            pictureBox.Image = ImageProcessHelper.ScaleImage(pictureBox.Image, this.GetDpiScale());

            textBox.Text = strTmp;
            textBox.Tag = entity;

            if (isEnd)
                CommonSetting.SetValue("快捷键", str, ((int)entity.Hotkey).ToString());
            else
                textBox.SelectionStart = textBox.Text.Length;
        }

        private void txtBox_PreviewKeyDown(object sender, PreviewKeyDownEventArgs e)
        {
            // For handle Tab key etc.
            e.IsInputKey = true;
        }

        private void InitShortKeys()
        {
            var hotTypeList = FrmMain.LstHotKeys.GroupBy(p => p.Group).ToList();
            hotTypeList.ForEach(p =>
            {
                //p.Key
                var tbTmp = new TabPage
                {
                    BackColor = Color.White,
                    Padding = new Padding(0, 5, 0, 0),
                    Text = p.Key.ToString().CurrentText(),
                    AccessibleDescription = p.Key.ToString()
                };

                foreach (var item in p.ToList())
                {
                    var pnl = new FlowLayoutPanel
                    {
                        BackColor = Color.White,
                        Width = tbShortKey.Width - 5,
                        Height = 32,
                        Padding = new Padding(0, 5, 0, 0),
                        Dock = DockStyle.Top
                    };
                    var lbl = new Label
                    {
                        AccessibleDescription = item.KeyName,
                        Text = item.KeyName.CurrentText() + ":",
                        Font = this.Font,
                        AutoSize = false,
                        BackColor = Color.White,
                        ForeColor = Color.Black,
                        Padding = new Padding(10, 6, 0, 0),
                        Width = 120,
                        TextAlign = ContentAlignment.MiddleRight
                    };
                    pnl.Controls.Add(lbl);
                    lbl.MouseEnter += (sender, e) =>
                    {
                        tipMsg.SetToolTip(lbl, lbl.Text);
                    };

                    var textBox = new TextBox
                    {
                        Name = "txtBox_" + item.KeyName.Trim(),
                        Font = this.Font,
                        BackColor = Color.White,
                        ForeColor = Color.Black,
                        Size = new Size(165, 29),
                        Padding = new Padding(0, 0, 0, 0),
                        ShortcutsEnabled = false,
                        Tag = item,
                        Text = item.ToString(),
                    };
                    textBox.MouseEnter += (sender, e) =>
                    {
                        tipMsg.SetToolTip(textBox, item.Desc.CurrentText());
                    };

                    textBox.KeyDown += txtBox_KeyDown;
                    textBox.KeyUp += txtBox_KeyUp;
                    textBox.KeyPress += txtCaptureKey_KeyPress;
                    textBox.LostFocus += txtBox_LostFocus;
                    textBox.GotFocus += txtBox_GotFocus;
                    textBox.PreviewKeyDown += txtBox_PreviewKeyDown;

                    tipMsg.SetToolTip(textBox, item.Desc.CurrentText());

                    pnl.Controls.Add(textBox);

                    var picture = new PictureBox
                    {
                        Name = "pictureBox_" + item.KeyName.Trim(),
                        BackColor = Color.White,
                        Size = new Size(25, 25),
                        SizeMode = PictureBoxSizeMode.CenterImage,
                        Padding = new Padding(5, 5, 0, 0),
                        Image = string.IsNullOrEmpty(textBox.Text) || textBox.Text.Equals(CommonString.StrDefaultDesc.CurrentText())
                            ? Resources.Info_Error
                            : Resources.Info_OK
                    };
                    pnl.Controls.Add(picture);

                    tbTmp.Controls.Add(pnl);
                }

                tabHotKeys.TabPages.Add(tbTmp);
            });
        }

        #endregion

        #region 常规

        private void chkAutoStart_CheckedChanged(object sender, EventArgs e)
        {
            CommonMethod.AutoStart(chkAutoStart.Checked);
        }

        #endregion

        #region 界面

        /// <summary>
        ///     获取一个颜色的人眼感知亮度，并以 0~1 之间的小数表示。
        ///     灰度值小于0.5，推荐亮色，反之，推荐黑色
        /// </summary>
        private double GetGrayLevel(Color color)
        {
            return (0.299 * color.R + 0.587 * color.G + 0.114 * color.B) / 255;
        }

        private void btnContentDefault_Click(object sender, EventArgs e)
        {
            var btn = sender as Button;
            if (Equals(btn, btnContentDefault))
            {
                btnContentBackColor.ImageColor = StaticValue.DefaultBackColor;
                btnContentFontColor.ImageColor = StaticValue.DefaultForeColor;
                SetFont(btnContentFont, CommonString.DefaultFont);
                BingViewText();
            }
            else
            {
                if (chkUseContentColor.Checked)
                {
                    chkUseContentColor_CheckedChanged(sender, e);
                }
                else
                {
                    btnTieTuBackColor.ImageColor = StaticValue.DefaultBackColor;
                    btnTieTuFontColor.ImageColor = StaticValue.DefaultForeColor;
                    SetFont(btnTieTuFont, CommonString.DefaultFont);
                    BingViewText(false);
                }
            }
        }

        private void BingViewText(bool isContent = true)
        {
            if (isContent)
            {
                lblContentLable.Font = GetFont(btnContentFont);
                lblContentLable.ForeColor = btnContentFontColor.ImageColor;
                lblContentLable.BackColor = btnContentBackColor.ImageColor;
                chkUseContentColor_CheckedChanged(null, null);
            }
            else
            {
                lblTieTuLabel.Font = GetFont(btnTieTuFont);
                lblTieTuLabel.ForeColor = btnTieTuFontColor.ImageColor;
                lblTieTuLabel.BackColor = btnTieTuBackColor.ImageColor;
            }
        }

        private void btnColor_Click(object sender, EventArgs e)
        {
            if (sender is SkinButton btn)
            {
                var dialog = new ColorDialog
                {
                    Color = btn.ImageColor,
                    CustomColors = new[]
                    {
                        ColorTranslator.ToOle(Color.FromArgb(240, 255, 240)),
                        ColorTranslator.ToOle(Color.FromArgb(255, 255, 224)),
                        ColorTranslator.ToOle(StaticValue.ShadowActiveColor),
                        ColorTranslator.ToOle(StaticValue.ShadowNormalColor)
                    }
                };
                if (dialog.ShowDialog(this) == DialogResult.OK)
                {
                    btn.ImageColor = dialog.Color;
                    var isContent = btn.Name.StartsWith("btnContent");
                    BingViewText(isContent);
                    if (!btn.Name.Contains("BackColor") && !btn.Name.Contains("FontColor")) return;
                    double backColorGray;
                    double foreColorGray;
                    if (isContent)
                    {
                        backColorGray = GetGrayLevel(btnContentBackColor.ImageColor);
                        foreColorGray = GetGrayLevel(btnContentFontColor.ImageColor);
                    }
                    else
                    {
                        backColorGray = GetGrayLevel(btnTieTuBackColor.ImageColor);
                        foreColorGray = GetGrayLevel(btnTieTuFontColor.ImageColor);
                    }

                    if ((backColorGray >= 0.5 && foreColorGray >= 0.5) || (backColorGray < 0.5 && foreColorGray < 0.5))
                        MessageBox.Show(this, "文字颜色与背景色区分度不明显，容易造成眼部疲劳，建议调整！", CommonString.StrReminder, MessageBoxButtons.OK,
                            MessageBoxIcon.Information);
                }
            }
        }

        private void chkUseContentColor_CheckedChanged(object sender, EventArgs e)
        {
            if (chkUseContentColor.Checked)
            {
                SetFont(btnTieTuFont, GetFont(btnContentFont));
                btnTieTuFontColor.ImageColor = btnContentFontColor.ImageColor;
                btnTieTuBackColor.ImageColor = btnContentBackColor.ImageColor;
            }

            BingViewText(false);
        }

        public readonly Dictionary<string, Font> DicTmpFont = new Dictionary<string, Font>();

        private Font GetFont(Button button)
        {
            var key = button?.Tag?.ToString();
            if (string.IsNullOrEmpty(key)) return CommonString.GetSysNormalFont(19F);
            if (!DicTmpFont.ContainsKey(key)) DicTmpFont.Add(key, CommonString.GetSysNormalFont(19F));
            return DicTmpFont[key];
        }

        private void SetFont(Button button, Font font)
        {
            var key = button.Tag?.ToString();
            if (string.IsNullOrEmpty(key))
                return;
            DicTmpFont[key] = font;
        }

        private void btnFont_Click(object sender, EventArgs e)
        {
            var btn = sender as Button;
            var dialog = new FontDialog
            { Font = GetFont(btn), ShowColor = false, ShowEffects = true, AllowScriptChange = false };
            if (dialog.ShowDialog(this) == DialogResult.OK)
            {
                SetFont(btn, dialog.Font);
                BingViewText(btn != null && btn.Name.StartsWith("btnContent"));
            }
        }

        #endregion

        #region 截屏

        private void txtCaptureFileName_TextChanged(object sender, EventArgs e)
        {
            /*
             %n 年份；%y 月份
            %r 天数；%s 小时
            %f 分钟；%m 秒钟
            %t 时间戳；%g 随机
             */
            var fileName = txtCaptureFileName.Text.Trim()
                .Replace("%n", ServerTime.DateTime.Year.ToString())
                .Replace("%y", ServerTime.DateTime.Month.ToString().PadLeft(2, '0'))
                .Replace("%r", ServerTime.DateTime.Day.ToString().PadLeft(2, '0'))
                .Replace("%s", ServerTime.DateTime.Hour.ToString().PadLeft(2, '0'))
                .Replace("%f", ServerTime.DateTime.Minute.ToString().PadLeft(2, '0'))
                .Replace("%m", ServerTime.DateTime.Second.ToString().PadLeft(2, '0'))
                .Replace("%t", ServerTime.DateTime.ToTimeSpan())
                .Replace("%g", Guid.NewGuid().ToString().Replace("-", ""));
            lblCaptureFileName.Text = string.Format("{1}：{0}", fileName, "示例".CurrentText());
        }

        private void btnOpenCaptureLocation_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtCaptureLocation.Text)) txtCaptureLocation.Text = CommonString.DefaultImagePath;
            CommonMethod.OpenFolder(txtCaptureLocation.Text.Trim());
        }

        private void btnChangeCaptureLocation_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtCaptureLocation.Text)) txtCaptureLocation.Text = CommonString.DefaultImagePath;
            var dialog = new FolderBrowserDialog
            { SelectedPath = txtCaptureLocation.Text, ShowNewFolderButton = true, Description = "截图文件存放目录".CurrentText() };
            if (dialog.ShowDialog(this) == DialogResult.OK) txtCaptureLocation.Text = dialog.SelectedPath;
        }

        #endregion

        private void lnkWebSite_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            CommonMethod.OpenUrl(CommonString.StrServerHostUrl);
        }

        private bool IsLocalOcrChecked(int code)
        {
            var result = Equals(code, CommonSetting.本地识别1) || Equals(code, CommonSetting.本地识别2) ||
                         Equals(code, CommonSetting.本地识别3) || Equals(code, CommonSetting.本地识别4) ||
                         Equals(code, CommonSetting.本地识别5);
            return result;
        }

        private void InitLocalOcrControl()
        {
            tbLocalOcr.Enabled = Program.NowUser?.IsSupportLocalOcr == true;
            OcrHelper.InitOcrGroup();
            var index = 1;
            var top = 19;
            OcrHelper.LstLocalOcrType?.ForEach(p =>
            {
                var left = 13;

                var panel = new Panel { Left = left, Top = top, Height = 26, Font = tbConfig.Font, Width = grpLocalOcr.Width - 10, Margin = CommonString.PaddingZero };
                var checkBox = new CheckBoxWithTip
                {
                    Text = p.Name,
                    Tag = p,
                    Font = tbConfig.Font,
                    Checked = IsLocalOcrChecked(p.Code),
                    Left = 5,
                    AutoSize = true,
                    Top = 5,
                    Name = "本地识别" + index
                };
                //如果已勾选，检查是否安装识别包
                if (checkBox.Checked && p.NeedInstall && !CommonUpdate.CheckIfLocalOcrModuleInstall(ref p))
                {
                    checkBox.Checked = false;
                }
                checkBox.CheckedChanged += (s, e) =>
                {
                    if (p.NeedInstall && checkBox.Checked)
                    {
                        if (!CommonUpdate.CheckIfLocalOcrModuleInstall(ref p))
                        {
                            checkBox.Checked = false;
                            CommonMethod.ShowHelpMsg("请先安装识别库后再尝试启用！".CurrentText());
                        }
                    }
                };
                panel.Controls.Add(checkBox);
                checkBox.TipControl = tipMsg;
                checkBox.TipText = p.Desc;
                left = checkBox.Left + checkBox.Width;
                if (!string.IsNullOrEmpty(p.DescUrl))
                {
                    var lnkLblWebSite = new LinkLabel
                    {
                        Text = "官网",
                        AutoSize = true,
                        Left = left,
                        Tag = p,
                        Top = 6,
                        LinkBehavior = LinkBehavior.NeverUnderline,
                    };
                    lnkLblWebSite.LinkClicked += (s, e) =>
                    {
                        CommonMethod.OpenUrl(p.DescUrl);
                    };
                    panel.Controls.Add(lnkLblWebSite);
                    left = lnkLblWebSite.Left + lnkLblWebSite.Width + 5;
                }
                if (!string.IsNullOrEmpty(p.UpdateUrl))
                {
                    var lnkLblInstall = new LinkLabel
                    {
                        Text = "安装/更新",
                        AutoSize = true,
                        Left = left,
                        Tag = p,
                        Top = 6,
                        LinkBehavior = LinkBehavior.NeverUnderline,
                    };
                    lnkLblInstall.AccessibleDescription = lnkLblInstall.Text;
                    p.AppPath = CommonString.DefaultLocalRecModelsPath;// + p.Name;
                    p.Date = DateTime.MinValue;
                    lnkLblInstall.LinkClicked += (s, e) =>
                    {
                        CommonUpdate.InstallLocalOcrItem(p);
                    };
                    panel.Controls.Add(lnkLblInstall);
                }

                grpLocalOcr.Controls.Add(panel);

                index++;
                top += 26;
            });
        }

        private void lnkLocalOcr_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            CommonUpdate.InstallLocalOcrExe(true);
        }

        private void lnkTestLocalOcr_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            var strState = OcrHelper.LocalOcrState();
            if (string.IsNullOrEmpty(strState))
            {
                strState = "本地识别服务未启动或服务状态异常！";
            }

            MessageBox.Show(this, strState, "本地引擎信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void lnkOpenLocalOcr_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            if (!File.Exists(CommonString.DefaultLocalRecExePath))
            {
                if (MessageBox.Show(this, "检测到本地识别服务尚未安装，是否立即安装？", "温馨提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
                    == DialogResult.Yes)
                {
                    CommonUpdate.InstallLocalOcrExe(true);
                }
                return;
            }
            LocalOcrService.OpenOcrService((int)numLocalOcrPort.Value, (int)numLocalOcrThread.Value);
        }

        private void cmbLoadingImage_SelectedIndexChanged(string value)
        {
            LoadingType loadingType = LoadingType.蓝色箭头;
            try
            {
                loadingType = (LoadingType)Enum.Parse(typeof(LoadingType), value);
            }
            catch
            {
            }
            MenuButton_SelectedIndexChanged(cmbLoadingType, loadingType.ToString());
            picLoadingImage.Image = LoadingTypeHelper.GetImageByConfig(loadingType);
        }

        private void cmbImageViewBackStyle_SelectedIndexChanged(ImageBoxGridDisplayMode mode)
        {
            var text = mode.ToString();
            cmbImageViewBackStyle.AccessibleDescription = text;
            cmbImageViewBackStyle.Value = text;
            cmbImageViewBackStyle.Text = text.CurrentText();
            btnImageViewBackColor.Visible = Equals(mode, ImageBoxGridDisplayMode.自定义);
        }

        #region 滚动截屏设置

        private void nudStartDelay_ValueChanged(object sender, EventArgs e)
        {
            CommonSetting.StartDelay = (int)nudStartDelay.Value;
        }

        private void nudScrollDelay_ValueChanged(object sender, EventArgs e)
        {
            CommonSetting.ScrollDelay = (int)nudScrollDelay.Value;
        }

        #endregion

        private void checkBox9_CheckedChanged(object sender, EventArgs e)
        {
            if (chkNotShowUIWhenOcr.Checked)
            {
                chkCopyResult.Checked = true;
            }
        }

        private void cmbDark_SelectedIndexChanged(ThemeStyle style)
        {
            bool isDark = false;
            switch (style)
            {
                case ThemeStyle.日间模式:
                    break;
                case ThemeStyle.夜间模式:
                    isDark = true;
                    break;
                case ThemeStyle.跟随系统:
                    {
                        isDark = CommonThemeManager.IsSystemDarkModel();
                        CommonMethod.ShowHelpMsg("当前系统主题".CurrentText() + ":" + (isDark ? "夜间" : "日间" + "模式").CurrentText());
                    }
                    break;
                case ThemeStyle.跟随日落:
                    {
                        var result = CommonThemeManager.GetSunSetResult(out GeoEntity geo);
                        CommonMethod.ShowHelpMsg(string.Format("{0} 今天 日出:{1}，日落:{2}".CurrentText()
                            , string.IsNullOrEmpty(geo.district) ? geo.city : geo.district
                            , result.SunriseTime.ToDateStr("HH:mm")
                            , result.SunsetTime.ToDateStr("HH:mm"))
                            , 3000);
                        isDark = result.IsDarkModel();
                    }
                    break;
            }
            if (!Equals(CommonSetting.夜间模式, isDark))
            {
                CommonSetting.夜间模式 = isDark;
                FrmMain.ChangeThemeDelegate.Invoke();
            }
        }
    }
}