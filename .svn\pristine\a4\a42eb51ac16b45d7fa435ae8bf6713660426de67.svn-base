﻿using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Threading;

namespace OCRTools
{
    public class CommonThemeManager
    {
        private static Thread _updateMainThread;
        public static void InitTheme(int minute)
        {
            DisposeTask();
            var timerInfo = new TimerInfo
            {
                TimerType = "LoopMinutes",
                DateValue = minute,
                IsExecFirst = true
            };
            _updateMainThread = TimerTaskService.CreateTimerTaskService(timerInfo, UpdateMethod);
            _updateMainThread.Start();
        }

        public static void DisposeTask()
        {
            try
            {
                _updateMainThread?.Abort();
            }
            catch { }
            _updateMainThread = null;
        }

        private static SunTimeResult SunTime;

        private static List<string> lstSunrise = new List<string>() {
            "美好的一天开始了",
            "愿你的每一个清晨，都伴着阳光上路",
            "如果有梦想，那就努力去捍卫",
            "只要心是晴天，人生就没有雨天",
            "不负韶华，只争朝夕",
        };

        public static void UpdateMethod(bool isUserUpdate, DateTime dtDate, string paramStr)
        {
            try
            {
                if (Equals(CommonSetting.日夜间模式, ThemeStyle.日间模式.ToString()) || Equals(CommonSetting.日夜间模式, ThemeStyle.夜间模式.ToString())) return;
                var isChanged = false;
                if (Equals(CommonSetting.日夜间模式, ThemeStyle.跟随系统.ToString()))
                {
                    var isDark = IsSystemDarkModel();
                    if (!Equals(isDark, CommonSetting.夜间模式))
                    {
                        CommonSetting.夜间模式 = isDark;
                        isChanged = true;
                    }
                }
                else if (Equals(CommonSetting.日夜间模式, ThemeStyle.跟随日落.ToString()))
                {
                    if (SunTime == null || !Equals(SunTime.SunriseTime.Date, ServerTime.DateTime.Date))
                    {
                        SunTime = GetSunSetResult(out GeoEntity geo);
                    }
                    var isDark = SunTime.IsDarkModel();
                    if (!Equals(isDark, CommonSetting.夜间模式))
                    {
                        if (!isDark)
                        {
                            CommonMethod.ShowHelpMsg(lstSunrise.GetRndItem() + "，早安！", 3000, "日夜间模式");
                        }
                        CommonSetting.夜间模式 = isDark;
                        isChanged = true;
                    }
                }

                if (isChanged)
                {
                    FrmMain.ChangeThemeDelegate.Invoke();
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("更新天气失败", oe);
            }
        }

        const string RegistryKeyPath = @"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize";
        const string RegistryValueName = "AppsUseLightTheme";

        public static bool IsSystemDarkModel()
        {
            var result = false;
            try
            {
                // 这里也可能是LocalMachine(HKEY_LOCAL_MACHINE)
                // see "https://www.addictivetips.com/windows-tips/how-to-enable-the-dark-theme-in-windows-10/"
                var strValue = Registry.CurrentUser.OpenSubKey(RegistryKeyPath)?.GetValue(RegistryValueName)?.ToString();
                if (!string.IsNullOrEmpty(strValue))
                    result = BoxUtil.GetInt32FromObject(strValue) > 0 ? false : true;
            }
            catch (Exception)
            {

                throw;
            }
            return result;
        }

        public static SunTimeResult GetSunSetResult(out GeoEntity geo)
        {
            geo = IpHelper.GetGeoEntity();
            if (geo.lat <= 0)
            {
                geo.lat = 31;
            }
            if (geo.lng <= 0)
            {
                geo.lng = 103;
            }
            return SunTimes.GetSunTime(ServerTime.DateTime, geo.lng, geo.lat);
        }
    }

    public enum ThemeStyle
    {
        日间模式 = 0,
        夜间模式 = 1,
        跟随系统 = 2,
        跟随日落 = 3
    }
}
