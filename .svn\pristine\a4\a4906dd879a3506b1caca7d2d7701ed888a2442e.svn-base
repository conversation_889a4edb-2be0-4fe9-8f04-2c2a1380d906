﻿using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Web;
using OCRTools.Common;
using OCRTools.Common.Entity;

namespace OCRTools
{
    public class OcrHelper
    {
        internal static OcrContent GetResult(OcrProcessEntity processEntity)
        {
            OcrContent content = null;
            try
            {
                var url = string.Format(
                    "code.ashx?op=codeFile&type={0}&left={1}&top={2}&group={3}&pid={4}&ext={5}&from={6}&to={7}"
                    , processEntity.OcrType.GetHashCode()
                    , processEntity.IsFromLeftToRight ? "1" : "0"
                    , processEntity.IsFromTopToDown ? "1" : "0"
                    , processEntity.GroupType
                    , processEntity.ProcessId
                    , processEntity.FileExt
                    , processEntity.From.GetHashCode()
                    , processEntity.To.GetHashCode());
                try
                {
                    NameValueCollection values = null;
                    if (processEntity.IsImgUrl)
                        values = new NameValueCollection
                        {
                            {"url", processEntity.ImgUrl}
                        };
                    var result = UploadFileRequest.PostFile(url, processEntity.IsImgUrl ? null : processEntity.Byts,
                        processEntity.FileExt, values, CommonMethod.GetRequestHeader());
                    if (!string.IsNullOrEmpty(result) && result.Length > 2)
                        content = CommonString.JavaScriptSerializer.Deserialize<OcrContent>(result);
                }
                catch (Exception)
                {
                    //Log.WriteError("CheckDecodeWeb出错", oe);
                }
            }
            catch (Exception)
            {
            }

            return content;
        }

        internal static string UploadImage(byte[] bytes)
        {
            string result = null;
            try
            {
                result = UploadFileRequest.PostFile("code.ashx?op=imgUpload", bytes, CommonString.StrDefaultImgType, null, CommonMethod.GetRequestHeader());
                if (!string.IsNullOrEmpty(result) && !result.StartsWith("http"))
                    result = null;
            }
            catch (Exception)
            {
                //Log.WriteError("CheckDecodeWeb出错", oe);
            }

            return result;
        }

        internal static string GetVoiceUrlResult(string strContent, string speaker, string voiceSpeed)
        {
            var content = string.Format(
                "https://fanyi.sogou.com/reventondc/synthesis?text={0}&speed={1}&from=translateweb&speaker={2}"
                , HttpUtility.UrlEncode(strContent)
                , voiceSpeed
                , speaker
            );
            return content;
        }

        internal static string GetFileResultUrl(OcrContent content)
        {
            var url = string.Format(CommonString.AutoCodeUrl + "/code.ashx?op=htmlfile&param={0}"
                , HttpUtility.UrlEncode(CommonString.JavaScriptSerializer.Serialize(content)));
            return url;
        }

        internal static string GetMathFileResultUrl(OcrContent content)
        {
            var url = string.Format(CommonString.AutoCodeUrl + "/math/view.html?m={0}"
                , HttpUtility.UrlEncode(content?.result?.autoText?.Replace("\n", "\\\\"))?.Replace("+", "%20"));
            return url;
        }

        internal static List<OcrContent> GetResultById(string id)
        {
            List<OcrContent> content = null;
            try
            {
                var url = "code.ashx?op=idcode";
                try
                {
                    var result = CommonMethod.GetServerHtml(url, CommonString.AutoCodeUrl, false, true, "id=" + id);
                    if (!string.IsNullOrEmpty(result) && result.Length > 2)
                        content = CommonString.JavaScriptSerializer.Deserialize<List<OcrContent>>(result);
                }
                catch (Exception)
                {
                    //Log.WriteError("CheckDecodeWeb出错", oe);
                }
            }
            catch (Exception)
            {
            }

            return content;
        }

        internal static bool SendRegMsg(string mobileNo, ref string strMsg)
        {
            var result = false;
            try
            {
                var url = string.Format("code.aspx?op=validatemsg&mobile={0}", mobileNo);
                url = CommonMethod.GetServerHtml(url, CommonString.HostAccountUrl, false);
                if (!url?.ToLower().Contains("true") == true)
                    strMsg = url;
                else
                    result = true;
            }
            catch (Exception)
            {
                //Log.WriteError("btnSend_Click出错", oe);
            }

            return result;
        }

        internal static void SendReportInfo(string content, List<UploadFileInfo> lstFiles, ref string strMsg)
        {
            try
            {
                var url = CommonString.AutoCodeUrl + "code.ashx?op=report";
                var values = new NameValueCollection { { "content", content } };
                var result = UploadFileRequest.Post(url, lstFiles.ToArray(), values, CommonMethod.GetRequestHeader());
                if (!result?.ToLower().Contains("true") == true) strMsg = result;
            }
            catch (Exception)
            {
            }
        }

        internal static bool SendRegInfo(string account, string pwd, string nickName, string code, ref string strMsg)
        {
            var result = false;
            try
            {
                var url = string.Format("code.aspx?op=reg&account={0}&pwd={1}&code={2}&nick={3}", account, pwd, code,
                    nickName);
                url = CommonMethod.GetServerHtml(url, CommonString.HostAccountUrl, false);
                if (!url?.ToLower().Contains("true") == true)
                    strMsg = url;
                else
                    result = true;
            }
            catch (Exception)
            {
                //Log.WriteError("btnSend_Click出错", oe);
            }

            return result;
        }

        internal static bool SendResetPwdMail(string email, ref string strMsg)
        {
            var result = false;
            try
            {
                var url = string.Format("mail.aspx?op=forgetpwd&email={0}", email);
                url = CommonMethod.GetServerHtml(url, CommonString.HostAccountUrl);
                if (!url?.ToLower().Contains("true") == true)
                    strMsg = url;
                else
                    result = true;
            }
            catch (Exception)
            {
                //Log.WriteError("btnSend_Click出错", oe);
            }

            return result;
        }

        internal static bool SendResetPwdMsg(string mobileNo, ref string strMsg)
        {
            var result = false;
            try
            {
                var url = string.Format("code.aspx?op=forgetpwd&mobile={0}", mobileNo);
                url = CommonMethod.GetServerHtml(url, CommonString.HostAccountUrl, false);
                if (!url?.ToLower().Contains("true") == true)
                    strMsg = url;
                else
                    result = true;
            }
            catch (Exception)
            {
                //Log.WriteError("btnSend_Click出错", oe);
            }

            return result;
        }

        internal static bool SendResetPwdInfo(string account, string pwd, string code, ref string strMsg)
        {
            var result = false;
            try
            {
                var url = string.Format("code.aspx?op=resetpwd&account={0}&pwd={1}&code={2}", account, pwd, code);
                url = CommonMethod.GetServerHtml(url, CommonString.HostAccountUrl, false);
                if (!url?.ToLower().Contains("true") == true)
                    strMsg = url;
                else
                    result = true;
            }
            catch (Exception)
            {
                //Log.WriteError("btnSend_Click出错", oe);
            }

            return result;
        }

        internal static bool EditNickName(string account, string strNickName, ref string strMsg)
        {
            var result = false;
            try
            {
                var url = string.Format("code.aspx?op=resetnickname&account={0}&nick={1}", account, strNickName);
                url = CommonMethod.GetServerHtml(url, CommonString.HostAccountUrl, false);
                if (!url?.ToLower().Contains("true") == true)
                    strMsg = url;
                else
                    result = true;
            }
            catch (Exception)
            {
                //Log.WriteError("btnSend_Click出错", oe);
            }

            return result;
        }

        internal static bool DoLogin(string account, string pwd, ref string strMsg)
        {
            var result = false;
            try
            {
                var url = string.Format("code.aspx?op=login&account={0}&pwd={1}", account, pwd);
                url = CommonMethod.GetServerHtml(url, CommonString.HostAccountUrl, false);
                if (!url?.ToLower().Contains("true") == true)
                {
                    //"True|" + user.StrAppCode + "|" + user.StrType + "|" + user.StrNickName + "|" + user.DtReg.ToString("yyyy-MM-dd HH:mm:ss") + "|" + user.DtExpire.ToString("yyyy-MM-dd HH:mm:ss") + "|" + user.StrRemark
                    strMsg = url;
                }
                else
                {
                    if (!string.IsNullOrEmpty(url))
                    {
                        url = url.Substring(url.IndexOf("|") + 1);
                        Program.NowUser = CommonString.JavaScriptSerializer.Deserialize<UserEntity>(url);
                    }

                    result = true;
                }
            }
            catch (Exception)
            {
                //Log.WriteError("btnSend_Click出错", oe);
            }

            return result;
        }

        internal static bool IsLogouted(string account, string token)
        {
            var result = false;
            try
            {
                var url = string.Format("code.aspx?op=heart&account={0}&token={1}", account, token);
                url = CommonMethod.GetServerHtml(url, CommonString.HostAccountUrl, false);
                if (!string.IsNullOrEmpty(url) && url.ToLower().Contains("false")) result = true;
            }
            catch (Exception)
            {
                //Log.WriteError("btnSend_Click出错", oe);
            }

            return result;
        }

        internal static List<UserType> GetCanRegUserTypes()
        {
            List<UserType> content = null;
            try
            {
                var url = "code.aspx?op=regtype";
                try
                {
                    var result = CommonMethod.GetServerHtml(url, CommonString.HostAccountUrl, false);
                    if (!string.IsNullOrEmpty(result))
                        content = CommonString.JavaScriptSerializer.Deserialize<List<UserType>>(result);
                }
                catch (Exception)
                {
                    //Log.WriteError("CheckDecodeWeb出错", oe);
                }
            }
            catch (Exception)
            {
            }

            return content;
        }

        internal static List<SearchEngine> GetGoogleUrl()
        {
            var content = new List<SearchEngine>();
            try
            {
                var result =
                    CommonMethod.GetServerHtml("code.ashx?op=google", CommonString.AutoCodeUrl, false, true);
                if (!string.IsNullOrEmpty(result) && result.Length > 2)
                {
                    var items = result.Split(new[] { "|" }, StringSplitOptions.RemoveEmptyEntries);
                    foreach (var item in items)
                    {
                        var eng = new SearchEngine
                        {
                            Name = CommonMethod.SubString(item, "", ","),
                            Url = CommonMethod.SubString(item, ",")
                        };
                        content.Add(eng);
                    }
                }
            }
            catch (Exception)
            {
                //Log.WriteError("CheckDecodeWeb出错", oe);
            }

            return content;
        }

        internal static void InitOcrGroup()
        {
            var dicTmp = GetOcrGroupTypes();
            if (dicTmp.Count > 0)
            {
                DicOcrGroup = dicTmp;
            }
        }

        internal static Dictionary<int, string> GetOcrGroupTypes()
        {
            var content = new Dictionary<int, string>();
            try
            {
                try
                {
                    var result =
                        CommonMethod.GetServerHtml("code.ashx?op=group", CommonString.AutoCodeUrl, false, true);
                    if (!string.IsNullOrEmpty(result) && result.Length > 2)
                    {
                        var items = result.Split(new[] { "|" }, StringSplitOptions.RemoveEmptyEntries);
                        foreach (var item in items)
                        {
                            var key = BoxUtil.GetInt32FromObject(CommonMethod.SubString(item, "", ","));
                            var value = CommonMethod.SubString(item, ",");
                            if (!content.ContainsKey(key))
                                content.Add(key, value);
                        }
                    }
                }
                catch (Exception)
                {
                    //Log.WriteError("CheckDecodeWeb出错", oe);
                }
            }
            catch (Exception)
            {
            }

            return content;
        }

        public static Dictionary<int, string> DicOcrGroup = new Dictionary<int, string>
        {
            {0,"不限"},
            {1,"百度"},
            {2,"腾讯"},
            {4,"有道"},
            {5,"搜狗"},
            {6,"讯飞"},
            {7,"迅捷"},
            {8,"VIVO"},
            {10,"学而思"},
            {11,"汉王"}
        };

        public static int GetGroupByName(string name)
        {
            int result = 0;
            foreach (var keyValuePair in DicOcrGroup)
            {
                if (Equals(keyValuePair.Value, name))
                {
                    result = keyValuePair.Key;
                    break;
                }
            }
            return result;
        }
    }

    public enum OcrType
    {
        文本 = 0,
        竖排 = 1,
        表格 = 2,
        公式 = 3,
        翻译 = 4
    }
}