namespace UtfUnknown.Core.Models
{
    public abstract class StateMachineModel
    {
        public const int Start = 0;

        public const int Error = 1;

        public int[] charLenTable;

        public BitPackage classTable;

        public BitPackage stateTable;

        protected StateMachineModel(BitPackage classTable, int classFactor, BitPackage stateTable, int[] charLenTable,
            string name)
        {
            this.classTable = classTable;
            ClassFactor = classFactor;
            this.stateTable = stateTable;
            this.charLenTable = charLenTable;
            Name = name;
        }

        public string Name { get; }

        public int ClassFactor { get; }

        public int GetClass(byte b)
        {
            return classTable.Unpack(b);
        }
    }
}