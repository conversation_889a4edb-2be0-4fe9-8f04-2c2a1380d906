using OCRTools.Common;
using OCRTools.Common.Entity;
using OCRTools.Properties;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Threading;
using System.Windows.Forms;

namespace OCRTools
{
    public static class Program
    {

        public static List<HistoryTask> RecentTasks;

        public static UserEntity NowUser { get; set; }

        [STAThread]
        private static void Main(string[] args)
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            //处理未捕获的异常
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);

            //处理UI线程异常
            Application.ThreadException += Application_ThreadException;

            //处理非UI线程异常
            AppDomain.CurrentDomain.UnhandledException += Backend_ThreadException;

            //var plug = new PlugEntity()
            //{
            //    Name = "ChatGPT",
            //    FontSize = 18.5F,
            //    Desc = "ChatGPT助手版",
            //    Url = "https://ocr.oldfish.cn",
            //    ButtonType = ButtonType.ImageAndText,
            //    Type = PlugType.Url,
            //    Image = ImageProcessHelper.ImageToBase64(Resources.chatgpt)
            //};
            //Console.WriteLine(CommonString.JavaScriptSerializer.Serialize(new List<PlugEntity>() { plug }));

            //Console.WriteLine();
            //ClipboardService.GetRtfHtml();

            //Application.Run(new RulerForm());

            //Application.Run(new ScrollingCaptureForm(new ScrollingCaptureOptions(), new RegionCaptureOptions(), false));

            //SelectRectangleList selectRectangleList = new SelectRectangleList
            //{
            //    IncludeChildWindows = true
            //};
            //var Windows = selectRectangleList.GetWindowInfoList();
            //var result = GZip.GZip.Compress(@"D:\Code\CatchTools\LocalOcrService\OcrMain\bin\Release\", @"D:\Code\CatchTools\LocalOcrService\OcrMain\bin\", "本地OCR.zip");
            //var result = GZip.GZip.Compress(@"C:\Users\<USER>\AppData\Roaming\OCR\OcrService\Models\", @"C:\Users\<USER>\AppData\Roaming\OCR\OcrService\", "飞浆Mobile.zip");

            //var byt = File.ReadAllBytes(@"D:\Code\CatchTools\bin\Debug\Microsoft.Web.WebView2.Core.dll");
            //File.WriteAllBytes("Microsoft.Web.WebView2.Core.txt", CommonEncryptHelper.Compress(byt));
            //byt = File.ReadAllBytes(@"D:\Code\CatchTools\bin\Debug\Microsoft.Web.WebView2.WinForms.dll");
            //File.WriteAllBytes("Microsoft.Web.WebView2.WinForms.txt", CommonEncryptHelper.Compress(byt));
            //byt = File.ReadAllBytes(@"D:\Code\CatchTools\bin\Debug\WebView2Loader.dll");
            //File.WriteAllBytes("WebView2Loader.txt", CommonEncryptHelper.Compress(byt));
            //byt = File.ReadAllBytes(@"D:\Code\CatchTools\bin\Debug\WebView2Loader.dll");
            //File.WriteAllBytes("WebView2Loader.dat", CommonEncryptHelper.Compress(byt));

            var targetLoc = CommonString.DataPath + Path.GetFileName(Application.ExecutablePath);
            if (CheckMainLocation(targetLoc) && CommonMethod.OpenFile(targetLoc))
            {
                return;
            }

            using (var mutex = new Mutex(true, CommonString.ProductName, out bool isFristInstance))
            {
                CommonString.IsAutoLogin = isFristInstance;
                var strMsg = args != null && args.Length > 0 ? args[0] : "";
                var isMsgProcess = !string.IsNullOrEmpty(strMsg);
                if (isMsgProcess)
                {
                    IniHelper.SetValue("系统", "右键菜单", strMsg);
                }
                if (isFristInstance || Path.GetFileNameWithoutExtension(Application.ExecutablePath).Equals(string.Format("{0}-多开版", CommonString.ProductName)))
                {
                    try
                    {
                        //Application.AddMessageFilter(new OcrMsgFilter());
                        RunMain();
                    }
                    catch { }
                    finally
                    {
                        if (isFristInstance)
                            mutex.ReleaseMutex();
                    }
                }
                else
                {
                    Application.ExitThread();
                }
            }
        }

        private static bool CheckMainLocation(string targetLoc)
        {
            var result = false;
#if !DEBUG
            var nowLoc = Application.ExecutablePath;
            if (!Equals(nowLoc, targetLoc))
            {
                if (!File.Exists(targetLoc))
                {
                    try
                    {
                        File.Copy(nowLoc, targetLoc);
                    }
                    catch (Exception)
                    {
                    }
                }
                result = File.Exists(targetLoc);
            }
#endif
            return result;
        }

        private static void InitShortLink()
        {
            ShortcutHelpers.CreateShortCuts();
            ShortcutHelpers.ClearOldShortCuts();
        }

        private static void RunMain()
        {
            var uue = new UheHandler();
            try
            {
                uue.InstallUheHandler();
                uue.SendingErrorReport += uue_SendingErrorReport;

                //CommonString.StrServerIp = "127.0.0.1";

                CommonString.InitRuntimeSetttings();

                CommonSetting.InitSetting();

                CommonTheme.InitTheme();

                if (!CommonMethod.IsAutoStart.Equals(CommonSetting.开机启动))
                {
                    CommonMethod.AutoStart(CommonSetting.开机启动);
                }

                InitShortLink();

                try
                {
                    InternetExplorerFeatureControl.Instance.BrowserEmulation = DocumentMode.DefaultRespectDocType;
                }
                catch { }

                //CommonString.HostCode?.FullUrl = "http://localhost:23205/";

                try
                {
                    var uiAutomation = Assembly.Load(Resources.Interop_UIAutomationClient);
                    AppDomain.CurrentDomain.AssemblyResolve += (sender, args) =>
                    {
                        if (args.Name.Contains("UIAutomationClient"))
                        {
                            return uiAutomation;
                        }
                        //else if (args.Name.Contains("PDFRender4NET"))
                        //{
                        //    return pdfRender4NET;
                        //}

                        return CommonString.LoadDllByName(args.Name);
                    };
                }
                catch
                {
                }

                Application.Run(new FrmMain());
            }
            catch { }
            finally
            {
                uue.UninstallUheHandler();
            }
        }

        private static void uue_SendingErrorReport(object sender, SendErrorReportArgs e)
        {
            if (!string.IsNullOrEmpty(e.Body)) Log.WriteLog(e.Body);
        }

        private static void Application_ThreadException(object sender, ThreadExceptionEventArgs e)
        {
            HandlerError(e.Exception);
        }

        private static void Backend_ThreadException(object sender, UnhandledExceptionEventArgs e)
        {
            HandlerError(e.ExceptionObject as Exception);
        }

        private static void HandlerError(Exception error)
        {
            Log.WriteError("运行时异常", error);
        }

        //private static void SetDpiAwareness()
        //{
        //    try
        //    {
        //        if (NativeMethods.OsVersion.Major >= 6)
        //        {
        //            //NativeMethods.SetProcessDPIAware();
        //NativeMethods.SetProcessDpiAwareness(NativeMethods.ProcessDPIAwareness.ProcessDPIUnaware);
        //        }
        //    }
        //    catch
        //    {
        //    }
        //}
    }

    [Serializable]
    [Obfuscation]
    public class SiteMain
    {
        [Obfuscation]
        public string update { get; set; }

        [Obfuscation]
        public List<WebInfo> web { get; set; }

        [Obfuscation]
        public string defaultHost { get; set; }
    }

    [Obfuscation]
    [Serializable]
    public class WebInfo
    {
        [Obfuscation]
        public string Host { get; set; }

        [Obfuscation]
        public string Ip { get; set; }

        [Obfuscation]
        public bool IsDefault { get; set; }

        [Obfuscation]
        public string FullUrl { get { return string.Format("http://{0}/", Host); } }

    }
}