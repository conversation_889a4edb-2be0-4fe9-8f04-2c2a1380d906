﻿using OCRTools;
using System.Drawing;

namespace MetroFramework
{
    public static class MetroFonts
    {
        public static Font TabControl(MetroTabControlSize labelSize, MetroTabControlWeight labelWeight)
        {
            switch (labelSize)
            {
                case MetroTabControlSize.Small:
                    switch (labelWeight)
                    {
                        case MetroTabControlWeight.Light:
                            return CommonString.GetSysNormalFont(12f);
                        case MetroTabControlWeight.Regular:
                            return CommonString.GetSysNormalFont(12f);
                        case MetroTabControlWeight.Bold:
                            return CommonString.GetSysNormalFont(12f);
                    }

                    break;
                case MetroTabControlSize.Medium:
                    switch (labelWeight)
                    {
                        case MetroTabControlWeight.Light:
                            return CommonString.GetSysNormalFont(14f);
                        case MetroTabControlWeight.Regular:
                            return CommonString.GetSysNormalFont(14f);
                        case MetroTabControlWeight.Bold:
                            return CommonString.GetSysNormalFont(14f);
                    }

                    break;
                case MetroTabControlSize.Tall:
                    switch (labelWeight)
                    {
                        case MetroTabControlWeight.Light:
                            return CommonString.GetSysNormalFont(18f);
                        case MetroTabControlWeight.Regular:
                            return CommonString.GetSysNormalFont(18f);
                        case MetroTabControlWeight.Bold:
                            return CommonString.GetSysNormalFont(18f);
                    }

                    break;
            }

            return CommonString.GetSysNormalFont(14f);
        }

        public static Font CheckBox(MetroCheckBoxSize linkSize, MetroCheckBoxWeight linkWeight)
        {
            switch (linkSize)
            {
                case MetroCheckBoxSize.Small:
                    switch (linkWeight)
                    {
                        case MetroCheckBoxWeight.Light:
                            return CommonString.GetSysNormalFont(12f);
                        case MetroCheckBoxWeight.Regular:
                            return CommonString.GetSysNormalFont(12f);
                        case MetroCheckBoxWeight.Bold:
                            return CommonString.GetSysNormalFont(12f);
                    }

                    break;
                case MetroCheckBoxSize.Medium:
                    switch (linkWeight)
                    {
                        case MetroCheckBoxWeight.Light:
                            return CommonString.GetSysNormalFont(14f);
                        case MetroCheckBoxWeight.Regular:
                            return CommonString.GetSysNormalFont(14f);
                        case MetroCheckBoxWeight.Bold:
                            return CommonString.GetSysNormalFont(14f);
                    }

                    break;
                case MetroCheckBoxSize.Tall:
                    switch (linkWeight)
                    {
                        case MetroCheckBoxWeight.Light:
                            return CommonString.GetSysNormalFont(18f);
                        case MetroCheckBoxWeight.Regular:
                            return CommonString.GetSysNormalFont(18f);
                        case MetroCheckBoxWeight.Bold:
                            return CommonString.GetSysNormalFont(18f);
                    }

                    break;
            }

            return CommonString.GetSysNormalFont(12f);
        }

        public static Font Button(MetroButtonSize linkSize, MetroButtonWeight linkWeight)
        {
            switch (linkSize)
            {
                case MetroButtonSize.Small:
                    switch (linkWeight)
                    {
                        case MetroButtonWeight.Light:
                            return CommonString.GetSysNormalFont(11f);
                        case MetroButtonWeight.Regular:
                            return CommonString.GetSysNormalFont(11f);
                        case MetroButtonWeight.Bold:
                            return CommonString.GetSysNormalFont(11f);
                    }

                    break;
                case MetroButtonSize.Medium:
                    switch (linkWeight)
                    {
                        case MetroButtonWeight.Light:
                            return CommonString.GetSysNormalFont(13f);
                        case MetroButtonWeight.Regular:
                            return CommonString.GetSysNormalFont(13f);
                        case MetroButtonWeight.Bold:
                            return CommonString.GetSysNormalFont(13f);
                    }

                    break;
                case MetroButtonSize.Tall:
                    switch (linkWeight)
                    {
                        case MetroButtonWeight.Light:
                            return CommonString.GetSysNormalFont(16f);
                        case MetroButtonWeight.Regular:
                            return CommonString.GetSysNormalFont(16f);
                        case MetroButtonWeight.Bold:
                            return CommonString.GetSysNormalFont(16f);
                    }

                    break;
            }

            return CommonString.GetSysNormalFont(11f);
        }
    }

    public enum MetroTabControlSize
    {
        Small,
        Medium,
        Tall
    }

    public enum MetroTabControlWeight
    {
        Light,
        Regular,
        Bold
    }

    public enum MetroCheckBoxSize
    {
        Small,
        Medium,
        Tall
    }

    public enum MetroCheckBoxWeight
    {
        Light,
        Regular,
        Bold
    }

    public enum MetroButtonSize
    {
        Small,
        Medium,
        Tall
    }

    public enum MetroButtonWeight
    {
        Light,
        Regular,
        Bold
    }
}