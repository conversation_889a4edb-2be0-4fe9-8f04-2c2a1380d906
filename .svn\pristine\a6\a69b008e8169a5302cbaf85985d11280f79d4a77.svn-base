﻿using MetroFramework.Components;
using MetroFramework.Design;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using OCRTools;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace MetroFramework.Controls
{
    [ToolboxBitmap(typeof(Button))]
    [Designer(typeof(MetroButtonDesigner), typeof(ParentControlDesigner))]
    [DefaultEvent("Click")]
    public class MetroButton : Button, IMetroControl
    {
        private bool _isFocused;

        private bool _isHovered;

        private bool _isPressed;

        private MetroColorStyle _metroStyle;

        private MetroThemeStyle _metroTheme;

        public MetroButton()
        {
            SetStyle(
                ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.SupportsTransparentBackColor |
                ControlStyles.OptimizedDoubleBuffer, true);
            //OCRTools.AnimationManager.EnableAnimation(this);
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseCustomBackColor { get; set; }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomForeColor { get; set; }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseStyleColors { get; set; }

        [Browsable(false)]
        [Category("Metro Behaviour")]
        [DefaultValue(false)]
        public bool UseSelectable
        {
            get => GetStyle(ControlStyles.Selectable);
            set => SetStyle(ControlStyles.Selectable, value);
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool DisplayFocus { get; set; }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool Highlight { get; set; }

        [DefaultValue(MetroButtonSize.Small)]
        [Category("Metro Appearance")]
        public MetroButtonSize FontSize { get; set; }

        [Category("Metro Appearance")]
        [DefaultValue(MetroButtonWeight.Bold)]
        public MetroButtonWeight FontWeight { get; set; } = MetroButtonWeight.Bold;

        [DefaultValue(MetroColorStyle.蓝色)]
        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                if (DesignMode || _metroStyle != 0) return _metroStyle;
                if (StyleManager != null) return StyleManager.Style;
                if (StyleManager == null) return MetroColorStyle.蓝色;
                return _metroStyle;
            }
            set => _metroStyle = value;
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroThemeStyle.Light)]
        public MetroThemeStyle Theme
        {
            get
            {
                if (DesignMode || _metroTheme != 0) return _metroTheme;
                if (StyleManager != null) return StyleManager.Theme;
                if (StyleManager == null) return MetroThemeStyle.Light;
                return _metroTheme;
            }
            set => _metroTheme = value;
        }

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public MetroStyleManager StyleManager { get; set; }

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintBackground != null) CustomPaintBackground(this, e);
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaint != null) CustomPaint(this, e);
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintForeground != null) CustomPaintForeground(this, e);
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            try
            {
                var color = BackColor;
                if (_isHovered && !_isPressed && Enabled)
                    color = MetroPaint.BackColor.Button.Hover(Theme);
                else if (_isHovered && _isPressed && Enabled)
                    color = MetroPaint.BackColor.Button.Press(Theme);
                else if (!Enabled)
                    color = MetroPaint.BackColor.Button.Disabled(Theme);
                else if (!UseCustomBackColor) color = MetroPaint.BackColor.Button.Normal(Theme);
                if (color.A == byte.MaxValue && BackgroundImage == null)
                {
                    e.Graphics.Clear(color);
                }
                else
                {
                    base.OnPaintBackground(e);
                    OnCustomPaintBackground(new MetroPaintEventArgs(color, Color.Empty, e.Graphics));
                }
            }
            catch
            {
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            if (!Size.IsValidate())
                return;
            try
            {
                if (GetStyle(ControlStyles.AllPaintingInWmPaint)) OnPaintBackground(e);
                OnCustomPaint(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
                OnPaintForeground(e);
            }
            catch
            {
                Invalidate();
            }
        }

        protected virtual void OnPaintForeground(PaintEventArgs e)
        {
            Color color;
            Color foreColor;
            if (_isHovered && !_isPressed && Enabled)
            {
                color = MetroPaint.BorderColor.Button.Hover(Theme);
                foreColor = MetroPaint.ForeColor.Button.Hover(Theme);
            }
            else if (_isHovered && _isPressed && Enabled)
            {
                color = MetroPaint.BorderColor.Button.Press(Theme);
                foreColor = MetroPaint.ForeColor.Button.Press(Theme);
            }
            else if (!Enabled)
            {
                color = MetroPaint.BorderColor.Button.Disabled(Theme);
                foreColor = MetroPaint.ForeColor.Button.Disabled(Theme);
            }
            else
            {
                color = MetroPaint.BorderColor.Button.Normal(Theme);
                foreColor = UseCustomForeColor ? ForeColor :
                    !UseStyleColors ? MetroPaint.ForeColor.Button.Normal(Theme) : MetroPaint.GetStyleColor(Style);
            }

            using (var pen = new Pen(color))
            {
                var rect = new Rectangle(0, 0, Width - 1, Height - 1);
                e.Graphics.DrawRectangle(pen, rect);
            }

            if (Highlight && !_isHovered && !_isPressed && Enabled)
                using (var pen2 = MetroPaint.GetStylePen(Style))
                {
                    var rect2 = new Rectangle(0, 0, Width - 1, Height - 1);
                    e.Graphics.DrawRectangle(pen2, rect2);
                    rect2 = new Rectangle(1, 1, Width - 3, Height - 3);
                    e.Graphics.DrawRectangle(pen2, rect2);
                }

            TextRenderer.DrawText(e.Graphics, Text, MetroFonts.Button(FontSize, FontWeight), ClientRectangle, foreColor,
                MetroPaint.GetTextFormatFlags(TextAlign));
            OnCustomPaintForeground(new MetroPaintEventArgs(Color.Empty, foreColor, e.Graphics));
            if (DisplayFocus && _isFocused) ControlPaint.DrawFocusRectangle(e.Graphics, ClientRectangle);
        }

        protected override void OnGotFocus(EventArgs e)
        {
            _isFocused = true;
            _isHovered = true;
            Invalidate();
            base.OnGotFocus(e);
        }

        protected override void OnLostFocus(EventArgs e)
        {
            _isFocused = false;
            _isHovered = false;
            _isPressed = false;
            Invalidate();
            base.OnLostFocus(e);
        }

        protected override void OnEnter(EventArgs e)
        {
            _isFocused = true;
            _isHovered = true;
            Invalidate();
            base.OnEnter(e);
        }

        protected override void OnLeave(EventArgs e)
        {
            _isFocused = false;
            _isHovered = false;
            _isPressed = false;
            Invalidate();
            base.OnLeave(e);
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Space)
            {
                _isHovered = true;
                _isPressed = true;
                Invalidate();
            }

            base.OnKeyDown(e);
        }

        protected override void OnKeyUp(KeyEventArgs e)
        {
            Invalidate();
            base.OnKeyUp(e);
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            _isHovered = true;
            Invalidate();
            base.OnMouseEnter(e);
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                _isPressed = true;
                Invalidate();
            }

            base.OnMouseDown(e);
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            _isPressed = false;
            Invalidate();
            base.OnMouseUp(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            if (!_isFocused) _isHovered = false;
            Invalidate();
            base.OnMouseLeave(e);
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);
            Invalidate();
        }
    }
}