﻿using OCRTools.Colors;
using OCRTools.Units;
using System;

namespace OCRTools
{
    [Serializable]
    public class Settings
    {
        private ThemeOption _selectedTheme;

        /// <summary>
        ///     Determines whether only a slim ruler scale should be shown.
        /// </summary>
        public bool SlimMode { get; set; }

        /// <summary>
        ///     The currently selected measuring unit.
        /// </summary>
        public MeasuringUnit MeasuringUnit { get; set; }

        /// <summary>
        ///     The DPI value that is used for calculating the distances on the ruler scale
        ///     (hardcoded to 96 by default).
        /// </summary>
        public float MonitorDpi { get; set; } = 96;

        /// <summary>
        ///     The display scaling value in percent (100 by default).
        /// </summary>
        public int MonitorScaling { get; set; } = 100;

        /// <summary>
        ///     Determines whether the center of the ruler should be marked.
        /// </summary>
        public bool ShowCenterLine { get; set; }

        /// <summary>
        ///     Determines whether the thirds of the ruler should be marked.
        /// </summary>
        public bool ShowThirdLines { get; set; }

        /// <summary>
        ///     Determines whether the Golden Ratio should be marked on the ruler.
        /// </summary>
        public bool ShowGoldenLine { get; set; }

        /// <summary>
        ///     Determines whether the position of the cursor should be marked.
        /// </summary>
        public bool ShowMouseLine { get; set; } = true;

        /// <summary>
        ///     Defines the thickness (in pixels) of one marking line.
        /// </summary>
        public byte MarkerThickness { get; set; } = 1;

        /// <summary>
        ///     Determines if multiple markings are allowed.
        /// </summary>
        public bool MultiMarking { get; set; } = true;

        /// <summary>
        ///     Determines whether labels for the ruler offset and length are shown.
        /// </summary>
        public bool ShowOffsetLengthLabels { get; set; } = true;

        /// <summary>
        ///     Determines whether to show the tool tip.
        /// </summary>
        public bool ShowToolTip { get; set; } = true;

        /// <summary>
        ///     Determines whether to show a symbol for markers
        /// </summary>
        public bool ShowMarkerSymbol { get; set; } = false;

        /// <summary>
        ///     Determines whether the ruler should follow the mouse pointer.
        /// </summary>
        public bool FollowMousePointer { get; set; } = false;

        /// <summary>
        ///     If set to true, the center point of the ruler will follow the mouse pointer; otherwise left upper corner will be
        ///     attached.
        /// </summary>
        public bool FollowMousePointerCenter { get; set; } = true;

        /// <summary>
        ///     The currently selected theme option.
        /// </summary>
        public ThemeOption SelectedTheme
        {
            get => _selectedTheme;
            set
            {
                _selectedTheme = value;
                switch (value)
                {
                    case ThemeOption.白色:
                        Theme = CommonThemes.LightTheme;
                        break;
                    case ThemeOption.黑色:
                        Theme = CommonThemes.DarkTheme;
                        break;
                }

                SelectedThemeChanged?.Invoke(this, new EventArgs());
            }
        }

        /// <summary>
        ///     The currently selected theme (influenced by SelectedTheme).
        /// </summary>
        public Theme Theme { get; set; } = CommonThemes.LightTheme;

        /// <summary>
        ///     Defines the size of one small ruler moving/ resizing step.
        /// </summary>
        public int SmallStep { get; set; } = 1;

        /// <summary>
        ///     Defines the size of one medium ruler resizing step.
        /// </summary>
        public int MediumStep { get; set; } = 5;

        /// <summary>
        ///     Defines the size of one big ruler resizing step.
        /// </summary>
        public int LargeStep { get; set; } = 25;

        /// <summary>
        ///     This event is raised whenever the selected theme option has changed.
        /// </summary>
        public event EventHandler SelectedThemeChanged;
    }
}