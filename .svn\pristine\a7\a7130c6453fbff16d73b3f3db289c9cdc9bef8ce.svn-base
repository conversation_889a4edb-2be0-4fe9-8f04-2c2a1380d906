﻿using System;
using System.Diagnostics;
using System.Management;
using System.Runtime.InteropServices;

namespace OCRTools
{
    public static class KillProcessHelper
    {
        private const int WM_CLOSE = 0x0010;

        [DllImport("User32.dll", EntryPoint = "SendMessage")]
        private static extern int SendMessage(IntPtr hWnd, int msg, int wParam, int lParam);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool CloseHandle(IntPtr hSnapshot);

        #region 根据指定的应用程序标题关闭相应窗口

        public static void KillProcess(Process proce)
        {
            if (proce != null)
            {
                try
                {
                    KillProcessByText(proce.ProcessName);
                }
                catch
                {
                }

                try
                {
                    KillProcessById(proce.Id);
                }
                catch
                {
                }

                try
                {
                    KillProcessByIntPtr(proce.Handle);
                }
                catch
                {
                }
            }
        }

        private static void KillProcessById(int pid)
        {
            try
            {
                var searcher = new ManagementObjectSearcher("Select * From Win32_Process Where ParentProcessID=" + pid);
                var moc = searcher.Get();
                foreach (var o in moc)
                {
                    var mo = (ManagementObject)o;
                    KillProcessById(Convert.ToInt32(mo["ProcessID"]));
                }
            }
            catch
            {
            }

            try
            {
                var proc = Process.GetProcessById(pid);
                proc.Kill();
            }
            catch
            {
            }
        }

        private static void KillProcessByText(string text)
        {
            if (!string.IsNullOrEmpty(text))
            {
                //强制关闭进程
                try
                {
                    CommonMethod.ExecCmd("taskkill /im " + text + ".exe /f ");
                }
                catch
                {
                }
            }
        }

        private static void KillProcessByIntPtr(IntPtr intptr)
        {
            SendMessage(intptr, WM_CLOSE, 0, 0);
            CloseHandle(intptr);
        }

        #endregion
    }
}