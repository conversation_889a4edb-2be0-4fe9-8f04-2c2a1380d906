﻿using System;
using System.IO;
using System.IO.Compression;
using System.Security.Cryptography;
using System.Text;

namespace OCRTools
{
    public class CommonEncryptHelper
    {
        private static readonly byte[] MBtIv = {0x12, 0x34, 0x56, 0x78, 0x90, 0xAB, 0xCD, 0xEF};

        #region GZip
        /// <summary>
        /// GZip压缩
        /// </summary>
        /// <param name="rawData"></param>
        /// <returns></returns>
        public static byte[] Compress(byte[] rawData)
        {
            using (var ms = new MemoryStream())
            {
                using (var zipSteam = new GZipStream(ms, CompressionMode.Compress, true))
                {
                    zipSteam.Write(rawData, 0, rawData.Length);
                    zipSteam.Close();
                }
                return ms.ToArray();
            }
        }

        /// <summary>
        /// ZIP解压
        /// </summary>
        /// <param name="zippedData"></param>
        /// <returns></returns>
        public static byte[] Decompress(byte[] zippedData)
        {
            using (var ms = new MemoryStream(zippedData))
            {
                using (var zipSteam = new GZipStream(ms, CompressionMode.Decompress))
                {
                    using (var outBuffer = new MemoryStream())
                    {
                        byte[] block = new byte[1024];
                        while (true)
                        {
                            int bytesRead = zipSteam.Read(block, 0, block.Length);
                            if (bytesRead <= 0)
                                break;
                            outBuffer.Write(block, 0, bytesRead);
                        }
                        zipSteam.Close();
                        return outBuffer.ToArray();
                    }
                }
            }
        }
        #endregion

        #region DES 加密解密

        /// <summary>
        ///     DES 加密(数据加密标准，速度较快，适用于加密大量数据的场合)
        /// </summary>
        /// <param name="encryptString">待加密的密文</param>
        /// <param name="encryptKey">加密的密钥</param>
        /// <returns>returns</returns>
        public static string DesEncrypt(string encryptString, string encryptKey)
        {
            var mStrEncrypt = "";
            if (string.IsNullOrEmpty(encryptString) || string.IsNullOrEmpty(encryptKey) || encryptKey.Length != 8)
                return mStrEncrypt;
            //if (string.IsNullOrEmpty(EncryptString)) { throw (new Exception("密文不得为空")); }
            //if (string.IsNullOrEmpty(EncryptKey)) { throw (new Exception("密钥不得为空")); }
            //if (EncryptKey.Length != 8) { throw (new Exception("密钥必须为8位")); }
            using (var mDesProvider = new DESCryptoServiceProvider())
            {
                try
                {
                    var mBtEncryptString = Encoding.Default.GetBytes(encryptString);
                    using (var mStream = new MemoryStream())
                    {
                        using (var mCstream = new CryptoStream(mStream,
                            mDesProvider.CreateEncryptor(Encoding.Default.GetBytes(encryptKey), MBtIv),
                            CryptoStreamMode.Write))
                        {
                            mCstream.Write(mBtEncryptString, 0, mBtEncryptString.Length);
                            mCstream.FlushFinalBlock();
                            mStrEncrypt = Convert.ToBase64String(mStream.ToArray());
                            mCstream.Close();
                            mCstream.Dispose();
                            mStream.Close();
                            mStream.Dispose();
                        }
                    }
                }
                //catch (IOException ex) { throw ex; }
                //catch (CryptographicException ex) { throw ex; }
                //catch (ArgumentException ex) { throw ex; }
                catch (Exception)
                {
                    //Log.WriteError(oe);
                }
                finally
                {
                    mDesProvider.Clear();
                }
            }

            return mStrEncrypt;
        }

        #endregion
    }
}