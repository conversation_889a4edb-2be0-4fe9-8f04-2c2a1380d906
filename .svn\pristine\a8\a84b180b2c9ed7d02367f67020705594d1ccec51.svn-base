﻿using MetroFramework.Forms;
using System;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmViewUrl : MetroForm
    {
        public string Url { get; set; }

        public FrmViewUrl()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            wbView.ScriptErrorsSuppressed = true;
            wbView.Document.Window.Error += (s, g) =>
            {
                g.Handled = true;
            };
        }

        private void WbPay_DocumentCompleted(object sender, WebBrowserDocumentCompletedEventArgs e)
        {
        }

        private void FrmGoBuy_Load(object sender, EventArgs e)
        {
            pnlView.Dock = DockStyle.Fill;
            pnlView.Visible = true;
            pnlView.BringToFront();
            wbView.Navigate(Url);
        }

        private void wbView_DocumentCompleted(object sender, WebBrowserDocumentCompletedEventArgs e)
        {
            this.Text = wbView.Document?.Title ?? "OCR助手";
            this.Invalidate();
        }
    }
}