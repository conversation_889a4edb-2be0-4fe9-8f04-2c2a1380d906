using System;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;

namespace OCRTools
{
    public static class HotKeyInfo
    {
        [DllImport("user32.dll")]
        public static extern bool RegisterHotKey(IntPtr hWnd, int id, uint fsModifiers, Keys vk);

        [DllImport("user32.dll")]
        public static extern bool UnregisterHotKey(IntPtr hWnd, int id);

        [DllImport("user32.dll")]
        private static extern int MapVirtualKey(uint uCode, uint uMapType);

        private static char KeyCodeToChar(Keys k)
        {
            int value = MapVirtualKey((uint)k, 2u);
            return Convert.ToChar(value);
        }

        public static string KeyCodeToStr(Keys k)
        {
            char c = KeyCodeToChar(k);
            string text = c.ToString();
            if (char.IsWhiteSpace(c) || string.IsNullOrEmpty(text) || c == '\r' || c == '\n' || c == KeyCodeToChar(Keys.F1))
            {
                return k.ToString();
            }
            return text ?? "";
        }

        public static string GetStringByKey(KeyEventArgs e, out string EditKey)
        {
            EditKey = e.KeyCode.ToString();
            if (e.KeyValue == 16)
            {
                return "Shift + ";
            }
            if (e.KeyValue == 17)
            {
                return "Ctrl + ";
            }
            if (e.KeyValue == 18)
            {
                return "Alt + ";
            }
            StringBuilder stringBuilder = new StringBuilder();
            if (e.Modifiers != 0)
            {
                if (e.Control)
                {
                    stringBuilder.Append("Ctrl + ");
                }
                if (e.Alt)
                {
                    stringBuilder.Append("Alt + ");
                }
                if (e.Shift)
                {
                    stringBuilder.Append("Shift + ");
                }
            }
            EditKey = stringBuilder.ToString() + e.KeyCode;
            stringBuilder.Append(KeyCodeToStr(e.KeyCode));
            return stringBuilder.ToString();
        }

        public static string GetSingleStrByKey(KeyEventArgs e)
        {
            if (e.KeyValue == 16)
            {
                return "Shift";
            }
            if (e.KeyValue == 17)
            {
                return "Ctrl";
            }
            if (e.KeyValue == 18)
            {
                return "Alt";
            }
            return e.KeyCode.ToString();
        }

        public static KeyModifiers KeyToKeyModifiers(Keys strKey)
        {
            KeyModifiers keyModifiers = KeyModifiers.None;
            string[] array = strKey.ToString().Split(',');
            if (array.Length != 0)
            {
                string[] array2 = array;
                foreach (string text in array2)
                {
                    if (text.Trim().ToUpper() == "CONTROL")
                    {
                        keyModifiers |= KeyModifiers.Ctrl;
                    }
                    else if (text.Trim().ToUpper() == "SHIFT")
                    {
                        keyModifiers |= KeyModifiers.Shift;
                    }
                    else if (text.Trim().ToUpper() == "ALT")
                    {
                        keyModifiers |= KeyModifiers.Alt;
                    }
                }
            }
            return keyModifiers;
        }

        public static void GetHotKey(string strKey, out KeyModifiers modifiers, out Keys keys)
        {
            Keys keys2 = Keys.None;
            string[] array = strKey.Split('+');
            if (array.Length != 0)
            {
                string[] array2 = array;
                foreach (string text in array2)
                {
                    int result;
                    if (text.Trim().ToUpper() == "CTRL")
                    {
                        keys2 |= Keys.Control;
                    }
                    else if (text.Trim().ToUpper() == "SHIFT")
                    {
                        keys2 |= Keys.Shift;
                    }
                    else if (text.Trim().ToUpper() == "ALT")
                    {
                        keys2 |= Keys.Alt;
                    }
                    else if (int.TryParse(text, out result))
                    {
                        KeysConverter keysConverter = new KeysConverter();
                        Keys keys3 = (Keys)keysConverter.ConvertFromString("D" + text.Trim());
                        keys2 |= keys3;
                    }
                    else
                    {
                        KeysConverter keysConverter2 = new KeysConverter();
                        Keys keys4 = (Keys)keysConverter2.ConvertFromString(text.Trim());
                        keys2 |= keys4;
                    }
                }
            }
            KeyEventArgs keyEventArgs = new KeyEventArgs(keys2);
            modifiers = KeyToKeyModifiers(keyEventArgs.Modifiers);
            keys = keyEventArgs.KeyCode;
        }
    }
}
