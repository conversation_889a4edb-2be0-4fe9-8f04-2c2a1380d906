﻿using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools
{
    internal class CommonMsg
    {
        private static int nowMSGID = -1;

        private static List<ScrollEntity> lstMSG = new List<ScrollEntity>();

        private static ScrollingText srcTxt;

        private static bool IsLoopAllOnce;

        private static int nLoopTimes;

        public static void Refresh()
        {
            if (srcTxt == null || srcTxt.Parent == null) return;
            if (srcTxt.Parent is FrmMain) srcTxt.BackColor = CommonSetting.默认背景颜色;
        }

        public static void ShowToWindow(Form owner, Point location, bool isOnce = false)
        {
            if (srcTxt == null) Init();
            IsLoopAllOnce = isOnce;
            if (nLoopTimes >= 1 && IsLoopAllOnce) return;
            srcTxt.Parent = owner;
            srcTxt.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            srcTxt.Location = location;
            if (owner is FrmMain)
            {
                srcTxt.BackColor = CommonSetting.默认背景颜色;
                srcTxt.Size = new Size(owner.Width - location.X * 2, 40);
            }
            else
            {
                srcTxt.BackColor = owner.BackColor;
                srcTxt.Size = new Size(owner.Width - location.X, 40);
            }
            srcTxt.Visible = true;
            srcTxt.BringToFront();
        }

        public static void Hide(Form owner)
        {
            if (srcTxt == null) return;
            srcTxt.Parent = null;
        }

        private static void Init()
        {
            srcTxt = new ScrollingText
            {
                ScrollText = "正在初始化…",
                VerticleTextPosition = VerticleTextPosition.Center,
                TextScrollSpeed = 60,
                TextScrollDistance = 2,
                MaxLoopTimes = 3,
                StopScrollOnMouseOver = false,
                ShowBorder = false,
                ScrollDirection = ScrollDirection.RightToLeft,
                Font = new Font("微软雅黑", 15F, FontStyle.Bold)
            };
            Task.Factory.StartNew(() => { ProcessMsg(); });
        }

        private static void ProcessMsg()
        {
            while (!CommonString.isExit)
            {
                try
                {
                    if (lstMSG == null || lstMSG.Count <= 0) lstMSG = GetLstMsg();
                    if (lstMSG != null && lstMSG.Count > 0)
                    {
                        nowMSGID++;
                        if (nowMSGID >= lstMSG.Count)
                        {
                            nLoopTimes++;
                            if (nLoopTimes == 1 && IsLoopAllOnce) srcTxt.Hide();
                        }

                        nowMSGID = nowMSGID <= 0 ? 0 : nowMSGID;
                        nowMSGID = nowMSGID >= lstMSG.Count ? 0 : nowMSGID;
                        srcTxt.ForeColor = lstMSG[nowMSGID].ForeColor;
                        srcTxt.StrLink = lstMSG[nowMSGID].LnkURL;
                        srcTxt.ScrollText =
                            lstMSG[nowMSGID].Text + (string.IsNullOrEmpty(srcTxt.StrLink) ? "" : "(链接)");
                        srcTxt.StopScrollOnMouseOver = !string.IsNullOrEmpty(srcTxt.StrLink);
                    }
                }
                catch
                {
                    nowMSGID = -1;
                }

                var maxSecond =
                    nowMSGID >= 0 && lstMSG != null && lstMSG.Count > 0 && lstMSG[nowMSGID].Text.Length >= 50
                        ? 120
                        : 60;
                var nowSecond = 0;
                while (!CommonString.isExit)
                {
                    Thread.Sleep(1000);
                    nowSecond++;
                    if (nowSecond >= maxSecond || srcTxt.LoopTimes >= srcTxt.MaxLoopTimes) break;
                }
            }
        }

        private static List<ScrollEntity> GetLstMsg()
        {
            var list = new List<ScrollEntity>();
            try
            {
                var responseText =
                    WebClientExt.GetHtml(CommonString.StrUpdateURL + "update/msg.txt?t=" + DateTime.Now.Ticks, 5);
                if (!string.IsNullOrEmpty(responseText))
                {
                    var array = responseText.Split(new[] { "\n" }, StringSplitOptions.RemoveEmptyEntries);
                    if (array.Length > 0)
                    {
                        string[] ss = null;
                        foreach (var str in array)
                        {
                            ss = str.Trim().Split(new[] { "|" }, StringSplitOptions.RemoveEmptyEntries);
                            if (ss != null && ss.Length > 0)
                            {
                                var entity = new ScrollEntity
                                {
                                    Text = ss[0]
                                };
                                try
                                {
                                    entity.ForeColor = Color.FromName(ss.Length > 1 ? ss[1] : "black");
                                }
                                catch
                                {
                                    entity.ForeColor = Color.Black;
                                }

                                entity.LnkURL = ss.Length > 2 ? ss[2] : "";
                                list.Add(entity);
                            }
                        }

                        ss = null;
                    }

                    responseText = "";
                    array = null;
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            var strHoliday = ChinaDate.GetChinaDate(ServerTime.DateTime);
            if (!string.IsNullOrEmpty(strHoliday))
            {
                list.Insert(0, new ScrollEntity()
                {
                    ForeColor = Color.Blue,
                    Text = string.Format("今天是{0}{1}，祝您生活愉快！", ServerTime.DateTime.ToString("yyyy-MM-dd"), strHoliday)
                });
            }

            return list;
        }
    }
}