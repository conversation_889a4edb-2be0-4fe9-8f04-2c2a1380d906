﻿using OCRTools;
using OCRTools.Common;
using System;
using System.Collections.Specialized;
using System.Reflection;

namespace ImageLib
{
    /// <summary>
    /// https://tinypng.com/
    /// </summary>
    internal class TinyPngUpload
    {
        private static readonly Random RndNext = new Random();

        internal static string GetResultUrl(byte[] content)
        {
            var result = string.Empty;
            var html = WebClientExt.GetHtml("https://tinypng.com/web/shrink", Convert.ToBase64String(content), 30, new NameValueCollection
                {
                    {"X-Forwarded-For",string.Format("{0}.{1}.{2}.{3}",RndNext.Next(1,255),RndNext.Next(1,255),RndNext.Next(1,255),RndNext.Next(1,255))}
                });
            if (!string.IsNullOrEmpty(html) && html.Length > 2)
            {
                var compressResult = CommonString.JavaScriptSerializer.Deserialize<ImageCompressResult>(html);
                if (compressResult.output != null && !string.IsNullOrEmpty(compressResult.output.url))
                {
                    result = compressResult.output.url.TrimEnd('/') + "/1.jpg";
                }
            }
            return result;
        }

        internal static byte[] GetZipResult(byte[] content, ref string strUrl)
        {
            strUrl = GetResultUrl(content);
            return CommonMethod.GetUrlBytes(strUrl);
        }
    }

    [Obfuscation]
    internal class ImageCompressResult
    {
        [Obfuscation]
        public string FileName { get; set; }

        [Obfuscation]
        public ImageCompressInfo input { get; set; }

        [Obfuscation]
        public ImageCompressInfo output { get; set; }
    }

    [Obfuscation]
    internal class ImageCompressInfo
    {
        [Obfuscation]
        public string url { get; set; }

        [Obfuscation]
        public string size { get; set; }
    }
}
