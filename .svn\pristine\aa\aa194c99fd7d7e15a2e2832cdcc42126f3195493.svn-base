﻿#region License Information (GPL v3)

/*
    ShareX - A program that allows you to take screenshots and share any file type
    Copyright (c) 2007-2021 ShareX Team

    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License
    as published by the Free Software Foundation; either version 2
    of the License, or (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.

    Optionally you can also view the license at <http://www.gnu.org/licenses/>.
*/

#endregion License Information (GPL v3)

using ShareX.HelpersLib;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.Design;
using MetroFramework.Native;
using OCRTools;
using OCRTools.Common;
using OCRTools.Properties;

namespace ShareX.ScreenCaptureLib
{
    public class ToolStripEx : ToolStrip
    {
        private bool clickThrough = false;

        [DefaultValue(false)]
        public bool ClickThrough
        {
            get
            {
                return clickThrough;
            }
            set
            {
                clickThrough = value;
            }
        }

        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m);

            if (clickThrough && m.Msg == (int)0x0021 && m.Result == (IntPtr)2)
            {
                m.Result = (IntPtr)1;
            }
        }
    }

    [ToolStripItemDesignerAvailability(ToolStripItemDesignerAvailability.MenuStrip | ToolStripItemDesignerAvailability.ContextMenuStrip)]
    public class ToolStripLabeledNumericUpDown : ToolStripControlHost
    {
        public LabeledNumericUpDown Content => Control as LabeledNumericUpDown;

        public ToolStripLabeledNumericUpDown(string text) : base(new LabeledNumericUpDown())
        {
            Content.Text = text;
        }
    }
    public enum ImageInterpolationMode
    {
        HighQualityBicubic,
        Bicubic,
        HighQualityBilinear,
        Bilinear,
        NearestNeighbor
    }
    public enum ArrowHeadDirection // Localized
    {
        End,
        Start,
        Both
    }
    public enum StepType // Localized
    {
        数字,
        字母_大写,
        字母_小写,
        罗马数字_大写,
        罗马数字_小写
    }

    internal partial class ShapeManager
    {
        public bool ToolbarCreated { get; private set; }
        public bool ToolbarCollapsed { get; private set; }

        internal TextAnimation MenuTextAnimation = new TextAnimation()
        {
            FadeInDuration = TimeSpan.FromMilliseconds(0),
            Duration = TimeSpan.FromMilliseconds(5000),
            FadeOutDuration = TimeSpan.FromMilliseconds(500)
        };

        private Form menuForm;
        private ToolStripEx tsMain;
        private ToolStripButton tsbBorderColor, tsbFillColor, tsbHighlightColor;
        private ToolStripDropDownButton tsddbShapeOptions;
        private ToolStripMenuItem tsmiShadow, tsmiShadowColor, tsmiUndo, tsmiDuplicate, tsmiDelete, tsmiDeleteAll,
            tsmiMoveTop, tsmiMoveUp, tsmiMoveDown, tsmiMoveBottom, tsmiRegionCapture, tsmiQuickCrop, tsmiShowMagnifier;
        private ToolStripLabeledNumericUpDown tslnudBorderSize, tslnudCornerRadius, tslnudCenterPoints, tslnudPixelateSize, tslnudStepFontSize,
            tslnudMagnifierPixelCount, tslnudStartingStepValue, tslnudMagnifyStrength;
        private ToolStripLabel tslDragLeft, tslDragRight;
        private ToolStripLabeledComboBox tscbBorderStyle, tscbArrowHeadDirection, tscbImageInterpolationMode, tscbStepType;

        internal void CreateToolbar()
        {
            menuForm = new Form
            {
                AutoScaleDimensions = new SizeF(6F, 13F),
                AutoScaleMode = AutoScaleMode.Font,
                AutoSize = true,
                AutoSizeMode = AutoSizeMode.GrowAndShrink,
                ClientSize = new Size(759, 509),
                FormBorderStyle = FormBorderStyle.None,
                Location = new Point(200, 200),
                ShowInTaskbar = false,
                StartPosition = FormStartPosition.Manual,
                Text = Application.ProductName,
                TopMost = true
            };

            menuForm.Shown += MenuForm_Shown;
            menuForm.KeyDown += MenuForm_KeyDown;
            menuForm.KeyUp += MenuForm_KeyUp;
            menuForm.LocationChanged += MenuForm_LocationChanged;
            menuForm.GotFocus += MenuForm_GotFocus;
            menuForm.LostFocus += MenuForm_LostFocus;

            menuForm.SuspendLayout();

            tsMain = new ToolStripEx()
            {
                AutoSize = true,
                CanOverflow = true,
                ClickThrough = true,
                Dock = DockStyle.None,
                GripStyle = ToolStripGripStyle.Hidden,
                Location = new Point(0, 0),
                MinimumSize = new Size(10, 30),
                Padding = new Padding(2, 1, 0, 0),
                Renderer = new ToolStripRoundedEdgeRenderer(),
                TabIndex = 0,
                ShowItemToolTips = false
            };

            tsMain.MouseLeave += TsMain_MouseLeave;

            tsMain.SuspendLayout();

            menuForm.Controls.Add(tsMain);

            tslDragLeft = new ToolStripLabel()
            {
                DisplayStyle = ToolStripItemDisplayStyle.Image,
                Image = ImageHelp.DrawGrip(Color.FromArgb(255, 22, 26, 31), Color.FromArgb(255, 56, 64, 75)),
                Margin = new Padding(0, 0, 2, 0),
                Padding = new Padding(2),
                Visible = true
            };

            tsMain.Items.Add(tslDragLeft);

            #region Tools

            foreach (ShapeType shapeType in Enum.GetValues(typeof(ShapeType)))
            {
                //if (Form.IsEditorMode)
                {
                    if (IsShapeTypeRegion(shapeType))
                    {
                        continue;
                    }
                }
                //if (shapeType == ShapeType.选择并移动)
                //{
                //    tsMain.Items.Add(new ToolStripSeparator());
                //}
                //else 
                if (shapeType == ShapeType.裁剪)
                {
                    continue;
                }

                ToolStripButton tsbShapeType = new ToolStripButton(shapeType.ToString())
                {
                    DisplayStyle = ToolStripItemDisplayStyle.Image
                };

                Image img = null;

                switch (shapeType)
                {
                    case ShapeType.矩形区域:
                        img = Resources.layer_shape_region;
                        break;
                    case ShapeType.圆形区域:
                        img = Resources.layer_shape_ellipse_region;
                        break;
                    case ShapeType.矩形:
                        img = CommonSetting.深色模式 ? Resources.layer_shape_white : Resources.layer_shape;
                        break;
                    case ShapeType.圆形:
                        img = CommonSetting.深色模式 ? Resources.layer_shape_ellipse_white : Resources.layer_shape_ellipse;
                        break;
                    case ShapeType.画笔:
                        img = CommonSetting.深色模式 ? Resources.pencil_white : Resources.pencil;
                        break;
                    case ShapeType.箭头:
                        img = CommonSetting.深色模式 ? Resources.layer_shape_arrow_white : Resources.layer_shape_arrow;
                        break;
                    case ShapeType.文字描边:
                        img = CommonSetting.深色模式 ? Resources.edit_outline_white : Resources.edit_outline;
                        break;
                    case ShapeType.文字背景填充:
                        img = CommonSetting.深色模式 ? Resources.edit_shade_white : Resources.edit_shade;
                        break;
                    case ShapeType.气泡:
                        img = Resources.balloon_box_left;
                        break;
                    case ShapeType.序号:
                        img = Resources.counter_reset;
                        break;
                    case ShapeType.放大镜:
                        img = Resources.magnifier_zoom;
                        break;
                    case ShapeType.橡皮擦:
                        img = Resources.eraser;
                        break;
                    case ShapeType.马赛克:
                        img = CommonSetting.深色模式 ? Resources.grid_white : Resources.grid;
                        break;
                    case ShapeType.高亮:
                        img = Resources.highlighter_text;
                        break;
                    case ShapeType.裁剪:
                        img = CommonSetting.深色模式 ? Resources.image_crop_white : Resources.image_crop;
                        break;
                    case ShapeType.选择并移动:
                        img = Resources.cursor;
                        break;
                }

                tsbShapeType.Image = img;
                tsbShapeType.Checked = shapeType == CurrentTool;
                tsbShapeType.Tag = shapeType;

                tsbShapeType.MouseDown += (sender, e) =>
                {
                    tsbShapeType.RadioCheck();
                    CurrentTool = shapeType;
                };

                tsMain.Items.Add(tsbShapeType);
            }

            #endregion Tools

            #region Shape options

            tsMain.Items.Add(new ToolStripSeparator());

            tsbBorderColor = new ToolStripButton("边框颜色...");
            tsbBorderColor.DisplayStyle = ToolStripItemDisplayStyle.Image;
            tsbBorderColor.Click += (sender, e) =>
            {
                Form.Pause();

                ShapeType shapeType = CurrentShapeTool;

                Color borderColor;

                if (shapeType == ShapeType.文字背景填充 || shapeType == ShapeType.气泡)
                {
                    borderColor = AnnotationOptions.TextBorderColor;
                }
                else if (shapeType == ShapeType.文字描边)
                {
                    borderColor = AnnotationOptions.TextOutlineBorderColor;
                }
                else if (shapeType == ShapeType.序号)
                {
                    borderColor = AnnotationOptions.StepBorderColor;
                }
                else
                {
                    borderColor = AnnotationOptions.BorderColor;
                }

                if (PickColor(borderColor, out Color newColor))
                {
                    if (shapeType == ShapeType.文字背景填充 || shapeType == ShapeType.气泡)
                    {
                        AnnotationOptions.TextBorderColor = newColor;
                    }
                    else if (shapeType == ShapeType.文字描边)
                    {
                        AnnotationOptions.TextOutlineBorderColor = newColor;
                    }
                    else if (shapeType == ShapeType.序号)
                    {
                        AnnotationOptions.StepBorderColor = newColor;
                    }
                    else
                    {
                        AnnotationOptions.BorderColor = newColor;
                    }

                    UpdateMenu();
                    UpdateCurrentShape();
                }

                Form.Resume();
            };
            tsMain.Items.Add(tsbBorderColor);

            tsbFillColor = new ToolStripButton("填充颜色...");
            tsbFillColor.DisplayStyle = ToolStripItemDisplayStyle.Image;
            tsbFillColor.Click += (sender, e) =>
            {
                Form.Pause();

                ShapeType shapeType = CurrentShapeTool;

                Color fillColor;

                if (shapeType == ShapeType.文字背景填充 || shapeType == ShapeType.气泡)
                {
                    fillColor = AnnotationOptions.TextFillColor;
                }
                else if (shapeType == ShapeType.序号)
                {
                    fillColor = AnnotationOptions.StepFillColor;
                }
                else
                {
                    fillColor = AnnotationOptions.FillColor;
                }

                if (PickColor(fillColor, out Color newColor))
                {
                    if (shapeType == ShapeType.文字背景填充 || shapeType == ShapeType.气泡)
                    {
                        AnnotationOptions.TextFillColor = newColor;
                    }
                    else if (shapeType == ShapeType.序号)
                    {
                        AnnotationOptions.StepFillColor = newColor;
                    }
                    else
                    {
                        AnnotationOptions.FillColor = newColor;
                    }

                    UpdateMenu();
                    UpdateCurrentShape();
                }

                Form.Resume();
            };
            tsMain.Items.Add(tsbFillColor);

            tsbHighlightColor = new ToolStripButton("高亮颜色...");
            tsbHighlightColor.DisplayStyle = ToolStripItemDisplayStyle.Image;
            tsbHighlightColor.Click += (sender, e) =>
            {
                Form.Pause();

                if (PickColor(AnnotationOptions.HighlightColor, out Color newColor))
                {
                    AnnotationOptions.HighlightColor = newColor;
                    UpdateMenu();
                    UpdateCurrentShape();
                }

                Form.Resume();
            };
            tsMain.Items.Add(tsbHighlightColor);

            tsddbShapeOptions = new ToolStripDropDownButton("属性设置");
            tsddbShapeOptions.DisplayStyle = ToolStripItemDisplayStyle.Image;
            tsddbShapeOptions.Image = Resources.layer__pencil;
            tsMain.Items.Add(tsddbShapeOptions);

            tslnudMagnifyStrength = new ToolStripLabeledNumericUpDown("放大:");
            tslnudMagnifyStrength.Content.Text2 = "%";
            tslnudMagnifyStrength.Content.Minimum = 100;
            tslnudMagnifyStrength.Content.Maximum = 1000;
            tslnudMagnifyStrength.Content.Increment = 100;
            tslnudMagnifyStrength.Content.ValueChanged = (sender, e) =>
            {
                AnnotationOptions.MagnifyStrength = (int)tslnudMagnifyStrength.Content.Value;
                UpdateCurrentShape();
            };
            tsddbShapeOptions.DropDownItems.Add(tslnudMagnifyStrength);

            tslnudBorderSize = new ToolStripLabeledNumericUpDown("边框尺寸：");
            tslnudBorderSize.Content.Minimum = 0;
            tslnudBorderSize.Content.Maximum = 20;
            tslnudBorderSize.Content.ValueChanged = (sender, e) =>
            {
                ShapeType shapeType = CurrentShapeTool;

                int borderSize = (int)tslnudBorderSize.Content.Value;

                if (shapeType == ShapeType.文字背景填充 || shapeType == ShapeType.气泡)
                {
                    AnnotationOptions.TextBorderSize = borderSize;
                }
                else if (shapeType == ShapeType.文字描边)
                {
                    AnnotationOptions.TextOutlineBorderSize = borderSize;
                }
                else if (shapeType == ShapeType.序号)
                {
                    AnnotationOptions.StepBorderSize = borderSize;
                }
                else
                {
                    AnnotationOptions.BorderSize = borderSize;
                }

                UpdateCurrentShape();
            };
            tsddbShapeOptions.DropDownItems.Add(tslnudBorderSize);

            tslnudCornerRadius = new ToolStripLabeledNumericUpDown("圆角：");
            tslnudCornerRadius.Content.Minimum = 0;
            tslnudCornerRadius.Content.Maximum = 150;
            tslnudCornerRadius.Content.ValueChanged = (sender, e) =>
            {
                ShapeType shapeType = CurrentShapeTool;

                if (shapeType == ShapeType.矩形区域)
                {
                    AnnotationOptions.RegionCornerRadius = (int)tslnudCornerRadius.Content.Value;
                }
                else if (shapeType == ShapeType.矩形 || shapeType == ShapeType.文字背景填充)
                {
                    AnnotationOptions.DrawingCornerRadius = (int)tslnudCornerRadius.Content.Value;
                }

                UpdateCurrentShape();
            };
            tsddbShapeOptions.DropDownItems.Add(tslnudCornerRadius);

            tscbBorderStyle = new ToolStripLabeledComboBox("边框样式");
            tscbBorderStyle.Content.AddRange(Enum.GetNames(typeof(BorderStyle)));
            tscbBorderStyle.Content.SelectedIndexChanged += (sender, e) =>
            {
                AnnotationOptions.BorderStyle = (BorderStyle)tscbBorderStyle.Content.SelectedIndex;
                tscbBorderStyle.Invalidate();
                UpdateCurrentShape();
            };
            tsddbShapeOptions.DropDownItems.Add(tscbBorderStyle);

            tscbImageInterpolationMode = new ToolStripLabeledComboBox("插值模式:");
            tscbImageInterpolationMode.Content.AddRange(Enum.GetNames(typeof(ImageInterpolationMode)));
            tscbImageInterpolationMode.Content.SelectedIndexChanged += (sender, e) =>
            {
                AnnotationOptions.ImageInterpolationMode = (ImageInterpolationMode)tscbImageInterpolationMode.Content.SelectedIndex;
                tscbImageInterpolationMode.Invalidate();
                UpdateCurrentShape();
            };
            tsddbShapeOptions.DropDownItems.Add(tscbImageInterpolationMode);

            tslnudPixelateSize = new ToolStripLabeledNumericUpDown("像素尺寸:");
            tslnudPixelateSize.Content.Minimum = 2;
            tslnudPixelateSize.Content.Maximum = 10000;
            tslnudPixelateSize.Content.ValueChanged = (sender, e) =>
            {
                AnnotationOptions.PixelateSize = (int)tslnudPixelateSize.Content.Value;
                UpdateCurrentShape();
            };
            tsddbShapeOptions.DropDownItems.Add(tslnudPixelateSize);

            tslnudCenterPoints = new ToolStripLabeledNumericUpDown("中心点:");
            tslnudCenterPoints.Content.Minimum = 0;
            tslnudCenterPoints.Content.Maximum = LineDrawingShape.MaximumCenterPointCount;
            tslnudCenterPoints.Content.ValueChanged = (sender, e) =>
            {
                AnnotationOptions.LineCenterPointCount = (int)tslnudCenterPoints.Content.Value;
                UpdateCurrentShape();
            };
            tsddbShapeOptions.DropDownItems.Add(tslnudCenterPoints);

            tscbArrowHeadDirection = new ToolStripLabeledComboBox("箭头方向:");
            tscbArrowHeadDirection.Content.AddRange(Enum.GetNames(typeof(ArrowHeadDirection)));
            tscbArrowHeadDirection.Content.SelectedIndexChanged += (sender, e) =>
            {
                AnnotationOptions.ArrowHeadDirection = (ArrowHeadDirection)tscbArrowHeadDirection.Content.SelectedIndex;
                tscbArrowHeadDirection.Invalidate();
                UpdateCurrentShape();
            };
            tsddbShapeOptions.DropDownItems.Add(tscbArrowHeadDirection);

            tslnudStepFontSize = new ToolStripLabeledNumericUpDown("字号：");
            tslnudStepFontSize.Content.Minimum = 10;
            tslnudStepFontSize.Content.Maximum = 100;
            tslnudStepFontSize.Content.ValueChanged = (sender, e) =>
            {
                AnnotationOptions.StepFontSize = (int)tslnudStepFontSize.Content.Value;
                UpdateCurrentShape();
            };
            tsddbShapeOptions.DropDownItems.Add(tslnudStepFontSize);

            tslnudStartingStepValue = new ToolStripLabeledNumericUpDown("初始值：");
            tslnudStartingStepValue.Content.Minimum = 1;
            tslnudStartingStepValue.Content.Maximum = 10000;
            tslnudStartingStepValue.Content.ValueChanged = (sender, e) =>
            {
                StartingStepNumber = (int)tslnudStartingStepValue.Content.Value;
                UpdateCurrentShape();
            };
            tsddbShapeOptions.DropDownItems.Add(tslnudStartingStepValue);

            tscbStepType = new ToolStripLabeledComboBox("类型:");
            tscbStepType.Content.AddRange(Enum.GetNames(typeof(StepType)));
            tscbStepType.Content.SelectedIndexChanged += (sender, e) =>
            {
                AnnotationOptions.StepType = (StepType)tscbStepType.Content.SelectedIndex;
                tscbStepType.Invalidate();
                UpdateCurrentShape();
            };
            tsddbShapeOptions.DropDownItems.Add(tscbStepType);

            tsmiShadow = new ToolStripMenuItem("阴影");
            tsmiShadow.Checked = true;
            tsmiShadow.CheckOnClick = true;
            tsmiShadow.Click += (sender, e) =>
            {
                AnnotationOptions.Shadow = tsmiShadow.Checked;
                UpdateCurrentShape();
            };
            tsddbShapeOptions.DropDownItems.Add(tsmiShadow);

            tsmiShadowColor = new ToolStripMenuItem("阴影颜色...");
            tsmiShadowColor.Click += (sender, e) =>
            {
                Form.Pause();

                if (PickColor(AnnotationOptions.ShadowColor, out Color newColor))
                {
                    AnnotationOptions.ShadowColor = newColor;
                    UpdateMenu();
                    UpdateCurrentShape();
                }

                Form.Resume();
            };
            tsddbShapeOptions.DropDownItems.Add(tsmiShadowColor);

            // In dropdown menu if only last item is visible then menu opens at 0, 0 position on first open, so need to add dummy item to solve this weird bug...
            tsddbShapeOptions.DropDownItems.Add(new ToolStripSeparator() { Visible = false });

            #endregion Shape options

            #region Edit

            ToolStripDropDownButton tsddbEdit = new ToolStripDropDownButton("编辑");
            tsddbEdit.DisplayStyle = ToolStripItemDisplayStyle.Image;
            tsddbEdit.Image = Resources.wrench_screwdriver;
            tsMain.Items.Add(tsddbEdit);

            tsmiUndo = new ToolStripMenuItem("撤销");
            tsmiUndo.Image = Resources.arrow_circle_225_left;
            tsmiUndo.ShortcutKeyDisplayString = "Ctrl+Z";
            tsmiUndo.Click += (sender, e) => UndoShape();
            tsddbEdit.DropDownItems.Add(tsmiUndo);

            ToolStripMenuItem tsmiPaste = new ToolStripMenuItem("粘贴图像/文本");
            tsmiPaste.Image = Resources.clipboard;
            tsmiPaste.ShortcutKeyDisplayString = "Ctrl+V";
            tsmiPaste.Click += (sender, e) => PasteFromClipboard(false);
            tsddbEdit.DropDownItems.Add(tsmiPaste);

            tsmiDuplicate = new ToolStripMenuItem("重复");
            tsmiDuplicate.Image = Resources.document_copy;
            tsmiDuplicate.ShortcutKeyDisplayString = "Ctrl+D";
            tsmiDuplicate.Click += (sender, e) => DuplicateCurrrentShape(false);
            tsddbEdit.DropDownItems.Add(tsmiDuplicate);

            tsddbEdit.DropDownItems.Add(new ToolStripSeparator());

            tsmiDelete = new ToolStripMenuItem("删除");
            tsmiDelete.Image = Resources.layer__minus;
            tsmiDelete.ShortcutKeyDisplayString = "Del";
            tsmiDelete.Click += (sender, e) => DeleteCurrentShape();
            tsddbEdit.DropDownItems.Add(tsmiDelete);

            tsmiDeleteAll = new ToolStripMenuItem("删除所有");
            tsmiDeleteAll.Image = Resources.eraser;
            tsmiDeleteAll.ShortcutKeyDisplayString = "Shift+Del";
            tsmiDeleteAll.Click += (sender, e) => DeleteAllShapes();
            tsddbEdit.DropDownItems.Add(tsmiDeleteAll);

            tsddbEdit.DropDownItems.Add(new ToolStripSeparator());

            tsmiMoveTop = new ToolStripMenuItem("置顶");
            tsmiMoveTop.Image = Resources.layers_stack_arrange;
            tsmiMoveTop.ShortcutKeyDisplayString = "Home";
            tsmiMoveTop.Click += (sender, e) => MoveCurrentShapeTop();
            tsddbEdit.DropDownItems.Add(tsmiMoveTop);

            tsmiMoveUp = new ToolStripMenuItem("置底");
            tsmiMoveUp.Image = Resources.layers_arrange;
            tsmiMoveUp.ShortcutKeyDisplayString = "Page up";
            tsmiMoveUp.Click += (sender, e) => MoveCurrentShapeUp();
            tsddbEdit.DropDownItems.Add(tsmiMoveUp);

            tsmiMoveDown = new ToolStripMenuItem("向上");
            tsmiMoveDown.Image = Resources.layers_arrange_back;
            tsmiMoveDown.ShortcutKeyDisplayString = "Page down";
            tsmiMoveDown.Click += (sender, e) => MoveCurrentShapeDown();
            tsddbEdit.DropDownItems.Add(tsmiMoveDown);

            tsmiMoveBottom = new ToolStripMenuItem("向下");
            tsmiMoveBottom.Image = Resources.layers_stack_arrange_back;
            tsmiMoveBottom.ShortcutKeyDisplayString = "End";
            tsmiMoveBottom.Click += (sender, e) => MoveCurrentShapeBottom();
            tsddbEdit.DropDownItems.Add(tsmiMoveBottom);

            #endregion Edit

            tsMain.Items.Add(new ToolStripSeparator());

            #region Options

            ToolStripDropDownButton tsddbOptions = new ToolStripDropDownButton("设置");
            tsddbOptions.DisplayStyle = ToolStripItemDisplayStyle.Image;
            tsddbOptions.Image = Resources.gear;
            tsMain.Items.Add(tsddbOptions);

            tsmiQuickCrop = new ToolStripMenuItem("多区域");
            tsmiQuickCrop.Checked = !Options.QuickCrop;
            tsmiQuickCrop.CheckOnClick = true;
            tsmiQuickCrop.Click += (sender, e) => Options.QuickCrop = !tsmiQuickCrop.Checked;
            tsddbOptions.DropDownItems.Add(tsmiQuickCrop);

            ToolStripMenuItem tsmiShowInfo = new ToolStripMenuItem("显示位置及大小");
            tsmiShowInfo.Checked = Options.ShowInfo;
            tsmiShowInfo.CheckOnClick = true;
            tsmiShowInfo.Click += (sender, e) => Options.ShowInfo = tsmiShowInfo.Checked;
            tsddbOptions.DropDownItems.Add(tsmiShowInfo);

            tsmiShowMagnifier = new ToolStripMenuItem("显示放大镜");
            tsmiShowMagnifier.Checked = Options.ShowMagnifier;
            tsmiShowMagnifier.CheckOnClick = true;
            tsmiShowMagnifier.Click += (sender, e) => Options.ShowMagnifier = tsmiShowMagnifier.Checked;
            tsddbOptions.DropDownItems.Add(tsmiShowMagnifier);

            ToolStripMenuItem tsmiUseSquareMagnifier = new ToolStripMenuItem("方形放大镜");
            tsmiUseSquareMagnifier.Checked = Options.UseSquareMagnifier;
            tsmiUseSquareMagnifier.CheckOnClick = true;
            tsmiUseSquareMagnifier.Click += (sender, e) => Options.UseSquareMagnifier = tsmiUseSquareMagnifier.Checked;
            tsddbOptions.DropDownItems.Add(tsmiUseSquareMagnifier);

            tslnudMagnifierPixelCount = new ToolStripLabeledNumericUpDown("放大镜像素个数");
            tslnudMagnifierPixelCount.Content.Minimum = RegionCaptureOptions.MagnifierPixelCountMinimum;
            tslnudMagnifierPixelCount.Content.Maximum = RegionCaptureOptions.MagnifierPixelCountMaximum;
            tslnudMagnifierPixelCount.Content.Increment = 2;
            tslnudMagnifierPixelCount.Content.Value = Options.MagnifierPixelCount;
            tslnudMagnifierPixelCount.Content.ValueChanged = (sender, e) => Options.MagnifierPixelCount = (int)tslnudMagnifierPixelCount.Content.Value;
            tsddbOptions.DropDownItems.Add(tslnudMagnifierPixelCount);

            ToolStripLabeledNumericUpDown tslnudMagnifierPixelSize = new ToolStripLabeledNumericUpDown("放大镜像素大小");
            tslnudMagnifierPixelSize.Content.Minimum = RegionCaptureOptions.MagnifierPixelSizeMinimum;
            tslnudMagnifierPixelSize.Content.Maximum = RegionCaptureOptions.MagnifierPixelSizeMaximum;
            tslnudMagnifierPixelSize.Content.Value = Options.MagnifierPixelSize;
            tslnudMagnifierPixelSize.Content.ValueChanged = (sender, e) => Options.MagnifierPixelSize = (int)tslnudMagnifierPixelSize.Content.Value;
            tsddbOptions.DropDownItems.Add(tslnudMagnifierPixelSize);

            ToolStripMenuItem tsmiShowCrosshair = new ToolStripMenuItem("显示屏幕宽十字线");
            tsmiShowCrosshair.Checked = Options.ShowCrosshair;
            tsmiShowCrosshair.CheckOnClick = true;
            tsmiShowCrosshair.Click += (sender, e) => Options.ShowCrosshair = tsmiShowCrosshair.Checked;
            tsddbOptions.DropDownItems.Add(tsmiShowCrosshair);

            ToolStripMenuItem tsmiEnableAnimations = new ToolStripMenuItem("动效");
            tsmiEnableAnimations.Checked = Options.EnableAnimations;
            tsmiEnableAnimations.CheckOnClick = true;
            tsmiEnableAnimations.Click += (sender, e) => Options.EnableAnimations = tsmiEnableAnimations.Checked;
            tsddbOptions.DropDownItems.Add(tsmiEnableAnimations);

            //if (!Form.IsEditorMode)
            //{
            //    ToolStripMenuItem tsmiFixedSize = new ToolStripMenuItem("固定尺寸区域模式");
            //    tsmiFixedSize.Checked = Options.IsFixedSize;
            //    tsmiFixedSize.CheckOnClick = true;
            //    tsmiFixedSize.Click += (sender, e) => Options.IsFixedSize = tsmiFixedSize.Checked;
            //    tsddbOptions.DropDownItems.Add(tsmiFixedSize);

            //    ToolStripDoubleLabeledNumericUpDown tslnudFixedSize = new ToolStripDoubleLabeledNumericUpDown("宽度:", "高度:");
            //    tslnudFixedSize.Content.Minimum = 10;
            //    tslnudFixedSize.Content.Maximum = 10000;
            //    tslnudFixedSize.Content.Increment = 10;
            //    tslnudFixedSize.Content.Value = Options.FixedSize.Width;
            //    tslnudFixedSize.Content.Value2 = Options.FixedSize.Height;
            //    tslnudFixedSize.Content.ValueChanged = (sender, e) => Options.FixedSize = new Size((int)tslnudFixedSize.Content.Value, (int)tslnudFixedSize.Content.Value2);
            //    tsddbOptions.DropDownItems.Add(tslnudFixedSize);
            //}

            ToolStripMenuItem tsmiSwitchToDrawingToolAfterSelection = new ToolStripMenuItem("选择形状后切换到绘图工具");
            tsmiSwitchToDrawingToolAfterSelection.Checked = Options.SwitchToDrawingToolAfterSelection;
            tsmiSwitchToDrawingToolAfterSelection.CheckOnClick = true;
            tsmiSwitchToDrawingToolAfterSelection.Click += (sender, e) =>
            {
                Options.SwitchToDrawingToolAfterSelection = tsmiSwitchToDrawingToolAfterSelection.Checked;
            };
            tsddbOptions.DropDownItems.Add(tsmiSwitchToDrawingToolAfterSelection);

            ToolStripMenuItem tsmiSwitchToSelectionToolAfterDrawing = new ToolStripMenuItem("图形绘制后切换到选择工具");
            tsmiSwitchToSelectionToolAfterDrawing.Checked = Options.SwitchToSelectionToolAfterDrawing;
            tsmiSwitchToSelectionToolAfterDrawing.CheckOnClick = true;
            tsmiSwitchToSelectionToolAfterDrawing.Click += (sender, e) =>
            {
                Options.SwitchToSelectionToolAfterDrawing = tsmiSwitchToSelectionToolAfterDrawing.Checked;
            };
            tsddbOptions.DropDownItems.Add(tsmiSwitchToSelectionToolAfterDrawing);

            tsddbOptions.DropDownItems.Add(new ToolStripSeparator());
            #endregion Options

            #region Editor mode

            ToolStripButton tsbSaveImageAs = new ToolStripButton("另存为… (Ctrl + Shift + S)")
            {
                DisplayStyle = ToolStripItemDisplayStyle.Image,
                Image = CommonSetting.深色模式 ? Resources.disks_black_white : Resources.disks_black
            };
            tsbSaveImageAs.Click += (sender, e) => Form.OnSaveImageAsRequested();
            tsMain.Items.Add(tsbSaveImageAs);

            ToolStripButton tsbCopyImage = new ToolStripButton("复制到剪贴板")
            {
                DisplayStyle = ToolStripItemDisplayStyle.Image,
                Image = Resources.clipboard
            };
            tsbCopyImage.Click += (sender, e) => Form.OnCopyImageRequested();
            tsMain.Items.Add(tsbCopyImage);

            ToolStripButton tsbCompleteEdit = new ToolStripButton();

            tsbCompleteEdit.DisplayStyle = ToolStripItemDisplayStyle.Image;
            tsbCompleteEdit.Image = CommonSetting.深色模式 ? Resources.finish_white : Resources.finish;
            tsbCompleteEdit.Click += (sender, e) => Form.CloseWindow(RegionResult.Region);
            tsMain.Items.Add(tsbCompleteEdit);

            tsMain.Items.Add(new ToolStripSeparator());

            #endregion Editor mode

            if (NativeMethods.IsTabletMode())
            {
                ToolStripButton tsbClose = new ToolStripButton("关闭 (Esc)");
                tsbClose.DisplayStyle = ToolStripItemDisplayStyle.Image;
                tsbClose.Image = ImageHelp.CreateClose(Color.Red);
                tsbClose.Click += (sender, e) => Form.CloseWindow();
                tsMain.Items.Add(tsbClose);

                tsMain.Items.Add(new ToolStripSeparator());
            }

            tslDragRight = new ToolStripLabel()
            {
                Alignment = ToolStripItemAlignment.Right,
                DisplayStyle = ToolStripItemDisplayStyle.Image,
                Image = ImageHelp.DrawGrip(Color.Black, Color.AliceBlue),
                Margin = new Padding(0, 0, 2, 0),
                Padding = new Padding(2),
                Visible = true
            };

            tsMain.Items.Add(tslDragRight);

            tslDragLeft.MouseDown += TslDrag_MouseDown;
            tslDragRight.MouseDown += TslDrag_MouseDown;
            tslDragLeft.MouseEnter += TslDrag_MouseEnter;
            tslDragRight.MouseEnter += TslDrag_MouseEnter;
            tslDragLeft.MouseLeave += TslDrag_MouseLeave;
            tslDragRight.MouseLeave += TslDrag_MouseLeave;

            foreach (ToolStripItem tsi in tsMain.Items.OfType<ToolStripItem>())
            {
                if (!string.IsNullOrEmpty(tsi.Text))
                {
                    tsi.MouseEnter += (sender, e) =>
                    {
                        Point pos = Form.PointToClient(menuForm.PointToScreen(tsi.Bounds.Location));
                        pos.Y += tsi.Height + 8;

                        MenuTextAnimation.Text = tsi.Text;
                        MenuTextAnimation.Position = pos;
                        MenuTextAnimation.Start();
                    };

                    tsi.MouseLeave += TsMain_MouseLeave;
                }

                tsi.Padding = new Padding(4);
            }

            tsMain.ResumeLayout(false);
            tsMain.PerformLayout();
            menuForm.ResumeLayout(false);

            menuForm.Show(Form);

            UpdateMenu();

            CurrentShapeChanged += shape => UpdateMenu();
            CurrentShapeTypeChanged += shapeType => UpdateMenu();
            ShapeCreated += shape => UpdateMenu();

            ConfigureMenuState();

            Form.Activate();

            ToolbarCreated = true;
        }

        private void MenuForm_Shown(object sender, EventArgs e)
        {
            Form.ToolbarHeight = menuForm.Height;
            Form.CenterCanvas();
        }

        private void MenuForm_KeyDown(object sender, KeyEventArgs e)
        {
            form_KeyDown(sender, e);
            Form.RegionCaptureForm_KeyDown(sender, e);

            e.Handled = true;
        }

        private void MenuForm_KeyUp(object sender, KeyEventArgs e)
        {
            form_KeyUp(sender, e);

            e.Handled = true;
        }

        private void MenuForm_LocationChanged(object sender, EventArgs e)
        {
            CheckMenuPosition();
        }

        private void MenuForm_GotFocus(object sender, EventArgs e)
        {
            Form.Resume();
        }

        private void MenuForm_LostFocus(object sender, EventArgs e)
        {
            Form.Pause();
        }

        private void TsMain_MouseLeave(object sender, EventArgs e)
        {
            MenuTextAnimation.Stop();
        }

        private void TslDrag_MouseEnter(object sender, EventArgs e)
        {
            menuForm.Cursor = Cursors.SizeAll;
        }

        private void TslDrag_MouseLeave(object sender, EventArgs e)
        {
            menuForm.Cursor = Cursors.Default;
        }

        private void TslDrag_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                WinApi.ReleaseCapture();
                NativeMethods.DefWindowProc(menuForm.Handle, (uint)0x0112, (UIntPtr)0xF012, IntPtr.Zero);
            }
            else if (e.Button == MouseButtons.Right)
            {
                SetMenuCollapsed(!ToolbarCollapsed);
                CheckMenuPosition();
            }
        }

        private void ConfigureMenuState()
        {
            UpdateMenuPosition();
        }

        internal void UpdateMenuPosition()
        {
            Rectangle rectScreen = NativeMethods.GetActiveScreenBounds();

            if (tsMain.Width < rectScreen.Width)
            {
                menuForm.Location = new Point(rectScreen.X + (rectScreen.Width / 2) - (tsMain.Width / 2), rectScreen.Y);
            }
            else
            {
                menuForm.Location = rectScreen.Location;
            }
        }

        internal void UpdateMenuMaxWidth(int width)
        {
            tsMain.MaximumSize = new Size(width, 0);
        }

        private void CheckMenuPosition()
        {
            Rectangle rectMenu = menuForm.Bounds;
            Rectangle rectScreen = NativeMethods.GetScreenBounds();
            Point pos = rectMenu.Location;

            if (rectMenu.Width < rectScreen.Width)
            {
                if (rectMenu.X < rectScreen.X)
                {
                    pos.X = rectScreen.X;
                }
                else if (rectMenu.Right > rectScreen.Right)
                {
                    pos.X = rectScreen.Right - rectMenu.Width;
                }
            }

            if (rectMenu.Height < rectScreen.Height)
            {
                if (rectMenu.Y < rectScreen.Y)
                {
                    pos.Y = rectScreen.Y;
                }
                else if (rectMenu.Bottom > rectScreen.Bottom)
                {
                    pos.Y = rectScreen.Bottom - rectMenu.Height;
                }
            }

            if (pos != rectMenu.Location)
            {
                menuForm.Location = pos;
            }
        }

        private void SetMenuCollapsed(bool isCollapsed)
        {
            if (ToolbarCollapsed == isCollapsed)
            {
                return;
            }

            ToolbarCollapsed = isCollapsed;

            if (ToolbarCollapsed)
            {
                foreach (ToolStripItem tsi in tsMain.Items.OfType<ToolStripItem>())
                {
                    if (tsi == tslDragLeft)
                    {
                        continue;
                    }

                    tsi.Visible = false;
                }
            }
            else
            {
                foreach (ToolStripItem tsi in tsMain.Items.OfType<ToolStripItem>())
                {
                    tsi.Visible = true;
                }

                UpdateMenu();
            }
        }

        private void UpdateMenu()
        {
            if (Form.IsClosing || menuForm == null || menuForm.IsDisposed) return;

            ShapeType shapeType = CurrentTool;

            foreach (ToolStripButton tsb in tsMain.Items.OfType<ToolStripButton>().Where(x => x.Tag is ShapeType))
            {
                if ((ShapeType)tsb.Tag == shapeType)
                {
                    tsb.RadioCheck();
                    break;
                }
            }

            // use menu of current shape in case of an active select tool.
            shapeType = CurrentShapeTool;

            Color borderColor;

            if (shapeType == ShapeType.文字背景填充 || shapeType == ShapeType.气泡)
            {
                borderColor = AnnotationOptions.TextBorderColor;
            }
            else if (shapeType == ShapeType.文字描边)
            {
                borderColor = AnnotationOptions.TextOutlineBorderColor;
            }
            else if (shapeType == ShapeType.序号)
            {
                borderColor = AnnotationOptions.StepBorderColor;
            }
            else
            {
                borderColor = AnnotationOptions.BorderColor;
            }

            if (tsbBorderColor.Image != null) tsbBorderColor.Image.Dispose();
            tsbBorderColor.Image = ImageHelp.CreateColorPickerIcon(borderColor, new Rectangle(0, 0, 16, 16), 8);

            int borderSize;

            if (shapeType == ShapeType.文字背景填充 || shapeType == ShapeType.气泡)
            {
                borderSize = AnnotationOptions.TextBorderSize;
            }
            else if (shapeType == ShapeType.文字描边)
            {
                borderSize = AnnotationOptions.TextOutlineBorderSize;
            }
            else if (shapeType == ShapeType.序号)
            {
                borderSize = AnnotationOptions.StepBorderSize;
            }
            else
            {
                borderSize = AnnotationOptions.BorderSize;
            }

            tslnudBorderSize.Content.Value = borderSize;

            Color fillColor;

            if (shapeType == ShapeType.文字背景填充 || shapeType == ShapeType.气泡)
            {
                fillColor = AnnotationOptions.TextFillColor;
            }
            else if (shapeType == ShapeType.序号)
            {
                fillColor = AnnotationOptions.StepFillColor;
            }
            else
            {
                fillColor = AnnotationOptions.FillColor;
            }

            if (tsbFillColor.Image != null) tsbFillColor.Image.Dispose();
            tsbFillColor.Image = ImageHelp.CreateColorPickerIcon(fillColor, new Rectangle(0, 0, 16, 16));

            int cornerRadius = 0;

            if (shapeType == ShapeType.矩形区域)
            {
                cornerRadius = AnnotationOptions.RegionCornerRadius;
            }
            else if (shapeType == ShapeType.矩形 || shapeType == ShapeType.文字背景填充)
            {
                cornerRadius = AnnotationOptions.DrawingCornerRadius;
            }

            tslnudCornerRadius.Content.Value = cornerRadius;

            tscbBorderStyle.Content.SelectedIndex = (int)AnnotationOptions.BorderStyle;

            tscbImageInterpolationMode.Content.SelectedIndex = (int)AnnotationOptions.ImageInterpolationMode;

            tslnudPixelateSize.Content.Value = AnnotationOptions.PixelateSize;

            if (tsbHighlightColor.Image != null) tsbHighlightColor.Image.Dispose();
            tsbHighlightColor.Image = ImageHelp.CreateColorPickerIcon(AnnotationOptions.HighlightColor, new Rectangle(0, 0, 16, 16));

            tslnudStepFontSize.Content.Value = AnnotationOptions.StepFontSize;
            tslnudStartingStepValue.Content.Value = StartingStepNumber;
            tscbStepType.Content.SelectedIndex = (int)AnnotationOptions.StepType;

            tslnudMagnifyStrength.Content.Value = AnnotationOptions.MagnifyStrength;

            tsmiShadow.Checked = AnnotationOptions.Shadow;

            if (tsmiShadowColor.Image != null) tsmiShadowColor.Image.Dispose();
            tsmiShadowColor.Image = ImageHelp.CreateColorPickerIcon(AnnotationOptions.ShadowColor, new Rectangle(0, 0, 16, 16));

            tslnudCenterPoints.Content.Value = AnnotationOptions.LineCenterPointCount;

            tscbArrowHeadDirection.Content.SelectedIndex = (int)AnnotationOptions.ArrowHeadDirection;

            switch (shapeType)
            {
                default:
                    tsddbShapeOptions.Visible = false;
                    break;
                case ShapeType.矩形区域:
                case ShapeType.矩形:
                case ShapeType.圆形:
                case ShapeType.画笔:
                case ShapeType.箭头:
                case ShapeType.文字描边:
                case ShapeType.文字背景填充:
                case ShapeType.气泡:
                case ShapeType.序号:
                case ShapeType.放大镜:
                case ShapeType.马赛克:
                    tsddbShapeOptions.Visible = true;
                    break;
            }

            tsmiUndo.Enabled = tsmiDeleteAll.Enabled = Shapes.Count > 0;
            tsmiDuplicate.Enabled = tsmiDelete.Enabled = tsmiMoveTop.Enabled = tsmiMoveUp.Enabled = tsmiMoveDown.Enabled = tsmiMoveBottom.Enabled = CurrentShape != null;

            switch (shapeType)
            {
                default:
                    tsbBorderColor.Visible = false;
                    tslnudBorderSize.Visible = false;
                    tsmiShadow.Visible = false;
                    tsmiShadowColor.Visible = false;
                    break;
                case ShapeType.矩形:
                case ShapeType.圆形:
                case ShapeType.画笔:
                case ShapeType.箭头:
                case ShapeType.文字描边:
                case ShapeType.文字背景填充:
                case ShapeType.气泡:
                case ShapeType.序号:
                case ShapeType.放大镜:
                    tsbBorderColor.Visible = true;
                    tslnudBorderSize.Visible = true;
                    tsmiShadow.Visible = true;
                    tsmiShadowColor.Visible = true;
                    break;
            }

            switch (shapeType)
            {
                default:
                    tsbFillColor.Visible = false;
                    break;
                case ShapeType.矩形:
                case ShapeType.圆形:
                case ShapeType.文字背景填充:
                case ShapeType.气泡:
                case ShapeType.序号:
                case ShapeType.放大镜:
                    tsbFillColor.Visible = true;
                    break;
            }

            switch (shapeType)
            {
                default:
                    tslnudCornerRadius.Visible = false;
                    break;
                case ShapeType.矩形区域:
                case ShapeType.矩形:
                case ShapeType.文字背景填充:
                    tslnudCornerRadius.Visible = true;
                    break;
            }

            switch (shapeType)
            {
                default:
                    tscbBorderStyle.Visible = false;
                    break;
                case ShapeType.矩形:
                case ShapeType.圆形:
                case ShapeType.画笔:
                case ShapeType.箭头:
                case ShapeType.放大镜:
                    tscbBorderStyle.Visible = true;
                    break;
            }

            tslnudCenterPoints.Visible = shapeType == ShapeType.箭头;
            tscbArrowHeadDirection.Visible = shapeType == ShapeType.箭头;
            tscbImageInterpolationMode.Visible = shapeType == ShapeType.放大镜;
            tslnudStartingStepValue.Visible = shapeType == ShapeType.序号;
            tslnudStepFontSize.Visible = tscbStepType.Visible = shapeType == ShapeType.序号;
            tslnudMagnifyStrength.Visible = shapeType == ShapeType.放大镜;
            tslnudPixelateSize.Visible = shapeType == ShapeType.马赛克;
            tsbHighlightColor.Visible = shapeType == ShapeType.高亮;

            if (tsmiRegionCapture != null)
            {
                tsmiRegionCapture.Visible = !Options.QuickCrop && ValidRegions.Length > 0;
            }
        }

        public void ShowMenuTooltip(string text)
        {
            MenuTextAnimation.Text = text;
            MenuTextAnimation.Start();
        }
    }
}