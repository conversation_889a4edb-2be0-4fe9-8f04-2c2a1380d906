﻿using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace OCRTools.UserControlEx
{
	[ToolboxItem(false)]
	public class TabStyleIE8Provider : TabStyleRoundedProvider
	{
		public TabStyleIE8Provider(TabControlExtra tabControl)
			: base(tabControl)
		{
			base.Radius = 3;
			base.ShowTabCloser = true;
			base.SelectedTabIsLarger = true;
			base.CloserColorFocusedActive = Color.Red;
			base.CloserColorFocused = Color.Black;
			base.CloserColorSelected = Color.Black;
			base.CloserColorHighlighted = Color.Black;
			base.CloserColorUnselected = Color.Empty;
			base.CloserButtonFillColorFocusedActive = Color.White;
			base.CloserButtonFillColorFocused = Color.Empty;
			base.CloserButtonFillColorSelected = Color.Empty;
			base.CloserButtonFillColorHighlighted = Color.Empty;
			base.CloserButtonFillColorUnselected = Color.Empty;
			base.CloserButtonOutlineColorFocusedActive = SystemColors.ControlDark;
			base.CloserButtonOutlineColorFocused = Color.Empty;
			base.CloserButtonOutlineColorSelected = Color.Empty;
			base.CloserButtonOutlineColorHighlighted = Color.Empty;
			base.CloserButtonOutlineColorUnselected = Color.Empty;
			base.PageBackgroundColorDisabled = Color.FromArgb(247, 247, 255);
			base.PageBackgroundColorFocused = Color.FromArgb(247, 247, 255);
			base.PageBackgroundColorHighlighted = Color.FromArgb(247, 247, 255);
			base.PageBackgroundColorSelected = Color.FromArgb(247, 247, 255);
			base.PageBackgroundColorUnselected = Color.FromArgb(198, 223, 255);
			base.TabColorFocused2 = Color.FromArgb(198, 223, 255);
			base.TabColorHighLighted2 = Color.FromArgb(198, 223, 255);
			base.TabColorSelected2 = Color.FromArgb(198, 223, 255);
			base.Padding = new Point(6, 5);
			base.TabPageMargin = new Padding(0, 4, 0, 4);
		}

		protected internal override void PaintTabBackground(GraphicsPath tabBorder, TabState state, Graphics graphics)
		{
			RectangleF bounds = tabBorder.GetBounds();
			switch (base.TabControl.Alignment)
			{
				case TabAlignment.Bottom:
					bounds.X += 1f;
					bounds.Width -= 2f;
					bounds.Height -= 1f;
					break;
				case TabAlignment.Top:
					bounds.X += 1f;
					bounds.Width -= 2f;
					bounds.Y += 1f;
					break;
				case TabAlignment.Left:
					bounds.X += 1f;
					bounds.Width -= 1f;
					bounds.Y += 1f;
					bounds.Height -= 2f;
					break;
				case TabAlignment.Right:
					bounds.Width -= 1f;
					bounds.Y += 1f;
					bounds.Height -= 2f;
					break;
			}
			using (Pen pen = new Pen(Color.FromArgb(247, 247, 255)))
			{
				graphics.DrawPath(pen, tabBorder);
			}
			bounds.X += 1f;
			bounds.Width -= 2f;
			bounds.Y += 1f;
			bounds.Height -= 1f;
			base.PaintTabBackground(GetTabBorder(Rectangle.Round(bounds)), state, graphics);
		}
	}
}
