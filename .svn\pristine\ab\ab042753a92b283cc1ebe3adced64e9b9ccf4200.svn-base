﻿using System;
using System.Drawing;
using System.Threading;
using System.Windows.Forms;

namespace OCRTools
{
    public class MouseTracker
    {
        private readonly Thread _thread;

        public MouseTracker(Form form, int ticks = 50)
        {
            _thread = new Thread(() =>
            {
                while (true)
                {
                    if (form.IsHandleCreated)
                        form.Invoke(new MethodInvoker(() =>
                        {
                            //if (form.Bounds.Contains(Cursor.Position))
                            {
                                Position = form.PointToClient(Cursor.Position);
                                form.Invalidate();
                            }
                            Tick?.Invoke(this, EventArgs.Empty);
                        }));
                    Thread.Sleep(ticks);
                }
            })
            {
                IsBackground = true
            };
        }

        public Point Position { get; private set; }

        public event EventHandler Tick;

        public void Start()
        {
            _thread.Start();
        }

        public void Stop()
        {
            _thread.Abort();
        }
    }
}