// "Therefore those skilled at the unorthodox
// are infinite as heaven and earth,
// inexhaustible as the great rivers.
// When they come to an end,
// they begin again,
// like the days and months;
// they die and are reborn,
// like the four seasons."
// 
// - <PERSON>,
// "The Art of War"

namespace OCRTools.HtmlRenderer.Core.Utils
{
    /// <summary>
    /// String constants to avoid typing errors.
    /// </summary>
    internal static class CssConstants
    {
        public const string Absolute = "absolute";
        public const string Auto = "auto";
        public const string Avoid = "avoid";
        public const string Baseline = "baseline";
        public const string Blink = "blink";
        public const string Block = "block";
        public const string InlineBlock = "inline-block";
        public const string Bold = "bold";
        public const string Bolder = "bolder";
        public const string Bottom = "bottom";
        public const string BreakAll = "break-all";
        public const string KeepAll = "keep-all";
        public const string Center = "center";
        public const string Collapse = "collapse";
        public const string Cursive = "cursive";
        public const string Circle = "circle";
        public const string Decimal = "decimal";
        public const string DecimalLeadingZero = "decimal-leading-zero";
        public const string Disc = "disc";
        public const string Fantasy = "fantasy";
        public const string Fixed = "fixed";
        public const string Hide = "hide";
        public const string Inherit = "inherit";
        public const string Inline = "inline";
        public const string InlineTable = "inline-table";
        public const string Inset = "inset";
        public const string Italic = "italic";
        public const string Justify = "justify";
        public const string Large = "large";
        public const string Larger = "larger";
        public const string Left = "left";
        public const string Lighter = "lighter";
        public const string LineThrough = "line-through";
        public const string ListItem = "list-item";
        public const string Ltr = "ltr";
        public const string LowerAlpha = "lower-alpha";
        public const string LowerLatin = "lower-latin";
        public const string LowerRoman = "lower-roman";
        public const string LowerGreek = "lower-greek";
        public const string Armenian = "armenian";
        public const string Georgian = "georgian";
        public const string Hebrew = "hebrew";
        public const string Hiragana = "hiragana";
        public const string HiraganaIroha = "hiragana-iroha";
        public const string Katakana = "katakana";
        public const string KatakanaIroha = "katakana-iroha";
        public const string Medium = "medium";
        public const string Middle = "middle";
        public const string Monospace = "monospace";
        public const string None = "none";
        public const string Normal = "normal";
        public const string NoWrap = "nowrap";
        public const string Oblique = "oblique";
        public const string Outset = "outset";
        public const string Overline = "overline";
        public const string Pre = "pre";
        public const string PreWrap = "pre-wrap";
        public const string PreLine = "pre-line";
        public const string Right = "right";
        public const string Rtl = "rtl";
        public const string SansSerif = "sans-serif";
        public const string Serif = "serif";
        public const string Show = "show";
        public const string Small = "small";
        public const string Smaller = "smaller";
        public const string Solid = "solid";
        public const string Sub = "sub";
        public const string Super = "super";
        public const string Square = "square";
        public const string Table = "table";
        public const string TableRow = "table-row";
        public const string TableRowGroup = "table-row-group";
        public const string TableHeaderGroup = "table-header-group";
        public const string TableFooterGroup = "table-footer-group";
        public const string TableColumn = "table-column";
        public const string TableColumnGroup = "table-column-group";
        public const string TableCell = "table-cell";
        public const string TableCaption = "table-caption";
        public const string TextBottom = "text-bottom";
        public const string TextTop = "text-top";
        public const string Thin = "thin";
        public const string Thick = "thick";
        public const string Top = "top";
        public const string Underline = "underline";
        public const string UpperAlpha = "upper-alpha";
        public const string UpperLatin = "upper-latin";
        public const string UpperRoman = "upper-roman";
        public const string XLarge = "x-large";
        public const string XSmall = "x-small";
        public const string XXLarge = "xx-large";
        public const string XXSmall = "xx-small";
        public const string Visible = "visible";
        public const string Hidden = "hidden";
        public const string Dotted = "dotted";
        public const string Dashed = "dashed";
        public const string Double = "double";
        public const string Groove = "groove";
        public const string Ridge = "ridge";

        /// <summary>
        /// Centimeters
        /// </summary>
        public const string Cm = "cm";

        /// <summary>
        /// Millimeters
        /// </summary>
        public const string Mm = "mm";

        /// <summary>
        /// Pixels
        /// </summary>
        public const string Px = "px";

        /// <summary>
        /// Inches
        /// </summary>
        public const string In = "in";

        /// <summary>
        /// Em - The font size of the relevant font
        /// </summary>
        public const string Em = "em";

        /// <summary>
        /// The 'x-height' of the relevan font
        /// </summary>
        public const string Ex = "ex";

        /// <summary>
        /// Points
        /// </summary>
        public const string Pt = "pt";

        /// <summary>
        /// Picas
        /// </summary>
        public const string Pc = "pc";

        /// <summary>
        /// Default font size in points. Change this value to modify the default font size.
        /// </summary>
        public const double FontSize = 11f;

        /// <summary>
        /// Default font used for the generic 'serif' family
        /// </summary>
        public const string DefaultFont = "Segoe UI";
    }
}