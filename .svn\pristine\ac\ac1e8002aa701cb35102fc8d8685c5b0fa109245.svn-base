﻿using OCRTools.Language;
using System;
using System.Drawing;
using System.Windows.Forms;
using System.Windows.Forms.VisualStyles;

namespace OCRTools.UserControlEx
{
    public class RadioWithTip : RadioButton
    {
        public RadioWithTip()
        {
            TextChanged += CheckBoxWithTip_TextChanged;
        }

        private void CheckBoxWithTip_TextChanged(object sender, EventArgs e)
        {
            InitPicBox();
        }

        private string tipText;

        public string TipText { get => tipText; set { tipText = value; InitPicBox(); } }

        public ToolTip TipControl { get; set; }
        public Bitmap TipIcon { get; set; }

        private PictureBox pictureBox;

        public bool LimitMaxWidth { get; set; } = true;

        private void InitPicBox()
        {
            if (string.IsNullOrEmpty(tipText) || TipControl == null)
            {
                return;
            }
            AutoSize = true;
            if (pictureBox == null)
            {
                pictureBox = new PictureBox
                {
                    BackColor = this.BackColor,
                    Image = TipIcon ?? Properties.Resources.帮助,
                    SizeMode = PictureBoxSizeMode.AutoSize,
                    TabStop = false,
                    Padding = new Padding(-3, 0, 0, 0)
                };
                pictureBox.Size = pictureBox.Image.Height > 20 ? new Size(25, 23) : new Size(Height, Height);
                pictureBox.Top = (Height + Padding.Top - pictureBox.Height);
                Controls.Add(pictureBox);
            }
            pictureBox.Left = TextRenderer.MeasureText(Text, Font).Width
                + CheckBoxRenderer.GetGlyphSize(Graphics.FromHwnd(IntPtr.Zero), CheckBoxState.UncheckedNormal).Width;
            AutoSize = false;
            Width = pictureBox.Left + pictureBox.Width;
            if (LimitMaxWidth && Parent != null)
            {
                var maxWidth = (Math.Max(Parent.PreferredSize.Width, this.Parent.Width) - 22 * 2) / 2;
                if (Width > maxWidth)
                {
                    pictureBox.Left -= Width - maxWidth;
                    this.Width = maxWidth;
                }
            }
            this.BringToFront();
            pictureBox.BringToFront();

            if (TipControl != null)
            {
                TipControl.SetToolTip(this, Text);
                TipControl.SetToolTip(pictureBox, "【" + Text + "】\n" + TipText.CurrentText());
            }
        }
    }
}
