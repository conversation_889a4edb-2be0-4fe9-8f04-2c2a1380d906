﻿using System.Drawing;
using System.Windows.Forms;
using MetroFramework.Forms;
using OCRTools.Common;

namespace OCRTools
{
    public partial class FrmPicCompare : MetroForm
    {
        public FrmPicCompare()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
        }

        internal void Init(SpiltMode spiltMode, bool isShowOld)
        {
            content.SpiltModel = spiltMode;
            content.IsShowOldContent = isShowOld;
        }

        internal void Bind(Image image, OcrContent ocrContent)
        {
            //content.FitZoom = 100;
            content.Image = image;
            content.NowDisplayMode = DisplayModel.图文模式;
            content.BindContentByOcr(ocrContent);
            Text = string.Format("图文对比-【{0}】", ocrContent.processName);
        }

        private void FrmPicCompare_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape) Close();
        }
    }
}