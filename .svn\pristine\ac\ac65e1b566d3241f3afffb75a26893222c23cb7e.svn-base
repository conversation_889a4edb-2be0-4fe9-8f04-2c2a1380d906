using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class UpDownButtonEx : PictureBox
    {
        public delegate void TextChangeHandler(object sender, EventArgs e);

        //private DrawArea drawAreaT;

        private readonly IContainer components = null;

        private UpDownButton Down;

        private Panel panel;

        private string text;

        private TextboxEx textBox1;

        private UpDownButton Up;

        public UpDownButtonEx()
        {
            SetStyle(ControlStyles.Selectable, true);
            InitializeComponent();
            textBox1.init(this);
        }

        public new string Text
        {
            get => text;
            set
            {
                text = value;
                if (textBox1 != null) textBox1.Text = text;
            }
        }

        public new event TextChangeHandler TextChanged;

        public event TextChangeHandler TextMouseLeave;

        private void textBox1_TextChanged(object sender, EventArgs e)
        {
        }

        public void Change()
        {
            if (TextChanged != null) TextChanged(null, EventArgs.Empty);
        }

        public void Mouse()
        {
            if (TextMouseLeave != null) TextMouseLeave(null, EventArgs.Empty);
        }

        private void Up_Click(object sender, EventArgs e)
        {
            textBox1.Text = (int.Parse(textBox1.Text) + 1).ToString();
        }

        private void Down_Click(object sender, EventArgs e)
        {
            textBox1.Text = (int.Parse(textBox1.Text) - 1).ToString();
        }

        private void customButton1_Paint(object sender, PaintEventArgs e)
        {
            var graphics = e.Graphics;
            var clipRectangle = e.ClipRectangle;
            clipRectangle = new Rectangle(clipRectangle.X, clipRectangle.Y, clipRectangle.Width - 1,
                clipRectangle.Height - 1);
            var pen = new Pen(Color.Red, 1f);
            graphics.DrawRectangle(pen, clipRectangle);
            pen.Dispose();
        }

        private void textBox1_MouseLeave(object sender, EventArgs e)
        {
            Mouse();
        }

        private void panel_Paint(object sender, PaintEventArgs e)
        {
            ControlPaint.DrawBorder(e.Graphics, panel.ClientRectangle, Color.FromArgb(192, 192, 192), 1,
                ButtonBorderStyle.Solid, Color.FromArgb(192, 192, 192), 1, ButtonBorderStyle.Solid,
                Color.FromArgb(192, 192, 192), 1, ButtonBorderStyle.Solid, Color.FromArgb(192, 192, 192), 1,
                ButtonBorderStyle.Solid);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null) components.Dispose();
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            Down = new UpDownButton();
            Up = new UpDownButton();
            textBox1 = new TextboxEx();
            panel = new Panel();
            panel.SuspendLayout();
            ((ISupportInitialize) this).BeginInit();
            SuspendLayout();
            Down.FlatStyle = FlatStyle.Flat;
            Down.Font = new Font("微软雅黑", 9f);
            Down.IsBorder = true;
            Down.IsUp = false;
            Down.ListItems = null;
            Down.Location = new Point(28, 18);
            Down.Name = "Down";
            Down.Size = new Size(20, 10);
            Down.TabIndex = 2;
            Down.TabStop = false;
            Down.UseVisualStyleBackColor = true;
            Down.Click += Down_Click;
            Up.FlatStyle = FlatStyle.Flat;
            Up.Font = new Font("微软雅黑", 9f);
            Up.IsBorder = true;
            Up.IsUp = true;
            Up.ListItems = null;
            Up.Location = new Point(28, 7);
            Up.Name = "Up";
            Up.Size = new Size(20, 10);
            Up.TabIndex = 1;
            Up.TabStop = false;
            Up.UseVisualStyleBackColor = true;
            Up.Click += Up_Click;
            Up.Paint += customButton1_Paint;
            textBox1.BackColor = Color.White;
            textBox1.BorderStyle = BorderStyle.None;
            textBox1.Dock = DockStyle.Fill;
            textBox1.Font = new Font("微软雅黑", 9f);
            textBox1.Location = new Point(1, 1);
            textBox1.Multiline = true;
            textBox1.Name = "textBox1";
            textBox1.Size = new Size(16, 19);
            textBox1.TabIndex = 0;
            textBox1.TabStop = false;
            textBox1.Text = "1";
            textBox1.TextChanged += textBox1_TextChanged;
            textBox1.MouseLeave += textBox1_MouseLeave;
            panel.BackColor = Color.White;
            panel.Controls.Add(textBox1);
            panel.Location = new Point(8, 7);
            panel.Name = "panel";
            panel.Padding = new Padding(1);
            panel.Size = new Size(18, 21);
            panel.TabIndex = 3;
            panel.Paint += panel_Paint;
            BackColor = Color.White;
            Controls.Add(Down);
            Controls.Add(Up);
            Controls.Add(panel);
            MaximumSize = new Size(50, 35);
            MinimumSize = new Size(50, 35);
            Size = new Size(50, 35);
            panel.ResumeLayout(false);
            panel.PerformLayout();
            ((ISupportInitialize) this).EndInit();
            ResumeLayout(false);
        }
    }
}