﻿using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    partial class FormSetting
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormSetting));
            this.tbConfig = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.groupBox15 = new System.Windows.Forms.GroupBox();
            this.cmsNotifyDoubleClick = new System.Windows.Forms.ComboBox();
            this.pictureBox27 = new System.Windows.Forms.PictureBox();
            this.pictureBox9 = new System.Windows.Forms.PictureBox();
            this.cmbDoubleClick = new System.Windows.Forms.ComboBox();
            this.label30 = new System.Windows.Forms.Label();
            this.chkShowNotify = new System.Windows.Forms.CheckBox();
            this.chkShowTool = new System.Windows.Forms.CheckBox();
            this.chkAutoStart = new System.Windows.Forms.CheckBox();
            this.pictureBox10 = new System.Windows.Forms.PictureBox();
            this.label33 = new System.Windows.Forms.Label();
            this.checkBox7 = new System.Windows.Forms.CheckBox();
            this.chkStartMainWindow = new System.Windows.Forms.CheckBox();
            this.chkFormTopMost = new System.Windows.Forms.CheckBox();
            this.grpJieMianFont = new System.Windows.Forms.GroupBox();
            this.btnContentDefault = new System.Windows.Forms.Button();
            this.btnContentBackColor = new OCRTools.ImageButton();
            this.btnContentFont = new System.Windows.Forms.Button();
            this.btnContentFontColor = new OCRTools.ImageButton();
            this.lblContentLable = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.btnClearConfig = new System.Windows.Forms.Button();
            this.btnOpenConfigLocation = new System.Windows.Forms.Button();
            this.chkAutoBackConfig = new System.Windows.Forms.CheckBox();
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.groupBox11 = new System.Windows.Forms.GroupBox();
            this.btnImageViewBackColor = new OCRTools.ImageButton();
            this.picLoadingImage = new System.Windows.Forms.PictureBox();
            this.cmbImageViewBackStyle = new System.Windows.Forms.ComboBox();
            this.cmbStyles = new System.Windows.Forms.ComboBox();
            this.cmbLoadingType = new System.Windows.Forms.ComboBox();
            this.label3 = new System.Windows.Forms.Label();
            this.chkDarkModel = new System.Windows.Forms.CheckBox();
            this.label2 = new System.Windows.Forms.Label();
            this.cmbVoice = new System.Windows.Forms.ComboBox();
            this.label26 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.cmbSearch = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.grpToolSet = new System.Windows.Forms.GroupBox();
            this.numToolShadowWidth = new System.Windows.Forms.NumericUpDown();
            this.cmbWeatherIcon = new System.Windows.Forms.ComboBox();
            this.cmbToolBarSize = new System.Windows.Forms.ComboBox();
            this.pictureBox17 = new System.Windows.Forms.PictureBox();
            this.btnDefaultToolBarPicture = new System.Windows.Forms.Button();
            this.pictureBox8 = new System.Windows.Forms.PictureBox();
            this.chkWeatherImage = new System.Windows.Forms.CheckBox();
            this.txtToolBarPicLocation = new System.Windows.Forms.TextBox();
            this.btnToolBarPicture = new System.Windows.Forms.Button();
            this.picToolBar = new System.Windows.Forms.PictureBox();
            this.label31 = new System.Windows.Forms.Label();
            this.chkToolBarCircle = new System.Windows.Forms.CheckBox();
            this.btnQQ = new System.Windows.Forms.Button();
            this.label40 = new System.Windows.Forms.Label();
            this.chkToolShadow = new System.Windows.Forms.CheckBox();
            this.tabPage7 = new System.Windows.Forms.TabPage();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage8 = new System.Windows.Forms.TabPage();
            this.groupBox10 = new System.Windows.Forms.GroupBox();
            this.cmbOcrType = new System.Windows.Forms.ComboBox();
            this.pictureBox16 = new System.Windows.Forms.PictureBox();
            this.pictureBox22 = new System.Windows.Forms.PictureBox();
            this.label25 = new System.Windows.Forms.Label();
            this.checkBox4 = new System.Windows.Forms.CheckBox();
            this.groupBox16 = new System.Windows.Forms.GroupBox();
            this.checkBox12 = new System.Windows.Forms.CheckBox();
            this.pictureBox26 = new System.Windows.Forms.PictureBox();
            this.pictureBox13 = new System.Windows.Forms.PictureBox();
            this.groupBox17 = new System.Windows.Forms.GroupBox();
            this.pictureBox3 = new System.Windows.Forms.PictureBox();
            this.pictureBox15 = new System.Windows.Forms.PictureBox();
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            this.cmbCopyMode = new System.Windows.Forms.ComboBox();
            this.checkBox8 = new System.Windows.Forms.CheckBox();
            this.label39 = new System.Windows.Forms.Label();
            this.checkBox3 = new System.Windows.Forms.CheckBox();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.pictureBox20 = new System.Windows.Forms.PictureBox();
            this.pictureBox21 = new System.Windows.Forms.PictureBox();
            this.pictureBox19 = new System.Windows.Forms.PictureBox();
            this.pictureBox18 = new System.Windows.Forms.PictureBox();
            this.pictureBox12 = new System.Windows.Forms.PictureBox();
            this.checkBox6 = new System.Windows.Forms.CheckBox();
            this.cmbFenDuan = new System.Windows.Forms.ComboBox();
            this.chkAutoBiaoDian = new System.Windows.Forms.CheckBox();
            this.checkBox10 = new System.Windows.Forms.CheckBox();
            this.label19 = new System.Windows.Forms.Label();
            this.checkBox5 = new System.Windows.Forms.CheckBox();
            this.chkAutoFull2Half = new System.Windows.Forms.CheckBox();
            this.checkBox11 = new System.Windows.Forms.CheckBox();
            this.checkBox1 = new System.Windows.Forms.CheckBox();
            this.tbLocalOcr = new System.Windows.Forms.TabPage();
            this.groupBox20 = new System.Windows.Forms.GroupBox();
            this.lnkOpenLocalOcr = new System.Windows.Forms.LinkLabel();
            this.lnkTestLocalOcr = new System.Windows.Forms.LinkLabel();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.lnkInstallLocalOcr = new System.Windows.Forms.LinkLabel();
            this.numLocalOcrThread = new System.Windows.Forms.NumericUpDown();
            this.numLocalOcrPort = new System.Windows.Forms.NumericUpDown();
            this.label34 = new System.Windows.Forms.Label();
            this.lblLocalOcrPort = new System.Windows.Forms.Label();
            this.pictureBox25 = new System.Windows.Forms.PictureBox();
            this.pictureBox24 = new System.Windows.Forms.PictureBox();
            this.pictureBox23 = new System.Windows.Forms.PictureBox();
            this.chkLocalOcrEnable = new System.Windows.Forms.CheckBox();
            this.grpLocalOcr = new System.Windows.Forms.GroupBox();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.groupBox19 = new System.Windows.Forms.GroupBox();
            this.numericUpDown1 = new System.Windows.Forms.NumericUpDown();
            this.label36 = new System.Windows.Forms.Label();
            this.label37 = new System.Windows.Forms.Label();
            this.pictureBox11 = new System.Windows.Forms.PictureBox();
            this.groupBox12 = new System.Windows.Forms.GroupBox();
            this.numCaptureBorderWidth = new System.Windows.Forms.NumericUpDown();
            this.imageButton1 = new OCRTools.ImageButton();
            this.label41 = new System.Windows.Forms.Label();
            this.chkZoom = new System.Windows.Forms.CheckBox();
            this.pictureBox2 = new System.Windows.Forms.PictureBox();
            this.label7 = new System.Windows.Forms.Label();
            this.chkCaptureAnimal = new System.Windows.Forms.CheckBox();
            this.chkCircleFangDa = new System.Windows.Forms.CheckBox();
            this.chkCrocessLine = new System.Windows.Forms.CheckBox();
            this.chkAutoWindow = new System.Windows.Forms.CheckBox();
            this.chkAutoControl = new System.Windows.Forms.CheckBox();
            this.label6 = new System.Windows.Forms.Label();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.btnChangeCaptureLocation = new System.Windows.Forms.Button();
            this.btnOpenCaptureLocation = new System.Windows.Forms.Button();
            this.txtCaptureFileName = new System.Windows.Forms.TextBox();
            this.lblCaptureFileName = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.numMaxHistoryCount = new System.Windows.Forms.NumericUpDown();
            this.label11 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.txtCaptureLocation = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.chkCaptureTip = new System.Windows.Forms.CheckBox();
            this.chkAutoSaveCapture = new System.Windows.Forms.CheckBox();
            this.chkSaveToClipborad = new System.Windows.Forms.CheckBox();
            this.tabPage5 = new System.Windows.Forms.TabPage();
            this.groupBox14 = new System.Windows.Forms.GroupBox();
            this.btnTieTuShadowColor = new OCRTools.ImageButton();
            this.label14 = new System.Windows.Forms.Label();
            this.label18 = new System.Windows.Forms.Label();
            this.chkTieTuFocus = new System.Windows.Forms.CheckBox();
            this.label22 = new System.Windows.Forms.Label();
            this.chkTieTuShark = new System.Windows.Forms.CheckBox();
            this.label15 = new System.Windows.Forms.Label();
            this.numShadowWidth = new System.Windows.Forms.NumericUpDown();
            this.chkTieTuUseCaptureLocation = new System.Windows.Forms.CheckBox();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.numTieTuMaxWidth = new System.Windows.Forms.NumericUpDown();
            this.numTieTuBorderWidth = new System.Windows.Forms.NumericUpDown();
            this.btnTieTuDefaultFont = new System.Windows.Forms.Button();
            this.btnTieTuBackColor = new OCRTools.ImageButton();
            this.btnTieTuFont = new System.Windows.Forms.Button();
            this.btnTieTuFontColor = new OCRTools.ImageButton();
            this.chkUseContentColor = new System.Windows.Forms.CheckBox();
            this.chkIngoreHtml = new System.Windows.Forms.CheckBox();
            this.label24 = new System.Windows.Forms.Label();
            this.label23 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.label21 = new System.Windows.Forms.Label();
            this.lblTieTuLabel = new System.Windows.Forms.Label();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.groupBox13 = new System.Windows.Forms.GroupBox();
            this.cmbHex = new System.Windows.Forms.ComboBox();
            this.label5 = new System.Windows.Forms.Label();
            this.chkHexUpper = new System.Windows.Forms.CheckBox();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.checkBox2 = new System.Windows.Forms.CheckBox();
            this.cmbRulerOpacity = new System.Windows.Forms.ComboBox();
            this.cmbRulerTheme = new System.Windows.Forms.ComboBox();
            this.label17 = new System.Windows.Forms.Label();
            this.cmbRulerUnit = new System.Windows.Forms.ComboBox();
            this.label13 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.tbShortKey = new System.Windows.Forms.TabPage();
            this.tabHotKeys = new System.Windows.Forms.TabControl();
            this.tabPage6 = new System.Windows.Forms.TabPage();
            this.groupBox9 = new System.Windows.Forms.GroupBox();
            this.pictureBox4 = new System.Windows.Forms.PictureBox();
            this.pictureBox7 = new System.Windows.Forms.PictureBox();
            this.pictureBox6 = new System.Windows.Forms.PictureBox();
            this.pictureBox5 = new System.Windows.Forms.PictureBox();
            this.linkLabel2 = new System.Windows.Forms.LinkLabel();
            this.linkLabel4 = new System.Windows.Forms.LinkLabel();
            this.linkLabel3 = new System.Windows.Forms.LinkLabel();
            this.linkLabel1 = new System.Windows.Forms.LinkLabel();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.numUpdateHour = new System.Windows.Forms.NumericUpDown();
            this.btnCheckUpdate = new System.Windows.Forms.Button();
            this.label28 = new System.Windows.Forms.Label();
            this.label29 = new System.Windows.Forms.Label();
            this.chkAutoUpdate = new System.Windows.Forms.CheckBox();
            this.label38 = new System.Windows.Forms.Label();
            this.lblCopyright = new System.Windows.Forms.Label();
            this.btnUpgrade = new System.Windows.Forms.Button();
            this.lblVersion = new System.Windows.Forms.Label();
            this.lnkWebSite = new System.Windows.Forms.LinkLabel();
            this.lblName = new System.Windows.Forms.Label();
            this.picIcon = new System.Windows.Forms.PictureBox();
            this.lnkLeft = new System.Windows.Forms.LinkLabel();
            this.tipMsg = new System.Windows.Forms.ToolTip(this.components);
            this.tbConfig.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.groupBox15.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox27)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox10)).BeginInit();
            this.grpJieMianFont.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.tabPage4.SuspendLayout();
            this.groupBox11.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.picLoadingImage)).BeginInit();
            this.grpToolSet.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numToolShadowWidth)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox17)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.picToolBar)).BeginInit();
            this.tabPage7.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.tabPage8.SuspendLayout();
            this.groupBox10.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox16)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox22)).BeginInit();
            this.groupBox16.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox26)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox13)).BeginInit();
            this.groupBox17.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            this.groupBox7.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox20)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox21)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox19)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox18)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox12)).BeginInit();
            this.tbLocalOcr.SuspendLayout();
            this.groupBox20.SuspendLayout();
            this.groupBox8.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLocalOcrThread)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numLocalOcrPort)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox25)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox24)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox23)).BeginInit();
            this.tabPage2.SuspendLayout();
            this.groupBox19.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDown1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox11)).BeginInit();
            this.groupBox12.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numCaptureBorderWidth)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxHistoryCount)).BeginInit();
            this.tabPage5.SuspendLayout();
            this.groupBox14.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numShadowWidth)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numTieTuMaxWidth)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTieTuBorderWidth)).BeginInit();
            this.tabPage3.SuspendLayout();
            this.groupBox13.SuspendLayout();
            this.groupBox6.SuspendLayout();
            this.tbShortKey.SuspendLayout();
            this.tabPage6.SuspendLayout();
            this.groupBox9.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox5)).BeginInit();
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numUpdateHour)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.picIcon)).BeginInit();
            this.SuspendLayout();
            // 
            // tbConfig
            // 
            this.tbConfig.Controls.Add(this.tabPage1);
            this.tbConfig.Controls.Add(this.tabPage4);
            this.tbConfig.Controls.Add(this.tabPage7);
            this.tbConfig.Controls.Add(this.tabPage2);
            this.tbConfig.Controls.Add(this.tabPage5);
            this.tbConfig.Controls.Add(this.tabPage3);
            this.tbConfig.Controls.Add(this.tbShortKey);
            this.tbConfig.Controls.Add(this.tabPage6);
            this.tbConfig.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tbConfig.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tbConfig.Location = new System.Drawing.Point(10, 60);
            this.tbConfig.Name = "tbConfig";
            this.tbConfig.SelectedIndex = 0;
            this.tbConfig.Size = new System.Drawing.Size(395, 400);
            this.tbConfig.TabIndex = 0;
            this.tbConfig.SelectedIndexChanged += new System.EventHandler(this.tbConfig_SelectedIndexChanged);
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.groupBox15);
            this.tabPage1.Controls.Add(this.grpJieMianFont);
            this.tabPage1.Controls.Add(this.groupBox1);
            this.tabPage1.Location = new System.Drawing.Point(4, 26);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(387, 370);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "常规";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // groupBox15
            // 
            this.groupBox15.Controls.Add(this.cmsNotifyDoubleClick);
            this.groupBox15.Controls.Add(this.pictureBox27);
            this.groupBox15.Controls.Add(this.pictureBox9);
            this.groupBox15.Controls.Add(this.cmbDoubleClick);
            this.groupBox15.Controls.Add(this.label30);
            this.groupBox15.Controls.Add(this.chkShowNotify);
            this.groupBox15.Controls.Add(this.chkShowTool);
            this.groupBox15.Controls.Add(this.chkAutoStart);
            this.groupBox15.Controls.Add(this.pictureBox10);
            this.groupBox15.Controls.Add(this.label33);
            this.groupBox15.Controls.Add(this.checkBox7);
            this.groupBox15.Controls.Add(this.chkStartMainWindow);
            this.groupBox15.Controls.Add(this.chkFormTopMost);
            this.groupBox15.Location = new System.Drawing.Point(8, 6);
            this.groupBox15.Name = "groupBox15";
            this.groupBox15.Size = new System.Drawing.Size(374, 122);
            this.groupBox15.TabIndex = 5;
            this.groupBox15.TabStop = false;
            this.groupBox15.Text = "通用";
            // 
            // cmsNotifyDoubleClick
            // 
            this.cmsNotifyDoubleClick.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmsNotifyDoubleClick.FormattingEnabled = true;
            this.cmsNotifyDoubleClick.Location = new System.Drawing.Point(261, 96);
            this.cmsNotifyDoubleClick.Name = "cmsNotifyDoubleClick";
            this.cmsNotifyDoubleClick.Size = new System.Drawing.Size(89, 25);
            this.cmsNotifyDoubleClick.TabIndex = 28;
            this.cmsNotifyDoubleClick.Tag = "双击托盘操作";
            // 
            // pictureBox27
            // 
            this.pictureBox27.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox27.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox27.Location = new System.Drawing.Point(290, 42);
            this.pictureBox27.Name = "pictureBox27";
            this.pictureBox27.Size = new System.Drawing.Size(28, 24);
            this.pictureBox27.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox27.TabIndex = 44;
            this.pictureBox27.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox27, "功能：给图片文件添加助手相关的右键菜单\r\n说明：注册后，在图片文件上点击右键，可直接操作助手相关功能。\r\n如：使用助手识别文件，预览图片，钉在桌面等。\r\n无论助手" +
        "是否正在运行，随时唤醒，随时生效！\r\n");
            // 
            // pictureBox9
            // 
            this.pictureBox9.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox9.Image = global::OCRTools.Properties.Resources.uac;
            this.pictureBox9.Location = new System.Drawing.Point(91, 20);
            this.pictureBox9.Name = "pictureBox9";
            this.pictureBox9.Size = new System.Drawing.Size(16, 16);
            this.pictureBox9.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox9.TabIndex = 45;
            this.pictureBox9.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox9, "功能：设置是否开机自启动。\r\n说明：\r\n      如果杀软拦截，请允许或者添加排除！\r\n      如果权限不足，请尝试右键->以管理员方式打开程序");
            // 
            // cmbDoubleClick
            // 
            this.cmbDoubleClick.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbDoubleClick.FormattingEnabled = true;
            this.cmbDoubleClick.Location = new System.Drawing.Point(261, 70);
            this.cmbDoubleClick.Name = "cmbDoubleClick";
            this.cmbDoubleClick.Size = new System.Drawing.Size(89, 25);
            this.cmbDoubleClick.TabIndex = 28;
            this.cmbDoubleClick.Tag = "双击工具栏操作";
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(198, 100);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(59, 17);
            this.label30.TabIndex = 27;
            this.label30.Text = "双击托盘:";
            // 
            // chkShowNotify
            // 
            this.chkShowNotify.AutoSize = true;
            this.chkShowNotify.Checked = true;
            this.chkShowNotify.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkShowNotify.Location = new System.Drawing.Point(13, 98);
            this.chkShowNotify.Name = "chkShowNotify";
            this.chkShowNotify.Size = new System.Drawing.Size(99, 21);
            this.chkShowNotify.TabIndex = 29;
            this.chkShowNotify.Text = "显示系统托盘";
            this.chkShowNotify.UseVisualStyleBackColor = true;
            this.chkShowNotify.CheckedChanged += new System.EventHandler(this.chkShowTool_CheckedChanged);
            // 
            // chkShowTool
            // 
            this.chkShowTool.AutoSize = true;
            this.chkShowTool.Checked = true;
            this.chkShowTool.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkShowTool.Location = new System.Drawing.Point(13, 72);
            this.chkShowTool.Name = "chkShowTool";
            this.chkShowTool.Size = new System.Drawing.Size(87, 21);
            this.chkShowTool.TabIndex = 29;
            this.chkShowTool.Text = "显示工具栏";
            this.chkShowTool.UseVisualStyleBackColor = true;
            this.chkShowTool.CheckedChanged += new System.EventHandler(this.chkShowTool_CheckedChanged);
            // 
            // chkAutoStart
            // 
            this.chkAutoStart.AutoSize = true;
            this.chkAutoStart.Location = new System.Drawing.Point(13, 20);
            this.chkAutoStart.Name = "chkAutoStart";
            this.chkAutoStart.Size = new System.Drawing.Size(75, 21);
            this.chkAutoStart.TabIndex = 0;
            this.chkAutoStart.Text = "开机启动";
            this.chkAutoStart.UseVisualStyleBackColor = true;
            this.chkAutoStart.CheckedChanged += new System.EventHandler(this.chkAutoStart_CheckedChanged);
            // 
            // pictureBox10
            // 
            this.pictureBox10.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox10.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox10.Location = new System.Drawing.Point(135, 43);
            this.pictureBox10.Name = "pictureBox10";
            this.pictureBox10.Size = new System.Drawing.Size(26, 23);
            this.pictureBox10.TabIndex = 46;
            this.pictureBox10.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox10, "功能：设置程序启动后，是否默认打开主窗体。\r\n说明：\r\n      设置不打开，则以静默方式启动，可以双击工具栏/右下角图标打开！");
            // 
            // label33
            // 
            this.label33.AutoSize = true;
            this.label33.Location = new System.Drawing.Point(186, 74);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(71, 17);
            this.label33.TabIndex = 27;
            this.label33.Text = "双击工具栏:";
            // 
            // checkBox7
            // 
            this.checkBox7.AutoSize = true;
            this.checkBox7.Checked = true;
            this.checkBox7.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox7.Location = new System.Drawing.Point(192, 46);
            this.checkBox7.Name = "checkBox7";
            this.checkBox7.Size = new System.Drawing.Size(99, 21);
            this.checkBox7.TabIndex = 4;
            this.checkBox7.Text = "注册右键菜单";
            this.checkBox7.UseVisualStyleBackColor = true;
            // 
            // chkStartMainWindow
            // 
            this.chkStartMainWindow.AutoSize = true;
            this.chkStartMainWindow.Checked = true;
            this.chkStartMainWindow.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkStartMainWindow.Location = new System.Drawing.Point(13, 46);
            this.chkStartMainWindow.Name = "chkStartMainWindow";
            this.chkStartMainWindow.Size = new System.Drawing.Size(123, 21);
            this.chkStartMainWindow.TabIndex = 0;
            this.chkStartMainWindow.Text = "启动后打开主窗体";
            this.chkStartMainWindow.UseVisualStyleBackColor = true;
            // 
            // chkFormTopMost
            // 
            this.chkFormTopMost.AutoSize = true;
            this.chkFormTopMost.Location = new System.Drawing.Point(192, 20);
            this.chkFormTopMost.Name = "chkFormTopMost";
            this.chkFormTopMost.Size = new System.Drawing.Size(75, 21);
            this.chkFormTopMost.TabIndex = 0;
            this.chkFormTopMost.Text = "窗体置顶";
            this.chkFormTopMost.UseVisualStyleBackColor = true;
            // 
            // grpJieMianFont
            // 
            this.grpJieMianFont.Controls.Add(this.btnContentDefault);
            this.grpJieMianFont.Controls.Add(this.btnContentBackColor);
            this.grpJieMianFont.Controls.Add(this.btnContentFont);
            this.grpJieMianFont.Controls.Add(this.btnContentFontColor);
            this.grpJieMianFont.Controls.Add(this.lblContentLable);
            this.grpJieMianFont.Location = new System.Drawing.Point(8, 193);
            this.grpJieMianFont.Name = "grpJieMianFont";
            this.grpJieMianFont.Size = new System.Drawing.Size(374, 136);
            this.grpJieMianFont.TabIndex = 26;
            this.grpJieMianFont.TabStop = false;
            this.grpJieMianFont.Text = "通用字体";
            // 
            // btnContentDefault
            // 
            this.btnContentDefault.Location = new System.Drawing.Point(293, 102);
            this.btnContentDefault.Name = "btnContentDefault";
            this.btnContentDefault.Size = new System.Drawing.Size(69, 26);
            this.btnContentDefault.TabIndex = 39;
            this.btnContentDefault.Tag = "";
            this.btnContentDefault.Text = "默认字体";
            this.btnContentDefault.UseVisualStyleBackColor = false;
            this.btnContentDefault.Click += new System.EventHandler(this.btnContentDefault_Click);
            // 
            // btnContentBackColor
            // 
            this.btnContentBackColor.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.btnContentBackColor.Image = ((System.Drawing.Image)(resources.GetObject("btnContentBackColor.Image")));
            this.btnContentBackColor.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnContentBackColor.ImageColor = System.Drawing.Color.Black;
            this.btnContentBackColor.Location = new System.Drawing.Point(293, 74);
            this.btnContentBackColor.Name = "btnContentBackColor";
            this.btnContentBackColor.Size = new System.Drawing.Size(69, 26);
            this.btnContentBackColor.TabIndex = 38;
            this.btnContentBackColor.Tag = "默认背景颜色";
            this.btnContentBackColor.Text = "背景色";
            this.btnContentBackColor.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnContentBackColor.UseVisualStyleBackColor = false;
            this.btnContentBackColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // btnContentFont
            // 
            this.btnContentFont.Location = new System.Drawing.Point(293, 18);
            this.btnContentFont.Name = "btnContentFont";
            this.btnContentFont.Size = new System.Drawing.Size(71, 26);
            this.btnContentFont.TabIndex = 37;
            this.btnContentFont.TabStop = false;
            this.btnContentFont.Tag = "默认文字字体";
            this.btnContentFont.Text = "字体";
            this.btnContentFont.UseVisualStyleBackColor = false;
            this.btnContentFont.Click += new System.EventHandler(this.btnFont_Click);
            // 
            // btnContentFontColor
            // 
            this.btnContentFontColor.Image = ((System.Drawing.Image)(resources.GetObject("btnContentFontColor.Image")));
            this.btnContentFontColor.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnContentFontColor.ImageColor = System.Drawing.Color.Black;
            this.btnContentFontColor.Location = new System.Drawing.Point(293, 46);
            this.btnContentFontColor.Name = "btnContentFontColor";
            this.btnContentFontColor.Size = new System.Drawing.Size(71, 26);
            this.btnContentFontColor.TabIndex = 37;
            this.btnContentFontColor.Tag = "默认文字颜色";
            this.btnContentFontColor.Text = "文本色";
            this.btnContentFontColor.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnContentFontColor.UseVisualStyleBackColor = false;
            this.btnContentFontColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // lblContentLable
            // 
            this.lblContentLable.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.lblContentLable.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.lblContentLable.Location = new System.Drawing.Point(12, 17);
            this.lblContentLable.Name = "lblContentLable";
            this.lblContentLable.Size = new System.Drawing.Size(276, 111);
            this.lblContentLable.TabIndex = 36;
            this.lblContentLable.Text = "预览\r\nAaOo012345";
            this.lblContentLable.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.textBox2);
            this.groupBox1.Controls.Add(this.textBox1);
            this.groupBox1.Controls.Add(this.btnClearConfig);
            this.groupBox1.Controls.Add(this.btnOpenConfigLocation);
            this.groupBox1.Controls.Add(this.chkAutoBackConfig);
            this.groupBox1.Location = new System.Drawing.Point(8, 134);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(374, 51);
            this.groupBox1.TabIndex = 3;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "配置文件";
            // 
            // textBox2
            // 
            this.textBox2.Location = new System.Drawing.Point(274, 0);
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(100, 23);
            this.textBox2.TabIndex = 42;
            this.textBox2.Tag = "用户名";
            this.textBox2.Visible = false;
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(274, 27);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(100, 23);
            this.textBox1.TabIndex = 41;
            this.textBox1.Tag = "密码";
            this.textBox1.Visible = false;
            // 
            // btnClearConfig
            // 
            this.btnClearConfig.Location = new System.Drawing.Point(111, 18);
            this.btnClearConfig.Name = "btnClearConfig";
            this.btnClearConfig.Size = new System.Drawing.Size(72, 23);
            this.btnClearConfig.TabIndex = 4;
            this.btnClearConfig.Text = "清理配置";
            this.btnClearConfig.UseVisualStyleBackColor = true;
            this.btnClearConfig.Click += new System.EventHandler(this.btnClearConfig_Click);
            // 
            // btnOpenConfigLocation
            // 
            this.btnOpenConfigLocation.Location = new System.Drawing.Point(193, 18);
            this.btnOpenConfigLocation.Name = "btnOpenConfigLocation";
            this.btnOpenConfigLocation.Size = new System.Drawing.Size(80, 23);
            this.btnOpenConfigLocation.TabIndex = 4;
            this.btnOpenConfigLocation.Text = "打开文件夹";
            this.btnOpenConfigLocation.UseVisualStyleBackColor = true;
            this.btnOpenConfigLocation.Click += new System.EventHandler(this.btnOpenConfigLocation_Click);
            // 
            // chkAutoBackConfig
            // 
            this.chkAutoBackConfig.AutoSize = true;
            this.chkAutoBackConfig.Checked = true;
            this.chkAutoBackConfig.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoBackConfig.Location = new System.Drawing.Point(13, 22);
            this.chkAutoBackConfig.Name = "chkAutoBackConfig";
            this.chkAutoBackConfig.Size = new System.Drawing.Size(75, 21);
            this.chkAutoBackConfig.TabIndex = 0;
            this.chkAutoBackConfig.Text = "自动备份";
            this.chkAutoBackConfig.UseVisualStyleBackColor = true;
            // 
            // tabPage4
            // 
            this.tabPage4.Controls.Add(this.groupBox11);
            this.tabPage4.Controls.Add(this.grpToolSet);
            this.tabPage4.Location = new System.Drawing.Point(4, 26);
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage4.Size = new System.Drawing.Size(387, 370);
            this.tabPage4.TabIndex = 3;
            this.tabPage4.Text = "界面";
            this.tabPage4.UseVisualStyleBackColor = true;
            // 
            // groupBox11
            // 
            this.groupBox11.Controls.Add(this.btnImageViewBackColor);
            this.groupBox11.Controls.Add(this.picLoadingImage);
            this.groupBox11.Controls.Add(this.cmbImageViewBackStyle);
            this.groupBox11.Controls.Add(this.cmbStyles);
            this.groupBox11.Controls.Add(this.cmbLoadingType);
            this.groupBox11.Controls.Add(this.label3);
            this.groupBox11.Controls.Add(this.chkDarkModel);
            this.groupBox11.Controls.Add(this.label2);
            this.groupBox11.Controls.Add(this.cmbVoice);
            this.groupBox11.Controls.Add(this.label26);
            this.groupBox11.Controls.Add(this.label4);
            this.groupBox11.Controls.Add(this.cmbSearch);
            this.groupBox11.Controls.Add(this.label1);
            this.groupBox11.Location = new System.Drawing.Point(8, 6);
            this.groupBox11.Name = "groupBox11";
            this.groupBox11.Size = new System.Drawing.Size(374, 97);
            this.groupBox11.TabIndex = 45;
            this.groupBox11.TabStop = false;
            this.groupBox11.Text = "通用";
            // 
            // btnImageViewBackColor
            // 
            this.btnImageViewBackColor.BackColor = System.Drawing.Color.White;
            this.btnImageViewBackColor.Image = ((System.Drawing.Image)(resources.GetObject("btnImageViewBackColor.Image")));
            this.btnImageViewBackColor.ImageColor = System.Drawing.Color.Black;
            this.btnImageViewBackColor.Location = new System.Drawing.Point(172, 68);
            this.btnImageViewBackColor.Name = "btnImageViewBackColor";
            this.btnImageViewBackColor.Size = new System.Drawing.Size(25, 20);
            this.btnImageViewBackColor.TabIndex = 45;
            this.btnImageViewBackColor.Tag = "图片预览背景颜色";
            this.btnImageViewBackColor.UseVisualStyleBackColor = false;
            this.btnImageViewBackColor.Visible = false;
            this.btnImageViewBackColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // picLoadingImage
            // 
            this.picLoadingImage.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.picLoadingImage.Location = new System.Drawing.Point(172, 41);
            this.picLoadingImage.Name = "picLoadingImage";
            this.picLoadingImage.Size = new System.Drawing.Size(25, 25);
            this.picLoadingImage.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.picLoadingImage.TabIndex = 25;
            this.picLoadingImage.TabStop = false;
            // 
            // cmbImageViewBackStyle
            // 
            this.cmbImageViewBackStyle.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbImageViewBackStyle.FormattingEnabled = true;
            this.cmbImageViewBackStyle.Location = new System.Drawing.Point(78, 68);
            this.cmbImageViewBackStyle.Name = "cmbImageViewBackStyle";
            this.cmbImageViewBackStyle.Size = new System.Drawing.Size(86, 25);
            this.cmbImageViewBackStyle.TabIndex = 28;
            this.cmbImageViewBackStyle.TabStop = false;
            this.cmbImageViewBackStyle.Tag = "图片预览背景";
            this.cmbImageViewBackStyle.SelectedIndexChanged += new System.EventHandler(this.cmbImageViewBackStyle_SelectedIndexChanged);
            // 
            // cmbStyles
            // 
            this.cmbStyles.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbStyles.FormattingEnabled = true;
            this.cmbStyles.Location = new System.Drawing.Point(78, 17);
            this.cmbStyles.Name = "cmbStyles";
            this.cmbStyles.Size = new System.Drawing.Size(65, 25);
            this.cmbStyles.TabIndex = 3;
            this.cmbStyles.Tag = "主题样式";
            this.cmbStyles.SelectedIndexChanged += new System.EventHandler(this.cmbStyles_SelectedIndexChanged);
            // 
            // cmbLoadingType
            // 
            this.cmbLoadingType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbLoadingType.FormattingEnabled = true;
            this.cmbLoadingType.Location = new System.Drawing.Point(78, 43);
            this.cmbLoadingType.Name = "cmbLoadingType";
            this.cmbLoadingType.Size = new System.Drawing.Size(86, 25);
            this.cmbLoadingType.TabIndex = 3;
            this.cmbLoadingType.Tag = "加载动画";
            this.cmbLoadingType.SelectedIndexChanged += new System.EventHandler(this.cmbLoadingType_SelectedIndexChanged);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(12, 20);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(59, 17);
            this.label3.TabIndex = 2;
            this.label3.Text = "主题样式:";
            // 
            // chkDarkModel
            // 
            this.chkDarkModel.AutoSize = true;
            this.chkDarkModel.Checked = true;
            this.chkDarkModel.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkDarkModel.Location = new System.Drawing.Point(152, 19);
            this.chkDarkModel.Name = "chkDarkModel";
            this.chkDarkModel.Size = new System.Drawing.Size(75, 21);
            this.chkDarkModel.TabIndex = 40;
            this.chkDarkModel.Text = "深色模式";
            this.chkDarkModel.UseVisualStyleBackColor = true;
            this.chkDarkModel.CheckedChanged += new System.EventHandler(this.chkDarkModel_CheckedChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(12, 72);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(59, 17);
            this.label2.TabIndex = 27;
            this.label2.Text = "图片背景:";
            // 
            // cmbVoice
            // 
            this.cmbVoice.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbVoice.FormattingEnabled = true;
            this.cmbVoice.Location = new System.Drawing.Point(271, 43);
            this.cmbVoice.Name = "cmbVoice";
            this.cmbVoice.Size = new System.Drawing.Size(82, 25);
            this.cmbVoice.TabIndex = 6;
            this.cmbVoice.Tag = "朗读配置";
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(233, 46);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(35, 17);
            this.label26.TabIndex = 5;
            this.label26.Text = "语音:";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(12, 46);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(59, 17);
            this.label4.TabIndex = 2;
            this.label4.Text = "加载动画:";
            // 
            // cmbSearch
            // 
            this.cmbSearch.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbSearch.FormattingEnabled = true;
            this.cmbSearch.Location = new System.Drawing.Point(271, 17);
            this.cmbSearch.Name = "cmbSearch";
            this.cmbSearch.Size = new System.Drawing.Size(81, 25);
            this.cmbSearch.TabIndex = 3;
            this.cmbSearch.Tag = "搜索引擎";
            this.cmbSearch.SelectedIndexChanged += new System.EventHandler(this.cmbLoadingType_SelectedIndexChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(231, 20);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(35, 17);
            this.label1.TabIndex = 2;
            this.label1.Text = "搜索:";
            // 
            // grpToolSet
            // 
            this.grpToolSet.Controls.Add(this.numToolShadowWidth);
            this.grpToolSet.Controls.Add(this.cmbWeatherIcon);
            this.grpToolSet.Controls.Add(this.cmbToolBarSize);
            this.grpToolSet.Controls.Add(this.pictureBox17);
            this.grpToolSet.Controls.Add(this.btnDefaultToolBarPicture);
            this.grpToolSet.Controls.Add(this.pictureBox8);
            this.grpToolSet.Controls.Add(this.chkWeatherImage);
            this.grpToolSet.Controls.Add(this.txtToolBarPicLocation);
            this.grpToolSet.Controls.Add(this.btnToolBarPicture);
            this.grpToolSet.Controls.Add(this.picToolBar);
            this.grpToolSet.Controls.Add(this.label31);
            this.grpToolSet.Controls.Add(this.chkToolBarCircle);
            this.grpToolSet.Controls.Add(this.btnQQ);
            this.grpToolSet.Controls.Add(this.label40);
            this.grpToolSet.Controls.Add(this.chkToolShadow);
            this.grpToolSet.Location = new System.Drawing.Point(8, 107);
            this.grpToolSet.Name = "grpToolSet";
            this.grpToolSet.Size = new System.Drawing.Size(374, 155);
            this.grpToolSet.TabIndex = 26;
            this.grpToolSet.TabStop = false;
            this.grpToolSet.Text = "工具栏样式";
            // 
            // numToolShadowWidth
            // 
            this.numToolShadowWidth.Location = new System.Drawing.Point(89, 44);
            this.numToolShadowWidth.Maximum = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numToolShadowWidth.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numToolShadowWidth.Name = "numToolShadowWidth";
            this.numToolShadowWidth.Size = new System.Drawing.Size(37, 23);
            this.numToolShadowWidth.TabIndex = 42;
            this.numToolShadowWidth.TabStop = false;
            this.numToolShadowWidth.Tag = "工具栏阴影宽度";
            this.numToolShadowWidth.Value = new decimal(new int[] {
            8,
            0,
            0,
            0});
            // 
            // cmbWeatherIcon
            // 
            this.cmbWeatherIcon.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbWeatherIcon.FormattingEnabled = true;
            this.cmbWeatherIcon.Location = new System.Drawing.Point(89, 122);
            this.cmbWeatherIcon.Name = "cmbWeatherIcon";
            this.cmbWeatherIcon.Size = new System.Drawing.Size(64, 25);
            this.cmbWeatherIcon.TabIndex = 28;
            this.cmbWeatherIcon.TabStop = false;
            this.cmbWeatherIcon.Tag = "天气图标样式";
            // 
            // cmbToolBarSize
            // 
            this.cmbToolBarSize.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbToolBarSize.FormattingEnabled = true;
            this.cmbToolBarSize.Items.AddRange(new object[] {
            "自动",
            "12*12",
            "24*24",
            "36*36",
            "48*48",
            "60*60",
            "72*72",
            "84*84",
            "96*96",
            "108*108",
            "120*120"});
            this.cmbToolBarSize.Location = new System.Drawing.Point(91, 17);
            this.cmbToolBarSize.Name = "cmbToolBarSize";
            this.cmbToolBarSize.Size = new System.Drawing.Size(89, 25);
            this.cmbToolBarSize.TabIndex = 28;
            this.cmbToolBarSize.Tag = "工具栏图标尺寸";
            // 
            // pictureBox17
            // 
            this.pictureBox17.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox17.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox17.Location = new System.Drawing.Point(159, 122);
            this.pictureBox17.Margin = new System.Windows.Forms.Padding(0);
            this.pictureBox17.Name = "pictureBox17";
            this.pictureBox17.Size = new System.Drawing.Size(26, 21);
            this.pictureBox17.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            this.pictureBox17.TabIndex = 44;
            this.pictureBox17.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox17, "功能：以当前IP所在位置的天气设置为图标。\r\n说明：\r\n      天气更新间隔为30分钟。\r\n      气象预警信息，以弹框方式提示。\r\n");
            // 
            // btnDefaultToolBarPicture
            // 
            this.btnDefaultToolBarPicture.Location = new System.Drawing.Point(127, 94);
            this.btnDefaultToolBarPicture.Name = "btnDefaultToolBarPicture";
            this.btnDefaultToolBarPicture.Size = new System.Drawing.Size(42, 25);
            this.btnDefaultToolBarPicture.TabIndex = 39;
            this.btnDefaultToolBarPicture.Tag = "";
            this.btnDefaultToolBarPicture.Text = "重置";
            this.btnDefaultToolBarPicture.UseVisualStyleBackColor = false;
            this.btnDefaultToolBarPicture.Click += new System.EventHandler(this.btnDefaultToolBarPicture_Click);
            // 
            // pictureBox8
            // 
            this.pictureBox8.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox8.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox8.Location = new System.Drawing.Point(162, 42);
            this.pictureBox8.Name = "pictureBox8";
            this.pictureBox8.Size = new System.Drawing.Size(26, 23);
            this.pictureBox8.TabIndex = 44;
            this.pictureBox8.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox8, "功能：工具栏图标阴影效果。\r\n说明：\r\n      支持异形透明图片(非圆形图标，不支持阴影)！\r\n      不透明图片，支持阴影。");
            // 
            // chkWeatherImage
            // 
            this.chkWeatherImage.AutoSize = true;
            this.chkWeatherImage.Location = new System.Drawing.Point(13, 124);
            this.chkWeatherImage.Margin = new System.Windows.Forms.Padding(0);
            this.chkWeatherImage.Name = "chkWeatherImage";
            this.chkWeatherImage.Size = new System.Drawing.Size(75, 21);
            this.chkWeatherImage.TabIndex = 30;
            this.chkWeatherImage.TabStop = false;
            this.chkWeatherImage.Text = "天气图标";
            this.chkWeatherImage.UseVisualStyleBackColor = true;
            // 
            // txtToolBarPicLocation
            // 
            this.txtToolBarPicLocation.Location = new System.Drawing.Point(48, -4);
            this.txtToolBarPicLocation.Name = "txtToolBarPicLocation";
            this.txtToolBarPicLocation.Size = new System.Drawing.Size(100, 23);
            this.txtToolBarPicLocation.TabIndex = 40;
            this.txtToolBarPicLocation.Tag = "工具栏图片";
            this.txtToolBarPicLocation.Visible = false;
            // 
            // btnToolBarPicture
            // 
            this.btnToolBarPicture.Location = new System.Drawing.Point(15, 94);
            this.btnToolBarPicture.Name = "btnToolBarPicture";
            this.btnToolBarPicture.Size = new System.Drawing.Size(50, 25);
            this.btnToolBarPicture.TabIndex = 37;
            this.btnToolBarPicture.TabStop = false;
            this.btnToolBarPicture.Text = "自定义";
            this.btnToolBarPicture.UseVisualStyleBackColor = false;
            this.btnToolBarPicture.Click += new System.EventHandler(this.btnToolBarPicture_Click);
            // 
            // picToolBar
            // 
            this.picToolBar.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.picToolBar.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.picToolBar.Location = new System.Drawing.Point(218, 20);
            this.picToolBar.Name = "picToolBar";
            this.picToolBar.Size = new System.Drawing.Size(120, 120);
            this.picToolBar.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            this.picToolBar.TabIndex = 25;
            this.picToolBar.TabStop = false;
            // 
            // label31
            // 
            this.label31.AutoSize = true;
            this.label31.Location = new System.Drawing.Point(13, 75);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(107, 17);
            this.label31.TabIndex = 27;
            this.label31.Text = "自定义工具栏图片:";
            // 
            // chkToolBarCircle
            // 
            this.chkToolBarCircle.AutoSize = true;
            this.chkToolBarCircle.Checked = true;
            this.chkToolBarCircle.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkToolBarCircle.Location = new System.Drawing.Point(13, 20);
            this.chkToolBarCircle.Margin = new System.Windows.Forms.Padding(0);
            this.chkToolBarCircle.Name = "chkToolBarCircle";
            this.chkToolBarCircle.Size = new System.Drawing.Size(75, 21);
            this.chkToolBarCircle.TabIndex = 30;
            this.chkToolBarCircle.Text = "圆形图标";
            this.chkToolBarCircle.UseVisualStyleBackColor = true;
            // 
            // btnQQ
            // 
            this.btnQQ.Location = new System.Drawing.Point(71, 94);
            this.btnQQ.Name = "btnQQ";
            this.btnQQ.Size = new System.Drawing.Size(50, 25);
            this.btnQQ.TabIndex = 37;
            this.btnQQ.TabStop = false;
            this.btnQQ.Text = "QQ头像";
            this.btnQQ.UseVisualStyleBackColor = false;
            this.btnQQ.Click += new System.EventHandler(this.btnQQ_Click);
            // 
            // label40
            // 
            this.label40.AutoSize = true;
            this.label40.Location = new System.Drawing.Point(131, 48);
            this.label40.Name = "label40";
            this.label40.Size = new System.Drawing.Size(32, 17);
            this.label40.TabIndex = 41;
            this.label40.Text = "像素";
            // 
            // chkToolShadow
            // 
            this.chkToolShadow.AutoSize = true;
            this.chkToolShadow.Checked = true;
            this.chkToolShadow.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkToolShadow.Location = new System.Drawing.Point(13, 46);
            this.chkToolShadow.Margin = new System.Windows.Forms.Padding(0);
            this.chkToolShadow.Name = "chkToolShadow";
            this.chkToolShadow.Size = new System.Drawing.Size(75, 21);
            this.chkToolShadow.TabIndex = 30;
            this.chkToolShadow.TabStop = false;
            this.chkToolShadow.Text = "阴影效果";
            this.chkToolShadow.UseVisualStyleBackColor = true;
            // 
            // tabPage7
            // 
            this.tabPage7.Controls.Add(this.tabControl1);
            this.tabPage7.Location = new System.Drawing.Point(4, 26);
            this.tabPage7.Name = "tabPage7";
            this.tabPage7.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage7.Size = new System.Drawing.Size(387, 370);
            this.tabPage7.TabIndex = 8;
            this.tabPage7.Text = "识别";
            this.tabPage7.UseVisualStyleBackColor = true;
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage8);
            this.tabControl1.Controls.Add(this.tbLocalOcr);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(3, 3);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(381, 364);
            this.tabControl1.TabIndex = 48;
            // 
            // tabPage8
            // 
            this.tabPage8.Controls.Add(this.groupBox10);
            this.tabPage8.Controls.Add(this.groupBox16);
            this.tabPage8.Location = new System.Drawing.Point(4, 26);
            this.tabPage8.Name = "tabPage8";
            this.tabPage8.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage8.Size = new System.Drawing.Size(373, 334);
            this.tabPage8.TabIndex = 0;
            this.tabPage8.Text = "识别设置";
            this.tabPage8.UseVisualStyleBackColor = true;
            // 
            // groupBox10
            // 
            this.groupBox10.Controls.Add(this.cmbOcrType);
            this.groupBox10.Controls.Add(this.pictureBox16);
            this.groupBox10.Controls.Add(this.pictureBox22);
            this.groupBox10.Controls.Add(this.label25);
            this.groupBox10.Controls.Add(this.checkBox4);
            this.groupBox10.Location = new System.Drawing.Point(6, 6);
            this.groupBox10.Name = "groupBox10";
            this.groupBox10.Size = new System.Drawing.Size(361, 71);
            this.groupBox10.TabIndex = 45;
            this.groupBox10.TabStop = false;
            this.groupBox10.Text = "识别过程";
            // 
            // cmbOcrType
            // 
            this.cmbOcrType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbOcrType.FormattingEnabled = true;
            this.cmbOcrType.Location = new System.Drawing.Point(74, 15);
            this.cmbOcrType.Name = "cmbOcrType";
            this.cmbOcrType.Size = new System.Drawing.Size(112, 25);
            this.cmbOcrType.TabIndex = 6;
            this.cmbOcrType.Tag = "识别策略";
            // 
            // pictureBox16
            // 
            this.pictureBox16.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox16.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox16.Location = new System.Drawing.Point(110, 39);
            this.pictureBox16.Name = "pictureBox16";
            this.pictureBox16.Size = new System.Drawing.Size(28, 24);
            this.pictureBox16.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox16.TabIndex = 44;
            this.pictureBox16.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox16, "功能：识别前自动压缩图片大小。\r\n说明：如果开启，可以加快识别速度，图越大效果越明显。\r\n可自行选择是否开启！");
            // 
            // pictureBox22
            // 
            this.pictureBox22.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox22.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox22.Location = new System.Drawing.Point(190, 13);
            this.pictureBox22.Name = "pictureBox22";
            this.pictureBox22.Size = new System.Drawing.Size(28, 24);
            this.pictureBox22.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox22.TabIndex = 43;
            this.pictureBox22.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox22, "功能：选择本地+网络识别的优先级。\r\n说明：\r\n本地识别：速度更稳定，但受限于特征库，识别结果没网络识别精确！\r\n网络识别：速度受限于本地网络及服务商接口，结果精" +
        "度有保证。\r\n\r\n可根据实际需求，进行选择。\r\n");
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(13, 19);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(59, 17);
            this.label25.TabIndex = 5;
            this.label25.Text = "识别策略:";
            // 
            // checkBox4
            // 
            this.checkBox4.AutoSize = true;
            this.checkBox4.Checked = true;
            this.checkBox4.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox4.Location = new System.Drawing.Point(13, 43);
            this.checkBox4.Name = "checkBox4";
            this.checkBox4.Size = new System.Drawing.Size(99, 21);
            this.checkBox4.TabIndex = 4;
            this.checkBox4.Text = "图片自动压缩";
            this.checkBox4.UseVisualStyleBackColor = true;
            // 
            // groupBox16
            // 
            this.groupBox16.Controls.Add(this.checkBox12);
            this.groupBox16.Controls.Add(this.pictureBox26);
            this.groupBox16.Controls.Add(this.pictureBox13);
            this.groupBox16.Controls.Add(this.groupBox17);
            this.groupBox16.Controls.Add(this.groupBox7);
            this.groupBox16.Controls.Add(this.checkBox11);
            this.groupBox16.Controls.Add(this.checkBox1);
            this.groupBox16.Location = new System.Drawing.Point(6, 84);
            this.groupBox16.Name = "groupBox16";
            this.groupBox16.Size = new System.Drawing.Size(363, 223);
            this.groupBox16.TabIndex = 45;
            this.groupBox16.TabStop = false;
            this.groupBox16.Text = "识别结果";
            // 
            // checkBox12
            // 
            this.checkBox12.AutoSize = true;
            this.checkBox12.Location = new System.Drawing.Point(95, 20);
            this.checkBox12.Name = "checkBox12";
            this.checkBox12.Size = new System.Drawing.Size(99, 21);
            this.checkBox12.TabIndex = 1;
            this.checkBox12.Text = "显示文字预览";
            this.checkBox12.UseVisualStyleBackColor = true;
            // 
            // pictureBox26
            // 
            this.pictureBox26.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox26.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox26.Location = new System.Drawing.Point(330, 16);
            this.pictureBox26.Name = "pictureBox26";
            this.pictureBox26.Size = new System.Drawing.Size(28, 24);
            this.pictureBox26.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox26.TabIndex = 43;
            this.pictureBox26.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox26, "功能：图文模式下，自动根据窗口大小缩放图片。\r\n说明：\r\n      如果要显示原尺寸，请去掉勾选！\r\n");
            // 
            // pictureBox13
            // 
            this.pictureBox13.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox13.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox13.Location = new System.Drawing.Point(197, 16);
            this.pictureBox13.Name = "pictureBox13";
            this.pictureBox13.Size = new System.Drawing.Size(28, 24);
            this.pictureBox13.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox13.TabIndex = 43;
            this.pictureBox13.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox13, "功能：图文模式下，在图上显示文字预览。\r\n说明：\r\n      此操作会增加性能消耗，可酌情打开！");
            // 
            // groupBox17
            // 
            this.groupBox17.Controls.Add(this.pictureBox3);
            this.groupBox17.Controls.Add(this.pictureBox15);
            this.groupBox17.Controls.Add(this.pictureBox1);
            this.groupBox17.Controls.Add(this.cmbCopyMode);
            this.groupBox17.Controls.Add(this.checkBox8);
            this.groupBox17.Controls.Add(this.label39);
            this.groupBox17.Controls.Add(this.checkBox3);
            this.groupBox17.Location = new System.Drawing.Point(4, 147);
            this.groupBox17.Name = "groupBox17";
            this.groupBox17.Size = new System.Drawing.Size(355, 70);
            this.groupBox17.TabIndex = 45;
            this.groupBox17.TabStop = false;
            this.groupBox17.Text = "结果复制";
            // 
            // pictureBox3
            // 
            this.pictureBox3.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox3.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox3.Location = new System.Drawing.Point(150, 15);
            this.pictureBox3.Name = "pictureBox3";
            this.pictureBox3.Size = new System.Drawing.Size(28, 24);
            this.pictureBox3.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox3.TabIndex = 44;
            this.pictureBox3.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox3, "功能：OCR完成后，自动复制识别结果到粘贴板。\r\n说明：如果不需要复制，请不要勾选！");
            // 
            // pictureBox15
            // 
            this.pictureBox15.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox15.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox15.Location = new System.Drawing.Point(115, 42);
            this.pictureBox15.Name = "pictureBox15";
            this.pictureBox15.Size = new System.Drawing.Size(28, 24);
            this.pictureBox15.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox15.TabIndex = 44;
            this.pictureBox15.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox15, "功能：翻译模式时，是否复制原文。\r\n说明：不勾选，仅复制译文，否则，复制原文+译文！");
            // 
            // pictureBox1
            // 
            this.pictureBox1.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox1.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox1.Location = new System.Drawing.Point(324, 14);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new System.Drawing.Size(28, 24);
            this.pictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox1.TabIndex = 44;
            this.pictureBox1.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox1, "功能：选择复制哪一个识别结果。\r\n说明：体验版以上版本，会有多个识别结果，可以选择复制哪一个！\r\n\r\n选项说明\r\n最快：第一个\r\n最新：最后一个\r\n所有：所有结果" +
        "累加");
            // 
            // cmbCopyMode
            // 
            this.cmbCopyMode.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbCopyMode.FormattingEnabled = true;
            this.cmbCopyMode.Location = new System.Drawing.Point(226, 16);
            this.cmbCopyMode.Name = "cmbCopyMode";
            this.cmbCopyMode.Size = new System.Drawing.Size(93, 25);
            this.cmbCopyMode.TabIndex = 6;
            this.cmbCopyMode.Tag = "复制模式";
            // 
            // checkBox8
            // 
            this.checkBox8.AutoSize = true;
            this.checkBox8.Location = new System.Drawing.Point(5, 19);
            this.checkBox8.Name = "checkBox8";
            this.checkBox8.Size = new System.Drawing.Size(147, 21);
            this.checkBox8.TabIndex = 4;
            this.checkBox8.Text = "自动复制结果到粘贴板";
            this.checkBox8.UseVisualStyleBackColor = true;
            // 
            // label39
            // 
            this.label39.AutoSize = true;
            this.label39.Location = new System.Drawing.Point(190, 19);
            this.label39.Name = "label39";
            this.label39.Size = new System.Drawing.Size(35, 17);
            this.label39.TabIndex = 5;
            this.label39.Text = "模式:";
            // 
            // checkBox3
            // 
            this.checkBox3.AutoSize = true;
            this.checkBox3.Location = new System.Drawing.Point(5, 46);
            this.checkBox3.Name = "checkBox3";
            this.checkBox3.Size = new System.Drawing.Size(111, 21);
            this.checkBox3.TabIndex = 1;
            this.checkBox3.Text = "翻译时复制原文";
            this.checkBox3.UseVisualStyleBackColor = true;
            // 
            // groupBox7
            // 
            this.groupBox7.Controls.Add(this.pictureBox20);
            this.groupBox7.Controls.Add(this.pictureBox21);
            this.groupBox7.Controls.Add(this.pictureBox19);
            this.groupBox7.Controls.Add(this.pictureBox18);
            this.groupBox7.Controls.Add(this.pictureBox12);
            this.groupBox7.Controls.Add(this.checkBox6);
            this.groupBox7.Controls.Add(this.cmbFenDuan);
            this.groupBox7.Controls.Add(this.chkAutoBiaoDian);
            this.groupBox7.Controls.Add(this.checkBox10);
            this.groupBox7.Controls.Add(this.label19);
            this.groupBox7.Controls.Add(this.checkBox5);
            this.groupBox7.Controls.Add(this.chkAutoFull2Half);
            this.groupBox7.Location = new System.Drawing.Point(4, 44);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Size = new System.Drawing.Size(355, 98);
            this.groupBox7.TabIndex = 44;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "排版展示";
            // 
            // pictureBox20
            // 
            this.pictureBox20.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox20.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox20.Location = new System.Drawing.Point(280, 68);
            this.pictureBox20.Name = "pictureBox20";
            this.pictureBox20.Size = new System.Drawing.Size(28, 24);
            this.pictureBox20.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox20.TabIndex = 47;
            this.pictureBox20.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox20, "功能：重复标点校正.\r\n\r\n正确：\r\n德国队竟然战胜了巴西队！\r\n她竟然对你说「喵」？！\r\n\r\n错误：\r\n德国队竟然战胜了巴西队！！\r\n她竟然对你说「喵」？？！！" +
        "");
            // 
            // pictureBox21
            // 
            this.pictureBox21.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox21.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox21.Location = new System.Drawing.Point(122, 68);
            this.pictureBox21.Name = "pictureBox21";
            this.pictureBox21.Size = new System.Drawing.Size(28, 24);
            this.pictureBox21.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox21.TabIndex = 48;
            this.pictureBox21.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox21, "功能：\r\n自动根据语境，把文字中的标点符号转换为中/英文标点");
            // 
            // pictureBox19
            // 
            this.pictureBox19.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox19.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox19.Location = new System.Drawing.Point(256, 42);
            this.pictureBox19.Name = "pictureBox19";
            this.pictureBox19.Size = new System.Drawing.Size(28, 24);
            this.pictureBox19.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox19.TabIndex = 44;
            this.pictureBox19.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox19, resources.GetString("pictureBox19.ToolTip"));
            // 
            // pictureBox18
            // 
            this.pictureBox18.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox18.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox18.Location = new System.Drawing.Point(122, 42);
            this.pictureBox18.Name = "pictureBox18";
            this.pictureBox18.Size = new System.Drawing.Size(28, 24);
            this.pictureBox18.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox18.TabIndex = 44;
            this.pictureBox18.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox18, resources.GetString("pictureBox18.ToolTip"));
            // 
            // pictureBox12
            // 
            this.pictureBox12.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox12.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox12.Location = new System.Drawing.Point(257, 16);
            this.pictureBox12.Name = "pictureBox12";
            this.pictureBox12.Size = new System.Drawing.Size(28, 24);
            this.pictureBox12.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox12.TabIndex = 44;
            this.pictureBox12.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox12, "功能：段落开头是否缩进。\r\n说明：对于文档排版更友好，如果只需要识别文字，可关闭");
            // 
            // checkBox6
            // 
            this.checkBox6.AutoSize = true;
            this.checkBox6.Checked = true;
            this.checkBox6.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox6.Location = new System.Drawing.Point(182, 72);
            this.checkBox6.Name = "checkBox6";
            this.checkBox6.Size = new System.Drawing.Size(99, 21);
            this.checkBox6.TabIndex = 45;
            this.checkBox6.Text = "去除重复标点";
            this.checkBox6.UseVisualStyleBackColor = true;
            // 
            // cmbFenDuan
            // 
            this.cmbFenDuan.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbFenDuan.FormattingEnabled = true;
            this.cmbFenDuan.Location = new System.Drawing.Point(73, 16);
            this.cmbFenDuan.Name = "cmbFenDuan";
            this.cmbFenDuan.Size = new System.Drawing.Size(82, 25);
            this.cmbFenDuan.TabIndex = 6;
            this.cmbFenDuan.Tag = "分段模式";
            // 
            // chkAutoBiaoDian
            // 
            this.chkAutoBiaoDian.AutoSize = true;
            this.chkAutoBiaoDian.Checked = true;
            this.chkAutoBiaoDian.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoBiaoDian.Location = new System.Drawing.Point(11, 72);
            this.chkAutoBiaoDian.Name = "chkAutoBiaoDian";
            this.chkAutoBiaoDian.Size = new System.Drawing.Size(111, 21);
            this.chkAutoBiaoDian.TabIndex = 46;
            this.chkAutoBiaoDian.Text = "自动中英文标点";
            this.chkAutoBiaoDian.UseVisualStyleBackColor = true;
            // 
            // checkBox10
            // 
            this.checkBox10.AutoSize = true;
            this.checkBox10.Location = new System.Drawing.Point(182, 20);
            this.checkBox10.Name = "checkBox10";
            this.checkBox10.Size = new System.Drawing.Size(75, 21);
            this.checkBox10.TabIndex = 4;
            this.checkBox10.Text = "首行缩进";
            this.checkBox10.UseVisualStyleBackColor = true;
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(13, 20);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(59, 17);
            this.label19.TabIndex = 5;
            this.label19.Text = "分段模式:";
            // 
            // checkBox5
            // 
            this.checkBox5.AutoSize = true;
            this.checkBox5.Checked = true;
            this.checkBox5.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox5.Location = new System.Drawing.Point(182, 46);
            this.checkBox5.Name = "checkBox5";
            this.checkBox5.Size = new System.Drawing.Size(75, 21);
            this.checkBox5.TabIndex = 4;
            this.checkBox5.Text = "自动空格";
            this.checkBox5.UseVisualStyleBackColor = true;
            // 
            // chkAutoFull2Half
            // 
            this.chkAutoFull2Half.AutoSize = true;
            this.chkAutoFull2Half.Checked = true;
            this.chkAutoFull2Half.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoFull2Half.Location = new System.Drawing.Point(11, 46);
            this.chkAutoFull2Half.Name = "chkAutoFull2Half";
            this.chkAutoFull2Half.Size = new System.Drawing.Size(111, 21);
            this.chkAutoFull2Half.TabIndex = 4;
            this.chkAutoFull2Half.Text = "自动全半角转换";
            this.chkAutoFull2Half.UseVisualStyleBackColor = true;
            // 
            // checkBox11
            // 
            this.checkBox11.AutoSize = true;
            this.checkBox11.Checked = true;
            this.checkBox11.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox11.Location = new System.Drawing.Point(13, 20);
            this.checkBox11.Name = "checkBox11";
            this.checkBox11.Size = new System.Drawing.Size(75, 21);
            this.checkBox11.TabIndex = 4;
            this.checkBox11.Text = "图文模式";
            this.checkBox11.UseVisualStyleBackColor = true;
            // 
            // checkBox1
            // 
            this.checkBox1.AutoSize = true;
            this.checkBox1.Checked = true;
            this.checkBox1.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox1.Location = new System.Drawing.Point(233, 20);
            this.checkBox1.Name = "checkBox1";
            this.checkBox1.Size = new System.Drawing.Size(99, 21);
            this.checkBox1.TabIndex = 1;
            this.checkBox1.Text = "图片自动缩放";
            this.checkBox1.UseVisualStyleBackColor = true;
            // 
            // tbLocalOcr
            // 
            this.tbLocalOcr.Controls.Add(this.groupBox20);
            this.tbLocalOcr.Controls.Add(this.groupBox8);
            this.tbLocalOcr.Controls.Add(this.grpLocalOcr);
            this.tbLocalOcr.Location = new System.Drawing.Point(4, 22);
            this.tbLocalOcr.Name = "tbLocalOcr";
            this.tbLocalOcr.Padding = new System.Windows.Forms.Padding(3);
            this.tbLocalOcr.Size = new System.Drawing.Size(373, 342);
            this.tbLocalOcr.TabIndex = 1;
            this.tbLocalOcr.Text = "本地识别";
            this.tbLocalOcr.UseVisualStyleBackColor = true;
            // 
            // groupBox20
            // 
            this.groupBox20.Controls.Add(this.lnkOpenLocalOcr);
            this.groupBox20.Controls.Add(this.lnkTestLocalOcr);
            this.groupBox20.Location = new System.Drawing.Point(3, 80);
            this.groupBox20.Name = "groupBox20";
            this.groupBox20.Size = new System.Drawing.Size(368, 44);
            this.groupBox20.TabIndex = 45;
            this.groupBox20.TabStop = false;
            this.groupBox20.Text = "操作";
            // 
            // lnkOpenLocalOcr
            // 
            this.lnkOpenLocalOcr.AutoSize = true;
            this.lnkOpenLocalOcr.Location = new System.Drawing.Point(11, 19);
            this.lnkOpenLocalOcr.Name = "lnkOpenLocalOcr";
            this.lnkOpenLocalOcr.Size = new System.Drawing.Size(133, 17);
            this.lnkOpenLocalOcr.TabIndex = 45;
            this.lnkOpenLocalOcr.TabStop = true;
            this.lnkOpenLocalOcr.Text = "启动/重启本地识别服务";
            this.lnkOpenLocalOcr.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lnkOpenLocalOcr_LinkClicked);
            // 
            // lnkTestLocalOcr
            // 
            this.lnkTestLocalOcr.AutoSize = true;
            this.lnkTestLocalOcr.Location = new System.Drawing.Point(164, 19);
            this.lnkTestLocalOcr.Name = "lnkTestLocalOcr";
            this.lnkTestLocalOcr.Size = new System.Drawing.Size(80, 17);
            this.lnkTestLocalOcr.TabIndex = 45;
            this.lnkTestLocalOcr.TabStop = true;
            this.lnkTestLocalOcr.Text = "检测服务状态";
            this.lnkTestLocalOcr.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lnkTestLocalOcr_LinkClicked);
            // 
            // groupBox8
            // 
            this.groupBox8.Controls.Add(this.lnkInstallLocalOcr);
            this.groupBox8.Controls.Add(this.numLocalOcrThread);
            this.groupBox8.Controls.Add(this.numLocalOcrPort);
            this.groupBox8.Controls.Add(this.label34);
            this.groupBox8.Controls.Add(this.lblLocalOcrPort);
            this.groupBox8.Controls.Add(this.pictureBox25);
            this.groupBox8.Controls.Add(this.pictureBox24);
            this.groupBox8.Controls.Add(this.pictureBox23);
            this.groupBox8.Controls.Add(this.chkLocalOcrEnable);
            this.groupBox8.Location = new System.Drawing.Point(3, 6);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.Size = new System.Drawing.Size(368, 67);
            this.groupBox8.TabIndex = 45;
            this.groupBox8.TabStop = false;
            this.groupBox8.Text = "本地识别配置";
            // 
            // lnkInstallLocalOcr
            // 
            this.lnkInstallLocalOcr.AutoSize = true;
            this.lnkInstallLocalOcr.Location = new System.Drawing.Point(149, 19);
            this.lnkInstallLocalOcr.Name = "lnkInstallLocalOcr";
            this.lnkInstallLocalOcr.Size = new System.Drawing.Size(133, 17);
            this.lnkInstallLocalOcr.TabIndex = 45;
            this.lnkInstallLocalOcr.TabStop = true;
            this.lnkInstallLocalOcr.Text = "安装/更新本地识别服务";
            this.lnkInstallLocalOcr.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lnkLocalOcr_LinkClicked);
            // 
            // numLocalOcrThread
            // 
            this.numLocalOcrThread.Location = new System.Drawing.Point(266, 39);
            this.numLocalOcrThread.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numLocalOcrThread.Name = "numLocalOcrThread";
            this.numLocalOcrThread.Size = new System.Drawing.Size(46, 23);
            this.numLocalOcrThread.TabIndex = 47;
            this.numLocalOcrThread.TabStop = false;
            this.numLocalOcrThread.Tag = "本地识别线程数";
            this.numLocalOcrThread.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.numLocalOcrThread.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // numLocalOcrPort
            // 
            this.numLocalOcrPort.Location = new System.Drawing.Point(75, 40);
            this.numLocalOcrPort.Maximum = new decimal(new int[] {
            9999,
            0,
            0,
            0});
            this.numLocalOcrPort.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numLocalOcrPort.Name = "numLocalOcrPort";
            this.numLocalOcrPort.Size = new System.Drawing.Size(52, 23);
            this.numLocalOcrPort.TabIndex = 47;
            this.numLocalOcrPort.Tag = "本地识别端口";
            this.numLocalOcrPort.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.numLocalOcrPort.Value = new decimal(new int[] {
            8080,
            0,
            0,
            0});
            // 
            // label34
            // 
            this.label34.AutoSize = true;
            this.label34.Location = new System.Drawing.Point(193, 44);
            this.label34.Name = "label34";
            this.label34.Size = new System.Drawing.Size(71, 17);
            this.label34.TabIndex = 46;
            this.label34.Text = "最大线程数:";
            // 
            // lblLocalOcrPort
            // 
            this.lblLocalOcrPort.AutoSize = true;
            this.lblLocalOcrPort.Location = new System.Drawing.Point(13, 45);
            this.lblLocalOcrPort.Name = "lblLocalOcrPort";
            this.lblLocalOcrPort.Size = new System.Drawing.Size(59, 17);
            this.lblLocalOcrPort.TabIndex = 46;
            this.lblLocalOcrPort.Text = "识别端口:";
            // 
            // pictureBox25
            // 
            this.pictureBox25.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox25.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox25.Location = new System.Drawing.Point(316, 37);
            this.pictureBox25.Name = "pictureBox25";
            this.pictureBox25.Size = new System.Drawing.Size(28, 24);
            this.pictureBox25.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox25.TabIndex = 44;
            this.pictureBox25.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox25, "功能：设置本地识别最大同时处理线程数量。\r\n说明：并不是越大越好！！！\r\n可以设置1-100之间的数字。\r\n\r\n注意：本地识别非常耗费性能，数字越大，电脑越卡！！" +
        "！\r\n");
            // 
            // pictureBox24
            // 
            this.pictureBox24.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox24.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox24.Location = new System.Drawing.Point(132, 37);
            this.pictureBox24.Name = "pictureBox24";
            this.pictureBox24.Size = new System.Drawing.Size(28, 24);
            this.pictureBox24.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox24.TabIndex = 44;
            this.pictureBox24.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox24, "功能：设置本地识别对外提供服务的端口。\r\n说明：一般不建议调整！！！\r\n可以设置1000-9999之间的端口！");
            // 
            // pictureBox23
            // 
            this.pictureBox23.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox23.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox23.Location = new System.Drawing.Point(110, 15);
            this.pictureBox23.Name = "pictureBox23";
            this.pictureBox23.Size = new System.Drawing.Size(28, 24);
            this.pictureBox23.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox23.TabIndex = 44;
            this.pictureBox23.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox23, "功能：是否开启本地库识别。\r\n说明：开启后，可通过本地库进行OCR识别。\r\n本地识别：速度更稳定，但受限于特征库，识别结果没网络识别精确！\r\n\r\n可根据实际需求，" +
        "进行选择！");
            // 
            // chkLocalOcrEnable
            // 
            this.chkLocalOcrEnable.AutoSize = true;
            this.chkLocalOcrEnable.Checked = true;
            this.chkLocalOcrEnable.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkLocalOcrEnable.Location = new System.Drawing.Point(13, 19);
            this.chkLocalOcrEnable.Name = "chkLocalOcrEnable";
            this.chkLocalOcrEnable.Size = new System.Drawing.Size(99, 21);
            this.chkLocalOcrEnable.TabIndex = 4;
            this.chkLocalOcrEnable.Text = "启用本地识别";
            this.chkLocalOcrEnable.UseVisualStyleBackColor = true;
            // 
            // grpLocalOcr
            // 
            this.grpLocalOcr.Location = new System.Drawing.Point(3, 130);
            this.grpLocalOcr.Name = "grpLocalOcr";
            this.grpLocalOcr.Size = new System.Drawing.Size(370, 170);
            this.grpLocalOcr.TabIndex = 45;
            this.grpLocalOcr.TabStop = false;
            this.grpLocalOcr.Text = "选择启用的本地识别引擎";
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.groupBox19);
            this.tabPage2.Controls.Add(this.groupBox12);
            this.tabPage2.Controls.Add(this.groupBox3);
            this.tabPage2.Location = new System.Drawing.Point(4, 26);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(387, 370);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "截屏";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // groupBox19
            // 
            this.groupBox19.Controls.Add(this.numericUpDown1);
            this.groupBox19.Controls.Add(this.label36);
            this.groupBox19.Controls.Add(this.label37);
            this.groupBox19.Controls.Add(this.pictureBox11);
            this.groupBox19.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBox19.Location = new System.Drawing.Point(7, 312);
            this.groupBox19.Name = "groupBox19";
            this.groupBox19.Size = new System.Drawing.Size(374, 58);
            this.groupBox19.TabIndex = 29;
            this.groupBox19.TabStop = false;
            this.groupBox19.Text = "其他设置";
            // 
            // numericUpDown1
            // 
            this.numericUpDown1.Location = new System.Drawing.Point(67, 21);
            this.numericUpDown1.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numericUpDown1.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numericUpDown1.Name = "numericUpDown1";
            this.numericUpDown1.Size = new System.Drawing.Size(46, 23);
            this.numericUpDown1.TabIndex = 27;
            this.numericUpDown1.Tag = "截图延时";
            this.numericUpDown1.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // label36
            // 
            this.label36.AutoSize = true;
            this.label36.Location = new System.Drawing.Point(7, 25);
            this.label36.Name = "label36";
            this.label36.Size = new System.Drawing.Size(59, 17);
            this.label36.TabIndex = 2;
            this.label36.Text = "截图延时:";
            // 
            // label37
            // 
            this.label37.AutoSize = true;
            this.label37.Location = new System.Drawing.Point(119, 25);
            this.label37.Name = "label37";
            this.label37.Size = new System.Drawing.Size(20, 17);
            this.label37.TabIndex = 2;
            this.label37.Text = "秒";
            // 
            // pictureBox11
            // 
            this.pictureBox11.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox11.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox11.Location = new System.Drawing.Point(139, 19);
            this.pictureBox11.Name = "pictureBox11";
            this.pictureBox11.Size = new System.Drawing.Size(28, 24);
            this.pictureBox11.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox11.TabIndex = 42;
            this.pictureBox11.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox11, "功能：延时截图前，等待指定时间后触发截图。\r\n说明：仅针对延时截图功能，普通截图不延时\r\n");
            // 
            // groupBox12
            // 
            this.groupBox12.Controls.Add(this.numCaptureBorderWidth);
            this.groupBox12.Controls.Add(this.imageButton1);
            this.groupBox12.Controls.Add(this.label41);
            this.groupBox12.Controls.Add(this.chkZoom);
            this.groupBox12.Controls.Add(this.pictureBox2);
            this.groupBox12.Controls.Add(this.label7);
            this.groupBox12.Controls.Add(this.chkCaptureAnimal);
            this.groupBox12.Controls.Add(this.chkCircleFangDa);
            this.groupBox12.Controls.Add(this.chkCrocessLine);
            this.groupBox12.Controls.Add(this.chkAutoWindow);
            this.groupBox12.Controls.Add(this.chkAutoControl);
            this.groupBox12.Controls.Add(this.label6);
            this.groupBox12.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBox12.Location = new System.Drawing.Point(8, 6);
            this.groupBox12.Name = "groupBox12";
            this.groupBox12.Size = new System.Drawing.Size(374, 126);
            this.groupBox12.TabIndex = 46;
            this.groupBox12.TabStop = false;
            this.groupBox12.Text = "通用";
            // 
            // numCaptureBorderWidth
            // 
            this.numCaptureBorderWidth.Location = new System.Drawing.Point(215, 96);
            this.numCaptureBorderWidth.Name = "numCaptureBorderWidth";
            this.numCaptureBorderWidth.Size = new System.Drawing.Size(46, 23);
            this.numCaptureBorderWidth.TabIndex = 27;
            this.numCaptureBorderWidth.Tag = "截图边框宽度";
            this.numCaptureBorderWidth.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // imageButton1
            // 
            this.imageButton1.BackColor = System.Drawing.Color.White;
            this.imageButton1.Image = ((System.Drawing.Image)(resources.GetObject("imageButton1.Image")));
            this.imageButton1.ImageColor = System.Drawing.Color.Black;
            this.imageButton1.Location = new System.Drawing.Point(85, 97);
            this.imageButton1.Name = "imageButton1";
            this.imageButton1.Size = new System.Drawing.Size(25, 20);
            this.imageButton1.TabIndex = 44;
            this.imageButton1.Tag = "截图边框颜色";
            this.imageButton1.UseVisualStyleBackColor = false;
            this.imageButton1.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // label41
            // 
            this.label41.AutoSize = true;
            this.label41.Location = new System.Drawing.Point(13, 101);
            this.label41.Name = "label41";
            this.label41.Size = new System.Drawing.Size(71, 17);
            this.label41.TabIndex = 43;
            this.label41.Text = "截图边框色:";
            // 
            // chkZoom
            // 
            this.chkZoom.AutoSize = true;
            this.chkZoom.Checked = true;
            this.chkZoom.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkZoom.Location = new System.Drawing.Point(13, 20);
            this.chkZoom.Name = "chkZoom";
            this.chkZoom.Size = new System.Drawing.Size(87, 21);
            this.chkZoom.TabIndex = 26;
            this.chkZoom.Text = "显示放大镜";
            this.chkZoom.UseVisualStyleBackColor = true;
            // 
            // pictureBox2
            // 
            this.pictureBox2.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox2.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox2.Location = new System.Drawing.Point(258, 70);
            this.pictureBox2.Name = "pictureBox2";
            this.pictureBox2.Size = new System.Drawing.Size(28, 24);
            this.pictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox2.TabIndex = 42;
            this.pictureBox2.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox2, "功能：自动检测窗口所有可见的元素。\r\n说明：可在截图过程中按【Ctrl键】切换窗口模式");
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(263, 101);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(32, 17);
            this.label7.TabIndex = 2;
            this.label7.Text = "像素";
            // 
            // chkCaptureAnimal
            // 
            this.chkCaptureAnimal.AutoSize = true;
            this.chkCaptureAnimal.Checked = true;
            this.chkCaptureAnimal.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkCaptureAnimal.Location = new System.Drawing.Point(13, 46);
            this.chkCaptureAnimal.Name = "chkCaptureAnimal";
            this.chkCaptureAnimal.Size = new System.Drawing.Size(99, 21);
            this.chkCaptureAnimal.TabIndex = 0;
            this.chkCaptureAnimal.Text = "截图动画效果";
            this.chkCaptureAnimal.UseVisualStyleBackColor = true;
            // 
            // chkCircleFangDa
            // 
            this.chkCircleFangDa.AutoSize = true;
            this.chkCircleFangDa.Location = new System.Drawing.Point(134, 20);
            this.chkCircleFangDa.Name = "chkCircleFangDa";
            this.chkCircleFangDa.Size = new System.Drawing.Size(87, 21);
            this.chkCircleFangDa.TabIndex = 26;
            this.chkCircleFangDa.Text = "圆形放大镜";
            this.chkCircleFangDa.UseVisualStyleBackColor = true;
            // 
            // chkCrocessLine
            // 
            this.chkCrocessLine.AutoSize = true;
            this.chkCrocessLine.Location = new System.Drawing.Point(134, 46);
            this.chkCrocessLine.Name = "chkCrocessLine";
            this.chkCrocessLine.Size = new System.Drawing.Size(111, 21);
            this.chkCrocessLine.TabIndex = 26;
            this.chkCrocessLine.Text = "显示全屏十字线";
            this.chkCrocessLine.UseVisualStyleBackColor = true;
            // 
            // chkAutoWindow
            // 
            this.chkAutoWindow.AutoSize = true;
            this.chkAutoWindow.Checked = true;
            this.chkAutoWindow.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoWindow.Location = new System.Drawing.Point(13, 74);
            this.chkAutoWindow.Name = "chkAutoWindow";
            this.chkAutoWindow.Size = new System.Drawing.Size(99, 21);
            this.chkAutoWindow.TabIndex = 26;
            this.chkAutoWindow.Text = "自动检测窗口";
            this.chkAutoWindow.UseVisualStyleBackColor = true;
            // 
            // chkAutoControl
            // 
            this.chkAutoControl.AutoSize = true;
            this.chkAutoControl.Checked = true;
            this.chkAutoControl.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoControl.Location = new System.Drawing.Point(134, 74);
            this.chkAutoControl.Name = "chkAutoControl";
            this.chkAutoControl.Size = new System.Drawing.Size(123, 21);
            this.chkAutoControl.TabIndex = 26;
            this.chkAutoControl.Text = "自动检测窗口元素";
            this.chkAutoControl.UseVisualStyleBackColor = true;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(155, 101);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(59, 17);
            this.label6.TabIndex = 2;
            this.label6.Text = "边框宽度:";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.btnChangeCaptureLocation);
            this.groupBox3.Controls.Add(this.btnOpenCaptureLocation);
            this.groupBox3.Controls.Add(this.txtCaptureFileName);
            this.groupBox3.Controls.Add(this.lblCaptureFileName);
            this.groupBox3.Controls.Add(this.label8);
            this.groupBox3.Controls.Add(this.numMaxHistoryCount);
            this.groupBox3.Controls.Add(this.label11);
            this.groupBox3.Controls.Add(this.label9);
            this.groupBox3.Controls.Add(this.txtCaptureLocation);
            this.groupBox3.Controls.Add(this.label10);
            this.groupBox3.Controls.Add(this.chkCaptureTip);
            this.groupBox3.Controls.Add(this.chkAutoSaveCapture);
            this.groupBox3.Controls.Add(this.chkSaveToClipborad);
            this.groupBox3.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBox3.Location = new System.Drawing.Point(8, 136);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(374, 172);
            this.groupBox3.TabIndex = 28;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "截图完成时";
            // 
            // btnChangeCaptureLocation
            // 
            this.btnChangeCaptureLocation.Location = new System.Drawing.Point(314, 115);
            this.btnChangeCaptureLocation.Name = "btnChangeCaptureLocation";
            this.btnChangeCaptureLocation.Size = new System.Drawing.Size(52, 25);
            this.btnChangeCaptureLocation.TabIndex = 7;
            this.btnChangeCaptureLocation.Text = "更改";
            this.btnChangeCaptureLocation.UseVisualStyleBackColor = true;
            this.btnChangeCaptureLocation.Click += new System.EventHandler(this.btnChangeCaptureLocation_Click);
            // 
            // btnOpenCaptureLocation
            // 
            this.btnOpenCaptureLocation.Location = new System.Drawing.Point(258, 115);
            this.btnOpenCaptureLocation.Name = "btnOpenCaptureLocation";
            this.btnOpenCaptureLocation.Size = new System.Drawing.Size(52, 25);
            this.btnOpenCaptureLocation.TabIndex = 8;
            this.btnOpenCaptureLocation.Text = "打开";
            this.btnOpenCaptureLocation.UseVisualStyleBackColor = true;
            this.btnOpenCaptureLocation.Click += new System.EventHandler(this.btnOpenCaptureLocation_Click);
            // 
            // txtCaptureFileName
            // 
            this.txtCaptureFileName.Location = new System.Drawing.Point(54, 47);
            this.txtCaptureFileName.Name = "txtCaptureFileName";
            this.txtCaptureFileName.Size = new System.Drawing.Size(199, 23);
            this.txtCaptureFileName.TabIndex = 6;
            this.txtCaptureFileName.Tag = "截图文件名";
            this.txtCaptureFileName.Text = "IMG_%n-%y-%r_%s-%f-%m.png";
            this.txtCaptureFileName.TextChanged += new System.EventHandler(this.txtCaptureFileName_TextChanged);
            // 
            // lblCaptureFileName
            // 
            this.lblCaptureFileName.Location = new System.Drawing.Point(52, 74);
            this.lblCaptureFileName.Name = "lblCaptureFileName";
            this.lblCaptureFileName.Size = new System.Drawing.Size(201, 36);
            this.lblCaptureFileName.TabIndex = 5;
            this.lblCaptureFileName.Text = "示例：\r\n截图_2021-03-15_19-03-01.png";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(11, 148);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(107, 17);
            this.label8.TabIndex = 2;
            this.label8.Text = "最大历史记录数量:";
            // 
            // numMaxHistoryCount
            // 
            this.numMaxHistoryCount.Location = new System.Drawing.Point(121, 143);
            this.numMaxHistoryCount.Name = "numMaxHistoryCount";
            this.numMaxHistoryCount.Size = new System.Drawing.Size(44, 23);
            this.numMaxHistoryCount.TabIndex = 27;
            this.numMaxHistoryCount.Tag = "最大历史记录数量";
            this.numMaxHistoryCount.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(255, 44);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(122, 68);
            this.label11.TabIndex = 5;
            this.label11.Text = "%n 年份；%y 月份\r\n%r 天数；%s 小时\r\n%f 分钟；%m 秒钟\r\n%t 时间戳；%g 随机";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(6, 51);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(47, 17);
            this.label9.TabIndex = 5;
            this.label9.Text = "文件名:";
            // 
            // txtCaptureLocation
            // 
            this.txtCaptureLocation.Location = new System.Drawing.Point(53, 116);
            this.txtCaptureLocation.Name = "txtCaptureLocation";
            this.txtCaptureLocation.ReadOnly = true;
            this.txtCaptureLocation.Size = new System.Drawing.Size(200, 23);
            this.txtCaptureLocation.TabIndex = 6;
            this.txtCaptureLocation.Tag = "截图文件保存路径";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(17, 120);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(35, 17);
            this.label10.TabIndex = 5;
            this.label10.Text = "路径:";
            // 
            // chkCaptureTip
            // 
            this.chkCaptureTip.AutoSize = true;
            this.chkCaptureTip.Checked = true;
            this.chkCaptureTip.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkCaptureTip.Location = new System.Drawing.Point(96, 23);
            this.chkCaptureTip.Name = "chkCaptureTip";
            this.chkCaptureTip.Size = new System.Drawing.Size(107, 21);
            this.chkCaptureTip.TabIndex = 0;
            this.chkCaptureTip.Text = "显示Toast通知";
            this.chkCaptureTip.UseVisualStyleBackColor = true;
            // 
            // chkAutoSaveCapture
            // 
            this.chkAutoSaveCapture.AutoSize = true;
            this.chkAutoSaveCapture.Checked = true;
            this.chkAutoSaveCapture.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoSaveCapture.Location = new System.Drawing.Point(13, 23);
            this.chkAutoSaveCapture.Name = "chkAutoSaveCapture";
            this.chkAutoSaveCapture.Size = new System.Drawing.Size(75, 21);
            this.chkAutoSaveCapture.TabIndex = 0;
            this.chkAutoSaveCapture.Text = "自动保存";
            this.chkAutoSaveCapture.UseVisualStyleBackColor = true;
            // 
            // chkSaveToClipborad
            // 
            this.chkSaveToClipborad.AutoSize = true;
            this.chkSaveToClipborad.Checked = true;
            this.chkSaveToClipborad.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkSaveToClipborad.Location = new System.Drawing.Point(209, 23);
            this.chkSaveToClipborad.Name = "chkSaveToClipborad";
            this.chkSaveToClipborad.Size = new System.Drawing.Size(99, 21);
            this.chkSaveToClipborad.TabIndex = 0;
            this.chkSaveToClipborad.Text = "复制到粘贴板";
            this.chkSaveToClipborad.UseVisualStyleBackColor = true;
            // 
            // tabPage5
            // 
            this.tabPage5.Controls.Add(this.groupBox14);
            this.tabPage5.Controls.Add(this.groupBox4);
            this.tabPage5.Location = new System.Drawing.Point(4, 26);
            this.tabPage5.Name = "tabPage5";
            this.tabPage5.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage5.Size = new System.Drawing.Size(387, 370);
            this.tabPage5.TabIndex = 4;
            this.tabPage5.Text = "贴图";
            this.tabPage5.UseVisualStyleBackColor = true;
            // 
            // groupBox14
            // 
            this.groupBox14.Controls.Add(this.btnTieTuShadowColor);
            this.groupBox14.Controls.Add(this.label14);
            this.groupBox14.Controls.Add(this.label18);
            this.groupBox14.Controls.Add(this.chkTieTuFocus);
            this.groupBox14.Controls.Add(this.label22);
            this.groupBox14.Controls.Add(this.chkTieTuShark);
            this.groupBox14.Controls.Add(this.label15);
            this.groupBox14.Controls.Add(this.numShadowWidth);
            this.groupBox14.Controls.Add(this.chkTieTuUseCaptureLocation);
            this.groupBox14.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBox14.Location = new System.Drawing.Point(8, 6);
            this.groupBox14.Name = "groupBox14";
            this.groupBox14.Size = new System.Drawing.Size(374, 102);
            this.groupBox14.TabIndex = 45;
            this.groupBox14.TabStop = false;
            this.groupBox14.Text = "通用";
            // 
            // btnTieTuShadowColor
            // 
            this.btnTieTuShadowColor.BackColor = System.Drawing.Color.White;
            this.btnTieTuShadowColor.Image = ((System.Drawing.Image)(resources.GetObject("btnTieTuShadowColor.Image")));
            this.btnTieTuShadowColor.ImageColor = System.Drawing.Color.Black;
            this.btnTieTuShadowColor.Location = new System.Drawing.Point(88, 22);
            this.btnTieTuShadowColor.Name = "btnTieTuShadowColor";
            this.btnTieTuShadowColor.Size = new System.Drawing.Size(25, 20);
            this.btnTieTuShadowColor.TabIndex = 4;
            this.btnTieTuShadowColor.Tag = "贴图窗口阴影色";
            this.btnTieTuShadowColor.UseVisualStyleBackColor = false;
            this.btnTieTuShadowColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(13, 26);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(71, 17);
            this.label14.TabIndex = 3;
            this.label14.Text = "窗口阴影色:";
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(254, 26);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(32, 17);
            this.label18.TabIndex = 29;
            this.label18.Text = "像素";
            // 
            // chkTieTuFocus
            // 
            this.chkTieTuFocus.AutoSize = true;
            this.chkTieTuFocus.Checked = true;
            this.chkTieTuFocus.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkTieTuFocus.Location = new System.Drawing.Point(137, 77);
            this.chkTieTuFocus.Name = "chkTieTuFocus";
            this.chkTieTuFocus.Size = new System.Drawing.Size(75, 21);
            this.chkTieTuFocus.TabIndex = 27;
            this.chkTieTuFocus.Text = "激活窗口";
            this.chkTieTuFocus.UseVisualStyleBackColor = true;
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(137, 26);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(59, 17);
            this.label22.TabIndex = 28;
            this.label22.Text = "阴影宽度:";
            // 
            // chkTieTuShark
            // 
            this.chkTieTuShark.AutoSize = true;
            this.chkTieTuShark.Checked = true;
            this.chkTieTuShark.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkTieTuShark.Location = new System.Drawing.Point(79, 77);
            this.chkTieTuShark.Name = "chkTieTuShark";
            this.chkTieTuShark.Size = new System.Drawing.Size(51, 21);
            this.chkTieTuShark.TabIndex = 27;
            this.chkTieTuShark.Text = "闪烁";
            this.chkTieTuShark.UseVisualStyleBackColor = true;
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(13, 78);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(59, 17);
            this.label15.TabIndex = 3;
            this.label15.Text = "新建窗口:";
            // 
            // numShadowWidth
            // 
            this.numShadowWidth.Location = new System.Drawing.Point(199, 22);
            this.numShadowWidth.Name = "numShadowWidth";
            this.numShadowWidth.Size = new System.Drawing.Size(52, 23);
            this.numShadowWidth.TabIndex = 30;
            this.numShadowWidth.Tag = "贴图窗口阴影宽度";
            this.numShadowWidth.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // chkTieTuUseCaptureLocation
            // 
            this.chkTieTuUseCaptureLocation.AutoSize = true;
            this.chkTieTuUseCaptureLocation.Checked = true;
            this.chkTieTuUseCaptureLocation.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkTieTuUseCaptureLocation.Location = new System.Drawing.Point(13, 52);
            this.chkTieTuUseCaptureLocation.Name = "chkTieTuUseCaptureLocation";
            this.chkTieTuUseCaptureLocation.Size = new System.Drawing.Size(171, 21);
            this.chkTieTuUseCaptureLocation.TabIndex = 27;
            this.chkTieTuUseCaptureLocation.Text = "截图贴图时使用截屏的位置";
            this.chkTieTuUseCaptureLocation.UseVisualStyleBackColor = true;
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.numTieTuMaxWidth);
            this.groupBox4.Controls.Add(this.numTieTuBorderWidth);
            this.groupBox4.Controls.Add(this.btnTieTuDefaultFont);
            this.groupBox4.Controls.Add(this.btnTieTuBackColor);
            this.groupBox4.Controls.Add(this.btnTieTuFont);
            this.groupBox4.Controls.Add(this.btnTieTuFontColor);
            this.groupBox4.Controls.Add(this.chkUseContentColor);
            this.groupBox4.Controls.Add(this.chkIngoreHtml);
            this.groupBox4.Controls.Add(this.label24);
            this.groupBox4.Controls.Add(this.label23);
            this.groupBox4.Controls.Add(this.label20);
            this.groupBox4.Controls.Add(this.label21);
            this.groupBox4.Controls.Add(this.lblTieTuLabel);
            this.groupBox4.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBox4.Location = new System.Drawing.Point(8, 115);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(374, 202);
            this.groupBox4.TabIndex = 28;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "文字转图片";
            // 
            // numTieTuMaxWidth
            // 
            this.numTieTuMaxWidth.Location = new System.Drawing.Point(250, 41);
            this.numTieTuMaxWidth.Maximum = new decimal(new int[] {
            2000,
            0,
            0,
            0});
            this.numTieTuMaxWidth.Minimum = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numTieTuMaxWidth.Name = "numTieTuMaxWidth";
            this.numTieTuMaxWidth.Size = new System.Drawing.Size(52, 23);
            this.numTieTuMaxWidth.TabIndex = 30;
            this.numTieTuMaxWidth.Tag = "贴图图片最大宽度";
            this.numTieTuMaxWidth.Value = new decimal(new int[] {
            600,
            0,
            0,
            0});
            // 
            // numTieTuBorderWidth
            // 
            this.numTieTuBorderWidth.Location = new System.Drawing.Point(65, 41);
            this.numTieTuBorderWidth.Name = "numTieTuBorderWidth";
            this.numTieTuBorderWidth.Size = new System.Drawing.Size(52, 23);
            this.numTieTuBorderWidth.TabIndex = 30;
            this.numTieTuBorderWidth.Tag = "贴图页边距宽度";
            this.numTieTuBorderWidth.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // btnTieTuDefaultFont
            // 
            this.btnTieTuDefaultFont.Location = new System.Drawing.Point(298, 167);
            this.btnTieTuDefaultFont.Name = "btnTieTuDefaultFont";
            this.btnTieTuDefaultFont.Size = new System.Drawing.Size(69, 26);
            this.btnTieTuDefaultFont.TabIndex = 43;
            this.btnTieTuDefaultFont.Tag = "";
            this.btnTieTuDefaultFont.Text = "默认字体";
            this.btnTieTuDefaultFont.UseVisualStyleBackColor = false;
            this.btnTieTuDefaultFont.Click += new System.EventHandler(this.btnContentDefault_Click);
            // 
            // btnTieTuBackColor
            // 
            this.btnTieTuBackColor.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.btnTieTuBackColor.Image = ((System.Drawing.Image)(resources.GetObject("btnTieTuBackColor.Image")));
            this.btnTieTuBackColor.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnTieTuBackColor.ImageColor = System.Drawing.Color.Black;
            this.btnTieTuBackColor.Location = new System.Drawing.Point(298, 139);
            this.btnTieTuBackColor.Name = "btnTieTuBackColor";
            this.btnTieTuBackColor.Size = new System.Drawing.Size(69, 26);
            this.btnTieTuBackColor.TabIndex = 42;
            this.btnTieTuBackColor.Tag = "贴图背景颜色";
            this.btnTieTuBackColor.Text = "背景色";
            this.btnTieTuBackColor.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnTieTuBackColor.UseVisualStyleBackColor = false;
            this.btnTieTuBackColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // btnTieTuFont
            // 
            this.btnTieTuFont.Location = new System.Drawing.Point(298, 83);
            this.btnTieTuFont.Name = "btnTieTuFont";
            this.btnTieTuFont.Size = new System.Drawing.Size(71, 26);
            this.btnTieTuFont.TabIndex = 40;
            this.btnTieTuFont.TabStop = false;
            this.btnTieTuFont.Tag = "贴图文字字体";
            this.btnTieTuFont.Text = "字体";
            this.btnTieTuFont.UseVisualStyleBackColor = false;
            this.btnTieTuFont.Click += new System.EventHandler(this.btnFont_Click);
            // 
            // btnTieTuFontColor
            // 
            this.btnTieTuFontColor.Image = ((System.Drawing.Image)(resources.GetObject("btnTieTuFontColor.Image")));
            this.btnTieTuFontColor.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnTieTuFontColor.ImageColor = System.Drawing.Color.Black;
            this.btnTieTuFontColor.Location = new System.Drawing.Point(298, 111);
            this.btnTieTuFontColor.Name = "btnTieTuFontColor";
            this.btnTieTuFontColor.Size = new System.Drawing.Size(71, 26);
            this.btnTieTuFontColor.TabIndex = 41;
            this.btnTieTuFontColor.Tag = "贴图文字颜色";
            this.btnTieTuFontColor.Text = "文本色";
            this.btnTieTuFontColor.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnTieTuFontColor.UseVisualStyleBackColor = false;
            this.btnTieTuFontColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // chkUseContentColor
            // 
            this.chkUseContentColor.AutoSize = true;
            this.chkUseContentColor.Checked = true;
            this.chkUseContentColor.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkUseContentColor.Location = new System.Drawing.Point(15, 69);
            this.chkUseContentColor.Name = "chkUseContentColor";
            this.chkUseContentColor.Size = new System.Drawing.Size(147, 21);
            this.chkUseContentColor.TabIndex = 27;
            this.chkUseContentColor.Text = "使用界面设置中的字体";
            this.chkUseContentColor.UseVisualStyleBackColor = true;
            this.chkUseContentColor.CheckedChanged += new System.EventHandler(this.chkUseContentColor_CheckedChanged);
            // 
            // chkIngoreHtml
            // 
            this.chkIngoreHtml.AutoSize = true;
            this.chkIngoreHtml.Location = new System.Drawing.Point(16, 20);
            this.chkIngoreHtml.Name = "chkIngoreHtml";
            this.chkIngoreHtml.Size = new System.Drawing.Size(210, 21);
            this.chkIngoreHtml.TabIndex = 27;
            this.chkIngoreHtml.Text = "忽略Html格式以文字方式生成图片";
            this.chkIngoreHtml.UseVisualStyleBackColor = true;
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(187, 45);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(59, 17);
            this.label24.TabIndex = 28;
            this.label24.Text = "最大宽度:";
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(309, 45);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(32, 17);
            this.label23.TabIndex = 29;
            this.label23.Text = "像素";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(14, 45);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(47, 17);
            this.label20.TabIndex = 28;
            this.label20.Text = "页边距:";
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(124, 45);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(32, 17);
            this.label21.TabIndex = 29;
            this.label21.Text = "像素";
            // 
            // lblTieTuLabel
            // 
            this.lblTieTuLabel.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.lblTieTuLabel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.lblTieTuLabel.Location = new System.Drawing.Point(16, 88);
            this.lblTieTuLabel.Name = "lblTieTuLabel";
            this.lblTieTuLabel.Size = new System.Drawing.Size(276, 104);
            this.lblTieTuLabel.TabIndex = 19;
            this.lblTieTuLabel.Text = "预览\r\nAaOo012345";
            this.lblTieTuLabel.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.groupBox13);
            this.tabPage3.Controls.Add(this.groupBox6);
            this.tabPage3.Location = new System.Drawing.Point(4, 26);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage3.Size = new System.Drawing.Size(387, 370);
            this.tabPage3.TabIndex = 7;
            this.tabPage3.Text = "工具";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // groupBox13
            // 
            this.groupBox13.Controls.Add(this.cmbHex);
            this.groupBox13.Controls.Add(this.label5);
            this.groupBox13.Controls.Add(this.chkHexUpper);
            this.groupBox13.Location = new System.Drawing.Point(8, 139);
            this.groupBox13.Name = "groupBox13";
            this.groupBox13.Size = new System.Drawing.Size(374, 50);
            this.groupBox13.TabIndex = 31;
            this.groupBox13.TabStop = false;
            this.groupBox13.Text = "取色器";
            // 
            // cmbHex
            // 
            this.cmbHex.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbHex.FormattingEnabled = true;
            this.cmbHex.Items.AddRange(new object[] {
            "RGB",
            "HEX"});
            this.cmbHex.Location = new System.Drawing.Point(71, 16);
            this.cmbHex.Name = "cmbHex";
            this.cmbHex.Size = new System.Drawing.Size(82, 25);
            this.cmbHex.TabIndex = 3;
            this.cmbHex.Tag = "取色器文字样式";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(13, 20);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(59, 17);
            this.label5.TabIndex = 2;
            this.label5.Text = "取色器值:";
            // 
            // chkHexUpper
            // 
            this.chkHexUpper.AutoSize = true;
            this.chkHexUpper.Location = new System.Drawing.Point(181, 18);
            this.chkHexUpper.Name = "chkHexUpper";
            this.chkHexUpper.Size = new System.Drawing.Size(111, 21);
            this.chkHexUpper.TabIndex = 26;
            this.chkHexUpper.Text = "HEX颜色值大写";
            this.chkHexUpper.UseVisualStyleBackColor = true;
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.checkBox2);
            this.groupBox6.Controls.Add(this.cmbRulerOpacity);
            this.groupBox6.Controls.Add(this.cmbRulerTheme);
            this.groupBox6.Controls.Add(this.label17);
            this.groupBox6.Controls.Add(this.cmbRulerUnit);
            this.groupBox6.Controls.Add(this.label13);
            this.groupBox6.Controls.Add(this.label16);
            this.groupBox6.Location = new System.Drawing.Point(8, 6);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(374, 125);
            this.groupBox6.TabIndex = 27;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "标尺";
            // 
            // checkBox2
            // 
            this.checkBox2.AutoSize = true;
            this.checkBox2.Checked = true;
            this.checkBox2.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox2.Location = new System.Drawing.Point(13, 20);
            this.checkBox2.Name = "checkBox2";
            this.checkBox2.Size = new System.Drawing.Size(75, 21);
            this.checkBox2.TabIndex = 30;
            this.checkBox2.Text = "标尺置顶";
            this.checkBox2.UseVisualStyleBackColor = true;
            // 
            // cmbRulerOpacity
            // 
            this.cmbRulerOpacity.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbRulerOpacity.FormattingEnabled = true;
            this.cmbRulerOpacity.Location = new System.Drawing.Point(71, 68);
            this.cmbRulerOpacity.Name = "cmbRulerOpacity";
            this.cmbRulerOpacity.Size = new System.Drawing.Size(121, 25);
            this.cmbRulerOpacity.TabIndex = 28;
            this.cmbRulerOpacity.Tag = "标尺透明度";
            // 
            // cmbRulerTheme
            // 
            this.cmbRulerTheme.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbRulerTheme.FormattingEnabled = true;
            this.cmbRulerTheme.Location = new System.Drawing.Point(71, 42);
            this.cmbRulerTheme.Name = "cmbRulerTheme";
            this.cmbRulerTheme.Size = new System.Drawing.Size(121, 25);
            this.cmbRulerTheme.TabIndex = 28;
            this.cmbRulerTheme.Tag = "标尺默认主题";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(13, 72);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(55, 17);
            this.label17.TabIndex = 27;
            this.label17.Text = "透 明 度:";
            // 
            // cmbRulerUnit
            // 
            this.cmbRulerUnit.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbRulerUnit.FormattingEnabled = true;
            this.cmbRulerUnit.Location = new System.Drawing.Point(71, 94);
            this.cmbRulerUnit.Name = "cmbRulerUnit";
            this.cmbRulerUnit.Size = new System.Drawing.Size(121, 25);
            this.cmbRulerUnit.TabIndex = 28;
            this.cmbRulerUnit.Tag = "标尺计量单位";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(13, 46);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(59, 17);
            this.label13.TabIndex = 27;
            this.label13.Text = "默认主题:";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(13, 98);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(59, 17);
            this.label16.TabIndex = 27;
            this.label16.Text = "计量单位:";
            // 
            // tbShortKey
            // 
            this.tbShortKey.Controls.Add(this.tabHotKeys);
            this.tbShortKey.Location = new System.Drawing.Point(4, 26);
            this.tbShortKey.Name = "tbShortKey";
            this.tbShortKey.Padding = new System.Windows.Forms.Padding(3);
            this.tbShortKey.Size = new System.Drawing.Size(387, 370);
            this.tbShortKey.TabIndex = 2;
            this.tbShortKey.Text = "快捷键";
            this.tbShortKey.UseVisualStyleBackColor = true;
            // 
            // tabHotKeys
            // 
            this.tabHotKeys.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabHotKeys.Location = new System.Drawing.Point(3, 3);
            this.tabHotKeys.Name = "tabHotKeys";
            this.tabHotKeys.SelectedIndex = 0;
            this.tabHotKeys.Size = new System.Drawing.Size(381, 364);
            this.tabHotKeys.TabIndex = 0;
            // 
            // tabPage6
            // 
            this.tabPage6.Controls.Add(this.picIcon);
            this.tabPage6.Controls.Add(this.groupBox9);
            this.tabPage6.Controls.Add(this.groupBox5);
            this.tabPage6.Controls.Add(this.label38);
            this.tabPage6.Controls.Add(this.lblCopyright);
            this.tabPage6.Controls.Add(this.btnUpgrade);
            this.tabPage6.Controls.Add(this.lblVersion);
            this.tabPage6.Controls.Add(this.lnkWebSite);
            this.tabPage6.Controls.Add(this.lblName);
            this.tabPage6.Location = new System.Drawing.Point(4, 26);
            this.tabPage6.Name = "tabPage6";
            this.tabPage6.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage6.Size = new System.Drawing.Size(387, 370);
            this.tabPage6.TabIndex = 5;
            this.tabPage6.Text = "关于";
            this.tabPage6.UseVisualStyleBackColor = true;
            // 
            // groupBox9
            // 
            this.groupBox9.Controls.Add(this.pictureBox4);
            this.groupBox9.Controls.Add(this.pictureBox7);
            this.groupBox9.Controls.Add(this.pictureBox6);
            this.groupBox9.Controls.Add(this.pictureBox5);
            this.groupBox9.Controls.Add(this.linkLabel2);
            this.groupBox9.Controls.Add(this.linkLabel4);
            this.groupBox9.Controls.Add(this.linkLabel3);
            this.groupBox9.Controls.Add(this.linkLabel1);
            this.groupBox9.Location = new System.Drawing.Point(6, 148);
            this.groupBox9.Name = "groupBox9";
            this.groupBox9.Size = new System.Drawing.Size(374, 85);
            this.groupBox9.TabIndex = 34;
            this.groupBox9.TabStop = false;
            this.groupBox9.Text = "联系我们";
            // 
            // pictureBox4
            // 
            this.pictureBox4.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox4.Image = global::OCRTools.Properties.Resources.qqQun;
            this.pictureBox4.Location = new System.Drawing.Point(21, 49);
            this.pictureBox4.Name = "pictureBox4";
            this.pictureBox4.Size = new System.Drawing.Size(20, 20);
            this.pictureBox4.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox4.TabIndex = 43;
            this.pictureBox4.TabStop = false;
            // 
            // pictureBox7
            // 
            this.pictureBox7.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox7.Image = global::OCRTools.Properties.Resources.fankui;
            this.pictureBox7.Location = new System.Drawing.Point(243, 53);
            this.pictureBox7.Name = "pictureBox7";
            this.pictureBox7.Size = new System.Drawing.Size(16, 16);
            this.pictureBox7.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox7.TabIndex = 44;
            this.pictureBox7.TabStop = false;
            // 
            // pictureBox6
            // 
            this.pictureBox6.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox6.Image = global::OCRTools.Properties.Resources.traffic_cone;
            this.pictureBox6.Location = new System.Drawing.Point(243, 26);
            this.pictureBox6.Name = "pictureBox6";
            this.pictureBox6.Size = new System.Drawing.Size(16, 16);
            this.pictureBox6.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox6.TabIndex = 44;
            this.pictureBox6.TabStop = false;
            // 
            // pictureBox5
            // 
            this.pictureBox5.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox5.Image = global::OCRTools.Properties.Resources.qqKeFu;
            this.pictureBox5.Location = new System.Drawing.Point(21, 21);
            this.pictureBox5.Name = "pictureBox5";
            this.pictureBox5.Size = new System.Drawing.Size(20, 20);
            this.pictureBox5.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox5.TabIndex = 44;
            this.pictureBox5.TabStop = false;
            // 
            // linkLabel2
            // 
            this.linkLabel2.AutoSize = true;
            this.linkLabel2.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Pixel);
            this.linkLabel2.Location = new System.Drawing.Point(42, 55);
            this.linkLabel2.Name = "linkLabel2";
            this.linkLabel2.Size = new System.Drawing.Size(135, 12);
            this.linkLabel2.TabIndex = 0;
            this.linkLabel2.TabStop = true;
            this.linkLabel2.Text = "QQ交流群(100029010)";
            this.linkLabel2.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel2_LinkClicked);
            // 
            // linkLabel4
            // 
            this.linkLabel4.AutoSize = true;
            this.linkLabel4.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Pixel);
            this.linkLabel4.Location = new System.Drawing.Point(264, 55);
            this.linkLabel4.Name = "linkLabel4";
            this.linkLabel4.Size = new System.Drawing.Size(57, 12);
            this.linkLabel4.TabIndex = 0;
            this.linkLabel4.TabStop = true;
            this.linkLabel4.Text = "意见建议";
            this.linkLabel4.Click += new System.EventHandler(this.btnSupport_Click);
            // 
            // linkLabel3
            // 
            this.linkLabel3.AutoSize = true;
            this.linkLabel3.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Pixel);
            this.linkLabel3.Location = new System.Drawing.Point(264, 28);
            this.linkLabel3.Name = "linkLabel3";
            this.linkLabel3.Size = new System.Drawing.Size(57, 12);
            this.linkLabel3.TabIndex = 0;
            this.linkLabel3.TabStop = true;
            this.linkLabel3.Text = "问题反馈";
            this.linkLabel3.Click += new System.EventHandler(this.btnSupport_Click);
            // 
            // linkLabel1
            // 
            this.linkLabel1.AutoSize = true;
            this.linkLabel1.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Pixel);
            this.linkLabel1.Location = new System.Drawing.Point(42, 28);
            this.linkLabel1.Name = "linkLabel1";
            this.linkLabel1.Size = new System.Drawing.Size(155, 12);
            this.linkLabel1.TabIndex = 0;
            this.linkLabel1.TabStop = true;
            this.linkLabel1.Text = "在线客服(QQ:365833440)";
            this.linkLabel1.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel1_LinkClicked);
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.numUpdateHour);
            this.groupBox5.Controls.Add(this.btnCheckUpdate);
            this.groupBox5.Controls.Add(this.label28);
            this.groupBox5.Controls.Add(this.label29);
            this.groupBox5.Controls.Add(this.chkAutoUpdate);
            this.groupBox5.Location = new System.Drawing.Point(3, 242);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(374, 85);
            this.groupBox5.TabIndex = 29;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "自动更新";
            // 
            // numUpdateHour
            // 
            this.numUpdateHour.Location = new System.Drawing.Point(54, 45);
            this.numUpdateHour.Maximum = new decimal(new int[] {
            24,
            0,
            0,
            0});
            this.numUpdateHour.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numUpdateHour.Name = "numUpdateHour";
            this.numUpdateHour.Size = new System.Drawing.Size(52, 23);
            this.numUpdateHour.TabIndex = 33;
            this.numUpdateHour.Tag = "自动更新间隔";
            this.numUpdateHour.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // btnCheckUpdate
            // 
            this.btnCheckUpdate.Image = global::OCRTools.Properties.Resources.LoadingSmallBlack;
            this.btnCheckUpdate.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnCheckUpdate.Location = new System.Drawing.Point(168, 40);
            this.btnCheckUpdate.Name = "btnCheckUpdate";
            this.btnCheckUpdate.Size = new System.Drawing.Size(83, 26);
            this.btnCheckUpdate.TabIndex = 40;
            this.btnCheckUpdate.TabStop = false;
            this.btnCheckUpdate.Text = "检查更新";
            this.btnCheckUpdate.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnCheckUpdate.UseVisualStyleBackColor = false;
            this.btnCheckUpdate.Click += new System.EventHandler(this.btnCheckUpdate_Click);
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(12, 49);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(35, 17);
            this.label28.TabIndex = 31;
            this.label28.Text = "间隔:";
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(109, 49);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(32, 17);
            this.label29.TabIndex = 32;
            this.label29.Text = "小时";
            // 
            // chkAutoUpdate
            // 
            this.chkAutoUpdate.AutoSize = true;
            this.chkAutoUpdate.Checked = true;
            this.chkAutoUpdate.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoUpdate.Location = new System.Drawing.Point(11, 23);
            this.chkAutoUpdate.Name = "chkAutoUpdate";
            this.chkAutoUpdate.Size = new System.Drawing.Size(111, 21);
            this.chkAutoUpdate.TabIndex = 0;
            this.chkAutoUpdate.Text = "启动时检查更新";
            this.chkAutoUpdate.UseVisualStyleBackColor = true;
            // 
            // label38
            // 
            this.label38.AutoSize = true;
            this.label38.Font = new System.Drawing.Font("宋体", 13F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Pixel);
            this.label38.Location = new System.Drawing.Point(18, 120);
            this.label38.Name = "label38";
            this.label38.Size = new System.Drawing.Size(33, 13);
            this.label38.TabIndex = 28;
            this.label38.Text = "官网";
            // 
            // lblCopyright
            // 
            this.lblCopyright.AutoSize = true;
            this.lblCopyright.Font = new System.Drawing.Font("宋体", 13F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Pixel);
            this.lblCopyright.Location = new System.Drawing.Point(18, 98);
            this.lblCopyright.Name = "lblCopyright";
            this.lblCopyright.Size = new System.Drawing.Size(33, 13);
            this.lblCopyright.TabIndex = 28;
            this.lblCopyright.Text = "版权";
            // 
            // btnUpgrade
            // 
            this.btnUpgrade.Font = new System.Drawing.Font("微软雅黑", 14F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Pixel);
            this.btnUpgrade.Location = new System.Drawing.Point(244, 24);
            this.btnUpgrade.Name = "btnUpgrade";
            this.btnUpgrade.Size = new System.Drawing.Size(125, 39);
            this.btnUpgrade.TabIndex = 7;
            this.btnUpgrade.Text = "升级为…";
            this.btnUpgrade.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnUpgrade.UseVisualStyleBackColor = true;
            this.btnUpgrade.Click += new System.EventHandler(this.btnUpgrade_Click);
            // 
            // lblVersion
            // 
            this.lblVersion.AutoSize = true;
            this.lblVersion.Font = new System.Drawing.Font("宋体", 13F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Pixel);
            this.lblVersion.Location = new System.Drawing.Point(18, 76);
            this.lblVersion.Name = "lblVersion";
            this.lblVersion.Size = new System.Drawing.Size(33, 13);
            this.lblVersion.TabIndex = 28;
            this.lblVersion.Text = "版本";
            // 
            // lnkWebSite
            // 
            this.lnkWebSite.AutoSize = true;
            this.lnkWebSite.Font = new System.Drawing.Font("微软雅黑", 13F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Pixel);
            this.lnkWebSite.Location = new System.Drawing.Point(54, 117);
            this.lnkWebSite.Name = "lnkWebSite";
            this.lnkWebSite.Size = new System.Drawing.Size(58, 19);
            this.lnkWebSite.TabIndex = 0;
            this.lnkWebSite.TabStop = true;
            this.lnkWebSite.Text = "https://";
            this.lnkWebSite.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lnkWebSite_LinkClicked);
            // 
            // lblName
            // 
            this.lblName.AutoSize = true;
            this.lblName.Font = new System.Drawing.Font("微软雅黑", 20F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Pixel);
            this.lblName.Location = new System.Drawing.Point(65, 30);
            this.lblName.Name = "lblName";
            this.lblName.Size = new System.Drawing.Size(22, 27);
            this.lblName.TabIndex = 27;
            this.lblName.Text = "*";
            // 
            // picIcon
            // 
            this.picIcon.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.picIcon.Location = new System.Drawing.Point(20, 20);
            this.picIcon.Name = "picIcon";
            this.picIcon.Size = new System.Drawing.Size(45, 45);
            this.picIcon.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.picIcon.TabIndex = 26;
            this.picIcon.TabStop = false;
            // 
            // lnkLeft
            // 
            this.lnkLeft.Font = new System.Drawing.Font("微软雅黑", 11F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Pixel);
            this.lnkLeft.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline;
            this.lnkLeft.Location = new System.Drawing.Point(141, 26);
            this.lnkLeft.Name = "lnkLeft";
            this.lnkLeft.Size = new System.Drawing.Size(244, 22);
            this.lnkLeft.TabIndex = 49;
            this.lnkLeft.TabStop = true;
            this.lnkLeft.Text = "今日余量:-";
            this.lnkLeft.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.lnkLeft.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lnkLeft_LinkClicked);
            // 
            // FormSetting
            // 
            this.ClientSize = new System.Drawing.Size(415, 470);
            this.Controls.Add(this.tbConfig);
            this.Controls.Add(this.lnkLeft);
            this.Name = "FormSetting";
            this.Padding = new System.Windows.Forms.Padding(10, 60, 10, 10);
            this.Text = "系统设置";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FormSetting_FormClosing);
            this.Load += new System.EventHandler(this.FormSetting_Load);
            this.tbConfig.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.groupBox15.ResumeLayout(false);
            this.groupBox15.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox27)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox10)).EndInit();
            this.grpJieMianFont.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.tabPage4.ResumeLayout(false);
            this.groupBox11.ResumeLayout(false);
            this.groupBox11.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.picLoadingImage)).EndInit();
            this.grpToolSet.ResumeLayout(false);
            this.grpToolSet.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numToolShadowWidth)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox17)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.picToolBar)).EndInit();
            this.tabPage7.ResumeLayout(false);
            this.tabControl1.ResumeLayout(false);
            this.tabPage8.ResumeLayout(false);
            this.groupBox10.ResumeLayout(false);
            this.groupBox10.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox22)).EndInit();
            this.groupBox16.ResumeLayout(false);
            this.groupBox16.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox26)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox13)).EndInit();
            this.groupBox17.ResumeLayout(false);
            this.groupBox17.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            this.groupBox7.ResumeLayout(false);
            this.groupBox7.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox20)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox21)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox19)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox18)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox12)).EndInit();
            this.tbLocalOcr.ResumeLayout(false);
            this.groupBox20.ResumeLayout(false);
            this.groupBox20.PerformLayout();
            this.groupBox8.ResumeLayout(false);
            this.groupBox8.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLocalOcrThread)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numLocalOcrPort)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox25)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox24)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox23)).EndInit();
            this.tabPage2.ResumeLayout(false);
            this.groupBox19.ResumeLayout(false);
            this.groupBox19.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDown1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox11)).EndInit();
            this.groupBox12.ResumeLayout(false);
            this.groupBox12.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numCaptureBorderWidth)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxHistoryCount)).EndInit();
            this.tabPage5.ResumeLayout(false);
            this.groupBox14.ResumeLayout(false);
            this.groupBox14.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numShadowWidth)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numTieTuMaxWidth)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTieTuBorderWidth)).EndInit();
            this.tabPage3.ResumeLayout(false);
            this.groupBox13.ResumeLayout(false);
            this.groupBox13.PerformLayout();
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            this.tbShortKey.ResumeLayout(false);
            this.tabPage6.ResumeLayout(false);
            this.tabPage6.PerformLayout();
            this.groupBox9.ResumeLayout(false);
            this.groupBox9.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox5)).EndInit();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numUpdateHour)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.picIcon)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tbConfig;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.CheckBox chkAutoStart;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.CheckBox chkAutoBackConfig;
        private System.Windows.Forms.TabPage tabPage4;
        private System.Windows.Forms.TabPage tbShortKey;
        private System.Windows.Forms.Button btnClearConfig;
        private System.Windows.Forms.Button btnOpenConfigLocation;
        private System.Windows.Forms.CheckBox chkCaptureAnimal;
        private System.Windows.Forms.CheckBox chkStartMainWindow;
        private System.Windows.Forms.ComboBox cmbStyles;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ComboBox cmbLoadingType;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.PictureBox picLoadingImage;
        private System.Windows.Forms.NumericUpDown numCaptureBorderWidth;
        private System.Windows.Forms.CheckBox chkHexUpper;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.CheckBox chkAutoControl;
        private System.Windows.Forms.CheckBox chkAutoWindow;
        private System.Windows.Forms.CheckBox chkCrocessLine;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.CheckBox chkZoom;
        private System.Windows.Forms.ComboBox cmbHex;
        private System.Windows.Forms.NumericUpDown numMaxHistoryCount;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.CheckBox chkCaptureTip;
        private System.Windows.Forms.CheckBox chkSaveToClipborad;
        private System.Windows.Forms.Button btnChangeCaptureLocation;
        private System.Windows.Forms.Button btnOpenCaptureLocation;
        private System.Windows.Forms.TextBox txtCaptureLocation;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.TextBox txtCaptureFileName;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.CheckBox chkAutoSaveCapture;
        private System.Windows.Forms.TabPage tabPage5;
        private System.Windows.Forms.TabPage tabPage6;
        private System.Windows.Forms.GroupBox grpJieMianFont;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.NumericUpDown numTieTuBorderWidth;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.Label lblTieTuLabel;
        private System.Windows.Forms.CheckBox chkTieTuFocus;
        private System.Windows.Forms.CheckBox chkTieTuShark;
        private System.Windows.Forms.CheckBox chkTieTuUseCaptureLocation;
        private ImageButton btnTieTuShadowColor;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.CheckBox chkIngoreHtml;
        private System.Windows.Forms.NumericUpDown numTieTuMaxWidth;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.Label lblContentLable;
        private System.Windows.Forms.NumericUpDown numShadowWidth;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.CheckBox chkAutoUpdate;
        private System.Windows.Forms.PictureBox picIcon;
        private System.Windows.Forms.Label lblCopyright;
        private System.Windows.Forms.Label lblVersion;
        private System.Windows.Forms.Label lblName;
        private System.Windows.Forms.NumericUpDown numUpdateHour;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.Label label29;
        private Button btnUpgrade;
        private System.Windows.Forms.CheckBox chkUseContentColor;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label lblCaptureFileName;
        private ImageButton btnContentBackColor;
        private ImageButton btnContentFontColor;
        private System.Windows.Forms.Button btnContentFont;
        private System.Windows.Forms.Button btnContentDefault;
        private System.Windows.Forms.Button btnTieTuDefaultFont;
        private ImageButton btnTieTuBackColor;
        private System.Windows.Forms.Button btnTieTuFont;
        private ImageButton btnTieTuFontColor;
        private System.Windows.Forms.TabControl tabHotKeys;
        private System.Windows.Forms.ToolTip tipMsg;
        private System.Windows.Forms.ComboBox cmbDoubleClick;
        private System.Windows.Forms.CheckBox chkShowTool;
        private System.Windows.Forms.CheckBox chkDarkModel;
        private System.Windows.Forms.ComboBox cmbSearch;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.CheckBox chkFormTopMost;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.GroupBox grpToolSet;
        private System.Windows.Forms.PictureBox picToolBar;
        private System.Windows.Forms.Button btnDefaultToolBarPicture;
        private System.Windows.Forms.Button btnToolBarPicture;
        private System.Windows.Forms.CheckBox chkToolBarCircle;
        private System.Windows.Forms.ComboBox cmbToolBarSize;
        private System.Windows.Forms.TextBox txtToolBarPicLocation;
        private System.Windows.Forms.Button btnQQ;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.CheckBox checkBox2;
        private System.Windows.Forms.ComboBox cmbRulerTheme;
        private System.Windows.Forms.ComboBox cmbRulerUnit;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.ComboBox cmbRulerOpacity;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.PictureBox pictureBox2;
        private System.Windows.Forms.TabPage tabPage7;
        private System.Windows.Forms.ComboBox cmbFenDuan;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.ComboBox cmbVoice;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.GroupBox groupBox9;
        private System.Windows.Forms.PictureBox pictureBox4;
        private System.Windows.Forms.PictureBox pictureBox5;
        private LinkLabel linkLabel2;
        private LinkLabel linkLabel1;
        private System.Windows.Forms.PictureBox pictureBox6;
        private LinkLabel linkLabel3;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.PictureBox pictureBox7;
        private LinkLabel linkLabel4;
        private System.Windows.Forms.CheckBox chkToolShadow;
        private System.Windows.Forms.NumericUpDown numToolShadowWidth;
        private System.Windows.Forms.PictureBox pictureBox8;
        private System.Windows.Forms.Button btnCheckUpdate;
        private System.Windows.Forms.CheckBox chkWeatherImage;
        private System.Windows.Forms.ComboBox cmbWeatherIcon;
        private System.Windows.Forms.GroupBox groupBox10;
        private System.Windows.Forms.PictureBox pictureBox9;
        private System.Windows.Forms.PictureBox pictureBox10;
        private System.Windows.Forms.GroupBox groupBox11;
        private System.Windows.Forms.GroupBox groupBox12;
        private System.Windows.Forms.GroupBox groupBox13;
        private System.Windows.Forms.PictureBox pictureBox11;
        private System.Windows.Forms.Label label37;
        private System.Windows.Forms.NumericUpDown numericUpDown1;
        private System.Windows.Forms.Label label36;
        private System.Windows.Forms.GroupBox groupBox14;
        private System.Windows.Forms.GroupBox groupBox15;
        private System.Windows.Forms.GroupBox groupBox16;
        private System.Windows.Forms.PictureBox pictureBox12;
        private System.Windows.Forms.PictureBox pictureBox13;
        private System.Windows.Forms.CheckBox checkBox10;
        private System.Windows.Forms.CheckBox checkBox12;
        private System.Windows.Forms.CheckBox checkBox11;
        private System.Windows.Forms.GroupBox groupBox17;
        private System.Windows.Forms.PictureBox pictureBox1;
        private System.Windows.Forms.CheckBox checkBox8;
        private System.Windows.Forms.ComboBox cmbCopyMode;
        private System.Windows.Forms.Label label39;
        private System.Windows.Forms.PictureBox pictureBox3;
        private System.Windows.Forms.CheckBox checkBox3;
        private System.Windows.Forms.PictureBox pictureBox15;
        private System.Windows.Forms.CheckBox chkCircleFangDa;
        private System.Windows.Forms.Label label38;
        private System.Windows.Forms.LinkLabel lnkWebSite;
        private System.Windows.Forms.PictureBox pictureBox16;
        private System.Windows.Forms.CheckBox checkBox4;
        private PictureBox pictureBox17;
        private Label label33;
        private Label label40;
        private Label label41;
        private ImageButton imageButton1;
        private GroupBox groupBox19;
        private PictureBox pictureBox18;
        private CheckBox chkAutoFull2Half;
        private PictureBox pictureBox20;
        private PictureBox pictureBox21;
        private CheckBox checkBox6;
        private CheckBox chkAutoBiaoDian;
        private PictureBox pictureBox19;
        private CheckBox checkBox5;
        private LinkLabel lnkLeft;
        private PictureBox pictureBox22;
        private ComboBox cmbOcrType;
        private CheckBox chkLocalOcrEnable;
        private PictureBox pictureBox23;
        private ComboBox cmsNotifyDoubleClick;
        private Label label30;
        private GroupBox groupBox8;
        private GroupBox grpLocalOcr;
        private LinkLabel lnkInstallLocalOcr;
        private LinkLabel lnkOpenLocalOcr;
        private NumericUpDown numLocalOcrPort;
        private Label lblLocalOcrPort;
        private TabControl tabControl1;
        private TabPage tabPage8;
        private TabPage tbLocalOcr;
        private LinkLabel lnkTestLocalOcr;
        private GroupBox groupBox20;
        private NumericUpDown numLocalOcrThread;
        private Label label34;
        private PictureBox pictureBox25;
        private PictureBox pictureBox24;
        private PictureBox pictureBox26;
        private CheckBox checkBox1;
        private PictureBox pictureBox27;
        private CheckBox checkBox7;
        private CheckBox chkShowNotify;
        private ComboBox cmbImageViewBackStyle;
        private Label label2;
        private ImageButton btnImageViewBackColor;
        private TextBox textBox2;
        private TextBox textBox1;
    }
}