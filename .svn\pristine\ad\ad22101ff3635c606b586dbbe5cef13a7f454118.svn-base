﻿using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace TencentLocalOCR
{
    public partial class FormSetting : Form
    {
        public int NType { get; set; }

        public FormSetting()
        {
            InitializeComponent();
        }

        private void FormSetting_Load(object sender, EventArgs e)
        {
            switch (NType)
            {
                case 0:
                    Text = "QQNT OCR识别参数配置";
                    txtOcrExe.Text = Properties.Settings.Default.StrQQNTOcr;
                    txtAppPath.Text = Properties.Settings.Default.StrQQNTApp;
                    break;
                case 1:
                    Text = "WeiXin OCR识别参数配置";
                    txtOcrExe.Text = Properties.Settings.Default.StrWxOcr;
                    txtAppPath.Text = Properties.Settings.Default.StrWxApp;
                    break;
                case 2:
                    Text = "WeWork OCR识别参数配置";
                    txtOcrExe.Text = Properties.Settings.Default.StrWXWorkOcr;
                    txtAppPath.Text = Properties.Settings.Default.StrWXWorkApp;
                    break;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            switch (NType)
            {
                case 0:
                    Properties.Settings.Default.StrQQNTOcr = txtOcrExe.Text;
                    Properties.Settings.Default.StrQQNTApp = txtAppPath.Text;
                    break;
                case 1:
                    Properties.Settings.Default.StrWxOcr = txtOcrExe.Text;
                    Properties.Settings.Default.StrWxApp = txtAppPath.Text;
                    break;
                case 2:
                    Properties.Settings.Default.StrWXWorkOcr = txtOcrExe.Text;
                    Properties.Settings.Default.StrWXWorkApp = txtAppPath.Text;
                    break;
            }
            Properties.Settings.Default.Save();
            this.Close();
        }

        public void Init()
        {
            string strAppPath = string.Empty;
            switch (NType)
            {
                case 0:
                    Properties.Settings.Default.StrQQNTOcr = GetQQLocation(out strAppPath);
                    Properties.Settings.Default.StrQQNTApp = strAppPath;
                    break;
                case 1:
                    Properties.Settings.Default.StrWxOcr = GetWeiXinLocation(out strAppPath);
                    Properties.Settings.Default.StrWxApp = strAppPath;
                    break;
                case 2:
                    Properties.Settings.Default.StrWXWorkOcr = GetWXWorkLocation(out strAppPath);
                    Properties.Settings.Default.StrWXWorkApp = strAppPath;
                    break;
            }
            Properties.Settings.Default.Save();
        }

        string SearchFile(string directoryPath, string fileName)
        {
            // 遍历目录及其子目录
            try
            {
                foreach (var dir in Directory.GetDirectories(directoryPath))
                {
                    // 递归搜索子目录
                    string found = SearchFile(dir, fileName);
                    if (!string.IsNullOrEmpty(found))
                    {
                        return found; // 找到后立即返回
                    }
                }
                // 检查当前目录中是否存在指定文件
                if (File.Exists(Path.Combine(directoryPath, fileName)))
                {
                    return directoryPath; // 返回当前目录
                }
            }
            catch (Exception oe)
            {
                // 处理未找到的目录
            }
            return null; // 未找到文件
        }

        string strMOJODLL = "mmmojo.dll";
        string StrQQOcrEXE = "TencentOCR.exe";
        List<string> lstQQPath = new List<string>() { "C:\\Program Files\\Tencent\\QQNT", @"C:\Program Files (x86)\Tencent\QQNT" };

        string GetQQLocation(out string appPath)
        {
            ////args[0] = "C:\Program Files\Tencent\QQNT\versions\9.9.16-28971\resources\app\QQScreenShot\Bin\TencentOCR.exe";
            ////args[1] = "C:\Program Files\Tencent\QQNT\versions\9.9.16-28971";
            var exePath = string.Empty;
            appPath = string.Empty;

            var strBasePath = lstQQPath.FirstOrDefault(p => Directory.Exists(p));

            if (string.IsNullOrEmpty(strBasePath))
            {
                try
                {
                    var process = Process.GetProcessesByName("QQ")?.FirstOrDefault();
                    if (process != null)
                    {
                        strBasePath = Path.GetDirectoryName(process.MainModule.FileName);
                    }
                }
                catch { }
            }

            if (string.IsNullOrEmpty(strBasePath))
            {
                try
                {
                    strBasePath = FindInstallPathFromRegistry("QQ");
                    if (string.IsNullOrEmpty(strBasePath))
                        strBasePath = FindInstallPathFromRegistry("QQNT");
                    if (string.IsNullOrEmpty(strBasePath))
                        strBasePath = FindInstallPathFromRegistry("{052CFB79-9D62-42E3-8A15-DE66C2C97C3E}");
                    if (string.IsNullOrEmpty(strBasePath))
                        strBasePath = FindInstallPathFromRegistryWOW6432Node("QQ");
                    if (string.IsNullOrEmpty(strBasePath))
                        strBasePath = FindInstallPathFromRegistryWOW6432Node("QQNT");
                    if (string.IsNullOrEmpty(strBasePath))
                        strBasePath = FindInstallPathFromRegistryWOW6432Node("{052CFB79-9D62-42E3-8A15-DE66C2C97C3E}");
                }
                catch { }
            }
            if (!string.IsNullOrEmpty(strBasePath))
            {
                appPath = SearchFile(strBasePath, strMOJODLL);
            }
            try
            {
                var process = Process.GetProcessesByName("TencentOCR")?.FirstOrDefault();
                if (process != null)
                {
                    exePath = process.MainModule.FileName;
                }
            }
            catch { }
            if (!string.IsNullOrEmpty(appPath) && string.IsNullOrEmpty(exePath))
            {
                strBasePath = SearchFile(appPath, StrQQOcrEXE);
                if (!string.IsNullOrEmpty(strBasePath))
                {
                    exePath = Path.Combine(strBasePath, StrQQOcrEXE);
                }
            }
            return exePath;
        }

        string StrWeiXinOcrEXE = "WeChatOCR.exe";
        List<string> lstWeiXinPath = new List<string>() { "C:\\Program Files\\Tencent\\WeChat", @"C:\Program Files (x86)\Tencent\WeChat" };

        string GetWeiXinLocation(out string appPath)
        {
            //args[0] = "C:\\Users\\<USER>\\AppData\\Roaming\\Tencent\\WeChat\\XPlugin\\Plugins\\WeChatOCR\\7079\\extracted\\WeChatOCR.exe";
            //args[1] = "C:\\Program Files\\Tencent\\WeChat\\[3.9.12.17]";
            var exePath = string.Empty;
            appPath = string.Empty;

            var strBasePath = string.Empty;
            foreach (var path in lstWeiXinPath)
            {
                if (Directory.Exists(path))
                {
                    strBasePath = path;
                    break;
                }
            }

            if (string.IsNullOrEmpty(strBasePath))
            {
                try
                {
                    var process = Process.GetProcessesByName("WeChat")?.FirstOrDefault();
                    if (process != null)
                    {
                        strBasePath = Path.GetDirectoryName(process.MainModule.FileName);
                    }
                }
                catch { }
            }

            if (string.IsNullOrEmpty(strBasePath))
            {
                try
                {
                    strBasePath = FindInstallPathFromRegistry("Wechat");
                    if (string.IsNullOrEmpty(strBasePath))
                        strBasePath = FindInstallPathFromRegistryWOW6432Node("Wechat");
                }
                catch { }
            }
            if (!string.IsNullOrEmpty(strBasePath))
            {
                appPath = SearchFile(strBasePath, strMOJODLL);
            }
            try
            {
                var process = Process.GetProcessesByName("WeChatOCR")?.FirstOrDefault();
                if (process != null)
                {
                    exePath = process.MainModule.FileName;
                }
            }
            catch { }
            if (string.IsNullOrEmpty(exePath))
            {
                strBasePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Tencent\\WeChat");
                strBasePath = SearchFile(strBasePath, StrWeiXinOcrEXE);
                if (!string.IsNullOrEmpty(strBasePath))
                {
                    exePath = Path.Combine(strBasePath, StrWeiXinOcrEXE);
                }
            }
            return exePath;
        }

        string StrWXWorkOcrEXE = "WeChatOCR.exe";

        string GetWXWorkLocation(out string appPath)
        {
            //args[0] = "C:\\Users\\<USER>\\AppData\\Roaming\\Tencent\\WXWork\\WeChatOCR\\1.0.1.28\\WeChatOCR\\WeChatOCR.exe";
            //args[1] = "C:\\Users\\<USER>\\AppData\\Roaming\\Tencent\\WXWork\\WeChatOCR\\1.0.1.28\\WeChatOCR";
            var exePath = string.Empty;
            appPath = string.Empty;

            var strBasePath = string.Empty;
            try
            {
                strBasePath = Process.GetProcessesByName("WeChatOCR")?.Where(p => p.MainModule.FileName.Contains("WXWork")).Select(p => Path.GetDirectoryName(p.MainModule.FileName)).FirstOrDefault();
            }
            catch { }
            if (string.IsNullOrEmpty(strBasePath))
            {
                strBasePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Tencent\\WXWork");
                strBasePath = SearchFile(strBasePath, StrWXWorkOcrEXE);
            }

            if (!string.IsNullOrEmpty(strBasePath))
            {
                appPath = SearchFile(strBasePath, strMOJODLL);
                var exeDir = SearchFile(strBasePath, StrWXWorkOcrEXE);
                if (!string.IsNullOrEmpty(exeDir))
                {
                    exePath = Path.Combine(exeDir, StrWXWorkOcrEXE);
                }
            }
            return exePath;
        }

        string FindInstallPathFromRegistry(string uninstallKeyName)
        {
            try
            {
                using (var key = Registry.LocalMachine.OpenSubKey($@"Software\Microsoft\Windows\CurrentVersion\Uninstall\{uninstallKeyName}"))
                {
                    if (key != null)
                    {
                        var strTmp = (key.GetValue("InstallLocation") ?? key.GetValue("UninstallString"))?.ToString() ?? "";
                        return strTmp.Contains(".exe") ? strTmp.Substring(0, strTmp.ToString().LastIndexOf("\\")) : strTmp;
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }
            return null;
        }

        string FindInstallPathFromRegistryWOW6432Node(string uninstallKeyName)
        {
            try
            {
                using (var key = Registry.LocalMachine.OpenSubKey($@"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\{uninstallKeyName}"))
                {
                    if (key != null)
                    {
                        var strTmp = (key.GetValue("InstallLocation") ?? key.GetValue("UninstallString"))?.ToString() ?? "";
                        return strTmp.Contains(".exe") ? strTmp.Substring(0, strTmp.ToString().LastIndexOf("\\")) : strTmp;
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }
            return null;
        }

        private void linkLabel1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            var strTmp = NType == 0 ? "TencentOCR.exe" : "WeChatOCR.exe";
            MessageBox.Show(this,
                "说明：建议用EveryThing或Windows搜索【"
                + strTmp
                + "】\n将完整路径[含文件名]填写到此处！\r\n\n示例：\r\n" +
                (NType == 0 ? "C:\\Program Files\\Tencent\\QQNT\\resources\\app\\versions\\9.9.15-27254\\QQScreenShot\\Bin\\" : (NType == 1 ? "C:\\Users\\<USER>\\AppData\\Roaming\\Tencent\\WeChat\\XPlugin\\Plugins\\WeChatOCR\\7079\\extracted\\" : "C:\\Users\\<USER>\\AppData\\Roaming\\Tencent\\WXWork\\WeChatOCR\\1.0.1.28\\WeChatOCR\\")) + strTmp
                + "\r\n\r\n"
                , "参数设置说明", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void linkLabel2_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            var strTmp = "mmmojo.dll";
            MessageBox.Show(this,
                "说明：建议用EveryThing或Windows搜索【"
                + strTmp
                + "】\n将完整路径[不含文件名]填写到此处！\r\n\n示例：\r\n" +
                (NType == 0 ? "C:\\Program Files\\Tencent\\QQNT\\versions\\9.9.16-28971\\resources\\app" : (NType == 1 ? "C:\\Program Files\\Tencent\\WeChat\\[3.9.12.17]" : "C:\\Users\\<USER>\\AppData\\Roaming\\Tencent\\WXWork\\WeChatOCR\\1.0.1.28\\WeChatOCR\\"))
                + "\r\n\r\n"
                , "参数设置说明", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
