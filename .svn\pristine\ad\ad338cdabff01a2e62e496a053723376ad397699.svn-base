﻿using OCRTools.Properties;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Reflection;
using System.Windows.Forms;

namespace OCRTools
{
    /// <summary>
    ///     Summary description for ScrollingTextControl.
    /// </summary>
    [ToolboxBitmap(typeof(ScrollingText), "ScrollingText.bmp")]
    [DefaultEvent("TextClicked")]
    public class ScrollingText : Control
    {
        private readonly Timer _timer; // Timer for text animation.
        private ScrollDirection _currentDirection = ScrollDirection.LeftToRight; // Used for text bouncing 
        private RectangleF _lastKnownRect; // The last known position of the text
        private bool _scrollOn = true; // Internal flag to stop / start the scrolling of the text
        private float _staticTextPos; // The running x pos of the text
        private string _text = "Text"; // Scrolling text
        private float _yPos; // The running y pos of the text
        private PictureBox picClose;
        public bool Close { get; set; }
        public Control CloseOwner { get; set; }
        public bool IsCanClose { get; set; }

        public ScrollingText()
        {
            // Setup default properties for ScrollingText control
            InitializeComponent();
            //This turns off internal double buffering of all custom GDI+ drawing
            var v = Environment.Version;
            if (v.Major < 2)
                SetStyle(ControlStyles.DoubleBuffer, true);
            else
                SetStyle(ControlStyles.OptimizedDoubleBuffer, true);

            SetStyle(ControlStyles.AllPaintingInWmPaint, true);
            SetStyle(ControlStyles.UserPaint, true);
            SetStyle(ControlStyles.ResizeRedraw, true);

            //setup the timer object
            _timer = new Timer
            {
                Interval = 100, //default timer interval
                Enabled = true
            };
            _timer.Tick += Tick;
        }

        /// <summary>
        ///     Clean up any resources being used.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                //Make sure our brushes are cleaned up
                if (ForegroundBrush != null)
                    ForegroundBrush.Dispose();

                //Make sure our brushes are cleaned up
                if (BackgroundBrush != null)
                    BackgroundBrush.Dispose();

                //Make sure our timer is cleaned up
                if (_timer != null)
                    _timer.Dispose();
            }

            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary>
        ///     Required method for Designer support - do not modify
        ///     the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            //ScrollingText            
            this.Name = "ScrollingText";
            this.Size = new Size(216, 40);
            this.Click += new EventHandler(this.ScrollingText_Click);
            this.picClose = new PictureBox() { Cursor = Cursors.Hand };
            picClose.Visible = false;
            picClose.Image = Resources.Full_close_down;
            picClose.SizeMode = PictureBoxSizeMode.StretchImage;
            this.SizeChanged += ScrollingText_SizeChanged;
            picClose.MouseDown += PicClose_MouseDown;
            this.Controls.Add(picClose);
        }

        private void ScrollingText_SizeChanged(object sender, EventArgs e)
        {
            var width = Math.Min(Height, picClose.Image.Height);
            picClose.Size = new Size(width, width);
            picClose.Location = new Point(Width - picClose.Width, 0);
        }

        private void PicClose_MouseDown(object sender, MouseEventArgs e)
        {
            CloseOwner = Parent;
            Close = true;
            this.Visible = false;
        }

        #endregion

        //Controls the animation of the text.
        private void Tick(object sender, EventArgs e)
        {
            if (IsCanClose)
                picClose.Visible = RectangleToScreen(ClientRectangle).Contains(MousePosition);
            //update rectangle to include where to paint for new position
            //lastKnownRect.X -= 10;
            //lastKnownRect.Width += 20;
            _lastKnownRect.Inflate(10, 5);

            //get the display rectangle
            var refreshRect = _lastKnownRect;
            refreshRect.X = Math.Max(0, _lastKnownRect.X);
            refreshRect.Width = Math.Min(_lastKnownRect.Width + _lastKnownRect.X, Width - _lastKnownRect.X);
            //refreshRect.Width = Math.Min(this.Width, refreshRect.Width);

            //create region based on updated rectangle
            //Region updateRegion = new Region(lastKnownRect);            
            using (var updateRegion = new Region(refreshRect))
            {
                //repaint the control            
                Invalidate(updateRegion);
            }

            Update();
        }

        //Paint the ScrollingTextCtrl.
        protected override void OnPaint(PaintEventArgs pe)
        {
            //Console.WriteLine(pe.ClipRectangle.X + ",  " + pe.ClipRectangle.Y + ",  " + pe.ClipRectangle.Width + ",  " + pe.ClipRectangle.Height);

            //Paint the text to its new position
            DrawScrollingText(pe.Graphics);

            //pass on the graphics obj to the base Control class
            base.OnPaint(pe);
        }

        //Draw the scrolling text on the control        
        public void DrawScrollingText(Graphics canvas)
        {
            //canvas.SmoothingMode = SmoothingMode.HighSpeed;
            //canvas.PixelOffsetMode = PixelOffsetMode.HighSpeed;
            canvas.InterpolationMode = InterpolationMode.HighQualityBilinear;
            canvas.CompositingQuality = CompositingQuality.HighQuality;
            canvas.SmoothingMode = SmoothingMode.HighQuality;
            canvas.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;

            //measure the size of the string for placement calculation
            var stringSize = canvas.MeasureString(_text, Font);

            //Calculate the begining x position of where to paint the text
            if (_scrollOn) CalcTextPosition(stringSize);

            //Clear the control with user set BackColor
            if (BackgroundBrush != null)
                canvas.FillRectangle(BackgroundBrush, 0, 0, ClientSize.Width, ClientSize.Height);
            else
                canvas.Clear(BackColor);

            // Draw the border
            if (ShowBorder)
                using (var borderPen = new Pen(BorderColor))
                {
                    canvas.DrawRectangle(borderPen, 0, 0, ClientSize.Width - 1, ClientSize.Height - 1);
                }

            // Draw the text string in the bitmap in memory
            if (ForegroundBrush == null)
                using (Brush tempForeBrush = new SolidBrush(ForeColor))
                {
                    canvas.DrawString(_text, Font, tempForeBrush, _staticTextPos, _yPos);
                }
            else
                canvas.DrawString(_text, Font, ForegroundBrush, _staticTextPos, _yPos);

            _lastKnownRect = new RectangleF(_staticTextPos, _yPos, stringSize.Width, stringSize.Height);
            EnableTextLink(_lastKnownRect);
        }

        //private int nowCount = 0;

        //public int NowCount
        //{
        //    get { return nowCount; }
        //    set { nowCount = value; }
        //}
        //private int maxCount = 0;

        //public int MaxCount
        //{
        //    get { return maxCount; }
        //    set { maxCount = value; }
        //}
        private void CalcTextPosition(SizeF stringSize)
        {
            switch (ScrollDirection)
            {
                case ScrollDirection.RightToLeft:
                    if (_staticTextPos < -1 * stringSize.Width + stringSize.Width / 3)
                    {
                        _staticTextPos = ClientSize.Width / 10;
                        LoopTimes++;
                    }
                    else
                    {
                        _staticTextPos -= TextScrollDistance;
                    }

                    break;
                case ScrollDirection.LeftToRight:
                    if (_staticTextPos > ClientSize.Width)
                    {
                        _staticTextPos = -1 * stringSize.Width;
                        LoopTimes++;
                    }
                    else
                    {
                        _staticTextPos += TextScrollDistance;
                    }

                    break;
                case ScrollDirection.Bouncing:
                    if (_currentDirection == ScrollDirection.RightToLeft)
                    {
                        if (_staticTextPos < 0)
                        {
                            _currentDirection = ScrollDirection.LeftToRight;
                            LoopTimes++;
                        }
                        else
                        {
                            _staticTextPos -= TextScrollDistance;
                        }
                    }
                    else if (_currentDirection == ScrollDirection.LeftToRight)
                    {
                        if (_staticTextPos > ClientSize.Width - stringSize.Width)
                        {
                            _currentDirection = ScrollDirection.RightToLeft;
                            LoopTimes++;
                        }
                        else
                        {
                            _staticTextPos += TextScrollDistance;
                        }
                    }

                    break;
            }

            //Calculate the vertical position for the scrolling text                
            switch (VerticleTextPosition)
            {
                case VerticleTextPosition.Top:
                    _yPos = 2;
                    break;
                case VerticleTextPosition.Center:
                    _yPos = ClientSize.Height * 1.0f / 2 - stringSize.Height / 2;
                    break;
                case VerticleTextPosition.Botom:
                    _yPos = ClientSize.Height - stringSize.Height;
                    break;
            }
        }

        #region Mouse over, text link logic

        private void EnableTextLink(RectangleF textRect)
        {
            if (!StopScrollOnMouseOver)
            {
                _scrollOn = true;
                if (Cursor != Cursors.Default)
                    Cursor = Cursors.Default;
                return;
            }

            var curPt = PointToClient(Cursor.Position);

            //if (curPt.X > textRect.Left && curPt.X < textRect.Right
            //    && curPt.Y > textRect.Top && curPt.Y < textRect.Bottom)
            if (textRect.Contains(curPt))
            {
                //Stop the text of the user mouse's over the text
                if (StopScrollOnMouseOver)
                    _scrollOn = false;
                Cursor = Cursors.Hand;
            }
            else
            {
                _scrollOn = true;
                //Make sure the text is scrolling if user's mouse is not over the text
                Cursor = Cursors.Default;
            }
        }

        private void ScrollingText_Click(object sender, EventArgs e)
        {
            //Trigger the text clicked event if the user clicks while the mouse 
            //is over the text.  This allows the text to act like a hyperlink
            if (Cursor == Cursors.Hand)
                OnTextClicked(this, new EventArgs());
        }

        public delegate void TextClickEventHandler(object sender, EventArgs args);

        public event TextClickEventHandler TextClicked;

        private void OnTextClicked(object sender, EventArgs args)
        {
            //Call the delegate
            if (TextClicked != null)
            {
                TextClicked(sender, args);
            }
            else
            {
                if (string.IsNullOrEmpty(StrLink)) return;
                try
                {
                    CommonMethod.DetermineCall(FrmMain.FrmTool, () =>
                    {
                        var form = ControlExtension.GetMetroForm();
                        new FrmViewUrl
                        {
                            Url = StrLink,
                            WindowState = FormWindowState.Maximized,
                            StartPosition = FormStartPosition.CenterScreen,
                            Icon = form.Icon,
                            Theme = form.Theme,
                            Style = form.Style,
                            StyleManager = form.StyleManager
                        }.Show();
                    });
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
            }
        }

        #endregion


        #region Properties

        [Browsable(true)]
        [Category("Scrolling Text")]
        [Description("The timer interval that determines how often the control is repainted")]
        public int TextScrollSpeed
        {
            set => _timer.Interval = value;
            get => _timer.Interval;
        }

        [Browsable(true)]
        [Category("Scrolling Text")]
        [Description("How many pixels will the text be moved per Paint")]
        public int TextScrollDistance { set; get; } = 2;

        [Browsable(true)]
        [Category("Scrolling Text")]
        [Description("The text that will scroll accros the control")]
        public string ScrollText
        {
            set
            {
                _text = value;
                _staticTextPos = ClientSize.Width / 10;
                _yPos = 0;
                LoopTimes = 0;
                Invalidate();
                Update();
            }
            get => _text;
        }

        [Browsable(true)]
        [Category("Scrolling Text")]
        [Description("What direction the text will scroll: Left to Right, Right to Left, or Bouncing")]
        public ScrollDirection ScrollDirection { set; get; } = ScrollDirection.RightToLeft;

        [Browsable(true)]
        [Category("Scrolling Text")]
        [Description("The verticle alignment of the text")]
        public VerticleTextPosition VerticleTextPosition { set; get; } = VerticleTextPosition.Center;

        [Browsable(true)]
        [Category("Scrolling Text")]
        [Description("Turns the border on or off")]
        public bool ShowBorder { set; get; } = false;

        [Browsable(true)]
        [Category("Scrolling Text")]
        [Description("Link URL of the this")]
        public string StrLink { set; get; } = string.Empty;

        [Browsable(true)]
        [Category("Scrolling Text")]
        [Description("The color of the border")]
        public Color BorderColor { set; get; } = Color.Black;

        [Browsable(true)]
        [Category("Scrolling Text")]
        [Description("Determines if the text will stop scrolling if the user's mouse moves over the text")]
        public bool StopScrollOnMouseOver { set; get; } = false;

        [Browsable(true)]
        [Category("Behavior")]
        [Description("Indicates whether the control is enabled")]
        public new bool Enabled
        {
            set
            {
                _timer.Enabled = value;
                base.Enabled = value;
            }
            get => base.Enabled;
        }

        [Browsable(false)] public Brush ForegroundBrush { set; get; } = null;

        [ReadOnly(true)] public Brush BackgroundBrush { set; get; } = null;

        public int LoopTimes { get; internal set; }
        public int MaxLoopTimes { get; internal set; }

        #endregion
    }

    public enum ScrollDirection
    {
        RightToLeft,
        LeftToRight,
        Bouncing
    }

    public enum VerticleTextPosition
    {
        Top,
        Center,
        Botom
    }

    [Obfuscation]
    public class ScrollEntity
    {
        [Obfuscation] public string LnkUrl { get; set; } = string.Empty;

        [Obfuscation] public Color ForeColor { get; set; } = Color.Black;

        [Obfuscation] public string Text { get; set; } = string.Empty;
    }
}