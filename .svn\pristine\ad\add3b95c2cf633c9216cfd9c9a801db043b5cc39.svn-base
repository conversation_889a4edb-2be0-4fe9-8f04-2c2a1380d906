// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Runtime.InteropServices;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public static class Automation
    {
        private static IUIAutomation _factory;

        // Explicit static constructor to tell C# compiler
        // not to mark type as beforefieldinit

        public static IUIAutomation Factory
        {
            get
            {
                // Try CUIAutomation8
                if (_factory == null)
                    try
                    {
                        _factory = new CUIAutomation8Class();
                    }
                    catch (COMException)
                    {
                    }

                // Fall back to CUIAutomation
                return _factory ?? (_factory = new CUIAutomationClass());
            }
        }
    }
}