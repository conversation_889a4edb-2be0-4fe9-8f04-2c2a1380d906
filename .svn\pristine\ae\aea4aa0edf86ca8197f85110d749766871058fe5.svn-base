﻿using System.Collections.Generic;
using System.Drawing;
using System.Reflection;

namespace OCRTools.Common
{
    internal class CommonPlug
    {

        internal static List<PlugEntity> GetAllPlug()
        {
            List<PlugEntity> lstAds = new List<PlugEntity>();
            try
            {
                //var plug = new PlugEntity()
                //{
                //    Name = "ChatGPT",
                //    FontSize = 15F,
                //    Desc = "ChatGPT 助手版",
                //    Url = "https://ocr.oldfish.cn/gpt/",
                //    ButtonType = ButtonType.Image,
                //    Type = PlugType.Url,
                //    Image = ImageProcessHelper.ImageToBase64(Properties.Resources.chatgpt),
                //    Top = 5,
                //    Width = 380,
                //    Height = 550
                //};
                //lstAds.Add(plug);
                //System.Console.WriteLine(CommonString.JavaScriptSerializer.Serialize(lstAds));

                var result =
                    WebClientExt.GetHtml(CommonString.HostUpdate?.FullUrl + "update/uPlug.txt?t=" + ServerTime.DateTime.Ticks, 5);
                if (!string.IsNullOrEmpty(result) && result.Length > 2)
                    lstAds = CommonString.JavaScriptSerializer.Deserialize<List<PlugEntity>>(result);
            }
            catch { }
            return lstAds;
        }
    }

    [Obfuscation]
    internal class PlugEntity
    {
        [Obfuscation]
        public string Name { get; set; }

        [Obfuscation]
        public string Desc { get; set; }

        [Obfuscation]
        public ButtonType ButtonType { get; set; }

        [Obfuscation]
        public PlugType Type { get; set; }

        [Obfuscation]
        public string Image { get; set; }

        [Obfuscation]
        public string Url { get; set; }

        [Obfuscation]
        public string ForeColor { get; set; }

        [Obfuscation]
        public float FontSize { get; set; } = 15F;

        [Obfuscation]
        public int Top { get; set; }

        [Obfuscation]
        public int Width { get; set; }

        [Obfuscation]
        public int Height { get; set; }

        public Image GetImage()
        {
            var image = ImageProcessHelper.Base64StringToImage(Image);
            if (CommonSetting.夜间模式)
                image = ImageProcessHelper.InverseImage(new Bitmap(image));
            return image;
        }

        public void Click(object sender, System.EventArgs e)
        {
            try
            {
                switch (Type)
                {
                    case PlugType.Url:
                        CommonMethod.DetermineCall(FrmMain.FrmTool, () =>
                        {
                            var view = new FrmViewUrl
                            {
                                Url = Url,
                                WindowState = System.Windows.Forms.FormWindowState.Maximized,
                                StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen,
                                Icon = FrmMain.FrmTool.Icon,
                                Text = Desc ?? Name
                            };
                            if (Width > 0)
                            {
                                view.WindowState = System.Windows.Forms.FormWindowState.Normal;
                                view.Width = Width;
                            }
                            if (Height > 0)
                            {
                                view.WindowState = System.Windows.Forms.FormWindowState.Normal;
                                view.Height = Height;
                            }
                            view.Show();
                        });
                        break;
                    case PlugType.Text:
                        CommonMethod.ShowNotificationTip(Desc, null, 10 * 1000);
                        break;
                }
            }
            catch { }
        }
    }

    [Obfuscation]
    internal enum ButtonType
    {
        Image = 0,
        ImageAndText = 1,
    }

    [Obfuscation]
    internal enum PlugType
    {
        Url = 0,
        Text = 1,
    }
}
