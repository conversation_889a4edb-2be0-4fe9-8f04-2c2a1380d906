using OCRTools.Common;
using System;
using System.Drawing;
using System.Runtime.InteropServices;

namespace OCRTools
{
    public class CursorData : IDisposable
    {
        public CursorData()
        {
            UpdateCursorData();
        }

        public IntPtr Handle { get; private set; }

        public bool IsVisible { get; private set; }

        public Point Position { get; private set; }

        public bool IsValid => Handle != IntPtr.Zero && IsVisible;

        public void Dispose()
        {
            if (Handle != IntPtr.Zero)
            {
                DestroyIcon(Handle);
                Handle = IntPtr.Zero;
            }
        }

        [DllImport("user32.dll")]
        public static extern bool GetCursorInfo(out CursorInfo pci);

        [DllImport("user32.dll")]
        public static extern IntPtr CopyIcon(IntPtr hIcon);

        [DllImport("user32.dll")]
        public static extern bool GetIconInfo(IntPtr hIcon, out IconInfo piconinfo);

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool GetCursorPos(out POINT lpPoint);

        public static Point GetCursorPosition()
        {
            if (GetCursorPos(out var lpPoint)) return (Point)lpPoint;
            return Point.Empty;
        }

        public static Point GetZeroBasedMousePosition()
        {
            return AutoSelectApi.ScreenToClient(GetCursorPosition());
        }

        public void UpdateCursorData()
        {
            var pci = default(CursorInfo);
            pci.cbSize = Marshal.SizeOf(pci);
            if (!GetCursorInfo(out pci)) return;
            IsVisible = pci.flags == 1;
            if (!IsVisible) return;
            Handle = CopyIcon(pci.hCursor);
            if (GetIconInfo(Handle, out var piconinfo))
            {
                var zeroBasedMousePosition = GetZeroBasedMousePosition();
                Position = new Point(zeroBasedMousePosition.X - piconinfo.xHotspot,
                    zeroBasedMousePosition.Y - piconinfo.yHotspot);
                if (piconinfo.hbmMask != IntPtr.Zero) NativeMethods.DeleteObject(piconinfo.hbmMask);
                if (piconinfo.hbmColor != IntPtr.Zero) NativeMethods.DeleteObject(piconinfo.hbmColor);
            }
        }

        public void DrawCursor(IntPtr hdcDest, Point cursorOffset)
        {
            if (IsValid)
            {
                var point = new Point(Position.X - cursorOffset.X, Position.Y - cursorOffset.Y);
                DrawIconEx(hdcDest, point.X, point.Y, Handle, 0, 0, 0, IntPtr.Zero, 3);
            }
        }

        [DllImport("user32.dll")]
        public static extern bool DrawIconEx(IntPtr hdc, int xLeft, int yTop, IntPtr hIcon, int cxWidth, int cyHeight,
            int istepIfAniCur, IntPtr hbrFlickerFreeDraw, int diFlags);

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool DestroyIcon(IntPtr hIcon);

        public struct CursorInfo
        {
            public int cbSize;

            public int flags;

            public IntPtr hCursor;

            public Point ptScreenPos;
        }

        public struct IconInfo
        {
            public bool fIcon;

            public int xHotspot;

            public int yHotspot;

            public IntPtr hbmMask;

            public IntPtr hbmColor;
        }

        public struct POINT
        {
            public int X;

            public int Y;

            public POINT(int x, int y)
            {
                X = x;
                Y = y;
            }

            public static explicit operator Point(POINT p)
            {
                return new Point(p.X, p.Y);
            }

            public static explicit operator POINT(Point p)
            {
                return new POINT(p.X, p.Y);
            }
        }
    }
}