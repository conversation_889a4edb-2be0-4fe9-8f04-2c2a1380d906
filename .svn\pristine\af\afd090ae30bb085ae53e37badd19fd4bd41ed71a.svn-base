using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace OCRTools
{
    /// <summary>
    /// 支持多模式文字交互的图片查看器基类
    /// 采用处理器模式，支持不同的交互模式
    /// </summary>
    public abstract class MultiModeImageViewer : ImageBox
    {
        #region 共同数据结构
        
        /// <summary>
        /// 文字区域数据 - 所有模式共享
        /// </summary>
        protected List<TextCellInfo> textCells = new List<TextCellInfo>();
        
        #endregion

        #region 处理器架构支持
        
        /// <summary>
        /// 当前活动的处理器
        /// </summary>
        protected IImageModeHandler CurrentHandler { get; set; }
        
        /// <summary>
        /// 处理器字典 - 支持多种模式
        /// </summary>
        protected Dictionary<string, IImageModeHandler> Handlers { get; } = new Dictionary<string, IImageModeHandler>();
        
        /// <summary>
        /// 注册处理器
        /// </summary>
        /// <param name="modeName">模式名称</param>
        /// <param name="handler">处理器实例</param>
        protected void RegisterHandler(string modeName, IImageModeHandler handler)
        {
            Handlers[modeName] = handler;
            handler.Initialize(this);
        }
        
        /// <summary>
        /// 切换模式
        /// </summary>
        /// <param name="modeName">模式名称</param>
        protected virtual void SwitchMode(string modeName)
        {
            if (Handlers.TryGetValue(modeName, out var handler))
            {
                // 清理当前处理器状态
                CurrentHandler?.ClearState();
                
                // 切换处理器
                CurrentHandler = handler;
                
                // 重新绑定数据
                CurrentHandler?.BindData(Image, textCells);
                
                Invalidate();
            }
        }
        
        #endregion

        #region 共同工具方法 - 所有模式都需要的基础功能
        
        /// <summary>
        /// 获取指定控件坐标点对应的文字区域
        /// </summary>
        /// <param name="controlPoint">控件坐标点</param>
        /// <returns>对应的文字区域，如果没有则返回null</returns>
        protected TextCellInfo GetCellAtControlPoint(Point controlPoint)
        {
            // 转换为图片坐标
            var imagePoint = GetImagePointFromControl(controlPoint);
            
            // 查找包含该点的文字区域
            return textCells.Where(item => 
                item?.location != null && 
                item.location.Rectangle.Contains(imagePoint)
            ).FirstOrDefault();
        }
        
        /// <summary>
        /// 坐标转换：控件坐标 → 图片坐标
        /// </summary>
        /// <param name="controlPoint">控件坐标</param>
        /// <returns>对应的图片坐标</returns>
        protected Point GetImagePointFromControl(Point controlPoint)
        {
            var scrollOffset = AutoScrollPosition;
            return new Point(
                (int)((controlPoint.X - scrollOffset.X) / ZoomFactor),
                (int)((controlPoint.Y - scrollOffset.Y) / ZoomFactor)
            );
        }
        
        /// <summary>
        /// 坐标转换：图片坐标 → 控件坐标
        /// </summary>
        /// <param name="imagePoint">图片坐标</param>
        /// <returns>对应的控件坐标</returns>
        protected Point GetControlPointFromImage(Point imagePoint)
        {
            var scrollOffset = AutoScrollPosition;
            return new Point(
                (int)(imagePoint.X * ZoomFactor + scrollOffset.X),
                (int)(imagePoint.Y * ZoomFactor + scrollOffset.Y)
            );
        }
        
        /// <summary>
        /// 获取文字区域在控件中的显示矩形
        /// </summary>
        /// <param name="cell">文字区域</param>
        /// <returns>在控件中的显示矩形</returns>
        protected Rectangle GetCellDisplayRect(TextCellInfo cell)
        {
            if (cell?.location == null) return Rectangle.Empty;
            
            var cellRect = cell.location.Rectangle.Zoom(ZoomFactor);
            cellRect.Location = cellRect.Location.Add(AutoScrollPosition);
            return cellRect;
        }
        
        /// <summary>
        /// 复制文字到剪贴板
        /// </summary>
        /// <param name="text">要复制的文字</param>
        protected void CopyTextToClipboard(string text)
        {
            if (string.IsNullOrEmpty(text)) return;
            
            try
            {
                ClipboardService.SetText(text);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"复制到剪贴板失败: {ex.Message}");
            }
        }
        
        #endregion

        #region 事件分发到处理器
        
        protected override void OnMouseDown(MouseEventArgs e)
        {
            // 先调用base，让事件正常触发
            base.OnMouseDown(e);

            // 然后根据模式处理
            if (CurrentHandler is DocumentModeHandler)
            {
                // 文档模式：直接调用处理方法（因为事件绑定可能不工作）
                CurrentHandler.HandleMouseDown(e);
            }
            else
            {
                // 图文模式使用事件分发
                CurrentHandler?.HandleMouseDown(e);
            }
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            // 先调用base，让事件正常触发
            base.OnMouseMove(e);

            // 然后根据模式处理
            if (CurrentHandler is DocumentModeHandler)
            {
                // 文档模式：直接调用处理方法
                CurrentHandler.HandleMouseMove(e);
            }
            else
            {
                // 图文模式使用事件分发
                CurrentHandler?.HandleMouseMove(e);
            }
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            // 先调用base，让事件正常触发
            base.OnMouseUp(e);

            // 然后根据模式处理
            if (CurrentHandler is DocumentModeHandler)
            {
                // 文档模式：直接调用处理方法
                CurrentHandler.HandleMouseUp(e);
            }
            else
            {
                // 图文模式使用事件分发
                CurrentHandler?.HandleMouseUp(e);
            }
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            // 先调用base，让事件正常触发
            base.OnMouseLeave(e);

            // 然后根据模式处理
            if (CurrentHandler is DocumentModeHandler)
            {
                // 文档模式：直接调用处理方法
                CurrentHandler.HandleMouseLeave(e);
            }
            else
            {
                // 图文模式使用事件分发
                CurrentHandler?.HandleMouseLeave(e);
            }
        }
        
        protected override void OnPaint(PaintEventArgs e)
        {
            // 始终先调用base.OnPaint绘制基础内容（图片等）
            base.OnPaint(e);

            // 然后让处理器绘制额外内容（与TextSelectableImageViewer的Paint事件绑定行为一致）
            CurrentHandler?.HandlePaint(e);
        }
        
        protected override void OnZoomChanged(EventArgs e)
        {
            CurrentHandler?.HandleZoomChanged(e);
            base.OnZoomChanged(e);
        }

        protected override void OnMouseWheel(MouseEventArgs e)
        {
            if (CurrentHandler != null)
            {
                // 如果有处理器，让处理器处理，但仍然调用base（滚轮缩放功能需要保留）
                CurrentHandler.HandleMouseWheel(e);
                base.OnMouseWheel(e);
            }
            else
            {
                // 如果没有处理器，调用base方法
                base.OnMouseWheel(e);
            }
        }
        
        #endregion

        #region 抽象方法 - 子类定义具体模式
        
        /// <summary>
        /// 初始化所有支持的模式处理器
        /// </summary>
        protected abstract void InitializeModeHandlers();
        
        /// <summary>
        /// 绑定数据 - 子类可以重写以支持特殊绑定需求
        /// </summary>
        /// <param name="image">图片</param>
        /// <param name="regions">文字区域列表</param>
        public virtual void BindImageAndTextRegions(Image image, List<TextCellInfo> regions)
        {
            textCells = regions ?? new List<TextCellInfo>();
            Image = image;
            
            // 通知当前处理器
            CurrentHandler?.BindData(image, regions);
            
            Invalidate();
        }
        
        #endregion
        
        #region 资源清理
        
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // 清理所有处理器
                foreach (var handler in Handlers.Values)
                {
                    handler?.Dispose();
                }
                Handlers.Clear();
                CurrentHandler = null;
            }
            base.Dispose(disposing);
        }
        
        #endregion
    }
}
