using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class AutoSizeTextBox : TextBox
    {
        private readonly Panel panel;
        public Label label;

        public AutoSizeTextBox()
        {
            label = new Label
            {
                AutoSize = true
            };
            panel = new Panel();
            panel.Controls.Add(label);
        }

        public void ArrangeTextBoxSize()
        {
            label.AutoSize = true;
            label.MaximumSize = new Size(Width, 0);
            label.Font = Font;
            if (Text.EndsWith(Environment.NewLine))
                label.Text = Text + "补";
            else
                label.Text = Text;
            ScrollBars = ScrollBars.None;
            Height = label.Height;
        }

        protected override void OnTextChanged(EventArgs e)
        {
            base.OnTextChanged(e);
            ArrangeTextBoxSize();
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            base.OnKeyDown(e);
            if (e.Control && e.KeyCode == Keys.A)
            {
                SelectAll();
                e.SuppressKeyPress = true;
            }
        }
    }
}