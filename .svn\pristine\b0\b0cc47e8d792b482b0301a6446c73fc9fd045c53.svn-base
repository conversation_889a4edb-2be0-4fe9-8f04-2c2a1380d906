﻿using System;
using System.Reflection;

namespace OcrMain
{
    internal enum LocalOcrType
    {
        飞浆Mobile = 90001,
        飞浆Server = 90002,
        中文识别Lite = 90003,
        WindowsOcr = 90004,
    }
    [Obfuscation]
    internal class OcrContent
    {
        [Obfuscation] public int ocrType { get; set; }

        [Obfuscation] public string id { get; set; }

        [Obfuscation] public int processId { get; set; }

        [Obfuscation] public string processName { get; set; }

        [Obfuscation] public ResultEntity result { get; set; }

        [Obfuscation]
        public int ProcessBy { get; set; }

        [Obfuscation]
        public string Identity { get; set; }
    }

    [Obfuscation]
    internal class ResultEntity
    {
        [Obfuscation] public string autoText { get; set; }

        [Obfuscation] public string spiltText { get; set; }

        [Obfuscation] public string spiltLocText { get; set; }

        [Obfuscation] public string verticalText { get; set; }

        [Obfuscation] public int resultType { get; set; }

    }
    [Serializable]
    [Obfuscation]
    public class TextCellInfo
    {
        [Obfuscation]
        public string words { get; set; }

        [Obfuscation]
        public LocationInfo location { get; set; }

        [Obfuscation]
        public bool IsProcessed { get; set; }

        [Obfuscation]
        public int PageIndex { get; set; }

        public override string ToString()
        {
            return string.Format("{0},Location:{1}", words, location);
        }
    }

    [Serializable]
    [Obfuscation]
    public class LocationInfo
    {
        [Obfuscation]
        public double left { get; set; }
        [Obfuscation]
        public double top { get; set; }
        [Obfuscation]
        public double width { get; set; }
        [Obfuscation]
        public double height { get; set; }
        [Obfuscation]
        public string words { get; set; }

        public override string ToString()
        {
            return string.Format("top:{0},left:{1},width:{2},height:{3}", top, left, width, height);
        }
    }
}
