﻿using OCRTools;
using System;
using System.Drawing;

namespace ShareX.ScreenCaptureLib
{
    public class ResizeNode : ImageEditorControl
    {
        public const int DefaultSize = 13;

        private Point position;

        public Point Position
        {
            get
            {
                return position;
            }
            set
            {
                position = value;

                Rectangle = new Rectangle(position.X - ((Size - 1) / 2), position.Y - ((Size - 1) / 2), Size, Size);
            }
        }

        public int Size { get; set; }

        public bool AutoSetSize { get; set; } = true;

        private NodeShape shape;

        public NodeShape Shape
        {
            get
            {
                return shape;
            }
            set
            {
                shape = value;

                if (AutoSetSize)
                {
                    if (shape == NodeShape.CustomNode && CustomNodeImage != null)
                    {
                        Size = Math.Max(CustomNodeImage.Width, CustomNodeImage.Height);
                    }
                    else
                    {
                        Size = DefaultSize;
                    }
                }
            }
        }

        public Image CustomNodeImage { get; private set; }

        public ResizeNode(int x = 0, int y = 0)
        {
            Shape = NodeShape.Square;
            Position = new Point(x, y);
        }

        public void SetCustomNode(Image customNodeImage)
        {
            CustomNodeImage = customNodeImage;
            Shape = NodeShape.CustomNode;
        }

        public override void OnDraw(Graphics g)
        {
            Rectangle rect = Rectangle.SizeOffset(-1);

            switch (Shape)
            {
                case NodeShape.Square:
                    g.DrawRectangle(Pens.White, rect.Offset(-1));
                    g.DrawRectangle(Pens.Black, rect);
                    break;
                default:
                case NodeShape.Circle:
                    g.DrawEllipse(Pens.White, rect.Offset(-1));
                    g.DrawEllipse(Pens.Black, rect);
                    break;
                case NodeShape.Diamond:
                    g.DrawDiamond(Pens.White, rect.Offset(-1));
                    g.DrawDiamond(Pens.Black, rect);
                    break;
                case NodeShape.CustomNode when CustomNodeImage != null:
                    g.DrawImage(CustomNodeImage, Rectangle);
                    break;
            }
        }
    }
}