﻿using OCRTools.Common;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace OCRTools
{
    public class DnsHelper
    {
        private static readonly Dictionary<string, string> DicServers = new Dictionary<string, string>();

        public static string GetDnsIp(string host)
        {
            var result = "";
            try
            {
                if (!string.IsNullOrEmpty(host) && !IsIPv4(host))
                {
                    result = GetDnsCache(host);

                    if (string.IsNullOrEmpty(result))
                    {
                        result = ToIpAddress(host);

                        if (!IsIPv4(result))
                            result = string.Empty;
                    }
                }
            }
            catch
            {
            }

            return result;
        }

        private static List<string> lstDns = new List<string>() { "************", "***************", "*********", "*******", "" };

        public static string InitByCName(string host)
        {
            var result = string.Empty;

            var lstTask = lstDns.AsParallel().Select(dns => Task.Factory.StartNew(() =>
            {
                var strUrl = GetCNameFromNsLookUp(host, dns);

                if (string.IsNullOrEmpty(strUrl) || !strUrl.Contains("."))
                {
                    System.Threading.Thread.Sleep(10000);
                }
                else
                {
                    result = strUrl;
                }
            })).ToArray();
            Task.WaitAny(lstTask);
            return result;
        }

        public static void InitServerService()
        {
            var timerInfo = new TimerInfo
            {
                TimerType = "LoopMinutes",
                DateValue = 1,
                IsExecFirst = true,
            };
            var thread = TimerTaskService.CreateTimerTaskService(timerInfo, (a, b, c) =>
            {
                Init();
            });
            thread.Start();
        }

        public static bool Init()
        {
            GetRealIp();
            SetServerUrl();
            return lstAccountHost?.Count > 0;
        }

        private static void SetServerUrl()
        {
            if (CommonString.HostAccount == null || string.IsNullOrEmpty(CommonString.HostAccount.Host)
                || !lstAccountHost.Any(p => Equals(CommonString.HostAccount.Ip, p.Ip) && Equals(CommonString.HostAccount.Host, p.Host)))
            {
                CommonString.HostAccount = lstAccountHost.FirstOrDefault();
            }
            if (CommonString.HostUpdate == null || string.IsNullOrEmpty(CommonString.HostUpdate.Host)
                || !lstAccountHost.Any(p => Equals(CommonString.HostUpdate.Ip, p.Ip) && Equals(CommonString.HostUpdate.Host, p.Host)))
            {
                CommonString.HostUpdate = lstAccountHost.FirstOrDefault();
            }
            if (CommonString.HostCode == null || string.IsNullOrEmpty(CommonString.HostCode.Host)
                || !lstOcrHost.Any(p => Equals(CommonString.HostCode.Ip, p.Ip) && Equals(CommonString.HostCode.Host, p.Host)))
            {
                CommonString.HostCode = lstOcrHost.FirstOrDefault();
            }
        }

        public static void ReportError(string host, string ip)
        {
            var index = CommonString.IsSelfHost(host);
            if (index <= 0)
            {
                return;
            }
            switch (index)
            {
                case 1:
                    //如果跟当前Site一致，特殊处理，否则跳过
                    if (Equals(CommonString.HostAccount.Ip, ip) && CommonString.HostAccount.Host.Contains(host))
                    {
                        var site = new SiteInfo();
                        //如果池子中不存在，重新设置Host
                        if (!lstAccountHost.Any(p => p.Host.Contains(host) && Equals(p.Ip, ip)))
                        {
                            site = lstAccountHost.FirstOrDefault();
                        }
                        else
                        {
                            site = lstAccountHost.FirstOrDefault(p => !p.Host.Contains(host) || !Equals(p.Ip, ip));
                        }
                        if (site != null && !string.IsNullOrEmpty(site.Host))
                        {
                            CommonString.HostAccount = site;
                        }
                    }
                    break;
                case 2:
                    //如果跟当前Site一致，特殊处理，否则跳过
                    if (Equals(CommonString.HostUpdate.Ip, ip) && CommonString.HostUpdate.Host.Contains(host))
                    {
                        var site = new SiteInfo();
                        //如果池子中不存在，重新设置Host
                        if (!lstAccountHost.Any(p => p.Host.Contains(host) && Equals(p.Ip, ip)))
                        {
                            site = lstAccountHost.FirstOrDefault();
                        }
                        else
                        {
                            site = lstAccountHost.FirstOrDefault(p => !p.Host.Contains(host) || !Equals(p.Ip, ip));
                        }
                        if (site != null && !string.IsNullOrEmpty(site.Host))
                        {
                            CommonString.HostUpdate = site;
                        }
                    }
                    break;
                case 3:
                    //如果跟当前Site一致，特殊处理，否则跳过
                    if (Equals(CommonString.HostCode.Ip, ip) && CommonString.HostCode.Host.Contains(host))
                    {
                        var site = new SiteInfo();
                        //如果池子中不存在，重新设置Host
                        if (!lstOcrHost.Any(p => p.Host.Contains(host) && Equals(p.Ip, ip)))
                        {
                            site = lstOcrHost.FirstOrDefault();
                        }
                        else
                        {
                            site = lstOcrHost.FirstOrDefault(p => !p.Host.Contains(host) || !Equals(p.Ip, ip));
                        }
                        if (site != null && !string.IsNullOrEmpty(site.Host))
                        {
                            CommonString.HostCode = site;
                        }
                    }
                    break;
            }
        }

        private static void GetRealIp()
        {
            //\"content\":\"<p><span style=\\\"color:#ffffff;\\\">**************</span></p>\\n\"
            var html = WebClientExt.GetHtml("https://support.qq.com/api/v2/348559/faqs/122463?_=" + DateTime.Now.Ticks, "", "", "", "https://support.qq.com/products/348559/faqs/122463", 5);
            if (!string.IsNullOrEmpty(html))
            {
                html = Regex.Unescape(html);
                if (html.Contains(System.Windows.Forms.Application.ProductName))
                {
                    html = CommonMethod.SubString(html, "<span", "</span>");
                    html = CommonMethod.SubString(html, ">").Replace("&amp;", "&");
                }
                else
                {
                    html = string.Empty;
                }
            }
            if (string.IsNullOrEmpty(html))
                html = InitByCName("txt.oldfish.cn");
            if (!string.IsNullOrEmpty(html))
            {
                lstAccountHost = new List<SiteInfo>();
                lstOcrHost = new List<SiteInfo>();
                InitRemoteServer(html);
            }
        }


        private static List<SiteInfo> lstAccountHost = new List<SiteInfo>();
        private static List<SiteInfo> lstOcrHost = new List<SiteInfo>();
        static void InitRemoteServer(string strTmp)
        {
            //[ocr.oldfish.cn&**************|ocr-acc.oldfish.cn&**************][ocr.oldfish.cn&**************|ocr-code.oldfish.cn&**************]
            var lstTmp = strTmp.Replace("]", "").Split(new string[] { "[" }, StringSplitOptions.RemoveEmptyEntries);
            if (lstTmp.Length <= 0)
            {
                return;
            }
            foreach (var item in lstTmp)
            {
                //ocr.oldfish.cn&**************|ocr-acc.oldfish.cn&**************
                var lstItem = item.Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries);
                var index = Array.IndexOf(lstTmp, item);
                foreach (var site in lstItem)
                {
                    SiteInfo info = new SiteInfo
                    {
                        Host = CommonMethod.SubString(site, "", "&")
                    };
                    if (site.Contains("&"))
                    {
                        info.Ip = CommonMethod.SubString(site, "&");
                    }
                    if (index == 0)
                    {
                        if (!lstAccountHost.Exists(p => Equals(p.Host, info.Host) && Equals(p.Ip, info.Ip)))
                        {
                            lstAccountHost.Add(info);
                        }
                    }
                    else
                    {
                        if (!lstOcrHost.Exists(p => Equals(p.Host, info.Host) && Equals(p.Ip, info.Ip)))
                            lstOcrHost.Add(info);
                    }
                    if (lstTmp.Length == 1)
                    {
                        if (!lstOcrHost.Exists(p => Equals(p.Host, info.Host) && Equals(p.Ip, info.Ip)))
                            lstOcrHost.Add(info);
                    }
                }
            }
        }

        static string ToIpAddress(string hostNameOrAddress)
        {
            var addrs = Dns.GetHostAddresses(hostNameOrAddress);
            return addrs.FirstOrDefault(addr => addr.AddressFamily == AddressFamily.InterNetwork)?.ToString();
        }

        private const string strCName = "\"";

        static string GetCNameFromNsLookUp(string strHost, string strNsServer)
        {
            var result = "";
            var strTmp = ExecCmd(string.Format("nslookup -qt=TXT {0} {1}", strHost, strNsServer));
            if (strTmp.Contains(strHost))
                if (strTmp.IndexOf(strCName) > 0)
                {
                    result = CommonMethod.SubString(strTmp, strCName, strCName);
                }

            return result;
        }

        static string GetDnsFromNsLookUp(string strHost, string strNsServer)
        {
            if (string.IsNullOrEmpty(strNsServer))
            {
                return ToIpAddress(strHost);
            }
            var result = "";
            var strTmp = ExecCmd(string.Format("nslookup {0} {1}", strHost, strNsServer));
            if (strTmp.Contains(strHost))
                if (strTmp.IndexOf(strHost) != strTmp.LastIndexOf(strHost))
                {
                    strTmp = strTmp.Substring(strTmp.LastIndexOf(strHost) + strHost.Length);
                    result = CommonMethod.SubString(strTmp, ":", "\n");
                }
            //ConfigHelper._Log.Info(strHost + "[" + result + "]");

            return result;
        }

        static string GetDnsCache(string strHost)
        {
            var cache = "";
            if (DicServers.ContainsKey(strHost)) cache = DicServers[strHost];
            return cache;
        }

        static string ExecCmd(string str)
        {
            //process用于调用外部程序
            using (var p = new Process())
            {
                //调用cmd.exe
                p.StartInfo.FileName = "cmd.exe";
                //不显示程序窗口
                p.StartInfo.CreateNoWindow = true;
                //是否指定操作系统外壳进程启动程序
                p.StartInfo.UseShellExecute = false;
                //可能接受来自调用程序的输入信息
                //重定向标准输入
                p.StartInfo.RedirectStandardInput = true;
                //重定向标准输出
                p.StartInfo.RedirectStandardOutput = true;
                //重定向错误输出
                p.StartInfo.RedirectStandardError = true;
                //启动程序
                p.Start();
                //输入命令
                p.StandardInput.WriteLine(str);
                //一定要关闭。
                p.StandardInput.WriteLine("exit");
                return p.StandardOutput.ReadToEnd();
            }
        }

        public static bool IsIPv4(string input)
        {
            var array = input.Split('.');
            if (array.Length != 4) return false;
            foreach (var t in array)
            {
                if (!IsMatch("^\\d+$", t)) return false;
                if (Convert.ToUInt16(t) > 255) return false;
            }

            return true;
        }

        static bool IsMatch(string pattern, string input)
        {
            if (string.IsNullOrEmpty(input)) return false;
            var regex = new Regex(pattern);
            return regex.IsMatch(input);
        }
    }

    internal class SiteInfo
    {
        public string Host { get; set; }

        public string Ip { get; set; }

        public string FullUrl { get { return string.Format("http://{0}/", Host); } }
    }
}