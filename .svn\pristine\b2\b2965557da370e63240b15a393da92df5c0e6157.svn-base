using OCRTools.Language;
using OCRTools.ScreenCaptureLib;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;

namespace OCRTools.Common
{
    public class GuidePresenterForm : Form
    {
        private Form _mainForm;
        private GuideEntity guide;
        private int _currentGuideIndex = -1;
        private Panel pnlMain;
        private Label lblTitle;
        private Label lblDesc;
        private Button btnPrev;
        private Button btnNext;
        private LinkLabel lnkSkip;
        private Color _guidePanelColor = Color.FromArgb(0, 112, 249);
        private double wScale = 1d;
        private double hScale = 1d;

        private BufferedGraphics _bufferedGraphics;
        private Rectangle _currentHighlightRect = Rectangle.Empty;
        private ArrowDirection _currentArrowDirection;

        private Timer _animationTimer;
        private Point _targetPanelLocation;
        private Point _startPanelLocation;
        private int _animationStep = 0;
        private const int ANIMATION_STEPS = 10;

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern int SendMessage(IntPtr hWnd, Int32 wMsg, bool wParam, Int32 lParam);
        private const int WM_SETREDRAW = 10;

        // 开始禁止窗体重绘
        private void BeginUpdate()
        {
            SendMessage(this.Handle, WM_SETREDRAW, false, 0);
        }

        // 结束禁止窗体重绘并强制刷新
        private void EndUpdate()
        {
            // 启用重绘
            SendMessage(this.Handle, WM_SETREDRAW, true, 0);

            // 刷新页面 - 使用更强制的方式
            this.Refresh();
        }

        public GuidePresenterForm(Form mainForm, GuideEntity entity)
        {
            // 确保窗体支持透明背景
            this.SetStyle(ControlStyles.SupportsTransparentBackColor, true);
            this.BackColor = Color.Transparent;
            this.Opacity = 1.0; // 确保窗体完全不透明

            // 初始化双缓冲绘图
            SetStyle(ControlStyles.AllPaintingInWmPaint |
                    ControlStyles.UserPaint |
                    ControlStyles.DoubleBuffer |
                    ControlStyles.OptimizedDoubleBuffer, true);
            UpdateStyles();

            this.Region = mainForm.Region;

            // 初始化动画计时器
            _animationTimer = new Timer();
            _animationTimer.Interval = 15; // 15毫秒一帧，约60fps
            _animationTimer.Tick += AnimationTimer_Tick;

            guide = entity;
            if (guide.ShowSummary && !guide.Items[0].Rect.IsEmpty)
            {
                var strSummary = guide.Desc;
                if (string.IsNullOrEmpty(strSummary))
                {
                    strSummary = string.Join("\n", guide.Items.Where(p => p.Summary).Select(p => p.Title.CurrentText(true))).Trim();
                    if (string.IsNullOrEmpty(strSummary))
                    {
                        strSummary = guide.Title;
                    }
                }
                guide.Items.Insert(0, new GuideItem()
                {
                    Title = guide.Title,
                    Desc = strSummary,
                    Rect = Rectangle.Empty,
                    Exec = guide.Items[0].Exec
                });
                guide.Items[1].Exec = null;
            }
            _mainForm = mainForm;
            if (!mainForm.Visible)
            {
                mainForm.Show();
                Update();
            }
            if (mainForm.WindowState != FormWindowState.Normal)
            {
                mainForm.WindowState = FormWindowState.Normal;
                Update();
            }
            wScale = _mainForm.Width * 1d / guide.BaseSize.Width;
            hScale = _mainForm.Height * 1d / guide.BaseSize.Height;
            FormBorderStyle = FormBorderStyle.None;
            ShowInTaskbar = false;
            Margin = CommonString.PaddingZero;
            Padding = CommonString.PaddingZero;
            BackgroundImageLayout = ImageLayout.Stretch;
            InitializeControls();
            CommonMethod.EnableDoubleBuffering(this);
            Shown += GuidePresenterForm_Shown;
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            if (keyData == Keys.Escape)
            {
                CloseGuide();
                return true;
            }
            if (keyData == Keys.Left || keyData == Keys.Up)
            {
                ShowPreviousGuide();
                return true;
            }
            if (keyData == Keys.Right || keyData == Keys.Down)
            {
                ShowNextGuide(false);
                return true;
            }
            return base.ProcessCmdKey(ref msg, keyData);
        }

        private void GuidePresenterForm_Shown(object sender, EventArgs e)
        {
            dicImage = new Dictionary<string, Bitmap>();
            RefreshBackImg(guide.Items[0].Exec);
            Bounds = _mainForm.Bounds;
            Location = _mainForm.Location;
            if (guide?.Items?.Count > 0)
            {
                ShowNextGuide(false);
            }
        }

        private Dictionary<string, Bitmap> dicImage;

        private Bitmap _backgroundImage; // 存储背景截图

        private void RefreshBackImg(string exec)
        {
            var strKey = string.IsNullOrEmpty(exec) ? "DEFAULT" : exec;
            if (!dicImage.TryGetValue(strKey, out _backgroundImage) || _backgroundImage == null)
            {
                var start = ServerTime.DateTime.Ticks;
                if (!string.IsNullOrEmpty(exec))
                    _mainForm.ExecuteScript(exec);

                var rect = new Rectangle();
                var image = Screenshot.CaptureHandle(_mainForm.Handle, ref rect);
                dicImage[strKey] = image;
                _backgroundImage = image;
                Console.Write(strKey + ":" + new TimeSpan(ServerTime.DateTime.Ticks - start).TotalMilliseconds.ToString("F0") + "ms");
            }
        }

        private float _MaxTitleFontSize;
        private float _MaxDescFontSize;

        private void InitializeControls()
        {
            // 创建引导提示框及内部控件 - 使用更纯正的蓝色，与设计图一致
            // 调整为更深郁的蓝色，更接近设计图
            _guidePanelColor = Color.FromArgb(255, 30, 110, 235); // 设计图中的蓝色

            pnlMain = new Panel
            {
                BackColor = _guidePanelColor,
                Size = new Size(Math.Min(_mainForm.Width, (int)(450 * wScale)), Math.Min((int)(196 * hScale), _mainForm.Width)),
                Location = new Point(100, 100),
                Dock = DockStyle.None,
            };

            // 使用精确的圆角和简洁的效果，精确匹配设计图
            int cornerRadius = 12; // 设计图中的圆角大小
            pnlMain.Paint += (s, e) =>
            {
                // 高质量绘制模式
                e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
                e.Graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                e.Graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;
                e.Graphics.CompositingQuality = CompositingQuality.HighQuality;

                // 准备路径
                Rectangle rect = new Rectangle(0, 0, pnlMain.Width, pnlMain.Height);
                GraphicsPath path = CreateRoundedRectanglePath(rect, cornerRadius);

                // 1. 外部阴影 - 精简版
                using (Pen shadowPen = new Pen(Color.FromArgb(40, 0, 0, 0), 2.0f))
                {
                    // 绘制简单的阴影边框，与设计图一致
                    Rectangle shadowRect = new Rectangle(0, 0, pnlMain.Width, pnlMain.Height);
                    GraphicsPath shadowPath = CreateRoundedRectanglePath(shadowRect, cornerRadius);

                    for (int i = 0; i < 3; i++) // 多层阴影融合
                    {
                        using (Pen p = new Pen(Color.FromArgb(10, 0, 0, 0), 1.0f))
                        {
                            Rectangle sr = new Rectangle(1 + i, 1 + i, pnlMain.Width - 2, pnlMain.Height - 2);
                            GraphicsPath sp = CreateRoundedRectanglePath(sr, cornerRadius);
                            e.Graphics.DrawPath(p, sp);
                            sp.Dispose();
                        }
                    }
                }

                // 2. 精细的渐变背景 - 从左上到右下的微妙渐变，精确匹配设计图
                using (LinearGradientBrush gradientBrush = new LinearGradientBrush(
                    rect,
                    Color.FromArgb(255, 25, 105, 230), // 左上角的蓝色(稍深)
                    Color.FromArgb(255, 38, 118, 240), // 右下角的蓝色(稍浅)
                    45f)) // 从左上到右下的渐变角度
                {
                    // 调整渐变控制点，使渐变更加精细和自然
                    ColorBlend colorBlend = new ColorBlend(4);
                    colorBlend.Colors = new Color[] {
                        Color.FromArgb(255, 25, 105, 230),   // 左上角深蓝
                        Color.FromArgb(255, 30, 110, 235),   // 左下角中蓝
                        Color.FromArgb(255, 35, 115, 238),   // 右上角中蓝
                        Color.FromArgb(255, 38, 118, 240)    // 右下角浅蓝
                    };
                    colorBlend.Positions = new float[] { 0.0f, 0.4f, 0.6f, 1.0f };
                    gradientBrush.InterpolationColors = colorBlend;

                    // 填充背景
                    e.Graphics.FillPath(gradientBrush, path);
                }

                // 3. 微妙的内边框效果
                using (Pen innerBorderPen = new Pen(Color.FromArgb(70, 255, 255, 255), 1.0f))
                {
                    innerBorderPen.Alignment = PenAlignment.Inset;
                    e.Graphics.DrawPath(innerBorderPen, path);
                }

                // 4. 轻微的顶部高光线
                Rectangle topLineRect = new Rectangle(5, 1, pnlMain.Width - 10, 1);
                using (SolidBrush topLineBrush = new SolidBrush(Color.FromArgb(50, 255, 255, 255)))
                {
                    e.Graphics.FillRectangle(topLineBrush, topLineRect);
                }
            };

            // 设置面板区域为圆角矩形
            pnlMain.Region = new Region(CreateRoundedRectanglePath(
                new Rectangle(0, 0, pnlMain.Width, pnlMain.Height), cornerRadius));

            Controls.Add(pnlMain);

            // 标题标签 - 保留样式调整但恢复原始颜色
            lblTitle = new Label
            {
                BackColor = Color.Transparent,
                Font = CommonString.GetSysBoldFont(20),
                ForeColor = Color.White,
                Padding = new Padding(24, 18, 24, 0), // 保留调整的间距
                AutoSize = false,
                Size = new Size(pnlMain.Width, (int)(45 * hScale))
            };
            pnlMain.Controls.Add(lblTitle);

            // 描述标签 - 恢复原始颜色
            lblDesc = new Label
            {
                BackColor = Color.Transparent,
                Font = CommonString.GetSysNormalFont(15),
                ForeColor = Color.White, // 原始的白色
                Padding = new Padding(24, 0, 24, 0), // 保留调整的间距
                AutoSize = false,
                TextAlign = ContentAlignment.MiddleLeft,
                Size = new Size(pnlMain.Width, (int)(90 * hScale)),
                Top = lblTitle.Top + lblTitle.Height,
            };
            pnlMain.Controls.Add(lblDesc);

            // 创建引导提示框中的按钮 - 恢复原始颜色方案
            lnkSkip = new LinkLabel
            {
                BackColor = Color.Transparent,
                Text = "跳过".CurrentText(),
                ForeColor = Color.FromArgb(165, 205, 253), // 原始的颜色
                LinkColor = Color.FromArgb(165, 205, 253), // 原始的颜色
                ActiveLinkColor = Color.White,
                //BackColor = _guidePanelColor, // 原始的背景色
                Font = CommonString.GetSysBoldFont(13),
                Location = new Point(24, pnlMain.Height - (int)(42 * hScale)), // 保留调整的位置
                AutoSize = true,
                LinkBehavior = LinkBehavior.NeverUnderline,
                TabStop = false,
                Cursor = Cursors.Hand // 保留手型光标
            };

            var btnWidth = (int)(100 * wScale);
            var btnFont = CommonString.GetSysBoldFont(16);
            // 创建现代风格的按钮 - 上一步
            btnPrev = new Button
            {
                Text = "上一步".CurrentText(),
                Size = new Size(btnWidth, (int)(36 * hScale)), // 稍微降低高度，更精致
                ForeColor = Color.White,
                BackColor = Color.FromArgb(0, 92, 230), // 使用渐变起始色，更协调
                Font = btnFont,
                Visible = false,
                TabStop = false,
                Location = new Point(pnlMain.Width - 30 - btnWidth * 2, pnlMain.Height - (int)(50 * hScale)),
                Cursor = Cursors.Hand // 保留手型光标
            };

            // 创建现代风格的按钮 - 下一步
            btnNext = new Button
            {
                Text = "下一步".CurrentText(),
                Size = new Size(btnWidth, (int)(36 * hScale)), // 稍微降低高度，更精致
                ForeColor = Color.FromArgb(0, 95, 235),
                BackColor = Color.White,
                Font = btnFont,
                Visible = false,
                TabStop = false,
                Cursor = Cursors.Hand,
                Location = new Point(btnPrev.Left + btnWidth + 10, pnlMain.Height - (int)(50 * hScale))
            };

            // 为按钮添加圆角效果
            btnPrev.Region = new Region(CreateRoundedRectanglePath(
                new Rectangle(0, 0, btnPrev.Width, btnPrev.Height), 8));

            btnNext.Region = new Region(CreateRoundedRectanglePath(
                new Rectangle(0, 0, btnNext.Width, btnNext.Height), 8));

            btnPrev.Click += (s, e) => ShowPreviousGuide();
            btnNext.Click += (s, e) => ShowNextGuide(true);
            lnkSkip.Click += (s, e) => CloseGuide();

            pnlMain.Controls.Add(btnPrev);
            pnlMain.Controls.Add(btnNext);
            pnlMain.Controls.Add(lnkSkip);
            CommonMethod.SetStyle(lnkSkip, ControlStyles.Selectable, false);
            _MaxTitleFontSize = lblTitle.Font.Size;
            _MaxDescFontSize = lblDesc.Font.Size;
        }

        private void ShowNextGuide(bool isUser)
        {
            if (_currentGuideIndex < guide.Items.Count - 1)
            {
                _currentGuideIndex++;
                ShowGuide(guide.Items[_currentGuideIndex]);
                btnNext.Focus();
            }
            else if (isUser && _currentGuideIndex == guide.Items.Count - 1)
            {
                CloseGuide();
            }
        }

        private void ShowPreviousGuide()
        {
            if (_currentGuideIndex > 0)
            {
                _currentGuideIndex--;
                ShowGuide(guide.Items[_currentGuideIndex]);
                if (btnPrev.Visible)
                    btnPrev.Focus();
                else
                    btnNext.Focus();
            }
        }

        private void ShowGuide(GuideItem item)
        {
            this.BeginUpdate(); // 使用WinAPI禁用所有绘制
            try
            {
                RefreshBackImg(item.Exec);

                // 计算高亮区域
                var rect = item.Rect;
                if (!string.IsNullOrEmpty(item.Ctrl))
                {
                    var targetRect = FindTargetControl(item.Ctrl);
                    if (!targetRect.IsEmpty)
                    {
                        rect = targetRect;
                        var frmPadding = _mainForm.Padding;
                        rect.Y += frmPadding.Top;
                        rect.X += frmPadding.Left;
                        rect = rect.ZoomBig(10);
                    }
                }

                if (!rect.IsEmpty)
                {
                    if (!guide.BaseSize.IsEmpty)
                    {
                        // 等比例缩放
                        var widthScale = Width * 1d / guide.BaseSize.Width;
                        var heightScale = Height * 1d / guide.BaseSize.Height;
                        rect.X = (int)(rect.X * widthScale);
                        rect.Y = (int)(rect.Y * heightScale);
                        rect.Width = (int)(rect.Width * widthScale);
                        rect.Height = (int)(rect.Height * heightScale);
                    }

                    if (rect.X < 0) rect.X += Width;
                    else if (rect.Y < 0) rect.Y += Height;
                }

                // 更新面板和文本内容
                SuspendLayout();

                lblTitle.Font = CommonMethod.ScaleLabelByHeight(item.Title, CommonString.GetUserNormalFont(8F, FontStyle.Bold), lblTitle.Size, _MaxTitleFontSize);
                lblTitle.Text = item.Title;
                lblDesc.Font = CommonMethod.ScaleLabelByHeight(item.Desc, CommonString.GetUserNormalFont(8F), lblDesc.Size, _MaxDescFontSize);
                lblDesc.Text = item.Desc;

                var index = guide.Items.IndexOf(item);
                if (guide.ShowSummary)
                    lnkSkip.Text = "跳过".CurrentText() + " [Esc]" + (index > 0 ? $" ({index}/{guide.Items.Count - 1})" : "");
                else
                    lnkSkip.Text = "跳过".CurrentText() + $" [Esc] ({index + 1}/{guide.Items.Count})";

                // 设置面板位置和箭头
                var arrow = SetGuidePanelPosition(rect);
                UpdateButtonVisibility();

                // 设置高亮区域
                _currentHighlightRect = rect;
                _currentArrowDirection = arrow;

                ResumeLayout(false); // 先不重绘
            }
            finally
            {
                // 恢复窗体重绘功能，并强制刷新 - 一次性显示完整结果
                this.EndUpdate();
            }
        }

        // 创建真正一体化的气泡形状，箭头和矩形完美融合
        private GraphicsPath CreateUnifiedBubblePath(Rectangle rect, int cornerRadius, Point arrowBase, Point arrowTip, int arrowSize, ArrowDirection direction)
        {
            GraphicsPath path = new GraphicsPath();

            // 控制圆角大小，确保圆角不会超过矩形尺寸的一半
            cornerRadius = Math.Min(cornerRadius, Math.Min(rect.Width, rect.Height) / 2);
            int diameter = cornerRadius * 2;

            // 定义矩形边缘的四个圆角区域
            Rectangle topLeft = new Rectangle(rect.X, rect.Y, diameter, diameter);
            Rectangle topRight = new Rectangle(rect.X + rect.Width - diameter, rect.Y, diameter, diameter);
            Rectangle bottomLeft = new Rectangle(rect.X, rect.Y + rect.Height - diameter, diameter, diameter);
            Rectangle bottomRight = new Rectangle(rect.X + rect.Width - diameter, rect.Y + rect.Height - diameter, diameter, diameter);

            // 程序优化适用于窄的矩形，确保矩形宽度小于箭头宽度时仍能正确显示
            int minRectWidth = Math.Max(rect.Width, arrowSize * 2 + cornerRadius);

            // 计算箭头的两个基点坐标
            // 这些点拉宽了，确保箭头和矩形的连接处不会有异常
            Point arrowPoint1, arrowPoint2;

            switch (direction)
            {
                // 每个方向分开管理，确保气泡形状的整体连续性
                case ArrowDirection.Up:
                    // 上箭头，箭头在矩形上方
                    arrowPoint1 = new Point(arrowBase.X - arrowSize, arrowBase.Y);
                    arrowPoint2 = new Point(arrowBase.X + arrowSize, arrowBase.Y);

                    // 检查箭头位置是否会与圆角重叠
                    if (arrowBase.X - arrowSize < rect.X + cornerRadius)
                    {
                        // 如果箭头左侧会夹在圆角内，调整坐标
                        arrowPoint1 = new Point(rect.X + cornerRadius, rect.Y);
                    }
                    if (arrowBase.X + arrowSize > rect.X + rect.Width - cornerRadius)
                    {
                        // 如果箭头右侧会夹在圆角内，调整坐标
                        arrowPoint2 = new Point(rect.X + rect.Width - cornerRadius, rect.Y);
                    }

                    // 从左上角开始绘制路径
                    if (arrowBase.X < rect.X + cornerRadius)
                    {
                        // 当箭头在左上角附近时
                        path.AddLine(arrowPoint1, arrowTip);  // 箭头左侧
                        path.AddLine(arrowTip, arrowPoint2);   // 箭头右侧
                    }
                    else if (arrowBase.X > rect.X + rect.Width - cornerRadius)
                    {
                        // 当箭头在右上角附近时
                        path.AddArc(topLeft, 180, 90);      // 左上角圆弧
                        path.AddLine(arrowPoint1, arrowTip);  // 箭头左侧
                        path.AddLine(arrowTip, arrowPoint2);   // 箭头右侧
                    }
                    else
                    {
                        // 箭头在上边中间
                        path.AddArc(topLeft, 180, 90);      // 左上角圆弧
                        path.AddLine(new Point(rect.X + cornerRadius, rect.Y), arrowPoint1);
                        path.AddLine(arrowPoint1, arrowTip);  // 箭头左侧
                        path.AddLine(arrowTip, arrowPoint2);   // 箭头右侧
                        path.AddLine(arrowPoint2, new Point(rect.X + rect.Width - cornerRadius, rect.Y));
                    }

                    path.AddArc(topRight, 270, 90);     // 右上角圆弧
                    path.AddArc(bottomRight, 0, 90);    // 右下角圆弧
                    path.AddArc(bottomLeft, 90, 90);    // 左下角圆弧
                    break;

                case ArrowDirection.Down:
                    // 下箭头，箭头在矩形下方
                    arrowPoint1 = new Point(arrowBase.X - arrowSize, arrowBase.Y);
                    arrowPoint2 = new Point(arrowBase.X + arrowSize, arrowBase.Y);

                    // 检查箭头位置是否会与圆角重叠
                    if (arrowBase.X - arrowSize < rect.X + cornerRadius)
                    {
                        arrowPoint1 = new Point(rect.X + cornerRadius, rect.Y + rect.Height);
                    }
                    if (arrowBase.X + arrowSize > rect.X + rect.Width - cornerRadius)
                    {
                        arrowPoint2 = new Point(rect.X + rect.Width - cornerRadius, rect.Y + rect.Height);
                    }

                    // 从左上角开始绘制路径
                    path.AddArc(topLeft, 180, 90);      // 左上角圆弧
                    path.AddArc(topRight, 270, 90);     // 右上角圆弧
                    path.AddArc(bottomRight, 0, 90);    // 右下角圆弧

                    // 判断箭头位置
                    if (arrowBase.X < rect.X + cornerRadius)
                    {
                        // 如果箭头在左下角附近
                        path.AddArc(bottomLeft, 90, 90);
                    }
                    else if (arrowBase.X > rect.X + rect.Width - cornerRadius)
                    {
                        // 如果箭头在右下角附近
                        path.AddArc(bottomLeft, 90, 90);
                    }
                    else
                    {
                        // 箭头在下边中间
                        path.AddLine(new Point(rect.X + rect.Width - cornerRadius, rect.Y + rect.Height), arrowPoint2);
                        path.AddLine(arrowPoint2, arrowTip);
                        path.AddLine(arrowTip, arrowPoint1);
                        path.AddLine(arrowPoint1, new Point(rect.X + cornerRadius, rect.Y + rect.Height));
                        path.AddArc(bottomLeft, 90, 90);
                    }
                    break;

                case ArrowDirection.Left:
                    // 左箭头，箭头在矩形左侧
                    arrowPoint1 = new Point(arrowBase.X, arrowBase.Y - arrowSize);
                    arrowPoint2 = new Point(arrowBase.X, arrowBase.Y + arrowSize);

                    // 检查箭头位置是否会与圆角重叠
                    if (arrowBase.Y - arrowSize < rect.Y + cornerRadius)
                    {
                        arrowPoint1 = new Point(rect.X, rect.Y + cornerRadius);
                    }
                    if (arrowBase.Y + arrowSize > rect.Y + rect.Height - cornerRadius)
                    {
                        arrowPoint2 = new Point(rect.X, rect.Y + rect.Height - cornerRadius);
                    }

                    // 从左上角开始绘制路径
                    path.AddArc(topLeft, 180, 90);

                    // 判断箭头位置
                    if (arrowBase.Y < rect.Y + cornerRadius)
                    {
                        // 箭头靠近左上角
                        path.AddLine(new Point(rect.X, rect.Y + cornerRadius), arrowPoint1);
                        path.AddLine(arrowPoint1, arrowTip);
                        path.AddLine(arrowTip, arrowPoint2);
                    }
                    else if (arrowBase.Y > rect.Y + rect.Height - cornerRadius)
                    {
                        // 箭头靠近左下角
                        path.AddLine(new Point(rect.X, rect.Y + cornerRadius), arrowPoint1);
                        path.AddLine(arrowPoint1, arrowTip);
                        path.AddLine(arrowTip, arrowPoint2);
                    }
                    else
                    {
                        // 箭头在左边中间
                        path.AddLine(new Point(rect.X, rect.Y + cornerRadius), arrowPoint1);
                        path.AddLine(arrowPoint1, arrowTip);
                        path.AddLine(arrowTip, arrowPoint2);
                        path.AddLine(arrowPoint2, new Point(rect.X, rect.Y + rect.Height - cornerRadius));
                    }

                    path.AddArc(bottomLeft, 90, 90);
                    path.AddArc(bottomRight, 0, 90);
                    path.AddArc(topRight, 270, 90);
                    break;

                case ArrowDirection.Right:
                    // 右箭头，箭头在矩形右侧
                    arrowPoint1 = new Point(arrowBase.X, arrowBase.Y - arrowSize);
                    arrowPoint2 = new Point(arrowBase.X, arrowBase.Y + arrowSize);

                    // 检查箭头位置是否会与圆角重叠
                    if (arrowBase.Y - arrowSize < rect.Y + cornerRadius)
                    {
                        arrowPoint1 = new Point(rect.X + rect.Width, rect.Y + cornerRadius);
                    }
                    if (arrowBase.Y + arrowSize > rect.Y + rect.Height - cornerRadius)
                    {
                        arrowPoint2 = new Point(rect.X + rect.Width, rect.Y + rect.Height - cornerRadius);
                    }

                    // 从左上角开始绘制路径
                    path.AddArc(topLeft, 180, 90);
                    path.AddArc(topRight, 270, 90);

                    // 判断箭头位置
                    if (arrowBase.Y < rect.Y + cornerRadius)
                    {
                        // 箭头靠近右上角
                        path.AddLine(new Point(rect.X + rect.Width, rect.Y + cornerRadius), arrowPoint1);
                        path.AddLine(arrowPoint1, arrowTip);
                        path.AddLine(arrowTip, arrowPoint2);
                    }
                    else if (arrowBase.Y > rect.Y + rect.Height - cornerRadius)
                    {
                        // 箭头靠近右下角
                        path.AddLine(new Point(rect.X + rect.Width, rect.Y + cornerRadius), arrowPoint1);
                        path.AddLine(arrowPoint1, arrowTip);
                        path.AddLine(arrowTip, arrowPoint2);
                    }
                    else
                    {
                        // 箭头在右边中间
                        path.AddLine(new Point(rect.X + rect.Width, rect.Y + cornerRadius), arrowPoint1);
                        path.AddLine(arrowPoint1, arrowTip);
                        path.AddLine(arrowTip, arrowPoint2);
                        path.AddLine(arrowPoint2, new Point(rect.X + rect.Width, rect.Y + rect.Height - cornerRadius));
                    }

                    path.AddArc(bottomRight, 0, 90);
                    path.AddArc(bottomLeft, 90, 90);
                    break;

                default:
                    // 其他情况下，只绘制圆角矩形
                    path.AddArc(topLeft, 180, 90);
                    path.AddArc(topRight, 270, 90);
                    path.AddArc(bottomRight, 0, 90);
                    path.AddArc(bottomLeft, 90, 90);
                    break;
            }

            // 闭合路径并设置填充模式
            path.CloseAllFigures();
            path.FillMode = FillMode.Winding;

            return path;
        }

        // 创建圆角矩形路径
        private GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius * 2, radius * 2, 180, 90); // 左上角圆弧
            path.AddArc(rect.Right - radius * 2, rect.Y, radius * 2, radius * 2, 270, 90); // 右上角圆弧
            path.AddArc(rect.Right - radius * 2, rect.Bottom - radius * 2, radius * 2, radius * 2, 0, 90); // 右下角圆弧
            path.AddArc(rect.X, rect.Bottom - radius * 2, radius * 2, radius * 2, 90, 90); // 左下角圆弧
            path.CloseFigure();
            return path;
        }

        // 获取箭头起点位置
        private Point GetArrowBasePoint(Rectangle rect, ArrowDirection arrow)
        {
            switch (arrow)
            {
                case ArrowDirection.Up:
                    return new Point(rect.X + rect.Width / 2, rect.Y);
                case ArrowDirection.Down:
                    return new Point(rect.X + rect.Width / 2, rect.Bottom);
                case ArrowDirection.Left:
                    return new Point(rect.X, rect.Y + rect.Height / 2);
                case ArrowDirection.Right:
                    return new Point(rect.Right, rect.Y + rect.Height / 2);
                default:
                    return new Point(rect.X + rect.Width / 2, rect.Bottom);
            }
        }

        // 获取箭头终点位置
        private Point GetArrowTipPoint(Point basePoint, ArrowDirection arrow, int length)
        {
            switch (arrow)
            {
                case ArrowDirection.Up:
                    return new Point(basePoint.X, basePoint.Y - length);
                case ArrowDirection.Down:
                    return new Point(basePoint.X, basePoint.Y + length);
                case ArrowDirection.Left:
                    return new Point(basePoint.X - length, basePoint.Y);
                case ArrowDirection.Right:
                    return new Point(basePoint.X + length, basePoint.Y);
                default:
                    return new Point(basePoint.X, basePoint.Y + length);
            }
        }

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            if (_animationStep >= ANIMATION_STEPS)
            {
                _animationTimer.Stop();
                pnlMain.Location = _targetPanelLocation;
                _animationStep = 0;
                return;
            }

            // 计算当前步骤的位置（使用缓动函数使动画更自然）
            float progress = (float)_animationStep / ANIMATION_STEPS;
            // 使用缓动函数：Cubic ease-out
            float easedProgress = 1 - (float)Math.Pow(1 - progress, 3);

            int newX = (int)(_startPanelLocation.X + ((_targetPanelLocation.X - _startPanelLocation.X) * easedProgress));
            int newY = (int)(_startPanelLocation.Y + ((_targetPanelLocation.Y - _startPanelLocation.Y) * easedProgress));

            pnlMain.Location = new Point(newX, newY);
            _animationStep++;
        }

        private ArrowDirection SetGuidePanelPosition(Rectangle targetArea)
        {
            ArrowDirection arrow;
            // 根据高亮区域的位置自适应调整引导提示框的位置
            int guidePanelWidth = pnlMain.Width;
            int guidePanelHeight = pnlMain.Height;
            int targetControlX = targetArea.X;
            int targetControlY = targetArea.Y;
            int targetControlWidth = targetArea.Width;
            int targetControlHeight = targetArea.Height;
            int screenWidth = Width;
            int screenHeight = Height;

            int guidePanelX, guidePanelY;
            if (targetArea.IsEmpty)
            {
                arrow = ArrowDirection.Down;
                // 在高亮区域下方显示
                guidePanelX = 0;
                guidePanelY = (screenHeight - guidePanelHeight) / 2;
            }
            else
            {
                if (targetControlX + targetControlWidth + guidePanelWidth <= screenWidth)
                {
                    arrow = ArrowDirection.Right;
                    // 在高亮区域右侧显示
                    guidePanelX = targetControlX + targetControlWidth + 20;
                    guidePanelY = targetControlY + (targetControlHeight - guidePanelHeight) / 2;
                }
                else if (targetControlX - guidePanelWidth >= 0)
                {
                    arrow = ArrowDirection.Left;
                    // 在高亮区域左侧显示
                    guidePanelX = targetControlX - guidePanelWidth - 20;
                    guidePanelY = targetControlY + (targetControlHeight - guidePanelHeight) / 2;
                }
                else if (targetControlY + targetControlHeight + guidePanelHeight <= screenHeight)
                {
                    arrow = ArrowDirection.Down;
                    // 在高亮区域下方显示
                    guidePanelX = targetControlX + (targetControlWidth - guidePanelWidth) / 2;
                    guidePanelY = targetControlY + targetControlHeight + 20;
                }
                else
                {
                    arrow = ArrowDirection.Up;
                    // 在高亮区域上方显示
                    guidePanelX = targetControlX + (targetControlWidth - guidePanelWidth) / 2;
                    guidePanelY = targetControlY - guidePanelHeight - 20;
                }
            }

            guidePanelX = Math.Max(0, guidePanelX);
            guidePanelY = Math.Max(0, guidePanelY);

            if (guidePanelX + guidePanelWidth > Width)
            {
                guidePanelX = Width - guidePanelWidth - 5;
            }
            if (guidePanelY + guidePanelHeight > Height)
            {
                guidePanelY = Height - guidePanelHeight - 5;
            }

            // 保存目标位置
            _targetPanelLocation = new Point(guidePanelX, guidePanelY);

            // 如果是第一次显示，直接设置位置
            if (pnlMain.Location.IsEmpty)
            {
                pnlMain.Location = _targetPanelLocation;
            }
            else
            {
                // 否则使用动画过渡
                _startPanelLocation = pnlMain.Location;
                _animationStep = 0;
                _animationTimer.Start();
            }

            return arrow;
        }

        private void UpdateButtonVisibility()
        {
            btnPrev.Visible = (_currentGuideIndex > 0);
            btnNext.Visible = (_currentGuideIndex <= guide.Items.Count - 1);
            lnkSkip.Visible = guide.Items.Count > 1;
            btnNext.Text = (_currentGuideIndex == guide.Items.Count - 1) ? "完成".CurrentText() : "下一步".CurrentText();
        }

        private Rectangle FindTargetControl(string controlName)
        {
            var rectangle = _mainForm.Controls.Find(controlName, true).FirstOrDefault()?.Bounds ?? Rectangle.Empty;
            if (rectangle.IsEmpty)
            {
                rectangle = _mainForm.Controls.OfType<Control>().FirstOrDefault(p => Equals(p.AccessibleDescription, controlName))?.Bounds ?? Rectangle.Empty;
            }
            return rectangle;
        }

        private void CloseGuide()
        {
            _currentGuideIndex = -1;
            Controls.Clear();

            if (guide.Items != null && guide.Items.Count > 0 && !string.IsNullOrEmpty(guide.Items[0].Exec))
                _mainForm.ExecuteScript(guide.Items[0].Exec);

            this.Close();
        }

        // 重写OnPaintBackground方法，防止背景被重复绘制
        // 完全改写背景绘制方法，确保透明度处理正确
        protected override void OnPaintBackground(PaintEventArgs e)
        {
            // 完全交给 OnPaint 处理
            // 不调用基类的 OnPaintBackground，避免绘制黑色背景
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            try
            {
                // 设置高质量绘图属性，确保气泡形状平滑美观
                e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
                e.Graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                e.Graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;
                e.Graphics.CompositingQuality = CompositingQuality.HighQuality;

                // 如果有背景图像，绘制背景
                if (_backgroundImage != null)
                {
                    // 使用正确的绘制模式 - 设置为SourceOver确保透明度正确
                    e.Graphics.CompositingMode = CompositingMode.SourceOver;

                    // 直接绘制背景图片 - 必须用完整尺寸绘制，确保覆盖整个窗体
                    e.Graphics.DrawImage(_backgroundImage, 0, 0, this.Width, this.Height);

                    // 根据是否夜间模式决定遮罩颜色和边框效果 - 参照效果图的高品质配置
                    Color maskColor;
                    Color bubbleBorderColor;
                    float borderWidth;

                    if (CommonSetting.夜间模式)
                    {
                        // 暗黑模式 - 调整版
                        maskColor = Color.FromArgb(235, 0, 0, 0); // 几乎完全不透明的遮罩
                        bubbleBorderColor = Color.FromArgb(255, 255, 255, 255); // 纯白色边框
                        borderWidth = 1.0f; // 精细边框，不占用高亮区域
                    }
                    else
                    {
                        // 正常模式 - 高品质版
                        maskColor = Color.FromArgb(190, 0, 0, 0); // 增强遮罩不透明度，但不会太重
                        bubbleBorderColor = Color.White; // 纯白色边框
                        borderWidth = 1.0f; // 很精致的细边框
                    }

                    // 绘制遮罩和高亮区域
                    using (SolidBrush maskBrush = new SolidBrush(maskColor))
                    {
                        if (_currentHighlightRect.IsEmpty)
                        {
                            // 没有高亮区域，绘制全屏半透明遮罩
                            e.Graphics.FillRectangle(maskBrush, ClientRectangle);
                        }
                        else
                        {
                            // 有高亮区域，创建气泡形状 - 精确调整圆角半径，增加现代感
                            // 扫大一点高亮区域，增加一些空间感
                            Rectangle expandedRect = new Rectangle(
                                _currentHighlightRect.X - 2,
                                _currentHighlightRect.Y - 2,
                                _currentHighlightRect.Width + 4,
                                _currentHighlightRect.Height + 4);

                            GraphicsPath bubblePath = CreateUnifiedBubblePath(
                                expandedRect, 8, // 圆角半径调小一点，与效果图接近
                                GetArrowBasePoint(expandedRect, _currentArrowDirection),
                                GetArrowTipPoint(GetArrowBasePoint(expandedRect, _currentArrowDirection),
                                                _currentArrowDirection, 12),
                                8, _currentArrowDirection);

                            // 创建遮罩区域（除了高亮区域外的全部区域）
                            using (Region formRegion = new Region(ClientRectangle))
                            {
                                formRegion.Exclude(bubblePath);
                                e.Graphics.FillRegion(maskBrush, formRegion);
                            }

                            // 正常模式下，添加边缘增亮效果，更加清晰
                            if (!CommonSetting.夜间模式)
                            {
                                using (Pen innerLightPen = new Pen(Color.FromArgb(255, 255, 255, 255), 1.0f))
                                {
                                    innerLightPen.LineJoin = LineJoin.Round;
                                    e.Graphics.DrawPath(innerLightPen, bubblePath);
                                }
                            }
                            // 暗黑模式下不做额外处理，保持高亮区域原样

                            // 添加高亮区域边框 - 对于亮色模式用纯白，更加清晰相划
                            using (Pen borderPen = new Pen(bubbleBorderColor, borderWidth))
                            {
                                borderPen.LineJoin = LineJoin.Round;
                                e.Graphics.DrawPath(borderPen, bubblePath);
                            }

                            bubblePath.Dispose();
                        }
                    }
                }

                // 使用基类绘制控件 - 这是最后一步
                base.OnPaint(e);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"OnPaint 异常: {ex.Message}");
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // 释放托管资源
                if (_bufferedGraphics != null)
                {
                    _bufferedGraphics.Dispose();
                    _bufferedGraphics = null;
                }

                if (_backgroundImage != null)
                {
                    _backgroundImage.Dispose();
                    _backgroundImage = null;
                }

                if (_animationTimer != null)
                {
                    _animationTimer.Stop();
                    _animationTimer.Dispose();
                    _animationTimer = null;
                }
            }

            base.Dispose(disposing);
        }
    }
}