﻿using System.ComponentModel;
using System.Drawing;

namespace OCRTools
{
    internal enum LoadingType
    {
        蓝色箭头 = 0,
        蓝色圆圈 = 1,
        红色圆圈 = 2,
        旋风 = 4,
        花瓣 = 5,
        Win11 = 6,
        风车 = 7,
        三环 = 8,
        齿轮 = 9
    }

    internal class LoadingTypeConfig
    {
        public int Interval { get; set; }

        public int ImgCount { get; set; }

        public string ImgName { get; set; }

        public bool IsRound { get; set; }
    }

    internal class LoadingTypeHelper
    {
        public static Image GetImageByConfig(LoadingType type, int index = 0)
        {
            var config = GetTypeConfig(type);
            return GetImageByConfig(config, index);
        }

        public static Image GetImageByConfig(LoadingTypeConfig config, int index)
        {
            return (Image)new ComponentResourceManager(typeof(UcLoading)).GetObject(index + config.ImgName);
        }

        public static LoadingTypeConfig GetTypeConfig(LoadingType type)
        {
            var config = new LoadingTypeConfig();
            switch (type)
            {
                default:
                    config.Interval = 60;
                    config.ImgCount = 12;
                    config.IsRound = true;
                    config.ImgName = "_qq";
                    break;
                case LoadingType.蓝色圆圈:
                    config.Interval = 60;
                    config.ImgCount = 12;
                    config.IsRound = true;
                    config.ImgName = "_bl";
                    break;
                case LoadingType.红色圆圈:
                    config.Interval = 60;
                    config.ImgCount = 12;
                    config.IsRound = true;
                    config.ImgName = "_red";
                    break;
                case LoadingType.花瓣:
                    config.Interval = 60;
                    config.ImgCount = 12;
                    config.IsRound = true;
                    config.ImgName = "_花瓣";
                    break;
                case LoadingType.Win11:
                    config.Interval = 60;
                    config.ImgCount = 12;
                    config.IsRound = true;
                    config.ImgName = "_Win8";
                    break;
                case LoadingType.风车:
                    config.Interval = 60;
                    config.ImgCount = 12;
                    config.IsRound = true;
                    config.ImgName = "_风车";
                    break;
                case LoadingType.三环:
                    config.Interval = 60;
                    config.ImgCount = 12;
                    config.IsRound = true;
                    config.ImgName = "_三环";
                    break;
                case LoadingType.齿轮:
                    config.Interval = 60;
                    config.ImgCount = 12;
                    config.IsRound = true;
                    config.ImgName = "_齿轮";
                    break;
                case LoadingType.旋风:
                    config.Interval = 60;
                    config.ImgCount = 12;
                    config.IsRound = true;
                    config.ImgName = "_旋风";
                    break;
            }

            return config;
        }
    }

    internal enum ToolDoubleClickEnum
    {
        显示主窗体 = 1,
        不做任何操作 = 2,
        截图识别 = 0,
        快速截图 = 3,
        截图编辑 = 4,
        快速贴图 = 5,
        截图贴图 = 6,
        粘贴贴图 = 7,
        显隐贴图 = 8,
    }
}