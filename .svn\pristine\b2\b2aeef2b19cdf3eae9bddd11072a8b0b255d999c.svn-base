﻿using OCRTools.Language;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Threading.Tasks;
using System.Web;
using System.Linq;
using System.Threading;

namespace OCRTools.Common
{
    internal class TransAction
    {
        public string source { get; set; }

        public Action<string, string> action { get; set; }
    }

    internal class CommonTranslate
    {
        private static Dictionary<string, Dictionary<string, string>> dicTransCache = new Dictionary<string, Dictionary<string, string>>();
        private static ConcurrentBag<TransAction> DicActions = new ConcurrentBag<TransAction>();

        internal static void InitTranslate()
        {
            try
            {
                dicTransCache = OcrHelper.GetServerConfig<Dictionary<string, Dictionary<string, string>>>("TransCache") ?? new Dictionary<string, Dictionary<string, string>>();
            }
            catch { }
        }

        internal static string Translate(string strSource, bool isNeedTrans = false)
        {
            var result = strSource;
            if (dicTransCache.ContainsKey(strSource) && dicTransCache[strSource].ContainsKey(LanguageHelper.NowLanguage))
                result = dicTransCache[strSource][LanguageHelper.NowLanguage];
#if DEBUG
            else if (isNeedTrans)
            {
                Translate(strSource, null, false);
            }
#endif
            return result;
        }

        internal static void RefreshLanguage()
        {
            Task.Factory.StartNew(() =>
            {
                foreach (var item in DicActions)
                {
                    Translate(item.source, item.action, false);
                }
            });
        }

        internal static void Translate(string strSource, Action<string, string> action = null, bool isUserAdd = true)
        {
            var isExec = false;
            try
            {
                if (string.IsNullOrEmpty(strSource))
                    return;
                if (isUserAdd)
                    DicActions.Add(new TransAction() { source = strSource, action = action });
                if (Equals(LanguageHelper.NowLanguage, LanguageHelper.StrBaseLanguage))
                {
                    return;
                }
                if (dicTransCache.ContainsKey(strSource) && dicTransCache[strSource].ContainsKey(LanguageHelper.NowLanguage))
                {
                    isExec = true;
                    action?.Invoke(strSource, dicTransCache[strSource][LanguageHelper.NowLanguage]);
                }
#if DEBUG
                else
                {
                    //DEBUG模式上传翻译
                    if (System.Text.RegularExpressions.Regex.IsMatch(strSource, @"[\u4e00-\u9fa5]"))
                    {
                        foreach (var item in dicLanguage)
                        {
                            TranslateProcessPool.Add(new TranslateEntity()
                            {
                                lang = item.Key,
                                source = strSource
                            });
                        }
                    }
                }
#endif
            }
            catch { }
            finally
            {
                if (!isExec)
                {
                    try
                    {
                        action?.Invoke(strSource, strSource);
                    }
                    catch { }
                }
            }
        }

#if DEBUG
        private static Dictionary<string, string> dicLanguage = new Dictionary<string, string>();
        private static string StrAuth = "https://edge.microsoft.com/translate/auth";
        private static string StrTransUrl = "https://api.cognitive.microsofttranslator.com/translate?from=zh&to={0}&api-version=3.0";

        public static string TranslageKey = "TransCache";

        public static BlockingCollection<TranslateEntity> TranslateProcessPool = new BlockingCollection<TranslateEntity>();

        static CommonTranslate()
        {
            dicLanguage.Add("zh-TW", "zh-CHT");
            dicLanguage.Add("en-US", "en");
            dicLanguage.Add("ja-JP", "ja");
            dicLanguage.Add("ko-KR", "ko");
            dicLanguage.Add("fr-FR", "fr");
            dicLanguage.Add("de-DE", "de");
            new Thread(p =>
            {
                try
                {
                    foreach (var processEntity in TranslateProcessPool.GetConsumingEnumerable())
                        try
                        {
                            Translate(processEntity.lang, processEntity.source);
                        }
                        catch { }
                }
                catch { }
            })
            { IsBackground = true, Priority = ThreadPriority.Highest }.Start();
        }

        internal class TranslateEntity
        {
            public string lang { get; set; }

            public string source { get; set; }
        }

        public static string Translate(string strLanguage, string strToTrans)
        {
            var result = string.Empty;
            if (dicLanguage.ContainsKey(strLanguage))
            {
                var strAuth = WebClientExt.GetHtml(StrAuth, 10);
                if (!string.IsNullOrEmpty(strAuth))
                {
                    var strPost = "[{\"Text\":\"" + strToTrans + "\"}]";
                    var header = new NameValueCollection() { { "Authorization", "Bearer " + strAuth } };
                    var html = WebClientExt.GetHtml(string.Format(StrTransUrl, dicLanguage[strLanguage]), strPost, 5, header);
                    //LogHelper.Log.Error(string.Format("Html:{0}", html));
                    if (!string.IsNullOrEmpty(html))
                    {
                        var lst = CommonString.JavaScriptSerializer.Deserialize<List<TranslateResult>>(html);
                        if (lst.Count > 0)
                        {
                            result = lst[0]?.translations?.FirstOrDefault().text;
                            if (!string.IsNullOrEmpty(result))
                            {
                                dicTransCache = OcrHelper.GetServerResult<Dictionary<string, Dictionary<string, string>>>("code.ashx?op=trans", CommonString.HostAccount?.FullUrl, "lang=" + strLanguage + "&source=" + HttpUtility.UrlEncode(strToTrans) + "&trans=" + HttpUtility.UrlEncode(result));
                            }
                        }
                    }
                }
            }
            return result;
        }

        internal class TranslateResult
        {
            public List<TranslateResultItem> translations { get; set; }
        }

        internal class TranslateResultItem
        {
            public string text { get; set; }
        }
#endif
    }

}
