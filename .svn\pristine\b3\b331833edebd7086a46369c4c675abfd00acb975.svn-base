﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using OCRTools.Properties;

namespace OCRTools
{
    public class EnumInfo
    {
        public EnumInfo(Enum value)
        {
            Value = value;
            Category = Value.GetCategory();
            Description = Value.GetDescription();
            ImageValue = Value.GetDefaultValue();
        }

        public Enum Value { get; set; }
        public string Category { get; set; }
        public string Description { get; set; }
        public string ImageValue { get; set; }
    }

    public enum CaptureActions
    {
        [Category("截图")] [Description("截图")] [DefaultValue("camera")]
        截图,

        //[Category("截图"), Description("自定义截图"), DefaultValue("固定区域")]
        //自定义截图,
        [Category("截图")] [Description("截图贴图")] [DefaultValue("贴图")]
        截图贴图,

        [Category("截图")] [Description("活动窗口")] [DefaultValue("窗口")]
        活动窗口,

        [Category("截图")] [Description("活动显示器")] [DefaultValue("显示器")]
        活动显示器,

        [Category("截图")] [Description("全屏截图")] [DefaultValue("全屏")]
        全屏,

        [Category("截图")] [Description("固定区域截图")] [DefaultValue("固定区域")]
        固定区域,

        [Category("截图")] [Description("滚动截图")] [DefaultValue("滚动截屏")]
        滚动截屏
    }

    public class CommonEnumAction<T>
    {
        public static ToolStripMenuItem FindMeunItem(ToolStripDropDown parent, T act)
        {
            var info = ((T[]) Enum.GetValues(typeof(T))).OfType<Enum>().Select(x => new EnumInfo(x))
                .FirstOrDefault(p => Equals(p.Value.GetHashCode(), act.GetHashCode()));
            return FindMeunItem(parent, info);
        }

        public static ToolStripMenuItem FindMeunItem(ToolStripDropDown parent, EnumInfo info)
        {
            var tsmResult = parent.Items.OfType<ToolStripMenuItem>().FirstOrDefault(x =>
                Equals(info.Value.GetHashCode(), ((EnumInfo) x.Tag)?.Value.GetHashCode()));
            if (tsmResult == null)
                foreach (ToolStripItem item in parent.Items)
                    if (item is ToolStripMenuItem tsm)
                    {
                        tsmResult = tsm.DropDownItems.OfType<ToolStripMenuItem>().FirstOrDefault(x =>
                            Equals(info.Value.GetHashCode(), ((EnumInfo) x.Tag)?.Value.GetHashCode()));
                        if (tsmResult != null) break;
                    }

            return tsmResult;
        }

        public static ToolStripMenuItem FindMeunItem(ToolStripDropDown parent, string text)
        {
            var tsmResult = parent.Items.OfType<ToolStripMenuItem>().FirstOrDefault(x => x.Text.Contains(text));
            if (tsmResult == null)
                foreach (ToolStripItem item in parent.Items)
                    if (item is ToolStripMenuItem tsm)
                    {
                        tsmResult = tsm.DropDownItems.OfType<ToolStripMenuItem>()
                            .FirstOrDefault(x => x.Text.Contains(text));
                        if (tsmResult != null) break;
                    }

            return tsmResult;
        }

        public static void AddEnumItemsContextMenu(ToolStripDropDown parent, EventHandler clickAction)
        {
            var enums = ((T[]) Enum.GetValues(typeof(T))).OfType<Enum>().Select(x => new EnumInfo(x)).ToArray();

            foreach (var enumInfo in enums)
            {
                var img = FindMenuIcon(enumInfo);
                var tsmItem = new ToolStripMenuItem(enumInfo.Description) {Image = img, Tag = enumInfo};
                tsmItem.Click += clickAction;

                if (!string.IsNullOrEmpty(enumInfo.Category))
                {
                    var tsmParent = parent.Items.OfType<ToolStripMenuItem>()
                        .FirstOrDefault(x => x.Text == enumInfo.Category);

                    if (tsmParent == null)
                    {
                        tsmParent = new ToolStripMenuItem(enumInfo.Category);
                        parent.Items.Add(tsmParent);
                    }

                    tsmParent.DropDownItems.Add(tsmItem);
                }
                else
                {
                    parent.Items.Add(tsmItem);
                }
            }
        }

        public static Image FindMenuIcon(EnumInfo value)
        {
            if (value.Value is CaptureActions)
            {
            }

            if (!string.IsNullOrEmpty(value.ImageValue))
                return Resources.ResourceManager.GetObject(value.ImageValue) as Bitmap;
            return null;
        }
    }

    public static class EnumExtensions
    {
        public static string GetDescription(this Enum value)
        {
            var fi = value.GetType().GetField(value.ToString());

            if (fi != null)
            {
                var attributes = (DescriptionAttribute[]) fi.GetCustomAttributes(typeof(DescriptionAttribute), false);

                if (attributes.Length > 0) return attributes[0].Description;
            }

            return value.ToString();
        }

        public static string GetCategory(this Enum value)
        {
            var fi = value.GetType().GetField(value.ToString());

            if (fi != null)
            {
                var attributes = (CategoryAttribute[]) fi.GetCustomAttributes(typeof(CategoryAttribute), false);

                if (attributes.Length > 0) return attributes[0].Category;
            }

            return value.ToString();
        }

        public static string GetDefaultValue(this Enum value)
        {
            var fi = value.GetType().GetField(value.ToString());

            if (fi != null)
            {
                var attributes = (DefaultValueAttribute[]) fi.GetCustomAttributes(typeof(DefaultValueAttribute), false);

                if (attributes.Length > 0) return attributes[0].Value?.ToString();
            }

            return value.ToString();
        }

        public static int GetIndex(this Enum value)
        {
            return Array.IndexOf(Enum.GetValues(value.GetType()), value);
        }
    }
}