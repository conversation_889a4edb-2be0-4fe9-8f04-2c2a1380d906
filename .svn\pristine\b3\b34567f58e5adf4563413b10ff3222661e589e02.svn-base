﻿using System;
using System.IO;

namespace OCRTools.Common.ImageLib
{
    /// <summary>
    ///     https://upload.cc/
    /// </summary>
    public class ImgUrlImageUpload : BaseImageUpload
    {
        private const string strFileNameSpilt = "\"url\":\"";

        public ImgUrlImageUpload()
        {
            ImageType = ImageTypeEnum.ImgUrl;
        }

        public override string GetResult(byte[] content, string ext)
        {
            var result = "";
            try
            {
                var url = "https://imgurl.org/upload/ftp";
                var file = new UploadFileInfo
                {
                    Name = "file",
                    Filename = "test.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                var html = UploadFileRequest.Post(url, new[] {file}, null);
                if (html.Contains(strFileNameSpilt))
                {
                    result = html.Substring(html.IndexOf(strFileNameSpilt) + strFileNameSpilt.Length);
                    result = result.Substring(0, result.IndexOf("\"")).Replace("\\/", "/");
                }
            }
            catch (Exception)
            {
            }

            return result;
        }
    }
}