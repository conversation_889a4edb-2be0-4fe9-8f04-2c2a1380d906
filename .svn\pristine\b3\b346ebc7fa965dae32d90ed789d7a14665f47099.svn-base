﻿using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Language;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmPicCompare : MetroForm
    {
        public FrmPicCompare()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            Load += FrmPicCompare_Load;
        }

        private void FrmPicCompare_Load(object sender, EventArgs e)
        {
            content.ShowImageTool();
        }

        internal void Init(SpiltMode spiltMode, bool isShowOld)
        {
            content.SpiltModel = spiltMode;
            content.IsShowOldContent = isShowOld;
        }

        internal void Bind(Image image, OcrContent ocrContent = null)
        {
            content.Image = image;
            content.NowDisplayMode = DisplayModel.图文模式;
            if (ocrContent != null)
            {
                content.RefreshStyle();
                content.BindContentByOcr(ocrContent);
                Text = $"{"图文模式".CurrentText()}-【{ocrContent.processName}】";
            }
        }

        private void FrmPicCompare_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape) Close();
        }
    }
}