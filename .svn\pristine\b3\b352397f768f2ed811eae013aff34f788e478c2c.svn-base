﻿using MetroFramework.Forms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmBatchCompress : MetroForm
    {
        private const string STR_PROCESS_FINISHED = "处理成功";
        private const string STR_PROCESS_FAILED = "处理失败";
        private const string STR_PROCESSING = "处理中…";
        private const string STR_PROCESS_NOSTART = "待处理";

        private readonly List<ImageCompressItem> _ocrItems = new List<ImageCompressItem>();

        private BindingList<ImageCompressItem> _bindingItems;

        public FrmBatchCompress()
        {
            InitializeComponent();
            CheckForIllegalCrossThreadCalls = false;
            ShadowType = CommonString.CommonShadowType;
            dgContent.AutoGenerateColumns = false;
            CommonMethod.EnableDoubleBuffering(this);
        }

        private void FrmBatch_Load(object sender, EventArgs e)
        {
            txtSaveToPath.Text =
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), CommonString.FullName);
            InitCompressType();
        }

        private void InitCompressType()
        {
            try
            {
                foreach (CompressType type in Enum.GetValues(typeof(CompressType)))
                {
                    //if (Equals(type, CompressType.助手压缩))
                    //    continue;
                    cmbCompressEngine.Items.Add(type.ToString());
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine("加载类型失败！" + oe.Message);
            }

            if (cmbCompressEngine.SelectedIndex < 0) cmbCompressEngine.SelectedIndex = 0;
        }

        private void btnClearFiles_Click(object sender, EventArgs e)
        {
            _ocrItems.Clear();
            ReBindGridView();
        }

        private void btnRemoveSelected_Click(object sender, EventArgs e)
        {
            if (dgContent.SelectedRows.Count <= 0)
            {
                return;
            }
            foreach (DataGridViewRow row in dgContent.SelectedRows)
            {
                if (row.DataBoundItem is ImageCompressItem item)
                {
                    _ocrItems.Remove(item);
                }
            }
            ReBindGridView();
        }

        private void btnClearSuccess_Click(object sender, EventArgs e)
        {
            _ocrItems.RemoveAll(p => Equals(p.State, STR_PROCESS_FINISHED));
            ReBindGridView();
        }

        private void btnAddFiles_Click(object sender, EventArgs e)
        {
            var openFile = new OpenFileDialog
            {
                Title = "请选择文件",
                Filter = "图片或文档|*.png;*.jpg;*.jpeg;*.bmp;",
                RestoreDirectory = true,
                Multiselect = true
            };
            if (openFile.ShowDialog(this) == DialogResult.OK && openFile.FileNames.Length > 0) AddFileToList(openFile.FileNames);

        }

        private void AddFileToList(string[] files)
        {
            foreach (var item in files)
            {
                if (!CommonString.LstCanProcessFilesExt.Any(p => item.EndsWith(p)))
                {
                    continue;
                }

                if (_ocrItems.Exists(p => Equals(p.FullName, item))) continue;
                var ocrItem = new ImageCompressItem
                {
                    Id = Guid.NewGuid().ToString(),
                    FullName = item,
                    FileName = Path.GetFileName(item),
                    InPutSize = CommonMethod.FormatBytes(item),
                    CompressType = (CompressType)Enum.Parse(typeof(CompressType), cmbCompressEngine.Text, true),
                    State = STR_PROCESS_NOSTART
                };
                _ocrItems.Add(ocrItem);
            }
            ReBindGridView();
        }

        private void btnAddFolder_Click(object sender, EventArgs e)
        {
            var openFile = new FolderBrowserDialog()
            {
                Description = "请选择要处理的文件所在目录",
            };
            if (openFile.ShowDialog(this) == DialogResult.OK && !string.IsNullOrEmpty(openFile.SelectedPath))
                AddFileToList(Directory.GetFiles(openFile.SelectedPath));
        }

        private void ReBindGridView()
        {
            _bindingItems = new BindingList<ImageCompressItem>(_ocrItems);
            _bindingItems.ListChanged += (sender, e) =>
            {
                if (e.ListChangedType == ListChangedType.ItemDeleted
                    || e.ListChangedType == ListChangedType.ItemAdded)
                    _bindingItems.ResetBindings();
            };
            dgContent.FastLoadDataGrid(_bindingItems);
            dgContent.Refresh();
            Text = string.Format("批量压缩{0}", _ocrItems.Count <= 0 ? "" : string.Format("-(共{0}个)", _ocrItems.Count));
            Invalidate();
        }

        private bool isFormExit;

        private int tryCount;

        private void btnProcess_Click(object sender, EventArgs e)
        {
            if (tryCount > 0)
            {
                btnProcess.Text = string.Format("正在重试({0})", tryCount);
            }
            else
            {
                btnProcess.Text = "正在处理…";
            }
            btnProcess.Enabled = false;
            Task.Factory.StartNew(() =>
            {
                try
                {
                    foreach (var ImageCompressItem in _ocrItems.Where(p => Equals(p.State, STR_PROCESS_FAILED)))
                    {
                        ImageCompressItem.State = STR_PROCESS_NOSTART;
                        ImageCompressItem.DtStart = null;
                        ImageCompressItem.DtEnd = null;
                    }
                    while (!isFormExit)
                    {
                        ImageCompressItem item;
                        try
                        {
                            item = _ocrItems.FirstOrDefault(p => Equals(p.State, STR_PROCESS_NOSTART));
                        }
                        catch
                        {
                            continue;
                        }
                        if (item == null)
                        {
                            break;
                        }

                        item.DtStart = ServerTime.DateTime;
                        item.State = STR_PROCESSING;


                        try
                        {
                            if (dgContent.ColumnCount > 3)
                                dgContent.InvalidateColumn(3);
                            var index = _ocrItems.IndexOf(item);
                            if (index > -1)
                                dgContent.CurrentCell = dgContent.Rows[index].Cells[0];
                        }
                        catch { }

                        // ProcessImage
                        Task.Factory.StartNew(() =>
                        {
                            var strPath = chkSameFolder.Checked
                                ? Path.GetDirectoryName(item.FullName)
                                : txtSaveToPath.Text;
                            var result = ImageCompress.CompressImageFile(item.FullName, strPath, item.CompressType);
                            if (!string.IsNullOrEmpty(result))
                            {
                                item.OutPutSize = CommonMethod.FormatBytes(result);
                                item.State = STR_PROCESS_FINISHED;
                                item.DtEnd = ServerTime.DateTime;
                                item.HasResult = true;
                            }
                            else
                            {
                                item.State = STR_PROCESS_FAILED;
                            }
                            try
                            {
                                if (dgContent.ColumnCount > 4)
                                    dgContent.InvalidateColumn(4);
                            }
                            catch { }
                        });

                        SetFaildItem();

                        try
                        {
                            //并行任务数量
                            while (!CommonString.IsExit && !isFormExit && _ocrItems.Count(p => Equals(p.State, STR_PROCESSING)) > nMaxThread.Value - 1)
                            {
                                Thread.Sleep(500);
                                SetFaildItem();
                            }
                        }
                        catch { }
                    }
                }
                catch (Exception exception)
                {
                    Console.WriteLine("btnProcess_Click Error:" + exception.Message);
                }

                try
                {
                    //大循环结束，等待剩余项处理完成
                    while (!CommonString.IsExit && !isFormExit && _ocrItems.Exists(p => Equals(p.State, STR_PROCESSING)))
                    {
                        Thread.Sleep(500);
                        SetFaildItem();
                    }
                }
                catch { }

                RefreshState();

                try
                {
                    //如果存在处理失败的，重新开始处理
                    if (!CommonString.IsExit && !isFormExit && tryCount < nFailedCount.Value + 1 && _ocrItems.Exists(p => Equals(p.State, STR_PROCESS_FAILED)))
                    {
                        tryCount++;
                        btnProcess_Click(sender, e);
                        return;
                    }
                }
                catch { }

                btnProcess.Text = "开始压缩(&S)";
                btnProcess.Enabled = true;
                tryCount = 0;
            });
        }

        private void RefreshState()
        {
            if (_ocrItems.Count > 0)
            {
                Text = string.Format("批量压缩 共{0}个({1})"
                    , _ocrItems.Count
                    , (_ocrItems.Exists(p => Equals(p.State, STR_PROCESS_FINISHED))
                          ? string.Format("成功:{0}", _ocrItems.Count(p => Equals(p.State, STR_PROCESS_FINISHED)))
                          : "")
                      + (_ocrItems.Exists(p => Equals(p.State, STR_PROCESS_FAILED))
                          ? string.Format(" 失败:{0}", _ocrItems.Count(p => Equals(p.State, STR_PROCESS_FAILED)))
                          : "")
                      + (_ocrItems.Exists(p => Equals(p.State, STR_PROCESSING))
                          ? string.Format(" 处理中:{0}", _ocrItems.Count(p => Equals(p.State, STR_PROCESSING)))
                          : "")
                      + (_ocrItems.Exists(p => Equals(p.State, STR_PROCESS_NOSTART))
                          ? string.Format(" 未开始:{0}", _ocrItems.Count(p => Equals(p.State, STR_PROCESS_NOSTART)))
                          : "")
                );
                Invalidate();
            }
        }

        private void SetFaildItem()
        {
            try
            {
                foreach (var failedItem in _ocrItems.Where(p => Equals(p.State, STR_PROCESSING) && p.DtStart.HasValue && p.DtStart < ServerTime.DateTime.AddSeconds(-(int)nTimeOutSecond.Value)))
                {
                    failedItem.State = STR_PROCESS_FAILED;
                    failedItem.DtEnd = ServerTime.DateTime;
                }
            }
            catch { }
        }

        private void btnResultFolder_Click(object sender, EventArgs e)
        {
            CommonMethod.OpenFolder(txtSaveToPath.Text);
        }

        private void btnSelectedPath_Click(object sender, EventArgs e)
        {
            var openFile = new FolderBrowserDialog()
            {
                Description = "请选择要将结果文件放在哪个目录",
            };
            if (openFile.ShowDialog(this) == DialogResult.OK && !string.IsNullOrEmpty(openFile.SelectedPath))
            {
                txtSaveToPath.Text = openFile.SelectedPath;
            }
        }

        private void FrmBatch_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_ocrItems != null && _ocrItems.Exists(p => Equals(p.State, STR_PROCESSING)))
            {
                if (MessageBox.Show(this, "当前有任务正在处理，是否继续退出？", CommonString.StrReminder, MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    isFormExit = true;
                }
                else
                {
                    e.Cancel = true;
                }
            }
        }

        private void dgContent_DragDrop(object sender, DragEventArgs e)
        {
            if (!(e.Data.GetData(DataFormats.FileDrop) is string[] path) || path.Length <= 0) return;
            var lstFiles = path.Where(Path.HasExtension).ToList();
            var lstDirs = path.Where(p => !Path.HasExtension(p)).ToList();
            if (lstDirs.Count > 0)
            {
                lstDirs.ForEach(p =>
                {
                    lstFiles.AddRange(Directory.GetFiles(p, "*", SearchOption.AllDirectories));
                });
            }
            AddFileToList(lstFiles.ToArray());
        }

        private void dgContent_DragEnter(object sender, DragEventArgs e)
        {
            e.Effect = e.Data.GetDataPresent(DataFormats.FileDrop) ? DragDropEffects.Copy : DragDropEffects.None;
        }
    }


    [Obfuscation]
    public class ImageCompressItem
    {
        private string _state;

        [Obfuscation]
        public string Id { get; set; }

        [Obfuscation]
        public string FileName { get; set; }

        [Obfuscation]
        public string InPutSize { get; set; }

        [Obfuscation]
        public CompressType CompressType { get; set; }

        [Obfuscation]
        public string OutPutSize { get; set; }

        [Obfuscation]
        public string FullName { get; set; }

        [Obfuscation]
        public DateTime? DtStart { get; set; }

        [Obfuscation]
        public DateTime? DtEnd { get; set; }

        [Obfuscation]
        public bool HasResult { get; set; }

        [Obfuscation]
        public string State
        {
            get => _state;
            set
            {
                _state = value;
                NotifyPropertyChanged("State");
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        private void NotifyPropertyChanged(string propertyName)
        {
            if (PropertyChanged != null)
                try
                {
                    PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
                }
                catch
                {
                    // ignored
                }
        }
    }
}