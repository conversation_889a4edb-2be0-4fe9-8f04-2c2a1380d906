using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace OCRTools
{
    /// <summary>
    /// 支持精确文字选择的图片查看器
    /// </summary>
    public class TextSelectableImageViewer : PanelPictureView
    {
        private readonly Color SELECTION_FILL_COLOR = Color.FromArgb(100, 0, 120, 215); // 类似PDF的选择颜色

        // 数据结构
        private List<TextCellInfo> textCells = new List<TextCellInfo>();

        // 选择相关字段
        private bool isDragging = false;
        private Point dragStartPoint;
        private Point dragEndPoint;
        private TextCellInfo startCell;
        private TextCellInfo endCell;
        private int startCharIndex = -1;
        private int endCharIndex = -1;

        // UI组件
        public RichTextBox TextRichTextBox { get; set; }

        // 事件
        public event EventHandler<TextSelectionEventArgs> TextSelectionChanged;

        /// <summary>
        /// Cell和字符位置信息
        /// </summary>
        private class CellCharPosition
        {
            public TextCellInfo Cell { get; set; }
            public int CharIndex { get; set; }

            public CellCharPosition(TextCellInfo cell, int charIndex)
            {
                Cell = cell;
                CharIndex = charIndex;
            }
        }

        /// <summary>
        /// Cell选择信息
        /// </summary>
        private class CellSelectionInfo
        {
            public TextCellInfo Cell { get; set; }
            public int StartChar { get; set; }
            public int EndChar { get; set; }

            public CellSelectionInfo(TextCellInfo cell, int startChar, int endChar)
            {
                Cell = cell;
                StartChar = startChar;
                EndChar = endChar;
            }
        }

        public TextSelectableImageViewer()
        {
            // 禁用原有的提示功能，我们自己处理
            IsShowTip = false;

            // 重新绑定鼠标事件
            MouseDown += OnMouseDown;
            MouseMove += OnMouseMove;
            MouseUp += OnMouseUp;
            Paint += OnCustomPaint;
            MouseLeave += OnMouseLeave;
        }

        /// <summary>
        /// 绑定图片和文字区域
        /// </summary>
        public void BindImageAndTextRegions(Image image, List<TextCellInfo> regions)
        {
            textCells = regions ?? new List<TextCellInfo>();

            // 清除选择
            ClearSelection();

            // 设置图片
            Image = image;

            // 标记为绑定模式
            IsBindImageMode = true;

            Invalidate();
        }
        
        /// <summary>
        /// 检测单个文字块的方向
        /// </summary>
        private bool DetectCellDirection(TextCellInfo cell)
        {
            if (cell?.location == null || string.IsNullOrEmpty(cell.words)) return false;

            double width = cell.location.width;
            double height = cell.location.height;
            double aspectRatio = width / height;

            // 竖排判断条件：
            // 1. 高度明显大于宽度（高宽比 > 2.0）
            // 2. 或者高度 >= 宽度且字符数较少（<=3）
            // 3. 或者是单字符且接近正方形

            bool condition1 = height / width > 2.0; // 明显的竖直形状
            bool condition2 = height >= width && cell.words.Length <= 3; // 高>=宽且字符少
            bool condition3 = cell.words.Length == 1 && aspectRatio > 0.5 && aspectRatio < 2.0; // 单字符且接近正方形

            return condition1 || condition2 || condition3;
        }

        private void OnMouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left && textCells.Count > 0)
            {
                // 清除之前的选择
                ClearSelection();

                isDragging = true;
                dragStartPoint = GetImagePoint(e.Location);

                // 找到起始位置的cell和字符索引
                var startPos = GetCellAndCharAtPoint(dragStartPoint);
                startCell = startPos.Cell;
                startCharIndex = startPos.CharIndex;
                endCell = startCell;
                endCharIndex = startCharIndex;

                Invalidate();
            }
        }

        private void OnMouseMove(object sender, MouseEventArgs e)
        {
            if (isDragging && e.Button == MouseButtons.Left)
            {
                // 更新拖拽结束点
                dragEndPoint = GetImagePoint(e.Location);

                // 找到结束位置的cell和字符索引
                var endPos = GetCellAndCharAtPoint(dragEndPoint);
                endCell = endPos.Cell;
                endCharIndex = endPos.CharIndex;

                Invalidate();
            }
            else
            {
                // 检查鼠标是否在可选择区域，更新光标
                UpdateCursor(e.Location);
            }
        }

        private void OnMouseLeave(object sender, EventArgs e)
        {
            // 鼠标离开时恢复默认光标
            Cursor = Cursors.Default;
        }

        /// <summary>
        /// 获取指定点位置的cell和字符索引
        /// </summary>
        private CellCharPosition GetCellAndCharAtPoint(Point imagePoint)
        {
            // 首先尝试精确匹配cell
            foreach (var cell in textCells)
            {
                if (cell?.location != null && !string.IsNullOrEmpty(cell.words))
                {
                    var cellRect = cell.location.Rectangle;
                    if (cellRect.Contains(imagePoint))
                    {
                        // 计算在cell内的字符位置
                        int charIndex = GetCharIndexInCell(cell, imagePoint);
                        return new CellCharPosition(cell, charIndex);
                    }
                }
            }

            // 智能处理：优先找X轴包含的cell，然后找最近的cell
            TextCellInfo nearestCell = null;
            double minDistance = double.MaxValue;

            // 第一轮：找X轴包含点的cell
            foreach (var cell in textCells)
            {
                if (cell?.location != null && !string.IsNullOrEmpty(cell.words))
                {
                    var cellRect = cell.location.Rectangle;
                    if (imagePoint.X >= cellRect.Left && imagePoint.X <= cellRect.Right)
                    {
                        // X轴包含，计算Y轴距离
                        var centerY = cellRect.Y + cellRect.Height / 2;
                        var yDistance = Math.Abs(imagePoint.Y - centerY);

                        if (yDistance < minDistance)
                        {
                            minDistance = yDistance;
                            nearestCell = cell;
                        }
                    }
                }
            }

            // 如果没找到X轴包含的cell，使用原来的距离算法
            if (nearestCell == null)
            {
                foreach (var cell in textCells)
                {
                    if (cell?.location != null && !string.IsNullOrEmpty(cell.words))
                    {
                        var cellRect = cell.location.Rectangle;
                        var centerX = cellRect.X + cellRect.Width / 2;
                        var centerY = cellRect.Y + cellRect.Height / 2;
                        var distance = Math.Sqrt(Math.Pow(imagePoint.X - centerX, 2) + Math.Pow(imagePoint.Y - centerY, 2));

                        if (distance < minDistance)
                        {
                            minDistance = distance;
                            nearestCell = cell;
                        }
                    }
                }
            }

            if (nearestCell != null)
            {
                // 根据点击位置决定字符索引
                var cellRect = nearestCell.location.Rectangle;
                int charIndex;

                if (imagePoint.X < cellRect.Left)
                    charIndex = 0; // 点击在cell左侧，选择开头
                else if (imagePoint.X > cellRect.Right)
                    charIndex = nearestCell.words.Length - 1; // 点击在cell右侧，选择结尾
                else
                    charIndex = GetCharIndexInCell(nearestCell, imagePoint);

                return new CellCharPosition(nearestCell, charIndex);
            }

            return new CellCharPosition(null, -1);
        }

        /// <summary>
        /// 计算在cell内的字符索引
        /// </summary>
        private int GetCharIndexInCell(TextCellInfo cell, Point imagePoint)
        {
            var cellRect = cell.location.Rectangle;
            bool isCellVertical = DetectCellDirection(cell);

            int charIndex;
            if (isCellVertical)
            {
                // 竖排：从上到下计算
                var relativeY = imagePoint.Y - cellRect.Y;
                var charHeight = (float)cellRect.Height / cell.words.Length;
                charIndex = Math.Max(0, Math.Min((int)(relativeY / charHeight), cell.words.Length - 1));
            }
            else
            {
                // 横排：从左到右计算
                var relativeX = imagePoint.X - cellRect.X;
                var charWidth = (float)cellRect.Width / cell.words.Length;
                charIndex = Math.Max(0, Math.Min((int)(relativeX / charWidth), cell.words.Length - 1));
            }



            return charIndex;
        }

        /// <summary>
        /// 根据鼠标位置更新光标
        /// </summary>
        private void UpdateCursor(Point mouseLocation)
        {
            if (textCells.Count == 0)
            {
                Cursor = Cursors.Default;
                return;
            }

            // 转换鼠标坐标到图片坐标
            var imagePoint = GetImagePoint(mouseLocation);

            // 检查是否在任何文字区域内
            bool isOverText = textCells.Any(cell =>
                cell?.location != null &&
                !string.IsNullOrEmpty(cell.words) &&
                cell.location.Rectangle.Contains(imagePoint));

            // 如果鼠标在文字区域，显示文本光标
            Cursor = isOverText ? Cursors.IBeam : Cursors.Default;
        }

        private void OnMouseUp(object sender, MouseEventArgs e)
        {
            if (isDragging)
            {
                isDragging = false;

                // 输出选择结束时的关键信息
                System.Diagnostics.Debug.WriteLine($"=== 选择结束 ===");
                System.Diagnostics.Debug.WriteLine($"拖拽起点: {dragStartPoint}, 拖拽终点: {dragEndPoint}");
                System.Diagnostics.Debug.WriteLine($"startCell: {startCell?.words}, startCharIndex: {startCharIndex}");
                System.Diagnostics.Debug.WriteLine($"endCell: {endCell?.words}, endCharIndex: {endCharIndex}");

                // 检查起点字符索引计算
                if (startCell != null)
                {
                    var startCellRect = startCell.location.Rectangle;
                    var expectedStartCharIndex = GetCharIndexInCell(startCell, dragStartPoint);
                    System.Diagnostics.Debug.WriteLine($"起点字符索引检查: 拖拽起点={dragStartPoint}, cell矩形={startCellRect}");
                    System.Diagnostics.Debug.WriteLine($"  预期字符索引={expectedStartCharIndex}, 实际字符索引={startCharIndex}");
                }

                // 检查终点字符索引计算
                if (endCell != null)
                {
                    var endCellRect = endCell.location.Rectangle;
                    var expectedEndCharIndex = GetCharIndexInCell(endCell, dragEndPoint);
                    System.Diagnostics.Debug.WriteLine($"终点字符索引检查: 拖拽终点={dragEndPoint}, cell矩形={endCellRect}");
                    System.Diagnostics.Debug.WriteLine($"  预期字符索引={expectedEndCharIndex}, 实际字符索引={endCharIndex}");
                }

                // 如果没有有效选择，清除选择
                if (startCell == null || endCell == null ||
                    startCharIndex < 0 || endCharIndex < 0)
                {
                    ClearSelection();
                }

                // 触发选择变更事件
                OnTextSelectionChanged();

                Invalidate();
            }
        }

        private void OnCustomPaint(object sender, PaintEventArgs e)
        {
            if (!IsBindImageMode || textCells.Count == 0) return;

            var g = e.Graphics;

            // 绘制选中的文字区域
            if (isDragging || HasSelection())
            {
                using (var selectedBrush = new SolidBrush(SELECTION_FILL_COLOR))
                {
                    var selectedRects = GetSelectedCharacterRectangles();

                    foreach (var rect in selectedRects)
                    {
                        var displayRect = GetDisplayRectangle(rect);

                        if (!displayRect.IsEmpty)
                        {
                            g.FillRectangle(selectedBrush, displayRect);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 获取鼠标位置对应的图片坐标
        /// </summary>
        private Point GetImagePoint(Point controlPoint)
        {
            // 考虑滚动位置和缩放因子
            var scrollOffset = AutoScrollPosition;
            var imagePoint = new Point(
                (int)((controlPoint.X - scrollOffset.X) / ZoomFactor),
                (int)((controlPoint.Y - scrollOffset.Y) / ZoomFactor)
            );
            return imagePoint;
        }

        /// <summary>
        /// 获取矩形在控件中的显示矩形
        /// </summary>
        private Rectangle GetDisplayRectangle(Rectangle imageRect)
        {
            var scrollOffset = AutoScrollPosition;

            return new Rectangle(
                (int)(imageRect.X * ZoomFactor) + scrollOffset.X,
                (int)(imageRect.Y * ZoomFactor) + scrollOffset.Y,
                (int)(imageRect.Width * ZoomFactor),
                (int)(imageRect.Height * ZoomFactor)
            );
        }

        /// <summary>
        /// 检查是否有选择
        /// </summary>
        private bool HasSelection()
        {
            return startCell != null && endCell != null &&
                   startCharIndex >= 0 && endCharIndex >= 0;
        }

        /// <summary>
        /// 获取选中字符的矩形区域
        /// </summary>
        private List<Rectangle> GetSelectedCharacterRectangles()
        {
            var rects = new List<Rectangle>();

            // 如果正在拖拽或有有效选择，使用三部分选择逻辑
            if (isDragging || HasSelection())
            {
                var selectedCells = GetThreePartSelection(); // 使用新的三部分选择逻辑

                foreach (var cellInfo in selectedCells)
                {
                    var cell = cellInfo.Cell;
                    var startChar = cellInfo.StartChar;
                    var endChar = cellInfo.EndChar;

                    var rect = GetCharacterRectInCell(cell, startChar, endChar);
                    if (!rect.IsEmpty)
                        rects.Add(rect);
                }
            }

            return rects;
        }

        /// <summary>
        /// 获取cell内指定字符范围的矩形
        /// </summary>
        private Rectangle GetCharacterRectInCell(TextCellInfo cell, int startChar, int endChar)
        {
            if (cell?.location == null || string.IsNullOrEmpty(cell.words) ||
                startChar < 0 || endChar < 0 || startChar >= cell.words.Length)
                return Rectangle.Empty;

            var cellRect = cell.location.Rectangle;
            bool isCellVertical = DetectCellDirection(cell);

            // 确保索引有效
            startChar = Math.Max(0, Math.Min(startChar, cell.words.Length - 1));
            endChar = Math.Max(0, Math.Min(endChar, cell.words.Length - 1));

            if (startChar > endChar)
            {
                var temp = startChar;
                startChar = endChar;
                endChar = temp;
            }

            Rectangle result;
            if (isCellVertical)
            {
                // 竖排文字：按高度分割
                var charHeight = (float)cellRect.Height / cell.words.Length;
                result = new Rectangle(
                    cellRect.X,
                    (int)(cellRect.Y + startChar * charHeight),
                    cellRect.Width,
                    (int)Math.Ceiling((endChar - startChar + 1) * charHeight)
                );
            }
            else
            {
                // 横排文字：按宽度分割
                var charWidth = (float)cellRect.Width / cell.words.Length;
                result = new Rectangle(
                    (int)(cellRect.X + startChar * charWidth),
                    cellRect.Y,
                    (int)Math.Ceiling((endChar - startChar + 1) * charWidth),
                    cellRect.Height
                );
            }



            return result;
        }

        /// <summary>
        /// 拖动方向枚举
        /// </summary>
        private enum DragDirection
        {
            Up,
            Down,
            Left,
            Right
        }

        /// <summary>
        /// 判断拖动方向（上下左右四个方向，取最大位移方向）
        /// </summary>
        private DragDirection GetDragDirection(Point startPoint, Point endPoint)
        {
            var deltaX = endPoint.X - startPoint.X;
            var deltaY = endPoint.Y - startPoint.Y;

            var absDeltaX = Math.Abs(deltaX);
            var absDeltaY = Math.Abs(deltaY);

            if (absDeltaX > absDeltaY)
            {
                return deltaX > 0 ? DragDirection.Right : DragDirection.Left;
            }
            else
            {
                return deltaY > 0 ? DragDirection.Down : DragDirection.Up;
            }
        }

        /// <summary>
        /// 获取三部分组合的选择区域
        /// </summary>
        private List<CellSelectionInfo> GetThreePartSelection()
        {
            // 如果没有拖拽起点和终点，返回空列表
            if (dragStartPoint.IsEmpty || dragEndPoint.IsEmpty)
                return new List<CellSelectionInfo>();

            // 1. 计算初始矩形
            var initialRect = GetSelectionRect(dragStartPoint, dragEndPoint);

            // 2. 判断拖动方向
            var dragDirection = GetDragDirection(dragStartPoint, dragEndPoint);

            // 3. 获取部分1：初始矩形内的cell
            var part1Cells = GetPart1Cells(initialRect);

            // 4. 获取部分2：同方向cell的扩散
            var part2Cells = GetPart2Cells(part1Cells, dragDirection);

            // 5. 计算扩散区域的边界矩形（包含初始矩形）
            var expansionRect = CalculateExpansionRect(initialRect, part2Cells);

            // 6. 获取部分3：扩散矩形内的所有cell
            var part3Cells = GetPart3Cells(expansionRect);

            // 7. 合并三部分（去重）
            var allSelectedCells = new HashSet<TextCellInfo>();
            allSelectedCells.UnionWith(part1Cells);
            allSelectedCells.UnionWith(part2Cells);
            allSelectedCells.UnionWith(part3Cells);

            // 8. 计算字符选择范围
            return CalculateCharacterRanges(allSelectedCells.ToList());
        }

        /// <summary>
        /// 计算选择矩形
        /// </summary>
        private Rectangle GetSelectionRect(Point startPoint, Point endPoint)
        {
            // 确保矩形包含起点和终点
            int left = Math.Min(startPoint.X, endPoint.X);
            int top = Math.Min(startPoint.Y, endPoint.Y);
            int right = Math.Max(startPoint.X, endPoint.X);
            int bottom = Math.Max(startPoint.Y, endPoint.Y);

            // 宽度和高度至少为1，确保包含边界点
            int width = Math.Max(1, right - left + 1);
            int height = Math.Max(1, bottom - top + 1);

            return new Rectangle(left, top, width, height);
        }

        /// <summary>
        /// 获取部分1：初始矩形内的cell
        /// </summary>
        private List<TextCellInfo> GetPart1Cells(Rectangle initialRect)
        {
            return textCells.Where(cell =>
                cell?.location != null &&
                !string.IsNullOrEmpty(cell.words) &&
                initialRect.IntersectsWith(cell.location.Rectangle)
            ).ToList();
        }

        /// <summary>
        /// 获取部分2：同方向cell的扩散
        /// </summary>
        private List<TextCellInfo> GetPart2Cells(List<TextCellInfo> part1Cells, DragDirection dragDirection)
        {
            var part2Cells = new HashSet<TextCellInfo>();

            foreach (var cell in part1Cells)
            {
                bool isCellVertical = DetectCellDirection(cell);

                switch (dragDirection)
                {
                    case DragDirection.Up:
                    case DragDirection.Down:
                        // 上下拖动 → 只有横排cell扩散
                        if (!isCellVertical)
                        {
                            var rowCells = GetCellsInSameRow(cell);
                            foreach (var rowCell in rowCells)
                                part2Cells.Add(rowCell);
                        }
                        break;

                    case DragDirection.Left:
                    case DragDirection.Right:
                        // 左右拖动 → 只有竖排cell扩散
                        if (isCellVertical)
                        {
                            var columnCells = GetCellsInSameColumn(cell);
                            foreach (var columnCell in columnCells)
                                part2Cells.Add(columnCell);
                        }
                        break;
                }
            }

            return part2Cells.ToList();
        }

        /// <summary>
        /// 获取同行的cell（只考虑横排cell，垂直方向有交叉）
        /// </summary>
        private List<TextCellInfo> GetCellsInSameRow(TextCellInfo targetCell)
        {
            // 只考虑横排cell的同行
            if (DetectCellDirection(targetCell)) return new List<TextCellInfo>(); // 竖排cell没有同行概念

            var targetRect = targetCell.location.Rectangle;

            return textCells.Where(cell =>
                cell?.location != null &&
                !string.IsNullOrEmpty(cell.words) &&
                !DetectCellDirection(cell) && // 必须也是横排
                HasVerticalOverlap(targetRect, cell.location.Rectangle) // 垂直方向有交叉
            ).ToList();
        }

        /// <summary>
        /// 获取同列的cell（只考虑竖排cell，水平方向有交叉）
        /// </summary>
        private List<TextCellInfo> GetCellsInSameColumn(TextCellInfo targetCell)
        {
            // 只考虑竖排cell的同列
            if (!DetectCellDirection(targetCell)) return new List<TextCellInfo>(); // 横排cell没有同列概念

            var targetRect = targetCell.location.Rectangle;

            return textCells.Where(cell =>
                cell?.location != null &&
                !string.IsNullOrEmpty(cell.words) &&
                DetectCellDirection(cell) && // 必须也是竖排
                HasHorizontalOverlap(targetRect, cell.location.Rectangle) // 水平方向有交叉
            ).ToList();
        }

        /// <summary>
        /// 判断两个矩形是否在垂直方向有交叉
        /// </summary>
        private bool HasVerticalOverlap(Rectangle rect1, Rectangle rect2)
        {
            return !(rect1.Bottom <= rect2.Top || rect2.Bottom <= rect1.Top);
        }

        /// <summary>
        /// 判断两个矩形是否在水平方向有交叉
        /// </summary>
        private bool HasHorizontalOverlap(Rectangle rect1, Rectangle rect2)
        {
            return !(rect1.Right <= rect2.Left || rect2.Right <= rect1.Left);
        }

        /// <summary>
        /// 计算扩散区域的边界矩形（包含初始矩形和扩散cell）
        /// </summary>
        private Rectangle CalculateExpansionRect(Rectangle initialRect, List<TextCellInfo> part2Cells)
        {
            if (part2Cells.Count == 0) return initialRect;

            // 计算part2Cells的边界
            var minLeft = part2Cells.Min(cell => cell.location.left);
            var minTop = part2Cells.Min(cell => cell.location.top);
            var maxRight = part2Cells.Max(cell => cell.location.left + cell.location.width);
            var maxBottom = part2Cells.Max(cell => cell.location.top + cell.location.height);

            var part2Rect = new Rectangle(
                (int)minLeft,
                (int)minTop,
                (int)(maxRight - minLeft),
                (int)(maxBottom - minTop)
            );

            // 合并初始矩形和扩散矩形
            return Rectangle.Union(initialRect, part2Rect);
        }

        /// <summary>
        /// 获取部分3：扩散矩形内的所有cell
        /// </summary>
        private List<TextCellInfo> GetPart3Cells(Rectangle expansionRect)
        {
            if (expansionRect.IsEmpty) return new List<TextCellInfo>();

            return textCells.Where(cell =>
                cell?.location != null &&
                !string.IsNullOrEmpty(cell.words) &&
                expansionRect.IntersectsWith(cell.location.Rectangle)
            ).ToList();
        }

        /// <summary>
        /// 计算字符选择范围
        /// </summary>
        private List<CellSelectionInfo> CalculateCharacterRanges(List<TextCellInfo> selectedCells)
        {
            var result = new List<CellSelectionInfo>();

            foreach (var cell in selectedCells)
            {
                int cellStartChar, cellEndChar;

                if (cell == startCell && cell == endCell)
                {
                    // 同一个cell内选择：使用精确的起始和结束字符索引
                    cellStartChar = Math.Min(startCharIndex, endCharIndex);
                    cellEndChar = Math.Max(startCharIndex, endCharIndex);
                }
                else if (cell == startCell)
                {
                    // 起点cell：从起点字符到结尾
                    cellStartChar = startCharIndex;
                    cellEndChar = cell.words.Length - 1;
                }
                else if (cell == endCell)
                {
                    // 终点cell：从开头到终点字符
                    cellStartChar = 0;
                    cellEndChar = endCharIndex;
                }
                else
                {
                    // 中间的cell：全选
                    cellStartChar = 0;
                    cellEndChar = cell.words.Length - 1;
                }

                result.Add(new CellSelectionInfo(cell, cellStartChar, cellEndChar));
            }

            return result;
        }

        /// <summary>
        /// 获取选中的文字内容
        /// </summary>
        private string GetSelectedText()
        {
            if (!HasSelection()) return string.Empty;

            var selectedCells = GetThreePartSelection(); // 使用新的三部分选择逻辑
            var result = new StringBuilder();

            // 按空间位置排序
            var sortedCells = selectedCells
                .OrderBy(c => c.Cell.location.top)
                .ThenBy(c => c.Cell.location.left)
                .ToList();

            for (int i = 0; i < sortedCells.Count; i++)
            {
                var cellInfo = sortedCells[i];
                var cell = cellInfo.Cell;

                if (cellInfo.StartChar >= 0 && cellInfo.EndChar < cell.words.Length)
                {
                    result.Append(cell.words.Substring(cellInfo.StartChar, cellInfo.EndChar - cellInfo.StartChar + 1));
                }

                // 添加cell间分隔符
                if (i < sortedCells.Count - 1)
                {
                    var currentCell = cellInfo.Cell;
                    var nextCell = sortedCells[i + 1].Cell;

                    if (ShouldAddLineBreak(currentCell, nextCell))
                    {
                        result.AppendLine();
                    }
                    else if (ShouldAddSpace(currentCell, nextCell))
                    {
                        result.Append(" ");
                    }
                }
            }

            return result.ToString();
        }



        /// <summary>
        /// 判断是否应该添加换行符
        /// </summary>
        private bool ShouldAddLineBreak(TextCellInfo currentCell, TextCellInfo nextCell)
        {
            // 如果下一个cell的Y坐标明显大于当前cell，认为是换行
            var currentBottom = currentCell.location.top + currentCell.location.height;
            var nextTop = nextCell.location.top;
            var lineThreshold = Math.Max(10, currentCell.location.height * 0.5);

            return nextTop - currentBottom > lineThreshold;
        }

        /// <summary>
        /// 判断是否应该添加空格
        /// </summary>
        private bool ShouldAddSpace(TextCellInfo currentCell, TextCellInfo nextCell)
        {
            // 如果两个cell在同一行但有较大间距，添加空格
            var currentRight = currentCell.location.left + currentCell.location.width;
            var nextLeft = nextCell.location.left;
            var gap = nextLeft - currentRight;
            var avgCharWidth = currentCell.location.width / Math.Max(1, currentCell.words.Length);

            return gap > avgCharWidth * 2.0;
        }



        /// <summary>
        /// 触发文字选择变化事件
        /// </summary>
        private void OnTextSelectionChanged()
        {
            var selectedText = GetSelectedText();

            // 获取选中的cells（为了兼容现有接口）
            var selectedCells = new List<TextCellInfo>();
            if (HasSelection())
            {
                var cellSelections = GetThreePartSelection();
                selectedCells.AddRange(cellSelections.Select(c => c.Cell));
            }

            TextSelectionChanged?.Invoke(this, new TextSelectionEventArgs(selectedCells)
            {
                SelectedText = selectedText,
                SelectedCharacterCount = selectedText.Length
            });
        }

        /// <summary>
        /// 清除所有选择
        /// </summary>
        public void ClearSelection()
        {
            startCell = null;
            endCell = null;
            startCharIndex = -1;
            endCharIndex = -1;
            dragStartPoint = Point.Empty;
            dragEndPoint = Point.Empty;

            OnTextSelectionChanged();
            Invalidate();
        }

        /// <summary>
        /// 获取当前选中的文字
        /// </summary>
        public string GetSelectedTextContent()
        {
            return GetSelectedText();
        }
    }

    /// <summary>
    /// 文字选择事件参数
    /// </summary>
    public class TextSelectionEventArgs : EventArgs
    {
        public List<TextCellInfo> SelectedCells { get; private set; }
        public string SelectedText { get; set; }
        public int SelectedCharacterCount { get; set; }

        public TextSelectionEventArgs(List<TextCellInfo> selectedCells)
        {
            SelectedCells = selectedCells ?? new List<TextCellInfo>();
            SelectedText = string.Empty;
            SelectedCharacterCount = 0;
        }
    }
}
