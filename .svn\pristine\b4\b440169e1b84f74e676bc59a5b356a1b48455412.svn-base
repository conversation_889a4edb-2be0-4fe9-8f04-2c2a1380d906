﻿namespace OCRTools.UserControlEx
{
    using System;
    using System.Drawing;
    using System.Drawing.Imaging;

    public class FastBitmap : IDisposable, ICloneable
    {
        private Bitmap bmp;
        internal BitmapData bmpData;

        private FastBitmap()
        {
        }

        public FastBitmap(Bitmap bitmap)
        {
            this.bmp = bitmap;
        }

        public FastBitmap(int width, int height, PixelFormat format)
        {
            this.bmp = new Bitmap(width, height, format);
        }

        public object Clone()
        {
            return new FastBitmap { bmp = (Bitmap)this.bmp.Clone() };
        }

        public void Dispose()
        {
            GC.SuppressFinalize(this);
            this.Dispose(true);
        }

        protected virtual void Dispose(bool disposing)
        {
            this.Unlock();
            if (disposing)
            {
                this.bmp.Dispose();
            }
        }

        ~FastBitmap()
        {
            this.Dispose(false);
        }

        public unsafe Color GetPixel(int x, int y)
        {
            if (this.bmpData.PixelFormat == PixelFormat.Format32bppArgb)
            {
                byte* numPtr = (byte*)((((int)this.bmpData.Scan0) + (y * this.bmpData.Stride)) + (x * 4));
                if (numPtr != null) return Color.FromArgb(numPtr[3], numPtr[2], numPtr[1], numPtr[0]);
            }
            if (this.bmpData.PixelFormat == PixelFormat.Format24bppRgb)
            {
                byte* numPtr2 = (byte*)((((int)this.bmpData.Scan0) + (y * this.bmpData.Stride)) + (x * 3));
                if (numPtr2 != null) return Color.FromArgb(numPtr2[2], numPtr2[1], numPtr2[0]);
            }
            return Color.Empty;
        }

        public void Lock()
        {
            this.bmpData = this.bmp.LockBits(new Rectangle(0, 0, this.bmp.Width, this.bmp.Height), ImageLockMode.ReadWrite, this.bmp.PixelFormat);
        }

        public void Unlock()
        {
            if (this.bmpData != null)
            {
                this.bmp.UnlockBits(this.bmpData);
                this.bmpData = null;
            }
        }

        public Bitmap Bitmap
        {
            get
            {
                return this.bmp;
            }
            set
            {
                if (value != null)
                {
                    this.bmp = value;
                }
            }
        }

        public int Height
        {
            get
            {
                return this.bmp.Height;
            }
        }

        public int Width
        {
            get
            {
                return this.bmp.Width;
            }
        }
    }
}
