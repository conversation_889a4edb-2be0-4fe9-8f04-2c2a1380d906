using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Windows.Forms;

namespace OCRTools
{
    public class FmTip : Form
    {
        private readonly IContainer components = null;
        private Bitmap _bmp;

        private Graphics _g;

        public int fmheight;

        public int fmwidth;

        public FmTip(PictureBox pictureBox)
        {
            InitializeComponent();
            pictureBox.MouseLeave += toolStripButton_MouseLeave;
            pictureBox.MouseEnter += toolStripButton_MouseEnter;
        }

        protected override CreateParams CreateParams
        {
            get
            {
                var createParams = base.CreateParams;
                createParams.Style |= 131072;
                if (!DesignMode) createParams.ExStyle |= 524288;
                return createParams;
            }
        }

        public void toolStripButton_MouseLeave(object sender, EventArgs e)
        {
            Hide();
        }

        public void toolStripButton_MouseEnter(object sender, EventArgs e)
        {
            try
            {
                var toolStripButton = (PictureBox) sender;
                DrawStr(toolStripButton);
                Show();
            }
            catch (Exception)
            {
            }
        }

        public void SetBits(Bitmap bitmap)
        {
            if (!Image.IsCanonicalPixelFormat(bitmap.PixelFormat) || !Image.IsAlphaPixelFormat(bitmap.PixelFormat))
                throw new ApplicationException("图片必须是32位带Alhpa通道的图片。");
            var hObj = IntPtr.Zero;
            var dC = HelpWin32.GetDC(IntPtr.Zero);
            var intPtr = IntPtr.Zero;
            var intPtr2 = HelpWin32.CreateCompatibleDC(dC);
            try
            {
                var pptDst = new HelpWin32.Point(Left, Top);
                var psize = new HelpWin32.Size(bitmap.Width, bitmap.Height);
                var pblend = default(HelpWin32.BLENDFUNCTION);
                var pptSrc = new HelpWin32.Point(0, 0);
                intPtr = bitmap.GetHbitmap(Color.FromArgb(0));
                hObj = HelpWin32.SelectObject(intPtr2, intPtr);
                pblend.BlendOp = 0;
                pblend.SourceConstantAlpha = byte.MaxValue;
                pblend.AlphaFormat = 1;
                pblend.BlendFlags = 0;
                HelpWin32.UpdateLayeredWindow(Handle, dC, ref pptDst, ref psize, intPtr2, ref pptSrc, 0, ref pblend, 2);
            }
            finally
            {
                if (intPtr != IntPtr.Zero)
                {
                    HelpWin32.SelectObject(intPtr2, hObj);
                    HelpWin32.DeleteObject(intPtr);
                }

                HelpWin32.ReleaseDC(IntPtr.Zero, dC);
                HelpWin32.DeleteDC(intPtr2);
            }
        }

        public void DrawStr(PictureBox toolStripButton)
        {
            var text = toolStripButton.Tag.ToString();
            var size = toolStripButton.Bounds.Size;
            var point = toolStripButton.Parent.PointToScreen(toolStripButton.Bounds.Location);
            var stringFormat = new StringFormat
            {
                Alignment = StringAlignment.Center,
                LineAlignment = StringAlignment.Center
            };
            fmwidth = 18.DpiValue() * text.Length;
            fmheight = 20.DpiValue();
            Size = new Size(fmwidth, fmheight + fmheight / 3 * 2);
            _bmp = new Bitmap(fmwidth, fmheight + fmheight / 3 * 2);
            _g = Graphics.FromImage(_bmp);
            _g.InterpolationMode = InterpolationMode.Bilinear;
            _g.SmoothingMode = SmoothingMode.HighQuality;
            _g.TextRenderingHint = TextRenderingHint.AntiAliasGridFit;
            _g.Clear(Color.Transparent);
            var rect = new Rectangle(0, 0, fmwidth, fmheight);
            _g.FillRectangle(new SolidBrush(Color.FromArgb(255, Color.White)), rect);
            _g.DrawRectangle(new Pen(Color.Silver), 0, 0, rect.Width - 1, rect.Height);
            var r = new Rectangle(0, 2, fmwidth, fmheight);
            _g.DrawString(text, new Font("微软雅黑", 10f), new SolidBrush(Color.FromArgb(255, Color.Black)), r,
                stringFormat);
            Brush brush = new SolidBrush(Color.White);
            Location = new Point(point.X, point.Y);
            var p = new Point(point.X + size.Width / 2, point.Y);
            var point2 = PointToClient(p);
            var num = fmwidth / 2 - point2.X;
            Location = new Point(point.X - num, point.Y + toolStripButton.Parent.Height + 2);
            SetBits(_bmp);
            _g.Dispose();
            _bmp.Dispose();
            _g.Dispose();
            brush.Dispose();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null) components.Dispose();
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            SuspendLayout();
            AutoScaleMode = AutoScaleMode.None;
            ClientSize = new Size(284, 261);
            ControlBox = false;
            ForeColor = Color.Aqua;
            FormBorderStyle = FormBorderStyle.None;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "FmTip";
            ShowIcon = false;
            ShowInTaskbar = false;
            SizeGripStyle = SizeGripStyle.Hide;
            StartPosition = FormStartPosition.Manual;
            Text = "Tip";
            TopMost = true;
            ResumeLayout(false);
        }
    }
}