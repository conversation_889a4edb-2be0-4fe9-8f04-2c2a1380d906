﻿using OCRTools.Common;
using System;
using System.IO;
using System.Net;

namespace ImageLib
{
    public class SouGouImageUpload
    {
        public static bool Enable { get; set; } = true;

        public static string GetResult(byte[] content)
        {
            var result = GetFromSouGou(content);
            if (!string.IsNullOrEmpty(result))
            {
                result += "?1.png";
            }
            return result;
        }

        private static string GetFromSouGou(byte[] content)
        {
            var result = "";
            try
            {
                var url = "http://pic.sogou.com/pic/upload_pic.jsp";
                var file = new UploadFileInfo()
                {
                    Name = "pic_path",
                    Filename = "test.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                try
                {
                    result = UploadFileRequest.Post(url, new[] { file }, null);
                }
                catch (BadApiException exception)
                {
                    switch (exception.Code)
                    {
                        case HttpStatusCode.MethodNotAllowed: //405
                        case HttpStatusCode.Unauthorized://401
                        case HttpStatusCode.NotFound: //404
                            Enable = false;
                            break;
                    }
                }
                catch { }
            }
            catch (Exception oe)
            {

            }
            return result;
        }

    }
}
