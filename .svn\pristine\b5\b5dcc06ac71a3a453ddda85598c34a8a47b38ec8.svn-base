﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Automation;

namespace OCRTools.Common
{
    internal class CommonAutomation
    {
        internal static List<WindowInfo> InitChildHandle(WindowInfo window, TreeScope scope)
        {
            var lstWindows = new List<WindowInfo>();
            try
            {
                //var stopwatch = Stopwatch.StartNew();
                var element = AutomationElement.FromHandle(window.Handle).FindAll(scope,
                    new PropertyCondition(AutomationElement.IsOffscreenProperty, false));
                if (element != null)
                    foreach (AutomationElement item in element)
                    {
                        var rect = new Rectangle((int)item.Current.BoundingRectangle.X,
                            (int)item.Current.BoundingRectangle.Y
                            , (int)item.Current.BoundingRectangle.Width, (int)item.Current.BoundingRectangle.Height);
                        if (rect.IsValid() && !lstWindows.Exists(q => q.Rectangle.Equals(rect)))
                            lstWindows.Add(new WindowInfo
                            {
                                IsSmallControl = true,
                                ParentHandle = window.Handle,
                                Handle = new IntPtr(item.Current.NativeWindowHandle),
                                Rectangle = rect,
                                ZIndex = window.ZIndex
                            });
                    }

                //Console.WriteLine(zIndex + "|" + (isContainThirdChild ? "三" : "二") + "级:handle:" + GetText() +
                //                  GetClassName() + handle + ",检测耗时：" + stopwatch.ElapsedMilliseconds.ToString("F0") +
                //                  "ms,结果：" + lstWindows.Count);
            }
            catch
            {
            }

            return lstWindows;
        }

        //internal static WindowInfo GetElementFromPoint(System.Windows.Point window)
        //{
        //    WindowInfo windowInfo = null;
        //    try
        //    {
        //        //var stopwatch = Stopwatch.StartNew();
        //        var item = AutomationElement.FromPoint(window);
        //        if (item != null)
        //        {
        //            var rect = new Rectangle((int)item.Current.BoundingRectangle.X,
        //                (int)item.Current.BoundingRectangle.Y
        //                , (int)item.Current.BoundingRectangle.Width, (int)item.Current.BoundingRectangle.Height);
        //            if (rect.IsValid())
        //                windowInfo = new WindowInfo
        //                {
        //                    IsSmallControl = true,
        //                    Handle = new IntPtr(item.Current.NativeWindowHandle),
        //                    Rectangle = rect,
        //                };
        //        }
        //    }
        //    catch
        //    {
        //    }

        //    return windowInfo;
        //}
    }
}
