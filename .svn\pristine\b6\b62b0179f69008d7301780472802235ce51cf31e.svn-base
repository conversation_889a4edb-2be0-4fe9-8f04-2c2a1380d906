﻿using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools
{
    internal class CommonMsg
    {
        private static int _nowMsgid = -1;

        private static List<ScrollEntity> _lstMsg = new List<ScrollEntity>();

        private static ScrollingText _srcTxt;

        private static bool _isLoopAllOnce;

        private static int _nLoopTimes;

        public static void Refresh()
        {
            if (_srcTxt == null || _srcTxt.Parent == null) return;
            if (_srcTxt.Parent is FrmMain) _srcTxt.BackColor = CommonSetting.默认背景颜色;
        }

        public static void ShowToWindow(Form owner, Point location, bool isOnce = false)
        {
            if (_srcTxt == null) Init();
            _isLoopAllOnce = isOnce;
            if (_nLoopTimes >= 1 && _isLoopAllOnce) return;
            _srcTxt.Parent = owner;
            _srcTxt.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            _srcTxt.Location = location;
            if (owner is FrmMain)
            {
                _srcTxt.BackColor = CommonSetting.默认背景颜色;
                _srcTxt.Size = new Size(owner.Width - location.X * 2, 40);
            }
            else
            {
                _srcTxt.BackColor = owner.BackColor;
                _srcTxt.Size = new Size(owner.Width - location.X, 40);
            }

            _srcTxt.Visible = true;
            _srcTxt.BringToFront();
        }

        public static void Hide(Form owner)
        {
            if (_srcTxt == null) return;
            _srcTxt.Parent = null;
        }

        private static void Init()
        {
            _srcTxt = new ScrollingText
            {
                ScrollText = "正在初始化…",
                VerticleTextPosition = VerticleTextPosition.Center,
                TextScrollSpeed = 60,
                TextScrollDistance = 2,
                MaxLoopTimes = 3,
                StopScrollOnMouseOver = false,
                ShowBorder = false,
                ScrollDirection = ScrollDirection.RightToLeft,
                Font = new Font("微软雅黑", 15F, FontStyle.Bold)
            };
            Task.Factory.StartNew(ProcessMsg);
        }

        private static void ProcessMsg()
        {
            while (!CommonString.IsExit)
            {
                try
                {
                    if (_lstMsg == null || _lstMsg.Count <= 0) _lstMsg = GetLstMsg();
                    if (_lstMsg != null && _lstMsg.Count > 0)
                    {
                        _nowMsgid++;
                        if (_nowMsgid >= _lstMsg.Count)
                        {
                            _nLoopTimes++;
                            if (_nLoopTimes == 1 && _isLoopAllOnce) _srcTxt.Hide();
                        }

                        _nowMsgid = _nowMsgid <= 0 ? 0 : _nowMsgid;
                        _nowMsgid = _nowMsgid >= _lstMsg.Count ? 0 : _nowMsgid;
                        _srcTxt.ForeColor = _lstMsg[_nowMsgid].ForeColor;
                        _srcTxt.StrLink = _lstMsg[_nowMsgid].LnkUrl;
                        _srcTxt.ScrollText =
                            _lstMsg[_nowMsgid].Text + (string.IsNullOrEmpty(_srcTxt.StrLink) ? "" : "(链接)");
                        _srcTxt.StopScrollOnMouseOver = !string.IsNullOrEmpty(_srcTxt.StrLink);
                    }
                }
                catch
                {
                    _nowMsgid = -1;
                }

                var maxSecond =
                    _nowMsgid >= 0 && _lstMsg != null && _lstMsg.Count > 0 && _lstMsg[_nowMsgid].Text.Length >= 50
                        ? 120
                        : 60;
                var nowSecond = 0;
                while (!CommonString.IsExit)
                {
                    Thread.Sleep(1000);
                    nowSecond++;
                    if (nowSecond >= maxSecond || _srcTxt.LoopTimes >= _srcTxt.MaxLoopTimes) break;
                }
            }
        }

        private static List<ScrollEntity> GetLstMsg()
        {
            var list = new List<ScrollEntity>();
            try
            {
                var responseText =
                    WebClientExt.GetHtml(CommonString.StrUpdateUrl + "update/msg.txt?t=" + DateTime.Now.Ticks, 5);
                if (!string.IsNullOrEmpty(responseText))
                {
                    var array = responseText.Split(new[] { "\n" }, StringSplitOptions.RemoveEmptyEntries);
                    if (array.Length > 0)
                        foreach (var str in array)
                        {
                            var ss = str.Trim().Split(new[] { "|" }, StringSplitOptions.RemoveEmptyEntries);
                            if (ss.Length > 0)
                            {
                                var entity = new ScrollEntity
                                {
                                    Text = ss[0]
                                };
                                try
                                {
                                    entity.ForeColor = Color.FromName(ss.Length > 1 ? ss[1] : "black");
                                }
                                catch
                                {
                                    entity.ForeColor = Color.Black;
                                }

                                entity.LnkUrl = ss.Length > 2 ? ss[2] : "";
                                list.Add(entity);
                            }
                        }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }

            var strHoliday = ChinaDate.GetChinaDate(ServerTime.DateTime);
            if (!string.IsNullOrEmpty(strHoliday))
                list.Insert(0, new ScrollEntity
                {
                    ForeColor = Color.Blue,
                    Text = string.Format("今天是{0:MM月dd日}{1}，祝您生活愉快！", ServerTime.DateTime, strHoliday)
                });

            return list;
        }
    }
}