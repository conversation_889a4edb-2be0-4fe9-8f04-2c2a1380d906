﻿using OCRTools.Common;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class NotificationForm : Form
    {
        private readonly int fadeInterval = 50;
        private readonly float opacityDecrement;
        private readonly int titleSpace = 3;
        private readonly int urlPadding = 3;
        private bool isDurationEnd;

        private bool isMouseInside;
        private Size textRenderSize;
        private Size titleRenderSize;

        private NotificationForm(NotificationFormConfig config)
        {
            InitializeComponent();
            SetStyle(ControlStyles.OptimizedDoubleBuffer | ControlStyles.UserPaint | ControlStyles.AllPaintingInWmPaint,
                true);

            Config = config;
            opacityDecrement = (float) fadeInterval / Config.FadeDuration;

            if (Config.Image != null)
            {
                Config.Image = ImageProcessHelper.ResizeImageLimit(Config.Image, Config.Size);
                Config.Size = new Size(Config.Image.Width + 2, Config.Image.Height + 2);
            }
            else if (!string.IsNullOrEmpty(Config.Text))
            {
                var size = Config.Size.Offset(-Config.TextPadding * 2);
                textRenderSize = TextRenderer.MeasureText(Config.Text, Config.TextFont, size,
                    TextFormatFlags.WordBreak | TextFormatFlags.TextBoxControl | TextFormatFlags.EndEllipsis);
                textRenderSize = new Size(textRenderSize.Width, Math.Min(textRenderSize.Height, size.Height));

                Config.Size = new Size(textRenderSize.Width + Config.TextPadding * 2,
                    textRenderSize.Height + Config.TextPadding * 2 + 2);
            }

            if (!string.IsNullOrEmpty(Config.Title))
            {
                titleRenderSize = TextRenderer.MeasureText(Config.Title, Config.TitleFont,
                    Config.Size.Offset(-Config.TextPadding * 2),
                    TextFormatFlags.Left | TextFormatFlags.EndEllipsis);

                Config.Size = new Size(Math.Max(Config.Size.Width, titleRenderSize.Width + Config.TextPadding * 2)
                    , titleRenderSize.Height + titleSpace + Config.Size.Height + Config.TextPadding * 2 + 2);
            }

            var position = GetPosition(Config.Placement, Config.Offset, Screen.PrimaryScreen.WorkingArea.Size,
                Config.Size);

            NativeMethods.SetWindowPos(Handle, (IntPtr) SpecialWindowHandles.HWND_TOPMOST,
                position.X + Screen.PrimaryScreen.WorkingArea.X,
                position.Y + Screen.PrimaryScreen.WorkingArea.Y, Config.Size.Width, Config.Size.Height,
                SetWindowPosFlags.SWP_NOACTIVATE);

            if (Config.Duration <= 0)
            {
                DurationEnd();
            }
            else
            {
                tDuration.Interval = Config.Duration;
                tDuration.Start();
            }
        }

        public NotificationFormConfig Config { get; }

        protected override CreateParams CreateParams
        {
            get
            {
                var createParams = base.CreateParams;
                createParams.ExStyle |= 0x00000080;
                return createParams;
            }
        }

        public static Point GetPosition(ContentAlignment placement, int offset, Size backgroundSize, Size objectSize)
        {
            return GetPosition(placement, new Point(offset, offset), backgroundSize, objectSize);
        }

        public static Point GetPosition(ContentAlignment placement, Point offset, Size backgroundSize, Size objectSize)
        {
            var midX = (int) Math.Round(backgroundSize.Width / 2f - objectSize.Width / 2f);
            var midY = (int) Math.Round(backgroundSize.Height / 2f - objectSize.Height / 2f);
            var right = backgroundSize.Width - objectSize.Width;
            var bottom = backgroundSize.Height - objectSize.Height;

            switch (placement)
            {
                default:
                case ContentAlignment.TopLeft:
                    return new Point(offset.X, offset.Y);
                case ContentAlignment.TopCenter:
                    return new Point(midX, offset.Y);
                case ContentAlignment.TopRight:
                    return new Point(right - offset.X, offset.Y);
                case ContentAlignment.MiddleLeft:
                    return new Point(offset.X, midY);
                case ContentAlignment.MiddleCenter:
                    return new Point(midX, midY);
                case ContentAlignment.MiddleRight:
                    return new Point(right - offset.X, midY);
                case ContentAlignment.BottomLeft:
                    return new Point(offset.X, bottom - offset.Y);
                case ContentAlignment.BottomCenter:
                    return new Point(midX, bottom - offset.Y);
                case ContentAlignment.BottomRight:
                    return new Point(right - offset.X, bottom - offset.Y);
            }
        }

        public static void Show(NotificationFormConfig config)
        {
            if (config.Image == null && !string.IsNullOrEmpty(config.FilePath))
                config.Image = ImageProcessHelper.LoadImage(config.FilePath);

            if (config.Image != null || !string.IsNullOrEmpty(config.Text))
            {
                var form = new NotificationForm(config);
                NativeMethods.ShowWindow(form.Handle, 8);
            }
        }

        private void tDuration_Tick(object sender, EventArgs e)
        {
            DurationEnd();
        }

        private void DurationEnd()
        {
            isDurationEnd = true;
            tDuration.Stop();

            if (!isMouseInside) StartClosing();
        }

        private void StartClosing()
        {
            if (Config.FadeDuration <= 0)
            {
                Close();
            }
            else
            {
                Opacity = 1;
                tOpacity.Interval = fadeInterval;
                tOpacity.Start();
            }
        }

        private void tOpacity_Tick(object sender, EventArgs e)
        {
            if (Opacity > opacityDecrement)
                Opacity -= opacityDecrement;
            else
                Close();
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            var g = e.Graphics;
            g.Clear(Config.BackgroundColor);

            var rect = ClientRectangle;
            var titleRect = Rectangle.Empty;
            if (!string.IsNullOrEmpty(Config.Title))
            {
                titleRect = new Rectangle(Config.TextPadding, Config.TextPadding, titleRenderSize.Width + 2,
                    titleRenderSize.Height + 2);
                TextRenderer.DrawText(g, Config.Title, Config.TitleFont, titleRect, Config.TitleColor,
                    TextFormatFlags.Left | TextFormatFlags.EndEllipsis);
            }

            if (Config.Image != null)
            {
                var imgRect = new Rectangle(1,
                    (titleRect.IsEmpty ? 0 : titleRect.Y + titleRect.Height + titleSpace) + 1, Config.Image.Width,
                    Config.Image.Height);
                g.DrawImage(Config.Image, imgRect);

                if (isMouseInside && !string.IsNullOrEmpty(Config.URL))
                {
                    Rectangle textRect;
                    if (!titleRect.IsEmpty)
                        textRect = new Rectangle(1, titleRect.Height + titleSpace + 1, rect.Width, 40);
                    else
                        textRect = new Rectangle(1, 1, rect.Width, 40);

                    using (var brush = new SolidBrush(Color.FromArgb(100, 0, 0, 0)))
                    {
                        g.FillRectangle(brush, textRect);
                    }

                    TextRenderer.DrawText(g, Config.URL, Config.TextFont, textRect.Offset(-urlPadding), Color.White,
                        TextFormatFlags.Left | TextFormatFlags.EndEllipsis);
                }
            }
            else if (!string.IsNullOrEmpty(Config.Text))
            {
                var textRect = new Rectangle(Config.TextPadding,
                    (titleRect.IsEmpty ? 0 : titleRect.Y + titleRect.Height + titleSpace) + Config.TextPadding,
                    textRenderSize.Width + 2, textRenderSize.Height + 2);

                TextRenderer.DrawText(g, Config.Text, Config.TextFont, textRect, Config.TextColor,
                    TextFormatFlags.WordBreak | TextFormatFlags.TextBoxControl | TextFormatFlags.EndEllipsis);
            }

            using (var borderPen = new Pen(Config.BorderColor))
            {
                g.DrawRectangleProper(borderPen, rect);
            }
        }

        private void NotificationForm_MouseClick(object sender, MouseEventArgs e)
        {
            tDuration.Stop();

            Close();

            var action = ToastClickAction.CloseNotification;

            if (e.Button == MouseButtons.Left)
                action = Config.LeftClickAction;
            else if (e.Button == MouseButtons.Right)
                action = Config.RightClickAction;
            else if (e.Button == MouseButtons.Middle) action = Config.MiddleClickAction;

            ExecuteAction(action);
        }

        private void ExecuteAction(ToastClickAction action)
        {
            switch (action)
            {
                case ToastClickAction.ViewImage:
                    this.ViewImage(Config.Image);
                    break;
                case ToastClickAction.CopyImageToClipboard:
                    ClipboardService.CopyImageFromFile(Config.FilePath);
                    break;
                case ToastClickAction.CopyUrl:
                    ClipboardService.SetText(Config.URL);
                    break;
                case ToastClickAction.OpenUrl:
                    CommonMethod.OpenURL(Config.URL);
                    break;
                case ToastClickAction.OpenFile:
                    CommonMethod.OpenFile(Config.FilePath);
                    break;
                case ToastClickAction.OpenFolder:
                    CommonMethod.OpenFolderWithFile(Config.FilePath);
                    break;
                case ToastClickAction.OpenForm:
                    CommonMethod.OpenForm(Config.Data);
                    break;
            }
        }

        private void NotificationForm_MouseEnter(object sender, EventArgs e)
        {
            isMouseInside = true;
            tOpacity.Stop();

            if (!IsDisposed)
            {
                Refresh();
                Opacity = 1;
            }
        }

        private void NotificationForm_MouseLeave(object sender, EventArgs e)
        {
            isMouseInside = false;
            Refresh();

            if (isDurationEnd) StartClosing();
        }

        #region Windows Form Designer generated code

        private Timer tDuration;
        private Timer tOpacity;

        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }

            if (Config != null)
            {
                Config.Dispose();
            }

            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            tDuration = new Timer(components);
            tOpacity = new Timer(components);
            SuspendLayout();
            //
            // tDuration
            //
            tDuration.Tick += new EventHandler(tDuration_Tick);
            //
            // tOpacity
            //
            tOpacity.Tick += new EventHandler(tOpacity_Tick);
            //
            // NotificationForm
            //
            AutoScaleDimensions = new SizeF(96F, 96F);
            AutoScaleMode = AutoScaleMode.Dpi;
            ClientSize = new Size(400, 300);
            Cursor = Cursors.Hand;
            FormBorderStyle = FormBorderStyle.None;
            Name = "NotificationForm";
            ShowInTaskbar = false;
            StartPosition = FormStartPosition.Manual;
            Text = "NotificationForm";
            MouseClick += new MouseEventHandler(NotificationForm_MouseClick);
            MouseEnter += new EventHandler(NotificationForm_MouseEnter);
            MouseLeave += new EventHandler(NotificationForm_MouseLeave);
            ResumeLayout(false);
        }

        #endregion Windows Form Designer generated code
    }

    public class NotificationFormConfig : IDisposable
    {
        public int Duration { get; set; } = 3000;
        public int FadeDuration { get; set; }
        public ContentAlignment Placement { get; set; }
        public int Offset { get; set; } = 5;
        public Size Size { get; set; }
        public bool IsValid => (Duration > 0 || FadeDuration > 0) && Size.Width > 0 && Size.Height > 0;
        public Color BackgroundColor { get; set; } = Color.FromArgb(50, 50, 50);
        public Color BorderColor { get; set; } = Color.FromArgb(40, 40, 40);
        public int TextPadding { get; set; } = 10;
        public Font TextFont { get; set; } = new Font("Arial", 11);
        public Color TextColor { get; set; } = Color.FromArgb(210, 210, 210);
        public Font TitleFont { get; set; } = new Font("Arial", 11, FontStyle.Bold);
        public Color TitleColor { get; set; } = Color.FromArgb(240, 240, 240);

        public Bitmap Image { get; set; }
        public string Title { get; set; }
        public string Text { get; set; }
        public string FilePath { get; set; }
        public string URL { get; set; }
        public string Data { get; set; }
        public ToastClickAction LeftClickAction { get; set; }
        public ToastClickAction RightClickAction { get; set; }
        public ToastClickAction MiddleClickAction { get; set; }

        public void Dispose()
        {
            if (TextFont != null) TextFont.Dispose();

            if (TitleFont != null) TitleFont.Dispose();

            if (Image != null) Image.Dispose();
        }
    }

    /// <summary>
    ///     Special window handles
    /// </summary>
    public enum SpecialWindowHandles
    {
        /// <summary>
        ///     Places the window at the bottom of the Z order. If the hWnd parameter identifies a topmost window, the window loses
        ///     its topmost status and is placed at the bottom of all other windows.
        /// </summary>
        HWND_TOP = 0,

        /// <summary>
        ///     Places the window above all non-topmost windows (that is, behind all topmost windows). This flag has no effect if
        ///     the window is already a non-topmost window.
        /// </summary>
        HWND_BOTTOM = 1,

        /// <summary>
        ///     Places the window at the top of the Z order.
        /// </summary>
        HWND_TOPMOST = -1,

        /// <summary>
        ///     Places the window above all non-topmost windows. The window maintains its topmost position even when it is
        ///     deactivated.
        /// </summary>
        HWND_NOTOPMOST = -2
    }

    [DefaultValue(None)]
    public enum ToastClickAction
    {
        None,
        [Description("Close notification")] CloseNotification,
        [Description("View image")] ViewImage,

        [Description("Copy image to clipboard")]
        CopyImageToClipboard,
        [Description("Copy URL")] CopyUrl,
        [Description("Open file")] OpenFile,
        [Description("Open folder")] OpenFolder,
        [Description("Open URL")] OpenUrl,
        [Description("Open Form")] OpenForm
    }

    public enum BalloonTipClickAction
    {
        None,
        OpenURL,
        OpenForm
    }

    internal class BalloonTipAction
    {
        public BalloonTipClickAction ClickAction { get; set; }
        public string Text { get; set; }
    }

    [Flags]
    public enum SetWindowPosFlags : uint
    {
        /// <summary>
        ///     If the calling thread and the thread that owns the window are attached to different input queues, the system posts
        ///     the request to the thread that owns the window. This prevents the calling thread from blocking its execution while
        ///     other threads process the request.
        /// </summary>
        SWP_ASYNCWINDOWPOS = 0x4000,

        /// <summary>
        ///     Prevents generation of the WM_SYNCPAINT message.
        /// </summary>
        SWP_DEFERERASE = 0x2000,

        /// <summary>
        ///     Draws a frame (defined in the window's class description) around the window.
        /// </summary>
        SWP_DRAWFRAME = 0x0020,

        /// <summary>
        ///     Applies new frame styles set using the SetWindowLong function. Sends a WM_NCCALCSIZE message to the window, even if
        ///     the window's size is not being changed. If this flag is not specified, WM_NCCALCSIZE is sent only when the window's
        ///     size is being changed.
        /// </summary>
        SWP_FRAMECHANGED = 0x0020,

        /// <summary>
        ///     Hides the window.
        /// </summary>
        SWP_HIDEWINDOW = 0x0080,

        /// <summary>
        ///     Does not activate the window. If this flag is not set, the window is activated and moved to the top of either the
        ///     topmost or non-topmost group (depending on the setting of the hWndInsertAfter parameter).
        /// </summary>
        SWP_NOACTIVATE = 0x0010,

        /// <summary>
        ///     Discards the entire contents of the client area. If this flag is not specified, the valid contents of the client
        ///     area are saved and copied back into the client area after the window is sized or repositioned.
        /// </summary>
        SWP_NOCOPYBITS = 0x0100,

        /// <summary>
        ///     Retains the current position (ignores X and Y parameters).
        /// </summary>
        SWP_NOMOVE = 0x0002,

        /// <summary>
        ///     Does not change the owner window's position in the Z order.
        /// </summary>
        SWP_NOOWNERZORDER = 0x0200,

        /// <summary>
        ///     Does not redraw changes. If this flag is set, no repainting of any kind occurs. This applies to the client area,
        ///     the nonclient area (including the title bar and scroll bars), and any part of the parent window uncovered as a
        ///     result of the window being moved. When this flag is set, the application must explicitly invalidate or redraw any
        ///     parts of the window and parent window that need redrawing.
        /// </summary>
        SWP_NOREDRAW = 0x0008,

        /// <summary>
        ///     Same as the SWP_NOOWNERZORDER flag.
        /// </summary>
        SWP_NOREPOSITION = 0x0200,

        /// <summary>
        ///     Prevents the window from receiving the WM_WINDOWPOSCHANGING message.
        /// </summary>
        SWP_NOSENDCHANGING = 0x0400,

        /// <summary>
        ///     Retains the current size (ignores the cx and cy parameters).
        /// </summary>
        SWP_NOSIZE = 0x0001,

        /// <summary>
        ///     Retains the current Z order (ignores the hWndInsertAfter parameter).
        /// </summary>
        SWP_NOZORDER = 0x0004,

        /// <summary>
        ///     Displays the window.
        /// </summary>
        SWP_SHOWWINDOW = 0x0040
    }

    public class DefaultToastSetting
    {
        private float toastWindowDuration = 3;

        private float toastWindowFadeDuration = 1;

        private Size toastWindowSize = new Size(400, 300);

        [Category("Notifications")]
        [DefaultValue(3f)]
        [Description("Specify how long should toast notification window will stay on screen (in seconds).")]
        public float ToastWindowDuration
        {
            get => toastWindowDuration;
            set => toastWindowDuration = value.Clamp(0, 30);
        }

        [Category("Notifications")]
        [DefaultValue(1f)]
        [Description(
            "After toast window duration end, toast window will start fading, specify duration of this fade animation (in seconds).")]
        public float ToastWindowFadeDuration
        {
            get => toastWindowFadeDuration;
            set => toastWindowFadeDuration = value.Clamp(0, 30);
        }

        [Category("Notifications")]
        [DefaultValue(ContentAlignment.BottomRight)]
        [Description("Specify where should toast notification window appear on the screen.")]
        public ContentAlignment ToastWindowPlacement { get; set; } = ContentAlignment.BottomRight;

        [Category("Notifications")]
        [DefaultValue(typeof(Size), "400, 300")]
        [Description("Maximum toast notification window size.")]
        public Size ToastWindowSize
        {
            get => toastWindowSize;
            set => toastWindowSize = new Size(Math.Max(value.Width, 100), Math.Max(value.Height, 100));
        }
    }
}