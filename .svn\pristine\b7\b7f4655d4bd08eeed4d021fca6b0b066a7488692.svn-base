using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Runtime.InteropServices;
using static OCRTools.Common.EnhancedSmartArea;

namespace OCRTools.Common
{
    /// <summary>
    /// 覆盖层窗体 - 用于在目标窗体上进行控件捕获和区域选择
    /// </summary>
    public partial class OverlayForm : Form
    {
        #region Win32 API

        [DllImport("user32.dll")]
        private static extern IntPtr WindowFromPoint(Point point);

        [DllImport("user32.dll")]
        private static extern int GetWindowText(IntPtr hWnd, StringBuilder text, int count);

        [DllImport("user32.dll")]
        private static extern int GetClassName(IntPtr hWnd, StringBuilder text, int count);

        [DllImport("user32.dll")]
        private static extern IntPtr GetParent(IntPtr hWnd);

        [DllImport("user32.dll", SetLastError = true)]
        private static extern bool GetWindowRect(IntPtr hwnd, out RECT lpRect);

        [DllImport("user32.dll")]
        private static extern bool ScreenToClient(IntPtr hWnd, ref Point lpPoint);

        [StructLayout(LayoutKind.Sequential)]
        public struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }

        #endregion

        #region 字段和属性

        // 设计器窗体引用
        private GuideDesigner _designer;
        // 目标窗体
        private Form _targetForm;
        // 当前操作模式
        private GuideDesigner.DesignMode _mode = GuideDesigner.DesignMode.None;
        // 当前步骤
        private GuideItem _currentStep;
        // 开始点
        private Point _startPoint;
        // 结束点
        private Point _endPoint;
        // 是否正在拖动选择
        private bool _isDragging = false;
        // 捕获的控件
        private Control _capturedControl;
        // 高亮矩形
        private Rectangle _highlightRect = Rectangle.Empty;
        // 增强型智能区域
        private EnhancedSmartArea _enhancedArea;

        #region 相对坐标定位相关变量
        // 是否使用相对坐标
        private bool _useRelativeCoordinates = true;
        // 边缘感知阈值（窗体尺寸的5%）
        private float _edgeThreshold = 0.05f;
        // 邻近控件列表
        private List<Control> _nearbyControls = new List<Control>();
        // 内容结构信息
        private Dictionary<string, float> _contentStructureInfo = new Dictionary<string, float>();
        // 区域和边缘的关系信息
        private Dictionary<string, float> _edgeRelationships = new Dictionary<string, float>();
        #endregion

        // 增强型交互模式
        private bool _useEnhancedMode = true;

        // 捕获的特殊项目
        private object _capturedSpecialItem;

        // 多控件选择列表
        private List<Control> _selectedControls = new List<Control>();

        // 选中的控件路径
        private string _selectedControlPath;

        // 控件树路径
        private List<string> _controlTreePath = new List<string>();

        // 调整手柄
        private List<Rectangle> _resizeHandles = new List<Rectangle>();

        // 锚点显示
        private List<Point> _anchorPoints = new List<Point>();

        // 当前选中锚点
        private EnhancedSmartArea.AnchorPoint _selectedAnchor = EnhancedSmartArea.AnchorPoint.Center;

        // 是否显示锚点
        private bool _showAnchors = false;

        // 是否处于调整大小模式
        private bool _isResizing = false;

        // 当前调整的手柄索引
        private int _currentHandleIndex = -1;

        // 是否显示控件信息
        private bool _showControlInfo = true;

        // 当前悬停控件
        private Control _hoverControl;

        // 当前悬停特殊项目
        private object _hoverSpecialItem;

        // 是否为多选模式
        private bool _isMultiSelectMode = false;

        // 手柄大小
        private const int HandleSize = 8;

        // 锚点大小
        private const int AnchorSize = 10;

        // 锚点颜色
        private Color _anchorColor = Color.Yellow;

        // 选中锚点颜色
        private Color _selectedAnchorColor = Color.Red;

        // 控件轮廓颜色
        private Color _controlOutlineColor = Color.Lime;

        // 选中控件轮廓颜色
        private Color _selectedControlOutlineColor = Color.Yellow;

        // 调整手柄颜色
        private Color _handleColor = Color.White;

        // 调整手柄边框颜色
        private Color _handleBorderColor = Color.Black;

        // 鼠标悬停控件信息面板
        private Rectangle _infoPanel = Rectangle.Empty;

        // 固定大小模式下的大小
        private Size _fixedSize = new Size(100, 100);

        // 相对锚点模式下的偏移量
        private Point _anchorOffset = Point.Empty;

        // 选中的特殊项列表
        private List<SelectedSpecialItem> _selectedSpecialItems = new List<SelectedSpecialItem>();

        // 添加支持嵌套区域的变量
        private bool _isNestingMode = false;
        private Rectangle _parentRect = Rectangle.Empty;
        private List<Rectangle> _nestedRects = new List<Rectangle>();

        #endregion

        #region 构造函数和初始化

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="designer">设计器窗体</param>
        /// <param name="targetForm">目标窗体</param>
        public OverlayForm(GuideDesigner designer, Form targetForm)
        {
            InitializeComponent();
            _designer = designer;
            _targetForm = targetForm;

            // 设置Owner关系，确保窗口不会分离
            this.Owner = targetForm;

            InitializeOverlay();

            // 初始化增强型区域
            _enhancedArea = new EnhancedSmartArea();

            // 创建悬浮工具栏
            CreateFloatingToolbar();
        }

        /// <summary>
        /// 初始化覆盖层
        /// </summary>
        private void InitializeOverlay()
        {
            SetStyle(
                ControlStyles.DoubleBuffer |
                ControlStyles.OptimizedDoubleBuffer |
                ControlStyles.SupportsTransparentBackColor
                , true);
            // 设置窗体属性
            this.FormBorderStyle = FormBorderStyle.None;
            this.ShowInTaskbar = false;
            this.BackColor = Color.FromArgb(50, 0, 0, 0); // 半透明黑色
            this.TransparencyKey = this.BackColor; // 设置透明色
            this.Opacity = 0.5;
            this.Cursor = Cursors.Cross;

            // 设置位置和大小
            Rectangle targetBounds = _targetForm.Bounds;
            this.Location = targetBounds.Location;
            this.Size = targetBounds.Size;

            // 绑定事件
            this.Paint += OverlayForm_Paint;
            this.MouseDown += OverlayForm_MouseDown;
            this.MouseMove += OverlayForm_MouseMove;
            this.MouseUp += OverlayForm_MouseUp;
            this.KeyDown += OverlayForm_KeyDown;

            // 设置双缓冲以减少闪烁
            this.SetStyle(ControlStyles.OptimizedDoubleBuffer |
                          ControlStyles.AllPaintingInWmPaint |
                          ControlStyles.UserPaint, true);
        }

        /// <summary>
        /// 创建悬浮工具栏
        /// </summary>
        private void CreateFloatingToolbar()
        {
            ToolStrip toolStrip = new ToolStrip();
            toolStrip.Dock = DockStyle.None;
            toolStrip.GripStyle = ToolStripGripStyle.Visible; // 显示拖动手柄
            toolStrip.BackColor = Color.FromArgb(200, 30, 30, 30);
            toolStrip.ForeColor = Color.White;
            toolStrip.Renderer = new CustomToolStripRenderer();

            // 添加工具按钮
            var btnSelect = new ToolStripButton("选择/框选");
            btnSelect.Click += (s, e) => SetCaptureMode(CaptureMode.Select);
            btnSelect.ToolTipText = "单击选择控件，拖动框选区域";

            var btnMulti = new ToolStripButton("多选");
            btnMulti.Click += (s, e) => SetCaptureMode(CaptureMode.Multi);
            btnMulti.ToolTipText = "选择多个控件组成组合";

            var btnNested = new ToolStripButton("嵌套选择");
            btnNested.Click += (s, e) => ToggleNestingMode();
            btnNested.ToolTipText = "在已选区域内选择子区域";

            var btnAnchor = new ToolStripButton("锚点");
            btnAnchor.Click += (s, e) => CycleAnchorPoint();
            btnAnchor.ToolTipText = "切换锚点位置(A键)";

            var btnCombine = new ToolStripButton("组合模式");
            btnCombine.Click += (s, e) => CycleCombineMode();
            btnCombine.ToolTipText = "切换组合模式(M键)";

            var btnConfirm = new ToolStripButton("确认");
            btnConfirm.Click += (s, e) => ApplySelectedArea();
            btnConfirm.ToolTipText = "确认当前选择";

            var btnCancel = new ToolStripButton("取消");
            btnCancel.Click += (s, e) => CancelOperation();
            btnCancel.ToolTipText = "取消操作";

            // 添加帮助按钮
            var btnHelp = new ToolStripButton("帮助");
            btnHelp.Click += (s, e) => ShowHelpInfo();
            btnHelp.ToolTipText = "显示帮助信息";

            // 添加按钮到工具栏
            toolStrip.Items.Add(btnSelect);
            toolStrip.Items.Add(btnMulti);
            toolStrip.Items.Add(btnNested);
            toolStrip.Items.Add(new ToolStripSeparator());
            toolStrip.Items.Add(btnAnchor);
            toolStrip.Items.Add(btnCombine);
            toolStrip.Items.Add(new ToolStripSeparator());
            toolStrip.Items.Add(btnConfirm);
            toolStrip.Items.Add(btnCancel);
            toolStrip.Items.Add(new ToolStripSeparator());
            toolStrip.Items.Add(btnHelp);

            this.Controls.Add(toolStrip);

            // 将工具栏设置在窗体内，靠右侧放置
            int initialX = 10;
            int initialY = 50;
            toolStrip.Location = new Point(initialX, initialY);

            // 添加窗体大小变化事件处理
            this.SizeChanged += (s, e) =>
            {
                // 确保工具栏始终在窗口范围内
                if (toolStrip.Location.X + toolStrip.Width > this.Width)
                {
                    toolStrip.Location = new Point(this.Width - toolStrip.Width - 10, toolStrip.Location.Y);
                }
            };

            // 允许工具栏拖拽，增加自定义拖拽处理
            toolStrip.MouseDown += (sender, e) =>
            {
                if (e.Button == MouseButtons.Left)
                {
                    toolStrip.Tag = new Point(e.X, e.Y);
                }
            };

            toolStrip.MouseMove += (sender, e) =>
            {
                if (e.Button == MouseButtons.Left && toolStrip.Tag is Point startPoint)
                {
                    int deltaX = e.X - startPoint.X;
                    int deltaY = e.Y - startPoint.Y;

                    int newX = toolStrip.Left + deltaX;
                    int newY = toolStrip.Top + deltaY;

                    // 确保工具栏部分可见
                    if (newX > this.Width - 50) newX = this.Width - 50;
                    if (newY > this.Height - 20) newY = this.Height - 20;
                    if (newY < -20) newY = -20;

                    toolStrip.Location = new Point(newX, newY);
                }
            };

            toolStrip.MouseUp += (sender, e) =>
            {
                if (e.Button == MouseButtons.Left)
                {
                    toolStrip.Tag = null;
                }
            };

            // 自定义工具栏渲染器
            toolStrip.Tag = toolStrip; // 防止被GC回收
        }

        /// <summary>
        /// 显示帮助信息
        /// </summary>
        private void ShowHelpInfo()
        {
            string helpMessage =
                "操作指南：\n\n" +
                "基本操作：\n" +
                "- 单击：选择控件\n" +
                "- 拖动：框选区域\n" +
                "- Esc：取消操作\n\n" +

                "智能模式快捷键：\n" +
                "- R 键：切换到区域框选模式（默认）\n" +
                "- M 键：切换到多选控件模式\n" +
                "- A 键：切换锚点位置\n" +
                "- C 键：切换组合模式\n" +
                "- S 键：切换特殊项目模式\n\n" +

                "区域框选模式：\n" +
                "- 直接拖动框选需要的区域\n" +
                "- 比多选控件模式更简单直观\n\n" +

                "多选控件模式：\n" +
                "- 单击选择多个控件组成组合\n" +
                "- 再次单击可取消选择\n\n" +

                "锚点模式：\n" +
                "- 用于指定控件的定位点\n" +
                "- 防止界面缩放时高亮区域偏移\n\n";

            MessageBox.Show(this, helpMessage, "操作帮助",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 自定义工具栏渲染器
        /// </summary>
        private class CustomToolStripRenderer : ToolStripProfessionalRenderer
        {
            public CustomToolStripRenderer() : base(new CustomColorTable())
            {
            }

            protected override void OnRenderToolStripBorder(ToolStripRenderEventArgs e)
            {
                // 绘制半透明边框
                using (Pen borderPen = new Pen(Color.FromArgb(100, 200, 200, 200)))
                {
                    e.Graphics.DrawRectangle(borderPen, new Rectangle(0, 0, e.ToolStrip.Width - 1, e.ToolStrip.Height - 1));
                }
            }
        }

        /// <summary>
        /// 自定义颜色表
        /// </summary>
        private class CustomColorTable : ProfessionalColorTable
        {
            public override Color ToolStripGradientBegin => Color.FromArgb(200, 40, 40, 40);
            public override Color ToolStripGradientMiddle => Color.FromArgb(200, 30, 30, 30);
            public override Color ToolStripGradientEnd => Color.FromArgb(200, 20, 20, 20);
            public override Color ButtonSelectedHighlight => Color.FromArgb(200, 100, 100, 100);
            public override Color ButtonSelectedGradientBegin => Color.FromArgb(200, 80, 80, 80);
            public override Color ButtonSelectedGradientMiddle => Color.FromArgb(200, 70, 70, 70);
            public override Color ButtonSelectedGradientEnd => Color.FromArgb(200, 60, 60, 60);
        }

        /// <summary>
        /// 捕获模式枚举
        /// </summary>
        private enum CaptureMode
        {
            Select,   // 单选控件
            Area,     // 框选区域
            Multi,    // 多选控件
            Special   // 特殊项目
        }

        /// <summary>
        /// 当前捕获模式
        /// </summary>
        private CaptureMode _captureMode = CaptureMode.Select;

        /// <summary>
        /// 设置捕获模式
        /// </summary>
        private void SetCaptureMode(CaptureMode mode)
        {
            _captureMode = mode;

            switch (mode)
            {
                case CaptureMode.Select:
                    _isMultiSelectMode = false;
                    this.Cursor = Cursors.Hand;

                    // 重置增强型区域
                    if (_enhancedArea != null)
                    {
                        _enhancedArea = new EnhancedSmartArea();
                        _enhancedArea.Type = EnhancedSmartArea.AreaType.ControlBased;
                    }
                    break;

                case CaptureMode.Area:
                    _isMultiSelectMode = false;
                    this.Cursor = Cursors.Cross;

                    // 确保正确设置EnhancedArea类型
                    if (_enhancedArea != null)
                    {
                        _enhancedArea.Type = EnhancedSmartArea.AreaType.RelativeArea;
                    }
                    break;

                case CaptureMode.Multi:
                    _isMultiSelectMode = true;
                    this.Cursor = Cursors.Hand;

                    // 确保正确设置EnhancedArea类型
                    if (_enhancedArea != null)
                    {
                        _enhancedArea.Type = EnhancedSmartArea.AreaType.MultiControl;
                    }
                    break;

                case CaptureMode.Special:
                    _isMultiSelectMode = false;
                    this.Cursor = Cursors.Hand;
                    break;
            }

            _selectedControls.Clear();
            _selectedSpecialItems.Clear();
            _highlightRect = Rectangle.Empty;

            // 更新UI提示
            Invalidate();
        }

        /// <summary>
        /// 循环切换锚点位置
        /// </summary>
        private void CycleAnchorPoint()
        {
            // 获取所有锚点值
            var anchorValues = Enum.GetValues(typeof(EnhancedSmartArea.AnchorPoint));

            // 获取当前锚点索引
            int currentIndex = 0;
            for (int i = 0; i < anchorValues.Length; i++)
            {
                if ((EnhancedSmartArea.AnchorPoint)anchorValues.GetValue(i) == _selectedAnchor)
                {
                    currentIndex = i;
                    break;
                }
            }

            // 切换到下一个锚点
            int nextIndex = (currentIndex + 1) % anchorValues.Length;
            _selectedAnchor = (EnhancedSmartArea.AnchorPoint)anchorValues.GetValue(nextIndex);

            // 更新UI
            _showAnchors = true;
            UpdateAnchorPoints();
            Invalidate();
        }

        /// <summary>
        /// 循环切换组合模式
        /// </summary>
        private void CycleCombineMode()
        {
            // 如果已经有选中的控件，重新计算
            if (_selectedControls.Count > 0 || _selectedSpecialItems.Count > 0)
            {
                // 重建区域以应用新的组合模式
                RebuildEnhancedArea();
            }
            // 更新UI
            Invalidate();
        }

        /// <summary>
        /// 更新锚点位置
        /// </summary>
        private void UpdateAnchorPoints()
        {
            _anchorPoints.Clear();

            if (_highlightRect.IsEmpty) return;

            // 添加9个标准锚点位置
            _anchorPoints.Add(new Point(_highlightRect.Left, _highlightRect.Top));                 // 左上
            _anchorPoints.Add(new Point(_highlightRect.Left + _highlightRect.Width / 2, _highlightRect.Top));           // 上中
            _anchorPoints.Add(new Point(_highlightRect.Right, _highlightRect.Top));                // 右上
            _anchorPoints.Add(new Point(_highlightRect.Left, _highlightRect.Top + _highlightRect.Height / 2));          // 左中
            _anchorPoints.Add(new Point(_highlightRect.Left + _highlightRect.Width / 2, _highlightRect.Top + _highlightRect.Height / 2));    // 中心
            _anchorPoints.Add(new Point(_highlightRect.Right, _highlightRect.Top + _highlightRect.Height / 2));         // 右中
            _anchorPoints.Add(new Point(_highlightRect.Left, _highlightRect.Bottom));              // 左下
            _anchorPoints.Add(new Point(_highlightRect.Left + _highlightRect.Width / 2, _highlightRect.Bottom));        // 下中
            _anchorPoints.Add(new Point(_highlightRect.Right, _highlightRect.Bottom));             // 右下
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 开始控件捕获
        /// </summary>
        public void StartControlCapture(GuideItem step)
        {
            if (step == null) return;

            _currentStep = step;
            _mode = GuideDesigner.DesignMode.ControlCapture;
            _captureMode = CaptureMode.Select;

            // 显示帮助提示
            _showControlInfo = true;

            // 初始化增强型区域
            if (_enhancedArea == null)
            {
                _enhancedArea = new EnhancedSmartArea();
                _enhancedArea.Type = EnhancedSmartArea.AreaType.ControlBased;
            }

            // 启用多维度定位策略
            _useEnhancedMode = true;

            // 禁用WSAD导航，避免干扰
            this.KeyPreview = true;

            // 设置鼠标指针
            this.Cursor = Cursors.Hand;

            // 恢复状态：如果步骤已有EnhancedArea数据，恢复选区
            if (step.EnhancedArea != null)
            {
                _enhancedArea = step.EnhancedArea;
                _highlightRect = step.EnhancedArea.CalculateActualRect(_targetForm);

                // 恢复选中的控件和特殊项
                if (step.EnhancedArea.Type == EnhancedSmartArea.AreaType.MultiControl ||
                    step.EnhancedArea.Type == EnhancedSmartArea.AreaType.ControlBased)
                {
                    // 尝试重建选区状态
                    RebuildSelectionFromEnhancedArea();
                }
            }

            // 显示遮罩表单
            this.Show();
            this.BringToFront();

            // 显示控件捕获模式说明
            MessageBox.Show(
                "控件捕获模式\n\n" +
                "- 单击选择任意控件\n" +
                "- 使用多维度定位策略自动分析控件特征\n" +
                "- 分析控件ID、视觉特征、相对位置和邻近关系\n" +
                "- 自动适应布局变化和尺寸调整\n\n" +
                "按ESC取消，按Enter确认选择",
                "控件捕获",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);

            // 触发重绘
            this.Invalidate();
        }

        /// <summary>
        /// 开始区域选择
        /// </summary>
        public void StartAreaSelect(GuideItem step)
        {
            if (step == null) return;

            _currentStep = step;
            _mode = GuideDesigner.DesignMode.AreaSelect;
            _captureMode = CaptureMode.Area;

            // 重置状态
            _isDragging = false;

            // 恢复状态：如果步骤已有EnhancedArea数据，恢复选区
            if (step.EnhancedArea != null)
            {
                _enhancedArea = step.EnhancedArea;
                _highlightRect = step.EnhancedArea.CalculateActualRect(_targetForm);

                // 设置调整柄
                UpdateResizeHandles(_highlightRect);
            }

            // 设置鼠标指针
            this.Cursor = Cursors.Cross;

            // 显示遮罩表单
            this.Show();
            this.BringToFront();

            // 触发重绘
            this.Invalidate();
        }

        /// <summary>
        /// 开始智能捕获
        /// </summary>
        public void StartSmartCapture(GuideItem step)
        {
            if (step == null) return;

            _currentStep = step;
            _mode = GuideDesigner.DesignMode.SmartCapture;

            // 启用增强模式
            _useEnhancedMode = true;

            // 清空选中控件列表
            _selectedControls.Clear();
            _selectedSpecialItems.Clear();

            // 恢复状态：如果步骤已有EnhancedArea数据，恢复选区
            if (step.EnhancedArea != null)
            {
                // 复用现有的增强区域
                _enhancedArea = step.EnhancedArea;
                _highlightRect = step.EnhancedArea.CalculateActualRect(_targetForm);

                // 根据EnhancedArea类型设置适当的捕获模式
                if (step.EnhancedArea.Type == EnhancedSmartArea.AreaType.MultiControl)
                {
                    _captureMode = CaptureMode.Multi;
                    _isMultiSelectMode = true;
                }
                else
                {
                    // 默认使用区域框选模式
                    _captureMode = CaptureMode.Area;
                    _isMultiSelectMode = false;
                }

                // 尝试重建选区状态
                RebuildSelectionFromEnhancedArea();

                // 设置调整柄
                UpdateResizeHandles(_highlightRect);
            }
            else
            {
                // 如果之前没有增强区域数据，则创建新的
                _enhancedArea = new EnhancedSmartArea();
                _enhancedArea.Type = EnhancedSmartArea.AreaType.RelativeArea;  // 默认使用相对区域类型
                _enhancedArea.AutoDetectChanges = true;

                // 默认使用区域框选模式
                _captureMode = CaptureMode.Area;
                _isMultiSelectMode = false;
            }

            // 创建工具栏
            CreateFloatingToolbar();

            // 设置鼠标指针 - 使用十字光标，与区域选择保持一致
            this.Cursor = Cursors.Cross;

            // 显示遮罩表单
            this.Show();
            this.BringToFront();

            // 触发重绘
            this.Invalidate();
        }

        /// <summary>
        /// 从EnhancedArea重建选中项
        /// </summary>
        private void RebuildSelectionFromEnhancedArea()
        {
            if (_enhancedArea == null)
                return;

            _selectedControls.Clear();
            _selectedSpecialItems.Clear();

            // 计算实际区域
            _highlightRect = _enhancedArea.CalculateActualRect(_targetForm);

            // 重建控件选区
            foreach (var anchor in _enhancedArea.Anchors)
            {
                string controlId = anchor.ControlId;
                if (!string.IsNullOrEmpty(controlId))
                {
                    // 检查是否为ToolStripItem (通常格式为 "parentControl.itemName")
                    if (controlId.Contains("."))
                    {
                        string[] parts = controlId.Split('.');
                        if (parts.Length >= 2)
                        {
                            string parentName = parts[0];
                            string itemName = parts[1];

                            // 查找父控件
                            var parentControl = _targetForm.Controls.Find(parentName, true).FirstOrDefault();
                            if (parentControl != null && parentControl is ToolStrip)
                            {
                                ToolStrip toolStrip = parentControl as ToolStrip;
                                ToolStripItem item = toolStrip.Items[itemName];

                                if (item != null)
                                {
                                    // 添加到特殊项列表
                                    Rectangle itemRect = GetSpecialItemRect(item, toolStrip);
                                    _selectedSpecialItems.Add(new SelectedSpecialItem
                                    {
                                        Item = item,
                                        Parent = toolStrip,
                                        Rect = itemRect
                                    });
                                }
                            }
                        }
                    }
                    else
                    {
                        // 尝试找到常规控件
                        var control = _targetForm.Controls.Find(controlId, true).FirstOrDefault();
                        if (control != null)
                        {
                            _selectedControls.Add(control);
                        }
                    }
                }
            }

            // 如果组件中有更多信息，也处理它们
            foreach (var component in _enhancedArea.Components)
            {
                // 检查组件是否基于控件且不是通过Anchors已处理的
                if (component.Type == EnhancedSmartArea.AreaType.ControlBased &&
                    component.Anchor != null &&
                    !_enhancedArea.Anchors.Contains(component.Anchor))
                {
                    string controlId = component.Anchor.ControlId;
                    if (!string.IsNullOrEmpty(controlId))
                    {
                        // 尝试找到对应控件(与上面类似的代码)
                        if (controlId.Contains("."))
                        {
                            // 处理ToolStripItem
                            string[] parts = controlId.Split('.');
                            if (parts.Length >= 2)
                            {
                                string parentName = parts[0];
                                string itemName = parts[1];

                                var parentControl = _targetForm.Controls.Find(parentName, true).FirstOrDefault();
                                if (parentControl != null && parentControl is ToolStrip)
                                {
                                    ToolStrip toolStrip = parentControl as ToolStrip;
                                    ToolStripItem item = toolStrip.Items[itemName];

                                    if (item != null && !_selectedSpecialItems.Any(s => s.Item == item))
                                    {
                                        Rectangle itemRect = GetSpecialItemRect(item, toolStrip);
                                        _selectedSpecialItems.Add(new SelectedSpecialItem
                                        {
                                            Item = item,
                                            Parent = toolStrip,
                                            Rect = itemRect
                                        });
                                    }
                                }
                            }
                        }
                        else
                        {
                            // 处理常规控件
                            var control = _targetForm.Controls.Find(controlId, true).FirstOrDefault();
                            if (control != null && !_selectedControls.Contains(control))
                            {
                                _selectedControls.Add(control);
                            }
                        }
                    }
                }
            }

            // 处理增强型数据
            if (_enhancedArea.Type == EnhancedSmartArea.AreaType.RelativeArea)
            {
                // 收集邻近控件信息
                CollectNearbyControls(_highlightRect);

                // 分析内容结构
                AnalyzeContentStructure(_highlightRect);

                // 分析与窗体边缘的关系
                AnalyzeEdgeRelationships(_highlightRect);
            }

            // 更新调整柄
            if (!_highlightRect.IsEmpty)
            {
                UpdateResizeHandles(_highlightRect);
            }

            // 更新锚点
            UpdateAnchorPoints();
        }

        /// <summary>
        /// 更新调整大小的控制点
        /// </summary>
        private void UpdateResizeHandles(Rectangle rect)
        {
            _resizeHandles.Clear();

            if (rect.IsEmpty) return;

            int halfSize = HandleSize / 2;

            // 添加8个调整大小的控制点
            // 左上
            _resizeHandles.Add(new Rectangle(
                rect.Left - halfSize,
                rect.Top - halfSize,
                HandleSize, HandleSize));

            // 上中
            _resizeHandles.Add(new Rectangle(
                rect.Left + rect.Width / 2 - halfSize,
                rect.Top - halfSize,
                HandleSize, HandleSize));

            // 右上
            _resizeHandles.Add(new Rectangle(
                rect.Right - halfSize,
                rect.Top - halfSize,
                HandleSize, HandleSize));

            // 右中
            _resizeHandles.Add(new Rectangle(
                rect.Right - halfSize,
                rect.Top + rect.Height / 2 - halfSize,
                HandleSize, HandleSize));

            // 右下
            _resizeHandles.Add(new Rectangle(
                rect.Right - halfSize,
                rect.Bottom - halfSize,
                HandleSize, HandleSize));

            // 下中
            _resizeHandles.Add(new Rectangle(
                rect.Left + rect.Width / 2 - halfSize,
                rect.Bottom - halfSize,
                HandleSize, HandleSize));

            // 左下
            _resizeHandles.Add(new Rectangle(
                rect.Left - halfSize,
                rect.Bottom - halfSize,
                HandleSize, HandleSize));

            // 左中
            _resizeHandles.Add(new Rectangle(
                rect.Left - halfSize,
                rect.Top + rect.Height / 2 - halfSize,
                HandleSize, HandleSize));
        }

        /// <summary>
        /// 开始增强型区域设计
        /// </summary>
        /// <param name="step">当前步骤</param>
        public void StartEnhancedDesign(GuideItem step)
        {
            _currentStep = step;
            _mode = GuideDesigner.DesignMode.SmartCapture; // 统一使用SmartCapture模式
            this.BackColor = Color.FromArgb(10, 100, 100, 100); // 半透明灰色
            this.Cursor = Cursors.Cross;
            this.Opacity = 0.3;

            // 重置选择状态
            _selectedControls.Clear();
            _resizeHandles.Clear();
            _anchorPoints.Clear();
            _capturedControl = null;
            _capturedSpecialItem = null;
            _highlightRect = Rectangle.Empty;
            _showAnchors = false;
            _isResizing = false;
            _isMultiSelectMode = false;

            // 初始化增强型区域
            if (_currentStep.EnhancedArea == null)
            {
                _enhancedArea = new EnhancedSmartArea();
                _currentStep.EnhancedArea = _enhancedArea;
            }
            else
            {
                _enhancedArea = _currentStep.EnhancedArea;
            }

            // 先显示覆盖层窗体
            this.Show();
            this.BringToFront();

            // 确保覆盖层与目标窗体大小一致
            Rectangle targetBounds = _targetForm.Bounds;
            this.Location = targetBounds.Location;
            this.Size = targetBounds.Size;

            // 使用异步方式显示消息框，避免阻塞UI线程
            this.BeginInvoke(new Action(() =>
            {
                MessageBox.Show(this, "增强型智能设计模式：\n" +
                    "- 单击选择控件或区域\n" +
                    "- Ctrl+单击添加多个控件\n" +
                    "- Shift+拖动创建自定义区域\n" +
                    "- 拖动调整大小和位置\n" +
                    "- A键切换锚点模式\n" +
                    "- M键切换组合模式\n" +
                    "- R键设置相对大小\n" +
                    "- F键设置固定大小\n" +
                    "- Tab键在层次控件间切换\n" +
                    "- Enter确认选择\n" +
                    "- ESC取消操作",
                    "增强型区域设计", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }));
        }

        /// <summary>
        /// 开始多控件选择模式
        /// </summary>
        /// <param name="step">当前步骤</param>
        public void StartMultiControlSelect(GuideItem step)
        {
            _currentStep = step;
            _mode = GuideDesigner.DesignMode.SmartCapture; // 使用SmartCapture模式
            this.BackColor = Color.FromArgb(10, 0, 100, 255); // 淡蓝色
            this.Cursor = Cursors.Hand;
            this.Opacity = 0.3;

            // 设置多选模式
            _isMultiSelectMode = true;
            _selectedControls.Clear();

            // 初始化增强型区域
            if (_currentStep.EnhancedArea == null)
            {
                _enhancedArea = new EnhancedSmartArea();
                _currentStep.EnhancedArea = _enhancedArea;
            }
            else
            {
                _enhancedArea = _currentStep.EnhancedArea;
            }

            // 先显示覆盖层窗体
            this.Show();
            this.BringToFront();

            // 确保覆盖层与目标窗体大小一致
            Rectangle targetBounds = _targetForm.Bounds;
            this.Location = targetBounds.Location;
            this.Size = targetBounds.Size;
        }

        /// <summary>
        /// 开始锚点定位模式
        /// </summary>
        /// <param name="step">当前步骤</param>
        public void StartAnchorPositioning(GuideItem step)
        {
            _currentStep = step;
            _mode = GuideDesigner.DesignMode.SmartCapture; // 使用SmartCapture模式
            this.BackColor = Color.FromArgb(10, 255, 128, 0); // 淡橙色
            this.Cursor = Cursors.Hand;
            this.Opacity = 0.3;

            // 显示锚点
            _showAnchors = true;

            // 初始化增强型区域
            if (_currentStep.EnhancedArea == null)
            {
                _enhancedArea = new EnhancedSmartArea();
                _currentStep.EnhancedArea = _enhancedArea;
            }
            else
            {
                _enhancedArea = _currentStep.EnhancedArea;
            }

            // 设置锚点定位模式
            _enhancedArea.Type = EnhancedSmartArea.AreaType.AnchorBased;

            // 先显示覆盖层窗体
            this.Show();
            this.BringToFront();

            // 确保覆盖层与目标窗体大小一致
            Rectangle targetBounds = _targetForm.Bounds;
            this.Location = targetBounds.Location;
            this.Size = targetBounds.Size;

            // 使用异步方式显示消息框，避免阻塞UI线程
            this.BeginInvoke(new Action(() =>
            {
                MessageBox.Show(this, "锚点定位模式：\n" +
                    "- 单击选择控件\n" +
                    "- A键循环切换锚点位置\n" +
                    "- 方向键微调位置\n" +
                    "- S键切换大小模式\n" +
                    "- 拖动调整位置\n" +
                    "- Enter确认选择\n" +
                    "- ESC取消操作",
                    "锚点定位", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }));
        }

        /// <summary>
        /// 开始相对位置模式
        /// </summary>
        /// <param name="step">当前步骤</param>
        public void StartRelativePositioning(GuideItem step)
        {
            _currentStep = step;
            _mode = GuideDesigner.DesignMode.SmartCapture; // 使用SmartCapture模式
            this.BackColor = Color.FromArgb(10, 128, 0, 255); // 淡紫色
            this.Cursor = Cursors.Hand;
            this.Opacity = 0.3;

            // 初始化增强型区域
            if (_currentStep.EnhancedArea == null)
            {
                _enhancedArea = new EnhancedSmartArea();
                _currentStep.EnhancedArea = _enhancedArea;
            }
            else
            {
                _enhancedArea = _currentStep.EnhancedArea;
            }

            // 设置相对区域模式
            _enhancedArea.Type = EnhancedSmartArea.AreaType.RelativeArea;

            // 先显示覆盖层窗体
            this.Show();
            this.BringToFront();

            // 确保覆盖层与目标窗体大小一致
            Rectangle targetBounds = _targetForm.Bounds;
            this.Location = targetBounds.Location;
            this.Size = targetBounds.Size;

            // 使用异步方式显示消息框，避免阻塞UI线程
            this.BeginInvoke(new Action(() =>
            {
                MessageBox.Show(this, "相对位置模式：\n" +
                    "- 拖动创建相对区域\n" +
                    "- R键设置相对大小\n" +
                    "- P键设置相对位置\n" +
                    "- Enter确认选择\n" +
                    "- ESC取消操作",
                    "相对位置", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }));
        }

        /// <summary>
        /// 切换嵌套模式
        /// </summary>
        public void ToggleNestingMode()
        {
            _isNestingMode = !_isNestingMode;

            if (_isNestingMode)
            {
                if (!_highlightRect.IsEmpty)
                {
                    _parentRect = _highlightRect;
                    this.Cursor = Cursors.Cross;
                    MessageBox.Show("已进入嵌套区域模式，请在选定区域内拖动鼠标选择子区域", "嵌套区域",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("请先选择一个父区域", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    _isNestingMode = false;
                }
            }
            else
            {
                _parentRect = Rectangle.Empty;
                this.Cursor = Cursors.Default;
            }

            Invalidate();
        }

        /// <summary>
        /// 创建嵌套区域
        /// </summary>
        private void CreateNestedArea(Rectangle rect)
        {
            // 确保嵌套区域在父区域内部
            Rectangle nestedRect = new Rectangle(
                Math.Max(rect.X, _parentRect.X),
                Math.Max(rect.Y, _parentRect.Y),
                Math.Min(rect.Width, _parentRect.Right - Math.Max(rect.X, _parentRect.X)),
                Math.Min(rect.Height, _parentRect.Bottom - Math.Max(rect.Y, _parentRect.Y))
            );

            if (nestedRect.Width > 5 && nestedRect.Height > 5)
            {
                _nestedRects.Add(nestedRect);
                _highlightRect = nestedRect;

                // 应用到增强型区域
                if (_enhancedArea != null)
                {
                    _enhancedArea.Type = EnhancedSmartArea.AreaType.CustomArea;
                    _enhancedArea.AddInnerRectangle(nestedRect, _targetForm.ClientSize, _parentRect);
                }

                Invalidate();
            }
        }

        #endregion

        #region 事件处理器

        private void OverlayForm_Paint(object sender, PaintEventArgs e)
        {
            // 绘制拖动选择的矩形
            if (_isDragging && _captureMode == CaptureMode.Area)
            {
                Rectangle rect = GetSelectionRectangle();
                if (!rect.IsEmpty && rect.Width > 3 && rect.Height > 3)
                {
                    DrawSelectionRectangle(e.Graphics, rect);
                }
            }

            // 绘制嵌套模式的父区域
            if (_isNestingMode && !_parentRect.IsEmpty)
            {
                using (Pen parentPen = new Pen(Color.FromArgb(200, 255, 128, 0), 3))
                {
                    e.Graphics.DrawRectangle(parentPen, _parentRect);
                }

                // 绘制半透明蒙层，只有父区域内可见
                using (Brush maskBrush = new SolidBrush(Color.FromArgb(100, 30, 30, 30)))
                {
                    // 绘制整个窗口的蒙层
                    e.Graphics.FillRectangle(maskBrush, this.ClientRectangle);

                    // 清除父区域的蒙层，使其透明
                    e.Graphics.SetClip(_parentRect);
                    e.Graphics.Clear(Color.Transparent);
                    e.Graphics.ResetClip();
                }

                // 绘制嵌套区域
                foreach (var nestedRect in _nestedRects)
                {
                    using (Pen nestedPen = new Pen(Color.Yellow, 2))
                    {
                        e.Graphics.DrawRectangle(nestedPen, nestedRect);
                    }
                }
            }

            // 绘制高亮矩形
            if (!_highlightRect.IsEmpty)
            {
                using (Pen pen = new Pen(Color.Yellow, 3))
                {
                    e.Graphics.DrawRectangle(pen, _highlightRect);
                }

                // 绘制矩形尺寸信息
                string sizeInfo = $"{_highlightRect.Width} × {_highlightRect.Height}";
                using (Font font = new Font("Arial", 10, FontStyle.Bold))
                using (SolidBrush brush = new SolidBrush(Color.Yellow))
                using (SolidBrush bgBrush = new SolidBrush(Color.FromArgb(150, 0, 0, 0)))
                {
                    SizeF textSize = e.Graphics.MeasureString(sizeInfo, font);
                    Point textPos = new Point(
                        _highlightRect.Right - (int)textSize.Width - 5,
                        _highlightRect.Bottom + 5);

                    // 绘制背景
                    e.Graphics.FillRectangle(bgBrush,
                        textPos.X - 2, textPos.Y - 2,
                        textSize.Width + 4, textSize.Height + 4);

                    // 绘制文本
                    e.Graphics.DrawString(sizeInfo, font, brush, textPos);
                }
            }

            // 绘制增强型交互元素
            if (_useEnhancedMode)
            {
                // 绘制调整手柄
                if (_resizeHandles.Count > 0)
                {
                    foreach (var handle in _resizeHandles)
                    {
                        // 填充内部
                        using (SolidBrush handleBrush = new SolidBrush(_handleColor))
                        {
                            e.Graphics.FillRectangle(handleBrush, handle);
                        }

                        // 绘制边框
                        using (Pen handlePen = new Pen(_handleBorderColor, 1))
                        {
                            e.Graphics.DrawRectangle(handlePen, handle);
                        }
                    }
                }

                // 绘制锚点
                if (_showAnchors && _anchorPoints.Count > 0)
                {
                    for (int i = 0; i < _anchorPoints.Count; i++)
                    {
                        Point p = _anchorPoints[i];
                        Rectangle anchorRect = new Rectangle(
                            p.X - AnchorSize / 2,
                            p.Y - AnchorSize / 2,
                            AnchorSize,
                            AnchorSize);

                        // 使用不同颜色绘制选中的锚点
                        Color anchorColor = ((EnhancedSmartArea.AnchorPoint)i == _selectedAnchor)
                            ? _selectedAnchorColor
                            : _anchorColor;

                        // 绘制填充圆形
                        using (SolidBrush anchorBrush = new SolidBrush(anchorColor))
                        {
                            e.Graphics.FillEllipse(anchorBrush, anchorRect);
                        }

                        // 绘制边框
                        using (Pen anchorPen = new Pen(Color.Black, 1))
                        {
                            e.Graphics.DrawEllipse(anchorPen, anchorRect);
                        }
                    }
                }

                // 绘制多选模式下的选中控件和特殊项
                if (_captureMode == CaptureMode.Multi)
                {
                    // 绘制选中的控件
                    foreach (var control in _selectedControls)
                    {
                        Rectangle controlRect = GetControlRectInForm(control);
                        if (!controlRect.IsEmpty)
                        {
                            // 绘制控件轮廓
                            using (Pen selectedPen = new Pen(_selectedControlOutlineColor, 2))
                            {
                                e.Graphics.DrawRectangle(selectedPen, controlRect);
                            }

                            // 绘制控件名称
                            string controlName = control.Name;
                            if (string.IsNullOrEmpty(controlName)) controlName = control.GetType().Name;

                            using (Font font = new Font("Arial", 8, FontStyle.Bold))
                            using (SolidBrush brush = new SolidBrush(_selectedControlOutlineColor))
                            using (SolidBrush bgBrush = new SolidBrush(Color.FromArgb(150, 0, 0, 0)))
                            {
                                SizeF textSize = e.Graphics.MeasureString(controlName, font);
                                Point textPos = new Point(
                                    controlRect.Left,
                                    controlRect.Top - (int)textSize.Height - 2);

                                // 绘制背景
                                e.Graphics.FillRectangle(bgBrush,
                                    textPos.X - 2, textPos.Y - 2,
                                    textSize.Width + 4, textSize.Height + 4);

                                // 绘制文本
                                e.Graphics.DrawString(controlName, font, brush, textPos);
                            }
                        }
                    }

                    // 绘制选中的特殊项
                    foreach (var item in _selectedSpecialItems)
                    {
                        if (!item.Rect.IsEmpty)
                        {
                            // 绘制特殊项轮廓
                            using (Pen selectedPen = new Pen(Color.Orange, 2))
                            {
                                e.Graphics.DrawRectangle(selectedPen, item.Rect);
                            }

                            // 绘制特殊项类型
                            string itemType = item.Item.GetType().Name;

                            using (Font font = new Font("Arial", 8, FontStyle.Bold))
                            using (SolidBrush brush = new SolidBrush(Color.Orange))
                            using (SolidBrush bgBrush = new SolidBrush(Color.FromArgb(150, 0, 0, 0)))
                            {
                                SizeF textSize = e.Graphics.MeasureString(itemType, font);
                                Point textPos = new Point(
                                    item.Rect.Left,
                                    item.Rect.Top - (int)textSize.Height - 2);

                                // 绘制背景
                                e.Graphics.FillRectangle(bgBrush,
                                    textPos.X - 2, textPos.Y - 2,
                                    textSize.Width + 4, textSize.Height + 4);

                                // 绘制文本
                                e.Graphics.DrawString(itemType, font, brush, textPos);
                            }
                        }
                    }
                }

                // 绘制鼠标悬停控件的信息
                if (_showControlInfo && _hoverControl != null)
                {
                    Rectangle controlRect = ControlExplorer.GetControlRectInForm(_hoverControl, _targetForm);

                    // 绘制控件轮廓
                    using (Pen hoverPen = new Pen(_controlOutlineColor, 1) { DashStyle = System.Drawing.Drawing2D.DashStyle.Dot })
                    {
                        e.Graphics.DrawRectangle(hoverPen, controlRect);
                    }

                    // 准备控件信息文本
                    string controlType = _hoverControl.GetType().Name;
                    string controlName = _hoverControl.Name;
                    string controlText = _hoverControl.Text;
                    string controlPath = ControlExplorer.GetControlPath(_hoverControl);

                    string controlInfo = $"类型: {controlType}\n名称: {controlName}";
                    if (!string.IsNullOrEmpty(controlText))
                        controlInfo += $"\n文本: {(controlText.Length > 30 ? controlText.Substring(0, 27) + "..." : controlText)}";
                    controlInfo += $"\n大小: {controlRect.Width} x {controlRect.Height}";

                    // 绘制信息面板
                    using (Font font = new Font("Arial", 8))
                    using (SolidBrush textBrush = new SolidBrush(Color.White))
                    using (SolidBrush bgBrush = new SolidBrush(Color.FromArgb(200, 0, 0, 0)))
                    using (Pen borderPen = new Pen(Color.FromArgb(200, 100, 100, 100), 1))
                    {
                        // 测量文本大小
                        SizeF textSize = e.Graphics.MeasureString(controlInfo, font);

                        // 计算信息面板位置
                        int panelWidth = (int)textSize.Width + 10;
                        int panelHeight = (int)textSize.Height + 10;
                        int panelX = controlRect.Right + 5;
                        int panelY = controlRect.Top;

                        // 确保面板在窗体内
                        if (panelX + panelWidth > this.Width)
                            panelX = controlRect.Left - panelWidth - 5;
                        if (panelY + panelHeight > this.Height)
                            panelY = this.Height - panelHeight - 5;

                        // 更新信息面板矩形
                        _infoPanel = new Rectangle(panelX, panelY, panelWidth, panelHeight);

                        // 绘制面板背景
                        e.Graphics.FillRectangle(bgBrush, _infoPanel);
                        e.Graphics.DrawRectangle(borderPen, _infoPanel);

                        // 绘制信息文本
                        e.Graphics.DrawString(controlInfo, font, textBrush,
                            new RectangleF(panelX + 5, panelY + 5, panelWidth - 10, panelHeight - 10));
                    }

                    // 绘制控件路径
                    if (!string.IsNullOrEmpty(controlPath))
                    {
                        using (Font pathFont = new Font("Arial", 8))
                        using (SolidBrush pathBrush = new SolidBrush(Color.Yellow))
                        using (SolidBrush pathBgBrush = new SolidBrush(Color.FromArgb(180, 0, 0, 0)))
                        {
                            // 截断过长路径
                            string displayPath = controlPath;
                            if (displayPath.Length > 50)
                                displayPath = "..." + displayPath.Substring(displayPath.Length - 47);

                            SizeF pathSize = e.Graphics.MeasureString(displayPath, pathFont);
                            Point pathPos = new Point(
                                controlRect.Left,
                                controlRect.Top - (int)pathSize.Height - 2);

                            // 绘制背景
                            e.Graphics.FillRectangle(pathBgBrush,
                                pathPos.X - 2, pathPos.Y - 2,
                                pathSize.Width + 4, pathSize.Height + 4);

                            // 绘制文本
                            e.Graphics.DrawString(displayPath, pathFont, pathBrush, pathPos);
                        }
                    }
                }

                // 绘制特殊项目信息
                if (_showControlInfo && _hoverSpecialItem != null)
                {
                    // 绘制特殊项目轮廓
                    Rectangle itemRect = GetSpecialItemRect(_hoverSpecialItem, _hoverControl);

                    if (!itemRect.IsEmpty)
                    {
                        using (Pen itemPen = new Pen(Color.Orange, 1) { DashStyle = System.Drawing.Drawing2D.DashStyle.Dot })
                        {
                            e.Graphics.DrawRectangle(itemPen, itemRect);
                        }

                        // 准备项目信息
                        string itemType = _hoverSpecialItem.GetType().Name;
                        string itemText = _hoverSpecialItem.ToString();
                        string itemPath = "";

                        if (_hoverControl != null)
                            itemPath = ControlExplorer.GetSpecialItemPath(_hoverSpecialItem, _hoverControl);

                        string itemInfo = $"类型: {itemType}\n文本: {(itemText.Length > 30 ? itemText.Substring(0, 27) + "..." : itemText)}";

                        // 绘制信息面板
                        using (Font font = new Font("Arial", 8))
                        using (SolidBrush textBrush = new SolidBrush(Color.White))
                        using (SolidBrush bgBrush = new SolidBrush(Color.FromArgb(200, 0, 0, 0)))
                        using (Pen borderPen = new Pen(Color.FromArgb(200, 100, 100, 100), 1))
                        {
                            // 测量文本大小
                            SizeF textSize = e.Graphics.MeasureString(itemInfo, font);

                            // 计算信息面板位置
                            int panelWidth = (int)textSize.Width + 10;
                            int panelHeight = (int)textSize.Height + 10;
                            int panelX = itemRect.Right + 5;
                            int panelY = itemRect.Top;

                            // 确保面板在窗体内
                            if (panelX + panelWidth > this.Width)
                                panelX = itemRect.Left - panelWidth - 5;
                            if (panelY + panelHeight > this.Height)
                                panelY = this.Height - panelHeight - 5;

                            // 绘制面板
                            Rectangle infoPanel = new Rectangle(panelX, panelY, panelWidth, panelHeight);
                            e.Graphics.FillRectangle(bgBrush, infoPanel);
                            e.Graphics.DrawRectangle(borderPen, infoPanel);

                            // 绘制信息文本
                            e.Graphics.DrawString(itemInfo, font, textBrush,
                                new RectangleF(panelX + 5, panelY + 5, panelWidth - 10, panelHeight - 10));
                        }
                    }
                }

                // 绘制当前模式信息提示
                string modeInfo = "当前模式: ";
                switch (_captureMode)
                {
                    case CaptureMode.Select: modeInfo += "控件选择"; break;
                    case CaptureMode.Area: modeInfo += "框选区域"; break;
                    case CaptureMode.Multi: modeInfo += "多选模式"; break;
                    case CaptureMode.Special: modeInfo += "特殊项选择"; break;
                }

                using (Font font = new Font("Arial", 10, FontStyle.Bold))
                using (SolidBrush textBrush = new SolidBrush(Color.White))
                using (SolidBrush bgBrush = new SolidBrush(Color.FromArgb(200, 30, 30, 30)))
                {
                    SizeF textSize = e.Graphics.MeasureString(modeInfo, font);

                    // 放在右上角显示
                    Point textPos = new Point(
                        this.Width - (int)textSize.Width - 10,
                        10);

                    // 绘制背景
                    e.Graphics.FillRectangle(bgBrush,
                        textPos.X - 5, textPos.Y - 5,
                        textSize.Width + 10, textSize.Height + 10);

                    // 绘制文本
                    e.Graphics.DrawString(modeInfo, font, textBrush, textPos);
                }
            }
        }

        private void OverlayForm_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                // 在多选模式下，不要重置_startPoint，以避免影响多选的计算
                if (!(_captureMode == CaptureMode.Multi && (_selectedControls.Count > 0 || _selectedSpecialItems.Count > 0)))
                {
                    _startPoint = e.Location;
                }
                _isDragging = false;
            }
        }

        private void OverlayForm_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                // 仅在框选模式或拖动距离足够大时启用拖动
                if ((_captureMode == CaptureMode.Area || !_isDragging) &&
                    (Math.Abs(e.X - _startPoint.X) > 5 || Math.Abs(e.Y - _startPoint.Y) > 5))
                {
                    _isDragging = true;
                }

                if (_isDragging)
                {
                    _endPoint = e.Location;
                    Invalidate(); // 重绘以显示选择框
                }
            }
            else
            {
                // 鼠标悬停检测
                if (_showControlInfo)
                {
                    _hoverControl = FindDeepestChildAt(_targetForm, e.Location);

                    // 检测特殊项目
                    _hoverSpecialItem = GetSpecialItemAtPoint(_hoverControl, e.Location);

                    Invalidate(); // 更新显示
                }
            }
        }

        private void OverlayForm_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                // 嵌套区域模式处理
                if (_isNestingMode && _isDragging)
                {
                    Rectangle selectedRect = GetSelectionRectangle();
                    if (!selectedRect.IsEmpty && selectedRect.Width > 5 && selectedRect.Height > 5)
                    {
                        CreateNestedArea(selectedRect);
                    }
                    _isDragging = false;
                    return;
                }

                // 处理拖动框选
                if (_isDragging && Math.Abs(_startPoint.X - e.X) > 5 && Math.Abs(_startPoint.Y - e.Y) > 5)
                {
                    Rectangle selectedRect = GetSelectionRectangle();
                    if (!selectedRect.IsEmpty && selectedRect.Width > 5 && selectedRect.Height > 5)
                    {
                        _highlightRect = selectedRect;

                        // 添加到增强型区域
                        if (_captureMode == CaptureMode.Area)
                        {
                            // 确保增强型区域存在
                            if (_enhancedArea == null)
                            {
                                _enhancedArea = new EnhancedSmartArea();
                                _enhancedArea.Type = EnhancedSmartArea.AreaType.RelativeArea;
                                _enhancedArea.AutoDetectChanges = true;
                            }

                            // 清空之前的组件
                            _enhancedArea.Components.Clear();

                            // 添加新的区域矩形
                            _enhancedArea.AddRectangle(selectedRect, _targetForm.ClientSize);
                        }

                        // 更新调整柄和锚点
                        UpdateResizeHandles(_highlightRect);
                        UpdateAnchorPoints();

                        Invalidate();
                    }
                }
                // 处理多选模式
                else if (_captureMode == CaptureMode.Multi)
                {
                    // 先检查是否是特殊项
                    Control parentControl = FindDeepestChildAt(_targetForm, e.Location);
                    object specialItem = GetSpecialItemAtPoint(parentControl, e.Location);

                    if (specialItem != null)
                    {
                        // 处理特殊项的选择
                        HandleSpecialItemSelection(specialItem, parentControl);
                    }
                    else if (parentControl != null)
                    {
                        // 处理普通控件的选择
                        HandleControlSelection(parentControl);
                    }
                }
                // 处理单击操作 - 自动判断是控件捕获
                else if (!_isDragging)
                {
                    CaptureControlAtPoint(e.Location);
                }

                _isDragging = false;
            }
        }

        private void OverlayForm_KeyDown(object sender, KeyEventArgs e)
        {
            // ESC键取消操作
            if (e.KeyCode == Keys.Escape)
            {
                CancelOperation();
                e.Handled = true;
            }
            // A键切换锚点
            else if (e.KeyCode == Keys.A && _mode == GuideDesigner.DesignMode.SmartCapture)
            {
                CycleAnchorPoint();
                e.Handled = true;
            }
            // C键切换组合模式
            else if (e.KeyCode == Keys.C && _mode == GuideDesigner.DesignMode.SmartCapture)
            {
                CycleCombineMode();
                e.Handled = true;
            }
            // M键切换多选模式
            else if (e.KeyCode == Keys.M && _mode == GuideDesigner.DesignMode.SmartCapture)
            {
                SetCaptureMode(_captureMode == CaptureMode.Multi ? CaptureMode.Select : CaptureMode.Multi);
                e.Handled = true;
            }
            // S键切换特殊项目模式
            else if (e.KeyCode == Keys.S && _mode == GuideDesigner.DesignMode.SmartCapture)
            {
                SetCaptureMode(_captureMode == CaptureMode.Special ? CaptureMode.Select : CaptureMode.Special);
                e.Handled = true;
            }
            // 区域选择模式
            else if (e.KeyCode == Keys.R && _mode == GuideDesigner.DesignMode.SmartCapture)
            {
                SetCaptureMode(CaptureMode.Area);
                e.Handled = true;
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取选择矩形
        /// </summary>
        private Rectangle GetSelectionRectangle()
        {
            // 如果_endPoint未设置（为0,0），使用_startPoint作为终点
            // 这种情况通常发生在单击选择时
            if (_endPoint.IsEmpty || (_endPoint.X == 0 && _endPoint.Y == 0))
            {
                _endPoint = _startPoint;
            }

            int x = Math.Min(_startPoint.X, _endPoint.X);
            int y = Math.Min(_startPoint.Y, _endPoint.Y);
            int width = Math.Abs(_startPoint.X - _endPoint.X);
            int height = Math.Abs(_startPoint.Y - _endPoint.Y);

            // 确保至少有最小大小
            if (width < 5) width = 5;
            if (height < 5) height = 5;

            return new Rectangle(x, y, width, height);
        }

        /// <summary>
        /// 应用选择的区域
        /// </summary>
        private void ApplySelectedArea()
        {
            try
            {
                if (_currentStep == null) return;

                // 优先处理多选模式，这种情况使用_selectedControls和_selectedSpecialItems
                if (_captureMode == CaptureMode.Multi &&
                    (_selectedControls.Count > 0 || _selectedSpecialItems.Count > 0))
                {
                    // 重建增强型区域以确保计算正确
                    RebuildEnhancedArea();

                    // 计算组合区域
                    Rectangle combinedRect = _enhancedArea.CalculateActualRect(_targetForm);
                    if (combinedRect.IsEmpty)
                    {
                        MessageBox.Show("无法计算有效区域，请重新选择。", "提示",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    // 显示确认对话框
                    string message = $"已选择{_selectedControls.Count + _selectedSpecialItems.Count}个元素\n" +
                                    $"区域：{combinedRect}\n\n是否确认使用此区域？";

                    if (MessageBox.Show(message, "确认组合区域",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        // 应用多选变更到步骤
                        ApplyChangesToStep(_currentStep);

                        // 重要：重置为区域选择模式，确保下次进入时的默认模式正确
                        _captureMode = CaptureMode.Area;
                        _isMultiSelectMode = false;
                        if (_enhancedArea != null)
                        {
                            _enhancedArea.Type = EnhancedSmartArea.AreaType.RelativeArea;
                        }

                        // 关闭覆盖窗口
                        DialogResult = DialogResult.OK;
                        Close();
                    }

                    return;
                }

                // 其他情况下再考虑_startPoint和_endPoint（如框选模式）
                if (_startPoint != Point.Empty)  // 确保已经设置了起始点
                {
                    // 获取选择的矩形区域
                    Rectangle selRect = GetSelectionRectangle();

                    // 如果选择的区域有效，应用到步骤
                    if (selRect.Width > 0 && selRect.Height > 0)
                    {
                        string message = $"已选择区域：{selRect}\n\n是否确认使用此区域？";

                        if (MessageBox.Show(message, "确认区域",
                            MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                        {
                            // 设置高亮矩形
                            _highlightRect = selRect;

                            // 保存相对位置增强区域
                            if (_useEnhancedMode)
                            {
                                _enhancedArea = new EnhancedSmartArea(selRect, _targetForm.ClientSize);
                                _enhancedArea.Type = EnhancedSmartArea.AreaType.RelativeArea;

                                // 收集邻近控件信息
                                CollectNearbyControls(selRect);

                                // 分析内容结构
                                AnalyzeContentStructure(selRect);

                                // 分析与窗体边缘的关系
                                AnalyzeEdgeRelationships(selRect);
                            }

                            // 应用变更到步骤
                            ApplyChangesToStep(_currentStep);

                            // 关闭窗口
                            DialogResult = DialogResult.OK;
                            Close();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用选择区域时出错：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 捕获指定位置的控件
        /// </summary>
        /// <param name="location">鼠标位置</param>
        private void CaptureControlAtPoint(Point location)
        {
            try
            {
                // 将覆盖层坐标转换为屏幕坐标
                Point screenPoint = this.PointToScreen(location);

                // 获取该点下的窗口句柄
                IntPtr hWnd = WindowFromPoint(screenPoint);

                if (hWnd != IntPtr.Zero)
                {
                    // 尝试找到对应的.NET控件
                    _capturedControl = FindControlFromHandle(hWnd);

                    if (_capturedControl != null)
                    {
                        // 尝试查找特殊UI元素（如ToolStrip子项）
                        _capturedSpecialItem = null; // 重置特殊项
                        if (_capturedControl is ToolStrip ||
                            _capturedControl is TabControl)
                        {
                            // 获取特殊项目
                            _capturedSpecialItem = GetSpecialItemAtPoint(_capturedControl, location);
                        }

                        Rectangle controlRect;
                        string controlName;
                        string controlType;

                        if (_capturedSpecialItem != null)
                        {
                            // 处理特殊项目
                            controlRect = GetSpecialItemRect(_capturedSpecialItem, _capturedControl);

                            // 获取项目名称和类型
                            if (_capturedSpecialItem is ToolStripItem item)
                            {
                                controlName = item.Name;
                                controlType = item.GetType().Name;
                                // 保存特殊项目的完整路径，确保正确引用
                                _currentStep.Ctrl = _capturedControl.Name + "." + item.Name;

                                // 保存至增强型区域
                                if (_enhancedArea != null)
                                {
                                    _enhancedArea.AddToolStripItem(item, _capturedControl, controlRect, _targetForm);
                                }
                            }
                            else if (_capturedSpecialItem is TabPage tabPage)
                            {
                                controlName = tabPage.Name;
                                controlType = tabPage.GetType().Name;
                                // 保存特殊项目的完整路径
                                _currentStep.Ctrl = _capturedControl.Name + "." + tabPage.Name;
                            }
                            else
                            {
                                controlName = _capturedSpecialItem.ToString();
                                controlType = _capturedSpecialItem.GetType().Name;
                                _currentStep.Ctrl = _capturedControl.Name;
                            }
                        }
                        else
                        {
                            // 常规控件处理
                            controlRect = GetControlRectInForm(_capturedControl);
                            controlName = _capturedControl.Name;
                            controlType = _capturedControl.GetType().Name;
                            _currentStep.Ctrl = controlName;

                            // 保存至增强型区域并进行多维度分析
                            if (_enhancedArea != null)
                            {
                                // 1. 基本控件定位
                                _enhancedArea.AddControl(_capturedControl, _targetForm);

                                // 2. 分析控件的视觉特征
                                AnalyzeControlVisualFeatures(_capturedControl);

                                // 3. 分析控件的相对位置
                                if (_capturedControl.Parent != null)
                                {
                                    // 计算在父容器中的相对位置
                                    var parent = _capturedControl.Parent;
                                    float relativeX = parent.Width > 0 ? (float)_capturedControl.Left / parent.Width : 0;
                                    float relativeY = parent.Height > 0 ? (float)_capturedControl.Top / parent.Height : 0;

                                    // 设置参考点
                                    if (relativeX < 0.25f && relativeY < 0.25f)
                                        _enhancedArea.ReferencePoint = EnhancedSmartArea.AnchorPoint.TopLeft;
                                    else if (relativeX > 0.75f && relativeY < 0.25f)
                                        _enhancedArea.ReferencePoint = EnhancedSmartArea.AnchorPoint.TopRight;
                                    else if (relativeX < 0.25f && relativeY > 0.75f)
                                        _enhancedArea.ReferencePoint = EnhancedSmartArea.AnchorPoint.BottomLeft;
                                    else if (relativeX > 0.75f && relativeY > 0.75f)
                                        _enhancedArea.ReferencePoint = EnhancedSmartArea.AnchorPoint.BottomRight;
                                    else if (relativeX < 0.25f)
                                        _enhancedArea.ReferencePoint = EnhancedSmartArea.AnchorPoint.MiddleLeft;
                                    else if (relativeX > 0.75f)
                                        _enhancedArea.ReferencePoint = EnhancedSmartArea.AnchorPoint.MiddleRight;
                                    else if (relativeY < 0.25f)
                                        _enhancedArea.ReferencePoint = EnhancedSmartArea.AnchorPoint.TopCenter;
                                    else if (relativeY > 0.75f)
                                        _enhancedArea.ReferencePoint = EnhancedSmartArea.AnchorPoint.BottomCenter;
                                    else
                                        _enhancedArea.ReferencePoint = EnhancedSmartArea.AnchorPoint.Center;
                                }

                                // 4. 分析邻近控件
                                // 获取控件矩形
                                Rectangle ctrlRect = GetControlRectInForm(_capturedControl);

                                // 定义搜索范围（控件周围的区域）
                                int searchDistance = Math.Max(ctrlRect.Width, ctrlRect.Height);
                                Rectangle searchRect = new Rectangle(
                                    ctrlRect.X - searchDistance,
                                    ctrlRect.Y - searchDistance,
                                    ctrlRect.Width + searchDistance * 2,
                                    ctrlRect.Height + searchDistance * 2
                                );

                                // 收集邻近控件
                                CollectNearbyControls(searchRect);
                            }
                        }

                        // 更新步骤的矩形
                        _currentStep.Rect = controlRect;
                        _highlightRect = controlRect;

                        // 刷新显示
                        this.Invalidate();

                        // 显示确认对话框
                        DialogResult result = MessageBox.Show(
                                    "已捕获控件：" + controlName + "\n类型：" + controlType +
                                "\n区域：" + controlRect.ToString() + "\n\n是否确认使用此控件？",
                                "确认控件",
                            MessageBoxButtons.YesNo,
                            MessageBoxIcon.Question);

                        if (result == DialogResult.Yes)
                        {
                            // 完成捕获，重置状态
                            _mode = GuideDesigner.DesignMode.None;
                            this.BackColor = Color.FromArgb(50, 0, 0, 0);
                            this.Opacity = 0.5;
                            this.Hide();

                            // 确保设计器可见
                            if (_designer != null && !_designer.Visible)
                            {
                                _designer.Show();
                                _designer.BringToFront();
                            }
                        }
                        else
                        {
                            // 用户不满意，重新开始捕获
                            _capturedControl = null;
                            _highlightRect = Rectangle.Empty;
                            this.Invalidate();
                        }
                    }
                    else
                    {
                        MessageBox.Show("无法捕获到有效的控件，请重试。", "提示",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("捕获控件时发生错误: " + ex.Message, "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消当前操作
        /// </summary>
        private void CancelOperation()
        {
            _isDragging = false;
            _highlightRect = Rectangle.Empty;
            _capturedControl = null;
            _mode = GuideDesigner.DesignMode.None;

            this.BackColor = Color.FromArgb(50, 0, 0, 0);
            this.Opacity = 0.5;
            this.Cursor = Cursors.Cross;
            this.Invalidate();
            this.Hide();

            // 确保设计器可见 - 修复ESC键取消后设计器不显示的问题
            if (_designer != null && !_designer.Visible)
            {
                _designer.Show();
                _designer.BringToFront();
            }
        }

        /// <summary>
        /// 根据窗口句柄查找控件
        /// </summary>
        /// <param name="handle">窗口句柄</param>
        /// <returns>找到的控件，未找到则返回null</returns>
        private Control FindControlFromHandle(IntPtr handle)
        {
            try
            {
                // 递归查找所有控件
                Control control = FindControlRecursive(_targetForm, handle);

                // 如果找不到或控件不可见，尝试更多策略
                if (control == null || !control.Visible)
                {
                    // 获取鼠标下的控件尝试附加方法
                    Point cursorPos = Cursor.Position;
                    Control parent = _targetForm;

                    // 转换坐标系
                    Point clientPoint = parent.PointToClient(cursorPos);

                    // 查找点击位置的子控件
                    control = FindDeepestChildAt(parent, clientPoint);
                }

                return control;
            }
            catch (Exception ex)
            {
                // 记录错误但不崩溃
                Console.WriteLine("控件捕获错误: " + ex.Message);
                return null;
            }
        }

        /// <summary>
        /// 查找给定点下最深的子控件
        /// </summary>
        /// <param name="parent">父控件</param>
        /// <param name="clientPoint">客户端坐标点</param>
        /// <returns>找到的最深子控件</returns>
        private Control FindDeepestChildAt(Control parent, Point clientPoint)
        {
            if (parent == null) return null;

            // 转换为父控件的客户端坐标
            Point parentPoint = parent.PointToClient(this.PointToScreen(clientPoint));

            // 检查点是否在父控件内
            if (!parent.ClientRectangle.Contains(parentPoint))
                return null;

            // 特殊处理TabControl，确保我们能获取到当前活动标签页
            if (parent is TabControl tabControl && tabControl.SelectedTab != null)
            {
                // 检查点击位置是否在TabPage内
                Rectangle tabPageRect = tabControl.SelectedTab.Bounds;
                if (tabPageRect.Contains(parentPoint))
                {
                    // 递归找到TabPage内的控件
                    Control childControl = FindDeepestChildAtRecursive(tabControl.SelectedTab,
                        new Point(parentPoint.X - tabPageRect.X, parentPoint.Y - tabPageRect.Y));
                    if (childControl != null)
                        return childControl;
                    return tabControl.SelectedTab; // 返回TabPage
                }

                // 检查是否点击在标签区域
                for (int i = 0; i < tabControl.TabCount; i++)
                {
                    Rectangle tabRect = tabControl.GetTabRect(i);
                    if (tabRect.Contains(parentPoint))
                    {
                        return tabControl; // 返回TabControl，让特殊处理代码识别TabPage
                    }
                }
            }

            // 递归查找子控件
            return FindDeepestChildAtRecursive(parent, parentPoint) ?? parent;
        }

        /// <summary>
        /// 递归查找最深层的子控件
        /// </summary>
        private Control FindDeepestChildAtRecursive(Control parent, Point point)
        {
            // 从后向前遍历，因为通常后面添加的控件在视觉上位于上层
            for (int i = parent.Controls.Count - 1; i >= 0; i--)
            {
                Control child = parent.Controls[i];

                // 跳过不可见或禁用的控件
                if (!child.Visible || !child.Enabled)
                    continue;

                // 转换到子控件坐标
                Point childPoint = new Point(point.X - child.Left, point.Y - child.Top);

                // 检查点是否在控件范围内
                if (childPoint.X >= 0 && childPoint.X < child.Width &&
                    childPoint.Y >= 0 && childPoint.Y < child.Height)
                {
                    // 特殊处理StatusStrip, MenuStrip, ToolStrip
                    if (child is ToolStrip)
                    {
                        return child; // 直接返回这些控件，让特殊处理代码处理其子项
                    }

                    // 继续递归子控件
                    Control deeperChild = FindDeepestChildAtRecursive(child, childPoint);
                    return deeperChild ?? child;
                }
            }

            return null;
        }

        /// <summary>
        /// 递归查找控件
        /// </summary>
        /// <param name="parent">父控件</param>
        /// <param name="handle">窗口句柄</param>
        /// <returns>找到的控件，未找到则返回null</returns>
        private Control FindControlRecursive(Control parent, IntPtr handle)
        {
            if (parent.Handle == handle)
            {
                return parent;
            }

            foreach (Control child in parent.Controls)
            {
                Control found = FindControlRecursive(child, handle);
                if (found != null)
                {
                    return found;
                }
            }

            return null;
        }

        /// <summary>
        /// 获取控件在窗体中的矩形区域
        /// </summary>
        private Rectangle GetControlRectInForm(Control control)
        {
            if (control == null) return Rectangle.Empty;

            try
            {
                // 获取控件在屏幕中的位置和大小
                Point screenLocation = control.Parent.PointToScreen(control.Location);

                // 转换为目标窗体的客户端坐标
                Point clientLocation = _targetForm.PointToClient(screenLocation);

                return new Rectangle(clientLocation, control.Size);
            }
            catch (Exception)
            {
                // 出现异常时返回空矩形
                return Rectangle.Empty;
            }
        }

        /// <summary>
        /// 捕获特殊UI元素
        /// </summary>
        private void CaptureSpecialItemAtPoint(Point location)
        {
            Control control = FindDeepestChildAt(_targetForm, location);
            if (control == null) return;

            // 获取特殊项目
            object specialItem = GetSpecialItemAtPoint(control, location);
            if (specialItem != null)
            {
                _capturedSpecialItem = specialItem;

                // 计算特殊项目的矩形区域
                Rectangle itemRect = GetSpecialItemRect(specialItem, control);
                if (!itemRect.IsEmpty)
                {
                    _highlightRect = itemRect;

                    // 添加到增强型区域
                    _enhancedArea.AddRectangle(itemRect, _targetForm.ClientSize);

                    // 更新锚点
                    UpdateAnchorPoints();

                    Invalidate();
                }
            }
        }

        /// <summary>
        /// 获取鼠标位置下的特殊UI元素
        /// </summary>
        private object GetSpecialItemAtPoint(Control control, Point location)
        {
            if (control == null) return null;

            try
            {
                // 获取鼠标的屏幕坐标
                Point screenPoint = this.PointToScreen(location);

                // 处理ToolStrip
                if (control is ToolStrip strip)
                {
                    Rectangle controlRect = GetControlRectInForm(strip);
                    if (!controlRect.Contains(location)) return null;

                    // 转换为控件坐标
                    Point stripPoint = strip.PointToClient(screenPoint);

                    // 获取特定项目
                    ToolStripItem item = strip.GetItemAt(stripPoint);
                    if (item != null)
                    {
                        return item;
                    }
                    return strip; // 返回整个ToolStrip
                }

                // 处理TabControl
                else if (control is TabControl tabControl)
                {
                    Rectangle controlRect = GetControlRectInForm(tabControl);
                    if (!controlRect.Contains(location)) return null;

                    // 转换为控件坐标系
                    Point tabPoint = tabControl.PointToClient(screenPoint);

                    // 检查是否点击在标签区域
                    for (int i = 0; i < tabControl.TabCount; i++)
                    {
                        Rectangle tabRect = tabControl.GetTabRect(i);
                        if (tabRect.Contains(tabPoint))
                        {
                            return tabControl.TabPages[i];
                        }
                    }

                    return tabControl; // 返回整个TabControl
                }
            }
            catch (Exception)
            {
                // 捕获任何异常，确保不会导致程序崩溃
            }

            return null;
        }

        /// <summary>
        /// 获取特殊项目的矩形区域
        /// </summary>
        /// <param name="specialItem">特殊项目对象</param>
        /// <param name="parentControl">父控件</param>
        /// <returns>计算得到的矩形区域</returns>
        private Rectangle GetSpecialItemRect(object specialItem, Control parentControl)
        {
            if (specialItem == null || parentControl == null)
                return Rectangle.Empty;

            try
            {
                // 处理ToolStripItem
                if (specialItem is ToolStripItem item)
                {
                    ToolStrip parent = item.Owner;
                    if (parent != null)
                    {
                        try
                        {
                            // 获取ToolStrip在屏幕上的绝对位置
                            Point parentScreenLocation = parent.PointToScreen(Point.Empty);

                            // 获取项目在其所有者中的位置
                            Rectangle itemBounds = item.Bounds;

                            // 计算项目在屏幕上的绝对位置
                            Rectangle screenRect = new Rectangle(
                                parentScreenLocation.X + itemBounds.X,
                                parentScreenLocation.Y + itemBounds.Y,
                                Math.Max(itemBounds.Width, 5),  // 确保有最小宽度
                                Math.Max(itemBounds.Height, 5)); // 确保有最小高度

                            // 转换为窗体客户区坐标
                            return _targetForm.RectangleToClient(screenRect);
                        }
                        catch
                        {
                            // 如果无法计算屏幕位置，尝试使用控件的相对位置
                            Rectangle parentRect = GetControlRectInForm(parent);
                            return new Rectangle(
                                parentRect.X + item.Bounds.X,
                                parentRect.Y + item.Bounds.Y,
                                item.Bounds.Width,
                                item.Bounds.Height);
                        }
                    }
                }
                // 其他类型的特殊项目处理可以根据需要添加
            }
            catch
            {
                // 异常处理：返回一个安全的默认值
                return new Rectangle(0, 0, 10, 10);
            }

            return Rectangle.Empty;
        }

        /// <summary>
        /// 绘制区域选择矩形
        /// </summary>
        private void DrawSelectionRectangle(Graphics g, Rectangle rect)
        {
            // 绘制半透明填充
            using (SolidBrush fillBrush = new SolidBrush(Color.FromArgb(30, 0, 120, 215)))
            {
                g.FillRectangle(fillBrush, rect);
            }

            // 绘制醒目的边框
            using (Pen borderPen = new Pen(Color.FromArgb(200, 0, 120, 215), 2))
            {
                g.DrawRectangle(borderPen, rect);
            }

            // 绘制角点标记，增强可见性
            int markerSize = 6;
            using (SolidBrush markerBrush = new SolidBrush(Color.FromArgb(255, 0, 120, 215)))
            {
                // 左上
                g.FillRectangle(markerBrush, rect.Left - markerSize / 2, rect.Top - markerSize / 2, markerSize, markerSize);
                // 右上
                g.FillRectangle(markerBrush, rect.Right - markerSize / 2, rect.Top - markerSize / 2, markerSize, markerSize);
                // 左下
                g.FillRectangle(markerBrush, rect.Left - markerSize / 2, rect.Bottom - markerSize / 2, markerSize, markerSize);
                // 右下
                g.FillRectangle(markerBrush, rect.Right - markerSize / 2, rect.Bottom - markerSize / 2, markerSize, markerSize);
            }

            // 绘制尺寸提示
            string sizeInfo = $"{rect.Width} × {rect.Height}";
            using (Font font = new Font("Arial", 9, FontStyle.Bold))
            using (SolidBrush textBrush = new SolidBrush(Color.White))
            using (SolidBrush bgBrush = new SolidBrush(Color.FromArgb(180, 0, 120, 215)))
            {
                SizeF textSize = g.MeasureString(sizeInfo, font);
                PointF textPos = new PointF(
                    rect.Right - textSize.Width - 5,
                    rect.Bottom + 5);

                // 确保文本不超出窗体边界
                if (textPos.X < 5) textPos.X = 5;
                if (textPos.Y + textSize.Height > this.Height - 5)
                    textPos.Y = rect.Top - textSize.Height - 5;

                // 绘制背景
                g.FillRectangle(bgBrush,
                    textPos.X - 3, textPos.Y - 1,
                    textSize.Width + 6, textSize.Height + 2);

                // 绘制文本
                g.DrawString(sizeInfo, font, textBrush, textPos);
            }
        }

        /// <summary>
        /// 处理特殊项的选择
        /// </summary>
        private void HandleSpecialItemSelection(object specialItem, Control parentControl)
        {
            bool alreadySelected = false;

            // 检查是否已经选择
            foreach (var item in _selectedSpecialItems)
            {
                if (item.Item == specialItem)
                {
                    // 移除已选择的项
                    _selectedSpecialItems.Remove(item);
                    alreadySelected = true;
                    break;
                }
            }

            if (!alreadySelected)
            {
                // 如果是单选模式，先清空当前所有选择
                if (!_isMultiSelectMode)
                {
                    _selectedControls.Clear();
                    _selectedSpecialItems.Clear();
                }

                // 获取特殊项的矩形区域
                Rectangle itemRect = GetSpecialItemRect(specialItem, parentControl);
                if (!itemRect.IsEmpty)
                {
                    // 添加到选择列表
                    _selectedSpecialItems.Add(new SelectedSpecialItem
                    {
                        Item = specialItem,
                        Parent = parentControl,
                        Rect = itemRect
                    });
                }
            }

            // 重建增强型区域
            RebuildEnhancedArea();
        }

        /// <summary>
        /// 处理控件的选择
        /// </summary>
        private void HandleControlSelection(Control control)
        {
            if (_selectedControls.Contains(control))
            {
                // 已经在选中列表中，移除它
                _selectedControls.Remove(control);
            }
            else
            {
                // 不在选中列表中，添加它

                // 如果是单选模式，先清空当前所有选择
                if (!_isMultiSelectMode)
                {
                    _selectedControls.Clear();
                    _selectedSpecialItems.Clear();
                }

                _selectedControls.Add(control);
            }

            // 重建增强型区域
            RebuildEnhancedArea();
        }

        /// <summary>
        /// 重建增强型区域
        /// </summary>
        private void RebuildEnhancedArea()
        {
            // 创建新的增强型区域
            _enhancedArea = new EnhancedSmartArea();

            // 检查是否有选择的控件或特殊项
            bool hasSelection = _selectedControls.Count > 0 || _selectedSpecialItems.Count > 0;

            if (!hasSelection)
            {
                // 没有选择任何内容，清空高亮区域
                _highlightRect = Rectangle.Empty;
                return;
            }

            // 设置为多控件模式
            _enhancedArea.Type = EnhancedSmartArea.AreaType.MultiControl;

            // 1. 先添加普通控件
            foreach (var control in _selectedControls)
            {
                if (control != null)
                {
                    _enhancedArea.AddControl(control, _targetForm);

                    // 如果只有一个控件，应用单控件多维度定位策略
                    if (_selectedControls.Count == 1 && _selectedSpecialItems.Count == 0)
                    {
                        AnalyzeControlVisualFeatures(control);
                    }
                }
            }

            // 2. 再添加特殊项，使用精确计算的矩形
            foreach (var item in _selectedSpecialItems)
            {
                if (item == null || item.Item == null) continue;

                // 对ToolStripItem特殊处理
                if (item.Item is ToolStripItem tsItem && item.Parent != null)
                {
                    // 重新计算位置，不依赖已存储的矩形，确保最新最准确
                    Rectangle itemRect = GetSpecialItemRect(item.Item, item.Parent);
                    _enhancedArea.AddToolStripItem(tsItem, item.Parent, itemRect, _targetForm);
                }
                else if (!item.Rect.IsEmpty)
                {
                    _enhancedArea.AddRectangle(item.Rect, _targetForm.ClientSize);
                }
            }

            // 5. 计算最终区域
            Rectangle combinedRect = _enhancedArea.CalculateActualRect(_targetForm);

            // 6. 更新高亮区域和锚点
            if (!combinedRect.IsEmpty)
            {
                _highlightRect = combinedRect;
                UpdateAnchorPoints();

                // 如果是单个控件或区域选择，应用更多分析
                if (_selectedControls.Count <= 1 && _selectedSpecialItems.Count == 0)
                {
                    // 区域分析
                    CollectNearbyControls(_highlightRect);
                    AnalyzeContentStructure(_highlightRect);
                    AnalyzeEdgeRelationships(_highlightRect);
                }
            }

            // 7. 触发重绘
            Invalidate();
        }

        /// <summary>
        /// 选中的特殊项
        /// </summary>
        private class SelectedSpecialItem
        {
            public object Item { get; set; }
            public Control Parent { get; set; }
            public Rectangle Rect { get; set; }
        }

        /// <summary>
        /// 初始化组件（设计器支持所需的方法）
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // OverlayForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Name = "OverlayForm";
            this.Text = "OverlayForm";
            this.ResumeLayout(false);
        }

        /// <summary>
        /// 应用所有捕获的变更到步骤
        /// </summary>
        /// <param name="step">当前步骤</param>
        public void ApplyChangesToStep(GuideItem step)
        {
            if (step == null) return;

            // 处理多选模式下的组合控件 (优先处理，这样不会被其他单选规则覆盖)
            if (_selectedControls.Count > 0 || _selectedSpecialItems.Count > 0)
            {
                // 重建更新增强区域计算
                RebuildEnhancedArea();

                // 确保增强区域的类型正确设置
                if (_captureMode == CaptureMode.Multi)
                {
                    _enhancedArea.Type = EnhancedSmartArea.AreaType.MultiControl;
                }

                // 使用计算出的组合区域，不依赖_startPoint和_endPoint
                Rectangle combinedRect = _enhancedArea.CalculateActualRect(_targetForm);
                if (!combinedRect.IsEmpty)
                {
                    step.Rect = combinedRect;

                    // 更新EnhancedArea - 创建深度拷贝
                    step.EnhancedArea = CloneEnhancedArea(_enhancedArea);
                }

                return; // 处理完多选控件后直接返回，不再走后续单选逻辑
            }

            // 保存捕获的控件信息和特殊项信息
            if (_capturedControl != null)
            {
                // 处理特殊控件项 - ToolStripItem
                if (_capturedSpecialItem != null && _capturedSpecialItem is ToolStripItem item)
                {
                    // 保存完整路径，包括父控件名称和子项名称
                    step.Ctrl = _capturedControl.Name + "." + item.Name;

                    // 获取特殊项的矩形区域
                    Rectangle itemRect = GetSpecialItemRect(_capturedSpecialItem, _capturedControl);
                    if (!itemRect.IsEmpty)
                    {
                        step.Rect = itemRect;
                    }
                    else
                    {
                        // 如果获取特殊项矩形失败，使用父控件矩形
                        step.Rect = GetControlRectInForm(_capturedControl);
                    }
                }
                // 处理普通控件
                else
                {
                    step.Ctrl = _capturedControl.Name;
                    Rectangle controlRect = GetControlRectInForm(_capturedControl);
                    step.Rect = controlRect;
                }
            }

            // 保存选择的高亮矩形
            if (!_highlightRect.IsEmpty)
            {
                step.Rect = _highlightRect;

                // 确保Area类型属性设置正确
                if (_enhancedArea != null && _captureMode == CaptureMode.Area)
                {
                    _enhancedArea.Type = EnhancedSmartArea.AreaType.RelativeArea;
                }
            }

            // 更新EnhancedArea
            if (_enhancedArea != null)
            {
                step.EnhancedArea = CloneEnhancedArea(_enhancedArea);
            }
        }

        /// <summary>
        /// 深度克隆EnhancedSmartArea对象
        /// </summary>
        private EnhancedSmartArea CloneEnhancedArea(EnhancedSmartArea original)
        {
            if (original == null) return null;

            EnhancedSmartArea clone = new EnhancedSmartArea();
            clone.Type = original.Type;
            clone.OriginalRect = original.OriginalRect;
            clone.RelativeRect = original.RelativeRect;
            clone.SizeType = original.SizeType;
            clone.PositionType = original.PositionType;
            clone.FixedSize = original.FixedSize;
            clone.RelativeSize = original.RelativeSize;
            clone.ReferencePoint = original.ReferencePoint;
            clone.Offset = original.Offset;
            clone.ParentRect = original.ParentRect;
            clone.AutoDetectChanges = true;

            // 深度复制集合
            clone.Anchors = new List<EnhancedSmartArea.ControlAnchor>(original.Anchors);
            clone.Components = new List<EnhancedSmartArea.AreaComponent>(original.Components);
            clone.NestedRects = new List<Rectangle>(original.NestedRects);

            return clone;
        }

        /// <summary>
        /// 收集邻近控件信息
        /// </summary>
        /// <param name="rect">选中区域</param>
        private void CollectNearbyControls(Rectangle rect)
        {
            _nearbyControls.Clear();
            if (rect.IsEmpty) return;

            // 扩大搜索区域（原区域外10%范围）
            int expandedSize = Math.Max(rect.Width, rect.Height) / 10;
            Rectangle expandedRect = new Rectangle(
                rect.X - expandedSize,
                rect.Y - expandedSize,
                rect.Width + expandedSize * 2,
                rect.Height + expandedSize * 2
            );

            // 递归寻找邻近控件
            FindNearbyControlsRecursive(_targetForm, expandedRect);
        }

        /// <summary>
        /// 递归寻找邻近控件
        /// </summary>
        private void FindNearbyControlsRecursive(Control parent, Rectangle searchRect)
        {
            if (parent == null) return;

            foreach (Control control in parent.Controls)
            {
                if (!control.Visible) continue;

                // 获取控件在窗体上的矩形区域
                Rectangle controlRect = GetControlRectInForm(control);

                // 检查是否在扩展搜索区域内但不完全被选中区域包含
                if (controlRect.IntersectsWith(searchRect) && !_highlightRect.Contains(controlRect))
                {
                    _nearbyControls.Add(control);

                    // 记录控件相对于选区的位置关系
                    RecordControlRelationship(control, controlRect);
                }

                // 递归检查子控件
                FindNearbyControlsRecursive(control, searchRect);
            }
        }

        /// <summary>
        /// 记录控件与选区的位置关系
        /// </summary>
        private void RecordControlRelationship(Control control, Rectangle controlRect)
        {
            // 计算控件中心点与选区中心点的相对位置
            Point controlCenter = new Point(controlRect.X + controlRect.Width / 2, controlRect.Y + controlRect.Height / 2);
            Point selectionCenter = new Point(_highlightRect.X + _highlightRect.Width / 2, _highlightRect.Y + _highlightRect.Height / 2);

            // 计算相对距离和角度
            double distance = Math.Sqrt(Math.Pow(controlCenter.X - selectionCenter.X, 2) + Math.Pow(controlCenter.Y - selectionCenter.Y, 2));
            double angle = Math.Atan2(controlCenter.Y - selectionCenter.Y, controlCenter.X - selectionCenter.X) * 180 / Math.PI;

            // 存储在增强型区域中
            if (_enhancedArea != null)
            {
                ControlAnchor anchor = new ControlAnchor
                {
                    ControlId = control.Name,
                    ControlPath = GetControlPath(control),
                    OriginalRect = controlRect,
                    Weight = 0.5f // 设置为辅助控件的权重
                };

                // 根据相对位置设置锚点
                if (angle >= -45 && angle < 45) // 右
                    anchor.AnchorPoint = EnhancedSmartArea.AnchorPoint.MiddleLeft;
                else if (angle >= 45 && angle < 135) // 下
                    anchor.AnchorPoint = EnhancedSmartArea.AnchorPoint.TopCenter;
                else if ((angle >= 135 && angle <= 180) || (angle >= -180 && angle < -135)) // 左
                    anchor.AnchorPoint = EnhancedSmartArea.AnchorPoint.MiddleRight;
                else // 上
                    anchor.AnchorPoint = EnhancedSmartArea.AnchorPoint.BottomCenter;

                // 添加为辅助锚点
                _enhancedArea.Anchors.Add(anchor);
            }
        }

        /// <summary>
        /// 分析内容结构
        /// </summary>
        private void AnalyzeContentStructure(Rectangle rect)
        {
            _contentStructureInfo.Clear();
            if (rect.IsEmpty) return;

            // 将区域分成3x3网格
            int gridWidth = rect.Width / 3;
            int gridHeight = rect.Height / 3;

            // 分析每个网格中的内容
            for (int y = 0; y < 3; y++)
            {
                for (int x = 0; x < 3; x++)
                {
                    Rectangle gridRect = new Rectangle(
                        rect.X + x * gridWidth,
                        rect.Y + y * gridHeight,
                        gridWidth,
                        gridHeight
                    );

                    // 计算此网格中的内容密度
                    float density = CalculateContentDensity(gridRect);

                    // 存储网格内容信息
                    _contentStructureInfo[$"Grid_{x}_{y}"] = density;
                }
            }

            // 存储整体结构特征
            if (_enhancedArea != null)
            {
                // 更新增强型区域的适应内容属性
                _enhancedArea.AdaptToContent = true;
            }
        }

        /// <summary>
        /// 计算区域内容密度
        /// </summary>
        private float CalculateContentDensity(Rectangle gridRect)
        {
            // 计算区域内的控件数量和覆盖率
            int controlCount = 0;
            float coverageArea = 0;

            // 递归检查控件
            CalculateDensityRecursive(_targetForm, gridRect, ref controlCount, ref coverageArea);

            // 计算密度值 (0-1)
            float totalArea = gridRect.Width * gridRect.Height;
            return totalArea > 0 ? Math.Min(1.0f, coverageArea / totalArea) : 0;
        }

        /// <summary>
        /// 递归计算内容密度
        /// </summary>
        private void CalculateDensityRecursive(Control parent, Rectangle gridRect, ref int controlCount, ref float coverageArea)
        {
            if (parent == null) return;

            foreach (Control control in parent.Controls)
            {
                if (!control.Visible) continue;

                // 获取控件在窗体上的矩形区域
                Rectangle ctrlRect = GetControlRectInForm(control);

                // 计算与网格的交集
                Rectangle intersection = Rectangle.Intersect(ctrlRect, gridRect);
                if (!intersection.IsEmpty)
                {
                    controlCount++;
                    coverageArea += intersection.Width * intersection.Height;
                }

                // 递归检查子控件
                CalculateDensityRecursive(control, gridRect, ref controlCount, ref coverageArea);
            }
        }

        /// <summary>
        /// 分析与窗体边缘的关系
        /// </summary>
        private void AnalyzeEdgeRelationships(Rectangle rect)
        {
            if (rect.IsEmpty || _targetForm == null) return;

            _edgeRelationships.Clear();

            // 获取窗体客户区尺寸
            Size clientSize = _targetForm.ClientSize;

            // 计算与各边缘的距离和比例
            float leftDistance = rect.Left;
            float rightDistance = clientSize.Width - rect.Right;
            float topDistance = rect.Top;
            float bottomDistance = clientSize.Height - rect.Bottom;

            // 计算相对距离（占窗体尺寸的百分比）
            float leftRatio = (float)leftDistance / clientSize.Width;
            float rightRatio = (float)rightDistance / clientSize.Width;
            float topRatio = (float)topDistance / clientSize.Height;
            float bottomRatio = (float)bottomDistance / clientSize.Height;

            // 存储边缘关系信息
            _edgeRelationships["LeftRatio"] = leftRatio;
            _edgeRelationships["RightRatio"] = rightRatio;
            _edgeRelationships["TopRatio"] = topRatio;
            _edgeRelationships["BottomRatio"] = bottomRatio;

            // 判断是否靠近边缘
            bool nearLeft = leftRatio <= _edgeThreshold;
            bool nearRight = rightRatio <= _edgeThreshold;
            bool nearTop = topRatio <= _edgeThreshold;
            bool nearBottom = bottomRatio <= _edgeThreshold;

            if (_enhancedArea != null)
            {
                // 更新增强型区域的相对矩形
                _enhancedArea.RelativeRect = new RectangleF(
                    (float)rect.X / clientSize.Width,
                    (float)rect.Y / clientSize.Height,
                    (float)rect.Width / clientSize.Width,
                    (float)rect.Height / clientSize.Height
                );

                // 存储边缘关系信息
                _enhancedArea.Components.Clear();

                // 添加主区域组件
                AreaComponent mainComponent = new AreaComponent
                {
                    Type = EnhancedSmartArea.AreaType.RelativeArea,
                    RelativeRect = _enhancedArea.RelativeRect,
                    AbsoluteRect = rect
                };
                _enhancedArea.Components.Add(mainComponent);

                // 如果靠近边缘，添加边缘锚点
                if (nearLeft || nearRight || nearTop || nearBottom)
                {
                    if (nearLeft && nearTop) // 左上角
                        _enhancedArea.ReferencePoint = EnhancedSmartArea.AnchorPoint.TopLeft;
                    else if (nearRight && nearTop) // 右上角
                        _enhancedArea.ReferencePoint = EnhancedSmartArea.AnchorPoint.TopRight;
                    else if (nearLeft && nearBottom) // 左下角
                        _enhancedArea.ReferencePoint = EnhancedSmartArea.AnchorPoint.BottomLeft;
                    else if (nearRight && nearBottom) // 右下角
                        _enhancedArea.ReferencePoint = EnhancedSmartArea.AnchorPoint.BottomRight;
                    else if (nearLeft) // 左边
                        _enhancedArea.ReferencePoint = EnhancedSmartArea.AnchorPoint.MiddleLeft;
                    else if (nearRight) // 右边
                        _enhancedArea.ReferencePoint = EnhancedSmartArea.AnchorPoint.MiddleRight;
                    else if (nearTop) // 上边
                        _enhancedArea.ReferencePoint = EnhancedSmartArea.AnchorPoint.TopCenter;
                    else if (nearBottom) // 下边
                        _enhancedArea.ReferencePoint = EnhancedSmartArea.AnchorPoint.BottomCenter;
                }
                else
                {
                    // 不靠近边缘，使用中心点
                    _enhancedArea.ReferencePoint = EnhancedSmartArea.AnchorPoint.Center;
                }
            }
        }

        #endregion

        /// <summary>
        /// 分析控件的视觉特征
        /// </summary>
        /// <param name="control">要分析的控件</param>
        private void AnalyzeControlVisualFeatures(Control control)
        {
            if (control == null || _enhancedArea == null) return;

            // 获取控件的视觉特征
            Dictionary<string, object> visualFeatures = new Dictionary<string, object>();

            // 1. 基本属性
            visualFeatures["BackColor"] = control.BackColor;
            visualFeatures["ForeColor"] = control.ForeColor;
            visualFeatures["Font"] = control.Font.Name;
            visualFeatures["FontSize"] = control.Font.Size;
            visualFeatures["IsBold"] = control.Font.Bold;
            visualFeatures["IsItalic"] = control.Font.Italic;

            // 2. 尺寸和位置特征
            Rectangle controlRect = GetControlRectInForm(control);
            visualFeatures["AspectRatio"] = controlRect.Width > 0 ? (float)controlRect.Height / controlRect.Width : 0;
            visualFeatures["Area"] = controlRect.Width * controlRect.Height;

            // 3. 边框特征（仅适用于部分控件）
            if (control is Button btn)
            {
                visualFeatures["FlatStyle"] = btn.FlatStyle;
                visualFeatures["FlatAppearanceBorderSize"] = btn.FlatAppearance.BorderSize;
            }

            // 4. 文本内容特征
            string controlText = control.Text;
            if (!string.IsNullOrEmpty(controlText))
            {
                visualFeatures["HasText"] = true;
                visualFeatures["TextLength"] = controlText.Length;
                visualFeatures["TextFirstChar"] = controlText.Length > 0 ? controlText[0].ToString() : "";
                visualFeatures["TextLastChar"] = controlText.Length > 0 ? controlText[controlText.Length - 1].ToString() : "";
            }
            else
            {
                visualFeatures["HasText"] = false;
            }

            // 5. 图像特征（仅适用于部分控件）
            if (control is PictureBox pb)
            {
                visualFeatures["HasImage"] = pb.Image != null;
                if (pb.Image != null)
                {
                    visualFeatures["ImageWidth"] = pb.Image.Width;
                    visualFeatures["ImageHeight"] = pb.Image.Height;
                }
            }

            // 将特征信息保存到增强型区域的特定组件中
            AreaComponent featureComponent = new AreaComponent
            {
                Id = "VisualFeatures_" + control.Name,
                Type = EnhancedSmartArea.AreaType.CustomArea
            };

            // 将特征信息存储为自定义属性
            // 注意：这里简化处理，实际中可能需要将复杂对象序列化
            _enhancedArea.Components.Add(featureComponent);
        }

        /// <summary>
        /// 获取控件的完整路径
        /// </summary>
        /// <param name="control">要获取路径的控件</param>
        /// <returns>形如"Parent.Child.GrandChild"的控件路径</returns>
        private string GetControlPath(Control control)
        {
            if (control == null) return string.Empty;

            // 构建控件路径
            List<string> path = new List<string>();
            path.Add(control.Name);

            // 向上遍历父控件
            Control parent = control.Parent;
            while (parent != null && parent != _targetForm)
            {
                if (!string.IsNullOrEmpty(parent.Name))
                {
                    path.Insert(0, parent.Name);
                }
                parent = parent.Parent;
            }

            // 组合路径
            return string.Join(".", path);
        }
    }
}