﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FormTool : Form
    {
        private static readonly int frameWidth = 250; //背景宽
        private static readonly int frameHeight = 100; //背景高

        private int frameX = Screen.PrimaryScreen.WorkingArea.Size.Width - frameWidth;
        private int frameY = frameHeight;

        private bool _haveHandle;

        //记录鼠标按键是否按下
        private bool _isMouseDown;
        private Point oldPoint = new Point(0, 0);

        public FormTool()
        {
            InitializeComponent();
            var baseStyle = 524416 | WS_EX_NOACTIVATE | WS_EX_TOPMOST;
            SetWindowLong(Handle, GWL_EXSTYLE, new IntPtr(baseStyle));
        }

        private bool isAuto = true;
        private Bitmap frmBitmap;

        private void FormHome_Load(object sender, EventArgs e)
        {
            SetLocation();
            RefreshImage();
            isAuto = false;
            MouseEnter += FormTool_MouseEnter;
            MouseLeave += FormTool_MouseLeave;
        }

        private void FormHome_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                oldPoint = e.Location;
                _isMouseDown = true;
            }
            else if (e.Button == MouseButtons.Right)
            {
                this.ForceActivate();
                ContextMenuStrip?.Show(this, e.Location);
            }
        }

        private void FormHome_MouseMove(object sender, MouseEventArgs e)
        {
            if (_isMouseDown)
            {
                this.Location = new Point(Left + e.X - oldPoint.X, Top + e.Y - oldPoint.Y);
            }
        }

        private void FormHome_MouseUp(object sender, MouseEventArgs e)
        {
            if (_isMouseDown) CommonSetting.SetValue("工具栏位置", string.Format("{0},{1}", Left, Top));
            _isMouseDown = false;
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            e.Cancel = true;
            base.OnClosing(e);
            _haveHandle = false;
        }

        public void VisibleChange()
        {
            Visible = !Visible;
        }

        protected override void OnHandleCreated(EventArgs e)
        {
            base.OnHandleCreated(e);
            _haveHandle = true;
        }

        #region Native Methods

        private const int WS_EX_TOPMOST = 0x00000008;
        private const int WS_EX_NOACTIVATE = 0x08000000;
        private const int GWL_EXSTYLE = -20;

        public static IntPtr SetWindowLong(IntPtr hWnd, int nIndex, IntPtr dwNewLong)
        {
            return Environment.Is64BitProcess
                ? SetWindowLong64(hWnd, nIndex, dwNewLong)
                : SetWindowLong32(hWnd, nIndex, dwNewLong);
        }

        [DllImport("user32.dll", EntryPoint = "SetWindowLong")]
        private static extern IntPtr SetWindowLong32(IntPtr hWnd, int nIndex, IntPtr dwNewLong);

        [DllImport("user32.dll", EntryPoint = "SetWindowLongPtr")]
        private static extern IntPtr SetWindowLong64(IntPtr hWnd, int nIndex, IntPtr dwNewLong);

        #endregion

        public Bitmap GetImage(Size size, string imgStr, bool isCircle, bool isShadow, decimal shadowWidth, bool isFocused)
        {
            var image = CommonSetting.Get工具栏图标(size, imgStr, isCircle, isShadow, shadowWidth, isFocused);
            return image;
        }

        public void RefreshImage(Bitmap image = null, bool isFocus = false)
        {
            if (image == null)
            {
                image = GetImage(CommonSetting.GetStrSize(CommonSetting.工具栏图标尺寸), CommonSetting.工具栏图片, CommonSetting.圆形图标, CommonSetting.图像阴影效果, CommonSetting.工具栏阴影宽度, isFocus);
            }

            if (Equals(frmBitmap, image))
            {
                return;
            }

            SetBits(image);
            if (frmBitmap == null)
            {
                this.Location = new Point(frameX, frameY);
            }
            else
            {
                // 计算偏移位置
                if (!_isMouseDown && !Equals(image.Size, frmBitmap.Size))
                {
                    Location = new Point(this.Left + (frmBitmap.Width - image.Width) / 2, this.Top + (frmBitmap.Height - image.Height) / 2);
                }
            }
            frmBitmap = image;
            Invalidate(true);
        }

        private void SetLocation()
        {
            var objValue = CommonSetting.GetValue<string>(null, "工具栏位置");
            if (string.IsNullOrEmpty(objValue) || !objValue.Contains(",")) return;
            var locations = objValue.Split(new[] { "," }, StringSplitOptions.RemoveEmptyEntries);
            if (locations?.Length != 2) return;
            var left = BoxUtil.GetInt32FromObject(locations[0]);
            if (left > 0 && left <= Screen.PrimaryScreen.WorkingArea.Size.Width - 30) frameX = left;
            var top = BoxUtil.GetInt32FromObject(locations[1]);
            if (top > 0 && top <= Screen.PrimaryScreen.WorkingArea.Size.Height - 30) frameY = top;
        }

        public void SetBits(Bitmap bitmap)
        {
            if (!_haveHandle) return;

            if (!Image.IsCanonicalPixelFormat(bitmap.PixelFormat) || !Image.IsAlphaPixelFormat(bitmap.PixelFormat))
                throw new ApplicationException("The picture must be 32bit picture with alpha channel.");

            var oldBits = IntPtr.Zero;
            var screenDc = Win32.GetDC(IntPtr.Zero);
            var hBitmap = IntPtr.Zero;
            var memDc = Win32.CreateCompatibleDC(screenDc);

            try
            {
                var topLoc = new Win32.Point(Left, Top);
                var bitMapSize = new Win32.Size(bitmap.Width, bitmap.Height);
                var blendFunc = new Win32.BLENDFUNCTION();
                var srcLoc = new Win32.Point(0, 0);

                hBitmap = bitmap.GetHbitmap(Color.FromArgb(0));
                oldBits = Win32.SelectObject(memDc, hBitmap);

                blendFunc.BlendOp = Win32.AC_SRC_OVER;
                blendFunc.SourceConstantAlpha = 255;
                blendFunc.AlphaFormat = Win32.AC_SRC_ALPHA;
                blendFunc.BlendFlags = 0;

                Win32.UpdateLayeredWindow(Handle, screenDc, ref topLoc, ref bitMapSize, memDc, ref srcLoc, 0,
                    ref blendFunc, Win32.ULW_ALPHA);
            }
            finally
            {
                if (hBitmap != IntPtr.Zero)
                {
                    Win32.SelectObject(memDc, oldBits);
                    Win32.DeleteObject(hBitmap);
                }

                Win32.ReleaseDC(IntPtr.Zero, screenDc);
                Win32.DeleteDC(memDc);
            }
        }

        public void SetDragDrop()
        {
            this.ControlUseDrop();
        }

        private void FormTool_MouseLeave(object sender, EventArgs e)
        {
            RefreshImage(null, false);
        }

        private void FormTool_MouseEnter(object sender, EventArgs e)
        {
            RefreshImage(null, true);
        }
    }
}