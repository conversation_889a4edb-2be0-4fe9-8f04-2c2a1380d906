﻿using System.Reflection;
using System.Runtime.InteropServices;

// 有关程序集的一般信息由以下
// 控制。更改这些特性值可修改
// 与程序集关联的信息。
[assembly: AssemblyTitle("OCR助手本地识别服务")]
[assembly: AssemblyDescription("OCR助手本地识别服务(2023-11-11)")]
[assembly: AssemblyProduct("OCR助手本地识别服务")]
[assembly: AssemblyCopyright("版权 © 2019-2023 SmartOldfish")]
// 将 ComVisible 设置为 false 会使此程序集中的类型
//对 COM 组件不可见。如果需要从 COM 访问此程序集中的类型
//请将此类型的 ComVisible 特性设置为 true。
[assembly: ComVisible(false)]

// 如果此项目向 COM 公开，则下列 GUID 用于类型库的 ID
[assembly: Guid("1afcd0cb-5a12-4e2d-a6c7-af4891aec814")]
[assembly: AssemblyVersion("1.5.1")]
[assembly: AssemblyFileVersion("1.5.1")]
