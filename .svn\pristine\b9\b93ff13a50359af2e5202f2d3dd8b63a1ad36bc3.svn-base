using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolMosaic : ToolObject
    {
        private DrawMosaic drawMosaic;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.current_ToolType = drawArea.ActiveTool;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else
            {
                drawMosaic = new DrawMosaic(e.X, e.Y, 1, 1)
                {
                    BackgroundImageEx = drawArea.BackgroundImageEx
                };
                AddNewObject(drawArea, drawMosaic);
            }
        }

        protected new void AddNewObject(DrawArea drawArea, DrawObject o)
        {
            drawArea.GraphicsList.UnselectAll();
            drawArea.GraphicsList.Add(o);
            drawArea.Capture = true;
            o.Selected = true;
            drawArea.Refresh();
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left)
            {
                if (drawMosaic == null)
                {
                    drawArea.ActiveTool = DrawToolType.Text;
                    return;
                }

                drawMosaic.IsSelected = true;
                var obj = drawMosaic;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    drawMosaic.MoveHandleTo(e.Location, 5, drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawMosaic != null)
            {
                StaticValue.current_Rectangle = drawMosaic.Rectangle;
                drawMosaic.IsCache = true;
                if (!drawMosaic.Rectangle.IsLimt())
                {
                    drawArea.GraphicsList.RemoveAt(0);
                    return;
                }

                var obj = drawMosaic;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    drawMosaic.Normalize();
                    drawArea.AddCommandToHistory(new CommandAdd(drawMosaic));
                }
            }
        }
    }
}