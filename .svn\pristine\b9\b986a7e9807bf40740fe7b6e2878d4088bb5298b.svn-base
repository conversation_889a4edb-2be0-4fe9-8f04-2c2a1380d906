﻿#region License Information (GPL v3)

/*
    ShareX - A program that allows you to take screenshots and share any file type
    Copyright (c) 2007-2021 ShareX Team

    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License
    as published by the Free Software Foundation; either version 2
    of the License, or (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.

    Optionally you can also view the license at <http://www.gnu.org/licenses/>.
*/

#endregion License Information (GPL v3)

using OCRTools;
using OCRTools.Common;
using System;
using System.Drawing;
using System.Text;

namespace ShareX.ScreenCaptureLib
{
    public class Screenshot
    {
        public static bool CaptureClientArea { get; set; } = false;
        public static bool RemoveOutsideScreenArea { get; set; } = true;
        public static bool AutoHideTaskbar { get; set; } = false;

        public static string GetWorkAreaInfo()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine("屏幕分辨率:");
            var realBounds = PrimaryScreen.GetAllRectangle();
            sb.AppendLine(string.Format("最大尺寸:{0}*{1}", realBounds.Size.Width, realBounds.Size.Height));
            var screenBounds = NativeMethods.GetScreenBounds();
            sb.AppendLine(string.Format("工作区:X:{0},Y:{1} 尺寸:{2}*{3}", screenBounds.X, screenBounds.Y, screenBounds.Size.Width, screenBounds.Size.Height));
            sb.AppendLine(string.Format("缩放系数:{0}", PrimaryScreen.ScreenScalingFactor.ToString("F2")));
            sb.AppendLine("确认以上信息是否正确。");
            sb.AppendLine("您可以补充信息，以帮助我们快速定位问题！");
            sb.AppendLine("如：图片黑屏/拉伸不正常");
            sb.AppendLine("双屏分辨率及缩放设置");
            return sb.ToString();
        }

        public static Rectangle GetWorkAreaRectangle()
        {
            //var screenBounds = NativeMethods.GetScreenBounds();
            var realBounds = PrimaryScreen.GetAllRectangle();
            //PrimaryScreen.ScreenScalingFactor = realBounds.Width * 1.0f / screenBounds.Width;
            PrimaryScreen.ScreenScalingFactor = PrimaryScreen.scaling();
            return realBounds;
        }

        public static Bitmap CaptureRectangle(Rectangle srcRect, Rectangle destRect)
        {
            return CaptureRectangleNative(srcRect, destRect);
        }

        public static Bitmap CaptureFullscreen(ref Rectangle srcRect, bool isNeedScal = false)
        {
            if (srcRect.IsEmpty)
                srcRect = GetWorkAreaRectangle();
            var destRect = srcRect;
            if (isNeedScal && PrimaryScreen.ScreenScalingFactor > 1)
            {
                destRect = NativeMethods.GetScreenBounds();
            }
            var bitMap = CaptureRectangle(srcRect, destRect);
            srcRect = destRect;
            return bitMap;
        }

        public static Bitmap CaptureWindow(IntPtr handle, ref Rectangle rect)
        {
            if (handle.ToInt32() > 0)
            {
                rect = handle.GetRectangle(!CaptureClientArea);

                var isTaskbarHide = false;
                try
                {
                    if (AutoHideTaskbar) isTaskbarHide = NativeMethods.SetTaskbarVisibilityIfIntersect(false, rect);

                    return CaptureRectangle(rect, rect);
                }
                finally
                {
                    if (isTaskbarHide) NativeMethods.SetTaskbarVisibility(true);
                }
            }

            return null;
        }

        public static Bitmap CaptureActiveWindow(ref Rectangle rect)
        {
            var handle = NativeMethods.GetForegroundWindow();

            return CaptureWindow(handle, ref rect);
        }

        public static Bitmap CaptureActiveMonitor(ref Rectangle rect)
        {
            rect = NativeMethods.GetActiveScreenBounds();

            return CaptureRectangle(rect, rect);
        }

        private static Bitmap CaptureRectangleNative2(Rectangle srcRect)
        {
            var bmp = new Bitmap(srcRect.Width, srcRect.Height);
            try
            {
                IntPtr hwnd = NativeMethods.GetDesktopWindow();
                IntPtr hdc = NativeMethods.GetDC(hwnd);
                using (Graphics g = Graphics.FromHdc(hdc))
                {
                    g.DrawImage(bmp, new Point(0, 0));
                }
                NativeMethods.ReleaseDC(hwnd, hdc);
            }
            catch { }
            return bmp;
        }

        private static Bitmap CaptureRectangleNative(Rectangle srcRect, Rectangle destRect)
        {
            if (srcRect.Size.IsEmpty) return null;

            var handle = NativeMethods.GetDesktopWindow();

            IntPtr hdcSrc = NativeMethods.GetWindowDC(handle);
            IntPtr hdcDest = NativeMethods.CreateCompatibleDC(hdcSrc);
            IntPtr hBitmap = NativeMethods.CreateCompatibleBitmap(hdcSrc, srcRect.Width, srcRect.Height);
            IntPtr hOld = NativeMethods.SelectObject(hdcDest, hBitmap);
            if (srcRect.Size.Equals(destRect.Size))
            {
                NativeMethods.BitBlt(hdcDest, 0, 0, srcRect.Width, srcRect.Height, hdcSrc, srcRect.X, srcRect.Y, CopyPixelOperation.SourceCopy | CopyPixelOperation.CaptureBlt);
            }
            else
            {
                NativeMethods.SetStretchBltMode(hdcDest, 4);
                NativeMethods.StretchBlt(hdcDest, destRect.X, destRect.Y, destRect.Width, destRect.Height
                    , hdcSrc, srcRect.X, srcRect.Y, srcRect.Width, srcRect.Height,
                    CopyPixelOperation.SourceCopy | CopyPixelOperation.CaptureBlt);
            }

            NativeMethods.SelectObject(hdcDest, hOld);
            NativeMethods.DeleteDC(hdcDest);
            NativeMethods.ReleaseDC(handle, hdcSrc);
            var bmp = Image.FromHbitmap(hBitmap);
            NativeMethods.DeleteObject(hBitmap);
            return bmp;
        }

        //private Bitmap CaptureRectangleManaged(Rectangle rect)
        //{
        //    if (rect.Width == 0 || rect.Height == 0)
        //    {
        //        return null;
        //    }

        //    Bitmap bmp = new Bitmap(rect.Width, rect.Height, PixelFormat.Format32bppArgb);

        //    using (Graphics g = Graphics.FromImage(bmp))
        //    {
        //        g.SetHighQuality();
        //        // Managed can't use SourceCopy | CaptureBlt because of .NET bug
        //        g.CopyFromScreen(rect.Location, Point.Empty, rect.Size, CopyPixelOperation.SourceCopy);
        //    }

        //    return bmp;
        //}
    }
}