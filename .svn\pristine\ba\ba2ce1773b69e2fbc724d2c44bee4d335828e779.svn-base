﻿using ImageLib;
using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Language;
using OCRTools.Properties;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class UcContent : UserControl
    {
        private SpiltMode _currentSpiltModel;

        private bool _isShowOldContent;

        private ContextMenuStrip _menuStrip;

        /// <summary>
        /// true：尽量缩放到能显示所有
        /// false:尽量看到更大更清楚
        /// </summary>
        public bool IsViewMore { get { return imageBox?.IsViewMore ?? false; } set { if (imageBox != null) imageBox.IsViewMore = value; } }

        internal UcContent()
        {
            SetStyle(
                ControlStyles.UserPaint | ControlStyles.DoubleBuffer | ControlStyles.OptimizedDoubleBuffer |
                ControlStyles.AllPaintingInWmPaint, true);
            InitializeComponent();
            InitBackColorDropDown();
            InitEmptyPanel();
            CommonMethod.EnableDoubleBuffering(this);

            dgContent.IsShowSequence = true;
            txtContent.LanguageOption = RichTextBoxLanguageOptions.UIFonts;
            txtContent.AllowDrop = true;
            txtContent.AutoWordSelection = false;

            wbContent.AllowWebBrowserDrop = true;
            wbContent.ScriptErrorsSuppressed = true; //禁用错误脚本提示
            wbContent.IsWebBrowserContextMenuEnabled = true; //禁用右键菜单
            wbContent.WebBrowserShortcutsEnabled = false; //禁用快捷键
            wbContent.Navigating += WbContent_Navigating;

            imageBox.Parent = this;
            imageBox.Dock = DockStyle.Fill;
            imageBox.ZoomChanged += ImageBoxZoomChanged;
            imageBox.SizeChanged += ImageBoxSizeChanged;

            toolResize.Parent = this;
            txtContent.TextChanged += TxtContent_TextChanged;
            ParentChanged += UcContent_ParentChanged;
            txtContent.LinkClicked += TxtContent_LinkClicked;
            SizeChanged += UcContent_SizeChanged;
        }

        private void UcContent_SizeChanged(object sender, EventArgs e)
        {
            if (Equals(tsmPicOrigin.ToolTipText, "原始尺寸".CurrentText()))
            {
                imageBox.ZoomToFit();
            }
        }

        private const string TraceInfo = "查看链路信息";

        private void TxtContent_LinkClicked(object sender, LinkClickedEventArgs e)
        {
            if (e.LinkText?.StartsWith(TraceInfo) == true)
            {
                ShowTraceInfo();
            }
        }

        public void ShowTraceInfo()
        {
            var lstTrace = OcrHelper.GetOcrTrace(OcrContent?.id);
            var strMsg = string.Join("\n", lstTrace);
            if (string.IsNullOrEmpty(strMsg))
            {
                strMsg = "未查询到调用链路信息！";
            }
            MessageBox.Show(this, strMsg, TraceInfo, MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void UcContent_ParentChanged(object sender, EventArgs e)
        {
            CommonMethod.DetermineCall(this, delegate
            {
                var parent = FindForm() ?? Application.OpenForms[0];
                HighDpiHelper.ScaleToolStrip(1, parent.GetDpiScale(), toolResize);
            });
        }

        private void TxtContent_TextChanged(object sender, EventArgs e)
        {
            ShowEmptyPanle(string.IsNullOrEmpty(txtContent.Text));
        }

        const int WM_PARENTNOTIFY = 0x0210;
        protected override void WndProc(ref Message m)
        {
            if (m.Msg == WM_PARENTNOTIFY)
            {
                var frm = FindForm();
                if (frm != null)
                {
                    if (!frm.Focused)
                        frm.Activate();
                }
            }
            base.WndProc(ref m);
        }

        internal OcrContent OcrContent { get; set; }

        public Image Image { get { return imageBox.Image; } }

        internal SpiltMode SpiltModel
        {
            get => _currentSpiltModel;
            set
            {
                if (_currentSpiltModel != value)
                {
                    _currentSpiltModel = value;
                    BindContentByOcr(OcrContent);
                }
            }
        }

        public bool IsShowOldContent
        {
            get => _isShowOldContent;
            set
            {
                if (_isShowOldContent != value)
                {
                    _isShowOldContent = value;
                    BindContentByOcr(OcrContent, false, IsShowTxt);
                }
            }
        }

        internal KeyEventHandler TxtKeyDownEventDelegate
        {
            set
            {
                txtContent.KeyDown -= value;
                imageBox.KeyDown -= value;

                txtContent.KeyDown += value;
                imageBox.KeyDown += value;
            }
        }

        internal ContextMenuStrip MenuStrip
        {
            get => _menuStrip;
            set
            {
                _menuStrip = value;
                dgContent.ContextMenuStrip = _menuStrip;
                txtContent.ContextMenuStrip = _menuStrip;
                imageBox.ContextMenuStrip = _menuStrip;
            }
        }

        public void SetImageMode(bool isImage)
        {
            IsImageMode = isImage;
            imageBox.AutoCenter = isImage;
            tsmPicType.Visible = isImage;
            tsmEdit.Visible = isImage;
            if (isImage)
                InitImageType();
        }

        public void InitImageType()
        {
            if (OcrHelper.LstLocalImageType?.Count > 0)
            {
                图像增强ToolStripMenuItem.Visible = OcrHelper.LstLocalImageType.Exists(p => p.Code.Equals(ImageProcessType.图像增强.GetHashCode()));
                去屏幕纹ToolStripMenuItem.Visible = OcrHelper.LstLocalImageType.Exists(p => p.Code.Equals(ImageProcessType.去屏幕纹.GetHashCode()));
                老照片修复ToolStripMenuItem.Visible = OcrHelper.LstLocalImageType.Exists(p => p.Code.Equals(ImageProcessType.老照片修复.GetHashCode()));
            }
        }

        public int Zoom => imageBox.Zoom;

        public Color ImageBoxBackColor { get { return imageBox.BackColor; } }

        private List<int> SplitNumbers(int startNumber, int fitZoom, int listLength)
        {
            List<int> result = new List<int>();
            int step = (fitZoom - startNumber) / (listLength - 1); // 计算步长

            for (int i = 0; i < listLength; i++)
            {
                result.Add(startNumber + i * step);
            }

            return result;
        }

        public void SetImageZoomBig(bool isLimitMax = false)
        {
            var fitZoom = imageBox.GetFitZoom(isLimitMax);
            if (fitZoom > ImageBox.MinZoom)
            {
                var initZoom = Math.Max((int)(fitZoom * 0.05d), ImageBox.MinZoom);
                var lstZoom = SplitNumbers(initZoom, fitZoom, 6);
                foreach (var item in lstZoom)
                {
                    imageBox.Zoom = item;
                    Application.DoEvents();
                }
            }
            if (!Equals(fitZoom, imageBox.Zoom))
                imageBox.ZoomToFit(isLimitMax);
        }

        public void SetImageZoomSmall()
        {
            var fitZoom = Math.Min(imageBox.Zoom, 100);
            if (fitZoom > ImageBox.MinZoom)
            {
                imageBox.Zoom = Math.Max((int)(fitZoom * 0.25d), ImageBox.MinZoom);
                while (imageBox.Zoom > ImageBox.MinZoom)
                {
                    imageBox.ZoomOut(true);
                }
            }
        }

        private void ImageBoxSizeChanged(object sender, EventArgs e)
        {
            toolResize.Location = new Point((int)((ClientRectangle.Width - toolResize.Width) * 1.0 / 2)
                , ClientRectangle.Height - toolResize.Height
                                         - (imageBox.Visible && imageBox.HScrollVisibile
                                             ? SystemInformation.HorizontalScrollBarHeight
                                             : 0) - 5);
            if (toolResize.Visible)
            {
                toolResize.BringToFront();
            }

            SetFullScreenMenu();
        }

        private void SetFullScreenMenu()
        {
            if (!toolResize.Visible)
                return;
            var isMax = FindForm()?.WindowState == FormWindowState.Maximized;
            toolResize.ShowItemToolTips = !isMax;
            if (tsmFullScreen.Visible)
            {
                if (isMax)
                {
                    tsmFullScreen.Image = ProcessStyleImage(Resources.退出全屏);
                }
                else
                {
                    tsmFullScreen.Image = ProcessStyleImage(Resources.全屏);
                }
            }
        }

        private void ImageBoxZoomChanged(object sender, EventArgs e)
        {
            if (imageBox.Zoom == 100)
            {
                tsmPicOrigin.Image = ProcessStyleImage(Resources.缩放);
                tsmPicOrigin.ToolTipText = "最佳缩放".CurrentText();
            }
            else
            {
                tsmPicOrigin.Image = ProcessStyleImage(Resources.原始);
                tsmPicOrigin.ToolTipText = "原始尺寸".CurrentText();
            }

            txtPicZoomPercent.Text = string.Format("{0}%", imageBox.Zoom);

            SetFullScreenMenu();
        }

        private Image ProcessStyleImage(Image image)
        {
            if (image == null) return null;
            var scale = FindForm().GetScale(1F);
            if (scale != 1)
            {
                image = ImageProcessHelper.ScaleImage(image, scale);
            }
            return image;
        }

        private void WbContent_Navigating(object sender, WebBrowserNavigatingEventArgs e)
        {
            if (e.Url != null)
                if (e.Url.ToString().StartsWith("file:"))
                {
                    e.Cancel = true;
                    var files = new List<string> { e.Url.ToString().Replace("file:///", "") };
                    FrmMain.DragDropEventDelegate?.Invoke(files, null, null, ProcessBy.主界面, null);
                }
        }

        Panel EmptyPanel;

        private void InitEmptyPanel()
        {
            if (EmptyPanel != null)
            {
                return;
            }
            EmptyPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackgroundImage = Resources.拖拽文件,
                BackgroundImageLayout = ImageLayout.Center,
                BackColor = Color.Transparent,
                Visible = true,
                AllowDrop = true,
                Parent = txtContent,
            };
            txtContent.Controls.Add(EmptyPanel);
            EmptyPanel.BringToFront();
        }

        internal void SetDragDrop()
        {
            this.ControlUseDrop();
            imageBox.ControlUseDrop();
            txtContent.ControlUseDrop();
            dgContent.ControlUseDrop();
            EmptyPanel.ControlUseDrop();
        }

        internal void ShowEmptyPanle(bool isShow)
        {
            EmptyPanel.Visible = isShow;
            if (isShow)
            {
                EmptyPanel.BringToFront();
                toolResize.Visible = false;
            }
        }

        public void SetCanClose()
        {
            var picClose = new PictureBox
            {
                Cursor = Cursors.Hand,
                Visible = true,
                Image = ProcessStyleImage(Resources.Full_close_down),
                BackColor = Color.Transparent,
                SizeMode = PictureBoxSizeMode.StretchImage
            };
            var width = Math.Min(Height, picClose.Image.Height);
            picClose.Size = new Size(width, width);
            //picClose.Anchor = AnchorStyles.Right | AnchorStyles.Top;
            picClose.MouseDown += (sender, e) =>
            {
                FindForm()?.Close();
            };
            picClose.MouseMove += (sender, e) =>
            {
                picClose.Image = ProcessStyleImage(Resources.Full_close_hover);
            };
            picClose.MouseLeave += (sender, e) =>
            {
                picClose.Image = ProcessStyleImage(Resources.Full_close_down);
            };
            imageBox.Controls.Add(picClose);
            picClose.BringToFront();
            imageBox.MouseDown += (sender, e) =>
            {
                picClose.Visible = false;
            };
            imageBox.MouseUp += (sender, e) =>
            {
                SetPicLocation(picClose);
            };
            imageBox.BeforeZoomChanged += (sender, e) =>
            {
                picClose.Visible = false;
            };
            imageBox.ZoomChanged += (sender, e) =>
            {
                if (!imageBox.IsOnMouseWheel)
                    SetPicLocation(picClose);
            };
            imageBox.MouseWheel += (sender, e) =>
            {
                SetPicLocation(picClose);
            };
            imageBox.BeforeMouseWheel += (sender, e) =>
            {
                picClose.Visible = false;
            };
            imageBox.SizeChanged += (sender, e) =>
            {
                SetPicLocation(picClose);
            };
            imageBox.Scroll += (sender, e) =>
            {
                SetPicLocation(picClose);
            };
            SizeChanged += (sender, e) =>
            {
                SetPicLocation(picClose);
            };
        }

        private void SetPicLocation(PictureBox picClose)
        {
            var loction = new Point(imageBox.DisplayRectangle.Width - picClose.Width - 10 + (imageBox.VScrollVisibile ? 10 : 0), 10);
            var visible = FindForm()?.WindowState == FormWindowState.Maximized;
            if (visible != picClose.Visible)
                picClose.Visible = visible;
            if (!loction.Equals(picClose.Location))
                picClose.Location = loction;
        }

        public void RefreshStyle()
        {
            txtContent.BackColor = CommonSetting.Get默认背景颜色();
            txtContent.Font = CommonSetting.默认文本字体;
            txtContent.ForeColor = CommonSetting.Get默认文字颜色();
            dgContent.BackgroundColor = CommonSetting.Get默认背景颜色();
            dgContent.Font = CommonSetting.默认文本字体;
            dgContent.ForeColor = CommonSetting.Get默认文字颜色();
            imageBox.GridColor = CommonSetting.图片预览背景颜色;
            imageBox.GridDisplayMode = CommonSetting.ConvertToEnum(CommonSetting.图片预览背景, ImageBoxGridDisplayMode.马赛克);
            imageBox.InitBackGround();
        }

        internal void BindContentByStr(string strContent, bool isAppend = false)
        {
            if (isAppend)
                txtContent.AppendText(Environment.NewLine + strContent);
            else
                txtContent.Text = strContent;
            if (!string.IsNullOrEmpty(txtContent.Text))
                txtContent.BringToFront();
            Application.DoEvents();
        }

        public bool IsShowToolBox { get; set; } = true;

        private DisplayModel _nowDisplayMode;

        internal DisplayModel NowDisplayMode
        {
            get => _nowDisplayMode;
            set
            {
                _nowDisplayMode = value;
                if (IsCanVertical())
                {
                    if (!imageBox.IsBindImageMode && _nowDisplayMode == DisplayModel.图文模式)
                        BindPicTxt();
                    SetDisplayMode(_nowDisplayMode);
                }
            }
        }

        internal void BindContentByOcr(OcrContent ocr, bool isAppend = false, bool? isShowTxt = false)
        {
            if (isShowTxt.HasValue) IsShowTxt = isShowTxt.Value;
            OcrContent = ocr;
            if (ocr?.result == null) return;
            switch (ocr.result.resultType)
            {
                //ProcessShowControl(resuType);
                case ResutypeEnum.表格:
                    {
                        var dt = CommonResult.GetTableContent(OcrContent.result);
                        CommonMethod.DetermineCall(this, delegate
                        {
                            dgContent.DataSource = dt;
                            dgContent.AutoResizeColumns();
                            dgContent.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.DisplayedCellsExceptHeaders;
                            foreach (DataGridViewColumn column in dgContent.Columns)
                            {
                                column.HeaderText = "";
                                column.MinimumWidth = 80;
                            }

                            dgContent.BringToFront();
                        });
                        break;
                    }
                case ResutypeEnum.网页:
                    CommonMethod.DetermineCall(this, delegate
                    {
                        wbContent.DocumentText = "";
                        wbContent.BringToFront();
                        var url = OcrHelper.GetFileResultUrl(OcrContent);
                        wbContent.NavigateWithAutoAgent(url);
                    });
                    break;
                default:
                    CommonMethod.DetermineCall(this, delegate
                    {
                        if (OcrContent.ocrType == OcrType.公式)
                        {
                            wbContent.DocumentText = "";
                            wbContent.BringToFront();
                            var strPost = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(OcrContent.result?.autoText));
                            wbContent.NavigateWithAutoAgent(CommonString.HostCode?.FullUrl + "math/view.aspx", strPost);
                        }
                        else
                        {
                            //txtContent.BackColor = ContentBackColor;
                            //txtContent.Font = ContentFont;
                            var contentStr = GetTextByContent(isAppend)?.TrimEnd() + Environment.NewLine;
                            if (isAppend)
                            {
                                if (string.IsNullOrEmpty(txtContent.Text))
                                {
                                    contentStr = contentStr.TrimStart('\r').TrimStart('\n');
                                }
                                txtContent.Text += contentStr;
                            }
                            else
                            {
                                txtContent.Text = contentStr;
                                imageBox.IsBindImageMode = false;
                                var isVertical = IsCanVertical();
                                tsmModel.Visible = isVertical;
                                if (isVertical)
                                {
                                    NowDisplayMode = NowDisplayMode;
                                }
                                else
                                {
                                    SetDisplayMode(DisplayModel.文字模式);
                                }
                            }
                            AppendLinkLabel();
                        }
                    });
                    break;
            }
        }

        private void AppendLinkLabel()
        {
            if (!txtContent.Text.StartsWith("识别失败"))
            {
                return;
            }
            txtContent.InsertLink(TraceInfo, txtContent.TextLength);
        }

        public bool IsImageMode { get; set; }

        public void ShowImageTool()
        {
            toolResize.Visible = IsShowToolBox && OcrContent != null && OcrContent.result != null
                                 && Equals(OcrContent.result.resultType, ResutypeEnum.文本) && !Equals(OcrContent.ocrType, OcrType.公式);
            if (toolResize.Visible)
            {
                var isVertical = IsCanVertical();
                tsmModel.Visible = isVertical && !IsImageMode;
                ImageBoxSizeChanged(null, null);
            }
        }

        public void BindImage(Image image, bool isBindImageOnly, bool isAutoSize)
        {
            imageBox.SetImage(image, isAutoSize);
            if (!Equals(tsmPicType.ToolTipText, 原始图像ToolStripMenuItem.Text))
                tsmPicType_DropDownItemClicked(this, new ToolStripItemClickedEventArgs(原始图像ToolStripMenuItem));

            if (!isBindImageOnly)
            {
                BindPicTxt();
                SetDisplayMode(DisplayModel.图文模式);
            }
        }

        private string GetTextByContent(bool isAppend = false)
        {
            var result = OcrContent.result.GetTextResult(CommonSetting.首行缩进, isAppend, IsShowOldContent, SpiltModel
                , Equals(OcrContent.ocrType, OcrType.翻译), OcrContent.processName);
            return result;
        }

        internal string GetTextByContent(TextCellInfo cell)
        {
            var result = cell.GetCellContent(OcrContent?.ocrType == OcrType.翻译, IsShowOldContent);
            return result;
        }

        public string GetContentText(bool isAll = true)
        {
            return isAll
                ? string.IsNullOrEmpty(txtContent.SelectedText) ? txtContent.Text : txtContent.SelectedText
                : txtContent.SelectedText;
        }

        public void ExportExcel()
        {
            if (dgContent.DataSource == null)
            {
                MessageBox.Show(this, "没有数据！".CurrentText(), CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var text = ServerTime.DateTime.ToDateStr("yyyyMMddhhmmss");
            var saveFileDialog1 = new SaveFileDialog
            {
                Filter = "Excel|*.xls;*.csv;*.xlsx;",
                Title = "选择保存位置".CurrentText(),
                FileName = "Excel_" + text,
                FilterIndex = 1
            };
            if (saveFileDialog1.ShowDialog(this) != DialogResult.OK ||
                string.IsNullOrEmpty(saveFileDialog1.FileName)) return;
            ExcelHelper.ExportCsv(dgContent, saveFileDialog1.FileName);
        }

        #region 图文模式

        public bool IsCanVertical()
        {
            return OcrContent?.IsCanVertical == true;
        }

        public bool IsShowTxt { get; set; }

        public void BindPicTxt()
        {
            if (FindForm() is FrmMain)
            {
                tsmNewWindow.Visible = true;
                tsmFullScreen.Visible = false;
            }
            else
            {
                tsmNewWindow.Visible = false;
                tsmFullScreen.Visible = true;
            }

            imageBox.BindPicTxt(this, IsShowTxt);
        }

        private void tsmPicBig_Click(object sender, EventArgs e)
        {
            imageBox.ZoomIn(true);
        }

        private void tsmPicOrigin_Click(object sender, EventArgs e)
        {
            if (imageBox.Zoom == 100)
                imageBox.ZoomToFit();
            else
                imageBox.Zoom = 100;
        }

        private void tsmPicSmall_Click(object sender, EventArgs e)
        {
            imageBox.ZoomOut(true);
        }

        private void tsmModel_Click(object sender, EventArgs e)
        {
            NowDisplayMode = NowDisplayMode == DisplayModel.文字模式 ? DisplayModel.图文模式 : DisplayModel.文字模式;
            var mouseLocation = toolResize.PointToScreen(tsmModel.Bounds.Location);
            CommonMethod.SetCursorPos(mouseLocation.X + tsmModel.Width / 2, mouseLocation.Y + tsmModel.Height / 2);
        }

        private void SetDisplayMode(DisplayModel model)
        {
            if (model == DisplayModel.文字模式)
            {
                tsmImageViewBackStyle.Visible = false;
                txtPicZoomPercent.Visible = false;
                tsmTrans.Visible = Program.NowUser?.IsSupportTranslate == true;
                tsmSearch.Visible = true;
                tsmPicBig.Visible = false;
                tsmPicOrigin.Visible = false;
                tsmPicSmall.Visible = false;
                //if (OcrContent.ocrType == OCRType.公式)
                //{
                //    wbContent.DocumentText = "";
                //    wbContent.BringToFront();
                //    var url = OCRHelper.GetMathFileResultUrl(new OcrContent() { result = new ResultEntity() { spiltText = string.Format("${0}$", OcrContent.result.spiltText) } });
                //    wbContent.Url = new Uri(url);
                //}
                //else
                {
                    imageBox.Visible = false;
                    txtContent.Visible = true;
                    txtContent.BringToFront();
                }
            }
            else
            {
                var isEmpty = string.IsNullOrEmpty(OcrContent?.result?.autoText);
                tsmSearch.Visible = false;
                tsmTrans.Visible = false;
                txtPicZoomPercent.Visible = true;
                tsmPicBig.Visible = true;
                tsmPicOrigin.Visible = true;
                tsmPicSmall.Visible = true;


                tsmCopy.Visible = !isEmpty;
                tsmModel.Visible = !isEmpty;

                tsbReOcr.Visible = isEmpty;
                tsbSaveImg.Visible = isEmpty;
                tsmRotate.Visible = isEmpty;

                tsmImageViewBackStyle.Visible = true;
                tsmEdit.Visible = isEmpty || FindForm() is FormViewImage;

                if (isEmpty || imageBox.IsBindImageMode)
                {
                    txtContent.Visible = false;
                    imageBox.Visible = true;
                    //imageBox.AdjustLayout();
                    imageBox.BringToFront();
                }
                else
                {
                    imageBox.Visible = false;
                    txtContent.Visible = true;
                    txtContent.BringToFront();
                }
            }

            ImageBoxZoomChanged(null, null);
            ImageBoxSizeChanged(null, null);
            var tModel = model == DisplayModel.图文模式 ? DisplayModel.文字模式 : DisplayModel.图文模式;
            tsmModel.Image = ProcessStyleImage(tsmModel.SetResourceImage(tModel.ToString()));
            tsmModel.Text = string.Format("{1}【{0}】", tModel.ToString().CurrentText(), "切换到".CurrentText());
        }

        #endregion

        #region 其他按钮功能

        private void tsmFullScreen_Click(object sender, EventArgs e)
        {
            imageBox.SwitchMaxWindow();
        }

        private void tsmSearch_Click(object sender, EventArgs e)
        {
            var txt = GetContentText();
            if (!string.IsNullOrEmpty(txt)) FrmMain.DoSearch(txt);
        }

        private void tsmPicView_Click(object sender, EventArgs e)
        {
            if (Image == null)
            {
                return;
            }

            if (string.IsNullOrEmpty(OcrContent.processName))
            {
                this.ViewImage(Image);
            }
            else
            {
                //if (FindForm() is MetroForm frm)
                //{
                //    var compare = new FormViewImageWX { Icon = frm.Icon };
                //    compare.Bind(Image, OcrContent.result.verticalText.DeserializeJson<List<TextCellInfo>>());
                //    compare.Show();
                //}
                if (FindForm() is MetroForm frm)
                {
                    var compare = new FrmPicCompare { Icon = frm.Icon };
                    compare.Init(SpiltModel, IsShowOldContent);
                    compare.Bind(Image, OcrContent);
                    compare.Show();
                }
            }
        }

        private void tsmCopy_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(txtContent.Text))
                ClipboardService.SetText(txtContent.Text);
        }

        private void tsmTrans_Click(object sender, EventArgs e)
        {
            var transText = string.IsNullOrEmpty(txtContent.SelectedText) ? txtContent.Text : txtContent.SelectedText;
            if (string.IsNullOrEmpty(transText)) return;
            var files = new List<string> { "data:txt" + transText };
            FrmMain.DragDropEventDelegate?.Invoke(files, null, OcrType.翻译, ProcessBy.主界面, null);
        }

        #endregion

        private void tsmRotate_Click(object sender, EventArgs e)
        {
            if (imageBox.Image == null)
            {
                return;
            }
            imageBox.Image.RotateFlip(RotateFlipType.Rotate90FlipNone);
            imageBox.Invalidate();
            //imageBox.ZoomToFit();
            //if (imageBox.Zoom > 100) imageBox.Zoom = 100;
        }

        private void tsbSaveImg_Click(object sender, EventArgs e)
        {
            if (imageBox.Image != null)
                new Bitmap(imageBox.Image).SaveFile(this);
        }

        private void tsbReOcr_Click(object sender, EventArgs e)
        {
            if (imageBox.Image != null)
                imageBox.Image.Ocr();
        }

        private void InitBackColorDropDown()
        {
            ToolStripMenuItem checkItem = null;
            foreach (ImageBoxGridDisplayMode type in Enum.GetValues(typeof(ImageBoxGridDisplayMode)))
            {
                var item = new ToolStripMenuItem
                {
                    Tag = type.GetHashCode(),
                    AutoToolTip = false,
                    Checked = Equals(type.ToString(), CommonSetting.图片预览背景),
                    Text = type.ToString()
                };
                item.Image = item.SetResourceImage("背景_" + type);
                if (item.Checked)
                {
                    checkItem = item;
                }
                tsmImageViewBackStyle.DropDownItems.Add(item);
            }

            if (checkItem == null)
            {
                checkItem = tsmImageViewBackStyle.DropDownItems[0] as ToolStripMenuItem;
            }

            if (checkItem != null) tsmImageViewBackStyle.Image = checkItem.Image;
        }

        private void tsmImageViewBackStyle_DropDownItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            var item = e.ClickedItem;
            foreach (ToolStripMenuItem oldItem in tsmImageViewBackStyle.DropDownItems) oldItem.Checked = Equals(item, oldItem);
            if (item?.Tag == null) return;
            var imgType = (ImageBoxGridDisplayMode)BoxUtil.GetInt32FromObject(item.Tag?.ToString());
            tsmImageViewBackStyle.Image = item.Image;

            var backColor = CommonSetting.图片预览背景颜色;
            switch (imgType)
            {
                case ImageBoxGridDisplayMode.豆沙绿:
                case ImageBoxGridDisplayMode.深沉黑:
                case ImageBoxGridDisplayMode.办公灰:
                    backColor = ImageBoxGridDisplayModeHelper.GetBackColorByType(imgType);
                    break;
                case ImageBoxGridDisplayMode.自定义:
                    {
                        if (!ColorPickerForm.PickColor(backColor, out var newColor, true, FindForm()))
                            newColor = Color.FromArgb(238, 243, 250);
                        backColor = newColor;
                        break;
                    }
            }
            CommonSetting.SetValue("图片预览背景", imgType.ToString());
            CommonSetting.SetValue("图片预览背景颜色", backColor);

            imageBox.GridColor = backColor;
            imageBox.GridDisplayMode = imgType;
            imageBox.InitBackGround();
        }

        private void tsmPicType_DropDownItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            var item = e.ClickedItem;
            if (item == null || item.Tag == null) return;
            if (imageBox.OriginImage == null && imageBox.Image != null)
            {
                imageBox.OriginImage = new Bitmap(imageBox.Image);
            }
            if (imageBox.OriginImage == null) return;

            //tsmPicType.BackgroundImage = item.Image;
            tsmPicType.Image = item.Image; //tsmPicType.BackgroundImage;
            tsmPicType.ToolTipText = item.Text;
            var imgType = (ImageProcessType)BoxUtil.GetInt32FromObject(item.Tag?.ToString());
            int oldZoom = imageBox.Zoom;
            try
            {
                imageBox.Image = ImageProcessHelper.ProcessImage(new Bitmap(imageBox.OriginImage), imgType);
            }
            catch (Exception oe)
            {
                Log.WriteError("tsmPicType_DropDownItemClicked:" + imgType.ToString(), oe);
            }
            imageBox.Tag = null;
            imageBox.Zoom = oldZoom;
        }

        private void tsmEdit_Click(object sender, EventArgs e)
        {
            CommonMethod.EditImage(Image);
        }
    }

    public enum DisplayModel
    {
        文字模式 = 0,
        图文模式 = 1
    }
}