﻿using OCRTools.Properties;
using System;
using System.Runtime.InteropServices;
using System.Security.Permissions;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools
{
    public class WebBrowser2 : WebBrowser
    {
        AxHost.ConnectionPointCookie _cookie;
        WebBrowser2EventHelper _helper;

        private bool _isUseDefaultAgent = true;
        private string _autoUrl;
        private string _postBody = string.Empty;

        private PictureBox picImage = new PictureBox()
        {
            AccessibleDefaultActionDescription = "webloading",
            SizeMode = PictureBoxSizeMode.CenterImage,
            Image = Resources.webloading,
            Visible = false
        };

        public WebBrowser2()
        {
            ProgressChanged += WbDesc_ProgressChanged;
        }

        private int _stateChangedCount = -1;

        private void WbDesc_ProgressChanged(object sender, WebBrowserProgressChangedEventArgs e)
        {
            _stateChangedCount++;
            if (_stateChangedCount == 0)
            {
                Task.Factory.StartNew(() =>
                {
                    System.Threading.Thread.Sleep(5000);
                    if (_stateChangedCount == 0)
                    {
                        if (_isUseDefaultAgent)
                        {
                            _isUseDefaultAgent = false;
                            NavigateWithAutoAgent(_autoUrl, _postBody);
                        }
                        else
                        {
                            CommonMethod.ShowHelpMsg("网站内容加载异常，正在尝试用本地浏览器打开！");
                            CommonMethod.OpenUrl(_autoUrl);
                        }
                    }
                });
            }

            if (!(sender is WebBrowser2 browser))
            {
                return;
            }

            if (browser.ReadyState == WebBrowserReadyState.Complete || Equals(e.CurrentProgress, e.MaximumProgress))
            {
                picImage.Visible = false;
            }
            else
            {
                FrmMain.FrmTool.Invoke((Action)delegate
                {
                    if (!browser.Parent.Controls.Contains(picImage))
                    {
                        browser.Parent.Controls.Add(picImage);
                    }
                    picImage.Visible = true;
                    picImage.Parent = browser.Parent;
                    picImage.Location = browser.Location;
                    picImage.Size = browser.Size;
                    picImage.BringToFront();
                    Application.DoEvents();
                });
            }
        }

        internal void NavigateWithAutoAgent(string url, string body = null)
        {
            try
            {
                if (_isUseDefaultAgent)
                {
                    _autoUrl = url;
                    _postBody = body;
                }
                else
                    WinInetInterop.SetConnectionNoProxy();
            }
            catch (Exception exception)
            {
                Console.WriteLine(exception);
            }
            if (!IsDisposed)
            {
                if (!string.IsNullOrEmpty(body))
                {
                    Navigate(url, null, System.Text.Encoding.UTF8.GetBytes(body), null);
                }
                else
                {
                    Navigate(url);
                }
                _stateChangedCount = -1;
                WbDesc_ProgressChanged(this, new WebBrowserProgressChangedEventArgs(0, 1));
            }
        }

        protected override void OnNavigating(WebBrowserNavigatingEventArgs e)
        {
            if (e.Url.ToString().Contains("file/view.html"))
            {
                e.Cancel = true;
                CommonMethod.OpenUrl(e.Url.ToString());
                return;
            }
            WebBroswerEx.SecurityManagerHelper.SetWebBrowser(this);
            base.OnNavigating(e);

        }

        protected override void Dispose(bool disposing)
        {
            if (!_isUseDefaultAgent)
            {
                try
                {
                    WinInetInterop.RestoreSystemProxy();
                }
                catch (Exception exception)
                {
                    Console.WriteLine(exception);
                }
            }
            base.Dispose(disposing);
        }

        [PermissionSet(SecurityAction.LinkDemand, Name = "FullTrust")]
        protected override void CreateSink()
        {
            base.CreateSink();

            // Create an instance of the client that will handle the event
            // and associate it with the underlying ActiveX control.
            _helper = new WebBrowser2EventHelper(this);
            _cookie = new AxHost.ConnectionPointCookie(
                this.ActiveXInstance, _helper, typeof(DWebBrowserEvents2));
        }

        [PermissionSet(SecurityAction.LinkDemand, Name = "FullTrust")]
        protected override void DetachSink()
        {
            // Disconnect the client that handles the event
            // from the underlying ActiveX control.
            if (_cookie != null)
            {
                _cookie.Disconnect();
                _cookie = null;
            }
            base.DetachSink();
        }

        public event WebBrowserNavigateErrorEventHandler NavigateError;

        // Raises the NavigateError event.
        protected virtual void OnNavigateError(
            WebBrowserNavigateErrorEventArgs e)
        {
            if (_isUseDefaultAgent)
            {
                _isUseDefaultAgent = false;
                NavigateWithAutoAgent(_autoUrl, _postBody);
                return;
            }
            NavigateError?.Invoke(this, e);
        }

        // Handles the NavigateError event from the underlying ActiveX 
        // control by raising the NavigateError event defined in this class.
        private class WebBrowser2EventHelper :
            StandardOleMarshalObject, DWebBrowserEvents2
        {
            private WebBrowser2 parent;

            public WebBrowser2EventHelper(WebBrowser2 parent)
            {
                this.parent = parent;
            }

            public void NavigateError(object pDisp, ref object url,
                ref object frame, ref object statusCode, ref bool cancel)
            {
                // Raise the NavigateError event.
                parent.OnNavigateError(
                    new WebBrowserNavigateErrorEventArgs(
                    (string)url, (string)frame, (int)statusCode, cancel));
            }
        }
    }

    // Represents the method that will handle the WebBrowser2.NavigateError event.
    public delegate void WebBrowserNavigateErrorEventHandler(object sender,
        WebBrowserNavigateErrorEventArgs e);

    // Provides data for the WebBrowser2.NavigateError event.
    public class WebBrowserNavigateErrorEventArgs : EventArgs
    {
        public WebBrowserNavigateErrorEventArgs(
            string url, string frame, int statusCode, bool cancel)
        {
            Url = url;
            Frame = frame;
            StatusCode = statusCode;
            Cancel = cancel;
        }

        public string Url { get; set; }

        public string Frame { get; set; }

        public int StatusCode { get; set; }

        public bool Cancel { get; set; }
    }

    // Imports the NavigateError method from the OLE DWebBrowserEvents2 
    // interface. 
    [ComImport, Guid("34A715A0-6587-11D0-924A-0020AFC7AC4D"),
    InterfaceType(ComInterfaceType.InterfaceIsIDispatch),
    TypeLibType(TypeLibTypeFlags.FHidden)]
    public interface DWebBrowserEvents2
    {
        [DispId(271)]
        void NavigateError(
            [In, MarshalAs(UnmanagedType.IDispatch)] object pDisp,
            [In] ref object URL, [In] ref object frame,
            [In] ref object statusCode, [In, Out] ref bool cancel);
    }
}
