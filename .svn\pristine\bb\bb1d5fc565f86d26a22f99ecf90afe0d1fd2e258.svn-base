﻿using System;

namespace OCRTools.Common.SNTP
{
    /// <summary>
    ///     Class that holds data relating to any errors that occurred.
    /// </summary>
    public class ErrorData
    {
        #region Constructors

        /// <summary>
        ///     Creates a new instance of ErrorData.
        /// </summary>
        internal ErrorData()
        {
        }

        #endregion Constructors 

        #region Properties 

        /// <summary>
        ///     Gets whether an error occurred.
        /// </summary>
        public bool Error { get; }

        /// <summary>
        ///     Gets the exception (if any) that was caught.
        /// </summary>
        public Exception Exception { get; }

        #endregion Properties 
    }
}