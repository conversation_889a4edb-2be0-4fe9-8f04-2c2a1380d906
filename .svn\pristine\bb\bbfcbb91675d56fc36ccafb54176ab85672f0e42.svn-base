using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools
{
    /// <summary>
    /// 图文模式处理器
    /// 完全基于PanelPictureView的逻辑，提供简单的悬停高亮和点击选择功能
    /// </summary>
    public class ImageTextModeHandler : BaseImageModeHandler
    {
        #region 常量定义

        private readonly Color IMG_MODE_FOCUS_COLOR = Color.FromArgb(50, Color.Red);
        private readonly Color IMG_MODE_CLICK_COLOR = Color.FromArgb(90, Color.Red);

        #endregion

        #region 私有字段

        private TextCellInfo _currentCell;
        private Rectangle _drawRegion;

        #endregion

        #region 公共属性 - 图文模式特有的属性

        /// <summary>
        /// 是否显示提示 - 图文模式特有的属性
        /// </summary>
        public bool IsShowTip { get; set; } = true;

        #endregion

        #region 事件定义

        /// <summary>
        /// 文本块选择状态变化事件
        /// </summary>
        public event EventHandler<TextCellEventArgs> TextCellStateChanged;

        /// <summary>
        /// 事件参数类
        /// </summary>
        public class TextCellEventArgs : EventArgs
        {
            public TextCellInfo Cell { get; private set; }
            public TextCellSelectionType SelectionType { get; private set; }

            public TextCellEventArgs(TextCellInfo cell, TextCellSelectionType type)
            {
                Cell = cell;
                SelectionType = type;
            }
        }

        /// <summary>
        /// 选择类型枚举
        /// </summary>
        public enum TextCellSelectionType
        {
            None,       // 无选择
            Hover,      // 悬停
            Click       // 点击
        }

        #endregion

        #region BaseImageModeHandler 实现

        public override void Activate()
        {
            // 图文模式不需要特殊的事件绑定，使用基类的事件分发即可
        }

        public override void Deactivate()
        {
            // 图文模式不需要特殊的事件解绑
        }

        protected override void ClearCurrentState()
        {
            // 清除当前高亮
            if (!_drawRegion.IsEmpty)
            {
                InvalidateRegion(_drawRegion);
                _drawRegion = Rectangle.Empty;
            }

            // 重置当前单元格
            _currentCell = null;
        }

        protected override void SetImage(Image image)
        {
            _viewer.Image = image;
        }

        public override void HandleMouseDown(MouseEventArgs e)
        {
            // 图文模式需要ImageBox的拖动功能，先调用基础功能
            _viewer.CallImageBoxMouseDown(e);

            if (e.Button == MouseButtons.Left)
            {
                _currentCell = null;
                DrawCell(true);
            }
        }

        public override void HandleMouseMove(MouseEventArgs e)
        {
            // 图文模式需要ImageBox的拖动功能
            _viewer.CallImageBoxMouseMove(e);

            DrawCell(false, e.Button != MouseButtons.None || e.Delta != 0);
        }

        public override void HandleMouseUp(MouseEventArgs e)
        {
            // 图文模式需要ImageBox的拖动功能
            _viewer.CallImageBoxMouseUp(e);

            // 图文模式不需要特殊的MouseUp处理
        }

        public override void HandleMouseLeave(EventArgs e)
        {
            // 鼠标离开时清除高亮并触发事件
            if (_currentCell != null)
            {
                TextCellStateChanged?.Invoke(this, new TextCellEventArgs(null, TextCellSelectionType.None));
                _currentCell = null;

                if (!_drawRegion.IsEmpty)
                {
                    InvalidateRegion(_drawRegion);
                    _drawRegion = Rectangle.Empty;
                }

                CommonMethod.HideTxtToolTip(_viewer);
            }
        }

        public override void HandlePaint(PaintEventArgs e)
        {
            if (!_drawRegion.IsEmpty)
            {
                using (var contentBrush = new SolidBrush(IMG_MODE_CLICK_COLOR))
                {
                    e.Graphics.FillRectangle(contentBrush, _drawRegion);
                }
            }
        }

        public override void HandleZoomChanged(EventArgs e)
        {
            if (!_drawRegion.IsEmpty)
            {
                InvalidateRegion(_drawRegion);
                _drawRegion = Rectangle.Empty;
            }
        }

        public override void HandleScroll(ScrollEventArgs e)
        {
            HandleZoomChanged(e);
        }

        public override void HandleMouseWheel(MouseEventArgs e)
        {
            HandleMouseMove(e);
        }

        public override void ClearState()
        {
            base.ClearState();
            _currentCell = null;
            if (!_drawRegion.IsEmpty)
            {
                InvalidateRegion(_drawRegion);
                _drawRegion = Rectangle.Empty;
            }
        }

        #endregion

        #region 核心逻辑方法

        /// <summary>
        /// 获取当前鼠标位置的文字区域
        /// </summary>
        private TextCellInfo GetCurrentCell()
        {
            var imagePoint = GetImagePointFromMouse();
            return FindCellAtImagePoint(imagePoint);
        }

        /// <summary>
        /// 绘制文字区域高亮
        /// </summary>
        private void DrawCell(bool isClick = false, bool isOnMove = false)
        {
            var cell = isOnMove ? null : GetCurrentCell();
            if (cell != null && Equals(_currentCell, cell))
            {
                return;
            }

            if (!_drawRegion.IsEmpty)
            {
                if (IsShowTip || cell != null)
                {
                    InvalidateRegion(_drawRegion);
                    _drawRegion = Rectangle.Empty;
                }
            }

            if (cell != null)
            {
                // 使用基类的坐标转换方法
                var imageRect = cell.location.Rectangle.SizeOffset(2);
                _drawRegion = GetControlRectFromImage(imageRect);

                var tipLoc = new Point(Math.Max(0, _drawRegion.Location.X), _drawRegion.Location.Y + _drawRegion.Height + 1);
                if (!_viewer.ClientRectangle.Contains(tipLoc))
                {
                    tipLoc = new Point(Math.Max(_viewer.ClientRectangle.X, tipLoc.X), Math.Min(_viewer.ClientRectangle.Y, tipLoc.Y));
                }

                // 触发事件
                TextCellStateChanged?.Invoke(this, new TextCellEventArgs(
                    cell,
                    isClick ? TextCellSelectionType.Click : TextCellSelectionType.Hover
                ));

                if (isClick)
                {
                    InvalidateRegion(_drawRegion);

                    if (IsShowTip)
                        CommonMethod.ShowTxtToolTipContextMenu(_viewer, cell.TipText, tipLoc);

                    if (CommonSetting.点击图片复制结果)
                    {
                        try
                        {
                            ClipboardService.SetText(cell.TipText);
                        }
                        catch { }
                    }
                }
                else
                {
                    using (var g = _viewer.CreateGraphics())
                    {
                        using (var contentBrush = new SolidBrush(IMG_MODE_FOCUS_COLOR))
                        {
                            g.FillRectangle(contentBrush, _drawRegion);
                        }
                    }

                    if (IsShowTip)
                        CommonMethod.ShowTxtToolTip(_viewer, cell.TipText, tipLoc);
                }
            }
            else
            {
                // 触发无选择事件
                TextCellStateChanged?.Invoke(this, new TextCellEventArgs(null, TextCellSelectionType.None));

                CommonMethod.HideTxtToolTip(_viewer);
                CommonMethod.HideTxtToolTipContextMenu();
            }
            _currentCell = cell;
        }

        /// <summary>
        /// 绑定图文数据
        /// </summary>
        public void BindPicTxt(UcContent content, bool isShowTxt = false)
        {
            _textCells = content.OcrContent?.result?.verticalText?.DeserializeJson<List<TextCellInfo>>() ?? new List<TextCellInfo>();

            // 清除当前高亮
            if (!_drawRegion.IsEmpty)
            {
                InvalidateRegion(_drawRegion);
                _drawRegion = Rectangle.Empty;
            }

            // 重置当前单元格
            _currentCell = null;

            // 不立即处理图像，而是在需要时异步处理
            if (content.Image != null && _textCells.Count > 0)
            {
                // 异步处理图像绘制，避免UI阻塞
                Task.Run(() => ProcessImageAsync(content, isShowTxt));
            }
            else
            {
                // 如果没有处理需求，直接使用原始图像
                _viewer.Image = content.Image;
            }

            _viewer.BringToFront();
        }

        #endregion

        #region 图像处理方法

        /// <summary>
        /// 异步处理图像绘制
        /// </summary>
        private async Task ProcessImageAsync(UcContent content, bool isShowTxt)
        {
            try
            {
                var processedImage = await Task.Run(() => CreateProcessedImage(content, isShowTxt));

                // 切换到UI线程更新图像
                _viewer.Invoke(new Action(() =>
                {
                    _viewer.Image = processedImage;
                }));
            }
            catch (Exception ex)
            {
                // 记录异常但不抛出，确保不会导致应用崩溃
                Console.WriteLine($"ProcessImageAsync异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建处理后的图像
        /// </summary>
        private Bitmap CreateProcessedImage(UcContent content, bool isShowTxt)
        {
            // 安全检查图片状态
            if (content?.Image == null)
            {
                throw new ArgumentException("Content image is null");
            }

            // 使用ImageResourceManager创建安全的图片副本
            Image managedImage = ImageResourceManager.GetManagedImage(content.Image);
            if (managedImage == null)
            {
                throw new ArgumentException("Failed to create managed image copy");
            }

            Bitmap processedImage = null;
            try
            {
                // 将管理的图片转换为Bitmap进行处理
                processedImage = managedImage as Bitmap ?? new Bitmap(managedImage);
                // 批量预处理文本内容，避免重复调用
                PreprocessTextContent(content, _textCells);

                // 预处理所有绘制操作，减少锁竞争
                var drawingOperations = PrepareDrawingOperations(_textCells, isShowTxt);

                // 串行执行GDI+操作
                using (var g = Graphics.FromImage(processedImage))
                {
                    // 优化渲染质量设置，平衡性能和质量
                    g.InterpolationMode = InterpolationMode.Bilinear;
                    g.CompositingQuality = CompositingQuality.HighSpeed;
                    g.SmoothingMode = SmoothingMode.HighSpeed;
                    g.TextRenderingHint = TextRenderingHint.SystemDefault;

                    using (var brush = new SolidBrush(CommonSetting.Get默认文字颜色()))
                    using (var backgroundBrush = new SolidBrush(CommonSetting.Get默认背景颜色()))
                    {
                        // 执行所有绘制操作
                        foreach (var operation in drawingOperations)
                        {
                            ExecuteDrawingOperation(g, operation, brush, backgroundBrush);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 如果处理失败，释放资源并重新抛出异常
                processedImage?.Dispose();
            }
            finally
            {
                // 释放管理的图片资源
                if (managedImage != null && managedImage != processedImage)
                {
                    ImageResourceManager.ReleaseImage(managedImage);
                }
            }
            return processedImage;
        }

        /// <summary>
        /// 批量预处理文本内容
        /// </summary>
        private void PreprocessTextContent(UcContent content, List<TextCellInfo> cells)
        {
            // 并行处理文本内容，避免重复调用
            Parallel.ForEach(cells, item =>
            {
                if (!string.IsNullOrEmpty(item.words) || !string.IsNullOrEmpty(item.trans))
                {
                    item.TipText = content.GetTextByContent(item);
                }
            });
        }

        /// <summary>
        /// 绘制操作数据结构
        /// </summary>
        private class DrawingOperation
        {
            public Rectangle Rectangle { get; set; }
            public string Text { get; set; }
            public Font Font { get; set; }
            public bool DrawText { get; set; }
        }

        /// <summary>
        /// 准备绘制操作
        /// </summary>
        private List<DrawingOperation> PrepareDrawingOperations(List<TextCellInfo> cells, bool isShowTxt)
        {
            // 并行预处理绘制数据
            return cells.AsParallel()
                .Where(item => !(string.IsNullOrEmpty(item.words) && string.IsNullOrEmpty(item.trans)) && item.location != null)
                .Select(item => PrepareDrawingOperation(item, isShowTxt))
                .Where(op => op != null)
                .ToList();
        }

        /// <summary>
        /// 准备单个绘制操作
        /// </summary>
        private DrawingOperation PrepareDrawingOperation(TextCellInfo item, bool isShowTxt)
        {
            var rectangle = item.location.Rectangle;
            var operation = new DrawingOperation
            {
                Rectangle = rectangle,
                DrawText = false
            };

            if (isShowTxt && !string.IsNullOrEmpty(item.TipText))
            {
                operation.Text = item.TipText;
                operation.Font = CommonMethod.ScaleLabelByHeight(item.TipText, CommonString.GetUserNormalFont(5F), rectangle.Size);
                operation.DrawText = true;
            }

            return operation;
        }

        /// <summary>
        /// 执行绘制操作
        /// </summary>
        private void ExecuteDrawingOperation(Graphics g, DrawingOperation operation, SolidBrush brush, SolidBrush backgroundBrush)
        {
            // 绘制红色边框
            g.DrawRectangle(Pens.Red, operation.Rectangle.LocationOffset(-1, -1).SizeOffset(2));

            // 绘制文本（如果需要）
            if (operation.DrawText)
            {
                g.FillRectangle(backgroundBrush, operation.Rectangle);
                g.DrawString(operation.Text, operation.Font, brush, operation.Rectangle);
            }
        }

        #endregion
    }
}
