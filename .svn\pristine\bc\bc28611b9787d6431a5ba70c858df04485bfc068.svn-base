using System;
using System.IO;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace OCRTools
{
    public static class Log
    {
        static Type telemetryType;
        static object telemetryClient;
        static Type eventTelemetryType;
        static Type exceptionTelemetryType;

        public static void InitInsight()
        {
            try
            {
                var assembly = CommonString.LoadDllByName("未能加载文件或程序集“Microsoft.ApplicationInsights,");
                if (assembly != null)
                {
                    Type configType = assembly.GetType("Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration");
                    telemetryType = assembly.GetType("Microsoft.ApplicationInsights.TelemetryClient");

                    object config = configType.InvokeMember("CreateDefault", BindingFlags.InvokeMethod | BindingFlags.Static | BindingFlags.Public, null, null, null);
                    configType.GetMethod("set_InstrumentationKey", new[] { typeof(string) })?.Invoke(config, new object[] { "85f0eb3d-ee17-48b2-912a-195caf0207e9" });

                    telemetryClient = Activator.CreateInstance(telemetryType, config);

                    try
                    {
                        //初始化Content信息
                        var context = telemetryType.GetProperty("Context")?.GetValue(telemetryClient);
                        if (context != null)
                        {
                            var contentType = context.GetType();
                            CommonMethod.GetRequestHeader();
                            var device = contentType.GetProperty("Device")?.GetValue(context);
                            if (device != null)
                            {
                                device.GetType().GetProperty("Id")?.SetValue(device, CommonMethod.UserDomainName);
                                device.GetType().GetProperty("OperatingSystem")?.SetValue(device, Environment.OSVersion.VersionString);
                            }

                            var user = contentType.GetProperty("User")?.GetValue(context);
                            if (user != null)
                            {
                                user.GetType().GetProperty("Id")?.SetValue(user, CommonMethod.Identity);
                                user.GetType().GetProperty("AuthenticatedUserId")?.SetValue(user, CommonMethod.Identity);
                                user.GetType().GetProperty("AccountId")?.SetValue(user, string.IsNullOrEmpty(CommonSetting.用户名) ? CommonMethod.Identity : CommonSetting.用户名);
                            }

                            var component = contentType.GetProperty("Component")?.GetValue(context);
                            if (component != null)
                            {
                                component.GetType().GetProperty("Version")?.SetValue(component, CommonString.DtNowDate.ToDateStr("yyyy-MM-dd HH:mm:ss"));
                            }
                        }
                    }
                    catch { }
                    exceptionTelemetryType = assembly.GetType("Microsoft.ApplicationInsights.DataContracts.ExceptionTelemetry");
                    eventTelemetryType = assembly.GetType("Microsoft.ApplicationInsights.DataContracts.EventTelemetry");
                    TrackEvent("Start");
                }
            }
            catch { }
        }

        static bool IsInsightInit()
        {
            return telemetryClient != null && telemetryType != null;
        }

        public static void TrackEvent(string eventMsg, bool isSync = true)
        {
            if (!IsInsightInit())
            {
                return;
            }
            if (isSync)
            {
                Task.Factory.StartNew(() =>
                {
                    TraceEventNoSync(eventMsg);
                });
            }
            else
            {
                TraceEventNoSync(eventMsg);
            }
        }

        private static void TraceEventNoSync(string eventMsg)
        {
            try
            {
                var eventTelemetryInstance = Activator.CreateInstance(eventTelemetryType);
                eventTelemetryType.GetProperty("Name")?.SetValue(eventTelemetryInstance, eventMsg);
                telemetryType?.GetMethod("TrackEvent", new[] { eventTelemetryType })?.Invoke(telemetryClient, new[] { eventTelemetryInstance });
            }
            catch { }
        }

        public static void FlushInsight()
        {
            if (!IsInsightInit())
            {
                return;
            }
            try
            {
                telemetryType?.GetMethod("Flush")?.Invoke(telemetryClient, null);
            }
            catch { }

        }

        static bool TrackException(Exception oe, string eventMsg = "")
        {
            var result = false;
            if (!IsInsightInit())
            {
                return result;
            }
            try
            {
                var eventTelemetryInstance = Activator.CreateInstance(exceptionTelemetryType);
                exceptionTelemetryType.GetProperty("Exception")?.SetValue(eventTelemetryInstance, oe);
                exceptionTelemetryType.GetProperty("Message")?.SetValue(eventTelemetryInstance, (eventMsg + ",Msg:" + oe.Message).TrimStart(','));
                telemetryType?.GetMethod("TrackException", new[] { exceptionTelemetryType })?.Invoke(telemetryClient, new[] { eventTelemetryInstance });
                result = true;
            }
            catch { }
            return result;
        }

        private static readonly object Obj = new object();

        /// <summary>
        ///     操作日志
        /// </summary>
        public static void WriteLog(string content)
        {
            WriteLogs(content, "操作日志");
        }

        /// <summary>
        ///     错误日志
        /// </summary>
        public static void WriteError(string strMsg, Exception oe)
        {
            Task.Factory.StartNew(() =>
            {
                var traceNet = false;
                try
                {
                    traceNet = TrackException(oe, strMsg);
                }
                catch { }
                if (!traceNet)
                {
                    var strTmp = "";
                    if (!string.IsNullOrEmpty(strMsg)) strTmp = string.Format("\n{0}", strMsg);
                    try
                    {
                        if (oe != null)
                            strTmp += string.Format("\nMessage:{0}\nStackTrace:{1}\nTargetSite:{2}", oe.Message, oe.StackTrace,
                                oe.TargetSite);
                    }
                    catch { }
                    WriteLogs(strTmp, "错误日志");
                }
            });
        }

        private static void WriteLogs(string content, string type)
        {
            if (string.IsNullOrEmpty(content))
                return;
            try
            {
                lock (Obj)
                {
                    var path = AppDomain.CurrentDomain.BaseDirectory;
                    if (!string.IsNullOrEmpty(path))
                    {
                        path = AppDomain.CurrentDomain.BaseDirectory + "Logs";
                        if (!Directory.Exists(path)) Directory.CreateDirectory(path);
                        path = path + "\\" + ServerTime.DateTime.ToDateStr("yyMM");
                        if (!Directory.Exists(path)) Directory.CreateDirectory(path);
                        path = path + "\\" + ServerTime.DateTime.ToDateStr("dd") + ".txt";
                        if (!File.Exists(path))
                        {
                            var fs = File.Create(path);
                            fs.Close();
                        }

                        if (File.Exists(path))
                        {
                            var sw = new StreamWriter(path, true, Encoding.UTF8);
                            sw.WriteLine(ServerTime.DateTime.ToDateStr("yyyy-MM-dd HH:mm:ss fff"));
                            sw.WriteLine("日志类型：" + type);
                            sw.WriteLine("详情：" + content);
                            sw.WriteLine("----------------------------------------");
                            sw.Close();
                        }
                    }
                }
            }
            catch
            {
            }
        }
    }
}