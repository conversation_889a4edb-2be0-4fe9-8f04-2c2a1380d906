using System;
using System.Collections.Generic;
using System.Drawing;

namespace OCRTools
{
    public static class StaticValue
    {
        public static readonly Color DefaultBackColor = Color.FromArgb(240, 255, 240);
        public static readonly Color DefaultForeColor = Color.Black;

        public static readonly Color ShadowActiveColor = Color.FromArgb(0, 150, 207, 249);
        public static readonly Color ShadowNormalColor = Color.FromArgb(0, 206, 206, 206);

        public static Rectangle catchRectangle;

        public static List<IntPtr> Handles { get; set; } = new List<IntPtr>();

        public static string CatchName { get; set; } = "image";

        public static bool IsCatchScreen { get; set; }

    }
}