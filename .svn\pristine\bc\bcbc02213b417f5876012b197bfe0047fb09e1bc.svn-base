﻿using OCRTools.Common;
using OCRTools.ImgUpload;
using System.Collections.Specialized;
using System.IO;

namespace ImageLib
{
    /// <summary>
    /// </summary>
    internal class Net126Upload : BaseImageUpload
    {
        public Net126Upload()
        {
            Name = "126";
        }

        private const string strFileNameSpilt = "\"data\":\"";

        public override string GetResult(byte[] content, bool isZip = false)
        {
            var result = "";
            var url = "https://dun.163.com/node/api/upload-image.json";
            var file = new UploadFileInfo()
            {
                Name = "image",
                Filename = "1.png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            var vaules = new NameValueCollection() {
                    { "name", "1.png" }
                };
            var html = string.Empty;
            try
            {
                html = UploadFileRequest.Post(url, new[] { file }, vaules);
            }
            catch { }
            if (html.Contains(strFileNameSpilt))
            {
                result = html.Substring(html.IndexOf(strFileNameSpilt) + strFileNameSpilt.Length);
                result = result.Substring(0, result.IndexOf("\"")).Replace("\\/", "/");
            }
            return result;
        }
    }
}
