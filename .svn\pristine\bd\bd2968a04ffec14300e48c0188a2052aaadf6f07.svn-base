﻿using MetroFramework.Components;
using MetroFramework.Controls;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using MetroFramework.Native;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Security;
using System.Windows.Forms;

/**
 * MetroFramework - Modern UI for WinForms
 * 
 * The MIT License (MIT)
 * Copyright (c) 2011 <PERSON>, http://github.com/viperneo
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of 
 * this software and associated documentation files (the "Software"), to deal in the 
 * Software without restriction, including without limitation the rights to use, copy, 
 * modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, 
 * and to permit persons to whom the Software is furnished to do so, subject to the 
 * following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in 
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
 * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT 
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF 
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE 
 * OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

namespace MetroFramework.Forms
{
    public class MetroForm : Form, IDisposable, IMetroForm
    {
        private const int borderWidth = 5;

        private const int CS_DROPSHADOW = 131072;

        private const int WS_MINIMIZEBOX = 131072;

        private Bitmap _image;

        private bool _imageinvert;

        private Image backImage;

        private Padding backImagePadding;

        private BackLocation backLocation;

        private int backMaxSize;
        private bool displayHeader = true;

        private MetroColorStyle metroStyle = MetroColorStyle.Blue;

        private MetroThemeStyle metroTheme = MetroThemeStyle.Light;

        private Form shadowForm;
        private MetroFormShadowType shadowType = MetroFormShadowType.Flat;

        private Dictionary<WindowButtons, MetroFormButton> windowButtonList;

        public MetroForm()
        {
            SetStyle(
                ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.AllPaintingInWmPaint |
                ControlStyles.OptimizedDoubleBuffer, true);
            FormBorderStyle = FormBorderStyle.None;
            Name = "MetroForm";
            StartPosition = FormStartPosition.CenterScreen;
            TransparencyKey = Color.Lavender;
        }

        [Category("Metro Appearance")]
        [Browsable(true)]
        public MetroFormTextAlign TextAlign { get; set; }

        [Browsable(false)] public override Color BackColor => MetroPaint.BackColor.Form(Theme);

        [Browsable(true)]
        [Category("Metro Appearance")]
        [DefaultValue(MetroFormBorderStyle.None)]
        public MetroFormBorderStyle BorderStyle { get; set; }

        [Category("Metro Appearance")] public bool Movable { get; set; } = true;

        public new Padding Padding
        {
            get => base.Padding;
            set
            {
                value.Top = Math.Max(value.Top, DisplayHeader ? 60 : 30);
                base.Padding = value;
            }
        }

        protected override Padding DefaultPadding => new Padding(20, DisplayHeader ? 60 : 20, 20, 20);

        [Category("Metro Appearance")]
        [DefaultValue(true)]
        public bool DisplayHeader
        {
            get => displayHeader;
            set
            {
                if (value != displayHeader)
                {
                    var padding = base.Padding;
                    padding.Top += value ? 30 : -30;
                    base.Padding = padding;
                }

                displayHeader = value;
            }
        }

        [Category("Metro Appearance")] public bool Resizable { get; set; } = true;

        [DefaultValue(MetroFormShadowType.Flat)]
        [Category("Metro Appearance")]
        public MetroFormShadowType ShadowType
        {
            get
            {
                if (!IsMdiChild) return shadowType;
                return MetroFormShadowType.None;
            }
            set => shadowType = value;
        }

        [Browsable(false)]
        public new FormBorderStyle FormBorderStyle
        {
            get => base.FormBorderStyle;
            set => base.FormBorderStyle = value;
        }

        public new Form MdiParent
        {
            get => base.MdiParent;
            set
            {
                if (value != null)
                {
                    RemoveShadow();
                    shadowType = MetroFormShadowType.None;
                }

                base.MdiParent = value;
            }
        }

        [DefaultValue(null)]
        [Category("Metro Appearance")]
        public Image BackImage
        {
            get => backImage;
            set
            {
                backImage = value;
                if (value != null) _image = ApplyInvert(new Bitmap(value));
                Refresh();
            }
        }

        [Category("Metro Appearance")]
        public Padding BackImagePadding
        {
            get => backImagePadding;
            set
            {
                backImagePadding = value;
                Refresh();
            }
        }

        [Category("Metro Appearance")]
        public int BackMaxSize
        {
            get => backMaxSize;
            set
            {
                backMaxSize = value;
                Refresh();
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(BackLocation.TopLeft)]
        public BackLocation BackLocation
        {
            get => backLocation;
            set
            {
                backLocation = value;
                Refresh();
            }
        }

        [DefaultValue(true)]
        [Category("Metro Appearance")]
        public bool ApplyImageInvert
        {
            get => _imageinvert;
            set
            {
                _imageinvert = value;
                Refresh();
            }
        }

        protected override CreateParams CreateParams
        {
            get
            {
                var createParams = base.CreateParams;
                createParams.Style |= 131072;
                if (ShadowType == MetroFormShadowType.SystemShadow) createParams.ClassStyle |= 131072;
                return createParams;
            }
        }

        public MetroStyleManager MetroStyleManager { get; set; }

        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                if (StyleManager != null) return StyleManager.Style;
                return metroStyle;
            }
            set => metroStyle = value;
        }

        [Category("Metro Appearance")]
        public MetroThemeStyle Theme
        {
            get
            {
                if (StyleManager != null) return StyleManager.Theme;
                return metroTheme;
            }
            set => metroTheme = value;
        }

        [Browsable(false)]
        public MetroStyleManager StyleManager
        {
            get => MetroStyleManager;
            set => MetroStyleManager = value;
        }

        public event EventHandler<EventArgs> ThemeChange;

        public event EventHandler<EventArgs> StyleChange;

        protected override void Dispose(bool disposing)
        {
            if (disposing) RemoveShadow();
            base.Dispose(disposing);
        }

        public Bitmap ApplyInvert(Bitmap bitmapImage)
        {
            for (var i = 0; i < bitmapImage.Height; i++)
            for (var j = 0; j < bitmapImage.Width; j++)
            {
                var pixel = bitmapImage.GetPixel(j, i);
                _ = pixel.A;
                var b = (byte) (255 - pixel.R);
                var b2 = (byte) (255 - pixel.G);
                var b3 = (byte) (255 - pixel.B);
                if (b <= 0) b = 17;
                if (b2 <= 0) b2 = 17;
                if (b3 <= 0) b3 = 17;
                bitmapImage.SetPixel(j, i, Color.FromArgb(b, b2, b3));
            }

            return bitmapImage;
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            var color = MetroPaint.BackColor.Form(Theme);
            var foreColor = MetroPaint.ForeColor.Title(Theme);
            e.Graphics.Clear(color);
            using (var brush = MetroPaint.GetStyleBrush(Style))
            {
                var rect = new Rectangle(0, 0, Width, 5);
                e.Graphics.FillRectangle(brush, rect);
            }

            if (BorderStyle != 0)
            {
                var color2 = MetroPaint.BorderColor.Form(Theme);
                using (var pen = new Pen(color2))
                {
                    e.Graphics.DrawLines(pen, new Point[4]
                    {
                        new Point(0, 5),
                        new Point(0, Height - 1),
                        new Point(Width - 1, Height - 1),
                        new Point(Width - 1, 5)
                    });
                }
            }

            if (backImage != null && backMaxSize != 0)
            {
                var image = MetroImage.ResizeImage(backImage, new Rectangle(0, 0, backMaxSize, backMaxSize));
                if (_imageinvert)
                    image = MetroImage.ResizeImage(Theme == MetroThemeStyle.Dark ? _image : backImage,
                        new Rectangle(0, 0, backMaxSize, backMaxSize));
                switch (backLocation)
                {
                    case BackLocation.TopLeft:
                        e.Graphics.DrawImage(image, backImagePadding.Left, backImagePadding.Top);
                        break;
                    case BackLocation.TopRight:
                        e.Graphics.DrawImage(image, ClientRectangle.Right - (backImagePadding.Right + image.Width),
                            backImagePadding.Top);
                        break;
                    case BackLocation.BottomLeft:
                        e.Graphics.DrawImage(image, backImagePadding.Left,
                            ClientRectangle.Bottom - (image.Height + backImagePadding.Bottom));
                        break;
                    case BackLocation.BottomRight:
                        e.Graphics.DrawImage(image, ClientRectangle.Right - (backImagePadding.Right + image.Width),
                            ClientRectangle.Bottom - (image.Height + backImagePadding.Bottom));
                        break;
                }
            }

            if (displayHeader)
            {
                var bounds = new Rectangle(20, 20, ClientRectangle.Width - 40, 40);
                var flags = TextFormatFlags.EndEllipsis | GetTextFormatFlags();
                TextRenderer.DrawText(e.Graphics, Text, MetroFonts.Title, bounds, foreColor, flags);
            }

            if (Resizable && (SizeGripStyle == SizeGripStyle.Auto || SizeGripStyle == SizeGripStyle.Show))
                using (var brush2 = new SolidBrush(MetroPaint.ForeColor.Button.Disabled(Theme)))
                {
                    var size = new Size(2, 2);
                    e.Graphics.FillRectangles(brush2, new Rectangle[6]
                    {
                        new Rectangle(new Point(ClientRectangle.Width - 6, ClientRectangle.Height - 6), size),
                        new Rectangle(new Point(ClientRectangle.Width - 10, ClientRectangle.Height - 10), size),
                        new Rectangle(new Point(ClientRectangle.Width - 10, ClientRectangle.Height - 6), size),
                        new Rectangle(new Point(ClientRectangle.Width - 6, ClientRectangle.Height - 10), size),
                        new Rectangle(new Point(ClientRectangle.Width - 14, ClientRectangle.Height - 6), size),
                        new Rectangle(new Point(ClientRectangle.Width - 6, ClientRectangle.Height - 14), size)
                    });
                }
        }

        private TextFormatFlags GetTextFormatFlags()
        {
            switch (TextAlign)
            {
                case MetroFormTextAlign.Left:
                    return TextFormatFlags.Default;
                case MetroFormTextAlign.Center:
                    return TextFormatFlags.HorizontalCenter;
                case MetroFormTextAlign.Right:
                    return TextFormatFlags.Right;
                default:
                    throw new InvalidOperationException();
            }
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            base.OnClosing(e);
        }

        protected override void OnClosed(EventArgs e)
        {
            if (Owner != null) Owner = null;
            RemoveShadow();
            base.OnClosed(e);
        }

        [SecuritySafeCritical]
        public bool FocusMe()
        {
            return WinApi.SetForegroundWindow(Handle);
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            if (DesignMode) return;
            switch (StartPosition)
            {
                case FormStartPosition.CenterParent:
                    CenterToParent();
                    break;
                case FormStartPosition.CenterScreen:
                    if (IsMdiChild)
                        CenterToParent();
                    else
                        CenterToScreen();
                    break;
            }

            RemoveCloseButton();
            if (ControlBox)
            {
                AddWindowButton(WindowButtons.Close);
                if (MaximizeBox) AddWindowButton(WindowButtons.Maximize);
                if (MinimizeBox) AddWindowButton(WindowButtons.Minimize);
                UpdateWindowButtonPosition();
            }

            CreateShadow();
        }

        protected override void OnActivated(EventArgs e)
        {
            base.OnActivated(e);
            if (shadowType == MetroFormShadowType.AeroShadow && IsAeroThemeEnabled() && IsDropShadowSupported())
            {
                var attrValue = 2;
                DwmApi.DwmSetWindowAttribute(Handle, 2, ref attrValue, 4);
                var mARGINS = default(DwmApi.MARGINS);
                mARGINS.cyBottomHeight = 1;
                mARGINS.cxLeftWidth = 0;
                mARGINS.cxRightWidth = 0;
                mARGINS.cyTopHeight = 0;
                var marInset = mARGINS;
                DwmApi.DwmExtendFrameIntoClientArea(Handle, ref marInset);
            }
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);
            Invalidate();
        }

        protected override void OnResizeEnd(EventArgs e)
        {
            base.OnResizeEnd(e);
            UpdateWindowButtonPosition();
        }

        protected override void WndProc(ref Message m)
        {
            if (DesignMode)
            {
                base.WndProc(ref m);
                return;
            }

            switch (m.Msg)
            {
                case 274:
                    switch (m.WParam.ToInt32() & 0xFFF0)
                    {
                        case 61456:
                            if (!Movable) return;
                            break;
                    }

                    break;
                case 163:
                case 515:
                    if (!MaximizeBox) return;
                    break;
                case 132:
                {
                    var hitTest = HitTestNCA(m.HWnd, m.WParam, m.LParam);
                    if (hitTest != WinApi.HitTest.HTCLIENT)
                    {
                        m.Result = (IntPtr) (long) hitTest;
                        return;
                    }

                    break;
                }
            }

            base.WndProc(ref m);
            switch (m.Msg)
            {
                case 36:
                    OnGetMinMaxInfo(m.HWnd, m.LParam);
                    break;
                case 5:
                {
                    if (windowButtonList == null) break;
                    windowButtonList.TryGetValue(WindowButtons.Maximize, out var value);
                    if (value == null) break;
                    if (WindowState == FormWindowState.Normal)
                    {
                        if (shadowForm != null) shadowForm.Visible = true;
                        value.Text = "1";
                    }

                    if (WindowState == FormWindowState.Maximized) value.Text = "2";
                    break;
                }
            }
        }

        [SecuritySafeCritical]
        private unsafe void OnGetMinMaxInfo(IntPtr hwnd, IntPtr lParam)
        {
            var ptr = (WinApi.MINMAXINFO*) (void*) lParam;
            var screen = Screen.FromHandle(hwnd);
            ptr->ptMaxSize.x = screen.WorkingArea.Width;
            ptr->ptMaxSize.y = screen.WorkingArea.Height;
            ptr->ptMaxPosition.x = Math.Abs(screen.WorkingArea.Left - screen.Bounds.Left);
            ptr->ptMaxPosition.y = Math.Abs(screen.WorkingArea.Top - screen.Bounds.Top);
        }

        private WinApi.HitTest HitTestNCA(IntPtr hwnd, IntPtr wparam, IntPtr lparam)
        {
            var pt = new Point((short) (int) lparam, (short) ((int) lparam >> 16));
            var num = Math.Max(Padding.Right, Padding.Bottom);
            if (Resizable &&
                RectangleToScreen(new Rectangle(ClientRectangle.Width - num, ClientRectangle.Height - num, num, num))
                    .Contains(pt)) return WinApi.HitTest.HTBOTTOMRIGHT;
            if (RectangleToScreen(new Rectangle(5, 5, ClientRectangle.Width - 10, 50)).Contains(pt))
                return WinApi.HitTest.HTCAPTION;
            return WinApi.HitTest.HTCLIENT;
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            base.OnMouseDown(e);
            if (e.Button == MouseButtons.Left && Movable && WindowState != FormWindowState.Maximized &&
                Width - 5 > e.Location.X && e.Location.X > 5 && e.Location.Y > 5) MoveControl();
        }

        [SecuritySafeCritical]
        private void MoveControl()
        {
            WinApi.ReleaseCapture();
            WinApi.SendMessage(Handle, 161, 2, 0);
        }

        [SecuritySafeCritical]
        private bool IsAeroThemeEnabled()
        {
            if (Environment.OSVersion.Version.Major <= 5) return false;
            DwmApi.DwmIsCompositionEnabled(out var pfEnabled);
            return pfEnabled;
        }

        private bool IsDropShadowSupported()
        {
            if (Environment.OSVersion.Version.Major > 5) return SystemInformation.IsDropShadowEnabled;
            return false;
        }

        private void AddWindowButton(WindowButtons button)
        {
            if (windowButtonList == null) windowButtonList = new Dictionary<WindowButtons, MetroFormButton>();
            if (windowButtonList.ContainsKey(button)) return;
            var metroFormButton = new MetroFormButton();
            switch (button)
            {
                case WindowButtons.Close:
                    metroFormButton.Text = "r";
                    break;
                case WindowButtons.Minimize:
                    metroFormButton.Text = "0";
                    break;
                case WindowButtons.Maximize:
                    if (WindowState == FormWindowState.Normal)
                        metroFormButton.Text = "1";
                    else
                        metroFormButton.Text = "2";
                    break;
            }

            metroFormButton.Style = Style;
            metroFormButton.Theme = Theme;
            metroFormButton.Tag = button;
            metroFormButton.Size = new Size(25, 20);
            metroFormButton.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            metroFormButton.TabStop = false;
            metroFormButton.Click += WindowButton_Click;
            Controls.Add(metroFormButton);
            windowButtonList.Add(button, metroFormButton);
        }

        private void WindowButton_Click(object sender, EventArgs e)
        {
            var metroFormButton = sender as MetroFormButton;
            if (metroFormButton == null) return;
            switch ((WindowButtons) metroFormButton.Tag)
            {
                case WindowButtons.Close:
                    Close();
                    break;
                case WindowButtons.Minimize:
                    WindowState = FormWindowState.Minimized;
                    break;
                case WindowButtons.Maximize:
                    if (WindowState == FormWindowState.Normal)
                    {
                        WindowState = FormWindowState.Maximized;
                        metroFormButton.Text = "2";
                    }
                    else
                    {
                        WindowState = FormWindowState.Normal;
                        metroFormButton.Text = "1";
                    }

                    break;
            }
        }

        private void UpdateWindowButtonPosition()
        {
            if (ControlBox)
            {
                var dictionary = new Dictionary<int, WindowButtons>(3)
                {
                    {0, WindowButtons.Close},
                    {1, WindowButtons.Maximize},
                    {2, WindowButtons.Minimize}
                };
                var dictionary2 = dictionary;
                var location = new Point(ClientRectangle.Width - 5 - 25, 5);
                var num = location.X - 25;
                MetroFormButton metroFormButton = null;
                if (windowButtonList.Count == 1)
                    foreach (var windowButton in windowButtonList)
                        windowButton.Value.Location = location;
                else
                    foreach (var item in dictionary2)
                    {
                        var flag = windowButtonList.ContainsKey(item.Value);
                        if (metroFormButton == null && flag)
                        {
                            metroFormButton = windowButtonList[item.Value];
                            metroFormButton.Location = location;
                        }
                        else if (metroFormButton != null && flag)
                        {
                            windowButtonList[item.Value].Location = new Point(num, 5);
                            num -= 25;
                        }
                    }

                Refresh();
            }
        }

        private void CreateShadow()
        {
            switch (ShadowType)
            {
                case MetroFormShadowType.None:
                    break;
                case MetroFormShadowType.Flat:
                    shadowForm = new MetroFlatDropShadow(this);
                    break;
                case MetroFormShadowType.DropShadow:
                    shadowForm = new MetroRealisticDropShadow(this);
                    break;
            }
        }

        private void RemoveShadow()
        {
            if (shadowForm != null && !shadowForm.IsDisposed)
            {
                shadowForm.Visible = false;
                Owner = shadowForm.Owner;
                shadowForm.Owner = null;
                shadowForm.Dispose();
                shadowForm = null;
            }
        }

        [SecuritySafeCritical]
        public void RemoveCloseButton()
        {
            var systemMenu = WinApi.GetSystemMenu(Handle, false);
            if (!(systemMenu == IntPtr.Zero))
            {
                var menuItemCount = WinApi.GetMenuItemCount(systemMenu);
                if (menuItemCount > 0)
                {
                    WinApi.RemoveMenu(systemMenu, (uint) (menuItemCount - 1), 5120u);
                    WinApi.RemoveMenu(systemMenu, (uint) (menuItemCount - 2), 5120u);
                    WinApi.DrawMenuBar(Handle);
                }
            }
        }

        private Rectangle MeasureText(Graphics g, Rectangle clientRectangle, Font font, string text,
            TextFormatFlags flags)
        {
            var proposedSize = new Size(int.MaxValue, int.MinValue);
            var size = TextRenderer.MeasureText(g, text, font, proposedSize, flags);
            return new Rectangle(clientRectangle.X, clientRectangle.Y, size.Width, size.Height);
        }

        private enum WindowButtons
        {
            Minimize,
            Maximize,
            Close
        }

        private class MetroFormButton : Button, IMetroControl
        {
            private bool isHovered;

            private bool isPressed;
            private MetroColorStyle metroStyle;

            private MetroThemeStyle metroTheme;

            public MetroFormButton()
            {
                SetStyle(
                    ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.AllPaintingInWmPaint |
                    ControlStyles.OptimizedDoubleBuffer, true);
            }

            [DefaultValue(false)]
            [Category("Metro Appearance")]
            public bool UseCustomBackColor { get; set; }

            [DefaultValue(false)]
            [Category("Metro Appearance")]
            public bool UseCustomForeColor { get; set; }

            [DefaultValue(false)]
            [Category("Metro Appearance")]
            public bool UseStyleColors { get; set; }

            [Browsable(false)]
            [Category("Metro Behaviour")]
            [DefaultValue(false)]
            public bool UseSelectable
            {
                get => GetStyle(ControlStyles.Selectable);
                set => SetStyle(ControlStyles.Selectable, value);
            }

            [Category("Metro Appearance")]
            [DefaultValue(MetroColorStyle.Blue)]
            public MetroColorStyle Style
            {
                get
                {
                    if (DesignMode || metroStyle != 0) return metroStyle;
                    if (StyleManager != null) return StyleManager.Style;
                    if (StyleManager == null) return MetroColorStyle.Blue;
                    return metroStyle;
                }
                set => metroStyle = value;
            }

            [Category("Metro Appearance")]
            [DefaultValue(MetroThemeStyle.Light)]
            public MetroThemeStyle Theme
            {
                get
                {
                    if (DesignMode || metroTheme != 0) return metroTheme;
                    if (StyleManager != null) return StyleManager.Theme;
                    if (StyleManager == null) return MetroThemeStyle.Light;
                    return metroTheme;
                }
                set => metroTheme = value;
            }

            [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
            [Browsable(false)]
            public MetroStyleManager StyleManager { get; set; }

            [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

            [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaint;

            [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

            protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
            {
                if (GetStyle(ControlStyles.UserPaint) && CustomPaintBackground != null) CustomPaintBackground(this, e);
            }

            protected virtual void OnCustomPaint(MetroPaintEventArgs e)
            {
                if (GetStyle(ControlStyles.UserPaint) && CustomPaint != null) CustomPaint(this, e);
            }

            protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
            {
                if (GetStyle(ControlStyles.UserPaint) && CustomPaintForeground != null) CustomPaintForeground(this, e);
            }

            protected override void OnPaint(PaintEventArgs e)
            {
                var theme = Theme;
                Color color;
                if (Parent != null)
                {
                    if (!(Parent is IMetroForm))
                    {
                        color = !(Parent is IMetroControl) ? Parent.BackColor : MetroPaint.GetStyleColor(Style);
                    }
                    else
                    {
                        theme = ((IMetroForm) Parent).Theme;
                        color = MetroPaint.BackColor.Form(theme);
                    }
                }
                else
                {
                    color = MetroPaint.BackColor.Form(theme);
                }

                Color foreColor;
                if (isHovered && !isPressed && Enabled)
                {
                    foreColor = MetroPaint.ForeColor.Button.Normal(theme);
                    color = MetroPaint.BackColor.Button.Normal(theme);
                }
                else if (isHovered && isPressed && Enabled)
                {
                    foreColor = MetroPaint.ForeColor.Button.Press(theme);
                    color = MetroPaint.GetStyleColor(Style);
                }
                else if (!Enabled)
                {
                    foreColor = MetroPaint.ForeColor.Button.Disabled(theme);
                    color = MetroPaint.BackColor.Button.Disabled(theme);
                }
                else
                {
                    foreColor = MetroPaint.ForeColor.Button.Normal(theme);
                }

                e.Graphics.Clear(color);
                var font = new Font("Webdings", 9.25f);
                TextRenderer.DrawText(e.Graphics, Text, font, ClientRectangle, foreColor, color,
                    TextFormatFlags.EndEllipsis | TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
            }

            protected override void OnMouseEnter(EventArgs e)
            {
                isHovered = true;
                Invalidate();
                base.OnMouseEnter(e);
            }

            protected override void OnMouseDown(MouseEventArgs e)
            {
                if (e.Button == MouseButtons.Left)
                {
                    isPressed = true;
                    Invalidate();
                }

                base.OnMouseDown(e);
            }

            protected override void OnMouseUp(MouseEventArgs e)
            {
                isPressed = false;
                Invalidate();
                base.OnMouseUp(e);
            }

            protected override void OnMouseLeave(EventArgs e)
            {
                isHovered = false;
                Invalidate();
                base.OnMouseLeave(e);
            }
        }

        protected abstract class MetroShadowBase : Form
        {
            protected const int WS_EX_TRANSPARENT = 32;

            protected const int WS_EX_LAYERED = 524288;

            protected const int WS_EX_NOACTIVATE = 134217728;

            private const int TICKS_PER_MS = 10000;

            private const long RESIZE_REDRAW_INTERVAL = 10000000L;

            private readonly int shadowSize;

            private readonly int wsExStyle;

            private bool isBringingToFront;

            private long lastResizedOn;

            protected MetroShadowBase(Form targetForm, int shadowSize, int wsExStyle)
            {
                TargetForm = targetForm;
                this.shadowSize = shadowSize;
                this.wsExStyle = wsExStyle;
                TargetForm.Activated += OnTargetFormActivated;
                //TargetForm.Activated += TargetForm_LostFocus;
                //TargetForm.LostFocus += TargetForm_LostFocus;
                TargetForm.ResizeBegin += OnTargetFormResizeBegin;
                TargetForm.ResizeEnd += OnTargetFormResizeEnd;
                TargetForm.VisibleChanged += OnTargetFormVisibleChanged;
                TargetForm.SizeChanged += OnTargetFormSizeChanged;
                TargetForm.Move += OnTargetFormMove;
                TargetForm.Resize += OnTargetFormResize;

                if (TargetForm.Owner != null) Owner = TargetForm.Owner;
                TargetForm.Owner = this;
                MaximizeBox = false;
                MinimizeBox = false;
                ShowInTaskbar = false;
                ShowIcon = false;
                FormBorderStyle = FormBorderStyle.None;
                Bounds = GetShadowBounds();
            }

            protected Form TargetForm { get; }

            protected override CreateParams CreateParams
            {
                get
                {
                    //const int WS_EX_NOACTIVATE = 0x08000000;
                    //const int WS_CHILD = 0x40000000;
                    //CreateParams cp = base.CreateParams;
                    //cp.Style |= WS_CHILD;
                    //cp.ExStyle |= WS_EX_NOACTIVATE;
                    //return cp;
                    var createParams = base.CreateParams;
                    createParams.ExStyle |= wsExStyle;
                    createParams.ExStyle |= 0x08000000;
                    //createParams.Style |= 0x40000000;
                    return createParams;
                }
            }

            private bool IsResizing => lastResizedOn > 0;

            //private void TargetForm_LostFocus(object sender, EventArgs e)
            //{
            //    if (this.TopMost != TargetForm.TopMost)
            //    {
            //        this.TopMost = TargetForm.TopMost;
            //    }
            //}

            private Rectangle GetShadowBounds()
            {
                var bounds = TargetForm.Bounds;
                bounds.Inflate(shadowSize, shadowSize);
                return bounds;
            }

            protected abstract void PaintShadow();

            protected abstract void ClearShadow();

            protected override void OnDeactivate(EventArgs e)
            {
                base.OnDeactivate(e);
                isBringingToFront = true;
            }

            private void OnTargetFormActivated(object sender, EventArgs e)
            {
                if (Visible) Update();
                if (isBringingToFront)
                {
                    Visible = true;
                    isBringingToFront = false;
                }
                else
                {
                    BringToFront();
                }
            }

            private void OnTargetFormVisibleChanged(object sender, EventArgs e)
            {
                Visible = TargetForm.Visible && TargetForm.WindowState != FormWindowState.Minimized;
                Update();
            }

            private void OnTargetFormResizeBegin(object sender, EventArgs e)
            {
                lastResizedOn = DateTime.Now.Ticks;
            }

            private void OnTargetFormMove(object sender, EventArgs e)
            {
                if (!TargetForm.Visible || TargetForm.WindowState != 0)
                    Visible = false;
                else
                    Bounds = GetShadowBounds();
            }

            private void OnTargetFormResize(object sender, EventArgs e)
            {
                ClearShadow();
            }

            private void OnTargetFormSizeChanged(object sender, EventArgs e)
            {
                Bounds = GetShadowBounds();
                if (!IsResizing) PaintShadowIfVisible();
            }

            private void OnTargetFormResizeEnd(object sender, EventArgs e)
            {
                lastResizedOn = 0L;
                PaintShadowIfVisible();
            }

            private void PaintShadowIfVisible()
            {
                if (TargetForm.Visible && TargetForm.WindowState != FormWindowState.Minimized) PaintShadow();
            }
        }

        protected class MetroAeroDropShadow : MetroShadowBase
        {
            public MetroAeroDropShadow(Form targetForm)
                : base(targetForm, 0, 134217760)
            {
                FormBorderStyle = FormBorderStyle.SizableToolWindow;
            }

            protected override void SetBoundsCore(int x, int y, int width, int height, BoundsSpecified specified)
            {
                if (specified != BoundsSpecified.Size) base.SetBoundsCore(x, y, width, height, specified);
            }

            protected override void PaintShadow()
            {
                Visible = true;
            }

            protected override void ClearShadow()
            {
            }
        }

        protected class MetroFlatDropShadow : MetroShadowBase
        {
            private Point Offset = new Point(-6, -6);

            public MetroFlatDropShadow(Form targetForm)
                : base(targetForm, 6, 134742048)
            {
            }

            protected override void OnLoad(EventArgs e)
            {
                base.OnLoad(e);
                PaintShadow();
            }

            protected override void OnPaint(PaintEventArgs e)
            {
                Visible = true;
                PaintShadow();
            }

            protected override void PaintShadow()
            {
                using (var bitmap = DrawBlurBorder())
                {
                    SetBitmap(bitmap, byte.MaxValue);
                }
            }

            protected override void ClearShadow()
            {
                var bitmap = new Bitmap(Width, Height, PixelFormat.Format32bppArgb);
                var graphics = Graphics.FromImage(bitmap);
                graphics.Clear(Color.Transparent);
                graphics.Flush();
                graphics.Dispose();
                SetBitmap(bitmap, byte.MaxValue);
                bitmap.Dispose();
            }

            [SecuritySafeCritical]
            private void SetBitmap(Bitmap bitmap, byte opacity)
            {
                if (bitmap.PixelFormat != PixelFormat.Format32bppArgb)
                    throw new ApplicationException("The bitmap must be 32ppp with alpha-channel.");
                var dC = WinApi.GetDC(IntPtr.Zero);
                var intPtr = WinApi.CreateCompatibleDC(dC);
                var intPtr2 = IntPtr.Zero;
                var hObject = IntPtr.Zero;
                try
                {
                    intPtr2 = bitmap.GetHbitmap(Color.FromArgb(0));
                    hObject = WinApi.SelectObject(intPtr, intPtr2);
                    var psize = new WinApi.SIZE(bitmap.Width, bitmap.Height);
                    var pprSrc = new WinApi.POINT(0, 0);
                    var pptDst = new WinApi.POINT(Left, Top);
                    var pblend = default(WinApi.BLENDFUNCTION);
                    pblend.BlendOp = 0;
                    pblend.BlendFlags = 0;
                    pblend.SourceConstantAlpha = opacity;
                    pblend.AlphaFormat = 1;
                    WinApi.UpdateLayeredWindow(Handle, dC, ref pptDst, ref psize, intPtr, ref pprSrc, 0, ref pblend, 2);
                }
                finally
                {
                    WinApi.ReleaseDC(IntPtr.Zero, dC);
                    if (intPtr2 != IntPtr.Zero)
                    {
                        WinApi.SelectObject(intPtr, hObject);
                        WinApi.DeleteObject(intPtr2);
                    }

                    WinApi.DeleteDC(intPtr);
                }
            }

            private Bitmap DrawBlurBorder()
            {
                return (Bitmap) DrawOutsetShadow(Color.Black,
                    new Rectangle(0, 0, ClientRectangle.Width, ClientRectangle.Height));
            }

            private Image DrawOutsetShadow(Color color, Rectangle shadowCanvasArea)
            {
                var rect = shadowCanvasArea;
                var rect2 = new Rectangle(shadowCanvasArea.X + (-Offset.X - 1), shadowCanvasArea.Y + (-Offset.Y - 1),
                    shadowCanvasArea.Width - (-Offset.X * 2 - 1), shadowCanvasArea.Height - (-Offset.Y * 2 - 1));
                var bitmap = new Bitmap(rect.Width, rect.Height, PixelFormat.Format32bppArgb);
                var graphics = Graphics.FromImage(bitmap);
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                using (Brush brush = new SolidBrush(Color.FromArgb(30, Color.Black)))
                {
                    graphics.FillRectangle(brush, rect);
                }

                using (Brush brush2 = new SolidBrush(Color.FromArgb(60, Color.Black)))
                {
                    graphics.FillRectangle(brush2, rect2);
                }

                graphics.Flush();
                graphics.Dispose();
                return bitmap;
            }
        }

        protected class MetroRealisticDropShadow : MetroShadowBase
        {
            public MetroRealisticDropShadow(Form targetForm)
                : base(targetForm, 15, 134742048)
            {
            }

            protected override void OnLoad(EventArgs e)
            {
                base.OnLoad(e);
                PaintShadow();
            }

            protected override void OnPaint(PaintEventArgs e)
            {
                Visible = true;
                PaintShadow();
            }

            protected override void PaintShadow()
            {
                using (var bitmap = DrawBlurBorder())
                {
                    SetBitmap(bitmap, byte.MaxValue);
                }
            }

            protected override void ClearShadow()
            {
                var bitmap = new Bitmap(Width, Height, PixelFormat.Format32bppArgb);
                var graphics = Graphics.FromImage(bitmap);
                graphics.Clear(Color.Transparent);
                graphics.Flush();
                graphics.Dispose();
                SetBitmap(bitmap, byte.MaxValue);
                bitmap.Dispose();
            }

            [SecuritySafeCritical]
            private void SetBitmap(Bitmap bitmap, byte opacity)
            {
                if (bitmap.PixelFormat != PixelFormat.Format32bppArgb)
                    throw new ApplicationException("The bitmap must be 32ppp with alpha-channel.");
                var dC = WinApi.GetDC(IntPtr.Zero);
                var intPtr = WinApi.CreateCompatibleDC(dC);
                var intPtr2 = IntPtr.Zero;
                var hObject = IntPtr.Zero;
                try
                {
                    intPtr2 = bitmap.GetHbitmap(Color.FromArgb(0));
                    hObject = WinApi.SelectObject(intPtr, intPtr2);
                    var psize = new WinApi.SIZE(bitmap.Width, bitmap.Height);
                    var pprSrc = new WinApi.POINT(0, 0);
                    var pptDst = new WinApi.POINT(Left, Top);
                    var bLENDFUNCTION = default(WinApi.BLENDFUNCTION);
                    bLENDFUNCTION.BlendOp = 0;
                    bLENDFUNCTION.BlendFlags = 0;
                    bLENDFUNCTION.SourceConstantAlpha = opacity;
                    bLENDFUNCTION.AlphaFormat = 1;
                    var pblend = bLENDFUNCTION;
                    WinApi.UpdateLayeredWindow(Handle, dC, ref pptDst, ref psize, intPtr, ref pprSrc, 0, ref pblend, 2);
                }
                finally
                {
                    WinApi.ReleaseDC(IntPtr.Zero, dC);
                    if (intPtr2 != IntPtr.Zero)
                    {
                        WinApi.SelectObject(intPtr, hObject);
                        WinApi.DeleteObject(intPtr2);
                    }

                    WinApi.DeleteDC(intPtr);
                }
            }

            private Bitmap DrawBlurBorder()
            {
                return (Bitmap) DrawOutsetShadow(0, 0, 40, 1, Color.Black,
                    new Rectangle(1, 1, ClientRectangle.Width, ClientRectangle.Height));
            }

            private Image DrawOutsetShadow(int hShadow, int vShadow, int blur, int spread, Color color,
                Rectangle shadowCanvasArea)
            {
                var rectangle = shadowCanvasArea;
                var rectangle2 = shadowCanvasArea;
                rectangle2.Offset(hShadow, vShadow);
                rectangle2.Inflate(-blur, -blur);
                rectangle.Inflate(spread, spread);
                rectangle.Offset(hShadow, vShadow);
                var rectangle3 = rectangle;
                var bitmap = new Bitmap(rectangle3.Width, rectangle3.Height, PixelFormat.Format32bppArgb);
                var graphics = Graphics.FromImage(bitmap);
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                var cornerRadius = 0;
                do
                {
                    var num = (rectangle.Height - rectangle2.Height) / (double) (blur * 2 + spread * 2);
                    var fillColor = Color.FromArgb((int) (200.0 * (num * num)), color);
                    var bounds = rectangle2;
                    bounds.Offset(-rectangle3.Left, -rectangle3.Top);
                    DrawRoundedRectangle(graphics, bounds, cornerRadius, Pens.Transparent, fillColor);
                    rectangle2.Inflate(1, 1);
                    cornerRadius = (int) (blur * (1.0 - num * num));
                } while (rectangle.Contains(rectangle2));

                graphics.Flush();
                graphics.Dispose();
                return bitmap;
            }

            private void DrawRoundedRectangle(Graphics g, Rectangle bounds, int cornerRadius, Pen drawPen,
                Color fillColor)
            {
                var num = Convert.ToInt32(Math.Ceiling(drawPen.Width));
                bounds = Rectangle.Inflate(bounds, -num, -num);
                var graphicsPath = new GraphicsPath();
                if (cornerRadius > 0)
                {
                    graphicsPath.AddArc(bounds.X, bounds.Y, cornerRadius, cornerRadius, 180f, 90f);
                    graphicsPath.AddArc(bounds.X + bounds.Width - cornerRadius, bounds.Y, cornerRadius, cornerRadius,
                        270f, 90f);
                    graphicsPath.AddArc(bounds.X + bounds.Width - cornerRadius, bounds.Y + bounds.Height - cornerRadius,
                        cornerRadius, cornerRadius, 0f, 90f);
                    graphicsPath.AddArc(bounds.X, bounds.Y + bounds.Height - cornerRadius, cornerRadius, cornerRadius,
                        90f, 90f);
                }
                else
                {
                    graphicsPath.AddRectangle(bounds);
                }

                graphicsPath.CloseAllFigures();
                if (cornerRadius > 5)
                    using (var brush = new SolidBrush(fillColor))
                    {
                        g.FillPath(brush, graphicsPath);
                    }

                if (drawPen != Pens.Transparent)
                    using (var pen = new Pen(drawPen.Color))
                    {
                        var lineCap3 = pen.EndCap = pen.StartCap = LineCap.Round;
                        g.DrawPath(pen, graphicsPath);
                    }
            }
        }
    }

    public enum MetroFormBorderStyle
    {
        None,
        FixedSingle
    }

    public enum MetroFormTextAlign
    {
        Left,
        Center,
        Right
    }

    public enum BackLocation
    {
        TopLeft,
        TopRight,
        BottomLeft,
        BottomRight
    }
}