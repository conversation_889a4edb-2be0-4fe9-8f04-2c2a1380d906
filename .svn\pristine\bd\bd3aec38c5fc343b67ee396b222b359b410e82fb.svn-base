﻿using System.ComponentModel;
using System.Drawing;

namespace OCRTools.UserControlEx
{
	[ToolboxItem(false)]
	public class TabStyleDefaultProvider : TabStyleProvider
	{
		public TabStyleDefaultProvider(TabControlExtra tabControl)
			: base(tabControl)
		{
			base.Radius = 2;
			base.SelectedTabIsLarger = true;
			base.TabColorHighLighted1 = Color.FromArgb(236, 244, 252);
			base.TabColorHighLighted2 = Color.FromArgb(221, 237, 252);
			base.PageBackgroundColorHighlighted = base.TabColorHighLighted1;
		}
	}
}
