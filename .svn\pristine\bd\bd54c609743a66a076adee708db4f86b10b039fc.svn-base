using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolRectangle : ToolObject
    {
        private DrawRectangle _drawRectangle;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.CurrentToolType = drawArea.ActiveTool;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else
            {
                _drawRectangle = new DrawRectangle(e.X, e.Y, 1, 1);
                AddNewObject(drawArea, _drawRectangle);
            }
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left)
            {
                if (_drawRectangle == null)
                {
                    drawArea.ActiveTool = DrawToolType.Text;
                    return;
                }

                _drawRectangle.IsSelected = true;
                var obj = _drawRectangle;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    _drawRectangle.MoveHandleTo(e.Location, 5, drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (_drawRectangle != null)
            {
                StaticValue.CurrentRectangle = _drawRectangle.Rectangle;
                if (!_drawRectangle.Rectangle.IsLimt())
                {
                    drawArea.GraphicsList.RemoveAt(0);
                    return;
                }

                var obj = _drawRectangle;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    _drawRectangle.Normalize();
                    drawArea.AddCommandToHistory(new CommandAdd(_drawRectangle));
                }
            }
        }
    }
}