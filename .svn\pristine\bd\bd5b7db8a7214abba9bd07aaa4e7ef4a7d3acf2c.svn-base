﻿using System.Drawing;
using System.Reflection;

namespace ShareX.ScreenCaptureLib
{

    [Obfuscation]
    public class AnnotationOptions
    {
        public static readonly Color PrimaryColor = Color.FromArgb(242, 60, 60);
        public static readonly Color SecondaryColor = Color.White;
        public static readonly Color TransparentColor = Color.FromArgb(0, 0, 0, 0);

        [Obfuscation]
        public int RegionCornerRadius { get; set; } = 0;

        [Obfuscation]
        public Color BorderColor { get; set; } = PrimaryColor;

        [Obfuscation]
        public int BorderSize { get; set; } = 5;

        [Obfuscation]
        public BorderStyle BorderStyle { get; set; } = BorderStyle.实线;

        [Obfuscation]
        public Color FillColor { get; set; } = TransparentColor;

        [Obfuscation]
        public int DrawingCornerRadius { get; set; } = 3;

        [Obfuscation]
        public bool Shadow { get; set; } = false;

        [Obfuscation]
        public Color ShadowColor { get; set; } = Color.FromArgb(125, 0, 0, 0);

        [Obfuscation]
        public Point ShadowOffset { get; set; } = new Point(0, 1);

        // Line, arrow drawing
        [Obfuscation]
        public int LineCenterPointCount { get; set; } = 1;

        // Arrow drawing
        [Obfuscation]
        public ArrowHeadDirection ArrowHeadDirection { get; set; } = ArrowHeadDirection.结束箭头;

        // Text (Outline) drawing
        [Obfuscation]
        public TextDrawingOptions TextOutlineOptions { get; set; } = new TextDrawingOptions()
        {
            Color = SecondaryColor,
            Size = 25,
            Bold = true
        };

        [Obfuscation]
        public Color TextOutlineBorderColor { get; set; } = PrimaryColor;

        [Obfuscation]
        public int TextOutlineBorderSize { get; set; } = 5;

        // Text (Background) drawing
        [Obfuscation]
        public TextDrawingOptions TextOptions { get; set; } = new TextDrawingOptions()
        {
            Color = SecondaryColor,
            Size = 25,
            Bold = true
        };

        [Obfuscation]
        public Color TextBorderColor { get; set; } = SecondaryColor;

        [Obfuscation]
        public int TextBorderSize { get; set; } = 0;

        [Obfuscation]
        public Color TextFillColor { get; set; } = PrimaryColor;

        // Image drawing
        [Obfuscation]
        public ImageInterpolationMode ImageInterpolationMode = ImageInterpolationMode.NearestNeighbor;

        // Step drawing
        [Obfuscation]
        public Color StepBorderColor { get; set; } = SecondaryColor;

        [Obfuscation]
        public int StepBorderSize { get; set; } = 0;

        [Obfuscation]
        public Color StepFillColor { get; set; } = PrimaryColor;

        [Obfuscation]
        public int StepFontSize { get; set; } = 18;

        [Obfuscation]
        public StepType StepType { get; set; } = StepType.数字;

        // Magnify drawing
        [Obfuscation]
        public int MagnifyStrength { get; set; } = 200;

        /// <summary>
        /// 像素模糊
        /// </summary>
        [Obfuscation]
        public int PixelateSize { get; set; } = 10;

        /// <summary>
        /// 高亮颜色
        /// </summary>
        [Obfuscation]
        public Color HighlightColor { get; set; } = Color.Yellow;
    }
}