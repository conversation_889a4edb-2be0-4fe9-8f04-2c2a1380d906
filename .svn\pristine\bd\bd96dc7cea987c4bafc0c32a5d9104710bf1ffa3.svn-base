// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using System.Runtime.InteropServices;
using UIAComWrapperInternal;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public sealed class AutomationElement
    {
        public static readonly AutomationProperty BoundingRectangleProperty =
            AutomationElementIdentifiers.BoundingRectangleProperty;

        public static readonly AutomationProperty ClassNameProperty = AutomationElementIdentifiers.ClassNameProperty;

        public static readonly AutomationProperty ClickablePointProperty =
            AutomationElementIdentifiers.ClickablePointProperty;

        public static readonly AutomationProperty
            ControlTypeProperty = AutomationElementIdentifiers.ControlTypeProperty;

        public static readonly AutomationProperty HasKeyboardFocusProperty =
            AutomationElementIdentifiers.HasKeyboardFocusProperty;

        public static readonly AutomationProperty IsContentElementProperty =
            AutomationElementIdentifiers.IsContentElementProperty;

        public static readonly AutomationProperty IsControlElementProperty =
            AutomationElementIdentifiers.IsControlElementProperty;

        public static readonly AutomationProperty IsKeyboardFocusableProperty =
            AutomationElementIdentifiers.IsKeyboardFocusableProperty;

        public static readonly AutomationProperty
            IsOffscreenProperty = AutomationElementIdentifiers.IsOffscreenProperty;

        public static readonly AutomationProperty IsPasswordProperty = AutomationElementIdentifiers.IsPasswordProperty;

        public static readonly AutomationProperty NameProperty = AutomationElementIdentifiers.NameProperty;

        public static readonly AutomationProperty NativeWindowHandleProperty =
            AutomationElementIdentifiers.NativeWindowHandleProperty;

        public static readonly object NotSupported = AutomationElementIdentifiers.NotSupported;

        public static readonly AutomationProperty
            OrientationProperty = AutomationElementIdentifiers.OrientationProperty;

        internal AutomationElement(IUIAutomationElement obj)
        {
            Debug.Assert(obj != null);
            NativeElement = obj;
        }

        public AutomationElementInformation Current => new AutomationElementInformation(this, false);

        public IUIAutomationElement NativeElement { get; }

        internal static AutomationElement Wrap(IUIAutomationElement obj)
        {
            return obj == null ? null : new AutomationElement(obj);
        }

        public override bool Equals(object obj)
        {
            var element = obj as AutomationElement;
            return obj != null && element != null &&
                   Automation.Factory.CompareElements(NativeElement, element.NativeElement) != 0;
        }

        public AutomationElementCollection FindAll(TreeScope scope, Condition condition)
        {
            Utility.ValidateArgumentNonNull(condition, "condition");

            try
            {
                var elemArray =
                    NativeElement.FindAllBuildCache(
                        (UIAutomationClient.TreeScope) scope,
                        condition.NativeCondition,
                        CacheRequest.CurrentNativeCacheRequest);
                return AutomationElementCollection.Wrap(elemArray);
            }
            catch (COMException e)
            {
                if (Utility.ConvertException(e, out var newEx))
                    throw newEx;
                throw;
            }
        }

        public static AutomationElement FromHandle(IntPtr hwnd)
        {
            Utility.ValidateArgument(hwnd != IntPtr.Zero, "Hwnd cannot be null");
            try
            {
                var element =
                    Automation.Factory.ElementFromHandleBuildCache(hwnd, CacheRequest.CurrentNativeCacheRequest);
                return Wrap(element);
            }
            catch (COMException e)
            {
                if (Utility.ConvertException(e, out var newEx))
                    throw newEx;
                throw;
            }
        }

        public static AutomationElement FromPoint(Point pt)
        {
            try
            {
                var element =
                    Automation.Factory.ElementFromPointBuildCache(
                        Utility.PointManagedToNative(pt),
                        CacheRequest.CurrentNativeCacheRequest);
                return Wrap(element);
            }
            catch (COMException e)
            {
                if (Utility.ConvertException(e, out var newEx))
                    throw newEx;
                throw;
            }
        }

        public object GetCachedPropertyValue(AutomationProperty property)
        {
            return GetCachedPropertyValue(property, false);
        }

        public object GetCachedPropertyValue(AutomationProperty property, bool ignoreDefaultValue)
        {
            Utility.ValidateArgumentNonNull(property, "property");

            try
            {
                var obj = NativeElement.GetCachedPropertyValueEx(property.Id, ignoreDefaultValue ? 1 : 0);
                return Utility.WrapObjectAsProperty(property, obj);
            }
            catch (COMException e)
            {
                if (Utility.ConvertException(e, out var newEx))
                    throw newEx;
                throw;
            }
        }

        public object GetCurrentPropertyValue(AutomationProperty property)
        {
            return GetCurrentPropertyValue(property, false);
        }

        public object GetCurrentPropertyValue(AutomationProperty property, bool ignoreDefaultValue)
        {
            Utility.ValidateArgumentNonNull(property, "property");
            try
            {
                var obj = NativeElement.GetCurrentPropertyValueEx(property.Id, ignoreDefaultValue ? 1 : 0);
                return Utility.WrapObjectAsProperty(property, obj);
            }
            catch (COMException e)
            {
                if (Utility.ConvertException(e, out var newEx))
                    throw newEx;
                throw;
            }
        }

        internal object GetPropertyValue(AutomationProperty property, bool cached)
        {
            if (cached)
                return GetCachedPropertyValue(property);
            return GetCurrentPropertyValue(property);
        }

        public override int GetHashCode()
        {
            var runtimeId = GetRuntimeId();
            var num = 0;
            if (runtimeId == null) throw new InvalidOperationException("Operation cannot be performed");
            foreach (var i in runtimeId) num = (num * 4) ^ i;
            return num;
        }

        internal object GetRawPattern(AutomationPattern pattern, bool isCached)
        {
            try
            {
                if (isCached)
                    return NativeElement.GetCachedPattern(pattern.Id);
                return NativeElement.GetCurrentPattern(pattern.Id);
            }
            catch (COMException e)
            {
                if (Utility.ConvertException(e, out var newEx))
                    throw newEx;
                throw;
            }
        }

        public int[] GetRuntimeId()
        {
            try
            {
                return (int[]) NativeElement.GetRuntimeId();
            }
            catch (COMException e)
            {
                if (Utility.ConvertException(e, out var newEx))
                    throw newEx;
                throw;
            }
        }

        public static bool operator ==(AutomationElement left, AutomationElement right)
        {
            if (Equals(left, null)) return Equals(right, null);
            if (Equals(right, null)) return Equals(left, null);
            return left.Equals(right);
        }

        public static bool operator !=(AutomationElement left, AutomationElement right)
        {
            return !(left == right);
        }

        public bool TryGetCurrentPattern(AutomationPattern pattern, out object patternObject)
        {
            patternObject = null;
            Utility.ValidateArgumentNonNull(pattern, "pattern");
            try
            {
                var nativePattern = NativeElement.GetCurrentPattern(pattern.Id);
                patternObject = Utility.WrapObjectAsPattern(this, nativePattern, pattern, false /* cached */);
                return patternObject != null;
            }
            catch (COMException e)
            {
                if (Utility.ConvertException(e, out var newEx))
                    throw newEx;
                throw;
            }
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct AutomationElementInformation
        {
            private readonly AutomationElement _el;
            private readonly bool _isCached;

            internal AutomationElementInformation(AutomationElement el, bool isCached)
            {
                _el = el;
                _isCached = isCached;
            }

            public ControlType ControlType => (ControlType) _el.GetPropertyValue(ControlTypeProperty, _isCached);

            public string Name => (string) _el.GetPropertyValue(NameProperty, _isCached);
            public bool HasKeyboardFocus => (bool) _el.GetPropertyValue(HasKeyboardFocusProperty, _isCached);
            public bool IsKeyboardFocusable => (bool) _el.GetPropertyValue(IsKeyboardFocusableProperty, _isCached);
            public Rect BoundingRectangle => (Rect) _el.GetPropertyValue(BoundingRectangleProperty, _isCached);
            public bool IsControlElement => (bool) _el.GetPropertyValue(IsControlElementProperty, _isCached);
            public bool IsContentElement => (bool) _el.GetPropertyValue(IsContentElementProperty, _isCached);
            public bool IsPassword => (bool) _el.GetPropertyValue(IsPasswordProperty, _isCached);
            public string ClassName => (string) _el.GetPropertyValue(ClassNameProperty, _isCached);
            public int NativeWindowHandle => (int) _el.GetPropertyValue(NativeWindowHandleProperty, _isCached);
            public bool IsOffscreen => (bool) _el.GetPropertyValue(IsOffscreenProperty, _isCached);
        }
    }
}