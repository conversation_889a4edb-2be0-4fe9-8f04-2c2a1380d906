﻿using MetroFramework.Forms;
using MetroFramework.Native;
using OCRTools.Common;
using OCRTools.Properties;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Cache;
using System.Reflection;
using System.Security;
using System.Security.Principal;
using System.Threading;
using System.Threading.Tasks;
using System.Web.Script.Serialization;
using System.Windows.Forms;

// ReSharper disable All

namespace OCRTools
{
    public class CommonString
    {
        public const string StrDefaultDesc = "请按下快捷键";
        public const string StrDefaultImgType = "png";
        public const string StrDefaultTxtType = "txt";

        ////连接超时  
        //public const int INTERNET_OPTION_CONNECT_TIMEOUT = 2;
        //public const int INTERNET_OPTION_CONNECT_RETRIES = 3;
        ////送信超时时间  
        //public const int INTERNET_OPTION_SEND_TIMEOUT = 5;
        ////受信超时时间  
        //public const int INTERNET_OPTION_RECEIVE_TIMEOUT = 6;
        //public const int INTERNET_OPEN_TYPE_PRECONFIG = 0;//使用 IE 中的连接设置
        //public const int INTERNET_OPEN_TYPE_DIRECT = 1;//直接连接到服务器
        //public const int INTERNET_OPEN_TYPE_PROXY = 3;//通过代理服务器进行连接
        //public const int INTERNET_OPTION_MAX_CONNS_PER_SERVER = 73;//最大连接数
        //public const int INTERNET_OPTION_MAX_CONNS_PER_1_0_SERVER = 74;//最大连接数

        public const string StrServerHost = "ocr.oldfish.cn";

        public static string DataPath =
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + "\\OCR\\";

        public static string DefaultRecPath = string.Format("{0}Rec\\", DataPath);

        public static string DefaultDLLPath = string.Format("{0}Widget\\", DataPath);

        public static string DefaultImagePath = string.Format("{0}Image\\", DataPath);

        public static string HeadImagePath = string.Format("{0}Head\\", DataPath);

        public static string DefaultTmpImagePath = string.Format("{0}Tmp\\", DataPath);

        public static MetroFormShadowType CommonShadowType;

        public static StringFormat BaseStringFormat =
            new StringFormat(StringFormatFlags.MeasureTrailingSpaces |
                             StringFormatFlags.LineLimit); //StringFormatFlags.NoWrap |

        public static TextFormatFlags BaseTextFormatFlags =
            TextFormatFlags.NoPadding | TextFormatFlags.TextBoxControl | TextFormatFlags.SingleLine |
            TextFormatFlags.NoPrefix;

        public static Point PointZero = new Point(0, 0);

        public static Padding PaddingZero = Padding.Empty;

        //public static double NowFontLineHeight = 60;

        public static JavaScriptSerializer JavaScriptSerializer = new JavaScriptSerializer();

        public static string StrServerIp;

        public static List<string> LstCanProcessFilesExt = new List<string>
            {"png", "jpg", "jpeg", "bmp", "gif", "pdf", "doc", "docx", "txt"}; //, "xls", "xlsx", "ppt", "pptx"

        public static List<string> LstCanProcessImageFilesExt = new List<string> { "png", "jpg", "jpeg", "bmp", "gif" };

        public static List<string>
            LstCanProcessDocFilesExt = new List<string> { "pdf", "doc", "docx", "txt" }; //, "xls", "xlsx", "ppt", "pptx"

        private static string _strUpdateUrl;

        private static string _hostAccountUrl = "";

        private static string _autoCodeUrl;
        public static bool IsOnLine = true;

        public static Random RndTmp = new Random();

        private static readonly List<string> LstRndHost = new List<string>
            {"www.sina.cn", "g.cn", "www.microsoft.com", "www.baidu.com", "www.360.cn", "www.bing.cn"};

        private static List<string> lstNotFindDll = new List<string>()
            {"Could not load file or assembly", "未能加载文件或程序集"};

        private static Dictionary<string, byte[]> dicAssemblyDictionary = new Dictionary<string, byte[]>();

        static CommonString()
        {
            //CommonShadowType = MetroFramework.Forms.MetroFormShadowType.AeroShadow;
            InitShadow();
            try
            {
                InitPath();
            }
            catch
            {
            }
        }

        public static bool IsDarkModel { get; set; }

        //private static void InitTaskRegister()
        //{
        //    if (CommonString.IsAdministrator && !TaskRegister.IsExits(".task"))
        //    {
        //        TaskRegister.AddReg(Assembly.GetExecutingAssembly().Location, ".task");
        //    }
        //}

        public static bool IsBeta { get; set; }

        public static int UpdateUrlPort { get; set; }

        public static string StrUpdateUrl
        {
            get
            {
                if (string.IsNullOrEmpty(_strUpdateUrl) && !string.IsNullOrEmpty(StrServerIp) && UpdateUrlPort > 0)
                    _strUpdateUrl = string.Format("http://{0}:{1}/", StrServerIp, UpdateUrlPort);
                return _strUpdateUrl;
            }
            set => _strUpdateUrl = value;
        }

        public static bool IsAdministrator { get; set; }

        public static string StrCommonEncryptKey => "!(*_^%$#";

        public static int AccountUrlPort { get; set; }

        public static string HostAccountUrl
        {
            get
            {
                if (string.IsNullOrEmpty(_hostAccountUrl) && !string.IsNullOrEmpty(StrServerIp) && AccountUrlPort > 0)
                    _hostAccountUrl = string.Format("http://{0}:{1}/", StrServerIp, AccountUrlPort);
                return _hostAccountUrl;
            }
            set => _hostAccountUrl = value;
        }

        public static int CodeUrlPort { get; set; }

        public static string AutoCodeUrl
        {
            get
            {
                if (string.IsNullOrEmpty(_autoCodeUrl) && !string.IsNullOrEmpty(StrServerIp) && CodeUrlPort > 0)
                    _autoCodeUrl = string.Format("http://{0}:{1}/", StrServerIp, CodeUrlPort);
                return _autoCodeUrl;
            }
            set => _autoCodeUrl = value;
        }

        //public static string strApplicationPath => Application.StartupPath.TrimEnd('\\');

        public static DateTime DtNowDate { get; set; }
        public static string StrNowVersion { get; set; }

        //public static bool Is64 = Environment.Is64BitOperatingSystem;

        public static bool IsExit { get; set; }
        public static string StrBadSoft { get; set; }
        public static string StrBadSoftDesc { get; set; }

        public static readonly Semaphore OcrMutex = new Semaphore(1, 1);
        //public static readonly Mutex OcrMutex = new Mutex(true, "Ocr");

        public static bool IsOnRec { get; internal set; }

        /// <summary>
        /// 鼠标是否正在划词翻译按钮上
        /// </summary>
        public static bool IsOnOcrButton { get; set; }

        /// <summary>
        /// 鼠标是否正在划词翻译弹出框中
        /// </summary>
        public static bool IsOnOcrSearch { get; set; }

        internal static string UpdateFileName => "update.xml";

        public static string BrowserPath { get; set; } = "";

        private static string GetRndHost(string url)
        {
            return !string.IsNullOrEmpty(url) && url.Contains(StrServerIp) ? LstRndHost.GetRndItem() : string.Empty;
        }

        private static string strOcrHost;

        public static void SetRandomHost(HttpWebRequest request, string url)
        {
            var host = GetRndHost(url);
            if (!string.IsNullOrEmpty(host))
            {
                request.Host = host;
                if (string.IsNullOrEmpty(strOcrHost))
                {
                    strOcrHost = string.Format("OcrAgent/{0}", StrNowVersion);
                }
                request.UserAgent = strOcrHost;
            }
            else
            {
                request.UserAgent =
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Safari/537.36";
            }
        }

        public static void InitPath()
        {
            if (!Directory.Exists(DataPath)) Directory.CreateDirectory(DataPath);
            if (!Directory.Exists(DefaultImagePath)) Directory.CreateDirectory(DefaultImagePath);
            if (!Directory.Exists(DefaultTmpImagePath)) Directory.CreateDirectory(DefaultTmpImagePath);
            if (!Directory.Exists(HeadImagePath)) Directory.CreateDirectory(HeadImagePath);
            if (!Directory.Exists(DefaultDLLPath)) Directory.CreateDirectory(DefaultDLLPath);
        }

        private static void InitShadow()
        {
            try
            {
                if (Environment.OSVersion.Version.Major <= 5)
                    CommonShadowType = MetroFormShadowType.Flat;
                else if (IsAeroThemeEnabled() && IsDropShadowSupported())
                    CommonShadowType = MetroFormShadowType.AeroShadow;
                else
                    CommonShadowType = MetroFormShadowType.DropShadow;
            }
            catch
            {
            }
        }

        [SecuritySafeCritical]
        private static bool IsAeroThemeEnabled()
        {
            DwmApi.DwmIsCompositionEnabled(out var pfEnabled);
            return pfEnabled;
        }

        private static bool IsDropShadowSupported()
        {
            return SystemInformation.IsDropShadowEnabled;
        }

        public static void InitString()
        {
            InitSetttingsBeforeStart();
            //InitTaskRegister();
        }

        public static void InitSetttingsBeforeStart()
        {
            IsAdministrator = IsAdmini();
            //CommonString.InitCurl();
            try
            {
                //CommonMethod.CheckFrameWork();
                ServicePointManager.ServerCertificateValidationCallback =
                    (sender, certificate, chain, sslPolicyErrors) => true;
                //ServicePointManager.Expect100Continue = false;
                ServicePointManager.DefaultConnectionLimit = int.MaxValue;
                //if (ServicePointManager.DefaultConnectionLimit < 200)
                //{
                //    ServicePointManager.DefaultConnectionLimit = 200;
                //}
                //ServicePointManager.MaxServicePoints = int.MaxValue;
                ServicePointManager.MaxServicePointIdleTime = int.MaxValue;
                ServicePointManager.UseNagleAlgorithm = false;
                //ServicePointManager.CheckCertificateRevocationList = false;
                ServicePointManager.Expect100Continue = false;
                //ServicePointManager.SetTcpKeepAlive(true, 1000, 200);
                WebRequest.DefaultWebProxy = null;
                WebRequest.DefaultCachePolicy = new HttpRequestCachePolicy(HttpRequestCacheLevel.NoCacheNoStore);
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | (SecurityProtocolType)768 |
                                                       (SecurityProtocolType)3072 | SecurityProtocolType.Tls;
            }
            catch
            {
                // ignored
            }

            //try
            //{
            //    if (!CommonString.isDebug)
            //        NetworkSetting.SetDNS(new string[] { "***************" });
            //}
            //catch (Exception oe)
            //{
            //    Log.WriteError(oe);
            //}
            try
            {
                var asm = Assembly.GetExecutingAssembly(); //如果是当前程序集
                var asmdis =
                    (AssemblyDescriptionAttribute)Attribute.GetCustomAttribute(asm,
                        typeof(AssemblyDescriptionAttribute));
                DtNowDate = DateTime.Parse(SubString(asmdis.Description, "(").Replace("(", "").Replace(")", ""));
                StrNowVersion = asm.GetName().Version.ToString();
            }
            catch
            {
                // ignored
            }

            StrServerIp = DnsHelper.GetDnsIp(StrServerHost);
            if (string.IsNullOrEmpty(StrServerIp)) StrServerIp = "**************";
            Task.Factory.StartNew(() =>
            {
                var strTmp = DnsHelper.InitByHost(StrServerHost);
                if (!string.IsNullOrEmpty(strTmp))
                {
                    StrServerIp = strTmp;
                }
            });
            //SetMaxNetWork();

            ////result = string.Format("{0},{1},{2},{3}", Type.GetHashCode(), StrName, StrUrl, IsDefault.ToString());
            //string str = "1,打码1";//1,打码1|2,打码2
            ////str = CommonEncryptHelper.DES3Encrypt(str, CommonString.StrCommonEncryptKey);
            //str = "http://***************:9001/";// "http://yzm.oldfish.cn/";//http://ymz.oldfish.cn/ // //http://img.jzjiu.com/
            //////str = "http://mzy.oldfish.cn/";//http://myz.oldfish.cn/
            //////str = "http://zym.oldfish.cn/";//http://zmy.oldfish.cn/
            //str = CommonEncryptHelper.DES3Encrypt(str, CommonString.StrCommonEncryptKey);

            ////抓包软件加密Key[Soft.txt]
            //var str = "[=====]360安全浏览器\r\nspoon\r\nspy\r\n-未完成订单";
            ////string str = "http://{0}.oldfish.cn/[a1]|\n121.42.84.169\n443";//http://img.jingyangchun.com/customerpic/{0}/|\n114.215.143.92\n21";//\nhttp://{0}.chinacloudsites.cn/
            //str = CommonMethod.Encrypto(str);
            //str = CommonEncryptHelper.DESEncrypt(str, CommonString.StrCommonEncryptKey);
            //str = "";

            //str = "http://{0}.oldfish.cn/[a2]|\n121.42.106.227\n443";
            //str = CommonMethod.Encrypto(str);
            //str = CommonEncryptHelper.DESEncrypt(str, CommonString.StrCommonEncryptKey);
            //str = "";
            //str = "http://{0}.oldfish.cn/[a3]|\n121.42.93.212\n443";
            //str = CommonMethod.Encrypto(str);
            //str = CommonEncryptHelper.DESEncrypt(str, CommonString.StrCommonEncryptKey);
            //str = "";
        }

        public static string SubString(string strSource, string strSpilt, string strEnd = "")
        {
            return strSource.SubStringHorspool(strSpilt, strEnd).Trim();
        }

        public static bool IsAdmini()
        {
            var result = false;
            try
            {
                var current = WindowsIdentity.GetCurrent();
                var windowsPrincipal = new WindowsPrincipal(current);
                result = windowsPrincipal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch (Exception)
            {
                //Log.WriteError("判断是否以管理员运行出错！", oe);
            }

            return result;
        }

        public static void RunAsAdmin(string exeFile = null, string arguments = null, bool isAdmin = true)
        {
            try
            {
                using (var process = new Process())
                {
                    if (string.IsNullOrEmpty(exeFile)) exeFile = Application.ExecutablePath;
                    var psi = new ProcessStartInfo
                    {
                        FileName = exeFile,
                        Arguments = arguments ?? string.Empty,
                        UseShellExecute = true,
                        Verb = isAdmin ? "runas" : string.Empty
                    };

                    process.StartInfo = psi;
                    process.Start();
                }
            }
            catch
            {
            }
        }

        internal static string StrUpdateFile(bool isCreate = true)
        {
            var updateFilePath = string.Format("{0}\\{1:yyyyMMddHHmmssfff}update.exe", Path.GetTempPath().TrimEnd('\\'),
                ServerTime.DateTime);
            if (File.Exists(updateFilePath))
                try
                {
                    File.Delete(updateFilePath);
                }
                catch
                {
                }

            if (isCreate && !File.Exists(updateFilePath))
                try
                {
                    File.WriteAllBytes(updateFilePath, Resources.UpdateFile);
                }
                catch
                {
                }

            return updateFilePath;
        }

        internal static Assembly LoadDllByName(string strMessage, bool isDownLoad = false)
        {
            var dllName = GetNotFindDll(strMessage);
            if (!string.IsNullOrEmpty(dllName))
            {
                if (!isDownLoad)
                {
                    isDownLoad = lstNotFindDll.Exists(p => strMessage.Contains(p));
                }

                var byts = GetDllByName(dllName, isDownLoad);
                if (byts != null && byts.Length > 0)
                {
                    return Assembly.Load(byts);
                }
            }

            return null;
        }

        private static string GetNotFindDll(string expMessage)
        {
            //未能加载文件或程序集“O2S.Components.PDFRender4NET, Version=5.0.3.0, Culture=neutral, PublicKeyToken=6753860be21d84fb”或它的某一个依赖项。系统找不到指定的文件。
            //Could not load file or assembly 'System.Core, Version=3.5​.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089'
            var result = string.Empty;
            result = CommonMethod.SubString(expMessage, "“", ",")?.Trim();
            if (string.IsNullOrEmpty(result))
                result = CommonMethod.SubString(expMessage, "'", ",")?.Trim();
            if (string.IsNullOrEmpty(result))
                result = CommonMethod.SubString(expMessage, "\"", ",")?.Trim();
            if (string.IsNullOrEmpty(result))
                result = CommonMethod.SubString(expMessage, "", ",")?.Trim();
            return result;
        }

        private static byte[] GetDllByName(string fileName, bool isCreate = true)
        {
            if (string.IsNullOrEmpty(fileName))
            {
                return null;
            }

            if (dicAssemblyDictionary.ContainsKey(fileName))
            {
                return dicAssemblyDictionary[fileName];
            }

            byte[] result = null;
            var dllName = string.Format("{0}\\{1}.dat", DefaultDLLPath, fileName);

            if (File.Exists(dllName))
            {
                try
                {
                    result = File.ReadAllBytes(dllName);
                    //File.WriteAllBytes("zip.dat", CommonEncryptHelper.Compress(result));
                }
                catch
                {
                }
            }

            if (isCreate && (result == null || result.Length <= 0))
            {
                try
                {
                    dicAssemblyDictionary.Add(fileName, result);
                    var urlPath = string.Format("{0}ext/{1}.txt", StrUpdateUrl, fileName);
                    result = new CnnWebClient().DownloadData(urlPath);
                    if (result != null && result.Length > 0)
                    {
                        result = CommonEncryptHelper.Decompress(result);
                        File.WriteAllBytes(dllName, result);
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine(e.Message);
                }
                finally
                {
                    lock (dicAssemblyDictionary)
                    {
                        if (result == null || result.Length <= 0)
                            dicAssemblyDictionary.Remove(fileName);
                        else
                            dicAssemblyDictionary[fileName] = result;
                    }
                }
            }

            return result;
        }

        #region 取色板相关

        public const int RecentColorsMax = 32;
        public static List<Color> RecentColors { get; set; } = new List<Color>();

        #endregion

    }

    public static class ListExtensions
    {
        public static T GetRndItem<T>(this ICollection<T> lstItem)
        {
            return lstItem.OrderBy(arg => Guid.NewGuid()).FirstOrDefault();
        }
    }
}