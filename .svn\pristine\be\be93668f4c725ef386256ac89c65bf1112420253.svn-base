using System.Collections;
using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class AutomationElementCollection : ICollection
    {
        private readonly IUIAutomationElementArray _obj;

        internal AutomationElementCollection(IUIAutomationElementArray obj)
        {
            Debug.Assert(obj != null);
            _obj = obj;
        }

        public AutomationElement this[int index] => AutomationElement.Wrap(_obj.GetElement(index));

        public virtual void CopyTo(Array array, int index)
        {
            var cElem = _obj.Length;
            for (var i = 0; i < cElem; ++i) array.SetValue(this[i], i + index);
        }

        public IEnumerator GetEnumerator()
        {
            return new AutomationElementCollectionEnumerator(_obj);
        }

        public int Count => _obj.Length;

        public virtual bool IsSynchronized => false;

        public virtual object SyncRoot => this;

        public static AutomationElementCollection Wrap(IUIAutomationElementArray obj)
        {
            return obj == null ? null : new AutomationElementCollection(obj);
        }
    }

    public class AutomationElementCollectionEnumerator : IEnumerator
    {
        private readonly int _cElem;
        private readonly IUIAutomationElementArray _obj;
        private int _index = -1;

        #region IEnumerator Members

        internal AutomationElementCollectionEnumerator(IUIAutomationElementArray obj)
        {
            Debug.Assert(obj != null);
            _obj = obj;
            _cElem = obj.Length;
        }

        public object Current => AutomationElement.Wrap(_obj.GetElement(_index));

        public bool MoveNext()
        {
            if (_index < _cElem - 1)
            {
                ++_index;
                return true;
            }

            return false;
        }

        public void Reset()
        {
            _index = -1;
        }

        #endregion
    }
}