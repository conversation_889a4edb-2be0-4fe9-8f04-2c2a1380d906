using System.Drawing;
using System.Drawing.Drawing2D;

namespace OCRTools
{
    internal class DrawEllipse : DrawRectangle
    {
        public DrawEllipse()
            : this(0, 0, 1, 1)
        {
        }

        public DrawEllipse(int x, int y, int width, int height)
        {
            Rectangle = new Rectangle(x, y, width, height);
            Initialize();
        }

        public override DrawToolType NoteType => DrawToolType.Ellipse;

        public override DrawObject Clone()
        {
            var drawEllipse = new DrawEllipse
            {
                Rectangle = Rectangle
            };
            FillDrawObjectFields(drawEllipse);
            return drawEllipse;
        }

        public override void Draw(Graphics g)
        {
            g.SmoothingMode = SmoothingMode.HighQuality;
            var normalizedRectangle = GetNormalizedRectangle(Rectangle);
            if (normalizedRectangle.IsLimt())
            {
                var pen = new Pen(Color.White)
                {
                    Color = Color,
                    Width = PenWidth.DPIValue()
                };
                if (IsDot)
                    pen.DashPattern = new float[2]
                    {
                        3f,
                        3f
                    };
                g.DrawEllipse(pen, GetNormalizedRectangle(Rectangle));
                pen.Dispose();
            }
        }

        public override bool PointInObject(Point point)
        {
            var normalizedRectangle = Rectangle.GetNormalizedRectangle();
            var rectangle = new Rectangle(normalizedRectangle.X - 5, normalizedRectangle.Y - 5,
                normalizedRectangle.Width + 10, normalizedRectangle.Height + 10);
            var rectangle2 = new Rectangle(normalizedRectangle.X + 5, normalizedRectangle.Y + 5,
                normalizedRectangle.Width - 10, normalizedRectangle.Height - 10);
            if (ContainsE(rectangle, point) && !ContainsE(rectangle2, point)) return true;
            return false;
        }

        public bool ContainsE(Rectangle rectangle, Point pt)
        {
            var graphicsPath = new GraphicsPath();
            graphicsPath.AddEllipse(rectangle);
            if (graphicsPath.IsVisible(pt))
            {
                graphicsPath.Dispose();
                return true;
            }

            graphicsPath.Dispose();
            return false;
        }
    }
}