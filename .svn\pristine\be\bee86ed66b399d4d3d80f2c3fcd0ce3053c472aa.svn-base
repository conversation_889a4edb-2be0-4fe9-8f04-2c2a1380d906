﻿using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Linq;

namespace OCRTools
{
    public class WindowsList
    {
        private readonly string[] ignoreList = {"Progman", "Button"};
        private List<WindowInfo> windows;

        public WindowsList()
        {
            IgnoreWindows = new List<IntPtr>();
        }

        public WindowsList(IntPtr ignoreWindow) : this()
        {
            IgnoreWindows.Add(ignoreWindow);
        }

        public List<IntPtr> IgnoreWindows { get; set; }

        public List<WindowInfo> GetWindowsList()
        {
            windows = new List<WindowInfo>();
            NativeMethods.EnumWindowsProc ewp = EvalWindows;
            NativeMethods.EnumWindows(ewp, IntPtr.Zero);
            return windows;
        }

        public List<WindowInfo> GetVisibleWindowsList()
        {
            var windows = GetWindowsList();

            return windows.Where(IsValidWindow).ToList();
        }

        private bool IsValidWindow(WindowInfo window)
        {
            return window != null && window.GetIsVisible() && !string.IsNullOrEmpty(window.GetText()) &&
                   IsClassNameAllowed(window) && window.GetRectangle().IsValid();
        }

        private bool IsClassNameAllowed(WindowInfo window)
        {
            var className = window.GetClassName();

            if (!string.IsNullOrEmpty(className))
                return ignoreList.All(ignore => !className.Equals(ignore, StringComparison.OrdinalIgnoreCase));

            return true;
        }

        private bool EvalWindows(IntPtr hWnd, IntPtr lParam)
        {
            if (IgnoreWindows.Any(window => hWnd == window)) return true;

            windows.Add(new WindowInfo(hWnd));

            return true;
        }
    }
}