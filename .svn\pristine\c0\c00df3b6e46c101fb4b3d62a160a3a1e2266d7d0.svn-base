﻿using System;
using System.Windows.Forms;

namespace OCRTools
{
    public class ControlHider : IDisposable
    {
        private readonly Timer _timer;

        public ControlHider(Control control, int autoHideTime)
        {
            Control = control;
            AutoHideTime = autoHideTime;

            _timer = new Timer
            {
                Interval = AutoHideTime
            };
            _timer.Tick += Timer_Tick;
        }

        public Control Control { get; }
        public int AutoHideTime { get; }

        public void Dispose()
        {
            _timer?.Dispose();
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            _timer.Stop();

            if (Control != null && !Control.IsDisposed) Control.Visible = false;
        }

        public void Show()
        {
            if (Control != null && !Control.IsDisposed)
            {
                Control.Visible = true;

                _timer.Stop();
                _timer.Start();
            }
        }
    }
}