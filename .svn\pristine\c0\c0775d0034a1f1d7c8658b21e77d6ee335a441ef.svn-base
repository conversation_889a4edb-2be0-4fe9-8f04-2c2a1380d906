using System.Collections.Generic;

namespace OCRTools
{
    internal class CommandDelete : Command
    {
        private readonly List<DrawObject> cloneList;

        private DrawArea drawAreaT;

        public CommandDelete(GraphicsList graphicsList, DrawArea drawArea)
        {
            drawAreaT = drawArea;
            cloneList = new List<DrawObject>();
            foreach (var item in graphicsList.Selection) cloneList.Add(item.Clone());
        }

        public override void Undo(GraphicsList list)
        {
            list.UnselectAll();
            foreach (var clone in cloneList) list.Add(clone);
        }

        public override void Redo(GraphicsList list)
        {
            var count = list.Count;
            for (var num = count - 1; num >= 0; num--)
            {
                var flag = false;
                var drawObject = list[num];
                foreach (var clone in cloneList)
                    if (drawObject.ID == clone.ID)
                    {
                        flag = true;
                        break;
                    }

                if (flag) list.RemoveAt(num);
            }
        }
    }
}