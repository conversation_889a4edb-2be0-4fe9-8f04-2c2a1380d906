﻿using OCRTools;
using OCRTools.Common;
using System;
using System.Collections.Specialized;
using System.IO;
using System.Reflection;

namespace ImageLib
{
    /// <summary>
    /// PnnLAB
    /// </summary>
    public class QiNiuUpload
    {
        public static bool Enable { get; set; } = true;

        private const string strFileNameSpilt = "origin_url\":\"";
        private const string strFileNameSpilt2 = "output_file\":\"";

        private static string GetToken(string key)
        {
            var html = WebClientExt.GetHtml("https://cdn.xbyham.com/common/gettoken?key=xcx/20220324/215900979&sid=&t=" + ServerTime.DateTime.Ticks);
            if (!string.IsNullOrEmpty(html) && html.Length > 2)
            {
                return CommonMethod.SubString(html, "\"uptoken\":\"", "\"");
            }

            return null;
        }

        internal static string GetResult(byte[] content, bool isZip = false)
        {
            var result = string.Empty;
            var key = string.Format("xcx/{0}/{1}", ServerTime.DateTime.ToString("yyyyMMdd"), ServerTime.DateTime.Ticks);
            var token = GetToken(key);

            if (string.IsNullOrEmpty(token))
                return result;
            var file = new UploadFileInfo()
            {
                Name = "file",
                Filename = "1.png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            var vaules = new NameValueCollection() {
                    { "key", key } ,
                    { "token", token }
                };
            try
            {
                var html = UploadFileRequest.Post("https://up-z2.qbox.me", new[] { file
    }, vaules);
                if (html?.Contains(key) == true)
                {
                    result = string.Format("http://qiniu.xbyham.com/{0}?1.png", key);
                }
            }
            catch { }
            return result;
        }
    }
}
