﻿using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Language;
using OCRTools.Properties;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Net;
using System.Threading;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FormUpdate : MetroForm
    {
        private string _tmpCacheFile;
        private bool _isCancel;

        private bool _isOpenDownLoad = true;

        public string AppName { get; set; }

        public string AppPath { get; set; }

        public bool IsUpdateMode { get; set; } = true;

        public bool IsNeedUnZip { get; set; }

        public bool IsCanUserUpdate { get; set; } = true;

        public string StrUpdateMode => IsUpdateMode ? "更新" : "安装";

        internal UpdateEntity UpdateInfo { get; set; }

        public bool IsAutoStart { get; set; }

        public bool IsNeedClearFolder { get; set; }

        public bool IsHasUpdate { get; set; }

        public bool IsBeta { get; set; }

        public FormUpdate()
        {
            InitializeComponent();
            SetStyle(ControlStyles.AllPaintingInWmPaint, true);
            ShadowType = CommonString.CommonShadowType;
            //不执行线程检查
            CheckForIllegalCrossThreadCalls = false;

            CommonMethod.SetStyle(lnkNoUpdate, ControlStyles.Selectable, false);

            AddCustomButton("官网", Resources.网络检测, 15F, (send, e) =>
            {
                CommonMethod.OpenUrl(CommonString.StrServerHostUrl);
            });
        }

        public override void OnThemeChange()
        {
            var foreColor = CommonSetting.Get默认文字颜色();
            lblNew.ForeColor = foreColor;
            lblDate.ForeColor = foreColor;
            rtbCon.ForeColor = foreColor;
            lblNowVersion.ForeColor = CommonSetting.夜间模式 ? CommonTheme.ReveseColor(Color.DimGray) : Color.DimGray;
            lblNewDate.ForeColor = Color.DimGray;
        }

        private void FormUpdate_Load(object sender, EventArgs e)
        {
            Text = AppName.CurrentText() + (IsBeta ? " Beta" : "") + "-" + (IsHasUpdate ? StrUpdateMode.CurrentText() : "最近更新内容".CurrentText());
            if (UpdateInfo == null) return;
            btnOK.Enabled = true;
            btnOK.Text = "立即" + StrUpdateMode;
            lblNew.Text = UpdateInfo.strNowVersion;
            lblDate.Text = UpdateInfo.dtNowDate.ToDateStr("yyyy-MM-dd");
            rtbCon.Text = UpdateInfo.strContext.CurrentText(true);
            rtbCon.Font = CommonMethod.ScaleLabelByHeight(rtbCon.Text, rtbCon.Font, new Size((int)(rtbCon.Size.Width * CommonTheme.DpiScale), (int)(rtbCon.Size.Height * CommonTheme.DpiScale)), 20);
            Opacity = 1;
            if (!IsHasUpdate)
            {
                btnOK.Left -= 90;
                btnOK.Width += 40;
                btnOK.Text = "好的，已了解！";
                btnUpdateLater.Visible = false;
                lnkNoUpdate.Visible = false;
                KeyDown += (object ee, KeyEventArgs eee) =>
                {
                    if (eee.KeyCode == Keys.Escape)
                    {
                        this.Close();
                    }
                };
            }
            else
            {
                lnkNoUpdate.Visible = IsCanUserUpdate;
                if (UpdateInfo.IsNowForce)
                {
                    Text = AppName.CurrentText();
                }
                if (IsAutoStart || UpdateInfo.IsNowForce)
                {
                    var lastUpdateTicks = CommonSetting.GetValue<long>("配置", AppName + "上次更新");
                    if (lastUpdateTicks > 0)
                    {
                        var diffMinute = new TimeSpan(ServerTime.DateTime.Ticks - lastUpdateTicks).TotalMinutes;
                        if (diffMinute <= 3)
                        {
                            // 推荐安装全量包
                            CommonMethod.ShowHelpMsg("检测到上次更新失败，请从官网下载最新安装包(ocr.oldfish.cn)".CurrentText());
                        }
                    }
                    else
                    {
                        btnOK_Click(sender, null);
                    }
                }
            }
        }

        private void bgUpdate_DoWork(object sender, DoWorkEventArgs e)
        {
            if (UpdateInfo == null) return;
            try
            {
                try
                {
                    var tmpFile = Path.GetTempFileName();
                    using (new FileStream(tmpFile, FileMode.OpenOrCreate))
                    {
                        _tmpCacheFile = tmpFile;
                        _isOpenDownLoad = false;
                    }
                }
                catch { }
                if (_isOpenDownLoad)
                {
                    try
                    {
                        var tmpFile = CommonString.DefaultTmpPath + Guid.NewGuid().ToString().Replace("-", "");
                        using (new FileStream(tmpFile, FileMode.OpenOrCreate))
                        {
                            _tmpCacheFile = tmpFile;
                            _isOpenDownLoad = false;
                        }
                    }
                    catch { }
                }

                if (_isOpenDownLoad)
                    proProcess.Value = proProcess.Maximum;
                else
                {
                    UpdateInfo.strURL = CheckUrl(UpdateInfo.strURL, UpdateInfo.strURLBak);
                    DownloadFile(UpdateInfo.strURL, _tmpCacheFile, proProcess, lblProcess);
                    if (!File.Exists(_tmpCacheFile))
                    {
                        _isOpenDownLoad = true;
                    }
                }
                if (!_isOpenDownLoad && proProcess.Value == proProcess.Maximum)
                {
                    lblProcess.Text = "下载完成".CurrentText() + "，" + ("开始" + StrUpdateMode).CurrentText() + "！";
                    btnOK.Tag = "open";
                }
                else
                {
                    pnlUpdate.Visible = false;
                    btnOK.Enabled = true;
                    btnOK.Tag = "down";
                    CommonMethod.ShowHelpMsg("更新失败".CurrentText() + "，" + CommonString.StrRetry.CurrentText());
                }

                bgUpdate.CancelAsync();
            }
            catch { }
        }

        private string CheckUrl(string url, string urlBak)
        {
            if (string.IsNullOrEmpty(url) || string.IsNullOrEmpty(urlBak))
                return string.IsNullOrEmpty(urlBak) ? url : urlBak;

            var lstParam = new List<TaskParam>
            {
                new TaskParam() { Param1 = url },
                new TaskParam() { Param1 = urlBak }
            };
            var result = CommonTask<string>.GetFastestValidResult(lstParam, GetResultByType, 3, 10000
                , (string obj) =>
                {
                    return !string.IsNullOrEmpty(obj);
                });
            return result;
        }

        private string GetResultByType(TaskParam param)
        {
            var result = string.Empty;
            try
            {
                using (var client = new CnnWebClient() { Method = "HEAD", Timeout = 10000 })
                {
                    client.OpenRead(param.Param1);
                    //大于10Kb
                    if (BoxUtil.GetInt32FromObject(client.ResponseHeaders["Content-Length"]) > 10240)
                        result = param.Param1;
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return result;
        }

        private void BeforeUpdate()
        {
            try
            {
                LocalOcrService.CloseService();
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (!IsHasUpdate)
            {
                Close();
                return;
            }
            if (_isCancel)
                return;
            var updateFileName = CommonUpdate.GetServerFile(CommonString.LocalUpdateExePath, CommonString.UpdateExeFileUrl + "?t=" + ServerTime.DateTime.Millisecond);
            if (string.IsNullOrEmpty(updateFileName))
            {
                CommonMethod.ShowHelpMsg("更新失败".CurrentText() + "，" + "请检查是否被杀软拦截，或以管理员身份运行重试！".CurrentText());
                return;
            }
            var tagNow = btnOK.Tag.ToString();
            if (tagNow == "down")
            {
                btnOK.Enabled = false;
                lblProcess.Visible = true;
                proProcess.Visible = true;
                pnlUpdate.Visible = true;
                pnlUpdate.BringToFront();
                lnkNoUpdate.BringToFront();
                bgUpdate.RunWorkerAsync();
            }
            else if (tagNow == "open")
            {
                btnOK.Enabled = false;

                CommonSetting.SetValue("配置", AppName + "上次更新", ServerTime.DateTime.Ticks);
                BeforeUpdate();

                if (IsNeedUnZip)
                {
                    if (IsNeedClearFolder && Directory.Exists(AppPath))
                    {
                        Microsoft.VisualBasic.FileIO.FileSystem.DeleteDirectory(AppPath, Microsoft.VisualBasic.FileIO.DeleteDirectoryOption.DeleteAllContents);
                    }
                    DoUnZip();
                    CommonMethod.ShowHelpMsg(AppName + " " + "已安装成功！".CurrentText());
                    Close();
                }
                else
                {
                    var paramStr = string.Format("\"{0}\" \"{1}\" \"{2}\""
                        , AppPath
                        , _tmpCacheFile
                        , _isOpenDownLoad ? UpdateInfo.strURL : string.Empty
                    );
                    try
                    {
                        var path = Path.GetDirectoryName(AppPath);
                        if (!Directory.Exists(path))
                        {
                            Directory.CreateDirectory(path);
                        }
                    }
                    catch (Exception exception)
                    {
                        Console.WriteLine(exception);
                    }
                    try
                    {
                        CommonString.RunAsAdmin(updateFileName, paramStr, true, true);
                    }
                    catch
                    {
                        CommonString.RunAsAdmin(updateFileName, paramStr, false, true);
                    }
                    finally
                    {
                        if (IsUpdateMode)
                            CommonMethod.Exit();
                        else
                            Close();
                    }
                }
            }
        }

        private void DoUnZip()
        {
            lblProcess.Text = "开始解压缩".CurrentText() + "," + CommonString.StrPleaseWait.CurrentText();
            GZip.GZip.Decompress(Path.GetDirectoryName(_tmpCacheFile), AppPath, Path.GetFileName(_tmpCacheFile));
            CommonMethod.ShowHelpMsg(AppName + " " + "已安装成功！".CurrentText());
        }

        public void DownloadFile(string url, string filename, ProgressBar prog, Label label1)
        {
            try
            {
                var client = new WebClient { Proxy = WebRequest.DefaultWebProxy };
                client.DownloadProgressChanged += (sender, e) =>
                {
                    if (!IsDisposed)
                    {
                        if (prog != null)
                            prog.Value = e.ProgressPercentage;
                        if (label1 != null)
                            label1.Text = CommonMethod.FormatBytes(e.TotalBytesToReceive) + ","
                            + "已下载".CurrentText() + " " + e.ProgressPercentage + "%";
                        Application.DoEvents();
                    }
                };
                if (url.Contains("?"))
                {
                    url += "&t=" + ServerTime.DateTime.Millisecond;
                }
                else
                {
                    url += "?t=" + ServerTime.DateTime.Millisecond;
                }
                client.DownloadFileAsync(new Uri(url), filename);
                while (client.IsBusy) Thread.Sleep(1000);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
        }

        private void FormUpdate_FormClosing(object sender, FormClosingEventArgs e)
        {
            _isCancel = true;
            bgUpdate.CancelAsync();
            if (UpdateInfo?.IsNowForce == true)
                CommonMethod.Exit();
        }

        private void lnkNoUpdate_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            var url = string.IsNullOrEmpty(UpdateInfo.strFullURL) ? UpdateInfo.strURL : UpdateInfo.strFullURL;
            if (!string.IsNullOrEmpty(url))
            {
                url += (url.Contains("?") ? "&" : "?") + "t=" + ServerTime.DateTime.Ticks;
                try
                {
                    ClipboardService.SetText(url);
                }
                catch
                {
                }

                MessageBox.Show(this, "已复制下载地址到粘贴板！\n助手将尝试自动用默认浏览器打开网址…\n如果一直没有弹出下载框，请手动粘贴网址到浏览器重试！".CurrentText(), "手动更新".CurrentText(),
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                CommonMethod.OpenUrl(url);
            }
        }

        private void bgUpdate_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            if (!btnOK.Enabled)
            {
                proProcess.Value = 100;
                Application.DoEvents();
                btnOK_Click(sender, null);
            }
        }

        private void btnUpdateLater_Click(object sender, EventArgs e)
        {
            CommonUpdate.IsAutoCheckUpdate = false;
            Close();
        }
    }
}