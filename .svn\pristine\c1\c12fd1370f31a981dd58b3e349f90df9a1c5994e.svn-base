﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace OCRTools
{
    public class GroupBoxEx : GroupBox
    {
        private Color borderLineColor = System.Drawing.SystemColors.GradientActiveCaption;
        /// <summary>
        /// 边框线颜色
        /// </summary>
        public Color BorderLineColor
        {
            get { return borderLineColor; }
            set { borderLineColor = value; }
        }

        private float borderLineWidth = 1;
        /// <summary>
        /// 边框线宽度
        /// </summary>
        public float BorderLineWidth
        {
            get { return borderLineWidth; }
            set { borderLineWidth = value; }
        }

        public GroupBoxEx()
        {
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            //base.OnPaint(e);

            e.Graphics.Clear(base.BackColor);

            //圆弧直径长度
            int radius = 20;
            int width = e.ClipRectangle.Width, height = e.ClipRectangle.Height;
            int titleStartX = 15;
            float titleWidth = e.Graphics.MeasureString(base.Text, base.Font).Width;
            float titleHeight = e.Graphics.MeasureString(base.Text, base.Font).Height;

            Pen pen = new Pen(borderLineColor, (float)borderLineWidth);

            //左上角圆弧
            e.Graphics.DrawArc(pen, new Rectangle(0, (int)(titleHeight / 2), radius, radius), 180, 90);

            //上边框，标题前半部分
            e.Graphics.DrawLine(pen, radius / 2, (titleHeight - 1) / 2, radius + titleStartX - 3, (titleHeight - 1) / 2);

            //画笔
            SolidBrush brush = new SolidBrush(Color.Black);
            Font font = new System.Drawing.Font("宋体", 10, FontStyle.Bold);
            //标题
            e.Graphics.DrawString(base.Text, font, brush, radius / 2 + titleStartX, 0);

            //上边框，标题右半部分
            e.Graphics.DrawLine(pen, radius / 2 + titleStartX + titleWidth + 3, (titleHeight - 1) / 2, (width - 1) - radius / 2, (titleHeight - 1) / 2);

            //右上角圆弧
            e.Graphics.DrawArc(pen, new Rectangle((width - 1) - radius, (int)(titleHeight / 2), radius, radius), 270, 90);

            //右边框
            e.Graphics.DrawLine(pen, width - 1, (int)(titleHeight / 2) + radius / 2, width - 1, (height - 1) - radius / 2);

            //右下角圆弧
            e.Graphics.DrawArc(pen, new Rectangle((width - 1) - radius, (height - 1) - radius, radius, radius), 0, 90);

            //下边框
            e.Graphics.DrawLine(pen, (width - 1) - radius / 2, height - 1, radius / 2, height - 1);

            //左下角圆弧
            e.Graphics.DrawArc(pen, new Rectangle(0, (height - 1) - radius, radius, radius), 90, 90);

            //左边框
            e.Graphics.DrawLine(pen, 0, (height - 1) - radius / 2, 0, (int)(titleHeight / 2) + radius / 2);
        }
    }
}
