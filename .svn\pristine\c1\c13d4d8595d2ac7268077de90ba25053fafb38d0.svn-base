﻿using OcrLib;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using Windows.Data.Json;
using Windows.Media.Ocr;

namespace OcrMain
{
    public class MyHttpServer : HttpServer
    {
        public MyHttpServer(int port)
            : base(port)
        {
            Task.Factory.StartNew(InitModules);
        }

        public static OnnxParam DefaultPaddleParam = new OnnxParam()
        {
            boxScoreThresh = 0.5f,
            boxThresh = 0.3f,
            doAngle = true,
            isPaddle = true,
            maxSideLen = 1024,
            mostAngle = true,
            padding = 50,
            unClipRatio = 1.6f
        };

        public static OnnxParam DefaultChineseLiteParam = new OnnxParam()
        {
            boxScoreThresh = 0.618f,
            boxThresh = 0.3f,
            doAngle = true,
            isPaddle = false,
            maxSideLen = 1024,
            mostAngle = true,
            padding = 50,
            unClipRatio = 2
        };

        private static List<Ocr> OcrEngine = new List<Ocr>();

        static JavaScriptSerializer JavaScriptSerializer = new JavaScriptSerializer();

        public static int NMaxThread { get; set; } = 5;

        private static void InitModules()
        {
            //internal enum LocalOcrType
            //{
            //    飞浆Mobile = 90001,
            //    飞浆Server = 90002,
            //    中文识别Lite = 90003,
            //    WindowsOcr = 90004,
            //}

            //模型1初始化
            var appPath = AppDomain.CurrentDomain.BaseDirectory + "models";
            foreach (var modelPath in Directory.GetDirectories(appPath))
            {
                if (InitModel(modelPath, out Ocr ocr))
                {
                    OcrEngine.Add(ocr);
                }
            }

            //Environment.OSVersion.Version >= new Version("10.0.18362.0") &&
            if (LocalOcrHelper.InitLanguage())
            {
                OcrEngine.Add(new Ocr() { Name = "WindowsOcr", IsONNX = false, Code = 90004 });
            }
        }

        private static bool InitModel(string path, out Ocr ocrEngin)
        {
            var result = false;
            ocrEngin = null;
            path = path.TrimEnd('\\');
            try
            {
                var infoFile = path + "\\info.txt";
                if (File.Exists(infoFile))
                {
                    ocrEngin = JavaScriptSerializer.Deserialize<Ocr>(File.ReadAllText(infoFile));
                }
                else
                {
                    var modelName = path.Substring(path.LastIndexOf("\\") + "\\".Length);
                    var modelCode = 0;
                    //没取到值的，兼容处理
                    if (modelCode <= 0)
                    {
                        switch (modelName)
                        {
                            case "飞浆Mobile":
                                modelCode = 90001;
                                break;
                            case "飞浆Server":
                                modelCode = 90002;
                                break;
                            case "中文识别Lite":
                                modelCode = 90003;
                                break;
                        }
                    }
                    ocrEngin = new Ocr(modelName, modelCode);
                    // 判断是否是ONNX
                    ocrEngin.IsONNX = Directory.GetFiles(path).Any(p => p.EndsWith(".onnx"));
                    if (ocrEngin.IsONNX)
                    {
                        ocrEngin.OnnxParam = modelName.Contains("飞浆") ? DefaultPaddleParam : DefaultChineseLiteParam;
                    }
                    //File.WriteAllText(infoFile, JavaScriptSerializer.Serialize(ocrEngin), System.Text.Encoding.UTF8);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(path + " 初始化失败：" + e.Message);
            }
            if (ocrEngin?.IsONNX == true)
            {
                var clsPath = path + "\\" + "cls.onnx";
                var detPath = path + "\\" + "det.onnx";
                var recPath = path + "\\" + "rec.onnx";
                var keysPath = path + "\\" + "keys.txt";
                if (File.Exists(detPath) && File.Exists(clsPath) && File.Exists(recPath) && File.Exists(keysPath))
                {
                    ocrEngin.InitModels(detPath, clsPath, recPath, keysPath, NMaxThread);
                    result = true;
                }
                else
                {
                    Console.WriteLine(ocrEngin.Name + " 初始化失败");
                }
            }
            else
            {
                result = true;
            }

            return result;
        }

        static string SubString(string strSource, string strSpilt, string strEnd = "")
        {
            return strSource.SubStringHorspool(strSpilt, strEnd).Trim();
        }

        public override void handleGETRequest(HttpProcessor p)
        {
            try
            {
                p.writeSuccess();
                var resultStr = "no";
                if (p.http_url.StartsWith("/Code.do"))
                {
                    try
                    {
                        var type = SubString(p.http_url, "type=", "&");
                        if (Equals(type, "state"))
                        {
                            resultStr = $"本地识别引擎：\n" + string.Join("\n", OcrEngine.Select(q => q.Name + ":✔").ToArray());
                        }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message + Environment.NewLine + oe.StackTrace + Environment.NewLine + oe.InnerException?.Message + Environment.NewLine + oe.InnerException?.StackTrace);
                    }
                }
                p.outputStream.Write(resultStr);
            }
            catch
            {
            }
        }

        public override void handlePOSTRequest(HttpProcessor p, StreamReader inputData)
        {
            try
            {
                p.writeSuccess();
                var resultStr = "no";
                if (p.http_url.StartsWith("/Code.do"))
                {
                    try
                    {
                        var img = SubString(inputData.ReadToEnd(), "img=", "");
                        var type = int.Parse(SubString(p.http_url, "type=", "&"));
                        var strOcrType = SubString(p.http_url, "ocr=", "&");
                        var id = SubString(p.http_url, "id=", "");
                        if (type > 0 && !string.IsNullOrEmpty(img))
                        {
                            if (!int.TryParse(strOcrType, out int ocrType))
                            {
                                ocrType = 0;
                            }
                            resultStr = processOcr(ocrType, type, img, id);
                        }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message + Environment.NewLine + oe.StackTrace + Environment.NewLine + oe.InnerException?.Message + Environment.NewLine + oe.InnerException?.StackTrace);
                    }
                }
                p.outputStream.Write(resultStr);
            }
            catch
            {
            }
        }

        private OcrLib.OcrResult GetOcrResult(byte[] byt, Ocr engine)
        {
            var result = new OcrLib.OcrResult();
            //本地识别
            if (Equals(engine.Code, 90004))
            {
                result = LocalOcrHelper.Detect(byt);
            }
            else
            {
                if (engine != null)
                {
                    if (engine.IsONNX)
                    {
                        result = engine.Detect(byt, engine.OnnxParam.padding, engine.OnnxParam.maxSideLen, engine.OnnxParam.boxScoreThresh
                                , engine.OnnxParam.boxThresh, engine.OnnxParam.unClipRatio, engine.OnnxParam.doAngle, engine.OnnxParam.mostAngle, engine.OnnxParam.isPaddle);
                    }
                    else
                    {
                        //exe 参数类型
                        switch (engine.ParamType)
                        {
                            default:
                                break;
                        }
                    }
                }
            }
            return result;
        }

        Dictionary<string, string> langSeparators = new Dictionary<string, string>() { { "zh", "" }, { "jp", "" }, { "kr", "" } };
        /**
        * 行间连字符
        * 空白：中文，日语
        * 一个空格：其他
        * @param  {String} lang franc-min 识别的语言类型字符串
        * @return {Number}      行间连字符
*/
        public string lineSeparator(string lang)
        {
            return langSeparators.ContainsKey(lang) ? langSeparators[lang] : " ";
        }

        private bool Contain_CN(string str)
        {
            return Regex.IsMatch(str, "[\u4e00-\u9fa5]");
        }

        private bool Contain_JP(string str)
        {
            return Regex.IsMatch(str, "[\u0800-\u4e00]");
        }

        private bool Contain_KR(string str)
        {
            return Regex.IsMatch(str, "[\u3130-\u318f]") || Regex.IsMatch(str, "[\xAC00-\xD7A3]");
        }

        private string processOcr(int ocrType, int engineType, string img, string id)
        {
            var byt = Convert.FromBase64String(HttpUtility.UrlDecode(img));
            var engine = OcrEngine.FirstOrDefault(p => Equals(engineType, p.Code));

            var ocrContent = new OcrContent
            {
                ocrType = ocrType,
                id = id,
                processId = engine.Code,
                processName = engine.Name,
                Identity = id,
                result = new ResultEntity()
            };

            if (ocrType == 0 || ocrType == 1)
            {
                var ocrResult = GetOcrResult(byt, engine);
                if (ocrResult != null && !string.IsNullOrEmpty(ocrResult.StrRes))
                {
                    var lstCell = new List<TextCellInfo>();
                    ocrResult.TextBlocks?.ForEach(text =>
                    {
                        var lang = Contain_CN(text.Text) ? "zh" : (Contain_JP(text.Text) ? "ja" : Contain_KR(text.Text) ? "kr" : "en");
                        var separator = lineSeparator(lang);
                        var cell = new TextCellInfo
                        {
                            words = CommonStyle.ReplacePunctuationAuto(text.Text, lang, true, true, true, true)
                        };
                        if (text.BoundingRect.IsEmpty)
                        {
                            cell.location = new LocationInfo()
                            {
                                left = Math.Min(text.BoxPoints[0].X, text.BoxPoints[3].X) - 50,
                                top = Math.Min(text.BoxPoints[0].Y, text.BoxPoints[1].Y) - 50,
                                width = Math.Min(text.BoxPoints[1].X - text.BoxPoints[0].X, text.BoxPoints[2].X - text.BoxPoints[3].X),
                                height = Math.Min(text.BoxPoints[3].Y - text.BoxPoints[0].Y, text.BoxPoints[2].Y - text.BoxPoints[1].Y),
                            };
                        }
                        else
                        {
                            cell.location = new LocationInfo()
                            {
                                left = text.BoundingRect.Left,
                                height = text.BoundingRect.Height,
                                top = text.BoundingRect.Top,
                                width = text.BoundingRect.Width
                            };
                        }
                        lstCell.Add(cell);
                    });

                    if (lstCell.Exists(p => p.location != null))
                    {
                        ocrContent.result.verticalText = JavaScriptSerializer.Serialize(lstCell);
                    }
                    var strStart = lstCell.Count > 1 ? "\t" : "";
                    ocrContent.result.spiltText = (strStart + string.Join("\n" + strStart, lstCell.Select(p => p.words?.TrimEnd()))).TrimEnd();
                    ocrContent.result.autoText = ocrContent.result.spiltText;
                }
            }
            else if (ocrType == 2)
            {
                var ocrResult = GetOcrResult(byt, engine);
                var tableResult = OcrUtils.DetectTableFromOcrResult(ocrResult);
                if (tableResult != null && tableResult.rows?.Count > 0)
                {
                    ocrContent.result.autoText = ocrContent.result.spiltText = JavaScriptSerializer.Serialize(tableResult);
                    ocrContent.result.resultType = ocrType;
                }
            }
            else
            {
                ocrContent.result = new ResultEntity()
                {
                    autoText = strNotSupport,
                    spiltText = strNotSupport,
                    verticalText = "{}"
                };
            }

            var resultStr = JavaScriptSerializer.Serialize(ocrContent);
            return resultStr;
        }

        string strNotSupport = "暂不支持当前操作！";
    }
}
