using ExcelLibrary.SpreadSheet;
using System.Data;

namespace ExcelLibrary
{
    public sealed class DataSetHelper
    {

        public static void CreateWorkbook(string filePath, DataTable table)
        {
            Workbook workbook = new Workbook();
            Worksheet worksheet = new Worksheet(table.TableName);
            for (int i = 0; i < table.Columns.Count; i++)
            {
                worksheet.Cells[0, i] = new Cell(table.Columns[i].ColumnName);
                for (int j = 0; j < table.Rows.Count; j++)
                {
                    worksheet.Cells[j + 1, i] = new Cell(table.Rows[j][i]);
                }
            }
            workbook.Worksheets.Add(worksheet);
            workbook.Save(filePath);
        }
    }
}