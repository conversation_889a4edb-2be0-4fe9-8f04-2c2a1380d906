using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolCatch : ToolObject
    {
        private DrawCatch drawCatch;

        private bool isdraw;

        private Point point;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            point = e.Location;
            if (drawArea.Cursor != CursorEx.Cross && drawCatch != null)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.current_ToolType = DrawToolType.Pointer;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else
            {
                drawArea.Refresh();
                drawCatch = new DrawCatch(e.X, e.Y, 1, 1);
                drawArea.Catch = drawCatch;
                NewObject(drawArea, drawCatch);
                drawCatch.ChangeRect(e.X, e.Y, 1, 1);
                isdraw = true;
            }
        }

        protected void NewObject(DrawArea drawArea, DrawObject o)
        {
            drawArea.GraphicsList.UnselectAll();
            drawArea.GraphicsList.graphicsList.Insert(0, o);
            drawArea.Capture = true;
            o.Selected = true;
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            var num = Math.Abs(e.X - point.X);
            var num2 = Math.Abs(e.Y - point.Y);
            if (e.Button == MouseButtons.Left && drawCatch != null)
            {
                drawCatch.IsSelected = true;
                if (num > 10 && num2 > 10 && isdraw)
                {
                    drawArea.isAutoDraw = false;
                    using (new AutomaticCanvasRefresher(drawArea, drawCatch.GetAddBound))
                    {
                        var obj = drawCatch;
                        using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                        {
                            drawCatch.MoveHandleTo(e.Location, 5);
                        }
                    }
                }
            }
        }

        public override void MouseDoubleClick(DrawArea drawArea, MouseEventArgs e)
        {
            drawArea.Copy();
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawCatch != null)
            {
                isdraw = false;
                drawArea.isAutoDraw = false;
                drawArea.isShowZoom = false;
                drawArea.IsShowCross = false;
                StaticValue.current_Rectangle = drawCatch.Rectangle;
                if (Math.Abs(e.X - point.X) < 10 && Math.Abs(e.Y - point.Y) < 10)
                {
                    drawCatch = new DrawCatch(drawArea.AutoRect.SizeOffset(-1));
                    StaticValue.current_Rectangle = drawArea.AutoRect;
                    drawArea.GraphicsList.RemoveAt(0);
                    drawArea.GraphicsList.Add(drawCatch);
                }

                drawCatch.Normalize();
                drawArea.AddCommandToHistory(new CommandAdd(drawCatch));
                drawArea.Refresh();
                drawCatch.IsCatchMove = true;
                drawArea.ShowTool(drawCatch);
                drawArea.ActiveTool = DrawToolType.Rectangle;
            }
        }
    }
}