using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class FONT : Record
	{
		public short Height;

		public ushort OptionFlags;

		public ushort ColorIndex;

		public ushort Weight;

		public ushort Escapement;

		public byte Underline;

		public byte Family;

		public byte CharacterSet;

		public byte NotUsed;

		public string Name;

		public FONT(Record record)
			: base(record)
		{
		}

		public FONT()
		{
			Type = 49;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			Height = binaryReader.ReadInt16();
			OptionFlags = binaryReader.ReadUInt16();
			ColorIndex = binaryReader.ReadUInt16();
			Weight = binaryReader.ReadUInt16();
			Escapement = binaryReader.ReadUInt16();
			Underline = binaryReader.ReadByte();
			Family = binaryReader.ReadByte();
			CharacterSet = binaryReader.ReadByte();
			NotUsed = binaryReader.ReadByte();
			Name = ReadString(binaryReader, 8);
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(Height);
			binaryWriter.Write(OptionFlags);
			binaryWriter.Write(ColorIndex);
			binaryWriter.Write(Weight);
			binaryWriter.Write(Escapement);
			binaryWriter.Write(Underline);
			binaryWriter.Write(Family);
			binaryWriter.Write(CharacterSet);
			binaryWriter.Write(NotUsed);
			Record.WriteString(binaryWriter, Name, 8);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
