using System.Drawing;
using System.Drawing.Drawing2D;
using System.Globalization;
using System.Runtime.Serialization;
using System.Windows.Forms;

namespace OCRTools
{
    internal class DrawLinebase : DrawObject
    {
        public Point endPoint;
        public Point startPoint;

        public DrawLinebase()
            : this(0, 0, 1, 0)
        {
        }

        public DrawLinebase(int x1, int y1, int x2, int y2)
        {
            startPoint.X = x1;
            startPoint.Y = y1;
            endPoint.X = x2;
            endPoint.Y = y2;
            Initialize();
        }

        public override int HandleCount => 2;

        protected GraphicsPath AreaPath { get; set; }

        protected Pen AreaPen { get; set; }

        protected Region AreaRegion { get; set; }

        public override Rectangle GetBoundingBox()
        {
            using (var graphicsPath = CreateArrowPath())
            {
                var bounds = graphicsPath.GetBounds();
                if (IsSelected)
                {
                    var num = 10;
                    bounds.Inflate(num, num);
                }

                return Rectangle.Ceiling(bounds);
            }
        }

        private GraphicsPath CreateArrowPath()
        {
            var graphicsPath = new GraphicsPath();
            var points = new[]
            {
                startPoint,
                endPoint
            };
            graphicsPath.AddCurve(points, 0.7f);
            return graphicsPath;
        }

        public override DrawObject Clone()
        {
            var drawLinebase = new DrawLinebase
            {
                startPoint = startPoint,
                endPoint = endPoint
            };
            FillDrawObjectFields(drawLinebase);
            return drawLinebase;
        }

        public override void Draw(Graphics g)
        {
            g.SmoothingMode = SmoothingMode.AntiAlias;
            var pen = new Pen(Color, PenWidth);
            g.DrawLine(pen, startPoint.X, startPoint.Y, endPoint.X, endPoint.Y);
            pen.Dispose();
        }

        public override Point GetHandle(int handleNumber)
        {
            if (handleNumber == 1) return startPoint;
            return endPoint;
        }

        public override int HitTest(Point point)
        {
            if (Selected)
                for (var i = 1; i <= HandleCount; i++)
                    if (GetHandleRectangle(i).Contains(point))
                        return i;
            if (PointInObject(point)) return 0;
            return -1;
        }

        public override bool PointInObject(Point point)
        {
            CreateObjects();
            return AreaRegion.IsVisible(point);
        }

        public override bool IntersectsWith(Rectangle rectangle)
        {
            CreateObjects();
            return AreaRegion.IsVisible(rectangle);
        }

        public override Cursor GetHandleCursor(int handleNumber)
        {
            if ((uint) (handleNumber - 1) <= 1u) return Cursors.SizeAll;
            return Cursors.Default;
        }

        public override void MoveHandleTo(Point point, int handleNumber)
        {
            if (handleNumber == 1)
                startPoint = point;
            else
                endPoint = point;
            Invalidate();
        }

        public override void Move(int deltaX, int deltaY)
        {
            startPoint.X += deltaX;
            startPoint.Y += deltaY;
            endPoint.X += deltaX;
            endPoint.Y += deltaY;
            Invalidate();
        }

        public override void SaveToStream(SerializationInfo info, int orderNumber)
        {
            info.AddValue(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "Start", orderNumber), startPoint);
            info.AddValue(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "End", orderNumber), endPoint);
            base.SaveToStream(info, orderNumber);
        }

        public override void LoadFromStream(SerializationInfo info, int orderNumber)
        {
            startPoint =
                (Point) info.GetValue(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "Start", orderNumber),
                    typeof(Point));
            endPoint = (Point) info.GetValue(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "End", orderNumber),
                typeof(Point));
            base.LoadFromStream(info, orderNumber);
        }

        protected void Invalidate()
        {
            if (AreaPath != null)
            {
                AreaPath.Dispose();
                AreaPath = null;
            }

            if (AreaPen != null)
            {
                AreaPen.Dispose();
                AreaPen = null;
            }

            if (AreaRegion != null)
            {
                AreaRegion.Dispose();
                AreaRegion = null;
            }
        }

        protected virtual void CreateObjects()
        {
            if (AreaPath == null)
            {
                AreaPath = new GraphicsPath();
                AreaPen = new Pen(Color.Black, 7f);
                AreaPath.AddLine(startPoint.X, startPoint.Y, endPoint.X, endPoint.Y);
                AreaPath.Widen(AreaPen);
                AreaRegion = new Region(AreaPath);
            }
        }
    }
}