﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace OCRTools
{
    public enum ShapeType // Localized
    {
        RegionRectangle,
        RegionEllipse,
        RegionFreehand,
        ToolSelect,
        DrawingRectangle,
        DrawingEllipse,
        DrawingFreehand,
        DrawingLine,
        DrawingArrow,
        DrawingTextOutline,
        DrawingTextBackground,
        DrawingSpeechBalloon,
        DrawingStep,
        DrawingMagnify,
        DrawingImage,
        DrawingImageScreen,
        DrawingSticker,
        DrawingCursor,
        DrawingSmartEraser,
        EffectBlur,
        EffectPixelate,
        EffectHighlight,
        ToolCrop
    }

    public enum ShapeCategory
    {
        Region,
        Drawing,
        Effect,
        Tool
    }
}
