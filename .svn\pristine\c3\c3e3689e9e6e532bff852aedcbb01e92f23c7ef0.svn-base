using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolMosaic : ToolObject
    {
        private DrawMosaic _drawMosaic;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.CurrentToolType = drawArea.ActiveTool;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else
            {
                _drawMosaic = new DrawMosaic(e.X, e.Y, 1, 1)
                {
                    BackgroundImageEx = drawArea.BackgroundImageEx
                };
                AddNewObject(drawArea, _drawMosaic);
            }
        }

        protected new void AddNewObject(DrawArea drawArea, DrawObject o)
        {
            drawArea.GraphicsList.UnselectAll();
            drawArea.GraphicsList.Add(o);
            drawArea.Capture = true;
            o.Selected = true;
            drawArea.Refresh();
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.Button == MouseButtons.Left)
            {
                if (_drawMosaic == null)
                {
                    drawArea.ActiveTool = DrawToolType.Text;
                    return;
                }

                _drawMosaic.IsSelected = true;
                var obj = _drawMosaic;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    _drawMosaic.MoveHandleTo(e.Location, 5, drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (_drawMosaic != null)
            {
                StaticValue.CurrentRectangle = _drawMosaic.Rectangle;
                _drawMosaic.IsCache = true;
                if (!_drawMosaic.Rectangle.IsLimt())
                {
                    drawArea.GraphicsList.RemoveAt(0);
                    return;
                }

                var obj = _drawMosaic;
                using (new AutomaticCanvasRefresher(drawArea, obj.GetBoundingBox))
                {
                    _drawMosaic.Normalize();
                    drawArea.AddCommandToHistory(new CommandAdd(_drawMosaic));
                }
            }
        }
    }
}