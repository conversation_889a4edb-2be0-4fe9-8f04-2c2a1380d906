﻿namespace OCRTools.Common.SNTP
{
    /// <summary>
    ///     Warns of an impending leap second to be inserted/deleted in the last minute of the current day.
    /// </summary>
    public enum LeapIndicator
    {
        /// <summary>
        ///     No warning.
        /// </summary>
        NoWarning = 0,

        /// <summary>
        ///     Last minute has 61 seconds.
        /// </summary>
        LastMinute61Seconds = 1,

        /// <summary>
        ///     Last minute has 59 seconds.
        /// </summary>
        LastMinute59Seconds = 2,

        /// <summary>
        ///     Alarm condition (clock not synchronized).
        /// </summary>
        Alarm = 3
    }
}