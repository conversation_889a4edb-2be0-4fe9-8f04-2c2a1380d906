namespace UtfUnknown.Core.Models.MultiByte.Chinese
{
    public class Big5SmModel : StateMachineModel
    {
        private static readonly int[] Big5Cls =
        {
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 0, 0),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 0, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 1),
            BitPackage.Pack4Bits(4, 4, 4, 4, 4, 4, 4, 4),
            BitPackage.Pack4Bits(4, 4, 4, 4, 4, 4, 4, 4),
            BitPackage.Pack4Bits(4, 4, 4, 4, 4, 4, 4, 4),
            BitPackage.Pack4Bits(4, 4, 4, 4, 4, 4, 4, 4),
            BitPackage.Pack4Bits(4, 3, 3, 3, 3, 3, 3, 3),
            BitPackage.Pack4Bits(3, 3, 3, 3, 3, 3, 3, 3),
            BitPackage.Pack4Bits(3, 3, 3, 3, 3, 3, 3, 3),
            BitPackage.Pack4Bits(3, 3, 3, 3, 3, 3, 3, 3),
            BitPackage.Pack4Bits(3, 3, 3, 3, 3, 3, 3, 3),
            BitPackage.Pack4Bits(3, 3, 3, 3, 3, 3, 3, 3),
            BitPackage.Pack4Bits(3, 3, 3, 3, 3, 3, 3, 3),
            BitPackage.Pack4Bits(3, 3, 3, 3, 3, 3, 3, 3),
            BitPackage.Pack4Bits(3, 3, 3, 3, 3, 3, 3, 3),
            BitPackage.Pack4Bits(3, 3, 3, 3, 3, 3, 3, 3),
            BitPackage.Pack4Bits(3, 3, 3, 3, 3, 3, 3, 3),
            BitPackage.Pack4Bits(3, 3, 3, 3, 3, 3, 3, 0)
        };

        private static readonly int[] Big5St =
        {
            BitPackage.Pack4Bits(1, 0, 0, 3, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 2, 2, 2, 2, 2, 1),
            BitPackage.Pack4Bits(1, 0, 0, 0, 0, 0, 0, 0)
        };

        private static readonly int[] Big5CharLenTable =
        {
            0,
            1,
            1,
            2,
            0
        };

        public Big5SmModel()
            : base(
                new BitPackage(BitPackage.indexShift4Bits, BitPackage.shiftMask4Bits, BitPackage.bitShift4Bits,
                    BitPackage.unitMask4Bits, Big5Cls), 5,
                new BitPackage(BitPackage.indexShift4Bits, BitPackage.shiftMask4Bits, BitPackage.bitShift4Bits,
                    BitPackage.unitMask4Bits, Big5St), Big5CharLenTable, "big5")
        {
        }
    }
}