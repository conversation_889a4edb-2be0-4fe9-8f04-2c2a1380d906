using System.Reflection;
using System.Runtime.InteropServices;
using System.Security;

[assembly: AssemblyProduct("OCR助手")]
[assembly: AssemblyCompany("SmartOldFish")]
[assembly: AssemblyCopyright("版权 © 2019-2023 OldFish.CN")]
[assembly: ComVisible(false)]
[assembly: Guid("f0481b1f-3226-415a-bcc4-9b28e2fb0f9f")]
[assembly: AssemblyVersion("2.6.5")]
[assembly: AssemblyFileVersion("2.6.5")]
[assembly: AssemblyDescription("OCR文字识别助手(2023-03-18)")]
//[assembly: AssemblyVersion("2.6.4")]
//[assembly: AssemblyFileVersion("2.6.4")]
//[assembly: AssemblyDescription("OCR文字识别助手(2023-02-28)")]
[module: UnverifiableCode]