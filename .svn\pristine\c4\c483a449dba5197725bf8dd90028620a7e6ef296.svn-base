using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Globalization;
using System.Runtime.Serialization;
using System.Windows.Forms;

namespace OCRTools
{
    internal class DrawStep : DrawObject
    {
        public Rectangle rectangle;

        public int StepSize = 24.DpiValue();

        public DrawStep(int x, int y, int width, int height)
        {
            rectangle.X = x - StepSize / 2;
            rectangle.Y = y - StepSize / 2;
            rectangle.Width = StepSize;
            rectangle.Height = StepSize;
            Initialize();
        }

        public DrawStep(int x, int y, DrawArea drawArea)
        {
            rectangle.X = x - StepSize / 2;
            rectangle.Y = y - StepSize / 2;
            rectangle.Width = StepSize;
            rectangle.Height = StepSize;
            Initialize();
        }

        public override Rectangle Rectangle
        {
            get => rectangle;
            set => rectangle = value;
        }

        public override int HandleCount => 8;

        public override DrawToolType NoteType => DrawToolType.Step;

        public override string Text { get; set; }

        public override Rectangle GetBoundingBox()
        {
            var normalizedRectangle = Rectangle.GetNormalizedRectangle();
            if (IsSelected)
            {
                var num = 5;
                normalizedRectangle.Inflate(num, num);
            }

            return normalizedRectangle;
        }

        public override DrawObject Clone()
        {
            var drawStep = new DrawStep(0, 0, 1, 1)
            {
                rectangle = rectangle,
                Text = Text
            };
            FillDrawObjectFields(drawStep);
            return drawStep;
        }

        public override void Draw(Graphics g)
        {
            g.SmoothingMode = SmoothingMode.HighQuality;
            var normalizedRectangle = GetNormalizedRectangle(Rectangle);
            normalizedRectangle = new Rectangle(normalizedRectangle.X, normalizedRectangle.Y, normalizedRectangle.Width,
                normalizedRectangle.Height);
            var textColor = Color.White;
            var color = Color;
            if (Color == CustomColor.gray)
            {
                textColor = CustomColor.black;
                color = CustomColor.gray;
            }

            using (var pen2 = new Pen(Color.White, 2f))
            {
                using (var pen = new Pen(Color.Black, 1f))
                {
                    using (Brush brush = new SolidBrush(color))
                    {
                        for (var i = 0; i < 4; i++)
                        {
                            pen.Color = Color.FromArgb(30 * (4 - i), Color.Gray);
                            g.DrawEllipse(pen,
                                new Rectangle(normalizedRectangle.X - i, normalizedRectangle.Y - i,
                                    normalizedRectangle.Width + 2 * i, normalizedRectangle.Height + 2 * i));
                        }

                        g.FillEllipse(brush, normalizedRectangle);
                        DrawNumber(g, Text, textColor, normalizedRectangle);
                        g.DrawEllipse(pen2, normalizedRectangle);
                    }
                }
            }
        }

        protected void DrawNumber(Graphics g, string str, Color textColor, Rectangle rect)
        {
            var num = Convert.ToInt32(str);
            if (rect.Width > 20 && rect.Height > 20)
            {
                var num2 = num > 99 ? 20 : num <= 9 ? 10.DpiValue() : 13.DpiValue();
                var num3 = Math.Min(rect.Width, rect.Height) - num2;
                using (var font = new Font("微软雅黑", num3, FontStyle.Bold, GraphicsUnit.Pixel))
                {
                    using (var format = new StringFormat
                    {
                        Alignment = StringAlignment.Center,
                        LineAlignment = StringAlignment.Center
                    })
                    {
                        using (Brush brush = new SolidBrush(textColor))
                        {
                            g.TextRenderingHint = TextRenderingHint.AntiAliasGridFit;
                            g.DrawString(num.ToString(), font, brush, rect, format);
                            g.TextRenderingHint = TextRenderingHint.SystemDefault;
                        }
                    }
                }
            }
        }

        protected void SetRectangle(int x, int y, int width, int height)
        {
            rectangle.X = x;
            rectangle.Y = y;
            rectangle.Width = width;
            rectangle.Height = height;
        }

        public override bool PointInObject(Point point)
        {
            var normalizedRectangle = Rectangle.GetNormalizedRectangle();
            var rectangle = new Rectangle(normalizedRectangle.X, normalizedRectangle.Y, StepSize, StepSize);
            if (ContainsE(rectangle, point)) return true;
            return false;
        }

        public bool ContainsE(Rectangle rectangle, Point pt)
        {
            var graphicsPath = new GraphicsPath();
            graphicsPath.AddEllipse(rectangle);
            if (graphicsPath.IsVisible(pt))
            {
                graphicsPath.Dispose();
                return true;
            }

            graphicsPath.Dispose();
            return false;
        }

        public override Point GetHandle(int handleNumber)
        {
            var num = rectangle.X + rectangle.Width / 2;
            var num2 = rectangle.Y + rectangle.Height / 2;
            var x = rectangle.X;
            var y = rectangle.Y;
            switch (handleNumber)
            {
                case 1:
                    x = rectangle.X;
                    y = rectangle.Y;
                    break;
                case 2:
                    x = num;
                    y = rectangle.Y;
                    break;
                case 3:
                    x = rectangle.Right;
                    y = rectangle.Y;
                    break;
                case 4:
                    x = rectangle.Right;
                    y = num2;
                    break;
                case 5:
                    x = rectangle.Right;
                    y = rectangle.Bottom;
                    break;
                case 6:
                    x = num;
                    y = rectangle.Bottom;
                    break;
                case 7:
                    x = rectangle.X;
                    y = rectangle.Bottom;
                    break;
                case 8:
                    x = rectangle.X;
                    y = num2;
                    break;
            }

            return new Point(x, y);
        }

        public override int HitTest(Point point)
        {
            if (Selected)
                for (var i = 1; i <= HandleCount; i++)
                    if (GetHandleRectangle(i).Contains(point))
                        return i;
            if (PointInObject(point)) return 0;
            return -1;
        }

        public override void MoveHandleTo(Point point, int handleNumber, bool shiftPressed)
        {
            var num = Rectangle.Left;
            var num2 = Rectangle.Top;
            var num3 = Rectangle.Right;
            var num4 = Rectangle.Bottom;
            switch (handleNumber)
            {
                case 1:
                    num = point.X;
                    num2 = point.Y;
                    break;
                case 2:
                    num2 = point.Y;
                    break;
                case 3:
                    num3 = point.X;
                    num2 = point.Y;
                    break;
                case 4:
                    num3 = point.X;
                    break;
                case 5:
                    num3 = point.X;
                    num4 = point.Y;
                    break;
                case 6:
                    num4 = point.Y;
                    break;
                case 7:
                    num = point.X;
                    num4 = point.Y;
                    break;
                case 8:
                    num = point.X;
                    break;
            }

            rectangle.X = num;
            rectangle.Y = num2;
            rectangle.Width = num3 - num;
            rectangle.Height = num4 - num2;
            if (shiftPressed)
                switch (handleNumber)
                {
                    case 1:
                        rectangle = MakeSquare(rectangle);
                        rectangle.X = num3 - rectangle.Width;
                        rectangle.Y = num4 - rectangle.Height;
                        break;
                    case 5:
                        rectangle = MakeSquare(rectangle);
                        break;
                    case 7:
                        rectangle = MakeSquare(rectangle);
                        rectangle.X = num3 - rectangle.Width;
                        rectangle.Y = num2;
                        break;
                    case 3:
                        rectangle = MakeSquare(rectangle);
                        rectangle.X = num;
                        rectangle.Y = num4 - rectangle.Height;
                        break;
                    case 2:
                    case 6:
                        rectangle.Width = SignMultiplier(rectangle.Width) * Math.Abs(rectangle.Height);
                        break;
                    case 4:
                    case 8:
                        rectangle.Height = SignMultiplier(rectangle.Height) * Math.Abs(rectangle.Width);
                        break;
                }
        }

        public override Cursor GetHandleCursor(int handleNumber)
        {
            switch (handleNumber)
            {
                case 1:
                    return CursorEx.Cross;
                case 2:
                    return CursorEx.Cross;
                case 3:
                    return CursorEx.Cross;
                case 4:
                    return CursorEx.Cross;
                case 5:
                    return CursorEx.Cross;
                case 6:
                    return CursorEx.Cross;
                case 7:
                    return CursorEx.Cross;
                case 8:
                    return CursorEx.Cross;
                default:
                    return CursorEx.Cross;
            }
        }

        public override void MoveHandleTo(Point point, int handleNumber)
        {
            var num = Rectangle.Left;
            var num2 = Rectangle.Top;
            var num3 = Rectangle.Right;
            var num4 = Rectangle.Bottom;
            switch (handleNumber)
            {
                case 1:
                    num = point.X;
                    num2 = point.Y;
                    break;
                case 2:
                    num2 = point.Y;
                    break;
                case 3:
                    num3 = point.X;
                    num2 = point.Y;
                    break;
                case 4:
                    num3 = point.X;
                    break;
                case 5:
                    num3 = point.X;
                    num4 = point.Y;
                    break;
                case 6:
                    num4 = point.Y;
                    break;
                case 7:
                    num = point.X;
                    num4 = point.Y;
                    break;
                case 8:
                    num = point.X;
                    break;
            }

            SetRectangle(num, num2, num3 - num, num4 - num2);
        }

        public override bool IntersectsWith(Rectangle rectangle)
        {
            return Rectangle.IntersectsWith(rectangle);
        }

        public override void Move(int deltaX, int deltaY)
        {
            rectangle.X += deltaX;
            rectangle.Y += deltaY;
        }

        public override void Normalize()
        {
            rectangle = DrawRectangle.GetNormalizedRectangle(rectangle);
        }

        public override void SaveToStream(SerializationInfo info, int orderNumber)
        {
            info.AddValue(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "Rect", orderNumber), rectangle);
            base.SaveToStream(info, orderNumber);
        }

        public override void LoadFromStream(SerializationInfo info, int orderNumber)
        {
            rectangle = (Rectangle)info.GetValue(
                string.Format(CultureInfo.InvariantCulture, "{0}{1}", "Rect", orderNumber), typeof(Rectangle));
            base.LoadFromStream(info, orderNumber);
        }

        public static Rectangle GetNormalizedRectangle(int x1, int y1, int x2, int y2)
        {
            if (x2 < x1)
            {
                var num = x2;
                x2 = x1;
                x1 = num;
            }

            if (y2 < y1)
            {
                var num2 = y2;
                y2 = y1;
                y1 = num2;
            }

            return new Rectangle(x1, y1, x2 - x1, y2 - y1);
        }

        public static Rectangle GetNormalizedRectangle(Rectangle r)
        {
            return GetNormalizedRectangle(r.X, r.Y, r.X + r.Width, r.Y + r.Height);
        }
    }
}