﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace OCRTools
{
    public class AutoSizeFormUtil
    {
        //(1).声明结构,只记录窗体和其控件的初始位置和大小。  
        public struct controlRect
        {
            public int Left;
            public int Top;
            public int Width;
            public int Height;
        }
        public Dictionary<string, controlRect> oldCtrlDir;
        public void controllInitializeSize(Form mForm)
        {
            oldCtrlDir = new Dictionary<string, controlRect>();
            controlRect cR;
            cR.Left = mForm.Left; cR.Top = mForm.Top; cR.Width = mForm.Width; cR.Height = mForm.Height;
            oldCtrlDir.Add(mForm.Name, cR);
            LoopInitializeSize(mForm);
        }
        private void LoopInitializeSize(Control control)
        {
            if (control.Controls.Count > 0)
            {
                foreach (Control c in control.Controls)
                {
                    controlRect cR;
                    cR.Left = c.Left; cR.Top = c.Top; cR.Width = c.Width; cR.Height = c.Height;
                    if (oldCtrlDir.ContainsKey(c.Name))
                    {
                        oldCtrlDir.Remove(c.Name);
                    }
                    oldCtrlDir.Add(c.Name, cR);
                    if (c.Controls.Count > 0)
                    {
                        LoopInitializeSize(c);
                    }
                }
            }
        }
        public void controlAutoSize(Form mForm)
        {
            float wScale = (float)mForm.Width / (float)oldCtrlDir[mForm.Name].Width;//新旧窗体之间的比例，与最早的旧窗体  
            float hScale = (float)mForm.Height / (float)oldCtrlDir[mForm.Name].Height;//.Height;  
            loopControls(mForm, wScale, hScale);
        }
        private void loopControls(Control c, float wScale, float hScale)
        {
            if (!oldCtrlDir.ContainsKey(c.Name))
            {
                return;
            }
            controlRect rect = oldCtrlDir[c.Name];
            c.Left = (int)((rect.Left) * wScale);//新旧控件之间的线性比例。控件位置只相对于窗体，所以不能加 + wLeft1  
            c.Top = (int)((rect.Top) * hScale);//  
            c.Width = (int)(rect.Width * wScale);//只与最初的大小相关，所以不能与现在的宽度相乘 (int)(c.Width * w);  
            c.Height = (int)(rect.Height * hScale);// 
            if (c.Controls.Count > 0)
            {
                foreach (Control tmp in c.Controls)
                {
                    loopControls(tmp, wScale, hScale);
                }
            }
        }
    }
}
