﻿using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Language;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmReport : MetroForm
    {
        public FrmReport()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            this.AddContactUserBtn();
        }

        private void btnSub_Click(object sender, EventArgs e)
        {
            if (!CommonString.IsOnLine)
            {
                CommonMethod.ShowHelpMsg(CommonString.StrNetWorkError.CurrentText());
                return;
            }

            if (pnlPicture.Controls.Count == 1 && string.IsNullOrEmpty(rtbContent.Text.Trim()))
            {
                MessageBox.Show(this, "描述内容不能为空，至少包含一张图片或者描述文字！".CurrentText(), CommonString.StrReminder.CurrentText(), MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
                return;
            }

            var txtContent = rtbContent.Text.Trim();
            if (pnlPicture.Controls.Count == 1 && txtContent.Length < 10)
            {
                MessageBox.Show(this, "描述内容长度不能小于10个文字！".CurrentText(), CommonString.StrReminder.CurrentText(), MessageBoxButtons.OK, MessageBoxIcon.Information);
                rtbContent.Focus();
                return;
            }

            if (txtContent.Length > 500)
            {
                MessageBox.Show(this, "描述内容长度不能大于500个文字！".CurrentText(), CommonString.StrReminder.CurrentText(), MessageBoxButtons.OK, MessageBoxIcon.Information);
                rtbContent.Focus();
                return;
            }

            var lstFiles = new List<UploadFileInfo>();
            foreach (var item in pnlPicture.Controls)
                if (item is PictureBox box)
                {
                    var file = new UploadFileInfo
                    {
                        Name = "image",
                        Filename = lstFiles.Count + 1 + ".png",
                        ContentType = "image/png",
                        Stream = new MemoryStream(ImageProcessHelper.ImageToByte(box.Image))
                    };
                    lstFiles.Add(file);
                }

            var strMsg = "";
            OcrHelper.SendReportInfo(txtContent, lstFiles, ref strMsg);
            CommonMethod.ShowHelpMsg("感谢您的反馈，助手的发展离不开您的支持！".CurrentText());
            DialogResult = DialogResult.OK;
            Close();
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            var open = new OpenFileDialog
            {
                Title = "请选择文件".CurrentText(),
                Filter = CommonString.GetImgFilter(),
                RestoreDirectory = true,
                Multiselect = true
            };
            if (open.ShowDialog(this) == DialogResult.OK)
                foreach (var item in open.FileNames)
                    if (!AddImage(item))
                        break;
        }

        public void SetContent(string content)
        {
            rtbContent.Text = content;
        }

        public bool AddImage(string location, Bitmap image = null)
        {
            if (pnlPicture.Controls.Count > 5)
            {
                MessageBox.Show(this, "最多只能上传5张图片，感谢配合！", CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Information);
                return false;
            }

            var pic = new PictureBox
            {
                Size = new Size(50, 50)
            };
            if (!string.IsNullOrEmpty(location))
                pic.ImageLocation = location;
            else
                pic.Image = image;
            pic.BorderStyle = BorderStyle.FixedSingle;
            pic.Cursor = Cursors.Hand;
            pic.SizeMode = PictureBoxSizeMode.StretchImage;
            pic.Click += Pic_Click;
            pnlPicture.Controls.Add(pic);
            return true;
        }

        private void Pic_Click(object sender, EventArgs e)
        {
            this.ViewImage((sender as PictureBox)?.Image);
        }
    }
}