﻿using OCRTools.UserControlEx;
using System.Windows.Forms;

namespace OCRTools
{
    partial class FrmBatchCompress
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            this.dgContent = new OCRTools.DataGridViewEx();
            this.文件名 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.压缩引擎 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.原始大小 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.压缩大小 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.状态 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.查看 = new System.Windows.Forms.DataGridViewLinkColumn();
            this.btnProcess = new MetroFramework.Controls.MetroButton();
            this.grpAdd = new System.Windows.Forms.GroupBox();
            this.btnAddFiles = new MetroFramework.Controls.MetroButton();
            this.btnAddFolder = new MetroFramework.Controls.MetroButton();
            this.btnClearFiles = new MetroFramework.Controls.MetroButton();
            this.btnClearSuccess = new MetroFramework.Controls.MetroButton();
            this.btnRemove = new MetroFramework.Controls.MetroButton();
            this.grpRemove = new System.Windows.Forms.GroupBox();
            this.lblSaveTo = new System.Windows.Forms.Label();
            this.txtSaveToPath = new System.Windows.Forms.TextBox();
            this.btnResultFolder = new System.Windows.Forms.Button();
            this.btnSelectedPath = new System.Windows.Forms.Button();
            this.grpOperate = new System.Windows.Forms.GroupBox();
            this.lblEngine = new System.Windows.Forms.Label();
            this.nFailedCount = new System.Windows.Forms.NumericUpDown();
            this.nMaxThread = new System.Windows.Forms.NumericUpDown();
            this.nTimeOutSecond = new System.Windows.Forms.NumericUpDown();
            this.lblRetryTimes = new System.Windows.Forms.Label();
            this.lblParTaskCount = new System.Windows.Forms.Label();
            this.lblTimeOutUnit = new System.Windows.Forms.Label();
            this.lblTimeOut = new System.Windows.Forms.Label();
            this.chkSameFolder = new CheckBoxWithTip();
            this.cmbCompressEngine = new System.Windows.Forms.ComboBox();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.panel2 = new System.Windows.Forms.Panel();
            ((System.ComponentModel.ISupportInitialize)(this.dgContent)).BeginInit();
            this.grpAdd.SuspendLayout();
            this.grpRemove.SuspendLayout();
            this.grpOperate.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nFailedCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nMaxThread)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nTimeOutSecond)).BeginInit();
            this.panel2.SuspendLayout();
            this.SuspendLayout();
            // 
            // dgContent
            // 
            this.dgContent.AllowDrop = true;
            this.dgContent.AllowUserToAddRows = false;
            this.dgContent.AllowUserToDeleteRows = false;
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.Black;
            this.dgContent.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            this.dgContent.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgContent.BackgroundColor = System.Drawing.Color.White;
            this.dgContent.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(244)))), ((int)(((byte)(244)))), ((int)(((byte)(244)))));
            dataGridViewCellStyle2.Font = CommonString.GetSysNormalFont(12F);
            dataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgContent.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            this.dgContent.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgContent.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.文件名,
            this.压缩引擎,
            this.原始大小,
            this.压缩大小,
            this.状态,
            this.查看});
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle3.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle3.Font = CommonString.GetSysNormalFont(12F);
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgContent.DefaultCellStyle = dataGridViewCellStyle3;
            this.dgContent.IsShowSequence = true;
            this.dgContent.Location = new System.Drawing.Point(0, 0);
            this.dgContent.Margin = new System.Windows.Forms.Padding(0);
            this.dgContent.Name = "dgContent";
            this.dgContent.ReadOnly = true;
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle4.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle4.Font = CommonString.GetSysNormalFont(12F);
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgContent.RowHeadersDefaultCellStyle = dataGridViewCellStyle4;
            this.dgContent.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.AutoSizeToAllHeaders;
            this.dgContent.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.Black;
            this.dgContent.RowTemplate.Height = 30;
            this.dgContent.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgContent.Size = new System.Drawing.Size(819, 618);
            this.dgContent.TabIndex = 3;
            this.dgContent.TabStop = false;
            this.dgContent.DragDrop += new System.Windows.Forms.DragEventHandler(this.dgContent_DragDrop);
            this.dgContent.DragEnter += new System.Windows.Forms.DragEventHandler(this.dgContent_DragEnter);
            // 
            // 文件名
            // 
            this.文件名.DataPropertyName = "FileName";
            this.文件名.FillWeight = 20F;
            this.文件名.HeaderText = "文件名";
            this.文件名.Name = "文件名";
            this.文件名.ReadOnly = true;
            this.文件名.Width = 300;
            // 
            // 压缩引擎
            // 
            this.压缩引擎.DataPropertyName = "CompressType";
            this.压缩引擎.HeaderText = "处理方式";
            this.压缩引擎.Name = "处理方式";
            this.压缩引擎.ReadOnly = true;
            // 
            // 原始大小
            // 
            this.原始大小.DataPropertyName = "InPutSize";
            this.原始大小.FillWeight = 10F;
            this.原始大小.HeaderText = "原始大小";
            this.原始大小.Name = "原始大小";
            this.原始大小.ReadOnly = true;
            // 
            // 压缩大小
            // 
            this.压缩大小.DataPropertyName = "OutPutSize";
            this.压缩大小.FillWeight = 10F;
            this.压缩大小.HeaderText = "压缩大小";
            this.压缩大小.Name = "压缩大小";
            this.压缩大小.ReadOnly = true;
            // 
            // 状态
            // 
            this.状态.DataPropertyName = "State";
            this.状态.FillWeight = 10F;
            this.状态.HeaderText = "状态";
            this.状态.Name = "状态";
            this.状态.ReadOnly = true;
            // 
            // 查看
            // 
            this.查看.FillWeight = 20F;
            this.查看.HeaderText = "查看";
            this.查看.Name = "查看";
            this.查看.ReadOnly = true;
            this.查看.Text = "查看结果";
            this.查看.Width = 120;
            // 
            // btnProcess
            // 
            this.btnProcess.Font = CommonString.GetSysBoldFont(12F);
            this.btnProcess.Location = new System.Drawing.Point(24, 167);
            this.btnProcess.Size = new System.Drawing.Size(103, 38);
            this.btnProcess.Style = MetroFramework.MetroColorStyle.黑色;
            this.btnProcess.TabIndex = 0;
            this.btnProcess.Text = "开始(&S)";
            this.btnProcess.UseSelectable = true;
            this.btnProcess.UseVisualStyleBackColor = true;
            this.btnProcess.Click += new System.EventHandler(this.btnProcess_Click);
            // 
            // grpAdd
            // 
            this.grpAdd.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.grpAdd.BackColor = System.Drawing.Color.White;
            this.grpAdd.Controls.Add(this.btnAddFiles);
            this.grpAdd.Controls.Add(this.btnAddFolder);
            this.grpAdd.Location = new System.Drawing.Point(822, 8);
            this.grpAdd.Size = new System.Drawing.Size(147, 102);
            this.grpAdd.TabIndex = 7;
            this.grpAdd.TabStop = false;
            this.grpAdd.Text = "添加";
            // 
            // btnAddFiles
            // 
            this.btnAddFiles.Location = new System.Drawing.Point(31, 24);
            this.btnAddFiles.Size = new System.Drawing.Size(88, 29);
            this.btnAddFiles.Style = MetroFramework.MetroColorStyle.黑色;
            this.btnAddFiles.TabIndex = 6;
            this.btnAddFiles.TabStop = false;
            this.btnAddFiles.Text = "添加文件";
            this.btnAddFiles.UseSelectable = true;
            this.btnAddFiles.UseVisualStyleBackColor = true;
            this.btnAddFiles.Click += new System.EventHandler(this.btnAddFiles_Click);
            // 
            // btnAddFolder
            // 
            this.btnAddFolder.Location = new System.Drawing.Point(31, 58);
            this.btnAddFolder.Size = new System.Drawing.Size(88, 29);
            this.btnAddFolder.Style = MetroFramework.MetroColorStyle.黑色;
            this.btnAddFolder.TabIndex = 6;
            this.btnAddFolder.TabStop = false;
            this.btnAddFolder.Text = "添加文件夹";
            this.btnAddFolder.UseSelectable = true;
            this.btnAddFolder.UseVisualStyleBackColor = true;
            this.btnAddFolder.Click += new System.EventHandler(this.btnAddFolder_Click);
            // 
            // btnClearFiles
            // 
            this.btnClearFiles.Location = new System.Drawing.Point(32, 79);
            this.btnClearFiles.Size = new System.Drawing.Size(88, 26);
            this.btnClearFiles.Style = MetroFramework.MetroColorStyle.黑色;
            this.btnClearFiles.TabIndex = 6;
            this.btnClearFiles.TabStop = false;
            this.btnClearFiles.Text = "清空所有";
            this.btnClearFiles.UseSelectable = true;
            this.btnClearFiles.UseVisualStyleBackColor = true;
            this.btnClearFiles.Click += new System.EventHandler(this.btnClearFiles_Click);
            // 
            // btnClearSuccess
            // 
            this.btnClearSuccess.Location = new System.Drawing.Point(32, 48);
            this.btnClearSuccess.Size = new System.Drawing.Size(88, 26);
            this.btnClearSuccess.Style = MetroFramework.MetroColorStyle.黑色;
            this.btnClearSuccess.TabIndex = 6;
            this.btnClearSuccess.TabStop = false;
            this.btnClearSuccess.Text = "移除已完成";
            this.btnClearSuccess.UseSelectable = true;
            this.btnClearSuccess.UseVisualStyleBackColor = true;
            this.btnClearSuccess.Click += new System.EventHandler(this.btnClearSuccess_Click);
            // 
            // btnRemove
            // 
            this.btnRemove.Location = new System.Drawing.Point(32, 17);
            this.btnRemove.Size = new System.Drawing.Size(88, 26);
            this.btnRemove.Style = MetroFramework.MetroColorStyle.黑色;
            this.btnRemove.TabIndex = 6;
            this.btnRemove.TabStop = false;
            this.btnRemove.Text = "移除选择项";
            this.btnRemove.UseSelectable = true;
            this.btnRemove.UseVisualStyleBackColor = true;
            this.btnRemove.Click += new System.EventHandler(this.btnRemoveSelected_Click);
            // 
            // grpRemove
            // 
            this.grpRemove.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.grpRemove.BackColor = System.Drawing.Color.White;
            this.grpRemove.Controls.Add(this.btnClearSuccess);
            this.grpRemove.Controls.Add(this.btnRemove);
            this.grpRemove.Controls.Add(this.btnClearFiles);
            this.grpRemove.Location = new System.Drawing.Point(822, 497);
            this.grpRemove.Size = new System.Drawing.Size(147, 116);
            this.grpRemove.TabIndex = 8;
            this.grpRemove.TabStop = false;
            this.grpRemove.Text = "移除";
            // 
            // lblSaveTo
            // 
            this.lblSaveTo.AutoSize = true;
            this.lblSaveTo.ForeColor = System.Drawing.Color.Red;
            this.lblSaveTo.Location = new System.Drawing.Point(12, 226);
            this.lblSaveTo.Size = new System.Drawing.Size(107, 12);
            this.lblSaveTo.TabIndex = 8;
            this.lblSaveTo.Text = "将文件保存到:";
            // 
            // txtSaveToPath
            // 
            this.txtSaveToPath.BackColor = System.Drawing.SystemColors.ControlLightLight;
            this.txtSaveToPath.Location = new System.Drawing.Point(12, 248);
            this.txtSaveToPath.Name = "txtSaveToPath";
            this.txtSaveToPath.ReadOnly = true;
            this.txtSaveToPath.Size = new System.Drawing.Size(97, 21);
            this.txtSaveToPath.TabIndex = 10;
            // 
            // btnResultFolder
            // 
            this.btnResultFolder.Location = new System.Drawing.Point(23, 299);
            this.btnResultFolder.Size = new System.Drawing.Size(104, 30);
            this.btnResultFolder.TabIndex = 9;
            this.btnResultFolder.Text = "打开结果目录";
            this.btnResultFolder.UseVisualStyleBackColor = true;
            this.btnResultFolder.Click += new System.EventHandler(this.btnResultFolder_Click);
            // 
            // btnSelectedPath
            // 
            this.btnSelectedPath.Location = new System.Drawing.Point(110, 247);
            this.btnSelectedPath.Size = new System.Drawing.Size(31, 23);
            this.btnSelectedPath.TabIndex = 9;
            this.btnSelectedPath.Text = "...";
            this.btnSelectedPath.UseVisualStyleBackColor = true;
            this.btnSelectedPath.Click += new System.EventHandler(this.btnSelectedPath_Click);
            // 
            // grpOperate
            // 
            this.grpOperate.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.grpOperate.BackColor = System.Drawing.Color.White;
            this.grpOperate.Controls.Add(this.lblEngine);
            this.grpOperate.Controls.Add(this.nFailedCount);
            this.grpOperate.Controls.Add(this.nMaxThread);
            this.grpOperate.Controls.Add(this.nTimeOutSecond);
            this.grpOperate.Controls.Add(this.lblRetryTimes);
            this.grpOperate.Controls.Add(this.lblParTaskCount);
            this.grpOperate.Controls.Add(this.lblTimeOutUnit);
            this.grpOperate.Controls.Add(this.lblTimeOut);
            this.grpOperate.Controls.Add(this.chkSameFolder);
            this.grpOperate.Controls.Add(this.btnProcess);
            this.grpOperate.Controls.Add(this.lblSaveTo);
            this.grpOperate.Controls.Add(this.btnSelectedPath);
            this.grpOperate.Controls.Add(this.btnResultFolder);
            this.grpOperate.Controls.Add(this.cmbCompressEngine);
            this.grpOperate.Controls.Add(this.txtSaveToPath);
            this.grpOperate.Location = new System.Drawing.Point(822, 120);
            this.grpOperate.Size = new System.Drawing.Size(147, 366);
            this.grpOperate.TabIndex = 9;
            this.grpOperate.TabStop = false;
            this.grpOperate.Text = "操作";
            // 
            // lblEngine
            // 
            this.lblEngine.AutoSize = true;
            this.lblEngine.Font = CommonString.GetSysBoldFont(12);
            this.lblEngine.Location = new System.Drawing.Point(6, 20);
            this.lblEngine.Size = new System.Drawing.Size(99, 19);
            this.lblEngine.TabIndex = 49;
            this.lblEngine.Text = "选择处理方式";
            // 
            // nFailedCount
            // 
            this.nFailedCount.Location = new System.Drawing.Point(91, 121);
            this.nFailedCount.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.nFailedCount.Name = "nFailedCount";
            this.nFailedCount.Size = new System.Drawing.Size(38, 21);
            this.nFailedCount.TabIndex = 11;
            this.nFailedCount.TabStop = false;
            this.nFailedCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nFailedCount.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // nMaxThread
            // 
            this.nMaxThread.Location = new System.Drawing.Point(103, 96);
            this.nMaxThread.Maximum = new decimal(new int[] {
            30,
            0,
            0,
            0});
            this.nMaxThread.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.nMaxThread.Name = "nMaxThread";
            this.nMaxThread.Size = new System.Drawing.Size(38, 21);
            this.nMaxThread.TabIndex = 11;
            this.nMaxThread.TabStop = false;
            this.nMaxThread.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nMaxThread.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // nTimeOutSecond
            // 
            this.nTimeOutSecond.Location = new System.Drawing.Point(65, 71);
            this.nTimeOutSecond.Maximum = new decimal(new int[] {
            360,
            0,
            0,
            0});
            this.nTimeOutSecond.Minimum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.nTimeOutSecond.Name = "nTimeOutSecond";
            this.nTimeOutSecond.Size = new System.Drawing.Size(46, 21);
            this.nTimeOutSecond.TabIndex = 11;
            this.nTimeOutSecond.TabStop = false;
            this.nTimeOutSecond.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nTimeOutSecond.Value = new decimal(new int[] {
            60,
            0,
            0,
            0});
            // 
            // lblRetryTimes
            // 
            this.lblRetryTimes.AutoSize = true;
            this.lblRetryTimes.Location = new System.Drawing.Point(6, 125);
            this.lblRetryTimes.Size = new System.Drawing.Size(77, 12);
            this.lblRetryTimes.TabIndex = 12;
            this.lblRetryTimes.Text = "失败重试次数";
            // 
            // lblParTaskCount
            // 
            this.lblParTaskCount.AutoSize = true;
            this.lblParTaskCount.Location = new System.Drawing.Point(6, 100);
            this.lblParTaskCount.Size = new System.Drawing.Size(89, 12);
            this.lblParTaskCount.TabIndex = 12;
            this.lblParTaskCount.Text = "同时处理任务数";
            // 
            // lblTimeOutUnit
            // 
            this.lblTimeOutUnit.AutoSize = true;
            this.lblTimeOutUnit.Location = new System.Drawing.Point(108, 75);
            this.lblTimeOutUnit.Size = new System.Drawing.Size(17, 12);
            this.lblTimeOutUnit.TabIndex = 12;
            this.lblTimeOutUnit.Text = "秒";
            // 
            // lblTimeOut
            // 
            this.lblTimeOut.AutoSize = true;
            this.lblTimeOut.Location = new System.Drawing.Point(6, 75);
            this.lblTimeOut.Size = new System.Drawing.Size(53, 12);
            this.lblTimeOut.TabIndex = 12;
            this.lblTimeOut.Text = "处理超时";
            // 
            // chkSameFolder
            // 
            this.chkSameFolder.AutoSize = true;
            this.chkSameFolder.Location = new System.Drawing.Point(12, 276);
            this.chkSameFolder.Size = new System.Drawing.Size(108, 16);
            this.chkSameFolder.TabIndex = 8;
            this.chkSameFolder.Text = "保存在图片目录";
            this.chkSameFolder.UseVisualStyleBackColor = true;
            this.chkSameFolder.TipControl = toolTip1;
            this.chkSameFolder.LimitMaxWidth = false;
            this.chkSameFolder.TipText = "功能：压缩结果，是否存放在与图片相同的目录。\r\n说明：\r\n      如果图片来源文件夹比较多，使用时请慎重！结果将分散在各个目录下边！\r\n";
            // 
            // cmbCompressEngine
            // 
            this.cmbCompressEngine.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbCompressEngine.Font = CommonString.GetSysNormalFont(10F);
            this.cmbCompressEngine.FormattingEnabled = true;
            this.cmbCompressEngine.ItemHeight = 13;
            this.cmbCompressEngine.Location = new System.Drawing.Point(10, 42);
            this.cmbCompressEngine.Name = "cmbCompressEngine";
            this.cmbCompressEngine.Size = new System.Drawing.Size(128, 21);
            this.cmbCompressEngine.TabIndex = 5;
            this.cmbCompressEngine.TabStop = false;
            // 
            // panel2
            // 
            this.panel2.BackColor = System.Drawing.Color.White;
            this.panel2.Controls.Add(this.grpOperate);
            this.panel2.Controls.Add(this.dgContent);
            this.panel2.Controls.Add(this.grpRemove);
            this.panel2.Controls.Add(this.grpAdd);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel2.Location = new System.Drawing.Point(20, 60);
            this.panel2.Size = new System.Drawing.Size(976, 621);
            this.panel2.TabIndex = 46;
            // 
            // FrmBatchCompress
            // 
            this.ClientSize = new System.Drawing.Size(1016, 701);
            this.Controls.Add(this.panel2);
            this.Name = "FrmBatchCompress";
            this.Text = "批量图片压缩";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FrmBatch_FormClosing);
            this.Load += new System.EventHandler(this.FrmBatch_Load);
            ((System.ComponentModel.ISupportInitialize)(this.dgContent)).EndInit();
            this.grpAdd.ResumeLayout(false);
            this.grpRemove.ResumeLayout(false);
            this.grpOperate.ResumeLayout(false);
            this.grpOperate.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nFailedCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nMaxThread)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nTimeOutSecond)).EndInit();
            this.panel2.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DataGridViewEx dgContent;
        private MetroFramework.Controls.MetroButton btnProcess;
        private System.Windows.Forms.GroupBox grpAdd;
        private System.Windows.Forms.GroupBox grpRemove;
        private MetroFramework.Controls.MetroButton btnClearFiles;
        private MetroFramework.Controls.MetroButton btnAddFolder;
        private MetroFramework.Controls.MetroButton btnClearSuccess;
        private MetroFramework.Controls.MetroButton btnAddFiles;
        private MetroFramework.Controls.MetroButton btnRemove;
        private System.Windows.Forms.GroupBox grpOperate;
        private System.Windows.Forms.TextBox txtSaveToPath;
        private System.Windows.Forms.Button btnSelectedPath;
        private System.Windows.Forms.Label lblSaveTo;
        private System.Windows.Forms.Button btnResultFolder;
        private System.Windows.Forms.NumericUpDown nMaxThread;
        private System.Windows.Forms.NumericUpDown nTimeOutSecond;
        private System.Windows.Forms.Label lblParTaskCount;
        private System.Windows.Forms.Label lblTimeOutUnit;
        private System.Windows.Forms.Label lblTimeOut;
        private System.Windows.Forms.NumericUpDown nFailedCount;
        private System.Windows.Forms.Label lblRetryTimes;
        private CheckBoxWithTip chkSameFolder;
        private ToolTip toolTip1;
        private ComboBox cmbCompressEngine;
        private DataGridViewTextBoxColumn 文件名;
        private DataGridViewTextBoxColumn 压缩引擎;
        private DataGridViewTextBoxColumn 原始大小;
        private DataGridViewTextBoxColumn 压缩大小;
        private DataGridViewTextBoxColumn 状态;
        private DataGridViewLinkColumn 查看;
        private Label lblEngine;
        private Panel panel2;
    }
}