﻿using System.Windows.Forms;

namespace OCRTools
{
    partial class FrmBatchCompress
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            this.dgContent = new OCRTools.DataGridViewEx();
            this.文件名 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.压缩引擎 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.原始大小 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.压缩大小 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.状态 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.查看 = new System.Windows.Forms.DataGridViewLinkColumn();
            this.btnProcess = new MetroFramework.Controls.MetroButton();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.btnAddFiles = new MetroFramework.Controls.MetroButton();
            this.btnAddFolder = new MetroFramework.Controls.MetroButton();
            this.btnClearFiles = new MetroFramework.Controls.MetroButton();
            this.btnClearSuccess = new MetroFramework.Controls.MetroButton();
            this.btnRemove = new MetroFramework.Controls.MetroButton();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label2 = new System.Windows.Forms.Label();
            this.txtSaveToPath = new System.Windows.Forms.TextBox();
            this.btnResultFolder = new System.Windows.Forms.Button();
            this.btnSelectedPath = new System.Windows.Forms.Button();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.label6 = new System.Windows.Forms.Label();
            this.pictureBox10 = new System.Windows.Forms.PictureBox();
            this.nFailedCount = new System.Windows.Forms.NumericUpDown();
            this.nMaxThread = new System.Windows.Forms.NumericUpDown();
            this.nTimeOutSecond = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.chkSameFolder = new System.Windows.Forms.CheckBox();
            this.cmbCompressEngine = new System.Windows.Forms.ComboBox();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.panel2 = new System.Windows.Forms.Panel();
            ((System.ComponentModel.ISupportInitialize)(this.dgContent)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nFailedCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nMaxThread)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nTimeOutSecond)).BeginInit();
            this.panel2.SuspendLayout();
            this.SuspendLayout();
            // 
            // dgContent
            // 
            this.dgContent.AllowDrop = true;
            this.dgContent.AllowUserToAddRows = false;
            this.dgContent.AllowUserToDeleteRows = false;
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.Black;
            this.dgContent.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            this.dgContent.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgContent.BackgroundColor = System.Drawing.Color.White;
            this.dgContent.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(244)))), ((int)(((byte)(244)))), ((int)(((byte)(244)))));
            dataGridViewCellStyle2.Font = CommonString.GetSysNormalFont(12F);
            dataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgContent.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            this.dgContent.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgContent.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.文件名,
            this.压缩引擎,
            this.原始大小,
            this.压缩大小,
            this.状态,
            this.查看});
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle3.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle3.Font = CommonString.GetSysNormalFont(12F);
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgContent.DefaultCellStyle = dataGridViewCellStyle3;
            this.dgContent.IsShowSequence = true;
            this.dgContent.Location = new System.Drawing.Point(0, 0);
            this.dgContent.Margin = new System.Windows.Forms.Padding(0);
            this.dgContent.Name = "dgContent";
            this.dgContent.ReadOnly = true;
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle4.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle4.Font = CommonString.GetSysNormalFont(12F);
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgContent.RowHeadersDefaultCellStyle = dataGridViewCellStyle4;
            this.dgContent.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.AutoSizeToAllHeaders;
            this.dgContent.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.Black;
            this.dgContent.RowTemplate.Height = 30;
            this.dgContent.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgContent.Size = new System.Drawing.Size(819, 618);
            this.dgContent.TabIndex = 3;
            this.dgContent.TabStop = false;
            this.dgContent.DragDrop += new System.Windows.Forms.DragEventHandler(this.dgContent_DragDrop);
            this.dgContent.DragEnter += new System.Windows.Forms.DragEventHandler(this.dgContent_DragEnter);
            // 
            // 文件名
            // 
            this.文件名.DataPropertyName = "FileName";
            this.文件名.FillWeight = 20F;
            this.文件名.HeaderText = "文件名";
            this.文件名.Name = "文件名";
            this.文件名.ReadOnly = true;
            this.文件名.Width = 300;
            // 
            // 压缩引擎
            // 
            this.压缩引擎.DataPropertyName = "CompressType";
            this.压缩引擎.HeaderText = "处理引擎";
            this.压缩引擎.Name = "压缩引擎";
            this.压缩引擎.ReadOnly = true;
            // 
            // 原始大小
            // 
            this.原始大小.DataPropertyName = "InPutSize";
            this.原始大小.FillWeight = 10F;
            this.原始大小.HeaderText = "原始大小";
            this.原始大小.Name = "原始大小";
            this.原始大小.ReadOnly = true;
            // 
            // 压缩大小
            // 
            this.压缩大小.DataPropertyName = "OutPutSize";
            this.压缩大小.FillWeight = 10F;
            this.压缩大小.HeaderText = "压缩大小";
            this.压缩大小.Name = "压缩大小";
            this.压缩大小.ReadOnly = true;
            // 
            // 状态
            // 
            this.状态.DataPropertyName = "State";
            this.状态.FillWeight = 10F;
            this.状态.HeaderText = "状态";
            this.状态.Name = "状态";
            this.状态.ReadOnly = true;
            // 
            // 查看
            // 
            this.查看.FillWeight = 20F;
            this.查看.HeaderText = "查看";
            this.查看.Name = "查看";
            this.查看.ReadOnly = true;
            this.查看.Text = "查看结果";
            this.查看.Width = 120;
            // 
            // btnProcess
            // 
            this.btnProcess.Font = CommonString.GetSysBoldFont(12F);
            this.btnProcess.Location = new System.Drawing.Point(24, 167);
            this.btnProcess.Name = "btnProcess";
            this.btnProcess.Size = new System.Drawing.Size(103, 38);
            this.btnProcess.Style = MetroFramework.MetroColorStyle.黑色;
            this.btnProcess.TabIndex = 0;
            this.btnProcess.Text = "开始压缩(&S)";
            this.btnProcess.UseSelectable = true;
            this.btnProcess.UseVisualStyleBackColor = true;
            this.btnProcess.Click += new System.EventHandler(this.btnProcess_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.BackColor = System.Drawing.Color.White;
            this.groupBox1.Controls.Add(this.btnAddFiles);
            this.groupBox1.Controls.Add(this.btnAddFolder);
            this.groupBox1.Location = new System.Drawing.Point(822, 8);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(147, 102);
            this.groupBox1.TabIndex = 7;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "添加";
            // 
            // btnAddFiles
            // 
            this.btnAddFiles.Location = new System.Drawing.Point(31, 24);
            this.btnAddFiles.Name = "btnAddFiles";
            this.btnAddFiles.Size = new System.Drawing.Size(88, 29);
            this.btnAddFiles.Style = MetroFramework.MetroColorStyle.黑色;
            this.btnAddFiles.TabIndex = 6;
            this.btnAddFiles.TabStop = false;
            this.btnAddFiles.Text = "添加文件";
            this.btnAddFiles.UseSelectable = true;
            this.btnAddFiles.UseVisualStyleBackColor = true;
            this.btnAddFiles.Click += new System.EventHandler(this.btnAddFiles_Click);
            // 
            // btnAddFolder
            // 
            this.btnAddFolder.Location = new System.Drawing.Point(31, 58);
            this.btnAddFolder.Name = "btnAddFolder";
            this.btnAddFolder.Size = new System.Drawing.Size(88, 29);
            this.btnAddFolder.Style = MetroFramework.MetroColorStyle.黑色;
            this.btnAddFolder.TabIndex = 6;
            this.btnAddFolder.TabStop = false;
            this.btnAddFolder.Text = "添加文件夹";
            this.btnAddFolder.UseSelectable = true;
            this.btnAddFolder.UseVisualStyleBackColor = true;
            this.btnAddFolder.Click += new System.EventHandler(this.btnAddFolder_Click);
            // 
            // btnClearFiles
            // 
            this.btnClearFiles.Location = new System.Drawing.Point(32, 79);
            this.btnClearFiles.Name = "btnClearFiles";
            this.btnClearFiles.Size = new System.Drawing.Size(88, 26);
            this.btnClearFiles.Style = MetroFramework.MetroColorStyle.黑色;
            this.btnClearFiles.TabIndex = 6;
            this.btnClearFiles.TabStop = false;
            this.btnClearFiles.Text = "清空所有";
            this.btnClearFiles.UseSelectable = true;
            this.btnClearFiles.UseVisualStyleBackColor = true;
            this.btnClearFiles.Click += new System.EventHandler(this.btnClearFiles_Click);
            // 
            // btnClearSuccess
            // 
            this.btnClearSuccess.Location = new System.Drawing.Point(32, 48);
            this.btnClearSuccess.Name = "btnClearSuccess";
            this.btnClearSuccess.Size = new System.Drawing.Size(88, 26);
            this.btnClearSuccess.Style = MetroFramework.MetroColorStyle.黑色;
            this.btnClearSuccess.TabIndex = 6;
            this.btnClearSuccess.TabStop = false;
            this.btnClearSuccess.Text = "移除已压缩";
            this.btnClearSuccess.UseSelectable = true;
            this.btnClearSuccess.UseVisualStyleBackColor = true;
            this.btnClearSuccess.Click += new System.EventHandler(this.btnClearSuccess_Click);
            // 
            // btnRemove
            // 
            this.btnRemove.Location = new System.Drawing.Point(32, 17);
            this.btnRemove.Name = "btnRemove";
            this.btnRemove.Size = new System.Drawing.Size(88, 26);
            this.btnRemove.Style = MetroFramework.MetroColorStyle.黑色;
            this.btnRemove.TabIndex = 6;
            this.btnRemove.TabStop = false;
            this.btnRemove.Text = "移除选择项";
            this.btnRemove.UseSelectable = true;
            this.btnRemove.UseVisualStyleBackColor = true;
            this.btnRemove.Click += new System.EventHandler(this.btnRemoveSelected_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox2.BackColor = System.Drawing.Color.White;
            this.groupBox2.Controls.Add(this.btnClearSuccess);
            this.groupBox2.Controls.Add(this.btnRemove);
            this.groupBox2.Controls.Add(this.btnClearFiles);
            this.groupBox2.Location = new System.Drawing.Point(822, 497);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(147, 116);
            this.groupBox2.TabIndex = 8;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "移除";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.ForeColor = System.Drawing.Color.Red;
            this.label2.Location = new System.Drawing.Point(12, 226);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(107, 12);
            this.label2.TabIndex = 8;
            this.label2.Text = "将压缩文件保存到:";
            // 
            // txtSaveToPath
            // 
            this.txtSaveToPath.BackColor = System.Drawing.SystemColors.ControlLightLight;
            this.txtSaveToPath.Location = new System.Drawing.Point(12, 248);
            this.txtSaveToPath.Name = "txtSaveToPath";
            this.txtSaveToPath.ReadOnly = true;
            this.txtSaveToPath.Size = new System.Drawing.Size(97, 21);
            this.txtSaveToPath.TabIndex = 10;
            // 
            // btnResultFolder
            // 
            this.btnResultFolder.Location = new System.Drawing.Point(23, 299);
            this.btnResultFolder.Name = "btnResultFolder";
            this.btnResultFolder.Size = new System.Drawing.Size(104, 30);
            this.btnResultFolder.TabIndex = 9;
            this.btnResultFolder.Text = "打开结果目录";
            this.btnResultFolder.UseVisualStyleBackColor = true;
            this.btnResultFolder.Click += new System.EventHandler(this.btnResultFolder_Click);
            // 
            // btnSelectedPath
            // 
            this.btnSelectedPath.Location = new System.Drawing.Point(110, 247);
            this.btnSelectedPath.Name = "btnSelectedPath";
            this.btnSelectedPath.Size = new System.Drawing.Size(31, 23);
            this.btnSelectedPath.TabIndex = 9;
            this.btnSelectedPath.Text = "...";
            this.btnSelectedPath.UseVisualStyleBackColor = true;
            this.btnSelectedPath.Click += new System.EventHandler(this.btnSelectedPath_Click);
            // 
            // groupBox3
            // 
            this.groupBox3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox3.BackColor = System.Drawing.Color.White;
            this.groupBox3.Controls.Add(this.label6);
            this.groupBox3.Controls.Add(this.pictureBox10);
            this.groupBox3.Controls.Add(this.nFailedCount);
            this.groupBox3.Controls.Add(this.nMaxThread);
            this.groupBox3.Controls.Add(this.nTimeOutSecond);
            this.groupBox3.Controls.Add(this.label5);
            this.groupBox3.Controls.Add(this.label4);
            this.groupBox3.Controls.Add(this.label3);
            this.groupBox3.Controls.Add(this.label1);
            this.groupBox3.Controls.Add(this.chkSameFolder);
            this.groupBox3.Controls.Add(this.btnProcess);
            this.groupBox3.Controls.Add(this.label2);
            this.groupBox3.Controls.Add(this.btnSelectedPath);
            this.groupBox3.Controls.Add(this.btnResultFolder);
            this.groupBox3.Controls.Add(this.cmbCompressEngine);
            this.groupBox3.Controls.Add(this.txtSaveToPath);
            this.groupBox3.Location = new System.Drawing.Point(822, 120);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(147, 366);
            this.groupBox3.TabIndex = 9;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "操作";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Font = CommonString.GetSysBoldFont(12);
            this.label6.Location = new System.Drawing.Point(6, 20);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(99, 19);
            this.label6.TabIndex = 49;
            this.label6.Text = "选择压缩引擎";
            // 
            // pictureBox10
            // 
            this.pictureBox10.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox10.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox10.Location = new System.Drawing.Point(120, 273);
            this.pictureBox10.Name = "pictureBox10";
            this.pictureBox10.Size = new System.Drawing.Size(26, 23);
            this.pictureBox10.TabIndex = 47;
            this.pictureBox10.TabStop = false;
            this.toolTip1.SetToolTip(this.pictureBox10, "功能：压缩结果，是否存放在与图片相同的目录。\r\n说明：\r\n      如果图片来源文件夹比较多，使用时请慎重！结果将分散在各个目录下边！\r\n");
            // 
            // nFailedCount
            // 
            this.nFailedCount.Location = new System.Drawing.Point(91, 121);
            this.nFailedCount.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.nFailedCount.Name = "nFailedCount";
            this.nFailedCount.Size = new System.Drawing.Size(38, 21);
            this.nFailedCount.TabIndex = 11;
            this.nFailedCount.TabStop = false;
            this.nFailedCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nFailedCount.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // nMaxThread
            // 
            this.nMaxThread.Location = new System.Drawing.Point(103, 96);
            this.nMaxThread.Maximum = new decimal(new int[] {
            30,
            0,
            0,
            0});
            this.nMaxThread.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.nMaxThread.Name = "nMaxThread";
            this.nMaxThread.Size = new System.Drawing.Size(38, 21);
            this.nMaxThread.TabIndex = 11;
            this.nMaxThread.TabStop = false;
            this.nMaxThread.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nMaxThread.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // nTimeOutSecond
            // 
            this.nTimeOutSecond.Location = new System.Drawing.Point(65, 71);
            this.nTimeOutSecond.Maximum = new decimal(new int[] {
            360,
            0,
            0,
            0});
            this.nTimeOutSecond.Minimum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.nTimeOutSecond.Name = "nTimeOutSecond";
            this.nTimeOutSecond.Size = new System.Drawing.Size(46, 21);
            this.nTimeOutSecond.TabIndex = 11;
            this.nTimeOutSecond.TabStop = false;
            this.nTimeOutSecond.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nTimeOutSecond.Value = new decimal(new int[] {
            60,
            0,
            0,
            0});
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(6, 125);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(77, 12);
            this.label5.TabIndex = 12;
            this.label5.Text = "失败重试次数";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(6, 100);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(89, 12);
            this.label4.TabIndex = 12;
            this.label4.Text = "同时处理任务数";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(108, 75);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(17, 12);
            this.label3.TabIndex = 12;
            this.label3.Text = "秒";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(6, 75);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 12;
            this.label1.Text = "处理超时";
            // 
            // chkSameFolder
            // 
            this.chkSameFolder.AutoSize = true;
            this.chkSameFolder.Location = new System.Drawing.Point(12, 276);
            this.chkSameFolder.Name = "chkSameFolder";
            this.chkSameFolder.Size = new System.Drawing.Size(108, 16);
            this.chkSameFolder.TabIndex = 8;
            this.chkSameFolder.Text = "保存在图片目录";
            this.chkSameFolder.UseVisualStyleBackColor = true;
            // 
            // cmbCompressEngine
            // 
            this.cmbCompressEngine.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbCompressEngine.Font = CommonString.GetSysNormalFont(10F);
            this.cmbCompressEngine.FormattingEnabled = true;
            this.cmbCompressEngine.ItemHeight = 13;
            this.cmbCompressEngine.Location = new System.Drawing.Point(10, 42);
            this.cmbCompressEngine.Name = "cmbCompressEngine";
            this.cmbCompressEngine.Size = new System.Drawing.Size(128, 21);
            this.cmbCompressEngine.TabIndex = 5;
            this.cmbCompressEngine.TabStop = false;
            // 
            // panel2
            // 
            this.panel2.BackColor = System.Drawing.Color.White;
            this.panel2.Controls.Add(this.groupBox3);
            this.panel2.Controls.Add(this.dgContent);
            this.panel2.Controls.Add(this.groupBox2);
            this.panel2.Controls.Add(this.groupBox1);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel2.Location = new System.Drawing.Point(20, 60);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(976, 621);
            this.panel2.TabIndex = 46;
            // 
            // FrmBatchCompress
            // 
            this.ClientSize = new System.Drawing.Size(1016, 701);
            this.Controls.Add(this.panel2);
            this.Name = "FrmBatchCompress";
            this.Text = "批量压缩";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FrmBatch_FormClosing);
            this.Load += new System.EventHandler(this.FrmBatch_Load);
            ((System.ComponentModel.ISupportInitialize)(this.dgContent)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nFailedCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nMaxThread)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nTimeOutSecond)).EndInit();
            this.panel2.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DataGridViewEx dgContent;
        private MetroFramework.Controls.MetroButton btnProcess;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private MetroFramework.Controls.MetroButton btnClearFiles;
        private MetroFramework.Controls.MetroButton btnAddFolder;
        private MetroFramework.Controls.MetroButton btnClearSuccess;
        private MetroFramework.Controls.MetroButton btnAddFiles;
        private MetroFramework.Controls.MetroButton btnRemove;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.TextBox txtSaveToPath;
        private System.Windows.Forms.Button btnSelectedPath;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btnResultFolder;
        private System.Windows.Forms.NumericUpDown nMaxThread;
        private System.Windows.Forms.NumericUpDown nTimeOutSecond;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown nFailedCount;
        private System.Windows.Forms.Label label5;
        private CheckBox chkSameFolder;
        private PictureBox pictureBox10;
        private ToolTip toolTip1;
        private ComboBox cmbCompressEngine;
        private DataGridViewTextBoxColumn 文件名;
        private DataGridViewTextBoxColumn 压缩引擎;
        private DataGridViewTextBoxColumn 原始大小;
        private DataGridViewTextBoxColumn 压缩大小;
        private DataGridViewTextBoxColumn 状态;
        private DataGridViewLinkColumn 查看;
        private Label label6;
        private Panel panel2;
    }
}