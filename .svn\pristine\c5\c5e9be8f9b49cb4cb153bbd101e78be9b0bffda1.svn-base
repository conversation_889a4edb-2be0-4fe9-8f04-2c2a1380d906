using System.Collections.Generic;
using System.Text;

namespace OcrLib
{
	public sealed class TextLine
	{
		public string Text { get; set; }

		public List<float> CharScores { get; set; }

		public float Time { get; set; }

		public override string ToString()
		{
			StringBuilder sb = new StringBuilder();
			CharScores.ForEach(delegate(float x)
			{
				sb.Append($"{x},");
			});
			return $"TextLine[Text({Text}),CharScores({sb.ToString()}),Time({Time}ms)]";
		}
	}
}
