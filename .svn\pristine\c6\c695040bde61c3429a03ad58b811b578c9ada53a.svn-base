﻿using OCRTools.Language;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools.UserControlEx
{
    public class ControlWithTip : PictureBox
    {
        public ControlWithTip(Control targetControl, ToolTip TipControl, string TipText, Bitmap TipIcon = null)
        {
            SizeMode = PictureBoxSizeMode.CenterImage;
            BackColor = targetControl.BackColor;
            TabStop = false;
            Padding = CommonString.PaddingZero;
            Visible = false;
            Image = TipIcon ?? Properties.Resources.帮助;
            targetControl.SizeChanged += (object sender, EventArgs e) =>
            {
                ShowTip(targetControl, TipControl, TipText);
            };
            targetControl.TextChanged += (object sender, EventArgs e) =>
            {
                ShowTip(targetControl, TipControl, TipText);
            };
            targetControl.Paint += (object sender, PaintEventArgs e) =>
            {
                ShowTip(targetControl, TipControl, TipText);
            };
            targetControl.Parent?.Controls.Add(this);
        }

        private void ShowTip(Control targetControl, ToolTip TipControl, string TipText)
        {
            Size = new Size(targetControl.Height, targetControl.Height);
            if (targetControl.Left > targetControl.Parent.Width / 3)
            {
                //右边
                Left = Math.Min(targetControl.Left + targetControl.Width + (int)(3 * CommonTheme.DpiScale), targetControl.Parent.Width - Width - (int)(2 * 3 * CommonTheme.DpiScale));
            }
            else
            {
                //左边
                Left = Math.Min(targetControl.Left + targetControl.Width + (int)(3 * CommonTheme.DpiScale), targetControl.Parent.Width / 2 - Width - (int)(3 * CommonTheme.DpiScale));
            }
            Top = targetControl.Top + (int)((targetControl.ClientRectangle.Height - Height) / 2 * CommonTheme.DpiScale);
            if (targetControl is Label || targetControl is CheckBox || targetControl is RadioButton)
            {
                TipControl.SetToolTip(targetControl, targetControl.Text.CurrentText());
                TipControl.SetToolTip(this, "【" + targetControl.Text.CurrentText() + "】\n" + TipText.CurrentText());
            }
            else
            {
                TipControl.SetToolTip(targetControl, TipText.CurrentText());
                TipControl.SetToolTip(this, TipText.CurrentText());
            }
            Visible = true;
            this.BringToFront();
        }
    }
}
