using System.Collections.Generic;

namespace OCRTools
{
    internal class CommandDelete : Command
    {
        private readonly List<DrawObject> _cloneList;

        public CommandDelete(GraphicsList graphicsList)
        {
            _cloneList = new List<DrawObject>();
            foreach (var item in graphicsList.Selection) _cloneList.Add(item.Clone());
        }

        public override void Undo(GraphicsList list)
        {
            list.UnselectAll();
            foreach (var clone in _cloneList) list.Add(clone);
        }

        public override void Redo(GraphicsList list)
        {
            var count = list.Count;
            for (var num = count - 1; num >= 0; num--)
            {
                var flag = false;
                var drawObject = list[num];
                foreach (var clone in _cloneList)
                    if (drawObject.Id == clone.Id)
                    {
                        flag = true;
                        break;
                    }

                if (flag) list.RemoveAt(num);
            }
        }
    }
}