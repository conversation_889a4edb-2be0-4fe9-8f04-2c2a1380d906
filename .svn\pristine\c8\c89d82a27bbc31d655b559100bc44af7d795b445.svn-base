using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace OCRTools
{
    /// <summary>
    /// 支持精确文字选择的图片查看器
    /// </summary>
    public class TextSelectableImageViewer : PanelPictureView
    {
        private readonly Color SELECTION_FILL_COLOR = Color.FromArgb(100, 0, 120, 215); // 类似PDF的选择颜色

        // 数据结构
        private List<TextCellInfo> textCells = new List<TextCellInfo>();

        // 选择相关字段
        private bool isDragging = false;
        private Point dragStartPoint;
        private Point dragEndPoint;
        private TextCellInfo startCell;
        private TextCellInfo endCell;
        private int startCharIndex = -1;
        private int endCharIndex = -1;

        // 事件
        public event EventHandler<TextSelectionEventArgs> TextSelectionChanged;

        /// <summary>
        /// Cell和字符位置信息
        /// </summary>
        private class CellCharPosition
        {
            public TextCellInfo Cell { get; set; }
            public int CharIndex { get; set; }

            public CellCharPosition(TextCellInfo cell, int charIndex)
            {
                Cell = cell;
                CharIndex = charIndex;
            }
        }

        /// <summary>
        /// Cell选择信息
        /// </summary>
        private class CellSelectionInfo
        {
            public TextCellInfo Cell { get; set; }
            public int StartChar { get; set; }
            public int EndChar { get; set; }

            public CellSelectionInfo(TextCellInfo cell, int startChar, int endChar)
            {
                Cell = cell;
                StartChar = startChar;
                EndChar = endChar;
            }
        }

        public TextSelectableImageViewer()
        {
            // 禁用原有的提示功能，我们自己处理
            IsShowTip = false;

            // 重新绑定鼠标事件
            MouseDown += OnMouseDown;
            MouseMove += OnMouseMove;
            MouseUp += OnMouseUp;
            Paint += OnCustomPaint;
            MouseLeave += OnMouseLeave;
        }

        /// <summary>
        /// 绑定图片和文字区域
        /// </summary>
        public void BindImageAndTextRegions(Image image, List<TextCellInfo> regions)
        {
            textCells = regions ?? new List<TextCellInfo>();

            // 清除选择
            ClearSelection();

            // 设置图片
            Image = image;

            // 标记为绑定模式
            IsBindImageMode = true;

            Invalidate();
        }
        
        /// <summary>
        /// 检测单个文字块的方向
        /// </summary>
        private bool DetectCellDirection(TextCellInfo cell)
        {
            if (cell?.location == null || string.IsNullOrEmpty(cell.words)) return false;

            double width = cell.location.width;
            double height = cell.location.height;
            double aspectRatio = width / height;

            // 竖排判断条件：
            // 1. 高度明显大于宽度（高宽比 > 2.0）
            // 2. 或者高度 >= 宽度且字符数较少（<=3）
            // 3. 或者是单字符且接近正方形

            bool condition1 = height / width > 2.0; // 明显的竖直形状
            bool condition2 = height >= width && cell.words.Length <= 3; // 高>=宽且字符少
            bool condition3 = cell.words.Length == 1 && aspectRatio > 0.5 && aspectRatio < 2.0; // 单字符且接近正方形

            return condition1 || condition2 || condition3;
        }

        private void OnMouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left && textCells.Count > 0)
            {
                // 清除之前的选择
                ClearSelection();

                dragStartPoint = GetImagePoint(e.Location);
                dragEndPoint = dragStartPoint; // 初始化终点为起点

                // 找到起始位置的cell和字符索引（此时isDragging=false，会保存为起点信息）
                var startPos = GetCellAndCharAtPoint(dragStartPoint);
                startCell = startPos.Cell;
                startCharIndex = startPos.CharIndex;
                endCell = startCell;
                endCharIndex = startCharIndex;

                // 设置拖拽状态（在获取起点信息之后）
                isDragging = true;

                Invalidate();
            }
        }

        private void OnMouseMove(object sender, MouseEventArgs e)
        {
            if (isDragging && e.Button == MouseButtons.Left)
            {
                // 更新拖拽结束点
                dragEndPoint = GetImagePoint(e.Location);

                // 找到结束位置的cell和字符索引
                var endPos = GetCellAndCharAtPoint(dragEndPoint);
                endCell = endPos.Cell;
                endCharIndex = endPos.CharIndex;

                Invalidate();
            }
            else
            {
                // 检查鼠标是否在可选择区域，更新光标
                UpdateCursor(e.Location);
            }
        }

        private void OnMouseLeave(object sender, EventArgs e)
        {
            // 鼠标离开时恢复默认光标
            Cursor = Cursors.Default;
        }

        /// <summary>
        /// 获取指定点位置的cell和字符索引
        /// </summary>
        private CellCharPosition GetCellAndCharAtPoint(Point imagePoint)
        {
            // 收集精确匹配的cells
            var exactMatches = new List<TextCellInfo>();
            foreach (var cell in textCells)
            {
                if (cell?.location != null && !string.IsNullOrEmpty(cell.words))
                {
                    var cellRect = cell.location.Rectangle;
                    if (cellRect.Contains(imagePoint))
                    {
                        exactMatches.Add(cell);
                    }
                }
            }

            // 获取智能匹配结果
            TextCellInfo smartMatch = FindNearestCell(imagePoint);

            // 决策逻辑：在精确匹配和智能匹配之间选择最佳的
            TextCellInfo bestCell = SelectBestCell(exactMatches, smartMatch, imagePoint);

            if (bestCell != null)
            {
                int charIndex = bestCell == smartMatch ?
                    CalculateCharIndexForPoint(bestCell, imagePoint) :
                    GetCharIndexInCell(bestCell, imagePoint);

                return new CellCharPosition(bestCell, charIndex);
            }

            return new CellCharPosition(null, -1);
        }

        /// <summary>
        /// 在精确匹配和智能匹配之间选择最佳cell
        /// </summary>
        private TextCellInfo SelectBestCell(List<TextCellInfo> exactMatches, TextCellInfo smartMatch, Point imagePoint)
        {
            // 如果有精确匹配，直接使用精确匹配，不考虑智能匹配
            // 精确匹配意味着点击位置确实在Cell内部，应该绝对优先
            if (exactMatches.Count > 0)
            {
                return exactMatches.Count == 1 ? exactMatches[0] : GetClosestToCenter(exactMatches, imagePoint);
            }

            // 只有在没有精确匹配时，才使用智能匹配
            return smartMatch;
        }

        private TextCellInfo GetClosestToCenter(List<TextCellInfo> cells, Point imagePoint)
        {
            return cells.OrderBy(cell => GetDistanceToCenter(cell, imagePoint)).First();
        }

        private double GetDistanceToCenter(TextCellInfo cell, Point imagePoint)
        {
            var cellRect = cell.location.Rectangle;
            var centerX = cellRect.X + cellRect.Width / 2;
            var centerY = cellRect.Y + cellRect.Height / 2;
            return Math.Sqrt(Math.Pow(imagePoint.X - centerX, 2) + Math.Pow(imagePoint.Y - centerY, 2));
        }

        /// <summary>
        /// 通用的最近cell查找算法
        /// </summary>
        private class CellCandidate
        {
            public TextCellInfo Cell { get; set; }
            public double WeightedDistance { get; set; }
            public double RawDistance { get; set; }
        }

        private class SelectionConfig
        {
            // 相对合理的固定值
            public double DistanceDiffThreshold { get; set; } = 0.2;
            public double OverlapThreshold { get; set; } = 0.5;

            // 需要自适应计算的值
            public double EndAreaExtensionRatio { get; set; } = 0.3; // cell高度的30%
            public double BottomDetectionRatio { get; set; } = 0.5; // 平均cell高度的50%
            public double RowToleranceRatio { get; set; } = 0.1; // cell高度的10%，最小2像素
            public double WeightMultiplierBase { get; set; } = 2.0; // 基础权重，可根据布局调整
        }

        private readonly SelectionConfig config = new SelectionConfig();

        /// <summary>
        /// 计算自适应的结尾区域扩展大小
        /// </summary>
        private int CalculateEndAreaExtension(Rectangle cellRect)
        {
            return Math.Max(5, (int)(cellRect.Height * config.EndAreaExtensionRatio));
        }

        /// <summary>
        /// 计算自适应的底部检测容差
        /// </summary>
        private int CalculateBottomDetectionTolerance()
        {
            if (textCells.Count == 0) return 10;

            var averageHeight = textCells
                .Where(c => c?.location != null)
                .Average(c => c.location.height);

            return Math.Max(5, (int)(averageHeight * config.BottomDetectionRatio));
        }

        /// <summary>
        /// 计算自适应的行容差
        /// </summary>
        private int CalculateRowTolerance(Rectangle cellRect)
        {
            return Math.Max(2, (int)(cellRect.Height * config.RowToleranceRatio));
        }

        private TextCellInfo FindNearestCell(Point imagePoint)
        {
            // 首先检查是否有cell的"结尾区域"包含点击点
            var endAreaCell = FindCellWithEndAreaContaining(imagePoint);
            if (endAreaCell != null)
            {
                return endAreaCell;
            }

            // 检查是否拖拽到文档底部（所有文字下方）
            var bottomCell = FindBottomCellIfPointBelowAllText(imagePoint);
            if (bottomCell != null)
            {
                return bottomCell;
            }

            var candidates = new List<CellCandidate>();

            foreach (var cell in textCells)
            {
                if (cell?.location == null || string.IsNullOrEmpty(cell.words))
                    continue;

                var cellRect = cell.location.Rectangle;
                bool isCellVertical = DetectCellDirection(cell);

                // 计算到cell的距离
                var centerX = cellRect.X + cellRect.Width / 2;
                var centerY = cellRect.Y + cellRect.Height / 2;
                var xDistance = Math.Abs(imagePoint.X - centerX);
                var yDistance = Math.Abs(imagePoint.Y - centerY);

                // 原始欧几里得距离
                double rawDistance = Math.Sqrt(Math.Pow(xDistance, 2) + Math.Pow(yDistance, 2));

                // 根据文字方向调整权重的距离
                double weightedDistance = CalculateWeightedDistance(xDistance, yDistance, isCellVertical);

                candidates.Add(new CellCandidate
                {
                    Cell = cell,
                    WeightedDistance = weightedDistance,
                    RawDistance = rawDistance
                });
            }

            if (candidates.Count == 0) return null;

            // 按加权距离排序
            candidates.Sort((a, b) => a.WeightedDistance.CompareTo(b.WeightedDistance));

            // 如果最近的两个候选距离很接近（差距小于20%），则进行智能选择
            if (candidates.Count > 1)
            {
                var first = candidates[0];
                var second = candidates[1];
                var distanceDiff = Math.Abs(first.WeightedDistance - second.WeightedDistance);
                var avgDistance = (first.WeightedDistance + second.WeightedDistance) / 2;

                if (distanceDiff / avgDistance < config.DistanceDiffThreshold)
                {
                    // 优先选择原始距离更近的cell
                    if (second.RawDistance < first.RawDistance)
                    {

                        return second.Cell;
                    }
                }
            }

            return candidates[0].Cell;
        }

        /// <summary>
        /// 查找结尾区域包含指定点的cell
        /// </summary>
        private TextCellInfo FindCellWithEndAreaContaining(Point imagePoint)
        {
            foreach (var cell in textCells)
            {
                if (cell?.location == null || string.IsNullOrEmpty(cell.words))
                    continue;

                var cellRect = cell.location.Rectangle;
                bool isCellVertical = DetectCellDirection(cell);

                // 扩展cell的结尾区域
                Rectangle endArea;
                if (isCellVertical)
                {
                    // 竖排：扩展下方区域
                    endArea = new Rectangle(
                        cellRect.X,
                        cellRect.Bottom,
                        cellRect.Width,
                        CalculateEndAreaExtension(cellRect)
                    );
                }
                else
                {
                    // 横排：扩展右方区域
                    endArea = new Rectangle(
                        cellRect.Right,
                        cellRect.Y,
                        CalculateEndAreaExtension(cellRect),
                        cellRect.Height
                    );
                }

                if (endArea.Contains(imagePoint))
                {
                    return cell;
                }
            }

            return null;
        }

        /// <summary>
        /// 如果点击点在所有文字下方，返回最底部的cell
        /// </summary>
        private TextCellInfo FindBottomCellIfPointBelowAllText(Point imagePoint)
        {
            if (textCells.Count == 0) return null;

            // 找到所有cells的最大Y坐标
            var maxBottom = textCells
                .Where(c => c?.location != null && !string.IsNullOrEmpty(c.words))
                .Max(c => c.location.Rectangle.Bottom);

            // 如果点击点在所有文字下方（有一定容差）
            if (imagePoint.Y > maxBottom + CalculateBottomDetectionTolerance())
            {
                // 返回Y坐标最大的cell（最底部的cell）
                return textCells
                    .Where(c => c?.location != null && !string.IsNullOrEmpty(c.words))
                    .OrderByDescending(c => c.location.Rectangle.Bottom)
                    .First();
            }

            return null;
        }

        /// <summary>
        /// 计算加权距离
        /// </summary>
        private double CalculateWeightedDistance(double xDistance, double yDistance, bool isCellVertical)
        {
            if (isCellVertical)
            {
                // 竖排：X轴距离更重要
                return Math.Sqrt(Math.Pow(xDistance * config.WeightMultiplierBase, 2) + Math.Pow(yDistance, 2));
            }
            else
            {
                // 横排：Y轴距离更重要
                return Math.Sqrt(Math.Pow(xDistance, 2) + Math.Pow(yDistance * config.WeightMultiplierBase, 2));
            }
        }

        /// <summary>
        /// 通用的字符索引计算
        /// </summary>
        private int CalculateCharIndexForPoint(TextCellInfo cell, Point imagePoint)
        {
            var cellRect = cell.location.Rectangle;
            bool isCellVertical = DetectCellDirection(cell);

            // 如果点在cell内部，使用精确计算
            if (cellRect.Contains(imagePoint))
            {
                return GetCharIndexInCell(cell, imagePoint);
            }

            // 点在cell外部，根据文字方向和位置关系确定字符索引
            if (isCellVertical)
            {
                // 竖排：从右到左，从上到下
                if (imagePoint.X > cellRect.Right) return 0; // 右侧 = 开头
                if (imagePoint.X < cellRect.Left) return cell.words.Length - 1; // 左侧 = 结尾
                if (imagePoint.Y < cellRect.Top) return 0; // 上方 = 开头
                if (imagePoint.Y > cellRect.Bottom) return cell.words.Length - 1; // 下方 = 结尾
            }
            else
            {
                // 横排：从左到右，从上到下
                if (imagePoint.X < cellRect.Left) return 0; // 左侧 = 开头
                if (imagePoint.X > cellRect.Right) return cell.words.Length - 1; // 右侧 = 结尾
                if (imagePoint.Y < cellRect.Top) return 0; // 上方 = 开头
                if (imagePoint.Y > cellRect.Bottom) return cell.words.Length - 1; // 下方 = 结尾
            }

            // 默认情况（理论上不应该到达这里）
            return GetCharIndexInCell(cell, imagePoint);
        }

        /// <summary>
        /// 判断字符是否为CJK字符（中日韩）
        /// </summary>
        private bool IsCJK(char c)
        {
            return (c >= 0x4E00 && c <= 0x9FFF) || // 中文
                   (c >= 0x3040 && c <= 0x309F) || // 日文平假名
                   (c >= 0x30A0 && c <= 0x30FF) || // 日文片假名
                   (c >= 0xAC00 && c <= 0xD7AF);   // 韩文
        }

        /// <summary>
        /// 估算字体大小（基于cell尺寸，使用多种策略）
        /// </summary>
        private float EstimateFontSize(Rectangle cellRect)
        {
            // 基础估算：字体大小通常是cell高度的70-90%
            float baseFontSize = Math.Max(8, Math.Min(cellRect.Height * 0.8f, 72));

            // 根据cell的宽高比进行调整
            float aspectRatio = (float)cellRect.Width / cellRect.Height;

            // 如果cell很宽（横排长文本），字体可能稍小
            if (aspectRatio > 5.0f)
            {
                baseFontSize *= 0.9f;
            }
            // 如果cell接近正方形（单字符），字体可能稍大
            else if (aspectRatio >= 0.8f && aspectRatio <= 1.2f)
            {
                baseFontSize *= 1.1f;
            }

            return Math.Max(8, Math.Min(baseFontSize, 72));
        }



        /// <summary>
        /// 文本测量结果
        /// </summary>
        private class TextMeasurementResult
        {
            public bool Success { get; set; }
            public List<float> CharWidths { get; set; } = new List<float>();
            public float TotalWidth { get; set; }
            public float FontSize { get; set; }
            public string FontName { get; set; }
        }

        /// <summary>
        /// 测量缓存键
        /// </summary>
        private class MeasurementCacheKey
        {
            public string Text { get; set; }
            public int CellWidth { get; set; }
            public int CellHeight { get; set; }

            public override bool Equals(object obj)
            {
                if (obj is MeasurementCacheKey other)
                {
                    return Text == other.Text && CellWidth == other.CellWidth && CellHeight == other.CellHeight;
                }
                return false;
            }

            public override int GetHashCode()
            {
                unchecked
                {
                    int hash = 17;
                    hash = hash * 23 + (Text?.GetHashCode() ?? 0);
                    hash = hash * 23 + CellWidth.GetHashCode();
                    hash = hash * 23 + CellHeight.GetHashCode();
                    return hash;
                }
            }
        }

        // 测量结果缓存（限制大小避免内存泄漏）
        private readonly Dictionary<MeasurementCacheKey, TextMeasurementResult> measurementCache =
            new Dictionary<MeasurementCacheKey, TextMeasurementResult>();
        private const int MAX_CACHE_SIZE = 100;

        /// <summary>
        /// 智能文本测量（带缓存的自动校准字体大小）
        /// </summary>
        private TextMeasurementResult MeasureTextWithCalibration(string text, Rectangle cellRect)
        {
            // 检查缓存
            var cacheKey = new MeasurementCacheKey
            {
                Text = text,
                CellWidth = cellRect.Width,
                CellHeight = cellRect.Height
            };

            if (measurementCache.TryGetValue(cacheKey, out var cachedResult))
            {
                return cachedResult;
            }

            var result = new TextMeasurementResult();

            // 性能优化：对于短文本（<=3字符）使用简化策略
            if (text.Length <= 3)
            {
                result = MeasureTextSimple(text, cellRect);
            }
            else
            {
                // 对于长文本使用完整的校准策略
                result = MeasureTextWithFullCalibration(text, cellRect);
            }

            // 缓存结果（限制缓存大小）
            if (measurementCache.Count >= MAX_CACHE_SIZE)
            {
                // 清除一半的缓存
                var keysToRemove = measurementCache.Keys.Take(MAX_CACHE_SIZE / 2).ToList();
                foreach (var key in keysToRemove)
                {
                    measurementCache.Remove(key);
                }
            }

            measurementCache[cacheKey] = result;
            return result;
        }

        /// <summary>
        /// 简化的文本测量（用于短文本）
        /// </summary>
        private TextMeasurementResult MeasureTextSimple(string text, Rectangle cellRect)
        {
            float baseFontSize = EstimateFontSize(cellRect);

            // 只尝试3个字体大小
            float[] fontSizeMultipliers = { 0.9f, 1.0f, 1.1f };

            foreach (float multiplier in fontSizeMultipliers)
            {
                float testFontSize = baseFontSize * multiplier;
                var testResult = MeasureTextAtFontSize(text, testFontSize);

                if (testResult.Success)
                {
                    return testResult;
                }
            }

            return new TextMeasurementResult();
        }

        /// <summary>
        /// 完整的文本测量校准（用于长文本）
        /// </summary>
        private TextMeasurementResult MeasureTextWithFullCalibration(string text, Rectangle cellRect)
        {
            float baseFontSize = EstimateFontSize(cellRect);
            float[] fontSizeMultipliers = { 0.8f, 0.9f, 1.0f, 1.1f, 1.2f };

            float bestScore = float.MaxValue;
            TextMeasurementResult bestResult = null;

            foreach (float multiplier in fontSizeMultipliers)
            {
                float testFontSize = baseFontSize * multiplier;
                var testResult = MeasureTextAtFontSize(text, testFontSize);

                if (testResult.Success)
                {
                    // 计算与目标宽度的偏差
                    float deviation = Math.Abs(testResult.TotalWidth - cellRect.Width) / cellRect.Width;

                    if (deviation < bestScore)
                    {
                        bestScore = deviation;
                        bestResult = testResult;
                    }

                    // 如果偏差很小，直接使用
                    if (deviation < 0.15f) // 偏差小于15%
                    {
                        break;
                    }
                }
            }

            return bestResult ?? new TextMeasurementResult();
        }

        /// <summary>
        /// 在指定字体大小下测量文本（优化版本）
        /// </summary>
        private TextMeasurementResult MeasureTextAtFontSize(string text, float fontSize)
        {
            var result = new TextMeasurementResult { FontSize = fontSize };

            try
            {
                // 优先使用最常见的字体，减少尝试次数
                string[] fontNames = { "Microsoft YaHei", "SimSun", "Arial" };

                foreach (string fontName in fontNames)
                {
                    try
                    {
                        using (var font = new Font(fontName, fontSize))
                        using (var bitmap = new Bitmap(1, 1))
                        using (var g = Graphics.FromImage(bitmap))
                        {
                            g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.AntiAlias;

                            var charWidths = new List<float>(text.Length);
                            float totalWidth = 0;

                            // 使用固定的StringFormat，避免重复创建
                            var format = StringFormat.GenericTypographic;

                            foreach (char c in text)
                            {
                                var size = g.MeasureString(c.ToString(), font, PointF.Empty, format);
                                if (size.Width <= 0)
                                {
                                    goto NextFont; // 快速跳出到下一个字体
                                }
                                charWidths.Add(size.Width);
                                totalWidth += size.Width;
                            }

                            // 所有字符测量成功
                            result.Success = true;
                            result.CharWidths = charWidths;
                            result.TotalWidth = totalWidth;
                            result.FontName = fontName;
                            return result;

                            NextFont:;
                        }
                    }
                    catch
                    {
                        continue;
                    }
                }
            }
            catch
            {
                // 测量失败
            }

            return result;
        }

        /// <summary>
        /// 计算在cell内的字符索引（基于实际字体测量+托底平分逻辑）
        /// </summary>
        private int GetCharIndexInCell(TextCellInfo cell, Point imagePoint)
        {
            var cellRect = cell.location.Rectangle;
            bool isCellVertical = DetectCellDirection(cell);

            if (isCellVertical)
            {
                // 竖排：使用改进的高度计算
                return GetCharIndexInVerticalCell(cell, imagePoint, cellRect);
            }
            else
            {
                // 横排：使用改进的宽度计算
                return GetCharIndexInHorizontalCell(cell, imagePoint, cellRect);
            }
        }

        /// <summary>
        /// 计算横排文字中的字符索引（基于实际字体测量+托底平分逻辑）
        /// </summary>
        private int GetCharIndexInHorizontalCell(TextCellInfo cell, Point imagePoint, Rectangle cellRect)
        {
            var relativeX = imagePoint.X - cellRect.X;

            // 尝试基于实际字体测量，使用智能字体大小校准
            var measurementResult = MeasureTextWithCalibration(cell.words, cellRect);

            if (measurementResult.Success)
            {
                var charWidths = measurementResult.CharWidths;
                float totalMeasuredWidth = measurementResult.TotalWidth;

                // 使用字体测量结果
                float scale = cellRect.Width / totalMeasuredWidth;

                float currentX = 0;
                for (int i = 0; i < charWidths.Count; i++)
                {
                    float scaledWidth = charWidths[i] * scale;

                    if (relativeX <= currentX + scaledWidth / 2)
                    {
                        return i;
                    }
                    currentX += scaledWidth;
                }

                return cell.words.Length - 1;
            }
            else
            {
                // 托底：使用平分逻辑
                var charWidth = (float)cellRect.Width / cell.words.Length;
                return Math.Max(0, Math.Min((int)(relativeX / charWidth), cell.words.Length - 1));
            }
        }

        /// <summary>
        /// 测量单个字符的实际高度（用于竖排文字）
        /// </summary>
        private float MeasureCharHeight(char c, float fontSize)
        {
            try
            {
                string[] fontNames = { "Microsoft YaHei", "SimSun", "SimHei", "Arial" };

                foreach (string fontName in fontNames)
                {
                    try
                    {
                        using (var font = new Font(fontName, fontSize))
                        using (var bitmap = new Bitmap(1, 1))
                        using (var g = Graphics.FromImage(bitmap))
                        {
                            g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.AntiAlias;
                            var size = g.MeasureString(c.ToString(), font, PointF.Empty, StringFormat.GenericTypographic);
                            if (size.Height > 0)
                            {
                                return size.Height;
                            }
                        }
                    }
                    catch
                    {
                        continue;
                    }
                }
            }
            catch
            {
                // 所有字体都失败
            }

            // 托底：返回-1表示测量失败
            return -1;
        }

        /// <summary>
        /// 计算竖排文字中的字符索引（基于实际字体测量+托底平分逻辑）
        /// </summary>
        private int GetCharIndexInVerticalCell(TextCellInfo cell, Point imagePoint, Rectangle cellRect)
        {
            var relativeY = imagePoint.Y - cellRect.Y;

            // 尝试基于实际字体测量
            float fontSize = EstimateFontSize(cellRect);
            var charHeights = new List<float>();
            float totalMeasuredHeight = 0;
            bool measurementSucceeded = true;

            foreach (char c in cell.words)
            {
                float charHeight = MeasureCharHeight(c, fontSize);
                if (charHeight <= 0) // 测量失败
                {
                    measurementSucceeded = false;
                    break;
                }
                charHeights.Add(charHeight);
                totalMeasuredHeight += charHeight;
            }

            if (measurementSucceeded && totalMeasuredHeight > 0)
            {
                // 使用字体测量结果
                float scale = cellRect.Height / totalMeasuredHeight;

                float currentY = 0;
                for (int i = 0; i < charHeights.Count; i++)
                {
                    float scaledHeight = charHeights[i] * scale;
                    if (relativeY <= currentY + scaledHeight / 2)
                    {
                        return i;
                    }
                    currentY += scaledHeight;
                }
                return cell.words.Length - 1;
            }
            else
            {
                // 托底：使用平分逻辑
                var charHeight = (float)cellRect.Height / cell.words.Length;
                return Math.Max(0, Math.Min((int)(relativeY / charHeight), cell.words.Length - 1));
            }
        }

        /// <summary>
        /// 根据鼠标位置更新光标
        /// </summary>
        private void UpdateCursor(Point mouseLocation)
        {
            if (textCells.Count == 0)
            {
                Cursor = Cursors.Default;
                return;
            }

            // 转换鼠标坐标到图片坐标
            var imagePoint = GetImagePoint(mouseLocation);

            // 检查是否在任何文字区域内
            bool isOverText = textCells.Any(cell =>
                cell?.location != null &&
                !string.IsNullOrEmpty(cell.words) &&
                cell.location.Rectangle.Contains(imagePoint));

            // 如果鼠标在文字区域，显示文本光标
            Cursor = isOverText ? Cursors.IBeam : Cursors.Default;
        }

        private void OnMouseUp(object sender, MouseEventArgs e)
        {
            if (isDragging)
            {
                isDragging = false;

                // 如果没有有效选择，清除选择
                if (startCell == null || endCell == null ||
                    startCharIndex < 0 || endCharIndex < 0)
                {
                    ClearSelection();
                }

                // 触发选择变更事件
                OnTextSelectionChanged();

                Invalidate();
            }
        }

        /// <summary>
        /// 判断指定点是否在空白区域（没有精确匹配的Cell）
        /// </summary>
        private bool IsPointInEmptyArea(Point imagePoint)
        {
            foreach (var cell in textCells)
            {
                if (cell?.location != null && !string.IsNullOrEmpty(cell.words))
                {
                    var cellRect = cell.location.Rectangle;
                    if (cellRect.Contains(imagePoint))
                    {
                        return false; // 有精确匹配，不是空白区域
                    }
                }
            }
            return true; // 没有精确匹配，是空白区域
        }

        private void OnCustomPaint(object sender, PaintEventArgs e)
        {
            if (!IsBindImageMode || textCells.Count == 0) return;

            var g = e.Graphics;

            // 绘制选中的文字区域
            if (isDragging || HasSelection())
            {
                using (var selectedBrush = new SolidBrush(SELECTION_FILL_COLOR))
                {
                    var selectedRects = GetSelectedCharacterRectangles();

                    foreach (var rect in selectedRects)
                    {
                        var displayRect = GetDisplayRectangle(rect);

                        if (!displayRect.IsEmpty)
                        {
                            g.FillRectangle(selectedBrush, displayRect);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 获取鼠标位置对应的图片坐标
        /// </summary>
        private Point GetImagePoint(Point controlPoint)
        {
            // 考虑滚动位置和缩放因子
            var scrollOffset = AutoScrollPosition;
            var imagePoint = new Point(
                (int)((controlPoint.X - scrollOffset.X) / ZoomFactor),
                (int)((controlPoint.Y - scrollOffset.Y) / ZoomFactor)
            );
            return imagePoint;
        }

        /// <summary>
        /// 获取矩形在控件中的显示矩形
        /// </summary>
        private Rectangle GetDisplayRectangle(Rectangle imageRect)
        {
            var scrollOffset = AutoScrollPosition;

            return new Rectangle(
                (int)(imageRect.X * ZoomFactor) + scrollOffset.X,
                (int)(imageRect.Y * ZoomFactor) + scrollOffset.Y,
                (int)(imageRect.Width * ZoomFactor),
                (int)(imageRect.Height * ZoomFactor)
            );
        }

        /// <summary>
        /// 检查是否有选择
        /// </summary>
        private bool HasSelection()
        {
            return startCell != null && endCell != null &&
                   startCharIndex >= 0 && endCharIndex >= 0;
        }

        /// <summary>
        /// 获取选中字符的矩形区域
        /// </summary>
        private List<Rectangle> GetSelectedCharacterRectangles()
        {
            var rects = new List<Rectangle>();

            // 如果正在拖拽或有有效选择，使用三部分选择逻辑
            if (isDragging || HasSelection())
            {
                var selectedCells = GetThreePartSelection(); // 使用新的三部分选择逻辑

                foreach (var cellInfo in selectedCells)
                {
                    var cell = cellInfo.Cell;
                    var startChar = cellInfo.StartChar;
                    var endChar = cellInfo.EndChar;

                    var rect = GetCharacterRectInCell(cell, startChar, endChar);
                    if (!rect.IsEmpty)
                        rects.Add(rect);
                }
            }

            return rects;
        }

        /// <summary>
        /// 获取cell内指定字符范围的矩形（基于实际字体测量）
        /// </summary>
        private Rectangle GetCharacterRectInCell(TextCellInfo cell, int startChar, int endChar)
        {
            if (cell?.location == null || string.IsNullOrEmpty(cell.words) ||
                startChar < 0 || endChar < 0 || startChar >= cell.words.Length)
                return Rectangle.Empty;

            var cellRect = cell.location.Rectangle;
            bool isCellVertical = DetectCellDirection(cell);

            // 确保索引有效
            startChar = Math.Max(0, Math.Min(startChar, cell.words.Length - 1));
            endChar = Math.Max(0, Math.Min(endChar, cell.words.Length - 1));

            if (startChar > endChar)
            {
                var temp = startChar;
                startChar = endChar;
                endChar = temp;
            }

            if (isCellVertical)
            {
                return GetVerticalCharacterRect(cell, cellRect, startChar, endChar);
            }
            else
            {
                return GetHorizontalCharacterRect(cell, cellRect, startChar, endChar);
            }
        }

        /// <summary>
        /// 获取横排文字的字符矩形（基于智能字体测量+托底平分逻辑）
        /// </summary>
        private Rectangle GetHorizontalCharacterRect(TextCellInfo cell, Rectangle cellRect, int startChar, int endChar)
        {
            // 使用与字符索引计算相同的智能测量逻辑
            var measurementResult = MeasureTextWithCalibration(cell.words, cellRect);

            if (measurementResult.Success)
            {
                var charWidths = measurementResult.CharWidths;
                float totalMeasuredWidth = measurementResult.TotalWidth;

                // 使用字体测量结果
                float scale = cellRect.Width / totalMeasuredWidth;

                // 计算起始位置
                float startX = 0;
                for (int i = 0; i < startChar; i++)
                {
                    startX += charWidths[i] * scale;
                }

                // 计算选中区域宽度
                float selectionWidth = 0;
                for (int i = startChar; i <= endChar; i++)
                {
                    selectionWidth += charWidths[i] * scale;
                }

                return new Rectangle(
                    (int)(cellRect.X + startX),
                    cellRect.Y,
                    (int)Math.Ceiling(selectionWidth),
                    cellRect.Height
                );
            }
            else
            {
                // 托底：使用平分逻辑
                var charWidth = (float)cellRect.Width / cell.words.Length;

                return new Rectangle(
                    (int)(cellRect.X + startChar * charWidth),
                    cellRect.Y,
                    (int)Math.Ceiling((endChar - startChar + 1) * charWidth),
                    cellRect.Height
                );
            }
        }

        /// <summary>
        /// 获取竖排文字的字符矩形（基于实际字体测量+托底平分逻辑）
        /// </summary>
        private Rectangle GetVerticalCharacterRect(TextCellInfo cell, Rectangle cellRect, int startChar, int endChar)
        {
            // 尝试基于实际字体测量（与字符索引计算使用相同逻辑）
            float fontSize = EstimateFontSize(cellRect);
            var charHeights = new List<float>();
            float totalMeasuredHeight = 0;
            bool measurementSucceeded = true;

            foreach (char c in cell.words)
            {
                float charHeight = MeasureCharHeight(c, fontSize);
                if (charHeight <= 0) // 测量失败
                {
                    measurementSucceeded = false;
                    break;
                }
                charHeights.Add(charHeight);
                totalMeasuredHeight += charHeight;
            }

            if (measurementSucceeded && totalMeasuredHeight > 0)
            {
                // 使用字体测量结果
                float scale = cellRect.Height / totalMeasuredHeight;

                // 计算起始位置
                float startY = 0;
                for (int i = 0; i < startChar; i++)
                {
                    startY += charHeights[i] * scale;
                }

                // 计算选中区域高度
                float selectionHeight = 0;
                for (int i = startChar; i <= endChar; i++)
                {
                    selectionHeight += charHeights[i] * scale;
                }

                return new Rectangle(
                    cellRect.X,
                    (int)(cellRect.Y + startY),
                    cellRect.Width,
                    (int)Math.Ceiling(selectionHeight)
                );
            }
            else
            {
                // 托底：使用平分逻辑
                var charHeight = (float)cellRect.Height / cell.words.Length;

                return new Rectangle(
                    cellRect.X,
                    (int)(cellRect.Y + startChar * charHeight),
                    cellRect.Width,
                    (int)Math.Ceiling((endChar - startChar + 1) * charHeight)
                );
            }
        }

        /// <summary>
        /// 获取选择区域（与拖拽方向无关的文档式选择）
        /// </summary>
        private List<CellSelectionInfo> GetThreePartSelection()
        {
            // 如果没有起点和终点cell，返回空列表
            if (startCell == null || endCell == null || startCharIndex < 0 || endCharIndex < 0)
                return new List<CellSelectionInfo>();

            // 空白区域单击判断：如果没有鼠标移动且起点在空白区域，不返回任何选择
            bool isNoMouseMove = (dragStartPoint.X == dragEndPoint.X && dragStartPoint.Y == dragEndPoint.Y);

            if (isNoMouseMove && IsPointInEmptyArea(dragStartPoint))
            {
                // 空白区域单击：不返回任何选择，避免绘制高亮
                return new List<CellSelectionInfo>();
            }

            // 特殊处理：如果起点和终点在同一个cell，直接返回该cell的字符选择
            if (startCell == endCell)
            {
                return GetSingleCellSelection();
            }

            // 核心改进：基于逻辑位置而非拖拽方向进行选择
            return GetLogicalSelection();
        }

        /// <summary>
        /// 基于逻辑位置的选择（与拖拽方向无关，考虑空间连续性）
        /// </summary>
        private List<CellSelectionInfo> GetLogicalSelection()
        {
            // 使用空间路径选择，而不是简单的线性选择
            var selectedCells = GetSpatialPathSelection();

            // 计算字符选择范围
            return CalculateCharacterRangesLogical(selectedCells);
        }

        /// <summary>
        /// 基于空间路径的选择（考虑表格布局的连续性）
        /// </summary>
        private List<TextCellInfo> GetSpatialPathSelection()
        {
            var result = new List<TextCellInfo>();

            // 确定逻辑起点和终点（按阅读顺序）
            var logicalStart = IsTextBefore(startCell, endCell) ? startCell : endCell;
            var logicalEnd = IsTextBefore(startCell, endCell) ? endCell : startCell;

            // 获取起点到终点的空间路径
            var pathCells = GetCellsInSpatialPath(logicalStart, logicalEnd);

            return pathCells;
        }

        /// <summary>
        /// 获取从起点到终点的空间路径上的cells
        /// </summary>
        private List<TextCellInfo> GetCellsInSpatialPath(TextCellInfo logicalStart, TextCellInfo logicalEnd)
        {
            var result = new List<TextCellInfo>();

            // 计算选择区域的边界
            var startRect = logicalStart.location.Rectangle;
            var endRect = logicalEnd.location.Rectangle;

            var selectionBounds = new Rectangle(
                Math.Min(startRect.Left, endRect.Left),
                Math.Min(startRect.Top, endRect.Top),
                Math.Max(startRect.Right, endRect.Right) - Math.Min(startRect.Left, endRect.Left),
                Math.Max(startRect.Bottom, endRect.Bottom) - Math.Min(startRect.Top, endRect.Top)
            );

            // 找到在选择路径上的cells（移除边界预筛选，让所有cells参与路径判断）
            var candidateCells = textCells.Where(cell =>
                cell?.location != null &&
                !string.IsNullOrEmpty(cell.words)
            ).ToList();

            // 按阅读顺序排序候选cells
            var sortedCandidates = SortCellsByReadingOrder(candidateCells);

            // 找到起点和终点在候选列表中的位置
            int startIndex = sortedCandidates.IndexOf(logicalStart);
            int endIndex = sortedCandidates.IndexOf(logicalEnd);

            // 不使用简单的索引范围，而是检查所有候选cells的空间位置关系
            foreach (var cell in candidateCells)
            {
                if (IsCellInSelectionPath(cell, logicalStart, logicalEnd))
                {
                    result.Add(cell);
                }
            }

            return result;
        }

        /// <summary>
        /// 判断cell是否在选择路径上（支持横排和竖排）
        /// </summary>
        private bool IsCellInSelectionPath(TextCellInfo cell, TextCellInfo logicalStart, TextCellInfo logicalEnd)
        {
            // 如果是起点或终点，直接包含
            if (cell == logicalStart || cell == logicalEnd)
                return true;

            // 检测文字方向
            bool isStartVertical = DetectCellDirection(logicalStart);
            bool isEndVertical = DetectCellDirection(logicalEnd);
            bool isCellVertical = DetectCellDirection(cell);

            // 如果起点和终点的方向不一致，或者当前cell的方向与它们不一致，则不包含
            if (isStartVertical != isEndVertical || isCellVertical != isStartVertical)
                return false;

            if (isCellVertical)
            {
                // 竖排文字：使用列逻辑
                return IsCellInVerticalSelectionPath(cell, logicalStart, logicalEnd);
            }
            else
            {
                // 横排文字：使用行逻辑
                return IsCellInHorizontalSelectionPath(cell, logicalStart, logicalEnd);
            }
        }

        /// <summary>
        /// 判断cell是否在竖排文字的选择路径上
        /// </summary>
        private bool IsCellInVerticalSelectionPath(TextCellInfo cell, TextCellInfo logicalStart, TextCellInfo logicalEnd)
        {
            var cellRect = cell.location.Rectangle;
            var startRect = logicalStart.location.Rectangle;
            var endRect = logicalEnd.location.Rectangle;

            // 竖排文字的选择逻辑：基于X坐标（列）
            // 起点在右，终点在左（竖排阅读顺序：从右到左）

            // 检查cell是否在起点和终点形成的X范围内
            var minX = Math.Min(startRect.Left, endRect.Left);
            var maxX = Math.Max(startRect.Right, endRect.Right);

            // cell必须在X范围内
            if (cellRect.Right < minX || cellRect.Left > maxX)
                return false;

            // 对于中间列的cells，需要更精确的判断
            var startColLeft = startRect.Left;
            var startColRight = startRect.Right;
            var endColLeft = endRect.Left;
            var endColRight = endRect.Right;

            // 简化的列判断逻辑：基于X坐标范围重叠
            const int colTolerance = 5; // 列容差

            // 计算起点列和终点列的X范围
            int startColMinX = startColLeft - colTolerance;
            int startColMaxX = startColRight + colTolerance;
            int endColMinX = endColLeft - colTolerance;
            int endColMaxX = endColRight + colTolerance;

            // 判断cell是否与各列有重叠
            bool isInStartCol = !(cellRect.Right < startColMinX || cellRect.Left > startColMaxX);
            bool isInEndCol = !(cellRect.Right < endColMinX || cellRect.Left > endColMaxX);

            // 中间列：在起点和终点之间，且不与起点列或终点列重叠
            int selectionMinX = Math.Min(startColLeft, endColLeft);
            int selectionMaxX = Math.Max(startColRight, endColRight);
            bool isInMiddleCol = !isInStartCol && !isInEndCol &&
                               cellRect.Left >= selectionMinX &&
                               cellRect.Right <= selectionMaxX;

            if (isInStartCol)
            {
                // 起点列：基于竖排框选逻辑，从起点开始到列底
                return IsInStartColPath(cell, cellRect, startRect, logicalStart);
            }
            else if (isInEndCol)
            {
                // 终点列：基于竖排框选逻辑，从列顶到终点
                return IsInEndColPath(cell, cellRect, endRect, logicalEnd);
            }
            else if (isInMiddleCol)
            {
                // 中间列：包含所有cells
                return true;
            }

            return false;
        }

        /// <summary>
        /// 判断cell是否在横排文字的选择路径上
        /// </summary>
        private bool IsCellInHorizontalSelectionPath(TextCellInfo cell, TextCellInfo logicalStart, TextCellInfo logicalEnd)
        {
            var cellRect = cell.location.Rectangle;
            var startRect = logicalStart.location.Rectangle;
            var endRect = logicalEnd.location.Rectangle;

            // 检查cell是否在起点和终点形成的"路径"上
            var minY = Math.Min(startRect.Top, endRect.Top);
            var maxY = Math.Max(startRect.Bottom, endRect.Bottom);

            // cell必须在Y范围内
            if (cellRect.Bottom < minY || cellRect.Top > maxY)
                return false;

            // 对于中间行的cells，需要更精确的判断
            var startRowTop = startRect.Top;
            var startRowBottom = startRect.Bottom;
            var endRowTop = endRect.Top;
            var endRowBottom = endRect.Bottom;

            // 简化的行判断逻辑：基于Y坐标范围重叠
            int rowTolerance = CalculateRowTolerance(startRect);

            // 计算起点行和终点行的Y范围
            int startRowMinY = startRowTop - rowTolerance;
            int startRowMaxY = startRowBottom + rowTolerance;
            int endRowMinY = endRowTop - rowTolerance;
            int endRowMaxY = endRowBottom + rowTolerance;

            // 判断cell是否与各行有重叠 - 使用更严格的重叠判断
            bool isInStartRow = HasSignificantOverlap(cellRect, startRowMinY, startRowMaxY);
            bool isInEndRow = HasSignificantOverlap(cellRect, endRowMinY, endRowMaxY);

            // 中间行：在起点和终点之间，且不与起点行或终点行重叠
            int selectionMinY = Math.Min(startRowTop, endRowTop);
            int selectionMaxY = Math.Max(startRowBottom, endRowBottom);
            bool isInMiddleRow = !isInStartRow && !isInEndRow &&
                               cellRect.Top >= selectionMinY &&
                               cellRect.Bottom <= selectionMaxY;

            if (isInStartRow)
            {
                // 起点行：基于文档框选逻辑，从起点开始到行尾
                return IsInStartRowPath(cell, cellRect, startRect, logicalStart);
            }
            else if (isInEndRow)
            {
                // 终点行：基于文档框选逻辑，从行首到终点
                return IsInEndRowPath(cell, cellRect, endRect, logicalEnd);
            }
            else if (isInMiddleRow)
            {
                // 中间行：包含所有cells
                return true;
            }

            return false;
        }

        /// <summary>
        /// 判断cell与指定Y范围是否有显著重叠
        /// </summary>
        private bool HasSignificantOverlap(Rectangle cellRect, int rangeMinY, int rangeMaxY)
        {
            // 计算重叠区域
            int overlapTop = Math.Max(cellRect.Top, rangeMinY);
            int overlapBottom = Math.Min(cellRect.Bottom, rangeMaxY);
            int overlapHeight = Math.Max(0, overlapBottom - overlapTop);

            // 重叠高度必须超过配置阈值才算显著重叠
            double overlapRatio = (double)overlapHeight / cellRect.Height;
            return overlapRatio > config.OverlapThreshold;
        }

        /// <summary>
        /// 判断cell是否在起点列的选择路径上（竖排）
        /// </summary>
        private bool IsInStartColPath(TextCellInfo cell, Rectangle cellRect, Rectangle startRect, TextCellInfo logicalStart)
        {
            const int rowTolerance = 5; // 行容差

            // 如果是起点cell本身，直接包含
            if (cell == logicalStart)
                return true;

            // 起点列：从起点开始到列底的所有cells
            // 竖排文字中，"列底"是指Y坐标更大的位置
            return cellRect.Top >= (startRect.Top - rowTolerance);
        }

        /// <summary>
        /// 判断cell是否在终点列的选择路径上（竖排）
        /// </summary>
        private bool IsInEndColPath(TextCellInfo cell, Rectangle cellRect, Rectangle endRect, TextCellInfo logicalEnd)
        {
            const int rowTolerance = 5; // 行容差

            // 如果是终点cell本身，直接包含
            if (cell == logicalEnd)
                return true;

            // 终点列：从列顶到终点的所有cells
            // 竖排文字中，"列顶"是指Y坐标更小的位置
            return cellRect.Bottom <= (endRect.Bottom + rowTolerance);
        }

        /// <summary>
        /// 判断cell是否在起点行的选择路径上
        /// </summary>
        private bool IsInStartRowPath(TextCellInfo cell, Rectangle cellRect, Rectangle startRect, TextCellInfo logicalStart)
        {
            const int colTolerance = 5; // 列容差

            // 如果是起点cell本身，直接包含
            if (cell == logicalStart)
                return true;

            // 起点行：从起点开始到行尾的所有cells
            // 使用容差判断，避免像素级精确匹配的问题
            return cellRect.Left >= (startRect.Left - colTolerance);
        }

        /// <summary>
        /// 判断cell是否在终点行的选择路径上
        /// </summary>
        private bool IsInEndRowPath(TextCellInfo cell, Rectangle cellRect, Rectangle endRect, TextCellInfo logicalEnd)
        {
            const int colTolerance = 5; // 列容差

            // 如果是终点cell本身，直接包含
            if (cell == logicalEnd)
                return true;

            // 终点行：从行首到终点的所有cells
            // 使用容差判断，避免像素级精确匹配的问题
            return cellRect.Right <= (endRect.Right + colTolerance);
        }

        /// <summary>
        /// 获取单个cell内的字符选择
        /// </summary>
        private List<CellSelectionInfo> GetSingleCellSelection()
        {
            if (startCell == null || startCharIndex < 0 || endCharIndex < 0)
                return new List<CellSelectionInfo>();

            // 确保字符索引在有效范围内
            int validStartChar = Math.Max(0, Math.Min(startCharIndex, startCell.words.Length - 1));
            int validEndChar = Math.Max(0, Math.Min(endCharIndex, startCell.words.Length - 1));

            // 确保起点索引 <= 终点索引（处理反向选择）
            int cellStartChar = Math.Min(validStartChar, validEndChar);
            int cellEndChar = Math.Max(validStartChar, validEndChar);

            return new List<CellSelectionInfo>
            {
                new CellSelectionInfo(startCell, cellStartChar, cellEndChar)
            };
        }

        /// <summary>
        /// 基于逻辑位置计算字符选择范围
        /// </summary>
        private List<CellSelectionInfo> CalculateCharacterRangesLogical(List<TextCellInfo> selectedCells)
        {
            var result = new List<CellSelectionInfo>();

            if (selectedCells.Count == 0) return result;

            // 确定逻辑起点和终点（基于阅读顺序，与拖拽方向无关）
            var logicalStartCell = IsTextBefore(startCell, endCell) ? startCell : endCell;
            var logicalEndCell = IsTextBefore(startCell, endCell) ? endCell : startCell;

            // 确定逻辑起点和终点的字符索引
            int logicalStartCharIndex, logicalEndCharIndex;

            // 逻辑起点的字符索引
            if (logicalStartCell == startCell)
            {
                logicalStartCharIndex = startCharIndex;
            }
            else if (logicalStartCell == endCell)
            {
                logicalStartCharIndex = endCharIndex;
            }
            else
            {
                logicalStartCharIndex = 0; // 完整选择
            }

            // 逻辑终点的字符索引
            if (logicalEndCell == startCell)
            {
                logicalEndCharIndex = startCharIndex;
            }
            else if (logicalEndCell == endCell)
            {
                logicalEndCharIndex = endCharIndex;
            }
            else
            {
                logicalEndCharIndex = logicalEndCell.words.Length - 1; // 完整选择
            }

            // 为每个cell计算字符范围
            for (int i = 0; i < selectedCells.Count; i++)
            {
                var cell = selectedCells[i];
                int cellStartChar, cellEndChar;

                if (selectedCells.Count == 1) // 只有一个cell（不应该到这里，但保险起见）
                {
                    cellStartChar = Math.Min(logicalStartCharIndex, logicalEndCharIndex);
                    cellEndChar = Math.Max(logicalStartCharIndex, logicalEndCharIndex);
                }
                else if (cell == logicalStartCell) // 根据实际身份判断：逻辑起点cell
                {
                    cellStartChar = logicalStartCharIndex;
                    cellEndChar = cell.words.Length - 1;

                }
                else if (cell == logicalEndCell) // 根据实际身份判断：逻辑终点cell
                {
                    cellStartChar = 0;
                    cellEndChar = logicalEndCharIndex;

                }
                else // 中间cells
                {
                    cellStartChar = 0;
                    cellEndChar = cell.words.Length - 1;

                }

                result.Add(new CellSelectionInfo(cell, cellStartChar, cellEndChar));
            }

            return result;
        }

        /// <summary>
        /// 判断cell1是否在文本阅读顺序上早于cell2
        /// </summary>
        private bool IsTextBefore(TextCellInfo cell1, TextCellInfo cell2)
        {
            bool isVertical1 = DetectCellDirection(cell1);
            bool isVertical2 = DetectCellDirection(cell2);

            if (isVertical1 && isVertical2)
            {
                // 都是竖排：右到左，上到下
                if (cell1.location.left > cell2.location.left) return true;
                if (cell1.location.left < cell2.location.left) return false;
                return cell1.location.top < cell2.location.top;
            }
            else if (!isVertical1 && !isVertical2)
            {
                // 都是横排：上到下，左到右
                if (cell1.location.top < cell2.location.top) return true;
                if (cell1.location.top > cell2.location.top) return false;
                return cell1.location.left < cell2.location.left;
            }
            else
            {
                // 混合情况，按位置判断
                return cell1.location.top < cell2.location.top;
            }
        }

        /// <summary>
        /// 按阅读顺序排序cells
        /// </summary>
        private List<TextCellInfo> SortCellsByReadingOrder(List<TextCellInfo> cells)
        {
            if (cells.Count <= 1) return cells.ToList();

            // 检测主要文本方向
            bool isMainlyVertical = cells.Count(DetectCellDirection) > cells.Count / 2;

            if (isMainlyVertical)
            {
                // 竖排：先按X坐标降序（从右到左），再按Y坐标升序（从上到下）
                return cells.OrderByDescending(c => c.location.left)
                           .ThenBy(c => c.location.top)
                           .ToList();
            }
            else
            {
                // 横排：先按Y坐标升序（从上到下），再按X坐标升序（从左到右）
                return cells.OrderBy(c => c.location.top)
                           .ThenBy(c => c.location.left)
                           .ToList();
            }
        }

        /// <summary>
        /// 获取选中的文字内容（按照阅读习惯排序）
        /// </summary>
        private string GetSelectedText()
        {
            if (!HasSelection()) return string.Empty;

            var selectedCells = GetThreePartSelection();
            if (selectedCells == null || selectedCells.Count == 0)
                return string.Empty;

            // 检测主要的文字方向
            var textDirection = DetectTextDirection(selectedCells);

            // 根据文字方向进行排序和拼接
            if (textDirection == TextDirection.Vertical)
            {
                return GetVerticalText(selectedCells);
            }
            else
            {
                return GetHorizontalText(selectedCells);
            }
        }

        /// <summary>
        /// 文字方向枚举
        /// </summary>
        private enum TextDirection
        {
            Horizontal, // 横排
            Vertical    // 竖排
        }

        /// <summary>
        /// 检测文字的主要方向
        /// </summary>
        private TextDirection DetectTextDirection(List<CellSelectionInfo> selectedCells)
        {
            if (selectedCells.Count <= 1)
                return TextDirection.Horizontal; // 默认横排

            int verticalCount = 0;
            int horizontalCount = 0;

            foreach (var cellInfo in selectedCells)
            {
                if (DetectCellDirection(cellInfo.Cell))
                    verticalCount++;
                else
                    horizontalCount++;
            }

            // 如果竖排cell数量占多数，认为是竖排文本
            return verticalCount > horizontalCount ? TextDirection.Vertical : TextDirection.Horizontal;
        }

        /// <summary>
        /// 获取横排文本（从左到右，从上到下）
        /// </summary>
        private string GetHorizontalText(List<CellSelectionInfo> selectedCells)
        {
            var result = new StringBuilder();

            // 按行分组：先按Y坐标分组，然后每组内按X坐标排序
            var lines = GroupCellsByLines(selectedCells);

            for (int lineIndex = 0; lineIndex < lines.Count; lineIndex++)
            {
                var line = lines[lineIndex];

                // 行内按X坐标（从左到右）排序
                var sortedCellsInLine = line.OrderBy(c => c.Cell.location.left).ToList();

                for (int cellIndex = 0; cellIndex < sortedCellsInLine.Count; cellIndex++)
                {
                    var cellInfo = sortedCellsInLine[cellIndex];
                    var cell = cellInfo.Cell;

                    // 添加cell内的文字
                    if (cellInfo.StartChar >= 0 && cellInfo.EndChar < cell.words.Length)
                    {
                        result.Append(cell.words.Substring(cellInfo.StartChar, cellInfo.EndChar - cellInfo.StartChar + 1));
                    }

                    // 添加cell间的分隔符
                    if (cellIndex < sortedCellsInLine.Count - 1)
                    {
                        var nextCell = sortedCellsInLine[cellIndex + 1];
                        if (ShouldAddSpaceBetweenCells(cellInfo.Cell, nextCell.Cell))
                        {
                            result.Append(" ");
                        }
                    }
                }

                // 添加行间换行符
                if (lineIndex < lines.Count - 1)
                {
                    result.AppendLine();
                }
            }

            return result.ToString();
        }

        /// <summary>
        /// 按行分组cells
        /// </summary>
        private List<List<CellSelectionInfo>> GroupCellsByLines(List<CellSelectionInfo> selectedCells)
        {
            var lines = new List<List<CellSelectionInfo>>();

            // 按Y坐标排序
            var sortedCells = selectedCells.OrderBy(c => c.Cell.location.top).ToList();

            foreach (var cellInfo in sortedCells)
            {
                bool addedToExistingLine = false;

                // 尝试添加到现有行
                foreach (var line in lines)
                {
                    if (IsInSameLine(cellInfo.Cell, line[0].Cell))
                    {
                        line.Add(cellInfo);
                        addedToExistingLine = true;
                        break;
                    }
                }

                // 如果没有找到合适的行，创建新行
                if (!addedToExistingLine)
                {
                    lines.Add(new List<CellSelectionInfo> { cellInfo });
                }
            }

            return lines;
        }

        /// <summary>
        /// 判断两个cell是否在同一行
        /// </summary>
        private bool IsInSameLine(TextCellInfo cell1, TextCellInfo cell2)
        {
            var cell1CenterY = cell1.location.top + cell1.location.height / 2;
            var cell2CenterY = cell2.location.top + cell2.location.height / 2;
            var maxHeight = Math.Max(cell1.location.height, cell2.location.height);

            // 如果两个cell的中心Y坐标差距小于最大高度的一半，认为在同一行
            return Math.Abs(cell1CenterY - cell2CenterY) < maxHeight * 0.5;
        }

        /// <summary>
        /// 获取竖排文本（从右到左，从上到下）
        /// </summary>
        private string GetVerticalText(List<CellSelectionInfo> selectedCells)
        {
            var result = new StringBuilder();

            // 按列分组：先按X坐标分组，然后每组内按Y坐标排序
            var columns = GroupCellsByColumns(selectedCells);

            for (int columnIndex = 0; columnIndex < columns.Count; columnIndex++)
            {
                var column = columns[columnIndex];

                // 列内按Y坐标（从上到下）排序
                var sortedCellsInColumn = column.OrderBy(c => c.Cell.location.top).ToList();

                for (int cellIndex = 0; cellIndex < sortedCellsInColumn.Count; cellIndex++)
                {
                    var cellInfo = sortedCellsInColumn[cellIndex];
                    var cell = cellInfo.Cell;

                    // 添加cell内的文字
                    if (cellInfo.StartChar >= 0 && cellInfo.EndChar < cell.words.Length)
                    {
                        result.Append(cell.words.Substring(cellInfo.StartChar, cellInfo.EndChar - cellInfo.StartChar + 1));
                    }

                    // 竖排文本中，同一列的cell之间通常不需要分隔符
                    // 除非有明显的间距
                    if (cellIndex < sortedCellsInColumn.Count - 1)
                    {
                        var nextCell = sortedCellsInColumn[cellIndex + 1];
                        if (ShouldAddSpaceBetweenVerticalCells(cellInfo.Cell, nextCell.Cell))
                        {
                            result.Append(" ");
                        }
                    }
                }

                // 添加列间分隔符（竖排文本的列间通常用空格或换行）
                if (columnIndex < columns.Count - 1)
                {
                    result.Append(" "); // 或者使用 result.AppendLine(); 根据需要调整
                }
            }

            return result.ToString();
        }

        /// <summary>
        /// 按列分组cells（竖排文本）
        /// </summary>
        private List<List<CellSelectionInfo>> GroupCellsByColumns(List<CellSelectionInfo> selectedCells)
        {
            var columns = new List<List<CellSelectionInfo>>();

            // 按X坐标排序（从右到左，所以使用降序）
            var sortedCells = selectedCells.OrderByDescending(c => c.Cell.location.left).ToList();

            foreach (var cellInfo in sortedCells)
            {
                bool addedToExistingColumn = false;

                // 尝试添加到现有列
                foreach (var column in columns)
                {
                    if (IsInSameColumn(cellInfo.Cell, column[0].Cell))
                    {
                        column.Add(cellInfo);
                        addedToExistingColumn = true;
                        break;
                    }
                }

                // 如果没有找到合适的列，创建新列
                if (!addedToExistingColumn)
                {
                    columns.Add(new List<CellSelectionInfo> { cellInfo });
                }
            }

            return columns;
        }

        /// <summary>
        /// 判断两个cell是否在同一列（竖排文本）
        /// </summary>
        private bool IsInSameColumn(TextCellInfo cell1, TextCellInfo cell2)
        {
            var cell1CenterX = cell1.location.left + cell1.location.width / 2;
            var cell2CenterX = cell2.location.left + cell2.location.width / 2;
            var maxWidth = Math.Max(cell1.location.width, cell2.location.width);

            // 如果两个cell的中心X坐标差距小于最大宽度的一半，认为在同一列
            return Math.Abs(cell1CenterX - cell2CenterX) < maxWidth * 0.5;
        }

        /// <summary>
        /// 判断两个cell之间是否应该添加空格（横排文本）
        /// </summary>
        private bool ShouldAddSpaceBetweenCells(TextCellInfo currentCell, TextCellInfo nextCell)
        {
            // 如果两个cell在同一行但有较大间距，添加空格
            var currentRight = currentCell.location.left + currentCell.location.width;
            var nextLeft = nextCell.location.left;
            var gap = nextLeft - currentRight;
            var avgCharWidth = currentCell.location.width / Math.Max(1, currentCell.words.Length);

            return gap > avgCharWidth * 1.5; // 稍微降低阈值，更容易添加空格
        }

        /// <summary>
        /// 判断两个cell之间是否应该添加空格（竖排文本）
        /// </summary>
        private bool ShouldAddSpaceBetweenVerticalCells(TextCellInfo currentCell, TextCellInfo nextCell)
        {
            // 竖排文本中，如果两个cell在垂直方向上有较大间距，添加空格
            var currentBottom = currentCell.location.top + currentCell.location.height;
            var nextTop = nextCell.location.top;
            var gap = nextTop - currentBottom;
            var avgCharHeight = currentCell.location.height / Math.Max(1, currentCell.words.Length);

            return gap > avgCharHeight * 1.5;
        }

        /// <summary>
        /// 触发文字选择变化事件
        /// </summary>
        private void OnTextSelectionChanged()
        {
            var selectedText = GetSelectedText();

            // 获取选中的cells（为了兼容现有接口）
            var selectedCells = new List<TextCellInfo>();
            if (HasSelection())
            {
                var cellSelections = GetThreePartSelection();
                selectedCells.AddRange(cellSelections.Select(c => c.Cell));
            }

            TextSelectionChanged?.Invoke(this, new TextSelectionEventArgs(selectedCells)
            {
                SelectedText = selectedText,
                SelectedCharacterCount = selectedText.Length
            });
        }

        /// <summary>
        /// 清除所有选择
        /// </summary>
        public void ClearSelection()
        {
            startCell = null;
            endCell = null;
            startCharIndex = -1;
            endCharIndex = -1;
            dragStartPoint = Point.Empty;
            dragEndPoint = Point.Empty;

            OnTextSelectionChanged();
            Invalidate();
        }

        /// <summary>
        /// 获取当前选中的文字
        /// </summary>
        public string GetSelectedTextContent()
        {
            return GetSelectedText();
        }
    }

    /// <summary>
    /// 文字选择事件参数
    /// </summary>
    public class TextSelectionEventArgs : EventArgs
    {
        public List<TextCellInfo> SelectedCells { get; private set; }
        public string SelectedText { get; set; }
        public int SelectedCharacterCount { get; set; }

        public TextSelectionEventArgs(List<TextCellInfo> selectedCells)
        {
            SelectedCells = selectedCells ?? new List<TextCellInfo>();
            SelectedText = string.Empty;
            SelectedCharacterCount = 0;
        }
    }
}
