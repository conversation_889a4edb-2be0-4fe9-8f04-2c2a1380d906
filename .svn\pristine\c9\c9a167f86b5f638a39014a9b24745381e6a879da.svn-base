﻿using System.IO;

namespace OCRTools.Common.ImageLib
{
    /// <summary>
    ///     腾讯AI体验中心小程序
    /// </summary>
    public class QQImageUpload : BaseImageUpload
    {
        private const string STR_FILE_NAME_SPILT = "\"image_url\": \"";
        private const string STR_FILE_NAME_SPILT1 = "\"image_url\":\"";

        public QQImageUpload()
        {
            ImageType = ImageTypeEnum.QQAI;
        }

        public override string GetResult(byte[] content, string ext)
        {
            var url = "https://ai.qq.com/cgi-bin/image_upload";
            var file = new UploadFileInfo
            {
                Name = "image_file",
                Filename = "test.png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            var html = UploadFileRequest.Post(url, new[] {file}, null);
            var result = CommonMethod.SubString(html, STR_FILE_NAME_SPILT, "\"").Replace("\\/", "/");
            if (string.IsNullOrEmpty(result))
                result = CommonMethod.SubString(html, STR_FILE_NAME_SPILT1, "\"").Replace("\\/", "/");
            return result;
        }
    }
}