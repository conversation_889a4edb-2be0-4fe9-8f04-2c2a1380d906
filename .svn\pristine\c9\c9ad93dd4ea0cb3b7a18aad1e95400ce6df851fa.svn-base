﻿using OCRTools.Language;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools.UserControlEx
{
    public class CheckBoxWithTip : CheckBox
    {
        public CheckBoxWithTip()
        {
            TextChanged += CheckBoxWithTip_TextChanged;
        }

        private void CheckBoxWithTip_TextChanged(object sender, EventArgs e)
        {
            InitPicBox();
        }

        private string tipText;

        public string TipText { get => tipText; set { tipText = value; InitPicBox(); } }

        public ToolTip TipControl { get; set; }
        public Bitmap TipIcon { get; set; }

        private PictureBox pictureBox;

        public bool LimitMaxWidth { get; set; } = true;

        private void InitPicBox()
        {
            if (string.IsNullOrEmpty(tipText))
            {
                return;
            }
            AutoSize = true;
            if (pictureBox == null)
            {
                pictureBox = new PictureBox
                {
                    BackColor = SystemColors.ButtonHighlight,
                    Image = TipIcon ?? Properties.Resources.帮助,
                    SizeMode = PictureBoxSizeMode.AutoSize,
                    TabStop = false,
                };
                pictureBox.Size = pictureBox.Image.Height > 20 ? new Size(25, 23) : pictureBox.Image.Size;
                pictureBox.Top = (Height + Padding.Top - pictureBox.Height) - 1;
                Controls.Add(pictureBox);
            }
            pictureBox.Left = Width - (pictureBox.Width - pictureBox.Height) * 2;
            AutoSize = false;
            Width = pictureBox.Left + pictureBox.Width;
            if (LimitMaxWidth)
            {
                var maxWidth = (this.Parent.Width - 22 * 2) / 2;
                if (Width > maxWidth)
                {
                    pictureBox.Left -= (Width - maxWidth);
                    this.Width = maxWidth;
                }
            }
            pictureBox.BringToFront();

            TipControl?.SetToolTip(this, Text);
            TipControl?.SetToolTip(pictureBox, "【" + Text + "】\n" + TipText.CurrentText());
        }
    }
}
