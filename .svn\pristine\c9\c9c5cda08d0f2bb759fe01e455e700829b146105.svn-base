﻿using System;
using System.Collections.Specialized;
using System.IO;

namespace OCRTools.Common.ImageLib
{
    /// <summary>
    ///     http://tools.yum6.cn/Tools/Images/
    /// </summary>
    public class _360ImageUpload : BaseImageUpload
    {
        public _360ImageUpload()
        {
            ImageType = ImageTypeEnum._360;
        }

        public override string GetResult(byte[] content, string ext)
        {
            var result = GetFromFourUomg(content);
            if (string.IsNullOrEmpty(result)) result = GetFromFourInOne(content);
            return result;
        }

        private string GetFromFourInOne(byte[] content)
        {
            var result = "";
            try
            {
                var url = "https://apis.yum6.cn/api/5bd90750c3f77?token=f07b711396f9a05bc7129c4507fb65c5";
                var file = new UploadFileInfo
                {
                    Name = "file",
                    Filename = "test.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                var html = UploadFileRequest.Post(url, new[] {file}, null);
                var strFileNameSpilt = "\"data\":\"";
                if (html?.Contains(strFileNameSpilt) == true)
                {
                    result = html.Substring(html.IndexOf(strFileNameSpilt) + strFileNameSpilt.Length);
                    result = result.Substring(0, result.IndexOf("\"")).Replace("\\/", "/");
                }
            }
            catch (Exception)
            {
            }

            return result;
        }

        private string GetFromFourUomg(byte[] content)
        {
            var result = "";
            try
            {
                var url = "https://st.so.com/stu";
                var file = new UploadFileInfo
                {
                    Name = "upload",
                    Filename = "test.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                var vaules = new NameValueCollection
                {
                    {"imgurl", ""},
                    {"base64image", ""},
                    {"submittype", "upload"},
                    {"src", "image"},
                    {"srcsp", "st_search"}
                };
                var html = UploadFileRequest.Post(url, new[] {file}, vaules);
                var strFileNameSpilt = "\"imgkey\": \"";
                if (html?.Contains(strFileNameSpilt) == true)
                {
                    //https://p1.ssl.qhimgs1.com/t01e785eaf21d406f8d.jpg
                    result = html.Substring(html.IndexOf(strFileNameSpilt) + strFileNameSpilt.Length);
                    result = "https://p1.ssl.qhimgs1.com/" +
                             result.Substring(0, result.IndexOf("\"")).Replace("\\/", "/");
                }
            }
            catch (Exception)
            {
            }

            return result;
        }
    }
}