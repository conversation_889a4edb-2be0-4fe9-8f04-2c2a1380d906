﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class AnimationManager
    {
        public static int Speed { get; set; } = 5;

        public static int NowStep { get; set; }

        public static Point NowPoint { get; set; }

        public static void EnableAnimation(Control owner)
        {
            var _animationTimer = new Timer() { Interval = 5 };
            var isFirst = true;
            owner.MouseDown += (sender, e) =>
            {
                if (_animationTimer.Enabled)
                {
                    _animationTimer.Stop();
                    owner.Invalidate();
                }
                NowStep = 0;
                NowPoint = e.Location;
                if (isFirst)
                {
                    isFirst = false;
                    _animationTimer.Tick += (obj, e1) =>
                    {
                        var max = Math.Min(80, Math.Max(owner.Width, owner.Height));
                        if (owner.IsDisposed) return;
                        if (NowStep > max)
                        {
                            _animationTimer.Stop();
                            owner.Invalidate();
                            return;
                        }
                        NowStep += 5;// Math.Max((int)(max * 1.0d / 25), 5);
                        //owner.Invalidate();
                        //g.SmoothingMode = SmoothingMode.AntiAlias;
                        Color bg = Color.FromArgb(100, (int)Math.Abs(255 - owner.BackColor.R * 1.5), (int)Math.Abs(255 - owner.BackColor.G * 1.5), (int)Math.Abs(255 - owner.BackColor.B * 1.5));//取相反色
                        using (SolidBrush hrush = new SolidBrush(Color.FromArgb(50, bg)))
                        {
                            float x = NowPoint.X - NowStep / 2;
                            float y = NowPoint.Y - NowStep / 2;
                            RectangleF rect = new RectangleF(x, y, NowStep, NowStep);
                            owner.CreateGraphics().FillEllipse(hrush, rect);
                        }
                    };
                }
                _animationTimer.Start();
            };
        }
    }
}
