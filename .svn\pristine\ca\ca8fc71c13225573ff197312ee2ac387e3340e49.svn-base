using OCRTools.Common;
using System;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace OCRTools
{
    internal delegate IntPtr CbtProc(int nCode, IntPtr wParam, IntPtr lParam);

    public static class CenterForm
    {
        public static void CenterChild(this IWin32Window owner)
        {
            var centerChildHelper = new CenterChildHelper();
            centerChildHelper.Run(owner);
        }

        private class CenterChildHelper
        {
            private GCHandle _gch;

            private IntPtr _hhk;

            private IntPtr _parent;

            public void Run(IWin32Window owner)
            {
                CbtProc cBtProc = CenterChildCallBack;
                _gch = GCHandle.Alloc(cBtProc);
                _parent = owner.Handle;
                _hhk = NativeMethods.SetWindowsHookEx(5, cBtProc, IntPtr.Zero, NativeMethods.GetCurrentThreadId());
            }

            private IntPtr CenterChildCallBack(int nCode, IntPtr wParam, IntPtr lParam)
            {
                if (nCode == 5)
                {
                    NativeMethods.GetWindowRect(_parent, out var lpRect);
                    NativeMethods.GetWindowRect(wParam, out var lpRect2);
                    var num = lpRect2.Right - lpRect2.Left;
                    var num2 = lpRect2.Bottom - lpRect2.Top;
                    var num3 = (lpRect.Left + lpRect.Right - num) >> 1;
                    var num4 = (lpRect.Top + lpRect.Bottom - num2) >> 1;
                    if (num3 < 0 || num4 < 0 || num3 + num > Screen.PrimaryScreen.Bounds.Width ||
                        num4 + num2 > Screen.PrimaryScreen.Bounds.Height)
                    {
                        num3 = (Screen.PrimaryScreen.Bounds.Width - num) / 2;
                        num4 = (Screen.PrimaryScreen.Bounds.Height - num2) / 2;
                    }

                    NativeMethods.MoveWindow(wParam, num3, num4, num, num2, false);
                    NativeMethods.UnhookWindowsHookEx(_hhk);
                    _gch.Free();
                }

                return IntPtr.Zero;
            }
        }
    }
}