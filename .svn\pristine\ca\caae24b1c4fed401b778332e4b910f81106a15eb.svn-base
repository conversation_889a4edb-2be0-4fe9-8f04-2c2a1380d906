﻿#region License Information (GPL v3)

/*
    ShareX - A program that allows you to take screenshots and share any file type
    Copyright (c) 2007-2021 ShareX Team

    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License
    as published by the Free Software Foundation; either version 2
    of the License, or (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.

    Optionally you can also view the license at <http://www.gnu.org/licenses/>.
*/

#endregion License Information (GPL v3)

using OCRTools;
using OCRTools.Common;
using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Text;
using System.Linq;
using System.Windows.Forms;

namespace ShareX.ScreenCaptureLib
{
    public class Screenshot
    {
        public static bool CaptureClientArea { get; set; } = false;
        public static bool RemoveOutsideScreenArea { get; set; } = true;
        public static bool AutoHideTaskbar { get; set; } = false;

        public static string GetWorkAreaInfo()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine(string.Format("本机共{0}个显示器", Screen.AllScreens.Length));
            sb.AppendLine("==============================");
            foreach (var p in Screen.AllScreens)
            {
                sb.AppendLine(string.Format("屏幕{0} 是否主屏:{1}", p.DeviceName, p.Primary));
                sb.AppendLine(string.Format("起点:{0},{1} 分辨率:{2}*{3}"
                    , p.Bounds.X, p.Bounds.Y
                    , p.Bounds.Width, p.Bounds.Height));
                sb.AppendLine();
            }
            sb.AppendLine("==============================");
            sb.AppendLine("确认以上信息是否正确。");
            sb.AppendLine("您可以补充信息，以帮助我们快速定位问题！");
            sb.AppendLine("如：图片黑屏/拉伸不正常");
            sb.AppendLine("双屏分辨率及缩放设置");
            return sb.ToString();
        }

        //public static Rectangle GetWorkAreaRectangle()
        //{
        //    //var screenBounds = NativeMethods.GetScreenBounds();
        //    //var realBounds = PrimaryScreen.GetAllRectangle();
        //    //PrimaryScreen.ScreenScalingFactor = realBounds.Width * 1.0f / screenBounds.Width;
        //    Rectangle rectDisplay = new Rectangle();
        //    return PrimaryScreen.GetAllRectangle(ref rectDisplay);
        //}

        public static Bitmap CaptureRectangle(Rectangle srcRect, Rectangle destRect)
        {
            return CaptureRectangleNative(srcRect, destRect);
        }

        public static Bitmap CaptureFullscreen(ref Rectangle srcRect, bool isNeedScal = false)
        {
            //Rectangle destRect = srcRect;
            //if (srcRect.IsEmpty)
            //    srcRect = PrimaryScreen.GetAllRectangle(ref destRect);
            ////destRect = NativeMethods.GetScreenBounds0Based();
            ////if (isNeedScal)
            ////{
            ////    destRect = GetDestRectangle(srcRect);
            ////    //destRect = NativeMethods.GetScreenBounds();
            ////}
            //var bitMap = CaptureRectangle(srcRect, destRect);
            //srcRect = destRect;
            var bitMap = CaptureRectangle(srcRect, srcRect);
            return bitMap;
        }

        public static Bitmap CaptureWindow(IntPtr handle, ref Rectangle rect)
        {
            if (handle.ToInt32() > 0)
            {
                rect = handle.GetRectangle(!CaptureClientArea);

                var isTaskbarHide = false;
                try
                {
                    if (AutoHideTaskbar) isTaskbarHide = NativeMethods.SetTaskbarVisibilityIfIntersect(false, rect);

                    return CaptureRectangle(rect, rect);
                }
                finally
                {
                    if (isTaskbarHide) NativeMethods.SetTaskbarVisibility(true);
                }
            }

            return null;
        }

        public static Bitmap CaptureActiveWindow(ref Rectangle rect)
        {
            var handle = NativeMethods.GetForegroundWindow();

            return CaptureWindow(handle, ref rect);
        }

        public static Bitmap CaptureActiveMonitor(ref Rectangle rect)
        {
            rect = NativeMethods.GetActiveScreenBounds();

            return CaptureRectangle(rect, rect);
        }

        private static Bitmap CaptureRectangleNative2(Rectangle srcRect)
        {
            var bmp = new Bitmap(srcRect.Width, srcRect.Height);
            try
            {
                IntPtr hwnd = NativeMethods.GetDesktopWindow();
                IntPtr hdc = NativeMethods.GetDC(hwnd);
                using (Graphics g = Graphics.FromHdc(hdc))
                {
                    g.DrawImage(bmp, new Point(0, 0));
                }
                NativeMethods.ReleaseDC(hwnd, hdc);
            }
            catch { }
            return bmp;
        }

        private static Bitmap CaptureRectangleNative(Rectangle srcRect, Rectangle destRect)
        {
            if (srcRect.Size.IsEmpty) return null;

            var handle = NativeMethods.GetDesktopWindow();

            IntPtr hdcSrc = NativeMethods.GetWindowDC(handle);
            IntPtr hdcDest = NativeMethods.CreateCompatibleDC(hdcSrc);
            IntPtr hBitmap = NativeMethods.CreateCompatibleBitmap(hdcSrc, srcRect.Width, srcRect.Height);
            IntPtr hOld = NativeMethods.SelectObject(hdcDest, hBitmap);
            if (srcRect.Size.Equals(destRect.Size))
            {
                NativeMethods.BitBlt(hdcDest, 0, 0, srcRect.Width, srcRect.Height, hdcSrc, srcRect.X, srcRect.Y, CopyPixelOperation.SourceCopy | CopyPixelOperation.CaptureBlt);
            }
            else
            {
                NativeMethods.SetStretchBltMode(hdcDest, 0x04);
                NativeMethods.StretchBlt(hdcDest, destRect.X, destRect.Y, destRect.Width, destRect.Height
                    , hdcSrc, srcRect.X, srcRect.Y, srcRect.Width, srcRect.Height,
                    CopyPixelOperation.SourceCopy | CopyPixelOperation.CaptureBlt);
            }

            NativeMethods.SelectObject(hdcDest, hOld);
            NativeMethods.DeleteDC(hdcDest);
            NativeMethods.ReleaseDC(handle, hdcSrc);
            var bmp = Image.FromHbitmap(hBitmap);
            NativeMethods.DeleteObject(hBitmap);
            //if (!srcRect.Size.Equals(destRect.Size))
            //{
            //    bmp = ResizeImage(bmp, destRect.Width, destRect.Height);
            //}
            return bmp;
        }

        /// <summary>
        /// Resize the image to the specified width and height.
        /// </summary>
        /// <param name="image">The image to resize.</param>
        /// <param name="width">The width to resize to.</param>
        /// <param name="height">The height to resize to.</param>
        /// <returns>The resized image.</returns>
        public static Bitmap ResizeImage(Image image, int width, int height)
        {
            var destRect = new Rectangle(0, 0, width, height);
            var destImage = new Bitmap(width, height);

            destImage.SetResolution(image.HorizontalResolution, image.VerticalResolution);

            using (var graphics = Graphics.FromImage(destImage))
            {
                graphics.CompositingMode = CompositingMode.SourceCopy;
                graphics.CompositingQuality = CompositingQuality.HighQuality;
                graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                graphics.SmoothingMode = SmoothingMode.HighQuality;
                graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;

                using (var wrapMode = new ImageAttributes())
                {
                    wrapMode.SetWrapMode(WrapMode.TileFlipXY);
                    graphics.DrawImage(image, destRect, 0, 0, image.Width, image.Height, GraphicsUnit.Pixel, wrapMode);
                }
            }

            return destImage;
        }

        //等比例缩放图片
        public static Bitmap ZoomImage(Bitmap bitmap, int destHeight, int destWidth)
        {
            try
            {
                System.Drawing.Image sourImage = bitmap;
                int width = 0, height = 0;
                //按比例缩放           
                int sourWidth = sourImage.Width;
                int sourHeight = sourImage.Height;
                if (sourHeight > destHeight || sourWidth > destWidth)
                {
                    if ((sourWidth * destHeight) > (sourHeight * destWidth))
                    {
                        width = destWidth;
                        height = (destWidth * sourHeight) / sourWidth;
                    }
                    else
                    {
                        height = destHeight;
                        width = (sourWidth * destHeight) / sourHeight;
                    }
                }
                else
                {
                    width = sourWidth;
                    height = sourHeight;
                }
                Bitmap destBitmap = new Bitmap(destWidth, destHeight);
                Graphics g = Graphics.FromImage(destBitmap);
                g.Clear(Color.Transparent);
                //设置画布的描绘质量         
                g.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                g.DrawImage(sourImage, new Rectangle((destWidth - width) / 2, (destHeight - height) / 2, width, height), 0, 0, sourImage.Width, sourImage.Height, GraphicsUnit.Pixel);
                g.Dispose();
                //设置压缩质量     
                System.Drawing.Imaging.EncoderParameters encoderParams = new System.Drawing.Imaging.EncoderParameters();
                long[] quality = new long[1];
                quality[0] = 100;
                System.Drawing.Imaging.EncoderParameter encoderParam = new System.Drawing.Imaging.EncoderParameter(System.Drawing.Imaging.Encoder.Quality, quality);
                encoderParams.Param[0] = encoderParam;
                sourImage.Dispose();
                return destBitmap;
            }
            catch
            {
                return bitmap;
            }
        }

        //private Bitmap CaptureRectangleManaged(Rectangle rect)
        //{
        //    if (rect.Width == 0 || rect.Height == 0)
        //    {
        //        return null;
        //    }

        //    Bitmap bmp = new Bitmap(rect.Width, rect.Height, PixelFormat.Format32bppArgb);

        //    using (Graphics g = Graphics.FromImage(bmp))
        //    {
        //        g.SetHighQuality();
        //        // Managed can't use SourceCopy | CaptureBlt because of .NET bug
        //        g.CopyFromScreen(rect.Location, Point.Empty, rect.Size, CopyPixelOperation.SourceCopy);
        //    }

        //    return bmp;
        //}
    }
}