using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.IO;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace OCRTools
{
    public static class ImageHelp
    {
        [DllImport("User32.dll")]
        private static extern IntPtr LoadCursorFromFile(string str);

        public static Cursor SetCursor(byte[] resourceName)
        {
            var folderPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var text = folderPath + "\\temp.cur";
            File.WriteAllBytes(text, resourceName);
            var result = new Cursor(LoadCursorFromFile(text));
            File.Delete(text);
            return result;
        }

        public static Image EditEllipse(Color color, int num)
        {
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var solidBrush = new SolidBrush(color))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                    switch (num)
                    {
                        case 1:
                            graphics.FillEllipseCenter(solidBrush, center, 4.DpiValue());
                            break;
                        case 2:
                            graphics.FillEllipseCenter(solidBrush, center, 6.DpiValue());
                            break;
                        case 3:
                            graphics.FillEllipseCenter(solidBrush, center, 8.DpiValue());
                            break;
                        case 4:
                            graphics.FillEllipseCenter(solidBrush, center, 10.DpiValue());
                            break;
                    }
                }
            }

            return bitmap;
        }

        public static Image CreateStep(Color color)
        {
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                    var rect = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
                    graphics.DrawRectangle(pen, rect);
                    graphics.DrawStringCenter(pen, center, 6.DpiValue(), color);
                }
            }

            return bitmap;
        }

        public static void DrawStringCenter(this Graphics graphics, Pen pen, Point center, int width, Color color)
        {
            var r = new Rectangle(center.X - width + 1.DpiValue(), center.Y - width, width * 2, width * 2);
            using (var format = new StringFormat
            {
                Alignment = StringAlignment.Center,
                LineAlignment = StringAlignment.Near
            })
            {
                using (var brush = new SolidBrush(color))
                {
                    graphics.TextRenderingHint = TextRenderingHint.AntiAlias;
                    graphics.DrawString("1", new Font("Times New Romans", 10f), brush, r, format);
                }
            }
        }

        public static void DrawTextCenter(this Graphics graphics, Pen pen, Point center, int width, Color color)
        {
            var r = new Rectangle(center.X - width + 1.DpiValue(), center.Y - width, width * 2, width * 2);
            using (var format = new StringFormat
            {
                Alignment = StringAlignment.Center,
                LineAlignment = StringAlignment.Near
            })
            {
                using (var brush = new SolidBrush(color))
                {
                    graphics.TextRenderingHint = TextRenderingHint.AntiAlias;
                    graphics.DrawString("T", new Font("Times New Romans", 10f), brush, r, format);
                }
            }
        }

        public static void FillEllipseCenter(this Graphics graphics, SolidBrush solidBrush1, Point center, int width)
        {
            var rect = new Rectangle(center.X - width, center.Y - width, width * 2, width * 2);
            graphics.FillEllipse(solidBrush1, rect);
        }

        public static void DrawRectangleCenter(this Graphics graphics, Pen pen, Point center, int width)
        {
            var rect = new Rectangle(center.X - width, center.Y - width, width * 2, width * 2);
            graphics.DrawRectangle(pen, rect);
        }

        public static void FillRectangleCenter(this Graphics graphics, SolidBrush solidBrush1, Point center, int width)
        {
            var rect = new Rectangle(center.X - width, center.Y - width, width * 2, width * 2);
            graphics.FillRectangle(solidBrush1, rect);
        }

        public static Image CreateEllipse(Color color, bool isdot = false, bool ischeck = false)
        {
            var rect = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var brush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
                {
                    using (var pen = new Pen(color, 1.DpiValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighQuality;
                        if (isdot)
                            pen.DashPattern = new[]
                            {
                                3f,
                                3f
                            };
                        if (ischeck) graphics.FillEllipse(brush, rect);
                        graphics.DrawEllipse(pen, rect);
                    }
                }
            }

            return bitmap;
        }

        public static Image CreateRectangle(Color color, bool isdot = false, bool ischeck = false)
        {
            var rect = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var brush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
                {
                    using (var pen = new Pen(color, 1.DpiValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighSpeed;
                        if (isdot)
                            pen.DashPattern = new[]
                            {
                                3f,
                                3f
                            };
                        if (ischeck) graphics.FillRectangle(brush, rect);
                        graphics.DrawRectangle(pen, rect);
                    }
                }
            }

            return bitmap;
        }

        public static Image CreateHighlight(Color color)
        {
            var rect = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var brush = new SolidBrush(Color.FromArgb(40, color)))
                {
                    using (var pen = new Pen(color, 1.DpiValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighSpeed;
                        graphics.FillRectangle(brush, rect);
                        graphics.DrawRectangle(pen, rect);
                        var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                        graphics.DrawTextCenter(pen, center, 6.DpiValue(), color);
                    }
                }
            }

            return bitmap;
        }

        public static Image CreateRectangleFill(Color color)
        {
            var rect = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var brush = new SolidBrush(Color.FromArgb(40, color)))
                {
                    using (var pen = new Pen(color, 1.DpiValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighSpeed;
                        graphics.FillRectangle(brush, rect);
                        graphics.DrawRectangle(pen, rect);
                    }
                }
            }

            return bitmap;
        }

        public static Image CreateRectangleImage(Color color, int width, int height, int left, int top)
        {
            var bitmap = new Bitmap(width.DpiValue(), height.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var brush = new SolidBrush(color))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    graphics.FillRectangle(brush,
                        new RectangleF(new Point(left, top),
                            new Size(bitmap.Width - 2 * left, bitmap.Height - 2 * top)));
                }
            }

            return bitmap;
        }

        public static Image CreateTextDot(bool ischeck = false)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                var color = Color.FromArgb(150, CustomColor.UnCheckColor);
                if (ischeck) color = CustomColor.CheckColor;
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    graphics.DrawPolygon(pen, new[]
                    {
                        new Point(rectangle.X, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y + 1.DpiValue() + 1.DpiValue()),
                        new Point(rectangle.X + rectangle.Width / 2 + 1.DpiValue(),
                            rectangle.Y + 1.DpiValue() + 1.DpiValue()),
                        new Point(rectangle.X + rectangle.Width / 2 + 1.DpiValue(), rectangle.Y + rectangle.Height),
                        new Point(rectangle.X + rectangle.Width / 2 - 1.DpiValue(), rectangle.Y + rectangle.Height),
                        new Point(rectangle.X + rectangle.Width / 2 - 1.DpiValue(),
                            rectangle.Y + 1.DpiValue() + 1.DpiValue()),
                        new Point(rectangle.X, rectangle.Y + 1.DpiValue() + 1.DpiValue()),
                        new Point(rectangle.X, rectangle.Y)
                    });
                }
            }

            return bitmap;
        }

        public static Image CreateCopy(Color color)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    var rect = new Rectangle(rectangle.X + rectangle.Width / 5, rectangle.Y + rectangle.Width / 5,
                        rectangle.Height * 4 / 5, rectangle.Height * 4 / 5);
                    graphics.DrawLines(pen, new[]
                    {
                        new Point(rectangle.X + rectangle.Width / 5, rectangle.Y + rectangle.Width * 4 / 5),
                        new Point(rectangle.X, rectangle.Y + rectangle.Width * 4 / 5),
                        new Point(rectangle.X, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width * 4 / 5, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width * 4 / 5, rectangle.Y + rectangle.Width / 5)
                    });
                    graphics.DrawRectangle(pen, rect);
                }
            }

            return bitmap;
        }

        public static Image CreatePaste(Color color)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    var rect = new Rectangle(rectangle.X + rectangle.Width / 5, rectangle.Y + rectangle.Width / 5,
                        rectangle.Height * 4 / 5, rectangle.Height * 4 / 5);
                    graphics.DrawLines(pen, new[]
                    {
                        new Point(rectangle.X + rectangle.Width / 5, rectangle.Y + rectangle.Width * 4 / 5),
                        new Point(rectangle.X, rectangle.Y + rectangle.Width * 4 / 5),
                        new Point(rectangle.X, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width * 4 / 5, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width * 4 / 5, rectangle.Y + rectangle.Width / 5)
                    });
                    graphics.DrawRectangle(pen, rect);
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.DrawLine(pen, new Point(rect.X + rect.Height / 4, rect.Y + rect.Height - rect.Height / 4),
                        new Point(rect.X + rect.Width - rect.Height / 4, rect.Y + rect.Height / 4));
                    var rectangle2 = new Rectangle(rect.X, rect.Y, rect.Width / 2, rect.Height / 2);
                    var rectangle3 = new Rectangle(rect.X + rect.Width / 2, rect.Y + rect.Height / 2, rect.Width / 2,
                        rect.Height / 2);
                    graphics.DrawLine(pen,
                        new Point(rectangle2.X + rectangle2.Height / 3,
                            rectangle2.Y + rectangle2.Height - rectangle2.Height / 3),
                        new Point(rectangle2.X + rectangle2.Width - rectangle2.Height / 3,
                            rectangle2.Y + rectangle2.Height / 3));
                    graphics.DrawLine(pen,
                        new Point(rectangle3.X + rectangle3.Height / 3,
                            rectangle3.Y + rectangle3.Height - rectangle3.Height / 3),
                        new Point(rectangle3.X + rectangle3.Width - rectangle3.Height / 3,
                            rectangle3.Y + rectangle3.Height / 3));
                }
            }

            return bitmap;
        }

        public static Image CreateMosaic(Color color)
        {
            var rect = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var brush = new SolidBrush(Color.FromArgb(100, color)))
                {
                    using (var brush2 = new SolidBrush(Color.FromArgb(40, color)))
                    {
                        using (var pen = new Pen(color, 1.DpiValue()))
                        {
                            graphics.SmoothingMode = SmoothingMode.HighSpeed;
                            graphics.FillRectangle(brush,
                                new Rectangle(rect.X, rect.Y, rect.Width / 2, rect.Height / 2));
                            graphics.FillRectangle(brush2,
                                new Rectangle(rect.X + rect.Width / 2, rect.Y + rect.Height / 2, rect.Width / 2,
                                    rect.Height / 2));
                            graphics.DrawRectangle(pen, rect);
                        }
                    }
                }
            }

            return bitmap;
        }

        public static Image CreateGaus(Color color)
        {
            var rect = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    graphics.DrawRectangle(pen, rect);
                    graphics.DrawLine(pen, new Point(rect.X + rect.Height / 4, rect.Y + rect.Height - rect.Height / 4),
                        new Point(rect.X + rect.Width - rect.Height / 4, rect.Y + rect.Height / 4));
                    var rectangle = new Rectangle(rect.X, rect.Y, rect.Width / 2, rect.Height / 2);
                    var rectangle2 = new Rectangle(rect.X + rect.Width / 2, rect.Y + rect.Height / 2, rect.Width / 2,
                        rect.Height / 2);
                    graphics.DrawLine(pen,
                        new Point(rectangle.X + rectangle.Height / 3,
                            rectangle.Y + rectangle.Height - rectangle.Height / 3),
                        new Point(rectangle.X + rectangle.Width - rectangle.Height / 3,
                            rectangle.Y + rectangle.Height / 3));
                    graphics.DrawLine(pen,
                        new Point(rectangle2.X + rectangle2.Height / 3,
                            rectangle2.Y + rectangle2.Height - rectangle2.Height / 3),
                        new Point(rectangle2.X + rectangle2.Width - rectangle2.Height / 3,
                            rectangle2.Y + rectangle2.Height / 3));
                }
            }

            return bitmap;
        }

        public static Image CreateUndo(Color color)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.DrawLine(pen, new Point(rectangle.X + 1, rectangle.Y + rectangle.Height / 2),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y + rectangle.Height / 2));
                    var rectangle2 = new Rectangle(rectangle.X, rectangle.Y, rectangle.Width / 2, rectangle.Height / 2);
                    graphics.DrawLine(pen, new Point(rectangle2.X, rectangle2.Y + rectangle2.Height),
                        new Point(rectangle2.X + rectangle2.Width - rectangle2.Height / 3,
                            rectangle2.Y + rectangle2.Height / 3));
                    graphics.DrawLine(pen, new Point(rectangle2.X, rectangle2.Y + rectangle2.Height),
                        new Point(rectangle2.X + rectangle2.Width - rectangle2.Height / 3,
                            rectangle2.Y + rectangle.Height - rectangle2.Height / 3));
                    //new Rectangle(rectangle.X, rectangle.Y + rectangle.Height / 2, rectangle.Width, rectangle.Height);
                }
            }

            return bitmap;
        }

        public static Image CreateRedo(Color color)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Height / 2),
                        new Point(rectangle.X + rectangle.Width - 2, rectangle.Y + rectangle.Height / 2));
                    var rectangle2 = new Rectangle(rectangle.X + rectangle.Width / 2, rectangle.Y, rectangle.Width / 2,
                        rectangle.Height / 2);
                    graphics.DrawLine(pen,
                        new Point(rectangle2.X + rectangle2.Height / 3, rectangle2.Y + rectangle2.Height / 3),
                        new Point(rectangle2.X + rectangle2.Width, rectangle2.Y + rectangle2.Width));
                    graphics.DrawLine(pen,
                        new Point(rectangle2.X + rectangle2.Height / 3,
                            rectangle.Y + rectangle.Height - rectangle2.Height / 3),
                        new Point(rectangle2.X + rectangle2.Width, rectangle2.Y + rectangle2.Width));
                    //new Rectangle(rectangle.X, rectangle.Y + rectangle.Height / 2, rectangle.Width, rectangle.Height);
                }
            }

            return bitmap;
        }

        public static Image CreateSave(Color color)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    var points = new[]
                    {
                        new Point(rectangle.X, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width * 3 / 4, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y + rectangle.Height / 4),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y + rectangle.Height),
                        new Point(rectangle.X, rectangle.Y + rectangle.Height),
                        new Point(rectangle.X, rectangle.Y)
                    };
                    graphics.DrawLines(pen, points);
                    graphics.DrawLine(pen,
                        new Point(rectangle.X + rectangle.Width / 4, rectangle.Y + rectangle.Height / 5),
                        new Point(rectangle.X + rectangle.Width * 3 / 4, rectangle.Y + rectangle.Height / 5));
                    var points2 = new[]
                    {
                        new Point(rectangle.X + rectangle.Width / 5, rectangle.Y + rectangle.Height),
                        new Point(rectangle.X + rectangle.Width / 5, rectangle.Y + rectangle.Height / 2),
                        new Point(rectangle.X + rectangle.Width * 4 / 5, rectangle.Y + rectangle.Height / 2),
                        new Point(rectangle.X + rectangle.Width * 4 / 5, rectangle.Y + rectangle.Height)
                    };
                    graphics.DrawLines(pen, points2);
                }
            }

            return bitmap;
        }

        public static Image CreateClose(Color color)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.DrawLine(pen,
                        new Point(rectangle.X + rectangle.Height / 8, rectangle.Y + rectangle.Height / 8),
                        new Point(rectangle.X + rectangle.Width - rectangle.Height / 8,
                            rectangle.Y + rectangle.Height - rectangle.Height / 8));
                    graphics.DrawLine(pen,
                        new Point(rectangle.X + rectangle.Height / 8,
                            rectangle.Y + rectangle.Height - rectangle.Height / 8),
                        new Point(rectangle.X + rectangle.Width - rectangle.Height / 8,
                            rectangle.Y + rectangle.Height / 8));
                }
            }

            return bitmap;
        }

        public static Image CreatePolygon(Color color, bool isdot = false, bool ischeck = false)
        {
            if (color == Color.FromArgb(120, 120, 120)) color = Color.FromArgb(165, color);
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen2 = new Pen(Color.White, 2.DpiValue()))
                {
                    using (var pen = new Pen(color, 2.DpiValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighQuality;
                        graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Width),
                            new Point(rectangle.X + rectangle.Width, rectangle.Y));
                        graphics.DrawLine(pen2, new Point(rectangle.X, rectangle.Y + rectangle.Height / 2),
                            new Point(rectangle.X + rectangle.Width,
                                rectangle.Y + rectangle.Height + rectangle.Height / 2));
                    }
                }

                return bitmap;
            }
        }

        public static Image CreateLine(Color color, bool isdot = false, bool ischeck = false)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen2 = new Pen(Color.FromArgb(20, 0, 0, 0), 6.DpiValue()))
                {
                    using (var pen = new Pen(color, 1.DpiValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighQuality;
                        if (isdot)
                            pen.DashPattern = new[]
                            {
                                3f,
                                3f
                            };
                        if (ischeck)
                            graphics.DrawLine(pen2, new Point(rectangle.X, rectangle.Y + rectangle.Height),
                                new Point(rectangle.X + rectangle.Width, rectangle.Y));
                        graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Height),
                            new Point(rectangle.X + rectangle.Width, rectangle.Y));
                    }
                }

                return bitmap;
            }
        }

        public static Image CreateArrow(Color color)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Height),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y));
                    graphics.DrawLine(pen, new Point(rectangle.X + rectangle.Width / 2, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y));
                    graphics.DrawLine(pen, new Point(rectangle.X + rectangle.Width, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y + rectangle.Height / 2));
                }
            }

            return bitmap;
        }

        public static Image CreateArrowBoth(bool ischeck)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            var color = Color.FromArgb(150, 120, 120, 120);
            if (ischeck) color = CustomColor.CheckColor;
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Height),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y));
                    graphics.DrawLine(pen, new Point(rectangle.X + rectangle.Width * 2 / 3, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y));
                    graphics.DrawLine(pen, new Point(rectangle.X + rectangle.Width, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y + rectangle.Height / 3));
                    graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Height * 2 / 3),
                        new Point(rectangle.X, rectangle.Y + rectangle.Height));
                    graphics.DrawLine(pen, new Point(rectangle.X + rectangle.Width / 3, rectangle.Y + rectangle.Height),
                        new Point(rectangle.X, rectangle.Y + rectangle.Height));
                }
            }

            return bitmap;
        }

        public static Image CreateText(Color color)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y));
                    graphics.DrawLine(pen, new Point(rectangle.X + rectangle.Width / 2, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width / 2, rectangle.Y + rectangle.Height));
                }
            }

            return bitmap;
        }

        public static Image EditRectangle(Color color, bool ischeck = false)
        {
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var solidBrush2 = new SolidBrush(Color.White))
                {
                    using (var pen = new Pen(Color.Black))
                    {
                        using (var solidBrush = new SolidBrush(color))
                        {
                            graphics.SmoothingMode = SmoothingMode.HighSpeed;
                            var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                            graphics.FillRectangleCenter(solidBrush, center, 8.DpiValue());
                            graphics.DrawRectangleCenter(pen, center, 8.DpiValue());
                            if (ischeck)
                            {
                                graphics.FillRectangleCenter(solidBrush2, center, 4.DpiValue());
                                graphics.DrawRectangleCenter(pen, center, 4.DpiValue());
                            }
                        }
                    }
                }
            }

            return bitmap;
        }

        public static Image EditCross(bool ischeck = false)
        {
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                var color = Color.FromArgb(150, CustomColor.UnCheckColor);
                if (ischeck) color = CustomColor.CheckColor;
                using (var pen = new Pen(color))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    var point = new Point(bitmap.Width / 2, bitmap.Height / 2);
                    var rectangle = new Rectangle(point.X - 8.DpiValue(), point.Y - 8.DpiValue(), 8.DpiValue() * 2,
                        8.DpiValue() * 2);
                    graphics.DrawPolygon(pen, new[]
                    {
                        new Point(point.X - 1.DpiValue(), rectangle.Y),
                        new Point(point.X + 1.DpiValue(), rectangle.Y),
                        new Point(point.X + 1.DpiValue(), point.Y - 1.DpiValue()),
                        new Point(rectangle.X + rectangle.Width, point.Y - 1.DpiValue()),
                        new Point(rectangle.X + rectangle.Width, point.Y + 1.DpiValue()),
                        new Point(point.X + 1.DpiValue(), point.Y + 1.DpiValue()),
                        new Point(point.X + 1.DpiValue(), rectangle.Y + rectangle.Height),
                        new Point(point.X - 1.DpiValue(), rectangle.Y + rectangle.Height),
                        new Point(point.X - 1.DpiValue(), point.Y + 1.DpiValue()),
                        new Point(rectangle.X, point.Y + 1.DpiValue()),
                        new Point(rectangle.X, point.Y - 1.DpiValue()),
                        new Point(point.X - 1.DpiValue(), point.Y - 1.DpiValue()),
                        new Point(point.X - 1.DpiValue(), rectangle.Y)
                    });
                }
            }

            return bitmap;
        }

        public static Image EditRectangleCus(Color color, bool ischeck = false)
        {
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(Color.Black))
                {
                    using (var brush = new SolidBrush(Color.FromArgb(100, color)))
                    {
                        using (var brush2 = new SolidBrush(Color.FromArgb(40, color)))
                        {
                            graphics.SmoothingMode = SmoothingMode.HighSpeed;
                            var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                            var rectangle = new Rectangle(center.X - 8.DpiValue(), center.Y - 8.DpiValue(),
                                8.DpiValue() * 2, 8.DpiValue() * 2);
                            graphics.FillRectangle(brush,
                                new Rectangle(rectangle.X, rectangle.Y, rectangle.Width / 2, rectangle.Height / 2));
                            graphics.FillRectangle(brush2,
                                new Rectangle(rectangle.X + rectangle.Width / 2, rectangle.Y + rectangle.Height / 2,
                                    rectangle.Width / 2, rectangle.Height / 2));
                            graphics.DrawRectangleCenter(pen, center, 8.DpiValue());
                        }
                    }
                }
            }

            return bitmap;
        }
    }
}