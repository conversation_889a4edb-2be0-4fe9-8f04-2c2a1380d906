using System;
using System.Text;
using UtfUnknown.Core.Models;

namespace UtfUnknown.Core.Probers
{
    public class SingleByteCharSetProber : CharsetProber
    {
        private const int SB_ENOUGH_REL_THRESHOLD = 1024;

        private const float POSITIVE_SHORTCUT_THRESHOLD = 0.95f;

        private const float NEGATIVE_SHORTCUT_THRESHOLD = 0.05f;

        private const int SYMBOL_CAT_ORDER = 250;

        private const int NUMBER_OF_SEQ_CAT = 4;

        private const int POSITIVE_CAT = 3;

        private const int PROBABLE_CAT = 2;

        private const int NEUTRAL_CAT = 1;

        private const int NEGATIVE_CAT = 0;

        private readonly CharsetProber nameProber;

        private readonly bool reversed;

        private readonly int[] seqCounters = new int[4];

        private int ctrlChar;

        private int freqChar;

        private byte lastOrder;

        protected SequenceModel model;

        private int totalChar;

        private int totalSeqs;

        public SingleByteCharSetProber(SequenceModel model)
            : this(model, false, null)
        {
        }

        public SingleByteCharSetProber(SequenceModel model, bool reversed, CharsetProber nameProber)
        {
            this.model = model;
            this.reversed = reversed;
            this.nameProber = nameProber;
            Reset();
        }

        public override ProbingState HandleData(byte[] buf, int offset, int len)
        {
            var num = offset + len;
            for (var i = offset; i < num; i++)
            {
                var order = model.GetOrder(buf[i]);
                if (order < 250)
                {
                    totalChar++;
                }
                else
                {
                    if (order == byte.MaxValue)
                    {
                        state = ProbingState.NotMe;
                        break;
                    }

                    if (order == 254) ctrlChar++;
                }

                if (order < model.FreqCharCount)
                {
                    freqChar++;
                    if (lastOrder < model.FreqCharCount)
                    {
                        totalSeqs++;
                        if (!reversed)
                            seqCounters[model.GetPrecedence(lastOrder * model.FreqCharCount + order)]++;
                        else
                            seqCounters[model.GetPrecedence(order * model.FreqCharCount + lastOrder)]++;
                    }
                }

                lastOrder = order;
            }

            if (state == ProbingState.Detecting && totalSeqs > 1024)
            {
                var confidence = GetConfidence();
                if (confidence > 0.95f)
                    state = ProbingState.FoundIt;
                else if (confidence < 0.05f) state = ProbingState.NotMe;
            }

            return state;
        }

        public override string DumpStatus()
        {
            var stringBuilder = new StringBuilder();
            stringBuilder.AppendLine($"  SBCS: {GetConfidence():0.00############} [{GetCharsetName()}]");
            return stringBuilder.ToString();
        }

        private void StringBuilder(string v1, float v2, string v3)
        {
            throw new NotImplementedException();
        }

        public override float GetConfidence(StringBuilder status = null)
        {
            if (totalSeqs > 0)
            {
                var num = 1f * seqCounters[3] / totalSeqs / model.TypicalPositiveRatio;
                num = num * (seqCounters[3] + seqCounters[2] / 4f) / totalChar;
                num = num * (totalChar - ctrlChar) / totalChar;
                num = num * freqChar / totalChar;
                if (num >= 1f) num = 0.99f;
                return num;
            }

            return 0.01f;
        }

        public override void Reset()
        {
            state = ProbingState.Detecting;
            lastOrder = byte.MaxValue;
            for (var i = 0; i < 4; i++) seqCounters[i] = 0;
            totalSeqs = 0;
            totalChar = 0;
            freqChar = 0;
        }

        public override string GetCharsetName()
        {
            if (nameProber != null) return nameProber.GetCharsetName();
            return model.CharsetName;
        }
    }
}