// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Windows.Automation.Text;
using UIAComWrapperInternal;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class TextPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = TextPatternIdentifiers.Pattern;

        public static readonly AutomationTextAttribute AnimationStyleAttribute =
            TextPatternIdentifiers.AnimationStyleAttribute;

        public static readonly AutomationTextAttribute BackgroundColorAttribute =
            TextPatternIdentifiers.BackgroundColorAttribute;

        public static readonly AutomationTextAttribute BulletStyleAttribute =
            TextPatternIdentifiers.BulletStyleAttribute;

        public static readonly AutomationTextAttribute CapStyleAttribute = TextPatternIdentifiers.CapStyleAttribute;
        public static readonly AutomationTextAttribute CultureAttribute = TextPatternIdentifiers.CultureAttribute;
        public static readonly AutomationTextAttribute FontNameAttribute = TextPatternIdentifiers.FontNameAttribute;
        public static readonly AutomationTextAttribute FontSizeAttribute = TextPatternIdentifiers.FontSizeAttribute;
        public static readonly AutomationTextAttribute FontWeightAttribute = TextPatternIdentifiers.FontWeightAttribute;

        public static readonly AutomationTextAttribute ForegroundColorAttribute =
            TextPatternIdentifiers.ForegroundColorAttribute;

        public static readonly AutomationTextAttribute HorizontalTextAlignmentAttribute =
            TextPatternIdentifiers.HorizontalTextAlignmentAttribute;

        public static readonly AutomationTextAttribute IndentationFirstLineAttribute =
            TextPatternIdentifiers.IndentationFirstLineAttribute;

        public static readonly AutomationTextAttribute IndentationLeadingAttribute =
            TextPatternIdentifiers.IndentationLeadingAttribute;

        public static readonly AutomationTextAttribute IndentationTrailingAttribute =
            TextPatternIdentifiers.IndentationTrailingAttribute;

        public static readonly AutomationTextAttribute IsHiddenAttribute = TextPatternIdentifiers.IsHiddenAttribute;
        public static readonly AutomationTextAttribute IsItalicAttribute = TextPatternIdentifiers.IsItalicAttribute;
        public static readonly AutomationTextAttribute IsReadOnlyAttribute = TextPatternIdentifiers.IsReadOnlyAttribute;

        public static readonly AutomationTextAttribute IsSubscriptAttribute =
            TextPatternIdentifiers.IsSubscriptAttribute;

        public static readonly AutomationTextAttribute IsSuperscriptAttribute =
            TextPatternIdentifiers.IsSuperscriptAttribute;

        public static readonly AutomationTextAttribute MarginBottomAttribute =
            TextPatternIdentifiers.MarginBottomAttribute;

        public static readonly AutomationTextAttribute MarginLeadingAttribute =
            TextPatternIdentifiers.MarginLeadingAttribute;

        public static readonly AutomationTextAttribute MarginTopAttribute = TextPatternIdentifiers.MarginTopAttribute;

        public static readonly AutomationTextAttribute MarginTrailingAttribute =
            TextPatternIdentifiers.MarginTrailingAttribute;

        public static readonly AutomationTextAttribute OutlineStylesAttribute =
            TextPatternIdentifiers.OutlineStylesAttribute;

        public static readonly AutomationTextAttribute OverlineColorAttribute =
            TextPatternIdentifiers.OverlineColorAttribute;

        public static readonly AutomationTextAttribute OverlineStyleAttribute =
            TextPatternIdentifiers.OverlineStyleAttribute;

        public static readonly AutomationTextAttribute StrikethroughColorAttribute =
            TextPatternIdentifiers.StrikethroughColorAttribute;

        public static readonly AutomationTextAttribute StrikethroughStyleAttribute =
            TextPatternIdentifiers.StrikethroughStyleAttribute;

        public static readonly AutomationTextAttribute TabsAttribute = TextPatternIdentifiers.TabsAttribute;

        public static readonly AutomationTextAttribute TextFlowDirectionsAttribute =
            TextPatternIdentifiers.TextFlowDirectionsAttribute;

        public static readonly AutomationTextAttribute UnderlineColorAttribute =
            TextPatternIdentifiers.UnderlineColorAttribute;

        public static readonly AutomationTextAttribute UnderlineStyleAttribute =
            TextPatternIdentifiers.UnderlineStyleAttribute;

        private readonly IUIAutomationTextPattern _pattern;


        protected TextPattern(AutomationElement el, IUIAutomationTextPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
            _pattern = pattern;
        }

        public SupportedTextSelection SupportedTextSelection
        {
            get
            {
                try
                {
                    return (SupportedTextSelection) _pattern.SupportedTextSelection;
                }
                catch (COMException e)
                {
                    if (Utility.ConvertException(e, out var newEx))
                        throw newEx;
                    throw;
                }
            }
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new TextPattern(el, (IUIAutomationTextPattern) pattern, cached);
        }

        public TextPatternRange[] GetSelection()
        {
            try
            {
                return TextPatternRange.Wrap(_pattern.GetSelection(), this);
            }
            catch (COMException e)
            {
                if (Utility.ConvertException(e, out var newEx))
                    throw newEx;
                throw;
            }
        }
    }

    public class TextPattern2 : TextPattern
    {
        public new static readonly AutomationPattern Pattern = TextPattern2Identifiers.Pattern;

        public static readonly AutomationTextAttribute AnnotationTypesAttribute =
            TextPattern2Identifiers.AnnotationTypesAttribute;

        public static readonly AutomationTextAttribute AnnotationObjectsAttribute =
            TextPattern2Identifiers.AnnotationObjectsAttribute;

        public static readonly AutomationTextAttribute StyleNameAttribute = TextPattern2Identifiers.StyleNameAttribute;
        public static readonly AutomationTextAttribute StyleIdAttribute = TextPattern2Identifiers.StyleIdAttribute;
        public static readonly AutomationTextAttribute LinkAttribute = TextPattern2Identifiers.LinkAttribute;
        public static readonly AutomationTextAttribute IsActiveAttribute = TextPattern2Identifiers.IsActiveAttribute;

        public static readonly AutomationTextAttribute SelectionActiveEndAttribute =
            TextPattern2Identifiers.SelectionActiveEndAttribute;

        public static readonly AutomationTextAttribute CaretPositionAttribute =
            TextPattern2Identifiers.CaretPositionAttribute;

        public static readonly AutomationTextAttribute CaretBidiModeAttribute =
            TextPattern2Identifiers.CaretBidiModeAttribute;

        private readonly IUIAutomationTextPattern2 _pattern;

        private TextPattern2(AutomationElement el, IUIAutomationTextPattern2 pattern,
            IUIAutomationTextPattern basePattern, bool cached)
            : base(el, basePattern, cached)
        {
            Debug.Assert(pattern != null);
            _pattern = pattern;
        }

        internal new static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            TextPattern2 result = null;
            if (pattern != null)
            {
                var basePattern =
                    (IUIAutomationTextPattern) el.GetRawPattern(TextPattern.Pattern, cached);
                if (basePattern != null)
                    result = new TextPattern2(el, (IUIAutomationTextPattern2) pattern,
                        basePattern, cached);
            }

            return result;
        }
    }
}