﻿using System;
using System.Diagnostics;
using System.Runtime.InteropServices;

namespace OCRTools.Common
{
    public sealed class MemoryManager
    {
        #region 内存回收

        [DllImport("kernel32.dll", EntryPoint = "SetProcessWorkingSetSize")]
        private static extern int SetProcessWorkingSetSize(IntPtr process, int minSize, int maxSize);

        /// <summary>
        ///     释放内存
        /// </summary>
        public static void ClearMemory()
        {
            try
            {
                //return;
                GC.Collect();
                GC.WaitForPendingFinalizers();
                if (Environment.OSVersion.Platform == PlatformID.Win32NT)
                    SetProcessWorkingSetSize(Process.GetCurrentProcess().Handle, -1, -1);
            }
            catch
            {
            }
        }

        #endregion
    }
}