﻿using System;
using System.Drawing;

namespace MetroFramework.Drawing
{
    internal class MetroImage
    {
        public static Image ResizeImage(Image imgToResize, Rectangle maxOffset)
        {
            int width = imgToResize.Width;
            int height = imgToResize.Height;
            float num = maxOffset.Width / (float)width;
            float num2 = maxOffset.Height / (float)height;
            float num3 = (num2 < num) ? num2 : num;
            int thumbWidth = (int)(width * num3);
            int thumbHeight = (int)(height * num3);
            return imgToResize.GetThumbnailImage(thumbWidth, thumbHeight, null, IntPtr.Zero);
        }
    }

}
