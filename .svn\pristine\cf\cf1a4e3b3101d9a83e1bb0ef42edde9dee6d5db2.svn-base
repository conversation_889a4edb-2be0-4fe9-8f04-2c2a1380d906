﻿using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Language;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Reflection;
using System.Security.Permissions;
using System.Web;
using System.Windows.Forms;

namespace OCRTools
{
    [PermissionSet(SecurityAction.Demand, Name = "FullTrust")]
    public partial class FrmGoBuy : MetroForm
    {
        private ChargeViewToUser _nowSelectedChargeType;

        private UserType _nowSelectedType;

        public UserTypeInfo NextUserType;

        public FrmGoBuy()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            this.AddContactUserBtn();
            Shown += FrmGoBuy_Shown;
        }

        private void FrmGoBuy_Shown(object sender, EventArgs e)
        {
            //ScrollMsg.ShowToWindow(this, new Point(156, 20));
            lblOldPrice.Location = new Point(btnOpenVip.Right + 5
                    , btnOpenVip.Top);
            lblOldPrice.Height = btnOpenVip.Height;
        }

        private void btnContactQQ_Click(object sender, EventArgs e)
        {
            CommonMethod.OpenKeFuQ();
        }

        private void btnOpenVip_Click(object sender, EventArgs e)
        {
            if (!Program.IsLogined())
            {
                CommonMethod.ShowHelpMsg("请登录后重试！".CurrentText());
                return;
            }

            if (!CommonString.IsOnLine)
            {
                CommonMethod.ShowHelpMsg(CommonString.StrNetWorkError.CurrentText());
                return;
            }

            if (_nowSelectedType == null)
            {
                CommonMethod.ShowHelpMsg("请选择要升级的类型！".CurrentText());
                return;
            }

            //if (NowSelectedType.Type == Program.NowUser?.UserType)
            //{
            //    CommonMethod.ShowHelpMsg(string.Format("当前已经是{0}，祝您使用愉快！", NowSelectedType.Type.ToString()));
            //    return;
            //}
            //http://t.cn/AimZvjI8
            Top -= (785 - Height) / 2;
            Height = 785;

            var url = CommonString.HostAccount?.FullUrl + "code.aspx?op=pay&remark=" +
                      HttpUtility.UrlEncode(_nowSelectedChargeType?.Name +
                                            (_nowSelectedType.Name ?? _nowSelectedType.Type.ToString()))
                      + "&account=" + Program.NowUser.Account + "&lang=" + LanguageHelper.NowLanguage;

            var html = "";
            for (int i = 0; i < 5; i++)
            {
                html = WebClientExt.GetHtml(url, 30);
                if (html.Trim().StartsWith("http"))
                {
                    break;
                }
                System.Threading.Thread.Sleep(500);
            }

            pnlMain.Controls.Clear();
            if (html.Trim().StartsWith("http"))
            {
                var dicCheck = new Dictionary<string, string>();
                dicCheck.Add("minute_show", "0分");
                CommonMethod.LoadHtml(pnlMain, new Point(0, 0), Size, html.Trim(), dicCheck);
            }
            else
            {
                CommonMethod.LoadHtml(pnlMain, new Point(0, 0), Size, CommonString.StrServerHostUrl + "pay.png");
            }

            CommonMethod.ShowHelpMsg("支付完成后，如未自动开通，请发送付款截图给客服，祝您使用愉快！".CurrentText(), 10000);
        }

        private void FrmGoBuy_Load(object sender, EventArgs e)
        {
            // LoadInfo
            CommonUser.GetUserTypes(true)?.ForEach(p =>
            {
                var radioButton = new RadioButton
                {
                    Text = (p.Name ?? p.Type.ToString()).CurrentText(),
                    Tag = p,
                    AutoSize = true,
                    Font = CommonString.GetSysNormalFont(16F),
                    TabStop = false
                };
                CommonMethod.SetStyle(radioButton, ControlStyles.Selectable, false);
                radioButton.CheckedChanged += RadioButton_CheckedChanged;
                if (Equals(p.Type, NextUserType?.Code)) radioButton.Checked = true;
                pnlUserType.Controls.Add(radioButton);
            });
            if (pnlPayType.Controls.Count <= 0 && pnlUserType.Controls.Count > 0)
            {
                (pnlUserType.Controls[0] as RadioButton).Checked = true;
            }
            var url = CommonString.HostAccount?.FullUrl + "Desc.aspx?t=" + ServerTime.DateTime.Ticks + "&lang=" + LanguageHelper.NowLanguage;
            CommonMethod.LoadHtml(pnlMain, new Point(7, 88), new Size(535, 400), url);
        }

        private void RadioButton_CheckedChanged(object sender, EventArgs e)
        {
            if (sender == null) return;
            _nowSelectedType = (sender as RadioButton)?.Tag as UserType;
            pnlPayType.Controls.Clear();
            _nowSelectedChargeType = null;
            _nowSelectedType?.UserChargeType?.ForEach(p =>
            {
                var radio = new RadioButton
                {
                    Text = p.Name.CurrentText(),
                    AutoSize = true,
                    Tag = p,
                    Font = CommonString.GetSysNormalFont(16F),
                    TabStop = false
                };
                CommonMethod.SetStyle(radio, ControlStyles.Selectable, false);
                radio.CheckedChanged += rdoByYear_CheckedChanged;
                if (!string.IsNullOrEmpty(p.Tag))
                {
                    radio.TextImageRelation = TextImageRelation.TextBeforeImage;
                    try
                    {
                        radio.Image = ImageProcessHelper.ScaleImage(radio.SetResourceImage(p.Tag), this.GetDpiScale());
                    }
                    catch { }
                }

                pnlPayType.Controls.Add(radio);
                if (p.IsDefault) radio.Checked = true;
            });
        }

        private void rdoByYear_CheckedChanged(object sender, EventArgs e)
        {
            var rdo = sender as RadioButton;
            if (rdo == null || !rdo.Checked)
                return;
            _nowSelectedChargeType = rdo.Tag as ChargeViewToUser;
            btnOpenVip.Text = _nowSelectedChargeType?.Desc ?? "-";

            var image = btnOpenVip.SetResourceImage("vip_" + _nowSelectedType.Type);
            if (image == null)
                image = btnOpenVip.SetResourceImage("qqKeFu");
            image = ImageProcessHelper.ScaleImage(image, this.GetDpiScale());
            btnOpenVip.ImageSize = image.Size;
            btnOpenVip.Image = image;

            var discountOriPrice = Math.Min(_nowSelectedChargeType.Price / _nowSelectedChargeType.OriPrice, _nowSelectedType.YearDiscount);
            //if (discountOriPrice > _nowSelectedChargeType?.OriPrice)
            {
                lblOldPrice.Font = CommonString.GetFont(CommonString.StrDefaultFontName, 13F, FontStyle.Bold, Font.Unit, false);
                lblOldPrice.ForeColor = Color.Red;
                lblOldPrice.Text = string.Format("限时{0}折优惠".CurrentText(), (discountOriPrice * 10).ToString("F0").TrimEnd('0').TrimEnd('.'));
                lblOldPrice.Font = CommonMethod.ScaleLabel(lblOldPrice.Text, CommonString.GetUserNormalFont(5F), lblOldPrice.Size);
            }
            //else
            //{
            //    lblOldPrice.Font = CommonString.GetFont(CommonString.StrDefaultFontName, 13F, FontStyle.Bold | FontStyle.Strikeout, Font.Unit, false);
            //    lblOldPrice.ForeColor = btnOpenVip.ForeColor;
            //    lblOldPrice.Text = "原价：" + _nowSelectedChargeType?.OriPrice.ToString("F2").TrimEnd('0').TrimEnd('.') + "元";
            //}
        }
    }

    [Obfuscation]
    public class UserType
    {
        [Obfuscation] public int Type { get; set; }

        [Obfuscation] public string Name { get; set; }

        [Obfuscation] public double YearDiscount { get; set; }

        [Obfuscation] public List<ChargeViewToUser> UserChargeType { get; set; }
    }

    [Obfuscation]
    public class ChargeViewToUser
    {
        [Obfuscation] public string Name { get; set; }

        [Obfuscation] public string Desc { get; set; }

        [Obfuscation] public double OriPrice { get; set; }

        [Obfuscation] public double Price { get; set; }

        [Obfuscation] public bool IsDefault { get; set; }

        [Obfuscation] public string Tag { get; set; }
    }

    [Obfuscation]
    public class UserTypeInfo
    {
        public string Name { get; set; }

        public int Code { get; set; }
    }
}