﻿using OCRTools.Language;
using System;
using System.Drawing;
using System.Windows.Forms;
using System.Windows.Forms.VisualStyles;

namespace OCRTools.UserControlEx
{
    public class RadioWithTip : RadioButton
    {
        public RadioWithTip()
        {
            TextChanged += CheckBoxWithTip_TextChanged;
        }

        private void CheckBoxWithTip_TextChanged(object sender, EventArgs e)
        {
            InitPicBox();
        }

        private string tipText;

        public string TipText { get => tipText; set { tipText = value; InitPicBox(); } }

        public ToolTip TipControl { get; set; }
        public Bitmap TipIcon { get; set; }

        private PictureBox pictureBox;

        public bool LimitMaxWidth { get; set; } = true;
        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);

            if (pictureBox.Image != null)
            {
                // 计算PictureBox的位置
                int textWidth = (int)e.Graphics.MeasureString(this.Text, this.Font).Width;
                pictureBox.Left = textWidth + 20;
                pictureBox.Visible = true;
                //pictureBox.BringToFront();
            }
            else
            {
                pictureBox.Visible = false;
            }
        }

        private void InitPicBox()
        {
            if (string.IsNullOrEmpty(tipText) || TipControl == null)
            {
                return;
            }
            if (pictureBox == null)
            {
                pictureBox = new PictureBox
                {
                    BackColor = this.BackColor,
                    Image = TipIcon ?? Properties.Resources.帮助,
                    SizeMode = PictureBoxSizeMode.CenterImage,
                    TabStop = false,
                    Padding = new Padding(-3, 0, 0, 0),
                    Visible = false
                };
                pictureBox.Size = new Size(this.Height, this.Height);
                Controls.Add(pictureBox);
            }
            AutoSize = false;
            pictureBox.Top = (int)((this.ClientRectangle.Height - pictureBox.Height) / 2 * CommonTheme.DpiScale);
            Width = TextRenderer.MeasureText(Text, Font).Width
                + CheckBoxRenderer.GetGlyphSize(Graphics.FromHwnd(IntPtr.Zero), CheckBoxState.UncheckedNormal).Width + pictureBox.Width;
            if (LimitMaxWidth && Parent != null)
            {
                var maxWidth = (Math.Max(Parent.PreferredSize.Width, this.Parent.Width) - 22 * 2) / 2;
                if (Width > maxWidth)
                {
                    this.Width = maxWidth;
                }
            }
            this.BringToFront();
            pictureBox.BringToFront();

            if (TipControl != null)
            {
                TipControl.SetToolTip(this, Text);
                TipControl.SetToolTip(pictureBox, "【" + Text + "】\n" + TipText.CurrentText());
            }
        }
    }
}
