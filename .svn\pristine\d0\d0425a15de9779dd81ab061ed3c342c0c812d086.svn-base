using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace OCRTools
{
    /// <summary>
    /// 支持精确文字选择的图片查看器
    /// </summary>
    public class TextSelectableImageViewer : PanelPictureView
    {
        private readonly Color SELECTION_FILL_COLOR = Color.FromArgb(100, 0, 120, 215); // 类似PDF的选择颜色

        // 数据结构
        private List<TextLineInfo> textLines = new List<TextLineInfo>();
        private List<TextCellInfo> textCells = new List<TextCellInfo>();

        // 文字排列方向判断
        private bool isVerticalLayout = false;

        // 选择相关字段
        private bool isDragging = false;
        private Point dragStartPoint;
        private int selectionStartLineIndex = -1;
        private int selectionEndLineIndex = -1;
        private int selectionStartCharIndex = -1;
        private int selectionEndCharIndex = -1;

        // UI组件
        public RichTextBox TextRichTextBox { get; set; }

        // 事件
        public event EventHandler<TextSelectionEventArgs> TextSelectionChanged;

        /// <summary>
        /// 文字行信息类
        /// </summary>
        private class TextLineInfo
        {
            public List<TextCellInfo> Cells { get; set; } = new List<TextCellInfo>();
            public Rectangle LineBounds { get; set; }
            public string LineText { get; set; }
            public int LineIndex { get; set; }
            public bool IsVertical { get; set; }
        }

        public TextSelectableImageViewer()
        {
            // 禁用原有的提示功能，我们自己处理
            IsShowTip = false;

            // 重新绑定鼠标事件
            MouseDown += OnMouseDown;
            MouseMove += OnMouseMove;
            MouseUp += OnMouseUp;
            Paint += OnCustomPaint;
            MouseLeave += OnMouseLeave;
        }

        /// <summary>
        /// 绑定图片和文字区域
        /// </summary>
        public void BindImageAndTextRegions(Image image, List<TextCellInfo> regions)
        {
            textCells = regions ?? new List<TextCellInfo>();

            // 检测文字排列方向
            DetectTextLayout();

            // 构建行级数据
            BuildLineData();

            // 更新文字列表
            UpdateTextList();

            // 清除选择
            ClearSelection();

            // 设置图片
            Image = image;

            // 标记为绑定模式
            IsBindImageMode = true;

            Invalidate();
        }

        /// <summary>
        /// 检测每个文字块的排列方向（支持混排）
        /// </summary>
        private void DetectTextLayout()
        {
            if (textCells == null || textCells.Count == 0)
            {
                isVerticalLayout = false;
                return;
            }

            System.Diagnostics.Debug.WriteLine("\n=== 单个文字块方向检测 ===");

            // 为每个cell检测其方向
            foreach (var cell in textCells)
            {
                if (cell?.location == null || string.IsNullOrEmpty(cell.words)) continue;

                double width = cell.location.width;
                double height = cell.location.height;

                if (width <= 0 || height <= 0) continue;

                // 判断单个cell的方向
                bool isVerticalCell = DetectCellDirection(cell);

                System.Diagnostics.Debug.WriteLine($"文字: '{cell.words}', 位置: ({cell.location.left:F1},{cell.location.top:F1}), " +
                    $"大小: {width:F1}x{height:F1}, 方向: {(isVerticalCell ? "竖排" : "横排")}");
            }

            // 整体布局判断（用于兼容现有逻辑）
            DetectOverallLayout();
        }

        /// <summary>
        /// 检测单个文字块的方向
        /// </summary>
        private bool DetectCellDirection(TextCellInfo cell)
        {
            if (cell?.location == null || string.IsNullOrEmpty(cell.words)) return false;

            double width = cell.location.width;
            double height = cell.location.height;
            double aspectRatio = width / height;

            // 竖排判断条件：
            // 1. 高度明显大于宽度（高宽比 > 2.0）
            // 2. 或者高度 >= 宽度且字符数较少（<=3）
            // 3. 或者是单字符且接近正方形

            bool condition1 = height / width > 2.0; // 明显的竖直形状
            bool condition2 = height >= width && cell.words.Length <= 3; // 高>=宽且字符少
            bool condition3 = cell.words.Length == 1 && aspectRatio > 0.5 && aspectRatio < 2.0; // 单字符且接近正方形

            return condition1 || condition2 || condition3;
        }

        /// <summary>
        /// 检测整体布局（用于兼容现有逻辑）
        /// </summary>
        private void DetectOverallLayout()
        {
            if (textCells == null || textCells.Count == 0)
            {
                isVerticalLayout = false;
                return;
            }

            int verticalCells = 0;
            int totalCells = 0;

            foreach (var cell in textCells)
            {
                if (cell?.location == null || string.IsNullOrEmpty(cell.words)) continue;

                totalCells++;
                if (DetectCellDirection(cell))
                {
                    verticalCells++;
                }
            }

            // 如果超过60%的cell是竖排，则认为整体是竖排
            isVerticalLayout = totalCells > 0 && (double)verticalCells / totalCells > 0.6;

            System.Diagnostics.Debug.WriteLine($"\n整体布局判断: 竖排cell={verticalCells}/{totalCells}, 整体判断={isVerticalLayout}");
        }

        /// <summary>
        /// 构建行级数据（支持混排）
        /// </summary>
        private void BuildLineData()
        {
            textLines.Clear();

            if (textCells.Count == 0) return;

            // 使用智能行构建，支持混排
            BuildSmartLineData();
        }

        /// <summary>
        /// 智能构建行数据，支持混排
        /// </summary>
        private void BuildSmartLineData()
        {
            var sortedCells = textCells
                .Where(cell => cell?.location != null && !string.IsNullOrEmpty(cell.words))
                .OrderBy(cell => cell.location.top)
                .ThenBy(cell => cell.location.left)
                .ToList();

            if (sortedCells.Count == 0) return;

            var lines = new List<TextLineInfo>();
            var usedCells = new HashSet<TextCellInfo>();

            // 为每个未使用的cell寻找同行的其他cells
            foreach (var cell in sortedCells)
            {
                if (usedCells.Contains(cell)) continue;

                var line = new TextLineInfo { LineIndex = lines.Count, IsVertical = isVerticalLayout };
                var lineCells = FindCellsInSameLine(cell, sortedCells, usedCells);

                line.Cells.AddRange(lineCells);
                foreach (var lineCell in lineCells)
                {
                    usedCells.Add(lineCell);
                }

                FinalizeLine(line);
                lines.Add(line);
            }

            textLines.AddRange(lines);
        }

        /// <summary>
        /// 查找与指定cell在同一行的其他cells
        /// </summary>
        private List<TextCellInfo> FindCellsInSameLine(TextCellInfo seedCell, List<TextCellInfo> allCells, HashSet<TextCellInfo> usedCells)
        {
            var result = new List<TextCellInfo> { seedCell };
            var seedRect = seedCell.location.Rectangle;
            var lineThreshold = Math.Max(10, (int)(seedRect.Height * 0.5)); // 动态阈值

            foreach (var cell in allCells)
            {
                if (usedCells.Contains(cell) || cell == seedCell) continue;

                var cellRect = cell.location.Rectangle;

                // 判断是否在同一行：垂直位置接近
                bool isInSameLine = Math.Abs(cellRect.Y - seedRect.Y) <= lineThreshold ||
                                   Math.Abs(cellRect.Bottom - seedRect.Bottom) <= lineThreshold ||
                                   (cellRect.Y <= seedRect.Bottom && cellRect.Bottom >= seedRect.Y); // 有垂直重叠

                if (isInSameLine)
                {
                    result.Add(cell);
                }
            }

            return result;
        }

        /// <summary>
        /// 构建横排文字的行数据
        /// </summary>
        private void BuildHorizontalLineData()
        {
            // 按Y坐标排序，然后按行分组
            var sortedCells = textCells
                .Where(cell => cell?.location != null && !string.IsNullOrEmpty(cell.words))
                .OrderBy(cell => cell.location.top)
                .ThenBy(cell => cell.location.left)
                .ToList();

            var currentLine = new TextLineInfo { LineIndex = 0, IsVertical = false };
            var lineThreshold = 10; // 行间距阈值

            foreach (var cell in sortedCells)
            {
                var cellTop = cell.location.top;

                // 判断是否需要新建行
                if (currentLine.Cells.Count > 0)
                {
                    var lastCellTop = currentLine.Cells.Last().location.top;
                    if (Math.Abs(cellTop - lastCellTop) > lineThreshold)
                    {
                        // 完成当前行
                        FinalizeLine(currentLine);
                        textLines.Add(currentLine);

                        // 开始新行
                        currentLine = new TextLineInfo { LineIndex = textLines.Count, IsVertical = false };
                    }
                }

                currentLine.Cells.Add(cell);
            }

            // 添加最后一行
            if (currentLine.Cells.Count > 0)
            {
                FinalizeLine(currentLine);
                textLines.Add(currentLine);
            }
        }

        /// <summary>
        /// 构建竖排文字的行数据（实际上是列数据）
        /// </summary>
        private void BuildVerticalLineData()
        {
            // 对于竖排文字，我们需要按列来组织"行"
            var sortedCells = textCells
                .Where(cell => cell?.location != null && !string.IsNullOrEmpty(cell.words))
                .ToList();

            if (sortedCells.Count == 0) return;

            // 按X坐标分组形成列
            var columnThreshold = 20; // 列间距阈值
            var columns = new List<List<TextCellInfo>>();

            // 按X坐标排序（从左到右）
            var cellsByX = sortedCells.OrderBy(cell => cell.location.left).ToList();

            System.Diagnostics.Debug.WriteLine("=== 竖排列构建调试 ===");
            foreach (var cell in cellsByX)
            {
                System.Diagnostics.Debug.WriteLine($"Cell: '{cell.words}', X={cell.location.left}");
            }

            var currentColumn = new List<TextCellInfo>();
            foreach (var cell in cellsByX)
            {
                var cellLeft = cell.location.left;

                // 判断是否需要新建列
                if (currentColumn.Count > 0)
                {
                    var lastCellLeft = currentColumn.Last().location.left;
                    if (Math.Abs(cellLeft - lastCellLeft) > columnThreshold)
                    {
                        // 完成当前列
                        if (currentColumn.Count > 0)
                        {
                            columns.Add(currentColumn);
                            System.Diagnostics.Debug.WriteLine($"完成列 {columns.Count - 1}: {string.Join(",", currentColumn.Select(c => c.words))}");
                        }

                        // 开始新列
                        currentColumn = new List<TextCellInfo>();
                    }
                }

                currentColumn.Add(cell);
            }

            // 添加最后一列
            if (currentColumn.Count > 0)
            {
                columns.Add(currentColumn);
                System.Diagnostics.Debug.WriteLine($"完成列 {columns.Count - 1}: {string.Join(",", currentColumn.Select(c => c.words))}");
            }

            System.Diagnostics.Debug.WriteLine($"反转前列数: {columns.Count}");

            // 注意：不要反转列顺序，保持从左到右的自然顺序
            // 这样列索引就与X坐标位置一致，避免拖动选择时的混乱
            // columns.Reverse(); // 暂时注释掉反转逻辑

            System.Diagnostics.Debug.WriteLine("列顺序（保持从左到右）:");
            for (int i = 0; i < columns.Count; i++)
            {
                var columnText = string.Join(",", columns[i].Select(c => c.words));
                var avgX = columns[i].Average(c => c.location.left);
                System.Diagnostics.Debug.WriteLine($"列 {i}: {columnText}, 平均X={avgX:F1}");
            }

            // 将每列转换为"行"，每列内的文字按从上到下排序
            for (int colIndex = 0; colIndex < columns.Count; colIndex++)
            {
                var column = columns[colIndex];

                // 列内按Y坐标排序
                var sortedColumnCells = column.OrderBy(cell => cell.location.top).ToList();

                var line = new TextLineInfo { LineIndex = colIndex, IsVertical = true };
                line.Cells.AddRange(sortedColumnCells);

                FinalizeLine(line);
                textLines.Add(line);
            }
        }

        /// <summary>
        /// 完成行数据构建
        /// </summary>
        private void FinalizeLine(TextLineInfo line)
        {
            if (line.Cells.Count == 0) return;

            // 计算行边界
            var minX = line.Cells.Min(c => (int)c.location.left);
            var maxX = line.Cells.Max(c => (int)(c.location.left + c.location.width));
            var minY = line.Cells.Min(c => (int)c.location.top);
            var maxY = line.Cells.Max(c => (int)(c.location.top + c.location.height));

            line.LineBounds = new Rectangle(minX, minY, maxX - minX, maxY - minY);

            if (isVerticalLayout)
            {
                FinalizeVerticalLine(line);
            }
            else
            {
                FinalizeHorizontalLine(line);
            }
        }

        /// <summary>
        /// 完成横排行数据构建
        /// </summary>
        private void FinalizeHorizontalLine(TextLineInfo line)
        {
            // 按X坐标排序cells，确保文字顺序正确
            var sortedCells = line.Cells.OrderBy(c => c.location.left).ToList();

            // 构建行文字，简化处理避免过多空格
            var lineTextBuilder = new StringBuilder();
            for (int i = 0; i < sortedCells.Count; i++)
            {
                var cell = sortedCells[i];
                lineTextBuilder.Append(cell.words);

                // 如果不是最后一个cell，且下一个cell与当前cell有较大间距，添加单个空格
                if (i < sortedCells.Count - 1)
                {
                    var currentCellRight = cell.location.left + cell.location.width;
                    var nextCellLeft = sortedCells[i + 1].location.left;
                    var gap = nextCellLeft - currentCellRight;

                    // 更严格的间距判断：只有当间距大于平均字符宽度的2倍时才添加空格
                    var avgCharWidth = cell.location.width / Math.Max(1, cell.words.Length);
                    if (gap > avgCharWidth * 2.0)
                    {
                        lineTextBuilder.Append(" ");
                    }
                }
            }

            line.LineText = lineTextBuilder.ToString();
        }

        /// <summary>
        /// 完成竖排行数据构建（实际上是列数据）
        /// </summary>
        private void FinalizeVerticalLine(TextLineInfo line)
        {
            // 对于竖排文字，cells已经按Y坐标排序了
            var sortedCells = line.Cells.OrderBy(c => c.location.top).ToList();

            // 构建列文字
            var lineTextBuilder = new StringBuilder();
            for (int i = 0; i < sortedCells.Count; i++)
            {
                var cell = sortedCells[i];
                lineTextBuilder.Append(cell.words);

                // 对于竖排文字，通常不需要在字符间添加空格
                // 但如果垂直间距很大，可以考虑添加换行或空格
                if (i < sortedCells.Count - 1)
                {
                    var currentCellBottom = cell.location.top + cell.location.height;
                    var nextCellTop = sortedCells[i + 1].location.top;
                    var gap = nextCellTop - currentCellBottom;

                    // 如果垂直间距很大，添加空格表示间隔
                    var avgCharHeight = cell.location.height / Math.Max(1, cell.words.Length);
                    if (gap > avgCharHeight * 1.5)
                    {
                        lineTextBuilder.Append(" ");
                    }
                }
            }

            line.LineText = lineTextBuilder.ToString();
        }

        /// <summary>
        /// 更新文字显示
        /// </summary>
        private void UpdateTextList()
        {
            if (TextRichTextBox == null) return;

            TextRichTextBox.Clear();
            var allText = new StringBuilder();

            // 无论横排还是竖排，都按行显示
            foreach (var line in textLines)
            {
                allText.AppendLine(line.LineText);
            }

            TextRichTextBox.Text = allText.ToString();

            // 设置默认样式
            TextRichTextBox.SelectAll();
            TextRichTextBox.SelectionBackColor = TextRichTextBox.BackColor;
            TextRichTextBox.SelectionColor = TextRichTextBox.ForeColor;
            TextRichTextBox.Select(0, 0);
        }

        private void OnMouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left && textLines.Count > 0)
            {
                isDragging = true;
                dragStartPoint = e.Location;

                // 转换鼠标坐标到图片坐标
                var imagePoint = GetImagePoint(e.Location);
                var lineCharPos = GetLineAndCharAtPoint(imagePoint);

                if (lineCharPos.LineIndex >= 0)
                {
                    // 开始新的选择
                    selectionStartLineIndex = lineCharPos.LineIndex;
                    selectionEndLineIndex = lineCharPos.LineIndex;
                    selectionStartCharIndex = lineCharPos.CharIndex;
                    selectionEndCharIndex = lineCharPos.CharIndex;
                }
                else
                {
                    // 点击空白区域，清除选择
                    ClearSelection();
                }

                UpdateSelection();
            }
        }

        private void OnMouseMove(object sender, MouseEventArgs e)
        {
            if (isDragging && e.Button == MouseButtons.Left && selectionStartLineIndex >= 0)
            {
                // 转换鼠标坐标到图片坐标
                var imagePoint = GetImagePoint(e.Location);
                var lineCharPos = GetLineAndCharAtPointSmart(imagePoint);

                if (lineCharPos.LineIndex >= 0)
                {
                    // 更新选择结束位置
                    selectionEndLineIndex = lineCharPos.LineIndex;
                    selectionEndCharIndex = lineCharPos.CharIndex;
                    UpdateSelection();
                }
                else
                {
                    // 如果智能选择也没有找到合适的位置，保持当前选择不变
                    // 这样可以避免选择突然消失的问题
                }
            }
            else
            {
                // 检查鼠标是否在可选择区域，更新光标
                UpdateCursor(e.Location);
            }
        }

        private void OnMouseLeave(object sender, EventArgs e)
        {
            // 鼠标离开时恢复默认光标
            Cursor = Cursors.Default;
        }

        /// <summary>
        /// 根据鼠标位置更新光标
        /// </summary>
        private void UpdateCursor(Point mouseLocation)
        {
            if (textLines.Count == 0)
            {
                Cursor = Cursors.Default;
                return;
            }

            // 转换鼠标坐标到图片坐标
            var imagePoint = GetImagePoint(mouseLocation);
            var lineCharPos = GetLineAndCharAtPoint(imagePoint);

            // 如果鼠标在文字区域，显示文本光标
            if (lineCharPos.LineIndex >= 0)
            {
                Cursor = Cursors.IBeam;
            }
            else
            {
                Cursor = Cursors.Default;
            }
        }

        private void OnMouseUp(object sender, MouseEventArgs e)
        {
            if (isDragging)
            {
                isDragging = false;
                Invalidate();
            }
        }

        private void OnCustomPaint(object sender, PaintEventArgs e)
        {
            if (!IsBindImageMode || textLines.Count == 0) return;

            var g = e.Graphics;

            // 绘制选中的文字区域
            if (HasSelection())
            {
                using (var selectedBrush = new SolidBrush(SELECTION_FILL_COLOR))
                {
                    var selectedRects = GetSelectedRectangles();
                    foreach (var rect in selectedRects)
                    {
                        var displayRect = GetDisplayRectangle(rect);
                        if (!displayRect.IsEmpty)
                        {
                            g.FillRectangle(selectedBrush, displayRect);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 获取鼠标位置对应的图片坐标
        /// </summary>
        private Point GetImagePoint(Point controlPoint)
        {
            // 考虑滚动位置和缩放因子
            var scrollOffset = AutoScrollPosition;
            var imagePoint = new Point(
                (int)((controlPoint.X - scrollOffset.X) / ZoomFactor),
                (int)((controlPoint.Y - scrollOffset.Y) / ZoomFactor)
            );
            return imagePoint;
        }

        /// <summary>
        /// 获取矩形在控件中的显示矩形
        /// </summary>
        private Rectangle GetDisplayRectangle(Rectangle imageRect)
        {
            var scrollOffset = AutoScrollPosition;

            return new Rectangle(
                (int)(imageRect.X * ZoomFactor) + scrollOffset.X,
                (int)(imageRect.Y * ZoomFactor) + scrollOffset.Y,
                (int)(imageRect.Width * ZoomFactor),
                (int)(imageRect.Height * ZoomFactor)
            );
        }

        /// <summary>
        /// 行和字符位置结果类
        /// </summary>
        private class LineCharPosition
        {
            public int LineIndex { get; set; }
            public int CharIndex { get; set; }

            public LineCharPosition(int lineIndex, int charIndex)
            {
                LineIndex = lineIndex;
                CharIndex = charIndex;
            }
        }

        /// <summary>
        /// 获取指定点位置的行和字符位置
        /// </summary>
        private LineCharPosition GetLineAndCharAtPoint(Point imagePoint)
        {
            for (int lineIndex = 0; lineIndex < textLines.Count; lineIndex++)
            {
                var line = textLines[lineIndex];
                if (line.LineBounds.Contains(imagePoint))
                {
                    // 在行内查找字符位置
                    var charIndex = GetCharIndexInLine(line, imagePoint);
                    return new LineCharPosition(lineIndex, charIndex);
                }
            }
            return new LineCharPosition(-1, -1);
        }

        /// <summary>
        /// 获取指定点位置的行和字符位置（智能处理空白区域）
        /// </summary>
        private LineCharPosition GetLineAndCharAtPointSmart(Point imagePoint)
        {
            if (textLines.Count == 0)
                return new LineCharPosition(-1, -1);

            System.Diagnostics.Debug.WriteLine($"=== 点击位置分析 ===");
            System.Diagnostics.Debug.WriteLine($"点击坐标: ({imagePoint.X}, {imagePoint.Y})");
            System.Diagnostics.Debug.WriteLine($"布局方向: {(isVerticalLayout ? "竖排" : "横排")}");

            // 首先尝试精确匹配
            for (int lineIndex = 0; lineIndex < textLines.Count; lineIndex++)
            {
                var line = textLines[lineIndex];
                System.Diagnostics.Debug.WriteLine($"检查行 {lineIndex}: 边界={line.LineBounds}, 文字='{line.LineText.Substring(0, Math.Min(10, line.LineText.Length))}...'");

                if (line.LineBounds.Contains(imagePoint))
                {
                    var charIndex = GetCharIndexInLine(line, imagePoint);
                    System.Diagnostics.Debug.WriteLine($"精确匹配: 行={lineIndex}, 字符={charIndex}");
                    return new LineCharPosition(lineIndex, charIndex);
                }
            }

            // 智能推断：只处理合理范围内的空白区域
            // 1. 检查是否在某行的水平延伸范围内（左右扩展）
            for (int lineIndex = 0; lineIndex < textLines.Count; lineIndex++)
            {
                var line = textLines[lineIndex];
                var lineBounds = line.LineBounds;

                // 扩展行的水平范围，允许在行左右一定距离内选择
                var expandedBounds = new Rectangle(
                    lineBounds.X - 50, // 左侧扩展50像素
                    lineBounds.Y,
                    lineBounds.Width + 100, // 左右各扩展50像素
                    lineBounds.Height
                );

                if (expandedBounds.Contains(imagePoint))
                {
                    int charIndex;
                    if (imagePoint.X < lineBounds.Left)
                    {
                        // 鼠标在行左侧扩展区域，选择行首
                        charIndex = 0;
                    }
                    else if (imagePoint.X > lineBounds.Right)
                    {
                        // 鼠标在行右侧扩展区域，选择行尾
                        charIndex = Math.Max(0, line.LineText.Length - 1);
                    }
                    else
                    {
                        // 鼠标在行的水平范围内，计算最接近的字符位置
                        charIndex = GetCharIndexInLine(line, imagePoint);
                    }

                    return new LineCharPosition(lineIndex, charIndex);
                }
            }

            // 2. 检查是否在行间的垂直空白区域（用于跨行选择）
            for (int lineIndex = 0; lineIndex < textLines.Count - 1; lineIndex++)
            {
                var currentLine = textLines[lineIndex];
                var nextLine = textLines[lineIndex + 1];

                // 检查是否在两行之间的空白区域
                if (imagePoint.Y > currentLine.LineBounds.Bottom &&
                    imagePoint.Y < nextLine.LineBounds.Top)
                {
                    // 根据鼠标的垂直位置决定选择哪一行
                    var currentLineDistance = imagePoint.Y - currentLine.LineBounds.Bottom;
                    var nextLineDistance = nextLine.LineBounds.Top - imagePoint.Y;

                    var selectedLine = currentLineDistance <= nextLineDistance ? currentLine : nextLine;
                    var selectedLineIndex = currentLineDistance <= nextLineDistance ? lineIndex : lineIndex + 1;

                    // 根据水平位置确定字符位置
                    int charIndex;
                    if (imagePoint.X < selectedLine.LineBounds.Left)
                    {
                        charIndex = 0;
                    }
                    else if (imagePoint.X > selectedLine.LineBounds.Right)
                    {
                        charIndex = Math.Max(0, selectedLine.LineText.Length - 1);
                    }
                    else
                    {
                        // 在行的水平范围内，选择最接近的字符
                        var adjustedPoint = new Point(imagePoint.X, selectedLine.LineBounds.Y + selectedLine.LineBounds.Height / 2);
                        charIndex = GetCharIndexInLine(selectedLine, adjustedPoint);
                    }

                    return new LineCharPosition(selectedLineIndex, charIndex);
                }
            }

            // 如果都不匹配，返回无效位置
            return new LineCharPosition(-1, -1);
        }

        /// <summary>
        /// 在行内获取字符索引（支持混排）
        /// </summary>
        private int GetCharIndexInLine(TextLineInfo line, Point imagePoint)
        {
            int totalCharIndex = 0;

            // 根据整体行方向决定cell的排序方式
            var sortedCells = line.IsVertical
                ? line.Cells.OrderBy(c => c.location.top).ToList()
                : line.Cells.OrderBy(c => c.location.left).ToList();

            for (int i = 0; i < sortedCells.Count; i++)
            {
                var cell = sortedCells[i];
                var cellRect = cell.location.Rectangle;

                if (cellRect.Contains(imagePoint))
                {
                    // 根据单个cell的方向计算在cell内的字符位置
                    int charIndexInCell;
                    bool isCellVertical = DetectCellDirection(cell);

                    if (isCellVertical)
                    {
                        // 竖排：从上到下选择
                        var relativeY = imagePoint.Y - cellRect.Y;
                        var charHeight = (float)cellRect.Height / cell.words.Length;
                        charIndexInCell = Math.Min((int)(relativeY / charHeight), cell.words.Length - 1);
                    }
                    else
                    {
                        // 横排：从左到右选择
                        var relativeX = imagePoint.X - cellRect.X;
                        var charWidth = (float)cellRect.Width / cell.words.Length;
                        charIndexInCell = Math.Min((int)(relativeX / charWidth), cell.words.Length - 1);
                    }

                    return totalCharIndex + Math.Max(0, charIndexInCell);
                }

                // 累加当前cell的字符数
                totalCharIndex += cell.words.Length;

                // 如果不是最后一个cell，检查是否需要添加空格的字符索引
                if (i < sortedCells.Count - 1)
                {
                    int gap;
                    bool isInGap;
                    float avgCharSize;

                    // 根据整体行方向判断间隙方向（保持原有逻辑）
                    if (line.IsVertical)
                    {
                        var currentCellBottom = (int)(cell.location.top + cell.location.height);
                        var nextCellTop = (int)sortedCells[i + 1].location.top;
                        gap = nextCellTop - currentCellBottom;
                        avgCharSize = (float)(cell.location.height / Math.Max(1, cell.words.Length));
                        isInGap = imagePoint.Y >= currentCellBottom && imagePoint.Y <= nextCellTop;
                    }
                    else
                    {
                        var currentCellRight = (int)(cell.location.left + cell.location.width);
                        var nextCellLeft = (int)sortedCells[i + 1].location.left;
                        gap = nextCellLeft - currentCellRight;
                        avgCharSize = (float)(cell.location.width / Math.Max(1, cell.words.Length));
                        isInGap = imagePoint.X >= currentCellRight && imagePoint.X <= nextCellLeft;
                    }

                    // 如果间距大于字符尺寸的一半，说明LineText中有空格
                    if (gap > avgCharSize * 0.5)
                    {
                        // 检查点击是否在间隙中
                        if (isInGap)
                        {
                            return totalCharIndex; // 返回空格的位置
                        }
                        totalCharIndex += 1; // 空格占一个字符位置
                    }
                }
            }

            // 如果没有精确命中，返回最接近的位置
            return Math.Max(0, Math.Min(totalCharIndex - 1, line.LineText.Length - 1));
        }

        /// <summary>
        /// 检查是否有选择
        /// </summary>
        private bool HasSelection()
        {
            return selectionStartLineIndex >= 0 && selectionEndLineIndex >= 0;
        }

        /// <summary>
        /// 获取选中的矩形区域
        /// </summary>
        private List<Rectangle> GetSelectedRectangles()
        {
            var rects = new List<Rectangle>();
            if (!HasSelection()) return rects;

            // 正确处理双向选择
            int startLine, endLine, startChar, endChar;

            if (selectionStartLineIndex < selectionEndLineIndex ||
                (selectionStartLineIndex == selectionEndLineIndex && selectionStartCharIndex <= selectionEndCharIndex))
            {
                // 正向选择
                startLine = selectionStartLineIndex;
                endLine = selectionEndLineIndex;
                startChar = selectionStartCharIndex;
                endChar = selectionEndCharIndex;
            }
            else
            {
                // 反向选择
                startLine = selectionEndLineIndex;
                endLine = selectionStartLineIndex;
                startChar = selectionEndCharIndex;
                endChar = selectionStartCharIndex;
            }

            for (int lineIndex = startLine; lineIndex <= endLine && lineIndex < textLines.Count; lineIndex++)
            {
                var line = textLines[lineIndex];
                var lineStartChar = lineIndex == startLine ? startChar : 0;
                var lineEndChar = lineIndex == endLine ? endChar : line.LineText.Length - 1;

                var lineRects = GetCharacterRectsInLine(line, lineStartChar, lineEndChar);
                rects.AddRange(lineRects);
            }

            return rects;
        }

        /// <summary>
        /// 获取行内指定字符范围的矩形
        /// </summary>
        private List<Rectangle> GetCharacterRectsInLine(TextLineInfo line, int startChar, int endChar)
        {
            var rects = new List<Rectangle>();
            if (startChar < 0 || endChar < 0 || startChar > endChar) return rects;

            int currentCharIndex = 0;

            // 根据行的方向决定cell的排序方式
            var sortedCells = line.IsVertical
                ? line.Cells.OrderBy(c => c.location.top).ToList()
                : line.Cells.OrderBy(c => c.location.left).ToList();

            for (int i = 0; i < sortedCells.Count; i++)
            {
                var cell = sortedCells[i];
                var cellStartChar = currentCharIndex;
                var cellEndChar = currentCharIndex + cell.words.Length - 1;

                // 检查cell是否与选择范围有交集
                if (cellEndChar >= startChar && cellStartChar <= endChar)
                {
                    var selStartInCell = Math.Max(0, startChar - cellStartChar);
                    var selEndInCell = Math.Min(cell.words.Length - 1, endChar - cellStartChar);

                    if (selStartInCell <= selEndInCell)
                    {
                        var cellRect = cell.location.Rectangle;

                        // 如果整个cell都被选中，直接使用cell的完整矩形
                        if (selStartInCell == 0 && selEndInCell == cell.words.Length - 1)
                        {
                            rects.Add(cellRect);
                        }
                        else
                        {
                            // 部分选中时根据cell方向计算字符级别的矩形
                            Rectangle selectionRect = CalculateCharacterSelectionRect(cell, selStartInCell, selEndInCell);
                            rects.Add(selectionRect);
                        }
                    }
                }

                // 累加当前cell的字符数
                currentCharIndex += cell.words.Length;

                // 检查cell之间的空格是否需要被选中
                if (i < sortedCells.Count - 1)
                {
                    Rectangle spaceRect = CalculateSpaceRect(cell, sortedCells[i + 1], line.IsVertical, currentCharIndex, startChar, endChar);
                    if (!spaceRect.IsEmpty)
                    {
                        rects.Add(spaceRect);
                        currentCharIndex += 1; // 空格占一个字符位置
                    }
                }
            }

            return rects;
        }

        /// <summary>
        /// 根据cell方向计算字符选择矩形
        /// </summary>
        private Rectangle CalculateCharacterSelectionRect(TextCellInfo cell, int selStartInCell, int selEndInCell)
        {
            var cellRect = cell.location.Rectangle;
            bool isCellVertical = DetectCellDirection(cell);

            if (isCellVertical)
            {
                // 竖排文字：按高度分割字符
                var charHeight = (float)cellRect.Height / cell.words.Length;

                return new Rectangle(
                    cellRect.X,
                    (int)(cellRect.Y + selStartInCell * charHeight),
                    cellRect.Width,
                    (int)Math.Ceiling((selEndInCell - selStartInCell + 1) * charHeight)
                );
            }
            else
            {
                // 横排文字：按宽度分割字符
                var charWidth = (float)cellRect.Width / cell.words.Length;

                return new Rectangle(
                    (int)(cellRect.X + selStartInCell * charWidth),
                    cellRect.Y,
                    (int)Math.Ceiling((selEndInCell - selStartInCell + 1) * charWidth),
                    cellRect.Height
                );
            }
        }

        /// <summary>
        /// 计算cell之间空格的矩形区域
        /// </summary>
        private Rectangle CalculateSpaceRect(TextCellInfo currentCell, TextCellInfo nextCell, bool isVerticalLine,
            int currentCharIndex, int startChar, int endChar)
        {
            // 检查空格是否在选择范围内
            if (currentCharIndex < startChar || currentCharIndex > endChar)
                return Rectangle.Empty;

            bool isCurrentCellVertical = DetectCellDirection(currentCell);

            if (isCurrentCellVertical)
            {
                // 竖排文字：检查垂直间距
                var currentCellBottom = currentCell.location.top + currentCell.location.height;
                var nextCellTop = nextCell.location.top;
                var gap = nextCellTop - currentCellBottom;

                // 如果间距大于字符高度的一半，说明LineText中有空格
                var avgCharHeight = currentCell.location.height / Math.Max(1, currentCell.words.Length);
                if (gap > avgCharHeight * 0.5)
                {
                    return new Rectangle(
                        currentCell.location.Rectangle.X,
                        (int)currentCellBottom,
                        currentCell.location.Rectangle.Width,
                        (int)gap
                    );
                }
            }
            else
            {
                // 横排文字：检查水平间距
                var currentCellRight = currentCell.location.left + currentCell.location.width;
                var nextCellLeft = nextCell.location.left;
                var gap = nextCellLeft - currentCellRight;

                // 如果间距大于字符宽度的一半，说明LineText中有空格
                var avgCharWidth = currentCell.location.width / Math.Max(1, currentCell.words.Length);
                if (gap > avgCharWidth * 0.5)
                {
                    return new Rectangle(
                        (int)currentCellRight,
                        currentCell.location.Rectangle.Y,
                        (int)gap,
                        currentCell.location.Rectangle.Height
                    );
                }
            }

            return Rectangle.Empty;
        }

        /// <summary>
        /// 获取选中的文字内容
        /// </summary>
        private string GetSelectedText()
        {
            if (!HasSelection()) return string.Empty;

            // 正确处理双向选择
            int startLine, endLine, startChar, endChar;

            if (selectionStartLineIndex < selectionEndLineIndex ||
                (selectionStartLineIndex == selectionEndLineIndex && selectionStartCharIndex <= selectionEndCharIndex))
            {
                // 正向选择
                startLine = selectionStartLineIndex;
                endLine = selectionEndLineIndex;
                startChar = selectionStartCharIndex;
                endChar = selectionEndCharIndex;
            }
            else
            {
                // 反向选择
                startLine = selectionEndLineIndex;
                endLine = selectionStartLineIndex;
                startChar = selectionEndCharIndex;
                endChar = selectionStartCharIndex;
            }

            var selectedText = new StringBuilder();

            for (int lineIndex = startLine; lineIndex <= endLine && lineIndex < textLines.Count; lineIndex++)
            {
                var line = textLines[lineIndex];
                var lineStartChar = lineIndex == startLine ? startChar : 0;
                var lineEndChar = lineIndex == endLine ? endChar : line.LineText.Length - 1;

                if (lineStartChar >= 0 && lineEndChar >= lineStartChar && lineStartChar < line.LineText.Length)
                {
                    var actualEndChar = Math.Min(lineEndChar, line.LineText.Length - 1);
                    var lineSelection = line.LineText.Substring(lineStartChar, actualEndChar - lineStartChar + 1);
                    selectedText.Append(lineSelection);

                    if (lineIndex < endLine)
                    {
                        if (isVerticalLayout)
                        {
                            // 竖排文字：列与列之间用空格分隔
                            selectedText.Append(" ");
                        }
                        else
                        {
                            // 横排文字：行与行之间用换行分隔
                            selectedText.AppendLine();
                        }
                    }
                }
            }

            return selectedText.ToString();
        }

        /// <summary>
        /// 更新选择状态
        /// </summary>
        private void UpdateSelection()
        {
            // 同步文字列表选择
            SyncTextListSelection();

            // 触发事件
            OnTextSelectionChanged();

            // 重绘
            Invalidate();
        }

        /// <summary>
        /// 同步文字显示的选择状态
        /// </summary>
        private void SyncTextListSelection()
        {
            if (TextRichTextBox == null || !HasSelection()) return;

            // 清除之前的高亮
            TextRichTextBox.SelectAll();
            TextRichTextBox.SelectionBackColor = TextRichTextBox.BackColor;
            TextRichTextBox.SelectionColor = TextRichTextBox.ForeColor;
            TextRichTextBox.Select(0, 0);

            // 计算选中文字在RichTextBox中的位置并高亮
            var selectedText = GetSelectedText();
            if (!string.IsNullOrEmpty(selectedText))
            {
                var startPos = GetTextPositionInRichTextBox();
                var length = selectedText.Replace("\r\n", "\n").Length; // 处理换行符差异

                if (startPos >= 0 && startPos + length <= TextRichTextBox.Text.Length)
                {
                    TextRichTextBox.Select(startPos, length);
                    TextRichTextBox.SelectionBackColor = Color.FromArgb(100, 0, 120, 215);
                    TextRichTextBox.SelectionColor = Color.White;
                    TextRichTextBox.Select(0, 0); // 取消选择状态，只保留高亮
                }
            }
        }

        /// <summary>
        /// 获取选中文字在RichTextBox中的起始位置
        /// </summary>
        private int GetTextPositionInRichTextBox()
        {
            if (!HasSelection()) return -1;

            // 正确处理双向选择
            int startLine, startChar;

            if (selectionStartLineIndex < selectionEndLineIndex ||
                (selectionStartLineIndex == selectionEndLineIndex && selectionStartCharIndex <= selectionEndCharIndex))
            {
                // 正向选择
                startLine = selectionStartLineIndex;
                startChar = selectionStartCharIndex;
            }
            else
            {
                // 反向选择
                startLine = selectionEndLineIndex;
                startChar = selectionEndCharIndex;
            }

            int position = 0;

            // 计算前面行的字符数
            for (int i = 0; i < startLine && i < textLines.Count; i++)
            {
                position += textLines[i].LineText.Length + 1; // +1 for newline
            }

            // 加上当前行的字符偏移，确保不超出行长度
            if (startLine < textLines.Count)
            {
                var lineLength = textLines[startLine].LineText.Length;
                position += Math.Max(0, Math.Min(startChar, lineLength));
            }

            return position;
        }

        /// <summary>
        /// 触发文字选择变化事件
        /// </summary>
        private void OnTextSelectionChanged()
        {
            var selectedText = GetSelectedText();

            // 获取选中的cells（为了兼容现有接口）
            var selectedCells = new List<TextCellInfo>();
            if (HasSelection())
            {
                var startLine = Math.Min(selectionStartLineIndex, selectionEndLineIndex);
                var endLine = Math.Max(selectionStartLineIndex, selectionEndLineIndex);

                for (int i = startLine; i <= endLine && i < textLines.Count; i++)
                {
                    selectedCells.AddRange(textLines[i].Cells);
                }
            }

            TextSelectionChanged?.Invoke(this, new TextSelectionEventArgs(selectedCells)
            {
                SelectedText = selectedText,
                SelectedCharacterCount = selectedText.Length
            });
        }

        /// <summary>
        /// 清除所有选择
        /// </summary>
        public void ClearSelection()
        {
            selectionStartLineIndex = -1;
            selectionEndLineIndex = -1;
            selectionStartCharIndex = -1;
            selectionEndCharIndex = -1;

            if (TextRichTextBox != null)
            {
                TextRichTextBox.SelectAll();
                TextRichTextBox.SelectionBackColor = TextRichTextBox.BackColor;
                TextRichTextBox.SelectionColor = TextRichTextBox.ForeColor;
                TextRichTextBox.Select(0, 0);
            }

            OnTextSelectionChanged();
            Invalidate();
        }

        /// <summary>
        /// 获取当前选中的文字
        /// </summary>
        public string GetSelectedTextContent()
        {
            return GetSelectedText();
        }
    }

    /// <summary>
    /// 文字选择事件参数
    /// </summary>
    public class TextSelectionEventArgs : EventArgs
    {
        public List<TextCellInfo> SelectedCells { get; private set; }
        public string SelectedText { get; set; }
        public int SelectedCharacterCount { get; set; }

        public TextSelectionEventArgs(List<TextCellInfo> selectedCells)
        {
            SelectedCells = selectedCells ?? new List<TextCellInfo>();
            SelectedText = string.Empty;
            SelectedCharacterCount = 0;
        }
    }
}
