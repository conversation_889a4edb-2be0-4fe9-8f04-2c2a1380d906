﻿using OCRTools.Common;
using System;
using System.Collections.Concurrent;
using System.Drawing;
using System.IO;
using System.Text;
using UtfUnknown;

namespace OCRTools
{
    internal enum ProcessBy
    {
        主界面 = 0,
        划词翻译 = 1,
        批量识别 = 2,
        固定区域 = 3
    }

    internal class OcrPoolProcess
    {
        public static BlockingCollection<OcrProcessEntity> OcrProcessPool = new BlockingCollection<OcrProcessEntity>();

        public static Image ProcessByFile(OcrType ocrType, int groupType
            , bool isFromLeftToRight, bool isFromTopToDown, TransLanguageTypeEnum from, TransLanguageTypeEnum to
            , string fileName, string imgUrl, string fileExt
            , bool upload, bool isSearch, ProcessBy processBy, string fileIdentity)
        {
            byte[] byts = null;
            Image bitmap = null;

            if (CommonString.LstCanProcessImageFilesExt.Contains(fileExt))
            {
                if (fileName.StartsWith("data:"))
                {
                    fileName = CommonMethod.SubString(fileName, ",");
                    byts = Convert.FromBase64String(fileName);
                }
                else if (fileName.StartsWith("http"))
                {
                    imgUrl = fileName;
                    try
                    {
                        byts = WebClientExt.GetOneClient().DownloadData(imgUrl);
                    }
                    catch (Exception)
                    {
                        imgUrl = null;
                    }
                }
                else
                {
                    bitmap = Image.FromFile(fileName);
                    byts = File.ReadAllBytes(fileName);
                    upload = new FileInfo(fileName).Length / 1024 > (Program.NowUser?.MaxUploadSize ?? 300);
                }

                if (byts != null)
                {
                    bitmap = Image.FromStream(new MemoryStream(byts));
                }
            }
            else
            {
                if (fileExt?.Equals(CommonString.StrDefaultTxtType) == true)
                {
                    string text;
                    if (fileName.StartsWith("data:txt"))
                    {
                        text = fileName.Substring(fileName.IndexOf("data:txt") + "data:txt".Length).Trim();
                        to = GetAutoToLanguage(text, to);
                    }
                    else
                    {
                        var encoding = CharsetDetector.DetectFromFile(fileName)?.Detected?.Encoding ?? Encoding.UTF8;
                        text = File.ReadAllText(fileName, encoding).Trim();
                    }

                    byts = Encoding.UTF8.GetBytes(text);
                }
                else
                {
                    byts = File.ReadAllBytes(fileName);
                }
            }

            if (byts != null && byts.Length > 0 || !string.IsNullOrEmpty(imgUrl))
                ProcessByBytes(ocrType, groupType, isFromLeftToRight, isFromTopToDown, from, to, byts,
                    imgUrl, fileExt, fileName, upload, isSearch, processBy, fileIdentity, null);
            return bitmap;
        }

        public static void ProcessByImage(OcrType ocrType, int groupType
            , bool isFromLeftToRight, bool isFromTopToDown, TransLanguageTypeEnum from, TransLanguageTypeEnum to
            , Image bitmap, string imgUrl, string fileExt, string fileName
            , bool upload, bool isSearch, ProcessBy processBy, string fileIdentity
            , bool? isSupportVertical)
        {
            var byts = ImageProcessHelper.ImageToByte(bitmap);
            ProcessByBytes(ocrType, groupType, isFromLeftToRight, isFromTopToDown, from, to, byts, imgUrl,
                fileExt, fileName, upload, isSearch, processBy, fileIdentity
                , isSupportVertical);
        }

        private static TransLanguageTypeEnum GetAutoToLanguage(string strTxt, TransLanguageTypeEnum to)
        {
            if (to.Equals(TransLanguageTypeEnum.中文))
            {
                if (CommonMethod.IsContainsChinese(strTxt))
                {
                    to = TransLanguageTypeEnum.英文;
                }
            }
            return to;
        }

        public static void ProcessByText(int groupType
            , TransLanguageTypeEnum from, TransLanguageTypeEnum to
            , string strTxt, ProcessBy processBy, string fileIdentity)
        {
            if (string.IsNullOrEmpty(strTxt))
                return;
            to = GetAutoToLanguage(strTxt, to);
            var byts = Encoding.UTF8.GetBytes(strTxt);
            ProcessByBytes(OcrType.翻译, groupType, false, true, from, to, byts, "",
                CommonString.StrDefaultTxtType, "文本翻译", false, false, processBy, fileIdentity, null);
        }

        private static void ProcessByBytes(OcrType ocrType, int groupType
            , bool isFromLeftToRight, bool isFromTopToDown, TransLanguageTypeEnum from, TransLanguageTypeEnum to
            , byte[] byts, string imgUrl, string fileExt, string fileName
            , bool upload, bool isSearch, ProcessBy processBy, string fileIdentity
            , bool? isSupportVertical)
        {
            var entity = GetProcessEntityByBytes(ocrType, groupType, isFromLeftToRight, isFromTopToDown,
                from, to, byts, imgUrl, fileExt, upload, isSearch, processBy, fileIdentity
                , isSupportVertical);
            OcrProcessPool.Add(entity);
            if (Equals(fileExt, CommonString.StrDefaultImgType))
            {
                var task = new HistoryTask
                {
                    Status = TaskStatus.History,
                    Info = new HistoryItem
                    {
                        DataType = string.IsNullOrEmpty(fileName) ? EDataType.Image : EDataType.File,
                        Url = imgUrl,
                        FilePath = fileName,
                        CreateTime = ServerTime.DateTime
                    }
                };
                if (string.IsNullOrEmpty(task.Info.FileName)) task.Info.FileName = fileName;
                CommonMethod.AddRecentTask(task);
            }
        }

        public static OcrProcessEntity GetProcessEntityByBytes(OcrType ocrType, int groupType
            , bool isFromLeftToRight, bool isFromTopToDown, TransLanguageTypeEnum from, TransLanguageTypeEnum to
            , byte[] byts, string imgUrl, string fileExt
            , bool upload, bool isSearch, ProcessBy processBy, string fileIdentity
                , bool? isSupportVertical)
        {
            if (ocrType != OcrType.翻译 && Equals(fileExt, CommonString.StrDefaultTxtType)) ocrType = OcrType.翻译;
            var entity = new OcrProcessEntity
            {
                ProcessBy = processBy,
                IsShowLoading = true,
                Byts = byts,
                FileExt = fileExt,
                ImgUrl = imgUrl,
                OcrType = ocrType,
                GroupType = groupType,
                IsNeedUpload = upload,
                IsFromLeftToRight = isFromLeftToRight,
                IsFromTopToDown = isFromTopToDown,
                From = ocrType == OcrType.翻译 ? from : TransLanguageTypeEnum.自动,
                To = ocrType == OcrType.翻译 ? to : TransLanguageTypeEnum.自动,
                IsSearch = isSearch,
                Identity = fileIdentity,
                IsSupportVertical = isSupportVertical
            };
            return entity;
        }
    }
}