﻿using MetroFramework.Controls;
using System;
using System.Drawing;

/**
* MetroFramework - Modern UI for WinForms
* 
* The MIT License (MIT)
* Copyright (c) 2011 <PERSON>, http://github.com/viperneo
* 
* Permission is hereby granted, free of charge, to any person obtaining a copy of 
* this software and associated documentation files (the "Software"), to deal in the 
* Software without restriction, including without limitation the rights to use, copy, 
* modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, 
* and to permit persons to whom the Software is furnished to do so, subject to the 
* following conditions:
* 
* The above copyright notice and this permission notice shall be included in 
* all copies or substantial portions of the Software.
* 
* THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
* INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
* PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT 
* HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WH<PERSON>HER IN AN ACTION OF 
* CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE 
* OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/

namespace MetroFramework
{
    public static class MetroFonts
    {
        private static readonly IMetroFontResolver FontResolver;

        static MetroFonts()
        {
            FontResolver = new DefaultFontResolver();
        }

        public static Font Title => DefaultLight(24f);

        public static Font Subtitle => Default(14f);

        public static Font TileCount => Default(44f);

        public static Font DefaultLight(float size)
        {
            return FontResolver.ResolveFont("Segoe UI Light", size, FontStyle.Regular, GraphicsUnit.Pixel);
        }

        public static Font Default(float size)
        {
            return FontResolver.ResolveFont("Segoe UI", size, FontStyle.Regular, GraphicsUnit.Pixel);
        }

        public static Font DefaultBold(float size)
        {
            return FontResolver.ResolveFont("Segoe UI", size, FontStyle.Bold, GraphicsUnit.Pixel);
        }

        public static Font DefaultItalic(float size)
        {
            return FontResolver.ResolveFont("Segoe UI", size, FontStyle.Italic, GraphicsUnit.Pixel);
        }

        public static Font Tile(MetroTileTextSize labelSize, MetroTileTextWeight labelWeight)
        {
            switch (labelSize)
            {
                case MetroTileTextSize.Small:
                    switch (labelWeight)
                    {
                        case MetroTileTextWeight.Light:
                            return DefaultLight(12f);
                        case MetroTileTextWeight.Regular:
                            return Default(12f);
                        case MetroTileTextWeight.Bold:
                            return DefaultBold(12f);
                    }

                    break;
                case MetroTileTextSize.Medium:
                    switch (labelWeight)
                    {
                        case MetroTileTextWeight.Light:
                            return DefaultLight(14f);
                        case MetroTileTextWeight.Regular:
                            return Default(14f);
                        case MetroTileTextWeight.Bold:
                            return DefaultBold(14f);
                    }

                    break;
                case MetroTileTextSize.Tall:
                    switch (labelWeight)
                    {
                        case MetroTileTextWeight.Light:
                            return DefaultLight(18f);
                        case MetroTileTextWeight.Regular:
                            return Default(18f);
                        case MetroTileTextWeight.Bold:
                            return DefaultBold(18f);
                    }

                    break;
            }

            return DefaultLight(14f);
        }

        public static Font Link(MetroLinkSize linkSize, MetroLinkWeight linkWeight)
        {
            switch (linkSize)
            {
                case MetroLinkSize.Small:
                    switch (linkWeight)
                    {
                        case MetroLinkWeight.Light:
                            return DefaultLight(12f);
                        case MetroLinkWeight.Regular:
                            return Default(12f);
                        case MetroLinkWeight.Bold:
                            return DefaultBold(12f);
                    }

                    break;
                case MetroLinkSize.Medium:
                    switch (linkWeight)
                    {
                        case MetroLinkWeight.Light:
                            return DefaultLight(14f);
                        case MetroLinkWeight.Regular:
                            return Default(14f);
                        case MetroLinkWeight.Bold:
                            return DefaultBold(14f);
                    }

                    break;
                case MetroLinkSize.Tall:
                    switch (linkWeight)
                    {
                        case MetroLinkWeight.Light:
                            return DefaultLight(18f);
                        case MetroLinkWeight.Regular:
                            return Default(18f);
                        case MetroLinkWeight.Bold:
                            return DefaultBold(18f);
                    }

                    break;
            }

            return Default(12f);
        }

        //public static Font ComboBox(MetroComboBoxSize linkSize, MetroComboBoxWeight linkWeight)
        //{
        //    switch (linkSize)
        //    {
        //        case MetroComboBoxSize.Small:
        //            switch (linkWeight)
        //            {
        //                case MetroComboBoxWeight.Light:
        //                    return DefaultLight(12f);
        //                case MetroComboBoxWeight.Regular:
        //                    return Default(12f);
        //                case MetroComboBoxWeight.Bold:
        //                    return DefaultBold(12f);
        //            }

        //            break;
        //        case MetroComboBoxSize.Medium:
        //            switch (linkWeight)
        //            {
        //                case MetroComboBoxWeight.Light:
        //                    return DefaultLight(14f);
        //                case MetroComboBoxWeight.Regular:
        //                    return Default(14f);
        //                case MetroComboBoxWeight.Bold:
        //                    return DefaultBold(14f);
        //            }

        //            break;
        //        case MetroComboBoxSize.Tall:
        //            switch (linkWeight)
        //            {
        //                case MetroComboBoxWeight.Light:
        //                    return DefaultLight(18f);
        //                case MetroComboBoxWeight.Regular:
        //                    return Default(18f);
        //                case MetroComboBoxWeight.Bold:
        //                    return DefaultBold(18f);
        //            }

        //            break;
        //    }

        //    return Default(12f);
        //}

        //public static Font DateTime(MetroDateTimeSize linkSize, MetroDateTimeWeight linkWeight)
        //{
        //    switch (linkSize)
        //    {
        //        case MetroDateTimeSize.Small:
        //            switch (linkWeight)
        //            {
        //                case MetroDateTimeWeight.Light:
        //                    return DefaultLight(12f);
        //                case MetroDateTimeWeight.Regular:
        //                    return Default(12f);
        //                case MetroDateTimeWeight.Bold:
        //                    return DefaultBold(12f);
        //            }
        //            break;
        //        case MetroDateTimeSize.Medium:
        //            switch (linkWeight)
        //            {
        //                case MetroDateTimeWeight.Light:
        //                    return DefaultLight(14f);
        //                case MetroDateTimeWeight.Regular:
        //                    return Default(14f);
        //                case MetroDateTimeWeight.Bold:
        //                    return DefaultBold(14f);
        //            }
        //            break;
        //        case MetroDateTimeSize.Tall:
        //            switch (linkWeight)
        //            {
        //                case MetroDateTimeWeight.Light:
        //                    return DefaultLight(18f);
        //                case MetroDateTimeWeight.Regular:
        //                    return Default(18f);
        //                case MetroDateTimeWeight.Bold:
        //                    return DefaultBold(18f);
        //            }
        //            break;
        //    }
        //    return Default(12f);
        //}

        public static Font Label(MetroLabelSize labelSize, MetroLabelWeight labelWeight)
        {
            switch (labelSize)
            {
                case MetroLabelSize.Small:
                    switch (labelWeight)
                    {
                        case MetroLabelWeight.Light:
                            return DefaultLight(12f);
                        case MetroLabelWeight.Regular:
                            return Default(12f);
                        case MetroLabelWeight.Bold:
                            return DefaultBold(12f);
                    }

                    break;
                case MetroLabelSize.Medium:
                    switch (labelWeight)
                    {
                        case MetroLabelWeight.Light:
                            return DefaultLight(14f);
                        case MetroLabelWeight.Regular:
                            return Default(14f);
                        case MetroLabelWeight.Bold:
                            return DefaultBold(14f);
                    }

                    break;
                case MetroLabelSize.Tall:
                    switch (labelWeight)
                    {
                        case MetroLabelWeight.Light:
                            return DefaultLight(18f);
                        case MetroLabelWeight.Regular:
                            return Default(18f);
                        case MetroLabelWeight.Bold:
                            return DefaultBold(18f);
                    }

                    break;
            }

            return DefaultLight(14f);
        }

        public static Font TextBox(MetroTextBoxSize linkSize, MetroTextBoxWeight linkWeight)
        {
            switch (linkSize)
            {
                case MetroTextBoxSize.Small:
                    switch (linkWeight)
                    {
                        case MetroTextBoxWeight.Light:
                            return DefaultLight(12f);
                        case MetroTextBoxWeight.Regular:
                            return Default(12f);
                        case MetroTextBoxWeight.Bold:
                            return DefaultBold(12f);
                    }

                    break;
                case MetroTextBoxSize.Medium:
                    switch (linkWeight)
                    {
                        case MetroTextBoxWeight.Light:
                            return DefaultLight(14f);
                        case MetroTextBoxWeight.Regular:
                            return Default(14f);
                        case MetroTextBoxWeight.Bold:
                            return DefaultBold(14f);
                    }

                    break;
                case MetroTextBoxSize.Tall:
                    switch (linkWeight)
                    {
                        case MetroTextBoxWeight.Light:
                            return DefaultLight(18f);
                        case MetroTextBoxWeight.Regular:
                            return Default(18f);
                        case MetroTextBoxWeight.Bold:
                            return DefaultBold(18f);
                    }

                    break;
            }

            return Default(12f);
        }

        public static Font ProgressBar(MetroProgressBarSize labelSize, MetroProgressBarWeight labelWeight)
        {
            switch (labelSize)
            {
                case MetroProgressBarSize.Small:
                    switch (labelWeight)
                    {
                        case MetroProgressBarWeight.Light:
                            return DefaultLight(12f);
                        case MetroProgressBarWeight.Regular:
                            return Default(12f);
                        case MetroProgressBarWeight.Bold:
                            return DefaultBold(12f);
                    }

                    break;
                case MetroProgressBarSize.Medium:
                    switch (labelWeight)
                    {
                        case MetroProgressBarWeight.Light:
                            return DefaultLight(14f);
                        case MetroProgressBarWeight.Regular:
                            return Default(14f);
                        case MetroProgressBarWeight.Bold:
                            return DefaultBold(14f);
                    }

                    break;
                case MetroProgressBarSize.Tall:
                    switch (labelWeight)
                    {
                        case MetroProgressBarWeight.Light:
                            return DefaultLight(18f);
                        case MetroProgressBarWeight.Regular:
                            return Default(18f);
                        case MetroProgressBarWeight.Bold:
                            return DefaultBold(18f);
                    }

                    break;
            }

            return DefaultLight(14f);
        }

        public static Font TabControl(MetroTabControlSize labelSize, MetroTabControlWeight labelWeight)
        {
            switch (labelSize)
            {
                case MetroTabControlSize.Small:
                    switch (labelWeight)
                    {
                        case MetroTabControlWeight.Light:
                            return DefaultLight(12f);
                        case MetroTabControlWeight.Regular:
                            return Default(12f);
                        case MetroTabControlWeight.Bold:
                            return DefaultBold(12f);
                    }

                    break;
                case MetroTabControlSize.Medium:
                    switch (labelWeight)
                    {
                        case MetroTabControlWeight.Light:
                            return DefaultLight(14f);
                        case MetroTabControlWeight.Regular:
                            return Default(14f);
                        case MetroTabControlWeight.Bold:
                            return DefaultBold(14f);
                    }

                    break;
                case MetroTabControlSize.Tall:
                    switch (labelWeight)
                    {
                        case MetroTabControlWeight.Light:
                            return DefaultLight(18f);
                        case MetroTabControlWeight.Regular:
                            return Default(18f);
                        case MetroTabControlWeight.Bold:
                            return DefaultBold(18f);
                    }

                    break;
            }

            return DefaultLight(14f);
        }

        public static Font CheckBox(MetroCheckBoxSize linkSize, MetroCheckBoxWeight linkWeight)
        {
            switch (linkSize)
            {
                case MetroCheckBoxSize.Small:
                    switch (linkWeight)
                    {
                        case MetroCheckBoxWeight.Light:
                            return DefaultLight(12f);
                        case MetroCheckBoxWeight.Regular:
                            return Default(12f);
                        case MetroCheckBoxWeight.Bold:
                            return DefaultBold(12f);
                    }

                    break;
                case MetroCheckBoxSize.Medium:
                    switch (linkWeight)
                    {
                        case MetroCheckBoxWeight.Light:
                            return DefaultLight(14f);
                        case MetroCheckBoxWeight.Regular:
                            return Default(14f);
                        case MetroCheckBoxWeight.Bold:
                            return DefaultBold(14f);
                    }

                    break;
                case MetroCheckBoxSize.Tall:
                    switch (linkWeight)
                    {
                        case MetroCheckBoxWeight.Light:
                            return DefaultLight(18f);
                        case MetroCheckBoxWeight.Regular:
                            return Default(18f);
                        case MetroCheckBoxWeight.Bold:
                            return DefaultBold(18f);
                    }

                    break;
            }

            return Default(12f);
        }

        public static Font WaterMark(MetroLabelSize labelSize, MetroWaterMarkWeight labelWeight)
        {
            switch (labelSize)
            {
                case MetroLabelSize.Small:
                    switch (labelWeight)
                    {
                        case MetroWaterMarkWeight.Light:
                            return DefaultLight(12f);
                        case MetroWaterMarkWeight.Regular:
                            return Default(12f);
                        case MetroWaterMarkWeight.Bold:
                            return DefaultBold(12f);
                        case MetroWaterMarkWeight.Italic:
                            return DefaultItalic(12f);
                    }

                    break;
                case MetroLabelSize.Medium:
                    switch (labelWeight)
                    {
                        case MetroWaterMarkWeight.Light:
                            return DefaultLight(14f);
                        case MetroWaterMarkWeight.Regular:
                            return Default(14f);
                        case MetroWaterMarkWeight.Bold:
                            return DefaultBold(14f);
                        case MetroWaterMarkWeight.Italic:
                            return DefaultItalic(14f);
                    }

                    break;
                case MetroLabelSize.Tall:
                    switch (labelWeight)
                    {
                        case MetroWaterMarkWeight.Light:
                            return DefaultLight(18f);
                        case MetroWaterMarkWeight.Regular:
                            return Default(18f);
                        case MetroWaterMarkWeight.Bold:
                            return DefaultBold(18f);
                        case MetroWaterMarkWeight.Italic:
                            return DefaultItalic(18f);
                    }

                    break;
            }

            return DefaultLight(14f);
        }

        public static Font Button(MetroButtonSize linkSize, MetroButtonWeight linkWeight)
        {
            switch (linkSize)
            {
                case MetroButtonSize.Small:
                    switch (linkWeight)
                    {
                        case MetroButtonWeight.Light:
                            return DefaultLight(11f);
                        case MetroButtonWeight.Regular:
                            return Default(11f);
                        case MetroButtonWeight.Bold:
                            return DefaultBold(11f);
                    }

                    break;
                case MetroButtonSize.Medium:
                    switch (linkWeight)
                    {
                        case MetroButtonWeight.Light:
                            return DefaultLight(13f);
                        case MetroButtonWeight.Regular:
                            return Default(13f);
                        case MetroButtonWeight.Bold:
                            return DefaultBold(13f);
                    }

                    break;
                case MetroButtonSize.Tall:
                    switch (linkWeight)
                    {
                        case MetroButtonWeight.Light:
                            return DefaultLight(16f);
                        case MetroButtonWeight.Regular:
                            return Default(16f);
                        case MetroButtonWeight.Bold:
                            return DefaultBold(16f);
                    }

                    break;
            }

            return Default(11f);
        }

        internal interface IMetroFontResolver
        {
            Font ResolveFont(string familyName, float emSize, FontStyle fontStyle, GraphicsUnit unit);
        }

        private class DefaultFontResolver : IMetroFontResolver
        {
            public Font ResolveFont(string familyName, float emSize, FontStyle fontStyle, GraphicsUnit unit)
            {
                return new Font(familyName, emSize, fontStyle, unit);
            }
        }
    }

    public enum MetroLabelSize
    {
        Small,
        Medium,
        Tall
    }

    public enum MetroLabelWeight
    {
        Light,
        Regular,
        Bold
    }

    public enum MetroLinkSize
    {
        Small,
        Medium,
        Tall
    }

    public enum MetroLinkWeight
    {
        Light,
        Regular,
        Bold
    }

    public enum MetroTextBoxSize
    {
        Small,
        Medium,
        Tall
    }

    public enum MetroTextBoxWeight
    {
        Light,
        Regular,
        Bold
    }

    public enum MetroProgressBarSize
    {
        Small,
        Medium,
        Tall
    }

    public enum MetroProgressBarWeight
    {
        Light,
        Regular,
        Bold
    }

    public enum MetroTabControlSize
    {
        Small,
        Medium,
        Tall
    }

    public enum MetroTabControlWeight
    {
        Light,
        Regular,
        Bold
    }

    public enum MetroWaterMarkWeight
    {
        Light,
        Regular,
        Bold,
        Italic
    }

    public enum MetroTileTextSize
    {
        Small,
        Medium,
        Tall
    }

    public enum MetroTileTextWeight
    {
        Light,
        Regular,
        Bold
    }

    public enum MetroCheckBoxSize
    {
        Small,
        Medium,
        Tall
    }

    public enum MetroCheckBoxWeight
    {
        Light,
        Regular,
        Bold
    }

    public enum MetroButtonSize
    {
        Small,
        Medium,
        Tall
    }

    public enum MetroButtonWeight
    {
        Light,
        Regular,
        Bold
    }
}