﻿using MetroFramework.Components;
using MetroFramework.Controls;
using System;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace OCRTools
{
    [ToolStripItemDesignerAvailability(ToolStripItemDesignerAvailability.StatusStrip)]
    public class ToolStripCheckBoxControl : ToolStripControlHost
    {
        private readonly MetroCheckBox checkBox = new MetroCheckBox();

        public ToolStripCheckBoxControl() : base(new MetroCheckBox())
        {
            checkBox = Control as MetroCheckBox;
        }

        public bool Checked
        {
            get => checkBox.Checked;
            set => checkBox.Checked = value;
        }

        public event EventHandler CheckedChanged
        {
            add => checkBox.CheckedChanged += value;
            remove => checkBox.CheckedChanged -= value;
        }

        public void SetStyleManager(MetroStyleManager styleManager)
        {
            checkBox.StyleManager = styleManager;
        }
    }
}