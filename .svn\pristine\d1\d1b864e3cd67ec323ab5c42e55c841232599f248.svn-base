using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    internal class ContextMenuHelp : ContextMenuStrip
    {
        private ListButton control;

        public ContextMenuHelp()
        {
            ShowImageMargin = false;
            Name = "标题";
            Size = new Size(125, 92);
            AutoSize = true;
        }

        public void ShowEx(ListButton button, bool autoFontwidth = false, bool isborder = false)
        {
            control = button;
            AutoSize = true;
            Size = new Size(button.Width, Size.Height);
            AutoSize = false;
            Font = button.Font;
            if (!autoFontwidth)
            {
                Size = new Size(control.Width, Size.Height);
                if (!isborder)
                    Show(button, new Point(0, button.Height));
                else
                    Show(button, new Point(0, button.Height - 1));
            }
            else
            {
                var num = 0;
                for (var i = 0; i < Items.Count; i++)
                    using (var graphics = CreateGraphics())
                    {
                        var sizeF = graphics.MeasureString("宽" + Items[i].Text, Font);
                        if (num < Convert.ToInt32(sizeF.Width)) num = Convert.ToInt32(sizeF.Width);
                    }

                Size = new Size(num, Size.Height);
                Show(button, new Point((button.Width - Width) / 2, button.Height));
            }

            for (var j = 0; j < Items.Count; j++)
            {
                Items[j].Width = Width;
                Items[j].Click -= item_click;
                Items[j].Click += item_click;
            }

            Renderer = new ComboxbtnRenderer(true);
        }

        private void item_click(object sender, EventArgs e)
        {
            var toolStripMenuItem = (ToolStripMenuItem) sender;
            control.Text = toolStripMenuItem.Text;
            control.Change();
        }
    }
}