namespace ExcelLibrary.BinaryFileFormat
{
	public class RecordType
	{
		public const ushort INTEGER = 2;

		public const ushort FORMULA = 6;

		public const ushort EOF = 10;

		public const ushort CALCCOUNT = 12;

		public const ushort CALCMODE = 13;

		public const ushort PRECISION = 14;

		public const ushort REFMODE = 15;

		public const ushort DELTA = 16;

		public const ushort ITERATION = 17;

		public const ushort PROTECT = 18;

		public const ushort PASSWORD = 19;

		public const ushort HEADER = 20;

		public const ushort FOOTER = 21;

		public const ushort EXTERNCOUNT = 22;

		public const ushort EXTERNSHEET = 23;

		public const ushort NAME = 24;

		public const ushort WINDOWPROTECT = 25;

		public const ushort VERTICALPAGEBREAKS = 26;

		public const ushort HORIZONTALPAGEBREAKS = 27;

		public const ushort NOTE = 28;

		public const ushort SELECTION = 29;

		public const ushort COLUMNDEFAULT = 32;

		public const ushort DATEMODE = 34;

		public const ushort EXTERNNAME = 35;

		public const ushort COLWIDTH = 36;

		public const ushort LEFTMARGIN = 38;

		public const ushort RIGHTMARGIN = 39;

		public const ushort TOPMARGIN = 40;

		public const ushort BOTTOMMARGIN = 41;

		public const ushort PRINTHEADERS = 42;

		public const ushort PRINTGRIDLINES = 43;

		public const ushort FILEPASS = 47;

		public const ushort FONT = 49;

		public const ushort PRINTSIZE = 51;

		public const ushort INFOOPTS = 53;

		public const ushort TABLEOP2 = 55;

		public const ushort WNDESK = 56;

		public const ushort BEGINPREF = 58;

		public const ushort ENDPREF = 59;

		public const ushort CONTINUE = 60;

		public const ushort WINDOW1 = 61;

		public const ushort BACKUP = 64;

		public const ushort PANE = 65;

		public const ushort CODEPAGE = 66;

		public const ushort IXFE = 68;

		public const ushort EFONT = 69;

		public const ushort SHOWSCROLL = 70;

		public const ushort SHOWFORMULA = 71;

		public const ushort STATUSBAR = 72;

		public const ushort SHORTMENUS = 73;

		public const ushort DDEENABLED = 74;

		public const ushort AUTODEC = 75;

		public const ushort MENUKEY = 76;

		public const ushort PLS_ZOOM = 77;

		public const ushort MENUUND = 78;

		public const ushort MOVESEL = 79;

		public const ushort DCON = 80;

		public const ushort DCONREF = 81;

		public const ushort DCONNAME = 82;

		public const ushort DEFCOLWIDTH = 85;

		public const ushort TOOLBAR = 88;

		public const ushort XCT = 89;

		public const ushort CRN = 90;

		public const ushort FILESHARING = 91;

		public const ushort WRITEACCESS = 92;

		public const ushort OBJ = 93;

		public const ushort UNCALCED = 94;

		public const ushort SAVERECALC = 95;

		public const ushort TEMPLATE = 96;

		public const ushort OBJECTPROTECT = 99;

		public const ushort COLINFO = 125;

		public const ushort IMDATA = 127;

		public const ushort GUTS = 128;

		public const ushort WSBOOL = 129;

		public const ushort GRIDSET = 130;

		public const ushort HCENTER = 131;

		public const ushort VCENTER = 132;

		public const ushort BOUNDSHEET = 133;

		public const ushort WRITEPROT = 134;

		public const ushort ADDIN = 135;

		public const ushort EDG = 136;

		public const ushort PUB = 137;

		public const ushort NOTEOFF = 138;

		public const ushort LH = 139;

		public const ushort COUNTRY = 140;

		public const ushort HIDEOBJ = 141;

		public const ushort SHEETSOFFSET = 142;

		public const ushort SHEETHDR = 143;

		public const ushort SORT = 144;

		public const ushort SUB = 145;

		public const ushort PALETTE = 146;

		public const ushort LHRECORD = 148;

		public const ushort LHNGRAPH = 149;

		public const ushort SOUND = 150;

		public const ushort LPR = 152;

		public const ushort STANDARDWIDTH = 153;

		public const ushort FNGROUPNAME = 154;

		public const ushort FILTERMODE = 155;

		public const ushort FNGROUPCOUNT = 156;

		public const ushort AUTOFILTERINFO = 157;

		public const ushort AUTOFILTER = 158;

		public const ushort SCL = 160;

		public const ushort SETUP = 161;

		public const ushort PROJEXTSHEET = 163;

		public const ushort DRAGDROP = 168;

		public const ushort COORDLIST = 169;

		public const ushort GCW = 171;

		public const ushort SCENMAN = 174;

		public const ushort SCENARIO = 175;

		public const ushort SXVIEW = 176;

		public const ushort SXVD = 177;

		public const ushort SXVI = 178;

		public const ushort SXIVD = 180;

		public const ushort SXLI = 181;

		public const ushort SXPI = 182;

		public const ushort DOCROUTE = 184;

		public const ushort RECIPNAME = 185;

		public const ushort MULRK = 189;

		public const ushort MULBLANK = 190;

		public const ushort MMS = 193;

		public const ushort ADDMENU = 194;

		public const ushort DELMENU = 195;

		public const ushort SXDI = 197;

		public const ushort SXDB = 198;

		public const ushort SXFIELD = 199;

		public const ushort SXINDEXLIST = 200;

		public const ushort SXDOUBLE = 201;

		public const ushort SXBOOLEAN = 202;

		public const ushort SXERROR = 203;

		public const ushort SXINTEGER = 204;

		public const ushort SXSTRING = 205;

		public const ushort SXDATETIME = 206;

		public const ushort SXEMPTY = 207;

		public const ushort SXTBL = 208;

		public const ushort SXTBRGITEM = 209;

		public const ushort SXTBPG = 210;

		public const ushort OBPROJ = 211;

		public const ushort SXIDSTM = 213;

		public const ushort RSTRING = 214;

		public const ushort DBCELL = 215;

		public const ushort SXNUMGROUP = 216;

		public const ushort SXGROUPINFO = 217;

		public const ushort BOOKBOOL = 218;

		public const ushort REVERT = 219;

		public const ushort SXEXT_PARAMQRY = 220;

		public const ushort SCENPROTECT = 221;

		public const ushort OLESIZE = 222;

		public const ushort UDDESC = 223;

		public const ushort XF = 224;

		public const ushort INTERFACEHDR = 225;

		public const ushort INTERFACEEND = 226;

		public const ushort SXVS = 227;

		public const ushort MERGEDCELLS = 229;

		public const ushort BITMAP = 233;

		public const ushort MSODRAWINGGROUP = 235;

		public const ushort MSODRAWING = 236;

		public const ushort MSODRAWINGSELECTION = 237;

		public const ushort PHONETIC = 239;

		public const ushort SXRULE = 240;

		public const ushort SXEX = 241;

		public const ushort SXFILT = 242;

		public const ushort SXNAME = 246;

		public const ushort SXSELECT = 247;

		public const ushort SXPAIR = 248;

		public const ushort SXFMLA = 249;

		public const ushort SXFORMAT = 251;

		public const ushort SST = 252;

		public const ushort LABELSST = 253;

		public const ushort EXTSST = 255;

		public const ushort SXVDEX = 256;

		public const ushort SXFORMULA = 259;

		public const ushort SXDBEX = 290;

		public const ushort CHTRINSERT = 311;

		public const ushort CHTRINFO = 312;

		public const ushort CHTRCELLCONTENT = 315;

		public const ushort TABID = 317;

		public const ushort CHTRMOVERANGE = 320;

		public const ushort CHTRINSERTTAB = 333;

		public const ushort LABELRANGES = 351;

		public const ushort USESELFS = 352;

		public const ushort DSF = 353;

		public const ushort XL5MODIFY = 354;

		public const ushort CHTRHEADER = 406;

		public const ushort USERBVIEW = 425;

		public const ushort USERSVIEWBEGIN = 426;

		public const ushort USERSVIEWEND = 427;

		public const ushort QSI = 429;

		public const ushort SUPBOOK = 430;

		public const ushort CONDFMT = 432;

		public const ushort CF = 433;

		public const ushort DVAL = 434;

		public const ushort DCONBIN = 437;

		public const ushort TXO = 438;

		public const ushort REFRESHALL = 439;

		public const ushort HLINK = 440;

		public const ushort CODENAME = 442;

		public const ushort SXFDBTYPE = 443;

		public const ushort PROT4REVPASS = 444;

		public const ushort DV = 446;

		public const ushort XL9FILE = 448;

		public const ushort RECALCID = 449;

		public const ushort DIMENSIONS = 512;

		public const ushort BLANK = 513;

		public const ushort NUMBER = 515;

		public const ushort LABEL = 516;

		public const ushort BOOLERR = 517;

		public const ushort STRING = 519;

		public const ushort ROW = 520;

		public const ushort INDEX = 523;

		public const ushort ARRAY = 545;

		public const ushort DEFAULTROWHEIGHT = 549;

		public const ushort TABLEOP = 566;

		public const ushort WINDOW2 = 574;

		public const ushort RK = 638;

		public const ushort STYLE = 659;

		public const ushort FORMAT = 1054;

		public const ushort SHRFMLA = 1212;

		public const ushort QUICKTIP = 2048;

		public const ushort WEBQRYSETTINGS = 2051;

		public const ushort WEBQRYTABLES = 2052;

		public const ushort BOF = 2057;

		public const ushort SHEETLAYOUT = 2146;

		public const ushort SHEETPROTECTION = 2151;

		public const ushort RANGEPROTECTION = 2152;

		public const ushort CHUNITS = 4097;

		public const ushort CHCHART = 4098;

		public const ushort CHSERIES = 4099;

		public const ushort CHDATAFORMAT = 4102;

		public const ushort CHLINEFORMAT = 4103;

		public const ushort CHMARKERFORMAT = 4105;

		public const ushort CHAREAFORMAT = 4106;

		public const ushort CHPIEFORMAT = 4107;

		public const ushort CHATTACHEDLABEL = 4108;

		public const ushort CHSTRING = 4109;

		public const ushort CHCHARTFORMAT = 4116;

		public const ushort CHLEGEND = 4117;

		public const ushort CHSERIESLIST = 4118;

		public const ushort CHBAR = 4119;

		public const ushort CHLINE = 4120;

		public const ushort CHPIE = 4121;

		public const ushort CHAREA = 4122;

		public const ushort CHSCATTER = 4123;

		public const ushort CHCHARTLINE = 4124;

		public const ushort CHAXIS = 4125;

		public const ushort CHTICK = 4126;

		public const ushort CHVALUERANGE = 4127;

		public const ushort CHLABELRANGE = 4128;

		public const ushort CHAXISLINE = 4129;

		public const ushort CHFORMATLINK = 4130;

		public const ushort CHDEFAULTTEXT = 4132;

		public const ushort CHTEXT = 4133;

		public const ushort CHFONT = 4134;

		public const ushort CHOBJECTLINK = 4135;

		public const ushort CHARROW = 4141;

		public const ushort CHARROWHEAD = 4143;

		public const ushort CHFRAME = 4146;

		public const ushort CHBEGIN = 4147;

		public const ushort CHEND = 4148;

		public const ushort CHPLOTAREA = 4149;

		public const ushort CHCHARTSIZE = 4150;

		public const ushort CHRELPOSITION = 4151;

		public const ushort CHARROWRELPOS = 4152;

		public const ushort CHCHART3D = 4154;

		public const ushort CHMULTILINK = 4155;

		public const ushort CHPICFORMAT = 4156;

		public const ushort CHDROPBAR = 4157;

		public const ushort CHRADARLINE = 4158;

		public const ushort CHSURFACE = 4159;

		public const ushort CHRADARAREA = 4160;

		public const ushort CHAXESSET = 4161;

		public const ushort CHLEGENDENTRY = 4163;

		public const ushort CHPROPERTIES = 4164;

		public const ushort CHSERGROUP = 4165;

		public const ushort CHUSEDAXESSETS = 4166;

		public const ushort CHPIVOTREF = 4168;

		public const ushort CHSERPARENT = 4170;

		public const ushort CHSERTRENDLINE = 4171;

		public const ushort CHFORMAT = 4174;

		public const ushort CHPOS = 4175;

		public const ushort CHFORMATRUNS = 4176;

		public const ushort CHSOURCELINK = 4177;

		public const ushort CHSERERRORBAR = 4187;

		public const ushort CHSERIESFORMAT = 4189;

		public const ushort CH3DDATAFORMAT = 4191;

		public const ushort CHFONTBASE = 4192;

		public const ushort CHPIEEXT = 4193;

		public const ushort CHEXTRANGE = 4194;

		public const ushort CHDATATABLE = 4195;

		public const ushort CHPLOTGROWTH = 4196;

		public const ushort CHSERINDEX = 4197;

		public const ushort CHESCHERFORMAT = 4198;

		public const ushort CHPIEEXTSETT = 4199;
	}
}
