using OCRTools.Common;
using System;
using System.Drawing;

namespace OCRTools
{
    public class WindowInfo
    {
        private string _className;

        private Icon _icon; // public Icon Get  NativeMethods.GetApplicationIcon(Handle);

        private string _text;

        public WindowInfo(IntPtr handle)
        {
            Handle = handle;
        }

        public WindowInfo(IntPtr handle, Rectangle rect)
        {
            Handle = handle;
            Rectangle = rect;
        }

        public WindowInfo()
        {
        }

        public bool IsSmallControl { get; set; }

        public IntPtr ParentHandle { get; set; }

        public IntPtr Handle { get; set; }

        public Rectangle Rectangle { get; set; }

        public bool IsWindow { get; set; }

        public bool IsHandleCreated => Handle != IntPtr.Zero;

        public bool IsParentHandleCreated => ParentHandle != null && ParentHandle != IntPtr.Zero;

        public int ZIndex { get; internal set; }

        public Rectangle GetRectangle()
        {
            if (Rectangle.IsEmpty) Rectangle = Handle.GetRectangle(IsWindow);
            return Rectangle;
        }

        public bool Activate()
        {
            var result = false;
            if (IsParentHandleCreated)
            {
                NativeMethods.SetActiveWindow(ParentHandle);
                result = NativeMethods.SetForegroundWindow(ParentHandle);
            }
            else
            {
                NativeMethods.SetActiveWindow(Handle);
                result = NativeMethods.SetForegroundWindow(Handle);
            }
            return result;
        }

        public string GetClassName()
        {
            if (string.IsNullOrEmpty(_className)) _className = NativeMethods.GetClassName(Handle);
            return _className;
        }

        public string GetText()
        {
            if (string.IsNullOrEmpty(_text)) _text = NativeMethods.GetWindowText(Handle);
            return _text;
        }

        public Icon GetIcon()
        {
            if (_icon != null) _icon = NativeMethods.GetApplicationIcon(Handle);
            return _icon;
        }

        public bool GetIsMinimized()
        {
            return NativeMethods.IsIconic(Handle);
        }

        public void Restore()
        {
            if (IsHandleCreated) NativeMethods.ShowWindow(Handle, (int)WindowShowStyle.Restore);
        }

        //internal void Scal()
        //{
        //    if (PrimaryScreen.ScreenScalingFactor < 1)
        //    {
        //        this.Rectangle = new Rectangle(
        //            new Point((int)(Rectangle.Location.X / PrimaryScreen.ScreenScalingFactor)
        //            , (int)(Rectangle.Location.Y / PrimaryScreen.ScreenScalingFactor))
        //            , new Size((int)(Rectangle.Size.Width / PrimaryScreen.ScreenScalingFactor)
        //            , (int)(Rectangle.Size.Height / PrimaryScreen.ScreenScalingFactor))
        //            );
        //    }
        //}

        internal void OffSet(Rectangle rectangle, Point offSet)
        {
            Rectangle = Rectangle.Intersect(Rectangle, rectangle);
            if (!Rectangle.Location.IsEmpty && !Rectangle.Size.IsEmpty && Rectangle.IsValid())
            {
                Rectangle = new Rectangle(new Point(Rectangle.Location.X + offSet.X, Rectangle.Location.Y + offSet.Y + 20), Rectangle.Size);
            }
        }
    }
}