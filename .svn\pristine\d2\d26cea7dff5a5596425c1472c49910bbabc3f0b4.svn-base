namespace ExcelLibrary.BinaryDrawingFormat
{
	public class MsofbtDggContainer : MsofbtContainer
	{
		public MsofbtBstoreContainer BstoreContainer
		{
			get
			{
				foreach (EscherRecord escherRecord in EscherRecords)
				{
					if (escherRecord.Type == 61441)
					{
						return escherRecord as MsofbtBstoreContainer;
					}
				}
				return null;
			}
		}

		public MsofbtDggContainer(EscherRecord record)
			: base(record)
		{
		}

		public MsofbtDggContainer()
		{
			Type = 61440;
		}
	}
}
