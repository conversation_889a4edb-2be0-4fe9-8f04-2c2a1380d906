using System.Text;
using UtfUnknown.Core.Models.MultiByte;

namespace UtfUnknown.Core.Probers.MultiByte
{
    public class Utf8Prober : CharsetProber
    {
        private static readonly float ONE_CHAR_PROB = 0.5f;

        private readonly CodingStateMachine _codingSm;

        private int _numOfMbChar;

        public Utf8Prober()
        {
            _numOfMbChar = 0;
            _codingSm = new CodingStateMachine(new Utf8SmModel());
            Reset();
        }

        public override string GetCharsetName()
        {
            return "utf-8";
        }

        public override void Reset()
        {
            _codingSm.Reset();
            _numOfMbChar = 0;
            state = ProbingState.Detecting;
        }

        public override ProbingState HandleData(byte[] buf, int offset, int len)
        {
            var num = offset + len;
            for (var i = offset; i < num; i++)
            {
                switch (_codingSm.NextState(buf[i]))
                {
                    case 1:
                        state = ProbingState.NotMe;
                        break;
                    case 2:
                        state = ProbingState.FoundIt;
                        break;
                    case 0:
                        if (_codingSm.CurrentCharLen >= 2) _numOfMbChar++;
                        continue;
                    default:
                        continue;
                }

                break;
            }

            if (state == ProbingState.Detecting && GetConfidence() > 0.95f) state = ProbingState.FoundIt;
            return state;
        }

        public override float GetConfidence(StringBuilder status = null)
        {
            var num = 0.99f;
            if (_numOfMbChar < 6)
            {
                for (var i = 0; i < _numOfMbChar; i++) num *= ONE_CHAR_PROB;
                return 1f - num;
            }

            return 0.99f;
        }
    }
}