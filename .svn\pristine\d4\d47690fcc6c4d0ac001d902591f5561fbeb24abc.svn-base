using System;
using System.Windows.Forms;

namespace OCRTools
{
    internal class RichTextBoxEx : RichTextBox
    {
        protected override void WndProc(ref Message m)
        {
            if (m.Msg == 32)
            {
                DefWndProc(ref m);
                if (!Cursor.Equals(Cursors.WaitCursor))
                {
                    m.Result = (IntPtr)1;
                }
                else
                {
                    base.WndProc(ref m);
                }
            }
            else
            {
                base.WndProc(ref m);
            }
        }
    }
}
