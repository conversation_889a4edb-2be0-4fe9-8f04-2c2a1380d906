﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="panPreview.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="panPreview.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 130</value>
  </data>
  <data name="panPreview.Size" type="System.Drawing.Size, System.Drawing">
    <value>284, 65</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="panPreview.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;panPreview.Name" xml:space="preserve">
    <value>panPreview</value>
  </data>
  <data name="&gt;&gt;panPreview.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panPreview.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;panPreview.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="numScaling.Location" type="System.Drawing.Point, System.Drawing">
    <value>148, 42</value>
  </data>
  <data name="numScaling.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 21</value>
  </data>
  <data name="numScaling.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;numScaling.Name" xml:space="preserve">
    <value>numScaling</value>
  </data>
  <data name="&gt;&gt;numScaling.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numScaling.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;numScaling.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="numDPI.Location" type="System.Drawing.Point, System.Drawing">
    <value>148, 18</value>
  </data>
  <data name="numDPI.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 21</value>
  </data>
  <data name="numDPI.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;numDPI.Name" xml:space="preserve">
    <value>numDPI</value>
  </data>
  <data name="&gt;&gt;numDPI.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numDPI.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;numDPI.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label11.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label11.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label11.Location" type="System.Drawing.Point, System.Drawing">
    <value>33, 43</value>
  </data>
  <data name="label11.Size" type="System.Drawing.Size, System.Drawing">
    <value>101, 12</value>
  </data>
  <data name="label11.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="label11.Text" xml:space="preserve">
    <value>显示缩放比例 (%)</value>
  </data>
  <data name="&gt;&gt;label11.Name" xml:space="preserve">
    <value>label11</value>
  </data>
  <data name="&gt;&gt;label11.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label11.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label11.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>33, 19</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>65, 12</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>显示器 DPI</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="label1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 108</value>
  </data>
  <data name="label1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>0, 0, 0, 0</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 12</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>预览:</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="comUnits.Location" type="System.Drawing.Point, System.Drawing">
    <value>142, 105</value>
  </data>
  <data name="comUnits.Size" type="System.Drawing.Size, System.Drawing">
    <value>130, 20</value>
  </data>
  <data name="comUnits.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;comUnits.Name" xml:space="preserve">
    <value>comUnits</value>
  </data>
  <data name="&gt;&gt;comUnits.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comUnits.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;comUnits.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;btnClose.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 2</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>260, 98</value>
  </data>
  <data name="groupBox1.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="groupBox1.Text" xml:space="preserve">
    <value>显示器属性</value>
  </data>
  <data name="&gt;&gt;groupBox1.Name" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;groupBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBox1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnClose.Location" type="System.Drawing.Point, System.Drawing">
    <value>130, 67</value>
  </data>
  <data name="btnClose.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnClose.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="btnClose.Text" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="butSubmit.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="butSubmit.Location" type="System.Drawing.Point, System.Drawing">
    <value>49, 67</value>
  </data>
  <data name="butSubmit.Size" type="System.Drawing.Size, System.Drawing">
    <value>74, 22</value>
  </data>
  <data name="butSubmit.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="butSubmit.Text" xml:space="preserve">
    <value>确认</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 12</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>284, 195</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterParent</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>矫正标尺</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>CalibrationForm</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>