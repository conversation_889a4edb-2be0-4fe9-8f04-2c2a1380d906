﻿namespace OCRTools.ScreenCaptureLib
{
    public enum RegionResult
    {
        Close,
        Region,
        Fullscreen,
        Monitor,
        ActiveMonitor,
    }

    public enum NodePosition
    {
        TopLeft,
        Top,
        TopRight,
        Right,
        BottomRight,
        Bottom,
        BottomLeft,
        Left,
        Extra
    }

    public enum NodeShape
    {
        Square, Circle, Diamond, CustomNode
    }

    public enum RegionCaptureMode
    {
        Annotation,
        ScreenColorPicker,
        Ruler,
        OneClick,
        Editor,
        Magnifier,
        WhiteBoard,
    }

    public enum ShapeCategory
    {
        Region,
        Drawing,
        Effect,
        Tool
    }

    public enum ShapeType
    {
        //主菜单
        圆形区域,
        矩形区域,
        自由截图,
        截图问题反馈,
        //二级菜单
        矩形,
        圆形,
        箭头,
        画笔,
        直线,
        文字,
        文字描边,
        气泡,
        序号,
        放大镜,
        橡皮擦,
        马赛克,
        高亮,
        选择并移动,
        //裁剪
    }

    public enum BorderStyle // Localized
    {
        实线,
        虚线,
        点线,
        点划线,
        双点划线
    }

}