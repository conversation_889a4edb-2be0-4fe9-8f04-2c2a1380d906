namespace UtfUnknown.Core.Models.MultiByte
{
    public class Utf8SmModel : StateMachineModel
    {
        private static readonly int[] Utf8Cls =
        {
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 0, 0),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 0, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(2, 2, 2, 2, 3, 3, 3, 3),
            BitPackage.Pack4Bits(4, 4, 4, 4, 4, 4, 4, 4),
            BitPackage.Pack4Bits(4, 4, 4, 4, 4, 4, 4, 4),
            BitPackage.Pack4Bits(4, 4, 4, 4, 4, 4, 4, 4),
            BitPackage.Pack4Bits(5, 5, 5, 5, 5, 5, 5, 5),
            BitPackage.Pack4Bits(5, 5, 5, 5, 5, 5, 5, 5),
            BitPackage.Pack4Bits(5, 5, 5, 5, 5, 5, 5, 5),
            BitPackage.Pack4Bits(5, 5, 5, 5, 5, 5, 5, 5),
            BitPackage.Pack4Bits(0, 0, 6, 6, 6, 6, 6, 6),
            BitPackage.Pack4Bits(6, 6, 6, 6, 6, 6, 6, 6),
            BitPackage.Pack4Bits(6, 6, 6, 6, 6, 6, 6, 6),
            BitPackage.Pack4Bits(6, 6, 6, 6, 6, 6, 6, 6),
            BitPackage.Pack4Bits(7, 8, 8, 8, 8, 8, 8, 8),
            BitPackage.Pack4Bits(8, 8, 8, 8, 8, 9, 8, 8),
            BitPackage.Pack4Bits(10, 11, 11, 11, 11, 11, 11, 11),
            BitPackage.Pack4Bits(12, 13, 13, 13, 14, 15, 0, 0)
        };

        private static readonly int[] Utf8St =
        {
            BitPackage.Pack4Bits(1, 0, 1, 1, 1, 1, 12, 10),
            BitPackage.Pack4Bits(9, 11, 8, 7, 6, 5, 4, 3),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(1, 1, 5, 5, 5, 5, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 5, 5, 5, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 7, 7, 7, 7, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 7, 7, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 9, 9, 9, 9, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 9, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 12, 12, 12, 12, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 12, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 12, 12, 12, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 0, 0, 0, 0, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1)
        };

        private static readonly int[] Utf8CharLenTable =
        {
            0,
            1,
            0,
            0,
            0,
            0,
            2,
            3,
            3,
            3,
            4,
            4,
            5,
            5,
            6,
            6
        };

        public Utf8SmModel()
            : base(
                new BitPackage(BitPackage.indexShift4Bits, BitPackage.shiftMask4Bits, BitPackage.bitShift4Bits,
                    BitPackage.unitMask4Bits, Utf8Cls), 16,
                new BitPackage(BitPackage.indexShift4Bits, BitPackage.shiftMask4Bits, BitPackage.bitShift4Bits,
                    BitPackage.unitMask4Bits, Utf8St), Utf8CharLenTable, "utf-8")
        {
        }
    }
}