﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Drawing.Text;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace OCRTools.Common
{
    public class ImageProcessHelper
    {
        private const string STR_DOC_IMG_SPILT = "\"image\":\"";
        private const string STR_IMG_SPILT = "\"img\": \"";

        public static readonly string[] ImageFileExtensions =
            {"jpg", "jpeg", "png", "gif", "bmp", "ico", "tif", "tiff"};

        public static bool IsImagesEqual(Bitmap bmp1, Bitmap bmp2)
        {
            using (var unsafeBitmap1 = new UnsafeBitmap(bmp1))
            using (var unsafeBitmap2 = new UnsafeBitmap(bmp2))
            {
                return unsafeBitmap1 == unsafeBitmap2;
            }
        }

        public static Bitmap ResizeImage(Bitmap bmp, Size size, bool allowEnlarge, bool centerImage = true)
        {
            return ResizeImage(bmp, size.Width, size.Height, allowEnlarge, centerImage);
        }

        public static Bitmap ResizeImage(Bitmap bmp, int width, int height, bool allowEnlarge, bool centerImage = true)
        {
            return ResizeImage(bmp, width, height, allowEnlarge, centerImage, Color.Transparent);
        }

        public static Bitmap ResizeImage(Bitmap bmp, int width, int height, bool allowEnlarge, bool centerImage,
            Color backColor)
        {
            double ratio;
            int newWidth, newHeight;

            if (!allowEnlarge && bmp.Width <= width && bmp.Height <= height)
            {
                ratio = 1.0;
                newWidth = bmp.Width;
                newHeight = bmp.Height;
            }
            else
            {
                var ratioX = (double)width / bmp.Width;
                var ratioY = (double)height / bmp.Height;
                ratio = ratioX < ratioY ? ratioX : ratioY;
                newWidth = (int)(bmp.Width * ratio);
                newHeight = (int)(bmp.Height * ratio);
            }

            var newX = 0;
            var newY = 0;

            if (centerImage)
            {
                newX += (int)((width - bmp.Width * ratio) / 2);
                newY += (int)((height - bmp.Height * ratio) / 2);
            }

            var bmpResult = new Bitmap(width, height, PixelFormat.Format32bppArgb);
            bmpResult.SetResolution(bmp.HorizontalResolution, bmp.VerticalResolution);

            using (var g = Graphics.FromImage(bmpResult))
            {
                if (backColor.A > 0) g.Clear(backColor);

                g.SetHighQuality();
                g.DrawImage(bmp, newX, newY, newWidth, newHeight);
            }

            return bmpResult;
        }

        public static Bitmap ResizeImage(Bitmap bmp, int width, int height, Color color, bool isToCircle = true,
            bool isShadow = false, decimal shadowWidth = 8, bool isActive = false)
        {
            if (width < 1 || height < 1) // || (bmp.Width == width && bmp.Height == height))
                return bmp;

            var bmpResult = new Bitmap(width, height, PixelFormat.Format32bppArgb);
            bmpResult.SetResolution(bmp.HorizontalResolution, bmp.VerticalResolution);

            using (bmp)
            using (var g = Graphics.FromImage(bmpResult))
            {
                g.Clear(color);

                g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                g.SmoothingMode = SmoothingMode.HighQuality;
                g.PixelOffsetMode = PixelOffsetMode.HighQuality;
                g.CompositingQuality = CompositingQuality.HighQuality;
                g.CompositingMode = CompositingMode.SourceOver;
                g.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;

                using (var ia = new ImageAttributes())
                {
                    ia.SetWrapMode(WrapMode.TileFlipXY);
                    g.DrawImage(bmp, new Rectangle(0, 0, width, height), 0, 0, bmp.Width, bmp.Height,
                        GraphicsUnit.Pixel, ia);
                }
            }

            if (isToCircle) bmpResult = ToCircle(bmpResult, color);

            if (isShadow || isActive)
                return DrawImageShadow(bmpResult, isToCircle, isShadow, (int)shadowWidth, isActive);
            return bmpResult;
        }

        private static Bitmap DrawImageShadow(Bitmap bmpResult, bool isToCircle, bool isShadow = false,
            int shadowWidth = 8, bool isActive = false)
        {
            var bmpTmp = new Bitmap(bmpResult.Width + (isShadow ? shadowWidth : 0) * 2,
                bmpResult.Height + (isShadow ? shadowWidth : 0) * 2);
            using (var g = Graphics.FromImage(bmpTmp))
            {
                g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                g.SmoothingMode = SmoothingMode.HighQuality;
                g.PixelOffsetMode = PixelOffsetMode.HighQuality;
                g.CompositingQuality = CompositingQuality.HighQuality;
                g.CompositingMode = CompositingMode.SourceOver;
                g.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
                var shadowMore = isActive ? 2 : 0;

                if (isShadow)
                {
                    var normalBaseColor =
                        isToCircle ? bmpResult.GetPixel(bmpResult.Width / 2, 1) : bmpResult.GetPixel(1, 1);
                    if (normalBaseColor.A == 255)
                    {
                        var baseColor = isActive ? StaticValue.ShadowActiveColor : normalBaseColor;
                        for (var i = 1; i <= shadowWidth + shadowMore; i++)
                        {
                            if (!isActive && baseColor.A < 255)
                                continue;
                            var borderColor =
                                Color.FromArgb((int)Math.Min(255 / shadowWidth * (isActive ? 3 : 1.5), 255),
                                    baseColor);
                            using (var pen = new Pen(borderColor, 1))
                            {
                                var rect = new Rectangle(i, i, bmpTmp.Width - i * 2, bmpTmp.Height - i * 2);
                                if (isToCircle)
                                    g.DrawEllipse(pen, rect);
                                else
                                    g.DrawRectangle(pen, rect);
                            }
                        }
                    }
                }

                g.DrawImage(bmpResult
                    , new Rectangle((isShadow ? shadowWidth : 0) + shadowMore,
                        (isShadow ? shadowWidth : 0) + shadowMore, bmpResult.Width - shadowMore * 2,
                        bmpResult.Height - shadowMore * 2)
                    , 0, 0, bmpResult.Width, bmpResult.Height, GraphicsUnit.Pixel);
                return bmpTmp;
            }
        }

        public static Bitmap ToCircle(Bitmap bitmap, Color color)
        {
            var bmpResult = new Bitmap(bitmap.Width, bitmap.Height, PixelFormat.Format32bppArgb);
            using (var g = Graphics.FromImage(bmpResult))
            {
                g.Clear(color);

                g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                g.SmoothingMode = SmoothingMode.HighQuality;
                g.PixelOffsetMode = PixelOffsetMode.HighQuality;
                g.CompositingQuality = CompositingQuality.HighQuality;
                g.CompositingMode = CompositingMode.SourceOver;
                g.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;

                using (var br = new TextureBrush(bitmap, WrapMode.Clamp,
                    new RectangleF(0, 0, bitmap.Width, bitmap.Height)))
                {
                    br.ScaleTransform(1, 1);
                    g.FillEllipse(br, new Rectangle(Point.Empty, bitmap.Size));
                }
            }

            return bmpResult;
        }

        public static Bitmap CreateCheckerPattern(int width, int height, Color checkerColor1, Color checkerColor2)
        {
            var bmp = new Bitmap(width * 2, height * 2);

            using (var g = Graphics.FromImage(bmp))
            using (Brush brush1 = new SolidBrush(checkerColor1))
            using (Brush brush2 = new SolidBrush(checkerColor2))
            {
                g.FillRectangle(brush1, 0, 0, width, height);
                g.FillRectangle(brush1, width, height, width, height);
                g.FillRectangle(brush2, width, 0, width, height);
                g.FillRectangle(brush2, 0, height, width, height);
            }

            return bmp;
        }

        public static Bitmap CreateCheckerPattern(int width, int height)
        {
            return CreateCheckerPattern(width, height, SystemColors.ControlLight, SystemColors.ControlLightLight);
        }

        public static Bitmap DrawCheckers(int width, int height)
        {
            return DrawCheckers(width, height, 10, SystemColors.ControlLight, SystemColors.ControlLightLight);
        }

        public static Bitmap DrawCheckers(int width, int height, int checkerSize, Color checkerColor1,
            Color checkerColor2)
        {
            var bmp = new Bitmap(width, height);

            using (var g = Graphics.FromImage(bmp))
            using (Image checker = CreateCheckerPattern(checkerSize, checkerSize, checkerColor1, checkerColor2))
            using (Brush checkerBrush = new TextureBrush(checker, WrapMode.Tile))
            {
                g.FillRectangle(checkerBrush, new Rectangle(0, 0, bmp.Width, bmp.Height));
            }

            return bmp;
        }

        public static Bitmap ResizeImageLimit(Bitmap bmp, Size size, bool isAlph = false)
        {
            return ResizeImageLimit(bmp, size.Width, size.Height, isAlph);
        }

        /// <summary>If image size is bigger than specified size then resize it and keep aspect ratio else return image.</summary>
        public static Bitmap ResizeImageLimit(Bitmap bmp, int width, int height, bool isAlph = false)
        {
            if (bmp.Width <= width && bmp.Height <= height) return bmp;

            var ratioX = (double)width / bmp.Width;
            var ratioY = (double)height / bmp.Height;

            if (ratioX < ratioY)
                height = (int)Math.Round(bmp.Height * ratioX);
            else if (ratioX > ratioY) width = (int)Math.Round(bmp.Width * ratioY);

            return ResizeImage(bmp, width, height, isAlph);
        }

        /// <summary>
        ///     灰度（指针）
        /// </summary>
        /// <param name="curBitmpap"></param>
        /// <returns></returns>
        public static Bitmap BitToGrayByPointer(Bitmap curBitmpap)
        {
            if (curBitmpap != null)
                try
                {
                    //位图矩形
                    var rect = new Rectangle(0, 0, curBitmpap.Width, curBitmpap.Height);
                    //以可读写的方式锁定全部位图像素
                    var bmpData = curBitmpap.LockBits(rect, ImageLockMode.ReadWrite, curBitmpap.PixelFormat);

                    //启用不安全模式
                    unsafe
                    {
                        //得到首地址
                        var ptr = (byte*)bmpData.Scan0;
                        //二维图像循环
                        for (var i = 0; i < bmpData.Height; i++)
                        {
                            for (var j = 0; j < bmpData.Width; j++)
                            {
                                //利用公式计算灰度值
                                var temp = (byte)(0.299 * ptr[2] + 0.587 * ptr[1] + 0.114 * ptr[0]);
                                //R=G=B
                                ptr[0] = ptr[1] = ptr[2] = temp;
                                //指向下一个像素
                                ptr += 3;
                            }

                            //指向下一行数组的首个字节
                            ptr += bmpData.Stride - bmpData.Width * 3;
                        }
                    }

                    //解锁位图像素
                    curBitmpap.UnlockBits(bmpData);
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }

            return curBitmpap;
        }

        public static Bitmap LoadImage(string filePath)
        {
            try
            {
                if (!string.IsNullOrEmpty(filePath))
                {
                    filePath = GetAbsolutePath(filePath);

                    if (!string.IsNullOrEmpty(filePath) && IsImageFile(filePath) && File.Exists(filePath))
                    {
                        // http://stackoverflow.com/questions/788335/why-does-image-fromfile-keep-a-file-handle-open-sometimes
                        var bmp = (Bitmap)Image.FromStream(new MemoryStream(File.ReadAllBytes(filePath)));

                        RotateImageByExifOrientationData(bmp);

                        return bmp;
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            return null;
        }

        public static RotateFlipType RotateImageByExifOrientationData(Bitmap bmp, bool removeExifOrientationData = true)
        {
            var orientationId = 0x0112;
            var rotateType = RotateFlipType.RotateNoneFlipNone;

            if (bmp.PropertyIdList.Contains(orientationId))
            {
                var propertyItem = bmp.GetPropertyItem(orientationId);
                rotateType = GetRotateFlipTypeByExifOrientationData(propertyItem.Value[0]);

                if (rotateType != RotateFlipType.RotateNoneFlipNone)
                {
                    bmp.RotateFlip(rotateType);

                    if (removeExifOrientationData) bmp.RemovePropertyItem(orientationId);
                }
            }

            return rotateType;
        }

        private static RotateFlipType GetRotateFlipTypeByExifOrientationData(int orientation)
        {
            switch (orientation)
            {
                default:
                    return RotateFlipType.RotateNoneFlipNone;
                case 2:
                    return RotateFlipType.RotateNoneFlipX;
                case 3:
                    return RotateFlipType.Rotate180FlipNone;
                case 4:
                    return RotateFlipType.Rotate180FlipX;
                case 5:
                    return RotateFlipType.Rotate90FlipX;
                case 6:
                    return RotateFlipType.Rotate90FlipNone;
                case 7:
                    return RotateFlipType.Rotate270FlipX;
                case 8:
                    return RotateFlipType.Rotate270FlipNone;
            }
        }

        public static string GetAbsolutePath(string path)
        {
            path = ExpandFolderVariables(path);

            if (!Path.IsPathRooted(path)) // Is relative path?
                path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, path);

            return Path.GetFullPath(path);
        }

        public static T[] GetEnums<T>()
        {
            return (T[])Enum.GetValues(typeof(T));
        }

        public static string ExpandFolderVariables(string path)
        {
            if (!string.IsNullOrEmpty(path))
                try
                {
                    foreach (var specialFolder in GetEnums<Environment.SpecialFolder>())
                        path = path.Replace($"%{specialFolder}%", Environment.GetFolderPath(specialFolder),
                            StringComparison.OrdinalIgnoreCase);

                    path = Environment.ExpandEnvironmentVariables(path);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }

            return path;
        }

        public static string GetFilenameExtension(string filePath, bool includeDot = false,
            bool checkSecondExtension = true)
        {
            var extension = "";

            if (!string.IsNullOrEmpty(filePath))
            {
                var pos = filePath.LastIndexOf('.');

                if (pos >= 0)
                {
                    extension = filePath.Substring(pos + 1);

                    if (checkSecondExtension)
                    {
                        filePath = filePath.Remove(pos);
                        var extension2 = GetFilenameExtension(filePath, false, false);

                        if (!string.IsNullOrEmpty(extension2))
                            foreach (var knownExtension in new[] { "tar" })
                                if (extension2.Equals(knownExtension, StringComparison.OrdinalIgnoreCase))
                                {
                                    extension = extension2 + "." + extension;
                                    break;
                                }
                    }

                    if (includeDot) extension = "." + extension;
                }
            }

            return extension;
        }

        public static bool CheckExtension(string filePath, IEnumerable<string> extensions)
        {
            var ext = GetFilenameExtension(filePath);

            if (!string.IsNullOrEmpty(ext))
                return extensions.Any(x => ext.Equals(x, StringComparison.OrdinalIgnoreCase));

            return false;
        }

        public static bool IsImageFile(string filePath)
        {
            return CheckExtension(filePath, ImageFileExtensions);
        }

        /// <summary>
        ///     对比度增强（指针）
        /// </summary>
        /// <returns></returns>
        public static unsafe Bitmap BitContrastByPointer(Bitmap bitmap, int degree = 90)
        {
            if (bitmap != null)
                try
                {
                    var num2 = (100.0 + degree) / 100.0;
                    num2 *= num2;
                    var width = bitmap.Width;
                    var height = bitmap.Height;
                    var bitmapdata = bitmap.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.ReadWrite,
                        PixelFormat.Format24bppRgb);
                    var numPtr = (byte*)bitmapdata.Scan0;

                    var offset = bitmapdata.Stride - width * 3;
                    for (var i = 0; i < height; i++)
                    {
                        for (var j = 0; j < width; j++)
                        {
                            for (var k = 0; k < 3; k++)
                            {
                                var num = ((numPtr[k] / 255.0 - 0.5) * num2 + 0.5) * 255.0;
                                if (num < 0.0) num = 0.0;
                                if (num > 255.0) num = 255.0;
                                numPtr[k] = (byte)num;
                            }

                            numPtr += 3;
                        }

                        numPtr += offset;
                    }

                    bitmap.UnlockBits(bitmapdata);
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }

            return bitmap;
        }

        /// <summary>
        ///     反色（指针）
        /// </summary>
        /// <param name="curBitmpap"></param>
        /// <returns></returns>
        public static Bitmap BitInverseByPointer(Bitmap curBitmpap)
        {
            if (curBitmpap != null)
                try
                {
                    var srcdat = curBitmpap.LockBits(new Rectangle(Point.Empty, curBitmpap.Size),
                        ImageLockMode.ReadWrite, PixelFormat.Format24bppRgb); // 锁定位图
                    unsafe // 不安全代码
                    {
                        var pix = (byte*)srcdat.Scan0; // 像素首地址
                        for (var i = 0; i < srcdat.Stride * srcdat.Height; i++) pix[i] = (byte)(255 - pix[i]);
                        curBitmpap.UnlockBits(srcdat); // 解锁
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }

            return curBitmpap;
        }

        public static Bitmap CropBitmap(Bitmap bmp, Rectangle rect)
        {
            if (bmp != null && rect.X >= 0 && rect.Y >= 0 && rect.Width > 0 && rect.Height > 0 &&
                new Rectangle(0, 0, bmp.Width, bmp.Height).Contains(rect)) return bmp.Clone(rect, bmp.PixelFormat);

            return null;
        }

        public static Bitmap CombineImages(IEnumerable<Image> images, Orientation orientation = Orientation.Vertical,
            ImageCombinerAlignment alignment = ImageCombinerAlignment.LeftOrTop, int space = 0)
        {
            int width, height;
            var imageCount = images.Count();
            var spaceSize = space * (imageCount - 1);

            if (orientation == Orientation.Vertical)
            {
                width = images.Max(x => x.Width);
                height = images.Sum(x => x.Height) + spaceSize;
            }
            else
            {
                width = images.Sum(x => x.Width) + spaceSize;
                height = images.Max(x => x.Height);
            }

            var bmp = new Bitmap(width, height);

            using (var g = Graphics.FromImage(bmp))
            {
                g.SetHighQuality();
                var position = 0;

                foreach (var image in images)
                {
                    Rectangle rect;

                    if (orientation == Orientation.Vertical)
                    {
                        int x;
                        switch (alignment)
                        {
                            default:
                                x = 0;
                                break;
                            case ImageCombinerAlignment.Center:
                                x = width / 2 - image.Width / 2;
                                break;
                            case ImageCombinerAlignment.RightOrBottom:
                                x = width - image.Width;
                                break;
                        }

                        rect = new Rectangle(x, position, image.Width, image.Height);
                        position += image.Height + space;
                    }
                    else
                    {
                        int y;
                        switch (alignment)
                        {
                            default:
                                y = 0;
                                break;
                            case ImageCombinerAlignment.Center:
                                y = height / 2 - image.Height / 2;
                                break;
                            case ImageCombinerAlignment.RightOrBottom:
                                y = height - image.Height;
                                break;
                        }

                        rect = new Rectangle(position, y, image.Width, image.Height);
                        position += image.Width + space;
                    }

                    g.DrawImage(image, rect);
                }
            }

            return bmp;
        }

        internal static Image ProcessImage(Bitmap originImage, ImageProcessType imgType)
        {
            Image img = originImage;
            switch (imgType)
            {
                case ImageProcessType.原图灰度:
                    img = BitToGrayByPointer(originImage);
                    break;
                case ImageProcessType.底片效果:
                    img = BitInverseByPointer(originImage);
                    break;
                case ImageProcessType.原图增强:
                    img = BitContrastByPointer(originImage);
                    break;
                case ImageProcessType.中值滤波:
                    img = MedianFilterByMemory(originImage);
                    break;
                case ImageProcessType.均值滤波:
                    img = AverageFilterByMemory(originImage);
                    break;
                case ImageProcessType.Outlier滤波:
                    img = OutlierFilter(originImage);
                    break;
                case ImageProcessType.高斯滤波:
                    img = GaussianFilter(originImage);
                    break;
                case ImageProcessType.高斯平滑:
                    img = Smooth(originImage);
                    break;
                case ImageProcessType.拉普拉斯锐化:
                    img = LaplacianSharpen(originImage);
                    break;
                case ImageProcessType.伪彩色增强:
                    img = BitToColorByMemory(originImage);
                    break;
                case ImageProcessType.文档矫正:
                    img = EnhanceDocImage(ImageToBase64(originImage));
                    break;
                case ImageProcessType.图片增强:
                    img = EnhanceImage(ImageToBase64(originImage));
                    break;
            }

            return img;
        }

        /// <summary>
        ///     文档图像矫正
        /// </summary>
        /// <param name="strBase64"></param>
        /// <returns></returns>
        public static Bitmap EnhanceDocImage(string strBase64)
        {
            var result = "";
            var strPost = "{\"request_id\":\"" + Guid.NewGuid() + "\",\"media\":\"" + strBase64
                          + "\",\"appid\":\"10200\",\"enhance_flag\":2}";

            var strTmp = WebClientExt.GetHtml("https://s.youtux.qq.com/EnhanceDocImage", "", "", strPost, "", 10);

            //{"code":0,"message":"ok","request_id":"0.5384177315701995","data":{"image":"/9j/4AAQSkZJRgABAQAAAQ"}}
            if (!string.IsNullOrEmpty(strTmp) && strTmp.Contains(STR_DOC_IMG_SPILT))
            {
                result = strTmp.Substring(strTmp.IndexOf(STR_DOC_IMG_SPILT) + STR_DOC_IMG_SPILT.Length);
                result = result.Substring(0, result.IndexOf("\""));
            }

            if (!string.IsNullOrEmpty(result)) return Base64StringToImage(result);
            return null;
        }

        private static string ImageToBase64(Image image)
        {
            var byts = ImageToByte(image);
            return byts?.Length > 0 ? Convert.ToBase64String(byts) : null;
        }

        public static byte[] ImageToByte(Image image)
        {
            using (var ms = new MemoryStream())
            {
                if (image.RawFormat.Guid == ImageFormat.Gif.Guid)
                    image.Save(ms, ImageFormat.Gif);
                else if (image.RawFormat.Guid == ImageFormat.Bmp.Guid)
                    image.Save(ms, ImageFormat.Bmp);
                else if (image.RawFormat.Guid == ImageFormat.Png.Guid)
                    image.Save(ms, ImageFormat.Png);
                else if (image.RawFormat.Guid == ImageFormat.Tiff.Guid)
                    image.Save(ms, ImageFormat.Tiff);
                else
                    image.Save(ms, ImageFormat.Jpeg);

                return ms.ToArray();
            }
        }

        private static Bitmap Base64StringToImage(string base64Img)
        {
            Bitmap bmp = null;
            try
            {
                using (var ms = new MemoryStream())
                {
                    var bytes = Convert.FromBase64String(base64Img);
                    ms.Write(bytes, 0, bytes.Length);
                    bmp = new Bitmap(ms);
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }

            return bmp;
        }

        /// <summary>
        ///     照片图像增强
        /// </summary>
        /// <param name="strBase64"></param>
        /// <returns></returns>
        public static Bitmap EnhanceImage(string strBase64)
        {
            var result = "";
            var strPost = strBase64;

            var strTmp = WebClientExt.GetHtml("https://cvpr2018deblur.youtux.qq.com/enhancement_b64/",
                "svc_openid=" + Guid.NewGuid().ToString().Replace("-", ""), "", strPost, "", 10);

            //{"code":0,"message":"ok","request_id":"0.5384177315701995","data":{"image":"/9j/4AAQSkZJRgABAQAAAQ"}}
            if (!string.IsNullOrEmpty(strTmp) && strTmp.Contains(STR_IMG_SPILT))
            {
                result = strTmp.Substring(strTmp.IndexOf(STR_IMG_SPILT) + STR_IMG_SPILT.Length);
                result = result.Substring(0, result.IndexOf("\""));
            }

            if (!string.IsNullOrEmpty(result)) return Base64StringToImage(result);
            return null;
        }

        /// <summary>
        ///     彩色（内存）
        /// </summary>
        /// <param name="curBitmpap"></param>
        /// <param name="method">使用何种方法，false强度分层法,true灰度级-彩色变换法</param>
        /// <param name="seg">强度分层中的分层数</param>
        /// <returns></returns>
        public static Bitmap BitToColorByMemory(Bitmap curBitmpap, bool method = true, int seg = 10)
        {
            if (curBitmpap != null)
                try
                {
                    //if (PixelFormat.Format24bppRgb == curBitmpap.PixelFormat)
                    //{
                    var rect = new Rectangle(0, 0, curBitmpap.Width, curBitmpap.Height);
                    var bmpData = curBitmpap.LockBits(rect, ImageLockMode.ReadWrite, curBitmpap.PixelFormat);
                    var ptr = bmpData.Scan0;
                    var bytes = curBitmpap.Width * curBitmpap.Height * 3;
                    var grayValues = new byte[bytes];
                    Marshal.Copy(ptr, grayValues, 0, bytes);
                    curBitmpap.UnlockBits(bmpData);

                    var rgbValues = new byte[bytes];
                    //清零
                    Array.Clear(rgbValues, 0, bytes);

                    if (method == false)
                        //强度分层法
                        for (var i = 0; i < bytes; i += 3)
                        {
                            var ser = (byte)(256 / seg);
                            var tempB = (byte)(grayValues[i] / ser);
                            //分配任意一种颜色
                            rgbValues[i + 1] = (byte)(tempB * ser);
                            rgbValues[i] = (byte)((seg - 1 - tempB) * ser);
                            rgbValues[i + 2] = 0;
                        }
                    else
                        //灰度级-彩色变换法
                        for (var i = 0; i < bytes; i += 3)
                            if (grayValues[i] < 64)
                            {
                                rgbValues[i + 2] = 0;
                                rgbValues[i + 1] = (byte)(4 * grayValues[i]);
                                rgbValues[i] = 255;
                            }
                            else if (grayValues[i] < 128)
                            {
                                rgbValues[i + 2] = 0;
                                rgbValues[i + 1] = 255;
                                rgbValues[i] = (byte)(-4 * grayValues[i] + 2 * 255);
                            }
                            else if (grayValues[i] < 192)
                            {
                                rgbValues[i + 2] = (byte)(4 * grayValues[i] - 2 * 255);
                                rgbValues[i + 1] = 255;
                                rgbValues[i] = 0;
                            }
                            else
                            {
                                rgbValues[i + 2] = 255;
                                rgbValues[i + 1] = (byte)(-4 * grayValues[i] + 4 * 255);
                                rgbValues[i] = 0;
                            }

                    curBitmpap = new Bitmap(curBitmpap.Width, curBitmpap.Height, PixelFormat.Format24bppRgb);
                    bmpData = curBitmpap.LockBits(rect, ImageLockMode.ReadWrite, curBitmpap.PixelFormat);
                    ptr = bmpData.Scan0;

                    Marshal.Copy(rgbValues, 0, ptr, bytes);
                    curBitmpap.UnlockBits(bmpData);

                    //}
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }

            return curBitmpap;
        }

        //中值滤波器
        public static Bitmap MedianFilterByMemory(Bitmap oriImage)
        {
            if (oriImage != null)
                try
                {
                    var width = oriImage.Width;
                    var height = oriImage.Height;
                    //创建新的bitmap存放滤波后的图
                    var newImageData = oriImage.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.ReadWrite,
                        oriImage.PixelFormat);

                    var stride = newImageData.Stride;
                    var intPtr = newImageData.Scan0;
                    var size = stride * height;
                    var newBytes = new byte[size];
                    //先复制一份原图的数据到滤波数组中
                    Marshal.Copy(intPtr, newBytes, 0, size);
                    var mask = new int[9];

                    var k = 3;
                    for (var y = 0; y < height - 2; y++)
                        for (var x = 0; x < width - 2; x++)
                        {
                            mask[0] = newBytes[y * stride + x * k];
                            mask[1] = newBytes[y * stride + x * k + 3];
                            mask[2] = newBytes[y * stride + x * k + 6];

                            mask[3] = newBytes[(y + 1) * stride + x * k];
                            mask[4] = newBytes[(y + 1) * stride + x * k + 3];
                            mask[5] = newBytes[(y + 1) * stride + x * k + 6];

                            mask[6] = newBytes[(y + 2) * stride + x * k];
                            mask[7] = newBytes[(y + 2) * stride + x * k + 3];
                            mask[8] = newBytes[(y + 2) * stride + x * k + 6];

                            Array.Sort(mask);
                            var median = mask[4];

                            //newImageData.Stride 是等于 stride 的
                            newBytes[(y + 1) * stride + x * k + 3] = (byte)median;
                            newBytes[(y + 1) * stride + x * k + 4] = (byte)median;
                            newBytes[(y + 1) * stride + x * k + 5] = (byte)median;
                        }

                    Marshal.Copy(newBytes, 0, intPtr, size);
                    oriImage.UnlockBits(newImageData);
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }

            return oriImage;
        }

        //均值滤波器
        public static Bitmap AverageFilterByMemory(Bitmap oriImage)
        {
            if (oriImage != null)
                try
                {
                    var width = oriImage.Width;
                    var height = oriImage.Height;
                    //创建新的bitmap存放滤波后的图
                    var newImageData = oriImage.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.ReadWrite,
                        oriImage.PixelFormat);

                    var intPtrN = newImageData.Scan0;
                    var stride = newImageData.Stride;
                    var size = newImageData.Stride * height;
                    var newBytes = new byte[size];
                    Marshal.Copy(intPtrN, newBytes, 0, size);
                    var mask = new int[9];

                    var k = 3;
                    for (var y = 0; y < height - 2; y++)
                        for (var x = 0; x < width - 2; x++)
                        {
                            mask[0] = newBytes[y * stride + x * k];
                            mask[1] = newBytes[y * stride + x * k + 3];
                            mask[2] = newBytes[y * stride + x * k + 6];

                            mask[3] = newBytes[(y + 1) * stride + x * k];
                            mask[4] = newBytes[(y + 1) * stride + x * k + 3];
                            mask[5] = newBytes[(y + 1) * stride + x * k + 6];

                            mask[6] = newBytes[(y + 2) * stride + x * k];
                            mask[7] = newBytes[(y + 2) * stride + x * k + 3];
                            mask[8] = newBytes[(y + 2) * stride + x * k + 6];

                            var mean = (mask[0] + mask[1] + mask[2] + mask[3] + mask[4] + mask[5] + mask[6] + mask[7] +
                                        mask[8]) / 9;

                            //newImageData.Stride 是等于 stride 的
                            newBytes[(y + 1) * stride + x * k + 3] = (byte)mean;
                            newBytes[(y + 1) * stride + x * k + 4] = (byte)mean;
                            newBytes[(y + 1) * stride + x * k + 5] = (byte)mean;
                        }

                    Marshal.Copy(newBytes, 0, intPtrN, size);
                    oriImage.UnlockBits(newImageData);
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }

            return oriImage;
        }

        //高斯滤波
        public static Bitmap GaussianFilter(Bitmap basemap)
        {
            var width = basemap.Width;
            var height = basemap.Height;
            var retmap = new Bitmap(basemap, width, height);
            int i, j;
            for (i = 1; i <= width - 2; i++)
                for (j = 1; j <= height - 2; j++)
                {
                    //提取9个格子的灰度值
                    var c1 = basemap.GetPixel(i, j - 1);
                    var c2 = basemap.GetPixel(i - 1, j);
                    var c3 = basemap.GetPixel(i, j);
                    var c4 = basemap.GetPixel(i + 1, j);
                    var c5 = basemap.GetPixel(i, j + 1);
                    var c6 = basemap.GetPixel(i - 1, j - 1);
                    var c7 = basemap.GetPixel(i - 1, j + 1);
                    var c8 = basemap.GetPixel(i + 1, j - 1);
                    var c9 = basemap.GetPixel(i + 1, j + 1);
                    int r1 = c1.R;
                    int r2 = c2.R;
                    int r3 = c3.R;
                    int r4 = c4.R;
                    int r5 = c5.R;
                    int r6 = c6.R;
                    int r7 = c7.R;
                    int r8 = c8.R;
                    int r9 = c9.R;
                    //求高斯滤波后的值
                    var fxr = (r6 + r7 + r8 + r9 + 2 * r1 + 2 * r2 + 2 * r4 + 2 * r5 + 4 * r3) / 16;
                    var rr = fxr;
                    if (rr < 0) rr = 0;
                    if (rr > 255) rr = 255;
                    var cc = Color.FromArgb(rr, rr, rr);
                    retmap.SetPixel(i, j, cc);
                }

            return retmap;
        }

        /// <summary>
        ///     对图像进行平滑处理（利用高斯平滑Gaussian Blur）
        /// </summary>
        /// <param name="bitmap">要处理的位图</param>
        /// <returns>返回平滑处理后的位图</returns>
        public static Bitmap Smooth(Bitmap bitmap)
        {
            //初始化高斯模糊卷积核
            var k = 273;
            var gaussianBlur = new[,]
            {
                {(double) 1 / k, (double) 4 / k, (double) 7 / k, (double) 4 / k, (double) 1 / k},
                {(double) 4 / k, (double) 16 / k, (double) 26 / k, (double) 16 / k, (double) 4 / k},
                {(double) 7 / k, (double) 26 / k, (double) 41 / k, (double) 26 / k, (double) 7 / k},
                {(double) 4 / k, (double) 16 / k, (double) 26 / k, (double) 16 / k, (double) 4 / k},
                {(double) 1 / k, (double) 4 / k, (double) 7 / k, (double) 4 / k, (double) 1 / k}
            };

            var inputPicture = new int[3, bitmap.Width, bitmap.Height]; //以GRB以及位图的长宽建立整数输入的位图的数组

            Color color; //储存某一像素的颜色
            //循环使得InputPicture数组得到位图的RGB
            for (var i = 0; i < bitmap.Width; i++)
                for (var j = 0; j < bitmap.Height; j++)
                {
                    color = bitmap.GetPixel(i, j);
                    inputPicture[0, i, j] = color.R;
                    inputPicture[1, i, j] = color.G;
                    inputPicture[2, i, j] = color.B;
                }

            var smooth = new Bitmap(bitmap.Width, bitmap.Height); //创建新位图
            //循环计算使得OutputPicture数组得到计算后位图的RGB
            for (var i = 0; i < bitmap.Width; i++)
                for (var j = 0; j < bitmap.Height; j++)
                {
                    var R = 0;
                    var G = 0;
                    var B = 0;

                    //每一个像素计算使用高斯模糊卷积核进行计算
                    for (var r = 0; r < 5; r++) //循环卷积核的每一行
                        for (var f = 0; f < 5; f++) //循环卷积核的每一列
                        {
                            //控制与卷积核相乘的元素
                            var row = i - 2 + r;
                            var index = j - 2 + f;

                            //当超出位图的大小范围时，选择最边缘的像素值作为该点的像素值
                            row = row < 0 ? 0 : row;
                            index = index < 0 ? 0 : index;
                            row = row >= bitmap.Width ? bitmap.Width - 1 : row;
                            index = index >= bitmap.Height ? bitmap.Height - 1 : index;

                            //输出得到像素的RGB值
                            R += (int)(gaussianBlur[r, f] * inputPicture[0, row, index]);
                            G += (int)(gaussianBlur[r, f] * inputPicture[1, row, index]);
                            B += (int)(gaussianBlur[r, f] * inputPicture[2, row, index]);
                        }

                    color = Color.FromArgb(R, G, B); //颜色结构储存该点RGB
                    smooth.SetPixel(i, j, color); //位图存储该点像素值
                }

            return smooth;
        }

        //Outlier滤波器
        public static Bitmap OutlierFilter(Bitmap oriImage)
        {
            var threshold = 30;
            var width = oriImage.Width;
            var height = oriImage.Height;

            var oriImageData = oriImage.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.ReadWrite,
                PixelFormat.Format24bppRgb);
            //创建新的bitmap存放滤波后的图
            var newImage = new Bitmap(width, height, PixelFormat.Format24bppRgb);
            var newImageData = newImage.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.ReadWrite,
                PixelFormat.Format24bppRgb);

            var intPtrN = newImageData.Scan0;
            var intPtr = oriImageData.Scan0;
            var size = oriImageData.Stride * height;
            var oriBytes = new byte[size];
            var newBytes = new byte[size];
            Marshal.Copy(intPtr, oriBytes, 0, size);
            //先复制一份一模一样的数据到滤波数组中
            Marshal.Copy(intPtr, newBytes, 0, size);
            var mask = new int[9];

            var k = 3;
            for (var y = 0; y < height - 2; y++)
                for (var x = 0; x < width - 2; x++)
                {
                    //因为是灰阶图RGB相同，bitmapdata数组中就以每3个pixel为间隔。
                    mask[0] = oriBytes[y * oriImageData.Stride + x * k];
                    mask[1] = oriBytes[y * oriImageData.Stride + x * k + 3];
                    mask[2] = oriBytes[y * oriImageData.Stride + x * k + 6];

                    mask[3] = oriBytes[(y + 1) * oriImageData.Stride + x * k];
                    mask[4] = oriBytes[(y + 1) * oriImageData.Stride + x * k + 3];
                    mask[5] = oriBytes[(y + 1) * oriImageData.Stride + x * k + 6];

                    mask[6] = oriBytes[(y + 2) * oriImageData.Stride + x * k];
                    mask[7] = oriBytes[(y + 2) * oriImageData.Stride + x * k + 3];
                    mask[8] = oriBytes[(y + 2) * oriImageData.Stride + x * k + 6];

                    var mean = (mask[0] + mask[1] + mask[2] + mask[3] + mask[5] + mask[6] + mask[7] + mask[8]) / 8;

                    //绝对值很重要，不要忘
                    if (Math.Abs(mask[4] - mean) > threshold)
                    {
                        //newImageData.Stride 是等于 oriImageData.Stride 的
                        newBytes[(y + 1) * oriImageData.Stride + x * k + 3] = (byte)mean;
                        newBytes[(y + 1) * oriImageData.Stride + x * k + 4] = (byte)mean;
                        newBytes[(y + 1) * oriImageData.Stride + x * k + 5] = (byte)mean;
                    }
                }

            Marshal.Copy(newBytes, 0, intPtrN, size);
            oriImage.UnlockBits(oriImageData);
            newImage.UnlockBits(newImageData);

            return newImage;
        }

        /// <summary>
        ///     拉普拉斯锐化
        /// </summary>
        /// <param name="basemap"></param>
        /// <returns></returns>
        private static Bitmap LaplacianSharpen(Bitmap basemap)
        {
            var width = basemap.Width;
            var height = basemap.Height;
            var retmap = new Bitmap(basemap, width, height);
            //拉普拉斯模板
            //a=1
            int[] laplacian = { -1, -1, -1, -1, 9, -1, -1, -1, -1 };
            int i, j;

            for (i = 1; i <= width - 2; i++)
                for (j = 1; j <= height - 2; j++)
                {
                    var r = 0;
                    var index = 0;
                    for (var col = -1; col <= 1; col++)
                        for (var row = -1; row <= 1; row++)
                        {
                            var pixel = basemap.GetPixel(i + row, j + col);
                            r += pixel.R * laplacian[index];
                            index++;
                        }

                    if (r < 0) r = 0;
                    if (r > 255) r = 255;
                    var cc = Color.FromArgb(r, r, r);
                    retmap.SetPixel(i, j, cc);
                }

            return retmap;
        }
    }

    public enum ImageCombinerAlignment
    {
        LeftOrTop,
        Center,
        RightOrBottom
    }

    internal enum ImageProcessType
    {
        原始图片 = 0,
        原图灰度 = 1,
        底片效果 = 2,
        原图增强 = 3,
        高斯平滑 = 4,
        中值滤波 = 5,
        均值滤波 = 6,
        高斯滤波 = 7,
        Outlier滤波 = 8,
        拉普拉斯锐化 = 9,
        伪彩色增强 = 10,
        文档矫正 = 50,
        图片增强 = 51
    }
}