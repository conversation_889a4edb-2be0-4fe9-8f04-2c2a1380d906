using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class BACKUP : Record
	{
		public ushort CreateBackupOnSaving;

		public BACKUP(Record record)
			: base(record)
		{
		}

		public BACKUP()
		{
			Type = 64;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			CreateBackupOnSaving = binaryReader.ReadUInt16();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(CreateBackupOnSaving);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
