using System.Collections.Generic;

namespace ExcelLibrary.BinaryFileFormat
{
	public class ErrorCode
	{
		public byte Code;

		public string Value;

		public static Dictionary<byte, ErrorCode> ErrorCodes;

		private ErrorCode(byte code, string value)
		{
			Code = code;
			Value = value;
		}

		public override string ToString()
		{
			return Value;
		}

		static ErrorCode()
		{
			ErrorCodes = new Dictionary<byte, ErrorCode>();
			AddErrorCode(0, "#NULL!");
			AddErrorCode(7, "#DIV/0!");
			AddErrorCode(15, "#VALUE!");
			AddErrorCode(23, "#REF!");
			AddErrorCode(29, "#NAME?");
			AddErrorCode(36, "#NUM!");
			AddErrorCode(42, "#N/A!");
		}

		private static void AddErrorCode(byte code, string value)
		{
			ErrorCode value2 = new ErrorCode(code, value);
			ErrorCodes.Add(code, value2);
		}
	}
}
