using System.Windows.Forms;
using OCRTools.Properties;

namespace OCRTools
{
    public static class CursorEx
    {
        //public static Cursor Arrow = ImageHelp.SetCursor(Resources.Cursor_arrow);

        //public static Cursor Cross = ImageHelp.SetCursor(Resources.Cursor_cross);

        //public static Cursor EW = ImageHelp.SetCursor(Resources.Cursor_ew);

        //public static Cursor Move = ImageHelp.SetCursor(Resources.Cursor_move);

        //public static Cursor NESW = ImageHelp.SetCursor(Resources.Cursor_nesw);

        //public static Cursor NS = ImageHelp.SetCursor(Resources.Cursor_ns);

        //public static Cursor NWSE = ImageHelp.SetCursor(Resources.Cursor_nwse);

        //public static Cursor None = ImageHelp.SetCursor(Resources.Cursor_none);

        //public static Cursor Hand = ImageHelp.SetCursor(Resources.hand);


        public static Cursor Arrow = ImageHelp.SetCursor(Resources.normal);

        public static Cursor Cross = ImageHelp.SetCursor(Resources.cross);

        public static Cursor EW = Cursors.SizeWE; // ImageHelp.SetCursor(Resources.Cursor_ew);

        public static Cursor Move = ImageHelp.SetCursor(Resources.move);

        public static Cursor NESW = Cursors.SizeNESW; // ImageHelp.SetCursor(Resources.Cursor_nesw);

        public static Cursor NS = Cursors.SizeNS; // ImageHelp.SetCursor(Resources.Cursor_ns);

        public static Cursor NWSE = Cursors.SizeNWSE; // ImageHelp.SetCursor(Resources.Cursor_nwse);

        public static Cursor None = ImageHelp.SetCursor(Resources.no);

        public static Cursor Hand = ImageHelp.SetCursor(Resources.hand);

        //public static Cursor Arrow = Cursors.Arrow;

        //public static Cursor Cross = Cursors.Cross;

        //public static Cursor EW = Cursors.AppStarting;

        //public static Cursor Move = Cursors.NoMove2D;

        //public static Cursor NESW = Cursors.SizeNESW;

        //public static Cursor NS = Cursors.SizeNS;

        //public static Cursor NWSE = Cursors.SizeNWSE;

        //public static Cursor None = Cursors.No;

        //public static Cursor Hand = Cursors.Hand;
    }
}