﻿using MetroFramework.Forms;
using System;
using System.Windows.Forms;

namespace OCRTools.NewForms
{
    public partial class FormPdfProcess : MetroForm
    {
        public FormPdfProcess()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;

            tipMsg.AutoPopDelay = 5000;
            //设置鼠标停在该控件后，再停多长时间显示说眀性文字
            tipMsg.InitialDelay = 100;
            //设置鼠标从一个控件移到叧一个啌件再次显示该说明性文牸哋时间间隔
            tipMsg.ReshowDelay = 200;
            //蔎置是否显示窗体的说明性文字
            tipMsg.ShowAlways = true;
            tipMsg.ToolTipTitle = "功能描述";
            tipMsg.ToolTipIcon = ToolTipIcon.Info;
        }

        public bool IsProcessByImage { get; set; }

        public bool IsSpiltImage { get; set; }

        private void FormAreaCapture_Load(object sender, EventArgs e)
        {
            chkImage.Checked = IsProcessByImage;
            chkSpilt.Checked = IsSpiltImage;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            IsProcessByImage = chkImage.Checked;
            IsSpiltImage = chkSpilt.Checked;
            DialogResult = DialogResult.OK;
        }
    }

}