﻿using System;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace UpdateFile
{
    public partial class FormUpdate : Form
    {
        private string strMainFile = string.Empty;
        private string strMainProcessName = "OCR助手";
        private string strTmpFile = string.Empty;
        private string strDownUrl = string.Empty;
        private string strAppPath = Application.StartupPath.TrimEnd('\\');

        public FormUpdate(string url = null)
        {
            InitializeComponent();
            strDownUrl = url;
            CheckForIllegalCrossThreadCalls = false;
        }

        private void FormUpdate_Load(object sender, EventArgs e)
        {
            this.Location = new Point(Screen.PrimaryScreen.WorkingArea.Width - this.Width
                , Screen.PrimaryScreen.WorkingArea.Height - this.Height);
            this.Text = string.Format("{0} 更新程序", strMainProcessName);
            this.TopMost = true;
            Task.Factory.StartNew(() =>
            {
                InitFile();
            });
        }

        private void tmrTick_Tick(object sender, EventArgs e)
        {
            try
            {
                KillProcess();
            }
            catch { }
        }

        private bool DownLoadCache()
        {
            if (string.IsNullOrEmpty(strDownUrl))
            {
                return true;
            }
            var tmpCacheFile = strAppPath + "\\tmp.cache";
            try
            {
                if (File.Exists(tmpCacheFile))
                    File.Delete(tmpCacheFile);
                using (Stream so = new FileStream(tmpCacheFile, FileMode.Create))
                {
                }
            }
            catch (Exception oe)
            {
                lblNowProcess.Text = "更新失败:" + oe.Message;
                return false;
            }

            return DownloadFile(strDownUrl, tmpCacheFile, lblNowProcess);
        }

        /// <summary>
        ///     c#,.net 下载文件
        /// </summary>
        /// <param name="URL">下载文件地址</param>
        /// <param name="Filename">下载后的存放地址</param>
        /// <param name="Prog">用于显示的进度条</param>
        public bool DownloadFile(string URL, string filename, Label label1)
        {
            var result = false;
            try
            {
                var client = new WebClient() { Proxy = null };
                client.DownloadProgressChanged += (sender, e) =>
                {
                    if (label1 != null)
                        label1.Text = "已下载" + e.ProgressPercentage + "%，请稍后…";
                    Application.DoEvents();
                };
                client.DownloadFileAsync(new Uri(URL), filename);
                while (client.IsBusy)
                {
                    Thread.Sleep(1000);
                }
                result = true;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return result;
        }

        private void InitFile()
        {
            var downloaded = DownLoadCache();
            Application.DoEvents();
            strTmpFile = strAppPath + "\\tmp.cache";
            if (downloaded && File.Exists(strTmpFile))
            {
                tmrTick.Enabled = true;
                new System.Threading.Thread(() =>
                {
                    int sleepTime = 3;
                    for (int i = 0; i < sleepTime; i++)
                    {
                        lblNowProcess.Text = string.Format("{0}秒后开始更新……", sleepTime - i);
                        Application.DoEvents();
                        System.Threading.Thread.Sleep(1000);
                    }
                    strMainFile = strAppPath + "\\" + strMainProcessName + ".exe";

                    KillProcess();
                    //MessageBox.Show("CopyFile");
                    CopyFile();
                    //MessageBox.Show("StartMainFile");
                    StartMainFile();
                    //MessageBox.Show(strMainFile);
                    Application.Exit();
                    System.Environment.Exit(0);

                })//{ IsBackground = true, Priority = System.Threading.ThreadPriority.Highest }
                .Start();
            }
            else
            {
                Application.Exit();
                System.Environment.Exit(0);
            }
        }

        private void CopyFile()
        {
            lblNowProcess.Text = "正在拷贝更新文件…";
            Application.DoEvents();
            try
            {
                if (File.Exists(strTmpFile))
                {
                    //FileInfo info = new FileInfo(tmpFile);
                    //if (info != null && info.Length > 0)
                    //{
                    File.Copy(strTmpFile, strMainFile, true);
                    //}
                    //else
                    //{
                    //    File.Delete(strMainFile);
                    //    File.Delete(strMainFile);
                    //}
                }
            }
            catch
            {
                KillProcess();
                try
                {
                    File.Copy(strTmpFile, strMainFile, true);
                }
                catch { }
            }
        }

        private void KillProcess()
        {
            try
            {
                Process[] Processes = Process.GetProcessesByName(strMainProcessName);
                while (Processes != null && Processes.Length > 0)
                {
                    try
                    {
                        Processes[0].Kill();
                    }
                    catch { }
                    Processes = Process.GetProcessesByName(strMainProcessName);
                }
            }
            catch { }
        }

        private void StartMainFile()
        {
            lblNowProcess.Text = string.Format("正在启动【{0}】…", strMainProcessName);
            Application.DoEvents();
            try
            {
                if (File.Exists(strMainFile))
                {
                    System.Diagnostics.Process.Start(strMainFile);
                }
            }
            catch
            {
                //MessageBox.Show(this, "启动应用程序失败！请尝试手动启动！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void lblNowProcess_Click(object sender, EventArgs e)
        {
        }
    }
}
