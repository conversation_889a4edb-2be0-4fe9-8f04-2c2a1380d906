using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolPolygon : ToolObject
    {
        private const int minDistance = 5;
        private int lastX;

        private int lastY;

        private DrawPolygon newPolygon;

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            if (drawArea.Cursor != CursorEx.Cross)
            {
                if (e.Button == MouseButtons.Left)
                {
                    StaticValue.current_ToolType = drawArea.ActiveTool;
                    drawArea.ActiveTool = DrawToolType.Pointer;
                    drawArea.tools[3].OnMouseDown(drawArea, e);
                }
            }
            else
            {
                newPolygon = new DrawPolygon(e.X, e.Y, e.X + 1, e.Y + 1);
                AddNewObject(drawArea, newPolygon);
                lastX = e.X;
                lastY = e.Y;
            }
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            OnCursor(drawArea, e);
            if (e.But<PERSON> == MouseButtons.Left)
            {
                if (newPolygon == null)
                {
                    drawArea.ActiveTool = DrawToolType.Text;
                    return;
                }

                newPolygon.IsSelected = true;
                var drawPolygon = newPolygon;
                using (new AutomaticCanvasRefresher(drawArea, drawPolygon.GetBoundingBox))
                {
                    var point = new Point(e.X, e.Y);
                    var num = (e.X - lastX) * (e.X - lastX) + (e.Y - lastY) * (e.Y - lastY);
                    if (num < 5)
                    {
                        newPolygon.MoveHandleTo(point, newPolygon.HandleCount);
                    }
                    else
                    {
                        newPolygon.AddPoint(point);
                        lastX = e.X;
                        lastY = e.Y;
                    }
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (newPolygon != null)
            {
                var array = newPolygon.pointArray.ToArray();
                if (array.Length <= 2)
                {
                    drawArea.GraphicsList.RemoveAt(0);
                }
                else
                {
                    newPolygon.Normalize();
                    drawArea.AddCommandToHistory(new CommandAdd(newPolygon));
                }

                drawArea.Refresh();
            }
        }
    }
}