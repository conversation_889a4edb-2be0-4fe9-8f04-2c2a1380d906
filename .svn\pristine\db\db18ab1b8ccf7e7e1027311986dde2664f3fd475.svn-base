﻿using System.Drawing;

namespace OCRTools.NewForms
{
    partial class FormAreaCapture
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.chkPoint = new System.Windows.Forms.CheckBox();
            this.nLeft = new System.Windows.Forms.NumericUpDown();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancle = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.numericUpDown2 = new System.Windows.Forms.NumericUpDown();
            this.nTop = new System.Windows.Forms.NumericUpDown();
            this.chkSize = new System.Windows.Forms.CheckBox();
            this.nWidth = new System.Windows.Forms.NumericUpDown();
            this.nHeight = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.chkSleep = new System.Windows.Forms.CheckBox();
            this.nSleep = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.nLoopTimes = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.pictureBox3 = new System.Windows.Forms.PictureBox();
            this.tipMsg = new System.Windows.Forms.ToolTip(this.components);
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            this.pictureBox2 = new System.Windows.Forms.PictureBox();
            this.pictureBox4 = new System.Windows.Forms.PictureBox();
            this.nInterval = new System.Windows.Forms.NumericUpDown();
            this.label6 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.chkShowInMainWindow = new System.Windows.Forms.CheckBox();
            ((System.ComponentModel.ISupportInitialize)(this.nLeft)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDown2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nTop)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nWidth)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nHeight)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nSleep)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nLoopTimes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nInterval)).BeginInit();
            this.SuspendLayout();
            // 
            // chkPoint
            // 
            this.chkPoint.AutoSize = true;
            this.chkPoint.Location = new System.Drawing.Point(46, 74);
            this.chkPoint.Name = "chkPoint";
            this.chkPoint.Size = new System.Drawing.Size(72, 16);
            this.chkPoint.TabIndex = 0;
            this.chkPoint.TabStop = false;
            this.chkPoint.Text = "左上角：";
            this.chkPoint.UseVisualStyleBackColor = true;
            // 
            // nLeft
            // 
            this.nLeft.Location = new System.Drawing.Point(125, 72);
            this.nLeft.Name = "nLeft";
            this.nLeft.Size = new System.Drawing.Size(74, 21);
            this.nLeft.TabIndex = 2;
            this.nLeft.TabStop = false;
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(89, 249);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(73, 30);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancle
            // 
            this.btnCancle.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancle.Location = new System.Drawing.Point(171, 249);
            this.btnCancle.Name = "btnCancle";
            this.btnCancle.Size = new System.Drawing.Size(73, 30);
            this.btnCancle.TabIndex = 3;
            this.btnCancle.Text = "取消";
            this.btnCancle.UseVisualStyleBackColor = true;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("宋体", 15F, GraphicsUnit.Pixel);
            this.label2.Location = new System.Drawing.Point(204, 72);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(15, 15);
            this.label2.TabIndex = 1;
            this.label2.Text = "x";
            // 
            // numericUpDown2
            // 
            this.numericUpDown2.Location = new System.Drawing.Point(-46, -90);
            this.numericUpDown2.Name = "numericUpDown2";
            this.numericUpDown2.Size = new System.Drawing.Size(58, 21);
            this.numericUpDown2.TabIndex = 2;
            // 
            // nTop
            // 
            this.nTop.Location = new System.Drawing.Point(222, 72);
            this.nTop.Name = "nTop";
            this.nTop.Size = new System.Drawing.Size(74, 21);
            this.nTop.TabIndex = 2;
            this.nTop.TabStop = false;
            // 
            // chkSize
            // 
            this.chkSize.AutoSize = true;
            this.chkSize.Location = new System.Drawing.Point(46, 101);
            this.chkSize.Name = "chkSize";
            this.chkSize.Size = new System.Drawing.Size(72, 16);
            this.chkSize.TabIndex = 0;
            this.chkSize.TabStop = false;
            this.chkSize.Text = "大  小：";
            this.chkSize.UseVisualStyleBackColor = true;
            // 
            // nWidth
            // 
            this.nWidth.Location = new System.Drawing.Point(125, 99);
            this.nWidth.Name = "nWidth";
            this.nWidth.Size = new System.Drawing.Size(74, 21);
            this.nWidth.TabIndex = 2;
            this.nWidth.TabStop = false;
            // 
            // nHeight
            // 
            this.nHeight.Location = new System.Drawing.Point(222, 99);
            this.nHeight.Name = "nHeight";
            this.nHeight.Size = new System.Drawing.Size(74, 21);
            this.nHeight.TabIndex = 2;
            this.nHeight.TabStop = false;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("宋体", 15F, GraphicsUnit.Pixel);
            this.label3.Location = new System.Drawing.Point(204, 99);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(15, 15);
            this.label3.TabIndex = 1;
            this.label3.Text = "x";
            // 
            // chkSleep
            // 
            this.chkSleep.AutoSize = true;
            this.chkSleep.Location = new System.Drawing.Point(46, 128);
            this.chkSleep.Name = "chkSleep";
            this.chkSleep.Size = new System.Drawing.Size(72, 16);
            this.chkSleep.TabIndex = 0;
            this.chkSleep.TabStop = false;
            this.chkSleep.Text = "延  迟：";
            this.chkSleep.UseVisualStyleBackColor = true;
            // 
            // nSleep
            // 
            this.nSleep.DecimalPlaces = 1;
            this.nSleep.Location = new System.Drawing.Point(125, 126);
            this.nSleep.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.nSleep.Name = "nSleep";
            this.nSleep.Size = new System.Drawing.Size(74, 21);
            this.nSleep.TabIndex = 2;
            this.nSleep.TabStop = false;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label4.Location = new System.Drawing.Point(205, 127);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(54, 17);
            this.label4.TabIndex = 1;
            this.label4.Text = "秒(0-60)";
            // 
            // nLoopTimes
            // 
            this.nLoopTimes.Location = new System.Drawing.Point(125, 153);
            this.nLoopTimes.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.nLoopTimes.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.nLoopTimes.Name = "nLoopTimes";
            this.nLoopTimes.Size = new System.Drawing.Size(74, 21);
            this.nLoopTimes.TabIndex = 2;
            this.nLoopTimes.TabStop = false;
            this.nLoopTimes.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label1.Location = new System.Drawing.Point(205, 155);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(68, 17);
            this.label1.TabIndex = 1;
            this.label1.Text = "次(1-1000)";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(53, 156);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(65, 12);
            this.label5.TabIndex = 1;
            this.label5.Text = "循环次数：";
            // 
            // pictureBox3
            // 
            this.pictureBox3.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox3.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox3.Location = new System.Drawing.Point(257, 123);
            this.pictureBox3.Name = "pictureBox3";
            this.pictureBox3.Size = new System.Drawing.Size(28, 24);
            this.pictureBox3.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox3.TabIndex = 44;
            this.pictureBox3.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox3, "功能：截图前先等待几秒，然后再触发截图操作！");
            // 
            // pictureBox1
            // 
            this.pictureBox1.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox1.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox1.Location = new System.Drawing.Point(272, 152);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new System.Drawing.Size(28, 24);
            this.pictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox1.TabIndex = 44;
            this.pictureBox1.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox1, "功能：设置循环截图次数，配合循环间隔使用\r\n说明：循环次数结束后，截图窗口自动关闭");
            // 
            // pictureBox2
            // 
            this.pictureBox2.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox2.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox2.Location = new System.Drawing.Point(264, 180);
            this.pictureBox2.Name = "pictureBox2";
            this.pictureBox2.Size = new System.Drawing.Size(28, 24);
            this.pictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox2.TabIndex = 44;
            this.pictureBox2.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox2, "功能：设置每次循环中间的间隔时间，配合循环次数使用");
            // 
            // pictureBox4
            // 
            this.pictureBox4.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox4.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox4.Location = new System.Drawing.Point(190, 206);
            this.pictureBox4.Name = "pictureBox4";
            this.pictureBox4.Size = new System.Drawing.Size(28, 24);
            this.pictureBox4.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox4.TabIndex = 44;
            this.pictureBox4.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox4, "功能：处理识别结果，在哪里展示。\r\n勾选：在主窗体中展示\r\n不勾选：在截图上展示识别结果");
            // 
            // nInterval
            // 
            this.nInterval.DecimalPlaces = 1;
            this.nInterval.Location = new System.Drawing.Point(124, 180);
            this.nInterval.Maximum = new decimal(new int[] {
            300,
            0,
            0,
            0});
            this.nInterval.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.nInterval.Name = "nInterval";
            this.nInterval.Size = new System.Drawing.Size(74, 21);
            this.nInterval.TabIndex = 2;
            this.nInterval.TabStop = false;
            this.nInterval.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label6.Location = new System.Drawing.Point(204, 182);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(61, 17);
            this.label6.TabIndex = 1;
            this.label6.Text = "秒(1-300)";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(53, 183);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(65, 12);
            this.label7.TabIndex = 1;
            this.label7.Text = "循环间隔：";
            // 
            // chkShowInMainWindow
            // 
            this.chkShowInMainWindow.AutoSize = true;
            this.chkShowInMainWindow.Location = new System.Drawing.Point(46, 210);
            this.chkShowInMainWindow.Name = "chkShowInMainWindow";
            this.chkShowInMainWindow.Size = new System.Drawing.Size(144, 16);
            this.chkShowInMainWindow.TabIndex = 0;
            this.chkShowInMainWindow.TabStop = false;
            this.chkShowInMainWindow.Text = "在主界面展示识别结果";
            this.chkShowInMainWindow.UseVisualStyleBackColor = true;
            // 
            // FormAreaCapture
            // 
            this.AcceptButton = this.btnOK;
            //this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            //this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.CancelButton = this.btnCancle;
            this.ClientSize = new System.Drawing.Size(379, 302);
            this.Controls.Add(this.pictureBox4);
            this.Controls.Add(this.pictureBox2);
            this.Controls.Add(this.pictureBox1);
            this.Controls.Add(this.pictureBox3);
            this.Controls.Add(this.btnCancle);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.numericUpDown2);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.nHeight);
            this.Controls.Add(this.nSleep);
            this.Controls.Add(this.nWidth);
            this.Controls.Add(this.nTop);
            this.Controls.Add(this.chkShowInMainWindow);
            this.Controls.Add(this.chkSleep);
            this.Controls.Add(this.nLeft);
            this.Controls.Add(this.chkSize);
            this.Controls.Add(this.chkPoint);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.nInterval);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.nLoopTimes);
            this.MaximizeBox = false;
            this.Name = "FormAreaCapture";
            this.Resizable = false;
            this.Text = "固定区域截图";
            this.Load += new System.EventHandler(this.FormAreaCapture_Load);
            ((System.ComponentModel.ISupportInitialize)(this.nLeft)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDown2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nTop)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nWidth)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nHeight)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nSleep)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nLoopTimes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nInterval)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        private System.Windows.Forms.CheckBox chkPoint;
        private System.Windows.Forms.NumericUpDown nLeft;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancle;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown numericUpDown2;
        private System.Windows.Forms.NumericUpDown nTop;
        private System.Windows.Forms.CheckBox chkSize;
        private System.Windows.Forms.NumericUpDown nWidth;
        private System.Windows.Forms.NumericUpDown nHeight;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.CheckBox chkSleep;
        private System.Windows.Forms.NumericUpDown nSleep;
        private System.Windows.Forms.Label label4;
        #endregion
        private System.Windows.Forms.NumericUpDown nLoopTimes;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.PictureBox pictureBox3;
        private System.Windows.Forms.ToolTip tipMsg;
        private System.Windows.Forms.NumericUpDown nInterval;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.PictureBox pictureBox1;
        private System.Windows.Forms.PictureBox pictureBox2;
        private System.Windows.Forms.CheckBox chkShowInMainWindow;
        private System.Windows.Forms.PictureBox pictureBox4;
    }
}