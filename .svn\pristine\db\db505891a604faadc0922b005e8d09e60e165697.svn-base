﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;

namespace OCRTools
{
    public static class IniHelper
    {
        private static Dictionary<string, IniFileProvider> DicFileProvider = new Dictionary<string, IniFileProvider>();

        private static IniFileProvider GetFileProvider(string iniFileName)
        {
            if (string.IsNullOrEmpty(iniFileName))
            {
                iniFileName = CommonString.IniFileName;
            }
            if (!DicFileProvider.TryGetValue(iniFileName, out IniFileProvider result))
            {
                result = new IniFileProvider(iniFileName, Encoding.UTF8);
                DicFileProvider[iniFileName] = result;
            }
            return result;
        }

        public static string GetValue(string sectionName, string key, string iniFileName = null)
        {
            string result = null;
            try
            {
                if (!string.IsNullOrEmpty(sectionName) && !string.IsNullOrEmpty(key))
                {
                    var file = GetFileProvider(iniFileName);
                    result = file.GetValue(sectionName, key);
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("IniHelper.GetValue", oe);
            }

            return result;
        }

        public static bool SetValue(string sectionName, string key, string value, string iniFileName = null)
        {
            var result = true;
            if (string.IsNullOrEmpty(sectionName) || string.IsNullOrEmpty(key))
            {
                result = false;
            }
            else
            {
                try
                {
                    lock (CommonString.StrRetry)
                    {
                        var file = GetFileProvider(iniFileName);
                        if (file.SetValue(sectionName, key, value))
                            file.Save();
                    }
                }
                catch (Exception oe)
                {
                    Log.WriteError("IniHelper.SetValue", oe);
                }

            }
            return result;
        }

        public static void ClearSection(string sectionName, List<string> lstKey, string iniFileName = null)
        {
            if (string.IsNullOrEmpty(sectionName))
            {
                return;
            }
            try
            {
                lock (CommonString.StrRetry)
                {
                    var file = GetFileProvider(iniFileName);
                    if (file.ClearSection(sectionName, lstKey))
                        file.Save();
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("IniHelper.SetValue", oe);
            }
        }
    }
    public class IniFileProvider
    {
        private string _filename;
        private readonly Encoding _encoding;

        private readonly Regex _section_rex = new Regex(@"(?<=\[)(?<SectionName>[^\]]+)(?=\])");
        private readonly Regex _kv_rex = new Regex(@"(?<Key>[^=]+)=(?<Value>.+)");

        private Dictionary<string, Dictionary<string, string>> _sections = new Dictionary<string, Dictionary<string, string>>();

        public IniFileProvider(string filename, Encoding encoding)
        {
            _encoding = encoding;
            _filename = filename;
            Load(_filename);
        }

        public string GetValue(string sectionName, string key)
        {
            _sections.TryGetValue(sectionName, out Dictionary<string, string> se);
            if (se != null && se.ContainsKey(key))
                return se[key];
            return null;
        }

        public bool SetValue(string sectionName, string key, string value)
        {
            var result = false;
            if (!_sections.TryGetValue(sectionName, out Dictionary<string, string> se))
            {
                se = new Dictionary<string, string>();
                _sections[sectionName] = se;
            }
            if (!se.ContainsKey(key) || !Equals(se[key], value))
            {
                se[key] = value;
                result = true;
            }
            return result;
        }

        public bool ClearSection(string sectionName, List<string> lstKeys)
        {
            var result = false;
            if (_sections.TryGetValue(sectionName, out Dictionary<string, string> se))
            {
                var lstRemove = se.Keys.Where(p => !lstKeys.Contains(p)).ToList();
                if (lstRemove?.Count > 0)
                {
                    foreach (var kvp in lstRemove)
                    {
                        se.Remove(kvp);
                    }
                    _sections[sectionName] = se;
                    result = true;
                }
            }
            return result;
        }

        public bool Save()
        {
            if (!string.IsNullOrEmpty(_filename))
            {
                try
                {
                    var sb = new StringBuilder();
                    foreach (var section in _sections.Keys)
                    {
                        if (_sections[section].Count > 0)
                        {
                            sb.AppendLine("[" + section + "]");
                            foreach (var key in _sections[section])
                            {
                                sb.AppendLine(key.Key + "=" + key.Value);
                            }
                        }
                    }
                    File.WriteAllText(_filename, sb.ToString(), _encoding);
                    return true;
                }
                catch { }
            }
            return false;
        }

        public bool Load(string filename)
        {
            if (string.IsNullOrEmpty(filename) || !File.Exists(filename))
                return false;
            try
            {
                string[] content = File.ReadAllLines(filename, _encoding);
                string strSection = null;
                foreach (var line in content)
                {
                    //match section
                    Match m = _section_rex.Match(line);
                    if (m.Success)
                    {
                        strSection = m.Groups[1].Value.Trim();
                        if (!_sections.ContainsKey(strSection))
                        {
                            _sections.Add(strSection, new Dictionary<string, string>());
                        }
                        continue;
                    }

                    //must has a section
                    if (strSection != null)
                    {
                        m = _kv_rex.Match(line);
                        if (m.Success)
                        {
                            string key = m.Groups[1].Value.Trim();
                            string value = m.Groups[2].Value.Trim();
                            //如果字符串值带有前后引号，则删除前后引号
                            if (value.StartsWith("\"") && value.EndsWith("\""))
                                value = value.Substring(1, value.Length - 2);
                            _sections[strSection][key] = value;
                        }
                    }
                }
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}