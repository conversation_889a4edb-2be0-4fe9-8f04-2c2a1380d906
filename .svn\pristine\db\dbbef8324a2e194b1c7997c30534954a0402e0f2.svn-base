﻿using System.Windows.Forms;

namespace OCRTools
{
    partial class FormSetting
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormSetting));
            this.tbConfig = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.groupBox15 = new System.Windows.Forms.GroupBox();
            this.chkAutoStart = new System.Windows.Forms.CheckBox();
            this.chkStartMainWindow = new System.Windows.Forms.CheckBox();
            this.chkFormTopMost = new System.Windows.Forms.CheckBox();
            this.grpJieMianFont = new System.Windows.Forms.GroupBox();
            this.btnContentDefault = new System.Windows.Forms.Button();
            this.btnContentFont = new System.Windows.Forms.Button();
            this.lblContentLable = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.btnOpenConfigFile = new System.Windows.Forms.Button();
            this.btnOpenConfigLocation = new System.Windows.Forms.Button();
            this.chkAutoBackConfig = new System.Windows.Forms.CheckBox();
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.groupBox11 = new System.Windows.Forms.GroupBox();
            this.label3 = new System.Windows.Forms.Label();
            this.chkDarkModel = new System.Windows.Forms.CheckBox();
            this.cmbStyles = new System.Windows.Forms.ComboBox();
            this.label4 = new System.Windows.Forms.Label();
            this.cmbLoadingType = new System.Windows.Forms.ComboBox();
            this.cmbSearch = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.grpToolSet = new System.Windows.Forms.GroupBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.cmbToolBarSize = new System.Windows.Forms.ComboBox();
            this.numToolShadowWidth = new System.Windows.Forms.NumericUpDown();
            this.btnToolBarPicture = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.chkWeatherImage = new System.Windows.Forms.CheckBox();
            this.label31 = new System.Windows.Forms.Label();
            this.chkToolShadow = new System.Windows.Forms.CheckBox();
            this.label35 = new System.Windows.Forms.Label();
            this.label32 = new System.Windows.Forms.Label();
            this.cmbWeatherIcon = new System.Windows.Forms.ComboBox();
            this.btnDefaultToolBarPicture = new System.Windows.Forms.Button();
            this.btnQQ = new System.Windows.Forms.Button();
            this.chkToolBarCircle = new System.Windows.Forms.CheckBox();
            this.cmbDoubleClick = new System.Windows.Forms.ComboBox();
            this.chkShowTool = new System.Windows.Forms.CheckBox();
            this.txtToolBarPicLocation = new System.Windows.Forms.TextBox();
            this.tabPage7 = new System.Windows.Forms.TabPage();
            this.groupBox16 = new System.Windows.Forms.GroupBox();
            this.groupBox17 = new System.Windows.Forms.GroupBox();
            this.cmbCopyMode = new System.Windows.Forms.ComboBox();
            this.checkBox8 = new System.Windows.Forms.CheckBox();
            this.label39 = new System.Windows.Forms.Label();
            this.checkBox3 = new System.Windows.Forms.CheckBox();
            this.checkBox10 = new System.Windows.Forms.CheckBox();
            this.checkBox11 = new System.Windows.Forms.CheckBox();
            this.checkBox12 = new System.Windows.Forms.CheckBox();
            this.groupBox10 = new System.Windows.Forms.GroupBox();
            this.cmbOcrGroup = new System.Windows.Forms.ComboBox();
            this.cmbFenDuan = new System.Windows.Forms.ComboBox();
            this.checkBox4 = new System.Windows.Forms.CheckBox();
            this.cmbBiaoDian = new System.Windows.Forms.ComboBox();
            this.cmbVoice = new System.Windows.Forms.ComboBox();
            this.label19 = new System.Windows.Forms.Label();
            this.label34 = new System.Windows.Forms.Label();
            this.label26 = new System.Windows.Forms.Label();
            this.label25 = new System.Windows.Forms.Label();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.groupBox12 = new System.Windows.Forms.GroupBox();
            this.chkZoom = new System.Windows.Forms.CheckBox();
            this.label37 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.chkCaptureAnimal = new System.Windows.Forms.CheckBox();
            this.chkCircleFangDa = new System.Windows.Forms.CheckBox();
            this.chkCrocessLine = new System.Windows.Forms.CheckBox();
            this.numericUpDown1 = new System.Windows.Forms.NumericUpDown();
            this.numCaptureBorderWidth = new System.Windows.Forms.NumericUpDown();
            this.chkAutoWindow = new System.Windows.Forms.CheckBox();
            this.label36 = new System.Windows.Forms.Label();
            this.chkAutoControl = new System.Windows.Forms.CheckBox();
            this.label6 = new System.Windows.Forms.Label();
            this.numMaxHistoryCount = new System.Windows.Forms.NumericUpDown();
            this.label8 = new System.Windows.Forms.Label();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.btnChangeCaptureLocation = new System.Windows.Forms.Button();
            this.btnOpenCaptureLocation = new System.Windows.Forms.Button();
            this.txtCaptureFileName = new System.Windows.Forms.TextBox();
            this.lblCaptureFileName = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.txtCaptureLocation = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.chkCaptureTip = new System.Windows.Forms.CheckBox();
            this.chkAutoSaveCapture = new System.Windows.Forms.CheckBox();
            this.chkSaveToClipborad = new System.Windows.Forms.CheckBox();
            this.tabPage5 = new System.Windows.Forms.TabPage();
            this.groupBox14 = new System.Windows.Forms.GroupBox();
            this.label14 = new System.Windows.Forms.Label();
            this.label18 = new System.Windows.Forms.Label();
            this.chkTieTuFocus = new System.Windows.Forms.CheckBox();
            this.label22 = new System.Windows.Forms.Label();
            this.chkTieTuShark = new System.Windows.Forms.CheckBox();
            this.label15 = new System.Windows.Forms.Label();
            this.numShadowWidth = new System.Windows.Forms.NumericUpDown();
            this.chkTieTuUseCaptureLocation = new System.Windows.Forms.CheckBox();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.btnTieTuDefaultFont = new System.Windows.Forms.Button();
            this.btnTieTuFont = new System.Windows.Forms.Button();
            this.numTieTuMaxWidth = new System.Windows.Forms.NumericUpDown();
            this.numTieTuBorderWidth = new System.Windows.Forms.NumericUpDown();
            this.chkUseContentColor = new System.Windows.Forms.CheckBox();
            this.chkIngoreHtml = new System.Windows.Forms.CheckBox();
            this.label24 = new System.Windows.Forms.Label();
            this.label23 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.label21 = new System.Windows.Forms.Label();
            this.lblTieTuLabel = new System.Windows.Forms.Label();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.groupBox13 = new System.Windows.Forms.GroupBox();
            this.cmbHex = new System.Windows.Forms.ComboBox();
            this.label5 = new System.Windows.Forms.Label();
            this.chkHexUpper = new System.Windows.Forms.CheckBox();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.checkBox2 = new System.Windows.Forms.CheckBox();
            this.cmbRulerOpacity = new System.Windows.Forms.ComboBox();
            this.cmbRulerTheme = new System.Windows.Forms.ComboBox();
            this.label17 = new System.Windows.Forms.Label();
            this.cmbRulerUnit = new System.Windows.Forms.ComboBox();
            this.label13 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.tbShortKey = new System.Windows.Forms.TabPage();
            this.tabHotKeys = new System.Windows.Forms.TabControl();
            this.tabPage6 = new System.Windows.Forms.TabPage();
            this.groupBox9 = new System.Windows.Forms.GroupBox();
            this.linkLabel2 = new System.Windows.Forms.LinkLabel();
            this.linkLabel4 = new System.Windows.Forms.LinkLabel();
            this.linkLabel3 = new System.Windows.Forms.LinkLabel();
            this.linkLabel1 = new System.Windows.Forms.LinkLabel();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.numUpdateHour = new System.Windows.Forms.NumericUpDown();
            this.label28 = new System.Windows.Forms.Label();
            this.label29 = new System.Windows.Forms.Label();
            this.chkAutoUpdate = new System.Windows.Forms.CheckBox();
            this.label38 = new System.Windows.Forms.Label();
            this.lblCopyright = new System.Windows.Forms.Label();
            this.btnUpgrade = new System.Windows.Forms.Button();
            this.lblVersion = new System.Windows.Forms.Label();
            this.lnkWebSite = new System.Windows.Forms.LinkLabel();
            this.lblName = new System.Windows.Forms.Label();
            this.tipMsg = new System.Windows.Forms.ToolTip(this.components);
            this.label33 = new System.Windows.Forms.Label();
            this.groupBox18 = new System.Windows.Forms.GroupBox();
            this.label40 = new System.Windows.Forms.Label();
            this.label41 = new System.Windows.Forms.Label();
            this.groupBox19 = new System.Windows.Forms.GroupBox();
            this.checkBox1 = new System.Windows.Forms.CheckBox();
            this.checkBox5 = new System.Windows.Forms.CheckBox();
            this.checkBox6 = new System.Windows.Forms.CheckBox();
            this.checkBox7 = new System.Windows.Forms.CheckBox();
            this.pictureBox9 = new System.Windows.Forms.PictureBox();
            this.pictureBox10 = new System.Windows.Forms.PictureBox();
            this.picLoadingImage = new System.Windows.Forms.PictureBox();
            this.pictureBox8 = new System.Windows.Forms.PictureBox();
            this.pictureBox17 = new System.Windows.Forms.PictureBox();
            this.picToolBar = new System.Windows.Forms.PictureBox();
            this.pictureBox13 = new System.Windows.Forms.PictureBox();
            this.pictureBox3 = new System.Windows.Forms.PictureBox();
            this.pictureBox15 = new System.Windows.Forms.PictureBox();
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            this.pictureBox20 = new System.Windows.Forms.PictureBox();
            this.pictureBox21 = new System.Windows.Forms.PictureBox();
            this.pictureBox19 = new System.Windows.Forms.PictureBox();
            this.pictureBox18 = new System.Windows.Forms.PictureBox();
            this.pictureBox12 = new System.Windows.Forms.PictureBox();
            this.pictureBox16 = new System.Windows.Forms.PictureBox();
            this.pictureBox14 = new System.Windows.Forms.PictureBox();
            this.pictureBox11 = new System.Windows.Forms.PictureBox();
            this.pictureBox2 = new System.Windows.Forms.PictureBox();
            this.pictureBox4 = new System.Windows.Forms.PictureBox();
            this.pictureBox7 = new System.Windows.Forms.PictureBox();
            this.pictureBox6 = new System.Windows.Forms.PictureBox();
            this.pictureBox5 = new System.Windows.Forms.PictureBox();
            this.btnCheckUpdate = new System.Windows.Forms.Button();
            this.picIcon = new System.Windows.Forms.PictureBox();
            this.btnContentBackColor = new OCRTools.ImageButton();
            this.btnContentFontColor = new OCRTools.ImageButton();
            this.imageButton1 = new OCRTools.ImageButton();
            this.btnTieTuShadowColor = new OCRTools.ImageButton();
            this.btnTieTuBackColor = new OCRTools.ImageButton();
            this.btnTieTuFontColor = new OCRTools.ImageButton();
            this.tbConfig.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.groupBox15.SuspendLayout();
            this.grpJieMianFont.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.tabPage4.SuspendLayout();
            this.groupBox11.SuspendLayout();
            this.grpToolSet.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numToolShadowWidth)).BeginInit();
            this.tabPage7.SuspendLayout();
            this.groupBox16.SuspendLayout();
            this.groupBox17.SuspendLayout();
            this.groupBox10.SuspendLayout();
            this.groupBox7.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.groupBox12.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDown1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCaptureBorderWidth)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxHistoryCount)).BeginInit();
            this.groupBox3.SuspendLayout();
            this.tabPage5.SuspendLayout();
            this.groupBox14.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numShadowWidth)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numTieTuMaxWidth)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTieTuBorderWidth)).BeginInit();
            this.tabPage3.SuspendLayout();
            this.groupBox13.SuspendLayout();
            this.groupBox6.SuspendLayout();
            this.tbShortKey.SuspendLayout();
            this.tabPage6.SuspendLayout();
            this.groupBox9.SuspendLayout();
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numUpdateHour)).BeginInit();
            this.groupBox18.SuspendLayout();
            this.groupBox19.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLoadingImage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox17)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.picToolBar)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox20)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox21)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox19)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox18)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox16)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox14)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.picIcon)).BeginInit();
            this.SuspendLayout();
            // 
            // tbConfig
            // 
            this.tbConfig.Controls.Add(this.tabPage1);
            this.tbConfig.Controls.Add(this.tabPage4);
            this.tbConfig.Controls.Add(this.tabPage7);
            this.tbConfig.Controls.Add(this.tabPage2);
            this.tbConfig.Controls.Add(this.tabPage5);
            this.tbConfig.Controls.Add(this.tabPage3);
            this.tbConfig.Controls.Add(this.tbShortKey);
            this.tbConfig.Controls.Add(this.tabPage6);
            this.tbConfig.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tbConfig.Location = new System.Drawing.Point(10, 60);
            this.tbConfig.Name = "tbConfig";
            this.tbConfig.SelectedIndex = 0;
            this.tbConfig.Size = new System.Drawing.Size(395, 423);
            this.tbConfig.TabIndex = 0;
            this.tbConfig.SelectedIndexChanged += new System.EventHandler(this.tbConfig_SelectedIndexChanged);
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.groupBox15);
            this.tabPage1.Controls.Add(this.grpJieMianFont);
            this.tabPage1.Controls.Add(this.groupBox1);
            this.tabPage1.Location = new System.Drawing.Point(4, 22);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(387, 397);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "常规";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // groupBox15
            // 
            this.groupBox15.Controls.Add(this.pictureBox9);
            this.groupBox15.Controls.Add(this.chkAutoStart);
            this.groupBox15.Controls.Add(this.pictureBox10);
            this.groupBox15.Controls.Add(this.chkStartMainWindow);
            this.groupBox15.Controls.Add(this.chkFormTopMost);
            this.groupBox15.Location = new System.Drawing.Point(8, 6);
            this.groupBox15.Name = "groupBox15";
            this.groupBox15.Size = new System.Drawing.Size(374, 100);
            this.groupBox15.TabIndex = 5;
            this.groupBox15.TabStop = false;
            this.groupBox15.Text = "通用";
            // 
            // chkAutoStart
            // 
            this.chkAutoStart.AutoSize = true;
            this.chkAutoStart.Location = new System.Drawing.Point(13, 20);
            this.chkAutoStart.Name = "chkAutoStart";
            this.chkAutoStart.Size = new System.Drawing.Size(72, 16);
            this.chkAutoStart.TabIndex = 0;
            this.chkAutoStart.Text = "开机启动";
            this.chkAutoStart.UseVisualStyleBackColor = true;
            this.chkAutoStart.CheckedChanged += new System.EventHandler(this.chkAutoStart_CheckedChanged);
            // 
            // chkStartMainWindow
            // 
            this.chkStartMainWindow.AutoSize = true;
            this.chkStartMainWindow.Checked = true;
            this.chkStartMainWindow.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkStartMainWindow.Location = new System.Drawing.Point(13, 46);
            this.chkStartMainWindow.Name = "chkStartMainWindow";
            this.chkStartMainWindow.Size = new System.Drawing.Size(120, 16);
            this.chkStartMainWindow.TabIndex = 0;
            this.chkStartMainWindow.Text = "启动后打开主窗体";
            this.chkStartMainWindow.UseVisualStyleBackColor = true;
            // 
            // chkFormTopMost
            // 
            this.chkFormTopMost.AutoSize = true;
            this.chkFormTopMost.Location = new System.Drawing.Point(13, 72);
            this.chkFormTopMost.Name = "chkFormTopMost";
            this.chkFormTopMost.Size = new System.Drawing.Size(72, 16);
            this.chkFormTopMost.TabIndex = 0;
            this.chkFormTopMost.Text = "窗体置顶";
            this.chkFormTopMost.UseVisualStyleBackColor = true;
            // 
            // grpJieMianFont
            // 
            this.grpJieMianFont.Controls.Add(this.btnContentDefault);
            this.grpJieMianFont.Controls.Add(this.btnContentBackColor);
            this.grpJieMianFont.Controls.Add(this.btnContentFont);
            this.grpJieMianFont.Controls.Add(this.btnContentFontColor);
            this.grpJieMianFont.Controls.Add(this.lblContentLable);
            this.grpJieMianFont.Location = new System.Drawing.Point(8, 171);
            this.grpJieMianFont.Name = "grpJieMianFont";
            this.grpJieMianFont.Size = new System.Drawing.Size(374, 136);
            this.grpJieMianFont.TabIndex = 26;
            this.grpJieMianFont.TabStop = false;
            this.grpJieMianFont.Text = "通用字体";
            // 
            // btnContentDefault
            // 
            this.btnContentDefault.Location = new System.Drawing.Point(293, 102);
            this.btnContentDefault.Name = "btnContentDefault";
            this.btnContentDefault.Size = new System.Drawing.Size(69, 26);
            this.btnContentDefault.TabIndex = 39;
            this.btnContentDefault.Tag = "";
            this.btnContentDefault.Text = "默认字体";
            this.btnContentDefault.UseVisualStyleBackColor = false;
            this.btnContentDefault.Click += new System.EventHandler(this.btnContentDefault_Click);
            // 
            // btnContentFont
            // 
            this.btnContentFont.Location = new System.Drawing.Point(293, 18);
            this.btnContentFont.Name = "btnContentFont";
            this.btnContentFont.Size = new System.Drawing.Size(71, 26);
            this.btnContentFont.TabIndex = 37;
            this.btnContentFont.TabStop = false;
            this.btnContentFont.Tag = "默认文字字体";
            this.btnContentFont.Text = "字体";
            this.btnContentFont.UseVisualStyleBackColor = false;
            this.btnContentFont.Click += new System.EventHandler(this.btnFont_Click);
            // 
            // lblContentLable
            // 
            this.lblContentLable.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.lblContentLable.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.lblContentLable.Location = new System.Drawing.Point(12, 17);
            this.lblContentLable.Name = "lblContentLable";
            this.lblContentLable.Size = new System.Drawing.Size(276, 111);
            this.lblContentLable.TabIndex = 36;
            this.lblContentLable.Text = "预览\r\nAaOo012345";
            this.lblContentLable.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.btnOpenConfigFile);
            this.groupBox1.Controls.Add(this.btnOpenConfigLocation);
            this.groupBox1.Controls.Add(this.chkAutoBackConfig);
            this.groupBox1.Location = new System.Drawing.Point(8, 112);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(374, 51);
            this.groupBox1.TabIndex = 3;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "配置文件";
            // 
            // btnOpenConfigFile
            // 
            this.btnOpenConfigFile.Location = new System.Drawing.Point(104, 18);
            this.btnOpenConfigFile.Name = "btnOpenConfigFile";
            this.btnOpenConfigFile.Size = new System.Drawing.Size(52, 23);
            this.btnOpenConfigFile.TabIndex = 4;
            this.btnOpenConfigFile.Text = "打开";
            this.btnOpenConfigFile.UseVisualStyleBackColor = true;
            this.btnOpenConfigFile.Click += new System.EventHandler(this.btnOpenConfigFile_Click);
            // 
            // btnOpenConfigLocation
            // 
            this.btnOpenConfigLocation.Location = new System.Drawing.Point(176, 18);
            this.btnOpenConfigLocation.Name = "btnOpenConfigLocation";
            this.btnOpenConfigLocation.Size = new System.Drawing.Size(80, 23);
            this.btnOpenConfigLocation.TabIndex = 4;
            this.btnOpenConfigLocation.Text = "打开文件夹";
            this.btnOpenConfigLocation.UseVisualStyleBackColor = true;
            this.btnOpenConfigLocation.Click += new System.EventHandler(this.btnOpenConfigLocation_Click);
            // 
            // chkAutoBackConfig
            // 
            this.chkAutoBackConfig.AutoSize = true;
            this.chkAutoBackConfig.Checked = true;
            this.chkAutoBackConfig.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoBackConfig.Location = new System.Drawing.Point(13, 22);
            this.chkAutoBackConfig.Name = "chkAutoBackConfig";
            this.chkAutoBackConfig.Size = new System.Drawing.Size(72, 16);
            this.chkAutoBackConfig.TabIndex = 0;
            this.chkAutoBackConfig.Text = "自动备份";
            this.chkAutoBackConfig.UseVisualStyleBackColor = true;
            // 
            // tabPage4
            // 
            this.tabPage4.Controls.Add(this.groupBox11);
            this.tabPage4.Controls.Add(this.grpToolSet);
            this.tabPage4.Location = new System.Drawing.Point(4, 22);
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage4.Size = new System.Drawing.Size(387, 397);
            this.tabPage4.TabIndex = 3;
            this.tabPage4.Text = "界面";
            this.tabPage4.UseVisualStyleBackColor = true;
            // 
            // groupBox11
            // 
            this.groupBox11.Controls.Add(this.label3);
            this.groupBox11.Controls.Add(this.chkDarkModel);
            this.groupBox11.Controls.Add(this.cmbStyles);
            this.groupBox11.Controls.Add(this.label4);
            this.groupBox11.Controls.Add(this.picLoadingImage);
            this.groupBox11.Controls.Add(this.cmbLoadingType);
            this.groupBox11.Controls.Add(this.cmbSearch);
            this.groupBox11.Controls.Add(this.label1);
            this.groupBox11.Location = new System.Drawing.Point(8, 6);
            this.groupBox11.Name = "groupBox11";
            this.groupBox11.Size = new System.Drawing.Size(374, 101);
            this.groupBox11.TabIndex = 45;
            this.groupBox11.TabStop = false;
            this.groupBox11.Text = "通用";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(13, 20);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(59, 12);
            this.label3.TabIndex = 2;
            this.label3.Text = "主题样式:";
            // 
            // chkDarkModel
            // 
            this.chkDarkModel.AutoSize = true;
            this.chkDarkModel.Checked = true;
            this.chkDarkModel.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkDarkModel.Location = new System.Drawing.Point(241, 19);
            this.chkDarkModel.Name = "chkDarkModel";
            this.chkDarkModel.Size = new System.Drawing.Size(72, 16);
            this.chkDarkModel.TabIndex = 40;
            this.chkDarkModel.Text = "深色模式";
            this.chkDarkModel.UseVisualStyleBackColor = true;
            this.chkDarkModel.CheckedChanged += new System.EventHandler(this.chkDarkModel_CheckedChanged);
            // 
            // cmbStyles
            // 
            this.cmbStyles.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbStyles.FormattingEnabled = true;
            this.cmbStyles.Location = new System.Drawing.Point(77, 17);
            this.cmbStyles.Name = "cmbStyles";
            this.cmbStyles.Size = new System.Drawing.Size(156, 20);
            this.cmbStyles.TabIndex = 3;
            this.cmbStyles.Tag = "主题样式";
            this.cmbStyles.SelectedIndexChanged += new System.EventHandler(this.cmbStyles_SelectedIndexChanged);
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(13, 46);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(59, 12);
            this.label4.TabIndex = 2;
            this.label4.Text = "加载动画:";
            // 
            // cmbLoadingType
            // 
            this.cmbLoadingType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbLoadingType.FormattingEnabled = true;
            this.cmbLoadingType.Location = new System.Drawing.Point(77, 43);
            this.cmbLoadingType.Name = "cmbLoadingType";
            this.cmbLoadingType.Size = new System.Drawing.Size(156, 20);
            this.cmbLoadingType.TabIndex = 3;
            this.cmbLoadingType.Tag = "加载动画";
            this.cmbLoadingType.SelectedIndexChanged += new System.EventHandler(this.cmbLoadingType_SelectedIndexChanged);
            // 
            // cmbSearch
            // 
            this.cmbSearch.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbSearch.FormattingEnabled = true;
            this.cmbSearch.Location = new System.Drawing.Point(77, 69);
            this.cmbSearch.Name = "cmbSearch";
            this.cmbSearch.Size = new System.Drawing.Size(156, 20);
            this.cmbSearch.TabIndex = 3;
            this.cmbSearch.Tag = "搜索引擎";
            this.cmbSearch.SelectedIndexChanged += new System.EventHandler(this.cmbLoadingType_SelectedIndexChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(13, 72);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(59, 12);
            this.label1.TabIndex = 2;
            this.label1.Text = "搜索引擎:";
            // 
            // grpToolSet
            // 
            this.grpToolSet.Controls.Add(this.groupBox18);
            this.grpToolSet.Controls.Add(this.groupBox2);
            this.grpToolSet.Controls.Add(this.txtToolBarPicLocation);
            this.grpToolSet.Controls.Add(this.chkShowTool);
            this.grpToolSet.Controls.Add(this.picToolBar);
            this.grpToolSet.Location = new System.Drawing.Point(8, 116);
            this.grpToolSet.Name = "grpToolSet";
            this.grpToolSet.Size = new System.Drawing.Size(374, 275);
            this.grpToolSet.TabIndex = 26;
            this.grpToolSet.TabStop = false;
            this.grpToolSet.Text = "工具栏";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.pictureBox17);
            this.groupBox2.Controls.Add(this.cmbWeatherIcon);
            this.groupBox2.Controls.Add(this.btnToolBarPicture);
            this.groupBox2.Controls.Add(this.chkWeatherImage);
            this.groupBox2.Controls.Add(this.label31);
            this.groupBox2.Controls.Add(this.label35);
            this.groupBox2.Controls.Add(this.btnDefaultToolBarPicture);
            this.groupBox2.Controls.Add(this.btnQQ);
            this.groupBox2.Location = new System.Drawing.Point(4, 45);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(239, 130);
            this.groupBox2.TabIndex = 45;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "图片设置";
            // 
            // cmbToolBarSize
            // 
            this.cmbToolBarSize.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbToolBarSize.FormattingEnabled = true;
            this.cmbToolBarSize.Items.AddRange(new object[] {
            "自动",
            "12*12",
            "24*24",
            "36*36",
            "48*48",
            "60*60",
            "72*72",
            "84*84",
            "96*96",
            "108*108",
            "120*120"});
            this.cmbToolBarSize.Location = new System.Drawing.Point(241, 15);
            this.cmbToolBarSize.Name = "cmbToolBarSize";
            this.cmbToolBarSize.Size = new System.Drawing.Size(72, 20);
            this.cmbToolBarSize.TabIndex = 28;
            this.cmbToolBarSize.Tag = "工具栏图标尺寸";
            // 
            // numToolShadowWidth
            // 
            this.numToolShadowWidth.Location = new System.Drawing.Point(241, 41);
            this.numToolShadowWidth.Maximum = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numToolShadowWidth.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numToolShadowWidth.Name = "numToolShadowWidth";
            this.numToolShadowWidth.Size = new System.Drawing.Size(37, 21);
            this.numToolShadowWidth.TabIndex = 42;
            this.numToolShadowWidth.TabStop = false;
            this.numToolShadowWidth.Tag = "工具栏阴影宽度";
            this.numToolShadowWidth.Value = new decimal(new int[] {
            8,
            0,
            0,
            0});
            // 
            // btnToolBarPicture
            // 
            this.btnToolBarPicture.Location = new System.Drawing.Point(11, 41);
            this.btnToolBarPicture.Name = "btnToolBarPicture";
            this.btnToolBarPicture.Size = new System.Drawing.Size(51, 26);
            this.btnToolBarPicture.TabIndex = 37;
            this.btnToolBarPicture.TabStop = false;
            this.btnToolBarPicture.Text = "自定义";
            this.btnToolBarPicture.UseVisualStyleBackColor = false;
            this.btnToolBarPicture.Click += new System.EventHandler(this.btnToolBarPicture_Click);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(179, 19);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(59, 12);
            this.label2.TabIndex = 27;
            this.label2.Text = "图标尺寸:";
            // 
            // chkWeatherImage
            // 
            this.chkWeatherImage.AutoSize = true;
            this.chkWeatherImage.Location = new System.Drawing.Point(9, 78);
            this.chkWeatherImage.Name = "chkWeatherImage";
            this.chkWeatherImage.Size = new System.Drawing.Size(96, 16);
            this.chkWeatherImage.TabIndex = 30;
            this.chkWeatherImage.TabStop = false;
            this.chkWeatherImage.Text = "启用天气图标";
            this.chkWeatherImage.UseVisualStyleBackColor = true;
            // 
            // label31
            // 
            this.label31.AutoSize = true;
            this.label31.Location = new System.Drawing.Point(9, 22);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(107, 12);
            this.label31.TabIndex = 27;
            this.label31.Text = "自定义工具栏图片:";
            // 
            // chkToolShadow
            // 
            this.chkToolShadow.AutoSize = true;
            this.chkToolShadow.Checked = true;
            this.chkToolShadow.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkToolShadow.Location = new System.Drawing.Point(11, 44);
            this.chkToolShadow.Name = "chkToolShadow";
            this.chkToolShadow.Size = new System.Drawing.Size(96, 16);
            this.chkToolShadow.TabIndex = 30;
            this.chkToolShadow.TabStop = false;
            this.chkToolShadow.Text = "图像阴影效果";
            this.chkToolShadow.UseVisualStyleBackColor = true;
            // 
            // label35
            // 
            this.label35.AutoSize = true;
            this.label35.Location = new System.Drawing.Point(8, 104);
            this.label35.Name = "label35";
            this.label35.Size = new System.Drawing.Size(59, 12);
            this.label35.TabIndex = 27;
            this.label35.Text = "天气样式:";
            // 
            // label32
            // 
            this.label32.AutoSize = true;
            this.label32.Location = new System.Drawing.Point(181, 45);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(59, 12);
            this.label32.TabIndex = 41;
            this.label32.Text = "阴影宽度:";
            // 
            // cmbWeatherIcon
            // 
            this.cmbWeatherIcon.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbWeatherIcon.FormattingEnabled = true;
            this.cmbWeatherIcon.Location = new System.Drawing.Point(64, 100);
            this.cmbWeatherIcon.Name = "cmbWeatherIcon";
            this.cmbWeatherIcon.Size = new System.Drawing.Size(110, 20);
            this.cmbWeatherIcon.TabIndex = 28;
            this.cmbWeatherIcon.TabStop = false;
            this.cmbWeatherIcon.Tag = "天气图标样式";
            // 
            // btnDefaultToolBarPicture
            // 
            this.btnDefaultToolBarPicture.Location = new System.Drawing.Point(121, 41);
            this.btnDefaultToolBarPicture.Name = "btnDefaultToolBarPicture";
            this.btnDefaultToolBarPicture.Size = new System.Drawing.Size(45, 26);
            this.btnDefaultToolBarPicture.TabIndex = 39;
            this.btnDefaultToolBarPicture.Tag = "";
            this.btnDefaultToolBarPicture.Text = "默认";
            this.btnDefaultToolBarPicture.UseVisualStyleBackColor = false;
            this.btnDefaultToolBarPicture.Click += new System.EventHandler(this.btnDefaultToolBarPicture_Click);
            // 
            // btnQQ
            // 
            this.btnQQ.Location = new System.Drawing.Point(66, 41);
            this.btnQQ.Name = "btnQQ";
            this.btnQQ.Size = new System.Drawing.Size(51, 26);
            this.btnQQ.TabIndex = 37;
            this.btnQQ.TabStop = false;
            this.btnQQ.Text = "QQ头像";
            this.btnQQ.UseVisualStyleBackColor = false;
            this.btnQQ.Click += new System.EventHandler(this.btnQQ_Click);
            // 
            // chkToolBarCircle
            // 
            this.chkToolBarCircle.AutoSize = true;
            this.chkToolBarCircle.Checked = true;
            this.chkToolBarCircle.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkToolBarCircle.Location = new System.Drawing.Point(11, 20);
            this.chkToolBarCircle.Name = "chkToolBarCircle";
            this.chkToolBarCircle.Size = new System.Drawing.Size(72, 16);
            this.chkToolBarCircle.TabIndex = 30;
            this.chkToolBarCircle.Text = "圆形图标";
            this.chkToolBarCircle.UseVisualStyleBackColor = true;
            // 
            // cmbDoubleClick
            // 
            this.cmbDoubleClick.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbDoubleClick.FormattingEnabled = true;
            this.cmbDoubleClick.Location = new System.Drawing.Point(86, 65);
            this.cmbDoubleClick.Name = "cmbDoubleClick";
            this.cmbDoubleClick.Size = new System.Drawing.Size(145, 20);
            this.cmbDoubleClick.TabIndex = 28;
            this.cmbDoubleClick.Tag = "双击工具栏操作";
            // 
            // chkShowTool
            // 
            this.chkShowTool.AutoSize = true;
            this.chkShowTool.Checked = true;
            this.chkShowTool.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkShowTool.Location = new System.Drawing.Point(13, 20);
            this.chkShowTool.Name = "chkShowTool";
            this.chkShowTool.Size = new System.Drawing.Size(84, 16);
            this.chkShowTool.TabIndex = 29;
            this.chkShowTool.Text = "显示工具栏";
            this.chkShowTool.UseVisualStyleBackColor = true;
            this.chkShowTool.CheckedChanged += new System.EventHandler(this.chkShowTool_CheckedChanged);
            // 
            // txtToolBarPicLocation
            // 
            this.txtToolBarPicLocation.Location = new System.Drawing.Point(273, 18);
            this.txtToolBarPicLocation.Name = "txtToolBarPicLocation";
            this.txtToolBarPicLocation.Size = new System.Drawing.Size(100, 21);
            this.txtToolBarPicLocation.TabIndex = 40;
            this.txtToolBarPicLocation.Tag = "工具栏图片";
            this.txtToolBarPicLocation.Visible = false;
            // 
            // tabPage7
            // 
            this.tabPage7.Controls.Add(this.groupBox16);
            this.tabPage7.Controls.Add(this.groupBox10);
            this.tabPage7.Location = new System.Drawing.Point(4, 22);
            this.tabPage7.Name = "tabPage7";
            this.tabPage7.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage7.Size = new System.Drawing.Size(387, 397);
            this.tabPage7.TabIndex = 8;
            this.tabPage7.Text = "识别";
            this.tabPage7.UseVisualStyleBackColor = true;
            // 
            // groupBox16
            // 
            this.groupBox16.Controls.Add(this.pictureBox13);
            this.groupBox16.Controls.Add(this.groupBox17);
            this.groupBox16.Controls.Add(this.groupBox7);
            this.groupBox16.Controls.Add(this.checkBox11);
            this.groupBox16.Controls.Add(this.checkBox12);
            this.groupBox16.Location = new System.Drawing.Point(8, 83);
            this.groupBox16.Name = "groupBox16";
            this.groupBox16.Size = new System.Drawing.Size(374, 229);
            this.groupBox16.TabIndex = 45;
            this.groupBox16.TabStop = false;
            this.groupBox16.Text = "识别结果";
            // 
            // groupBox17
            // 
            this.groupBox17.Controls.Add(this.pictureBox3);
            this.groupBox17.Controls.Add(this.pictureBox15);
            this.groupBox17.Controls.Add(this.pictureBox1);
            this.groupBox17.Controls.Add(this.cmbCopyMode);
            this.groupBox17.Controls.Add(this.checkBox8);
            this.groupBox17.Controls.Add(this.label39);
            this.groupBox17.Controls.Add(this.checkBox3);
            this.groupBox17.Location = new System.Drawing.Point(6, 148);
            this.groupBox17.Name = "groupBox17";
            this.groupBox17.Size = new System.Drawing.Size(362, 71);
            this.groupBox17.TabIndex = 45;
            this.groupBox17.TabStop = false;
            this.groupBox17.Text = "自动结果复制";
            // 
            // cmbCopyMode
            // 
            this.cmbCopyMode.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbCopyMode.FormattingEnabled = true;
            this.cmbCopyMode.Location = new System.Drawing.Point(65, 40);
            this.cmbCopyMode.Name = "cmbCopyMode";
            this.cmbCopyMode.Size = new System.Drawing.Size(93, 20);
            this.cmbCopyMode.TabIndex = 6;
            this.cmbCopyMode.Tag = "复制模式";
            // 
            // checkBox8
            // 
            this.checkBox8.AutoSize = true;
            this.checkBox8.Location = new System.Drawing.Point(5, 20);
            this.checkBox8.Name = "checkBox8";
            this.checkBox8.Size = new System.Drawing.Size(144, 16);
            this.checkBox8.TabIndex = 4;
            this.checkBox8.Text = "自动复制结果到粘贴板";
            this.checkBox8.UseVisualStyleBackColor = true;
            // 
            // label39
            // 
            this.label39.AutoSize = true;
            this.label39.Location = new System.Drawing.Point(5, 43);
            this.label39.Name = "label39";
            this.label39.Size = new System.Drawing.Size(59, 12);
            this.label39.TabIndex = 5;
            this.label39.Text = "复制模式:";
            // 
            // checkBox3
            // 
            this.checkBox3.AutoSize = true;
            this.checkBox3.Location = new System.Drawing.Point(192, 20);
            this.checkBox3.Name = "checkBox3";
            this.checkBox3.Size = new System.Drawing.Size(108, 16);
            this.checkBox3.TabIndex = 1;
            this.checkBox3.Text = "翻译时复制原文";
            this.checkBox3.UseVisualStyleBackColor = true;
            // 
            // checkBox10
            // 
            this.checkBox10.AutoSize = true;
            this.checkBox10.Location = new System.Drawing.Point(176, 20);
            this.checkBox10.Name = "checkBox10";
            this.checkBox10.Size = new System.Drawing.Size(72, 16);
            this.checkBox10.TabIndex = 4;
            this.checkBox10.Text = "首行缩进";
            this.checkBox10.UseVisualStyleBackColor = true;
            // 
            // checkBox11
            // 
            this.checkBox11.AutoSize = true;
            this.checkBox11.Checked = true;
            this.checkBox11.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox11.Location = new System.Drawing.Point(13, 20);
            this.checkBox11.Name = "checkBox11";
            this.checkBox11.Size = new System.Drawing.Size(72, 16);
            this.checkBox11.TabIndex = 4;
            this.checkBox11.Text = "图文模式";
            this.checkBox11.UseVisualStyleBackColor = true;
            // 
            // checkBox12
            // 
            this.checkBox12.AutoSize = true;
            this.checkBox12.Location = new System.Drawing.Point(176, 20);
            this.checkBox12.Name = "checkBox12";
            this.checkBox12.Size = new System.Drawing.Size(96, 16);
            this.checkBox12.TabIndex = 1;
            this.checkBox12.Text = "显示文字预览";
            this.checkBox12.UseVisualStyleBackColor = true;
            // 
            // groupBox10
            // 
            this.groupBox10.Controls.Add(this.pictureBox16);
            this.groupBox10.Controls.Add(this.pictureBox14);
            this.groupBox10.Controls.Add(this.cmbOcrGroup);
            this.groupBox10.Controls.Add(this.checkBox4);
            this.groupBox10.Controls.Add(this.cmbBiaoDian);
            this.groupBox10.Controls.Add(this.cmbVoice);
            this.groupBox10.Controls.Add(this.label34);
            this.groupBox10.Controls.Add(this.label26);
            this.groupBox10.Controls.Add(this.label25);
            this.groupBox10.Location = new System.Drawing.Point(8, 6);
            this.groupBox10.Name = "groupBox10";
            this.groupBox10.Size = new System.Drawing.Size(374, 72);
            this.groupBox10.TabIndex = 45;
            this.groupBox10.TabStop = false;
            this.groupBox10.Text = "通用";
            // 
            // cmbOcrGroup
            // 
            this.cmbOcrGroup.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbOcrGroup.FormattingEnabled = true;
            this.cmbOcrGroup.Location = new System.Drawing.Point(73, 43);
            this.cmbOcrGroup.Name = "cmbOcrGroup";
            this.cmbOcrGroup.Size = new System.Drawing.Size(65, 20);
            this.cmbOcrGroup.TabIndex = 6;
            this.cmbOcrGroup.Tag = "识别引擎";
            // 
            // cmbFenDuan
            // 
            this.cmbFenDuan.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbFenDuan.FormattingEnabled = true;
            this.cmbFenDuan.Location = new System.Drawing.Point(73, 16);
            this.cmbFenDuan.Name = "cmbFenDuan";
            this.cmbFenDuan.Size = new System.Drawing.Size(82, 20);
            this.cmbFenDuan.TabIndex = 6;
            this.cmbFenDuan.Tag = "分段模式";
            // 
            // checkBox4
            // 
            this.checkBox4.AutoSize = true;
            this.checkBox4.Checked = true;
            this.checkBox4.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.checkBox4.ForeColor = System.Drawing.Color.Blue;
            this.checkBox4.Location = new System.Drawing.Point(13, 20);
            this.checkBox4.Name = "checkBox4";
            this.checkBox4.Size = new System.Drawing.Size(76, 16);
            this.checkBox4.TabIndex = 4;
            this.checkBox4.Text = "快速识别";
            this.checkBox4.UseVisualStyleBackColor = true;
            // 
            // cmbBiaoDian
            // 
            this.cmbBiaoDian.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbBiaoDian.FormattingEnabled = true;
            this.cmbBiaoDian.Location = new System.Drawing.Point(236, 17);
            this.cmbBiaoDian.Name = "cmbBiaoDian";
            this.cmbBiaoDian.Size = new System.Drawing.Size(93, 20);
            this.cmbBiaoDian.TabIndex = 6;
            this.cmbBiaoDian.Tag = "标点模式";
            // 
            // cmbVoice
            // 
            this.cmbVoice.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbVoice.FormattingEnabled = true;
            this.cmbVoice.Location = new System.Drawing.Point(236, 43);
            this.cmbVoice.Name = "cmbVoice";
            this.cmbVoice.Size = new System.Drawing.Size(93, 20);
            this.cmbVoice.TabIndex = 6;
            this.cmbVoice.Tag = "朗读配置";
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(13, 20);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(59, 12);
            this.label19.TabIndex = 5;
            this.label19.Text = "分段模式:";
            // 
            // label34
            // 
            this.label34.AutoSize = true;
            this.label34.Location = new System.Drawing.Point(175, 20);
            this.label34.Name = "label34";
            this.label34.Size = new System.Drawing.Size(59, 12);
            this.label34.TabIndex = 5;
            this.label34.Text = "标点模式:";
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(175, 46);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(59, 12);
            this.label26.TabIndex = 5;
            this.label26.Text = "朗读配置:";
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(13, 46);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(59, 12);
            this.label25.TabIndex = 5;
            this.label25.Text = "识别引擎:";
            // 
            // groupBox7
            // 
            this.groupBox7.Controls.Add(this.pictureBox20);
            this.groupBox7.Controls.Add(this.pictureBox21);
            this.groupBox7.Controls.Add(this.pictureBox19);
            this.groupBox7.Controls.Add(this.pictureBox18);
            this.groupBox7.Controls.Add(this.pictureBox12);
            this.groupBox7.Controls.Add(this.checkBox6);
            this.groupBox7.Controls.Add(this.cmbFenDuan);
            this.groupBox7.Controls.Add(this.checkBox7);
            this.groupBox7.Controls.Add(this.checkBox10);
            this.groupBox7.Controls.Add(this.label19);
            this.groupBox7.Controls.Add(this.checkBox5);
            this.groupBox7.Controls.Add(this.checkBox1);
            this.groupBox7.Location = new System.Drawing.Point(6, 44);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Size = new System.Drawing.Size(374, 98);
            this.groupBox7.TabIndex = 44;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "排版";
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.groupBox19);
            this.tabPage2.Controls.Add(this.groupBox12);
            this.tabPage2.Controls.Add(this.groupBox3);
            this.tabPage2.Location = new System.Drawing.Point(4, 22);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(387, 397);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "截屏";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // groupBox12
            // 
            this.groupBox12.Controls.Add(this.label41);
            this.groupBox12.Controls.Add(this.imageButton1);
            this.groupBox12.Controls.Add(this.chkZoom);
            this.groupBox12.Controls.Add(this.pictureBox2);
            this.groupBox12.Controls.Add(this.label7);
            this.groupBox12.Controls.Add(this.chkCaptureAnimal);
            this.groupBox12.Controls.Add(this.chkCircleFangDa);
            this.groupBox12.Controls.Add(this.chkCrocessLine);
            this.groupBox12.Controls.Add(this.numCaptureBorderWidth);
            this.groupBox12.Controls.Add(this.chkAutoWindow);
            this.groupBox12.Controls.Add(this.chkAutoControl);
            this.groupBox12.Controls.Add(this.label6);
            this.groupBox12.Location = new System.Drawing.Point(8, 6);
            this.groupBox12.Name = "groupBox12";
            this.groupBox12.Size = new System.Drawing.Size(374, 126);
            this.groupBox12.TabIndex = 46;
            this.groupBox12.TabStop = false;
            this.groupBox12.Text = "通用";
            // 
            // chkZoom
            // 
            this.chkZoom.AutoSize = true;
            this.chkZoom.Checked = true;
            this.chkZoom.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkZoom.Location = new System.Drawing.Point(13, 20);
            this.chkZoom.Name = "chkZoom";
            this.chkZoom.Size = new System.Drawing.Size(84, 16);
            this.chkZoom.TabIndex = 26;
            this.chkZoom.Text = "显示放大镜";
            this.chkZoom.UseVisualStyleBackColor = true;
            // 
            // label37
            // 
            this.label37.AutoSize = true;
            this.label37.Location = new System.Drawing.Point(119, 22);
            this.label37.Name = "label37";
            this.label37.Size = new System.Drawing.Size(17, 12);
            this.label37.TabIndex = 2;
            this.label37.Text = "秒";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(245, 101);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(29, 12);
            this.label7.TabIndex = 2;
            this.label7.Text = "像素";
            // 
            // chkCaptureAnimal
            // 
            this.chkCaptureAnimal.AutoSize = true;
            this.chkCaptureAnimal.Checked = true;
            this.chkCaptureAnimal.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkCaptureAnimal.Location = new System.Drawing.Point(13, 46);
            this.chkCaptureAnimal.Name = "chkCaptureAnimal";
            this.chkCaptureAnimal.Size = new System.Drawing.Size(96, 16);
            this.chkCaptureAnimal.TabIndex = 0;
            this.chkCaptureAnimal.Text = "截图动画效果";
            this.chkCaptureAnimal.UseVisualStyleBackColor = true;
            // 
            // chkCircleFangDa
            // 
            this.chkCircleFangDa.AutoSize = true;
            this.chkCircleFangDa.Location = new System.Drawing.Point(116, 20);
            this.chkCircleFangDa.Name = "chkCircleFangDa";
            this.chkCircleFangDa.Size = new System.Drawing.Size(84, 16);
            this.chkCircleFangDa.TabIndex = 26;
            this.chkCircleFangDa.Text = "圆形放大镜";
            this.chkCircleFangDa.UseVisualStyleBackColor = true;
            // 
            // chkCrocessLine
            // 
            this.chkCrocessLine.AutoSize = true;
            this.chkCrocessLine.Location = new System.Drawing.Point(116, 46);
            this.chkCrocessLine.Name = "chkCrocessLine";
            this.chkCrocessLine.Size = new System.Drawing.Size(108, 16);
            this.chkCrocessLine.TabIndex = 26;
            this.chkCrocessLine.Text = "显示全屏十字线";
            this.chkCrocessLine.UseVisualStyleBackColor = true;
            // 
            // numericUpDown1
            // 
            this.numericUpDown1.Location = new System.Drawing.Point(67, 20);
            this.numericUpDown1.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numericUpDown1.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numericUpDown1.Name = "numericUpDown1";
            this.numericUpDown1.Size = new System.Drawing.Size(46, 21);
            this.numericUpDown1.TabIndex = 27;
            this.numericUpDown1.Tag = "截图延时";
            this.numericUpDown1.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // numCaptureBorderWidth
            // 
            this.numCaptureBorderWidth.Location = new System.Drawing.Point(197, 96);
            this.numCaptureBorderWidth.Name = "numCaptureBorderWidth";
            this.numCaptureBorderWidth.Size = new System.Drawing.Size(46, 21);
            this.numCaptureBorderWidth.TabIndex = 27;
            this.numCaptureBorderWidth.Tag = "截图边框宽度";
            this.numCaptureBorderWidth.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // chkAutoWindow
            // 
            this.chkAutoWindow.AutoSize = true;
            this.chkAutoWindow.Checked = true;
            this.chkAutoWindow.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoWindow.Location = new System.Drawing.Point(13, 74);
            this.chkAutoWindow.Name = "chkAutoWindow";
            this.chkAutoWindow.Size = new System.Drawing.Size(96, 16);
            this.chkAutoWindow.TabIndex = 26;
            this.chkAutoWindow.Text = "自动检测窗口";
            this.chkAutoWindow.UseVisualStyleBackColor = true;
            // 
            // label36
            // 
            this.label36.AutoSize = true;
            this.label36.Location = new System.Drawing.Point(7, 25);
            this.label36.Name = "label36";
            this.label36.Size = new System.Drawing.Size(59, 12);
            this.label36.TabIndex = 2;
            this.label36.Text = "截图延时:";
            // 
            // chkAutoControl
            // 
            this.chkAutoControl.AutoSize = true;
            this.chkAutoControl.Checked = true;
            this.chkAutoControl.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoControl.Location = new System.Drawing.Point(116, 74);
            this.chkAutoControl.Name = "chkAutoControl";
            this.chkAutoControl.Size = new System.Drawing.Size(120, 16);
            this.chkAutoControl.TabIndex = 26;
            this.chkAutoControl.Text = "自动检测窗口元素";
            this.chkAutoControl.UseVisualStyleBackColor = true;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(137, 101);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(59, 12);
            this.label6.TabIndex = 2;
            this.label6.Text = "边框宽度:";
            // 
            // numMaxHistoryCount
            // 
            this.numMaxHistoryCount.Location = new System.Drawing.Point(121, 143);
            this.numMaxHistoryCount.Name = "numMaxHistoryCount";
            this.numMaxHistoryCount.Size = new System.Drawing.Size(44, 21);
            this.numMaxHistoryCount.TabIndex = 27;
            this.numMaxHistoryCount.Tag = "最大历史记录数量";
            this.numMaxHistoryCount.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(11, 148);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(107, 12);
            this.label8.TabIndex = 2;
            this.label8.Text = "最大历史记录数量:";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.btnChangeCaptureLocation);
            this.groupBox3.Controls.Add(this.btnOpenCaptureLocation);
            this.groupBox3.Controls.Add(this.txtCaptureFileName);
            this.groupBox3.Controls.Add(this.lblCaptureFileName);
            this.groupBox3.Controls.Add(this.label8);
            this.groupBox3.Controls.Add(this.numMaxHistoryCount);
            this.groupBox3.Controls.Add(this.label11);
            this.groupBox3.Controls.Add(this.label9);
            this.groupBox3.Controls.Add(this.txtCaptureLocation);
            this.groupBox3.Controls.Add(this.label10);
            this.groupBox3.Controls.Add(this.chkCaptureTip);
            this.groupBox3.Controls.Add(this.chkAutoSaveCapture);
            this.groupBox3.Controls.Add(this.chkSaveToClipborad);
            this.groupBox3.Location = new System.Drawing.Point(8, 136);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(374, 172);
            this.groupBox3.TabIndex = 28;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "截图完成时";
            // 
            // btnChangeCaptureLocation
            // 
            this.btnChangeCaptureLocation.Location = new System.Drawing.Point(324, 112);
            this.btnChangeCaptureLocation.Name = "btnChangeCaptureLocation";
            this.btnChangeCaptureLocation.Size = new System.Drawing.Size(43, 23);
            this.btnChangeCaptureLocation.TabIndex = 7;
            this.btnChangeCaptureLocation.Text = "更改";
            this.btnChangeCaptureLocation.UseVisualStyleBackColor = true;
            this.btnChangeCaptureLocation.Click += new System.EventHandler(this.btnChangeCaptureLocation_Click);
            // 
            // btnOpenCaptureLocation
            // 
            this.btnOpenCaptureLocation.Location = new System.Drawing.Point(275, 112);
            this.btnOpenCaptureLocation.Name = "btnOpenCaptureLocation";
            this.btnOpenCaptureLocation.Size = new System.Drawing.Size(43, 23);
            this.btnOpenCaptureLocation.TabIndex = 8;
            this.btnOpenCaptureLocation.Text = "打开";
            this.btnOpenCaptureLocation.UseVisualStyleBackColor = true;
            this.btnOpenCaptureLocation.Click += new System.EventHandler(this.btnOpenCaptureLocation_Click);
            // 
            // txtCaptureFileName
            // 
            this.txtCaptureFileName.Location = new System.Drawing.Point(54, 47);
            this.txtCaptureFileName.Name = "txtCaptureFileName";
            this.txtCaptureFileName.Size = new System.Drawing.Size(199, 21);
            this.txtCaptureFileName.TabIndex = 6;
            this.txtCaptureFileName.Tag = "截图文件名";
            this.txtCaptureFileName.Text = "IMG_%n-%y-%r_%s-%f-%m.png";
            this.txtCaptureFileName.TextChanged += new System.EventHandler(this.txtCaptureFileName_TextChanged);
            // 
            // lblCaptureFileName
            // 
            this.lblCaptureFileName.Location = new System.Drawing.Point(52, 74);
            this.lblCaptureFileName.Name = "lblCaptureFileName";
            this.lblCaptureFileName.Size = new System.Drawing.Size(201, 36);
            this.lblCaptureFileName.TabIndex = 5;
            this.lblCaptureFileName.Text = "示例：\r\n截图_2021-03-15_19-03-01.png";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(255, 51);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(113, 48);
            this.label11.TabIndex = 5;
            this.label11.Text = "%n 年份；%y 月份\r\n%r 天数；%s 小时\r\n%f 分钟；%m 秒钟\r\n%t 时间戳；%g 随机";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(6, 51);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(47, 12);
            this.label9.TabIndex = 5;
            this.label9.Text = "文件名:";
            // 
            // txtCaptureLocation
            // 
            this.txtCaptureLocation.Location = new System.Drawing.Point(53, 114);
            this.txtCaptureLocation.Name = "txtCaptureLocation";
            this.txtCaptureLocation.ReadOnly = true;
            this.txtCaptureLocation.Size = new System.Drawing.Size(216, 21);
            this.txtCaptureLocation.TabIndex = 6;
            this.txtCaptureLocation.Tag = "截图文件保存路径";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(17, 118);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(35, 12);
            this.label10.TabIndex = 5;
            this.label10.Text = "路径:";
            // 
            // chkCaptureTip
            // 
            this.chkCaptureTip.AutoSize = true;
            this.chkCaptureTip.Checked = true;
            this.chkCaptureTip.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkCaptureTip.Location = new System.Drawing.Point(91, 23);
            this.chkCaptureTip.Name = "chkCaptureTip";
            this.chkCaptureTip.Size = new System.Drawing.Size(102, 16);
            this.chkCaptureTip.TabIndex = 0;
            this.chkCaptureTip.Text = "显示Toast通知";
            this.chkCaptureTip.UseVisualStyleBackColor = true;
            // 
            // chkAutoSaveCapture
            // 
            this.chkAutoSaveCapture.AutoSize = true;
            this.chkAutoSaveCapture.Checked = true;
            this.chkAutoSaveCapture.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoSaveCapture.Location = new System.Drawing.Point(13, 23);
            this.chkAutoSaveCapture.Name = "chkAutoSaveCapture";
            this.chkAutoSaveCapture.Size = new System.Drawing.Size(72, 16);
            this.chkAutoSaveCapture.TabIndex = 0;
            this.chkAutoSaveCapture.Text = "自动保存";
            this.chkAutoSaveCapture.UseVisualStyleBackColor = true;
            // 
            // chkSaveToClipborad
            // 
            this.chkSaveToClipborad.AutoSize = true;
            this.chkSaveToClipborad.Checked = true;
            this.chkSaveToClipborad.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkSaveToClipborad.Location = new System.Drawing.Point(199, 23);
            this.chkSaveToClipborad.Name = "chkSaveToClipborad";
            this.chkSaveToClipborad.Size = new System.Drawing.Size(96, 16);
            this.chkSaveToClipborad.TabIndex = 0;
            this.chkSaveToClipborad.Text = "复制到粘贴板";
            this.chkSaveToClipborad.UseVisualStyleBackColor = true;
            // 
            // tabPage5
            // 
            this.tabPage5.Controls.Add(this.groupBox14);
            this.tabPage5.Controls.Add(this.groupBox4);
            this.tabPage5.Location = new System.Drawing.Point(4, 22);
            this.tabPage5.Name = "tabPage5";
            this.tabPage5.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage5.Size = new System.Drawing.Size(387, 397);
            this.tabPage5.TabIndex = 4;
            this.tabPage5.Text = "贴图";
            this.tabPage5.UseVisualStyleBackColor = true;
            // 
            // groupBox14
            // 
            this.groupBox14.Controls.Add(this.label14);
            this.groupBox14.Controls.Add(this.label18);
            this.groupBox14.Controls.Add(this.chkTieTuFocus);
            this.groupBox14.Controls.Add(this.label22);
            this.groupBox14.Controls.Add(this.chkTieTuShark);
            this.groupBox14.Controls.Add(this.label15);
            this.groupBox14.Controls.Add(this.numShadowWidth);
            this.groupBox14.Controls.Add(this.btnTieTuShadowColor);
            this.groupBox14.Controls.Add(this.chkTieTuUseCaptureLocation);
            this.groupBox14.Location = new System.Drawing.Point(8, 6);
            this.groupBox14.Name = "groupBox14";
            this.groupBox14.Size = new System.Drawing.Size(374, 102);
            this.groupBox14.TabIndex = 45;
            this.groupBox14.TabStop = false;
            this.groupBox14.Text = "通用";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(13, 26);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(71, 12);
            this.label14.TabIndex = 3;
            this.label14.Text = "窗口阴影色:";
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(254, 26);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(29, 12);
            this.label18.TabIndex = 29;
            this.label18.Text = "像素";
            // 
            // chkTieTuFocus
            // 
            this.chkTieTuFocus.AutoSize = true;
            this.chkTieTuFocus.Checked = true;
            this.chkTieTuFocus.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkTieTuFocus.Location = new System.Drawing.Point(132, 77);
            this.chkTieTuFocus.Name = "chkTieTuFocus";
            this.chkTieTuFocus.Size = new System.Drawing.Size(72, 16);
            this.chkTieTuFocus.TabIndex = 27;
            this.chkTieTuFocus.Text = "激活窗口";
            this.chkTieTuFocus.UseVisualStyleBackColor = true;
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(137, 26);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(59, 12);
            this.label22.TabIndex = 28;
            this.label22.Text = "阴影宽度:";
            // 
            // chkTieTuShark
            // 
            this.chkTieTuShark.AutoSize = true;
            this.chkTieTuShark.Checked = true;
            this.chkTieTuShark.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkTieTuShark.Location = new System.Drawing.Point(78, 77);
            this.chkTieTuShark.Name = "chkTieTuShark";
            this.chkTieTuShark.Size = new System.Drawing.Size(48, 16);
            this.chkTieTuShark.TabIndex = 27;
            this.chkTieTuShark.Text = "闪烁";
            this.chkTieTuShark.UseVisualStyleBackColor = true;
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(13, 78);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(59, 12);
            this.label15.TabIndex = 3;
            this.label15.Text = "新建窗口:";
            // 
            // numShadowWidth
            // 
            this.numShadowWidth.Location = new System.Drawing.Point(199, 22);
            this.numShadowWidth.Name = "numShadowWidth";
            this.numShadowWidth.Size = new System.Drawing.Size(52, 21);
            this.numShadowWidth.TabIndex = 30;
            this.numShadowWidth.Tag = "贴图窗口阴影宽度";
            this.numShadowWidth.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // chkTieTuUseCaptureLocation
            // 
            this.chkTieTuUseCaptureLocation.AutoSize = true;
            this.chkTieTuUseCaptureLocation.Checked = true;
            this.chkTieTuUseCaptureLocation.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkTieTuUseCaptureLocation.Location = new System.Drawing.Point(13, 52);
            this.chkTieTuUseCaptureLocation.Name = "chkTieTuUseCaptureLocation";
            this.chkTieTuUseCaptureLocation.Size = new System.Drawing.Size(168, 16);
            this.chkTieTuUseCaptureLocation.TabIndex = 27;
            this.chkTieTuUseCaptureLocation.Text = "截图贴图时使用截屏的位置";
            this.chkTieTuUseCaptureLocation.UseVisualStyleBackColor = true;
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.btnTieTuDefaultFont);
            this.groupBox4.Controls.Add(this.btnTieTuBackColor);
            this.groupBox4.Controls.Add(this.btnTieTuFont);
            this.groupBox4.Controls.Add(this.btnTieTuFontColor);
            this.groupBox4.Controls.Add(this.numTieTuMaxWidth);
            this.groupBox4.Controls.Add(this.numTieTuBorderWidth);
            this.groupBox4.Controls.Add(this.chkUseContentColor);
            this.groupBox4.Controls.Add(this.chkIngoreHtml);
            this.groupBox4.Controls.Add(this.label24);
            this.groupBox4.Controls.Add(this.label23);
            this.groupBox4.Controls.Add(this.label20);
            this.groupBox4.Controls.Add(this.label21);
            this.groupBox4.Controls.Add(this.lblTieTuLabel);
            this.groupBox4.Location = new System.Drawing.Point(8, 115);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(374, 202);
            this.groupBox4.TabIndex = 28;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "文字转图片";
            // 
            // btnTieTuDefaultFont
            // 
            this.btnTieTuDefaultFont.Location = new System.Drawing.Point(298, 167);
            this.btnTieTuDefaultFont.Name = "btnTieTuDefaultFont";
            this.btnTieTuDefaultFont.Size = new System.Drawing.Size(69, 26);
            this.btnTieTuDefaultFont.TabIndex = 43;
            this.btnTieTuDefaultFont.Tag = "";
            this.btnTieTuDefaultFont.Text = "默认字体";
            this.btnTieTuDefaultFont.UseVisualStyleBackColor = false;
            this.btnTieTuDefaultFont.Click += new System.EventHandler(this.btnContentDefault_Click);
            // 
            // btnTieTuFont
            // 
            this.btnTieTuFont.Location = new System.Drawing.Point(298, 83);
            this.btnTieTuFont.Name = "btnTieTuFont";
            this.btnTieTuFont.Size = new System.Drawing.Size(71, 26);
            this.btnTieTuFont.TabIndex = 40;
            this.btnTieTuFont.TabStop = false;
            this.btnTieTuFont.Tag = "贴图文字字体";
            this.btnTieTuFont.Text = "字体";
            this.btnTieTuFont.UseVisualStyleBackColor = false;
            this.btnTieTuFont.Click += new System.EventHandler(this.btnFont_Click);
            // 
            // numTieTuMaxWidth
            // 
            this.numTieTuMaxWidth.Location = new System.Drawing.Point(250, 41);
            this.numTieTuMaxWidth.Maximum = new decimal(new int[] {
            2000,
            0,
            0,
            0});
            this.numTieTuMaxWidth.Minimum = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numTieTuMaxWidth.Name = "numTieTuMaxWidth";
            this.numTieTuMaxWidth.Size = new System.Drawing.Size(52, 21);
            this.numTieTuMaxWidth.TabIndex = 30;
            this.numTieTuMaxWidth.Tag = "贴图图片最大宽度";
            this.numTieTuMaxWidth.Value = new decimal(new int[] {
            600,
            0,
            0,
            0});
            // 
            // numTieTuBorderWidth
            // 
            this.numTieTuBorderWidth.Location = new System.Drawing.Point(65, 41);
            this.numTieTuBorderWidth.Name = "numTieTuBorderWidth";
            this.numTieTuBorderWidth.Size = new System.Drawing.Size(52, 21);
            this.numTieTuBorderWidth.TabIndex = 30;
            this.numTieTuBorderWidth.Tag = "贴图页边距宽度";
            this.numTieTuBorderWidth.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // chkUseContentColor
            // 
            this.chkUseContentColor.AutoSize = true;
            this.chkUseContentColor.Checked = true;
            this.chkUseContentColor.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkUseContentColor.Location = new System.Drawing.Point(15, 69);
            this.chkUseContentColor.Name = "chkUseContentColor";
            this.chkUseContentColor.Size = new System.Drawing.Size(144, 16);
            this.chkUseContentColor.TabIndex = 27;
            this.chkUseContentColor.Text = "使用界面设置中的字体";
            this.chkUseContentColor.UseVisualStyleBackColor = true;
            this.chkUseContentColor.CheckedChanged += new System.EventHandler(this.chkUseContentColor_CheckedChanged);
            // 
            // chkIngoreHtml
            // 
            this.chkIngoreHtml.AutoSize = true;
            this.chkIngoreHtml.Location = new System.Drawing.Point(16, 20);
            this.chkIngoreHtml.Name = "chkIngoreHtml";
            this.chkIngoreHtml.Size = new System.Drawing.Size(204, 16);
            this.chkIngoreHtml.TabIndex = 27;
            this.chkIngoreHtml.Text = "忽略Html格式以文字方式生成图片";
            this.chkIngoreHtml.UseVisualStyleBackColor = true;
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(187, 45);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(59, 12);
            this.label24.TabIndex = 28;
            this.label24.Text = "最大宽度:";
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(305, 45);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(29, 12);
            this.label23.TabIndex = 29;
            this.label23.Text = "像素";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(14, 45);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(47, 12);
            this.label20.TabIndex = 28;
            this.label20.Text = "页边距:";
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(120, 45);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(29, 12);
            this.label21.TabIndex = 29;
            this.label21.Text = "像素";
            // 
            // lblTieTuLabel
            // 
            this.lblTieTuLabel.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.lblTieTuLabel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.lblTieTuLabel.Location = new System.Drawing.Point(16, 88);
            this.lblTieTuLabel.Name = "lblTieTuLabel";
            this.lblTieTuLabel.Size = new System.Drawing.Size(276, 104);
            this.lblTieTuLabel.TabIndex = 19;
            this.lblTieTuLabel.Text = "预览\r\nAaOo012345";
            this.lblTieTuLabel.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.groupBox13);
            this.tabPage3.Controls.Add(this.groupBox6);
            this.tabPage3.Location = new System.Drawing.Point(4, 22);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage3.Size = new System.Drawing.Size(387, 397);
            this.tabPage3.TabIndex = 7;
            this.tabPage3.Text = "工具";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // groupBox13
            // 
            this.groupBox13.Controls.Add(this.cmbHex);
            this.groupBox13.Controls.Add(this.label5);
            this.groupBox13.Controls.Add(this.chkHexUpper);
            this.groupBox13.Location = new System.Drawing.Point(8, 139);
            this.groupBox13.Name = "groupBox13";
            this.groupBox13.Size = new System.Drawing.Size(374, 45);
            this.groupBox13.TabIndex = 31;
            this.groupBox13.TabStop = false;
            this.groupBox13.Text = "取色器";
            // 
            // cmbHex
            // 
            this.cmbHex.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbHex.FormattingEnabled = true;
            this.cmbHex.Items.AddRange(new object[] {
            "RGB",
            "HEX"});
            this.cmbHex.Location = new System.Drawing.Point(71, 16);
            this.cmbHex.Name = "cmbHex";
            this.cmbHex.Size = new System.Drawing.Size(82, 20);
            this.cmbHex.TabIndex = 3;
            this.cmbHex.Tag = "取色器文字样式";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(13, 20);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(59, 12);
            this.label5.TabIndex = 2;
            this.label5.Text = "取色器值:";
            // 
            // chkHexUpper
            // 
            this.chkHexUpper.AutoSize = true;
            this.chkHexUpper.Location = new System.Drawing.Point(181, 18);
            this.chkHexUpper.Name = "chkHexUpper";
            this.chkHexUpper.Size = new System.Drawing.Size(102, 16);
            this.chkHexUpper.TabIndex = 26;
            this.chkHexUpper.Text = "HEX颜色值大写";
            this.chkHexUpper.UseVisualStyleBackColor = true;
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.checkBox2);
            this.groupBox6.Controls.Add(this.cmbRulerOpacity);
            this.groupBox6.Controls.Add(this.cmbRulerTheme);
            this.groupBox6.Controls.Add(this.label17);
            this.groupBox6.Controls.Add(this.cmbRulerUnit);
            this.groupBox6.Controls.Add(this.label13);
            this.groupBox6.Controls.Add(this.label16);
            this.groupBox6.Location = new System.Drawing.Point(8, 6);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(374, 125);
            this.groupBox6.TabIndex = 27;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "标尺";
            // 
            // checkBox2
            // 
            this.checkBox2.AutoSize = true;
            this.checkBox2.Checked = true;
            this.checkBox2.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox2.Location = new System.Drawing.Point(13, 20);
            this.checkBox2.Name = "checkBox2";
            this.checkBox2.Size = new System.Drawing.Size(72, 16);
            this.checkBox2.TabIndex = 30;
            this.checkBox2.Text = "标尺置顶";
            this.checkBox2.UseVisualStyleBackColor = true;
            // 
            // cmbRulerOpacity
            // 
            this.cmbRulerOpacity.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbRulerOpacity.FormattingEnabled = true;
            this.cmbRulerOpacity.Location = new System.Drawing.Point(71, 68);
            this.cmbRulerOpacity.Name = "cmbRulerOpacity";
            this.cmbRulerOpacity.Size = new System.Drawing.Size(121, 20);
            this.cmbRulerOpacity.TabIndex = 28;
            this.cmbRulerOpacity.Tag = "标尺透明度";
            // 
            // cmbRulerTheme
            // 
            this.cmbRulerTheme.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbRulerTheme.FormattingEnabled = true;
            this.cmbRulerTheme.Location = new System.Drawing.Point(71, 42);
            this.cmbRulerTheme.Name = "cmbRulerTheme";
            this.cmbRulerTheme.Size = new System.Drawing.Size(121, 20);
            this.cmbRulerTheme.TabIndex = 28;
            this.cmbRulerTheme.Tag = "标尺默认主题";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(13, 72);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(59, 12);
            this.label17.TabIndex = 27;
            this.label17.Text = "透 明 度:";
            // 
            // cmbRulerUnit
            // 
            this.cmbRulerUnit.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbRulerUnit.FormattingEnabled = true;
            this.cmbRulerUnit.Location = new System.Drawing.Point(71, 94);
            this.cmbRulerUnit.Name = "cmbRulerUnit";
            this.cmbRulerUnit.Size = new System.Drawing.Size(121, 20);
            this.cmbRulerUnit.TabIndex = 28;
            this.cmbRulerUnit.Tag = "标尺计量单位";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(13, 46);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(59, 12);
            this.label13.TabIndex = 27;
            this.label13.Text = "默认主题:";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(13, 98);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(59, 12);
            this.label16.TabIndex = 27;
            this.label16.Text = "计量单位:";
            // 
            // tbShortKey
            // 
            this.tbShortKey.Controls.Add(this.tabHotKeys);
            this.tbShortKey.Location = new System.Drawing.Point(4, 22);
            this.tbShortKey.Name = "tbShortKey";
            this.tbShortKey.Padding = new System.Windows.Forms.Padding(3);
            this.tbShortKey.Size = new System.Drawing.Size(387, 397);
            this.tbShortKey.TabIndex = 2;
            this.tbShortKey.Text = "快捷键";
            this.tbShortKey.UseVisualStyleBackColor = true;
            // 
            // tabHotKeys
            // 
            this.tabHotKeys.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabHotKeys.Location = new System.Drawing.Point(3, 3);
            this.tabHotKeys.Name = "tabHotKeys";
            this.tabHotKeys.SelectedIndex = 0;
            this.tabHotKeys.Size = new System.Drawing.Size(381, 391);
            this.tabHotKeys.TabIndex = 0;
            // 
            // tabPage6
            // 
            this.tabPage6.Controls.Add(this.groupBox9);
            this.tabPage6.Controls.Add(this.groupBox5);
            this.tabPage6.Controls.Add(this.label38);
            this.tabPage6.Controls.Add(this.lblCopyright);
            this.tabPage6.Controls.Add(this.btnUpgrade);
            this.tabPage6.Controls.Add(this.lblVersion);
            this.tabPage6.Controls.Add(this.lnkWebSite);
            this.tabPage6.Controls.Add(this.lblName);
            this.tabPage6.Controls.Add(this.picIcon);
            this.tabPage6.Location = new System.Drawing.Point(4, 22);
            this.tabPage6.Name = "tabPage6";
            this.tabPage6.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage6.Size = new System.Drawing.Size(387, 397);
            this.tabPage6.TabIndex = 5;
            this.tabPage6.Text = "关于";
            this.tabPage6.UseVisualStyleBackColor = true;
            // 
            // groupBox9
            // 
            this.groupBox9.Controls.Add(this.pictureBox4);
            this.groupBox9.Controls.Add(this.pictureBox7);
            this.groupBox9.Controls.Add(this.pictureBox6);
            this.groupBox9.Controls.Add(this.pictureBox5);
            this.groupBox9.Controls.Add(this.linkLabel2);
            this.groupBox9.Controls.Add(this.linkLabel4);
            this.groupBox9.Controls.Add(this.linkLabel3);
            this.groupBox9.Controls.Add(this.linkLabel1);
            this.groupBox9.Location = new System.Drawing.Point(6, 148);
            this.groupBox9.Name = "groupBox9";
            this.groupBox9.Size = new System.Drawing.Size(374, 85);
            this.groupBox9.TabIndex = 34;
            this.groupBox9.TabStop = false;
            this.groupBox9.Text = "联系我们";
            // 
            // linkLabel2
            // 
            this.linkLabel2.AutoSize = true;
            this.linkLabel2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.linkLabel2.Location = new System.Drawing.Point(40, 55);
            this.linkLabel2.Name = "linkLabel2";
            this.linkLabel2.Size = new System.Drawing.Size(181, 12);
            this.linkLabel2.TabIndex = 0;
            this.linkLabel2.TabStop = true;
            this.linkLabel2.Text = "助手交流群(QQ群:" + CommonMethod.StrKeFuQun + ")";
            this.linkLabel2.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel2_LinkClicked);
            // 
            // linkLabel4
            // 
            this.linkLabel4.AutoSize = true;
            this.linkLabel4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.linkLabel4.Location = new System.Drawing.Point(260, 55);
            this.linkLabel4.Name = "linkLabel4";
            this.linkLabel4.Size = new System.Drawing.Size(57, 12);
            this.linkLabel4.TabIndex = 0;
            this.linkLabel4.TabStop = true;
            this.linkLabel4.Text = "意见建议";
            this.linkLabel4.Click += new System.EventHandler(this.btnSupport_Click);
            // 
            // linkLabel3
            // 
            this.linkLabel3.AutoSize = true;
            this.linkLabel3.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.linkLabel3.Location = new System.Drawing.Point(260, 28);
            this.linkLabel3.Name = "linkLabel3";
            this.linkLabel3.Size = new System.Drawing.Size(57, 12);
            this.linkLabel3.TabIndex = 0;
            this.linkLabel3.TabStop = true;
            this.linkLabel3.Text = "问题反馈";
            this.linkLabel3.Click += new System.EventHandler(this.btnSupport_Click);
            // 
            // linkLabel1
            // 
            this.linkLabel1.AutoSize = true;
            this.linkLabel1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.linkLabel1.Location = new System.Drawing.Point(40, 28);
            this.linkLabel1.Name = "linkLabel1";
            this.linkLabel1.Size = new System.Drawing.Size(155, 12);
            this.linkLabel1.TabIndex = 0;
            this.linkLabel1.TabStop = true;
            this.linkLabel1.Text = "在线客服(QQ:" + CommonMethod.StrKeFuQ + ")";
            this.linkLabel1.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel1_LinkClicked);
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.numUpdateHour);
            this.groupBox5.Controls.Add(this.btnCheckUpdate);
            this.groupBox5.Controls.Add(this.label28);
            this.groupBox5.Controls.Add(this.label29);
            this.groupBox5.Controls.Add(this.chkAutoUpdate);
            this.groupBox5.Location = new System.Drawing.Point(3, 242);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(374, 85);
            this.groupBox5.TabIndex = 29;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "自动更新";
            // 
            // numUpdateHour
            // 
            this.numUpdateHour.Location = new System.Drawing.Point(54, 45);
            this.numUpdateHour.Maximum = new decimal(new int[] {
            24,
            0,
            0,
            0});
            this.numUpdateHour.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numUpdateHour.Name = "numUpdateHour";
            this.numUpdateHour.Size = new System.Drawing.Size(52, 21);
            this.numUpdateHour.TabIndex = 33;
            this.numUpdateHour.Tag = "自动更新间隔";
            this.numUpdateHour.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(12, 49);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(35, 12);
            this.label28.TabIndex = 31;
            this.label28.Text = "间隔:";
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(109, 49);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(29, 12);
            this.label29.TabIndex = 32;
            this.label29.Text = "小时";
            // 
            // chkAutoUpdate
            // 
            this.chkAutoUpdate.AutoSize = true;
            this.chkAutoUpdate.Checked = true;
            this.chkAutoUpdate.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkAutoUpdate.Location = new System.Drawing.Point(11, 23);
            this.chkAutoUpdate.Name = "chkAutoUpdate";
            this.chkAutoUpdate.Size = new System.Drawing.Size(108, 16);
            this.chkAutoUpdate.TabIndex = 0;
            this.chkAutoUpdate.Text = "启动时检查更新";
            this.chkAutoUpdate.UseVisualStyleBackColor = true;
            // 
            // label38
            // 
            this.label38.AutoSize = true;
            this.label38.Font = new System.Drawing.Font("宋体", 10F);
            this.label38.Location = new System.Drawing.Point(18, 120);
            this.label38.Name = "label38";
            this.label38.Size = new System.Drawing.Size(35, 14);
            this.label38.TabIndex = 28;
            this.label38.Text = "官网";
            // 
            // lblCopyright
            // 
            this.lblCopyright.AutoSize = true;
            this.lblCopyright.Font = new System.Drawing.Font("宋体", 10F);
            this.lblCopyright.Location = new System.Drawing.Point(18, 98);
            this.lblCopyright.Name = "lblCopyright";
            this.lblCopyright.Size = new System.Drawing.Size(35, 14);
            this.lblCopyright.TabIndex = 28;
            this.lblCopyright.Text = "版权";
            // 
            // btnUpgrade
            // 
            this.btnUpgrade.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnUpgrade.Location = new System.Drawing.Point(237, 24);
            this.btnUpgrade.Name = "btnUpgrade";
            this.btnUpgrade.Size = new System.Drawing.Size(125, 39);
            this.btnUpgrade.TabIndex = 7;
            this.btnUpgrade.Text = "升级为…";
            this.btnUpgrade.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnUpgrade.UseVisualStyleBackColor = true;
            this.btnUpgrade.Click += new System.EventHandler(this.btnUpgrade_Click);
            // 
            // lblVersion
            // 
            this.lblVersion.AutoSize = true;
            this.lblVersion.Font = new System.Drawing.Font("宋体", 10F);
            this.lblVersion.Location = new System.Drawing.Point(18, 76);
            this.lblVersion.Name = "lblVersion";
            this.lblVersion.Size = new System.Drawing.Size(35, 14);
            this.lblVersion.TabIndex = 28;
            this.lblVersion.Text = "版本";
            // 
            // lnkWebSite
            // 
            this.lnkWebSite.AutoSize = true;
            this.lnkWebSite.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lnkWebSite.Location = new System.Drawing.Point(54, 117);
            this.lnkWebSite.Name = "lnkWebSite";
            this.lnkWebSite.Size = new System.Drawing.Size(139, 17);
            this.lnkWebSite.TabIndex = 0;
            this.lnkWebSite.TabStop = true;
            this.lnkWebSite.Text = CommonString.StrServerHostUrl;
            this.lnkWebSite.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lnkWebSite_LinkClicked);
            // 
            // lblName
            // 
            this.lblName.AutoSize = true;
            this.lblName.Font = new System.Drawing.Font("微软雅黑", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblName.Location = new System.Drawing.Point(66, 30);
            this.lblName.Name = "lblName";
            this.lblName.Size = new System.Drawing.Size(21, 26);
            this.lblName.TabIndex = 27;
            this.lblName.Text = "*";
            // 
            // label33
            // 
            this.label33.AutoSize = true;
            this.label33.Location = new System.Drawing.Point(9, 69);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(71, 12);
            this.label33.TabIndex = 27;
            this.label33.Text = "双击工具栏:";
            // 
            // groupBox18
            // 
            this.groupBox18.Controls.Add(this.pictureBox8);
            this.groupBox18.Controls.Add(this.cmbDoubleClick);
            this.groupBox18.Controls.Add(this.numToolShadowWidth);
            this.groupBox18.Controls.Add(this.label33);
            this.groupBox18.Controls.Add(this.label2);
            this.groupBox18.Controls.Add(this.chkToolBarCircle);
            this.groupBox18.Controls.Add(this.cmbToolBarSize);
            this.groupBox18.Controls.Add(this.label40);
            this.groupBox18.Controls.Add(this.label32);
            this.groupBox18.Controls.Add(this.chkToolShadow);
            this.groupBox18.Location = new System.Drawing.Point(0, 179);
            this.groupBox18.Name = "groupBox18";
            this.groupBox18.Size = new System.Drawing.Size(368, 90);
            this.groupBox18.TabIndex = 46;
            this.groupBox18.TabStop = false;
            this.groupBox18.Text = "其他设置";
            // 
            // label40
            // 
            this.label40.AutoSize = true;
            this.label40.Location = new System.Drawing.Point(279, 45);
            this.label40.Name = "label40";
            this.label40.Size = new System.Drawing.Size(29, 12);
            this.label40.TabIndex = 41;
            this.label40.Text = "像素";
            // 
            // label41
            // 
            this.label41.AutoSize = true;
            this.label41.Location = new System.Drawing.Point(13, 101);
            this.label41.Name = "label41";
            this.label41.Size = new System.Drawing.Size(71, 12);
            this.label41.TabIndex = 43;
            this.label41.Text = "截图边框色:";
            // 
            // groupBox19
            // 
            this.groupBox19.Controls.Add(this.label36);
            this.groupBox19.Controls.Add(this.numericUpDown1);
            this.groupBox19.Controls.Add(this.label37);
            this.groupBox19.Controls.Add(this.pictureBox11);
            this.groupBox19.Location = new System.Drawing.Point(7, 312);
            this.groupBox19.Name = "groupBox19";
            this.groupBox19.Size = new System.Drawing.Size(374, 58);
            this.groupBox19.TabIndex = 29;
            this.groupBox19.TabStop = false;
            this.groupBox19.Text = "其他设置";
            // 
            // checkBox1
            // 
            this.checkBox1.AutoSize = true;
            this.checkBox1.Checked = true;
            this.checkBox1.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox1.Location = new System.Drawing.Point(11, 46);
            this.checkBox1.Name = "checkBox1";
            this.checkBox1.Size = new System.Drawing.Size(108, 16);
            this.checkBox1.TabIndex = 4;
            this.checkBox1.Text = "自动全半角转换";
            this.checkBox1.UseVisualStyleBackColor = true;
            // 
            // checkBox5
            // 
            this.checkBox5.AutoSize = true;
            this.checkBox5.Checked = true;
            this.checkBox5.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox5.Location = new System.Drawing.Point(176, 46);
            this.checkBox5.Name = "checkBox5";
            this.checkBox5.Size = new System.Drawing.Size(72, 16);
            this.checkBox5.TabIndex = 4;
            this.checkBox5.Text = "自动空格";
            this.checkBox5.UseVisualStyleBackColor = true;
            // 
            // checkBox6
            // 
            this.checkBox6.AutoSize = true;
            this.checkBox6.Checked = true;
            this.checkBox6.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox6.Location = new System.Drawing.Point(176, 72);
            this.checkBox6.Name = "checkBox6";
            this.checkBox6.Size = new System.Drawing.Size(96, 16);
            this.checkBox6.TabIndex = 45;
            this.checkBox6.Text = "去除重复标点";
            this.checkBox6.UseVisualStyleBackColor = true;
            // 
            // checkBox7
            // 
            this.checkBox7.AutoSize = true;
            this.checkBox7.Checked = true;
            this.checkBox7.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox7.Location = new System.Drawing.Point(11, 72);
            this.checkBox7.Name = "checkBox7";
            this.checkBox7.Size = new System.Drawing.Size(108, 16);
            this.checkBox7.TabIndex = 46;
            this.checkBox7.Text = "自动中英文标点";
            this.checkBox7.UseVisualStyleBackColor = true;
            // 
            // pictureBox9
            // 
            this.pictureBox9.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox9.Image = global::OCRTools.Properties.Resources.uac;
            this.pictureBox9.Location = new System.Drawing.Point(85, 19);
            this.pictureBox9.Name = "pictureBox9";
            this.pictureBox9.Size = new System.Drawing.Size(16, 16);
            this.pictureBox9.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox9.TabIndex = 45;
            this.pictureBox9.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox9, "功能：设置是否开机自启动。\r\n说明：\r\n      如果杀软拦截，请允许或者添加排除！\r\n      如果权限不足，请尝试右键->以管理员方式打开程序");
            // 
            // pictureBox10
            // 
            this.pictureBox10.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox10.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox10.Location = new System.Drawing.Point(128, 41);
            this.pictureBox10.Name = "pictureBox10";
            this.pictureBox10.Size = new System.Drawing.Size(26, 23);
            this.pictureBox10.TabIndex = 46;
            this.pictureBox10.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox10, "功能：设置程序启动后，是否默认打开主窗体。\r\n说明：\r\n      设置不打开，则以静默方式启动，可以双击工具栏/右下角图标打开！");
            // 
            // picLoadingImage
            // 
            this.picLoadingImage.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.picLoadingImage.Location = new System.Drawing.Point(241, 39);
            this.picLoadingImage.Name = "picLoadingImage";
            this.picLoadingImage.Size = new System.Drawing.Size(30, 30);
            this.picLoadingImage.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.picLoadingImage.TabIndex = 25;
            this.picLoadingImage.TabStop = false;
            // 
            // pictureBox8
            // 
            this.pictureBox8.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox8.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox8.Location = new System.Drawing.Point(104, 39);
            this.pictureBox8.Name = "pictureBox8";
            this.pictureBox8.Size = new System.Drawing.Size(26, 23);
            this.pictureBox8.TabIndex = 44;
            this.pictureBox8.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox8, "功能：工具栏图标阴影效果。\r\n说明：\r\n      支持异形透明图片(非圆形图标，不支持阴影)！\r\n      不透明图片，支持阴影。");
            // 
            // pictureBox17
            // 
            this.pictureBox17.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox17.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox17.Location = new System.Drawing.Point(102, 73);
            this.pictureBox17.Margin = new System.Windows.Forms.Padding(0);
            this.pictureBox17.Name = "pictureBox17";
            this.pictureBox17.Size = new System.Drawing.Size(26, 21);
            this.pictureBox17.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            this.pictureBox17.TabIndex = 44;
            this.pictureBox17.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox17, "功能：以当前IP所在位置的天气设置为图标。\r\n说明：\r\n      天气更新间隔为30分钟。\r\n      气象预警信息，以弹框方式提示。\r\n");
            // 
            // picToolBar
            // 
            this.picToolBar.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.picToolBar.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.picToolBar.Location = new System.Drawing.Point(247, 53);
            this.picToolBar.Name = "picToolBar";
            this.picToolBar.Size = new System.Drawing.Size(120, 120);
            this.picToolBar.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            this.picToolBar.TabIndex = 25;
            this.picToolBar.TabStop = false;
            // 
            // pictureBox13
            // 
            this.pictureBox13.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox13.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox13.Location = new System.Drawing.Point(271, 14);
            this.pictureBox13.Name = "pictureBox13";
            this.pictureBox13.Size = new System.Drawing.Size(28, 24);
            this.pictureBox13.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox13.TabIndex = 43;
            this.pictureBox13.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox13, "功能：图文模式下，在图上显示文字预览。\r\n说明：\r\n      此操作会增加性能消耗，可酌情打开！");
            // 
            // pictureBox3
            // 
            this.pictureBox3.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox3.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox3.Location = new System.Drawing.Point(146, 14);
            this.pictureBox3.Name = "pictureBox3";
            this.pictureBox3.Size = new System.Drawing.Size(28, 24);
            this.pictureBox3.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox3.TabIndex = 44;
            this.pictureBox3.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox3, "功能：OCR完成后，自动复制识别结果到粘贴板。\r\n说明：如果不需要复制，请不要勾选！");
            // 
            // pictureBox15
            // 
            this.pictureBox15.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox15.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox15.Location = new System.Drawing.Point(300, 14);
            this.pictureBox15.Name = "pictureBox15";
            this.pictureBox15.Size = new System.Drawing.Size(28, 24);
            this.pictureBox15.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox15.TabIndex = 44;
            this.pictureBox15.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox15, "功能：翻译模式时，是否复制原文。\r\n说明：不勾选，仅复制译文，否则，复制原文+译文！");
            // 
            // pictureBox1
            // 
            this.pictureBox1.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox1.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox1.Location = new System.Drawing.Point(159, 38);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new System.Drawing.Size(28, 24);
            this.pictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox1.TabIndex = 44;
            this.pictureBox1.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox1, "功能：选择复制哪一个识别结果。\r\n说明：体验版以上版本，会有多个识别结果，可以选择复制哪一个！\r\n\r\n选项说明\r\n最快：第一个\r\n最新：最后一个\r\n所有：所有结果" +
        "累加");
            // 
            // pictureBox20
            // 
            this.pictureBox20.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox20.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox20.Location = new System.Drawing.Point(272, 64);
            this.pictureBox20.Name = "pictureBox20";
            this.pictureBox20.Size = new System.Drawing.Size(28, 24);
            this.pictureBox20.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox20.TabIndex = 47;
            this.pictureBox20.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox20, "功能：重复标点校正.\r\n\r\n正确：\r\n德国队竟然战胜了巴西队！\r\n她竟然对你说「喵」？！\r\n\r\n错误：\r\n德国队竟然战胜了巴西队！！\r\n她竟然对你说「喵」？？！！" +
        "");
            // 
            // pictureBox21
            // 
            this.pictureBox21.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox21.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox21.Location = new System.Drawing.Point(117, 66);
            this.pictureBox21.Name = "pictureBox21";
            this.pictureBox21.Size = new System.Drawing.Size(28, 24);
            this.pictureBox21.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox21.TabIndex = 48;
            this.pictureBox21.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox21, "功能：\r\n自动根据语境，把文字中的标点符号转换为中/英文标点");
            // 
            // pictureBox19
            // 
            this.pictureBox19.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox19.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox19.Location = new System.Drawing.Point(246, 39);
            this.pictureBox19.Name = "pictureBox19";
            this.pictureBox19.Size = new System.Drawing.Size(28, 24);
            this.pictureBox19.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox19.TabIndex = 44;
            this.pictureBox19.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox19, resources.GetString("pictureBox19.ToolTip"));
            // 
            // pictureBox18
            // 
            this.pictureBox18.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox18.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox18.Location = new System.Drawing.Point(117, 40);
            this.pictureBox18.Name = "pictureBox18";
            this.pictureBox18.Size = new System.Drawing.Size(28, 24);
            this.pictureBox18.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox18.TabIndex = 44;
            this.pictureBox18.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox18, resources.GetString("pictureBox18.ToolTip"));
            // 
            // pictureBox12
            // 
            this.pictureBox12.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox12.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox12.Location = new System.Drawing.Point(246, 14);
            this.pictureBox12.Name = "pictureBox12";
            this.pictureBox12.Size = new System.Drawing.Size(28, 24);
            this.pictureBox12.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox12.TabIndex = 44;
            this.pictureBox12.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox12, "功能：段落开头是否缩进。\r\n说明：对于文档排版更友好，如果只需要识别文字，可关闭");
            // 
            // pictureBox16
            // 
            this.pictureBox16.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox16.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox16.Location = new System.Drawing.Point(87, 15);
            this.pictureBox16.Name = "pictureBox16";
            this.pictureBox16.Size = new System.Drawing.Size(28, 24);
            this.pictureBox16.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox16.TabIndex = 44;
            this.pictureBox16.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox16, "功能：加快识别速度！\r\n说明：如果开启，可以加快识别速度，图越大效果越明显。\r\n可自行选择是否开启！");
            // 
            // pictureBox14
            // 
            this.pictureBox14.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox14.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox14.Location = new System.Drawing.Point(140, 40);
            this.pictureBox14.Name = "pictureBox14";
            this.pictureBox14.Size = new System.Drawing.Size(28, 24);
            this.pictureBox14.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox14.TabIndex = 43;
            this.pictureBox14.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox14, "功能：支持市面上十几种主流识别引擎。\r\n说明：可以根据识别结果，自行选择！");
            // 
            // pictureBox11
            // 
            this.pictureBox11.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox11.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox11.Location = new System.Drawing.Point(139, 17);
            this.pictureBox11.Name = "pictureBox11";
            this.pictureBox11.Size = new System.Drawing.Size(28, 24);
            this.pictureBox11.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox11.TabIndex = 42;
            this.pictureBox11.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox11, "功能：延时截图前，等待指定时间后触发截图。\r\n说明：仅针对延时截图功能，普通截图不延时\r\n");
            // 
            // pictureBox2
            // 
            this.pictureBox2.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox2.Image = global::OCRTools.Properties.Resources.帮助;
            this.pictureBox2.Location = new System.Drawing.Point(233, 68);
            this.pictureBox2.Name = "pictureBox2";
            this.pictureBox2.Size = new System.Drawing.Size(28, 24);
            this.pictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox2.TabIndex = 42;
            this.pictureBox2.TabStop = false;
            this.tipMsg.SetToolTip(this.pictureBox2, "功能：自动检测窗口所有可见的元素。\r\n说明：可在截图过程中按【Ctrl键】切换窗口模式");
            // 
            // pictureBox4
            // 
            this.pictureBox4.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox4.Image = global::OCRTools.Properties.Resources.qqQun;
            this.pictureBox4.Location = new System.Drawing.Point(21, 49);
            this.pictureBox4.Name = "pictureBox4";
            this.pictureBox4.Size = new System.Drawing.Size(20, 20);
            this.pictureBox4.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox4.TabIndex = 43;
            this.pictureBox4.TabStop = false;
            // 
            // pictureBox7
            // 
            this.pictureBox7.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox7.Image = global::OCRTools.Properties.Resources.fankui;
            this.pictureBox7.Location = new System.Drawing.Point(241, 53);
            this.pictureBox7.Name = "pictureBox7";
            this.pictureBox7.Size = new System.Drawing.Size(16, 16);
            this.pictureBox7.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox7.TabIndex = 44;
            this.pictureBox7.TabStop = false;
            // 
            // pictureBox6
            // 
            this.pictureBox6.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox6.Image = global::OCRTools.Properties.Resources.traffic_cone;
            this.pictureBox6.Location = new System.Drawing.Point(241, 26);
            this.pictureBox6.Name = "pictureBox6";
            this.pictureBox6.Size = new System.Drawing.Size(16, 16);
            this.pictureBox6.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox6.TabIndex = 44;
            this.pictureBox6.TabStop = false;
            // 
            // pictureBox5
            // 
            this.pictureBox5.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.pictureBox5.Image = global::OCRTools.Properties.Resources.qqKeFu;
            this.pictureBox5.Location = new System.Drawing.Point(21, 21);
            this.pictureBox5.Name = "pictureBox5";
            this.pictureBox5.Size = new System.Drawing.Size(20, 20);
            this.pictureBox5.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox5.TabIndex = 44;
            this.pictureBox5.TabStop = false;
            // 
            // btnCheckUpdate
            // 
            this.btnCheckUpdate.Image = global::OCRTools.Properties.Resources.LoadingSmallBlack;
            this.btnCheckUpdate.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnCheckUpdate.Location = new System.Drawing.Point(168, 40);
            this.btnCheckUpdate.Name = "btnCheckUpdate";
            this.btnCheckUpdate.Size = new System.Drawing.Size(83, 26);
            this.btnCheckUpdate.TabIndex = 40;
            this.btnCheckUpdate.TabStop = false;
            this.btnCheckUpdate.Text = "检查更新";
            this.btnCheckUpdate.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnCheckUpdate.UseVisualStyleBackColor = false;
            this.btnCheckUpdate.Click += new System.EventHandler(this.btnCheckUpdate_Click);
            // 
            // picIcon
            // 
            this.picIcon.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.picIcon.Location = new System.Drawing.Point(20, 20);
            this.picIcon.Name = "picIcon";
            this.picIcon.Size = new System.Drawing.Size(45, 45);
            this.picIcon.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.picIcon.TabIndex = 26;
            this.picIcon.TabStop = false;
            // 
            // btnContentBackColor
            // 
            this.btnContentBackColor.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.btnContentBackColor.Image = ((System.Drawing.Image)(resources.GetObject("btnContentBackColor.Image")));
            this.btnContentBackColor.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnContentBackColor.ImageColor = System.Drawing.Color.Black;
            this.btnContentBackColor.Location = new System.Drawing.Point(293, 74);
            this.btnContentBackColor.Name = "btnContentBackColor";
            this.btnContentBackColor.Size = new System.Drawing.Size(69, 26);
            this.btnContentBackColor.TabIndex = 38;
            this.btnContentBackColor.Tag = "默认背景颜色";
            this.btnContentBackColor.Text = "背景色";
            this.btnContentBackColor.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnContentBackColor.UseVisualStyleBackColor = false;
            this.btnContentBackColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // btnContentFontColor
            // 
            this.btnContentFontColor.Image = ((System.Drawing.Image)(resources.GetObject("btnContentFontColor.Image")));
            this.btnContentFontColor.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnContentFontColor.ImageColor = System.Drawing.Color.Black;
            this.btnContentFontColor.Location = new System.Drawing.Point(293, 46);
            this.btnContentFontColor.Name = "btnContentFontColor";
            this.btnContentFontColor.Size = new System.Drawing.Size(71, 26);
            this.btnContentFontColor.TabIndex = 37;
            this.btnContentFontColor.Tag = "默认文字颜色";
            this.btnContentFontColor.Text = "文本色";
            this.btnContentFontColor.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnContentFontColor.UseVisualStyleBackColor = false;
            this.btnContentFontColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // imageButton1
            // 
            this.imageButton1.BackColor = System.Drawing.Color.White;
            this.imageButton1.Image = ((System.Drawing.Image)(resources.GetObject("imageButton1.Image")));
            this.imageButton1.ImageColor = System.Drawing.Color.Black;
            this.imageButton1.Location = new System.Drawing.Point(85, 97);
            this.imageButton1.Name = "imageButton1";
            this.imageButton1.Size = new System.Drawing.Size(25, 20);
            this.imageButton1.TabIndex = 44;
            this.imageButton1.Tag = "截图边框颜色";
            this.imageButton1.UseVisualStyleBackColor = false;
            this.imageButton1.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // btnTieTuShadowColor
            // 
            this.btnTieTuShadowColor.BackColor = System.Drawing.Color.White;
            this.btnTieTuShadowColor.Image = ((System.Drawing.Image)(resources.GetObject("btnTieTuShadowColor.Image")));
            this.btnTieTuShadowColor.ImageColor = System.Drawing.Color.Black;
            this.btnTieTuShadowColor.Location = new System.Drawing.Point(88, 22);
            this.btnTieTuShadowColor.Name = "btnTieTuShadowColor";
            this.btnTieTuShadowColor.Size = new System.Drawing.Size(25, 20);
            this.btnTieTuShadowColor.TabIndex = 4;
            this.btnTieTuShadowColor.Tag = "贴图窗口阴影色";
            this.btnTieTuShadowColor.UseVisualStyleBackColor = false;
            this.btnTieTuShadowColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // btnTieTuBackColor
            // 
            this.btnTieTuBackColor.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.btnTieTuBackColor.Image = ((System.Drawing.Image)(resources.GetObject("btnTieTuBackColor.Image")));
            this.btnTieTuBackColor.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnTieTuBackColor.ImageColor = System.Drawing.Color.Black;
            this.btnTieTuBackColor.Location = new System.Drawing.Point(298, 139);
            this.btnTieTuBackColor.Name = "btnTieTuBackColor";
            this.btnTieTuBackColor.Size = new System.Drawing.Size(69, 26);
            this.btnTieTuBackColor.TabIndex = 42;
            this.btnTieTuBackColor.Tag = "贴图背景颜色";
            this.btnTieTuBackColor.Text = "背景色";
            this.btnTieTuBackColor.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnTieTuBackColor.UseVisualStyleBackColor = false;
            this.btnTieTuBackColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // btnTieTuFontColor
            // 
            this.btnTieTuFontColor.Image = ((System.Drawing.Image)(resources.GetObject("btnTieTuFontColor.Image")));
            this.btnTieTuFontColor.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnTieTuFontColor.ImageColor = System.Drawing.Color.Black;
            this.btnTieTuFontColor.Location = new System.Drawing.Point(298, 111);
            this.btnTieTuFontColor.Name = "btnTieTuFontColor";
            this.btnTieTuFontColor.Size = new System.Drawing.Size(71, 26);
            this.btnTieTuFontColor.TabIndex = 41;
            this.btnTieTuFontColor.Tag = "贴图文字颜色";
            this.btnTieTuFontColor.Text = "文本色";
            this.btnTieTuFontColor.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnTieTuFontColor.UseVisualStyleBackColor = false;
            this.btnTieTuFontColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // FormSetting
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(415, 493);
            this.Controls.Add(this.tbConfig);
            this.Name = "FormSetting";
            this.Padding = new System.Windows.Forms.Padding(10, 60, 10, 10);
            this.Text = "系统设置";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FormSetting_FormClosing);
            this.Load += new System.EventHandler(this.FormSetting_Load);
            this.tbConfig.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.groupBox15.ResumeLayout(false);
            this.groupBox15.PerformLayout();
            this.grpJieMianFont.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.tabPage4.ResumeLayout(false);
            this.groupBox11.ResumeLayout(false);
            this.groupBox11.PerformLayout();
            this.grpToolSet.ResumeLayout(false);
            this.grpToolSet.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numToolShadowWidth)).EndInit();
            this.tabPage7.ResumeLayout(false);
            this.groupBox16.ResumeLayout(false);
            this.groupBox16.PerformLayout();
            this.groupBox17.ResumeLayout(false);
            this.groupBox17.PerformLayout();
            this.groupBox10.ResumeLayout(false);
            this.groupBox10.PerformLayout();
            this.groupBox7.ResumeLayout(false);
            this.groupBox7.PerformLayout();
            this.tabPage2.ResumeLayout(false);
            this.groupBox12.ResumeLayout(false);
            this.groupBox12.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDown1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCaptureBorderWidth)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxHistoryCount)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.tabPage5.ResumeLayout(false);
            this.groupBox14.ResumeLayout(false);
            this.groupBox14.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numShadowWidth)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numTieTuMaxWidth)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTieTuBorderWidth)).EndInit();
            this.tabPage3.ResumeLayout(false);
            this.groupBox13.ResumeLayout(false);
            this.groupBox13.PerformLayout();
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            this.tbShortKey.ResumeLayout(false);
            this.tabPage6.ResumeLayout(false);
            this.tabPage6.PerformLayout();
            this.groupBox9.ResumeLayout(false);
            this.groupBox9.PerformLayout();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numUpdateHour)).EndInit();
            this.groupBox18.ResumeLayout(false);
            this.groupBox18.PerformLayout();
            this.groupBox19.ResumeLayout(false);
            this.groupBox19.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLoadingImage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox17)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.picToolBar)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox20)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox21)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox19)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox18)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.picIcon)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tbConfig;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.CheckBox chkAutoStart;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.CheckBox chkAutoBackConfig;
        private System.Windows.Forms.TabPage tabPage4;
        private System.Windows.Forms.TabPage tbShortKey;
        private System.Windows.Forms.Button btnOpenConfigFile;
        private System.Windows.Forms.Button btnOpenConfigLocation;
        private System.Windows.Forms.CheckBox chkCaptureAnimal;
        private System.Windows.Forms.CheckBox chkStartMainWindow;
        private System.Windows.Forms.ComboBox cmbStyles;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ComboBox cmbLoadingType;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.PictureBox picLoadingImage;
        private System.Windows.Forms.NumericUpDown numCaptureBorderWidth;
        private System.Windows.Forms.CheckBox chkHexUpper;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.CheckBox chkAutoControl;
        private System.Windows.Forms.CheckBox chkAutoWindow;
        private System.Windows.Forms.CheckBox chkCrocessLine;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.CheckBox chkZoom;
        private System.Windows.Forms.ComboBox cmbHex;
        private System.Windows.Forms.NumericUpDown numMaxHistoryCount;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.CheckBox chkCaptureTip;
        private System.Windows.Forms.CheckBox chkSaveToClipborad;
        private System.Windows.Forms.Button btnChangeCaptureLocation;
        private System.Windows.Forms.Button btnOpenCaptureLocation;
        private System.Windows.Forms.TextBox txtCaptureLocation;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.TextBox txtCaptureFileName;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.CheckBox chkAutoSaveCapture;
        private System.Windows.Forms.TabPage tabPage5;
        private System.Windows.Forms.TabPage tabPage6;
        private System.Windows.Forms.GroupBox grpJieMianFont;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.NumericUpDown numTieTuBorderWidth;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.Label lblTieTuLabel;
        private System.Windows.Forms.CheckBox chkTieTuFocus;
        private System.Windows.Forms.CheckBox chkTieTuShark;
        private System.Windows.Forms.CheckBox chkTieTuUseCaptureLocation;
        private ImageButton btnTieTuShadowColor;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.CheckBox chkIngoreHtml;
        private System.Windows.Forms.NumericUpDown numTieTuMaxWidth;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.Label lblContentLable;
        private System.Windows.Forms.NumericUpDown numShadowWidth;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.CheckBox chkAutoUpdate;
        private System.Windows.Forms.PictureBox picIcon;
        private System.Windows.Forms.Label lblCopyright;
        private System.Windows.Forms.Label lblVersion;
        private System.Windows.Forms.Label lblName;
        private System.Windows.Forms.NumericUpDown numUpdateHour;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.Label label29;
        private Button btnUpgrade;
        private System.Windows.Forms.CheckBox chkUseContentColor;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label lblCaptureFileName;
        private ImageButton btnContentBackColor;
        private ImageButton btnContentFontColor;
        private System.Windows.Forms.Button btnContentFont;
        private System.Windows.Forms.Button btnContentDefault;
        private System.Windows.Forms.Button btnTieTuDefaultFont;
        private ImageButton btnTieTuBackColor;
        private System.Windows.Forms.Button btnTieTuFont;
        private ImageButton btnTieTuFontColor;
        private System.Windows.Forms.TabControl tabHotKeys;
        private System.Windows.Forms.ToolTip tipMsg;
        private System.Windows.Forms.ComboBox cmbDoubleClick;
        private System.Windows.Forms.CheckBox chkShowTool;
        private System.Windows.Forms.CheckBox chkDarkModel;
        private System.Windows.Forms.ComboBox cmbSearch;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.CheckBox chkFormTopMost;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.GroupBox grpToolSet;
        private System.Windows.Forms.PictureBox picToolBar;
        private System.Windows.Forms.Button btnDefaultToolBarPicture;
        private System.Windows.Forms.Button btnToolBarPicture;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.CheckBox chkToolBarCircle;
        private System.Windows.Forms.ComboBox cmbToolBarSize;
        private System.Windows.Forms.TextBox txtToolBarPicLocation;
        private System.Windows.Forms.Button btnQQ;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.CheckBox checkBox2;
        private System.Windows.Forms.ComboBox cmbRulerTheme;
        private System.Windows.Forms.ComboBox cmbRulerUnit;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.ComboBox cmbRulerOpacity;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.PictureBox pictureBox2;
        private System.Windows.Forms.TabPage tabPage7;
        private System.Windows.Forms.ComboBox cmbFenDuan;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.ComboBox cmbVoice;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.ComboBox cmbOcrGroup;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.GroupBox groupBox9;
        private System.Windows.Forms.PictureBox pictureBox4;
        private System.Windows.Forms.PictureBox pictureBox5;
        private LinkLabel linkLabel2;
        private LinkLabel linkLabel1;
        private System.Windows.Forms.PictureBox pictureBox6;
        private LinkLabel linkLabel3;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.PictureBox pictureBox7;
        private LinkLabel linkLabel4;
        private System.Windows.Forms.CheckBox chkToolShadow;
        private System.Windows.Forms.NumericUpDown numToolShadowWidth;
        private System.Windows.Forms.Label label32;
        private System.Windows.Forms.PictureBox pictureBox8;
        private System.Windows.Forms.Button btnCheckUpdate;
        private System.Windows.Forms.ComboBox cmbBiaoDian;
        private System.Windows.Forms.Label label34;
        private System.Windows.Forms.CheckBox chkWeatherImage;
        private System.Windows.Forms.ComboBox cmbWeatherIcon;
        private System.Windows.Forms.Label label35;
        private System.Windows.Forms.GroupBox groupBox10;
        private System.Windows.Forms.PictureBox pictureBox9;
        private System.Windows.Forms.PictureBox pictureBox10;
        private System.Windows.Forms.GroupBox groupBox11;
        private System.Windows.Forms.GroupBox groupBox12;
        private System.Windows.Forms.GroupBox groupBox13;
        private System.Windows.Forms.PictureBox pictureBox11;
        private System.Windows.Forms.Label label37;
        private System.Windows.Forms.NumericUpDown numericUpDown1;
        private System.Windows.Forms.Label label36;
        private System.Windows.Forms.GroupBox groupBox14;
        private System.Windows.Forms.GroupBox groupBox15;
        private System.Windows.Forms.GroupBox groupBox16;
        private System.Windows.Forms.PictureBox pictureBox12;
        private System.Windows.Forms.PictureBox pictureBox13;
        private System.Windows.Forms.CheckBox checkBox10;
        private System.Windows.Forms.CheckBox checkBox12;
        private System.Windows.Forms.CheckBox checkBox11;
        private System.Windows.Forms.GroupBox groupBox17;
        private System.Windows.Forms.PictureBox pictureBox1;
        private System.Windows.Forms.CheckBox checkBox8;
        private System.Windows.Forms.ComboBox cmbCopyMode;
        private System.Windows.Forms.Label label39;
        private System.Windows.Forms.PictureBox pictureBox3;
        private System.Windows.Forms.PictureBox pictureBox14;
        private System.Windows.Forms.CheckBox checkBox3;
        private System.Windows.Forms.PictureBox pictureBox15;
        private System.Windows.Forms.CheckBox chkCircleFangDa;
        private System.Windows.Forms.Label label38;
        private System.Windows.Forms.LinkLabel lnkWebSite;
        private System.Windows.Forms.PictureBox pictureBox16;
        private System.Windows.Forms.CheckBox checkBox4;
        private GroupBox groupBox2;
        private PictureBox pictureBox17;
        private Label label33;
        private GroupBox groupBox18;
        private Label label40;
        private Label label41;
        private ImageButton imageButton1;
        private GroupBox groupBox19;
        private PictureBox pictureBox18;
        private CheckBox checkBox1;
        private PictureBox pictureBox20;
        private PictureBox pictureBox21;
        private CheckBox checkBox6;
        private CheckBox checkBox7;
        private PictureBox pictureBox19;
        private CheckBox checkBox5;
    }
}