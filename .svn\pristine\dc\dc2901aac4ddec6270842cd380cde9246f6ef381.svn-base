﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class DropFileWebBroswer : WebBrowser
    {
        /// <summary>
        /// provide custom WebBrowserSite,
        /// where we override IDocHostUIHandler and call the base implementation
        /// More info: http://stackoverflow.com/a/19739699/1768303
        /// </summary>
        protected override WebBrowserSiteBase CreateWebBrowserSiteBase()
        {
            return new ImprovedWebBrowserSite(this);
        }

        #region ImprovedWebBrowserSite

        [ClassInterface(ClassInterfaceType.None)]
        protected class ImprovedWebBrowserSite :
            WebBrowserSite,
            NativeMethods.IDocHostUIHandler,
            IDisposable,
            ICustomQueryInterface
        {
            DropFileWebBroswer _host;

            NativeMethods.IDocHostUIHandler _baseIDocHostUIHandler;

            IntPtr _unkInnerAggregated;
            IntPtr _unkOuter;
            Inner _inner;

            // constructor
            public ImprovedWebBrowserSite(WebBrowser host) :
                base(host)
            {
                _host = (DropFileWebBroswer)host;

                // get the CCW object for this
                _unkOuter = Marshal.GetIUnknownForObject(this);
                Marshal.AddRef(_unkOuter);
                try
                {
                    // aggregate the CCW object with the helper Inner object
                    _inner = new Inner(this);
                    _unkInnerAggregated = Marshal.CreateAggregatedObject(_unkOuter, _inner);

                    // obtain private WebBrowserSite COM interfaces
                    _baseIDocHostUIHandler = (NativeMethods.IDocHostUIHandler)Marshal.GetTypedObjectForIUnknown(_unkInnerAggregated, typeof(NativeMethods.IDocHostUIHandler));
                }
                finally
                {
                    Marshal.Release(_unkOuter);
                }
            }

            ~ImprovedWebBrowserSite()
            {
                // need to work out the reference counting for GC to work correctly
                Debug.Print("ImprovedWebBrowserSite object finalized.");
            }

            void IDisposable.Dispose()
            {
                base.Dispose();

                _baseIDocHostUIHandler = null;

                if (_unkInnerAggregated != IntPtr.Zero)
                {
                    Marshal.Release(_unkInnerAggregated);
                    _unkInnerAggregated = IntPtr.Zero;
                }

                if (_unkOuter != IntPtr.Zero)
                {
                    Marshal.Release(_unkOuter);
                    _unkOuter = IntPtr.Zero;
                }
            }

            #region Inner
            // Inner as aggregated object
            class Inner :
                ICustomQueryInterface,
                IDisposable
            {
                object _outer;
                Type[] _interfaces;

                public Inner(object outer)
                {
                    _outer = outer;
                    _interfaces = _outer.GetType().BaseType.GetInterfaces();
                }

                public CustomQueryInterfaceResult GetInterface(ref Guid iid, out IntPtr ppv)
                {
                    if (_outer != null)
                    {
                        var guid = iid;
                        var iface = _interfaces.FirstOrDefault((t) => t.GUID == guid);
                        if (iface != null)
                        {
                            var unk = Marshal.GetComInterfaceForObject(_outer, iface, CustomQueryInterfaceMode.Ignore);
                            if (unk != IntPtr.Zero)
                            {
                                ppv = unk;
                                return CustomQueryInterfaceResult.Handled;
                            }
                        }
                    }
                    ppv = IntPtr.Zero;
                    return CustomQueryInterfaceResult.Failed;
                }

                ~Inner()
                {
                    // need to work out the reference counting for GC to work correctly
                    Debug.Print("Inner object finalized.");
                }

                public void Dispose()
                {
                    _outer = null;
                    _interfaces = null;
                }
            }
            #endregion

            #region ICustomQueryInterface
            public CustomQueryInterfaceResult GetInterface(ref Guid iid, out IntPtr ppv)
            {
                // CustomQueryInterfaceMode.Ignore is to avoid infinite loop during QI.
                if (iid == typeof(NativeMethods.IDocHostUIHandler).GUID)
                {
                    ppv = Marshal.GetComInterfaceForObject(this, typeof(NativeMethods.IDocHostUIHandler), CustomQueryInterfaceMode.Ignore);
                }
                else
                {
                    ppv = IntPtr.Zero;
                    return CustomQueryInterfaceResult.NotHandled;
                }
                return CustomQueryInterfaceResult.Handled;
            }
            #endregion

            #region IDocHostUIHandler
            int NativeMethods.IDocHostUIHandler.ShowContextMenu(int dwID, ref NativeMethods.POINT pt, IntPtr pcmdtReserved, IntPtr pdispReserved)
            {
                return _baseIDocHostUIHandler.ShowContextMenu(dwID, ref pt, pcmdtReserved, pdispReserved);
            }

            int NativeMethods.IDocHostUIHandler.GetHostInfo(ref NativeMethods.DOCHOSTUIINFO info)
            {
                Debug.Print("IDocHostUIHandler.GetHostInfo");
                return _baseIDocHostUIHandler.GetHostInfo(ref info);
            }

            int NativeMethods.IDocHostUIHandler.ShowUI(int dwID, IntPtr activeObject, IntPtr commandTarget, IntPtr frame, IntPtr doc)
            {
                return _baseIDocHostUIHandler.ShowUI(dwID, activeObject, commandTarget, frame, doc);
            }

            int NativeMethods.IDocHostUIHandler.HideUI()
            {
                return _baseIDocHostUIHandler.HideUI();
            }

            int NativeMethods.IDocHostUIHandler.UpdateUI()
            {
                return _baseIDocHostUIHandler.UpdateUI();
            }

            int NativeMethods.IDocHostUIHandler.EnableModeless(bool fEnable)
            {
                return _baseIDocHostUIHandler.EnableModeless(fEnable);
            }

            int NativeMethods.IDocHostUIHandler.OnDocWindowActivate(bool fActivate)
            {
                return _baseIDocHostUIHandler.OnDocWindowActivate(fActivate);
            }

            int NativeMethods.IDocHostUIHandler.OnFrameWindowActivate(bool fActivate)
            {
                return _baseIDocHostUIHandler.OnFrameWindowActivate(fActivate);
            }

            int NativeMethods.IDocHostUIHandler.ResizeBorder(ref NativeMethods.COMRECT rect, IntPtr doc, bool fFrameWindow)
            {
                return _baseIDocHostUIHandler.ResizeBorder(ref rect, doc, fFrameWindow);
            }

            int NativeMethods.IDocHostUIHandler.TranslateAccelerator(ref NativeMethods.MSG msg, ref Guid group, int nCmdID)
            {
                return _baseIDocHostUIHandler.TranslateAccelerator(ref msg, ref group, nCmdID);
            }

            int NativeMethods.IDocHostUIHandler.GetOptionKeyPath(string[] pbstrKey, int dw)
            {
                return _baseIDocHostUIHandler.GetOptionKeyPath(pbstrKey, dw);
            }

            int NativeMethods.IDocHostUIHandler.GetDropTarget(IntPtr pDropTarget, out IntPtr ppDropTarget)
            {
                Debug.Print("IDocHostUIHandler.GetDropTarget");
                return _baseIDocHostUIHandler.GetDropTarget(pDropTarget, out ppDropTarget);
            }

            int NativeMethods.IDocHostUIHandler.GetExternal(out object ppDispatch)
            {
                return _baseIDocHostUIHandler.GetExternal(out ppDispatch);
            }

            int NativeMethods.IDocHostUIHandler.TranslateUrl(int dwTranslate, string strURLIn, out string pstrURLOut)
            {
                return _baseIDocHostUIHandler.TranslateUrl(dwTranslate, strURLIn, out pstrURLOut);
            }

            int NativeMethods.IDocHostUIHandler.FilterDataObject(IntPtr pDO, out IntPtr ppDORet)
            {
                return _baseIDocHostUIHandler.FilterDataObject(pDO, out ppDORet);
            }
            #endregion
        }
        #endregion

    }
}
