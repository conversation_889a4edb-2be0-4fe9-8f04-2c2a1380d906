﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace OCRTools
{
    public class CommonEncryptHelper
    {
        private static readonly byte[] m_btIV = {0x12, 0x34, 0x56, 0x78, 0x90, 0xAB, 0xCD, 0xEF};

        #region DES 加密解密

        /// <summary>
        ///     DES 加密(数据加密标准，速度较快，适用于加密大量数据的场合)
        /// </summary>
        /// <param name="EncryptString">待加密的密文</param>
        /// <param name="EncryptKey">加密的密钥</param>
        /// <returns>returns</returns>
        public static string DESEncrypt(string EncryptString, string EncryptKey)
        {
            var m_strEncrypt = "";
            if (string.IsNullOrEmpty(EncryptString) || string.IsNullOrEmpty(EncryptKey) || EncryptKey.Length != 8)
                return m_strEncrypt;
            //if (string.IsNullOrEmpty(EncryptString)) { throw (new Exception("密文不得为空")); }
            //if (string.IsNullOrEmpty(EncryptKey)) { throw (new Exception("密钥不得为空")); }
            //if (EncryptKey.Length != 8) { throw (new Exception("密钥必须为8位")); }
            var m_DESProvider = new DESCryptoServiceProvider();
            try
            {
                var m_btEncryptString = Encoding.Default.GetBytes(EncryptString);
                var m_stream = new MemoryStream();
                var m_cstream = new CryptoStream(m_stream,
                    m_DESProvider.CreateEncryptor(Encoding.Default.GetBytes(EncryptKey), m_btIV),
                    CryptoStreamMode.Write);
                m_cstream.Write(m_btEncryptString, 0, m_btEncryptString.Length);
                m_cstream.FlushFinalBlock();
                m_strEncrypt = Convert.ToBase64String(m_stream.ToArray());
                m_stream.Close();
                m_stream.Dispose();
                m_cstream.Close();
                m_cstream.Dispose();
            }
            //catch (IOException ex) { throw ex; }
            //catch (CryptographicException ex) { throw ex; }
            //catch (ArgumentException ex) { throw ex; }
            catch (Exception)
            {
                //Log.WriteError(oe);
            }
            finally
            {
                m_DESProvider.Clear();
            }

            return m_strEncrypt;
        }

        /// <summary>
        ///     DES 解密(数据加密标准，速度较快，适用于加密大量数据的场合)
        /// </summary>
        /// <param name="DecryptString">待解密的密文</param>
        /// <param name="DecryptKey">解密的密钥</param>
        /// <returns>returns</returns>
        public static string DESDecrypt(string DecryptString, string DecryptKey)
        {
            var m_strDecrypt = "";
            if (string.IsNullOrEmpty(DecryptString) || string.IsNullOrEmpty(DecryptKey) || DecryptKey.Length != 8)
                return m_strDecrypt;
            //if (string.IsNullOrEmpty(EncryptString)) { throw (new Exception("密文不得为空")); }
            //if (string.IsNullOrEmpty(EncryptKey)) { throw (new Exception("密钥不得为空")); }
            //if (EncryptKey.Length != 8) { throw (new Exception("密钥必须为8位")); }
            var m_DESProvider = new DESCryptoServiceProvider();
            try
            {
                var m_btDecryptString = Convert.FromBase64String(DecryptString);
                var m_stream = new MemoryStream();
                var m_cstream = new CryptoStream(m_stream,
                    m_DESProvider.CreateDecryptor(Encoding.Default.GetBytes(DecryptKey), m_btIV),
                    CryptoStreamMode.Write);
                m_cstream.Write(m_btDecryptString, 0, m_btDecryptString.Length);
                m_cstream.FlushFinalBlock();
                m_strDecrypt = Encoding.Default.GetString(m_stream.ToArray());
                m_stream.Close();
                m_stream.Dispose();
                m_cstream.Close();
                m_cstream.Dispose();
            }
            //catch (IOException ex) { throw ex; }
            //catch (CryptographicException ex) { throw ex; }
            //catch (ArgumentException ex) { throw ex; }
            catch (Exception)
            {
                //Log.WriteError(oe);
            }
            finally
            {
                m_DESProvider.Clear();
            }

            return m_strDecrypt;
        }

        #endregion
    }
}