﻿namespace OCRTools
{
    partial class FormUpdate
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.bgUpdate = new System.ComponentModel.BackgroundWorker();
            this.lblNowVersion = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.lblNew = new System.Windows.Forms.Label();
            this.lblDate = new System.Windows.Forms.Label();
            this.lblNewDate = new System.Windows.Forms.Label();
            this.rtbCon = new System.Windows.Forms.RichTextBox();
            this.lblDown = new System.Windows.Forms.Label();
            this.tmrTick = new System.Windows.Forms.Timer(this.components);
            this.lblLoading = new System.Windows.Forms.Label();
            this.proProcess = new System.Windows.Forms.ProgressBar();
            this.lblProcess = new System.Windows.Forms.Label();
            this.lnkNoUpdate = new System.Windows.Forms.LinkLabel();
            this.btnOK = new MetroFramework.Controls.MetroButton();
            this.SuspendLayout();
            // 
            // bgUpdate
            // 
            this.bgUpdate.WorkerReportsProgress = true;
            this.bgUpdate.WorkerSupportsCancellation = true;
            this.bgUpdate.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bgUpdate_DoWork);
            this.bgUpdate.RunWorkerCompleted += new System.ComponentModel.RunWorkerCompletedEventHandler(this.bgUpdate_RunWorkerCompleted);
            // 
            // lblNowVersion
            // 
            this.lblNowVersion.AutoSize = true;
            this.lblNowVersion.BackColor = System.Drawing.Color.Transparent;
            this.lblNowVersion.Font = new System.Drawing.Font("宋体", 11F);
            this.lblNowVersion.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.lblNowVersion.Location = new System.Drawing.Point(62, 75);
            this.lblNowVersion.Name = "lblNowVersion";
            this.lblNowVersion.Size = new System.Drawing.Size(82, 15);
            this.lblNowVersion.TabIndex = 3;
            this.lblNowVersion.Text = "最新版本：";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.BackColor = System.Drawing.Color.Transparent;
            this.label3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.label3.Location = new System.Drawing.Point(42, 124);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 4;
            this.label3.Text = "更新内容：";
            // 
            // lblNew
            // 
            this.lblNew.AutoSize = true;
            this.lblNew.BackColor = System.Drawing.Color.Transparent;
            this.lblNew.Font = new System.Drawing.Font("宋体", 11F);
            this.lblNew.ForeColor = System.Drawing.Color.Red;
            this.lblNew.Location = new System.Drawing.Point(137, 75);
            this.lblNew.Name = "lblNew";
            this.lblNew.Size = new System.Drawing.Size(0, 15);
            this.lblNew.TabIndex = 6;
            // 
            // lblDate
            // 
            this.lblDate.AutoSize = true;
            this.lblDate.BackColor = System.Drawing.Color.Transparent;
            this.lblDate.Font = new System.Drawing.Font("宋体", 11F);
            this.lblDate.ForeColor = System.Drawing.Color.Red;
            this.lblDate.Location = new System.Drawing.Point(137, 96);
            this.lblDate.Name = "lblDate";
            this.lblDate.Size = new System.Drawing.Size(0, 15);
            this.lblDate.TabIndex = 9;
            // 
            // lblNewDate
            // 
            this.lblNewDate.AutoSize = true;
            this.lblNewDate.BackColor = System.Drawing.Color.Transparent;
            this.lblNewDate.Font = new System.Drawing.Font("宋体", 11F);
            this.lblNewDate.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.lblNewDate.Location = new System.Drawing.Point(62, 96);
            this.lblNewDate.Name = "lblNewDate";
            this.lblNewDate.Size = new System.Drawing.Size(82, 15);
            this.lblNewDate.TabIndex = 8;
            this.lblNewDate.Text = "更新日期：";
            // 
            // rtbCon
            // 
            this.rtbCon.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.rtbCon.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.rtbCon.Location = new System.Drawing.Point(45, 144);
            this.rtbCon.Name = "rtbCon";
            this.rtbCon.ReadOnly = true;
            this.rtbCon.Size = new System.Drawing.Size(210, 96);
            this.rtbCon.TabIndex = 10;
            this.rtbCon.TabStop = false;
            this.rtbCon.Text = "";
            // 
            // lblDown
            // 
            this.lblDown.BackColor = System.Drawing.Color.Transparent;
            this.lblDown.Font = new System.Drawing.Font("宋体", 18F);
            this.lblDown.ForeColor = System.Drawing.Color.Red;
            this.lblDown.Location = new System.Drawing.Point(33, 103);
            this.lblDown.Name = "lblDown";
            this.lblDown.Size = new System.Drawing.Size(235, 51);
            this.lblDown.TabIndex = 11;
            this.lblDown.Text = "更新文件下载中…";
            this.lblDown.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.lblDown.Visible = false;
            // 
            // tmrTick
            // 
            this.tmrTick.Interval = 600;
            this.tmrTick.Tick += new System.EventHandler(this.tmrTick_Tick);
            // 
            // lblLoading
            // 
            this.lblLoading.AutoSize = true;
            this.lblLoading.BackColor = System.Drawing.Color.Transparent;
            this.lblLoading.Font = new System.Drawing.Font("宋体", 13F);
            this.lblLoading.ForeColor = System.Drawing.Color.Blue;
            this.lblLoading.Location = new System.Drawing.Point(103, 82);
            this.lblLoading.Name = "lblLoading";
            this.lblLoading.Size = new System.Drawing.Size(98, 18);
            this.lblLoading.TabIndex = 12;
            this.lblLoading.Text = "loading...";
            this.lblLoading.Visible = false;
            // 
            // proProcess
            // 
            this.proProcess.BackColor = System.Drawing.SystemColors.ActiveCaptionText;
            this.proProcess.Location = new System.Drawing.Point(44, 159);
            this.proProcess.Name = "proProcess";
            this.proProcess.Size = new System.Drawing.Size(215, 29);
            this.proProcess.TabIndex = 13;
            this.proProcess.Visible = false;
            // 
            // lblProcess
            // 
            this.lblProcess.AutoSize = true;
            this.lblProcess.Font = new System.Drawing.Font("宋体", 11F);
            this.lblProcess.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.lblProcess.Location = new System.Drawing.Point(105, 205);
            this.lblProcess.Name = "lblProcess";
            this.lblProcess.Size = new System.Drawing.Size(68, 15);
            this.lblProcess.TabIndex = 14;
            this.lblProcess.Text = "已下载0%";
            this.lblProcess.Visible = false;
            // 
            // lnkNoUpdate
            // 
            this.lnkNoUpdate.AutoSize = true;
            this.lnkNoUpdate.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lnkNoUpdate.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline;
            this.lnkNoUpdate.LinkColor = System.Drawing.Color.Red;
            this.lnkNoUpdate.Location = new System.Drawing.Point(188, 255);
            this.lnkNoUpdate.Name = "lnkNoUpdate";
            this.lnkNoUpdate.Size = new System.Drawing.Size(65, 24);
            this.lnkNoUpdate.TabIndex = 15;
            this.lnkNoUpdate.TabStop = true;
            this.lnkNoUpdate.Text = "升级失败？\r\n手动升级！";
            this.lnkNoUpdate.Visible = true;
            this.lnkNoUpdate.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lnkNoUpdate_LinkClicked);
            // 
            // btnOK
            // 
            this.btnOK.Enabled = false;
            this.btnOK.Location = new System.Drawing.Point(94, 251);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(91, 31);
            this.btnOK.Style = MetroFramework.MetroColorStyle.黑色;
            this.btnOK.TabIndex = 16;
            this.btnOK.Tag = "down";
            this.btnOK.Text = "立即更新(&O)";
            this.btnOK.UseSelectable = true;
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // FormUpdate
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(294, 302);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.lnkNoUpdate);
            this.Controls.Add(this.lblDown);
            this.Controls.Add(this.proProcess);
            this.Controls.Add(this.lblProcess);
            this.Controls.Add(this.lblLoading);
            this.Controls.Add(this.rtbCon);
            this.Controls.Add(this.lblDate);
            this.Controls.Add(this.lblNew);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.lblNewDate);
            this.Controls.Add(this.lblNowVersion);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FormUpdate";
            this.Resizable = false;
            this.ShowInTaskbar = false;
            this.Text = "发现新版本！";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FormUpdate_FormClosing);
            this.Load += new System.EventHandler(this.FormUpdate_Load);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        
        private System.ComponentModel.BackgroundWorker bgUpdate;
        private System.Windows.Forms.Label lblNowVersion;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label lblNew;
        private System.Windows.Forms.Label lblDate;
        private System.Windows.Forms.Label lblNewDate;
        private System.Windows.Forms.RichTextBox rtbCon;
        private System.Windows.Forms.Label lblDown;
        private System.Windows.Forms.Timer tmrTick;
        private System.Windows.Forms.Label lblLoading;
        private System.Windows.Forms.ProgressBar proProcess;
        private System.Windows.Forms.Label lblProcess;
        private System.Windows.Forms.LinkLabel lnkNoUpdate;
        private MetroFramework.Controls.MetroButton btnOK;
    }
}