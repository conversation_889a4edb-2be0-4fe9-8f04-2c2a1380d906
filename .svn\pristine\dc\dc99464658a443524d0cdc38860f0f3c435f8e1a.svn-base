﻿using OCRTools.Common;
using OCRTools.Language;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FormViewImageWX : BaseForm
    {
        private PictureBox pictureBox;
        private Graphics graphics;
        private Rectangle selectedRectangle;
        private List<TextCellInfo> textRegions;

        public FormViewImageWX()
        {
            Icon = FrmMain.FrmTool.Icon;
            Text = $"{"图像预览".CurrentText()}-{CommonString.FullName.CurrentText()}";
            ShowIcon = true;
            ShowInTaskbar = true;

            // 创建 PictureBox 以显示图像
            pictureBox = new PictureBox();
            pictureBox.Dock = DockStyle.Fill;
            this.Controls.Add(pictureBox);

            // 创建 Graphics 对象以绘制选定的文本块
            graphics = pictureBox.CreateGraphics();

            // 响应鼠标事件以更新选定的文本块
            pictureBox.MouseDown += new MouseEventHandler(pictureBox_MouseDown);
            pictureBox.MouseMove += new MouseEventHandler(pictureBox_MouseMove);
            pictureBox.MouseUp += new MouseEventHandler(pictureBox_MouseUp);
        }

        internal void Bind(Image image, List<TextCellInfo> regions)
        {
            textRegions = regions;
            Text = $"{"图像预览".CurrentText()} {"尺寸".CurrentText()}:{image?.Width}×{image?.Height} - {CommonString.FullName.CurrentText()}";
            pictureBox.Image = image;
        }

        private void pictureBox_MouseDown(object sender, MouseEventArgs e)
        {
            // 开始选择文本块
            selectedRectangle = new Rectangle(e.X, e.Y, 0, 0);
        }

        private void pictureBox_MouseMove(object sender, MouseEventArgs e)
        {
            // 更新选定的文本块
            if (!selectedRectangle.IsEmpty)
            {
                selectedRectangle.Width = e.X - selectedRectangle.X;
                selectedRectangle.Height = e.Y - selectedRectangle.Y;

                // 获取选定的文本块
                var selectedTextRegions = new List<TextCellInfo>();
                foreach (var region in textRegions)
                {
                    if (selectedRectangle.IntersectsWith(new Rectangle() { X = (int)region.location.left, Y = (int)region.location.top, Width = (int)region.location.width, Height = (int)region.location.height }))
                    {
                        selectedTextRegions.Add(region);
                    }
                }

                // 绘制新的选定区域
                foreach (var region in selectedTextRegions)
                {
                    graphics.DrawRectangle(Pens.Red, new Rectangle() { X = (int)region.location.left, Y = (int)region.location.top, Width = (int)region.location.width, Height = (int)region.location.height });
                }
            }
        }

        private void pictureBox_MouseUp(object sender, MouseEventArgs e)
        {
            // 获取选定的文本区域
            var selectedTextRegions = textRegions.Where(region => selectedRectangle.IntersectsWith(new Rectangle() { X = (int)region.location.left, Y = (int)region.location.top, Width = (int)region.location.width, Height = (int)region.location.height })).ToList();

            // 获取选定的文本
            var selectedText = string.Join(" ", selectedTextRegions.Select(region => region.words));

            // 在控制台中输出选定的文本
            Console.WriteLine(selectedText);
            // 结束选择文本块
            selectedRectangle = Rectangle.Empty;
            pictureBox.Image = pictureBox.Image;
        }
    }
}
