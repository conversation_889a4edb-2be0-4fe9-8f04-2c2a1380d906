﻿using MetroFramework.Forms;
using OCRTools.Language;
using OCRTools.Properties;
using System;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Net;
using System.Threading;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FormUpdate : MetroForm
    {
        private readonly string _tmpCacheFile = Path.GetTempFileName();
        private bool _isCancel;

        private bool _isOpenDownLoad;

        public string AppName { get; set; }

        public string AppPath { get; set; }

        public bool IsUpdateMode { get; set; } = true;

        public bool IsNeedUnZip { get; set; }

        public bool IsCanUserUpdate { get; set; } = true;

        public string StrUpdateMode => IsUpdateMode ? "更新" : "安装";

        internal UpdateEntity UpdateInfo { get; set; }

        public bool IsAutoStart { get; set; }

        public bool IsNeedClearFolder { get; set; }

        public bool IsHasUpdate { get; set; }

        public bool IsBeta { get; set; }

        public FormUpdate()
        {
            InitializeComponent();
            SetStyle(ControlStyles.OptimizedDoubleBuffer |
                     ControlStyles.UserPaint |
                     ControlStyles.AllPaintingInWmPaint, true);
            ShadowType = CommonString.CommonShadowType;
            //不执行线程检查
            CheckForIllegalCrossThreadCalls = false;

            CommonMethod.SetStyle(lnkNoUpdate, ControlStyles.Selectable, false);

            AddCustomButton("官网", Resources.网络检测, 15F, (send, e) =>
            {
                CommonMethod.OpenUrl(CommonString.StrServerHostUrl);
            });
        }

        public override void OnThemeChange()
        {
            var foreColor = CommonSetting.Get默认文字颜色();
            lblNew.ForeColor = foreColor;
            lblDate.ForeColor = foreColor;
            rtbCon.ForeColor = foreColor;
            lblNowVersion.ForeColor = CommonSetting.夜间模式 ? CommonTheme.ReveseColor(Color.DimGray) : Color.DimGray;
            lblNewDate.ForeColor = Color.DimGray;
        }

        private void FormUpdate_Load(object sender, EventArgs e)
        {
            Text = AppName.CurrentText() + (IsBeta ? " Beta" : "") + "-" + (IsHasUpdate ? StrUpdateMode.CurrentText() : "最近更新内容".CurrentText());
            if (UpdateInfo == null) return;
            btnOK.Enabled = true;
            btnOK.Text = "立即" + StrUpdateMode;
            lblNew.Text = UpdateInfo.strNowVersion;
            lblDate.Text = UpdateInfo.dtNowDate.ToDateStr("yyyy-MM-dd");
            rtbCon.Text = UpdateInfo.strContext.CurrentText(true);
            rtbCon.Font = CommonMethod.ScaleLabelByHeight(rtbCon.Text, rtbCon.Font, new Size((int)(rtbCon.Size.Width * CommonTheme.DpiScale), (int)(rtbCon.Size.Height * CommonTheme.DpiScale)), 20);
            Opacity = 1;
            if (!IsHasUpdate)
            {
                btnOK.Left -= 90;
                btnOK.Width += 40;
                btnOK.Text = "好的，已了解！";
                btnUpdateLater.Visible = false;
                lnkNoUpdate.Visible = false;
            }
            else
            {
                lnkNoUpdate.Visible = IsCanUserUpdate;
                if (UpdateInfo.IsNowForce)
                {
                    Text = AppName.CurrentText() + "【强制更新】".CurrentText();
                }
                if (IsAutoStart || UpdateInfo.IsNowForce)
                {
                    btnOK_Click(sender, null);
                }
            }
        }

        private void bgUpdate_DoWork(object sender, DoWorkEventArgs e)
        {
            if (UpdateInfo == null) return;
            try
            {
                _isOpenDownLoad = false;
                try
                {
                    using (new FileStream(_tmpCacheFile, FileMode.Create))
                    {
                    }
                }
                catch
                {
                    _isOpenDownLoad = true;
                }

                if (_isOpenDownLoad)
                    proProcess.Value = proProcess.Maximum;
                else
                    DownloadFile(UpdateInfo.strURL, _tmpCacheFile, proProcess, lblProcess);
                if (proProcess.Value == proProcess.Maximum)
                {
                    lblProcess.Text = "下载完成".CurrentText() + "，" + ("开始" + StrUpdateMode).CurrentText() + "！";
                    btnOK.Tag = "open";
                }
                else
                {
                    pnlUpdate.Visible = false;
                    btnOK.Enabled = true;
                    btnOK.Tag = "down";
                    CommonMethod.ShowHelpMsg("更新失败".CurrentText() + "，" + CommonString.StrRetry.CurrentText());
                }

                bgUpdate.CancelAsync();
            }
            catch { }
        }

        private void BeforeUpdate()
        {
            try
            {
                LocalOcrService.CloseService();
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (!IsHasUpdate)
            {
                Close();
                return;
            }
            if (_isCancel)
                return;
            var updateFileName = CommonUpdate.GetServerFile(CommonString.LocalUpdateExePath, CommonString.UpdateExeFileUrl + "?t=" + ServerTime.DateTime.Millisecond);
            if (string.IsNullOrEmpty(updateFileName))
            {
                CommonMethod.ShowHelpMsg("更新失败".CurrentText() + "，" + "请检查是否被杀软拦截，或以管理员身份运行重试！".CurrentText());
                return;
            }
            var tagNow = btnOK.Tag.ToString();
            if (tagNow == "down")
            {
                btnOK.Enabled = false;
                lblProcess.Visible = true;
                proProcess.Visible = true;
                pnlUpdate.Visible = true;
                pnlUpdate.BringToFront();
                lnkNoUpdate.BringToFront();
                bgUpdate.RunWorkerAsync();
            }
            else if (tagNow == "open")
            {
                btnOK.Enabled = false;
                BeforeUpdate();

                if (IsNeedUnZip)
                {
                    if (IsNeedClearFolder && Directory.Exists(AppPath))
                    {
                        Microsoft.VisualBasic.FileIO.FileSystem.DeleteDirectory(AppPath, Microsoft.VisualBasic.FileIO.DeleteDirectoryOption.DeleteAllContents);
                    }
                    DoUnZip();
                    CommonMethod.ShowHelpMsg(AppName + " " + "已安装成功！".CurrentText());
                    Close();
                }
                else
                {
                    var paramStr = string.Format("\"{0}\" \"{1}\" \"{2}\""
                        , AppPath
                        , _tmpCacheFile
                        , _isOpenDownLoad ? UpdateInfo.strURL : string.Empty
                    );
                    try
                    {
                        var path = Path.GetDirectoryName(AppPath);
                        if (!Directory.Exists(path))
                        {
                            Directory.CreateDirectory(path);
                        }
                    }
                    catch (Exception exception)
                    {
                        Console.WriteLine(exception);
                    }
                    try
                    {
                        CommonString.RunAsAdmin(updateFileName, paramStr, true, true);
                    }
                    catch
                    {
                        CommonString.RunAsAdmin(updateFileName, paramStr, false, true);
                    }
                    finally
                    {
                        if (IsUpdateMode)
                            CommonMethod.Exit();
                        else
                            Close();
                    }
                }
            }
        }

        private void DoUnZip()
        {
            lblProcess.Text = "开始解压缩".CurrentText() + "," + CommonString.StrPleaseWait.CurrentText();
            GZip.GZip.Decompress(Path.GetDirectoryName(_tmpCacheFile), AppPath, Path.GetFileName(_tmpCacheFile));
            CommonMethod.ShowHelpMsg(AppName + " " + "已安装成功！".CurrentText());
        }

        public void DownloadFile(string url, string filename, ProgressBar prog, Label label1)
        {
            try
            {
                var client = new WebClient { Proxy = WebRequest.DefaultWebProxy };
                client.DownloadProgressChanged += (sender, e) =>
                {
                    if (!IsDisposed)
                    {
                        if (prog != null)
                            prog.Value = e.ProgressPercentage;
                        if (label1 != null)
                            label1.Text = CommonMethod.FormatBytes(e.TotalBytesToReceive) + ","
                            + "已下载".CurrentText() + " " + e.ProgressPercentage + "%";
                        Application.DoEvents();
                    }
                };
                if (url.Contains("?"))
                {
                    url += "&t=" + ServerTime.DateTime.Millisecond;
                }
                else
                {
                    url += "?t=" + ServerTime.DateTime.Millisecond;
                }
                client.DownloadFileAsync(new Uri(url), filename);
                while (client.IsBusy) Thread.Sleep(1000);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
        }

        private void FormUpdate_FormClosing(object sender, FormClosingEventArgs e)
        {
            _isCancel = true;
            bgUpdate.CancelAsync();
            if (UpdateInfo?.IsNowForce == true)
                CommonMethod.Exit();
        }

        private void lnkNoUpdate_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            var url = string.IsNullOrEmpty(UpdateInfo.strFullURL) ? UpdateInfo.strURL : UpdateInfo.strFullURL;
            if (!string.IsNullOrEmpty(url))
            {
                url += (url.Contains("?") ? "&" : "?") + "t=" + ServerTime.DateTime.Ticks;
                try
                {
                    ClipboardService.SetText(url);
                }
                catch
                {
                }

                MessageBox.Show(this, "已复制下载地址到粘贴板！\n助手将尝试自动用默认浏览器打开网址…\n如果一直没有弹出下载框，请手动粘贴网址到浏览器重试！".CurrentText(), "手动更新".CurrentText(),
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                CommonMethod.OpenUrl(url);
            }
        }

        private void bgUpdate_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            if (!btnOK.Enabled)
            {
                proProcess.Value = 100;
                Application.DoEvents();
                btnOK_Click(sender, null);
            }
        }

        private void btnUpdateLater_Click(object sender, EventArgs e)
        {
            CommonUpdate.IsAutoCheckUpdate = false;
            Close();
        }
    }
}