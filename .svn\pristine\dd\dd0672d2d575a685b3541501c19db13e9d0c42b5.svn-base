﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtInput.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="txtInput.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 37</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtInput.Multiline" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtInput.ScrollBars" type="System.Windows.Forms.ScrollBars, System.Windows.Forms">
    <value>Vertical</value>
  </data>
  <data name="txtInput.Size" type="System.Drawing.Size, System.Drawing">
    <value>518, 260</value>
  </data>
  <data name="txtInput.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtInput.Name" xml:space="preserve">
    <value>txtInput</value>
  </data>
  <data name="&gt;&gt;txtInput.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtInput.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtInput.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="flpProperties.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="lblFont.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="lblFont.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblFont.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblFont.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 8</value>
  </data>
  <data name="lblFont.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>0, 0, 0, 0</value>
  </data>
  <data name="lblFont.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 12</value>
  </data>
  <data name="lblFont.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="lblFont.Text" xml:space="preserve">
    <value>字体：</value>
  </data>
  <data name="&gt;&gt;lblFont.Name" xml:space="preserve">
    <value>lblFont</value>
  </data>
  <data name="&gt;&gt;lblFont.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblFont.Parent" xml:space="preserve">
    <value>flpProperties</value>
  </data>
  <data name="&gt;&gt;lblFont.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="cbFonts.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="cbFonts.Location" type="System.Drawing.Point, System.Drawing">
    <value>44, 4</value>
  </data>
  <data name="cbFonts.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 20</value>
  </data>
  <data name="cbFonts.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;cbFonts.Name" xml:space="preserve">
    <value>cbFonts</value>
  </data>
  <data name="&gt;&gt;cbFonts.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbFonts.Parent" xml:space="preserve">
    <value>flpProperties</value>
  </data>
  <data name="&gt;&gt;cbFonts.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="lblTextSize.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="lblTextSize.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblTextSize.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblTextSize.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 8</value>
  </data>
  <data name="lblTextSize.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 0, 0, 0</value>
  </data>
  <data name="lblTextSize.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 12</value>
  </data>
  <data name="lblTextSize.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="lblTextSize.Text" xml:space="preserve">
    <value>字号：</value>
  </data>
  <data name="&gt;&gt;lblTextSize.Name" xml:space="preserve">
    <value>lblTextSize</value>
  </data>
  <data name="&gt;&gt;lblTextSize.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblTextSize.Parent" xml:space="preserve">
    <value>flpProperties</value>
  </data>
  <data name="&gt;&gt;lblTextSize.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="nudTextSize.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="nudTextSize.Location" type="System.Drawing.Point, System.Drawing">
    <value>252, 3</value>
  </data>
  <data name="nudTextSize.Size" type="System.Drawing.Size, System.Drawing">
    <value>49, 21</value>
  </data>
  <data name="nudTextSize.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="nudTextSize.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;nudTextSize.Name" xml:space="preserve">
    <value>nudTextSize</value>
  </data>
  <data name="&gt;&gt;nudTextSize.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nudTextSize.Parent" xml:space="preserve">
    <value>flpProperties</value>
  </data>
  <data name="&gt;&gt;nudTextSize.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="btnTextColor.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="btnTextColor.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnTextColor.Location" type="System.Drawing.Point, System.Drawing">
    <value>307, 3</value>
  </data>
  <data name="btnTextColor.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 22</value>
  </data>
  <data name="btnTextColor.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btnTextColor.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <metadata name="ttTextInput.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>493, 17</value>
  </metadata>
  <data name="btnTextColor.ToolTip" xml:space="preserve">
    <value>文本颜色</value>
  </data>
  <data name="&gt;&gt;btnTextColor.Name" xml:space="preserve">
    <value>btnTextColor</value>
  </data>
  <data name="&gt;&gt;btnTextColor.Type" xml:space="preserve">
    <value>OCRTools.ColorButton, OCR助手, Version=*******, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;btnTextColor.Parent" xml:space="preserve">
    <value>flpProperties</value>
  </data>
  <data name="&gt;&gt;btnTextColor.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="btnGradient.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnGradient.Location" type="System.Drawing.Point, System.Drawing">
    <value>337, 3</value>
  </data>
  <data name="btnGradient.Size" type="System.Drawing.Size, System.Drawing">
    <value>26, 22</value>
  </data>
  <data name="btnGradient.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="btnGradient.ToolTip" xml:space="preserve">
    <value>渐变</value>
  </data>
  <data name="&gt;&gt;btnGradient.Name" xml:space="preserve">
    <value>btnGradient</value>
  </data>
  <data name="&gt;&gt;btnGradient.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnGradient.Parent" xml:space="preserve">
    <value>flpProperties</value>
  </data>
  <data name="&gt;&gt;btnGradient.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="cbBold.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="cbBold.Appearance" type="System.Windows.Forms.Appearance, System.Windows.Forms">
    <value>Button</value>
  </data>
  <data name="cbBold.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="cbBold.Location" type="System.Drawing.Point, System.Drawing">
    <value>369, 3</value>
  </data>
  <data name="cbBold.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 22</value>
  </data>
  <data name="cbBold.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="cbBold.ToolTip" xml:space="preserve">
    <value>粗体</value>
  </data>
  <data name="&gt;&gt;cbBold.Name" xml:space="preserve">
    <value>cbBold</value>
  </data>
  <data name="&gt;&gt;cbBold.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbBold.Parent" xml:space="preserve">
    <value>flpProperties</value>
  </data>
  <data name="&gt;&gt;cbBold.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="cbItalic.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="cbItalic.Appearance" type="System.Windows.Forms.Appearance, System.Windows.Forms">
    <value>Button</value>
  </data>
  <data name="cbItalic.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="cbItalic.Location" type="System.Drawing.Point, System.Drawing">
    <value>399, 3</value>
  </data>
  <data name="cbItalic.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 22</value>
  </data>
  <data name="cbItalic.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="cbItalic.ToolTip" xml:space="preserve">
    <value>斜体</value>
  </data>
  <data name="&gt;&gt;cbItalic.Name" xml:space="preserve">
    <value>cbItalic</value>
  </data>
  <data name="&gt;&gt;cbItalic.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbItalic.Parent" xml:space="preserve">
    <value>flpProperties</value>
  </data>
  <data name="&gt;&gt;cbItalic.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="cbUnderline.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="cbUnderline.Appearance" type="System.Windows.Forms.Appearance, System.Windows.Forms">
    <value>Button</value>
  </data>
  <data name="cbUnderline.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="cbUnderline.Location" type="System.Drawing.Point, System.Drawing">
    <value>429, 3</value>
  </data>
  <data name="cbUnderline.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 22</value>
  </data>
  <data name="cbUnderline.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="cbUnderline.ToolTip" xml:space="preserve">
    <value>下划线</value>
  </data>
  <data name="&gt;&gt;cbUnderline.Name" xml:space="preserve">
    <value>cbUnderline</value>
  </data>
  <data name="&gt;&gt;cbUnderline.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbUnderline.Parent" xml:space="preserve">
    <value>flpProperties</value>
  </data>
  <data name="&gt;&gt;cbUnderline.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="btnAlignmentHorizontal.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="btnAlignmentHorizontal.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnAlignmentHorizontal.Location" type="System.Drawing.Point, System.Drawing">
    <value>459, 3</value>
  </data>
  <data name="btnAlignmentHorizontal.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 22</value>
  </data>
  <data name="btnAlignmentHorizontal.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="btnAlignmentHorizontal.ToolTip" xml:space="preserve">
    <value>水平对齐</value>
  </data>
  <data name="&gt;&gt;btnAlignmentHorizontal.Name" xml:space="preserve">
    <value>btnAlignmentHorizontal</value>
  </data>
  <data name="&gt;&gt;btnAlignmentHorizontal.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnAlignmentHorizontal.Parent" xml:space="preserve">
    <value>flpProperties</value>
  </data>
  <data name="&gt;&gt;btnAlignmentHorizontal.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="btnAlignmentVertical.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="btnAlignmentVertical.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnAlignmentVertical.Location" type="System.Drawing.Point, System.Drawing">
    <value>489, 3</value>
  </data>
  <data name="btnAlignmentVertical.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 22</value>
  </data>
  <data name="btnAlignmentVertical.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="btnAlignmentVertical.ToolTip" xml:space="preserve">
    <value>垂直对齐</value>
  </data>
  <data name="&gt;&gt;btnAlignmentVertical.Name" xml:space="preserve">
    <value>btnAlignmentVertical</value>
  </data>
  <data name="&gt;&gt;btnAlignmentVertical.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnAlignmentVertical.Parent" xml:space="preserve">
    <value>flpProperties</value>
  </data>
  <data name="&gt;&gt;btnAlignmentVertical.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="flpProperties.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 5</value>
  </data>
  <data name="flpProperties.Size" type="System.Drawing.Size, System.Drawing">
    <value>518, 30</value>
  </data>
  <data name="flpProperties.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="flpProperties.WrapContents" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;flpProperties.Name" xml:space="preserve">
    <value>flpProperties</value>
  </data>
  <data name="&gt;&gt;flpProperties.Type" xml:space="preserve">
    <value>System.Windows.Forms.FlowLayoutPanel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;flpProperties.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;flpProperties.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="btnOK.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="btnOK.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>312, 303</value>
  </data>
  <data name="btnOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 22</value>
  </data>
  <data name="btnOK.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="btnOK.Text" xml:space="preserve">
    <value>确定</value>
  </data>
  <data name="&gt;&gt;btnOK.Name" xml:space="preserve">
    <value>btnOK</value>
  </data>
  <data name="&gt;&gt;btnOK.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnOK.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnOK.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <metadata name="cmsAlignmentHorizontal.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="tsmiAlignmentLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 22</value>
  </data>
  <data name="tsmiAlignmentLeft.Text" xml:space="preserve">
    <value>左</value>
  </data>
  <data name="tsmiAlignmentCenter.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 22</value>
  </data>
  <data name="tsmiAlignmentCenter.Text" xml:space="preserve">
    <value>中心</value>
  </data>
  <data name="tsmiAlignmentRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 22</value>
  </data>
  <data name="tsmiAlignmentRight.Text" xml:space="preserve">
    <value>右</value>
  </data>
  <data name="cmsAlignmentHorizontal.Size" type="System.Drawing.Size, System.Drawing">
    <value>101, 70</value>
  </data>
  <data name="&gt;&gt;cmsAlignmentHorizontal.Name" xml:space="preserve">
    <value>cmsAlignmentHorizontal</value>
  </data>
  <data name="&gt;&gt;cmsAlignmentHorizontal.Type" xml:space="preserve">
    <value>System.Windows.Forms.ContextMenuStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <metadata name="cmsAlignmentVertical.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>203, 17</value>
  </metadata>
  <data name="tsmiAlignmentTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 22</value>
  </data>
  <data name="tsmiAlignmentTop.Text" xml:space="preserve">
    <value>顶部</value>
  </data>
  <data name="tsmiAlignmentMiddle.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 22</value>
  </data>
  <data name="tsmiAlignmentMiddle.Text" xml:space="preserve">
    <value>中部</value>
  </data>
  <data name="tsmiAlignmentBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 22</value>
  </data>
  <data name="tsmiAlignmentBottom.Text" xml:space="preserve">
    <value>底部</value>
  </data>
  <data name="cmsAlignmentVertical.Size" type="System.Drawing.Size, System.Drawing">
    <value>101, 70</value>
  </data>
  <data name="&gt;&gt;cmsAlignmentVertical.Name" xml:space="preserve">
    <value>cmsAlignmentVertical</value>
  </data>
  <data name="&gt;&gt;cmsAlignmentVertical.Type" xml:space="preserve">
    <value>System.Windows.Forms.ContextMenuStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="lblTip.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="lblTip.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblTip.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblTip.Location" type="System.Drawing.Point, System.Drawing">
    <value>37, 308</value>
  </data>
  <data name="lblTip.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 12</value>
  </data>
  <data name="lblTip.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;lblTip.Name" xml:space="preserve">
    <value>lblTip</value>
  </data>
  <data name="&gt;&gt;lblTip.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblTip.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblTip.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="btnCancel.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="btnCancel.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>424, 303</value>
  </data>
  <data name="btnCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 22</value>
  </data>
  <data name="btnCancel.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnCancel.Text" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="&gt;&gt;btnCancel.Name" xml:space="preserve">
    <value>btnCancel</value>
  </data>
  <data name="&gt;&gt;btnCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnCancel.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <metadata name="cmsGradient.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>373, 17</value>
  </metadata>
  <data name="tsmiEnableGradient.Size" type="System.Drawing.Size, System.Drawing">
    <value>124, 22</value>
  </data>
  <data name="tsmiEnableGradient.Text" xml:space="preserve">
    <value>启用渐变</value>
  </data>
  <data name="tsrbmiGradientHorizontal.Size" type="System.Drawing.Size, System.Drawing">
    <value>180, 22</value>
  </data>
  <data name="tsrbmiGradientHorizontal.Text" xml:space="preserve">
    <value>水平</value>
  </data>
  <data name="tsrbmiGradientVertical.Size" type="System.Drawing.Size, System.Drawing">
    <value>180, 22</value>
  </data>
  <data name="tsrbmiGradientVertical.Text" xml:space="preserve">
    <value>垂直</value>
  </data>
  <data name="tsrbmiGradientForwardDiagonal.Size" type="System.Drawing.Size, System.Drawing">
    <value>180, 22</value>
  </data>
  <data name="tsrbmiGradientForwardDiagonal.Text" xml:space="preserve">
    <value>正对角线</value>
  </data>
  <data name="tsrbmiGradientBackwardDiagonal.Size" type="System.Drawing.Size, System.Drawing">
    <value>180, 22</value>
  </data>
  <data name="tsrbmiGradientBackwardDiagonal.Text" xml:space="preserve">
    <value>反对角线</value>
  </data>
  <data name="tsmiGradientMode.Size" type="System.Drawing.Size, System.Drawing">
    <value>124, 22</value>
  </data>
  <data name="tsmiGradientMode.Text" xml:space="preserve">
    <value>渐变模式</value>
  </data>
  <data name="cmsGradient.Size" type="System.Drawing.Size, System.Drawing">
    <value>125, 48</value>
  </data>
  <data name="&gt;&gt;cmsGradient.Name" xml:space="preserve">
    <value>cmsGradient</value>
  </data>
  <data name="&gt;&gt;cmsGradient.Type" xml:space="preserve">
    <value>System.Windows.Forms.ContextMenuStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="btnSwapEnterKey.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 303</value>
  </data>
  <data name="btnSwapEnterKey.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 22</value>
  </data>
  <data name="btnSwapEnterKey.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;btnSwapEnterKey.Name" xml:space="preserve">
    <value>btnSwapEnterKey</value>
  </data>
  <data name="&gt;&gt;btnSwapEnterKey.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnSwapEnterKey.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnSwapEnterKey.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>534, 333</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>文本输入</value>
  </data>
  <data name="&gt;&gt;tsmiAlignmentLeft.Name" xml:space="preserve">
    <value>tsmiAlignmentLeft</value>
  </data>
  <data name="&gt;&gt;tsmiAlignmentLeft.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiAlignmentCenter.Name" xml:space="preserve">
    <value>tsmiAlignmentCenter</value>
  </data>
  <data name="&gt;&gt;tsmiAlignmentCenter.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiAlignmentRight.Name" xml:space="preserve">
    <value>tsmiAlignmentRight</value>
  </data>
  <data name="&gt;&gt;tsmiAlignmentRight.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiAlignmentTop.Name" xml:space="preserve">
    <value>tsmiAlignmentTop</value>
  </data>
  <data name="&gt;&gt;tsmiAlignmentTop.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiAlignmentMiddle.Name" xml:space="preserve">
    <value>tsmiAlignmentMiddle</value>
  </data>
  <data name="&gt;&gt;tsmiAlignmentMiddle.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiAlignmentBottom.Name" xml:space="preserve">
    <value>tsmiAlignmentBottom</value>
  </data>
  <data name="&gt;&gt;tsmiAlignmentBottom.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiEnableGradient.Name" xml:space="preserve">
    <value>tsmiEnableGradient</value>
  </data>
  <data name="&gt;&gt;tsmiEnableGradient.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiGradientMode.Name" xml:space="preserve">
    <value>tsmiGradientMode</value>
  </data>
  <data name="&gt;&gt;tsmiGradientMode.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsrbmiGradientHorizontal.Name" xml:space="preserve">
    <value>tsrbmiGradientHorizontal</value>
  </data>
  <data name="&gt;&gt;tsrbmiGradientHorizontal.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.ToolStripRadioButtonMenuItem, OCR助手, Version=*******, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;tsrbmiGradientVertical.Name" xml:space="preserve">
    <value>tsrbmiGradientVertical</value>
  </data>
  <data name="&gt;&gt;tsrbmiGradientVertical.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.ToolStripRadioButtonMenuItem, OCR助手, Version=*******, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;tsrbmiGradientForwardDiagonal.Name" xml:space="preserve">
    <value>tsrbmiGradientForwardDiagonal</value>
  </data>
  <data name="&gt;&gt;tsrbmiGradientForwardDiagonal.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.ToolStripRadioButtonMenuItem, OCR助手, Version=*******, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;tsrbmiGradientBackwardDiagonal.Name" xml:space="preserve">
    <value>tsrbmiGradientBackwardDiagonal</value>
  </data>
  <data name="&gt;&gt;tsrbmiGradientBackwardDiagonal.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.ToolStripRadioButtonMenuItem, OCR助手, Version=*******, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;ttTextInput.Name" xml:space="preserve">
    <value>ttTextInput</value>
  </data>
  <data name="&gt;&gt;ttTextInput.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolTip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>TextDrawingInputBox</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>