﻿using OCRTools.Properties;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Timer = System.Windows.Forms.Timer;

namespace OCRTools
{
    public partial class FormOCR : Form
    {
        private FrmSearch frmSearch;

        private bool isLeave;
        private bool isReduceing;

        private double LeaveSecond;

        public FormOCR()
        {
            SetStyle(ControlStyles.SupportsTransparentBackColor, true);
            InitializeComponent();
            BackColor = Color.Transparent;
            BackgroundImage = Resources.mini_search;
            BackgroundImageLayout = ImageLayout.Stretch;
            var tmrTick = new Timer { Interval = 100 };
            tmrTick.Tick += TmrTick_Tick;
            tmrTick.Enabled = true;
        }

        public object Content { get; set; }

        public ClipboardContentType ContentType { get; set; }

        public bool IsForceFocus { get; set; }

        public bool IsLeave
        {
            get => isLeave;
            set
            {
                isLeave = value;
                if (isLeave && !isReduceing)
                {
                    isReduceing = true;
                    Task.Factory.StartNew(() =>
                    {
                        ReduceOpactity();
                        isReduceing = false;
                    });
                }
            }
        }

        protected override CreateParams CreateParams
        {
            get
            {
                var result = base.CreateParams;
                result.ExStyle |= 0x08000000; // WS_EX_NOACTIVATE
                return result;
            }
        }

        [DllImport("user32.dll", CharSet = CharSet.Auto, ExactSpelling = true)]
        public static extern bool ShowWindow(HandleRef hWnd, int nCmdShow);

        private void TmrTick_Tick(object sender, EventArgs e)
        {
            CommonString.IsOnOcrButton = Visible && Bounds.Contains(Cursor.Position);
            IsLeave = !Visible || !IsForceFocus && !CommonString.IsOnOcrButton;
            if (!IsLeave)
            {
                LeaveSecond = 0;
            }
            else if (Visible)
            {
                LeaveSecond += 0.1;
                if (LeaveSecond > 10) HideWindow();
            }
        }

        public void HideWindow(bool hideSearch = false)
        {
            Hide();
            if (hideSearch)
                frmSearch?.Hide();
        }

        public void ShowTool(bool isFirst = false)
        {
            Opacity = 0;
            if (isFirst)
            {
                ShowWindow(new HandleRef(this, Handle), 4);
                //this.Show();
                IsForceFocus = true;
                IsLeave = false;
                Task.Factory.StartNew(() =>
                {
                    var left = 15;
                    while (!CommonString.isExit && !IsLeave && left > 0)
                    {
                        Thread.Sleep(100);
                        left--;
                    }

                    IsForceFocus = false;
                });
            }

            Opacity = 0.9;
        }

        private void FormOCR_MouseHover(object sender, EventArgs e)
        {
            ShowSearch();
        }

        public void ShowSearch()
        {
            CommonMethod.DetermineCall(this, delegate
             {
                 if (Content == null) return;
                 if (!ContentType.Equals(ClipboardContentType.文本))
                 {
                     switch (ContentType)
                     {
                         case ClipboardContentType.图片:
                             break;
                         case ClipboardContentType.文件:
                             FrmMain.DragDropEventDelegate?.Invoke(new List<string> { Content?.ToString() }, null, null,
                                 ProcessBy.主界面, null);
                             break;
                     }

                     HideWindow(true);
                 }
                 else
                 {
                     var isFirst = false;
                     if (frmSearch == null)
                     {
                         isFirst = true;
                         frmSearch = new FrmSearch { Icon = Icon };
                     }
                     else
                     {
                         if (frmSearch.Visible && Equals(frmSearch.SelectedText, Content?.ToString())) return;
                     }

                     frmSearch.SelectedText = Content?.ToString();

                     var location = new Point(Cursor.Position.X + 20, Cursor.Position.Y + 20);
                     frmSearch.Location = location;
                     frmSearch.Opacity = 0;
                     frmSearch.ShowTool(isFirst);
                     if (!Equals(location, frmSearch.Location)) frmSearch.Location = location;
                     frmSearch.Opacity = 1;
                     Application.DoEvents();
                 }
             });
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            e.Cancel = true;
            base.OnClosing(e);
        }

        private void FormOCR_MouseMove(object sender, MouseEventArgs e)
        {
            IsLeave = false;
            ShowTool();
        }

        private void ReduceOpactity()
        {
            try
            {
                while (IsLeave && !CommonString.isExit && Opacity >= 0.4)
                {
                    Thread.Sleep(10);
                    Opacity -= 0.003;
                }
            }
            catch
            {
            }
        }

        internal UCContent GetUcContent()
        {
            return frmSearch?.GetUcContent();
        }

        internal void ShowLoading(string strProcessOcr, bool isShowLoading)
        {
            frmSearch?.ShowLoading(strProcessOcr, isShowLoading);
        }

        internal void CloseLoading()
        {
            frmSearch?.CloseLoading();
        }
    }

    public enum ClipboardContentType
    {
        图片,
        文件,
        文本
    }
}