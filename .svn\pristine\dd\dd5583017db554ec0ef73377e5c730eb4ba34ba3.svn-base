﻿namespace OCRTools.ImgUpload
{
    public abstract class BaseImageUpload
    {
        public bool Enable { get; set; } = true;

        public bool SupportCompress { get; set; }

        public string Name { get; set; }

        public int MaxSize { get; set; }

        /// <summary>
        /// 上传图片
        /// </summary>
        /// <param name="content"></param>
        /// <returns></returns>
        public abstract string GetResult(byte[] content, bool isZip = false);

        /// <summary>
        /// 获取压缩后的图片
        /// </summary>
        /// <param name="content"></param>
        /// <param name="strUrl"></param>
        /// <returns></returns>
        public virtual byte[] GetZipResult(byte[] content, ref string strUrl)
        {
            strUrl = GetResult(content, true);
            return CommonMethod.GetUrlBytes(strUrl);
        }
    }
}
