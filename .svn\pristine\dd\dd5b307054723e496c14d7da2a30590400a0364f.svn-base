﻿using OCRTools.Common;
using System;
using System.Diagnostics;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    /// <summary>
    ///     Provides an overlay over the screen that allows the selection of a window.
    /// </summary>
    internal class RulerOverlayForm : Form
    {
        private bool _transp;

        public RulerOverlayForm()
        {
            FormBorderStyle = FormBorderStyle.None;
            ShowInTaskbar = false;
            BackColor = Color.White;
            Opacity = 0.2;
            DoubleBuffered = true;
        }

        public Rectangle WindowSelection { get; private set; }
        public Color SelectionColor { get; set; } = Color.Red;

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            Cursor = Cursors.Cross;
            Location = StaticValue.ScreenRectangle.Location;
            Size = StaticValue.ScreenRectangle.Size;
        }

        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m);
            if (m.Msg == 0x84) //WM_NCHITTEST (called in response to WindowFromPoint)
                // Set HTTRANSPARENT as result to ignore this window in WindowFromPoint.
                if (_transp)
                    m.Result = (IntPtr)(-1);
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Escape:
                    Close();
                    break;
            }

            base.OnKeyDown(e);
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            base.OnMouseMove(e);
            try
            {
                var point = PointToScreen(e.Location);
                _transp = true;
                WindowSelection = NativeMethods.GetWindowRectangle(point);
                _transp = false;
                Invalidate();
            }
            catch (Exception ex)
            {
                Debug.WriteLine(ex.ToString());
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            e.Graphics.Clear(BackColor);
            using (var brush = new SolidBrush(SelectionColor))
            {
                e.Graphics.FillRectangle(brush, RectangleToClient(WindowSelection));
            }
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            DialogResult = DialogResult.OK;
        }
    }
}