namespace UtfUnknown.Core.Models.MultiByte.Chinese
{
    public class Gb18030SmModel : StateMachineModel
    {
        private static readonly int[] Gb18030Cls =
        {
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 0, 0),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 0, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(3, 3, 3, 3, 3, 3, 3, 3),
            BitPackage.Pack4Bits(3, 3, 1, 1, 1, 1, 1, 1),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 2, 2, 4),
            BitPackage.Pack4Bits(5, 6, 6, 6, 6, 6, 6, 6),
            BitPackage.Pack4Bits(6, 6, 6, 6, 6, 6, 6, 6),
            BitPackage.Pack4Bits(6, 6, 6, 6, 6, 6, 6, 6),
            BitPackage.Pack4Bits(6, 6, 6, 6, 6, 6, 6, 6),
            BitPackage.Pack4Bits(6, 6, 6, 6, 6, 6, 6, 6),
            BitPackage.Pack4Bits(6, 6, 6, 6, 6, 6, 6, 6),
            BitPackage.Pack4Bits(6, 6, 6, 6, 6, 6, 6, 6),
            BitPackage.Pack4Bits(6, 6, 6, 6, 6, 6, 6, 6),
            BitPackage.Pack4Bits(6, 6, 6, 6, 6, 6, 6, 6),
            BitPackage.Pack4Bits(6, 6, 6, 6, 6, 6, 6, 6),
            BitPackage.Pack4Bits(6, 6, 6, 6, 6, 6, 6, 6),
            BitPackage.Pack4Bits(6, 6, 6, 6, 6, 6, 6, 6),
            BitPackage.Pack4Bits(6, 6, 6, 6, 6, 6, 6, 6),
            BitPackage.Pack4Bits(6, 6, 6, 6, 6, 6, 6, 6),
            BitPackage.Pack4Bits(6, 6, 6, 6, 6, 6, 6, 6),
            BitPackage.Pack4Bits(6, 6, 6, 6, 6, 6, 6, 0)
        };

        private static readonly int[] Gb18030St =
        {
            BitPackage.Pack4Bits(1, 0, 0, 0, 0, 0, 3, 1),
            BitPackage.Pack4Bits(1, 1, 1, 1, 1, 1, 2, 2),
            BitPackage.Pack4Bits(2, 2, 2, 2, 2, 1, 1, 0),
            BitPackage.Pack4Bits(4, 1, 0, 0, 1, 1, 1, 1),
            BitPackage.Pack4Bits(1, 1, 5, 1, 1, 1, 2, 1),
            BitPackage.Pack4Bits(1, 1, 0, 0, 0, 0, 0, 0)
        };

        private static readonly int[] Gb18030CharLenTable =
        {
            0,
            1,
            1,
            1,
            1,
            1,
            2
        };

        public Gb18030SmModel()
            : base(
                new BitPackage(BitPackage.indexShift4Bits, BitPackage.shiftMask4Bits, BitPackage.bitShift4Bits,
                    BitPackage.unitMask4Bits, Gb18030Cls), 7,
                new BitPackage(BitPackage.indexShift4Bits, BitPackage.shiftMask4Bits, BitPackage.bitShift4Bits,
                    BitPackage.unitMask4Bits, Gb18030St), Gb18030CharLenTable, "gb18030")
        {
        }
    }
}