﻿using System;
using System.Drawing;
using System.Threading;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class UcLoading : UserControl
    {
        private Image _bgImg;

        private LoadingTypeConfig _config;

        private bool _isStop = true;
        private Thread _thread;

        public int iC;

        public UcLoading()
        {
            InitializeComponent();
        }

        public void InitLoading(Size size, Point locaion)
        {
            Visible = false;
            Size = size;
            Location = locaion;
            BackColor = Color.Transparent;
            BackgroundImageLayout = ImageLayout.Center;
            MouseDoubleClick += UcLoading_MouseDoubleClick;
        }

        private void UcLoading_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            var form = FindForm();
            if (form == null) return;
            form.WindowState = form.WindowState == FormWindowState.Maximized
                ? FormWindowState.Normal
                : FormWindowState.Maximized;
        }

        public void ShowLoading(string strText = "", bool isShowLoading = true)
        {
            ShowText(strText);
            Visible = isShowLoading;
            if (isShowLoading)
            {
                BringToFront();
                if (_isStop)
                {
                    _isStop = false;
                    _thread = new Thread(Start);
                    _thread.Start();
                }
            }
        }

        private void Start()
        {
            while (!_isStop)
            {
                TmrTick();
                Thread.Sleep(_config.Interval);
            }

            _thread = null;
        }

        public void ShowText(string strText)
        {
            CommonString.IsOnRec = true;
            lblText.Text = strText;
            lblText.ForeColor = CommonString.IsDarkModel ? Color.White : Color.Black;
        }

        public void CloseLoading(int seconds = 0)
        {
            if (seconds > 0)
                for (var i = 0; i < seconds * 2; i++)
                {
                    Thread.Sleep(500);
                    Application.DoEvents();
                }

            Visible = false;
            _isStop = true;
            CommonString.IsOnRec = false;
        }

        private void TmrTick()
        {
            try
            {
                if (iC >= _config.ImgCount) iC = 0;
                _bgImg = LoadingTypeHelper.GetImageByConfig(_config, iC);
                //SetBits((Bitmap)bgImg);
                picImage.Image = _bgImg;
                iC++;
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        internal void SetLoadingType(LoadingType loadingType)
        {
            iC = 0;
            _config = LoadingTypeHelper.GetTypeConfig(loadingType);
        }
    }
}