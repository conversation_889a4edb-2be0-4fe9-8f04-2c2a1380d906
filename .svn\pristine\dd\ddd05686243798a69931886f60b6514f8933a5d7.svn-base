﻿using OCRTools.Properties;
using System;
using System.IO;
using System.Reflection;
using System.Windows.Forms;

namespace OCRTools
{
    public static class ShortcutHelpers
    {
        public static void ClearOldShortCuts()
        {
            //公用桌面-OCR助手.lnk
            var shortCutFile = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonDesktopDirectory), CommonString.ProductName + ".lnk");
            Delete(shortCutFile);
            //个人桌面-OCR助手.lnk
            shortCutFile = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), CommonString.ProductName + ".lnk");
            Delete(shortCutFile);

            //公用桌面-OCR文字识别助手.lnk
            var publicShortCutFile = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonDesktopDirectory), CommonString.FullName + ".lnk");
            //个人桌面-OCR文字识别助手.lnk
            var userShortCutFile = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), CommonString.FullName + ".lnk");
            //如果两个都存在，删除个人桌面
            if (File.Exists(userShortCutFile) && File.Exists(publicShortCutFile))
            {
                Delete(userShortCutFile);
            }

            //发送到-OCR助手.lnk
            shortCutFile = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.SendTo), CommonString.ProductName + ".lnk");
            Delete(shortCutFile);
        }

        public static void CreateShortCuts()
        {
            //公用桌面-OCR文字识别助手.lnk
            var publicShortCutFile = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonDesktopDirectory), CommonString.FullName + ".lnk");
            //个人桌面-OCR文字识别助手.lnk
            var userShortCutFile = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), CommonString.FullName + ".lnk");

            if (!File.Exists(userShortCutFile) && !File.Exists(publicShortCutFile))
            {
                if (!CreateShortCut(publicShortCutFile, Application.ExecutablePath, CommonString.FullName))
                {
                    CreateShortCut(userShortCutFile, Application.ExecutablePath, CommonString.FullName);
                }
            }
            //发送到-OCR文字识别助手.lnk
            var sendToShortCutFile = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.SendTo), CommonString.FullName + ".lnk");
            CreateShortCut(sendToShortCutFile, Application.ExecutablePath, CommonString.FullName);
        }

        private static bool CreateShortCut(string shortCutFile, string targetPath, string description = "")
        {
            if (!string.IsNullOrEmpty(shortCutFile) && File.Exists(shortCutFile))
            {
                return true;
            }
            try
            {
                var type = Type.GetTypeFromProgID("WScript.Shell");
                object instance = Activator.CreateInstance(type);
                var result = type.InvokeMember("CreateShortCut", BindingFlags.InvokeMethod, null, instance, new object[] { shortCutFile });

                type = result.GetType();
                type.InvokeMember("TargetPath", BindingFlags.SetProperty, null, result, new object[] { targetPath });
                type.InvokeMember("Description", BindingFlags.SetProperty, null, result, new object[] { description });
                type.InvokeMember("Save", BindingFlags.InvokeMethod, null, result, null);
                return true;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return false;
        }

        private static bool Delete(string shortcutPath)
        {
            if (!string.IsNullOrEmpty(shortcutPath) && File.Exists(shortcutPath))
            {
                File.Delete(shortcutPath);
                return true;
            }

            return false;
        }

        public static void RegFileType()
        {
            var updateFilePath = string.Format("{0}\\{2}-注册右键菜单-{1:yyyyMMddHHmmssfff}.exe", Path.GetTempPath().TrimEnd('\\'), ServerTime.DateTime, CommonString.ProductName);
            try
            {
                if (File.Exists(updateFilePath))
                    try
                    {
                        File.Delete(updateFilePath);
                    }
                    catch { }

                if (!File.Exists(updateFilePath))
                    try
                    {
                        File.WriteAllBytes(updateFilePath, Resources.RegSysMenu);
                    }
                    catch { }
                CommonString.RunAsAdmin(updateFilePath, $"\"{Application.ExecutablePath}\" " + CommonSetting.注册右键菜单, true, false);
            }
            catch
            {
                CommonString.RunAsAdmin(updateFilePath, $"\"{Application.ExecutablePath}\" " + CommonSetting.注册右键菜单, false, false);
            }
        }
    }
}
