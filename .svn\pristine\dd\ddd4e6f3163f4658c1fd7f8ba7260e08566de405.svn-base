﻿using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Language;
using System;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmUserInfo : MetroForm
    {
        private UserTypeInfo _nextUserType;

        public FrmUserInfo()
        {
            InitializeComponent();
            CommonMethod.SetStyle(btnOpenVip, ControlStyles.Selectable, false);
            CommonMethod.SetStyle(lnkLogout, ControlStyles.Selectable, false);
            CommonMethod.SetStyle(lnkEditNickName, ControlStyles.Selectable, false);
            ShadowType = CommonString.CommonShadowType;
            lblAccount.Text = Program.NowUser?.Account;
            lnkNickName.Text = Program.NowUser?.NickName;
            lnkUserType.Text = Program.NowUser?.UserTypeName;
            if (Program.IsLogined())
            {
                lnkRegDate.Text = string.Format("{0}{1}"
                    , Program.NowUser != null && Program.NowUser.DtExpired.Year < 2000 ? "" : Program.NowUser.DtExpired.ToDateStr("yyyy-MM-dd")
                    , GetLeftDays(Program.NowUser.DtExpired));
            }
            else
            {
                Close();
            }

            InitBtnText();

            this.AddContactUserBtn("FrmUserInfo");
        }

        public override void OnThemeChange()
        {
            InitBtnText();
        }

        private string GetLeftDays(DateTime dtExpired)
        {
            var ts = new TimeSpan(dtExpired.Ticks - ServerTime.DateTime.Ticks);
            if (ts.TotalSeconds <= 0)
            {
                return "已过期".CurrentText();
            }
            else if (ts.TotalDays < 100)
            {
                return string.Format("({1}{0:F1}天)", ts.TotalDays, "剩".CurrentText());
            }
            return string.Empty;
        }

        private void InitBtnText()
        {
            _nextUserType = CommonUser.GetNextType();
            btnOpenVip.Text = _nextUserType.Name.CurrentText();
            var image = btnOpenVip.SetResourceImage("vip_" + _nextUserType.Code) ?? btnOpenVip.SetResourceImage("qqKeFu");
            image = ImageProcessHelper.ScaleImage(image, this.GetDpiScale());
            btnOpenVip.ImageSize = image.Size;
            btnOpenVip.Image = image;
            this.lblAccount.BringToFront();
            this.lnkUserType.BringToFront();
            this.lnkNickName.BringToFront();
            this.lnkRegDate.BringToFront();
        }

        private void btnOpenVip_Click(object sender, EventArgs e)
        {
            if (CommonString.IsOnLine)
            {
                using (var frmBuy = new FrmGoBuy())
                {
                    frmBuy.Icon = Icon;
                    frmBuy.NextUserType = _nextUserType;
                    frmBuy.ShowDialog(this);
                }

                //CommonMsg.ShowToWindow(this, new Point(136, 20));
                //MessageBox.Show(this, "欢迎开通会员服务！\n识别速度更快，精度更高，功能更全面……\n敬请期待！", CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                CommonMethod.ShowHelpMsg(CommonString.StrNetWorkError.CurrentText());
            }
        }

        private void lnkLogout_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            Program.NowUser = null;
            CommonString.IsAutoLogin = false;
            Close();
        }

        private void lnkEditNickName_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            if (!Program.IsLogined()) return;
            if (!CommonString.IsOnLine)
            {
                CommonMethod.ShowHelpMsg(CommonString.StrNetWorkError.CurrentText());
                return;
            }

            if (lnkEditNickName.Text.Equals("编辑".CurrentText()))
            {
                lnkEditNickName.Text = "保存".CurrentText();
                txtNickName.Visible = true;
                lnkNickName.Visible = false;
                txtNickName.Text = lnkNickName.Text;
                txtNickName.SelectAll();
                txtNickName.Focus();
            }
            else
            {
                var nickName = txtNickName.Text.Trim();
                if (nickName.Length < 2 || nickName.Length > 12 ||
                    !new Regex(@"^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z0-9_])").IsMatch(nickName))
                {
                    MessageBox.Show(this, "昵称为2-12位的中英文数字字母或下划线！".CurrentText(), CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
                    txtNickName.Focus();
                    return;
                }

                var result = Program.NowUser.NickName.Equals(nickName);
                var strMsg = "";
                if (!result)
                    result = OcrHelper.EditNickName(Program.NowUser?.Account, nickName, ref strMsg);
                if (result)
                {
                    if (Program.NowUser != null) Program.NowUser.NickName = nickName;
                    lnkEditNickName.Text = "编辑".CurrentText();
                    txtNickName.Visible = false;
                    lnkNickName.Visible = true;
                    lnkNickName.Text = txtNickName.Text;
                }
                else
                {
                    if (string.IsNullOrEmpty(strMsg)) strMsg = "保存失败".CurrentText() + "，" + CommonString.StrRetry.CurrentText();
                    MessageBox.Show(this, strMsg, CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
}