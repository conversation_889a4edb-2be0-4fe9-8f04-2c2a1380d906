﻿using ImageLib;
using nQuant;
using OCRTools.Common;
using System.Drawing;
using System.IO;

namespace OCRTools
{
    internal class ImageCompress
    {
        const int alphaTransparency = 0;
        const int alphaFader = 0;

        internal static string CompressImageFile(string fileName, string strPath, CompressType type)
        {
            string url = string.Empty;
            var bytes = CompressImage(File.ReadAllBytes(fileName), type, ref url);
            if (bytes == null || bytes.Length <= 0) return string.Empty;

            var filePath = Path.Combine(strPath,
                Path.GetFileNameWithoutExtension(fileName) + "-" +
                ServerTime.DateTime.Millisecond + Path.GetExtension(fileName));
            File.WriteAllBytes(filePath, bytes);
            return filePath;

        }

        internal static byte[] CompressImage(byte[] byts, CompressType type, ref string strUrl)
        {
            byte[] result = null;
            try
            {
                switch (type)
                {
                    case CompressType.TinyPng:
                        result = TinyPngUpload.GetZipResult(byts, ref strUrl);
                        break;
                    case CompressType.ImgTop:
                        result = ImageTopUpload.GetZipResult(byts, ref strUrl);
                        break;
                    //case CompressType.Pnn压缩:
                    //    using (var stream = new MemoryStream(byts))
                    //    {
                    //        using (var tmp = new Bitmap(stream))
                    //        {
                    //            var process = new PnnQuantizer();
                    //            using (var resultImg = process.QuantizeImage(tmp, PixelFormat.Undefined, 256, true))
                    //            {
                    //                result = ImageProcessHelper.ImageToByte(resultImg);
                    //            }
                    //        }
                    //    }
                    //    break;
                    default:
                        using (var stream = new MemoryStream(byts))
                        {
                            using (var tmp = new Bitmap(stream))
                            {
                                var process = new WuQuantizer();
                                using (var resultImg = process.QuantizeImage(tmp, alphaTransparency, alphaFader))
                                {
                                    if (resultImg != null)
                                        result = ImageProcessHelper.ImageToByte(resultImg);
                                }
                            }
                        }
                        break;
                }
            }
            catch { }

            return result;
        }
    }

    internal enum CompressType
    {
        TinyPng,
        ImgTop,
        助手压缩,
        //Pnn压缩
    }
}
