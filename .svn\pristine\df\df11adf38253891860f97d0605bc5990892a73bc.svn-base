<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <AssemblyName>OcrLib</AssemblyName>
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
    <TargetFramework>net461</TargetFramework>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup>
    <LangVersion>default</LangVersion>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup />
  <ItemGroup>
    <Reference Include="Windows.Foundation.FoundationContract">
      <HintPath>C:\Program Files (x86)\Windows Kits\10\References\10.0.19041.0\Windows.Foundation.FoundationContract\*******\Windows.Foundation.FoundationContract.winmd</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Windows.Foundation.UniversalApiContract">
      <HintPath>C:\Program Files (x86)\Windows Kits\10\References\10.0.19041.0\Windows.Foundation.UniversalApiContract\10.0.0.0\Windows.Foundation.UniversalApiContract.winmd</HintPath>
      <Private>False</Private>
    </Reference>
    <PackageReference Include="clipper_library" Version="6.2.1" />
    <PackageReference Include="Emgu.CV" Version="4.6.0.5131" />
    <PackageReference Include="Microsoft.ML.OnnxRuntime.Managed" Version="1.13.1" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="UwpDesktop" Version="10.0.14393.3" />
  </ItemGroup>
  <ItemGroup>
    <Reference Update="System.Runtime.WindowsRuntime, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Update="System.Runtime.WindowsRuntime.UI.Xaml, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Update="Windows">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
</Project>