﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools.ShareX.Controls
{
    public partial class LineShapeComboBox : ComboBox
    {
        public LineShapeComboBox()
        {
            DrawMode = DrawMode.OwnerDrawFixed;
            DropDownStyle = ComboBoxStyle.DropDownList;
            ItemHeight = 25;
            InitializeComponent();
        }

        public int ImageType { get; set; } = -1;

        protected override void OnDrawItem(DrawItemEventArgs e)
        {
            if (e.Index < 0)
                return;
            Rectangle rect = e.Bounds;
            rect.Inflate(5, -2);//缩放一定大小
            Pen pen = new Pen(Color.Black, 2);
            float offSet = (rect.Height - pen.Width) / 2;
            pen.DashStyle = DashStyle.Solid;
            e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
            switch (ImageType)
            {
                case 1:
                    {
                        float x = 5;
                        float y = rect.Top + offSet;//如果设置成e.ItemHeigh*e.Index +offSet,则在选中节点后，由于Item位置固定，因此Item不能显示出来。
                        switch (e.Index)
                        {
                            case 0:
                                e.Graphics.DrawLine(pen, new PointF(x, y), new PointF(rect.Width - x - 10, y));
                                break;
                            case 1:
                                //绘制长间断线
                                int xNum = 0;
                                for (int i = 0; i < 4; i++)
                                {
                                    e.Graphics.DrawLine(pen, new PointF(x + xNum, y), new PointF(15 + x + xNum, y));
                                    xNum += 20;
                                }
                                e.Graphics.DrawLine(pen, new PointF(x + xNum, y), new PointF(7 + x + xNum, y));
                                break;
                            case 2:
                                //绘制短间端线
                                xNum = 0;
                                for (int i = 0; i < 10; i++)
                                {
                                    e.Graphics.DrawLine(pen, new PointF(x + xNum, y), new PointF(6 + x + xNum, y));
                                    xNum += 9;
                                }
                                break;
                            case 3:
                                //绘制长短相间
                                e.Graphics.DrawLine(pen, new PointF(x, y), new PointF(x + 10, y));
                                e.Graphics.DrawLine(pen, new PointF(x + 15, y), new PointF(x + 20, y));
                                e.Graphics.DrawLine(pen, new PointF(x + 25, y), new PointF(x + 35, y));
                                e.Graphics.DrawLine(pen, new PointF(x + 40, y), new PointF(x + 45, y));
                                e.Graphics.DrawLine(pen, new PointF(x + 50, y), new PointF(x + 60, y));
                                e.Graphics.DrawLine(pen, new PointF(x + 65, y), new PointF(x + 70, y));
                                e.Graphics.DrawLine(pen, new PointF(x + 75, y), new PointF(x + 87, y));
                                //e.Graphics.DrawLine(pen, new PointF(x + 90, y), new PointF(x + 95, y));
                                break;
                            case 4:
                                //绘制长短短相间
                                e.Graphics.DrawLine(pen, new PointF(x, y), new PointF(x + 10, y));
                                e.Graphics.DrawLine(pen, new PointF(x + 15, y), new PointF(x + 18, y));
                                e.Graphics.DrawLine(pen, new PointF(x + 23, y), new PointF(x + 26, y));
                                e.Graphics.DrawLine(pen, new PointF(x + 31, y), new PointF(x + 41, y));
                                e.Graphics.DrawLine(pen, new PointF(x + 46, y), new PointF(x + 49, y));
                                e.Graphics.DrawLine(pen, new PointF(x + 54, y), new PointF(x + 57, y));
                                e.Graphics.DrawLine(pen, new PointF(x + 62, y), new PointF(x + 72, y));
                                e.Graphics.DrawLine(pen, new PointF(x + 77, y), new PointF(x + 80, y));
                                e.Graphics.DrawLine(pen, new PointF(x + 85, y), new PointF(x + 88, y));
                                //e.Graphics.DrawLine(pen, new PointF(x + 93, y), new PointF(x + 103, y));
                                break;
                        }
                    }
                    break;
                case 2:
                    {

                        float x = 5;
                        float y = rect.Top + offSet;
                        switch (e.Index)
                        {
                            case 0:
                                pen.CustomEndCap = new AdjustableArrowCap(8, 8, true)
                                {
                                    MiddleInset = 3
                                };
                                break;
                            case 1:
                                pen.CustomStartCap = new AdjustableArrowCap(8, 8, true)
                                {
                                    MiddleInset = 3
                                };
                                break;
                            case 2:
                                pen.CustomEndCap = new AdjustableArrowCap(8, 8, true)
                                {
                                    MiddleInset = 3
                                };
                                pen.CustomStartCap = new AdjustableArrowCap(8, 8, true)
                                {
                                    MiddleInset = 3
                                };
                                break;
                        }
                        e.Graphics.DrawLine(pen, new PointF(x, y), new PointF(rect.Width - x - 10, y));
                    }
                    break;
                case 3:
                    {
                        float x = 0;
                        float y = rect.Top;
                        switch (e.Index)
                        {
                            case 0:
                                e.Graphics.DrawString("1234567890", CommonString.GetFont(CommonString.StrDefaultFontName, 15, FontStyle.Bold), new SolidBrush(Color.Black), new PointF(x, y));
                                break;
                            case 1:
                                e.Graphics.DrawString("ABCDEFGHI", CommonString.GetFont(CommonString.StrDefaultFontName, 15, FontStyle.Bold), new SolidBrush(Color.Black), new PointF(x, y));
                                break;
                            case 2:
                                e.Graphics.DrawString("abcdefghijk", CommonString.GetFont(CommonString.StrDefaultFontName, 15, FontStyle.Bold), new SolidBrush(Color.Black), new PointF(x, y));
                                break;
                            case 3:
                                e.Graphics.DrawString("i ii iii iv v vii", CommonString.GetFont(CommonString.StrDefaultFontName, 15, FontStyle.Bold), new SolidBrush(Color.Black), new PointF(x, y));
                                break;
                            case 4:
                                e.Graphics.DrawString("ⅠⅡⅢⅣⅤⅥ", CommonString.GetFont(CommonString.StrDefaultFontName, 15, FontStyle.Bold), new SolidBrush(Color.Black), new PointF(x, y));
                                break;
                        }
                    }
                    break;
                case 4:
                    {
                        float y = rect.Top + offSet;
                        pen.Width = e.Index + 1;
                        e.Graphics.DrawString(pen.Width.ToString(), CommonString.GetFont(CommonString.StrDefaultFontName, 13, FontStyle.Bold), new SolidBrush(Color.DimGray), new PointF(2 + (pen.Width < 10 ? 5 : 0), rect.Top));
                        e.Graphics.DrawLine(pen, new PointF(25, y), new PointF(rect.Width - 15, y));
                    }
                    break;
            }
        }
    }
}
