﻿using System;
using System.Runtime.InteropServices;

namespace OCRTools.Common
{
    public class HotKeyHelper
    {
        //定义了辅助键的名称（将数字转变为字符以便于记忆，也可去除此枚举而直接使用数值）

        /// <summary> The RegisterHotKey function defines a system-wide hot key </summary>
        /// <param name="hWnd">Handle to the window that will receive WM_HOTKEY messages generated by the hot key.</param>
        /// <param name="id">Specifies the identifier of the hot key.</param>
        /// <param name="fsModifiers">
        ///     Specifies keys that must be pressed in combination with the key
        ///     specified by the 'vk' parameter in order to generate the WM_HOTKEY message.
        /// </param>
        /// <param name="vk">Specifies the virtual-key code of the hot key</param>
        /// <returns><c>true</c> if the function succeeds, otherwise <c>false</c></returns>
        /// <seealso cref="http://msdn.microsoft.com/en-us/library/ms646309(VS.85).aspx" />
        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool RegisterHotKey(IntPtr hWnd, int id, uint fsModifiers, uint vk);

        [DllImport("user32.dll", SetLastError = true)]
        private static extern bool UnregisterHotKey(
            IntPtr hWnd, //要取消热键的窗口的句柄
            int id //要取消热键的ID
        );

        internal static void RegKey(IntPtr hwnd, HotKeyEntity hotkeyInfo)
        {
            if (!hotkeyInfo.IsValidHotkey || hotkeyInfo.Id <= 0) return;
            try
            {
                if (!RegisterHotKey(hwnd, hotkeyInfo.Id, (uint)hotkeyInfo.ModifiersEnum, (uint)hotkeyInfo.KeyCode))
                {
                    if (Marshal.GetLastWin32Error() == 1409)
                        CommonMethod.ShowHelpMsg(string.Format("快捷键{0}被占用,注册失败！", hotkeyInfo));
                    else
                        CommonMethod.ShowHelpMsg(string.Format("注册快捷键{0}失败！", hotkeyInfo));
                }
            }
            catch (Exception)
            {
            }
        }

        /// <summary>
        ///     注销热键
        /// </summary>
        /// <param name="hwnd">窗口句柄</param>
        /// <param name="hotKeyId">热键ID</param>
        public static void UnRegKey(IntPtr hwnd, int hotKeyId)
        {
            //注销Id号为hotKey_id的热键设定
            UnregisterHotKey(hwnd, hotKeyId);
        }
    }
}