﻿using System;
using System.Collections;
using System.Globalization;

namespace OCRTools.Common
{
    public static class ChinaDate
    {
        private static readonly ChineseLunisolarCalendar China = new ChineseLunisolarCalendar();
        private static readonly Hashtable GHoliday = new Hashtable();
        private static readonly Hashtable NHoliday = new Hashtable();

        static ChinaDate()
        {
            //公历节日
            GHoliday.Add("0101", "元旦");
            GHoliday.Add("0214", "情人节");
            GHoliday.Add("0308", "妇女节");
            GHoliday.Add("0312", "植树节");
            GHoliday.Add("0401", "愚人节");
            GHoliday.Add("0501", "劳动节");
            GHoliday.Add("0504", "青年节");
            GHoliday.Add("0520", "情人节");
            GHoliday.Add("0601", "儿童节");
            GHoliday.Add("0701", "建党节");
            GHoliday.Add("0801", "建军节");
            GHoliday.Add("0910", "教师节");
            GHoliday.Add("1001", "国庆节");
            GHoliday.Add("1224", "平安夜");
            GHoliday.Add("1225", "圣诞节");

            //农历节日
            NHoliday.Add("0101", "春节");
            NHoliday.Add("0115", "元宵节");
            NHoliday.Add("0505", "端午节");
            NHoliday.Add("0707", "七夕");
            NHoliday.Add("0815", "中秋节");
            NHoliday.Add("0909", "重阳节");
            NHoliday.Add("1208", "腊八节");
            NHoliday.Add("1224", "小年");
        }

        /// <summary>
        ///     获取农历
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        public static string GetChinaDate(DateTime dt)
        {
            if (dt > China.MaxSupportedDateTime || dt < China.MinSupportedDateTime)
                //日期范围：1901 年 2 月 19 日 - 2101 年 1 月 28 日
                throw new Exception(string.Format("日期超出范围！必须在{0:yyyy-MM-dd}到{1:yyyy-MM-dd}之间！",
                    China.MinSupportedDateTime, China.MaxSupportedDateTime));

            var strHoliday = GetHolidayStr(dt);
            if (string.IsNullOrEmpty(strHoliday))
            {
                strHoliday = GetHolidayStr(dt.AddDays(1));
                if (!string.IsNullOrEmpty(strHoliday)) strHoliday = string.Format("，明天是[{0}]", strHoliday);
            }
            else
            {
                strHoliday = string.Format("-[{0}]", strHoliday);
            }

            return strHoliday;
        }

        private static string GetHolidayStr(DateTime dt)
        {
            var strHoliday = GetHoliday(dt);
            if (string.IsNullOrEmpty(strHoliday)) strHoliday = GetChinaHoliday(dt);
            return strHoliday;
        }

        /// <summary>
        ///     获取公历节日
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        private static string GetHoliday(DateTime dt)
        {
            var strReturn = "";
            var g = GHoliday[dt.Month.ToString("00") + dt.Day.ToString("00")];
            if (g != null) strReturn = g.ToString();

            return strReturn;
        }

        /// <summary>
        ///     获取农历节日
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        private static string GetChinaHoliday(DateTime dt)
        {
            var strReturn = "";
            var year = China.GetYear(dt);
            var iMonth = China.GetMonth(dt);
            var leapMonth = China.GetLeapMonth(year);
            var iDay = China.GetDayOfMonth(dt);
            if (China.GetDayOfYear(dt) == China.GetDaysInYear(year))
            {
                strReturn = "除夕";
            }
            else if (leapMonth != iMonth)
            {
                if (leapMonth != 0 && iMonth >= leapMonth) iMonth--;
                var n = NHoliday[iMonth.ToString("00") + iDay.ToString("00")];
                if (n != null)
                {
                    if (strReturn == "")
                        strReturn = n.ToString();
                    else
                        strReturn += " " + n;
                }
            }

            return strReturn;
        }
    }
}