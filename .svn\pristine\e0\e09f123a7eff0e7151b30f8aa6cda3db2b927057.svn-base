using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools
{
    public class PanelPictureView : ImageBox
    {
        private readonly Color IMG_MODE_FOCUS_COLOR = Color.FromArgb(50, Color.Red);
        private readonly Color IMG_MODE_CLICK_COLOR = Color.FromArgb(90, Color.Red);

        // 添加事件 - 用于通知文本块选择状态变化
        public event EventHandler<TextCellEventArgs> TextCellStateChanged;

        // 事件参数类
        public class TextCellEventArgs : EventArgs
        {
            public TextCellInfo Cell { get; private set; }
            public TextCellSelectionType SelectionType { get; private set; }

            public TextCellEventArgs(TextCellInfo cell, TextCellSelectionType type)
            {
                Cell = cell;
                SelectionType = type;
            }
        }

        // 选择类型枚举
        public enum TextCellSelectionType
        {
            None,       // 无选择
            Hover,      // 悬停
            Click       // 点击
        }

        public PanelPictureView()
        {
            Margin = Padding.Empty;
            AutoScroll = true;
            TabStop = false;
            AutoCenter = false;

            MouseMove += PanelPictureView_MouseMove;
            MouseClick += PanelPictureView_MouseClick;
            MouseWheel += PanelPictureView_MouseWheel;
            ZoomChanged += PanelPictureView_ZoomChanged;
            Scroll += PanelPictureView_Scroll;
        }

        private void PanelPictureView_Scroll(object sender, ScrollEventArgs e)
        {
            PanelPictureView_ZoomChanged(sender, null);
        }

        private void PanelPictureView_ZoomChanged(object sender, EventArgs e)
        {
            if (!drawRegion.IsEmpty)
            {
                Invalidate(drawRegion);
                drawRegion = Rectangle.Empty;
            }
        }

        /// <summary>
        /// 是否已经绑定过图片文字
        /// </summary>
        public bool IsBindImageMode { get; set; }
        public bool IsShowTip { get; set; }

        List<TextCellInfo> lstCells = new List<TextCellInfo>();

        private TextCellInfo CurrentCell;

        private Rectangle drawRegion;

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            if (IsBindImageMode && !drawRegion.IsEmpty)
            {
                using (var contentBrush = new SolidBrush(IMG_MODE_CLICK_COLOR))
                {
                    e.Graphics.FillRectangle(contentBrush, drawRegion);
                }
            }
        }

        private void PanelPictureView_MouseWheel(object sender, MouseEventArgs e)
        {
            PanelPictureView_MouseMove(sender, e);
        }

        private void PanelPictureView_MouseMove(object sender, MouseEventArgs e)
        {
            DrawCell(false, e.Button != MouseButtons.None || e.Delta != 0);
        }

        private void PanelPictureView_MouseClick(object sender, MouseEventArgs e)
        {
            CurrentCell = null;
            DrawCell(true);
        }

        private TextCellInfo GetCurrentCell()
        {
            var mouseLoc = PointToClient(Cursor.Position);
            mouseLoc = new Point(mouseLoc.X - AutoScrollPosition.X, mouseLoc.Y - AutoScrollPosition.Y);
            var cell = lstCells.Where(item => item.location != null && item.location.Rectangle.Zoom(ZoomFactor).Contains(mouseLoc)).FirstOrDefault();
            return cell;
        }

        private void DrawCell(bool isClick = false, bool isOnMove = false)
        {
            if (!IsBindImageMode)
            {
                return;
            }
            var cell = isOnMove ? null : GetCurrentCell();
            if (cell != null && Equals(CurrentCell, cell))
            {
                return;
            }
            if (!drawRegion.IsEmpty)
            {
                if (IsShowTip || cell != null)
                {
                    Invalidate(drawRegion);
                    drawRegion = Rectangle.Empty;
                }
            }

            if (cell != null)
            {
                var cellRect = cell.location.Rectangle.SizeOffset(2).Zoom(ZoomFactor);
                cellRect.Location = cellRect.Location.Add(AutoScrollPosition);
                drawRegion = new Rectangle(cellRect.Location, cellRect.Size);
                var tipLoc = new Point(Math.Max(0, drawRegion.Location.X), drawRegion.Location.Y + drawRegion.Height + 1);
                if (!ClientRectangle.Contains(tipLoc))
                {
                    tipLoc = new Point(Math.Max(ClientRectangle.X, tipLoc.X), Math.Min(ClientRectangle.Y, tipLoc.Y));
                }

                // 触发事件
                TextCellStateChanged?.Invoke(this, new TextCellEventArgs(
                    cell,
                    isClick ? TextCellSelectionType.Click : TextCellSelectionType.Hover
                ));

                if (isClick)
                {
                    Invalidate(drawRegion);

                    if (IsShowTip)
                        CommonMethod.ShowTxtToolTipContextMenu(this, cell.TipText, tipLoc);

                    if (CommonSetting.点击图片复制结果)
                    {
                        try
                        {
                            ClipboardService.SetText(cell.TipText);
                        }
                        catch { }
                    }
                }
                else
                {
                    using (var g = CreateGraphics())
                    {
                        using (var contentBrush = new SolidBrush(IMG_MODE_FOCUS_COLOR))
                        {
                            g.FillRectangle(contentBrush, drawRegion);
                        }
                    }

                    if (IsShowTip)
                        CommonMethod.ShowTxtToolTip(this, cell.TipText, tipLoc);
                }
            }
            else
            {
                // 触发无选择事件
                TextCellStateChanged?.Invoke(this, new TextCellEventArgs(null, TextCellSelectionType.None));

                CommonMethod.HideTxtToolTip(this);
                CommonMethod.HideTxtToolTipContextMenu();
            }
            CurrentCell = cell;
        }

        object objLock = "";

        protected override void OnMouseLeave(EventArgs e)
        {
            base.OnMouseLeave(e);

            // 鼠标离开时清除高亮并触发事件
            if (CurrentCell != null)
            {
                TextCellStateChanged?.Invoke(this, new TextCellEventArgs(null, TextCellSelectionType.None));
                CurrentCell = null;

                if (!drawRegion.IsEmpty)
                {
                    Invalidate(drawRegion);
                    drawRegion = Rectangle.Empty;
                }

                CommonMethod.HideTxtToolTip(this);
            }
        }

        public void BindPicTxt(UcContent content, bool isShowTxt = false)
        {
            lstCells = content.OcrContent?.result?.verticalText?.DeserializeJson<List<TextCellInfo>>() ?? new List<TextCellInfo>();

            // 清除当前高亮
            if (!drawRegion.IsEmpty)
            {
                Invalidate(drawRegion);
                drawRegion = Rectangle.Empty;
            }

            // 重置当前单元格
            CurrentCell = null;

            // 不立即处理图像，而是在需要时异步处理
            if (content.Image != null && lstCells.Count > 0)
            {
                // 异步处理图像绘制，避免UI阻塞
                Task.Run(() => ProcessImageAsync(content, isShowTxt));
            }
            else
            {
                // 如果没有处理需求，直接使用原始图像
                Image = content.Image;
            }

            // 标记已绑定图像模式
            IsBindImageMode = true;
            this.BringToFront();
        }

        /// <summary>
        /// 异步处理图像绘制
        /// </summary>
        private async Task ProcessImageAsync(UcContent content, bool isShowTxt)
        {
            try
            {
                var processedImage = await Task.Run(() => CreateProcessedImage(content, isShowTxt));

                // 切换到UI线程更新图像
                this.Invoke(new Action(() =>
                {
                    Image = processedImage;
                }));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ProcessImageAsync error: {ex.Message}");
                // 出错时使用原始图像
                this.Invoke(new Action(() =>
                {
                    Image = content.Image;
                }));
            }
        }

        /// <summary>
        /// 创建处理后的图像
        /// </summary>
        private Bitmap CreateProcessedImage(UcContent content, bool isShowTxt)
        {
            var image = new Bitmap(content.Image);

            // 批量预处理文本内容，避免重复调用
            PreprocessTextContent(content, lstCells);

            // 预处理所有绘制操作，减少锁竞争
            var drawingOperations = PrepareDrawingOperations(lstCells, isShowTxt);

            // 串行执行GDI+操作
            using (var g = Graphics.FromImage(image))
            {
                // 优化渲染质量设置，平衡性能和质量
                g.InterpolationMode = InterpolationMode.Bilinear;
                g.CompositingQuality = CompositingQuality.HighSpeed;
                g.SmoothingMode = SmoothingMode.HighSpeed;
                g.TextRenderingHint = TextRenderingHint.SystemDefault;

                using (var brush = new SolidBrush(CommonSetting.Get默认文字颜色()))
                using (var backgroundBrush = new SolidBrush(CommonSetting.Get默认背景颜色()))
                {
                    // 执行所有绘制操作
                    foreach (var operation in drawingOperations)
                    {
                        ExecuteDrawingOperation(g, operation, brush, backgroundBrush);
                    }
                }
            }

            return image;
        }

        /// <summary>
        /// 批量预处理文本内容
        /// </summary>
        private void PreprocessTextContent(UcContent content, List<TextCellInfo> cells)
        {
            // 并行处理文本内容，避免重复调用
            Parallel.ForEach(cells, item =>
            {
                if (!string.IsNullOrEmpty(item.words) || !string.IsNullOrEmpty(item.trans))
                {
                    item.TipText = content.GetTextByContent(item);
                }
            });
        }

        /// <summary>
        /// 绘制操作数据结构
        /// </summary>
        private class DrawingOperation
        {
            public Rectangle Rectangle { get; set; }
            public string Text { get; set; }
            public Font Font { get; set; }
            public bool DrawText { get; set; }
        }

        /// <summary>
        /// 准备绘制操作
        /// </summary>
        private List<DrawingOperation> PrepareDrawingOperations(List<TextCellInfo> cells, bool isShowTxt)
        {
            // 并行预处理绘制数据
            return cells.AsParallel()
                .Where(item => !(string.IsNullOrEmpty(item.words) && string.IsNullOrEmpty(item.trans)) && item.location != null)
                .Select(item => PrepareDrawingOperation(item, isShowTxt))
                .Where(op => op != null)
                .ToList();
        }

        /// <summary>
        /// 准备单个绘制操作
        /// </summary>
        private DrawingOperation PrepareDrawingOperation(TextCellInfo item, bool isShowTxt)
        {
            var rectangle = item.location.Rectangle;
            var operation = new DrawingOperation
            {
                Rectangle = rectangle,
                DrawText = false
            };

            if (isShowTxt && !string.IsNullOrEmpty(item.TipText))
            {
                operation.Text = item.TipText;
                operation.Font = CommonMethod.ScaleLabelByHeight(item.TipText, CommonString.GetUserNormalFont(5F), rectangle.Size);
                operation.DrawText = true;
            }

            return operation;
        }

        /// <summary>
        /// 执行绘制操作
        /// </summary>
        private void ExecuteDrawingOperation(Graphics g, DrawingOperation operation, SolidBrush brush, SolidBrush backgroundBrush)
        {
            // 绘制红色边框
            g.DrawRectangle(Pens.Red, operation.Rectangle.LocationOffset(-1, -1).SizeOffset(2));

            // 绘制文本（如果需要）
            if (operation.DrawText)
            {
                g.FillRectangle(backgroundBrush, operation.Rectangle);
                g.DrawString(operation.Text, operation.Font, brush, operation.Rectangle);
            }
        }
    }
}