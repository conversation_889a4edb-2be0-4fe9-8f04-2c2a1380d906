using System;
using System.IO;

namespace QiHe.CodeLib
{
	public class StreamHelper
	{
		public static byte[] ReadBytes(Stream stream, int count)
		{
			int num = Math.Min((int)stream.Length, count);
			byte[] array = new byte[num];
			stream.Read(array, 0, array.Length);
			return array;
		}

		public static byte[] ReadToEnd(Stream stream)
		{
			byte[] array = new byte[stream.Length - stream.Position];
			stream.Read(array, 0, array.Length);
			return array;
		}
	}
}
