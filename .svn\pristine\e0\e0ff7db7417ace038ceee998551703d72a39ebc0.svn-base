﻿using OCRTools.Common;
using System;
using System.Data;
using System.IO;
using System.Net;
using System.Threading;
using System.Windows.Forms;
using System.Xml;

namespace OCRTools
{
    public class CommonUpdate
    {
        private static Thread _updateMainThread;

        private static bool _isOnUpdate;
        public static bool isAlertUpdate;

        public static bool IsAutoCheckUpdate { get; set; } = true;

        public static void InitUpdate()
        {
            if (_updateMainThread != null)
                try
                {
                    _updateMainThread.Abort();
                    _updateMainThread = null;
                }
                catch
                {
                }

            try
            {
                InitUpdateService();
            }
            catch
            {
            }
        }

        private static void InitUpdateService()
        {
            var timerInfo = new TimerInfo
            {
                TimerType = "LoopHours",
                DateValue = (int)Math.Max(CommonSetting.自动更新间隔, 1),
                IsExecFirst = CommonSetting.启动时检查更新,
                Param = CommonString.UpdateFileUrl,
                DtNow = CommonString.DtNowDate
            };
            _updateMainThread = TimerTaskService.CreateTimerTaskService(timerInfo, UpdateMethod);
            _updateMainThread.Start();
        }

        public static bool CheckInstallLocalDectExe()
        {
            var result = File.Exists(CommonString.DefaultLocalDectExePath);
            if (!result)
            {
                var item = new ObjectTypeItem
                {
                    Name = "本地文档矫正服务",
                    AppPath = CommonString.DefaultLocalDectExePath,
                    Date = DateTime.MinValue,
                    Desc = "Deskew本地文档矫正服务，用于修复倾斜的扫描文档。\n原理：使用 Hough 变换来检测图像中的“文本行”及倾斜方向，\n再加上助手的智能化图像处理算法，来实现倾斜文档的平铺拉伸。",
                    NeedInstall = true,
                    UpdateUrl = CommonString.DectServiceUpdateFileUrl
                };
                CommonUpdate.Install(item.UpdateUrl, item.Date, item.Desc, item.Name, item.AppPath, false, false, true, false, null, true);
                result = File.Exists(CommonString.DefaultLocalDectExePath);
            }
            return result;
        }

        public static void InstallLocalOcrExe(bool isUserUpdate = false)
        {
            if (!isUserUpdate && !File.Exists(CommonString.DefaultLocalRecExePath))
            {
                return;
            }
            var item = new ObjectTypeItem
            {
                Name = "本地识别服务",
                AppPath = CommonString.DefaultLocalRecPath,
                UpdateUrl = CommonString.OcrServiceUpdateFileUrl,
                Date = CommonMethod.GetAssemblyDate(CommonString.DefaultLocalRecExePath)
            };
            var result = CommonUpdate.Install(item.UpdateUrl, item.Date, item.Desc, item.Name, item.AppPath, false, true, true, true, null, true);
            if (isUserUpdate && !result)
            {
                CommonMethod.ShowHelpMsg(item.Name + "已经是最新版本！");
            }
        }

        public static void InstallLocalOcrItem(ObjectTypeItem item)
        {
            CheckIfLocalOcrModuleInstall(ref item);

            var result = CommonUpdate.Install(item.UpdateUrl, item.Date, item.Desc, item.Name, item.AppPath, false, true, true, false, null, true);
            if (!result)
            {
                CommonMethod.ShowHelpMsg(item.Name + "已经是最新版本！");
            }
        }

        public static bool CheckIfLocalOcrModuleInstall(ref ObjectTypeItem item)
        {
            var modelPath = CommonString.DefaultLocalRecModelsPath + item.Name;
            if (Directory.Exists(modelPath))
            {
                var versionTxt = modelPath + "\\version.txt";
                try
                {
                    DateTime dtLast = DateTime.MinValue;
                    if (File.Exists(versionTxt))
                    {
                        DateTime.TryParse(File.ReadAllText(versionTxt), out dtLast);
                    }
                    item.Date = dtLast;
                    return true;
                }
                catch { }
            }

            return false;
        }

        public static bool Install(string url, DateTime dtNowDate, string desc, string appName, string appPath, bool isUserUpdateMode
            , bool isNeedUnZip, bool isAutoStart, bool isNeedClearFolder, FormUpdate updateForm, bool isDialog = false)
        {
            var result = false;
            var isBeta = false;
            UpdateEntity updateEntity = null;
            result = IsHasNew(url, dtNowDate, desc, ref updateEntity);
            //检查更新Beta版本
            if (!result && CommonSetting.体验Beta测试版)
            {
                var newUrl = CommonString.GetBetaUrl(url);
                UpdateEntity updateBetaEntity = null;
                result = IsHasNew(newUrl, dtNowDate, desc, ref updateBetaEntity);
                if (updateBetaEntity != null)
                {
                    if (result || Equals(updateBetaEntity.dtNewDate, dtNowDate))
                    {
                        isBeta = true;
                        updateEntity = updateBetaEntity;
                    }
                }
            }
            if (result && isUserUpdateMode)
                CommonMethod.ShowHelpMsg("发现新版本,更新前请关闭360等杀毒软件，或添加信任，避免被误杀！");
            if (result || isUserUpdateMode)
            {
                if (updateForm == null || updateForm.IsDisposed)
                {
                    updateForm = new FormUpdate
                    {
                        Icon = FrmMain.FrmTool.Icon,
                        UpdateInfo = updateEntity,
                        TopMost = true,
                        StartPosition = FormStartPosition.CenterScreen,
                        AppName = appName,
                        AppPath = appPath,
                        IsUpdateMode = isUserUpdateMode,
                        IsNeedUnZip = isNeedUnZip,
                        IsCanUserUpdate = isUserUpdateMode,
                        IsAutoStart = isAutoStart,
                        IsNeedClearFolder = isNeedClearFolder,
                        IsHasUpdate = result,
                        IsBeta = isBeta
                    };
                }
                CommonMethod.DetermineCall(FrmMain.FrmTool, delegate
                {
                    if (isDialog)
                    {
                        updateForm.ShowDialog();
                    }
                    else
                    {
                        updateForm.Show();
                    }
                });
            }

            return result;
        }

        public static void UpdateMain()
        {
            CommonUpdate.isAlertUpdate = true;
            CommonUpdate.UpdateMethod(true, CommonString.DtNowDate, CommonString.UpdateFileUrl);
        }

        private static bool UpdateMethod(bool isUserUpdate, DateTime dtDate, string url)
        {
            bool result = false;
            try
            {
                if (_isOnUpdate)
                {
                    if (isAlertUpdate) CommonMethod.ShowHelpMsg("正在检查更新中，请稍候重试！");
                    return result;
                }

                var isCheckUpdate = isUserUpdate || IsAutoCheckUpdate;
                if (isCheckUpdate)
                {
                    _isOnUpdate = true;
                    result = Install(url, dtDate, CommonString.FullName, CommonString.FullName, Application.ExecutablePath, isUserUpdate, false, false, false, FrmMain.FormUpdate);
                    if (!result)
                    {
                        if (isAlertUpdate)
                        {
                            isAlertUpdate = false;
                            CommonMethod.ShowHelpMsg(string.Format("当前版本:V{0}，已经是最新版本！", CommonString.StrNowVersion.Replace(".0", "")));
                        }
                        InstallLocalOcrExe();
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("检查更新错误", oe);
            }
            finally
            {
                _isOnUpdate = false;
            }
            return result;
        }

        public static bool IsHasNew(string url, DateTime dtNowDate, string desc, ref UpdateEntity updateNew)
        {
            var result = false;
            try
            {
                if (!string.IsNullOrEmpty(url))
                {
                    updateNew = LoadVersionInfo(url, desc);
                    if (updateNew != null)
                    {
                        result = dtNowDate < updateNew?.dtNewDate;
                        if (result && !updateNew.IsForceUpdate)
                        {
                            if (dtNowDate < updateNew.dtMinDate)
                            {
                                updateNew.IsForceUpdate = true;
                            }
                        }
                    }
                }
            }
            catch
            {
            }

            return result;
        }

        /// <summary>
        /// 将Xml内容字符串转换成DataSet对象
        /// </summary>
        /// <param name="xmlStr"></param>
        /// <returns></returns>
        private static DataSet CXmlToDataSet(string xmlStr)
        {
            if (string.IsNullOrEmpty(xmlStr)) return null;
            StringReader strStream = null;
            XmlTextReader xmlrdr = null;
            try
            {
                var ds = new DataSet();
                //读取字符串中的信息
                strStream = new StringReader(xmlStr);
                //获取StrStream中的数据
                xmlrdr = new XmlTextReader(strStream);
                //ds获取Xmlrdr中的数据
                ds.ReadXml(xmlrdr);
                return ds;
            }
            finally
            {
                //释放资源
                if (xmlrdr != null)
                {
                    xmlrdr.Close();
                    strStream.Close();
                    strStream.Dispose();
                }
            }
        }

        private static UpdateEntity LoadVersionInfo(string url, string desc)
        {
            UpdateEntity updateNew = null;

            if (url.Contains(".zip") || url.Contains(".exe"))
            {
                try
                {
                    DateTime newDate;
                    using (var client = new WebClient() { Proxy = null })
                    {
                        client.OpenRead(url);
                        newDate = DateTime.Parse(client.ResponseHeaders["Last-Modified"]);
                    }

                    updateNew = new UpdateEntity
                    {
                        strNewVersion = "*******",
                        dtNewDate = newDate.Date,
                        strContext = desc,
                        strURL = url,
                        strFullURL = url,
                        IsForceUpdate = false
                    };
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
            }
            else
            {
                var strUpdate = WebClientExt.GetHtml(url + "?t=" + DateTime.Now.Ticks, 10);
                try
                {
                    Console.WriteLine("url:" + url + "  result:" + strUpdate);
                    var dsTemp = CXmlToDataSet(strUpdate);
                    if (dsTemp != null)
                    {
                        var row = dsTemp.Tables.Count > 0 && dsTemp.Tables[0].Rows.Count > 0 ? dsTemp.Tables[0].Rows[0] : null;
                        if (row != null)
                            updateNew = new UpdateEntity
                            {
                                strNewVersion = row["strNowVersion"].ToString(),
                                dtNewDate = DateTime.Parse(row["dtNowDate"].ToString()),
                                strMinVersion = row["strMinVersion"]?.ToString(),
                                dtMinDate = DateTime.Parse(row["dtMinDate"]?.ToString()),
                                strContext = row["strContext"].ToString()
                                    .Replace("|", Environment.NewLine),
                                strURL = row["strURL"].ToString(),
                                strFullURL = row["strFullURL"].ToString(),
                                IsForceUpdate = BoxUtil.GetBooleanFromObject(row["IsNowForce"].ToString(), false)
                            };
                        dsTemp.Dispose();
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
            }

            return updateNew;
        }
    }

    public class UpdateEntity
    {
        public string strNewVersion { get; internal set; }
        public DateTime dtNewDate { get; internal set; }
        public string strMinVersion { get; internal set; }
        public DateTime dtMinDate { get; internal set; }
        public string strContext { get; internal set; }
        public string strURL { get; internal set; }
        public string strFullURL { get; internal set; }
        public bool IsForceUpdate { get; set; }
    }
}