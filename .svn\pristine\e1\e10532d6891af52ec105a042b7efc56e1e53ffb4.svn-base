using MetroFramework.Forms;
using OCRTools;
using OCRTools.Common;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

[DefaultProperty("Image")]
[ToolboxBitmap(typeof(ImageBox), "ImageBox.bmp")]
[ToolboxItem(true)]
public class ImageBox : VirtualScrollableControl
{
    public const int MaxZoom = 10000;

    public const int MinZoom = 10;

    public double ZoomIncrement { get; set; } = 1.2;

    private bool _autoCenter = true;

    private Cursor _currentCursor;

    private Timer _freePanTimer;

    private int _gridCellSize = 8;

    private Color _gridColor = Color.FromArgb(220, Color.Gainsboro);

    private Color _gridColorAlternate = Color.White;

    private ImageBoxGridDisplayMode _gridDisplayMode = ImageBoxGridDisplayMode.马赛克;

    private ImageBoxGridScale _gridScale = ImageBoxGridScale.Small;

    private Image _image;

    private Image _originImage;

    private InterpolationMode _interpolationMode = InterpolationMode.NearestNeighbor;

    private bool _invertMouse;

    private double _mouseDownStart;

    private ImageBoxPanMode _panMode = ImageBoxPanMode.Both;

    private ImageBoxPanStyle _panStyle;

    private Color _pixelGridColor = Color.DimGray;

    private int _pixelGridThreshold = 5;

    private bool _showPixelGrid;

    private ImageBoxSizeMode _sizeMode = ImageBoxSizeMode.Normal;

    private Point _startMousePosition;

    private Point _startScrollPosition;

    private Brush _texture;

    private int _updateCount;

    private int _zoom;

    // 添加缓存相关字段
    private InterpolationMode _lastInterpolationMode;
    private float _lastZoomFactor;
    private Rectangle _lastViewRectangle;
    private bool _isDirty = true;

    // 添加脏区域管理相关字段
    private Region _dirtyRegion;

    [Category("Appearance")]
    public virtual bool AutoCenter
    {
        get
        {
            return _autoCenter;
        }
        set
        {
            if (_autoCenter != value)
            {
                _autoCenter = value;
                Invalidate();
            }
        }
    }

    [DefaultValue(true)]
    [Category("Behavior")]
    public virtual bool AutoPan
    {
        get
        {
            return (_panMode & ImageBoxPanMode.Left) != 0;
        }
        set
        {
            if (AutoPan != value)
            {
                PanMode = (value ? (_panMode & ImageBoxPanMode.Left) : (_panMode & ~ImageBoxPanMode.Left));
            }
        }
    }

    [DefaultValue(true)]
    public override bool AutoScroll
    {
        get
        {
            return base.AutoScroll;
        }
        set
        {
            base.AutoScroll = value;
        }
    }

    [Browsable(false)]
    [EditorBrowsable(EditorBrowsableState.Never)]
    [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
    public new Size AutoScrollMinSize
    {
        get
        {
            return base.AutoScrollMinSize;
        }
        set
        {
            base.AutoScrollMinSize = value;
        }
    }

    [Browsable(true)]
    [EditorBrowsable(EditorBrowsableState.Always)]
    [DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
    [DefaultValue(false)]
    public override bool AutoSize
    {
        get
        {
            return base.AutoSize;
        }
        set
        {
            if (base.AutoSize != value)
            {
                base.AutoSize = value;
                AdjustLayout();
            }
        }
    }

    public override Color BackColor
    {
        get
        {
            return base.BackColor;
        }
        set
        {
            base.BackColor = Color.FromArgb(255, value.R, value.G, value.B);
        }
    }

    [Browsable(false)]
    public Point CenterPoint
    {
        get
        {
            Rectangle imageViewPort = GetImageViewPort();
            return new Point(imageViewPort.Width / 2, imageViewPort.Height / 2);
        }
    }

    [Browsable(false)]
    [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
    public override Cursor Cursor
    {
        get
        {
            return base.Cursor;
        }
        set
        {
            base.Cursor = value;
        }
    }

    [Category("Appearance")]
    public virtual int GridCellSize
    {
        get
        {
            return _gridCellSize;
        }
        set
        {
            if (_gridCellSize != value)
            {
                _gridCellSize = value;
                InitBackGround();
            }
        }
    }

    [Category("Appearance")]
    public virtual Color GridColor
    {
        get
        {
            return _gridColor;
        }
        set
        {
            if (_gridColor != value)
            {
                _gridColor = value;
            }
        }
    }

    [Category("Appearance")]
    public virtual Color GridColorAlternate
    {
        get
        {
            return _gridColorAlternate;
        }
        set
        {
            if (_gridColorAlternate != value)
            {
                _gridColorAlternate = value;
                InitBackGround();
            }
        }
    }

    [Category("Appearance")]
    public virtual ImageBoxGridDisplayMode GridDisplayMode
    {
        get
        {
            return _gridDisplayMode;
        }
        set
        {
            if (_gridDisplayMode != value)
            {
                _gridDisplayMode = value;
            }
        }
    }

    [Category("Appearance")]
    public virtual ImageBoxGridScale GridScale
    {
        get
        {
            return _gridScale;
        }
        set
        {
            if (_gridScale != value)
            {
                _gridScale = value;
                InitBackGround();
            }
        }
    }

    [Category("Appearance")]
    public virtual Image Image
    {
        get
        {
            return _image;
        }
        set
        {
            if (_image != value)
            {
                try
                {
                    // 保存旧图像引用，但先不释放
                    Image oldImage = _image;
                    
                    // 如果新图像不为空，注册到资源管理器
                    if (value != null)
                    {
                        OCRTools.Common.ImageResourceManager.RegisterImage(value);
                    }
                    
                    // 设置新图像
                    _image = value;
                    
                    // 标记控件为脏，触发重新布局
                    _isDirty = true;
                    OnImageChanged(EventArgs.Empty);
                    
                    // 安全释放旧图像资源
                    if (oldImage != null && oldImage != _originImage && oldImage != value)
                    {
                        SafeDisposeImage(oldImage);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Setting Image property error: {ex.Message}");
                }
            }
        }
    }

    [Category("Appearance")]
    public virtual Image OriginImage
    {
        get => _originImage;
        set
        {
            if (_originImage != value)
            {
                try
                {
                    // 保存旧图像引用，但先不释放
                    Image oldOriginImage = _originImage;
                    
                    // 如果新图像不为空，注册到资源管理器
                    if (value != null)
                    {
                        OCRTools.Common.ImageResourceManager.RegisterImage(value);
                    }
                    
                    // 设置新图像
                    _originImage = value;
                    
                    // 更新显示图像
                    Image = _originImage;
                    
                    // 安全释放旧图像资源
                    if (oldOriginImage != null && oldOriginImage != value && oldOriginImage != _image)
                    {
                        SafeDisposeImage(oldOriginImage);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Setting OriginImage property error: {ex.Message}");
                }
            }
        }
    }

    [Category("Appearance")]
    public virtual InterpolationMode InterpolationMode
    {
        get
        {
            return _interpolationMode;
        }
        set
        {
            if (value == InterpolationMode.Invalid)
            {
                value = InterpolationMode.Default;
            }
            if (_interpolationMode != value)
            {
                _interpolationMode = value;
                Invalidate();
            }
        }
    }

    [Category("Behavior")]
    public virtual bool InvertMouse
    {
        get
        {
            return _invertMouse;
        }
        set
        {
            if (_invertMouse != value)
            {
                _invertMouse = value;
            }
        }
    }

    [Browsable(false)]
    public virtual bool IsActualSize => Zoom == 100;

    [Category("Behavior")]
    public virtual ImageBoxPanMode PanMode
    {
        get
        {
            return _panMode;
        }
        set
        {
            if (_panMode != value)
            {
                _panMode = value;
            }
        }
    }

    [Category("Appearance")]
    public virtual Color PixelGridColor
    {
        get
        {
            return _pixelGridColor;
        }
        set
        {
            if (PixelGridColor != value)
            {
                _pixelGridColor = value;
                Invalidate();
            }
        }
    }

    [Category("Behavior")]
    public virtual int PixelGridThreshold
    {
        get
        {
            return _pixelGridThreshold;
        }
        set
        {
            if (PixelGridThreshold != value)
            {
                _pixelGridThreshold = value;
            }
        }
    }

    [Category("Appearance")]
    public virtual bool ShowPixelGrid
    {
        get
        {
            return _showPixelGrid;
        }
        set
        {
            if (ShowPixelGrid != value)
            {
                _showPixelGrid = value;
                Invalidate();
            }
        }
    }

    [Category("Behavior")]
    public virtual ImageBoxSizeMode SizeMode
    {
        get
        {
            return _sizeMode;
        }
        set
        {
            if (SizeMode != value)
            {
                _sizeMode = value;
                AdjustLayout();
            }
        }
    }

    [Category("Appearance")]
    public virtual int Zoom
    {
        get
        {
            return _zoom;
        }
        set
        {
            SetZoom(value, (value > Zoom) ? ImageBoxZoomActions.ZoomIn : ImageBoxZoomActions.ZoomOut);
        }
    }

    [Browsable(false)]
    [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
    public virtual double ZoomFactor => Zoom / 100.0;

    protected virtual bool AllowPainting => _updateCount == 0;

    protected virtual int ScaledImageHeight => Convert.ToInt32(ViewSize.Height * ZoomFactor);

    protected virtual int ScaledImageWidth => Convert.ToInt32(ViewSize.Width * ZoomFactor);

    protected virtual Size ViewSize
    {
        get
        {
            return GetImageSize();
        }
    }

    public event EventHandler BeforeZoomChanged;

    public event EventHandler ZoomChanged;

    public ImageBox()
    {
        SetStyle(ControlStyles.OptimizedDoubleBuffer |
            ControlStyles.ResizeRedraw |
                 ControlStyles.DoubleBuffer |
                 ControlStyles.UserPaint, true);
        SetStyle(ControlStyles.StandardDoubleClick, value: true);
        BeginUpdate();
        BackColor = Color.White;
        ActualSize();
        DoubleClick += ImageBox_DoubleClick;
        KeyUp += ImageBox_KeyUp;
        EndUpdate();
    }

    public bool IsCanMax { get; set; } = true;

    private void ImageBox_DoubleClick(object sender, EventArgs e)
    {
        SwitchMaxWindow();
    }

    public void SwitchMaxWindow()
    {
        if (!IsCanMax)
            return;
        var form = FindForm();
        if (form == null)
        {
            return;
        }

        if (!(form is MetroForm))
        {
            if (form.FormBorderStyle == FormBorderStyle.None)
            {
                form.FormBorderStyle = FormBorderStyle.Sizable;
            }
        }
        form.WindowState = form.WindowState == FormWindowState.Normal
            ? FormWindowState.Maximized
            : FormWindowState.Normal;
        if (!(form is MetroForm))
        {
            if (form.WindowState == FormWindowState.Maximized)
            {
                form.FormBorderStyle = FormBorderStyle.None;
            }
        }
    }

    private void ImageBox_KeyUp(object sender, KeyEventArgs e)
    {
        var form = FindForm();
        if (form == null)
        {
            return;
        }

        if (e.KeyCode == Keys.Escape)
        {
            if (form is FrmMain)
            {
                if (form.WindowState == FormWindowState.Maximized)
                {
                    SwitchMaxWindow();
                }
            }
            else
            {
                form.Close();
            }
        }
    }

    public virtual void ActualSize()
    {
        PerformActualSize();
    }

    public virtual void BeginUpdate()
    {
        _updateCount++;
    }

    public virtual void EndUpdate()
    {
        if (_updateCount > 0)
        {
            _updateCount--;
        }
        if (AllowPainting)
        {
            Invalidate();
        }
    }

    public virtual Rectangle GetImageViewPort()
    {
        if (!ViewSize.IsEmpty)
        {
            Rectangle insideViewPort = GetInsideViewPort(includePadding: true);
            Point point;
            int num3;
            int num4;
            if (SizeMode != ImageBoxSizeMode.Stretch)
            {
                if (AutoCenter)
                {
                    int num = ((!base.HScroll) ? ((insideViewPort.Width - (ScaledImageWidth + base.Padding.Horizontal)) / 2) : 0);
                    int num2 = ((!base.VScroll) ? ((insideViewPort.Height - (ScaledImageHeight + base.Padding.Vertical)) / 2) : 0);
                    point = new Point(num, num2);
                }
                else
                {
                    point = Point.Empty;
                }
                // 确保宽高不会小于1
                num3 = Math.Max(1, Math.Min(ScaledImageWidth - Math.Abs(AutoScrollPosition.X), insideViewPort.Width));
                num4 = Math.Max(1, Math.Min(ScaledImageHeight - Math.Abs(AutoScrollPosition.Y), insideViewPort.Height));
            }
            else
            {
                point = Point.Empty;
                // 确保宽高不会小于1
                num3 = Math.Max(1, insideViewPort.Width);
                num4 = Math.Max(1, insideViewPort.Height);
            }
            return new Rectangle(point.X + insideViewPort.Left, point.Y + insideViewPort.Top, num3, num4);
        }
        return Rectangle.Empty;
    }

    public Rectangle GetInsideViewPort()
    {
        return GetInsideViewPort(includePadding: false);
    }

    private int GetBorderOffset()
    {
        int offset;

        switch (BorderStyle)
        {
            case BorderStyle.Fixed3D:
                offset = 2;
                break;
            case BorderStyle.FixedSingle:
                offset = 1;
                break;
            default:
                offset = 0;
                break;
        }

        return offset;
    }

    public virtual Rectangle GetInsideViewPort(bool includePadding)
    {
        var borderOffset = GetBorderOffset();
        var left = borderOffset;
        var top = borderOffset;
        var width = ClientSize.Width - borderOffset * 2;
        var height = ClientSize.Height - borderOffset * 2;

        if (includePadding)
        {
            left += Padding.Left;
            top += Padding.Top;
            width -= Padding.Horizontal;
            height -= Padding.Vertical;
        }

        return new Rectangle(left, top, width, height);
    }

    public override Size GetPreferredSize(Size proposedSize)
    {
        if (!ViewSize.IsEmpty)
        {
            int scaledImageWidth = ScaledImageWidth;
            int scaledImageHeight = ScaledImageHeight;
            scaledImageWidth += base.Padding.Horizontal;
            scaledImageHeight += base.Padding.Vertical;
            return new Size(scaledImageWidth, scaledImageHeight);
        }
        return base.GetPreferredSize(proposedSize);
    }

    public virtual RectangleF GetSourceImageRegion()
    {
        if (!ViewSize.IsEmpty)
        {
            RectangleF result;
            if (SizeMode != ImageBoxSizeMode.Stretch)
            {
                Rectangle imageViewPort = GetImageViewPort();
                
                // 防止除零错误
                double zoomFactor = ZoomFactor;
                if (zoomFactor <= 0.001) zoomFactor = 0.001;
                
                float num = (float)(-AutoScrollPosition.X / zoomFactor);
                float num2 = (float)(-AutoScrollPosition.Y / zoomFactor);
                float num3 = (float)(imageViewPort.Width / zoomFactor);
                float num4 = (float)(imageViewPort.Height / zoomFactor);
                
                // 确保宽高不会小于0.1
                num3 = Math.Max(0.1f, num3);
                num4 = Math.Max(0.1f, num4);
                
                result = new RectangleF(num, num2, num3, num4);
            }
            else
            {
                // 确保视图大小有效
                Size viewSizeValidated = new Size(
                    Math.Max(1, ViewSize.Width),
                    Math.Max(1, ViewSize.Height)
                );
                result = new RectangleF(PointF.Empty, viewSizeValidated);
            }
            return result;
        }
        return RectangleF.Empty;
    }

    public Point PointToImage(Point point)
    {
        return PointToImage(point, fitToBounds: false);
    }

    public virtual Point PointToImage(Point point, bool fitToBounds)
    {
        Rectangle imageViewPort = GetImageViewPort();
        int num;
        int num2;
        if (!fitToBounds || imageViewPort.Contains(point))
        {
            if (AutoScrollPosition != Point.Empty)
            {
                point = new Point(point.X - AutoScrollPosition.X, point.Y - AutoScrollPosition.Y);
            }
            num = (int)((point.X - imageViewPort.X) / ZoomFactor);
            num2 = (int)((point.Y - imageViewPort.Y) / ZoomFactor);
            if (fitToBounds)
            {
                if (num < 0)
                {
                    num = 0;
                }
                else if (num > ViewSize.Width)
                {
                    num = ViewSize.Width;
                }
                if (num2 < 0)
                {
                    num2 = 0;
                }
                else if (num2 > ViewSize.Height)
                {
                    num2 = ViewSize.Height;
                }
            }
        }
        else
        {
            num = 0;
            num2 = 0;
        }
        return new Point(num, num2);
    }

    public virtual void ScrollTo(Point imageLocation, Point relativeDisplayPoint)
    {
        int num = (int)(imageLocation.X * ZoomFactor) - relativeDisplayPoint.X;
        int num2 = (int)(imageLocation.Y * ZoomFactor) - relativeDisplayPoint.Y;
        AutoScrollPosition = new Point(num, num2);
    }

    public virtual void ZoomIn(bool preservePosition)
    {
        PerformZoom(ImageBoxZoomActions.ZoomIn, preservePosition, CenterPoint);
    }

    public virtual void ZoomOut(bool preservePosition)
    {
        PerformZoom(ImageBoxZoomActions.ZoomOut, preservePosition, CenterPoint);
    }

    public virtual void SetImage(Image image, bool isAutoFit)
    {
        if (image != null)
        {
            try
            {
                // 先记录旧图像引用
                Image oldOriginImage = _originImage;
                Image oldImage = _image;
                
                // 清除引用，但暂不释放资源
                _originImage = null;
                _image = null;
                
                // 如果新图像不为空，注册到资源管理器
                OCRTools.Common.ImageResourceManager.RegisterImage(image);
                
                // 设置新图像
                Image = image;
                
                if (isAutoFit)
                {
                    Zoom = GetFitZoom();
                }
                else
                {
                    Zoom = 100;
                }
                
                // 触发布局调整
                OnImageChanged(EventArgs.Empty);
                
                // 安全释放旧资源
                if (oldOriginImage != null && oldOriginImage != image)
                {
                    SafeDisposeImage(oldOriginImage);
                }
                
                if (oldImage != null && oldImage != image && oldImage != oldOriginImage)
                {
                    SafeDisposeImage(oldImage);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"SetImage error: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 图片缩放策略
    /// </summary>
    public enum ImageScalingStrategy
    {
        /// <summary>
        /// 完整显示 - 优先显示图片全貌，尽量避免滚动
        /// 适用场景：快速预览、了解图片整体内容
        /// </summary>
        FitComplete,

        /// <summary>
        /// 清晰显示 - 优先显示清晰度，允许滚动查看
        /// 适用场景：查看图片细节、大窗口预览
        /// </summary>
        FitClear,

        /// <summary>
        /// 操作优化 - 平衡完整性和操作精度，针对交互操作优化
        /// 适用场景：OCR文字选择、图片标注等需要精确操作的场景
        /// </summary>
        FitInteractive
    }

    /// <summary>
    /// 图片缩放策略
    /// </summary>
    public ImageScalingStrategy ScalingStrategy { get; set; } = ImageScalingStrategy.FitComplete;

    public int GetFitZoom(bool isLimitMax = false)
    {
        AutoScrollMinSize = Size.Empty;

        // 检查图像是否为null
        if (Image == null || Parent == null)
        {
            return MinZoom;
        }

        var containerSize = this.Parent.ClientSize;

        // 确保图像尺寸有效，避免除零错误
        if (Image.Width <= 0 || Image.Height <= 0 || containerSize.Width <= 0 || containerSize.Height <= 0)
        {
            return MinZoom;
        }

        // 根据缩放策略计算缩放比例
        double zoom;
        switch (ScalingStrategy)
        {
            case ImageScalingStrategy.FitComplete:
                zoom = CalculateFitComplete(containerSize, isLimitMax);
                break;
            case ImageScalingStrategy.FitClear:
                zoom = CalculateFitClear(containerSize, isLimitMax);
                break;
            case ImageScalingStrategy.FitInteractive:
                zoom = CalculateFitInteractive(containerSize, isLimitMax);
                break;
            default:
                zoom = CalculateFitComplete(containerSize, isLimitMax); // 默认策略
                break;
        }

        return Math.Max(MinZoom, (int)zoom);
    }

    /// <summary>
    /// 计算完整显示策略的缩放比例
    /// </summary>
    private double CalculateFitComplete(Size containerSize, bool isLimitMax)
    {
        // 完整适应：取宽高缩放比的最小值，确保图片完全显示
        double widthScale = containerSize.Width / (double)Image.Width * 100.0;
        double heightScale = containerSize.Height / (double)Image.Height * 100.0;
        double zoom = Math.Min(widthScale, heightScale);

        return ApplyZoomLimits(zoom, isLimitMax);
    }

    /// <summary>
    /// 计算清晰显示策略的缩放比例
    /// </summary>
    private double CalculateFitClear(Size containerSize, bool isLimitMax)
    {
        // 填充适应：取宽高缩放比的最大值，优先清晰度
        double widthScale = containerSize.Width / (double)Image.Width * 100.0;
        double heightScale = containerSize.Height / (double)Image.Height * 100.0;
        double zoom = Math.Max(widthScale, heightScale);

        return ApplyZoomLimits(zoom, isLimitMax);
    }

    /// <summary>
    /// 计算交互操作优化策略的缩放比例
    /// </summary>
    private double CalculateFitInteractive(Size containerSize, bool isLimitMax)
    {
        // 分析窗口和图片特征
        var windowArea = containerSize.Width * containerSize.Height;
        var imageArea = Image.Width * Image.Height;

        // 计算基础缩放比例
        double widthScale = containerSize.Width / (double)Image.Width * 100.0;
        double heightScale = containerSize.Height / (double)Image.Height * 100.0;
        double fitCompleteZoom = Math.Min(widthScale, heightScale);

        // 根据窗口大小设置最小缩放阈值（针对OCR操作优化）
        double minZoom;
        if (windowArea < 200000)  // 约447x447，极小窗口
        {
            minZoom = 75;  // 极小窗口：最小75%，保证基本可操作性
        }
        else if (windowArea < 500000)  // 约707x707，小窗口
        {
            minZoom = 85;  // 小窗口：最小85%，提升操作舒适度
        }
        else if (windowArea < 1000000)  // 约1000x1000，中窗口
        {
            minZoom = 90;  // 中窗口：最小90%，更好的操作精度
        }
        else
        {
            minZoom = 100;  // 大窗口：最小100%，追求最佳操作体验
        }

        double finalZoom;

        // 如果完整适应的缩放足够大，就用完整适应
        if (fitCompleteZoom >= minZoom)
        {
            finalZoom = ApplyZoomLimits(fitCompleteZoom, isLimitMax);
        }
        else
        {
            // 否则选择更好的单方向适应，但要保证最小缩放
            double betterZoom = Math.Max(widthScale, heightScale);
            double adjustedZoom = Math.Max(betterZoom, minZoom);
            finalZoom = ApplyZoomLimits(adjustedZoom, isLimitMax);
        }

        return finalZoom;
    }

    /// <summary>
    /// 应用缩放限制
    /// </summary>
    private double ApplyZoomLimits(double zoom, bool isLimitMax)
    {
        double originalZoom = zoom;

        if (isLimitMax)
        {
            zoom = Math.Min(100, zoom);
        }
        else
        {
            if (zoom > 100)
            {
                // 超过100%时进行衰减
                zoom = 100 + (zoom - 100) * 0.75;
            }
        }

        return zoom;
    }

    public virtual void ZoomToFit(bool isLimitMax = false)
    {
        if (Image != null)
        {
            Zoom = GetFitZoom(isLimitMax);
        }
    }

    public virtual void AdjustLayout()
    {
        if (AllowPainting)
        {
            if (AutoSize)
            {
                AdjustSize();
            }
            else if (SizeMode != 0)
            {
                ZoomToFit();
            }
            else if (AutoScroll)
            {
                AdjustViewPort();
            }
            Invalidate();
        }
    }

    protected virtual void AdjustScroll(int x, int y)
    {
        Point position = new Point(base.HorizontalScroll.Value + x, base.VerticalScroll.Value + y);
        UpdateScrollPosition(position);
    }

    protected virtual void AdjustSize()
    {
        if (AutoSize && Dock == DockStyle.None)
        {
            base.Size = base.PreferredSize;
        }
    }

    protected virtual void AdjustViewPort()
    {
        if (AutoScroll && !ViewSize.IsEmpty)
        {
            AutoScrollMinSize = new Size(ScaledImageWidth + base.Padding.Horizontal, ScaledImageHeight + base.Padding.Vertical);
            base.OnResize(null);
        }
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            // 释放纹理资源
            if (_texture != null)
            {
                _texture.Dispose();
                _texture = null;
            }
            
            // 释放计时器资源
            KillTimer();
            
            // 释放图像资源
            if (_image != null && _image != _originImage)
            {
                SafeDisposeImage(_image);
                _image = null;
            }
            
            if (_originImage != null)
            {
                SafeDisposeImage(_originImage);
                _originImage = null;
            }
            
            // 释放脏区域资源
            if (_dirtyRegion != null)
            {
                _dirtyRegion.Dispose();
                _dirtyRegion = null;
            }
        }
        base.Dispose(disposing);
    }

    protected virtual void DrawBackground(PaintEventArgs e)
    {
        Rectangle insideViewPort = GetInsideViewPort();
        if (_texture != null)
        {
            try
            {
                e.Graphics.FillRectangle(_texture, insideViewPort);
            }
            catch
            {
                using (SolidBrush brush = new SolidBrush(BackColor))
                {
                    e.Graphics.FillRectangle(brush, insideViewPort);
                }
            }
        }
        else
        {
            using (SolidBrush brush = new SolidBrush(BackColor))
            {
                e.Graphics.FillRectangle(brush, insideViewPort);
            }
        }
    }

    protected virtual void DrawImage(Graphics g)
    {
        if (Image == null) return;
        
        // 保存原始图形状态
        InterpolationMode originalInterpolationMode = g.InterpolationMode;
        PixelOffsetMode originalPixelOffsetMode = g.PixelOffsetMode;
        
        try
        {
            // 检查是否需要更新缓存
            bool isZoomFactorChanged = _lastZoomFactor != ZoomFactor;
            
            try
            {
                // 获取视图区域和源区域
                var viewRectangle = GetImageViewPort();
                var sourceRegion = GetSourceImageRegion();
                
                // 额外检查确保区域有效
                if (viewRectangle.Width <= 0 || viewRectangle.Height <= 0 || 
                    sourceRegion.Width <= 0 || sourceRegion.Height <= 0 ||
                    float.IsNaN(sourceRegion.X) || float.IsNaN(sourceRegion.Y) ||
                    float.IsNaN(sourceRegion.Width) || float.IsNaN(sourceRegion.Height))
                {
                    return;
                }
                
                // 确保源区域不超出图像边界
                if (Image != null)
                {
                    float maxWidth = Image.Width;
                    float maxHeight = Image.Height;
                    
                    // 如果源区域起点超出图像边界，进行调整
                    if (sourceRegion.X > maxWidth || sourceRegion.Y > maxHeight)
                    {
                        return; // 源区域完全在图像外部，不需要绘制
                    }
                    
                    // 调整源区域，确保不超出图像边界
                    RectangleF adjustedSourceRegion = new RectangleF(
                        sourceRegion.X,
                        sourceRegion.Y,
                        Math.Min(sourceRegion.Width, maxWidth - sourceRegion.X),
                        Math.Min(sourceRegion.Height, maxHeight - sourceRegion.Y)
                    );
                    
                    // 如果调整后的区域无效，则不进行绘制
                    if (adjustedSourceRegion.Width <= 0 || adjustedSourceRegion.Height <= 0)
                    {
                        return;
                    }
                    
                    sourceRegion = adjustedSourceRegion;
                }
                
                // 如果视图区域发生变化，标记为脏
                if (_lastViewRectangle != viewRectangle)
                {
                    _isDirty = true;
                    _lastViewRectangle = viewRectangle;
                }
                
                // 智能设置插值模式
                InterpolationMode currentMode = GetOptimizedInterpolationMode();
                
                // 仅在需要时切换插值模式，避免不必要的状态切换
                if (_isDirty || isZoomFactorChanged || currentMode != _lastInterpolationMode)
                {
                    g.InterpolationMode = currentMode;
                    g.PixelOffsetMode = GetOptimizedPixelOffsetMode(currentMode);
                    
                    // 更新缓存
                    _lastInterpolationMode = currentMode;
                    _lastZoomFactor = (float)ZoomFactor;
                    _isDirty = false;
                }

                // 执行绘制
                g.DrawImage(Image, viewRectangle, sourceRegion, GraphicsUnit.Pixel);
            }
            catch (ArgumentException argEx)
            {
                // 捕获参数异常并记录
                Console.WriteLine($"DrawImage ArgumentException: {argEx.Message}");
                Console.WriteLine($"ZoomFactor: {ZoomFactor}, Image Size: {(Image != null ? Image.Size.ToString() : "null")}");
                
                // 尝试使用更简单的绘制方式
                try
                {
                    Rectangle clientRect = GetInsideViewPort(true);
                    if (Image != null && clientRect.Width > 0 && clientRect.Height > 0)
                    {
                        g.DrawImage(Image, clientRect);
                    }
                }
                catch (Exception fallbackEx)
                {
                    // 如果简单绘制也失败，记录错误并显示错误信息
                    Console.WriteLine($"Fallback drawing failed: {fallbackEx.Message}");
                    TextRenderer.DrawText(g, "绘制失败: " + argEx.Message, Font, ClientRectangle, ForeColor, BackColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.NoPrefix |
                        TextFormatFlags.VerticalCenter | TextFormatFlags.WordBreak |
                        TextFormatFlags.NoPadding);
                }
            }
            catch (Exception ex)
            {
                // 绘制错误信息
                Console.WriteLine($"DrawImage Exception: {ex.Message}");
                TextRenderer.DrawText(g, "绘制错误: " + ex.Message, Font, ClientRectangle, ForeColor, BackColor,
                    TextFormatFlags.HorizontalCenter | TextFormatFlags.NoPrefix |
                    TextFormatFlags.VerticalCenter | TextFormatFlags.WordBreak |
                    TextFormatFlags.NoPadding);
            }
        }
        finally
        {
            // 恢复原始图形状态
            try
            {
                g.PixelOffsetMode = originalPixelOffsetMode;
                g.InterpolationMode = originalInterpolationMode;
            }
            catch { /* 忽略恢复状态时的异常 */ }
        }
    }

    /// <summary>
    /// 获取优化的像素偏移模式，与插值模式匹配
    /// </summary>
    protected virtual PixelOffsetMode GetOptimizedPixelOffsetMode(InterpolationMode mode)
    {
        // 根据插值模式选择合适的像素偏移模式
        if (mode == InterpolationMode.NearestNeighbor)
            return PixelOffsetMode.Half; // 保持像素对齐
        else
            return PixelOffsetMode.HighQuality; // 高质量模式
    }

    /// <summary>
    /// 获取优化的插值模式
    /// </summary>
    protected virtual InterpolationMode GetOptimizedInterpolationMode()
    {
        // 如果已指定插值模式且不是默认值，则使用指定的模式
        if (_interpolationMode != InterpolationMode.Default && _interpolationMode != InterpolationMode.Invalid)
            return _interpolationMode;
            
        // 基于缩放比例智能选择插值模式
        double zoomFactor = ZoomFactor;
        
        if (zoomFactor >= 3.0)
        {
            // 放大3倍以上使用NearestNeighbor以保持像素清晰
            return InterpolationMode.NearestNeighbor;
        }
        else if (zoomFactor > 1.0 && zoomFactor < 3.0)
        {
            // 中等放大使用双线性，平衡质量和性能
            return InterpolationMode.Bilinear;
        }
        else if (zoomFactor == 1.0)
        {
            // 实际尺寸使用NearestNeighbor以避免模糊
            return InterpolationMode.NearestNeighbor;
        }
        else if (zoomFactor >= 0.5 && zoomFactor < 1.0)
        {
            // 轻微缩小使用高质量双三次
            return InterpolationMode.HighQualityBicubic;
        }
        else
        {
            // 大幅缩小使用双线性，减少摩尔纹
            return InterpolationMode.Bilinear;
        }
    }

    protected virtual InterpolationMode GetInterpolationMode()
    {
        // 保留此方法以向后兼容，但实现更优化的逻辑
        return GetOptimizedInterpolationMode();
    }
    
    public override void Refresh()
    {
        // 标记为脏以确保重新计算绘制参数
        _isDirty = true;
        base.Refresh();
    }

    protected virtual void OnImageChanged(EventArgs e)
    {
        // 图像改变时标记为脏
        _isDirty = true;
        AdjustLayout();
    }

    protected virtual void DrawPixelGrid(Graphics g)
    {
        float num = (float)ZoomFactor;
        if (!(num > PixelGridThreshold))
        {
            return;
        }
        Rectangle imageViewPort = GetImageViewPort();
        float num2 = Math.Abs(AutoScrollPosition.X) % num;
        float num3 = Math.Abs(AutoScrollPosition.Y) % num;
        using (Pen pen = new Pen(PixelGridColor))
        {
            pen.DashStyle = DashStyle.Dot;
            for (float num4 = imageViewPort.Left + num - num2; num4 < imageViewPort.Right; num4 += num)
            {
                g.DrawLine(pen, num4, imageViewPort.Top, num4, imageViewPort.Bottom);
            }
            for (float num5 = imageViewPort.Top + num - num3; num5 < imageViewPort.Bottom; num5 += num)
            {
                g.DrawLine(pen, imageViewPort.Left, num5, imageViewPort.Right, num5);
            }
            g.DrawRectangle(pen, imageViewPort);
        }
    }

    protected virtual Cursor GetCursor(Point location)
    {
        var cursor = Cursors.Default;
        switch (_panStyle)
        {
            case ImageBoxPanStyle.Standard:
                cursor = Cursors.SizeAll;
                break;
            case ImageBoxPanStyle.Free:
                switch (GetPanDirection(location))
                {
                    case ImageBoxPanDirection.None:
                        cursor = Cursors.SizeAll;
                        break;
                    case ImageBoxPanDirection.Up:
                        cursor = Cursors.PanNorth;
                        break;
                    case ImageBoxPanDirection.Down:
                        cursor = Cursors.PanSouth;
                        break;
                    case ImageBoxPanDirection.Left:
                        cursor = Cursors.PanWest;
                        break;
                    case ImageBoxPanDirection.Right:
                        cursor = Cursors.PanEast;
                        break;
                }
                break;
        }
        return cursor;
    }

    protected override bool IsInputKey(Keys keyData)
    {
        if ((keyData & Keys.Right) == Keys.Right || (keyData & Keys.Left) == Keys.Left || (keyData & Keys.Up) == Keys.Up || (keyData & Keys.Down) == Keys.Down)
        {
            return true;
        }
        return base.IsInputKey(keyData);
    }

    protected override void OnBackColorChanged(EventArgs e)
    {
        base.OnBackColorChanged(e);
        Invalidate();
    }

    protected override void OnBorderStyleChanged(EventArgs e)
    {
        base.OnBorderStyleChanged(e);
        AdjustLayout();
    }

    protected override void OnDockChanged(EventArgs e)
    {
        base.OnDockChanged(e);
        if (Dock != 0)
        {
            AutoSize = false;
        }
    }

    protected override void OnKeyDown(KeyEventArgs e)
    {
        base.OnKeyDown(e);
        ProcessScrollingShortcuts(e);
    }

    protected override void OnMouseDown(MouseEventArgs e)
    {
        base.OnMouseDown(e);
        if (!Focused)
        {
            Focus();
        }
        if (e.Button != 0)
        {
            if (_panStyle == ImageBoxPanStyle.Free)
            {
                ProcessPanEvents(ImageBoxPanStyle.None);
            }
            else
            {
                _mouseDownStart = NativeMethods.GetTickCount();
                ProcessPanning(e);
            }
        }
        SetCursor(e.Location);
    }

    protected override void OnMouseMove(MouseEventArgs e)
    {
        base.OnMouseMove(e);
        if (e.Button != 0)
        {
            ProcessPanning(e);
        }
        SetCursor(e.Location);
    }

    protected override void OnMouseUp(MouseEventArgs e)
    {
        base.OnMouseUp(e);
        if (_panStyle == ImageBoxPanStyle.Standard || (_panStyle == ImageBoxPanStyle.Free && NativeMethods.GetTickCount() > _mouseDownStart + SystemInformation.DoubleClickTime))
        {
            ProcessPanEvents(ImageBoxPanStyle.None);
        }
    }

    [Browsable(true)]
    [EditorBrowsable(EditorBrowsableState.Always)]
    [Category("Mouse")]
    public event MouseEventHandler BeforeMouseWheel;

    [Browsable(true)]
    [EditorBrowsable(EditorBrowsableState.Always)]
    [Category("Mouse")]
    public new event MouseEventHandler MouseWheel;

    public bool IsOnMouseWheel { get; set; }

    protected override void OnMouseWheel(MouseEventArgs e)
    {
        if (!IsOnMouseWheel)
        {
            BeforeMouseWheel?.Invoke(this, e);
            IsOnMouseWheel = true;
            base.OnMouseWheel(e);
            if (SizeMode == ImageBoxSizeMode.Normal)
            {
                int num = Math.Abs(Math.Min(e.Delta / SystemInformation.MouseWheelScrollDelta, 2));
                for (int i = 0; i < num; i++)
                {
                    PerformZoom(e.Delta > 0 ? ImageBoxZoomActions.ZoomIn : ImageBoxZoomActions.ZoomOut, preservePosition: true, e.Location);
                }
            }
            MouseWheel?.Invoke(this, e);
            IsOnMouseWheel = false;
        }
    }

    protected override void OnPaddingChanged(EventArgs e)
    {
        base.OnPaddingChanged(e);
        AdjustLayout();
    }

    protected override void OnPaint(PaintEventArgs e)
    {
        if (!AllowPainting) return;
        
        // 获取需要绘制的区域
        Rectangle clipRect = e.ClipRectangle;
        
        // 如果是全屏重绘，或者绘制区域足够大，则绘制背景
        if (clipRect.Width * clipRect.Height > ClientSize.Width * ClientSize.Height * 0.5 || clipRect == ClientRectangle)
        {
            DrawBackground(e);
        }
        
        // 只有在有图像的情况下才绘制图像
        if (Image != null)
        {
            DrawImage(e.Graphics);
            
            // 仅在需要时绘制像素网格
            if (ShowPixelGrid && ZoomFactor > PixelGridThreshold)
            {
                DrawPixelGrid(e.Graphics);
            }
        }
        
        // 基类绘制
        base.OnPaint(e);
        
        // 清除脏区域
        _dirtyRegion = null;
    }

    protected override void OnParentChanged(EventArgs e)
    {
        base.OnParentChanged(e);
        AdjustLayout();
    }

    protected override void OnResize(EventArgs e)
    {
        AdjustLayout();
        base.OnResize(e);
    }

    protected override void OnScroll(ScrollEventArgs se)
    {
        Invalidate();
        base.OnScroll(se);
    }

    protected virtual void OnBeforeZoomChanged(EventArgs e)
    {
        BeforeZoomChanged?.Invoke(this, e);
    }

    protected virtual void OnZoomChanged(EventArgs e)
    {
        AdjustLayout();
        ZoomChanged?.Invoke(this, e);
    }

    public bool HScrollVisibile { get { return base.HScroll; } }
    public bool VScrollVisibile { get { return base.VScroll; } }

    protected virtual void ProcessPanning(MouseEventArgs e)
    {
        if (CanPan(e.Button) && _panStyle == ImageBoxPanStyle.None && (base.HScroll || base.VScroll))
        {
            _startMousePosition = e.Location;
            ProcessPanEvents((e.Button != MouseButtons.Middle) ? ImageBoxPanStyle.Standard : ImageBoxPanStyle.Free);
        }
        int num;
        int num2;
        if (!InvertMouse)
        {
            num = -_startScrollPosition.X + (_startMousePosition.X - e.Location.X);
            num2 = -_startScrollPosition.Y + (_startMousePosition.Y - e.Location.Y);
        }
        else
        {
            num = -(_startScrollPosition.X + (_startMousePosition.X - e.Location.X));
            num2 = -(_startScrollPosition.Y + (_startMousePosition.Y - e.Location.Y));
        }
        Point position = new Point(num, num2);
        UpdateScrollPosition(position);
    }

    protected virtual void ProcessScrollingShortcuts(KeyEventArgs e)
    {
        switch (e.KeyCode)
        {
            case Keys.Left:
                AdjustScroll(-((e.Modifiers == Keys.None) ? base.HorizontalScroll.SmallChange : base.HorizontalScroll.LargeChange), 0);
                break;
            case Keys.Right:
                AdjustScroll((e.Modifiers == Keys.None) ? base.HorizontalScroll.SmallChange : base.HorizontalScroll.LargeChange, 0);
                break;
            case Keys.Up:
                AdjustScroll(0, -((e.Modifiers == Keys.None) ? base.VerticalScroll.SmallChange : base.VerticalScroll.LargeChange));
                break;
            case Keys.Down:
                AdjustScroll(0, (e.Modifiers == Keys.None) ? base.VerticalScroll.SmallChange : base.VerticalScroll.LargeChange);
                break;
        }
    }

    protected void RestoreSizeMode()
    {
        if (SizeMode != 0)
        {
            int zoom = Zoom;
            SizeMode = ImageBoxSizeMode.Normal;
            Zoom = zoom;
        }
    }

    protected virtual void UpdateScrollPosition(Point position)
    {
        AutoScrollPosition = position;
        Invalidate();
        OnScroll(new ScrollEventArgs(ScrollEventType.EndScroll, 0));
    }

    private bool CanPan(MouseButtons button)
    {
        if ((base.HScroll || base.VScroll) && ((uint)_panMode & (uint)button) != 0 && !ViewSize.IsEmpty)
        {
            return true;
        }
        return false;
    }

    private void CreateTimer()
    {
        _freePanTimer = new Timer
        {
            Enabled = true,
            Interval = 250
        };
        _freePanTimer.Tick += FreePanTimerTickHandler;
        _freePanTimer.Start();
    }

    private void FreePanTimerTickHandler(object sender, EventArgs e)
    {
        Point location = PointToClient(MousePosition);
        ImageBoxPanDirection panDirection = GetPanDirection(location);
        int distance = GetDistance(_startMousePosition.X, _startMousePosition.Y, location.X, location.Y);
        int num = 0;
        int num2 = 0;
        switch (panDirection)
        {
            case ImageBoxPanDirection.Up:
                num2 = -distance;
                break;
            case ImageBoxPanDirection.Down:
                num2 = distance;
                break;
            case ImageBoxPanDirection.Left:
                num = -distance;
                break;
            case ImageBoxPanDirection.Right:
                num = distance;
                break;
        }
        if (num != 0 || num2 != 0)
        {
            AdjustScroll(num, num2);
        }
    }

    private int GetDistance(int x1, int y1, int x2, int y2)
    {
        int num = x2 - x1;
        int num2 = y2 - y1;
        return Convert.ToInt32(Math.Sqrt(num * num + num2 * num2));
    }

    private Size GetImageSize()
    {
        if (Image != null)
        {
            try
            {
                return Image.Size;
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }
        return Size.Empty;
    }

    private ImageBoxPanDirection GetPanDirection(Point location)
    {
        int num = location.X - _startMousePosition.X;
        int num2 = location.Y - _startMousePosition.Y;
        if (num >= -32 && num <= 32 && num2 >= -32 && num2 <= 32)
        {
            return ImageBoxPanDirection.None;
        }
        int num3 = location.X - _startMousePosition.X;
        if (-num2 > Math.Abs(num3))
        {
            return ImageBoxPanDirection.Up;
        }
        if (num2 > Math.Abs(num3))
        {
            return ImageBoxPanDirection.Down;
        }
        return (num3 < 0) ? ImageBoxPanDirection.Left : ImageBoxPanDirection.Right;
    }

    private int GetZoomLevel(ImageBoxZoomActions action)
    {
        switch (action)
        {
            case ImageBoxZoomActions.None:
                return Zoom;
            case ImageBoxZoomActions.ZoomIn:
                return Math.Min((int)(Zoom * ZoomIncrement), MaxZoom);
            case ImageBoxZoomActions.ZoomOut:
                return Math.Max((int)(Zoom / ZoomIncrement), MinZoom);
            case ImageBoxZoomActions.ActualSize:
                return 100;
        }
        throw new ArgumentOutOfRangeException("action");
    }

    public void InitBackGround()
    {
        _texture = ImageBoxGridDisplayModeHelper.GetBackBrush(ref _gridColor);
        BackColor = _gridColor;
        Invalidate();
    }

    private void KillTimer()
    {
        if (_freePanTimer != null)
        {
            _freePanTimer.Stop();
            _freePanTimer.Tick -= FreePanTimerTickHandler;
            _freePanTimer.Dispose();
            _freePanTimer = null;
        }
    }

    private void OnFrameChangedHandler(object sender, EventArgs eventArgs)
    {
        Invalidate();
    }

    private void PerformActualSize()
    {
        SizeMode = ImageBoxSizeMode.Normal;
        SetZoom(100, ImageBoxZoomActions.ActualSize | ((Zoom < 100) ? ImageBoxZoomActions.ZoomIn : ImageBoxZoomActions.ZoomOut));
    }

    private void PerformZoom(ImageBoxZoomActions action, bool preservePosition, Point relativePoint)
    {
        Point imageLocation = PointToImage(relativePoint);
        int zoom = Zoom;
        int zoomLevel = GetZoomLevel(action);
        RestoreSizeMode();
        SetZoom(zoomLevel, action);
        if (preservePosition && Zoom != zoom)
        {
            ScrollTo(imageLocation, relativePoint);
        }
        Update();
    }

    private void ProcessPanEvents(ImageBoxPanStyle panStyle)
    {
        if (_panStyle == panStyle)
        {
            return;
        }
        KillTimer();
        if (panStyle == ImageBoxPanStyle.None)
        {
            _panStyle = ImageBoxPanStyle.None;
            Invalidate();
            return;
        }
        CancelEventArgs cancelEventArgs = new CancelEventArgs();
        if (!cancelEventArgs.Cancel)
        {
            _panStyle = panStyle;
            if (panStyle == ImageBoxPanStyle.Free)
            {
                CreateTimer();
            }
            _startScrollPosition = AutoScrollPosition;
        }
        Invalidate();
    }

    private void SetCursor(Point location)
    {
        Cursor cursor = GetCursor(location);
        if (_currentCursor != cursor)
        {
            _currentCursor = cursor;
            Cursor = cursor;
        }
    }

    private void SetZoom(int value, ImageBoxZoomActions actions)
    {
        if (value < MinZoom)
        {
            value = MinZoom;
        }
        else if (value > MaxZoom)
        {
            value = MaxZoom;
        }
        if (_zoom != value)
        {
            OnBeforeZoomChanged(EventArgs.Empty);
            _zoom = value;
            OnZoomChanged(EventArgs.Empty);
        }
    }

    /// <summary>
    /// 安全释放图像资源
    /// </summary>
    /// <param name="image">要释放的图像</param>
    private void SafeDisposeImage(Image image)
    {
        if (image == null) return;
        
        try
        {
            // 使用资源管理器释放资源，不强制释放
            OCRTools.Common.ImageResourceManager.ReleaseImage(image);
        }
        catch (Exception ex)
        {
            // 记录异常但不抛出
            Console.WriteLine($"Error disposing image in ImageBox: {ex.Message}");
        }
    }

    /// <summary>
    /// 标记需要重绘的区域
    /// </summary>
    /// <param name="rect">需要重绘的矩形区域</param>
    public virtual void InvalidateRegion(Rectangle rect)
    {
        if (rect.IsEmpty) return;
        
        if (_dirtyRegion == null)
        {
            _dirtyRegion = new Region(rect);
        }
        else
        {
            _dirtyRegion.Union(rect);
        }
        
        Invalidate(rect);
    }
    
    public new virtual void Invalidate(Rectangle rect)
    {
        if (InvokeRequired)
        {
            BeginInvoke(new Action<Rectangle>(base.Invalidate), rect);
        }
        else
        {
            base.Invalidate(rect);
        }
    }
    
    public new virtual void Invalidate()
    {
        _isDirty = true;
        
        if (InvokeRequired)
        {
            BeginInvoke(new Action(base.Invalidate));
        }
        else
        {
            base.Invalidate();
        }
    }
}
