﻿using MetroFramework.Components;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace MetroFramework.Controls
{
    public class MetroContextMenu : ContextMenuStrip, IMetroControl
    {
        private MetroColorStyle metroStyle;

        private MetroStyleManager metroStyleManager;

        private MetroThemeStyle metroTheme;

        public MetroContextMenu(IContainer Container)
        {
            Container?.Add(this);
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomBackColor { get; set; }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseCustomForeColor { get; set; }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseStyleColors { get; set; }

        [Browsable(false)]
        [DefaultValue(false)]
        [Category("Metro Behaviour")]
        public bool UseSelectable
        {
            get => GetStyle(ControlStyles.Selectable);
            set => SetStyle(ControlStyles.Selectable, value);
        }

        [DefaultValue(MetroColorStyle.Blue)]
        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                if (DesignMode || metroStyle != 0) return metroStyle;
                if (StyleManager != null) return StyleManager.Style;
                if (StyleManager == null) return MetroColorStyle.Blue;
                return metroStyle;
            }
            set => metroStyle = value;
        }

        [DefaultValue(MetroThemeStyle.Light)]
        [Category("Metro Appearance")]
        public MetroThemeStyle Theme
        {
            get
            {
                if (DesignMode || metroTheme != 0) return metroTheme;
                if (StyleManager != null) return StyleManager.Theme;
                if (StyleManager == null) return MetroThemeStyle.Light;
                return metroTheme;
            }
            set => metroTheme = value;
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        public MetroStyleManager StyleManager
        {
            get => metroStyleManager;
            set
            {
                metroStyleManager = value;
                settheme();
            }
        }

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintBackground != null) CustomPaintBackground(this, e);
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaint != null) CustomPaint(this, e);
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintForeground != null) CustomPaintForeground(this, e);
        }

        private void settheme()
        {
            BackColor = MetroPaint.BackColor.Form(Theme);
            ForeColor = MetroPaint.ForeColor.Button.Normal(Theme);
            Renderer = new MetroCTXRenderer(Theme, Style);
        }

        private class MetroCTXRenderer : ToolStripProfessionalRenderer
        {
            private readonly MetroThemeStyle _theme;

            public MetroCTXRenderer(MetroThemeStyle Theme, MetroColorStyle Style)
                : base(new contextcolors(Theme, Style))
            {
                _theme = Theme;
            }

            protected override void OnRenderItemText(ToolStripItemTextRenderEventArgs e)
            {
                e.TextColor = MetroPaint.ForeColor.Button.Normal(_theme);
                base.OnRenderItemText(e);
            }
        }

        private class contextcolors : ProfessionalColorTable
        {
            private readonly MetroColorStyle _style = MetroColorStyle.Blue;
            private readonly MetroThemeStyle _theme = MetroThemeStyle.Light;

            public contextcolors(MetroThemeStyle Theme, MetroColorStyle Style)
            {
                _theme = Theme;
                _style = Style;
            }

            public override Color MenuItemSelected => MetroPaint.GetStyleColor(_style);

            public override Color MenuBorder => MetroPaint.BackColor.Form(_theme);

            public override Color ToolStripBorder => MetroPaint.GetStyleColor(_style);

            public override Color MenuItemBorder => MetroPaint.GetStyleColor(_style);

            public override Color ToolStripDropDownBackground => MetroPaint.BackColor.Form(_theme);

            public override Color ImageMarginGradientBegin => MetroPaint.BackColor.Form(_theme);

            public override Color ImageMarginGradientMiddle => MetroPaint.BackColor.Form(_theme);

            public override Color ImageMarginGradientEnd => MetroPaint.BackColor.Form(_theme);
        }
    }
}