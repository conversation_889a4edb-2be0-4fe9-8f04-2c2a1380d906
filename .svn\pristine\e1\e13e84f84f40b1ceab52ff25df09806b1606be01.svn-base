using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using OCRTools.Common;

namespace OCRTools
{
    public class SelectRectangleList
    {
        private List<WindowInfo> _lstResult;

        public IntPtr IgnoreHandle { get; set; }

        public ConcurrentBag<WindowInfo> GetWindowInfoListAsync(int timeout)
        {
            ConcurrentBag<WindowInfo> windowInfoList = null;
            var thread = new Thread((ThreadStart) delegate
            {
                try
                {
                    windowInfoList = GetWindowInfoList();
                }
                catch
                {
                }
            });
            thread.Start();
            if (!thread.Join(timeout)) thread.Abort();
            return windowInfoList;
        }

        public void SetWindowZOrder()
        {
            if (!_lstResult.Any(p => p.IsWindow)) return;
            Parallel.ForEach(_lstResult.Where(p => p.IsWindow), new ParallelOptions {MaxDegreeOfParallelism = 5},
                p => { p.ZIndex = GetWindowZOrder(p.Handle); });
        }

        private int GetWindowZOrder(IntPtr hWnd)
        {
            var zOrder = -1;
            while ((hWnd = NativeMethods.GetWindow(hWnd, 2 /* GW_HWNDNEXT */)) != IntPtr.Zero) zOrder++;
            return zOrder;
        }

        private ConcurrentBag<WindowInfo> GetWindowInfoList()
        {
            _lstResult = new List<WindowInfo>();
            NativeMethods.EnumWindows(EvalWindow, IntPtr.Zero);
            var list = new ConcurrentBag<WindowInfo>();
            foreach (var window in _lstResult)
            {
                var flag = true;
                if (!window.IsWindow) flag = !list.Any(p => p.Rectangle.Contains(window.Rectangle));
                if (flag) list.Add(window);
            }

            return list;
        }

        private bool EvalWindow(IntPtr hWnd, IntPtr lParam)
        {
            return CheckHandle(hWnd, true);
        }

        private bool CheckHandle(IntPtr handle, bool isWindow)
        {
            if (handle == IgnoreHandle || !NativeMethods.IsWindowVisible(handle) ||
                isWindow && NativeMethods.IsWindowCloaked(handle)) return true;
            var windowInfo = new WindowInfo(handle) {IsWindow = isWindow};
            if (!windowInfo.GetRectangle().IsValid()) return true;
            var clientRect = NativeMethods.GetClientRect(handle);
            if (clientRect.IsValid() && clientRect != windowInfo.Rectangle)
                _lstResult.Add(new WindowInfo(handle, clientRect) {IsWindow = true, ParentHandle = handle});
            _lstResult.Add(windowInfo);
            return true;
        }
    }
}