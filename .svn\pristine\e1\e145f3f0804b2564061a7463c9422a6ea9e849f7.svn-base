﻿using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class PanelPictureView : ImageBox
    {
        private Size DragSize;
        public PanelPictureView()
        {
            Margin = Padding.Empty;
            AutoScroll = true;
            TabStop = false;
            AutoCenter = false;

            var monitor = new MouseWheelMonitor(this, 300);
            monitor.MouseWheelStopped += Monitor_MouseWheelStopped;
            monitor.MouseWheelStarted += Monitor_MouseWheelStarted;

            MouseDoubleClick += PanelPictureView_MouseDoubleClick;
            MouseDown += PanelPictureView_MouseDown;
            MouseMove += PanelPictureView_MouseMove;
            MouseUp += PanelPictureView_MouseUp;
            DragSize = SystemInformation.DragSize;
        }

        private void PanelPictureView_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            ClearMoveInfo();
        }

        private void ClearMoveInfo()
        {
            _lastPoint = Point.Empty;
            _isBeginMouseDown = false;
            _isBeginMouseMove = false;
        }

        private Point _lastPoint;
        private bool _isBeginMouseDown;
        private bool _isBeginMouseMove;

        private void PanelPictureView_MouseDown(object sender, MouseEventArgs e)
        {
            _isBeginMouseDown = true;
            _lastPoint = MousePosition;
        }

        private void PanelPictureView_MouseMove(object sender, MouseEventArgs e)
        {
            if (!_isBeginMouseDown || _isBeginMouseMove) return;
            if (!_lastPoint.IsEmpty)
            {
                var nowPoint = MousePosition;
                _isBeginMouseMove = Math.Abs(nowPoint.X - _lastPoint.X) > DragSize.Width || Math.Abs(nowPoint.Y - _lastPoint.Y) > DragSize.Height;
            }
            if (_isBeginMouseMove)
                HideAll();
        }

        private void PanelPictureView_MouseUp(object sender, MouseEventArgs e)
        {
            ClearMoveInfo();
            ShowAll();
        }

        private void Monitor_MouseWheelStarted(object sender, EventArgs e)
        {
            HideAll();
        }

        private void Monitor_MouseWheelStopped(object sender, EventArgs e)
        {
            ShowAll();
        }

        private void HideAll()
        {
            HideChild(this, true);
            Invalidate();
        }

        private void ShowAll()
        {
            InitChildZoom(this, true, Zoom * 1.0 / 100);
            Invalidate();
        }

        public void Clear()
        {
            try
            {
                foreach (Control item in Controls) item.Dispose();
            }
            catch
            {
            }

            try
            {
                Controls.Clear();
            }
            catch
            {
            }
        }

        public void BindPicTxt(UcContent content, double zoom, bool isShowTxt = false)
        {
            Image = content.Image;
            if (content.Image != null)
                Size = new Size((int)(content.Image.Size.Width * zoom), (int)(content.Image.Size.Height * zoom));
            //SendToBack();

            var lstCells =
                CommonString.JavaScriptSerializer.Deserialize<List<TextCellInfo>>(
                    content.OcrContent.result.verticalText);

            foreach (var item in lstCells)
            {
                if (string.IsNullOrEmpty(item.words) && string.IsNullOrEmpty(item.trans)) continue;
                var itemTxt = content.GetTextByContent(item);
                var baseSize = new Size((int)item.location.width, (int)item.location.height);

                var lbl = new TransParentLabel
                {
                    OrgLocation = new Point((int)Math.Floor(item.location.left), (int)Math.Floor(item.location.top)),
                    OriSize = new Size(baseSize.Width, baseSize.Height),
                    Font = CommonSetting.默认文字字体,
                    ForeColor = CommonSetting.默认文字颜色,
                    ContentBackColor = CommonSetting.默认背景颜色,
                    TabStop = false,
                    Text = itemTxt,
                    TabIndex = lstCells.IndexOf(item) + 1,
                    IsShowText = isShowTxt
                }; // new Label() { BorderStyle = BorderStyle.Fixed3D, AutoSize = false, BackColor = Color.Transparent };

                ////lbl.Font = GetFontByGraphicsMeasure(gh, lbl.Text, BaseFont, ref baseSize);
                ////lbl.Font = GetFontByTextRendererMeasure(gh, lbl.Text, BaseFont, ref baseSize);

                lbl.Location = GetZoomLocation(lbl.OrgLocation, zoom);
                lbl.Size = GetZoomSize(lbl.OriSize, zoom);

                CommonMethod.ShowTxtToolTip(content, lbl);

                Controls.Add(lbl);

                lbl.BringToFront();
            }

            lstCells.Clear();
            CommonMethod.EnableDoubleBuffering(this);
        }

        private Size GetZoomSize(Size oldSize, double zoom)
        {
            return new Size((int)(oldSize.Width * zoom) + 4, (int)(oldSize.Height * zoom) + 4);
        }

        private Point GetZoomLocation(Point location, double zoom)
        {
            return new Point((int)(AutoScrollPosition.X + location.X * zoom) - 1, (int)(AutoScrollPosition.Y + location.Y * zoom) - 1);
        }

        private void InitChildZoom(Control item, bool isSkip, double zoom)
        {
            if (!isSkip)
                if (item is TransParentLabel ctrl)
                {
                    ctrl.Location = GetZoomLocation(ctrl.OrgLocation, zoom);
                    ctrl.Size = GetZoomSize(ctrl.OriSize, zoom);
                    if (!ctrl.Visible) ctrl.Visible = true;
                }

            if (item.Controls.Count > 0)
                foreach (Control child in item.Controls)
                    InitChildZoom(child, false, zoom);
        }

        private void HideChild(Control item, bool isSkip)
        {
            if (!isSkip)
                if (item is TransParentLabel)
                    item.Hide();
            if (item.Controls.Count > 0)
                foreach (Control child in item.Controls)
                    HideChild(child, false);
        }
    }
}