﻿namespace OCRTools.Common
{
    using System;
    using System.Drawing;
    using System.Runtime.InteropServices;
    using System.Windows.Forms;
    using ComIDataObject = System.Runtime.InteropServices.ComTypes.IDataObject;
    public static class DragDropEngine
    {
        private static readonly IDropTargetHelper dropHelper;

        static DragDropEngine()
        {
            try
            {
                dropHelper = (IDropTargetHelper) new DragDropHelper();
            }
            catch { }
        }

        public static void ProcessDragEnter(object sender, DragEventArgs e)
        {
            if (dropHelper == null)
            {
                return;
            }
            var lstFormat = e.Data.GetFormats(true);
            if (!ControlExtension.IsFile(lstFormat))
            {
                return;
            }
            if (ControlExtension.IsQqDrag(lstFormat))
            {
                return;
            }

            var effect = e.Effect == DragDropEffects.None ? DragDropEffects.Copy : e.Effect;
            Point point = Cursor.Position;
            WindowsPoint winpoint;
            winpoint.X = point.X;
            winpoint.Y = point.Y;
            dropHelper.DragEnter(IntPtr.Zero, (ComIDataObject)e.Data,
                  ref winpoint, (int)effect);
        }
        public static void ProcessDragDrop(object sender, DragEventArgs e)
        {
            if (dropHelper == null)
            {
                return;
            }
            var lstFormat = e.Data.GetFormats(true);
            if (!ControlExtension.IsFile(lstFormat))
            {
                return;
            }
            if (ControlExtension.IsQqDrag(lstFormat))
            {
                return;
            }

            var effect = e.Effect == DragDropEffects.None ? DragDropEffects.Copy : e.Effect;
            Point point = Cursor.Position;
            WindowsPoint winpoint;
            winpoint.X = point.X;
            winpoint.Y = point.Y;
            dropHelper.Drop((ComIDataObject)e.Data, ref winpoint, (int)effect);
        }
        public static void ProcessDragOver(object sender, DragEventArgs e)
        {
            if (dropHelper == null)
            {
                return;
            }
            var lstFormat = e.Data.GetFormats(true);
            if (!ControlExtension.IsFile(lstFormat))
            {
                return;
            }
            if (ControlExtension.IsQqDrag(lstFormat))
            {
                return;
            }

            var effect = e.Effect == DragDropEffects.None ? DragDropEffects.Copy : e.Effect;
            Point point = Cursor.Position;
            WindowsPoint winpoint;
            winpoint.X = point.X;
            winpoint.Y = point.Y;
            dropHelper.DragOver(ref winpoint, (int)effect);
        }
        public static void ProcessDragLeave(object sender, EventArgs e)
        {
            dropHelper?.DragLeave();
        }
    }
    [ComImport]
    [Guid("4657278A-411B-11d2-839A-00C04FD918D0")]
    public class DragDropHelper
    {
    }
    [ComVisible(true)]
    [ComImport]
    [Guid("4657278B-411B-11D2-839A-00C04FD918D0")]
    [InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    public interface IDropTargetHelper
    {
        void DragEnter(
            [In] IntPtr hwndTarget,
            [In, MarshalAs(UnmanagedType.Interface)]
            System.Runtime.InteropServices.ComTypes.IDataObject dataObject,
            [In] ref WindowsPoint pt,
            [In] int effect);
        void DragLeave();
        void DragOver(
            [In] ref WindowsPoint pt,
            [In] int effect);
        void Drop(
            [In, MarshalAs(UnmanagedType.Interface)]
            System.Runtime.InteropServices.ComTypes.IDataObject dataObject,
            [In] ref WindowsPoint pt,
            [In] int effect);
        void Show(
            [In] bool show);
    }
    [StructLayout(LayoutKind.Sequential)]
    public struct WindowsPoint
    {
        public int X;
        public int Y;
    }
}
