﻿using OCRTools.Language;
using OCRTools.UserControlEx;

namespace OCRTools
{
    partial class ColorPickerForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.lblOld = new System.Windows.Forms.Label();
            this.lblNew = new System.Windows.Forms.Label();
            this.txtHex = new System.Windows.Forms.TextBox();
            this.lblHex = new System.Windows.Forms.Label();
            this.nudKey = new System.Windows.Forms.NumericUpDown();
            this.nudYellow = new System.Windows.Forms.NumericUpDown();
            this.nudMagenta = new System.Windows.Forms.NumericUpDown();
            this.nudCyan = new System.Windows.Forms.NumericUpDown();
            this.lblKey = new System.Windows.Forms.Label();
            this.lblYellow = new System.Windows.Forms.Label();
            this.lblMagenta = new System.Windows.Forms.Label();
            this.lblCyan = new System.Windows.Forms.Label();
            this.lblHue = new System.Windows.Forms.Label();
            this.lblBrightnessPerc = new System.Windows.Forms.Label();
            this.lblSaturationPerc = new System.Windows.Forms.Label();
            this.nudBlue = new System.Windows.Forms.NumericUpDown();
            this.nudGreen = new System.Windows.Forms.NumericUpDown();
            this.nudRed = new System.Windows.Forms.NumericUpDown();
            this.nudBrightness = new System.Windows.Forms.NumericUpDown();
            this.nudSaturation = new System.Windows.Forms.NumericUpDown();
            this.nudHue = new System.Windows.Forms.NumericUpDown();
            this.rbBlue = new System.Windows.Forms.RadioButton();
            this.rbGreen = new System.Windows.Forms.RadioButton();
            this.rbRed = new System.Windows.Forms.RadioButton();
            this.rbBrightness = new System.Windows.Forms.RadioButton();
            this.rbSaturation = new System.Windows.Forms.RadioButton();
            this.rbHue = new System.Windows.Forms.RadioButton();
            this.lblCyanPerc = new System.Windows.Forms.Label();
            this.lblMagentaPerc = new System.Windows.Forms.Label();
            this.lblYellowPerc = new System.Windows.Forms.Label();
            this.lblKeyPerc = new System.Windows.Forms.Label();
            this.nudAlpha = new System.Windows.Forms.NumericUpDown();
            this.lblAlpha = new System.Windows.Forms.Label();
            this.ttMain = new System.Windows.Forms.ToolTip(this.components);
            this.cbTransparent = new OCRTools.ColorButton();
            this.btnClose = new SkinButton();
            this.flpColorPalette = new System.Windows.Forms.FlowLayoutPanel();
            this.rbStandardColors = new System.Windows.Forms.RadioButton();
            this.flpColorPaletteSelection = new System.Windows.Forms.FlowLayoutPanel();
            this.rbRecentColors = new System.Windows.Forms.RadioButton();
            this.lblName = new System.Windows.Forms.Label();
            this.lblNameValue = new System.Windows.Forms.Label();
            this.btnClipboardStatus = new System.Windows.Forms.Button();
            this.btnCopy = new SkinButton();
            this.pbColorPreview = new OCRTools.MyPictureBox();
            this.colorPicker = new OCRTools.ColorPicker();
            this.panel2 = new System.Windows.Forms.Panel();
            ((System.ComponentModel.ISupportInitialize)(this.nudKey)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudYellow)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudMagenta)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudCyan)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudBlue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudGreen)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudRed)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudBrightness)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudSaturation)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudHue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudAlpha)).BeginInit();
            this.flpColorPaletteSelection.SuspendLayout();
            this.panel2.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.btnCancel.Location = new System.Drawing.Point(576, 296);
            this.btnCancel.Size = new System.Drawing.Size(96, 32);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Visible = false;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.btnOK.Location = new System.Drawing.Point(472, 296);
            this.btnOK.Size = new System.Drawing.Size(96, 32);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Visible = false;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // lblOld
            // 
            this.lblOld.AutoSize = true;
            this.lblOld.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblOld.Location = new System.Drawing.Point(309, 262);
            this.lblOld.Name = "lblOld";
            this.lblOld.Size = new System.Drawing.Size(29, 12);
            this.lblOld.Text = "老:";
            // 
            // lblNew
            // 
            this.lblNew.AutoSize = true;
            this.lblNew.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblNew.Location = new System.Drawing.Point(309, 238);
            this.lblNew.Name = "lblNew";
            this.lblNew.Size = new System.Drawing.Size(29, 12);
            this.lblNew.TabIndex = 36;
            this.lblNew.Text = "新:";
            // 
            // txtHex
            // 
            this.txtHex.Location = new System.Drawing.Point(600, 174);
            this.txtHex.MaxLength = 9;
            this.txtHex.Name = "txtHex";
            this.txtHex.Size = new System.Drawing.Size(72, 21);
            this.txtHex.TabIndex = 33;
            this.txtHex.Text = "FF00FF00";
            this.txtHex.TextChanged += new System.EventHandler(this.txtHex_TextChanged);
            // 
            // lblHex
            // 
            this.lblHex.AutoSize = true;
            this.lblHex.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblHex.Location = new System.Drawing.Point(485, 178);
            this.lblHex.Name = "lblHex";
            this.lblHex.Size = new System.Drawing.Size(29, 12);
            this.lblHex.TabIndex = 32;
            this.lblHex.Text = "十六进制：";
            // 
            // nudKey
            // 
            this.nudKey.DecimalPlaces = 1;
            this.nudKey.Location = new System.Drawing.Point(600, 142);
            this.nudKey.Name = "nudKey";
            this.nudKey.Size = new System.Drawing.Size(56, 21);
            this.nudKey.TabIndex = 30;
            this.nudKey.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nudKey.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.nudKey.ValueChanged += new System.EventHandler(this.CMYK_ValueChanged);
            // 
            // nudYellow
            // 
            this.nudYellow.DecimalPlaces = 1;
            this.nudYellow.Location = new System.Drawing.Point(600, 108);
            this.nudYellow.Name = "nudYellow";
            this.nudYellow.Size = new System.Drawing.Size(56, 21);
            this.nudYellow.TabIndex = 27;
            this.nudYellow.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nudYellow.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.nudYellow.ValueChanged += new System.EventHandler(this.CMYK_ValueChanged);
            // 
            // nudMagenta
            // 
            this.nudMagenta.DecimalPlaces = 1;
            this.nudMagenta.Location = new System.Drawing.Point(600, 78);
            this.nudMagenta.Name = "nudMagenta";
            this.nudMagenta.Size = new System.Drawing.Size(56, 21);
            this.nudMagenta.TabIndex = 24;
            this.nudMagenta.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nudMagenta.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.nudMagenta.ValueChanged += new System.EventHandler(this.CMYK_ValueChanged);
            // 
            // nudCyan
            // 
            this.nudCyan.DecimalPlaces = 1;
            this.nudCyan.Location = new System.Drawing.Point(600, 46);
            this.nudCyan.Name = "nudCyan";
            this.nudCyan.Size = new System.Drawing.Size(56, 21);
            this.nudCyan.TabIndex = 21;
            this.nudCyan.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nudCyan.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.nudCyan.ValueChanged += new System.EventHandler(this.CMYK_ValueChanged);
            // 
            // lblKey
            // 
            this.lblKey.AutoSize = true;
            this.lblKey.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblKey.Location = new System.Drawing.Point(485, 146);
            this.lblKey.Name = "lblKey";
            this.lblKey.Size = new System.Drawing.Size(29, 12);
            this.lblKey.TabIndex = 29;
            this.lblKey.Text = "键：";
            // 
            // lblYellow
            // 
            this.lblYellow.AutoSize = true;
            this.lblYellow.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblYellow.Location = new System.Drawing.Point(485, 112);
            this.lblYellow.Name = "lblYellow";
            this.lblYellow.Size = new System.Drawing.Size(47, 12);
            this.lblYellow.TabIndex = 26;
            this.lblYellow.Text = "黄色：";
            // 
            // lblMagenta
            // 
            this.lblMagenta.AutoSize = true;
            this.lblMagenta.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblMagenta.Location = new System.Drawing.Point(485, 82);
            this.lblMagenta.Name = "lblMagenta";
            this.lblMagenta.Size = new System.Drawing.Size(53, 12);
            this.lblMagenta.TabIndex = 23;
            this.lblMagenta.Text = "品红：";
            // 
            // lblCyan
            // 
            this.lblCyan.AutoSize = true;
            this.lblCyan.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblCyan.Location = new System.Drawing.Point(485, 50);
            this.lblCyan.Name = "lblCyan";
            this.lblCyan.Size = new System.Drawing.Size(35, 12);
            this.lblCyan.TabIndex = 20;
            this.lblCyan.Text = "青色：";
            // 
            // lblHue
            // 
            this.lblHue.AutoSize = true;
            this.lblHue.ForeColor = System.Drawing.Color.White;
            this.lblHue.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblHue.Location = new System.Drawing.Point(460, 112);
            this.lblHue.Name = "lblHue";
            this.lblHue.Size = new System.Drawing.Size(13, 13);
            this.lblHue.TabIndex = 5;
            this.lblHue.Text = "°";
            // 
            // lblBrightnessPerc
            // 
            this.lblBrightnessPerc.AutoSize = true;
            this.lblBrightnessPerc.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblBrightnessPerc.Location = new System.Drawing.Point(460, 178);
            this.lblBrightnessPerc.Name = "lblBrightnessPerc";
            this.lblBrightnessPerc.Size = new System.Drawing.Size(19, 13);
            this.lblBrightnessPerc.TabIndex = 11;
            this.lblBrightnessPerc.Text = "%";
            // 
            // lblSaturationPerc
            // 
            this.lblSaturationPerc.AutoSize = true;
            this.lblSaturationPerc.ForeColor = System.Drawing.Color.White;
            this.lblSaturationPerc.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblSaturationPerc.Location = new System.Drawing.Point(460, 146);
            this.lblSaturationPerc.Name = "lblSaturationPerc";
            this.lblSaturationPerc.Size = new System.Drawing.Size(19, 13);
            this.lblSaturationPerc.TabIndex = 8;
            this.lblSaturationPerc.Text = "%";
            // 
            // nudBlue
            // 
            this.nudBlue.Location = new System.Drawing.Point(408, 78);
            this.nudBlue.Maximum = new decimal(new int[] {
            255,
            0,
            0,
            0});
            this.nudBlue.Name = "nudBlue";
            this.nudBlue.Size = new System.Drawing.Size(48, 21);
            this.nudBlue.TabIndex = 17;
            this.nudBlue.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nudBlue.Value = new decimal(new int[] {
            255,
            0,
            0,
            0});
            this.nudBlue.ValueChanged += new System.EventHandler(this.RGB_ValueChanged);
            // 
            // nudGreen
            // 
            this.nudGreen.Location = new System.Drawing.Point(408, 46);
            this.nudGreen.Maximum = new decimal(new int[] {
            255,
            0,
            0,
            0});
            this.nudGreen.Name = "nudGreen";
            this.nudGreen.Size = new System.Drawing.Size(48, 21);
            this.nudGreen.TabIndex = 15;
            this.nudGreen.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nudGreen.Value = new decimal(new int[] {
            255,
            0,
            0,
            0});
            this.nudGreen.ValueChanged += new System.EventHandler(this.RGB_ValueChanged);
            // 
            // nudRed
            // 
            this.nudRed.Location = new System.Drawing.Point(408, 14);
            this.nudRed.Maximum = new decimal(new int[] {
            255,
            0,
            0,
            0});
            this.nudRed.Name = "nudRed";
            this.nudRed.Size = new System.Drawing.Size(48, 21);
            this.nudRed.TabIndex = 13;
            this.nudRed.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nudRed.Value = new decimal(new int[] {
            255,
            0,
            0,
            0});
            this.nudRed.ValueChanged += new System.EventHandler(this.RGB_ValueChanged);
            // 
            // nudBrightness
            // 
            this.nudBrightness.Location = new System.Drawing.Point(408, 174);
            this.nudBrightness.Name = "nudBrightness";
            this.nudBrightness.Size = new System.Drawing.Size(48, 21);
            this.nudBrightness.TabIndex = 10;
            this.nudBrightness.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nudBrightness.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.nudBrightness.ValueChanged += new System.EventHandler(this.HSB_ValueChanged);
            // 
            // nudSaturation
            // 
            this.nudSaturation.Location = new System.Drawing.Point(408, 142);
            this.nudSaturation.Name = "nudSaturation";
            this.nudSaturation.Size = new System.Drawing.Size(48, 21);
            this.nudSaturation.TabIndex = 7;
            this.nudSaturation.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nudSaturation.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.nudSaturation.ValueChanged += new System.EventHandler(this.HSB_ValueChanged);
            // 
            // nudHue
            // 
            this.nudHue.Location = new System.Drawing.Point(408, 108);
            this.nudHue.Maximum = new decimal(new int[] {
            360,
            0,
            0,
            0});
            this.nudHue.Name = "nudHue";
            this.nudHue.Size = new System.Drawing.Size(48, 21);
            this.nudHue.TabIndex = 4;
            this.nudHue.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nudHue.Value = new decimal(new int[] {
            360,
            0,
            0,
            0});
            this.nudHue.ValueChanged += new System.EventHandler(this.HSB_ValueChanged);
            // 
            // rbBlue
            // 
            this.rbBlue.AutoSize = true;
            this.rbBlue.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.rbBlue.Location = new System.Drawing.Point(312, 80);
            this.rbBlue.Name = "rbBlue";
            this.rbBlue.Size = new System.Drawing.Size(53, 16);
            this.rbBlue.TabIndex = 16;
            this.rbBlue.Text = "蓝色：";
            this.rbBlue.UseVisualStyleBackColor = true;
            this.rbBlue.CheckedChanged += new System.EventHandler(this.rbBlue_CheckedChanged);
            // 
            // rbGreen
            // 
            this.rbGreen.AutoSize = true;
            this.rbGreen.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.rbGreen.Location = new System.Drawing.Point(312, 48);
            this.rbGreen.Name = "rbGreen";
            this.rbGreen.Size = new System.Drawing.Size(59, 16);
            this.rbGreen.TabIndex = 14;
            this.rbGreen.Text = "绿色：";
            this.rbGreen.UseVisualStyleBackColor = true;
            this.rbGreen.CheckedChanged += new System.EventHandler(this.rbGreen_CheckedChanged);
            // 
            // rbRed
            // 
            this.rbRed.AutoSize = true;
            this.rbRed.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.rbRed.Location = new System.Drawing.Point(312, 16);
            this.rbRed.Name = "rbRed";
            this.rbRed.Size = new System.Drawing.Size(47, 16);
            this.rbRed.TabIndex = 12;
            this.rbRed.Text = "红色：";
            this.rbRed.UseVisualStyleBackColor = true;
            this.rbRed.CheckedChanged += new System.EventHandler(this.rbRed_CheckedChanged);
            // 
            // rbBrightness
            // 
            this.rbBrightness.AutoSize = true;
            this.rbBrightness.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.rbBrightness.Location = new System.Drawing.Point(312, 176);
            this.rbBrightness.Name = "rbBrightness";
            this.rbBrightness.Size = new System.Drawing.Size(89, 16);
            this.rbBrightness.TabIndex = 9;
            this.rbBrightness.Text = "亮度：";
            this.rbBrightness.UseVisualStyleBackColor = true;
            this.rbBrightness.CheckedChanged += new System.EventHandler(this.rbBrightness_CheckedChanged);
            // 
            // rbSaturation
            // 
            this.rbSaturation.AutoSize = true;
            this.rbSaturation.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.rbSaturation.Location = new System.Drawing.Point(312, 144);
            this.rbSaturation.Name = "rbSaturation";
            this.rbSaturation.Size = new System.Drawing.Size(89, 16);
            this.rbSaturation.TabIndex = 6;
            this.rbSaturation.Text = "饱和度：";
            this.rbSaturation.UseVisualStyleBackColor = true;
            this.rbSaturation.CheckedChanged += new System.EventHandler(this.rbSaturation_CheckedChanged);
            // 
            // rbHue
            // 
            this.rbHue.AutoSize = true;
            this.rbHue.Checked = true;
            this.rbHue.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.rbHue.Location = new System.Drawing.Point(312, 112);
            this.rbHue.Name = "rbHue";
            this.rbHue.Size = new System.Drawing.Size(47, 16);
            this.rbHue.TabIndex = 3;
            this.rbHue.TabStop = true;
            this.rbHue.Text = "色调：";
            this.rbHue.UseVisualStyleBackColor = true;
            this.rbHue.CheckedChanged += new System.EventHandler(this.rbHue_CheckedChanged);
            // 
            // lblCyanPerc
            // 
            this.lblCyanPerc.AutoSize = true;
            this.lblCyanPerc.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblCyanPerc.Location = new System.Drawing.Point(658, 50);
            this.lblCyanPerc.Name = "lblCyanPerc";
            this.lblCyanPerc.Size = new System.Drawing.Size(19, 13);
            this.lblCyanPerc.TabIndex = 22;
            this.lblCyanPerc.Text = "%";
            // 
            // lblMagentaPerc
            // 
            this.lblMagentaPerc.AutoSize = true;
            this.lblMagentaPerc.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblMagentaPerc.Location = new System.Drawing.Point(658, 82);
            this.lblMagentaPerc.Name = "lblMagentaPerc";
            this.lblMagentaPerc.Size = new System.Drawing.Size(19, 13);
            this.lblMagentaPerc.TabIndex = 25;
            this.lblMagentaPerc.Text = "%";
            // 
            // lblYellowPerc
            // 
            this.lblYellowPerc.AutoSize = true;
            this.lblYellowPerc.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblYellowPerc.Location = new System.Drawing.Point(658, 112);
            this.lblYellowPerc.Name = "lblYellowPerc";
            this.lblYellowPerc.Size = new System.Drawing.Size(19, 13);
            this.lblYellowPerc.TabIndex = 28;
            this.lblYellowPerc.Text = "%";
            // 
            // lblKeyPerc
            // 
            this.lblKeyPerc.AutoSize = true;
            this.lblKeyPerc.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblKeyPerc.Location = new System.Drawing.Point(658, 146);
            this.lblKeyPerc.Name = "lblKeyPerc";
            this.lblKeyPerc.Size = new System.Drawing.Size(19, 13);
            this.lblKeyPerc.TabIndex = 31;
            this.lblKeyPerc.Text = "%";
            // 
            // nudAlpha
            // 
            this.nudAlpha.Location = new System.Drawing.Point(600, 14);
            this.nudAlpha.Maximum = new decimal(new int[] {
            255,
            0,
            0,
            0});
            this.nudAlpha.Name = "nudAlpha";
            this.nudAlpha.Size = new System.Drawing.Size(48, 21);
            this.nudAlpha.TabIndex = 19;
            this.nudAlpha.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.nudAlpha.Value = new decimal(new int[] {
            255,
            0,
            0,
            0});
            this.nudAlpha.ValueChanged += new System.EventHandler(this.RGB_ValueChanged);
            // 
            // lblAlpha
            // 
            this.lblAlpha.AutoSize = true;
            this.lblAlpha.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblAlpha.Location = new System.Drawing.Point(485, 18);
            this.lblAlpha.Name = "lblAlpha";
            this.lblAlpha.Size = new System.Drawing.Size(41, 12);
            this.lblAlpha.TabIndex = 18;
            this.lblAlpha.Text = "透明度：";
            // 
            // ttMain
            // 
            this.ttMain.AutoPopDelay = 5000;
            this.ttMain.InitialDelay = 100;
            this.ttMain.ReshowDelay = 100;
            // 
            // cbTransparent
            // 
            this.cbTransparent.Color = System.Drawing.Color.Transparent;
            this.cbTransparent.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.cbTransparent.Location = new System.Drawing.Point(652, 13);
            this.cbTransparent.ManualButtonClick = true;
            this.cbTransparent.Name = "cbTransparent";
            this.cbTransparent.Size = new System.Drawing.Size(23, 23);
            this.cbTransparent.TabIndex = 39;
            this.ttMain.SetToolTip(this.cbTransparent, this.CurrentText(lblAlpha));
            this.cbTransparent.UseVisualStyleBackColor = true;
            this.cbTransparent.Click += new System.EventHandler(this.cbTransparent_Click);
            // 
            // btnClose
            // 
            this.btnClose.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.btnClose.Location = new System.Drawing.Point(576, 296);
            this.btnClose.Size = new System.Drawing.Size(96, 32);
            this.btnClose.TabIndex = 43;
            this.btnClose.Text = "关闭";
            this.btnClose.UseVisualStyleBackColor = true;
            this.btnClose.Visible = false;
            this.btnClose.Click += new System.EventHandler(this.btnClose_Click);
            // 
            // flpColorPalette
            // 
            this.flpColorPalette.Location = new System.Drawing.Point(8, 293);
            this.flpColorPalette.Name = "flpColorPalette";
            this.flpColorPalette.Size = new System.Drawing.Size(296, 40);
            this.flpColorPalette.TabIndex = 0;
            // 
            // rbStandardColors
            // 
            this.rbStandardColors.AutoSize = true;
            this.rbStandardColors.Checked = true;
            this.rbStandardColors.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.rbStandardColors.Location = new System.Drawing.Point(110, 3);
            this.rbStandardColors.Name = "rbStandardColors";
            this.rbStandardColors.Size = new System.Drawing.Size(113, 16);
            this.rbStandardColors.TabIndex = 47;
            this.rbStandardColors.TabStop = true;
            this.rbStandardColors.Text = "标准颜色";
            this.rbStandardColors.UseVisualStyleBackColor = true;
            // 
            // flpColorPaletteSelection
            // 
            this.flpColorPaletteSelection.AutoSize = true;
            this.flpColorPaletteSelection.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.flpColorPaletteSelection.Controls.Add(this.rbRecentColors);
            this.flpColorPaletteSelection.Controls.Add(this.rbStandardColors);
            this.flpColorPaletteSelection.Location = new System.Drawing.Point(6, 269);
            this.flpColorPaletteSelection.Name = "flpColorPaletteSelection";
            this.flpColorPaletteSelection.Size = new System.Drawing.Size(226, 22);
            this.flpColorPaletteSelection.TabIndex = 48;
            // 
            // rbRecentColors
            // 
            this.rbRecentColors.AutoSize = true;
            this.rbRecentColors.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.rbRecentColors.Location = new System.Drawing.Point(3, 3);
            this.rbRecentColors.Name = "rbRecentColors";
            this.rbRecentColors.Size = new System.Drawing.Size(101, 16);
            this.rbRecentColors.TabIndex = 46;
            this.rbRecentColors.Text = "最近使用颜色";
            this.rbRecentColors.UseVisualStyleBackColor = true;
            this.rbRecentColors.CheckedChanged += new System.EventHandler(this.rbRecentColors_CheckedChanged);
            // 
            // lblName
            // 
            this.lblName.AutoSize = true;
            this.lblName.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblName.Location = new System.Drawing.Point(309, 208);
            this.lblName.Name = "lblName";
            this.lblName.Size = new System.Drawing.Size(35, 12);
            this.lblName.TabIndex = 49;
            this.lblName.Text = "名称：";
            // 
            // lblNameValue
            // 
            this.lblNameValue.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.lblNameValue.Location = new System.Drawing.Point(355, 205);
            this.lblNameValue.Name = "lblNameValue";
            this.lblNameValue.Size = new System.Drawing.Size(112, 23);
            this.lblNameValue.TabIndex = 50;
            this.lblNameValue.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btnClipboardStatus
            // 
            this.btnClipboardStatus.AutoSize = true;
            this.btnClipboardStatus.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.btnClipboardStatus.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnClipboardStatus.Image = global::OCRTools.Properties.Resources.tick;
            this.btnClipboardStatus.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.btnClipboardStatus.Location = new System.Drawing.Point(436, 264);
            this.btnClipboardStatus.Name = "btnClipboardStatus";
            this.btnClipboardStatus.Size = new System.Drawing.Size(8, 8);
            this.btnClipboardStatus.TabIndex = 53;
            this.btnClipboardStatus.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btnClipboardStatus.UseVisualStyleBackColor = true;
            this.btnClipboardStatus.Visible = false;
            // 
            // mbCopy
            // 
            this.btnCopy.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.btnCopy.Location = new System.Drawing.Point(472, 296);
            this.btnCopy.Size = new System.Drawing.Size(96, 32);
            this.btnCopy.TabIndex = 40;
            this.btnCopy.Text = "复制";
            this.btnCopy.Click += BtnCopy_Click;
            this.btnCopy.UseVisualStyleBackColor = true;
            this.btnCopy.Visible = false;
            // 
            // pbColorPreview
            // 
            this.pbColorPreview.BackColor = System.Drawing.SystemColors.Window;
            this.pbColorPreview.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.pbColorPreview.DrawCheckeredBackground = true;
            this.pbColorPreview.Location = new System.Drawing.Point(408, 232);
            this.pbColorPreview.Name = "pbColorPreview";
            this.pbColorPreview.PictureBoxBackColor = System.Drawing.SystemColors.Window;
            this.pbColorPreview.Size = new System.Drawing.Size(48, 48);
            this.pbColorPreview.TabIndex = 38;
            this.pbColorPreview.MouseClick += new System.Windows.Forms.MouseEventHandler(this.pbColorPreview_MouseClick);
            // 
            // colorPicker
            // 
            this.colorPicker.AutoSize = true;
            this.colorPicker.DrawStyle = OCRTools.DrawStyle.Hue;
            this.colorPicker.Location = new System.Drawing.Point(8, 8);
            this.colorPicker.Name = "colorPicker";
            this.colorPicker.Size = new System.Drawing.Size(292, 261);
            this.colorPicker.TabIndex = 2;
            this.colorPicker.TabStop = false;
            this.colorPicker.ColorChanged += new OCRTools.ColorEventHandler(this.colorPicker_ColorChanged);
            // 
            // panel2
            // 
            this.panel2.BackColor = System.Drawing.Color.White;
            this.panel2.Controls.Add(this.cbTransparent);
            this.panel2.Controls.Add(this.nudCyan);
            this.panel2.Controls.Add(this.nudMagenta);
            this.panel2.Controls.Add(this.nudYellow);
            this.panel2.Controls.Add(this.nudKey);
            this.panel2.Controls.Add(this.nudAlpha);
            this.panel2.Controls.Add(this.lblKeyPerc);
            this.panel2.Controls.Add(this.txtHex);
            this.panel2.Controls.Add(this.lblYellowPerc);
            this.panel2.Controls.Add(this.lblMagentaPerc);
            this.panel2.Controls.Add(this.lblCyanPerc);
            this.panel2.Controls.Add(this.btnClipboardStatus);
            this.panel2.Controls.Add(this.pbColorPreview);
            this.panel2.Controls.Add(this.lblNameValue);
            this.panel2.Controls.Add(this.nudHue);
            this.panel2.Controls.Add(this.nudSaturation);
            this.panel2.Controls.Add(this.nudBrightness);
            this.panel2.Controls.Add(this.nudBlue);
            this.panel2.Controls.Add(this.nudGreen);
            this.panel2.Controls.Add(this.nudRed);
            this.panel2.Controls.Add(this.colorPicker);
            this.panel2.Controls.Add(this.rbHue);
            this.panel2.Controls.Add(this.rbSaturation);
            this.panel2.Controls.Add(this.lblName);
            this.panel2.Controls.Add(this.rbBrightness);
            this.panel2.Controls.Add(this.flpColorPalette);
            this.panel2.Controls.Add(this.flpColorPaletteSelection);
            this.panel2.Controls.Add(this.btnClose);
            this.panel2.Controls.Add(this.lblSaturationPerc);
            this.panel2.Controls.Add(this.btnCopy);
            this.panel2.Controls.Add(this.lblBrightnessPerc);
            this.panel2.Controls.Add(this.lblHue);
            this.panel2.Controls.Add(this.lblCyan);
            this.panel2.Controls.Add(this.lblMagenta);
            this.panel2.Controls.Add(this.lblYellow);
            this.panel2.Controls.Add(this.rbBlue);
            this.panel2.Controls.Add(this.lblKey);
            this.panel2.Controls.Add(this.rbGreen);
            this.panel2.Controls.Add(this.rbRed);
            this.panel2.Controls.Add(this.lblAlpha);
            this.panel2.Controls.Add(this.lblHex);
            this.panel2.Controls.Add(this.lblNew);
            this.panel2.Controls.Add(this.lblOld);
            this.panel2.Controls.Add(this.btnOK);
            this.panel2.Controls.Add(this.btnCancel);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel2.Location = new System.Drawing.Point(20, 60);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(637, 369);
            this.panel2.TabIndex = 54;
            // 
            // ColorPickerForm
            // 
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(737, 439);
            this.Controls.Add(this.panel2);
            this.MaximizeBox = false;
            this.Name = "ColorPickerForm";
            this.Text = "调色板";
            this.Shown += new System.EventHandler(this.ColorPickerForm_Shown);
            ((System.ComponentModel.ISupportInitialize)(this.nudKey)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudYellow)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudMagenta)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudCyan)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudBlue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudGreen)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudRed)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudBrightness)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudSaturation)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudHue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudAlpha)).EndInit();
            this.flpColorPaletteSelection.ResumeLayout(false);
            this.flpColorPaletteSelection.PerformLayout();
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion Windows Form Designer generated code

        private System.Windows.Forms.Label lblOld;
        private System.Windows.Forms.Label lblNew;
        private System.Windows.Forms.Label lblHex;
        private System.Windows.Forms.NumericUpDown nudKey;
        private System.Windows.Forms.NumericUpDown nudYellow;
        private System.Windows.Forms.NumericUpDown nudMagenta;
        private System.Windows.Forms.NumericUpDown nudCyan;
        private System.Windows.Forms.Label lblKey;
        private System.Windows.Forms.Label lblYellow;
        private System.Windows.Forms.Label lblMagenta;
        private System.Windows.Forms.Label lblCyan;
        private System.Windows.Forms.Label lblHue;
        private System.Windows.Forms.Label lblBrightnessPerc;
        private System.Windows.Forms.Label lblSaturationPerc;
        private System.Windows.Forms.NumericUpDown nudBlue;
        private System.Windows.Forms.NumericUpDown nudGreen;
        private System.Windows.Forms.NumericUpDown nudRed;
        private System.Windows.Forms.NumericUpDown nudBrightness;
        private System.Windows.Forms.NumericUpDown nudSaturation;
        private System.Windows.Forms.NumericUpDown nudHue;
        private System.Windows.Forms.RadioButton rbBlue;
        private System.Windows.Forms.RadioButton rbGreen;
        private System.Windows.Forms.RadioButton rbRed;
        private System.Windows.Forms.RadioButton rbBrightness;
        private System.Windows.Forms.RadioButton rbSaturation;
        private System.Windows.Forms.RadioButton rbHue;
        private System.Windows.Forms.Label lblCyanPerc;
        private System.Windows.Forms.Label lblMagentaPerc;
        private System.Windows.Forms.Label lblYellowPerc;
        private System.Windows.Forms.Label lblKeyPerc;
        private System.Windows.Forms.NumericUpDown nudAlpha;
        private System.Windows.Forms.Label lblAlpha;
        private MyPictureBox pbColorPreview;
        protected ColorPicker colorPicker;
        protected System.Windows.Forms.TextBox txtHex;
        protected System.Windows.Forms.Button btnCancel;
        protected System.Windows.Forms.Button btnOK;
        private ColorButton cbTransparent;
        private System.Windows.Forms.ToolTip ttMain;
        private SkinButton btnCopy;
        private SkinButton btnClose;
        private System.Windows.Forms.FlowLayoutPanel flpColorPalette;
        private System.Windows.Forms.RadioButton rbStandardColors;
        private System.Windows.Forms.FlowLayoutPanel flpColorPaletteSelection;
        private System.Windows.Forms.Label lblName;
        private System.Windows.Forms.Label lblNameValue;
        private System.Windows.Forms.Button btnClipboardStatus;
        private System.Windows.Forms.RadioButton rbRecentColors;
        private System.Windows.Forms.Panel panel2;
    }
}