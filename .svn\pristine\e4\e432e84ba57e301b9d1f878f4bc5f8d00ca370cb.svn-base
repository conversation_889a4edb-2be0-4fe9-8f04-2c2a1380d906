﻿using System;
using System.Windows.Forms;

namespace OCRTools
{
    public class ControlHider : IDisposable
    {
        private readonly Timer timer;

        public ControlHider(Control control, int autoHideTime)
        {
            Control = control;
            AutoHideTime = autoHideTime;

            timer = new Timer
            {
                Interval = AutoHideTime
            };
            timer.Tick += Timer_Tick;
        }

        public Control Control { get; }
        public int AutoHideTime { get; }

        public void Dispose()
        {
            timer?.Dispose();
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            timer.Stop();

            if (Control != null && !Control.IsDisposed) Control.Visible = false;
        }

        public void Show()
        {
            if (Control != null && !Control.IsDisposed)
            {
                Control.Visible = true;

                timer.Stop();
                timer.Start();
            }
        }
    }
}