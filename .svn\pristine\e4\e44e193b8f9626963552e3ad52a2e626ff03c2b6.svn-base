﻿using OCRTools.Common;
using System.Collections.Generic;
using System.Reflection;

namespace OCRTools
{
    public class IpHelper
    {
        /// <summary>
        /// 返回经纬度及详细地址
        /// </summary>
        /// <returns></returns>
        public static GeoEntity GetGeoEntity()
        {
            var geo = GetQQGeo();
            if (string.IsNullOrEmpty(geo.province))
            {
                geo = GetBaiDuGeo();
            }
            return geo;
        }

        /// <summary>
        /// 返回详细地址（不一定有经纬度）
        /// </summary>
        /// <returns></returns>
        public static GeoEntity GetLocation()
        {
            var geo = GetQQLocation();
            if (string.IsNullOrEmpty(geo.province))
            {
                geo = GetQQGeo();
            }
            if (string.IsNullOrEmpty(geo.province))
            {
                geo = GetBaiDuGeo();
            }
            return geo;
        }

        private const string STR_QQIP_URL = "https://apis.map.qq.com/ws/location/v1/ip?key=";
        private static List<string> lstQQKey = new List<string>() { "3BFBZ-ZKD3X-LW54A-ZT76D-E7AHO-4RBD5", "COQBZ-K3ZR6-46VSJ-ETBLM-JKJ6O-PDFLZ", "PTMBZ-GCQLW-SC2RG-R2FNI-HWPNQ-4PBQM", "XOXBZ-MZWWD-CDX4H-PONXN-UA5PJ-D7FJN", "NUMBZ-QW4RF-CLQJI-J5A77-XIRM3-26BWY", "CQPBZ-QRWCU-2RGVJ-44CGR-5WCY6-WVB5Q", "MJABZ-YGGL5-IBIIN-Q5Z45-FMOI7-VIBGN", "RSOBZ-A43CQ-PH25E-GMBDH-KKDMK-GZF53", "F2GBZ-SREWQ-A3K56-GSLK5-ELOHS-PRB2X", "IGNBZ-RNYHP-DPKDR-LLOVQ-FUKM6-HQBFY", "KEUBZ-5LQWP-ORXDU-VSN5L-277L3-NVFZM", "4VQBZ-ZGO3G-VGSQE-ILN4G-LWFUK-5WB7H", "RLHBZ-WMPRP-Q3JDS-V2IQA-JNRFH-EJBHL", "RRXBZ-WC6KF-ZQSJT-N2QU7-T5QIT-6KF5X", "OHTBZ-7IFRG-JG2QF-IHFUK-XTTK6-VXFBN", "EIMBZ-RBNH4-NZQUG-X7RKQ-3SPPH-YSFCX", "SGVBZ-4RO34-NB2US-DQKYK-ZEUVJ-4KFZF" };
        private static GeoEntity GetQQGeo()
        {
            var geo = new GeoEntity();
            var html = WebClientExt.GetHtml(STR_QQIP_URL + lstQQKey.GetRndItem()).Trim();
            if (!string.IsNullOrEmpty(html))
            {
                try
                {
                    var qqRoot = CommonString.JavaScriptSerializer.Deserialize<QQRootObject>(html);
                    geo.province = qqRoot.result.ad_info.province;
                    geo.city = qqRoot.result.ad_info.city;
                    geo.district = qqRoot.result.ad_info.district;
                    geo.lat = qqRoot.result.location.lat;
                    geo.lng = qqRoot.result.location.lng;
                }
                catch { }
            }
            return geo;
        }

        private const string STR_QQIP_URL_2 = "https://r.inews.qq.com/api/ip2city?otype=json";
        private static GeoEntity GetQQLocation()
        {
            var geo = new GeoEntity();
            var html = WebClientExt.GetHtml(STR_QQIP_URL_2).Trim();
            if (!string.IsNullOrEmpty(html))
            {
                try
                {
                    var qqRoot = CommonString.JavaScriptSerializer.Deserialize<QQArea>(html);
                    geo.province = qqRoot.province;
                    geo.city = qqRoot.city;
                    geo.district = qqRoot.district;
                }
                catch { }
            }
            return geo;
        }

        private const string STR_BAIDU_IP_URL = "https://api.map.baidu.com/location/ip?coor=bd09ll&ak=";
        private static List<string> lstBaiDuKey = new List<string>() { "Er8iGG4UMfSd3Ckuc6w8C56peI4ge1Ih", "1jyKeVBnklxsB2Zyduy6wXdnwWEPUjaZ", "dYg9wxs2v2xfaNEpTmcR4S6jZ0idkHN6", "f1BGUFqiutpquxc42GBoamR82hGMCAMG", "T0IxOVsyHiwtTfZRxdyzNH5C", "20480eb5ebe60b5b3c12178317758876", "I130uPke2NZmsb4nobIRcoyOt2CrHsUr", "9084bacfc2d6d9f9617758a3b954b040", "FyY5v7kEdkbMHLYoUOsuXBAK", "E2b1f66bfca15db829af548a3267d54d", "x2U7MvdIBQRn174kZFGaqqFZOPXFHueb", "K2SUGMBIRN98Nxhx4Yfo6vyqx3QTrarE", "zOPoKOjOvYQlwPSFjN3i6mKzc3rQ8Wnm", "********************************", "Y8ZEquaZfYlL6CuGRbwaV8DC", "0Qx1rK7eywvOwpHN4tXaQcAUQmWs8Niy", "fjke3YUipM9N64GdOIh1DNeK2APO2WcT", "0jKbOcwqK7dGZiYIhSai5rsxTnQZ4UQt", "6lvVPMDlm2gjLpU0aiqPsHXi2OiwGQRj", "aTETpT7NGwDnUrTf7bROng6SttoQEv6O", "C73357a276668f8b0563d3f936475007", "Hc6fBVaQUl3tRc6uHYlvpZIea7pwhGui", "08fadd5a7e397b10f4599c325ee55b9c" };
        private static GeoEntity GetBaiDuGeo()
        {
            var geo = new GeoEntity();
            var html = WebClientExt.GetHtml(STR_BAIDU_IP_URL + lstBaiDuKey.GetRndItem()).Trim();
            if (!string.IsNullOrEmpty(html))
            {
                try
                {
                    var qqRoot = CommonString.JavaScriptSerializer.Deserialize<BaiDuRootObject>(html);
                    geo.province = qqRoot.content.address_detail.province;
                    geo.city = qqRoot.content.address_detail.city;
                    geo.district = qqRoot.content.address_detail.district;
                    geo.lat = qqRoot.content.point.y;
                    geo.lng = qqRoot.content.point.x;
                }
                catch { }
            }
            return geo;
        }
    }

    #region BaiDu GeoEntity

    [Obfuscation]
    public class Address_detail
    {
        [Obfuscation]
        public string province { get; set; }
        [Obfuscation]
        public string city { get; set; }
        [Obfuscation]
        public string district { get; set; }
    }

    [Obfuscation]
    public class BaiDuPoint
    {
        [Obfuscation]
        public double x { get; set; }
        [Obfuscation]
        public double y { get; set; }
    }

    [Obfuscation]
    public class Content
    {
        [Obfuscation]
        public Address_detail address_detail { get; set; }
        [Obfuscation]
        public BaiDuPoint point { get; set; }
    }

    [Obfuscation]
    public class BaiDuRootObject
    {
        [Obfuscation]
        public Content content { get; set; }
    }
    #endregion

    #region QQ GeoEntity

    [Obfuscation]
    public class QQLocation
    {

        [Obfuscation]
        public double lat { get; set; }

        [Obfuscation]
        public double lng { get; set; }
    }

    [Obfuscation]
    public class QQArea
    {

        [Obfuscation]
        public string province { get; set; }

        [Obfuscation]
        public string city { get; set; }

        [Obfuscation]
        public string district { get; set; }
    }

    [Obfuscation]
    public class QQResult
    {

        [Obfuscation]
        public QQLocation location { get; set; }

        [Obfuscation]
        public QQArea ad_info { get; set; }
    }

    [Obfuscation]
    public class QQRootObject
    {
        [Obfuscation]
        public QQResult result { get; set; }
    }
    #endregion

    public class GeoEntity
    {
        public double lat { get; set; }

        public double lng { get; set; }

        public string province { get; set; }

        public string city { get; set; }

        public string district { get; set; }
    }
}
