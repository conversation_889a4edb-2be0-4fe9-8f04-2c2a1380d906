﻿using MetroFramework.Components;
using MetroFramework.Controls;
using System.ComponentModel;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmMain
    {

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private IContainer components = null;
        private MetroTabControl tbMain;
        private MetroTabPage tbContentText;
        private MetroTabPage tbImageBox;
        private NotifyIcon notifyMain;
        private MetroContextMenu cmsNotify;
        private ToolStripMenuItem tsmShowMain;
        private ToolStripMenuItem 截图合集toolStripMenuItem;
        private ToolStripMenuItem 工具合集toolStripMenuItem;
        private ToolStripMenuItem 识别合集toolStripMenuItem;
        private ToolStripMenuItem 截图识别toolStripMenuItem;
        private ToolStripMenuItem 粘贴识别ToolStripMenuItem;
        private ToolStripMenuItem 文件识别ToolStripMenuItem;
        private ToolStripMenuItem 批量识别ToolStripMenuItem;
        private ToolStripMenuItem 识别历史ToolStripMenuItem;
        private ToolStripMenuItem tsmExit;
        private ToolStripMenuItem 系统设置SToolStripMenuItem;
        private ToolStripSeparator toolStripSeparator1;
        private ToolStripSeparator tsmOcrGroupSpilt;
        private StatusStrip stateStip;
        private ToolStripDropDownButton tmsSpiltMode;
        private ToolStripMenuItem 取色器toolStripMenuItem1;
        private ToolStripMenuItem 调色板toolStripMenuItem1;
        private ToolStripMenuItem 标尺工具toolStripMenuItem1;
        private ToolStripDropDownButton tsmContentType;
        private UcContent content;
        private ToolStripDropDownButton tsmLeftToRight;
        private ToolStripMenuItem 从右到左ToolStripMenuItem;
        private ToolStripMenuItem 从左到右ToolStripMenuItem;
        private ToolStripDropDownButton tsmTopToDown;
        private ToolStripMenuItem toolStripMenuItem2;
        private ToolStripMenuItem toolStripMenuItem16;
        private ToolStripMenuItem ocrGroupTypeToolStripMenuItem;
        private ToolStripSeparator toolStripSeparator3;
        private ToolStripSeparator toolStripSeparator4;
        private LinkLabel lnkLogin;
        private MetroStyleManager msManager;
        private Panel pnlTop;
        private MetroFramework.Components.MetroToolTip tipMain;
        private Panel pnlSetting;

        private UcLoading ucLoading1;
        private ToolStripMenuItem tsmShowTool;
        private ToolStripMenuItem tsmToolImage;
        private ToolStripMenuItem tsmExportExcel;
        private ToolStripMenuItem tsmExportTxt;
        private ToolStripMenuItem tsmCopyTxt;
        private ToolStripSeparator tsmSpiltExport;

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FrmMain));
            this.tbMain = new MetroFramework.Controls.MetroTabControl();
            this.tbContentText = new MetroFramework.Controls.MetroTabPage();
            this.content = new OCRTools.UcContent();
            this.tbImageBox = new MetroFramework.Controls.MetroTabPage();
            this.imageBox = new OCRTools.UcPicView();
            this.msManager = new MetroFramework.Components.MetroStyleManager(this.components);
            this.notifyMain = new System.Windows.Forms.NotifyIcon(this.components);
            this.cmsNotify = new MetroFramework.Controls.MetroContextMenu(this.components);
            this.tsmCopyTxt = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmSearch = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmTrans = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmExportTxt = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmSpiltExport = new System.Windows.Forms.ToolStripSeparator();
            this.截图合集toolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.截图识别toolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.粘贴识别ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.文件识别ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.批量识别ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.识别历史ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.工具合集toolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.识别合集toolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.取色器toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.调色板toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.标尺工具toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator5 = new System.Windows.Forms.ToolStripSeparator();
            this.toolStripSeparator6 = new System.Windows.Forms.ToolStripSeparator();
            this.ocrGroupTypeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmOcrGroupSpilt = new System.Windows.Forms.ToolStripSeparator();
            this.tsmShowMain = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmShowTool = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmToolImage = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.网络检测ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.系统设置SToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmExit = new System.Windows.Forms.ToolStripMenuItem();
            this.stateStip = new System.Windows.Forms.StatusStrip();
            this.tsmContentType = new System.Windows.Forms.ToolStripDropDownButton();
            this.tsmPicViewModel = new System.Windows.Forms.ToolStripButton();
            this.tsmBiaoDian = new System.Windows.Forms.ToolStripDropDownButton();
            this.tsmTransFrom = new System.Windows.Forms.ToolStripDropDownButton();
            this.tsmTransTo = new System.Windows.Forms.ToolStripDropDownButton();
            this.tsmTopToDown = new System.Windows.Forms.ToolStripDropDownButton();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem16 = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmLeftToRight = new System.Windows.Forms.ToolStripDropDownButton();
            this.从右到左ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.从左到右ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.tmsSpiltMode = new System.Windows.Forms.ToolStripDropDownButton();
            this.btnVoice = new System.Windows.Forms.ToolStripSplitButton();
            this.tsmShowOldContent = new OCRTools.ToolStripCheckBoxControl();
            this.tsmAutoTrans = new OCRTools.ToolStripCheckBoxControl();
            this.lnkLogin = new System.Windows.Forms.LinkLabel();
            this.tipMain = new MetroFramework.Components.MetroToolTip();
            this.pnlSetting = new System.Windows.Forms.Panel();
            this.pnlTop = new System.Windows.Forms.Panel();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.toolStripSeparator4 = new System.Windows.Forms.ToolStripSeparator();
            this.wbVoice = new System.Windows.Forms.WebBrowser();
            this.picUser = new PictureBox();
            this.ucLoading1 = new OCRTools.UcLoading();
            this.关于我们ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.tbMain.SuspendLayout();
            this.tbContentText.SuspendLayout();
            this.tbImageBox.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.msManager)).BeginInit();
            //this.cmsNotify.SuspendLayout();
            this.stateStip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.picUser)).BeginInit();
            this.SuspendLayout();
            // 
            // tbMain
            // 
            this.tbMain.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tbMain.Controls.Add(this.tbContentText);
            this.tbMain.Controls.Add(this.tbImageBox);
            this.tbMain.FontSize = MetroFramework.MetroTabControlSize.Tall;
            this.tbMain.FontWeight = MetroFramework.MetroTabControlWeight.Regular;
            this.tbMain.HotTrack = true;
            this.tbMain.Location = new System.Drawing.Point(1, 31);
            this.tbMain.Name = "tbMain";
            this.tbMain.Padding = new System.Drawing.Point(6, 8);
            this.tbMain.SelectedIndex = 0;
            this.tbMain.ShowToolTips = true;
            this.tbMain.Size = new System.Drawing.Size(453, 335);
            this.tbMain.Style = MetroFramework.MetroColorStyle.黑色;
            this.tbMain.TabIndex = 4;
            this.tbMain.TabStop = false;
            this.tbMain.UseSelectable = true;
            // 
            // tbContentText
            // 
            this.tbContentText.Controls.Add(this.content);
            this.tbContentText.HorizontalScrollbarBarColor = true;
            this.tbContentText.HorizontalScrollbarHighlightOnWheel = false;
            this.tbContentText.HorizontalScrollbarSize = 10;
            this.tbContentText.Location = new System.Drawing.Point(4, 44);
            this.tbContentText.Margin = new System.Windows.Forms.Padding(0);
            this.tbContentText.Name = "tbContentText";
            this.tbContentText.Padding = new System.Windows.Forms.Padding(3);
            this.tbContentText.Size = new System.Drawing.Size(445, 287);
            this.tbContentText.Style = MetroFramework.MetroColorStyle.黑色;
            this.tbContentText.TabIndex = 0;
            this.tbContentText.Tag = "识别内容";
            this.tbContentText.Text = "【识别内容】";
            this.tbContentText.UseVisualStyleBackColor = true;
            this.tbContentText.VerticalScrollbarBarColor = true;
            this.tbContentText.VerticalScrollbarHighlightOnWheel = false;
            this.tbContentText.VerticalScrollbarSize = 10;
            // 
            // content
            // 
            this.content.BiaoDianMode = OCRTools.Common.BiaoDianMode.中文;
            this.content.Dock = System.Windows.Forms.DockStyle.Fill;
            this.content.Image = null;
            this.content.IsShowOldContent = true;
            this.content.Location = new System.Drawing.Point(3, 3);
            this.content.Margin = new System.Windows.Forms.Padding(0);
            this.content.MenuStrip = null;
            this.content.Name = "content";
            this.content.NowDisplayMode = OCRTools.DisplayModel.图文模式;
            this.content.OcrContent = null;
            this.content.Size = new System.Drawing.Size(439, 281);
            this.content.SpiltModel = OCRTools.Common.SpiltMode.自动分段;
            this.content.TabIndex = 0;
            // 
            // tbImageBox
            // 
            this.tbImageBox.Controls.Add(this.imageBox);
            this.tbImageBox.HorizontalScrollbarBarColor = true;
            this.tbImageBox.HorizontalScrollbarHighlightOnWheel = false;
            this.tbImageBox.HorizontalScrollbarSize = 10;
            this.tbImageBox.Location = new System.Drawing.Point(4, 36);
            this.tbImageBox.Margin = new System.Windows.Forms.Padding(0);
            this.tbImageBox.Name = "tbImageBox";
            this.tbImageBox.Padding = new System.Windows.Forms.Padding(3);
            this.tbImageBox.Size = new System.Drawing.Size(445, 295);
            this.tbImageBox.Style = MetroFramework.MetroColorStyle.黑色;
            this.tbImageBox.TabIndex = 1;
            this.tbImageBox.Tag = "图片预览";
            this.tbImageBox.Text = "图片预览";
            this.tbImageBox.UseVisualStyleBackColor = true;
            this.tbImageBox.VerticalScrollbarBarColor = true;
            this.tbImageBox.VerticalScrollbarHighlightOnWheel = false;
            this.tbImageBox.VerticalScrollbarSize = 10;
            this.tbImageBox.Visible = false;
            // 
            // imageBox
            // 
            this.imageBox.Dock = System.Windows.Forms.DockStyle.Fill;
            this.imageBox.Location = new System.Drawing.Point(3, 3);
            this.imageBox.Margin = new System.Windows.Forms.Padding(0);
            this.imageBox.Name = "imageBox";
            this.imageBox.Size = new System.Drawing.Size(439, 289);
            this.imageBox.TabIndex = 2;
            this.imageBox.Zoom = 100;
            // 
            // msManager
            // 
            this.msManager.Owner = this;
            // 
            // notifyMain
            // 
            this.notifyMain.BalloonTipText = "双击显示主窗体";
            this.notifyMain.BalloonTipTitle = Application.ProductName;
            this.notifyMain.Text = Application.ProductName;
            this.notifyMain.Visible = true;
            this.notifyMain.BalloonTipClicked += NotifyMain_BalloonTipClicked;
            this.notifyMain.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.notifyMain_MouseDoubleClick);
            //
            // cms工具集合
            //
            this.工具合集toolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] {
            this.取色器toolStripMenuItem1,
            this.调色板toolStripMenuItem1,
            this.标尺工具toolStripMenuItem1,
            this.网络检测ToolStripMenuItem
            });
            //
            // 识别合集toolStripMenuItem
            //
            this.识别合集toolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[]
            {
            this.截图识别toolStripMenuItem,
            this.粘贴识别ToolStripMenuItem,
            this.文件识别ToolStripMenuItem,
            this.批量识别ToolStripMenuItem
            });
            // 
            // cmsNotify
            // 
            this.cmsNotify.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmCopyTxt,
            this.tsmSearch,
            this.tsmTrans,
            this.tsmExportExcel,
            this.tsmExportTxt,
            this.tsmSpiltExport,
            this.截图合集toolStripMenuItem,
            this.识别合集toolStripMenuItem,
            this.工具合集toolStripMenuItem,
            this.toolStripSeparator5,
            this.ocrGroupTypeToolStripMenuItem,
            this.识别历史ToolStripMenuItem,
            this.tsmOcrGroupSpilt,
            this.tsmShowMain,
            this.tsmShowTool,
            this.tsmToolImage,
            this.toolStripSeparator1,
            this.系统设置SToolStripMenuItem,
            this.关于我们ToolStripMenuItem,
            this.tsmExit});
            this.cmsNotify.Name = "标题";
            this.cmsNotify.Size = new System.Drawing.Size(181, 496);
            this.cmsNotify.Style = MetroFramework.MetroColorStyle.黑色;
            // 
            // tsmCopyTxt
            // 
            this.tsmCopyTxt.Image = global::OCRTools.Properties.Resources.复制;
            this.tsmCopyTxt.Name = "tsmCopyTxt";
            this.tsmCopyTxt.Size = new System.Drawing.Size(191, 22);
            this.tsmCopyTxt.Text = "复制文本";
            this.tsmCopyTxt.Visible = false;
            this.tsmCopyTxt.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // tsmSearch
            // 
            this.tsmSearch.Image = global::OCRTools.Properties.Resources.搜索;
            this.tsmSearch.Name = "tsmSearch";
            this.tsmSearch.Size = new System.Drawing.Size(191, 22);
            this.tsmSearch.Text = "搜索选中文字";
            this.tsmSearch.Visible = false;
            this.tsmSearch.Click += new System.EventHandler(this.tsmSearch_Click);
            // 
            // tsmTrans
            // 
            this.tsmTrans.Image = global::OCRTools.Properties.Resources.翻译;
            this.tsmTrans.Name = "tsmTrans";
            this.tsmTrans.Size = new System.Drawing.Size(191, 22);
            this.tsmTrans.Text = "翻译选中文字";
            this.tsmTrans.Visible = false;
            this.tsmTrans.Click += new System.EventHandler(this.tsmTrans_Click);
            // 
            // tsmExportExcel
            // 
            this.tsmExportExcel.Image = global::OCRTools.Properties.Resources.excel;
            this.tsmExportExcel.Name = "tsmExportExcel";
            this.tsmExportExcel.Size = new System.Drawing.Size(191, 22);
            this.tsmExportExcel.Text = "导出Excel";
            this.tsmExportExcel.Visible = false;
            this.tsmExportExcel.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // tsmExportTxt
            // 
            this.tsmExportTxt.Image = global::OCRTools.Properties.Resources.导出;
            this.tsmExportTxt.Name = "tsmExportTxt";
            this.tsmExportTxt.Size = new System.Drawing.Size(191, 22);
            this.tsmExportTxt.Text = "保存文本";
            this.tsmExportTxt.Visible = false;
            this.tsmExportTxt.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // tsmSpiltExport
            // 
            this.tsmSpiltExport.Name = "tsmSpiltExport";
            this.tsmSpiltExport.Size = new System.Drawing.Size(188, 6);
            this.tsmSpiltExport.Visible = false;
            // 
            // 截图合集toolStripMenuItem
            // 
            this.截图合集toolStripMenuItem.Image = global::OCRTools.Properties.Resources.camera;
            this.截图合集toolStripMenuItem.Name = "截图合集toolStripMenuItem";
            this.截图合集toolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.截图合集toolStripMenuItem.Text = "截图";
            this.截图合集toolStripMenuItem.Click += new System.EventHandler(this.截图ToolStripMenuItem_Click);
            // 
            // 识别合集toolStripMenuItem
            // 
            this.识别合集toolStripMenuItem.Image = global::OCRTools.Properties.Resources.文本;
            this.识别合集toolStripMenuItem.Name = "识别合集toolStripMenuItem";
            this.识别合集toolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.识别合集toolStripMenuItem.Text = "识别";
            this.识别合集toolStripMenuItem.Click += new System.EventHandler(this.截图识别ToolStripMenuItem_Click);
            // 
            // 工具合集toolStripMenuItem
            // 
            this.工具合集toolStripMenuItem.Image = global::OCRTools.Properties.Resources.toolbox;
            this.工具合集toolStripMenuItem.Name = "tsmTools";
            this.工具合集toolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.工具合集toolStripMenuItem.Text = "工具";
            // 
            // 截图识别toolStripMenuItem
            // 
            this.截图识别toolStripMenuItem.Image = global::OCRTools.Properties.Resources.截图;
            this.截图识别toolStripMenuItem.Name = "截图识别toolStripMenuItem";
            this.截图识别toolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.截图识别toolStripMenuItem.Text = "截图识别";
            this.截图识别toolStripMenuItem.Click += new System.EventHandler(this.截图识别ToolStripMenuItem_Click);
            // 
            // 粘贴识别ToolStripMenuItem
            // 
            this.粘贴识别ToolStripMenuItem.Image = global::OCRTools.Properties.Resources.粘贴;
            this.粘贴识别ToolStripMenuItem.Name = "粘贴识别ToolStripMenuItem";
            this.粘贴识别ToolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.粘贴识别ToolStripMenuItem.Text = "粘贴识别";
            this.粘贴识别ToolStripMenuItem.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // 文件识别ToolStripMenuItem
            // 
            this.文件识别ToolStripMenuItem.Image = global::OCRTools.Properties.Resources.文件;
            this.文件识别ToolStripMenuItem.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.文件识别ToolStripMenuItem.Name = "文件识别ToolStripMenuItem";
            this.文件识别ToolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.文件识别ToolStripMenuItem.Text = "文件识别";
            this.文件识别ToolStripMenuItem.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // tsmCaptureColor
            // 
            this.取色器toolStripMenuItem1.Image = global::OCRTools.Properties.Resources.pipette;
            this.取色器toolStripMenuItem1.Name = "tsmCaptureColor";
            this.取色器toolStripMenuItem1.Size = new System.Drawing.Size(191, 22);
            this.取色器toolStripMenuItem1.Text = "取色器";
            this.取色器toolStripMenuItem1.Click += new System.EventHandler(this.取色器ToolStripMenuItem_Click);
            // 
            // tsmCaptureColor
            // 
            this.调色板toolStripMenuItem1.Image = global::OCRTools.Properties.Resources.取色器;
            this.调色板toolStripMenuItem1.Name = "tsmCaptureColor";
            this.调色板toolStripMenuItem1.Size = new System.Drawing.Size(191, 22);
            this.调色板toolStripMenuItem1.Text = "调色板";
            this.调色板toolStripMenuItem1.Click += new System.EventHandler(this.调色板ToolStripMenuItem_Click);
            // 
            // tsmRuler
            // 
            this.标尺工具toolStripMenuItem1.Image = global::OCRTools.Properties.Resources.标尺;
            this.标尺工具toolStripMenuItem1.Name = "tsmRuler";
            this.标尺工具toolStripMenuItem1.Size = new System.Drawing.Size(191, 22);
            this.标尺工具toolStripMenuItem1.Text = "标尺工具";
            this.标尺工具toolStripMenuItem1.Click += new System.EventHandler(this.标尺工具ToolStripMenuItem_Click);
            // 
            // 识别历史ToolStripMenuItem
            // 
            this.识别历史ToolStripMenuItem.Image = global::OCRTools.Properties.Resources.历史;
            this.识别历史ToolStripMenuItem.Name = "识别历史ToolStripMenuItem";
            this.识别历史ToolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.识别历史ToolStripMenuItem.Text = "识别历史";
            this.识别历史ToolStripMenuItem.Click += new System.EventHandler(this.识别历史ToolStripMenuItem_Click);
            // 
            // 批量识别ToolStripMenuItem
            // 
            this.批量识别ToolStripMenuItem.Image = global::OCRTools.Properties.Resources.batch;
            this.批量识别ToolStripMenuItem.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.批量识别ToolStripMenuItem.Name = "批量识别ToolStripMenuItem";
            this.批量识别ToolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.批量识别ToolStripMenuItem.Text = "批量识别";
            this.批量识别ToolStripMenuItem.Click += new System.EventHandler(this.批量识别ToolStripMenuItem_Click);
            // 
            // toolStripSeparator5
            // 
            this.toolStripSeparator5.Name = "toolStripSeparator5";
            this.toolStripSeparator5.Size = new System.Drawing.Size(188, 6);
            // 
            // ocrGroupTypeToolStripMenuItem
            // 
            this.ocrGroupTypeToolStripMenuItem.Image = global::OCRTools.Properties.Resources.upload_cloud;
            this.ocrGroupTypeToolStripMenuItem.Name = "ocrGroupTypeToolStripMenuItem";
            this.ocrGroupTypeToolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.ocrGroupTypeToolStripMenuItem.Text = "识别引擎";
            // 
            // tsmOcrGroupSpilt
            // 
            this.tsmOcrGroupSpilt.Name = "tsmOcrGroupSpilt";
            this.tsmOcrGroupSpilt.Size = new System.Drawing.Size(188, 6);
            // 
            // tsmShowMain
            // 
            this.tsmShowMain.Image = global::OCRTools.Properties.Resources.窗体;
            this.tsmShowMain.Name = "tsmShowMain";
            this.tsmShowMain.Size = new System.Drawing.Size(191, 22);
            this.tsmShowMain.Text = "显示窗体";
            this.tsmShowMain.Click += new System.EventHandler(this.tsmShowMain_Click);
            // 
            // tsmShowTool
            // 
            this.tsmShowTool.Image = global::OCRTools.Properties.Resources.开始;
            this.tsmShowTool.Name = "tsmShowTool";
            this.tsmShowTool.Size = new System.Drawing.Size(191, 22);
            this.tsmShowTool.Text = "显示工具栏";
            this.tsmShowTool.Click += new System.EventHandler(this.tsmShowTool_Click);
            // 
            // tsmToolImage
            // 
            this.tsmToolImage.Image = global::OCRTools.Properties.Resources.工具栏;
            this.tsmToolImage.Name = "tsmToolImage";
            this.tsmToolImage.Size = new System.Drawing.Size(191, 22);
            this.tsmToolImage.Text = "工具栏图片";
            this.tsmToolImage.Visible = false;
            this.tsmToolImage.Click += new System.EventHandler(this.系统设置SToolStripMenuItem_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(188, 6);
            // 
            // 网络检测ToolStripMenuItem
            // 
            this.网络检测ToolStripMenuItem.Image = global::OCRTools.Properties.Resources.globe;
            this.网络检测ToolStripMenuItem.Name = "网络检测ToolStripMenuItem";
            this.网络检测ToolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.网络检测ToolStripMenuItem.Text = "网络检测";
            this.网络检测ToolStripMenuItem.Click += new System.EventHandler(this.pnlNetWork_Click);
            // 
            // 系统设置SToolStripMenuItem
            // 
            this.系统设置SToolStripMenuItem.Image = global::OCRTools.Properties.Resources.设置;
            this.系统设置SToolStripMenuItem.Name = "系统设置SToolStripMenuItem";
            this.系统设置SToolStripMenuItem.Size = new System.Drawing.Size(191, 22);
            this.系统设置SToolStripMenuItem.Text = "系统设置";
            this.系统设置SToolStripMenuItem.Click += new System.EventHandler(this.系统设置SToolStripMenuItem_Click);
            // 
            // tsmExit
            // 
            this.tsmExit.Image = global::OCRTools.Properties.Resources.退出;
            this.tsmExit.Name = "tsmExit";
            this.tsmExit.Size = new System.Drawing.Size(191, 22);
            this.tsmExit.Text = "退出程序(&X)";
            this.tsmExit.Click += new System.EventHandler(this.tsmExit_Click);
            // 
            // stateStip
            // 
            this.stateStip.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.stateStip.AutoSize = false;
            this.stateStip.BackColor = System.Drawing.Color.Transparent;
            this.stateStip.Dock = System.Windows.Forms.DockStyle.None;
            this.stateStip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmContentType,
            this.tsmPicViewModel,
            this.tsmBiaoDian,
            this.tsmTransFrom,
            this.tsmTransTo,
            this.tsmTopToDown,
            this.tsmLeftToRight,
            this.tmsSpiltMode,
            this.btnVoice,
            this.tsmShowOldContent,
            this.tsmAutoTrans});
            this.stateStip.Location = new System.Drawing.Point(6, 357);
            this.stateStip.Name = "stateStip";
            this.stateStip.RenderMode = System.Windows.Forms.ToolStripRenderMode.Professional;
            this.stateStip.ShowItemToolTips = true;
            this.stateStip.Size = new System.Drawing.Size(444, 40);
            this.stateStip.TabIndex = 5;
            this.stateStip.Text = "statusStrip1";
            // 
            // tsmContentType
            // 
            this.tsmContentType.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmContentType.Font = new System.Drawing.Font("Microsoft YaHei UI", 10F);
            this.tsmContentType.ForeColor = System.Drawing.Color.Black;
            this.tsmContentType.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmContentType.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmContentType.Name = "tsmContentType";
            this.tsmContentType.Size = new System.Drawing.Size(13, 38);
            this.tsmContentType.Tag = "识别方式";
            this.tsmContentType.Text = "文本";
            this.tsmContentType.DropDownItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.tsmDDL_DropDownItemClicked);
            // 
            // tsmPicViewModel
            // 
            this.tsmPicViewModel.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmPicViewModel.ForeColor = System.Drawing.Color.Black;
            this.tsmPicViewModel.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmPicViewModel.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmPicViewModel.Name = "tsmPicViewModel";
            this.tsmPicViewModel.Size = new System.Drawing.Size(23, 38);
            this.tsmPicViewModel.Tag = "";
            this.tsmPicViewModel.Click += new System.EventHandler(this.tsmPicViewModel_Click);
            // 
            // tsmBiaoDian
            // 
            this.tsmBiaoDian.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmBiaoDian.ForeColor = System.Drawing.Color.Black;
            this.tsmBiaoDian.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmBiaoDian.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmBiaoDian.Name = "tsmBiaoDian";
            this.tsmBiaoDian.Size = new System.Drawing.Size(23, 38);
            this.tsmBiaoDian.Tag = "标点";
            // 
            // tsmTransFrom
            // 
            this.tsmTransFrom.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmTransFrom.ForeColor = System.Drawing.Color.Black;
            this.tsmTransFrom.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmTransFrom.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmTransFrom.Name = "tsmTransFrom";
            this.tsmTransFrom.Size = new System.Drawing.Size(13, 38);
            this.tsmTransFrom.Tag = "翻译语言From";
            this.tsmTransFrom.Text = "自动";
            this.tsmTransFrom.Visible = false;
            this.tsmTransFrom.DropDownItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.tsmDDL_DropDownItemClicked);
            // 
            // tsmTransTo
            // 
            this.tsmTransTo.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmTransTo.ForeColor = System.Drawing.Color.Black;
            this.tsmTransTo.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tsmTransTo.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmTransTo.Name = "tsmTransTo";
            this.tsmTransTo.Size = new System.Drawing.Size(13, 38);
            this.tsmTransTo.Tag = "翻译语言To";
            this.tsmTransTo.Text = "英文";
            this.tsmTransTo.Visible = false;
            this.tsmTransTo.DropDownItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.tsmDDL_DropDownItemClicked);
            // 
            // tsmTopToDown
            // 
            this.tsmTopToDown.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmTopToDown.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem2,
            this.toolStripMenuItem16});
            this.tsmTopToDown.ForeColor = System.Drawing.Color.Black;
            this.tsmTopToDown.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmTopToDown.Name = "tsmTopToDown";
            this.tsmTopToDown.Size = new System.Drawing.Size(13, 38);
            this.tsmTopToDown.Tag = "上下方向";
            this.tsmTopToDown.Text = "从上到下";
            this.tsmTopToDown.ToolTipText = "选择竖排识别-上下方向";
            this.tsmTopToDown.Visible = false;
            this.tsmTopToDown.DropDownItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.tsmDDL_DropDownItemClicked);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Font = new System.Drawing.Font("Microsoft YaHei UI", 10F);
            this.toolStripMenuItem2.Image = global::OCRTools.Properties.Resources.从上往下;
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(132, 22);
            this.toolStripMenuItem2.Tag = "True";
            this.toolStripMenuItem2.Text = "从上到下";
            // 
            // toolStripMenuItem16
            // 
            this.toolStripMenuItem16.Font = new System.Drawing.Font("Microsoft YaHei UI", 10F);
            this.toolStripMenuItem16.Image = global::OCRTools.Properties.Resources.从下往上;
            this.toolStripMenuItem16.Name = "toolStripMenuItem16";
            this.toolStripMenuItem16.Size = new System.Drawing.Size(132, 22);
            this.toolStripMenuItem16.Tag = "False";
            this.toolStripMenuItem16.Text = "从下到上";
            // 
            // tsmLeftToRight
            // 
            this.tsmLeftToRight.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsmLeftToRight.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.从右到左ToolStripMenuItem,
            this.从左到右ToolStripMenuItem});
            this.tsmLeftToRight.ForeColor = System.Drawing.Color.Black;
            this.tsmLeftToRight.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmLeftToRight.Name = "tsmLeftToRight";
            this.tsmLeftToRight.Size = new System.Drawing.Size(13, 38);
            this.tsmLeftToRight.Tag = "左右方向";
            this.tsmLeftToRight.Text = "从右到左";
            this.tsmLeftToRight.ToolTipText = "选择竖排识别-左右方向";
            this.tsmLeftToRight.Visible = false;
            this.tsmLeftToRight.DropDownItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.tsmDDL_DropDownItemClicked);
            // 
            // 从右到左ToolStripMenuItem
            // 
            this.从右到左ToolStripMenuItem.Font = new System.Drawing.Font("Microsoft YaHei UI", 10F);
            this.从右到左ToolStripMenuItem.Image = global::OCRTools.Properties.Resources.从右往左;
            this.从右到左ToolStripMenuItem.Name = "从右到左ToolStripMenuItem";
            this.从右到左ToolStripMenuItem.Size = new System.Drawing.Size(132, 22);
            this.从右到左ToolStripMenuItem.Tag = "False";
            this.从右到左ToolStripMenuItem.Text = "从右到左";
            // 
            // 从左到右ToolStripMenuItem
            // 
            this.从左到右ToolStripMenuItem.Font = new System.Drawing.Font("Microsoft YaHei UI", 10F);
            this.从左到右ToolStripMenuItem.Image = global::OCRTools.Properties.Resources.从左往右;
            this.从左到右ToolStripMenuItem.Name = "从左到右ToolStripMenuItem";
            this.从左到右ToolStripMenuItem.Size = new System.Drawing.Size(132, 22);
            this.从左到右ToolStripMenuItem.Tag = "True";
            this.从左到右ToolStripMenuItem.Text = "从左到右";
            // 
            // tmsSpiltMode
            // 
            this.tmsSpiltMode.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tmsSpiltMode.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F);
            this.tmsSpiltMode.ForeColor = System.Drawing.Color.Black;
            this.tmsSpiltMode.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.tmsSpiltMode.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tmsSpiltMode.Name = "tmsSpiltMode";
            this.tmsSpiltMode.Size = new System.Drawing.Size(13, 38);
            this.tmsSpiltMode.Tag = "分段模式";
            this.tmsSpiltMode.DropDownItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.tsmDDL_DropDownItemClicked);
            // 
            // btnVoice
            // 
            this.btnVoice.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnVoice.ForeColor = System.Drawing.Color.Black;
            this.btnVoice.Image = global::OCRTools.Properties.Resources.语音;
            this.btnVoice.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.btnVoice.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnVoice.Name = "btnVoice";
            this.btnVoice.Size = new System.Drawing.Size(42, 38);
            this.btnVoice.Tag = "发音人";
            this.btnVoice.ToolTipText = "切换语音发音人";
            this.btnVoice.ButtonClick += new System.EventHandler(this.btnVoice_ButtonClick);
            // 
            // tsmShowOldContent
            // 
            this.tsmShowOldContent.BackColor = System.Drawing.Color.Transparent;
            this.tsmShowOldContent.Checked = true;
            this.tsmShowOldContent.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F);
            this.tsmShowOldContent.ForeColor = System.Drawing.Color.Black;
            this.tsmShowOldContent.Name = "tsmShowOldContent";
            this.tsmShowOldContent.Size = new System.Drawing.Size(71, 38);
            this.tsmShowOldContent.Text = "显示原文";
            this.tsmShowOldContent.ToolTipText = "翻译模式是否显示原文";
            this.tsmShowOldContent.Visible = false;
            this.tsmShowOldContent.CheckedChanged += new System.EventHandler(this.tsmShowOldContent_CheckedChanged);
            // 
            // tsmCopyTrans
            // 
            this.tsmAutoTrans.BackColor = System.Drawing.Color.Transparent;
            this.tsmAutoTrans.Checked = false;
            this.tsmAutoTrans.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F);
            this.tsmAutoTrans.ForeColor = System.Drawing.Color.Black;
            this.tsmAutoTrans.Name = "tsmCopyTrans";
            this.tsmAutoTrans.Size = new System.Drawing.Size(83, 38);
            this.tsmAutoTrans.Text = "监听粘贴板";
            this.tsmAutoTrans.Visible = false;
            // 
            // lnkLogin
            // 
            this.lnkLogin.ActiveLinkColor = System.Drawing.Color.Black;
            this.lnkLogin.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lnkLogin.AutoSize = true;
            this.lnkLogin.Font = new System.Drawing.Font("微软雅黑", 14F);
            this.lnkLogin.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline;
            this.lnkLogin.LinkColor = System.Drawing.Color.DarkGray;
            this.lnkLogin.Location = new System.Drawing.Point(218, 7);
            this.lnkLogin.Name = "lnkLogin";
            this.lnkLogin.Size = new System.Drawing.Size(96, 25);
            this.lnkLogin.TabIndex = 7;
            this.lnkLogin.TabStop = true;
            this.lnkLogin.Text = "登录/注册";
            this.lnkLogin.TextAlign = System.Drawing.ContentAlignment.BottomRight;
            this.lnkLogin.UseMnemonic = false;
            this.lnkLogin.Click += new System.EventHandler(this.metroLink1_Click);
            // 
            // tipMain
            // 
            this.tipMain.Style = MetroFramework.MetroColorStyle.银色;
            this.tipMain.StyleManager = null;
            this.tipMain.Theme = MetroFramework.MetroThemeStyle.Light;
            // 
            // pnlSetting
            // 
            this.pnlSetting.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.pnlSetting.BackColor = System.Drawing.Color.Transparent;
            this.pnlSetting.BackgroundImage = global::OCRTools.Properties.Resources.menu;
            this.pnlSetting.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.pnlSetting.Location = new System.Drawing.Point(342, 5);
            this.pnlSetting.Name = "pnlSetting";
            this.pnlSetting.Size = new System.Drawing.Size(24, 23);
            this.pnlSetting.TabIndex = 10;
            this.tipMain.SetToolTip(this.pnlSetting, "菜单");
            this.pnlSetting.Click += new System.EventHandler(this.pnlSetting_Click);
            // 
            // pnlTop
            // 
            this.pnlTop.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.pnlTop.BackColor = System.Drawing.Color.Transparent;
            this.pnlTop.BackgroundImage = global::OCRTools.Properties.Resources.top;
            this.pnlTop.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.pnlTop.Location = new System.Drawing.Point(317, 5);
            this.pnlTop.Name = "pnlTop";
            this.pnlTop.Size = new System.Drawing.Size(28, 23);
            this.pnlTop.TabIndex = 9;
            this.tipMain.SetToolTip(this.pnlTop, "置顶/取消");
            this.pnlTop.Click += new System.EventHandler(this.pnlTop_Click);
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            this.toolStripSeparator3.Size = new System.Drawing.Size(101, 6);
            // 
            // toolStripSeparator4
            // 
            this.toolStripSeparator4.Name = "toolStripSeparator4";
            this.toolStripSeparator4.Size = new System.Drawing.Size(101, 6);
            // 
            // wbVoice
            // 
            this.wbVoice.Location = new System.Drawing.Point(1, 1);
            this.wbVoice.Name = "wbVoice";
            this.wbVoice.ScriptErrorsSuppressed = true;
            //this.wbVoice.ScrollBarsEnabled = false;
            this.wbVoice.Size = new System.Drawing.Size(0, 0);
            this.wbVoice.TabIndex = 11;
            this.wbVoice.TabStop = false;
            //this.wbVoice.WebBrowserShortcutsEnabled = false;
            //this.wbVoice.AllowWebBrowserDrop = false;
            this.wbVoice.IsWebBrowserContextMenuEnabled = false;
            // 
            // picUser
            // 
            this.picUser.Location = new System.Drawing.Point(199, 13);
            this.picUser.Name = "picUser";
            this.picUser.Size = new System.Drawing.Size(16, 16);
            this.picUser.TabIndex = 12;
            this.picUser.TabStop = false;
            // 
            // ucLoading1
            // 
            this.ucLoading1.Location = new System.Drawing.Point(7, 7);
            this.ucLoading1.Name = "ucLoading1";
            this.ucLoading1.Size = new System.Drawing.Size(27, 30);
            this.ucLoading1.TabIndex = 2;
            // 
            // 联系我们ToolStripMenuItem
            // 
            this.关于我们ToolStripMenuItem.Image = global::OCRTools.Properties.Resources.窗口;
            this.关于我们ToolStripMenuItem.Name = "联系我们ToolStripMenuItem";
            this.关于我们ToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.关于我们ToolStripMenuItem.Text = "关于我们";
            this.关于我们ToolStripMenuItem.Click += new System.EventHandler(this.系统设置SToolStripMenuItem_Click);
            // 
            // FrmMain
            // 
            this.ClientSize = new System.Drawing.Size(454, 398);
            this.Controls.Add(this.picUser);
            this.Controls.Add(this.pnlSetting);
            this.Controls.Add(this.pnlTop);
            this.Controls.Add(this.wbVoice);
            this.Controls.Add(this.lnkLogin);
            this.Controls.Add(this.stateStip);
            this.Controls.Add(this.tbMain);
            this.Controls.Add(this.ucLoading1);
            this.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.ForeColor = System.Drawing.Color.DarkGray;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "FrmMain";
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Show;
            this.StyleManager = this.msManager;
            this.Load += new System.EventHandler(this.FrmMain_Load);
            this.Shown += new System.EventHandler(this.FrmMain_Shown);
            this.tbMain.ResumeLayout(false);
            this.tbContentText.ResumeLayout(false);
            this.tbImageBox.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.msManager)).EndInit();
            //this.cmsNotify.ResumeLayout(false);
            this.stateStip.ResumeLayout(false);
            this.stateStip.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.picUser)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        private ToolStripDropDownButton tsmTransFrom;
        private ToolStripDropDownButton tsmTransTo;
        private ToolStripCheckBoxControl tsmShowOldContent;
        private ToolStripSeparator toolStripSeparator5;
        private ToolStripSeparator toolStripSeparator6;
        private ToolStripCheckBoxControl tsmAutoTrans;
        private ToolStripSplitButton btnVoice;
        private WebBrowser wbVoice;
        private ToolStripMenuItem tsmTrans;
        private ToolStripDropDownButton tsmBiaoDian;
        private ToolStripMenuItem tsmSearch;
        private ToolStripButton tsmPicViewModel;
        private UcPicView imageBox;
        private ToolStripMenuItem 网络检测ToolStripMenuItem;
        private PictureBox picUser;
        private ToolStripMenuItem 关于我们ToolStripMenuItem;
    }
}
