﻿using System.ComponentModel;
using System.Drawing;

namespace OCRTools
{
    internal enum LoadingType
    {
        蓝色箭头 = 0,
        蓝色圆圈 = 1,
        红色圆圈 = 2,
        旋风 = 4,
        花瓣 = 5,
        Win11 = 6,
        风车 = 7,
        三环 = 8,
        齿轮 = 9
    }

    internal class LoadingTypeConfig
    {
        public int Interval { get; set; }

        public int ImgCount { get; set; }

        public string ImgName { get; set; }

        public bool IsRound { get; set; }
    }

    internal class LoadingTypeHelper
    {
        public static Image GetImageByConfig(LoadingType type, int index = 0)
        {
            var config = GetTypeConfig(type);
            return GetImageByConfig(config, index);
        }

        public static Image GetImageByConfig(LoadingTypeConfig config, int index)
        {
            return (Image)new ComponentResourceManager(typeof(UcLoading)).GetObject(index + config.ImgName);
        }

        public static LoadingTypeConfig GetTypeConfig(LoadingType type)
        {
            var config = new LoadingTypeConfig() { IsRound = true, Interval = 60, ImgCount = 12 };
            switch (type)
            {
                default:
                case LoadingType.蓝色箭头:
                case LoadingType.蓝色圆圈:
                case LoadingType.红色圆圈:
                case LoadingType.Win11:
                case LoadingType.花瓣:
                case LoadingType.风车:
                case LoadingType.三环:
                case LoadingType.齿轮:
                case LoadingType.旋风:
                    config.ImgName = "_" + type.ToString();
                    break;
            }

            return config;
        }
    }

    internal enum ToolDoubleClickEnum
    {
        显示主窗体 = 1,
        不做任何操作 = 2,
        截图识别 = 0,
        快速截图 = 3,
        截图编辑 = 4,
        快速贴图 = 5,
        截图贴图 = 6,
        粘贴贴图 = 7,
        显隐贴图 = 8,
    }
}