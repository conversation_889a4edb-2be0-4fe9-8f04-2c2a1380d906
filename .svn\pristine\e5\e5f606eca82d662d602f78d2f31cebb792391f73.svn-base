﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace OCRTools
{
    public class DnsHelper
    {
        private static readonly Dictionary<string, string> DicServers = new Dictionary<string, string>();

        public static string GetDnsIp(string host)
        {
            var result = "";
            try
            {
                if (!string.IsNullOrEmpty(host) && !IsIPv4(host))
                {
                    result = GetDnsCache(host);

                    if (string.IsNullOrEmpty(result))
                    {
                        result = ToIpAddress(host);

                        if (!IsIPv4(result))
                            result = string.Empty;
                    }
                }
            }
            catch
            {
            }

            return result;
        }

        public static string InitByHost(string host)
        {
            var result = string.Empty;
            var lstDicCount = new List<DnsItem>();
            var str114 = GetDnsFromNsLookUp(host, "***************");

            if (IsIPv4(str114))
            {
                var item = lstDicCount.FirstOrDefault(p => Equals(p.ip, str114)) ?? new DnsItem() { ip = str114 };
                lstDicCount.Remove(item);
                item.count++;
                lstDicCount.Add(item);
            }

            var str119 = GetDnsFromNsLookUp(host, "************");

            if (IsIPv4(str119))
            {
                var item = lstDicCount.FirstOrDefault(p => Equals(p.ip, str119)) ?? new DnsItem() { ip = str119 };
                lstDicCount.Remove(item);
                item.count++;
                lstDicCount.Add(item);
            }

            var str223 = GetDnsFromNsLookUp(host, "*********");

            if (IsIPv4(str223))
            {
                var item = lstDicCount.FirstOrDefault(p => Equals(p.ip, str223)) ?? new DnsItem() { ip = str223 };
                lstDicCount.Remove(item);
                item.count++;
                lstDicCount.Add(item);
            }

            var strLocal = ToIpAddress(host);

            if (IsIPv4(strLocal))
            {
                var item = lstDicCount.FirstOrDefault(p => Equals(p.ip, strLocal)) ?? new DnsItem() { ip = strLocal };
                lstDicCount.Remove(item);
                item.count++;
                lstDicCount.Add(item);
            }

            var maxItem = lstDicCount.OrderByDescending(p => p.count).FirstOrDefault();

            if (maxItem != null)
            {
                if (DicServers.ContainsKey(host))
                    DicServers.Remove(host);
                DicServers.Add(host, maxItem.ip);
                result = maxItem.ip;
            }

            return result;
        }

        private class DnsItem
        {
            public string ip { get; set; }

            public int count { get; set; }
        }

        public static string ToIpAddress(string hostNameOrAddress, bool favorIpV6 = false)
        {
            var favoredFamily = favorIpV6 ? AddressFamily.InterNetworkV6 : AddressFamily.InterNetwork;
            var addrs = Dns.GetHostAddresses(hostNameOrAddress);
            return addrs.FirstOrDefault(addr => addr.AddressFamily == favoredFamily)?.ToString();
        }

        public static string GetDnsFromNsLookUp(string strHost, string strNsServer = "")
        {
            var result = "";
            var strTmp = ExecCmd(string.Format("nslookup {0} {1}", strHost, strNsServer));
            if (strTmp.Contains(strHost))
                if (strTmp.IndexOf(strHost) != strTmp.LastIndexOf(strHost))
                {
                    strTmp = strTmp.Substring(strTmp.LastIndexOf(strHost) + strHost.Length);
                    result = CommonMethod.SubString(strTmp, ":", "\n").Trim();
                }
            //ConfigHelper._Log.Info(strHost + "[" + result + "]");

            return result;
        }

        private static string GetDnsCache(string strHost)
        {
            var cache = "";
            if (DicServers.ContainsKey(strHost)) cache = DicServers[strHost];
            return cache;
        }

        public static string ExecCmd(string str)
        {
            //process用于调用外部程序
            using (var p = new Process())
            {
                //调用cmd.exe
                p.StartInfo.FileName = "cmd.exe";
                //不显示程序窗口
                p.StartInfo.CreateNoWindow = true;
                //是否指定操作系统外壳进程启动程序
                p.StartInfo.UseShellExecute = false;
                //可能接受来自调用程序的输入信息
                //重定向标准输入
                p.StartInfo.RedirectStandardInput = true;
                //重定向标准输出
                p.StartInfo.RedirectStandardOutput = true;
                //重定向错误输出
                p.StartInfo.RedirectStandardError = true;
                //启动程序
                p.Start();
                //输入命令
                p.StandardInput.WriteLine(str);
                //一定要关闭。
                p.StandardInput.WriteLine("exit");
                return p.StandardOutput.ReadToEnd();
            }
        }

        public static bool IsIPv4(string input)
        {
            var array = input.Split('.');
            if (array.Length != 4) return false;
            foreach (var t in array)
            {
                if (!IsMatch("^\\d+$", t)) return false;
                if (Convert.ToUInt16(t) > 255) return false;
            }

            return true;
        }

        public static bool IsMatch(string pattern, string input)
        {
            if (string.IsNullOrEmpty(input)) return false;
            var regex = new Regex(pattern);
            return regex.IsMatch(input);
        }
    }
}