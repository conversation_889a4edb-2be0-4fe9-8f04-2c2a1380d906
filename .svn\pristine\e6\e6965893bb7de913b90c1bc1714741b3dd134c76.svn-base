﻿using System.Collections.Generic;
using System.Drawing;
using System.Reflection;

namespace OCRTools.Common
{
    internal class CommonPlug
    {

        internal static List<PlugEntity> GetAllPlug()
        {
            List<PlugEntity> lstAds = new List<PlugEntity>();
            try
            {
                //var plug = new PlugEntity()
                //{
                //    Name = "限时促销进行中",
                //    FontSize = 15F,
                //    Desc = "解锁更多生产力，限时促销进行中！",
                //    Url = "https://ocr.oldfish.cn/UserUpgrade.aspx",
                //    Type = PlugType.Url,
                //    ButtonType = ButtonType.Image,
                //    Image = ImageProcessHelper.ImageToBase64(Properties.Resources.vip_4),
                //    Top = 3,
                //    Width = 1050,
                //    Height = 680
                //};
                //lstAds.Add(plug);
                //System.Console.WriteLine(CommonString.JavaScriptSerializer.Serialize(lstAds));
                //return lstAds;

                var result =
                    WebClientExt.GetHtml(CommonString.HostUpdate?.FullUrl + "update/uPlug.txt?t=" + ServerTime.DateTime.Ticks, 5);
                if (!string.IsNullOrEmpty(result) && result.Length > 2)
                    lstAds = CommonString.JavaScriptSerializer.Deserialize<List<PlugEntity>>(result);
            }
            catch { }
            return lstAds;
        }
    }

    [Obfuscation]
    public class BaseMsgEntity
    {
        [Obfuscation] public string Id { get; set; }

        [Obfuscation] public string Name { get; set; }

        [Obfuscation] public string Title { get; set; }

        [Obfuscation] public string Desc { get; set; }

        [Obfuscation] public string ForeColor { get; set; }

        [Obfuscation] public string BackColor { get; set; }

        [Obfuscation] public float FontSize { get; set; } = 15F;

        [Obfuscation] public string Font { get; set; } = CommonString.StrDefaultFontName;

        /// <summary>
        /// 展示会员类型
        /// </summary>
        [Obfuscation] public string ShowLevel { get; set; }
    }

    [Obfuscation]
    internal class PlugEntity : BaseMsgEntity
    {
        [Obfuscation] public ButtonType ButtonType { get; set; }

        [Obfuscation] public PlugType Type { get; set; }

        [Obfuscation] public string Image { get; set; }

        [Obfuscation] public string Url { get; set; }

        [Obfuscation] public int Top { get; set; }

        [Obfuscation] public int Width { get; set; }

        [Obfuscation] public int Height { get; set; }

        /// <summary>
        /// 是否需要登录
        /// </summary>
        [Obfuscation] public bool IsNeedLogin { get; set; }

        /// <summary>
        /// 弹窗数据
        /// </summary>
        [Obfuscation] public string Data { get; set; }

        public Image GetImage()
        {
            var image = ImageProcessHelper.Base64StringToImage(Image);
            if (CommonSetting.夜间模式)
                image = ImageProcessHelper.InverseImage(new Bitmap(image));
            return image;
        }

        public void Click(object sender, System.EventArgs e)
        {
            try
            {
                if (IsNeedLogin && !Program.IsLogined())
                {
                    CommonMethod.ShowHelpMsg("插件[" + Name + "]需要登录后才能使用！");
                    FrmMain.LoginDelegate.Invoke();
                    return;
                }
                switch (Type)
                {
                    case PlugType.Url:
                        CommonMethod.DetermineCall(FrmMain.FrmTool, () =>
                        {

                            var view = new FrmViewUrl
                            {
                                Url = GetResultUrl(Url),
                                WindowState = System.Windows.Forms.FormWindowState.Maximized,
                                StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen,
                                Icon = FrmMain.FrmTool.Icon,
                                Text = Desc ?? Name
                            };
                            if (Width > 0 && Height > 0)
                            {
                                view.WindowState = System.Windows.Forms.FormWindowState.Normal;
                                view.Size = new Size(Width, Height);
                            }
                            view.Show();
                        });
                        break;
                    case PlugType.Text:
                        CommonMethod.ShowNotificationTip(Desc, null, 10 * 1000, null, FontSize, ForeColor, null, Url, Data);
                        break;
                }
            }
            catch { }
        }

        private string GetResultUrl(string url)
        {
            return !Program.IsLogined() ? url :
                url + (url.Contains("?") ? "&" : "?")
                + "account=" + System.Web.HttpUtility.UrlEncode(Program.NowUser.Account ?? "");
        }
    }

    [Obfuscation]
    internal enum ButtonType
    {
        Image = 0,
        ImageAndText = 1,
    }

    [Obfuscation]
    internal enum PlugType
    {
        Url = 0,
        Text = 1
    }
}
