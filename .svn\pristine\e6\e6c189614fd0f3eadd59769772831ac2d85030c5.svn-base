using OCRTools.Common.Entity;
using OCRTools.Properties;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using System.Windows.Forms;
using OCRTools.Common;

// ReSharper disable All

namespace OCRTools
{
    internal static class Program
    {
        //private enum ProcessDPIAwareness
        //{
        //    ProcessDPIUnaware,
        //    ProcessSystemDPIAware,
        //    ProcessPerMonitorDPIAware
        //}

        private static readonly Mutex Mutex = new Mutex(true, Application.ProductName);

        public static List<HistoryTask> RecentTasks;

        public static UserEntity NowUser { get; internal set; }

        //[DllImport("shcore.dll")]
        //private static extern int SetProcessDpiAwareness(ProcessDPIAwareness value);

        [STAThread]
        private static void Main()
        {
            //SetDpiAwareness();
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            //ClipboardService.GetRtfHtml();

            //Application.Run(new RulerForm());

            //Application.Run(new ScrollingCaptureForm(new ScrollingCaptureOptions(), new RegionCaptureOptions(), false));

            //SelectRectangleList selectRectangleList = new SelectRectangleList
            //{
            //    IncludeChildWindows = true
            //};
            //var Windows = selectRectangleList.GetWindowInfoList();

            //处理未捕获的异常
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);

            //处理UI线程异常
            Application.ThreadException += Application_ThreadException;

            //处理非UI线程异常
            AppDomain.CurrentDomain.UnhandledException += Backend_ThreadException;

            var uue = new UheHandler();
            uue.InstallUheHandler();
            uue.SendingErrorReport += uue_SendingErrorReport;
            //MessageBox.Show(CommonString.CommonShadowType.ToString());

            if (Mutex.WaitOne(TimeSpan.Zero, true))
            {
                //CommonString.StrServerIp = "127.0.0.1";
                CommonString.AccountUrlPort = 2020;
                CommonString.UpdateUrlPort = 6060;
                CommonString.CodeUrlPort = 8080;

                CommonString.InitString();

                CommonSetting.InitSetting();
                //new FormSetting().SaveConfig();

                //#if !DEBUG
                //                if (CommonSetting.以管理员身份运行 && !CommonString.IsAdministrator)
                //                {
                //                    CommonString.RunAsAdmin();
                //                    Mutex.ReleaseMutex();
                //                    return;
                //                }
                //#endif

                //try
                //{
                //    ShortcutHelper.CheckAndCreatShortcur(Application.ProductName, Application.ExecutablePath);
                //}
                //catch
                //{
                //}
                try
                {
                    InternetExplorerFeatureControl.Instance.BrowserEmulation = DocumentMode.DefaultRespectDocType;
                }
                catch { }

                //CommonString.AutoCodeURL = "http://localhost:23205/";

#if !DEBUG
                ImgLogHelper.Run();
#endif

                //RefDll.AssemblyLoader.Attach();
                try
                {
                    var uiAutomation = Assembly.Load(Resources.Interop_UIAutomationClient);
                    //var pdfRender4NET = Assembly.Load(Resources.O2S_Components_PDFRender4NET);
                    AppDomain.CurrentDomain.AssemblyResolve += (sender, args) =>
                    {
                        if (args.Name.Contains("UIAutomationClient"))
                        {
                            return uiAutomation;
                        }
                        //else if (args.Name.Contains("PDFRender4NET"))
                        //{
                        //    return pdfRender4NET;
                        //}

                        return CommonString.LoadDllByName(args.Name);
                    };
                }
                catch
                {
                }

                //SetDpiAwareness();
                Application.Run(new FrmMain());
                Mutex.ReleaseMutex();
            }
            else
            {
                CommonMethod.ShowHelpMsg("软件已经在运行中！");
                Application.ExitThread();
            }

            uue.UninstallUheHandler();
        }

        private static void uue_SendingErrorReport(object sender, SendErrorReportArgs e)
        {
            if (!string.IsNullOrEmpty(e.Body)) Log.WriteLog(e.Body);
        }

        private static void Application_ThreadException(object sender, ThreadExceptionEventArgs e)
        {
            HandlerError(e.Exception);
        }

        private static void Backend_ThreadException(object sender, UnhandledExceptionEventArgs e)
        {
            HandlerError(e.ExceptionObject as Exception);
        }

        private static void HandlerError(Exception error)
        {
            Log.WriteError("运行时异常", error);
        }

        //private static void SetDpiAwareness()
        //{
        //    try
        //    {
        //        if (NativeMethods.OsVersion.Major >= 6)
        //        {
        //            //NativeMethods.SetProcessDPIAware();
        //NativeMethods.SetProcessDpiAwareness(NativeMethods.ProcessDPIAwareness.ProcessDPIUnaware);
        //        }
        //    }
        //    catch
        //    {
        //    }
        //}
    }
}