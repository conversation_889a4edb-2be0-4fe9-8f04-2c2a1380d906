using System.Drawing;

namespace OCRTools
{
    public class Spline
    {
        private static readonly int _samplePointCount = 20;

        private static readonly float _tension = 0f;

        public Spline()
        {
            StartControlPoint1 = default(PointF);
            StartPoint = default(PointF);
            EndPoint = default(PointF);
            EndControlPoint = default(PointF);
            CtrlPoints = new PointF[_samplePointCount + 1];
            for (var i = 0; i < CtrlPoints.Length; i++) CtrlPoints[i] = default(PointF);
        }

        public PointF StartControlPoint
        {
            get => StartControlPoint1;
            set => StartControlPoint1 = value;
        }

        public PointF StartPoint { get; set; }

        public PointF EndPoint { get; set; }

        public PointF EndControlPoint { get; set; }

        public PointF[] CtrlPoints { get; }

        public bool IsFirst { get; set; }
        public PointF StartControlPoint1 { get; set; }

        public void GenerateSamplePoint()
        {
            var startControlPoint = StartControlPoint;
            var startPoint = StartPoint;
            var endPoint = EndPoint;
            var endControlPoint = EndControlPoint;
            var num = 1f / _samplePointCount;
            var num2 = 0f;
            for (var i = 0; i < _samplePointCount; i++)
            {
                var pointF = GenerateSimulatePoint(num2, startControlPoint, startPoint, endPoint, endControlPoint);
                CtrlPoints[i] = pointF;
                num2 += num;
            }

            CtrlPoints[CtrlPoints.Length - 1] = endPoint;
        }

        public void Draw(Graphics g, Pen pen)
        {
            for (var i = 0; i < CtrlPoints.Length - 1; i++)
            {
                var pt = CtrlPoints[i];
                var pt2 = CtrlPoints[i + 1];
                g.DrawLine(pen, pt, pt2);
            }
        }

        private PointF GenerateSimulatePoint(float u, PointF startControlPoint, PointF startPoint, PointF endPoint,
            PointF endControlPoint)
        {
            var s = (1f - _tension) / 2f;
            var result = default(PointF);
            result.X = CalculateAxisCoordinate(startControlPoint.X, startPoint.X, endPoint.X, endControlPoint.X, s, u);
            result.Y = CalculateAxisCoordinate(startControlPoint.Y, startPoint.Y, endPoint.Y, endControlPoint.Y, s, u);
            return result;
        }

        private float CalculateAxisCoordinate(float a, float b, float c, float d, float s, float u)
        {
            return a * (2f * s * u * u - s * u * u * u - s * u) + b * ((2f - s) * u * u * u + (s - 3f) * u * u + 1f) +
                   c * ((s - 2f) * u * u * u + (3f - 2f * s) * u * u + s * u) + d * (s * u * u * u - s * u * u);
        }
    }
}