using UtfUnknown.Core.Models;

namespace UtfUnknown.Core.Probers
{
    public class CodingStateMachine
    {
        private readonly StateMachineModel _model;
        private int _currentState;

        public CodingStateMachine(StateMachineModel model)
        {
            _currentState = 0;
            _model = model;
        }

        public int CurrentCharLen { get; private set; }

        public string ModelName => _model.Name;

        public int NextState(byte b)
        {
            var @class = _model.GetClass(b);
            if (_currentState == 0) CurrentCharLen = _model.charLenTable[@class];
            _currentState = _model.stateTable.Unpack(_currentState * _model.ClassFactor + @class);
            return _currentState;
        }

        public void Reset()
        {
            _currentState = 0;
        }
    }
}