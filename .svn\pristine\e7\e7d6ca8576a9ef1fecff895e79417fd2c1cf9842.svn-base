using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;

namespace OCRTools
{
    internal class DrawText : DrawRectangle
    {
        public DrawText(int x, int y, int width, int height)
        {
            Rectangle = new Rectangle(x, y, width, height);
            Initialize();
            Text = "双击进行编辑";
            Font = new Font("微软雅黑", 16f, FontStyle.Regular);
        }

        public override DrawToolType NoteType => DrawToolType.Text;

        public override string Text { get; set; }

        public override Font Font { get; set; }

        public override Rectangle GetBoundingBox()
        {
            var normalizedRectangle = Rectangle.GetNormalizedRectangle();
            if (IsSelected)
            {
                var num = 30.DpiValue();
                normalizedRectangle.Inflate(num, num);
            }

            return normalizedRectangle;
        }

        public override DrawObject Clone()
        {
            var drawText = new DrawText(0, 0, 1, 1)
            {
                Rectangle = Rectangle,
                Text = Text,
                Font = Font
            };
            FillDrawObjectFields(drawText);
            return drawText;
        }

        public override bool PointInObject(Point point)
        {
            if (Rectangle.GetNormalizedRectangle().Contains(point)) return true;
            return false;
        }

        public void DrawIsWindows(Graphics g)
        {
            var normalizedRectangle = GetNormalizedRectangle(Rectangle);
            if (!normalizedRectangle.IsLimt()) return;
            new Font(Fontstyle, FontSize, FontStyle.Regular);
            var font2 = Font = !IsOutline
                ? new Font(Fontstyle, FontSize, FontStyle.Regular)
                : new Font(Fontstyle, FontSize, FontStyle.Bold);
            if (LastRect != normalizedRectangle || LastText != Text || IsChange)
            {
                if (IsCache)
                {
                    LastRect = normalizedRectangle;
                    IsChange = false;
                    LastText = Text;
                    var rect = new Rectangle(0, 0, normalizedRectangle.Width, normalizedRectangle.Height);
                    var bitmap = new Bitmap(rect.Width, rect.Height);
                    var graphics = Graphics.FromImage(bitmap);
                    if (IsOutline)
                        DrawTextWithOutline(graphics, Text, font2, Color.White, Color, 3.DpiValue(), rect);
                    else
                        Drawtext(graphics, Text, font2, Color, rect);
                    CurrentImage = bitmap;
                    g.DrawImage(bitmap, normalizedRectangle);
                    graphics.Dispose();
                }
                else
                {
                    if (IsOutline)
                        DrawTextWithOutline(g, Text, font2, Color.White, Color, 3.DpiValue(), normalizedRectangle);
                    else
                        Drawtext(g, Text, font2, Color, normalizedRectangle);
                    using (var pen = new Pen(Color.Gray, 1f))
                    {
                        pen.DashStyle = DashStyle.Custom;
                        pen.DashPattern = new[]
                        {
                            5f,
                            5f
                        };
                        g.DrawRectangle(pen, normalizedRectangle);
                    }
                }
            }
            else
            {
                g.DrawImage(CurrentImage, normalizedRectangle);
            }
        }

        public override void Draw(Graphics g)
        {
            DrawIsWindows(g);
        }

        protected void Drawtext(Graphics g, string text, Font font, Color textColor, Rectangle rect)
        {
            g.SmoothingMode = SmoothingMode.HighQuality;
            g.TextRenderingHint = TextRenderingHint.AntiAlias;
            using (var format = new StringFormat
            {
                Alignment = StringAlignment.Near,
                LineAlignment = StringAlignment.Near
            })
            {
                using (Brush brush = new SolidBrush(textColor))
                {
                    g.DrawString(text, font, brush, rect, format);
                }
            }
        }

        protected void DrawTextWithOutline(Graphics g, string text, Font afont, Color textColor, Color borderColor,
            int borderSize, Rectangle rect)
        {
            if (Color == CustomColor.gray)
            {
                textColor = CustomColor.black;
                borderColor = CustomColor.gray;
            }

            g.SmoothingMode = SmoothingMode.HighQuality;
            if (!string.IsNullOrEmpty(text))
                using (var graphicsPath = new GraphicsPath())
                {
                    using (var font = new Font(afont.FontFamily, afont.Size, afont.Style))
                    {
                        using (var format = new StringFormat
                        {
                            Alignment = StringAlignment.Near,
                            LineAlignment = StringAlignment.Near
                        })
                        {
                            var emSize = g.DpiY * font.SizeInPoints / 72f;
                            graphicsPath.AddString(text, font.FontFamily, (int)font.Style, emSize, rect, format);
                        }
                    }

                    using (var pen = new Pen(borderColor, borderSize)
                    {
                        LineJoin = LineJoin.Round
                    })
                    {
                        g.DrawPath(pen, graphicsPath);
                    }

                    using (Brush brush = new SolidBrush(textColor))
                    {
                        g.FillPath(brush, graphicsPath);
                    }
                }
        }
    }
}