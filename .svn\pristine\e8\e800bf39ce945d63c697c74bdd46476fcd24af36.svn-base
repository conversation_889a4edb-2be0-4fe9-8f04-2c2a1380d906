using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class BOF : Record
	{
		public ushort BIFFversion;

		public ushort StreamType;

		public ushort BuildID;

		public ushort BuildYear;

		public uint FileHistoryFlags;

		public uint RequiredExcelVersion;

		public BOF(Record record)
			: base(record)
		{
		}

		public BOF()
		{
			Type = 2057;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			BIFFversion = binaryReader.ReadUInt16();
			StreamType = binaryReader.ReadUInt16();
			if (Data.Length >= 6)
			{
				BuildID = binaryReader.ReadUInt16();
			}
			if (Data.Length >= 8)
			{
				BuildYear = binaryReader.ReadUInt16();
			}
			if (Data.Length >= 12)
			{
				FileHistoryFlags = binaryReader.ReadUInt32();
			}
			if (Data.Length >= 16)
			{
				RequiredExcelVersion = binaryReader.ReadUInt32();
			}
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(BIFFversion);
			binaryWriter.Write(StreamType);
			binaryWriter.Write(BuildID);
			binaryWriter.Write(BuildYear);
			binaryWriter.Write(FileHistoryFlags);
			binaryWriter.Write(RequiredExcelVersion);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
