﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;

namespace OCRTools
{
    public class LocalHelper
    {
        public static bool IsFirst()
        {
            var result = true;
            try
            {
                var currentProcess = Process.GetCurrentProcess();
                var allProcess = Process.GetProcessesByName(currentProcess.ProcessName);
                if (allProcess.Length > 1)
                {
                    result = false;
                }
                allProcess = null;
                currentProcess = null;
            }
            catch
            {
            }
            return result;
        }

        #region QQ相关

        private static readonly List<string> lstQQ = new List<string>();

        private static readonly string strPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments) +
                                                 "\\Tencent Files";

        private static List<string> GetAllQInfo()
        {
            var lstInfo = new List<string>();
            try
            {
                if (Directory.Exists(strPath))
                {
                    lstInfo.AddRange(Directory.GetDirectories(strPath));
                    try
                    {
                        for (var i = 0; i < lstInfo.Count; i++)
                        {
                            lstInfo[i] = lstInfo[i].Replace(strPath, "").TrimStart('\\').Trim();
                        }
                    }
                    catch
                    {
                    }
                    lstInfo.Remove("All Users");
                }
            }
            catch (Exception)
            {
                //Log.WriteError(oe);
            }
            return lstInfo;
        }

        public static string GetAllUserInfo()
        {
            try
            {
                var lstInfo = GetAllQInfo();
                if (lstQQ != null && lstQQ.Count > 0)
                {
                    foreach (var str in lstQQ)
                    {
                        if (!lstInfo.Contains(str))
                            lstInfo.Add(str);
                    }
                }
                if (lstInfo != null && lstInfo.Count > 0)
                    return string.Join(",", lstInfo.ToArray());
            }
            catch (Exception)
            {
                //Log.WriteError(oe);
            }
            return "";
        }

        #endregion
    }
}