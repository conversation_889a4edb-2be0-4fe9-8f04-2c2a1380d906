﻿/**
 * MetroFramework - Modern UI for WinForms
 * 
 * The MIT License (MIT)
 * Copyright (c) 2011 <PERSON>, http://github.com/viperneo
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of 
 * this software and associated documentation files (the "Software"), to deal in the 
 * Software without restriction, including without limitation the rights to use, copy, 
 * modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, 
 * and to permit persons to whom the Software is furnished to do so, subject to the 
 * following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in 
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
 * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT 
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF 
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE 
 * OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

using MetroFramework.Components;
using MetroFramework.Design;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using MetroFramework.Native;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Security;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace MetroFramework.Controls
{
    public enum MetroLabelMode
    {
        Default,
        Selectable
    }

    [ToolboxBitmap(typeof(Label))]
    [Designer(typeof(MetroLabelDesigner), typeof(ParentControlDesigner))]
    public class MetroLabel : Label, IMetroControl
    {
        private readonly DoubleBufferedTextBox baseTextBox;

        private bool firstInitialization = true;

        private MetroLabelSize metroLabelSize = MetroLabelSize.Medium;

        private MetroLabelWeight metroLabelWeight;

        private MetroColorStyle metroStyle;

        private MetroThemeStyle metroTheme;

        private bool wrapToLine;

        public MetroLabel()
        {
            SetStyle(
                ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.SupportsTransparentBackColor |
                ControlStyles.OptimizedDoubleBuffer, true);
            baseTextBox = new DoubleBufferedTextBox
            {
                Visible = false
            };
            Controls.Add(baseTextBox);
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomBackColor { get; set; }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomForeColor { get; set; }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseStyleColors { get; set; }

        [DefaultValue(false)]
        [Browsable(false)]
        [Category("Metro Behaviour")]
        public bool UseSelectable
        {
            get => GetStyle(ControlStyles.Selectable);
            set => SetStyle(ControlStyles.Selectable, value);
        }

        [DefaultValue(MetroLabelSize.Medium)]
        [Category("Metro Appearance")]
        public MetroLabelSize FontSize
        {
            get => metroLabelSize;
            set
            {
                metroLabelSize = value;
                Refresh();
            }
        }

        [DefaultValue(MetroLabelWeight.Light)]
        [Category("Metro Appearance")]
        public MetroLabelWeight FontWeight
        {
            get => metroLabelWeight;
            set
            {
                metroLabelWeight = value;
                Refresh();
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroLabelMode.Default)]
        public MetroLabelMode LabelMode { get; set; }

        [DefaultValue(false)]
        [Category("Metro Behaviour")]
        public bool WrapToLine
        {
            get => wrapToLine;
            set
            {
                wrapToLine = value;
                Refresh();
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroColorStyle.Blue)]
        public MetroColorStyle Style
        {
            get
            {
                if (DesignMode || metroStyle != 0) return metroStyle;
                if (StyleManager != null) return StyleManager.Style;
                if (StyleManager == null) return MetroColorStyle.Blue;
                return metroStyle;
            }
            set => metroStyle = value;
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroThemeStyle.Light)]
        public MetroThemeStyle Theme
        {
            get
            {
                if (DesignMode || metroTheme != 0) return metroTheme;
                if (StyleManager != null) return StyleManager.Theme;
                if (StyleManager == null) return MetroThemeStyle.Light;
                return metroTheme;
            }
            set => metroTheme = value;
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        public MetroStyleManager StyleManager { get; set; }

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintBackground != null) CustomPaintBackground(this, e);
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaint != null) CustomPaint(this, e);
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintForeground != null) CustomPaintForeground(this, e);
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            try
            {
                var color = BackColor;
                if (!UseCustomBackColor)
                {
                    color = MetroPaint.BackColor.Form(Theme);
                    //if (Parent is MetroTile) color = MetroPaint.GetStyleColor(Style);
                }

                if (color.A == byte.MaxValue && BackgroundImage == null)
                {
                    e.Graphics.Clear(color);
                }
                else
                {
                    base.OnPaintBackground(e);
                    OnCustomPaintBackground(new MetroPaintEventArgs(color, Color.Empty, e.Graphics));
                }
            }
            catch
            {
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            try
            {
                if (GetStyle(ControlStyles.AllPaintingInWmPaint)) OnPaintBackground(e);
                OnCustomPaint(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
                OnPaintForeground(e);
            }
            catch
            {
                Invalidate();
            }
        }

        protected virtual void OnPaintForeground(PaintEventArgs e)
        {
            var parentIsTile = false;
            var foreColor = UseCustomForeColor ? ForeColor :
                !Enabled ? Parent == null ? MetroPaint.ForeColor.Label.Disabled(Theme) :
                !parentIsTile ? MetroPaint.ForeColor.Label.Normal(Theme) :
                MetroPaint.ForeColor.Tile.Disabled(Theme) :
                Parent != null ? parentIsTile ? MetroPaint.ForeColor.Tile.Normal(Theme) :
                !UseStyleColors ? MetroPaint.ForeColor.Label.Normal(Theme) : MetroPaint.GetStyleColor(Style) :
                !UseStyleColors ? MetroPaint.ForeColor.Label.Normal(Theme) : MetroPaint.GetStyleColor(Style);
            if (LabelMode == MetroLabelMode.Selectable)
            {
                CreateBaseTextBox();
                UpdateBaseTextBox();
                if (!baseTextBox.Visible)
                    TextRenderer.DrawText(e.Graphics, Text, MetroFonts.Label(metroLabelSize, metroLabelWeight),
                        ClientRectangle, foreColor, MetroPaint.GetTextFormatFlags(TextAlign));
            }
            else
            {
                DestroyBaseTextbox();
                TextRenderer.DrawText(e.Graphics, Text, MetroFonts.Label(metroLabelSize, metroLabelWeight),
                    ClientRectangle, foreColor, MetroPaint.GetTextFormatFlags(TextAlign, wrapToLine));
                OnCustomPaintForeground(new MetroPaintEventArgs(Color.Empty, foreColor, e.Graphics));
            }
        }

        public override void Refresh()
        {
            if (LabelMode == MetroLabelMode.Selectable) UpdateBaseTextBox();
            base.Refresh();
        }

        public override Size GetPreferredSize(Size proposedSize)
        {
            base.GetPreferredSize(proposedSize);
            using (var dc = CreateGraphics())
            {
                proposedSize = new Size(int.MaxValue, int.MaxValue);
                return TextRenderer.MeasureText(dc, Text, MetroFonts.Label(metroLabelSize, metroLabelWeight),
                    proposedSize, MetroPaint.GetTextFormatFlags(TextAlign));
            }
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);
            Invalidate();
        }

        protected override void OnResize(EventArgs e)
        {
            if (LabelMode == MetroLabelMode.Selectable) HideBaseTextBox();
            base.OnResize(e);
        }

        protected override void OnSizeChanged(EventArgs e)
        {
            base.OnSizeChanged(e);
            if (LabelMode == MetroLabelMode.Selectable) ShowBaseTextBox();
        }

        private void CreateBaseTextBox()
        {
            if (baseTextBox.Visible && !firstInitialization || !firstInitialization) return;
            firstInitialization = false;
            if (!DesignMode)
            {
                var form = FindForm();
                if (form != null)
                {
                    form.ResizeBegin += parentForm_ResizeBegin;
                    form.ResizeEnd += parentForm_ResizeEnd;
                }
            }

            baseTextBox.BackColor = Color.Transparent;
            baseTextBox.Visible = true;
            baseTextBox.BorderStyle = BorderStyle.None;
            baseTextBox.Font = MetroFonts.Label(metroLabelSize, metroLabelWeight);
            baseTextBox.Location = new Point(1, 0);
            baseTextBox.Text = Text;
            baseTextBox.ReadOnly = true;
            baseTextBox.Size = GetPreferredSize(Size.Empty);
            baseTextBox.Multiline = true;
            baseTextBox.DoubleClick += BaseTextBoxOnDoubleClick;
            baseTextBox.Click += BaseTextBoxOnClick;
            Controls.Add(baseTextBox);
        }

        private void parentForm_ResizeEnd(object sender, EventArgs e)
        {
            if (LabelMode == MetroLabelMode.Selectable) ShowBaseTextBox();
        }

        private void parentForm_ResizeBegin(object sender, EventArgs e)
        {
            if (LabelMode == MetroLabelMode.Selectable) HideBaseTextBox();
        }

        private void DestroyBaseTextbox()
        {
            if (baseTextBox.Visible)
            {
                baseTextBox.DoubleClick -= BaseTextBoxOnDoubleClick;
                baseTextBox.Click -= BaseTextBoxOnClick;
                baseTextBox.Visible = false;
            }
        }

        private void UpdateBaseTextBox()
        {
            if (!baseTextBox.Visible) return;
            SuspendLayout();
            baseTextBox.SuspendLayout();
            if (UseCustomBackColor)
                baseTextBox.BackColor = BackColor;
            else
                baseTextBox.BackColor = MetroPaint.BackColor.Form(Theme);
            if (!Enabled)
            {
                if (Parent != null)
                {
                    if (UseStyleColors)
                        baseTextBox.ForeColor = MetroPaint.GetStyleColor(Style);
                    else
                        baseTextBox.ForeColor = MetroPaint.ForeColor.Label.Disabled(Theme);
                }
                else if (UseStyleColors)
                {
                    baseTextBox.ForeColor = MetroPaint.GetStyleColor(Style);
                }
                else
                {
                    baseTextBox.ForeColor = MetroPaint.ForeColor.Label.Disabled(Theme);
                }
            }
            else if (Parent != null)
            {
                if (UseStyleColors)
                    baseTextBox.ForeColor = MetroPaint.GetStyleColor(Style);
                else
                    baseTextBox.ForeColor = MetroPaint.ForeColor.Label.Normal(Theme);
            }
            else if (UseStyleColors)
            {
                baseTextBox.ForeColor = MetroPaint.GetStyleColor(Style);
            }
            else
            {
                baseTextBox.ForeColor = MetroPaint.ForeColor.Label.Normal(Theme);
            }

            baseTextBox.Font = MetroFonts.Label(metroLabelSize, metroLabelWeight);
            baseTextBox.Text = Text;
            baseTextBox.BorderStyle = BorderStyle.None;
            Size = GetPreferredSize(Size.Empty);
            baseTextBox.ResumeLayout();
            ResumeLayout();
        }

        private void HideBaseTextBox()
        {
            baseTextBox.Visible = false;
        }

        private void ShowBaseTextBox()
        {
            baseTextBox.Visible = true;
        }

        [SecuritySafeCritical]
        private void BaseTextBoxOnClick(object sender, EventArgs eventArgs)
        {
            WinCaret.HideCaret(baseTextBox.Handle);
        }

        [SecuritySafeCritical]
        private void BaseTextBoxOnDoubleClick(object sender, EventArgs eventArgs)
        {
            baseTextBox.SelectAll();
            WinCaret.HideCaret(baseTextBox.Handle);
        }

        private class DoubleBufferedTextBox : TextBox
        {
            public DoubleBufferedTextBox()
            {
                SetStyle(ControlStyles.SupportsTransparentBackColor | ControlStyles.OptimizedDoubleBuffer, true);
            }
        }
    }
}