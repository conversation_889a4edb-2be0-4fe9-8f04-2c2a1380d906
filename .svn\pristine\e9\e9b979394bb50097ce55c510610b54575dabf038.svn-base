using MetroFramework;
using MetroFramework.Forms;
using O2S.Components.PDFRender4NET;
using OCRTools.Common;
using OCRTools.Common.Hook;
using OCRTools.NewForms;
using OCRTools.Properties;
using OCRTools.Shadow;
using ShareX.ScreenCaptureLib;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Windows.Automation;
using System.Windows.Forms;
using NativeMethods = OCRTools.Common.NativeMethods;
using RECT = OCRTools.Common.RECT;


namespace OCRTools
{
    public partial class FrmMain : MetroForm
    {
        private const string StrProcessOcr = "正在识别，请稍候……";
        private const string StrProcessLater = "正在识别中，请稍候重试！";
        private const string StrUploadImg = "正在上传图片，请稍候……";

        private const int MinFixAreaWidth = 10;

        public static FormTool FrmTool = new FormTool { IsImage = true };

        public static FormUpdate FormUpdate;

        private static readonly int minWidth = 300;
        private static readonly int minHeight = 150;

        internal static Action<List<string>, string, OcrType?, ProcessBy, string> DragDropEventDelegate;

        internal static Action<OcrType?, ProcessBy, Image, string> RecByImageDelegate;

        internal static List<SearchEngine> LstSearchEngine = new List<SearchEngine>();

        private static TransLanguageTypeEnum CurrentTranslateFrom;

        private static TransLanguageTypeEnum CurrentTranslateTo;

        private static OcrType NowOcrType;
        private static int NowOcrGroupType;

        private static bool IsFromLeftToRight = true;
        private static bool IsFromTopToDown = true;

        private readonly BlockingCollection<OcrContent> OcrResultPool = new BlockingCollection<OcrContent>();

        private Speaker CurrentSpeaker;

        private SpiltMode NowSpiltModel;

        private bool IsCopyTrans;

        private bool IsOcrForSearch;

        private bool IsShowOldContent;

        private LoadingType nowLoadingType;

        private TabPage tbContentText;
        private UcContent content;

        private TabPage tbImageBox;
        private UcContent imageBox;

        public FrmMain()
        {
            Opacity = 0;

            SetStyle(
                ControlStyles.UserPaint |
                ControlStyles.AllPaintingInWmPaint |
                ControlStyles.ResizeRedraw |
                ControlStyles.OptimizedDoubleBuffer |
                ControlStyles.ContainerControl, true);

            InitializeComponent();

            ShadowType = CommonString.CommonShadowType;
            CheckForIllegalCrossThreadCalls = false;

            FormClosing += FrmMain_FormClosing;
            msManager.StyleChange += MsManager_StyleChange;
            msManager.ThemeChange += MsManager_ThemeChange;
            CommonMethod.EnableDoubleBuffering(this);
            //Width = Width.DpiValue();
            //Height = Height.DpiValue();
            SizeChanged += FrmMain_SizeChanged;
            CommonMethod.SetStyle(lnkLogin, ControlStyles.Selectable, false);

            notifyMain.Icon = Icon;
            notifyMain.ContextMenuStrip = cmsNotify;
            cmsNotify.Opening += CmsNotify_Opening;
            ImgLogHelper.TickEvent += (a, b) => { CheckLoginStatus(); };

            FrmTool.Icon = Icon;
            FrmTool.ContextMenuStrip = cmsNotify;
            FrmTool.MouseDoubleClick += FrmTool_MouseDoubleClick;

            DragDropEventDelegate = (lstStr, fileExt, ocrType, processBy, fileIdentity) =>
            {
                if (!ocrType.HasValue) ocrType = NowOcrType;
                var fileName = lstStr[0];
                ProcessFile(ocrType.Value, processBy, fileName, fileIdentity, fileExt);
            };
            RecByImageDelegate = (ocrType, processBy, img, url) =>
            {
                ProcessByImage(NowOcrType, processBy, img, false, url);
            };

            try
            {
                this.ControlUseDrop();
                tbMain.ControlUseDrop();
                FrmTool.SetDragDrop();
            }
            catch { }

            tbMain.KeyDown += TxtContent_KeyDown;
            KeyDown += TxtContent_KeyDown;

            InitContent();

            tsmAutoTrans = new ToolStripCheckBoxControl
            {
                Checked = false,
                ForeColor = Color.Black,
                Name = "tsmAutoTrans",
                Size = new Size(88, 29),
                Text = "划词翻译",
                ToolTipText = "鼠标拖选或者双击自动取词",
                Visible = false
            };
            stateStip.Items.Add(tsmAutoTrans);
            tsmAutoTrans.CheckedChanged += TsmAutoTrans_CheckedChanged;

            ////tsmVersion.Text = string.Format("版本:V{0}", Assembly.GetExecutingAssembly().GetName().Version.ToString());
            //tsmPicType.DropDownItems.Clear();
            //foreach (ImageProcessType v in Enum.GetValues(typeof(ImageProcessType)))
            //{
            //    tsmPicType.DropDownItems.Add(new ToolStripMenuItem() { Text = v.ToString(), Tag = v.GetHashCode(), Font = tsmContentType.Font });
            //}

            tsmTransFrom.DropDownItems.Clear();
            tsmTransTo.DropDownItems.Clear();
            foreach (TransLanguageTypeEnum v in Enum.GetValues(typeof(TransLanguageTypeEnum)))
            {
                tsmTransFrom.DropDownItems.Add(new ToolStripMenuItem
                {
                    Text = v.ToString(),
                    Tag = v.GetHashCode(),
                    Font = tsmContentType.Font,
                    Image = Resources.ResourceManager.GetObject(v.ToString()) as Bitmap
                });
                if (!v.Equals(TransLanguageTypeEnum.自动))
                    tsmTransTo.DropDownItems.Add(new ToolStripMenuItem
                    {
                        Text = v.ToString(),
                        Tag = v.GetHashCode(),
                        Font = tsmContentType.Font,
                        Image = Resources.ResourceManager.GetObject(v.ToString()) as Bitmap
                    });
            }

            btnVoice.DropDownItems.Clear();


            if (Enum.TryParse(CommonSetting.朗读配置, out Speaker speaker))
            {
                CurrentSpeaker = speaker;
            }
            foreach (Speaker v in Enum.GetValues(typeof(Speaker)))
            {
                var item = new ToolStripMenuItem
                { Text = v.ToString(), Tag = v.GetHashCode(), Font = tsmContentType.Font };
                item.Click += SpeakerItem_Click;
                if (v.Equals(CurrentSpeaker)) item.Checked = true;
                btnVoice.DropDownItems.Add(item);
            }

            tsmAutoTrans.SetStyleManager(msManager);
            tsmShowOldContent.SetStyleManager(msManager);

            Application.ApplicationExit += Application_ApplicationExit;

            //MessageBox.Show(str);
        }

        private void SetDefaultTabContentName(TabPage tabPage)
        {
            tabPage.Text = "【识别内容】";
            tabPage.Tag = "识别内容";
        }

        internal static OcrContent EmptyContent = new OcrContent { result = new ResultEntity { verticalText = "{}" } };

        private void InitContent()
        {
            tbImageBox = new TabPage()
            {
                Padding = Padding.Empty,
                Margin = Padding.Empty,
                Name = "tbImageBox",
                Text = "图像预览",
            };
            tbContentText = new TabPage()
            {
                Padding = Padding.Empty,
                Margin = Padding.Empty,
                Name = "tbContentText",
            };
            SetDefaultTabContentName(tbContentText);
            content = new UcContent
            {
                Dock = DockStyle.Fill,
                SpiltModel = NowSpiltModel,
                IsShowOldContent = IsShowOldContent,
                MenuStrip = cmsNotify,
            };
            content.TxtKeyDownEventDelegate = TxtContent_KeyDown;
            content.SetDragDrop();
            imageBox = new UcContent
            {
                Dock = DockStyle.Fill,
            };
            imageBox.SetDragDrop();
            imageBox.SetImageMode(true);
            tbMain.Controls.Add(tbContentText);
            tbMain.Controls.Add(tbImageBox);
            //tbImageBox.Parent = null;
            tbContentText.Controls.Add(content);
            tbImageBox.Controls.Add(imageBox);

            imageBox.OcrContent = FrmMain.EmptyContent;
            imageBox.BindImage(null, null);
            tbMain.SelectedIndexChanged += (sender, e) =>
            {
                (tbMain.SelectedTab?.Controls[0] as UcContent)?.ShowImageTool();
            };
        }

        internal LoadingType NowLoadingType
        {
            get => nowLoadingType;
            set
            {
                nowLoadingType = value;
                ucLoading1.SetLoadingType(NowLoadingType);
            }
        }

        internal static SearchEngine NowSearchEngine { get; set; }
        private static DisplayModel NowDisplayMode { get; set; }

        private void ProcessByImage(OcrType ocrType, ProcessBy processBy, Image image, bool isCapture,
            string tagUrl = null, bool isSearch = false)
        {
            var fileName = SaveImage(image, isCapture);
            if (!Equals(processBy, ProcessBy.固定区域))
                image = ProcessImgSize(image);
            SetPicImage(image, tagUrl, Equals(processBy, ProcessBy.主界面));
            OcrPoolProcess.ProcessByImage(ocrType, NowOcrGroupType, IsFromLeftToRight, IsFromTopToDown,
                CurrentTranslateFrom, CurrentTranslateTo, image, tagUrl, CommonString.StrDefaultImgType, fileName,
                false, isSearch, processBy, null);
        }

        private string SaveImage(Image image, bool isCapture)
        {
            if (CommonSetting.复制到粘贴板)
                ClipboardService.ClipSetImage(image, false);
            if (CommonSetting.自动保存)
            {
                var fileName = image.SaveFileWithOutConfirm(null, !isCapture);
                if (isCapture && CommonSetting.显示Toast通知) CommonMethod.ShowCaptureNotificationTip(fileName);
                return fileName;
            }

            return string.Empty;
        }

        private Image ProcessImgSize(Image image)
        {
            if (image != null)
            {
                if (image.Width < minWidth || image.Height < minHeight)
                {
                    using (var bitmap = new Bitmap(image.Width > minWidth ? image.Width : minWidth,
                        image.Height > minHeight ? image.Height : minHeight))
                    {
                        using (var graphics = Graphics.FromImage(bitmap))
                        {
                            graphics.Clear(Color.Transparent);
                            // 设置画布的描绘质量
                            graphics.CompositingQuality = CompositingQuality.HighQuality;
                            graphics.SmoothingMode = SmoothingMode.HighQuality;
                            graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                            graphics.DrawImage(image, (bitmap.Width - image.Width) / 2,
                                (bitmap.Height - image.Height) / 2,
                                image.Width, image.Height);
                            graphics.Save();
                            graphics.Dispose();
                        }

                        image = new Bitmap(bitmap);
                    }
                }
            }
            return image;
        }

        private void FrmMain_SizeChanged(object sender, EventArgs e)
        {
            BindUserImg();
        }

        private void ShowMsgControl()
        {
            CommonMsg.ShowToWindow(this, new Point(8, (Height - tbMain.Top - 40) / 2), true);
        }

        private void FrmMain_Shown(object sender, EventArgs e)
        {
            StaticValue.Handles.Add(Handle);
            if (!CommonSetting.启动后打开主窗体) Hide();
            Opacity = 1;
            ShowMsgControl();
            if (CommonString.IsAutoLogin)
            {
                try
                {
                    ShortcutHelpers.RegFileType();
                }
                catch { }
                ProcessSystemMenuMsg();
                ProcessOcrMsg();
            }
        }

        private void ProcessSystemMenuMsg()
        {
            try
            {
                var watch = new FileSystemWatcher
                {
                    Path = CommonSetting.ConfigPath,
                    Filter = CommonSetting.ConfigName,
                    NotifyFilter = NotifyFilters.LastWrite,
                    IncludeSubdirectories = false
                };
                watch.Changed += (source, e) =>
                {
                    try
                    {
                        watch.EnableRaisingEvents = false;
                        ProcessOcrMsg();
                    }
                    finally
                    {
                        watch.EnableRaisingEvents = true;
                    }
                };
                watch.EnableRaisingEvents = true;
            }
            catch { }
        }

        private void ProcessOcrMsg()
        {
            try
            {
                var strFile = IniHelper.GetValue("系统", "右键菜单");
                if (string.IsNullOrEmpty(strFile)) return;

                var ocrType = 0;
                if (strFile.Contains(" "))
                {
                    ocrType = BoxUtil.GetInt32FromObject(strFile.Substring(strFile.LastIndexOf(" ") + 1).Trim(), 0);
                    strFile = strFile.Substring(0, strFile.LastIndexOf(" "));
                }

                IniHelper.SetValue("系统", "右键菜单", "");

                switch (ocrType)
                {
                    case 0:
                        DragDropEventDelegate?.Invoke(new List<string> { strFile }, null, OcrType.文本, ProcessBy.主界面, null);
                        break;
                    case 1:
                        DragDropEventDelegate?.Invoke(new List<string> { strFile }, null, OcrType.竖排, ProcessBy.主界面, null);
                        break;
                    case 2:
                        DragDropEventDelegate?.Invoke(new List<string> { strFile }, null, OcrType.表格, ProcessBy.主界面, null);
                        break;
                    case 3:
                        DragDropEventDelegate?.Invoke(new List<string> { strFile }, null, OcrType.公式, ProcessBy.主界面, null);
                        break;
                    case 4:
                        DragDropEventDelegate?.Invoke(new List<string> { strFile }, null, OcrType.翻译, ProcessBy.主界面, null);
                        break;
                    case 11:
                        FrmTool.Invoke((Action)delegate
                        {
                            this.ViewImageFile(strFile, Point.Empty);
                        });
                        break;
                    case 12:
                        CommonMethod.ShowHelpMsg(string.Format("正在压缩图片[{0}]，请稍候…", Path.GetFileNameWithoutExtension(strFile)));
                        Task.Factory.StartNew(() =>
                        {
                            var compressType = CommonString.IsOnLine ? CompressType.TinyPng : CompressType.助手压缩;
                            var strNewFile = ImageCompress.CompressImageFile(strFile, Path.GetDirectoryName(strFile), compressType);
                            if (string.IsNullOrEmpty(strNewFile) && CommonString.IsOnLine)
                            {
                                strNewFile = ImageCompress.CompressImageFile(strFile, Path.GetDirectoryName(strFile), CompressType.助手压缩);
                            }
                            if (!string.IsNullOrEmpty(strNewFile))
                            {
                                var oldSize = CommonMethod.FormatBytes(strFile);
                                var outPutSize = CommonMethod.FormatBytes(strNewFile);
                                CommonMethod.OpenFolderWithFile(strNewFile);
                                CommonMethod.ShowHelpMsg(string.Format("图片[{2}]压缩成功！原：{0}，新：{1}", oldSize, outPutSize, Path.GetFileNameWithoutExtension(strFile)));
                            }
                            else
                            {
                                CommonMethod.ShowHelpMsg(string.Format("图片[{0}]压缩失败，请稍候重试！", Path.GetFileNameWithoutExtension(strFile)));
                            }
                        });
                        break;
                    default:
                        FrmTool.Invoke((Action)delegate
                        {
                            FrmTool.ViewImage(null, strFile);
                        });
                        break;
                }
            }
            catch { }
        }

        private void TsmAutoTrans_CheckedChanged(object sender, EventArgs e)
        {
            IsCopyTrans = tsmAutoTrans.Checked;
            if (IsCopyTrans)
            {
                HookManager.MouseSelected += mouseHook_MouseSelected;
                HookManager.MouseUp += HookManager_MouseClick;
                HookManager.MouseDoubleClick += mouseHook_DoubleClick;
                HookManager.SubscribedToGlobalMouseEvents();
            }
            else
            {
                HookManager.MouseSelected -= mouseHook_MouseSelected;
                HookManager.MouseUp -= HookManager_MouseClick;
                HookManager.MouseDoubleClick -= mouseHook_DoubleClick;
                HookManager.UnsunscribeFromGlobalMouseEvents();
                HookManager_MouseClick(null, null);
            }

            CommonSetting.SetValue("划词翻译", IsCopyTrans);
        }

        private void Application_ApplicationExit(object sender, EventArgs e)
        {
            HookManager.UnsunscribeFromGlobalMouseEvents();
        }

        private void TxtContent_KeyDown(object sender, KeyEventArgs e)
        {
            if (!e.Control || e.KeyCode != Keys.V) return;
            try
            {
                var img = ClipboardService.GetImage();
                if (img != null)
                {
                    ProcessByImage(NowOcrType, ProcessBy.主界面, img, false);
                }
                else
                {
                    var imgFile = ClipboardService.GetOneFile();
                    if (!string.IsNullOrEmpty(imgFile))
                    {
                        ProcessFile(NowOcrType, ProcessBy.主界面, imgFile, null, null);
                    }
                    else if (sender != null)
                    {
                        var txt = ClipboardService.GetText(true);
                        if (!string.IsNullOrEmpty(txt))
                        {
                            if (sender is RichTextBox textBox)
                            {
                                textBox.AppendText(txt);
                            }
                            else if (sender is bool isTrans)
                            {
                                if (isTrans)
                                {
                                    ShowMiniSearch(txt, ClipboardContentType.文本, Point.Empty);
                                    //TransByText(txt);
                                }
                            }
                        }
                    }
                }
            }
            catch
            {
                // ignored
            }

            e.Handled = true;
        }

        private void FrmMain_Load(object sender, EventArgs e)
        {
            InitScreenShot();
            InitAllConfig();

            ucLoading1.InitLoading(tbMain.Size, tbMain.Location);
            ucLoading1.Anchor = tbMain.Anchor;
            //ucLoading1.ShowLoading("正在识别，请稍候……");

            Task.Factory.StartNew(() =>
            {
                Screenshot.GetWorkAreaRectangle();
                InitNetWorkInfo();
                InitOthersSync();
                NetWorkChangeEvent();
                CommonUpdate.InitUpdate();
                CommonWeather.InitWeather();

                InitSearchEngines();
                SetSearchEngine();

                if (CommonSetting.启用本地识别)
                {
                    LocalOcrService.OpenOcrService((int)CommonSetting.本地识别端口, (int)CommonSetting.本地识别线程数, false);
                }
            });
        }

        private void InitOthersSync()
        {
            OcrProcessThread();
            OcrResultProcessThread();
            OcrHelper.InitOcrGroup();
            //try
            //{
            //    CommonMethod.CheckShortcut();
            //}
            //catch (Exception)
            //{
            //    CommonMethod.ShowHelpMsg("创建快捷方式到桌面失败！", 5000);
            //}
        }

        private void ProcessFile(OcrType ocrType, ProcessBy processBy, string fileName, string fileIdentity,
            string fileExt)
        {
            if (Program.NowUser == null || string.IsNullOrEmpty(fileName))
            {
                CommonMethod.ShowHelpMsg("未登录用户不支持文件识别，请先注册！");
                return;
            }

            fileExt = CommonMethod.GetFileExt(fileName, fileExt);

            if (!CommonString.LstCanProcessFilesExt.Contains(fileExt))
            {
                MessageBox.Show(this,
                    "请选择以下类型的文件！\n【图片】" + string.Join("、", CommonString.LstCanProcessImageFilesExt) + "\n【文档】" +
                    string.Join("、", CommonString.LstCanProcessDocFilesExt), CommonString.StrReminder, MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
                return;
            }

            if (CommonString.LstCanProcessImageFilesExt.Contains(fileExt) &&
                Program.NowUser?.IsSupportImageFile == false)
            {
                CommonMethod.ShowHelpMsg("当前账户不支持图片文件识别！");
                return;
            }

            if (ocrType.Equals(OcrType.翻译) && Program.NowUser?.IsSupportTranslate == false)
            {
                CommonMethod.ShowHelpMsg("当前账户不支持翻译功能！");
                return;
            }

            if (CommonString.LstCanProcessDocFilesExt.Contains(fileExt) && !Equals(fileExt, CommonString.StrDefaultTxtType) &&
                Program.NowUser?.IsSupportDocFile == false)
            {
                CommonMethod.ShowHelpMsg("当前账户不支持文档文件识别！");
                return;
            }

            if (!Equals(processBy, ProcessBy.批量识别) && fileName.EndsWith(".pdf"))
            {
                using (var pdfProcess = new FormPdfProcess()
                { Icon = Icon, Theme = msManager.Theme, Style = msManager.Style, StyleManager = msManager })
                {
                    pdfProcess.TopMost = true;
                    if (pdfProcess.ShowDialog(this) != DialogResult.OK)
                    {
                        return;
                    }

                    if (pdfProcess.IsProcessByImage)
                    {
                        ProcessByPDFFile(fileName, pdfProcess.IsSpiltImage);
                        return;
                    }
                }
            }

            var imgUrl = "";
            var bitmap = OcrPoolProcess.ProcessByFile(ocrType, NowOcrGroupType, IsFromLeftToRight,
                IsFromTopToDown, CurrentTranslateFrom, CurrentTranslateTo, fileName, imgUrl, fileExt, false, false,
                processBy, fileIdentity);
            SetPicImage(bitmap, imgUrl, Equals(processBy, ProcessBy.主界面));
        }

        private void FrmMain_FormClosing(object sender, FormClosingEventArgs e)
        {
            WindowState = FormWindowState.Minimized;
            Visible = false;
            e.Cancel = true;
        }

        private void InitAllConfig()
        {
            FrmTool.TopMost = true;
            FrmTool.Visible = CommonSetting.显示工具栏;
            if (!CommonString.IsAutoLogin)
            {
                FrmTool.Left -= FrmTool.Width;
            }

            NowDisplayMode = CommonSetting.图文模式 ? DisplayModel.图文模式 : DisplayModel.文字模式;
            NowOcrGroupType = OcrHelper.GetGroupByName(CommonSetting.识别引擎);

            if (Enum.TryParse(CommonSetting.分段模式, out SpiltMode spiltMode))
            {
                NowSpiltModel = spiltMode;
            }
            if (Enum.TryParse(CommonSetting.加载动画, out LoadingType loadingType))
            {
                NowLoadingType = loadingType;
            }

            InitSearchMenu();
            ProcessForbidControls();

            InitOcrGroupItems();
            InitSpiltModel();
            InitBiaoDian();
            BindSpiltModel(NowSpiltModel);

            IsShowOldContent = CommonSetting.显示原文;
            tsmShowOldContent.Checked = IsShowOldContent;
            IsCopyTrans = CommonSetting.划词翻译;
            tsmAutoTrans.Checked = IsCopyTrans;

            RefreshUcContent();
            SetTheme();

            SetTopMost(CommonSetting.窗体置顶);

            InitItemTypeByValue(tsmTransFrom, CommonSetting.源语言);
            InitItemTypeByValue(tsmTransTo, CommonSetting.目标语言);
            InitItemTypeByValue(tsmContentType, OcrType.文本.ToString());

            InitVerticalDirection();
            if (string.IsNullOrEmpty(CommonSetting.竖排方向))
            {
                CommonSetting.竖排方向 = "上下左右";
            }
            InitItemTypeByValue(tsmVerticalDirection, CommonSetting.竖排方向);

            UpdatePicViewModel(NowDisplayMode);
        }

        private void InitVerticalDirection()
        {
            var strTopLeft = GetVerticalDirectionStr(true, true);
            var tsmTopLeft = new ToolStripMenuItem
            {
                Tag = strTopLeft,
                Text = strTopLeft,
                Image = GetVerticalDirectionImage(true, true),
                Font = new Font("Microsoft YaHei UI", 10F),
                ToolTipText = "方向：从上到下，从左到右\n场景：通用印刷体\n\n例子：\n白日依山尽，黄河入海流。\n欲穷千里目，更上一层楼！"
            };
            var strTopRight = GetVerticalDirectionStr(true, false);
            var tsmTopRight = new ToolStripMenuItem
            {
                Tag = strTopRight,
                Text = strTopRight,
                Image = GetVerticalDirectionImage(true, false),
                Font = new Font("Microsoft YaHei UI", 10F),
                ToolTipText = "方向：从上到下，从右到左\n场景：古诗文\n\n例子：\n" + "更 欲 黄 白     登\n" +
                              "上 穷 河 日 唐 鹳\n" +
                              "一 千 入 依 王 雀\n" +
                              "层 里 海 山 之 楼\n" +
                              "楼 目 流 尽 涣"
            };
            var strDownLeft = GetVerticalDirectionStr(false, true);
            var tsmDownLeft = new ToolStripMenuItem
            {
                Tag = strDownLeft,
                Text = strDownLeft,
                Image = GetVerticalDirectionImage(false, true),
                Font = new Font("Microsoft YaHei UI", 10F),
                ToolTipText = "方向：从下到上，从左到右\n场景：古籍，现代多见于车道上的文字\n\n例子：\n道\n车\n交\n公"
            };
            var strDownRight = GetVerticalDirectionStr(false, false);
            var tsmDownRight = new ToolStripMenuItem
            {
                Tag = strDownRight,
                Text = strDownRight,
                Image = GetVerticalDirectionImage(false, false),
                Font = new Font("Microsoft YaHei UI", 10F),
                ToolTipText = "方向：从下到上，从左到右\n场景：古籍，不常见\n\n例子：\n自行脑补"
            };
            tsmVerticalDirection.DropDownItems.AddRange(new ToolStripItem[] { tsmTopLeft, tsmTopRight, tsmDownLeft, tsmDownRight });
        }

        private void InitSearchEngines()
        {
            LstSearchEngine.Add(new SearchEngine
            {
                Name = "百度",
                Url = "https://www.baidu.com/s?wd={0}"
            });
            LstSearchEngine.Add(new SearchEngine
            {
                Name = "谷歌",
                Url = "https://www.google.com/search?q={0}"
            });
            LstSearchEngine.Add(new SearchEngine
            {
                Name = "必应",
                Url = "https://cn.bing.com/search?q={0}"
            });
            LstSearchEngine.Add(new SearchEngine
            {
                Name = "360",
                Url = "https://www.so.com/s?q={0}"
            });
            LstSearchEngine.Add(new SearchEngine
            {
                Name = "神马",
                Url = "https://m.sm.cn/s?q={0}"
            });
        }

        private void SetSearchEngine()
        {
            NowSearchEngine = LstSearchEngine.FirstOrDefault(p => p.Name.Equals(CommonSetting.搜索引擎));
            if (NowSearchEngine == null) NowSearchEngine = LstSearchEngine.FirstOrDefault();
        }

        private void RefreshUcContent()
        {
            var lstControl = GetNowBindUserControls();
            lstControl.ForEach(p => { p.RefreshStyle(); });
        }

        private void MsManager_ThemeChange(object sender, EventArgs e)
        {
            CommonString.IsDarkModel = msManager.Theme == MetroThemeStyle.Dark;
            RefreshAllStyleImage();
        }

        private void RefreshAllStyleImage()
        {
            if (tsmContentType.Visible)
                tsmContentType.Image = ProcessStyleImage(Resources.ResourceManager.GetObject(NowOcrType.ToString()) as Bitmap);
            if (tsmPicViewModel.Visible)
                tsmPicViewModel.Image = ProcessStyleImage(Resources.ResourceManager.GetObject(NowDisplayMode.ToString()) as Bitmap);
            if (tmsSpiltMode.Visible)
                tmsSpiltMode.Image = ProcessStyleImage(Resources.ResourceManager.GetObject(NowSpiltModel.ToString()) as Bitmap);
            if (tsmVerticalDirection.Visible)
                tsmVerticalDirection.Image = ProcessStyleImage(GetVerticalDirectionImage(IsFromTopToDown, IsFromLeftToRight));
            if (btnVoice.Visible)
                btnVoice.Image = ProcessStyleImage(Resources.语音);
        }

        private string GetVerticalDirectionStr(bool isFromTopToDown, bool isFromLeftToRight)
        {
            var strTmp = string.Format("{0}{1}", isFromTopToDown ? "上下" : "下上", isFromLeftToRight ? "左右" : "右左");
            return strTmp;
        }

        private Bitmap GetVerticalDirectionImage(bool isFromTopToDown, bool isFromLeftToRight)
        {
            return Resources.ResourceManager.GetObject(GetVerticalDirectionStr(isFromTopToDown, isFromLeftToRight)) as Bitmap;
        }

        private void MsManager_StyleChange(object sender, EventArgs e)
        {
        }

        private Image ProcessStyleImage(Image image)
        {
            if (image == null) return null;
            if (CommonString.IsDarkModel)
                return Inverse(new Bitmap(image));
            return image;
        }

        /// <summary>
        ///     反色处理
        /// </summary>
        private Image Inverse(Bitmap bmp)
        {
            var srcdat = bmp.LockBits(new Rectangle(Point.Empty, bmp.Size), ImageLockMode.ReadWrite,
                PixelFormat.Format24bppRgb); // 锁定位图
            unsafe // 不安全代码
            {
                var pix = (byte*)srcdat.Scan0; // 像素首地址
                for (var i = 0; i < srcdat.Stride * srcdat.Height; i++)
                {
                    if (pix[i] == 0 || pix[i] == 255) continue;
                    pix[i] = (byte)(255 - pix[i]);
                }

                bmp.UnlockBits(srcdat); // 解锁
                return bmp;
            }
        }

        private void notifyMain_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            NotifyMainMouseDoubleClick(CommonSetting.双击托盘操作, ToolDoubleClickEnum.显示主窗体);
        }

        private void FrmTool_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            FrmTool.SetNotMove();
            NotifyMainMouseDoubleClick(CommonSetting.双击工具栏操作, ToolDoubleClickEnum.显示主窗体);
        }

        private void NotifyMainMouseDoubleClick(string enumStr, ToolDoubleClickEnum defaultClick)
        {
            if (!Enum.TryParse(enumStr, out ToolDoubleClickEnum clickEnum))
            {
                clickEnum = defaultClick;
            }
            switch (clickEnum)
            {
                case ToolDoubleClickEnum.显示主窗体:
                    ShowWindow();
                    break;
                case ToolDoubleClickEnum.截图识别:
                    截图识别ToolStripMenuItem_Click(null, null);
                    break;
                case ToolDoubleClickEnum.快速截图:
                    ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.截图), null);
                    break;
                case ToolDoubleClickEnum.截图编辑:
                    ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.截图编辑), null);
                    break;
                case ToolDoubleClickEnum.快速贴图:
                    ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.快速贴图), null);
                    break;
                case ToolDoubleClickEnum.截图贴图:
                    ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.截图贴图), null);
                    break;
                case ToolDoubleClickEnum.粘贴贴图:
                    文字贴图ToolStripMenuItem_Click(null, null);
                    break;
                case ToolDoubleClickEnum.显隐贴图:
                    ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.显隐贴图), null);
                    break;
            }
        }

        private void CmsNotify_Opening(object sender, CancelEventArgs e)
        {
            tsmShowTool.Visible = false;
            if (sender != null)
            {
                tsmTrans.Visible = false;
                tsmSearch.Visible = false;
                tsmCopyTxt.Visible = false;
                tsmExportTxt.Visible = false;
                tsmExportExcel.Visible = false;
                tsmSpiltExport.Visible = false;

                tsmToolImage.Visible = false;

                tsmTransfer.Visible = false;

                tsmShowMain.Visible = !Visible;
                tsmShowMain.Text = (!Visible ? "显示" : "隐藏") + "窗体";
                tsmOcrGroupSpilt.Visible = true;

                var ctrl = (sender as ContextMenuStrip).SourceControl;
                if (ctrl is null || ctrl is Panel && ctrl.Name == "")
                {
                    tsmShowTool.Visible = true;
                    tsmShowTool.Text = (!FrmTool.Visible ? "显示" : "隐藏") + "工具栏";
                    //tsmToolImage.Visible = true;
                    //tsmToolImage.Text = ((!FrmTool.TopMost) ? "" : "取消") + "置顶工具栏";
                    //tsmTopWindow.Visible = true;
                    //tsmTopWindow.Text = ((!TopMost) ? "" : "取消") + "置顶窗体";
                }
                else if (ctrl is Form)
                {
                    tsmShowTool.Visible = true;
                    tsmShowTool.Text = (!FrmTool.Visible ? "显示" : "隐藏") + "工具栏";
                    tsmToolImage.Visible = true;
                }
                else
                {
                    tsmOcrGroupSpilt.Visible = !Visible;
                    if (ctrl is RichTextBox || ctrl is PanelPictureView)
                    {
                        if (ctrl is RichTextBox
                            && (ctrl as RichTextBox).SelectedText.Length > 0)
                        {
                            tsmSearch.Visible = true;
                            tsmTrans.Visible = Program.NowUser?.IsSupportTranslate == true;
                            tsmTransfer.Visible = true;
                        }

                        tsmCopyTxt.Visible = true;
                        tsmExportTxt.Visible = true;
                        tsmSpiltExport.Visible = true;
                    }
                    else if (ctrl is DataGridViewEx)
                    {
                        tsmExportExcel.Visible = true;
                        tsmSpiltExport.Visible = true;
                    }
                }
            }
        }

        private void SetTopMost(bool result)
        {
            TopMost = result;
            tipMain.SetToolTip(pnlTop, TopMost ? "取消置顶" : "置顶窗体");
            pnlTop.BackgroundImage = TopMost ? Resources.top : Resources.untop;
        }

        private bool InitItemTypeByValue(ToolStripDropDownButton toolStripDropDownButton, string objValue)
        {
            if (string.IsNullOrEmpty(objValue)) return false;
            var result = false;
            foreach (ToolStripDropDownItem item in toolStripDropDownButton.DropDownItems)
                if (item.Text.Equals(objValue) || item.Tag?.ToString().Equals(objValue) == true)
                {
                    result = true;
                    tsmDDL_DropDownItemClicked(toolStripDropDownButton, new ToolStripItemClickedEventArgs(item));
                    break;
                }

            return result;
        }

        private List<UcContent> GetNowBindUserControls()
        {
            var lstUcContent = new List<UcContent>();
            foreach (TabPage tab in tbMain.TabPages)
            {
                if (Equals(tab, tbImageBox))
                {
                    continue;
                }
                foreach (var ctrl in tab.Controls)
                    if (ctrl is UcContent ucContent)
                    {
                        lstUcContent.Add(ucContent);
                        break;
                    }
            }
            return lstUcContent;
        }

        private void SetUcContentSpiltMode(SpiltMode spiltMode)
        {
            var lstControl = GetNowBindUserControls();
            lstControl.ForEach(p => { p.SpiltModel = spiltMode; });
        }

        private void SetUcContentTranslateMode(bool showOldContent)
        {
            var lstControl = GetNowBindUserControls();
            lstControl.ForEach(p => { p.IsShowOldContent = showOldContent; });
        }

        //private void SetUcContentOcrType(OCRType ocrType)
        //{
        //    var lstControl = GetNowBindUserControls();
        //    //lstControl.ForEach(p =>
        //    //{
        //    //    p.OcrType = ocrType;
        //    //});
        //}

        private void SetUcContentBiaoDianMode(BiaoDianMode biaoDianModel)
        {
            var lstControl = GetNowBindUserControls();
            lstControl.ForEach(p => { p.BiaoDianMode = biaoDianModel; });
        }

        private void SetUcContentHanZiMode(HanZiMode hanZi)
        {
            var lstControl = GetNowBindUserControls();
            lstControl.ForEach(p => { p.HanZiMode = hanZi; });
        }

        private void SetUcContentPinYin(bool isPinYin)
        {
            var lstControl = GetNowBindUserControls();
            lstControl.ForEach(p => { p.IsPinYin = isPinYin; });
        }

        private void SetUcContentDisplayMode(DisplayModel model)
        {
            var lstControl = GetNowBindUserControls();
            lstControl.ForEach(p => { p.NowDisplayMode = model; });
        }

        private void tsmDDL_DropDownItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            var item = e.ClickedItem;
            if (item == null || item.Tag == null) return;
            ToolStripDropDownButton ddlBtn = null;
            string strOpType;
            if (sender is string)
            {
                strOpType = sender?.ToString();
            }
            else
            {
                ddlBtn = sender as ToolStripDropDownButton;
                strOpType = ddlBtn?.Tag?.ToString();
            }

            if (string.IsNullOrEmpty(strOpType)) return;
            switch (strOpType)
            {
                case "识别方式":
                    ddlBtn.Text = item.Text;
                    ddlBtn.Image = ProcessStyleImage(item.Image);
                    var selectedType = (OcrType)BoxUtil.GetInt32FromObject(item.Tag?.ToString());
                    if (!selectedType.Equals(NowOcrType))
                    {
                        SetOcrType(selectedType);
                        IniHelper.SetValue("配置", strOpType, item.Text);
                    }

                    break;
                case "竖排方向":
                    ddlBtn.Text = item.Text;
                    ddlBtn.Image = ProcessStyleImage(item.Image);
                    IsFromTopToDown = item.Tag?.ToString().Contains("上下") == true;
                    IsFromLeftToRight = item.Tag?.ToString().Contains("左右") == true;
                    CommonSetting.SetValue("竖排方向", GetVerticalDirectionStr(IsFromTopToDown, IsFromLeftToRight));
                    break;
                case "翻译语言From":
                    ddlBtn.Image = item.Image;
                    BindTranslateLanguage(item.Tag, true);
                    CommonSetting.SetValue("源语言", CurrentTranslateFrom.ToString());
                    break;
                case "翻译语言To":
                    ddlBtn.Image = item.Image;
                    BindTranslateLanguage(item.Tag, false);
                    CommonSetting.SetValue("目标语言", CurrentTranslateTo.ToString());
                    break;
            }
        }

        private void SetOcrType(OcrType selectedType)
        {
            if (!selectedType.Equals(NowOcrType))
            {
                NowOcrType = selectedType;
                CloseAllTabs();

                btnVoice.Visible = NowOcrType != OcrType.表格 && NowOcrType != OcrType.公式;
                tsmPicViewModel.Visible = NowOcrType != OcrType.表格 && NowOcrType != OcrType.公式;
                tmsSpiltMode.Visible = NowDisplayMode == DisplayModel.文字模式 && NowOcrType != OcrType.翻译 && NowOcrType != OcrType.表格 && NowOcrType != OcrType.公式;
                tsmVerticalDirection.Visible = NowOcrType == OcrType.竖排;
                tsmTransFrom.Visible = NowOcrType == OcrType.翻译;
                tsmTransTo.Visible = NowOcrType == OcrType.翻译;
                tsmAutoTrans.Visible = NowOcrType == OcrType.翻译;
                tsmShowOldContent.Visible = NowOcrType == OcrType.翻译;
            }
        }

        private void BindSpiltModel(SpiltMode model)
        {
            NowSpiltModel = model;
            tmsSpiltMode.Text = model.ToString();
            RefreshAllStyleImage();
            tsmVerticalDirection.Visible = NowOcrType == OcrType.竖排;
            SetUcContentSpiltMode(model);
        }

        private void tsmShowOldContent_CheckedChanged(object sender, EventArgs e)
        {
            BindTranslateModel();
        }

        private void BindTranslateModel()
        {
            IsShowOldContent = tsmShowOldContent.Checked; // ? TranslateMode.显示译文 : TranslateMode.译文加原文;
            tsmShowOldContent.Checked = IsShowOldContent;
            tsmShowOldContent.ToolTipText = IsShowOldContent ? "显示原文+译文" : "仅显示译文";
            SetUcContentTranslateMode(IsShowOldContent);
            CommonSetting.SetValue("显示原文", IsShowOldContent);
        }

        private void BindTranslateLanguage(object obj, bool isFrom)
        {
            var type = (TransLanguageTypeEnum)BoxUtil.GetInt32FromObject(obj?.ToString(), 0);
            if (isFrom)
            {
                CurrentTranslateFrom = type;
                tsmTransFrom.Text = CurrentTranslateFrom.ToString();
            }
            else
            {
                CurrentTranslateTo = type;
                tsmTransTo.Text = CurrentTranslateTo.ToString();
            }
        }

        private UcContent GetCurrentContent()
        {
            foreach (var ctrl in tbMain.SelectedTab.Controls)
                if (ctrl is UcContent)
                    return ctrl as UcContent;
            return null;
        }

        private bool CheckIsOnRec()
        {
            if (CommonString.IsOnRec) CommonMethod.ShowHelpMsg(StrProcessLater);
            return CommonString.IsOnRec;
        }

        private void 批量识别ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var frmBatch = new FrmBatchOCR
            { Icon = Icon, Theme = msManager.Theme, Style = msManager.Style, StyleManager = msManager };
            frmBatch.Show();
        }

        private void 批量压缩ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var frmBatch = new FrmBatchCompress
            { Icon = Icon, Theme = msManager.Theme, Style = msManager.Style, StyleManager = msManager };
            frmBatch.Show();
        }

        private void ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            cmsNotify.Hide();
            if (sender == null) return;
            var name = sender is ToolStripMenuItem
                ? ((ToolStripMenuItem)sender).Name
                : ((ToolStripButton)sender).Name;
            var control = GetCurrentContent();
            switch (name)
            {
                case "复制全部ToolStripMenuItem":
                case "tsmCopyTxt":
                    var strContent = control?.GetContentText();
                    if (!string.IsNullOrEmpty(strContent)) ClipboardService.SetText(strContent);
                    //txtContent.SelectAll();
                    //txtContent.Copy();
                    return;
                case "保存文本ToolStripMenuItem":
                case "tsmExportTxt":
                    SaveTxtFile(control?.GetContentText());
                    return;
                case "导出ExcelToolStripMenuItem":
                case "tsmExportExcel":
                    control?.ExportExcel();
                    return;
                case "文件识别ToolStripMenuItem":
                case "图片识别StripMenuItem":
                //case "文件ToolStripMenuItem":
                case "打开ToolStripMenuItem":
                    if (CheckIsOnRec()) return;
                    using (var imgFileDialog = new OpenFileDialog
                    {
                        Title = "请选择文件",
                        Filter = "图片或文档|*.png;*.jpg;*.jpeg;*.bmp;*.pdf;*.doc;*.docx;*.txt;",
                        RestoreDirectory = true,
                        Multiselect = false
                    })
                    {
                        if (imgFileDialog.ShowDialog(this) == DialogResult.OK)
                            ProcessFile(NowOcrType, ProcessBy.主界面, imgFileDialog.FileName, null, null);
                    }

                    return;
                case "PDF识别ToolStripMenuItem":
                case "PDF识别StripMenuItem":
                    if (CheckIsOnRec()) return;
                    using (var pdfFileDialog = new OpenFileDialog
                    {
                        Title = "请选择PDF文档",
                        Filter = "PDF文档|*.pdf;",
                        RestoreDirectory = true,
                        Multiselect = false
                    })
                    {
                        if (pdfFileDialog.ShowDialog(this) == DialogResult.OK)
                            ProcessFile(NowOcrType, ProcessBy.主界面, pdfFileDialog.FileName, null, null);
                    }

                    return;
                case "粘贴识别ToolStripMenuItem":
                    CopyAction();
                    return;
            }
        }

        private void CopyAction(bool isTrans = false)
        {
            if (CheckIsOnRec()) return;
            TxtContent_KeyDown(isTrans, new KeyEventArgs(Keys.Control | Keys.V));
        }

        private void ProcessByPDFFile(string fileName, bool isSpiltImage)
        {
            CloseAllTabs();
            ShowWindow();
            Task.Factory.StartNew(() =>
            {
                try
                {
                    //CommonMethod.ShowLoading();
                    var objLock = "";
                    ucLoading1.ShowLoading("正在解析PDF，请稍候……");
                    Dictionary<int, List<Bitmap>> dicImgContent = ConvertPdf2Image(fileName, isSpiltImage);
                    Dictionary<int, string> dicContent = new Dictionary<int, string>();
                    ucLoading1.ShowText(string.Format("正在识别第1/{0}页……", dicImgContent.Count));
                    bool isUplpadImg = false;
                    var execPerTime = (Program.NowUser?.PerTimeSpan ?? 15000) / (Program.NowUser?.PerTimeSpanExecCount ?? 1) + 1000;
                    foreach (var pageIndex in dicImgContent.Keys)
                    {
                        var pageImages = dicImgContent[pageIndex];

                        for (int ii = 0; ii < pageImages.Count; ii++)
                        {
                            var img = pageImages[ii];
                            var byts = ImageProcessHelper.ImageToByte(img);
                            string imgUrl = null;// isUplpadImg ? UploadByFile(byts, CommonString.StrDefaultImgType, false) : null;

                            OcrContent ocr = null;

                            var stop = Stopwatch.StartNew();
                            var processEntity = OcrPoolProcess.GetProcessEntityByBytes(OcrType.文本, NowOcrGroupType,
                                 IsFromLeftToRight, IsFromTopToDown, CurrentTranslateFrom,
                                CurrentTranslateTo, byts,
                                imgUrl, CommonString.StrDefaultImgType, isUplpadImg, false, ProcessBy.主界面, null);
                            var strOnePage = pageImages.Count > 1 ? "中第{0}/{1}张图片" : string.Empty;
                            for (int i = 0; i < 10; i++)
                            {
                                if (ocr == null || ocr?.result?.HasResult == false)
                                {
                                    if (i == 0)
                                    {
                                        ucLoading1.ShowText(string.Format("正在识别第{0}/{1}页{2}…", pageIndex,
                                            dicImgContent.Count, string.Format(strOnePage, ii + 1, pageImages.Count)));
                                    }

                                    ocr = GetOcrContentByBytes(processEntity);
                                    if (stop.ElapsedMilliseconds < execPerTime)
                                        Thread.Sleep((int)(execPerTime - stop.ElapsedMilliseconds));
                                }
                                else
                                {
                                    break;
                                }
                            }

                            lock (objLock)
                            {
                                var content =
                                    //string.Format("\n第{1}/{2}页\n", ocr?.processName, imgKey.Key, dicImgContent.Count) +
                                    ocr?.result?.spiltText;
                                // 处理节点：【{0}】
                                if (dicContent.ContainsKey(pageIndex))
                                {
                                    dicContent[pageIndex] += content;
                                }
                                else
                                {
                                    dicContent.Add(pageIndex, content);
                                }

                                ucLoading1.ShowText(string.Format("第{0}/{1}页{2}识别完成！", pageIndex,
                                    dicImgContent.Count, string.Format(strOnePage, ii + 1, pageImages.Count)));
                            }

                            byts = null;
                            img.Dispose();
                            img = null;
                            ocr = null;
                        }
                    }

                    Parallel.ForEach(dicImgContent, new ParallelOptions() { MaxDegreeOfParallelism = 1 }, imgKey => { });
                    ucLoading1.ShowText("识别完毕，开始合并文件……");
                    var sb = new StringBuilder();
                    for (int i = 1; i <= dicContent.Count; i++)
                    {
                        sb.AppendLine(dicContent[i]?.Replace("\n", Environment.NewLine));
                    }

                    string strFileName = CommonString.DefaultRecPath + Path.GetFileNameWithoutExtension(fileName) +
                                         "-" + NowOcrGroupType.ToString() + ".txt";
                    try
                    {
                        if (!Directory.Exists(CommonString.DefaultRecPath))
                        {
                            Directory.CreateDirectory(CommonString.DefaultRecPath);
                        }

                        if (File.Exists(strFileName))
                        {
                            File.Delete(strFileName);
                        }

                        File.WriteAllText(strFileName, sb.ToString());
                        ucLoading1.ShowText("PDF识别完成！\n" + strFileName);
                        CommonMethod.OpenFolderWithFile(strFileName);
                    }
                    catch
                    {
                        ucLoading1.ShowText("PDF识别完成！");
                        ShowContentText(sb.ToString());
                    }

                    sb.Clear();
                }
                catch (Exception oe)
                {
                    ucLoading1.ShowText("正在加载PDF组件，请稍候重试…");
                    var dllName = CommonString.LoadDllByName(oe.Message, true);
                }
                finally
                {
                    ucLoading1.CloseLoading(3);
                }
            });
        }

        /// <summary>
        ///     将PDF文档转换为图片的方法
        /// </summary>
        /// <param name="pdfInputPath">PDF文件路径</param>
        /// <param name="isSpiltImage"></param>
        private Dictionary<int, List<Bitmap>> ConvertPdf2Image(string pdfInputPath, bool isSpiltImage)
        {
            Dictionary<int, List<Bitmap>> dicImgContent = new Dictionary<int, List<Bitmap>>();
            int definition = 3;
            //const string imgPath = @"C:\Users\<USER>\Desktop\pdf\";
            using (var pdfFile = PDFFile.Open(pdfInputPath))
            {
                int startPageNum = 1, endPageNum = pdfFile.PageCount;

                for (int i = startPageNum; i <= endPageNum; i++)
                {
                    Bitmap pageImage = pdfFile.GetPageImage(i - 1, 56 * definition);
                    var lstImage = new List<Bitmap>();
                    if (isSpiltImage)
                    {
                        Bitmap leftPageImage = new Bitmap(pageImage.Width / 2, pageImage.Height);
                        Bitmap rightPageImage = new Bitmap(pageImage.Width / 2, pageImage.Height);
                        using (Graphics g = Graphics.FromImage(leftPageImage))
                        {
                            g.InterpolationMode = InterpolationMode.High;
                            //重新画图的时候Y轴减去130，高度也减去130  这样水印就看不到了
                            g.DrawImage(pageImage, new Rectangle(0, 0, leftPageImage.Width, leftPageImage.Height),
                                new Rectangle(0, 130, pageImage.Width / 2, pageImage.Height - 130), GraphicsUnit.Pixel);
                        }

                        using (Graphics g = Graphics.FromImage(rightPageImage))
                        {
                            g.InterpolationMode = InterpolationMode.High;
                            //重新画图的时候Y轴减去130，高度也减去130  这样水印就看不到了
                            g.DrawImage(pageImage, new Rectangle(0, 0, rightPageImage.Width, rightPageImage.Height),
                                new Rectangle(pageImage.Width / 2, 130, pageImage.Width / 2, pageImage.Height - 130),
                                GraphicsUnit.Pixel);
                        }

                        ////保存图片
                        //leftPageImage.Save(imgPath + i + "_1.jpg");
                        //rightPageImage.Save(imgPath + i + "_2.jpg");
                        lstImage.Add(leftPageImage);
                        lstImage.Add(rightPageImage);
                    }
                    else
                    {
                        Bitmap newPageImage = new Bitmap(pageImage.Width, pageImage.Height);
                        using (Graphics g = Graphics.FromImage(newPageImage))
                        {
                            g.InterpolationMode = InterpolationMode.High;
                            //重新画图的时候Y轴减去130，高度也减去130  这样水印就看不到了
                            g.DrawImage(pageImage, new Rectangle(0, 0, newPageImage.Width, newPageImage.Height),
                                new Rectangle(0, 130, pageImage.Width, pageImage.Height - 130), GraphicsUnit.Pixel);

                            //保存图片
                            //newPageImage.Save(imgPath + i + ".jpg");
                            g.Dispose();
                        }

                        lstImage.Add(newPageImage);
                    }

                    dicImgContent.Add(i, lstImage);
                }

                //pdfFile.Dispose();
            }

            return dicImgContent;
        }

        //private string UploadByFile(byte[] byts, string ext, bool isShowLoading = true)
        //{
        //    var imgUrl = string.Empty;
        //    try
        //    {
        //        if (isShowLoading)
        //            ucLoading1.ShowLoading(StrUploadImg);

        //        imgUrl = OcrHelper.UploadImage(byts);
        //    }
        //    catch
        //    {
        //    }

        //    return imgUrl;
        //}

        private void SaveTxtFile(string txt)
        {
            using (var saveFileDialog1 = new SaveFileDialog
            {
                Filter = "txt文档| *.txt|Word文档| *.doc;*.docx|所有文件|*.*",
                Title = "选择保存位置",
                FileName = "文本_" + ServerTime.DateTime.ToString("yyyyMMddhhmmss") + ".txt",
                FilterIndex = 0
            })
            {
                if (saveFileDialog1.ShowDialog(this) == DialogResult.OK && saveFileDialog1.FileName != "")
                {
                    CommonResult.ExportToTxt(txt, saveFileDialog1.FileName);
                }
            }
        }

        private void 识别历史ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var frmBatch = new FormRecent
            { Icon = Icon, Theme = msManager.Theme, Style = msManager.Style, StyleManager = msManager };
            frmBatch.Show(this);
        }

        private void 截图识别ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var img = CaptureImageByType(false);
            if (img?.Image != null)
            {
                var ocrType = IsOcrForSearch ? OcrType.文本 : NowOcrType;
                ProcessByImage(ocrType, ProcessBy.主界面, img?.Image, true, null, IsOcrForSearch);
            }

            IsOcrForSearch = false;
        }

        private void 取色器ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var color = Color.Transparent;
            using (var form = new RegionCaptureForm(RegionCaptureMode.ScreenColorPicker, new RegionCaptureOptions() { CustomInfoTitle = "取色器" }))
            {
                try
                {
                    form.Icon = CommonMethod.GetApplicationIcon();
                    form.ShowDialog();
                    if (form.Result != RegionResult.Close)
                    {
                        if (form.ShapeManager != null)
                        {
                            try
                            {
                                color = form.ShapeManager.GetCurrentColor();
                            }
                            catch { }
                        }
                    }
                }
                catch { }
            }
            if (!Equals(Color.Transparent, color))
            {
                var data = ClipboardService.GetColorHtmlImage(color);
                ClipboardService.SetText(data.OriginalText);
                this.ViewTextImage(data);
                CommonMethod.ShowNotificationTip(data.OriginalText + "\n\n已复制到粘贴板！", Application.ProductName + " - 取色器",
                    5 * 1000);
            }
        }

        private void 调色板ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ColorPickerForm.PickColor(Color.Black, out var newColor, false, this);
        }

        private void 标尺工具ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var scroll = new RulerForm { Icon = Icon };
            scroll.Show(this);
        }

        private void 贴图ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var result = CaptureImageByType(true, CaptureActions.截图贴图.ToString());
            this.ViewImageWithLocation(result.Image,
                new Point(result.Rectangle.X, result.Rectangle.Y));
        }

        private void 截图ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var img = CaptureImageByType(true, CaptureActions.截图编辑.ToString());
            if (img?.Image != null)
            {
                SaveImage(img?.Image, true);
            }
        }

        private void 文字贴图ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var data = new ClipboardTextEntity
            {
                Type = ClipboardDataType.Image,
                Image = ClipboardService.GetImage()
            };
            if (data.Image == null) data = ClipboardService.GetRtfHtmlImage();
            this.ViewTextImage(data);
        }

        private void InitScreenShot()
        {
            CommonEnumAction<CaptureActions>.AddEnumItemsContextMenu(cmsNotify, ProcessCaptureClickAction);
        }

        private bool HideFormTool(bool isHideMain = false)
        {
            var isShowTool = FrmTool.Visible;
            if (isShowTool)
                FrmTool.Visible = false;
            if (isHideMain)
            {
                Hide();
            }

            frmOCR?.HideWindow(true);
            return isShowTool;
        }

        private void ProcessCaptureClickAction(object sender, EventArgs e)
        {
            var clickItem = sender as ToolStripItem;
            var info = (EnumInfo)clickItem.Tag;
            var action = (CaptureActions)info.Value;
            Image image = null;
            Rectangle rectangle = Rectangle.Empty;
            var isShowTool = HideFormTool();
            switch (action)
            {
                case CaptureActions.截图:
                    image = CaptureImageByType(false, action.ToString())?.Image;
                    break;
                case CaptureActions.截图编辑:
                    image = CaptureImageByType(true, action.ToString())?.Image;
                    break;
                case CaptureActions.延时截图:
                    var delayMilSec = (int)(CommonSetting.截图延时 * 1000);
                    if (delayMilSec > 0)
                    {
                        DelayCapture(delayMilSec);
                        image = CaptureImageByType(true, action.ToString())?.Image;
                    }
                    break;
                case CaptureActions.快速贴图:
                    var fastPasteResult = CaptureImageByType(false, action.ToString());
                    image = fastPasteResult?.Image;
                    if (fastPasteResult != null)
                        this.ViewImageWithLocation(image,
                            new Point(fastPasteResult.Rectangle.X, fastPasteResult.Rectangle.Y));
                    break;
                case CaptureActions.粘贴贴图:
                    文字贴图ToolStripMenuItem_Click(null, null);
                    break;
                case CaptureActions.截图贴图:
                    var tieTuResult = CaptureImageByType(true, action.ToString());
                    image = tieTuResult?.Image;
                    if (image != null)
                        this.ViewImageWithLocation(image,
                            new Point(tieTuResult.Rectangle.X, tieTuResult.Rectangle.Y));
                    break;
                case CaptureActions.显隐贴图:
                    try
                    {
                        var isHasPasteWindow = false;
                        var froms = Application.OpenForms.OfType<FrmPasteImage>().Where(x => !x.IsDisposed);
                        var isShow = froms.Any(x => !x.Visible);
                        foreach (var form in froms)
                        {
                            isHasPasteWindow = true;
                            form.Visible = isShow;
                        }

                        if (!isHasPasteWindow)
                        {
                            CommonMethod.ShowHelpMsg("当前无贴图窗口！");
                        }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
                    break;
                case CaptureActions.活动窗口:
                    image = Screenshot.CaptureActiveWindow(ref rectangle);
                    break;
                case CaptureActions.全屏:
                    image = Screenshot.CaptureFullscreen(ref rectangle, false);
                    break;
                case CaptureActions.活动显示器:
                    image = Screenshot.CaptureActiveMonitor(ref rectangle);
                    break;
                case CaptureActions.固定区域:
                    //var item = CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.固定区域);
                    FixAreaItemClick(clickItem);
                    break;
                case CaptureActions.滚动截屏:
                    var scroll = new ScrollingCaptureForm(new ScrollingCaptureOptions())
                    { Icon = Icon, Theme = msManager.Theme, Style = msManager.Style, StyleManager = msManager };
                    scroll.Show(this);
                    break;
                case CaptureActions.打开截图文件夹:
                    CommonMethod.OpenFolder(CommonString.DefaultImagePath);
                    break;
            }

            if (isShowTool) FrmTool.Visible = true;
            if (image != null)
            {
                SaveImage(image, true);
            }
        }

        private void DelayCapture(int delayMilSec)
        {
            var size = new Size(750, 500);
            var rect = new Rectangle((Screen.PrimaryScreen.WorkingArea.Width - size.Width) / 2, (Screen.PrimaryScreen.WorkingArea.Height - size.Height) / 2, size.Width, size.Height);
            var shadowForm = InitShadowForm(null, false, true, new Size(rect.Size.Width + 4, rect.Size.Height + 4),
                new Point(rect.X - 2, rect.Y - 2));
            shadowForm.Text = "延时截图";
            shadowForm.TopMost = true;
            shadowForm?.ForceActivate();
            var entity = new FixAreaCaptureEntity()
            {
                BaseRect = rect,
                DelayMilSec = delayMilSec,
                IntervalMilSecond = 3500,
                Item = null,
                LoopTimes = 0,
                ShadowForm = shadowForm
            };
            int delayMinSec = 1000;
            var font = new Font(CommonSetting.默认文字字体.FontFamily, 100, FontStyle.Bold);
            while (!CommonString.IsExit && entity.IsRun && entity.DelayMilSec > 0)
            {
                SetShadowFormText(entity.ShadowForm, Math.Ceiling(entity.DelayMilSec * 1.0 / 1000).ToString("F0"), font);
                Thread.Sleep(delayMinSec);
                entity.DelayMilSec -= delayMinSec;
            }
            entity.ShadowForm?.Close();
            entity.ShadowForm?.Dispose();
            entity.ShadowForm = null;
        }

        private void FixAreaItemClick(ToolStripItem item)
        {
            if (item == null)
            {
                return;
            }

            var text = item.Text.Replace("停止", "");
            if (item.Text.StartsWith("停止"))
            {
                item.Text = text;
                this.GetShadowForm(text)?.Close();
            }
            else
            {
                var rectangle = CaptureImageByType(false, text)?.Rectangle;
                if (rectangle == null || rectangle.Value.IsEmpty)
                {
                    return;
                }

                item.Text = "停止" + text;

                FixAreaCaptureMethod(item, rectangle.Value);
            }
        }

        private void FixAreaCaptureMethod(ToolStripItem item, Rectangle rect)
        {
            var text = item.Text.Replace("停止", "");
            bool isOcr = text.Contains("识别") || text.Contains("翻译");

            var shadowForm = InitShadowForm(item, isOcr, true, new Size(rect.Size.Width + 4, rect.Size.Height + 4),
                new Point(rect.X - 2, rect.Y - 2));

            int delayMilSec = 0;
            int intervalMilSec = 3500;
            int loopTimes = isOcr ? 100 : 1;
            bool isShowInMainWindow = false;

            SetShadowFormText(shadowForm, "等待操作…");
            var frmArea = new FormAreaCapture()
            { Icon = Icon, Theme = msManager.Theme, Style = msManager.Style, StyleManager = msManager };
            frmArea.CaptureLocation = rect.Location;
            frmArea.CaptureSize = new Size(shadowForm.Size.Width, shadowForm.Size.Height - (shadowForm.IsShowTitle ? 35 : 0));
            frmArea.IntervalMilSecond = intervalMilSec;
            frmArea.DelayMilSecond = delayMilSec;
            frmArea.LoopTimes = loopTimes;
            frmArea.TopMost = true;
            frmArea.Text = text;
            shadowForm.TopMost = false;
            if (frmArea.ShowDialog(this) == DialogResult.OK)
            {
                if (!frmArea.CaptureLocation.IsEmpty)
                {
                    rect.Location = frmArea.CaptureLocation;
                }

                if (!frmArea.CaptureSize.IsEmpty)
                {
                    rect.Size = frmArea.CaptureSize;
                }

                delayMilSec = frmArea.DelayMilSecond;
                loopTimes = frmArea.LoopTimes;
                intervalMilSec = Math.Max(frmArea.IntervalMilSecond, 1000);
                isShowInMainWindow = frmArea.IsShowInMainWindow;

                shadowForm.Location = new Point(rect.X - 2, rect.Y - 2);
                shadowForm.Size = new Size(rect.Size.Width + 4, rect.Size.Height + 4);
            }

            shadowForm.TopMost = true;
            shadowForm?.ForceActivate();

            if (rect.Width < MinFixAreaWidth) rect.Width = MinFixAreaWidth;
            if (rect.Height < MinFixAreaWidth) rect.Height = MinFixAreaWidth;

            var execPerTime = (Program.NowUser?.PerTimeSpan ?? 15000) / (Program.NowUser?.PerTimeSpanExecCount ?? 1) + 1000;
            var entity = new FixAreaCaptureEntity()
            {
                BaseRect = rect,
                DelayMilSec = delayMilSec,
                IntervalMilSecond = Math.Max(execPerTime, intervalMilSec),
                Item = item,
                LoopTimes = loopTimes,
                ShadowForm = shadowForm,
                IsShowInMainWindow = isShowInMainWindow,
            };
            FixedFieldCapture(entity);
        }

        private void FixedFieldCapture(FixAreaCaptureEntity entity)
        {
            Task.Factory.StartNew(() =>
            {
                int delayMinSec = 1000;
                while (!CommonString.IsExit && entity.IsRun && entity.DelayMilSec > 0)
                {
                    //SetShadowFormText(entity.ShadowForm,
                    //    string.Format("{0}秒后开始", Math.Ceiling(entity.DelayMilSec * 1.0 / 1000)));
                    SetShadowFormTextTick(entity.ShadowForm, (int)Math.Ceiling(entity.DelayMilSec * 1.0 / 1000));
                    Thread.Sleep(delayMinSec);
                    entity.DelayMilSec -= delayMinSec;
                }

                SetShadowFormText(entity.ShadowForm);
                var nowLoopTime = 0;
                var isOcr = entity.IsOcr;
                isFixShowResultInMainWindow = entity.IsShowInMainWindow;
                if (isOcr)
                {
                    SetShadowFormContent(entity.ShadowForm);
                }
                while (!CommonString.IsExit && entity.IsRun && nowLoopTime < entity.LoopTimes)
                    if (!CommonString.IsOnRec)
                    {
                        if (isOcr && nowLoopTime == 0)
                        {
                            CommonString.IsOnRec = true;
                        }
                        nowLoopTime++;
                        CommonMethod.DetermineCall(FrmTool, delegate
                        {
                            entity.ShadowForm.Text = string.Format("{2} 第{0}/{1}次"
                                , nowLoopTime, entity.LoopTimes
                                , entity.Item.Text.Replace("停止", "").Replace("固定区域", ""));
                        });
                        var location = entity.ShadowForm?.Location ?? entity.BaseRect.Location;
                        var rect = new Rectangle(location, entity.ShadowForm?.Size ?? entity.BaseRect.Size);
                        if (rect.IsEmpty)
                        {
                            rect = entity.BaseRect;
                        }
                        else
                        {
                            if (entity.ShadowForm?.IsShowTitle == true)
                            {
                                rect.Y += 35;
                                rect.Height -= 35;
                            }
                        }
                        rect.Width -= 5;
                        rect.Height -= 5;
                        if (isOcr)
                        {
                            try
                            {
                                CommonString.OcrMutex.WaitOne(10 * 1000, true);
                            }
                            catch
                            {
                                try
                                {
                                    CommonString.OcrMutex.Release();
                                }
                                catch { }
                            }
                            if (nowShadowContent?.Visible == true)
                            {
                                CommonMethod.DetermineCall(FrmTool, delegate
                                {
                                    nowShadowContent.Visible = false;
                                    Application.DoEvents();
                                });
                            }
                        }
                        var image = Screenshot.CaptureRectangle(rect, rect);
                        if (isOcr && !isFixShowResultInMainWindow && nowShadowContent?.Visible == false)
                        {
                            CommonMethod.DetermineCall(FrmTool, delegate
                            {
                                nowShadowContent.Visible = true;
                                //nowShadowContent.BindImage(image);
                                Application.DoEvents();
                            });
                        }
                        //CommonMethod.DetermineCall(FrmTool, delegate
                        //{
                        //    SetShadowFormText(entity.ShadowForm, string.Format("第{0}/{1}次", nowLoopTime, entity.LoopTimes));
                        //});
                        if (isOcr)
                        {
                            ProcessByImage(entity.OcrType, ProcessBy.固定区域, image, false);
                        }
                        else
                        {
                            SaveImage(image, true);
                        }

                        if (nowLoopTime < entity.LoopTimes)
                        {
                            Thread.Sleep(entity.IntervalMilSecond);
                        }
                    }
                    else
                    {
                        Thread.Sleep(1000);
                    }

                entity.ShadowForm?.Close();
                entity.ShadowForm?.Dispose();
                entity.ShadowForm = null;
            });
        }

        private ShadowForm InitShadowForm(ToolStripItem parentItem, bool isOcr, bool isShowTitle, Size size, Point location)
        {
            var shadowForm = new ShadowForm
            {
                Size = size,
                IsShowTitle = isShowTitle,
                Location = location,
                StartPosition = FormStartPosition.Manual,
                BackColor = Color.FromArgb(255, 255, 254),
                TransparencyKey = Color.FromArgb(255, 255, 254),
                TopMost = true,
                Icon = Icon,
                Text = parentItem?.Text.Replace("停止", ""),
                ShowInTaskbar = false,
                IsSharkWindow = false,
                ShadowWidth = 10,
                MinimizeBox = false,
                MaximizeBox = false
            };
            if (parentItem != null)
            {
                var item = new ToolStripMenuItem
                {
                    Text = parentItem.Text,
                    Image = parentItem.Image,
                    Tag = parentItem.Tag
                };
                item.Click += (obj, e) => { parentItem.PerformClick(); };

                var contentMenu = new ContextMenuStrip();
                contentMenu.Items.Add(item);
                shadowForm.ContextMenuStrip = contentMenu;
            }

            var label = new Label()
            {
                BackColor = Color.Transparent,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = CommonSetting.默认文字字体,
                ForeColor = CommonSetting.默认文字颜色
            };
            label.TextChanged += (object obj, EventArgs e) =>
            {
                label.Visible = !string.IsNullOrEmpty(label.Text);
                //try
                //{
                //    if (shadowForm.IsShowTitle != label.Visible)
                //    {
                //        shadowForm.IsShowTitle = label.Visible;
                //        shadowForm.Invalidate();
                //    }
                //}
                //catch
                //{
                //}
            };
            shadowForm.Controls.Add(label);
            var txtContent = new UcContent()
            {
                Dock = DockStyle.Fill,
                Padding = Padding.Empty,
                Margin = Padding.Empty,
                Visible = false
            };
            shadowForm.Controls.Add(txtContent);

            shadowForm.DoubleClick += (sender, e) =>
            {
                if (parentItem != null && parentItem.Text.StartsWith("停止"))
                    parentItem.PerformClick();
            };
            shadowForm.FormClosed += (sender, e) =>
            {
                if (parentItem != null && parentItem.Text.StartsWith("停止"))
                    parentItem.PerformClick();
            };
            shadowForm.Show();
            return shadowForm;
        }

        private UcContent nowShadowContent;
        private bool isFixShowResultInMainWindow;

        private void SetShadowFormContent(ShadowForm shadowForm)
        {
            try
            {
                nowShadowContent = null;
                if (shadowForm?.Visible == true && shadowForm.Controls.Count > 0)
                    foreach (var control in shadowForm.Controls)
                    {
                        if (control is UcContent ucContent)
                        {
                            nowShadowContent = ucContent;
                            ucContent.SpiltModel = NowSpiltModel;
                            ucContent.IsShowOldContent = IsShowOldContent;
                            ucContent.NowDisplayMode = DisplayModel.图文模式;
                            ucContent.IsShowToolBox = false;
                            break;
                        }
                    }
            }
            catch
            {
            }
        }

        private void SetShadowFormText(ShadowForm shadowForm, string text = "", Font font = null)
        {
            try
            {
                if (shadowForm?.Visible == false || shadowForm.Controls.Count <= 0)
                    return;
                foreach (var control in shadowForm.Controls)
                {
                    if (control is Label lbl)
                    {
                        lbl.Text = text;
                        lbl.Font = font ?? CommonSetting.默认文字字体;
                        Application.DoEvents();
                    }
                }
            }
            catch
            {
            }
        }

        private void SetShadowFormTextTick(ShadowForm shadowForm, int second)
        {
            try
            {
                if (shadowForm?.Visible == false || shadowForm.Controls.Count <= 0)
                    return;
                var lbl = shadowForm.Controls[0] as Label;
                lbl.TextAlign = ContentAlignment.MiddleCenter;
                lbl.Text = second.ToString();
                Application.DoEvents();
                var thread = new Thread(() =>
                {
                    int loopTime = 5;
                    int loopPerSize = 20;
                    var isDouble = second % 2 != 0;
                    var baseFontSize = isDouble ? loopTime * loopPerSize : 20;
                    for (int i = 4; i > 0; i--)
                    {
                        try
                        {
                            lbl.Font = new Font(CommonSetting.默认文字字体.FontFamily, baseFontSize + (isDouble ? -1 : 1) * i * loopPerSize, FontStyle.Bold);
                        }
                        catch
                        {
                            break;
                        }
                        Thread.Sleep(100);
                    }
                });
                thread.Start();
            }
            catch
            {
            }
        }

        private CaptureImageContent CaptureImageByType(bool isEdit, string operate = null)
        {
            CaptureImageContent img = null;
            cmsNotify.Hide();
            if (CommonString.IsOnRec)
            {
                CommonMethod.ShowHelpMsg(StrProcessLater);
                return img;
            }

            if (!StaticValue.IsCatchScreen)
            {
                var isShowTool = HideFormTool(true);
                img = CatchImage(isEdit, operate);
                if (isShowTool) FrmTool.Visible = true;
            }

            return img;
        }

        private class CaptureImageContent
        {
            public Bitmap Image { get; set; }

            public Rectangle Rectangle { get; set; }

            public Color Color { get; set; }
        }

        private CaptureImageContent CatchImage(bool isEdit, string operate)
        {
            CaptureImageContent result = null;

            using (RegionCaptureForm form = new RegionCaptureForm(RegionCaptureMode.Annotation
                , new RegionCaptureOptions() { QuickCrop = true, CustomInfoTitle = string.IsNullOrEmpty(operate) ? NowOcrType + "识别" : operate }
                )
            {
                Icon = Icon
            })
            {
                try
                {
                    form.ShowDialog();
                    if (form.Result != RegionResult.Close)
                    {
                        result = new CaptureImageContent()
                        {
                            Image = form.GetResultImage(),
                            Color = form.ShapeManager?.GetCurrentColor() ?? Color.Empty,
                            Rectangle = form.RectangleResult
                        };
                    }
                }
                catch { }
            }
            return result;
        }

        private void OcrProcessThread()
        {
            new Thread(p =>
                {
                    try
                    {
                        foreach (var processEntity in OcrPoolProcess.OcrProcessPool.GetConsumingEnumerable())
                            try
                            {
                                if (CommonString.IsOnLine)
                                {
                                    CommonString.IsOnRec = true;
                                    OcrProcessMethod(processEntity);
                                }
                                else
                                    CommonMethod.ShowHelpMsg("当前本地网络不可用，请稍候重试！");
                                if (Equals(processEntity.ProcessBy, ProcessBy.主界面) ||
                                    (Equals(processEntity.ProcessBy, ProcessBy.固定区域) && isFixShowResultInMainWindow)) ShowWindow();
                            }
                            catch (Exception oe)
                            {
                                Console.WriteLine(oe.Message);
                            }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
                })
            { IsBackground = true, Priority = ThreadPriority.Highest }.Start();
        }

        private void OcrResultProcessThread()
        {
            new Thread(p =>
                {
                    try
                    {
                        bool isFirst = true;
                        var lastId = string.Empty;
                        foreach (var ocrResult in OcrResultPool.GetConsumingEnumerable())
                            try
                            {
                                CommonMethod.DetermineCall(this, delegate
                                {
                                    Console.WriteLine("识别完成：" + ocrResult.Identity + ",结果：" + CommonString.JavaScriptSerializer.Serialize(ocrResult));

                                    if (Equals(ocrResult.processName, CommonString.StrReminder) && ocrResult.result?.autoText.Contains("升级到最新版") == true)
                                    {
                                        CommonUpdate.isAlertUpdate = true;
                                        CommonUpdate.UpdateMethod(true, CommonString.DtNowDate, CommonString.UpdateFileUrl);
                                    }
                                    if (Equals(ocrResult.ProcessBy, ProcessBy.批量识别))
                                    {
                                        FrmBatchOCR.OcrResultPool.Add(ocrResult);
                                    }
                                    else if (Equals(ocrResult.ProcessBy, ProcessBy.主界面)
                                        || Equals(ocrResult.ProcessBy, ProcessBy.固定区域)
                                        || Equals(ocrResult.ProcessBy, ProcessBy.划词翻译))
                                    {
                                        if (Equals(ocrResult.ProcessBy, ProcessBy.固定区域))
                                        {
                                            try
                                            {
                                                if (ocrResult.result.HasResult)
                                                {
                                                    CommonString.OcrMutex.Release();
                                                }
                                            }
                                            catch { }
                                        }
                                        if (Equals(ocrResult.ProcessBy, ProcessBy.划词翻译))
                                        {
                                            BindToolSearch(ocrResult);
                                        }
                                        else if (Equals(ocrResult.ProcessBy, ProcessBy.固定区域) && !isFixShowResultInMainWindow)
                                        {
                                            BindFixCapture(ocrResult);
                                        }
                                        else
                                        {
                                            CommonMsg.Hide(this);
                                            NativeMethods.LockWindowUpdate(tbMain.Handle);
                                            BindResult(ocrResult);
                                            NativeMethods.LockWindowUpdate(IntPtr.Zero);
                                        }
                                        if (CommonSetting.自动复制结果到粘贴板 && ocrResult.result.HasResult && ocrResult.result.resultType == ResutypeEnum.文本)
                                        {
                                            isFirst = string.IsNullOrEmpty(lastId) || !Equals(lastId, ocrResult.id);
                                            if (Enum.TryParse(CommonSetting.复制模式, out CopyResultEnum model))
                                            {
                                                var txtThis = ocrResult.result.GetTextResult(CommonSetting.首行缩进, false
                                                    , CommonSetting.翻译时复制原文, SpiltMode.自动分段
                                                    , Equals(ocrResult.ocrType, OcrType.翻译), ocrResult.processName);
                                                if (!string.IsNullOrEmpty(txtThis))
                                                {
                                                    if (!isFirst)
                                                    {
                                                        var txtOld = ClipboardService.GetText();
                                                        if (!string.IsNullOrEmpty(txtOld))
                                                        {
                                                            if (Equals(model, CopyResultEnum.最快))
                                                                txtThis = string.Empty;
                                                            else if (Equals(model, CopyResultEnum.所有))
                                                                txtThis = string.Format("{0}\n\n{1}", txtOld, txtThis)
                                                                    .TrimStart();
                                                        }
                                                    }

                                                    if (!string.IsNullOrEmpty(txtThis))
                                                    {
                                                        ClipboardService.SetText(txtThis);
                                                        lastId = ocrResult.id;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                });
                            }
                            catch (Exception oe)
                            {
                                Console.WriteLine(oe.Message);
                            }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
                })
            { IsBackground = true, Priority = ThreadPriority.Highest }.Start();
        }

        private void tsmSearch_Click(object sender, EventArgs e)
        {
            var txt = GetCurrentTxt();
            if (!string.IsNullOrEmpty(txt)) DoSearch(txt);
        }

        public static void DoSearch(string text)
        {
            var url = string.Format(NowSearchEngine.Url, HttpUtility.UrlEncode(text));
            Process.Start(url);
        }

        private void OcrProcessMethod(OcrProcessEntity processEntity)
        {
            try
            {
                //Console.WriteLine(string.Format("开始处理时间：{0}",DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss fff")));

                var isOnMainFormProcess = Equals(processEntity.ProcessBy, ProcessBy.主界面)
                                          || (Equals(processEntity.ProcessBy, ProcessBy.固定区域) && isFixShowResultInMainWindow);
                var isOnHuaCi = Equals(processEntity.ProcessBy, ProcessBy.划词翻译);

                var isFixCaptureShowPic = Equals(processEntity.ProcessBy, ProcessBy.固定区域) && !isFixShowResultInMainWindow;

                //if (processEntity.IsNeedUpload
                //) //&& NowOcrType != OCRType.表格 && NowOcrType != OCRType.公式 && NowOcrType != OCRType.翻译
                //{
                //    processEntity.ImgUrl = UploadByFile(processEntity.Byts, processEntity.FileExt, isOnMainFormProcess);
                //    if (isOnMainFormProcess)
                //        imageBox.SetPicImageUrl(processEntity.IsImgUrl ? processEntity.ImgUrl : null);
                //}

                if (processEntity.IsShowLoading)
                {
                    if (isOnHuaCi)
                        frmOCR?.ShowLoading(StrProcessOcr, processEntity.IsShowLoading);
                    else if (isOnMainFormProcess && !Equals(processEntity.ProcessBy, ProcessBy.固定区域))
                        ucLoading1.ShowLoading(StrProcessOcr, processEntity.IsShowLoading);
                }
                if (isOnMainFormProcess) CloseAllTabs();

                var ocrTask = Task.Factory.StartNew(() =>
                {
                    //Console.WriteLine(string.Format("开始OCR时间：{0}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss fff")));
                    var ocr = GetOcrContentByBytes(processEntity);
                    return ocr;
                })
                    .ContinueWith(task =>
                    {
                        var ocr = task.Result;
                        if (string.IsNullOrEmpty(ocr?.id))
                        {
                            if (isOnHuaCi)
                                frmOCR.Tag = null;
                            else if (isOnMainFormProcess)
                                tbMain.Tag = null;
                            return;
                        }

                        if (isOnHuaCi)
                            frmOCR.Tag = ocr.id;
                        else if (isOnMainFormProcess)
                            tbMain.Tag = ocr.id;
                        var hasResult = ocr?.result?.HasResult == true;
                        if (hasResult)
                        {
                            if (processEntity.IsSearch && !string.IsNullOrEmpty(ocr.result.GetAutoText()))
                                DoSearch(ocr.result.GetAutoText());
                            else if (processEntity.OcrType == OcrType.翻译 && ocr.result.IsTransResult &&
                                     processEntity.FileExt.Equals(CommonString.StrDefaultTxtType))
                            {
                                try
                                {
                                    ocr.result.spiltText = Encoding.UTF8.GetString(processEntity.Byts);
                                }
                                catch (Exception oe)
                                {
                                    Console.WriteLine("处理翻译原文出错！" + oe.Message);
                                }
                            }
                            ocr.Identity = processEntity.Identity;
                            ocr.ProcessBy = processEntity.ProcessBy;
                            OcrResultPool.Add(ocr);
                        }

                        if (!isFixCaptureShowPic)
                        {
                            LoadOtherOcrResult(ocr.id, processEntity.ProcessBy, processEntity.Identity, hasResult);
                        }
                    })
                    .ContinueWith(task =>
                    {
                        if (isOnHuaCi)
                        {
                            frmOCR?.CloseLoading();
                        }
                        else if (isOnMainFormProcess)
                        {
                            ucLoading1.CloseLoading();
                            if (ActiveForm != this) FlashWindowHelper.Flash(3, 600, Handle);
                        }

                        CommonString.IsOnRec = false;
                        processEntity.Byts = null;
                        processEntity = null;
                    });
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        private OcrContent GetEmptyContent(string msg)
        {
            return new OcrContent
            {
                id = Guid.NewGuid().ToString(),
                result = new ResultEntity
                {
                    autoText = msg,
                    spiltText = msg
                },
                processName = CommonString.StrReminder
            };
        }

        private OcrContent GetOcrContentByBytes(OcrProcessEntity processEntity)
        {
            processEntity.FileExt = string.IsNullOrEmpty(processEntity.FileExt)
                ? CommonString.StrDefaultImgType
                : processEntity.FileExt;
            if (processEntity.IsFile && Program.NowUser == null)
            {
                return GetEmptyContent("未登录用户不支持文件识别！");
            }

            // 处理OcrType
            if (CheckIsForbidOperate(processEntity.OcrType)
                || !CommonString.LstCanProcessFilesExt.Contains(processEntity.FileExt)
                || processEntity.IsFile &&
                (Program.NowUser?.IsSupportDocFile == false &&
                 CommonString.LstCanProcessDocFilesExt.Contains(processEntity.FileExt)
                 || Program.NowUser?.IsSupportImageFile == false &&
                 CommonString.LstCanProcessImageFilesExt.Contains(processEntity.FileExt))
            )
            {
                return GetEmptyContent("当前用户不支持此类型文件识别！");
            }

            try
            {
                OcrHelper.QuantizeOcrProcessEntity(processEntity);
            }
            catch { }

            if (!Equals(processEntity.GroupType, 0) && Program.NowUser?.IsSupportPassage == false)
            {
                processEntity.GroupType = 0;
            }

            var lstOcrEntity = new List<OcrProcessEntity>();
            if (!Equals(CommonSetting.识别策略, OcrModel.仅网络识别.ToString()) && CommonSetting.启用本地识别 && Program.NowUser?.IsSupportLocalOcr == true)
            {
                processLocalOcrEntity(processEntity, lstOcrEntity);

                if (lstOcrEntity.Count <= 0)
                {
                    if (!CommonSetting.识别策略.Contains("网络"))
                    {
                        CommonMethod.ShowHelpMsg("无可用的本地识别引擎，识别策略自动切换为 本地加网络 模式！");
                        CommonSetting.识别策略 = OcrModel.本地加网络.ToString();
                    }
                    else
                    {
                        CommonSetting.启用本地识别 = false;
                        CommonMethod.ShowHelpMsg("无可用的本地识别引擎，本地识别已自动停用，请在设置->识别->本地识别 中配置！");
                    }
                }
            }
            if (!Equals(CommonSetting.识别策略, OcrModel.仅本地识别.ToString()))
            {
                lstOcrEntity.Add(processEntity);
            }
            OcrContent ocr;
            if (lstOcrEntity.Count <= 1)
            {
                ocr = OcrHelper.GetResult(processEntity);
            }
            else
            {
                var isFirst = true;
                var objLock = string.Empty;

                var lstTask = lstOcrEntity.AsParallel().Select(p => Task.Factory.StartNew(() =>
                {
                    var result = p.IsLocal ? OcrHelper.GetLocalOcrResult(p) : OcrHelper.GetResult(p);
                    lock (objLock)
                    {
                        if (result?.result != null && result.result.HasResult)
                        {
                            if (!isFirst)
                            {
                                OcrResultPool.Add(result);
                            }
                        }
                        else
                        {
                            result = null;
                        }
                        if (isFirst)
                        {
                            isFirst = false;
                        }
                    }
                    return result;
                })).ToArray();
                var index = Task.WaitAny(lstTask);
                if (index >= 0)
                {
                    ocr = lstTask[index].Result;
                }
                else
                {
                    ocr = null;
                }
            }

            return ocr;
        }

        private void processLocalOcrEntity(OcrProcessEntity processEntity, List<OcrProcessEntity> lstOcrEntity)
        {
            var fileIdentity = Guid.NewGuid().ToString();
            for (int i = 0; i < 5; i++)
            {
                var ocrType = CommonSetting.GetValue("识别", "本地识别" + (i + 1), 0);
                if (ocrType > 0)
                {
                    OcrProcessEntity localEntity = ObjectCopy<OcrProcessEntity, OcrProcessEntity>.Trans(processEntity);
                    localEntity.IsLocal = true;
                    localEntity.LocalOcrType = ocrType;
                    localEntity.Identity = fileIdentity;
                    lstOcrEntity.Add(localEntity);
                }
            }
        }

        private void ocrSpiltModelItem_Click(object sender, EventArgs e)
        {
            var clickedItem = sender as ToolStripMenuItem;
            if (clickedItem == null) return;
            foreach (ToolStripMenuItem item in tmsSpiltMode.DropDownItems) item.Checked = Equals(item, clickedItem);
            var selectedGroupType = (SpiltMode)BoxUtil.GetInt32FromObject(clickedItem.Tag?.ToString());
            if (!selectedGroupType.Equals(NowSpiltModel))
            {
                BindSpiltModel(selectedGroupType);
                CommonSetting.SetValue("分段模式", NowSpiltModel.ToString());
            }

            //tsmDDL_DropDownItemClicked("OCR分组", new ToolStripItemClickedEventArgs(clickedItem));
            //(sender as ToolStripMenuItem)?.OwnerItem?.PerformClick();
        }

        private void ocrGroupTypeItem_Click(object sender, EventArgs e)
        {
            var clickedItem = sender as ToolStripMenuItem;
            if (clickedItem == null) return;
            foreach (ToolStripMenuItem item in ocrGroupTypeToolStripMenuItem.DropDownItems)
                item.Checked = Equals(item, clickedItem);
            var newType = BoxUtil.GetInt32FromObject(clickedItem.Tag?.ToString(), 0);
            if (!newType.Equals(NowOcrGroupType) && OcrHelper.LstServerOcrGroup.Any(p => Equals(p.Code, newType)))
            {
                NowOcrGroupType = newType;
                CommonSetting.SetValue("识别引擎", clickedItem.Text);
            }

            //tsmDDL_DropDownItemClicked("OCR分组", new ToolStripItemClickedEventArgs(clickedItem));
            //(sender as ToolStripMenuItem)?.OwnerItem?.PerformClick();
        }

        private void ocrTypeItem_Click(object sender, EventArgs e)
        {
            tsmDDL_DropDownItemClicked(tsmContentType,
                new ToolStripItemClickedEventArgs(sender as ToolStripMenuItem));
            (sender as ToolStripMenuItem)?.OwnerItem?.PerformClick();
        }

        #region 图片预览保存

        private void SetPicImage(Image img, string imgUrl = null, bool isSetParent = true)
        {
            try
            {
                if (isSetParent)
                {
                    imageBox.BindImage(img, imgUrl);
                }
                else
                {
                    imageBox.BindImageOnly(img, imgUrl, CommonSetting.图片自动缩放);
                }
                if (isSetParent)
                {
                    if (img == null)
                        tbImageBox.Parent = null;
                    else
                        tbImageBox.Parent = tbMain;
                }
            }
            catch { }
        }

        #endregion

        private void btnVoice_ButtonClick(object sender, EventArgs e)
        {
            var txt = GetCurrentTxt().Trim();
            if (!string.IsNullOrEmpty(txt))
                try
                {
                    var url = OcrHelper.GetVoiceResultUrl(txt, CurrentSpeaker.GetHashCode().ToString(), "1");
                    wbVoice.NavigateWithAutoAgent(url);
                    //VoiceStateChange(true);
                    //wbVoice.DocumentText = "<bgsound src=\"" + url + "\" loop=\"1\"/>";
                    //wbVoice.DocumentText = "<audio src=\"" + url +
                    //"\" controls=\"true\" autoplay=\"true\" onended=\"window.external.VoiceStoped()\" />";
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
        }

        private string GetCurrentTxt(bool isAll = true)
        {
            foreach (Control item in tbMain.SelectedTab.Controls)
                if (item is UcContent)
                {
                    var tab = item as UcContent;
                    return tab.GetContentText(isAll);
                }

            return null;
        }

        private void SpeakerItem_Click(object sender, EventArgs e)
        {
            var clickedItem = sender as ToolStripMenuItem;
            foreach (ToolStripMenuItem item in btnVoice.DropDownItems) item.Checked = Equals(item, clickedItem);
            CurrentSpeaker = (Speaker)BoxUtil.GetInt32FromObject(clickedItem.Tag?.ToString(), 0);
            CommonSetting.SetValue("朗读配置", CurrentSpeaker.ToString());
        }

        private void tsmPicViewModel_Click(object sender, EventArgs e)
        {
            NowDisplayMode = NowDisplayMode == DisplayModel.文字模式 ? DisplayModel.图文模式 : DisplayModel.文字模式;
            UpdatePicViewModel(NowDisplayMode);
            CommonSetting.SetValue("图文模式", NowDisplayMode == DisplayModel.图文模式);
        }

        private void UpdatePicViewModel(DisplayModel model)
        {
            tsmPicViewModel.Image = ProcessStyleImage(Resources.ResourceManager.GetObject(model.ToString()) as Bitmap);
            tsmPicViewModel.Text = string.Format("【{0}】", model);
            tmsSpiltMode.Visible = NowDisplayMode == DisplayModel.文字模式 && NowOcrType != OcrType.翻译 && NowOcrType != OcrType.表格 && NowOcrType != OcrType.公式;
            SetUcContentDisplayMode(model);
        }

        private void ShowContentText(string txtContent, bool isAppend = false)
        {
            content.BindContentByStr(txtContent, isAppend);
        }

        private void CheckNetWork()
        {
            tbMain.SelectedIndex = 0;

            ShowContentText("【网络检测】");
            ShowContentText("1、本地连接状态检测…", true);
            SetNowNetWork(CommonString.IsOnLine, true);
            var error = !CommonString.IsOnLine;
            ShowContentText("本地网络连接" + (error ? "异常" : "正常"), true);
            ShowContentText("", true);

            ShowContentText("2、开始DNS检测…", true);
            var ip = DnsHelper.ToIpAddress(CommonString.StrServerHost);
            error = string.IsNullOrEmpty(ip) || !DnsHelper.IsIPv4(ip);
            ShowContentText("本地DNS解析" + (error ? "异常" : "正常"), true);
            ip = DnsHelper.GetDnsFromNsLookUp(CommonString.StrServerHost, "***************");
            error = string.IsNullOrEmpty(ip) || !DnsHelper.IsIPv4(ip);
            ShowContentText("***************解析" + (error ? "异常" : "正常"), true);
            ip = DnsHelper.GetDnsFromNsLookUp(CommonString.StrServerHost, "************");
            error = string.IsNullOrEmpty(ip) || !DnsHelper.IsIPv4(ip);
            ShowContentText("************解析" + (error ? "异常" : "正常"), true);
            ShowContentText("", true);

            ShowContentText("3、开始上网检测…", true);
            var html = WebClientExt.GetHtml("http://www.baidu.com");
            error = string.IsNullOrEmpty(html);
            ShowContentText("访问百度服务器" + (error ? "异常" : "正常"), true);
            var lstType = OcrHelper.GetCanRegUserTypes();
            error = !(lstType != null && lstType.Count > 0);
            ShowContentText("访问助手服务器" + (error ? "异常" : "正常"), true);
            ShowContentText("", true);

            ShowContentText("网络检测完毕！", true);
        }

        private void pnlNetWork_Click(object sender, EventArgs e)
        {
            Task.Factory.StartNew(() =>
            {
                ShowWindow();
                CheckNetWork();
            });
        }

        #region 检查更新相关

        private void CheckLoginStatus()
        {
            try
            {
                if (OcrHelper.IsLogouted())
                {
                    Program.NowUser = null;
                    BindUserInfo();
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        #endregion

        #region MouseHook

        private void mouseHook_MouseSelected(object sender, MouseEventArgs e)
        {
            mouseHook_DoubleClick(sender, null);
        }

        private FormOcr frmOCR;

        private void HookManager_MouseClick(object sender, MouseEventArgs e)
        {
            //if (frmSearch?.IsLeave == false)
            //{
            //    return;
            //}
            if (!CommonString.IsOnOcrButton && !CommonString.IsOnOcrSearch) frmOCR?.HideWindow(true);
        }

        private readonly List<string> lstAllowCopyControls = new List<string>
            {"Pane", "Document", "Edit", "ListItem", "Text", "Window"};

        private readonly List<string> lstNotAllowCopyClasses = new List<string> { "IHWindowClass" };

        private object GetFocusElementText(out ClipboardContentType contentType)
        {
            object result = null;

            contentType = ClipboardContentType.文本;
            var isFile = false;
            var isImg = false;
            var mouse = Cursor.Position; // use Windows forms mouse code instead of WPF
            var element = AutomationElement.FromPoint(new System.Windows.Point(mouse.X, mouse.Y));

            //var element1 = AutomationElement.FocusedElement;
            if (element != null)
            {
                foreach (AutomationElement item in element.FindAll(TreeScope.Subtree, Condition.TrueCondition))
                    if (!item.Current.IsOffscreen)
                        Console.WriteLine(item.Current.Name);
                foreach (AutomationElement item in element.FindAll(TreeScope.Subtree,
                    new PropertyCondition(AutomationElement.IsOffscreenProperty, false)))
                    if (!item.Current.IsOffscreen)
                        Console.WriteLine(item.Current.Name);
                var controlName = element.Current.ControlType?.ProgrammaticName;
                var className = element.Current.ClassName;
                Console.WriteLine("Type:" + className
                                          + ",isContent:" + element.Current.IsContentElement
                                          + ",isControl:" + element.Current.IsControlElement
                                          + ",canFocus:" + element.Current.IsKeyboardFocusable
                                          + ",isFocus:" + element.Current.HasKeyboardFocus
                                          + ",isVisiable:" + !element.Current.IsOffscreen
                                          + ",isPassword:" + element.Current.IsPassword
                                          + ";Desc:" + controlName);
                //隐藏元素或者密码类，不捕获
                if (element.Current.IsPassword) return result;
                result = element.GetText(out var isIn);
                if (!isIn)
                {
                    if (!string.IsNullOrEmpty(className) && lstNotAllowCopyClasses.Any(p => className.Contains(p)))
                        return result;
                    if (!string.IsNullOrEmpty(controlName) && lstAllowCopyControls.Any(p => controlName.Contains(p)))
                        CommonMethod.DetermineCall(this, delegate
                        {
                            var oldText = ClipboardService.GetText()?.Trim();
                            var oldDataBackup = ClipboardService.Backup();
                            ////清空剪切板，避免脏读
                            //Clipboard.Clear();

                            //开始复制
                            SendKeys.SendWait("^c");
                            SendKeys.Flush();
                            //System.Threading.Thread.Sleep(500);
                            result = ClipboardService.GetText()?.Trim();
                            if (result == null)
                            {
                                //result = ClipboardService.GetImage();
                                //if (result != null)
                                //{
                                //    isImg = true;
                                //}
                                //else
                                //{
                                //    result = ClipboardService.GetOneFile();
                                //    if (result != null && !string.IsNullOrEmpty(result.ToString()))
                                //    {
                                //        isFile = true;
                                //    }
                                //}
                            }
                            else
                            {
                                if (Equals(result, oldText)) result = null;
                            }

                            ClipboardService.Restore(oldDataBackup);
                        });
                }
            }

            if (isFile)
                contentType = ClipboardContentType.文件;
            else if (isImg) contentType = ClipboardContentType.图片;
            return result;
        }

        private void mouseHook_DoubleClick(object sender, EventArgs e)
        {
            if (CommonString.IsOnOcrButton || CommonString.IsOnOcrSearch) return;
            Task.Factory.StartNew(() =>
            {
                try
                {
                    var result = GetFocusElementText(out var contentType);

                    var location = new Point(Cursor.Position.X + 10, Cursor.Position.Y - 15 - 32);
                    ShowMiniSearch(result?.ToString(), contentType, location);
                }
                catch
                {
                }
            });
        }

        private void ShowMiniSearch(string result, ClipboardContentType contentType, Point location)
        {
            try
            {
                if (string.IsNullOrEmpty(result)) return;
                //frmSearch?.Hide();
                CommonMethod.DetermineCall(this, delegate
                {
                    if (frmOCR == null) frmOCR = new FormOcr { Icon = Icon };
                    frmOCR.Content = result;
                    frmOCR.ContentType = contentType;

                    if (location != Point.Empty)
                    {
                        frmOCR.Location = location;
                        frmOCR.ShowTool(true);
                        if (!Equals(frmOCR.Width, 32)) frmOCR.Size = new Size(32, 32);
                        if (!Equals(location, frmOCR.Location)) frmOCR.Location = location;
                    }
                    else
                    {
                        frmOCR.ShowSearch();
                    }
                    Application.DoEvents();
                });
                //Clipboard.Clear();
            }
            catch
            {
            }
        }

        #endregion

        #region 权限相关

        private bool CheckIsForbidOperate(OcrType ocrType)
        {
            var isForbid = false;
            if (Equals(ocrType, OcrType.竖排) && !(Program.NowUser?.IsSupportVertical == true)
                || Equals(ocrType, OcrType.表格) && !(Program.NowUser?.IsSupportTable == true)
                || Equals(ocrType, OcrType.公式) && !(Program.NowUser?.IsSupportMath == true)
                || Equals(ocrType, OcrType.翻译) && !(Program.NowUser?.IsSupportTranslate == true)
            )
                isForbid = true;

            return isForbid;
        }

        private void ProcessForbidControls()
        {
            try
            {
                //PDF识别StripMenuItem.Visible = (Program.NowUser?.IsSupportDocFile == true);
                批量识别ToolStripMenuItem.Visible = Program.NowUser?.IsSupportBatch == true;
                文件识别ToolStripMenuItem.Visible = Program.NowUser?.IsSupportImageFile == true;

                ocrGroupTypeToolStripMenuItem.Visible = Program.NowUser?.IsSupportPassage == true;
                tsmOcrGroupSpilt.Visible = Program.NowUser?.IsSupportPassage == true;

                InitOcrTypes();
                tbMain.Focus();

                CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.固定区域).Visible =
                    Program.NowUser?.IsSupportImageFile == true;
                CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, "固定区域识别").Visible =
                    Program.NowUser?.IsSupportImageFile == true;
                CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, "固定区域翻译").Visible =
                    Program.NowUser?.IsSupportTranslate == true;
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        private void InitOcrTypes()
        {
            tsmContentType.DropDownItems.Clear();
            截图识别toolStripMenuItem.DropDownItems.Clear();
            粘贴识别ToolStripMenuItem.DropDownItems.Clear();
            文件识别ToolStripMenuItem.DropDownItems.Clear();
            foreach (OcrType ocrType in Enum.GetValues(typeof(OcrType)))
                if (!CheckIsForbidOperate(ocrType))
                {
                    var item = new ToolStripMenuItem
                    {
                        Text = ocrType.ToString(),
                        Tag = ocrType.GetHashCode(),
                        Font = tsmContentType.Font,
                        Image = Resources.ResourceManager.GetObject(ocrType.ToString()) as Bitmap
                    };
                    tsmContentType.DropDownItems.Add(item);

                    var 截图item = new ToolStripMenuItem
                    {
                        Text = ocrType.ToString(),
                        Tag = ocrType.GetHashCode(),
                        Font = tsmContentType.Font,
                        Image = Resources.ResourceManager.GetObject(ocrType.ToString()) as Bitmap
                    };
                    截图item.Click += ocrTypeItem_Click;
                    截图识别toolStripMenuItem.DropDownItems.Add(截图item);

                    var 粘贴item = new ToolStripMenuItem
                    {
                        Text = ocrType.ToString(),
                        Tag = ocrType.GetHashCode(),
                        Font = tsmContentType.Font,
                        Image = Resources.ResourceManager.GetObject(ocrType.ToString()) as Bitmap
                    };
                    粘贴item.Click += ocrTypeItem_Click;
                    粘贴识别ToolStripMenuItem.DropDownItems.Add(粘贴item);

                    var 图片识别item = new ToolStripMenuItem
                    {
                        Text = ocrType.ToString(),
                        Tag = ocrType.GetHashCode(),
                        Font = tsmContentType.Font,
                        Image = Resources.ResourceManager.GetObject(ocrType.ToString()) as Bitmap
                    };
                    图片识别item.Click += ocrTypeItem_Click;
                    文件识别ToolStripMenuItem.DropDownItems.Add(图片识别item);
                }

            var 搜索item = new ToolStripMenuItem
            {
                Text = "搜索",
                Tag = "搜索",
                Font = tsmContentType.Font,
                Image = Resources.ResourceManager.GetObject("搜索") as Bitmap
            };
            搜索item.Click += ocrTypeItem_Click;
            截图识别toolStripMenuItem.DropDownItems.Add(搜索item);
        }

        private void InitOcrGroupItems()
        {
            ocrGroupTypeToolStripMenuItem.DropDownItems.Clear();
            foreach (var keyValuePair in OcrHelper.LstServerOcrGroup)
            {
                var item = new ToolStripMenuItem
                { Text = keyValuePair.Name, Tag = keyValuePair.Code, Font = tsmContentType.Font };
                item.Click += ocrGroupTypeItem_Click;
                if (keyValuePair.Code.Equals(NowOcrGroupType)) item.Checked = true;
                ocrGroupTypeToolStripMenuItem.DropDownItems.Add(item);
            }
        }

        private void InitSearchMenu()
        {
            if (CommonEnumAction<string>.FindMeunItem(cmsNotify, "固定区域识别") == null)
            {
                var item = new ToolStripMenuItem
                {
                    Text = "固定区域识别",
                    Font = tsmContentType.Font,
                    Image = Resources.文本
                };
                item.Click += (obj, e) => { FixAreaItemClick(item); };
                识别合集toolStripMenuItem.DropDownItems.Add(item);
            }
            if (CommonEnumAction<string>.FindMeunItem(cmsNotify, "固定区域翻译") == null)
            {
                var item = new ToolStripMenuItem
                {
                    Text = "固定区域翻译",
                    Font = tsmContentType.Font,
                    Image = Resources.翻译
                };
                item.Click += (obj, e) => { FixAreaItemClick(item); };
                识别合集toolStripMenuItem.DropDownItems.Add(item);
            }
        }

        private void InitSpiltModel()
        {
            tmsSpiltMode.DropDownItems.Clear();
            foreach (SpiltMode model in Enum.GetValues(typeof(SpiltMode)))
            {
                var item = new ToolStripMenuItem
                {
                    Text = model.ToString(),
                    Tag = model.GetHashCode(),
                    Font = tsmContentType.Font,
                    Image = Resources.ResourceManager.GetObject(model.ToString()) as Bitmap,
                    ImageScaling = ToolStripItemImageScaling.None
                };
                item.Click += ocrSpiltModelItem_Click;
                if (model.Equals(NowSpiltModel)) item.Checked = true;
                tmsSpiltMode.DropDownItems.Add(item);
            }
        }

        private void InitBiaoDian()
        {
            foreach (var model in Enum.GetValues(typeof(BiaoDianMode)))
            {
                if (model.Equals(BiaoDianMode.自动))
                    continue;
                var item = new ToolStripMenuItem
                {
                    Text = model.ToString() + "标点",
                    Tag = model.GetHashCode(),
                    Font = tsmContentType.Font,
                    Image = Resources.ResourceManager.GetObject(model.ToString() + "标点") as Bitmap,
                    ImageScaling = ToolStripItemImageScaling.None
                };
                item.Click += (sender, e) =>
                {
                    var biaoDian = (BiaoDianMode)BoxUtil.GetInt32FromObject((sender as ToolStripMenuItem)?.Tag?.ToString());
                    SetUcContentBiaoDianMode(biaoDian);
                };
                tsmTransfer.DropDownItems.Add(item);
            }

            foreach (var model in Enum.GetValues(typeof(HanZiMode)))
            {
                if (model.Equals(HanZiMode.自动))
                    continue;
                var item = new ToolStripMenuItem
                {
                    Text = "转为" + model.ToString(),
                    Tag = model.GetHashCode(),
                    Font = tsmContentType.Font,
                    Image = Resources.ResourceManager.GetObject(model.ToString()) as Bitmap,
                    ImageScaling = ToolStripItemImageScaling.None
                };
                item.Click += (sender, e) =>
                {
                    var biaoDian = (HanZiMode)BoxUtil.GetInt32FromObject((sender as ToolStripMenuItem)?.Tag?.ToString());
                    SetUcContentHanZiMode(biaoDian);
                };
                tsmTransfer.DropDownItems.Add(item);
            }

            var pinYinItem = new ToolStripMenuItem
            {
                Text = "汉字->拼音",
                Font = tsmContentType.Font,
                Image = Resources.ResourceManager.GetObject("拼音") as Bitmap,
                ImageScaling = ToolStripItemImageScaling.None
            };
            pinYinItem.Click += (sender, e) =>
            {
                pinYinItem.Checked = !pinYinItem.Checked;
                pinYinItem.Text = pinYinItem.Checked ? "拼音->汉字" : "汉字->拼音";
                SetUcContentPinYin(pinYinItem.Checked);
            };
            tsmTransfer.DropDownItems.Add(pinYinItem);
        }

        #endregion

        #region 绑定识别结果

        private bool IsClosingTabs;

        private void CloseAllTabs()
        {
            IsClosingTabs = true;
            try
            {
                CommonMethod.DetermineCall(this, delegate
                {
                    tbMain.Tag = null;
                    content.Tag = null;
                    tbMain.SelectedTab = tbContentText;
                    SetDefaultTabContentName(tbContentText);
                    ShowContentText("");
                    try
                    {
                        for (var i = 0; i < tbMain.TabCount; i++)
                        {
                            var itemPage = tbMain.TabPages[i];
                            if (itemPage == tbContentText || itemPage == tbImageBox) continue;
                            tbMain.TabPages.Remove(itemPage);
                            i--;
                        }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
                });
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe);
            }
            finally
            {
                IsClosingTabs = false;
            }
        }

        private void LoadOtherOcrResult(string tagId, ProcessBy processBy, string fileIdentity, bool sync = true)
        {
            if (!sync || Program.NowUser?.IsSetOtherResult == true)
            {
                if (sync)
                {
                    Task.Factory.StartNew(() => { GetResultProcess(tagId, processBy, fileIdentity); });
                }
                else
                {
                    Thread.Sleep(800);
                    GetResultProcess(tagId, processBy, fileIdentity, true);
                }
            }
        }

        private void GetResultProcess(string tagId, ProcessBy processBy, string fileIdentity,
            bool hasResultBreak = false)
        {
            var loopCount = hasResultBreak ? 20 : 40;
            for (var i = 0; i < loopCount; i++)
            {
                var id = Equals(processBy, ProcessBy.主界面) || Equals(processBy, ProcessBy.固定区域)
                    ? tbMain.Tag as string
                    : Equals(processBy, ProcessBy.划词翻译)
                        ? frmOCR.Tag as string
                        : tagId;
                if (string.IsNullOrEmpty(id) || !id.Equals(tagId)) return;
                var lstResult = OcrHelper.GetResultById(id);
                if (lstResult?.Count > 0)
                {
                    if (!hasResultBreak) loopCount = Math.Min(loopCount, i + 3);
                    lstResult.ForEach(ocr =>
                    {
                        ocr.Identity = fileIdentity;
                        ocr.ProcessBy = processBy;
                        OcrResultPool.Add(ocr);
                    });
                }

                if (hasResultBreak && lstResult?.Count > 0)
                {
                    LoadOtherOcrResult(tagId, processBy, fileIdentity, true);
                    break;
                }

                if (i == loopCount - 1)
                {
                    if (hasResultBreak && (lstResult == null || lstResult.Count <= 0))
                    {
                        var ocr = new OcrContent
                        {
                            id = fileIdentity,
                            Identity = fileIdentity,
                            ProcessBy = processBy,
                            result = new ResultEntity
                            {
                                autoText = "识别失败！",
                                spiltText = "识别失败！"
                            },
                            processName = CommonString.StrReminder
                        };
                        OcrResultPool.Add(ocr);
                    }
                }
                else
                {
                    Thread.Sleep(1500);
                }
            }
        }

        private void BindResult(OcrContent item)
        {
            if (IsClosingTabs) return;
            if (item?.result == null) return;
            var tabTitle = string.Format("【{0}】", item.processName);
            var tabId = item.processId.ToString();
            bool isTxtTrans = item.ocrType.Equals(OcrType.翻译)
                              && imageBox.Image == null
                              && item.result.resultType == ResutypeEnum.文本;

            if (isTxtTrans)
            {
                tabTitle = "【文本翻译】";
                tabId = "TransResult";
                if (BindTransResult(tabId, item)) return;
            }

            lock (content)
            {
                var isFirstTab = content.Tag == null;
                var tabPage = isFirstTab ? tbContentText : new TabPage()
                {
                    Padding = Padding.Empty,
                    Margin = Padding.Empty
                };
                var contentCtrl = isFirstTab ? content : new UcContent
                {
                    Dock = DockStyle.Fill,
                    SpiltModel = NowSpiltModel,
                    IsShowOldContent = IsShowOldContent,
                    MenuStrip = content.MenuStrip,
                };
                if (!isFirstTab)
                {
                    contentCtrl.TxtKeyDownEventDelegate = TxtContent_KeyDown;
                    contentCtrl.SetDragDrop();
                }
                tabPage.Text = tabTitle;
                tabPage.Name = tabId;

                if (isFirstTab)
                {
                    content.Tag = item.processName;
                }
                else
                {
                    tabPage.Controls.Add(contentCtrl);
                    tbMain.TabPages.Add(tabPage);
                }
                BindUcContent(contentCtrl, item, isTxtTrans);
            }
        }

        private void BindToolSearch(OcrContent item)
        {
            var ucContent = frmOCR?.GetUcContent();
            ucContent.SpiltModel = NowSpiltModel;
            ucContent.IsShowOldContent = IsShowOldContent;
            //ucContent.ContentBackColor = DefaultTxtColor;
            //ucContent.MenuStrip = content.MenuStrip;

            BindUcContent(ucContent, item, Equals(item.id, frmOCR.Tag?.ToString()));
        }

        private void BindFixCapture(OcrContent item)
        {
            var ucContent = nowShadowContent;
            if (ucContent != null && !ucContent.IsDisposed)
            {
                try
                {
                    ucContent.Visible = true;
                    ucContent.BringToFront();

                    BindUcContent(ucContent, item, false, false);
                }
                catch { }
            }
        }

        private bool BindTransResult(string transKey, OcrContent ocr)
        {
            var result = false;
            var controls = Controls.Find(transKey, true);
            if (controls.Length > 0)
                foreach (var item in controls[0].Controls)
                    if (item is UcContent)
                    {
                        BindUcContent(item as UcContent, ocr, true);
                        result = true;
                        break;
                    }

            return result;
        }

        private void BindUcContent(UcContent ucContent, OcrContent ocr, bool isAppend = false, bool isSetModel = true)
        {
            ucContent.Padding = Padding.Empty;
            ucContent.Margin = Padding.Empty;
            ucContent.BindImageOnly(imageBox.Image, imageBox.Tag?.ToString(), CommonSetting.图片自动缩放);
            if (isSetModel)
            {
                ucContent.NowDisplayMode = NowDisplayMode;
                ucContent.BiaoDianMode = BiaoDianMode.自动;
                ucContent.HanZiMode = HanZiMode.自动;
                ucContent.IsPinYin = false;
            }
            ucContent.RefreshStyle();
            ucContent.BindContentByOcr(ocr, isAppend,
                Equals(ocr.ProcessBy, ProcessBy.固定区域) || CommonSetting.显示文字预览);
            //ucContent.Focus();
        }

        #endregion

        #region 设置相关

        private void tsmShowTool_Click(object sender, EventArgs e)
        {
            FrmTool.VisibleChange();
            CommonSetting.SetValue("显示工具栏", FrmTool.Visible);
        }

        private void tsmShowMain_Click(object sender, EventArgs e)
        {
            if (Visible)
                Visible = false;
            else
                ShowWindow();
        }

        private void NotifyMain_BalloonTipClicked(object sender, EventArgs e)
        {
            var action = notifyMain.Tag as BalloonTipAction;

            if (action != null && !string.IsNullOrEmpty(action.Text))
                switch (action.ClickAction)
                {
                    case BalloonTipClickAction.OpenUrl:
                        CommonMethod.OpenUrl(action.Text);
                        break;
                    case BalloonTipClickAction.OpenForm:
                        CommonMethod.OpenForm(action.Text);
                        break;
                }
        }

        private bool IsOverlapped(Form form)
        {
            return isoverlapped(form.Handle, form);
        }

        private bool isoverlapped(IntPtr win, Form form)
        {
            try
            {
                var preWin = NativeMethods.GetWindow(win, 3); //获取显示在Form之上的窗口

                if (preWin == null || preWin == IntPtr.Zero)
                    return false;

                if (!NativeMethods.IsWindowVisible(preWin))
                    return isoverlapped(preWin, form);

                var rect = new RECT();
                if (NativeMethods.GetWindowRect(preWin, out rect)) //获取窗体矩形
                {
                    var winrect = new Rectangle(rect.Left, rect.Top, rect.Right - rect.Left, rect.Bottom - rect.Top);

                    if (winrect.Width == Screen.PrimaryScreen.WorkingArea.Width &&
                        winrect.Y == Screen.PrimaryScreen.WorkingArea.Height) //菜单栏。不判断遮挡（可略过）
                        return isoverlapped(preWin, form);

                    if (winrect.X == 0 && winrect.Width == 54 && winrect.Height == 54) //开始按钮。不判断遮挡（可略过）
                        return isoverlapped(preWin, form);

                    var formRect = new Rectangle(form.Location, form.Size); //Form窗体矩形
                    if (formRect.IntersectsWith(winrect)) //判断是否遮挡
                        return true;
                }

                return isoverlapped(preWin, form);
            }
            catch
            {
            }

            return false;
        }

        private void ShowWindow()
        {
            CommonMethod.DetermineCall(this, delegate
            {
                this.ForceActivate();
                Application.DoEvents();
            });
        }

        private void pnlTop_Click(object sender, EventArgs e)
        {
            SetTopMost(!TopMost);
            CommonSetting.SetValue("窗体置顶", TopMost);
        }

        private void SetTheme()
        {
            if (Enum.TryParse(CommonSetting.主题样式, out MetroColorStyle style))
            {
                msManager.Style = style;
            }
            msManager.Theme = CommonSetting.深色模式 ? MetroThemeStyle.Dark : MetroThemeStyle.Light;
            tipMain.SetToolTip(pnlDark, CommonSetting.深色模式 ? "浅色模式" : "深色模式");
            pnlDark.BackgroundImage = CommonSetting.深色模式 ? Resources.sun : Resources.sun_dark;
        }

        private void pnlDark_Click(object sender, EventArgs e)
        {
            CommonSetting.深色模式 = !CommonSetting.深色模式;
            SetTheme();
            CommonSetting.SetValue("深色模式", CommonSetting.深色模式);
        }

        private void pnlSetting_Click(object sender, EventArgs e)
        {
            cmsNotify.Show(pnlSetting, new Point(0, 32));
        }

        private void tsmExit_Click(object sender, EventArgs e)
        {
            UnRegAllHandle();
            FrmTool.Hide();
            Hide();
            notifyMain.Dispose();
            CommonMethod.Exit();
        }

        private const int WM_HOTKEY = 0x312; //窗口消息-热键
        private const int WM_CREATE = 0x1; //窗口消息-创建
        private const int WM_DESTROY = 0x2; //窗口消息-销毁
        private const int WM_DISPLAYCHANGE = 0x007e;//分辨率调整事件

        private bool IsOnSetting;

        internal static List<HotKeyEntity> LstHotKeys = new List<HotKeyEntity>();
        private const ushort Hot_CopyKey = 0x3454;
        private const ushort Hot_OCRTextModeKey = 0x3455;
        private const ushort Hot_OcrNowModeKey = 0x3456;
        private const ushort Hot_OCRLineModeKey = 0x3457;
        private const ushort Hot_OCRMathModeKey = 0x3458;
        private const ushort Hot_OCRTableModeKey = 0x3459;
        private const ushort Hot_OCRTransModeKey = 0x3460;
        private const ushort Hot_OCRSearchModeKey = 0x3461;
        private const ushort Hot_CapturePasteKey = 0x3462;
        private const ushort Hot_FastCapturePasteKey = 0x3472;
        private const ushort Hot_PasteShowHideKey = 0x3473;
        private const ushort Hot_PasteImageKey = 0x3463;

        private const ushort Hot_GunDongCaptureKey = 0x3465;
        private const ushort Hot_FastCaptureKey = 0x3471;
        private const ushort Hot_CaptureAndEditKey = 0x3466;
        private const ushort Hot_DelayCaptureKey = 0x3467;
        private const ushort Hot_CopyTrans = 0x3468;
        private const ushort Hot_FixedAreaOcr = 0x3469;
        private const ushort Hot_FixedAreaTrans = 0x3470;

        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m);
            switch (m.Msg)
            {
                case WM_DISPLAYCHANGE:
                    FrmTool.ResetLocation();
                    break;
                case WM_HOTKEY: //窗口消息-热键ID
                    if (!IsOnSetting)
                        switch (m.WParam.ToInt32())
                        {
                            #region OCR相关

                            case Hot_OcrNowModeKey:
                                截图识别ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_OCRTextModeKey:
                                InitItemTypeByValue(tsmContentType, OcrType.文本.GetHashCode().ToString());
                                截图识别ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_OCRLineModeKey:
                                InitItemTypeByValue(tsmContentType, OcrType.竖排.GetHashCode().ToString());
                                截图识别ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_OCRMathModeKey:
                                InitItemTypeByValue(tsmContentType, OcrType.公式.GetHashCode().ToString());
                                截图识别ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_OCRTableModeKey:
                                InitItemTypeByValue(tsmContentType, OcrType.表格.GetHashCode().ToString());
                                截图识别ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_OCRTransModeKey:
                                InitItemTypeByValue(tsmContentType, OcrType.翻译.GetHashCode().ToString());
                                截图识别ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_OCRSearchModeKey:
                                截图识别ToolStripMenuItem_Click("搜索", null);
                                break;
                            case Hot_FixedAreaOcr:
                                FixAreaItemClick(CommonEnumAction<string>.FindMeunItem(cmsNotify, "固定区域识别"));
                                break;
                            case Hot_FixedAreaTrans:
                                FixAreaItemClick(CommonEnumAction<string>.FindMeunItem(cmsNotify, "固定区域翻译"));
                                break;

                            #endregion

                            #region 截图

                            case Hot_FastCaptureKey:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.截图), null);
                                break;
                            case Hot_CaptureAndEditKey:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.截图编辑), null);
                                break;
                            case Hot_DelayCaptureKey:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.延时截图), null);
                                break;
                            case Hot_GunDongCaptureKey:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.滚动截屏), null);
                                break;

                            #endregion

                            case Hot_CopyTrans:
                                InitItemTypeByValue(tsmContentType, OcrType.翻译.GetHashCode().ToString());
                                CopyAction(true);
                                break;
                            case Hot_FastCapturePasteKey:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.快速贴图), null);
                                break;
                            case Hot_CapturePasteKey:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.截图贴图), null);
                                break;
                            case Hot_PasteImageKey:
                                文字贴图ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_PasteShowHideKey:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.显隐贴图), null);
                                break;

                            case Hot_CopyKey:
                                CopyAction();
                                break;
                        }

                    break;
                case WM_CREATE: //窗口消息-创建
                                //RegisterClipboard();
                    RegAll(true);
                    break;
                case WM_DESTROY: //窗口消息-销毁
                    UnRegAllHandle();
                    break;
                    //case WM_DRAWCLIPBOARD:
                    //    //将WM_DRAWCLIPBOARD消息传递到下一个观察链中的窗口
                    //    SendMessage(NextClipHwnd, m.Msg, m.WParam, m.LParam);
                    //    //获取Clipboard内容，并处理
                    //    var text = ClipboardService.GetText();
                    //    if (!string.IsNullOrEmpty(text))
                    //    {
                    //        ClipboardTexts.Add(text);
                    //    }
                    //    break;
            }
        }

        private void UnRegAllHandle()
        {
            //try
            //{
            //    UnRegisterClipboard();
            //}
            //catch { }
            try
            {
                UnRegAll();
            }
            catch
            {
            }
        }

        private void RegHotKey(List<HotKeyEntity> lstKeys)
        {
            foreach (var entity in lstKeys)
                try
                {
                    UnRegHotKey(entity.Id);
                    HotKeyHelper.RegKey(Handle, entity);
                }
                catch
                {
                }
        }

        private void UnRegHotKey(int keyCode)
        {
            try
            {
                HotKeyHelper.UnRegKey(Handle, keyCode);
            }
            catch
            {
            }
        }

        ////存放观察链中下一个窗口句柄   
        //IntPtr NextClipHwnd;

        //BlockingCollection<string> ClipboardTexts = new BlockingCollection<string>();

        //// SetClipboardViewer 用于往观察链中添加一个窗口句柄，这个窗口就可成为观察链中的一员了，返回值指向下一个观察者
        //[System.Runtime.InteropServices.DllImport("user32")]
        //private static extern IntPtr SetClipboardViewer(IntPtr hwnd);

        ////ChangeClipboardChain删除由hwnd指定的观察链成员，这是一个窗口句柄，第二个参数hWndNext是观察链中下一个窗口的句柄
        //[System.Runtime.InteropServices.DllImport("user32")]
        //private static extern IntPtr ChangeClipboardChain(IntPtr hwnd, IntPtr hWndNext);

        ////SendMessage 发送消息
        //[System.Runtime.InteropServices.DllImport("user32")]
        //private static extern int SendMessage(IntPtr hwnd, int wMsg, IntPtr wParam, IntPtr lParam);

        ////Clipboard内容变化消息
        //const int WM_DRAWCLIPBOARD = 0x308;
        ////Clipboard观察链变化消息
        //const int WM_CHANGECBCHAIN = 0x30D;

        ////private ClipboardListener ClipboardListener;
        ////public static ClipboardNative ClipboardNativeService;
        //private void RegisterClipboard()
        //{
        //    //获得观察链中下一个窗口句柄
        //    NextClipHwnd = SetClipboardViewer(this.Handle);
        //    //ClipboardNativeService = new ClipboardNative(new ActiveWindowListener());
        //    //ClipboardListener = new ClipboardListener(ClipboardNativeService);
        //    //ClipboardListener.开始箭头(true);
        //    FileStatusResultProcess();
        //}

        //private void UnRegisterClipboard()
        //{
        //    //从观察链中删除本观察窗口
        //    ChangeClipboardChain(this.Handle, NextClipHwnd);
        //    //将变动消息WM_CHANGECBCHAIN消息传递到下一个观察链中的窗口
        //    SendMessage(NextClipHwnd, WM_CHANGECBCHAIN, this.Handle, NextClipHwnd);
        //    //if (ClipboardListener != null)
        //    //{
        //    //    ClipboardListener.Stop();
        //    //    ClipboardListener.Dispose();
        //    //}
        //}

        private void tsmTrans_Click(object sender, EventArgs e)
        {
            var txt = GetCurrentTxt(false);
            TransByText(txt);
        }

        private void TransByText(string content)
        {
            if (!CommonString.IsOnRec && !string.IsNullOrEmpty(content))
            {
                SetPicImage(null);
                OcrPoolProcess.ProcessByText(NowOcrGroupType, CurrentTranslateFrom, CurrentTranslateTo,
                    content, ProcessBy.主界面, null);
            }
        }

        //private void FileStatusResultProcess()
        //{
        //    new Thread(p =>
        //    {
        //        try
        //        {
        //            foreach (var content in ClipboardTexts.GetConsumingEnumerable())
        //            {
        //                try
        //                {
        //                    //if (IsCopyTrans && NowOcrType == OCRType.翻译)
        //                    //{
        //                    //    TransByText(content);
        //                    //}
        //                    ////CodeProcessHelper.SetFileStatusResult(content);
        //                }
        //                catch (Exception oe)
        //                {
        //                    Console.WriteLine(oe.Message);
        //                }
        //            }
        //        }
        //        catch (Exception oe)
        //        {
        //            Console.WriteLine(oe.Message);
        //        }
        //    })
        //    { IsBackground = true, Priority = ThreadPriority.Highest }.开始箭头();
        //}

        private void IniRegKey(HotKeyEntity entity)
        {
            var strKey = CommonSetting.GetValue<string>("快捷键", entity.KeyName.Trim())?.ToString();
            if (string.IsNullOrEmpty(strKey))
            {
                entity.Hotkey = entity.DefaultKey;
            }
            else
            {
                if (BoxUtil.IsInt(strKey))
                {
                    entity.Hotkey = (Keys)BoxUtil.GetInt32FromObject(strKey);
                }
                else
                {
                    if (Enum.TryParse(strKey, out Keys keys))
                    {
                        entity.Hotkey = keys;
                    }
                }
            }
        }

        private void InitHotKeyList()
        {
            var fastCapture = new HotKeyEntity(Hot_FastCaptureKey)
            {
                DefaultKey = Keys.Control | Keys.Alt | Keys.A,
                KeyName = "快速截图",
                Desc = "框选矩形截图（不带编辑）",
                Group = HotKeyType.截图
            };
            IniRegKey(fastCapture);
            LstHotKeys.Add(fastCapture);
            var freeCapture = new HotKeyEntity(Hot_CaptureAndEditKey)
            {
                DefaultKey = Keys.PrintScreen,
                KeyName = "框选截图",
                Desc = "框选矩形截图（带编辑）",
                Group = HotKeyType.截图
            };
            IniRegKey(freeCapture);
            LstHotKeys.Add(freeCapture);
            var txtGunDong = new HotKeyEntity(Hot_GunDongCaptureKey)
            {
                KeyName = "滚动截图",
                Desc = "滚动截屏",
                Group = HotKeyType.截图
            };
            IniRegKey(txtGunDong);
            LstHotKeys.Add(txtGunDong);
            var txtDelay = new HotKeyEntity(Hot_DelayCaptureKey)
            {
                KeyName = "延时截图",
                Desc = "延时截图",
                Group = HotKeyType.截图
            };
            IniRegKey(txtDelay);
            LstHotKeys.Add(txtDelay);

            var fastPaste = new HotKeyEntity(Hot_FastCapturePasteKey)
            {
                DefaultKey = Keys.F2,
                KeyName = "快速贴图",
                Desc = "截图（不带编辑）并贴图到屏幕上",
                Group = HotKeyType.贴图
            };
            IniRegKey(fastPaste);
            LstHotKeys.Add(fastPaste);
            var capturePaste = new HotKeyEntity(Hot_CapturePasteKey)
            {
                DefaultKey = Keys.F7,
                KeyName = "截图贴图",
                Desc = "截图（带编辑）并贴图到屏幕上",
                Group = HotKeyType.贴图
            };
            IniRegKey(capturePaste);
            LstHotKeys.Add(capturePaste);
            var txtPaste = new HotKeyEntity(Hot_PasteImageKey)
            {
                DefaultKey = Keys.F6,
                KeyName = "粘贴贴图",
                Desc = "将粘贴板中的文字或图片贴到屏幕上",
                Group = HotKeyType.贴图
            };
            IniRegKey(txtPaste);
            LstHotKeys.Add(txtPaste);
            var hidePaste = new HotKeyEntity(Hot_PasteShowHideKey)
            {
                KeyName = "显隐贴图",
                Desc = "显示/隐藏所有贴图",
                Group = HotKeyType.贴图
            };
            IniRegKey(hidePaste);
            LstHotKeys.Add(hidePaste);

            var fixOcrEntity = new HotKeyEntity(Hot_FixedAreaOcr)
            {
                KeyName = "区域识别",
                Desc = "固定区域截图并识别",
                Group = HotKeyType.其他功能
            };
            IniRegKey(fixOcrEntity);
            LstHotKeys.Add(fixOcrEntity);
            var fixTransEntity = new HotKeyEntity(Hot_FixedAreaTrans)
            {
                KeyName = "区域翻译",
                Desc = "固定区域截图并翻译",
                Group = HotKeyType.其他功能
            };
            IniRegKey(fixTransEntity);
            LstHotKeys.Add(fixTransEntity);

            var copyOcrEntity = new HotKeyEntity(Hot_CopyKey)
            {
                KeyName = "粘贴识别",
                Desc = "粘贴并以【当前选择的模式】识别",
                Group = HotKeyType.其他功能
            };
            IniRegKey(copyOcrEntity);
            LstHotKeys.Add(copyOcrEntity);
            var transTextEntity = new HotKeyEntity(Hot_CopyTrans)
            {
                KeyName = "粘贴翻译",
                Desc = "翻译粘贴板文字或图片",
                Group = HotKeyType.其他功能
            };
            IniRegKey(transTextEntity);
            LstHotKeys.Add(transTextEntity);

            var captureEntity = new HotKeyEntity(Hot_OcrNowModeKey)
            {
                DefaultKey = Keys.F3,
                KeyName = "截图识别",
                Desc = "截图并以【当前主界面中选择的模式】识别",
                Group = HotKeyType.OCR识别
            };
            IniRegKey(captureEntity);
            LstHotKeys.Add(captureEntity);
            var ocrEntity = new HotKeyEntity(Hot_OCRTextModeKey)
            {
                DefaultKey = Keys.F4,
                KeyName = "文字识别",
                Desc = "截图并以【文字】模式识别",
                Group = HotKeyType.OCR识别
            };
            IniRegKey(ocrEntity);
            LstHotKeys.Add(ocrEntity);
            var shuEntity = new HotKeyEntity(Hot_OCRLineModeKey)
            {
                KeyName = "竖排识别",
                Desc = "截图并以【竖排文字】模式识别",
                Group = HotKeyType.OCR识别
            };
            IniRegKey(shuEntity);
            LstHotKeys.Add(shuEntity);
            var mathEntity = new HotKeyEntity(Hot_OCRMathModeKey)
            {
                KeyName = "公式识别",
                Desc = "截图并以【公式】模式识别",
                Group = HotKeyType.OCR识别
            };
            IniRegKey(mathEntity);
            LstHotKeys.Add(mathEntity);
            var tableEntity = new HotKeyEntity(Hot_OCRTableModeKey)
            {
                KeyName = "表格识别",
                Desc = "截图并以【表格】模式识别",
                Group = HotKeyType.OCR识别
            };
            IniRegKey(tableEntity);
            LstHotKeys.Add(tableEntity);
            var transEntity = new HotKeyEntity(Hot_OCRTransModeKey)
            {
                KeyName = "截图翻译",
                Desc = "截图并以【翻译】模式识别",
                Group = HotKeyType.OCR识别
            };
            IniRegKey(transEntity);
            LstHotKeys.Add(transEntity);
            var searchEntity = new HotKeyEntity(Hot_OCRSearchModeKey)
            {
                KeyName = "截图搜索",
                Desc = "截图并搜索识别内容",
                Group = HotKeyType.OCR识别
            };
            IniRegKey(searchEntity);
            LstHotKeys.Add(searchEntity);
            LstHotKeys.Reverse();
        }

        private void RegAll(bool isFirst)
        {
            if (LstHotKeys.Count <= 0 && CommonString.IsAutoLogin) InitHotKeyList();
            IsOnSetting = true;
            RegHotKey(LstHotKeys);
            //RegHotKey(StrKey_Trans);
            IsOnSetting = false;
            if (isFirst) ShowNotifyMessage();
        }

        private void ShowNotifyMessage()
        {
            var sb = new StringBuilder();
            foreach (var item in LstHotKeys)
                if (!string.IsNullOrEmpty(item.StrKey) && !Equals(item.StrKey, CommonString.StrDefaultDesc))
                    sb.AppendLine(string.Format("{0}:{1}", item.KeyName, item.StrKey));
            var strMsg = string.Empty;
            if (sb.Length <= 0)
                strMsg = "当前无快捷键。";
            else
                strMsg = string.Format("【当前已设置快捷键】\n{0}", sb);
            strMsg += "\n\n更多快捷键，请在\n系统设置->快捷键 中设置！";
            CommonMethod.ShowNotificationTip(strMsg, null, 5 * 1000);
            //notifyMain.ShowBalloonTip(5000, Application.ProductName, string.Format("小主!我在这里，有事双击我！\n\n{0}\n\n", strMsg), ToolTipIcon.Info);
        }

        private void UnRegAll()
        {
            foreach (var item in LstHotKeys) UnRegHotKey(item.Id);
            //UnRegHotKey(StrKey_Trans);
        }

        private void 系统设置SToolStripMenuItem_Click(object sender, EventArgs e)
        {
            IsOnSetting = true;
            UnRegAll();
            using (var frmSetting = new FormSetting { Icon = Icon, Theme = msManager.Theme, Style = msManager.Style, StyleManager = msManager })
            {
                frmSetting.TopMost = true;
                if (!Visible) frmSetting.StartPosition = FormStartPosition.CenterScreen;
                if (Equals(sender, tsmToolImage))
                {
                    frmSetting.OpenTab = "界面";
                    frmSetting.SharkGroup = "图片设置";
                }
                else if (Equals(sender, 关于我们ToolStripMenuItem))
                {
                    frmSetting.OpenTab = "关于";
                    frmSetting.SharkGroup = "联系我们";
                }

                frmSetting.ShowDialog(this);
            }

            SetTopMost(CommonSetting.窗体置顶);

            if (!Equals(NowDisplayMode, CommonSetting.图文模式 ? DisplayModel.图文模式 : DisplayModel.文字模式))
            {
                tsmPicViewModel_Click(sender, e);
            }
            if (Enum.TryParse(CommonSetting.分段模式, out SpiltMode spiltMode) && !Equals(NowSpiltModel, spiltMode))
            {
                BindSpiltModel(spiltMode);
            }

            RefreshAllStyleImage();

            CommonUpdate.InitUpdate();

            //SetTheme();
            CommonMsg.Refresh();

            NowOcrGroupType = OcrHelper.GetGroupByName(CommonSetting.识别引擎);

            RefreshUcContent();

            RegAll(false);

            SetSearchEngine();

            if (Enum.TryParse(CommonSetting.加载动画, out LoadingType loadingType))
            {
                NowLoadingType = loadingType;
            }

            try
            {
                ShortcutHelpers.RegFileType();
            }
            catch { }

            IsOnSetting = false;
            if (Visible) ShowWindow();

            if (CommonSetting.启用本地识别)
            {
                Task.Factory.StartNew(() =>
                {
                    LocalOcrService.OpenOcrService((int)CommonSetting.本地识别端口, (int)CommonSetting.本地识别线程数, false);
                });
            }
        }

        #endregion

        #region 登录相关

        private void InitNetWorkInfo()
        {
            SetNowNetWork(CommonString.IsOnLine, true);
            NetworkChange.NetworkAvailabilityChanged += NetworkChange_NetworkAvailabilityChanged;
        }

        private void NetworkChange_NetworkAvailabilityChanged(object sender, NetworkAvailabilityEventArgs e)
        {
            SetNowNetWork(e.IsAvailable);
        }

        private void SetNowNetWork(bool isAvailable, bool isUserChange = false)
        {
            CommonString.IsOnLine = isAvailable;
            if (isUserChange)
                CommonString.IsOnLine = NetworkInterface.GetIsNetworkAvailable();
            else
                NetWorkChangeEvent();
            //tipMain.SetToolTip(pnlNetWork, "本地网络" + (CommonString.IsOnLine ? "正常" : "异常"));
            //lnkLogin.Image = CommonString.IsOnLine ? Properties.Resources.Info_OK : Properties.Resources.Info_Error;
        }

        private void NetWorkChangeEvent()
        {
            BindUserInfo();
            if (CommonString.IsOnLine)
                if (Program.NowUser == null || !Program.NowUser.IsLogined)
                    DoLoginSilence();
        }

        private void DoLoginSilence()
        {
            if (!CommonString.IsAutoLogin)
            {
                return;
            }
            var account = IniHelper.GetValue("配置", "Account");
            var pwd = IniHelper.GetValue("配置", "Password");
            if (!string.IsNullOrEmpty(account) && !string.IsNullOrEmpty(pwd))
            {
                var strMsg = "";
                var result = OcrHelper.DoLogin(account, pwd, ref strMsg);
                BindUserInfo();
            }
        }

        private void metroLink1_Click(object sender, EventArgs e)
        {
            if (Program.NowUser == null)
            {
                using (var frmLogin = new FrmLogin { Icon = Icon, Theme = msManager.Theme, Style = msManager.Style, StyleManager = msManager })
                {
                    frmLogin.ShowDialog(this);
                }
            }
            else
            {
                // 显示用户信息
                using (var frmUser = new FrmUserInfo { Icon = Icon, Theme = msManager.Theme, Style = msManager.Style, StyleManager = msManager })
                {
                    frmUser.ShowDialog(this);
                }

                ShowMsgControl();
            }

            BindUserInfo();
            SendKeys.Send("{Tab}");
        }

        private Bitmap lnkLoginImage;

        private void BindUserInfo()
        {
            var lastText = lnkLogin.Text;
            lnkLogin.AutoSize = true;
            lnkLoginImage = null;
            if (Program.NowUser == null || Program.NowUser.IsLogined == false)
            {
                lnkLogin.Text = "登录/注册";
                tipMain.SetToolTip(lnkLogin, "  点击登录/注册账号   ");
            }
            else
            {
                lnkLogin.Text = Program.NowUser.NickName;
                lnkLoginImage = CommonUser.GetUserLevelImage(Program.NowUser.UserType);

                tipMain.SetToolTip(lnkLogin, string.Format("{0}-{1}"
                    , string.IsNullOrEmpty(Program.NowUser.NickName)
                        ? Program.NowUser.Account
                        : Program.NowUser.NickName
                    , Program.NowUser.UserTypeName
                ));
            }

            if (lnkLogin.Width > 150)
            {
                lnkLogin.AutoSize = false;
                lnkLogin.Width = 150;
            }

            lnkLogin.Location = new Point(pnlTop.Location.X - (pnlDark.Visible ? pnlDark.Size.Width : 0) - lnkLogin.Size.Width, 6);
            CommonMethod.SetStyle(lnkLogin, ControlStyles.Selectable, false);
            BindUserImg();
            ProcessForbidControls();
            if (!Equals(lastText, lnkLogin.Text))
            {
                CheckLoginStatus();
            }
        }

        private void BindUserImg()
        {
            picUser.Image = lnkLoginImage ?? Resources.qqKeFu;
            picUser.Size = picUser.Image.Size;
            picUser.Location = new Point(lnkLogin.Location.X - picUser.Image.Width,
                lnkLogin.Location.Y + (lnkLogin.Height - picUser.Height) / 2);
            picUser.BringToFront();
            picUser.Invalidate();
        }

        #endregion
    }

    //internal class HotKeyEntity
    //{
    //    public string KeyName { get; set; }

    //    public string DefaultKey { get; set; }

    //    public string StrKey { get; set; }

    //    public int UserKey { get; set; }

    //    public int ID { get; set; }

    //    public string Desc { get; set; }

    //    public HotKeyType Group { get; set; }
    //}

    internal enum HotKeyType
    {
        截图 = 1,
        贴图 = 3,
        OCR识别 = 0,
        其他功能 = 2
    }

    internal enum Speaker
    {
        可爱正太 = 0,
        磁性男声 = 1,
        时尚男声 = 4,
        萝莉女声 = 2,
        知性女声 = 3,
        亲切女声 = 6
    }

    internal class SearchEngine
    {
        public string Name { get; set; }

        public string Url { get; set; }
    }
}