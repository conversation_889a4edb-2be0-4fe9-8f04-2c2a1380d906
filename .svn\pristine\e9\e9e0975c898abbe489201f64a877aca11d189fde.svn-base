using System;
using System.Drawing;

namespace OCRTools
{
    public struct Vector
    {
        public static readonly Vector Empty;

        public float X { get; set; }

        public float Y { get; set; }

        public Vector(float x, float y)
        {
            X = x;
            Y = y;
        }

        public override bool Equals(object obj)
        {
            if (obj is Vector vector) return vector.X == X && vector.Y == Y;

            return false;
        }

        public override string ToString()
        {
            return $"X={X}, Y={Y}";
        }

        public static bool operator ==(Vector u, Vector v)
        {
            return u.X == v.X && u.Y == v.Y;
        }

        public static bool operator !=(Vector u, Vector v)
        {
            return !(u == v);
        }

        public static Vector operator +(Vector u, Vector v)
        {
            return new Vector(u.X + v.X, u.Y + v.Y);
        }

        public static Vector operator -(Vector u, Vector v)
        {
            return new Vector(u.X - v.X, u.Y - v.Y);
        }

        public static Vector operator *(Vector u, float a)
        {
            return new Vector(a * u.X, a * u.Y);
        }

        public static Vector operator /(Vector u, float a)
        {
            return new Vector(u.X / a, u.Y / a);
        }

        public static Vector operator -(Vector u)
        {
            return new Vector(0f - u.X, 0f - u.Y);
        }

        public static explicit operator Point(Vector u)
        {
            return new Point((int)Math.Round(u.X), (int)Math.Round(u.Y));
        }

        public static implicit operator Vector(Point p)
        {
            return new Vector(p.X, p.Y);
        }
    }
}