using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace OCRTools
{
    /// <summary>
    /// 文档模式处理器
    /// 完全基于TextSelectableImageViewer的逻辑，提供精确的文字选择功能
    /// </summary>
    public class DocumentModeHandler : BaseImageModeHandler
    {
        #region 常量定义
        
        private readonly Color SELECTION_FILL_COLOR = Color.FromArgb(100, 0, 120, 215); // 类似PDF的选择颜色
        
        #endregion
        
        #region 私有字段

        // 选择相关字段
        private bool _isDragging = false;
        private Point _dragStartPoint;
        private Point _dragEndPoint;
        private TextCellInfo _startCell;
        private TextCellInfo _endCell;
        private int _startCharIndex = -1;
        private int _endCharIndex = -1;

        #endregion
        
        #region 事件定义
        
        /// <summary>
        /// 文字选择变化事件
        /// </summary>
        public event EventHandler<TextSelectionEventArgs> TextSelectionChanged;
        
        /// <summary>
        /// 模式特定事件（实现接口要求）
        /// </summary>
        public event EventHandler<EventArgs> ModeSpecificEvent;
        
        /// <summary>
        /// 文字选择事件参数
        /// </summary>
        public class TextSelectionEventArgs : EventArgs
        {
            public List<TextCellInfo> SelectedCells { get; private set; }
            public string SelectedText { get; set; }
            public int SelectedCharacterCount { get; set; }

            public TextSelectionEventArgs(List<TextCellInfo> selectedCells)
            {
                SelectedCells = selectedCells ?? new List<TextCellInfo>();
            }
        }
        
        #endregion
        
        #region BaseImageModeHandler 实现

        /// <summary>
        /// 激活文档模式
        /// </summary>
        public override void Activate()
        {
            BindMouseEvents();
        }

        /// <summary>
        /// 停用文档模式 - 解绑事件
        /// </summary>
        public override void Deactivate()
        {
            UnbindMouseEvents();
        }

        protected override void ClearCurrentState()
        {
            // 清除选择状态
            _isDragging = false;
            _startCell = null;
            _endCell = null;
            _startCharIndex = -1;
            _endCharIndex = -1;
            _dragStartPoint = Point.Empty;
            _dragEndPoint = Point.Empty;
        }
        
        public override void BindData(Image image, List<TextCellInfo> textCells)
        {
            base.BindData(image, textCells);

            // 清除选择
            ClearSelection();
        }

        public override void HandleMouseDown(MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left && _textCells.Count > 0)
            {
                var imagePoint = GetImagePoint(e.Location);
                bool isOnTextArea = FindCellAtImagePoint(imagePoint) != null;

                if (!isOnTextArea)
                {
                    // 空白区域：调用ImageBox的拖动功能
                    _viewer.CallImageBoxMouseDown(e);
                }

                // 确保控件获得焦点（文档模式需要焦点来处理键盘事件）
                if (_viewer.CanFocus)
                {
                    _viewer.Focus();
                }

                // 清除之前的选择
                ClearSelection();

                if (!isOnTextArea)
                {
                    return;
                }

                _dragStartPoint = imagePoint;
                _dragEndPoint = _dragStartPoint; // 初始化终点为起点

                // 找到起始位置的cell和字符索引（此时isDragging=false，会保存为起点信息）
                var startPos = GetCellAndCharAtPoint(_dragStartPoint);
                _startCell = startPos.Cell;
                _startCharIndex = startPos.CharIndex;
                _endCell = _startCell;
                _endCharIndex = _startCharIndex;

                // 设置拖拽状态（在获取起点信息之后）
                _isDragging = true;

                _viewer.Invalidate();
            }
            else
            {
                // 非左键或没有文字区域，调用ImageBox基础功能
                _viewer.CallImageBoxMouseDown(e);
            }
        }
        
        public override void HandleMouseMove(MouseEventArgs e)
        {
            if (_isDragging && e.Button == MouseButtons.Left)
            {
                // 文字选择拖动中，不调用ImageBox拖动
                _dragEndPoint = GetImagePoint(e.Location);

                // 找到结束位置的cell和字符索引
                var endPos = GetCellAndCharAtPoint(_dragEndPoint);
                _endCell = endPos.Cell;
                _endCharIndex = endPos.CharIndex;

                _viewer.Invalidate();
            }
            else
            {
                // 不在文字选择拖动中，调用ImageBox拖动功能
                _viewer.CallImageBoxMouseMove(e);

                // 检查鼠标是否在可选择区域，更新光标
                UpdateCursor(e.Location);
            }
        }

        public override void HandleMouseUp(MouseEventArgs e)
        {
            if (_isDragging)
            {
                // 文字选择结束
                _isDragging = false;

                // 如果没有有效选择，清除选择
                if (_startCell == null || _endCell == null ||
                    _startCharIndex < 0 || _endCharIndex < 0)
                {
                    ClearSelection();
                }

                // 触发选择变更事件
                OnTextSelectionChanged();

                _viewer.Invalidate();
            }
            else
            {
                // 不在文字选择中，调用ImageBox基础功能
                _viewer.CallImageBoxMouseUp(e);
            }
        }

        public override void HandleMouseLeave(EventArgs e)
        {
            // 鼠标离开时恢复默认光标
            _viewer.Cursor = Cursors.Default;
        }

        public override void HandlePaint(PaintEventArgs e)
        {
            if (_textCells.Count == 0)
            {
                return;
            }

            var g = e.Graphics;

            // 绘制选中的文字区域
            if (_isDragging || HasSelection())
            {
                using (var selectedBrush = new SolidBrush(SELECTION_FILL_COLOR))
                {
                    var selectedRects = GetSelectedCharacterRectangles();

                    foreach (var rect in selectedRects)
                    {
                        var displayRect = GetDisplayRectangle(rect);

                        if (!displayRect.IsEmpty)
                        {
                            g.FillRectangle(selectedBrush, displayRect);
                        }
                    }
                }
            }
        }
        
        public override void HandleZoomChanged(EventArgs e)
        {
            // 缩放变化时重新绘制
            _viewer.Invalidate();
        }

        public override void HandleScroll(ScrollEventArgs e)
        {
            // 滚动时重新绘制
            _viewer.Invalidate();
        }

        public override void HandleMouseWheel(MouseEventArgs e)
        {
            // 文档模式下鼠标滚轮不需要特殊处理，只更新光标
            if (!_isDragging)
            {
                UpdateCursor(e.Location);
            }
        }
        
        public override void ClearState()
        {
            base.ClearState();
            ClearSelection();
        }
        
        #endregion

        #region 核心方法

        /// <summary>
        /// 坐标转换：控件坐标 → 图片坐标
        /// </summary>
        private Point GetImagePoint(Point controlPoint)
        {
            // 使用基类的标准坐标转换方法
            return GetImagePointFromMouse(controlPoint);
        }

        /// <summary>
        /// 查找指定图片坐标位置的文字区域
        /// </summary>
        private TextCellInfo FindCellAtImagePoint(Point imagePoint)
        {
            return _textCells?.FirstOrDefault(cell =>
                cell?.location?.Rectangle.Contains(imagePoint) == true);
        }

        /// <summary>
        /// 获取指定点位置的cell和字符索引
        /// </summary>
        private CellCharPosition GetCellAndCharAtPoint(Point imagePoint)
        {
            // 收集精确匹配的cells
            var exactMatches = new List<TextCellInfo>();
            foreach (var cell in _textCells)
            {
                if (cell?.location != null && !string.IsNullOrEmpty(cell.words))
                {
                    var cellRect = cell.location.Rectangle;
                    if (cellRect.Contains(imagePoint))
                    {
                        exactMatches.Add(cell);
                    }
                }
            }

            // 获取智能匹配结果
            TextCellInfo smartMatch = FindNearestCell(imagePoint);

            // 决策逻辑：在精确匹配和智能匹配之间选择最佳的
            TextCellInfo bestCell = SelectBestCell(exactMatches, smartMatch, imagePoint);

            if (bestCell != null)
            {
                int charIndex = bestCell == smartMatch ?
                    CalculateCharIndexForPoint(bestCell, imagePoint) :
                    GetCharIndexInCell(bestCell, imagePoint);

                return new CellCharPosition(bestCell, charIndex);
            }

            return new CellCharPosition(null, -1);
        }

        /// <summary>
        /// 在精确匹配和智能匹配之间选择最佳cell
        /// </summary>
        private TextCellInfo SelectBestCell(List<TextCellInfo> exactMatches, TextCellInfo smartMatch, Point imagePoint)
        {
            // 如果有精确匹配，直接使用精确匹配，不考虑智能匹配
            // 精确匹配意味着点击位置确实在Cell内部，应该绝对优先
            if (exactMatches.Count > 0)
            {
                return exactMatches.Count == 1 ? exactMatches[0] : GetClosestToCenter(exactMatches, imagePoint);
            }

            // 只有在没有精确匹配时，才使用智能匹配
            return smartMatch;
        }

        private TextCellInfo GetClosestToCenter(List<TextCellInfo> cells, Point imagePoint)
        {
            return cells.OrderBy(cell => GetDistanceToCenter(cell, imagePoint)).First();
        }

        private double GetDistanceToCenter(TextCellInfo cell, Point imagePoint)
        {
            var cellRect = cell.location.Rectangle;
            var centerX = cellRect.X + cellRect.Width / 2;
            var centerY = cellRect.Y + cellRect.Height / 2;
            return Math.Sqrt(Math.Pow(imagePoint.X - centerX, 2) + Math.Pow(imagePoint.Y - centerY, 2));
        }

        /// <summary>
        /// 查找最近的文字区域
        /// </summary>
        private TextCellInfo FindNearestCell(Point imagePoint)
        {
            if (_textCells.Count == 0) return null;

            // 首先检查是否有cell的"结尾区域"包含点击点
            var endAreaCell = FindCellWithEndAreaContaining(imagePoint);
            if (endAreaCell != null)
            {
                return endAreaCell;
            }

            // 检查是否拖拽到文档底部（所有文字下方）
            var bottomCell = FindBottomCellIfPointBelowAllText(imagePoint);
            if (bottomCell != null)
            {
                return bottomCell;
            }

            var candidates = new List<CellCandidate>();

            foreach (var cell in _textCells)
            {
                if (cell?.location == null || string.IsNullOrEmpty(cell.words))
                    continue;

                var cellRect = cell.location.Rectangle;
                bool isCellVertical = DetectCellDirection(cell);

                // 计算到cell的距离
                var centerX = cellRect.X + cellRect.Width / 2;
                var centerY = cellRect.Y + cellRect.Height / 2;
                var xDistance = Math.Abs(imagePoint.X - centerX);
                var yDistance = Math.Abs(imagePoint.Y - centerY);

                // 原始欧几里得距离
                double rawDistance = Math.Sqrt(Math.Pow(xDistance, 2) + Math.Pow(yDistance, 2));

                // 根据文字方向调整权重的距离
                double weightedDistance = CalculateWeightedDistance(xDistance, yDistance, isCellVertical);

                candidates.Add(new CellCandidate
                {
                    Cell = cell,
                    WeightedDistance = weightedDistance,
                    RawDistance = rawDistance
                });
            }

            if (candidates.Count == 0) return null;

            // 按加权距离排序
            candidates.Sort((a, b) => a.WeightedDistance.CompareTo(b.WeightedDistance));

            // 如果最近的两个候选距离很接近（差距小于20%），则进行智能选择
            if (candidates.Count > 1)
            {
                var first = candidates[0];
                var second = candidates[1];
                var distanceDiff = Math.Abs(first.WeightedDistance - second.WeightedDistance);
                var avgDistance = (first.WeightedDistance + second.WeightedDistance) / 2;

                if (distanceDiff / avgDistance < config.DistanceDiffThreshold)
                {
                    // 优先选择原始距离更近的cell
                    if (second.RawDistance < first.RawDistance)
                    {
                        return second.Cell;
                    }
                }
            }

            return candidates[0].Cell;
        }

        /// <summary>
        /// 检测单个文字块的方向
        /// </summary>
        private bool DetectCellDirection(TextCellInfo cell)
        {
            if (cell?.location == null || string.IsNullOrEmpty(cell.words)) return false;

            double width = cell.location.width;
            double height = cell.location.height;
            double aspectRatio = width / height;

            // 竖排判断条件：
            // 1. 高度明显大于宽度（高宽比 > 2.0）
            // 2. 或者高度 >= 宽度且字符数较少（<=3）
            // 3. 或者是单字符且接近正方形

            bool condition1 = height / width > 2.0; // 明显的竖直形状
            bool condition2 = height >= width && cell.words.Length <= 3; // 高>=宽且字符少
            bool condition3 = cell.words.Length == 1 && aspectRatio > 0.5 && aspectRatio < 2.0; // 单字符且接近正方形

            return condition1 || condition2 || condition3;
        }

        /// <summary>
        /// 计算加权距离
        /// </summary>
        private double CalculateWeightedDistance(int xDistance, int yDistance, bool isCellVertical)
        {
            if (isCellVertical)
            {
                // 竖排：X轴距离更重要
                return Math.Sqrt(Math.Pow(xDistance * config.WeightMultiplierBase, 2) + Math.Pow(yDistance, 2));
            }
            else
            {
                // 横排：Y轴距离更重要
                return Math.Sqrt(Math.Pow(xDistance, 2) + Math.Pow(yDistance * config.WeightMultiplierBase, 2));
            }
        }

        /// <summary>
        /// 计算字符索引
        /// </summary>
        private int CalculateCharIndexForPoint(TextCellInfo cell, Point imagePoint)
        {
            if (cell == null || string.IsNullOrEmpty(cell.words))
                return -1;

            var cellRect = cell.location.Rectangle;
            bool isCellVertical = DetectCellDirection(cell);

            // 如果点在cell内部，使用精确计算
            if (cellRect.Contains(imagePoint))
            {
                return GetCharIndexInCell(cell, imagePoint);
            }

            // 点在cell外部，根据文字方向和位置关系确定字符索引
            if (isCellVertical)
            {
                // 竖排：从右到左，从上到下
                if (imagePoint.X > cellRect.Right) return 0; // 右侧 = 开头
                if (imagePoint.X < cellRect.Left) return cell.words.Length - 1; // 左侧 = 结尾
                if (imagePoint.Y < cellRect.Top) return 0; // 上方 = 开头
                if (imagePoint.Y > cellRect.Bottom) return cell.words.Length - 1; // 下方 = 结尾
            }
            else
            {
                // 横排：从左到右，从上到下
                if (imagePoint.X < cellRect.Left) return 0; // 左侧 = 开头
                if (imagePoint.X > cellRect.Right) return cell.words.Length - 1; // 右侧 = 结尾
                if (imagePoint.Y < cellRect.Top) return 0; // 上方 = 开头
                if (imagePoint.Y > cellRect.Bottom) return cell.words.Length - 1; // 下方 = 结尾
            }

            // 默认情况（理论上不应该到达这里）
            return GetCharIndexInCell(cell, imagePoint);
        }

        /// <summary>
        /// 获取文字区域内的字符索引
        /// </summary>
        private int GetCharIndexInCell(TextCellInfo cell, Point imagePoint)
        {
            if (cell == null || string.IsNullOrEmpty(cell.words))
                return -1;

            var cellRect = cell.location.Rectangle;
            bool isVertical = DetectCellDirection(cell);

            if (isVertical)
            {
                return GetCharIndexInVerticalCell(cell, imagePoint, cellRect);
            }
            else
            {
                return GetCharIndexInHorizontalCell(cell, imagePoint, cellRect);
            }
        }

        /// <summary>
        /// 获取水平文字区域内的字符索引
        /// </summary>
        private int GetCharIndexInHorizontalCell(TextCellInfo cell, Point imagePoint, Rectangle cellRect)
        {
            if (string.IsNullOrEmpty(cell.words))
                return -1;

            var relativeX = imagePoint.X - cellRect.X;

            // 尝试基于实际字体测量，使用智能字体大小校准
            var measurementResult = MeasureTextWithCalibration(cell.words, cellRect);

            if (measurementResult.Success)
            {
                var charWidths = measurementResult.CharWidths;
                float totalMeasuredWidth = measurementResult.TotalWidth;

                // 使用字体测量结果
                float scale = cellRect.Width / totalMeasuredWidth;

                float currentX = 0;
                for (int i = 0; i < charWidths.Count; i++)
                {
                    float scaledWidth = charWidths[i] * scale;

                    if (relativeX <= currentX + scaledWidth / 2)
                    {
                        return i;
                    }
                    currentX += scaledWidth;
                }

                return cell.words.Length - 1;
            }
            else
            {
                // 托底：使用平分逻辑
                var charWidth = (float)cellRect.Width / cell.words.Length;
                return Math.Max(0, Math.Min((int)(relativeX / charWidth), cell.words.Length - 1));
            }
        }

        /// <summary>
        /// 获取垂直文字区域内的字符索引
        /// </summary>
        private int GetCharIndexInVerticalCell(TextCellInfo cell, Point imagePoint, Rectangle cellRect)
        {
            if (string.IsNullOrEmpty(cell.words))
                return -1;

            var relativeY = imagePoint.Y - cellRect.Y;

            // 尝试基于实际字体测量
            float fontSize = EstimateFontSize(cellRect);
            var charHeights = new List<float>();
            float totalMeasuredHeight = 0;
            bool measurementSucceeded = true;

            foreach (char c in cell.words)
            {
                float charHeight = MeasureCharHeight(c, fontSize);
                if (charHeight <= 0) // 测量失败
                {
                    measurementSucceeded = false;
                    break;
                }
                charHeights.Add(charHeight);
                totalMeasuredHeight += charHeight;
            }

            if (measurementSucceeded && totalMeasuredHeight > 0)
            {
                // 使用字体测量结果
                float scale = cellRect.Height / totalMeasuredHeight;

                float currentY = 0;
                for (int i = 0; i < charHeights.Count; i++)
                {
                    float scaledHeight = charHeights[i] * scale;
                    if (relativeY <= currentY + scaledHeight / 2)
                    {
                        return i;
                    }
                    currentY += scaledHeight;
                }
                return cell.words.Length - 1;
            }
            else
            {
                // 托底：使用平分逻辑
                var charHeight = (float)cellRect.Height / cell.words.Length;
                return Math.Max(0, Math.Min((int)(relativeY / charHeight), cell.words.Length - 1));
            }
        }

        #endregion

        #region 结尾区域和底部检测方法

        /// <summary>
        /// 查找结尾区域包含指定点的cell
        /// </summary>
        private TextCellInfo FindCellWithEndAreaContaining(Point imagePoint)
        {
            foreach (var cell in _textCells)
            {
                if (cell?.location == null || string.IsNullOrEmpty(cell.words))
                    continue;

                var cellRect = cell.location.Rectangle;
                bool isCellVertical = DetectCellDirection(cell);

                // 扩展cell的结尾区域
                Rectangle endArea;
                if (isCellVertical)
                {
                    // 竖排：扩展下方区域
                    endArea = new Rectangle(
                        cellRect.X,
                        cellRect.Bottom,
                        cellRect.Width,
                        CalculateEndAreaExtension(cellRect)
                    );
                }
                else
                {
                    // 横排：扩展右方区域
                    endArea = new Rectangle(
                        cellRect.Right,
                        cellRect.Y,
                        CalculateEndAreaExtension(cellRect),
                        cellRect.Height
                    );
                }

                if (endArea.Contains(imagePoint))
                {
                    return cell;
                }
            }

            return null;
        }

        /// <summary>
        /// 计算结尾区域扩展大小
        /// </summary>
        private int CalculateEndAreaExtension(Rectangle cellRect)
        {
            // 扩展区域为cell尺寸的50%，最小10像素，最大30像素
            int extension = Math.Max(10, Math.Min(30, Math.Max(cellRect.Width, cellRect.Height) / 2));
            return extension;
        }

        /// <summary>
        /// 查找底部cell（如果点在所有文字下方）
        /// </summary>
        private TextCellInfo FindBottomCellIfPointBelowAllText(Point imagePoint)
        {
            if (_textCells.Count == 0) return null;

            // 找到所有文字的最底部Y坐标
            var maxBottom = _textCells.Where(c => c?.location != null && !string.IsNullOrEmpty(c.words))
                                     .Max(c => c.location.Rectangle.Bottom);

            // 计算底部检测容差
            var tolerance = CalculateBottomDetectionTolerance();

            // 如果点击位置在所有文字下方（考虑容差）
            if (imagePoint.Y > maxBottom + tolerance)
            {
                // 返回最底部的cell
                return _textCells.Where(c => c?.location != null && !string.IsNullOrEmpty(c.words))
                                .OrderByDescending(c => c.location.Rectangle.Bottom)
                                .FirstOrDefault();
            }

            return null;
        }

        /// <summary>
        /// 计算底部检测容差
        /// </summary>
        private int CalculateBottomDetectionTolerance()
        {
            if (_textCells.Count == 0) return 20;

            // 计算平均cell高度
            var avgHeight = _textCells.Where(c => c?.location != null && !string.IsNullOrEmpty(c.words))
                                     .Average(c => c.location.Rectangle.Height);

            // 容差为平均高度的50%，最小10像素，最大50像素
            return Math.Max(10, Math.Min(50, (int)(avgHeight * config.BottomDetectionRatio)));
        }

        #endregion

        #region 选择处理方法

        /// <summary>
        /// 检查是否有选择
        /// </summary>
        private bool HasSelection()
        {
            return _startCell != null && _endCell != null &&
                   _startCharIndex >= 0 && _endCharIndex >= 0;
        }

        /// <summary>
        /// 获取选中字符的矩形区域
        /// </summary>
        private List<Rectangle> GetSelectedCharacterRectangles()
        {
            var rects = new List<Rectangle>();

            // 如果正在拖拽或有有效选择，使用三部分选择逻辑
            if (_isDragging || HasSelection())
            {
                var selectedCells = GetThreePartSelection(); // 使用新的三部分选择逻辑

                foreach (var cellInfo in selectedCells)
                {
                    var cell = cellInfo.Cell;
                    var startChar = cellInfo.StartChar;
                    var endChar = cellInfo.EndChar;

                    if (cell?.location != null && !string.IsNullOrEmpty(cell.words))
                    {
                        // 获取字符级别的矩形
                        var charRect = GetCharacterRectInCell(cell, startChar, endChar);
                        if (!charRect.IsEmpty)
                        {
                            rects.Add(charRect);
                        }
                    }
                }
            }

            return rects;
        }

        /// <summary>
        /// 获取cell内指定字符范围的矩形（基于实际字体测量）
        /// </summary>
        private Rectangle GetCharacterRectInCell(TextCellInfo cell, int startChar, int endChar)
        {
            if (cell?.location == null || string.IsNullOrEmpty(cell.words) ||
                startChar < 0 || endChar < 0 || startChar >= cell.words.Length)
                return Rectangle.Empty;

            var cellRect = cell.location.Rectangle;
            bool isCellVertical = DetectCellDirection(cell);

            // 确保索引有效
            startChar = Math.Max(0, Math.Min(startChar, cell.words.Length - 1));
            endChar = Math.Max(0, Math.Min(endChar, cell.words.Length - 1));

            if (startChar > endChar)
            {
                var temp = startChar;
                startChar = endChar;
                endChar = temp;
            }

            if (isCellVertical)
            {
                return GetVerticalCharacterRect(cell, cellRect, startChar, endChar);
            }
            else
            {
                return GetHorizontalCharacterRect(cell, cellRect, startChar, endChar);
            }
        }

        /// <summary>
        /// 获取水平文字的字符矩形（基于智能字体测量+托底平分逻辑）
        /// </summary>
        private Rectangle GetHorizontalCharacterRect(TextCellInfo cell, Rectangle cellRect, int startChar, int endChar)
        {
            // 使用与字符索引计算相同的智能测量逻辑
            var measurementResult = MeasureTextWithCalibration(cell.words, cellRect);

            if (measurementResult.Success)
            {
                var charWidths = measurementResult.CharWidths;
                float totalMeasuredWidth = measurementResult.TotalWidth;

                // 使用字体测量结果
                float scale = cellRect.Width / totalMeasuredWidth;

                // 计算起始位置
                float startX = 0;
                for (int i = 0; i < startChar; i++)
                {
                    startX += charWidths[i] * scale;
                }

                // 计算选中区域宽度
                float selectionWidth = 0;
                for (int i = startChar; i <= endChar; i++)
                {
                    selectionWidth += charWidths[i] * scale;
                }

                return new Rectangle(
                    (int)(cellRect.X + startX),
                    cellRect.Y,
                    (int)Math.Ceiling(selectionWidth),
                    cellRect.Height
                );
            }
            else
            {
                // 托底：使用平分逻辑
                var charWidth = (float)cellRect.Width / cell.words.Length;

                return new Rectangle(
                    (int)(cellRect.X + startChar * charWidth),
                    cellRect.Y,
                    (int)Math.Ceiling((endChar - startChar + 1) * charWidth),
                    cellRect.Height
                );
            }
        }

        /// <summary>
        /// 获取垂直文字的字符矩形（基于实际字体测量+托底平分逻辑）
        /// </summary>
        private Rectangle GetVerticalCharacterRect(TextCellInfo cell, Rectangle cellRect, int startChar, int endChar)
        {
            // 尝试基于实际字体测量（与字符索引计算使用相同逻辑）
            float fontSize = EstimateFontSize(cellRect);
            var charHeights = new List<float>();
            float totalMeasuredHeight = 0;
            bool measurementSucceeded = true;

            foreach (char c in cell.words)
            {
                float charHeight = MeasureCharHeight(c, fontSize);
                if (charHeight <= 0) // 测量失败
                {
                    measurementSucceeded = false;
                    break;
                }
                charHeights.Add(charHeight);
                totalMeasuredHeight += charHeight;
            }

            if (measurementSucceeded && totalMeasuredHeight > 0)
            {
                // 使用字体测量结果
                float scale = cellRect.Height / totalMeasuredHeight;

                // 计算起始位置
                float startY = 0;
                for (int i = 0; i < startChar; i++)
                {
                    startY += charHeights[i] * scale;
                }

                // 计算选中区域高度
                float selectionHeight = 0;
                for (int i = startChar; i <= endChar; i++)
                {
                    selectionHeight += charHeights[i] * scale;
                }

                return new Rectangle(
                    cellRect.X,
                    (int)(cellRect.Y + startY),
                    cellRect.Width,
                    (int)Math.Ceiling(selectionHeight)
                );
            }
            else
            {
                // 托底：使用平分逻辑
                var charHeight = (float)cellRect.Height / cell.words.Length;

                return new Rectangle(
                    cellRect.X,
                    (int)(cellRect.Y + startChar * charHeight),
                    cellRect.Width,
                    (int)Math.Ceiling((endChar - startChar + 1) * charHeight)
                );
            }
        }

        /// <summary>
        /// 获取选中的文字区域列表
        /// </summary>
        private List<TextCellInfo> GetSelectedCells()
        {
            if (!HasSelection()) return new List<TextCellInfo>();

            var cellSelections = GetThreePartSelection();
            return cellSelections.Select(c => c.Cell).ToList();
        }

        /// <summary>
        /// 获取三部分选择结果
        /// </summary>
        private List<CellSelectionInfo> GetThreePartSelection()
        {
            // 如果没有起点和终点cell，返回空列表
            if (_startCell == null || _endCell == null || _startCharIndex < 0 || _endCharIndex < 0)
                return new List<CellSelectionInfo>();

            // 空白区域单击判断：如果没有鼠标移动且起点在空白区域，不返回任何选择
            bool isNoMouseMove = (_dragStartPoint.X == _dragEndPoint.X && _dragStartPoint.Y == _dragEndPoint.Y);

            if (isNoMouseMove && IsPointInEmptyArea(_dragStartPoint))
            {
                // 空白区域单击：不返回任何选择，避免绘制高亮
                return new List<CellSelectionInfo>();
            }

            // 特殊处理：如果起点和终点在同一个cell，直接返回该cell的字符选择
            if (_startCell == _endCell)
            {
                return GetSingleCellSelection();
            }

            // 核心改进：基于逻辑位置而非拖拽方向进行选择
            return GetLogicalSelection();
        }

        /// <summary>
        /// 检查点是否在空白区域
        /// </summary>
        private bool IsPointInEmptyArea(Point imagePoint)
        {
            return !_textCells.Any(cell =>
                cell?.location != null &&
                !string.IsNullOrEmpty(cell.words) &&
                cell.location.Rectangle.Contains(imagePoint));
        }

        /// <summary>
        /// 获取单个cell的选择
        /// </summary>
        private List<CellSelectionInfo> GetSingleCellSelection()
        {
            var result = new List<CellSelectionInfo>();

            if (_startCell != null && !string.IsNullOrEmpty(_startCell.words))
            {
                // 确保字符索引有效
                int startChar = Math.Max(0, Math.Min(_startCharIndex, _startCell.words.Length - 1));
                int endChar = Math.Max(0, Math.Min(_endCharIndex, _startCell.words.Length - 1));

                // 确保起点不大于终点
                if (startChar > endChar)
                {
                    var temp = startChar;
                    startChar = endChar;
                    endChar = temp;
                }

                result.Add(new CellSelectionInfo(_startCell, startChar, endChar));
            }

            return result;
        }

        /// <summary>
        /// 基于逻辑位置的选择
        /// </summary>
        private List<CellSelectionInfo> GetLogicalSelection()
        {
            // 使用空间路径选择，而不是简单的线性选择
            var selectedCells = GetSpatialPathSelection();

            // 计算字符选择范围
            return CalculateCharacterRangesLogical(selectedCells);
        }

        /// <summary>
        /// 获取显示矩形
        /// </summary>
        private Rectangle GetDisplayRectangle(Rectangle imageRect)
        {
            // 使用基类的标准坐标转换方法
            return GetControlRectFromImage(imageRect);
        }

        /// <summary>
        /// 更新鼠标光标
        /// </summary>
        private void UpdateCursor(Point mouseLocation)
        {
            if (_textCells.Count == 0)
            {
                _viewer.Cursor = Cursors.Default;
                return;
            }

            // 转换鼠标坐标到图片坐标
            var imagePoint = GetImagePoint(mouseLocation);

            // 检查是否在任何文字区域内
            bool isOverText = _textCells.Any(cell =>
                cell?.location != null &&
                !string.IsNullOrEmpty(cell.words) &&
                cell.location.Rectangle.Contains(imagePoint));

            // 如果鼠标在文字区域，显示文本光标
            _viewer.Cursor = isOverText ? Cursors.IBeam : Cursors.Default;
        }

        /// <summary>
        /// 清除所有选择
        /// </summary>
        public void ClearSelection()
        {
            _startCell = null;
            _endCell = null;
            _startCharIndex = -1;
            _endCharIndex = -1;
            _dragStartPoint = Point.Empty;
            _dragEndPoint = Point.Empty;

            OnTextSelectionChanged();
            _viewer?.Invalidate();
        }

        /// <summary>
        /// 获取选中的文字
        /// </summary>
        public string GetSelectedText()
        {
            if (!HasSelection()) return string.Empty;

            var selectedCells = GetThreePartSelection();
            if (selectedCells == null || selectedCells.Count == 0)
                return string.Empty;

            // 检测主要的文字方向
            var textDirection = DetectTextDirection(selectedCells);

            if (textDirection == TextDirection.Horizontal)
            {
                return GetHorizontalText(selectedCells);
            }
            else
            {
                return GetVerticalText(selectedCells);
            }
        }

        /// <summary>
        /// 检测文字方向
        /// </summary>
        private TextDirection DetectTextDirection(List<CellSelectionInfo> selectedCells)
        {
            if (selectedCells == null || selectedCells.Count == 0)
                return TextDirection.Horizontal;

            // 统计垂直和水平文字的数量
            int verticalCount = selectedCells.Count(c => DetectCellDirection(c.Cell));
            int horizontalCount = selectedCells.Count - verticalCount;

            return verticalCount > horizontalCount ? TextDirection.Vertical : TextDirection.Horizontal;
        }

        /// <summary>
        /// 获取水平文字
        /// </summary>
        private string GetHorizontalText(List<CellSelectionInfo> selectedCells)
        {
            var result = new StringBuilder();

            // 按行分组
            var lineGroups = GroupCellsByLines(selectedCells);

            for (int i = 0; i < lineGroups.Count; i++)
            {
                var line = lineGroups[i];

                // 按X坐标排序（从左到右）
                var sortedLine = line.OrderBy(c => c.Cell.location.Rectangle.Left).ToList();

                for (int j = 0; j < sortedLine.Count; j++)
                {
                    var cellInfo = sortedLine[j];
                    var cell = cellInfo.Cell;

                    if (!string.IsNullOrEmpty(cell.words))
                    {
                        // 提取选中的字符
                        int startChar = Math.Max(0, cellInfo.StartChar);
                        int endChar = Math.Min(cell.words.Length - 1, cellInfo.EndChar);

                        if (startChar <= endChar)
                        {
                            string selectedText = cell.words.Substring(startChar, endChar - startChar + 1);
                            result.Append(selectedText);
                        }
                    }

                    // 添加cell间的空格
                    if (j < sortedLine.Count - 1)
                    {
                        var currentCell = cellInfo.Cell;
                        var nextCell = sortedLine[j + 1].Cell;

                        if (ShouldAddSpaceBetweenCells(currentCell, nextCell))
                        {
                            result.Append(" ");
                        }
                    }
                }

                // 添加行间的换行符
                if (i < lineGroups.Count - 1)
                {
                    result.AppendLine();
                }
            }

            return result.ToString();
        }

        /// <summary>
        /// 触发文字选择变化事件
        /// </summary>
        private void OnTextSelectionChanged()
        {
            var selectedText = GetSelectedText();

            // 获取选中的cells
            var selectedCells = new List<TextCellInfo>();
            if (HasSelection())
            {
                selectedCells.AddRange(GetSelectedCells());
            }

            TextSelectionChanged?.Invoke(this, new TextSelectionEventArgs(selectedCells)
            {
                SelectedText = selectedText,
                SelectedCharacterCount = selectedText.Length
            });
        }

        #endregion

        #region 辅助数据结构

        /// <summary>
        /// 文字区域和字符位置
        /// </summary>
        private class CellCharPosition
        {
            public TextCellInfo Cell { get; }
            public int CharIndex { get; }

            public CellCharPosition(TextCellInfo cell, int charIndex)
            {
                Cell = cell;
                CharIndex = charIndex;
            }
        }

        /// <summary>
        /// 候选文字区域
        /// </summary>
        private class CellCandidate
        {
            public TextCellInfo Cell { get; set; }
            public double WeightedDistance { get; set; }
            public double RawDistance { get; set; }
        }

        /// <summary>
        /// Cell选择信息
        /// </summary>
        private class CellSelectionInfo
        {
            public TextCellInfo Cell { get; set; }
            public int StartChar { get; set; }
            public int EndChar { get; set; }

            public CellSelectionInfo(TextCellInfo cell, int startChar, int endChar)
            {
                Cell = cell;
                StartChar = startChar;
                EndChar = endChar;
            }
        }

        /// <summary>
        /// 选择配置
        /// </summary>
        private class SelectionConfig
        {
            public double DistanceDiffThreshold { get; set; } = 0.2;
            public double OverlapThreshold { get; set; } = 0.3;
            public double BottomDetectionRatio { get; set; } = 0.5; // 平均cell高度的50%
            public double WeightMultiplierBase { get; set; } = 2.0; // 基础权重，可根据布局调整
        }

        private readonly SelectionConfig config = new SelectionConfig();

        /// <summary>
        /// 文字测量结果
        /// </summary>
        private class TextMeasurementResult
        {
            public bool Success { get; set; }
            public List<float> CharWidths { get; set; } = new List<float>();
            public float TotalWidth { get; set; }
            public float FontSize { get; set; }
            public string FontName { get; set; }
        }

        /// <summary>
        /// 测量缓存键
        /// </summary>
        private class MeasurementCacheKey
        {
            public string Text { get; set; }
            public int CellWidth { get; set; }
            public int CellHeight { get; set; }

            public override bool Equals(object obj)
            {
                if (obj is MeasurementCacheKey other)
                {
                    return Text == other.Text && CellWidth == other.CellWidth && CellHeight == other.CellHeight;
                }
                return false;
            }

            public override int GetHashCode()
            {
                unchecked
                {
                    int hash = 17;
                    hash = hash * 23 + (Text?.GetHashCode() ?? 0);
                    hash = hash * 23 + CellWidth.GetHashCode();
                    hash = hash * 23 + CellHeight.GetHashCode();
                    return hash;
                }
            }
        }

        /// <summary>
        /// 文字方向枚举
        /// </summary>
        private enum TextDirection
        {
            Horizontal,
            Vertical
        }

        #endregion

        #region 空间路径选择算法

        /// <summary>
        /// 基于空间路径的选择
        /// </summary>
        private List<TextCellInfo> GetSpatialPathSelection()
        {
            // 确定逻辑起点和终点（按阅读顺序）
            var logicalStart = IsTextBefore(_startCell, _endCell) ? _startCell : _endCell;
            var logicalEnd = IsTextBefore(_startCell, _endCell) ? _endCell : _startCell;

            // 获取起点到终点的空间路径
            var pathCells = GetCellsInSpatialPath(logicalStart, logicalEnd);

            return pathCells;
        }

        /// <summary>
        /// 获取从起点到终点的空间路径上的cells
        /// </summary>
        private List<TextCellInfo> GetCellsInSpatialPath(TextCellInfo logicalStart, TextCellInfo logicalEnd)
        {
            var result = new List<TextCellInfo>();

            // 找到在选择路径上的cells
            var candidateCells = _textCells.Where(cell =>
                cell?.location != null &&
                !string.IsNullOrEmpty(cell.words)
            ).ToList();

            // 不使用简单的索引范围，而是检查所有候选cells的空间位置关系
            foreach (var cell in candidateCells)
            {
                if (IsCellInSelectionPath(cell, logicalStart, logicalEnd))
                {
                    result.Add(cell);
                }
            }

            return result;
        }

        /// <summary>
        /// 判断cell是否在选择路径上
        /// </summary>
        private bool IsCellInSelectionPath(TextCellInfo cell, TextCellInfo logicalStart, TextCellInfo logicalEnd)
        {
            // 如果是起点或终点，直接包含
            if (cell == logicalStart || cell == logicalEnd)
                return true;

            // 检测文字方向
            bool isStartVertical = DetectCellDirection(logicalStart);
            bool isEndVertical = DetectCellDirection(logicalEnd);
            bool isCellVertical = DetectCellDirection(cell);

            // 如果起点和终点的方向不一致，或者当前cell的方向与它们不一致，则不包含
            if (isStartVertical != isEndVertical || isCellVertical != isStartVertical)
                return false;

            if (isCellVertical)
            {
                // 竖排文字：使用列逻辑
                return IsCellInVerticalSelectionPath(cell, logicalStart, logicalEnd);
            }
            else
            {
                // 横排文字：使用行逻辑
                return IsCellInHorizontalSelectionPath(cell, logicalStart, logicalEnd);
            }
        }

        /// <summary>
        /// 判断文字前后关系
        /// </summary>
        private bool IsTextBefore(TextCellInfo cell1, TextCellInfo cell2)
        {
            if (cell1 == null || cell2 == null) return false;

            var rect1 = cell1.location.Rectangle;
            var rect2 = cell2.location.Rectangle;

            bool isVertical1 = DetectCellDirection(cell1);
            bool isVertical2 = DetectCellDirection(cell2);

            // 如果方向不同，按位置判断
            if (isVertical1 != isVertical2)
            {
                // 优先按Y坐标，再按X坐标
                if (Math.Abs(rect1.Top - rect2.Top) > 10)
                    return rect1.Top < rect2.Top;
                return rect1.Left < rect2.Left;
            }

            if (isVertical1)
            {
                // 垂直文字：从右到左，从上到下
                if (Math.Abs(rect1.Right - rect2.Right) > 10)
                    return rect1.Right > rect2.Right; // 右边的在前
                return rect1.Top < rect2.Top;
            }
            else
            {
                // 水平文字：从左到右，从上到下
                if (Math.Abs(rect1.Top - rect2.Top) > 10)
                    return rect1.Top < rect2.Top;
                return rect1.Left < rect2.Left;
            }
        }

        #endregion

        #region 垂直和水平选择路径判断

        /// <summary>
        /// 判断cell是否在竖排文字的选择路径上
        /// </summary>
        private bool IsCellInVerticalSelectionPath(TextCellInfo cell, TextCellInfo logicalStart, TextCellInfo logicalEnd)
        {
            var cellRect = cell.location.Rectangle;
            var startRect = logicalStart.location.Rectangle;
            var endRect = logicalEnd.location.Rectangle;

            // 竖排文字的选择逻辑：基于X坐标（列）
            // 起点在右，终点在左（竖排阅读顺序：从右到左）

            // 检查cell是否在起点和终点形成的X范围内
            var minX = Math.Min(startRect.Left, endRect.Left);
            var maxX = Math.Max(startRect.Right, endRect.Right);

            // cell必须在X范围内
            if (cellRect.Right < minX || cellRect.Left > maxX)
                return false;

            // 对于中间列的cells，需要更精确的判断
            var startColLeft = startRect.Left;
            var startColRight = startRect.Right;
            var endColLeft = endRect.Left;
            var endColRight = endRect.Right;

            // 如果cell在起始列
            if (cellRect.Right >= startColLeft && cellRect.Left <= startColRight)
            {
                return IsInStartColPath(cell, cellRect, startRect, logicalStart);
            }

            // 如果cell在结束列
            if (cellRect.Right >= endColLeft && cellRect.Left <= endColRight)
            {
                return IsInEndColPath(cell, cellRect, endRect, logicalEnd);
            }

            // 如果cell在中间列，直接包含
            return cellRect.Left > Math.Min(startColRight, endColRight) &&
                   cellRect.Right < Math.Max(startColLeft, endColLeft);
        }

        /// <summary>
        /// 判断cell是否在横排文字的选择路径上
        /// </summary>
        private bool IsCellInHorizontalSelectionPath(TextCellInfo cell, TextCellInfo logicalStart, TextCellInfo logicalEnd)
        {
            var cellRect = cell.location.Rectangle;
            var startRect = logicalStart.location.Rectangle;
            var endRect = logicalEnd.location.Rectangle;

            // 横排文字的选择逻辑：基于Y坐标（行）
            var minY = Math.Min(startRect.Top, endRect.Top);
            var maxY = Math.Max(startRect.Bottom, endRect.Bottom);

            // 检查cell是否在Y范围内，允许一定的重叠容差
            if (!HasSignificantOverlap(cellRect, minY, maxY))
                return false;

            // 对于在Y范围内的cells，检查X坐标关系
            var startRowTop = startRect.Top;
            var startRowBottom = startRect.Bottom;
            var endRowTop = endRect.Top;
            var endRowBottom = endRect.Bottom;

            // 如果cell在起始行
            if (HasSignificantOverlap(cellRect, startRowTop, startRowBottom))
            {
                return IsInStartRowPath(cell, cellRect, startRect, logicalStart);
            }

            // 如果cell在结束行
            if (HasSignificantOverlap(cellRect, endRowTop, endRowBottom))
            {
                return IsInEndRowPath(cell, cellRect, endRect, logicalEnd);
            }

            // 如果cell在中间行，直接包含
            return cellRect.Top > Math.Min(startRowBottom, endRowBottom) &&
                   cellRect.Bottom < Math.Max(startRowTop, endRowTop);
        }

        /// <summary>
        /// 检查是否有显著重叠
        /// </summary>
        private bool HasSignificantOverlap(Rectangle cellRect, int rangeMinY, int rangeMaxY)
        {
            var overlapTop = Math.Max(cellRect.Top, rangeMinY);
            var overlapBottom = Math.Min(cellRect.Bottom, rangeMaxY);
            var overlapHeight = overlapBottom - overlapTop;

            // 至少有30%的重叠才认为是显著重叠
            var cellHeight = cellRect.Height;
            return overlapHeight > 0 && (double)overlapHeight / cellHeight >= config.OverlapThreshold;
        }

        #endregion

        #region 路径判断辅助方法

        /// <summary>
        /// 判断cell是否在起始列路径上
        /// </summary>
        private bool IsInStartColPath(TextCellInfo cell, Rectangle cellRect, Rectangle startRect, TextCellInfo logicalStart)
        {
            // 在起始列中，只选择从起始cell开始向下的部分
            return cellRect.Top >= startRect.Top;
        }

        /// <summary>
        /// 判断cell是否在结束列路径上
        /// </summary>
        private bool IsInEndColPath(TextCellInfo cell, Rectangle cellRect, Rectangle endRect, TextCellInfo logicalEnd)
        {
            // 在结束列中，只选择到结束cell为止的部分
            return cellRect.Bottom <= endRect.Bottom;
        }

        /// <summary>
        /// 判断cell是否在起始行路径上
        /// </summary>
        private bool IsInStartRowPath(TextCellInfo cell, Rectangle cellRect, Rectangle startRect, TextCellInfo logicalStart)
        {
            // 在起始行中，只选择从起始cell开始向右的部分
            return cellRect.Left >= startRect.Left;
        }

        /// <summary>
        /// 判断cell是否在结束行路径上
        /// </summary>
        private bool IsInEndRowPath(TextCellInfo cell, Rectangle cellRect, Rectangle endRect, TextCellInfo logicalEnd)
        {
            // 在结束行中，只选择到结束cell为止的部分
            return cellRect.Right <= endRect.Right;
        }

        #endregion

        #region 字符范围计算

        /// <summary>
        /// 计算字符选择范围
        /// </summary>
        private List<CellSelectionInfo> CalculateCharacterRangesLogical(List<TextCellInfo> selectedCells)
        {
            var result = new List<CellSelectionInfo>();

            if (selectedCells == null || selectedCells.Count == 0)
                return result;

            // 按阅读顺序排序
            var sortedCells = SortCellsByReadingOrder(selectedCells);

            foreach (var cell in sortedCells)
            {
                if (cell == null || string.IsNullOrEmpty(cell.words))
                    continue;

                int startChar = 0;
                int endChar = cell.words.Length - 1;

                // 如果是起始cell，使用起始字符索引
                if (cell == _startCell)
                {
                    startChar = Math.Max(0, Math.Min(_startCharIndex, cell.words.Length - 1));
                }

                // 如果是结束cell，使用结束字符索引
                if (cell == _endCell)
                {
                    endChar = Math.Max(0, Math.Min(_endCharIndex, cell.words.Length - 1));
                }

                // 确保起点不大于终点
                if (startChar > endChar)
                {
                    var temp = startChar;
                    startChar = endChar;
                    endChar = temp;
                }

                result.Add(new CellSelectionInfo(cell, startChar, endChar));
            }

            return result;
        }

        /// <summary>
        /// 按阅读顺序排序cells
        /// </summary>
        private List<TextCellInfo> SortCellsByReadingOrder(List<TextCellInfo> cells)
        {
            if (cells == null || cells.Count == 0)
                return new List<TextCellInfo>();

            // 检测主要的文字方向
            bool isMostlyVertical = cells.Count(c => DetectCellDirection(c)) > cells.Count / 2;

            if (isMostlyVertical)
            {
                // 垂直文字：从右到左，从上到下
                return cells.OrderByDescending(c => c.location.Rectangle.Right)
                           .ThenBy(c => c.location.Rectangle.Top)
                           .ToList();
            }
            else
            {
                // 水平文字：从上到下，从左到右
                return cells.OrderBy(c => c.location.Rectangle.Top)
                           .ThenBy(c => c.location.Rectangle.Left)
                           .ToList();
            }
        }

        #endregion

        #region 文字分组和处理方法

        /// <summary>
        /// 获取垂直文字
        /// </summary>
        private string GetVerticalText(List<CellSelectionInfo> selectedCells)
        {
            var result = new StringBuilder();

            // 按列分组
            var columnGroups = GroupCellsByColumns(selectedCells);

            for (int i = 0; i < columnGroups.Count; i++)
            {
                var column = columnGroups[i];

                // 按Y坐标排序（从上到下）
                var sortedColumn = column.OrderBy(c => c.Cell.location.Rectangle.Top).ToList();

                for (int j = 0; j < sortedColumn.Count; j++)
                {
                    var cellInfo = sortedColumn[j];
                    var cell = cellInfo.Cell;

                    if (!string.IsNullOrEmpty(cell.words))
                    {
                        // 提取选中的字符
                        int startChar = Math.Max(0, cellInfo.StartChar);
                        int endChar = Math.Min(cell.words.Length - 1, cellInfo.EndChar);

                        if (startChar <= endChar)
                        {
                            string selectedText = cell.words.Substring(startChar, endChar - startChar + 1);
                            result.Append(selectedText);
                        }
                    }

                    // 添加cell间的空格
                    if (j < sortedColumn.Count - 1)
                    {
                        var currentCell = cellInfo.Cell;
                        var nextCell = sortedColumn[j + 1].Cell;

                        if (ShouldAddSpaceBetweenVerticalCells(currentCell, nextCell))
                        {
                            result.Append(" ");
                        }
                    }
                }

                // 添加列间的换行符
                if (i < columnGroups.Count - 1)
                {
                    result.AppendLine();
                }
            }

            return result.ToString();
        }

        /// <summary>
        /// 按行分组cells
        /// </summary>
        private List<List<CellSelectionInfo>> GroupCellsByLines(List<CellSelectionInfo> selectedCells)
        {
            var groups = new List<List<CellSelectionInfo>>();

            foreach (var cellInfo in selectedCells)
            {
                bool addedToGroup = false;

                foreach (var group in groups)
                {
                    if (group.Any(c => IsInSameLine(c.Cell, cellInfo.Cell)))
                    {
                        group.Add(cellInfo);
                        addedToGroup = true;
                        break;
                    }
                }

                if (!addedToGroup)
                {
                    groups.Add(new List<CellSelectionInfo> { cellInfo });
                }
            }

            // 按Y坐标排序组
            return groups.OrderBy(g => g.Min(c => c.Cell.location.Rectangle.Top)).ToList();
        }

        /// <summary>
        /// 按列分组cells
        /// </summary>
        private List<List<CellSelectionInfo>> GroupCellsByColumns(List<CellSelectionInfo> selectedCells)
        {
            var groups = new List<List<CellSelectionInfo>>();

            foreach (var cellInfo in selectedCells)
            {
                bool addedToGroup = false;

                foreach (var group in groups)
                {
                    if (group.Any(c => IsInSameColumn(c.Cell, cellInfo.Cell)))
                    {
                        group.Add(cellInfo);
                        addedToGroup = true;
                        break;
                    }
                }

                if (!addedToGroup)
                {
                    groups.Add(new List<CellSelectionInfo> { cellInfo });
                }
            }

            // 按X坐标排序组（从右到左，垂直文字的阅读顺序）
            return groups.OrderByDescending(g => g.Max(c => c.Cell.location.Rectangle.Right)).ToList();
        }

        #endregion

        #region 辅助判断方法

        /// <summary>
        /// 判断两个cell是否在同一行
        /// </summary>
        private bool IsInSameLine(TextCellInfo cell1, TextCellInfo cell2)
        {
            if (cell1 == null || cell2 == null) return false;

            var rect1 = cell1.location.Rectangle;
            var rect2 = cell2.location.Rectangle;

            // 检查Y坐标重叠
            var overlapTop = Math.Max(rect1.Top, rect2.Top);
            var overlapBottom = Math.Min(rect1.Bottom, rect2.Bottom);
            var overlapHeight = overlapBottom - overlapTop;

            // 至少有50%的高度重叠才认为在同一行
            var minHeight = Math.Min(rect1.Height, rect2.Height);
            return overlapHeight > 0 && (double)overlapHeight / minHeight >= 0.5;
        }

        /// <summary>
        /// 判断两个cell是否在同一列
        /// </summary>
        private bool IsInSameColumn(TextCellInfo cell1, TextCellInfo cell2)
        {
            if (cell1 == null || cell2 == null) return false;

            var rect1 = cell1.location.Rectangle;
            var rect2 = cell2.location.Rectangle;

            // 检查X坐标重叠
            var overlapLeft = Math.Max(rect1.Left, rect2.Left);
            var overlapRight = Math.Min(rect1.Right, rect2.Right);
            var overlapWidth = overlapRight - overlapLeft;

            // 至少有50%的宽度重叠才认为在同一列
            var minWidth = Math.Min(rect1.Width, rect2.Width);
            return overlapWidth > 0 && (double)overlapWidth / minWidth >= 0.5;
        }

        /// <summary>
        /// 判断是否应该在cells间添加空格
        /// </summary>
        private bool ShouldAddSpaceBetweenCells(TextCellInfo currentCell, TextCellInfo nextCell)
        {
            if (currentCell == null || nextCell == null) return false;

            var currentRect = currentCell.location.Rectangle;
            var nextRect = nextCell.location.Rectangle;

            // 计算水平距离
            var distance = nextRect.Left - currentRect.Right;

            // 如果距离大于当前cell宽度的20%，添加空格
            return distance > currentRect.Width * 0.2;
        }

        /// <summary>
        /// 判断是否应该在垂直cells间添加空格
        /// </summary>
        private bool ShouldAddSpaceBetweenVerticalCells(TextCellInfo currentCell, TextCellInfo nextCell)
        {
            if (currentCell == null || nextCell == null) return false;

            var currentRect = currentCell.location.Rectangle;
            var nextRect = nextCell.location.Rectangle;

            // 计算垂直距离
            var distance = nextRect.Top - currentRect.Bottom;

            // 如果距离大于当前cell高度的20%，添加空格
            return distance > currentRect.Height * 0.2;
        }

        /// <summary>
        /// 获取选中文字内容
        /// </summary>
        public string GetSelectedTextContent()
        {
            return GetSelectedText();
        }

        #endregion

        #region 字体测量方法

        /// <summary>
        /// 字体测量缓存
        /// </summary>
        private readonly Dictionary<MeasurementCacheKey, TextMeasurementResult> measurementCache =
            new Dictionary<MeasurementCacheKey, TextMeasurementResult>();
        private const int MAX_CACHE_SIZE = 100;

        /// <summary>
        /// 智能文本测量（带缓存的自动校准字体大小）
        /// </summary>
        private TextMeasurementResult MeasureTextWithCalibration(string text, Rectangle cellRect)
        {
            // 检查缓存
            var cacheKey = new MeasurementCacheKey
            {
                Text = text,
                CellWidth = cellRect.Width,
                CellHeight = cellRect.Height
            };

            if (measurementCache.TryGetValue(cacheKey, out var cachedResult))
            {
                return cachedResult;
            }

            var result = new TextMeasurementResult();

            // 性能优化：对于短文本（<=3字符）使用简化策略
            if (text.Length <= 3)
            {
                result = MeasureTextSimple(text, cellRect);
            }
            else
            {
                // 对于长文本使用完整的校准策略
                result = MeasureTextWithFullCalibration(text, cellRect);
            }

            // 缓存结果（限制缓存大小）
            if (measurementCache.Count >= MAX_CACHE_SIZE)
            {
                // 清除一半的缓存
                var keysToRemove = measurementCache.Keys.Take(MAX_CACHE_SIZE / 2).ToList();
                foreach (var key in keysToRemove)
                {
                    measurementCache.Remove(key);
                }
            }

            measurementCache[cacheKey] = result;
            return result;
        }

        /// <summary>
        /// 简化的文本测量（用于短文本）
        /// </summary>
        private TextMeasurementResult MeasureTextSimple(string text, Rectangle cellRect)
        {
            float baseFontSize = EstimateFontSize(cellRect);

            // 只尝试3个字体大小
            float[] fontSizeMultipliers = { 0.9f, 1.0f, 1.1f };

            foreach (float multiplier in fontSizeMultipliers)
            {
                float testFontSize = baseFontSize * multiplier;
                var testResult = MeasureTextAtFontSize(text, testFontSize);

                if (testResult.Success)
                {
                    return testResult;
                }
            }

            return new TextMeasurementResult();
        }

        /// <summary>
        /// 完整的文本测量校准（用于长文本）
        /// </summary>
        private TextMeasurementResult MeasureTextWithFullCalibration(string text, Rectangle cellRect)
        {
            float baseFontSize = EstimateFontSize(cellRect);
            float[] fontSizeMultipliers = { 0.8f, 0.9f, 1.0f, 1.1f, 1.2f };

            float bestScore = float.MaxValue;
            TextMeasurementResult bestResult = null;

            foreach (float multiplier in fontSizeMultipliers)
            {
                float testFontSize = baseFontSize * multiplier;
                var testResult = MeasureTextAtFontSize(text, testFontSize);

                if (testResult.Success)
                {
                    // 计算与目标宽度的偏差
                    float deviation = Math.Abs(testResult.TotalWidth - cellRect.Width) / cellRect.Width;

                    if (deviation < bestScore)
                    {
                        bestScore = deviation;
                        bestResult = testResult;
                    }

                    // 如果偏差很小，直接使用
                    if (deviation < 0.15f) // 偏差小于15%
                    {
                        break;
                    }
                }
            }

            return bestResult ?? new TextMeasurementResult();
        }

        /// <summary>
        /// 在指定字体大小下测量文本（优化版本）
        /// </summary>
        private TextMeasurementResult MeasureTextAtFontSize(string text, float fontSize)
        {
            var result = new TextMeasurementResult { FontSize = fontSize };

            try
            {
                // 优先使用最常见的字体，减少尝试次数
                string[] fontNames = { "Microsoft YaHei", "SimSun", "Arial" };

                foreach (string fontName in fontNames)
                {
                    try
                    {
                        using (var font = new Font(fontName, fontSize))
                        using (var bitmap = new Bitmap(1, 1))
                        using (var g = Graphics.FromImage(bitmap))
                        {
                            g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.AntiAlias;

                            var charWidths = new List<float>(text.Length);
                            float totalWidth = 0;

                            // 使用固定的StringFormat，避免重复创建
                            var format = StringFormat.GenericTypographic;

                            foreach (char c in text)
                            {
                                var size = g.MeasureString(c.ToString(), font, PointF.Empty, format);
                                if (size.Width <= 0)
                                {
                                    goto NextFont; // 快速跳出到下一个字体
                                }
                                charWidths.Add(size.Width);
                                totalWidth += size.Width;
                            }

                            // 所有字符测量成功
                            result.Success = true;
                            result.CharWidths = charWidths;
                            result.TotalWidth = totalWidth;
                            result.FontName = fontName;
                            return result;

                            NextFont:;
                        }
                    }
                    catch
                    {
                        continue;
                    }
                }
            }
            catch
            {
                // 测量失败
            }

            return result;
        }

        /// <summary>
        /// 估算字体大小
        /// </summary>
        private float EstimateFontSize(Rectangle cellRect)
        {
            // 基础估算：字体大小通常是cell高度的70-90%
            float baseFontSize = Math.Max(8, Math.Min(cellRect.Height * 0.8f, 72));

            // 根据cell的宽高比进行调整
            float aspectRatio = (float)cellRect.Width / cellRect.Height;

            // 如果cell很宽（横排长文本），字体可能稍小
            if (aspectRatio > 5.0f)
            {
                baseFontSize *= 0.9f;
            }
            // 如果cell接近正方形（单字符），字体可能稍大
            else if (aspectRatio >= 0.8f && aspectRatio <= 1.2f)
            {
                baseFontSize *= 1.1f;
            }

            return Math.Max(8, Math.Min(baseFontSize, 72));
        }

        /// <summary>
        /// 测量单个字符的实际高度（用于竖排文字）
        /// </summary>
        private float MeasureCharHeight(char c, float fontSize)
        {
            try
            {
                string[] fontNames = { "Microsoft YaHei", "SimSun", "SimHei", "Arial" };

                foreach (string fontName in fontNames)
                {
                    try
                    {
                        using (var font = new Font(fontName, fontSize))
                        using (var bitmap = new Bitmap(1, 1))
                        using (var g = Graphics.FromImage(bitmap))
                        {
                            g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.AntiAlias;
                            var size = g.MeasureString(c.ToString(), font, PointF.Empty, StringFormat.GenericTypographic);
                            if (size.Height > 0)
                            {
                                return size.Height;
                            }
                        }
                    }
                    catch
                    {
                        continue;
                    }
                }
            }
            catch
            {
                // 所有字体都失败
            }

            // 托底：返回-1表示测量失败
            return -1;
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 鼠标按下事件处理
        /// </summary>
        private void OnMouseDown(object sender, MouseEventArgs e)
        {
            HandleMouseDown(e);
        }

        /// <summary>
        /// 鼠标移动事件处理
        /// </summary>
        private void OnMouseMove(object sender, MouseEventArgs e)
        {
            HandleMouseMove(e);
        }

        /// <summary>
        /// 鼠标释放事件处理
        /// </summary>
        private void OnMouseUp(object sender, MouseEventArgs e)
        {
            HandleMouseUp(e);
        }

        /// <summary>
        /// 鼠标离开事件处理
        /// </summary>
        private void OnMouseLeave(object sender, EventArgs e)
        {
            HandleMouseLeave(e);
        }

        /// <summary>
        /// 自定义绘制事件处理
        /// </summary>
        private void OnCustomPaint(object sender, PaintEventArgs e)
        {
            HandlePaint(e);
        }

        #endregion
    }
}
