﻿using System;
using System.Drawing;
using System.ComponentModel;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Forms;

using MetroFramework.Components;
using MetroFramework.Design;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using MetroFramework.Native;

namespace MetroFramework.Controls
{
    public enum ScrollBarOrientation
    {
        Horizontal,
        Vertical
    }

    internal enum ScrollBarState
    {
        Normal,
        Hot,
        Active,
        Pressed,
        Disabled
    }

    [Designer(typeof(MetroScrollBarDesigner))]
    [DefaultEvent("Scroll")]
    [DefaultProperty("Value")]
    public class MetroScrollBar : Control, IMetroControl
    {
        #region Interface

        private MetroColorStyle metroStyle = MetroColorStyle.Blue;
        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                if (StyleManager != null)
                    return StyleManager.Style;

                return metroStyle;
            }
            set { metroStyle = value; }
        }

        private MetroThemeStyle metroTheme = MetroThemeStyle.Light;
        [Category("Metro Appearance")]
        public MetroThemeStyle Theme
        {
            get
            {
                if (StyleManager != null)
                    return StyleManager.Theme;

                return metroTheme;
            }
            set { metroTheme = value; }
        }

        private MetroStyleManager metroStyleManager = null;
        [Browsable(false)]
        public MetroStyleManager StyleManager
        {
            get { return metroStyleManager; }
            set { metroStyleManager = value; }
        }

        #endregion

        #region Events

        public event ScrollEventHandler Scroll;
        private void OnScroll(ScrollEventType type, int newValue)
        {
            if (Scroll != null)
                Scroll(this, new ScrollEventArgs(type, newValue));
        }
        private void OnScroll(ScrollEventType type, int oldValue, int newValue)
        {
            if (Scroll != null)
                Scroll(this, new ScrollEventArgs(type, oldValue, newValue));
        }
        private void OnScroll(ScrollEventType type, int oldValue, int newValue, ScrollOrientation orientation)
        {
            if (Scroll != null)
                Scroll(this, new ScrollEventArgs(type, oldValue, newValue, orientation));
        }

        #endregion

        #region Fields

        private const int SETREDRAW = 11;

        private bool inUpdate;

        private ScrollBarOrientation orientation = ScrollBarOrientation.Vertical;
        private ScrollOrientation scrollOrientation = ScrollOrientation.VerticalScroll;

        private Rectangle clickedBarRectangle;
        private Rectangle thumbRectangle;
        private Rectangle channelRectangle;

        private bool topBarClicked;
        private bool bottomBarClicked;
        private bool thumbClicked;

        private ScrollBarState thumbState = ScrollBarState.Normal;

        private int minimum;
        private int maximum = 100;
        private int smallChange = 1;
        private int largeChange = 10;
        private int value;

        private int thumbWidth = 6;
        private int thumbHeight;

        private int thumbBottomLimitBottom;
        private int thumbBottomLimitTop;
        private int thumbTopLimit;
        private int thumbPosition;

        private int trackPosition;

        private Timer progressTimer = new Timer();

        #endregion

        #region Properties

        public ScrollBarOrientation Orientation
        {
            get
            {
                return this.orientation;
            }

            set
            {
                if (value == this.orientation)
                {
                    return;
                }

                this.orientation = value;

                this.scrollOrientation = value == ScrollBarOrientation.Vertical ?
                   ScrollOrientation.VerticalScroll : ScrollOrientation.HorizontalScroll;

                if (this.DesignMode)
                {
                    this.Size = new Size(this.Height, this.Width);
                }

                this.SetUpScrollBar();
            }
        }

        public int Minimum
        {
            get
            {
                return this.minimum;
            }

            set
            {
                if (this.minimum == value || value < 0 || value >= this.maximum)
                {
                    return;
                }

                this.minimum = value;
                if (this.value < value)
                {
                    this.value = value;
                }

                if (this.largeChange > this.maximum - this.minimum)
                {
                    this.largeChange = this.maximum - this.minimum;
                }

                this.SetUpScrollBar();

                if (this.value < value)
                {
                    this.Value = value;
                }
                else
                {
                    this.ChangeThumbPosition(this.GetThumbPosition());
                    this.Refresh();
                }
            }
        }

        public int Maximum
        {
            get
            {
                return this.maximum;
            }

            set
            {
                if (value == this.maximum || value < 1 || value <= this.minimum)
                {
                    return;
                }

                this.maximum = value;

                if (this.largeChange > this.maximum - this.minimum)
                {
                    this.largeChange = this.maximum - this.minimum;
                }

                this.SetUpScrollBar();

                if (this.value > value)
                {
                    this.Value = this.maximum;
                }
                else
                {
                    this.ChangeThumbPosition(this.GetThumbPosition());

                    this.Refresh();
                }
            }
        }

        public int SmallChange
        {
            get
            {
                return this.smallChange;
            }

            set
            {
                if (value == this.smallChange || value < 1 || value >= this.largeChange)
                {
                    return;
                }

                this.smallChange = value;

                this.SetUpScrollBar();
            }
        }

        public int LargeChange
        {
            get
            {
                return this.largeChange;
            }

            set
            {
                if (value == this.largeChange || value < this.smallChange || value < 2)
                {
                    return;
                }

                if (value > this.maximum - this.minimum)
                {
                    this.largeChange = this.maximum - this.minimum;
                }
                else
                {
                    this.largeChange = value;
                }

                this.SetUpScrollBar();
            }
        }

        public int Value
        {
            get
            {
                return this.value;
            }

            set
            {
                if (this.value == value || value < this.minimum || value > this.maximum)
                {
                    return;
                }

                this.value = value;

                this.ChangeThumbPosition(this.GetThumbPosition());

                this.OnScroll(ScrollEventType.ThumbPosition, -1, this.value, this.scrollOrientation);

                this.Refresh();
            }
        }

        #endregion

        #region Constructor

        public MetroScrollBar()
        {
            SetStyle(ControlStyles.OptimizedDoubleBuffer | 
                     ControlStyles.ResizeRedraw |
                     ControlStyles.Selectable | 
                     ControlStyles.AllPaintingInWmPaint |
                     ControlStyles.UserPaint, true);

            Width = 6;
            Height = 200;

            SetUpScrollBar();

            this.progressTimer.Interval = 20;
            this.progressTimer.Tick += this.ProgressTimerTick;
        }

        #endregion

        #region Update Methods

        public void BeginUpdate()
        {
            WinApi.SendMessage(this.Handle, SETREDRAW, false, 0);
            this.inUpdate = true;
        }

        public void EndUpdate()
        {
            WinApi.SendMessage(this.Handle, SETREDRAW, true, 0);
            this.inUpdate = false;
            this.SetUpScrollBar();
            this.Refresh();
        }

        #endregion

        #region Paint Methods

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            // no painting here
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            Color backColor, thumbColor, barColor;

            if (Parent != null)
                backColor = Parent.BackColor;
            else
                backColor = MetroPaint.BackColor.Form(Theme);

            if (isHovered && !isPressed && Enabled)
            {
                thumbColor = MetroPaint.BackColor.ScrollBar.Thumb.Hover(Theme);
                barColor = MetroPaint.BackColor.ScrollBar.Bar.Hover(Theme);
            }
            else if (isHovered && isPressed && Enabled)
            {
                thumbColor = MetroPaint.BackColor.ScrollBar.Thumb.Press(Theme);
                barColor = MetroPaint.BackColor.ScrollBar.Bar.Press(Theme);
            }
            else if (!Enabled)
            {
                thumbColor = MetroPaint.BackColor.ScrollBar.Thumb.Disabled(Theme);
                barColor = MetroPaint.BackColor.ScrollBar.Bar.Disabled(Theme);
            }
            else
            {
                thumbColor = MetroPaint.BackColor.ScrollBar.Thumb.Normal(Theme);
                barColor = MetroPaint.BackColor.ScrollBar.Bar.Normal(Theme);
            }

            e.Graphics.Clear(backColor);
            DrawTrackBar(e.Graphics, thumbColor, barColor);

            e.Graphics.Clear(Color.White);
            e.Graphics.FillRectangle(new SolidBrush(Color.Red), ClientRectangle);
            e.Graphics.FillRectangle(new SolidBrush(Color.Gray), thumbRectangle);
        }

        #endregion

        #region Mouse Methods

        protected override void OnMouseDown(MouseEventArgs e)
        {
            base.OnMouseDown(e);

            this.Focus();

            if (e.Button == MouseButtons.Left)
            {
                Point mouseLocation = e.Location;

                if (this.thumbRectangle.Contains(mouseLocation))
                {
                    this.thumbClicked = true;
                    this.thumbPosition = this.orientation == ScrollBarOrientation.Vertical ? mouseLocation.Y - thumbRectangle.Y : mouseLocation.X - thumbRectangle.X;
                    this.thumbState = ScrollBarState.Pressed;

                    Invalidate(thumbRectangle);
                }
                else
                {
                    this.trackPosition =
                       this.orientation == ScrollBarOrientation.Vertical ?
                          mouseLocation.Y : mouseLocation.X;

                    if (this.trackPosition <
                       (this.orientation == ScrollBarOrientation.Vertical ?
                          this.thumbRectangle.Y : this.thumbRectangle.X))
                    {
                        this.topBarClicked = true;
                    }
                    else
                    {
                        this.bottomBarClicked = true;
                    }

                    this.ProgressThumb(true);
                }
            }
            else if (e.Button == MouseButtons.Right)
            {
                this.trackPosition =
                   this.orientation == ScrollBarOrientation.Vertical ? e.Y : e.X;
            }
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            base.OnMouseUp(e);

            if (e.Button == MouseButtons.Left)
            {
                if (this.thumbClicked)
                {
                    this.thumbClicked = false;
                    this.thumbState = ScrollBarState.Normal;

                    this.OnScroll(
                       ScrollEventType.EndScroll,
                       -1,
                       this.value,
                       this.scrollOrientation
                    );
                }
                else if (this.topBarClicked)
                {
                    this.topBarClicked = false;
                    this.StopTimer();
                }
                else if (this.bottomBarClicked)
                {
                    this.bottomBarClicked = false;
                    this.StopTimer();
                }

                Invalidate();
            }
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            base.OnMouseEnter(e);

            this.thumbState = ScrollBarState.Active;

            Invalidate();
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            base.OnMouseLeave(e);

            this.ResetScrollStatus();
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            base.OnMouseMove(e);

            // moving and holding the left mouse button
            if (e.Button == MouseButtons.Left)
            {
                // Update the thumb position, if the new location is within the bounds.
                if (this.thumbClicked)
                {
                    int oldScrollValue = this.value;

                    int pos = this.orientation == ScrollBarOrientation.Vertical ?
                       e.Location.Y : e.Location.X;

                    // The thumb is all the way to the top
                    if (pos <= (this.thumbTopLimit + this.thumbPosition))
                    {
                        this.ChangeThumbPosition(this.thumbTopLimit);

                        this.value = this.minimum;
                        Invalidate();
                    }
                    else if (pos >= (this.thumbBottomLimitTop + this.thumbPosition))
                    {
                        // The thumb is all the way to the bottom
                        this.ChangeThumbPosition(this.thumbBottomLimitTop);

                        this.value = this.maximum;
                        Invalidate();
                    }
                    else
                    {
                        // The thumb is between the ends of the track.
                        this.ChangeThumbPosition(pos - this.thumbPosition);

                        int pixelRange, thumbPos;

                        // calculate the value - first some helper variables
                        // dependent on the current orientation
                        if (this.orientation == ScrollBarOrientation.Vertical)
                        {
                            pixelRange = this.Height - this.thumbHeight;
                            thumbPos = this.thumbRectangle.Y;
                        }
                        else
                        {
                            pixelRange = this.Width - this.thumbWidth;
                            thumbPos = this.thumbRectangle.X;
                        }

                        float perc = 0f;

                        if (pixelRange != 0)
                        {
                            // percent of the new position
                            perc = (float)(thumbPos) / (float)pixelRange;
                        }

                        // the new value is somewhere between max and min, starting
                        // at min position
                        this.value = Convert.ToInt32((perc * (this.maximum - this.minimum)) + this.minimum);
                    }

                    // raise scroll event if new value different
                    if (oldScrollValue != this.value)
                    {
                        this.OnScroll(ScrollEventType.ThumbTrack, oldScrollValue, this.value, this.scrollOrientation);

                        this.Refresh();
                    }
                }
            }
            else if (!this.ClientRectangle.Contains(e.Location))
            {
                this.ResetScrollStatus();
            }
            else if (e.Button == MouseButtons.None) // only moving the mouse
            {
                if (this.thumbRectangle.Contains(e.Location))
                {
                    this.thumbState = ScrollBarState.Hot;

                    this.Invalidate(this.thumbRectangle);
                }
                else if (this.ClientRectangle.Contains(e.Location))
                {
                    this.thumbState = ScrollBarState.Active;

                    Invalidate();
                }
            }
        }

        #endregion

        #region Management Methods

        protected override void SetBoundsCore(int x, int y, int width, int height, BoundsSpecified specified)
        {
            if (this.DesignMode)
            {
                if (this.orientation == ScrollBarOrientation.Vertical)
                {
                    if (height < 10)
                    {
                        height = 10;
                    }

                    width = 6;
                }
                else
                {
                    if (width < 10)
                    {
                        width = 10;
                    }

                    height = 6;
                }
            }

            base.SetBoundsCore(x, y, width, height, specified);

            if (this.DesignMode)
            {
                this.SetUpScrollBar();
            }
        }

        protected override void OnSizeChanged(EventArgs e)
        {
            base.OnSizeChanged(e);
            this.SetUpScrollBar();
        }

        protected override bool ProcessDialogKey(Keys keyData)
        {
            Keys keyUp = Keys.Up;
            Keys keyDown = Keys.Down;

            if (this.orientation == ScrollBarOrientation.Horizontal)
            {
                keyUp = Keys.Left;
                keyDown = Keys.Right;
            }

            if (keyData == keyUp)
            {
                this.Value -= this.smallChange;

                return true;
            }

            if (keyData == keyDown)
            {
                this.Value += this.smallChange;

                return true;
            }

            if (keyData == Keys.PageUp)
            {
                this.Value = this.GetValue(false, true);

                return true;
            }

            if (keyData == Keys.PageDown)
            {
                if (this.value + this.largeChange > this.maximum)
                {
                    this.Value = this.maximum;
                }
                else
                {
                    this.Value += this.largeChange;
                }

                return true;
            }

            if (keyData == Keys.Home)
            {
                this.Value = this.minimum;

                return true;
            }

            if (keyData == Keys.End)
            {
                this.Value = this.maximum;

                return true;
            }

            return base.ProcessDialogKey(keyData);
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);

            if (this.Enabled)
            {
                this.thumbState = ScrollBarState.Normal;
            }
            else
            {
                this.thumbState = ScrollBarState.Disabled;
            }

            this.Refresh();
        }

        private void SetUpScrollBar()
        {
            if (this.inUpdate)
            {
                return;
            }

            if (this.orientation == ScrollBarOrientation.Vertical)
            {
                this.thumbWidth = 6;
                this.thumbHeight = this.GetThumbSize();

                this.clickedBarRectangle = this.ClientRectangle;
                this.clickedBarRectangle.Inflate(-1, -1);

                this.channelRectangle = this.clickedBarRectangle;

                this.thumbRectangle = new Rectangle(
                   ClientRectangle.X,
                   ClientRectangle.Y,
                   this.thumbWidth,
                   this.thumbHeight
                );

                this.thumbPosition = this.thumbRectangle.Height / 2;
                this.thumbBottomLimitBottom = ClientRectangle.Bottom;
                this.thumbBottomLimitTop = this.thumbBottomLimitBottom - this.thumbRectangle.Height;
                this.thumbTopLimit = ClientRectangle.Y;
            }
            else
            {
                this.thumbHeight = 6;
                this.thumbWidth = this.GetThumbSize();

                this.clickedBarRectangle = this.ClientRectangle;
                this.clickedBarRectangle.Inflate(-1, -1);

                this.channelRectangle = this.clickedBarRectangle;

                this.thumbRectangle = new Rectangle(
                   ClientRectangle.X,
                   ClientRectangle.Y,
                   this.thumbWidth,
                   this.thumbHeight
                );

                this.thumbPosition = this.thumbRectangle.Width / 2;
                this.thumbBottomLimitBottom = ClientRectangle.Right;
                this.thumbBottomLimitTop = this.thumbBottomLimitBottom - this.thumbRectangle.Width;
                this.thumbTopLimit = ClientRectangle.X;
            }

            this.ChangeThumbPosition(this.GetThumbPosition());

            this.Refresh();
        }

        private void ResetScrollStatus()
        {
            Point pos = this.PointToClient(Cursor.Position);
            this.thumbState = this.thumbRectangle.Contains(pos) ? ScrollBarState.Hot : ScrollBarState.Normal;
            this.bottomBarClicked = this.topBarClicked = false;

            this.StopTimer();
            this.Refresh();
        }

        private void ProgressTimerTick(object sender, EventArgs e)
        {
            this.ProgressThumb(true);
        }

        private int GetValue(bool smallIncrement, bool up)
        {
            int newValue;

            if (up)
            {
                newValue = this.value - (smallIncrement ? this.smallChange : this.largeChange);

                if (newValue < this.minimum)
                {
                    newValue = this.minimum;
                }
            }
            else
            {
                newValue = this.value + (smallIncrement ? this.smallChange : this.largeChange);

                if (newValue > this.maximum)
                {
                    newValue = this.maximum;
                }
            }

            return newValue;
        }

        private int GetThumbPosition()
        {
            int pixelRange;

            if (this.orientation == ScrollBarOrientation.Vertical)
            {
                pixelRange = this.Height - this.thumbHeight;
            }
            else
            {
                pixelRange = this.Width - this.thumbWidth;
            }

            int realRange = this.maximum - this.minimum;
            float perc = 0f;

            if (realRange != 0)
            {
                perc = ((float)this.value - (float)this.minimum) / (float)realRange;
            }

            return Math.Max(this.thumbTopLimit, Math.Min(this.thumbBottomLimitTop, Convert.ToInt32((perc * pixelRange))));
        }

        private int GetThumbSize()
        {
            int trackSize =
               this.orientation == ScrollBarOrientation.Vertical ?
               this.Height : this.Width;

            if (this.maximum == 0 || this.largeChange == 0)
            {
                return trackSize;
            }

            float newThumbSize = ((float)this.largeChange * (float)trackSize) / (float)this.maximum;

            return Convert.ToInt32(Math.Min((float)trackSize, Math.Max(newThumbSize, 10f)));
        }

        private void EnableTimer()
        {
            if (!this.progressTimer.Enabled)
            {
                this.progressTimer.Interval = 600;
                this.progressTimer.Start();
            }
            else
            {
                this.progressTimer.Interval = 10;
            }
        }

        private void StopTimer()
        {
            this.progressTimer.Stop();
        }

        private void ChangeThumbPosition(int position)
        {
            if (this.orientation == ScrollBarOrientation.Vertical)
            {
                this.thumbRectangle.Y = position;
            }
            else
            {
                this.thumbRectangle.X = position;
            }
        }

        private void ProgressThumb(bool enableTimer)
        {
            int scrollOldValue = this.value;
            ScrollEventType type = ScrollEventType.First;
            int thumbSize, thumbPos;

            if (this.orientation == ScrollBarOrientation.Vertical)
            {
                thumbPos = this.thumbRectangle.Y;
                thumbSize = this.thumbRectangle.Height;
            }
            else
            {
                thumbPos = this.thumbRectangle.X;
                thumbSize = this.thumbRectangle.Width;
            }

            if ((this.bottomBarClicked && (thumbPos + thumbSize) < this.trackPosition))
            {
                type = ScrollEventType.LargeIncrement;

                this.value = this.GetValue(false, false);

                if (this.value == this.maximum)
                {
                    this.ChangeThumbPosition(this.thumbBottomLimitTop);

                    type = ScrollEventType.Last;
                }
                else
                {
                    this.ChangeThumbPosition(Math.Min(this.thumbBottomLimitTop, this.GetThumbPosition()));
                }
            }
            else if ((this.topBarClicked && thumbPos > this.trackPosition))
            {
                type = ScrollEventType.LargeDecrement;

                this.value = this.GetValue(false, true);

                if (this.value == this.minimum)
                {
                    this.ChangeThumbPosition(this.thumbTopLimit);

                    type = ScrollEventType.First;
                }
                else
                {
                    this.ChangeThumbPosition(Math.Max(this.thumbTopLimit, this.GetThumbPosition()));
                }
            }

            if (scrollOldValue != this.value)
            {
                this.OnScroll(type, scrollOldValue, this.value, this.scrollOrientation);

                this.Invalidate();

                if (enableTimer)
                {
                    this.EnableTimer();
                }
            }
        }

        #endregion
    }
}
