﻿using System;
using System.Collections.Generic;
using System.Reflection;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace OCRTools.Common
{
    [Obfuscation]
    internal class OcrContent
    {
        [Obfuscation] public OCRType ocrType { get; set; }

        [Obfuscation] public string id { get; set; }

        [Obfuscation]
        [XmlIgnore]
        [ScriptIgnore]
        public string strBase64 { get; set; }

        [Obfuscation] public int processId { get; set; }

        [Obfuscation]
        [XmlIgnore]
        [ScriptIgnore]
        public string processName { get; set; }

        [Obfuscation]
        [XmlIgnore]
        [ScriptIgnore]
        public int threadId { get; set; }

        [Obfuscation] public ResultEntity result { get; set; }

        [Obfuscation]
        [XmlIgnore]
        [ScriptIgnore]
        public string message { get; set; }

        [Obfuscation] public OcrProcessState state { get; set; }

        [Obfuscation]
        [XmlIgnore]
        [ScriptIgnore]
        public Exception exception { get; set; }

        [Obfuscation]
        [XmlIgnore]
        [ScriptIgnore]
        public long receivedTicks { get; set; }

        [Obfuscation]
        [XmlIgnore]
        [ScriptIgnore]
        public long startTicks { get; set; }

        [Obfuscation]
        [XmlIgnore]
        [ScriptIgnore]
        public long endTicks { get; set; }

        public ProcessBy ProcessBy { get; set; }
        public string Identity { get; internal set; }
    }

    [Obfuscation]
    internal class ResultEntity
    {
        [Obfuscation]
        [XmlIgnore]
        [ScriptIgnore]
        public string html { get; set; }

        [Obfuscation] public string autoText { get; set; }

        [Obfuscation] public string spiltText { get; set; }

        [Obfuscation] public string transText { get; set; }

        [Obfuscation] public string spiltLocText { get; set; }

        [Obfuscation] public string transLocText { get; set; }

        [Obfuscation] public string verticalText { get; set; }

        [Obfuscation]
        [XmlIgnore]
        [ScriptIgnore]
        public string mergeText => autoText?.Replace("\r", "").Replace("\n", "").Replace("\t", "");

        [Obfuscation] public List<DownLoadInfo> files { get; set; }

        /// <summary>
        ///     文件预览页URL
        /// </summary>
        [Obfuscation]
        public string viewUrl { get; set; }

        [Obfuscation]
        [XmlIgnore]
        [ScriptIgnore]
        public bool HasResult =>
            !string.IsNullOrEmpty(autoText) || !string.IsNullOrEmpty(GetAutoText()) || files?.Count > 0;

        [Obfuscation]
        [XmlIgnore]
        [ScriptIgnore]
        public bool IsTransResult => !string.IsNullOrEmpty(GetAutoText(true));

        [Obfuscation] public ResutypeEnum resultType { get; set; }

        public string GetAutoText(bool isTrans = false)
        {
            if (isTrans)
            {
                return string.IsNullOrEmpty(transLocText) ? transText : transLocText;
            }
            return string.IsNullOrEmpty(spiltLocText) ? spiltText : spiltLocText;
        }

        public string GetTextResult(bool isKeepT, bool isAppend, bool isShowOldContent, SpiltMode mode, string processName)
        {
            string result;
            if (IsTransResult)
            {
                if (isAppend || !isShowOldContent)
                    return string.Format("【{2}】{0}{1}"
                        , Environment.NewLine
                        , string.IsNullOrEmpty(GetAutoText(true)?.TrimEnd())
                            ? ""
                            : GetAutoText(true)?.TrimEnd()
                        , processName);
                result = string.Format("{0}【{3}】{1}{2}"
                    , string.IsNullOrEmpty(GetAutoText()?.TrimEnd())
                        ? ""
                        : "【原】" + Environment.NewLine + GetAutoText()?.TrimEnd() + Environment.NewLine +
                          Environment.NewLine
                    , Environment.NewLine
                    , GetAutoText(true)?.TrimEnd()
                    , processName);
            }
            else
            {
                switch (mode)
                {
                    case SpiltMode.原始格式:
                        result = autoText;
                        break;
                    case SpiltMode.强制合并:
                        result = mergeText;
                        break;
                    default:
                        result = GetAutoText();
                        break;
                }
            }

            return isKeepT ? result : result?.Replace("\t", "");
        }
    }

    [Serializable]
    [Obfuscation]
    internal class TextCellInfo
    {
        [Obfuscation] public string words { get; set; }

        [Obfuscation] public string trans { get; set; }

        [Obfuscation] public LocationInfo location { get; set; }

        [Obfuscation] public int PageIndex { get; set; }

        public string GetCellContent(bool isTrans, bool isShowOldContent)
        {
            var result = string.Empty;
            if (isTrans && !string.IsNullOrEmpty(trans))
            {
                if (!isShowOldContent || Equals(trans, words))
                    result = trans;
                else
                    result = string.Format("{0}{1}{2}"
                        , string.IsNullOrEmpty(words?.TrimEnd()) ? "" : "【原】" + words?.TrimEnd()
                        , Environment.NewLine
                        , string.IsNullOrEmpty(trans?.TrimEnd()) ? "" : "【译】" + trans?.TrimEnd()
                    );
            }
            else
            {
                result = words?.TrimEnd();
            }
            return result;
        }

        public override string ToString()
        {
            return string.Format("{0},Location:{1}", words, location);
        }
    }

    [Serializable]
    [Obfuscation]
    internal class LocationInfo
    {
        [Obfuscation] public double left { get; set; }

        [Obfuscation] public double top { get; set; }

        [Obfuscation] public double width { get; set; }

        [Obfuscation] public double height { get; set; }

        public override string ToString()
        {
            return string.Format("top:{0},left:{1},width:{2},height:{3}", top, left, width, height);
        }
    }

    internal enum ResutypeEnum
    {
        文本 = 0,
        网页 = 1,
        表格 = 2
    }

    [Obfuscation]
    internal class DownLoadInfo
    {
        [Obfuscation] public string url { get; set; }

        [Obfuscation] public string param { get; set; }

        [Obfuscation] public string viewUrl { get; set; }

        [Obfuscation] public OcrFileType fileType { get; set; }

        [Obfuscation] public string desc { get; set; }
    }

    public enum OcrProcessState
    {
        待处理 = 0,
        处理中 = 1,
        处理成功 = 2,
        处理失败 = 3,
        处理超时 = 4,
        未知状态 = 5,
        并发限制 = 6,
        类型不支持 = 7,
        可预览 = 8
    }

    public enum OcrFileType
    {
        自动 = -1,
        pdf = 1,
        doc = 2,
        ppt = 3,
        xls = 4,
        txt = 5
    }

    public enum TransLanguageTypeEnum
    {
        自动 = 0,
        中文 = 1,
        英文 = 2,

        //繁体中文 = 3,
        日语 = 4,

        //朝鲜语 = 5,
        法语 = 6,

        //西班牙语 = 7,
        //泰语 = 8,
        //阿拉伯语 = 9,
        俄语 = 10,
        葡萄牙语 = 11,
        德语 = 12,

        //意大利语 = 13,
        //希腊语 = 14,
        //荷兰语 = 15,
        //波兰语 = 16,
        //保加利亚语 = 17,
        //爱沙尼亚语 = 18,
        //丹麦语 = 19,
        //芬兰语 = 20,
        //捷克语 = 21,
        //罗马尼亚语 = 22,
        //斯洛文尼亚语 = 23,
        //瑞典语 = 24,
        //匈牙利语 = 25,
        //越南语 = 26,
        //印尼语 = 27,
        韩语 = 28
    }
}