﻿using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace OCRTools
{
    public class TaskRoundedCornerPanel : RoundedCornerPanel
    {
        private bool _selected;

        public bool Selected
        {
            get => _selected;
            set
            {
                if (_selected != value)
                {
                    _selected = value;

                    Invalidate();
                }
            }
        }

        public Color StatusColor { get; } = Color.Transparent;
        public ThumbnailTitleLocation StatusLocation { get; set; }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);

            var g = e.Graphics;

            if (Selected)
            {
                g.PixelOffsetMode = PixelOffsetMode.Default;

                using (var pen = new Pen(Color.FromArgb(77, 77, 77)))
                {
                    pen.DashStyle = DashStyle.Dot;
                    g.DrawRoundedRectangle(pen, ClientRectangle, Radius);
                }
            }

            if (StatusColor.A > 0)
            {
                g.PixelOffsetMode = PixelOffsetMode.Half;

                var y = StatusLocation == ThumbnailTitleLocation.Top ? 0 : ClientRectangle.Height;

                using (var brush = new LinearGradientBrush(new Rectangle(0, 0, ClientRectangle.Width, 1), Color.Black,
                    Color.Black,
                    LinearGradientMode.Horizontal))
                {
                    var cb = new ColorBlend
                    {
                        Positions = new[] { 0, 0.3f, 0.7f, 1 },
                        Colors = new[] { Color.Transparent, StatusColor, StatusColor, Color.Transparent }
                    };
                    brush.InterpolationColors = cb;

                    using (var pen = new Pen(brush))
                    {
                        g.DrawLine(pen, new Point(0, y), new Point(ClientRectangle.Width - 1, y));
                    }
                }
            }
        }
    }

    public class RoundedCornerPanel : Panel
    {
        private Color _panelColor;

        private float _radius;

        public RoundedCornerPanel()
        {
            BackColor = Color.Transparent;

            SetStyle(
                ControlStyles.UserPaint | ControlStyles.AllPaintingInWmPaint | ControlStyles.ResizeRedraw |
                ControlStyles.OptimizedDoubleBuffer | ControlStyles.SupportsTransparentBackColor, true);
        }

        public float Radius
        {
            get => _radius;
            set
            {
                _radius = value;

                Invalidate();
            }
        }

        public Color PanelColor
        {
            get => _panelColor;
            set
            {
                _panelColor = value;

                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            var g = e.Graphics;

            g.PixelOffsetMode = PixelOffsetMode.Half;
            g.SmoothingMode = SmoothingMode.HighQuality;

            using (var brush = new SolidBrush(PanelColor))
            {
                g.DrawRoundedRectangle(brush, ClientRectangle.SizeOffset(1), Radius);
            }
        }
    }
}