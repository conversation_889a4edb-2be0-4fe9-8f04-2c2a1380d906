using CatchTools.Common;
using ImageLib;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;
using TianruoOCR;
using TrOCR.Helper;
using UserControl;

namespace CatchTools.PasteFm
{
    public class FmPasteText : Form
    {
        private SaveFileDialog saveFileDialog1;

        private IContainer components = null;

        private ContextMenuHelp contextMenuHelp1;

        private ToolStripMenuItem 复制全部ToolStripMenuItem;

        private ToolStripMenuItem 粘贴识别ToolStripMenuItem;

        private ToolStripMenuItem 保存文本ToolStripMenuItem;

        private ToolStripMenuItem 取消置顶ToolStripMenuItem;

        private ToolStripMenuItem 文件识别ToolStripMenuItem;
        private RichTextBox txtContent;
        private TabControl tbMain;
        private TabPage tbContentText;
        private TabPage tabPage2;
        private UserControl.ImageBox imageBox;
        private ToolStrip toolStrip1;
        private ToolStripLabel lblPicSize;
        private ToolStripButton tsbSaveImg;
        private NotifyIcon notifyMain;
        private ContextMenuStrip cmsNotify;
        private ToolStripMenuItem tsmShowMain;
        private ToolStripMenuItem 截图toolStripMenuItem1;
        private ToolStripMenuItem 粘贴ToolStripMenuItem;
        private ToolStripMenuItem 文件ToolStripMenuItem;
        private ToolStripMenuItem tsmExit;
        private ToolStripButton tsbReOcr;
        private ToolStripMenuItem 系统设置SToolStripMenuItem;
        private ToolStripSeparator toolStripSeparator1;
        private ToolStripSeparator toolStripSeparator2;
        private ToolStripMenuItem 系统设置ToolStripMenuItem;
        private StatusStrip statusStrip1;
        private ToolStripDropDownButton tmsSpiltMode;
        private ToolStripMenuItem tsmOldSpilt;
        private ToolStripMenuItem tsmAutoSpilt;
        private ToolStripMenuItem tsmAutoMerge;
        private ToolStripDropDownButton tsmColor;
        private ToolStripMenuItem toolStripMenuItem1;
        private ToolStripMenuItem toolStripMenuItem2;
        private ToolStripMenuItem toolStripMenuItem3;
        private ToolStripMenuItem toolStripMenuItem4;
        private ToolStripDropDownButton tsmFontName;
        private ToolStripMenuItem toolStripMenuItem6;
        private ToolStripMenuItem toolStripMenuItem5;
        private ToolStripMenuItem toolStripMenuItem9;
        private ToolStripMenuItem toolStripMenuItem7;
        private ToolStripMenuItem toolStripMenuItem8;
        private ToolStripDropDownButton tsmFontSize;
        private ToolStripMenuItem toolStripMenuItem10;
        private ToolStripMenuItem toolStripMenuItem11;
        private ToolStripMenuItem toolStripMenuItem12;
        private ToolStripMenuItem toolStripMenuItem13;
        private ToolStripMenuItem toolStripMenuItem14;
        private ToolStripMenuItem toolStripMenuItem15;
        private ToolStripMenuItem 截图编辑ToolStripMenuItem;
        private ToolStripMenuItem 截图ToolStripMenuItem;

        //双缓冲
        public static void EnableDoubleBuffering(Control ctrl)
        {
            try
            {
                // Set the value of the double-buffering style bits to true.
                System.Reflection.PropertyInfo info = ctrl.GetType().GetProperty("DoubleBuffered", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
                info.SetValue(ctrl, true, null);

                foreach (Control subCtrl in ctrl.Controls)
                {
                    EnableDoubleBuffering(subCtrl);
                }
            }
            catch { }
        }

        public FmPasteText()
        {
            this.DoubleBuffered = true;
            InitializeComponent();
            EnableDoubleBuffering(this);
            notifyMain.Icon = this.Icon;
            notifyMain.ContextMenuStrip = cmsNotify;
            CheckForIllegalCrossThreadCalls = false;
            base.Width = base.Width.DPIValue();
            base.Height = base.Height.DPIValue();
            txtContent.ContextMenuStrip = contextMenuHelp1;
            saveFileDialog1 = new SaveFileDialog();
            contextMenuHelp1.Opening += RightCMS_Opening;
            contextMenuHelp1.Renderer = new ComboxbtnRenderer(fontCenter: true);
            txtContent.Dock = DockStyle.Fill;
            imageBox.ZoomChanged += ImageBox_ZoomChanged;
            txtContent.Font = new Font(FontFamily, FontSize);
        }

        private SpiltMode CurrentSpiltModel;

        private Color DefaultTxtColor = Color.FromArgb(240, 255, 240);

        private string FontFamily = "新罗马";

        private float FontSize = 12;

        private void FmPasteText_Load(object sender, EventArgs e)
        {
            FontFamily = IniHelper.GetValue("配置", "字体");
            FontSize = BoxUtil.GetFloatFromObject(IniHelper.GetValue("配置", "字号"), 12);
            BindSpiltModel(IniHelper.GetValue("配置", "分段模式"));
            var color = IniHelper.GetValue("配置", "背景色");
            if (!string.IsNullOrEmpty(color))
            {
                foreach (ToolStripDropDownItem item in tsmColor.DropDownItems)
                {
                    if (item.Text.Equals(color))
                    {
                        tsmColor_DropDownItemClicked(sender, new ToolStripItemClickedEventArgs(item));
                        break;
                    }
                }
            }
            var fontSize = IniHelper.GetValue("配置", "字号");
            if (!string.IsNullOrEmpty(fontSize))
            {
                foreach (ToolStripDropDownItem item in tsmFontSize.DropDownItems)
                {
                    if (item.Text.Equals(color))
                    {
                        tsmFontSize_DropDownItemClicked(sender, new ToolStripItemClickedEventArgs(item));
                        break;
                    }
                }
            }
        }

        private void tsmFontName_DropDownItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            var item = e.ClickedItem;
            if (item == null || item.Tag == null)
            {
                return;
            }
            tsmFontName.Text = item.Text;
            FontFamily = item.Tag.ToString();
            txtContent.Font = new Font(FontFamily, FontSize);
            IniHelper.SetValue("配置", "字体", item.Text);
        }

        private void tsmFontSize_DropDownItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            var item = e.ClickedItem;
            if (item == null || item.Tag == null)
            {
                return;
            }
            tsmFontSize.Text = item.Text;
            FontSize = BoxUtil.GetFloatFromObject(item.Tag.ToString(), 12);
            txtContent.Font = new Font(FontFamily, FontSize);
            IniHelper.SetValue("配置", "字号", FontSize.ToString());
        }

        private void tsmColor_DropDownItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            var item = e.ClickedItem;
            if (item == null || item.Tag == null)
            {
                return;
            }
            tsmColor.Text = item.Text;
            var array = item.Tag.ToString().Split(new string[] { " " }, StringSplitOptions.RemoveEmptyEntries);
            if (array?.Length == 3)
            {
                DefaultTxtColor = Color.FromArgb(BoxUtil.GetInt32FromObject(array[0]), BoxUtil.GetInt32FromObject(array[1]), BoxUtil.GetInt32FromObject(array[2]));
                txtContent.BackColor = DefaultTxtColor;
                IniHelper.SetValue("配置", "背景色", item.Text);
            }
        }

        private void BindSpiltModel(object obj)
        {
            CurrentSpiltModel = (SpiltMode)BoxUtil.GetInt32FromObject(obj?.ToString(), 0);
            tmsSpiltMode.Text = CurrentSpiltModel.ToString();
            tmsSpiltMode.Tag = CurrentSpiltModel.GetHashCode();
        }

        private void tmsSpiltMode_DropDownItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            var item = e.ClickedItem;
            if (item == null || item.Tag == null)
            {
                return;
            }
            BindSpiltModel(item.Tag);
            if (tbContentText.Tag != null)
            {
                txtContent.Text = GetTextByContent(tbContentText.Tag as OcrContent);
            }
            IniHelper.SetValue("配置", "分段模式", CurrentSpiltModel.GetHashCode().ToString());
        }

        private void ImageBox_ZoomChanged(object sender, EventArgs e)
        {
            lblPicSize.Text = string.Format("原始：{0}*{1}，倍数：{2}%，显示：{3}*{4}", imageBox.Image?.Width, imageBox.Image?.Height, imageBox.Zoom, imageBox.AutoScrollMinSize.Width, imageBox.AutoScrollMinSize.Height);
        }

        private void RightCMS_Opening(object sender, CancelEventArgs e)
        {
            if (base.TopMost)
            {
                取消置顶ToolStripMenuItem.Text = "取消置顶";
            }
            else
            {
                取消置顶ToolStripMenuItem.Text = "置顶窗体";
            }
        }

        private void FmPasteString_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            Rectangle rectangle = new Rectangle(0, 0, base.Width, 25.DPIValue());
            Point pt = PointToClient(Control.MousePosition);
            if (rectangle.Contains(pt))
            {
                Close();
            }
        }

        private void ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ToolStripMenuItem toolStripMenuItem = (ToolStripMenuItem)sender;
            string name = toolStripMenuItem.Name;
            switch (name)
            {
                case "复制全部ToolStripMenuItem":
                    txtContent.SelectAll();
                    txtContent.Copy();
                    return;
                case "保存文本ToolStripMenuItem":
                    SaveFile(txtContent.Text);
                    return;
                case "文件识别ToolStripMenuItem":
                case "文件ToolStripMenuItem":
                    var openFileDialog = new OpenFileDialog();
                    openFileDialog.Title = "请选择图片文件";
                    openFileDialog.Filter = "图片文件|*.png;*.jpg;*.bmp";
                    openFileDialog.RestoreDirectory = true;
                    openFileDialog.Multiselect = false;
                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        var imgFile = Image.FromFile(openFileDialog.FileName);
                        if (imgFile != null)
                        {
                            new Thread(obj =>
                            {
                                txtContent.Tag = null;
                                txtContent.Text = "正在上传图片，请稍后…";
                                //CommonMethod.ShowHelpMsg("开始上传图片，开始识别…", 1000);
                                string imgUrl = UploadImgByFile(imgFile);
                                OcrByImage(imgFile, imgUrl);
                                //var image = ThumbnailMaker.MakeThumbnail(imgFile, imgFile.Width, imgFile.Height);
                                //OcrByImage(image);
                            }).Start();
                        }
                    }
                    return;
                case "粘贴识别ToolStripMenuItem":
                case "粘贴ToolStripMenuItem":
                    var imgCopy = ClipboardService.GetImage();
                    if (imgCopy != null)
                    {
                        new Thread(obj =>
                        {
                            OcrByImage(imgCopy);
                        }).Start();
                    }
                    return;
                case "取消置顶ToolStripMenuItem":
                    base.TopMost = !base.TopMost;
                    return;
            }
        }

        private string UploadImgByFile(Image imgFile)
        {
            var imgUrl = string.Empty;
            try
            {
                CommonMethod.ShowLoading();

                var byts = ImageToByte(imgFile);
                imgUrl = ImageHelper.GetResult(ImageTypeEnum.阿里, byts);
                if (string.IsNullOrEmpty(imgUrl))
                {
                    imgUrl = ImageHelper.GetResult(ImageTypeEnum.新浪, byts);
                }
                if (string.IsNullOrEmpty(imgUrl))
                {
                    imgUrl = ImageHelper.GetResult(ImageTypeEnum.搜狗, byts);
                }
                if (string.IsNullOrEmpty(imgUrl))
                {
                    imgUrl = ImageHelper.GetResult(ImageTypeEnum.SMMS, byts);
                }
                if (string.IsNullOrEmpty(imgUrl))
                {
                    imgUrl = ImageHelper.GetResult(ImageTypeEnum.UploadCC, byts);
                }
            }
            catch { }
            finally
            {
                CommonMethod.CloseLoading();
            }
            return imgUrl;
        }

        private void SaveFile(string txt)
        {
            string text = DateTime.Now.ToString("yyyyMMddhhmmss");
            string text2 = null;
            string text3 = text;
            for (int i = 0; i < text3.Length; i++)
            {
                char c = text3[i];
                if (c != '/' && c != ':' && c != ' ')
                {
                    text2 += c.ToString();
                }
            }
            saveFileDialog1.Filter = "文本文件| *.txt|所有文件|*.*";
            saveFileDialog1.Title = "保存位置";
            saveFileDialog1.FileName = "文本_" + text2;
            saveFileDialog1.FilterIndex = 0;
            base.ShowIcon = false;
            this.CenterChild();
            if (saveFileDialog1.ShowDialog(this) == DialogResult.OK && saveFileDialog1.FileName != "")
            {
                StreamWriter streamWriter = File.AppendText(saveFileDialog1.FileName);
                streamWriter.Write(txt);
                streamWriter.Flush();
                streamWriter.Close();
            }
        }

        private void FmPastColor_Shown(object sender, EventArgs e)
        {
            StaticValue.handles.Add(base.Handle);
        }

        private void FmPastColor_FormClosed(object sender, FormClosedEventArgs e)
        {
            StaticValue.handles.Remove(base.Handle);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null)
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FmPasteText));
            this.txtContent = new System.Windows.Forms.RichTextBox();
            this.tbMain = new System.Windows.Forms.TabControl();
            this.tbContentText = new System.Windows.Forms.TabPage();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.tsbReOcr = new System.Windows.Forms.ToolStripButton();
            this.tsbSaveImg = new System.Windows.Forms.ToolStripButton();
            this.lblPicSize = new System.Windows.Forms.ToolStripLabel();
            this.imageBox = new CatchTools.UserControl.ImageBox();
            this.notifyMain = new System.Windows.Forms.NotifyIcon(this.components);
            this.cmsNotify = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.tsmShowMain = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.截图编辑ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.截图toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.粘贴ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.文件ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.系统设置SToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmExit = new System.Windows.Forms.ToolStripMenuItem();
            this.statusStrip1 = new System.Windows.Forms.StatusStrip();
            this.tmsSpiltMode = new System.Windows.Forms.ToolStripDropDownButton();
            this.tsmOldSpilt = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmAutoSpilt = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmAutoMerge = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmColor = new System.Windows.Forms.ToolStripDropDownButton();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem3 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem4 = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmFontName = new System.Windows.Forms.ToolStripDropDownButton();
            this.toolStripMenuItem6 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem5 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem9 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem7 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem8 = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmFontSize = new System.Windows.Forms.ToolStripDropDownButton();
            this.toolStripMenuItem10 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem11 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem12 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem13 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem14 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem15 = new System.Windows.Forms.ToolStripMenuItem();
            this.contextMenuHelp1 = new CatchTools.ContextMenuHelp();
            this.复制全部ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.保存文本ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.截图ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.粘贴识别ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.文件识别ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.系统设置ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.取消置顶ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.tbMain.SuspendLayout();
            this.tbContentText.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.toolStrip1.SuspendLayout();
            this.cmsNotify.SuspendLayout();
            this.statusStrip1.SuspendLayout();
            this.contextMenuHelp1.SuspendLayout();
            this.SuspendLayout();
            // 
            // txtContent
            // 
            this.txtContent.BackColor = System.Drawing.Color.Honeydew;
            this.txtContent.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.txtContent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txtContent.Font = new System.Drawing.Font("新宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtContent.Location = new System.Drawing.Point(3, 3);
            this.txtContent.Name = "txtContent";
            this.txtContent.Size = new System.Drawing.Size(470, 351);
            this.txtContent.TabIndex = 0;
            this.txtContent.Text = "";
            // 
            // tbMain
            // 
            this.tbMain.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tbMain.Controls.Add(this.tbContentText);
            this.tbMain.Controls.Add(this.tabPage2);
            this.tbMain.Location = new System.Drawing.Point(0, 0);
            this.tbMain.Name = "tbMain";
            this.tbMain.SelectedIndex = 0;
            this.tbMain.Size = new System.Drawing.Size(484, 389);
            this.tbMain.TabIndex = 4;
            this.tbMain.TabStop = false;
            // 
            // tbContentText
            // 
            this.tbContentText.Controls.Add(this.txtContent);
            this.tbContentText.Location = new System.Drawing.Point(4, 28);
            this.tbContentText.Name = "tbContentText";
            this.tbContentText.Padding = new System.Windows.Forms.Padding(3);
            this.tbContentText.Size = new System.Drawing.Size(476, 357);
            this.tbContentText.TabIndex = 0;
            this.tbContentText.Tag = "识别内容";
            this.tbContentText.Text = "识别内容";
            this.tbContentText.UseVisualStyleBackColor = true;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.toolStrip1);
            this.tabPage2.Controls.Add(this.imageBox);
            this.tabPage2.Location = new System.Drawing.Point(4, 28);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(476, 357);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Tag = "图片预览";
            this.tabPage2.Text = "图片预览";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // toolStrip1
            // 
            this.toolStrip1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsbReOcr,
            this.tsbSaveImg,
            this.lblPicSize});
            this.toolStrip1.Location = new System.Drawing.Point(3, 329);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(470, 25);
            this.toolStrip1.TabIndex = 0;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // tsbReOcr
            // 
            this.tsbReOcr.Checked = true;
            this.tsbReOcr.CheckState = System.Windows.Forms.CheckState.Checked;
            this.tsbReOcr.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.tsbReOcr.ForeColor = System.Drawing.Color.Black;
            this.tsbReOcr.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsbReOcr.Name = "tsbReOcr";
            this.tsbReOcr.Size = new System.Drawing.Size(36, 22);
            this.tsbReOcr.Text = "识别";
            this.tsbReOcr.Click += new System.EventHandler(this.tsbReOcr_Click);
            // 
            // tsbSaveImg
            // 
            this.tsbSaveImg.Checked = true;
            this.tsbSaveImg.CheckState = System.Windows.Forms.CheckState.Checked;
            this.tsbSaveImg.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.tsbSaveImg.ForeColor = System.Drawing.Color.Black;
            this.tsbSaveImg.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsbSaveImg.Name = "tsbSaveImg";
            this.tsbSaveImg.Size = new System.Drawing.Size(36, 22);
            this.tsbSaveImg.Text = "保存";
            this.tsbSaveImg.Click += new System.EventHandler(this.tsbSaveImg_Click);
            // 
            // lblPicSize
            // 
            this.lblPicSize.ForeColor = System.Drawing.Color.Black;
            this.lblPicSize.Name = "lblPicSize";
            this.lblPicSize.Size = new System.Drawing.Size(49, 22);
            this.lblPicSize.Text = "大小：-";
            // 
            // imageBox
            // 
            this.imageBox.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.imageBox.AutoScroll = true;
            this.imageBox.AutoSize = false;
            this.imageBox.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.imageBox.Location = new System.Drawing.Point(-1, 0);
            this.imageBox.Name = "imageBox";
            this.imageBox.Size = new System.Drawing.Size(477, 294);
            this.imageBox.TabIndex = 1;
            this.imageBox.TabStop = false;
            this.imageBox.ZoomIncrement = 20;
            this.imageBox.MouseClick += new System.Windows.Forms.MouseEventHandler(this.ImageBox_MouseClick);
            // 
            // notifyMain
            // 
            this.notifyMain.BalloonTipText = "双击显示主窗体";
            this.notifyMain.BalloonTipTitle = "OCR助手";
            this.notifyMain.Text = "OCR助手";
            this.notifyMain.Visible = true;
            this.notifyMain.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.notifyMain_MouseDoubleClick);
            // 
            // cmsNotify
            // 
            this.cmsNotify.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmShowMain,
            this.toolStripSeparator1,
            this.截图编辑ToolStripMenuItem,
            this.截图toolStripMenuItem1,
            this.粘贴ToolStripMenuItem,
            this.文件ToolStripMenuItem,
            this.toolStripSeparator2,
            this.系统设置SToolStripMenuItem,
            this.tsmExit});
            this.cmsNotify.Name = "标题";
            this.cmsNotify.ShowImageMargin = false;
            this.cmsNotify.Size = new System.Drawing.Size(116, 170);
            // 
            // tsmShowMain
            // 
            this.tsmShowMain.Name = "tsmShowMain";
            this.tsmShowMain.Size = new System.Drawing.Size(115, 22);
            this.tsmShowMain.Text = "显示窗体";
            this.tsmShowMain.Click += new System.EventHandler(this.tsmShowMain_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(112, 6);
            // 
            // 截图编辑ToolStripMenuItem
            // 
            this.截图编辑ToolStripMenuItem.Name = "截图编辑ToolStripMenuItem";
            this.截图编辑ToolStripMenuItem.Size = new System.Drawing.Size(115, 22);
            this.截图编辑ToolStripMenuItem.Text = "截图";
            this.截图编辑ToolStripMenuItem.Click += new System.EventHandler(this.截图ToolStripMenuItem_Click);
            // 
            // 截图toolStripMenuItem1
            // 
            this.截图toolStripMenuItem1.Name = "截图toolStripMenuItem1";
            this.截图toolStripMenuItem1.Size = new System.Drawing.Size(115, 22);
            this.截图toolStripMenuItem1.Text = "截图识别";
            this.截图toolStripMenuItem1.Click += new System.EventHandler(this.截图ToolStripMenuItem_Click);
            // 
            // 粘贴ToolStripMenuItem
            // 
            this.粘贴ToolStripMenuItem.Name = "粘贴ToolStripMenuItem";
            this.粘贴ToolStripMenuItem.Size = new System.Drawing.Size(115, 22);
            this.粘贴ToolStripMenuItem.Text = "粘贴识别";
            this.粘贴ToolStripMenuItem.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // 文件ToolStripMenuItem
            // 
            this.文件ToolStripMenuItem.Name = "文件ToolStripMenuItem";
            this.文件ToolStripMenuItem.Size = new System.Drawing.Size(115, 22);
            this.文件ToolStripMenuItem.Text = "文件识别";
            this.文件ToolStripMenuItem.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(112, 6);
            // 
            // 系统设置SToolStripMenuItem
            // 
            this.系统设置SToolStripMenuItem.Name = "系统设置SToolStripMenuItem";
            this.系统设置SToolStripMenuItem.Size = new System.Drawing.Size(115, 22);
            this.系统设置SToolStripMenuItem.Text = "系统设置";
            this.系统设置SToolStripMenuItem.Click += new System.EventHandler(this.系统设置SToolStripMenuItem_Click);
            // 
            // tsmExit
            // 
            this.tsmExit.Name = "tsmExit";
            this.tsmExit.Size = new System.Drawing.Size(115, 22);
            this.tsmExit.Text = "退出程序(&X)";
            this.tsmExit.Click += new System.EventHandler(this.tsmExit_Click);
            // 
            // statusStrip1
            // 
            this.statusStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tmsSpiltMode,
            this.tsmColor,
            this.tsmFontName,
            this.tsmFontSize});
            this.statusStrip1.Location = new System.Drawing.Point(0, 388);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Size = new System.Drawing.Size(484, 23);
            this.statusStrip1.TabIndex = 5;
            this.statusStrip1.Text = "statusStrip1";
            // 
            // tmsSpiltMode
            // 
            this.tmsSpiltMode.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.tmsSpiltMode.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmOldSpilt,
            this.tsmAutoSpilt,
            this.tsmAutoMerge});
            this.tmsSpiltMode.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F);
            this.tmsSpiltMode.ForeColor = System.Drawing.Color.Black;
            this.tmsSpiltMode.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tmsSpiltMode.Name = "tmsSpiltMode";
            this.tmsSpiltMode.Size = new System.Drawing.Size(68, 21);
            this.tmsSpiltMode.Text = "原始格式";
            this.tmsSpiltMode.DropDownItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.tmsSpiltMode_DropDownItemClicked);
            // 
            // tsmOldSpilt
            // 
            this.tsmOldSpilt.Name = "tsmOldSpilt";
            this.tsmOldSpilt.Size = new System.Drawing.Size(122, 22);
            this.tsmOldSpilt.Tag = "0";
            this.tsmOldSpilt.Text = "原始格式";
            // 
            // tsmAutoSpilt
            // 
            this.tsmAutoSpilt.Name = "tsmAutoSpilt";
            this.tsmAutoSpilt.Size = new System.Drawing.Size(122, 22);
            this.tsmAutoSpilt.Tag = "1";
            this.tsmAutoSpilt.Text = "自动分段";
            // 
            // tsmAutoMerge
            // 
            this.tsmAutoMerge.Name = "tsmAutoMerge";
            this.tsmAutoMerge.Size = new System.Drawing.Size(122, 22);
            this.tsmAutoMerge.Tag = "2";
            this.tsmAutoMerge.Text = "强制合并";
            // 
            // tsmColor
            // 
            this.tsmColor.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.tsmColor.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem1,
            this.toolStripMenuItem2,
            this.toolStripMenuItem3,
            this.toolStripMenuItem4});
            this.tsmColor.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F);
            this.tsmColor.ForeColor = System.Drawing.Color.Black;
            this.tsmColor.Image = ((System.Drawing.Image)(resources.GetObject("tsmColor.Image")));
            this.tsmColor.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmColor.Name = "tsmColor";
            this.tsmColor.Size = new System.Drawing.Size(56, 21);
            this.tsmColor.Text = "护眼色";
            this.tsmColor.DropDownItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.tsmColor_DropDownItemClicked);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(110, 22);
            this.toolStripMenuItem1.Tag = "240 255 240";
            this.toolStripMenuItem1.Text = "护眼色";
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(110, 22);
            this.toolStripMenuItem2.Tag = "255 255 255";
            this.toolStripMenuItem2.Text = "雪白色";
            // 
            // toolStripMenuItem3
            // 
            this.toolStripMenuItem3.Name = "toolStripMenuItem3";
            this.toolStripMenuItem3.Size = new System.Drawing.Size(110, 22);
            this.toolStripMenuItem3.Tag = "255 255 224";
            this.toolStripMenuItem3.Text = "淡黄色";
            // 
            // toolStripMenuItem4
            // 
            this.toolStripMenuItem4.Name = "toolStripMenuItem4";
            this.toolStripMenuItem4.Size = new System.Drawing.Size(110, 22);
            this.toolStripMenuItem4.Tag = "255 250 250";
            this.toolStripMenuItem4.Text = "雪白色";
            // 
            // tsmFontName
            // 
            this.tsmFontName.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.tsmFontName.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem6,
            this.toolStripMenuItem5,
            this.toolStripMenuItem9,
            this.toolStripMenuItem7,
            this.toolStripMenuItem8});
            this.tsmFontName.ForeColor = System.Drawing.Color.Black;
            this.tsmFontName.Image = ((System.Drawing.Image)(resources.GetObject("tsmFontName.Image")));
            this.tsmFontName.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmFontName.Name = "tsmFontName";
            this.tsmFontName.Size = new System.Drawing.Size(57, 21);
            this.tsmFontName.Text = "新罗马";
            this.tsmFontName.DropDownItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.tsmFontName_DropDownItemClicked);
            // 
            // toolStripMenuItem6
            // 
            this.toolStripMenuItem6.Name = "toolStripMenuItem6";
            this.toolStripMenuItem6.Size = new System.Drawing.Size(124, 22);
            this.toolStripMenuItem6.Tag = "新罗马";
            this.toolStripMenuItem6.Text = "新罗马";
            // 
            // toolStripMenuItem5
            // 
            this.toolStripMenuItem5.Name = "toolStripMenuItem5";
            this.toolStripMenuItem5.Size = new System.Drawing.Size(124, 22);
            this.toolStripMenuItem5.Tag = "宋体";
            this.toolStripMenuItem5.Text = "宋体";
            // 
            // toolStripMenuItem9
            // 
            this.toolStripMenuItem9.Name = "toolStripMenuItem9";
            this.toolStripMenuItem9.Size = new System.Drawing.Size(124, 22);
            this.toolStripMenuItem9.Tag = "楷体";
            this.toolStripMenuItem9.Text = "楷体";
            // 
            // toolStripMenuItem7
            // 
            this.toolStripMenuItem7.Name = "toolStripMenuItem7";
            this.toolStripMenuItem7.Size = new System.Drawing.Size(124, 22);
            this.toolStripMenuItem7.Tag = "黑体";
            this.toolStripMenuItem7.Text = "黑体";
            // 
            // toolStripMenuItem8
            // 
            this.toolStripMenuItem8.Name = "toolStripMenuItem8";
            this.toolStripMenuItem8.Size = new System.Drawing.Size(124, 22);
            this.toolStripMenuItem8.Tag = "微软雅黑";
            this.toolStripMenuItem8.Text = "微软雅黑";
            // 
            // tsmFontSize
            // 
            this.tsmFontSize.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem10,
            this.toolStripMenuItem11,
            this.toolStripMenuItem12,
            this.toolStripMenuItem13,
            this.toolStripMenuItem14,
            this.toolStripMenuItem15});
            this.tsmFontSize.ForeColor = System.Drawing.Color.Black;
            this.tsmFontSize.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsmFontSize.Name = "tsmFontSize";
            this.tsmFontSize.Size = new System.Drawing.Size(45, 21);
            this.tsmFontSize.Text = "小四";
            this.tsmFontSize.DropDownItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.tsmFontSize_DropDownItemClicked);
            // 
            // toolStripMenuItem10
            // 
            this.toolStripMenuItem10.Name = "toolStripMenuItem10";
            this.toolStripMenuItem10.Size = new System.Drawing.Size(100, 22);
            this.toolStripMenuItem10.Tag = "16";
            this.toolStripMenuItem10.Text = "三号";
            // 
            // toolStripMenuItem11
            // 
            this.toolStripMenuItem11.Name = "toolStripMenuItem11";
            this.toolStripMenuItem11.Size = new System.Drawing.Size(100, 22);
            this.toolStripMenuItem11.Tag = "15";
            this.toolStripMenuItem11.Text = "小三";
            // 
            // toolStripMenuItem12
            // 
            this.toolStripMenuItem12.Name = "toolStripMenuItem12";
            this.toolStripMenuItem12.Size = new System.Drawing.Size(100, 22);
            this.toolStripMenuItem12.Tag = "14";
            this.toolStripMenuItem12.Text = "四号";
            // 
            // toolStripMenuItem13
            // 
            this.toolStripMenuItem13.Name = "toolStripMenuItem13";
            this.toolStripMenuItem13.Size = new System.Drawing.Size(100, 22);
            this.toolStripMenuItem13.Tag = "12";
            this.toolStripMenuItem13.Text = "小四";
            // 
            // toolStripMenuItem14
            // 
            this.toolStripMenuItem14.Name = "toolStripMenuItem14";
            this.toolStripMenuItem14.Size = new System.Drawing.Size(100, 22);
            this.toolStripMenuItem14.Tag = "10.5";
            this.toolStripMenuItem14.Text = "五号";
            // 
            // toolStripMenuItem15
            // 
            this.toolStripMenuItem15.Name = "toolStripMenuItem15";
            this.toolStripMenuItem15.Size = new System.Drawing.Size(100, 22);
            this.toolStripMenuItem15.Tag = "9";
            this.toolStripMenuItem15.Text = "小五";
            // 
            // contextMenuHelp1
            // 
            this.contextMenuHelp1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.复制全部ToolStripMenuItem,
            this.保存文本ToolStripMenuItem,
            this.截图ToolStripMenuItem,
            this.粘贴识别ToolStripMenuItem,
            this.文件识别ToolStripMenuItem,
            this.系统设置ToolStripMenuItem,
            this.取消置顶ToolStripMenuItem});
            this.contextMenuHelp1.Name = "标题";
            this.contextMenuHelp1.ShowImageMargin = false;
            this.contextMenuHelp1.Size = new System.Drawing.Size(100, 158);
            // 
            // 复制全部ToolStripMenuItem
            // 
            this.复制全部ToolStripMenuItem.Name = "复制全部ToolStripMenuItem";
            this.复制全部ToolStripMenuItem.Size = new System.Drawing.Size(99, 22);
            this.复制全部ToolStripMenuItem.Text = "复制全部";
            this.复制全部ToolStripMenuItem.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // 保存文本ToolStripMenuItem
            // 
            this.保存文本ToolStripMenuItem.Name = "保存文本ToolStripMenuItem";
            this.保存文本ToolStripMenuItem.Size = new System.Drawing.Size(99, 22);
            this.保存文本ToolStripMenuItem.Text = "保存文本";
            this.保存文本ToolStripMenuItem.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // 截图ToolStripMenuItem
            // 
            this.截图ToolStripMenuItem.Name = "截图ToolStripMenuItem";
            this.截图ToolStripMenuItem.Size = new System.Drawing.Size(99, 22);
            this.截图ToolStripMenuItem.Text = "截图识别";
            this.截图ToolStripMenuItem.Click += new System.EventHandler(this.截图ToolStripMenuItem_Click);
            // 
            // 粘贴识别ToolStripMenuItem
            // 
            this.粘贴识别ToolStripMenuItem.Name = "粘贴识别ToolStripMenuItem";
            this.粘贴识别ToolStripMenuItem.Size = new System.Drawing.Size(99, 22);
            this.粘贴识别ToolStripMenuItem.Text = "粘贴识别";
            this.粘贴识别ToolStripMenuItem.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // 文件识别ToolStripMenuItem
            // 
            this.文件识别ToolStripMenuItem.Name = "文件识别ToolStripMenuItem";
            this.文件识别ToolStripMenuItem.Size = new System.Drawing.Size(99, 22);
            this.文件识别ToolStripMenuItem.Text = "文件识别";
            this.文件识别ToolStripMenuItem.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // 系统设置ToolStripMenuItem
            // 
            this.系统设置ToolStripMenuItem.Name = "系统设置ToolStripMenuItem";
            this.系统设置ToolStripMenuItem.Size = new System.Drawing.Size(99, 22);
            this.系统设置ToolStripMenuItem.Text = "系统设置";
            this.系统设置ToolStripMenuItem.Click += new System.EventHandler(this.系统设置SToolStripMenuItem_Click);
            // 
            // 取消置顶ToolStripMenuItem
            // 
            this.取消置顶ToolStripMenuItem.Name = "取消置顶ToolStripMenuItem";
            this.取消置顶ToolStripMenuItem.Size = new System.Drawing.Size(99, 22);
            this.取消置顶ToolStripMenuItem.Text = "取消置顶";
            this.取消置顶ToolStripMenuItem.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // FmPasteText
            // 
            this.ClientSize = new System.Drawing.Size(484, 411);
            this.Controls.Add(this.statusStrip1);
            this.Controls.Add(this.tbMain);
            this.DoubleBuffered = true;
            this.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.ForeColor = System.Drawing.Color.DarkGray;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "FmPasteText";
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "便签";
            this.TopMost = true;
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.FmPastColor_FormClosed);
            this.Load += new System.EventHandler(this.FmPasteText_Load);
            this.Shown += new System.EventHandler(this.FmPastColor_Shown);
            this.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.FmPasteString_MouseDoubleClick);
            this.tbMain.ResumeLayout(false);
            this.tbContentText.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            this.tabPage2.PerformLayout();
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.cmsNotify.ResumeLayout(false);
            this.statusStrip1.ResumeLayout(false);
            this.statusStrip1.PerformLayout();
            this.contextMenuHelp1.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        [DllImport("user32.dll", CharSet = CharSet.Auto)]
        public static extern bool SwitchToThisWindow(IntPtr hWnd, bool fAltTab);

        private void 截图ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (!StaticValue.isCatchScreen)
            {
                this.Hide();
                var windowState = WindowState == FormWindowState.Minimized ? FormWindowState.Normal : WindowState;
                this.WindowState = FormWindowState.Minimized;
                var img = CatchImage(sender != null && (sender as ToolStripMenuItem).Equals(截图编辑ToolStripMenuItem));
                if (img != null)
                {
                    new Thread(obj =>
                    {
                        OcrByImage(img);
                    }).Start();
                }
                this.WindowState = windowState;
            }
        }

        private Image CatchImage(bool isEdit = false)
        {
            Image result = null;
            DrawArea @catch = new DrawArea();
            try
            {
                @catch.UnshowZoom = false;
                @catch.IsShowCross = false;
                @catch.IsEdit = isEdit;
                @catch.Prepare();
                @catch.IsRGB = true;
                @catch.isAutoDraw = true;
                @catch.ActiveTool = isEdit ? DrawToolType.Catch : DrawToolType.QuickCatch;
                StaticValue.current_ToolType = isEdit ? DrawToolType.Catch : DrawToolType.QuickCatch;
                @catch.Status = isEdit ? "截图" : "文字识别";
                if (@catch.ShowDialog() == DialogResult.OK)
                {
                    StaticValue.isCatchScreen = false;
                    if (!isEdit)
                    {
                        result = @catch.GetResultImage();
                    }
                }
            }
            catch (Exception oe)
            {

            }
            finally
            {
                StaticValue.isCatchScreen = false;
                @catch.Close();
                @catch.Dispose();
                if (@catch != null)
                {
                    ((IDisposable)@catch).Dispose();
                }
            }
            return result;
        }

        private void tsbReOcr_Click(object sender, EventArgs e)
        {
            if (imageBox.Image != null)
            {
                new Thread(obj =>
                {
                    OcrByImage(imageBox.Image, imageBox.Tag?.ToString());
                }).Start();
            }
        }

        private void OcrByImage(Image img, string imgUrl = null)
        {
            try
            {
                txtContent.Tag = null;
                txtContent.Text = "正在识别，请稍后…";
                Application.DoEvents();
                CommonMethod.ShowLoading();

                while (tbMain.TabCount > 2)
                {
                    for (int i = 0; i < tbMain.TabCount; i++)
                    {
                        if (tbMain.TabPages[i].Tag == null)
                        {
                            tbMain.TabPages.Remove(tbMain.TabPages[i]);
                            Application.DoEvents();
                            break;
                        }
                    }
                }
                OcrContent ocr = null;
                if (string.IsNullOrEmpty(imgUrl))
                {
                    var byts = ImageToByte(img);
                    //var ocr = OCRHelper.GetResult(byts);
                    ocr = OCRHelper.GetFileResult(byts);
                }
                else
                {
                    ocr = OCRHelper.GetFileResultByUrl(imgUrl);
                }
                if (!string.IsNullOrEmpty(ocr?.id))
                {
                    txtContent.Tag = ocr.id;
                    LoadOtherOcrResult(ocr.id);
                }
                if (!string.IsNullOrEmpty(ocr?.result?.autoText))
                {
                    tbContentText.Tag = ocr;
                    txtContent.Text = GetTextByContent(ocr) + string.Format("{1}{1}【{0}】提供技术支持，耗时：{2}s"
                        , ocr.processId, Environment.NewLine
                        , new TimeSpan(ocr.endTicks - ocr.startTicks).TotalSeconds.ToString("F2"));
                }
                else
                {
                    tbContentText.Tag = null;
                    txtContent.Text = "***该区域未发现文本***";
                }
                imageBox.Image = img;
                imageBox.Tag = imgUrl;
                ImageBox_ZoomChanged(null, null);
            }
            catch (Exception)
            {

                throw;
            }
            finally
            {
                CommonMethod.CloseLoading();
                if (!Visible)
                {
                    Visible = true;
                }
                if (this.WindowState == FormWindowState.Minimized)
                {
                    this.WindowState = FormWindowState.Normal;
                }
                SwitchToThisWindow(this.Handle, true);
            }

        }

        private void LoadOtherOcrResult(string tagId)
        {
            new Thread(p =>
            {
                for (int i = 0; i < 5; i++)
                {
                    Thread.Sleep(2500);
                    var id = txtContent.Tag as string;
                    if (string.IsNullOrEmpty(id) || !id.Equals(tagId))
                    {
                        return;
                    }
                    var lstResult = OCRHelper.GetResultById(id);
                    if (lstResult?.Count > 0)
                    {
                        DetermineCall(delegate
                        {
                            BingTabs(lstResult);
                        });
                    }
                }
            }).Start();
        }

        private void BingTabs(List<OcrContent> lstResult)
        {
            foreach (var item in lstResult)
            {
                if (!tbMain.TabPages.ContainsKey(item.processId))
                {
                    var tabPag = new TabPage
                    {
                        Text = string.Format("【{0}】", item.processId),
                        Name = item.processId
                    };
                    tabPag.Controls.Add(new RichTextBox()
                    {
                        Text = GetTextByContent(item),
                        Tag = item,
                        BackColor = DefaultTxtColor,
                        Dock = DockStyle.Fill,
                        Font = txtContent.Font
                    });
                    tbMain.TabPages.Add(tabPag);
                }
            }
        }

        private string GetTextByContent(OcrContent item)
        {
            switch (CurrentSpiltModel)
            {
                case SpiltMode.自动分段:
                    return item?.result?.autoText;
                case SpiltMode.强制合并:
                    return item?.result?.mergeText;
            }
            return item?.result?.spiltText;
        }

        private void DetermineCall(MethodInvoker method)
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(method);
                }
                else
                {
                    method();
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        private byte[] ImageToByte(Image img)
        {
            img = ProcessImgSize(img);
            byte[] buffer = null;
            using (MemoryStream ms = new MemoryStream())
            {
                img.Save(ms, ImageFormat.Png);
                buffer = ms.ToArray();
            }
            return buffer;
        }

        private int minWidth = 300;
        private int minHeight = 150;
        private Image ProcessImgSize(Image image)
        {
            if (image.Width < minWidth || image.Height < minHeight)
            {
                using (var bitmap = new Bitmap(image.Width > minWidth ? image.Width : minWidth, image.Height > minHeight ? image.Height : minHeight))
                {
                    using (var graphics = Graphics.FromImage(bitmap))
                    {
                        graphics.DrawImage(image, (bitmap.Width - image.Width) / 2, (bitmap.Height - image.Height) / 2, image.Width, image.Height);
                        graphics.Save();
                        graphics.Dispose();
                    }
                    image = new Bitmap(bitmap);
                }
            }
            return image;
        }

        private void 看图ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var img = ClipboardService.GetImage();
            if (img != null)
            {
                var fmScreenPaste = new FrmPasteScreen(img) { Icon = this.Icon};
                fmScreenPaste.Show();
            }
        }

        private const int WM_HOTKEY = 0x312; //窗口消息-热键
        private const int WM_CREATE = 0x1; //窗口消息-创建
        private const int WM_DESTROY = 0x2; //窗口消息-销毁
        private const int OCRKey = 0x3456; //热键ID
        protected override void WndProc(ref Message m)
        {
            if (m.Msg == 274 && (int)m.WParam == 61536)
            {
                WindowState = FormWindowState.Minimized;
                Visible = false;
                return;
            }
            base.WndProc(ref m);
            switch (m.Msg)
            {
                case WM_HOTKEY: //窗口消息-热键ID
                    if (!IsOnSetting)
                    {
                        switch (m.WParam.ToInt32())
                        {
                            case OCRKey: //热键ID
                                截图ToolStripMenuItem_Click(null, null);
                                break;
                            default:
                                break;
                        }
                    }
                    break;
                case WM_CREATE: //窗口消息-创建
                    var userKey = IniHelper.GetValue("快捷键", "文字识别");
                    RegHotKey(userKey);
                    break;
                case WM_DESTROY: //窗口消息-销毁
                    HotKeyHelper.UnRegKey(Handle, OCRKey);
                    break;
                default:
                    break;
            }
        }

        private void RegHotKey(string strUserKey)
        {
            if (strUserKey != "请按下快捷键")
            {
                var strDefaultCtrl = "None";
                var strDefaultKey = "F4";
                SetHotkey(strDefaultCtrl, strDefaultKey, strUserKey, OCRKey);
            }
        }

        private void tsbSaveImg_Click(object sender, EventArgs e)
        {
            if (imageBox.Image != null)
            {
                SaveFile(imageBox.Image);
            }
        }

        private void SaveFile(Image bmp)
        {
            string text = DateTime.Now.ToString("yyyyMMddhhmmss");
            string text2 = null;
            string text3 = text;
            for (int i = 0; i < text3.Length; i++)
            {
                char c = text3[i];
                if (c != '/' && c != ':' && c != ' ')
                {
                    text2 += c.ToString();
                }
            }
            saveFileDialog1 = new SaveFileDialog();
            saveFileDialog1.Filter = "png文件|*.png|jpg文件|*.jpg|bmp文件|*.bmp|gif文件|*.gif";
            saveFileDialog1.Title = "保存位置";
            saveFileDialog1.FileName = StaticValue.CatchName + "_" + text2;
            saveFileDialog1.FilterIndex = 0;
            ((IWin32Window)this).CenterChild();
            if (saveFileDialog1.ShowDialog(this) != DialogResult.OK)
            {
                return;
            }
            string extension = Path.GetExtension(saveFileDialog1.FileName);
            ImageFormat png = ImageFormat.Png;
            if (extension != "")
            {
                switch (extension)
                {
                    default:
                        png = ImageFormat.Png;
                        break;
                    case ".gif":
                        png = ImageFormat.Gif;
                        break;
                    case ".png":
                        png = ImageFormat.Png;
                        break;
                    case ".bmp":
                        png = ImageFormat.Bmp;
                        break;
                    case ".jpg":
                        png = ImageFormat.Jpeg;
                        break;
                }
                bmp.SafeSave(saveFileDialog1.FileName, png);
            }
        }

        private void notifyMain_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (!Visible)
            {
                Show();
                WindowState = FormWindowState.Normal;
                Visible = true;
                this.BringToFront();
            }
        }

        private void tsmShowMain_Click(object sender, EventArgs e)
        {
            notifyMain_MouseDoubleClick(sender, null);
        }

        private void tsmExit_Click(object sender, EventArgs e)
        {
            this.Hide();
            notifyMain.Dispose();
            Application.ExitThread();
            Application.Exit();
        }

        private bool IsOnSetting = false;

        private void 系统设置SToolStripMenuItem_Click(object sender, EventArgs e)
        {
            IsOnSetting = true;
            var oldKey = IniHelper.GetValue("快捷键", "文字识别");
            if (new FmSetting().ShowDialog(this) == DialogResult.OK)
            {
                var newKey = IniHelper.GetValue("快捷键", "文字识别");
                if (!Equals(oldKey, newKey))
                {
                    HotKeyHelper.UnRegKey(Handle, OCRKey);
                    RegHotKey(newKey);
                }
            }
            IsOnSetting = false;
        }

        public void SetHotkey(string strDefaultCtrlKey, string strDefaultKey, string strSetVaule, int flag)
        {
            var array = (strSetVaule + "+").Split('+');
            if (array.Length == 3)
            {
                strDefaultCtrlKey = array[0];
                strDefaultKey = array[1];
            }
            if (array.Length == 2)
            {
                strDefaultCtrlKey = "None";
                strDefaultKey = strSetVaule;
            }
            var array2 = new[]
            {
                strDefaultCtrlKey,
                strDefaultKey
            };
            if (!HelpWin32.RegisterHotKey(Handle, flag, (HelpWin32.KeyModifiers)Enum.Parse(typeof(HelpWin32.KeyModifiers), array2[0].Trim()), (Keys)Enum.Parse(typeof(Keys), array2[1].Trim())))
            {
                CommonMethod.ShowHelpMsg("快捷键冲突，请更换！");
            }
            HelpWin32.RegisterHotKey(Handle, flag, (HelpWin32.KeyModifiers)Enum.Parse(typeof(HelpWin32.KeyModifiers), array2[0].Trim()), (Keys)Enum.Parse(typeof(Keys), array2[1].Trim()));
        }

        private void ImageBox_MouseClick(object sender, MouseEventArgs e)
        {
            if (imageBox.Image != null && e.Button == MouseButtons.Left)
            {
                var fmScreenPaste = new FrmPasteScreen(imageBox.Image) { Icon = this.Icon };
                fmScreenPaste.Show();
            }
        }
    }
}
