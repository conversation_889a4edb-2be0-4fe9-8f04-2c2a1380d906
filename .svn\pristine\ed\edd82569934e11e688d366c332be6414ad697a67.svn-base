using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class BOUNDSHEET : Record
	{
		public uint StreamPosition;

		public byte Visibility;

		public byte SheetType;

		public string SheetName;

		public BOUNDSHEET(Record record)
			: base(record)
		{
		}

		public BOUNDSHEET()
		{
			Type = 133;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			StreamPosition = binaryReader.ReadUInt32();
			Visibility = binaryReader.ReadByte();
			SheetType = binaryReader.ReadByte();
			SheetName = ReadString(binaryReader, 8);
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(StreamPosition);
			binaryWriter.Write(Visibility);
			binaryWriter.Write(SheetType);
			Record.WriteString(binaryWriter, SheetName, 8);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
