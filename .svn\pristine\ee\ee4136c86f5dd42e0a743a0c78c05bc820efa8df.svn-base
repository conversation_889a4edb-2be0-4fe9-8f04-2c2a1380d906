﻿using Microsoft.Win32;
using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using System.Windows.Forms;
using ShareX.ScreenCaptureLib;
using BorderStyle = System.Windows.Forms.BorderStyle;

namespace OCRTools
{
    public class CommonMethod
    {
        private static readonly DefaultToastSetting ToastSetting = new DefaultToastSetting();

        private static ToolTip _txtTooTip;

        //[DllImport("user32.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Winapi)]
        //public static extern IntPtr GetFocus();

        /////获取 当前拥有焦点的控件
        //public static Control GetFocusedControl()
        //{
        //    Control focusedControl = null;
        //    // To get hold of the focused control:
        //    IntPtr focusedHandle = GetFocus();
        //    if (focusedHandle != IntPtr.Zero)
        //        focusedControl = Control.FromChildHandle(focusedHandle);
        //    return focusedControl;
        //}

        private static ToolStripDropDown _txtTipDropDown;

        private static readonly FormTool FrmMsg = new FormTool();

        static CommonMethod()
        {
            InitRecentTask();
        }

        public static bool IsContainsChinese(string strTxt)
        {
            return Regex.IsMatch(strTxt, "[\u4e00-\u9fa5]");
        }

        //public static bool IsContainEnglish(string str)
        //{
        //    return Regex.IsMatch(str, @"[a-zA-Z]");
        //}

        public static Font BaseFont =>
            new Font(CommonSetting.默认文字字体.FontFamily, 5, FontStyle.Regular, GraphicsUnit.Pixel);

        public static bool IsAutoStart
        {
            get
            {
                try
                {
                    using (var currentUser = Registry.CurrentUser)
                    {
                        using (var registryKey =
                            currentUser.CreateSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run"))
                        {
                            return registryKey != null && registryKey.GetValue(Application.ProductName) != null;
                        }
                    }
                }
                catch (Exception)
                {
                }

                return false;
            }
        }

        public static string GetNextTypeStr(out UserTypeEnum nextUserType)
        {
            var nTmp = Math.Max(Program.NowUser?.UserType.GetHashCode() ?? 0, 0) + 1;
            string strText;
            if (Enum.TryParse(nTmp.ToString(), out nextUserType) && Enum.IsDefined(typeof(UserTypeEnum), nextUserType))
                strText = string.Format("升级{0}", nextUserType.ToString());
            else
                strText = "敬请期待";
            return strText;
        }

        private static void InitRecentTask()
        {
            if (Program.RecentTasks == null) Program.RecentTasks = new List<HistoryTask>();
            try
            {
                var files = Directory.GetFiles(string.IsNullOrEmpty(CommonSetting.截图文件保存路径)
                    ? CommonString.DefaultImagePath
                    : CommonSetting.截图文件保存路径);
                for (var i = 0; i < Math.Min(CommonSetting.最大历史记录数量, files.Length); i++)
                    AddRecentTask(new HistoryTask
                    {
                        Status = TaskStatus.History,
                        Info = new HistoryItem
                        {
                            DataType = EDataType.File,
                            FilePath = files[i],
                            CreateTime = ServerTime.DateTime
                        }
                    });
            }
            catch
            {
            }
        }

        public static void ClearAllTask()
        {
            Program.RecentTasks.Clear();
        }

        public static void AddRecentTask(HistoryTask task)
        {
            if (task != null)
            {
                if (Program.RecentTasks.Count >= CommonSetting.最大历史记录数量) Program.RecentTasks.RemoveAt(0);
                if (!string.IsNullOrEmpty(task.Info.FilePath))
                    task.Info.FilePath = task.Info.FilePath.Replace("\\\\", "\\");
                Program.RecentTasks.Add(task);
            }
        }

        public static void RemoveRecentTask(HistoryTask task)
        {
            if (task != null) Program.RecentTasks.Remove(task);
        }

        //ShowNotificationTip(string.Format(Resources.TaskHelpers_OpenQuickScreenColorPicker_Copied_to_clipboard___0_, text),
        //                "OCRTools - " + Resources.ScreenColorPicker);
        public static void ShowNotificationTip(string text, string title = null, int duration = -1)
        {
            FrmMain.FrmTool.Invoke((Action)delegate
            {
                ShowNotificationTip(text, title, null, duration);
            });
        }

        public static void ShowCaptureNotificationTip(string fileName, int duration = -1)
        {
            FrmMain.FrmTool.Invoke((Action)delegate
            {
                ShowNotificationTip(null, Application.ProductName + "-截图保存", fileName, duration);
            });
        }

        public static void ShowNotificationTip(string text, string title, string fileName = null, int duration = -1)
        {
            if (string.IsNullOrEmpty(title)) title = Application.ProductName;
            if (duration < 0) duration = (int)(ToastSetting.ToastWindowDuration * 1000);

            var toastConfig = new NotificationFormConfig
            {
                Duration = duration,
                FadeDuration = (int)(ToastSetting.ToastWindowFadeDuration * 1000),
                Placement = ToastSetting.ToastWindowPlacement,
                Size = ToastSetting.ToastWindowSize,
                Title = title,
                Text = text,
                FilePath = fileName
            };
            if (toastConfig.LeftClickAction == ToastClickAction.None)
            {
                if (!string.IsNullOrEmpty(toastConfig.Url))
                    toastConfig.LeftClickAction = ToastClickAction.OpenUrl;
                else if (!string.IsNullOrEmpty(toastConfig.FilePath))
                    toastConfig.LeftClickAction = ToastClickAction.OpenFolder;
                else if (toastConfig.Image != null) toastConfig.LeftClickAction = ToastClickAction.ViewImage;
            }
            NotificationForm.Show(toastConfig);
        }

        public static void AutoStart(bool isAdd)
        {
            try
            {
                //if (Equals(isAdd, CommonSetting.开机启动)) return;
                using (var currentUser = Registry.CurrentUser)
                {
                    using (var registryKey =
                        currentUser.CreateSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run"))
                    {
                        if (registryKey != null)
                        {
                            if (isAdd)
                            {
                                if (registryKey.GetValue(Application.ProductName) == null)
                                    registryKey.SetValue(Application.ProductName,
                                        Application.ExecutablePath.Replace("/", "\\"));
                            }
                            else
                            {
                                if (registryKey.GetValue(Application.ProductName) != null)
                                    registryKey.DeleteValue(Application.ProductName, false);
                            }

                            registryKey.Close();
                        }
                    }

                    currentUser.Close();
                }
            }
            catch (Exception)
            {
                ShowHelpMsg("设置开机启动失败，如果被拦截，请点击允许！或者右键->以管理员方式打开重试！");
            }
        }

        public static bool OpenFolder(string filePath)
        {
            if (!string.IsNullOrEmpty(filePath) && Directory.Exists(filePath))
                try
                {
                    using (var process = new Process())
                    {
                        var psi = new ProcessStartInfo
                        {
                            FileName = filePath
                        };

                        process.StartInfo = psi;
                        process.Start();
                    }

                    return true;
                }
                catch
                {
                }

            return false;
        }

        public static bool OpenFile(string filePath)
        {
            if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                try
                {
                    using (var process = new Process())
                    {
                        var psi = new ProcessStartInfo
                        {
                            FileName = filePath
                        };

                        process.StartInfo = psi;
                        process.Start();
                    }

                    return true;
                }
                catch
                {
                }

            return false;
        }

        public static bool OpenFolderWithFile(string filePath)
        {
            filePath = filePath.Replace("\\\\", "\\");
            if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                try
                {
                    NativeMethods.OpenFolderAndSelectFile(filePath);
                    return true;
                }
                catch
                {
                }

            return false;
        }

        public static void DetermineCall(Control ctrl, MethodInvoker method)
        {
            try
            {
                if (ctrl.InvokeRequired)
                    ctrl.Invoke(method);
                else
                    method();
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        public static void OpenUrl(string url)
        {
            if (!string.IsNullOrEmpty(url))
                Task.Factory.StartNew(() =>
                {
                    try
                    {
                        using (var process = new Process())
                        {
                            var psi = new ProcessStartInfo();

                            if (!string.IsNullOrEmpty(CommonString.BrowserPath))
                            {
                                psi.FileName = CommonString.BrowserPath;
                                psi.Arguments = url;
                            }
                            else
                            {
                                psi.FileName = url;
                            }

                            process.StartInfo = psi;
                            process.Start();
                        }
                    }
                    catch
                    {
                    }
                });
        }

        public static void OpenForm(string cName)
        {
            if (string.IsNullOrEmpty(cName)) return;
            try
            {
                (Assembly.GetExecutingAssembly().CreateInstance(cName) as Form)?.Show();
            }
            catch
            {
            }
        }

        /// <summary>
        ///     引用user32.dll动态链接库（windows api），
        ///     使用库中定义 API：SetCursorPos
        /// </summary>
        [DllImport("user32.dll")]
        public static extern int SetCursorPos(int x, int y);

        internal static void BindImgLocation(Control control, List<TextCellInfo> lstCells)
        {
            foreach (var item in lstCells)
            {
                if (item == null || item.location == null) continue;
                var baseSize = new Size((int)item.location.width, (int)item.location.height);

                var lbl = new TransParentLabel
                {
                    Location = new Point((int)Math.Floor(item.location.left), (int)Math.Floor(item.location.top)),
                    TabStop = false,
                    Size = new Size(baseSize.Width, baseSize.Height + 2),
                    TabIndex = lstCells.IndexOf(item) + 1,
                    BackColor = Color.Transparent
                }; // new Label() { BorderStyle = BorderStyle.Fixed3D, AutoSize = false, BackColor = Color.Transparent };

                ////lbl.Font = GetFontByGraphicsMeasure(gh, lbl.Text, BaseFont, ref baseSize);
                ////lbl.Font = GetFontByTextRendererMeasure(gh, lbl.Text, BaseFont, ref baseSize);

                lbl.OrgLocation = lbl.Location;
                lbl.OriSize = lbl.Size;

                ShowTxtToolTip(null, lbl);

                control.Controls.Add(lbl);

                lbl.BringToFront();
            }

            lstCells.Clear();
        }

        //private static PopupForm popupForm;
        public static void ShowTxtToolTip(UcContent parentCtrl, TransParentLabel tipControl)
        {
            if (_txtTooTip == null)
            {
                _txtTooTip = new ToolTip { ToolTipIcon = ToolTipIcon.Info, OwnerDraw = true };
                var tipStringFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center,
                    //FormatFlags = StringFormatFlags.NoWrap,
                    HotkeyPrefix = HotkeyPrefix.None
                };
                var tipFont = new Font("微软雅黑", 10, FontStyle.Bold);
                _txtTooTip.Draw += (sender, e) =>
                {
                    e.Graphics.InterpolationMode = InterpolationMode.HighQualityBilinear;
                    e.Graphics.CompositingQuality = CompositingQuality.HighQuality;
                    e.Graphics.SmoothingMode = SmoothingMode.HighQuality;
                    e.Graphics.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;

                    using (var contentBrush = new SolidBrush(CommonSetting.默认背景颜色))
                    {
                        e.Graphics.FillRectangle(contentBrush, e.Bounds);
                    }

                    //e.DrawBackground();

                    e.Graphics.DrawString(e.ToolTipText, tipFont, SystemBrushes.ControlText,
                        new RectangleF(e.Bounds.X, e.Bounds.Y + 2, e.Bounds.Width, e.Bounds.Height - 4),
                        tipStringFormat);

                    e.DrawBorder();
                    //using (var penBorder = new Pen(Color.Gray, 1))
                    //{
                    //    Rectangle rectBorder = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width - 1, e.Bounds.Height - 1);
                    //    e.Graphics.DrawRectangle(penBorder, e.Bounds);
                    //}
                };
            }

            tipControl.MouseEnter += (sender, e) =>
            {
                tipControl.BringToFront();
                tipControl.IsMouseMove = true;
                if (!string.IsNullOrEmpty(tipControl.Text))
                {
                    //txtTooTip.SetToolTip(parentCtrl, tipControl.Tag?.ToString());
                    //txtTooTip.ToolTipTitle = tipControl.Tag?.ToString();
                    var mouseToForm = parentCtrl.PointToClient(Control.MousePosition);
                    var ctrlToForm = tipControl.PointToClient(Control.MousePosition);
                    var x = mouseToForm.X - ctrlToForm.X;
                    x = x < 0 ? 0 : x;
                    var y = mouseToForm.Y;
                    var verHeight = tipControl.Height - ctrlToForm.Y;
                    if (verHeight + y > parentCtrl.Height)
                        y += 20;
                    else
                        y += verHeight;

                    //var popupConfig = new PopupConfig();
                    //popupConfig.f1HelpText = "图文模式";
                    //popupConfig.TitleText = "title";
                    //popupConfig.MessageText = tipControl.Text;
                    //popupForm = new PopupForm(popupConfig);
                    _txtTooTip.Show(tipControl.Text, parentCtrl, new Point(x, y));
                }
            };
            tipControl.MouseLeave += (sender, e) =>
            {
                tipControl.IsMouseMove = false;
                if (!string.IsNullOrEmpty(tipControl.Text)) _txtTooTip.Hide(parentCtrl);
            };
            //tipControl.MouseMove += (object sender, MouseEventArgs e) =>
            //{
            //    tipControl.IsMouseMove = true;
            //    Console.WriteLine("MouseMove" + tipControl.Tag?.ToString());
            //};}
        }

        public static Font ScaleLabel(string measuredString, Font baseFont, Size baseSize, float stepSize = 0.1f)
        {
            Label label = new Label() { Text = measuredString, AutoSize = false, Font = baseFont, Size = baseSize };
            //decrease font size if text is wider or higher than label
            while (lblTextSize() is Size s && s.Width > label.Width || s.Height > label.Height)
            {
                var fontSize = label.Font.Size - stepSize;
                if (fontSize <= 0)
                {
                    break;
                }
                label.Font = new Font(label.Font.FontFamily, fontSize, label.Font.Style);
            }

            //increase font size if label width is bigger than text size
            while (label.Width > lblTextSize().Width)
            {
                var font = new Font(label.Font.FontFamily, label.Font.Size + stepSize, label.Font.Style);
                var nextSize = TextRenderer.MeasureText(label.Text, font, label.Size, TextFormatFlags.WordBreak);

                //dont make text width or hight bigger than label
                if (nextSize.Width > label.Width || nextSize.Height > label.Height)
                    break;

                label.Font = font;
            }

            Size lblTextSize() => TextRenderer.MeasureText(label.Text,
                new Font(label.Font.FontFamily, label.Font.Size, label.Font.Style), label.Size, TextFormatFlags.WordBreak);

            return label.Font;
        }

        public static void ShowTxtToolTipContextMenu(TransParentLabel ctrl, Point location)
        {
            TextBox txtBox;
            if (_txtTipDropDown == null)
            {
                _txtTipDropDown = new ToolStripDropDown
                {
                    Padding = Padding.Empty,
                    Margin = Padding.Empty,
                    TabStop = false
                };
                txtBox = new TextBox
                {
                    BorderStyle = BorderStyle.FixedSingle,
                    AutoSize = false,
                    Multiline = true,
                    Margin = CommonString.PaddingZero,
                    Font = ctrl.Font,
                    //Location = ctrl.Location,
                    TabStop = false
                };
                ToolStripItem lblInfo = new ToolStripControlHost(txtBox)
                {
                    //DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                    AutoSize = false,
                    Padding = CommonString.PaddingZero,
                    Margin = CommonString.PaddingZero,
                    DisplayStyle = ToolStripItemDisplayStyle.Text
                    //Image = Properties.Resources.复制,
                };
                _txtTipDropDown.Items.Add(lblInfo);
                _txtTipDropDown.VisibleChanged += (sender, e) =>
                {
                    var sourceControl = sender?.GetType()
                        .GetProperty("SourceControlInternal", BindingFlags.Instance | BindingFlags.NonPublic)
                        ?.GetValue(sender, null);
                    if (sourceControl != null && sourceControl is TransParentLabel label)
                        label.IsClickToView = _txtTipDropDown.Visible;
                };
            }
            else
            {
                txtBox = (_txtTipDropDown.Items[0] as ToolStripControlHost).Control as TextBox;
            }

            if (txtBox != null)
            {
                txtBox.Text = ctrl.Text;
                var preferredSize = txtBox.PreferredSize;
                if (preferredSize.Width < 100) preferredSize.Width = (int)(preferredSize.Width * 1.5d);
                preferredSize.Width = Math.Max(preferredSize.Width, ctrl.Width);
                txtBox.Size = preferredSize;
            }

            _txtTipDropDown.Show(ctrl, location);

            if (txtBox != null)
            {
                txtBox.Focus();
                txtBox.SelectAll();
            }
        }

        //双缓冲
        public static void EnableDoubleBuffering(Control ctrl)
        {
            try
            {
                // Set the value of the double-buffering style bits to true.
                var info = ctrl.GetType().GetProperty("DoubleBuffered", BindingFlags.Instance | BindingFlags.NonPublic);
                info.SetValue(ctrl, true, null);

                foreach (Control subCtrl in ctrl.Controls) EnableDoubleBuffering(subCtrl);
            }
            catch
            {
            }
        }

        public static string GetFileExt(string fileName, string fileExt = null)
        {
            try
            {
                if (string.IsNullOrEmpty(fileExt))
                {
                    if (fileName.StartsWith("data:txt"))
                        fileExt = CommonString.StrDefaultTxtType;
                    else if (fileName.StartsWith("data:") || fileName.StartsWith("http"))
                        fileExt = CommonString.StrDefaultImgType;
                }

                if (string.IsNullOrEmpty(fileExt))
                {
                    fileExt = Path.GetExtension(fileName).TrimStart('.').ToLower();
                    if (!string.IsNullOrEmpty(fileExt) && fileExt.Contains("?"))
                        fileExt = CommonString.SubString(fileExt, "", "?");
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe);
            }

            return fileExt;
        }

        public static void ShowHelpMsg(string msg, int totalMilsec = 3000, string strFrom = null)
        {
            Task.Factory.StartNew(() =>
            {
                try
                {
                    if (string.IsNullOrEmpty(msg))
                    {
                        msg = string.Format("欢迎使用{0}！", Application.ProductName);
                    }
                    else
                    {
                        if (string.IsNullOrEmpty(strFrom)) msg = string.Format("{0} {1}", Application.ProductName, msg);
                    }

                    FrmMsg.DelayMilSec = totalMilsec;
                    FrmMsg.DrawStr(msg);
                }
                catch
                {
                }
            });
        }

        #region 初始化数据

        public static void Exit()
        {
            try
            {
                CommonString.IsExit = true;
                Application.ExitThread();
                Application.Exit();
                Process.GetCurrentProcess().Kill();
            }
            catch (Exception oe)
            {
                Log.WriteError("退出异常", oe);
            }

            try
            {
                Environment.Exit(0);
            }
            catch
            {
            }
        }

        #endregion

        #region 站点可用性相关

        public static string GetSubStrByUrl(string strUrl)
        {
            var strTmp = "";
            if (!string.IsNullOrEmpty(strUrl))
                try
                {
                    if (strUrl.Contains("&"))
                    {
                        strTmp = SubString(strUrl, "", "&");
                        var strEncrpt = SubString(strUrl, "&");
                        strEncrpt = CommonEncryptHelper.DesEncrypt(strEncrpt, CommonString.StrCommonEncryptKey);
                        //strURL = CommonEncryptHelper.DESDecrypt(strTmp, CommonString.StrCommonEncryptKey);
                        strTmp += "&con=" + HttpUtility.UrlEncode(strEncrpt);
                    }
                    else
                    {
                        strTmp = strUrl;
                    }
                }
                catch (Exception)
                {
                    strTmp = strUrl;
                    //Log.WriteError("GetSubStrByURL出错", oe);
                }

            return strTmp;
        }

        public static int ExecTimeOutSecond = 35;

        public static NameValueCollection GetRequestHeader()
        {
            if (Program.NowUser != null)
                return new NameValueCollection
                {
                    {"mac", Environment.UserDomainName},
                    {"ver", CommonString.DtNowDate.ToString("yyyy-MM-dd HH:mm:ss")},
                    {"tick",ServerTime.DateTime.Ticks.ToString()},
                    {"app", Program.NowUser?.Account},
                    {"reg", Program.NowUser?.DtReg.ToString("yyyy-MM-dd HH:mm:ss")},
                    {"token", Program.NowUser?.Token}
                };
            return new NameValueCollection
            {
                {"mac", Environment.UserDomainName},
                {"ver", CommonString.DtNowDate.ToString("yyyy-MM-dd HH:mm:ss")},
                {"tick",ServerTime.DateTime.Ticks.ToString()}
            };
        }

        public static string GetServerHtml(string url, string strNowSite
            , bool isDecodeUrl = true, bool isPost = false, string strPost = "")
        {
            var html = "";
            try
            {
                var headers = GetRequestHeader();
                if (isDecodeUrl)
                {
                    if (isPost)
                        html = WebClientExt.GetHtml(strNowSite + GetSubStrByUrl(url), "", "", strPost, "",
                            ExecTimeOutSecond, headers);
                    else
                        html = WebClientExt.GetHtml(strNowSite + GetSubStrByUrl(url), "", "", "", "", ExecTimeOutSecond,
                            headers);
                }
                else
                {
                    if (isPost)
                        html = WebClientExt.GetHtml(strNowSite + url, "", "", strPost, "", ExecTimeOutSecond, headers);
                    else
                        html = WebClientExt.GetHtml(strNowSite + url, "", "", "", "", ExecTimeOutSecond, headers);
                }
            }
            catch (Exception)
            {
                //Log.WriteError("GetServerHtml出错", oe);
            }

            return html;
        }

        #endregion

        #region 共享方法

        public static string ExecCmd(string str)
        {
            //process用于调用外部程序
            using (var p = new Process())
            {
                //调用cmd.exe
                p.StartInfo.FileName = "cmd.exe";
                //不显示程序窗口
                p.StartInfo.CreateNoWindow = true;
                //是否指定操作系统外壳进程启动程序
                p.StartInfo.UseShellExecute = false;
                //可能接受来自调用程序的输入信息
                //重定向标准输入
                p.StartInfo.RedirectStandardInput = true;
                //重定向标准输出
                p.StartInfo.RedirectStandardOutput = true;
                //重定向错误输出
                p.StartInfo.RedirectStandardError = true;
                //启动程序
                p.Start();
                //输入命令
                p.StandardInput.WriteLine(str);
                //一定要关闭。
                p.StandardInput.WriteLine("exit");
                return p.StandardOutput.ReadToEnd();
            }
        }

        public static string SubString(string strSource, string strSpilt, string strEnd = "")
        {
            return strSource.SubStringHorspool(strSpilt, strEnd).Trim();
        }

        #endregion

        public static string NumberToLetters(int num)
        {
            string result = "";
            while (--num >= 0)
            {
                result = (char)('A' + (num % 26)) + result;
                num /= 26;
            }
            return result;
        }

        private static string GetNextRomanNumeralStep(ref int num, int step, string numeral)
        {
            string result = "";
            if (num >= step)
            {
                result = numeral.Repeat(num / step);
                num %= step;
            }
            return result;
        }

        public static string NumberToRomanNumeral(int num)
        {
            string result = "";
            result += GetNextRomanNumeralStep(ref num, 1000, "M");
            result += GetNextRomanNumeralStep(ref num, 900, "CM");
            result += GetNextRomanNumeralStep(ref num, 500, "D");
            result += GetNextRomanNumeralStep(ref num, 400, "CD");
            result += GetNextRomanNumeralStep(ref num, 100, "C");
            result += GetNextRomanNumeralStep(ref num, 90, "XC");
            result += GetNextRomanNumeralStep(ref num, 50, "L");
            result += GetNextRomanNumeralStep(ref num, 40, "XL");
            result += GetNextRomanNumeralStep(ref num, 10, "X");
            result += GetNextRomanNumeralStep(ref num, 9, "IX");
            result += GetNextRomanNumeralStep(ref num, 5, "V");
            result += GetNextRomanNumeralStep(ref num, 4, "IV");
            result += GetNextRomanNumeralStep(ref num, 1, "I");
            return result;
        }

        public static Size MeasureText(string text, Font font)
        {
            using (Graphics g = Graphics.FromHwnd(IntPtr.Zero))
            {
                return g.MeasureString(text, font).ToSize();
            }
        }

        public static Color CatchImageColor(Bitmap back, Color baseColor)
        {
            var result = baseColor;
            using (var form = new RegionCaptureForm(RegionCaptureMode.ScreenColorPicker
                , new RegionCaptureOptions()
                {
                    CustomInfoTitle = "取色器"
                }, back))
            {
                try
                {
                    form.Icon = ControlExtension.GetMetroForm()?.Icon;
                    form.ShowDialog();
                    if (form.Result != RegionResult.Close)
                    {
                        if (form.ShapeManager != null)
                        {
                            try
                            {
                                result = form.ShapeManager.GetCurrentColor();
                            }
                            catch { }
                        }
                    }
                }
                catch { }
            }
            return result;
        }
    }
}