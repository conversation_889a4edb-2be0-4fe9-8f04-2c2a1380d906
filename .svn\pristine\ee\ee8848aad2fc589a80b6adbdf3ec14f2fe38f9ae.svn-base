using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Globalization;
using System.Runtime.Serialization;
using System.Windows.Forms;

namespace OCRTools
{
    internal class DrawLine : DrawObject
    {
        private Point _endPoint;
        private Point _startPoint;

        public DrawLine()
            : this(0, 0, 1, 0)
        {
        }

        public DrawLine(int x1, int y1, int x2, int y2)
        {
            _startPoint.X = x1;
            _startPoint.Y = y1;
            _endPoint.X = x2;
            _endPoint.Y = y2;
            Initialize();
        }

        public override int HandleCount => 2;

        public override DrawToolType NoteType => DrawToolType.Line;

        protected GraphicsPath AreaPath { get; set; }

        protected Pen AreaPen { get; set; }

        protected Region AreaRegion { get; set; }

        public override Rectangle GetBoundingBox()
        {
            using (var graphicsPath = CreateArrowPath())
            {
                var bounds = graphicsPath.GetBounds();
                if (IsSelected)
                {
                    var num = 10.DpiValue();
                    bounds.Inflate(num, num);
                }

                return Rectangle.Ceiling(bounds);
            }
        }

        private GraphicsPath CreateArrowPath()
        {
            var graphicsPath = new GraphicsPath();
            var points = new[]
            {
                _startPoint,
                _endPoint
            };
            graphicsPath.AddCurve(points, 0.7f);
            return graphicsPath;
        }

        public override DrawObject Clone()
        {
            var drawLine = new DrawLine
            {
                _startPoint = _startPoint,
                _endPoint = _endPoint
            };
            FillDrawObjectFields(drawLine);
            return drawLine;
        }

        public override void Draw(Graphics g)
        {
            g.SmoothingMode = SmoothingMode.HighQuality;
            var point = _startPoint;
            var point2 = _endPoint;
            var value = Math.Sqrt(Math.Abs(point.X - point2.X) * Math.Abs(point.X - point2.X) +
                                  Math.Abs(point.Y - point2.Y) * Math.Abs(point.Y - point2.Y));
            var num = Convert.ToInt32(value);
            var num2 = Convert.ToInt32(value);
            StartPoint = _startPoint;
            EndPoint = _endPoint;
            Rectangle = new Rectangle(0, 0, num, num2);
            if (num > 6 || num2 > 6)
            {
                var pen = new Pen(Color.White)
                {
                    Color = Color,
                    Width = PenWidth.DpiValue()
                };
                if (IsDot)
                    pen.DashPattern = new[]
                    {
                        3f,
                        3f
                    };
                g.DrawLine(pen, _startPoint.X, _startPoint.Y, _endPoint.X, _endPoint.Y);
                pen.Dispose();
            }
        }

        public override Point GetHandle(int handleNumber)
        {
            if (handleNumber == 1) return _startPoint;
            return _endPoint;
        }

        public override int HitTest(Point point)
        {
            if (Selected)
                for (var i = 1; i <= HandleCount; i++)
                    if (GetHandleRectangle(i).Contains(point))
                        return i;
            if (PointInObject(point)) return 0;
            return -1;
        }

        public override bool PointInObject(Point point)
        {
            if (_startPoint == _endPoint) return false;
            var graphicsPath = new GraphicsPath();
            using (var pen = new Pen(Color.Black, 20f))
            {
                graphicsPath.AddLine(_startPoint.X, _startPoint.Y, _endPoint.X, _endPoint.Y);
                graphicsPath.Widen(pen);
                return graphicsPath.IsVisible(point);
            }
        }

        public override bool IntersectsWith(Rectangle rectangle)
        {
            CreateObjects();
            return AreaRegion.IsVisible(rectangle);
        }

        public override Cursor GetHandleCursor(int handleNumber)
        {
            switch (handleNumber)
            {
                case 1:
                    return CursorEx.Arrow;
                case 2:
                    return CursorEx.Arrow;
                default:
                    return CursorEx.Arrow;
            }
        }

        public override void MoveHandleTo(Point point, int handleNumber)
        {
            if (handleNumber == 1)
                _startPoint = point;
            else
                _endPoint = point;
            Invalidate();
        }

        public override void MoveHandleTo(Point point, int handleNumber, bool shiftPressed)
        {
            var degree = 45f;
            var startDegree = 0f;
            switch (handleNumber)
            {
                case 1:
                    _startPoint = shiftPressed ? SnapPositionToDegree(_endPoint, point, degree, startDegree) : point;
                    break;
                case 2:
                    _endPoint = shiftPressed ? SnapPositionToDegree(_startPoint, point, degree, startDegree) : point;
                    break;
            }
        }

        public override void Move(int deltaX, int deltaY)
        {
            _startPoint.X += deltaX;
            _startPoint.Y += deltaY;
            _endPoint.X += deltaX;
            _endPoint.Y += deltaY;
            Invalidate();
        }

        public override void SaveToStream(SerializationInfo info, int orderNumber)
        {
            info.AddValue(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "Start", orderNumber), _startPoint);
            info.AddValue(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "End", orderNumber), _endPoint);
            base.SaveToStream(info, orderNumber);
        }

        public override void LoadFromStream(SerializationInfo info, int orderNumber)
        {
            _startPoint =
                (Point) info.GetValue(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "Start", orderNumber),
                    typeof(Point));
            _endPoint = (Point) info.GetValue(string.Format(CultureInfo.InvariantCulture, "{0}{1}", "End", orderNumber),
                typeof(Point));
            base.LoadFromStream(info, orderNumber);
        }

        protected void Invalidate()
        {
            if (AreaPath != null)
            {
                AreaPath.Dispose();
                AreaPath = null;
            }

            if (AreaPen != null)
            {
                AreaPen.Dispose();
                AreaPen = null;
            }

            if (AreaRegion != null)
            {
                AreaRegion.Dispose();
                AreaRegion = null;
            }
        }

        protected virtual void CreateObjects()
        {
            if (AreaPath == null && !(_startPoint == _endPoint))
            {
                AreaPath = new GraphicsPath();
                AreaPen = new Pen(Color.Black, 7f);
                AreaPath.AddLine(_startPoint.X, _startPoint.Y, _endPoint.X, _endPoint.Y);
                AreaPath.Widen(AreaPen);
                CurrentRect = new Rectangle(0, 0, _endPoint.X - _startPoint.X, _endPoint.Y - _startPoint.Y);
                AreaRegion = new Region(AreaPath);
            }
        }
    }
}