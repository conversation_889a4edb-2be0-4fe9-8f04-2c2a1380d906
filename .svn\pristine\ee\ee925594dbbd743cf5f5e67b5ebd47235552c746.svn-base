﻿/**
 * MetroFramework - Modern UI for WinForms
 * 
 * The MIT License (MIT)
 * Copyright (c) 2011 <PERSON>, http://github.com/viperneo
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of 
 * this software and associated documentation files (the "Software"), to deal in the 
 * Software without restriction, including without limitation the rights to use, copy, 
 * modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, 
 * and to permit persons to whom the Software is furnished to do so, subject to the 
 * following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in 
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
 * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT 
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF 
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE 
 * OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace MetroFramework.Components
{
    [ToolboxBitmap(typeof(ToolTip))]
    public class MetroToolTip : ToolTip, IMetroComponent
    {
        private MetroColorStyle metroStyle = MetroColorStyle.Blue;

        private MetroThemeStyle metroTheme = MetroThemeStyle.Light;

        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                if (StyleManager != null)
                {
                    return StyleManager.Style;
                }
                return metroStyle;
            }
            set
            {
                metroStyle = value;
            }
        }

        [Category("Metro Appearance")]
        public MetroThemeStyle Theme
        {
            get
            {
                if (StyleManager != null)
                {
                    return StyleManager.Theme;
                }
                return metroTheme;
            }
            set
            {
                metroTheme = value;
            }
        }

        [Browsable(false)]
        public MetroStyleManager StyleManager { get; set; }

        [DefaultValue(true)]
        [Browsable(false)]
        public new bool ShowAlways
        {
            get
            {
                return base.ShowAlways;
            }
            set
            {
                base.ShowAlways = true;
            }
        }

        [Browsable(false)]
        [DefaultValue(true)]
        public new bool OwnerDraw
        {
            get
            {
                return base.OwnerDraw;
            }
            set
            {
                base.OwnerDraw = true;
            }
        }

        [Browsable(false)]
        public new bool IsBalloon
        {
            get
            {
                return base.IsBalloon;
            }
            set
            {
                base.IsBalloon = false;
            }
        }

        [Browsable(false)]
        public new Color BackColor
        {
            get
            {
                return base.BackColor;
            }
            set
            {
                base.BackColor = value;
            }
        }

        [Browsable(false)]
        public new Color ForeColor
        {
            get
            {
                return base.ForeColor;
            }
            set
            {
                base.ForeColor = value;
            }
        }

        [Browsable(false)]
        public new string ToolTipTitle
        {
            get
            {
                return base.ToolTipTitle;
            }
            set
            {
                base.ToolTipTitle = "";
            }
        }

        [Browsable(false)]
        public new ToolTipIcon ToolTipIcon
        {
            get
            {
                return base.ToolTipIcon;
            }
            set
            {
                base.ToolTipIcon = ToolTipIcon.None;
            }
        }

        public MetroToolTip()
        {
            OwnerDraw = true;
            ShowAlways = true;
            base.Draw += MetroToolTip_Draw;
            base.Popup += MetroToolTip_Popup;
        }

        public new void SetToolTip(Control control, string caption)
        {
            base.SetToolTip(control, caption);
            if (control is IMetroControl)
            {
                foreach (Control control2 in control.Controls)
                {
                    SetToolTip(control2, caption);
                }
            }
        }

        private void MetroToolTip_Popup(object sender, PopupEventArgs e)
        {
            if (e.AssociatedWindow is IMetroForm)
            {
                Style = ((IMetroForm)e.AssociatedWindow).Style;
                Theme = ((IMetroForm)e.AssociatedWindow).Theme;
                StyleManager = ((IMetroForm)e.AssociatedWindow).StyleManager;
            }
            else if (e.AssociatedControl is IMetroControl)
            {
                Style = ((IMetroControl)e.AssociatedControl).Style;
                Theme = ((IMetroControl)e.AssociatedControl).Theme;
                StyleManager = ((IMetroControl)e.AssociatedControl).StyleManager;
            }
            e.ToolTipSize = new Size(e.ToolTipSize.Width + 24, e.ToolTipSize.Height + 9);
        }

        private void MetroToolTip_Draw(object sender, DrawToolTipEventArgs e)
        {
            MetroThemeStyle theme = (Theme != MetroThemeStyle.Light) ? MetroThemeStyle.Light : MetroThemeStyle.Dark;
            Color color = MetroPaint.BackColor.Form(theme);
            Color color2 = MetroPaint.BorderColor.Button.Normal(theme);
            Color foreColor = MetroPaint.ForeColor.Label.Normal(theme);
            using (SolidBrush brush = new SolidBrush(color))
            {
                e.Graphics.FillRectangle(brush, e.Bounds);
            }
            using (Pen pen = new Pen(color2))
            {
                e.Graphics.DrawRectangle(pen, new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width - 1, e.Bounds.Height - 1));
            }
            Font font = MetroFonts.Default(13f);
            TextRenderer.DrawText(e.Graphics, e.ToolTipText, font, e.Bounds, foreColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
        }
    }
}
