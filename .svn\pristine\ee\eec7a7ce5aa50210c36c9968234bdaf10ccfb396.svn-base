using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class BLANK : Record
	{
		public ushort RowIndex;

		public ushort ColIndex;

		public ushort XFIndex;

		public BLANK(Record record)
			: base(record)
		{
		}

		public BLANK()
		{
			Type = 513;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			RowIndex = binaryReader.ReadUInt16();
			ColIndex = binaryReader.ReadUInt16();
			XFIndex = binaryReader.ReadUInt16();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(RowIndex);
			binaryWriter.Write(ColIndex);
			binaryWriter.Write(XFIndex);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
