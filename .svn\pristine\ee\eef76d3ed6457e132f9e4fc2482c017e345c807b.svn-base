using OCRTools.Common;
using OCRTools.Language;
using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FormViewImage : BaseForm
    {

        private UcContent ucContent;

        // 记录窗口尺寸状态
        private Size _lastNormalSize;
        private bool _sizeChangeInProgress = false;

        public FormViewImage()
        {
            Icon = FrmMain.FrmTool.Icon;
            Text = $"{"图像预览".CurrentText()}-{CommonString.FullName.CurrentText()}";
            ShowIcon = true;
            ShowInTaskbar = true;
            ucContent = new UcContent
            {
                Dock = DockStyle.Fill,
                IsShowOldContent = false,
                IsShowToolBox = true,
                IsShowTxt = false,
                Margin = CommonString.PaddingZero,
                Padding = CommonString.PaddingZero,
                Location = new Point(0, 0),
                Name = "ucContent",
                TabIndex = 0,
                TabStop = false,
                IsViewMore = true
            };
            Controls.Add(ucContent);
            InitializeComponent();
            Shown += FormViewImage_Shown;
            FormClosing += FormViewImage_FormClosing;
        }

        private void FormViewImage_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                ucContent.SetImageZoomSmall();
            }
            catch { }
            MemoryManager.ClearMemory();
        }

        private void FormViewImage_Shown(object sender, EventArgs e)
        {
            Task.Run(() => {
                BeginInvoke(new Action(() => {
                    ucContent.SetImageZoomBig(true);
                }));
            });
            SizeChanged += FormViewImage_SizeChanged;
            this.ForceActivate();
        }

        // 添加双缓冲支持，减少闪烁
        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams cp = base.CreateParams;
                cp.ExStyle |= 0x02000000; // WS_EX_COMPOSITED
                return cp;
            }
        }

        private void FormViewImage_SizeChanged(object sender, EventArgs e)
        {
            FormBorderStyle = WindowState == FormWindowState.Maximized ? FormBorderStyle.None : FormBorderStyle.Sizable;
            if (WindowState == FormWindowState.Normal && !_sizeChangeInProgress)
            {
                if (_lastNormalSize != Size)
                {
                    SetImageMode(ucContent.Image);
                    _lastNormalSize = Size;
                }
            }
        }

        private void SetImageMode(Image image, bool isInit = false)
        {
            if (image == null) return;

            try
            {
                _sizeChangeInProgress = true;
                
                var zoom = isInit ? 100 : ucContent.Zoom;
                var width = image.Width * zoom / 100 + (Width - ClientRectangle.Width);
                var height = image.Height * zoom / 100 + (Height - ClientRectangle.Height);

                var minWidth = Screen.PrimaryScreen.WorkingArea.Width * 0.282;
                var minHeight = Screen.PrimaryScreen.WorkingArea.Height * 0.530;
                if (width < minWidth && height < minHeight)
                {
                    width = (int)Math.Max(width, minWidth);
                    height = (int)Math.Max(height, minHeight);
                }

                if (width > Screen.PrimaryScreen.WorkingArea.Width)
                {
                    height += SystemInformation.HorizontalScrollBarArrowWidth;
                }
                if (height > Screen.PrimaryScreen.WorkingArea.Height)
                {
                    width += SystemInformation.VerticalScrollBarWidth;
                }
                width = Math.Min(width, Screen.PrimaryScreen.WorkingArea.Width);
                height = Math.Min(height, Screen.PrimaryScreen.WorkingArea.Height);

                // 避免微小变化触发多次调整
                if (!isInit && Math.Abs(width - Width) < 10 && Math.Abs(height - Height) < 10)
                    return;

                // 直接设置大小，不使用动画以避免多次触发事件
                Size = new Size(width, height);
                _lastNormalSize = Size; // 记住这个大小

                // 居中窗口
                Location = new Point(
                    (Screen.PrimaryScreen.WorkingArea.Width - width) / 2, 
                    (Screen.PrimaryScreen.WorkingArea.Height - height) / 2);
            }
            finally
            {
                _sizeChangeInProgress = false;
            }
        }

        internal void Bind(Image image, string fileName = null)
        {
            if (image == null && !string.IsNullOrEmpty(fileName))
            {
                image = Image.FromFile(fileName);
            }
            Text = $"{"图像预览".CurrentText()} {"尺寸".CurrentText()}:{image?.Width}×{image?.Height} - {CommonString.FullName.CurrentText()}";
            ucContent.BindImage(image, true, CommonSetting.图片自动缩放);
            ucContent.NowDisplayMode = DisplayModel.图片模式;
            ucContent.RefreshStyle();
            ucContent.BindContentByOcr(FrmMain.EmptyContent);
            ucContent.SetCanClose();
            
            // 重置窗口状态
            _lastNormalSize = Size.Empty;
            _sizeChangeInProgress = false;
            
            Location = new Point((int)(Screen.PrimaryScreen.WorkingArea.Width * 0.359), (int)(Screen.PrimaryScreen.WorkingArea.Height * 0.235));
            SetImageMode(ucContent.Image, true);
            BackColor = ucContent.ImageBoxBackColor;
        }

        private void FrmPicCompare_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape) Close();
        }

        private void FormViewImage_Load(object sender, EventArgs e)
        {
            FormBorderStyle = FormBorderStyle.None;
            ucContent.SetImageMode(true);
            ucContent.ShowImageTool();
        }
    }
}
