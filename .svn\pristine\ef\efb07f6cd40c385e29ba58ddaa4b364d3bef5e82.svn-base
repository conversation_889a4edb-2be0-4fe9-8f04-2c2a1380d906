// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class InvokePattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = InvokePatternIdentifiers.Pattern;


        private InvokePattern(AutomationElement el, IUIAutomationInvokePattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new InvokePattern(el, (IUIAutomationInvokePattern)pattern, cached);
        }
    }
}