﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text.RegularExpressions;

namespace OCRTools
{
    public class DnsHelper
    {
        private static readonly Dictionary<string, string> dicServers = new Dictionary<string, string>();

        public static string GetDnsIp(string host)
        {
            var result = "";
            try
            {
                if (!string.IsNullOrEmpty(host) && !IsIPv4(host))
                {
                    result = GetDnsCache(host);

                    if (string.IsNullOrEmpty(result))
                    {
                        if (string.IsNullOrEmpty(result)) result = ToIPAddress(host);

                        if (!IsIPv4(result)) result = "";

                        if (string.IsNullOrEmpty(result)) result = GetDnsFromNsLookUp(host, "************");

                        if (!IsIPv4(result)) result = "";

                        if (string.IsNullOrEmpty(result)) result = GetDnsFromNsLookUp(host, "*********");

                        if (!IsIPv4(result)) result = "";

                        if (string.IsNullOrEmpty(result)) result = GetDnsFromNsLookUp(host, "***************");

                        if (!IsIPv4(result)) result = "";

                        if (!string.IsNullOrEmpty(result))
                            if (!dicServers.ContainsKey(host))
                                dicServers.Add(host, result);
                    }
                }
            }
            catch
            {
            }

            return result;
        }

        public static string ToIPAddress(string hostNameOrAddress, bool favorIpV6 = false)
        {
            var favoredFamily = favorIpV6 ? AddressFamily.InterNetworkV6 : AddressFamily.InterNetwork;
            var addrs = Dns.GetHostAddresses(hostNameOrAddress);
            return addrs.FirstOrDefault(addr => addr.AddressFamily == favoredFamily).ToString() ??
                   addrs.FirstOrDefault().ToString();
        }

        public static string GetDnsFromNsLookUp(string strHost, string strNsServer = "")
        {
            var result = "";
            var strTmp = ExecCmd(string.Format("nslookup {0} {1}", strHost, strNsServer));
            if (strTmp.Contains(strHost))
                if (strTmp.IndexOf(strHost) != strTmp.LastIndexOf(strHost))
                {
                    strTmp = strTmp.Substring(strTmp.LastIndexOf(strHost) + strHost.Length);
                    result = CommonMethod.SubString(strTmp, ":", "\n").Trim();
                }
            //ConfigHelper._Log.Info(strHost + "[" + result + "]");

            return result;
        }

        private static string GetDnsCache(string strHost)
        {
            var cache = "";
            if (dicServers.ContainsKey(strHost)) cache = dicServers[strHost];
            return cache;
        }

        public static string ExecCmd(string str)
        {
            //process用于调用外部程序
            using (var p = new Process())
            {
                //调用cmd.exe
                p.StartInfo.FileName = "cmd.exe";
                //不显示程序窗口
                p.StartInfo.CreateNoWindow = true;
                //是否指定操作系统外壳进程启动程序
                p.StartInfo.UseShellExecute = false;
                //可能接受来自调用程序的输入信息
                //重定向标准输入
                p.StartInfo.RedirectStandardInput = true;
                //重定向标准输出
                p.StartInfo.RedirectStandardOutput = true;
                //重定向错误输出
                p.StartInfo.RedirectStandardError = true;
                //启动程序
                p.Start();
                //输入命令
                p.StandardInput.WriteLine(str);
                //一定要关闭。
                p.StandardInput.WriteLine("exit");
                return p.StandardOutput.ReadToEnd();
            }
        }

        public static bool IsIPv4(string input)
        {
            var array = input.Split('.');
            if (array?.Length != 4) return false;
            for (var i = 0; i < array.Length; i++)
            {
                if (!IsMatch("^\\d+$", array[i])) return false;
                if (Convert.ToUInt16(array[i]) > 255) return false;
            }

            return true;
        }

        public static bool IsMatch(string pattern, string input)
        {
            if (input == null || input == "") return false;
            var regex = new Regex(pattern);
            return regex.IsMatch(input);
        }
    }
}