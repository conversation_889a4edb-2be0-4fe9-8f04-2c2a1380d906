using MetroFramework.Components;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using MetroFramework.Native;
using OCRTools;
using OCRTools.Common;
using OCRTools.OtherExt.MetroFramework.Forms;
using OCRTools.Properties;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Drawing.Text;
using System.Linq;
using System.Reflection;
using System.Security;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using static OCRTools.OtherExt.MetroFramework.Forms.ImageAnimatorHelper;

namespace MetroFramework.Forms
{
    public class MetroForm : BaseForm, IMetroForm
    {
        private bool _displayHeader = true;

        private MetroColorStyle _metroStyle = MetroCommonStyle.DefaultStyle;

        private MetroThemeStyle _metroTheme = MetroThemeStyle.Light;

        private Form _shadowForm;
        private MetroFormShadowType _shadowType = MetroFormShadowType.Flat;

        private Dictionary<WindowButtons, List<Control>> _windowButtonList = new Dictionary<WindowButtons, List<Control>>();

        public MetroForm()
        {
            SetStyle(
                ControlStyles.UserPaint |
                //ControlStyles.AllPaintingInWmPaint |
                ControlStyles.ResizeRedraw |
                ControlStyles.DoubleBuffer |
                ControlStyles.OptimizedDoubleBuffer |
                ControlStyles.ContainerControl |
                ControlStyles.SupportsTransparentBackColor
                , true);

            FormBorderStyle = FormBorderStyle.None;
            Name = "MetroForm";
            StartPosition = FormStartPosition.CenterScreen;
            TransparencyKey = Color.Lavender;
            AutoScaleMode = CommonString.CommonAutoScaleMode;

            var newUnit = CommonString.CommonGraphicsUnit();
            if (Font.Unit != newUnit)
            {
                var size = newUnit == GraphicsUnit.Pixel ? Font.Size * 1.33F : Font.Size / 1.33F;
                Font = new Font(Font.FontFamily.Name, size, Font.Style, newUnit);
            }
            //Font = CommonString.GetSysNormalFont(12f);
        }

        public virtual void OnThemeChange()
        {

        }

        [Category("Metro Appearance")]
        [Browsable(true)]
        public MetroFormTextAlign TextAlign { get; set; }

        [Browsable(false)] public override Color BackColor => MetroPaint.BackColor.Form(Theme);

        private const int borderWidth = 5;

        [Category("Metro Appearance")] public bool Movable { get; set; } = true;

        public new bool TopMost
        {
            get => base.TopMost;
            set
            {
                base.TopMost = value;
            }
        }

        public new Padding Padding
        {
            get => base.Padding;
            set
            {
                value.Top = Math.Max((int)(value.Top * CommonTheme.DpiScale), DefaultPadding.Top);
                base.Padding = value;
            }
        }

        protected override Padding DefaultPadding => new Padding((int)(20 * CommonTheme.DpiScale), _displayHeader ? (int)(45 * CommonTheme.DpiScale) : (int)(20 * CommonTheme.DpiScale), (int)(20 * CommonTheme.DpiScale), (int)(20 * CommonTheme.DpiScale));

        [Category("Metro Appearance")]
        [DefaultValue(true)]
        public bool DisplayHeader
        {
            get => _displayHeader;
            set
            {
                if (!Equals(value, _displayHeader))
                {
                    _displayHeader = value;
                    base.Padding = DefaultPadding;
                }
            }
        }

        [Category("Metro Appearance")] public bool Resizable { get; set; } = true;

        [DefaultValue(MetroFormShadowType.Flat)]
        [Category("Metro Appearance")]
        public MetroFormShadowType ShadowType
        {
            get
            {
                if (!IsMdiChild) return _shadowType;
                return MetroFormShadowType.None;
            }
            set => _shadowType = value;
        }

        [Browsable(false)]
        public new FormBorderStyle FormBorderStyle
        {
            get => base.FormBorderStyle;
            set => base.FormBorderStyle = value;
        }

        protected override CreateParams CreateParams
        {
            get
            {
                var createParams = base.CreateParams;
                // 确保窗体绘制正确处理DPI缩放
                createParams.ExStyle |= 0x02000000; // WS_EX_COMPOSITED
                createParams.Style |= 131072;
                if (ShadowType == MetroFormShadowType.SystemShadow) createParams.ClassStyle |= 131072;
                return createParams;
            }
        }

        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                if (StyleManager != null) return StyleManager.Style;
                return _metroStyle;
            }
            set => _metroStyle = value;
        }

        [Category("Metro Appearance")]
        public MetroThemeStyle Theme
        {
            get
            {
                if (StyleManager != null) return StyleManager.Theme;
                return _metroTheme;
            }
            set
            {
                _metroTheme = value;
            }
        }

        [Browsable(false)]
        public MetroStyleManager StyleManager { get; set; } = CommonTheme.StyleManager;

        protected override void Dispose(bool disposing)
        {
            if (disposing) RemoveShadow();
            base.Dispose(disposing);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            if (!Size.IsValidate())
                return;
            e.Graphics.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
            e.Graphics.InterpolationMode = InterpolationMode.High;
            e.Graphics.CompositingQuality = CompositingQuality.HighQuality;

            var backColor = MetroPaint.BackColor.Form(Theme);
            e.Graphics.Clear(backColor);

            using (var brush = MetroPaint.GetStyleBrush(Style))
            {
                var topRect = new Rectangle(0, 0, Width, borderWidth);
                e.Graphics.FillRectangle(brush, topRect);
            }

            //Draw Border
            var borderColor = MetroPaint.BorderColor.Form(Theme);
            using (var pen = new Pen(borderColor))
            {
                e.Graphics.DrawLines(pen, new[]
                {
                        new Point(0, borderWidth),
                        new Point(0, Height - 1),
                        new Point(Width - 1, Height - 1),
                        new Point(Width - 1, borderWidth)
                });
            }

            e.Graphics.SetHighQuality();
            if (_displayHeader && !string.IsNullOrEmpty(Text))
            {
                var titleSize = new Size(ClientRectangle.Width, Padding.Top);
                var baseFont = CommonString.GetSysNormalFont(23f);
                var titleFont = CommonMethod.ScaleLabelByHeight(Text, baseFont, titleSize);
                if (titleFont.Size > baseFont.Size)
                {
                    titleFont = baseFont;
                }
                var bounds = new Rectangle(Padding.Left, 0, titleSize.Width, titleSize.Height - 2 + Padding.Bottom);
                if (CommonSetting.夜间模式)
                    bounds.Y = (bounds.Height + Padding.Top) / 2 - bounds.Height + 2;
                TextRenderer.DrawText(e.Graphics, Text, titleFont, bounds, MetroPaint.ForeColor.Title(Theme), TextFormatFlags.EndEllipsis | TextFormatFlags.Left | TextFormatFlags.VerticalCenter);
                //foreach (var item in _windowButtonList.Values)
                //{
                //    foreach (var ctrl in item)
                //    {
                //        if (ctrl is Button btn && btn.AutoSize && btn.Font.Size > titleFont.Size)
                //        {
                //            btn.Font = titleFont;
                //            UpdateWindowButtonPosition();
                //            break;
                //        }
                //    }
                //}
            }

            if (Resizable && (SizeGripStyle == SizeGripStyle.Auto || SizeGripStyle == SizeGripStyle.Show))
                using (var brush2 = new SolidBrush(MetroPaint.ForeColor.Button.Disabled(Theme)))
                {
                    var size = new Size(2, 2);
                    e.Graphics.FillRectangles(brush2, new[]
                    {
                        new Rectangle(new Point(ClientRectangle.Width - 6, ClientRectangle.Height - 6), size),
                        new Rectangle(new Point(ClientRectangle.Width - 10, ClientRectangle.Height - 10), size),
                        new Rectangle(new Point(ClientRectangle.Width - 10, ClientRectangle.Height - 6), size),
                        new Rectangle(new Point(ClientRectangle.Width - 6, ClientRectangle.Height - 10), size),
                        new Rectangle(new Point(ClientRectangle.Width - 14, ClientRectangle.Height - 6), size),
                        new Rectangle(new Point(ClientRectangle.Width - 6, ClientRectangle.Height - 14), size)
                    });
                }
        }

        protected override void OnClosed(EventArgs e)
        {
            if (Owner != null) Owner = null;
            RemoveShadow();
            base.OnClosed(e);
        }

        public AutoSizeFormClass InitForm { get; set; } = new AutoSizeFormClass();

        private bool IsInitFinish = false;

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            if (DesignMode) return;

            RemoveCloseButton();
            if (ControlBox)
            {
                AddWindowButton(WindowButtons.Close);
                if (MaximizeBox) AddWindowButton(WindowButtons.Maximize);
                if (MinimizeBox) AddWindowButton(WindowButtons.Minimize);
            }

            InitForm.controllInitializeSize(this);

            HighDpiHelper.AdjustControlImagesDpiScale(this);

            switch (StartPosition)
            {
                case FormStartPosition.CenterParent:
                    CenterToParent();
                    break;
                case FormStartPosition.CenterScreen:
                    if (IsMdiChild)
                        CenterToParent();
                    else
                        CenterToScreen();
                    break;
            }
            CreateShadow();
            IsInitFinish = true;
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
            OnThemeChange();
            UpdateWindowButtonPosition();
            this.AutoSizeMutilScreen();
            HighDpiHelper.AttachMouseMoveEvent(this);
        }

        protected override void OnActivated(EventArgs e)
        {
            base.OnActivated(e);
            if (IsInitFinish)
                this.RefreshDpiScale();
            if (_shadowType == MetroFormShadowType.AeroShadow && IsAeroThemeEnabled() && IsDropShadowSupported())
            {
                var attrValue = 2;
                DwmApi.DwmSetWindowAttribute(Handle, 2, ref attrValue, 4);
                var mArgins = new DwmApi.Margins
                {
                    cyBottomHeight = 1,
                    cxLeftWidth = 0,
                    cxRightWidth = 0,
                    cyTopHeight = 0
                };

                DwmApi.DwmExtendFrameIntoClientArea(Handle, ref mArgins);
            }
            if (Owner != null && Owner.TopMost && !TopMost)
            {
                this.TopMost = Owner.TopMost;
            }
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);
            Invalidate();
        }

        protected override void OnResizeEnd(EventArgs e)
        {
            base.OnResizeEnd(e);
            UpdateWindowButtonPosition();
        }

        private System.Threading.Timer displayChangeTimer;

        protected override void WndProc(ref Message m)
        {
            if (DesignMode)
            {
                base.WndProc(ref m);
                return;
            }

            switch (m.Msg)
            {
                case (int)Messages.WM_DISPLAYCHANGE:
                    displayChangeTimer = new System.Threading.Timer(
                         _ =>
                        {
                            CommonMethod.DetermineCall(this, () =>
                            {
                                this.RefreshDpiScale(true);
                            });
                        },
                        null,
                        new TimeSpan(0, 0, 3),
                        Timeout.InfiniteTimeSpan
                    );
                    break;
                case (int)Messages.WM_SYSCOMMAND:
                    int sc = m.WParam.ToInt32() & 0xFFF0;
                    switch (sc)
                    {
                        case (int)Messages.SC_MOVE:
                            if (!Movable) return;
                            break;

                        case (int)Messages.SC_MAXIMIZE:
                            break;
                        case (int)Messages.SC_RESTORE:
                            break;
                    }
                    break;

                case (int)Messages.WM_NCLBUTTONDBLCLK:
                case (int)Messages.WM_LBUTTONDBLCLK:
                    if (!MaximizeBox) return;
                    break;
                case (int)Messages.WM_NCHITTEST:
                    WinApi.HitTest ht = HitTestNca(m.HWnd, m.WParam, m.LParam);
                    if (ht != WinApi.HitTest.HTCLIENT)
                    {
                        m.Result = (IntPtr)ht;
                        return;
                    }
                    break;
                case (int)Messages.WM_DWMCOMPOSITIONCHANGED:
                    break;
            }

            base.WndProc(ref m);
            switch (m.Msg)
            {
                case (int)Messages.WM_GETMINMAXINFO:
                    OnGetMinMaxInfo(m.HWnd, m.LParam);
                    break;
                case (int)Messages.WM_SIZE:
                    {
                        if (_shadowForm != null)
                        {
                            _shadowForm.Visible = WindowState != FormWindowState.Maximized && Visible && Opacity > 0;
                        }
                        _windowButtonList.TryGetValue(WindowButtons.Maximize, out var value);
                        if (value == null || value.Count <= 0) break;
                        value.FirstOrDefault().BackgroundImage = null;
                        break;
                    }
            }
        }

        [SecuritySafeCritical]
        private unsafe void OnGetMinMaxInfo(IntPtr hwnd, IntPtr lParam)
        {
            var ptr = (WinApi.MINMAXINFO*)(void*)lParam;
            var screen = Screen.FromHandle(hwnd);
            ptr->ptMaxSize.x = screen.WorkingArea.Width;
            ptr->ptMaxSize.y = screen.WorkingArea.Height;
            ptr->ptMaxPosition.x = Math.Abs(screen.WorkingArea.Left - screen.Bounds.Left);
            ptr->ptMaxPosition.y = Math.Abs(screen.WorkingArea.Top - screen.Bounds.Top);
        }

        private WinApi.HitTest HitTestNca(IntPtr hwnd, IntPtr wparam, IntPtr lparam)
        {
            Point vPoint = new Point((short)lparam, (short)((int)lparam >> 16));
            int vPadding = Math.Max(Padding.Right, Padding.Bottom);

            if (Resizable)
            {
                if (RectangleToScreen(new Rectangle(ClientRectangle.Width - vPadding, ClientRectangle.Height - vPadding, vPadding, vPadding)).Contains(vPoint))
                    return WinApi.HitTest.HTBOTTOMRIGHT;
            }

            if (RectangleToScreen(new Rectangle(borderWidth, borderWidth, ClientRectangle.Width - 2 * borderWidth, 50)).Contains(vPoint))
                return WinApi.HitTest.HTCAPTION;

            return WinApi.HitTest.HTCLIENT;
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            base.OnMouseDown(e);
            if (e.Button == MouseButtons.Left && Movable && WindowState != FormWindowState.Maximized &&
                Width - borderWidth > e.Location.X && e.Location.X > borderWidth && e.Location.Y > borderWidth) MoveControl();
        }

        [SecuritySafeCritical]
        private void MoveControl()
        {
            WinApi.ReleaseCapture();
            WinApi.SendMessage(Handle, (int)Messages.WM_NCLBUTTONDOWN, (int)WinApi.HitTest.HTCAPTION, 0);
        }

        [SecuritySafeCritical]
        private bool IsAeroThemeEnabled()
        {
            if (Environment.OSVersion.Version.Major <= 5) return false;
            DwmApi.DwmIsCompositionEnabled(out var pfEnabled);
            return pfEnabled;
        }

        private bool IsDropShadowSupported()
        {
            return Environment.OSVersion.Version.Major > 5 && SystemInformation.IsDropShadowEnabled;
        }

        internal Control AddWindowButton(WindowButtons button, EventHandler click = null, bool canInverse = true, Image image = null)
        {
            var metroFormButton = new MetroFormButton
            {
                BackgroundImageLayout = ImageLayout.Stretch,
                Style = Style,
                Theme = Theme,
                Tag = button,
                TabStop = false,
                Anchor = AnchorStyles.Top | AnchorStyles.Right,
                Size = button == WindowButtons.Top ? new Size(30, 25) : new Size(25, 25),
                Font = Font,
                CanInverse = canInverse,
                Visible = false,
                Location = new Point(-100, -100)
            };
            if (image != null)
            {
                var size = image.Size;
                metroFormButton.BackgroundImage = image;
                metroFormButton.Size = new Size((int)(size.Width * 1.5), (int)(size.Height * 1.5));
            }
            metroFormButton.BaseSize = metroFormButton.Size;
            metroFormButton.Click += click ?? WindowButton_Click;

            Controls.Add(metroFormButton);
            if (_windowButtonList.ContainsKey(button))
            {
                _windowButtonList[button].Add(metroFormButton);
            }
            else
                _windowButtonList.Add(button, new List<Control> { metroFormButton });

            return metroFormButton;
        }

        internal Button AddCustomButton(string text, Image image, float fontSize, EventHandler click = null, string foreColor = "DarkGray")
        {
            var btn = new Button
            {
                AutoSize = true,
                Size = new Size(1, 1),
                Text = text,
                Image = image,
                ImageAlign = ContentAlignment.MiddleCenter,
                TextAlign = ContentAlignment.MiddleRight,
                TextImageRelation = TextImageRelation.ImageBeforeText,
                FlatStyle = FlatStyle.Flat,
                Anchor = AnchorStyles.Top | AnchorStyles.Right,
                Padding = Padding.Empty,
                Margin = Padding.Empty,
                TabStop = false,
                Cursor = Cursors.Hand,
                Font = CommonString.GetSysNormalFont(fontSize),
                ForeColor = string.IsNullOrEmpty(foreColor) ? Color.DarkGray : Color.FromName(foreColor),
                Visible = false,
                Location = new Point(-100, -100)
            };
            btn.FlatAppearance.BorderSize = 0;
            btn.Click += click;
            btn.TextChanged += (obj, erg) =>
            {
                UpdateWindowButtonPosition();
            };
            CommonMethod.SetStyle(btn, ControlStyles.Selectable, false);

            AddCustomControl(WindowButtons.Custom, btn);
            return btn;
        }

        private void AddCustomControl(WindowButtons button, Control ctrl)
        {
            if (!_windowButtonList.ContainsKey(button))
            {
                _windowButtonList.Add(button, new List<Control>());
            }
            var lstControl = _windowButtonList[button];
            Controls.Add(ctrl);
            lstControl.Add(ctrl);
            _windowButtonList[button] = lstControl;
        }

        private void WindowButton_Click(object sender, EventArgs e)
        {
            var metroFormButton = sender as MetroFormButton;
            if (metroFormButton == null) return;
            switch ((WindowButtons)metroFormButton.Tag)
            {
                case WindowButtons.Close:
                    Close();
                    break;
                case WindowButtons.Minimize:
                    WindowState = FormWindowState.Minimized;
                    break;
                case WindowButtons.Maximize:
                    metroFormButton.BackgroundImage = null;
                    WindowState = WindowState == FormWindowState.Normal ? FormWindowState.Maximized : FormWindowState.Normal;
                    break;
            }
        }

        public Rectangle LeftButtonRectangle { get; set; }

        internal void UpdateWindowButtonImage()
        {
            foreach (var item in _windowButtonList)
            {
                if (item.Key.Equals(WindowButtons.Custom))
                {
                    continue;
                }
                item.Value.ForEach(p =>
                {
                    p.BackgroundImage = null;
                });
            }
        }

        internal void UpdateWindowButtonPosition()
        {
            // 暂停布局逻辑，防止多次重绘
            SuspendLayout();

            var firstButtonLocation = new Point(ClientRectangle.Width - borderWidth, borderWidth);
            var lastDrawedButtonPosition = firstButtonLocation.X;

            // 先将所有按钮设为不可见，避免闪烁
            foreach (var item in _windowButtonList)
            {
                foreach (var ctrl in item.Value)
                {
                    ctrl.Visible = false;
                }
            }

            // 计算所有按钮的位置，保持原有的排序
            foreach (var item in _windowButtonList.OrderBy(p => p.Key.GetHashCode()))
            {
                foreach (var ctrl in item.Value)
                {
                    if (ctrl is Button btn && btn.AutoSize)
                    {
                        btn.Size = new Size();
                    }
                    if (ctrl is MetroFormButton formButton)
                    {
                        formButton.SetBackGroundImage();
                    }

                    // 设置位置
                    ctrl.Location = new Point(lastDrawedButtonPosition - ctrl.Width, borderWidth);
                    if (item.Key == WindowButtons.Menu || item.Key == WindowButtons.Dark)
                    {
                        ctrl.Top += 3;
                    }
                    else if (item.Key == WindowButtons.Top)
                    {
                        ctrl.Top += 4;
                    }
                    lastDrawedButtonPosition -= ctrl.Width;
                    LeftButtonRectangle = ctrl.Bounds;
                }
            }

            // 所有位置计算完毕后，一次性显示所有按钮并置于前景
            foreach (var item in _windowButtonList)
            {
                foreach (var ctrl in item.Value)
                {
                    ctrl.BringToFront();
                    ctrl.Visible = true;
                }
            }

            // 恢复布局逻辑并刷新
            ResumeLayout();
            Refresh();
        }

        private void CreateShadow()
        {
            switch (ShadowType)
            {
                case MetroFormShadowType.None:
                    break;
                case MetroFormShadowType.Flat:
                    _shadowForm = new MetroFlatDropShadow(this);
                    break;
                case MetroFormShadowType.DropShadow:
                    _shadowForm = new MetroRealisticDropShadow(this);
                    break;
            }
        }

        private void RemoveShadow()
        {
            if (_shadowForm == null || _shadowForm.IsDisposed) return;

            _shadowForm.Visible = false;
            Owner = _shadowForm.Owner;
            _shadowForm.Owner = null;
            _shadowForm.Dispose();
            _shadowForm = null;
        }

        [SecuritySafeCritical]
        public void RemoveCloseButton()
        {
            var systemMenu = WinApi.GetSystemMenu(Handle, false);
            if (systemMenu == IntPtr.Zero) return;

            int menuItemCount = WinApi.GetMenuItemCount(systemMenu);
            if (menuItemCount <= 0) return;
            WinApi.RemoveMenu(systemMenu, (uint)(menuItemCount - 1), 5120u);
            WinApi.RemoveMenu(systemMenu, (uint)(menuItemCount - 2), 5120u);
            WinApi.DrawMenuBar(Handle);
        }

        internal enum WindowButtons
        {
            Close = 0,
            Maximize = 1,
            Minimize = 2,
            Menu = 3,
            Top = 4,
            Dark = 5,
            Plugin = 6,
            Custom = 7,
        }

        internal class MetroFormButton : Button, IMetroControl
        {
            private bool _isHovered;

            private bool _isPressed;
            private MetroColorStyle _metroStyle;

            private MetroThemeStyle _metroTheme;

            public Size BaseSize { get; set; }

            public bool CanInverse { get; set; }

            public MetroFormButton()
            {
                SetStyle(
                    ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.AllPaintingInWmPaint |
                    ControlStyles.OptimizedDoubleBuffer | ControlStyles.DoubleBuffer, true);
            }

            [Category("Metro Appearance")]
            [DefaultValue(MetroCommonStyle.DefaultStyle)]
            public MetroColorStyle Style
            {
                get
                {
                    if (DesignMode || _metroStyle != 0) return _metroStyle;
                    if (StyleManager != null) return StyleManager.Style;
                    if (StyleManager == null) return MetroCommonStyle.DefaultStyle;
                    return _metroStyle;
                }
                set => _metroStyle = value;
            }

            [Category("Metro Appearance")]
            [DefaultValue(MetroThemeStyle.Light)]
            public MetroThemeStyle Theme
            {
                get
                {
                    if (DesignMode || _metroTheme != 0) return _metroTheme;
                    if (StyleManager != null) return StyleManager.Theme;
                    if (StyleManager == null) return MetroThemeStyle.Light;
                    return _metroTheme;
                }
                set
                {
                    _metroTheme = value;
                }
            }

            [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
            [Browsable(false)]
            public MetroStyleManager StyleManager { get; set; }

            protected override void OnPaint(PaintEventArgs e)
            {
                var theme = Theme;
                var style = Style;
                Color backColor;
                if (Parent != null)
                {
                    if (!(Parent is IMetroForm form))
                    {
                        backColor = !(Parent is IMetroControl) ? Parent.BackColor : MetroPaint.GetStyleColor(Style);
                    }
                    else
                    {
                        theme = form.Theme;
                        style = form.Style;
                        backColor = MetroPaint.BackColor.Form(theme);
                    }
                }
                else
                {
                    backColor = MetroPaint.BackColor.Form(theme);
                }

                if (_isHovered && !_isPressed && Enabled)
                {
                    backColor = MetroPaint.BackColor.Button.Normal(theme);
                }
                else if (_isHovered && _isPressed && Enabled)
                {
                    backColor = MetroPaint.GetStyleColor(style);
                }
                else if (!Enabled)
                {
                    backColor = MetroPaint.BackColor.Button.Disabled(theme);
                }

                e.Graphics.Clear(backColor);
                e.Graphics.InterpolationMode = InterpolationMode.HighQualityBilinear;
                e.Graphics.CompositingQuality = CompositingQuality.HighQuality;
                e.Graphics.SmoothingMode = SmoothingMode.HighQuality;
                e.Graphics.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
                SetBackGroundImage();
                if (BackgroundImage != null)
                {
                    //CommonMethod.ShowHelpMsg(Tag?.ToString());
                    var size = BackgroundImage.Size;
                    e.Graphics.DrawImage(BackgroundImage, new PointF((Width - size.Width) / 2, (Height - size.Height) / 2));
                }
            }

            internal void SetBackGroundImage()
            {
                if (BackgroundImage == null && Tag != null && Tag is WindowButtons button)
                {
                    BackgroundImage = GetWindowsButtonImage(button);
                }
            }

            internal Bitmap GetWindowsButtonImage(WindowButtons button)
            {
                Bitmap image = null;
                switch (button)
                {
                    case WindowButtons.Close:
                        image = CommonSetting.夜间模式 ? Resources.ic_close_dark : Resources.ic_close;
                        break;
                    case WindowButtons.Minimize:
                        image = CommonSetting.夜间模式 ? Resources.ic_smallest_dark : Resources.ic_smallest;
                        break;
                    case WindowButtons.Maximize:
                        if (FindForm()?.WindowState == FormWindowState.Normal)
                            image = CommonSetting.夜间模式 ? Resources.ic_largest_dark : Resources.ic_largest;
                        else
                            image = CommonSetting.夜间模式 ? Resources.ic_recover_dark : Resources.ic_recover;
                        break;
                    case WindowButtons.Menu:
                        image = CommonSetting.夜间模式 ? Resources.menu_dark : Resources.menu;
                        break;
                    case WindowButtons.Top:
                        var topMost = FindForm().TopMost;
                        image = CommonSetting.夜间模式 ? (topMost ? Resources.top_dark : Resources.untop_dark) : (topMost ? Resources.top : Resources.untop);
                        break;
                    case WindowButtons.Dark:
                        image = CommonSetting.夜间模式 ? Resources.sun : Resources.sun_dark;
                        break;
                    case WindowButtons.Plugin:
                        image = ImageProcessHelper.GetImageByBase64AndReverse(AccessibleDefaultActionDescription, CanInverse);
                        break;
                }
                image = ImageProcessHelper.ScaleImage(image, CommonTheme.DpiScale);
                return image;
            }

            protected override void OnMouseEnter(EventArgs e)
            {
                _isHovered = true;
                Invalidate();
                base.OnMouseEnter(e);
            }

            protected override void OnMouseDown(MouseEventArgs e)
            {
                if (e.Button == MouseButtons.Left)
                {
                    _isPressed = true;
                    Invalidate();
                }
                base.OnMouseDown(e);
            }

            protected override void OnMouseUp(MouseEventArgs e)
            {
                _isPressed = false;
                Invalidate();
                base.OnMouseUp(e);
            }

            protected override void OnMouseLeave(EventArgs e)
            {
                _isHovered = false;
                Invalidate();
                base.OnMouseLeave(e);
            }
        }

        protected const int WS_EX_TRANSPARENT = 0x20;
        protected const int WS_EX_LAYERED = 0x80000;
        protected const int WS_EX_NOACTIVATE = 0x8000000;

        protected abstract class MetroShadowBase : Form
        {
            private readonly int _shadowSize;

            private readonly int _wsExStyle;

            //private bool _isBringingToFront;

            private long _lastResizedOn;

            protected MetroShadowBase(MetroForm targetForm, int shadowSize, int wsExStyle)
            {
                TargetForm = targetForm;
                _shadowSize = shadowSize;
                _wsExStyle = wsExStyle;
                TargetForm.Activated += OnTargetFormActivated;
                //TargetForm.Activated += TargetForm_LostFocus;
                //TargetForm.LostFocus += TargetForm_LostFocus;
                TargetForm.ResizeBegin += OnTargetFormResizeBegin;
                TargetForm.ResizeEnd += OnTargetFormResizeEnd;
                TargetForm.VisibleChanged += OnTargetFormVisibleChanged;
                TargetForm.SizeChanged += OnTargetFormSizeChanged;
                TargetForm.Move += OnTargetFormMove;
                TargetForm.Resize += OnTargetFormResize;
                TargetForm.Shown += TargetForm_Shown;

                if (TargetForm.Owner != null) Owner = TargetForm.Owner;
                TargetForm.Owner = this;
                MaximizeBox = false;
                MinimizeBox = false;
                ShowInTaskbar = false;
                ShowIcon = false;
                FormBorderStyle = FormBorderStyle.None;
                Bounds = GetShadowBounds();
            }

            protected MetroForm TargetForm { get; }

            protected override CreateParams CreateParams
            {
                get
                {
                    //const int WS_EX_NOACTIVATE = 0x08000000;
                    //const int WS_CHILD = 0x40000000;
                    //CreateParams cp = base.CreateParams;
                    //cp.Style |= WS_CHILD;
                    //cp.ExStyle |= WS_EX_NOACTIVATE;
                    //return cp;
                    var createParams = base.CreateParams;
                    //createParams.Style |= 0x40000000;
                    createParams.ExStyle |= _wsExStyle;
                    //createParams.ExStyle |= 0x08000000;
                    return createParams;
                }
            }

            private bool IsResizing => _lastResizedOn > 0;

            private Rectangle GetShadowBounds()
            {
                var bounds = TargetForm.Bounds;
                bounds.Inflate(_shadowSize, _shadowSize);
                return bounds;
            }

            protected abstract void PaintShadow();

            protected abstract void ClearShadow();

            private bool _isTargetFormShown;

            private void OnTargetFormTopMostChanged(object sender, EventArgs e)
            {
                TopMost = TargetForm.TopMost;
            }

            private void TargetForm_Shown(object sender, EventArgs e)
            {
                OnTargetFormTopMostChanged(sender, e);
                _isTargetFormShown = true;

                // 使用异步延迟方式绘制阴影，确保主窗体已完全绘制
                BeginInvoke(new Action(() =>
                {
                    PaintShadowIfVisible();
                    if (Visible)
                        TargetForm?.ForceActivate();
                }));
            }

            private void OnTargetFormActivated(object sender, EventArgs e)
            {
                if (Visible) Update();
            }

            private void OnTargetFormVisibleChanged(object sender, EventArgs e)
            {
                Visible = IsCanShow;
                Update();
            }

            private void OnTargetFormMove(object sender, EventArgs e)
            {
                Bounds = GetShadowBounds();
            }

            private void OnTargetFormResizeBegin(object sender, EventArgs e)
            {
                _lastResizedOn = ServerTime.DateTime.Ticks;
            }

            private void OnTargetFormResizeEnd(object sender, EventArgs e)
            {
                _lastResizedOn = 0L;
                PaintShadowIfVisible();
            }

            private void OnTargetFormResize(object sender, EventArgs e)
            {
                ClearShadow();
            }

            private void OnTargetFormSizeChanged(object sender, EventArgs e)
            {
                Bounds = GetShadowBounds();
                if (!IsResizing) PaintShadowIfVisible();
            }

            private void PaintShadowIfVisible()
            {
                Visible = IsCanShow;
                if (Visible) PaintShadow();
            }

            public bool IsCanShow => _isTargetFormShown && TargetForm != null && TargetForm.Visible && TargetForm.WindowState == FormWindowState.Normal && TargetForm.Opacity > 0;
        }

        protected class MetroFlatDropShadow : MetroShadowBase
        {
            private Point _offset = new Point(-6, -6);

            public MetroFlatDropShadow(MetroForm targetForm)
                : base(targetForm, 6, WS_EX_LAYERED | WS_EX_TRANSPARENT | WS_EX_NOACTIVATE)
            {
            }

            protected override void OnLoad(EventArgs e)
            {
                base.OnLoad(e);
                OnPaint(null);
            }

            protected override void OnPaint(PaintEventArgs e)
            {
                Visible = IsCanShow;
                if (Visible)
                    PaintShadow();
            }

            protected override void PaintShadow()
            {
                using (var bitmap = DrawBlurBorder())
                {
                    SetBitmap(bitmap, byte.MaxValue);
                }
            }

            protected override void ClearShadow()
            {
                var bitmap = new Bitmap(Width, Height, PixelFormat.Format32bppArgb);
                var graphics = Graphics.FromImage(bitmap);
                graphics.Clear(Color.Transparent);
                graphics.Flush();
                graphics.Dispose();
                SetBitmap(bitmap, byte.MaxValue);
                bitmap.Dispose();
            }

            [SecuritySafeCritical]
            private void SetBitmap(Bitmap bitmap, byte opacity)
            {
                if (bitmap.PixelFormat != PixelFormat.Format32bppArgb)
                    throw new ApplicationException("The bitmap must be 32ppp with alpha-channel.");
                var dC = WinApi.GetDC(IntPtr.Zero);
                var intPtr = WinApi.CreateCompatibleDC(dC);
                var intPtr2 = IntPtr.Zero;
                var hObject = IntPtr.Zero;
                try
                {
                    intPtr2 = bitmap.GetHbitmap(Color.FromArgb(0));
                    hObject = WinApi.SelectObject(intPtr, intPtr2);
                    var psize = new WinApi.SIZE(bitmap.Width, bitmap.Height);
                    var pprSrc = new WinApi.POINT(0, 0);
                    var pptDst = new WinApi.POINT(Left, Top);
                    var pblend = default(WinApi.BLENDFUNCTION);
                    pblend.BlendOp = 0;
                    pblend.BlendFlags = 0;
                    pblend.SourceConstantAlpha = opacity;
                    pblend.AlphaFormat = 1;
                    WinApi.UpdateLayeredWindow(Handle, dC, ref pptDst, ref psize, intPtr, ref pprSrc, 0, ref pblend, 2);
                }
                finally
                {
                    WinApi.ReleaseDC(IntPtr.Zero, dC);
                    if (intPtr2 != IntPtr.Zero)
                    {
                        WinApi.SelectObject(intPtr, hObject);
                        WinApi.DeleteObject(intPtr2);
                    }

                    WinApi.DeleteDC(intPtr);
                }
            }

            private Bitmap DrawBlurBorder()
            {
                return (Bitmap)DrawOutsetShadow(Color.Black,
                    new Rectangle(0, 0, ClientRectangle.Width, ClientRectangle.Height));
            }

            private Image DrawOutsetShadow(Color color, Rectangle shadowCanvasArea)
            {
                var rect = shadowCanvasArea;
                var rect2 = new Rectangle(shadowCanvasArea.X + (-_offset.X - 1), shadowCanvasArea.Y + (-_offset.Y - 1),
                    shadowCanvasArea.Width - (-_offset.X * 2 - 1), shadowCanvasArea.Height - (-_offset.Y * 2 - 1));
                var bitmap = new Bitmap(rect.Width, rect.Height, PixelFormat.Format32bppArgb);
                var graphics = Graphics.FromImage(bitmap);
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                using (Brush brush = new SolidBrush(Color.FromArgb(30, Color.Black)))
                {
                    graphics.FillRectangle(brush, rect);
                }

                using (Brush brush2 = new SolidBrush(Color.FromArgb(60, Color.Black)))
                {
                    graphics.FillRectangle(brush2, rect2);
                }

                graphics.Flush();
                graphics.Dispose();
                return bitmap;
            }
        }

        protected class MetroRealisticDropShadow : MetroShadowBase
        {
            public MetroRealisticDropShadow(MetroForm targetForm)
                : base(targetForm, 15, WS_EX_LAYERED | WS_EX_TRANSPARENT | WS_EX_NOACTIVATE)
            {
            }

            protected override void OnLoad(EventArgs e)
            {
                base.OnLoad(e);
                OnPaint(null);
            }

            protected override void OnPaint(PaintEventArgs e)
            {
                Visible = IsCanShow;
                if (Visible)
                    PaintShadow();
            }

            protected override void PaintShadow()
            {
                using (var bitmap = DrawBlurBorder())
                {
                    SetBitmap(bitmap, byte.MaxValue);
                }
            }

            protected override void ClearShadow()
            {
                var bitmap = new Bitmap(Width, Height, PixelFormat.Format32bppArgb);
                var graphics = Graphics.FromImage(bitmap);
                graphics.Clear(Color.Transparent);
                graphics.Flush();
                graphics.Dispose();
                SetBitmap(bitmap, byte.MaxValue);
                bitmap.Dispose();
            }

            [SecuritySafeCritical]
            private void SetBitmap(Bitmap bitmap, byte opacity)
            {
                if (bitmap.PixelFormat != PixelFormat.Format32bppArgb)
                    throw new ApplicationException("The bitmap must be 32ppp with alpha-channel.");

                if (TargetForm == null || TargetForm.IsDisposed)
                {
                    return;
                }
                var dC = WinApi.GetDC(IntPtr.Zero);
                var intPtr = WinApi.CreateCompatibleDC(dC);
                var intPtr2 = IntPtr.Zero;
                var hObject = IntPtr.Zero;
                try
                {
                    intPtr2 = bitmap.GetHbitmap(Color.FromArgb(0));
                    hObject = WinApi.SelectObject(intPtr, intPtr2);
                    var psize = new WinApi.SIZE(bitmap.Width, bitmap.Height);
                    var pprSrc = new WinApi.POINT(0, 0);
                    var pptDst = new WinApi.POINT(Left, Top);
                    var bLendfunction = default(WinApi.BLENDFUNCTION);
                    bLendfunction.BlendOp = 0;
                    bLendfunction.BlendFlags = 0;
                    bLendfunction.SourceConstantAlpha = opacity;
                    bLendfunction.AlphaFormat = 1;
                    var pblend = bLendfunction;
                    WinApi.UpdateLayeredWindow(Handle, dC, ref pptDst, ref psize, intPtr, ref pprSrc, 0, ref pblend, 2);
                }
                finally
                {
                    WinApi.ReleaseDC(IntPtr.Zero, dC);
                    if (intPtr2 != IntPtr.Zero)
                    {
                        WinApi.SelectObject(intPtr, hObject);
                        WinApi.DeleteObject(intPtr2);
                    }

                    WinApi.DeleteDC(intPtr);
                }
            }

            private Bitmap DrawBlurBorder()
            {
                return (Bitmap)DrawOutsetShadow(0, 0, 40, 1, Color.Black,
                    new Rectangle(1, 1, ClientRectangle.Width, ClientRectangle.Height));
            }

            private Image DrawOutsetShadow(int hShadow, int vShadow, int blur, int spread, Color color,
                Rectangle shadowCanvasArea)
            {
                var rectangle = shadowCanvasArea;
                var rectangle2 = shadowCanvasArea;
                rectangle2.Offset(hShadow, vShadow);
                rectangle2.Inflate(-blur, -blur);
                rectangle.Inflate(spread, spread);
                rectangle.Offset(hShadow, vShadow);
                var rectangle3 = rectangle;
                var bitmap = new Bitmap(rectangle3.Width, rectangle3.Height, PixelFormat.Format32bppArgb);
                var graphics = Graphics.FromImage(bitmap);
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                var cornerRadius = 0;
                do
                {
                    var num = (rectangle.Height - rectangle2.Height) / (double)(blur * 2 + spread * 2);
                    var fillColor = Color.FromArgb((int)(200.0 * (num * num)), color);
                    var bounds = rectangle2;
                    bounds.Offset(-rectangle3.Left, -rectangle3.Top);
                    DrawRoundedRectangle(graphics, bounds, cornerRadius, Pens.Transparent, fillColor);
                    rectangle2.Inflate(1, 1);
                    cornerRadius = (int)(blur * (1.0 - num * num));
                } while (rectangle.Contains(rectangle2));

                graphics.Flush();
                graphics.Dispose();
                return bitmap;
            }

            private void DrawRoundedRectangle(Graphics g, Rectangle bounds, int cornerRadius, Pen drawPen,
                Color fillColor)
            {
                var num = Convert.ToInt32(Math.Ceiling(drawPen.Width));
                bounds = Rectangle.Inflate(bounds, -num, -num);
                var graphicsPath = new GraphicsPath();
                if (cornerRadius > 0)
                {
                    graphicsPath.AddArc(bounds.X, bounds.Y, cornerRadius, cornerRadius, 180f, 90f);
                    graphicsPath.AddArc(bounds.X + bounds.Width - cornerRadius, bounds.Y, cornerRadius, cornerRadius,
                        270f, 90f);
                    graphicsPath.AddArc(bounds.X + bounds.Width - cornerRadius, bounds.Y + bounds.Height - cornerRadius,
                        cornerRadius, cornerRadius, 0f, 90f);
                    graphicsPath.AddArc(bounds.X, bounds.Y + bounds.Height - cornerRadius, cornerRadius, cornerRadius,
                        90f, 90f);
                }
                else
                {
                    graphicsPath.AddRectangle(bounds);
                }

                graphicsPath.CloseAllFigures();
                if (cornerRadius > 5)
                    using (var brush = new SolidBrush(fillColor))
                    {
                        g.FillPath(brush, graphicsPath);
                    }

                if (!Equals(drawPen, Pens.Transparent))
                    using (var pen = new Pen(drawPen.Color))
                    {
                        pen.EndCap = pen.StartCap = LineCap.Round;
                        g.DrawPath(pen, graphicsPath);
                    }
            }
        }
    }

    public enum MetroFormTextAlign
    {
        Left,
        Center,
        Right
    }
    public enum Messages : uint
    {
        WM_SIZE = 0x5,
        WM_GETMINMAXINFO = 0x24,
        WM_NCHITTEST = 0x84,
        WM_NCLBUTTONDOWN = 0xa1,
        WM_NCLBUTTONDBLCLK = 0xa3,
        WM_SYSCOMMAND = 0x112,
        WM_LBUTTONDBLCLK = 0x203,
        WM_DWMCOMPOSITIONCHANGED = 0x031E,

        SC_MOVE = 0xF010,
        SC_MAXIMIZE = 0xF030,
        SC_RESTORE = 0xF120,
        WM_DISPLAYCHANGE = 0x007e //分辨率调整事件
    }

    public class AutoSizeFormClass
    {
        //(1).声明结构,只记录窗体和其控件的初始位置和大小。
        public struct controlRect
        {
            public int Left;
            public int Top;
            public int Width;
            public int Height;
        }

        public controlRect InitRect { get; set; }

        //(2).声明 1个对象
        //注意这里不能使用控件列表记录 List nCtrl;，因为控件的关联性，记录的始终是当前的大小。
        //      public List oldCtrl= new List();//这里将西文的大于小于号都过滤掉了，只能改为中文的，使用中要改回西文
        public Dictionary<Control, controlRect> oldCtrl = new Dictionary<Control, controlRect>();

        //(3). 创建两个函数
        //(3.1)记录窗体和其控件的初始位置和大小,
        public void controllInitializeSize(MetroForm mForm)
        {
            controlRect cR;
            cR.Left = mForm.Left; cR.Top = mForm.Top; cR.Width = mForm.Width; cR.Height = mForm.Height;
            InitRect = cR;
            AddControl(mForm);
        }

        private void Clear()
        {
            try
            {
                var lstKeys = oldCtrl.Keys.ToList().Where(p => p == null || p.Disposing || p.IsDisposed);
                if (lstKeys.Count() > 0)
                {
                    foreach (var item in lstKeys)
                    {
                        oldCtrl.Remove(item);
                    }
                    MemoryManager.ClearMemory();
                }
            }
            catch { }
        }

        public void AddNewControl(Control ctrl)
        {
            Clear();
            if (oldCtrl.ContainsKey(ctrl))
            {
                return;
            }
            var objCtrl = new controlRect
            {
                Left = ctrl.Left,
                Top = ctrl.Top,
                Width = ctrl.Width,
                Height = ctrl.Height
            };
            oldCtrl.Add(ctrl, objCtrl);
            AddControl(ctrl);
            controlAutoSize(ctrl.Parent);
            HighDpiHelper.AttachMouseMoveEvent(ctrl);
        }

        private void AddControl(Control ctl)
        {
            foreach (Control c in ctl.Controls)
            {
                //if (c is PanelPictureView)
                //{
                //    continue;
                //}
                if (oldCtrl.ContainsKey(c))
                    continue;
                controlRect objCtrl;
                objCtrl.Left = c.Left; objCtrl.Top = c.Top; objCtrl.Width = c.Width; objCtrl.Height = c.Height;
                oldCtrl.Add(c, objCtrl);
                //**放在这里，是先记录控件本身，后记录控件的子控件
                if (c.Controls.Count > 0)
                    AddControl(c);//窗体内其余控件还可能嵌套控件(比如panel),要单独抽出,因为要递归调用
            }
        }

        //控件自适应大小
        public void controlAutoSize(Control mForm)
        {
            Form form = mForm.FindForm();
            if (form != null)
            {
                float wScale = (float)form.Width / InitRect.Width;//新旧窗体之间的比例，与最早的旧窗体
                float hScale = (float)form.Height / InitRect.Height;//.Height;
                AutoScaleControl(mForm, wScale, hScale);//窗体内其余控件还可能嵌套控件(比如panel),要单独抽出,因为要递归调用
            }
        }

        private void AutoScaleControl(Control ctl, float wScale, float hScale)
        {
            foreach (Control c in ctl.Controls)
            {
                if (!oldCtrl.ContainsKey(c))
                {
                    continue;
                }
                var oldRect = oldCtrl[c];
                if (string.IsNullOrEmpty(c.Text)
                    && Equals(c.GetType().Name, "MetroFormButton"))
                {
                }
                else
                {
                    c.Left = (int)(oldRect.Left * wScale);
                    c.Top = (int)(oldRect.Top * hScale);
                }
                c.Width = (int)(oldRect.Width * wScale);
                c.Height = (int)(oldRect.Height * hScale);
                if (c.Controls.Count > 0)
                    AutoScaleControl(c, wScale, hScale);

                if (ctl is DataGridView gridView)
                {
                    int widths = 0;
                    for (int i = 0; i < gridView.Columns.Count; i++)
                    {
                        gridView.AutoResizeColumn(i, DataGridViewAutoSizeColumnMode.AllCells);  // 自动调整列宽  
                        widths += gridView.Columns[i].Width;   // 计算调整列后单元列的宽度和                       
                    }
                    if (widths >= gridView.Size.Width)  // 如果调整列的宽度大于设定列宽  
                        gridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.DisplayedCells;  // 调整列的模式 自动  
                    else
                        gridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;  // 如果小于 则填充  
                }
            }
        }
    }

    public static class HighDpiHelper
    {
        private static Dictionary<Form, float> dicOldDpi = new Dictionary<Form, float>();

        public static void ConvertFontByUnit(Control ctrl, GraphicsUnit unit, float scale)
        {
            if (ctrl.Font.Unit != unit)
            {
                var size = unit == GraphicsUnit.Pixel ? ctrl.Font.Size * 1.33F : ctrl.Font.Size / 1.33F;
                ctrl.Font = new Font(ctrl.Font.FontFamily.Name, size, ctrl.Font.Style, unit);
                if (ctrl is Button btn)
                {
                    if (btn.AutoSize)
                        ctrl.Size = new Size(1, 1);
                }
            }
            else
            {
                var size = ctrl.Font.Size * scale;
                ctrl.Font = new Font(ctrl.Font.FontFamily.Name, size, ctrl.Font.Style, unit);
                if (ctrl is Button btn)
                {
                    if (btn.AutoSize)
                        ctrl.Size = new Size(1, 1);
                }
            }
            foreach (Control item in ctrl.Controls)
            {
                ConvertFontByUnit(item, unit, scale);
            }
        }

        public static float GetFormDpi(Form container)
        {
            return container == null ? 1 : dicOldDpi.TryGetValue(container, out var value) ? value : container.GetDpiScale();
        }

        public static void AdjustControlImagesDpiScale(MetroForm container, bool isReset = false)
        {
            if ((!isReset && CommonString.CommonGraphicsUnit() == GraphicsUnit.Pixel) || !container.Visible)
            {
                return;
            }

            if (container.WindowState == FormWindowState.Minimized)
            {
                container.WindowState = FormWindowState.Normal;
            }

            try
            {
                // 暂停布局和绘制
                container.SuspendLayout();

                // 记录原始位置和大小
                var originalBounds = container.Bounds;

                // 计算DPI缩放
                var dpiScale = isReset ? 1F : container.GetDpiScale();
                var isOldForm = dicOldDpi.ContainsKey(container);
                var oldDpi = dicOldDpi.TryGetValue(container, out var value) ? value : 1f;
                dicOldDpi[container] = dpiScale;
                if (Equals(dpiScale, oldDpi))
                {
                    return;
                }

                var scale = dpiScale / oldDpi;

                // 转换字体
                ConvertFontByUnit(container, CommonString.CommonGraphicsUnit(), isOldForm ? scale : 1);

                // 使用SetBounds而不是直接修改Size
                int newWidth = (int)(container.InitForm.InitRect.Width * dpiScale);
                int newHeight = (int)(container.InitForm.InitRect.Height * dpiScale);

                // 计算并设置新尺寸（保持位置不变）
                // GetStartPosition确保窗体在屏幕中央
                Point centerPosition = new Point(
                    (Screen.FromControl(container).WorkingArea.Width - newWidth) / 2 + Screen.FromControl(container).WorkingArea.X,
                    (Screen.FromControl(container).WorkingArea.Height - newHeight) / 2 + Screen.FromControl(container).WorkingArea.Y
                );

                // 一次性设置新的位置和尺寸
                container.SetBounds(centerPosition.X, centerPosition.Y, newWidth, newHeight);

                // 内部控件调整
                container.InitForm.controlAutoSize(container);
                container.OnThemeChange();

                // 调整其他控件
                AdjustControlImagesDpiScale(new List<Control> { container }, scale, dpiScale);

                // 更新窗口按钮
                container.UpdateWindowButtonImage();
                container.UpdateWindowButtonPosition();
            }
            finally
            {
                // 恢复布局
                container.ResumeLayout(true);

                // 强制完整重绘
                container.Invalidate(true);
                container.Update();
            }
        }

        private static readonly List<string> lstExpMouseMoveType = new List<string> { "ImageBox", "PanelPictureView" };
        public static void AttachMouseMoveEvent(Control control)
        {
            // 判断控件是否有 Image 属性
            if (control.GetType().GetProperty("Image") != null && !lstExpMouseMoveType.Any(p => control.GetType().ToString().Contains(p)))
            {
                if (!Equals(control.AccessibleDefaultActionDescription, "webloading") && !Equals(control.Name, "pbThumbnail"))
                {
                    control.MouseHover -= AutoScaleWhenMove;
                    control.MouseHover += AutoScaleWhenMove;
                }
            }
            //if (control.GetType().GetEvent("Click") != null)
            //{
            //    control.MouseDown -= AnimationManager.DoAnimation;
            //    control.MouseDown += AnimationManager.DoAnimation;
            //}

            // 遍历控件的子控件并递归调用 AttachMouseMoveEvent 方法
            foreach (Control childControl in control.Controls)
            {
                AttachMouseMoveEvent(childControl);
            }

            if (control is ToolStrip toolStrip)
            {
                AttachMouseMoveEvent(toolStrip.Items);
            }
            if (control.ContextMenuStrip != null)
            {
                AttachMouseMoveEvent(control.ContextMenuStrip.Items);
            }
        }

        private static void AttachMouseMoveEvent(ToolStripItemCollection items)
        {
            foreach (ToolStripItem item in items)
            {
                if (item.GetType().GetProperty("Image") != null
                    && (string.IsNullOrEmpty(item.Name) || !item.Name.StartsWith("cmsTrans")))
                {
                    // 1. 先移除事件避免重复添加
                    item.MouseHover -= AutoScaleWhenMove;

                    // 2. 安全处理可能有问题的图标
                    try
                    {
                        // 确保图标不是损坏的或格式不兼容的
                        if (item.Image != null)
                        {
                            // 尝试访问可能导致问题的属性，提前捕获异常
                            try
                            {
                                var dimensions = item.Image.FrameDimensionsList;
                                // 如果没有抛出异常，图像是安全的，可以添加事件
                                item.MouseHover += AutoScaleWhenMove;
                            }
                            catch
                            {
                                // 图像有问题，不添加动画事件
                                // 可选：替换为安全图像
                                // item.Image = CreateSafeImage(img);
                            }
                        }
                        else
                        {
                            // 没有图像的项目是安全的
                            item.MouseHover += AutoScaleWhenMove;
                        }
                    }
                    catch
                    {
                        // 如果获取Image属性也失败，不添加事件
                    }
                }

                // 如果该项是一个下拉菜单，则递归调用 AttachMouseMoveEvent 方法为其子项添加事件
                if (item is ToolStripMenuItem menuItem && menuItem.DropDownItems.Count > 0)
                {
                    AttachMouseMoveEvent(menuItem.DropDownItems);
                }
                if (item is ToolStripDropDownButton dropDownButton && dropDownButton.DropDownItems.Count > 0)
                {
                    AttachMouseMoveEvent(dropDownButton.DropDownItems);
                }
            }
        }

        private static void AutoScaleWhenMove(object sender, EventArgs e)
        {
            // 对其他控件使用新的动画逻辑
            ImageAnimatorHelper.HandleMouseMoveAnimation(sender, e);
        }

        public static void AdjustControlImagesDpiScale(IEnumerable controls, float scale, float dpiScale)
        {
            foreach (Control control in controls)
            {
                //if (control is UcContent)
                //{
                //    continue;
                //}
                switch (control)
                {
                    case Button button:
                        if (button.Image != null)
                        {
                            button.Image = GetControlImage(button.AccessibleDefaultActionDescription, button.Image, scale, dpiScale);
                        }
                        //if (button.BackgroundImage != null)
                        //{
                        //    button.BackgroundImage = ImageProcessHelper.ScaleImage(button.BackgroundImage, scale);
                        //}
                        break;
                    case PictureBox pic:
                        if (pic.Image != null && pic.SizeMode != PictureBoxSizeMode.StretchImage
                            && !Equals(pic.AccessibleDefaultActionDescription, "webloading"))
                            pic.Image = GetControlImage(pic.AccessibleDefaultActionDescription, pic.Image, scale, dpiScale);
                        else if (pic.BackgroundImage != null)
                            pic.BackgroundImage = GetControlImage(pic.AccessibleDefaultActionDescription, pic.BackgroundImage, scale, dpiScale);
                        break;
                    case ToolStrip toolStrip:
                        ScaleToolStrip(scale, dpiScale, toolStrip);
                        break;
                }

                if (control.ContextMenuStrip != null)
                {
                    ScaleToolStrip(scale, dpiScale, control.ContextMenuStrip);
                }

                // Then recurse
                AdjustControlImagesDpiScale(control.Controls, scale, dpiScale);
            }
        }

        private static Image GetControlImage(string resource, Image image, float scale, float dpiScale)
        {
            Bitmap bitmap = null;
            if (!string.IsNullOrEmpty(resource))
            {
                try
                {
                    bitmap = ImageProcessHelper.ScaleImage(ImageProcessHelper.GetResourceImage(resource), dpiScale);
                }
                catch { }
            }
            try
            {
                if (bitmap == null)
                    bitmap = ImageProcessHelper.ScaleImage(image, scale);
            }
            catch { }
            return bitmap;
        }

        public static void ScaleToolStrip(float scale, float dpiScale, ToolStrip toolStrip)
        {
            // 基准图标大小，默认为 16x16 像素
            int targetSize = (int)(16 * dpiScale);
            toolStrip.ImageScalingSize = new Size(targetSize, targetSize);

            ScaleToolStripMenuItem(scale, dpiScale, toolStrip.Items);
            toolStrip.AutoSize = !toolStrip.AutoSize;
            toolStrip.AutoSize = !toolStrip.AutoSize;

            //toolStrip.Size = new Size(toolStrip.Width + 5, toolStrip.Height + 5);
        }

        private static void ScaleToolStripMenuItem(float scale, float dpiScale, ToolStripItemCollection items)
        {
            foreach (ToolStripItem item in items)
            {
                if (item.Image != null && item.BackgroundImage == null)
                    try
                    {
                        //item.AutoSize = false;
                        //item.ImageScaling = ToolStripItemImageScaling.SizeToFit;
                        item.Image = GetControlImage(item.AccessibleDefaultActionDescription, item.Image, scale, dpiScale);
                        //item.Size = new Size(item.Width + 5, item.Height);
                        //if (item.Image != null)
                        //{
                        //    item.ImageScaling = ToolStripItemImageScaling.None;
                        //}
                    }
                    catch { }
                if (item is ToolStripMenuItem menuItem && menuItem.DropDownItems.Count > 0)
                {
                    ScaleToolStripMenuItem(scale, dpiScale, menuItem.DropDownItems);
                }
                if (item is ToolStripDropDownButton dropDownButton && dropDownButton.DropDownItems.Count > 0)
                {
                    ScaleToolStripMenuItem(scale, dpiScale, dropDownButton.DropDownItems);
                }
            }
        }
    }
}