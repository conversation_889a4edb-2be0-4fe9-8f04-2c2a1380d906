﻿using MetroFramework.Components;
using MetroFramework.Design;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using MetroFramework.Native;
using OCRTools;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Security;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace MetroFramework.Controls
{
    [ToolboxItem(false)]
    [Designer(typeof(MetroTabPageDesigner), typeof(ParentControlDesigner))]
    public class MetroTabPage : TabPage, IMetroControl
    {
        private readonly MetroScrollBar _horizontalScrollbar = new MetroScrollBar(MetroScrollOrientation.Horizontal);

        private readonly MetroScrollBar _verticalScrollbar = new MetroScrollBar(MetroScrollOrientation.Vertical);
        private MetroColorStyle _metroStyle;

        private MetroThemeStyle _metroTheme;

        public MetroTabPage()
        {
            SetStyle(ControlStyles.OptimizedDoubleBuffer |
                ControlStyles.ResizeRedraw |
                     ControlStyles.DoubleBuffer |
                     ControlStyles.SupportsTransparentBackColor |
                     ControlStyles.UserPaint, true);
            Controls.Add(_verticalScrollbar);
            Controls.Add(_horizontalScrollbar);
            _verticalScrollbar.UseBarColor = true;
            _horizontalScrollbar.UseBarColor = true;
            _verticalScrollbar.Scroll += VerticalScrollbarScroll;
            _horizontalScrollbar.Scroll += HorizontalScrollbarScroll;
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomBackColor { get; set; }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool HorizontalScrollbar { get; set; }

        [Category("Metro Appearance")]
        public int HorizontalScrollbarSize
        {
            get => _horizontalScrollbar.ScrollbarSize;
            set => _horizontalScrollbar.ScrollbarSize = value;
        }

        [Category("Metro Appearance")]
        public bool HorizontalScrollbarBarColor
        {
            get => _horizontalScrollbar.UseBarColor;
            set => _horizontalScrollbar.UseBarColor = value;
        }

        [Category("Metro Appearance")]
        public bool HorizontalScrollbarHighlightOnWheel
        {
            get => _horizontalScrollbar.HighlightOnWheel;
            set => _horizontalScrollbar.HighlightOnWheel = value;
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool VerticalScrollbar { get; set; }

        [Category("Metro Appearance")]
        public int VerticalScrollbarSize
        {
            get => _verticalScrollbar.ScrollbarSize;
            set => _verticalScrollbar.ScrollbarSize = value;
        }

        [Category("Metro Appearance")]
        public bool VerticalScrollbarBarColor
        {
            get => _verticalScrollbar.UseBarColor;
            set => _verticalScrollbar.UseBarColor = value;
        }

        [Category("Metro Appearance")]
        public bool VerticalScrollbarHighlightOnWheel
        {
            get => _verticalScrollbar.HighlightOnWheel;
            set => _verticalScrollbar.HighlightOnWheel = value;
        }

        [Category("Metro Appearance")]
        public new bool AutoScroll
        {
            get => base.AutoScroll;
            set
            {
                if (value)
                {
                    HorizontalScrollbar = true;
                    VerticalScrollbar = true;
                }

                base.AutoScroll = value;
            }
        }

        [DefaultValue(MetroCommonStyle.DefaultStyle)]
        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                if (DesignMode || _metroStyle != 0) return _metroStyle;
                if (StyleManager != null) return StyleManager.Style;
                if (StyleManager == null) return MetroCommonStyle.DefaultStyle;
                return _metroStyle;
            }
            set => _metroStyle = value;
        }

        [DefaultValue(MetroThemeStyle.Light)]
        [Category("Metro Appearance")]
        public MetroThemeStyle Theme
        {
            get
            {
                if (DesignMode || _metroTheme != 0) return _metroTheme;
                if (StyleManager != null) return StyleManager.Theme;
                if (StyleManager == null) return MetroThemeStyle.Light;
                return _metroTheme;
            }
            set => _metroTheme = value;
        }

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public MetroStyleManager StyleManager { get; set; }

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintBackground != null) CustomPaintBackground(this, e);
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaint != null) CustomPaint(this, e);
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintForeground != null) CustomPaintForeground(this, e);
        }

        private void HorizontalScrollbarScroll(object sender, ScrollEventArgs e)
        {
            AutoScrollPosition = new Point(e.NewValue, _verticalScrollbar.Value);
            UpdateScrollBarPositions();
        }

        private void VerticalScrollbarScroll(object sender, ScrollEventArgs e)
        {
            AutoScrollPosition = new Point(_horizontalScrollbar.Value, e.NewValue);
            UpdateScrollBarPositions();
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            try
            {
                var color = BackColor;
                if (!UseCustomBackColor) color = MetroPaint.BackColor.Form(Theme);
                if (color.A == byte.MaxValue && BackgroundImage == null)
                {
                    e.Graphics.Clear(color);
                }
                else
                {
                    base.OnPaintBackground(e);
                    OnCustomPaintBackground(new MetroPaintEventArgs(color, Color.Empty, e.Graphics));
                }
            }
            catch
            {
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            if (!Size.IsValidate())
                return;
            base.OnPaint(e);
            try
            {
                if (GetStyle(ControlStyles.AllPaintingInWmPaint)) OnPaintBackground(e);
                OnCustomPaint(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
                OnPaintForeground(e);
            }
            catch
            {
                Invalidate();
            }
        }

        protected virtual void OnPaintForeground(PaintEventArgs e)
        {
            if (DesignMode)
            {
                _horizontalScrollbar.Visible = false;
                _verticalScrollbar.Visible = false;
                return;
            }

            UpdateScrollBarPositions();
            if (HorizontalScrollbar) _horizontalScrollbar.Visible = HorizontalScroll.Visible;
            if (HorizontalScroll.Visible)
            {
                _horizontalScrollbar.Minimum = HorizontalScroll.Minimum;
                _horizontalScrollbar.Maximum = HorizontalScroll.Maximum;
                _horizontalScrollbar.SmallChange = HorizontalScroll.SmallChange;
                _horizontalScrollbar.LargeChange = HorizontalScroll.LargeChange;
            }

            if (VerticalScrollbar) _verticalScrollbar.Visible = VerticalScroll.Visible;
            if (VerticalScroll.Visible)
            {
                _verticalScrollbar.Minimum = VerticalScroll.Minimum;
                _verticalScrollbar.Maximum = VerticalScroll.Maximum;
                _verticalScrollbar.SmallChange = VerticalScroll.SmallChange;
                _verticalScrollbar.LargeChange = VerticalScroll.LargeChange;
            }

            OnCustomPaintForeground(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
        }

        protected override void OnMouseWheel(MouseEventArgs e)
        {
            base.OnMouseWheel(e);
            _verticalScrollbar.Value = VerticalScroll.Value;
            _horizontalScrollbar.Value = HorizontalScroll.Value;
        }

        [SecuritySafeCritical]
        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m);
            if (!DesignMode) WinApi.ShowScrollBar(Handle, 3, 0);
        }

        private void UpdateScrollBarPositions()
        {
            if (DesignMode) return;
            if (!AutoScroll)
            {
                _verticalScrollbar.Visible = false;
                _horizontalScrollbar.Visible = false;
                return;
            }

            _verticalScrollbar.Location =
                new Point(ClientRectangle.Width - _verticalScrollbar.Width, ClientRectangle.Y);
            _verticalScrollbar.Height = ClientRectangle.Height;
            if (!VerticalScrollbar) _verticalScrollbar.Visible = false;
            _horizontalScrollbar.Location =
                new Point(ClientRectangle.X, ClientRectangle.Height - _horizontalScrollbar.Height);
            _horizontalScrollbar.Width = ClientRectangle.Width;
            if (!HorizontalScrollbar) _horizontalScrollbar.Visible = false;
        }
    }
}