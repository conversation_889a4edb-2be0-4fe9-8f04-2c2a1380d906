﻿using System;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using MetroFramework.Forms;

namespace OCRTools
{
    public partial class FrmLogin : MetroForm
    {
        public FrmLogin()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            lnkPwd.TabStop = false;
            lnkReg.TabStop = false;
            txtPwd.KeyDown += TxtPwd_KeyDown;
            txtAccount.Text = IniHelper.GetValue("配置", "Account");
            txtPwd.Text = IniHelper.GetValue("配置", "Password");
        }

        private void TxtPwd_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
                if (!string.IsNullOrEmpty(txtAccount.Text.Trim()) && !string.IsNullOrEmpty(txtPwd.Text))
                    btnLogin_Click(sender, null);
        }

        private void lnkReg_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            var reg = new FrmReg {Icon = Icon, Theme = Theme, Style = Style};
            var result = reg.ShowDialog(this);
            if (result == DialogResult.OK)
            {
                CommonMethod.ShowHelpMsg("注册成功！使用过程中，如有任何问题，请联系客服！祝您使用愉快！", 5000);
                txtAccount.Text = reg.Account;
                txtPwd.Text = reg.Pwd;
                btnLogin_Click(sender, null);
            }
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            var account = txtAccount.Text.Trim();
            var isEmail = Regex.IsMatch(account, "^\\s*([A-Za-z0-9_-]+(\\.\\w+)*@(\\w+\\.)+\\w{2,5})\\s*$");
            var isMobile = Regex.IsMatch(account, @"^[1]\d{10}");
            if (!isEmail && !isMobile)
            {
                MessageBox.Show(this, "账号格式不正确，必须为手机号或者邮箱！", "温馨提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtAccount.SelectAll();
                txtAccount.Focus();
                return;
            }

            var pwd = txtPwd.Text.Trim();
            if (string.IsNullOrEmpty(pwd))
            {
                MessageBox.Show(this, "密码不能为空，必须为6-15位的数字或大小写字母！", "温馨提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtPwd.Focus();
                return;
            }

            //使用regex进行格式设置 至少有数字、大小写字母，最少8个字符、最长30个字符
            if (!new Regex(@"[0-9A-Za-z].{5,15}").IsMatch(pwd)) //判断密码格式是否符合要求
            {
                MessageBox.Show(this, "密码必须为6-15位的数字或大小写字母！", "温馨提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtPwd.Focus();
                return;
            }

            var strMsg = "";
            var result = OcrHelper.DoLogin(account, pwd, ref strMsg);
            if (result)
            {
                IniHelper.SetValue("配置", "Account", account);
                IniHelper.SetValue("配置", "Password", pwd);
                DialogResult = DialogResult.OK;
                Close();
            }
            else
            {
                if (string.IsNullOrEmpty(strMsg)) strMsg = "登录失败，请稍后重试！";
                MessageBox.Show(this, strMsg, "温馨提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void lnkPwd_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            var reg = new FrmForgetPwd {Icon = Icon, Theme = Theme, Style = Style, StyleManager = StyleManager};
            if (!string.IsNullOrEmpty(txtAccount.Text.Trim())) reg.Account = txtAccount.Text.Trim();
            var result = reg.ShowDialog(this);
            if (result == DialogResult.OK)
            {
                txtAccount.Text = reg.Account;
                txtPwd.Text = reg.Pwd;
                btnLogin_Click(sender, null);
            }
        }
    }
}