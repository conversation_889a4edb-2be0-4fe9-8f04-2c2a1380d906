namespace UtfUnknown.Core
{
	internal static class CodepageName
	{
		internal const string ASCII = "ascii";

		internal const string UTF7 = "utf-7";

		internal const string UTF8 = "utf-8";

		internal const string UTF16_LE = "utf-16le";

		internal const string UTF16_BE = "utf-16be";

		internal const string UTF32_LE = "utf-32le";

		internal const string UTF32_BE = "utf-32be";

		internal const string EUC_JP = "euc-jp";

		internal const string EUC_KR = "euc-kr";

		internal const string EUC_TW = "euc-tw";

		internal const string ISO_2022_CN = "iso-2022-cn";

		internal const string ISO_2022_KR = "iso-2022-kr";

		internal const string ISO_2022_JP = "iso-2022-jp";

		internal const string X_CP50227 = "x-cp50227";

		internal const string BIG5 = "big5";

		internal const string GB18030 = "gb18030";

		internal const string HZ_GB_2312 = "hz-gb-2312";

		internal const string SHIFT_JIS = "shift-jis";

		internal const string KS_C_5601_1987 = "ks_c_5601-1987";

		internal const string CP949 = "cp949";

		internal const string IBM852 = "ibm852";

		internal const string IBM855 = "ibm855";

		internal const string IBM866 = "ibm866";

		internal const string ISO_8859_1 = "iso-8859-1";

		internal const string ISO_8859_2 = "iso-8859-2";

		internal const string ISO_8859_3 = "iso-8859-3";

		internal const string ISO_8859_4 = "iso-8859-4";

		internal const string ISO_8859_5 = "iso-8859-5";

		internal const string ISO_8859_6 = "iso-8859-6";

		internal const string ISO_8859_7 = "iso-8859-7";

		internal const string ISO_8859_8 = "iso-8859-8";

		internal const string ISO_8859_9 = "iso-8859-9";

		internal const string ISO_8859_10 = "iso-8859-10";

		internal const string ISO_8859_11 = "iso-8859-11";

		internal const string ISO_8859_13 = "iso-8859-13";

		internal const string ISO_8859_15 = "iso-8859-15";

		internal const string ISO_8859_16 = "iso-8859-16";

		internal const string WINDOWS_1250 = "windows-1250";

		internal const string WINDOWS_1251 = "windows-1251";

		internal const string WINDOWS_1252 = "windows-1252";

		internal const string WINDOWS_1253 = "windows-1253";

		internal const string WINDOWS_1255 = "windows-1255";

		internal const string WINDOWS_1256 = "windows-1256";

		internal const string WINDOWS_1257 = "windows-1257";

		internal const string WINDOWS_1258 = "windows-1258";

		internal const string X_MAC_CE = "x-mac-ce";

		internal const string X_MAC_CYRILLIC = "x-mac-cyrillic";

		internal const string KOI8_R = "koi8-r";

		internal const string TIS_620 = "tis-620";

		internal const string VISCII = "viscii";

		internal const string X_ISO_10646_UCS_4_3412 = "X-ISO-10646-UCS-4-3412";

		internal const string X_ISO_10646_UCS_4_2143 = "X-ISO-10646-UCS-4-2143";
	}
}
