﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace MAutoUpdate.Control
{
    public class YButton : Label
    {
        public bool IsFontChange { get; set; }

        public bool IsColorChange { get; set; } = true;

        public Color NormalColor { get; set; } = Color.FromArgb(56, 95, 170);

        public Color MoveColor { get; set; } = Color.FromArgb(128, 156, 211);

        public Color MoveFontColor { get; set; } = Color.FromArgb(128, 156, 211);

        public Color NormalFontColor { get; set; } = Color.FromArgb(128, 156, 211);

        public Image EnterImage { get; set; }

        public YButton()
        {
            Size = new Size(61, 23);
            ForeColor = Color.FromArgb(240, 240, 240);
            BackColor = NormalColor;
            TextAlign = ContentAlignment.MiddleCenter;
            AutoSize = false;
            Cursor = Cursors.Hand;
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            if (IsColorChange)
            {
                BackColor = MoveColor;
            }
            if (IsFontChange)
            {
                ForeColor = MoveFontColor;
            }

            base.OnMouseEnter(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            if (IsColorChange)
            {
                BackColor = NormalColor;
            }
            if (IsFontChange)
            {
                ForeColor = NormalFontColor;
            }
            base.OnMouseLeave(e);
        }
    }
}
