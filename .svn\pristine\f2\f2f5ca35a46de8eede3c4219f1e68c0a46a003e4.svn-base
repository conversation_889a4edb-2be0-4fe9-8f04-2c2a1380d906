using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class BOOKBOOL : Record
	{
		public ushort NotSaveExternalLinkedValues;

		public BOOKBOOL(Record record)
			: base(record)
		{
		}

		public BOOKBOOL()
		{
			Type = 218;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			NotSaveExternalLinkedValues = binaryReader.ReadUInt16();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(NotSaveExternalLinkedValues);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
