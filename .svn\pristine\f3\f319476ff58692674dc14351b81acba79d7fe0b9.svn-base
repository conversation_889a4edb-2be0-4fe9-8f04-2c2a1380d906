﻿#region License Information (GPL v3)

/*
    ShareX - A program that allows you to take screenshots and share any file type
    Copyright (c) 2007-2021 ShareX Team

    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License
    as published by the Free Software Foundation; either version 2
    of the License, or (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.

    Optionally you can also view the license at <http://www.gnu.org/licenses/>.
*/

#endregion License Information (GPL v3)

using ShareX.HelpersLib;
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.Windows.Forms;
using OCRTools;
using OCRTools.Common;

namespace ShareX.ScreenCaptureLib
{
    public class Screenshot
    {
        public bool CaptureClientArea { get; set; } = false;
        public bool RemoveOutsideScreenArea { get; set; } = true;
        public bool AutoHideTaskbar { get; set; } = false;

        private static Rectangle GetWorkAreaRectangle()
        {
            return Rectangle.Union(new Rectangle(Point.Empty, PrimaryScreen.DESKTOP), NativeMethods.GetScreenBounds());
        }

        public Bitmap CaptureRectangle(Rectangle srcRect, Rectangle destRect)
        {
            if (RemoveOutsideScreenArea)
            {
                srcRect = Rectangle.Intersect(GetWorkAreaRectangle(), srcRect);
            }
            return CaptureRectangleNative(srcRect, destRect);
        }

        public Bitmap CaptureFullscreen(ref Rectangle srcRect, bool isNeedScal = false)
        {
            if (srcRect.IsEmpty)
                srcRect = GetWorkAreaRectangle();
            var destRect = srcRect;
            if (isNeedScal && PrimaryScreen.GetScreenScalingFactor() > 1)
            {
                destRect = NativeMethods.GetScreenBounds();
            }
            var bitMap = CaptureRectangle(srcRect, destRect);
            srcRect = destRect;
            return bitMap;
        }

        public Bitmap CaptureWindow(IntPtr handle, ref Rectangle rect)
        {
            if (handle.ToInt32() > 0)
            {
                rect = handle.GetRectangle(!CaptureClientArea);

                var isTaskbarHide = false;

                try
                {
                    if (AutoHideTaskbar) isTaskbarHide = NativeMethods.SetTaskbarVisibilityIfIntersect(false, rect);

                    return CaptureRectangle(rect, rect);
                }
                finally
                {
                    if (isTaskbarHide) NativeMethods.SetTaskbarVisibility(true);
                }
            }

            return null;
        }

        public Bitmap CaptureActiveWindow(ref Rectangle rect)
        {
            var handle = NativeMethods.GetForegroundWindow();

            return CaptureWindow(handle, ref rect);
        }

        public Bitmap CaptureActiveMonitor(ref Rectangle rect)
        {
            rect = NativeMethods.GetActiveScreenBounds();

            return CaptureRectangle(rect, rect);
        }

        private Bitmap CaptureRectangleNative(Rectangle srcRect, Rectangle destRect)
        {
            var handle = NativeMethods.GetDesktopWindow();
            return CaptureRectangleNative(handle, srcRect, destRect);
        }

        private Bitmap CaptureRectangleNative(IntPtr handle, Rectangle srcRect, Rectangle destRect)
        {
            if (srcRect.Width == 0 || srcRect.Height == 0) return null;
            //if (!srcRect.Size.Equals(destRect.Size))
            //{
            //    using (var source = CaptureRectangleManaged(srcRect))
            //    {
            //        var sourceStr = source.SaveFileWithOutConfirm(CommonSetting.截图文件保存路径 + ServerTime.DateTime.Ticks + "_source.png");
            //        var image = ImageProcessHelper.ResizeImage(source, destRect.Width, destRect.Height, Color.Transparent, false, false,
            //            0);
            //        var destStr = image.SaveFileWithOutConfirm(CommonSetting.截图文件保存路径 + ServerTime.DateTime.Ticks + "_dest.png");
            //        return image;
            //    }
            //}

            IntPtr hdcSrc = NativeMethods.GetWindowDC(handle);
            IntPtr hdcDest = NativeMethods.CreateCompatibleDC(hdcSrc);
            IntPtr hBitmap = NativeMethods.CreateCompatibleBitmap(hdcSrc, srcRect.Width, srcRect.Height);
            IntPtr hOld = NativeMethods.SelectObject(hdcDest, hBitmap);
            if (srcRect.Size.Equals(destRect.Size))
            {
                NativeMethods.BitBlt(hdcDest, 0, 0, srcRect.Width, srcRect.Height, hdcSrc, srcRect.X, srcRect.Y, CopyPixelOperation.SourceCopy | CopyPixelOperation.CaptureBlt);
            }
            else
            {
                NativeMethods.SetStretchBltMode(hdcDest, 4);
                NativeMethods.StretchBlt(hdcDest, destRect.X, destRect.Y, destRect.Width, destRect.Height
                    , hdcSrc, srcRect.X, srcRect.Y, srcRect.Width, srcRect.Height,
                    CopyPixelOperation.SourceCopy | CopyPixelOperation.CaptureBlt);
            }

            NativeMethods.SelectObject(hdcDest, hOld);
            NativeMethods.DeleteDC(hdcDest);
            NativeMethods.ReleaseDC(handle, hdcSrc);
            Bitmap bmp = Image.FromHbitmap(hBitmap);
            NativeMethods.DeleteObject(hBitmap);
            return bmp;
        }

        private Bitmap CaptureRectangleManaged(Rectangle rect)
        {
            if (rect.Width == 0 || rect.Height == 0)
            {
                return null;
            }

            Bitmap bmp = new Bitmap(rect.Width, rect.Height, PixelFormat.Format32bppArgb);

            using (Graphics g = Graphics.FromImage(bmp))
            {
                g.SetHighQuality();
                // Managed can't use SourceCopy | CaptureBlt because of .NET bug
                g.CopyFromScreen(rect.Location, Point.Empty, rect.Size, CopyPixelOperation.SourceCopy);
            }

            return bmp;
        }
    }
}