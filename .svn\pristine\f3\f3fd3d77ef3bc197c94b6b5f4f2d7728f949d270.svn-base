﻿using OCRTools.Common;
using OCRTools.Language;
using OCRTools.Properties;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools.ScreenCaptureLib
{
    public sealed class RegionCaptureForm : BaseForm
    {
        public RegionCaptureOptions Options { get; set; }
        public Rectangle ClientArea { get; private set; }
        public Bitmap Canvas { get; private set; }
        public Rectangle CanvasRectangle { get; internal set; }
        public RegionResult Result { get; private set; }
        public int MonitorIndex { get; set; }

        public RegionCaptureMode Mode { get; set; }

        public bool IsEditorMode => Mode == RegionCaptureMode.Editor || Mode == RegionCaptureMode.WhiteBoard;

        public bool IsAnnotationMode => Mode == RegionCaptureMode.Annotation || IsEditorMode;

        public bool IsImageModified => ShapeManager != null && ShapeManager.IsImageModified;

        public Point CurrentPosition { get; private set; }
        public Point PanningStrech;

        public WindowInfo SelectedWindow { get; private set; }

        public Vector CanvasCenterOffset { get; set; } = new Vector(0f, 0f);

        internal ShapeManager ShapeManager { get; private set; }
        internal bool IsClosing { get; private set; }

        internal Bitmap DimmedCanvas;
        internal Image CustomNodeImage = Resources.CircleNode;
        internal int ToolbarHeight;

        private InputManager InputManager => ShapeManager.InputManager;
        private TextureBrush backgroundBrush, backgroundHighlightBrush;
        private GraphicsPath regionFillPath, regionDrawPath;
        private Pen borderPen, borderDotPen, borderDotStaticPen, textOuterBorderPen, textInnerBorderPen, markerPen, canvasBorderPen;
        private Brush textBrush, textShadowBrush, textBackgroundBrush, backBrush;
        private Font infoFont, infoFontMedium, infoFontBig;
        private Stopwatch timerStart;
        private bool pause, isKeyAllowed, forceClose;
        private RectangleAnimation regionAnimation;
        private Cursor defaultCursor = CursorEx.Cross;
        private Cursor openHandCursor = CursorEx.OpenHand;
        private Cursor closedHandCursor = CursorEx.CloseHand;
        private Color canvasBackgroundColor, canvasBorderColor, textColor, textShadowColor, textBackgroundColor, textOuterBorderColor, textInnerBorderColor;

        public RegionCaptureForm(RegionCaptureMode mode, RegionCaptureOptions options, Bitmap canvas = null)
        {
            Mode = mode;

            options.EnableAnimations = CommonSetting.截图动画效果;
            options.ShowMagnifier = CommonSetting.显示放大镜;
            options.ShowCrosshair = CommonSetting.显示全屏十字线;
            options.DetectWindows = CommonSetting.自动检测窗口;
            options.DetectControls = CommonSetting.自动检测窗口元素;
            options.IsSmallControlModel = CommonSetting.自动检测窗口元素;
            options.UseSquareMagnifier = !CommonSetting.圆形放大镜;
            options.DrawMagnifierCenterLines = true;
            options.ShowInfo = true;

            options.MagnifierPixelCount = 13;
            options.MagnifierPixelSize = 10;

            if (Mode == RegionCaptureMode.ScreenColorPicker)
            {
                options.ShowMagnifier = false;
                options.DetectWindows = false;
            }
            else if (Mode == RegionCaptureMode.Magnifier)
            {
                options.MagnifierPixelSize = CommonSetting.默认放大倍数 > 0 ? (int)CommonSetting.默认放大倍数 : 3;
                options.MagnifierPixelCount = 240 / options.MagnifierPixelSize;
                options.DrawMagnifierCenterLines = false;
                options.ShowInfo = false;
                options.ShowMagnifier = true;
                options.DetectWindows = false;
            }
            else if (Mode == RegionCaptureMode.WhiteBoard)
            {
                options.DetectWindows = false;
                options.ShowInfo = false;
                options.ShowMagnifier = false;
            }
            else if (Mode == RegionCaptureMode.Editor)
            {
                options.DetectWindows = false;
            }
            else if (Mode == RegionCaptureMode.OneClick)
            {
                options.ShowMagnifier = false;
                options.DetectWindows = true;
                options.DetectControls = true;
                options.IsSmallControlModel = false;
            }

            Options = options;

            if (canvas == null)
            {
                Rectangle rect = Rectangle.Empty;
                canvas = Screenshot.CaptureFullscreen(ref rect, true);
                //canvas.SaveFileWithOutConfirm();
                //System.Threading.Thread.Sleep(1000);
                //var a = Screenshot.ResizeImage(canvas, rect.Width, rect.Height);
                //a.SaveFileWithOutConfirm();
                //System.Threading.Thread.Sleep(1000);
                //var b = Screenshot.ZoomImage(canvas, rect.Height, rect.Width);
                //b.SaveFileWithOutConfirm();
            }

            //this.Activated += (sender, e) => Cursor.Clip = this.Bounds;
            //this.Deactivate += (sender, e) => Cursor.Clip = Rectangle.Empty;

            ClientArea = NativeMethods.GetScreenBounds0Based();
            CanvasRectangle = ClientArea;

            timerStart = new Stopwatch();
            regionAnimation = new RectangleAnimation()
            {
                Duration = TimeSpan.FromMilliseconds(200)
            };

            borderPen = new Pen(Color.White);
            borderDotPen = new Pen(CommonSetting.截图边框颜色, (float)Math.Max(1, CommonSetting.截图边框宽度)) { DashPattern = new float[] { 5, 2 } };
            borderDotStaticPen = new Pen(Color.White) { DashPattern = new float[] { 5, 2 } };
            infoFont = CommonString.GetSysNormalFont(16);
            infoFontMedium = CommonString.GetSysNormalFont(16);
            infoFontBig = CommonString.GetSysBoldFont(21);
            markerPen = new Pen(Color.FromArgb(200, Color.Red));

            canvasBackgroundColor = Color.FromArgb(255, 42, 47, 56);
            backBrush = ImageBoxGridDisplayModeHelper.GetBrush(canvasBorderColor);
            //backBrush = ImageBoxGridDisplayModeHelper.GetBackBrush(ref canvasBackgroundColor);
            canvasBorderColor = Color.FromArgb(200, 28, 32, 38);
            textColor = Color.FromArgb(255, 235, 235, 235);
            textShadowColor = Color.FromArgb(255, 28, 32, 38);
            textBackgroundColor = Color.FromArgb(200, 42, 47, 56);
            textOuterBorderColor = Color.FromArgb(200, 22, 26, 31);
            textInnerBorderColor = Color.FromArgb(200, 56, 64, 75);

            canvasBorderPen = new Pen(canvasBorderColor);
            textBrush = new SolidBrush(textColor);
            textShadowBrush = new SolidBrush(textShadowColor);
            textBackgroundBrush = new SolidBrush(textBackgroundColor);
            textOuterBorderPen = new Pen(textOuterBorderColor);
            textInnerBorderPen = new Pen(textInnerBorderColor);

            Prepare(canvas);

            InitializeComponent();
        }

        private void InitializeComponent()
        {
            SuspendLayout();

            SetStyle(ControlStyles.OptimizedDoubleBuffer | ControlStyles.DoubleBuffer | ControlStyles.UserPaint | ControlStyles.AllPaintingInWmPaint, true);

            Text = CommonString.FullName.CurrentText();
            AutoScaleMode = AutoScaleMode.None;
            SetDefaultCursor();
            StartPosition = FormStartPosition.Manual;

            FormBorderStyle = FormBorderStyle.None;

            Bounds = NativeMethods.GetScreenBounds();

            ShowInTaskbar = IsEditorMode;
#if !DEBUG
            TopMost = true;
#endif

            Shown += RegionCaptureForm_Shown;
            KeyDown += RegionCaptureForm_KeyDown;
            MouseDown += RegionCaptureForm_MouseDown;
            Resize += RegionCaptureForm_Resize;
            LocationChanged += RegionCaptureForm_LocationChanged;
            LostFocus += RegionCaptureForm_LostFocus;
            GotFocus += RegionCaptureForm_GotFocus;
            FormClosing += RegionCaptureForm_FormClosing;

            ResumeLayout(false);
        }

        private void Prepare(Bitmap canvas = null)
        {
            ShapeManager = new ShapeManager(this)
            {
                WindowCaptureMode = !IsEditorMode && Options.DetectWindows,
                IncludeControls = Options.DetectControls,
                IsSmallControlModel = Options.IsSmallControlModel
            };
            ShapeManager.ImageModified += ShapeManager_ImageModified;

            InitBackground(canvas);

            if (Mode == RegionCaptureMode.OneClick || ShapeManager.WindowCaptureMode)
            {
                InitWindows();
            }
        }

        private void ShapeManager_ImageModified()
        {
            if (!IsClosing && IsEditorMode)
            {
            }
        }

        /// <summary>
        /// 截屏窗口是否正在运行
        /// </summary>
        public bool IsExitCapture { get; set; }

        private void InitWindows()
        {
            var selectRectangleList = new SelectRectangleList
            {
                IgnoreHandle = Handle,
                IncludeChildWindows = Options.DetectControls
            };
            ShapeManager.Windows = selectRectangleList.GetWindowInfoListAsync(5000, IntPtr.Zero);
            if (Options.DetectControls && ShapeManager.Windows?.Count > 0)
            {
                new Thread(() =>
                {
                    selectRectangleList.SetWindowZOrder(ShapeManager.Windows);
                    Parallel.Invoke(() =>
                    {
                        Parallel.ForEach(ShapeManager.Windows.Where(p => p.IsWindow).OrderByDescending(p => p.ZIndex),
                            new ParallelOptions { MaxDegreeOfParallelism = 5 }, windowInfo =>
                            {
                                if (IsExitCapture || !windowInfo.Rectangle.IsValid())
                                {
                                    return;
                                }
                                var lstTmp = CommonAutomation.InitChildHandle(windowInfo, System.Windows.Automation.TreeScope.Children);
                                ProcessWindowList(lstTmp);
                            });
                    }, () =>
                    {
                        Parallel.ForEach(ShapeManager.Windows.Where(p => p.IsWindow).OrderByDescending(p => p.ZIndex),
                            new ParallelOptions { MaxDegreeOfParallelism = 3 }, windowInfo =>
                            {
                                if (IsExitCapture || !windowInfo.Rectangle.IsValid())
                                {
                                    return;
                                }
                                var lstTmp = CommonAutomation.InitChildHandle(windowInfo, System.Windows.Automation.TreeScope.Descendants);
                                ProcessWindowList(lstTmp);
                            });
                    });
                }).Start();
            }
        }

        private void ProcessWindowList(List<WindowInfo> lstTmp)
        {
            if (lstTmp.Count <= 0) return;
            lstTmp.ForEach(p =>
            {
                if (p.Rectangle.IsValid() && !ShapeManager.Windows.Any(q => q.Rectangle.Equals(p.Rectangle)))
                    ShapeManager.Windows.Add(p);
            });
        }

        internal void InitBackground(Bitmap canvas, bool centerCanvas = true)
        {
            Canvas = canvas;

            if (IsEditorMode)
            {
                CanvasRectangle = new Rectangle(CanvasRectangle.X, CanvasRectangle.Y, Canvas.Width, Canvas.Height);

                using (Bitmap background = new Bitmap(Canvas.Width, Canvas.Height))
                using (Graphics g = Graphics.FromImage(background))
                {
                    Rectangle sourceRect = new Rectangle(0, 0, Canvas.Width, Canvas.Height);

                    using (Bitmap checkers = ImageHelp.DrawCheckers(Canvas.Width, Canvas.Height, 15,
                        Color.FromArgb(255, 60, 60, 60), Color.FromArgb(255, 50, 50, 50)))
                    {
                        g.DrawImage(checkers, sourceRect);
                    }

                    g.DrawImage(Canvas, sourceRect);

                    backgroundBrush = new TextureBrush(background) { WrapMode = WrapMode.Clamp };
                    backgroundBrush.TranslateTransform(CanvasRectangle.X, CanvasRectangle.Y);
                }

                if (centerCanvas)
                {
                    CenterCanvas();
                }
            }
            else if (Options.UseDimming)
            {
                DimmedCanvas?.Dispose();
                DimmedCanvas = (Bitmap)Canvas.Clone();

                using (Graphics g = Graphics.FromImage(DimmedCanvas))
                using (Brush brush = new SolidBrush(Color.FromArgb(30, Color.Black)))
                {
                    g.FillRectangle(brush, 0, 0, DimmedCanvas.Width, DimmedCanvas.Height);

                    backgroundBrush = new TextureBrush(DimmedCanvas) { WrapMode = WrapMode.Clamp };
                }

                backgroundHighlightBrush = new TextureBrush(Canvas) { WrapMode = WrapMode.Clamp };
            }
            else
            {
                backgroundBrush = new TextureBrush(Canvas) { WrapMode = WrapMode.Clamp };
            }
        }

        private void OnMoved()
        {
            if (ShapeManager != null)
            {
                UpdateCoordinates();

                if (IsAnnotationMode && ShapeManager.ToolbarCreated)
                {
                    ShapeManager.UpdateMenuMaxWidth(ClientSize.Width);
                    ShapeManager.UpdateMenuPosition();
                }
            }
        }

        private void Pan(int deltaX, int deltaY, bool usePanningStretch = true)
        {
            if (usePanningStretch)
            {
                PanningStrech.X -= deltaX;
                PanningStrech.Y -= deltaY;
            }

            Size panLimitSize = new Size(Math.Min((int)Math.Round(ClientArea.Width * 0.25f), CanvasRectangle.Width),
                Math.Min((int)Math.Round(ClientArea.Height * 0.25f), CanvasRectangle.Height));

            Rectangle limitRectangle = new Rectangle(ClientArea.X + panLimitSize.Width, ClientArea.Y + panLimitSize.Height,
                ClientArea.Width - (panLimitSize.Width * 2), ClientArea.Height - (panLimitSize.Height * 2));

            deltaX = Math.Max(deltaX, limitRectangle.Left - CanvasRectangle.Right);
            deltaX = Math.Min(deltaX, limitRectangle.Right - CanvasRectangle.Left);
            deltaY = Math.Max(deltaY, limitRectangle.Top - CanvasRectangle.Bottom);
            deltaY = Math.Min(deltaY, limitRectangle.Bottom - CanvasRectangle.Top);

            if (usePanningStretch)
            {
                deltaX -= Math.Min(Math.Max(deltaX, 0), Math.Max(0, PanningStrech.X));
                deltaX -= Math.Max(Math.Min(deltaX, 0), Math.Min(0, PanningStrech.X));
                deltaY -= Math.Min(Math.Max(deltaY, 0), Math.Max(0, PanningStrech.Y));
                deltaY -= Math.Max(Math.Min(deltaY, 0), Math.Min(0, PanningStrech.Y));

                PanningStrech.X += deltaX;
                PanningStrech.Y += deltaY;
            }

            CanvasRectangle = CanvasRectangle.LocationOffset(deltaX, deltaY);

            if (backgroundBrush != null)
            {
                backgroundBrush.TranslateTransform(deltaX, deltaY);
            }

            if (ShapeManager != null)
            {
                ShapeManager.MoveAll(deltaX, deltaY);
            }
        }

        private void Pan(Point delta)
        {
            Pan(delta.X, delta.Y);
        }

        public void AutomaticPan()
        {
            if (IsEditorMode)
            {
                int x = (int)Math.Round((ClientArea.Width * 0.5f) + CanvasCenterOffset.X);
                int y = (int)Math.Round((ClientArea.Height * 0.5f) + CanvasCenterOffset.Y);
                int newX = x - (CanvasRectangle.Width / 2);
                int newY = y - (CanvasRectangle.Height / 2);
                int deltaX = newX - CanvasRectangle.X;
                int deltaY = newY - CanvasRectangle.Y;
                Pan(deltaX, deltaY, false);
            }
        }

        private void UpdateCenterOffset()
        {
            CanvasCenterOffset = new Vector(CanvasRectangle.X + (CanvasRectangle.Width / 2f) - (ClientArea.Width / 2f),
                CanvasRectangle.Y + (CanvasRectangle.Height / 2f) - (ClientArea.Height / 2f));
        }

        public void CenterCanvas()
        {
            CanvasCenterOffset = new Vector(0f, ToolbarHeight / 2f);
            AutomaticPan();
        }

        public void SetDefaultCursor()
        {
            if (Cursor != defaultCursor)
            {
                Cursor = defaultCursor;
            }
        }

        public void SetHandCursor(bool grabbing)
        {
            if (grabbing)
            {
                if (Cursor != closedHandCursor)
                {
                    Cursor = closedHandCursor;
                }
            }
            else
            {
                if (Cursor != openHandCursor)
                {
                    Cursor = openHandCursor;
                }
            }
        }

        private void RegionCaptureForm_Shown(object sender, EventArgs e)
        {
            this.ForceActivate();

            OnMoved();
            CenterCanvas();
        }

        private void RegionCaptureForm_Resize(object sender, EventArgs e)
        {
            OnMoved();
            AutomaticPan();
        }

        private void RegionCaptureForm_LocationChanged(object sender, EventArgs e)
        {
            OnMoved();
        }

        private void RegionCaptureForm_GotFocus(object sender, EventArgs e)
        {
            Resume();
        }

        private void RegionCaptureForm_LostFocus(object sender, EventArgs e)
        {
            Pause();
        }

        private void RegionCaptureForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (IsEditorMode)
            {
                if (e.CloseReason == CloseReason.UserClosing && !forceClose && !ShowExitConfirmation())
                {
                    e.Cancel = true;
                }
            }
        }

        internal bool ShowExitConfirmation()
        {
            bool result = true;

            if (IsImageModified)
            {
                Pause();
                result = MessageBox.Show(this, "当前有未保存的改动，确定要退出？".CurrentText(), CommonString.FullName.CurrentText(),
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes;
                Resume();
            }

            return result;
        }

        internal void RegionCaptureForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyData == Keys.Escape)
            {
                if (ShapeManager.HandleEscape())
                {
                    return;
                }

                if (!IsEditorMode || ShowExitConfirmation())
                {
                    CloseWindow();
                }
                return;
            }

            if (!isKeyAllowed && timerStart.ElapsedMilliseconds < Options.InputDelay)
            {
                return;
            }

            isKeyAllowed = true;

            switch (e.KeyData)
            {
                case Keys.Space:
                    CloseWindow(RegionResult.Fullscreen);
                    break;
                case Keys.Enter:
                    if (ShapeManager.IsCurrentShapeTypeRegion)
                    {
                        ShapeManager.StartRegionSelection();
                        ShapeManager.EndRegionSelection();
                    }

                    CloseWindow(RegionResult.Region);
                    break;
                case Keys.Oemtilde:
                    CloseWindow(RegionResult.ActiveMonitor);
                    break;
                case Keys.Control | Keys.C:
                    CopyAreaInfo();
                    break;
                case Keys.Control | Keys.Z:
                    ShapeManager.UndoShape();
                    break;
                case Keys.Control | Keys.ControlKey:
                    if (Options.DetectWindows && ShapeManager != null)
                    {
                        ShapeManager.IsSmallControlModel = !ShapeManager.IsSmallControlModel;
                    }
                    break;
            }

            if (!IsEditorMode && e.KeyData >= Keys.D0 && e.KeyData <= Keys.D9)
            {
                MonitorKey(e.KeyData - Keys.D0);
            }
        }

        private void RegionCaptureForm_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                SelectedWindow = ShapeManager.NowSelectedWindow;

                if (Mode == RegionCaptureMode.OneClick || Mode == RegionCaptureMode.ScreenColorPicker || Mode == RegionCaptureMode.Magnifier)
                {
                    CurrentPosition = InputManager.MousePosition;

                    CloseWindow(RegionResult.Region);
                }
            }
            else
            {
                if (IsEditorMode && e.Button == MouseButtons.Middle)
                {
                    CloseWindow(RegionResult.Region);
                }
            }
        }

        private void MonitorKey(int index)
        {
            if (index == 0)
            {
                index = 10;
            }

            index--;

            MonitorIndex = index;

            CloseWindow(RegionResult.Monitor);
        }

        public Rectangle RectangleResult => ShapeManager?.CurrentRectangle ?? Rectangle.Empty;

        internal void CloseWindow(RegionResult result = RegionResult.Close)
        {
            Result = result;
            forceClose = true;
            Close();
        }

        internal void Pause()
        {
            pause = true;
        }

        internal void Resume()
        {
            pause = false;

            Invalidate();
        }

        private void CopyAreaInfo()
        {
            string clipboardText;

            if (ShapeManager.IsCurrentShapeValid)
            {
                clipboardText = GetAreaText(ShapeManager.CurrentRectangle);
            }
            else
            {
                CurrentPosition = InputManager.MousePosition;
                clipboardText = GetInfoText();
            }
            ClipboardService.SetText(clipboardText);
        }

        private void UpdateCoordinates()
        {
            ClientArea = ClientRectangle;

            InputManager.Update(this);
        }

        private new void Update()
        {
            if (!timerStart.IsRunning)
            {
                timerStart.Start();
            }

            UpdateCoordinates();

            ShapeManager.UpdateObjects();

            if (ShapeManager.IsPanning)
            {
                Pan(InputManager.MouseVelocity);
                UpdateCenterOffset();
            }

            if (Options.EnableAnimations)
            {
                borderDotPen.DashOffset = (float)timerStart.Elapsed.TotalSeconds * -15;
            }

            ShapeManager.Update();
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            //base.OnPaintBackground(e);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            Update();

            Graphics g = e.Graphics;

            if (IsEditorMode && !CanvasRectangle.Contains(ClientArea))
            {
                g.FillRectangle(backBrush, ClientRectangle);
                //g.Clear(canvasBackgroundColor);
                g.DrawRectangleProper(canvasBorderPen, CanvasRectangle.Offset(1));
            }

            g.CompositingMode = CompositingMode.SourceCopy;
            g.FillRectangle(backgroundBrush, CanvasRectangle);
            g.CompositingMode = CompositingMode.SourceOver;

            Draw(g);

            if (!pause)
            {
                Invalidate();
            }
        }

        private void Draw(Graphics g)
        {
            // Draw snap rectangles
            if (ShapeManager.IsCreating && ShapeManager.IsSnapResizing)
            {
                BaseShape shape = ShapeManager.CurrentShape;

                if (shape != null && shape.ShapeType != ShapeType.画笔 && shape.ShapeType != ShapeType.自由截图)
                {
                    foreach (Size size in Options.SnapSizes)
                    {
                        Rectangle snapRect = ImageHelp.CalculateNewRectangle(shape.StartPosition, shape.EndPosition, size);
                        g.DrawRectangleProper(markerPen, snapRect);
                    }
                }
            }

            var areas = ShapeManager.ValidRegions.ToList();

            if (areas.Count > 0)
            {
                // Create graphics path from all regions
                UpdateRegionPath();

                // If background is dimmed then draw non dimmed background to region selections
                if (!IsEditorMode && Options.UseDimming)
                {
                    using (Region region = new Region(regionDrawPath))
                    {
                        g.Clip = region;
                        g.FillRectangle(backgroundHighlightBrush, ClientArea);
                        g.ResetClip();
                    }
                }

                g.DrawPath(borderPen, regionDrawPath);
                g.DrawPath(borderDotStaticPen, regionDrawPath);
            }

            // Draw effect shapes
            foreach (BaseEffectShape effectShape in ShapeManager.EffectShapes)
            {
                effectShape.OnDraw(g);
            }

            // Draw drawing shapes
            foreach (BaseDrawingShape drawingShape in ShapeManager.DrawingShapes)
            {
                drawingShape.OnDraw(g);
            }

            // Draw tools
            foreach (BaseTool toolShape in ShapeManager.ToolShapes)
            {
                toolShape.OnDraw(g);
            }

            // Draw animated rectangle on hover area
            if (ShapeManager.IsCurrentHoverShapeValid)
            {
                if (Options.EnableAnimations)
                {
                    if (!ShapeManager.PreviousHoverRectangle.IsEmpty && ShapeManager.CurrentHoverShape.Rectangle != ShapeManager.PreviousHoverRectangle)
                    {
                        if (regionAnimation.CurrentRectangle.Width > 2 && regionAnimation.CurrentRectangle.Height > 2)
                        {
                            regionAnimation.FromRectangle = regionAnimation.CurrentRectangle;
                        }
                        else
                        {
                            regionAnimation.FromRectangle = ShapeManager.PreviousHoverRectangle;
                        }

                        regionAnimation.ToRectangle = ShapeManager.CurrentHoverShape.Rectangle;
                        regionAnimation.Start();
                    }

                    regionAnimation.Update();
                }

                using (GraphicsPath hoverDrawPath = new GraphicsPath())
                {
                    hoverDrawPath.FillMode = FillMode.Winding;
                    if (Options.EnableAnimations && regionAnimation.IsActive && regionAnimation.CurrentRectangle.Width > 2 && regionAnimation.CurrentRectangle.Height > 2)
                    {
                        ShapeManager.CurrentHoverShape.OnShapePathRequested(hoverDrawPath, regionAnimation.CurrentRectangle.SizeOffset(-1));
                    }
                    else
                    {
                        ShapeManager.CurrentHoverShape.AddShapePath(hoverDrawPath, -1);
                    }

                    g.DrawPath(borderPen, hoverDrawPath);
                    g.DrawPath(borderDotPen, hoverDrawPath);
                }
            }

            // Draw animated rectangle on selection area
            if (ShapeManager.IsCurrentShapeTypeRegion && ShapeManager.IsCurrentShapeValid)
            {
                if (Mode == RegionCaptureMode.Ruler)
                {
                    using (SolidBrush brush = new SolidBrush(Color.FromArgb(50, 255, 255, 255)))
                    {
                        g.FillRectangle(brush, ShapeManager.CurrentRectangle);
                    }

                    DrawRuler(g, ShapeManager.CurrentRectangle, borderPen, 5, 10);
                    DrawRuler(g, ShapeManager.CurrentRectangle, borderPen, 15, 100);

                    g.DrawCross(borderPen, ShapeManager.CurrentRectangle.Center(), 10);
                }

                DrawRegionArea(g, ShapeManager.CurrentRectangle, true);
            }

            // Draw all regions rectangle info
            if (Options.ShowInfo)
            {
                // Add hover area to list so rectangle info can be shown
                if (ShapeManager.IsCurrentShapeTypeRegion && ShapeManager.IsCurrentHoverShapeValid && areas.All(area => area.Rectangle != ShapeManager.CurrentHoverShape.Rectangle))
                {
                    areas.Add(ShapeManager.CurrentHoverShape);
                }

                foreach (BaseShape regionInfo in areas)
                {
                    if (regionInfo.Rectangle.IsValid())
                    {
                        string areaText = GetAreaText(regionInfo.Rectangle);
                        DrawAreaText(g, areaText, regionInfo.Rectangle);
                    }
                }
            }

            // Draw resize nodes
            ShapeManager.DrawObjects(g);

            // Draw magnifier
            if (Options.ShowMagnifier || Options.ShowInfo)
            {
                DrawCursorGraphics(g);
            }

            // Draw screen wide crosshair
            if (Options.ShowCrosshair)
            {
                DrawCrosshair(g);
            }

            // Draw menu tooltips
            if (IsAnnotationMode && ShapeManager.MenuTextAnimation.Update())
            {
                DrawTextAnimation(g, ShapeManager.MenuTextAnimation);
            }
        }

        internal void DrawRegionArea(Graphics g, Rectangle rect, bool isAnimated)
        {
            g.DrawRectangleProper(borderPen, rect);

            if (isAnimated)
            {
                g.DrawRectangleProper(borderDotPen, rect);
            }
            else
            {
                g.DrawRectangleProper(borderDotStaticPen, rect);
            }
        }

        private void DrawInfoText(Graphics g, string text, Rectangle rect, Font font, int padding)
        {
            DrawInfoText(g, text, rect, font, new Point(padding, padding));
        }

        private void DrawInfoText(Graphics g, string text, Rectangle rect, Font font, Point padding)
        {
            DrawInfoText(g, text, rect, font, padding, textBackgroundBrush, textOuterBorderPen, textInnerBorderPen, textBrush, textShadowBrush);
        }

        private void DrawInfoText(Graphics g, string text, Rectangle rect, Font font, int padding,
            Brush backgroundBrush, Pen outerBorderPen, Pen innerBorderPen, Brush textBrush, Brush textShadowBrush)
        {
            DrawInfoText(g, text, rect, font, new Point(padding, padding), backgroundBrush, outerBorderPen, innerBorderPen, textBrush, textShadowBrush);
        }

        private void DrawInfoText(Graphics g, string text, Rectangle rect, Font font, Point padding,
            Brush backgroundBrush, Pen outerBorderPen, Pen innerBorderPen, Brush textBrush, Brush textShadowBrush)
        {
            g.InterpolationMode = InterpolationMode.HighQualityBilinear;
            g.CompositingQuality = CompositingQuality.HighQuality;
            g.SmoothingMode = SmoothingMode.HighQuality;
            g.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
            g.FillRectangle(backgroundBrush, rect.Offset(-2));
            g.DrawRectangleProper(innerBorderPen, rect.Offset(-1));
            g.DrawRectangleProper(outerBorderPen, rect);

            g.DrawTextWithShadow(text, rect.LocationOffset(padding.X, padding.Y).Location, font, textBrush, textShadowBrush);
        }

        internal void DrawAreaText(Graphics g, string text, Rectangle area)
        {
            int offset = 6;
            int backgroundPadding = 3;
            try
            {
                Size textSize = g.MeasureString(text, infoFont).ToSize();
                Point textPos;

                if (area.Y - offset - textSize.Height - (backgroundPadding * 2) < ClientArea.Y)
                {
                    textPos = new Point(area.X + offset + backgroundPadding, area.Y + offset + backgroundPadding);
                }
                else
                {
                    textPos = new Point(area.X + backgroundPadding, area.Y - offset - backgroundPadding - textSize.Height);
                }

                if (textPos.X + textSize.Width + backgroundPadding >= ClientArea.Width)
                {
                    textPos.X = ClientArea.Width - textSize.Width - backgroundPadding;
                }

                Rectangle backgroundRect = new Rectangle(textPos.X - backgroundPadding, textPos.Y - backgroundPadding, textSize.Width + (backgroundPadding * 2), textSize.Height + (backgroundPadding * 2));

                DrawInfoText(g, text, backgroundRect, infoFont, backgroundPadding);
            }
            catch (Exception oe)
            {
                Log.WriteError("DrawAreaText:" + text, oe);
            }
        }

        private void DrawTextAnimation(Graphics g, TextAnimation textAnimation)
        {
            Size textSize = g.MeasureString(textAnimation.Text, infoFontMedium).ToSize();
            int padding = 3;
            textSize.Width += padding * 2;
            textSize.Height += padding * 2;
            Rectangle textRectangle = new Rectangle(textAnimation.Position.X, Math.Abs(textAnimation.Position.Y), textSize.Width, textSize.Height);
            DrawTextAnimation(g, textAnimation, textRectangle, padding);
        }

        private void DrawTextAnimation(Graphics g, TextAnimation textAnimation, Rectangle textRectangle, int padding)
        {
            using (Brush backgroundBrush = new SolidBrush(Color.FromArgb((int)(textAnimation.Opacity * 200), textBackgroundColor)))
            using (Pen outerBorderPen = new Pen(Color.FromArgb((int)(textAnimation.Opacity * 200), textOuterBorderColor)))
            using (Pen innerBorderPen = new Pen(Color.FromArgb((int)(textAnimation.Opacity * 200), textInnerBorderColor)))
            using (Brush textBrush = new SolidBrush(Color.FromArgb((int)(textAnimation.Opacity * 255), textColor)))
            using (Brush textShadowBrush = new SolidBrush(Color.FromArgb((int)(textAnimation.Opacity * 255), textShadowColor)))
            {
                DrawInfoText(g, textAnimation.Text, textRectangle, infoFontMedium, padding, backgroundBrush, outerBorderPen, innerBorderPen, textBrush, textShadowBrush);
            }
        }

        internal string GetAreaText(Rectangle rect)
        {
            if (IsEditorMode)
            {
                rect = new Rectangle(rect.X - CanvasRectangle.X, rect.Y - CanvasRectangle.Y, rect.Width, rect.Height);
            }
            else if (Mode == RegionCaptureMode.Ruler)
            {
                Point endLocation = new Point(rect.Right - 1, rect.Bottom - 1);
                return $"{"左上".CurrentText()}: {rect.X} * {rect.Y} | {"右下".CurrentText()}: {endLocation.X} * {endLocation.Y}\n" +
                       $"{"尺寸".CurrentText()}: {rect.Width} * {rect.Height} px | {"面积".CurrentText()}: {rect.Area()} px | {"周长".CurrentText()}: {rect.Perimeter()} px\n" +
                       $"{"斜角-长度".CurrentText()}: {MathHelpers.Distance(rect.Location, endLocation):0.00} px | {"角度".CurrentText()}: {MathHelpers.LookAtDegree(rect.Location, endLocation):0.00}°";
            }

            return string.Format("{0} " + "左上".CurrentText() + ": {1} * {2} " + "尺寸".CurrentText() + ": {3} * {4}", Options.CustomInfoTitle.CurrentText(), rect.X, rect.Y, rect.Width, rect.Height).Trim();
        }

        private string GetInfoText()
        {
            if (IsEditorMode)
            {
                Point canvasRelativePosition = new Point(InputManager.ClientMousePosition.X - CanvasRectangle.X, InputManager.ClientMousePosition.Y - CanvasRectangle.Y);
                return $"{"左".CurrentText()}: {canvasRelativePosition.X} {"上".CurrentText()}: {canvasRelativePosition.Y}";
            }
            var lstDrawStr = new List<string>();

            Color color = ShapeManager.GetCurrentColor();
            lstDrawStr.Add(string.Format("RGB: {0}, {1}, {2}", color.R, color.G, color.B));
            lstDrawStr.Add(string.Format("HEX: {0}", ColorHelper.ColorToHex(color)));
            lstDrawStr.Add(string.Format("鼠标".CurrentText() + ": {0} * {1}", CurrentPosition.X, CurrentPosition.Y));
            if (ShapeManager.CurrentHoverShape != null && !ShapeManager.CurrentHoverShape.Rectangle.IsEmpty)
                lstDrawStr.Add(string.Format("尺寸".CurrentText() + ": {0} * {1}", ShapeManager.CurrentHoverShape.Rectangle.Width, ShapeManager.CurrentHoverShape.Rectangle.Height));

            if (Mode == RegionCaptureMode.ScreenColorPicker)
                lstDrawStr.Add("单击/Enter选择颜色".CurrentText());
            else if (Options.DetectWindows)
                lstDrawStr.Add("按Ctrl键切换控件模式".CurrentText());
            lstDrawStr.Add("鼠标右键/Esc退出".CurrentText());

            if (!string.IsNullOrEmpty(Options.CustomInfoText))
            {
                lstDrawStr.Add(Options.CustomInfoText);
            }
            return string.Join("\n", lstDrawStr.ToArray()).Trim();
        }

        private void DrawCrosshair(Graphics g)
        {
            int offset = 5;
            Point mousePos = InputManager.ClientMousePosition;
            Point left = new Point(mousePos.X - offset, mousePos.Y), left2 = new Point(0, mousePos.Y);
            Point right = new Point(mousePos.X + offset, mousePos.Y), right2 = new Point(ClientArea.Width - 1, mousePos.Y);
            Point top = new Point(mousePos.X, mousePos.Y - offset), top2 = new Point(mousePos.X, 0);
            Point bottom = new Point(mousePos.X, mousePos.Y + offset), bottom2 = new Point(mousePos.X, ClientArea.Height - 1);

            if (left.X - left2.X > 10)
            {
                g.DrawLine(borderPen, left, left2);
                g.DrawLine(borderDotPen, left, left2);
            }

            if (right2.X - right.X > 10)
            {
                g.DrawLine(borderPen, right, right2);
                g.DrawLine(borderDotPen, right, right2);
            }

            if (top.Y - top2.Y > 10)
            {
                g.DrawLine(borderPen, top, top2);
                g.DrawLine(borderDotPen, top, top2);
            }

            if (bottom2.Y - bottom.Y > 10)
            {
                g.DrawLine(borderPen, bottom, bottom2);
                g.DrawLine(borderDotPen, bottom, bottom2);
            }
        }

        private void DrawCursorGraphics(Graphics g)
        {
            Point mousePos = InputManager.ClientMousePosition;
            Rectangle currentScreenRect0Based = NativeMethods.GetActiveScreenBounds0Based();
            int cursorOffsetX = 10, cursorOffsetY = 10, itemGap = 10, itemCount = 0;
            Size totalSize = Size.Empty;

            int magnifierPosition = 0;
            Bitmap magnifier = null;

            if (Options.ShowMagnifier)
            {
                magnifierPosition = totalSize.Height;

                magnifier = Magnifier(Canvas, mousePos, Options.MagnifierPixelCount, Options.MagnifierPixelCount, Options.MagnifierPixelSize, Options.DrawMagnifierCenterLines);
                totalSize.Width = Math.Max(totalSize.Width, magnifier.Width);

                totalSize.Height += magnifier.Height;
                itemCount++;
            }

            int infoTextPadding = 3;
            int infoTextPosition = 0;
            Rectangle infoTextRect = Rectangle.Empty;
            string infoText = "";

            if (Options.ShowInfo)
            {
                if (itemCount > 0) totalSize.Height += itemGap;
                infoTextPosition = totalSize.Height;

                CurrentPosition = InputManager.MousePosition;
                infoText = GetInfoText();
                Size textSize = g.MeasureString(infoText, infoFont).ToSize();
                infoTextRect.Size = new Size(textSize.Width + (infoTextPadding * 2), textSize.Height + (infoTextPadding * 2));
                totalSize.Width = Math.Max(totalSize.Width, infoTextRect.Width);

                totalSize.Height += infoTextRect.Height;
                //itemCount++;
            }

            int x = mousePos.X + cursorOffsetX;

            if (x + totalSize.Width > currentScreenRect0Based.Right)
            {
                x = mousePos.X - cursorOffsetX - totalSize.Width;
            }

            int y = mousePos.Y + cursorOffsetY;

            if (y + totalSize.Height > currentScreenRect0Based.Bottom)
            {
                y = mousePos.Y - cursorOffsetY - totalSize.Height;
            }

            if (Options.ShowMagnifier && magnifier != null)
            {
                using (GraphicsQualityManager quality = new GraphicsQualityManager(g))
                using (TextureBrush brush = new TextureBrush(magnifier))
                {
                    brush.TranslateTransform(x, y + magnifierPosition);

                    if (Options.UseSquareMagnifier)
                    {
                        g.FillRectangle(brush, x, y + magnifierPosition, magnifier.Width, magnifier.Height);
                        g.DrawRectangleProper(Pens.White, x - 1, y + magnifierPosition - 1, magnifier.Width + 2, magnifier.Height + 2);
                        g.DrawRectangleProper(Pens.Black, x, y + magnifierPosition, magnifier.Width, magnifier.Height);
                    }
                    else
                    {
                        g.FillEllipse(brush, x, y + magnifierPosition, magnifier.Width, magnifier.Height);
                        g.DrawEllipse(Pens.White, x - 1, y + magnifierPosition - 1, magnifier.Width + 2 - 1, magnifier.Height + 2 - 1);
                        g.DrawEllipse(Pens.Black, x, y + magnifierPosition, magnifier.Width - 1, magnifier.Height - 1);
                    }
                }
            }

            if (Options.ShowInfo)
            {
                if (Mode == RegionCaptureMode.ScreenColorPicker)
                {
                    int colorBoxOffset = 2;
                    int colorBoxSize = infoTextRect.Height - (colorBoxOffset * 2);
                    int textOffset = 4;
                    int colorBoxExtraWidth = colorBoxSize + textOffset;
                    infoTextRect.Width += colorBoxExtraWidth;
                    infoTextRect.Location = new Point(x + (totalSize.Width / 2) - (infoTextRect.Width / 2), y + infoTextPosition);
                    Point padding = new Point(infoTextPadding + colorBoxExtraWidth, infoTextPadding);

                    Rectangle colorRect = new Rectangle(infoTextRect.X + colorBoxOffset, infoTextRect.Y + colorBoxOffset, colorBoxSize, colorBoxSize);

                    DrawInfoText(g, infoText, infoTextRect, infoFont, padding);

                    using (Brush colorBrush = new SolidBrush(ShapeManager.GetCurrentColor()))
                    {
                        g.FillRectangle(colorBrush, colorRect);
                    }

                    g.DrawLine(textInnerBorderPen, colorRect.Right, colorRect.Top, colorRect.Right, colorRect.Bottom - 1);
                }
                else
                {
                    infoTextRect.Location = new Point(x + (totalSize.Width / 2) - (infoTextRect.Width / 2), y + infoTextPosition);
                    Point padding = new Point(infoTextPadding, infoTextPadding);

                    DrawInfoText(g, infoText, infoTextRect, infoFont, padding);
                }
            }
        }

        private Bitmap Magnifier(Image img, Point position, int horizontalPixelCount, int verticalPixelCount, int pixelSize, bool drawCenterLines = true)
        {
            horizontalPixelCount = (horizontalPixelCount | 1).Between(1, 101);
            verticalPixelCount = (verticalPixelCount | 1).Between(1, 101);
            pixelSize = pixelSize.Between(1, 1000);
            if (horizontalPixelCount * pixelSize > ClientArea.Width ||
                verticalPixelCount * pixelSize > ClientArea.Height)
            {
                horizontalPixelCount = verticalPixelCount = 15;
                pixelSize = 10;
            }

            Rectangle srcRect = new Rectangle(position.X - (horizontalPixelCount / 2) - CanvasRectangle.X,
                position.Y - (verticalPixelCount / 2) - CanvasRectangle.Y, horizontalPixelCount, verticalPixelCount);

            var width = horizontalPixelCount * pixelSize;
            var height = verticalPixelCount * pixelSize;
            var bitmap = new Bitmap(width - 1, height - 1);
            using (var graphics = Graphics.FromImage(bitmap))
            {
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;

                if (!new Rectangle(0, 0, img.Width, img.Height).Contains(srcRect))
                {
                    graphics.FillRectangle(backBrush, ClientRectangle);
                }

                graphics.PixelOffsetMode = PixelOffsetMode.Half;
                graphics.DrawImage(img, new Rectangle(0, 0, width, height), srcRect, GraphicsUnit.Pixel);
                graphics.PixelOffsetMode = PixelOffsetMode.None;

                if (drawCenterLines)
                {
                    using (var brush = new SolidBrush(Color.FromArgb(100, 26, 173, 25)))
                    {
                        graphics.FillRectangle(brush, new Rectangle(0, (height - pixelSize) / 2, (width - pixelSize) / 2, pixelSize)); // Left
                        graphics.FillRectangle(brush, new Rectangle((width + pixelSize) / 2, (height - pixelSize) / 2, (width - pixelSize) / 2, pixelSize)); // Right
                        graphics.FillRectangle(brush, new Rectangle((width - pixelSize) / 2, 0, pixelSize, (height - pixelSize) / 2)); // Top
                        graphics.FillRectangle(brush, new Rectangle((width - pixelSize) / 2, (height + pixelSize) / 2, pixelSize, (height - pixelSize) / 2)); // Bottom
                    }

                    graphics.DrawRectangle(Pens.Black, (width - pixelSize) / 2 - 1, (height - pixelSize) / 2 - 1, pixelSize,
                        pixelSize);
                    if (pixelSize >= 6)
                        graphics.DrawRectangle(Pens.White, (width - pixelSize) / 2, (height - pixelSize) / 2, pixelSize - 2,
                            pixelSize - 2);
                }
            }

            return bitmap;
        }

        private void DrawRuler(Graphics g, Rectangle rect, Pen pen, int rulerSize, int rulerWidth)
        {
            if (rect.Width >= rulerSize && rect.Height >= rulerSize)
            {
                for (int x = 1; x <= rect.Width / rulerWidth; x++)
                {
                    g.DrawLine(pen, new Point(rect.X + (x * rulerWidth), rect.Y), new Point(rect.X + (x * rulerWidth), rect.Y + rulerSize));
                    g.DrawLine(pen, new Point(rect.X + (x * rulerWidth), rect.Bottom), new Point(rect.X + (x * rulerWidth), rect.Bottom - rulerSize));
                }

                for (int y = 1; y <= rect.Height / rulerWidth; y++)
                {
                    g.DrawLine(pen, new Point(rect.X, rect.Y + (y * rulerWidth)), new Point(rect.X + rulerSize, rect.Y + (y * rulerWidth)));
                    g.DrawLine(pen, new Point(rect.Right, rect.Y + (y * rulerWidth)), new Point(rect.Right - rulerSize, rect.Y + (y * rulerWidth)));
                }
            }
        }

        internal void UpdateRegionPath()
        {
            if (regionFillPath != null)
            {
                regionFillPath.Dispose();
                regionFillPath = null;
            }

            if (regionDrawPath != null)
            {
                regionDrawPath.Dispose();
                regionDrawPath = null;
            }

            BaseShape[] areas = ShapeManager.ValidRegions;

            if (areas != null && areas.Length > 0)
            {
                regionFillPath = new GraphicsPath { FillMode = FillMode.Winding };
                regionDrawPath = new GraphicsPath { FillMode = FillMode.Winding };

                foreach (BaseShape regionShape in ShapeManager.ValidRegions)
                {
                    regionShape.AddShapePath(regionFillPath);
                    regionShape.AddShapePath(regionDrawPath, -1);
                }
            }
        }

        public static Bitmap ApplyRegionPathToImage(Bitmap bmp, GraphicsPath gp, out Rectangle resultArea)
        {
            if (bmp != null && gp != null)
            {
                Rectangle regionArea = Rectangle.Round(gp.GetBounds());
                Rectangle screenRectangle = NativeMethods.GetScreenBounds0Based();
                resultArea = Rectangle.Intersect(regionArea, screenRectangle);

                if (resultArea.IsValid())
                {
                    using (Bitmap bmpResult = bmp.CreateEmptyBitmap())
                    using (Graphics g = Graphics.FromImage(bmpResult))
                    using (TextureBrush brush = new TextureBrush(bmp))
                    {
                        g.PixelOffsetMode = PixelOffsetMode.Half;
                        g.SmoothingMode = SmoothingMode.HighQuality;

                        g.FillPath(brush, gp);

                        return ImageProcessHelper.CropBitmap(bmpResult, resultArea);
                    }
                }
            }

            resultArea = Rectangle.Empty;
            return null;
        }

        public Bitmap GetResultImage()
        {
            if (IsEditorMode)
            {
                return ShapeManager.RenderOutputImage(Canvas, CanvasRectangle.Location);
            }

            if (Result == RegionResult.Region)
            {
                if (regionFillPath != null)
                {
                    using (Bitmap bmp = ApplyRegionPathToImage(Canvas, regionFillPath, out Rectangle rect))
                    {
                        return ShapeManager.RenderOutputImage(bmp, rect.Location);
                    }
                }
            }
            else if (Result == RegionResult.Fullscreen)
            {
                return ShapeManager.RenderOutputImage(Canvas);
            }
            else if (Result == RegionResult.Monitor)
            {
                var screens = Screen.AllScreens;

                if (MonitorIndex < screens.Length)
                {
                    Screen screen = screens[MonitorIndex];
                    Rectangle screenRect = NativeMethods.ScreenToClient(screen.Bounds);

                    using (Bitmap bmp = ShapeManager.RenderOutputImage(Canvas))
                    {
                        return ImageProcessHelper.CropBitmap(bmp, screenRect);
                    }
                }
            }
            else if (Result == RegionResult.ActiveMonitor)
            {
                Rectangle activeScreenRect = NativeMethods.GetActiveScreenBounds0Based();

                using (Bitmap bmp = ShapeManager.RenderOutputImage(Canvas))
                {
                    return ImageProcessHelper.CropBitmap(bmp, activeScreenRect);
                }
            }

            return null;
        }

        internal void OnSaveImageAsRequested()
        {
            Result = RegionResult.Region;
            using (var bmp = GetResultImage())
            {
                if (bmp != null)
                    if (bmp.SaveFile(this))
                    {
                        ShapeManager.ShowMenuTooltip("图片已保存".CurrentText());
                        CloseWindow();
                    }
            }
        }

        internal void OnCopyImageRequested()
        {
            Result = RegionResult.Region;
            using (var bmp = GetResultImage())
            {
                if (bmp != null)
                {
                    ClipboardService.ClipSetImage(bmp, false);
                    ShapeManager.ShowMenuTooltip("图片已复制".CurrentText());
                    CloseWindow();
                }
            }

        }

        protected override void Dispose(bool disposing)
        {
            IsClosing = true;

            ShapeManager?.Dispose();
            backgroundBrush?.Dispose();
            backgroundHighlightBrush?.Dispose();
            borderPen?.Dispose();
            borderDotPen?.Dispose();
            borderDotStaticPen?.Dispose();
            infoFont?.Dispose();
            infoFontMedium?.Dispose();
            infoFontBig?.Dispose();
            textBrush?.Dispose();
            textShadowBrush?.Dispose();
            textBackgroundBrush?.Dispose();
            textOuterBorderPen?.Dispose();
            textInnerBorderPen?.Dispose();
            markerPen?.Dispose();
            canvasBorderPen?.Dispose();
            CustomNodeImage?.Dispose();

            if (regionFillPath != null)
            {
                if (Result != RegionResult.Region)
                {
                    regionFillPath.Dispose();
                }
            }

            regionDrawPath?.Dispose();
            DimmedCanvas?.Dispose();
            Canvas?.Dispose();

            base.Dispose(disposing);
        }
    }
}