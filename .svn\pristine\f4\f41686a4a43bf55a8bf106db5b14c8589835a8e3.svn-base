﻿/**
 * MetroFramework - Modern UI for WinForms
 * 
 * The MIT License (MIT)
 * Copyright (c) 2011 <PERSON>, http://github.com/viperneo
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of 
 * this software and associated documentation files (the "Software"), to deal in the 
 * Software without restriction, including without limitation the rights to use, copy, 
 * modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, 
 * and to permit persons to whom the Software is furnished to do so, subject to the 
 * following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in 
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
 * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT 
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF 
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE 
 * OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

using MetroFramework.Components;
using MetroFramework.Design;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace MetroFramework.Controls
{
    [ToolboxBitmap(typeof(ProgressBar))]
    [Designer(typeof(MetroProgressSpinnerDesigner), typeof(ParentControlDesigner))]
    public class MetroProgressSpinner : Control, IMetroControl
    {
        private readonly Timer timer;
        private float angle = 270f;

        private bool backwards;

        private bool ensureVisible = true;

        private int maximum = 100;
        private MetroColorStyle metroStyle;

        private MetroThemeStyle metroTheme;

        private int minimum;

        private int progress;

        private float speed;

        public MetroProgressSpinner()
        {
            timer = new Timer
            {
                Interval = 20
            };
            timer.Tick += timer_Tick;
            timer.Enabled = true;
            Width = 16;
            Height = 16;
            speed = 1f;
            DoubleBuffered = true;
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomBackColor { get; set; }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomForeColor { get; set; }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseStyleColors { get; set; }

        [Category("Metro Behaviour")]
        [DefaultValue(false)]
        [Browsable(false)]
        public bool UseSelectable
        {
            get => GetStyle(ControlStyles.Selectable);
            set => SetStyle(ControlStyles.Selectable, value);
        }

        [Category("Metro Behaviour")]
        [DefaultValue(true)]
        public bool Spinning
        {
            get => timer.Enabled;
            set => timer.Enabled = value;
        }

        [Category("Metro Appearance")]
        [DefaultValue(0)]
        public int Value
        {
            get => progress;
            set
            {
                if (value != -1 && (value < minimum || value > maximum))
                    throw new ArgumentOutOfRangeException("Progress value must be -1 or between Minimum and Maximum.",
                        (Exception) null);
                progress = value;
                Refresh();
            }
        }

        [DefaultValue(0)]
        [Category("Metro Appearance")]
        public int Minimum
        {
            get => minimum;
            set
            {
                if (value < 0) throw new ArgumentOutOfRangeException("Minimum value must be >= 0.", (Exception) null);
                if (value >= maximum)
                    throw new ArgumentOutOfRangeException("Minimum value must be < Maximum.", (Exception) null);
                minimum = value;
                if (progress != -1 && progress < minimum) progress = minimum;
                Refresh();
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(0)]
        public int Maximum
        {
            get => maximum;
            set
            {
                if (value <= minimum)
                    throw new ArgumentOutOfRangeException("Maximum value must be > Minimum.", (Exception) null);
                maximum = value;
                if (progress > maximum) progress = maximum;
                Refresh();
            }
        }

        [DefaultValue(true)]
        [Category("Metro Appearance")]
        public bool EnsureVisible
        {
            get => ensureVisible;
            set
            {
                ensureVisible = value;
                Refresh();
            }
        }

        [DefaultValue(1f)]
        [Category("Metro Behaviour")]
        public float Speed
        {
            get => speed;
            set
            {
                if (value <= 0f || value > 10f)
                    throw new ArgumentOutOfRangeException("Speed value must be > 0 and <= 10.", (Exception) null);
                speed = value;
            }
        }

        [DefaultValue(false)]
        [Category("Metro Behaviour")]
        public bool Backwards
        {
            get => backwards;
            set
            {
                backwards = value;
                Refresh();
            }
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool CustomBackground { get; set; }

        [Category("Metro Appearance")]
        [DefaultValue(MetroColorStyle.Blue)]
        public MetroColorStyle Style
        {
            get
            {
                if (DesignMode || metroStyle != 0) return metroStyle;
                if (StyleManager != null) return StyleManager.Style;
                if (StyleManager == null) return MetroColorStyle.Blue;
                return metroStyle;
            }
            set => metroStyle = value;
        }

        [DefaultValue(MetroThemeStyle.Light)]
        [Category("Metro Appearance")]
        public MetroThemeStyle Theme
        {
            get
            {
                if (DesignMode || metroTheme != 0) return metroTheme;
                if (StyleManager != null) return StyleManager.Theme;
                if (StyleManager == null) return MetroThemeStyle.Light;
                return metroTheme;
            }
            set => metroTheme = value;
        }

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public MetroStyleManager StyleManager { get; set; }

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintBackground != null) CustomPaintBackground(this, e);
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaint != null) CustomPaint(this, e);
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintForeground != null) CustomPaintForeground(this, e);
        }

        public void Reset()
        {
            progress = minimum;
            angle = 270f;
            Refresh();
        }

        private void timer_Tick(object sender, EventArgs e)
        {
            if (!DesignMode)
            {
                angle += 6f * speed * (!backwards ? 1 : -1);
                Refresh();
            }
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            try
            {
                var color = BackColor;
                if (!UseCustomBackColor)
                    color = !(Parent is MetroTile) ? MetroPaint.BackColor.Form(Theme) : MetroPaint.GetStyleColor(Style);
                if (color.A == byte.MaxValue)
                {
                    e.Graphics.Clear(color);
                }
                else
                {
                    base.OnPaintBackground(e);
                    OnCustomPaintBackground(new MetroPaintEventArgs(color, Color.Empty, e.Graphics));
                }
            }
            catch
            {
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            try
            {
                if (GetStyle(ControlStyles.AllPaintingInWmPaint)) OnPaintBackground(e);
                OnCustomPaint(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
                OnPaintForeground(e);
            }
            catch
            {
                Invalidate();
            }
        }

        protected virtual void OnPaintForeground(PaintEventArgs e)
        {
            var color = CustomBackground ? MetroPaint.GetStyleColor(Style) :
                !(Parent is MetroTile) ? MetroPaint.GetStyleColor(Style) : MetroPaint.ForeColor.Tile.Normal(Theme);
            using (var pen = new Pen(color, Width / 5f))
            {
                var num = (int) Math.Ceiling(Width / 10f);
                e.Graphics.SmoothingMode = SmoothingMode.HighQuality;
                if (progress != -1)
                {
                    var num2 = (float) (progress - minimum) / (maximum - minimum);
                    var num3 = !ensureVisible ? 360f * num2 : 30f + 300f * num2;
                    if (backwards) num3 = 0f - num3;
                    e.Graphics.DrawArc(pen, num, num, Width - 2 * num - 1, Height - 2 * num - 1, angle, num3);
                }
                else
                {
                    for (var i = 0; i <= 180; i += 15)
                    {
                        var num4 = 290 - i * 290 / 180;
                        if (num4 > 255) num4 = 255;
                        if (num4 < 0) num4 = 0;
                        var color2 = Color.FromArgb(num4, pen.Color);
                        using (var pen2 = new Pen(color2, pen.Width))
                        {
                            var startAngle = angle + (i - (ensureVisible ? 30 : 0)) * (backwards ? 1 : -1);
                            float sweepAngle = 15 * (backwards ? 1 : -1);
                            e.Graphics.DrawArc(pen2, num, num, Width - 2 * num - 1, Height - 2 * num - 1, startAngle,
                                sweepAngle);
                        }
                    }
                }
            }

            OnCustomPaintForeground(new MetroPaintEventArgs(Color.Empty, color, e.Graphics));
        }
    }
}