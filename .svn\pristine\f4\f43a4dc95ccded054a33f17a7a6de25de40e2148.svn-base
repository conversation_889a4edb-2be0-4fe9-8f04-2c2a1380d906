﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;

namespace OCRTools
{
    public enum ColorType
    {
        None,
        RGBA,
        HSB,
        CMYK,
        Hex
    }

    public enum ColorFormat
    {
        RGB,
        RGBA,
        ARGB
    }

    public class ColorHelper
    {
        public static Color[] StandardColors =
        {
            Color.FromArgb(0, 0, 0),
            Color.FromArgb(64, 64, 64),
            Color.FromArgb(255, 0, 0),
            Color.FromArgb(255, 106, 0),
            Color.FromArgb(255, 216, 0),
            Color.FromArgb(182, 255, 0),
            Color.FromArgb(76, 255, 0),
            Color.FromArgb(0, 255, 33),
            Color.FromArgb(0, 255, 144),
            Color.FromArgb(0, 255, 255),
            Color.FromArgb(0, 148, 255),
            Color.FromArgb(0, 38, 255),
            Color.FromArgb(72, 0, 255),
            Color.FromArgb(178, 0, 255),
            Color.FromArgb(255, 0, 220),
            Color.FromArgb(255, 0, 110),
            Color.FromArgb(255, 255, 255),
            Color.FromArgb(128, 128, 128),
            Color.FromArgb(127, 0, 0),
            Color.FromArgb(127, 51, 0),
            Color.FromArgb(127, 106, 0),
            Color.FromArgb(91, 127, 0),
            Color.FromArgb(38, 127, 0),
            Color.FromArgb(0, 127, 14),
            Color.FromArgb(0, 127, 70),
            Color.FromArgb(0, 127, 127),
            Color.FromArgb(0, 74, 127),
            Color.FromArgb(0, 19, 127),
            Color.FromArgb(33, 0, 127),
            Color.FromArgb(87, 0, 127),
            Color.FromArgb(127, 0, 110),
            Color.FromArgb(127, 0, 55)
        };

        #region Convert HSB to ...

        public static Color HSBToColor(HSB hsb)
        {
            int mid;
            var max = (int)Math.Round(hsb.Brightness * 255);
            var min = (int)Math.Round((1.0 - hsb.Saturation) * (hsb.Brightness / 1.0) * 255);
            var q = (double)(max - min) / 255;

            if (hsb.Hue >= 0 && hsb.Hue <= (double)1 / 6)
            {
                mid = (int)Math.Round((hsb.Hue - 0) * q * 1530 + min);
                return Color.FromArgb(hsb.Alpha, max, mid, min);
            }

            if (hsb.Hue <= (double)1 / 3)
            {
                mid = (int)Math.Round(-((hsb.Hue - (double)1 / 6) * q) * 1530 + max);
                return Color.FromArgb(hsb.Alpha, mid, max, min);
            }

            if (hsb.Hue <= 0.5)
            {
                mid = (int)Math.Round((hsb.Hue - (double)1 / 3) * q * 1530 + min);
                return Color.FromArgb(hsb.Alpha, min, max, mid);
            }

            if (hsb.Hue <= (double)2 / 3)
            {
                mid = (int)Math.Round(-((hsb.Hue - 0.5) * q) * 1530 + max);
                return Color.FromArgb(hsb.Alpha, min, mid, max);
            }

            if (hsb.Hue <= (double)5 / 6)
            {
                mid = (int)Math.Round((hsb.Hue - (double)2 / 3) * q * 1530 + min);
                return Color.FromArgb(hsb.Alpha, mid, min, max);
            }

            if (hsb.Hue <= 1.0)
            {
                mid = (int)Math.Round(-((hsb.Hue - (double)5 / 6) * q) * 1530 + max);
                return Color.FromArgb(hsb.Alpha, max, min, mid);
            }

            return Color.FromArgb(hsb.Alpha, 0, 0, 0);
        }

        #endregion Convert HSB to ...

        #region Convert CMYK to ...

        public static Color CmykToColor(Cmyk cmyk)
        {
            if (cmyk.Cyan == 0 && cmyk.Magenta == 0 && cmyk.Yellow == 0 && cmyk.Key == 1)
                return Color.FromArgb(cmyk.Alpha, 0, 0, 0);

            var c = cmyk.Cyan * (1 - cmyk.Key) + cmyk.Key;
            var m = cmyk.Magenta * (1 - cmyk.Key) + cmyk.Key;
            var y = cmyk.Yellow * (1 - cmyk.Key) + cmyk.Key;

            var r = (int)Math.Round((1 - c) * 255);
            var g = (int)Math.Round((1 - m) * 255);
            var b = (int)Math.Round((1 - y) * 255);

            return Color.FromArgb(cmyk.Alpha, r, g, b);
        }

        public static CMYK ColorToCMYK(Color color)
        {
            if (color.R == 0 && color.G == 0 && color.B == 0)
            {
                return new CMYK(0, 0, 0, 1, color.A);
            }

            double c = 1 - (color.R / 255d);
            double m = 1 - (color.G / 255d);
            double y = 1 - (color.B / 255d);
            double k = Math.Min(c, Math.Min(m, y));

            c = (c - k) / (1 - k);
            m = (m - k) / (1 - k);
            y = (y - k) / (1 - k);

            return new CMYK(c, m, y, k, color.A);
        }

        #endregion Convert CMYK to ...

        public static Color CMYKToColor(CMYK cmyk)
        {
            if (cmyk.Cyan == 0 && cmyk.Magenta == 0 && cmyk.Yellow == 0 && cmyk.Key == 1)
            {
                return Color.FromArgb(cmyk.Alpha, 0, 0, 0);
            }

            double c = (cmyk.Cyan * (1 - cmyk.Key)) + cmyk.Key;
            double m = (cmyk.Magenta * (1 - cmyk.Key)) + cmyk.Key;
            double y = (cmyk.Yellow * (1 - cmyk.Key)) + cmyk.Key;

            int r = (int)Math.Round((1 - c) * 255);
            int g = (int)Math.Round((1 - m) * 255);
            int b = (int)Math.Round((1 - y) * 255);

            return Color.FromArgb(cmyk.Alpha, r, g, b);
        }

        public static double ValidColor(double number)
        {
            return number.Clamp(0, 1);
        }

        public static int ValidColor(int number)
        {
            return number.Clamp(0, 255);
        }

        public static int ColorDifference(Color color1, Color color2)
        {
            var rDiff = Math.Abs(color1.R - color2.R);
            var gDiff = Math.Abs(color1.G - color2.G);
            var bDiff = Math.Abs(color1.B - color2.B);
            return rDiff + gDiff + bDiff;
        }

        public static List<Color> GetKnownColors()
        {
            var colors = new List<Color>();

            for (var knownColor = KnownColor.AliceBlue; knownColor <= KnownColor.YellowGreen; knownColor++)
            {
                var color = Color.FromKnownColor(knownColor);
                colors.Add(color);
            }

            return colors;
        }

        public static Color FindClosestKnownColor(Color color)
        {
            var colors = GetKnownColors();
            return colors.Aggregate(Color.Black,
                (accu, curr) => ColorDifference(color, curr) < ColorDifference(color, accu) ? curr : accu);
        }

        public static string GetColorName(Color color)
        {
            var knownColor = FindClosestKnownColor(color);
            return GetProperName(knownColor.Name);
        }

        // Example: "TopLeft" becomes "Top left"
        // Example2: "Rotate180" becomes "Rotate 180"
        private static string GetProperName(string name, bool keepCase = false)
        {
            var sb = new StringBuilder();

            var number = false;

            for (var i = 0; i < name.Length; i++)
            {
                var c = name[i];

                if (i > 0 && (char.IsUpper(c) || !number && char.IsNumber(c)))
                {
                    sb.Append(' ');

                    sb.Append(keepCase ? c : char.ToLowerInvariant(c));
                }
                else
                {
                    sb.Append(c);
                }

                number = char.IsNumber(c);
            }

            return sb.ToString();
        }

        #region Convert Color to ...

        public static string ColorToHex(Color color, ColorFormat format = ColorFormat.RGB)
        {
            switch (format)
            {
                default:
                    return string.Format("{0:X2}{1:X2}{2:X2}", color.R, color.G, color.B);
                case ColorFormat.RGBA:
                    return string.Format("{0:X2}{1:X2}{2:X2}{3:X2}", color.R, color.G, color.B, color.A);
                case ColorFormat.ARGB:
                    return string.Format("{0:X2}{1:X2}{2:X2}{3:X2}", color.A, color.R, color.G, color.B);
            }
        }

        public static Color VisibleColor(Color color, Color lightColor, Color darkColor)
        {
            if (IsLightColor(color))
            {
                return darkColor;
            }

            return lightColor;
        }

        public static bool IsLightColor(Color color)
        {
            return PerceivedBrightness(color) > 130;
        }

        public static int PerceivedBrightness(Color color)
        {
            return (int)Math.Sqrt((color.R * color.R * .299) + (color.G * color.G * .587) + (color.B * color.B * .114));
        }

        public static HSB ColorToHsb(Color color)
        {
            var hsb = new HSB();

            int max, min;

            if (color.R > color.G)
            {
                max = color.R;
                min = color.G;
            }
            else
            {
                max = color.G;
                min = color.R;
            }

            if (color.B > max) max = color.B;
            else if (color.B < min) min = color.B;

            var diff = max - min;

            hsb.Brightness = (double)max / 255;

            if (max == 0) hsb.Saturation = 0;
            else hsb.Saturation = (double)diff / max;

            double q;
            if (diff == 0) q = 0;
            else q = (double)60 / diff;

            if (max == color.R)
            {
                if (color.G < color.B) hsb.Hue = (360 + q * (color.G - color.B)) / 360;
                else hsb.Hue = q * (color.G - color.B) / 360;
            }
            else if (max == color.G)
            {
                hsb.Hue = (120 + q * (color.B - color.R)) / 360;
            }
            else if (max == color.B)
            {
                hsb.Hue = (240 + q * (color.R - color.G)) / 360;
            }
            else
            {
                hsb.Hue = 0.0;
            }

            hsb.Alpha = color.A;

            return hsb;
        }

        public static Cmyk ColorToCmyk(Color color)
        {
            if (color.R == 0 && color.G == 0 && color.B == 0) return new Cmyk(0, 0, 0, 1, color.A);

            var c = 1 - color.R / 255d;
            var m = 1 - color.G / 255d;
            var y = 1 - color.B / 255d;
            var k = Math.Min(c, Math.Min(m, y));

            c = (c - k) / (1 - k);
            m = (m - k) / (1 - k);
            y = (y - k) / (1 - k);

            return new Cmyk(c, m, y, k, color.A);
        }

        #endregion Convert Color to ...

        #region Convert Hex to ...

        public static Color HexToColor(string hex, ColorFormat format = ColorFormat.RGB)
        {
            if (string.IsNullOrEmpty(hex)) return Color.Empty;

            if (hex[0] == '#')
                hex = hex.Remove(0, 1);
            else if (hex.StartsWith("0x", StringComparison.InvariantCultureIgnoreCase)) hex = hex.Remove(0, 2);

            if ((format == ColorFormat.RGBA || format == ColorFormat.ARGB) && hex.Length != 8 ||
                format == ColorFormat.RGB && hex.Length != 6)
                return Color.Empty;

            int r, g, b, a;

            switch (format)
            {
                default:
                    r = HexToDecimal(hex.Substring(0, 2));
                    g = HexToDecimal(hex.Substring(2, 2));
                    b = HexToDecimal(hex.Substring(4, 2));
                    a = 255;
                    break;
                case ColorFormat.RGBA:
                    r = HexToDecimal(hex.Substring(0, 2));
                    g = HexToDecimal(hex.Substring(2, 2));
                    b = HexToDecimal(hex.Substring(4, 2));
                    a = HexToDecimal(hex.Substring(6, 2));
                    break;
                case ColorFormat.ARGB:
                    a = HexToDecimal(hex.Substring(0, 2));
                    r = HexToDecimal(hex.Substring(2, 2));
                    g = HexToDecimal(hex.Substring(4, 2));
                    b = HexToDecimal(hex.Substring(6, 2));
                    break;
            }

            return Color.FromArgb(a, r, g, b);
        }

        public static int HexToDecimal(string hex)
        {
            return Convert.ToInt32(hex, 16);
        }

        #endregion Convert Hex to ...
    }
    public struct CMYK
    {
        private double cyan;
        private double magenta;
        private double yellow;
        private double key;
        private int alpha;

        public double Cyan
        {
            get
            {
                return cyan;
            }
            set
            {
                cyan = ColorHelper.ValidColor(value);
            }
        }

        public double Cyan100
        {
            get
            {
                return cyan * 100;
            }
            set
            {
                cyan = ColorHelper.ValidColor(value / 100);
            }
        }

        public double Magenta
        {
            get
            {
                return magenta;
            }
            set
            {
                magenta = ColorHelper.ValidColor(value);
            }
        }

        public double Magenta100
        {
            get
            {
                return magenta * 100;
            }
            set
            {
                magenta = ColorHelper.ValidColor(value / 100);
            }
        }

        public double Yellow
        {
            get
            {
                return yellow;
            }
            set
            {
                yellow = ColorHelper.ValidColor(value);
            }
        }

        public double Yellow100
        {
            get
            {
                return yellow * 100;
            }
            set
            {
                yellow = ColorHelper.ValidColor(value / 100);
            }
        }

        public double Key
        {
            get
            {
                return key;
            }
            set
            {
                key = ColorHelper.ValidColor(value);
            }
        }

        public double Key100
        {
            get
            {
                return key * 100;
            }
            set
            {
                key = ColorHelper.ValidColor(value / 100);
            }
        }

        public int Alpha
        {
            get
            {
                return alpha;
            }
            set
            {
                alpha = ColorHelper.ValidColor(value);
            }
        }

        public CMYK(double cyan, double magenta, double yellow, double key, int alpha = 255) : this()
        {
            Cyan = cyan;
            Magenta = magenta;
            Yellow = yellow;
            Key = key;
            Alpha = alpha;
        }

        public CMYK(int cyan, int magenta, int yellow, int key, int alpha = 255) : this()
        {
            Cyan100 = cyan;
            Magenta100 = magenta;
            Yellow100 = yellow;
            Key100 = key;
            Alpha = alpha;
        }

        public CMYK(Color color)
        {
            this = ColorHelper.ColorToCMYK(color);
        }

        public static implicit operator CMYK(Color color)
        {
            return ColorHelper.ColorToCMYK(color);
        }

        public static implicit operator Color(CMYK color)
        {
            return color.ToColor();
        }

        public static implicit operator RGBA(CMYK color)
        {
            return color.ToColor();
        }

        public static implicit operator HSB(CMYK color)
        {
            return color.ToColor();
        }

        public static bool operator ==(CMYK left, CMYK right)
        {
            return (left.Cyan == right.Cyan) && (left.Magenta == right.Magenta) && (left.Yellow == right.Yellow) && (left.Key == right.Key);
        }

        public static bool operator !=(CMYK left, CMYK right)
        {
            return !(left == right);
        }

        public Color ToColor()
        {
            return ColorHelper.CMYKToColor(this);
        }
    }
}