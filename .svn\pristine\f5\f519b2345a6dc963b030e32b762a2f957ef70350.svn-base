using System;

namespace OCRTools
{
    public static class MathHelpers
    {
        public static float Lerp(float value1, float value2, float amount)
        {
            return value1 + (value2 - value1) * amount;
        }

        public static T Clamp<T>(T num, T min, T max) where T : IComparable<T>
        {
            if (num.CompareTo(min) <= 0) return min;
            if (num.CompareTo(max) >= 0) return max;
            return num;
        }

        public static float DegreeToRadian(float degree)
        {
            return degree * 0.01745329f;
        }

        public static Vector RadianToVector2(float radian)
        {
            return new Vector((float) Math.Cos(radian), (float) Math.Sin(radian));
        }

        public static Vector RadianToVector2(float radian, float length)
        {
            return RadianToVector2(radian) * length;
        }

        public static float LookAtRadian(Vector pos1, Vector pos2)
        {
            return (float) Math.Atan2(pos2.Y - pos1.Y, pos2.X - pos1.X);
        }

        public static float Distance(Vector pos1, Vector pos2)
        {
            return (float) Math.Sqrt(Math.Pow(pos2.X - pos1.X, 2.0) + Math.Pow(pos2.Y - pos1.Y, 2.0));
        }
    }
}