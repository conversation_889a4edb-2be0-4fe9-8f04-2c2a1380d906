// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class WindowPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = WindowPatternIdentifiers.Pattern;
        public static readonly AutomationProperty CanMaximizeProperty = WindowPatternIdentifiers.CanMaximizeProperty;
        public static readonly AutomationProperty CanMinimizeProperty = WindowPatternIdentifiers.CanMinimizeProperty;
        public static readonly AutomationProperty IsModalProperty = WindowPatternIdentifiers.IsModalProperty;
        public static readonly AutomationProperty IsTopmostProperty = WindowPatternIdentifiers.IsTopmostProperty;
        public static readonly AutomationEvent WindowClosedEvent = WindowPatternIdentifiers.WindowClosedEvent;

        public static readonly AutomationProperty WindowInteractionStateProperty =
            WindowPatternIdentifiers.WindowInteractionStateProperty;

        public static readonly AutomationEvent WindowOpenedEvent = WindowPatternIdentifiers.WindowOpenedEvent;

        public static readonly AutomationProperty WindowVisualStateProperty =
            WindowPatternIdentifiers.WindowVisualStateProperty;

        private IUIAutomationWindowPattern _pattern;


        private WindowPattern(AutomationElement el, IUIAutomationWindowPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
            _pattern = pattern;
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new WindowPattern(el, (IUIAutomationWindowPattern) pattern, cached);
        }
    }
}