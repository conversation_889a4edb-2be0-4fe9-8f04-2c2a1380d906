﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace OCRTools.UserControlEx
{
	[ToolboxItem(false)]
	public class TabStyleChromeProvider : TabStyleProvider
	{
		public TabStyleChromeProvider(TabControlExtra tabControl)
			: base(tabControl)
		{
			base.Overlap = 16;
			base.ShowTabCloser = true;
			base.CloserColorFocused = Color.Black;
			base.CloserColorFocusedActive = Color.White;
			base.CloserColorSelected = Color.Black;
			base.CloserColorSelectedActive = Color.White;
			base.CloserColorHighlighted = Color.Black;
			base.CloserColorHighlightedActive = Color.White;
			base.CloserColorUnselected = Color.Empty;
			base.CloserButtonFillColorFocused = Color.Empty;
			base.CloserButtonFillColorFocusedActive = Color.FromArgb(244, 159, 148);
			base.CloserButtonFillColorSelected = Color.Empty;
			base.CloserButtonFillColorSelectedActive = Color.FromArgb(244, 159, 148);
			base.CloserButtonFillColorHighlighted = Color.Empty;
			base.CloserButtonFillColorHighlightedActive = Color.FromArgb(244, 159, 148);
			base.CloserButtonFillColorUnselected = Color.Empty;
			base.CloserButtonOutlineColorFocused = Color.Empty;
			base.CloserButtonOutlineColorFocusedActive = Color.FromArgb(209, 106, 94);
			base.CloserButtonOutlineColorSelected = Color.Empty;
			base.CloserButtonOutlineColorSelectedActive = Color.FromArgb(209, 106, 94);
			base.CloserButtonOutlineColorHighlighted = Color.Empty;
			base.CloserButtonOutlineColorHighlightedActive = Color.FromArgb(209, 106, 94);
			base.CloserButtonOutlineColorUnselected = Color.Empty;
			base.Padding = new Point(16, 5);
		}

		public override void AddTabBorder(GraphicsPath path, Rectangle tabBounds)
		{
			int num;
			int num2;
			int num3;
			int num4;
			if (base.TabControl.Alignment <= TabAlignment.Bottom)
			{
				num = (int)Math.Floor((decimal)tabBounds.Height * 2m / 3m);
				num2 = (int)Math.Floor((decimal)tabBounds.Height * 1m / 8m);
				num3 = (int)Math.Floor((decimal)tabBounds.Height * 1m / 6m);
				num4 = (int)Math.Floor((decimal)tabBounds.Height * 1m / 4m);
			}
			else
			{
				num = (int)Math.Floor((decimal)tabBounds.Width * 2m / 3m);
				num2 = (int)Math.Floor((decimal)tabBounds.Width * 1m / 8m);
				num3 = (int)Math.Floor((decimal)tabBounds.Width * 1m / 6m);
				num4 = (int)Math.Floor((decimal)tabBounds.Width * 1m / 4m);
			}
			switch (base.TabControl.Alignment)
			{
				case TabAlignment.Top:
					path.AddCurve(new Point[4]
					{
				new Point(tabBounds.X, tabBounds.Bottom),
				new Point(tabBounds.X + num3, tabBounds.Bottom - num2),
				new Point(tabBounds.X + num - num4, tabBounds.Y + num2),
				new Point(tabBounds.X + num, tabBounds.Y)
					});
					path.AddLine(tabBounds.X + num, tabBounds.Y, tabBounds.Right - num, tabBounds.Y);
					path.AddCurve(new Point[4]
					{
				new Point(tabBounds.Right - num, tabBounds.Y),
				new Point(tabBounds.Right - num + num4, tabBounds.Y + num2),
				new Point(tabBounds.Right - num3, tabBounds.Bottom - num2),
				new Point(tabBounds.Right, tabBounds.Bottom)
					});
					break;
				case TabAlignment.Bottom:
					path.AddCurve(new Point[4]
					{
				new Point(tabBounds.Right, tabBounds.Y),
				new Point(tabBounds.Right - num3, tabBounds.Y + num2),
				new Point(tabBounds.Right - num + num4, tabBounds.Bottom - num2),
				new Point(tabBounds.Right - num, tabBounds.Bottom)
					});
					path.AddLine(tabBounds.Right - num, tabBounds.Bottom, tabBounds.X + num, tabBounds.Bottom);
					path.AddCurve(new Point[4]
					{
				new Point(tabBounds.X + num, tabBounds.Bottom),
				new Point(tabBounds.X + num - num4, tabBounds.Bottom - num2),
				new Point(tabBounds.X + num3, tabBounds.Y + num2),
				new Point(tabBounds.X, tabBounds.Y)
					});
					break;
				case TabAlignment.Left:
					path.AddCurve(new Point[4]
					{
				new Point(tabBounds.Right, tabBounds.Bottom),
				new Point(tabBounds.Right - num2, tabBounds.Bottom - num3),
				new Point(tabBounds.X + num2, tabBounds.Bottom - num + num4),
				new Point(tabBounds.X, tabBounds.Bottom - num)
					});
					path.AddLine(tabBounds.X, tabBounds.Bottom - num, tabBounds.X, tabBounds.Y + num);
					path.AddCurve(new Point[4]
					{
				new Point(tabBounds.X, tabBounds.Y + num),
				new Point(tabBounds.X + num2, tabBounds.Y + num - num4),
				new Point(tabBounds.Right - num2, tabBounds.Y + num3),
				new Point(tabBounds.Right, tabBounds.Y)
					});
					break;
				case TabAlignment.Right:
					path.AddCurve(new Point[4]
					{
				new Point(tabBounds.X, tabBounds.Y),
				new Point(tabBounds.X + num2, tabBounds.Y + num3),
				new Point(tabBounds.Right - num2, tabBounds.Y + num - num4),
				new Point(tabBounds.Right, tabBounds.Y + num)
					});
					path.AddLine(tabBounds.Right, tabBounds.Y + num, tabBounds.Right, tabBounds.Bottom - num);
					path.AddCurve(new Point[4]
					{
				new Point(tabBounds.Right, tabBounds.Bottom - num),
				new Point(tabBounds.Right - num2, tabBounds.Bottom - num + num4),
				new Point(tabBounds.X + num2, tabBounds.Bottom - num3),
				new Point(tabBounds.X, tabBounds.Bottom)
					});
					break;
			}
		}

		protected internal override GraphicsPath GetTabCloserPath(Rectangle closerButtonRect)
		{
			GraphicsPath graphicsPath = new GraphicsPath();
			graphicsPath.AddLine(closerButtonRect.X + 4, closerButtonRect.Y + 4, closerButtonRect.Right - 4, closerButtonRect.Bottom - 4);
			graphicsPath.CloseFigure();
			graphicsPath.AddLine(closerButtonRect.Right - 4, closerButtonRect.Y + 4, closerButtonRect.X + 4, closerButtonRect.Bottom - 4);
			graphicsPath.CloseFigure();
			return graphicsPath;
		}

		protected internal override GraphicsPath GetTabCloserButtonPath(Rectangle closerButtonRect)
		{
			GraphicsPath graphicsPath = new GraphicsPath();
			graphicsPath.AddEllipse(new Rectangle(closerButtonRect.X, closerButtonRect.Y, closerButtonRect.Width, closerButtonRect.Height));
			graphicsPath.CloseFigure();
			return graphicsPath;
		}
	}
}
