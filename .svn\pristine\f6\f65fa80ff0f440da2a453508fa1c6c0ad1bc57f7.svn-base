using System.Collections.Generic;

namespace OCRTools
{
    internal class CommandDeleteAll : Command
    {
        private readonly List<DrawObject> cloneList;

        public CommandDeleteAll(GraphicsList graphicsList)
        {
            cloneList = new List<DrawObject>();
            var count = graphicsList.Count;
            for (var num = count - 1; num >= 0; num--) cloneList.Add(graphicsList[num].Clone());
        }

        public override void Undo(GraphicsList list)
        {
            foreach (var clone in cloneList) list.Add(clone);
        }

        public override void Redo(GraphicsList list)
        {
            list.Clear();
        }
    }
}