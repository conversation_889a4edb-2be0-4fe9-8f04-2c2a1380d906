// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class LegacyIAccessiblePattern : BasePattern
    {
        public static readonly AutomationProperty ChildIdProperty = LegacyIAccessiblePatternIdentifiers.ChildIdProperty;
        public static readonly AutomationProperty NameProperty = LegacyIAccessiblePatternIdentifiers.NameProperty;
        public static readonly AutomationProperty ValueProperty = LegacyIAccessiblePatternIdentifiers.ValueProperty;

        public static readonly AutomationProperty DescriptionProperty =
            LegacyIAccessiblePatternIdentifiers.DescriptionProperty;

        public static readonly AutomationProperty RoleProperty = LegacyIAccessiblePatternIdentifiers.RoleProperty;
        public static readonly AutomationProperty StateProperty = LegacyIAccessiblePatternIdentifiers.StateProperty;
        public static readonly AutomationProperty HelpProperty = LegacyIAccessiblePatternIdentifiers.HelpProperty;

        public static readonly AutomationProperty KeyboardShortcutProperty =
            LegacyIAccessiblePatternIdentifiers.KeyboardShortcutProperty;

        public static readonly AutomationProperty SelectionProperty =
            LegacyIAccessiblePatternIdentifiers.SelectionProperty;

        public static readonly AutomationProperty DefaultActionProperty =
            LegacyIAccessiblePatternIdentifiers.DefaultActionProperty;

        public static readonly AutomationPattern Pattern = LegacyIAccessiblePatternIdentifiers.Pattern;

        private IUIAutomationLegacyIAccessiblePattern _pattern;

        private LegacyIAccessiblePattern(AutomationElement el, IUIAutomationLegacyIAccessiblePattern pattern,
            bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
            _pattern = pattern;
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null
                ? null
                : new LegacyIAccessiblePattern(el, (IUIAutomationLegacyIAccessiblePattern) pattern, cached);
        }
    }
}