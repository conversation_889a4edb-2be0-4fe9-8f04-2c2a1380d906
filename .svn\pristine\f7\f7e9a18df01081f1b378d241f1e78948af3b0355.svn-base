using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

[ToolboxItem(false)]
public class VirtualScrollableControl : ScrollControl
{
    private bool _autoScroll;

    private Size _autoScrollMargin;

    private Size _autoScrollMinSize;

    private Point _autoScrollPosition;

    [Category("Layout")]
    [DefaultValue(true)]
    public virtual bool AutoScroll
    {
        get
        {
            return _autoScroll;
        }
        set
        {
            if (AutoScroll != value)
            {
                _autoScroll = value;
                OnAutoScrollChanged(EventArgs.Empty);
            }
        }
    }

    [Category("Layout")]
    [DefaultValue(typeof(Size), "0, 0")]
    public virtual Size AutoScrollMargin
    {
        get
        {
            return _autoScrollMargin;
        }
        set
        {
            if (value.Width < 0)
            {
                throw new ArgumentOutOfRangeException("value", "Width must be a positive integer.");
            }
            if (value.Height < 0)
            {
                throw new ArgumentOutOfRangeException("value", "Height must be a positive integer.");
            }
            if (AutoScrollMargin != value)
            {
                _autoScrollMargin = value;
                OnAutoScrollMarginChanged(EventArgs.Empty);
            }
        }
    }

    [Category("Layout")]
    [DefaultValue(typeof(Size), "0, 0")]
    public virtual Size AutoScrollMinSize
    {
        get
        {
            return _autoScrollMinSize;
        }
        set
        {
            if (AutoScrollMinSize != value)
            {
                _autoScrollMinSize = value;
                OnAutoScrollMinSizeChanged(EventArgs.Empty);
            }
        }
    }

    [Browsable(false)]
    [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
    public virtual Point AutoScrollPosition
    {
        get
        {
            return _autoScrollPosition;
        }
        set
        {
            Point point = AdjustPositionToSize(new Point(-value.X, -value.Y));
            if (AutoScrollPosition != point)
            {
                ScrollByOffset(new Size(_autoScrollPosition.X - point.X, _autoScrollPosition.Y - point.Y));
                _autoScrollPosition = point;
                OnAutoScrollPositionChanged(EventArgs.Empty);
            }
        }
    }

    protected Rectangle ScrollArea
    {
        get
        {
            Rectangle rectangle = Rectangle.Empty;
            foreach (Control control in base.Controls)
            {
                if (control.Visible)
                {
                    rectangle = Rectangle.Union(control.Bounds, rectangle);
                }
            }
            return Rectangle.Union(rectangle, new Rectangle(_autoScrollPosition, _autoScrollMinSize));
        }
    }

    protected Rectangle ViewPortRectangle => new Rectangle(-_autoScrollPosition.X, -_autoScrollPosition.Y, DisplayRectangle.Width, DisplayRectangle.Height);

    [Category("Property Changed")]
    public event EventHandler AutoScrollChanged;

    [Category("Property Changed")]
    public event EventHandler AutoScrollMarginChanged;

    [Category("Property Changed")]
    public event EventHandler AutoScrollMinSizeChanged;

    [Category("Property Changed")]
    public event EventHandler AutoScrollPositionChanged;

    public VirtualScrollableControl()
    {
        AutoScrollMargin = Size.Empty;
        AutoScrollMinSize = Size.Empty;
        AutoScrollPosition = Point.Empty;
        AutoScroll = true;
        SetStyle(ControlStyles.ContainerControl, value: true);
    }

    protected override void OnResize(EventArgs e)
    {
        base.OnResize(e);
        AdjustScrollbars();
        if (AutoScroll && !AutoScrollPosition.IsEmpty)
        {
            Rectangle scrollArea = ScrollArea;
            int num = scrollArea.Right - DisplayRectangle.Right;
            int num2 = scrollArea.Bottom - DisplayRectangle.Bottom;
            num2 = ((AutoScrollPosition.Y < 0 && num2 < 0) ? Math.Max(num2, AutoScrollPosition.Y) : 0);
            num = ((AutoScrollPosition.X < 0 && num < 0) ? Math.Max(num, AutoScrollPosition.X) : 0);
            ScrollByOffset(new Size(num, num2));
        }
    }

    protected override void OnScroll(ScrollEventArgs e)
    {
        if (e.Type != ScrollEventType.EndScroll)
        {
            switch (e.ScrollOrientation)
            {
                case ScrollOrientation.HorizontalScroll:
                    ScrollByOffset(new Size(e.NewValue + AutoScrollPosition.X, 0));
                    break;
                case ScrollOrientation.VerticalScroll:
                    ScrollByOffset(new Size(0, e.NewValue + AutoScrollPosition.Y));
                    break;
            }
        }
        base.OnScroll(e);
    }

    protected override void OnVisibleChanged(EventArgs e)
    {
        if (base.Visible)
        {
            PerformLayout();
        }
        base.OnVisibleChanged(e);
    }

    public void ScrollControlIntoView(Control activeControl)
    {
        if (activeControl.Visible && AutoScroll && (base.HorizontalScroll.Visible || base.VerticalScroll.Visible))
        {
            Point point = AdjustPositionToSize(new Point(AutoScrollPosition.X + activeControl.Left, AutoScrollPosition.Y + activeControl.Top));
            if (point.X != AutoScrollPosition.X || point.Y != AutoScrollPosition.Y)
            {
                ScrollByOffset(new Size(AutoScrollPosition.X - point.X, AutoScrollPosition.Y - point.Y));
            }
        }
    }

    protected Point AdjustPositionToSize(Point position)
    {
        int num = position.X;
        int num2 = position.Y;
        if (num < -(AutoScrollMinSize.Width - base.ClientRectangle.Width))
        {
            num = -(AutoScrollMinSize.Width - base.ClientRectangle.Width);
        }
        if (num2 < -(AutoScrollMinSize.Height - base.ClientRectangle.Height))
        {
            num2 = -(AutoScrollMinSize.Height - base.ClientRectangle.Height);
        }
        if (num > 0)
        {
            num = 0;
        }
        if (num2 > 0)
        {
            num2 = 0;
        }
        return new Point(num, num2);
    }

    protected virtual void OnAutoScrollChanged(EventArgs e)
    {
        this.AutoScrollChanged?.Invoke(this, e);
    }

    protected virtual void OnAutoScrollMarginChanged(EventArgs e)
    {
        this.AutoScrollMarginChanged?.Invoke(this, e);
    }

    protected virtual void OnAutoScrollMinSizeChanged(EventArgs e)
    {
        AutoScrollPosition = AdjustPositionToSize(AutoScrollPosition);
        AdjustScrollbars();
        this.AutoScrollMinSizeChanged?.Invoke(this, e);
    }

    protected virtual void OnAutoScrollPositionChanged(EventArgs e)
    {
        this.AutoScrollPositionChanged?.Invoke(this, e);
    }

    private void AdjustScrollbars()
    {
        Rectangle clientRectangle = base.ClientRectangle;
        if (clientRectangle.Width > 1 && clientRectangle.Height > 1)
        {
            Size empty = Size.Empty;
            Size empty2 = Size.Empty;
            bool num = AutoScroll && AutoScrollMinSize.Width > clientRectangle.Width;
            if (AutoScroll && AutoScrollMinSize.Height > clientRectangle.Height)
            {
                empty.Height = AutoScrollMinSize.Height;
                empty2.Height = clientRectangle.Height - 1;
            }
            if (num)
            {
                empty.Width = AutoScrollMinSize.Width;
                empty2.Width = clientRectangle.Width - 1;
            }
            ScrollSize = empty;
            PageSize = empty2;
        }
    }

    private void ScrollByOffset(Size offset)
    {
        if (offset.IsEmpty)
        {
            return;
        }
        SuspendLayout();
        foreach (Control control in base.Controls)
        {
            control.Location -= offset;
        }
        _autoScrollPosition = new Point(_autoScrollPosition.X - offset.Width, _autoScrollPosition.Y - offset.Height);
        ScrollTo(-_autoScrollPosition.X, -_autoScrollPosition.Y);
        ResumeLayout();
        Invalidate();
    }
}
