﻿using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public class PanelPictureView : ImageBox
    {
        private Size DragSize;
        public PanelPictureView()
        {
            Margin = Padding.Empty;
            AutoScroll = true;
            TabStop = false;
            AutoCenter = false;
            DragSize = SystemInformation.DragSize;

            var monitor = new MouseWheelMonitor(this, 300);
            monitor.MouseWheelStarted += DelayHideAll_Event;
            monitor.MouseWheelStopped += DelayShowAll_Event;

            MouseDown += PanelPictureView_MouseDown;
            MouseMove += PanelPictureView_MouseMove;
            MouseUp += PanelPictureView_MouseUp;
            MouseDoubleClick += PanelPictureView_MouseDoubleClick;
        }

        private void DelayHideAll_Event(object sender, EventArgs e)
        {
            HideAll();
        }

        private void DelayShowAll_Event(object sender, EventArgs e)
        {
            ShowAll();
        }

        private void PanelPictureView_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            ClearMoveInfo();
        }

        private void ClearMoveInfo()
        {
            _lastPoint = Point.Empty;
            _isBeginMouseDown = false;
            _isBeginMouseMove = false;
        }

        private Point _lastPoint;
        private bool _isBeginMouseDown;
        private bool _isBeginMouseMove;

        private void PanelPictureView_MouseDown(object sender, MouseEventArgs e)
        {
            _isBeginMouseDown = true;
            _lastPoint = MousePosition;
        }

        private void PanelPictureView_MouseMove(object sender, MouseEventArgs e)
        {
            if (!_isBeginMouseDown || _isBeginMouseMove) return;
            if (!_lastPoint.IsEmpty)
            {
                var nowPoint = MousePosition;
                _isBeginMouseMove = Math.Abs(nowPoint.X - _lastPoint.X) > DragSize.Width || Math.Abs(nowPoint.Y - _lastPoint.Y) > DragSize.Height;
            }
            if (_isBeginMouseMove)
                HideAll();
        }

        private void PanelPictureView_MouseUp(object sender, MouseEventArgs e)
        {
            if (_isBeginMouseMove)
                ShowAll();
            ClearMoveInfo();
        }

        public void FirstInit()
        {
            //ShowAll();
        }

        private void HideAll()
        {
            CommonMethod.DetermineCall(this, delegate
            {
                HideChild(this, true);
                Invalidate();
            });
        }

        private void ShowAll()
        {
            CommonMethod.DetermineCall(this, delegate
            {
                InitChildZoom(this, true, Zoom * 1.0 / 100);
                Invalidate();
            });
        }

        public void Clear()
        {
            try
            {
                foreach (Control item in Controls) item.Dispose();
            }
            catch
            {
            }

            try
            {
                Controls.Clear();
            }
            catch
            {
            }
        }

        public void BindPicTxt(UcContent content, double zoom, bool isShowTxt = false)
        {
            Image = content.Image;
            if (content.Image != null)
                Size = new Size((int)(content.Image.Size.Width * zoom), (int)(content.Image.Size.Height * zoom));
            //SendToBack();

            var lstCells =
                CommonString.JavaScriptSerializer.Deserialize<List<TextCellInfo>>(
                    content.OcrContent.result.verticalText);
            var lstControls = new List<Control>();

            foreach (var item in lstCells)
            {
                if (string.IsNullOrEmpty(item.words) && string.IsNullOrEmpty(item.trans)) continue;
                var itemTxt = content.GetTextByContent(item);
                var baseSize = new Size((int)item.location.width, (int)item.location.height);

                var lbl = new TransParentLabel
                {
                    OrgLocation = new Point((int)Math.Floor(item.location.left), (int)Math.Floor(item.location.top)),
                    OriSize = new Size(baseSize.Width + 6, baseSize.Height + 6),
                    Font = CommonSetting.默认文字字体,
                    ForeColor = CommonSetting.Get默认文字颜色(),
                    ContentBackColor = CommonSetting.Get默认背景颜色(),
                    TabStop = false,
                    Text = itemTxt,
                    TabIndex = lstCells.IndexOf(item) + 1,
                    IsShowText = isShowTxt,
                    Padding = Padding.Empty,
                    Margin = Padding.Empty
                }; // new Label() { BorderStyle = BorderStyle.Fixed3D, AutoSize = false, BackColor = Color.Transparent };

                ////lbl.Font = GetFontByGraphicsMeasure(gh, lbl.Text, BaseFont, ref baseSize);
                ////lbl.Font = GetFontByTextRendererMeasure(gh, lbl.Text, BaseFont, ref baseSize);

                lbl.Location = GetZoomLocation(lbl.OrgLocation, zoom);
                lbl.Size = GetZoomSize(lbl.OriSize, zoom);

                CommonMethod.ShowTxtToolTip(content, lbl);

                //lbl.Visible = ClientRectangle.IntersectsWith(new Rectangle(lbl.Location, lbl.Size));

                lstControls.Add(lbl);
                //Controls.Add(lbl);

                //lbl.BringToFront();
            }
            try
            {
                SuspendLayout();
                Controls.AddRange(lstControls.ToArray());
                lstControls.ForEach(p =>
                {
                    p.BringToFront();
                });

                lstControls.Clear();
                lstCells.Clear();
            }
            catch { }
            finally
            {
                CommonMethod.EnableDoubleBuffering(this);
                ResumeLayout();
            }
        }

        private Size GetZoomSize(Size oldSize, double zoom)
        {
            return new Size((int)(oldSize.Width * zoom), (int)(oldSize.Height * zoom));
        }

        private Point GetZoomLocation(Point location, double zoom)
        {
            return new Point((int)(AutoScrollPosition.X + location.X * zoom) - 1, (int)(AutoScrollPosition.Y + location.Y * zoom) - 1);
        }

        private void InitChildZoom(Control item, bool isSkip, double zoom)
        {
            if (!isSkip)
                if (item is TransParentLabel ctrl)
                {
                    var newSize = GetZoomSize(ctrl.OriSize, zoom);
                    var newLocation = GetZoomLocation(ctrl.OrgLocation, zoom);
                    var visible = ClientRectangle.IntersectsWith(new Rectangle(newLocation, newSize));

                    if (!Equals(ctrl.Location, newLocation))
                        ctrl.Location = newLocation;
                    if (!Equals(ctrl.Size, newSize))
                        ctrl.Size = newSize;
                    if (!Equals(ctrl.Visible, visible))
                        ctrl.Visible = visible;
                }

            if (item.Controls.Count > 0)
                foreach (Control child in item.Controls)
                    InitChildZoom(child, false, zoom);
        }

        private void HideChild(Control item, bool isSkip)
        {
            if (!isSkip)
                if (item is TransParentLabel)
                    item.Hide();
            if (item.Controls.Count > 0)
                foreach (Control child in item.Controls)
                    HideChild(child, false);
        }
    }
}