﻿using OCRTools.Properties;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Reflection;

namespace OCRTools
{
    internal class CommonUser
    {
        private static List<UserType> LstUserType { get; set; } = new List<UserType>();

        public static List<UserType> GetUserTypes(bool isRefresh)
        {
            if (isRefresh || LstUserType == null || LstUserType.Count <= 0)
            {
                var lstTmp = OcrHelper.GetCanRegUserTypes()?.OrderBy(p => p.Type).ToList();
                if (lstTmp?.Count > 0)
                {
                    LstUserType = lstTmp;
                }
            }

            return LstUserType;
        }

        public static Bitmap GetUserLevelImage(int userLevel)
        {
            Bitmap image;
            try
            {
                image = CommonMethod.GetBitmapFromResource("vip_" + userLevel);
            }
            catch (Exception e)
            {
                image = Resources.vip_0;
            }

            return image;
        }

        public static UserTypeInfo GetNextType()
        {
            GetUserTypes(false);
            UserType nNextType = null;
            try
            {
                nNextType = Program.NowUser == null ? LstUserType.FirstOrDefault() : LstUserType.FirstOrDefault(p => p.Type > Program.NowUser.UserType);
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return new UserTypeInfo
            {
                Name = nNextType == null ? "敬请期待" : "升级到" + nNextType.Name,
                Code = nNextType?.Type ?? 0
            };
        }

        public static void UpdateLimitInfo()
        {
            var count = OcrHelper.GetCodeCount();
            if (count != null && !string.IsNullOrEmpty(count.Account))
            {
                CommonString.TodayCount = count.TodayCount;
                CommonString.LimitCount = count.LimitCount;
            }
        }

        public static string GetTodayLimitInfo(bool isShowUpdate)
        {
            return string.Format("{0}/{1}{2}"
                , CommonString.TodayCount > 0 ? CommonString.TodayCount.ToString() : "-"
                , CommonString.LimitCount > 0 ? CommonString.LimitCount.ToString() : "-"
                , isShowUpdate ? "(点击更新)" : ""
                );
        }
    }

    [Obfuscation]
    internal class UserCodeCount
    {
        [Obfuscation]
        public string Account { get; set; }

        [Obfuscation]
        public long TodayCount { get; set; }

        [Obfuscation]
        public long LimitCount { get; set; }
    }
}
