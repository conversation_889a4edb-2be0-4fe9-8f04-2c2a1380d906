using System.IO;

namespace ExcelLibrary.BinaryDrawingFormat
{
	public class EscherRecord
	{
		public ushort Prop;

		public ushort Type;

		public uint Size;

		public byte[] Data;

		public ushort Instance
		{
			get
			{
				return (ushort)(Prop >> 4);
			}
			set
			{
				Prop = (ushort)(Version | (value << 4));
			}
		}

		public byte Version
		{
			get
			{
				return (byte)(Prop & 0xFu);
			}
			set
			{
				Prop |= (ushort)(value & 0xF);
			}
		}

		public static EscherRecord Read(Stream stream)
		{
			EscherRecord escherRecord = ReadBase(stream);
			switch (escherRecord.Type)
			{
			case 61446:
				return new MsofbtDgg(escherRecord);
			case 61447:
				return new MsofbtBSE(escherRecord);
			case 61448:
				return new MsofbtDg(escherRecord);
			case 61449:
				return new MsofbtSpgr(escherRecord);
			case 61450:
				return new MsofbtSp(escherRecord);
			case 61451:
				return new MsofbtOPT(escherRecord);
			case 61452:
				return new MsofbtTextbox(escherRecord);
			case 61453:
				return new MsofbtClientTextbox(escherRecord);
			case 61454:
				return new MsofbtAnchor(escherRecord);
			case 61455:
				return new MsofbtChildAnchor(escherRecord);
			case 61456:
				return new MsofbtClientAnchor(escherRecord);
			case 61457:
				return new MsofbtClientData(escherRecord);
			case 61458:
				return new MsofbtConnectorRule(escherRecord);
			case 61459:
				return new MsofbtAlignRule(escherRecord);
			case 61460:
				return new MsofbtArcRule(escherRecord);
			case 61461:
				return new MsofbtClientRule(escherRecord);
			case 61462:
				return new MsofbtCLSID(escherRecord);
			case 61463:
				return new MsofbtCalloutRule(escherRecord);
			case 61720:
				return new MsofbtRegroupItems(escherRecord);
			case 61721:
				return new MsofbtSelection(escherRecord);
			case 61722:
				return new MsofbtColorMRU(escherRecord);
			case 61725:
				return new MsofbtDeletedPspl(escherRecord);
			case 61726:
				return new MsofbtSplitMenuColors(escherRecord);
			case 61727:
				return new MsofbtOleObject(escherRecord);
			case 61728:
				return new MsofbtColorScheme(escherRecord);
			case 61440:
				return new MsofbtDggContainer(escherRecord);
			case 61442:
				return new MsofbtDgContainer(escherRecord);
			case 61441:
				return new MsofbtBstoreContainer(escherRecord);
			case 61443:
				return new MsofbtSpgrContainer(escherRecord);
			case 61444:
				return new MsofbtSpContainer(escherRecord);
			case 61445:
				return new MsofbtSolverContainer(escherRecord);
			case 61464:
				return new MsofbtBlipStart(escherRecord);
			case 61719:
				return new MsofbtBlipEnd(escherRecord);
			default:
				return escherRecord;
			}
		}

		public EscherRecord()
		{
		}

		public EscherRecord(EscherRecord record)
		{
			Prop = record.Prop;
			Type = record.Type;
			Size = record.Size;
			Data = record.Data;
		}

		public virtual void Decode()
		{
		}

		public virtual void Encode()
		{
		}

		public static EscherRecord ReadBase(Stream stream)
		{
			BinaryReader binaryReader = new BinaryReader(stream);
			EscherRecord escherRecord = new EscherRecord();
			escherRecord.Prop = binaryReader.ReadUInt16();
			escherRecord.Type = binaryReader.ReadUInt16();
			escherRecord.Size = binaryReader.ReadUInt32();
			escherRecord.Data = binaryReader.ReadBytes((int)escherRecord.Size);
			return escherRecord;
		}

		public void Write(BinaryWriter writer)
		{
			if (this is MsofbtContainer)
			{
				Version = 15;
			}
			writer.Write(Prop);
			writer.Write(Type);
			writer.Write(Size);
			if (Size != 0)
			{
				writer.Write(Data);
			}
		}
	}
}
