using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class BOTTOMMARGIN : Record
	{
		public double Value;

		public BOTTOMMARGIN(Record record)
			: base(record)
		{
		}

		public BOTTOMMARGIN()
		{
			Type = 41;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			Value = binaryReader.ReadDouble();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(Value);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
