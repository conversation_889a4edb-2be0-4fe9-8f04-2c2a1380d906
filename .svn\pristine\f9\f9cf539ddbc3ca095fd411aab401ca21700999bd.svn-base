﻿using System.Drawing;

namespace OCRTools
{
    public struct MyColor
    {
        public RGBA RGBA;
        public HSB HSB;
        public Cmyk CMYK;

        public bool IsTransparent => RGBA.Alpha < 255;

        public MyColor(Color color)
        {
            RGBA = color;
            HSB = color;
            CMYK = color;
        }

        public static implicit operator MyColor(Color color)
        {
            return new MyColor(color);
        }

        public static implicit operator Color(MyColor color)
        {
            return color.RGBA;
        }

        public static bool operator ==(MyColor left, MyColor right)
        {
            return left.RGBA == right.RGBA && left.HSB == right.HSB && left.CMYK == right.CMYK;
        }

        public static bool operator !=(MyColor left, MyColor right)
        {
            return !(left == right);
        }

        public void RGBAUpdate()
        {
            HSB = RGBA;
            CMYK = RGBA;
        }

        public void HSBUpdate()
        {
            RGBA = HSB;
            CMYK = HSB;
        }
    }
}