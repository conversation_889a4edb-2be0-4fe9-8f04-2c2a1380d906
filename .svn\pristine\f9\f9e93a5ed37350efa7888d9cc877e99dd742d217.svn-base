using System;
using System.Collections.Generic;
using System.Drawing;

namespace OCRTools.Common
{
    /// <summary>
    /// 图像资源管理器 - 用于统一管理图像资源，避免多处引用同一图像资源时被错误释放
    /// </summary>
    public static class ImageResourceManager
    {
        // 图像资源引用计数字典
        private static readonly Dictionary<int, ImageResourceInfo> _imageResources = new Dictionary<int, ImageResourceInfo>();

        // 用于线程安全操作的锁对象
        private static readonly object _lockObj = new object();

        /// <summary>
        /// 获取一个图像的副本，并由资源管理器进行管理
        /// </summary>
        /// <param name="source">源图像</param>
        /// <returns>图像副本</returns>
        public static Image GetManagedImage(Image source)
        {
            if (source == null) return null;

            try
            {
                Image imageCopy;
                try
                {
                    imageCopy = (Image)source.Clone();
                }
                catch
                {
                    try
                    {
                        imageCopy = new Bitmap(source);
                    }
                    catch
                    {
                        imageCopy = source;
                    }
                }

                // 注册到资源管理器
                RegisterImage(imageCopy);

                return imageCopy;
            }
            catch
            {
                return source;
            }
        }

        /// <summary>
        /// 智能注册图像到资源管理器
        /// 如果图像已被管理，则增加引用计数；如果未被管理，则新建管理记录
        /// </summary>
        /// <param name="image">要管理的图像</param>
        /// <returns>是否成功注册（包括增加引用计数）</returns>
        public static bool RegisterImage(Image image)
        {
            if (image == null) return false;

            lock (_lockObj)
            {
                var key = image.GetHashCode();

                if (_imageResources.ContainsKey(key))
                {
                    var info = _imageResources[key];

                    // 尝试获取图像对象
                    if (info.TryGetImage(out Image targetImage))
                    {
                        // 检查是否是同一个图像对象的引用
                        if (ReferenceEquals(targetImage, image))
                        {
                            // 增加引用计数
                            info.ReferenceCount++;
                            return true;
                        }
                    }

                    // 键相同但不是同一对象，或者对象已被回收，记录警告
                    // 仍然增加引用计数，但记录警告
                    info.ReferenceCount++;
                    return true;
                }
                else
                {
                    // 如果不存在，添加新条目
                    var newInfo = new ImageResourceInfo(image)
                    {
                        ReferenceCount = 1
                    };

                    _imageResources.Add(key, newInfo);
                    return true;
                }
            }
        }

        /// <summary>
        /// 释放对图像的引用
        /// </summary>
        /// <param name="image">要释放的图像</param>
        public static void ReleaseImage(Image image)
        {
            if (image == null) return;

            lock (_lockObj)
            {
                var key = image.GetHashCode();

                if (_imageResources.ContainsKey(key))
                {
                    var info = _imageResources[key];

                    // 检查是否是同一个图像对象的引用
                    if (!info.TryGetImage(out Image targetImage) || !ReferenceEquals(targetImage, image))
                    {
                        // 对于不同对象，仍然尝试直接释放
                        try
                        {
                            image.Dispose();
                        }
                        catch { }
                        return;
                    }

                    // 减少引用计数
                    info.ReferenceCount--;

                    // 如果引用计数为0，则真正释放资源
                    if (info.ReferenceCount <= 0)
                    {
                        try
                        {
                            // 移除引用
                            _imageResources.Remove(key);

                            // 释放资源
                            if (targetImage != null)
                            {
                                targetImage.Dispose();
                            }
                        }
                        catch
                        {
                            // 即使释放失败，也要从管理器中移除，避免内存泄漏
                            _imageResources.Remove(key);
                        }
                    }
                    else if (info.ReferenceCount < 0)
                    {
                        // 引用计数异常，记录错误并修正
                        info.ReferenceCount = 0;
                    }
                }
                else
                {
                    try
                    {
                        image.Dispose();
                    }
                    catch { }
                }
            }
        }

        /// <summary>
        /// 清理所有未使用的图像资源
        /// </summary>
        /// <param name="timeoutMinutes">超时时间（分钟），超过此时间未访问的资源将被清理，设为0则不进行超时清理</param>
        public static void CleanupUnusedResources(int timeoutMinutes = 5)
        {
            lock (_lockObj)
            {
                List<int> keysToRemove = new List<int>();
                DateTime now = DateTime.Now;
                DateTime timeoutThreshold = now.AddMinutes(-timeoutMinutes);

                // 遍历所有资源
                foreach (var pair in _imageResources)
                {
                    bool shouldRemove = false;
                    var info = pair.Value;

                    // 检查引用计数
                    if (info.ReferenceCount <= 0)
                    {
                        shouldRemove = true;
                    }
                    // 检查超时（如果启用）
                    else if (timeoutMinutes > 0 && info.LastAccessTime < timeoutThreshold)
                    {
                        shouldRemove = true;
                    }
                    // 检查弱引用是否已失效
                    else if (!info.TryGetImage(out _))
                    {
                        shouldRemove = true;
                    }

                    if (shouldRemove)
                    {
                        keysToRemove.Add(pair.Key);
                        try
                        {
                            if (info.TryGetImage(out Image targetImage) && targetImage != null)
                            {
                                targetImage.Dispose();
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error disposing image during cleanup: {ex.Message}");
                        }
                    }
                }

                // 从字典中移除
                foreach (var key in keysToRemove)
                {
                    _imageResources.Remove(key);
                }

                // 输出清理信息（仅在开发环境或调试模式下）
                if (keysToRemove.Count > 0)
                {
                    Console.WriteLine($"[ImageResourceManager] Cleaned up {keysToRemove.Count} unused resources. Remaining: {_imageResources.Count}");
                }
            }
        }
    }

    /// <summary>
    /// 改进的图像资源信息，使用WeakReference避免强引用
    /// </summary>
    internal class ImageResourceInfo
    {
        /// <summary>
        /// 图像对象的弱引用
        /// </summary>
        public WeakReference<Image> ImageRef { get; set; }

        /// <summary>
        /// 引用计数
        /// </summary>
        public int ReferenceCount { get; set; }

        /// <summary>
        /// 最后访问时间
        /// </summary>
        public DateTime LastAccessTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ImageResourceInfo(Image image)
        {
            ImageRef = new WeakReference<Image>(image);
            LastAccessTime = DateTime.Now;
        }

        /// <summary>
        /// 尝试获取图像对象
        /// </summary>
        public bool TryGetImage(out Image image)
        {
            bool result = ImageRef.TryGetTarget(out image);
            if (result)
            {
                // 更新最后访问时间
                LastAccessTime = DateTime.Now;
            }
            return result;
        }
    }
}