using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace OCRTools
{
    internal class ToolPointer : Tool
    {
        private CommandChangeState _commandChangeState;

        private Point _lastPoint = new Point(0, 0);

        private DrawObject _resizedObject;

        private int _resizedObjectHandle;

        private SelectionMode _selectMode = SelectionMode.None;

        private Point _startPoint = new Point(0, 0);

        private bool _wasMove;

        public void MouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            _commandChangeState = null;
            _wasMove = false;
            _selectMode = SelectionMode.None;
            var point = new Point(e.X, e.Y);
            foreach (var item in drawArea.GraphicsList.Selection)
            {
                var num = item.HitTest(point);
                if (num > 0)
                {
                    _selectMode = SelectionMode.Size;
                    _resizedObject = item;
                    _resizedObjectHandle = num;
                    drawArea.GraphicsList.UnselectAll();
                    if (item.NoteType != DrawToolType.Step) item.Selected = true;
                    if (item.NoteType == DrawToolType.Mosaic || item.NoteType == DrawToolType.Gaus)
                        item.IsCache = false;
                    if (item.NoteType == DrawToolType.Catch) drawArea.HideTool();
                    if (item.NoteType == DrawToolType.MultiCatch) drawArea.HideMultiTool();
                    _commandChangeState = new CommandChangeState(drawArea.GraphicsList);
                    break;
                }
            }

            var @catch = drawArea.GetCatch();
            if (@catch != null)
            {
                var num2 = @catch.HitCatch(point);
                if (num2 > 0)
                {
                    _selectMode = SelectionMode.Size;
                    _resizedObject = @catch;
                    _resizedObjectHandle = num2;
                    drawArea.GraphicsList.UnselectAll();
                    @catch.Selected = true;
                    drawArea.HideTool();
                    drawArea.HideMultiTool();
                    _commandChangeState = new CommandChangeState(drawArea.GraphicsList);
                }
            }

            if (_selectMode == SelectionMode.None)
            {
                var count = drawArea.GraphicsList.Count;
                DrawObject drawObject = null;
                for (var i = 0; i < count; i++)
                    if (drawArea.GraphicsList[i].HitTest(point) == 0)
                    {
                        drawObject = drawArea.GraphicsList[i];
                        break;
                    }

                if (drawObject != null)
                {
                    _selectMode = SelectionMode.Move;
                    if ((Control.ModifierKeys & Keys.Control) == 0 && !drawObject.Selected)
                        drawArea.GraphicsList.UnselectAll();
                    drawObject.Selected = true;
                    if (drawObject.NoteType == DrawToolType.Mosaic || drawObject.NoteType == DrawToolType.Gaus ||
                        drawObject.NoteType == DrawToolType.Highlight) drawObject.IsCache = false;
                    if (drawObject.NoteType == DrawToolType.Catch) drawArea.HideTool();
                    if (drawObject.NoteType == DrawToolType.MultiCatch) drawArea.HideMultiTool();
                    StaticValue.CurrentToolType = drawObject.NoteType;
                    _commandChangeState = new CommandChangeState(drawArea.GraphicsList);
                    drawArea.Cursor = CursorEx.Move;
                }
            }

            _lastPoint.X = e.X;
            _lastPoint.Y = e.Y;
            _startPoint.X = e.X;
            _startPoint.Y = e.Y;
            drawArea.Capture = true;
            drawArea.Refresh();
        }

        public override void OnMouseDown(DrawArea drawArea, MouseEventArgs e)
        {
            MouseDown(drawArea, e);
        }

        public override void OnMouseMove(DrawArea drawArea, MouseEventArgs e)
        {
            var point = new Point(e.X, e.Y);
            if (e.Button == MouseButtons.None)
            {
                Cursor cursor = null;
                for (var i = 0; i < drawArea.GraphicsList.Count; i++)
                {
                    var num = drawArea.GraphicsList[i].HitTest(point);
                    if (num > 0)
                    {
                        cursor = drawArea.GraphicsList[i].GetHandleCursor(num);
                        break;
                    }
                }

                if (cursor == null) cursor = CursorEx.Cross;
                drawArea.Cursor = cursor;
                return;
            }

            var selected = drawArea.GetSelected(drawArea);
            if (e.Button != MouseButtons.Left) return;
            var num2 = e.X - _lastPoint.X;
            var num3 = e.Y - _lastPoint.Y;
            _lastPoint.X = e.X;
            _lastPoint.Y = e.Y;
            if (_selectMode == SelectionMode.Size && _resizedObject != null)
            {
                _wasMove = true;
                var @object = drawArea.GraphicsList.SelectedObjects.ToList();
                if (selected != null && selected.NoteType == DrawToolType.MultiCatch)
                {
                    _resizedObject.MoveHandleTo(e.Location, _resizedObjectHandle);
                    return;
                }

                using (new AutomaticCanvasRefresher(drawArea, @object.GetGroupBoundingBox))
                {
                    var selected2 = drawArea.GetSelected(drawArea);
                    if (selected2 != null)
                        switch (selected2.NoteType)
                        {
                            case DrawToolType.Pointer:
                            case DrawToolType.Step:
                            case DrawToolType.Polygon:
                                break;
                            case DrawToolType.Line:
                                _resizedObject.MoveHandleTo(e.Location, _resizedObjectHandle,
                                    drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                                break;
                            case DrawToolType.Arrow:
                                _resizedObject.MoveHandleTo(e.Location, _resizedObjectHandle,
                                    drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                                break;
                            case DrawToolType.Rectangle:
                                _resizedObject.MoveHandleTo(e.Location, _resizedObjectHandle,
                                    drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                                break;
                            case DrawToolType.Ellipse:
                                _resizedObject.MoveHandleTo(e.Location, _resizedObjectHandle,
                                    drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                                break;
                            case DrawToolType.Text:
                                _resizedObject.MoveHandleTo(e.Location, _resizedObjectHandle,
                                    drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                                break;
                            case DrawToolType.Mosaic:
                                _resizedObject.MoveHandleTo(e.Location, _resizedObjectHandle,
                                    drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                                break;
                            case DrawToolType.Gaus:
                                _resizedObject.MoveHandleTo(e.Location, _resizedObjectHandle,
                                    drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                                break;
                            case DrawToolType.RectangleFill:
                                _resizedObject.MoveHandleTo(e.Location, _resizedObjectHandle,
                                    drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                                break;
                            case DrawToolType.Highlight:
                                _resizedObject.MoveHandleTo(e.Location, _resizedObjectHandle,
                                    drawArea.IsAnyModifierPressed(KeyModifiers.Shift));
                                break;
                            case DrawToolType.MultiCatch:
                                _resizedObject.MoveHandleTo(e.Location, _resizedObjectHandle);
                                break;
                        }
                }

                if (selected != null && selected.NoteType == DrawToolType.Catch && selected is DrawCatch drawCatch)
                {
                    using (new AutomaticCanvasRefresher(drawArea, drawCatch.GetAddBound))
                    {
                    }

                    using (new AutomaticCanvasRefresher(drawArea, () => drawCatch.GetBoundingBox()))
                    {
                        _resizedObject.MoveHandleTo(e.Location, _resizedObjectHandle);
                    }
                }
            }

            if (_selectMode != SelectionMode.Move || !drawArea.GraphicsList.SelectedObjects.Any()) return;
            var list = drawArea.GraphicsList.SelectedObjects.ToList();
            var rectangle = drawArea.BaseVirtualScreen().SizeOffset(-2);
            _wasMove = true;
            if (selected.NoteType == DrawToolType.MultiCatch)
            {
                foreach (var item in list)
                {
                    var rectangle2 = item.Rectangle.SizeOffset(-1);
                    var num4 = rectangle.X - rectangle2.X;
                    var num5 = rectangle.Y - rectangle2.Y;
                    var num6 = rectangle.X + rectangle.Width - rectangle2.X - rectangle2.Width;
                    var num7 = rectangle.Y + rectangle.Height - rectangle2.Y - rectangle2.Height;
                    if (num4 > num2) num2 = num4;
                    if (num6 < num2) num2 = num6;
                    if (num5 > num3) num3 = num5;
                    if (num7 < num3) num3 = num7;
                    item.Move(num2, num3);
                }

                return;
            }

            if (selected.NoteType == DrawToolType.Catch && selected is DrawCatch object2)
                using (new AutomaticCanvasRefresher(drawArea, object2.GetAddBound))
                {
                }

            using (new AutomaticCanvasRefresher(drawArea, list.GetGroupBoundingBox))
            {
                foreach (var item2 in list)
                {
                    if (selected.NoteType == DrawToolType.Catch)
                    {
                        var rectangle3 = item2.Rectangle.SizeOffset(-1);
                        var num8 = rectangle.X - rectangle3.X;
                        var num9 = rectangle.Y - rectangle3.Y;
                        var num10 = rectangle.X + rectangle.Width - rectangle3.X - rectangle3.Width;
                        var num11 = rectangle.Y + rectangle.Height - rectangle3.Y - rectangle3.Height;
                        if (num8 > num2) num2 = num8;
                        if (num10 < num2) num2 = num10;
                        if (num9 > num3) num3 = num9;
                        if (num11 < num3) num3 = num11;
                    }

                    item2.Move(num2, num3);
                }
            }
        }

        public override void OnMouseUp(DrawArea drawArea, MouseEventArgs e)
        {
            if (_resizedObject != null)
            {
                _resizedObject.Normalize();
                _resizedObject = null;
            }

            var graphicsList = drawArea.GraphicsList.graphicsList;
            foreach (var item in graphicsList) item.IsCache = true;
            var selected = drawArea.GetSelected(drawArea);
            if (selected != null)
            {
                if (selected.NoteType == DrawToolType.Catch) drawArea.ShowTool(selected);
                if (selected.NoteType == DrawToolType.MultiCatch) drawArea.ShowMultiTool(selected);
            }

            if (_commandChangeState != null && _wasMove)
            {
                _commandChangeState.NewState(drawArea.GraphicsList);
                drawArea.AddCommandToHistory(_commandChangeState);
                _commandChangeState = null;
            }

            drawArea.Capture = false;
            drawArea.Refresh();
            if (StaticValue.CurrentToolType != 0) drawArea.ActiveTool = StaticValue.CurrentToolType;
        }

        private enum SelectionMode
        {
            None,
            Move,
            Size
        }
    }
}