﻿using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Language;
using OCRTools.Properties;
using OCRTools.Shadow;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Drawing.Text;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;

namespace OCRTools
{
    public static class ControlExtension
    {
        public static List<string> LastContent;

        public static List<string> lstQqDrag = new List<string> { "WeChat_", "QQ_" };

        public static void ApplyResourcesExt(this ComponentResourceManager resourceManager, Control ctrl, string objectName = null)
        {
            resourceManager.ApplyResources(ctrl, objectName == null ? ctrl.Name : objectName);
        }

        public static string ToHex(this Color color, ColorFormat format = ColorFormat.RGB)
        {
            switch (format)
            {
                default:
                    return $"{color.R:X2}{color.G:X2}{color.B:X2}";
                case ColorFormat.RGBA:
                    return $"{color.R:X2}{color.G:X2}{color.B:X2}{color.A:X2}";
                case ColorFormat.ARGB:
                    return $"{color.A:X2}{color.R:X2}{color.G:X2}{color.B:X2}";
            }
        }

        public static string ToRgb(this Color color)
        {
            return string.Format("{0},{1},{2}"
                , color.R.ToString().PadLeft(3, ' ')
                , color.G.ToString().PadLeft(3, ' ')
                , color.B.ToString().PadLeft(3, ' '));
        }

        public static float GetDpiScale(this Control ctrl)
        {
            var windowRatio = 1f;
            try
            {
                GetScreenDpi(Screen.FromHandle(ctrl.Handle), DpiType.Effective, out uint dpiX, out uint dpiY);
                windowRatio = dpiX / 96.0f;
            }
            catch
            {
                try
                {
                    var dpiForWindow = GetDpiForWindow(ctrl.Handle);
                    windowRatio = dpiForWindow / 96.0f;
                }
                catch
                {
                    using (Graphics graphics = ctrl.CreateGraphics())
                    {
                        switch (graphics.DpiX)
                        {
                            case 96: windowRatio = 1f; break;
                            case 120: windowRatio = 1.25f; break;
                            case 144: windowRatio = 1.5f; break;
                            case 168: windowRatio = 1.75f; break;
                            case 192: windowRatio = 2f; break;
                            default:
                                windowRatio = 1f;
                                break;
                        }
                    }
                }
            }
            return windowRatio;
        }

        //https://msdn.microsoft.com/en-us/library/windows/desktop/dd145062(v=vs.85).aspx
        [DllImport("User32.dll")]
        private static extern IntPtr MonitorFromPoint([In] Point pt, [In] uint dwFlags);

        //https://msdn.microsoft.com/en-us/library/windows/desktop/dn280510(v=vs.85).aspx
        [DllImport("Shcore.dll")]
        private static extern IntPtr GetDpiForMonitor([In] IntPtr hmonitor, [In] DpiType dpiType, [Out] out uint dpiX, [Out] out uint dpiY);

        //https://msdn.microsoft.com/en-us/library/windows/desktop/dn280511(v=vs.85).aspx
        internal enum DpiType
        {
            Effective = 0,
            Angular = 1,
            Raw = 2,
        }

        private static void GetScreenDpi(Screen screen, DpiType dpiType, out uint dpiX, out uint dpiY)
        {
            var pnt = new Point(screen.Bounds.Left + 1, screen.Bounds.Top + 1);
            var mon = MonitorFromPoint(pnt, 2/*MONITOR_DEFAULTTONEAREST*/);
            GetDpiForMonitor(mon, dpiType, out dpiX, out dpiY);
        }

        [DllImport("user32.dll")]
        private static extern uint GetDpiForWindow([In] IntPtr hmonitor);

        public static float GetScale(this Form form, float oldDpi)
        {
            var scale = 1f;
            if (CommonString.CommonGraphicsUnit() != GraphicsUnit.Pixel)
            {
                var dpiScale = HighDpiHelper.GetFormDpi(form);
                scale = dpiScale / oldDpi;
            }
            return scale;
        }

        /// 文件拖拽支持
        /// 拖拽的文件路径列表
        [Obfuscation]
        public static void ControlUseDrop(this Control control)
        {
            CommonMethod.DetermineCall(FrmMain.FrmTool, () =>
            {
                try
                {
                    control.AllowDrop = true;

                    control.DragEnter -= DragDropEngine.ProcessDragEnter;
                    control.DragDrop -= DragDropEngine.ProcessDragDrop;
                    control.DragLeave -= DragDropEngine.ProcessDragLeave;
                    control.DragOver -= DragDropEngine.ProcessDragOver;

                    control.DragEnter += DragDropEngine.ProcessDragEnter;
                    control.DragDrop += DragDropEngine.ProcessDragDrop;
                    control.DragLeave += DragDropEngine.ProcessDragLeave;
                    control.DragOver += DragDropEngine.ProcessDragOver;

                    control.DragEnter -= Control_DragEnter;
                    control.DragDrop -= DragDropEvent();
                    control.DragEnter += Control_DragEnter;
                    control.DragDrop += DragDropEvent();

                    if (CommonString.IsAdministrator)
                    {
                        ElevatedDragDropManager.Instance.EnableDragDrop(control);
                        ElevatedDragDropManager.Instance.ElevatedDragDrop -= Control_DragEnter;
                        ElevatedDragDropManager.Instance.ElevatedDragDrop += Control_DragEnter;
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
            });
        }

        public static void ViewImage(this Control control, Image image, string fileName = null)
        {
            if (image == null && string.IsNullOrEmpty(fileName)) return;
            try
            {
                var frmViewImg = new FormViewImage();
                frmViewImg.Bind(image == null ? null : new Bitmap(image), fileName);
                frmViewImg.Show();
                frmViewImg.WindowState = FormWindowState.Maximized;
            }
            catch (Exception oe)
            {
                Log.WriteError("ViewImage", oe);
            }
        }

        public static void ViewImageFile(this Control control, string file, Point point)
        {
            if (string.IsNullOrEmpty(file) || !File.Exists(file))
            {
                return;
            }

            try
            {
                var image = Image.FromFile(file);
                control.ViewImageWithLocation(image, point);
            }
            catch (Exception oe)
            {
                Log.WriteError("ViewImageFile", oe);
            }
        }

        public static void ViewImageWithLocation(this Control control, Image image, Point point)
        {
            if (image == null) return;
            var data = new ClipboardTextEntity
            {
                Image = image,
                Location = point,
                Type = ClipboardDataType.Image
            };
            control.ViewTextImage(data);
        }

        public static void ViewTextImage(this Control control, ClipboardTextEntity data)
        {
            if (data == null || data.Image == null && string.IsNullOrEmpty(data.Content)) return;
            var frmViewImg = GetViewImageForm(data);
            if (frmViewImg != null)
            {
                frmViewImg.ShadowShark();
            }
            else
            {
                frmViewImg = new FrmPasteImage
                {
                    IsShowTitle = false,
                    Icon = CommonMethod.GetApplicationIcon(),
                    Data = data,
                    ShowInTaskbar = false,
                    IsSharkWindow = CommonSetting.闪烁,
                    IsForceActive = CommonSetting.激活窗口,
                    IsShowShadow = CommonSetting.贴图阴影,
                    IsUseCustomerShadowColor = true
                };
                frmViewImg.SetImageData();
                var size = frmViewImg.Size;
                frmViewImg.Show();
                if (!Equals(size, frmViewImg.Size))
                {
                    frmViewImg.Size = size;
                }
                frmViewImg.BingSizeChangeEvent(true);
            }
        }

        public static void Ocr(this Image image)
        {
            FrmMain.RecByImageDelegate?.Invoke(null, ProcessBy.主界面, image, image.Tag?.ToString());
        }

        public static string SaveFileWithOutConfirm(this Image bmp, string fileName = null, bool isTemp = false)
        {
            if (bmp == null) return fileName;
            if (string.IsNullOrEmpty(fileName))
            {
                var filePath = isTemp ? CommonString.DefaultTmpPath : CommonSetting.截图文件保存路径;
                try
                {
                    if (!string.IsNullOrEmpty(filePath) && !Directory.Exists(filePath))
                        Directory.CreateDirectory(filePath);
                }
                catch
                {
                    filePath = Application.StartupPath;
                }

                fileName = string.Format("{0}\\{1}", filePath, bmp.GetFileName());
            }

            fileName = fileName.Replace("\\\\", "\\");
            var extension = Path.GetExtension(fileName);
            if (string.IsNullOrEmpty(extension))
            {
                fileName += ".png";
                extension = Path.GetExtension(fileName);
            }
            var png = ImageFormat.Png;
            switch (extension)
            {
                case ".gif":
                    png = ImageFormat.Gif;
                    break;
                case ".bmp":
                    png = ImageFormat.Bmp;
                    break;
                case ".jpg":
                case ".jpeg":
                    png = ImageFormat.Jpeg;
                    break;
                case ".tif":
                    png = ImageFormat.Tiff;
                    break;
            }

            bmp.SafeSave(fileName, png);

            return fileName;
        }

        public static bool SaveFile(this Image bmp, Control ctrl)
        {
            var result = false;
            if (bmp != null)
            {
                var saveFileDialog1 = new SaveFileDialog
                {
                    Filter = CommonString.GetImgFilter(),
                    Title = "选择保存位置".CurrentText(),
                    FileName = bmp.GetFileName(),
                    FilterIndex = 0
                };
                ctrl.CenterChild();
                if (saveFileDialog1.ShowDialog(ctrl) == DialogResult.OK)
                {
                    SaveFileWithOutConfirm(bmp, saveFileDialog1.FileName);
                    result = true;
                }
            }
            return result;
        }

        public static string GetFileName(this Image image, string strTmp = null)
        {
            /*
             %n 年份；%y 月份
            %r 天数；%s 小时
            %f 分钟；%m 秒钟
            %t 时间戳；%g 随机
             */
            if (string.IsNullOrEmpty(strTmp)) strTmp = CommonSetting.截图文件名;
            if (string.IsNullOrEmpty(strTmp)) strTmp = CommonString.DefaultImageFormat;
            var fileName = strTmp
                .Replace("%n", ServerTime.DateTime.Year.ToString())
                .Replace("%y", ServerTime.DateTime.Month.ToString("00"))
                .Replace("%r", ServerTime.DateTime.Day.ToString("00"))
                .Replace("%s", ServerTime.DateTime.Hour.ToString("00"))
                .Replace("%f", ServerTime.DateTime.Minute.ToString("00"))
                .Replace("%m", ServerTime.DateTime.Second.ToString("00"))
                .Replace("%t", ServerTime.DateTime.ToTimeSpan())
                .Replace("%g", Guid.NewGuid().ToString().Replace("-", ""));
            if (Equals(fileName, strTmp)) fileName = ServerTime.DateTime.ToTimeSpan() + "-" + fileName;
            return fileName;
        }

        private static FrmPasteImage GetViewImageForm(ClipboardTextEntity data)
        {
            foreach (Form ff in Application.OpenForms)
                if (ff is FrmPasteImage image && !image.IsDisposed)
                {
                    var tag = image.Data;
                    if (!string.IsNullOrEmpty(data.Content) && Equals(tag.Content, data.Content)
                        || string.IsNullOrEmpty(tag.Content) && Equals(tag.Image, data.Image))
                        return image;
                }

            return null;
        }

        public static ShadowForm GetShadowForm(this Control ctrl, string text)
        {
            foreach (Form ff in Application.OpenForms)
                if (!ff.IsDisposed && Equals(ff.Text, text) && ff is ShadowForm form)
                    return form;

            return null;
        }

        private static DragEventHandler DragDropEvent(bool isQq = false)
        {
            return (sender, e) =>
            {
                try
                {
                    Console.WriteLine("开始处理：" + string.Join("、", e.Data.GetFormats()));
                    var sources = new List<string>();
                    if (e.Data.GetDataPresent(DataFormats.FileDrop))
                        if (e.Data.GetData(DataFormats.FileDrop) is string[] array && array.Length > 0)
                            sources.AddRange(array);

                    if (e.Data.GetDataPresent(DataFormats.Html))
                    {
                        var url = GetUrlContent(e);
                        if (!string.IsNullOrEmpty(url)) sources.Add(url);
                    }

                    if (e.Data.GetDataPresent(DataFormats.Text))
                    {
                        var text = e.Data.GetData(DataFormats.StringFormat)?.ToString();
                        if (!string.IsNullOrEmpty(text)) sources.Add("data:txt" + text);
                    }

                    e.Effect = DragDropEffects.None;
                    if (isQq && LastContent != null && LastContent.SequenceEqual(sources)) return;
                    LastContent = sources;
                    foreach (var item in sources)
                    {
                        var fileName = item;
                        if (string.IsNullOrEmpty(fileName)) continue;

                        if (!(fileName.StartsWith("http") || fileName.StartsWith("data")) &&
                            !CommonString.LstCanProcessFilesExt.Any(p => fileName.EndsWith(p)))
                        {
                            CommonMethod.ShowHelpMsg(
                                "文件类型错误！".CurrentText() + "支持的类型".CurrentText() + ":【" + "图片".CurrentText() + "】" + string.Join("、", CommonString.LstCanProcessImageFilesExt) +
                                ";【" + "文档".CurrentText() + "】" + string.Join("、", CommonString.LstCanProcessDocFilesExt));
                            return;
                        }
                    }

                    if (sources.Count > 0)
                        FrmMain.DragDropEventDelegate?.Invoke(sources, null, null, ProcessBy.主界面, null);
                }
                catch (Exception oe)
                {
                    Log.WriteError("DragDropEvent", oe);
                }
            };
        }

        private static string GetUrlContent(DragEventArgs e)
        {
            var strTmp = e.Data.GetData(DataFormats.Html) as string;
            var url = CommonMethod.SubString(strTmp, "src=\"", "\"").Trim();
            if (string.IsNullOrEmpty(url)) url = CommonMethod.SubString(strTmp, "SourceURL:", "\n").Trim();
            if (!(url.StartsWith("http") && IsImageUrl(url) || url.StartsWith("data:image"))) url = "";
            return url;
        }

        private static bool IsImageUrl(string fileUrl)
        {
            bool result; //下载结果
            WebResponse response = null;
            try
            {
                var req = WebRequest.Create(fileUrl);
                req.Timeout = 3000;
                response = req.GetResponse();
                result = response.ContentType?.Contains("image") == true;
            }
            catch (Exception)
            {
                result = false;
            }
            finally
            {
                if (response != null) response.Close();
            }

            return result;
        }

        public static bool IsFile(string[] lstFormat)
        {
            return lstFormat.Any(p => p.Contains("File"));
        }

        public static bool IsQqDrag(string[] lstFormat)
        {
            return lstFormat.Any(p => lstQqDrag.Exists(p.StartsWith));
        }

        private static void Control_DragEnter(object sender, DragEventArgs e)
        {
            var isQq = sender is ElevatedDragDropManager || IsQqDrag(e.Data.GetFormats(true));
            if (e.Data.GetDataPresent(DataFormats.FileDrop) || e.Data.GetDataPresent(DataFormats.Html) ||
                e.Data.GetDataPresent(DataFormats.StringFormat))
            {
                Console.WriteLine("当前拖拽类型：" + string.Join("、", e.Data.GetFormats()));
                if (isQq)
                {
                    DragDropEvent(true).Invoke(sender, e);
                    return;
                }

                e.Effect = DragDropEffects.All;
            }
            else
            {
                e.Effect = DragDropEffects.None;
            }
        }

        [DllImport("user32.dll", CharSet = CharSet.Auto)]
        private static extern bool SwitchToThisWindow(IntPtr hWnd, bool fAltTab);

        public static void ForceActivate(this Form form)
        {
            if (!form.IsDisposed)
                try
                {
                    if (!form.Visible) form.Show();

                    if (form.WindowState == FormWindowState.Minimized) form.WindowState = FormWindowState.Normal;

                    form.BringToFront();
                    form.Activate();
                    SwitchToThisWindow(form.Handle, true);
                }
                catch
                {
                }
        }

        public static void SetValue(this NumericUpDown nud, decimal number)
        {
            nud.Value = number.Clamp(nud.Minimum, nud.Maximum);
        }

        public static T Clamp<T>(this T num, T min, T max) where T : IComparable<T>
        {
            return MathHelpers.Clamp(num, min, max);
        }

        public static bool IsValidImage(this PictureBox pb)
        {
            return pb.Image != null && pb.Image != pb.InitialImage && pb.Image != pb.ErrorImage;
        }

        public static Size Offset(this Size size, int offset)
        {
            return size.Offset(offset, offset);
        }

        public static Size Offset(this Size size, int width, int height)
        {
            return new Size(size.Width + width, size.Height + height);
        }

        public static string Replace(this string str, string oldValue, string newValue, StringComparison comparison)
        {
            if (string.IsNullOrEmpty(oldValue)) return str;

            var sb = new StringBuilder();

            var previousIndex = 0;
            var index = str.IndexOf(oldValue, comparison);
            while (index != -1)
            {
                sb.Append(str.Substring(previousIndex, index - previousIndex));
                sb.Append(newValue);
                index += oldValue.Length;

                previousIndex = index;
                index = str.IndexOf(oldValue, index, comparison);
            }

            sb.Append(str.Substring(previousIndex));

            return sb.ToString();
        }

        public static void SetHighQuality(this Graphics g)
        {
            g.PixelOffsetMode = PixelOffsetMode.HighQuality;
            g.CompositingMode = CompositingMode.SourceCopy;
            g.CompositingQuality = CompositingQuality.HighQuality;
            g.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
            g.InterpolationMode = InterpolationMode.HighQualityBicubic;
            g.SmoothingMode = SmoothingMode.HighQuality;
        }

        public static string Left(this string str, int length)
        {
            if (length < 1) return "";
            if (length < str.Length) return str.Substring(0, length);
            return str;
        }

        public static string Right(this string str, int length)
        {
            if (length < 1) return "";
            if (length < str.Length) return str.Substring(str.Length - length);
            return str;
        }

        public static string Truncate(this string str, int maxLength, string endings, bool truncateFromRight = true)
        {
            if (!string.IsNullOrEmpty(str) && str.Length > maxLength)
            {
                var length = maxLength - endings.Length;

                if (length > 0)
                {
                    if (truncateFromRight)
                        str = str.Left(length) + endings;
                    else
                        str = endings + str.Right(length);
                }
            }

            return str;
        }

        public static Rectangle GetRectangle(this IntPtr intPtr, bool isWindow = true)
        {
            var rectangle = isWindow ? NativeMethods.GetWindowRectangle(intPtr) : NativeMethods.GetWindowRect(intPtr);
            return rectangle;
        }

        public static List<T> Range<T>(this List<T> source, int start, int end)
        {
            var list = new List<T>();

            if (start > end)
                for (var i = start; i >= end; i--)
                    list.Add(source[i]);
            else
                for (var i = start; i <= end; i++)
                    list.Add(source[i]);

            return list;
        }

        public static List<T> Range<T>(this List<T> source, T start, T end)
        {
            var startIndex = source.IndexOf(start);
            if (startIndex == -1) return new List<T>();

            var endIndex = source.IndexOf(end);
            if (endIndex == -1) return new List<T>();

            return Range(source, startIndex, endIndex);
        }

        public static void Check(this ToolStripMenuItem tsmi)
        {
            if (tsmi != null)
                foreach (ToolStripItem item in tsmi.GetCurrentParent().Items)
                    if (item != null && item is ToolStripMenuItem tsmiItem && tsmiItem.Tag.Equals(tsmi.Tag))
                        tsmiItem.Checked = tsmiItem == tsmi;
        }

        public static void RadioCheck(this ToolStripButton tsb)
        {
            ToolStrip parent = tsb.GetCurrentParent();

            foreach (ToolStripButton tsbParent in parent.Items.OfType<ToolStripButton>())
            {
                if (tsbParent != tsb)
                {
                    tsbParent.Checked = false;
                }
            }

            tsb.Checked = true;
        }

        public static bool IsTransparent(this Color color)
        {
            return color.A < 255;
        }

        public static void HideImageMargin(this ToolStripDropDownItem tsddi)
        {
            ((ToolStripDropDownMenu)tsddi.DropDown).ShowImageMargin = false;
        }

        public static void DrawCross(this Graphics g, Pen pen, Point center, int crossSize)
        {
            if (crossSize > 0)
            {
                // Horizontal
                g.DrawLine(pen, center.X - crossSize, center.Y, center.X + crossSize, center.Y);

                // Vertical
                g.DrawLine(pen, center.X, center.Y - crossSize, center.X, center.Y + crossSize);
            }
        }

        public static int Area(this Rectangle rect)
        {
            return rect.Width * rect.Height;
        }

        public static int Perimeter(this Rectangle rect)
        {
            return 2 * (rect.Width + rect.Height);
        }

        public static IEnumerable<Tuple<string, string>> ForEachBetween(this string text, string front, string back)
        {
            int f = 0;
            int b;
            while (text.Length > f && (f = text.IndexOf(front, f)) >= 0 && (b = text.IndexOf(back, f + front.Length)) >= 0)
            {
                string result = text.Substring(f, (b + back.Length) - f);
                yield return new Tuple<string, string>(result, result.Substring(front.Length, (result.Length - back.Length) - front.Length));
                f += front.Length;
            }
        }

        public static void SupportSelectAll(this TextBox tb)
        {
            tb.KeyDown += (sender, e) =>
            {
                if (e.Control && e.KeyCode == Keys.A)
                {
                    tb.SelectAll();
                    e.SuppressKeyPress = true;
                    e.Handled = true;
                }
            };
        }

        public static T Copy<T>(this T original)
        {
            return (T)Copy((object)original);
        }

        public static object Copy(this object originalObject)
        {
            return InternalCopy(originalObject, new Dictionary<object, object>(new ReferenceEqualityComparer()));
        }

        private static readonly MethodInfo CloneMethod = typeof(object).GetMethod("MemberwiseClone", BindingFlags.NonPublic | BindingFlags.Instance);
        public static bool IsPrimitive(this Type type)
        {
            if (type == typeof(string)) return true;
            return type.IsValueType && type.IsPrimitive;
        }
        public static void ForEach(this Array array, Action<Array, int[]> action)
        {
            if (array.LongLength == 0) return;
            ArrayTraverse walker = new ArrayTraverse(array);
            do action(array, walker.Position);
            while (walker.Step());
        }

        private static object InternalCopy(object originalObject, IDictionary<object, object> visited)
        {
            if (originalObject == null) return null;
            Type typeToReflect = originalObject.GetType();
            if (IsPrimitive(typeToReflect)) return originalObject;
            if (visited.ContainsKey(originalObject)) return visited[originalObject];
            if (typeof(Delegate).IsAssignableFrom(typeToReflect)) return null;
            object cloneObject = CloneMethod.Invoke(originalObject, null);
            if (typeToReflect.IsArray)
            {
                Type arrayType = typeToReflect.GetElementType();
                if (IsPrimitive(arrayType) == false)
                {
                    Array clonedArray = (Array)cloneObject;
                    clonedArray.ForEach((array, indices) => array.SetValue(InternalCopy(clonedArray.GetValue(indices), visited), indices));
                }
            }
            visited.Add(originalObject, cloneObject);
            CopyFields(originalObject, visited, cloneObject, typeToReflect);
            RecursiveCopyBaseTypePrivateFields(originalObject, visited, cloneObject, typeToReflect);
            return cloneObject;
        }

        private static void RecursiveCopyBaseTypePrivateFields(object originalObject, IDictionary<object, object> visited, object cloneObject, Type typeToReflect)
        {
            if (typeToReflect.BaseType != null)
            {
                RecursiveCopyBaseTypePrivateFields(originalObject, visited, cloneObject, typeToReflect.BaseType);
                CopyFields(originalObject, visited, cloneObject, typeToReflect.BaseType, BindingFlags.Instance | BindingFlags.NonPublic, info => info.IsPrivate);
            }
        }

        private static void CopyFields(object originalObject, IDictionary<object, object> visited, object cloneObject, Type typeToReflect, BindingFlags bindingFlags = BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.Public | BindingFlags.FlattenHierarchy, Func<FieldInfo, bool> filter = null)
        {
            foreach (FieldInfo fieldInfo in typeToReflect.GetFields(bindingFlags))
            {
                if (filter != null && filter(fieldInfo) == false) continue;
                if (IsPrimitive(fieldInfo.FieldType)) continue;
                object originalFieldValue = fieldInfo.GetValue(originalObject);
                object clonedFieldValue = InternalCopy(originalFieldValue, visited);
                fieldInfo.SetValue(cloneObject, clonedFieldValue);
            }
        }

        public static void AutoSizeDropDown(this ComboBox cb)
        {
            int maxWidth = 0;
            int verticalScrollBarWidth = cb.Items.Count > cb.MaxDropDownItems ? SystemInformation.VerticalScrollBarWidth : 0;
            foreach (object item in cb.Items)
            {
                int tempWidth = TextRenderer.MeasureText(cb.GetItemText(item), cb.Font).Width + verticalScrollBarWidth;
                if (tempWidth > maxWidth)
                {
                    maxWidth = tempWidth;
                }
            }
            cb.DropDownWidth = maxWidth;
        }

        public static void DrawCornerLines(this Graphics g, Rectangle rect, Pen pen, int lineSize)
        {
            if (rect.Width <= lineSize * 2)
            {
                g.DrawLine(pen, rect.X, rect.Y, rect.Right - 1, rect.Y);
                g.DrawLine(pen, rect.X, rect.Bottom - 1, rect.Right - 1, rect.Bottom - 1);
            }
            else
            {
                // Top left
                g.DrawLine(pen, rect.X, rect.Y, rect.X + lineSize, rect.Y);

                // Top right
                g.DrawLine(pen, rect.Right - 1, rect.Y, rect.Right - 1 - lineSize, rect.Y);

                // Bottom left
                g.DrawLine(pen, rect.X, rect.Bottom - 1, rect.X + lineSize, rect.Bottom - 1);

                // Bottom right
                g.DrawLine(pen, rect.Right - 1, rect.Bottom - 1, rect.Right - 1 - lineSize, rect.Bottom - 1);
            }

            if (rect.Height <= lineSize * 2)
            {
                g.DrawLine(pen, rect.X, rect.Y, rect.X, rect.Bottom - 1);
                g.DrawLine(pen, rect.Right - 1, rect.Y, rect.Right - 1, rect.Bottom - 1);
            }
            else
            {
                // Top left
                g.DrawLine(pen, rect.X, rect.Y, rect.X, rect.Y + lineSize);

                // Top right
                g.DrawLine(pen, rect.Right - 1, rect.Y, rect.Right - 1, rect.Y + lineSize);

                // Bottom left
                g.DrawLine(pen, rect.X, rect.Bottom - 1, rect.X, rect.Bottom - 1 - lineSize);

                // Bottom right
                g.DrawLine(pen, rect.Right - 1, rect.Bottom - 1, rect.Right - 1, rect.Bottom - 1 - lineSize);
            }
        }

        public static void AddDiamond(this GraphicsPath graphicsPath, RectangleF rect)
        {
            PointF p1 = new PointF(rect.X + (rect.Width / 2.0f), rect.Y);
            PointF p2 = new PointF(rect.X + rect.Width, rect.Y + (rect.Height / 2.0f));
            PointF p3 = new PointF(rect.X + (rect.Width / 2.0f), rect.Y + rect.Height);
            PointF p4 = new PointF(rect.X, rect.Y + (rect.Height / 2.0f));

            graphicsPath.AddPolygon(new[] { p1, p2, p3, p4 });
        }

        public static void DrawDiamond(this Graphics g, Pen pen, Rectangle rect)
        {
            using (GraphicsPath gp = new GraphicsPath())
            {
                gp.AddDiamond(rect);
                g.DrawPath(pen, gp);
            }
        }

        public static Rectangle AddPoint(this Rectangle rect, Point point)
        {
            return Rectangle.Union(rect, new Rectangle(point, new Size(1, 1)));
        }

        public static Point Restrict(this Point point, Rectangle rect)
        {
            point.X = Math.Max(point.X, rect.X);
            point.Y = Math.Max(point.Y, rect.Y);
            point.X = Math.Min(point.X, rect.X + rect.Width - 1);
            point.Y = Math.Min(point.Y, rect.Y + rect.Height - 1);
            return point;
        }

        public static string Repeat(this string str, int count)
        {
            if (!string.IsNullOrEmpty(str) && count > 0)
            {
                StringBuilder sb = new StringBuilder(str.Length * count);

                for (int i = 0; i < count; i++)
                {
                    sb.Append(str);
                }

                return sb.ToString();
            }

            return null;
        }

        public static Rectangle CreateRectangle(this IEnumerable<Point> points)
        {
            Rectangle result = Rectangle.Empty;

            foreach (Point point in points)
            {
                result = result.IsEmpty ? new Rectangle(point, new Size(1, 1)) : result.AddPoint(point);
            }

            return result;
        }

        public static Rectangle Combine(this IEnumerable<Rectangle> rects)
        {
            Rectangle result = Rectangle.Empty;

            foreach (Rectangle rect in rects)
            {
                result = result.IsEmpty ? rect : Rectangle.Union(result, rect);
            }

            return result;
        }

        public static Bitmap SetResourceImage(this Control ctrl, string source)
        {
            ctrl.AccessibleDefaultActionDescription = source;
            return ImageProcessHelper.GetResourceImage(source);
        }

        public static Bitmap SetResourceImage(this ToolStripItem ctrl, string source)
        {
            ctrl.AccessibleDefaultActionDescription = source;
            return ImageProcessHelper.GetResourceImage(source);
        }

        public static void AddContactUserBtn(this MetroForm form)
        {
            form.AddCustomButton("客服", Resources.qqKeFu, 15F, (send, e) => { CommonMethod.OpenKeFuQ(); });
            form.UpdateWindowButtonPosition();
        }

        public static void AutoSizeMutilScreen(this Form control)
        {
            Size oldSize = control.Size;
            Screen screen = Screen.FromControl(control);
            control.Activated += (object sender, EventArgs e) =>
            {
                //CommonMethod.ShowHelpMsg("Activated,Size:" + control.Size + ",OldSize:" + oldSize);
                oldSize = control.Size;
            };
            control.SizeChanged += (object sender, EventArgs e) =>
            {
                //CommonMethod.ShowHelpMsg("SizeChanged,Size:" + control.Size + ",OldSize:" + oldSize);
                oldSize = control.Size;
            };
            control.LocationChanged += (object se, EventArgs es) =>
            {
                if (Equals(screen, Screen.FromControl(control)) || oldSize.IsEmpty)
                {
                    return;
                }
                if (!Equals(control.Size, oldSize))
                {
                    //CommonMethod.ShowHelpMsg("LocationChanged,Size:" + control.Size + ",OldSize:" + oldSize);
                    control.Size = oldSize;
                }

                oldSize = control.Size;
                screen = Screen.FromControl(control);
            };
        }

        public static void LoadHtml(this Control ctrl, Point location, Size size, string url, Dictionary<string, string> dicCheck = null)
        {
            for (int i = 0; i < ctrl.Controls.Count; i++)
            {
                try
                {
                    if (ctrl.Controls[i] is PictureBox || ctrl.Controls[i] is WebBrowser2)
                    {
                        ctrl.Controls.RemoveAt(i);
                        i--;
                    }
                }
                catch
                {
                    break;
                }
            }
            var broswer = new WebBrowser2
            {
                Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom,
                Location = location,
                Size = size,
                IsWebBrowserContextMenuEnabled = false
            };
            if (size.IsEmpty)
            {
                broswer.Dock = DockStyle.Fill;
            }
            broswer.ScriptErrorsSuppressed = true;
            broswer.NavigateError += (sender, e) =>
            {
                CommonMethod.ShowHelpMsg("网页加载失败，将使用浏览器打开！".CurrentText());
                CommonMethod.OpenUrl(url);
            };
            bool isChecked = false;
            broswer.DocumentCompleted += (sender, e) =>
            {
                if (!isChecked && dicCheck != null && dicCheck.Count > 0)
                {
                    isChecked = true;
                    System.Threading.Tasks.Task.Factory.StartNew(() =>
                    {
                        System.Threading.Thread.Sleep(3000);
                        try
                        {
                            CommonMethod.DetermineCall(broswer, () =>
                                {
                                    var result = false;
                                    foreach (var item in dicCheck)
                                    {
                                        var objText = broswer.Document?.GetElementById(item.Key)?.InnerText;
                                        Console.WriteLine(objText);
                                        result = Equals(objText, item.Value);
                                        if (result)
                                        {
                                            break;
                                        }
                                    }
                                    dicCheck = null;
                                    if (result)
                                    {
                                        broswer.FindForm().Close();
                                        //CommonMethod.ShowHelpMsg("网页加载可能异常，尝试直接在浏览器打开！");
                                        CommonMethod.OpenUrl(url);
                                    }
                                });
                        }
                        catch { }
                    });
                }
            };
            broswer.Navigate(url);
            ctrl.Controls.Add(broswer);
            broswer.NavigateWithAutoAgent(url);
        }
    }

    public class ArrayTraverse
    {
        public int[] Position;
        private int[] maxLengths;

        public ArrayTraverse(Array array)
        {
            maxLengths = new int[array.Rank];
            for (int i = 0; i < array.Rank; ++i)
            {
                maxLengths[i] = array.GetLength(i) - 1;
            }
            Position = new int[array.Rank];
        }

        public bool Step()
        {
            for (int i = 0; i < Position.Length; ++i)
            {
                if (Position[i] < maxLengths[i])
                {
                    Position[i]++;
                    for (int j = 0; j < i; j++)
                    {
                        Position[j] = 0;
                    }
                    return true;
                }
            }
            return false;
        }
    }

    public class ReferenceEqualityComparer : EqualityComparer<object>
    {
        public override bool Equals(object x, object y)
        {
            return ReferenceEquals(x, y);
        }

        public override int GetHashCode(object obj)
        {
            return obj.GetHashCode();
        }
    }
}