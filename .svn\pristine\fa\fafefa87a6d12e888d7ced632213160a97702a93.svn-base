using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Runtime.InteropServices;

namespace OCRTools.Common
{
    /// <summary>
    /// 引导页可视化设计器 - 允许在实际窗体上直接设计引导步骤
    /// </summary>
    public partial class GuideDesigner : Form
    {
        #region 窗体属性和变量

        // 目标窗体
        private Form _targetForm;
        // 引导实体
        private GuideEntity _guideEntity;
        // 当前选中的引导步骤
        private GuideItem _currentStep;
        // 当前步骤索引
        private int _currentStepIndex = -1;
        // 覆盖层窗体
        private OverlayForm _overlayForm;
        // 设计模式
        private DesignMode _designMode = DesignMode.None;
        // 鼠标开始位置（用于框选）
        private Point _startPoint;
        // 鼠标结束位置（用于框选）
        private Point _endPoint;
        // 是否正在框选
        private bool _isSelecting = false;
        // 是否已经修改
        private bool _isModified = false;
        // 添加静态标志，防止多次触发
        private static bool _isHandlingFormClosing = false;

        // 设计模式枚举
        public enum DesignMode
        {
            None,           // 无操作
            ControlCapture, // 控件捕获
            AreaSelect,     // 区域框选
            SmartCapture    // 智能匹配
        }

        #endregion

        #region 构造函数和初始化

        /// <summary>
        /// 构造函数
        /// </summary>
        public GuideDesigner()
        {
            InitializeComponent();
            InitializeCustomComponents();
        }

        /// <summary>
        /// 初始化组件（设计器支持所需的方法）
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // GuideDesigner
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(700, 500);
            this.Name = "GuideDesigner";
            this.Text = "引导页设计器";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.GuideDesigner_FormClosing);

            // 关键：添加键盘事件处理
            this.KeyPreview = true;
            this.KeyDown += new System.Windows.Forms.KeyEventHandler(this.GuideDesigner_KeyDown);

            this.ResumeLayout(false);
        }

        // 添加键盘事件处理方法
        private void GuideDesigner_KeyDown(object sender, KeyEventArgs e)
        {
            // Esc键取消当前操作
            if (e.KeyCode == Keys.Escape)
            {
                if (_designMode != DesignMode.None)
                {
                    _designMode = DesignMode.None;
                    _overlayForm?.Hide();
                    UpdateCaptureButton();
                }
            }
        }

        // 重写Dispose方法，确保释放资源
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // 释放覆盖层
                if (_overlayForm != null)
                {
                    _overlayForm.Dispose();
                    _overlayForm = null;
                }
            }
            base.Dispose(disposing);
        }

        /// <summary>
        /// 初始化自定义组件
        /// </summary>
        private void InitializeCustomComponents()
        {
            // 设置窗体属性
            this.FormBorderStyle = FormBorderStyle.SizableToolWindow;
            this.TopMost = true;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "引导页设计器";
            this.ShowInTaskbar = true;
            this.Size = new Size(700, 500);

            // 创建并初始化界面控件
            CreateDesignerUI();

            // 绑定事件处理器
            BindEventHandlers();
        }

        /// <summary>
        /// 为指定窗体创建引导设计器
        /// </summary>
        /// <param name="targetForm">目标窗体</param>
        /// <param name="guide">现有引导实体（可选）</param>
        public static void CreateForForm(Form targetForm, GuideEntity guide = null)
        {
            GuideDesigner designer = new GuideDesigner();
            designer.AttachToForm(targetForm, guide);
            designer.Show();
        }

        /// <summary>
        /// 附加到目标窗体
        /// </summary>
        /// <param name="targetForm">目标窗体</param>
        /// <param name="guide">现有引导实体（可选）</param>
        public void AttachToForm(Form targetForm, GuideEntity guide = null)
        {
            _targetForm = targetForm;

            // 如果没有提供引导实体，则创建新的
            if (guide == null)
            {
                _guideEntity = new GuideEntity();
                _guideEntity.Title = targetForm.Text + " 引导";
                _guideEntity.ShowSummary = true;
                _guideEntity.BaseSize = targetForm.ClientSize;
            }
            else
            {
                _guideEntity = guide;
            }

            // 创建覆盖层
            CreateOverlayForm();

            // 更新UI
            UpdateStepsList();
            UpdateStepEditor();
        }

        #endregion

        #region UI创建和事件绑定

        /// <summary>
        /// 创建设计器UI
        /// </summary>
        private void CreateDesignerUI()
        {
            // 步骤列表区域
            lstSteps = new ListBox();
            lstSteps.Dock = DockStyle.Fill;
            lstSteps.DisplayMember = "Title";

            // 步骤控制按钮
            btnNewStep = new Button();
            btnNewStep.Text = "新增";
            btnNewStep.Width = 60;

            btnDeleteStep = new Button();
            btnDeleteStep.Text = "删除";
            btnDeleteStep.Width = 60;

            btnCopyStep = new Button();
            btnCopyStep.Text = "复制";
            btnCopyStep.Width = 60;

            btnMoveUp = new Button();
            btnMoveUp.Text = "上移";
            btnMoveUp.Width = 60;

            btnMoveDown = new Button();
            btnMoveDown.Text = "下移";
            btnMoveDown.Width = 60;

            // 步骤控制按钮面板
            pnlStepButtons = new FlowLayoutPanel();
            pnlStepButtons.Dock = DockStyle.Top;
            pnlStepButtons.Height = 35;
            pnlStepButtons.Padding = new Padding(5);
            pnlStepButtons.Controls.AddRange(new Control[] {
                btnNewStep, btnDeleteStep, btnCopyStep, btnMoveUp, btnMoveDown
            });

            // 步骤列表面板
            pnlSteps = new Panel();
            pnlSteps.Dock = DockStyle.Left;
            pnlSteps.Width = 150;
            pnlSteps.Controls.Add(lstSteps);
            pnlSteps.Controls.Add(pnlStepButtons);

            // 步骤编辑区域 - 标题和描述
            lblTitle = new Label();
            lblTitle.Text = "标题:";
            lblTitle.AutoSize = true;

            txtTitle = new TextBox();
            txtTitle.Width = 400;

            lblDescription = new Label();
            lblDescription.Text = "描述:";
            lblDescription.AutoSize = true;

            txtDescription = new TextBox();
            txtDescription.Multiline = true;
            txtDescription.Height = 60;
            txtDescription.Width = 400;

            // 高亮方式选择
            lblHighlightType = new Label();
            lblHighlightType.Text = "高亮方式:";
            lblHighlightType.AutoSize = true;

            rdoControlCapture = new RadioButton();
            rdoControlCapture.Text = "控件选择";
            rdoControlCapture.AutoSize = true;

            rdoAreaSelect = new RadioButton();
            rdoAreaSelect.Text = "区域框选";
            rdoAreaSelect.AutoSize = true;

            rdoSmartCapture = new RadioButton();
            rdoSmartCapture.Text = "智能匹配";
            rdoSmartCapture.AutoSize = true;

            // 高亮方式面板
            pnlHighlightType = new FlowLayoutPanel();
            pnlHighlightType.FlowDirection = FlowDirection.LeftToRight;
            pnlHighlightType.Width = 400;
            pnlHighlightType.Height = 30;
            pnlHighlightType.Controls.AddRange(new Control[] {
                rdoControlCapture, rdoAreaSelect, rdoSmartCapture
            });

            // 面板位置选择
            lblPanelPosition = new Label();
            lblPanelPosition.Text = "面板位置:";
            lblPanelPosition.AutoSize = true;

            // 面板位置帮助按钮
            Button btnPanelPositionHelp = new Button();
            btnPanelPositionHelp.Text = "?";
            btnPanelPositionHelp.Size = new Size(20, 20);
            btnPanelPositionHelp.Font = new Font(btnPanelPositionHelp.Font, FontStyle.Bold);
            btnPanelPositionHelp.Click += (s, e) => MessageBox.Show(
                "面板位置：指引导提示文本框相对于高亮区域的显示位置\n\n" +
                "- 自动：根据空间自动选择最合适的位置\n" +
                "- 上方：提示文本显示在高亮区域上方\n" +
                "- 下方：提示文本显示在高亮区域下方\n" +
                "- 左侧：提示文本显示在高亮区域左侧\n" +
                "- 右侧：提示文本显示在高亮区域右侧\n\n" +
                "注意：面板位置与锚点/组合不同，锚点用于控制高亮区域如何定位，而面板位置控制的是说明文字框的位置。",
                "面板位置说明", MessageBoxButtons.OK, MessageBoxIcon.Information);

            // 将帮助按钮添加到主要说明标签旁边
            FlowLayoutPanel pnlPanelPositionTitle = new FlowLayoutPanel();
            pnlPanelPositionTitle.FlowDirection = FlowDirection.LeftToRight;
            pnlPanelPositionTitle.AutoSize = true;
            pnlPanelPositionTitle.Controls.Add(lblPanelPosition);
            pnlPanelPositionTitle.Controls.Add(btnPanelPositionHelp);

            rdoPositionAuto = new RadioButton();
            rdoPositionAuto.Text = "自动";
            rdoPositionAuto.AutoSize = true;
            rdoPositionAuto.Checked = true;

            rdoPositionTop = new RadioButton();
            rdoPositionTop.Text = "上方";
            rdoPositionTop.AutoSize = true;

            rdoPositionBottom = new RadioButton();
            rdoPositionBottom.Text = "下方";
            rdoPositionBottom.AutoSize = true;

            rdoPositionLeft = new RadioButton();
            rdoPositionLeft.Text = "左侧";
            rdoPositionLeft.AutoSize = true;

            rdoPositionRight = new RadioButton();
            rdoPositionRight.Text = "右侧";
            rdoPositionRight.AutoSize = true;

            // 面板位置选择面板
            pnlPanelPosition = new FlowLayoutPanel();
            pnlPanelPosition.FlowDirection = FlowDirection.LeftToRight;
            pnlPanelPosition.Width = 400;
            pnlPanelPosition.Height = 30;
            pnlPanelPosition.Controls.AddRange(new Control[] {
                rdoPositionAuto, rdoPositionTop, rdoPositionBottom, rdoPositionLeft, rdoPositionRight
            });

            // 步骤编辑区域布局
            tblStepEditor = new TableLayoutPanel();
            tblStepEditor.Dock = DockStyle.Fill;
            tblStepEditor.ColumnCount = 2;
            tblStepEditor.RowCount = 7; // 增加一行用于Exec脚本区域
            tblStepEditor.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
            tblStepEditor.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));
            for (int i = 0; i < tblStepEditor.RowCount; i++)
            {
                tblStepEditor.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }

            // 添加Exec执行脚本区域
            lblExec = new Label();
            lblExec.Text = "执行脚本 (设计前先执行)";
            lblExec.AutoSize = true;
            lblExec.Margin = new Padding(3, 10, 3, 0);

            txtExec = new TextBox();
            txtExec.Multiline = true;
            txtExec.ScrollBars = ScrollBars.Vertical;
            txtExec.Height = 80;
            txtExec.Dock = DockStyle.Fill;

            btnExecRun = new Button();
            btnExecRun.Text = "执行脚本";
            btnExecRun.Width = 80;
            btnExecRun.Height = 30;
            btnExecRun.BackColor = System.Drawing.Color.FromArgb(0, 120, 215);
            btnExecRun.ForeColor = System.Drawing.Color.White;
            btnExecRun.FlatStyle = FlatStyle.Flat;

            // 初始化捕获按钮
            btnCaptureControl = new Button();
            btnCaptureControl.Text = "捕获控件";
            btnCaptureControl.Width = 150;
            btnCaptureControl.Height = 35;
            btnCaptureControl.BackColor = System.Drawing.Color.FromArgb(0, 120, 215);
            btnCaptureControl.ForeColor = System.Drawing.Color.White;
            btnCaptureControl.FlatStyle = FlatStyle.Flat;
            btnCaptureControl.Dock = DockStyle.None;
            btnCaptureControl.Anchor = AnchorStyles.None;

            // 添加脚本执行面板
            FlowLayoutPanel pnlExecLabel = new FlowLayoutPanel();
            pnlExecLabel.FlowDirection = FlowDirection.LeftToRight;
            pnlExecLabel.AutoSize = true;
            pnlExecLabel.Controls.Add(lblExec);

            Panel pnlExecTextBox = new Panel();
            pnlExecTextBox.Padding = new Padding(0, 0, 0, 5);
            pnlExecTextBox.Controls.Add(txtExec);
            pnlExecTextBox.Dock = DockStyle.Fill;

            FlowLayoutPanel pnlExecButton = new FlowLayoutPanel();
            pnlExecButton.FlowDirection = FlowDirection.LeftToRight;
            pnlExecButton.AutoSize = true;
            pnlExecButton.Controls.Add(btnExecRun);
            pnlExecButton.Dock = DockStyle.Fill;

            // 添加到表格布局
            tblStepEditor.Controls.Add(pnlExecLabel, 0, 0);
            tblStepEditor.Controls.Add(pnlExecTextBox, 1, 0);
            tblStepEditor.Controls.Add(pnlExecButton, 0, 1);

            // 标题
            tblStepEditor.Controls.Add(lblTitle, 0, 2);
            tblStepEditor.Controls.Add(txtTitle, 1, 2);

            // 描述
            tblStepEditor.Controls.Add(lblDescription, 0, 3);
            tblStepEditor.Controls.Add(txtDescription, 1, 3);

            // 高亮类型
            tblStepEditor.Controls.Add(lblHighlightType, 0, 4);
            tblStepEditor.Controls.Add(pnlHighlightType, 1, 4);

            // 面板位置
            tblStepEditor.Controls.Add(pnlPanelPositionTitle, 0, 5);
            tblStepEditor.Controls.Add(pnlPanelPosition, 1, 5);

            // 捕获按钮 - 居中显示
            FlowLayoutPanel pnlCaptureButton = new FlowLayoutPanel();
            pnlCaptureButton.FlowDirection = FlowDirection.LeftToRight;
            pnlCaptureButton.AutoSize = true;
            pnlCaptureButton.Dock = DockStyle.Fill;
            pnlCaptureButton.Padding = new Padding(0, 10, 0, 0);
            pnlCaptureButton.Controls.Add(btnCaptureControl);

            tblStepEditor.Controls.Add(pnlCaptureButton, 0, 6);
            tblStepEditor.SetColumnSpan(pnlCaptureButton, 2);

            // 主要功能按钮
            btnTestPreview = new Button();
            btnTestPreview.Text = "测试预览";
            btnTestPreview.Width = 100;

            btnSaveGuide = new Button();
            btnSaveGuide.Text = "保存引导";
            btnSaveGuide.Width = 100;

            btnApplyToApp = new Button();
            btnApplyToApp.Text = "应用到程序";
            btnApplyToApp.Width = 100;

            // 底部按钮面板
            pnlBottomButtons = new FlowLayoutPanel();
            pnlBottomButtons.Dock = DockStyle.Bottom;
            pnlBottomButtons.Height = 45;
            pnlBottomButtons.Padding = new Padding(5);
            pnlBottomButtons.Controls.AddRange(new Control[] {
                btnTestPreview, btnSaveGuide, btnApplyToApp
            });

            // 添加到主窗体
            this.Controls.Add(tblStepEditor);
            this.Controls.Add(pnlSteps);
            this.Controls.Add(pnlBottomButtons);
        }

        /// <summary>
        /// 绑定事件处理器
        /// </summary>
        private void BindEventHandlers()
        {
            // 步骤列表事件
            lstSteps.SelectedIndexChanged += LstSteps_SelectedIndexChanged;

            // 步骤控制按钮事件
            btnNewStep.Click += BtnNewStep_Click;
            btnDeleteStep.Click += BtnDeleteStep_Click;
            btnCopyStep.Click += BtnCopyStep_Click;
            btnMoveUp.Click += BtnMoveUp_Click;
            btnMoveDown.Click += BtnMoveDown_Click;

            // 步骤编辑器事件
            txtTitle.TextChanged += TxtTitle_TextChanged;
            txtDescription.TextChanged += TxtDescription_TextChanged;

            // 高亮方式事件
            rdoControlCapture.CheckedChanged += RdoHighlightType_CheckedChanged;
            rdoAreaSelect.CheckedChanged += RdoHighlightType_CheckedChanged;
            rdoSmartCapture.CheckedChanged += RdoHighlightType_CheckedChanged;

            // 面板位置事件
            rdoPositionAuto.CheckedChanged += RdoPanelPosition_CheckedChanged;
            rdoPositionTop.CheckedChanged += RdoPanelPosition_CheckedChanged;
            rdoPositionBottom.CheckedChanged += RdoPanelPosition_CheckedChanged;
            rdoPositionLeft.CheckedChanged += RdoPanelPosition_CheckedChanged;
            rdoPositionRight.CheckedChanged += RdoPanelPosition_CheckedChanged;

            // 功能按钮事件
            btnCaptureControl.Click += BtnCaptureControl_Click;
            btnTestPreview.Click += BtnTestPreview_Click;
            btnSaveGuide.Click += BtnSaveGuide_Click;
            btnApplyToApp.Click += BtnApplyToApp_Click;

            // 脚本执行事件
            btnExecRun.Click += BtnExecRun_Click;
            txtExec.TextChanged += TxtExec_TextChanged;

            // 窗体事件
            this.FormClosing += GuideDesigner_FormClosing;
        }

        /// <summary>
        /// 创建覆盖窗体
        /// </summary>
        private void CreateOverlayForm()
        {
            if (_overlayForm != null && _overlayForm.IsDisposed)
            {
                _overlayForm = null;
            }
            if (_overlayForm == null)
            {
                _overlayForm = new OverlayForm(this, _targetForm);

                // 获取包含目标窗体的屏幕
                Screen targetScreen = Screen.FromControl(_targetForm);

                // 设置覆盖层在正确的屏幕上
                _overlayForm.StartPosition = FormStartPosition.Manual;
                _overlayForm.Location = new Point(
                    targetScreen.Bounds.X + _targetForm.Left,
                    targetScreen.Bounds.Y + _targetForm.Top
                );

                // 处理目标窗体移动和大小变化
                _targetForm.LocationChanged += (s, e) => UpdateOverlayPosition();
                _targetForm.SizeChanged += (s, e) => UpdateOverlayPosition();
            }
        }

        /// <summary>
        /// 更新覆盖层位置
        /// </summary>
        private void UpdateOverlayPosition()
        {
            if (_overlayForm != null && _targetForm != null)
            {
                try
                {
                    // 获取目标窗体的屏幕位置
                    Screen targetScreen = Screen.FromControl(_targetForm);

                    // 更新覆盖层位置和大小
                    _overlayForm.Location = new Point(
                        targetScreen.Bounds.X + _targetForm.Left,
                        targetScreen.Bounds.Y + _targetForm.Top
                    );
                    _overlayForm.Size = _targetForm.Size;
                }
                catch (Exception ex)
                {
                    Console.WriteLine("更新覆盖层位置失败: " + ex.Message);
                }
            }
        }

        #endregion

        #region 事件处理器

        private void LstSteps_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (lstSteps.SelectedIndex >= 0 && lstSteps.SelectedIndex < _guideEntity.Items.Count)
            {
                _currentStepIndex = lstSteps.SelectedIndex;
                _currentStep = _guideEntity.Items[_currentStepIndex];
                UpdateStepEditor();
            }
        }

        private void BtnNewStep_Click(object sender, EventArgs e)
        {
            GuideItem newStep = new GuideItem();
            newStep.Title = "新步骤 " + (_guideEntity.Items.Count + 1);
            newStep.Type = GuideItem.HighlightType.SmartArea;

            _guideEntity.Items.Add(newStep);
            _isModified = true;

            UpdateStepsList();
            lstSteps.SelectedIndex = _guideEntity.Items.Count - 1;
        }

        private void BtnDeleteStep_Click(object sender, EventArgs e)
        {
            if (_currentStepIndex >= 0 && _currentStepIndex < _guideEntity.Items.Count)
            {
                if (MessageBox.Show("确定要删除此步骤吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    _guideEntity.Items.RemoveAt(_currentStepIndex);
                    _isModified = true;

                    UpdateStepsList();
                    if (_guideEntity.Items.Count > 0)
                    {
                        lstSteps.SelectedIndex = Math.Min(_currentStepIndex, _guideEntity.Items.Count - 1);
                    }
                    else
                    {
                        _currentStep = null;
                        _currentStepIndex = -1;
                        UpdateStepEditor();
                    }
                }
            }
        }

        private void BtnCopyStep_Click(object sender, EventArgs e)
        {
            if (_currentStep != null)
            {
                // 创建深度复制
                GuideItem newStep = new GuideItem();
                newStep.Title = _currentStep.Title + " (复制)";
                newStep.Desc = _currentStep.Desc;
                newStep.Rect = _currentStep.Rect;
                newStep.Type = _currentStep.Type;
                newStep.Ctrl = _currentStep.Ctrl;

                if (_currentStep.EnhancedArea != null)
                {
                    newStep.EnhancedArea = CloneEnhancedArea(_currentStep.EnhancedArea);
                }

                _guideEntity.Items.Add(newStep);
                _isModified = true;

                UpdateStepsList();
                lstSteps.SelectedIndex = _guideEntity.Items.Count - 1;
            }
        }

        private void BtnMoveUp_Click(object sender, EventArgs e)
        {
            if (_currentStepIndex > 0)
            {
                GuideItem step = _guideEntity.Items[_currentStepIndex];
                _guideEntity.Items.RemoveAt(_currentStepIndex);
                _guideEntity.Items.Insert(_currentStepIndex - 1, step);
                _isModified = true;

                UpdateStepsList();
                lstSteps.SelectedIndex = _currentStepIndex - 1;
            }
        }

        private void BtnMoveDown_Click(object sender, EventArgs e)
        {
            if (_currentStepIndex >= 0 && _currentStepIndex < _guideEntity.Items.Count - 1)
            {
                GuideItem step = _guideEntity.Items[_currentStepIndex];
                _guideEntity.Items.RemoveAt(_currentStepIndex);
                _guideEntity.Items.Insert(_currentStepIndex + 1, step);
                _isModified = true;

                UpdateStepsList();
                lstSteps.SelectedIndex = _currentStepIndex + 1;
            }
        }

        private void TxtTitle_TextChanged(object sender, EventArgs e)
        {
            if (_currentStep != null)
            {
                string oldTitle = _currentStep.Title;
                string newTitle = txtTitle.Text;

                if (oldTitle != newTitle)
                {
                    _currentStep.Title = newTitle;
                    _isModified = true;

                    // 更新ListBox显示
                    int index = lstSteps.SelectedIndex;
                    if (index >= 0)
                    {
                        lstSteps.Items[index] = _currentStep;
                        lstSteps.Refresh();
                    }
                }
            }
        }

        private void TxtDescription_TextChanged(object sender, EventArgs e)
        {
            if (_currentStep != null)
            {
                string oldDesc = _currentStep.Desc;
                string newDesc = txtDescription.Text;

                if (oldDesc != newDesc)
                {
                    _currentStep.Desc = newDesc;
                    _isModified = true;
                }
            }
        }

        private void RdoHighlightType_CheckedChanged(object sender, EventArgs e)
        {
            if (_currentStep == null) return;

            if (rdoControlCapture.Checked)
            {
                _designMode = DesignMode.ControlCapture;
                _currentStep.Type = GuideItem.HighlightType.Control;
            }
            else if (rdoAreaSelect.Checked)
            {
                _designMode = DesignMode.AreaSelect;
                _currentStep.Type = GuideItem.HighlightType.Default;
            }
            else if (rdoSmartCapture.Checked)
            {
                _designMode = DesignMode.SmartCapture;
                _currentStep.Type = GuideItem.HighlightType.SmartArea;

                // 确保EnhancedArea已初始化
                if (_currentStep.EnhancedArea == null)
                {
                    _currentStep.EnhancedArea = new EnhancedSmartArea();
                    _currentStep.EnhancedArea.Type = EnhancedSmartArea.AreaType.AbsoluteArea;
                }
            }

            _isModified = true;
            UpdateCaptureButton();
        }

        private void RdoPanelPosition_CheckedChanged(object sender, EventArgs e)
        {
            // GuideItem类没有Position属性，所以我们将面板位置记录在设计器状态中
            // 在预览或保存时，可以通过其他方式使用此信息
            if (_currentStep != null)
            {
                _isModified = true;

                // 如果需要记录面板位置，可以使用标签或自定义数据
                // 例如：可以添加到_currentStep.Title中的前缀或后缀，或者使用_currentStep.Desc
                // 这里仅作为示例，实际实现可能需要其他方式
                // 例如: "[位置:上方]实际标题"
            }
        }

        private void BtnCaptureControl_Click(object sender, EventArgs e)
        {
            if (_currentStep == null)
            {
                MessageBox.Show("请先选择一个步骤！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 根据当前模式开始捕获
            switch (_designMode)
            {
                case DesignMode.ControlCapture:
                    StartControlCapture();
                    break;
                case DesignMode.AreaSelect:
                    StartAreaSelect();
                    break;
                case DesignMode.SmartCapture:
                    StartSmartCapture();
                    break;
                default:
                    MessageBox.Show("请先选择高亮方式！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    break;
            }
        }

        private void BtnTestPreview_Click(object sender, EventArgs e)
        {
            try
            {
                // 保存当前步骤
                UpdateCurrentStep();

                // 确保EnhancedArea数据已保存到当前步骤
                if (_currentStep != null && _overlayForm != null && !_overlayForm.IsDisposed)
                {
                    // 手动强制更新智能区域数据
                    _overlayForm.ApplyChangesToStep(_currentStep);
                }

                // 设置测试预览标志，确保立即显示最新的捕获数据
                if (_currentStep != null && _currentStep.EnhancedArea != null)
                {
                    _currentStep.EnhancedArea.AutoDetectChanges = true;

                    // 如果特殊控件路径包含"."，确保正确解析
                    if (!string.IsNullOrEmpty(_currentStep.Ctrl) && _currentStep.Ctrl.Contains("."))
                    {
                        // 不需要额外处理，EnhancedArea已经处理好了
                    }
                }

                // 在预览前完全释放OverlayForm对象，避免因ShowGuide方法的特性导致其被隐式释放
                if (_overlayForm != null && !_overlayForm.IsDisposed)
                {
                    _overlayForm.Close();
                    _overlayForm.Dispose();
                    _overlayForm = null;
                }

                // 创建临时预览实例
                GuideEntity previewGuide = new GuideEntity();
                previewGuide.Title = _guideEntity.Title;
                previewGuide.Desc = _guideEntity.Desc;
                previewGuide.Target = _guideEntity.Target;
                previewGuide.BaseSize = _guideEntity.BaseSize;
                previewGuide.ShowSummary = _guideEntity.ShowSummary;

                // 如果选中了步骤，只预览选中的步骤
                if (_currentStep != null)
                {
                    // 创建副本，避免直接修改原始数据
                    GuideItem previewItem = new GuideItem();
                    previewItem.Title = _currentStep.Title;
                    previewItem.Desc = _currentStep.Desc;
                    previewItem.Rect = _currentStep.Rect;
                    previewItem.Ctrl = _currentStep.Ctrl;
                    previewItem.Exec = _currentStep.Exec;
                    previewItem.Type = _currentStep.Type;

                    // 使用EnhancedArea
                    previewItem.EnhancedArea = _currentStep.EnhancedArea;

                    previewGuide.Items.Add(previewItem);

                    // 显示预览
                    CommonGuide.ShowGuide(_targetForm, previewGuide);
                }
                else
                {
                    MessageBox.Show("请先选择一个步骤进行预览", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("预览失败: " + ex.Message,
                    "预览错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSaveGuide_Click(object sender, EventArgs e)
        {
            try
            {
                SaveCurrentGuide();
            }
            catch (Exception ex)
            {
                MessageBox.Show("保存引导文件时发生错误:\n" + ex.Message,
                    "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnApplyToApp_Click(object sender, EventArgs e)
        {
            if (_guideEntity.Items.Count == 0)
            {
                MessageBox.Show("没有可应用的引导步骤！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // TODO: 实现代码生成和应用逻辑
            MessageBox.Show("此功能暂未实现，请使用保存功能后手动集成。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 处理窗体关闭事件
        /// </summary>
        private void GuideDesigner_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 添加静态标志，防止多次触发
            if (_isHandlingFormClosing)
                return;

            _isHandlingFormClosing = true;

            try
            {
                if (_isModified)
                {
                    DialogResult result = MessageBox.Show(
                        "有未保存的更改，是否保存？",
                        "保存确认",
                        MessageBoxButtons.YesNoCancel,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // 标记为非修改，避免SaveCurrentGuide内再次提示
                        bool wasModified = _isModified;
                        _isModified = false;

                        // 保存当前引导
                        bool saved = SaveCurrentGuide();

                        // 如果保存失败，恢复修改状态并取消关闭
                        if (!saved)
                        {
                            _isModified = wasModified;
                            e.Cancel = true;
                        }
                    }
                    else if (result == DialogResult.Cancel)
                    {
                        e.Cancel = true; // 取消关闭
                    }
                }

                // 清理资源
                if (!e.Cancel)
                {
                    if (_overlayForm != null && !_overlayForm.IsDisposed)
                    {
                        _overlayForm.Close();
                        _overlayForm = null;
                    }
                }
            }
            finally
            {
                _isHandlingFormClosing = false;
            }
        }

        /// <summary>
        /// 保存当前引导
        /// </summary>
        /// <returns>是否保存成功</returns>
        private bool SaveCurrentGuide()
        {
            try
            {
                // 保存当前步骤
                UpdateCurrentStep();

                // 显示保存对话框
                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "引导文件(*.guide;*.json)|*.guide;*.json|XML文件(*.xml)|*.xml|所有文件(*.*)|*.*";
                saveDialog.DefaultExt = ".guide";
                saveDialog.Title = "保存引导文件";

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    // 保存到文件
                    if (GuideDesignerHelper.SaveGuideToFile(_guideEntity, saveDialog.FileName))
                    {
                        MessageBox.Show("引导已成功保存到:\n" + saveDialog.FileName,
                            "保存成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        _isModified = false;
                        return true;
                    }
                    else
                    {
                        MessageBox.Show("保存引导文件失败！", "保存失败",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("保存引导文件时发生错误:\n" + ex.Message,
                    "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            return false;
        }

        /// <summary>
        /// 更新捕获按钮状态
        /// </summary>
        private void UpdateCaptureButton()
        {
            if (_currentStep == null)
            {
                btnCaptureControl.Enabled = false;
                btnCaptureControl.Text = "捕获控件";
                return;
            }

            btnCaptureControl.Enabled = true;

            switch (_designMode)
            {
                case DesignMode.ControlCapture:
                    btnCaptureControl.Text = "捕获控件";
                    break;
                case DesignMode.AreaSelect:
                    btnCaptureControl.Text = "选择区域";
                    break;
                case DesignMode.SmartCapture:
                    btnCaptureControl.Text = "智能捕获";
                    break;
                default:
                    btnCaptureControl.Text = "捕获";
                    break;
            }
        }

        /// <summary>
        /// 开始控件捕获
        /// </summary>
        private void StartControlCapture()
        {
            if (_currentStep == null) return;

            _designMode = DesignMode.ControlCapture;
            UpdateCaptureButton();

            // 执行脚本内容
            if (!string.IsNullOrEmpty(_currentStep.Exec))
            {
                try
                {
                    ExecuteScript(_currentStep.Exec);
                }
                catch (Exception ex)
                {
                    MessageBox.Show("执行脚本失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
            }

            // 创建覆盖层窗体（如果不存在或已释放）
            if (_overlayForm == null || _overlayForm.IsDisposed)
            {
                CreateOverlayForm();
            }

            // 更新覆盖层位置
            UpdateOverlayPosition();

            // 开始控件捕获
            _overlayForm.StartControlCapture(_currentStep);
        }

        /// <summary>
        /// 开始区域选择
        /// </summary>
        private void StartAreaSelect()
        {
            if (_currentStep == null) return;

            _designMode = DesignMode.AreaSelect;
            UpdateCaptureButton();

            // 执行脚本内容
            if (!string.IsNullOrEmpty(_currentStep.Exec))
            {
                try
                {
                    ExecuteScript(_currentStep.Exec);
                }
                catch (Exception ex)
                {
                    MessageBox.Show("执行脚本失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
            }

            // 创建覆盖层窗体（如果不存在或已释放）
            if (_overlayForm == null || _overlayForm.IsDisposed)
            {
                CreateOverlayForm();
            }

            // 更新覆盖层位置
            UpdateOverlayPosition();

            // 开始区域选择
            _overlayForm.StartAreaSelect(_currentStep);
        }

        /// <summary>
        /// 开始智能捕获
        /// </summary>
        private void StartSmartCapture()
        {
            if (_currentStep == null) return;

            _designMode = DesignMode.SmartCapture;
            UpdateCaptureButton();

            // 执行脚本内容
            if (!string.IsNullOrEmpty(_currentStep.Exec))
            {
                try
                {
                    ExecuteScript(_currentStep.Exec);
                }
                catch (Exception ex)
                {
                    MessageBox.Show("执行脚本失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
            }

            // 创建覆盖层窗体（如果不存在或已释放）
            if (_overlayForm == null || _overlayForm.IsDisposed)
            {
                CreateOverlayForm();
            }

            // 更新覆盖层位置
            UpdateOverlayPosition();

            // 开始智能捕获
            _overlayForm.StartSmartCapture(_currentStep);
        }

        /// <summary>
        /// 执行脚本内容
        /// </summary>
        private void BtnExecRun_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtExec.Text))
            {
                MessageBox.Show("请输入要执行的脚本内容", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // 直接执行脚本内容
                ExecuteScript(txtExec.Text);

                // 执行成功后更新当前步骤的Exec字段
                if (_currentStep != null)
                {
                    _currentStep.Exec = txtExec.Text;
                    _isModified = true;
                }

                MessageBox.Show("脚本执行成功", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // 更新覆盖层位置
                UpdateOverlayPosition();
            }
            catch (Exception ex)
            {
                MessageBox.Show("脚本执行失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 脚本内容变更
        /// </summary>
        private void TxtExec_TextChanged(object sender, EventArgs e)
        {
            if (_currentStep != null)
            {
                _currentStep.Exec = txtExec.Text;
                _isModified = true;
            }
        }

        /// <summary>
        /// 执行脚本内容
        /// </summary>
        private void ExecuteScript(string script)
        {
            if (string.IsNullOrEmpty(script) || _targetForm == null)
                return;

            try
            {
                // 简单的脚本执行方式，可以根据实际需要扩展
                // 这里使用的是直接C#代码执行方式

                // 例如: 点击某个按钮
                // _targetForm.Controls["button1"].PerformClick();

                // 或者: 选择某个选项卡
                // ((TabControl)_targetForm.Controls["tabControl1"]).SelectedIndex = 2;

                // 这里我们使用动态执行方式
                System.Reflection.Assembly assembly = System.Reflection.Assembly.GetEntryAssembly();
                Type scriptType = assembly.GetType("OCRTools.Common.ScriptExecutor");

                if (scriptType != null)
                {
                    // 使用反射调用静态方法
                    object result = scriptType.InvokeMember("Execute",
                        System.Reflection.BindingFlags.InvokeMethod | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.Public,
                        null, null, new object[] { script, _targetForm });
                }
                else
                {
                    // 简化版本：直接使用代码片段执行
                    // 注意：这种方式有安全风险，生产环境中应该加入更多安全检查
                    System.CodeDom.Compiler.CompilerParameters parameters = new System.CodeDom.Compiler.CompilerParameters();
                    parameters.GenerateInMemory = true;
                    parameters.ReferencedAssemblies.Add("System.dll");
                    parameters.ReferencedAssemblies.Add("System.Windows.Forms.dll");
                    parameters.ReferencedAssemblies.Add(assembly.Location);

                    // 创建脚本包装
                    string source = @"
                        using System;
                        using System.Windows.Forms;
                        
                        namespace OCRTools.ScriptExecution
                        {
                            public class DynamicScript
                            {
                                public static void Execute(Form form)
                                {
                                    " + script + @"
                                }
                            }
                        }";

                    // 编译并执行
                    System.CodeDom.Compiler.CodeDomProvider provider = System.CodeDom.Compiler.CodeDomProvider.CreateProvider("CSharp");
                    System.CodeDom.Compiler.CompilerResults results = provider.CompileAssemblyFromSource(parameters, source);

                    if (results.Errors.HasErrors)
                    {
                        string errors = "";
                        foreach (System.CodeDom.Compiler.CompilerError error in results.Errors)
                        {
                            errors += error.ErrorText + "\r\n";
                        }
                        throw new Exception("脚本编译错误:\r\n" + errors);
                    }

                    // 执行脚本
                    Type type = results.CompiledAssembly.GetType("OCRTools.ScriptExecution.DynamicScript");
                    type.InvokeMember("Execute",
                        System.Reflection.BindingFlags.InvokeMethod | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.Public,
                        null, null, new object[] { _targetForm });
                }
            }
            catch (Exception ex)
            {
                throw new Exception("脚本执行异常: " + ex.Message, ex);
            }
        }

        /// <summary>
        /// 将当前UI控件中的数据更新到当前步骤对象
        /// </summary>
        private void UpdateCurrentStep()
        {
            if (_currentStep != null)
            {
                // 保存标题和描述
                _currentStep.Title = txtTitle.Text;
                _currentStep.Desc = txtDescription.Text;

                // 注意：GuideItem没有Position属性，所以这部分代码暂时注释掉
                // 如果需要处理面板位置，可能需要在其他地方实现
                /*
                if (rdoPositionAuto.Checked)
                    _currentStep.Position = GuideItem.PanelPosition.Auto;
                else if (rdoPositionTop.Checked)
                    _currentStep.Position = GuideItem.PanelPosition.Top;
                else if (rdoPositionBottom.Checked)
                    _currentStep.Position = GuideItem.PanelPosition.Bottom;
                else if (rdoPositionLeft.Checked)
                    _currentStep.Position = GuideItem.PanelPosition.Left;
                else if (rdoPositionRight.Checked)
                    _currentStep.Position = GuideItem.PanelPosition.Right;
                */

                // 保存高亮类型，使用正确的枚举值
                if (rdoControlCapture.Checked)
                    _currentStep.Type = GuideItem.HighlightType.Control;
                else if (rdoAreaSelect.Checked)
                    _currentStep.Type = GuideItem.HighlightType.Default;
                else if (rdoSmartCapture.Checked)
                    _currentStep.Type = GuideItem.HighlightType.SmartArea;
            }
        }

        /// <summary>
        /// 更新步骤列表
        /// </summary>
        private void UpdateStepsList()
        {
            lstSteps.BeginUpdate();
            lstSteps.Items.Clear();

            foreach (var step in _guideEntity.Items)
            {
                lstSteps.Items.Add(step);
            }

            lstSteps.EndUpdate();

            // 更新按钮状态
            UpdateButtonStates();
        }

        /// <summary>
        /// 更新按钮状态
        /// </summary>
        private void UpdateButtonStates()
        {
            bool hasSteps = _guideEntity.Items.Count > 0;
            bool hasSelection = _currentStepIndex >= 0;

            btnDeleteStep.Enabled = hasSelection;
            btnCopyStep.Enabled = hasSelection;
            btnMoveUp.Enabled = hasSelection && _currentStepIndex > 0;
            btnMoveDown.Enabled = hasSelection && _currentStepIndex < _guideEntity.Items.Count - 1;

            btnTestPreview.Enabled = hasSteps;
            btnSaveGuide.Enabled = hasSteps;
            btnApplyToApp.Enabled = hasSteps;
        }

        /// <summary>
        /// 更新步骤编辑器
        /// </summary>
        private void UpdateStepEditor()
        {
            if (_currentStep == null)
            {
                // 禁用所有编辑控件
                txtTitle.Enabled = false;
                txtDescription.Enabled = false;
                pnlHighlightType.Enabled = false;
                pnlPanelPosition.Enabled = false;
                btnCaptureControl.Enabled = false;
                txtExec.Enabled = false;
                btnExecRun.Enabled = false;

                // 清空内容
                txtTitle.Text = "";
                txtDescription.Text = "";
                txtExec.Text = "";

                // 默认选中自动定位
                rdoPositionAuto.Checked = true;

                return;
            }

            // 启用所有编辑控件
            txtTitle.Enabled = true;
            txtDescription.Enabled = true;
            pnlHighlightType.Enabled = true;
            pnlPanelPosition.Enabled = true;
            btnCaptureControl.Enabled = true;
            txtExec.Enabled = true;
            btnExecRun.Enabled = true;

            // 更新内容
            txtTitle.Text = _currentStep.Title ?? "";
            txtDescription.Text = _currentStep.Desc ?? "";
            txtExec.Text = _currentStep.Exec ?? "";

            // 根据高亮类型选中对应单选按钮
            switch (_currentStep.Type)
            {
                case GuideItem.HighlightType.Control:
                    rdoControlCapture.Checked = true;
                    break;

                case GuideItem.HighlightType.Default:
                    rdoAreaSelect.Checked = true;
                    break;

                case GuideItem.HighlightType.SmartArea:
                    rdoSmartCapture.Checked = true;
                    break;
            }

            UpdateCaptureButton();
        }

        /// <summary>
        /// 深度克隆EnhancedSmartArea对象
        /// </summary>
        private EnhancedSmartArea CloneEnhancedArea(EnhancedSmartArea original)
        {
            if (original == null) return null;

            EnhancedSmartArea clone = new EnhancedSmartArea();
            clone.Type = original.Type;
            clone.OriginalRect = original.OriginalRect;
            clone.RelativeRect = original.RelativeRect;
            clone.SizeType = original.SizeType;
            clone.PositionType = original.PositionType;
            clone.FixedSize = original.FixedSize;
            clone.RelativeSize = original.RelativeSize;
            clone.ReferencePoint = original.ReferencePoint;
            clone.Offset = original.Offset;
            clone.ParentRect = original.ParentRect;
            clone.AutoDetectChanges = true;

            // 深度复制集合
            clone.Anchors = new List<EnhancedSmartArea.ControlAnchor>(original.Anchors);
            clone.Components = new List<EnhancedSmartArea.AreaComponent>(original.Components);
            clone.NestedRects = new List<Rectangle>(original.NestedRects);

            return clone;
        }

        #endregion

        #region 控件声明

        private ListBox lstSteps;
        private Button btnNewStep;
        private Button btnDeleteStep;
        private Button btnCopyStep;
        private Button btnMoveUp;
        private Button btnMoveDown;
        private FlowLayoutPanel pnlStepButtons;
        private Panel pnlSteps;

        private Label lblTitle;
        private TextBox txtTitle;
        private Label lblDescription;
        private TextBox txtDescription;
        private Label lblHighlightType;
        private RadioButton rdoControlCapture;
        private RadioButton rdoAreaSelect;
        private RadioButton rdoSmartCapture;
        private FlowLayoutPanel pnlHighlightType;
        private Label lblPanelPosition;
        private RadioButton rdoPositionAuto;
        private RadioButton rdoPositionTop;
        private RadioButton rdoPositionBottom;
        private RadioButton rdoPositionLeft;
        private RadioButton rdoPositionRight;
        private FlowLayoutPanel pnlPanelPosition;
        private TableLayoutPanel tblStepEditor;

        private Button btnCaptureControl;
        private Button btnTestPreview;
        private Button btnSaveGuide;
        private Button btnApplyToApp;
        private FlowLayoutPanel pnlBottomButtons;

        // 脚本相关控件
        private Label lblExec;
        private TextBox txtExec;
        private Button btnExecRun;
        private FlowLayoutPanel pnlExec;

        #endregion
    }
}