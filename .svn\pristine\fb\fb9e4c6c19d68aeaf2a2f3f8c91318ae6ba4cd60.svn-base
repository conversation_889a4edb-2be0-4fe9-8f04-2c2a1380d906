// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class MultipleViewPattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = MultipleViewPatternIdentifiers.Pattern;


        private MultipleViewPattern(AutomationElement el, IUIAutomationMultipleViewPattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        public static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null
                ? null
                : new MultipleViewPattern(el, (IUIAutomationMultipleViewPattern)pattern, cached);
        }
    }
}