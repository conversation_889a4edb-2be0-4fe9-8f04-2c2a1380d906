// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Runtime.InteropServices;
using System.Windows.Automation.Text;

// Provider interfaces.
// IRawElementProviderSimple is defined in the interop DLL,
// since the Client code refers to it.  Everything else is here.

namespace System.Windows.Automation.Providers
{
    //[Guid("670c3006-bf4c-428b-8534-e1848f645122")]
    //[ComVisible(true)]
    //public enum NavigateDirection
    //{
    //    Parent,
    //    NextSibling,
    //    PreviousSibling,
    //    FirstChild,
    //    LastChild
    //}

    //[Guid("f7063da8-8359-439c-9297-bbc5299a7d87")]
    //[InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    //[ComVisible(true)]
    //public interface IRawElementProviderFragment : IRawElementProviderSimple
    //{
    //    IRawElementProviderFragment Navigate(NavigateDirection direction);
    //    int[] GetRuntimeId();
    //    Rect BoundingRectangle { get; }
    //    IRawElementProviderSimple[] GetEmbeddedFragmentRoots();
    //    void SetFocus();
    //    //IRawElementProviderFragmentRoot FragmentRoot { get; }
    //}

    //[ComVisible(true)]
    //[Guid("620ce2a5-ab8f-40a9-86cb-de3c75599b58")]
    //[InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    //public interface IRawElementProviderFragmentRoot : IRawElementProviderFragment, IRawElementProviderSimple
    //{
    //    IRawElementProviderFragment ElementProviderFromPoint(double x, double y);
    //    IRawElementProviderFragment GetFocus();
    //}

    [Guid("5347ad7b-c355-46f8-aff5-909033582f63")]
    [InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    [ComVisible(true)]
    public interface ITextRangeProvider
    {
        ITextRangeProvider Clone();

        [return: MarshalAs(UnmanagedType.Bool)]
        bool Compare(ITextRangeProvider range);

        int CompareEndpoints(TextPatternRangeEndpoint endpoint, ITextRangeProvider targetRange,
            TextPatternRangeEndpoint targetEndpoint);

        void ExpandToEnclosingUnit(TextUnit unit);
        ITextRangeProvider FindAttribute(int attribute, object value, [MarshalAs(UnmanagedType.Bool)] bool backward);

        ITextRangeProvider FindText(string text, [MarshalAs(UnmanagedType.Bool)] bool backward,
            [MarshalAs(UnmanagedType.Bool)] bool ignoreCase);

        object GetAttributeValue(int attribute);
        double[] GetBoundingRectangles();
        IRawElementProviderSimple GetEnclosingElement();
        string GetText(int maxLength);
        int Move(TextUnit unit, int count);
        int MoveEndpointByUnit(TextPatternRangeEndpoint endpoint, TextUnit unit, int count);

        void MoveEndpointByRange(TextPatternRangeEndpoint endpoint, ITextRangeProvider targetRange,
            TextPatternRangeEndpoint targetEndpoint);

        void Select();
        void AddToSelection();
        void RemoveFromSelection();
        void ScrollIntoView([MarshalAs(UnmanagedType.Bool)] bool alignToTop);
        IRawElementProviderSimple[] GetChildren();
    }
}