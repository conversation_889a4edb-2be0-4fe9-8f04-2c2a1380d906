﻿using OCRTools.Common;
using System;
using System.Collections.Specialized;
using System.IO;
using System.Net;

namespace ImageLib
{
    /// <summary>
    /// </summary>
    public class Net126Upload
    {
        public static bool Enable { get; set; } = true;

        private const string strFileNameSpilt = "\"data\":\"";

        public static string GetResult(byte[] content)
        {
            var result = "";
            var url = "https://dun.163.com/node/api/upload-image.json";
            var file = new UploadFileInfo()
            {
                Name = "image",
                Filename = "1.png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            var vaules = new NameValueCollection() {
                    { "name", "1.png" }
                };
            var html = string.Empty;
            try
            {
                html = UploadFileRequest.Post(url, new[] { file }, vaules);
            }
            catch { }
            if (html.Contains(strFileNameSpilt))
            {
                result = html.Substring(html.IndexOf(strFileNameSpilt) + strFileNameSpilt.Length);
                result = result.Substring(0, result.IndexOf("\"")).Replace("\\/", "/");
            }
            return result;
        }
    }
}
