// (c) Copyright Microsoft, 2012.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.


using System.Diagnostics;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public class TogglePattern : BasePattern
    {
        public static readonly AutomationPattern Pattern = TogglePatternIdentifiers.Pattern;
        public static readonly AutomationProperty ToggleStateProperty = TogglePatternIdentifiers.ToggleStateProperty;


        private TogglePattern(AutomationElement el, IUIAutomationTogglePattern pattern, bool cached)
            : base(el, cached)
        {
            Debug.Assert(pattern != null);
        }

        internal static object Wrap(AutomationElement el, object pattern, bool cached)
        {
            return pattern == null ? null : new TogglePattern(el, (IUIAutomationTogglePattern)pattern, cached);
        }
    }
}