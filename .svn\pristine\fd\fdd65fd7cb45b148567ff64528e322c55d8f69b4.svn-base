using System;
using System.Windows.Forms;

namespace OCRTools.Common.Hook
{
    /// <summary>
    ///     This class monitors all mouse activities globally (also outside of the application)
    ///     and provides appropriate events.
    /// </summary>
    public static partial class HookManager
    {
        //################################################################

        #region Mouse events

        ///// <summary>
        ///// Occurs when the mouse pointer is moved. 
        ///// </summary>
        //public static event MouseEventHandler MouseMove;

        ///// <summary>
        ///// Occurs when a click was performed by the mouse. 
        ///// </summary>
        //public static event MouseEventHandler MouseClick;

        ///// <summary>
        ///// Occurs when the mouse a mouse button is pressed. 
        ///// </summary>
        //public static event MouseEventHandler MouseDown;

        /// <summary>
        ///     Occurs when a mouse button is released.
        /// </summary>
        public static event MouseEventHandler MouseUp;

        /// <summary>
        ///     click+move+up
        /// </summary>
        public static event <PERSON><PERSON><PERSON><PERSON>and<PERSON> MouseSelected;

        ///// <summary>
        ///// Occurs when the mouse wheel moves. 
        ///// </summary>
        //public static event MouseEventHandler MouseWheel;

        private static event MouseEventHandler SMouseDoubleClick;

        //The double click event will not be provided directly from hook.
        //To fire the double click event wee need to monitor mouse up event and when it occures 
        //Two times during the time interval which is defined in Windows as a doble click time
        //we fire this event.

        /// <summary>
        ///     Occurs when a double clicked was performed by the mouse.
        /// </summary>
        public static event MouseEventHandler MouseDoubleClick
        {
            add
            {
                if (SMouseDoubleClick == null)
                {
                    //We create a timer to monitor interval between two clicks
                    _sDoubleClickTimer = new Timer
                    {
                        //This interval will be set to the value we retrive from windows. This is a windows setting from contro planel.
                        Interval = GetDoubleClickTime(),
                        //We do not start timer yet. It will be start when the click occures.
                        Enabled = false
                    };
                    //We define the callback function for the timer
                    _sDoubleClickTimer.Tick += DoubleClickTimeElapsed;
                    //We start to monitor mouse up event.
                    MouseUp += OnMouseUp;
                }

                SMouseDoubleClick += value;
            }
            remove
            {
                if (SMouseDoubleClick != null)
                {
                    SMouseDoubleClick -= value;
                    if (SMouseDoubleClick == null)
                    {
                        //Stop monitoring mouse up
                        MouseUp -= OnMouseUp;
                        //Dispose the timer
                        _sDoubleClickTimer.Tick -= DoubleClickTimeElapsed;
                        _sDoubleClickTimer = null;
                    }
                }
            }
        }

        //This field remembers mouse button pressed because in addition to the short interval it must be also the same button.
        private static MouseButtons _sPrevClickedButton;

        //The timer to monitor time interval between two clicks.
        private static Timer _sDoubleClickTimer;

        private static void DoubleClickTimeElapsed(object sender, EventArgs e)
        {
            //Timer is alapsed and no second click ocuured
            _sDoubleClickTimer.Enabled = false;
            _sPrevClickedButton = MouseButtons.None;
        }

        /// <summary>
        ///     This method is designed to monitor mouse clicks in order to fire a double click event if interval between
        ///     clicks was short enaugh.
        /// </summary>
        /// <param name="sender">Is always null</param>
        /// <param name="e">Some information about click heppened.</param>
        private static void OnMouseUp(object sender, MouseEventArgs e)
        {
            //This should not heppen
            if (e.Clicks < 1) return;
            //If the secon click heppened on the same button
            if (e.Button.Equals(_sPrevClickedButton))
            {
                Console.WriteLine("MouseDoubleClick");
                //Fire double click
                SMouseDoubleClick?.Invoke(null, e);
                //Stop timer
                _sDoubleClickTimer.Enabled = false;
                _sPrevClickedButton = MouseButtons.None;
            }
            else
            {
                //If it was the firts click start the timer
                _sDoubleClickTimer.Enabled = true;
                _sPrevClickedButton = e.Button;
            }
        }

        #endregion
    }
}