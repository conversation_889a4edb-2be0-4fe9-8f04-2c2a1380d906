﻿using OCRTools.Common;
using System;
using System.Collections.Specialized;
using System.IO;
using System.Net;

namespace ImageLib
{
    /// <summary>
    /// http://tools.yum6.cn/Tools/Images/
    /// </summary>
    public class _360ImageUpload
    {
        public static bool Enable { get; set; } = true;

        public static string GetResult(byte[] content)
        {
            var result = GetFromFourUomg(content);
            return result;
        }

        private const string strFileNameSpilt = "data-imgkey=\"";

        private static string GetFromFourUomg(byte[] content)
        {
            var result = "";
            try
            {
                var url = "https://st.so.com/stu";
                var file = new UploadFileInfo()
                {
                    Name = "upload",
                    Filename = "test.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                var vaules = new NameValueCollection() {
                    { "imgurl", "" } ,
                    { "base64image", "" } ,
                    { "submittype", "upload" } ,
                    { "src", "image" } ,
                    { "srcsp", "st_search" } ,
                };
                var html = string.Empty;
                try
                {
                    html = UploadFileRequest.Post(url, new[] { file }, vaules);
                }
                catch (BadApiException exception)
                {
                    switch (exception.Code)
                    {
                        case HttpStatusCode.MethodNotAllowed: //405
                        case HttpStatusCode.Unauthorized://401
                        case HttpStatusCode.NotFound: //404
                            Enable = false;
                            break;
                    }
                }
                catch { }
                if (html?.Contains(strFileNameSpilt) == true)
                {
                    //https://p1.ssl.qhimgs1.com/t01e785eaf21d406f8d.jpg
                    html = html.Substring(html.IndexOf(strFileNameSpilt) + strFileNameSpilt.Length);
                    html = html.Substring(0, html.IndexOf("\"")).Replace("\\/", "/");
                    if (!string.IsNullOrEmpty(html))
                        result = "https://p1.ssl.qhimgs1.com/" + html;
                }
            }
            catch (Exception oe)
            {

            }
            return result;
        }
    }
}
