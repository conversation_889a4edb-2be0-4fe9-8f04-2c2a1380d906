using ShareX.ScreenCaptureLib;
using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Drawing.Text;
using System.IO;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace OCRTools
{
    public static class ImageHelp
    {
        [DllImport("User32.dll")]
        private static extern IntPtr LoadCursorFromFile(string str);

        public static Cursor SetCursor(byte[] resourceName)
        {
            var folderPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var text = folderPath + "\\temp.cur";
            File.WriteAllBytes(text, resourceName);
            var result = new Cursor(LoadCursorFromFile(text));
            File.Delete(text);
            return result;
        }

        public static Image EditEllipse(Color color, int num)
        {
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var solidBrush = new SolidBrush(color))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                    switch (num)
                    {
                        case 1:
                            graphics.FillEllipseCenter(solidBrush, center, 4.DpiValue());
                            break;
                        case 2:
                            graphics.FillEllipseCenter(solidBrush, center, 6.DpiValue());
                            break;
                        case 3:
                            graphics.FillEllipseCenter(solidBrush, center, 8.DpiValue());
                            break;
                        case 4:
                            graphics.FillEllipseCenter(solidBrush, center, 10.DpiValue());
                            break;
                    }
                }
            }

            return bitmap;
        }
        public static void Pixelate(Bitmap bmp, int pixelSize)
        {
            if (pixelSize > 1)
            {
                using (UnsafeBitmap unsafeBitmap = new UnsafeBitmap(bmp, true))
                {
                    for (int y = 0; y < unsafeBitmap.Height; y += pixelSize)
                    {
                        for (int x = 0; x < unsafeBitmap.Width; x += pixelSize)
                        {
                            int xLimit = Math.Min(x + pixelSize, unsafeBitmap.Width);
                            int yLimit = Math.Min(y + pixelSize, unsafeBitmap.Height);
                            int pixelCount = (xLimit - x) * (yLimit - y);
                            float r = 0, g = 0, b = 0, a = 0;
                            float weightedCount = 0;

                            for (int y2 = y; y2 < yLimit; y2++)
                            {
                                for (int x2 = x; x2 < xLimit; x2++)
                                {
                                    ColorBgra color = unsafeBitmap.GetPixel(x2, y2);

                                    float pixelWeight = color.Alpha / 255f;

                                    r += color.Red * pixelWeight;
                                    g += color.Green * pixelWeight;
                                    b += color.Blue * pixelWeight;
                                    a += color.Alpha * pixelWeight;

                                    weightedCount += pixelWeight;
                                }
                            }

                            ColorBgra averageColor = new ColorBgra((byte)(b / weightedCount), (byte)(g / weightedCount), (byte)(r / weightedCount), (byte)(a / pixelCount));

                            for (int y2 = y; y2 < yLimit; y2++)
                            {
                                for (int x2 = x; x2 < xLimit; x2++)
                                {
                                    unsafeBitmap.SetPixel(x2, y2, averageColor);
                                }
                            }
                        }
                    }
                }
            }
        }


        public static void HighlightImage(Bitmap bmp)
        {
            HighlightImage(bmp, new Rectangle(0, 0, bmp.Width, bmp.Height));
        }

        public static void HighlightImage(Bitmap bmp, Color highlightColor)
        {
            HighlightImage(bmp, new Rectangle(0, 0, bmp.Width, bmp.Height), highlightColor);
        }

        public static void HighlightImage(Bitmap bmp, Rectangle rect)
        {
            HighlightImage(bmp, rect, Color.Yellow);
        }

        public static void HighlightImage(Bitmap bmp, Rectangle rect, Color highlightColor)
        {
            using (UnsafeBitmap unsafeBitmap = new UnsafeBitmap(bmp, true))
            {
                for (int y = rect.Y; y < rect.Height; y++)
                {
                    for (int x = rect.X; x < rect.Width; x++)
                    {
                        ColorBgra color = unsafeBitmap.GetPixel(x, y);
                        color.Red = Math.Min(color.Red, highlightColor.R);
                        color.Green = Math.Min(color.Green, highlightColor.G);
                        color.Blue = Math.Min(color.Blue, highlightColor.B);
                        unsafeBitmap.SetPixel(x, y, color);
                    }
                }
            }
        }


        public static void BoxBlur(Bitmap bmp, int range)
        {
            BoxBlur(bmp, range, new Rectangle(0, 0, bmp.Width, bmp.Height));
        }

        // https://lotsacode.wordpress.com/2010/12/08/fast-blur-box-blur-with-accumulator/
        public static void BoxBlur(Bitmap bmp, int range, Rectangle rect)
        {
            if (range > 1)
            {
                if (range.IsEvenNumber())
                {
                    range++;
                }

                using (UnsafeBitmap unsafeBitmap = new UnsafeBitmap(bmp, true))
                {
                    BoxBlurHorizontal(unsafeBitmap, range, rect);
                    BoxBlurVertical(unsafeBitmap, range, rect);
                    BoxBlurHorizontal(unsafeBitmap, range, rect);
                    BoxBlurVertical(unsafeBitmap, range, rect);
                }
            }
        }

        private static void BoxBlurHorizontal(UnsafeBitmap unsafeBitmap, int range, Rectangle rect)
        {
            int left = rect.X;
            int top = rect.Y;
            int right = rect.Right;
            int bottom = rect.Bottom;
            int halfRange = range / 2;
            ColorBgra[] newColors = new ColorBgra[unsafeBitmap.Width];

            for (int y = top; y < bottom; y++)
            {
                int hits = 0;
                int r = 0;
                int g = 0;
                int b = 0;
                int a = 0;

                for (int x = left - halfRange; x < right; x++)
                {
                    int oldPixel = x - halfRange - 1;
                    if (oldPixel >= left)
                    {
                        ColorBgra color = unsafeBitmap.GetPixel(oldPixel, y);

                        if (color.Bgra != 0)
                        {
                            r -= color.Red;
                            g -= color.Green;
                            b -= color.Blue;
                            a -= color.Alpha;
                        }

                        hits--;
                    }

                    int newPixel = x + halfRange;
                    if (newPixel < right)
                    {
                        ColorBgra color = unsafeBitmap.GetPixel(newPixel, y);

                        if (color.Bgra != 0)
                        {
                            r += color.Red;
                            g += color.Green;
                            b += color.Blue;
                            a += color.Alpha;
                        }

                        hits++;
                    }

                    if (x >= left)
                    {
                        newColors[x] = new ColorBgra((byte)(b / hits), (byte)(g / hits), (byte)(r / hits), (byte)(a / hits));
                    }
                }

                for (int x = left; x < right; x++)
                {
                    unsafeBitmap.SetPixel(x, y, newColors[x]);
                }
            }
        }

        private static void BoxBlurVertical(UnsafeBitmap unsafeBitmap, int range, Rectangle rect)
        {
            int left = rect.X;
            int top = rect.Y;
            int right = rect.Right;
            int bottom = rect.Bottom;
            int halfRange = range / 2;
            ColorBgra[] newColors = new ColorBgra[unsafeBitmap.Height];

            for (int x = left; x < right; x++)
            {
                int hits = 0;
                int r = 0;
                int g = 0;
                int b = 0;
                int a = 0;

                for (int y = top - halfRange; y < bottom; y++)
                {
                    int oldPixel = y - halfRange - 1;
                    if (oldPixel >= top)
                    {
                        ColorBgra color = unsafeBitmap.GetPixel(x, oldPixel);

                        if (color.Bgra != 0)
                        {
                            r -= color.Red;
                            g -= color.Green;
                            b -= color.Blue;
                            a -= color.Alpha;
                        }

                        hits--;
                    }

                    int newPixel = y + halfRange;
                    if (newPixel < bottom)
                    {
                        ColorBgra color = unsafeBitmap.GetPixel(x, newPixel);

                        if (color.Bgra != 0)
                        {
                            r += color.Red;
                            g += color.Green;
                            b += color.Blue;
                            a += color.Alpha;
                        }

                        hits++;
                    }

                    if (y >= top)
                    {
                        newColors[y] = new ColorBgra((byte)(b / hits), (byte)(g / hits), (byte)(r / hits), (byte)(a / hits));
                    }
                }

                for (int y = top; y < bottom; y++)
                {
                    unsafeBitmap.SetPixel(x, y, newColors[y]);
                }
            }
        }


        public static Image CreateStep(Color color)
        {
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                    var rect = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
                    graphics.DrawRectangle(pen, rect);
                    graphics.DrawStringCenter(pen, center, 6.DpiValue(), color);
                }
            }

            return bitmap;
        }

        public static InterpolationMode GetInterpolationMode(ImageInterpolationMode interpolationMode)
        {
            switch (interpolationMode)
            {
                default:
                case ImageInterpolationMode.HighQualityBicubic:
                    return InterpolationMode.HighQualityBicubic;
                case ImageInterpolationMode.Bicubic:
                    return InterpolationMode.Bicubic;
                case ImageInterpolationMode.HighQualityBilinear:
                    return InterpolationMode.HighQualityBilinear;
                case ImageInterpolationMode.Bilinear:
                    return InterpolationMode.Bilinear;
                case ImageInterpolationMode.NearestNeighbor:
                    return InterpolationMode.NearestNeighbor;
            }
        }

        public static Bitmap CreateBitmap(int width, int height, Color color)
        {
            if (width > 0 && height > 0)
            {
                Bitmap bmp = new Bitmap(width, height, PixelFormat.Format32bppArgb);

                using (Graphics g = Graphics.FromImage(bmp))
                {
                    g.Clear(color);
                }

                return bmp;
            }

            return null;
        }

        public static void DrawStringCenter(this Graphics graphics, Pen pen, Point center, int width, Color color)
        {
            var r = new Rectangle(center.X - width + 1.DpiValue(), center.Y - width, width * 2, width * 2);
            using (var format = new StringFormat
            {
                Alignment = StringAlignment.Center,
                LineAlignment = StringAlignment.Near
            })
            {
                using (var brush = new SolidBrush(color))
                {
                    graphics.TextRenderingHint = TextRenderingHint.AntiAlias;
                    graphics.DrawString("1", new Font("Times New Romans", 10f), brush, r, format);
                }
            }
        }

        public static void DrawTextCenter(this Graphics graphics, Pen pen, Point center, int width, Color color)
        {
            var r = new Rectangle(center.X - width + 1.DpiValue(), center.Y - width, width * 2, width * 2);
            using (var format = new StringFormat
            {
                Alignment = StringAlignment.Center,
                LineAlignment = StringAlignment.Near
            })
            {
                using (var brush = new SolidBrush(color))
                {
                    graphics.TextRenderingHint = TextRenderingHint.AntiAlias;
                    graphics.DrawString("T", new Font("Times New Romans", 10f), brush, r, format);
                }
            }
        }

        public static void FillEllipseCenter(this Graphics graphics, SolidBrush solidBrush1, Point center, int width)
        {
            var rect = new Rectangle(center.X - width, center.Y - width, width * 2, width * 2);
            graphics.FillEllipse(solidBrush1, rect);
        }

        public static void DrawRectangleCenter(this Graphics graphics, Pen pen, Point center, int width)
        {
            var rect = new Rectangle(center.X - width, center.Y - width, width * 2, width * 2);
            graphics.DrawRectangle(pen, rect);
        }

        public static void FillRectangleCenter(this Graphics graphics, SolidBrush solidBrush1, Point center, int width)
        {
            var rect = new Rectangle(center.X - width, center.Y - width, width * 2, width * 2);
            graphics.FillRectangle(solidBrush1, rect);
        }

        public static Image CreateEllipse(Color color, bool isdot = false, bool ischeck = false)
        {
            var rect = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var brush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
                {
                    using (var pen = new Pen(color, 1.DpiValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighQuality;
                        if (isdot)
                            pen.DashPattern = new[]
                            {
                                3f,
                                3f
                            };
                        if (ischeck) graphics.FillEllipse(brush, rect);
                        graphics.DrawEllipse(pen, rect);
                    }
                }
            }

            return bitmap;
        }

        public static Rectangle CreateRectangle(Point pos, Point pos2)
        {
            return CreateRectangle(pos.X, pos.Y, pos2.X, pos2.Y);
        }

        public static Rectangle CreateRectangle(int x, int y, int x2, int y2)
        {
            int width, height;

            if (x <= x2)
            {
                width = x2 - x + 1;
            }
            else
            {
                width = x - x2 + 1;
                x = x2;
            }

            if (y <= y2)
            {
                height = y2 - y + 1;
            }
            else
            {
                height = y - y2 + 1;
                y = y2;
            }

            return new Rectangle(x, y, width, height);
        }

        public static Image CreateRectangle(Color color, bool isdot = false, bool ischeck = false)
        {
            var rect = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var brush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
                {
                    using (var pen = new Pen(color, 1.DpiValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighSpeed;
                        if (isdot)
                            pen.DashPattern = new[]
                            {
                                3f,
                                3f
                            };
                        if (ischeck) graphics.FillRectangle(brush, rect);
                        graphics.DrawRectangle(pen, rect);
                    }
                }
            }

            return bitmap;
        }

        public static Point CalculateNewPosition(Point posOnClick, Point posCurrent, Size size)
        {
            if (posCurrent.X > posOnClick.X)
            {
                if (posCurrent.Y > posOnClick.Y)
                {
                    return new Point(posOnClick.X + size.Width - 1, posOnClick.Y + size.Height - 1);
                }
                else
                {
                    return new Point(posOnClick.X + size.Width - 1, posOnClick.Y - size.Height + 1);
                }
            }
            else
            {
                if (posCurrent.Y > posOnClick.Y)
                {
                    return new Point(posOnClick.X - size.Width + 1, posOnClick.Y + size.Height - 1);
                }
                else
                {
                    return new Point(posOnClick.X - size.Width + 1, posOnClick.Y - size.Height + 1);
                }
            }
        }

        public static Image CreateHighlight(Color color)
        {
            var rect = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var brush = new SolidBrush(Color.FromArgb(40, color)))
                {
                    using (var pen = new Pen(color, 1.DpiValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighSpeed;
                        graphics.FillRectangle(brush, rect);
                        graphics.DrawRectangle(pen, rect);
                        var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                        graphics.DrawTextCenter(pen, center, 6.DpiValue(), color);
                    }
                }
            }

            return bitmap;
        }

        public static Image CreateRectangleFill(Color color)
        {
            var rect = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var brush = new SolidBrush(Color.FromArgb(40, color)))
                {
                    using (var pen = new Pen(color, 1.DpiValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighSpeed;
                        graphics.FillRectangle(brush, rect);
                        graphics.DrawRectangle(pen, rect);
                    }
                }
            }

            return bitmap;
        }

        public static Image CreateRectangleImage(Color color, int width, int height, int left, int top)
        {
            var bitmap = new Bitmap(width.DpiValue(), height.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var brush = new SolidBrush(color))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    graphics.FillRectangle(brush,
                        new RectangleF(new Point(left, top),
                            new Size(bitmap.Width - 2 * left, bitmap.Height - 2 * top)));
                }
            }

            return bitmap;
        }

        public static Image CreateTextDot(bool ischeck = false)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                var color = Color.FromArgb(150, CustomColor.UnCheckColor);
                if (ischeck) color = CustomColor.CheckColor;
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    graphics.DrawPolygon(pen, new[]
                    {
                        new Point(rectangle.X, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y + 1.DpiValue() + 1.DpiValue()),
                        new Point(rectangle.X + rectangle.Width / 2 + 1.DpiValue(),
                            rectangle.Y + 1.DpiValue() + 1.DpiValue()),
                        new Point(rectangle.X + rectangle.Width / 2 + 1.DpiValue(), rectangle.Y + rectangle.Height),
                        new Point(rectangle.X + rectangle.Width / 2 - 1.DpiValue(), rectangle.Y + rectangle.Height),
                        new Point(rectangle.X + rectangle.Width / 2 - 1.DpiValue(),
                            rectangle.Y + 1.DpiValue() + 1.DpiValue()),
                        new Point(rectangle.X, rectangle.Y + 1.DpiValue() + 1.DpiValue()),
                        new Point(rectangle.X, rectangle.Y)
                    });
                }
            }

            return bitmap;
        }

        public static Bitmap DrawCheckers(int width, int height, int checkerSize, Color checkerColor1, Color checkerColor2)
        {
            Bitmap bmp = new Bitmap(width, height);

            using (Graphics g = Graphics.FromImage(bmp))
            using (Image checker = CreateCheckerPattern(checkerSize, checkerSize, checkerColor1, checkerColor2))
            using (Brush checkerBrush = new TextureBrush(checker, WrapMode.Tile))
            {
                g.FillRectangle(checkerBrush, new Rectangle(0, 0, bmp.Width, bmp.Height));
            }

            return bmp;
        }

        public static Image CreateCopy(Color color)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    var rect = new Rectangle(rectangle.X + rectangle.Width / 5, rectangle.Y + rectangle.Width / 5,
                        rectangle.Height * 4 / 5, rectangle.Height * 4 / 5);
                    graphics.DrawLines(pen, new[]
                    {
                        new Point(rectangle.X + rectangle.Width / 5, rectangle.Y + rectangle.Width * 4 / 5),
                        new Point(rectangle.X, rectangle.Y + rectangle.Width * 4 / 5),
                        new Point(rectangle.X, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width * 4 / 5, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width * 4 / 5, rectangle.Y + rectangle.Width / 5)
                    });
                    graphics.DrawRectangle(pen, rect);
                }
            }

            return bitmap;
        }

        public static Image CreatePaste(Color color)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    var rect = new Rectangle(rectangle.X + rectangle.Width / 5, rectangle.Y + rectangle.Width / 5,
                        rectangle.Height * 4 / 5, rectangle.Height * 4 / 5);
                    graphics.DrawLines(pen, new[]
                    {
                        new Point(rectangle.X + rectangle.Width / 5, rectangle.Y + rectangle.Width * 4 / 5),
                        new Point(rectangle.X, rectangle.Y + rectangle.Width * 4 / 5),
                        new Point(rectangle.X, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width * 4 / 5, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width * 4 / 5, rectangle.Y + rectangle.Width / 5)
                    });
                    graphics.DrawRectangle(pen, rect);
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.DrawLine(pen, new Point(rect.X + rect.Height / 4, rect.Y + rect.Height - rect.Height / 4),
                        new Point(rect.X + rect.Width - rect.Height / 4, rect.Y + rect.Height / 4));
                    var rectangle2 = new Rectangle(rect.X, rect.Y, rect.Width / 2, rect.Height / 2);
                    var rectangle3 = new Rectangle(rect.X + rect.Width / 2, rect.Y + rect.Height / 2, rect.Width / 2,
                        rect.Height / 2);
                    graphics.DrawLine(pen,
                        new Point(rectangle2.X + rectangle2.Height / 3,
                            rectangle2.Y + rectangle2.Height - rectangle2.Height / 3),
                        new Point(rectangle2.X + rectangle2.Width - rectangle2.Height / 3,
                            rectangle2.Y + rectangle2.Height / 3));
                    graphics.DrawLine(pen,
                        new Point(rectangle3.X + rectangle3.Height / 3,
                            rectangle3.Y + rectangle3.Height - rectangle3.Height / 3),
                        new Point(rectangle3.X + rectangle3.Width - rectangle3.Height / 3,
                            rectangle3.Y + rectangle3.Height / 3));
                }
            }

            return bitmap;
        }

        public static Image CreateMosaic(Color color)
        {
            var rect = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var brush = new SolidBrush(Color.FromArgb(100, color)))
                {
                    using (var brush2 = new SolidBrush(Color.FromArgb(40, color)))
                    {
                        using (var pen = new Pen(color, 1.DpiValue()))
                        {
                            graphics.SmoothingMode = SmoothingMode.HighSpeed;
                            graphics.FillRectangle(brush,
                                new Rectangle(rect.X, rect.Y, rect.Width / 2, rect.Height / 2));
                            graphics.FillRectangle(brush2,
                                new Rectangle(rect.X + rect.Width / 2, rect.Y + rect.Height / 2, rect.Width / 2,
                                    rect.Height / 2));
                            graphics.DrawRectangle(pen, rect);
                        }
                    }
                }
            }

            return bitmap;
        }

        public static Image CreateGaus(Color color)
        {
            var rect = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    graphics.DrawRectangle(pen, rect);
                    graphics.DrawLine(pen, new Point(rect.X + rect.Height / 4, rect.Y + rect.Height - rect.Height / 4),
                        new Point(rect.X + rect.Width - rect.Height / 4, rect.Y + rect.Height / 4));
                    var rectangle = new Rectangle(rect.X, rect.Y, rect.Width / 2, rect.Height / 2);
                    var rectangle2 = new Rectangle(rect.X + rect.Width / 2, rect.Y + rect.Height / 2, rect.Width / 2,
                        rect.Height / 2);
                    graphics.DrawLine(pen,
                        new Point(rectangle.X + rectangle.Height / 3,
                            rectangle.Y + rectangle.Height - rectangle.Height / 3),
                        new Point(rectangle.X + rectangle.Width - rectangle.Height / 3,
                            rectangle.Y + rectangle.Height / 3));
                    graphics.DrawLine(pen,
                        new Point(rectangle2.X + rectangle2.Height / 3,
                            rectangle2.Y + rectangle2.Height - rectangle2.Height / 3),
                        new Point(rectangle2.X + rectangle2.Width - rectangle2.Height / 3,
                            rectangle2.Y + rectangle2.Height / 3));
                }
            }

            return bitmap;
        }

        public static Image CreateUndo(Color color)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.DrawLine(pen, new Point(rectangle.X + 1, rectangle.Y + rectangle.Height / 2),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y + rectangle.Height / 2));
                    var rectangle2 = new Rectangle(rectangle.X, rectangle.Y, rectangle.Width / 2, rectangle.Height / 2);
                    graphics.DrawLine(pen, new Point(rectangle2.X, rectangle2.Y + rectangle2.Height),
                        new Point(rectangle2.X + rectangle2.Width - rectangle2.Height / 3,
                            rectangle2.Y + rectangle2.Height / 3));
                    graphics.DrawLine(pen, new Point(rectangle2.X, rectangle2.Y + rectangle2.Height),
                        new Point(rectangle2.X + rectangle2.Width - rectangle2.Height / 3,
                            rectangle2.Y + rectangle.Height - rectangle2.Height / 3));
                    //new Rectangle(rectangle.X, rectangle.Y + rectangle.Height / 2, rectangle.Width, rectangle.Height);
                }
            }

            return bitmap;
        }

        public static Image CreateRedo(Color color)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Height / 2),
                        new Point(rectangle.X + rectangle.Width - 2, rectangle.Y + rectangle.Height / 2));
                    var rectangle2 = new Rectangle(rectangle.X + rectangle.Width / 2, rectangle.Y, rectangle.Width / 2,
                        rectangle.Height / 2);
                    graphics.DrawLine(pen,
                        new Point(rectangle2.X + rectangle2.Height / 3, rectangle2.Y + rectangle2.Height / 3),
                        new Point(rectangle2.X + rectangle2.Width, rectangle2.Y + rectangle2.Width));
                    graphics.DrawLine(pen,
                        new Point(rectangle2.X + rectangle2.Height / 3,
                            rectangle.Y + rectangle.Height - rectangle2.Height / 3),
                        new Point(rectangle2.X + rectangle2.Width, rectangle2.Y + rectangle2.Width));
                    //new Rectangle(rectangle.X, rectangle.Y + rectangle.Height / 2, rectangle.Width, rectangle.Height);
                }
            }

            return bitmap;
        }

        public static Image CreateSave(Color color)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    var points = new[]
                    {
                        new Point(rectangle.X, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width * 3 / 4, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y + rectangle.Height / 4),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y + rectangle.Height),
                        new Point(rectangle.X, rectangle.Y + rectangle.Height),
                        new Point(rectangle.X, rectangle.Y)
                    };
                    graphics.DrawLines(pen, points);
                    graphics.DrawLine(pen,
                        new Point(rectangle.X + rectangle.Width / 4, rectangle.Y + rectangle.Height / 5),
                        new Point(rectangle.X + rectangle.Width * 3 / 4, rectangle.Y + rectangle.Height / 5));
                    var points2 = new[]
                    {
                        new Point(rectangle.X + rectangle.Width / 5, rectangle.Y + rectangle.Height),
                        new Point(rectangle.X + rectangle.Width / 5, rectangle.Y + rectangle.Height / 2),
                        new Point(rectangle.X + rectangle.Width * 4 / 5, rectangle.Y + rectangle.Height / 2),
                        new Point(rectangle.X + rectangle.Width * 4 / 5, rectangle.Y + rectangle.Height)
                    };
                    graphics.DrawLines(pen, points2);
                }
            }

            return bitmap;
        }

        public static Rectangle CalculateNewRectangle(Point posOnClick, Point posCurrent, Size size)
        {
            Point newPosition = CalculateNewPosition(posOnClick, posCurrent, size);
            return CreateRectangle(posOnClick, newPosition);
        }

        public static Image CreateClose(Color color)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.DrawLine(pen,
                        new Point(rectangle.X + rectangle.Height / 8, rectangle.Y + rectangle.Height / 8),
                        new Point(rectangle.X + rectangle.Width - rectangle.Height / 8,
                            rectangle.Y + rectangle.Height - rectangle.Height / 8));
                    graphics.DrawLine(pen,
                        new Point(rectangle.X + rectangle.Height / 8,
                            rectangle.Y + rectangle.Height - rectangle.Height / 8),
                        new Point(rectangle.X + rectangle.Width - rectangle.Height / 8,
                            rectangle.Y + rectangle.Height / 8));
                }
            }

            return bitmap;
        }

        public static Bitmap DrawGrip(Color color, Color shadow)
        {
            int size = 16;
            Bitmap bmp = new Bitmap(size, size);

            using (Graphics g = Graphics.FromImage(bmp))
            using (SolidBrush brush = new SolidBrush(color))
            using (SolidBrush shadowBrush = new SolidBrush(shadow))
            {
                int x = size / 2;
                int boxSize = 2;

                for (int i = 0; i < 4; i++)
                {
                    g.FillRectangle(shadowBrush, x - boxSize, (i * 4) + 2, boxSize, boxSize);
                    g.FillRectangle(brush, x - boxSize - 1, (i * 4) + 1, boxSize, boxSize);

                    g.FillRectangle(shadowBrush, x + 2, (i * 4) + 2, boxSize, boxSize);
                    g.FillRectangle(brush, x + 1, (i * 4) + 1, boxSize, boxSize);
                }
            }

            return bmp;
        }

        public static Image CreatePolygon(Color color, bool isdot = false, bool ischeck = false)
        {
            if (color == Color.FromArgb(120, 120, 120)) color = Color.FromArgb(165, color);
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen2 = new Pen(Color.White, 2.DpiValue()))
                {
                    using (var pen = new Pen(color, 2.DpiValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighQuality;
                        graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Width),
                            new Point(rectangle.X + rectangle.Width, rectangle.Y));
                        graphics.DrawLine(pen2, new Point(rectangle.X, rectangle.Y + rectangle.Height / 2),
                            new Point(rectangle.X + rectangle.Width,
                                rectangle.Y + rectangle.Height + rectangle.Height / 2));
                    }
                }

                return bitmap;
            }
        }

        public static Image CreateLine(Color color, bool isdot = false, bool ischeck = false)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen2 = new Pen(Color.FromArgb(20, 0, 0, 0), 6.DpiValue()))
                {
                    using (var pen = new Pen(color, 1.DpiValue()))
                    {
                        graphics.SmoothingMode = SmoothingMode.HighQuality;
                        if (isdot)
                            pen.DashPattern = new[]
                            {
                                3f,
                                3f
                            };
                        if (ischeck)
                            graphics.DrawLine(pen2, new Point(rectangle.X, rectangle.Y + rectangle.Height),
                                new Point(rectangle.X + rectangle.Width, rectangle.Y));
                        graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Height),
                            new Point(rectangle.X + rectangle.Width, rectangle.Y));
                    }
                }

                return bitmap;
            }
        }

        public static Image CreateArrow(Color color)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Height),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y));
                    graphics.DrawLine(pen, new Point(rectangle.X + rectangle.Width / 2, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y));
                    graphics.DrawLine(pen, new Point(rectangle.X + rectangle.Width, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y + rectangle.Height / 2));
                }
            }

            return bitmap;
        }

        public static Image CreateArrowBoth(bool ischeck)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            var color = Color.FromArgb(150, 120, 120, 120);
            if (ischeck) color = CustomColor.CheckColor;
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Height),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y));
                    graphics.DrawLine(pen, new Point(rectangle.X + rectangle.Width * 2 / 3, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y));
                    graphics.DrawLine(pen, new Point(rectangle.X + rectangle.Width, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y + rectangle.Height / 3));
                    graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y + rectangle.Height * 2 / 3),
                        new Point(rectangle.X, rectangle.Y + rectangle.Height));
                    graphics.DrawLine(pen, new Point(rectangle.X + rectangle.Width / 3, rectangle.Y + rectangle.Height),
                        new Point(rectangle.X, rectangle.Y + rectangle.Height));
                }
            }

            return bitmap;
        }

        public static Image CreateText(Color color)
        {
            var rectangle = new Rectangle(8.DpiValue(), 8.DpiValue(), 15.DpiValue(), 15.DpiValue());
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(color, 1.DpiValue()))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    graphics.DrawLine(pen, new Point(rectangle.X, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width, rectangle.Y));
                    graphics.DrawLine(pen, new Point(rectangle.X + rectangle.Width / 2, rectangle.Y),
                        new Point(rectangle.X + rectangle.Width / 2, rectangle.Y + rectangle.Height));
                }
            }

            return bitmap;
        }

        public static Image EditRectangle(Color color, bool ischeck = false)
        {
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var solidBrush2 = new SolidBrush(Color.White))
                {
                    using (var pen = new Pen(Color.Black))
                    {
                        using (var solidBrush = new SolidBrush(color))
                        {
                            graphics.SmoothingMode = SmoothingMode.HighSpeed;
                            var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                            graphics.FillRectangleCenter(solidBrush, center, 8.DpiValue());
                            graphics.DrawRectangleCenter(pen, center, 8.DpiValue());
                            if (ischeck)
                            {
                                graphics.FillRectangleCenter(solidBrush2, center, 4.DpiValue());
                                graphics.DrawRectangleCenter(pen, center, 4.DpiValue());
                            }
                        }
                    }
                }
            }

            return bitmap;
        }

        public static Image EditCross(bool ischeck = false)
        {
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                var color = Color.FromArgb(150, CustomColor.UnCheckColor);
                if (ischeck) color = CustomColor.CheckColor;
                using (var pen = new Pen(color))
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    var point = new Point(bitmap.Width / 2, bitmap.Height / 2);
                    var rectangle = new Rectangle(point.X - 8.DpiValue(), point.Y - 8.DpiValue(), 8.DpiValue() * 2,
                        8.DpiValue() * 2);
                    graphics.DrawPolygon(pen, new[]
                    {
                        new Point(point.X - 1.DpiValue(), rectangle.Y),
                        new Point(point.X + 1.DpiValue(), rectangle.Y),
                        new Point(point.X + 1.DpiValue(), point.Y - 1.DpiValue()),
                        new Point(rectangle.X + rectangle.Width, point.Y - 1.DpiValue()),
                        new Point(rectangle.X + rectangle.Width, point.Y + 1.DpiValue()),
                        new Point(point.X + 1.DpiValue(), point.Y + 1.DpiValue()),
                        new Point(point.X + 1.DpiValue(), rectangle.Y + rectangle.Height),
                        new Point(point.X - 1.DpiValue(), rectangle.Y + rectangle.Height),
                        new Point(point.X - 1.DpiValue(), point.Y + 1.DpiValue()),
                        new Point(rectangle.X, point.Y + 1.DpiValue()),
                        new Point(rectangle.X, point.Y - 1.DpiValue()),
                        new Point(point.X - 1.DpiValue(), point.Y - 1.DpiValue()),
                        new Point(point.X - 1.DpiValue(), rectangle.Y)
                    });
                }
            }

            return bitmap;
        }

        public static Image EditRectangleCus(Color color, bool ischeck = false)
        {
            var bitmap = new Bitmap(30.DpiValue(), 30.DpiValue());
            using (var graphics = Graphics.FromImage(bitmap))
            {
                using (var pen = new Pen(Color.Black))
                {
                    using (var brush = new SolidBrush(Color.FromArgb(100, color)))
                    {
                        using (var brush2 = new SolidBrush(Color.FromArgb(40, color)))
                        {
                            graphics.SmoothingMode = SmoothingMode.HighSpeed;
                            var center = new Point(bitmap.Width / 2, bitmap.Height / 2);
                            var rectangle = new Rectangle(center.X - 8.DpiValue(), center.Y - 8.DpiValue(),
                                8.DpiValue() * 2, 8.DpiValue() * 2);
                            graphics.FillRectangle(brush,
                                new Rectangle(rectangle.X, rectangle.Y, rectangle.Width / 2, rectangle.Height / 2));
                            graphics.FillRectangle(brush2,
                                new Rectangle(rectangle.X + rectangle.Width / 2, rectangle.Y + rectangle.Height / 2,
                                    rectangle.Width / 2, rectangle.Height / 2));
                            graphics.DrawRectangleCenter(pen, center, 8.DpiValue());
                        }
                    }
                }
            }

            return bitmap;
        }

        public static Bitmap CreateColorPickerIcon(Color color, Rectangle rect, int holeSize = 0)
        {
            Bitmap bmp = new Bitmap(rect.Width, rect.Height);

            using (Graphics g = Graphics.FromImage(bmp))
            {
                DrawColorPickerIcon(g, color, rect, holeSize);
            }

            return bmp;
        }

        public static Bitmap CreateCheckerPattern(int width, int height)
        {
            return CreateCheckerPattern(width, height, SystemColors.ControlLight, SystemColors.ControlLightLight);
        }

        public static Bitmap CreateCheckerPattern(int width, int height, Color checkerColor1, Color checkerColor2)
        {
            Bitmap bmp = new Bitmap(width * 2, height * 2);

            using (Graphics g = Graphics.FromImage(bmp))
            using (Brush brush1 = new SolidBrush(checkerColor1))
            using (Brush brush2 = new SolidBrush(checkerColor2))
            {
                g.FillRectangle(brush1, 0, 0, width, height);
                g.FillRectangle(brush1, width, height, width, height);
                g.FillRectangle(brush2, width, 0, width, height);
                g.FillRectangle(brush2, 0, height, width, height);
            }

            return bmp;
        }

        public static void DrawColorPickerIcon(Graphics g, Color color, Rectangle rect, int holeSize = 0)
        {
            if (color.IsTransparent())
            {
                using (Image checker = CreateCheckerPattern(rect.Width / 2, rect.Height / 2))
                {
                    g.DrawImage(checker, rect);
                }
            }

            using (SolidBrush brush = new SolidBrush(color))
            {
                g.FillRectangle(brush, rect);
            }

            g.DrawRectangleProper(Pens.Black, rect);

            if (holeSize > 0)
            {
                g.CompositingMode = CompositingMode.SourceCopy;

                Rectangle holeRect = new Rectangle((rect.Width / 2) - (holeSize / 2), (rect.Height / 2) - (holeSize / 2), holeSize, holeSize);

                g.FillRectangle(Brushes.Transparent, holeRect);
                g.DrawRectangleProper(Pens.Black, holeRect);
            }
        }

        public static Bitmap AddCanvas(Image img, Padding margin, Color canvasColor)
        {
            if (margin.All == 0 || img.Width + margin.Horizontal < 1 || img.Height + margin.Vertical < 1)
            {
                return null;
            }

            Bitmap bmp = img.CreateEmptyBitmap(margin.Horizontal, margin.Vertical);

            using (Graphics g = Graphics.FromImage(bmp))
            {
                g.SetHighQuality();
                g.DrawImage(img, margin.Left, margin.Top, img.Width, img.Height);

                if (canvasColor.A > 0)
                {
                    g.CompositingMode = CompositingMode.SourceCopy;
                    g.SmoothingMode = SmoothingMode.None;

                    using (Brush brush = new SolidBrush(canvasColor))
                    {
                        if (margin.Top > 0)
                        {
                            g.FillRectangle(brush, 0, 0, bmp.Width, margin.Top);
                        }

                        if (margin.Right > 0)
                        {
                            g.FillRectangle(brush, bmp.Width - margin.Right, 0, margin.Right, bmp.Height);
                        }

                        if (margin.Bottom > 0)
                        {
                            g.FillRectangle(brush, 0, bmp.Height - margin.Bottom, bmp.Width, margin.Bottom);
                        }

                        if (margin.Left > 0)
                        {
                            g.FillRectangle(brush, 0, 0, margin.Left, bmp.Height);
                        }
                    }
                }
            }

            return bmp;
        }

        public static Rectangle FindAutoCropRectangle(Bitmap bmp, bool sameColorCrop = false,
            AnchorStyles sides = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right)
        {
            Rectangle source = new Rectangle(0, 0, bmp.Width, bmp.Height);

            if (sides == AnchorStyles.None)
            {
                return source;
            }

            Rectangle crop = source;

            using (UnsafeBitmap unsafeBitmap = new UnsafeBitmap(bmp, true, ImageLockMode.ReadOnly))
            {
                bool leave = false;

                ColorBgra checkColor = unsafeBitmap.GetPixel(0, 0);
                uint mask = checkColor.Alpha == 0 ? 0xFF000000 : 0xFFFFFFFF;
                uint check = checkColor.Bgra & mask;

                if (sides.HasFlag(AnchorStyles.Left))
                {
                    // Find X (Left to right)
                    for (int x = 0; x < bmp.Width && !leave; x++)
                    {
                        for (int y = 0; y < bmp.Height; y++)
                        {
                            if ((unsafeBitmap.GetPixel(x, y).Bgra & mask) != check)
                            {
                                crop.X = x;
                                crop.Width -= x;
                                leave = true;
                                break;
                            }
                        }
                    }

                    // If all pixels same color
                    if (!leave)
                    {
                        return crop;
                    }

                    leave = false;
                }

                if (sides.HasFlag(AnchorStyles.Top))
                {
                    // Find Y (Top to bottom)
                    for (int y = 0; y < bmp.Height && !leave; y++)
                    {
                        for (int x = 0; x < bmp.Width; x++)
                        {
                            if ((unsafeBitmap.GetPixel(x, y).Bgra & mask) != check)
                            {
                                crop.Y = y;
                                crop.Height -= y;
                                leave = true;
                                break;
                            }
                        }
                    }

                    leave = false;
                }

                if (!sameColorCrop)
                {
                    checkColor = unsafeBitmap.GetPixel(bmp.Width - 1, bmp.Height - 1);
                    mask = checkColor.Alpha == 0 ? 0xFF000000 : 0xFFFFFFFF;
                    check = checkColor.Bgra & mask;
                }

                if (sides.HasFlag(AnchorStyles.Right))
                {
                    // Find Width (Right to left)
                    for (int x = bmp.Width - 1; x >= 0 && !leave; x--)
                    {
                        for (int y = 0; y < bmp.Height; y++)
                        {
                            if ((unsafeBitmap.GetPixel(x, y).Bgra & mask) != check)
                            {
                                crop.Width = x - crop.X + 1;
                                leave = true;
                                break;
                            }
                        }
                    }

                    leave = false;
                }

                if (sides.HasFlag(AnchorStyles.Bottom))
                {
                    // Find Height (Bottom to top)
                    for (int y = bmp.Height - 1; y >= 0 && !leave; y--)
                    {
                        for (int x = 0; x < bmp.Width; x++)
                        {
                            if ((unsafeBitmap.GetPixel(x, y).Bgra & mask) != check)
                            {
                                crop.Height = y - crop.Y + 1;
                                leave = true;
                                break;
                            }
                        }
                    }
                }
            }

            return crop;
        }
    }
}