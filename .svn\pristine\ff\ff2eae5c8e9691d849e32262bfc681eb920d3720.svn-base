﻿using MetroFramework.Components;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
/**
 * MetroFramework - Modern UI for WinForms
 * 
 * The MIT License (MIT)
 * Copyright (c) 2011 <PERSON>, http://github.com/viperneo
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of 
 * this software and associated documentation files (the "Software"), to deal in the 
 * Software without restriction, including without limitation the rights to use, copy, 
 * modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, 
 * and to permit persons to whom the Software is furnished to do so, subject to the 
 * following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in 
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
 * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT 
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF 
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE 
 * OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace MetroFramework.Controls
{
    [ToolboxBitmap(typeof(RadioButton))]
    [Designer(typeof(Design.MetroRadioButtonDesigner), typeof(System.Windows.Forms.Design.ParentControlDesigner))]
    public class MetroRadioButton : RadioButton, IMetroControl
    {
        private MetroColorStyle metroStyle;

        private MetroThemeStyle metroTheme;

        private MetroStyleManager metroStyleManager;

        private bool useCustomBackColor;

        private bool useCustomForeColor;

        private bool useStyleColors;

        private bool displayFocusRectangle;

        private MetroCheckBoxSize metroCheckBoxSize;

        private MetroCheckBoxWeight metroCheckBoxWeight = MetroCheckBoxWeight.Regular;

        private bool isHovered;

        private bool isPressed;

        private bool isFocused;

        [DefaultValue(MetroColorStyle.Blue)]
        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                if (base.DesignMode || metroStyle != 0)
                {
                    return metroStyle;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Style;
                }
                if (StyleManager == null)
                {
                    return MetroColorStyle.Blue;
                }
                return metroStyle;
            }
            set
            {
                metroStyle = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroThemeStyle.Light)]
        public MetroThemeStyle Theme
        {
            get
            {
                if (base.DesignMode || metroTheme != 0)
                {
                    return metroTheme;
                }
                if (StyleManager != null)
                {
                    return StyleManager.Theme;
                }
                if (StyleManager == null)
                {
                    return MetroThemeStyle.Light;
                }
                return metroTheme;
            }
            set
            {
                metroTheme = value;
            }
        }

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public MetroStyleManager StyleManager
        {
            get
            {
                return metroStyleManager;
            }
            set
            {
                metroStyleManager = value;
            }
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseCustomBackColor
        {
            get
            {
                return useCustomBackColor;
            }
            set
            {
                useCustomBackColor = value;
            }
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool UseCustomForeColor
        {
            get
            {
                return useCustomForeColor;
            }
            set
            {
                useCustomForeColor = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseStyleColors
        {
            get
            {
                return useStyleColors;
            }
            set
            {
                useStyleColors = value;
            }
        }

        [Browsable(false)]
        [DefaultValue(false)]
        [Category("Metro Behaviour")]
        public bool UseSelectable
        {
            get
            {
                return GetStyle(ControlStyles.Selectable);
            }
            set
            {
                SetStyle(ControlStyles.Selectable, value);
            }
        }

        [DefaultValue(false)]
        [Category("Metro Appearance")]
        public bool DisplayFocus
        {
            get
            {
                return displayFocusRectangle;
            }
            set
            {
                displayFocusRectangle = value;
            }
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroCheckBoxSize.Small)]
        public MetroCheckBoxSize FontSize
        {
            get
            {
                return metroCheckBoxSize;
            }
            set
            {
                metroCheckBoxSize = value;
            }
        }

        [DefaultValue(MetroCheckBoxWeight.Regular)]
        [Category("Metro Appearance")]
        public MetroCheckBoxWeight FontWeight
        {
            get
            {
                return metroCheckBoxWeight;
            }
            set
            {
                metroCheckBoxWeight = value;
            }
        }

        [Browsable(false)]
        public override Font Font
        {
            get
            {
                return base.Font;
            }
            set
            {
                base.Font = value;
            }
        }

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")]
        public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintBackground != null)
            {
                this.CustomPaintBackground(this, e);
            }
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaint != null)
            {
                this.CustomPaint(this, e);
            }
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && this.CustomPaintForeground != null)
            {
                this.CustomPaintForeground(this, e);
            }
        }

        public MetroRadioButton()
        {
            SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.AllPaintingInWmPaint | ControlStyles.OptimizedDoubleBuffer, value: true);
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            try
            {
                Color color = BackColor;
                if (!useCustomBackColor)
                {
                    color = MetroPaint.BackColor.Form(Theme);
                    if (base.Parent is MetroTile)
                    {
                        color = MetroPaint.GetStyleColor(Style);
                    }
                }
                if (color.A == byte.MaxValue)
                {
                    e.Graphics.Clear(color);
                }
                else
                {
                    base.OnPaintBackground(e);
                    OnCustomPaintBackground(new MetroPaintEventArgs(color, Color.Empty, e.Graphics));
                }
            }
            catch
            {
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            try
            {
                if (GetStyle(ControlStyles.AllPaintingInWmPaint))
                {
                    OnPaintBackground(e);
                }
                OnCustomPaint(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
                OnPaintForeground(e);
            }
            catch
            {
                Invalidate();
            }
        }

        protected virtual void OnPaintForeground(PaintEventArgs e)
        {
            Color foreColor;
            Color color;
            if (useCustomForeColor)
            {
                foreColor = ForeColor;
                color = ((isHovered && !isPressed && base.Enabled) ? MetroPaint.BorderColor.CheckBox.Hover(Theme) : ((isHovered && isPressed && base.Enabled) ? MetroPaint.BorderColor.CheckBox.Press(Theme) : (base.Enabled ? MetroPaint.BorderColor.CheckBox.Normal(Theme) : MetroPaint.BorderColor.CheckBox.Disabled(Theme))));
            }
            else if (isHovered && !isPressed && base.Enabled)
            {
                foreColor = MetroPaint.ForeColor.CheckBox.Hover(Theme);
                color = MetroPaint.BorderColor.CheckBox.Hover(Theme);
            }
            else if (isHovered && isPressed && base.Enabled)
            {
                foreColor = MetroPaint.ForeColor.CheckBox.Press(Theme);
                color = MetroPaint.BorderColor.CheckBox.Press(Theme);
            }
            else if (!base.Enabled)
            {
                foreColor = MetroPaint.ForeColor.CheckBox.Disabled(Theme);
                color = MetroPaint.BorderColor.CheckBox.Disabled(Theme);
            }
            else
            {
                foreColor = ((!useStyleColors) ? MetroPaint.ForeColor.CheckBox.Normal(Theme) : MetroPaint.GetStyleColor(Style));
                color = MetroPaint.BorderColor.CheckBox.Normal(Theme);
            }
            e.Graphics.SmoothingMode = SmoothingMode.HighQuality;
            using (Pen pen = new Pen(color))
            {
                Rectangle rect = new Rectangle(0, base.Height / 2 - 6, 12, 12);
                e.Graphics.DrawEllipse(pen, rect);
            }
            if (base.Checked)
            {
                Color styleColor = MetroPaint.GetStyleColor(Style);
                using (SolidBrush brush = new SolidBrush(styleColor))
                {
                    Rectangle rect2 = new Rectangle(3, base.Height / 2 - 3, 6, 6);
                    e.Graphics.FillEllipse(brush, rect2);
                }
            }
            e.Graphics.SmoothingMode = SmoothingMode.Default;
            TextRenderer.DrawText(bounds: new Rectangle(16, 0, base.Width - 16, base.Height), dc: e.Graphics, text: Text, font: MetroFonts.CheckBox(metroCheckBoxSize, metroCheckBoxWeight), foreColor: foreColor, flags: MetroPaint.GetTextFormatFlags(TextAlign));
            OnCustomPaintForeground(new MetroPaintEventArgs(Color.Empty, foreColor, e.Graphics));
            if (displayFocusRectangle && isFocused)
            {
                ControlPaint.DrawFocusRectangle(e.Graphics, base.ClientRectangle);
            }
        }

        protected override void OnGotFocus(EventArgs e)
        {
            isFocused = true;
            isHovered = true;
            Invalidate();
            base.OnGotFocus(e);
        }

        protected override void OnLostFocus(EventArgs e)
        {
            isFocused = false;
            isHovered = false;
            isPressed = false;
            Invalidate();
            base.OnLostFocus(e);
        }

        protected override void OnEnter(EventArgs e)
        {
            isFocused = true;
            isHovered = true;
            Invalidate();
            base.OnEnter(e);
        }

        protected override void OnLeave(EventArgs e)
        {
            isFocused = false;
            isHovered = false;
            isPressed = false;
            Invalidate();
            base.OnLeave(e);
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Space)
            {
                isHovered = true;
                isPressed = true;
                Invalidate();
            }
            base.OnKeyDown(e);
        }

        protected override void OnKeyUp(KeyEventArgs e)
        {
            Invalidate();
            base.OnKeyUp(e);
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            isHovered = true;
            Invalidate();
            base.OnMouseEnter(e);
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                isPressed = true;
                Invalidate();
            }
            base.OnMouseDown(e);
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            isPressed = false;
            Invalidate();
            base.OnMouseUp(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            if (!isFocused)
            {
                isHovered = false;
            }
            Invalidate();
            base.OnMouseLeave(e);
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);
            Invalidate();
        }

        protected override void OnCheckedChanged(EventArgs e)
        {
            base.OnCheckedChanged(e);
            Invalidate();
        }

        public override Size GetPreferredSize(Size proposedSize)
        {
            base.GetPreferredSize(proposedSize);
            using (Graphics dc = CreateGraphics())
            {
                proposedSize = new Size(int.MaxValue, int.MaxValue);
                Size result = TextRenderer.MeasureText(dc, Text, MetroFonts.CheckBox(metroCheckBoxSize, metroCheckBoxWeight), proposedSize, MetroPaint.GetTextFormatFlags(TextAlign));
                result.Width += 16;
                return result;
            }
        }
    }
}
