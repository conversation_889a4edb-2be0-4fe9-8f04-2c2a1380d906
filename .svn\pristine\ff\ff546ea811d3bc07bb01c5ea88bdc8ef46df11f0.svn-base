using System.ComponentModel;
using System.Windows.Forms;

namespace OCRTools
{
    public class FmGetColor : Form
    {
        private IContainer components = null;

        public FmGetColor()
        {
            InitializeComponent();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null)
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            SuspendLayout();
            base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 12f);
            base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            base.ClientSize = new System.Drawing.Size(800, 450);
            base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            base.Name = "FmGetColor";
            base.StartPosition = System.Windows.Forms.FormStartPosition.Manual;
            Text = "FmGetColor";
            ResumeLayout(false);
        }
    }
}
