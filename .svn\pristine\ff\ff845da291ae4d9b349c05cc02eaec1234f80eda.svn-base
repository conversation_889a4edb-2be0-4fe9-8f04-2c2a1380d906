using System;

namespace ExcelLibrary.CompoundDocumentFormat
{
	public class CompoundFileHeader : FileHeader
	{
		public new static readonly byte[] FileTypeIdentifier = new byte[8] { 208, 207, 17, 224, 161, 177, 26, 225 };

		public CompoundFileHeader()
		{
			base.FileTypeIdentifier = FileTypeIdentifier;
			FileIdentifier = Guid.NewGuid();
			RevisionNumber = 62;
			VersionNumber = 3;
			ByteOrderMark = ByteOrderMarks.LittleEndian;
			SectorSizeInPot = 9;
			ShortSectorSizeInPot = 6;
			UnUsed10 = new byte[10];
			UnUsed4 = new byte[4];
			MinimumStreamSize = 4096;
			FirstSectorIDofShortSectorAllocationTable = -2;
			FirstSectorIDofMasterSectorAllocationTable = -2;
			FirstSectorIDofDirectoryStream = -2;
			MasterSectorAllocationTable = new int[109];
			for (int i = 0; i < MasterSectorAllocationTable.Length; i++)
			{
				MasterSectorAllocationTable[i] = -1;
			}
		}
	}
}
