using UIAutomationClient;

namespace System.Windows.Automation
{
    public sealed class CacheRequest
    {
        public static readonly CacheRequest DefaultCacheRequest = new CacheRequest();

        public CacheRequest()
        {
            NativeCacheRequest = Automation.Factory.CreateCacheRequest();
        }

        public static CacheRequest Current => DefaultCacheRequest;

        public static IUIAutomationCacheRequest CurrentNativeCacheRequest => Current.NativeCacheRequest;

        internal IUIAutomationCacheRequest NativeCacheRequest { get; }
    }
}