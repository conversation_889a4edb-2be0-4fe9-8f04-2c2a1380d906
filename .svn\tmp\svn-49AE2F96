using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using OCRTools.Common;

namespace OCRTools.UserControlEx
{
    /// <summary>
    /// 文本流方向检测器，负责分析文本块的排列方向。
    /// </summary>
    public class TextFlowDirectionDetector
    {
        private readonly List<TextCellInfo> cells;

        // 自适应分组阈值
        private double groupingThresholdVertical = 10;
        private double groupingThresholdHorizontal = 10;

        // 极端竖排比例阈值
        private double extremeVerticalRatioThreshold = 7.0;
        
        // 直方图分析结果 - 用于在不同方法间共享
        private HistogramFeatures horizontalHistogram;
        private HistogramFeatures verticalHistogram;

        public TextFlowDirectionDetector(List<TextCellInfo> cells)
        {
            this.cells = cells;
        }

        /// <summary>
        /// 文本方向检测结果
        /// </summary>
        public class TextDirectionResult
        {
            /// <summary>水平方向（左右方向）</summary>
            public TextFlowDirection HorizontalDirection { get; set; } = TextFlowDirection.LeftToRight;

            /// <summary>垂直方向（上下方向）</summary>
            public TextFlowDirection VerticalDirection { get; set; } = TextFlowDirection.TopToBottom;

            /// <summary>水平方向置信度（0-100）</summary>
            public int HorizontalConfidence { get; set; } = 0;

            /// <summary>垂直方向置信度（0-100）</summary>
            public int VerticalConfidence { get; set; } = 0;

            /// <summary>是否为竖排布局（true表示竖排，false表示横排）</summary>
            public bool IsVerticalLayout { get; set; } = false;

            /// <summary>布局方式置信度（0-100）</summary>
            public int LayoutConfidence { get; set; } = 0;

            /// <summary>
            /// 获取主要文本方向（基于布局类型）
            /// </summary>
            /// <returns>主要阅读方向</returns>
            public TextFlowDirection GetPrimaryDirection()
            {
                // 如果水平方向是混合的，返回垂直方向
                if (HorizontalDirection == TextFlowDirection.Mixed)
                    return VerticalDirection;

                // 如果垂直方向是混合的，返回水平方向
                if (VerticalDirection == TextFlowDirection.Mixed)
                    return HorizontalDirection;

                // 根据布局类型判断主要方向
                return IsVerticalLayout ? VerticalDirection : HorizontalDirection;
            }

            /// <summary>
            /// 获取结果的字符串表示
            /// </summary>
            public override string ToString()
            {
                string layoutType = IsVerticalLayout ? "竖排" : "横排";
                return $"布局类型: {layoutType}(置信度:{LayoutConfidence}), 水平方向: {HorizontalDirection}(置信度:{HorizontalConfidence}), 垂直方向: {VerticalDirection}(置信度:{VerticalConfidence})";
            }
        }

        /// <summary>
        /// 检测文本方向（兼容旧版本）
        /// </summary>
        /// <returns>主要文本方向</returns>
        public TextFlowDirection Detect()
        {
            var result = DetectDirections();
            return result.GetPrimaryDirection();
        }

        /// <summary>
        /// 检测文本的方向和布局
        /// </summary>
        /// <returns>文本方向检测结果</returns>
        public TextDirectionResult DetectDirections()
        {
            // 初始化结果对象
            var result = new TextDirectionResult();

            if (cells == null || cells.Count < 2) return result; // 默认方向

            var validCells = cells.Where(c => c?.location != null).ToList();
            if (validCells.Count < 2) return result;

            // 计算自适应分组阈值
            CalculateAdaptiveThresholds(validCells);

            // 提取统一的文本布局特征
            var features = ExtractTextFeatures(validCells);

            // 分析文本布局特征

            // 收集方向证据
            var directionEvidence = CollectDirectionEvidenceFromFeatures(features);

            // 添加密度直方图分析 - 新增功能
            AnalyzeDensityHistograms(directionEvidence);

            // 分析方向证据，确定水平和垂直方向
            AnalyzeDirectionEvidence(directionEvidence, result, validCells);

            // 分析列的分布和方向特征
            bool isLikelyRightToLeft = false;
            if (features.VerticalColumnCount >= 2)
            {
                // 计算首尾列的中心位置
                var firstColumn = features.VerticalColumns.First();
                var lastColumn = features.VerticalColumns.Last();

                double firstColumnCenter = firstColumn.Average(c => c.location.left + c.location.width / 2);
                double lastColumnCenter = lastColumn.Average(c => c.location.left + c.location.width / 2);

                // 检测是否从右到左排列 - 第一列位于页面右侧，最后一列位于页面左侧
                isLikelyRightToLeft = firstColumnCenter > features.PageCenter_X && lastColumnCenter < features.PageCenter_X;

                // 基于内容密度进行分析
                bool densitySupportsRightToLeft = features.ContentDensityRightHalf > features.ContentDensityLeftHalf * 1.2;

                // 分析右侧列比例
                int columnsInRightHalf = 0;
                foreach (var column in features.VerticalColumns)
                {
                    double centerX = column.Average(c => c.location.left + c.location.width / 2);
                    if (centerX > features.PageCenter_X)
                    {
                        columnsInRightHalf++;
                    }
                }

                double rightColumnsRatio = columnsInRightHalf / (double)features.VerticalColumnCount;

                // 如果右半页列比例大于50%，考虑从右到左读取
                if (rightColumnsRatio > 0.55 && (densitySupportsRightToLeft || !isLikelyRightToLeft))
                {
                    isLikelyRightToLeft = true;
                }
            }

            // 如果列分布分析发现从右到左的倾向，增加相应证据
            if (isLikelyRightToLeft)
            {
                directionEvidence.RightToLeftCount += Math.Max(2, features.VerticalColumnCount);
            }

            // 确定文档布局类型
            DetermineLayoutType(result, directionEvidence);

            // 确保最低置信度，避免不确定结果
            EnsureMinimumConfidence(result);

            // 对于复杂文档（有更多文本块），进行额外的置信度优化
            if (validCells.Count >= 10)
            {
                ApplyAdvancedConfidenceOptimization(result, directionEvidence, validCells);
            }

            // 进行最终的一致性检查 - 确保方向和布局保持一致
            AdjustConfidenceBasedOnConsistency(result, directionEvidence);

            // 最终水平方向判断调整 - 竖排多列情况特殊处理
            if (result.IsVerticalLayout)
            {
                // 竖排文本特殊处理
                OptimizeVerticalLayoutDirections(result, features, directionEvidence);
            }

            return result;
        }

        /// <summary>
        /// 优化竖排布局的方向判断
        /// </summary>
        /// <param name="result">方向结果</param>
        /// <param name="features">文本布局特征</param>
        /// <param name="evidence">方向证据</param>
        private void OptimizeVerticalLayoutDirections(TextDirectionResult result, TextLayoutFeatures features, DirectionEvidence evidence)
        {
            // 针对竖排文本的特殊优化
            if (features.VerticalTextCount <= features.HorizontalTextCount) return;

            // 1. 多列竖排文本的水平方向判断
            if (features.VerticalColumnCount >= 2)
            {
                // 计算右到左和左到右的证据比例
                double totalHorizontalEvidence = evidence.LeftToRightCount + evidence.RightToLeftCount;
                if (totalHorizontalEvidence > 0)
                {
                    double rtlRatio = evidence.RightToLeftCount / totalHorizontalEvidence;

                    // 如果右到左证据占比较高，调整方向和置信度
                    if (rtlRatio > 0.4) // 阈值降低，因为竖排文本的水平方向判断较难
                    {
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;

                        // 置信度基于证据比例
                        int confidenceBoost = (int)(Math.Min(25, features.VerticalColumnCount * 5) *
                                                   Math.Min(1.0, rtlRatio + 0.2));

                        result.HorizontalConfidence = Math.Min(95,
                            Math.Max(result.HorizontalConfidence, result.HorizontalConfidence + confidenceBoost));
                    }
                }

                // 2. 基于内容分布的判断
                if (features.ContentDensityRightHalf > 0 && features.ContentDensityLeftHalf > 0)
                {
                    double densityRatio = features.ContentDensityRightHalf / features.ContentDensityLeftHalf;

                    // 右半页密度明显高于左半页，增强从右到左的可能性
                    if (densityRatio > 1.2)
                    {
                        // 如果已经是RightToLeft，增强置信度
                        if (result.HorizontalDirection == TextFlowDirection.RightToLeft)
                        {
                            result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 10);
                        }
                        // 如果是LeftToRight但置信度不高，考虑切换
                        else if (result.HorizontalConfidence < 70)
                        {
                            result.HorizontalDirection = TextFlowDirection.RightToLeft;
                            result.HorizontalConfidence = Math.Max(result.HorizontalConfidence, 70);
                        }
                    }
                    // 左半页密度明显高于右半页，增强从左到右的可能性
                    else if (densityRatio < 0.8)
                    {
                        // 如果已经是LeftToRight，增强置信度
                        if (result.HorizontalDirection == TextFlowDirection.LeftToRight)
                        {
                            result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 10);
                        }
                        // 如果是RightToLeft但置信度不高，考虑切换
                        else if (result.HorizontalConfidence < 70)
                        {
                            result.HorizontalDirection = TextFlowDirection.LeftToRight;
                            result.HorizontalConfidence = Math.Max(result.HorizontalConfidence, 70);
                        }
                    }
                }
            }

            // 3. 单列竖排文本的特殊处理
            else if (features.VerticalTextCount >= 3)
            {
                // 计算方形文字比例
                double squareTextRatio = CalculateSquareTextRatio(this.cells);

                // 对于中文等方形文字主导的单列竖排，几乎都是从右到左的
                // 设置非常高的置信度
                result.HorizontalDirection = TextFlowDirection.RightToLeft;
                // 方形文字比例越高，置信度越高，但基础置信度很高
                int confidenceBoost = (int)(15 * Math.Min(1.0, squareTextRatio));
                result.HorizontalConfidence = Math.Max(result.HorizontalConfidence,
                                                      Math.Min(98, 90 + confidenceBoost));

                System.Diagnostics.Debug.WriteLine($"检测到竖排文本，强制设置水平方向为从右到左，置信度:{result.HorizontalConfidence}");

            }

            // 4. 确保垂直方向置信度较高
            if (result.VerticalDirection == TextFlowDirection.TopToBottom)
            {
                result.VerticalConfidence = Math.Max(result.VerticalConfidence, 85);
            }
        }

        /// <summary>
        /// 检查文本是否主要为中日韩文字
        /// </summary>
        /// <param name="cells">文本单元格列表</param>
        /// <returns>是否为中日韩文字</returns>
        /// <summary>
        /// 检测是否为方形文字主导的文本
        /// </summary>
        /// <param name="cells">文本单元列表</param>
        /// <returns>是否为方形文字主导</returns>
        private bool IsSquareTextDominant(List<TextCellInfo> cells)
        {
            // 使用新方法计算占比，并设置阈值判断
            double ratio = CalculateSquareTextRatio(cells);
            return ratio > 0.4; // 当方形文字占比超过40%时认为占主导
        }

        /// <summary>
        /// 计算方形文字的占比程度
        /// </summary>
        /// <param name="cells">文本单元列表</param>
        /// <returns>方形文字占比(0.0-1.0)，值越大表示方形文字占比越高</returns>
        private double CalculateSquareTextRatio(List<TextCellInfo> cells)
        {
            if (cells == null || cells.Count == 0)
                return 0;

            // 统计方形文字特征
            int strictSquareCharCount = 0;  // 严格方形文字(CJK等)
            int totalCharCount = 0;
            int strictSquareTextCellCount = 0;  // 严格方形文本单元(0.9-1.1)
            int nearSquareTextCellCount = 0;    // 接近方形文本单元(0.8-1.2)
            int totalTextCellCount = 0;

            foreach (var cell in cells)
            {
                if (cell?.location == null) continue;

                totalTextCellCount++;

                // 检查文本单元是否接近正方形
                double ratio = cell.location.width > 0 && cell.location.height > 0 ?
                    cell.location.height / cell.location.width : 1.0;

                if (ratio >= 0.9 && ratio <= 1.1)
                {
                    strictSquareTextCellCount++;
                }
                else if (ratio >= 0.8 && ratio <= 1.2)
                {
                    nearSquareTextCellCount++;
                }

                // 检查文字特征
                if (!string.IsNullOrEmpty(cell.words))
                {
                    foreach (char c in cell.words)
                    {
                        totalCharCount++;

                        // 检查是否为方形文字（通用判断，不限于特定语言）
                        // 包括中日韩文字、方块符号等
                        if ((c >= 0x4E00 && c <= 0x9FFF) || // CJK统一汉字
                            (c >= 0x3040 && c <= 0x30FF) || // 日文假名
                            (c >= 0x3130 && c <= 0x318F) || // 韩文兼容字母
                            (c >= 0xAC00 && c <= 0xD7AF) || // 韩文音节
                            (c >= 0x2500 && c <= 0x257F))   // 制表符
                        {
                            strictSquareCharCount++;
                        }
                    }
                }
            }

            // 计算各指标比例
            double strictCellRatio = totalTextCellCount > 0 ?
                (double)strictSquareTextCellCount / totalTextCellCount : 0;

            double nearCellRatio = totalTextCellCount > 0 ?
                (double)nearSquareTextCellCount / totalTextCellCount : 0;

            double charRatio = totalCharCount > 0 ?
                (double)strictSquareCharCount / totalCharCount : 0;

            // 综合计算方形文字占比 - 使用加权平均
            // 严格方形文本单元权重0.4，接近方形文本单元权重0.1，方形字符权重0.5
            double squareRatio = (strictCellRatio * 0.4) + (nearCellRatio * 0.1) + (charRatio * 0.5);

            System.Diagnostics.Debug.WriteLine($"方形文字分析: 严格方形单元={strictCellRatio:P1}, 接近方形单元={nearCellRatio:P1}, 方形字符={charRatio:P1}, 综合占比={squareRatio:F3}");

            return squareRatio;
        }

        /// <summary>
        /// 基于统一特征收集方向证据
        /// </summary>
        /// <param name="features">文本布局特征</param>
        /// <returns>方向证据</returns>
        private DirectionEvidence CollectDirectionEvidenceFromFeatures(TextLayoutFeatures features)
        {
            var evidence = new DirectionEvidence();

            // 1. 从形状特征收集证据
            evidence.HorizontalTextCount = features.HorizontalTextCount;
            evidence.VerticalTextCount = features.VerticalTextCount;

            // 2. 从行列结构特征收集证据
            evidence.HorizontalAlignedRows = features.HorizontalRowCount;
            evidence.VerticalAlignedColumns = features.VerticalColumnCount;

            // 3. 从对齐特征收集证据
            evidence.LeftAlignedCount = features.LeftAlignedCount;
            evidence.RightAlignedCount = features.RightAlignedCount;
            evidence.TopAlignedCount = features.TopAlignedCount;
            evidence.BottomAlignedCount = features.BottomAlignedCount;

            // 4. 从边缘变异特征收集证据
            evidence.LeftEdgeVariance = features.LeftEdgeVariance;
            evidence.RightEdgeVariance = features.RightEdgeVariance;
            evidence.TopEdgeVariance = features.TopEdgeVariance;
            evidence.BottomEdgeVariance = features.BottomEdgeVariance;

            // 5. 从段落特征收集证据
            evidence.ParagraphCount = features.ParagraphCount;

            // 6. 从方向证据直接收集
            evidence.LeftToRightCount += features.LeftToRightEvidence;
            evidence.RightToLeftCount += features.RightToLeftEvidence;
            evidence.TopToBottomCount += features.TopToBottomEvidence;
            evidence.BottomToTopCount += features.BottomToTopEvidence;

            // 计算基准单位 - 基于文本块总数的动态基准
            // 文本块越多，每个证据的权重越小，避免大文档中证据过度累积
            int baseUnitWeight = Math.Max(1, Math.Min(5, features.ValidTextBlocks / 10));

            // 7. 从边缘变异分析方向 - 使用动态阈值
            // 计算边缘整齐度强度 (变异比率越极端，强度越高)
            double leftRightVarianceStrength = 0;
            double topBottomVarianceStrength = 0;

            // 左右边缘变异比率阈值 - 动态计算
            double lrLowThreshold = Math.Max(0.7, 0.8 - features.ValidTextBlocks * 0.005); // 文本块越多，阈值越低
            double lrHighThreshold = Math.Min(1.3, 1.25 + features.ValidTextBlocks * 0.005); // 文本块越多，阈值越高

            // 左边界比右边界更整齐（变异小）表示从左到右阅读
            if (features.LeftRightEdgeVarianceRatio < lrLowThreshold)
            {
                leftRightVarianceStrength = Math.Min(1.0, (lrLowThreshold - features.LeftRightEdgeVarianceRatio) / 0.3);
                evidence.LeftToRightCount += (int)(baseUnitWeight * 3 * leftRightVarianceStrength);
            }
            // 右边界比左边界更整齐表示从右到左阅读
            else if (features.LeftRightEdgeVarianceRatio > lrHighThreshold)
            {
                leftRightVarianceStrength = Math.Min(1.0, (features.LeftRightEdgeVarianceRatio - lrHighThreshold) / 0.75);
                evidence.RightToLeftCount += (int)(baseUnitWeight * 3 * leftRightVarianceStrength);
            }

            // 上下边缘变异比率阈值 - 动态计算
            double tbLowThreshold = Math.Max(0.7, 0.8 - features.ValidTextBlocks * 0.005);
            double tbHighThreshold = Math.Min(1.3, 1.25 + features.ValidTextBlocks * 0.005);

            // 上边界比下边界更整齐表示从上到下阅读
            if (features.TopBottomEdgeVarianceRatio < tbLowThreshold)
            {
                topBottomVarianceStrength = Math.Min(1.0, (tbLowThreshold - features.TopBottomEdgeVarianceRatio) / 0.3);
                evidence.TopToBottomCount += (int)(baseUnitWeight * 3 * topBottomVarianceStrength);
            }
            // 下边界比上边界更整齐表示从下到上阅读
            else if (features.TopBottomEdgeVarianceRatio > tbHighThreshold)
            {
                topBottomVarianceStrength = Math.Min(1.0, (features.TopBottomEdgeVarianceRatio - tbHighThreshold) / 0.75);
                evidence.BottomToTopCount += (int)(baseUnitWeight * 3 * topBottomVarianceStrength);
            }

            // 8. 竖排文本的特殊处理 - 根据竖排文本比例增强方向证据
            if (features.VerticalTextCount > features.HorizontalTextCount * 1.5)
            {
                // 竖排文本比例明显高于横排文本
                double verticalRatio = Math.Min(1.0,
                    (double)features.VerticalTextCount / Math.Max(1, features.HorizontalTextCount) / 2.0);

                // 强度限制在0.2-1.0之间
                verticalRatio = Math.Max(0.2, verticalRatio);

                // 增加从上到下的证据权重
                evidence.TopToBottomCount += (int)(baseUnitWeight * 5 * verticalRatio);

                // 对于竖排文本，如果有明确的右到左证据，增强这一方向
                if (features.RightToLeftEvidence > features.LeftToRightEvidence)
                {
                    double rtlStrength = Math.Min(1.0,
                        (double)features.RightToLeftEvidence / Math.Max(1, features.LeftToRightEvidence));

                    evidence.RightToLeftCount += (int)(baseUnitWeight * 4 * rtlStrength * verticalRatio);
                }
            }

            return evidence;
        }

        /// <summary>
        /// 对复杂文档应用高级置信度优化
        /// </summary>
        private void ApplyAdvancedConfidenceOptimization(TextDirectionResult result, DirectionEvidence evidence, List<TextCellInfo> cells)
        {
            // 1. 检查布局和主方向的一致性
            // 检查是否存在强烈的一致性证据
            bool hasStrongConsistency = false;

            // 横排文本与从左到右方向一致性
            if (!result.IsVerticalLayout && result.HorizontalDirection == TextFlowDirection.LeftToRight &&
                evidence.LeftToRightCount > evidence.RightToLeftCount * 2 &&
                evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.5)
            {
                hasStrongConsistency = true;
            }

            // 竖排文本与从上到下方向一致性
            else if (result.IsVerticalLayout && result.VerticalDirection == TextFlowDirection.TopToBottom &&
                    evidence.TopToBottomCount > evidence.BottomToTopCount * 2 &&
                    evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5)
            {
                hasStrongConsistency = true;
            }

            // 如果有强烈一致性，适当提高置信度
            if (hasStrongConsistency)
            {
                // 同时提高布局和方向置信度
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + 5);

                if (!result.IsVerticalLayout)
                {
                    result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + 5);
                }
                else
                {
                    result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + 5);
                }
            }

            // 2. 一致性特征分析
            // 计算各种证据的互相支持程度
            int consistencyScore = 0;

            // 文本形状与对齐特征一致性
            if ((evidence.HorizontalTextCount > evidence.VerticalTextCount &&
                 evidence.LeftAlignedCount > evidence.TopAlignedCount) ||
                (evidence.VerticalTextCount > evidence.HorizontalTextCount &&
                 evidence.TopAlignedCount > evidence.LeftAlignedCount))
            {
                consistencyScore += 2;
            }

            // 边界变异与方向一致性
            if ((evidence.LeftEdgeVariance < evidence.RightEdgeVariance &&
                 result.HorizontalDirection == TextFlowDirection.LeftToRight) ||
                (evidence.RightEdgeVariance < evidence.LeftEdgeVariance &&
                 result.HorizontalDirection == TextFlowDirection.RightToLeft) ||
                (evidence.TopEdgeVariance < evidence.BottomEdgeVariance &&
                 result.VerticalDirection == TextFlowDirection.TopToBottom) ||
                (evidence.BottomEdgeVariance < evidence.TopEdgeVariance &&
                 result.VerticalDirection == TextFlowDirection.BottomToTop))
            {
                consistencyScore += 2;
            }

            // 段落布局与布局类型一致性
            if ((evidence.ParagraphCount >= 3 && !result.IsVerticalLayout) ||
                (evidence.IsSequentialParagraphs && evidence.LeftAlignmentRatio > 0.7 && !result.IsVerticalLayout))
            {
                consistencyScore += 2;
            }

            // 应用一致性评分
            if (consistencyScore >= 4)
            {
                // 高一致性得分提高整体置信度
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + consistencyScore / 2);
                result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + consistencyScore / 2);
                result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + consistencyScore / 2);
            }
        }

        /// <summary>
        /// 确保结果具有合理的置信度，避免不确定的输出
        /// </summary>
        private void EnsureMinimumConfidence(TextDirectionResult result)
        {
            // 基于相对性的置信度调整

            // 1. 默认方向置信度阈值 - 使用相对阈值而非绝对值
            int baseThreshold = 50; // 最基础的置信度阈值

            // 2. 根据文本复杂度动态调整阈值
            // 如果两个方向的置信度差异很小，说明文本布局复杂或模糊，应该降低阈值
            if (Math.Abs(result.HorizontalConfidence - result.VerticalConfidence) < 15)
            {
                baseThreshold = 45; // 降低阈值
            }

            // 3. 水平方向的置信度调整
            if (result.HorizontalConfidence < baseThreshold)
            {
                // 根据垂直方向的置信度动态决定水平方向的默认置信度
                // 如果垂直方向的置信度很高，我们对水平方向的默认值更有信心
                int defaultHorizontalConfidence =
                    result.VerticalConfidence > 75 ? 65 : 60;

                // 特殊处理：如果是竖排布局，水平方向默认为从右到左
                if (result.IsVerticalLayout)
                {
                    // 对于竖排布局，特别是中文等方形文字的竖排文本，水平方向通常是从右到左
                    double squareTextRatio = CalculateSquareTextRatio(this.cells);
                    // 对于竖排布局，特别是中文等方形文字的竖排文本，水平方向通常是从右到左
                    result.HorizontalDirection = TextFlowDirection.RightToLeft;

                    // 无论方形文字比例，一律设置高置信度
                    // 竖排布局水平方向几乎一定是从右到左
                    defaultHorizontalConfidence = Math.Min(95, 85 + (int)(squareTextRatio * 10));
                    System.Diagnostics.Debug.WriteLine($"竖排布局，强制设置水平方向为从右到左，置信度:{defaultHorizontalConfidence}");
                }
                else
                {
                    // 横排布局，默认为从左到右
                    result.HorizontalDirection = TextFlowDirection.LeftToRight; // 最常见的默认方向
                }

                result.HorizontalConfidence = defaultHorizontalConfidence;
            }

            // 4. 垂直方向的置信度调整
            if (result.VerticalConfidence < baseThreshold)
            {
                // 根据水平方向的置信度动态决定垂直方向的默认置信度
                int defaultVerticalConfidence =
                    result.HorizontalConfidence > 75 ? 70 : 65;

                result.VerticalDirection = TextFlowDirection.TopToBottom; // 最常见的默认方向
                result.VerticalConfidence = defaultVerticalConfidence;
            }

            // 5. 布局类型置信度调整 - 根据主方向置信度动态决定
            if (result.LayoutConfidence < baseThreshold)
            {
                // 根据当前布局类型选择参考置信度
                int referenceConfidence = result.IsVerticalLayout ?
                    result.VerticalConfidence : result.HorizontalConfidence;

                // 如果参考置信度高，保持当前布局类型但提高置信度
                if (referenceConfidence > 70)
                {
                    result.LayoutConfidence = Math.Max(referenceConfidence - 10, 60);
                }
                // 否则采用更保守的默认值：横排布局（更常见）
                else
                {
                    result.IsVerticalLayout = false;
                    result.LayoutConfidence = 60;
                }
            }
        }

        /// <summary>
        /// 确定文档的布局类型（横排或竖排）
        /// </summary>
        /// <remarks>
        /// 这个方法是布局类型判断的核心算法，使用多维度特征的累加得分系统来判断文档是横排还是竖排布局。
        /// 该方法考虑多种布局证据，包括文本形状、行列结构、对齐特征等，适用于各种语言的文档。
        /// </remarks>
        /// <param name="result">输出的文本方向结果，将设置布局类型</param>
        /// <param name="evidence">收集的方向证据</param>
        private void DetermineLayoutType(TextDirectionResult result, DirectionEvidence evidence)
        {
            // 使用得分累加系统确定布局类型
            // 通过多维度特征分析，使系统适用于各种不同的文档布局
            int horizontalLayoutScore = 0; // 横排布局得分
            int verticalLayoutScore = 0;   // 竖排布局得分

            // 计算形状特征权重 - 动态计算而非固定值
            double shapeWeight = CalculateShapeWeight(evidence);
            double alignmentWeight = 1.0 - shapeWeight * 0.7; // 边缘对齐权重
            double otherWeight = 1.0 - shapeWeight - alignmentWeight; // 其他特征权重

            System.Diagnostics.Debug.WriteLine($"\n智能权重分配 - 形状权重: {shapeWeight:F2}, 边缘对齐权重: {alignmentWeight:F2}, 其他特征权重: {otherWeight:F2}");

            // 开始详细的布局分析记录
            System.Diagnostics.Debug.WriteLine("\n============ 布局类型分析开始 ============");

            // ====================== 布局类型特征分析 ======================

            // 1. 文本块形状特征 - 最直观和通用的特征
            // 对任何语言都适用，无需特定语言知识
            // 1.1 高宽比特征 - 根据文本块的形状判断其可能的排版方向

            // 计算总的形状特征得分
            int totalShapeScore = 0;

            // 记录形状得分情况
            System.Diagnostics.Debug.WriteLine("\n--- 1. 形状特征得分 ---");
            System.Diagnostics.Debug.WriteLine($"竖向文本数: {evidence.VerticalTextCount}, 横向文本数: {evidence.HorizontalTextCount}");

            // 检查是否存在极端竖排文本特征
            bool hasExtremeVerticalFeature = false;
            bool hasExtremeHorizontalFeature = false;

            // 检测极端比例文本块占比
            double totalTextCount = evidence.VerticalTextCount + evidence.HorizontalTextCount;
            double verticalRatio = totalTextCount > 0 ? evidence.VerticalTextCount / totalTextCount : 0;
            double horizontalRatio = totalTextCount > 0 ? evidence.HorizontalTextCount / totalTextCount : 0;

            System.Diagnostics.Debug.WriteLine($"竖向文本占比: {verticalRatio:P2}, 横向文本占比: {horizontalRatio:P2}");

            // 检查是否有足够的文本来判断
            if (totalTextCount < 3)
            {
                System.Diagnostics.Debug.WriteLine("文本数量不足，难以可靠判断形状特征");
                // 使用其他证据继续分析
            }
            else
            {
                // 1. 分析显著优势情况 - 一种形状完全占优
                if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5)
                {
                    // 竖向文本明显占优 - 强烈暗示竖排布局
                    int score = 8;  // 强力证据

                    // 增强对极端竖排情况的检测
                    if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 2.5)
                    {
                        // 竖向文本极度占优 - 之前是3倍，现在降低到2.5倍
                        score += 6; // 增加得分
                        hasExtremeVerticalFeature = true;
                        System.Diagnostics.Debug.WriteLine($"竖向文本远远多于横向(2.5倍以上) => 竖排得分 +{score}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"竖向文本远多于横向(1.5倍以上) => 竖排得分 +{score}");
                    }

                    verticalLayoutScore += score;
                    totalShapeScore += score;
                }
                else if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.2)
                {
                    // 竖向文本明显较多 - 从1.25倍降低到1.2倍
                    int score = 7;  // 中等证据，增加得分
                    verticalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"竖向文本明显多于横向(1.2倍以上) => 竖排得分 +{score}");
                }
                else if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.05)
                {
                    // 竖向文本略多 - 从1.1倍降低到1.05倍，更容易触发竖排判断
                    int score = 5;  // 中弱证据，增加得分
                    verticalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"竖向文本略多于横向(1.05倍以上) => 竖排得分 +{score}");
                }
                else if (evidence.VerticalTextCount > evidence.HorizontalTextCount)
                {
                    // 竖向文本稍多 - 弱竖排证据，但仍有意义
                    int score = 3;  // 弱证据，增加得分
                    verticalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"竖向文本稍多于横向 => 竖排得分 +{score}");
                }
                else if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.5)
                {
                    // 横向文本明显占优 - 强烈暗示横排布局
                    int score = 8;  // 强力证据

                    // 增强对极端横排情况的检测
                    if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 3)
                    {
                        // 横向文本极度占优
                        score += 4;
                        hasExtremeHorizontalFeature = true;
                        System.Diagnostics.Debug.WriteLine($"横向文本远远多于竖向(3倍以上) => 横排得分 +{score}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"横向文本远多于竖向(1.5倍以上) => 横排得分 +{score}");
                    }

                    horizontalLayoutScore += score;
                    totalShapeScore += score;
                }
                else if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.4)
                {
                    // 横向文本明显较多 - 中等强度的横排证据
                    int score = 6;  // 中等证据
                    horizontalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"横向文本明显多于竖向(1.4倍以上) => 横排得分 +{score}");
                }
                else if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.2)
                {
                    // 横向文本略多 - 中弱强度的横排证据
                    int score = 4;  // 中弱证据
                    horizontalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"横向文本略多于竖向(1.2倍以上) => 横排得分 +{score}");
                }
                else if (evidence.HorizontalTextCount > evidence.VerticalTextCount)
                {
                    // 横向文本稍多 - 弱横排证据，但仍有意义
                    int score = 2;  // 弱证据
                    horizontalLayoutScore += score;
                    totalShapeScore += score;
                    System.Diagnostics.Debug.WriteLine($"横向文本稍多于竖向 => 横排得分 +{score}");
                }
                // 2. 当文本数量接近相等时，引入额外判断因素
                else if (Math.Abs(evidence.VerticalTextCount - evidence.HorizontalTextCount) <= 2)
                {
                    // 当形状特征没有明显优势时，考虑其他证据
                    System.Diagnostics.Debug.WriteLine($"横向与竖向文本数量接近，形状特征不明显");

                    // 检查列与行的数量，通常能更好地反映整体布局
                    if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows)
                    {
                        int score = 4; // 增加得分
                        verticalLayoutScore += score;
                        totalShapeScore += score;
                        System.Diagnostics.Debug.WriteLine($"垂直列数({evidence.VerticalAlignedColumns})大于水平行数({evidence.HorizontalAlignedRows}) => 竖排得分 +{score}");
                    }
                    else if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns)
                    {
                        int score = 3;
                        horizontalLayoutScore += score;
                        totalShapeScore += score;
                        System.Diagnostics.Debug.WriteLine($"水平行数({evidence.HorizontalAlignedRows})大于垂直列数({evidence.VerticalAlignedColumns}) => 横排得分 +{score}");
                    }
                }
            }

            // 检查极端形状特征的占比影响
            if (verticalRatio > 0.7 && evidence.VerticalTextCount >= 3)
            {
                // 如果竖向文本占比超过70%，且至少有3个，这是很强的竖排证据
                // 原来是80%，现在降低到70%
                int bonusScore = 8; // 增加得分
                verticalLayoutScore += bonusScore;
                totalShapeScore += bonusScore;
                System.Diagnostics.Debug.WriteLine($"竖向文本占比超过70%，强烈的竖排证据 => 竖排得分 +{bonusScore}");
            }
            else if (horizontalRatio > 0.8 && evidence.HorizontalTextCount >= 3)
            {
                // 如果横向文本占比超过80%，且至少有3个，这是很强的横排证据
                int bonusScore = 6;
                horizontalLayoutScore += bonusScore;
                totalShapeScore += bonusScore;
                System.Diagnostics.Debug.WriteLine($"横向文本占比超过80%，强烈的横排证据 => 横排得分 +{bonusScore}");
            }

            // 形状特征总结
            if (totalShapeScore > 0)
            {
                double verticalRatioScore = verticalLayoutScore / (double)totalShapeScore;
                System.Diagnostics.Debug.WriteLine($"形状特征总结: 总分={totalShapeScore}, 竖排得分比例={verticalRatioScore:P2}");

                if (verticalRatioScore > 0.6)
                {
                    System.Diagnostics.Debug.WriteLine("形状特征总体倾向: 竖排");
                }
                else if (verticalRatioScore < 0.4)
                {
                    System.Diagnostics.Debug.WriteLine("形状特征总体倾向: 横排");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("形状特征总体倾向: 不确定");
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("形状特征分析: 无明显倾向");
            }

            // 2. 方向置信度比较 - 通过已确定的方向置信度差异判断布局
            // 已确定的阅读方向置信度差异可以作为布局判断的辅助证据
            int confidenceDiff = Math.Abs(result.HorizontalConfidence - result.VerticalConfidence);
            if (confidenceDiff >= 20) // 方向置信度差异非常明显
            {
                // 方向置信度差异很大，证据强
                if (result.HorizontalConfidence > result.VerticalConfidence)
                {
                    // 水平方向置信度明显高 - 横排证据
                    // 例：阅读方向分析中水平方向特征非常强
                    horizontalLayoutScore += 6;
                }
                else
                {
                    // 垂直方向置信度明显高 - 竖排证据
                    // 例：阅读方向分析中垂直方向特征非常强
                    verticalLayoutScore += 6;
                }
            }
            else if (confidenceDiff >= 10) // 方向置信度有一定差异
            {
                // 方向置信度差异中等
                if (result.HorizontalConfidence > result.VerticalConfidence)
                {
                    // 水平方向置信度稍高 - 弱横排证据
                    horizontalLayoutScore += 3;
                }
                else
                {
                    // 垂直方向置信度稍高 - 弱竖排证据
                    verticalLayoutScore += 3;
                }
            }

            // 3. 行列结构比较 - 文档的整体排列结构
            // 3.1 行数与列数比较 - 分析文本的整体排列模式
            if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows * 1.8)
            {
                // 列数远多于行数 - 强竖排证据，降低阈值从2.0到1.8
                // 例：传统竖排中文，文本块按列排列
                verticalLayoutScore += 8;  // 增加得分
                System.Diagnostics.Debug.WriteLine($"列数({evidence.VerticalAlignedColumns})远多于行数({evidence.HorizontalAlignedRows})，强竖排证据 => 竖排得分 +8");
            }
            else if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows * 1.3)
            {
                // 列数明显多于行数 - 中等竖排证据，降低阈值从1.5到1.3
                verticalLayoutScore += 6;  // 增加得分
                System.Diagnostics.Debug.WriteLine($"列数明显多于行数(1.3倍) => 竖排得分 +6");
            }
            else if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows)
            {
                // 列数略多于行数 - 弱竖排证据
                verticalLayoutScore += 3;  // 增加得分
                System.Diagnostics.Debug.WriteLine($"列数略多于行数 => 竖排得分 +3");
            }
            else if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns * 2.0)
            {
                // 行数远多于列数 - 强横排证据
                // 例：普通书籍文档，文本块按行排列
                horizontalLayoutScore += 7;  // 行数远大于列数，强力横排证据
                System.Diagnostics.Debug.WriteLine($"行数远多于列数(2倍) => 横排得分 +7");
            }
            else if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns * 1.5)
            {
                // 行数明显多于列数 - 中等横排证据
                horizontalLayoutScore += 5;  // 行数明显多于列数
                System.Diagnostics.Debug.WriteLine($"行数明显多于列数(1.5倍) => 横排得分 +5");
            }
            else if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns)
            {
                // 行数略多于列数 - 弱横排证据
                horizontalLayoutScore += 2;  // 行数略多于列数
                System.Diagnostics.Debug.WriteLine($"行数略多于列数 => 横排得分 +2");
            }

            // 检查列数和行数的绝对值
            if (evidence.VerticalAlignedColumns >= 3)
            {
                // 至少有3列，这是很强的竖排证据
                int columnBonus = Math.Min(10, evidence.VerticalAlignedColumns * 2);
                verticalLayoutScore += columnBonus;
                System.Diagnostics.Debug.WriteLine($"至少有3列排列，强竖排证据 => 竖排得分 +{columnBonus}");
            }
            else if (evidence.VerticalAlignedColumns == 2)
            {
                // 有2列，中等竖排证据
                verticalLayoutScore += 5;
                System.Diagnostics.Debug.WriteLine($"有2列排列，中等竖排证据 => 竖排得分 +5");
            }

            // 4. 对齐特征 - 分析文本块的对齐模式
            // 4.1 左/右对齐 vs 上/下对齐的比较
            if (evidence.LeftAlignedCount > evidence.TopAlignedCount * 2.0)
            {
                // 左对齐远多于顶部对齐 - 强横排证据
                // 例：典型的左对齐段落排版
                horizontalLayoutScore += 5;  // 左对齐远多于顶对齐，横排证据
                System.Diagnostics.Debug.WriteLine($"左对齐({evidence.LeftAlignedCount})远多于顶对齐({evidence.TopAlignedCount})，横排证据 => 横排得分 +5");
            }
            else if (evidence.LeftAlignedCount > evidence.TopAlignedCount * 1.5)
            {
                // 左对齐明显多于顶部对齐 - 中等横排证据
                horizontalLayoutScore += 3;
                System.Diagnostics.Debug.WriteLine($"左对齐明显多于顶对齐(1.5倍) => 横排得分 +3");
            }
            else if (evidence.TopAlignedCount > evidence.LeftAlignedCount * 2.0)
            {
                // 顶部对齐远多于左对齐 - 强竖排证据
                // 例：竖排文本的顶部对齐排版
                verticalLayoutScore += 5;  // 顶对齐远多于左对齐，竖排证据
                System.Diagnostics.Debug.WriteLine($"顶对齐({evidence.TopAlignedCount})远多于左对齐({evidence.LeftAlignedCount})，竖排证据 => 竖排得分 +5");
            }
            else if (evidence.TopAlignedCount > evidence.LeftAlignedCount * 1.5)
            {
                // 顶部对齐明显多于左对齐 - 中等竖排证据
                verticalLayoutScore += 3;
                System.Diagnostics.Debug.WriteLine($"顶对齐明显多于左对齐(1.5倍) => 竖排得分 +3");
            }

            // 5. 边界变异特征 - 文本边界的整齐程度
            // 阅读起始边界通常更加整齐（变异小）
            // 显著增强边缘变异特征的影响
            
            // 计算边缘变异特征的权重因子 - 基于文本块数量动态调整
            double edgeWeightFactor = 1.0;
            int textBlockCount = this.cells.Count;
            
            // 文本块数量越少，边缘变异特征越重要
            if (textBlockCount <= 5)
            {
                edgeWeightFactor = 1.8; // 显著增加权重
                System.Diagnostics.Debug.WriteLine($"文本块数量较少({textBlockCount})，显著增加边缘变异特征权重到{edgeWeightFactor:F1}");
            }
            else if (textBlockCount <= 10)
            {
                edgeWeightFactor = 1.5; // 中度增加权重
                System.Diagnostics.Debug.WriteLine($"文本块数量适中({textBlockCount})，中度增加边缘变异特征权重到{edgeWeightFactor:F1}");
            }
            else if (textBlockCount <= 20)
            {
                edgeWeightFactor = 1.2; // 轻度增加权重
                System.Diagnostics.Debug.WriteLine($"文本块数量较多({textBlockCount})，轻度增加边缘变异特征权重到{edgeWeightFactor:F1}");
            }
            
            // 主要边缘比较：左边界 vs 顶部边界
            if (evidence.LeftEdgeVariance < evidence.TopEdgeVariance * 0.7) // 进一步放宽比例阈值，从0.6到0.7
            {
                // 左边界比顶部边界更整齐 - 横排证据
                // 例：左对齐的横排文本，左边界一致而顶部边界不一致
                int baseEdgeScore = 6;  // 增加基础权重，从5提高到6
                int edgeScore = (int)(baseEdgeScore * edgeWeightFactor);
                horizontalLayoutScore += edgeScore;
                System.Diagnostics.Debug.WriteLine($"左边界比顶边界更整齐，横排证据 => 横排得分 +{edgeScore} (基础分:{baseEdgeScore}, 权重:{edgeWeightFactor:F1})");
                
                // 增加额外得分，基于边缘变异差异程度
                double varianceRatio = evidence.TopEdgeVariance / Math.Max(0.001, evidence.LeftEdgeVariance);
                
                // 使用更细粒度的比例阈值
                if (varianceRatio > 3.0) // 差异非常显著
                {
                    int bonusScore = (int)Math.Min(8, 3 + Math.Log10(varianceRatio) * 2);
                    bonusScore = (int)(bonusScore * edgeWeightFactor);
                    horizontalLayoutScore += bonusScore;
                    System.Diagnostics.Debug.WriteLine($"左边界变异度显著低于顶边界(比例:{varianceRatio:F1})，强横排证据 => 额外横排得分 +{bonusScore}");
                }
                else if (varianceRatio > 2.0) // 差异明显
                {
                    int bonusScore = (int)Math.Min(6, 2 + Math.Log10(varianceRatio) * 1.5);
                    bonusScore = (int)(bonusScore * edgeWeightFactor);
                    horizontalLayoutScore += bonusScore;
                    System.Diagnostics.Debug.WriteLine($"左边界变异度明显低于顶边界(比例:{varianceRatio:F1})，中强横排证据 => 额外横排得分 +{bonusScore}");
                }
                else if (varianceRatio > 1.5) // 差异较小但仍有意义
                {
                    int bonusScore = (int)(3 * edgeWeightFactor);
                    horizontalLayoutScore += bonusScore;
                    System.Diagnostics.Debug.WriteLine($"左边界变异度略低于顶边界(比例:{varianceRatio:F1})，弱横排证据 => 额外横排得分 +{bonusScore}");
                }
            }
            else if (evidence.TopEdgeVariance < evidence.LeftEdgeVariance * 0.7) // 进一步放宽比例阈值，从0.6到0.7
            {
                // 顶部边界比左边界更整齐 - 竖排证据
                // 例：顶部对齐的竖排文本，顶部边界一致而左边界不一致
                int baseEdgeScore = 7;  // 显著增加基础权重，从5提高到7，增强对竖排布局的识别
                int edgeScore = (int)(baseEdgeScore * edgeWeightFactor);
                verticalLayoutScore += edgeScore;
                System.Diagnostics.Debug.WriteLine($"顶边界比左边界更整齐，竖排证据 => 竖排得分 +{edgeScore} (基础分:{baseEdgeScore}, 权重:{edgeWeightFactor:F1})");
                
                // 增加额外得分，基于边缘变异差异程度
                double varianceRatio = evidence.LeftEdgeVariance / Math.Max(0.001, evidence.TopEdgeVariance);
                
                // 使用更细粒度的比例阈值，并增强竖排识别
                if (varianceRatio > 3.0) // 差异非常显著
                {
                    int bonusScore = (int)Math.Min(10, 4 + Math.Log10(varianceRatio) * 2); // 增加最大得分
                    bonusScore = (int)(bonusScore * edgeWeightFactor);
                    verticalLayoutScore += bonusScore;
                    System.Diagnostics.Debug.WriteLine($"顶边界变异度显著低于左边界(比例:{varianceRatio:F1})，强竖排证据 => 额外竖排得分 +{bonusScore}");
                }
                else if (varianceRatio > 2.0) // 差异明显
                {
                    int bonusScore = (int)Math.Min(7, 3 + Math.Log10(varianceRatio) * 1.5); // 增加得分
                    bonusScore = (int)(bonusScore * edgeWeightFactor);
                    verticalLayoutScore += bonusScore;
                    System.Diagnostics.Debug.WriteLine($"顶边界变异度明显低于左边界(比例:{varianceRatio:F1})，中强竖排证据 => 额外竖排得分 +{bonusScore}");
                }
                else if (varianceRatio > 1.5) // 差异较小但仍有意义
                {
                    int bonusScore = (int)(4 * edgeWeightFactor); // 增加得分，从3提高到4
                    verticalLayoutScore += bonusScore;
                    System.Diagnostics.Debug.WriteLine($"顶边界变异度略低于左边界(比例:{varianceRatio:F1})，弱竖排证据 => 额外竖排得分 +{bonusScore}");
                }
            }
            
            // 增加右边界和底部边界的分析
            if (evidence.RightEdgeVariance < evidence.BottomEdgeVariance * 0.7)
            {
                // 右边界比底部边界更整齐 - 可能是右对齐的横排文本
                int baseEdgeScore = 4; // 增加权重，从3提高到4
                int edgeScore = (int)(baseEdgeScore * Math.Min(1.3, edgeWeightFactor)); // 使用较小的权重因子
                horizontalLayoutScore += edgeScore;
                System.Diagnostics.Debug.WriteLine($"右边界比底边界更整齐，横排辅助证据 => 横排得分 +{edgeScore}");
                
                // 额外检查右边界变异度是否非常低
                if (evidence.RightEdgeVariance < evidence.LeftEdgeVariance * 0.5)
                {
                    // 右边界比左边界更整齐，可能是右对齐文本
                    int alignBonus = (int)(3 * Math.Min(1.3, edgeWeightFactor));
                    horizontalLayoutScore += alignBonus;
                    System.Diagnostics.Debug.WriteLine($"右边界变异度显著低于左边界，可能是右对齐文本 => 额外横排得分 +{alignBonus}");
                }
            }
            else if (evidence.BottomEdgeVariance < evidence.RightEdgeVariance * 0.7)
            {
                // 底部边界比右边界更整齐 - 可能是底部对齐的竖排文本
                int baseEdgeScore = 4; // 增加权重，从3提高到4
                int edgeScore = (int)(baseEdgeScore * Math.Min(1.3, edgeWeightFactor)); // 使用较小的权重因子
                verticalLayoutScore += edgeScore;
                System.Diagnostics.Debug.WriteLine($"底边界比右边界更整齐，竖排辅助证据 => 竖排得分 +{edgeScore}");
                
                // 额外检查底部边界变异度是否非常低
                if (evidence.BottomEdgeVariance < evidence.TopEdgeVariance * 0.5)
                {
                    // 底部边界比顶部边界更整齐，可能是底部对齐文本
                    int alignBonus = (int)(3 * Math.Min(1.3, edgeWeightFactor));
                    verticalLayoutScore += alignBonus;
                    System.Diagnostics.Debug.WriteLine($"底部边界变异度显著低于顶部边界，可能是底部对齐文本 => 额外竖排得分 +{alignBonus}");
                }
            }

            // 9. 段落特征 - 段落结构分析
            if (evidence.ParagraphCount >= 3)
            {
                // 多段落文本通常是横排的
                // 例：常见的文章结构，多个段落按横排布局
                horizontalLayoutScore += 4;
                System.Diagnostics.Debug.WriteLine($"检测到多段落结构({evidence.ParagraphCount}个)，横排证据 => 横排得分 +4");
            }

            // 增强对极端竖排布局的识别
            if (hasExtremeVerticalFeature && evidence.VerticalAlignedColumns >= 2)
            {
                // 如果有极端竖排特征且至少有2列，增加竖排得分
                int extraScore = 10;  // 提高权重，使其更有影响力
                verticalLayoutScore += extraScore;
                System.Diagnostics.Debug.WriteLine($"检测到极端竖排特征且有多列排列 => 额外竖排得分 +{extraScore}");
            }

            // 增强对极端横排布局的识别
            if (hasExtremeHorizontalFeature && evidence.HorizontalAlignedRows >= 2)
            {
                // 如果有极端横排特征且至少有2行，增加横排得分
                int extraScore = 8;  // 提高权重，使其更有影响力
                horizontalLayoutScore += extraScore;
                System.Diagnostics.Debug.WriteLine($"检测到极端横排特征且有多行排列 => 额外横排得分 +{extraScore}");
            }

            // ====================== 应用智能权重 ======================

            // 保存原始得分用于记录
            int originalVerticalScore = verticalLayoutScore;
            int originalHorizontalScore = horizontalLayoutScore;

            // 直方图分析结果直接加入评分
            int histogramScore = evidence.HistogramScore;
            
            // 使用直方图分析结果增强布局判断
            if (histogramScore != 0)
            {
                // 计算直方图分析的权重因子 - 根据直方图分数的绝对值动态调整
                double histogramAbsValue = Math.Abs(histogramScore);
                // 当直方图分数绝对值较大时，增加权重因子
                double histogramWeightFactor = 1.0;
                
                // 检查直方图特征是否非常明显
                bool isStrongHistogramFeature = false;
                
                if (this.horizontalHistogram != null && this.verticalHistogram != null)
                {
                    // 计算峰值质量差异
                    double horizontalPeakQuality = this.horizontalHistogram.PeakCount * this.horizontalHistogram.PeakClearness;
                    double verticalPeakQuality = this.verticalHistogram.PeakCount * this.verticalHistogram.PeakClearness;
                    
                    // 如果峰值质量差异显著，认为是强直方图特征
                    if (horizontalPeakQuality > 0 && verticalPeakQuality > 0)
                    {
                        double peakQualityRatio = Math.Max(horizontalPeakQuality, verticalPeakQuality) / 
                                                Math.Min(horizontalPeakQuality, verticalPeakQuality);
                        isStrongHistogramFeature = (peakQualityRatio >= 2.0);
                        
                        if (isStrongHistogramFeature)
                        {
                            // 峰值质量差异显著，增加权重因子
                            histogramWeightFactor = Math.Min(2.0, 1.0 + (peakQualityRatio - 2.0) * 0.2);
                            System.Diagnostics.Debug.WriteLine($"直方图峰值质量差异显著(比例:{peakQualityRatio:F2})，增加权重因子到{histogramWeightFactor:F2}");
                        }
                    }
                }
                
                // 根据直方图分数的绝对值进一步调整权重因子
                if (histogramAbsValue >= 30)
                {
                    // 直方图分数非常高，显著增加权重
                    histogramWeightFactor *= 1.5;
                    isStrongHistogramFeature = true;
                    System.Diagnostics.Debug.WriteLine($"直方图分数非常高({histogramAbsValue})，显著增加权重因子到{histogramWeightFactor:F2}");
                }
                else if (histogramAbsValue >= 20)
                {
                    // 直方图分数较高，中度增加权重
                    histogramWeightFactor *= 1.3;
                    System.Diagnostics.Debug.WriteLine($"直方图分数较高({histogramAbsValue})，中度增加权重因子到{histogramWeightFactor:F2}");
                }
                
                // 应用加权的直方图分数
                int weightedHistogramScore = (int)(histogramAbsValue * histogramWeightFactor);
                
                if (histogramScore > 0)
                {
                    // 直方图分析支持横排
                    horizontalLayoutScore += weightedHistogramScore;
                    System.Diagnostics.Debug.WriteLine($"直方图分析支持横排布局，增加横排得分 +{weightedHistogramScore} (原始:{histogramScore}, 权重:{histogramWeightFactor:F2})");
                    
                    // 使用保存的直方图特征进行更详细的分析
                    if (this.verticalHistogram != null && this.verticalHistogram.PeakCount > 2 && 
                        this.verticalHistogram.Regularity > 0.5)
                    {
                        // 垂直方向有多个规律的峰值，强烈暗示横排多行结构
                        int bonusScore = isStrongHistogramFeature ? 
                                        (int)(weightedHistogramScore * 0.6) : 
                                        (int)(weightedHistogramScore * 0.4);
                        horizontalLayoutScore += bonusScore;
                        System.Diagnostics.Debug.WriteLine($"垂直方向存在规律的多峰结构，强烈暗示横排多行布局 => 额外横排得分 +{bonusScore}");
                    }
                }
                else
                {
                    // 直方图分析支持竖排
                    verticalLayoutScore += weightedHistogramScore;
                    System.Diagnostics.Debug.WriteLine($"直方图分析支持竖排布局，增加竖排得分 +{weightedHistogramScore} (原始:{histogramScore}, 权重:{histogramWeightFactor:F2})");
                    
                    // 使用保存的直方图特征进行更详细的分析
                    if (this.horizontalHistogram != null && this.horizontalHistogram.PeakCount > 2 && 
                        this.horizontalHistogram.Regularity > 0.5)
                    {
                        // 水平方向有多个规律的峰值，强烈暗示竖排多列结构
                        int bonusScore = isStrongHistogramFeature ? 
                                        (int)(weightedHistogramScore * 0.6) : 
                                        (int)(weightedHistogramScore * 0.4);
                        verticalLayoutScore += bonusScore;
                        System.Diagnostics.Debug.WriteLine($"水平方向存在规律的多峰结构，强烈暗示竖排多列布局 => 额外竖排得分 +{bonusScore}");
                    }
                }
            }
            
            if (histogramScore < 0) // 直方图分析支持竖排
            {
                // 检查方形文字比例
                double squareTextRatio = CalculateSquareTextRatio(this.cells);
                if (squareTextRatio > 0.3)
                {
                    // 方形文字比例较高，通常中文竖排从右到左
                    // 增加RightToLeft证据，让方向通过整体累积计算得出
                    int rtlBonus = (int)(20 * Math.Min(1.0, squareTextRatio));
                    evidence.RightToLeftCount += rtlBonus;
                    System.Diagnostics.Debug.WriteLine($"检测到较高方形文字比例({squareTextRatio:F2})的竖排文本，增加从右到左证据 +{rtlBonus}");
                }

                // 竖排文本的水平方向通常从右到左的证据
                evidence.RightToLeftCount += 5; // 增加一个基础倾向值
                System.Diagnostics.Debug.WriteLine($"竖排布局通常倾向于从右到左，增加基础从右到左证据 +5");
            }
            else if (histogramScore > 0)
            {
                // 为横排布局增加从左到右证据
                // 横排文本默认从左到右的概率更高
                evidence.LeftToRightCount += 5; // 增加一个基础倾向值
                System.Diagnostics.Debug.WriteLine($"横排布局通常倾向于从左到右，增加基础从左到右证据 +5");
            }

            // 记录添加直方图分析后的得分
            int afterHistogramVertical = verticalLayoutScore;
            int afterHistogramHorizontal = horizontalLayoutScore;
            System.Diagnostics.Debug.WriteLine($"添加直方图分析后得分 - 竖排: {afterHistogramVertical}, 横排: {afterHistogramHorizontal}");

            // 计算权重时确保不会出现0得分
            double weightedVertical = verticalLayoutScore * shapeWeight;
            double weightedHorizontal = horizontalLayoutScore * shapeWeight;

            // 确保有意义的得分不会被权重完全消除
            verticalLayoutScore = weightedVertical < 1 && verticalLayoutScore > 0 ? 1 : (int)weightedVertical;
            horizontalLayoutScore = weightedHorizontal < 1 && horizontalLayoutScore > 0 ? 1 : (int)weightedHorizontal;

            // 增强边缘对齐分析
            var alignmentScores = AnalyzeEdgeAlignment(evidence);
            int weightedAlignmentVertical = (int)(alignmentScores.VerticalScore * alignmentWeight);
            int weightedAlignmentHorizontal = (int)(alignmentScores.HorizontalScore * alignmentWeight);
            verticalLayoutScore += weightedAlignmentVertical;
            horizontalLayoutScore += weightedAlignmentHorizontal;

            System.Diagnostics.Debug.WriteLine($"\n应用智能权重 - 竖排: {originalVerticalScore} -> {verticalLayoutScore}, 横排: {originalHorizontalScore} -> {horizontalLayoutScore}");
            System.Diagnostics.Debug.WriteLine($"边缘对齐分析得分 - 竖排: +{weightedAlignmentVertical}, 横排: +{weightedAlignmentHorizontal}");

            // ====================== 布局类型判断 ======================

            // 综合评分确定布局类型 - 分数高的布局类型胜出
            bool isVerticalLayout = verticalLayoutScore > horizontalLayoutScore;

            // 计算置信度 - 基于得分差异比例
            int totalScore = Math.Max(1, horizontalLayoutScore + verticalLayoutScore);
            int winningScore = isVerticalLayout ? verticalLayoutScore : horizontalLayoutScore;
            int opposingScore = isVerticalLayout ? horizontalLayoutScore : verticalLayoutScore;

            // 计算置信度 - 分数差异越大，置信度越高
            int layoutConfidence = CalculateLayoutConfidence(winningScore, opposingScore, totalScore);

            // 应用布局决策
            result.IsVerticalLayout = isVerticalLayout;
            result.LayoutConfidence = layoutConfidence;

            System.Diagnostics.Debug.WriteLine($"\n最终布局得分 - 竖排: {verticalLayoutScore}, 横排: {horizontalLayoutScore}");
            System.Diagnostics.Debug.WriteLine($"判定结果: {(isVerticalLayout ? "竖排" : "横排")}, 置信度: {layoutConfidence}");

            // 特殊布局处理 - 处理特殊布局场景
            ApplySpecialLayoutTypeRules(evidence, result);

            // 增强置信度 - 基于特征一致性进一步调整置信度
            AdjustConfidenceBasedOnConsistency(result, evidence);

            System.Diagnostics.Debug.WriteLine($"最终布局结果(特殊规则后): {(result.IsVerticalLayout ? "竖排" : "横排")}, 置信度: {result.LayoutConfidence}");
        }

        /// <summary>
        /// 根据布局类型和方向的一致性调整置信度
        /// </summary>
        /// <param name="result">方向检测结果</param>
        /// <param name="evidence">方向证据</param>
        private void AdjustConfidenceBasedOnConsistency(TextDirectionResult result, DirectionEvidence evidence)
        {
            System.Diagnostics.Debug.WriteLine("\n=== 一致性调整开始 ===");
            System.Diagnostics.Debug.WriteLine($"调整前 - 布局: {(result.IsVerticalLayout ? "竖排" : "横排")}, 置信度: {result.LayoutConfidence}");
            System.Diagnostics.Debug.WriteLine($"水平方向: {result.HorizontalDirection}, 置信度: {result.HorizontalConfidence}");
            System.Diagnostics.Debug.WriteLine($"垂直方向: {result.VerticalDirection}, 置信度: {result.VerticalConfidence}");

            // 检查布局类型和方向是否一致
            bool isConsistent = false;

            // 增强一致性检查，考虑更多情况
            if (!result.IsVerticalLayout)
            {
                // 横排布局一致性检查
                if (result.HorizontalDirection == TextFlowDirection.LeftToRight ||
                    result.HorizontalDirection == TextFlowDirection.RightToLeft)
                {
                    isConsistent = true;

                    // 计算一致性得分 - 基于证据强度
                    int consistencyScore = 0;

                    // 形状特征支持
                    if (evidence.HorizontalTextCount > evidence.VerticalTextCount * 1.5)
                        consistencyScore += 2;

                    // 行列结构支持
                    if (evidence.HorizontalAlignedRows > evidence.VerticalAlignedColumns)
                        consistencyScore += 2;

                    // 边界变异支持
                    if ((result.HorizontalDirection == TextFlowDirection.LeftToRight &&
                         evidence.LeftEdgeVariance < evidence.RightEdgeVariance) ||
                        (result.HorizontalDirection == TextFlowDirection.RightToLeft &&
                         evidence.RightEdgeVariance < evidence.LeftEdgeVariance))
                    {
                        consistencyScore += 1;
                    }

                    // 应用一致性增强
                    int consistencyBonus = Math.Min(15, consistencyScore * 3);
                    result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + consistencyBonus);
                    result.HorizontalConfidence = Math.Min(95, result.HorizontalConfidence + consistencyBonus / 2);

                    System.Diagnostics.Debug.WriteLine($"横排布局与水平方向一致性检查: 得分={consistencyScore}, 增加置信度 +{consistencyBonus}");
                }
            }
            else // 竖排布局
            {
                // 竖排布局一致性检查 - 与横排类似但针对垂直方向
                if (result.VerticalDirection == TextFlowDirection.TopToBottom ||
                    result.VerticalDirection == TextFlowDirection.BottomToTop)
                {
                    isConsistent = true;

                    // 计算一致性得分
                    int consistencyScore = 0;

                    // 形状特征支持 - 进一步降低阈值从1.2到1.1
                    if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.1)
                        consistencyScore += 4; // 增加得分，从3提高到4

                    // 增强对极端竖排情况的检测
                    int extremeVerticalCount = 0;
                    int highVerticalCount = 0;
                    int totalVerticalCount = 0; // 总的竖排文本块数量
                    int totalTextCount = this.cells.Count;
                    
                    // 计算动态阈值 - 基于文本块高宽比分布
                    List<double> heightWidthRatios = new List<double>();
                    foreach (var cell in this.cells)
                    {
                        if (cell?.location != null)
                        {
                            double ratio = cell.location.height / (double)cell.location.width;
                            if (ratio > 1.0) // 所有竖直方向的文本块
                            {
                                heightWidthRatios.Add(ratio);
                            }
                        }
                    }
                    
                    // 计算高宽比的百分位数
                    double extremeThreshold = 8.0; // 默认值
                    double highThreshold = 5.0;    // 默认值
                    
                    if (heightWidthRatios.Count >= 4) // 至少需要一定数量的样本
                    {
                        heightWidthRatios.Sort();
                        int p90Index = (int)(heightWidthRatios.Count * 0.9);
                        int p75Index = (int)(heightWidthRatios.Count * 0.75);
                        
                        if (p90Index < heightWidthRatios.Count)
                        {
                            extremeThreshold = Math.Max(4.0, heightWidthRatios[p90Index]); // 使用90百分位作为极端阈值
                        }
                        
                        if (p75Index < heightWidthRatios.Count)
                        {
                            highThreshold = Math.Max(3.0, heightWidthRatios[p75Index]); // 使用75百分位作为高阈值
                        }
                    }
                    
                    System.Diagnostics.Debug.WriteLine($"动态竖排阈值 - 极端: {extremeThreshold:F2}, 高: {highThreshold:F2}");
                    
                    // 计算竖排文本块的比例
                    foreach (var cell in this.cells)
                    {
                        if (cell?.location != null)
                        {
                            double ratio = cell.location.height / (double)cell.location.width;
                            if (ratio > 1.2) // 所有竖排文本块
                            {
                                totalVerticalCount++;
                                
                                if (ratio > extremeThreshold) // 超极端竖排 - 使用动态阈值
                                {
                                    extremeVerticalCount++;
                                }
                                else if (ratio > highThreshold) // 高竖排 - 使用动态阈值
                                {
                                    highVerticalCount++;
                                }
                            }
                        }
                    }
                    
                    // 计算竖排文本块占总文本块的比例
                    double verticalRatio = totalTextCount > 0 ? (double)totalVerticalCount / totalTextCount : 0;

                    System.Diagnostics.Debug.WriteLine($"极端竖排文本块: {extremeVerticalCount}, 高竖排文本块: {highVerticalCount}, 总竖排比例: {verticalRatio:P2}");

                    // 如果竖排文本块占比很高，显著增加一致性得分
                    if (verticalRatio > 0.8 && totalVerticalCount >= 3)
                    {
                        consistencyScore += 7; // 显著增加得分
                        System.Diagnostics.Debug.WriteLine($"竖排文本块占比极高({verticalRatio:P2})，显著增加一致性得分 +7");
                    }
                    else if (verticalRatio > 0.6 && totalVerticalCount >= 3)
                    {
                        consistencyScore += 5; // 中度增加得分
                        System.Diagnostics.Debug.WriteLine($"竖排文本块占比较高({verticalRatio:P2})，中度增加一致性得分 +5");
                    }

                    // 如果存在极端竖排文本块，增加一致性得分
                    if (extremeVerticalCount >= 2) // 两个或更多极端竖排
                    {
                        // 增加更高的得分
                        consistencyScore += 6; // 从5增加到6
                        System.Diagnostics.Debug.WriteLine($"检测到多个极端竖排文本块({extremeVerticalCount}个)，增加一致性得分 +6");
                    }
                    else if (extremeVerticalCount >= 1 && highVerticalCount >= 1) // 添加新条件
                    {
                        // 即使只有1个极端竖排，只要还有高竖排文本，也增加得分
                        consistencyScore += 4; // 从3增加到4
                        System.Diagnostics.Debug.WriteLine($"检测到1个极端竖排和{highVerticalCount}个高竖排文本块，增加一致性得分 +4");
                    }
                    else if (highVerticalCount >= 3) // 添加仅高竖排文本的判断
                    {
                        consistencyScore += 3; // 从2增加到3
                        System.Diagnostics.Debug.WriteLine($"检测到多个高竖排文本块({highVerticalCount}个)，增加一致性得分 +3");
                    }
                    else if (highVerticalCount >= 2) // 添加更宽松的条件
                    {
                        consistencyScore += 2;
                        System.Diagnostics.Debug.WriteLine($"检测到2个高竖排文本块，增加一致性得分 +2");
                    }
                    else if (extremeVerticalCount == 1) // 即使只有1个极端竖排，也给予一定得分
                    {
                        consistencyScore += 2;
                        System.Diagnostics.Debug.WriteLine($"检测到1个极端竖排文本块，增加一致性得分 +2");
                    }

                    // 行列结构支持 - 降低列数要求
                    if (evidence.VerticalAlignedColumns > evidence.HorizontalAlignedRows)
                        consistencyScore += 2;
                    else if (evidence.VerticalAlignedColumns >= 2) // 添加绝对列数判断
                        consistencyScore += 3;

                    // 边界变异支持
                    if ((result.VerticalDirection == TextFlowDirection.TopToBottom &&
                         evidence.TopEdgeVariance < evidence.BottomEdgeVariance) ||
                        (result.VerticalDirection == TextFlowDirection.BottomToTop &&
                         evidence.BottomEdgeVariance < evidence.TopEdgeVariance))
                    {
                        consistencyScore += 1;
                    }

                    // 分析行内文本宽度一致性 - 为中文竖排诗文特殊优化
                    if (extremeVerticalCount + highVerticalCount >= 2)
                    {
                        var verticalBlocks = this.cells
                            .Where(c => c?.location != null && c.location.height / (double)c.location.width > 5.0)
                            .ToList();

                        if (verticalBlocks.Count >= 2)
                        {
                            // 计算宽度的标准差
                            var widths = verticalBlocks.Select(c => c.location.width).ToList();
                            double avgWidth = widths.Average();
                            double widthStdDev = CalculateStandardDeviation(widths);
                            double widthCV = widthStdDev / avgWidth; // 变异系数

                            System.Diagnostics.Debug.WriteLine($"竖排文本块宽度分析 - 平均值: {avgWidth:F2}, 标准差: {widthStdDev:F2}, 变异系数: {widthCV:F2}");

                            // 如果宽度非常一致（表明是规整排版），增加一致性得分
                            if (widthCV < 0.15)
                            {
                                consistencyScore += 4;
                                System.Diagnostics.Debug.WriteLine($"竖排文本块宽度非常一致，很可能是规整排版，增加一致性得分 +4");
                            }
                            else if (widthCV < 0.25)
                            {
                                consistencyScore += 2;
                                System.Diagnostics.Debug.WriteLine($"竖排文本块宽度较一致，增加一致性得分 +2");
                            }
                        }
                    }

                    // 应用一致性增强 - 增加竖排布局的加权得分
                    // 根据一致性得分的高低使用不同的乘数
                    double multiplier;
                    if (consistencyScore >= 10) // 一致性得分非常高
                    {
                        multiplier = 3.5; // 增加乘数
                        System.Diagnostics.Debug.WriteLine($"一致性得分非常高({consistencyScore})，使用更高乘数: {multiplier:F1}");
                    }
                    else if (consistencyScore >= 7) // 一致性得分较高
                    {
                        multiplier = 3.2; // 中等乘数
                        System.Diagnostics.Debug.WriteLine($"一致性得分较高({consistencyScore})，使用中等乘数: {multiplier:F1}");
                    }
                    else
                    {
                        multiplier = 3.0; // 基础乘数
                    }
                    
                    int consistencyBonus = Math.Min(25, (int)(consistencyScore * multiplier)); // 增加最大值到25
                    
                    // 如果存在大量极端竖排文本块，额外增加布局置信度
                    if (extremeVerticalCount >= 3 || (extremeVerticalCount >= 1 && highVerticalCount >= 3))
                    {
                        consistencyBonus = Math.Min(30, consistencyBonus + 5); // 额外增加5点，最大30
                        System.Diagnostics.Debug.WriteLine($"存在大量极端竖排文本块，额外增加布局置信度 +5");
                    }
                    
                    result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + consistencyBonus);
                    result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + consistencyBonus / 2);

                    System.Diagnostics.Debug.WriteLine($"竖排布局与垂直方向一致性检查: 得分={consistencyScore}, 乘数={multiplier:F1}, 增加置信度 +{consistencyBonus}");
                }
            }

            // 处理不一致情况
            if (!isConsistent)
            {
                // 检查不一致程度
                int primaryConfidence = result.IsVerticalLayout ?
                    result.VerticalConfidence : result.HorizontalConfidence;

                // 如果主方向置信度高但布局置信度低，调整布局以匹配主方向
                if (primaryConfidence > result.LayoutConfidence + 25)
                {
                    // 重新评估布局方向
                    if (result.HorizontalConfidence > result.VerticalConfidence + 20)
                    {
                        // 水平方向明显更可信
                        result.IsVerticalLayout = false;
                        result.LayoutConfidence = (result.LayoutConfidence + result.HorizontalConfidence) / 2;
                        System.Diagnostics.Debug.WriteLine($"方向不一致修正: 水平方向置信度明显更高，调整为横排布局");
                    }
                    else if (result.VerticalConfidence > result.HorizontalConfidence + 20)
                    {
                        // 垂直方向明显更可信
                        result.IsVerticalLayout = true;
                        result.LayoutConfidence = (result.LayoutConfidence + result.VerticalConfidence) / 2;
                        System.Diagnostics.Debug.WriteLine($"方向不一致修正: 垂直方向置信度明显更高，调整为竖排布局");
                    }
                }
            }

            // 特殊情况处理：竖排布局中的垂直方向检查 - 基于统计特征
            if (result.IsVerticalLayout)
            {
                // 1. 基于方向证据评估垂直方向
                if (evidence.TopToBottomCount > evidence.BottomToTopCount * 1.5)
                {
                    // 有明显的从上到下的证据
                    if (result.VerticalDirection != TextFlowDirection.TopToBottom)
                    {
                        // 修正垂直方向
                        result.VerticalDirection = TextFlowDirection.TopToBottom;
                        result.VerticalConfidence = Math.Max(70, result.VerticalConfidence);
                        System.Diagnostics.Debug.WriteLine($"竖排布局中检测到强烈的从上到下证据，修正垂直方向为TopToBottom");
                    }
                }
                else if (evidence.BottomToTopCount > evidence.TopToBottomCount * 1.5)
                {
                    // 有明显的从下到上的证据
                    if (result.VerticalDirection != TextFlowDirection.BottomToTop)
                    {
                        // 修正垂直方向
                        result.VerticalDirection = TextFlowDirection.BottomToTop;
                        result.VerticalConfidence = Math.Max(70, result.VerticalConfidence);
                        System.Diagnostics.Debug.WriteLine($"竖排布局中检测到强烈的从下到上证据，修正垂直方向为BottomToTop");
                    }
                }
                else
                {
                    // 2. 如果方向证据不明确，使用内容分布特征进行判断
                    TextLayoutFeatures features = null;

                    // 判断features是否在局部作用域内可用
                    if (features != null)
                    {
                        if (features.ContentDensityTopHalf > features.ContentDensityBottomHalf * 1.2)
                        {
                            // 上半页内容密度更高，倾向于从上到下
                            result.VerticalDirection = TextFlowDirection.TopToBottom;
                            result.VerticalConfidence = Math.Max(65, result.VerticalConfidence);
                            System.Diagnostics.Debug.WriteLine($"基于上半页内容密度较高，设置垂直方向为TopToBottom");
                        }
                        else if (features.ContentDensityBottomHalf > features.ContentDensityTopHalf * 1.2)
                        {
                            // 下半页内容密度更高，倾向于从下到上
                            result.VerticalDirection = TextFlowDirection.BottomToTop;
                            result.VerticalConfidence = Math.Max(65, result.VerticalConfidence);
                            System.Diagnostics.Debug.WriteLine($"基于下半页内容密度较高，设置垂直方向为BottomToTop");
                        }
                        else if (evidence.VerticalAlignedColumns >= 2)
                        {
                            // 内容分布也不明确，但有多列，默认使用从上到下（通用性更好）
                            result.VerticalDirection = TextFlowDirection.TopToBottom;
                            result.VerticalConfidence = Math.Max(60, result.VerticalConfidence);
                            System.Diagnostics.Debug.WriteLine($"方向证据和内容分布均不明确，但有多列，默认使用TopToBottom");
                        }
                    }
                    else if (evidence.VerticalAlignedColumns >= 2)
                    {
                        // features不可用，但有多列，默认使用从上到下
                        result.VerticalDirection = TextFlowDirection.TopToBottom;
                        result.VerticalConfidence = Math.Max(60, result.VerticalConfidence);
                        System.Diagnostics.Debug.WriteLine($"垂直方向证据不明确，但检测到多列，默认使用TopToBottom");
                    }
                }

                // 3. 基于形状特征增强布局置信度
                if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5)
                {
                    // 竖排文本明显占优，增强竖排布局置信度
                    int verticalBonus = Math.Min(15, (evidence.VerticalTextCount - evidence.HorizontalTextCount) / 2);
                    result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + verticalBonus);
                    result.VerticalConfidence = Math.Min(95, result.VerticalConfidence + verticalBonus / 2);

                    System.Diagnostics.Debug.WriteLine($"竖排文本明显占优({evidence.VerticalTextCount}:{evidence.HorizontalTextCount})，增强竖排布局置信度 +{verticalBonus}");
                }
            }

            System.Diagnostics.Debug.WriteLine($"调整后 - 布局: {(result.IsVerticalLayout ? "竖排" : "横排")}, 置信度: {result.LayoutConfidence}");
            System.Diagnostics.Debug.WriteLine($"水平方向: {result.HorizontalDirection}, 置信度: {result.HorizontalConfidence}");
            System.Diagnostics.Debug.WriteLine($"垂直方向: {result.VerticalDirection}, 置信度: {result.VerticalConfidence}");
            System.Diagnostics.Debug.WriteLine("=== 一致性调整结束 ===\n");
        }

        /// <summary>
        /// 计算布局置信度
        /// 基于得分差异比例和总分动态计算置信度，适用于任何得分分布
        /// </summary>
        /// <param name="winningScore">获胜方向的得分</param>
        /// <param name="opposingScore">相反方向的得分</param>
        /// <param name="totalScore">总得分</param>
        /// <returns>计算得出的置信度（55-95）</returns>
        private int CalculateLayoutConfidence(int winningScore, int opposingScore, int totalScore)
        {
            // 基础置信度基于分数差异比例
            // 使用相对差异比例而非绝对差异，使结果更通用
            double scoreDiff = winningScore - opposingScore;
            double diffRatio = scoreDiff / Math.Max(1, totalScore);

            // 映射到置信度范围：55-95
            // 下限保证最低可信度，上限避免过于武断
            int baseConfidence = 55 + (int)(diffRatio * 40);

            // 高总分提升置信度 - 更多证据支持时提升置信度
            // 使用平方根函数使增长合理
            int scoreBoost = (int)Math.Min(10, Math.Sqrt(totalScore));

            return Math.Min(95, baseConfidence + scoreBoost);
        }

        /// <summary>
        /// 应用特殊布局类型规则
        /// 处理各种特殊布局场景，增强系统的通用性
        /// </summary>
        /// <param name="evidence">收集的方向证据</param>
        /// <param name="result">文本方向结果，可能被调整</param>
        private void ApplySpecialLayoutTypeRules(DirectionEvidence evidence, TextDirectionResult result)
        {
            // 特殊布局规则用于处理一些极端或特殊情况
            // 这些规则基于具体的布局特征，适用范围广

            System.Diagnostics.Debug.WriteLine("\n--- 应用特殊布局规则 ---");

            // 1. 多列竖排文本布局
            // 垂直排列的文本列是非常明显的竖排特征
            if (evidence.VerticalAlignedColumns >= 2 &&
                evidence.VerticalTextCount > evidence.HorizontalTextCount &&
                evidence.HorizontalAlignedRows <= 1)
            {
                result.IsVerticalLayout = true;
                int oldConfidence = result.LayoutConfidence;
                // 提高置信度，但不超过95
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + 15);

                // 同时强制设置水平方向为从右到左
                result.HorizontalDirection = TextFlowDirection.RightToLeft;
                result.HorizontalConfidence = Math.Max(result.HorizontalConfidence, 90);

                System.Diagnostics.Debug.WriteLine($"特殊规则1: 检测到多列竖排布局 => 布局置信度从{oldConfidence}提升到{result.LayoutConfidence}，强制水平方向为从右到左，置信度{result.HorizontalConfidence}");
            }

            // 2. 多行横排文本布局
            // 水平排列的文本行是非常明显的横排特征
            if (evidence.HorizontalAlignedRows >= 2 &&
                evidence.HorizontalTextCount > evidence.VerticalTextCount &&
                evidence.VerticalAlignedColumns <= 1)
            {
                result.IsVerticalLayout = false;
                int oldConfidence = result.LayoutConfidence;
                // 提高置信度，但不超过95
                result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + 10);

                System.Diagnostics.Debug.WriteLine($"特殊规则2: 检测到多行横排布局 => 置信度从{oldConfidence}提升到{result.LayoutConfidence}");
            }

            // 3. 极端高宽比文本块特征 - 新增规则
            // 检查是否存在极端高宽比特征，这些特征对布局判断非常重要
            // 获取TextLayoutFeatures对象，以访问极端高宽比信息
            TextLayoutFeatures features = ExtractTextFeatures(this.cells);
            
            // 如果存在极端竖排文本块，且数量足够多
            if (features.ExtremeVerticalCount >= 2 && 
                features.ExtremeVerticalCount > features.ExtremeHorizontalCount)
            {
                // 如果当前判断为横排，但有强烈的竖排特征，考虑修正
                if (!result.IsVerticalLayout && result.LayoutConfidence < 85)
                {
                    // 重新判断为竖排
                    result.IsVerticalLayout = true;
                    int oldConfidence = result.LayoutConfidence;
                    
                    // 设置较高的置信度，特别是对于极端高宽比较多的情况
                    int newConfidence = 80; // 基础置信度
                    
                    // 根据极端竖排文本块数量和比例动态调整置信度
                    if (features.ExtremeVerticalCount >= 4 || features.ExtremeVerticalRatio >= 0.4)
                    {
                        newConfidence = 90; // 非常高的置信度
                        System.Diagnostics.Debug.WriteLine($"特殊规则3: 检测到大量极端竖排文本块({features.ExtremeVerticalCount}个，占比{features.ExtremeVerticalRatio:P2})，强制修正为竖排布局");
                    }
                    else if (features.ExtremeVerticalCount >= 3 || features.ExtremeVerticalRatio >= 0.3)
                    {
                        newConfidence = 85; // 较高的置信度
                    }
                    
                    result.LayoutConfidence = Math.Max(newConfidence, result.LayoutConfidence);
                    
                    // 同时设置水平方向为从右到左（中文竖排通常从右到左）
                    result.HorizontalDirection = TextFlowDirection.RightToLeft;
                    result.HorizontalConfidence = Math.Max(result.HorizontalConfidence, 85);
                    
                    System.Diagnostics.Debug.WriteLine($"特殊规则3: 检测到{features.ExtremeVerticalCount}个极端竖排文本块(占比{features.ExtremeVerticalRatio:P2})，修正为竖排布局 => 置信度从{oldConfidence}调整到{result.LayoutConfidence}");
                }
                else if (result.IsVerticalLayout)
                {
                    // 如果已经判断为竖排，进一步提高置信度
                    int oldConfidence = result.LayoutConfidence;
                    
                    // 根据极端竖排文本块数量动态调整置信度提升
                    int confidenceBoost = Math.Min(20, 10 + features.ExtremeVerticalCount * 2);
                    result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + confidenceBoost);
                    
                    // 同时确保水平方向为从右到左
                    if (features.ExtremeVerticalCount >= 3)
                    {
                        result.HorizontalDirection = TextFlowDirection.RightToLeft;
                        result.HorizontalConfidence = Math.Max(result.HorizontalConfidence, 85);
                    }
                    
                    if (oldConfidence != result.LayoutConfidence)
                    {
                        System.Diagnostics.Debug.WriteLine($"特殊规则3: 检测到{features.ExtremeVerticalCount}个极端竖排文本块，增强竖排判断 => 置信度从{oldConfidence}提升到{result.LayoutConfidence}");
                    }
                }
            }
            // 如果存在极端横排文本块，且数量足够多
            else if (features.ExtremeHorizontalCount >= 2 && 
                     features.ExtremeHorizontalCount > features.ExtremeVerticalCount)
            {
                // 如果当前判断为竖排，但有强烈的横排特征，考虑修正
                if (result.IsVerticalLayout && result.LayoutConfidence < 85)
                {
                    // 重新判断为横排
                    result.IsVerticalLayout = false;
                    int oldConfidence = result.LayoutConfidence;
                    
                    // 设置较高的置信度，特别是对于极端高宽比较多的情况
                    int newConfidence = 80; // 基础置信度
                    
                    // 根据极端横排文本块数量和比例动态调整置信度
                    if (features.ExtremeHorizontalCount >= 4 || features.ExtremeHorizontalRatio >= 0.4)
                    {
                        newConfidence = 90; // 非常高的置信度
                        System.Diagnostics.Debug.WriteLine($"特殊规则3: 检测到大量极端横排文本块({features.ExtremeHorizontalCount}个，占比{features.ExtremeHorizontalRatio:P2})，强制修正为横排布局");
                    }
                    else if (features.ExtremeHorizontalCount >= 3 || features.ExtremeHorizontalRatio >= 0.3)
                    {
                        newConfidence = 85; // 较高的置信度
                    }
                    
                    result.LayoutConfidence = Math.Max(newConfidence, result.LayoutConfidence);
                    
                    System.Diagnostics.Debug.WriteLine($"特殊规则3: 检测到{features.ExtremeHorizontalCount}个极端横排文本块(占比{features.ExtremeHorizontalRatio:P2})，修正为横排布局 => 置信度从{oldConfidence}调整到{result.LayoutConfidence}");
                }
                else if (!result.IsVerticalLayout)
                {
                    // 如果已经判断为横排，进一步提高置信度
                    int oldConfidence = result.LayoutConfidence;
                    
                    // 根据极端横排文本块数量动态调整置信度提升
                    int confidenceBoost = Math.Min(15, 8 + features.ExtremeHorizontalCount * 2);
                    result.LayoutConfidence = Math.Min(95, result.LayoutConfidence + confidenceBoost);
                    
                    if (oldConfidence != result.LayoutConfidence)
                    {
                        System.Diagnostics.Debug.WriteLine($"特殊规则3: 检测到{features.ExtremeHorizontalCount}个极端横排文本块，增强横排判断 => 置信度从{oldConfidence}提升到{result.LayoutConfidence}");
                    }
                }
            }

            // 5. 文本内容极少的情况
            if (evidence.HorizontalTextCount + evidence.VerticalTextCount < 5 &&
                result.LayoutConfidence > 60)
            {
                int oldConfidence = result.LayoutConfidence;
                // 文本内容很少时，降低我们的判断置信度
                result.LayoutConfidence = Math.Min(result.LayoutConfidence, 70);

                if (oldConfidence != result.LayoutConfidence)
                {
                    System.Diagnostics.Debug.WriteLine($"特殊规则5: 文本内容极少 => 降低置信度从{oldConfidence}到{result.LayoutConfidence}");
                }
            }
        }

        /// <summary>
        /// 统一的文本布局特征数据结构
        /// </summary>
        private class TextLayoutFeatures
        {
            // 文本块基本统计
            public int TotalTextBlocks { get; set; } = 0;
            public int ValidTextBlocks { get; set; } = 0;

            // 文本形状分布
            public List<double> HeightWidthRatios { get; set; } = new List<double>();
            public List<double> WidthHeightRatios { get; set; } = new List<double>();

            // 形状统计特征
            public double MedianHeightWidthRatio { get; set; } = 1.0;
            public double MedianWidthHeightRatio { get; set; } = 1.0;
            public double HeightWidthRatioVariance { get; set; } = 0;
            public double WidthHeightRatioVariance { get; set; } = 0;

            // 形状分布百分位数（用于动态阈值）
            public double VerticalRatio_P90 { get; set; } = 7.0; // 90百分位高宽比，替代固定阈值
            public double VerticalRatio_P75 { get; set; } = 5.0; // 75百分位高宽比
            public double VerticalRatio_P50 { get; set; } = 1.5; // 中位数高宽比

            public double HorizontalRatio_P90 { get; set; } = 7.0; // 90百分位宽高比
            public double HorizontalRatio_P75 { get; set; } = 5.0; // 75百分位宽高比
            public double HorizontalRatio_P50 { get; set; } = 1.5; // 中位数宽高比

            // 文本形状计数（基于动态阈值）
            public int VerticalTextCount { get; set; } = 0;   // 高>宽的文本块
            public int HorizontalTextCount { get; set; } = 0; // 宽>高的文本块
            public int SquareTextCount { get; set; } = 0;     // 近似正方形的文本块
            
            // 极端高宽比文本块计数
            public int ExtremeVerticalCount { get; set; } = 0;   // 极端高宽比(高>>宽)的文本块
            public int HighVerticalCount { get; set; } = 0;      // 高竖排(高>宽)但不极端的文本块
            public int ExtremeHorizontalCount { get; set; } = 0; // 极端宽高比(宽>>高)的文本块
            public double ExtremeVerticalRatio { get; set; } = 0; // 极端竖排文本块占比
            public double ExtremeHorizontalRatio { get; set; } = 0; // 极端横排文本块占比

            // 文本块大小统计
            public double MedianWidth { get; set; } = 0;
            public double MedianHeight { get; set; } = 0;
            public double WidthVariance { get; set; } = 0;
            public double HeightVariance { get; set; } = 0;

            // 边缘位置统计
            public List<double> LeftEdges { get; set; } = new List<double>();
            public List<double> RightEdges { get; set; } = new List<double>();
            public List<double> TopEdges { get; set; } = new List<double>();
            public List<double> BottomEdges { get; set; } = new List<double>();

            // 边缘变异特征
            public double LeftEdgeVariance { get; set; } = 0;
            public double RightEdgeVariance { get; set; } = 0;
            public double TopEdgeVariance { get; set; } = 0;
            public double BottomEdgeVariance { get; set; } = 0;

            // 边缘变异比例（用于方向判断）
            public double LeftRightEdgeVarianceRatio { get; set; } = 1.0;
            public double TopBottomEdgeVarianceRatio { get; set; } = 1.0;

            // 行列结构特征
            public int HorizontalRowCount { get; set; } = 0;
            public int VerticalColumnCount { get; set; } = 0;
            public List<List<TextCellInfo>> HorizontalRows { get; set; } = new List<List<TextCellInfo>>();
            public List<List<TextCellInfo>> VerticalColumns { get; set; } = new List<List<TextCellInfo>>();

            // 行列间距统计
            public List<double> RowGaps { get; set; } = new List<double>();
            public List<double> ColumnGaps { get; set; } = new List<double>();
            public double MedianRowGap { get; set; } = 0;
            public double MedianColumnGap { get; set; } = 0;
            public double RowGapVariance { get; set; } = 0;
            public double ColumnGapVariance { get; set; } = 0;

            // 行列规律性（变异系数，越小越规律）
            public double RowGapRegularity { get; set; } = 1.0;
            public double ColumnGapRegularity { get; set; } = 1.0;

            // 对齐特征
            public int LeftAlignedCount { get; set; } = 0;
            public int RightAlignedCount { get; set; } = 0;
            public int TopAlignedCount { get; set; } = 0;
            public int BottomAlignedCount { get; set; } = 0;

            // 段落特征
            public int ParagraphCount { get; set; } = 1;
            public double ParagraphGapThreshold { get; set; } = 0;
            public List<double> ParagraphGaps { get; set; } = new List<double>();

            // 内容分布特征
            public double ContentDensityLeftHalf { get; set; } = 0; // 左半页内容密度
            public double ContentDensityRightHalf { get; set; } = 0; // 右半页内容密度
            public double ContentDensityTopHalf { get; set; } = 0;   // 上半页内容密度
            public double ContentDensityBottomHalf { get; set; } = 0; // 下半页内容密度

            // 页面范围
            public double PageLeft { get; set; } = 0;
            public double PageRight { get; set; } = 0;
            public double PageTop { get; set; } = 0;
            public double PageBottom { get; set; } = 0;
            public double PageWidth { get; set; } = 0;
            public double PageHeight { get; set; } = 0;
            public double PageCenter_X { get; set; } = 0;
            public double PageCenter_Y { get; set; } = 0;

            // 特征关系
            public double RowColumnRatio { get; set; } = 1.0;
            public double VerticalHorizontalRatio { get; set; } = 1.0;
            public double LeftAlignmentRatio { get; set; } = 0.5;
            public double TopAlignmentRatio { get; set; } = 0.5;

            // 方向证据 - 新增字段
            public int LeftToRightEvidence { get; set; } = 0;
            public int RightToLeftEvidence { get; set; } = 0;
            public int TopToBottomEvidence { get; set; } = 0;
            public int BottomToTopEvidence { get; set; } = 0;
        }

        /// <summary>
        /// 方向证据数据结构
        /// </summary>
        private class DirectionEvidence
        {
            // 通用布局特征
            public int LeftToRightCount { get; set; } = 0;
            public int RightToLeftCount { get; set; } = 0;
            public int TopToBottomCount { get; set; } = 0;
            public int BottomToTopCount { get; set; } = 0;

            // 文本特征
            public int HorizontalTextCount { get; set; } = 0;  // 宽>高的文本块数量
            public int VerticalTextCount { get; set; } = 0;    // 高>宽的文本块数量

            // 段落特征
            public int ParagraphCount { get; set; } = 0;       // 识别出的段落数量

            // 布局统计
            public int HorizontalAlignedRows { get; set; } = 0;  // 水平排列的行数
            public int VerticalAlignedColumns { get; set; } = 0; // 垂直排列的列数

            // 对齐特征
            public int LeftAlignedCount { get; set; } = 0;     // 左对齐数量
            public int RightAlignedCount { get; set; } = 0;    // 右对齐数量
            public int TopAlignedCount { get; set; } = 0;      // 顶部对齐数量 
            public int BottomAlignedCount { get; set; } = 0;   // 底部对齐数量

            // 对齐差异特征
            public double LeftEdgeVariance { get; set; } = 0;  // 左边界变异度
            public double RightEdgeVariance { get; set; } = 0; // 右边界变异度
            public double TopEdgeVariance { get; set; } = 0;   // 顶部边界变异度
            public double BottomEdgeVariance { get; set; } = 0;// 底部边界变异度

            // 文本布局特征
            public bool IsSequentialParagraphs { get; set; } = false;  // 是否为连续段落布局
            public double LeftAlignmentRatio { get; set; } = 0;        // 左对齐比例
            
            // 直方图分析结果
            public int HistogramScore { get; set; } = 0;       // 直方图分析得分，正值表示倾向横排，负值表示倾向竖排
        }

        /// <summary>
        /// 将相似的值分组
        /// </summary>
        private List<List<double>> GroupSimilarValues(List<double> values, double threshold)
        {
            var groups = new List<List<double>>();

            foreach (var value in values)
            {
                bool addedToExisting = false;

                foreach (var group in groups)
                {
                    if (Math.Abs(group[0] - value) <= threshold)
                    {
                        group.Add(value);
                        addedToExisting = true;
                        break;
                    }
                }

                if (!addedToExisting)
                {
                    groups.Add(new List<double> { value });
                }
            }

            return groups;
        }

        /// <summary>
        /// 综合分析方向证据，确定最终方向
        /// 使用得分累加系统来判断方向，适用于各种语言和文档类型
        /// </summary>
        /// <param name="evidence">收集的方向证据</param>
        /// <param name="result">输出的方向结果</param>
        /// <param name="cells">文本单元格列表</param>
        private void AnalyzeDirectionEvidence(DirectionEvidence evidence, TextDirectionResult result, List<TextCellInfo> cells)
        {
            // 使用得分累加系统，而非直接设置置信度
            // 这种方法更加通用，可以处理各种语言和排版方式

            // ====================== 水平方向分析 ======================
            // 水平方向得分系统，分别计算从左到右和从右到左的证据得分
            int leftToRightScore = 0;
            int rightToLeftScore = 0;

            // 计算基于文本块数量的基准权重 - 动态基准
            int baseWeight = Math.Max(1, Math.Min(5, cells.Count / 10));

            // 1.1 基础方向证据 - 基础权重
            leftToRightScore += evidence.LeftToRightCount;
            rightToLeftScore += evidence.RightToLeftCount;

            // 1.2 对齐特征分析 - 使用相对比例
            if (evidence.LeftAlignedCount > 0 || evidence.RightAlignedCount > 0)
            {
                double totalAligned = evidence.LeftAlignedCount + evidence.RightAlignedCount;
                if (totalAligned > 0)
                {
                    double leftAlignRatio = evidence.LeftAlignedCount / totalAligned;
                    double rightAlignRatio = evidence.RightAlignedCount / totalAligned;

                    // 对齐比例差异阈值 - 动态计算 - 降低阈值，提高对齐特征的敏感度
                    double alignDiffThreshold = Math.Max(0.10, 0.15 - cells.Count * 0.002);

                    // 对齐比例差异超过阈值才有明显意义
                    if (leftAlignRatio > rightAlignRatio + alignDiffThreshold)
                    {
                        double strengthFactor = Math.Min(1.0, (leftAlignRatio - rightAlignRatio) / 0.4);
                        int weightedScore = (int)(Math.Min(evidence.LeftAlignedCount, cells.Count / 2) * strengthFactor);
                        leftToRightScore += weightedScore;
                    }
                    else if (rightAlignRatio > leftAlignRatio + alignDiffThreshold)
                    {
                        double strengthFactor = Math.Min(1.0, (rightAlignRatio - leftAlignRatio) / 0.4);
                        int weightedScore = (int)(Math.Min(evidence.RightAlignedCount, cells.Count / 2) * strengthFactor);
                        rightToLeftScore += weightedScore;
                    }
                }
            }

            // 1.3 边界变异特征分析 - 使用动态阈值
            if (evidence.LeftEdgeVariance > 0 && evidence.RightEdgeVariance > 0)
            {
                double edgeVarianceRatio = evidence.LeftEdgeVariance / evidence.RightEdgeVariance;

                // 边缘变异比率阈值 - 动态计算，更加敏感
                double lrLowThreshold = Math.Max(0.6, 0.7 - cells.Count * 0.002);
                double lrHighThreshold = Math.Min(1.6, 1.4 + cells.Count * 0.002);

                // 使用比率而非固定倍数判断
                if (edgeVarianceRatio < lrLowThreshold) // 左边界比右边界更整齐
                {
                    // 增加差异程度的计算，使得权重更具连续性
                    double strengthFactor = Math.Min(1.0, (lrLowThreshold - edgeVarianceRatio) / 0.3);
                    // 调高权重计算中的系数，从3增加到4，增强边缘变异特征影响力
                    leftToRightScore += (int)(baseWeight * 4 * strengthFactor);
                }
                else if (edgeVarianceRatio > lrHighThreshold) // 右边界比左边界更整齐
                {
                    double strengthFactor = Math.Min(1.0, (edgeVarianceRatio - lrHighThreshold) / 0.5);
                    rightToLeftScore += (int)(baseWeight * 4 * strengthFactor);
                }
                // 添加中间区域的细化处理
                else if (edgeVarianceRatio < 0.85) // 微弱左边界整齐倾向
                {
                    double strengthFactor = Math.Min(0.6, (0.85 - edgeVarianceRatio) / 0.25);
                    leftToRightScore += (int)(baseWeight * 2 * strengthFactor);
                }
                else if (edgeVarianceRatio > 1.15) // 微弱右边界整齐倾向
                {
                    double strengthFactor = Math.Min(0.6, (edgeVarianceRatio - 1.15) / 0.25);
                    rightToLeftScore += (int)(baseWeight * 2 * strengthFactor);
                }
            }

            // 1.4 形状特征分析 - 使用相对权重
            // 横向文本块比例 - 考虑文本块总数
            if (evidence.HorizontalTextCount > 0 || evidence.VerticalTextCount > 0)
            {
                double totalTextCount = evidence.HorizontalTextCount + evidence.VerticalTextCount;
                if (totalTextCount > 0)
                {
                    double horizontalRatio = evidence.HorizontalTextCount / totalTextCount;
                    
                    // 降低横向文本占比阈值，从0.6降至0.55，使特征更敏感
                    if (horizontalRatio > 0.55 && evidence.HorizontalTextCount >= 3)
                    {
                        double strengthFactor = Math.Min(1.0, (horizontalRatio - 0.55) / 0.45);
                        int weightedScore = (int)(baseWeight * 5 * strengthFactor);
                        leftToRightScore += weightedScore;
                    }
                    // 添加对极端水平形状的处理，增强算法对明显特征的响应
                    else if (horizontalRatio > 0.75 && evidence.HorizontalTextCount >= 2)
                    {
                        // 明显的横向文本占优，额外增加左到右的证据
                        int bonusScore = (int)(baseWeight * 3 * (horizontalRatio / 0.75));
                        leftToRightScore += bonusScore;
                    }
                }
            }

            // 新增：竖直文本形状特征对水平方向的影响
            if (evidence.VerticalTextCount > evidence.HorizontalTextCount * 1.5)
            {
                // 竖排文本明显占优，增加右到左的倾向
                double verticalTextRatio = Math.Min(1.0, (double)evidence.VerticalTextCount / 
                                         Math.Max(1, evidence.HorizontalTextCount) / 3.0);
                int rtlBonus = (int)(baseWeight * 3 * verticalTextRatio);
                rightToLeftScore += rtlBonus;
            }

            // 1.5 行列结构分析 - 使用相对权重
            // 行数与列数比较
            if (evidence.HorizontalAlignedRows > 0 || evidence.VerticalAlignedColumns > 0)
            {
                double totalRowColCount = evidence.HorizontalAlignedRows + evidence.VerticalAlignedColumns;
                if (totalRowColCount > 0)
                {
                    double rowRatio = evidence.HorizontalAlignedRows / totalRowColCount;

                    // 降低行占比阈值，从0.6降至0.55，增强敏感度
                    if (rowRatio > 0.55 && evidence.HorizontalAlignedRows >= 2)
                    {
                        double strengthFactor = Math.Min(1.0, (rowRatio - 0.55) / 0.45);
                        int weightedScore = (int)(baseWeight * 4 * strengthFactor);
                        leftToRightScore += weightedScore;
                    }
                    // 添加对列结构显著情况的处理
                    else if (evidence.VerticalAlignedColumns >= 2 && rowRatio < 0.45)
                    {
                        // 列结构明显，增加右到左的证据
                        double colStrength = Math.Min(1.0, (0.45 - rowRatio) / 0.45);
                        int rtlBonus = (int)(baseWeight * 3 * colStrength);
                        rightToLeftScore += rtlBonus;
                    }
                }
            }

            // 1.6 段落特征分析
            if (evidence.ParagraphCount >= 2)
            {
                // 多段落文本通常是从左到右阅读的
                int paragraphBonus = Math.Min(10, evidence.ParagraphCount * 2);
                leftToRightScore += paragraphBonus;
            }

            // 1.7 内容密度分析
            // 左右内容密度比较，基于统计特征而非语言假设
            if (evidence.LeftEdgeVariance > 0 && evidence.RightEdgeVariance > 0)
            {
                // 降低边界变异判断的阈值，从0.8降至0.75，增强敏感度
                if (evidence.LeftEdgeVariance < evidence.RightEdgeVariance * 0.75)
                {
                    leftToRightScore += baseWeight * 3;  // 增加权重从2到3
                }
                else if (evidence.RightEdgeVariance < evidence.LeftEdgeVariance * 0.75)
                {
                    rightToLeftScore += baseWeight * 3;  // 增加权重从2到3
                }
                // 添加对微弱差异的处理
                else if (evidence.LeftEdgeVariance < evidence.RightEdgeVariance * 0.9)
                {
                    leftToRightScore += baseWeight;
                }
                else if (evidence.RightEdgeVariance < evidence.LeftEdgeVariance * 0.9)
                {
                    rightToLeftScore += baseWeight;
                }
            }

            // ====================== 垂直方向分析 ======================
            // 垂直方向的得分系统，类似修改省略...
            int topToBottomScore = 0;
            int bottomToTopScore = 0;

            // 2.1 基础方向证据 - 基础权重
            topToBottomScore += evidence.TopToBottomCount;
            bottomToTopScore += evidence.BottomToTopCount;

            // 2.2 对齐特征分析 - 使用相对比例
            if (evidence.TopAlignedCount > 0 || evidence.BottomAlignedCount > 0)
            {
                double totalAligned = evidence.TopAlignedCount + evidence.BottomAlignedCount;
                if (totalAligned > 0)
                {
                    double topAlignRatio = evidence.TopAlignedCount / totalAligned;
                    double bottomAlignRatio = evidence.BottomAlignedCount / totalAligned;

                    // 对齐比例差异阈值 - 动态计算 - 降低阈值
                    double alignDiffThreshold = Math.Max(0.10, 0.15 - cells.Count * 0.002);

                    // 对齐比例差异超过阈值才有明显意义
                    if (topAlignRatio > bottomAlignRatio + alignDiffThreshold)
                    {
                        double strengthFactor = Math.Min(1.0, (topAlignRatio - bottomAlignRatio) / 0.4);
                        int weightedScore = (int)(Math.Min(evidence.TopAlignedCount, cells.Count / 2) * strengthFactor);
                        topToBottomScore += weightedScore;
                    }
                    else if (bottomAlignRatio > topAlignRatio + alignDiffThreshold)
                    {
                        double strengthFactor = Math.Min(1.0, (bottomAlignRatio - topAlignRatio) / 0.4);
                        int weightedScore = (int)(Math.Min(evidence.BottomAlignedCount, cells.Count / 2) * strengthFactor);
                        bottomToTopScore += weightedScore;
                    }
                }
            }

            // 2.3 边界变异特征分析 - 使用动态阈值
            if (evidence.TopEdgeVariance > 0 && evidence.BottomEdgeVariance > 0)
            {
                double edgeVarianceRatio = evidence.TopEdgeVariance / evidence.BottomEdgeVariance;

                // 边缘变异比率阈值 - 动态计算
                double tbLowThreshold = Math.Max(0.6, 0.7 - cells.Count * 0.002);
                double tbHighThreshold = Math.Min(1.6, 1.4 + cells.Count * 0.002);

                // 使用比率而非固定倍数判断
                if (edgeVarianceRatio < tbLowThreshold) // 上边界比下边界更整齐
                {
                    double strengthFactor = Math.Min(1.0, (tbLowThreshold - edgeVarianceRatio) / 0.3);
                    topToBottomScore += (int)(baseWeight * 4 * strengthFactor);
                }
                else if (edgeVarianceRatio > tbHighThreshold) // 下边界比上边界更整齐
                {
                    double strengthFactor = Math.Min(1.0, (edgeVarianceRatio - tbHighThreshold) / 0.5);
                    bottomToTopScore += (int)(baseWeight * 4 * strengthFactor);
                }
                // 添加中间区域的细化处理
                else if (edgeVarianceRatio < 0.85) // 微弱上边界整齐倾向
                {
                    double strengthFactor = Math.Min(0.6, (0.85 - edgeVarianceRatio) / 0.25);
                    topToBottomScore += (int)(baseWeight * 2 * strengthFactor);
                }
                else if (edgeVarianceRatio > 1.15) // 微弱下边界整齐倾向
                {
                    double strengthFactor = Math.Min(0.6, (edgeVarianceRatio - 1.15) / 0.25);
                    bottomToTopScore += (int)(baseWeight * 2 * strengthFactor);
                }
            }

            // 2.4 形状特征分析 - 使用相对权重与连续函数
            if (evidence.HorizontalTextCount > 0 || evidence.VerticalTextCount > 0)
            {
                double totalTextCount = evidence.HorizontalTextCount + evidence.VerticalTextCount;
                if (totalTextCount > 0)
                {
                    double verticalRatio = evidence.VerticalTextCount / totalTextCount;
                    
                    // 降低竖向文本占比阈值，从0.6降至0.55
                    if (verticalRatio > 0.55 && evidence.VerticalTextCount >= 3)
                    {
                        double strengthFactor = Math.Min(1.0, (verticalRatio - 0.55) / 0.45);
                        int weightedScore = (int)(baseWeight * 5 * strengthFactor);
                        topToBottomScore += weightedScore;
                    }
                    // 添加对极端竖直形状的处理
                    else if (verticalRatio > 0.75 && evidence.VerticalTextCount >= 2)
                    {
                        // 明显的竖向文本占优，额外增加上到下的证据
                        int bonusScore = (int)(baseWeight * 3 * (verticalRatio / 0.75));
                        topToBottomScore += bonusScore;
                    }
                }
            }

            // 2.5 行列结构分析
            if (evidence.HorizontalAlignedRows > 0 || evidence.VerticalAlignedColumns > 0)
            {
                double totalRowColCount = evidence.HorizontalAlignedRows + evidence.VerticalAlignedColumns;
                if (totalRowColCount > 0)
                {
                    double colRatio = evidence.VerticalAlignedColumns / totalRowColCount;

                    // 降低列占比阈值，从0.6降至0.55
                    if (colRatio > 0.55 && evidence.VerticalAlignedColumns >= 2)
                    {
                        double strengthFactor = Math.Min(1.0, (colRatio - 0.55) / 0.45);
                        int weightedScore = (int)(baseWeight * 4 * strengthFactor);
                        topToBottomScore += weightedScore;
                    }
                    // 添加对行结构显著情况的处理
                    else if (evidence.HorizontalAlignedRows >= 2 && colRatio < 0.45)
                    {
                        double rowStrength = Math.Min(1.0, (0.45 - colRatio) / 0.45);
                        int ttbBonus = (int)(baseWeight * 3 * rowStrength);
                        topToBottomScore += ttbBonus;
                    }
                }
            }

            // 2.6 内容密度分析
            if (evidence.TopEdgeVariance > 0 && evidence.BottomEdgeVariance > 0)
            {
                // 降低阈值，增加敏感度
                if (evidence.TopEdgeVariance < evidence.BottomEdgeVariance * 0.75)
                {
                    topToBottomScore += baseWeight * 3;
                }
                else if (evidence.BottomEdgeVariance < evidence.TopEdgeVariance * 0.75)
                {
                    bottomToTopScore += baseWeight * 3;
                }
                // 添加对微弱差异的处理
                else if (evidence.TopEdgeVariance < evidence.BottomEdgeVariance * 0.9)
                {
                    topToBottomScore += baseWeight;
                }
                else if (evidence.BottomEdgeVariance < evidence.TopEdgeVariance * 0.9)
                {
                    bottomToTopScore += baseWeight;
                }
            }

            // 边缘变异总量分析 - 增强这一特征的影响
            if (evidence.LeftEdgeVariance > 0 && evidence.RightEdgeVariance > 0 &&
                evidence.TopEdgeVariance > 0 && evidence.BottomEdgeVariance > 0)
            {
                // 计算边缘变异总量
                double horizontalEdgeVarianceTotal = evidence.LeftEdgeVariance + evidence.RightEdgeVariance;
                double verticalEdgeVarianceTotal = evidence.TopEdgeVariance + evidence.BottomEdgeVariance;
                
                // 只有当总量差异足够大时才进行判断
                if (Math.Min(horizontalEdgeVarianceTotal, verticalEdgeVarianceTotal) > 0)
                {
                    // 计算边缘变异总量比例
                    double edgeVarianceRatio = verticalEdgeVarianceTotal / horizontalEdgeVarianceTotal;
                    
                    // 使用对数变换处理比例，使判断更敏感
                    double logRatio = Math.Log10(Math.Max(0.1, edgeVarianceRatio));
                    
                    // 上下边缘变异总体远小于左右边缘变异 - 强横排特征
                    if (logRatio < -0.15) // 对应比例约0.7以下
                    {
                        int bonusScore = (int)(baseWeight * 4 * Math.Min(1.5, Math.Abs(logRatio)));
                        leftToRightScore += bonusScore;
                        topToBottomScore += bonusScore / 2; // 同时适度影响垂直方向
                    }
                    // 左右边缘变异总体远小于上下边缘变异 - 强竖排特征
                    else if (logRatio > 0.15) // 对应比例约1.4以上
                    {
                        int bonusScore = (int)(baseWeight * 4 * Math.Min(1.5, Math.Abs(logRatio)));
                        rightToLeftScore += bonusScore;
                        topToBottomScore += bonusScore / 2; // 同时适度影响垂直方向
                    }
                }
            }

            // ====================== 方向决策 ======================
            // 使用直方图分析结果增强方向判断
            if (this.horizontalHistogram != null && this.verticalHistogram != null && evidence.HistogramScore != 0)
            {
                // 使用直方图特征增强水平方向判断
                if (evidence.HistogramScore > 0) // 直方图分析支持横排
                {
                    // 增加左到右的证据
                    int histogramBonus = Math.Min(10, Math.Abs(evidence.HistogramScore) / 2);
                    leftToRightScore += histogramBonus;
                    System.Diagnostics.Debug.WriteLine($"使用直方图分析增强水平方向判断，增加左到右得分 +{histogramBonus}");
                }
                else if (evidence.HistogramScore < 0) // 直方图分析支持竖排
                {
                    // 增加右到左的证据
                    int histogramBonus = Math.Min(10, Math.Abs(evidence.HistogramScore) / 2);
                    rightToLeftScore += histogramBonus;
                    System.Diagnostics.Debug.WriteLine($"使用直方图分析增强水平方向判断，增加右到左得分 +{histogramBonus}");
                }
            }
            
            // 处理横竖方向特征相似的情况
            // 计算水平和垂直方向的总得分
            int horizontalTotalScore = leftToRightScore + rightToLeftScore;
            int verticalTotalScore = topToBottomScore + bottomToTopScore;
            
            // 如果两个方向的总得分相差不大，使用额外特征进行区分
            double scoreDiffRatio = Math.Abs(horizontalTotalScore - verticalTotalScore) / (double)Math.Max(1, Math.Max(horizontalTotalScore, verticalTotalScore));
            
            if (scoreDiffRatio < 0.2) // 两个方向的得分相差不大，需要额外判断
            {
                System.Diagnostics.Debug.WriteLine($"水平和垂直方向特征相似(差异比例:{scoreDiffRatio:F2})，使用额外特征进行判断");
                
                // 使用边缘变异特征进行额外判断
                double leftRightVarianceRatio = evidence.LeftEdgeVariance / Math.Max(0.001, evidence.RightEdgeVariance);
                double topBottomVarianceRatio = evidence.TopEdgeVariance / Math.Max(0.001, evidence.BottomEdgeVariance);
                
                // 边缘变异比例差异越大，越能说明方向特征
                if (Math.Abs(Math.Log10(leftRightVarianceRatio)) > Math.Abs(Math.Log10(topBottomVarianceRatio)) * 1.2)
                {
                    // 水平方向边缘变异差异更显著
                    int bonusScore = (int)(baseWeight * 3 * Math.Min(1.5, Math.Abs(Math.Log10(leftRightVarianceRatio))));
                    
                    if (leftRightVarianceRatio < 0.7) // 左边界更整齐
                    {
                        leftToRightScore += bonusScore;
                        System.Diagnostics.Debug.WriteLine($"水平边缘变异差异显著(左边界更整齐)，增加左到右得分 +{bonusScore}");
                    }
                    else if (leftRightVarianceRatio > 1.4) // 右边界更整齐
                    {
                        rightToLeftScore += bonusScore;
                        System.Diagnostics.Debug.WriteLine($"水平边缘变异差异显著(右边界更整齐)，增加右到左得分 +{bonusScore}");
                    }
                }
                else if (Math.Abs(Math.Log10(topBottomVarianceRatio)) > Math.Abs(Math.Log10(leftRightVarianceRatio)) * 1.2)
                {
                    // 垂直方向边缘变异差异更显著
                    int bonusScore = (int)(baseWeight * 3 * Math.Min(1.5, Math.Abs(Math.Log10(topBottomVarianceRatio))));
                    
                    if (topBottomVarianceRatio < 0.7) // 上边界更整齐
                    {
                        topToBottomScore += bonusScore;
                        System.Diagnostics.Debug.WriteLine($"垂直边缘变异差异显著(上边界更整齐)，增加上到下得分 +{bonusScore}");
                    }
                    else if (topBottomVarianceRatio > 1.4) // 下边界更整齐
                    {
                        bottomToTopScore += bonusScore;
                        System.Diagnostics.Debug.WriteLine($"垂直边缘变异差异显著(下边界更整齐)，增加下到上得分 +{bonusScore}");
                    }
                }
                
                // 使用直方图特征进行额外判断
                if (this.horizontalHistogram != null && this.verticalHistogram != null)
                {
                    double horizontalPeakQuality = this.horizontalHistogram.PeakCount * this.horizontalHistogram.PeakClearness * this.horizontalHistogram.Regularity;
                    double verticalPeakQuality = this.verticalHistogram.PeakCount * this.verticalHistogram.PeakClearness * this.verticalHistogram.Regularity;
                    
                    double peakQualityRatio = Math.Max(horizontalPeakQuality, verticalPeakQuality) / Math.Max(0.001, Math.Min(horizontalPeakQuality, verticalPeakQuality));
                    
                    if (peakQualityRatio > 1.5) // 峰值质量差异显著
                    {
                        int bonusScore = (int)(baseWeight * 2 * Math.Min(2.0, peakQualityRatio / 1.5));
                        
                        if (horizontalPeakQuality > verticalPeakQuality)
                        {
                            // 水平方向直方图特征更好，支持竖排布局
                            rightToLeftScore += bonusScore;
                            System.Diagnostics.Debug.WriteLine($"水平方向直方图峰值质量更高，支持竖排布局，增加右到左得分 +{bonusScore}");
                        }
                        else
                        {
                            // 垂直方向直方图特征更好，支持横排布局
                            leftToRightScore += bonusScore;
                            System.Diagnostics.Debug.WriteLine($"垂直方向直方图峰值质量更高，支持横排布局，增加左到右得分 +{bonusScore}");
                        }
                    }
                }
            }
            
            // 水平方向决策
            TextFlowDirection horizontalDirection = TextFlowDirection.LeftToRight; // 默认从左到右
            int horizontalConfidence = 0;

            // 如果有足够的证据，确定水平方向
            if (leftToRightScore > 0 || rightToLeftScore > 0)
            {
                // 计算总分和差异
                int totalHorizontalScore = leftToRightScore + rightToLeftScore;
                int horizontalDiff = Math.Abs(leftToRightScore - rightToLeftScore);

                // 计算置信度 - 基于分数差异和总分
                horizontalConfidence = CalculateConfidenceFromScore(
                    Math.Max(leftToRightScore, rightToLeftScore),
                    Math.Min(leftToRightScore, rightToLeftScore),
                    totalHorizontalScore);

                // 确定方向 - 得分高的方向胜出
                if (rightToLeftScore > leftToRightScore)
                {
                    horizontalDirection = TextFlowDirection.RightToLeft;
                }
            }
            else
            {
                // 没有足够的证据，使用默认方向和低置信度
                horizontalConfidence = 60;
            }

            // 垂直方向决策
            TextFlowDirection verticalDirection = TextFlowDirection.TopToBottom; // 默认从上到下
            int verticalConfidence = 0;

            // 如果有足够的证据，确定垂直方向
            if (topToBottomScore > 0 || bottomToTopScore > 0)
            {
                // 计算总分和差异
                int totalVerticalScore = topToBottomScore + bottomToTopScore;
                int verticalDiff = Math.Abs(topToBottomScore - bottomToTopScore);

                // 计算置信度 - 基于分数差异和总分
                verticalConfidence = CalculateConfidenceFromScore(
                    Math.Max(topToBottomScore, bottomToTopScore),
                    Math.Min(topToBottomScore, bottomToTopScore),
                    totalVerticalScore);

                // 确定方向 - 得分高的方向胜出
                if (bottomToTopScore > topToBottomScore)
                {
                    verticalDirection = TextFlowDirection.BottomToTop;
                }
            }
            else
            {
                // 没有足够的证据，使用默认方向和低置信度
                verticalConfidence = 60;
            }

            // 设置结果
            result.HorizontalDirection = horizontalDirection;
            result.HorizontalConfidence = horizontalConfidence;
            result.VerticalDirection = verticalDirection;
            result.VerticalConfidence = verticalConfidence;
        }

        /// <summary>
        /// 计算置信度分数
        /// </summary>
        /// <param name="winningScore">获胜方向的得分</param>
        /// <param name="opposingScore">对立方向的得分</param>
        /// <param name="totalScore">总得分</param>
        /// <returns>计算出的置信度（55-95）</returns>
        private int CalculateConfidenceFromScore(int winningScore, int opposingScore, int totalScore)
        {
            // 基础置信度基于分数差异比例
            // 使用相对差异比例而非绝对差异，使结果更通用
            double scoreDiff = winningScore - opposingScore;
            double diffRatio = scoreDiff / Math.Max(1, totalScore);

            // 映射到置信度范围：55-95
            // 下限保证最低可信度，上限避免过于武断
            int baseConfidence = 55 + (int)(diffRatio * 40);

            // 高总分提升置信度 - 更多证据支持时提升置信度
            // 使用平方根函数使增长合理
            int scoreBoost = (int)Math.Min(10, Math.Sqrt(totalScore));

            return Math.Min(95, baseConfidence + scoreBoost);
        }

        /// <summary>
        /// 计算自适应分组阈值，基于文本块高宽比分布
        /// </summary>
        /// <param name="cells">文本单元格列表</param>
        private void CalculateAdaptiveThresholds(List<TextCellInfo> cells)
        {
            // 初始化默认阈值
            double defaultThresholdVertical = 25.0;
            double defaultThresholdHorizontal = 10.0;

            if (cells.Count < 2)
            {
                groupingThresholdVertical = defaultThresholdVertical;
                groupingThresholdHorizontal = defaultThresholdHorizontal;
                return;
            }

            // 计算比例分布
            var ratios = cells
                .Where(c => c?.location != null)
                .Select(c => Math.Max(
                    c.location.height / (double)c.location.width,
                    c.location.width / (double)c.location.height))
                .OrderBy(r => r)
                .ToList();

            if (ratios.Count == 0)
            {
                groupingThresholdVertical = defaultThresholdVertical;
                groupingThresholdHorizontal = defaultThresholdHorizontal;
                return;
            }

            // 检查是否存在极端比例（高于7:1）
            bool hasExtremeRatio = ratios.Any(r => r > 7.0);
            // 检查是否存在超极端比例（高于10:1）
            bool hasSuperExtremeRatio = ratios.Any(r => r > 10.0);

            // 分析比例分布，计算高比例文本块的百分比
            int extremeRatioCount = ratios.Count(r => r > 5.0);
            double extremeRatioPercentage = (double)extremeRatioCount / ratios.Count;

            // 计算自适应阈值
            double adaptiveVerticalThreshold;
            double adaptiveHorizontalThreshold;

            // 根据极端比例的存在情况，自适应调整阈值
            if (hasSuperExtremeRatio)
            {
                System.Diagnostics.Debug.WriteLine("检测到超极端竖排比例(>10:1)，使用特殊阈值");
                adaptiveVerticalThreshold = defaultThresholdVertical * 2.0; // 更大的阈值
                adaptiveHorizontalThreshold = defaultThresholdHorizontal * 1.8; // 也调整横向阈值
            }
            else if (hasExtremeRatio)
            {
                System.Diagnostics.Debug.WriteLine("检测到极端比例(>7:1)，调整阈值");
                adaptiveVerticalThreshold = defaultThresholdVertical * 1.5; // 调整阈值
                adaptiveHorizontalThreshold = defaultThresholdHorizontal * 1.3;
            }
            else if (extremeRatioPercentage > 0.3) // 如果30%以上的文本块都有较高比例
            {
                adaptiveVerticalThreshold = defaultThresholdVertical * 1.2;
                adaptiveHorizontalThreshold = defaultThresholdHorizontal * 1.2;
            }
            else
            {
                // 使用标准阈值
                adaptiveVerticalThreshold = defaultThresholdVertical;
                adaptiveHorizontalThreshold = defaultThresholdHorizontal;
            }

            // 安全检查 - 确保阈值在合理范围内
            groupingThresholdVertical = Math.Max(15.0, Math.Min(adaptiveVerticalThreshold, 100.0));
            groupingThresholdHorizontal = Math.Max(5.0, Math.Min(adaptiveHorizontalThreshold, 50.0));

            // 计算极端竖排比例的自适应阈值
            // 这个阈值用于判断哪些文本块是极端竖排的
            if (ratios.Count >= 5)
            {
                // 使用百分位数来判断极端比例
                // 找出前20%的比例值作为极端比例的起点
                int percentileIndex = Math.Max(0, (int)(ratios.Count * 0.8) - 1);
                double percentileValue = ratios[percentileIndex];

                // 取5和百分位数的较大值，确保阈值不会太低
                extremeVerticalRatioThreshold = Math.Max(5.0, percentileValue);

                System.Diagnostics.Debug.WriteLine($"自适应极端竖排比例阈值: {extremeVerticalRatioThreshold:F1}");
            }
            else
            {
                // 默认值
                extremeVerticalRatioThreshold = 7.0;
            }

            System.Diagnostics.Debug.WriteLine($"自适应阈值 - 垂直: {groupingThresholdVertical:F1}, 水平: {groupingThresholdHorizontal:F1}, 存在极端比例: {hasExtremeRatio}, 超极端比例: {hasSuperExtremeRatio}");
        }

        /// <summary>
        /// 计算标准差
        /// </summary>
        private double CalculateStandardDeviation(IEnumerable<double> values)
        {
            double avg = values.Average();
            double sumOfSquaresOfDifferences = values.Select(val => (val - avg) * (val - avg)).Sum();
            double variance = sumOfSquaresOfDifferences / values.Count();
            return Math.Sqrt(variance);
        }

        /// <summary>
        /// 提取统一的文本布局特征
        /// </summary>
        /// <param name="cells">文本单元格列表</param>
        /// <returns>文本布局特征对象</returns>
        private TextLayoutFeatures ExtractTextFeatures(List<TextCellInfo> cells)
        {
            var features = new TextLayoutFeatures();

            // 过滤有效的文本块
            var validCells = cells.Where(c =>
                c.location.width >= 5 &&
                c.location.height >= 5 &&
                !string.IsNullOrWhiteSpace(c.words)).ToList();

            features.ValidTextBlocks = validCells.Count;

            if (validCells.Count < 2) return features;

            // 1. 提取形状特征
            ExtractShapeFeatures(validCells, features);

            // 2. 提取边缘特征
            ExtractEdgeFeatures(validCells, features);

            // 3. 提取行列结构特征
            ExtractRowColumnFeatures(validCells, features);

            // 4. 提取对齐特征
            ExtractAlignmentFeatures(validCells, features);

            // 5. 提取段落特征
            ExtractParagraphFeatures(validCells, features);

            // 6. 提取内容分布特征
            ExtractContentDistributionFeatures(validCells, features);

            // 7. 提取文本方向特征
            ExtractTextDirectionFeatures(validCells, features);

            // 8. 计算特征比率和关系
            CalculateFeatureRelationships(features);

            return features;
        }

        /// <summary>
        /// 提取文本方向特征 - 特别关注竖排文本的水平方向判断
        /// </summary>
        /// <param name="cells">文本单元格列表</param>
        /// <param name="features">文本布局特征</param>
        private void ExtractTextDirectionFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            if (cells.Count < 3) return;

            // 检测是否为竖排文本
            bool isLikelyVerticalText = features.VerticalTextCount > features.HorizontalTextCount * 1.2; // 降低阈值，从1.5降低到1.2
            bool isLikelyHorizontalText = features.HorizontalTextCount > features.VerticalTextCount * 1.2; // 添加水平文本判断

            // 根据文本类型选择不同的方向特征提取逻辑
            if (isLikelyVerticalText)
            {
                ExtractVerticalTextDirectionFeatures(cells, features);
            }
            else if (isLikelyHorizontalText)
            {
                ExtractHorizontalTextDirectionFeatures(cells, features);
            }
            else
            {
                // 当没有明显倾向时，同时提取两种方向特征，但使用较低权重
                // 这确保在不确定情况下，仍能获取基本的方向证据
                ExtractVerticalTextDirectionFeatures(cells, features, 0.7); // 使用较低权重
                ExtractHorizontalTextDirectionFeatures(cells, features, 0.7); // 使用较低权重
                
                // 添加默认基础证据 - 确保即使特征不明显也有基本方向倾向
                if (features.TopBottomEdgeVarianceRatio < 1.0)
                {
                    features.TopToBottomEvidence += 1;
                }
                else
                {
                    features.BottomToTopEvidence += 1;
                }

                if (features.LeftRightEdgeVarianceRatio < 1.0)
                {
                    features.LeftToRightEvidence += 1;
                }
                else
                {
                    features.RightToLeftEvidence += 1;
                }
            }

            // 基于边缘变异进行额外的方向判断
            if (features.LeftEdgeVariance > 0 && features.RightEdgeVariance > 0)
            {
                double leftRightDiffRatio = Math.Abs(features.LeftEdgeVariance - features.RightEdgeVariance) / 
                                          Math.Max(features.LeftEdgeVariance, features.RightEdgeVariance);
                                          
                // 边缘变异有明显差异时提供额外的方向证据
                if (leftRightDiffRatio > 0.2)
                {
                    if (features.LeftEdgeVariance < features.RightEdgeVariance)
                    {
                        features.LeftToRightEvidence += 2;
                    }
                    else
                    {
                        features.RightToLeftEvidence += 2;
                    }
                }
            }

            if (features.TopEdgeVariance > 0 && features.BottomEdgeVariance > 0)
            {
                double topBottomDiffRatio = Math.Abs(features.TopEdgeVariance - features.BottomEdgeVariance) / 
                                          Math.Max(features.TopEdgeVariance, features.BottomEdgeVariance);
                                          
                // 边缘变异有明显差异时提供额外的方向证据
                if (topBottomDiffRatio > 0.2)
                {
                    if (features.TopEdgeVariance < features.BottomEdgeVariance)
                    {
                        features.TopToBottomEvidence += 2;
                    }
                    else
                    {
                        features.BottomToTopEvidence += 2;
                    }
                }
            }
            
            // 基于边缘变异的绝对值差异进行判断
            if (features.TopEdgeVariance + features.BottomEdgeVariance < 
                (features.LeftEdgeVariance + features.RightEdgeVariance) * 0.7)
            {
                // 上下边缘变异总体远小于左右边缘变异，强横排特征
                features.LeftToRightEvidence += 3;
                features.TopToBottomEvidence += 1;
            }
            else if ((features.LeftEdgeVariance + features.RightEdgeVariance) < 
                    (features.TopEdgeVariance + features.BottomEdgeVariance) * 0.7)
            {
                // 左右边缘变异总体远小于上下边缘变异，强竖排特征
                features.RightToLeftEvidence += 3;
                features.TopToBottomEvidence += 1;
            }
        }

        /// <summary>
        /// 提取竖排文本的方向特征
        /// </summary>
        private void ExtractVerticalTextDirectionFeatures(List<TextCellInfo> cells, TextLayoutFeatures features, double weightFactor = 1.0)
        {
            // 1. 检查多列情况下的阅读顺序
            if (features.VerticalColumnCount >= 2)
            {
                // 按X坐标排序列
                var sortedColumns = features.VerticalColumns.OrderBy(col =>
                    col.Average(c => c.location.left)).ToList();

                // 检查首列和末列的文本密度
                if (sortedColumns.Count >= 2)
                {
                    var firstColumn = sortedColumns.First();
                    var lastColumn = sortedColumns.Last();

                    double firstColumnDensity = firstColumn.Sum(c => c.location.width * c.location.height);
                    double lastColumnDensity = lastColumn.Sum(c => c.location.width * c.location.height);

                    // 首列密度明显高于末列，可能是从左到右阅读
                    if (firstColumnDensity > lastColumnDensity * 1.3)
                    {
                        features.LeftToRightEvidence += (int)(3 * weightFactor);
                    }
                    // 末列密度明显高于首列，可能是从右到左阅读
                    else if (lastColumnDensity > firstColumnDensity * 1.3)
                    {
                        features.RightToLeftEvidence += (int)(3 * weightFactor);
                    }
                }

                // 2. 检查列间距的规律性
                var columnCenters = features.VerticalColumns.Select(col =>
                    col.Average(c => c.location.left + c.location.width / 2)).OrderBy(x => x).ToList();

                if (columnCenters.Count >= 3)
                {
                    // 计算列间距
                    List<double> columnGaps = new List<double>();
                    for (int i = 1; i < columnCenters.Count; i++)
                    {
                        columnGaps.Add(columnCenters[i] - columnCenters[i - 1]);
                    }

                    // 检查列间距是否从左到右递增（可能表示从右到左阅读）
                    bool increasingGaps = true;
                    for (int i = 1; i < columnGaps.Count; i++)
                    {
                        if (columnGaps[i] < columnGaps[i - 1] * 0.8)
                        {
                            increasingGaps = false;
                            break;
                        }
                    }

                    // 检查列间距是否从左到右递减（可能表示从左到右阅读）
                    bool decreasingGaps = true;
                    for (int i = 1; i < columnGaps.Count; i++)
                    {
                        if (columnGaps[i] > columnGaps[i - 1] * 0.8)
                        {
                            decreasingGaps = false;
                            break;
                        }
                    }

                    if (increasingGaps)
                    {
                        features.RightToLeftEvidence += (int)(2 * weightFactor);
                    }
                    else if (decreasingGaps)
                    {
                        features.LeftToRightEvidence += (int)(2 * weightFactor);
                    }
                }
            }

            // 3. 分析单个竖排文本块的内部特征
            var verticalTextBlocks = cells.Where(c => c.location.height > c.location.width * 1.8).ToList(); // 降低阈值从2.0到1.8
            if (verticalTextBlocks.Count >= 3)
            {
                // 检查字符分布 - 对于中日韩文字，可以通过分析Unicode范围判断
                int cjkCharCount = 0;
                int totalCharCount = 0;

                foreach (var cell in verticalTextBlocks)
                {
                    if (string.IsNullOrEmpty(cell.words)) continue;

                    foreach (char c in cell.words)
                    {
                        totalCharCount++;
                        // 检查是否为中日韩文字（粗略判断）
                        if (c >= 0x4E00 && c <= 0x9FFF) // 基本汉字范围
                        {
                            cjkCharCount++;
                        }
                    }
                }

                // 如果大部分是中日韩文字，增加从右到左的证据
                // 但这只是一个统计特征，不是绝对规则
                if (totalCharCount > 0 && (double)cjkCharCount / totalCharCount > 0.7)
                {
                    // 根据CJK文字比例适度增加证据
                    double cjkRatio = (double)cjkCharCount / totalCharCount;
                    features.RightToLeftEvidence += (int)(2 * cjkRatio * weightFactor);
                    features.TopToBottomEvidence += (int)(1 * cjkRatio * weightFactor);
                }
            }

            // 4. 分析页面整体布局
            // 计算页面中心
            double pageWidth = cells.Max(c => c.location.left + c.location.width);
            double pageCenter = pageWidth / 2;

            // 计算文本块在页面左右两侧的分布
            int leftSideBlocks = 0;
            int rightSideBlocks = 0;

            foreach (var cell in verticalTextBlocks)
            {
                double cellCenter = cell.location.left + cell.location.width / 2;
                if (cellCenter < pageCenter)
                {
                    leftSideBlocks++;
                }
                else
                {
                    rightSideBlocks++;
                }
            }

            // 如果右侧文本块明显多于左侧，可能是从右到左阅读
            if (rightSideBlocks > leftSideBlocks * 1.5 && rightSideBlocks >= 3)
            {
                features.RightToLeftEvidence += (int)(2 * weightFactor);
            }
            // 如果左侧文本块明显多于右侧，可能是从左到右阅读
            else if (leftSideBlocks > rightSideBlocks * 1.5 && leftSideBlocks >= 3)
            {
                features.LeftToRightEvidence += (int)(2 * weightFactor);
            }
        }

        /// <summary>
        /// 提取横排文本的方向特征
        /// </summary>
        private void ExtractHorizontalTextDirectionFeatures(List<TextCellInfo> cells, TextLayoutFeatures features, double weightFactor = 1.0)
        {
            // 1. 检查多行情况下的阅读顺序
            if (features.HorizontalRowCount >= 2)
            {
                // 按Y坐标排序行
                var sortedRows = features.HorizontalRows.OrderBy(row =>
                    row.Average(c => c.location.top)).ToList();

                // 检查首行和末行的文本密度
                if (sortedRows.Count >= 2)
                {
                    var firstRow = sortedRows.First();
                    var lastRow = sortedRows.Last();

                    double firstRowDensity = firstRow.Sum(c => c.location.width * c.location.height);
                    double lastRowDensity = lastRow.Sum(c => c.location.width * c.location.height);

                    // 首行密度明显高于末行，可能是从上到下阅读
                    if (firstRowDensity > lastRowDensity * 1.3)
                    {
                        features.TopToBottomEvidence += (int)(3 * weightFactor);
                    }
                    // 末行密度明显高于首行，可能是从下到上阅读
                    else if (lastRowDensity > firstRowDensity * 1.3)
                    {
                        features.BottomToTopEvidence += (int)(3 * weightFactor);
                    }
                }

                // 2. 检查行间距的规律性
                var rowCenters = features.HorizontalRows.Select(row =>
                    row.Average(c => c.location.top + c.location.height / 2)).OrderBy(y => y).ToList();

                if (rowCenters.Count >= 3)
                {
                    // 计算行间距
                    List<double> rowGaps = new List<double>();
                    for (int i = 1; i < rowCenters.Count; i++)
                    {
                        rowGaps.Add(rowCenters[i] - rowCenters[i - 1]);
                    }

                    // 检查行间距是否从上到下递增（可能表示从下到上阅读）
                    bool increasingGaps = true;
                    for (int i = 1; i < rowGaps.Count; i++)
                    {
                        if (rowGaps[i] < rowGaps[i - 1] * 0.8)
                        {
                            increasingGaps = false;
                            break;
                        }
                    }

                    // 检查行间距是否从上到下递减（可能表示从上到下阅读）
                    bool decreasingGaps = true;
                    for (int i = 1; i < rowGaps.Count; i++)
                    {
                        if (rowGaps[i] > rowGaps[i - 1] * 0.8)
                        {
                            decreasingGaps = false;
                            break;
                        }
                    }

                    if (increasingGaps)
                    {
                        features.BottomToTopEvidence += (int)(2 * weightFactor);
                    }
                    else if (decreasingGaps)
                    {
                        features.TopToBottomEvidence += (int)(2 * weightFactor);
                    }
                }
            }

            // 3. 分析单个横排文本块的内部特征
            var horizontalTextBlocks = cells.Where(c => c.location.width > c.location.height * 1.8).ToList();
            if (horizontalTextBlocks.Count >= 3)
            {
                // 默认添加从左到右的基础证据 - 大多数语言是从左到右阅读的
                features.LeftToRightEvidence += (int)(2 * weightFactor);
                
                // 检查字符分布
                int rtlCharCount = 0; // 右到左字符计数（如阿拉伯文、希伯来文）
                int totalCharCount = 0;

                foreach (var cell in horizontalTextBlocks)
                {
                    if (string.IsNullOrEmpty(cell.words)) continue;

                    foreach (char c in cell.words)
                    {
                        totalCharCount++;
                        // 检查是否为右到左书写的语言字符（简单判断）
                        if ((c >= 0x0600 && c <= 0x06FF) || // 阿拉伯文
                            (c >= 0x0590 && c <= 0x05FF))    // 希伯来文
                        {
                            rtlCharCount++;
                        }
                    }
                }

                // 如果存在明显的RTL字符，增加从右到左的证据
                if (totalCharCount > 0 && (double)rtlCharCount / totalCharCount > 0.4)
                {
                    double rtlRatio = (double)rtlCharCount / totalCharCount;
                    // 抵消之前的LTR证据，转而增加RTL证据
                    features.LeftToRightEvidence -= (int)(2 * weightFactor); 
                    features.RightToLeftEvidence += (int)(3 * rtlRatio * weightFactor);
                }
            }

            // 4. 检查对齐情况下的方向证据
            if (features.LeftAlignedCount >= 3 && features.LeftAlignedCount > features.RightAlignedCount * 1.5)
            {
                // 左对齐明显多于右对齐，通常表示从左到右阅读
                features.LeftToRightEvidence += (int)(3 * weightFactor);
            }
            else if (features.RightAlignedCount >= 3 && features.RightAlignedCount > features.LeftAlignedCount * 1.5)
            {
                // 右对齐明显多于左对齐，通常表示从右到左阅读
                features.RightToLeftEvidence += (int)(3 * weightFactor);
            }

            // 5. 分析行内字符分布（仅当有足够字符时）
            if (horizontalTextBlocks.Count >= 5)
            {
                // 添加基础从上到下的方向证据
                features.TopToBottomEvidence += (int)(2 * weightFactor);
            }
        }

        /// <summary>
        /// 计算特征之间的关系
        /// </summary>
        /// <param name="features">文本布局特征</param>
        private void CalculateFeatureRelationships(TextLayoutFeatures features)
        {
            // 计算边缘变异比率
            if (features.RightEdgeVariance > 0)
            {
                features.LeftRightEdgeVarianceRatio = features.LeftEdgeVariance / features.RightEdgeVariance;
            }

            if (features.BottomEdgeVariance > 0)
            {
                features.TopBottomEdgeVarianceRatio = features.TopEdgeVariance / features.BottomEdgeVariance;
            }

            // 计算行列比例
            if (features.VerticalColumnCount > 0)
            {
                features.RowColumnRatio = (double)features.HorizontalRowCount / features.VerticalColumnCount;
            }

            // 计算形状比例
            if (features.HorizontalTextCount > 0)
            {
                features.VerticalHorizontalRatio = (double)features.VerticalTextCount / features.HorizontalTextCount;
            }

            // 计算对齐比例
            double totalHorizontalAligned = features.LeftAlignedCount + features.RightAlignedCount;
            if (totalHorizontalAligned > 0)
            {
                features.LeftAlignmentRatio = features.LeftAlignedCount / totalHorizontalAligned;
            }

            double totalVerticalAligned = features.TopAlignedCount + features.BottomAlignedCount;
            if (totalVerticalAligned > 0)
            {
                features.TopAlignmentRatio = features.TopAlignedCount / totalVerticalAligned;
            }
        }

        /// <summary>
        /// 提取文本形状特征
        /// </summary>
        private void ExtractShapeFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 计算所有文本块的高宽比和宽高比
            foreach (var cell in cells)
            {
                double hwRatio = cell.location.height / (double)cell.location.width;
                double whRatio = cell.location.width / (double)cell.location.height;

                features.HeightWidthRatios.Add(hwRatio);
                features.WidthHeightRatios.Add(whRatio);
            }

            // 按高宽比排序，用于计算百分位数
            var sortedHWRatios = features.HeightWidthRatios.OrderBy(r => r).ToList();
            var sortedWHRatios = features.WidthHeightRatios.OrderBy(r => r).ToList();

            // 计算统计特征
            if (sortedHWRatios.Count > 0)
            {
                // 中位数
                int midIndex = sortedHWRatios.Count / 2;
                features.MedianHeightWidthRatio = sortedHWRatios[midIndex];
                features.MedianWidthHeightRatio = sortedWHRatios[midIndex];

                // 方差
                features.HeightWidthRatioVariance = CalculateVariance(sortedHWRatios);
                features.WidthHeightRatioVariance = CalculateVariance(sortedWHRatios);

                // 百分位数
                if (sortedHWRatios.Count >= 4)
                {
                    // 90百分位
                    int p90Index = (int)(sortedHWRatios.Count * 0.9);
                    features.VerticalRatio_P90 = sortedHWRatios[p90Index];
                    features.HorizontalRatio_P90 = sortedWHRatios[p90Index];

                    // 75百分位
                    int p75Index = (int)(sortedHWRatios.Count * 0.75);
                    features.VerticalRatio_P75 = sortedHWRatios[p75Index];
                    features.HorizontalRatio_P75 = sortedWHRatios[p75Index];

                    // 50百分位（中位数）
                    int p50Index = (int)(sortedHWRatios.Count * 0.5);
                    features.VerticalRatio_P50 = sortedHWRatios[p50Index];
                    features.HorizontalRatio_P50 = sortedWHRatios[p50Index];
                }
                else
                {
                    // 如果样本太少，使用合理的默认值
                    features.VerticalRatio_P90 = Math.Max(7.0, features.MedianHeightWidthRatio * 2);
                    features.VerticalRatio_P75 = Math.Max(5.0, features.MedianHeightWidthRatio * 1.5);
                    features.VerticalRatio_P50 = Math.Max(1.5, features.MedianHeightWidthRatio);

                    features.HorizontalRatio_P90 = Math.Max(7.0, features.MedianWidthHeightRatio * 2);
                    features.HorizontalRatio_P75 = Math.Max(5.0, features.MedianWidthHeightRatio * 1.5);
                    features.HorizontalRatio_P50 = Math.Max(1.5, features.MedianWidthHeightRatio);
                }
            }

            // 基于动态阈值计算文本形状计数
            // 使用更合理的阈值计算方法，考虑极端高宽比
            double verticalThreshold = Math.Max(1.2, features.VerticalRatio_P50 * 0.6); // 降低竖排阈值，从0.7降到0.6
            double horizontalThreshold = Math.Max(1.2, features.HorizontalRatio_P50 * 0.7); // 保持横排阈值不变
            
            // 计算动态极端比例阈值 - 基于百分位数而非固定值
            double extremeVerticalThreshold = Math.Max(4.0, features.VerticalRatio_P75 * 1.2);
            double extremeHorizontalThreshold = Math.Max(4.0, features.HorizontalRatio_P75 * 1.2);
            
            // 记录日志
            System.Diagnostics.Debug.WriteLine($"动态极端比例阈值 - 竖排: {extremeVerticalThreshold:F2}, 横排: {extremeHorizontalThreshold:F2}");
            System.Diagnostics.Debug.WriteLine($"基于百分位数 - 竖排P75: {features.VerticalRatio_P75:F2}, 横排P75: {features.HorizontalRatio_P75:F2}");
            
            // 统计极端高宽比的文本块数量
            int extremeVerticalCount = 0;
            int highVerticalCount = 0;
            int extremeHorizontalCount = 0;
            
            // 计算更高的极端阈值，用于识别更极端的情况
            double superExtremeVerticalThreshold = Math.Max(8.0, features.VerticalRatio_P90 * 1.2);
            double highVerticalThreshold = Math.Max(3.0, features.VerticalRatio_P75);
            
            System.Diagnostics.Debug.WriteLine($"高宽比阈值 - 超极端竖排: {superExtremeVerticalThreshold:F2}, 高竖排: {highVerticalThreshold:F2}, 极端竖排: {extremeVerticalThreshold:F2}");
            
            foreach (var cell in cells)
            {
                double hwRatio = cell.location.height / (double)cell.location.width;
                double whRatio = cell.location.width / (double)cell.location.height;
                
                // 检测极端高宽比 - 使用动态阈值和多级别区分
                if (hwRatio >= superExtremeVerticalThreshold) // 超极端竖排
                {
                    extremeVerticalCount += 2; // 计为2个极端竖排，增强其影响
                    features.VerticalTextCount++; // 同时计入竖排文本计数
                    System.Diagnostics.Debug.WriteLine($"检测到超极端竖排文本块: hwRatio={hwRatio:F2}");
                }
                else if (hwRatio >= extremeVerticalThreshold) // 极端竖排
                {
                    extremeVerticalCount++;
                    features.VerticalTextCount++; // 同时计入竖排文本计数
                    System.Diagnostics.Debug.WriteLine($"检测到极端竖排文本块: hwRatio={hwRatio:F2}");
                }
                else if (hwRatio >= highVerticalThreshold) // 高竖排
                {
                    highVerticalCount++;
                    features.VerticalTextCount++; // 同时计入竖排文本计数
                }
                else if (hwRatio >= verticalThreshold)
                {
                    features.VerticalTextCount++;
                }
                else if (whRatio >= extremeHorizontalThreshold) // 极端横排
                {
                    extremeHorizontalCount++;
                    features.HorizontalTextCount++; // 同时计入横排文本计数
                }
                else if (whRatio >= horizontalThreshold)
                {
                    features.HorizontalTextCount++;
                }
                else
                {
                    features.SquareTextCount++;
                }
            }
            
            // 保存极端高宽比文本块计数到特征中
            features.ExtremeVerticalCount = extremeVerticalCount;
            features.HighVerticalCount = highVerticalCount;
            features.ExtremeHorizontalCount = extremeHorizontalCount;
            
            // 计算极端文本块的比例
            double totalTextCount = Math.Max(1, cells.Count);
            
            // 计算极端竖排文本块占比
            features.ExtremeVerticalRatio = extremeVerticalCount / totalTextCount;
            features.ExtremeHorizontalRatio = extremeHorizontalCount / totalTextCount;
            
            System.Diagnostics.Debug.WriteLine($"文本形状统计 - 竖排: {features.VerticalTextCount}, 横排: {features.HorizontalTextCount}, 方形: {features.SquareTextCount}");
            System.Diagnostics.Debug.WriteLine($"极端形状统计 - 极端竖排: {extremeVerticalCount}({features.ExtremeVerticalRatio:P2}), 高竖排: {highVerticalCount}, 极端横排: {extremeHorizontalCount}({features.ExtremeHorizontalRatio:P2})");
            
            // 如果存在极端高宽比的文本块，调整计数以增强其影响
            // 这是一个通用的处理方法，适用于任何语言的极端比例文本
            if (extremeVerticalCount > 0)
            {
                // 根据极端竖排文本块的数量和比例动态计算额外得分
                double ratio = extremeVerticalCount / totalTextCount;
                int bonusCount = (int)Math.Ceiling(Math.Min(extremeVerticalCount * 1.5, cells.Count / 3.0));
                
                // 当极端竖排文本块占比高时，给予更多额外计数
                if (ratio >= 0.5 || extremeVerticalCount >= 3)
                {
                    bonusCount = (int)Math.Ceiling(bonusCount * 1.5);
                }
                
                features.VerticalTextCount += bonusCount;
                System.Diagnostics.Debug.WriteLine($"检测到{extremeVerticalCount}个极端竖排文本块(占比{ratio:P2})，增加额外竖排计数 +{bonusCount}");
            }
            
            // 高竖排文本块也有一定影响，但权重较低
            if (highVerticalCount > 0 && extremeVerticalCount > 0)
            {
                int bonusCount = Math.Min(highVerticalCount / 2, cells.Count / 5);
                if (bonusCount > 0)
                {
                    features.VerticalTextCount += bonusCount;
                    System.Diagnostics.Debug.WriteLine($"检测到{highVerticalCount}个高竖排文本块，增加额外竖排计数 +{bonusCount}");
                }
            }
            
            // 处理极端横排文本块
            if (extremeHorizontalCount > 0 && extremeHorizontalCount > extremeVerticalCount)
            {
                double ratio = extremeHorizontalCount / totalTextCount;
                int bonusCount = (int)Math.Ceiling(Math.Min(extremeHorizontalCount * 1.5, cells.Count / 3.0));
                
                // 当极端横排文本块占比高时，给予更多额外计数
                if (ratio >= 0.5 || extremeHorizontalCount >= 3)
                {
                    bonusCount = (int)Math.Ceiling(bonusCount * 1.5);
                }
                
                features.HorizontalTextCount += bonusCount;
                System.Diagnostics.Debug.WriteLine($"检测到{extremeHorizontalCount}个极端横排文本块(占比{ratio:P2})，增加额外横排计数 +{bonusCount}");
            }

            // 计算文本块大小统计
            var widths = cells.Select(c => (double)c.location.width).OrderBy(w => w).ToList();
            var heights = cells.Select(c => (double)c.location.height).OrderBy(h => h).ToList();

            if (widths.Count > 0 && heights.Count > 0)
            {
                features.MedianWidth = widths[widths.Count / 2];
                features.MedianHeight = heights[heights.Count / 2];
                features.WidthVariance = CalculateVariance(widths);
                features.HeightVariance = CalculateVariance(heights);
            }
        }

        /// <summary>
        /// 提取边缘特征
        /// </summary>
        private void ExtractEdgeFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 提取边缘坐标
            foreach (var cell in cells)
            {
                features.LeftEdges.Add(cell.location.left);
                features.RightEdges.Add(cell.location.left + cell.location.width);
                features.TopEdges.Add(cell.location.top);
                features.BottomEdges.Add(cell.location.top + cell.location.height);
            }

            // 计算边缘变异
            if (features.LeftEdges.Count > 1)
            {
                features.LeftEdgeVariance = CalculateVariance(features.LeftEdges);
                features.RightEdgeVariance = CalculateVariance(features.RightEdges);
                features.TopEdgeVariance = CalculateVariance(features.TopEdges);
                features.BottomEdgeVariance = CalculateVariance(features.BottomEdges);

                // 计算边缘变异比例（避免除零）
                features.LeftRightEdgeVarianceRatio = features.RightEdgeVariance > 0 ?
                    features.LeftEdgeVariance / features.RightEdgeVariance : 1.0;

                features.TopBottomEdgeVarianceRatio = features.BottomEdgeVariance > 0 ?
                    features.TopEdgeVariance / features.BottomEdgeVariance : 1.0;
            }
        }

        /// <summary>
        /// 提取行列结构特征
        /// </summary>
        private void ExtractRowColumnFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 动态计算分组阈值
            double verticalGroupThreshold = this.groupingThresholdVertical;
            double horizontalGroupThreshold = this.groupingThresholdHorizontal;

            // 按水平位置分组形成列
            var columnGroups = cells
                .GroupBy(c => Math.Round(c.location.left / horizontalGroupThreshold) * horizontalGroupThreshold)
                .OrderBy(g => g.Key)
                .ToList();

            features.VerticalColumnCount = columnGroups.Count;

            // 存储列数据
            foreach (var column in columnGroups)
            {
                features.VerticalColumns.Add(column.ToList());
            }

            // 计算列间距
            if (columnGroups.Count >= 2)
            {
                for (int i = 1; i < columnGroups.Count; i++)
                {
                    double currentColCenter = columnGroups[i].Average(c => c.location.left + c.location.width / 2);
                    double prevColCenter = columnGroups[i - 1].Average(c => c.location.left + c.location.width / 2);
                    double gap = currentColCenter - prevColCenter;

                    if (gap > 0)
                    {
                        features.ColumnGaps.Add(gap);
                    }
                }

                if (features.ColumnGaps.Count > 0)
                {
                    features.MedianColumnGap = features.ColumnGaps.OrderBy(g => g).ElementAt(features.ColumnGaps.Count / 2);
                    features.ColumnGapVariance = CalculateVariance(features.ColumnGaps);

                    // 计算规律性（变异系数：标准差/平均值）
                    double columnGapMean = features.ColumnGaps.Average();
                    if (columnGapMean > 0)
                    {
                        features.ColumnGapRegularity = Math.Sqrt(features.ColumnGapVariance) / columnGapMean;
                    }
                }
            }

            // 按垂直位置分组形成行
            var rowGroups = cells
                .GroupBy(c => Math.Round(c.location.top / verticalGroupThreshold) * verticalGroupThreshold)
                .OrderBy(g => g.Key)
                .ToList();

            features.HorizontalRowCount = rowGroups.Count;

            // 存储行数据
            foreach (var row in rowGroups)
            {
                features.HorizontalRows.Add(row.ToList());
            }

            // 计算行间距
            if (rowGroups.Count >= 2)
            {
                for (int i = 1; i < rowGroups.Count; i++)
                {
                    double currentRowCenter = rowGroups[i].Average(c => c.location.top + c.location.height / 2);
                    double prevRowCenter = rowGroups[i - 1].Average(c => c.location.top + c.location.height / 2);
                    double gap = currentRowCenter - prevRowCenter;

                    if (gap > 0)
                    {
                        features.RowGaps.Add(gap);
                    }
                }

                if (features.RowGaps.Count > 0)
                {
                    features.MedianRowGap = features.RowGaps.OrderBy(g => g).ElementAt(features.RowGaps.Count / 2);
                    features.RowGapVariance = CalculateVariance(features.RowGaps);

                    // 计算规律性（变异系数）
                    double rowGapMean = features.RowGaps.Average();
                    if (rowGapMean > 0)
                    {
                        features.RowGapRegularity = Math.Sqrt(features.RowGapVariance) / rowGapMean;
                    }
                }
            }
        }

        /// <summary>
        /// 提取对齐特征
        /// </summary>
        private void ExtractAlignmentFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 动态确定对齐阈值
            double alignmentThreshold = Math.Min(10.0, features.MedianWidth * 0.1); // 默认10像素或宽度的10%

            // 统计左对齐
            var leftGroups = GroupSimilarValues(features.LeftEdges, alignmentThreshold);
            features.LeftAlignedCount = leftGroups.Count > 0 ? leftGroups.Max(g => g.Count) : 0;

            // 统计右对齐
            var rightGroups = GroupSimilarValues(features.RightEdges, alignmentThreshold);
            features.RightAlignedCount = rightGroups.Count > 0 ? rightGroups.Max(g => g.Count) : 0;

            // 统计顶部对齐
            var topGroups = GroupSimilarValues(features.TopEdges, alignmentThreshold);
            features.TopAlignedCount = topGroups.Count > 0 ? topGroups.Max(g => g.Count) : 0;

            // 统计底部对齐
            var bottomGroups = GroupSimilarValues(features.BottomEdges, alignmentThreshold);
            features.BottomAlignedCount = bottomGroups.Count > 0 ? bottomGroups.Max(g => g.Count) : 0;
        }

        /// <summary>
        /// 提取段落特征
        /// </summary>
        private void ExtractParagraphFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            if (cells.Count < 3)
            {
                features.ParagraphCount = 1;
                return;
            }

            // 按垂直位置排序
            var sortedCells = cells.OrderBy(c => c.location.top).ToList();

            // 计算段落间距阈值 - 动态计算
            features.ParagraphGapThreshold = features.MedianHeight * 1.5; // 默认为中位数高度的1.5倍

            // 统计段落数量
            int paragraphCount = 1;

            for (int i = 1; i < sortedCells.Count; i++)
            {
                double gap = sortedCells[i].location.top - (sortedCells[i - 1].location.top + sortedCells[i - 1].location.height);

                if (gap > features.ParagraphGapThreshold)
                {
                    paragraphCount++;
                    features.ParagraphGaps.Add(gap);
                }
            }

            features.ParagraphCount = paragraphCount;
        }

        /// <summary>
        /// 提取内容分布特征
        /// </summary>
        private void ExtractContentDistributionFeatures(List<TextCellInfo> cells, TextLayoutFeatures features)
        {
            // 计算内容面积和位置
            double totalArea = 0;
            double leftHalfArea = 0;
            double rightHalfArea = 0;
            double topHalfArea = 0;
            double bottomHalfArea = 0;

            foreach (var cell in cells)
            {
                double area = cell.location.width * cell.location.height;
                totalArea += area;

                // 计算中心点
                double centerX = cell.location.left + cell.location.width / 2;
                double centerY = cell.location.top + cell.location.height / 2;

                // 按位置分配面积
                if (centerX < features.PageCenter_X)
                {
                    leftHalfArea += area;
                }
                else
                {
                    rightHalfArea += area;
                }

                if (centerY < features.PageCenter_Y)
                {
                    topHalfArea += area;
                }
                else
                {
                    bottomHalfArea += area;
                }
            }

            // 计算内容密度比例
            if (totalArea > 0)
            {
                features.ContentDensityLeftHalf = leftHalfArea / totalArea;
                features.ContentDensityRightHalf = rightHalfArea / totalArea;
                features.ContentDensityTopHalf = topHalfArea / totalArea;
                features.ContentDensityBottomHalf = bottomHalfArea / totalArea;
            }
        }

        /// <summary>
        /// 计算方差
        /// </summary>
        private double CalculateVariance(List<double> values)
        {
            if (values.Count <= 1) return 0;

            double avg = values.Average();
            double sumOfSquaredDifferences = values.Sum(x => (x - avg) * (x - avg));
            return sumOfSquaredDifferences / values.Count;
        }

        /// <summary>
        /// 计算形状特征的智能权重
        /// </summary>
        /// <param name="evidence">方向证据</param>
        /// <returns>形状特征权重(0.0-1.0)</returns>
        private double CalculateShapeWeight(DirectionEvidence evidence)
        {
            // 1. 检查是否有文本块
            int totalTextCount = evidence.HorizontalTextCount + evidence.VerticalTextCount;
            if (totalTextCount == 0)
            {
                return 0.5; // 没有文本块，降低默认权重，从1.0降到0.5
            }

            // 2. 计算方形文字占比
            double squareRatio = CalculateSquareTextRatio(cells);

            // 3. 计算形状特征的强度
            double horizontalRatio = (double)evidence.HorizontalTextCount / totalTextCount;
            double verticalRatio = (double)evidence.VerticalTextCount / totalTextCount;

            // 4. 计算形状差异 - 差异越大，基础权重越高
            double shapeDiff = Math.Abs(horizontalRatio - verticalRatio);
            
            // 5. 检查是否存在极端高宽比文本块
            bool hasExtremeRatios = false;
            int extremeVerticalCount = 0;
            int extremeHorizontalCount = 0;
            
            // 计算动态阈值 - 基于文本块高宽比分布
            List<double> heightWidthRatios = new List<double>();
            List<double> widthHeightRatios = new List<double>();
            
            foreach (var cell in this.cells)
            {
                if (cell?.location != null)
                {
                    double hwRatio = cell.location.height / (double)cell.location.width;
                    double whRatio = cell.location.width / (double)cell.location.height;
                    
                    if (hwRatio > 1.0)
                    {
                        heightWidthRatios.Add(hwRatio);
                    }
                    
                    if (whRatio > 1.0)
                    {
                        widthHeightRatios.Add(whRatio);
                    }
                }
            }
            
            // 计算动态极端阈值
            double extremeVerticalThreshold = 5.0; // 默认值
            double extremeHorizontalThreshold = 5.0; // 默认值
            
            // 计算高宽比的百分位数
            if (heightWidthRatios.Count >= 3)
            {
                heightWidthRatios.Sort();
                int p90Index = (int)(heightWidthRatios.Count * 0.9);
                
                if (p90Index < heightWidthRatios.Count)
                {
                    extremeVerticalThreshold = Math.Max(4.0, heightWidthRatios[p90Index]); // 使用90百分位
                }
            }
            
            if (widthHeightRatios.Count >= 3)
            {
                widthHeightRatios.Sort();
                int p90Index = (int)(widthHeightRatios.Count * 0.9);
                
                if (p90Index < widthHeightRatios.Count)
                {
                    extremeHorizontalThreshold = Math.Max(4.0, widthHeightRatios[p90Index]); // 使用90百分位
                }
            }
            
            System.Diagnostics.Debug.WriteLine($"动态极端阈值 - 竖排: {extremeVerticalThreshold:F2}, 横排: {extremeHorizontalThreshold:F2}");
            
            // 统计极端高宽比的文本块
            foreach (var cell in this.cells)
            {
                if (cell?.location != null)
                {
                    double hwRatio = cell.location.height / (double)cell.location.width;
                    double whRatio = cell.location.width / (double)cell.location.height;
                    
                    if (hwRatio >= extremeVerticalThreshold) // 使用动态阈值
                    {
                        extremeVerticalCount++;
                    }
                    else if (whRatio >= extremeHorizontalThreshold) // 使用动态阈值
                    {
                        extremeHorizontalCount++;
                    }
                }
            }
            
            // 如果存在极端高宽比文本块，增加形状特征权重
            if (extremeVerticalCount > 0 || extremeHorizontalCount > 0)
            {
                hasExtremeRatios = true;
                
                // 计算极端比例文本块占总数的比例
                double extremeRatio = (extremeVerticalCount + extremeHorizontalCount) / (double)Math.Max(1, totalTextCount);
                
                // 检查是否存在明显的方向偏好
                bool hasVerticalPreference = extremeVerticalCount > extremeHorizontalCount * 2;
                bool hasHorizontalPreference = extremeHorizontalCount > extremeVerticalCount * 2;
                
                // 如果极端比例文本块比例较高，显著增加形状特征权重
                if (extremeRatio >= 0.5 || (extremeVerticalCount >= 3 && hasVerticalPreference) || (extremeHorizontalCount >= 3 && hasHorizontalPreference)) 
                {
                    // 增加形状差异，但不超过1.0
                    double bonus = 0.5; // 增加到0.5，之前是0.4
                    shapeDiff = Math.Min(1.0, shapeDiff + bonus);
                    System.Diagnostics.Debug.WriteLine($"检测到大量极端高宽比文本块({extremeRatio:P0})，显著增加形状差异: +{bonus:F1}");
                    
                    // 如果有明显的方向偏好，进一步增加差异
                    if (hasVerticalPreference)
                    {
                        System.Diagnostics.Debug.WriteLine($"检测到明显的竖排偏好(极端竖排:{extremeVerticalCount}，极端横排:{extremeHorizontalCount})");
                    }
                    else if (hasHorizontalPreference)
                    {
                        System.Diagnostics.Debug.WriteLine($"检测到明显的横排偏好(极端横排:{extremeHorizontalCount}，极端竖排:{extremeVerticalCount})");
                    }
                }
                else if (extremeRatio >= 0.3 || extremeVerticalCount >= 2 || extremeHorizontalCount >= 2) 
                {
                    // 中度增加形状差异
                    double bonus = 0.35; // 增加到0.35，之前是0.25
                    shapeDiff = Math.Min(1.0, shapeDiff + bonus);
                    System.Diagnostics.Debug.WriteLine($"检测到较多极端高宽比文本块({extremeRatio:P0})，中度增加形状差异: +{bonus:F2}");
                }
                else if (extremeRatio >= 0.1 || extremeVerticalCount >= 1 || extremeHorizontalCount >= 1) 
                {
                    // 轻度增加形状差异
                    double bonus = 0.25; // 增加到0.25，之前是0.15
                    shapeDiff = Math.Min(1.0, shapeDiff + bonus);
                    System.Diagnostics.Debug.WriteLine($"检测到少量极端高宽比文本块({extremeRatio:P0})，轻度增加形状差异: +{bonus:F2}");
                }
            }

            // 修改连续函数，考虑极端高宽比的影响
            // shapeDiff越大，权重越高
            double baseWeight;
            
            if (hasExtremeRatios)
            {
                // 存在极端高宽比时，显著提高基础权重范围到0.3-0.9
                baseWeight = 0.3 + (shapeDiff * 0.6);
                
                // 如果极端竖排文本块占比高，进一步增加权重
                if (extremeVerticalCount >= 3 || (extremeVerticalCount > 0 && extremeVerticalCount > extremeHorizontalCount * 2))
                {
                    double bonus = Math.Min(0.15, 0.05 * extremeVerticalCount);
                    baseWeight = Math.Min(0.9, baseWeight + bonus);
                    System.Diagnostics.Debug.WriteLine($"存在多个极端竖排文本块({extremeVerticalCount}个)，额外增加形状特征权重 +{bonus:F2}");
                }
                
                System.Diagnostics.Debug.WriteLine($"存在极端高宽比文本块，显著提高形状特征权重范围");
            }
            else
            {
                // 没有极端高宽比时，适度提高权重范围到0.2-0.8
                baseWeight = 0.2 + (shapeDiff * 0.6);
            }

            // 对于形状差异特别小的情况，显著降低权重
            if (shapeDiff < 0.1) // 差异极小
            {
                baseWeight = 0.1;
                System.Diagnostics.Debug.WriteLine($"形状差异极小({shapeDiff:F2})，大幅降低基础权重: 0.1");
            }
            else if (shapeDiff < 0.2 && !hasExtremeRatios) // 差异较小且没有极端高宽比
            {
                baseWeight = 0.15;
                System.Diagnostics.Debug.WriteLine($"形状差异较小({shapeDiff:F2})，降低基础权重: 0.15");
            }

            System.Diagnostics.Debug.WriteLine($"形状差异: {shapeDiff:F2}，基础权重={baseWeight:F2}");

            // 5. 根据方形文字占比动态调整权重
            double finalWeight = baseWeight;

            if (squareRatio > 0.05) // 有明显的方形文字特征
            {
                // 方形文字占比越高，权重降低越多
                // 使用更强的惩罚因子，显著降低方形文字的影响
                double reductionFactor = Math.Exp(-3.0 * squareRatio);
                finalWeight = baseWeight * reductionFactor;

                System.Diagnostics.Debug.WriteLine($"方形文字占比: {squareRatio:F2}，权重调整因子: {reductionFactor:F2}，最终权重: {finalWeight:F2}");
            }

            // 6. 考虑文本块数量因素 - 文本块越多，形状特征越可靠，但提高幅度较小
            if (totalTextCount > 10)
            {
                double confidenceFactor = Math.Min(1.15, 1.0 + Math.Log10(totalTextCount) * 0.04);
                finalWeight *= confidenceFactor;
                System.Diagnostics.Debug.WriteLine($"文本块数量较多({totalTextCount})，适度增加形状权重可靠性: x{confidenceFactor:F2}");
            }

            // 7. 基于边缘变异特征进一步调整形状权重
            // 当上下边缘变异与左右边缘变异有显著差异时，降低形状权重
            if (evidence.LeftEdgeVariance > 0 && evidence.RightEdgeVariance > 0 &&
                evidence.TopEdgeVariance > 0 && evidence.BottomEdgeVariance > 0)
            {
                double horizontalEdgeVariance = evidence.LeftEdgeVariance + evidence.RightEdgeVariance;
                double verticalEdgeVariance = evidence.TopEdgeVariance + evidence.BottomEdgeVariance;
                
                if (horizontalEdgeVariance > 0 && verticalEdgeVariance > 0)
                {
                    // 计算边缘变异比例
                    double edgeVarianceRatio = verticalEdgeVariance / horizontalEdgeVariance;
                    
                    // 边缘变异差异极大时，大幅降低形状权重
                    if (edgeVarianceRatio < 0.5 || edgeVarianceRatio > 2.0)
                    {
                        finalWeight *= 0.6;
                        System.Diagnostics.Debug.WriteLine($"边缘变异特征极为显著(比率:{edgeVarianceRatio:F2})，大幅降低形状权重: x0.6");
                    }
                    // 边缘变异差异显著时，降低形状权重
                    else if (edgeVarianceRatio < 0.7 || edgeVarianceRatio > 1.4)
                    {
                        finalWeight *= 0.75;
                        System.Diagnostics.Debug.WriteLine($"边缘变异特征显著(比率:{edgeVarianceRatio:F2})，降低形状权重: x0.75");
                    }
                }
            }

            // 8. 如果行列结构特征明显，大幅降低形状权重
            if (evidence.VerticalAlignedColumns > 0 || evidence.HorizontalAlignedRows > 0)
            {
                // 行列结构总数
                int rowColTotal = evidence.VerticalAlignedColumns + evidence.HorizontalAlignedRows;
                
                // 行列结构差异比例
                double rowColDiffRatio = 0;
                if (rowColTotal > 0)
                {
                    rowColDiffRatio = Math.Abs(evidence.HorizontalAlignedRows - evidence.VerticalAlignedColumns) / 
                                     (double)rowColTotal;
                }
                
                // 当结构特别丰富时
                if (rowColTotal >= 10)
                {
                    // 如果行列结构差异明显
                    if (rowColDiffRatio > 0.3)
                    {
                        finalWeight *= 0.5;
                        System.Diagnostics.Debug.WriteLine($"行列结构非常丰富且差异显著(行:{evidence.HorizontalAlignedRows},列:{evidence.VerticalAlignedColumns})，显著降低形状权重: x0.5");
                    }
                    else
                    {
                        finalWeight *= 0.6;
                        System.Diagnostics.Debug.WriteLine($"行列结构非常丰富(行:{evidence.HorizontalAlignedRows},列:{evidence.VerticalAlignedColumns})，大幅降低形状权重: x0.6");
                    }
                }
                // 当存在中等数量的行列结构
                else if (rowColTotal >= 6)
                {
                    finalWeight *= 0.7;
                    System.Diagnostics.Debug.WriteLine($"检测到明显的行列结构(行:{evidence.HorizontalAlignedRows},列:{evidence.VerticalAlignedColumns})，降低形状权重: x0.7");
                }
                // 当行列结构特征较弱时
                else if (rowColTotal >= 3)
                {
                    finalWeight *= 0.85;
                    System.Diagnostics.Debug.WriteLine($"检测到少量行列结构(行:{evidence.HorizontalAlignedRows},列:{evidence.VerticalAlignedColumns})，轻微降低形状权重: x0.85");
                }
            }

            // 9. 设置最小权重下限，降低到0.03（原来是0.05）
            return Math.Max(0.03, Math.Min(0.8, finalWeight)); // 同时降低最大权重上限到0.8（原来是1.0）
        }

        /// <summary>
        /// 分析边缘对齐特征，增强布局判断
        /// </summary>
        /// <param name="evidence">方向证据</param>
        /// <returns>边缘对齐分析得分</returns>
        private AlignmentScores AnalyzeEdgeAlignment(DirectionEvidence evidence)
        {
            int verticalScore = 0;
            int horizontalScore = 0;

            // 1. 分析左右边缘变异性
            if (evidence.LeftEdgeVariance > 0 && evidence.RightEdgeVariance > 0)
            {
                double leftRightRatio = evidence.LeftEdgeVariance / evidence.RightEdgeVariance;

                // 左边缘变异小于右边缘 - 支持从左到右读取 - 横排证据
                if (leftRightRatio < 0.7)
                {
                    horizontalScore += 8;
                    System.Diagnostics.Debug.WriteLine($"左边缘明显比右边缘整齐(比率:{leftRightRatio:F2}) => 横排得分 +8");
                }
                // 右边缘变异小于左边缘 - 支持从右到左读取 - 竖排证据
                else if (leftRightRatio > 1.4)
                {
                    verticalScore += 8;
                    System.Diagnostics.Debug.WriteLine($"右边缘明显比左边缘整齐(比率:{leftRightRatio:F2}) => 竖排得分 +8");
                }
                // 比率接近但有绝对差异时的处理
                else if (Math.Abs(leftRightRatio - 1.0) < 0.3)
                {
                    // 当比率接近1时检查绝对差异
                    double absoluteDiff = Math.Abs(evidence.LeftEdgeVariance - evidence.RightEdgeVariance);
                    double avgVariance = (evidence.LeftEdgeVariance + evidence.RightEdgeVariance) / 2;
                    
                    // 如果绝对差异足够大，仍然可以提供方向证据
                    if (absoluteDiff > 10000 && absoluteDiff > avgVariance * 0.05)
                    {
                        if (evidence.LeftEdgeVariance < evidence.RightEdgeVariance)
                        {
                            horizontalScore += 5;
                            System.Diagnostics.Debug.WriteLine($"左边缘比右边缘整齐(绝对差值显著:{absoluteDiff:F0}) => 横排得分 +5");
                        }
                        else
                        {
                            verticalScore += 5;
                            System.Diagnostics.Debug.WriteLine($"右边缘比左边缘整齐(绝对差值显著:{absoluteDiff:F0}) => 竖排得分 +5");
                        }
                    }
                }
            }

            // 2. 分析上下边缘变异性
            if (evidence.TopEdgeVariance > 0 && evidence.BottomEdgeVariance > 0)
            {
                double topBottomRatio = evidence.TopEdgeVariance / evidence.BottomEdgeVariance;

                // 上边缘变异小于下边缘 - 支持从上到下读取 - 增强当前方向判断
                if (topBottomRatio < 0.7)
                {
                    // 如果已判定为竖排，进一步增强竖排得分
                    if (verticalScore > horizontalScore)
                    {
                        verticalScore += 5;
                        System.Diagnostics.Debug.WriteLine($"上边缘明显比下边缘整齐(比率:{topBottomRatio:F2})，与竖排判断一致 => 额外竖排得分 +5");
                    }
                    // 如果倾向于横排，也适度增强横排得分
                    else
                    {
                        horizontalScore += 4;
                        System.Diagnostics.Debug.WriteLine($"上边缘明显比下边缘整齐(比率:{topBottomRatio:F2}) => 横排得分 +4");
                    }
                }
                // 下边缘变异小于上边缘 - 较少见情况，可能是特殊排版
                else if (topBottomRatio > 1.4)
                {
                    // 这种情况较少见，仅轻微增加相关方向得分
                    if (verticalScore > horizontalScore)
                    {
                        verticalScore += 3;
                        System.Diagnostics.Debug.WriteLine($"下边缘明显比上边缘整齐(比率:{topBottomRatio:F2}) => 竖排得分 +3");
                    }
                    else
                    {
                        horizontalScore += 2;
                        System.Diagnostics.Debug.WriteLine($"下边缘明显比上边缘整齐(比率:{topBottomRatio:F2}) => 横排得分 +2");
                    }
                }
                // 比率接近但有绝对差异时的处理
                else if (Math.Abs(topBottomRatio - 1.0) < 0.3)
                {
                    // 当比率接近1时检查绝对差异
                    double absoluteDiff = Math.Abs(evidence.TopEdgeVariance - evidence.BottomEdgeVariance);
                    double avgVariance = (evidence.TopEdgeVariance + evidence.BottomEdgeVariance) / 2;
                    
                    // 如果绝对差异足够大，仍然可以提供方向证据
                    if (absoluteDiff > 5000 && absoluteDiff > avgVariance * 0.05)
                    {
                        if (evidence.TopEdgeVariance < evidence.BottomEdgeVariance)
                        {
                            // 上边界更整齐
                            if (verticalScore > horizontalScore)
                            {
                                verticalScore += 4;
                                System.Diagnostics.Debug.WriteLine($"上边缘比下边缘整齐(绝对差值显著:{absoluteDiff:F0})，与竖排判断一致 => 额外竖排得分 +4");
                            }
                            else
                            {
                                horizontalScore += 3;
                                System.Diagnostics.Debug.WriteLine($"上边缘比下边缘整齐(绝对差值显著:{absoluteDiff:F0}) => 横排得分 +3");
                            }
                        }
                        else
                        {
                            // 下边界更整齐
                            if (verticalScore > horizontalScore)
                            {
                                verticalScore += 2;
                                System.Diagnostics.Debug.WriteLine($"下边缘比上边缘整齐(绝对差值显著:{absoluteDiff:F0}) => 竖排得分 +2");
                            }
                            else
                            {
                                horizontalScore += 1;
                                System.Diagnostics.Debug.WriteLine($"下边缘比上边缘整齐(绝对差值显著:{absoluteDiff:F0}) => 横排得分 +1");
                            }
                        }
                    }
                }
            }

            // 3. 分析对齐文本块数量
            if (evidence.LeftAlignedCount > 0 || evidence.RightAlignedCount > 0)
            {
                double totalAligned = evidence.LeftAlignedCount + evidence.RightAlignedCount;
                double leftRightRatio = totalAligned > 0 ?
                    evidence.LeftAlignedCount / totalAligned : 0.5;

                // 左对齐占比明显高 - 通常是横排布局
                if (leftRightRatio > 0.7 && evidence.LeftAlignedCount >= 3)
                {
                    horizontalScore += 6;
                    System.Diagnostics.Debug.WriteLine($"左对齐文本块占比高({leftRightRatio:P0}) => 横排得分 +6");
                }
                // 右对齐占比明显高 - 可能是从右到左的竖排布局
                else if (leftRightRatio < 0.3 && evidence.RightAlignedCount >= 3)
                {
                    verticalScore += 6;
                    System.Diagnostics.Debug.WriteLine($"右对齐文本块占比高({1 - leftRightRatio:P0}) => 竖排得分 +6");
                }
            }

            // 4. 分析上下对齐情况
            if (evidence.TopAlignedCount > 0 || evidence.BottomAlignedCount > 0)
            {
                double totalAligned = evidence.TopAlignedCount + evidence.BottomAlignedCount;
                double topBottomRatio = totalAligned > 0 ?
                    evidence.TopAlignedCount / totalAligned : 0.5;

                // 顶部对齐占比明显高 - 通常是竖排布局
                if (topBottomRatio > 0.7 && evidence.TopAlignedCount >= 3)
                {
                    verticalScore += 5;
                    System.Diagnostics.Debug.WriteLine($"顶部对齐文本块占比高({topBottomRatio:P0}) => 竖排得分 +5");
                }
                // 底部对齐占比明显高 - 较少见情况
                else if (topBottomRatio < 0.3 && evidence.BottomAlignedCount >= 3)
                {
                    horizontalScore += 3;
                    System.Diagnostics.Debug.WriteLine($"底部对齐文本块占比高({1 - topBottomRatio:P0}) => 横排得分 +3");
                }
            }

            // 5. 检查边缘对齐的一致性
            // 如果左右边缘和上下边缘的判断结果一致，增强得分
            if ((evidence.LeftEdgeVariance < evidence.RightEdgeVariance && evidence.TopEdgeVariance < evidence.BottomEdgeVariance) ||
                (evidence.RightEdgeVariance < evidence.LeftEdgeVariance && evidence.BottomEdgeVariance < evidence.TopEdgeVariance))
            {
                // 边缘变异性判断一致，增强当前倾向
                if (verticalScore > horizontalScore)
                {
                    verticalScore += 4;
                    System.Diagnostics.Debug.WriteLine("边缘变异性判断一致，增强竖排倾向 => 竖排得分 +4");
                }
                else if (horizontalScore > verticalScore)
                {
                    horizontalScore += 4;
                    System.Diagnostics.Debug.WriteLine("边缘变异性判断一致，增强横排倾向 => 横排得分 +4");
                }
            }
            
            // 6. 比较上下边缘和左右边缘的总体变异差异
            double horizontalEdgeVariance = evidence.LeftEdgeVariance + evidence.RightEdgeVariance;
            double verticalEdgeVariance = evidence.TopEdgeVariance + evidence.BottomEdgeVariance;
            
            if (horizontalEdgeVariance > 0 && verticalEdgeVariance > 0)
            {
                // 计算两者之比
                double edgeVarianceRatio = verticalEdgeVariance / horizontalEdgeVariance;
                
                // 上下边缘总体变异远小于左右边缘变异 - 强横排证据
                if (edgeVarianceRatio < 0.7)
                {
                    int bonusScore = 10;
                    horizontalScore += bonusScore;
                    System.Diagnostics.Debug.WriteLine($"上下边缘总体变异({verticalEdgeVariance:F0})远小于左右边缘变异({horizontalEdgeVariance:F0})，比率:{edgeVarianceRatio:F2} => 强横排证据 +{bonusScore}");
                }
                // 左右边缘总体变异远小于上下边缘变异 - 强竖排证据
                else if (edgeVarianceRatio > 1.4)
                {
                    int bonusScore = 10;
                    verticalScore += bonusScore;
                    System.Diagnostics.Debug.WriteLine($"左右边缘总体变异({horizontalEdgeVariance:F0})远小于上下边缘变异({verticalEdgeVariance:F0})，比率:{edgeVarianceRatio:F2} => 强竖排证据 +{bonusScore}");
                }
                // 边缘变异差异明显但不极端
                else if (edgeVarianceRatio < 0.85)
                {
                    int bonusScore = 6;
                    horizontalScore += bonusScore;
                    System.Diagnostics.Debug.WriteLine($"上下边缘总体变异小于左右边缘变异，比率:{edgeVarianceRatio:F2} => 横排证据 +{bonusScore}");
                }
                else if (edgeVarianceRatio > 1.18)
                {
                    int bonusScore = 6;
                    verticalScore += bonusScore;
                    System.Diagnostics.Debug.WriteLine($"左右边缘总体变异小于上下边缘变异，比率:{edgeVarianceRatio:F2} => 竖排证据 +{bonusScore}");
                }
            }

            return new AlignmentScores
            {
                VerticalScore = verticalScore,
                HorizontalScore = horizontalScore
            };
        }

        /// <summary>
        /// 边缘对齐分析得分
        /// </summary>
        private class AlignmentScores
        {
            public int VerticalScore { get; set; } = 0;
            public int HorizontalScore { get; set; } = 0;
        }

        /// <summary>
        /// 分析文本密度直方图，增强布局判断
        /// </summary>
        /// <param name="evidence">方向证据</param>
        private void AnalyzeDensityHistograms(DirectionEvidence evidence)
        {
            // 1. 参数设置
            int binCount = 40; // 直方图分区数量

            // 2. 创建水平和垂直方向的密度直方图
            var horizontalHistogram = CreateDensityHistogram(this.cells, binCount, true);
            var verticalHistogram = CreateDensityHistogram(this.cells, binCount, false);

            // 3. 分析直方图特征，并保存到类成员变量中，供DetermineLayoutType方法使用
            this.horizontalHistogram = AnalyzeHistogramFeatures(horizontalHistogram);
            this.verticalHistogram = AnalyzeHistogramFeatures(verticalHistogram);

            System.Diagnostics.Debug.WriteLine("\n--- 直方图分析结果 ---");
            System.Diagnostics.Debug.WriteLine($"水平方向 - 峰值数: {this.horizontalHistogram.PeakCount}, 峰值清晰度: {this.horizontalHistogram.PeakClearness:F2}, 规律性: {this.horizontalHistogram.Regularity:F2}");
            System.Diagnostics.Debug.WriteLine($"垂直方向 - 峰值数: {this.verticalHistogram.PeakCount}, 峰值清晰度: {this.verticalHistogram.PeakClearness:F2}, 规律性: {this.verticalHistogram.Regularity:F2}");

            // 4. 确定文本方向倾向
            bool horizontalMoreStructured = IsMoreStructured(this.horizontalHistogram, this.verticalHistogram);
            int histogramScore = CalculateHistogramScore(this.horizontalHistogram, this.verticalHistogram);
            
            // 保存直方图得分到evidence，供DetermineLayoutType方法使用
            evidence.HistogramScore = histogramScore;

            // 5. 根据分析结果增加布局得分
            // 降低直方图分析的权重，避免它过度影响结果
            int adjustedHistogramScore = (int)(histogramScore * 0.7); // 降低到70%
            
            if (horizontalMoreStructured)
            {
                // 水平方向直方图更有结构 - 表明文本可能是竖排的(多列)
                evidence.VerticalTextCount += adjustedHistogramScore;
                System.Diagnostics.Debug.WriteLine($"水平方向直方图结构更明显，表明可能是竖排布局 => 竖排证据 +{adjustedHistogramScore}");

                // 额外处理：检查是否存在明显的列结构特征 - 使用相对值而非固定阈值
                // 峰值数量超过平均值，且规律性良好
                double avgPeakCount = (this.horizontalHistogram.PeakCount + Math.Max(1, this.verticalHistogram.PeakCount)) / 3.0;
                if (this.horizontalHistogram.PeakCount > avgPeakCount && this.horizontalHistogram.Regularity > 0.4)
                {
                    // 水平方向有多个规律的峰值，强烈暗示竖排多列结构
                    // 奖励分数与峰值数量、规律性和与平均值的差距成正比
                    // 降低奖励因子，从0.6降低到0.5
                    double bonusFactor = Math.Min(0.5, 0.25 + (this.horizontalHistogram.PeakCount / avgPeakCount - 1) * 0.15 + this.horizontalHistogram.Regularity * 0.1);
                    int bonusScore = (int)(adjustedHistogramScore * bonusFactor);
                    evidence.VerticalTextCount += bonusScore;
                    System.Diagnostics.Debug.WriteLine($"水平方向存在规律的多峰结构(峰值数:{this.horizontalHistogram.PeakCount}，规律性:{this.horizontalHistogram.Regularity:F2})，强烈暗示竖排多列布局 => 额外竖排证据 +{bonusScore}");
                }
            }
            else
            {
                // 垂直方向直方图更有结构 - 表明文本可能是横排的(多行)
                evidence.HorizontalTextCount += adjustedHistogramScore;
                System.Diagnostics.Debug.WriteLine($"垂直方向直方图结构更明显，表明可能是横排布局 => 横排证据 +{adjustedHistogramScore}");

                // 额外处理：检查是否存在明显的行结构特征 - 使用相对值而非固定阈值
                double avgPeakCount = (this.verticalHistogram.PeakCount + Math.Max(1, this.horizontalHistogram.PeakCount)) / 3.0;
                if (this.verticalHistogram.PeakCount > avgPeakCount && this.verticalHistogram.Regularity > 0.4)
                {
                    // 垂直方向有多个规律的峰值，强烈暗示横排多行结构
                    // 降低奖励因子，从0.6降低到0.5
                    double bonusFactor = Math.Min(0.5, 0.25 + (this.verticalHistogram.PeakCount / avgPeakCount - 1) * 0.15 + this.verticalHistogram.Regularity * 0.1);
                    int bonusScore = (int)(adjustedHistogramScore * bonusFactor);
                    evidence.HorizontalTextCount += bonusScore;
                    System.Diagnostics.Debug.WriteLine($"垂直方向存在规律的多峰结构(峰值数:{this.verticalHistogram.PeakCount}，规律性:{this.verticalHistogram.Regularity:F2})，强烈暗示横排多行布局 => 额外横排证据 +{bonusScore}");
                }
            }

            // 6. 特殊情况处理：峰值质量差异显著的情况
            // 使用峰值质量比较，而不是仅依赖峰值数量
            double horizontalQuality = Math.Max(0.1, this.horizontalHistogram.PeakCount * this.horizontalHistogram.PeakClearness);
            double verticalQuality = Math.Max(0.1, this.verticalHistogram.PeakCount * this.verticalHistogram.PeakClearness);
            double peakQualityRatio = Math.Max(horizontalQuality, verticalQuality) / Math.Min(horizontalQuality, verticalQuality);

            // 峰值质量差异显著
            double significantQualityRatio = Math.Max(3.0, 5.0 - Math.Log10(Math.Max(10, this.cells.Count)));

            if (peakQualityRatio >= significantQualityRatio)
            {
                // 动态计算加分，基于峰值质量、清晰度、规律性和文本块数量
                if (horizontalQuality > verticalQuality)
                {
                    // 水平方向峰值质量显著高于垂直方向
                    double baseScore = Math.Min(3.0, this.horizontalHistogram.PeakCount) * 1.5;
                    double qualityFactor = Math.Min(1.8, 1.0 + this.horizontalHistogram.PeakClearness * 0.2 + this.horizontalHistogram.Regularity * 0.3);
                    double cellCountFactor = Math.Min(1.2, 1.0 + Math.Log10(Math.Max(10, this.cells.Count)) * 0.05);

                    // 计算最终得分，设置动态上限
                    int maxScore = Math.Min(15, 8 + (int)(Math.Log10(Math.Max(10, this.cells.Count)) * 2));
                    int specialScore = (int)Math.Min(maxScore, baseScore * qualityFactor * cellCountFactor);

                    evidence.VerticalTextCount += specialScore;
                    System.Diagnostics.Debug.WriteLine($"水平方向峰值质量({horizontalQuality:F2})显著高于垂直方向({verticalQuality:F2})，比例:{peakQualityRatio:F2} >= {significantQualityRatio:F2}，暗示竖排布局 => 特殊竖排证据 +{specialScore}");
                }
                else
                {
                    // 垂直方向峰值质量显著高于水平方向
                    double baseScore = Math.Min(3.0, this.verticalHistogram.PeakCount) * 1.5;
                    double qualityFactor = Math.Min(1.8, 1.0 + this.verticalHistogram.PeakClearness * 0.2 + this.verticalHistogram.Regularity * 0.3);
                    double cellCountFactor = Math.Min(1.2, 1.0 + Math.Log10(Math.Max(10, this.cells.Count)) * 0.05);

                    // 计算最终得分，设置动态上限
                    int maxScore = Math.Min(15, 8 + (int)(Math.Log10(Math.Max(10, this.cells.Count)) * 2));
                    int specialScore = (int)Math.Min(maxScore, baseScore * qualityFactor * cellCountFactor);

                    evidence.HorizontalTextCount += specialScore;
                    System.Diagnostics.Debug.WriteLine($"垂直方向峰值质量({verticalQuality:F2})显著高于水平方向({horizontalQuality:F2})，比例:{peakQualityRatio:F2} >= {significantQualityRatio:F2}，暗示横排布局 => 特殊横排证据 +{specialScore}");
                }
            }
            // 处理两个方向峰值数量差异显著的情况
            else if (this.horizontalHistogram.PeakCount > 0 && this.verticalHistogram.PeakCount > 0)
            {
                // 计算峰值数量比例和峰值清晰度比例的加权平均
                double countRatio = Math.Max(this.horizontalHistogram.PeakCount, this.verticalHistogram.PeakCount) /
                                   Math.Max(1.0, Math.Min(this.horizontalHistogram.PeakCount, this.verticalHistogram.PeakCount));

                double clearnessRatio = Math.Max(this.horizontalHistogram.PeakClearness, this.verticalHistogram.PeakClearness) /
                                       Math.Max(1.0, Math.Min(this.horizontalHistogram.PeakClearness, this.verticalHistogram.PeakClearness));

                // 综合比例 - 峰值数量和清晰度的加权平均
                double combinedRatio = countRatio * 0.7 + clearnessRatio * 0.3;

                // 差异阈值随文本块数量动态调整
                double ratioThreshold = Math.Max(1.5, 2.0 - Math.Log10(Math.Max(10, this.cells.Count)) * 0.15);

                // 峰值特征差异显著
                if (combinedRatio >= ratioThreshold)
                {
                    // 计算差异分数，与峰值综合比例成正比
                    double baseDiff = Math.Min(10, 5 + (combinedRatio - ratioThreshold) * 2.0);

                    // 考虑峰值规律性
                    double regularityFactor = 1.0;
                    if (this.horizontalHistogram.PeakCount > this.verticalHistogram.PeakCount)
                    {
                        regularityFactor = Math.Min(1.5, 1.0 + this.horizontalHistogram.Regularity * 0.5);
                    }
                    else
                    {
                        regularityFactor = Math.Min(1.5, 1.0 + this.verticalHistogram.Regularity * 0.5);
                    }

                    int diffScore = (int)(baseDiff * regularityFactor);

                    if ((this.horizontalHistogram.PeakCount > this.verticalHistogram.PeakCount && this.horizontalHistogram.PeakClearness >= this.verticalHistogram.PeakClearness * 0.8) ||
                        (this.horizontalHistogram.PeakClearness > this.verticalHistogram.PeakClearness * 1.5))
                    {
                        evidence.VerticalTextCount += diffScore;
                        System.Diagnostics.Debug.WriteLine($"水平方向峰值特征(数量:{this.horizontalHistogram.PeakCount}，清晰度:{this.horizontalHistogram.PeakClearness:F2})显著强于垂直方向(数量:{this.verticalHistogram.PeakCount}，清晰度:{this.verticalHistogram.PeakClearness:F2})，综合比例:{combinedRatio:F1} >= {ratioThreshold:F1}，暗示竖排布局 => 额外竖排证据 +{diffScore}");
                    }
                    else if ((this.verticalHistogram.PeakCount > this.horizontalHistogram.PeakCount && this.verticalHistogram.PeakClearness >= this.horizontalHistogram.PeakClearness * 0.8) ||
                             (this.verticalHistogram.PeakClearness > this.horizontalHistogram.PeakClearness * 1.5))
                    {
                        evidence.HorizontalTextCount += diffScore;
                        System.Diagnostics.Debug.WriteLine($"垂直方向峰值特征(数量:{this.verticalHistogram.PeakCount}，清晰度:{this.verticalHistogram.PeakClearness:F2})显著强于水平方向(数量:{this.horizontalHistogram.PeakCount}，清晰度:{this.horizontalHistogram.PeakClearness:F2})，综合比例:{combinedRatio:F1} >= {ratioThreshold:F1}，暗示横排布局 => 额外横排证据 +{diffScore}");
                    }
                }
            }

            // 7. 处理峰值质量显著差异的情况
            // 不再使用硬编码的PeakCount == 1或PeakCount == 0判断
            // 改用峰值清晰度和质量的相对比较
            double horizontalPeakQuality = CalculatePeakQuality(this.horizontalHistogram);
            double verticalPeakQuality = CalculatePeakQuality(this.verticalHistogram);

            // 计算两个方向峰值质量的差异比例
            double qualityRatio = 0.0;
            if (horizontalPeakQuality > 0 && verticalPeakQuality > 0)
            {
                qualityRatio = Math.Max(horizontalPeakQuality, verticalPeakQuality) /
                              Math.Min(horizontalPeakQuality, verticalPeakQuality);
            }

            // 一个方向的峰值质量显著高于另一个方向（比例阈值随文本块数量动态调整）
            double qualityRatioThreshold = Math.Max(1.5, 2.0 - Math.Log10(Math.Max(10, this.cells.Count)) * 0.2);

            if (horizontalPeakQuality > verticalPeakQuality * qualityRatioThreshold && horizontalPeakQuality > 1.5)
            {
                // 水平方向峰值质量显著高于垂直方向
                // 计算得分，与质量差异和峰值清晰度成正比
                double qualityDiff = horizontalPeakQuality - verticalPeakQuality;
                int specialScore = (int)Math.Min(8, 3 + qualityDiff * 0.5);
                evidence.VerticalTextCount += specialScore;
                System.Diagnostics.Debug.WriteLine($"水平方向峰值质量({horizontalPeakQuality:F2})显著高于垂直方向({verticalPeakQuality:F2})，比例:{qualityRatio:F2} > {qualityRatioThreshold:F2}，暗示竖排布局 => 特殊竖排证据 +{specialScore}");
            }
            else if (verticalPeakQuality > horizontalPeakQuality * qualityRatioThreshold && verticalPeakQuality > 1.5)
            {
                // 垂直方向峰值质量显著高于水平方向
                double qualityDiff = verticalPeakQuality - horizontalPeakQuality;
                int specialScore = (int)Math.Min(8, 3 + qualityDiff * 0.5);
                evidence.HorizontalTextCount += specialScore;
                System.Diagnostics.Debug.WriteLine($"垂直方向峰值质量({verticalPeakQuality:F2})显著高于水平方向({horizontalPeakQuality:F2})，比例:{qualityRatio:F2} > {qualityRatioThreshold:F2}，暗示横排布局 => 特殊横排证据 +{specialScore}");
            }

        }

        /// <summary>
        /// 创建密度直方图
        /// </summary>
        /// <param name="cells">文本单元列表</param>
        /// <param name="binCount">直方图分区数量</param>
        /// <param name="isHorizontal">是否为水平方向直方图</param>
        /// <returns>密度直方图</returns>
        private int[] CreateDensityHistogram(List<TextCellInfo> cells, int binCount, bool isHorizontal)
        {
            // 1. 初始化直方图数组
            var histogram = new int[binCount];

            // 2. 确定页面范围
            double min = double.MaxValue;
            double max = double.MinValue;

            foreach (var cell in cells)
            {
                if (cell?.location == null) continue;

                if (isHorizontal) // 创建X轴(水平)方向的直方图
                {
                    min = Math.Min(min, cell.location.left);
                    max = Math.Max(max, cell.location.left + cell.location.width);
                }
                else // 创建Y轴(垂直)方向的直方图
                {
                    min = Math.Min(min, cell.location.top);
                    max = Math.Max(max, cell.location.top + cell.location.height);
                }
            }

            // 确保有效范围
            if (max <= min) return histogram;

            // 3. 填充直方图
            double binWidth = (max - min) / binCount;

            foreach (var cell in cells)
            {
                if (cell?.location == null) continue;

                if (isHorizontal) // 根据X坐标填充
                {
                    // 每个文本块可能跨越多个bin
                    int startBin = (int)Math.Floor((cell.location.left - min) / binWidth);
                    int endBin = (int)Math.Floor((cell.location.left + cell.location.width - min) / binWidth);

                    startBin = Math.Max(0, Math.Min(binCount - 1, startBin));
                    endBin = Math.Max(0, Math.Min(binCount - 1, endBin));

                    for (int i = startBin; i <= endBin; i++)
                    {
                        histogram[i]++;
                    }
                }
                else // 根据Y坐标填充
                {
                    int startBin = (int)Math.Floor((cell.location.top - min) / binWidth);
                    int endBin = (int)Math.Floor((cell.location.top + cell.location.height - min) / binWidth);

                    startBin = Math.Max(0, Math.Min(binCount - 1, startBin));
                    endBin = Math.Max(0, Math.Min(binCount - 1, endBin));

                    for (int i = startBin; i <= endBin; i++)
                    {
                        histogram[i]++;
                    }
                }
            }

            return histogram;
        }

        /// <summary>
        /// 分析直方图特征
        /// </summary>
        /// <param name="histogram">密度直方图</param>
        /// <returns>直方图特征</returns>
        private HistogramFeatures AnalyzeHistogramFeatures(int[] histogram)
        {
            var features = new HistogramFeatures();

            // 1. 平滑直方图减少噪声
            var smoothedHistogram = SmoothHistogram(histogram, 3);

            // 2. 检测峰值
            var peaks = DetectPeaks(smoothedHistogram);
            features.PeakCount = peaks.Count;

            // 3. 计算峰值清晰度 (峰谷比)
            if (peaks.Count >= 2)
            {
                double avgPeakHeight = peaks.Average(p => smoothedHistogram[p]);

                // 计算峰之间的谷的高度
                double avgValleyHeight = CalculateAverageValleyHeight(smoothedHistogram, peaks);

                features.PeakClearness = avgValleyHeight > 0 ?
                    avgPeakHeight / avgValleyHeight : 1.0;
            }

            // 4. 计算峰值规律性 (峰间距的变异系数)
            if (peaks.Count >= 3)
            {
                var peakDistances = new List<int>();
                for (int i = 1; i < peaks.Count; i++)
                {
                    peakDistances.Add(peaks[i] - peaks[i - 1]);
                }

                double avgDistance = peakDistances.Average();
                double variance = peakDistances.Sum(d => Math.Pow(d - avgDistance, 2)) / peakDistances.Count;
                double stdDev = Math.Sqrt(variance);

                // 变异系数 (CV) - 值越小，规律性越好
                features.Regularity = avgDistance > 0 ?
                    1.0 - Math.Min(1.0, stdDev / avgDistance) : 0.0;
            }

            return features;
        }

        /// <summary>
        /// 平滑直方图
        /// </summary>
        /// <param name="histogram">原始直方图</param>
        /// <param name="windowSize">窗口大小</param>
        /// <returns>平滑后的直方图</returns>
        private int[] SmoothHistogram(int[] histogram, int windowSize)
        {
            int[] smoothed = new int[histogram.Length];
            int halfWindow = windowSize / 2;

            for (int i = 0; i < histogram.Length; i++)
            {
                int sum = 0;
                int count = 0;

                for (int j = Math.Max(0, i - halfWindow); j <= Math.Min(histogram.Length - 1, i + halfWindow); j++)
                {
                    sum += histogram[j];
                    count++;
                }

                smoothed[i] = count > 0 ? sum / count : 0;
            }

            return smoothed;
        }

        /// <summary>
        /// 检测直方图中的峰值
        /// </summary>
        /// <param name="histogram">直方图</param>
        /// <returns>峰值索引列表</returns>
        private List<int> DetectPeaks(int[] histogram)
        {
            var peaks = new List<int>();

            // 忽略边缘
            for (int i = 1; i < histogram.Length - 1; i++)
            {
                // 简单峰值检测：当前值大于左右相邻值
                if (histogram[i] > histogram[i - 1] && histogram[i] > histogram[i + 1])
                {
                    // 确保峰值足够高，避免噪声
                    double avgHeight = (histogram[i - 1] + histogram[i + 1]) / 2.0;
                    if (histogram[i] > avgHeight * 1.2 && histogram[i] >= 3) // 至少20%高于平均值且至少有3个单元
                    {
                        peaks.Add(i);
                    }
                }
            }

            return peaks;
        }

        /// <summary>
        /// 计算峰值之间的平均谷高度
        /// </summary>
        /// <param name="histogram">直方图</param>
        /// <param name="peaks">峰值索引列表</param>
        /// <returns>平均谷高度</returns>
        private double CalculateAverageValleyHeight(int[] histogram, List<int> peaks)
        {
            if (peaks.Count < 2) return 0;

            var valleyHeights = new List<int>();

            // 对每对相邻峰值，找出它们之间的最低点
            for (int i = 0; i < peaks.Count - 1; i++)
            {
                int start = peaks[i];
                int end = peaks[i + 1];

                int minHeight = int.MaxValue;
                for (int j = start + 1; j < end; j++)
                {
                    minHeight = Math.Min(minHeight, histogram[j]);
                }

                if (minHeight < int.MaxValue)
                {
                    valleyHeights.Add(minHeight);
                }
            }

            return valleyHeights.Count > 0 ? valleyHeights.Average() : 0;
        }

        /// <summary>
        /// 判断哪个方向的直方图结构更明显
        /// </summary>
        /// <param name="horizontal">水平方向直方图特征</param>
        /// <param name="vertical">垂直方向直方图特征</param>
        /// <returns>水平方向是否更有结构</returns>
        private bool IsMoreStructured(HistogramFeatures horizontal, HistogramFeatures vertical)
        {
            // 计算结构化分数
            double horizontalScore = CalculateStructureScore(horizontal);
            double verticalScore = CalculateStructureScore(vertical);

            // 1. 处理峰值质量差异显著的情况
            // 使用峰值质量综合评估，而不是仅依赖峰值数量
            double horizontalQuality = CalculatePeakQuality(horizontal);
            double verticalQuality = CalculatePeakQuality(vertical);
            double peakQualityRatio = 0.0;
            bool hasSignificantQualityDifference = false;

            // 安全计算质量比例
            if (horizontalQuality > 0.1 && verticalQuality > 0.1)
            {
                peakQualityRatio = Math.Max(horizontalQuality, verticalQuality) /
                                  Math.Min(horizontalQuality, verticalQuality);

                // 质量差异阈值随文本块数量动态调整
                double peakQualityThreshold = Math.Max(2.0, 3.0 - Math.Log10(Math.Max(10, this.cells.Count)) * 0.3);

                if (peakQualityRatio >= peakQualityThreshold)
                {
                    hasSignificantQualityDifference = true;
                    if (horizontalQuality > verticalQuality)
                    {
                        System.Diagnostics.Debug.WriteLine($"水平方向峰值质量({horizontalQuality:F2})显著高于垂直方向({verticalQuality:F2})，比例:{peakQualityRatio:F1} >= {peakQualityThreshold:F1}，判定水平方向更有结构");
                        return true;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"垂直方向峰值质量({verticalQuality:F2})显著高于水平方向({horizontalQuality:F2})，比例:{peakQualityRatio:F1} >= {peakQualityThreshold:F1}，判定垂直方向更有结构");
                        return false;
                    }
                }
            }

            // 处理一个方向有峰值而另一个方向峰值质量极低的情况
            if ((horizontal.PeakCount > 0 && verticalQuality < 0.2) ||
                (vertical.PeakCount > 0 && horizontalQuality < 0.2))
            {
                if (horizontalQuality > 1.0 && verticalQuality < 0.2)
                {
                    System.Diagnostics.Debug.WriteLine($"水平方向有有效峰值(质量:{horizontalQuality:F2})，垂直方向峰值质量极低({verticalQuality:F2})，判定水平方向更有结构");
                    return true;
                }
                else if (verticalQuality > 1.0 && horizontalQuality < 0.2)
                {
                    System.Diagnostics.Debug.WriteLine($"垂直方向有有效峰值(质量:{verticalQuality:F2})，水平方向峰值质量极低({horizontalQuality:F2})，判定垂直方向更有结构");
                    return false;
                }
            }

            // 2. 处理峰值清晰度差异显著的情况
            // 使用相对比例而非固定倍数阈值
            if (horizontal.PeakClearness > 0 && vertical.PeakClearness > 0)
            {
                // 计算清晰度比例
                double clearnessRatio = Math.Max(horizontal.PeakClearness, vertical.PeakClearness) /
                                       Math.Min(horizontal.PeakClearness, vertical.PeakClearness);

                // 动态计算清晰度比例阈值，文本块越多，阈值越低
                double clearnessRatioThreshold = Math.Max(1.5, 2.0 - Math.Log10(Math.Max(10, this.cells.Count)) * 0.15);

                // 清晰度基础阈值，文本块越多，基础阈值越低
                double baseThreshold = Math.Max(1.2, 1.5 - Math.Log10(Math.Max(10, this.cells.Count)) * 0.1);

                // 一个方向有清晰的峰值，另一个方向峰值不清晰
                if (clearnessRatio >= clearnessRatioThreshold)
                {
                    if (horizontal.PeakClearness > vertical.PeakClearness && horizontal.PeakClearness > baseThreshold)
                    {
                        System.Diagnostics.Debug.WriteLine($"水平方向峰值清晰度({horizontal.PeakClearness:F2})显著高于垂直方向({vertical.PeakClearness:F2})，比例:{clearnessRatio:F1} >= {clearnessRatioThreshold:F1}，判定水平方向更有结构");
                        return true;
                    }
                    else if (vertical.PeakClearness > horizontal.PeakClearness && vertical.PeakClearness > baseThreshold)
                    {
                        System.Diagnostics.Debug.WriteLine($"垂直方向峰值清晰度({vertical.PeakClearness:F2})显著高于水平方向({horizontal.PeakClearness:F2})，比例:{clearnessRatio:F1} >= {clearnessRatioThreshold:F1}，判定垂直方向更有结构");
                        return false;
                    }
                }
            }

            // 3. 比较结构化分数
            // 使用综合结构得分和峰值质量进行加权评估
            double horizontalCombinedScore = horizontalScore * (1.0 + Math.Min(0.5, horizontalQuality * 0.1));
            double verticalCombinedScore = verticalScore * (1.0 + Math.Min(0.5, verticalQuality * 0.1));

            // 动态计算比例阈值，文本块越多，阈值越接近1.0（要求更明确的差异）
            double cellCountFactor = Math.Min(0.25, Math.Max(0.12, 0.25 - Math.Log10(Math.Max(10, this.cells.Count)) * 0.05));
            double upperThreshold = 1.0 + cellCountFactor;
            double lowerThreshold = 1.0 / upperThreshold;

            // 安全处理除零情况
            double ratio = verticalCombinedScore > 0 ? horizontalCombinedScore / verticalCombinedScore :
                          (horizontalCombinedScore > 0 ? 2.0 : 1.0);

            if (ratio > upperThreshold)
            {
                System.Diagnostics.Debug.WriteLine($"水平方向综合结构分数({horizontalCombinedScore:F2})明显高于垂直方向({verticalCombinedScore:F2})，比例:{ratio:F2} > {upperThreshold:F2}");
                return true;
            }
            else if (ratio < lowerThreshold)
            {
                System.Diagnostics.Debug.WriteLine($"垂直方向综合结构分数({verticalCombinedScore:F2})明显高于水平方向({horizontalCombinedScore:F2})，比例:{1 / ratio:F2} > {1 / lowerThreshold:F2}");
                return false;
            }

            // 4. 如果分数接近，使用峰值质量进行更精细的判断
            // 峰值质量已经综合考虑了峰值数量、清晰度和规律性

            // 动态计算峰值质量比例阈值，根据文本块数量调整
            double fineQualityThreshold = 1.0 + Math.Max(0.15, 0.25 - Math.Log10(Math.Max(10, this.cells.Count)) * 0.05);

            // 计算峰值质量比例
            double fineQualityRatio = Math.Max(horizontalQuality, verticalQuality) /
                                     Math.Max(0.1, Math.Min(horizontalQuality, verticalQuality));

            if (fineQualityRatio >= fineQualityThreshold)
            {
                if (horizontalQuality > verticalQuality)
                {
                    System.Diagnostics.Debug.WriteLine($"分数接近但水平方向峰值质量({horizontalQuality:F2})明显高于垂直方向({verticalQuality:F2})，比例:{fineQualityRatio:F2} >= {fineQualityThreshold:F2}");
                    return true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"分数接近但垂直方向峰值质量({verticalQuality:F2})明显高于水平方向({horizontalQuality:F2})，比例:{fineQualityRatio:F2} >= {fineQualityThreshold:F2}");
                    return false;
                }
            }

            // 如果峰值质量接近，考虑峰值清晰度和规律性的综合表现
            double horizontalPerformance = horizontal.PeakClearness * (1.0 + horizontal.Regularity);
            double verticalPerformance = vertical.PeakClearness * (1.0 + vertical.Regularity);

            double performanceRatio = Math.Max(horizontalPerformance, verticalPerformance) /
                                     Math.Max(0.001, Math.Min(horizontalPerformance, verticalPerformance));

            double performanceThreshold = 1.0 + Math.Max(0.2, 0.3 - Math.Log10(Math.Max(10, this.cells.Count)) * 0.05);

            if (performanceRatio >= performanceThreshold)
            {
                if (horizontalPerformance > verticalPerformance)
                {
                    System.Diagnostics.Debug.WriteLine($"分数接近但水平方向峰值表现({horizontalPerformance:F2})明显优于垂直方向({verticalPerformance:F2})，比例:{performanceRatio:F2} >= {performanceThreshold:F2}");
                    return true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"分数接近但垂直方向峰值表现({verticalPerformance:F2})明显优于水平方向({horizontalPerformance:F2})，比例:{performanceRatio:F2} >= {performanceThreshold:F2}");
                    return false;
                }
            }

            // 5. 特殊情况：中文竖排文本的特征处理
            // 对于竖排中文，通常垂直方向有更多的峰值，但水平方向峰值更清晰
            double squareTextRatio = CalculateSquareTextRatio(this.cells);
            if (squareTextRatio > 0.5)
            {
                System.Diagnostics.Debug.WriteLine($"检测到高比例方形文字({squareTextRatio:P0})，可能是中文文本");

                // 对于中文等方形文字，竖排布局时水平方向直方图通常更有结构（表示多列）
                // 如果方形文字比例高且水平方向有明显峰值，强制判断为竖排布局
                if (horizontal.PeakCount >= 2 && horizontal.PeakClearness > 1.2)
                {
                    System.Diagnostics.Debug.WriteLine($"方形文字比例高且水平方向有明显峰值(数量:{horizontal.PeakCount}, 清晰度:{horizontal.PeakClearness:F2})，强制判断为竖排布局");
                    return true; // 水平方向直方图更有结构，表明文本可能是竖排的(多列)
                }

                // 对于单列竖排中文，通常水平方向和垂直方向峰值都不明显
                // 此时应该根据方形文字比例来判断
                if (horizontal.PeakCount <= 1 && vertical.PeakCount <= 1 &&
                    horizontal.PeakClearness < 1.5 && vertical.PeakClearness < 1.5)
                {
                    System.Diagnostics.Debug.WriteLine($"方形文字比例高且两个方向峰值都不明显，可能是单列竖排中文，强制判断为竖排布局");
                    return true; // 返回true表示水平方向更有结构，这里是为了让系统判断为竖排布局
                }
            }

            // 6. 如果前面的判断都接近，考虑更多特征的综合评估
            // 创建综合评分系统，考虑多种因素的加权平均
            double horizontalFeatureScore = 0;
            double verticalFeatureScore = 0;

            // 因子1：峰值数量（权重0.3）
            if (horizontal.PeakCount > 0 || vertical.PeakCount > 0)
            {
                double peakCountRatio = 0;
                if (horizontal.PeakCount > 0 && vertical.PeakCount > 0)
                {
                    peakCountRatio = Math.Max(horizontal.PeakCount, vertical.PeakCount) /
                                    Math.Max(1.0, Math.Min(horizontal.PeakCount, vertical.PeakCount));
                }
                else if (horizontal.PeakCount > 0)
                {
                    peakCountRatio = 2.0;
                    horizontalFeatureScore += 0.3;
                }
                else if (vertical.PeakCount > 0)
                {
                    peakCountRatio = 2.0;
                    verticalFeatureScore += 0.3;
                }

                if (peakCountRatio > 1.2)
                {
                    if (horizontal.PeakCount > vertical.PeakCount)
                    {
                        horizontalFeatureScore += 0.3 * Math.Min(1.0, (peakCountRatio - 1.0) * 0.5);
                    }
                    else if (vertical.PeakCount > horizontal.PeakCount)
                    {
                        verticalFeatureScore += 0.3 * Math.Min(1.0, (peakCountRatio - 1.0) * 0.5);
                    }
                }
            }

            // 因子2：峰值清晰度（权重0.4）
            if (horizontal.PeakClearness > 0 || vertical.PeakClearness > 0)
            {
                double clearnessRatio = 0;
                if (horizontal.PeakClearness > 0 && vertical.PeakClearness > 0)
                {
                    clearnessRatio = Math.Max(horizontal.PeakClearness, vertical.PeakClearness) /
                                    Math.Max(0.1, Math.Min(horizontal.PeakClearness, vertical.PeakClearness));
                }
                else if (horizontal.PeakClearness > 0)
                {
                    horizontalFeatureScore += 0.4;
                }
                else if (vertical.PeakClearness > 0)
                {
                    verticalFeatureScore += 0.4;
                }

                if (clearnessRatio > 1.3)
                {
                    if (horizontal.PeakClearness > vertical.PeakClearness)
                    {
                        horizontalFeatureScore += 0.4 * Math.Min(1.0, (clearnessRatio - 1.0) * 0.4);
                    }
                    else if (vertical.PeakClearness > horizontal.PeakClearness)
                    {
                        verticalFeatureScore += 0.4 * Math.Min(1.0, (clearnessRatio - 1.0) * 0.4);
                    }
                }
            }

            // 因子3：峰值规律性（权重0.3）
            if (horizontal.Regularity > 0 || vertical.Regularity > 0)
            {
                double regularityRatio = 0;
                if (horizontal.Regularity > 0.1 && vertical.Regularity > 0.1)
                {
                    regularityRatio = Math.Max(horizontal.Regularity, vertical.Regularity) /
                                     Math.Max(0.1, Math.Min(horizontal.Regularity, vertical.Regularity));
                }
                else if (horizontal.Regularity > 0.1)
                {
                    horizontalFeatureScore += 0.3;
                }
                else if (vertical.Regularity > 0.1)
                {
                    verticalFeatureScore += 0.3;
                }

                if (regularityRatio > 1.5)
                {
                    if (horizontal.Regularity > vertical.Regularity)
                    {
                        horizontalFeatureScore += 0.3 * Math.Min(1.0, (regularityRatio - 1.0) * 0.3);
                    }
                    else if (vertical.Regularity > horizontal.Regularity)
                    {
                        verticalFeatureScore += 0.3 * Math.Min(1.0, (regularityRatio - 1.0) * 0.3);
                    }
                }
            }

            // 计算最终得分差异
            double featureScoreDiff = Math.Abs(horizontalFeatureScore - verticalFeatureScore);
            if (featureScoreDiff > 0.15)
            {
                if (horizontalFeatureScore > verticalFeatureScore)
                {
                    System.Diagnostics.Debug.WriteLine($"综合评估后水平方向得分({horizontalFeatureScore:F2})高于垂直方向({verticalFeatureScore:F2})，差异:{featureScoreDiff:F2}");
                    return true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"综合评估后垂直方向得分({verticalFeatureScore:F2})高于水平方向({horizontalFeatureScore:F2})，差异:{featureScoreDiff:F2}");
                    return false;
                }
            }

            // 6. 默认情况下，综合比较所有特征
            // 最终比较综合结构分数、峰值质量和特征分数的加权平均
            double finalHorizontalScore = horizontalCombinedScore * 0.5 + horizontalQuality * 0.3 + horizontalFeatureScore * 0.2;
            double finalVerticalScore = verticalCombinedScore * 0.5 + verticalQuality * 0.3 + verticalFeatureScore * 0.2;

            System.Diagnostics.Debug.WriteLine($"所有判断条件接近，使用综合评分: 水平={finalHorizontalScore:F2} vs 垂直={finalVerticalScore:F2}");
            return finalHorizontalScore >= finalVerticalScore;
        }

        /// <summary>
        /// 计算直方图结构化得分
        /// </summary>
        /// <param name="features">直方图特征</param>
        /// <returns>结构化得分</returns>
        private double CalculateStructureScore(HistogramFeatures features)
        {
            // 基础分数 - 峰值数量是基础要素
            double baseScore = Math.Min(5.0, features.PeakCount * 1.0);

            // 无峰值时返回0分
            if (features.PeakCount == 0)
            {
                return 0;
            }

            // 单峰值情况 - 主要考虑峰值清晰度
            if (features.PeakCount == 1)
            {
                return baseScore * (0.5 + features.PeakClearness * 0.1);
            }

            // 多峰值情况 - 同时考虑清晰度和规律性
            double clearnessFactor = Math.Min(2.0, features.PeakClearness);
            double regularityFactor = features.PeakCount >= 3 ? features.Regularity : 0.5;

            // 峰值数量越多，规律性权重越高
            double regularityWeight = Math.Min(0.5, 0.2 + (features.PeakCount - 2) * 0.1);
            double clearnessWeight = 0.5 - regularityWeight;

            return baseScore * (1.0 + clearnessFactor * clearnessWeight + regularityFactor * regularityWeight);
        }

        /// <summary>
        /// 计算直方图分析得分
        /// </summary>
        /// <param name="horizontal">水平方向直方图特征</param>
        /// <param name="vertical">垂直方向直方图特征</param>
        /// <returns>分析得分</returns>
        private int CalculateHistogramScore(HistogramFeatures horizontal, HistogramFeatures vertical)
        {
            // 计算方向判断的确信度分数
            double horizontalScore = CalculateStructureScore(horizontal);
            double verticalScore = CalculateStructureScore(vertical);

            double diff = Math.Max(horizontalScore, verticalScore) -
                         Math.Min(horizontalScore, verticalScore);

            // 计算差异比例，而不是绝对差异
            double totalScore = horizontalScore + verticalScore;
            double diffRatio = totalScore > 0 ? diff / totalScore : 0;

            // 基于差异比例动态计算基础分数 - 差异比例越大，得分越高
            // 降低基础分范围：10-25 (之前是15-30)，减弱直方图分析的影响力
            int baseScore = 10 + (int)(diffRatio * 15);

            // 根据峰值数量比例调整权重
            if (horizontal.PeakCount > 0 || vertical.PeakCount > 0)
            {
                double peakRatio = 0;
                bool isExtremelyDominant = false; // 标记是否有极其明显的差异

                // 计算峰值比例
                if (horizontal.PeakCount > vertical.PeakCount && vertical.PeakCount > 0)
                {
                    peakRatio = (double)horizontal.PeakCount / vertical.PeakCount;
                    isExtremelyDominant = (horizontal.PeakCount >= 3 && peakRatio >= 4.0);
                }
                else if (vertical.PeakCount > horizontal.PeakCount && horizontal.PeakCount > 0)
                {
                    peakRatio = (double)vertical.PeakCount / horizontal.PeakCount;
                    isExtremelyDominant = (vertical.PeakCount >= 3 && peakRatio >= 4.0);
                }
                else if (horizontal.PeakCount > 0 && vertical.PeakCount == 0)
                {
                    // 动态计算单方向峰值的比例，基于峰值数量，增加单方向峰值的权重
                    peakRatio = Math.Min(15.0, 5.0 + horizontal.PeakCount * 1.5);
                    isExtremelyDominant = (horizontal.PeakCount >= 3);
                    System.Diagnostics.Debug.WriteLine($"水平方向有{horizontal.PeakCount}个峰值而垂直方向无峰值，计算比例为{peakRatio:F1}，极度明显:{isExtremelyDominant}");
                }
                else if (vertical.PeakCount > 0 && horizontal.PeakCount == 0)
                {
                    // 动态计算单方向峰值的比例，基于峰值数量，增加单方向峰值的权重
                    peakRatio = Math.Min(15.0, 5.0 + vertical.PeakCount * 1.5);
                    isExtremelyDominant = (vertical.PeakCount >= 3);
                    System.Diagnostics.Debug.WriteLine($"垂直方向有{vertical.PeakCount}个峰值而水平方向无峰值，计算比例为{peakRatio:F1}，极度明显:{isExtremelyDominant}");
                }

                // 非常明显的行列结构差异，直接给予非常高的权重
                if (isExtremelyDominant && horizontal.PeakClearness > 1.8 || vertical.PeakClearness > 1.8)
                {
                    // 如果结构差异非常明显，几乎可以确定方向
                    double extremeWeightFactor = Math.Min(5.0, 3.0 + peakRatio * 0.2);
                    baseScore = (int)(baseScore * extremeWeightFactor);
                    System.Diagnostics.Debug.WriteLine($"直方图分析：峰值差异极其明显，应用极高权重因子{extremeWeightFactor:F2}，几乎确定方向");
                }
                else
                {
                    // 使用连续函数计算权重因子，而不是离散阈值
                    // 峰值比例越大，权重因子越高
                    double weightFactor = 1.0 + Math.Min(2.5, (peakRatio - 1.0) * 0.6);
                    baseScore = (int)(baseScore * weightFactor);
                    System.Diagnostics.Debug.WriteLine($"直方图分析：峰值比例为{peakRatio:F2}，应用权重因子{weightFactor:F2}");
                }
            }

            // 考虑峰值清晰度和规律性，增加清晰度的权重
            double maxClearness = Math.Max(horizontal.PeakClearness, vertical.PeakClearness);
            double maxRegularity = Math.Max(horizontal.Regularity, vertical.Regularity);

            // 清晰度和规律性越高，得分越高，增加清晰度的影响
            double qualityBonus = Math.Min(2.0, 1.0 + (maxClearness - 1.0) * 0.25 + maxRegularity * 0.35);
            baseScore = (int)(baseScore * qualityBonus);

            if (qualityBonus > 1.05)
            {
                System.Diagnostics.Debug.WriteLine($"直方图质量较高(清晰度:{maxClearness:F2}, 规律性:{maxRegularity:F2})，应用质量加成{qualityBonus:F2}");
            }

            // 特殊处理：检查峰值质量差异
            // 计算峰值质量，这是一个综合考虑峰值数量、清晰度和规律性的指标
            double horizontalPeakQuality = CalculatePeakQuality(horizontal);
            double verticalPeakQuality = CalculatePeakQuality(vertical);

            // 如果一个方向的峰值质量显著高于另一个方向，增加额外得分
            if (horizontalPeakQuality > 1.5 || verticalPeakQuality > 1.5)
            {
                double qualityRatio = Math.Max(horizontalPeakQuality, verticalPeakQuality) /
                                     Math.Max(0.1, Math.Min(horizontalPeakQuality, verticalPeakQuality));

                if (qualityRatio >= 2.5) // 降低阈值，更容易触发质量加成
                {
                    // 质量差异显著，增加额外得分
                    int qualityBonus2 = (int)(8 + Math.Min(15, qualityRatio * 1.5));
                    baseScore += qualityBonus2;

                    if (horizontalPeakQuality > verticalPeakQuality)
                    {
                        System.Diagnostics.Debug.WriteLine($"水平方向峰值质量({horizontalPeakQuality:F2})显著高于垂直方向({verticalPeakQuality:F2})，比例:{qualityRatio:F2} >= 3.00，暗示竖排布局 => 特殊竖排证据 +{qualityBonus2}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"垂直方向峰值质量({verticalPeakQuality:F2})显著高于水平方向({horizontalPeakQuality:F2})，比例:{qualityRatio:F2} >= 3.00，暗示横排布局 => 特殊横排证据 +{qualityBonus2}");
                    }
                }
            }

            // 设置动态上限，基于文本块数量和峰值特征
            int cellCount = this.cells.Count;

            // 检查是否有非常强的行列结构指示
            bool hasStrongRowColEvidence = (horizontal.PeakCount >= 3 && horizontal.PeakClearness > 2.0) ||
                                          (vertical.PeakCount >= 3 && vertical.PeakClearness > 2.0);

            // 降低直方图分析的最大影响力，减少其在决策中的权重
            int upperLimit = hasStrongRowColEvidence ?
                            // 非常明显的行列结构，但仍然限制其影响力
                            Math.Min(60, 45 + (int)(Math.Min(cellCount, 50) / 5)) :
                            // 普通情况，显著降低上限
                            Math.Min(40, 30 + (int)(Math.Min(cellCount, 50) / 8));

            return Math.Min(upperLimit, baseScore);
        }

        /// <summary>
        /// 直方图特征
        /// </summary>
        private class HistogramFeatures
        {
            public int PeakCount { get; set; } = 0;
            public double PeakClearness { get; set; } = 1.0; // 值越大，峰值越明显
            public double Regularity { get; set; } = 0.0;   // 值越接近1.0，规律性越好
        }

        /// <summary>
        /// 计算峰值总体质量
        /// </summary>
        /// <param name="features">直方图特征</param>
        /// <returns>峰值质量得分</returns>
        private double CalculatePeakQuality(HistogramFeatures features)
        {
            // 综合考虑峰值数量、清晰度和规律性
            // 即使峰值数量为0，也返回一个基于其他特征的小值，避免除零错误
            if (features.PeakCount == 0)
                return 0.1;

            // 基础分数 - 峰值数量是基础要素，但使用对数函数避免过度依赖具体数量
            double countFactor = Math.Min(2.5, 0.5 + Math.Log10(features.PeakCount + 1) * 1.2);

            // 清晰度因子 - 峰值越清晰，质量越高
            // 使用非线性函数，对高清晰度给予更多奖励
            double clearnessFactor = Math.Min(2.5, Math.Pow(features.PeakClearness, 0.8));

            // 规律性因子 - 峰值分布越规律，质量越高
            // 规律性对多峰值情况更重要
            double regularityWeight = Math.Min(1.0, 0.3 + features.PeakCount * 0.15);
            double regularityFactor = 1.0 + features.Regularity * regularityWeight;

            // 峰值质量评分 - 基础分数乘以清晰度和规律性因子
            double baseQuality = countFactor * clearnessFactor * regularityFactor;

            // 对单峰值情况进行特殊处理 - 确保单峰值但非常清晰的情况也能获得较高分数
            if (features.PeakCount == 1 && features.PeakClearness > 1.8)
            {
                double singlePeakBonus = Math.Min(1.5, 1.0 + (features.PeakClearness - 1.8) * 0.3);
                baseQuality *= singlePeakBonus;
            }

            // 对多峰值且规律性好的情况进行奖励
            if (features.PeakCount >= 3 && features.Regularity > 0.6)
            {
                double multiPeakBonus = Math.Min(1.5, 1.0 + (features.Regularity - 0.6) * 0.8);
                baseQuality *= multiPeakBonus;
            }

            // 调试输出
            System.Diagnostics.Debug.WriteLine($"峰值质量计算 - 峰值数:{features.PeakCount}, 清晰度:{features.PeakClearness:F2}, 规律性:{features.Regularity:F2}");
            System.Diagnostics.Debug.WriteLine($"  计算因子 - 数量因子:{countFactor:F2}, 清晰度因子:{clearnessFactor:F2}, 规律性因子:{regularityFactor:F2}");
            System.Diagnostics.Debug.WriteLine($"  最终质量得分:{baseQuality:F2}");

            return baseQuality;
        }
    }

    /// <summary>
    /// 文本流方向枚举
    /// </summary>
    public enum TextFlowDirection
    {
        /// <summary>从左到右（常见于拉丁语系、中文横排等）</summary>
        LeftToRight,
        /// <summary>从右到左（常见于阿拉伯语、希伯来语等）</summary>
        RightToLeft,
        /// <summary>从上到下（常见于中文竖排等）</summary>
        TopToBottom,
        /// <summary>从下到上（常见于车道提示语等）</summary>
        BottomToTop,
        /// <summary>混合方向（多种方向混合）</summary>
        Mixed
    }
}