{"RootPath": "D:\\Code\\CatchTools", "ProjectFileName": "OCRTools.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "BaseForm.cs"}, {"SourceFile": "Colors\\CMYK.cs"}, {"SourceFile": "Colors\\ColorBox.cs"}, {"SourceFile": "Colors\\ColorEventHandler.cs"}, {"SourceFile": "Colors\\ColorHelper.cs"}, {"SourceFile": "Colors\\ColorPicker.cs"}, {"SourceFile": "Colors\\ColorPickerForm.cs"}, {"SourceFile": "Colors\\ColorPickerForm.designer.cs"}, {"SourceFile": "Colors\\ColorSlider.cs"}, {"SourceFile": "Colors\\ColorUserControl.cs"}, {"SourceFile": "Colors\\HSB.cs"}, {"SourceFile": "Colors\\MyColor.cs"}, {"SourceFile": "Colors\\RGBA.cs"}, {"SourceFile": "Common\\CommonCDNRequest.cs"}, {"SourceFile": "Common\\CommonConfig.cs"}, {"SourceFile": "Common\\CommonGuide.cs"}, {"SourceFile": "Common\\CommonGuideForm.cs"}, {"SourceFile": "Common\\CommonPlug.cs"}, {"SourceFile": "Common\\CommonAutomation.cs"}, {"SourceFile": "Common\\CommonTask.cs"}, {"SourceFile": "Common\\CommonTheme.cs"}, {"SourceFile": "Common\\CommonThemeManager.cs"}, {"SourceFile": "Common\\CommonToImage.cs"}, {"SourceFile": "Common\\CommonTranslate.cs"}, {"SourceFile": "Common\\CommonUser.cs"}, {"SourceFile": "Common\\CommonEnumAction.cs"}, {"SourceFile": "Common\\FileDropHandler.cs"}, {"SourceFile": "Common\\IDropTargetHelper.cs"}, {"SourceFile": "Common\\ImageCompress.cs"}, {"SourceFile": "Common\\ImageResourceManager.cs"}, {"SourceFile": "Common\\InternetExplorerFeatureControl.cs"}, {"SourceFile": "Common\\IpHelper.cs"}, {"SourceFile": "Common\\LoadingTypeHelper.cs"}, {"SourceFile": "Common\\SunTimes.cs"}, {"SourceFile": "Forms\\FrmBatchDect.cs"}, {"SourceFile": "Forms\\FrmBatchDect.Designer.cs"}, {"SourceFile": "Forms\\FrmBatchCompress.cs"}, {"SourceFile": "Forms\\FrmBatchCompress.Designer.cs"}, {"SourceFile": "Forms\\FrmReport.cs"}, {"SourceFile": "Forms\\FrmReport.Designer.cs"}, {"SourceFile": "Forms\\FrmViewUrl.cs"}, {"SourceFile": "Forms\\FrmViewUrl.Designer.cs"}, {"SourceFile": "Forms\\FormViewImage.cs"}, {"SourceFile": "Forms\\FormViewImage.Designer.cs"}, {"SourceFile": "GZip\\GZip.cs"}, {"SourceFile": "GZip\\GZipFileInfo.cs"}, {"SourceFile": "GZip\\GZipResult.cs"}, {"SourceFile": "Common\\SearchEngine.cs"}, {"SourceFile": "HotKeyType.cs"}, {"SourceFile": "ImageBox\\ImageBox.cs"}, {"SourceFile": "ImageBox\\ImageBoxGridDisplayMode.cs"}, {"SourceFile": "ImageBox\\ImageBoxGridScale.cs"}, {"SourceFile": "ImageBox\\ImageBoxPanDirection.cs"}, {"SourceFile": "ImageBox\\ImageBoxPanMode.cs"}, {"SourceFile": "ImageBox\\ImageBoxPanStyle.cs"}, {"SourceFile": "ImageBox\\ImageBoxSizeMode.cs"}, {"SourceFile": "ImageBox\\ImageBoxZoomActions.cs"}, {"SourceFile": "ImageBox\\ScrollControl.cs"}, {"SourceFile": "ImageBox\\VirtualScrollableControl.cs"}, {"SourceFile": "ImgUpload\\BaiDuUpload.cs"}, {"SourceFile": "ImgUpload\\BaseImageUpload.cs"}, {"SourceFile": "ImgUpload\\TencentNewUpload.cs"}, {"SourceFile": "ImgUpload\\TencentUpload.cs"}, {"SourceFile": "ImgUpload\\WebResizerUpload.cs"}, {"SourceFile": "ImgUpload\\ImageHelper.cs"}, {"SourceFile": "ImgUpload\\Net126Upload.cs"}, {"SourceFile": "ImgUpload\\SouGouImageUpload.cs"}, {"SourceFile": "ImgUpload\\TinyPngUpload.cs"}, {"SourceFile": "ImgUpload\\ALiYunUpload.cs"}, {"SourceFile": "ImgUpload\\_360ImageUpload.cs"}, {"SourceFile": "Language\\LanguageHelper.cs"}, {"SourceFile": "Language\\SupportedLanguage.cs"}, {"SourceFile": "Common\\LocalOcrService.cs"}, {"SourceFile": "NewForms\\CommonSetting.cs"}, {"SourceFile": "NewForms\\FormPDFProcess.cs"}, {"SourceFile": "NewForms\\FormPDFProcess.Designer.cs"}, {"SourceFile": "NewForms\\FormAreaCapture.cs"}, {"SourceFile": "NewForms\\FormAreaCapture.Designer.cs"}, {"SourceFile": "NewForms\\FormSetting.cs"}, {"SourceFile": "NewForms\\FormSetting.Designer.cs"}, {"SourceFile": "NQuant\\Box.cs"}, {"SourceFile": "NQuant\\ColorData.cs"}, {"SourceFile": "NQuant\\CubeCut.cs"}, {"SourceFile": "NQuant\\Lookup.cs"}, {"SourceFile": "NQuant\\LookupData.cs"}, {"SourceFile": "NQuant\\Pixel.cs"}, {"SourceFile": "NQuant\\QuantizedPalette.cs"}, {"SourceFile": "NQuant\\WuQuantizer.cs"}, {"SourceFile": "Common\\ObjectCopy.cs"}, {"SourceFile": "OCRTools\\CenterForm.cs"}, {"SourceFile": "OCRTools\\ColorBgra.cs"}, {"SourceFile": "OCRTools\\ComboxbtnRenderer.cs"}, {"SourceFile": "OCRTools\\CursorEx.cs"}, {"SourceFile": "OCRTools\\ExtensionMethods.cs"}, {"SourceFile": "OCRTools\\ImageHelp.cs"}, {"SourceFile": "OCRTools\\MathHelpers.cs"}, {"SourceFile": "OCRTools\\RenderHelper.cs"}, {"SourceFile": "OCRTools\\SelectRectangleList.cs"}, {"SourceFile": "OCRTools\\UnsafeBitmap.cs"}, {"SourceFile": "OCRTools\\Vector.cs"}, {"SourceFile": "OCRTools\\Win32.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\AnimationManager.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Forms\\ImageAnimatorHelper.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\CharsetDetector.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Analyzers\\CharDistributionAnalyser.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Analyzers\\Chinese\\BIG5DistributionAnalyser.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Analyzers\\Chinese\\EUCTWDistributionAnalyser.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Analyzers\\Chinese\\GB18030DistributionAnalyser.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\BitPackage.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\InputState.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Models\\MultiByte\\Chinese\\BIG5SMModel.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Models\\MultiByte\\Chinese\\EUCTWSMModel.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Models\\MultiByte\\Chinese\\GB18030_SMModel.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Models\\MultiByte\\Chinese\\HZ_GB_2312_SMModel.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Models\\MultiByte\\Chinese\\Iso_2022_CN_SMModel.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Models\\MultiByte\\UTF8_SMModel.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Models\\StateMachineModel.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Probers\\CharsetProber.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Probers\\CodingStateMachine.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Probers\\EscCharsetProber.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Probers\\Latin1Prober.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Probers\\MBCSGroupProber.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Probers\\MultiByte\\Chinese\\Big5Prober.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Probers\\MultiByte\\Chinese\\EUCTWProber.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Probers\\MultiByte\\Chinese\\GB18030Prober.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Probers\\MultiByte\\UTF8Prober.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\Code\\Probers\\ProbingState.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\DetectionDetail.cs"}, {"SourceFile": "OtherExt\\UtfUnknown\\DetectionResult.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\AnnotationPattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\Automation.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\AutomationElement.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\AutomationElementCollection.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\AutomationTypes.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\BasePattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\CacheRequest.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\Conditions.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\DockPattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\DragPattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\DropTargetPattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\ExpandCollapsePattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\GridPattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\InternalSchema.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\InternalTypes.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\InvokePattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\LegacyIAccessiblePattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\MultipleViewPattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\ObjectModelPattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\Point.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\ProviderInterfaces.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\Rect.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\ScrollPattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\SelectionPattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\Size.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\SpreadsheetPattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\StylesPattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\SynchronizedInput.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\TablePattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\TextChildPattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\TextPattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\TextRange.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\TextTypes.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\TogglePattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\TransformPattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\Utility.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\ValuePattern.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\VirtualizedPatterns.cs"}, {"SourceFile": "OtherExt\\UIAComWrapper\\WindowPattern.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "RecentControl\\BlackStyleLabel.cs"}, {"SourceFile": "Common\\CommonMsg.cs"}, {"SourceFile": "Common\\CommonResult.cs"}, {"SourceFile": "Common\\AutomationExtensions.cs"}, {"SourceFile": "Common\\CommonUpdate.cs"}, {"SourceFile": "Common\\Hook\\HookManager.Callbacks.cs"}, {"SourceFile": "Common\\Hook\\HookManager.cs"}, {"SourceFile": "Common\\Hook\\HookManager.Structures.cs"}, {"SourceFile": "Common\\Hook\\HookManager.Windows.cs"}, {"SourceFile": "Common\\Hook\\MouseEventExtArgs.cs"}, {"SourceFile": "Common\\Input\\HotKeyEntity.cs"}, {"SourceFile": "Common\\Input\\InputHelpers.cs"}, {"SourceFile": "Common\\Input\\InputManager.cs"}, {"SourceFile": "Common\\UserEntity.cs"}, {"SourceFile": "Common\\FlashWindowHelper.cs"}, {"SourceFile": "Common\\MemoryManager.cs"}, {"SourceFile": "Common\\MouseWheelMonitor.cs"}, {"SourceFile": "Common\\NativeMethods.cs"}, {"SourceFile": "Forms\\FormOCR.cs"}, {"SourceFile": "Forms\\FormOCR.designer.cs"}, {"SourceFile": "RecentControl\\FormRecent.cs"}, {"SourceFile": "RecentControl\\FormRecent.designer.cs"}, {"SourceFile": "Forms\\FormUpdate.cs"}, {"SourceFile": "Forms\\FormUpdate.designer.cs"}, {"SourceFile": "Forms\\FrmBatchOCR.cs"}, {"SourceFile": "Forms\\FrmBatchOCR.Designer.cs"}, {"SourceFile": "Forms\\FrmPicCompare.cs"}, {"SourceFile": "Forms\\FrmPicCompare.Designer.cs"}, {"SourceFile": "Common\\OcrProcessEntity.cs"}, {"SourceFile": "Common\\ExcelHelper.cs"}, {"SourceFile": "Common\\ImageProcessHelper.cs"}, {"SourceFile": "Common\\Log.cs"}, {"SourceFile": "Common\\ControlExtension.cs"}, {"SourceFile": "Common\\TableContentInfo.cs"}, {"SourceFile": "Expection\\ExceptionForm.cs"}, {"SourceFile": "Expection\\UHEHandler.cs"}, {"SourceFile": "Forms\\FormTool.cs"}, {"SourceFile": "Forms\\FormTool.designer.cs"}, {"SourceFile": "Forms\\FrmSearch.cs"}, {"SourceFile": "Forms\\FrmSearch.Designer.cs"}, {"SourceFile": "Forms\\FrmForgetPwd.cs"}, {"SourceFile": "Forms\\FrmForgetPwd.Designer.cs"}, {"SourceFile": "Forms\\FrmGoBuy.cs"}, {"SourceFile": "Forms\\FrmGoBuy.Designer.cs"}, {"SourceFile": "Forms\\FrmUserInfo.cs"}, {"SourceFile": "Forms\\FrmUserInfo.Designer.cs"}, {"SourceFile": "Forms\\FrmReg.cs"}, {"SourceFile": "Forms\\FrmReg.Designer.cs"}, {"SourceFile": "Forms\\FrmLogin.cs"}, {"SourceFile": "Forms\\FrmLogin.Designer.cs"}, {"SourceFile": "FrmMain.cs"}, {"SourceFile": "NewForms\\NotificationForm.cs"}, {"SourceFile": "Ruler\\Colors\\CommonThemes.cs"}, {"SourceFile": "Ruler\\Colors\\Theme.cs"}, {"SourceFile": "Ruler\\Forms\\CalibrationForm.cs"}, {"SourceFile": "Ruler\\Forms\\CalibrationForm.Designer.cs"}, {"SourceFile": "Ruler\\Forms\\RulerBaseForm.cs"}, {"SourceFile": "Ruler\\Forms\\RulerForm.cs"}, {"SourceFile": "Ruler\\Forms\\RulerForm.Designer.cs"}, {"SourceFile": "Ruler\\Forms\\RulerFormResizeMode.cs"}, {"SourceFile": "Ruler\\Forms\\RulerOverlayForm.cs"}, {"SourceFile": "Ruler\\Forms\\SetSizeForm.cs"}, {"SourceFile": "Ruler\\Forms\\SetSizeForm.Designer.cs"}, {"SourceFile": "Ruler\\MouseTracker.cs"}, {"SourceFile": "Ruler\\RulerMarker.cs"}, {"SourceFile": "Ruler\\RulerMarkerCollection.cs"}, {"SourceFile": "Ruler\\RulerPainter.cs"}, {"SourceFile": "Ruler\\Settings.cs"}, {"SourceFile": "Ruler\\Units\\MeasuringUnit.cs"}, {"SourceFile": "Ruler\\Units\\UnitConverters.cs"}, {"SourceFile": "ScrollingCapture\\ScrollingCapture.cs"}, {"SourceFile": "ScrollingCapture\\ScrollingCaptureManager.cs"}, {"SourceFile": "ShadowForm\\frmPasteImage.cs"}, {"SourceFile": "ShadowForm\\frmPasteImage.Designer.cs"}, {"SourceFile": "ShadowForm\\FormStyleAPI.cs"}, {"SourceFile": "ShadowForm\\ShadowForm.cs"}, {"SourceFile": "ShadowForm\\ShadowForm.designer.cs"}, {"SourceFile": "ShadowForm\\ShadowFormSkin.cs"}, {"SourceFile": "ShadowForm\\ShadowFormSkin.designer.cs"}, {"SourceFile": "Common\\OCRPoolProcess.cs"}, {"SourceFile": "Common\\ClipboardService.cs"}, {"SourceFile": "Common\\SpiltMode.cs"}, {"SourceFile": "Common\\UploadFileRequest.cs"}, {"SourceFile": "FrmMain.designer.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Components\\MetroStyleManager.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Components\\MetroToolTip.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Controls\\MetroButton.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Controls\\MetroCheckBox.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Controls\\MetroContextMenu.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Controls\\MetroScrollBar.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Controls\\MetroTabControl.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Controls\\MetroTabPage.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Design\\MetroButtonDesigner.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Design\\MetroCheckBoxDesigner.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Design\\MetroScrollBarDesigner.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Design\\MetroStyleManagerDesigner.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Design\\MetroTabControlDesigner.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Design\\MetroTabPageDesigner.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Drawing\\MetroImage.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Drawing\\MetroPaint.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Forms\\MetroForm.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Forms\\MetroFormShadowType.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Interfaces\\IMetroComponent.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Interfaces\\IMetroControl.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Interfaces\\IMetroForm.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\MetroBrushes.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\MetroColors.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\MetroColorStyle.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\MetroFonts.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\MetroPens.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\MetroThemeStyle.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Native\\DwmApi.cs"}, {"SourceFile": "OtherExt\\MetroFramework\\Native\\WinApi.cs"}, {"SourceFile": "Program.cs"}, {"SourceFile": "OCRTools\\StaticValue.cs"}, {"SourceFile": "OCRTools\\WindowInfo.cs"}, {"SourceFile": "Common\\HotKeyHelper.cs"}, {"SourceFile": "Common\\KillProcessHelper.cs"}, {"SourceFile": "Common\\BoxUtil.cs"}, {"SourceFile": "Common\\CommonEncryptHelper.cs"}, {"SourceFile": "Common\\CommonMethod.cs"}, {"SourceFile": "Common\\CommonString.cs"}, {"SourceFile": "Common\\DnsHelper.cs"}, {"SourceFile": "Common\\IniHelper.cs"}, {"SourceFile": "Common\\OcrContent.cs"}, {"SourceFile": "Common\\ServerTime.cs"}, {"SourceFile": "Common\\SNtpClient.cs"}, {"SourceFile": "Common\\StringExtension.cs"}, {"SourceFile": "Common\\WebClientExt.cs"}, {"SourceFile": "Common\\OcrHelper.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Properties\\Settings.Designer.cs"}, {"SourceFile": "Common\\TimerTaskService.cs"}, {"SourceFile": "RecentControl\\TaskRoundedCornerPanel.cs"}, {"SourceFile": "RecentControl\\ThumbnailSizeForm.cs"}, {"SourceFile": "RecentControl\\ThumbnailSizeForm.designer.cs"}, {"SourceFile": "ShareX\\Animations\\BaseAnimation.cs"}, {"SourceFile": "ShareX\\Animations\\OpacityAnimation.cs"}, {"SourceFile": "ShareX\\Animations\\RectangleAnimation.cs"}, {"SourceFile": "ShareX\\Animations\\TextAnimation.cs"}, {"SourceFile": "ShareX\\Controls\\LabeledNumericUpDown.cs"}, {"SourceFile": "ShareX\\Controls\\LabeledNumericUpDown.Designer.cs"}, {"SourceFile": "ShareX\\Controls\\LineShapeComboBox.cs"}, {"SourceFile": "ShareX\\Controls\\LineShapeComboBox.Designer.cs"}, {"SourceFile": "ShareX\\Controls\\ToolStripLabeledComboBox.cs"}, {"SourceFile": "ShareX\\Controls\\ToolStripRadioButtonMenuItem.cs"}, {"SourceFile": "ShareX\\Enums.cs"}, {"SourceFile": "ShareX\\Forms\\RegionCaptureForm.cs"}, {"SourceFile": "ShareX\\Forms\\TextDrawingInputBox.cs"}, {"SourceFile": "ShareX\\Forms\\TextDrawingInputBox.Designer.cs"}, {"SourceFile": "ShareX\\GraphicsQualityManager.cs"}, {"SourceFile": "ShareX\\RegionCaptureOptions.cs"}, {"SourceFile": "ShareX\\RegionHelpers\\ImageEditorControl.cs"}, {"SourceFile": "ShareX\\RegionHelpers\\InputManager.cs"}, {"SourceFile": "ShareX\\RegionHelpers\\MouseState.cs"}, {"SourceFile": "ShareX\\RegionHelpers\\ResizeNode.cs"}, {"SourceFile": "ShareX\\RegionHelpers\\SnapSize.cs"}, {"SourceFile": "ShareX\\Screenshot.cs"}, {"SourceFile": "ShareX\\Shapes\\AnnotationOptions.cs"}, {"SourceFile": "ShareX\\Shapes\\BaseShape.cs"}, {"SourceFile": "ShareX\\Shapes\\Drawing\\ArrowDrawingShape.cs"}, {"SourceFile": "ShareX\\Shapes\\Drawing\\BaseDrawingShape.cs"}, {"SourceFile": "ShareX\\Shapes\\Drawing\\EllipseDrawingShape.cs"}, {"SourceFile": "ShareX\\Shapes\\Drawing\\FreehandDrawingShape.cs"}, {"SourceFile": "ShareX\\Shapes\\Drawing\\LineDrawingShape.cs"}, {"SourceFile": "ShareX\\Shapes\\Drawing\\MagnifyDrawingShape.cs"}, {"SourceFile": "ShareX\\Shapes\\Drawing\\RectangleDrawingShape.cs"}, {"SourceFile": "ShareX\\Shapes\\Drawing\\SmartEraserDrawingShape.cs"}, {"SourceFile": "ShareX\\Shapes\\Drawing\\SpeechBalloonDrawingShape.cs"}, {"SourceFile": "ShareX\\Shapes\\Drawing\\StepDrawingShape.cs"}, {"SourceFile": "ShareX\\Shapes\\Drawing\\TextDrawingShape.cs"}, {"SourceFile": "ShareX\\Shapes\\Drawing\\TextOutlineDrawingShape.cs"}, {"SourceFile": "ShareX\\Shapes\\Effect\\BaseEffectShape.cs"}, {"SourceFile": "ShareX\\Shapes\\Effect\\HighlightEffectShape.cs"}, {"SourceFile": "ShareX\\Shapes\\Effect\\PixelateEffectShape.cs"}, {"SourceFile": "ShareX\\Shapes\\Region\\BaseRegionShape.cs"}, {"SourceFile": "ShareX\\Shapes\\Region\\EllipseRegionShape.cs"}, {"SourceFile": "ShareX\\Shapes\\Region\\FreehandRegionShape.cs"}, {"SourceFile": "ShareX\\Shapes\\Region\\RectangleRegionShape.cs"}, {"SourceFile": "ShareX\\Shapes\\ShapeManager.cs"}, {"SourceFile": "ShareX\\Shapes\\ShapeManagerMenu.cs"}, {"SourceFile": "ShareX\\Shapes\\TextDrawingOptions.cs"}, {"SourceFile": "ShareX\\Shapes\\Tool\\BaseTool.cs"}, {"SourceFile": "Common\\ShortcutHelpers.cs"}, {"SourceFile": "UserControlEx\\FastBitmap.cs"}, {"SourceFile": "UserControlEx\\GraphicsPathHelper.cs"}, {"SourceFile": "UserControlEx\\MyPictureBox.cs"}, {"SourceFile": "UserControlEx\\MyPictureBox.designer.cs"}, {"SourceFile": "UserControlEx\\RichTextBoxEx.cs"}, {"SourceFile": "RecentControl\\TaskThumbnailPanel.cs"}, {"SourceFile": "RecentControl\\TaskThumbnailPanel.designer.cs"}, {"SourceFile": "RecentControl\\TaskThumbnailView.cs"}, {"SourceFile": "RecentControl\\TaskThumbnailView.designer.cs"}, {"SourceFile": "UserControlEx\\ScrollingText.cs"}, {"SourceFile": "UserControlEx\\SkinButton.cs"}, {"SourceFile": "UserControlEx\\SkinTools.cs"}, {"SourceFile": "UserControlEx\\RoundStyle.cs"}, {"SourceFile": "UserControlEx\\TextImage\\BaseImageModeHandler.cs"}, {"SourceFile": "UserControlEx\\TextImage\\MultiModeImageViewer.cs"}, {"SourceFile": "UserControlEx\\TextImage\\IImageModeHandler.cs"}, {"SourceFile": "UserControlEx\\TextImage\\DualModeImageViewer.cs"}, {"SourceFile": "UserControlEx\\TextImage\\ImageTextModeHandler.cs"}, {"SourceFile": "UserControlEx\\TextImage\\DocumentModeHandler.cs"}, {"SourceFile": "UserControlEx\\TextPositionMapper.cs"}, {"SourceFile": "UserControlEx\\ToolStripEx.cs"}, {"SourceFile": "UserControlEx\\ToolStripLabeledNumericUpDown.cs"}, {"SourceFile": "UserControlEx\\ToolStripCheckBoxControl.cs"}, {"SourceFile": "UserControlEx\\DataGridViewEx.cs"}, {"SourceFile": "UserControlEx\\ucContent.cs"}, {"SourceFile": "UserControlEx\\ucContent.Designer.cs"}, {"SourceFile": "UserControlEx\\ucLoading.cs"}, {"SourceFile": "UserControlEx\\ucLoading.Designer.cs"}, {"SourceFile": "WebBroswerEx\\SecurityManagerCOM.cs"}, {"SourceFile": "WebBroswerEx\\SecurityManagerHelper.cs"}, {"SourceFile": "WebBroswerEx\\WebBrowser2.cs"}, {"SourceFile": "WebBroswerEx\\WinInetInterop.cs"}, {"SourceFile": "UserControlEx\\YButton.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.5.AssemblyAttributes.cs"}], "References": [{"Reference": "D:\\Code\\CatchTools\\RefDll\\Interop.UIAutomationClient.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\Common7\\IDE\\PublicAssemblies\\Microsoft.mshtml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.5\\Microsoft.VisualBasic.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.5\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\Code\\CatchTools\\RefDll\\O2S.Components.PDFRender4NET.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.5\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.5\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.5\\System.Design.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.5\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.5\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.5\\System.Management.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.5\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.5\\System.Web.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.5\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.5\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\Code\\CatchTools\\bin\\Debug\\OCR Recognition Assistant.exe", "OutputItemRelativePath": "OCR Recognition Assistant.exe"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}