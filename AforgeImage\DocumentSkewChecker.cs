﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.Globalization;
using System.IO;
using System.Runtime.InteropServices;

namespace OCRTools.AforgeImage
{
    /// <summary>
    /// Skew angle checker for scanned documents.
    /// </summary>
    ///
    /// <remarks><para>The class implements document's skew checking algorithm, which is based
    /// on <see cref="T:AForge.Imaging.HoughLineTransformation">Hough line transformation</see>. The algorithm
    /// is based on searching for text base lines - black line of text bottoms' followed
    /// by white line below.</para>
    ///
    /// <para><note>The routine supposes that a white-background document is provided
    /// with black letters. The algorithm is not supposed for any type of objects, but for
    /// document images with text.</note></para>
    ///
    /// <para>The range of angles to detect is controlled by <see cref="P:AForge.Imaging.DocumentSkewChecker.MaxSkewToDetect" /> property.</para>
    ///
    /// <para>The filter accepts 8 bpp grayscale images for processing.</para>
    ///
    /// <para>Sample usage:</para>
    /// <code>
    /// // create instance of skew checker
    /// DocumentSkewChecker skewChecker = new DocumentSkewChecker( );
    /// // get documents skew angle
    /// double angle = skewChecker.GetSkewAngle( documentImage );
    /// // create rotation filter
    /// RotateBilinear rotationFilter = new RotateBilinear( -angle );
    /// rotationFilter.FillColor = Color.White;
    /// // rotate image applying the filter
    /// Bitmap rotatedImage = rotationFilter.Apply( documentImage );
    /// </code>
    ///
    /// <para><b>Initial image:</b></para>
    /// <img src="img/imaging/sample10.png" width="300" height="184" />
    /// <para><b>Deskewed image:</b></para>
    /// <img src="img/imaging/deskew.png" width="335" height="250" /> 
    /// </remarks>
    ///
    /// <seealso cref="T:AForge.Imaging.HoughLineTransformation" />
    public class DocumentSkewChecker
    {
        private int stepsPerDegree;

        private int houghHeight;

        private double thetaStep;

        private double maxSkewToDetect;

        private double[] sinMap;

        private double[] cosMap;

        private bool needToInitialize = true;

        private short[,] houghMap;

        private short maxMapIntensity;

        private int localPeakRadius = 4;

        private ArrayList lines = new ArrayList();

        /// <summary>
        /// Steps per degree, [1, 10].
        /// </summary>
        ///
        /// <remarks><para>The value defines quality of Hough transform and its ability to detect
        /// line slope precisely.</para>
        ///
        /// <para>Default value is set to <b>1</b>.</para>
        /// </remarks>
        public int StepsPerDegree
        {
            get => stepsPerDegree;
            set
            {
                stepsPerDegree = Math.Max(1, Math.Min(10, value));
                needToInitialize = true;
            }
        }

        /// <summary>
        /// Maximum skew angle to detect, [0, 45] degrees.
        /// </summary>
        ///
        /// <remarks><para>The value sets maximum document's skew angle to detect.
        /// Document's skew angle can be as positive (rotated counter clockwise), as negative
        /// (rotated clockwise). So setting this value to 25, for example, will lead to
        /// [-25, 25] degrees detection range.</para>
        ///
        /// <para>Scanned documents usually have skew in the [-20, 20] degrees range.</para>
        ///
        /// <para>Default value is set to <b>30</b>.</para>
        /// </remarks>
        public double MaxSkewToDetect
        {
            get => maxSkewToDetect;
            set
            {
                maxSkewToDetect = Math.Max(0.0, Math.Min(45.0, value));
                needToInitialize = true;
            }
        }

        /// <summary>
        /// Minimum angle to detect skew in degrees.
        /// </summary>
        ///
        /// <remarks><para><note>The property is deprecated and setting it has not any effect.
        /// Use <see cref="P:AForge.Imaging.DocumentSkewChecker.MaxSkewToDetect" /> property instead.</note></para></remarks>
        [Obsolete("The property is deprecated and setting it has not any effect. Use MaxSkewToDetect property instead.")]
        public double MinBeta
        {
            get => 0.0 - maxSkewToDetect;
            set
            {
            }
        }

        /// <summary>
        /// Maximum angle to detect skew in degrees.
        /// </summary>
        ///
        /// <remarks><para><note>The property is deprecated and setting it has not any effect.
        /// Use <see cref="P:AForge.Imaging.DocumentSkewChecker.MaxSkewToDetect" /> property instead.</note></para></remarks>
        [Obsolete("The property is deprecated and setting it has not any effect. Use MaxSkewToDetect property instead.")]
        public double MaxBeta
        {
            get => maxSkewToDetect;
            set
            {
            }
        }

        /// <summary>
        /// Radius for searching local peak value, [1, 10].
        /// </summary>
        ///
        /// <remarks><para>The value determines radius around a map's value, which is analyzed to determine
        /// if the map's value is a local maximum in specified area.</para>
        ///
        /// <para>Default value is set to <b>4</b>.</para></remarks>
        public int LocalPeakRadius
        {
            get => localPeakRadius;
            set => localPeakRadius = Math.Max(1, Math.Min(10, value));
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="T:AForge.Imaging.DocumentSkewChecker" /> class.
        /// </summary>
        public DocumentSkewChecker()
        {
            StepsPerDegree = 10;
            MaxSkewToDetect = 30.0;
        }

        /// <summary>
        /// Get skew angle of the provided document image.
        /// </summary>
        ///
        /// <param name="image">Document's image to get skew angle of.</param>
        ///
        /// <returns>Returns document's skew angle. If the returned angle equals to -90,
        /// then document skew detection has failed.</returns>
        ///
        /// <exception cref="T:AForge.Imaging.UnsupportedImageFormatException">Unsupported pixel format of the source image.</exception>
        public double GetSkewAngle(Bitmap image)
        {
            return GetSkewAngle(image, new Rectangle(0, 0, image.Width, image.Height));
        }

        /// <summary>
        /// Get skew angle of the provided document image.
        /// </summary>
        ///
        /// <param name="image">Document's image to get skew angle of.</param>
        /// <param name="rect">Image's rectangle to process (used to exclude processing of
        /// regions, which are not relevant to skew detection).</param>
        ///
        /// <returns>Returns document's skew angle. If the returned angle equals to -90,
        /// then document skew detection has failed.</returns>
        ///
        /// <exception cref="T:AForge.Imaging.UnsupportedImageFormatException">Unsupported pixel format of the source image.</exception>
        public double GetSkewAngle(Bitmap image, Rectangle rect)
        {
            //if (image.PixelFormat != PixelFormat.Format8bppIndexed)
            //{
            //    throw new UnsupportedImageFormatException("Unsupported pixel format of the source image.");
            //}
            BitmapData bitmapData = image.LockBits(new Rectangle(0, 0, image.Width, image.Height), ImageLockMode.ReadOnly, PixelFormat.Format8bppIndexed);
            try
            {
                return GetSkewAngle(new UnmanagedImage(bitmapData), rect);
            }
            finally
            {
                image.UnlockBits(bitmapData);
            }
        }

        /// <summary>
        /// Get skew angle of the provided document image.
        /// </summary>
        ///
        /// <param name="imageData">Document's image data to get skew angle of.</param>
        ///
        /// <returns>Returns document's skew angle. If the returned angle equals to -90,
        /// then document skew detection has failed.</returns>
        ///
        /// <exception cref="T:AForge.Imaging.UnsupportedImageFormatException">Unsupported pixel format of the source image.</exception>
        public double GetSkewAngle(BitmapData imageData)
        {
            return GetSkewAngle(new UnmanagedImage(imageData), new Rectangle(0, 0, imageData.Width, imageData.Height));
        }

        /// <summary>
        /// Get skew angle of the provided document image.
        /// </summary>
        ///
        /// <param name="imageData">Document's image data to get skew angle of.</param>
        /// <param name="rect">Image's rectangle to process (used to exclude processing of
        /// regions, which are not relevant to skew detection).</param>
        ///
        /// <returns>Returns document's skew angle. If the returned angle equals to -90,
        /// then document skew detection has failed.</returns>
        ///
        /// <exception cref="T:AForge.Imaging.UnsupportedImageFormatException">Unsupported pixel format of the source image.</exception>
        public double GetSkewAngle(BitmapData imageData, Rectangle rect)
        {
            return GetSkewAngle(new UnmanagedImage(imageData), rect);
        }

        /// <summary>
        /// Get skew angle of the provided document image.
        /// </summary>
        ///
        /// <param name="image">Document's unmanaged image to get skew angle of.</param>
        ///
        /// <returns>Returns document's skew angle. If the returned angle equals to -90,
        /// then document skew detection has failed.</returns>
        ///
        /// <exception cref="T:AForge.Imaging.UnsupportedImageFormatException">Unsupported pixel format of the source image.</exception>
        public double GetSkewAngle(UnmanagedImage image)
        {
            return GetSkewAngle(image, new Rectangle(0, 0, image.Width, image.Height));
        }

        /// <summary>
        /// Get skew angle of the provided document image.
        /// </summary>
        ///
        /// <param name="image">Document's unmanaged image to get skew angle of.</param>
        /// <param name="rect">Image's rectangle to process (used to exclude processing of
        /// regions, which are not relevant to skew detection).</param>
        ///
        /// <returns>Returns document's skew angle. If the returned angle equals to -90,
        /// then document skew detection has failed.</returns>
        ///
        /// <exception cref="T:AForge.Imaging.UnsupportedImageFormatException">Unsupported pixel format of the source image.</exception>
        public unsafe double GetSkewAngle(UnmanagedImage image, Rectangle rect)
        {
            if (image.PixelFormat != PixelFormat.Format8bppIndexed)
            {
                throw new UnsupportedImageFormatException("Unsupported pixel format of the source image.");
            }
            InitHoughMap();
            int width = image.Width;
            int height = image.Height;
            int num = width / 2;
            int num2 = height / 2;
            rect.Intersect(new Rectangle(0, 0, width, height));
            int num3 = -num + rect.Left;
            int num4 = -num2 + rect.Top;
            int num5 = width - num - (width - rect.Right);
            int num6 = height - num2 - (height - rect.Bottom) - 1;
            int num7 = image.Stride - rect.Width;
            int num8 = (int)Math.Sqrt(num * num + num2 * num2);
            int num9 = num8 * 2;
            houghMap = new short[houghHeight, num9];
            byte* ptr = (byte*)image.ImageData.ToPointer() + rect.Top * (long)image.Stride + rect.Left;
            byte* ptr2 = ptr + image.Stride;
            for (int i = num4; i < num6; i++)
            {
                int num10 = num3;
                while (num10 < num5)
                {
                    if (*ptr < 128 && *ptr2 >= 128)
                    {
                        for (int j = 0; j < houghHeight; j++)
                        {
                            int num11 = (int)(cosMap[j] * num10 - sinMap[j] * i) + num8;
                            if (num11 >= 0 && num11 < num9)
                            {
                                houghMap[j, num11]++;
                            }
                        }
                    }
                    num10++;
                    ptr++;
                    ptr2++;
                }
                ptr += num7;
                ptr2 += num7;
            }
            maxMapIntensity = 0;
            for (int k = 0; k < houghHeight; k++)
            {
                for (int l = 0; l < num9; l++)
                {
                    if (houghMap[k, l] > maxMapIntensity)
                    {
                        maxMapIntensity = houghMap[k, l];
                    }
                }
            }
            var lineCount = Math.Min((width / 10), 10);
            CollectLines((short)lineCount);
            HoughLine[] mostIntensiveLines = GetMostIntensiveLines(10);
            double num12 = 0.0;
            double num13 = 0.0;
            HoughLine[] array = mostIntensiveLines;
            foreach (HoughLine houghLine in array)
            {
                if (houghLine.RelativeIntensity > 0.5)
                {
                    num12 += houghLine.Theta * houghLine.RelativeIntensity;
                    num13 += houghLine.RelativeIntensity;
                }
            }
            if (mostIntensiveLines.Length > 0)
            {
                num12 /= num13;
            }
            return num12 - 90.0;
        }

        private HoughLine[] GetMostIntensiveLines(int count)
        {
            int num = Math.Min(count, lines.Count);
            HoughLine[] array = new HoughLine[num];
            lines.CopyTo(0, array, 0, num);
            return array;
        }

        private void CollectLines(short minLineIntensity)
        {
            int length = houghMap.GetLength(0);
            int length2 = houghMap.GetLength(1);
            int num = length2 >> 1;
            lines.Clear();
            for (int i = 0; i < length; i++)
            {
                for (int j = 0; j < length2; j++)
                {
                    short num2 = houghMap[i, j];
                    if (num2 < minLineIntensity)
                    {
                        continue;
                    }
                    bool flag = false;
                    int k = i - localPeakRadius;
                    for (int num3 = i + localPeakRadius; k < num3; k++)
                    {
                        if (k < 0)
                        {
                            continue;
                        }
                        if (k >= length || flag)
                        {
                            break;
                        }
                        int l = j - localPeakRadius;
                        for (int num4 = j + localPeakRadius; l < num4; l++)
                        {
                            if (l >= 0)
                            {
                                if (l >= length2)
                                {
                                    break;
                                }
                                if (houghMap[k, l] > num2)
                                {
                                    flag = true;
                                    break;
                                }
                            }
                        }
                    }
                    if (!flag)
                    {
                        lines.Add(new HoughLine(90.0 - maxSkewToDetect + i / (double)stepsPerDegree, (short)(j - num), num2, num2 / (double)maxMapIntensity));
                    }
                }
            }
            lines.Sort();
        }

        private void InitHoughMap()
        {
            if (needToInitialize)
            {
                needToInitialize = false;
                houghHeight = (int)(2.0 * maxSkewToDetect * stepsPerDegree);
                thetaStep = 2.0 * maxSkewToDetect * Math.PI / 180.0 / houghHeight;
                sinMap = new double[houghHeight];
                cosMap = new double[houghHeight];
                double num = 90.0 - maxSkewToDetect;
                for (int i = 0; i < houghHeight; i++)
                {
                    sinMap[i] = Math.Sin(num * Math.PI / 180.0 + i * thetaStep);
                    cosMap[i] = Math.Cos(num * Math.PI / 180.0 + i * thetaStep);
                }
            }
        }
    }

    /// <summary>
    /// Hough line.
    /// </summary>
    ///
    /// <remarks><para>Represents line of Hough Line transformation using
    /// <a href="http://en.wikipedia.org/wiki/Polar_coordinate_system">polar coordinates</a>.
    /// See <a href="http://en.wikipedia.org/wiki/Polar_coordinate_system#Converting_between_polar_and_Cartesian_coordinates">Wikipedia</a>
    /// for information on how to convert polar coordinates to Cartesian coordinates.
    /// </para>
    ///
    /// <para><note><see cref="T:AForge.Imaging.HoughLineTransformation">Hough Line transformation</see> does not provide
    /// information about lines start and end points, only slope and distance from image's center. Using
    /// only provided information it is not possible to draw the detected line as it exactly appears on
    /// the source image. But it is possible to draw a line through the entire image, which contains the
    /// source line (see sample code below).
    /// </note></para>
    ///
    /// <para>Sample code to draw detected Hough lines:</para>
    /// <code>
    /// HoughLineTransformation lineTransform = new HoughLineTransformation( );
    /// // apply Hough line transofrm
    /// lineTransform.ProcessImage( sourceImage );
    /// Bitmap houghLineImage = lineTransform.ToBitmap( );
    /// // get lines using relative intensity
    /// HoughLine[] lines = lineTransform.GetLinesByRelativeIntensity( 0.5 );
    ///
    /// foreach ( HoughLine line in lines )
    /// {
    ///     // get line's radius and theta values
    ///     int    r = line.Radius;
    ///     double t = line.Theta;
    ///
    ///     // check if line is in lower part of the image
    ///     if ( r &lt; 0 )
    ///     {
    ///         t += 180;
    ///         r = -r;
    ///     }
    ///
    ///     // convert degrees to radians
    ///     t = ( t / 180 ) * Math.PI;
    ///
    ///     // get image centers (all coordinate are measured relative
    ///     // to center)
    ///     int w2 = image.Width /2;
    ///     int h2 = image.Height / 2;
    ///
    ///     double x0 = 0, x1 = 0, y0 = 0, y1 = 0;
    ///
    ///     if ( line.Theta != 0 )
    ///     {
    ///         // none-vertical line
    ///         x0 = -w2; // most left point
    ///         x1 = w2;  // most right point
    ///
    ///         // calculate corresponding y values
    ///         y0 = ( -Math.Cos( t ) * x0 + r ) / Math.Sin( t );
    ///         y1 = ( -Math.Cos( t ) * x1 + r ) / Math.Sin( t );
    ///     }
    ///     else
    ///     {
    ///         // vertical line
    ///         x0 = line.Radius;
    ///         x1 = line.Radius;
    ///
    ///         y0 = h2;
    ///         y1 = -h2;
    ///     }
    ///
    ///     // draw line on the image
    ///     Drawing.Line( sourceData,
    ///         new IntPoint( (int) x0 + w2, h2 - (int) y0 ),
    ///         new IntPoint( (int) x1 + w2, h2 - (int) y1 ),
    ///         Color.Red );
    /// }
    /// </code>
    ///
    /// <para>To clarify meaning of <see cref="F:AForge.Imaging.HoughLine.Radius" /> and <see cref="F:AForge.Imaging.HoughLine.Theta" /> values
    /// of detected Hough lines, let's take a look at the below sample image and
    /// corresponding values of radius and theta for the lines on the image:
    /// </para>
    ///
    /// <img src="img/imaging/sample15.png" width="400" height="300" />
    ///
    /// <para>Detected radius and theta values (color in corresponding colors):
    /// <list type="bullet">
    /// <item><font color="#FF0000">Theta = 90, R = 125, I = 249</font>;</item>
    /// <item><font color="#00FF00">Theta = 0, R = -170, I = 187</font> (converts to Theta = 180, R = 170);</item>
    /// <item><font color="#0000FF">Theta = 90, R = -58, I = 163</font> (converts to Theta = 270, R = 58);</item>
    /// <item><font color="#FFFF00">Theta = 101, R = -101, I = 130</font> (converts to Theta = 281, R = 101);</item>
    /// <item><font color="#FF8000">Theta = 0, R = 43, I = 112</font>;</item>
    /// <item><font color="#FF80FF">Theta = 45, R = 127, I = 82</font>.</item>
    /// </list>
    /// </para>
    ///
    /// </remarks>
    ///
    /// <seealso cref="T:AForge.Imaging.HoughLineTransformation" />
    public class HoughLine : IComparable
    {
        /// <summary>
        /// Line's slope - angle between polar axis and line's radius (normal going
        /// from pole to the line). Measured in degrees, [0, 180).
        /// </summary>
        public readonly double Theta;

        /// <summary>
        /// Line's distance from image center, (−∞, +∞).
        /// </summary>
        ///
        /// <remarks><note>Negative line's radius means, that the line resides in lower
        /// part of the polar coordinates system. This means that <see cref="F:AForge.Imaging.HoughLine.Theta" /> value
        /// should be increased by 180 degrees and radius should be made positive.
        /// </note></remarks>
        public readonly short Radius;

        /// <summary>
        /// Line's absolute intensity, (0, +∞).
        /// </summary>
        ///
        /// <remarks><para>Line's absolute intensity is a measure, which equals
        /// to number of pixels detected on the line. This value is bigger for longer
        /// lines.</para>
        ///
        /// <para><note>The value may not be 100% reliable to measure exact number of pixels
        /// on the line. Although these value correlate a lot (which means they are very close
        /// in most cases), the intensity value may slightly vary.</note></para>
        /// </remarks>
        public readonly short Intensity;

        /// <summary>
        /// Line's relative intensity, (0, 1].
        /// </summary>
        ///
        /// <remarks><para>Line's relative intensity is relation of line's <see cref="F:AForge.Imaging.HoughLine.Intensity" />
        /// value to maximum found intensity. For the longest line (line with highest intesity) the
        /// relative intensity is set to 1. If line's relative is set 0.5, for example, this means
        /// its intensity is half of maximum found intensity.</para>
        /// </remarks>
        public readonly double RelativeIntensity;

        /// <summary>
        /// Initializes a new instance of the <see cref="T:AForge.Imaging.HoughLine" /> class.
        /// </summary>
        ///
        /// <param name="theta">Line's slope.</param>
        /// <param name="radius">Line's distance from image center.</param>
        /// <param name="intensity">Line's absolute intensity.</param>
        /// <param name="relativeIntensity">Line's relative intensity.</param>
        public HoughLine(double theta, short radius, short intensity, double relativeIntensity)
        {
            Theta = theta;
            Radius = radius;
            Intensity = intensity;
            RelativeIntensity = relativeIntensity;
        }

        /// <summary>
        /// Compare the object with another instance of this class.
        /// </summary>
        ///
        /// <param name="value">Object to compare with.</param>
        ///
        /// <returns><para>A signed number indicating the relative values of this instance and <b>value</b>: 1) greater than zero - 
        /// this instance is greater than <b>value</b>; 2) zero - this instance is equal to <b>value</b>;
        /// 3) greater than zero - this instance is less than <b>value</b>.</para>
        ///
        /// <para><note>The sort order is descending.</note></para></returns>
        ///
        /// <remarks>
        /// <para><note>Object are compared using their <see cref="F:AForge.Imaging.HoughLine.Intensity">intensity</see> value.</note></para>
        /// </remarks>
        public int CompareTo(object value)
        {
            return -Intensity.CompareTo(((HoughLine)value).Intensity);
        }
    }



    /// <summary>
    /// Invalid image properties exception.
    /// </summary>
    ///
    /// <remarks><para>The invalid image properties exception is thrown in the case when
    /// user provides an image with certain properties, which are treated as invalid by
    /// particular image processing routine. Another case when this exception is
    /// thrown is the case when user tries to access some properties of an image (or
    /// of a recently processed image by some routine), which are not valid for that image.</para>
    /// </remarks>
    public class InvalidImagePropertiesException : ArgumentException
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="T:AForge.Imaging.InvalidImagePropertiesException" /> class.
        /// </summary>
        public InvalidImagePropertiesException()
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="T:AForge.Imaging.InvalidImagePropertiesException" /> class.
        /// </summary>
        ///
        /// <param name="message">Message providing some additional information.</param>
        public InvalidImagePropertiesException(string message)
            : base(message)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="T:AForge.Imaging.InvalidImagePropertiesException" /> class.
        /// </summary>
        ///
        /// <param name="message">Message providing some additional information.</param>
        /// <param name="paramName">Name of the invalid parameter.</param>
        public InvalidImagePropertiesException(string message, string paramName)
            : base(message, paramName)
        {
        }
    }


    /// <summary>
    /// Unsupported image format exception.
    /// </summary>
    ///
    /// <remarks><para>The unsupported image format exception is thrown in the case when
    /// user passes an image of certain format to an image processing routine, which does
    /// not support the format. Check documentation of the image processing routine
    /// to discover which formats are supported by the routine.</para>
    /// </remarks>
    public class UnsupportedImageFormatException : ArgumentException
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="T:AForge.Imaging.UnsupportedImageFormatException" /> class.
        /// </summary>
        public UnsupportedImageFormatException()
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="T:AForge.Imaging.UnsupportedImageFormatException" /> class.
        /// </summary>
        ///
        /// <param name="message">Message providing some additional information.</param>
        public UnsupportedImageFormatException(string message)
            : base(message)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="T:AForge.Imaging.UnsupportedImageFormatException" /> class.
        /// </summary>
        ///
        /// <param name="message">Message providing some additional information.</param>
        /// <param name="paramName">Name of the invalid parameter.</param>
        public UnsupportedImageFormatException(string message, string paramName)
            : base(message, paramName)
        {
        }
    }


    /// <summary>
    /// Image in unmanaged memory.
    /// </summary>
    ///
    /// <remarks>
    /// <para>The class represents wrapper of an image in unmanaged memory. Using this class
    /// it is possible as to allocate new image in unmanaged memory, as to just wrap provided
    /// pointer to unmanaged memory, where an image is stored.</para>
    ///
    /// <para>Usage of unmanaged images is mostly beneficial when it is required to apply <b>multiple</b>
    /// image processing routines to a single image. In such scenario usage of .NET managed images 
    /// usually leads to worse performance, because each routine needs to lock managed image
    /// before image processing is done and then unlock it after image processing is done. Without
    /// these lock/unlock there is no way to get direct access to managed image's data, which means
    /// there is no way to do fast image processing. So, usage of managed images lead to overhead, which
    /// is caused by locks/unlock. Unmanaged images are represented internally using unmanaged memory
    /// buffer. This means that it is not required to do any locks/unlocks in order to get access to image
    /// data (no overhead).</para>
    ///
    /// <para>Sample usage:</para>
    /// <code>
    /// // sample 1 - wrapping .NET image into unmanaged without
    /// // making extra copy of image in memory
    /// BitmapData imageData = image.LockBits(
    ///     new Rectangle( 0, 0, image.Width, image.Height ),
    ///     ImageLockMode.ReadWrite, image.PixelFormat );
    ///
    /// try
    /// {
    ///     UnmanagedImage unmanagedImage = new UnmanagedImage( imageData ) );
    ///     // apply several routines to the unmanaged image
    /// }
    /// finally
    /// {
    ///     image.UnlockBits( imageData );
    /// }
    ///
    ///
    /// // sample 2 - converting .NET image into unmanaged
    /// UnmanagedImage unmanagedImage = UnmanagedImage.FromManagedImage( image );
    /// // apply several routines to the unmanaged image
    /// ...
    /// // conver to managed image if it is required to display it at some point of time
    /// Bitmap managedImage = unmanagedImage.ToManagedImage( );
    /// </code>
    /// </remarks>
    public class UnmanagedImage : IDisposable
    {
        private IntPtr imageData;

        private int width;

        private int height;

        private int stride;

        private PixelFormat pixelFormat;

        private bool mustBeDisposed;

        /// <summary>
        /// Pointer to image data in unmanaged memory.
        /// </summary>
        public IntPtr ImageData => imageData;

        /// <summary>
        /// Image width in pixels.
        /// </summary>
        public int Width => width;

        /// <summary>
        /// Image height in pixels.
        /// </summary>
        public int Height => height;

        /// <summary>
        /// Image stride (line size in bytes).
        /// </summary>
        public int Stride => stride;

        /// <summary>
        /// Image pixel format.
        /// </summary>
        public PixelFormat PixelFormat => pixelFormat;

        /// <summary>
        /// Initializes a new instance of the <see cref="T:AForge.Imaging.UnmanagedImage" /> class.
        /// </summary>
        ///
        /// <param name="imageData">Pointer to image data in unmanaged memory.</param>
        /// <param name="width">Image width in pixels.</param>
        /// <param name="height">Image height in pixels.</param>
        /// <param name="stride">Image stride (line size in bytes).</param>
        /// <param name="pixelFormat">Image pixel format.</param>
        ///
        /// <remarks><para><note>Using this constructor, make sure all specified image attributes are correct
        /// and correspond to unmanaged memory buffer. If some attributes are specified incorrectly,
        /// this may lead to exceptions working with the unmanaged memory.</note></para></remarks>
        public UnmanagedImage(IntPtr imageData, int width, int height, int stride, PixelFormat pixelFormat)
        {
            this.imageData = imageData;
            this.width = width;
            this.height = height;
            this.stride = stride;
            this.pixelFormat = pixelFormat;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="T:AForge.Imaging.UnmanagedImage" /> class.
        /// </summary>
        ///
        /// <param name="bitmapData">Locked bitmap data.</param>
        ///
        /// <remarks><note>Unlike <see cref="M:AForge.Imaging.UnmanagedImage.FromManagedImage(System.Drawing.Imaging.BitmapData)" /> method, this constructor does not make
        /// copy of managed image. This means that managed image must stay locked for the time of using the instance
        /// of unamanged image.</note></remarks>
        public UnmanagedImage(BitmapData bitmapData)
        {
            imageData = bitmapData.Scan0;
            width = bitmapData.Width;
            height = bitmapData.Height;
            stride = bitmapData.Stride;
            pixelFormat = bitmapData.PixelFormat;
        }

        /// <summary>
        /// Destroys the instance of the <see cref="T:AForge.Imaging.UnmanagedImage" /> class.
        /// </summary>
        ~UnmanagedImage()
        {
            Dispose(disposing: false);
        }

        /// <summary>
        /// Dispose the object.
        /// </summary>
        ///
        /// <remarks><para>Frees unmanaged resources used by the object. The object becomes unusable
        /// after that.</para>
        ///
        /// <par><note>The method needs to be called only in the case if unmanaged image was allocated
        /// using <see cref="M:AForge.Imaging.UnmanagedImage.Create(System.Int32,System.Int32,System.Drawing.Imaging.PixelFormat)" /> method. In the case if the class instance was created using constructor,
        /// this method does not free unmanaged memory.</note></par>
        /// </remarks>
        public void Dispose()
        {
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Dispose the object.
        /// </summary>
        ///
        /// <param name="disposing">Indicates if disposing was initiated manually.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (mustBeDisposed && imageData != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(imageData);
                GC.RemoveMemoryPressure(stride * height);
                imageData = IntPtr.Zero;
            }
        }

        /// <summary>
        /// Clone the unmanaged images.
        /// </summary>
        ///
        /// <returns>Returns clone of the unmanaged image.</returns>
        ///
        /// <remarks><para>The method does complete cloning of the object.</para></remarks>
        public UnmanagedImage Clone()
        {
            IntPtr dst = Marshal.AllocHGlobal(stride * height);
            GC.AddMemoryPressure(stride * height);
            UnmanagedImage unmanagedImage = new UnmanagedImage(dst, width, height, stride, pixelFormat)
            {
                mustBeDisposed = true
            };
            SystemTools.CopyUnmanagedMemory(dst, imageData, stride * height);
            return unmanagedImage;
        }

        /// <summary>
        /// Copy unmanaged image.
        /// </summary>
        ///
        /// <param name="destImage">Destination image to copy this image to.</param>
        ///
        /// <remarks><para>The method copies current unmanaged image to the specified image.
        /// Size and pixel format of the destination image must be exactly the same.</para></remarks>
        ///
        /// <exception cref="T:AForge.Imaging.InvalidImagePropertiesException">Destination image has different size or pixel format.</exception>
        public unsafe void Copy(UnmanagedImage destImage)
        {
            if (width != destImage.width || height != destImage.height || pixelFormat != destImage.pixelFormat)
            {
                throw new InvalidImagePropertiesException("Destination image has different size or pixel format.");
            }
            if (stride == destImage.stride)
            {
                SystemTools.CopyUnmanagedMemory(destImage.imageData, imageData, stride * height);
                return;
            }
            int num = destImage.stride;
            int count = (stride < num) ? stride : num;
            byte* ptr = (byte*)imageData.ToPointer();
            byte* ptr2 = (byte*)destImage.imageData.ToPointer();
            for (int i = 0; i < height; i++)
            {
                SystemTools.CopyUnmanagedMemory(ptr2, ptr, count);
                ptr2 += num;
                ptr += stride;
            }
        }

        /// <summary>
        /// Allocate new image in unmanaged memory.
        /// </summary>
        ///
        /// <param name="width">Image width.</param>
        /// <param name="height">Image height.</param>
        /// <param name="pixelFormat">Image pixel format.</param>
        ///
        /// <returns>Return image allocated in unmanaged memory.</returns>
        ///
        /// <remarks><para>Allocate new image with specified attributes in unmanaged memory.</para>
        ///
        /// <para><note>The method supports only
        /// <see cref="T:System.Drawing.Imaging.PixelFormat">Format8bppIndexed</see>,
        /// <see cref="T:System.Drawing.Imaging.PixelFormat">Format16bppGrayScale</see>,
        /// <see cref="T:System.Drawing.Imaging.PixelFormat">Format24bppRgb</see>,
        /// <see cref="T:System.Drawing.Imaging.PixelFormat">Format32bppRgb</see>,
        /// <see cref="T:System.Drawing.Imaging.PixelFormat">Format32bppArgb</see>,
        /// <see cref="T:System.Drawing.Imaging.PixelFormat">Format32bppPArgb</see>,
        /// <see cref="T:System.Drawing.Imaging.PixelFormat">Format48bppRgb</see>,
        /// <see cref="T:System.Drawing.Imaging.PixelFormat">Format64bppArgb</see> and
        /// <see cref="T:System.Drawing.Imaging.PixelFormat">Format64bppPArgb</see> pixel formats.
        /// In the case if <see cref="T:System.Drawing.Imaging.PixelFormat">Format8bppIndexed</see>
        /// format is specified, pallete is not not created for the image (supposed that it is
        /// 8 bpp grayscale image).
        /// </note></para>
        /// </remarks>
        ///
        /// <exception cref="T:AForge.Imaging.UnsupportedImageFormatException">Unsupported pixel format was specified.</exception>
        /// <exception cref="T:AForge.Imaging.InvalidImagePropertiesException">Invalid image size was specified.</exception>
        public static UnmanagedImage Create(int width, int height, PixelFormat pixelFormat)
        {
            int num;
            switch (pixelFormat)
            {
                case PixelFormat.Format8bppIndexed:
                    num = 1;
                    break;
                case PixelFormat.Format16bppGrayScale:
                    num = 2;
                    break;
                case PixelFormat.Format24bppRgb:
                    num = 3;
                    break;
                case PixelFormat.Format32bppRgb:
                case PixelFormat.Format32bppPArgb:
                case PixelFormat.Format32bppArgb:
                    num = 4;
                    break;
                case PixelFormat.Format48bppRgb:
                    num = 6;
                    break;
                case PixelFormat.Format64bppPArgb:
                case PixelFormat.Format64bppArgb:
                    num = 8;
                    break;
                default:
                    throw new UnsupportedImageFormatException("Can not create image with specified pixel format.");
            }
            if (width <= 0 || height <= 0)
            {
                throw new InvalidImagePropertiesException("Invalid image size specified.");
            }
            int num2 = width * num;
            if (num2 % 4 != 0)
            {
                num2 += 4 - num2 % 4;
            }
            IntPtr dst = Marshal.AllocHGlobal(num2 * height);
            SystemTools.SetUnmanagedMemory(dst, 0, num2 * height);
            GC.AddMemoryPressure(num2 * height);
            UnmanagedImage unmanagedImage = new UnmanagedImage(dst, width, height, num2, pixelFormat)
            {
                mustBeDisposed = true
            };
            return unmanagedImage;
        }

        /// <summary>
        /// Create managed image from the unmanaged.
        /// </summary>
        ///
        /// <returns>Returns managed copy of the unmanaged image.</returns>
        ///
        /// <remarks><para>The method creates a managed copy of the unmanaged image with the
        /// same size and pixel format (it calls <see cref="M:AForge.Imaging.UnmanagedImage.ToManagedImage(System.Boolean)" /> specifying
        /// <see langword="true" /> for the <b>makeCopy</b> parameter).</para></remarks>
        public Bitmap ToManagedImage()
        {
            return ToManagedImage(makeCopy: true);
        }

        /// <summary>
        /// Create managed image from the unmanaged.
        /// </summary>
        ///
        /// <param name="makeCopy">Make a copy of the unmanaged image or not.</param>
        ///
        /// <returns>Returns managed copy of the unmanaged image.</returns>
        ///
        /// <remarks><para>If the <paramref name="makeCopy" /> is set to <see langword="true" />, then the method
        /// creates a managed copy of the unmanaged image, so the managed image stays valid even when the unmanaged
        /// image gets disposed. However, setting this parameter to <see langword="false" /> creates a managed image which is
        /// just a wrapper around the unmanaged image. So if unmanaged image is disposed, the
        /// managed image becomes no longer valid and accessing it will generate an exception.</para></remarks>
        ///
        /// <exception cref="T:AForge.Imaging.InvalidImagePropertiesException">The unmanaged image has some invalid properties, which results
        /// in failure of converting it to managed image. This may happen if user used the
        /// <see cref="M:AForge.Imaging.UnmanagedImage.#ctor(System.IntPtr,System.Int32,System.Int32,System.Int32,System.Drawing.Imaging.PixelFormat)" /> constructor specifying some
        /// invalid parameters.</exception>
        public unsafe Bitmap ToManagedImage(bool makeCopy)
        {
            Bitmap bitmap = null;
            try
            {
                if (!makeCopy)
                {
                    bitmap = new Bitmap(width, height, stride, pixelFormat, imageData);
                    if (pixelFormat == PixelFormat.Format8bppIndexed)
                    {
                        AForgeImage.SetGrayscalePalette(bitmap);
                    }
                }
                else
                {
                    bitmap = ((pixelFormat == PixelFormat.Format8bppIndexed) ? AForgeImage.CreateGrayscaleImage(width, height) : new Bitmap(width, height, pixelFormat));
                    BitmapData bitmapData = bitmap.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.ReadWrite, pixelFormat);
                    int num = bitmapData.Stride;
                    int count = Math.Min(stride, num);
                    byte* ptr = (byte*)bitmapData.Scan0.ToPointer();
                    byte* ptr2 = (byte*)imageData.ToPointer();
                    if (stride != num)
                    {
                        for (int i = 0; i < height; i++)
                        {
                            SystemTools.CopyUnmanagedMemory(ptr, ptr2, count);
                            ptr += num;
                            ptr2 += stride;
                        }
                    }
                    else
                    {
                        SystemTools.CopyUnmanagedMemory(ptr, ptr2, stride * height);
                    }
                    bitmap.UnlockBits(bitmapData);
                }
                return bitmap;
            }
            catch (Exception)
            {
                bitmap?.Dispose();
                throw new InvalidImagePropertiesException("The unmanaged image has some invalid properties, which results in failure of converting it to managed image.");
            }
        }

        /// <summary>
        /// Create unmanaged image from the specified managed image.
        /// </summary>
        ///
        /// <param name="image">Source managed image.</param>
        ///
        /// <returns>Returns new unmanaged image, which is a copy of source managed image.</returns>
        ///
        /// <remarks><para>The method creates an exact copy of specified managed image, but allocated
        /// in unmanaged memory.</para></remarks>
        ///
        /// <exception cref="T:AForge.Imaging.UnsupportedImageFormatException">Unsupported pixel format of source image.</exception>
        public static UnmanagedImage FromManagedImage(Bitmap image)
        {
            BitmapData bitmapdata = image.LockBits(new Rectangle(0, 0, image.Width, image.Height), ImageLockMode.ReadOnly, image.PixelFormat);
            try
            {
                return FromManagedImage(bitmapdata);
            }
            finally
            {
                image.UnlockBits(bitmapdata);
            }
        }

        /// <summary>
        /// Create unmanaged image from the specified managed image.
        /// </summary>
        ///
        /// <param name="imageData">Source locked image data.</param>
        ///
        /// <returns>Returns new unmanaged image, which is a copy of source managed image.</returns>
        ///
        /// <remarks><para>The method creates an exact copy of specified managed image, but allocated
        /// in unmanaged memory. This means that managed image may be unlocked right after call to this
        /// method.</para></remarks>
        ///
        /// <exception cref="T:AForge.Imaging.UnsupportedImageFormatException">Unsupported pixel format of source image.</exception>
        public static UnmanagedImage FromManagedImage(BitmapData imageData)
        {
            PixelFormat pixelFormat = imageData.PixelFormat;
            if (pixelFormat != PixelFormat.Format8bppIndexed && pixelFormat != PixelFormat.Format16bppGrayScale && pixelFormat != PixelFormat.Format24bppRgb && pixelFormat != PixelFormat.Format32bppRgb && pixelFormat != PixelFormat.Format32bppArgb && pixelFormat != PixelFormat.Format32bppPArgb && pixelFormat != PixelFormat.Format48bppRgb && pixelFormat != PixelFormat.Format64bppArgb && pixelFormat != PixelFormat.Format64bppPArgb)
            {
                throw new UnsupportedImageFormatException("Unsupported pixel format of the source image.");
            }
            IntPtr dst = Marshal.AllocHGlobal(imageData.Stride * imageData.Height);
            GC.AddMemoryPressure(imageData.Stride * imageData.Height);
            UnmanagedImage unmanagedImage = new UnmanagedImage(dst, imageData.Width, imageData.Height, imageData.Stride, pixelFormat);
            SystemTools.CopyUnmanagedMemory(dst, imageData.Scan0, imageData.Stride * imageData.Height);
            unmanagedImage.mustBeDisposed = true;
            return unmanagedImage;
        }

        /// <summary>
        /// Collect pixel values from the specified list of coordinates.
        /// </summary>
        ///
        /// <param name="points">List of coordinates to collect pixels' value from.</param>
        ///
        /// <returns>Returns array of pixels' values from the specified coordinates.</returns>
        ///
        /// <remarks><para>The method goes through the specified list of points and for each point retrievs
        /// corresponding pixel's value from the unmanaged image.</para>
        ///
        /// <para><note>For grayscale image the output array has the same length as number of points in the
        /// specified list of points. For color image the output array has triple length, containing pixels'
        /// values in RGB order.</note></para>
        ///
        /// <para><note>The method does not make any checks for valid coordinates and leaves this up to user.
        /// If specified coordinates are out of image's bounds, the result is not predictable (crash in most cases).
        /// </note></para>
        ///
        /// <para><note>This method is supposed for images with 8 bpp channels only (8 bpp grayscale image and
        /// 24/32 bpp color images).</note></para>
        /// </remarks>
        ///
        /// <exception cref="T:AForge.Imaging.UnsupportedImageFormatException">Unsupported pixel format of the source image. Use Collect16bppPixelValues() method for
        /// images with 16 bpp channels.</exception>
        public unsafe byte[] Collect8bppPixelValues(List<IntPoint> points)
        {
            int num = System.Drawing.Image.GetPixelFormatSize(pixelFormat) / 8;
            if (pixelFormat == PixelFormat.Format16bppGrayScale || num > 4)
            {
                throw new UnsupportedImageFormatException("Unsupported pixel format of the source image. Use Collect16bppPixelValues() method for it.");
            }
            byte[] array = new byte[points.Count * ((pixelFormat == PixelFormat.Format8bppIndexed) ? 1 : 3)];
            byte* ptr = (byte*)imageData.ToPointer();
            if (pixelFormat == PixelFormat.Format8bppIndexed)
            {
                int num2 = 0;
                {
                    foreach (IntPoint point in points)
                    {
                        IntPoint current = point;
                        byte* ptr2 = ptr + stride * (long)current.Y + current.X;
                        array[num2++] = *ptr2;
                    }
                    return array;
                }
            }
            int num4 = 0;
            foreach (IntPoint point2 in points)
            {
                IntPoint current2 = point2;
                byte* ptr2 = ptr + stride * (long)current2.Y + current2.X * (long)num;
                array[num4++] = ptr2[2];
                array[num4++] = ptr2[1];
                array[num4++] = *ptr2;
            }
            return array;
        }

        /// <summary>
        /// Collect coordinates of none black pixels in the image.
        /// </summary>
        ///
        /// <returns>Returns list of points, which have other than black color.</returns>
        public List<IntPoint> CollectActivePixels()
        {
            return CollectActivePixels(new Rectangle(0, 0, width, height));
        }

        /// <summary>
        /// Collect coordinates of none black pixels within specified rectangle of the image.
        /// </summary>
        ///
        /// <param name="rect">Image's rectangle to process.</param>
        ///
        /// <returns>Returns list of points, which have other than black color.</returns>
        public unsafe List<IntPoint> CollectActivePixels(Rectangle rect)
        {
            List<IntPoint> list = new List<IntPoint>();
            int num = System.Drawing.Image.GetPixelFormatSize(pixelFormat) / 8;
            rect.Intersect(new Rectangle(0, 0, width, height));
            int x = rect.X;
            int y = rect.Y;
            int right = rect.Right;
            int bottom = rect.Bottom;
            byte* ptr = (byte*)imageData.ToPointer();
            if (pixelFormat == PixelFormat.Format16bppGrayScale || num > 4)
            {
                int num2 = num >> 1;
                for (int i = y; i < bottom; i++)
                {
                    ushort* ptr2 = (ushort*)(ptr + i * (long)stride + x * (long)num);
                    if (num2 == 1)
                    {
                        int num3 = x;
                        while (num3 < right)
                        {
                            if (*ptr2 != 0)
                            {
                                list.Add(new IntPoint(num3, i));
                            }
                            num3++;
                            ptr2++;
                        }
                        continue;
                    }
                    int num4 = x;
                    while (num4 < right)
                    {
                        if (ptr2[2] != 0 || ptr2[1] != 0 || *ptr2 != 0)
                        {
                            list.Add(new IntPoint(num4, i));
                        }
                        num4++;
                        ptr2 += num2;
                    }
                }
            }
            else
            {
                for (int j = y; j < bottom; j++)
                {
                    byte* ptr3 = ptr + j * (long)stride + x * (long)num;
                    if (num == 1)
                    {
                        int num5 = x;
                        while (num5 < right)
                        {
                            if (*ptr3 != 0)
                            {
                                list.Add(new IntPoint(num5, j));
                            }
                            num5++;
                            ptr3++;
                        }
                        continue;
                    }
                    int num6 = x;
                    while (num6 < right)
                    {
                        if (ptr3[2] != 0 || ptr3[1] != 0 || *ptr3 != 0)
                        {
                            list.Add(new IntPoint(num6, j));
                        }
                        num6++;
                        ptr3 += num;
                    }
                }
            }
            return list;
        }

        /// <summary>
        /// Set pixels with the specified coordinates to the specified color.
        /// </summary>
        ///
        /// <param name="coordinates">List of points to set color for.</param>
        /// <param name="color">Color to set for the specified points.</param>
        ///
        /// <remarks><para><note>For images having 16 bpp per color plane, the method extends the specified color
        /// value to 16 bit by multiplying it by 256.</note></para></remarks>
        public unsafe void SetPixels(List<IntPoint> coordinates, Color color)
        {
            int num = System.Drawing.Image.GetPixelFormatSize(pixelFormat) / 8;
            byte* ptr = (byte*)imageData.ToPointer();
            byte r = color.R;
            byte g = color.G;
            byte b = color.B;
            byte a = color.A;
            switch (pixelFormat)
            {
                case PixelFormat.Format8bppIndexed:
                    {
                        byte b2 = (byte)(0.2125 * r + 0.7154 * g + 0.0721 * b);
                        foreach (IntPoint coordinate in coordinates)
                        {
                            IntPoint current6 = coordinate;
                            if (current6.X >= 0 && current6.Y >= 0 && current6.X < width && current6.Y < height)
                            {
                                byte* ptr7 = ptr + current6.Y * (long)stride + current6.X;
                                *ptr7 = b2;
                            }
                        }
                        break;
                    }
                case PixelFormat.Format24bppRgb:
                case PixelFormat.Format32bppRgb:
                    foreach (IntPoint coordinate2 in coordinates)
                    {
                        IntPoint current5 = coordinate2;
                        if (current5.X >= 0 && current5.Y >= 0 && current5.X < width && current5.Y < height)
                        {
                            byte* ptr6 = ptr + current5.Y * (long)stride + current5.X * (long)num;
                            ptr6[2] = r;
                            ptr6[1] = g;
                            *ptr6 = b;
                        }
                    }
                    break;
                case PixelFormat.Format32bppArgb:
                    foreach (IntPoint coordinate3 in coordinates)
                    {
                        IntPoint current4 = coordinate3;
                        if (current4.X >= 0 && current4.Y >= 0 && current4.X < width && current4.Y < height)
                        {
                            byte* ptr5 = ptr + current4.Y * (long)stride + current4.X * (long)num;
                            ptr5[2] = r;
                            ptr5[1] = g;
                            *ptr5 = b;
                            ptr5[3] = a;
                        }
                    }
                    break;
                case PixelFormat.Format16bppGrayScale:
                    {
                        ushort num9 = (ushort)((ushort)(0.2125 * r + 0.7154 * g + 0.0721 * b) << 8);
                        foreach (IntPoint coordinate4 in coordinates)
                        {
                            IntPoint current3 = coordinate4;
                            if (current3.X >= 0 && current3.Y >= 0 && current3.X < width && current3.Y < height)
                            {
                                ushort* ptr4 = (ushort*)(ptr + current3.Y * (long)stride + current3.X * 2L);
                                *ptr4 = num9;
                            }
                        }
                        break;
                    }
                case PixelFormat.Format48bppRgb:
                    {
                        ushort num6 = (ushort)(r << 8);
                        ushort num7 = (ushort)(g << 8);
                        ushort num8 = (ushort)(b << 8);
                        foreach (IntPoint coordinate5 in coordinates)
                        {
                            IntPoint current2 = coordinate5;
                            if (current2.X >= 0 && current2.Y >= 0 && current2.X < width && current2.Y < height)
                            {
                                ushort* ptr3 = (ushort*)(ptr + current2.Y * (long)stride + current2.X * (long)num);
                                ptr3[2] = num6;
                                ptr3[1] = num7;
                                *ptr3 = num8;
                            }
                        }
                        break;
                    }
                case PixelFormat.Format64bppArgb:
                    {
                        ushort num2 = (ushort)(r << 8);
                        ushort num3 = (ushort)(g << 8);
                        ushort num4 = (ushort)(b << 8);
                        ushort num5 = (ushort)(a << 8);
                        foreach (IntPoint coordinate6 in coordinates)
                        {
                            IntPoint current = coordinate6;
                            if (current.X >= 0 && current.Y >= 0 && current.X < width && current.Y < height)
                            {
                                ushort* ptr2 = (ushort*)(ptr + current.Y * (long)stride + current.X * (long)num);
                                ptr2[2] = num2;
                                ptr2[1] = num3;
                                *ptr2 = num4;
                                ptr2[3] = num5;
                            }
                        }
                        break;
                    }
                default:
                    throw new UnsupportedImageFormatException("The pixel format is not supported: " + pixelFormat);
            }
        }

        /// <summary>
        /// Set pixel with the specified coordinates to the specified color.
        /// </summary>
        ///
        /// <param name="point">Point's coordiates to set color for.</param>
        /// <param name="color">Color to set for the pixel.</param>
        ///
        /// <remarks><para>See <see cref="M:AForge.Imaging.UnmanagedImage.SetPixel(System.Int32,System.Int32,System.Drawing.Color)" /> for more information.</para></remarks>
        public void SetPixel(IntPoint point, Color color)
        {
            SetPixel(point.X, point.Y, color);
        }

        /// <summary>
        /// Set pixel with the specified coordinates to the specified color.
        /// </summary>
        ///
        /// <param name="x">X coordinate of the pixel to set.</param>
        /// <param name="y">Y coordinate of the pixel to set.</param>
        /// <param name="color">Color to set for the pixel.</param>
        ///
        /// <remarks><para><note>For images having 16 bpp per color plane, the method extends the specified color
        /// value to 16 bit by multiplying it by 256.</note></para>
        ///
        /// <para>For grayscale images this method will calculate intensity value based on the below formula:
        /// <code lang="none">
        /// 0.2125 * Red + 0.7154 * Green + 0.0721 * Blue
        /// </code>
        /// </para>
        /// </remarks>
        public void SetPixel(int x, int y, Color color)
        {
            SetPixel(x, y, color.R, color.G, color.B, color.A);
        }

        /// <summary>
        /// Set pixel with the specified coordinates to the specified value.
        /// </summary>
        ///
        /// <param name="x">X coordinate of the pixel to set.</param>
        /// <param name="y">Y coordinate of the pixel to set.</param>
        /// <param name="value">Pixel value to set.</param>
        ///
        /// <remarks><para>The method sets all color components of the pixel to the specified value.
        /// If it is a grayscale image, then pixel's intensity is set to the specified value.
        /// If it is a color image, then pixel's R/G/B components are set to the same specified value
        /// (if an image has alpha channel, then it is set to maximum value - 255 or 65535).</para>
        ///
        /// <para><note>For images having 16 bpp per color plane, the method extends the specified color
        /// value to 16 bit by multiplying it by 256.</note></para>
        /// </remarks>
        public void SetPixel(int x, int y, byte value)
        {
            SetPixel(x, y, value, value, value, byte.MaxValue);
        }

        private unsafe void SetPixel(int x, int y, byte r, byte g, byte b, byte a)
        {
            if (x >= 0 && y >= 0 && x < width && y < height)
            {
                int num = System.Drawing.Image.GetPixelFormatSize(pixelFormat) / 8;
                byte* ptr = (byte*)imageData.ToPointer() + y * (long)stride + x * (long)num;
                ushort* ptr2 = (ushort*)ptr;
                switch (pixelFormat)
                {
                    case PixelFormat.Format8bppIndexed:
                        *ptr = (byte)(0.2125 * r + 0.7154 * g + 0.0721 * b);
                        break;
                    case PixelFormat.Format24bppRgb:
                    case PixelFormat.Format32bppRgb:
                        ptr[2] = r;
                        ptr[1] = g;
                        *ptr = b;
                        break;
                    case PixelFormat.Format32bppArgb:
                        ptr[2] = r;
                        ptr[1] = g;
                        *ptr = b;
                        ptr[3] = a;
                        break;
                    case PixelFormat.Format16bppGrayScale:
                        *ptr2 = (ushort)((ushort)(0.2125 * r + 0.7154 * g + 0.0721 * b) << 8);
                        break;
                    case PixelFormat.Format48bppRgb:
                        ptr2[2] = (ushort)(r << 8);
                        ptr2[1] = (ushort)(g << 8);
                        *ptr2 = (ushort)(b << 8);
                        break;
                    case PixelFormat.Format64bppArgb:
                        ptr2[2] = (ushort)(r << 8);
                        ptr2[1] = (ushort)(g << 8);
                        *ptr2 = (ushort)(b << 8);
                        ptr2[3] = (ushort)(a << 8);
                        break;
                    default:
                        throw new UnsupportedImageFormatException("The pixel format is not supported: " + pixelFormat);
                }
            }
        }

        /// <summary>
        /// Get color of the pixel with the specified coordinates.
        /// </summary>
        ///
        /// <param name="point">Point's coordiates to get color of.</param>
        ///
        /// <returns>Return pixel's color at the specified coordinates.</returns>
        ///
        /// <remarks><para>See <see cref="M:AForge.Imaging.UnmanagedImage.GetPixel(System.Int32,System.Int32)" /> for more information.</para></remarks>
        public Color GetPixel(IntPoint point)
        {
            return GetPixel(point.X, point.Y);
        }

        /// <summary>
        /// Get color of the pixel with the specified coordinates.
        /// </summary>
        ///
        /// <param name="x">X coordinate of the pixel to get.</param>
        /// <param name="y">Y coordinate of the pixel to get.</param>
        ///
        /// <returns>Return pixel's color at the specified coordinates.</returns>
        ///
        /// <remarks>
        /// <para><note>In the case if the image has 8 bpp grayscale format, the method will return a color with
        /// all R/G/B components set to same value, which is grayscale intensity.</note></para>
        ///
        /// <para><note>The method supports only 8 bpp grayscale images and 24/32 bpp color images so far.</note></para>
        /// </remarks>
        ///
        /// <exception cref="T:System.ArgumentOutOfRangeException">The specified pixel coordinate is out of image's bounds.</exception>
        /// <exception cref="T:AForge.Imaging.UnsupportedImageFormatException">Pixel format of this image is not supported by the method.</exception>
        public unsafe Color GetPixel(int x, int y)
        {
            if (x < 0 || y < 0)
            {
                throw new ArgumentOutOfRangeException("x", "The specified pixel coordinate is out of image's bounds.");
            }
            if (x >= width || y >= height)
            {
                throw new ArgumentOutOfRangeException("y", "The specified pixel coordinate is out of image's bounds.");
            }
            int num = System.Drawing.Image.GetPixelFormatSize(pixelFormat) / 8;
            byte* ptr = (byte*)imageData.ToPointer() + y * (long)stride + x * (long)num;
            switch (pixelFormat)
            {
                case PixelFormat.Format8bppIndexed:
                    return Color.FromArgb(*ptr, *ptr, *ptr);
                case PixelFormat.Format24bppRgb:
                case PixelFormat.Format32bppRgb:
                    return Color.FromArgb(ptr[2], ptr[1], *ptr);
                case PixelFormat.Format32bppArgb:
                    return Color.FromArgb(ptr[3], ptr[2], ptr[1], *ptr);
                default:
                    throw new UnsupportedImageFormatException("The pixel format is not supported: " + pixelFormat);
            }
        }

        /// <summary>
        /// Collect pixel values from the specified list of coordinates.
        /// </summary>
        ///
        /// <param name="points">List of coordinates to collect pixels' value from.</param>
        ///
        /// <returns>Returns array of pixels' values from the specified coordinates.</returns>
        ///
        /// <remarks><para>The method goes through the specified list of points and for each point retrievs
        /// corresponding pixel's value from the unmanaged image.</para>
        ///
        /// <para><note>For grayscale image the output array has the same length as number of points in the
        /// specified list of points. For color image the output array has triple length, containing pixels'
        /// values in RGB order.</note></para>
        ///
        /// <para><note>The method does not make any checks for valid coordinates and leaves this up to user.
        /// If specified coordinates are out of image's bounds, the result is not predictable (crash in most cases).
        /// </note></para>
        ///
        /// <para><note>This method is supposed for images with 16 bpp channels only (16 bpp grayscale image and
        /// 48/64 bpp color images).</note></para>
        /// </remarks>
        ///
        /// <exception cref="T:AForge.Imaging.UnsupportedImageFormatException">Unsupported pixel format of the source image. Use Collect8bppPixelValues() method for
        /// images with 8 bpp channels.</exception>
        public unsafe ushort[] Collect16bppPixelValues(List<IntPoint> points)
        {
            int num = System.Drawing.Image.GetPixelFormatSize(pixelFormat) / 8;
            if (pixelFormat == PixelFormat.Format8bppIndexed || num == 3 || num == 4)
            {
                throw new UnsupportedImageFormatException("Unsupported pixel format of the source image. Use Collect8bppPixelValues() method for it.");
            }
            ushort[] array = new ushort[points.Count * ((pixelFormat == PixelFormat.Format16bppGrayScale) ? 1 : 3)];
            byte* ptr = (byte*)imageData.ToPointer();
            if (pixelFormat == PixelFormat.Format16bppGrayScale)
            {
                int num2 = 0;
                {
                    foreach (IntPoint point in points)
                    {
                        IntPoint current = point;
                        ushort* ptr2 = (ushort*)(ptr + stride * (long)current.Y + current.X * (long)num);
                        array[num2++] = *ptr2;
                    }
                    return array;
                }
            }
            int num4 = 0;
            foreach (IntPoint point2 in points)
            {
                IntPoint current2 = point2;
                ushort* ptr2 = (ushort*)(ptr + stride * (long)current2.Y + current2.X * (long)num);
                array[num4++] = ptr2[2];
                array[num4++] = ptr2[1];
                array[num4++] = *ptr2;
            }
            return array;
        }
    }

    /// <summary>
    /// Structure for representing a pair of coordinates of integer type.
    /// </summary>
    ///
    /// <remarks><para>The structure is used to store a pair of integer coordinates.</para>
    ///
    /// <para>Sample usage:</para>
    /// <code>
    /// // assigning coordinates in the constructor
    /// IntPoint p1 = new IntPoint( 10, 20 );
    /// // creating a point and assigning coordinates later
    /// IntPoint p2;
    /// p2.X = 30;
    /// p2.Y = 40;
    /// // calculating distance between two points
    /// float distance = p1.DistanceTo( p2 );
    /// </code>
    /// </remarks>
    [Serializable]
    public struct IntPoint
    {
        /// <summary> 
        /// X coordinate.
        /// </summary> 
        public int X;

        /// <summary> 
        /// Y coordinate.
        /// </summary> 
        public int Y;

        /// <summary>
        /// Initializes a new instance of the <see cref="T:AForge.IntPoint" /> structure.
        /// </summary>
        ///
        /// <param name="x">X axis coordinate.</param>
        /// <param name="y">Y axis coordinate.</param>
        public IntPoint(int x, int y)
        {
            X = x;
            Y = y;
        }

        /// <summary>
        /// Calculate Euclidean distance between two points.
        /// </summary>
        ///
        /// <param name="anotherPoint">Point to calculate distance to.</param>
        ///
        /// <returns>Returns Euclidean distance between this point and
        /// <paramref name="anotherPoint" /> points.</returns>
        public float DistanceTo(IntPoint anotherPoint)
        {
            int num = X - anotherPoint.X;
            int num2 = Y - anotherPoint.Y;
            return (float)Math.Sqrt(num * num + num2 * num2);
        }

        /// <summary>
        /// Calculate squared Euclidean distance between two points.
        /// </summary>
        ///
        /// <param name="anotherPoint">Point to calculate distance to.</param>
        ///
        /// <returns>Returns squared Euclidean distance between this point and
        /// <paramref name="anotherPoint" /> points.</returns>
        public float SquaredDistanceTo(Point anotherPoint)
        {
            float num = (float)X - anotherPoint.X;
            float num2 = (float)Y - anotherPoint.Y;
            return num * num + num2 * num2;
        }

        /// <summary>
        /// Addition operator - adds values of two points.
        /// </summary>
        ///
        /// <param name="point1">First point for addition.</param>
        /// <param name="point2">Second point for addition.</param>
        ///
        /// <returns>Returns new point which coordinates equal to sum of corresponding
        /// coordinates of specified points.</returns>
        public static IntPoint operator +(IntPoint point1, IntPoint point2)
        {
            return new IntPoint(point1.X + point2.X, point1.Y + point2.Y);
        }

        /// <summary>
        /// Addition operator - adds values of two points.
        /// </summary>
        ///
        /// <param name="point1">First point for addition.</param>
        /// <param name="point2">Second point for addition.</param>
        ///
        /// <returns>Returns new point which coordinates equal to sum of corresponding
        /// coordinates of specified points.</returns>
        public static IntPoint Add(IntPoint point1, IntPoint point2)
        {
            return new IntPoint(point1.X + point2.X, point1.Y + point2.Y);
        }

        /// <summary>
        /// Subtraction operator - subtracts values of two points.
        /// </summary>
        ///
        /// <param name="point1">Point to subtract from.</param>
        /// <param name="point2">Point to subtract.</param>
        ///
        /// <returns>Returns new point which coordinates equal to difference of corresponding
        /// coordinates of specified points.</returns>
        public static IntPoint operator -(IntPoint point1, IntPoint point2)
        {
            return new IntPoint(point1.X - point2.X, point1.Y - point2.Y);
        }

        /// <summary>
        /// Subtraction operator - subtracts values of two points.
        /// </summary>
        ///
        /// <param name="point1">Point to subtract from.</param>
        /// <param name="point2">Point to subtract.</param>
        ///
        /// <returns>Returns new point which coordinates equal to difference of corresponding
        /// coordinates of specified points.</returns>
        public static IntPoint Subtract(IntPoint point1, IntPoint point2)
        {
            return new IntPoint(point1.X - point2.X, point1.Y - point2.Y);
        }

        /// <summary>
        /// Addition operator - adds scalar to the specified point.
        /// </summary>
        ///
        /// <param name="point">Point to increase coordinates of.</param>
        /// <param name="valueToAdd">Value to add to coordinates of the specified point.</param>
        ///
        /// <returns>Returns new point which coordinates equal to coordinates of
        /// the specified point increased by specified value.</returns>
        public static IntPoint operator +(IntPoint point, int valueToAdd)
        {
            return new IntPoint(point.X + valueToAdd, point.Y + valueToAdd);
        }

        /// <summary>
        /// Addition operator - adds scalar to the specified point.
        /// </summary>
        ///
        /// <param name="point">Point to increase coordinates of.</param>
        /// <param name="valueToAdd">Value to add to coordinates of the specified point.</param>
        ///
        /// <returns>Returns new point which coordinates equal to coordinates of
        /// the specified point increased by specified value.</returns>
        public static IntPoint Add(IntPoint point, int valueToAdd)
        {
            return new IntPoint(point.X + valueToAdd, point.Y + valueToAdd);
        }

        /// <summary>
        /// Subtraction operator - subtracts scalar from the specified point.
        /// </summary>
        ///
        /// <param name="point">Point to decrease coordinates of.</param>
        /// <param name="valueToSubtract">Value to subtract from coordinates of the specified point.</param>
        ///
        /// <returns>Returns new point which coordinates equal to coordinates of
        /// the specified point decreased by specified value.</returns>
        public static IntPoint operator -(IntPoint point, int valueToSubtract)
        {
            return new IntPoint(point.X - valueToSubtract, point.Y - valueToSubtract);
        }

        /// <summary>
        /// Subtraction operator - subtracts scalar from the specified point.
        /// </summary>
        ///
        /// <param name="point">Point to decrease coordinates of.</param>
        /// <param name="valueToSubtract">Value to subtract from coordinates of the specified point.</param>
        ///
        /// <returns>Returns new point which coordinates equal to coordinates of
        /// the specified point decreased by specified value.</returns>
        public static IntPoint Subtract(IntPoint point, int valueToSubtract)
        {
            return new IntPoint(point.X - valueToSubtract, point.Y - valueToSubtract);
        }

        /// <summary>
        /// Multiplication operator - multiplies coordinates of the specified point by scalar value.
        /// </summary>
        ///
        /// <param name="point">Point to multiply coordinates of.</param>
        /// <param name="factor">Multiplication factor.</param>
        ///
        /// <returns>Returns new point which coordinates equal to coordinates of
        /// the specified point multiplied by specified value.</returns>
        public static IntPoint operator *(IntPoint point, int factor)
        {
            return new IntPoint(point.X * factor, point.Y * factor);
        }

        /// <summary>
        /// Multiplication operator - multiplies coordinates of the specified point by scalar value.
        /// </summary>
        ///
        /// <param name="point">Point to multiply coordinates of.</param>
        /// <param name="factor">Multiplication factor.</param>
        ///
        /// <returns>Returns new point which coordinates equal to coordinates of
        /// the specified point multiplied by specified value.</returns>
        public static IntPoint Multiply(IntPoint point, int factor)
        {
            return new IntPoint(point.X * factor, point.Y * factor);
        }

        /// <summary>
        /// Division operator - divides coordinates of the specified point by scalar value.
        /// </summary>
        ///
        /// <param name="point">Point to divide coordinates of.</param>
        /// <param name="factor">Division factor.</param>
        ///
        /// <returns>Returns new point which coordinates equal to coordinates of
        /// the specified point divided by specified value.</returns>
        public static IntPoint operator /(IntPoint point, int factor)
        {
            return new IntPoint(point.X / factor, point.Y / factor);
        }

        /// <summary>
        /// Division operator - divides coordinates of the specified point by scalar value.
        /// </summary>
        ///
        /// <param name="point">Point to divide coordinates of.</param>
        /// <param name="factor">Division factor.</param>
        ///
        /// <returns>Returns new point which coordinates equal to coordinates of
        /// the specified point divided by specified value.</returns>
        public static IntPoint Divide(IntPoint point, int factor)
        {
            return new IntPoint(point.X / factor, point.Y / factor);
        }

        /// <summary>
        /// Equality operator - checks if two points have equal coordinates.
        /// </summary>
        ///
        /// <param name="point1">First point to check.</param>
        /// <param name="point2">Second point to check.</param>
        ///
        /// <returns>Returns <see langword="true" /> if coordinates of specified
        /// points are equal.</returns>
        public static bool operator ==(IntPoint point1, IntPoint point2)
        {
            if (point1.X == point2.X)
            {
                return point1.Y == point2.Y;
            }
            return false;
        }

        /// <summary>
        /// Inequality operator - checks if two points have different coordinates.
        /// </summary>
        ///
        /// <param name="point1">First point to check.</param>
        /// <param name="point2">Second point to check.</param>
        ///
        /// <returns>Returns <see langword="true" /> if coordinates of specified
        /// points are not equal.</returns>
        public static bool operator !=(IntPoint point1, IntPoint point2)
        {
            if (point1.X == point2.X)
            {
                return point1.Y != point2.Y;
            }
            return true;
        }

        /// <summary>
        /// Check if this instance of <see cref="T:AForge.IntPoint" /> equal to the specified one.
        /// </summary>
        ///
        /// <param name="obj">Another point to check equalty to.</param>
        ///
        /// <returns>Return <see langword="true" /> if objects are equal.</returns>
        public override bool Equals(object obj)
        {
            if (!(obj is IntPoint))
            {
                return false;
            }
            return this == (IntPoint)obj;
        }

        /// <summary>
        /// Get hash code for this instance.
        /// </summary>
        ///
        /// <returns>Returns the hash code for this instance.</returns>
        public override int GetHashCode()
        {
            return X.GetHashCode() + Y.GetHashCode();
        }

        /// <summary>
        /// Implicit conversion to <see cref="T:AForge.Point" />.
        /// </summary>
        ///
        /// <param name="point">Integer point to convert to single precision point.</param>
        ///
        /// <returns>Returns new single precision point which coordinates are implicitly converted
        /// to floats from coordinates of the specified integer point.</returns>
        public static implicit operator Point(IntPoint point)
        {
            return new Point(point.X, point.Y);
        }

        ///// <summary>
        ///// Implicit conversion to <see cref="T:AForge.DoublePoint" />.
        ///// </summary>
        /////
        ///// <param name="point">Integer point to convert to double precision point.</param>
        /////
        ///// <returns>Returns new double precision point which coordinates are implicitly converted
        ///// to doubles from coordinates of the specified integer point.</returns>
        //public static implicit operator DoublePoint(IntPoint point)
        //{
        //    return new DoublePoint(point.X, point.Y);
        //}

        /// <summary>
        /// Get string representation of the class.
        /// </summary>
        ///
        /// <returns>Returns string, which contains values of the point in readable form.</returns>
        public override string ToString()
        {
            return string.Format(CultureInfo.InvariantCulture, "{0}, {1}", X, Y);
        }

        /// <summary>
        /// Calculate Euclidean norm of the vector comprised of the point's 
        /// coordinates - distance from (0, 0) in other words.
        /// </summary>
        ///
        /// <returns>Returns point's distance from (0, 0) point.</returns>
        public float EuclideanNorm()
        {
            return (float)Math.Sqrt(X * X + Y * Y);
        }
    }

    /// <summary>
    /// Core image relatad methods.
    /// </summary>
    ///
    /// <remarks>All methods of this class are static and represent general routines
    /// used by different image processing classes.</remarks>
    public static class AForgeImage
    {
        /// <summary>
        /// Check if specified 8 bpp image is grayscale.
        /// </summary>
        ///
        /// <param name="image">Image to check.</param>
        ///
        /// <returns>Returns <b>true</b> if the image is grayscale or <b>false</b> otherwise.</returns>
        ///
        /// <remarks>The methods checks if the image is a grayscale image of 256 gradients.
        /// The method first examines if the image's pixel format is
        /// <see cref="T:System.Drawing.Imaging.PixelFormat">Format8bppIndexed</see>
        /// and then it examines its palette to check if the image is grayscale or not.</remarks>
        public static bool IsGrayscale(Bitmap image)
        {
            bool result = false;
            if (image.PixelFormat == PixelFormat.Format8bppIndexed)
            {
                result = true;
                ColorPalette palette = image.Palette;
                for (int i = 0; i < 256; i++)
                {
                    Color color = palette.Entries[i];
                    if (color.R != i || color.G != i || color.B != i)
                    {
                        result = false;
                        break;
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// Create and initialize new 8 bpp grayscale image.
        /// </summary>
        ///
        /// <param name="width">Image width.</param>
        /// <param name="height">Image height.</param>
        ///
        /// <returns>Returns the created grayscale image.</returns>
        ///
        /// <remarks>The method creates new 8 bpp grayscale image and initializes its palette.
        /// Grayscale image is represented as
        /// <see cref="T:System.Drawing.Imaging.PixelFormat">Format8bppIndexed</see>
        /// image with palette initialized to 256 gradients of gray color.</remarks>
        public static Bitmap CreateGrayscaleImage(int width, int height)
        {
            Bitmap bitmap = new Bitmap(width, height, PixelFormat.Format8bppIndexed);
            SetGrayscalePalette(bitmap);
            return bitmap;
        }

        /// <summary>
        /// Set pallete of the 8 bpp indexed image to grayscale.
        /// </summary>
        ///
        /// <param name="image">Image to initialize.</param>
        ///
        /// <remarks>The method initializes palette of
        /// <see cref="T:System.Drawing.Imaging.PixelFormat">Format8bppIndexed</see>
        /// image with 256 gradients of gray color.</remarks>
        ///
        /// <exception cref="T:AForge.Imaging.UnsupportedImageFormatException">Provided image is not 8 bpp indexed image.</exception>
        public static void SetGrayscalePalette(Bitmap image)
        {
            if (image.PixelFormat != PixelFormat.Format8bppIndexed)
            {
                throw new UnsupportedImageFormatException("Source image is not 8 bpp image.");
            }
            ColorPalette palette = image.Palette;
            for (int i = 0; i < 256; i++)
            {
                palette.Entries[i] = Color.FromArgb(i, i, i);
            }
            image.Palette = palette;
        }

        /// <summary>
        /// Clone image.
        /// </summary>
        ///
        /// <param name="source">Source image.</param>
        /// <param name="format">Pixel format of result image.</param>
        ///
        /// <returns>Returns clone of the source image with specified pixel format.</returns>
        ///
        /// <remarks>The original <see cref="M:System.Drawing.Bitmap.Clone(System.Drawing.Rectangle,System.Drawing.Imaging.PixelFormat)">Bitmap.Clone()</see>
        /// does not produce the desired result - it does not create a clone with specified pixel format.
        /// More of it, the original method does not create an actual clone - it does not create a copy
        /// of the image. That is why this method was implemented to provide the functionality.</remarks> 
        public static Bitmap Clone(Bitmap source, PixelFormat format)
        {
            if (source.PixelFormat == format)
            {
                return Clone(source);
            }
            int width = source.Width;
            int height = source.Height;
            Bitmap bitmap = new Bitmap(width, height, format);
            Graphics graphics = Graphics.FromImage(bitmap);
            graphics.DrawImage(source, 0, 0, width, height);
            graphics.Dispose();
            return bitmap;
        }

        /// <summary>
        /// Clone image.
        /// </summary>
        ///
        /// <param name="source">Source image.</param>
        ///
        /// <returns>Return clone of the source image.</returns>
        ///
        /// <remarks>The original <see cref="M:System.Drawing.Bitmap.Clone(System.Drawing.Rectangle,System.Drawing.Imaging.PixelFormat)">Bitmap.Clone()</see>
        /// does not produce the desired result - it does not create an actual clone (it does not create a copy
        /// of the image). That is why this method was implemented to provide the functionality.</remarks> 
        public static Bitmap Clone(Bitmap source)
        {
            BitmapData bitmapData = source.LockBits(new Rectangle(0, 0, source.Width, source.Height), ImageLockMode.ReadOnly, source.PixelFormat);
            Bitmap bitmap = Clone(bitmapData);
            source.UnlockBits(bitmapData);
            if (source.PixelFormat == PixelFormat.Format1bppIndexed || source.PixelFormat == PixelFormat.Format4bppIndexed || source.PixelFormat == PixelFormat.Format8bppIndexed || source.PixelFormat == PixelFormat.Indexed)
            {
                ColorPalette palette = source.Palette;
                ColorPalette palette2 = bitmap.Palette;
                int num = palette.Entries.Length;
                for (int i = 0; i < num; i++)
                {
                    palette2.Entries[i] = palette.Entries[i];
                }
                bitmap.Palette = palette2;
            }
            return bitmap;
        }

        /// <summary>
        /// Clone image.
        /// </summary>
        ///
        /// <param name="sourceData">Source image data.</param>
        ///
        /// <returns>Clones image from source image data. The message does not clone pallete in the
        /// case if the source image has indexed pixel format.</returns>
        public static Bitmap Clone(BitmapData sourceData)
        {
            int width = sourceData.Width;
            int height = sourceData.Height;
            Bitmap bitmap = new Bitmap(width, height, sourceData.PixelFormat);
            BitmapData bitmapData = bitmap.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.ReadWrite, bitmap.PixelFormat);
            SystemTools.CopyUnmanagedMemory(bitmapData.Scan0, sourceData.Scan0, height * sourceData.Stride);
            bitmap.UnlockBits(bitmapData);
            return bitmap;
        }

        /// <summary>
        /// Format an image.
        /// </summary>
        ///
        /// <param name="image">Source image to format.</param>
        ///
        /// <remarks><para>Formats the image to one of the formats, which are supported
        /// by the <b>AForge.Imaging</b> library. The image is left untouched in the
        /// case if it is already of
        /// <see cref="T:System.Drawing.Imaging.PixelFormat">Format24bppRgb</see> or
        /// <see cref="T:System.Drawing.Imaging.PixelFormat">Format32bppRgb</see> or
        /// <see cref="T:System.Drawing.Imaging.PixelFormat">Format32bppArgb</see> or
        /// <see cref="T:System.Drawing.Imaging.PixelFormat">Format48bppRgb</see> or
        /// <see cref="T:System.Drawing.Imaging.PixelFormat">Format64bppArgb</see>
        /// format or it is <see cref="M:AForgeImage.IsGrayscale(System.Drawing.Bitmap)">grayscale</see>, otherwise the image
        /// is converted to <see cref="T:System.Drawing.Imaging.PixelFormat">Format24bppRgb</see>
        /// format.</para>
        ///
        /// <para><note>The method is deprecated and <see cref="M:AForgeImage.Clone(System.Drawing.Bitmap,System.Drawing.Imaging.PixelFormat)" /> method should
        /// be used instead with specifying desired pixel format.</note></para>
        /// </remarks>
        [Obsolete("Use Clone(Bitmap, PixelFormat) method instead and specify desired pixel format")]
        public static void FormatImage(ref Bitmap image)
        {
            if (image.PixelFormat != PixelFormat.Format24bppRgb && image.PixelFormat != PixelFormat.Format32bppRgb && image.PixelFormat != PixelFormat.Format32bppArgb && image.PixelFormat != PixelFormat.Format48bppRgb && image.PixelFormat != PixelFormat.Format64bppArgb && image.PixelFormat != PixelFormat.Format16bppGrayScale && !IsGrayscale(image))
            {
                Bitmap bitmap = image;
                image = Clone(bitmap, PixelFormat.Format24bppRgb);
                bitmap.Dispose();
            }
        }

        /// <summary>
        /// Load bitmap from file.
        /// </summary>
        ///
        /// <param name="fileName">File name to load bitmap from.</param>
        ///
        /// <returns>Returns loaded bitmap.</returns>
        ///
        /// <remarks><para>The method is provided as an alternative of <see cref="M:System.Drawing.Image.FromFile(System.String)" />
        /// method to solve the issues of locked file. The standard .NET's method locks the source file until
        /// image's object is disposed, so the file can not be deleted or overwritten. This method workarounds the issue and
        /// does not lock the source file.</para>
        ///
        /// <para>Sample usage:</para>
        /// <code>
        /// Bitmap image = AForgeImage.FromFile( "test.jpg" );
        /// </code>
        /// </remarks>
        public static Bitmap FromFile(string fileName)
        {
            FileStream fileStream = null;
            try
            {
                fileStream = File.OpenRead(fileName);
                MemoryStream memoryStream = new MemoryStream();
                byte[] buffer = new byte[10000];
                while (true)
                {
                    int num = fileStream.Read(buffer, 0, 10000);
                    if (num == 0)
                    {
                        break;
                    }
                    memoryStream.Write(buffer, 0, num);
                }
                return (Bitmap)System.Drawing.Image.FromStream(memoryStream);
            }
            finally
            {
                if (fileStream != null)
                {
                    fileStream.Close();
                    fileStream.Dispose();
                }
            }
        }

        /// <summary>
        /// Convert bitmap with 16 bits per plane to a bitmap with 8 bits per plane.
        /// </summary>
        ///
        /// <param name="bimap">Source image to convert.</param>
        ///
        /// <returns>Returns new image which is a copy of the source image but with 8 bits per plane.</returns>
        ///
        /// <remarks><para>The routine does the next pixel format conversions:
        /// <list type="bullet">
        /// <item><see cref="F:System.Drawing.Imaging.PixelFormat.Format16bppGrayScale">Format16bppGrayScale</see> to
        /// <see cref="F:System.Drawing.Imaging.PixelFormat.Format8bppIndexed">Format8bppIndexed</see> with grayscale palette;</item>
        /// <item><see cref="F:System.Drawing.Imaging.PixelFormat.Format48bppRgb">Format48bppRgb</see> to
        /// <see cref="F:System.Drawing.Imaging.PixelFormat.Format24bppRgb">Format24bppRgb</see>;</item>
        /// <item><see cref="F:System.Drawing.Imaging.PixelFormat.Format64bppArgb">Format64bppArgb</see> to
        /// <see cref="F:System.Drawing.Imaging.PixelFormat.Format32bppArgb">Format32bppArgb</see>;</item>
        /// <item><see cref="F:System.Drawing.Imaging.PixelFormat.Format64bppPArgb">Format64bppPArgb</see> to
        /// <see cref="F:System.Drawing.Imaging.PixelFormat.Format32bppPArgb">Format32bppPArgb</see>.</item>
        /// </list>
        /// </para></remarks>
        ///
        /// <exception cref="T:AForge.Imaging.UnsupportedImageFormatException">Invalid pixel format of the source image.</exception>
        public static unsafe Bitmap Convert16bppTo8bpp(Bitmap bimap)
        {
            int width = bimap.Width;
            int height = bimap.Height;
            Bitmap bitmap;
            int num;
            switch (bimap.PixelFormat)
            {
                case PixelFormat.Format16bppGrayScale:
                    bitmap = CreateGrayscaleImage(width, height);
                    num = 1;
                    break;
                case PixelFormat.Format48bppRgb:
                    bitmap = new Bitmap(width, height, PixelFormat.Format24bppRgb);
                    num = 3;
                    break;
                case PixelFormat.Format64bppArgb:
                    bitmap = new Bitmap(width, height, PixelFormat.Format32bppArgb);
                    num = 4;
                    break;
                case PixelFormat.Format64bppPArgb:
                    bitmap = new Bitmap(width, height, PixelFormat.Format32bppPArgb);
                    num = 4;
                    break;
                default:
                    throw new UnsupportedImageFormatException("Invalid pixel format of the source image.");
            }
            BitmapData bitmapData = bimap.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.ReadOnly, bimap.PixelFormat);
            BitmapData bitmapData2 = bitmap.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.ReadWrite, bitmap.PixelFormat);
            byte* ptr = (byte*)bitmapData.Scan0.ToPointer();
            byte* ptr2 = (byte*)bitmapData2.Scan0.ToPointer();
            int stride = bitmapData.Stride;
            int stride2 = bitmapData2.Stride;
            for (int i = 0; i < height; i++)
            {
                ushort* ptr3 = (ushort*)(ptr + i * (long)stride);
                byte* ptr4 = ptr2 + i * (long)stride2;
                int num2 = 0;
                int num3 = width * num;
                while (num2 < num3)
                {
                    *ptr4 = (byte)(*ptr3 >> 8);
                    num2++;
                    ptr3++;
                    ptr4++;
                }
            }
            bimap.UnlockBits(bitmapData);
            bitmap.UnlockBits(bitmapData2);
            return bitmap;
        }

        /// <summary>
        /// Convert bitmap with 8 bits per plane to a bitmap with 16 bits per plane.
        /// </summary>
        ///
        /// <param name="bimap">Source image to convert.</param>
        ///
        /// <returns>Returns new image which is a copy of the source image but with 16 bits per plane.</returns>
        ///
        /// <remarks><para>The routine does the next pixel format conversions:
        /// <list type="bullet">
        /// <item><see cref="F:System.Drawing.Imaging.PixelFormat.Format8bppIndexed">Format8bppIndexed</see> (grayscale palette assumed) to
        /// <see cref="F:System.Drawing.Imaging.PixelFormat.Format16bppGrayScale">Format16bppGrayScale</see>;</item>
        /// <item><see cref="F:System.Drawing.Imaging.PixelFormat.Format24bppRgb">Format24bppRgb</see> to
        /// <see cref="F:System.Drawing.Imaging.PixelFormat.Format48bppRgb">Format48bppRgb</see>;</item>
        /// <item><see cref="F:System.Drawing.Imaging.PixelFormat.Format32bppArgb">Format32bppArgb</see> to
        /// <see cref="F:System.Drawing.Imaging.PixelFormat.Format64bppArgb">Format64bppArgb</see>;</item>
        /// <item><see cref="F:System.Drawing.Imaging.PixelFormat.Format32bppPArgb">Format32bppPArgb</see> to
        /// <see cref="F:System.Drawing.Imaging.PixelFormat.Format64bppPArgb">Format64bppPArgb</see>.</item>
        /// </list>
        /// </para></remarks>
        ///
        /// <exception cref="T:AForge.Imaging.UnsupportedImageFormatException">Invalid pixel format of the source image.</exception>
        public static unsafe Bitmap Convert8bppTo16bpp(Bitmap bimap)
        {
            int width = bimap.Width;
            int height = bimap.Height;
            Bitmap bitmap;
            int num;
            switch (bimap.PixelFormat)
            {
                case PixelFormat.Format8bppIndexed:
                    bitmap = new Bitmap(width, height, PixelFormat.Format16bppGrayScale);
                    num = 1;
                    break;
                case PixelFormat.Format24bppRgb:
                    bitmap = new Bitmap(width, height, PixelFormat.Format48bppRgb);
                    num = 3;
                    break;
                case PixelFormat.Format32bppArgb:
                    bitmap = new Bitmap(width, height, PixelFormat.Format64bppArgb);
                    num = 4;
                    break;
                case PixelFormat.Format32bppPArgb:
                    bitmap = new Bitmap(width, height, PixelFormat.Format64bppPArgb);
                    num = 4;
                    break;
                default:
                    throw new UnsupportedImageFormatException("Invalid pixel format of the source image.");
            }
            BitmapData bitmapData = bimap.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.ReadOnly, bimap.PixelFormat);
            BitmapData bitmapData2 = bitmap.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.ReadWrite, bitmap.PixelFormat);
            byte* ptr = (byte*)bitmapData.Scan0.ToPointer();
            byte* ptr2 = (byte*)bitmapData2.Scan0.ToPointer();
            int stride = bitmapData.Stride;
            int stride2 = bitmapData2.Stride;
            for (int i = 0; i < height; i++)
            {
                byte* ptr3 = ptr + i * (long)stride;
                ushort* ptr4 = (ushort*)(ptr2 + i * (long)stride2);
                int num2 = 0;
                int num3 = width * num;
                while (num2 < num3)
                {
                    *ptr4 = (ushort)(*ptr3 << 8);
                    num2++;
                    ptr3++;
                    ptr4++;
                }
            }
            bimap.UnlockBits(bitmapData);
            bitmap.UnlockBits(bitmapData2);
            return bitmap;
        }
    }

    /// <summary>
    /// Set of systems tools.
    /// </summary>
    ///
    /// <remarks><para>The class is a container of different system tools, which are used
    /// across the framework. Some of these tools are platform specific, so their
    /// implementation is different on different platform, like .NET and Mono.</para>
    /// </remarks>
    public static class SystemTools
    {
        /// <summary>
        /// Copy block of unmanaged memory.
        /// </summary>
        ///
        /// <param name="dst">Destination pointer.</param>
        /// <param name="src">Source pointer.</param>
        /// <param name="count">Memory block's length to copy.</param>
        ///
        /// <returns>Return's value of <paramref name="dst" /> - pointer to destination.</returns>
        ///
        /// <remarks><para>This function is required because of the fact that .NET does
        /// not provide any way to copy unmanaged blocks, but provides only methods to
        /// copy from unmanaged memory to managed memory and vise versa.</para></remarks>
        public static unsafe IntPtr CopyUnmanagedMemory(IntPtr dst, IntPtr src, int count)
        {
            CopyUnmanagedMemory((byte*)dst.ToPointer(), (byte*)src.ToPointer(), count);
            return dst;
        }

        /// <summary>
        /// Copy block of unmanaged memory.
        /// </summary>
        ///
        /// <param name="dst">Destination pointer.</param>
        /// <param name="src">Source pointer.</param>
        /// <param name="count">Memory block's length to copy.</param>
        ///
        /// <returns>Return's value of <paramref name="dst" /> - pointer to destination.</returns>
        ///
        /// <remarks><para>This function is required because of the fact that .NET does
        /// not provide any way to copy unmanaged blocks, but provides only methods to
        /// copy from unmanaged memory to managed memory and vise versa.</para></remarks>
        public static unsafe byte* CopyUnmanagedMemory(byte* dst, byte* src, int count)
        {
            return memcpy(dst, src, count);
        }

        /// <summary>
        /// Fill memory region with specified value.
        /// </summary>
        ///
        /// <param name="dst">Destination pointer.</param>
        /// <param name="filler">Filler byte's value.</param>
        /// <param name="count">Memory block's length to fill.</param>
        ///
        /// <returns>Return's value of <paramref name="dst" /> - pointer to destination.</returns>
        public static unsafe IntPtr SetUnmanagedMemory(IntPtr dst, int filler, int count)
        {
            SetUnmanagedMemory((byte*)dst.ToPointer(), filler, count);
            return dst;
        }

        /// <summary>
        /// Fill memory region with specified value.
        /// </summary>
        ///
        /// <param name="dst">Destination pointer.</param>
        /// <param name="filler">Filler byte's value.</param>
        /// <param name="count">Memory block's length to fill.</param>
        ///
        /// <returns>Return's value of <paramref name="dst" /> - pointer to destination.</returns>
        public static unsafe byte* SetUnmanagedMemory(byte* dst, int filler, int count)
        {
            return memset(dst, filler, count);
        }

        [DllImport("ntdll.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern unsafe byte* memcpy(byte* dst, byte* src, int count);

        [DllImport("ntdll.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern unsafe byte* memset(byte* dst, int filler, int count);
    }
}
