﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace OCRTools
{
    public class StickerPackInfo
    {
        public string FolderPath { get; set; }
        public string Name { get; set; }

        public StickerPackInfo(string folderPath = "", string name = "")
        {
            FolderPath = folderPath;
            Name = name;
        }

        public override string ToString()
        {
            if (!string.IsNullOrEmpty(Name))
            {
                return Name;
            }

            if (!string.IsNullOrEmpty(FolderPath))
            {
                return Path.GetFileName(FolderPath);
            }

            return "";
        }
    }
}
