﻿using System.Collections.Generic;
using System.Drawing;

namespace OCRTools
{
    public class TaskHelpers
    {

        public static Screenshot GetScreenshot(TaskSettingsCapture taskSettings = null)
        {
            //if (taskSettings == null) taskSettings = TaskSettings.GetDefaultTaskSettings();

            Screenshot screenshot = new Screenshot()
            {
                CaptureCursor = taskSettings.ShowCursor,
                CaptureClientArea = taskSettings.CaptureClientArea,
                RemoveOutsideScreenArea = true,
                CaptureShadow = taskSettings.CaptureShadow,
                ShadowOffset = taskSettings.CaptureShadowOffset,
                AutoHideTaskbar = taskSettings.CaptureAutoHideTaskbar
            };

            return screenshot;
        }
    }

    public class TaskSettingsCapture
    {
        #region Capture / General

        public bool ShowCursor = true;
        public decimal ScreenshotDelay = 0;
        public bool CaptureTransparent = false;
        public bool CaptureShadow = true;
        public int CaptureShadowOffset = 20;
        public bool CaptureClientArea = false;
        public bool CaptureAutoHideTaskbar = false;
        public Rectangle CaptureCustomRegion = new Rectangle(0, 0, 0, 0);

        #endregion Capture / General

        #region Capture / Region capture

        public RegionCaptureOptions SurfaceOptions = new RegionCaptureOptions();

        #endregion Capture / Region capture

        #region Capture / Scrolling capture

        public ScrollingCaptureOptions ScrollingCaptureOptions = new ScrollingCaptureOptions();

        #endregion Capture / Scrolling capture

    }
    public class RegionCaptureOptions
    {
        public const int DefaultMinimumSize = 5;
        public const int MagnifierPixelCountMinimum = 3;
        public const int MagnifierPixelCountMaximum = 35;
        public const int MagnifierPixelSizeMinimum = 3;
        public const int MagnifierPixelSizeMaximum = 30;
        public const int SnapDistance = 30;
        public const int MoveSpeedMinimum = 1;
        public const int MoveSpeedMaximum = 10;

        public bool QuickCrop = true;
        public int MinimumSize = DefaultMinimumSize;
        public RegionCaptureAction RegionCaptureActionRightClick = RegionCaptureAction.RemoveShapeCancelCapture;
        public RegionCaptureAction RegionCaptureActionMiddleClick = RegionCaptureAction.SwapToolType;
        public RegionCaptureAction RegionCaptureActionX1Click = RegionCaptureAction.CaptureFullscreen;
        public RegionCaptureAction RegionCaptureActionX2Click = RegionCaptureAction.CaptureActiveMonitor;
        public bool DetectWindows = true;
        public bool DetectControls = true;
        public bool UseDimming = true;
        public bool UseCustomInfoText = false;
        public string CustomInfoText = "X: $x, Y: $y$nR: $r, G: $g, B: $b$nHex: $hex"; // Formats: $x, $y, $r, $g, $b, $hex, $HEX, $n
        public List<SnapSize> SnapSizes = new List<SnapSize>()
        {
            new SnapSize(426, 240), // 240p
            new SnapSize(640, 360), // 360p
            new SnapSize(854, 480), // 480p
            new SnapSize(1280, 720), // 720p
            new SnapSize(1920, 1080) // 1080p
        };
        public bool ShowInfo = true;
        public bool ShowMagnifier = true;
        public bool UseSquareMagnifier = false;
        public int MagnifierPixelCount = 15; // Must be odd number like 11, 13, 15 etc.
        public int MagnifierPixelSize = 10;
        public bool ShowCrosshair = false;
        public bool UseLightResizeNodes = false;
        public bool EnableAnimations = true;
        public bool IsFixedSize = false;
        public Size FixedSize = new Size(250, 250);
        public bool ShowFPS = false;
        public int MenuIconSize = 0;
        public bool MenuLocked = false;
        public bool RememberMenuState = false;
        public bool MenuCollapsed = false;
        public Point MenuPosition = Point.Empty;

        public bool SwitchToDrawingToolAfterSelection = false;
        public bool SwitchToSelectionToolAfterDrawing = false;

        // Annotation
        public AnnotationOptions AnnotationOptions = new AnnotationOptions();
        public ShapeType LastRegionTool = ShapeType.RegionRectangle;
        public ShapeType LastAnnotationTool = ShapeType.DrawingRectangle;
        public ShapeType LastEditorTool = ShapeType.DrawingRectangle;

        // Image editor
        public ImageEditorStartMode ImageEditorStartMode = ImageEditorStartMode.AutoSize;
        public WindowState ImageEditorWindowState = new WindowState();
        public bool AutoCloseEditorOnTask = false;
        public bool ShowEditorPanTip = true;
        public ImageInterpolationMode ImageEditorResizeInterpolationMode = ImageInterpolationMode.Bicubic;
        public Size EditorNewImageSize = new Size(800, 600);
        public bool EditorNewImageTransparent = false;
        public Color EditorNewImageBackgroundColor = Color.White;
        public Color EditorCanvasColor = Color.Transparent;
        public List<ImageEffectPreset> ImageEffectPresets = new List<ImageEffectPreset>();
        public int SelectedImageEffectPreset = 0;

        // Screen color picker
        public string ScreenColorPickerInfoText = "";
    }

    public enum ImageInterpolationMode
    {
        HighQualityBicubic,
        Bicubic,
        HighQualityBilinear,
        Bilinear,
        NearestNeighbor
    }

    public enum ImageEditorStartMode // Localized
    {
        AutoSize,
        Normal,
        Maximized,
        PreviousState,
        Fullscreen
    }

    public enum RegionCaptureMode
    {
        Default,
        Annotation,
        ScreenColorPicker,
        Ruler,
        OneClick,
        Editor,
        TaskEditor
    }

    public enum RegionCaptureAction // Localized
    {
        None,
        CancelCapture,
        RemoveShapeCancelCapture,
        RemoveShape,
        SwapToolType,
        CaptureFullscreen,
        CaptureActiveMonitor
    }
}
