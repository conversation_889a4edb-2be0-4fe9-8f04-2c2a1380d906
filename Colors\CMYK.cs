﻿using System.Drawing;

namespace OCRTools
{
    public struct Cmyk
    {
        private double _cyan;
        private double _magenta;
        private double _yellow;
        private double _key;
        private int _alpha;

        public double Cyan
        {
            get => _cyan;
            set => _cyan = ColorHelper.ValidColor(value);
        }

        public double Cyan100
        {
            get => _cyan * 100;
            set => _cyan = ColorHelper.ValidColor(value / 100);
        }

        public double Magenta
        {
            get => _magenta;
            set => _magenta = ColorHelper.ValidColor(value);
        }

        public double Magenta100
        {
            get => _magenta * 100;
            set => _magenta = ColorHelper.ValidColor(value / 100);
        }

        public double Yellow
        {
            get => _yellow;
            set => _yellow = ColorHelper.ValidColor(value);
        }

        public double Yellow100
        {
            get => _yellow * 100;
            set => _yellow = ColorHelper.ValidColor(value / 100);
        }

        public double Key
        {
            get => _key;
            set => _key = ColorHelper.ValidColor(value);
        }

        public double Key100
        {
            get => _key * 100;
            set => _key = ColorHelper.ValidColor(value / 100);
        }

        public int Alpha
        {
            get => _alpha;
            set => _alpha = ColorHelper.ValidColor(value);
        }

        public Cmyk(double cyan, double magenta, double yellow, double key, int alpha = 255) : this()
        {
            Cyan = cyan;
            Magenta = magenta;
            Yellow = yellow;
            Key = key;
            Alpha = alpha;
        }

        public Cmyk(int cyan, int magenta, int yellow, int key, int alpha = 255) : this()
        {
            Cyan100 = cyan;
            Magenta100 = magenta;
            Yellow100 = yellow;
            Key100 = key;
            Alpha = alpha;
        }

        public static implicit operator Cmyk(Color color)
        {
            return ColorHelper.ColorToCmyk(color);
        }

        public static implicit operator Color(Cmyk color)
        {
            return color.ToColor();
        }

        public static implicit operator RGBA(Cmyk color)
        {
            return color.ToColor();
        }

        public static implicit operator HSB(Cmyk color)
        {
            return color.ToColor();
        }

        public static bool operator ==(Cmyk left, Cmyk right)
        {
            return left.Cyan == right.Cyan && left.Magenta == right.Magenta && left.Yellow == right.Yellow &&
                   left.Key == right.Key;
        }

        public static bool operator !=(Cmyk left, Cmyk right)
        {
            return !(left == right);
        }

        public override string ToString()
        {
            return string.Format("青色: {0:0.0}%，品红: {1:0.0}%，黄色: {2:0.0}%, 键: {3:0.0}%", Cyan100, Magenta100, Yellow100,
                Key100);
        }

        public Color ToColor()
        {
            return ColorHelper.CmykToColor(this);
        }
    }
}