﻿using System.Drawing;
using System.Drawing.Drawing2D;

namespace OCRTools
{
    public class ColorBox : ColorUserControl
    {
        public ColorBox()
        {
            Initialize();
        }

        protected sealed override void Initialize()
        {
            Name = "ColorBox";
            ClientSize = new Size(256, 256);
            base.Initialize();
        }

        protected override void DrawCrosshair(Graphics g)
        {
            DrawCrosshair(g, Pens.Black, 6);
            DrawCrosshair(g, Pens.White, 5);
        }

        private void DrawCrosshair(Graphics g, Pen pen, int size)
        {
            g.Draw<PERSON>llipse(pen,
                new Rectangle(new Point(lastPos.X - size, lastPos.Y - size), new Size(size * 2, size * 2)));
        }

        // X = Saturation 0 -> 100
        // Y = Brightness 100 -> 0
        protected override void DrawHue()
        {
            using (var g = Graphics.FromImage(bmp))
            {
                var start = new HSB(SelectedColor.HSB.Hue, 0.0, 0.0, SelectedColor.RGBA.Alpha);
                var end = new HSB(SelectedColor.HSB.Hue, 1.0, 0.0, SelectedColor.RGBA.Alpha);

                for (var y = 0; y < clientHeight; y++)
                {
                    start.Brightness = end.Brightness = 1.0 - (double)y / (clientHeight - 1);

                    using (var brush = new LinearGradientBrush(new Rectangle(0, 0, clientWidth, 1), start, end,
                        LinearGradientMode.Horizontal))
                    {
                        g.FillRectangle(brush, new Rectangle(0, y, clientWidth, 1));
                    }
                }
            }
        }

        // X = Hue 0 -> 360
        // Y = Brightness 100 -> 0
        protected override void DrawSaturation()
        {
            using (var g = Graphics.FromImage(bmp))
            {
                var start = new HSB(0.0, SelectedColor.HSB.Saturation, 1.0, SelectedColor.RGBA.Alpha);
                var end = new HSB(0.0, SelectedColor.HSB.Saturation, 0.0, SelectedColor.RGBA.Alpha);

                for (var x = 0; x < clientWidth; x++)
                {
                    start.Hue = end.Hue = (double)x / (clientHeight - 1);

                    using (var brush = new LinearGradientBrush(new Rectangle(0, 0, 1, clientHeight), start, end,
                        LinearGradientMode.Vertical))
                    {
                        g.FillRectangle(brush, new Rectangle(x, 0, 1, clientHeight));
                    }
                }
            }
        }

        // X = Hue 0 -> 360
        // Y = Saturation 100 -> 0
        protected override void DrawBrightness()
        {
            using (var g = Graphics.FromImage(bmp))
            {
                var start = new HSB(0.0, 1.0, SelectedColor.HSB.Brightness, SelectedColor.RGBA.Alpha);
                var end = new HSB(0.0, 0.0, SelectedColor.HSB.Brightness, SelectedColor.RGBA.Alpha);

                for (var x = 0; x < clientWidth; x++)
                {
                    start.Hue = end.Hue = (double)x / (clientHeight - 1);

                    using (var brush = new LinearGradientBrush(new Rectangle(0, 0, 1, clientHeight), start, end,
                        LinearGradientMode.Vertical))
                    {
                        g.FillRectangle(brush, new Rectangle(x, 0, 1, clientHeight));
                    }
                }
            }
        }

        // X = 蓝色 0 -> 255
        // Y = 绿色 255 -> 0
        protected override void DrawRed()
        {
            using (var g = Graphics.FromImage(bmp))
            {
                var start = new RGBA(SelectedColor.RGBA.Red, 0, 0, SelectedColor.RGBA.Alpha);
                var end = new RGBA(SelectedColor.RGBA.Red, 0, 255, SelectedColor.RGBA.Alpha);

                for (var y = 0; y < clientHeight; y++)
                {
                    start.Green = end.Green = Round(255 - 255 * (double)y / (clientHeight - 1));

                    using (var brush = new LinearGradientBrush(new Rectangle(0, 0, clientWidth, 1), start, end,
                        LinearGradientMode.Horizontal))
                    {
                        g.FillRectangle(brush, new Rectangle(0, y, clientWidth, 1));
                    }
                }
            }
        }

        // X = 蓝色 0 -> 255
        // Y = 红色 255 -> 0
        protected override void DrawGreen()
        {
            using (var g = Graphics.FromImage(bmp))
            {
                var start = new RGBA(0, SelectedColor.RGBA.Green, 0, SelectedColor.RGBA.Alpha);
                var end = new RGBA(0, SelectedColor.RGBA.Green, 255, SelectedColor.RGBA.Alpha);

                for (var y = 0; y < clientHeight; y++)
                {
                    start.Red = end.Red = Round(255 - 255 * (double)y / (clientHeight - 1));

                    using (var brush = new LinearGradientBrush(new Rectangle(0, 0, clientWidth, 1), start, end,
                        LinearGradientMode.Horizontal))
                    {
                        g.FillRectangle(brush, new Rectangle(0, y, clientWidth, 1));
                    }
                }
            }
        }

        // X = 红色 0 -> 255
        // Y = 绿色 255 -> 0
        protected override void DrawBlue()
        {
            using (var g = Graphics.FromImage(bmp))
            {
                var start = new RGBA(0, 0, SelectedColor.RGBA.Blue, SelectedColor.RGBA.Alpha);
                var end = new RGBA(255, 0, SelectedColor.RGBA.Blue, SelectedColor.RGBA.Alpha);

                for (var y = 0; y < clientHeight; y++)
                {
                    start.Green = end.Green = Round(255 - 255 * (double)y / (clientHeight - 1));

                    using (var brush = new LinearGradientBrush(new Rectangle(0, 0, clientWidth, 1), start, end,
                        LinearGradientMode.Horizontal))
                    {
                        g.FillRectangle(brush, new Rectangle(0, y, clientWidth, 1));
                    }
                }
            }
        }
    }
}