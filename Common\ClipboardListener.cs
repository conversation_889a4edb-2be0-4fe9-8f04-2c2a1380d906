﻿using System;
using System.Collections.Concurrent;
using System.ComponentModel;
using System.Diagnostics;

namespace OCRTools.Common
{
    public interface IClipboardListener : IDisposable
    {
        void Start(bool isExpSelf = true);

        void Stop();
    }
    public sealed class ClipboardListener : IClipboardListener, IDisposable
    {
        private bool isDisposed;

        private readonly IClipboardNative clipboardNative;

        public BlockingCollection<ClipboardText> ClipboardTexts = new BlockingCollection<ClipboardText>();

        public ClipboardListener(IClipboardNative clipboardNative)
        {
            this.clipboardNative = (clipboardNative ?? throw new ArgumentNullException("clipboardNative"));
            this.clipboardNative.ClipboardChanged += OnClipboardChanged;
        }

        public void Dispose()
        {
            if (!isDisposed)
            {
                isDisposed = true;
                ClipboardTexts.Dispose();
                clipboardNative.ClipboardChanged -= OnClipboardChanged;
                clipboardNative.StopListening();
            }
        }

        private void OnClipboardChanged(object sender, ClipboardChangedEventArgs e)
        {
            var processInfo = new ProcessInfo(e.ClipboardText.SourceProcessId);
            ClipboardText clipboardText = e.ClipboardText.WithAdminMode(processInfo.IsAdmin);
            if (clipboardText != null && !string.IsNullOrEmpty(clipboardText.Text))
            {
                ClipboardTexts.Add(clipboardText);
            }
        }

        public void Start(bool isExpSelf = true)
        {
            clipboardNative.StartListening(isExpSelf);
        }

        public void Stop()
        {
            clipboardNative.StopListening();
        }

    }

    public class ProcessInfo : IEquatable<ProcessInfo>
    {
        private const int hashingPrime = 23;

        private const string svcHostProcessExeName = "svchost.exe";

        public string DisplayName
        {
            get;
            private set;
        }

        public string ExeName
        {
            get;
            private set;
        }

        public string FileName
        {
            get;
            private set;
        }

        public bool IsCurrentProcess
        {
            get;
            private set;
        }

        public bool IsSvcHostedProcess
        {
            get;
            private set;
        }

        public string ProcessName
        {
            get;
            private set;
        }

        public byte[] Icon
        {
            get;
            set;
        }

        public bool IsAdmin
        {
            get;
            private set;
        }

        public ProcessInfo(int processId)
        {
            if (processId >= 0)
            {
                try
                {
                    using (var process = Process.GetProcessById(processId))
                    {
                        string processName = process.ProcessName;
                        string fileName = null;
                        string displayName = null;
                        try
                        {
                            var mainModule = process.MainModule;
                            if (mainModule != null)
                            {
                                fileName = mainModule.FileName;
                                displayName = mainModule.FileVersionInfo.FileDescription;
                            }
                        }
                        catch (Win32Exception)
                        {
                            IsAdmin = true;
                        }
                        SetInitialData(processName, fileName, displayName);
                    }
                    return;
                }
                catch
                {
                }
            }
            SetInitialData(string.Empty, string.Empty, string.Empty, string.Empty);
        }

        public ProcessInfo(string processName, string fileName, string displayName)
        {
            SetInitialData(processName, fileName, displayName);
        }

        public ProcessInfo(string processName, string exeName, string fileName, string displayName)
        {
            SetInitialData(processName, exeName, fileName, displayName);
        }

        public bool Equals(ProcessInfo other)
        {
            if (ProcessName == other?.ProcessName)
            {
                return DisplayName == other?.DisplayName;
            }
            return false;
        }

        public override bool Equals(object obj)
        {
            return Equals(obj as ProcessInfo);
        }

        public override int GetHashCode()
        {
            int hashCode = string.Empty.GetHashCode();
            hashCode = ((ProcessName != null) ? (hashCode * 23 + ProcessName.GetHashCode()) : hashCode);
            return (DisplayName != null) ? (hashCode * 23 + DisplayName.GetHashCode()) : hashCode;
        }

        private void SetInitialData(string processName, string fileName, string displayName)
        {
            ProcessName = (processName ?? string.Empty);
            DisplayName = ((!string.IsNullOrEmpty(displayName)) ? displayName : ProcessName);
            FileName = (fileName ?? string.Empty);
            ExeName = ((FileName != null) ? System.IO.Path.GetFileName(FileName).ToLower() : string.Empty);
            IsSvcHostedProcess = (FileName != null && FileName.ToLower().Contains("svchost.exe"));
        }

        private void SetInitialData(string processName, string exeName, string fileName, string displayName)
        {
            ProcessName = (processName ?? string.Empty);
            DisplayName = ((!string.IsNullOrEmpty(displayName)) ? displayName : ProcessName);
            FileName = (fileName ?? string.Empty);
            ExeName = (exeName ?? string.Empty);
            IsSvcHostedProcess = (FileName != null && FileName.ToLower().Contains("svchost.exe"));
        }

        public static bool operator ==(ProcessInfo obj1, ProcessInfo obj2)
        {
            return object.Equals(obj1, obj2);
        }

        public static bool operator !=(ProcessInfo obj1, ProcessInfo obj2)
        {
            return !(obj1 == obj2);
        }
    }

}
