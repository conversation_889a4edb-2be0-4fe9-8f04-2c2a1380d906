﻿using System;
using System.Runtime.InteropServices;

namespace OCRTools.Common
{
    public class ClipboardMethods
	{
		[DllImport("user32.dll", SetLastError = true)]
		[return: Marshal<PERSON>(UnmanagedType.Bool)]
		internal static extern bool AddClipboardFormatListener(IntPtr hwnd);

		[DllImport("user32.dll")]
		internal static extern IntPtr GetClipboardOwner();

		[DllImport("user32.dll")]
		internal static extern int GetClipboardSequenceNumber();

		[DllImport("user32.dll", SetLastError = true)]
		internal static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint processId);

		[DllImport("user32.dll", SetLastError = true)]
		[return: Marshal<PERSON>(UnmanagedType.Bool)]
		internal static extern bool RemoveClipboardFormatListener(IntPtr hwnd);
	}
}
