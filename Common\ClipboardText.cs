﻿using System;
using System.Linq;

namespace OCRTools.Common
{
    public class ClipboardText
	{
		public static readonly ClipboardText Empty = new ClipboardText(-1, IntPtr.Zero, -1, string.Empty, null);

		public IntPtr ActiveWindowHandle
		{
			get;
		}

		public string Formats
		{
			get;
		}

		public int SequenceNumber
		{
			get;
			private set;
		}

		public int SourceProcessId
		{
			get;
		}

		public string Text
		{
			get;
		}

		public bool IsAdminProcess
		{
			get;
			private set;
		}

		public ClipboardText(int sourceProcessId, IntPtr activeWindowHandle, int sequenceNumber, string text, string[] formats, bool isAdminProcess = false)
		{
			Text = (text ?? string.Empty);
			SourceProcessId = sourceProcessId;
			ActiveWindowHandle = activeWindowHandle;
			Formats = ((formats != null) ? string.Join(";", Enumerable.OrderBy(formats, (string format) => format)) : string.Empty);
			SequenceNumber = sequenceNumber;
			IsAdminProcess = isAdminProcess;
		}

		public ClipboardText WithAdminMode(bool value)
		{
			if (value == IsAdminProcess)
			{
				return this;
			}
			ClipboardText clipboardText = (ClipboardText)MemberwiseClone();
			clipboardText.IsAdminProcess = value;
			return clipboardText;
		}

		public ClipboardText WithSequenceNumber(int value)
		{
			ClipboardText clipboardText = (ClipboardText)MemberwiseClone();
			clipboardText.SequenceNumber = value;
			return clipboardText;
		}

		public bool IsRepeatOf(ClipboardText clipboardText)
		{
			if (clipboardText != null && SourceProcessId == clipboardText.SourceProcessId && ActiveWindowHandle == clipboardText.ActiveWindowHandle && Text == clipboardText.Text && Formats == clipboardText.Formats && SequenceNumber > clipboardText.SequenceNumber)
			{
				return IsAdminProcess == clipboardText.IsAdminProcess;
			}
			return false;
		}
	}

}
