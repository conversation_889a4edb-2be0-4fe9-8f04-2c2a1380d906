using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;

namespace OCRTools
{
    internal static class ColorExtensions
    {
        public static bool IsEnoughContrast(this Color color1, Color color2)
        {
            Console.WriteLine(color1.GetBrightness() + ": " + color1.GetBrightness2() + " - " + color2.GetBrightness2() + " = " + Math.Abs(color1.GetBrightness2() - color2.GetBrightness2()));
            return Math.Abs(color1.GetBrightness2() - color2.GetBrightness2()) > 125.0;
        }

        public static int GetBrightness4(this Color color)
        {
            return color.R + 2 * color.G + color.B;
        }

        public static float GetBrightness3(this Color color)
        {
            float num = color.R / 255f;
            float num2 = color.G / 255f;
            float num3 = color.B / 255f;
            float num4 = num;
            float num5 = num;
            if (num2 > num4)
            {
                num4 = num2;
            }
            if (num3 > num4)
            {
                num4 = num3;
            }
            if (num2 < num5)
            {
                num5 = num2;
            }
            if (num3 < num5)
            {
                num5 = num3;
            }
            return (num4 + num5) / 2f;
        }

        public static double GetBrightness2(this Color c)
        {
            return 0.2126 * c.R + 0.7152 * c.G + 0.0722 * c.B;
        }

        public static int GetBrightness1(this Color c)
        {
            return (int)Math.Sqrt(c.R * c.R * 0.241 + c.G * c.G * 0.691 + c.B * c.B * 0.068);
        }

        public static int GetBrightness(this Color c)
        {
            return 2 * c.R + 5 * c.G + c.B;
        }

        public static float GetHue(this Color color)
        {
            if (color.R == color.G && color.G == color.B)
            {
                return 0f;
            }
            float num = color.R / 255f;
            float num2 = color.G / 255f;
            float num3 = color.B / 255f;
            float num4 = 0f;
            float num5 = num;
            float num6 = num;
            if (num2 > num5)
            {
                num5 = num2;
            }
            if (num3 > num5)
            {
                num5 = num3;
            }
            if (num2 < num6)
            {
                num6 = num2;
            }
            if (num3 < num6)
            {
                num6 = num3;
            }
            float num7 = num5 - num6;
            if (num == num5)
            {
                num4 = (num2 - num3) / num7;
            }
            else if (num2 == num5)
            {
                num4 = 2f + (num3 - num) / num7;
            }
            else if (num3 == num5)
            {
                num4 = 4f + (num - num2) / num7;
            }
            num4 *= 60f;
            if (num4 < 0f)
            {
                num4 += 360f;
            }
            return num4;
        }

        public static float GetSaturation(this Color color)
        {
            float num = color.R / 255f;
            float num2 = color.G / 255f;
            float num3 = color.B / 255f;
            float result = 0f;
            float num4 = num;
            float num5 = num;
            if (num2 > num4)
            {
                num4 = num2;
            }
            if (num3 > num4)
            {
                num4 = num3;
            }
            if (num2 < num5)
            {
                num5 = num2;
            }
            if (num3 < num5)
            {
                num5 = num3;
            }
            if (num4 == num5)
            {
                return result;
            }
            float num6 = (num4 + num5) / 2f;
            if (num6 <= 0.5)
            {
                return (num4 - num5) / (num4 + num5);
            }
            return (num4 - num5) / (2f - num4 - num5);
        }

        public static int ClosestColorHue(List<Color> colors, Color target)
        {
            float hue = target.GetHue();
            IEnumerable<float> source = from n in colors
                                        select GetHueDistance(n.GetHue(), hue);
            float diffMin = source.Min((float n) => n);
            return source.ToList().FindIndex((float n) => n == diffMin);
        }

        public static int ClosestColorRgb(List<Color> colors, Color target)
        {
            int distance = int.MaxValue;
            int indexOfMin = -1;
            Parallel.For(0, colors.Count, delegate (int i, ParallelLoopState x)
            {
                int num = ColorDiff(colors[i], target);
                if (num < distance)
                {
                    distance = num;
                    indexOfMin = i;
                }
                if (distance == 0)
                {
                    x.Break();
                }
            });
            return indexOfMin;
        }

        public static int IndexOfMin<T>(this IEnumerable<T> list) where T : IComparable
        {
            IEnumerator<T> enumerator = list.GetEnumerator();
            enumerator.MoveNext();
            T current = enumerator.Current;
            int result = 0;
            int num = 1;
            while (enumerator.MoveNext())
            {
                if (enumerator.Current.CompareTo(current) < 0)
                {
                    current = enumerator.Current;
                    result = num;
                }
                num++;
            }
            return result;
        }

        public static int ClosestColorHsb(List<Color> colors, Color target)
        {
            float hue = target.GetHue();
            float num = ColorNum(target);
            IEnumerable<float> source = from n in colors
                                        select Math.Abs(ColorNum(n) - num) + GetHueDistance(n.GetHue(), hue);
            float diffMin = source.Min((float x) => x);
            return source.ToList().FindIndex((float n) => n == diffMin);
        }

        public static float GetLuminance(Color c)
        {
            return (c.R * 0.299f + c.G * 0.587f + c.B * 0.114f) / 256f;
        }

        public static float GetHueDistance(float hue1, float hue2)
        {
            float num = Math.Abs(hue1 - hue2);
            return (num > 180f) ? (360f - num) : num;
        }

        public static float ColorNum(Color c)
        {
            int num = 3;
            int num2 = 3;
            return c.GetSaturation() * num + c.GetBrightness() * num2;
        }

        public static int ColorDiff(Color first, Color second)
        {
            return (int)Math.Sqrt((first.R - second.R) * (first.R - second.R) + (first.G - second.G) * (first.G - second.G) + (first.B - second.B) * (first.B - second.B));
        }

        public static Color ConvertHsvToRgb(double h, double s, double v, double alpha)
        {
            double num;
            double num2;
            double num3;
            if (s == 0.0)
            {
                num = v;
                num2 = v;
                num3 = v;
            }
            else
            {
                h = ((h != 360.0) ? (h / 60.0) : 0.0);
                int num4 = (int)Math.Truncate(h);
                double num5 = h - num4;
                double num6 = v * (1.0 - s);
                double num7 = v * (1.0 - s * num5);
                double num8 = v * (1.0 - s * (1.0 - num5));
                switch (num4)
                {
                    case 0:
                        num = v;
                        num2 = num8;
                        num3 = num6;
                        break;
                    case 1:
                        num = num7;
                        num2 = v;
                        num3 = num6;
                        break;
                    case 2:
                        num = num6;
                        num2 = v;
                        num3 = num8;
                        break;
                    case 3:
                        num = num6;
                        num2 = num7;
                        num3 = v;
                        break;
                    case 4:
                        num = num8;
                        num2 = num6;
                        num3 = v;
                        break;
                    default:
                        num = v;
                        num2 = num6;
                        num3 = num7;
                        break;
                }
            }
            return Color.FromArgb((byte)alpha, (byte)(num * 255.0), (byte)(num2 * 255.0), (byte)(num3 * 255.0));
        }

        public static List<Color> GenerateHsvSpectrum()
        {
            List<Color> list = new List<Color>(8);
            for (int i = 0; i < 29; i++)
            {
                list.Add(ConvertHsvToRgb(i * 12, 1.0, 1.0, 255.0));
            }
            list.Add(ConvertHsvToRgb(0.0, 1.0, 1.0, 255.0));
            return list;
        }
    }
}
