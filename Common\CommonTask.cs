using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace OCRTools.Common
{
    internal class CommonTask<T>
    {
        public static T GetFastestValidResult(List<TaskParam> lstPram, Func<TaskParam, T> function, int maxConcurrentTasks, int maxTimeout, Func<T, bool> isValid)
        {
            if (lstPram == null || lstPram.Count == 0)
                return default;

            if (lstPram.Count == 1)
            {
                var singleResult = function(lstPram[0]);
                return IsValidResult(singleResult, isValid) ? singleResult : default;
            }

            try
            {
                using (var semaphore = new SemaphoreSlim(maxConcurrentTasks))
                {
                    var tcs = new TaskCompletionSource<T>();
                    var completedCount = 0;
                    var totalTasks = lstPram.Count;
                    var isSemaphoreDisposed = false;

                    foreach (var param in lstPram)
                    {
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                if (isSemaphoreDisposed)
                                {
                                    return;
                                }

                                await semaphore.WaitAsync();

                                if (isSemaphoreDisposed)
                                {
                                    return;
                                }

                                var result = function(param);

                                if (isSemaphoreDisposed)
                                {
                                    return;
                                }

                                if (IsValidResult(result, isValid))
                                {
                                    //Console.WriteLine(string.Format("{0}:{1}", ServerTime.DateTime.ToString("HH:mm:ss fff"), CommonString.JavaScriptSerializer.Serialize(result)));
                                    tcs.TrySetResult(result);
                                }
                                else
                                {
                                    // 无效结果，检查是否所有任务都完成
                                    if (Interlocked.Increment(ref completedCount) == totalTasks)
                                    {
                                        tcs.TrySetResult(default);
                                    }
                                }
                            }
                            catch (OperationCanceledException)
                            {
                                // 取消操作是正常的，不需要处理
                            }
                            catch
                            {
                                // 任务执行失败，检查是否所有任务都完成
                                if (Interlocked.Increment(ref completedCount) == totalTasks)
                                {
                                    tcs.TrySetResult(default);
                                }
                            }
                            finally
                            {
                                if (!isSemaphoreDisposed)
                                {
                                    semaphore.Release();
                                }
                            }
                        });
                    }

                    try
                    {
                        return tcs.Task.Result;
                    }
                    finally
                    {
                        isSemaphoreDisposed = true;
                    }
                }
            }
            catch
            {
                return default;
            }
        }

        /// <summary>
        /// 检查结果是否有效
        /// </summary>
        private static bool IsValidResult<TResult>(TResult result, Func<TResult, bool> isValid)
        {
            if (result == null)
                return false;

            return isValid == null || isValid(result);
        }
    }

    public class TaskParam
    {
        public string Param1 { get; set; }

        public object Param2 { get; set; }

        public object Param3 { get; set; }
    }
}
