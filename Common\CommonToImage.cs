﻿using mshtml;
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;

namespace OCRTools
{
    public class CommonToImage
    {
        private static readonly int ImgWidth = Screen.PrimaryScreen.WorkingArea.Width / 2 * 1;
        private static readonly int ImgHeight = Screen.PrimaryScreen.WorkingArea.Height / 3 * 1;

        private static RichTextBoxPrintCtrl _richTextBox;
        private static WebBrowser _browser;

        private static bool _isOnHtmlDraw;
        private static bool _isOnRtfDraw;

        public static Bitmap ByRtf(string strRtf, Size forceSize)
        {
            Bitmap img = null;
            if (string.IsNullOrEmpty(strRtf)) return null;
            try
            {
                if (_isOnRtfDraw) return null;
                _isOnRtfDraw = true;
                if (_richTextBox == null)
                {
                    _richTextBox = new RichTextBoxPrintCtrl
                    {
                        ScrollBars = RichTextBoxScrollBars.None,
                        BackColor = CommonSetting.贴图背景颜色,
                        ForeColor = CommonSetting.贴图文字颜色,
                        Font = CommonSetting.Get贴图文本字体(),
                        WordWrap = false
                    };
                    _richTextBox.ContentsResized += RichTextBox_ContentsResized;
                }

                //RichTextBox.Refresh();

                if (!forceSize.IsEmpty)
                {
                    _richTextBox.Tag = "";
                    _richTextBox.Rtf = strRtf;
                    _richTextBox.Size = forceSize;
                }
                else
                {
                    _richTextBox.Tag = null;
                    _richTextBox.Rtf = strRtf;
                }

                img = new Bitmap(_richTextBox.Width, _richTextBox.Height, PixelFormat.Format32bppArgb);
                _richTextBox.DrawToBitmap(img, new Rectangle(new Point(0, 0), _richTextBox.Size));
            }
            catch (Exception oe)
            {
                Log.WriteError("ByRtf", oe);
            }
            finally
            {
                _isOnRtfDraw = false;
            }

            //img = richTextBox.DrawToBitmap1();
            return img;
        }

        private static void RichTextBox_ContentsResized(object sender, ContentsResizedEventArgs e)
        {
            var richTextBox = (RichTextBox)sender;
            if (richTextBox.Tag == null)
            {
                richTextBox.Width = e.NewRectangle.Width;
                richTextBox.Height = e.NewRectangle.Height;
            }

            richTextBox.Width += richTextBox.Margin.Horizontal +
                                 SystemInformation.HorizontalResizeBorderThickness +
                                 SystemInformation.HorizontalScrollBarThumbWidth;

            richTextBox.Height += richTextBox.Margin.Vertical +
                                  SystemInformation.VerticalResizeBorderThickness;
        }

        public static Bitmap ByHtml_WebBroswer(string html, Size forceSize)
        {
            Bitmap img = null;
            if (string.IsNullOrEmpty(html)) return null;
            try
            {
                if (_isOnHtmlDraw) return null;
                _isOnHtmlDraw = true;
                var finalSize = new Size(forceSize.IsEmpty ? ImgWidth : forceSize.Width,
                    forceSize.IsEmpty ? ImgHeight : forceSize.Height)
                {
                    Width = (int)Math.Max(forceSize.Width, CommonSetting.贴图图片最大宽度)
                };
                if (_browser == null)
                    _browser = new WebBrowser
                    {
                        ScrollBarsEnabled = false,
                        ScriptErrorsSuppressed = true,
                        Dock = DockStyle.Fill,
                        Size = finalSize
                    };
                _browser.Navigate("about:blank");
                _browser.Document?.OpenNew(false);
                _browser.Document?.Write(html);
                //Browser.Document.Body.Style = "overflow:hidden";
                //Browser.Refresh();
                if (forceSize.IsEmpty)
                    try
                    {
                        ResizeBroswer(_browser);
                        finalSize = _browser.Size;
                    }
                    catch
                    {
                        for (var i = 0; i < 10; i++)
                        {
                            //Browser.Refresh();
                            finalSize = _browser.Document?.Body?.ScrollRectangle.Size ??
                                        _browser.Document?.Window?.Size ?? _browser.GetPreferredSize(_browser.Size);
                            if (Equals(_browser.Size, finalSize)) break;
                            _browser.Size = new Size(finalSize.Width, finalSize.Height);
                            Thread.Sleep(50);
                        }

                        AdjWidth(_browser);
                        finalSize = _browser.Document?.Body?.ScrollRectangle.Size ?? _browser.Document?.Window?.Size ??
                            _browser.GetPreferredSize(_browser.Size);
                    }
                else
                    _browser.Size = finalSize;

                img = new Bitmap(finalSize.Width, finalSize.Height, PixelFormat.Format32bppArgb);
                _browser.DrawToBitmap(img, new Rectangle(new Point(0, 0), finalSize));
            }
            catch (Exception oe)
            {
                Log.WriteError("ByHtml_WebBroswer", oe);
            }
            finally
            {
                _isOnHtmlDraw = false;
            }

            return img;
        }

        protected static void ResizeBroswer(WebBrowser wb)
        {
            var docs2 = (IHTMLDocument2)wb.Document.DomDocument;
            var docs3 = (IHTMLDocument3)wb.Document.DomDocument;
            var body2 = (IHTMLElement2)docs2.body;
            var root2 = (IHTMLElement2)docs3.documentElement;

            var width = Math.Max(body2.scrollWidth, root2.scrollWidth);
            var height = Math.Max(root2.scrollHeight, body2.scrollHeight);

            // Resize the control to the exact size to display the page. Also, make sure scroll bars are disabled
            wb.Width = width;
            wb.Height = height;
        }

        private static void AdjWidth(WebBrowser browser)
        {
            var oldSize = browser.Size;
            while (browser.Width > 300)
            {
                browser.Width -= 50;
                //Browser.Refresh();
                var finalSize = _browser.Document?.Body?.ScrollRectangle.Size ??
                                _browser.Document?.Window?.Size ?? _browser.GetPreferredSize(_browser.Size);
                if (Equals(_browser.Size.Width, finalSize.Width) && Math.Abs(browser.Height - finalSize.Height) <= 5)
                {
                    oldSize = finalSize;
                    browser.Size = finalSize;
                }
                else
                {
                    browser.Size = oldSize;
                    //Browser.Refresh();
                    break;
                }

                Thread.Sleep(50);
            }
        }

        public static Bitmap AutoCrop(Bitmap bmp, int borderWidth = 5)
        {
            if (Image.GetPixelFormatSize(bmp.PixelFormat) != 32)
                throw new InvalidOperationException("Autocrop currently only supports 32 bits per pixel images.");

            var cropColor = bmp.GetPixel(2, 2);

            var bottom = 0;
            var
                left = bmp.Width; // Set the left crop point to the width so that the logic below will set the left value to the first non crop color pixel it comes across.
            var right = 0;
            var
                top = bmp.Height; // Set the top crop point to the height so that the logic below will set the top value to the first non crop color pixel it comes across.

            left = _sdfdsfsdfsd(bmp, cropColor, left, ref top, ref right, ref bottom);

            if (left < right && top < bottom)
            {
                var srcRect = new Rectangle(Math.Max(left - borderWidth, 0), Math.Max(top - borderWidth, 0),
                    right - left + borderWidth * 2, bottom - top + borderWidth * 2);
                var destRect = new Rectangle(borderWidth, borderWidth, srcRect.Width, srcRect.Height);

                var bm = new Bitmap(srcRect.Width + borderWidth * 2, srcRect.Height + borderWidth * 2);
                using (var gr = Graphics.FromImage(bm))
                {
                    gr.Clear(CommonSetting.贴图背景颜色);
                    gr.DrawImage(bmp, destRect, srcRect, GraphicsUnit.Pixel);
                }

                bmp = bm;
            }

            return bmp; // Entire image should be cropped, so just return null
        }

        [Obfuscation]
        private static unsafe int _sdfdsfsdfsd(Bitmap bmp, Color cropColor, int left, ref int top, ref int right,
            ref int bottom)
        {
            var bmpData = bmp.LockBits(new Rectangle(0, 0, bmp.Width, bmp.Height), ImageLockMode.ReadOnly,
                bmp.PixelFormat);

            var dataPtr = (byte*)bmpData.Scan0;

            for (var y = 0; y < bmp.Height; y++)
            {
                for (var x = 0; x < bmp.Width; x++)
                {
                    var rgbPtr = dataPtr + x * 4;

                    var a = rgbPtr[3];

                    if (a == 0 || a != cropColor.A) continue;

                    var b = rgbPtr[0];
                    var g = rgbPtr[1];
                    var r = rgbPtr[2];

                    // If any of the pixel RGBA values don't match and the crop color is not transparent
                    //or if the crop color is transparent and the pixel A value is not transparent
                    if (b != cropColor.B || g != cropColor.G || r != cropColor.R)
                    // || (cropColor.A == 0 && a != 0))
                    {
                        left = left == -1 ? x : Math.Min(x, left);
                        top = top == -1 ? y : Math.Min(y, top);

                        right = right == -1 ? x + 1 : Math.Max(x + 1, right);
                        bottom = bottom == -1 ? y + 1 : Math.Max(y + 1, bottom);
                    }
                }

                dataPtr += bmpData.Stride;
            }

            left = Math.Max(0, left);
            top = Math.Max(0, top);
            right = Math.Min(bmp.Width, right);
            bottom = Math.Min(bmp.Height, bottom);

            bmp.UnlockBits(bmpData);
            return left;
        }
    }

    public class RichTextBoxPrintCtrl : RichTextBox
    {
        //Convert the unit used by the .NET framework (1/100 inch)
        //and the unit used by Win32 API calls (twips 1/1440 inch)

        protected override CreateParams CreateParams
        {
            get
            {
                var cparams = base.CreateParams;
                if (LoadLibrary("msftedit.dll") != IntPtr.Zero) cparams.ClassName = "RICHEDIT50W";
                return cparams;
            }
        }

        //[DllImport("USER32.dll")]
        //private static extern IntPtr SendMessage(IntPtr hWnd, int msg, IntPtr wp, IntPtr lp);

        // Render the contents of the RichTextBox for printing

        // Return the last character printed + 1 (printing start from this point for next page)

        [DllImport("kernel32.dll", CharSet = CharSet.Auto)]
        private static extern IntPtr LoadLibrary(string lpFileName);
    }
}