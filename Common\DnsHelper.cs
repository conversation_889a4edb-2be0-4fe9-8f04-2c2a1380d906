﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text.RegularExpressions;

namespace OCRTools
{
    public class DnsHelper
    {
        private static readonly Dictionary<string, string> DicServers = new Dictionary<string, string>();

        public static string GetDnsIp(string host)
        {
            var result = "";
            try
            {
                if (!string.IsNullOrEmpty(host) && !IsIPv4(host))
                {
                    result = GetDnsCache(host);

                    if (string.IsNullOrEmpty(result))
                    {
                        result = ToIpAddress(host);

                        if (!IsIPv4(result))
                            result = string.Empty;
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("GetDnsIp:" + host, oe);
            }

            return result;
        }

        public static void InitServerService()
        {
            var timerInfo = new TimerInfo
            {
                TimerType = "LoopMinutes",
                DateValue = 1,
                IsExecFirst = true,
                TaskName = "InitServerTask"
            };
            var thread = TimerTaskService.CreateTimerTaskService(timerInfo, (a, b, c) =>
            {
                Init();
                return true;
            });
            thread.Start();
        }

        public static bool Init()
        {
//#if DEBUG
//            CommonString.HostAccount = new WebInfo() { Https = false, Host = "localhost:19225" };
//            CommonString.HostCode = new WebInfo() { Https = false, Host = "localhost:19225" };
//            return true;
//#endif
            GetRealIp();
            SetServerUrl();
            FrmMain.IpInitFinishEvent?.Invoke(null, null);
            return lstAllSite?.Count > 0;
        }

        private static List<WebInfo> lstAllSite = new List<WebInfo>();

        private static void SetServerUrl()
        {
            if (lstAllSite.Count > 0
                && (CommonString.HostAccount == null
                || !lstAllSite.Any(p => Equals(CommonString.HostAccount.Ip, p.Ip) && Equals(CommonString.HostAccount.Host, p.Host) && Equals(CommonString.HostAccount.Https, p.Https))))
            {
                CommonString.HostAccount = GetNewSite(SiteType.Account);
            }
            if (lstAllSite.Count > 0
                && (CommonString.HostCode == null
                || !lstAllSite.Any(p => Equals(CommonString.HostCode.Ip, p.Ip) && Equals(CommonString.HostCode.Host, p.Host) && Equals(CommonString.HostCode.Https, p.Https))))
            {
                CommonString.HostCode = GetNewSite(SiteType.Code);
            }
            if (lstAllSite.Count > 0
                && (CommonString.HostUpdate == null
                || !lstAllSite.Any(p => Equals(CommonString.HostUpdate.Ip, p.Ip) && Equals(CommonString.HostUpdate.Host, p.Host) && Equals(CommonString.HostUpdate.Https, p.Https))))
            {
                CommonString.HostUpdate = GetNewSite(SiteType.Update);
            }
        }

        public static void ReportError(string host, string ip)
        {
            var siteType = CommonString.IsSelfHost(host);
            if (Equals(siteType, SiteType.Default))
            {
                return;
            }
            switch (siteType)
            {
                case SiteType.Account:
                    //如果跟当前Site一致，特殊处理，否则跳过
                    if (Equals(CommonString.HostAccount.Ip, ip) && CommonString.HostAccount.Host.Contains(host))
                    {
                        var site = GetNewSite(siteType, host, ip);
                        if (site != null && !string.IsNullOrEmpty(site.Host))
                        {
                            CommonString.HostAccount = site;
                        }
                    }
                    break;
                case SiteType.Code:
                    //如果跟当前Site一致，特殊处理，否则跳过
                    if (Equals(CommonString.HostCode.Ip, ip) && CommonString.HostCode.Host.Contains(host))
                    {
                        var site = GetNewSite(siteType, host, ip);
                        if (site != null && !string.IsNullOrEmpty(site.Host))
                        {
                            CommonString.HostCode = site;
                        }
                    }
                    break;
                case SiteType.Update:
                    //如果跟当前Site一致，特殊处理，否则跳过
                    if (Equals(CommonString.HostUpdate.Ip, ip) && CommonString.HostUpdate.Host.Contains(host))
                    {
                        var site = GetNewSite(siteType, host, ip);
                        if (site != null && !string.IsNullOrEmpty(site.Host))
                        {
                            CommonString.HostUpdate = site;
                        }
                    }
                    break;
            }
        }

        private static WebInfo GetNewSite(SiteType type, string host = null, string ip = null)
        {
            var site = new WebInfo();
            var lstSite = lstAllSite.Where(p => Equals(p.Type, type)).ToList();
            if (lstSite.Count <= 0)
            {
                lstSite = lstAllSite.Where(p => Equals(p.Type, SiteType.Default)).ToList();
            }
            //如果池子中不存在，重新设置Host
            if ((string.IsNullOrEmpty(host) && string.IsNullOrEmpty(ip)) || !lstSite.Any(p => p.Host.Contains(host) && Equals(p.Ip, ip)))
            {
                site = lstSite.FirstOrDefault();
            }
            else
            {
                site = lstSite.FirstOrDefault(p => !p.Host.Contains(host) || !Equals(p.Ip, ip));
            }
            return site;
        }

        private static void GetRealIp()
        {
            ConcurrentBag<SiteMain> sites = CommonCDNRequest<SiteMain>.GetInfo(CommonString.HostUpdate?.FullUrl + "site.json", GetTFromStr, CommonString.StrServerHost);
            var site = sites.OrderByDescending(p => p.update.ToDateTime()).FirstOrDefault();
            if (site != null)
            {
                site.web?.ForEach(p =>
                {
                    if (string.IsNullOrEmpty(p.Host))
                    {
                        p.Host = site.defaultHost;
                    }
                });
                //site.web[0].Host = "oldfish.azurewebsites.net";
                //site.web[0].Ip = null;
                lstAllSite = site.web;
            }
        }

        private static SiteMain GetTFromStr(string html)
        {
            SiteMain site = null;
            if (!string.IsNullOrEmpty(html))
            {
                try
                {
                    site = html.DeserializeJson<SiteMain>();
                }
                catch (Exception oe)
                {
                    Log.WriteError("GetSiteFromStr:" + html, oe);
                }
            }
            return site;
        }

        static string ToIpAddress(string hostNameOrAddress)
        {
            var addrs = Dns.GetHostAddresses(hostNameOrAddress);
            return addrs.FirstOrDefault(addr => addr.AddressFamily == AddressFamily.InterNetwork)?.ToString();
        }

        static string GetDnsCache(string strHost)
        {
            var cache = "";
            if (DicServers.TryGetValue(strHost, out var server)) cache = server;
            return cache;
        }

        public static bool IsIPv4(string input)
        {
            var array = input.Split('.');
            if (array.Length != 4) return false;
            foreach (var t in array)
            {
                if (!IsMatch("^\\d+$", t)) return false;
                if (Convert.ToUInt16(t) > 255) return false;
            }

            return true;
        }

        static bool IsMatch(string pattern, string input)
        {
            if (string.IsNullOrEmpty(input)) return false;
            var regex = new Regex(pattern);
            return regex.IsMatch(input);
        }
    }
}