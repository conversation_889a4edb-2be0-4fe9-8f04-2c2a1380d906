﻿using System;
using System.Drawing;
using System.Drawing.Imaging;

namespace OCRTools
{
    public class FastBitmap : IDisposable, ICloneable
	{
		private Bitmap bitmap_0;

		internal BitmapData bitmapData_0;

		public Bitmap Bitmap
		{
			get => bitmap_0;
			set
			{
				if (value != null)
				{
					bitmap_0 = value;
				}
			}
		}

		public int Height => bitmap_0.Height;

		public int Width => bitmap_0.Width;

		private FastBitmap()
		{
		}

		public FastBitmap(Bitmap bitmap)
		{
			bitmap_0 = bitmap;
		}

		public FastBitmap(int width, int height, PixelFormat format)
		{
			bitmap_0 = new Bitmap(width, height, format);
		}

		public object Clone()
		{
			FastBitmap fastBitmap = new FastBitmap
			{
				bitmap_0 = (Bitmap)bitmap_0.Clone()
			};
			return fastBitmap;
		}

		public void Dispose()
		{
			GC.SuppressFinalize(this);
			Dispose(disposing: true);
		}

		protected virtual void Dispose(bool disposing)
		{
			Unlock();
			if (disposing)
			{
				bitmap_0.Dispose();
			}
		}

		~FastBitmap()
		{
			Dispose(disposing: false);
		}

		public byte GetIntensity(int x, int y)
		{
			Color pixel = GetPixel(x, y);
			return (byte)(pixel.R * 0.3 + pixel.G * 0.59 + pixel.B * 0.11 + 0.5);
		}

		public unsafe Color GetPixel(int x, int y)
		{
			if (bitmapData_0.PixelFormat == PixelFormat.Format32bppArgb)
			{
				byte* ptr = (byte*)((int)bitmapData_0.Scan0 + y * bitmapData_0.Stride + x * 4);
				return Color.FromArgb(ptr[3], ptr[2], ptr[1], *ptr);
			}
			if (bitmapData_0.PixelFormat == PixelFormat.Format24bppRgb)
			{
				byte* ptr2 = (byte*)((int)bitmapData_0.Scan0 + y * bitmapData_0.Stride + x * 3);
				return Color.FromArgb(ptr2[2], ptr2[1], *ptr2);
			}
			return Color.Empty;
		}

		public void Lock()
		{
			bitmapData_0 = bitmap_0.LockBits(new Rectangle(0, 0, bitmap_0.Width, bitmap_0.Height), ImageLockMode.ReadWrite, bitmap_0.PixelFormat);
		}

		public void Save(string filename)
		{
			bitmap_0.Save(filename);
		}

		public void Save(string filename, ImageFormat format)
		{
			bitmap_0.Save(filename, format);
		}

		public unsafe void SetPixel(int x, int y, Color c)
		{
			if (bitmapData_0.PixelFormat == PixelFormat.Format32bppArgb)
			{
				byte* ptr = (byte*)((int)bitmapData_0.Scan0 + y * bitmapData_0.Stride + x * 4);
				*ptr = c.B;
				ptr[1] = c.G;
				ptr[2] = c.R;
				ptr[3] = c.A;
			}
			if (bitmapData_0.PixelFormat == PixelFormat.Format24bppRgb)
			{
				byte* ptr2 = (byte*)((int)bitmapData_0.Scan0 + y * bitmapData_0.Stride + x * 3);
				*ptr2 = c.B;
				ptr2[1] = c.G;
				ptr2[2] = c.R;
			}
		}

		public void Unlock()
		{
			if (bitmapData_0 != null)
			{
				bitmap_0.UnlockBits(bitmapData_0);
				bitmapData_0 = null;
			}
		}
	}
}
