﻿using System.Drawing;
using System.Drawing.Drawing2D;

namespace OCRTools
{
    public static class GraphicsPathHelper
    {
        public static GraphicsPath CreatePath(Rectangle rect, int radius, SkinButtonRoundStyle style, bool correction)
        {
            GraphicsPath graphicsPath = new GraphicsPath();
            int num = correction ? 1 : 0;
            switch (style)
            {
                case SkinButtonRoundStyle.None:
                    graphicsPath.AddRectangle(rect);
                    break;
                case SkinButtonRoundStyle.TopLeft:
                    graphicsPath.AddArc(rect.X, rect.Y, radius, radius, 180f, 90f);
                    graphicsPath.AddLine(rect.Right - num, rect.Y, rect.Right - num, rect.Bottom - num);
                    graphicsPath.AddLine(rect.Right - num, rect.Bottom - num, rect.X, rect.Bottom - num);
                    break;
                case SkinButtonRoundStyle.TopRight:
                    graphicsPath.AddArc(rect.Right - radius - num, rect.Y, radius, radius, 270f, 90f);
                    graphicsPath.AddLine(rect.Right - num, rect.Bottom - num, rect.X, rect.Bottom - num);
                    graphicsPath.AddLine(rect.X, rect.Bottom - num, rect.X, rect.Y);
                    break;
                case SkinButtonRoundStyle.Top:
                    graphicsPath.AddArc(rect.X, rect.Y, radius, radius, 180f, 90f);
                    graphicsPath.AddArc(rect.Right - radius - num, rect.Y, radius, radius, 270f, 90f);
                    graphicsPath.AddLine(rect.Right - num, rect.Bottom - num, rect.X, rect.Bottom - num);
                    break;
                case SkinButtonRoundStyle.BottomLeft:
                    graphicsPath.AddArc(rect.X, rect.Bottom - radius - num, radius, radius, 90f, 90f);
                    graphicsPath.AddLine(rect.X, rect.Y, rect.Right - num, rect.Y);
                    graphicsPath.AddLine(rect.Right - num, rect.Y, rect.Right - num, rect.Bottom - num);
                    break;
                case SkinButtonRoundStyle.Left:
                    graphicsPath.AddArc(rect.X, rect.Y, radius, radius, 180f, 90f);
                    graphicsPath.AddLine(rect.Right - num, rect.Y, rect.Right - num, rect.Bottom - num);
                    graphicsPath.AddArc(rect.X, rect.Bottom - radius - num, radius, radius, 90f, 90f);
                    break;
                case SkinButtonRoundStyle.BottomRight:
                    graphicsPath.AddArc(rect.Right - radius - num, rect.Bottom - radius - num, radius, radius, 0f, 90f);
                    graphicsPath.AddLine(rect.X, rect.Bottom - num, rect.X, rect.Y);
                    graphicsPath.AddLine(rect.X, rect.Y, rect.Right - num, rect.Y);
                    break;
                case SkinButtonRoundStyle.Right:
                    graphicsPath.AddArc(rect.Right - radius - num, rect.Y, radius, radius, 270f, 90f);
                    graphicsPath.AddArc(rect.Right - radius - num, rect.Bottom - radius - num, radius, radius, 0f, 90f);
                    graphicsPath.AddLine(rect.X, rect.Bottom - num, rect.X, rect.Y);
                    break;
                case SkinButtonRoundStyle.Bottom:
                    graphicsPath.AddArc(rect.Right - radius - num, rect.Bottom - radius - num, radius, radius, 0f, 90f);
                    graphicsPath.AddArc(rect.X, rect.Bottom - radius - num, radius, radius, 90f, 90f);
                    graphicsPath.AddLine(rect.X, rect.Y, rect.Right - num, rect.Y);
                    break;
                case SkinButtonRoundStyle.All:
                    graphicsPath.AddArc(rect.X, rect.Y, radius, radius, 180f, 90f);
                    graphicsPath.AddArc(rect.Right - radius - num, rect.Y, radius, radius, 270f, 90f);
                    graphicsPath.AddArc(rect.Right - radius - num, rect.Bottom - radius - num, radius, radius, 0f, 90f);
                    graphicsPath.AddArc(rect.X, rect.Bottom - radius - num, radius, radius, 90f, 90f);
                    break;
            }
            graphicsPath.CloseFigure();
            return graphicsPath;
        }

        //public static GraphicsPath CreateTrackBarThumbPath(Rectangle rect, ThumbArrowDirection arrowDirection)
        //{
        //    GraphicsPath graphicsPath = new GraphicsPath();
        //    PointF pointF = new PointF(rect.X + rect.Width / 2f, rect.Y + rect.Height / 2f);
        //    float num = 0f;
        //    switch (arrowDirection)
        //    {
        //        case ThumbArrowDirection.Left:
        //        case ThumbArrowDirection.Right:
        //            num = rect.Width / 2f - 4f;
        //            break;
        //        case ThumbArrowDirection.Up:
        //        case ThumbArrowDirection.Down:
        //            num = rect.Height / 2f - 4f;
        //            break;
        //    }
        //    switch (arrowDirection)
        //    {
        //        case ThumbArrowDirection.None:
        //            graphicsPath.AddRectangle(rect);
        //            break;
        //        case ThumbArrowDirection.Left:
        //            graphicsPath.AddLine(rect.X, pointF.Y, rect.X + num, rect.Y);
        //            graphicsPath.AddLine(rect.Right, rect.Y, rect.Right, rect.Bottom);
        //            graphicsPath.AddLine(rect.X + num, rect.Bottom, rect.X, pointF.Y);
        //            break;
        //        case ThumbArrowDirection.Right:
        //            graphicsPath.AddLine(rect.Right, pointF.Y, rect.Right - num, rect.Bottom);
        //            graphicsPath.AddLine(rect.X, rect.Bottom, rect.X, rect.Y);
        //            graphicsPath.AddLine(rect.Right - num, rect.Y, rect.Right, pointF.Y);
        //            break;
        //        case ThumbArrowDirection.Up:
        //            graphicsPath.AddLine(pointF.X, rect.Y, rect.X, rect.Y + num);
        //            graphicsPath.AddLine(rect.X, rect.Bottom, rect.Right, rect.Bottom);
        //            graphicsPath.AddLine(rect.Right, rect.Y + num, pointF.X, rect.Y);
        //            break;
        //        case ThumbArrowDirection.Down:
        //            graphicsPath.AddLine(pointF.X, rect.Bottom, rect.X, rect.Bottom - num);
        //            graphicsPath.AddLine(rect.X, rect.Y, rect.Right, rect.Y);
        //            graphicsPath.AddLine(rect.Right, rect.Bottom - num, pointF.X, rect.Bottom);
        //            break;
        //    }
        //    graphicsPath.CloseFigure();
        //    return graphicsPath;
        //}

        //public static GraphicsPath Create7x4In7x7DownTriangleFlag(Rectangle rect)
        //{
        //    GraphicsPath graphicsPath = new GraphicsPath();
        //    int num = rect.X + (rect.Width - 7) / 2;
        //    int num2 = rect.Y + (rect.Height - 7) / 2 + 2;
        //    int num3 = num;
        //    int num4 = num + 6;
        //    for (int i = 0; i < 4; i++)
        //    {
        //        if (i % 2 == 0)
        //        {
        //            graphicsPath.AddLine(num3, num2, num4, num2);
        //        }
        //        else
        //        {
        //            graphicsPath.AddLine(num4, num2, num3, num2);
        //        }
        //        num3++;
        //        num4--;
        //        num2++;
        //    }
        //    graphicsPath.CloseFigure();
        //    return graphicsPath;
        //}
    }
}
