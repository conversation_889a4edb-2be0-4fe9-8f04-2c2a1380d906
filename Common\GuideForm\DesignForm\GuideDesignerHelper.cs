using System;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace OCRTools.Common
{
    /// <summary>
    /// 引导设计器助手 - 提供静态方法方便调用设计器
    /// </summary>
    public static class GuideDesignerHelper
    {
        /// <summary>
        /// 为指定窗体创建引导设计器
        /// </summary>
        /// <param name="form">目标窗体</param>
        public static void CreateGuideForForm(Form form)
        {
            GuideDesigner.CreateForForm(form);
        }

        /// <summary>
        /// 加载现有引导并在设计器中编辑
        /// </summary>
        /// <param name="form">目标窗体</param>
        /// <param name="guidePath">引导文件路径</param>
        public static void EditExistingGuide(Form form, string guidePath)
        {
            try
            {
                GuideEntity guide = LoadGuideFromFile(guidePath);
                if (guide != null)
                {
                    GuideDesigner.CreateForForm(form, guide);
                }
                else
                {
                    MessageBox.Show("无法加载引导文件，将创建新的引导。", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    GuideDesigner.CreateForForm(form);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("加载引导文件时出错：" + ex.Message, "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                GuideDesigner.CreateForForm(form);
            }
        }

        /// <summary>
        /// 从文件加载引导
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>引导实体，失败则返回null</returns>
        public static GuideEntity LoadGuideFromFile(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                return null;

            try
            {
                // 尝试按 JSON 格式加载
                string content = File.ReadAllText(filePath);
                return content.DeserializeJson<GuideEntity>();
            }
            catch (Exception ex)
            {
                string errorMessage = "加载引导文件失败: " + ex.Message;

                // 记录错误日志
                try
                {
                    Log.WriteError("LoadGuideFromFile: " + filePath, ex);
                }
                catch
                {
                    // 如果日志记录失败，至少在控制台显示错误
                    Console.WriteLine(errorMessage);
                }

                return null;
            }
        }

        /// <summary>
        /// 将引导保存到文件
        /// </summary>
        /// <param name="guide">引导实体</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否保存成功</returns>
        public static bool SaveGuideToFile(GuideEntity guide, string filePath)
        {
            if (guide == null)
                return false;

            try
            {
                // 创建目录（如果不存在）
                string directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 默认使用 JSON 格式
                string json = CommonString.JavaScriptSerializer.Serialize(guide);
                File.WriteAllText(filePath, json);

                return true;
            }
            catch (Exception ex)
            {
                string errorMessage = "保存引导文件失败: " + ex.Message;

                // 记录错误日志
                try
                {
                    Log.WriteError("SaveGuideToFile: " + filePath, ex);
                }
                catch
                {
                    // 如果日志记录失败，至少在控制台显示错误
                    Console.WriteLine(errorMessage);
                }

                return false;
            }
        }

        /// <summary>
        /// 生成引导代码
        /// </summary>
        /// <param name="guide">引导实体</param>
        /// <returns>代码字符串</returns>
        public static string GenerateGuideCode(GuideEntity guide)
        {
            if (guide == null || guide.Items.Count == 0)
                return string.Empty;

            StringBuilder sb = new StringBuilder();
            sb.AppendLine("// 引导代码 - 由引导设计器生成");
            sb.AppendLine("// 生成时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            sb.AppendLine();
            sb.AppendLine("using OCRTools.Common;");
            sb.AppendLine("using System;");
            sb.AppendLine("using System.Drawing;");
            sb.AppendLine("using System.Windows.Forms;");
            sb.AppendLine();
            sb.AppendLine("// 创建引导实体");
            sb.AppendLine("GuideEntity guide = new GuideEntity();");
            sb.AppendLine($"guide.Title = \"{guide.Title}\";");
            sb.AppendLine($"guide.Desc = \"{guide.Desc}\";");
            sb.AppendLine($"guide.Target = \"{guide.Target}\";");
            sb.AppendLine($"guide.BaseSize = new Size({guide.BaseSize.Width}, {guide.BaseSize.Height});");
            sb.AppendLine($"guide.ShowSummary = {guide.ShowSummary.ToString().ToLower()};");
            sb.AppendLine();

            // 生成每个步骤的代码
            for (int i = 0; i < guide.Items.Count; i++)
            {
                GuideItem item = guide.Items[i];
                sb.AppendLine($"// 步骤 {i + 1}: {item.Title}");
                sb.AppendLine("GuideItem item" + (i + 1) + " = new GuideItem();");
                sb.AppendLine($"item{i + 1}.Title = \"{item.Title}\";");
                sb.AppendLine($"item{i + 1}.Desc = \"{item.Desc}\";");
                sb.AppendLine($"item{i + 1}.Type = GuideItem.HighlightType.{item.Type};");

                if (!string.IsNullOrEmpty(item.Ctrl))
                {
                    sb.AppendLine($"item{i + 1}.Ctrl = \"{item.Ctrl}\";");
                }

                if (!item.Rect.IsEmpty)
                {
                    sb.AppendLine($"item{i + 1}.Rect = new Rectangle({item.Rect.X}, {item.Rect.Y}, {item.Rect.Width}, {item.Rect.Height});");
                }

                if (item.EnhancedArea != null)
                {
                    sb.AppendLine($"// 增强型智能高亮区域");
                    sb.AppendLine($"item{i + 1}.EnhancedArea = new EnhancedSmartArea();");
                    sb.AppendLine($"item{i + 1}.EnhancedArea.Type = EnhancedSmartArea.AreaType.{item.EnhancedArea.Type};");

                    if (!item.EnhancedArea.OriginalRect.IsEmpty)
                    {
                        var r = item.EnhancedArea.OriginalRect;
                        sb.AppendLine($"item{i + 1}.EnhancedArea.OriginalRect = new Rectangle({r.X}, {r.Y}, {r.Width}, {r.Height});");
                    }

                    if (!item.EnhancedArea.RelativeRect.IsEmpty)
                    {
                        var r = item.EnhancedArea.RelativeRect;
                        sb.AppendLine($"item{i + 1}.EnhancedArea.RelativeRect = new RectangleF({r.X}f, {r.Y}f, {r.Width}f, {r.Height}f);");
                    }
                }

                sb.AppendLine($"guide.Items.Add(item{i + 1});");
                sb.AppendLine();
            }

            sb.AppendLine("// 显示引导");
            sb.AppendLine("CommonGuide.ShowGuide(this, guide);");

            return sb.ToString();
        }

        /// <summary>
        /// 显示引导
        /// </summary>
        /// <param name="form">目标窗体</param>
        /// <param name="guidePath">引导文件路径</param>
        public static void ShowGuide(Form form, string guidePath)
        {
            GuideEntity guide = LoadGuideFromFile(guidePath);
            if (guide != null)
            {
                CommonGuide.ShowGuide(form, guide);
            }
            else
            {
                MessageBox.Show("无法加载引导文件。", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}