﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools.Common
{
    /// <summary>
    /// 集中管理主题色彩和样式
    /// </summary>
    internal class GuideThemeManager
    {
        public Color[] PanelBlendColors { get; private set; }
        public float[] PanelBlendPositions { get; private set; }

        // 边框与高光
        public Color InnerBorderColor { get; private set; }
        public Color TopLineColor { get; private set; }

        // 遮罩与高亮区域
        public Color MaskColor { get; private set; }
        public Color BubbleBorderColor { get; private set; }
        public float BorderWidth { get; private set; }

        // 太阳/月亮效果颜色
        public Color CoreCenterColor { get; private set; }
        public Color CoreSurroundColor { get; private set; }
        public Color OuterCenterBaseColor { get; private set; }
        public Color OuterSurroundColor { get; private set; }
        public Color WideGlowCenterColor { get; private set; }
        public Color FlareBase1 { get; private set; }
        public Color FlareBase2 { get; private set; }
        public Color FlareBase3 { get; private set; }
        public float GlowRadiusFactor { get; private set; }

        // 文本颜色
        public Color TitleTextColor { get; private set; }
        public Color DescTextColor { get; private set; }

        // 按钮颜色
        public ButtonStyle PrevButtonStyle { get; private set; }
        public ButtonStyle NextButtonStyle { get; private set; }
        public LinkStyle SkipLinkStyle { get; private set; }

        public class ButtonStyle
        {
            public bool IsRound { get; set; }
            public FlatStyle Style { get; set; }
            public int BorderSize { get; set; }
            public Color BackColor { get; set; }
            public Color ForeColor { get; set; }
            public Color HoverColor { get; set; }
            public Color ClickColor { get; set; }
        }

        public class LinkStyle
        {
            public LinkBehavior Behavior { get; set; }
            public Color ForeColor { get; set; }
            public Color LinkColor { get; set; }
            public Color ActiveLinkColor { get; set; }
        }

        // 初始化主题管理器，根据当前模式设置所有相关样式
        public static GuideThemeManager Create(bool isDarkMode)
        {
            var manager = new GuideThemeManager();

            if (isDarkMode) // 夜间模式
            {
                // 面板背景
                var PanelBaseColor = Color.FromArgb(255, 20, 25, 55);
                var PanelGradientEndColor = Color.FromArgb(255, 35, 70, 190); // 较亮深夜天空蓝

                // 渐变色配置
                manager.PanelBlendColors = new Color[] {
                        PanelBaseColor,
                        Color.FromArgb(255, (PanelBaseColor.R + PanelGradientEndColor.R) / 2,
                                          (PanelBaseColor.G + PanelGradientEndColor.G) / 2,
                                          (PanelBaseColor.B + PanelGradientEndColor.B) / 2),
                        PanelGradientEndColor
                    };
                manager.PanelBlendPositions = new float[] { 0.0f, 0.3f, 1.0f };

                // 边框与高光
                manager.InnerBorderColor = Color.FromArgb(25, 200, 210, 230); // 低透明度淡蓝白
                manager.TopLineColor = Color.FromArgb(20, 210, 220, 240); // 低透明度淡蓝白

                // 遮罩与高亮区域
                manager.MaskColor = Color.FromArgb(220, 10, 10, 20); // 较深微蓝遮罩
                manager.BubbleBorderColor = Color.FromArgb(255, 0, 122, 204); // 科技蓝边框
                manager.BorderWidth = 1.5f; // 稍厚以增强可见性

                // 月亮效果颜色
                manager.CoreCenterColor = Color.Yellow;// Color.FromArgb(255, 255, 250, 180); // 黄色核心
                manager.CoreSurroundColor = Color.FromArgb(170, 255, 250, 170); // 黄色包围
                manager.OuterCenterBaseColor = Color.FromArgb(255, 200, 210, 230);
                manager.OuterSurroundColor = Color.FromArgb(0, 180, 190, 220);
                manager.WideGlowCenterColor = Color.FromArgb(50, 255, 245, 150); // 柔和黄晕
                manager.FlareBase1 = Color.FromArgb(180, 215, 255, 240);
                manager.FlareBase2 = Color.Yellow;// Color.FromArgb(255, 240, 180, 230);
                manager.FlareBase3 = Color.FromArgb(255, 200, 120, 220);
                manager.GlowRadiusFactor = 0.70f; // 夜间模式的月晕半径因子

                // 文本颜色
                manager.TitleTextColor = Color.White;
                manager.DescTextColor = Color.FromArgb(255, 230, 230, 230);

                // 链接样式
                manager.SkipLinkStyle = new LinkStyle
                {
                    Behavior = LinkBehavior.NeverUnderline,
                    ForeColor = Color.FromArgb(255, 130, 150, 180),
                    LinkColor = Color.FromArgb(255, 130, 150, 180),
                    ActiveLinkColor = Color.FromArgb(255, 160, 180, 210)
                };

                // 按钮样式 - 前一步
                manager.PrevButtonStyle = new ButtonStyle
                {
                    IsRound = true,
                    Style = FlatStyle.Flat,
                    BorderSize = 0,
                    BackColor = Color.FromArgb(255, 70, 90, 135),
                    ForeColor = Color.FromArgb(255, 220, 230, 245),
                    HoverColor = Color.FromArgb(255, 85, 105, 150),
                    ClickColor = Color.FromArgb(255, 60, 80, 125)
                };

                // 按钮样式 - 下一步
                manager.NextButtonStyle = new ButtonStyle
                {
                    IsRound = true,
                    Style = FlatStyle.Flat,
                    BorderSize = 0,
                    BackColor = Color.FromArgb(255, 0, 60, 145),
                    ForeColor = Color.FromArgb(255, 210, 215, 220),
                    HoverColor = Color.FromArgb(255, 0, 80, 165),
                    ClickColor = Color.FromArgb(255, 0, 45, 125)
                };
            }
            else // 日间模式
            {
                // 渐变色配置 - 调整为更饱和的蓝色系列
                manager.PanelBlendColors = new Color[] {
                        Color.FromArgb(255, 25, 105, 230),   // 左上角深蓝
                        Color.FromArgb(255, 30, 110, 235),   // 左下角中蓝
                        Color.FromArgb(255, 35, 115, 238),   // 右上角中蓝
                        Color.FromArgb(255, 38, 118, 240)    // 右下角浅蓝
                    };
                manager.PanelBlendPositions = new float[] { 0.0f, 0.4f, 0.6f, 1.0f };

                // 边框与高光
                manager.InnerBorderColor = Color.FromArgb(70, 255, 255, 255);
                manager.TopLineColor = Color.FromArgb(50, 255, 255, 255);

                // 遮罩与高亮区域
                manager.MaskColor = Color.FromArgb(100, 0, 0, 0); // 将透明度从190降低到100
                manager.BubbleBorderColor = Color.White;
                manager.BorderWidth = 1.0f;

                // 太阳效果颜色
                manager.CoreCenterColor = Color.FromArgb(255, 248, 220, 80);
                manager.CoreSurroundColor = Color.FromArgb(80, 255, 255, 60);
                manager.OuterCenterBaseColor = Color.FromArgb(255, 255, 240, 80);
                manager.OuterSurroundColor = Color.FromArgb(0, 255, 240, 120);
                manager.WideGlowCenterColor = Color.FromArgb(15, 255, 220, 100); // 顺滑更弥散的太阳晕 (Alpha 15)
                manager.FlareBase1 = Color.FromArgb(255, 220, 200);
                manager.FlareBase2 = Color.FromArgb(180, 220, 255);
                manager.FlareBase3 = Color.FromArgb(255, 240, 200);
                manager.GlowRadiusFactor = 1.1f; // 日间模式的太阳晕极大扩展因子

                // 文本颜色
                manager.TitleTextColor = Color.White;
                manager.DescTextColor = Color.White;

                // 链接样式
                manager.SkipLinkStyle = new LinkStyle
                {
                    Behavior = LinkBehavior.NeverUnderline,
                    ForeColor = Color.FromArgb(255, 165, 205, 253),
                    LinkColor = Color.FromArgb(255, 165, 205, 253),
                    ActiveLinkColor = Color.DarkBlue
                };

                // 按钮样式 - 前一步
                manager.PrevButtonStyle = new ButtonStyle
                {
                    Style = FlatStyle.Standard,
                    BorderSize = 1,
                    BackColor = Color.FromArgb(255, 0, 92, 230),
                    ForeColor = Color.White,
                    HoverColor = Color.FromArgb(255, 0, 102, 240),
                    ClickColor = Color.FromArgb(255, 0, 82, 220)
                };

                // 按钮样式 - 下一步
                manager.NextButtonStyle = new ButtonStyle
                {
                    Style = FlatStyle.Standard,
                    BorderSize = 1,
                    BackColor = Color.White,
                    ForeColor = Color.FromArgb(255, 0, 95, 235),
                    HoverColor = Color.FromArgb(255, 240, 240, 240),
                    ClickColor = Color.FromArgb(255, 230, 230, 230)
                };
            }

            return manager;
        }
    }
}
