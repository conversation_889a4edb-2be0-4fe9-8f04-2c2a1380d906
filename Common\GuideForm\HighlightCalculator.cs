using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace OCRTools.Common
{
    /// <summary>
    /// 高亮区域计算工具类
    /// 统一处理设计器和预览界面的高亮区域计算逻辑，
    /// 以设计器逻辑(OverlayForm)为准
    /// </summary>
    public static class HighlightCalculator
    {
        /// <summary>
        /// 计算高亮区域
        /// </summary>
        /// <param name="item">引导项</param>
        /// <param name="targetForm">目标窗体</param>
        /// <param name="baseSize">基础尺寸</param>
        /// <returns>高亮区域矩形</returns>
        public static Rectangle CalculateHighlightRect(GuideItem item, Form targetForm, Size baseSize)
        {
            if (item == null || targetForm == null)
                return Rectangle.Empty;

            try
            {
                // 首先检查是否有EnhancedArea，优先使用
                if (item.EnhancedArea != null)
                {
                    // 设置自动检测变化，保持与OverlayForm一致
                    item.EnhancedArea.AutoDetectChanges = true;

                    // 使用EnhancedSmartArea计算的方式可能调用CalculateActualRect
                    // 这里我们直接使用该方法，不做特殊处理
                    return item.EnhancedArea.CalculateActualRect(targetForm);
                }

                // 根据高亮类型选择不同的计算方式
                switch (item.Type)
                {
                    case GuideItem.HighlightType.Control:
                        // 控件高亮
                        if (!string.IsNullOrEmpty(item.Ctrl))
                        {
                            // 使用通用的控件查找和位置计算方法
                            return GetControlRectangle(item.Ctrl, targetForm);
                        }
                        break;

                    case GuideItem.HighlightType.Default:
                    default:
                        // 如果没有指定区域，返回空区域
                        if (item.Rect.IsEmpty)
                        {
                            return Rectangle.Empty;
                        }

                        // 处理默认矩形区域
                        // 对于负坐标的处理方式，保持与设计页面一致
                        Rectangle rect = item.Rect;

                        // 应用窗体缩放
                        float wScale = (float)targetForm.ClientSize.Width / baseSize.Width;
                        float hScale = (float)targetForm.ClientSize.Height / baseSize.Height;

                        Rectangle scaledRect = new Rectangle(
                            (int)(rect.X * wScale),
                            (int)(rect.Y * hScale),
                            (int)(rect.Width * wScale),
                            (int)(rect.Height * hScale)
                        );

                        // 处理负坐标，与设计页面保持一致
                        if (scaledRect.X < 0) scaledRect.X += targetForm.Width;
                        if (scaledRect.Y < 0) scaledRect.Y += targetForm.Height;

                        return scaledRect;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"计算高亮区域失败: {ex.Message}");
            }

            return Rectangle.Empty;
        }

        public static Rectangle GetControlRectangle(string ctrl, Form targetForm)
        {
            if (string.IsNullOrEmpty(ctrl) || targetForm == null)
                return Rectangle.Empty;

            // 使用通用的控件查找和位置计算方法
            Rectangle rect = GetElementRect(ctrl, targetForm);

            // 应用Padding
            if (!rect.IsEmpty)
            {
                rect = rect.ZoomBig(10);
            }

            return rect;
        }

        /// <summary>
        /// 获取控件或特殊项元素的矩形区域 - 通用方法
        /// 能够处理普通控件和特殊项（如菜单项）
        /// </summary>
        /// <param name="elementId">控件ID或路径，如"button1"或"menuStrip1.fileMenu"</param>
        /// <param name="targetForm">目标窗体</param>
        /// <returns>元素在窗体中的矩形区域</returns>
        public static Rectangle GetElementRect(string elementId, Form targetForm)
        {
            if (string.IsNullOrEmpty(elementId) || targetForm == null)
                return Rectangle.Empty;

            try
            {
                // 处理特殊控件项（如菜单项）格式: "父控件.子项"
                if (elementId.Contains("."))
                {
                    string[] parts = elementId.Split('.');
                    if (parts.Length >= 2)
                    {
                        string parentId = parts[0];
                        string itemId = parts[1];

                        // 查找父控件
                        Control parentControl = FindControlById(targetForm, parentId);

                        if (parentControl != null)
                        {
                            // 处理ToolStrip菜单项
                            if (parentControl is ToolStrip toolStrip)
                            {
                                ToolStripItem item = null;

                                // 尝试通过名称查找
                                foreach (ToolStripItem tsi in toolStrip.Items)
                                {
                                    if (tsi.Name == itemId)
                                    {
                                        item = tsi;
                                        break;
                                    }
                                }

                                // 如果没找到，尝试通过索引查找(如果itemId是数字)
                                if (item == null && int.TryParse(itemId, out int index) &&
                                    index >= 0 && index < toolStrip.Items.Count)
                                {
                                    item = toolStrip.Items[index];
                                }

                                if (item != null)
                                {
                                    return GetMenuItemRect(item, targetForm);
                                }
                            }

                            // 处理其他容器控件，递归查找子控件
                            Control childControl = FindControlById(parentControl, itemId);
                            if (childControl != null)
                            {
                                return GetControlRectInForm(childControl, targetForm);
                            }
                        }
                    }
                }
                else
                {
                    // 直接查找控件
                    Control control = FindControlById(targetForm, elementId);
                    if (control != null)
                    {
                        return GetControlRectInForm(control, targetForm);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取元素区域异常: {ex.Message}");
            }

            return Rectangle.Empty;
        }

        /// <summary>
        /// 通过ID查找控件（支持名称或索引）
        /// </summary>
        /// <param name="parent">父控件</param>
        /// <param name="controlId">控件ID</param>
        /// <returns>找到的控件，未找到则为null</returns>
        private static Control FindControlById(Control parent, string controlId)
        {
            if (parent == null || string.IsNullOrEmpty(controlId))
                return null;

            if (Equals(parent.Name, controlId) || Equals(parent.AccessibleDefaultActionDescription, controlId))
            {
                return parent;
            }

            try
            {
                // 尝试通过名称查找
                Control[] foundControls = parent.Controls.Find(controlId, true);
                if (foundControls != null && foundControls.Length > 0)
                {
                    return foundControls[0];
                }

                // 检查是否为索引格式（数字）
                if (int.TryParse(controlId, out int index) && index >= 0 && index < parent.Controls.Count)
                {
                    return parent.Controls[index];
                }

                // 简单的深度优先搜索
                foreach (Control child in parent.Controls)
                {
                    if (Equals(child.Name, controlId) || Equals(child.AccessibleDefaultActionDescription, controlId))
                    {
                        return child;
                    }

                    // 递归查找
                    Control found = FindControlById(child, controlId);
                    if (found != null)
                    {
                        return found;
                    }
                }
            }
            catch
            {
                // 忽略异常，返回null
            }

            return null;
        }

        /// <summary>
        /// 获取控件在窗体中的矩形位置
        /// </summary>
        private static Rectangle GetControlRectInForm(Control control, Form form)
        {
            if (control == null || form == null)
                return Rectangle.Empty;

            try
            {
                // 获取控件在屏幕中的位置
                Point screenLocation = control.Parent.PointToScreen(control.Location);

                // 转换为窗体客户端坐标
                Point clientLocation = form.PointToClient(screenLocation);

                // 返回控件在窗体上的矩形区域
                return new Rectangle(clientLocation.X, clientLocation.Y, control.Width, control.Height);
            }
            catch
            {
                return Rectangle.Empty;
            }
        }

        /// <summary>
        /// 获取菜单项在窗体中的矩形位置
        /// </summary>
        private static Rectangle GetMenuItemRect(ToolStripItem item, Form form)
        {
            if (item == null || form == null)
                return Rectangle.Empty;

            try
            {
                ToolStrip owner = item.Owner;
                if (owner != null)
                {
                    try
                    {
                        // 计算菜单项在屏幕上的位置
                        Point screenPos = owner.PointToScreen(Point.Empty);
                        Rectangle bounds = item.Bounds;

                        Rectangle screenRect = new Rectangle(
                            screenPos.X + bounds.X,
                            screenPos.Y + bounds.Y,
                            Math.Max(bounds.Width, 5),
                            Math.Max(bounds.Height, 5));

                        // 转换为窗体坐标
                        return form.RectangleToClient(screenRect);
                    }
                    catch
                    {
                        // 备用方法：使用父控件位置加上相对位置
                        Rectangle ownerRect = GetControlRectInForm(owner, form);
                        return new Rectangle(
                            ownerRect.X + item.Bounds.X,
                            ownerRect.Y + item.Bounds.Y,
                            item.Bounds.Width,
                            item.Bounds.Height);
                    }
                }
            }
            catch
            {
                // 忽略异常
            }

            return Rectangle.Empty;
        }
    }
}