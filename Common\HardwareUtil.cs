﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Management;
using System.Net.NetworkInformation;
using System.Runtime.InteropServices;
using System.Text;
using System.Web.Security;

namespace OCRTools
{
    public class HardwareUtil
    {
        public static string StrCpuId { get; set; }

        public static string StrHDid { get; set; }

        public static string StrBiosDate { get; set; }

        /// <summary>
        ///     网卡编号
        /// </summary>
        /// <returns></returns>
        private static string GetNetAdapterNumber(bool isUseCMD = true)
        {
            var result = string.Empty;
            if (isUseCMD)
            {
                try
                {
                    var temp = CommonMethod.ExecCmd("vol c:");
                    if (temp.Contains("序列号是"))
                    {
                        temp = CommonMethod.SubString(temp, "序列号是").Trim();
                    }
                    else
                    {
                        if (temp.Contains("Serial Number is"))
                        {
                            temp = CommonMethod.SubString(temp, "Serial Number is").Trim();
                        }
                    }
                    result = CommonMethod.SubString(temp, "", "\n").Replace("-", "").Trim();
                    result = result.EndsWith(">exit") ? "" : result;
                }
                catch
                {
                    result = "";
                }
            }
            if (string.IsNullOrEmpty(result))
            {
                try
                {
                    //new ManagementClass("Win32_NetworkAdapterConfiguration");
                    using (var managementObject = new ManagementObject("win32_logicaldisk.deviceid=\"c:\""))
                    {
                        managementObject.Get();
                        result = managementObject.GetPropertyValue("VolumeSerialNumber").ToString();
                    }
                }
                catch
                {
                    result = "";
                }
            }
            return result;
        }

        /// <summary>
        ///     BIOS编号
        /// </summary>
        /// <returns></returns>
        private static string GetBIOSNumber(bool isUseCMD = true)
        {
            var result = string.Empty;
            if (isUseCMD)
            {
                try
                {
                    var temp = CommonMethod.ExecCmd("wmic bios get serialnumber");
                    temp = CommonMethod.SubString(temp, "SerialNumber").Trim();
                    result = CommonMethod.SubString(temp, "", "\n").Trim();
                    result = result.EndsWith(">exit") ? "" : result;
                }
                catch
                {
                    result = "";
                }
            }
            if (string.IsNullOrEmpty(result))
            {
                try
                {
                    using (var mc = new ManagementClass("Win32_BIOS"))
                    {
                        var moc = mc.GetInstances();
                        foreach (ManagementObject mo in moc)
                        {
                            result = mo.Properties["SerialNumber"].Value.ToString();
                            break;
                        }
                    }
                }
                catch
                {
                    result = "";
                }
            }
            return result;
        }

        /// <summary>
        ///     硬盘编号
        /// </summary>
        /// <returns></returns>
        public static string GetHDid()
        {
            if (!string.IsNullOrEmpty(StrHDid))
                return StrHDid;
            var HDid = "";
            try
            {
                var hdd = AtapiDevice.GetHddInfo(0); // 第一个硬盘
                //string hardinfo = "硬盘型号：" + hdd.ModuleNumber + "  硬盘ID号：" + hdd.SerialNumber + "  固件版本：" + hdd.Firmware + "  硬盘容量：" + hdd.Capacity;
                HDid = hdd.SerialNumber + hdd.ModuleNumber + hdd.Firmware;
            }
            catch
            {
            }
            if (string.IsNullOrEmpty(HDid))
            {
                try
                {
                    using (var mc = new ManagementClass("Win32_PhysicalMedia"))
                    {
                        //网上有提到，用Win32_DiskDrive，但是用Win32_DiskDrive获得的硬盘信息中并不包含SerialNumber属性。  
                        var moc = mc.GetInstances();
                        foreach (ManagementObject mo in moc)
                        {
                            HDid = mo.Properties["SerialNumber"].Value.ToString().Trim();
                            if (!string.IsNullOrEmpty(HDid))
                            {
                                break;
                            }
                        }
                    }
                }
                catch
                {
                }
            }
            if (string.IsNullOrEmpty(HDid))
            {
                try
                {
                    using (var mc = new ManagementClass("Win32_DiskDrive"))
                    {
                        var moc = mc.GetInstances();
                        foreach (ManagementObject mo in moc)
                        {
                            HDid = mo.Properties["SerialNumber"].Value.ToString().Trim();
                            if (!string.IsNullOrEmpty(HDid))
                            {
                                break;
                            }
                        }
                    }
                }
                catch
                {
                }
            }
            if (string.IsNullOrEmpty(StrHDid))
            {
                StrHDid = HDid.Trim();
            }
            return HDid.Trim();
        }

        /// <summary>
        ///     获取网卡的MAC地址
        /// </summary>
        /// <returns>返回一个string</returns>
        public static string GetNetCardMAC()
        {
            var strMAC = "";
            try
            {
                //获取所有网卡信息
                var nics = NetworkInterface.GetAllNetworkInterfaces();
                if (nics != null && nics.Length > 0)
                {
                    var lstTmp = new List<NetworkInterface>();
                    lstTmp.AddRange(nics);
                    var tmp = lstTmp.Find(p => p.NetworkInterfaceType == NetworkInterfaceType.Ethernet
                                               &&
                                               (p.Name.Contains("本地") || p.Name.ToLower().Contains("local") ||
                                                p.OperationalStatus == OperationalStatus.Up));
                    if (tmp != null && !string.IsNullOrEmpty(tmp.Id))
                    {
                        strMAC = tmp.GetPhysicalAddress().ToString();
                    }
                    else
                    {
                        tmp = lstTmp.Find(p => p.OperationalStatus == OperationalStatus.Up);
                        if (tmp != null && !string.IsNullOrEmpty(tmp.Id))
                        {
                            strMAC = tmp.GetPhysicalAddress().ToString();
                        }
                        else
                        {
                            tmp = lstTmp.Find(p => p.NetworkInterfaceType == NetworkInterfaceType.Ethernet);
                            if (tmp != null && !string.IsNullOrEmpty(tmp.Id))
                            {
                                strMAC = tmp.GetPhysicalAddress().ToString();
                            }
                        }
                        //foreach (NetworkInterface adapter in nics)
                        //{
                        //    //判断是否为以太网卡
                        //    //Wireless80211         无线网卡;    Ppp     宽带连接;Ethernet              以太网卡
                        //    if (adapter.NetworkInterfaceType == NetworkInterfaceType.Ethernet)
                        //    {
                        //        strMAC += adapter.GetPhysicalAddress().ToString();
                        //    }
                        //    //else if (adapter.NetworkInterfaceType == NetworkInterfaceType.Wireless80211)
                        //    //{
                        //    //    //获取以太网卡网络接口信息
                        //    //    IPInterfaceProperties ip = adapter.GetIPProperties();
                        //    //    //获取单播地址集
                        //    //    UnicastIPAddressInformationCollection ipCollection = ip.UnicastAddresses;
                        //    //    foreach (UnicastIPAddressInformation ipadd in ipCollection)
                        //    //    {
                        //    //        //InterNetwork    IPV4地址      InterNetworkV6        IPV6地址
                        //    //        //Max            MAX 位址
                        //    //        if (ipadd.Address.AddressFamily == AddressFamily.InterNetwork)
                        //    //            label2.Text = ipadd.Address.ToString();//获取ip
                        //    //    }
                        //    //}
                        //}
                    }
                }
            }
            catch
            {
            }
            try
            {
                if (string.IsNullOrEmpty(strMAC))
                {
                    using (var MC = new ManagementClass("Win32_NetworkAdapterConfiguration"))
                    {
                        var MOC = MC.GetInstances();
                        foreach (ManagementObject MO in MOC)
                        {
                            if ((bool)MO["IPEnabled"])
                            {
                                strMAC = MO["MACAddress"].ToString();
                                break;
                            }
                        }
                    }
                }
            }
            catch
            {
            }
            return strMAC.Replace(":", "");
        }

        /// <summary>
        ///     主板编号
        /// </summary>
        /// <returns></returns>
        public static string GetBoardID(bool isUseCMD = true)
        {
            var text = string.Empty;
            if (isUseCMD)
            {
                try
                {
                    var result = CommonMethod.ExecCmd("wmic baseboard get serialnumber");
                    result = CommonMethod.SubString(result, "SerialNumber").Trim();
                    text = CommonMethod.SubString(result, "", "\n").Trim();
                    result = result.EndsWith(">exit") ? "" : result;
                }
                catch
                {
                    text = "";
                }
            }
            if (string.IsNullOrEmpty(text))
            {
                try
                {
                    using (var mc = new ManagementClass("Win32_BaseBoard"))
                    {
                        var moc = mc.GetInstances();
                        foreach (ManagementObject mo in moc)
                        {
                            text = mo.Properties["SerialNumber"].Value.ToString();
                            break;
                        }
                    }
                }
                catch
                {
                    text = "";
                }
            }
            if (string.IsNullOrEmpty(text))
                text = GetBoardIDOld();
            return text;
        }

        public static string GetBoardIDOld()
        {
            var text = string.Empty;
            try
            {
                using (var managementObjectSearcher = new ManagementObjectSearcher("select * from Win32_baseboard"))
                {
                    using (var enumerator = managementObjectSearcher.Get().GetEnumerator())
                    {
                        while (enumerator.MoveNext())
                        {
                            var managementObject = (ManagementObject)enumerator.Current;
                            if (string.IsNullOrEmpty(text))
                            {
                                foreach (var current in managementObject.Properties)
                                {
                                    if (current.Name == "Product")
                                    {
                                        text = managementObject["Product"] != null
                                            ? managementObject["Product"].ToString()
                                            : string.Empty;
                                        break;
                                    }
                                }
                            }
                            if (!string.IsNullOrEmpty(text))
                            {
                                break;
                            }
                        }
                    }
                }
            }
            catch
            {
            }
            text = text.Replace(" ", string.Empty);
            text = text.Replace(":", string.Empty);
            if (string.IsNullOrEmpty(text))
            {
                text = GetDiskVolumeSerialNumber();
            }
            return text;
        }

        private static string GetDiskVolumeSerialNumber()
        {
            string result;
            try
            {
                //new ManagementClass("Win32_NetworkAdapterConfiguration");
                using (var managementObject = new ManagementObject("win32_logicaldisk.deviceid=\"c:\""))
                {
                    managementObject.Get();
                    result = managementObject.GetPropertyValue("VolumeSerialNumber").ToString();
                }
            }
            catch
            {
                result = "4C04CE54";
            }
            return result;
        }

        public static string GetAnZhuangDate(bool isUseCMD = true)
        {
            var StrInfo = "";
            if (isUseCMD)
            {
                try
                {
                    var result = CommonMethod.ExecCmd("wmic os get installdate");
                    StrInfo = CommonMethod.SubString(result, "InstallDate", ".").Trim();
                    StrInfo = StrInfo.EndsWith(">exit") ? "" : StrInfo;
                }
                catch
                {
                    StrInfo = "";
                }
            }
            if (string.IsNullOrEmpty(StrInfo))
            {
                try
                {
                    //获取系统日期信息
                    var MyQuery = new ObjectQuery("SELECT * FROM Win32_OperatingSystem");
                    var MyScope = new ManagementScope();
                    using (var MySearch = new ManagementObjectSearcher(MyScope, MyQuery))
                    {
                        var MyCollection = MySearch.Get();
                        foreach (ManagementObject MyObject in MyCollection)
                        {
                            StrInfo = MyObject.GetText(TextFormat.Mof);
                            if (!string.IsNullOrEmpty(StrInfo))
                                break;
                        }
                    }
                    StrInfo = CommonMethod.SubString(StrInfo, "InstallDate = \"", ".");
                }
                catch
                {
                    StrInfo = "";
                }
            }
            //if (string.IsNullOrEmpty(StrInfo))
            //{
            //    StrInfo = GetAnZhuangDateByReg();
            //}
            return StrInfo;
        }

        /// <summary>
        ///     获取系统内存大小
        /// </summary>
        /// <returns>内存大小（单位M）</returns>
        public static long GetPhisicalMemory()
        {
            long capacity = 0;
            try
            {
                var searcher = new ManagementObjectSearcher
                {
                    Query = new SelectQuery("Win32_PhysicalMemory ", "", new[] { "Capacity" }) //设置查询条件 
                }; //用于查询一些如系统信息的管理对象 
                var collection = searcher.Get(); //获取内存容量 
                var em = collection.GetEnumerator();

                while (em.MoveNext())
                {
                    var baseObj = em.Current;
                    if (baseObj.Properties["Capacity"].Value != null)
                    {
                        try
                        {
                            capacity += long.Parse(baseObj.Properties["Capacity"].Value.ToString());
                        }
                        catch
                        {
                            return 0;
                        }
                    }
                }
            }
            catch
            {
            }
            return capacity;
        }

        public static string GetDiskInfo()
        {
            var result = "";
            try
            {
                var nCount = 0;
                foreach (var item in DriveInfo.GetDrives())
                {
                    if (item.DriveType == DriveType.Fixed)
                    {
                        nCount++;
                        result += item.TotalSize;
                        if (item.RootDirectory.Name.StartsWith(CommonString.RootDirectory))
                        {
                            result += CommonString.RootDirectory.Substring(0, 1) + item.TotalSize;
                        }
                    }
                }
                result += nCount;
            }
            catch
            {
                result = "";
            }
            result += GetPhisicalMemory();
            return result;
        }

        public static string GetMichineCode(bool isUseCMD = true)
        {
            #region 虚拟机检测

            //string strTmp = GetComputerName().ToLower();

            //try
            //{
            //    if (strTmp.Contains("vm") || strTmp.Contains("virtual") || strTmp.Contains("hyper"))
            //    {
            //        CommonReg.SetNoReg(true, true);
            //        return "";
            //    }
            //}
            //catch { }
            //try
            //{
            //    strTmp = System.Environment.MachineName.ToLower();
            //    if (strTmp.Contains("vm") || strTmp.Contains("virtual") || strTmp.Contains("hyper"))
            //    {
            //        CommonReg.SetNoReg(true, true);
            //        return "";
            //    }
            //}
            //catch { }
            //try
            //{
            //    strTmp = System.Environment.UserDomainName.ToLower();
            //    if (strTmp.Contains("vm") || strTmp.Contains("virtual") || strTmp.Contains("hyper"))
            //    {
            //        CommonReg.SetNoReg(true, true);
            //        return "";
            //    }
            //}
            //catch { }
            //try
            //{
            //    strTmp = System.Environment.UserName.ToLower();
            //    if (strTmp.Contains("vm") || strTmp.Contains("virtual") || strTmp.Contains("hyper"))
            //    {
            //        CommonReg.SetNoReg(true, true);
            //        return "";
            //    }
            //}
            //catch { }
            //try
            //{
            //    NetworkInterface[] adapters = NetworkInterface.GetAllNetworkInterfaces();
            //    if (adapters != null && adapters.Length > 0)
            //    {
            //        foreach (NetworkInterface adapter in adapters)
            //        {
            //            if (adapter.Name.ToLower().Contains("vm") || adapter.Name.ToLower().Contains("virtual") || adapter.Name.ToLower().Contains("hyper"))
            //            {
            //                CommonReg.SetNoReg(true, true);
            //                return "";
            //            }
            //        }
            //    }
            //}
            //catch { }
            //try
            //{
            //    ManagementObjectSearcher FlashDevice = new ManagementObjectSearcher("select * from win32_VideoController");//声明一个用于检索设备管理信息的对象
            //    foreach (ManagementObject FlashDeviceObject in FlashDevice.Get())//循环遍历WMI实例中的每一个对象
            //    {
            //        try
            //        {
            //            if (FlashDeviceObject["name"].ToString().ToLower().Contains("vm") || FlashDeviceObject["name"].ToString().ToLower().Contains("virtual") || FlashDeviceObject["name"].ToString().ToLower().Contains("hyper"))
            //            {
            //                CommonReg.SetNoReg(true, true);
            //                return "";
            //            }
            //        }
            //        catch { }
            //    }
            //}
            //catch { }

            #endregion

            //DateTime dtConsole = DateTime.Now;
            var otherCode = "";
            var cpuId = GetAnZhuangDate(isUseCMD); // +HardwareUtil.GetCpuId();
            var boardID = GetBoardID(isUseCMD);
            if (string.IsNullOrEmpty(cpuId) ||
                cpuId.Replace(".", "").Replace(" ", "").ToUpper().HorspoolIndex("OEM") > 0)
            {
                cpuId = GetHDid();
            }
            if (string.IsNullOrEmpty(boardID) ||
                boardID.Replace(".", "").Replace(" ", "").ToUpper().HorspoolIndex("OEM") > 0)
            {
                boardID = GetHDid();
            }
            if (string.IsNullOrEmpty(cpuId) ||
                cpuId.Replace(".", "").Replace(" ", "").ToUpper().HorspoolIndex("OEM") > 0)
            {
                cpuId = GetNetCardMAC();
            }
            if (string.IsNullOrEmpty(boardID) ||
                boardID.Replace(".", "").Replace(" ", "").ToUpper().HorspoolIndex("OEM") > 0)
            {
                boardID = GetNetCardMAC();
            }
            cpuId = GetBIOSNumber(isUseCMD) + cpuId;
            boardID = GetNetAdapterNumber(isUseCMD) + boardID;
            cpuId += GetDiskInfo();
            boardID += otherCode + GetNetCardMAC();
            if (!string.IsNullOrEmpty(cpuId))
                cpuId = cpuId.Replace(" ", "").Replace("/", "").Replace(":", "").Replace(" ", "").Trim();
            if (!string.IsNullOrEmpty(boardID))
                boardID = boardID.Replace(" ", "").Replace("/", "").Replace(":", "").Replace(" ", "").Trim();
            //System.Windows.Forms.MessageBox.Show(new TimeSpan(DateTime.Now.Ticks - dtConsole.Ticks).TotalMilliseconds.ToString());
            return GetResultCode(cpuId, boardID);
        }

        public static string GetSysTick()
        {
            var dt = DateTime.Now.AddMilliseconds(-Environment.TickCount);
            return dt.ToString("yyyy-MM-dd HH:mm:ss");
        }

        #region 获取U盘的盘符和序列号

        private static string GetResultCode(string cpuId, string boardID)
        {
            var stringBuilder = new StringBuilder(256);
            try
            {
                stringBuilder.Append(FormsAuthentication.HashPasswordForStoringInConfigFile(cpuId + boardID, "MD5"));
            }
            catch
            {
                try
                {
                    for (var i = 0; i < 30; i++)
                    {
                        if (i % 2 == 0)
                        {
                            var num = i / 2;
                            num = num >= cpuId.Length ? num % cpuId.Length : num;
                            stringBuilder.Append(cpuId[num]);
                        }
                        else
                        {
                            var num = (i + 1) / 2;
                            num = num >= boardID.Length ? num % boardID.Length : num;
                            stringBuilder.Append(boardID[num]);
                        }
                    }
                }
                catch (Exception)
                {
                    //System.Windows.Forms.MessageBox.Show(oe.Message + "\n" + cpuId + "\n" + boardID);
                }
            }
            return stringBuilder.ToString().Replace(" ", "").ToUpper();
        }

        #endregion
    }


    [Serializable]
    public struct HardDiskInfo
    {
        /// <summary>
        ///     型号
        /// </summary>
        public string ModuleNumber;

        /// <summary>
        ///     固件版本
        /// </summary>
        public string Firmware;

        /// <summary>
        ///     序列号
        /// </summary>
        public string SerialNumber;

        /// <summary>
        ///     容量，以M为单位
        /// </summary>
        public uint Capacity;
    }

    #region Internal Structs

    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    internal struct GetVersionOutParams
    {
        public byte bVersion;
        public byte bRevision;
        public byte bReserved;
        public byte bIDEDeviceMap;
        public uint fCapabilities;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 4)] public uint[] dwReserved; // For future use. 
    }

    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    internal struct IdeRegs
    {
        public byte bFeaturesReg;
        public byte bSectorCountReg;
        public byte bSectorNumberReg;
        public byte bCylLowReg;
        public byte bCylHighReg;
        public byte bDriveHeadReg;
        public byte bCommandReg;
        public byte bReserved;
    }

    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    internal struct SendCmdInParams
    {
        public uint cBufferSize;
        public IdeRegs irDriveRegs;
        public byte bDriveNumber;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 3)] public byte[] bReserved;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 4)] public uint[] dwReserved;
        public byte bBuffer;
    }

    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    internal struct DriverStatus
    {
        public byte bDriverError;
        public byte bIDEStatus;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 2)] public byte[] bReserved;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 2)] public uint[] dwReserved;
    }

    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    internal struct SendCmdOutParams
    {
        public uint cBufferSize;
        public DriverStatus DriverStatus;
        public IdSector bBuffer;
    }

    [StructLayout(LayoutKind.Sequential, Pack = 1, Size = 512)]
    internal struct IdSector
    {
        public ushort wGenConfig;
        public ushort wNumCyls;
        public ushort wReserved;
        public ushort wNumHeads;
        public ushort wBytesPerTrack;
        public ushort wBytesPerSector;
        public ushort wSectorsPerTrack;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 3)] public ushort[] wVendorUnique;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 20)] public byte[] sSerialNumber;
        public ushort wBufferType;
        public ushort wBufferSize;
        public ushort wECCSize;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)] public byte[] sFirmwareRev;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 40)] public byte[] sModelNumber;
        public ushort wMoreVendorUnique;
        public ushort wDoubleWordIO;
        public ushort wCapabilities;
        public ushort wReserved1;
        public ushort wPIOTiming;
        public ushort wDMATiming;
        public ushort wBS;
        public ushort wNumCurrentCyls;
        public ushort wNumCurrentHeads;
        public ushort wNumCurrentSectorsPerTrack;
        public uint ulCurrentSectorCapacity;
        public ushort wMultSectorStuff;
        public uint ulTotalAddressableSectors;
        public ushort wSingleWordDMA;
        public ushort wMultiWordDMA;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 128)] public byte[] bReserved;
    }

    #endregion

    /// <summary>
    ///     ATAPI驱动器相关
    /// </summary>
    public class AtapiDevice
    {
        #region DllImport

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern int CloseHandle(IntPtr hObject);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr CreateFile(
            string lpFileName,
            uint dwDesiredAccess,
            uint dwShareMode,
            IntPtr lpSecurityAttributes,
            uint dwCreationDisposition,
            uint dwFlagsAndAttributes,
            IntPtr hTemplateFile);

        [DllImport("kernel32.dll")]
        private static extern int DeviceIoControl(
            IntPtr hDevice,
            uint dwIoControlCode,
            IntPtr lpInBuffer,
            uint nInBufferSize,
            ref GetVersionOutParams lpOutBuffer,
            uint nOutBufferSize,
            ref uint lpBytesReturned,
            [Out] IntPtr lpOverlapped);

        [DllImport("kernel32.dll")]
        private static extern int DeviceIoControl(
            IntPtr hDevice,
            uint dwIoControlCode,
            ref SendCmdInParams lpInBuffer,
            uint nInBufferSize,
            ref SendCmdOutParams lpOutBuffer,
            uint nOutBufferSize,
            ref uint lpBytesReturned,
            [Out] IntPtr lpOverlapped);

        private const uint DFP_GET_VERSION = 0x00074080;
        private const uint DFP_SEND_DRIVE_COMMAND = 0x0007c084;
        private const uint DFP_RECEIVE_DRIVE_DATA = 0x0007c088;

        private const uint GENERIC_READ = 0x80000000;
        private const uint GENERIC_WRITE = 0x40000000;
        private const uint FILE_SHARE_READ = 0x00000001;
        private const uint FILE_SHARE_WRITE = 0x00000002;
        private const uint CREATE_NEW = 1;
        private const uint OPEN_EXISTING = 3;

        #endregion

        #region GetHddInfo

        /// <summary>
        ///     获得硬盘信息
        /// </summary>
        /// <param name="driveIndex">硬盘序号</param>
        /// <returns>硬盘信息</returns>
        /// <remarks>
        ///     参考lu0的文章：http://lu0s1.3322.org/App/2k1103.html
        ///     by sunmast for everyone
        ///     thanks lu0 for his great works
        ///     在Windows 98/ME中，S.M.A.R.T并不缺省安装，请将SMARTVSD.VXD拷贝到%SYSTEM%/IOSUBSYS目录下。
        ///     在Windows 2000/2003下，需要Administrators组的权限。
        /// </remarks>
        /// <example>
        ///     AtapiDevice.GetHddInfo()
        /// </example>
        public static HardDiskInfo GetHddInfo(byte driveIndex)
        {
            switch (Environment.OSVersion.Platform)
            {
                case PlatformID.Win32Windows:
                    return GetHddInfo9x(driveIndex);
                case PlatformID.Win32NT:
                    return GetHddInfoNT(driveIndex);
                case PlatformID.Win32S:
                    throw new NotSupportedException("Win32s is not supported.");
                case PlatformID.WinCE:
                    throw new NotSupportedException("WinCE is not supported.");
                default:
                    throw new NotSupportedException("Unknown Platform.");
            }
        }

        #region GetHddInfo9x

        private static HardDiskInfo GetHddInfo9x(byte driveIndex)
        {
            var vers = new GetVersionOutParams();
            var inParam = new SendCmdInParams();
            var outParam = new SendCmdOutParams();
            uint bytesReturned = 0;

            var hDevice = CreateFile(
                @"//./Smartvsd",
                0,
                0,
                IntPtr.Zero,
                CREATE_NEW,
                0,
                IntPtr.Zero);
            if (hDevice == IntPtr.Zero)
            {
                throw new Exception("Open smartvsd.vxd failed.");
            }
            if (0 == DeviceIoControl(
                hDevice,
                DFP_GET_VERSION,
                IntPtr.Zero,
                0,
                ref vers,
                (uint)Marshal.SizeOf(vers),
                ref bytesReturned,
                IntPtr.Zero))
            {
                CloseHandle(hDevice);
                throw new Exception("DeviceIoControl failed:DFP_GET_VERSION");
            }
            // If IDE identify command not supported, fails 
            if (0 == (vers.fCapabilities & 1))
            {
                CloseHandle(hDevice);
                throw new Exception("Error: IDE identify command not supported.");
            }
            if (0 != (driveIndex & 1))
            {
                inParam.irDriveRegs.bDriveHeadReg = 0xb0;
            }
            else
            {
                inParam.irDriveRegs.bDriveHeadReg = 0xa0;
            }
            if (0 != (vers.fCapabilities & (16 >> driveIndex)))
            {
                // We don't detect a ATAPI device. 
                CloseHandle(hDevice);
                throw new Exception(string.Format("Drive {0} is a ATAPI device, we don't detect it", driveIndex + 1));
            }
            inParam.irDriveRegs.bCommandReg = 0xec;
            inParam.bDriveNumber = driveIndex;
            inParam.irDriveRegs.bSectorCountReg = 1;
            inParam.irDriveRegs.bSectorNumberReg = 1;
            inParam.cBufferSize = 512;
            if (0 == DeviceIoControl(
                hDevice,
                DFP_RECEIVE_DRIVE_DATA,
                ref inParam,
                (uint)Marshal.SizeOf(inParam),
                ref outParam,
                (uint)Marshal.SizeOf(outParam),
                ref bytesReturned,
                IntPtr.Zero))
            {
                CloseHandle(hDevice);
                throw new Exception("DeviceIoControl failed: DFP_RECEIVE_DRIVE_DATA");
            }
            CloseHandle(hDevice);

            return GetHardDiskInfo(outParam.bBuffer);
        }

        #endregion

        #region GetHddInfoNT

        private static HardDiskInfo GetHddInfoNT(byte driveIndex)
        {
            var vers = new GetVersionOutParams();
            var inParam = new SendCmdInParams();
            var outParam = new SendCmdOutParams();
            uint bytesReturned = 0;

            // We start in NT/Win2000 
            var hDevice = CreateFile(
                string.Format(@"//./PhysicalDrive{0}", driveIndex),
                GENERIC_READ | GENERIC_WRITE,
                FILE_SHARE_READ | FILE_SHARE_WRITE,
                IntPtr.Zero,
                OPEN_EXISTING,
                0,
                IntPtr.Zero);
            if (hDevice == IntPtr.Zero)
            {
                throw new Exception("CreateFile faild.");
            }
            if (0 == DeviceIoControl(
                hDevice,
                DFP_GET_VERSION,
                IntPtr.Zero,
                0,
                ref vers,
                (uint)Marshal.SizeOf(vers),
                ref bytesReturned,
                IntPtr.Zero))
            {
                CloseHandle(hDevice);
                throw new Exception(string.Format("Drive {0} may not exists.", driveIndex + 1));
            }
            // If IDE identify command not supported, fails 
            if (0 == (vers.fCapabilities & 1))
            {
                CloseHandle(hDevice);
                throw new Exception("Error: IDE identify command not supported.");
            }
            // Identify the IDE drives 
            if (0 != (driveIndex & 1))
            {
                inParam.irDriveRegs.bDriveHeadReg = 0xb0;
            }
            else
            {
                inParam.irDriveRegs.bDriveHeadReg = 0xa0;
            }
            if (0 != (vers.fCapabilities & (16 >> driveIndex)))
            {
                // We don't detect a ATAPI device. 
                CloseHandle(hDevice);
                throw new Exception(string.Format("Drive {0} is a ATAPI device, we don't detect it.", driveIndex + 1));
            }
            inParam.irDriveRegs.bCommandReg = 0xec;
            inParam.bDriveNumber = driveIndex;
            inParam.irDriveRegs.bSectorCountReg = 1;
            inParam.irDriveRegs.bSectorNumberReg = 1;
            inParam.cBufferSize = 512;

            if (0 == DeviceIoControl(
                hDevice,
                DFP_RECEIVE_DRIVE_DATA,
                ref inParam,
                (uint)Marshal.SizeOf(inParam),
                ref outParam,
                (uint)Marshal.SizeOf(outParam),
                ref bytesReturned,
                IntPtr.Zero))
            {
                CloseHandle(hDevice);
                throw new Exception("DeviceIoControl failed: DFP_RECEIVE_DRIVE_DATA");
            }
            CloseHandle(hDevice);

            return GetHardDiskInfo(outParam.bBuffer);
        }

        #endregion

        private static HardDiskInfo GetHardDiskInfo(IdSector phdinfo)
        {
            var hddInfo = new HardDiskInfo();

            ChangeByteOrder(phdinfo.sModelNumber);
            hddInfo.ModuleNumber = Encoding.ASCII.GetString(phdinfo.sModelNumber).Trim();

            ChangeByteOrder(phdinfo.sFirmwareRev);
            hddInfo.Firmware = Encoding.ASCII.GetString(phdinfo.sFirmwareRev).Trim();

            ChangeByteOrder(phdinfo.sSerialNumber);
            hddInfo.SerialNumber = Encoding.ASCII.GetString(phdinfo.sSerialNumber).Trim();

            hddInfo.Capacity = phdinfo.ulTotalAddressableSectors / 2 / 1024;

            return hddInfo;
        }

        private static void ChangeByteOrder(byte[] charArray)
        {
            byte temp;
            for (var i = 0; i < charArray.Length; i += 2)
            {
                temp = charArray[i];
                charArray[i] = charArray[i + 1];
                charArray[i + 1] = temp;
            }
        }

        #endregion
    }
}