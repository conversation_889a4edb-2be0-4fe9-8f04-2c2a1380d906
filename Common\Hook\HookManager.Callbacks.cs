using System;
using System.ComponentModel;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace OCRTools.Common.Hook
{
    public static partial class HookManager
    {
        /// <summary>
        ///     The CallWndProc hook procedure is an application-defined or library-defined callback
        ///     function used with the SetWindowsHookEx function. The HOOKPROC type defines a pointer
        ///     to this callback function. CallWndProc is a placeholder for the application-defined
        ///     or library-defined function name.
        /// </summary>
        /// <param name="nCode">
        ///     [in] Specifies whether the hook procedure must process the message.
        ///     If nCode is HC_ACTION, the hook procedure must process the message.
        ///     If nCode is less than zero, the hook procedure must pass the message to the
        ///     CallNextHookEx function without further processing and must return the
        ///     value returned by CallNextHookEx.
        /// </param>
        /// <param name="wParam">
        ///     [in] Specifies whether the message was sent by the current thread.
        ///     If the message was sent by the current thread, it is nonzero; otherwise, it is zero.
        /// </param>
        /// <param name="lParam">
        ///     [in] Pointer to a CWPSTRUCT structure that contains details about the message.
        /// </param>
        /// <returns>
        ///     If nCode is less than zero, the hook procedure must return the value returned by CallNextHookEx.
        ///     If nCode is greater than or equal to zero, it is highly recommended that you call CallNextHookEx
        ///     and return the value it returns; otherwise, other applications that have installed WH_CALLWNDPROC
        ///     hooks will not receive hook notifications and may behave incorrectly as a result. If the hook
        ///     procedure does not call CallNextHookEx, the return value should be zero.
        /// </returns>
        /// <remarks>
        ///     http://msdn.microsoft.com/library/default.asp?url=/library/en-us/winui/winui/windowsuserinterface/windowing/hooks/hookreference/hookfunctions/callwndproc.asp
        /// </remarks>
        private delegate int HookProc(int nCode, int wParam, IntPtr lParam);

        //##############################################################################

        #region Mouse hook processing

        /// <summary>
        ///     This field is not objectively needed but we need to keep a reference on a delegate which will be
        ///     passed to unmanaged code. To avoid GC to clean it up.
        ///     When passing delegates to unmanaged code, they must be kept alive by the managed application
        ///     until it is guaranteed that they will never be called.
        /// </summary>
        private static HookProc _sMouseDelegate;

        /// <summary>
        ///     Stores the handle to the mouse hook procedure.
        /// </summary>
        private static int _sMouseHookHandle;

        private static bool _isDown;
        private static bool _isMove;

        private static int _moveCount;

        /// <summary>
        ///     A callback function which will be called every Time a mouse activity detected.
        /// </summary>
        /// <param name="nCode">
        ///     [in] Specifies whether the hook procedure must process the message.
        ///     If nCode is HC_ACTION, the hook procedure must process the message.
        ///     If nCode is less than zero, the hook procedure must pass the message to the
        ///     CallNextHookEx function without further processing and must return the
        ///     value returned by CallNextHookEx.
        /// </param>
        /// <param name="wParam">
        ///     [in] Specifies whether the message was sent by the current thread.
        ///     If the message was sent by the current thread, it is nonzero; otherwise, it is zero.
        /// </param>
        /// <param name="lParam">
        ///     [in] Pointer to a CWPSTRUCT structure that contains details about the message.
        /// </param>
        /// <returns>
        ///     If nCode is less than zero, the hook procedure must return the value returned by CallNextHookEx.
        ///     If nCode is greater than or equal to zero, it is highly recommended that you call CallNextHookEx
        ///     and return the value it returns; otherwise, other applications that have installed WH_CALLWNDPROC
        ///     hooks will not receive hook notifications and may behave incorrectly as a result. If the hook
        ///     procedure does not call CallNextHookEx, the return value should be zero.
        /// </returns>
        private static int MouseHookProc(int nCode, int wParam, IntPtr lParam)
        {
            if (nCode >= 0 && !CommonString.IsExit && !StaticValue.IsCatchScreen && !CheckIsOnMySelf())
            {
                //Marshall the data from callback.
                var mouseHookStruct = (MouseLLHookStruct)Marshal.PtrToStructure(lParam, typeof(MouseLLHookStruct));

                //detect button clicked
                var button = MouseButtons.None;
                short mouseDelta = 0;
                var clickCount = 0;
                var mouseUp = false;
                var mouseSelected = false;

                switch (wParam)
                {
                    case WM_LBUTTONDOWN:
                        button = MouseButtons.Left;
                        clickCount = 1;
                        _isDown = true;
                        _isMove = false;
                        _moveCount = 0;
                        break;
                    case WM_MOUSEMOVE:
                        _isMove = _isDown;
                        if (_isMove) _moveCount++;
                        break;
                    case WM_LBUTTONUP:
                        mouseUp = true;
                        button = MouseButtons.Left;
                        clickCount = 1;
                        mouseSelected = _isDown && _isMove && _moveCount > 10;
                        _moveCount = 0;
                        _isMove = false;
                        _isDown = false;
                        break;
                    case WM_LBUTTONDBLCLK:
                        button = MouseButtons.Left;
                        clickCount = 2;
                        break;
                    case WM_RBUTTONDOWN:
                        button = MouseButtons.Right;
                        clickCount = 1;
                        break;
                    case WM_RBUTTONUP:
                        mouseUp = true;
                        button = MouseButtons.Right;
                        clickCount = 1;
                        break;
                    case WM_RBUTTONDBLCLK:
                        button = MouseButtons.Right;
                        clickCount = 2;
                        break;
                    case WM_MOUSEWHEEL:
                        //If the message is WM_MOUSEWHEEL, the high-order word of MouseData member is the wheel delta. 
                        //One wheel click is defined as WHEEL_DELTA, which is 120. 
                        //(value >> 16) & 0xffff; retrieves the high-order word from the given 32-bit value
                        mouseDelta = (short)((mouseHookStruct.MouseData >> 16) & 0xffff);

                        //TODO: X BUTTONS (I havent them so was unable to test)
                        //If the message is WM_XBUTTONDOWN, WM_XBUTTONUP, WM_XBUTTONDBLCLK, WM_NCXBUTTONDOWN, WM_NCXBUTTONUP, 
                        //or WM_NCXBUTTONDBLCLK, the high-order word specifies which X button was pressed or released, 
                        //and the low-order word is reserved. This value can be one or more of the following values. 
                        //Otherwise, MouseData is not used. 
                        break;
                }

                #region MouseEvent

                //Mouse up
                if (mouseUp)
                    if (MouseUp != null)
                        try
                        {
                            //generate event 
                            var e = new MouseEventExtArgs(
                                button,
                                clickCount,
                                mouseHookStruct.Point.X,
                                mouseHookStruct.Point.Y,
                                mouseDelta);
                            MouseUp?.Invoke(null, e);
                        }
                        catch
                        {
                        }

                if (mouseSelected)
                    if (MouseSelected != null)
                        try
                        {
                            //generate event 
                            var e = new MouseEventExtArgs(
                                button,
                                clickCount,
                                mouseHookStruct.Point.X,
                                mouseHookStruct.Point.Y,
                                mouseDelta);
                            Console.WriteLine("MouseSelected");
                            MouseSelected?.Invoke(null, e);
                        }
                        catch
                        {
                        }

                ////If someone listens to move and there was a change in coordinates raise move event
                //if (mouseMove)
                //{
                //    try
                //    {
                //        MouseMove?.Invoke(null, e);
                //    }
                //    catch { }
                //}

                ////Mouse down
                //if (mouseDown)
                //{
                //    try
                //    {
                //        MouseDown?.Invoke(null, e);
                //    }
                //    catch { }
                //}

                ////If someone listens to click and a click is heppened
                //if (clickCount > 0)
                //{
                //    try
                //    {
                //        MouseClick?.Invoke(null, e);
                //    }
                //    catch { }
                //}

                //////If someone listens to double click and a click is heppened
                ////if (clickCount == 2)
                ////{
                ////    s_MouseDoubleClick?.Invoke(null, e);
                ////}

                ////Wheel was moved
                //if (MouseWheel != null && mouseDelta != 0)
                //{
                //    try
                //    {
                //        MouseWheel?.Invoke(null, e);
                //    }
                //    catch { }
                //}

                #endregion

                //if (e.Handled)
                //{
                //    return -1;
                //}
            }

            //call next hook
            return CallNextHookEx(_sMouseHookHandle, nCode, wParam, lParam);
        }

        private static bool CheckIsOnMySelf()
        {
            var isOnSelf = false;
            try
            {
                foreach (Form item in Application.OpenForms)
                    if (item.Visible && item.Bounds.Contains(Cursor.Position))
                    {
                        if (!(item is FormOcr || item is FormTool || item is FrmSearch) && !item.Focused) continue;
                        isOnSelf = true;
                        break;
                    }
            }
            catch
            {
            }

            return isOnSelf;
        }

        public static void SubscribedToGlobalMouseEvents()
        {
            // install Mouse hook only if it is not installed and must be installed
            if (_sMouseHookHandle == 0)
                try
                {
                    //See comment of this field. To avoid GC to clean it up.
                    _sMouseDelegate = MouseHookProc;
                    //install hook
                    _sMouseHookHandle = SetWindowsHookEx(
                        WH_MOUSE_LL,
                        _sMouseDelegate,
                        GetModuleHandle("user32"),
                        0);
                    //If SetWindowsHookEx fails.
                    if (_sMouseHookHandle == 0)
                    {
                        //Returns the error code returned by the last unmanaged function called using platform invoke that has the DllImportAttribute.SetLastError flag set. 
                        var errorCode = Marshal.GetLastWin32Error();
                        //do cleanup

                        //Initializes and throws a new instance of the Win32Exception class with the specified error. 
                        throw new Win32Exception(errorCode);
                    }
                }
                catch
                {
                }
        }

        public static void UnsunscribeFromGlobalMouseEvents()
        {
            if (_sMouseHookHandle != 0)
                try
                {
                    //uninstall hook
                    var result = UnhookWindowsHookEx(_sMouseHookHandle);
                    //reset invalid handle
                    _sMouseHookHandle = 0;
                    //Free up for GC
                    _sMouseDelegate = null;
                    //if failed and exception must be thrown
                    if (result == 0)
                    {
                        //Returns the error code returned by the last unmanaged function called using platform invoke that has the DllImportAttribute.SetLastError flag set. 
                        var errorCode = Marshal.GetLastWin32Error();
                        //Initializes and throws a new instance of the Win32Exception class with the specified error. 
                        throw new Win32Exception(errorCode);
                    }
                }
                catch
                {
                }
        }

        #endregion

        //##############################################################################

        #region Keyboard hook processing

        ///// <summary>
        /////     This field is not objectively needed but we need to keep a reference on a delegate which will be
        /////     passed to unmanaged code. To avoid GC to clean it up.
        /////     When passing delegates to unmanaged code, they must be kept alive by the managed application
        /////     until it is guaranteed that they will never be called.
        ///// </summary>
        //private static HookProc _sKeyboardDelegate;

        ///// <summary>
        /////     Stores the handle to the Keyboard hook procedure.
        ///// </summary>
        //private static int _sKeyboardHookHandle;

        ///// <summary>
        /////     A callback function which will be called every Time a keyboard activity detected.
        ///// </summary>
        ///// <param name="nCode">
        /////     [in] Specifies whether the hook procedure must process the message.
        /////     If nCode is HC_ACTION, the hook procedure must process the message.
        /////     If nCode is less than zero, the hook procedure must pass the message to the
        /////     CallNextHookEx function without further processing and must return the
        /////     value returned by CallNextHookEx.
        ///// </param>
        ///// <param name="wParam">
        /////     [in] Specifies whether the message was sent by the current thread.
        /////     If the message was sent by the current thread, it is nonzero; otherwise, it is zero.
        ///// </param>
        ///// <param name="lParam">
        /////     [in] Pointer to a CWPSTRUCT structure that contains details about the message.
        ///// </param>
        ///// <returns>
        /////     If nCode is less than zero, the hook procedure must return the value returned by CallNextHookEx.
        /////     If nCode is greater than or equal to zero, it is highly recommended that you call CallNextHookEx
        /////     and return the value it returns; otherwise, other applications that have installed WH_CALLWNDPROC
        /////     hooks will not receive hook notifications and may behave incorrectly as a result. If the hook
        /////     procedure does not call CallNextHookEx, the return value should be zero.
        ///// </returns>
        //private static int KeyboardHookProc(int nCode, int wParam, IntPtr lParam)
        //{
        //    //indicates if any of underlaing events set e.Handled flag
        //    var handled = false;

        //    if (nCode >= 0)
        //    {
        //        //read structure KeyboardHookStruct at lParam
        //        var myKeyboardHookStruct =
        //            (KeyboardHookStruct) Marshal.PtrToStructure(lParam, typeof(KeyboardHookStruct));
        //        //raise KeyDown
        //        if (KeyDown != null && (wParam == WM_KEYDOWN || wParam == WM_SYSKEYDOWN))
        //        {
        //            var keyData = (Keys) myKeyboardHookStruct.VirtualKeyCode;
        //            var e = new KeyEventArgs(keyData);
        //            KeyDown?.Invoke(null, e);
        //            handled = e.Handled;
        //        }

        //        // raise KeyPress
        //        if (KeyPress != null && wParam == WM_KEYDOWN)
        //        {
        //            var isDownShift = (GetKeyState(VK_SHIFT) & 0x80) == 0x80;
        //            var isDownCapslock = GetKeyState(VK_CAPITAL) != 0;

        //            var keyState = new byte[256];
        //            GetKeyboardState(keyState);
        //            var inBuffer = new byte[2];
        //            if (ToAscii(myKeyboardHookStruct.VirtualKeyCode,
        //                myKeyboardHookStruct.ScanCode,
        //                keyState,
        //                inBuffer,
        //                myKeyboardHookStruct.Flags) == 1)
        //            {
        //                var key = (char) inBuffer[0];
        //                if (isDownCapslock ^ isDownShift && char.IsLetter(key)) key = char.ToUpper(key);
        //                var e = new KeyPressEventArgs(key);
        //                KeyPress?.Invoke(null, e);
        //                handled = handled || e.Handled;
        //            }
        //        }

        //        // raise KeyUp
        //        if (KeyUp != null && (wParam == WM_KEYUP || wParam == WM_SYSKEYUP))
        //        {
        //            var keyData = (Keys) myKeyboardHookStruct.VirtualKeyCode;
        //            var e = new KeyEventArgs(keyData);
        //            KeyUp?.Invoke(null, e);
        //            handled = handled || e.Handled;
        //        }
        //    }

        //    //if event handled in application do not handoff to other listeners
        //    if (handled)
        //        return -1;

        //    //forward to other application
        //    return CallNextHookEx(_sKeyboardHookHandle, nCode, wParam, lParam);
        //}

        //public static void SubscribedToGlobalKeyboardEvents()
        //{
        //    // install Keyboard hook only if it is not installed and must be installed
        //    if (_sKeyboardHookHandle == 0)
        //    {
        //        //See comment of this field. To avoid GC to clean it up.
        //        _sKeyboardDelegate = KeyboardHookProc;
        //        //install hook
        //        _sKeyboardHookHandle = SetWindowsHookEx(
        //            WH_KEYBOARD_LL,
        //            _sKeyboardDelegate,
        //            Marshal.GetHINSTANCE(
        //                Assembly.GetExecutingAssembly().GetModules()[0]),
        //            0);
        //        //If SetWindowsHookEx fails.
        //        if (_sKeyboardHookHandle == 0)
        //        {
        //            //Returns the error code returned by the last unmanaged function called using platform invoke that has the DllImportAttribute.SetLastError flag set. 
        //            var errorCode = Marshal.GetLastWin32Error();
        //            //do cleanup

        //            //Initializes and throws a new instance of the Win32Exception class with the specified error. 
        //            throw new Win32Exception(errorCode);
        //        }
        //    }
        //}

        //public static void UnsunscribeFromGlobalKeyboardEvents()
        //{
        //    if (_sKeyboardHookHandle != 0)
        //    {
        //        //uninstall hook
        //        var result = UnhookWindowsHookEx(_sKeyboardHookHandle);
        //        //reset invalid handle
        //        _sKeyboardHookHandle = 0;
        //        //Free up for GC
        //        _sKeyboardDelegate = null;
        //        //if failed and exception must be thrown
        //        if (result == 0)
        //        {
        //            //Returns the error code returned by the last unmanaged function called using platform invoke that has the DllImportAttribute.SetLastError flag set. 
        //            var errorCode = Marshal.GetLastWin32Error();
        //            //Initializes and throws a new instance of the Win32Exception class with the specified error. 
        //            throw new Win32Exception(errorCode);
        //        }
        //    }
        //}

        #endregion
    }
}