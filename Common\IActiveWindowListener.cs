﻿using System;
using System.Diagnostics;

namespace OCRTools.Common
{
    public sealed class ActiveWindowListener : IActiveWindowListener, IDisposable
    {
        private readonly IntPtr activeWindowChangedHook;

        private readonly int currentProcessId;

        private readonly NativeMethods.WinEventDelegate windowChangedEventDelegate;

        private ActiveWindow currentWindow;

        private bool isDisposed;

        public IntPtr ActiveWindowHandle => currentWindow.WindowHandle;

        public int ActiveWindowProcessId => currentWindow.ActiveWindowProcessId;

        public bool IsAppActive => currentWindow.IsOwnWindow;

        public event EventHandler<ActiveWindow> ActiveWindowChanged = delegate
        {
        };

        public ActiveWindowListener()
        {
            using (Process process = Process.GetCurrentProcess())
            {
                currentProcessId = process.Id;
            }
            currentWindow = new ActiveWindow(IntPtr.Zero, currentProcessId, isOwnWindow: true);
            windowChangedEventDelegate = WindowChangedEventProc;
            activeWindowChangedHook = NativeMethods.ListenActiveWindowChanged(windowChangedEventDelegate);
        }

        private void WindowChangedEventProc(IntPtr hWinEventHook, uint eventType, IntPtr hwnd, int idObject, int idChild, uint dwEventThread, uint dwmsEventTime)
        {
            NativeMethods.GetWindowThreadProcessId(hwnd, out uint processId);
            currentWindow = new ActiveWindow(hwnd, (int)processId, processId == currentProcessId);
            this.ActiveWindowChanged(this, currentWindow);
        }

        ~ActiveWindowListener()
        {
            Dispose(disposeManagedMembers: false);
        }

        public void Dispose()
        {
            try
            {
                Dispose(disposeManagedMembers: true);
            }
            finally
            {
                GC.SuppressFinalize(this);
            }
        }

        private void Dispose(bool disposeManagedMembers)
        {
            if (!isDisposed)
            {
                isDisposed = true;
                NativeMethods.UnhookActiveWindowChanged(activeWindowChangedHook);
            }
        }
    }
    public interface IActiveWindowListener : IDisposable
    {
        IntPtr ActiveWindowHandle
        {
            get;
        }

        int ActiveWindowProcessId
        {
            get;
        }

        bool IsAppActive
        {
            get;
        }

        event EventHandler<ActiveWindow> ActiveWindowChanged;
    }
    public sealed class ActiveWindow : EventArgs
    {
        public IntPtr WindowHandle
        {
            get;
        }

        public bool IsOwnWindow
        {
            get;
        }

        public int ActiveWindowProcessId
        {
            get;
        }

        public ActiveWindow(IntPtr windowHandle, int activeWindowProcessId, bool isOwnWindow)
        {
            WindowHandle = windowHandle;
            ActiveWindowProcessId = activeWindowProcessId;
            IsOwnWindow = isOwnWindow;
        }
    }
}
