﻿namespace OCRTools
{
    public static class InputHelpers
    {
        public static bool SendKeyPress(VirtualKeyCode keyCode, int times = 1)
        {
            var inputManager = new InputManager() { AutoClearAfterSend = true };
            for (int i = 0; i < times; i++)
            {
                inputManager.AddKeyPress(keyCode);
            }
            return inputManager.SendInputs();
        }

        public static bool SendMouseWheel(int delta)
        {
            var inputManager = new InputManager();
            inputManager.AddMouseWheel(delta);
            return inputManager.SendInputs();
        }
    }
}