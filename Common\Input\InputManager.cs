﻿using OCRTools.Common;
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;

namespace OCRTools
{
    public struct INPUT
    {
        public InputType Type;
        public InputUnion Data;
    }

    public enum InputType
    {
        InputMouse,
        InputKeyboard
    }

    [StructLayout(LayoutKind.Explicit)]
    public struct InputUnion
    {
        [FieldOffset(0)] public MOUSEINPUT Mouse;

        [FieldOffset(0)] public KEYBDINPUT Keyboard;

        [FieldOffset(0)] public HARDWAREINPUT Hardware;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct MOUSEINPUT
    {
        public int dx;
        public int dy;
        public uint mouseData;
        public MouseEventFlags dwFlags;
        public uint time;
        public IntPtr dwExtraInfo;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct KEYBDINPUT
    {
        public VirtualKeyCode wVk;
        public ushort wScan;
        public KeyboardEventFlags dwFlags;
        public uint time;
        public IntPtr dwExtraInfo;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct HARDWAREINPUT
    {
        public int uMsg;
        public short wParamL;
        public short wParamH;
    }

    [Flags]
    public enum MouseEventFlags : uint
    {
        MOUSEEVENTF_MOVE = 0x0001,
        MOUSEEVENTF_LEFTDOWN = 0x0002,
        MOUSEEVENTF_LEFTUP = 0x0004,
        MOUSEEVENTF_RIGHTDOWN = 0x0008,
        MOUSEEVENTF_RIGHTUP = 0x0010,
        MOUSEEVENTF_MIDDLEDOWN = 0x0020,
        MOUSEEVENTF_MIDDLEUP = 0x0040,
        MOUSEEVENTF_XDOWN = 0x0080,
        MOUSEEVENTF_XUP = 0x0100,
        MOUSEEVENTF_WHEEL = 0x0800,
        MOUSEEVENTF_VIRTUALDESK = 0x4000,
        MOUSEEVENTF_ABSOLUTE = 0x8000
    }

    [Flags]
    public enum KeyboardEventFlags : uint
    {
        KEYEVENTF_EXTENDEDKEY = 0x0001,
        KEYEVENTF_KEYUP = 0x0002,
        KEYEVENTF_UNICODE = 0x0004,
        KEYEVENTF_SCANCODE = 0x0008
    }

    public class InputManager
    {
        public List<INPUT> InputList { get; } = new List<INPUT>();

        public bool AutoClearAfterSend { get; set; }

        public bool SendInputs()
        {
            var inputList = InputList.ToArray();
            var len = (uint)inputList.Length;
            var successfulInputs = NativeMethods.SendInput(len, inputList, Marshal.SizeOf(typeof(INPUT)));
            if (AutoClearAfterSend) ClearInputs();
            return successfulInputs == len;
        }

        public void ClearInputs()
        {
            InputList.Clear();
        }

        private void AddKeyInput(VirtualKeyCode keyCode, bool isKeyUp)
        {
            var input = new INPUT { Type = InputType.InputKeyboard, Data = { Keyboard = new KEYBDINPUT { wVk = keyCode } } };
            if (isKeyUp) input.Data.Keyboard.dwFlags = KeyboardEventFlags.KEYEVENTF_KEYUP;
            InputList.Add(input);
        }

        public void AddKeyPress(VirtualKeyCode keyCode)
        {
            AddKeyInput(keyCode, false);
            AddKeyInput(keyCode, true);
        }

        public void AddMouseWheel(int delta)
        {
            var input = new INPUT
            {
                Type = InputType.InputMouse,
                Data =
                {
                    Mouse = new MOUSEINPUT
                    {
                        dwFlags = MouseEventFlags.MOUSEEVENTF_WHEEL, mouseData = (uint) delta
                    }
                }
            };
            InputList.Add(input);
        }
    }
}