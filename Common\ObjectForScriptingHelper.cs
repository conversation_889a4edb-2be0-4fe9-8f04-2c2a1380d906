﻿using System.Runtime.InteropServices;

namespace OCRTools.Common
{
    [ComVisible(true)]
    public class ObjectForScriptingHelper
    {
        private FrmMain _form;

        public ObjectForScriptingHelper(FrmMain main)
        {
            _form = main;
        }

        public void VoiceStoped()
        {
            _form.VoiceStateChange();
        }

        public void VoiceStart()
        {
            _form.VoiceStateChange(true);
        }
    }
}