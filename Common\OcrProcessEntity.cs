﻿using OCRTools.Common;
using System.Collections.Generic;
using System.Linq;

namespace OCRTools
{
    public class OcrProcessEntity
    {
        public byte[] Byts { get; set; }
        internal OcrType OcrType { get; set; }
        internal List<int> GroupType { get; set; }
        public string FileExt { get; set; }

        #region 排版相关

        /// <summary>
        /// 是否自动识别文本方向
        /// </summary>
        public bool IsAutoDirectionDetector { get; set; } = true;

        /// <summary>
        /// 排版方向（1：竖版，2：横版）
        /// </summary>
        public int LayoutDirection { get; set; }

        public bool IsFromLeftToRight { get; set; }

        public bool IsFromTopToDown { get; set; }

        #endregion

        internal TransLanguageTypeEnum From { get; set; }
        internal TransLanguageTypeEnum To { get; set; }
        public string ImgUrl { get; set; }
        public int? ProcessId { get; set; }

        public bool IsImgUrl => !string.IsNullOrEmpty(ImgUrl);
        public bool IsNeedUpload { get; set; }

        public bool IsShowLoading { get; set; }
        public bool IsSearch { get; set; }

        public ProcessBy ProcessBy { get; set; }
        public string Identity { get; set; }

        public bool IsLocal { get; set; }

        public int LocalOcrType { get; set; }

        public bool? IsSupportVertical { get; set; }
        public bool IsLongImage { get; set; }

        public bool AddToPool { get; set; } = true;
    }
}