﻿using System;
using System.Text;

namespace OCRTools
{
    /// <summary>
    ///     非对称RSA加密类 可以参考
    ///     http://www.cnblogs.com/hhh/archive/2011/06/03/2070692.html
    ///     http://blog.csdn.net/zhilunchen/article/details/2943158
    ///     若是私匙加密 则需公钥解密
    ///     反正公钥加密 私匙来解密
    ///     需要BigInteger类来辅助
    /// </summary>
    public static class RSAHelper
    {
        /// <summary>
        ///     RSA的容器 可以解密的源字符串长度为 DWKEYSIZE/8-11
        /// </summary>
        public const int DWKEYSIZE = 1024;

        #region 检查明文的有效性 DWKEYSIZE/8-11 长度之内为有效 中英文都算一个字符

        /// <summary>
        ///     检查明文的有效性 DWKEYSIZE/8-11 长度之内为有效 中英文都算一个字符
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        public static bool CheckSourceValidate(string source)
        {
            return DWKEYSIZE / 8 - 11 >= source.Length;
        }

        #endregion

        #region 组合解析密匙

        /// <summary>
        ///     解析密匙
        /// </summary>
        /// <param name="key">密匙</param>
        /// <param name="b1">RSA的相应参数1</param>
        /// <param name="b2">RSA的相应参数2</param>
        private static void ResolveKey(string key, out byte[] b1, out byte[] b2)
        {
            //从base64字符串 解析成原来的字节数组
            var b = Convert.FromBase64String(key);
            //初始化参数的数组长度
            b1 = new byte[b[0]];
            b2 = new byte[b.Length - b[0] - 1];
            //将相应位置是值放进相应的数组
            for (int n = 1, i = 0, j = 0; n < b.Length; n++)
            {
                if (n <= b[0])
                {
                    b1[i++] = b[n];
                }
                else
                {
                    b2[j++] = b[n];
                }
            }
        }

        #endregion/// <summary>

        /// Base64加密，解密方法
        /// </summary>
        /// <paramname="s">
        ///     输入字符串</param>
        ///     <paramname="c">true-加密,false-解密</param>
        public static string base64(string s, bool c)
        {
            try
            {
                if (c)
                {
                    return Convert.ToBase64String(Encoding.Default.GetBytes(s));
                }
                return Encoding.Default.GetString(Convert.FromBase64String(s));
            }
            catch
            {
                return s;
            }
        }

        #region 字符串加密解密 私有  实现加解密的实现方法

        /// <summary>
        ///     用指定的密匙加密
        /// </summary>
        /// <param name="source">密文</param>
        /// <param name="e">可以是RSACryptoServiceProvider生成的Exponent</param>
        /// <param name="n">可以是RSACryptoServiceProvider生成的Modulus</param>
        /// <returns>返回明文</returns>
        private static string DecryptString(string encryptString, BigInteger e, BigInteger n)
        {
            var result = new StringBuilder(256);
            var strarr1 = encryptString.Split(new[] { '@' }, StringSplitOptions.RemoveEmptyEntries);
            for (var i = 0; i < strarr1.Length; i++)
            {
                var block = strarr1[i];
                var biText = new BigInteger(block, 16);
                var biEnText = biText.modPow(e, n);
                var temp = Encoding.Default.GetString(biEnText.getBytes());
                result.Append(temp);
            }
            return result.ToString();
        }

        #endregion

        /// <summary>
        ///     RSA加密的密匙结构  公钥和私匙
        /// </summary>
        public struct RSAKey
        {
            public string PublicKey { get; set; }
            public string PrivateKey { get; set; }
        }

        #region 字符串加密解密 公开方法

        #endregion
    }
}