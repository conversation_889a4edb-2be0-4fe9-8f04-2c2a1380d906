﻿using System;
using System.IO;
using System.Reflection;

namespace OCRTools.Common
{
    /// <summary>
    ///     快捷方式帮助类
    /// </summary>
    public static class ShortcutHelper
    {
        /// <summary>
        ///     windows styles
        /// </summary>
        public enum ShortcutWindowStyles
        {
            /// <summary>
            ///     Hide
            /// </summary>
            WshHide = 0,

            /// <summary>
            ///     NormalFocus
            /// </summary>
            WshNormalFocus = 1,

            /// <summary>
            ///     MinimizedFocus
            /// </summary>
            WshMinimizedFocus = 2,

            /// <summary>
            ///     MaximizedFocus
            /// </summary>
            WshMaximizedFocus = 3,

            /// <summary>
            ///     NormalNoFocus
            /// </summary>
            WshNormalNoFocus = 4,

            /// <summary>
            ///     MinimizedNoFocus
            /// </summary>
            WshMinimizedNoFocus = 6
        }

        /// <summary>
        ///     检测并创建快捷方式
        /// </summary>
        /// <param name="lnkName">快捷方式名称，不包含(.lnk)</param>
        /// <param name="exePath">目标程序名，带完整路径</param>
        /// <param name="description">快捷方式描述</param>
        public static void CheckAndCreatShortcur(string lnkName, string exePath, string description = "")
        {
            var linkFile = string.Format("{0}\\{1}{2}",
                Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory), lnkName,
                DEFAULT_SHORTCUT_EXTENSION);
            var result = File.Exists(linkFile);
            if (!result) result = CreateShortcut(linkFile, exePath, Path.GetDirectoryName(exePath), description);
            if (result)
            {
                linkFile = string.Format("{0}\\{1}{2}",
                    Environment.GetFolderPath(Environment.SpecialFolder.CommonDesktopDirectory), lnkName,
                    DEFAULT_SHORTCUT_EXTENSION);
                if (File.Exists(linkFile)) File.Delete(linkFile);
            }
        }

        /// <summary>
        ///     Create shortcut in current path.
        /// </summary>
        /// <param name="linkFileName">shortcut name(include .lnk extension.)</param>
        /// <param name="targetPath">target path</param>
        /// <param name="workingDirectory">working path</param>
        /// <param name="arguments">arguments</param>
        /// <param name="hotkey">hot key(ex: Ctrl+Shift+Alt+A)</param>
        /// <param name="shortcutWindowStyle">window style</param>
        /// <param name="description">shortcut description</param>
        /// <param name="iconNumber">icon index(start of 0)</param>
        /// <returns>shortcut file path.</returns>
        /// <exception cref="System.IO.FileNotFoundException"></exception>
        public static bool CreateShortcut(string linkFileName, string targetPath,
            string workingDirectory = "",
            string description = "",
            string arguments = "",
            string hotkey = "",
            ShortcutWindowStyles shortcutWindowStyle = ShortcutWindowStyles.WshNormalFocus,
            int iconNumber = 0)
        {
            if (linkFileName.Contains(DEFAULT_SHORTCUT_EXTENSION) == false)
                linkFileName = string.Format("{0}{1}", linkFileName, DEFAULT_SHORTCUT_EXTENSION);

            if (File.Exists(targetPath) == false) throw new FileNotFoundException(targetPath);

            if (workingDirectory == string.Empty) workingDirectory = Path.GetDirectoryName(targetPath);

            var iconLocation = string.Format("{0},{1}", targetPath, iconNumber);

            //if (Environment.Version.Major >= 4)
            //{
            //    Type shellType = Type.GetTypeFromProgID(WSCRIPT_SHELL_NAME);
            //    dynamic shell = Activator.CreateInstance(shellType);
            //    dynamic shortcut = shell.CreateShortcut(linkFileName);

            //    shortcut.TargetPath = targetPath;
            //    shortcut.WorkingDirectory = workingDirectory;
            //    shortcut.Arguments = arguments;
            //    shortcut.Hotkey = hotkey;
            //    shortcut.WindowStyle = shortcutWindowStyle;
            //    shortcut.Description = description;
            //    shortcut.IconLocation = iconLocation;

            //    shortcut.Save();
            //}
            //else
            {
                var shellType = Type.GetTypeFromProgID(WSCRIPT_SHELL_NAME);
                var shell = Activator.CreateInstance(shellType);
                var shortcut = shellType.InvokeMethod("CreateShortcut", shell, linkFileName);
                var shortcutType = shortcut.GetType();

                shortcutType.InvokeSetMember("TargetPath", shortcut, targetPath);
                shortcutType.InvokeSetMember("WorkingDirectory", shortcut, workingDirectory);
                shortcutType.InvokeSetMember("Arguments", shortcut, arguments);
                shortcutType.InvokeSetMember("Hotkey", shortcut, hotkey);
                shortcutType.InvokeSetMember("WindowStyle", shortcut, shortcutWindowStyle);
                shortcutType.InvokeSetMember("Description", shortcut, description);
                shortcutType.InvokeSetMember("IconLocation", shortcut, iconLocation);

                shortcutType.InvokeMethod("Save", shortcut);
            }

            return true;
        }

        private static object InvokeSetMember(this Type type, string methodName, object targetInstance,
            params object[] arguments)
        {
            return type.InvokeMember(
                methodName,
                BindingFlags.Public | BindingFlags.Instance | BindingFlags.SetProperty,
                null,
                targetInstance,
                arguments);
        }

        private static object InvokeMethod(this Type type, string methodName, object targetInstance,
            params object[] arguments)
        {
            return type.InvokeMember(
                methodName,
                BindingFlags.Public | BindingFlags.Instance | BindingFlags.InvokeMethod,
                null,
                targetInstance,
                arguments);
        }

        #region Constants

        /// <summary>
        ///     Default shortcut extension
        /// </summary>
        public const string DEFAULT_SHORTCUT_EXTENSION = ".lnk";

        private const string WSCRIPT_SHELL_NAME = "WScript.Shell";

        #endregion
    }
}