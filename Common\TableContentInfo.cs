﻿using System;
using System.Collections.Generic;
using System.Reflection;

namespace OCRTools.Common
{
    [Serializable]
    [Obfuscation]
    public class TableContentInfo
    {
        [Obfuscation] public List<TableRow> rows { get; set; }
    }

    [Serializable]
    [Obfuscation]
    public class TableRow
    {
        [Obfuscation] public int index { get; set; }

        [Obfuscation] public List<TableCell> cells { get; set; }
    }

    [Serializable]
    [Obfuscation]
    public class TableCell
    {
        [Obfuscation] public int index { get; set; }

        [Obfuscation] public string content { get; set; }
    }
}