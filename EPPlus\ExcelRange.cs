﻿/*******************************************************************************
 * You may amend and distribute as you like, but don't remove this header!
 *
 * EPPlus provides server-side generation of Excel 2007/2010 spreadsheets.
 * See https://github.com/JanKallman/EPPlus for details.
 *
 * Copyright (C) 2011  <PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.

 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  
 * See the GNU Lesser General Public License for more details.
 *
 * The GNU Lesser General Public License can be viewed at http://www.opensource.org/licenses/lgpl-license.php
 * If you unfamiliar with this license or have questions about it, here is an http://www.gnu.org/licenses/gpl-faq.html
 *
 * All code and executables are provided "as is" with no warranty either express or implied. 
 * The author accepts no liability for any damage or loss of business that this product may cause.
 *
 * Code change notes:
 * 
 * Author							Change						Date
 * ******************************************************************************
 * Jan Källman		    Initial Release		        2009-10-01
 * Jan Källman		    License changed GPL-->LGPL 2011-12-27
 *******************************************************************************/
using System;
namespace OfficeOpenXml
{
    /// <summary>
    /// A range of cells. 
    /// </summary>
    public class ExcelRange : ExcelRangeBase
    {
        #region "Constructors"
        internal ExcelRange(ExcelWorksheet sheet) :
            base(sheet)
        {

        }
        internal ExcelRange(ExcelWorksheet sheet, string address)
            : base(sheet, address)
        {

        }
        internal ExcelRange(ExcelWorksheet sheet, int fromRow, int fromCol, int toRow, int toCol)
            : base(sheet)
        {
            _fromRow = fromRow;
            _fromCol = fromCol;
            _toRow = toRow;
            _toCol = toCol;
        }
        #endregion
        #region "Indexers"
        /// <summary>
        /// Access the range using an address
        /// </summary>
        /// <param name="Address">The address</param>
        /// <returns>A range object</returns>
        public ExcelRange this[string Address]
        {
            get
            {
                if (_worksheet.Names.ContainsKey(Address))
                {
                    if (_worksheet.Names[Address].IsName)
                    {
                        return null;
                    }
                    else
                    {
                        base.Address = _worksheet.Names[Address].Address;
                    }
                }
                else
                {
                    base.Address = Address;
                }
                _rtc = null;
                return this;
            }
        }

        private ExcelRange GetTableAddess(ExcelWorksheet _worksheet, string address)
        {
            int ixStart = address.IndexOf('[');
            if (ixStart == 0) //External Address
            {
                int ixEnd = address.IndexOf(']', ixStart + 1);
                if (ixStart >= 0 & ixEnd >= 0)
                {
                    var external = address.Substring(ixStart + 1, ixEnd - 1);
                    //if (Worksheet.Workbook._externalReferences.Count < external)
                    //{
                    //foreach(var 
                    //}
                }
            }
            return null;
        }
        /// <summary>
        /// Access a single cell
        /// </summary>
        /// <param name="Row">The row</param>
        /// <param name="Col">The column</param>
        /// <returns>A range object</returns>
        public ExcelRange this[int Row, int Col]
        {
            get
            {
                ValidateRowCol(Row, Col);

                _fromCol = Col;
                _fromRow = Row;
                _toCol = Col;
                _toRow = Row;
                _rtc = null;
                // avoid address re-calculation
                //base.Address = GetAddress(_fromRow, _fromCol);
                _start = null;
                _end = null;
                _addresses = null;
                _address = GetAddress(_fromRow, _fromCol);
                ChangeAddress();
                return this;
            }
        }
        /// <summary>
        /// Access a range of cells
        /// </summary>
        /// <param name="FromRow">Start row</param>
        /// <param name="FromCol">Start column</param>
        /// <param name="ToRow">End Row</param>
        /// <param name="ToCol">End Column</param>
        /// <returns></returns>
        public ExcelRange this[int FromRow, int FromCol, int ToRow, int ToCol]
        {
            get
            {
                ValidateRowCol(FromRow, FromCol);
                ValidateRowCol(ToRow, ToCol);

                _fromCol = FromCol;
                _fromRow = FromRow;
                _toCol = ToCol;
                _toRow = ToRow;
                _rtc = null;
                // avoid address re-calculation
                //base.Address = GetAddress(_fromRow, _fromCol, _toRow, _toCol);
                _start = null;
                _end = null;
                _addresses = null;
                _address = GetAddress(_fromRow, _fromCol, _toRow, _toCol);
                ChangeAddress();
                return this;
            }
        }
        #endregion
        private static void ValidateRowCol(int Row, int Col)
        {
            if (Row < 1 || Row > ExcelPackage.MaxRows)
            {
                throw (new ArgumentException("Row out of range"));
            }
            if (Col < 1 || Col > ExcelPackage.MaxColumns)
            {
                throw (new ArgumentException("Column out of range"));
            }
        }

    }
}
