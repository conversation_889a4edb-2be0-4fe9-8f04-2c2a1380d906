﻿using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FmFlags
    {
        private readonly Font baseFont = new Font("微软雅黑", 24f / Program.Factor);

        public FmFlags(int delayMilSec)
        {
            DelayMilSec = delayMilSec;
            InitializeComponent();
            var baseStyle = 524416 | WS_EX_NOACTIVATE | WS_EX_TOPMOST;
            SetWindowLong(Handle, GWL_EXSTYLE, new IntPtr(baseStyle));
            TopMost = true;
        }

        #region Native Methods

        private const int WS_EX_TOPMOST = 0x00000008;
        private const int WS_EX_NOACTIVATE = 0x08000000;
        private const int GWL_EXSTYLE = -20;

        public static IntPtr SetWindowLong(IntPtr hWnd, int nIndex, IntPtr dwNewLong)
        {
            return Environment.Is64BitProcess
                ? SetWindowLong64(hWnd, nIndex, dwNewLong)
                : SetWindowLong32(hWnd, nIndex, dwNewLong);
        }

        [DllImport("user32.dll", EntryPoint = "SetWindowLong")]
        private static extern IntPtr SetWindowLong32(IntPtr hWnd, int nIndex, IntPtr dwNewLong);

        [DllImport("user32.dll", EntryPoint = "SetWindowLongPtr")]
        private static extern IntPtr SetWindowLong64(IntPtr hWnd, int nIndex, IntPtr dwNewLong);

        #endregion

        public int DelayMilSec { get; set; }

        protected override CreateParams CreateParams
        {
            get
            {
                var createParams = base.CreateParams;
                createParams.Style |= 131072;
                if (!DesignMode) createParams.ExStyle |= 524288;
                return createParams;
            }
        }

        private void SetBits(Bitmap bitmap)
        {
            var flag = !Image.IsCanonicalPixelFormat(bitmap.PixelFormat) ||
                       !Image.IsAlphaPixelFormat(bitmap.PixelFormat);
            if (flag) throw new ApplicationException("图片必须是32位带Alhpa通道的图片。");
            var hObj = IntPtr.Zero;
            var dc = HelpWin32.GetDC(IntPtr.Zero);
            var intPtr = IntPtr.Zero;
            var intPtr2 = HelpWin32.CreateCompatibleDC(dc);
            try
            {
                var point = new HelpWin32.Point(Left, Top);
                var size = new HelpWin32.Size(bitmap.Width, bitmap.Height);
                var blendfunction = default(HelpWin32.BLENDFUNCTION);
                var point2 = new HelpWin32.Point(0, 0);
                intPtr = bitmap.GetHbitmap(Color.FromArgb(0));
                hObj = HelpWin32.SelectObject(intPtr2, intPtr);
                blendfunction.BlendOp = 0;
                blendfunction.SourceConstantAlpha = byte.MaxValue;
                blendfunction.AlphaFormat = 1;
                blendfunction.BlendFlags = 0;
                HelpWin32.UpdateLayeredWindow(Handle, dc, ref point, ref size, intPtr2, ref point2, 0,
                    ref blendfunction, 2);
            }
            finally
            {
                if (intPtr != IntPtr.Zero)
                {
                    HelpWin32.SelectObject(intPtr2, hObj);
                    HelpWin32.DeleteObject(intPtr);
                }

                HelpWin32.ReleaseDC(IntPtr.Zero, dc);
                HelpWin32.DeleteDC(intPtr2);
            }
        }

        public void DrawStr(string tipMsg)
        {
            SizeF sf = TextRenderer.MeasureText(CreateGraphics(), tipMsg, baseFont, new Size(10000000, 1000000),
                CommonString.BaseTextFormatFlags);
            var 宽度 = (int) (100 + sf.Width);
            ClientSize = new Size(宽度, 50);
            Location = new Point((Screen.PrimaryScreen.Bounds.Width - Width) / 2,
                (Screen.PrimaryScreen.WorkingArea.Height - Height) / 2 / 3 * 5);
            using (var bmp = new Bitmap(宽度, 50))
            {
                using (var g = Graphics.FromImage(bmp))
                {
                    g.InterpolationMode = InterpolationMode.Bilinear;
                    g.SmoothingMode = SmoothingMode.HighQuality;
                    g.TextRenderingHint = TextRenderingHint.AntiAlias;
                    g.Clear(Color.Transparent);
                    g.FillRectangle(new SolidBrush(Color.FromArgb(1, 255, 255, 255)), ClientRectangle);
                    var stringFormat = new StringFormat
                    {
                        Alignment = StringAlignment.Center
                    };
                    var r = new Rectangle(0, 3, 宽度, 50);
                    g.FillRectangle(new SolidBrush(Color.FromArgb(120, Color.Black)), 1, 1, 宽度 - 2, 48);
                    g.DrawRectangle(new Pen(Color.FromArgb(224, 224, 224)), 2, 2, 宽度 - 2 - 2, 46);
                    g.DrawString(tipMsg, baseFont, new SolidBrush(Color.FromArgb(255, Color.White)), r, stringFormat);
                    SetBits(bmp);
                    g.Dispose();
                }

                bmp.Dispose();
            }

            Show();
            Delay((uint) DelayMilSec);
            Close();
        }

        private void Delay(uint ms)
        {
            var tickCount = HelpWin32.GetTickCount();
            while (HelpWin32.GetTickCount() - tickCount < ms)
            {
                Thread.SpinWait(5);
                Application.DoEvents();
            }
        }
    }
}