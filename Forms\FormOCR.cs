﻿using OCRTools.Properties;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Timer = System.Windows.Forms.Timer;

namespace OCRTools
{
    public partial class FormOcr : BaseForm
    {
        private FrmSearch _frmSearch;

        private bool _isLeave;
        private bool _isReduceing;

        private double _leaveSecond;

        public FormOcr()
        {
            SetStyle(ControlStyles.SupportsTransparentBackColor, true);
            InitializeComponent();
            BackColor = Color.Transparent;
            BackgroundImage = Resources.mini_search;
            BackgroundImageLayout = ImageLayout.Stretch;
            var tmrTick = new Timer { Interval = 100 };
            tmrTick.Tick += TmrTick_Tick;
            tmrTick.Enabled = true;
        }

        public object Content { get; set; }

        public ClipboardContentType ContentType { get; set; }

        public bool IsForceFocus { get; set; }

        public bool IsLeave
        {
            get => _isLeave;
            set
            {
                _isLeave = value;
                if (_isLeave && !_isReduceing)
                {
                    _isReduceing = true;
                    Task.Factory.StartNew(() =>
                    {
                        ReduceOpactity();
                        _isReduceing = false;
                    });
                }
            }
        }

        protected override CreateParams CreateParams
        {
            get
            {
                var result = base.CreateParams;
                result.ExStyle |= 0x08000000; // WS_EX_NOACTIVATE
                return result;
            }
        }

        [DllImport("user32.dll", CharSet = CharSet.Auto, ExactSpelling = true)]
        private static extern bool ShowWindow(HandleRef hWnd, int nCmdShow);

        private void TmrTick_Tick(object sender, EventArgs e)
        {
            CommonString.IsOnOcrButton = Visible && Bounds.Contains(Cursor.Position);
            IsLeave = !Visible || !IsForceFocus && !CommonString.IsOnOcrButton;
            if (!IsLeave)
            {
                _leaveSecond = 0;
            }
            else if (Visible)
            {
                _leaveSecond += 0.1;
                if (_leaveSecond > 10) HideWindow();
            }
        }

        public void HideWindow(bool hideSearch = false)
        {
            Hide();
            if (hideSearch)
                _frmSearch?.Hide();
        }

        public void ShowTool(bool isFirst = false)
        {
            Opacity = 0;
            if (isFirst)
            {
                ShowWindow(new HandleRef(this, Handle), 4);
                //this.Show();
                IsForceFocus = true;
                IsLeave = false;
                Task.Factory.StartNew(() =>
                {
                    var left = 15;
                    while (!CommonString.IsExit && !IsLeave && left > 0)
                    {
                        Thread.Sleep(100);
                        left--;
                    }

                    IsForceFocus = false;
                });
            }

            Opacity = 0.9;
        }

        private void FormOCR_MouseHover(object sender, EventArgs e)
        {
            ShowSearch();
        }

        public void ShowSearch()
        {
            CommonMethod.DetermineCall(this, delegate
            {
                if (Content == null) return;
                if (!ContentType.Equals(ClipboardContentType.文本))
                {
                    switch (ContentType)
                    {
                        case ClipboardContentType.图片:
                            break;
                        case ClipboardContentType.文件:
                            FrmMain.DragDropEventDelegate?.Invoke(new List<string> { Content?.ToString() }, null, null,
                                ProcessBy.主界面, null);
                            break;
                    }

                    HideWindow(true);
                }
                else
                {
                    var isFirst = false;
                    if (_frmSearch == null)
                    {
                        isFirst = true;
                        _frmSearch = new FrmSearch { Icon = Icon };
                    }
                    else
                    {
                        if (_frmSearch.Visible && Equals(_frmSearch.SelectedText, Content?.ToString())) return;
                    }

                    _frmSearch.SelectedText = Content?.ToString();

                    var location = new Point(Cursor.Position.X + 20, Cursor.Position.Y + 20);
                    _frmSearch.Location = location;
                    _frmSearch.Opacity = 0;
                    _frmSearch.ShowTool(isFirst);
                    if (!Equals(location, _frmSearch.Location)) _frmSearch.Location = location;
                    _frmSearch.Opacity = 1;
                    Update();
                }
            });
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            e.Cancel = true;
            base.OnClosing(e);
        }

        private void FormOCR_MouseMove(object sender, MouseEventArgs e)
        {
            IsLeave = false;
            ShowTool();
        }

        private void ReduceOpactity()
        {
            try
            {
                while (IsLeave && !CommonString.IsExit && Opacity >= 0.4)
                {
                    Thread.Sleep(10);
                    Opacity -= 0.003;
                }
            }
            catch
            {
            }
        }

        internal UcContent GetUcContent()
        {
            return _frmSearch?.GetUcContent();
        }

        internal void ShowLoading(string strProcessOcr, bool isShowLoading)
        {
            _frmSearch?.ShowLoading(strProcessOcr, isShowLoading);
        }

        internal void CloseLoading()
        {
            _frmSearch?.CloseLoading();
        }
    }

    public enum ClipboardContentType
    {
        图片,
        文件,
        文本
    }
}