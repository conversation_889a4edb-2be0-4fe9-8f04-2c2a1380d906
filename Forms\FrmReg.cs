﻿using MetroFramework.Forms;
using OCRTools.Language;
using System;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmReg : MetroForm
    {
        private const int N_TOTAL_SECOND = 120;

        private int _nowSeconds;

        public FrmReg()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            CommonMethod.SetStyle(lnkValidateCode, ControlStyles.Selectable, false);
            this.AddContactUserBtn("FrmReg");
        }

        public string Account { get; internal set; }
        public string Pwd { get; internal set; }

        private void lnkValidateCode_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            if (!lnkValidateCode.Text.Equals("验证码".CurrentText())) return;
            var account = txtAccount.Text.Trim();
            if (string.IsNullOrEmpty(account))
            {
                MessageBox.Show(this, "账号不能为空！".CurrentText(), CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtAccount.Focus();
                return;
            }

            var isEmail = account.IsEmail();
            var isMobile = account.IsMobile();
            if (!isEmail && !isMobile) //
            {
                MessageBox.Show(this, "账号格式不正确，必须为手机号/邮箱！".CurrentText(), CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtAccount.SelectAll();
                txtAccount.Focus();
                return;
            }

            var strMsg = "";
            var result = OcrHelper.SendRegMsg(account, isMobile, ref strMsg);
            if (!result)
            {
                if (string.IsNullOrEmpty(strMsg)) strMsg = "获取验证码失败".CurrentText() + "，" + CommonString.StrRetry.CurrentText();
                MessageBox.Show(this, strMsg, CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            else
            {
                _nowSeconds = 0;
                tmrTick.Enabled = true;
            }
        }

        private void tmrTick_Tick(object sender, EventArgs e)
        {
            _nowSeconds++;
            if (N_TOTAL_SECOND - _nowSeconds > 0)
            {
                lnkValidateCode.Text = string.Format("{0}s", N_TOTAL_SECOND - _nowSeconds);
            }
            else
            {
                lnkValidateCode.Text = "验证码".CurrentText();
                tmrTick.Enabled = false;
            }
        }

        private void btnReg_Click(object sender, EventArgs e)
        {
            var account = txtAccount.Text.Trim();
            var isEmail = account.IsEmail();
            var isMobile = account.IsMobile();
            if (!isEmail && !isMobile)
            {
                MessageBox.Show(this, "账号格式不正确，必须为手机号或者邮箱！".CurrentText(), CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtAccount.SelectAll();
                txtAccount.Focus();
                return;
            }

            var code = txtCode.Text.Trim();
            if (string.IsNullOrEmpty(code))
            {
                MessageBox.Show(this, "请填写验证码，如果收不到，请尝试用更换账号类型(手机/邮箱)！".CurrentText(), CommonString.StrReminder, MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
                txtCode.Focus();
                return;
            }

            var nickName = txtNickName.Text.Trim();
            if (nickName.Length < 2 || nickName.Length > 12 ||
                !new Regex(@"^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z0-9_])").IsMatch(nickName))
            {
                MessageBox.Show(this, "昵称为2-12位的中英文数字字母或下划线！".CurrentText(), CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtNickName.Focus();
                return;
            }

            var pwd = txtPwd.Text.Trim();
            if (string.IsNullOrEmpty(pwd))
            {
                MessageBox.Show(this, "密码不能为空，必须为6-15位的数字或大小写字母！".CurrentText(), CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtPwd.Focus();
                return;
            }

            //使用regex进行格式设置 至少有数字、大小写字母，最少8个字符、最长30个字符
            if (!new Regex(@"[0-9A-Za-z].{5,15}").IsMatch(pwd)) //判断密码格式是否符合要求
            {
                MessageBox.Show(this, "密码必须为6-15位的数字或大小写字母！".CurrentText(), CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtPwd.Focus();
                return;
            }

            if (!CommonString.IsOnLine)
            {
                CommonMethod.ShowHelpMsg(CommonString.StrNetWorkError.CurrentText());
                return;
            }

            var strMsg = "";
            var result = OcrHelper.SendRegInfo(account, pwd, nickName, code, ref strMsg);
            if (result)
            {
                Account = account;
                Pwd = pwd;
                DialogResult = DialogResult.OK;
                Close();
            }
            else
            {
                if (string.IsNullOrEmpty(strMsg)) strMsg = "注册失败".CurrentText() + "，" + CommonString.StrRetry.CurrentText();
                MessageBox.Show(this, strMsg, CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}