using System;
using System.IO;
using System.Web.Script.Serialization;

namespace TianruoOCR
{
	public static class SaveDataInit
	{
		public static string GifTextDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData) + "\\OCR_Data\\";

		public static string DataPath = GifTextDataPath + "Data";

        public static JavaScriptSerializer JavaScriptSerializer = new JavaScriptSerializer();

        public static SaveStatus.Root JsonRoot()
		{
			string decryptString = File.ReadAllText(DataPath);
			string value = DesEncryption.DecryptDES(decryptString);
			SaveStatus.Root rt = JavaScriptSerializer.Deserialize<SaveStatus.Root>(value);
			return fix(rt);
		}

		private static SaveStatus.Root fix(SaveStatus.Root rt)
		{
			if (rt.Set == null)
			{
				rt.Set = new SaveStatus.Set();
			}
			return rt;
		}
	}
}
