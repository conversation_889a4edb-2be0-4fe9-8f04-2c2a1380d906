﻿using OCRTools.Language;
using OCRTools.Properties;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmSearch : BaseForm
    {
        private readonly UcContent _content;

        //记录鼠标按键是否按下
        private bool _isMouseDown;
        private Point _oldPoint = new Point(0, 0);

        public FrmSearch()
        {
            InitializeComponent();
            //ShadowType = CommonString.CommonShadowType;
            var tmrTick = new Timer { Interval = 100 };
            tmrTick.Tick += TmrTick_Tick;
            tmrTick.Enabled = true;
            MouseDown += FrmSearch_MouseDown;
            MouseMove += FrmSearch_MouseMove;
            MouseUp += FrmSearch_MouseUp;

            _content = new UcContent
            {
                Location = new Point(10, txtSearch.Top + txtSearch.Height + 20),
                Size = new Size(Width - 20, Height - txtSearch.Height - txtSearch.Top - 25),
                Anchor = AnchorStyles.Top | AnchorStyles.Right | AnchorStyles.Bottom | AnchorStyles.Left
            };
            Controls.Add(_content);

            BackgroundImage = null;
        }

        public string SelectedText { get; set; }

        public bool IsLeave { get; set; }

        //protected override CreateParams CreateParams
        //{
        //    get
        //    {
        //        var result = base.CreateParams;
        //        result.ExStyle |= 0x08000000; // WS_EX_NOACTIVATE
        //        return result;
        //    }
        //}

        protected override CreateParams CreateParams
        {
            get
            {
                var cp = base.CreateParams;
                if (!DesignMode) cp.ClassStyle |= 0x20000; //实现窗体边框阴影效果
                return cp;
            }
        }

        private void FrmSearch_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                _oldPoint = e.Location;

                _isMouseDown = true;
            }
        }

        private void FrmSearch_MouseMove(object sender, MouseEventArgs e)
        {
            if (_isMouseDown)
            {
                Left += e.X - _oldPoint.X;
                Top += e.Y - _oldPoint.Y;
            }
        }

        private void FrmSearch_MouseUp(object sender, MouseEventArgs e)
        {
            //   修改鼠标状态isMouseDown的值 
            //   确保只有鼠标左键按下并移动时，才移动窗体 
            if (e.Button == MouseButtons.Left) _isMouseDown = false;
        }

        private void TmrTick_Tick(object sender, EventArgs e)
        {
            CommonString.IsOnOcrSearch = Visible && Bounds.Contains(Cursor.Position);
            IsLeave = !Visible || !CommonString.IsOnOcrButton;
        }

        [DllImport("user32.dll", CharSet = CharSet.Auto, ExactSpelling = true)]
        public static extern bool ShowWindow(HandleRef hWnd, int nCmdShow);

        public void ShowTool(bool isFirst = false)
        {
            Opacity = 0;
            if (isFirst) Show();
            ShowWindow(new HandleRef(this, Handle), 4);
            txtSearch.Text = SelectedText;
            btnSearch_Click(null, null);
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            e.Cancel = true;
            Hide();
            base.OnClosing(e);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            e.Graphics.InterpolationMode = InterpolationMode.HighQualityBilinear;
            e.Graphics.CompositingQuality = CompositingQuality.HighQuality;
            e.Graphics.SmoothingMode = SmoothingMode.HighQuality;
            e.Graphics.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;

            RenderHelper.DrawFormFringe(this, e.Graphics, Resources.tips_nolight_bkg, 5);

            e.Graphics.DrawIcon(Icon, new Rectangle { X = 8, Y = 8, Height = 25, Width = 25 });
            e.Graphics.DrawString("划词翻译".CurrentText(), CommonString.GetFont(txtSearch.Font.FontFamily.Name, 11)
                , Brushes.Gray, new PointF { X = 12 + 22, Y = 13 });
            e.Graphics.FillRectangle(Brushes.Red,
                new RectangleF(new PointF(3, txtSearch.Location.Y + txtSearch.Height + 8), new SizeF(Width - 6, 2)));
        }

        private void pnlSetting_Click(object sender, EventArgs e)
        {
            Hide();
        }

        private void pnlSetting_MouseMove(object sender, MouseEventArgs e)
        {
            pnlSetting.BackgroundImage = Resources.loginurl_close_hover;
        }

        private void pnlSetting_MouseLeave(object sender, EventArgs e)
        {
            pnlSetting.BackgroundImage = Resources.loginurl_close;
        }

        internal UcContent GetUcContent()
        {
            return _content;
        }

        private void FrmSearch_Load(object sender, EventArgs e)
        {
            ucLoading1.InitLoading(_content.Size, _content.Location);
            ucLoading1.Anchor = _content.Anchor;
            ucLoading1.SetLoadingType(LoadingType.红色圆圈);
        }

        internal void ShowLoading(string strProcessOcr, bool isShowLoading)
        {
            ucLoading1.ShowLoading(strProcessOcr, isShowLoading);
        }

        internal void CloseLoading()
        {
            ucLoading1.CloseLoading();
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            var text = txtSearch.Text.Trim();
            if (string.IsNullOrEmpty(text)) return;
            _content.BindContentByStr("");
            Tag = null;
            Task.Factory.StartNew(() =>
            {
                var files = new List<string> { "data:txt" + text };
                FrmMain.DragDropEventDelegate?.Invoke(files, null, OcrType.翻译, ProcessBy.划词翻译, null);
            });
        }

        private void txtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter) btnSearch_Click(sender, e);
        }
    }
}