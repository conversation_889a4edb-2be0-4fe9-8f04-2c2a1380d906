﻿using OCRTools;
using OCRTools.Common;
using OCRTools.ImgUpload;
using System;
using System.Collections.Specialized;
using System.Net;
using System.Reflection;

namespace ImageLib
{
    /// <summary>
    /// https://cloud.tencent.com/act/pro/ciExhibition?tab=contentReview&sub=pictureReview
    /// </summary>
    internal class TencentNewUpload : BaseImageUpload
    {
        public TencentNewUpload()
        {
            Name = "TencentNew";
            SupportCompress = true;
        }

        public override string GetResult(byte[] content, bool isZip = false)
        {
            var result = "";
            var html = WebClientExt.GetHtml("https://ci-exhibition.cloud.tencent.com/samples/createUploadKey?ext=png");
            var token = html?.DeserializeJson<WanXiangRoot>();
            if (!string.IsNullOrEmpty(token?.data?.key))
            {
                try
                {
                    var url = "https://ci-h5-demo-1258125638.cos.ap-chengdu.myqcloud.com/" + token.data.key;
                    html = WebClientExt.GetHtml(url, Convert.ToBase64String(content), 30, new NameValueCollection()
                       {
                            { "Authorization", token.data.uploadAuthorization},
                       });
                    using (var client = new CnnWebClient() { Method = "HEAD", Timeout = 10000 })
                    {
                        client.OpenRead(url);
                        result = url;
                    }
                }
                catch { }
                if (!string.IsNullOrEmpty(result) && isZip)
                {
                    if (token != null && !string.IsNullOrEmpty(token?.data?.downloadAuthorization))
                        result = string.Format("{0}?{1}&imageSlim", result, token.data.downloadAuthorization);
                }
            }
            return result;
        }

        [Obfuscation]
        class WanXiangData
        {
            [Obfuscation]
            public string key { get; set; }
            [Obfuscation]
            public string uploadAuthorization { get; set; }
            [Obfuscation]
            public string downloadAuthorization { get; set; }
        }

        [Obfuscation]
        class WanXiangRoot
        {
            [Obfuscation]
            public WanXiangData data { get; set; }
        }
    }
}
