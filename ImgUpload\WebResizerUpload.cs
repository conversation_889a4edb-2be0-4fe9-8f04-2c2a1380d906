﻿using OCRTools.Common;
using OCRTools.ImgUpload;
using System.Collections.Specialized;
using System.IO;

namespace ImageLib
{
    /// <summary>
    /// </summary>
    internal class WebResizerUpload : BaseImageUpload
    {
        public WebResizerUpload()
        {
            MaxSize = 1024 * 1024 * 15;
            Name = "WebResizer";
            SupportCompress = true;
        }

        private const string strFileNameSpilt = ").attr(\"src\",'";

        public override string GetResult(byte[] content, bool isZip = false)
        {
            var result = "";
            var url = "http://webresizer.com/resizer/action-z-shivani.ajax.php";
            var file = new UploadFileInfo()
            {
                Name = "files[]",
                Filename = "1.png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            var vaules = new NameValueCollection() {
                    { "filenames", "1.png" }
                };
            var html = string.Empty;
            try
            {
                html = UploadFileRequest.Post(url, new[] { file }, vaules);
            }
            catch { }
            if (html.Contains(strFileNameSpilt))
            {
                result = html.Substring(html.IndexOf(strFileNameSpilt) + strFileNameSpilt.Length);
                result = result.Substring(0, result.IndexOf("'")).Replace("\\/", "/");
                result = "http://webresizer.com/resizer/" + result;
            }
            return result;
        }
    }
}
