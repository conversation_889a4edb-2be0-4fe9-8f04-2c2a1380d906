﻿using OCRTools;
using OCRTools.Common;
using OCRTools.ImgUpload;
using System.Collections.Specialized;
using System.IO;

namespace ImageLib
{
    /// <summary>
    /// http://tools.yum6.cn/Tools/Images/
    /// </summary>
    internal class _360ImageUpload : BaseImageUpload
    {
        public _360ImageUpload()
        {
            Name = "360";
            MaxSize = (int)(1024 * 1024 * 1.3d);
        }

        private const string strFileNameSpilt = "data-imgkey=\"";

        public override string GetResult(byte[] content, bool isZip = false)
        {
            var result = string.Empty;
            var file = new UploadFileInfo()
            {
                Name = "upload",
                Filename = "test.png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            var vaules = new NameValueCollection() {
                    { "imgurl", "" } ,
                    { "base64image", "" } ,
                    { "submittype", "upload" } ,
                    { "src", "image" } ,
                    { "srcsp", "st_search" } ,
                };
            var html = string.Empty;
            try
            {
                html = UploadFileRequest.Post("https://st.so.com/stu", new[] { file }, vaules);
            }
            catch { }
            if (html?.Contains(strFileNameSpilt) == true)
            {
                //https://p1.ssl.qhimgs1.com/t01e785eaf21d406f8d.jpg
                html = html.Substring(html.IndexOf(strFileNameSpilt) + strFileNameSpilt.Length);
                html = html.Substring(0, html.IndexOf("\"")).Replace("\\/", "/");
                if (!string.IsNullOrEmpty(html))
                    result = "https://p1.ssl.qhimgs1.com/" + html;
            }
            return result;
        }
    }
}
