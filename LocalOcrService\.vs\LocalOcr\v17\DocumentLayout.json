{"Version": 1, "WorkspaceRootPath": "D:\\Code\\CatchTools\\LocalOcrService\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}|OcrMain\\OcrMain.csproj|D:\\Code\\CatchTools\\LocalOcrService\\ocrmain\\properties\\assemblyinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}|OcrMain\\OcrMain.csproj|solutionrelative:ocrmain\\properties\\assemblyinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}|OcrMain\\OcrMain.csproj|d:\\code\\catchtools\\localocrservice\\ocrmain\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}|OcrMain\\OcrMain.csproj|solutionrelative:ocrmain\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}|OcrMain\\OcrMain.csproj|D:\\Code\\CatchTools\\LocalOcrService\\ocrmain\\myhttpserver.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}|OcrMain\\OcrMain.csproj|solutionrelative:ocrmain\\myhttpserver.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}|OcrMain\\OcrMain.csproj|D:\\Code\\CatchTools\\LocalOcrService\\ocrmain\\localocrhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}|OcrMain\\OcrMain.csproj|solutionrelative:ocrmain\\localocrhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}|OcrMain\\OcrMain.csproj|D:\\Code\\CatchTools\\LocalOcrService\\ocrmain\\ocrresultutil.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}|OcrMain\\OcrMain.csproj|solutionrelative:ocrmain\\ocrresultutil.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}|OcrMain\\OcrMain.csproj|D:\\Code\\CatchTools\\LocalOcrService\\ocrmain\\ocrresult.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1AFCD0CB-5A12-4E2D-A6C7-AF4891AEC814}|OcrMain\\OcrMain.csproj|solutionrelative:ocrmain\\ocrresult.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "AssemblyInfo.cs", "DocumentMoniker": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\Properties\\AssemblyInfo.cs", "RelativeDocumentMoniker": "OcrMain\\Properties\\AssemblyInfo.cs", "ToolTip": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\Properties\\AssemblyInfo.cs", "RelativeToolTip": "OcrMain\\Properties\\AssemblyInfo.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-18T08:18:25.54Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "LocalOcrHelper.cs", "DocumentMoniker": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\LocalOcrHelper.cs", "RelativeDocumentMoniker": "OcrMain\\LocalOcrHelper.cs", "ToolTip": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\LocalOcrHelper.cs", "RelativeToolTip": "OcrMain\\LocalOcrHelper.cs", "ViewState": "AgIAAEYAAAAAAAAAAAAjwE4AAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-11T01:48:01.114Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "Program.cs", "DocumentMoniker": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\Program.cs", "RelativeDocumentMoniker": "OcrMain\\Program.cs", "ToolTip": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\Program.cs", "RelativeToolTip": "OcrMain\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACEAAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-11T01:46:35.811Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "MyHttpServer.cs", "DocumentMoniker": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\MyHttpServer.cs", "RelativeDocumentMoniker": "OcrMain\\MyHttpServer.cs", "ToolTip": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\MyHttpServer.cs", "RelativeToolTip": "OcrMain\\MyHttpServer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwA4AAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-07T10:08:52.482Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "OcrResultUtil.cs", "DocumentMoniker": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\OcrResultUtil.cs", "RelativeDocumentMoniker": "OcrMain\\OcrResultUtil.cs", "ToolTip": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\OcrResultUtil.cs", "RelativeToolTip": "OcrMain\\OcrResultUtil.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-07T10:08:44.048Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "OcrResult.cs", "DocumentMoniker": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\OcrResult.cs", "RelativeDocumentMoniker": "OcrMain\\OcrResult.cs", "ToolTip": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\OcrResult.cs", "RelativeToolTip": "OcrMain\\OcrResult.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAIwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-07T10:08:37.052Z"}]}]}]}