{"RootPath": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain", "ProjectFileName": "OcrMain.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Angle.cs"}, {"SourceFile": "AngleNet.cs"}, {"SourceFile": "CommonStyle.cs"}, {"SourceFile": "CrnnNet.cs"}, {"SourceFile": "DbNet.cs"}, {"SourceFile": "HttpProcessor.cs"}, {"SourceFile": "HttpServer.cs"}, {"SourceFile": "IniHelper.cs"}, {"SourceFile": "LocalOcrHelper.cs"}, {"SourceFile": "MyHttpServer.cs"}, {"SourceFile": "Ocr.cs"}, {"SourceFile": "OcrResult.cs"}, {"SourceFile": "OcrUtils.cs"}, {"SourceFile": "Program.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "ScaleParam.cs"}, {"SourceFile": "StringExtension.cs"}, {"SourceFile": "TextBlock.cs"}, {"SourceFile": "TextBox.cs"}, {"SourceFile": "TextCellInfo.cs"}, {"SourceFile": "TextLine.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.6.1.AssemblyAttributes.cs"}], "References": [{"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.1\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.1\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.1\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.1\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.1\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.1\\System.Web.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.1\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\bin\\Debug\\OcrMain.exe", "OutputItemRelativePath": "OcrMain.exe"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}