﻿using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace OcrMain
{
    public class CommonStyle
    {
        internal static string ReplacePunctuationAuto(string text, string lang, bool isAutoFull2Half, bool isAutoSpace, bool isAutoSymbol, bool isAutoDuplicateSymbol)
        {
            if (string.IsNullOrEmpty(text))
            {
                return text;
            }

            if (isAutoFull2Half)
                text = Full2Half(text);

            if (isAutoSpace)
                text = ParseSpace(text, lang);

            if (isAutoSymbol)
                text = TransSymbol(text, lang);

            if (isAutoDuplicateSymbol)
                text = RemoveDuplicateSymbol(text);

            text = ParseDateStr(text);

            text = ParseMoneyStr(text);

            return text;
        }

        /// <summary>  
        /// 将钱数中间的中文逗号替换成英文逗号  
        /// </summary>  
        /// <param name="str" type=string></param>  
        public static string ParseMoneyStr(string str)
        {
            str = Regex.Replace(str, @"(?<=\d)，(?=\d{1,3}\b)", ",");
            return str;
        }

        /// <summary>  
        /// 使用正则表达式判断是否为日期
        /// </summary>  
        /// <param name="str" type=string></param>  
        public static string ParseDateStr(string str)
        {
            // yyyy/MM/dd
            var dateMatch = Regex.Matches(str, "\\d{2,4}/\\d{1,2}/\\d{1,2}");
            str = ParseDateByType(str, dateMatch, "/");
            // yyyy-MM-dd
            dateMatch = Regex.Matches(str, "\\d{2,4}-\\d{1,2}-\\d{1,2}");
            str = ParseDateByType(str, dateMatch, "-");
            // yyyy.MM.dd
            dateMatch = Regex.Matches(str, "\\d{2,4}[.]\\d{1,2}[.]\\d{1,2}");
            str = ParseDateByType(str, dateMatch, ".");

            return str;
        }

        public static string ParseDateByType(string str, MatchCollection match, string spilt)
        {
            if (!string.IsNullOrEmpty(str) && match.Count > 0)
            {
                foreach (Match nextMatch in match)
                {
                    var nextStr = nextMatch.Value;
                    if (!string.IsNullOrEmpty(nextStr) && nextStr.Contains(spilt))
                    {
                        int.TryParse(nextStr.Substring(nextStr.LastIndexOf(spilt) + spilt.Length), out int day);
                        //大于31，说明天数只有一位，一位后边加空格
                        if (day > 31)
                        {
                            str = str.Replace(nextStr, nextStr.Substring(0, nextStr.LastIndexOf(spilt) + spilt.Length + 1) + " " + nextStr[nextStr.Length - 1]);
                        }
                        else
                        {
                            //正常日期后边加空格
                            str = str.Replace(nextStr, nextStr + " ").Replace("  ", " ");
                        }
                    }
                }
            }
            return str;
        }

        const string CJK = @"\u2E80-\u2EFF\u2F00-\u2FDF\u3040-\u309F\u30A0-\u30FA\u30FC-\u30FF\u3100-\u312F\u3200-\u32FF\u3400-\u4DBF\u4E00-\u9FFF\uF900-\uFAFF";

        /// <summary>
        /// 在中文与英文字母/用于数学、科学和工程的希腊字母/数字之间添加空格
        /// https://cdnjs.com/libraries/pangu
        /// https://github.com/Rakume/pangu.php/blob/master/pangu.php
        /// https://github.com/mzlogin/chinese-copywriting-guidelines#空格
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        internal static string ParseSpace(string text, string lang)
        {
            //spacing
            text = Regex.Replace(text, "([\\.]{2,}|\u2026)([" + CJK + "])", "$1 $2");//DOTS_CJK
            text = Regex.Replace(text, "([" + CJK + "])\\:([A-Z0-9\\(\\)])", "$1：$2");//FIX_CJK_COLON_ANS
            text = Regex.Replace(text, "([" + CJK + "])([`\"\u05F4])", "$1 $2");//CJK_QUOTE
            text = Regex.Replace(text, "([`\"\u05F4])([" + CJK + "])", "$1 $2");//QUOTE_CJK
            text = Regex.Replace(text, "([`\"\u05f4]+)[ ]* (.+?)[ ]* ([`\"\u05f4]+)", "$1$2$3");//FIX_QUOTE_ANY_QUOTE

            text = Regex.Replace(text, "([" + CJK + "])('[^s])", "$1 $2");//CJK_SINGLE_QUOTE_BUT_POSSESSIVE
            text = Regex.Replace(text, "(')([" + CJK + "])", "$1 $2");//SINGLE_QUOTE_CJK
            text = Regex.Replace(text, "([A-Za-z0-9" + CJK + "])( )('s)", "$1's");//FIX_POSSESSIVE_SINGLE_QUOTE

            text = Regex.Replace(text, "([" + CJK + "])(#)([" + CJK + "]+)(#)([" + CJK + "])", "$1 $2$3$4 $5");//HASH_ANS_CJK_HASH
            text = Regex.Replace(text, "([" + CJK + "])(#([^ ]))", "$1 $2");//CJK_HASH
            text = Regex.Replace(text, "(([^ ])#)([" + CJK + "])", "$1 $3");//HASH_CJK
            text = Regex.Replace(text, "([" + CJK + "])([\\+\\-\\*\\/=&\\|<>])([A-Za-z0-9])", "$1 $2 $3");//CJK_OPERATOR_ANS

            text = Regex.Replace(text, "([A-Za-z0-9])([\\+\\-\\*\\/=&\\|<>])([" + CJK + "])", "$1 $2 $3");//ANS_OPERATOR_CJK
            text = Regex.Replace(text, "([/]) ([a-z\\-_\\./]+)", "$1$2");//FIX_SLASH_AS
            text = Regex.Replace(text, "([/\\.])([A-Za-z\\-_\\./]+) ([/])", "$1$2$3");//FIX_SLASH_AS_SLASH
            text = Regex.Replace(text, "([" + CJK + "])([\\(\\[\\{<>\u201C])", "$1 $2");//CJK_LEFT_BRACKET
            text = Regex.Replace(text, "([\\)\\]\\}<>\u201D])([" + CJK + "])", "$1 $2");//RIGHT_BRACKET_CJK

            text = Regex.Replace(text, "([\\(\\[\\{<\u201c]+)[ ]*(.+?)[ ]*([\\)\\]\\}>\u201d]+)", "$1$2$3");//FIX_LEFT_BRACKET_ANY_RIGHT_BRACKET
            text = Regex.Replace(text, "([A-Za-z0-9" + CJK + "])[ ]*([\u201C])([A-Za-z0-9" + CJK + "\\-_ ]+)([\u201D])", "$1 $2$3$4");//ANS_CJK_LEFT_BRACKET_ANY_RIGHT_BRACKET
            text = Regex.Replace(text, "([\u201C])([A-Za-z0-9" + CJK + "\\-_ ]+)([\u201D])[ ]*([A-Za-z0-9" + CJK + "])", "$1$2$3 $4");//LEFT_BRACKET_ANY_RIGHT_BRACKET_ANS_CJK
            text = Regex.Replace(text, "([A-Za-z0-9])([\\(\\[\\{])", "$1 $2");//AN_LEFT_BRACKET
            text = Regex.Replace(text, "([\\)\\]\\}])([A-Za-z0-9])", "$1 $2");//RIGHT_BRACKET_AN
            text = Regex.Replace(text, "([" + CJK + "])([A-Za-z\u0370-\u03FF0-9@\\$%\\^&\\*\\-\\+\\\\=\\|/\xA1-\xFF\u2150-\u218F\u2700\u2014\u27BF])", "$1 $2");//CJK_ANS
            text = Regex.Replace(text, "([A-Za-z\u0370-\u03FF0-9~\\$%\\^&\\*\\-\\+\\\\=\\|/!;:,\\.\\?\xA1-\xFF\u2150-\u218F\u2700\u2014\u27BF])([" + CJK + "])", "$1 $2");//ANS_CJK
            text = Regex.Replace(text, "(%)([A-Za-z])", "$1 $2");//S_A
            text = Regex.Replace(text, "([ ]*)([\u00b7\u2022\u2027])([ ]*)", "・");//MIDDLE_DOT

            #region 中日韩

            #region 移除空格

            //去除中文之间的英文（半角）空格
            text = Regex.Replace(text, "(?<=[\u4e00-\u9fa5])(\u0020)(?=[\u4e00-\u9fa5])", string.Empty);
            //去除中文之间的中文（全角）空格
            text = Regex.Replace(text, "(?<=[\u4e00-\u9fa5])(\u3000)(?=[\u4e00-\u9fa5])", string.Empty);

            //去除韩文之间的英文（半角）空格
            text = Regex.Replace(text, "(?<=[\x3130-\x318F])(\u0020)(?=[\x3130-\x318F])", string.Empty);
            text = Regex.Replace(text, "(?<=[\xAC00-\xD7A3])(\u0020)(?=[\xAC00-\xD7A3])", string.Empty);
            //去除韩文之间的中文（全角）空格
            text = Regex.Replace(text, "(?<=[\x3130-\x318F])(\u3000)(?=[\x3130-\x318F])", string.Empty);
            text = Regex.Replace(text, "(?<=[\xAC00-\xD7A3])(\u3000)(?=[\xAC00-\xD7A3])", string.Empty);

            //去除日文之间的（半角）空格
            text = Regex.Replace(text, "(?<=[\u0800-\u4e00])(\u0020)(?=[\u0800-\u4e00])", string.Empty);
            //去除日文之间的中文（全角）空格
            text = Regex.Replace(text, "(?<=[\u0800-\u4e00])(\u3000)(?=[\u0800-\u4e00])", string.Empty);

            #endregion

            #region 增加空格

            //在中文字符与英文字符之间增加空格
            text = Regex.Replace(text, "(?<=[\u4e00-\u9fa5])([a-zA-Z])(?=[a-zA-Z]{0,})", " $1");
            //在英文字符与中文字符之间增加空格
            text = Regex.Replace(text, "(?<=[a-zA-Z])([\u4e00-\u9fa5])(?=[\u4e00-\u9fa5]{0,})", " $1");

            //在韩文字符与英文字符之间增加空格
            text = Regex.Replace(text, "(?<=[\x3130-\x318F])([a-zA-Z])(?=[a-zA-Z]{0,})", " $1");
            //在英文字符与韩文字符之间增加空格
            text = Regex.Replace(text, "(?<=[a-zA-Z])([\x3130-\x318F])(?=[\x3130-\x318F]{0,})", " $1");
            //在韩文字符与英文字符之间增加空格
            text = Regex.Replace(text, "(?<=[\xAC00-\xD7A3])([a-zA-Z])(?=[a-zA-Z]{0,})", " $1");
            //在英文字符与韩文字符之间增加空格
            text = Regex.Replace(text, "(?<=[a-zA-Z])([\xAC00-\xD7A3])(?=[\xAC00-\xD7A3]{0,})", " $1");

            //在日文字符与英文字符之间增加空格
            text = Regex.Replace(text, "(?<=[\u0800-\u4e00])([a-zA-Z])(?=[a-zA-Z]{0,})", " $1");
            //在英文字符与日文字符之间增加空格
            text = Regex.Replace(text, "(?<=[a-zA-Z])([\u0800-\u4e00])(?=[\u0800-\u4e00]{0,})", " $1");

            #endregion

            #endregion

            if (ifUseChineseSymbol(lang))
            {
                //在英文与数字字符之间增加空格
                text = Regex.Replace(text, "([A-Za-z])([0-9])", "$1 $2");
            }

            //去掉计量百分号或者单位之间的空格
            text = Regex.Replace(text, "([0-9])([ ]*)([%°])", "$1$3");


            #region 标点前后空格

            //去除与中文符号之间的（半角）空格
            text = Regex.Replace(text, "(\u0020)(?=[" + strChineseSymbol + "])", string.Empty);
            //去除与中文符号之间的（全角）空格
            text = Regex.Replace(text, "(\u3000)(?=[" + strChineseSymbol + "])", string.Empty);
            //去除与英文符号之间的（半角）空格
            text = Regex.Replace(text, "(\u0020)(?=[" + strEnglishSymbol + "])", string.Empty);
            //去除与英文符号之间的（全角）空格
            text = Regex.Replace(text, "(\u3000)(?=[" + strEnglishSymbol + "])", string.Empty);

            //去除与中文符号之间的（半角）空格
            text = Regex.Replace(text, "(?<=[" + strChineseSymbol + "])(\u0020)", string.Empty);
            //去除与中文符号之间的（全角）空格
            text = Regex.Replace(text, "(?<=[" + strChineseSymbol + "])(\u3000)", string.Empty);
            //去除与英文符号之间的（半角）空格
            text = Regex.Replace(text, "(?<=[" + strEnglishSymbol + "])(\u0020)", string.Empty);
            //去除与英文符号之间的（全角）空格
            text = Regex.Replace(text, "(?<=[" + strEnglishSymbol + "])(\u3000)", string.Empty);

            #endregion

            return text;
        }

        //！？。，；：、“”‘’『』「」〖〗【】《》（）
        static string strChineseSymbol = @"\uff01\uff1f\u3002\uff0c\uff1b\uff1a\u3001\u201c\u201d\u2018\u2019\u300e\u300f\u300c\u300d\u3016\u3017\u3010\u3011\u300a\u300b\uff08\uff09";
        //!?.,;:'\"[]<>()
        static string strEnglishSymbol = @"\u0021\u003f\u002e\u002c\u003b\u003a\u0027\u0022\u005b\u005d\u003c\u003e\u0028\u0029";

        /// <summary>
        /// 重复标点校正
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        internal static string RemoveDuplicateSymbol(string text)
        {
            // 不重复使用中文标点符号，重复时只保留第一个
            text = Regex.Replace(text, "([" + strChineseSymbol + "])\\1{1,}", "$1");
            // 不重复使用英文标点符号，重复时只保留第一个
            text = Regex.Replace(text, "([" + strEnglishSymbol + "])\\1{1,}", "$1");

            // 正确使用省略号
            text = Regex.Replace(text, "([。\\.]){3,}|(…){1}", "……");
            text = Regex.Replace(text, "(……){2,}", "……");
            return text;
        }

        static readonly Dictionary<string, string> Full2HalfDictionary = new Dictionary<string, string> {{ "０" , "0"},{ "１" , "1"},{ "２" , "2"},{ "３" , "3"},{ "４" , "4"},
                    { "５" , "5"},{ "６" , "6"},{ "７" , "7"},{ "８" , "8"},{ "９" , "9"},
                    { "Ａ" , "A"},{ "Ｂ" , "B"},{ "Ｃ" , "C"},{ "Ｄ" , "D"},{ "Ｅ" , "E"},
                    { "Ｆ" , "F"},{ "Ｇ" , "G"},{ "Ｈ" , "H"},{ "Ｉ" , "I"},{ "Ｊ" , "J"},
                    { "Ｋ" , "K"},{ "Ｌ" , "L"},{ "Ｍ" , "M"},{ "Ｎ" , "N"},{ "Ｏ" , "O"},
                    { "Ｐ" , "P"},{ "Ｑ" , "Q"},{ "Ｒ" , "R"},{ "Ｓ" , "S"},{ "Ｔ" , "T"},
                    { "Ｕ" , "U"},{ "Ｖ" , "V"},{ "Ｗ" , "W"},{ "Ｘ" , "X"},{ "Ｙ" , "Y"},
                    { "Ｚ" , "Z"},{ "ａ" , "a"},{ "ｂ" , "b"},{ "ｃ" , "c"},{ "ｄ" , "d"},
                    { "ｅ" , "e"},{ "ｆ" , "f"},{ "ｇ" , "g"},{ "ｈ" , "h"},{ "ｉ" , "i"},
                    { "ｊ" , "j"},{"ｋ"  , "k"},{"ｌ" , "l"},{"ｍ" , "m"},{"ｎ" , "n"},
                    {"ｏ" , "o"},{ "ｐ"  , "p"},{ "ｑ" , "q"},{ "ｒ" , "r"},{ "ｓ" , "text"},
                    {"ｔ" , "t"},{"ｕ"   , "u"},{"ｖ" , "v"},{"ｗ" , "w"},{"ｘ" , "x"},
                    {"ｙ" , "y"},{"ｚ"   , "z"},
                    {"－" , "-"},{"　"   , " "},{"／" , "/"},
                    {"％" , "%"},{"＃"  , "#"},{"＠" , "@"},{"＆" , "&"},{"＜" , "<"},
                    {"＞" , ">"},{"［"  , "["},{"］" , "]"},{"｛" , "{"},{"｝" , "}"},
                    {"＼" , "\\"},{"｜" , "|"},{"＋" , "+"},{"＝" , "="},{"＿" , "_"},
                    {"＾" , "^"},{"￣"  , "~"},{"｀" , "`" }};

        static readonly string[] ChineseSymbol = {
            "，", "；", "：", "？", "！", "……", "—", "～", "（", "）", "【", "】", "“", "”", "‘", "’"
        };
        static readonly string[] EnglishSymbol = {
            ",", ";", ":", "?", "!", "…", "-", "~", "(", ")", "[", "]", "\"", "\"","'","'"
        };

        /// <summary>
        /// 有限度的全角转半角（英文、数字、空格以及某些特殊字符等使用半角字符）
        /// https://github.com/mzlogin/chinese-copywriting-guidelines#全角和半角
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        internal static string Full2Half(string text)
        {
            foreach (var key in Full2HalfDictionary.Keys)
            {
                text = Regex.Replace(text, "[" + key + "]", Full2HalfDictionary[key]);
            }
            return text;
        }

        /// <summary>
        /// 根据语言类型，把文字中的标点符号转换为中/英文标点
        /// </summary>
        /// <param name="s">识别结果文字</param>
        /// <param name="lang">识别的语言类型字符串</param>
        /// <returns>转换以后的文字</returns>
        internal static string TransSymbol(string s, string lang)
        {
            if (ifUseChineseSymbol(lang))
            {
                for (int i = 0; i < ChineseSymbol.Length; i++)
                {
                    s = Regex.Replace(s, "[" + EnglishSymbol[i] + "]", ChineseSymbol[i]);
                }
                s = Regex.Replace(s, "[\"|“]([^\"“”]+?)[\"|”]", "“$1”");
                s = Regex.Replace(s, "['|‘]([^'‘’]+?)['|’]", "‘$1’");
            }
            else
            {
                for (int i = 0; i < EnglishSymbol.Length; i++)
                {
                    s = Regex.Replace(s, "[" + ChineseSymbol[i] + "]", EnglishSymbol[i]);
                }
            }

            //时间中间为英文间隔
            s = Regex.Replace(s, "([0-9])：([0-9])", "$1:$2");
            return s;
        }

        internal static Dictionary<string, bool> langUseChineseSymbols = new Dictionary<string, bool>() { { "zh", true }, { "jp", true }, { "kr", true } };

        /**
        * 是否使用中文标点符号
        * @param  {String} lang franc-min 识别的语言类型字符串
        * @return {Boolean} [description]
*/
        internal static bool ifUseChineseSymbol(string lang)
        {
            return langUseChineseSymbols.ContainsKey(lang) && langUseChineseSymbols[lang];
        }
    }
}
