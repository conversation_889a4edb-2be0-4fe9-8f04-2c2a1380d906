using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Emgu.CV;
using Microsoft.ML.OnnxRuntime;
using Microsoft.ML.OnnxRuntime.Tensors;

namespace OcrLib
{
    internal class CrnnNet
    {
        private readonly float[] MeanValues = new float[3] { 127.5f, 127.5f, 127.5f };

        private readonly float[] NormValues = new float[3]
        {
            2f / 255f,
            2f / 255f,
            2f / 255f
        };

        private InferenceSession crnnNet;

        private List<string> keys;

        private List<string> inputNames;

        ~CrnnNet()
        {
            crnnNet.Dispose();
        }

        public void InitModel(string path, string keysPath, int numThread)
        {
            try
            {
                SessionOptions options = new SessionOptions
                {
                    GraphOptimizationLevel = GraphOptimizationLevel.ORT_ENABLE_EXTENDED,
                    InterOpNumThreads = numThread,
                    IntraOpNumThreads = numThread
                };
                crnnNet = new InferenceSession(path, options);
                inputNames = crnnNet.InputMetadata.Keys.ToList();
                keys = InitKeys(keysPath);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message + ex.StackTrace);
                throw ex;
            }
        }

        private List<string> InitKeys(string path)
        {
            StreamReader streamReader = new StreamReader(path, Encoding.UTF8);
            List<string> list = new List<string>();
            string item;
            while ((item = streamReader.ReadLine()) != null)
            {
                list.Add(item);
            }
            return list;
        }

        public List<TextLine> GetTextLines(List<Mat> partImgs, bool isPaddle)
        {
            TextLine[] textLines = new TextLine[partImgs.Count];
            Parallel.For(0, partImgs.Count, new ParallelOptions
            {
                MaxDegreeOfParallelism = Ocr.NMaxDegreeOfParallelism
            }, delegate (int i)
            {
                textLines[i] = GetTextLine(partImgs[i], isPaddle);
            });
            return textLines.ToList();
        }

        private TextLine GetTextLine(Mat src, bool isPaddle)
        {
            TextLine result = new TextLine();
            int crnnDstHeight = isPaddle ? 48 : 32;
            float scale = crnnDstHeight / (float)src.Rows;
            int dstWidth = (int)(src.Cols * scale);

            Mat srcResize = new Mat();
            CvInvoke.Resize(src, srcResize, new Size(dstWidth, crnnDstHeight));
            Tensor<float> inputTensors = OcrUtils.SubstractMeanNormalize(srcResize, MeanValues, NormValues);
            List<NamedOnnxValue> inputs = new List<NamedOnnxValue> { NamedOnnxValue.CreateFromTensor(inputNames[0], inputTensors) };
            try
            {
                using (IDisposableReadOnlyCollection<DisposableNamedOnnxValue> source = crnnNet.Run(inputs))
                {
                    DisposableNamedOnnxValue[] array = source.ToArray();
                    ReadOnlySpan<int> dimensions = array[0].AsTensor<float>().Dimensions;
                    float[] srcData = array[0].AsEnumerable<float>().ToArray();
                    result = isPaddle ? ScoreToPaddleTextLine(srcData, dimensions[1], dimensions[2]) : ScoreToChineseTextLine(srcData, dimensions[0], dimensions[2]);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message + ex.StackTrace);
            }
            return result;
        }

        private TextLine ScoreToPaddleTextLine(float[] srcData, int rows, int cols)
        {
            StringBuilder sb = new StringBuilder();
            int lastIndex = 0;
            for (int i = 0; i < rows; i++)
            {
                int maxIndex = 0;
                float maxValue = -1000f;
                for (int j = 0; j < cols; j++)
                {
                    int idx = i * cols + j;
                    if (srcData[idx] > maxValue)
                    {
                        maxIndex = j;
                        maxValue = srcData[idx];
                    }
                }
                if (maxIndex > 0 && maxIndex < keys.Count && (i <= 0 || maxIndex != lastIndex))
                {
                    sb.Append(keys[maxIndex]);
                }
                lastIndex = maxIndex;
            }
            return new TextLine
            {
                Text = sb.ToString(),
            };
        }

        private TextLine ScoreToChineseTextLine(float[] srcData, int rows, int cols)
        {
            StringBuilder stringBuilder = new StringBuilder();
            int num = 0;
            for (int i = 0; i < rows; i++)
            {
                int num2 = 0;
                float num3 = -1000f;
                List<float> list2 = new List<float>();
                for (int j = 0; j < cols; j++)
                {
                    float item = (float)Math.Exp(srcData[i * cols + j]);
                    list2.Add(item);
                }
                float num4 = list2.Sum();
                for (int k = 0; k < cols; k++)
                {
                    float num5 = list2[k] / num4;
                    if (num5 > num3)
                    {
                        num3 = num5;
                        num2 = k;
                    }
                }
                if (num2 > 0 && num2 < keys.Count && (i <= 0 || num2 != num))
                {
                    stringBuilder.Append(keys[num2 - 1]);
                }
                num = num2;
            }
            return new TextLine
            {
                Text = stringBuilder.ToString(),
            };
        }
    }
}
