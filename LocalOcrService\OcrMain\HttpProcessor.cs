﻿using System;
using System.Collections;
using System.IO;
using System.Net.Sockets;
using System.Threading;

namespace OcrMain
{
    public class HttpProcessor
    {
        private const int BUF_SIZE = 4096;
        public TcpClient socket;
        public HttpServer srv;
        private Stream inputStream;
        public StreamWriter outputStream;
        public string http_method;
        public string http_url;
        public string http_protocol_versionstring;
        public Hashtable httpHeaders = new Hashtable();
        private static int MAX_POST_SIZE = 10485760;

        public HttpProcessor(TcpClient s, HttpServer srv)
        {
            socket = s;
            this.srv = srv;
        }

        private string streamReadLine(Stream inputStream)
        {
            string text = "";
            while (true)
            {
                int num = inputStream.ReadByte();
                if (num == 10)
                {
                    break;
                }
                if (num != 13)
                {
                    if (num == -1)
                    {
                        Thread.Sleep(1);
                    }
                    else
                    {
                        text += Convert.ToChar(num);
                    }
                }
            }
            return text;
        }

        public void process()
        {
            try
            {
                inputStream = new BufferedStream(socket.GetStream());
                outputStream = new StreamWriter(new BufferedStream(socket.GetStream()));
                try
                {
                    parseRequest();
                    readHeaders();
                    if (http_method.Equals("GET"))
                    {
                        handleGETRequest();
                    }
                    else if (http_method.Equals("POST"))
                    {
                        handlePOSTRequest();
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Exception: " + ex);
                    writeFailure();
                }
                outputStream.Flush();
                inputStream = null;
                outputStream = null;
                socket.Close();
            }
            catch
            {
            }
        }

        public void parseRequest()
        {
            string text = streamReadLine(inputStream);
            string[] array = text.Split(' ');
            if (array.Length != 3)
            {
                throw new Exception("invalid http request line");
            }
            http_method = array[0].ToUpper();
            http_url = array[1];
            http_protocol_versionstring = array[2];
        }

        public void readHeaders()
        {
            string text;
            while ((text = streamReadLine(inputStream)) != null)
            {
                if (text.Equals(""))
                {
                    return;
                }
                int num = text.IndexOf(':');
                if (num == -1)
                {
                    throw new Exception("invalid http header line: " + text);
                }
                string key = text.Substring(0, num);
                int num2 = num + 1;
                while (num2 < text.Length && text[num2] == ' ')
                {
                    num2++;
                }
                string value = text.Substring(num2, text.Length - num2);
                httpHeaders[key] = value;
            }
        }

        public void handleGETRequest()
        {
            srv.handleGETRequest(this);
        }
        public void handlePOSTRequest()
        {
            using (var memoryStream = new MemoryStream())
            {
                if (httpHeaders.ContainsKey("Content-Length"))
                {
                    var num = Convert.ToInt32(httpHeaders["Content-Length"]);
                    if (num > MAX_POST_SIZE)
                    {
                        throw new Exception(string.Format("POST Content-Length({0}) too big for this simple server", num));
                    }
                    var buffer = new byte[4096];
                    var i = num;
                    while (i > 0)
                    {
                        int num2 = inputStream.Read(buffer, 0, Math.Min(4096, i));
                        if (num2 != 0)
                        {
                            i -= num2;
                            memoryStream.Write(buffer, 0, num2);
                        }
                        else
                        {
                            if (i == 0)
                            {
                                memoryStream.Seek(0L, SeekOrigin.Begin);
                            }
                            throw new Exception("client disconnected during post");
                        }
                    }
                    memoryStream.Seek(0L, SeekOrigin.Begin);
                    srv.handlePOSTRequest(this, new StreamReader(memoryStream));
                }
            }
        }

        public void writeSuccess()
        {
            outputStream.WriteLine("HTTP/1.0 200 OK");
            outputStream.WriteLine("Content-Type: text/plain; charset=UTF-8");
            outputStream.WriteLine("Connection: close");
            outputStream.WriteLine("");
        }

        public void writeFailure()
        {
            outputStream.WriteLine("HTTP/1.0 404 File not found");
            outputStream.WriteLine("Connection: close");
            outputStream.WriteLine("");
        }
    }
}
