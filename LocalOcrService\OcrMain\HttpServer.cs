﻿using System.IO;
using System.Net.Sockets;
using System.Threading;

namespace OcrMain
{
    public abstract class HttpServer
    {
        private bool is_active = true;
        private TcpListener listener;
        protected int port;

        protected HttpServer(int port)
        {
            this.port = port;
        }

        public abstract void handleGETRequest(HttpProcessor p);

        public abstract void handlePOSTRequest(HttpProcessor p, StreamReader inputData);

        public void Listen()
        {
            listener = new TcpListener(port);
            listener.Start();
            while (is_active)
            {
                try
                {
                    HttpProcessor @object = new HttpProcessor(listener.AcceptTcpClient(), this);
                    new Thread(@object.process).Start();
                    Thread.Sleep(1);
                }
                catch
                {
                }
            }
        }
    }
}
