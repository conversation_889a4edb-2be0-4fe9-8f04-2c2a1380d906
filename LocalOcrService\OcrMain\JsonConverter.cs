﻿using OcrLib;
using System;
using System.Collections.Generic;
using System.Drawing;

namespace OcrMain
{
    public class JsonConverter
    {

        public static OcrResult ConvertJsonToTargetList(string strJson, string strStart, string strEnd, int RegionType, string strRegionStart, string strRegionEnd, Dictionary<string, string> dicConfig)
        {
            var targetObject = new OcrResult() { StrRes = "Auto Convert", TextBlocks = new List<TextBlock>() };
            if (!string.IsNullOrEmpty(strStart))
            {
                strJson = strJson.Substring(strJson.IndexOf(strStart) + strStart.Length);
            }
            if (!string.IsNullOrEmpty(strEnd))
            {
                strJson = strJson.Substring(0, strJson.LastIndexOf(strEnd) + strEnd.Length);
            }
            var lstTmp = OcrResultUtil.JavaScriptSerializer.Deserialize<List<Dictionary<string, object>>>(strJson);

            foreach (var item in lstTmp)
            {
                var textBlock = new TextBlock();
                foreach (var config in dicConfig)
                {
                    var sourceField = config.Key;
                    var targetField = config.Value;

                    if (item.TryGetValue(sourceField, out var value))
                    {
                        var propertyInfo = typeof(TextBlock).GetProperty(targetField);
                        if (propertyInfo != null && propertyInfo.CanWrite)
                        {
                            if (targetField == "BoxPoints")
                            {
                                var rect = new Rectangle();
                                var strLocationInfo = OcrResultUtil.JavaScriptSerializer.Serialize(value);
                                if (!string.IsNullOrEmpty(strRegionStart))
                                {
                                    strLocationInfo = strLocationInfo.Substring(strLocationInfo.IndexOf(strRegionStart) + strRegionStart.Length);
                                }
                                if (!string.IsNullOrEmpty(strRegionEnd))
                                {
                                    strLocationInfo = strLocationInfo.Substring(0, strLocationInfo.IndexOf(strRegionEnd) + strRegionEnd.Length);
                                }
                                switch (RegionType)
                                {
                                    case 1:
                                        rect = OcrResultUtil.JavaScriptSerializer.Deserialize<Rectangle>(strLocationInfo);
                                        break;
                                    default:
                                        strLocationInfo = strLocationInfo
                                       .Replace("[", "").Replace("]", "")
                                       .Replace("{", "").Replace("}", "")
                                       .Replace("\"", "").Replace(":", "")
                                       .Replace("\r", "").Replace("\t", "")
                                       .Replace("\n", "").Replace(" ", "")
                                       .Trim();
                                        var spilt = strLocationInfo.Split(new[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                                        if (spilt.Length == 8)
                                        {
                                            //左上角坐标(0,1)，右上角坐标(2,3)，右下角坐标(4,5)，左下角坐标(6,7)
                                            rect = new Rectangle
                                            {
                                                X = (int)Math.Min(double.Parse(spilt[0]), double.Parse(spilt[6])),
                                                Y = (int)Math.Min(double.Parse(spilt[1]), double.Parse(spilt[3])),
                                                Width = (int)Math.Max(double.Parse(spilt[2]) - double.Parse(spilt[0]), double.Parse(spilt[4]) - double.Parse(spilt[6])),
                                                Height = (int)Math.Max(double.Parse(spilt[7]) - double.Parse(spilt[1]), double.Parse(spilt[5]) - double.Parse(spilt[3])),
                                            };
                                            if (rect.Width < 0 && rect.Height < 0)
                                            {
                                                //右下角坐标(0,1)，左下角坐标(2,3)，左上角坐标(4,5)，右上角坐标(6,7)
                                                rect = new Rectangle
                                                {
                                                    X = (int)Math.Min(double.Parse(spilt[4]), double.Parse(spilt[2])),
                                                    Y = (int)Math.Min(double.Parse(spilt[5]), double.Parse(spilt[7])),
                                                    Width = (int)Math.Max(double.Parse(spilt[6]) - double.Parse(spilt[4]), double.Parse(spilt[0]) - double.Parse(spilt[2])),
                                                    Height = (int)Math.Max(double.Parse(spilt[3]) - double.Parse(spilt[5]), double.Parse(spilt[1]) - double.Parse(spilt[7])),
                                                };
                                            }
                                            if (rect.Width < 0 && rect.Height < 0)
                                            {
                                                //左下角坐标(0,1)，右下角坐标(2,3)，右上角坐标(4,5)，左上角坐标(6,7)
                                                rect = new Rectangle
                                                {
                                                    X = (int)Math.Min(double.Parse(spilt[6]), double.Parse(spilt[2])),
                                                    Y = (int)Math.Min(double.Parse(spilt[5]), double.Parse(spilt[7])),
                                                    Width = (int)Math.Max(double.Parse(spilt[4]) - double.Parse(spilt[6]),
                                                    double.Parse(spilt[2]) - double.Parse(spilt[0])),
                                                    Height = (int)Math.Max(double.Parse(spilt[1]) - double.Parse(spilt[7]),
                                                    double.Parse(spilt[3]) - double.Parse(spilt[5])),
                                                };
                                            }
                                        }
                                        else if (spilt.Length == 4)
                                        {
                                            rect = new Rectangle
                                            {
                                                X = (int)double.Parse(spilt[0]),
                                                Y = (int)double.Parse(spilt[1]),
                                                Width = (int)(double.Parse(spilt[2]) - double.Parse(spilt[0])),
                                                Height = (int)(double.Parse(spilt[3]) - double.Parse(spilt[1])),
                                            };
                                        }
                                        break;
                                }
                                if (!rect.IsEmpty)
                                {
                                    typeof(TextBlock).GetProperty("BoundingRect").SetValue(textBlock, rect);
                                }
                            }
                            else
                            {
                                propertyInfo.SetValue(textBlock, Convert.ChangeType(value, propertyInfo.PropertyType));
                            }
                        }
                    }
                }
                targetObject.TextBlocks.Add(textBlock);
            }

            return targetObject;
        }
    }
}
