﻿using OcrLib;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;

namespace OcrMain
{
    public class MyHttpServer : HttpServer
    {
        public MyHttpServer(int port)
            : base(port)
        {
            Task.Factory.StartNew(InitModules);
        }

        public static OnnxParam DefaultPaddleParam = new OnnxParam()
        {
            boxScoreThresh = 0.5f,
            boxThresh = 0.3f,
            doAngle = true,
            isPaddle = true,
            maxSideLen = 1024,
            mostAngle = true,
            padding = 50,
            unClipRatio = 1.6f
        };

        public static OnnxParam DefaultChineseLiteParam = new OnnxParam()
        {
            boxScoreThresh = 0.618f,
            boxThresh = 0.3f,
            doAngle = true,
            isPaddle = false,
            maxSideLen = 1024,
            mostAngle = true,
            padding = 50,
            unClipRatio = 2
        };

        private static List<Ocr> OcrEngine = new List<Ocr>();

        public static int NMaxThread { get; set; } = 10;

        private static void InitModules()
        {
            //internal enum LocalOcrType
            //{
            //    飞浆Mobile = 90001,
            //    飞浆Server = 90002,
            //    中文识别Lite = 90003,
            //    WindowsOcr = 90004,
            //    QQNT = 90010,
            //    WeiXin = 90011,
            //}

            //模型1初始化
            var appPath = AppDomain.CurrentDomain.BaseDirectory + "models";
            foreach (var modelPath in Directory.GetDirectories(appPath))
            {
                if (InitModel(modelPath, out Ocr ocr))
                {
                    ocr.BasePath = modelPath;
                    OcrEngine.Add(ocr);
                }
            }

            //Environment.OSVersion.Version >= new Version("10.0.18362.0") &&
            if (LocalOcrHelper.InitLanguage())
            {
                OcrEngine.Add(new Ocr("WindowsOcr", 90004));
            }
        }

        private static bool InitModel(string path, out Ocr ocrEngin)
        {
            var result = false;
            ocrEngin = null;
            path = path.TrimEnd('\\');
            try
            {
                var infoFile = path + "\\info.txt";
                if (File.Exists(infoFile))
                {
                    ocrEngin = OcrResultUtil.JavaScriptSerializer.Deserialize<Ocr>(File.ReadAllText(infoFile));
                }
                else
                {
                    var modelName = path.Substring(path.LastIndexOf("\\") + "\\".Length);
                    var modelCode = 0;
                    //没取到值的，兼容处理
                    if (modelCode <= 0)
                    {
                        switch (modelName)
                        {
                            case "飞浆Mobile":
                                modelCode = 90001;
                                break;
                            case "飞浆Server":
                                modelCode = 90002;
                                break;
                            case "中文识别Lite":
                                modelCode = 90003;
                                break;
                        }
                    }
                    ocrEngin = new Ocr(modelName, modelCode);
                    // 判断是否是ONNX
                    ocrEngin.IsONNX = Directory.GetFiles(path).Any(p => p.EndsWith(".onnx"));
                    if (ocrEngin.IsONNX)
                    {
                        ocrEngin.OnnxParam = modelName.Contains("飞浆") ? DefaultPaddleParam : DefaultChineseLiteParam;
                    }
                    //File.WriteAllText(infoFile, OcrResultUtil.JavaScriptSerializer.Serialize(ocrEngin), System.Text.Encoding.UTF8);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(path + " 初始化失败：" + e.Message);
            }
            if (ocrEngin?.IsONNX == true)
            {
                var clsPath = path + "\\" + "cls.onnx";
                var detPath = path + "\\" + "det.onnx";
                var recPath = path + "\\" + "rec.onnx";
                var keysPath = path + "\\" + "keys.txt";
                if (File.Exists(detPath) && File.Exists(clsPath) && File.Exists(recPath) && File.Exists(keysPath))
                {
                    ocrEngin.InitModels(detPath, clsPath, recPath, keysPath, NMaxThread);
                    result = true;
                }
                else
                {
                    Console.WriteLine(ocrEngin.Name + " 初始化失败");
                }
            }
            else
            {
                result = true;
            }

            return result;
        }

        static string SubString(string strSource, string strSpilt, string strEnd = "")
        {
            return strSource.SubStringHorspool(strSpilt, strEnd).Trim();
        }

        public override void handleGETRequest(HttpProcessor p)
        {
            try
            {
                p.writeSuccess();
                var resultStr = "no";
                if (p.http_url.StartsWith("/Code.do"))
                {
                    try
                    {
                        var type = SubString(p.http_url, "type=", "&");
                        if (Equals(type, "state"))
                        {
                            resultStr = $"本地识别引擎：\n" + string.Join("\n", OcrEngine.Select(q => q.Name + ":✔").ToArray());
                        }
                        else if (Equals(type, "config"))
                        {
                            var strOcrType = SubString(p.http_url, "ocr=", "&");
                            if (!int.TryParse(strOcrType, out int ocrType))
                            {
                                ocrType = 0;
                            }
                            var engine = OcrEngine.FirstOrDefault(eg => Equals(ocrType, eg.Code));
                            if (engine != null)
                            {
                                if (engine.EnableConfig)
                                {
                                    if (!string.IsNullOrEmpty(engine.ConfigParam))
                                    {
                                        var strTmp = ExecCmd(engine.ConfigParam.Replace("[BasePath]", engine.BasePath));
                                    }
                                    else
                                        Process.Start(engine.BasePath);
                                }
                            }
                        }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message + Environment.NewLine + oe.StackTrace + Environment.NewLine + oe.InnerException?.Message + Environment.NewLine + oe.InnerException?.StackTrace);
                    }
                }
                p.outputStream.Write(resultStr);
            }
            catch
            {
            }
        }

        public override void handlePOSTRequest(HttpProcessor p, StreamReader inputData)
        {
            try
            {
                p.writeSuccess();
                var resultStr = "no";
                if (p.http_url.StartsWith("/Code.do"))
                {
                    try
                    {
                        var img = SubString(inputData.ReadToEnd(), "img=", "");
                        var type = int.Parse(SubString(p.http_url, "type=", "&"));
                        var strOcrType = SubString(p.http_url, "ocr=", "&");
                        var strParam = SubString(p.http_url, "param=", "&");
                        var id = SubString(p.http_url, "id=", "");
                        if (type > 0 && !string.IsNullOrEmpty(img))
                        {
                            if (!int.TryParse(strOcrType, out int ocrType))
                            {
                                ocrType = 0;
                            }
                            bool isAutoFull2Half = !Equals(SubString(p.http_url, "autofull2half=", "&"), "0");
                            bool isAutoSpace = !Equals(SubString(p.http_url, "autospace=", "&"), "0");
                            bool isAutoSymbol = !Equals(SubString(p.http_url, "autosymbol=", "&"), "0");
                            bool isAutoDuplicateSymbol = !Equals(SubString(p.http_url, "autoduplicatesymbol=", "&"), "0");
                            resultStr = processOcr(ocrType, type, img, id, strParam, isAutoFull2Half, isAutoSpace, isAutoSymbol, isAutoDuplicateSymbol);
                        }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message + Environment.NewLine + oe.StackTrace + Environment.NewLine + oe.InnerException?.Message + Environment.NewLine + oe.InnerException?.StackTrace);
                    }
                }
                p.outputStream.Write(resultStr);
            }
            catch
            {
            }
        }

        private OcrLib.OcrResult GetOcrResult(byte[] byt, Ocr engine, string strParam)
        {
            var result = new OcrLib.OcrResult();
            //本地识别
            if (Equals(engine.Code, 90004))
            {
                result = LocalOcrHelper.Detect(byt);
            }
            else
            {
                if (engine != null)
                {
                    if (engine.IsONNX)
                    {
                        result = engine.Detect(byt, engine.OnnxParam.padding, engine.OnnxParam.maxSideLen, engine.OnnxParam.boxScoreThresh
                                , engine.OnnxParam.boxThresh, engine.OnnxParam.unClipRatio, engine.OnnxParam.doAngle, engine.OnnxParam.mostAngle, engine.OnnxParam.isPaddle);
                    }
                    else
                    {
                        var file = string.Empty;
                        try
                        {
                            strParam = engine.Param.Replace("[BasePath]", engine.BasePath);
                            //Base64
                            if (engine.ParamType == 0)
                            {
                                strParam = strParam.Replace("[Base64]", Convert.ToBase64String(byt));
                            }
                            else//File
                            {
                                file = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString() + ".png");
                                //var file = "F:\\1.jpg";
                                File.WriteAllBytes(file, byt);
                                strParam = strParam.Replace("[ImageFile]", file);
                            }
                            var strTmp = ExecCmd(strParam);
                            if (!string.IsNullOrEmpty(strTmp))
                            {
                                if (engine.NeedParse)
                                {
                                    //engine.StrDataStart = "\"Lines\":";
                                    //engine.StrDataEnd = "]";
                                    //engine.RegionType = 1;
                                    //engine.StrRegionStart = "\"BoundingRect\":";
                                    //engine.StrRegionEnd = "}";
                                    //engine.DicConfig = new Dictionary<string, string>();
                                    //engine.DicConfig.Add("Text", "Text");
                                    //engine.DicConfig.Add("Words", "BoxPoints");
                                    result = JsonConverter.ConvertJsonToTargetList(strTmp, engine.StrDataStart, engine.StrDataEnd, engine.RegionType, engine.StrRegionStart, engine.StrRegionEnd, engine.DicConfig);
                                }
                                else
                                {
                                    strTmp = SubString(strTmp, "<OcrResult>", "</OcrResult>");
                                    if (!string.IsNullOrEmpty(strTmp))
                                    {
                                        result = OcrResultUtil.JavaScriptSerializer.Deserialize<OcrResult>(strTmp);
                                    }
                                }
                            }
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe.Message);
                        }
                        finally
                        {
                            try
                            {
                                if (!string.IsNullOrEmpty(file))
                                    File.Delete(file);
                            }
                            catch { }
                        }
                    }
                }
            }
            return result;
        }

        string ExecCmd(string str)
        {
            //process用于调用外部程序
            using (var p = new Process())
            {
                //调用cmd.exe
                p.StartInfo.FileName = "cmd.exe";
                //不显示程序窗口
                p.StartInfo.CreateNoWindow = true;
                //是否指定操作系统外壳进程启动程序
                p.StartInfo.UseShellExecute = false;
                //可能接受来自调用程序的输入信息
                //重定向标准输入
                p.StartInfo.RedirectStandardInput = true;
                //重定向标准输出
                p.StartInfo.RedirectStandardOutput = true;
                //重定向错误输出
                p.StartInfo.RedirectStandardError = true;
                //启动程序
                p.Start();
                //输入命令
                p.StandardInput.WriteLine(str);
                //一定要关闭。
                p.StandardInput.WriteLine("exit");
                return p.StandardOutput.ReadToEnd();
            }
        }

        private string processOcr(int ocrType, int engineType, string img, string id, string strParam,
            bool isAutoFull2Half, bool isAutoSpace, bool isAutoSymbol, bool isAutoDuplicateSymbol)
        {
            var byt = Convert.FromBase64String(HttpUtility.UrlDecode(img));
            var engine = OcrEngine.FirstOrDefault(p => Equals(engineType, p.Code));

            var ocrContent = new OcrContent
            {
                ocrType = ocrType,
                id = id,
                processId = engine.Code,
                processName = engine.Name,
                Identity = id,
                result = new ResultEntity()
            };

            //0:文字识别，1:表格识别
            if (ocrType == 0 || ocrType == 1)
            {
                var ocrResult = GetOcrResult(byt, engine, strParam);
                if (ocrResult != null)
                {
                    OcrResultUtil.ProcessResult(ocrContent.result, ocrResult, isAutoFull2Half, isAutoSpace, isAutoSymbol, isAutoDuplicateSymbol);
                }
            }
            else if (ocrType == 2)
            {
                var ocrResult = GetOcrResult(byt, engine, strParam);
                var tableResult = OcrUtils.DetectTableFromOcrResult(ocrResult);
                if (tableResult != null && tableResult.rows?.Count > 0)
                {
                    ocrContent.result.autoText = ocrContent.result.spiltText = OcrResultUtil.JavaScriptSerializer.Serialize(tableResult);
                    ocrContent.result.resultType = ocrType;
                }
            }
            else
            {
                ocrContent.result = new ResultEntity()
                {
                    verticalText = "{}"
                };
                ocrContent.result.autoText = ocrContent.result.spiltText = strNotSupport;
            }

            var resultStr = OcrResultUtil.JavaScriptSerializer.Serialize(ocrContent);
            return resultStr;
        }

        string strNotSupport = "暂不支持当前操作！";
    }
}
