using System;
using System.Collections.Generic;
using System.Reflection;

namespace OcrLib
{
    [Obfuscation]
    public sealed class OcrResult
    {
        [Obfuscation]
        public List<TextBlock> TextBlocks { get; set; }

        [Obfuscation]
        public string StrRes { get; set; }
    }

    [Obfuscation]
    [Serializable]
	public sealed class TableContentInfo
    {
        [Obfuscation]
        public List<TableRow> rows { get; set; }
    }

    [Obfuscation]
    [Serializable]
	public sealed class TableRow
    {
        [Obfuscation]
        public int index { get; set; }

        [Obfuscation]
        public List<TableCell> cells { get; set; }
    }

	[Serializable]
    [Obfuscation]
    public sealed class TableCell
    {
        [Obfuscation]
        public int index { get; set; }

        [Obfuscation]
        public string content { get; set; }
    }
}
