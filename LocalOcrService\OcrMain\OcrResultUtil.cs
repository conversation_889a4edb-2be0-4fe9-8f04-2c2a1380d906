﻿using OcrLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web.Script.Serialization;

namespace OcrMain
{
    public class OcrResultUtil
    {
        public static JavaScriptSerializer JavaScriptSerializer = new JavaScriptSerializer();

        public static string GetLang(string words)
        {
            var lang = Contain_CN(words) ? "zh" : (Contain_JP(words) ? "ja" : Contain_KR(words) ? "kr" : "en");
            return lang;
        }

        static bool Contain_CN(string str)
        {
            return Regex.IsMatch(str, "[\u4e00-\u9fa5]");
        }

        static bool Contain_JP(string str)
        {
            return Regex.IsMatch(str, "[\u0800-\u4e00]");
        }

        static bool Contain_KR(string str)
        {
            return Regex.IsMatch(str, "[\u3130-\u318f]") || Regex.IsMatch(str, "[\xAC00-\xD7A3]");
        }

        static Dictionary<string, string> langSeparators = new Dictionary<string, string>() { { "zh", "" }, { "jp", "" }, { "kr", "" } };

        /**
        * 行间连字符
        * 空白：中文，日语
        * 一个空格：其他
        * @param  {String} lang franc-min 识别的语言类型字符串
        * @return {Number}      行间连字符
*/
        static string lineSeparator(string lang)
        {
            return langSeparators.ContainsKey(lang) ? langSeparators[lang] : " ";
        }

        internal static void ProcessResult(ResultEntity result, OcrResult oldResult
            , bool isAutoFull2Half, bool isAutoSpace, bool isAutoSymbol, bool isAutoDuplicateSymbol)
        {
            var lstCells = oldResult?.TextBlocks.Select(p => new TextCellInfo()
            {
                words = p.Text,
                IsProcessed = false,
                PageIndex = 0,
                location = new LocationInfo()
                {
                    left = p.BoundingRect.IsEmpty ? Math.Min(p.BoxPoints[0].X, p.BoxPoints[3].X) - 50 : p.BoundingRect.Left,
                    top = p.BoundingRect.IsEmpty ? Math.Min(p.BoxPoints[0].Y, p.BoxPoints[1].Y) - 50 : p.BoundingRect.Top,
                    width = p.BoundingRect.IsEmpty ? Math.Min(p.BoxPoints[1].X - p.BoxPoints[0].X, p.BoxPoints[2].X - p.BoxPoints[3].X) : p.BoundingRect.Width,
                    height = p.BoundingRect.IsEmpty ? Math.Min(p.BoxPoints[3].Y - p.BoxPoints[0].Y, p.BoxPoints[2].Y - p.BoxPoints[1].Y) : p.BoundingRect.Height,
                    words = p.Text,
                }
            }).ToList();
            if (lstCells?.Count > 0)
            {
                lstCells.ForEach(p =>
                {
                    if (p.location != null && p.location.height < 5)
                    {
                        p.location.height = 10;
                    }
                    if (p.location != null && p.location.width < 5)
                    {
                        p.location.width = 5;
                    }
                });
                if (lstCells.Exists(p => p.location != null))
                {
                    result.verticalText = JavaScriptSerializer.Serialize(lstCells);
                }
                string StrContactCell = lineSeparator(GetLang(string.Join("", lstCells.Select(p => p.words))));
                //正常文字识别，从左到右,从上到下
                var lstLines = GetVerticalOcrResult(lstCells, StrContactCell);
                var strStart = lstLines?.Count > 1 ? "\t" : "";
                result.autoText = result.spiltText = (strStart + string.Join("\n" + strStart, lstLines.Select(p => p.words?.TrimEnd()))).TrimEnd();
                try
                {
                    if (lstLines.Exists(p => p.rectangle.IsEmpty))
                    {
                        return;
                    }
                    var lines = new LineProcess(lstLines, isAutoFull2Half, isAutoSpace, isAutoSymbol, isAutoDuplicateSymbol);
                    result.spiltLocText = lines.GetMergeResult(true);
                }
                catch { }
            }
        }
        private static double floatPercentDown = 1.25;
        private static double floatPercentUp = 0.75;

        private static List<LineInfo> GetVerticalOcrResult(List<TextCellInfo> lstCells, string StrContactCell)
        {
            List<LineInfo> lstLine = new List<LineInfo>();
            try
            {
                if (lstCells?.Count > 0)
                {
                    double? left = null;
                    double? top = null;
                    double? height = null;
                    double? width = null;
                    double? lastTop = null;
                    double? lastLeft = null;
                    double? lastTopTmp = null;
                    double? lastLeftTmp = null;
                    int? lastPageTmp = null;
                    TextCellInfo cell;
                    LineInfo line = null;
                    while ((cell = GetNextProcessFromLeftToRight(lstCells, ref lastTop)) != null)
                    {
                        bool isNextLine = cell.location == null;
                        if (!isNextLine & left.HasValue)
                        {
                            if (cell.PageIndex != lastPageTmp || lastPageTmp == null)
                            {
                                isNextLine = true;
                            }
                            else
                            {
                                if (lastTopTmp != lastTop || lastTop == null)
                                {
                                    //从上到下
                                    //如果高度偏离1/10以上
                                    if (cell.location.top > top && cell.location.top + cell.location.height > top + height * floatPercentDown)
                                        isNextLine = true;
                                    if (!isNextLine)
                                    {
                                        //从左向右
                                        //if (cell.rectangle.left > left && !CheckCross(new rectangle() { X = cell.rectangle.left, Y = cell.rectangle.top, Width = (int)(cell.rectangle.width * floatPercentDown), Height = cell.rectangle.height }, new rectangle() { X = left.Value, Y = cell.rectangle.top, Width = width.Value, Height = height.Value }))
                                        if (cell.location.left > left && cell.location.left + cell.location.width < left + width * floatPercentDown)
                                            isNextLine = true;
                                    }
                                }
                            }
                        }

                        if (line != null && isNextLine)
                        {
                            ProcessTextByLine(line, lstLine, StrContactCell);
                            line = null;
                        }
                        if (line == null)
                        {
                            line = new LineInfo { lstCell = new List<TextCellInfo>(), words = string.Empty };
                        }
                        if (isNextLine)
                        {
                            top = -1;
                            left = -1;
                            height = -1;
                            width = -1;
                        }
                        line.lstCell.Add(cell);
                        //if (!string.IsNullOrEmpty(cell.words))
                        //{
                        //    line.words += cell.words;
                        //}
                        //if (!string.IsNullOrEmpty(cell.trans))
                        //{
                        //    line.trans += cell.trans;
                        //}
                        if (cell.location != null)
                        {
                            top = cell.location.top;
                            left = cell.location.left;
                            height = cell.location.height;
                            width = cell.location.width;
                        }
                        if (lastTop.HasValue)
                        {
                            lastTopTmp = lastTop.Value;
                        }
                        if (lastLeft.HasValue)
                        {
                            lastLeftTmp = lastLeft.Value;
                        }
                        lastPageTmp = cell.PageIndex;
                        cell.IsProcessed = true;
                    }
                    if (line != null)
                    {
                        ProcessTextByLine(line, lstLine, StrContactCell);
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            lstLine.ForEach(p => p.Init());
            return lstLine;
        }

        private static void ProcessTextByLine(LineInfo line, List<LineInfo> lstLine, string StrContactCell)
        {
            line.lstCell = line.lstCell.OrderBy(p => p.location.left).ToList();
            line.words = string.Join(StrContactCell, line.lstCell.Select(p => p.words?.Replace("\n", " "))).TrimEnd();

            lstLine.Add(new LineInfo() { AverageWidth = line.AverageWidth, breakReason = line.breakReason, isBreak = line.isBreak, IsFromLeftToRight = line.IsFromLeftToRight, IsFromTopToDown = line.IsFromTopToDown, lang = line.lang, lstCell = line.lstCell, rectangle = line.rectangle, separator = line.separator, words = line.words });
        }

        private static TextCellInfo GetNextProcessFromLeftToRight(List<TextCellInfo> lstCells, ref double? lastTop)
        {
            TextCellInfo cell = null;

            var minPage = lstCells.Exists(p => !p.IsProcessed) ? lstCells.Where(p => !p.IsProcessed).Min(p => p.PageIndex) : 0;
            var lstNoProcessed = lstCells.Where(p => !p.IsProcessed && p.PageIndex == minPage);
            if (lstNoProcessed?.Count() > 0)
            {
                double? minTop = lastTop;
                List<TextCellInfo> lstTmp = null;
                if (!minTop.HasValue)
                {
                    minTop = lstNoProcessed.Min(p => p.location?.top);
                }
                else
                {
                    //Console.WriteLine("上次高度：" + lastTop);
                }
                lstTmp = GetFitResultByTop(minTop, lstNoProcessed);
                lastTop = null;
                if (lstTmp?.Count > 0)
                {
                    cell = lstTmp.OrderBy(p => p.location?.top).ThenBy(p => p.location?.left).FirstOrDefault();
                    if (lstTmp.Count > 1)
                    {
                        lastTop = minTop;
                    }
                }
                else
                {
                    cell = lstNoProcessed.FirstOrDefault();
                }
            }
            return cell;
        }

        private static List<TextCellInfo> GetFitResultByTop(double? top, IEnumerable<TextCellInfo> lstNoProcessed)
        {
            double? minTop;
            double? maxTop;

            List<TextCellInfo> lstTmp;
            double? height = lstNoProcessed.Where(p => p.location?.top <= top).Select(p => p.location?.height).Max();
            if (height == null)
            {
                top = lstNoProcessed.Where(p => p.IsProcessed == false).Select(p => p.location?.top).Min();
                height = lstNoProcessed.Where(p => p.location?.top <= top).Select(p => p.location?.height).Max();
            }
            minTop = top;
            //maxTop = minTop + height * 1.25;
            maxTop = lstNoProcessed.Where(p => p.location?.top >= top && p.location?.top <= top + height * floatPercentUp)
                .Select(p => p.location?.top + p.location?.height * floatPercentUp).Max();
            var tmpMaxHeight = lstNoProcessed.Where(p => p.location?.top <= maxTop).Select(p => p.location?.height).Max();
            if (tmpMaxHeight > height)
            {
                maxTop = lstNoProcessed.Where(p => p.location?.top >= top && p.location?.top <= top + tmpMaxHeight * floatPercentUp)
                    .Select(p => p.location?.top + p.location?.height * floatPercentUp).Max();
            }

            minTop = Math.Max(0, minTop ?? 0d);

            lstTmp = lstNoProcessed.Where(p => p.location?.top >= minTop && p.location?.top <= maxTop).ToList();
            return lstTmp;
        }

    }
}

