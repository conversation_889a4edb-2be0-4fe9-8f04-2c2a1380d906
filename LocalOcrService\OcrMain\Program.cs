﻿using System;
using System.Net;
using System.Threading;

namespace OcrMain
{
    class Program
    {
        #region 配置文件相关

        public static string ConfigPath { get; set; } = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + "\\OcrService\\";

        public static string ConfigName { get; set; } = "config.ini";

        public static string IniFileName => string.Format("{0}\\{1}", ConfigPath, ConfigName);

        #endregion

        static void Main(string[] args)
        {
#if DEBUG
            //var strJson = "{\"code\":100,\"data\":[{\"box\":[[416,26],[826,26],[826,46],[416,46]],\"score\":0.9336511282359853,\"text\":\"第十五章这突破的速度，也太假了吧？\"},{\"box\":[[63,72],[281,72],[281,91],[63,91]],\"score\":0.9395257234573364,\"text\":\"距离陆云洞府干里之外。\"},{\"box\":[[62,97],[404,97],[404,116],[62,116]],\"score\":0.9351317952660954,\"text\":\"一座人迹罕至，妖气缭绕的山脉之中。\"},{\"box\":[[61,121],[692,121],[692,144],[61,144]],\"score\":0.9430405305277917,\"text\":\"这地方叫做虎王山，因为有一只修炼百年的虎 妖在此驻扎，故而得名。\"},{\"box\":[[62,145],[261,147],[261,168],[62,165]],\"score\":0.9696218503846062,\"text\":\"一只鬼来到了 这里。\"},{\"box\":[[61,170],[425,172],[425,193],[61,190]],\"score\":0.9526669681072235,\"text\":\"这只长鬼，浑身狼，看起来受了 重伤。\"},{\"box\":[[63,197],[281,197],[281,218],[63,218]],\"score\":0.9775119478052313,\"text\":\"正是被猴子打跑的那只。\"},{\"box\":[[63,221],[444,221],[444,242],[63,242]],\"score\":0.9796839732872812,\"text\":\"他进入了虎王山，来到一处巨大的洞府中。\"},{\"box\":[[63,247],[279,247],[279,267],[63,267]],\"score\":0.9940127080137079,\"text\":\"寻到虎王，声音沙哑道：\"},{\"box\":[[62,273],[367,273],[367,293],[62,293]],\"score\":0.9025976955890656,\"text\":\"“大王，我去追那狐狸，失败了！”\"},{\"box\":[[63,296],[158,296],[158,317],[63,317]],\"score\":0.9439790844917297,\"text\":\"他话一落。\"},{\"box\":[[63,323],[507,323],[507,342],[63,342]],\"score\":0.8999964567747983,\"text\":\"黑暗中，便有两只灯笼大的黄色眼晴，挣了开来。\"},{\"box\":[[62,345],[836,345],[836,369],[62,369]],\"score\":0.9597387233295956,\"text\":\"随后，一只十米高，二十米长的斑驳巨虎，便站来起来，几个步子，便来到鬼周身 。\"},{\"box\":[[63,371],[319,371],[319,392],[63,392]],\"score\":0.9832587540149689,\"text\":\"他把头颅凑近鬼身前，道：\"},{\"box\":[[60,395],[390,393],[390,417],[60,419]],\"score\":0.9387835649883046,\"text\":\"“那狐狸修为低微，你竟然失败了？”\"},{\"box\":[[61,419],[774,422],[773,444],[61,442]],\"score\":0.9748846020017351,\"text\":\"这声音，虽然低沉，但是从虎王的喉咙里发出来，却 给人一种脑袋轰鸣的感觉。\"},{\"box\":[[63,447],[421,447],[421,467],[63,467]],\"score\":0.9321136573950449,\"text\":\"尤其是，这 低沉的声音中，还带着怒气！\"},{\"box\":[[61,470],[752,472],[752,494],[61,493]],\"score\":0.9224916202947497,\"text\":\"那鬼战战 藐，不敢直视虎王，连忙将整个事情，一五一十的告诉了虎王。\"},{\"box\":[[61,497],[122,497],[122,517],[61,517]],\"score\":0.8346756547689438,\"text\":\"“吼！”\"},{\"box\":[[63,523],[481,523],[481,542],[63,542]],\"score\":0.9693729102611541,\"text\":\"听完鬼的话，虎王勃然大怒，直接一声大吼！\"},{\"box\":[[62,545],[383,548],[383,568],[62,566]],\"score\":0.9331351183354855,\"text\":\"顿 时，山洞晃动，山脉中鸟兽惊惶。\"},{\"box\":[[62,572],[509,572],[509,592],[62,592]],\"score\":0.9571908194085826,\"text\":\"“真是岂有此理！一只猴子，竟然敢管本王的事！”\"},{\"box\":[[63,598],[465,598],[465,618],[63,618]],\"score\":0.909853720664978,\"text\":\"他心头震怒，连出气的声音，都像是在冒火。\"},{\"box\":[[62,623],[463,623],[463,642],[62,642]],\"score\":0.9757320553064346,\"text\":\"这时，黑暗之中，又有两只妖怪，走了出来。\"},{\"box\":[[62,646],[549,646],[549,668],[62,668]],\"score\":0.931299477815628,\"text\":\"一只是一只豹子，一只则是赤色巨蟒，均是身高体长。\"},{\"box\":[[62,671],[301,672],[301,693],[62,692]],\"score\":0.9889059017101923,\"text\":\"实力也都有练气化神境界。\"}]}";
            //var dicConfig = new Dictionary<string, string>();
            //dicConfig.Add("text", "Text");
            //dicConfig.Add("box", "BoxPoints");
            //JsonConverter.ConvertJsonToTargetList(strJson, "\"data\":", "]", dicConfig);
#endif
            if (args == null || args.Length != 2)
            {
                args = new string[2];
                args[0] = "8080";
                args[1] = "10";
            }
            using (new Mutex(true, "OcrServer", out var flag))
            {
                if (flag)
                {
                    int.TryParse(args[0], out int nPort);
                    if (nPort < 80 || nPort > 9999)
                    {
                        nPort = 8080;
                    }

                    int.TryParse(args[1], out int nThread);
                    if (nThread < 1 || nThread > 100)
                    {
                        nThread = 5;
                    }

                    InitThread();

                    HttpServer httpServer = new MyHttpServer(nPort);
                    MyHttpServer.NMaxThread = nThread;
                    Console.WriteLine("端口：{0}，线程：{1}", nPort, nThread);
                    httpServer.Listen();
                }
            }
        }

        static void InitThread()
        {
            try
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls | SecurityProtocolType.Ssl3;
                ServicePointManager.ServerCertificateValidationCallback = (obj, certificate, chain, sslPolicyErrors) => true;
                ServicePointManager.DefaultConnectionLimit = int.MaxValue;
                ServicePointManager.MaxServicePoints = int.MaxValue;
                ServicePointManager.MaxServicePointIdleTime = int.MaxValue;
                ServicePointManager.UseNagleAlgorithm = false;
                //ServicePointManager.CheckCertificateRevocationList = false;
                ServicePointManager.Expect100Continue = false;
                //ServicePointManager.SetTcpKeepAlive(true, 1000, 200);
                GlobalProxySelection.Select = GlobalProxySelection.GetEmptyWebProxy();
                WebRequest.DefaultWebProxy = null;

                ThreadPool.GetAvailableThreads(out var workerThreads, out var completionPortThreads);
                ThreadPool.SetMinThreads(10, 10);
                ThreadPool.SetMaxThreads(100, 100);
            }
            catch
            {
                // ignored
            }
        }

    }
}
