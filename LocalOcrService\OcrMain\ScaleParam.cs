using System;
using Emgu.CV;

namespace OcrLib
{
	internal class ScaleParam
	{
		public int SrcWidth { get; set; }

		public int SrcHeight { get; set; }

		public int DstWidth { get; set; }

		public int DstHeight { get; set; }

		public float ScaleWidth { get; set; }

		public float ScaleHeight { get; set; }

		public ScaleParam(int srcWidth, int srcHeight, int dstWidth, int dstHeight, float scaleWidth, float scaleHeight)
		{
			SrcWidth = srcWidth;
			SrcHeight = srcHeight;
			DstWidth = dstWidth;
			DstHeight = dstHeight;
			ScaleWidth = scaleWidth;
			ScaleHeight = scaleHeight;
		}

		public override string ToString()
		{
			return $"sw:{SrcWidth},sh:{SrcHeight},dw:{DstWidth},dh:{DstHeight},{ScaleWidth},{ScaleHeight}";
		}

		public static ScaleParam GetScaleParam(Mat src, int dstSize)
		{
			int cols = src.Cols;
			int cols2 = src.Cols;
			int rows = src.Rows;
			int rows2 = src.Rows;
			if (cols2 > rows2)
			{
				float num = (float)dstSize / (float)cols2;
				cols2 = dstSize;
				rows2 = (int)((float)rows2 * num);
			}
			else
			{
				float num = (float)dstSize / (float)rows2;
				rows2 = dstSize;
				cols2 = (int)((float)cols2 * num);
			}
			if (cols2 % 32 != 0)
			{
				cols2 = (cols2 / 32 - 1) * 32;
				cols2 = Math.Max(cols2, 32);
			}
			if (rows2 % 32 != 0)
			{
				rows2 = (rows2 / 32 - 1) * 32;
				rows2 = Math.Max(rows2, 32);
			}
			float scaleWidth = (float)cols2 / (float)cols;
			float scaleHeight = (float)rows2 / (float)rows;
			return new ScaleParam(cols, rows, cols2, rows2, scaleWidth, scaleHeight);
		}
	}
}
