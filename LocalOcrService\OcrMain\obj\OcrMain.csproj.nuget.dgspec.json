{"format": 1, "restore": {"D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\OcrMain.csproj": {}}, "projects": {"D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\OcrMain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\OcrMain.csproj", "projectName": "OcrMain", "projectPath": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\OcrMain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net461"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net461": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net461": {"dependencies": {"Emgu.CV": {"target": "Package", "version": "[4.6.0.5131, )"}, "Microsoft.ML.OnnxRuntime.Managed": {"target": "Package", "version": "[1.13.1, )"}, "UwpDesktop": {"target": "Package", "version": "[10.0.14393.3, )"}, "clipper_library": {"target": "Package", "version": "[6.2.1, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}}