﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)uwpdesktop\10.0.14393.3\build\portable-net45+uap\UwpDesktop.targets" Condition="Exists('$(NuGetPackageRoot)uwpdesktop\10.0.14393.3\build\portable-net45+uap\UwpDesktop.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.ml.onnxruntime.managed\1.13.1\build\netstandard2.0\Microsoft.ML.OnnxRuntime.Managed.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.ml.onnxruntime.managed\1.13.1\build\netstandard2.0\Microsoft.ML.OnnxRuntime.Managed.targets')" />
  </ImportGroup>
</Project>