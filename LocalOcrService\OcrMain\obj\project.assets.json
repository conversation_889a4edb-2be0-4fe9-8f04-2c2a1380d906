{"version": 3, "targets": {".NETFramework,Version=v4.6.1": {"clipper_library/6.2.1": {"type": "package", "compile": {"lib/net40/clipper_library.dll": {}}, "runtime": {"lib/net40/clipper_library.dll": {}}}, "Emgu.CV/4.6.0.5131": {"type": "package", "dependencies": {"System.Drawing.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0"}, "compile": {"lib/netstandard2.0/Emgu.CV.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Emgu.CV.dll": {"related": ".xml"}}}, "Microsoft.ML.OnnxRuntime.Managed/1.13.1": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/netstandard2.0/Microsoft.ML.OnnxRuntime.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Microsoft.ML.OnnxRuntime.dll": {"related": ".pdb"}}, "build": {"build/netstandard2.0/Microsoft.ML.OnnxRuntime.Managed.targets": {}}}, "System.Buffers/4.4.0": {"type": "package", "compile": {"ref/netstandard2.0/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Buffers.dll": {"related": ".xml"}}}, "System.Drawing.Primitives/4.3.0": {"type": "package", "frameworkAssemblies": ["System.Drawing", "mscorlib"], "compile": {"ref/net45/System.Drawing.Primitives.dll": {}}, "runtime": {"lib/net45/System.Drawing.Primitives.dll": {}}}, "System.Memory/4.5.3": {"type": "package", "dependencies": {"System.Buffers": "4.4.0", "System.Numerics.Vectors": "4.4.0", "System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "compile": {"lib/netstandard2.0/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.4.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime/4.3.1": {"type": "package", "frameworkAssemblies": ["System", "System.ComponentModel.Composition", "System.Core"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"type": "package", "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "compile": {"ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtime": {"lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "win"}}}, "UwpDesktop/10.0.14393.3": {"type": "package", "build": {"build/portable-net45+uap/UwpDesktop.targets": {}}}}, ".NETFramework,Version=v4.6.1/win": {"clipper_library/6.2.1": {"type": "package", "compile": {"lib/net40/clipper_library.dll": {}}, "runtime": {"lib/net40/clipper_library.dll": {}}}, "Emgu.CV/4.6.0.5131": {"type": "package", "dependencies": {"System.Drawing.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0"}, "compile": {"lib/netstandard2.0/Emgu.CV.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Emgu.CV.dll": {"related": ".xml"}}}, "Microsoft.ML.OnnxRuntime.Managed/1.13.1": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/netstandard2.0/Microsoft.ML.OnnxRuntime.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Microsoft.ML.OnnxRuntime.dll": {"related": ".pdb"}}, "build": {"build/netstandard2.0/Microsoft.ML.OnnxRuntime.Managed.targets": {}}}, "System.Buffers/4.4.0": {"type": "package", "compile": {"ref/netstandard2.0/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Buffers.dll": {"related": ".xml"}}}, "System.Drawing.Primitives/4.3.0": {"type": "package", "frameworkAssemblies": ["System.Drawing", "mscorlib"], "compile": {"ref/net45/System.Drawing.Primitives.dll": {}}, "runtime": {"lib/net45/System.Drawing.Primitives.dll": {}}}, "System.Memory/4.5.3": {"type": "package", "dependencies": {"System.Buffers": "4.4.0", "System.Numerics.Vectors": "4.4.0", "System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "compile": {"lib/netstandard2.0/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.4.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime/4.3.1": {"type": "package", "frameworkAssemblies": ["System", "System.ComponentModel.Composition", "System.Core"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"type": "package", "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "compile": {"ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtime": {"runtimes/win/lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll": {}}}, "UwpDesktop/10.0.14393.3": {"type": "package", "build": {"build/portable-net45+uap/UwpDesktop.targets": {}}}}, ".NETFramework,Version=v4.6.1/win-arm64": {"clipper_library/6.2.1": {"type": "package", "compile": {"lib/net40/clipper_library.dll": {}}, "runtime": {"lib/net40/clipper_library.dll": {}}}, "Emgu.CV/4.6.0.5131": {"type": "package", "dependencies": {"System.Drawing.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0"}, "compile": {"lib/netstandard2.0/Emgu.CV.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Emgu.CV.dll": {"related": ".xml"}}}, "Microsoft.ML.OnnxRuntime.Managed/1.13.1": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/netstandard2.0/Microsoft.ML.OnnxRuntime.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Microsoft.ML.OnnxRuntime.dll": {"related": ".pdb"}}, "build": {"build/netstandard2.0/Microsoft.ML.OnnxRuntime.Managed.targets": {}}}, "System.Buffers/4.4.0": {"type": "package", "compile": {"ref/netstandard2.0/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Buffers.dll": {"related": ".xml"}}}, "System.Drawing.Primitives/4.3.0": {"type": "package", "frameworkAssemblies": ["System.Drawing", "mscorlib"], "compile": {"ref/net45/System.Drawing.Primitives.dll": {}}, "runtime": {"lib/net45/System.Drawing.Primitives.dll": {}}}, "System.Memory/4.5.3": {"type": "package", "dependencies": {"System.Buffers": "4.4.0", "System.Numerics.Vectors": "4.4.0", "System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "compile": {"lib/netstandard2.0/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.4.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime/4.3.1": {"type": "package", "frameworkAssemblies": ["System", "System.ComponentModel.Composition", "System.Core"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"type": "package", "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "compile": {"ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtime": {"lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll": {}}}, "UwpDesktop/10.0.14393.3": {"type": "package", "build": {"build/portable-net45+uap/UwpDesktop.targets": {}}}}, ".NETFramework,Version=v4.6.1/win-x64": {"clipper_library/6.2.1": {"type": "package", "compile": {"lib/net40/clipper_library.dll": {}}, "runtime": {"lib/net40/clipper_library.dll": {}}}, "Emgu.CV/4.6.0.5131": {"type": "package", "dependencies": {"System.Drawing.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0"}, "compile": {"lib/netstandard2.0/Emgu.CV.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Emgu.CV.dll": {"related": ".xml"}}}, "Microsoft.ML.OnnxRuntime.Managed/1.13.1": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/netstandard2.0/Microsoft.ML.OnnxRuntime.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Microsoft.ML.OnnxRuntime.dll": {"related": ".pdb"}}, "build": {"build/netstandard2.0/Microsoft.ML.OnnxRuntime.Managed.targets": {}}}, "System.Buffers/4.4.0": {"type": "package", "compile": {"ref/netstandard2.0/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Buffers.dll": {"related": ".xml"}}}, "System.Drawing.Primitives/4.3.0": {"type": "package", "frameworkAssemblies": ["System.Drawing", "mscorlib"], "compile": {"ref/net45/System.Drawing.Primitives.dll": {}}, "runtime": {"lib/net45/System.Drawing.Primitives.dll": {}}}, "System.Memory/4.5.3": {"type": "package", "dependencies": {"System.Buffers": "4.4.0", "System.Numerics.Vectors": "4.4.0", "System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "compile": {"lib/netstandard2.0/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.4.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime/4.3.1": {"type": "package", "frameworkAssemblies": ["System", "System.ComponentModel.Composition", "System.Core"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"type": "package", "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "compile": {"ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtime": {"lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll": {}}}, "UwpDesktop/10.0.14393.3": {"type": "package", "build": {"build/portable-net45+uap/UwpDesktop.targets": {}}}}, ".NETFramework,Version=v4.6.1/win-x86": {"clipper_library/6.2.1": {"type": "package", "compile": {"lib/net40/clipper_library.dll": {}}, "runtime": {"lib/net40/clipper_library.dll": {}}}, "Emgu.CV/4.6.0.5131": {"type": "package", "dependencies": {"System.Drawing.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0"}, "compile": {"lib/netstandard2.0/Emgu.CV.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Emgu.CV.dll": {"related": ".xml"}}}, "Microsoft.ML.OnnxRuntime.Managed/1.13.1": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/netstandard2.0/Microsoft.ML.OnnxRuntime.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Microsoft.ML.OnnxRuntime.dll": {"related": ".pdb"}}, "build": {"build/netstandard2.0/Microsoft.ML.OnnxRuntime.Managed.targets": {}}}, "System.Buffers/4.4.0": {"type": "package", "compile": {"ref/netstandard2.0/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Buffers.dll": {"related": ".xml"}}}, "System.Drawing.Primitives/4.3.0": {"type": "package", "frameworkAssemblies": ["System.Drawing", "mscorlib"], "compile": {"ref/net45/System.Drawing.Primitives.dll": {}}, "runtime": {"lib/net45/System.Drawing.Primitives.dll": {}}}, "System.Memory/4.5.3": {"type": "package", "dependencies": {"System.Buffers": "4.4.0", "System.Numerics.Vectors": "4.4.0", "System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "compile": {"lib/netstandard2.0/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.4.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime/4.3.1": {"type": "package", "frameworkAssemblies": ["System", "System.ComponentModel.Composition", "System.Core"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"type": "package", "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "compile": {"ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtime": {"lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll": {}}}, "UwpDesktop/10.0.14393.3": {"type": "package", "build": {"build/portable-net45+uap/UwpDesktop.targets": {}}}}}, "libraries": {"clipper_library/6.2.1": {"sha512": "Jp1fCCMQiLGc62/ugLMFK2SUX2Y23Aihq2R6r0dp3Gq2C/en8VtS4ZSAIuYV+JzPZ+dXEQBNHt6EeJaDioCHMw==", "type": "package", "path": "clipper_library/6.2.1", "files": [".nupkg.metadata", ".signature.p7s", "clipper_library.6.2.1.nupkg.sha512", "clipper_library.nuspec", "lib/net40/clipper_library.dll"]}, "Emgu.CV/4.6.0.5131": {"sha512": "pRtYFTe+IZBoxm79cfkAS2QHnmLiz6upyKJNUnxUyVrOlw+QoLL7lx1siXCruvOVU5wkxCqOCBYDHnDNBSURsQ==", "type": "package", "path": "emgu.cv/4.6.0.5131", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "docs/README.md", "emgu.cv.4.6.0.5131.nupkg.sha512", "emgu.cv.nuspec", "icon.png", "lib/net6.0-ios11.0/Emgu.CV.dll", "lib/net6.0-ios11.0/Emgu.CV.xml", "lib/net6.0-maccatalyst13.2/Emgu.CV.dll", "lib/net6.0-maccatalyst13.2/Emgu.CV.xml", "lib/netstandard2.0/Emgu.CV.dll", "lib/netstandard2.0/Emgu.CV.xml", "lib/xamarinios10/Emgu.CV.dll", "lib/xamarinios10/Emgu.CV.xml"]}, "Microsoft.ML.OnnxRuntime.Managed/1.13.1": {"sha512": "s2u9lkkfxDXwng19NyLL20mdQ56Jzkj68nO1O4y3kwV9adT3Xp+Hsw0jTZ3zjFsksEWrdlnyIJbMz/q3AvrlaA==", "type": "package", "path": "microsoft.ml.onnxruntime.managed/1.13.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "ORT_icon_for_light_bg.png", "Privacy.md", "ThirdPartyNotices.txt", "build/netstandard1.1/Microsoft.ML.OnnxRuntime.Managed.targets", "build/netstandard2.0/Microsoft.ML.OnnxRuntime.Managed.targets", "lib/monoandroid11.0/Microsoft.ML.OnnxRuntime.dll", "lib/monoandroid11.0/Microsoft.ML.OnnxRuntime.pdb", "lib/net5.0/Microsoft.ML.OnnxRuntime.dll", "lib/net5.0/Microsoft.ML.OnnxRuntime.pdb", "lib/net6.0-android31.0/Microsoft.ML.OnnxRuntime.dll", "lib/net6.0-android31.0/Microsoft.ML.OnnxRuntime.pdb", "lib/net6.0-android31.0/Microsoft.ML.OnnxRuntime.xml", "lib/net6.0-ios16.0/Microsoft.ML.OnnxRuntime.dll", "lib/net6.0-ios16.0/Microsoft.ML.OnnxRuntime.pdb", "lib/net6.0-macos12.3/Microsoft.ML.OnnxRuntime.dll", "lib/net6.0-macos12.3/Microsoft.ML.OnnxRuntime.pdb", "lib/net6.0/Microsoft.ML.OnnxRuntime.dll", "lib/net6.0/Microsoft.ML.OnnxRuntime.pdb", "lib/netcoreapp3.1/Microsoft.ML.OnnxRuntime.dll", "lib/netcoreapp3.1/Microsoft.ML.OnnxRuntime.pdb", "lib/netstandard1.1/Microsoft.ML.OnnxRuntime.dll", "lib/netstandard1.1/Microsoft.ML.OnnxRuntime.pdb", "lib/netstandard2.0/Microsoft.ML.OnnxRuntime.dll", "lib/netstandard2.0/Microsoft.ML.OnnxRuntime.pdb", "lib/xamarinios10/Microsoft.ML.OnnxRuntime.dll", "lib/xamarinios10/Microsoft.ML.OnnxRuntime.pdb", "microsoft.ml.onnxruntime.managed.1.13.1.nupkg.sha512", "microsoft.ml.onnxruntime.managed.nuspec"]}, "System.Buffers/4.4.0": {"sha512": "AwarXzzoDwX6BgrhjoJsk6tUezZEozOT5Y9QKF94Gl4JK91I4PIIBkBco9068Y9/Dra8Dkbie99kXB8+1BaYKw==", "type": "package", "path": "system.buffers/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "system.buffers.4.4.0.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Drawing.Primitives/4.3.0": {"sha512": "1QU/c35gwdhvj77fkScXQQbjiVAqIL3fEYn/19NE0CV/ic5TN5PyWAft8HsrbRd4SBLEoErNCkWSzMDc0MmbRw==", "type": "package", "path": "system.drawing.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.Drawing.Primitives.dll", "lib/netstandard1.1/System.Drawing.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Drawing.Primitives.dll", "ref/netstandard1.1/System.Drawing.Primitives.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.drawing.primitives.4.3.0.nupkg.sha512", "system.drawing.primitives.nuspec"]}, "System.Memory/4.5.3": {"sha512": "3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "type": "package", "path": "system.memory/4.5.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.3.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Numerics.Vectors/4.4.0": {"sha512": "UiLzLW+Lw6HLed1Hcg+8jSRttrbuXv7DANVj0DkL9g6EnnzbL75EB7EWsw5uRbhxd/4YdG8li5XizGWepmG3PQ==", "type": "package", "path": "system.numerics.vectors/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.4.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime/4.3.1": {"sha512": "abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "type": "package", "path": "system.runtime/4.3.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.1.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"sha512": "wprSFgext8cwqymChhrBLu62LMg/1u92bU+VOwyfBimSPVFXtsNqEWC92Pf9ofzJFlk4IHmJA75EDJn1b2goAQ==", "type": "package", "path": "system.runtime.compilerservices.unsafe/4.5.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"sha512": "cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "type": "package", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/win8/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/wpa81/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "system.runtime.interopservices.runtimeinformation.nuspec"]}, "UwpDesktop/10.0.14393.3": {"sha512": "INgnDsZ7qoS08iBtt+C2qpBb5YQc0AINe1gcXFw0RwEisC9jqBapwwu5oMVR5MIgt+MdoQfKbq8YwOqNsb2eOA==", "type": "package", "path": "uwpdesktop/10.0.14393.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/UwpDesktopAnalyzer.dll", "build/portable-net45+uap/UwpDesktop.targets", "tools/install.ps1", "tools/uninstall.ps1", "uwpdesktop.10.0.14393.3.nupkg.sha512", "uwpdesktop.nuspec"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.6.1": ["Emgu.CV >= 4.6.0.5131", "Microsoft.ML.OnnxRuntime.Managed >= 1.13.1", "UwpDesktop >= 10.0.14393.3", "clipper_library >= 6.2.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\OcrMain.csproj", "projectName": "OcrMain", "projectPath": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\OcrMain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net461"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net461": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net461": {"dependencies": {"Emgu.CV": {"target": "Package", "version": "[4.6.0.5131, )"}, "Microsoft.ML.OnnxRuntime.Managed": {"target": "Package", "version": "[1.13.1, )"}, "UwpDesktop": {"target": "Package", "version": "[10.0.14393.3, )"}, "clipper_library": {"target": "Package", "version": "[6.2.1, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}