{"version": 2, "dgSpecHash": "6hsHbe7w+tc=", "success": true, "projectFilePath": "D:\\Code\\CatchTools\\LocalOcrService\\OcrMain\\OcrMain.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\clipper_library\\6.2.1\\clipper_library.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\emgu.cv\\4.6.0.5131\\emgu.cv.4.6.0.5131.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.ml.onnxruntime.managed\\1.13.1\\microsoft.ml.onnxruntime.managed.1.13.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.4.0\\system.buffers.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.primitives\\4.3.0\\system.drawing.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.3\\system.memory.4.5.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.4.0\\system.numerics.vectors.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.1\\system.runtime.4.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\4.5.2\\system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices.runtimeinformation\\4.3.0\\system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\uwpdesktop\\10.0.14393.3\\uwpdesktop.10.0.14393.3.nupkg.sha512"], "logs": []}