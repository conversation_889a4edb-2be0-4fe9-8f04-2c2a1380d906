<?xml version="1.0"?>
<doc>
    <assembly>
        <name>StackExchange.Redis</name>
    </assembly>
    <members>
        <member name="T:StackExchange.Redis.Aggregate">
            <summary>
            Specifies how elements should be aggregated when combining sorted sets
            </summary>
        </member>
        <member name="F:StackExchange.Redis.Aggregate.Sum">
            <summary>
            The values of the combined elements are added
            </summary>
        </member>
        <member name="F:StackExchange.Redis.Aggregate.Min">
            <summary>
            The least value of the combined elements is used
            </summary>
        </member>
        <member name="F:StackExchange.Redis.Aggregate.Max">
            <summary>
            The greatest value of the combined elements is used
            </summary>
        </member>
        <member name="T:StackExchange.Redis.Bitwise">
            <summary>
            <a href="http://en.wikipedia.org/wiki/Bitwise_operation">Bitwise operators</a>
            </summary>
        </member>
        <member name="F:StackExchange.Redis.Bitwise.And">
            <summary>
            <a href="http://en.wikipedia.org/wiki/Bitwise_operation#AND">And</a>
            </summary>
        </member>
        <member name="F:StackExchange.Redis.Bitwise.Or">
            <summary>
            <a href="http://en.wikipedia.org/wiki/Bitwise_operation#OR">Or</a>
            </summary>
        </member>
        <member name="F:StackExchange.Redis.Bitwise.Xor">
            <summary>
            <a href="http://en.wikipedia.org/wiki/Bitwise_operation#XOR">Xor</a>
            </summary>
        </member>
        <member name="F:StackExchange.Redis.Bitwise.Not">
            <summary>
            <a href="http://en.wikipedia.org/wiki/Bitwise_operation#NOT">Not</a>
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ClientFlags">
            <summary>
            The client flags can be a combination of:
            O: the client is a slave in MONITOR mode
            S: the client is a normal slave server
            M: the client is a master
            x: the client is in a MULTI/EXEC context
            b: the client is waiting in a blocking operation
            i: the client is waiting for a VM I/O (deprecated)
            d: a watched keys has been modified - EXEC will fail
            c: connection to be closed after writing entire reply
            u: the client is unblocked
            A: connection to be closed ASAP
            N: no specific flag set
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ClientFlags.None">
            <summary>
            no specific flag set
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ClientFlags.SlaveMonitor">
            <summary>
            the client is a slave in MONITOR mode
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ClientFlags.Slave">
            <summary>
            the client is a normal slave server
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ClientFlags.Master">
            <summary>
            the client is a master
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ClientFlags.Transaction">
            <summary>
            the client is in a MULTI/EXEC context
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ClientFlags.Blocked">
            <summary>
            the client is waiting in a blocking operation
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ClientFlags.TransactionDoomed">
            <summary>
            a watched keys has been modified - EXEC will fail
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ClientFlags.Closing">
            <summary>
            connection to be closed after writing entire reply
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ClientFlags.Unblocked">
            <summary>
            the client is unblocked
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ClientFlags.CloseASAP">
            <summary>
            connection to be closed ASAP
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ClientInfo">
            <summary>
            Represents the state of an individual client connection to redis
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClientInfo.Address">
            <summary>
            Address (host and port) of the client
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClientInfo.AgeSeconds">
            <summary>
            total duration of the connection in seconds
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClientInfo.Database">
            <summary>
            current database ID
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClientInfo.Flags">
            <summary>
            The flags associated with this connection
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClientInfo.FlagsRaw">
            <summary>
            The client flags can be a combination of:
            O: the client is a slave in MONITOR mode
            S: the client is a normal slave server
            M: the client is a master
            x: the client is in a MULTI/EXEC context
            b: the client is waiting in a blocking operation
            i: the client is waiting for a VM I/O (deprecated)
            d: a watched keys has been modified - EXEC will fail
            c: connection to be closed after writing entire reply
            u: the client is unblocked
            A: connection to be closed ASAP
            N: no specific flag set
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClientInfo.Host">
            <summary>
            The host of the client (typically an IP address)
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClientInfo.IdleSeconds">
            <summary>
            idle time of the connection in seconds
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClientInfo.LastCommand">
            <summary>
             last command played
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClientInfo.Name">
            <summary>
            The name allocated to this connection, if any
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClientInfo.PatternSubscriptionCount">
            <summary>
            number of pattern matching subscriptions
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClientInfo.Port">
            <summary>
            The port of the client
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClientInfo.Raw">
            <summary>
            The raw content from redis
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClientInfo.SubscriptionCount">
            <summary>
            number of channel subscriptions
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClientInfo.TransactionCommandLength">
            <summary>
            number of commands in a MULTI/EXEC context
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClientInfo.Id">
            <summary>
            an unique 64-bit client ID (introduced in Redis 2.8.12).
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ClientInfo.ToString">
            <summary>
            Format the object as a string
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClientInfo.ClientType">
            <summary>
            The class of the connection
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ClientType">
            <summary>
            The class of the connection
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ClientType.Normal">
            <summary>
            Regular connections, including MONITOR connections
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ClientType.Slave">
            <summary>
            Replication connections
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ClientType.PubSub">
            <summary>
            Subscription connections
            </summary>
        </member>
        <member name="T:StackExchange.Redis.SlotRange">
            <summary>
            Indicates a range of slots served by a cluster node
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SlotRange.#ctor(System.Int32,System.Int32)">
            <summary>
            Create a new SlotRange value
            </summary>
        </member>
        <member name="P:StackExchange.Redis.SlotRange.From">
            <summary>
            The start of the range (inclusive)
            </summary>
        </member>
        <member name="P:StackExchange.Redis.SlotRange.To">
            <summary>
            The end of the range (inclusive)
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SlotRange.op_Inequality(StackExchange.Redis.SlotRange,StackExchange.Redis.SlotRange)">
            <summary>
            Indicates whether two ranges are not equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SlotRange.op_Equality(StackExchange.Redis.SlotRange,StackExchange.Redis.SlotRange)">
            <summary>
            Indicates whether two ranges are equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SlotRange.TryParse(System.String,StackExchange.Redis.SlotRange@)">
            <summary>
            Try to parse a string as a range
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SlotRange.CompareTo(StackExchange.Redis.SlotRange)">
            <summary>
            Compares the current instance with another object of the same type and returns an integer that indicates whether the current instance precedes, follows, or occurs in the same position in the sort order as the other object.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SlotRange.Equals(System.Object)">
            <summary>
            See Object.Equals
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SlotRange.Equals(StackExchange.Redis.SlotRange)">
            <summary>
            Indicates whether two ranges are equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SlotRange.GetHashCode">
            <summary>
            See Object.GetHashCode()
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SlotRange.ToString">
            <summary>
            See Object.ToString()
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ClusterConfiguration">
            <summary>
            Describes the state of the cluster as reported by a single node
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClusterConfiguration.Nodes">
            <summary>
            Gets all nodes contained in the configuration
            </summary>
            <returns></returns>
        </member>
        <member name="P:StackExchange.Redis.ClusterConfiguration.Origin">
            <summary>
            The node that was asked for the configuration
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClusterConfiguration.Item(System.Net.EndPoint)">
            <summary>
            Obtain the node relating to a specified endpoint
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ClusterConfiguration.GetBySlot(System.Int32)">
            <summary>
            Gets the node that serves the specified slot
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ClusterConfiguration.GetBySlot(StackExchange.Redis.RedisKey)">
            <summary>
            Gets the node that serves the specified slot
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ClusterNode">
            <summary>
            Represents the configuration of a single node in a cluster configuration
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClusterNode.Children">
            <summary>
            Gets all child nodes of the current node
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClusterNode.EndPoint">
            <summary>
            Gets the endpoint of the current node
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClusterNode.IsMyself">
            <summary>
            Gets whether this is the node which responded to the CLUSTER NODES request
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClusterNode.IsSlave">
            <summary>
            Gets whether this node is a slave
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClusterNode.IsNoAddr">
            <summary>
            Gets whether this node is flagged as noaddr
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClusterNode.IsConnected">
            <summary>
            Gets the node's connection status
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClusterNode.NodeId">
            <summary>
            Gets the unique node-id of the current node
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClusterNode.Parent">
            <summary>
            Gets the parent node of the current node
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClusterNode.ParentNodeId">
            <summary>
            Gets the unique node-id of the parent of the current node
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClusterNode.Raw">
            <summary>
            The configuration as reported by the server
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ClusterNode.Slots">
            <summary>
            The slots owned by this server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ClusterNode.CompareTo(StackExchange.Redis.ClusterNode)">
            <summary>
            Compares the current instance with another object of the same type and returns an integer that indicates whether the current instance precedes, follows, or occurs in the same position in the sort order as the other object.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ClusterNode.Equals(System.Object)">
            <summary>
            See Object.Equals
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ClusterNode.Equals(StackExchange.Redis.ClusterNode)">
            <summary>
            Indicates whether two ClusterNode instances are equivalent
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ClusterNode.GetHashCode">
            <summary>
            See object.GetHashCode()
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ClusterNode.ToString">
            <summary>
            See Object.ToString()
            </summary>
        </member>
        <member name="T:StackExchange.Redis.CommandFlags">
            <summary>
            Behaviour markers associated with a given command
            </summary>
        </member>
        <member name="F:StackExchange.Redis.CommandFlags.None">
            <summary>
            Default behaviour.
            </summary>
        </member>
        <member name="F:StackExchange.Redis.CommandFlags.HighPriority">
            <summary>
            This command may jump regular-priority commands that have not yet been written to the redis stream.
            </summary>
        </member>
        <member name="F:StackExchange.Redis.CommandFlags.FireAndForget">
            <summary>
            The caller is not interested in the result; the caller will immediately receive a default-value
            of the expected return type (this value is not indicative of anything at the server).
            </summary>
        </member>
        <member name="F:StackExchange.Redis.CommandFlags.PreferMaster">
            <summary>
            This operation should be performed on the master if it is available, but read operations may
            be performed on a slave if no master is available. This is the default option.
            </summary>
        </member>
        <member name="F:StackExchange.Redis.CommandFlags.DemandMaster">
            <summary>
            This operation should only be performed on the master.
            </summary>
        </member>
        <member name="F:StackExchange.Redis.CommandFlags.PreferSlave">
            <summary>
            This operation should be performed on the slave if it is available, but will be performed on
            a master if no slaves are available. Suitable for read operations only.
            </summary>
        </member>
        <member name="F:StackExchange.Redis.CommandFlags.DemandSlave">
            <summary>
            This operation should only be performed on a slave. Suitable for read operations only.
            </summary>
        </member>
        <member name="F:StackExchange.Redis.CommandFlags.NoRedirect">
            <summary>
            Indicates that this operation should not be forwarded to other servers as a result of an ASK or MOVED response
            </summary>
        </member>
        <member name="T:StackExchange.Redis.CommandMap">
            <summary>
            Represents the commands mapped on a particular configuration
            </summary>
        </member>
        <member name="P:StackExchange.Redis.CommandMap.Default">
            <summary>
            The default commands specified by redis
            </summary>
        </member>
        <member name="P:StackExchange.Redis.CommandMap.Twemproxy">
            <summary>
            The commands available to <a href="twemproxy">https://github.com/twitter/twemproxy</a>
            </summary>
            <remarks>https://github.com/twitter/twemproxy/blob/master/notes/redis.md</remarks>
        </member>
        <member name="P:StackExchange.Redis.CommandMap.SSDB">
            <summary>
            The commands available to <a href="ssdb">http://www.ideawu.com/ssdb/</a>
            </summary>
            <remarks>http://www.ideawu.com/ssdb/docs/redis-to-ssdb.html</remarks>
        </member>
        <member name="P:StackExchange.Redis.CommandMap.Sentinel">
            <summary>
            The commands available to <a href="Sentinel">http://redis.io/topics/sentinel</a>
            </summary>
            <remarks>http://redis.io/topics/sentinel</remarks>
        </member>
        <member name="M:StackExchange.Redis.CommandMap.Create(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Create a new CommandMap, customizing some commands
            </summary>
        </member>
        <member name="M:StackExchange.Redis.CommandMap.Create(System.Collections.Generic.HashSet{System.String},System.Boolean)">
            <summary>
            Creates a CommandMap by specifying which commands are available or unavailable
            </summary>
        </member>
        <member name="M:StackExchange.Redis.CommandMap.ToString">
            <summary>
            See Object.ToString()
            </summary>
        </member>
        <member name="T:StackExchange.Redis.CommandTrace">
            <summary>
            Represents the information known about long-running commands
            </summary>    
        </member>
        <member name="P:StackExchange.Redis.CommandTrace.Arguments">
            <summary>
            The array composing the arguments of the command.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.CommandTrace.Duration">
            <summary>
            The amount of time needed for its execution
            </summary>
        </member>
        <member name="P:StackExchange.Redis.CommandTrace.Time">
            <summary>
            The time at which the logged command was processed.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.CommandTrace.UniqueId">
            <summary>
            A unique progressive identifier for every slow log entry.
            </summary>
            <remarks>The entry's unique ID can be used in order to avoid processing slow log entries multiple times (for instance you may have a script sending you an email alert for every new slow log entry). The ID is never reset in the course of the Redis server execution, only a server restart will reset it.</remarks>
        </member>
        <member name="M:StackExchange.Redis.CommandTrace.GetHelpUrl">
            <summary>
            Deduces a link to the redis documentation about the specified command
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ConvertHelper">
            <summary>
            Helper for Array.ConvertAll() as it's missing on .Net Core.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConvertHelper.ConvertAll``2(``0[],System.Func{``0,``1})">
            <summary>
            Converts array of one type to an array of another type.
            </summary>
            <typeparam name="TInput">Input type</typeparam>
            <typeparam name="TOutput">Output type</typeparam>
            <param name="source">source</param>
            <param name="selector">selector</param>
            <returns></returns>
        </member>
        <member name="T:StackExchange.Redis.ProfiledCommandEnumerable">
            <summary>
            A collection of IProfiledCommands.
            
            This is a very light weight data structure, only supporting enumeration.
            
            While it implements IEnumerable, it there are fewer allocations if one uses
            it's explicit GetEnumerator() method.  Using `foreach` does this automatically.
            
            This type is not threadsafe.
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ProfiledCommandEnumerable.Enumerator">
            <summary>
            Implements IEnumerator for ProfiledCommandEnumerable.
            This implementation is comparable to List.Enumerator and Dictionary.Enumerator,
            and is provided to reduce allocations in the common (ie. foreach) case.
            
            This type is not threadsafe.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ProfiledCommandEnumerable.Enumerator.Current">
            <summary>
            The current element.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ProfiledCommandEnumerable.Enumerator.MoveNext">
            <summary>
            Advances the enumeration, returning true if there is a new element to consume and false
            if enumeration is complete.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ProfiledCommandEnumerable.Enumerator.Reset">
            <summary>
            Resets the enumeration.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ProfiledCommandEnumerable.Enumerator.Dispose">
            <summary>
            Disposes the enumeration.
            subsequent attempts to enumerate results in undefined behavior.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ProfiledCommandEnumerable.GetEnumerator">
            <summary>
            Returns an implementor of IEnumerator that, provided it isn't accessed
            though an interface, avoids allocations.
            
            `foreach` will automatically use this method.
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ConcurrentProfileStorageCollection">
            <summary>
            A thread-safe collection tailored to the "always append, with high contention, then enumerate once with no contention"
            behavior of our profiling.
            
            Performs better than ConcurrentBag, which is important since profiling code shouldn't impact timings.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConcurrentProfileStorageCollection.Add(StackExchange.Redis.ProfileStorage)">
            <summary>
            This method is thread-safe.
            
            Adds an element to the bag.
            
            Order is not preserved.
            
            The element can only be a member of *one* bag.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConcurrentProfileStorageCollection.EnumerateAndReturnForReuse">
            <summary>
            This method returns an enumerable view of the bag, and returns it to 
            an internal pool for reuse by GetOrCreate().
            
            It is not thread safe.
            
            It should only be called once the bag is finished being mutated.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConcurrentProfileStorageCollection.ReturnForReuse">
            <summary>
            This returns the ConcurrentProfileStorageCollection to an internal pool for reuse by GetOrCreate().
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConcurrentProfileStorageCollection.GetOrCreate">
            <summary>
            Returns a ConcurrentProfileStorageCollection to use.
            
            It *may* have allocated a new one, or it may return one that has previously been released.
            To return the collection, call EnumerateAndReturnForReuse()
            </summary>
        </member>
        <member name="T:StackExchange.Redis.Condition">
            <summary>
            Describes a pre-condition used in a redis transaction
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.HashEqual(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue)">
            <summary>
            Enforces that the given hash-field must have the specified value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.HashExists(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue)">
            <summary>
            Enforces that the given hash-field must exist
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.HashNotEqual(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue)">
            <summary>
            Enforces that the given hash-field must not have the specified value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.HashNotExists(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue)">
            <summary>
            Enforces that the given hash-field must not exist
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.KeyExists(StackExchange.Redis.RedisKey)">
            <summary>
            Enforces that the given key must exist
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.KeyNotExists(StackExchange.Redis.RedisKey)">
            <summary>
            Enforces that the given key must not exist
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.ListIndexEqual(StackExchange.Redis.RedisKey,System.Int64,StackExchange.Redis.RedisValue)">
            <summary>
            Enforces that the given list index must have the specified value 
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.ListIndexExists(StackExchange.Redis.RedisKey,System.Int64)">
            <summary>
            Enforces that the given list index must exist
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.ListIndexNotEqual(StackExchange.Redis.RedisKey,System.Int64,StackExchange.Redis.RedisValue)">
            <summary>
            Enforces that the given list index must not have the specified value 
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.ListIndexNotExists(StackExchange.Redis.RedisKey,System.Int64)">
            <summary>
            Enforces that the given list index must not exist
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.StringEqual(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue)">
            <summary>
            Enforces that the given key must have the specified value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.StringNotEqual(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue)">
            <summary>
            Enforces that the given key must not have the specified value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.HashLengthEqual(StackExchange.Redis.RedisKey,System.Int64)">
            <summary>
            Enforces that the given hash length is a certain value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.HashLengthLessThan(StackExchange.Redis.RedisKey,System.Int64)">
            <summary>
            Enforces that the given hash length is less than a certain value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.HashLengthGreaterThan(StackExchange.Redis.RedisKey,System.Int64)">
            <summary>
            Enforces that the given hash length is greater than a certain value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.StringLengthEqual(StackExchange.Redis.RedisKey,System.Int64)">
            <summary>
            Enforces that the given string length is a certain value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.StringLengthLessThan(StackExchange.Redis.RedisKey,System.Int64)">
            <summary>
            Enforces that the given string length is less than a certain value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.StringLengthGreaterThan(StackExchange.Redis.RedisKey,System.Int64)">
            <summary>
            Enforces that the given string length is greater than a certain value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.ListLengthEqual(StackExchange.Redis.RedisKey,System.Int64)">
            <summary>
            Enforces that the given list length is a certain value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.ListLengthLessThan(StackExchange.Redis.RedisKey,System.Int64)">
            <summary>
            Enforces that the given list length is less than a certain value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.ListLengthGreaterThan(StackExchange.Redis.RedisKey,System.Int64)">
            <summary>
            Enforces that the given list length is greater than a certain value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.SetLengthEqual(StackExchange.Redis.RedisKey,System.Int64)">
            <summary>
            Enforces that the given set cardinality is a certain value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.SetLengthLessThan(StackExchange.Redis.RedisKey,System.Int64)">
            <summary>
            Enforces that the given set cardinality is less than a certain value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.SetLengthGreaterThan(StackExchange.Redis.RedisKey,System.Int64)">
            <summary>
            Enforces that the given set cardinality is greater than a certain value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.SortedSetLengthEqual(StackExchange.Redis.RedisKey,System.Int64)">
            <summary>
            Enforces that the given sorted set cardinality is a certain value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.SortedSetLengthLessThan(StackExchange.Redis.RedisKey,System.Int64)">
            <summary>
            Enforces that the given sorted set cardinality is less than a certain value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Condition.SortedSetLengthGreaterThan(StackExchange.Redis.RedisKey,System.Int64)">
            <summary>
            Enforces that the given sorted set cardinality is greater than a certain value
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ConditionResult">
            <summary>
            Indicates the status of a condition as part of a transaction
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConditionResult.WasSatisfied">
            <summary>
            Indicates whether the condition was satisfied
            </summary>
        </member>
        <member name="T:StackExchange.Redis.Proxy">
            <summary>
            Specifies the proxy that is being used to communicate to redis
            </summary>
        </member>
        <member name="F:StackExchange.Redis.Proxy.None">
            <summary>
            Direct communication to the redis server(s)
            </summary>
        </member>
        <member name="F:StackExchange.Redis.Proxy.Twemproxy">
            <summary>
            Communication via <a href="https://github.com/twitter/twemproxy">twemproxy</a>
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ConfigurationOptions">
            <summary>
            The options relevant to a set of redis connections
            </summary>
        </member>
        <member name="E:StackExchange.Redis.ConfigurationOptions.CertificateSelection">
            <summary>
            A LocalCertificateSelectionCallback delegate responsible for selecting the certificate used for authentication; note
            that this cannot be specified in the configuration-string.
            </summary>
        </member>
        <member name="E:StackExchange.Redis.ConfigurationOptions.CertificateValidation">
            <summary>
            A RemoteCertificateValidationCallback delegate responsible for validating the certificate supplied by the remote party; note
            that this cannot be specified in the configuration-string.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.AbortOnConnectFail">
            <summary>
            Gets or sets whether connect/configuration timeouts should be explicitly notified via a TimeoutException
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.AllowAdmin">
            <summary>
            Indicates whether admin operations should be allowed
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.UseSsl">
            <summary>
            Indicates whether the connection should be encrypted
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.Ssl">
            <summary>
            Indicates whether the connection should be encrypted
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.ChannelPrefix">
            <summary>
            Automatically encodes and decodes channels
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.ClientName">
            <summary>
            The client name to use for all connections
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.ConnectRetry">
            <summary>
            The number of times to repeat the initial connect cycle if no servers respond promptly
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.CommandMap">
            <summary>
            The command-map associated with this configuration
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.ConfigurationChannel">
            <summary>
            Channel to use for broadcasting and listening for configuration change notification
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.ConnectTimeout">
            <summary>
            Specifies the time in milliseconds that should be allowed for connection (defaults to 5 seconds unless SyncTimeout is higher)
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.DefaultVersion">
            <summary>
            The server version to assume
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.EndPoints">
            <summary>
            The endpoints defined for this configuration
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.HighPrioritySocketThreads">
            <summary>
            Use ThreadPriority.AboveNormal for SocketManager reader and writer threads (true by default). If false, ThreadPriority.Normal will be used.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.KeepAlive">
            <summary>
            Specifies the time in seconds at which connections should be pinged to ensure validity
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.Password">
            <summary>
            The password to use to authenticate with the server
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.Proxy">
            <summary>
            Indicates whether admin operations should be allowed
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.ResolveDns">
            <summary>
            Indicates whether endpoints should be resolved via DNS before connecting.
            If enabled the ConnectionMultiplexer will not re-resolve DNS
            when attempting to re-connect after a connection failure.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.ServiceName">
            <summary>
            The service name used to resolve a service via sentinel
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.SocketManager">
            <summary>
            Gets or sets the SocketManager instance to be used with these options; if this is null a per-multiplexer
            SocketManager is created automatically.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.SslHost">
            <summary>
            The target-host to use when validating SSL certificate; setting a value here enables SSL mode
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.SyncTimeout">
            <summary>
            Specifies the time in milliseconds that the system should allow for synchronous operations (defaults to 1 second)
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.ResponseTimeout">
            <summary>
            Specifies the time in milliseconds that the system should allow for responses before concluding that the socket is unhealthy
            (defaults to SyncTimeout)
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.TieBreaker">
            <summary>
            Tie-breaker used to choose between masters (must match the endpoint exactly)
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.WriteBuffer">
            <summary>
            The size of the output buffer to use
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.DefaultDatabase">
            <summary>
            Specifies the default database to be used when calling ConnectionMultiplexer.GetDatabase() without any parameters
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConfigurationOptions.ConfigCheckSeconds">
            <summary>
            Check configuration every n seconds (every minute by default)
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConfigurationOptions.Parse(System.String)">
            <summary>
            Parse the configuration from a comma-delimited configuration string
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="configuration"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="configuration"/> is empty.</exception>
        </member>
        <member name="M:StackExchange.Redis.ConfigurationOptions.Parse(System.String,System.Boolean)">
            <summary>
            Parse the configuration from a comma-delimited configuration string
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="configuration"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="configuration"/> is empty.</exception>
        </member>
        <member name="M:StackExchange.Redis.ConfigurationOptions.Clone">
            <summary>
            Create a copy of the configuration
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConfigurationOptions.SetDefaultPorts">
            <summary>
            Resolve the default port for any endpoints that did not have a port explicitly specified
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConfigurationOptions.ToString">
            <summary>
            Returns the effective configuration string for this configuration
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ConnectionCounters">
            <summary>
            Illustrates the counters associated with an individual connection
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionCounters.CompletedAsynchronously">
            <summary>
            The number of operations that have been completed asynchronously
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionCounters.CompletedSynchronously">
            <summary>
            The number of operations that have been completed synchronously
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionCounters.ConnectionType">
            <summary>
            The type of this connection
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionCounters.FailedAsynchronously">
            <summary>
            The number of operations that failed to complete asynchronously
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionCounters.IsEmpty">
            <summary>
            Indicates if there are any pending items or failures on this connection
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionCounters.NonPreferredEndpointCount">
            <summary>
            Indicates the total number of messages despatched to a non-preferred endpoint, for example sent to a master
            when the caller stated a preference of slave
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionCounters.OperationCount">
            <summary>
            The number of operations performed on this connection
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionCounters.PendingUnsentItems">
            <summary>
            Operations that have been requested, but which have not yet been sent to the server
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionCounters.ResponsesAwaitingAsyncCompletion">
            <summary>
            Operations for which the response has been processed, but which are awaiting asynchronous completion
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionCounters.SentItemsAwaitingResponse">
            <summary>
            Operations that have been sent to the server, but which are awaiting a response
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionCounters.SocketCount">
            <summary>
            The number of sockets used by this logical connection (total, including reconnects)
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionCounters.Subscriptions">
            <summary>
            The number of subscriptions (with and without patterns) currently held against this connection
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionCounters.TotalOutstanding">
            <summary>
            Indicates the total number of outstanding items against this connection
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionCounters.WriterCount">
            <summary>
            Indicates the total number of writers items against this connection
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionCounters.ToString">
            <summary>
            See Object.ToString()
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ConnectionFailedEventArgs">
            <summary>
            Contains information about a server connection failure
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionFailedEventArgs.ConnectionType">
            <summary>
            Gets the connection-type of the failing connection
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionFailedEventArgs.EndPoint">
            <summary>
            Gets the failing server-endpoint
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionFailedEventArgs.Exception">
            <summary>
            Gets the exception if available (this can be null)
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionFailedEventArgs.FailureType">
            <summary>
            The type of failure
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ConnectionFailureType">
            <summary>
            The known types of connection failure
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ConnectionFailureType.None">
            <summary>
            This event is not a failure
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ConnectionFailureType.UnableToResolvePhysicalConnection">
            <summary>
            No viable connections were available for this operation
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ConnectionFailureType.SocketFailure">
            <summary>
            The socket for this connection failed
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ConnectionFailureType.AuthenticationFailure">
            <summary>
            Either SSL Stream or Redis authentication failed
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ConnectionFailureType.ProtocolFailure">
            <summary>
            An unexpected response was received from the server
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ConnectionFailureType.InternalFailure">
            <summary>
            An unknown internal error occurred
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ConnectionFailureType.SocketClosed">
            <summary>
            The socket was closed
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ConnectionFailureType.ConnectionDisposed">
            <summary>
            The socket was closed
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ConnectionFailureType.Loading">
            <summary>
            The database is loading and is not available for use
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ConnectionFailureType.UnableToConnect">
            <summary>
            It has not been possible to create an intial connection to the redis server(s)
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ConnectionMultiplexer">
            <summary>
            Represents an inter-related group of connections to redis servers
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionMultiplexer.Factory">
            <summary>
            Provides a way of overriding the default Task Factory. If not set, it will use the default Task.Factory.
            Useful when top level code sets it's own factory which may interfere with Redis queries.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.GetCounters">
            <summary>
            Get summary statistics associates with this server
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionMultiplexer.ClientName">
            <summary>
            Gets the client-name that will be used on all new connections
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.TryGetAzureRoleInstanceIdNoThrow">
            Tries to get the Roleinstance Id if Microsoft.WindowsAzure.ServiceRuntime is loaded.
            In case of any failure, swallows the exception and returns null
        </member>
        <member name="P:StackExchange.Redis.ConnectionMultiplexer.Configuration">
            <summary>
            Gets the configuration of the connection
            </summary>
        </member>
        <member name="E:StackExchange.Redis.ConnectionMultiplexer.ErrorMessage">
            <summary>
            A server replied with an error message;
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.ExportConfiguration(System.IO.Stream,StackExchange.Redis.ExportOptions)">
            <summary>
            Write the configuration of all servers to an output stream
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionMultiplexer.LogSyncLock">
            <summary>
            Used internally to synchronize loggine without depending on locking the log instance
            </summary>
        </member>
        <member name="E:StackExchange.Redis.ConnectionMultiplexer.ConnectionFailed">
            <summary>
            Raised whenever a physical connection fails
            </summary>
        </member>
        <member name="E:StackExchange.Redis.ConnectionMultiplexer.InternalError">
            <summary>
            Raised whenever an internal error occurs (this is primarily for debugging)
            </summary>
        </member>
        <member name="E:StackExchange.Redis.ConnectionMultiplexer.ConnectionRestored">
            <summary>
            Raised whenever a physical connection is established
            </summary>
        </member>
        <member name="E:StackExchange.Redis.ConnectionMultiplexer.ConfigurationChanged">
            <summary>
            Raised when configuration changes are detected
            </summary>
        </member>
        <member name="E:StackExchange.Redis.ConnectionMultiplexer.ConfigurationChangedBroadcast">
            <summary>
            Raised when nodes are explicitly requested to reconfigure via broadcast;
            this usually means master/slave changes
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionMultiplexer.TimeoutMilliseconds">
            <summary>
            Gets the timeout associated with the connections
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.GetEndPoints(System.Boolean)">
            <summary>
            Gets all endpoints defined on the server
            </summary>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.Wait(System.Threading.Tasks.Task)">
            <summary>
            Wait for a given asynchronous operation to complete (or timeout)
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.Wait``1(System.Threading.Tasks.Task{``0})">
            <summary>
            Wait for a given asynchronous operation to complete (or timeout)
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.WaitAll(System.Threading.Tasks.Task[])">
            <summary>
            Wait for the given asynchronous operations to complete (or timeout)
            </summary>
        </member>
        <member name="E:StackExchange.Redis.ConnectionMultiplexer.HashSlotMoved">
            <summary>
            Raised when a hash-slot has been relocated
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.HashSlot(StackExchange.Redis.RedisKey)">
            <summary>
            Compute the hash-slot of a specified key
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.ConnectAsync(System.String,System.IO.TextWriter)">
            <summary>
            Create a new ConnectionMultiplexer instance
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.ConnectAsync(StackExchange.Redis.ConfigurationOptions,System.IO.TextWriter)">
            <summary>
            Create a new ConnectionMultiplexer instance
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.Connect(System.String,System.IO.TextWriter)">
            <summary>
            Create a new ConnectionMultiplexer instance
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.Connect(StackExchange.Redis.ConfigurationOptions,System.IO.TextWriter)">
            <summary>
            Create a new ConnectionMultiplexer instance
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.GetSubscriber(System.Object)">
            <summary>
            Obtain a pub/sub subscriber connection to the specified server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.GetDatabase(System.Int32,System.Object)">
            <summary>
            Obtain an interactive connection to a database inside redis
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.GetServer(System.String,System.Int32,System.Object)">
            <summary>
            Obtain a configuration API for an individual server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.GetServer(System.String,System.Object)">
            <summary>
            Obtain a configuration API for an individual server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.GetServer(System.Net.IPAddress,System.Int32)">
            <summary>
            Obtain a configuration API for an individual server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.GetServer(System.Net.EndPoint,System.Object)">
            <summary>
            Obtain a configuration API for an individual server
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionMultiplexer.OperationCount">
            <summary>
            The number of operations that have been performed on all connections
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.ConfigureAsync(System.IO.TextWriter)">
            <summary>
            Reconfigure the current connections based on the existing configuration
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.Configure(System.IO.TextWriter)">
            <summary>
            Reconfigure the current connections based on the existing configuration
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.GetStatus">
            <summary>
            Provides a text overview of the status of all connections
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.GetStatus(System.IO.TextWriter)">
            <summary>
            Provides a text overview of the status of all connections
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.ToString">
            <summary>
            See Object.ToString()
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionMultiplexer.PreserveAsyncOrder">
            <summary>
            Gets or sets whether asynchronous operations should be invoked in a way that guarantees their original delivery order
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionMultiplexer.IsConnected">
            <summary>
            Indicates whether any servers are connected
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.Close(System.Boolean)">
            <summary>
            Close all connections and release all resources associated with this object
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.CloseAsync(System.Boolean)">
            <summary>
            Close all connections and release all resources associated with this object
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.Dispose">
            <summary>
            Release all resources associated with this object
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionMultiplexer.IncludeDetailInExceptions">
            <summary>
            Should exceptions include identifiable details? (key names, additional .Data annotations)
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ConnectionMultiplexer.StormLogThreshold">
            <summary>
            Limit at which to start recording unusual busy patterns (only one log will be retained at a time;
            set to a negative value to disable this feature)
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.GetStormLog">
            <summary>
            Obtains the log of unusual busy patterns
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.ResetStormLog">
            <summary>
            Resets the log of unusual busy patterns
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.PublishReconfigure(StackExchange.Redis.CommandFlags)">
            <summary>
            Request all compatible clients to reconfigure or reconnect
            </summary>
            <returns>The number of instances known to have received the message (however, the actual number can be higher; returns -1 if the operation is pending)</returns>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.PublishReconfigureAsync(StackExchange.Redis.CommandFlags)">
            <summary>
            Request all compatible clients to reconfigure or reconnect
            </summary>
            <returns>The number of instances known to have received the message (however, the actual number can be higher)</returns>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.RegisterProfiler(StackExchange.Redis.IProfiler)">
            <summary>
            Sets an IProfiler instance for this ConnectionMultiplexer.
            
            An IProfiler instances is used to determine which context to associate an
            IProfiledCommand with.  See BeginProfiling(object) and FinishProfiling(object)
            for more details.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.BeginProfiling(System.Object)">
            <summary>
            Begins profiling for the given context.
            
            If the same context object is returned by the registered IProfiler, the IProfiledCommands
            will be associated with each other.
            
            Call FinishProfiling with the same context to get the assocated commands.
            
            Note that forContext cannot be a WeakReference or a WeakReference&lt;T&gt;
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ConnectionMultiplexer.FinishProfiling(System.Object,System.Boolean)">
            <summary>
            Stops profiling for the given context, returns all IProfiledCommands associated.
            
            By default this may do a sweep for dead profiling contexts, you can disable this by passing "allowCleanupSweep: false".
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ConnectionType">
            <summary>
            The type of a connection
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ConnectionType.None">
            <summary>
            Not connection-type related
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ConnectionType.Interactive">
            <summary>
            An interactive connection handles request/response commands for accessing data on demand
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ConnectionType.Subscription">
            <summary>
            A subscriber connection recieves unsolicted messages from the server as pub/sub events occur
            </summary>
        </member>
        <member name="T:StackExchange.Redis.CompletionType">
            <summary>
            Completion type for CompletionTypeHelper
            </summary>
        </member>
        <member name="F:StackExchange.Redis.CompletionType.Any">
            <summary>
            Retain original completion type (either sync or async)
            </summary>
        </member>
        <member name="F:StackExchange.Redis.CompletionType.Sync">
            <summary>
            Force sync completion
            </summary>
        </member>
        <member name="F:StackExchange.Redis.CompletionType.Async">
            <summary>
            Force async completion
            </summary>
        </member>
        <member name="T:StackExchange.Redis.EndPointCollection">
            <summary>
            A list of endpoints
            </summary>
        </member>
        <member name="M:StackExchange.Redis.EndPointCollection.#ctor">
            <summary>
            Create a new EndPointCollection
            </summary>
        </member>
        <member name="M:StackExchange.Redis.EndPointCollection.#ctor(System.Collections.Generic.IList{System.Net.EndPoint})">
            <summary>
            Create a new EndPointCollection
            </summary>
        </member>
        <member name="M:StackExchange.Redis.EndPointCollection.ToString(System.Net.EndPoint)">
            <summary>
            Format an endpoint
            </summary>
        </member>
        <member name="M:StackExchange.Redis.EndPointCollection.TryParse(System.String)">
            <summary>
            Attempt to parse a string into an EndPoint
            </summary>
        </member>
        <member name="M:StackExchange.Redis.EndPointCollection.Add(System.String)">
            <summary>
            Adds a new endpoint to the list
            </summary>
        </member>
        <member name="M:StackExchange.Redis.EndPointCollection.Add(System.String,System.Int32)">
            <summary>
            Adds a new endpoint to the list
            </summary>
        </member>
        <member name="M:StackExchange.Redis.EndPointCollection.Add(System.Net.IPAddress,System.Int32)">
            <summary>
            Adds a new endpoint to the list
            </summary>
        </member>
        <member name="M:StackExchange.Redis.EndPointCollection.InsertItem(System.Int32,System.Net.EndPoint)">
            <summary>
            See Collection&lt;T&gt;.InsertItem()
            </summary>
        </member>
        <member name="M:StackExchange.Redis.EndPointCollection.SetItem(System.Int32,System.Net.EndPoint)">
            <summary>
            See Collection&lt;T&gt;.SetItem()
            </summary>
        </member>
        <member name="T:StackExchange.Redis.EndPointEventArgs">
            <summary>
            Event information related to redis endpoints
            </summary>
        </member>
        <member name="P:StackExchange.Redis.EndPointEventArgs.EndPoint">
            <summary>
            The endpoint involved in this event (this can be null)
            </summary>
        </member>
        <member name="T:StackExchange.Redis.Exclude">
            <summary>
            When performing a range query, by default the start / stop limits are inclusive;
            however, both can also be specified separately as exclusive
            </summary>
        </member>
        <member name="F:StackExchange.Redis.Exclude.None">
            <summary>
            Both start and stop are inclusive
            </summary>
        </member>
        <member name="F:StackExchange.Redis.Exclude.Start">
            <summary>
            Start is exclusive, stop is inclusive
            </summary>
        </member>
        <member name="F:StackExchange.Redis.Exclude.Stop">
            <summary>
            Start is inclusive, stop is exclusive
            </summary>
        </member>
        <member name="F:StackExchange.Redis.Exclude.Both">
            <summary>
            Both start and stop are exclusive
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ExportOptions">
            <summary>
            Which settings to export
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ExportOptions.None">
            <summary>
            No options
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ExportOptions.Info">
            <summary>
            The output of INFO
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ExportOptions.Config">
            <summary>
            The output of CONFIG GET *
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ExportOptions.Client">
            <summary>
            The output of CLIENT LIST
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ExportOptions.Cluster">
            <summary>
            The output of CLUSTER NODES
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ExportOptions.All">
            <summary>
            Everything available
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ExtensionMethods">
            <summary>
            Utility methods
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ExtensionMethods.ToStringDictionary(StackExchange.Redis.HashEntry[])">
            <summary>
            Create a dictionary from an array of HashEntry values 
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ExtensionMethods.ToDictionary(StackExchange.Redis.HashEntry[])">
            <summary>
            Create a dictionary from an array of HashEntry values 
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ExtensionMethods.ToStringDictionary(StackExchange.Redis.SortedSetEntry[])">
            <summary>
            Create a dictionary from an array of SortedSetEntry values 
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ExtensionMethods.ToDictionary(StackExchange.Redis.SortedSetEntry[])">
            <summary>
            Create a dictionary from an array of SortedSetEntry values 
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ExtensionMethods.ToStringDictionary(System.Collections.Generic.KeyValuePair{StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue}[])">
            <summary>
            Create a dictionary from an array of key/value pairs
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ExtensionMethods.ToDictionary(System.Collections.Generic.KeyValuePair{StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue}[])">
            <summary>
            Create a dictionary from an array of key/value pairs
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ExtensionMethods.ToDictionary(System.Collections.Generic.KeyValuePair{System.String,System.String}[])">
            <summary>
            Create a dictionary from an array of string pairs
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ExtensionMethods.ToStringArray(StackExchange.Redis.RedisValue[])">
            <summary>
            Create an array of strings from an array of values
            </summary>
        </member>
        <member name="T:StackExchange.Redis.HashEntry">
            <summary>
            Describes a hash-field (a name/value pair)
            </summary>
        </member>
        <member name="M:StackExchange.Redis.HashEntry.#ctor(StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue)">
            <summary>
            Initializes a HashEntry value
            </summary>
        </member>
        <member name="P:StackExchange.Redis.HashEntry.Name">
            <summary>
            The name of the hash field
            </summary>
        </member>
        <member name="P:StackExchange.Redis.HashEntry.Value">
            <summary>
            The value of the hash field
            </summary>
        </member>
        <member name="P:StackExchange.Redis.HashEntry.Key">
            <summary>
            The name of the hash field
            </summary>
        </member>
        <member name="M:StackExchange.Redis.HashEntry.op_Implicit(StackExchange.Redis.HashEntry)~System.Collections.Generic.KeyValuePair{StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue}">
            <summary>
            Converts to a key/value pair
            </summary>
        </member>
        <member name="M:StackExchange.Redis.HashEntry.op_Implicit(System.Collections.Generic.KeyValuePair{StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue})~StackExchange.Redis.HashEntry">
            <summary>
            Converts from a key/value pair
            </summary>
        </member>
        <member name="M:StackExchange.Redis.HashEntry.ToString">
            <summary>
            See Object.ToString()
            </summary>
        </member>
        <member name="M:StackExchange.Redis.HashEntry.GetHashCode">
            <summary>
            See Object.GetHashCode()
            </summary>
        </member>
        <member name="M:StackExchange.Redis.HashEntry.Equals(System.Object)">
            <summary>
            Compares two values for equality
            </summary>
        </member>
        <member name="M:StackExchange.Redis.HashEntry.Equals(StackExchange.Redis.HashEntry)">
            <summary>
            Compares two values for equality
            </summary>
        </member>
        <member name="M:StackExchange.Redis.HashEntry.op_Equality(StackExchange.Redis.HashEntry,StackExchange.Redis.HashEntry)">
            <summary>
            Compares two values for equality
            </summary>
        </member>
        <member name="M:StackExchange.Redis.HashEntry.op_Inequality(StackExchange.Redis.HashEntry,StackExchange.Redis.HashEntry)">
            <summary>
            Compares two values for non-equality
            </summary>
        </member>
        <member name="T:StackExchange.Redis.HashSlotMovedEventArgs">
            <summary>
            Contains information about individual hash-slot relocations
            </summary>
        </member>
        <member name="P:StackExchange.Redis.HashSlotMovedEventArgs.HashSlot">
            <summary>
            The hash-slot that was relocated
            </summary>
        </member>
        <member name="P:StackExchange.Redis.HashSlotMovedEventArgs.OldEndPoint">
            <summary>
            The old endpoint for this hash-slot (if known)
            </summary>
        </member>
        <member name="P:StackExchange.Redis.HashSlotMovedEventArgs.NewEndPoint">
            <summary>
            The new endpoint for this hash-slot (if known)
            </summary>
        </member>
        <member name="T:StackExchange.Redis.IBatch">
            <summary>
            Represents a block of operations that will be sent to the server together;
            this can be useful to reduce packet fragmentation on slow connections - it
            can improve the time to get *all* the operations processed, with the trade-off
            of a slower time to get the *first* operation processed; this is usually
            a good thing. Unless this batch is a <b>transaction</b>, there is no guarantee
            that these operations will be processed either contiguously or atomically by the server.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IBatch.Execute">
            <summary>
            Execute the batch operation, sending all queued commands to the server.
            Note that this operation is neither synchronous nor truly asyncronous - it
            simply enqueues the buffered messages. To check on completion, you should
            check the individual responses.
            </summary>
        </member>
        <member name="T:StackExchange.Redis.IConnectionMultiplexer">
            <summary>
            Represents the abstract multiplexer API
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IConnectionMultiplexer.ClientName">
            <summary>
            Gets the client-name that will be used on all new connections
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IConnectionMultiplexer.Configuration">
            <summary>
            Gets the configuration of the connection
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IConnectionMultiplexer.TimeoutMilliseconds">
            <summary>
            Gets the timeout associated with the connections
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IConnectionMultiplexer.OperationCount">
            <summary>
            The number of operations that have been performed on all connections
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IConnectionMultiplexer.PreserveAsyncOrder">
            <summary>
            Gets or sets whether asynchronous operations should be invoked in a way that guarantees their original delivery order
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IConnectionMultiplexer.IsConnected">
            <summary>
            Indicates whether any servers are connected
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IConnectionMultiplexer.IncludeDetailInExceptions">
            <summary>
            Should exceptions include identifiable details? (key names, additional .Data annotations)
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IConnectionMultiplexer.StormLogThreshold">
            <summary>
            Limit at which to start recording unusual busy patterns (only one log will be retained at a time;
            set to a negative value to disable this feature)
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.RegisterProfiler(StackExchange.Redis.IProfiler)">
            <summary>
            Sets an IProfiler instance for this ConnectionMultiplexer.
            
            An IProfiler instances is used to determine which context to associate an
            IProfiledCommand with.  See BeginProfiling(object) and FinishProfiling(object)
            for more details.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.BeginProfiling(System.Object)">
            <summary>
            Begins profiling for the given context.
            
            If the same context object is returned by the registered IProfiler, the IProfiledCommands
            will be associated with each other.
            
            Call FinishProfiling with the same context to get the assocated commands.
            
            Note that forContext cannot be a WeakReference or a WeakReference&lt;T&gt;
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.FinishProfiling(System.Object,System.Boolean)">
            <summary>
            Stops profiling for the given context, returns all IProfiledCommands associated.
            
            By default this may do a sweep for dead profiling contexts, you can disable this by passing "allowCleanupSweep: false".
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.GetCounters">
            <summary>
            Get summary statistics associates with this server
            </summary>
        </member>
        <member name="E:StackExchange.Redis.IConnectionMultiplexer.ErrorMessage">
            <summary>
            A server replied with an error message;
            </summary>
        </member>
        <member name="E:StackExchange.Redis.IConnectionMultiplexer.ConnectionFailed">
            <summary>
            Raised whenever a physical connection fails
            </summary>
        </member>
        <member name="E:StackExchange.Redis.IConnectionMultiplexer.InternalError">
            <summary>
            Raised whenever an internal error occurs (this is primarily for debugging)
            </summary>
        </member>
        <member name="E:StackExchange.Redis.IConnectionMultiplexer.ConnectionRestored">
            <summary>
            Raised whenever a physical connection is established
            </summary>
        </member>
        <member name="E:StackExchange.Redis.IConnectionMultiplexer.ConfigurationChanged">
            <summary>
            Raised when configuration changes are detected
            </summary>
        </member>
        <member name="E:StackExchange.Redis.IConnectionMultiplexer.ConfigurationChangedBroadcast">
            <summary>
            Raised when nodes are explicitly requested to reconfigure via broadcast;
            this usually means master/slave changes
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.GetEndPoints(System.Boolean)">
            <summary>
            Gets all endpoints defined on the server
            </summary>
            <returns></returns>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.Wait(System.Threading.Tasks.Task)">
            <summary>
            Wait for a given asynchronous operation to complete (or timeout)
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.Wait``1(System.Threading.Tasks.Task{``0})">
            <summary>
            Wait for a given asynchronous operation to complete (or timeout)
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.WaitAll(System.Threading.Tasks.Task[])">
            <summary>
            Wait for the given asynchronous operations to complete (or timeout)
            </summary>
        </member>
        <member name="E:StackExchange.Redis.IConnectionMultiplexer.HashSlotMoved">
            <summary>
            Raised when a hash-slot has been relocated
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.HashSlot(StackExchange.Redis.RedisKey)">
            <summary>
            Compute the hash-slot of a specified key
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.GetSubscriber(System.Object)">
            <summary>
            Obtain a pub/sub subscriber connection to the specified server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.GetDatabase(System.Int32,System.Object)">
            <summary>
            Obtain an interactive connection to a database inside redis
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.GetServer(System.String,System.Int32,System.Object)">
            <summary>
            Obtain a configuration API for an individual server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.GetServer(System.String,System.Object)">
            <summary>
            Obtain a configuration API for an individual server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.GetServer(System.Net.IPAddress,System.Int32)">
            <summary>
            Obtain a configuration API for an individual server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.GetServer(System.Net.EndPoint,System.Object)">
            <summary>
            Obtain a configuration API for an individual server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.ConfigureAsync(System.IO.TextWriter)">
            <summary>
            Reconfigure the current connections based on the existing configuration
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.Configure(System.IO.TextWriter)">
            <summary>
            Reconfigure the current connections based on the existing configuration
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.GetStatus">
            <summary>
            Provides a text overview of the status of all connections
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.GetStatus(System.IO.TextWriter)">
            <summary>
            Provides a text overview of the status of all connections
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.ToString">
            <summary>
            See Object.ToString()
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.Close(System.Boolean)">
            <summary>
            Close all connections and release all resources associated with this object
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.CloseAsync(System.Boolean)">
            <summary>
            Close all connections and release all resources associated with this object
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.Dispose">
            <summary>
            Release all resources associated with this object
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.GetStormLog">
            <summary>
            Obtains the log of unusual busy patterns
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.ResetStormLog">
            <summary>
            Resets the log of unusual busy patterns
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.PublishReconfigure(StackExchange.Redis.CommandFlags)">
            <summary>
            Request all compatible clients to reconfigure or reconnect
            </summary>
            <returns>The number of instances known to have received the message (however, the actual number can be higher; returns -1 if the operation is pending)</returns>
        </member>
        <member name="M:StackExchange.Redis.IConnectionMultiplexer.PublishReconfigureAsync(StackExchange.Redis.CommandFlags)">
            <summary>
            Request all compatible clients to reconfigure or reconnect
            </summary>
            <returns>The number of instances known to have received the message (however, the actual number can be higher)</returns>
        </member>
        <member name="T:StackExchange.Redis.IDatabase">
            <summary>
            Describes functionality that is common to both standalone redis servers and redis clusters
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IDatabase.Database">
            <summary>
            The numeric identifier of this database
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.CreateBatch(System.Object)">
            <summary>
            Allows creation of a group of operations that will be sent to the server as a single unit,
            but which may or may not be processed on the server contiguously.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.KeyMigrate(StackExchange.Redis.RedisKey,System.Net.EndPoint,System.Int32,System.Int32,StackExchange.Redis.MigrateOptions,StackExchange.Redis.CommandFlags)">
            <summary>
            Atomically transfer a key from a source Redis instance to a destination Redis instance. On success the key is deleted from the original instance by default, and is guaranteed to exist in the target instance.
            </summary>
            <remarks>http://redis.io/commands/MIGRATE</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.CreateTransaction(System.Object)">
            <summary>
            Allows creation of a group of operations that will be sent to the server as a single unit,
            and processed on the server as a single unit.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.DebugObject(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the raw DEBUG OBJECT output for a key; this command is not fully documented and should be avoided unless you have good reason, and then avoided anyway.
            </summary>
            <remarks>http://redis.io/commands/debug-object</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HashDecrement(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Decrements the number stored at field in the hash stored at key by decrement. If key does not exist, a new key holding a hash is created. If field does not exist or holds a string that cannot be interpreted as integer, the value is set to 0 before the operation is performed.
            </summary>
            <remarks>The range of values supported by HINCRBY is limited to 64 bit signed integers.</remarks>
            <returns>the value at field after the decrement operation.</returns>
            <remarks>http://redis.io/commands/hincrby</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HashDecrement(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Double,StackExchange.Redis.CommandFlags)">
            <summary>
            Decrement the specified field of an hash stored at key, and representing a floating point number, by the specified decrement. If the field does not exist, it is set to 0 before performing the operation.
            </summary>
            <remarks>The precision of the output is fixed at 17 digits after the decimal point regardless of the actual internal precision of the computation.</remarks>
            <returns>the value at field after the decrement operation.</returns>
            <remarks>http://redis.io/commands/hincrbyfloat</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HashDelete(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Removes the specified fields from the hash stored at key. Non-existing fields are ignored. Non-existing keys are treated as empty hashes and this command returns 0.
            </summary>
            <remarks>http://redis.io/commands/hdel</remarks>
            <returns>The number of fields that were removed.</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HashDelete(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Removes the specified fields from the hash stored at key. Non-existing fields are ignored. Non-existing keys are treated as empty hashes and this command returns 0.
            </summary>
            <remarks>http://redis.io/commands/hdel</remarks>
            <returns>The number of fields that were removed.</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HashExists(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns if field is an existing field in the hash stored at key.
            </summary>
            <returns>1 if the hash contains field. 0 if the hash does not contain field, or key does not exist.</returns>
            <remarks>http://redis.io/commands/hexists</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HashGet(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the value associated with field in the hash stored at key.
            </summary>
            <returns>the value associated with field, or nil when field is not present in the hash or key does not exist.</returns>
            <remarks>http://redis.io/commands/hget</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HashGet(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the values associated with the specified fields in the hash stored at key.
            For every field that does not exist in the hash, a nil value is returned.Because a non-existing keys are treated as empty hashes, running HMGET against a non-existing key will return a list of nil values.
            </summary>
            <returns>list of values associated with the given fields, in the same order as they are requested.</returns>
            <remarks>http://redis.io/commands/hmget</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HashGetAll(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns all fields and values of the hash stored at key. 
            </summary>
            <returns>list of fields and their values stored in the hash, or an empty list when key does not exist.</returns>
            <remarks>http://redis.io/commands/hgetall</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HashIncrement(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Increments the number stored at field in the hash stored at key by increment. If key does not exist, a new key holding a hash is created. If field does not exist or holds a string that cannot be interpreted as integer, the value is set to 0 before the operation is performed.
            </summary>
            <remarks>The range of values supported by HINCRBY is limited to 64 bit signed integers.</remarks>
            <returns>the value at field after the increment operation.</returns>
            <remarks>http://redis.io/commands/hincrby</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HashIncrement(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Double,StackExchange.Redis.CommandFlags)">
            <summary>
            Increment the specified field of an hash stored at key, and representing a floating point number, by the specified increment. If the field does not exist, it is set to 0 before performing the operation.
            </summary>
            <remarks>The precision of the output is fixed at 17 digits after the decimal point regardless of the actual internal precision of the computation.</remarks>
            <returns>the value at field after the increment operation.</returns>
            <remarks>http://redis.io/commands/hincrbyfloat</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HashKeys(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns all field names in the hash stored at key.
            </summary>
            <returns>list of fields in the hash, or an empty list when key does not exist.</returns>
            <remarks>http://redis.io/commands/hkeys</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HashLength(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the number of fields contained in the hash stored at key.
            </summary>
            <returns>number of fields in the hash, or 0 when key does not exist.</returns>
            <remarks>http://redis.io/commands/hlen</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HashScan(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Int32,StackExchange.Redis.CommandFlags)">
            <summary>
            The HSCAN command is used to incrementally iterate over a hash
            </summary>
            <returns>yields all elements of the hash.</returns>
            <remarks>http://redis.io/commands/hscan</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HashScan(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Int32,System.Int64,System.Int32,StackExchange.Redis.CommandFlags)">
            <summary>
            The HSCAN command is used to incrementally iterate over a hash; note: to resume an iteration via <i>cursor</i>, cast the original enumerable or enumerator to <i>IScanningCursor</i>.
            </summary>
            <returns>yields all elements of the hash.</returns>
            <remarks>http://redis.io/commands/hscan</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HashSet(StackExchange.Redis.RedisKey,StackExchange.Redis.HashEntry[],StackExchange.Redis.CommandFlags)">
            <summary>
            Sets the specified fields to their respective values in the hash stored at key. This command overwrites any existing fields in the hash. If key does not exist, a new key holding a hash is created.
            </summary>
            <remarks>http://redis.io/commands/hmset</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HashSet(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue,StackExchange.Redis.When,StackExchange.Redis.CommandFlags)">
            <summary>
            Sets field in the hash stored at key to value. If key does not exist, a new key holding a hash is created. If field already exists in the hash, it is overwritten.
            </summary>
            <returns>1 if field is a new field in the hash and value was set. 0 if field already exists in the hash and the value was updated.</returns>
            <remarks>http://redis.io/commands/hset</remarks>
            <remarks>http://redis.io/commands/hsetnx</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HashValues(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns all values in the hash stored at key.
            </summary>
            <returns>list of values in the hash, or an empty list when key does not exist.</returns>
            <remarks>http://redis.io/commands/hvals</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HyperLogLogAdd(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Adds the element to the HyperLogLog data structure stored at the variable name specified as first argument.
            </summary>
            <returns>true if at least 1 HyperLogLog internal register was altered. false otherwise.</returns>
            <remarks>http://redis.io/commands/pfadd</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HyperLogLogAdd(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Adds all the element arguments to the HyperLogLog data structure stored at the variable name specified as first argument.
            </summary>
            <returns>true if at least 1 HyperLogLog internal register was altered. false otherwise.</returns>
            <remarks>http://redis.io/commands/pfadd</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HyperLogLogLength(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the approximated cardinality computed by the HyperLogLog data structure stored at the specified variable, or 0 if the variable does not exist.
            </summary>
            <returns>The approximated number of unique elements observed via HyperLogLogAdd.</returns>
            <remarks>http://redis.io/commands/pfcount</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HyperLogLogLength(StackExchange.Redis.RedisKey[],StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the approximated cardinality of the union of the HyperLogLogs passed, by internally merging the HyperLogLogs stored at the provided keys into a temporary hyperLogLog, or 0 if the variable does not exist.
            </summary>
            <returns>The approximated number of unique elements observed via HyperLogLogAdd.</returns>
            <remarks>http://redis.io/commands/pfcount</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HyperLogLogMerge(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Merge multiple HyperLogLog values into an unique value that will approximate the cardinality of the union of the observed Sets of the source HyperLogLog structures.
            </summary>
            <remarks>http://redis.io/commands/pfmerge</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.HyperLogLogMerge(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey[],StackExchange.Redis.CommandFlags)">
            <summary>
            Merge multiple HyperLogLog values into an unique value that will approximate the cardinality of the union of the observed Sets of the source HyperLogLog structures.
            </summary>
            <remarks>http://redis.io/commands/pfmerge</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.IdentifyEndpoint(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Inidicate exactly which redis server we are talking to
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.KeyDelete(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Removes the specified key. A key is ignored if it does not exist.
            </summary>
            <returns>True if the key was removed.</returns>
            <remarks>http://redis.io/commands/del</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.KeyDelete(StackExchange.Redis.RedisKey[],StackExchange.Redis.CommandFlags)">
            <summary>
            Removes the specified keys. A key is ignored if it does not exist.
            </summary>
            <returns>The number of keys that were removed.</returns>
            <remarks>http://redis.io/commands/del</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.KeyDump(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Serialize the value stored at key in a Redis-specific format and return it to the user. The returned value can be synthesized back into a Redis key using the RESTORE command.
            </summary>
            <returns>the serialized value.</returns>
            <remarks>http://redis.io/commands/dump</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.KeyExists(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns if key exists.
            </summary>
            <returns>1 if the key exists. 0 if the key does not exist.</returns>
            <remarks>http://redis.io/commands/exists</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.KeyExpire(StackExchange.Redis.RedisKey,System.Nullable{System.TimeSpan},StackExchange.Redis.CommandFlags)">
            <summary>
            Set a timeout on key. After the timeout has expired, the key will automatically be deleted. A key with an associated timeout is said to be volatile in Redis terminology.
            </summary>
            <remarks>If key is updated before the timeout has expired, then the timeout is removed as if the PERSIST command was invoked on key.
            For Redis versions &lt; 2.1.3, existing timeouts cannot be overwritten. So, if key already has an associated timeout, it will do nothing and return 0. Since Redis 2.1.3, you can update the timeout of a key. It is also possible to remove the timeout using the PERSIST command. See the page on key expiry for more information.</remarks>
            <returns>1 if the timeout was set. 0 if key does not exist or the timeout could not be set.</returns>
            <remarks>http://redis.io/commands/expire</remarks>
            <remarks>http://redis.io/commands/pexpire</remarks>
            <remarks>http://redis.io/commands/persist</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.KeyExpire(StackExchange.Redis.RedisKey,System.Nullable{System.DateTime},StackExchange.Redis.CommandFlags)">
            <summary>
            Set a timeout on key. After the timeout has expired, the key will automatically be deleted. A key with an associated timeout is said to be volatile in Redis terminology.
            </summary>
            <remarks>If key is updated before the timeout has expired, then the timeout is removed as if the PERSIST command was invoked on key.
            For Redis versions &lt; 2.1.3, existing timeouts cannot be overwritten. So, if key already has an associated timeout, it will do nothing and return 0. Since Redis 2.1.3, you can update the timeout of a key. It is also possible to remove the timeout using the PERSIST command. See the page on key expiry for more information.</remarks>
            <returns>1 if the timeout was set. 0 if key does not exist or the timeout could not be set.</returns>
            <remarks>http://redis.io/commands/expireat</remarks>
            <remarks>http://redis.io/commands/pexpireat</remarks>
            <remarks>http://redis.io/commands/persist</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.KeyMove(StackExchange.Redis.RedisKey,System.Int32,StackExchange.Redis.CommandFlags)">
            <summary>
            Move key from the currently selected database (see SELECT) to the specified destination database. When key already exists in the destination database, or it does not exist in the source database, it does nothing. It is possible to use MOVE as a locking primitive because of this.
            </summary>
            <returns>1 if key was moved; 0 if key was not moved.</returns>
            <remarks>http://redis.io/commands/move</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.KeyPersist(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>Remove the existing timeout on key, turning the key from volatile (a key with an expire set) to persistent (a key that will never expire as no timeout is associated).</summary>
            <returns>1 if the timeout was removed. 0 if key does not exist or does not have an associated timeout.</returns>
            <remarks>http://redis.io/commands/persist</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.KeyRandom(StackExchange.Redis.CommandFlags)">
            <summary>
            Return a random key from the currently selected database.
            </summary>
            <returns>the random key, or nil when the database is empty.</returns>
            <remarks>http://redis.io/commands/randomkey</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.KeyRename(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.When,StackExchange.Redis.CommandFlags)">
            <summary>
            Renames key to newkey. It returns an error when the source and destination names are the same, or when key does not exist. 
            </summary>
            <returns>http://redis.io/commands/rename</returns>
            <remarks>http://redis.io/commands/renamenx</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.KeyRestore(StackExchange.Redis.RedisKey,System.Byte[],System.Nullable{System.TimeSpan},StackExchange.Redis.CommandFlags)">
            <summary>
            Create a key associated with a value that is obtained by deserializing the provided serialized value (obtained via DUMP).
            If ttl is 0 the key is created without any expire, otherwise the specified expire time(in milliseconds) is set.
            </summary>
            <remarks>http://redis.io/commands/restore</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.KeyTimeToLive(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the remaining time to live of a key that has a timeout.  This introspection capability allows a Redis client to check how many seconds a given key will continue to be part of the dataset.
            </summary>
            <returns>TTL, or nil when key does not exist or does not have a timeout.</returns>
            <remarks>http://redis.io/commands/ttl</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.KeyType(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the string representation of the type of the value stored at key. The different types that can be returned are: string, list, set, zset and hash.
            </summary>
            <returns>type of key, or none when key does not exist.</returns>
            <remarks>http://redis.io/commands/type</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.ListGetByIndex(StackExchange.Redis.RedisKey,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the element at index index in the list stored at key. The index is zero-based, so 0 means the first element, 1 the second element and so on. Negative indices can be used to designate elements starting at the tail of the list. Here, -1 means the last element, -2 means the penultimate and so forth.
            </summary>
            <returns>the requested element, or nil when index is out of range.</returns>
            <remarks>http://redis.io/commands/lindex</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.ListInsertAfter(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Inserts value in the list stored at key either before or after the reference value pivot.
            When key does not exist, it is considered an empty list and no operation is performed.
            </summary>
            <returns>the length of the list after the insert operation, or -1 when the value pivot was not found.</returns>
            <remarks>http://redis.io/commands/linsert</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.ListInsertBefore(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Inserts value in the list stored at key either before or after the reference value pivot.
            When key does not exist, it is considered an empty list and no operation is performed.
            </summary>
            <returns>the length of the list after the insert operation, or -1 when the value pivot was not found.</returns>
            <remarks>http://redis.io/commands/linsert</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.ListLeftPop(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Removes and returns the first element of the list stored at key.
            </summary>
            <returns>the value of the first element, or nil when key does not exist.</returns>
            <remarks>http://redis.io/commands/lpop</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.ListLeftPush(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.When,StackExchange.Redis.CommandFlags)">
            <summary>
            Insert the specified value at the head of the list stored at key. If key does not exist, it is created as empty list before performing the push operations.
            </summary>
            <returns>the length of the list after the push operations.</returns>
            <remarks>http://redis.io/commands/lpush</remarks>
            <remarks>http://redis.io/commands/lpushx</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.ListLeftPush(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Insert all the specified values at the head of the list stored at key. If key does not exist, it is created as empty list before performing the push operations.
            Elements are inserted one after the other to the head of the list, from the leftmost element to the rightmost element. So for instance the command LPUSH mylist a b c will result into a list containing c as first element, b as second element and a as third element.
            </summary>
            <returns>the length of the list after the push operations.</returns>
            <remarks>http://redis.io/commands/lpush</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.ListLength(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the length of the list stored at key. If key does not exist, it is interpreted as an empty list and 0 is returned. 
            </summary>
            <returns>the length of the list at key.</returns>
            <remarks>http://redis.io/commands/llen</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.ListRange(StackExchange.Redis.RedisKey,System.Int64,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the specified elements of the list stored at key. The offsets start and stop are zero-based indexes, with 0 being the first element of the list (the head of the list), 1 being the next element and so on.
            These offsets can also be negative numbers indicating offsets starting at the end of the list.For example, -1 is the last element of the list, -2 the penultimate, and so on.
            Note that if you have a list of numbers from 0 to 100, LRANGE list 0 10 will return 11 elements, that is, the rightmost item is included. 
            </summary>
            <returns>list of elements in the specified range.</returns>
            <remarks>http://redis.io/commands/lrange</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.ListRemove(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Removes the first count occurrences of elements equal to value from the list stored at key. The count argument influences the operation in the following ways:
            count &gt; 0: Remove elements equal to value moving from head to tail.
            count &lt; 0: Remove elements equal to value moving from tail to head.
            count = 0: Remove all elements equal to value.
            </summary>
            <returns>the number of removed elements.</returns>
            <remarks>http://redis.io/commands/lrem</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.ListRightPop(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Removes and returns the last element of the list stored at key.
            </summary>
            <remarks>http://redis.io/commands/rpop</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.ListRightPopLeftPush(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Atomically returns and removes the last element (tail) of the list stored at source, and pushes the element at the first element (head) of the list stored at destination.
            </summary>
            <returns>the element being popped and pushed.</returns>
            <remarks>http://redis.io/commands/rpoplpush</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.ListRightPush(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.When,StackExchange.Redis.CommandFlags)">
            <summary>
            Insert the specified value at the tail of the list stored at key. If key does not exist, it is created as empty list before performing the push operation.
            </summary>
            <returns>the length of the list after the push operation.</returns>
            <remarks>http://redis.io/commands/rpush</remarks>
            <remarks>http://redis.io/commands/rpushx</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.ListRightPush(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Insert all the specified values at the tail of the list stored at key. If key does not exist, it is created as empty list before performing the push operation. 
            Elements are inserted one after the other to the tail of the list, from the leftmost element to the rightmost element. So for instance the command RPUSH mylist a b c will result into a list containing a as first element, b as second element and c as third element.
            </summary>
            <returns>the length of the list after the push operation.</returns>
            <remarks>http://redis.io/commands/rpush</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.ListSetByIndex(StackExchange.Redis.RedisKey,System.Int64,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Sets the list element at index to value. For more information on the index argument, see ListGetByIndex. An error is returned for out of range indexes.
            </summary>
            <remarks>http://redis.io/commands/lset</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.ListTrim(StackExchange.Redis.RedisKey,System.Int64,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Trim an existing list so that it will contain only the specified range of elements specified. Both start and stop are zero-based indexes, where 0 is the first element of the list (the head), 1 the next element and so on.
            For example: LTRIM foobar 0 2 will modify the list stored at foobar so that only the first three elements of the list will remain.
            start and end can also be negative numbers indicating offsets from the end of the list, where -1 is the last element of the list, -2 the penultimate element and so on.
            </summary>
            <remarks>http://redis.io/commands/ltrim</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.LockExtend(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.TimeSpan,StackExchange.Redis.CommandFlags)">
            <summary>
            Extends a lock, if the token value is correct
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.LockQuery(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Queries the token held against a lock
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.LockRelease(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Releases a lock, if the token value is correct
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.LockTake(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.TimeSpan,StackExchange.Redis.CommandFlags)">
            <summary>
            Takes a lock (specifying a token value) if it is not already taken
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.Publish(StackExchange.Redis.RedisChannel,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Posts a message to the given channel.
            </summary>
            <returns>the number of clients that received the message.</returns>
            <remarks>http://redis.io/commands/publish</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.ScriptEvaluate(System.String,StackExchange.Redis.RedisKey[],StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Execute a Lua script against the server
            </summary>
            <remarks>http://redis.io/commands/eval, http://redis.io/commands/evalsha</remarks>
            <returns>A dynamic representation of the script's result</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.ScriptEvaluate(System.Byte[],StackExchange.Redis.RedisKey[],StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Execute a Lua script against the server using just the SHA1 hash
            </summary>
            <remarks>http://redis.io/commands/evalsha</remarks>
            <returns>A dynamic representation of the script's result</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.ScriptEvaluate(StackExchange.Redis.LuaScript,System.Object,StackExchange.Redis.CommandFlags)">
            <summary>
            Execute a lua script against the server, using previously prepared script.
            Named parameters, if any, are provided by the `parameters` object.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.ScriptEvaluate(StackExchange.Redis.LoadedLuaScript,System.Object,StackExchange.Redis.CommandFlags)">
            <summary>
            Execute a lua script against the server, using previously prepared and loaded script.
            This method sends only the SHA1 hash of the lua script to Redis.
            Named parameters, if any, are provided by the `parameters` object.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SetAdd(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Add the specified member to the set stored at key. Specified members that are already a member of this set are ignored. If key does not exist, a new set is created before adding the specified members.
            </summary>
            <returns>True if the specified member was not already present in the set, else False</returns>
            <remarks>http://redis.io/commands/sadd</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SetAdd(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Add the specified members to the set stored at key. Specified members that are already a member of this set are ignored. If key does not exist, a new set is created before adding the specified members.
            </summary>
            <returns>the number of elements that were added to the set, not including all the elements already present into the set.</returns>
            <remarks>http://redis.io/commands/sadd</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SetCombine(StackExchange.Redis.SetOperation,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the members of the set resulting from the specified operation against the given sets.
            </summary>
            <returns>list with members of the resulting set.</returns>
            <remarks>http://redis.io/commands/sunion</remarks>
            <remarks>http://redis.io/commands/sinter</remarks>
            <remarks>http://redis.io/commands/sdiff</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SetCombine(StackExchange.Redis.SetOperation,StackExchange.Redis.RedisKey[],StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the members of the set resulting from the specified operation against the given sets.
            </summary>
            <returns>list with members of the resulting set.</returns>
            <remarks>http://redis.io/commands/sunion</remarks>
            <remarks>http://redis.io/commands/sinter</remarks>
            <remarks>http://redis.io/commands/sdiff</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SetCombineAndStore(StackExchange.Redis.SetOperation,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            This command is equal to SetCombine, but instead of returning the resulting set, it is stored in destination. If destination already exists, it is overwritten.
            </summary>
            <returns>the number of elements in the resulting set.</returns>
            <remarks>http://redis.io/commands/sunionstore</remarks>
            <remarks>http://redis.io/commands/sinterstore</remarks>
            <remarks>http://redis.io/commands/sdiffstore</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SetCombineAndStore(StackExchange.Redis.SetOperation,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey[],StackExchange.Redis.CommandFlags)">
            <summary>
            This command is equal to SetCombine, but instead of returning the resulting set, it is stored in destination. If destination already exists, it is overwritten.
            </summary>
            <returns>the number of elements in the resulting set.</returns>
            <remarks>http://redis.io/commands/sunionstore</remarks>
            <remarks>http://redis.io/commands/sinterstore</remarks>
            <remarks>http://redis.io/commands/sdiffstore</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SetContains(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns if member is a member of the set stored at key.
            </summary>
            <returns>1 if the element is a member of the set. 0 if the element is not a member of the set, or if key does not exist.</returns>
            <remarks>http://redis.io/commands/sismember</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SetLength(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the set cardinality (number of elements) of the set stored at key.
            </summary>
            <returns>the cardinality (number of elements) of the set, or 0 if key does not exist.</returns>
            <remarks>http://redis.io/commands/scard</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SetMembers(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns all the members of the set value stored at key.
            </summary>
            <returns>all elements of the set.</returns>
            <remarks>http://redis.io/commands/smembers</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SetMove(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Move member from the set at source to the set at destination. This operation is atomic. In every given moment the element will appear to be a member of source or destination for other clients.
            When the specified element already exists in the destination set, it is only removed from the source set.
            </summary>
            <returns>1 if the element is moved. 0 if the element is not a member of source and no operation was performed.</returns>
            <remarks>http://redis.io/commands/smove</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SetPop(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Removes and returns a random element from the set value stored at key.
            </summary>
            <returns>the removed element, or nil when key does not exist.</returns>
            <remarks>http://redis.io/commands/spop</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SetRandomMember(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Return a random element from the set value stored at key.
            </summary>
            <returns>the randomly selected element, or nil when key does not exist</returns>
            <remarks>http://redis.io/commands/srandmember</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SetRandomMembers(StackExchange.Redis.RedisKey,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Return an array of count distinct elements if count is positive. If called with a negative count the behavior changes and the command is allowed to return the same element multiple times.
            In this case the numer of returned elements is the absolute value of the specified count.
            </summary>
            <returns>an array of elements, or an empty array when key does not exist</returns>
            <remarks>http://redis.io/commands/srandmember</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SetRemove(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Remove the specified member from the set stored at key.  Specified members that are not a member of this set are ignored.
            </summary>
            <returns>True if the specified member was already present in the set, else False</returns>
            <remarks>http://redis.io/commands/srem</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SetRemove(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Remove the specified members from the set stored at key. Specified members that are not a member of this set are ignored.
            </summary>
            <returns>the number of members that were removed from the set, not including non existing members.</returns>
            <remarks>http://redis.io/commands/srem</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SetScan(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Int32,StackExchange.Redis.CommandFlags)">
            <summary>
            The SSCAN command is used to incrementally iterate over set
            </summary>
            <returns>yields all elements of the set.</returns>
            <remarks>http://redis.io/commands/sscan</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SetScan(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Int32,System.Int64,System.Int32,StackExchange.Redis.CommandFlags)">
            <summary>
            The SSCAN command is used to incrementally iterate over set; note: to resume an iteration via <i>cursor</i>, cast the original enumerable or enumerator to <i>IScanningCursor</i>.
            </summary>
            <returns>yields all elements of the set.</returns>
            <remarks>http://redis.io/commands/sscan</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.Sort(StackExchange.Redis.RedisKey,System.Int64,System.Int64,StackExchange.Redis.Order,StackExchange.Redis.SortType,StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Sorts a list, set or sorted set (numerically or alphabetically, ascending by default); By default, the elements themselves are compared, but the values can also be
            used to perform external key-lookups using the <c>by</c> parameter. By default, the elements themselves are returned, but external key-lookups (one or many) can
            be performed instead by specifying the <c>get</c> parameter (note that <c>#</c> specifies the element itself, when used in <c>get</c>).
            Referring to the <a href="http://redis.io/commands/sort">redis SORT documentation </a> for examples is recommended. When used in hashes, <c>by</c> and <c>get</c>
            can be used to specify fields using <c>-&gt;</c> notation (again, refer to redis documentation).
            </summary>
            <remarks>http://redis.io/commands/sort</remarks>
            <returns>Returns the sorted elements, or the external values if <c>get</c> is specified</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortAndStore(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,System.Int64,System.Int64,StackExchange.Redis.Order,StackExchange.Redis.SortType,StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Sorts a list, set or sorted set (numerically or alphabetically, ascending by default); By default, the elements themselves are compared, but the values can also be
            used to perform external key-lookups using the <c>by</c> parameter. By default, the elements themselves are returned, but external key-lookups (one or many) can
            be performed instead by specifying the <c>get</c> parameter (note that <c>#</c> specifies the element itself, when used in <c>get</c>).
            Referring to the <a href="http://redis.io/commands/sort">redis SORT documentation </a> for examples is recommended. When used in hashes, <c>by</c> and <c>get</c>
            can be used to specify fields using <c>-&gt;</c> notation (again, refer to redis documentation).
            </summary>
            <remarks>http://redis.io/commands/sort</remarks>
            <returns>Returns the number of elements stored in the new list</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetAdd(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Double,StackExchange.Redis.CommandFlags)">
            <summary>
            Adds the specified member with the specified score to the sorted set stored at key. If the specified member is already a member of the sorted set, the score is updated and the element reinserted at the right position to ensure the correct ordering.
            </summary>
            <returns>True if the value was added, False if it already existed (the score is still updated)</returns>
            <remarks>http://redis.io/commands/zadd</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetAdd(StackExchange.Redis.RedisKey,StackExchange.Redis.SortedSetEntry[],StackExchange.Redis.CommandFlags)">
            <summary>
            Adds all the specified members with the specified scores to the sorted set stored at key. If a specified member is already a member of the sorted set, the score is updated and the element reinserted at the right position to ensure the correct ordering.
            </summary>
            <returns>The number of elements added to the sorted sets, not including elements already existing for which the score was updated.</returns>
            <remarks>http://redis.io/commands/zadd</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetCombineAndStore(StackExchange.Redis.SetOperation,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.Aggregate,StackExchange.Redis.CommandFlags)">
            <summary>
            Computes a set operation over two sorted sets, and stores the result in destination, optionally performing 
            a specific aggregation (defaults to sum)
            </summary>
            <remarks>http://redis.io/commands/zunionstore</remarks>
            <remarks>http://redis.io/commands/zinterstore</remarks>
            <returns>the number of elements in the resulting sorted set at destination</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetCombineAndStore(StackExchange.Redis.SetOperation,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey[],System.Double[],StackExchange.Redis.Aggregate,StackExchange.Redis.CommandFlags)">
            <summary>
            Computes a set operation over multiple sorted sets (optionally using per-set weights), and stores the result in destination, optionally performing 
            a specific aggregation (defaults to sum)
            </summary>
            <remarks>http://redis.io/commands/zunionstore</remarks>
            <remarks>http://redis.io/commands/zinterstore</remarks>
            <returns>the number of elements in the resulting sorted set at destination</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetDecrement(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Double,StackExchange.Redis.CommandFlags)">
            <summary>
            Decrements the score of member in the sorted set stored at key by decrement. If member does not exist in the sorted set, it is added with -decrement as its score (as if its previous score was 0.0).
            </summary>
            <returns>the new score of member</returns>
            <remarks>http://redis.io/commands/zincrby</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetIncrement(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Double,StackExchange.Redis.CommandFlags)">
            <summary>
            Increments the score of member in the sorted set stored at key by increment. If member does not exist in the sorted set, it is added with increment as its score (as if its previous score was 0.0).
            </summary>
            <returns>the new score of member</returns>
            <remarks>http://redis.io/commands/zincrby</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetLength(StackExchange.Redis.RedisKey,System.Double,System.Double,StackExchange.Redis.Exclude,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the sorted set cardinality (number of elements) of the sorted set stored at key.
            </summary>
            <returns>the cardinality (number of elements) of the sorted set, or 0 if key does not exist.</returns>
            <remarks>http://redis.io/commands/zcard</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetLengthByValue(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue,StackExchange.Redis.Exclude,StackExchange.Redis.CommandFlags)">
            <summary>
            When all the elements in a sorted set are inserted with the same score, in order to force lexicographical ordering, this command returns the number of elements in the sorted set at key with a value between min and max.
            </summary>
            <returns>the number of elements in the specified score range.</returns>
            <remarks>When all the elements in a sorted set are inserted with the same score, in order to force lexicographical ordering, this command returns all the elements in the sorted set at key with a value between min and max.</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetRangeByRank(StackExchange.Redis.RedisKey,System.Int64,System.Int64,StackExchange.Redis.Order,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the specified range of elements in the sorted set stored at key. By default the elements are considered to be ordered from the lowest to the highest score. Lexicographical order is used for elements with equal score.
            Both start and stop are zero-based indexes, where 0 is the first element, 1 is the next element and so on. They can also be negative numbers indicating offsets from the end of the sorted set, with -1 being the last element of the sorted set, -2 the penultimate element and so on.
            </summary>
            <returns>list of elements in the specified range</returns>
            <remarks>http://redis.io/commands/zrange</remarks>
            <remarks>http://redis.io/commands/zrevrange</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetRangeByRankWithScores(StackExchange.Redis.RedisKey,System.Int64,System.Int64,StackExchange.Redis.Order,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the specified range of elements in the sorted set stored at key. By default the elements are considered to be ordered from the lowest to the highest score. Lexicographical order is used for elements with equal score.
            Both start and stop are zero-based indexes, where 0 is the first element, 1 is the next element and so on. They can also be negative numbers indicating offsets from the end of the sorted set, with -1 being the last element of the sorted set, -2 the penultimate element and so on.
            </summary>
            <returns>list of elements in the specified range</returns>
            <remarks>http://redis.io/commands/zrange</remarks>
            <remarks>http://redis.io/commands/zrevrange</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetRangeByScore(StackExchange.Redis.RedisKey,System.Double,System.Double,StackExchange.Redis.Exclude,StackExchange.Redis.Order,System.Int64,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the specified range of elements in the sorted set stored at key. By default the elements are considered to be ordered from the lowest to the highest score. Lexicographical order is used for elements with equal score.
            Start and stop are used to specify the min and max range for score values. Similar to other range methods the values are inclusive.
            </summary>
            <returns>list of elements in the specified score range</returns>
            <remarks>http://redis.io/commands/zrangebyscore</remarks>
            <remarks>http://redis.io/commands/zrevrangebyscore</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetRangeByScoreWithScores(StackExchange.Redis.RedisKey,System.Double,System.Double,StackExchange.Redis.Exclude,StackExchange.Redis.Order,System.Int64,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the specified range of elements in the sorted set stored at key. By default the elements are considered to be ordered from the lowest to the highest score. Lexicographical order is used for elements with equal score.
            Start and stop are used to specify the min and max range for score values. Similar to other range methods the values are inclusive.
            </summary>
            <returns>list of elements in the specified score range</returns>
            <remarks>http://redis.io/commands/zrangebyscore</remarks>
            <remarks>http://redis.io/commands/zrevrangebyscore</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetRangeByValue(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue,StackExchange.Redis.Exclude,System.Int64,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            When all the elements in a sorted set are inserted with the same score, in order to force lexicographical ordering, this command returns all the elements in the sorted set at key with a value between min and max.
            </summary>
            <remarks>http://redis.io/commands/zrangebylex</remarks>
            <returns>list of elements in the specified score range.</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetRank(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.Order,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the rank of member in the sorted set stored at key, by default with the scores ordered from low to high. The rank (or index) is 0-based, which means that the member with the lowest score has rank 0.
            </summary>
            <returns>If member exists in the sorted set, the rank of member; If member does not exist in the sorted set or key does not exist, null</returns>
            <remarks>http://redis.io/commands/zrank</remarks>
            <remarks>http://redis.io/commands/zrevrank</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetRemove(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Removes the specified member from the sorted set stored at key. Non existing members are ignored.
            </summary>
            <returns>True if the member existed in the sorted set and was removed; False otherwise.</returns>
            <remarks>http://redis.io/commands/zrem</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetRemove(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Removes the specified members from the sorted set stored at key. Non existing members are ignored.
            </summary>
            <returns>The number of members removed from the sorted set, not including non existing members.</returns>
            <remarks>http://redis.io/commands/zrem</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetRemoveRangeByRank(StackExchange.Redis.RedisKey,System.Int64,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Removes all elements in the sorted set stored at key with rank between start and stop. Both start and stop are 0 -based indexes with 0 being the element with the lowest score. These indexes can be negative numbers, where they indicate offsets starting at the element with the highest score. For example: -1 is the element with the highest score, -2 the element with the second highest score and so forth.
            </summary>
            <returns> the number of elements removed.</returns>
            <remarks>http://redis.io/commands/zremrangebyrank</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetRemoveRangeByScore(StackExchange.Redis.RedisKey,System.Double,System.Double,StackExchange.Redis.Exclude,StackExchange.Redis.CommandFlags)">
            <summary>
            Removes all elements in the sorted set stored at key with a score between min and max (inclusive by default).
            </summary>
            <returns> the number of elements removed.</returns>
            <remarks>http://redis.io/commands/zremrangebyscore</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetRemoveRangeByValue(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue,StackExchange.Redis.Exclude,StackExchange.Redis.CommandFlags)">
            <summary>
            When all the elements in a sorted set are inserted with the same score, in order to force lexicographical ordering, this command removes all elements in the sorted set stored at key between the lexicographical range specified by min and max.
            </summary>
            <remarks>http://redis.io/commands/zremrangebylex</remarks>
            <returns>the number of elements removed.</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetScan(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Int32,StackExchange.Redis.CommandFlags)">
            <summary>
            The ZSCAN command is used to incrementally iterate over a sorted set
            </summary>
            <returns>yields all elements of the sorted set.</returns>
            <remarks>http://redis.io/commands/zscan</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetScan(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Int32,System.Int64,System.Int32,StackExchange.Redis.CommandFlags)">
            <summary>
            The ZSCAN command is used to incrementally iterate over a sorted set; note: to resume an iteration via <i>cursor</i>, cast the original enumerable or enumerator to <i>IScanningCursor</i>.
            </summary>
            <returns>yields all elements of the sorted set.</returns>
            <remarks>http://redis.io/commands/zscan</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.SortedSetScore(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the score of member in the sorted set at key; If member does not exist in the sorted set, or key does not exist, nil is returned.
            </summary>
            <returns>the score of member</returns>
            <remarks>http://redis.io/commands/zscore</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringAppend(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            If key already exists and is a string, this command appends the value at the end of the string. If key does not exist it is created and set as an empty string,
            so APPEND will be similar to SET in this special case.
            </summary>
            <returns>the length of the string after the append operation.</returns>
            <remarks>http://redis.io/commands/append</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringBitCount(StackExchange.Redis.RedisKey,System.Int64,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Count the number of set bits (population counting) in a string.
            By default all the bytes contained in the string are examined.It is possible to specify the counting operation only in an interval passing the additional arguments start and end.
            Like for the GETRANGE command start and end can contain negative values in order to index bytes starting from the end of the string, where -1 is the last byte, -2 is the penultimate, and so forth.
            </summary>
            <returns>The number of bits set to 1</returns>
            <remarks>http://redis.io/commands/bitcount</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringBitOperation(StackExchange.Redis.Bitwise,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Perform a bitwise operation between multiple keys (containing string values) and store the result in the destination key.
            The BITOP command supports four bitwise operations; note that NOT is a unary operator: the second key should be omitted in this case
            and only the first key will be considered.
            The result of the operation is always stored at destkey.
            </summary>
            <returns>The size of the string stored in the destination key, that is equal to the size of the longest input string.</returns>
            <remarks>http://redis.io/commands/bitop</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringBitOperation(StackExchange.Redis.Bitwise,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey[],StackExchange.Redis.CommandFlags)">
            <summary>
            Perform a bitwise operation between multiple keys (containing string values) and store the result in the destination key.
            The BITOP command supports four bitwise operations; note that NOT is a unary operator.
            The result of the operation is always stored at destkey.
            </summary>
            <returns>The size of the string stored in the destination key, that is equal to the size of the longest input string.</returns>
            <remarks>http://redis.io/commands/bitop</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringBitPosition(StackExchange.Redis.RedisKey,System.Boolean,System.Int64,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Return the position of the first bit set to 1 or 0 in a string.
            The position is returned thinking at the string as an array of bits from left to right where the first byte most significant bit is at position 0, the second byte most significant bit is at position 8 and so forth.
            An start and end may be specified; these are in bytes, not bits; start and end can contain negative values in order to index bytes starting from the end of the string, where -1 is the last byte, -2 is the penultimate, and so forth.
            </summary>
            <returns>The command returns the position of the first bit set to 1 or 0 according to the request.
            If we look for set bits(the bit argument is 1) and the string is empty or composed of just zero bytes, -1 is returned.</returns>
            <remarks>http://redis.io/commands/bitpos</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringDecrement(StackExchange.Redis.RedisKey,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Decrements the number stored at key by decrement. If the key does not exist, it is set to 0 before performing the operation.
            An error is returned if the key contains a value of the wrong type or contains a string that is not representable as integer. This operation is limited to 64 bit signed integers.
            </summary>
            <returns> the value of key after the decrement</returns>
            <remarks>http://redis.io/commands/decrby</remarks>
            <remarks>http://redis.io/commands/decr</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringDecrement(StackExchange.Redis.RedisKey,System.Double,StackExchange.Redis.CommandFlags)">
            <summary>
            Decrements the string representing a floating point number stored at key by the specified decrement. If the key does not exist, it is set to 0 before performing the operation. The precision of the output is fixed at 17 digits after the decimal point regardless of the actual internal precision of the computation.
            </summary>
            <returns>the value of key after the decrement</returns>
            <remarks>http://redis.io/commands/incrbyfloat</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringGet(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Get the value of key. If the key does not exist the special value nil is returned. An error is returned if the value stored at key is not a string, because GET only handles string values.
            </summary>
            <returns>the value of key, or nil when key does not exist.</returns>
            <remarks>http://redis.io/commands/get</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringGet(StackExchange.Redis.RedisKey[],StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the values of all specified keys. For every key that does not hold a string value or does not exist, the special value nil is returned.
            </summary>
            <remarks>http://redis.io/commands/mget</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringGetBit(StackExchange.Redis.RedisKey,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the bit value at offset in the string value stored at key.
            When offset is beyond the string length, the string is assumed to be a contiguous space with 0 bits.
            </summary>
            <returns>the bit value stored at offset.</returns>
            <remarks>http://redis.io/commands/getbit</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringGetRange(StackExchange.Redis.RedisKey,System.Int64,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the substring of the string value stored at key, determined by the offsets start and end (both are inclusive). Negative offsets can be used in order to provide an offset starting from the end of the string. So -1 means the last character, -2 the penultimate and so forth.
            </summary>
            <returns>the substring of the string value stored at key</returns>
            <remarks>http://redis.io/commands/getrange</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringGetSet(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Atomically sets key to value and returns the old value stored at key.
            </summary>
            <remarks>http://redis.io/commands/getset</remarks>
            <returns> the old value stored at key, or nil when key did not exist.</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringGetWithExpiry(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Get the value of key. If the key does not exist the special value nil is returned. An error is returned if the value stored at key is not a string, because GET only handles string values.
            </summary>
            <returns>the value of key, or nil when key does not exist.</returns>
            <remarks>http://redis.io/commands/get</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringIncrement(StackExchange.Redis.RedisKey,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Increments the number stored at key by increment. If the key does not exist, it is set to 0 before performing the operation. An error is returned if the key contains a value of the wrong type or contains a string that is not representable as integer. This operation is limited to 64 bit signed integers.
            </summary>
            <returns> the value of key after the increment</returns>
            <remarks>http://redis.io/commands/incrby</remarks>
            <remarks>http://redis.io/commands/incr</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringIncrement(StackExchange.Redis.RedisKey,System.Double,StackExchange.Redis.CommandFlags)">
            <summary>
            Increments the string representing a floating point number stored at key by the specified increment. If the key does not exist, it is set to 0 before performing the operation. The precision of the output is fixed at 17 digits after the decimal point regardless of the actual internal precision of the computation.
            </summary>
            <returns>the value of key after the increment</returns>
            <remarks>http://redis.io/commands/incrbyfloat</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringLength(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the length of the string value stored at key.
            </summary>
            <returns>the length of the string at key, or 0 when key does not exist.</returns>
            <remarks>http://redis.io/commands/strlen</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringSet(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Nullable{System.TimeSpan},StackExchange.Redis.When,StackExchange.Redis.CommandFlags)">
            <summary>
            Set key to hold the string value. If key already holds a value, it is overwritten, regardless of its type.
            </summary>
            <remarks>http://redis.io/commands/set</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringSet(System.Collections.Generic.KeyValuePair{StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue}[],StackExchange.Redis.When,StackExchange.Redis.CommandFlags)">
            <summary>
            Sets the given keys to their respective values. If "not exists" is specified, this will not perform any operation at all even if just a single key already exists.
            </summary>
            <returns>True if the keys were set, else False</returns>
            <remarks>http://redis.io/commands/mset</remarks>
            <remarks>http://redis.io/commands/msetnx</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringSetBit(StackExchange.Redis.RedisKey,System.Int64,System.Boolean,StackExchange.Redis.CommandFlags)">
            <summary>
            Sets or clears the bit at offset in the string value stored at key.
            The bit is either set or cleared depending on value, which can be either 0 or 1. When key does not exist, a new string value is created.The string is grown to make sure it can hold a bit at offset.
            </summary>
            <returns>the original bit value stored at offset.</returns>
            <remarks>http://redis.io/commands/setbit</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabase.StringSetRange(StackExchange.Redis.RedisKey,System.Int64,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Overwrites part of the string stored at key, starting at the specified offset, for the entire length of value. If the offset is larger than the current length of the string at key, the string is padded with zero-bytes to make offset fit. Non-existing keys are considered as empty strings, so this command will make sure it holds a string large enough to be able to set value at offset.
            </summary>
            <returns>the length of the string after it was modified by the command.</returns>
            <remarks>http://redis.io/commands/setrange</remarks>
        </member>
        <member name="T:StackExchange.Redis.IDatabaseAsync">
            <summary>
            Describes functionality that is common to both standalone redis servers and redis clusters
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.DebugObjectAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the raw DEBUG OBJECT output for a key; this command is not fully documented and should be avoided unless you have good reason, and then avoided anyway.
            </summary>
            <remarks>http://redis.io/commands/debug-object</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HashDecrementAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Increments the number stored at field in the hash stored at key by increment. If key does not exist, a new key holding a hash is created. If field does not exist or holds a string that cannot be interpreted as integer, the value is set to 0 before the operation is performed.
            </summary>
            <remarks>The range of values supported by HINCRBY is limited to 64 bit signed integers.</remarks>
            <returns>the value at field after the increment operation.</returns>
            <remarks>http://redis.io/commands/hincrby</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HashDecrementAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Double,StackExchange.Redis.CommandFlags)">
            <summary>
            Decrement the specified field of an hash stored at key, and representing a floating point number, by the specified decrement. If the field does not exist, it is set to 0 before performing the operation.
            </summary>
            <remarks>The precision of the output is fixed at 17 digits after the decimal point regardless of the actual internal precision of the computation.</remarks>
            <returns>the value at field after the decrement operation.</returns>
            <remarks>http://redis.io/commands/hincrbyfloat</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HashDeleteAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Removes the specified fields from the hash stored at key. Non-existing fields are ignored. Non-existing keys are treated as empty hashes and this command returns 0.
            </summary>
            <remarks>http://redis.io/commands/hdel</remarks>
            <returns>The number of fields that were removed.</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HashDeleteAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Removes the specified fields from the hash stored at key. Non-existing fields are ignored. Non-existing keys are treated as empty hashes and this command returns 0.
            </summary>
            <remarks>http://redis.io/commands/hdel</remarks>
            <returns>The number of fields that were removed.</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HashExistsAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns if field is an existing field in the hash stored at key.
            </summary>
            <returns>1 if the hash contains field. 0 if the hash does not contain field, or key does not exist.</returns>
            <remarks>http://redis.io/commands/hexists</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HashGetAllAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns all fields and values of the hash stored at key. 
            </summary>
            <returns>list of fields and their values stored in the hash, or an empty list when key does not exist.</returns>
            <remarks>http://redis.io/commands/hgetall</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HashGetAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the value associated with field in the hash stored at key.
            </summary>
            <returns>the value associated with field, or nil when field is not present in the hash or key does not exist.</returns>
            <remarks>http://redis.io/commands/hget</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HashGetAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the values associated with the specified fields in the hash stored at key.
            For every field that does not exist in the hash, a nil value is returned.Because a non-existing keys are treated as empty hashes, running HMGET against a non-existing key will return a list of nil values.
            </summary>
            <returns>list of values associated with the given fields, in the same order as they are requested.</returns>
            <remarks>http://redis.io/commands/hmget</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HashIncrementAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Increments the number stored at field in the hash stored at key by increment. If key does not exist, a new key holding a hash is created. If field does not exist or holds a string that cannot be interpreted as integer, the value is set to 0 before the operation is performed.
            </summary>
            <remarks>The range of values supported by HINCRBY is limited to 64 bit signed integers.</remarks>
            <returns>the value at field after the increment operation.</returns>
            <remarks>http://redis.io/commands/hincrby</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HashIncrementAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Double,StackExchange.Redis.CommandFlags)">
            <summary>
            Increment the specified field of an hash stored at key, and representing a floating point number, by the specified increment. If the field does not exist, it is set to 0 before performing the operation.
            </summary>
            <remarks>The precision of the output is fixed at 17 digits after the decimal point regardless of the actual internal precision of the computation.</remarks>
            <returns>the value at field after the increment operation.</returns>
            <remarks>http://redis.io/commands/hincrbyfloat</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HashKeysAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns all field names in the hash stored at key.
            </summary>
            <returns>list of fields in the hash, or an empty list when key does not exist.</returns>
            <remarks>http://redis.io/commands/hkeys</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HashLengthAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the number of fields contained in the hash stored at key.
            </summary>
            <returns>number of fields in the hash, or 0 when key does not exist.</returns>
            <remarks>http://redis.io/commands/hlen</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HashSetAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.HashEntry[],StackExchange.Redis.CommandFlags)">
            <summary>
            Sets the specified fields to their respective values in the hash stored at key. This command overwrites any existing fields in the hash. If key does not exist, a new key holding a hash is created.
            </summary>
            <remarks>http://redis.io/commands/hmset</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HashSetAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue,StackExchange.Redis.When,StackExchange.Redis.CommandFlags)">
            <summary>
            Sets field in the hash stored at key to value. If key does not exist, a new key holding a hash is created. If field already exists in the hash, it is overwritten.
            </summary>
            <returns>1 if field is a new field in the hash and value was set. 0 if field already exists in the hash and the value was updated.</returns>
            <remarks>http://redis.io/commands/hset</remarks>
            <remarks>http://redis.io/commands/hsetnx</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HashValuesAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns all values in the hash stored at key.
            </summary>
            <returns>list of values in the hash, or an empty list when key does not exist.</returns>
            <remarks>http://redis.io/commands/hvals</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HyperLogLogAddAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Adds the element to the HyperLogLog data structure stored at the variable name specified as first argument.
            </summary>
            <returns>true if at least 1 HyperLogLog internal register was altered. false otherwise.</returns>
            <remarks>http://redis.io/commands/pfadd</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HyperLogLogAddAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Adds all the element arguments to the HyperLogLog data structure stored at the variable name specified as first argument.
            </summary>
            <returns>true if at least 1 HyperLogLog internal register was altered. false otherwise.</returns>
            <remarks>http://redis.io/commands/pfadd</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HyperLogLogLengthAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the approximated cardinality computed by the HyperLogLog data structure stored at the specified variable, or 0 if the variable does not exist.
            </summary>
            <returns>The approximated number of unique elements observed via HyperLogLogAdd.</returns>
            <remarks>http://redis.io/commands/pfcount</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HyperLogLogLengthAsync(StackExchange.Redis.RedisKey[],StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the approximated cardinality of the union of the HyperLogLogs passed, by internally merging the HyperLogLogs stored at the provided keys into a temporary hyperLogLog, or 0 if the variable does not exist.
            </summary>
            <returns>The approximated number of unique elements observed via HyperLogLogAdd.</returns>
            <remarks>http://redis.io/commands/pfcount</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HyperLogLogMergeAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Merge multiple HyperLogLog values into an unique value that will approximate the cardinality of the union of the observed Sets of the source HyperLogLog structures.
            </summary>
            <remarks>http://redis.io/commands/pfmerge</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.HyperLogLogMergeAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey[],StackExchange.Redis.CommandFlags)">
            <summary>
            Merge multiple HyperLogLog values into an unique value that will approximate the cardinality of the union of the observed Sets of the source HyperLogLog structures.
            </summary>
            <remarks>http://redis.io/commands/pfmerge</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.IdentifyEndpointAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Inidicate exactly which redis server we are talking to
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.IsConnected(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Indicates whether the instance can communicate with the server (resolved
            using the supplied key and optional flags)
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.KeyDeleteAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Removes the specified key. A key is ignored if it does not exist.
            </summary>
            <returns>True if the key was removed.</returns>
            <remarks>http://redis.io/commands/del</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.KeyDeleteAsync(StackExchange.Redis.RedisKey[],StackExchange.Redis.CommandFlags)">
            <summary>
            Removes the specified keys. A key is ignored if it does not exist.
            </summary>
            <returns>The number of keys that were removed.</returns>
            <remarks>http://redis.io/commands/del</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.KeyDumpAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Serialize the value stored at key in a Redis-specific format and return it to the user. The returned value can be synthesized back into a Redis key using the RESTORE command.
            </summary>
            <returns>the serialized value.</returns>
            <remarks>http://redis.io/commands/dump</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.KeyExistsAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns if key exists.
            </summary>
            <returns>1 if the key exists. 0 if the key does not exist.</returns>
            <remarks>http://redis.io/commands/exists</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.KeyExpireAsync(StackExchange.Redis.RedisKey,System.Nullable{System.TimeSpan},StackExchange.Redis.CommandFlags)">
            <summary>
            Set a timeout on key. After the timeout has expired, the key will automatically be deleted. A key with an associated timeout is said to be volatile in Redis terminology.
            </summary>
            <remarks>If key is updated before the timeout has expired, then the timeout is removed as if the PERSIST command was invoked on key.
            For Redis versions &lt; 2.1.3, existing timeouts cannot be overwritten. So, if key already has an associated timeout, it will do nothing and return 0. Since Redis 2.1.3, you can update the timeout of a key. It is also possible to remove the timeout using the PERSIST command. See the page on key expiry for more information.</remarks>
            <returns>1 if the timeout was set. 0 if key does not exist or the timeout could not be set.</returns>
            <remarks>http://redis.io/commands/expire</remarks>
            <remarks>http://redis.io/commands/pexpire</remarks>
            <remarks>http://redis.io/commands/persist</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.KeyExpireAsync(StackExchange.Redis.RedisKey,System.Nullable{System.DateTime},StackExchange.Redis.CommandFlags)">
            <summary>
            Set a timeout on key. After the timeout has expired, the key will automatically be deleted. A key with an associated timeout is said to be volatile in Redis terminology.
            </summary>
            <remarks>If key is updated before the timeout has expired, then the timeout is removed as if the PERSIST command was invoked on key.
            For Redis versions &lt; 2.1.3, existing timeouts cannot be overwritten. So, if key already has an associated timeout, it will do nothing and return 0. Since Redis 2.1.3, you can update the timeout of a key. It is also possible to remove the timeout using the PERSIST command. See the page on key expiry for more information.</remarks>
            <returns>1 if the timeout was set. 0 if key does not exist or the timeout could not be set.</returns>
            <remarks>http://redis.io/commands/expireat</remarks>
            <remarks>http://redis.io/commands/pexpireat</remarks>
            <remarks>http://redis.io/commands/persist</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.KeyMigrateAsync(StackExchange.Redis.RedisKey,System.Net.EndPoint,System.Int32,System.Int32,StackExchange.Redis.MigrateOptions,StackExchange.Redis.CommandFlags)">
            <summary>
            Atomically transfer a key from a source Redis instance to a destination Redis instance. On success the key is deleted from the original instance by default, and is guaranteed to exist in the target instance.
            </summary>
            <remarks>http://redis.io/commands/MIGRATE</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.KeyMoveAsync(StackExchange.Redis.RedisKey,System.Int32,StackExchange.Redis.CommandFlags)">
            <summary>
            Move key from the currently selected database (see SELECT) to the specified destination database. When key already exists in the destination database, or it does not exist in the source database, it does nothing. It is possible to use MOVE as a locking primitive because of this.
            </summary>
            <returns>1 if key was moved; 0 if key was not moved.</returns>
            <remarks>http://redis.io/commands/move</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.KeyPersistAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>Remove the existing timeout on key, turning the key from volatile (a key with an expire set) to persistent (a key that will never expire as no timeout is associated).</summary>
            <returns>1 if the timeout was removed. 0 if key does not exist or does not have an associated timeout.</returns>
            <remarks>http://redis.io/commands/persist</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.KeyRandomAsync(StackExchange.Redis.CommandFlags)">
            <summary>
            Return a random key from the currently selected database.
            </summary>
            <returns>the random key, or nil when the database is empty.</returns>
            <remarks>http://redis.io/commands/randomkey</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.KeyRenameAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.When,StackExchange.Redis.CommandFlags)">
            <summary>
            Renames key to newkey. It returns an error when the source and destination names are the same, or when key does not exist. 
            </summary>
            <returns>http://redis.io/commands/rename</returns>
            <remarks>http://redis.io/commands/renamenx</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.KeyRestoreAsync(StackExchange.Redis.RedisKey,System.Byte[],System.Nullable{System.TimeSpan},StackExchange.Redis.CommandFlags)">
            <summary>
            Create a key associated with a value that is obtained by deserializing the provided serialized value (obtained via DUMP).
            If ttl is 0 the key is created without any expire, otherwise the specified expire time(in milliseconds) is set.
            </summary>
            <remarks>http://redis.io/commands/restore</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.KeyTimeToLiveAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the remaining time to live of a key that has a timeout.  This introspection capability allows a Redis client to check how many seconds a given key will continue to be part of the dataset.
            </summary>
            <returns>TTL, or nil when key does not exist or does not have a timeout.</returns>
            <remarks>http://redis.io/commands/ttl</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.KeyTypeAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the string representation of the type of the value stored at key. The different types that can be returned are: string, list, set, zset and hash.
            </summary>
            <returns>type of key, or none when key does not exist.</returns>
            <remarks>http://redis.io/commands/type</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.ListGetByIndexAsync(StackExchange.Redis.RedisKey,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the element at index index in the list stored at key. The index is zero-based, so 0 means the first element, 1 the second element and so on. Negative indices can be used to designate elements starting at the tail of the list. Here, -1 means the last element, -2 means the penultimate and so forth.
            </summary>
            <returns>the requested element, or nil when index is out of range.</returns>
            <remarks>http://redis.io/commands/lindex</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.ListInsertAfterAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Inserts value in the list stored at key either before or after the reference value pivot.
            When key does not exist, it is considered an empty list and no operation is performed.
            </summary>
            <returns>the length of the list after the insert operation, or -1 when the value pivot was not found.</returns>
            <remarks>http://redis.io/commands/linsert</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.ListInsertBeforeAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Inserts value in the list stored at key either before or after the reference value pivot.
            When key does not exist, it is considered an empty list and no operation is performed.
            </summary>
            <returns>the length of the list after the insert operation, or -1 when the value pivot was not found.</returns>
            <remarks>http://redis.io/commands/linsert</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.ListLeftPopAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Removes and returns the first element of the list stored at key.
            </summary>
            <returns>the value of the first element, or nil when key does not exist.</returns>
            <remarks>http://redis.io/commands/lpop</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.ListLeftPushAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.When,StackExchange.Redis.CommandFlags)">
            <summary>
            Insert the specified value at the head of the list stored at key. If key does not exist, it is created as empty list before performing the push operations.
            </summary>
            <returns>the length of the list after the push operations.</returns>
            <remarks>http://redis.io/commands/lpush</remarks>
            <remarks>http://redis.io/commands/lpushx</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.ListLeftPushAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Insert all the specified values at the head of the list stored at key. If key does not exist, it is created as empty list before performing the push operations.
            Elements are inserted one after the other to the head of the list, from the leftmost element to the rightmost element. So for instance the command LPUSH mylist a b c will result into a list containing c as first element, b as second element and a as third element.
            </summary>
            <returns>the length of the list after the push operations.</returns>
            <remarks>http://redis.io/commands/lpush</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.ListLengthAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the length of the list stored at key. If key does not exist, it is interpreted as an empty list and 0 is returned. 
            </summary>
            <returns>the length of the list at key.</returns>
            <remarks>http://redis.io/commands/llen</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.ListRangeAsync(StackExchange.Redis.RedisKey,System.Int64,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the specified elements of the list stored at key. The offsets start and stop are zero-based indexes, with 0 being the first element of the list (the head of the list), 1 being the next element and so on.
            These offsets can also be negative numbers indicating offsets starting at the end of the list.For example, -1 is the last element of the list, -2 the penultimate, and so on.
            Note that if you have a list of numbers from 0 to 100, LRANGE list 0 10 will return 11 elements, that is, the rightmost item is included. 
            </summary>
            <returns>list of elements in the specified range.</returns>
            <remarks>http://redis.io/commands/lrange</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.ListRemoveAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Removes the first count occurrences of elements equal to value from the list stored at key. The count argument influences the operation in the following ways:
            count &gt; 0: Remove elements equal to value moving from head to tail.
            count &lt; 0: Remove elements equal to value moving from tail to head.
            count = 0: Remove all elements equal to value.
            </summary>
            <returns>the number of removed elements.</returns>
            <remarks>http://redis.io/commands/lrem</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.ListRightPopAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Removes and returns the last element of the list stored at key.
            </summary>
            <remarks>http://redis.io/commands/rpop</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.ListRightPopLeftPushAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Atomically returns and removes the last element (tail) of the list stored at source, and pushes the element at the first element (head) of the list stored at destination.
            </summary>
            <returns>the element being popped and pushed.</returns>
            <remarks>http://redis.io/commands/rpoplpush</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.ListRightPushAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.When,StackExchange.Redis.CommandFlags)">
            <summary>
            Insert the specified value at the tail of the list stored at key. If key does not exist, it is created as empty list before performing the push operation.
            </summary>
            <returns>the length of the list after the push operation.</returns>
            <remarks>http://redis.io/commands/rpush</remarks>
            <remarks>http://redis.io/commands/rpushx</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.ListRightPushAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Insert all the specified values at the tail of the list stored at key. If key does not exist, it is created as empty list before performing the push operation. 
            Elements are inserted one after the other to the tail of the list, from the leftmost element to the rightmost element. So for instance the command RPUSH mylist a b c will result into a list containing a as first element, b as second element and c as third element.
            </summary>
            <returns>the length of the list after the push operation.</returns>
            <remarks>http://redis.io/commands/rpush</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.ListSetByIndexAsync(StackExchange.Redis.RedisKey,System.Int64,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Sets the list element at index to value. For more information on the index argument, see ListGetByIndex. An error is returned for out of range indexes.
            </summary>
            <remarks>http://redis.io/commands/lset</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.ListTrimAsync(StackExchange.Redis.RedisKey,System.Int64,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Trim an existing list so that it will contain only the specified range of elements specified. Both start and stop are zero-based indexes, where 0 is the first element of the list (the head), 1 the next element and so on.
            For example: LTRIM foobar 0 2 will modify the list stored at foobar so that only the first three elements of the list will remain.
            start and end can also be negative numbers indicating offsets from the end of the list, where -1 is the last element of the list, -2 the penultimate element and so on.
            </summary>
            <remarks>http://redis.io/commands/ltrim</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.LockExtendAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.TimeSpan,StackExchange.Redis.CommandFlags)">
            <summary>
            Extends a lock, if the token value is correct
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.LockQueryAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Queries the token held against a lock
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.LockReleaseAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Releases a lock, if the token value is correct
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.LockTakeAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.TimeSpan,StackExchange.Redis.CommandFlags)">
            <summary>
            Takes a lock (specifying a token value) if it is not already taken
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.PublishAsync(StackExchange.Redis.RedisChannel,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Posts a message to the given channel.
            </summary>
            <returns>the number of clients that received the message.</returns>
            <remarks>http://redis.io/commands/publish</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.ScriptEvaluateAsync(System.String,StackExchange.Redis.RedisKey[],StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Execute a Lua script against the server
            </summary>
            <remarks>http://redis.io/commands/eval, http://redis.io/commands/evalsha</remarks>
            <returns>A dynamic representation of the script's result</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.ScriptEvaluateAsync(System.Byte[],StackExchange.Redis.RedisKey[],StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Execute a Lua script against the server using just the SHA1 hash
            </summary>
            <remarks>http://redis.io/commands/evalsha</remarks>
            <returns>A dynamic representation of the script's result</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.ScriptEvaluateAsync(StackExchange.Redis.LuaScript,System.Object,StackExchange.Redis.CommandFlags)">
            <summary>
            Execute a lua script against the server, using previously prepared script.
            Named parameters, if any, are provided by the `parameters` object.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.ScriptEvaluateAsync(StackExchange.Redis.LoadedLuaScript,System.Object,StackExchange.Redis.CommandFlags)">
            <summary>
            Execute a lua script against the server, using previously prepared and loaded script.
            This method sends only the SHA1 hash of the lua script to Redis.
            Named parameters, if any, are provided by the `parameters` object.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SetAddAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Add the specified member to the set stored at key. Specified members that are already a member of this set are ignored. If key does not exist, a new set is created before adding the specified members.
            </summary>
            <returns>True if the specified member was not already present in the set, else False</returns>
            <remarks>http://redis.io/commands/sadd</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SetAddAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Add the specified members to the set stored at key. Specified members that are already a member of this set are ignored. If key does not exist, a new set is created before adding the specified members.
            </summary>
            <returns>the number of elements that were added to the set, not including all the elements already present into the set.</returns>
            <remarks>http://redis.io/commands/sadd</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SetCombineAndStoreAsync(StackExchange.Redis.SetOperation,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            This command is equal to SetCombine, but instead of returning the resulting set, it is stored in destination. If destination already exists, it is overwritten.
            </summary>
            <returns>the number of elements in the resulting set.</returns>
            <remarks>http://redis.io/commands/sunionstore</remarks>
            <remarks>http://redis.io/commands/sinterstore</remarks>
            <remarks>http://redis.io/commands/sdiffstore</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SetCombineAndStoreAsync(StackExchange.Redis.SetOperation,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey[],StackExchange.Redis.CommandFlags)">
            <summary>
            This command is equal to SetCombine, but instead of returning the resulting set, it is stored in destination. If destination already exists, it is overwritten.
            </summary>
            <returns>the number of elements in the resulting set.</returns>
            <remarks>http://redis.io/commands/sunionstore</remarks>
            <remarks>http://redis.io/commands/sinterstore</remarks>
            <remarks>http://redis.io/commands/sdiffstore</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SetCombineAsync(StackExchange.Redis.SetOperation,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the members of the set resulting from the specified operation against the given sets.
            </summary>
            <returns>list with members of the resulting set.</returns>
            <remarks>http://redis.io/commands/sunion</remarks>
            <remarks>http://redis.io/commands/sinter</remarks>
            <remarks>http://redis.io/commands/sdiff</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SetCombineAsync(StackExchange.Redis.SetOperation,StackExchange.Redis.RedisKey[],StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the members of the set resulting from the specified operation against the given sets.
            </summary>
            <returns>list with members of the resulting set.</returns>
            <remarks>http://redis.io/commands/sunion</remarks>
            <remarks>http://redis.io/commands/sinter</remarks>
            <remarks>http://redis.io/commands/sdiff</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SetContainsAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns if member is a member of the set stored at key.
            </summary>
            <returns>1 if the element is a member of the set. 0 if the element is not a member of the set, or if key does not exist.</returns>
            <remarks>http://redis.io/commands/sismember</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SetLengthAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the set cardinality (number of elements) of the set stored at key.
            </summary>
            <returns>the cardinality (number of elements) of the set, or 0 if key does not exist.</returns>
            <remarks>http://redis.io/commands/scard</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SetMembersAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns all the members of the set value stored at key.
            </summary>
            <returns>all elements of the set.</returns>
            <remarks>http://redis.io/commands/smembers</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SetMoveAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Move member from the set at source to the set at destination. This operation is atomic. In every given moment the element will appear to be a member of source or destination for other clients.
            When the specified element already exists in the destination set, it is only removed from the source set.
            </summary>
            <returns>1 if the element is moved. 0 if the element is not a member of source and no operation was performed.</returns>
            <remarks>http://redis.io/commands/smove</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SetPopAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Removes and returns a random element from the set value stored at key.
            </summary>
            <returns>the removed element, or nil when key does not exist.</returns>
            <remarks>http://redis.io/commands/spop</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SetRandomMemberAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Return a random element from the set value stored at key.
            </summary>
            <returns>the randomly selected element, or nil when key does not exist</returns>
            <remarks>http://redis.io/commands/srandmember</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SetRandomMembersAsync(StackExchange.Redis.RedisKey,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Return an array of count distinct elements if count is positive. If called with a negative count the behavior changes and the command is allowed to return the same element multiple times.
            In this case the numer of returned elements is the absolute value of the specified count.
            </summary>
            <returns>an array of elements, or an empty array when key does not exist</returns>
            <remarks>http://redis.io/commands/srandmember</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SetRemoveAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Remove the specified member from the set stored at key.  Specified members that are not a member of this set are ignored.
            </summary>
            <returns>True if the specified member was already present in the set, else False</returns>
            <remarks>http://redis.io/commands/srem</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SetRemoveAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Remove the specified members from the set stored at key. Specified members that are not a member of this set are ignored.
            </summary>
            <returns>the number of members that were removed from the set, not including non existing members.</returns>
            <remarks>http://redis.io/commands/srem</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortAndStoreAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,System.Int64,System.Int64,StackExchange.Redis.Order,StackExchange.Redis.SortType,StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Sorts a list, set or sorted set (numerically or alphabetically, ascending by default); By default, the elements themselves are compared, but the values can also be
            used to perform external key-lookups using the <c>by</c> parameter. By default, the elements themselves are returned, but external key-lookups (one or many) can
            be performed instead by specifying the <c>get</c> parameter (note that <c>#</c> specifies the element itself, when used in <c>get</c>).
            Referring to the <a href="http://redis.io/commands/sort">redis SORT documentation </a> for examples is recommended. When used in hashes, <c>by</c> and <c>get</c>
            can be used to specify fields using <c>-&gt;</c> notation (again, refer to redis documentation).
            </summary>
            <remarks>http://redis.io/commands/sort</remarks>
            <returns>Returns the number of elements stored in the new list</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortAsync(StackExchange.Redis.RedisKey,System.Int64,System.Int64,StackExchange.Redis.Order,StackExchange.Redis.SortType,StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Sorts a list, set or sorted set (numerically or alphabetically, ascending by default); By default, the elements themselves are compared, but the values can also be
            used to perform external key-lookups using the <c>by</c> parameter. By default, the elements themselves are returned, but external key-lookups (one or many) can
            be performed instead by specifying the <c>get</c> parameter (note that <c>#</c> specifies the element itself, when used in <c>get</c>).
            Referring to the <a href="http://redis.io/commands/sort">redis SORT documentation </a> for examples is recommended. When used in hashes, <c>by</c> and <c>get</c>
            can be used to specify fields using <c>-&gt;</c> notation (again, refer to redis documentation).
            </summary>
            <remarks>http://redis.io/commands/sort</remarks>
            <returns>Returns the sorted elements, or the external values if <c>get</c> is specified</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetAddAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Double,StackExchange.Redis.CommandFlags)">
            <summary>
            Adds the specified member with the specified score to the sorted set stored at key. If the specified member is already a member of the sorted set, the score is updated and the element reinserted at the right position to ensure the correct ordering.
            </summary>
            <returns>True if the value was added, False if it already existed (the score is still updated)</returns>
            <remarks>http://redis.io/commands/zadd</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetAddAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.SortedSetEntry[],StackExchange.Redis.CommandFlags)">
            <summary>
            Adds all the specified members with the specified scores to the sorted set stored at key. If a specified member is already a member of the sorted set, the score is updated and the element reinserted at the right position to ensure the correct ordering.
            </summary>
            <returns>The number of elements added to the sorted sets, not including elements already existing for which the score was updated.</returns>
            <remarks>http://redis.io/commands/zadd</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetCombineAndStoreAsync(StackExchange.Redis.SetOperation,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.Aggregate,StackExchange.Redis.CommandFlags)">
            <summary>
            Computes a set operation over two sorted sets, and stores the result in destination, optionally performing 
            a specific aggregation (defaults to sum)
            </summary>
            <remarks>http://redis.io/commands/zunionstore</remarks>
            <remarks>http://redis.io/commands/zinterstore</remarks>
            <returns>the number of elements in the resulting sorted set at destination</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetCombineAndStoreAsync(StackExchange.Redis.SetOperation,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey[],System.Double[],StackExchange.Redis.Aggregate,StackExchange.Redis.CommandFlags)">
            <summary>
            Computes a set operation over multiple sorted sets (optionally using per-set weights), and stores the result in destination, optionally performing 
            a specific aggregation (defaults to sum)
            </summary>
            <remarks>http://redis.io/commands/zunionstore</remarks>
            <remarks>http://redis.io/commands/zinterstore</remarks>
            <returns>the number of elements in the resulting sorted set at destination</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetDecrementAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Double,StackExchange.Redis.CommandFlags)">
            <summary>
            Decrements the score of member in the sorted set stored at key by decrement. If member does not exist in the sorted set, it is added with -decrement as its score (as if its previous score was 0.0).
            </summary>
            <returns>the new score of member</returns>
            <remarks>http://redis.io/commands/zincrby</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetIncrementAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Double,StackExchange.Redis.CommandFlags)">
            <summary>
            Increments the score of member in the sorted set stored at key by increment. If member does not exist in the sorted set, it is added with increment as its score (as if its previous score was 0.0).
            </summary>
            <returns>the new score of member</returns>
            <remarks>http://redis.io/commands/zincrby</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetLengthAsync(StackExchange.Redis.RedisKey,System.Double,System.Double,StackExchange.Redis.Exclude,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the sorted set cardinality (number of elements) of the sorted set stored at key.
            </summary>
            <returns>the cardinality (number of elements) of the sorted set, or 0 if key does not exist.</returns>
            <remarks>http://redis.io/commands/zcard</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetLengthByValueAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue,StackExchange.Redis.Exclude,StackExchange.Redis.CommandFlags)">
            <summary>
            When all the elements in a sorted set are inserted with the same score, in order to force lexicographical ordering, this command returns the number of elements in the sorted set at key with a value between min and max.
            </summary>
            <returns>the number of elements in the specified score range.</returns>
            <remarks>When all the elements in a sorted set are inserted with the same score, in order to force lexicographical ordering, this command returns all the elements in the sorted set at key with a value between min and max.</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetRangeByRankAsync(StackExchange.Redis.RedisKey,System.Int64,System.Int64,StackExchange.Redis.Order,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the specified range of elements in the sorted set stored at key. By default the elements are considered to be ordered from the lowest to the highest score. Lexicographical order is used for elements with equal score.
            Both start and stop are zero-based indexes, where 0 is the first element, 1 is the next element and so on. They can also be negative numbers indicating offsets from the end of the sorted set, with -1 being the last element of the sorted set, -2 the penultimate element and so on.
            </summary>
            <returns>list of elements in the specified range</returns>
            <remarks>http://redis.io/commands/zrange</remarks>
            <remarks>http://redis.io/commands/zrevrange</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetRangeByRankWithScoresAsync(StackExchange.Redis.RedisKey,System.Int64,System.Int64,StackExchange.Redis.Order,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the specified range of elements in the sorted set stored at key. By default the elements are considered to be ordered from the lowest to the highest score. Lexicographical order is used for elements with equal score.
            Both start and stop are zero-based indexes, where 0 is the first element, 1 is the next element and so on. They can also be negative numbers indicating offsets from the end of the sorted set, with -1 being the last element of the sorted set, -2 the penultimate element and so on.
            </summary>
            <returns>list of elements in the specified range</returns>
            <remarks>http://redis.io/commands/zrange</remarks>
            <remarks>http://redis.io/commands/zrevrange</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetRangeByScoreAsync(StackExchange.Redis.RedisKey,System.Double,System.Double,StackExchange.Redis.Exclude,StackExchange.Redis.Order,System.Int64,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the specified range of elements in the sorted set stored at key. By default the elements are considered to be ordered from the lowest to the highest score. Lexicographical order is used for elements with equal score.
            Start and stop are used to specify the min and max range for score values. Similar to other range methods the values are inclusive.
            </summary>
            <returns>list of elements in the specified score range</returns>
            <remarks>http://redis.io/commands/zrangebyscore</remarks>
            <remarks>http://redis.io/commands/zrevrangebyscore</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetRangeByScoreWithScoresAsync(StackExchange.Redis.RedisKey,System.Double,System.Double,StackExchange.Redis.Exclude,StackExchange.Redis.Order,System.Int64,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the specified range of elements in the sorted set stored at key. By default the elements are considered to be ordered from the lowest to the highest score. Lexicographical order is used for elements with equal score.
            Start and stop are used to specify the min and max range for score values. Similar to other range methods the values are inclusive.
            </summary>
            <returns>list of elements in the specified score range</returns>
            <remarks>http://redis.io/commands/zrangebyscore</remarks>
            <remarks>http://redis.io/commands/zrevrangebyscore</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetRangeByValueAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue,StackExchange.Redis.Exclude,System.Int64,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            When all the elements in a sorted set are inserted with the same score, in order to force lexicographical ordering, this command returns all the elements in the sorted set at key with a value between min and max.
            </summary>
            <remarks>http://redis.io/commands/zrangebylex</remarks>
            <returns>list of elements in the specified score range.</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetRankAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.Order,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the rank of member in the sorted set stored at key, by default with the scores ordered from low to high. The rank (or index) is 0-based, which means that the member with the lowest score has rank 0.
            </summary>
            <returns>If member exists in the sorted set, the rank of member; If member does not exist in the sorted set or key does not exist, null</returns>
            <remarks>http://redis.io/commands/zrank</remarks>
            <remarks>http://redis.io/commands/zrevrank</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetRemoveAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Removes the specified member from the sorted set stored at key. Non existing members are ignored.
            </summary>
            <returns>True if the member existed in the sorted set and was removed; False otherwise.</returns>
            <remarks>http://redis.io/commands/zrem</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetRemoveAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue[],StackExchange.Redis.CommandFlags)">
            <summary>
            Removes the specified members from the sorted set stored at key. Non existing members are ignored.
            </summary>
            <returns>The number of members removed from the sorted set, not including non existing members.</returns>
            <remarks>http://redis.io/commands/zrem</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetRemoveRangeByRankAsync(StackExchange.Redis.RedisKey,System.Int64,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Removes all elements in the sorted set stored at key with rank between start and stop. Both start and stop are 0 -based indexes with 0 being the element with the lowest score. These indexes can be negative numbers, where they indicate offsets starting at the element with the highest score. For example: -1 is the element with the highest score, -2 the element with the second highest score and so forth.
            </summary>
            <returns> the number of elements removed.</returns>
            <remarks>http://redis.io/commands/zremrangebyrank</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetRemoveRangeByScoreAsync(StackExchange.Redis.RedisKey,System.Double,System.Double,StackExchange.Redis.Exclude,StackExchange.Redis.CommandFlags)">
            <summary>
            Removes all elements in the sorted set stored at key with a score between min and max (inclusive by default).
            </summary>
            <returns> the number of elements removed.</returns>
            <remarks>http://redis.io/commands/zremrangebyscore</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetRemoveRangeByValueAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue,StackExchange.Redis.Exclude,StackExchange.Redis.CommandFlags)">
            <summary>
            When all the elements in a sorted set are inserted with the same score, in order to force lexicographical ordering, this command removes all elements in the sorted set stored at key between the lexicographical range specified by min and max.
            </summary>
            <remarks>http://redis.io/commands/zremrangebylex</remarks>
            <returns>the number of elements removed.</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.SortedSetScoreAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the score of member in the sorted set at key; If member does not exist in the sorted set, or key does not exist, nil is returned.
            </summary>
            <returns>the score of member</returns>
            <remarks>http://redis.io/commands/zscore</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringAppendAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            If key already exists and is a string, this command appends the value at the end of the string. If key does not exist it is created and set as an empty string,
            so APPEND will be similar to SET in this special case.
            </summary>
            <returns>the length of the string after the append operation.</returns>
            <remarks>http://redis.io/commands/append</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringBitCountAsync(StackExchange.Redis.RedisKey,System.Int64,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Count the number of set bits (population counting) in a string.
            By default all the bytes contained in the string are examined.It is possible to specify the counting operation only in an interval passing the additional arguments start and end.
            Like for the GETRANGE command start and end can contain negative values in order to index bytes starting from the end of the string, where -1 is the last byte, -2 is the penultimate, and so forth.
            </summary>
            <returns>The number of bits set to 1</returns>
            <remarks>http://redis.io/commands/bitcount</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringBitOperationAsync(StackExchange.Redis.Bitwise,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Perform a bitwise operation between multiple keys (containing string values) and store the result in the destination key.
            The BITOP command supports four bitwise operations; note that NOT is a unary operator: the second key should be omitted in this case
            and only the first key will be considered.
            The result of the operation is always stored at destkey.
            </summary>
            <returns>The size of the string stored in the destination key, that is equal to the size of the longest input string.</returns>
            <remarks>http://redis.io/commands/bitop</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringBitOperationAsync(StackExchange.Redis.Bitwise,StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey[],StackExchange.Redis.CommandFlags)">
            <summary>
            Perform a bitwise operation between multiple keys (containing string values) and store the result in the destination key.
            The BITOP command supports four bitwise operations; note that NOT is a unary operator.
            The result of the operation is always stored at destkey.
            </summary>
            <returns>The size of the string stored in the destination key, that is equal to the size of the longest input string.</returns>
            <remarks>http://redis.io/commands/bitop</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringBitPositionAsync(StackExchange.Redis.RedisKey,System.Boolean,System.Int64,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Return the position of the first bit set to 1 or 0 in a string.
            The position is returned thinking at the string as an array of bits from left to right where the first byte most significant bit is at position 0, the second byte most significant big is at position 8 and so forth.
            An start and end may be specified; these are in bytes, not bits; start and end can contain negative values in order to index bytes starting from the end of the string, where -1 is the last byte, -2 is the penultimate, and so forth.
            </summary>
            <returns>The command returns the position of the first bit set to 1 or 0 according to the request.
            If we look for set bits(the bit argument is 1) and the string is empty or composed of just zero bytes, -1 is returned.</returns>
            <remarks>http://redis.io/commands/bitpos</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringDecrementAsync(StackExchange.Redis.RedisKey,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Decrements the number stored at key by decrement. If the key does not exist, it is set to 0 before performing the operation. An error is returned if the key contains a value of the wrong type or contains a string that is not representable as integer. This operation is limited to 64 bit signed integers.
            </summary>
            <returns> the value of key after the increment</returns>
            <remarks>http://redis.io/commands/decrby</remarks>
            <remarks>http://redis.io/commands/decr</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringDecrementAsync(StackExchange.Redis.RedisKey,System.Double,StackExchange.Redis.CommandFlags)">
            <summary>
            Decrements the string representing a floating point number stored at key by the specified increment. If the key does not exist, it is set to 0 before performing the operation. The precision of the output is fixed at 17 digits after the decimal point regardless of the actual internal precision of the computation.
            </summary>
            <returns>the value of key after the increment</returns>
            <remarks>http://redis.io/commands/incrbyfloat</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringGetAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Get the value of key. If the key does not exist the special value nil is returned. An error is returned if the value stored at key is not a string, because GET only handles string values.
            </summary>
            <returns>the value of key, or nil when key does not exist.</returns>
            <remarks>http://redis.io/commands/get</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringGetAsync(StackExchange.Redis.RedisKey[],StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the values of all specified keys. For every key that does not hold a string value or does not exist, the special value nil is returned.
            </summary>
            <remarks>http://redis.io/commands/mget</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringGetBitAsync(StackExchange.Redis.RedisKey,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the bit value at offset in the string value stored at key.
            When offset is beyond the string length, the string is assumed to be a contiguous space with 0 bits.
            </summary>
            <returns>the bit value stored at offset.</returns>
            <remarks>http://redis.io/commands/getbit</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringGetRangeAsync(StackExchange.Redis.RedisKey,System.Int64,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the substring of the string value stored at key, determined by the offsets start and end (both are inclusive). Negative offsets can be used in order to provide an offset starting from the end of the string. So -1 means the last character, -2 the penultimate and so forth.
            </summary>
            <returns>the substring of the string value stored at key</returns>
            <remarks>http://redis.io/commands/getrange</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringGetSetAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Atomically sets key to value and returns the old value stored at key.
            </summary>
            <remarks>http://redis.io/commands/getset</remarks>
            <returns> the old value stored at key, or nil when key did not exist.</returns>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringGetWithExpiryAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Get the value of key. If the key does not exist the special value nil is returned. An error is returned if the value stored at key is not a string, because GET only handles string values.
            </summary>
            <returns>the value of key, or nil when key does not exist.</returns>
            <remarks>http://redis.io/commands/get</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringIncrementAsync(StackExchange.Redis.RedisKey,System.Int64,StackExchange.Redis.CommandFlags)">
            <summary>
            Increments the number stored at key by increment. If the key does not exist, it is set to 0 before performing the operation. An error is returned if the key contains a value of the wrong type or contains a string that is not representable as integer. This operation is limited to 64 bit signed integers.
            </summary>
            <returns>the value of key after the increment</returns>
            <remarks>http://redis.io/commands/incrby</remarks>
            <remarks>http://redis.io/commands/incr</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringIncrementAsync(StackExchange.Redis.RedisKey,System.Double,StackExchange.Redis.CommandFlags)">
            <summary>
            Increment the string representing a floating point number stored at key by the specified increment. If the key does not exist, it is set to 0 before performing the operation. The precision of the output is fixed at 17 digits after the decimal point regardless of the actual internal precision of the computation.
            </summary>
            <returns>the value of key after the increment</returns>
            <remarks>http://redis.io/commands/incrbyfloat</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringLengthAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the length of the string value stored at key.
            </summary>
            <returns>the length of the string at key, or 0 when key does not exist.</returns>
            <remarks>http://redis.io/commands/strlen</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringSetAsync(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue,System.Nullable{System.TimeSpan},StackExchange.Redis.When,StackExchange.Redis.CommandFlags)">
            <summary>
            Set key to hold the string value. If key already holds a value, it is overwritten, regardless of its type.
            </summary>
            <remarks>http://redis.io/commands/set</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringSetAsync(System.Collections.Generic.KeyValuePair{StackExchange.Redis.RedisKey,StackExchange.Redis.RedisValue}[],StackExchange.Redis.When,StackExchange.Redis.CommandFlags)">
            <summary>
            Sets the given keys to their respective values. If "not exists" is specified, this will not perform any operation at all even if just a single key already exists.
            </summary>
            <returns>True if the keys were set, else False</returns>
            <remarks>http://redis.io/commands/mset</remarks>
            <remarks>http://redis.io/commands/msetnx</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringSetBitAsync(StackExchange.Redis.RedisKey,System.Int64,System.Boolean,StackExchange.Redis.CommandFlags)">
            <summary>
            Sets or clears the bit at offset in the string value stored at key.
            The bit is either set or cleared depending on value, which can be either 0 or 1. When key does not exist, a new string value is created.The string is grown to make sure it can hold a bit at offset.
            </summary>
            <returns>the original bit value stored at offset.</returns>
            <remarks>http://redis.io/commands/setbit</remarks>
        </member>
        <member name="M:StackExchange.Redis.IDatabaseAsync.StringSetRangeAsync(StackExchange.Redis.RedisKey,System.Int64,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Overwrites part of the string stored at key, starting at the specified offset, for the entire length of value. If the offset is larger than the current length of the string at key, the string is padded with zero-bytes to make offset fit. Non-existing keys are considered as empty strings, so this command will make sure it holds a string large enough to be able to set value at offset.
            </summary>
            <returns>the length of the string after it was modified by the command.</returns>
            <remarks>http://redis.io/commands/setrange</remarks>
        </member>
        <member name="T:StackExchange.Redis.RedisValueWithExpiry">
            <summary>
            Describes a value/expiry pair
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisValueWithExpiry.Expiry">
            <summary>
            The expiry of this record
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisValueWithExpiry.Value">
            <summary>
            The value of this record
            </summary>
        </member>
        <member name="T:StackExchange.Redis.InternalErrorEventArgs">
            <summary>
            Describes internal errors (mainly intended for debugging)
            </summary>
        </member>
        <member name="P:StackExchange.Redis.InternalErrorEventArgs.ConnectionType">
            <summary>
            Gets the connection-type of the failing connection
            </summary>
        </member>
        <member name="P:StackExchange.Redis.InternalErrorEventArgs.EndPoint">
            <summary>
            Gets the failing server-endpoint (this can be null)
            </summary>
        </member>
        <member name="P:StackExchange.Redis.InternalErrorEventArgs.Exception">
            <summary>
            Gets the exception if available (this can be null)
            </summary>
        </member>
        <member name="P:StackExchange.Redis.InternalErrorEventArgs.Origin">
            <summary>
            The underlying origin of the error
            </summary>
        </member>
        <member name="T:StackExchange.Redis.InternalRegexCompiledOption">
            <summary>
            Credits to Sam Harwell https://github.com/dotnet/corefx/issues/340#issuecomment-120749951
            </summary>
        </member>
        <member name="P:StackExchange.Redis.InternalRegexCompiledOption.Default">
            <summary>
            Gets the default <see cref="T:System.Text.RegularExpressions.RegexOptions"/> to use.
            <see cref="F:System.Text.RegularExpressions.RegexOptions.Compiled"/> option isn't available yet for dnxcore50.
            This returns <see cref="F:System.Text.RegularExpressions.RegexOptions.Compiled"/> if it is supported; 
            <see cref="F:System.Text.RegularExpressions.RegexOptions.None"/> otherwise.
            </summary>
        </member>
        <member name="T:StackExchange.Redis.RetransmissionReasonType">
            <summary>
            If an IProfiledCommand is a retransmission of a previous command, this enum
            is used to indicate what prompted the retransmission.
            
            This can be used to distinguish between transient causes (moving hashslots, joining nodes, etc.)
            and incorrect routing.
            </summary>
        </member>
        <member name="F:StackExchange.Redis.RetransmissionReasonType.None">
            <summary>
            No stated reason
            </summary>
        </member>
        <member name="F:StackExchange.Redis.RetransmissionReasonType.Ask">
            <summary>
            Issued to investigate which node owns a key
            </summary>
        </member>
        <member name="F:StackExchange.Redis.RetransmissionReasonType.Moved">
            <summary>
            A node has indicated that it does *not* own the given key
            </summary>
        </member>
        <member name="T:StackExchange.Redis.IProfiledCommand">
            <summary>
            A profiled command against a redis instance.
            
            TimeSpans returned by this interface use a high precision timer if possible.
            DateTimes returned by this interface are no more precise than DateTime.UtcNow.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IProfiledCommand.EndPoint">
            <summary>
            The endpoint this command was sent to.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IProfiledCommand.Db">
            <summary>
            The Db this command was sent to.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IProfiledCommand.Command">
            <summary>
            The name of this command.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IProfiledCommand.Flags">
            <summary>
            The CommandFlags the command was submitted with.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IProfiledCommand.CommandCreated">
            <summary>
            When this command was *created*, will be approximately
            when the paired method of StackExchange.Redis was called but
            before that method returned.
            
            Note that the resolution of the returned DateTime is limited by DateTime.UtcNow.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IProfiledCommand.CreationToEnqueued">
            <summary>
            How long this command waited to be added to the queue of pending
            redis commands.  A large TimeSpan indicates serious contention for
            the pending queue.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IProfiledCommand.EnqueuedToSending">
            <summary>
            How long this command spent in the pending queue before being sent to redis.
            A large TimeSpan can indicate a large number of pending events, large pending events,
            or network issues.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IProfiledCommand.SentToResponse">
            <summary>
            How long before Redis responded to this command and it's response could be handled after it was sent.
            A large TimeSpan can indicate a large response body, an overtaxed redis instance, or network issues.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IProfiledCommand.ResponseToCompletion">
            <summary>
            How long between Redis responding to this command and awaiting consumers being notified.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IProfiledCommand.ElapsedTime">
            <summary>
            How long it took this redis command to be processed, from creation to deserializing the final resposne.
            
            Note that this TimeSpan *does not* include time spent awaiting a Task in consumer code.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IProfiledCommand.RetransmissionOf">
            <summary>
            If a command has to be resent due to an ASK or MOVED response from redis (in a cluster configuration),
            the second sending of the command will have this property set to the original IProfiledCommand.
            
            This can only be set if redis is configured as a cluster.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IProfiledCommand.RetransmissionReason">
            <summary>
            If RetransmissionOf is not null, this property will be set to either Ask or Moved to indicate
            what sort of response triggered the retransmission.
            
            This can be useful for determining the root cause of extra commands.
            </summary>
        </member>
        <member name="T:StackExchange.Redis.IProfiler">
            <summary>
            Interface for profiling individual commands against an Redis ConnectionMulitplexer.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IProfiler.GetContext">
            <summary>
            Called to provide a context object.
            
            This method is called before the method which triggers work against redis (such as StringSet(Async)) returns,
            and will always be called on the same thread as that method.
            
            Note that GetContext() may be called even if ConnectionMultiplexer.BeginProfiling() has not been called.
            You may return `null` to prevent any tracking of commands.
            </summary>
        </member>
        <member name="T:StackExchange.Redis.IRedis">
            <summary>
            Common operations available to all redis connections
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IRedis.Ping(StackExchange.Redis.CommandFlags)">
            <summary>
            This command is often used to test if a connection is still alive, or to measure latency.
            </summary>
            <returns>The observed latency.</returns>
            <remarks>http://redis.io/commands/ping</remarks>
        </member>
        <member name="T:StackExchange.Redis.IScanningCursor">
            <summary>
            Represents a resumable, cursor-based scanning operation
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IScanningCursor.Cursor">
            <summary>
            Returns the cursor that represents the *active* page of results (not the pending/next page of results as returned by SCAN/HSCAN/ZSCAN/SSCAN)
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IScanningCursor.PageSize">
            <summary>
            The page size of the current operation
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IScanningCursor.PageOffset">
            <summary>
            The offset into the current page
            </summary>
        </member>
        <member name="T:StackExchange.Redis.IRedisAsync">
            <summary>
            Common operations available to all redis connections
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IRedisAsync.Multiplexer">
            <summary>
            Gets the multiplexer that created this instance
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IRedisAsync.PingAsync(StackExchange.Redis.CommandFlags)">
            <summary>
            This command is often used to test if a connection is still alive, or to measure latency.
            </summary>
            <returns>The observed latency.</returns>
            <remarks>http://redis.io/commands/ping</remarks>
        </member>
        <member name="M:StackExchange.Redis.IRedisAsync.TryWait(System.Threading.Tasks.Task)">
            <summary>
            Wait for a given asynchronous operation to complete (or timeout), reporting which
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IRedisAsync.Wait(System.Threading.Tasks.Task)">
            <summary>
            Wait for a given asynchronous operation to complete (or timeout)
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IRedisAsync.Wait``1(System.Threading.Tasks.Task{``0})">
            <summary>
            Wait for a given asynchronous operation to complete (or timeout)
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IRedisAsync.WaitAll(System.Threading.Tasks.Task[])">
            <summary>
            Wait for the given asynchronous operations to complete (or timeout)
            </summary>
        </member>
        <member name="T:StackExchange.Redis.IServer">
            <summary>
            Provides configuration controls of a redis server
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IServer.ClusterConfiguration">
            <summary>
            Gets the cluster configuration associated with this server, if known
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IServer.EndPoint">
            <summary>
            Gets the address of the connected server
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IServer.Features">
            <summary>
            Gets the features available to the connected server
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IServer.IsConnected">
            <summary>
            Gets whether the connection to the server is active and usable
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IServer.IsSlave">
            <summary>
            Gets whether the connected server is a replica / slave
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IServer.AllowSlaveWrites">
            <summary>
            Explicitly opt in for slave writes on writable slaves
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IServer.ServerType">
            <summary>
            Gets the operating mode of the connected server
            </summary>
        </member>
        <member name="P:StackExchange.Redis.IServer.Version">
            <summary>
            Gets the version of the connected server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IServer.ClientKill(System.Net.EndPoint,StackExchange.Redis.CommandFlags)">
            <summary>
            The CLIENT KILL command closes a given client connection identified by ip:port.
            The ip:port should match a line returned by the CLIENT LIST command.
            Due to the single-treaded nature of Redis, it is not possible to kill a client connection while it is executing a command.From the client point of view, the connection can never be closed in the middle of the execution of a command.However, the client will notice the connection has been closed only when the next command is sent (and results in network error).
            </summary>
            <remarks>http://redis.io/commands/client-kill</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.ClientKillAsync(System.Net.EndPoint,StackExchange.Redis.CommandFlags)">
            <summary>
            The CLIENT KILL command closes a given client connection identified by ip:port.
            The ip:port should match a line returned by the CLIENT LIST command.
            Due to the single-treaded nature of Redis, it is not possible to kill a client connection while it is executing a command.From the client point of view, the connection can never be closed in the middle of the execution of a command.However, the client will notice the connection has been closed only when the next command is sent (and results in network error).
            </summary>
            <remarks>http://redis.io/commands/client-kill</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.ClientKill(System.Nullable{System.Int64},System.Nullable{StackExchange.Redis.ClientType},System.Net.EndPoint,System.Boolean,StackExchange.Redis.CommandFlags)">
            <summary>
            The CLIENT KILL command closes multiple connections that match the specified filters
            </summary>
            <returns>the number of clients killed.</returns>
            <remarks>http://redis.io/commands/client-kill</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.ClientKillAsync(System.Nullable{System.Int64},System.Nullable{StackExchange.Redis.ClientType},System.Net.EndPoint,System.Boolean,StackExchange.Redis.CommandFlags)">
            <summary>
            The CLIENT KILL command closes multiple connections that match the specified filters
            </summary>
            <returns>the number of clients killed.</returns>
            <remarks>http://redis.io/commands/client-kill</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.ClientList(StackExchange.Redis.CommandFlags)">
            <summary>
            The CLIENT LIST command returns information and statistics about the client connections server in a mostly human readable format.
            </summary>
            <remarks>http://redis.io/commands/client-list</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.ClientListAsync(StackExchange.Redis.CommandFlags)">
            <summary>
            The CLIENT LIST command returns information and statistics about the client connections server in a mostly human readable format.
            </summary>
            <remarks>http://redis.io/commands/client-list</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.ClusterNodes(StackExchange.Redis.CommandFlags)">
            <summary>
            Obtains the current CLUSTER NODES output from a cluster server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IServer.ClusterNodesAsync(StackExchange.Redis.CommandFlags)">
            <summary>
            Obtains the current CLUSTER NODES output from a cluster server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IServer.ClusterNodesRaw(StackExchange.Redis.CommandFlags)">
            <summary>
            Obtains the current raw CLUSTER NODES output from a cluster server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IServer.ClusterNodesRawAsync(StackExchange.Redis.CommandFlags)">
            <summary>
            Obtains the current raw CLUSTER NODES output from a cluster server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IServer.ConfigGet(StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Get all configuration parameters matching the specified pattern.
            </summary>
            <returns>All matching configuration parameters.</returns>
            <remarks>http://redis.io/commands/config-get</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.ConfigGetAsync(StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Get all configuration parameters matching the specified pattern.
            </summary>
            <returns>All matching configuration parameters.</returns>
            <remarks>http://redis.io/commands/config-get</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.ConfigResetStatistics(StackExchange.Redis.CommandFlags)">
            <summary>
            Resets the statistics reported by Redis using the INFO command.
            </summary>
            <remarks>http://redis.io/commands/config-resetstat</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.ConfigResetStatisticsAsync(StackExchange.Redis.CommandFlags)">
            <summary>
            Resets the statistics reported by Redis using the INFO command.
            </summary>
            <remarks>http://redis.io/commands/config-resetstat</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.ConfigRewrite(StackExchange.Redis.CommandFlags)">
            <summary>
            The CONFIG REWRITE command rewrites the redis.conf file the server was started with, applying the minimal changes needed to make it reflecting the configuration currently used by the server, that may be different compared to the original one because of the use of the CONFIG SET command.
            </summary>
            <remarks>http://redis.io/commands/config-rewrite</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.ConfigRewriteAsync(StackExchange.Redis.CommandFlags)">
            <summary>
            The CONFIG REWRITE command rewrites the redis.conf file the server was started with, applying the minimal changes needed to make it reflecting the configuration currently used by the server, that may be different compared to the original one because of the use of the CONFIG SET command.
            </summary>
            <remarks>http://redis.io/commands/config-rewrite</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.ConfigSet(StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            The CONFIG SET command is used in order to reconfigure the server at runtime without the need to restart Redis. You can change both trivial parameters or switch from one to another persistence option using this command.
            </summary>
            <remarks>http://redis.io/commands/config-set</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.ConfigSetAsync(StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            The CONFIG SET command is used in order to reconfigure the server at runtime without the need to restart Redis. You can change both trivial parameters or switch from one to another persistence option using this command.
            </summary>
            <remarks>http://redis.io/commands/config-set</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.DatabaseSize(System.Int32,StackExchange.Redis.CommandFlags)">
            <summary>
            Return the number of keys in the database.
            </summary>
            <remarks>http://redis.io/commands/dbsize</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.DatabaseSizeAsync(System.Int32,StackExchange.Redis.CommandFlags)">
            <summary>
            Return the number of keys in the database.
            </summary>
            <remarks>http://redis.io/commands/dbsize</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.FlushAllDatabases(StackExchange.Redis.CommandFlags)">
            <summary>
            Delete all the keys of all databases on the server.
            </summary>
            <remarks>http://redis.io/commands/flushall</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.FlushAllDatabasesAsync(StackExchange.Redis.CommandFlags)">
            <summary>
            Delete all the keys of all databases on the server.
            </summary>
            <remarks>http://redis.io/commands/flushall</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.FlushDatabase(System.Int32,StackExchange.Redis.CommandFlags)">
            <summary>
            Delete all the keys of the database.
            </summary>
            <remarks>http://redis.io/commands/flushdb</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.FlushDatabaseAsync(System.Int32,StackExchange.Redis.CommandFlags)">
            <summary>
            Delete all the keys of the database.
            </summary>
            <remarks>http://redis.io/commands/flushdb</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.GetCounters">
            <summary>
            Get summary statistics associates with this server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IServer.Info(StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            The INFO command returns information and statistics about the server in a format that is simple to parse by computers and easy to read by humans.
            </summary>
            <remarks>http://redis.io/commands/info</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.InfoAsync(StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            The INFO command returns information and statistics about the server in a format that is simple to parse by computers and easy to read by humans.
            </summary>
            <remarks>http://redis.io/commands/info</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.InfoRaw(StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            The INFO command returns information and statistics about the server in a format that is simple to parse by computers and easy to read by humans.
            </summary>
            <remarks>http://redis.io/commands/info</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.InfoRawAsync(StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            The INFO command returns information and statistics about the server in a format that is simple to parse by computers and easy to read by humans.
            </summary>
            <remarks>http://redis.io/commands/info</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.Keys(System.Int32,StackExchange.Redis.RedisValue,System.Int32,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns all keys matching pattern; the KEYS or SCAN commands will be used based on the server capabilities.
            </summary>
            <remarks>Warning: consider KEYS as a command that should only be used in production environments with extreme care.</remarks>
            <remarks>http://redis.io/commands/keys</remarks>
            <remarks>http://redis.io/commands/scan</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.Keys(System.Int32,StackExchange.Redis.RedisValue,System.Int32,System.Int64,System.Int32,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns all keys matching pattern; the KEYS or SCAN commands will be used based on the server capabilities; note: to resume an iteration via <i>cursor</i>, cast the original enumerable or enumerator to <i>IScanningCursor</i>.
            </summary>
            <remarks>Warning: consider KEYS as a command that should only be used in production environments with extreme care.</remarks>
            <remarks>http://redis.io/commands/keys</remarks>
            <remarks>http://redis.io/commands/scan</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.LastSave(StackExchange.Redis.CommandFlags)">
            <summary>
            Return the time of the last DB save executed with success. A client may check if a BGSAVE command succeeded reading the LASTSAVE value, then issuing a BGSAVE command and checking at regular intervals every N seconds if LASTSAVE changed.
            </summary>
            <remarks>http://redis.io/commands/lastsave</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.LastSaveAsync(StackExchange.Redis.CommandFlags)">
            <summary>
            Return the time of the last DB save executed with success. A client may check if a BGSAVE command succeeded reading the LASTSAVE value, then issuing a BGSAVE command and checking at regular intervals every N seconds if LASTSAVE changed.
            </summary>
            <remarks>http://redis.io/commands/lastsave</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.MakeMaster(StackExchange.Redis.ReplicationChangeOptions,System.IO.TextWriter)">
            <summary>
            Promote the selected node to be master
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IServer.Save(StackExchange.Redis.SaveType,StackExchange.Redis.CommandFlags)">
            <summary>
            Explicitly request the database to persist the current state to disk
            </summary>
            <remarks>http://redis.io/commands/bgrewriteaof</remarks>
            <remarks>http://redis.io/commands/bgsave</remarks>
            <remarks>http://redis.io/commands/save</remarks>
            <remarks>http://redis.io/topics/persistence</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SaveAsync(StackExchange.Redis.SaveType,StackExchange.Redis.CommandFlags)">
            <summary>
            Explicitly request the database to persist the current state to disk
            </summary>
            <remarks>http://redis.io/commands/bgrewriteaof</remarks>
            <remarks>http://redis.io/commands/bgsave</remarks>
            <remarks>http://redis.io/commands/save</remarks>
            <remarks>http://redis.io/topics/persistence</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.ScriptExists(System.String,StackExchange.Redis.CommandFlags)">
            <summary>
            Inidicates whether the specified script is defined on the server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IServer.ScriptExists(System.Byte[],StackExchange.Redis.CommandFlags)">
            <summary>
            Inidicates whether the specified script hash is defined on the server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IServer.ScriptExistsAsync(System.String,StackExchange.Redis.CommandFlags)">
            <summary>
            Inidicates whether the specified script is defined on the server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IServer.ScriptExistsAsync(System.Byte[],StackExchange.Redis.CommandFlags)">
            <summary>
            Inidicates whether the specified script hash is defined on the server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IServer.ScriptFlush(StackExchange.Redis.CommandFlags)">
            <summary>
            Removes all cached scripts on this server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IServer.ScriptFlushAsync(StackExchange.Redis.CommandFlags)">
            <summary>
            Removes all cached scripts on this server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IServer.ScriptLoad(System.String,StackExchange.Redis.CommandFlags)">
            <summary>
            Explicitly defines a script on the server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IServer.ScriptLoad(StackExchange.Redis.LuaScript,StackExchange.Redis.CommandFlags)">
            <summary>
            Explicitly defines a script on the server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IServer.ScriptLoadAsync(System.String,StackExchange.Redis.CommandFlags)">
            <summary>
            Explicitly defines a script on the server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IServer.ScriptLoadAsync(StackExchange.Redis.LuaScript,StackExchange.Redis.CommandFlags)">
            <summary>
            Explicitly defines a script on the server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.IServer.Shutdown(StackExchange.Redis.ShutdownMode,StackExchange.Redis.CommandFlags)">
            <summary>Asks the redis server to shutdown, killing all connections. Please FULLY read the notes on the SHUTDOWN command.</summary>
            <remarks>http://redis.io/commands/shutdown</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SlaveOf(System.Net.EndPoint,StackExchange.Redis.CommandFlags)">
            <summary>
            The SLAVEOF command can change the replication settings of a slave on the fly. If a Redis server is already acting as slave, specifying a null master will turn off the replication, turning the Redis server into a MASTER. Specifying a non-null master will make the server a slave of another server listening at the specified hostname and port.
            </summary>
            <remarks>http://redis.io/commands/slaveof</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SlaveOfAsync(System.Net.EndPoint,StackExchange.Redis.CommandFlags)">
            <summary>
            The SLAVEOF command can change the replication settings of a slave on the fly. If a Redis server is already acting as slave, specifying a null master will turn off the replication, turning the Redis server into a MASTER. Specifying a non-null master will make the server a slave of another server listening at the specified hostname and port.
            </summary>
            <remarks>http://redis.io/commands/slaveof</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SlowlogGet(System.Int32,StackExchange.Redis.CommandFlags)">
            <summary>
            To read the slow log the SLOWLOG GET command is used, that returns every entry in the slow log. It is possible to return only the N most recent entries passing an additional argument to the command (for instance SLOWLOG GET 10).
            </summary>
            <remarks>http://redis.io/commands/slowlog</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SlowlogGetAsync(System.Int32,StackExchange.Redis.CommandFlags)">
            <summary>
            To read the slow log the SLOWLOG GET command is used, that returns every entry in the slow log. It is possible to return only the N most recent entries passing an additional argument to the command (for instance SLOWLOG GET 10).
            </summary>
            <remarks>http://redis.io/commands/slowlog</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SlowlogReset(StackExchange.Redis.CommandFlags)">
            <summary>
            You can reset the slow log using the SLOWLOG RESET command. Once deleted the information is lost forever.
            </summary>
            <remarks>http://redis.io/commands/slowlog</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SlowlogResetAsync(StackExchange.Redis.CommandFlags)">
            <summary>
            You can reset the slow log using the SLOWLOG RESET command. Once deleted the information is lost forever.
            </summary>
            <remarks>http://redis.io/commands/slowlog</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SubscriptionChannels(StackExchange.Redis.RedisChannel,StackExchange.Redis.CommandFlags)">
            <summary>
            Lists the currently active channels. An active channel is a Pub/Sub channel with one ore more subscribers (not including clients subscribed to patterns).
            </summary>
            <returns> a list of active channels, optionally matching the specified pattern.</returns>
            <remarks>http://redis.io/commands/pubsub</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SubscriptionChannelsAsync(StackExchange.Redis.RedisChannel,StackExchange.Redis.CommandFlags)">
            <summary>
            Lists the currently active channels. An active channel is a Pub/Sub channel with one ore more subscribers (not including clients subscribed to patterns).
            </summary>
            <returns> a list of active channels, optionally matching the specified pattern.</returns>
            <remarks>http://redis.io/commands/pubsub</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SubscriptionPatternCount(StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the number of subscriptions to patterns (that are performed using the PSUBSCRIBE command). Note that this is not just the count of clients subscribed to patterns but the total number of patterns all the clients are subscribed to.
            </summary>
            <returns>the number of patterns all the clients are subscribed to.</returns>
            <remarks>http://redis.io/commands/pubsub</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SubscriptionPatternCountAsync(StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the number of subscriptions to patterns (that are performed using the PSUBSCRIBE command). Note that this is not just the count of clients subscribed to patterns but the total number of patterns all the clients are subscribed to.
            </summary>
            <returns>the number of patterns all the clients are subscribed to.</returns>
            <remarks>http://redis.io/commands/pubsub</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SubscriptionSubscriberCount(StackExchange.Redis.RedisChannel,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the number of subscribers (not counting clients subscribed to patterns) for the specified channel.
            </summary>
            <remarks>http://redis.io/commands/pubsub</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SubscriptionSubscriberCountAsync(StackExchange.Redis.RedisChannel,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the number of subscribers (not counting clients subscribed to patterns) for the specified channel.
            </summary>
            <remarks>http://redis.io/commands/pubsub</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.Time(StackExchange.Redis.CommandFlags)">
            <summary>
            The TIME command returns the current server time.
            </summary>
            <returns>The server's current time.</returns>
            <remarks>http://redis.io/commands/time</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.TimeAsync(StackExchange.Redis.CommandFlags)">
            <summary>
            The TIME command returns the current server time.
            </summary>
            <returns>The server's current time.</returns>
            <remarks>http://redis.io/commands/time</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SentinelGetMasterAddressByName(System.String,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the ip and port number of the master with that name. 
            If a failover is in progress or terminated successfully for this master it returns the address and port of the promoted slave.
            </summary>
            <param name="serviceName">the sentinel service name</param>
            <param name="flags"></param>
            <returns>the master ip and port</returns>
            <remarks>http://redis.io/topics/sentinel</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SentinelGetMasterAddressByNameAsync(System.String,StackExchange.Redis.CommandFlags)">
            <summary>
            Returns the ip and port number of the master with that name. 
            If a failover is in progress or terminated successfully for this master it returns the address and port of the promoted slave.
            </summary>
            <param name="serviceName">the sentinel service name</param>
            <param name="flags"></param>
            <returns>the master ip and port</returns>
            <remarks>http://redis.io/topics/sentinel</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SentinelMaster(System.String,StackExchange.Redis.CommandFlags)">
            <summary>
            Show the state and info of the specified master.
            </summary>
            <param name="serviceName">the sentinel service name</param>
            <param name="flags"></param>
            <returns>the master state as KeyValuePairs</returns>
            <remarks>http://redis.io/topics/sentinel</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SentinelMasterAsync(System.String,StackExchange.Redis.CommandFlags)">
            <summary>
            Force a failover as if the master was not reachable, and without asking for agreement to other Sentinels 
            (however a new version of the configuration will be published so that the other Sentinels will update their configurations).
            </summary>
            <param name="serviceName">the sentinel service name</param>
            <param name="flags"></param>
            <returns>the master state as KeyValuePairs</returns>
            <remarks>http://redis.io/topics/sentinel</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SentinelMasters(StackExchange.Redis.CommandFlags)">
            <summary>
            Show a list of monitored masters and their state.
            </summary>
            <param name="flags"></param>
            <returns>an array of master state KeyValuePair arrays</returns>
            <remarks>http://redis.io/topics/sentinel</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SentinelMastersAsync(StackExchange.Redis.CommandFlags)">
            <summary>
            Show a list of monitored masters and their state.
            </summary>
            <param name="flags"></param>
            <returns>an array of master state KeyValuePair arrays</returns>
            <remarks>http://redis.io/topics/sentinel</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SentinelSlaves(System.String,StackExchange.Redis.CommandFlags)">
            <summary>
            Show a list of slaves for this master, and their state.
            </summary>
            <param name="serviceName">the sentinel service name</param>
            <param name="flags"></param>
            <returns>an array of slave state KeyValuePair arrays</returns>
            <remarks>http://redis.io/topics/sentinel</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SentinelSlavesAsync(System.String,StackExchange.Redis.CommandFlags)">
            <summary>
            Show a list of slaves for this master, and their state.
            </summary>
            <param name="serviceName">the sentinel service name</param>
            <param name="flags"></param>
            <returns>an array of slave state KeyValuePair arrays</returns>
            <remarks>http://redis.io/topics/sentinel</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SentinelFailover(System.String,StackExchange.Redis.CommandFlags)">
            <summary>
            Force a failover as if the master was not reachable, and without asking for agreement to other Sentinels 
            (however a new version of the configuration will be published so that the other Sentinels will update their configurations).
            </summary>
            <param name="serviceName">the sentinel service name</param>
            <param name="flags"></param>
            <remarks>http://redis.io/topics/sentinel</remarks>
        </member>
        <member name="M:StackExchange.Redis.IServer.SentinelFailoverAsync(System.String,StackExchange.Redis.CommandFlags)">
            <summary>
            Force a failover as if the master was not reachable, and without asking for agreement to other Sentinels 
            (however a new version of the configuration will be published so that the other Sentinels will update their configurations).
            </summary>
            <param name="serviceName">the sentinel service name</param>
            <param name="flags"></param>
            <remarks>http://redis.io/topics/sentinel</remarks>
        </member>
        <member name="T:StackExchange.Redis.ISubscriber">
            <summary>
            A redis connection used as the subscriber in a pub/sub scenario
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ISubscriber.IdentifyEndpoint(StackExchange.Redis.RedisChannel,StackExchange.Redis.CommandFlags)">
            <summary>
            Inidicate exactly which redis server we are talking to
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ISubscriber.IdentifyEndpointAsync(StackExchange.Redis.RedisChannel,StackExchange.Redis.CommandFlags)">
            <summary>
            Inidicate exactly which redis server we are talking to
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ISubscriber.IsConnected(StackExchange.Redis.RedisChannel)">
            <summary>
            Indicates whether the instance can communicate with the server;
            if a channel is specified, the existing subscription map is queried to
            resolve the server responsible for that subscription - otherwise the
            server is chosen aribtraily from the masters.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ISubscriber.Publish(StackExchange.Redis.RedisChannel,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Posts a message to the given channel.
            </summary>
            <returns>the number of clients that received the message.</returns>
            <remarks>http://redis.io/commands/publish</remarks>
        </member>
        <member name="M:StackExchange.Redis.ISubscriber.PublishAsync(StackExchange.Redis.RedisChannel,StackExchange.Redis.RedisValue,StackExchange.Redis.CommandFlags)">
            <summary>
            Posts a message to the given channel.
            </summary>
            <returns>the number of clients that received the message.</returns>
            <remarks>http://redis.io/commands/publish</remarks>
        </member>
        <member name="M:StackExchange.Redis.ISubscriber.Subscribe(StackExchange.Redis.RedisChannel,System.Action{StackExchange.Redis.RedisChannel,StackExchange.Redis.RedisValue},StackExchange.Redis.CommandFlags)">
            <summary>
            Subscribe to perform some operation when a change to the preferred/active node is broadcast.
            </summary>
            <remarks>http://redis.io/commands/subscribe</remarks>
            <remarks>http://redis.io/commands/psubscribe</remarks>
        </member>
        <member name="M:StackExchange.Redis.ISubscriber.SubscribeAsync(StackExchange.Redis.RedisChannel,System.Action{StackExchange.Redis.RedisChannel,StackExchange.Redis.RedisValue},StackExchange.Redis.CommandFlags)">
            <summary>
            Subscribe to perform some operation when a change to the preferred/active node is broadcast.
            </summary>
            <remarks>http://redis.io/commands/subscribe</remarks>
            <remarks>http://redis.io/commands/psubscribe</remarks>
        </member>
        <member name="M:StackExchange.Redis.ISubscriber.SubscribedEndpoint(StackExchange.Redis.RedisChannel)">
            <summary>
            Inidicate to which redis server we are actively subscribed for a given channel; returns null if
            the channel is not actively subscribed
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ISubscriber.Unsubscribe(StackExchange.Redis.RedisChannel,System.Action{StackExchange.Redis.RedisChannel,StackExchange.Redis.RedisValue},StackExchange.Redis.CommandFlags)">
            <summary>
            Unsubscribe from a specified message channel; note; if no handler is specified, the subscription is cancelled regardless
            of the subscribers; if a handler is specified, the subscription is only cancelled if this handler is the 
            last handler remaining against the channel
            </summary>
            <remarks>http://redis.io/commands/unsubscribe</remarks>
            <remarks>http://redis.io/commands/punsubscribe</remarks>
        </member>
        <member name="M:StackExchange.Redis.ISubscriber.UnsubscribeAll(StackExchange.Redis.CommandFlags)">
            <summary>
            Unsubscribe all subscriptions on this instance
            </summary>
            <remarks>http://redis.io/commands/unsubscribe</remarks>
            <remarks>http://redis.io/commands/punsubscribe</remarks>
        </member>
        <member name="M:StackExchange.Redis.ISubscriber.UnsubscribeAllAsync(StackExchange.Redis.CommandFlags)">
            <summary>
            Unsubscribe all subscriptions on this instance
            </summary>
            <remarks>http://redis.io/commands/unsubscribe</remarks>
            <remarks>http://redis.io/commands/punsubscribe</remarks>
        </member>
        <member name="M:StackExchange.Redis.ISubscriber.UnsubscribeAsync(StackExchange.Redis.RedisChannel,System.Action{StackExchange.Redis.RedisChannel,StackExchange.Redis.RedisValue},StackExchange.Redis.CommandFlags)">
            <summary>
            Unsubscribe from a specified message channel; note; if no handler is specified, the subscription is cancelled regardless
            of the subscribers; if a handler is specified, the subscription is only cancelled if this handler is the 
            last handler remaining against the channel
            </summary>
            <remarks>http://redis.io/commands/unsubscribe</remarks>
            <remarks>http://redis.io/commands/punsubscribe</remarks>
        </member>
        <member name="T:StackExchange.Redis.ITransaction">
            <summary>
            Represents a group of operations that will be sent to the server as a single unit,
            and processed on the server as a single unit. Transactions can also include constraints
            (implemented via WATCH), but note that constraint checking involves will (very briefly)
            block the connection, since the transaction cannot be correctly committed (EXEC),
            aborted (DISCARD) or not applied in the first place (UNWATCH) until the responses from
            the constraint checks have arrived.
            </summary>
            <remarks>http://redis.io/topics/transactions</remarks>
            <remarks>Note that on a cluster, it may be required that all keys involved in the transaction
            (including constraints) are in the same hash-slot</remarks>
        </member>
        <member name="M:StackExchange.Redis.ITransaction.AddCondition(StackExchange.Redis.Condition)">
            <summary>
            Adds a precondition for this transaction
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ITransaction.Execute(StackExchange.Redis.CommandFlags)">
            <summary>
            Execute the batch operation, sending all queued commands to the server.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ITransaction.ExecuteAsync(StackExchange.Redis.CommandFlags)">
            <summary>
            Execute the batch operation, sending all queued commands to the server.
            </summary>
        </member>
        <member name="T:StackExchange.Redis.KeyspaceIsolation.DatabaseExtensions">
            <summary>
                Provides the <see cref="M:StackExchange.Redis.KeyspaceIsolation.DatabaseExtensions.WithKeyPrefix(StackExchange.Redis.IDatabase,StackExchange.Redis.RedisKey)"/> extension method to <see cref="T:StackExchange.Redis.IDatabase"/>.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.KeyspaceIsolation.DatabaseExtensions.WithKeyPrefix(StackExchange.Redis.IDatabase,StackExchange.Redis.RedisKey)">
            <summary>
                Creates a new <see cref="T:StackExchange.Redis.IDatabase"/> instance that provides an isolated key space
                of the specified underyling database instance.
            </summary>
            <param name="database">
                The underlying database instance that the returned instance shall use.
            </param>
            <param name="keyPrefix">
                The prefix that defines a key space isolation for the returned database instance.
            </param>
            <returns>
                A new <see cref="T:StackExchange.Redis.IDatabase"/> instance that invokes the specified underlying
                <paramref name="database"/> but prepends the specified <paramref name="keyPrefix"/>
                to all key paramters and thus forms a logical key space isolation.
            </returns>
            <remarks>
            <para>
                The following methods are not supported in a key space isolated database and
                will throw an <see cref="T:System.NotSupportedException"/> when invoked:
            </para>    
            <list type="bullet">
                <item><see cref="M:StackExchange.Redis.IDatabaseAsync.KeyRandomAsync(StackExchange.Redis.CommandFlags)"/></item>
                <item><see cref="M:StackExchange.Redis.IDatabase.KeyRandom(StackExchange.Redis.CommandFlags)"/></item>
            </list>
            <para>
                Please notice that keys passed to a script are prefixed (as normal) but care must
                be taken when a script returns the name of a key as that will (currently) not be
                "unprefixed".
            </para>
            </remarks>
        </member>
        <member name="T:StackExchange.Redis.LuaScript">
            <summary>
            Represents a Lua script that can be executed on Redis.
            
            Unlike normal Redis Lua scripts, LuaScript can have named parameters (prefixed by a @).
            Public fields and properties of the passed in object are treated as parameters.
            
            Parameters of type RedisKey are sent to Redis as KEY (http://redis.io/commands/eval) in addition to arguments, 
            so as to play nicely with Redis Cluster.
            
            All members of this class are thread safe.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.LuaScript.OriginalScript">
            <summary>
            The original Lua script that was used to create this.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.LuaScript.ExecutableScript">
            <summary>
            The Lua script that will actually be sent to Redis for execution.
            
            All @-prefixed parameter names have been replaced at this point.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.LuaScript.Finalize">
            <summary>
            Finalizer, used to prompt cleanups of the script cache when
            a LuaScript reference goes out of scope.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.LuaScript.PurgeCache">
            <summary>
            Invalidates the internal cache of LuaScript objects.
            Existing LuaScripts will continue to work, but future calls to LuaScript.Prepare
            return a new LuaScript instance.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.LuaScript.GetCachedScriptCount">
            <summary>
            Returns the number of cached LuaScripts.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.LuaScript.Prepare(System.String)">
            <summary>
            Prepares a Lua script with named parameters to be run against any Redis instance.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.LuaScript.Evaluate(StackExchange.Redis.IDatabase,System.Object,System.Nullable{StackExchange.Redis.RedisKey},StackExchange.Redis.CommandFlags)">
            <summary>
            Evaluates this LuaScript against the given database, extracting parameters from the passed in object if any.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.LuaScript.EvaluateAsync(StackExchange.Redis.IDatabaseAsync,System.Object,System.Nullable{StackExchange.Redis.RedisKey},StackExchange.Redis.CommandFlags)">
            <summary>
            Evaluates this LuaScript against the given database, extracting parameters from the passed in object if any.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.LuaScript.Load(StackExchange.Redis.IServer,StackExchange.Redis.CommandFlags)">
            <summary>
            Loads this LuaScript into the given IServer so it can be run with it's SHA1 hash, instead of
            passing the full script on each Evaluate or EvaluateAsync call.
            
            Note: the FireAndForget command flag cannot be set
            </summary>
        </member>
        <member name="M:StackExchange.Redis.LuaScript.LoadAsync(StackExchange.Redis.IServer,StackExchange.Redis.CommandFlags)">
            <summary>
            Loads this LuaScript into the given IServer so it can be run with it's SHA1 hash, instead of
            passing the full script on each Evaluate or EvaluateAsync call.
            
            Note: the FireAndForget command flag cannot be set
            </summary>
        </member>
        <member name="T:StackExchange.Redis.LoadedLuaScript">
            <summary>
            Represents a Lua script that can be executed on Redis.
            
            Unlike LuaScript, LoadedLuaScript sends the hash of it's ExecutableScript to Redis rather than pass
            the whole script on each call.  This requires that the script be loaded into Redis before it is used.
            
            To create a LoadedLuaScript first create a LuaScript via LuaScript.Prepare(string), then
            call Load(IServer, CommandFlags) on the returned LuaScript.
            
            Unlike normal Redis Lua scripts, LoadedLuaScript can have named parameters (prefixed by a @).
            Public fields and properties of the passed in object are treated as parameters.
            
            Parameters of type RedisKey are sent to Redis as KEY (http://redis.io/commands/eval) in addition to arguments, 
            so as to play nicely with Redis Cluster.
            
            All members of this class are thread safe.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.LoadedLuaScript.OriginalScript">
            <summary>
            The original script that was used to create this LoadedLuaScript.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.LoadedLuaScript.ExecutableScript">
            <summary>
            The script that will actually be sent to Redis for execution.
            </summary>
        </member>
        <member name="P:StackExchange.Redis.LoadedLuaScript.Hash">
            <summary>
            The SHA1 hash of ExecutableScript.
            
            This is sent to Redis instead of ExecutableScript during Evaluate and EvaluateAsync calls.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.LoadedLuaScript.Evaluate(StackExchange.Redis.IDatabase,System.Object,System.Nullable{StackExchange.Redis.RedisKey},StackExchange.Redis.CommandFlags)">
            <summary>
            Evaluates this LoadedLuaScript against the given database, extracting parameters for the passed in object if any.
            
            This method sends the SHA1 hash of the ExecutableScript instead of the script itself.  If the script has not
            been loaded into the passed Redis instance it will fail.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.LoadedLuaScript.EvaluateAsync(StackExchange.Redis.IDatabaseAsync,System.Object,System.Nullable{StackExchange.Redis.RedisKey},StackExchange.Redis.CommandFlags)">
            <summary>
            Evaluates this LoadedLuaScript against the given database, extracting parameters for the passed in object if any.
            
            This method sends the SHA1 hash of the ExecutableScript instead of the script itself.  If the script has not
            been loaded into the passed Redis instance it will fail.
            </summary>
        </member>
        <member name="T:StackExchange.Redis.RedisCommandException">
            <summary>
            Indicates that a command was illegal and was not sent to the server
            </summary>
        </member>
        <member name="T:StackExchange.Redis.RedisConnectionException">
            <summary>
            Indicates a connection fault when communicating with redis
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisConnectionException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Serialization implementation; not intended for general usage
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisConnectionException.FailureType">
            <summary>
            The type of connection failure
            </summary>
        </member>
        <member name="T:StackExchange.Redis.RedisException">
            <summary>
            Indicates an issue communicating with redis
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Deserialization constructor; not intended for general usage
            </summary>
        </member>
        <member name="T:StackExchange.Redis.RedisServerException">
            <summary>
            Indicates an exception raised by a redis server
            </summary>
        </member>
        <member name="P:StackExchange.Redis.Message.IsAdmin">
            <summary>
            Things with the potential to cause harm, or to reveal configuration information
            </summary>
        </member>
        <member name="M:StackExchange.Redis.Message.SetInternalCall">
            <summary>
            This does a few important things:
            1: it suppresses error events for commands that the user isn't interested in
               (i.e. "why does my standalone server keep saying ERR unknown command 'cluster' ?")
            2: it allows the initial PING and GET (during connect) to get queued rather
               than be rejected as no-server-available (note that this doesn't apply to
               handshake messages, as they bypass the queue completely)
            3: it disables non-pref logging, as it is usually server-targeted
            </summary>
        </member>
        <member name="M:StackExchange.Redis.MessageQueue.DequeueUnsentPing(System.Int32@)">
            <summary>
            Checks both high-pri and regular queues to see if the next item is a PING, and if so: dequeues it and returns it
            </summary>
        </member>
        <member name="T:StackExchange.Redis.MigrateOptions">
            <summary>
            Additional options for the MIGRATE command
            </summary>
        </member>
        <member name="F:StackExchange.Redis.MigrateOptions.None">
            <summary>
            No options specified
            </summary>
        </member>
        <member name="F:StackExchange.Redis.MigrateOptions.Copy">
            <summary>
            Do not remove the key from the local instance.
            </summary>
        </member>
        <member name="F:StackExchange.Redis.MigrateOptions.Replace">
            <summary>
            Replace existing key on the remote instance.
            </summary>
        </member>
        <member name="T:StackExchange.Redis.Order">
            <summary>
            The direction in which to sequence elements
            </summary>
        </member>
        <member name="F:StackExchange.Redis.Order.Ascending">
            <summary>
            Ordered from low values to high values
            </summary>
        </member>
        <member name="F:StackExchange.Redis.Order.Descending">
            <summary>
            Ordered from high values to low values
            </summary>
        </member>
        <member name="M:StackExchange.Redis.PhysicalBridge.WriteMessageDirect(StackExchange.Redis.PhysicalConnection,StackExchange.Redis.Message)">
            <summary>
            This writes a message **directly** to the output stream; note
            that this ignores the queue, so should only be used *either*
            from the regular dequeue loop, *or* from the "I've just
            connected" handshake (when there is no dequeue loop) - otherwise,
            you can pretty much assume you're going to destroy the stream
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ProfileContextTracker">
            <summary>
            Big ol' wrapper around most of the profiling storage logic, 'cause it got too big to just live in ConnectionMultiplexer.
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ProfileContextTracker.ProfileContextCell">
            <summary>
            Necessary, because WeakReference can't be readily comparable (since the reference is... weak).
            
            This lets us detect leaks* with some reasonable confidence, and cleanup periodically.
            
            Some calisthenics are done to avoid allocating WeakReferences for no reason, as often
            we're just looking up ProfileStorage.
            
            * Somebody starts profiling, but for whatever reason never *stops* with a context object
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ProfileContextTracker.ProfileContextCell.ToStoreUnder(System.Object)">
            <summary>
            Suitable for use as a key into something.
            
            This instance **WILL NOT** keep forObj alive, so it can
            be copied out of the calling method's scope.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ProfileContextTracker.ProfileContextCell.ToLookupBy(System.Object)">
            <summary>
            Only suitable for looking up.
            
            This instance **ABSOLUTELY WILL** keep forObj alive, so this
            had better not be copied into anything outside the scope of the
            calling method.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ProfileContextTracker.TryCreate(System.Object)">
            <summary>
            Registers the passed context with a collection that can be retried with subsequent calls to TryGetValue.
            
            Returns false if the passed context object is already registered.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ProfileContextTracker.TryGetValue(System.Object,StackExchange.Redis.ConcurrentProfileStorageCollection@)">
            <summary>
            Returns true and sets val to the tracking collection associated with the given context if the context
            was registered with TryCreate.
            
            Otherwise returns false and sets val to null.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ProfileContextTracker.TryRemove(System.Object,StackExchange.Redis.ProfiledCommandEnumerable@)">
            <summary>
            Removes a context, setting all commands to a (non-thread safe) enumerable of
            all the commands attached to that context.
            
            If the context was never registered, will return false and set commands to null.
            
            Subsequent calls to TryRemove with the same context will return false unless it is
            re-registered with TryCreate.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ProfileContextTracker.TryCleanup">
            <summary>
            If enough time has passed (1 minute) since the last call, this does walk of all contexts
            and removes those that the GC has collected.
            </summary>
        </member>
        <member name="T:StackExchange.Redis.RedisChannel">
            <summary>
            Represents a pub/sub channel name
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.#ctor(System.Byte[],StackExchange.Redis.RedisChannel.PatternMode)">
            <summary>
            Create a new redis channel from a buffer, explicitly controlling the pattern mode
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.#ctor(System.String,StackExchange.Redis.RedisChannel.PatternMode)">
            <summary>
            Create a new redis channel from a string, explicitly controlling the pattern mode
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisChannel.IsNullOrEmpty">
            <summary>
            Indicates whether the channel-name is either null or a zero-length value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.op_Inequality(StackExchange.Redis.RedisChannel,StackExchange.Redis.RedisChannel)">
            <summary>
            Indicate whether two channel names are not equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.op_Inequality(System.String,StackExchange.Redis.RedisChannel)">
            <summary>
            Indicate whether two channel names are not equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.op_Inequality(System.Byte[],StackExchange.Redis.RedisChannel)">
            <summary>
            Indicate whether two channel names are not equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.op_Inequality(StackExchange.Redis.RedisChannel,System.String)">
            <summary>
            Indicate whether two channel names are not equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.op_Inequality(StackExchange.Redis.RedisChannel,System.Byte[])">
            <summary>
            Indicate whether two channel names are not equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.op_Equality(StackExchange.Redis.RedisChannel,StackExchange.Redis.RedisChannel)">
            <summary>
            Indicate whether two channel names are equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.op_Equality(System.String,StackExchange.Redis.RedisChannel)">
            <summary>
            Indicate whether two channel names are equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.op_Equality(System.Byte[],StackExchange.Redis.RedisChannel)">
            <summary>
            Indicate whether two channel names are equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.op_Equality(StackExchange.Redis.RedisChannel,System.String)">
            <summary>
            Indicate whether two channel names are equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.op_Equality(StackExchange.Redis.RedisChannel,System.Byte[])">
            <summary>
            Indicate whether two channel names are equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.Equals(System.Object)">
            <summary>
            See Object.Equals
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.Equals(StackExchange.Redis.RedisChannel)">
            <summary>
            Indicate whether two channel names are equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.GetHashCode">
            <summary>
            See Object.GetHashCode
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.ToString">
            <summary>
            Obtains a string representation of the channel name
            </summary>
        </member>
        <member name="T:StackExchange.Redis.RedisChannel.PatternMode">
            <summary>
            The matching pattern for this channel
            </summary>
        </member>
        <member name="F:StackExchange.Redis.RedisChannel.PatternMode.Auto">
            <summary>
            Will be treated as a pattern if it includes *
            </summary>
        </member>
        <member name="F:StackExchange.Redis.RedisChannel.PatternMode.Literal">
            <summary>
            Never a pattern
            </summary>
        </member>
        <member name="F:StackExchange.Redis.RedisChannel.PatternMode.Pattern">
            <summary>
            Always a pattern
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.op_Implicit(System.String)~StackExchange.Redis.RedisChannel">
            <summary>
            Create a channel name from a String
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.op_Implicit(System.Byte[])~StackExchange.Redis.RedisChannel">
            <summary>
            Create a channel name from a Byte[]
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.op_Implicit(StackExchange.Redis.RedisChannel)~System.Byte[]">
            <summary>
            Obtain the channel name as a Byte[]
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisChannel.op_Implicit(StackExchange.Redis.RedisChannel)~System.String">
            <summary>
            Obtain the channel name as a String
            </summary>
        </member>
        <member name="T:StackExchange.Redis.RedisErrorEventArgs">
            <summary>
            Notification of errors from the redis server
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisErrorEventArgs.EndPoint">
            <summary>
            The origin of the message
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisErrorEventArgs.Message">
            <summary>
            The message from the server
            </summary>
        </member>
        <member name="T:StackExchange.Redis.RedisFeatures">
            <summary>
            Provides basic information about the features available on a particular version of Redis
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisFeatures.#ctor(System.Version)">
            <summary>
            Create a new RedisFeatures instance for the given version
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.BitwiseOperations">
            <summary>
            Does BITOP / BITCOUNT exist?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.ClientName">
            <summary>
            Is CLIENT SETNAME available?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.ExecAbort">
            <summary>
            Does EXEC support EXECABORT if there are errors?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.ExpireOverwrite">
            <summary>
            Can EXPIRE be used to set expiration on a key that is already volatile (i.e. has an expiration)?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.HashVaradicDelete">
            <summary>
            Does HDEL support varadic usage?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.IncrementFloat">
            <summary>
            Does INCRBYFLOAT / HINCRBYFLOAT exist?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.InfoSections">
            <summary>
            Does INFO support sections?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.ListInsert">
            <summary>
            Is LINSERT available?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.MillisecondExpiry">
            <summary>
            Indicates whether PEXPIRE and PTTL are supported
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.MultipleRandom">
            <summary>
            Does SRANDMEMBER support "count"?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.Persist">
            <summary>
            Is the PERSIST operation supported?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.PushIfNotExists">
            <summary>
            Is RPUSHX and LPUSHX available?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.Scan">
            <summary>
            Are cursor-based scans available?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.Scripting">
            <summary>
            Does EVAL / EVALSHA / etc exist?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.SetConditional">
            <summary>
            Does SET have the EX|PX|NX|XX extensions?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.SetVaradicAddRemove">
            <summary>
            Does SADD support varadic usage?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.StringLength">
            <summary>
            Is STRLEN available?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.StringSetRange">
            <summary>
            Is SETRANGE available?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.Time">
            <summary>
            Does TIME exist?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.ScriptingDatabaseSafe">
            <summary>
            Are Lua changes to the calling database transparent to the calling client?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.HyperLogLogCountSlaveSafe">
            <summary>
            Is PFCOUNT supported on slaves?
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisFeatures.Version">
            <summary>
            The Redis version of the server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisFeatures.ToString">
            <summary>
            Create a string representation of the available features
            </summary>
        </member>
        <member name="T:StackExchange.Redis.RedisKey">
            <summary>
            Represents a key that can be stored in redis
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.op_Inequality(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey)">
            <summary>
            Indicate whether two keys are not equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.op_Inequality(System.String,StackExchange.Redis.RedisKey)">
            <summary>
            Indicate whether two keys are not equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.op_Inequality(System.Byte[],StackExchange.Redis.RedisKey)">
            <summary>
            Indicate whether two keys are not equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.op_Inequality(StackExchange.Redis.RedisKey,System.String)">
            <summary>
            Indicate whether two keys are not equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.op_Inequality(StackExchange.Redis.RedisKey,System.Byte[])">
            <summary>
            Indicate whether two keys are not equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.op_Equality(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey)">
            <summary>
            Indicate whether two keys are equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.op_Equality(System.String,StackExchange.Redis.RedisKey)">
            <summary>
            Indicate whether two keys are equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.op_Equality(System.Byte[],StackExchange.Redis.RedisKey)">
            <summary>
            Indicate whether two keys are equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.op_Equality(StackExchange.Redis.RedisKey,System.String)">
            <summary>
            Indicate whether two keys are equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.op_Equality(StackExchange.Redis.RedisKey,System.Byte[])">
            <summary>
            Indicate whether two keys are equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.Equals(System.Object)">
            <summary>
            See Object.Equals
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.Equals(StackExchange.Redis.RedisKey)">
            <summary>
            Indicate whether two keys are equal
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.GetHashCode">
            <summary>
            See Object.GetHashCode
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.ToString">
            <summary>
            Obtains a string representation of the key
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.op_Implicit(System.String)~StackExchange.Redis.RedisKey">
            <summary>
            Create a key from a String
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.op_Implicit(System.Byte[])~StackExchange.Redis.RedisKey">
            <summary>
            Create a key from a Byte[]
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.op_Implicit(StackExchange.Redis.RedisKey)~System.Byte[]">
            <summary>
            Obtain the key as a Byte[]
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.op_Implicit(StackExchange.Redis.RedisKey)~System.String">
            <summary>
            Obtain the key as a String
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.op_Addition(StackExchange.Redis.RedisKey,StackExchange.Redis.RedisKey)">
            <summary>
            Concatenate two keys
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.Prepend(StackExchange.Redis.RedisKey)">
            <summary>
            Prepends p to this RedisKey, returning a new RedisKey.
            
            Avoids some allocations if possible, repeated Prepend/Appends make
            it less possible.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisKey.Append(StackExchange.Redis.RedisKey)">
            <summary>
            Appends p to this RedisKey, returning a new RedisKey.
            
            Avoids some allocations if possible, repeated Prepend/Appends make
            it less possible.
            </summary>
        </member>
        <member name="T:StackExchange.Redis.RedisResult">
            <summary>
            Represents a general-purpose result from redis, that may be cast into various anticipated types
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisResult.IsNull">
            <summary>
            Indicates whether this result was a null result
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~System.String">
            <summary>
            Interprets the result as a String
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~System.Byte[]">
            <summary>
            Interprets the result as a Byte[]
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~System.Double">
            <summary>
            Interprets the result as a Double
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~System.Int64">
            <summary>
            Interprets the result as an Int64
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~System.Int32">
            <summary>
            Interprets the result as an Int32
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~System.Boolean">
            <summary>
            Interprets the result as a Boolean
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~StackExchange.Redis.RedisValue">
            <summary>
            Interprets the result as a RedisValue
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~StackExchange.Redis.RedisKey">
            <summary>
            Interprets the result as a RedisKey
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~System.Nullable{System.Double}">
            <summary>
            Interprets the result as a Nullable Double
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~System.Nullable{System.Int64}">
            <summary>
            Interprets the result as a Nullable Int64
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~System.Nullable{System.Int32}">
            <summary>
            Interprets the result as a Nullable Int32
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~System.Nullable{System.Boolean}">
            <summary>
            Interprets the result as a Nullable Boolean
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~System.String[]">
            <summary>
            Interprets the result as an array of String
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~System.Byte[][]">
            <summary>
            Interprets the result as an array of Byte[]
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~System.Double[]">
            <summary>
            Interprets the result as an array of Double
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~System.Int64[]">
            <summary>
            Interprets the result as an array of Int64
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~System.Int32[]">
            <summary>
            Interprets the result as an array of Int32
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~System.Boolean[]">
            <summary>
            Interprets the result as an array of Boolean
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~StackExchange.Redis.RedisValue[]">
            <summary>
            Interprets the result as an array of RedisValue
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~StackExchange.Redis.RedisKey[]">
            <summary>
            Interprets the result as an array of RedisKey
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisResult.op_Explicit(StackExchange.Redis.RedisResult)~StackExchange.Redis.RedisResult[]">
            <summary>
            Interprets the result as an array of RedisResult
            </summary>
        </member>
        <member name="T:StackExchange.Redis.RedisType">
            <summary>
            The intrinsinc data-types supported by redis
            </summary>
            <remarks>http://redis.io/topics/data-types</remarks>
        </member>
        <member name="F:StackExchange.Redis.RedisType.None">
            <summary>
            The specified key does not exist
            </summary>
        </member>
        <member name="F:StackExchange.Redis.RedisType.String">
            <summary>
            Strings are the most basic kind of Redis value. Redis Strings are binary safe, this means that a Redis string can contain any kind of data, for instance a JPEG image or a serialized Ruby object.
            A String value can be at max 512 Megabytes in length.
            </summary>
            <remarks>http://redis.io/commands#string</remarks>
        </member>
        <member name="F:StackExchange.Redis.RedisType.List">
            <summary>
            Redis Lists are simply lists of strings, sorted by insertion order. It is possible to add elements to a Redis List pushing new elements on the head (on the left) or on the tail (on the right) of the list.
            </summary>
            <remarks>http://redis.io/commands#list</remarks>
        </member>
        <member name="F:StackExchange.Redis.RedisType.Set">
            <summary>
            Redis Sets are an unordered collection of Strings. It is possible to add, remove, and test for existence of members in O(1) (constant time regardless of the number of elements contained inside the Set).
            Redis Sets have the desirable property of not allowing repeated members. Adding the same element multiple times will result in a set having a single copy of this element. Practically speaking this means that adding a member does not require a check if exists then add operation.
            </summary>
            <remarks>http://redis.io/commands#set</remarks>
        </member>
        <member name="F:StackExchange.Redis.RedisType.SortedSet">
            <summary>
            Redis Sorted Sets are, similarly to Redis Sets, non repeating collections of Strings. The difference is that every member of a Sorted Set is associated with score, that is used in order to take the sorted set ordered, from the smallest to the greatest score. While members are unique, scores may be repeated.
            </summary>
            <remarks>http://redis.io/commands#sorted_set</remarks>
        </member>
        <member name="F:StackExchange.Redis.RedisType.Hash">
            <summary>
            Redis Hashes are maps between string fields and string values, so they are the perfect data type to represent objects (eg: A User with a number of fields like name, surname, age, and so forth)
            </summary>
            <remarks>http://redis.io/commands#hash</remarks>
        </member>
        <member name="F:StackExchange.Redis.RedisType.Unknown">
            <summary>
            The data-type was not recognised by the client library
            </summary>
        </member>
        <member name="T:StackExchange.Redis.RedisValue">
            <summary>
            Represents values that can be stored in redis
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisValue.EmptyString">
            <summary>
            Represents the string <c>""</c>
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisValue.Null">
            <summary>
            A null value
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisValue.IsInteger">
            <summary>
            Indicates whether the value is a primitive integer
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisValue.IsNull">
            <summary>
            Indicates whether the value should be considered a null value
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisValue.IsNullOrEmpty">
            <summary>
            Indicates whether the value is either null or a zero-length value
            </summary>
        </member>
        <member name="P:StackExchange.Redis.RedisValue.HasValue">
            <summary>
            Indicates whether the value is greater than zero-length
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Inequality(StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue)">
            <summary>
            Indicates whether two RedisValue values are equivalent
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Equality(StackExchange.Redis.RedisValue,StackExchange.Redis.RedisValue)">
            <summary>
            Indicates whether two RedisValue values are equivalent
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.Equals(System.Object)">
            <summary>
            See Object.Equals()
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.Equals(StackExchange.Redis.RedisValue)">
            <summary>
            Indicates whether two RedisValue values are equivalent
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.GetHashCode">
            <summary>
            See Object.GetHashCode()
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.ToString">
            <summary>
            Returns a string representation of the value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.CompareTo(StackExchange.Redis.RedisValue)">
            <summary>
            Compare against a RedisValue for relative order
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Implicit(System.Int32)~StackExchange.Redis.RedisValue">
            <summary>
            Creates a new RedisValue from an Int32
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Implicit(System.Nullable{System.Int32})~StackExchange.Redis.RedisValue">
            <summary>
            Creates a new RedisValue from a nullable Int32
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Implicit(System.Int64)~StackExchange.Redis.RedisValue">
            <summary>
            Creates a new RedisValue from an Int64
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Implicit(System.Nullable{System.Int64})~StackExchange.Redis.RedisValue">
            <summary>
            Creates a new RedisValue from a nullable Int64
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Implicit(System.Double)~StackExchange.Redis.RedisValue">
            <summary>
            Creates a new RedisValue from a Double
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Implicit(System.Nullable{System.Double})~StackExchange.Redis.RedisValue">
            <summary>
            Creates a new RedisValue from a nullable Double
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Implicit(System.String)~StackExchange.Redis.RedisValue">
            <summary>
            Creates a new RedisValue from a String
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Implicit(System.Byte[])~StackExchange.Redis.RedisValue">
            <summary>
            Creates a new RedisValue from a Byte[]
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Implicit(System.Boolean)~StackExchange.Redis.RedisValue">
            <summary>
            Creates a new RedisValue from a Boolean
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Implicit(System.Nullable{System.Boolean})~StackExchange.Redis.RedisValue">
            <summary>
            Creates a new RedisValue from a nullable Boolean
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Explicit(StackExchange.Redis.RedisValue)~System.Boolean">
            <summary>
            Converts the value to a Boolean
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Explicit(StackExchange.Redis.RedisValue)~System.Int32">
            <summary>
            Converts the value to an Int32
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Explicit(StackExchange.Redis.RedisValue)~System.Int64">
            <summary>
            Converts the value to an Int64
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Explicit(StackExchange.Redis.RedisValue)~System.Double">
            <summary>
            Converts the value to a Double
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Explicit(StackExchange.Redis.RedisValue)~System.Nullable{System.Double}">
            <summary>
            Converts the value to a nullable Double
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Explicit(StackExchange.Redis.RedisValue)~System.Nullable{System.Int64}">
            <summary>
            Converts the value to a nullable Int64
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Explicit(StackExchange.Redis.RedisValue)~System.Nullable{System.Int32}">
            <summary>
            Converts the value to a nullable Int32
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Explicit(StackExchange.Redis.RedisValue)~System.Nullable{System.Boolean}">
            <summary>
            Converts the value to a nullable Boolean
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Implicit(StackExchange.Redis.RedisValue)~System.String">
            <summary>
            Converts the value to a String
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.op_Implicit(StackExchange.Redis.RedisValue)~System.Byte[]">
            <summary>
            Converts the value to a byte[]
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.TryParse(System.Int64@)">
            <summary>
            Convert to a long if possible, returning true.
            
            Returns false otherwise.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.TryParse(System.Int32@)">
            <summary>
            Convert to a int if possible, returning true.
            
            Returns false otherwise.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.RedisValue.TryParse(System.Double@)">
            <summary>
            Convert to a double if possible, returning true.
            
            Returns false otherwise.
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ReplicationChangeOptions">
            <summary>
            Additional operations to perform when making a server a master
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ReplicationChangeOptions.None">
            <summary>
            No additional operations
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ReplicationChangeOptions.SetTiebreaker">
            <summary>
            Set the tie-breaker key on all available masters, to specify this server
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ReplicationChangeOptions.Broadcast">
            <summary>
            Broadcast to the pub-sub channel to listening clients to reconfigure themselves
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ReplicationChangeOptions.EnslaveSubordinates">
            <summary>
            Issue a SLAVEOF to all other known nodes, making this this master of all
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ReplicationChangeOptions.All">
            <summary>
            All additional operations
            </summary>
        </member>
        <member name="T:StackExchange.Redis.SaveType">
            <summary>
            The type of save operation to perform
            </summary>
        </member>
        <member name="F:StackExchange.Redis.SaveType.BackgroundRewriteAppendOnlyFile">
            <summary>
            Instruct Redis to start an Append Only File rewrite process. The rewrite will create a small optimized version of the current Append Only File.
            </summary>
            <remarks>http://redis.io/commands/bgrewriteaof</remarks>
        </member>
        <member name="F:StackExchange.Redis.SaveType.BackgroundSave">
            <summary>
            Save the DB in background. The OK code is immediately returned. Redis forks, the parent continues to serve the clients, the child saves the DB on disk then exits. A client my be able to check if the operation succeeded using the LASTSAVE command.
            </summary>
            <remarks>http://redis.io/commands/bgsave</remarks>
        </member>
        <member name="F:StackExchange.Redis.SaveType.ForegroundSave">
            <summary>
            Save the DB in foreground. This is almost never a good thing to do, and could cause significant blocking. Only do this if you know you need to save
            </summary>
            <remarks>http://redis.io/commands/save</remarks>
        </member>
        <member name="M:StackExchange.Redis.ScriptParameterMapper.PrepareScript(System.String)">
            <summary>
            Turns a script with @namedParameters into a LuaScript that can be executed
            against a given IDatabase(Async) object
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ScriptParameterMapper.IsValidParameterHash(System.Type,StackExchange.Redis.LuaScript,System.String@,System.String@)">
            <summary>
            Determines whether or not the given type can be used to provide parameters for the given LuaScript.
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ScriptParameterMapper.GetParameterExtractor(System.Type,StackExchange.Redis.LuaScript)">
            <summary>
            Creates a Func that extracts parameters from the given type for use by a LuaScript.
            
            Members that are RedisKey's get extracted to be passed in as keys to redis; all members that
            appear in the script get extracted as RedisValue arguments to be sent up as args.
            
            We send all values as arguments so we don't have to prepare the same script for different parameter
            types.
            
            The created Func takes a RedisKey, which will be prefixed to all keys (and arguments of type RedisKey) for 
            keyspace isolation.
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ServerCounters">
            <summary>
            Illustrates the queues associates with this server
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ServerCounters.EndPoint">
            <summary>
            The endpoint to which this data relates (this can be null if the data represents all servers)
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ServerCounters.Interactive">
            <summary>
            Counters associated with the interactive (non pub-sub) connection
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ServerCounters.Other">
            <summary>
            Counters associated with other ambient activity
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ServerCounters.Subscription">
            <summary>
            Counters associated with the subscription (pub-sub) connection
            </summary>
        </member>
        <member name="P:StackExchange.Redis.ServerCounters.TotalOutstanding">
            <summary>
            Indicates the total number of outstanding items against this server
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ServerCounters.ToString">
            <summary>
            See Object.ToString();
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ServerSelectionStrategy.HashSlot(StackExchange.Redis.RedisKey)">
            <summary>
            Computes the hash-slot that would be used by the given key
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ServerType">
            <summary>
            Indicates the flavor of a particular redis server
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ServerType.Standalone">
            <summary>
            Classic redis-server server
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ServerType.Sentinel">
            <summary>
            Monitoring/configuration redis-sentinel server
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ServerType.Cluster">
            <summary>
            Distributed redis-cluster server
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ServerType.Twemproxy">
            <summary>
            Distributed redis installation via <a href="https://github.com/twitter/twemproxy">twemproxy</a>
            </summary>
        </member>
        <member name="T:StackExchange.Redis.SetOperation">
            <summary>
            Describes an algebraic set operation that can be performed to combine multiple sets
            </summary>
        </member>
        <member name="F:StackExchange.Redis.SetOperation.Union">
            <summary>
            Returns the members of the set resulting from the union of all the given sets.
            </summary>
        </member>
        <member name="F:StackExchange.Redis.SetOperation.Intersect">
            <summary>
            Returns the members of the set resulting from the intersection of all the given sets.
            </summary>
        </member>
        <member name="F:StackExchange.Redis.SetOperation.Difference">
            <summary>
            Returns the members of the set resulting from the difference between the first set and all the successive sets.
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ShutdownMode">
            <summary>
            Defines the persistence behaviour of the server during shutdown
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ShutdownMode.Default">
            <summary>
            The data is persisted if save points are configured
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ShutdownMode.Never">
            <summary>
            The data is NOT persisted even if save points are configured
            </summary>
        </member>
        <member name="F:StackExchange.Redis.ShutdownMode.Always">
            <summary>
            The data is persisted even if save points are NOT configured
            </summary>
        </member>
        <member name="T:StackExchange.Redis.ISocketCallback">
            <summary>
            Allows callbacks from SocketManager as work is discovered
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ISocketCallback.Connected(System.IO.Stream,System.IO.TextWriter)">
            <summary>
            Indicates that a socket has connected
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ISocketCallback.Error">
            <summary>
            Indicates that the socket has signalled an error condition
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ISocketCallback.Read">
            <summary>
            Indicates that data is available on the socket, and that the consumer should read synchronously from the socket while there is data
            </summary>
        </member>
        <member name="M:StackExchange.Redis.ISocketCallback.StartReading">
            <summary>
            Indicates that we cannot know whether data is available, and that the consume should commence reading asynchronously
            </summary>
        </member>
        <member name="T:StackExchange.Redis.SocketManager">
            <summary>
            A SocketManager monitors multiple sockets for availability of data; this is done using
            the Socket.Select API and a dedicated reader-thread, which allows for fast responses
            even when the system is under ambient load. 
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SocketManager.#ctor(System.String)">
            <summary>
            Creates a new (optionally named) SocketManager instance
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SocketManager.#ctor(System.String,System.Boolean)">
            <summary>
            Creates a new SocketManager instance
            </summary>
        </member>
        <member name="P:StackExchange.Redis.SocketManager.Name">
            <summary>
            Gets the name of this SocketManager instance
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SocketManager.Dispose">
            <summary>
            Releases all resources associated with this instance
            </summary>
        </member>
        <member name="T:StackExchange.Redis.SortedSetEntry">
            <summary>
            Describes a sorted-set element with the corresponding value
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SortedSetEntry.#ctor(StackExchange.Redis.RedisValue,System.Double)">
            <summary>
            Initializes a SortedSetEntry value
            </summary>
        </member>
        <member name="P:StackExchange.Redis.SortedSetEntry.Element">
            <summary>
            The unique element stored in the sorted set
            </summary>
        </member>
        <member name="P:StackExchange.Redis.SortedSetEntry.Score">
            <summary>
            The score against the element
            </summary>
        </member>
        <member name="P:StackExchange.Redis.SortedSetEntry.Value">
            <summary>
            The score against the element
            </summary>
        </member>
        <member name="P:StackExchange.Redis.SortedSetEntry.Key">
            <summary>
            The unique element stored in the sorted set
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SortedSetEntry.op_Implicit(StackExchange.Redis.SortedSetEntry)~System.Collections.Generic.KeyValuePair{StackExchange.Redis.RedisValue,System.Double}">
            <summary>
            Converts to a key/value pair
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SortedSetEntry.op_Implicit(System.Collections.Generic.KeyValuePair{StackExchange.Redis.RedisValue,System.Double})~StackExchange.Redis.SortedSetEntry">
            <summary>
            Converts from a key/value pair
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SortedSetEntry.ToString">
            <summary>
            See Object.ToString()
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SortedSetEntry.GetHashCode">
            <summary>
            See Object.GetHashCode()
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SortedSetEntry.Equals(System.Object)">
            <summary>
            Compares two values for equality
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SortedSetEntry.Equals(StackExchange.Redis.SortedSetEntry)">
            <summary>
            Compares two values for equality
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SortedSetEntry.CompareTo(StackExchange.Redis.SortedSetEntry)">
            <summary>
            Compares two values by score
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SortedSetEntry.CompareTo(System.Object)">
            <summary>
            Compares two values by score
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SortedSetEntry.op_Equality(StackExchange.Redis.SortedSetEntry,StackExchange.Redis.SortedSetEntry)">
            <summary>
            Compares two values for equality
            </summary>
        </member>
        <member name="M:StackExchange.Redis.SortedSetEntry.op_Inequality(StackExchange.Redis.SortedSetEntry,StackExchange.Redis.SortedSetEntry)">
            <summary>
            Compares two values for non-equality
            </summary>
        </member>
        <member name="T:StackExchange.Redis.SortType">
            <summary>
            Specifies how to compare elements for sorting
            </summary>
        </member>
        <member name="F:StackExchange.Redis.SortType.Numeric">
            <summary>
            Elements are interpreted as a double-precision floating point number and sorted numerically
            </summary>
        </member>
        <member name="F:StackExchange.Redis.SortType.Alphabetic">
            <summary>
            Elements are sorted using their alphabetic form (Redis is UTF-8 aware as long as the !LC_COLLATE environment variable is set at the server)
            </summary>
        </member>
        <member name="T:StackExchange.Redis.TaskSource">
            <summary>
            We want to prevent callers hijacking the reader thread; this is a bit nasty, but works;
            see http://stackoverflow.com/a/22588431/23354 for more information; a huge
            thanks to Eli Arbel for spotting this (even though it is pure evil; it is *my kind of evil*)
            </summary>
        </member>
        <member name="F:StackExchange.Redis.TaskSource.IsSyncSafe">
            <summary>
            Indicates whether the specified task will not hijack threads when results are set
            </summary>
        </member>
        <member name="M:StackExchange.Redis.TaskSource.Create``1(System.Object)">
            <summary>
            Create a new TaskCompletion source
            </summary>
        </member>
        <member name="M:StackExchange.Redis.TaskSource.CreateDenyExecSync``1(System.Object)">
            <summary>
            Create a new TaskCompletionSource that will not allow result-setting threads to be hijacked
            </summary>
        </member>
        <member name="T:StackExchange.Redis.When">
            <summary>
            Indicates when this operation should be performed (only some variations are legal in a given context)
            </summary>
        </member>
        <member name="F:StackExchange.Redis.When.Always">
            <summary>
            The operation should occur whether or not there is an existing value 
            </summary>
        </member>
        <member name="F:StackExchange.Redis.When.Exists">
            <summary>
            The operation should only occur when there is an existing value 
            </summary>
        </member>
        <member name="F:StackExchange.Redis.When.NotExists">
            <summary>
            The operation should only occur when there is not an existing value 
            </summary>
        </member>
    </members>
</doc>
