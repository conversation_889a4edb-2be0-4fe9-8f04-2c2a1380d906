﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Collections</name>
  </assembly>
  <members>
    <member name="T:System.Collections.BitArray">
      <summary>Gère un tableau compact de valeurs de bit représentées par des valeurs booléennes, où true indique que le bit est activé (1) et false que le bit est désactivé (0).</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Boolean[])">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.BitArray" /> qui contient des valeurs de bit copiées à partir du tableau de valeurs booléennes spécifié.</summary>
      <param name="values">Tableau de valeurs booléennes à copier. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Byte[])">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.BitArray" /> qui contient des valeurs de bit copiées à partir du tableau d'octets spécifié.</summary>
      <param name="bytes">Tableau d'octets contenant les valeurs à copier, dans lequel chaque octet représente huit bits consécutifs. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="bytes" /> is greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Collections.BitArray)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.BitArray" /> qui contient des valeurs de bit copiées à partir du <see cref="T:System.Collections.BitArray" /> spécifié.</summary>
      <param name="bits">
        <see cref="T:System.Collections.BitArray" /> à copier. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bits" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.BitArray" /> qui peut contenir le nombre spécifié de valeurs de bit, dont la valeur initiale est false.</summary>
      <param name="length">Nombre de valeurs de bit dans le nouveau <see cref="T:System.Collections.BitArray" />. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32,System.Boolean)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.BitArray" /> qui peut contenir le nombre spécifié de valeurs de bit auxquelles la valeur spécifiée est affectée initialement.</summary>
      <param name="length">Nombre de valeurs de bit dans le nouveau <see cref="T:System.Collections.BitArray" />. </param>
      <param name="defaultValue">Valeur booléenne à assigner à chaque bit. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32[])">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.BitArray" /> qui contient des valeurs de bit copiées à partir du tableau d'entiers 32 bits spécifié.</summary>
      <param name="values">Tableau d'entiers contenant les valeurs à copier, dans lequel chaque entier représente 32 bits consécutifs. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="values" /> is greater than <see cref="F:System.Int32.MaxValue" /></exception>
    </member>
    <member name="M:System.Collections.BitArray.And(System.Collections.BitArray)">
      <summary>Exécute l'opération de bits AND sur les éléments dans le <see cref="T:System.Collections.BitArray" /> actuel par rapport aux éléments correspondants dans le <see cref="T:System.Collections.BitArray" /> spécifié.</summary>
      <returns>Instance actuelle contenant le résultat de l'opération de bits AND sur les éléments du <see cref="T:System.Collections.BitArray" /> actuel par rapport aux éléments correspondants du <see cref="T:System.Collections.BitArray" /> spécifié.</returns>
      <param name="value">
        <see cref="T:System.Collections.BitArray" /> utilisé pour exécuter l'opération de bits AND. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Get(System.Int32)">
      <summary>Obtient la valeur du bit à une position spécifique dans <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Valeur du bit à la position <paramref name="index" />.</returns>
      <param name="index">Index de base zéro de la valeur à obtenir. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Un <see cref="T:System.Collections.IEnumerator" /> pour l'intégralité de <see cref="T:System.Collections.BitArray" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Item(System.Int32)">
      <summary>Obtient ou définit la valeur du bit à une position spécifique dans <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Valeur du bit à la position <paramref name="index" />.</returns>
      <param name="index">Index de base zéro de la valeur à obtenir ou définir. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is equal to or greater than <see cref="P:System.Collections.BitArray.Count" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Length">
      <summary>Obtient ou définit le nombre d'éléments dans <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Nombre d'éléments dans le <see cref="T:System.Collections.BitArray" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The property is set to a value that is less than zero. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Not">
      <summary>Inverse toutes les valeurs de bits dans le <see cref="T:System.Collections.BitArray" /> actuel, de sorte que les éléments ayant la valeur true prennent la valeur false et que les éléments ayant la valeur false prennent la valeur true.</summary>
      <returns>L'instance actuelle comportant des valeurs de bit inversées.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Or(System.Collections.BitArray)">
      <summary>Exécute l'opération de bits OR sur les éléments dans le <see cref="T:System.Collections.BitArray" /> actuel par rapport aux éléments correspondants dans le <see cref="T:System.Collections.BitArray" /> spécifié.</summary>
      <returns>Instance actuelle contenant le résultat de l'opération de bits OR sur les éléments du <see cref="T:System.Collections.BitArray" /> actuel par rapport aux éléments correspondants du <see cref="T:System.Collections.BitArray" /> spécifié.</returns>
      <param name="value">
        <see cref="T:System.Collections.BitArray" /> utilisé pour exécuter l'opération de bits OR. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Set(System.Int32,System.Boolean)">
      <summary>Affecte au bit à une position spécifique dans <see cref="T:System.Collections.BitArray" /> la valeur spécifiée.</summary>
      <param name="index">Index de base zéro du bit à définir. </param>
      <param name="value">Valeur booléenne à assigner au bit. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.SetAll(System.Boolean)">
      <summary>Affecte la valeur spécifiée à tous les bits de <see cref="T:System.Collections.BitArray" />.</summary>
      <param name="value">Valeur booléenne à assigner à tous les bits. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copie les éléments de <see cref="T:System.Collections.BitArray" /> dans <see cref="T:System.Array" />, à partir de l'index spécifié de <see cref="T:System.Array" />.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensionnel qui constitue la destination des éléments copiés à partir de <see cref="T:System.Collections.BitArray" />.<see cref="T:System.Array" /> doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans l'<paramref name="array" /> à partir duquel la copie commence. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or- The number of elements in the source <see cref="T:System.Collections.BitArray" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.BitArray" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#Count">
      <summary>Obtient le nombre d'éléments dans <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Nombre d'éléments dans le <see cref="T:System.Collections.BitArray" />.</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#IsSynchronized">
      <summary>Obtient une valeur qui indique si l'accès à <see cref="T:System.Collections.BitArray" /> est synchronisé (thread safe).</summary>
      <returns>true si l'accès à <see cref="T:System.Collections.BitArray" /> est synchronisé (thread-safe) ; sinon false.</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#SyncRoot">
      <summary>Obtient un objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.BitArray" />.</returns>
    </member>
    <member name="M:System.Collections.BitArray.Xor(System.Collections.BitArray)">
      <summary>Exécute l'opération de bits OR exclusive sur les éléments dans le <see cref="T:System.Collections.BitArray" /> actuel par rapport aux éléments correspondants dans le <see cref="T:System.Collections.BitArray" /> spécifié.</summary>
      <returns>Instance actuelle contenant le résultat de l'opération de bits OR exclusive sur les éléments du <see cref="T:System.Collections.BitArray" /> actuel par rapport aux éléments correspondants du <see cref="T:System.Collections.BitArray" /> spécifié. </returns>
      <param name="value">
        <see cref="T:System.Collections.BitArray" /> utilisé pour exécuter l'opération de bits OR exclusive. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Collections.StructuralComparisons">
      <summary>Fournit les objets nécessaires pour effectuer une comparaison structurelle de deux objets collection.</summary>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralComparer">
      <summary>Obtient un objet prédéfini qui effectue une comparaison structurelle de deux objets.</summary>
      <returns>Objet prédéfini utilisé pour effectuer une comparaison structurelle de deux objets collection.</returns>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralEqualityComparer">
      <summary>Obtient un objet prédéfini qui compare deux objets pour déterminer l'égalité structurelle.</summary>
      <returns>Objet prédéfini utilisé pour comparer deux objets collection pour déterminer l'égalité structurelle.</returns>
    </member>
    <member name="T:System.Collections.Generic.Comparer`1">
      <summary>Fournit une classe de base pour les implémentations de l'interface générique <see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
      <typeparam name="T">Type des objets à comparer.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.Comparer`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Compare(`0,`0)">
      <summary>En cas de substitution dans une classe dérivée, effectue une comparaison de deux objets du même type et retourne une valeur indiquant si l'un est inférieur, égal ou supérieur à l'autre.</summary>
      <returns>Entier signé qui indique les valeurs relatives de <paramref name="x" /> et <paramref name="y" />, comme indiqué dans le tableau suivant.Valeur Signification Inférieur à zéro <paramref name="x" /> est inférieur à <paramref name="y" />.Zéro <paramref name="x" /> est égal à <paramref name="y" />.Supérieure à zéro <paramref name="x" /> est supérieur à <paramref name="y" />.</returns>
      <param name="x">Premier objet à comparer.</param>
      <param name="y">Second objet à comparer.</param>
      <exception cref="T:System.ArgumentException">Le type <paramref name="T" /> n'implémente pas l'interface générique <see cref="T:System.IComparable`1" /> ni l'interface <see cref="T:System.IComparable" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Create(System.Comparison{`0})">
      <summary>Crée un comparateur à partir d'une comparaison spécifiée.</summary>
      <returns>Nouveau comparateur.</returns>
      <param name="comparison">La comparaison à utiliser.</param>
    </member>
    <member name="P:System.Collections.Generic.Comparer`1.Default">
      <summary>Retourne un comparateur d'ordre de tri par défaut pour le type spécifié par l'argument générique.</summary>
      <returns>Objet qui hérite de <see cref="T:System.Collections.Generic.Comparer`1" /> et sert de comparateur d'ordre de tri pour le type <paramref name="T" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>Compare deux objets et retourne une valeur indiquant si le premier est inférieur, égal ou supérieur au second.</summary>
      <returns>Entier signé qui indique les valeurs relatives de <paramref name="x" /> et <paramref name="y" />, comme indiqué dans le tableau suivant.Valeur Signification Inférieur à zéro<paramref name="x" /> est inférieur à <paramref name="y" />.Zéro<paramref name="x" /> est égal à <paramref name="y" />.Supérieure à zéro<paramref name="x" /> est supérieur à <paramref name="y" />.</returns>
      <param name="x">Premier objet à comparer.</param>
      <param name="y">Second objet à comparer.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> ou <paramref name="y" /> est d'un type qui ne peut pas être casté en type <paramref name="T" />.ou<paramref name="x" /> et <paramref name="y" /> n'implémentent pas l'interface générique <see cref="T:System.IComparable`1" /> ni l'interface <see cref="T:System.IComparable" />.</exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2">
      <summary>Représente une collection de clés et de valeurs.Pour parcourir le code source de .NET Framework pour ce type, consultez la Reference Source.</summary>
      <typeparam name="TKey">Type des clés dans le dictionnaire.</typeparam>
      <typeparam name="TValue">Type des valeurs dans le dictionnaire.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.Dictionary`2" /> qui est vide, possède la capacité initiale par défaut et utilise le comparateur d'égalité par défaut pour le type de clé.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.Dictionary`2" /> qui contient des éléments copiés à partir du <see cref="T:System.Collections.Generic.IDictionary`2" /> spécifié et utilise le comparateur d'égalité par défaut pour le type de clé.</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" /> dont les éléments sont copiés dans le nouveau <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> contient une ou plusieurs clés dupliquées.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.Dictionary`2" /> qui contient des éléments copiés à partir du <see cref="T:System.Collections.Generic.IDictionary`2" /> spécifié et utilise le <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> spécifié.</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" /> dont les éléments sont copiés dans le nouveau <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <param name="comparer">Implémentation de <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> à utiliser pendant la comparaison de clés, ou null pour utiliser le <see cref="T:System.Collections.Generic.EqualityComparer`1" /> par défaut pour le type de la clé.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> contient une ou plusieurs clés dupliquées.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.Dictionary`2" /> qui est vide, possède la capacité initiale par défaut et utilise le <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> spécifié.</summary>
      <param name="comparer">Implémentation de <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> à utiliser pendant la comparaison de clés, ou null pour utiliser le <see cref="T:System.Collections.Generic.EqualityComparer`1" /> par défaut pour le type de la clé.</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.Dictionary`2" /> qui est vide, possède la capacité initiale spécifiée et utilise le comparateur d'égalité par défaut pour le type de clé.</summary>
      <param name="capacity">Nombre initial d'éléments que <see cref="T:System.Collections.Generic.Dictionary`2" /> peut contenir.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> est inférieur à 0.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.Dictionary`2" /> qui est vide, possède la capacité initiale spécifiée et utilise le <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> spécifié.</summary>
      <param name="capacity">Nombre initial d'éléments que <see cref="T:System.Collections.Generic.Dictionary`2" /> peut contenir.</param>
      <param name="comparer">Implémentation de <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> à utiliser pendant la comparaison de clés, ou null pour utiliser le <see cref="T:System.Collections.Generic.EqualityComparer`1" /> par défaut pour le type de la clé.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> est inférieur à 0.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Add(`0,`1)">
      <summary>Ajoute la clé et la valeur spécifiées au dictionnaire.</summary>
      <param name="key">Clé de l'élément à ajouter.</param>
      <param name="value">Valeur de l'élément à ajouter.La valeur peut être null pour les types référence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Un élément possédant la même clé existe déjà dans <see cref="T:System.Collections.Generic.Dictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Clear">
      <summary>Supprime toutes les clés et les valeurs de <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Comparer">
      <summary>Obtient le <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> qui est utilisé pour déterminer l'égalité des clés pour le dictionnaire. </summary>
      <returns>Implémentation d'interface générique <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> qui est utilisée pour déterminer l'égalité des clés pour le <see cref="T:System.Collections.Generic.Dictionary`2" /> actuel et pour fournir des valeurs de hachage pour les clés.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsKey(`0)">
      <summary>Détermine si <see cref="T:System.Collections.Generic.Dictionary`2" /> contient la clé spécifiée.</summary>
      <returns>true si <see cref="T:System.Collections.Generic.Dictionary`2" /> contient un élément correspondant à la clé spécifiée ; sinon, false.</returns>
      <param name="key">Clé à rechercher dans <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsValue(`1)">
      <summary>Détermine si <see cref="T:System.Collections.Generic.Dictionary`2" /> contient une valeur spécifique.</summary>
      <returns>true si <see cref="T:System.Collections.Generic.Dictionary`2" /> contient un élément correspondant à la valeur spécifiée ; sinon, false.</returns>
      <param name="value">Valeur à trouver dans <see cref="T:System.Collections.Generic.Dictionary`2" />.La valeur peut être null pour les types référence.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Count">
      <summary>Obtient le nombre de paires clé/valeur contenues dans <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>Nombre de paires clé/valeur contenues dans <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>Structure <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" /> pour l'objet <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Item(`0)">
      <summary>Obtient ou définit la valeur associée à la clé spécifiée.</summary>
      <returns>Valeur associée à la clé spécifiée.Si la clé spécifiée est introuvable, une opération Get retourne <see cref="T:System.Collections.Generic.KeyNotFoundException" /> et une opération Set crée un nouvel élément avec la clé spécifiée.</returns>
      <param name="key">Clé de la valeur à obtenir ou à définir.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">La propriété est récupérée et <paramref name="key" /> n'existe pas dans la collection.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Keys">
      <summary>Obtient une collection contenant les clés dans <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> contenant les clés de <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Remove(`0)">
      <summary>Supprime de <see cref="T:System.Collections.Generic.Dictionary`2" /> la valeur ayant la clé spécifiée.</summary>
      <returns>true si la recherche et la suppression de l'élément réussissent ; sinon, false.Cette méthode retourne false si <paramref name="key" /> est introuvable dans <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
      <param name="key">Clé de l'élément à supprimer.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Ajoute la valeur spécifiée à <see cref="T:System.Collections.Generic.ICollection`1" /> avec la clé spécifiée.</summary>
      <param name="keyValuePair">Structure <see cref="T:System.Collections.Generic.KeyValuePair`2" /> qui représente la clé et la valeur à ajouter à <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">La clé de <paramref name="keyValuePair" /> est null.</exception>
      <exception cref="T:System.ArgumentException">Un élément possédant la même clé existe déjà dans <see cref="T:System.Collections.Generic.Dictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Détermine si <see cref="T:System.Collections.Generic.ICollection`1" /> contient une clé et une valeur spécifiques.</summary>
      <returns>true si <paramref name="keyValuePair" /> existe dans <see cref="T:System.Collections.Generic.ICollection`1" /> ; sinon, false.</returns>
      <param name="keyValuePair">Structure <see cref="T:System.Collections.Generic.KeyValuePair`2" /> à rechercher dans <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>Copie les éléments de <see cref="T:System.Collections.Generic.ICollection`1" /> dans un tableau de type <see cref="T:System.Collections.Generic.KeyValuePair`2" />, en commençant au niveau de l'index de tableau spécifié.</summary>
      <param name="array">Tableau unidimensionnel de type <see cref="T:System.Collections.Generic.KeyValuePair`2" /> constituant la destination des éléments <see cref="T:System.Collections.Generic.KeyValuePair`2" /> copiés à partir de <see cref="T:System.Collections.Generic.ICollection`1" />.Ce tableau doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans l'<paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">Le nombre d'éléments dans le <see cref="T:System.Collections.Generic.ICollection`1" /> source est supérieur à la quantité d'espace disponible entre <paramref name="index" /> et la fin du <paramref name="array" /> de destination.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtient une valeur indiquant si le dictionnaire est en lecture seule.</summary>
      <returns>true si <see cref="T:System.Collections.Generic.ICollection`1" /> est en lecture seule ; sinon, false.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.Dictionary`2" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Supprime une clé et une valeur du dictionnaire.</summary>
      <returns>true si la recherche et la suppression de la clé et de la valeur représentées par <paramref name="keyValuePair" /> réussissent ; sinon, false.Cette méthode retourne false si <paramref name="keyValuePair" /> est introuvable dans <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">Structure <see cref="T:System.Collections.Generic.KeyValuePair`2" /> qui représente la clé et la valeur à supprimer de <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Obtient un <see cref="T:System.Collections.Generic.ICollection`1" /> contenant les clés de <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> de type <paramref name="TKey" /> contenant les clés de <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Obtient <see cref="T:System.Collections.Generic.ICollection`1" /> contenant les valeurs de <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> de type <paramref name="TValue" /> contenant les valeurs de <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de la collection.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> qui peut être utilisé pour itérer au sein de la collection.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Obtient une collection contenant les clés de <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</summary>
      <returns>Collection contenant les clés de <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Obtient une collection contenant les valeurs de <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</summary>
      <returns>Collection contenant les valeurs de <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copie les éléments de <see cref="T:System.Collections.Generic.ICollection`1" /> dans un tableau, en commençant au niveau d'un index de tableau spécifié.</summary>
      <param name="array">Tableau unidimensionnel correspondant à la destination des éléments copiés à partir depuis <see cref="T:System.Collections.Generic.ICollection`1" />.Ce tableau doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans l'<paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> est multidimensionnel.ou<paramref name="array" /> n'a pas d'indexation de base zéro.ouLe nombre d'éléments dans le <see cref="T:System.Collections.Generic.ICollection`1" /> source est supérieur à la quantité d'espace disponible entre <paramref name="index" /> et la fin du <paramref name="array" /> de destination.ouLe cast automatique du type du <see cref="T:System.Collections.Generic.ICollection`1" /> source en type du <paramref name="array" /> de destination est impossible.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>Obtient une valeur indiquant si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe).</summary>
      <returns>true si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe) ; sinon false.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.Dictionary`2" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>Obtient un objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />. </returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Ajoute la clé et la valeur spécifiées au dictionnaire.</summary>
      <param name="key">Objet à utiliser comme clé.</param>
      <param name="value">Objet à utiliser comme valeur.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> a un type qui ne peut pas être assigné au type de clé <paramref name="TKey" /> de <see cref="T:System.Collections.Generic.Dictionary`2" />.ou<paramref name="value" /> a un type qui ne peut pas être assigné à <paramref name="TValue" />, le type des valeurs dans <see cref="T:System.Collections.Generic.Dictionary`2" />.ouUne valeur ayant la même clé existe déjà dans <see cref="T:System.Collections.Generic.Dictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Détermine si <see cref="T:System.Collections.IDictionary" /> contient un élément avec la clé spécifiée.</summary>
      <returns>true si <see cref="T:System.Collections.IDictionary" /> contient un élément correspondant à la clé spécifiée ; sinon, false.</returns>
      <param name="key">Clé à rechercher dans <see cref="T:System.Collections.IDictionary" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Retourne <see cref="T:System.Collections.IDictionaryEnumerator" /> pour l'objet <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>
        <see cref="T:System.Collections.IDictionaryEnumerator" /> pour le <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Obtient une valeur indiquant si <see cref="T:System.Collections.IDictionary" /> est de taille fixe.</summary>
      <returns>true si <see cref="T:System.Collections.IDictionary" /> est de taille fixe ; sinon, false.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.Dictionary`2" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Obtient une valeur indiquant si <see cref="T:System.Collections.IDictionary" /> est en lecture seule.</summary>
      <returns>true si <see cref="T:System.Collections.IDictionary" /> est en lecture seule ; sinon, false.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.Dictionary`2" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Obtient ou définit la valeur avec la clé spécifiée.</summary>
      <returns>Valeur associée à la clé spécifiée, ou null si <paramref name="key" /> ne figure pas dans le dictionnaire ou si le type de <paramref name="key" /> ne peut pas être assigné au type de clé <paramref name="TKey" /> de <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
      <param name="key">Clé de la valeur à obtenir.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Une valeur est assignée et <paramref name="key" /> a un type qui ne peut pas être assigné au type de clé <paramref name="TKey" /> de <see cref="T:System.Collections.Generic.Dictionary`2" />.ouUne valeur est assignée et <paramref name="value" /> a un type qui ne peut pas être assigné au type valeur <paramref name="TValue" /> de <see cref="T:System.Collections.Generic.Dictionary`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Keys">
      <summary>Obtient un <see cref="T:System.Collections.ICollection" /> contenant les clés de <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> contenant les clés de <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Supprime de <see cref="T:System.Collections.IDictionary" /> l'élément ayant la clé spécifiée.</summary>
      <param name="key">Clé de l'élément à supprimer.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Values">
      <summary>Obtient <see cref="T:System.Collections.ICollection" /> contenant les valeurs de <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> contenant les valeurs de <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de la collection.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> qui peut être utilisé pour itérer au sein de la collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.TryGetValue(`0,`1@)">
      <summary>Obtient la valeur associée à la clé spécifiée.</summary>
      <returns>true si <see cref="T:System.Collections.Generic.Dictionary`2" /> contient un élément correspondant à la clé spécifiée ; sinon, false.</returns>
      <param name="key">Clé de la valeur à obtenir.</param>
      <param name="value">Cette méthode retourne la valeur associée à la clé spécifiée si la clé est trouvée ; sinon, retourne la valeur par défaut pour le type du paramètre <paramref name="value" />.Ce paramètre est passé sans être initialisé.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Values">
      <summary>Obtient une collection contenant les valeurs dans <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> contenant les valeurs de <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.Enumerator">
      <summary>Énumère les éléments de <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans <see cref="T:System.Collections.Generic.Dictionary`2" /> à la position actuelle de l'énumérateur.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.Dispose">
      <summary>Libère toutes les ressources utilisées par <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.MoveNext">
      <summary>Avance l'énumérateur à l'élément suivant de <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>true si l'énumérateur a pu avancer jusqu'à l'élément suivant ; false si l'énumérateur a dépassé la fin de la collection.</returns>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans le dictionnaire à la position actuelle de l'énumérateur en tant que <see cref="T:System.Collections.DictionaryEntry" />.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>Obtient la clé de l'élément à la position actuelle de l'énumérateur.</summary>
      <returns>Clé de l'élément dans le dictionnaire à la position actuelle de l'énumérateur.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>Obtient la valeur de l'élément à la position actuelle de l'énumérateur.</summary>
      <returns>Valeur de l'élément dans le dictionnaire à la position actuelle de l'énumérateur.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans la collection à la position actuelle de l'énumérateur en tant que <see cref="T:System.Object" />.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Rétablit l'énumérateur à sa position initiale, qui précède le premier élément de la collection.</summary>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection">
      <summary>Représente la collection de clés dans <see cref="T:System.Collections.Generic.Dictionary`2" />.Cette classe ne peut pas être héritée.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> qui reflète les clés dans le <see cref="T:System.Collections.Generic.Dictionary`2" /> spécifié.</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.Dictionary`2" /> dont les clés sont répercutées dans la nouvelle <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>Copie les éléments <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> dans un <see cref="T:System.Array" /> unidimensionnel existant, en commençant au niveau de l'index de tableau spécifié.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensionnel qui constitue la destination des éléments copiés à partir de <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.<see cref="T:System.Array" /> doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans <paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à zéro.</exception>
      <exception cref="T:System.ArgumentException">Le nombre d'éléments dans le <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> source est supérieur à la quantité d'espace disponible entre <paramref name="index" /> et la fin du <paramref name="array" /> de destination.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Count">
      <summary>Obtient le nombre d'éléments contenus dans <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
      <returns>Nombre d'éléments contenus dans <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.La récupération de la valeur de cette propriété est une opération O(1).</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" /> pour <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Ajoute un élément à <see cref="T:System.Collections.Generic.ICollection`1" />.  Cette implémentation lève toujours <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">Objet à ajouter à <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Toujours levée.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Supprime tous les éléments du <see cref="T:System.Collections.Generic.ICollection`1" />.  Cette implémentation lève toujours <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Toujours levée.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>Détermine si <see cref="T:System.Collections.Generic.ICollection`1" /> contient une valeur spécifique.</summary>
      <returns>true si <paramref name="item" /> existe dans <see cref="T:System.Collections.Generic.ICollection`1" /> ; sinon, false.</returns>
      <param name="item">Objet à trouver dans <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtient une valeur indiquant si <see cref="T:System.Collections.Generic.ICollection`1" /> est en lecture seule.</summary>
      <returns>true si <see cref="T:System.Collections.Generic.ICollection`1" /> est en lecture seule ; sinon, false.  Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, cette propriété retourne toujours true.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>Supprime la première occurrence d'un objet spécifique de <see cref="T:System.Collections.Generic.ICollection`1" />.  Cette implémentation lève toujours <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true si <paramref name="item" /> a été correctement supprimé de <see cref="T:System.Collections.Generic.ICollection`1" /> ; sinon false.Cette méthode retourne également false si <paramref name="item" /> est introuvable dans le <see cref="T:System.Collections.Generic.ICollection`1" /> d'origine.</returns>
      <param name="item">Objet à supprimer de <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Toujours levée.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein d'une collection.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> qui peut être utilisé pour itérer au sein de la collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copie les éléments de <see cref="T:System.Collections.ICollection" /> dans <see cref="T:System.Array" />, en commençant à un index particulier de <see cref="T:System.Array" />.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensionnel qui constitue la destination des éléments copiés à partir d'<see cref="T:System.Collections.ICollection" />.<see cref="T:System.Array" /> doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans <paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à zéro.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> est multidimensionnel.ou<paramref name="array" /> n'a pas d'indexation de base zéro.ouLe nombre d'éléments dans le <see cref="T:System.Collections.ICollection" /> source est supérieur à la quantité d'espace disponible entre <paramref name="index" /> et la fin du <paramref name="array" /> de destination.ouLe cast automatique du type du <see cref="T:System.Collections.ICollection" /> source en type du <paramref name="array" /> de destination est impossible.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Obtient une valeur indiquant si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe).</summary>
      <returns>true si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe) ; sinon false.  Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>Obtient un objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.  Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, cette propriété retourne toujours l'instance actuelle.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein d'une collection.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> qui peut être utilisé pour itérer la collection.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator">
      <summary>Énumère les éléments d'un élément <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Current">
      <summary>Obtient l'élément situé à la position actuelle de l'énumérateur.</summary>
      <returns>Élément situé dans le <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> à la position actuelle de l'énumérateur.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>Libère toutes les ressources utilisées par <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>Avance l'énumérateur à l'élément suivant du <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
      <returns>true si l'énumérateur a pu avancer jusqu'à l'élément suivant ; false si l'énumérateur a dépassé la fin de la collection.</returns>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtient l'élément situé à la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans la collection à la position actuelle de l'énumérateur.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Rétablit l'énumérateur à sa position initiale, qui précède le premier élément de la collection.</summary>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection">
      <summary>Représente la collection de valeurs dans <see cref="T:System.Collections.Generic.Dictionary`2" />.Cette classe ne peut pas être héritée.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> qui reflète les valeurs dans le <see cref="T:System.Collections.Generic.Dictionary`2" /> spécifié.</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.Dictionary`2" /> dont les valeurs sont reflétées dans la nouvelle <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>Copie les éléments <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> dans un <see cref="T:System.Array" /> unidimensionnel existant, en commençant au niveau de l'index de tableau spécifié.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensionnel qui constitue la destination des éléments copiés à partir de <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.<see cref="T:System.Array" /> doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans <paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à zéro.</exception>
      <exception cref="T:System.ArgumentException">Le nombre d'éléments dans le <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> source est supérieur à la quantité d'espace disponible entre <paramref name="index" /> et la fin du <paramref name="array" /> de destination.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Count">
      <summary>Obtient le nombre d'éléments contenus dans <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
      <returns>Nombre d'éléments contenus dans <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" /> pour <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>Ajoute un élément à <see cref="T:System.Collections.Generic.ICollection`1" />.  Cette implémentation lève toujours <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">Objet à ajouter à <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Toujours levée.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Supprime tous les éléments du <see cref="T:System.Collections.Generic.ICollection`1" />.  Cette implémentation lève toujours <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Toujours levée.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>Détermine si <see cref="T:System.Collections.Generic.ICollection`1" /> contient une valeur spécifique.</summary>
      <returns>true si <paramref name="item" /> existe dans <see cref="T:System.Collections.Generic.ICollection`1" /> ; sinon, false.</returns>
      <param name="item">Objet à trouver dans <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtient une valeur indiquant si <see cref="T:System.Collections.Generic.ICollection`1" /> est en lecture seule.</summary>
      <returns>true si <see cref="T:System.Collections.Generic.ICollection`1" /> est en lecture seule ; sinon, false.  Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />, cette propriété retourne toujours true.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>Supprime la première occurrence d'un objet spécifique de <see cref="T:System.Collections.Generic.ICollection`1" />.Cette implémentation lève toujours <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true si <paramref name="item" /> a été correctement supprimé de <see cref="T:System.Collections.Generic.ICollection`1" /> ; sinon false.Cette méthode retourne également false si <paramref name="item" /> est introuvable dans le <see cref="T:System.Collections.Generic.ICollection`1" /> d'origine.</returns>
      <param name="item">Objet à supprimer de <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Toujours levée.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein d'une collection.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> qui peut être utilisé pour itérer au sein de la collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copie les éléments de <see cref="T:System.Collections.ICollection" /> dans <see cref="T:System.Array" />, en commençant à un index particulier de <see cref="T:System.Array" />.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensionnel qui constitue la destination des éléments copiés à partir d'<see cref="T:System.Collections.ICollection" />.<see cref="T:System.Array" /> doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans <paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à zéro.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> est multidimensionnel.ou<paramref name="array" /> n'a pas d'indexation de base zéro.ouLe nombre d'éléments dans le <see cref="T:System.Collections.ICollection" /> source est supérieur à la quantité d'espace disponible entre <paramref name="index" /> et la fin du <paramref name="array" /> de destination.ouLe cast automatique du type du <see cref="T:System.Collections.ICollection" /> source en type du <paramref name="array" /> de destination est impossible.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Obtient une valeur indiquant si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe).</summary>
      <returns>true si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe) ; sinon false.  Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>Obtient un objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.  Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />, cette propriété retourne toujours l'instance actuelle.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein d'une collection.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> qui peut être utilisé pour itérer la collection.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator">
      <summary>Énumère les éléments de <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> à la position actuelle de l'énumérateur.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>Libère toutes les ressources utilisées par <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>Avance l'énumérateur à l'élément suivant de <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
      <returns>true si l'énumérateur a pu avancer jusqu'à l'élément suivant ; false si l'énumérateur a dépassé la fin de la collection.</returns>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans la collection à la position actuelle de l'énumérateur.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Rétablit l'énumérateur à sa position initiale, qui précède le premier élément de la collection.</summary>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="T:System.Collections.Generic.EqualityComparer`1">
      <summary>Fournit une classe de base pour les implémentations de l'interface générique <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <typeparam name="T">Type des objets à comparer.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.EqualityComparer`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.EqualityComparer`1.Default">
      <summary>Retourne un comparateur d'égalité par défaut pour le type spécifié par l'argument générique.</summary>
      <returns>Instance par défaut de la classe <see cref="T:System.Collections.Generic.EqualityComparer`1" /> pour le type <paramref name="T" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.Equals(`0,`0)">
      <summary>En cas de substitution dans une classe dérivée, détermine si deux objets de type <paramref name="T" /> sont égaux.</summary>
      <returns>true si les objets spécifiés sont égaux ; sinon, false.</returns>
      <param name="x">Premier objet à comparer.</param>
      <param name="y">Deuxième objet à comparer.</param>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.GetHashCode(`0)">
      <summary>En cas de substitution dans une classe dérivée, sert de fonction de hachage pour l'objet spécifié pour le hachage d'algorithmes et des structures de données, telles qu'une table de hachage.</summary>
      <returns>Code de hachage pour l'objet spécifié.</returns>
      <param name="obj">Objet pour lequel obtenir un code de hachage.</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>Détermine si les objets spécifiés sont égaux.</summary>
      <returns>true si les objets spécifiés sont égaux ; sinon, false.</returns>
      <param name="x">Premier objet à comparer.</param>
      <param name="y">Deuxième objet à comparer.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> or <paramref name="y" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>Retourne un code de hachage pour l'objet spécifié.</summary>
      <returns>Code de hachage pour l'objet spécifié.</returns>
      <param name="obj">
        <see cref="T:System.Object" /> pour lequel un code de hachage doit être retourné.</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.-or-<paramref name="obj" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1">
      <summary>Représente un ensemble de valeurs.Pour parcourir le code source de .NET Framework pour ce type, consultez la Reference Source.</summary>
      <typeparam name="T">Type d'éléments de l'ensemble de hachages.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.HashSet`1" /> vide et utilise le comparateur d'égalité par défaut pour le type du jeu.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.HashSet`1" /> qui utilise le comparateur d'égalité par défaut pour le type du jeu, contient des éléments copiés à partir de la collection spécifiée et possède une capacité suffisante pour accueillir le nombre d'éléments copiés.</summary>
      <param name="collection">Collection dont les éléments sont copiés dans le nouvel ensemble.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.HashSet`1" /> qui utilise le comparateur d'égalité spécifié pour le type du jeu, contient des éléments copiés à partir de la collection spécifiée et possède une capacité suffisante pour accueillir le nombre d'éléments copiés.</summary>
      <param name="collection">Collection dont les éléments sont copiés dans le nouvel ensemble.</param>
      <param name="comparer">Implémentation du <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> à utiliser pendant la comparaison des valeurs du jeu, ou null pour utiliser l'implémentation du <see cref="T:System.Collections.Generic.EqualityComparer`1" /> par défaut pour le type du jeu.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.HashSet`1" /> vide et utilise le comparateur d'égalité spécifié pour le type du jeu.</summary>
      <param name="comparer">Implémentation du <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> à utiliser pendant la comparaison des valeurs du jeu, ou null pour utiliser l'implémentation du <see cref="T:System.Collections.Generic.EqualityComparer`1" /> par défaut pour le type du jeu.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Add(`0)">
      <summary>Ajoute l'élément spécifié à un ensemble.</summary>
      <returns>true si l'élément est ajouté à l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> ; false si l'élément est déjà présent.</returns>
      <param name="item">Élément à ajouter à l'ensemble.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Clear">
      <summary>Supprime tous les éléments d'un objet <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Comparer">
      <summary>Obtient l'objet <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> qui est utilisé pour déterminer l'égalité des valeurs du jeu.</summary>
      <returns>Objet <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> qui est utilisé pour déterminer l'égalité des valeurs du jeu.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Contains(`0)">
      <summary>Détermine si un objet <see cref="T:System.Collections.Generic.HashSet`1" /> contient l'élément spécifié.</summary>
      <returns>true si l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> contient l'élément spécifié ; sinon, false.</returns>
      <param name="item">Élément à rechercher dans l'objet <see cref="T:System.Collections.Generic.HashSet`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[])">
      <summary>Copie les éléments d'un objet <see cref="T:System.Collections.Generic.HashSet`1" /> dans un tableau.</summary>
      <param name="array">Tableau unidimensionnel qui constitue la destination des éléments copiés à partir de l'objet <see cref="T:System.Collections.Generic.HashSet`1" />.Ce tableau doit avoir une indexation de base zéro.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32)">
      <summary>Copie les éléments d'un objet <see cref="T:System.Collections.Generic.HashSet`1" /> dans un tableau, en commençant à l'index de tableau spécifié.</summary>
      <param name="array">Tableau unidimensionnel qui constitue la destination des éléments copiés à partir de l'objet <see cref="T:System.Collections.Generic.HashSet`1" />.Ce tableau doit avoir une indexation de base zéro.</param>
      <param name="arrayIndex">Index de base zéro dans l'<paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arrayIndex" /> est supérieur à la longueur du <paramref name="array" /> de destination.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>Copie le nombre spécifié d'éléments d'un objet <see cref="T:System.Collections.Generic.HashSet`1" /> dans un tableau, en commençant à l'index de tableau spécifié.</summary>
      <param name="array">Tableau unidimensionnel qui constitue la destination des éléments copiés à partir de l'objet <see cref="T:System.Collections.Generic.HashSet`1" />.Ce tableau doit avoir une indexation de base zéro.</param>
      <param name="arrayIndex">Index de base zéro dans l'<paramref name="array" /> à partir duquel la copie commence.</param>
      <param name="count">Nombre d'éléments à copier dans l'élément <paramref name="array" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> est inférieur à 0.ou<paramref name="count" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arrayIndex" /> est supérieur à la longueur du <paramref name="array" /> de destination.ouLe nombre d'éléments dans le <paramref name="count" /> est supérieur à la quantité d'espace disponible entre <paramref name="index" /> et la fin du <paramref name="array" /> de destination.</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Count">
      <summary>Obtient le nombre d'éléments contenus dans un ensemble.</summary>
      <returns>Nombre d'éléments contenus dans l'ensemble.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Supprime tous les éléments dans la collection spécifiée de l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> actif.</summary>
      <param name="other">Collection d'éléments à supprimer de l'objet <see cref="T:System.Collections.Generic.HashSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein d'un objet <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <returns>Objet <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" /> pour l'objet <see cref="T:System.Collections.Generic.HashSet`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifie l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> actif afin qu'il contienne uniquement les éléments qui sont présents dans cet objet et dans la collection spécifiée.</summary>
      <param name="other">Collection à comparer à l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> actif.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Détermine si un objet <see cref="T:System.Collections.Generic.HashSet`1" /> appartient à la collection spécifiée.</summary>
      <returns>true si l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> appartient à l'élément <paramref name="other" /> ; sinon, false.</returns>
      <param name="other">Collection à comparer à l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> actif.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Détermine si un objet <see cref="T:System.Collections.Generic.HashSet`1" /> contient la collection spécifiée.</summary>
      <returns>true si l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> contient l'élément <paramref name="other" /> ; sinon, false.</returns>
      <param name="other">Collection à comparer à l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> actif. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Détermine si un objet <see cref="T:System.Collections.Generic.HashSet`1" /> est un sous-ensemble de la collection spécifiée.</summary>
      <returns>true si l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> est un sous-ensemble de l'élément <paramref name="other" /> ; sinon, false.</returns>
      <param name="other">Collection à comparer à l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> actif.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Détermine si un objet <see cref="T:System.Collections.Generic.HashSet`1" /> est un sur-ensemble de la collection spécifiée.</summary>
      <returns>true si l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> est un sur-ensemble de l'élément <paramref name="other" /> ; sinon, false.</returns>
      <param name="other">Collection à comparer à l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> actif.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>Détermine si l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> actif et une collection spécifiée partagent des éléments communs.</summary>
      <returns>true si l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> et l'élément <paramref name="other" /> partagent au moins un élément commun ; sinon, false.</returns>
      <param name="other">Collection à comparer à l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> actif.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Remove(`0)">
      <summary>Supprime l'élément spécifié d'un objet <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <returns>true si la recherche et la suppression de l'élément réussissent ; sinon, false.Cette méthode retourne false si <paramref name="item" /> est introuvable dans l'objet <see cref="T:System.Collections.Generic.HashSet`1" />.</returns>
      <param name="item">Élément à supprimer.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>Supprime de la collection <see cref="T:System.Collections.Generic.HashSet`1" /> tous les éléments qui respectent les conditions définies par le prédicat spécifié.</summary>
      <returns>Nombre d'éléments supprimés de la collection <see cref="T:System.Collections.Generic.HashSet`1" />.</returns>
      <param name="match">Délégué <see cref="T:System.Predicate`1" /> qui définit les conditions des éléments à supprimer.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>Détermine si un objet <see cref="T:System.Collections.Generic.HashSet`1" /> et la collection spécifiée contiennent les mêmes éléments.</summary>
      <returns>true si l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> est égal à l'élément <paramref name="other" /> ; sinon, false.</returns>
      <param name="other">Collection à comparer à l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> actif.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifie l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> actif afin qu'il contienne uniquement les éléments présents dans cet objet ou dans la collection spécifiée, mais pas dans les deux.</summary>
      <param name="other">Collection à comparer à l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> actif.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Ajoute un élément à un objet <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="item">Objet à ajouter à l'objet <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Collections.Generic.ICollection`1" /> est en lecture seule.</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtient une valeur indiquant si une collection est en lecture seule.</summary>
      <returns>true si la collection est en lecture seule ; sinon, false.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein d'une collection.</summary>
      <returns>Objet <see cref="T:System.Collections.Generic.IEnumerator`1" /> pouvant être utilisé pour itérer au sein de la collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein d'une collection.</summary>
      <returns>Objet <see cref="T:System.Collections.IEnumerator" /> pouvant être utilisé pour itérer au sein de la collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.TrimExcess">
      <summary>Affecte à la capacité d'un objet <see cref="T:System.Collections.Generic.HashSet`1" /> le nombre réel d'éléments qu'il contient, arrondi à une valeur proche spécifique à l'implémentation.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifie l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> actif afin qu'il contienne tous les éléments qui sont présents dans cet objet, dans la collection spécifiée ou dans les deux.</summary>
      <param name="other">Collection à comparer à l'objet <see cref="T:System.Collections.Generic.HashSet`1" /> actif.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1.Enumerator">
      <summary>Énumère les éléments d'un objet <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans la <see cref="T:System.Collections.Generic.HashSet`1" /> collection à la position actuelle de l'énumérateur.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.Dispose">
      <summary>Libère toutes les ressources utilisées par un objet <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.MoveNext">
      <summary>Avance l'énumérateur à l'élément suivant de la collection <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <returns>true si l'énumérateur a pu avancer jusqu'à l'élément suivant ; false si l'énumérateur a dépassé la fin de la collection.</returns>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans la collection à la position actuelle de l'énumérateur en tant que <see cref="T:System.Object" />.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Rétablit l'énumérateur à sa position initiale, qui précède le premier élément de la collection.</summary>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1">
      <summary>Représente une liste à lien double.</summary>
      <typeparam name="T">Spécifie le type d'élément de la liste liée.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.LinkedList`1" /> qui est vide.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.LinkedList`1" /> qui contient des éléments copiés à partir du <see cref="T:System.Collections.IEnumerable" /> spécifié et qui possède une capacité suffisante pour accepter le nombre d'éléments copiés. </summary>
      <param name="collection">
        <see cref="T:System.Collections.IEnumerable" /> dont les éléments sont copiés dans le nouveau <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>Ajoute le nouveau nœud spécifié après le nœud existant spécifié dans <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">
        <see cref="T:System.Collections.Generic.LinkedListNode`1" /> après lequel insérer <paramref name="newNode" />.</param>
      <param name="newNode">Le nouveau <see cref="T:System.Collections.Generic.LinkedListNode`1" /> à ajouter à <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> a la valeur null.ou<paramref name="newNode" /> a la valeur null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> n'est pas dans le <see cref="T:System.Collections.Generic.LinkedList`1" /> actuel.ou<paramref name="newNode" /> appartient à un autre <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>Ajoute un nouveau nœud qui contient la valeur spécifiée après le nœud existant spécifié dans <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Le nouveau <see cref="T:System.Collections.Generic.LinkedListNode`1" /> contenant <paramref name="value" />.</returns>
      <param name="node">
        <see cref="T:System.Collections.Generic.LinkedListNode`1" /> après lequel insérer un nouveau <see cref="T:System.Collections.Generic.LinkedListNode`1" /> qui contient <paramref name="value" />.</param>
      <param name="value">Valeur à ajouter à <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> a la valeur null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> n'est pas dans le <see cref="T:System.Collections.Generic.LinkedList`1" /> actuel.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>Ajoute le nouveau nœud spécifié avant le nœud existant spécifié dans <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">
        <see cref="T:System.Collections.Generic.LinkedListNode`1" /> avant lequel insérer <paramref name="newNode" />.</param>
      <param name="newNode">Le nouveau <see cref="T:System.Collections.Generic.LinkedListNode`1" /> à ajouter à <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> a la valeur null.ou<paramref name="newNode" /> a la valeur null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> n'est pas dans le <see cref="T:System.Collections.Generic.LinkedList`1" /> actuel.ou<paramref name="newNode" /> appartient à un autre <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>Ajoute un nouveau nœud qui contient la valeur spécifiée avant le nœud existant spécifié dans <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Le nouveau <see cref="T:System.Collections.Generic.LinkedListNode`1" /> contenant <paramref name="value" />.</returns>
      <param name="node">
        <see cref="T:System.Collections.Generic.LinkedListNode`1" /> avant lequel insérer un nouveau <see cref="T:System.Collections.Generic.LinkedListNode`1" /> qui contient <paramref name="value" />.</param>
      <param name="value">Valeur à ajouter à <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> a la valeur null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> n'est pas dans le <see cref="T:System.Collections.Generic.LinkedList`1" /> actuel.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(System.Collections.Generic.LinkedListNode{`0})">
      <summary>Ajoute le nouveau nœud spécifié au début de <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">Le nouveau <see cref="T:System.Collections.Generic.LinkedListNode`1" /> à ajouter au début de <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> a la valeur null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> appartient à une autre <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(`0)">
      <summary>Ajoute un nouveau nœud qui contient la valeur spécifiée au début de <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Le nouveau <see cref="T:System.Collections.Generic.LinkedListNode`1" /> contenant <paramref name="value" />.</returns>
      <param name="value">Valeur à ajouter au début de <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(System.Collections.Generic.LinkedListNode{`0})">
      <summary>Ajoute le nouveau nœud spécifié à la fin de <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">Le nouveau <see cref="T:System.Collections.Generic.LinkedListNode`1" /> à ajouter à la fin de <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> a la valeur null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> appartient à une autre <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(`0)">
      <summary>Ajoute un nouveau nœud qui contient la valeur spécifiée à la fin de <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Le nouveau <see cref="T:System.Collections.Generic.LinkedListNode`1" /> contenant <paramref name="value" />.</returns>
      <param name="value">Valeur à ajouter à la fin de <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Clear">
      <summary>Supprime tous les nœuds de <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Contains(`0)">
      <summary>Détermine si une valeur est dans <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>true si <paramref name="value" /> existe dans <see cref="T:System.Collections.Generic.LinkedList`1" /> ; sinon, false.</returns>
      <param name="value">Valeur à trouver dans <see cref="T:System.Collections.Generic.LinkedList`1" />.La valeur peut être null pour les types référence.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.CopyTo(`0[],System.Int32)">
      <summary>Copie l'ensemble de l'objet <see cref="T:System.Collections.Generic.LinkedList`1" /> vers un objet <see cref="T:System.Array" /> unidimensionnel compatible, en commençant à l'index spécifié du tableau cible.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensionnel qui constitue la destination des éléments copiés à partir de <see cref="T:System.Collections.Generic.LinkedList`1" />.<see cref="T:System.Array" /> doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans <paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à zéro.</exception>
      <exception cref="T:System.ArgumentException">Le nombre d'éléments dans le <see cref="T:System.Collections.Generic.LinkedList`1" /> source est supérieur à la quantité d'espace disponible entre <paramref name="index" /> et la fin du <paramref name="array" /> de destination.</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Count">
      <summary>Obtient le nombre de nœuds réellement contenus dans <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Nombre de nœuds réellement contenus dans <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Find(`0)">
      <summary>Recherche le premier nœud qui contient la valeur spécifiée.</summary>
      <returns>Le premier <see cref="T:System.Collections.Generic.LinkedListNode`1" /> qui contient la valeur spécifiée, s'il est trouvé ; sinon, null.</returns>
      <param name="value">Valeur à trouver dans <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.FindLast(`0)">
      <summary>Recherche le dernier nœud qui contient la valeur spécifiée.</summary>
      <returns>Le dernier <see cref="T:System.Collections.Generic.LinkedListNode`1" /> qui contient la valeur spécifiée, s'il est trouvé ; sinon, null.</returns>
      <param name="value">Valeur à trouver dans <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.First">
      <summary>Obtient le premier nœud de <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Le premier <see cref="T:System.Collections.Generic.LinkedListNode`1" /> de <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" /> pour <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Last">
      <summary>Obtient le dernier nœud de <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Le dernier <see cref="T:System.Collections.Generic.LinkedListNode`1" /> de <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(System.Collections.Generic.LinkedListNode{`0})">
      <summary>Supprime le nœud spécifié de <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">
        <see cref="T:System.Collections.Generic.LinkedListNode`1" /> à supprimer de <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> a la valeur null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> n'est pas dans le <see cref="T:System.Collections.Generic.LinkedList`1" /> actuel.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(`0)">
      <summary>Supprime la première occurrence de la valeur spécifiée dans <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>true si la suppression de l'élément contenant <paramref name="value" /> réussit ; sinon, false.  Cette méthode retourne également false si <paramref name="value" /> est introuvable dans le <see cref="T:System.Collections.Generic.LinkedList`1" /> d'origine.</returns>
      <param name="value">Valeur à supprimer de <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveFirst">
      <summary>Supprime le nœud au début de <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <exception cref="T:System.InvalidOperationException">L'objet <see cref="T:System.Collections.Generic.LinkedList`1" /> est vide.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveLast">
      <summary>Supprime le nœud à la fin de <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <exception cref="T:System.InvalidOperationException">L'objet <see cref="T:System.Collections.Generic.LinkedList`1" /> est vide.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Ajoute un élément à la fin de <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="value">Valeur à ajouter à la fin de <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtient une valeur indiquant si <see cref="T:System.Collections.Generic.ICollection`1" /> est en lecture seule.</summary>
      <returns>true si <see cref="T:System.Collections.Generic.ICollection`1" /> est en lecture seule ; sinon, false.  Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.LinkedList`1" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein d'une collection.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> qui peut être utilisé pour itérer au sein de la collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copie les éléments de <see cref="T:System.Collections.ICollection" /> dans <see cref="T:System.Array" />, en commençant à un index particulier de <see cref="T:System.Array" />.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensionnel qui constitue la destination des éléments copiés à partir d'<see cref="T:System.Collections.ICollection" />.<see cref="T:System.Array" /> doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans <paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à zéro.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> est multidimensionnel.ou<paramref name="array" /> n'a pas d'indexation de base zéro.ouLe nombre d'éléments dans le <see cref="T:System.Collections.ICollection" /> source est supérieur à la quantité d'espace disponible entre <paramref name="index" /> et la fin du <paramref name="array" /> de destination.ouLe cast automatique du type du <see cref="T:System.Collections.ICollection" /> source en type du <paramref name="array" /> de destination est impossible.</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#IsSynchronized">
      <summary>Obtient une valeur indiquant si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe).</summary>
      <returns>true si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe) ; sinon false.  Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.LinkedList`1" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#SyncRoot">
      <summary>Obtient un objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.  Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.LinkedList`1" />, cette propriété retourne toujours l'instance actuelle.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de la liste liée en tant que collection.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> pouvant être utilisé pour itérer au sein de la liste liée en tant que collection.</returns>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1.Enumerator">
      <summary>Énumère les éléments de <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans <see cref="T:System.Collections.Generic.LinkedList`1" /> à la position actuelle de l'énumérateur.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.Dispose">
      <summary>Libère toutes les ressources utilisées par <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.MoveNext">
      <summary>Avance l'énumérateur à l'élément suivant de <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>true si l'énumérateur a pu avancer jusqu'à l'élément suivant ; false si l'énumérateur a dépassé la fin de la collection.</returns>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans la collection à la position actuelle de l'énumérateur.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Rétablit l'énumérateur à sa position initiale, qui précède le premier élément de la collection.Cette classe ne peut pas être héritée.</summary>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedListNode`1">
      <summary>Représente un nœud dans un <see cref="T:System.Collections.Generic.LinkedList`1" />.Cette classe ne peut pas être héritée.</summary>
      <typeparam name="T">Spécifie le type d'élément de la liste liée.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedListNode`1.#ctor(`0)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.LinkedListNode`1" /> comportant la valeur spécifiée.</summary>
      <param name="value">Valeur à contenir dans <see cref="T:System.Collections.Generic.LinkedListNode`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.List">
      <summary>Obtient <see cref="T:System.Collections.Generic.LinkedList`1" /> auquel appartient <see cref="T:System.Collections.Generic.LinkedListNode`1" />.</summary>
      <returns>Référence à <see cref="T:System.Collections.Generic.LinkedList`1" /> à laquelle appartient <see cref="T:System.Collections.Generic.LinkedListNode`1" />, ou null si <see cref="T:System.Collections.Generic.LinkedListNode`1" /> n'est pas lié.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Next">
      <summary>Obtient le nœud suivant dans <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Référence au nœud suivant dans <see cref="T:System.Collections.Generic.LinkedList`1" />, ou null si le nœud actuel est le dernier élément (<see cref="P:System.Collections.Generic.LinkedList`1.Last" />) de <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Previous">
      <summary>Obtient le nœud précédent dans <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Référence au nœud précédent dans <see cref="T:System.Collections.Generic.LinkedList`1" />, ou null si le nœud actuel est le premier élément (<see cref="P:System.Collections.Generic.LinkedList`1.First" />) de <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Value">
      <summary>Obtient la valeur contenue dans le nœud.</summary>
      <returns>Valeur contenue dans le nœud.</returns>
    </member>
    <member name="T:System.Collections.Generic.List`1">
      <summary>Représente une liste fortement typée d'objets accessibles par index.Fournit des méthodes de recherche, de tri et de manipulation de listes.Pour parcourir le code source de .NET Framework pour ce type, consultez la Source de référence.</summary>
      <typeparam name="T">Type d'éléments de la liste.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.List`1" /> qui est vide et possède la capacité initiale par défaut.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.List`1" /> qui contient des éléments copiés à partir de la collection spécifiée et qui possède une capacité suffisante pour accepter le nombre d'éléments copiés.</summary>
      <param name="collection">Collection dont les éléments sont copiés dans la nouvelle liste.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.List`1" /> qui est vide et a la capacité initiale spécifiée.</summary>
      <param name="capacity">Nombre d'éléments que la nouvelle liste peut initialement stocker.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> est inférieur à 0. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Add(`0)">
      <summary>Ajoute un objet à la fin de <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="item">Objet à ajouter à la fin de <see cref="T:System.Collections.Generic.List`1" />.La valeur peut être null pour les types référence.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Ajoute les éléments de la collection spécifiée à la fin du <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="collection">Collection dont les éléments devraient être ajoutés à la fin de <see cref="T:System.Collections.Generic.List`1" />.La collection elle-même ne peut pas être null, mais elle peut contenir des éléments qui sont null, si le type <paramref name="T" /> est un type référence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.AsReadOnly">
      <summary>Retourne un wrapper <see cref="T:System.Collections.Generic.IList`1" /> en lecture seule pour la collection actuelle.</summary>
      <returns>
        <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> qui agit comme un wrapper en lecture seule autour du <see cref="T:System.Collections.Generic.List`1" /> actuel.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(System.Int32,System.Int32,`0,System.Collections.Generic.IComparer{`0})">
      <summary>Recherche un élément utilisant le comparateur spécifié dans une plage d'éléments du <see cref="T:System.Collections.Generic.List`1" /> trié et retourne l'index de base zéro de l'élément.</summary>
      <returns>Index de base zéro de <paramref name="item" /> dans le <see cref="T:System.Collections.Generic.List`1" /> trié, si <paramref name="item" /> existe ; sinon, un nombre négatif qui est le complément de bits de l'index de l'élément suivant supérieur à <paramref name="item" /> ou, s'il n'existe aucun élément supérieur, le complément de bits de <see cref="P:System.Collections.Generic.List`1.Count" />.</returns>
      <param name="index">Index de début de base zéro de la plage dans laquelle effectuer la recherche.</param>
      <param name="count">Longueur de la plage dans laquelle effectuer la recherche.</param>
      <param name="item">Objet à trouver.La valeur peut être null pour les types référence.</param>
      <param name="comparer">Implémentation de <see cref="T:System.Collections.Generic.IComparer`1" /> à utiliser lors de la comparaison d'éléments, ou null pour utiliser le comparateur par défaut <see cref="P:System.Collections.Generic.Comparer`1.Default" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.ou<paramref name="count" /> est inférieur à 0. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> et <paramref name="count" /> ne désignent pas une plage valide dans <see cref="T:System.Collections.Generic.List`1" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> est null, et le comparateur par défaut <see cref="P:System.Collections.Generic.Comparer`1.Default" /> ne peut pas trouver une implémentation de l'interface générique <see cref="T:System.IComparable`1" /> ou de l'interface <see cref="T:System.IComparable" /> pour le type <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0)">
      <summary>Recherche un élément utilisant le comparateur par défaut dans le <see cref="T:System.Collections.Generic.List`1" /> entièrement trié et retourne l'index de base zéro de l'élément.</summary>
      <returns>Index de base zéro de <paramref name="item" /> dans le <see cref="T:System.Collections.Generic.List`1" /> trié, si <paramref name="item" /> existe ; sinon, un nombre négatif qui est le complément de bits de l'index de l'élément suivant supérieur à <paramref name="item" /> ou, s'il n'existe aucun élément supérieur, le complément de bits de <see cref="P:System.Collections.Generic.List`1.Count" />.</returns>
      <param name="item">Objet à trouver.La valeur peut être null pour les types référence.</param>
      <exception cref="T:System.InvalidOperationException">Le comparateur par défaut <see cref="P:System.Collections.Generic.Comparer`1.Default" /> ne peut pas trouver une implémentation de l'interface générique <see cref="T:System.IComparable`1" /> ou de l'interface <see cref="T:System.IComparable" /> pour le type <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0,System.Collections.Generic.IComparer{`0})">
      <summary>Recherche un élément utilisant le comparateur spécifié dans le <see cref="T:System.Collections.Generic.List`1" /> entièrement trié et retourne l'index de base zéro de l'élément.</summary>
      <returns>Index de base zéro de <paramref name="item" /> dans le <see cref="T:System.Collections.Generic.List`1" /> trié, si <paramref name="item" /> existe ; sinon, un nombre négatif qui est le complément de bits de l'index de l'élément suivant supérieur à <paramref name="item" /> ou, s'il n'existe aucun élément supérieur, le complément de bits de <see cref="P:System.Collections.Generic.List`1.Count" />.</returns>
      <param name="item">Objet à trouver.La valeur peut être null pour les types référence.</param>
      <param name="comparer">Implémentation de <see cref="T:System.Collections.Generic.IComparer`1" /> à utiliser lors de la comparaison d'éléments.ounull pour utiliser le comparateur par défaut <see cref="P:System.Collections.Generic.Comparer`1.Default" />.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> est null, et le comparateur par défaut <see cref="P:System.Collections.Generic.Comparer`1.Default" /> ne peut pas trouver une implémentation de l'interface générique <see cref="T:System.IComparable`1" /> ou de l'interface <see cref="T:System.IComparable" /> pour le type <paramref name="T" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Capacity">
      <summary>Obtient ou définit le nombre total des éléments que la structure de données interne peut contenir sans redimensionnement.</summary>
      <returns>Nombre d'éléments que <see cref="T:System.Collections.Generic.List`1" /> peut contenir avant que le redimensionnement ne soit requis.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Une valeur inférieure à <see cref="P:System.Collections.Generic.List`1.Count" /> est affectée à <see cref="P:System.Collections.Generic.List`1.Capacity" />. </exception>
      <exception cref="T:System.OutOfMemoryException">La mémoire disponible sur le système est insuffisante.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Clear">
      <summary>Supprime tous les éléments de <see cref="T:System.Collections.Generic.List`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Contains(`0)">
      <summary>Détermine si un élément est dans <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>true si <paramref name="item" /> existe dans <see cref="T:System.Collections.Generic.List`1" /> ; sinon, false.</returns>
      <param name="item">Objet à trouver dans <see cref="T:System.Collections.Generic.List`1" />.La valeur peut être null pour les types référence.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(System.Int32,`0[],System.Int32,System.Int32)">
      <summary>Copie la plage d'éléments de <see cref="T:System.Collections.Generic.List`1" /> dans un tableau compatible unidimensionnel en commençant à l'index spécifié du tableau cible.</summary>
      <param name="index">Index de base zéro dans le <see cref="T:System.Collections.Generic.List`1" /> source, à partir duquel la copie commence.</param>
      <param name="array">
        <see cref="T:System.Array" /> unidimensionnel qui constitue la destination des éléments copiés à partir d'<see cref="T:System.Collections.Generic.List`1" />.<see cref="T:System.Array" /> doit avoir une indexation de base zéro.</param>
      <param name="arrayIndex">Index de base zéro dans le <paramref name="array" /> à partir duquel la copie commence.</param>
      <param name="count">Nombre d'éléments à copier.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.ou<paramref name="arrayIndex" /> est inférieur à 0.ou<paramref name="count" /> est inférieur à 0. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> est supérieur ou égal au <see cref="P:System.Collections.Generic.List`1.Count" /> du <see cref="T:System.Collections.Generic.List`1" /> source.ouLe nombre d'éléments de <paramref name="index" /> jusqu'à la fin du <see cref="T:System.Collections.Generic.List`1" /> source est supérieur à la quantité d'espace disponible entre <paramref name="arrayIndex" /> et la fin du <paramref name="array" /> de destination. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[])">
      <summary>Copie l'ensemble du <see cref="T:System.Collections.Generic.List`1" /> dans un tableau compatible unidimensionnel en commençant au début du tableau cible.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensionnel qui constitue la destination des éléments copiés à partir d'<see cref="T:System.Collections.Generic.List`1" />.<see cref="T:System.Array" /> doit avoir une indexation de base zéro.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Le nombre d'éléments dans le <see cref="T:System.Collections.Generic.List`1" /> source est supérieur au nombre d'éléments que le <paramref name="array" /> de destination peut contenir.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[],System.Int32)">
      <summary>Copie l'ensemble du <see cref="T:System.Collections.Generic.List`1" /> vers un tableau compatible unidimensionnel, en commençant à l'index spécifié du tableau cible.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensionnel qui constitue la destination des éléments copiés à partir d'<see cref="T:System.Collections.Generic.List`1" />.<see cref="T:System.Array" /> doit avoir une indexation de base zéro.</param>
      <param name="arrayIndex">Index de base zéro dans le <paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">Le nombre d'éléments dans le <see cref="T:System.Collections.Generic.List`1" /> source est supérieur à la quantité d'espace disponible entre <paramref name="arrayIndex" /> et la fin du <paramref name="array" /> de destination.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Count">
      <summary>Obtient le nombre d'éléments contenus dans le <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Nombre d'éléments contenus dans <see cref="T:System.Collections.Generic.List`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Exists(System.Predicate{`0})">
      <summary>Détermine si le <see cref="T:System.Collections.Generic.List`1" /> contient des éléments qui correspondent aux conditions définies par le prédicat spécifié.</summary>
      <returns>true si le <see cref="T:System.Collections.Generic.List`1" /> contient un ou plusieurs éléments qui correspondent aux conditions définies par le prédicat spécifié ; sinon, false.</returns>
      <param name="match">Délégué <see cref="T:System.Predicate`1" /> qui définit les conditions des éléments à rechercher.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Find(System.Predicate{`0})">
      <summary>Recherche un élément qui correspond aux conditions définies par le prédicat spécifié et retourne la première occurrence dans le <see cref="T:System.Collections.Generic.List`1" /> entier.</summary>
      <returns>Premier élément qui correspond aux conditions définies par le prédicat spécifié, s'il est trouvé ; sinon, la valeur par défaut pour le type <paramref name="T" />.</returns>
      <param name="match">Délégué <see cref="T:System.Predicate`1" /> qui définit les conditions de l'élément à rechercher.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindAll(System.Predicate{`0})">
      <summary>Récupère tous les éléments qui correspondent aux conditions définies par le prédicat spécifié.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" /> contenant tous les éléments qui correspondent aux conditions définies par le prédicat spécifié, si une correspondance est trouvée ; sinon, un <see cref="T:System.Collections.Generic.List`1" /> vide.</returns>
      <param name="match">Délégué <see cref="T:System.Predicate`1" /> qui définit les conditions des éléments à rechercher.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>Recherche un élément qui correspond aux conditions définies par le prédicat spécifié et retourne l'index de base zéro de la première occurrence dans la plage d'éléments du <see cref="T:System.Collections.Generic.List`1" /> qui commence à l'index spécifié et contient le nombre d'éléments spécifié.</summary>
      <returns>Index de base zéro de la première occurrence d'un élément répondant aux conditions définies par <paramref name="match" />, si cette occurrence est trouvée ; sinon, -1.</returns>
      <param name="startIndex">Index de début de base zéro de la recherche.</param>
      <param name="count">Nombre d'éléments contenus dans la section où la recherche doit être effectuée.</param>
      <param name="match">Délégué <see cref="T:System.Predicate`1" /> qui définit les conditions de l'élément à rechercher.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> est en dehors de la plage d'index valide pour <see cref="T:System.Collections.Generic.List`1" />.ou<paramref name="count" /> est inférieur à 0.ou<paramref name="startIndex" /> et <paramref name="count" /> ne spécifient pas une section valide dans <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Predicate{`0})">
      <summary>Recherche un élément qui correspond aux conditions définies par le prédicat spécifié et retourne l'index de base zéro de la première occurrence dans la plage d'éléments du <see cref="T:System.Collections.Generic.List`1" /> qui s'étend de l'index spécifié au dernier élément.</summary>
      <returns>Index de base zéro de la première occurrence d'un élément répondant aux conditions définies par <paramref name="match" />, si cette occurrence est trouvée ; sinon, -1.</returns>
      <param name="startIndex">Index de début de base zéro de la recherche.</param>
      <param name="match">Délégué <see cref="T:System.Predicate`1" /> qui définit les conditions de l'élément à rechercher.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> est en dehors de la plage d'index valide pour <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Predicate{`0})">
      <summary>Recherche un élément qui correspond aux conditions définies par le prédicat spécifié, et retourne l'index de base zéro de la première occurrence dans le <see cref="T:System.Collections.Generic.List`1" /> entier.</summary>
      <returns>Index de base zéro de la première occurrence d'un élément répondant aux conditions définies par <paramref name="match" />, si cette occurrence est trouvée ; sinon, -1.</returns>
      <param name="match">Délégué <see cref="T:System.Predicate`1" /> qui définit les conditions de l'élément à rechercher.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLast(System.Predicate{`0})">
      <summary>Recherche un élément qui correspond aux conditions définies par le prédicat spécifié et retourne la dernière occurrence dans le <see cref="T:System.Collections.Generic.List`1" /> entier.</summary>
      <returns>Dernier élément qui correspond aux conditions définies par le prédicat spécifié, s'il est trouvé ; sinon, la valeur par défaut pour le type <paramref name="T" />.</returns>
      <param name="match">Délégué <see cref="T:System.Predicate`1" /> qui définit les conditions de l'élément à rechercher.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>Recherche un élément qui correspond aux conditions définies par le prédicat spécifié et retourne l'index de base zéro de la dernière occurrence dans la plage d'éléments du <see cref="T:System.Collections.Generic.List`1" /> qui contient le nombre d'éléments spécifié et se termine à l'index spécifié.</summary>
      <returns>Index de base zéro de la dernière occurrence d'un élément répondant aux conditions définies par <paramref name="match" />, si cette occurrence est trouvée ; sinon, -1.</returns>
      <param name="startIndex">Index de début de base zéro de la recherche vers le haut.</param>
      <param name="count">Nombre d'éléments contenus dans la section où la recherche doit être effectuée.</param>
      <param name="match">Délégué <see cref="T:System.Predicate`1" /> qui définit les conditions de l'élément à rechercher.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> est en dehors de la plage d'index valide pour <see cref="T:System.Collections.Generic.List`1" />.ou<paramref name="count" /> est inférieur à 0.ou<paramref name="startIndex" /> et <paramref name="count" /> ne spécifient pas une section valide dans <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Predicate{`0})">
      <summary>Recherche un élément qui correspond aux conditions définies par le prédicat spécifié et retourne l'index de base zéro de la dernière occurrence dans la plage d'éléments du <see cref="T:System.Collections.Generic.List`1" /> qui s'étend du premier élément à l'index spécifié.</summary>
      <returns>Index de base zéro de la dernière occurrence d'un élément répondant aux conditions définies par <paramref name="match" />, si cette occurrence est trouvée ; sinon, -1.</returns>
      <param name="startIndex">Index de début de base zéro de la recherche vers le haut.</param>
      <param name="match">Délégué <see cref="T:System.Predicate`1" /> qui définit les conditions de l'élément à rechercher.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> est en dehors de la plage d'index valide pour <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Predicate{`0})">
      <summary>Recherche un élément qui correspond aux conditions définies par le prédicat spécifié, et retourne l'index de base zéro de la dernière occurrence dans le <see cref="T:System.Collections.Generic.List`1" /> entier.</summary>
      <returns>Index de base zéro de la dernière occurrence d'un élément répondant aux conditions définies par <paramref name="match" />, si cette occurrence est trouvée ; sinon, -1.</returns>
      <param name="match">Délégué <see cref="T:System.Predicate`1" /> qui définit les conditions de l'élément à rechercher.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ForEach(System.Action{`0})">
      <summary>Exécute l'action spécifiée sur chaque élément du <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="action">Délégué <see cref="T:System.Action`1" /> à exécuter sur chaque élément du <see cref="T:System.Collections.Generic.List`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1.Enumerator" /> pour <see cref="T:System.Collections.Generic.List`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetRange(System.Int32,System.Int32)">
      <summary>Crée une copie superficielle d'une plage d'éléments dans la source <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Copie superficielle d'une plage d'éléments dans la source <see cref="T:System.Collections.Generic.List`1" />.</returns>
      <param name="index">Index de base zéro <see cref="T:System.Collections.Generic.List`1" /> auquel la plage commence.</param>
      <param name="count">Nombre d'éléments dans la plage.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.ou<paramref name="count" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> et <paramref name="count" /> ne désignent pas une plage valide d'éléments dans <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0)">
      <summary>Recherche l'objet spécifié et retourne l'index de base zéro de la première occurrence dans l'ensemble du <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Index de base zéro de la première occurrence de <paramref name="item" /> dans l'ensemble du <see cref="T:System.Collections.Generic.List`1" />, s'il existe ; sinon, -1.</returns>
      <param name="item">Objet à trouver dans <see cref="T:System.Collections.Generic.List`1" />.La valeur peut être null pour les types référence.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32)">
      <summary>Recherche l'objet spécifié et retourne l'index de base zéro de la première occurrence dans la plage d'éléments du <see cref="T:System.Collections.Generic.List`1" /> qui s'étend de l'index spécifié au dernier élément.</summary>
      <returns>Index de base zéro de la première occurrence de <paramref name="item" /> dans la plage d'éléments du <see cref="T:System.Collections.Generic.List`1" /> qui s'étend de <paramref name="index" /> au dernier élément, s'il existe ; sinon, -1.</returns>
      <param name="item">Objet à trouver dans <see cref="T:System.Collections.Generic.List`1" />.La valeur peut être null pour les types référence.</param>
      <param name="index">Index de début de base zéro de la recherche.0 (zéro) est valide dans une liste vide.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est en dehors de la plage d'index valide pour <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32,System.Int32)">
      <summary>Recherche l'objet spécifié et retourne l'index de base zéro de la première occurrence dans la plage d'éléments du <see cref="T:System.Collections.Generic.List`1" /> qui commence à l'index spécifié et qui contient le nombre d'éléments spécifié.</summary>
      <returns>Index de base zéro de la première occurrence de <paramref name="item" /> dans la plage d'éléments du <see cref="T:System.Collections.Generic.List`1" /> qui commence à <paramref name="index" /> et qui contient le nombre d'éléments <paramref name="count" />, s'il existe ; sinon, -1.</returns>
      <param name="item">Objet à trouver dans <see cref="T:System.Collections.Generic.List`1" />.La valeur peut être null pour les types référence.</param>
      <param name="index">Index de début de base zéro de la recherche.0 (zéro) est valide dans une liste vide.</param>
      <param name="count">Nombre d'éléments contenus dans la section où la recherche doit être effectuée.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est en dehors de la plage d'index valide pour <see cref="T:System.Collections.Generic.List`1" />.ou<paramref name="count" /> est inférieur à 0.ou<paramref name="index" /> et <paramref name="count" /> ne spécifient pas une section valide dans <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Insert(System.Int32,`0)">
      <summary>Insère un élément dans <see cref="T:System.Collections.Generic.List`1" /> à l'index spécifié.</summary>
      <param name="index">Index de base zéro auquel <paramref name="item" /> doit être inséré.</param>
      <param name="item">Objet à insérer.La valeur peut être null pour les types référence.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.ou<paramref name="index" /> est supérieur à <see cref="P:System.Collections.Generic.List`1.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
      <summary>Insère les éléments d'une collection <see cref="T:System.Collections.Generic.List`1" /> à l'index spécifié.</summary>
      <param name="index">Index de base zéro au niveau duquel les nouveaux éléments devraient être insérés.</param>
      <param name="collection">Collection dont les éléments devraient être insérés dans <see cref="T:System.Collections.Generic.List`1" />.La collection elle-même ne peut pas être null, mais elle peut contenir des éléments qui sont null, si le type <paramref name="T" /> est un type référence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.ou<paramref name="index" /> est supérieur à <see cref="P:System.Collections.Generic.List`1.Count" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Item(System.Int32)">
      <summary>Obtient ou définit l'élément au niveau de l'index spécifié.</summary>
      <returns>Élément à l'index spécifié.</returns>
      <param name="index">Index de base zéro de l'élément à obtenir ou à définir.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.ou<paramref name="index" /> est supérieur ou égal à <see cref="P:System.Collections.Generic.List`1.Count" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0)">
      <summary>Recherche l'objet spécifié et retourne l'index de base zéro de la dernière occurrence dans l'ensemble du <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Index de base zéro de la dernière occurrence de <paramref name="item" /> dans l'ensemble du <see cref="T:System.Collections.Generic.List`1" />, s'il existe ; sinon, -1.</returns>
      <param name="item">Objet à trouver dans <see cref="T:System.Collections.Generic.List`1" />.La valeur peut être null pour les types référence.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32)">
      <summary>Recherche l'objet spécifié et retourne l'index de base zéro de la dernière occurrence dans la plage d'éléments du <see cref="T:System.Collections.Generic.List`1" /> qui s'étend du premier élément à l'index spécifié.</summary>
      <returns>Index de base zéro de la dernière occurrence de <paramref name="item" /> dans la plage d'éléments du <see cref="T:System.Collections.Generic.List`1" /> qui s'étend du premier élément à <paramref name="index" />, s'il existe ; sinon, -1.</returns>
      <param name="item">Objet à trouver dans <see cref="T:System.Collections.Generic.List`1" />.La valeur peut être null pour les types référence.</param>
      <param name="index">Index de début de base zéro de la recherche vers le haut.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est en dehors de la plage d'index valide pour <see cref="T:System.Collections.Generic.List`1" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32,System.Int32)">
      <summary>Recherche l'objet spécifié et retourne l'index de base zéro de la dernière occurrence dans la plage d'éléments du <see cref="T:System.Collections.Generic.List`1" /> qui contient le nombre spécifié d'éléments et se termine à l'index spécifié.</summary>
      <returns>Index de base zéro de la dernière occurrence de <paramref name="item" /> dans la plage d'éléments du <see cref="T:System.Collections.Generic.List`1" /> qui contient le nombre d'éléments <paramref name="count" /> et qui se termine à <paramref name="index" />, s'il existe ; sinon, -1.</returns>
      <param name="item">Objet à trouver dans <see cref="T:System.Collections.Generic.List`1" />.La valeur peut être null pour les types référence.</param>
      <param name="index">Index de début de base zéro de la recherche vers le haut.</param>
      <param name="count">Nombre d'éléments contenus dans la section où la recherche doit être effectuée.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est en dehors de la plage d'index valide pour <see cref="T:System.Collections.Generic.List`1" />.ou<paramref name="count" /> est inférieur à 0.ou<paramref name="index" /> et <paramref name="count" /> ne spécifient pas une section valide dans <see cref="T:System.Collections.Generic.List`1" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Remove(`0)">
      <summary>Supprime la première occurrence d'un objet spécifique de <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>true si la suppression de <paramref name="item" /> est réussie ; sinon, false.Cette méthode retourne également false si <paramref name="item" /> est introuvable dans <see cref="T:System.Collections.Generic.List`1" />.</returns>
      <param name="item">Objet à supprimer de <see cref="T:System.Collections.Generic.List`1" />.La valeur peut être null pour les types référence.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAll(System.Predicate{`0})">
      <summary>Supprime tous les éléments qui correspondent aux conditions définies par le prédicat spécifié.</summary>
      <returns>Nombre d'éléments supprimés du <see cref="T:System.Collections.Generic.List`1" />.</returns>
      <param name="match">Délégué <see cref="T:System.Predicate`1" /> qui définit les conditions des éléments à supprimer.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAt(System.Int32)">
      <summary>Supprime l'élément au niveau de l'index spécifié de <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="index">Index de base zéro de l'élément à supprimer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.ou<paramref name="index" /> est supérieur ou égal à <see cref="P:System.Collections.Generic.List`1.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveRange(System.Int32,System.Int32)">
      <summary>Supprime une plage d'éléments de <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="index">Index de début de base zéro de la plage d'éléments à supprimer.</param>
      <param name="count">Nombre d'éléments à supprimer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.ou<paramref name="count" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> et <paramref name="count" /> ne désignent pas une plage valide d'éléments dans <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse">
      <summary>Inverse l'ordre des éléments dans l'intégralité de <see cref="T:System.Collections.Generic.List`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse(System.Int32,System.Int32)">
      <summary>Inverse l'ordre des éléments dans la plage spécifiée.</summary>
      <param name="index">Index de début de base zéro de la plage à inverser.</param>
      <param name="count">Nombre d'éléments à inverser dans la plage.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.ou<paramref name="count" /> est inférieur à 0. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> et <paramref name="count" /> ne désignent pas une plage valide d'éléments dans <see cref="T:System.Collections.Generic.List`1" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort">
      <summary>Trie les éléments dans l'intégralité de <see cref="T:System.Collections.Generic.List`1" /> à l'aide du comparateur par défaut.</summary>
      <exception cref="T:System.InvalidOperationException">Le comparateur par défaut <see cref="P:System.Collections.Generic.Comparer`1.Default" /> ne peut pas trouver une implémentation de l'interface générique <see cref="T:System.IComparable`1" /> ou de l'interface <see cref="T:System.IComparable" /> pour le type <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Collections.Generic.IComparer{`0})">
      <summary>Trie les éléments dans l'intégralité de <see cref="T:System.Collections.Generic.List`1" /> à l'aide du comparateur spécifié.</summary>
      <param name="comparer">Implémentation de <see cref="T:System.Collections.Generic.IComparer`1" /> à utiliser lors de la comparaison d'éléments, ou null pour utiliser le comparateur par défaut <see cref="P:System.Collections.Generic.Comparer`1.Default" />.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> est null, et le comparateur par défaut <see cref="P:System.Collections.Generic.Comparer`1.Default" /> ne peut pas trouver une implémentation de l'interface générique <see cref="T:System.IComparable`1" /> ou de l'interface <see cref="T:System.IComparable" /> pour le type <paramref name="T" />.</exception>
      <exception cref="T:System.ArgumentException">L'implémentation de <paramref name="comparer" /> a provoqué une erreur pendant le tri.Par exemple, <paramref name="comparer" /> peut ne pas retourner 0 lors de la comparaison d'un élément à lui-même.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Comparison{`0})">
      <summary>Trie les éléments dans l'intégralité de <see cref="T:System.Collections.Generic.List`1" /> à l'aide du <see cref="T:System.Comparison`1" /> spécifié.</summary>
      <param name="comparison">
        <see cref="T:System.Comparison`1" /> à utiliser lors de la comparaison d'éléments.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparison" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">L'implémentation de <paramref name="comparison" /> a provoqué une erreur pendant le tri.Par exemple, <paramref name="comparison" /> peut ne pas retourner 0 lors de la comparaison d'un élément à lui-même.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Int32,System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>Trie les éléments dans une plage d'éléments de <see cref="T:System.Collections.Generic.List`1" /> à l'aide du comparateur spécifié.</summary>
      <param name="index">Index de début de base zéro de la plage à trier.</param>
      <param name="count">Longueur de la plage à trier.</param>
      <param name="comparer">Implémentation de <see cref="T:System.Collections.Generic.IComparer`1" /> à utiliser lors de la comparaison d'éléments, ou null pour utiliser le comparateur par défaut <see cref="P:System.Collections.Generic.Comparer`1.Default" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.ou<paramref name="count" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> et <paramref name="count" /> ne spécifient pas une plage valide dans <see cref="T:System.Collections.Generic.List`1" />.ouL'implémentation de <paramref name="comparer" /> a provoqué une erreur pendant le tri.Par exemple, <paramref name="comparer" /> peut ne pas retourner 0 lors de la comparaison d'un élément à lui-même.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> est null, et le comparateur par défaut <see cref="P:System.Collections.Generic.Comparer`1.Default" /> ne peut pas trouver une implémentation de l'interface générique <see cref="T:System.IComparable`1" /> ou de l'interface <see cref="T:System.IComparable" /> pour le type <paramref name="T" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtient une valeur indiquant si <see cref="T:System.Collections.Generic.ICollection`1" /> est en lecture seule.</summary>
      <returns>true si <see cref="T:System.Collections.Generic.ICollection`1" /> est en lecture seule ; sinon, false.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.List`1" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein d'une collection.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> qui peut être utilisé pour itérer la collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copie les éléments de <see cref="T:System.Collections.ICollection" /> dans <see cref="T:System.Array" />, à partir d'un index particulier de <see cref="T:System.Array" />.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensionnel qui constitue la destination des éléments copiés à partir d'<see cref="T:System.Collections.ICollection" />.<see cref="T:System.Array" /> doit avoir une indexation de base zéro.</param>
      <param name="arrayIndex">Index de base zéro dans le <paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> est multidimensionnel.ou<paramref name="array" /> n'a pas d'indexation de base zéro.ouLe nombre d'éléments dans le <see cref="T:System.Collections.ICollection" /> source est supérieur à la quantité d'espace disponible entre <paramref name="arrayIndex" /> et la fin du <paramref name="array" /> de destination.ouLe cast automatique du type du <see cref="T:System.Collections.ICollection" /> source en type du <paramref name="array" /> de destination est impossible.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#IsSynchronized">
      <summary>Obtient une valeur indiquant si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe).</summary>
      <returns>true si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe) ; sinon false.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.List`1" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#SyncRoot">
      <summary>Obtient un objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.List`1" />, cette propriété retourne toujours l'instance actuelle.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein d'une collection.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> qui peut être utilisé pour itérer la collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Add(System.Object)">
      <summary>Ajoute un élément à <see cref="T:System.Collections.IList" />.</summary>
      <returns>Position à laquelle le nouvel élément est inséré.</returns>
      <param name="item">
        <see cref="T:System.Object" /> à ajouter dans <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> a un type qui ne peut pas être assigné à <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Contains(System.Object)">
      <summary>Détermine si <see cref="T:System.Collections.IList" /> contient une valeur spécifique.</summary>
      <returns>true si <paramref name="item" /> existe dans <see cref="T:System.Collections.IList" /> ; sinon, false.</returns>
      <param name="item">
        <see cref="T:System.Object" /> à rechercher dans <see cref="T:System.Collections.IList" />.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#IndexOf(System.Object)">
      <summary>Détermine l'index d'un élément spécifique de <see cref="T:System.Collections.IList" />.</summary>
      <returns>Index de <paramref name="item" /> s'il figure dans la liste ; sinon, -1.</returns>
      <param name="item">Objet à trouver dans <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> a un type qui ne peut pas être assigné à <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>Insère un élément dans la <see cref="T:System.Collections.IList" /> au niveau de l'index spécifié.</summary>
      <param name="index">Index de base zéro auquel <paramref name="item" /> doit être inséré.</param>
      <param name="item">Objet à insérer dans <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> n'est pas un index valide dans <see cref="T:System.Collections.IList" />. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> a un type qui ne peut pas être assigné à <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsFixedSize">
      <summary>Obtient une valeur indiquant si <see cref="T:System.Collections.IList" /> est de taille fixe.</summary>
      <returns>true si <see cref="T:System.Collections.IList" /> est de taille fixe ; sinon, false.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.List`1" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsReadOnly">
      <summary>Obtient une valeur indiquant si <see cref="T:System.Collections.IList" /> est en lecture seule.</summary>
      <returns>true si <see cref="T:System.Collections.IList" /> est en lecture seule ; sinon, false.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.List`1" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#Item(System.Int32)">
      <summary>Obtient ou définit l'élément au niveau de l'index spécifié.</summary>
      <returns>Élément à l'index spécifié.</returns>
      <param name="index">Index de base zéro de l'élément à obtenir ou à définir.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> n'est pas un index valide dans <see cref="T:System.Collections.IList" />.</exception>
      <exception cref="T:System.ArgumentException">La propriété est définie et <paramref name="value" /> a un type qui ne peut pas être assigné à <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Remove(System.Object)">
      <summary>Supprime la première occurrence d'un objet spécifique de <see cref="T:System.Collections.IList" />.</summary>
      <param name="item">Objet à supprimer de <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> a un type qui ne peut pas être assigné à <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ToArray">
      <summary>Copie les éléments de <see cref="T:System.Collections.Generic.List`1" /> vers un nouveau tableau.</summary>
      <returns>Tableau contenant les copies des éléments du <see cref="T:System.Collections.Generic.List`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrimExcess">
      <summary>Affecte à la capacité le nombre réel d'éléments dans <see cref="T:System.Collections.Generic.List`1" />, si ce nombre est inférieur à une valeur de seuil.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrueForAll(System.Predicate{`0})">
      <summary>Détermine si chaque élément de <see cref="T:System.Collections.Generic.List`1" /> correspond aux conditions définies par le prédicat spécifié.</summary>
      <returns>true si chaque élément de <see cref="T:System.Collections.Generic.List`1" /> correspond aux conditions définies par le prédicat spécifié ; sinon, false.Si la liste ne comporte pas d'éléments, la valeur de retour est true.</returns>
      <param name="match">Délégué <see cref="T:System.Predicate`1" /> qui définit les conditions à vérifier par rapport aux éléments.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> a la valeur null.</exception>
    </member>
    <member name="T:System.Collections.Generic.List`1.Enumerator">
      <summary>Énumère les éléments de <see cref="T:System.Collections.Generic.List`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans <see cref="T:System.Collections.Generic.List`1" /> à la position actuelle de l'énumérateur.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.Dispose">
      <summary>Libère toutes les ressources utilisées par <see cref="T:System.Collections.Generic.List`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.MoveNext">
      <summary>Avance l'énumérateur à l'élément suivant de <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>true si l'énumérateur a pu avancer jusqu'à l'élément suivant ; false si l'énumérateur a dépassé la fin de la collection.</returns>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans <see cref="T:System.Collections.Generic.List`1" /> à la position actuelle de l'énumérateur.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Rétablit l'énumérateur à sa position initiale, qui précède le premier élément de la collection.</summary>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="T:System.Collections.Generic.Queue`1">
      <summary>Représente une collection d'objets premier entré, premier sorti.</summary>
      <typeparam name="T">Spécifie le type d'éléments dans la file d'attente.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.Queue`1" /> qui est vide et possède la capacité initiale par défaut.</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.Queue`1" /> qui contient des éléments copiés à partir de la collection spécifiée et qui possède une capacité suffisante pour accepter le nombre d'éléments copiés.</summary>
      <param name="collection">Collection dont les éléments sont copiés dans la nouvelle <see cref="T:System.Collections.Generic.Queue`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.Queue`1" /> qui est vide et a la capacité initiale spécifiée.</summary>
      <param name="capacity">Nombre initial d'éléments que <see cref="T:System.Collections.Generic.Queue`1" /> peut contenir.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Clear">
      <summary>Supprime tous les objets de <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Contains(`0)">
      <summary>Détermine si un élément est dans <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>true si <paramref name="item" /> existe dans <see cref="T:System.Collections.Generic.Queue`1" /> ; sinon, false.</returns>
      <param name="item">Objet à trouver dans <see cref="T:System.Collections.Generic.Queue`1" />.La valeur peut être null pour les types référence.</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.CopyTo(`0[],System.Int32)">
      <summary>Copie les éléments <see cref="T:System.Collections.Generic.Queue`1" /> dans un <see cref="T:System.Array" /> unidimensionnel existant, en commençant au niveau de l'index de tableau spécifié.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensionnel qui constitue la destination des éléments copiés à partir de <see cref="T:System.Collections.Generic.Queue`1" />.<see cref="T:System.Array" /> doit avoir une indexation de base zéro.</param>
      <param name="arrayIndex">Index de base zéro dans l'<paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Queue`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Count">
      <summary>Obtient le nombre d'éléments contenus dans le <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>Nombre d'éléments contenus dans <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Dequeue">
      <summary>Supprime et retourne l'objet au début de <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>Objet supprimé du début de <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enqueue(`0)">
      <summary>Ajoute un objet à la fin de <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <param name="item">Objet à ajouter à <see cref="T:System.Collections.Generic.Queue`1" />.La valeur peut être null pour les types référence.</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Queue`1.Enumerator" /> pour <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Peek">
      <summary>Retourne l'objet situé au début de <see cref="T:System.Collections.Generic.Queue`1" /> sans le supprimer.</summary>
      <returns>Objet situé au début de <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein d'une collection.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> qui peut être utilisé pour itérer au sein de la collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copie les éléments de <see cref="T:System.Collections.ICollection" /> dans <see cref="T:System.Array" />, à partir d'un index particulier de <see cref="T:System.Array" />.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensionnel qui constitue la destination des éléments copiés à partir de <see cref="T:System.Collections.ICollection" />.<see cref="T:System.Array" /> doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans l'<paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#IsSynchronized">
      <summary>Obtient une valeur indiquant si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe).</summary>
      <returns>true si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe) ; sinon false.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.Queue`1" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#SyncRoot">
      <summary>Obtient un objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.Queue`1" />, cette propriété retourne toujours l'instance actuelle.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein d'une collection.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> qui peut être utilisé pour itérer au sein de la collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.ToArray">
      <summary>Copie les éléments <see cref="T:System.Collections.Generic.Queue`1" /> vers un nouveau tableau.</summary>
      <returns>Nouveau tableau contenant les éléments copiés à partir de <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.TrimExcess">
      <summary>Définit la capacité au nombre réel d'éléments dans <see cref="T:System.Collections.Generic.Queue`1" />, si ce nombre est inférieur à 90 pour cent de la capacité actuelle.</summary>
    </member>
    <member name="T:System.Collections.Generic.Queue`1.Enumerator">
      <summary>Énumère les éléments de <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans <see cref="T:System.Collections.Generic.Queue`1" /> à la position actuelle de l'énumérateur.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.Dispose">
      <summary>Libère toutes les ressources utilisées par <see cref="T:System.Collections.Generic.Queue`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.MoveNext">
      <summary>Avance l'énumérateur à l'élément suivant de <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>true si l'énumérateur a pu avancer jusqu'à l'élément suivant ; false si l'énumérateur a dépassé la fin de la collection.</returns>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans la collection à la position actuelle de l'énumérateur.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Rétablit l'énumérateur à sa position initiale, qui précède le premier élément de la collection.</summary>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2">
      <summary>Représente une collection de paires clé/valeur triées sur la clé. </summary>
      <typeparam name="TKey">Type des clés dans le dictionnaire.</typeparam>
      <typeparam name="TValue">Type des valeurs dans le dictionnaire.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.SortedDictionary`2" /> vide et utilise l'implémentation de <see cref="T:System.Collections.Generic.IComparer`1" /> par défaut pour le type de clé.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.SortedDictionary`2" /> vide et utilise l'implémentation de <see cref="T:System.Collections.Generic.IComparer`1" /> spécifiée pour comparer les clés.</summary>
      <param name="comparer">Implémentation de <see cref="T:System.Collections.Generic.IComparer`1" /> à utiliser lors de la comparaison de clés, ou null pour utiliser le <see cref="T:System.Collections.Generic.Comparer`1" /> par défaut pour le type de la clé.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.SortedDictionary`2" /> qui contient des éléments copiés du <see cref="T:System.Collections.Generic.IDictionary`2" /> spécifié et utilise l'implémentation de <see cref="T:System.Collections.Generic.IComparer`1" /> par défaut pour le type de clé.</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" /> dont les éléments sont copiés dans le nouveau <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> contient une ou plusieurs clés dupliquées.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.SortedDictionary`2" /> qui contient des éléments copiés du <see cref="T:System.Collections.Generic.IDictionary`2" /> spécifié et utilise l'implémentation de <see cref="T:System.Collections.Generic.IComparer`1" /> spécifiée pour comparer les clés.</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" /> dont les éléments sont copiés dans le nouveau <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</param>
      <param name="comparer">Implémentation de <see cref="T:System.Collections.Generic.IComparer`1" /> à utiliser lors de la comparaison de clés, ou null pour utiliser le <see cref="T:System.Collections.Generic.Comparer`1" /> par défaut pour le type de la clé.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> contient une ou plusieurs clés dupliquées.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Add(`0,`1)">
      <summary>Ajoute un élément avec la clé et la valeur spécifiées dans <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <param name="key">Clé de l'élément à ajouter.</param>
      <param name="value">Valeur de l'élément à ajouter.La valeur peut être null pour les types référence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Un élément possédant la même clé existe déjà dans <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Clear">
      <summary>Supprime tous les éléments de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Comparer">
      <summary>Obtient le <see cref="T:System.Collections.Generic.IComparer`1" /> utilisé pour commander les éléments du <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IComparer`1" /> utilisé pour commander les éléments du <see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsKey(`0)">
      <summary>Détermine si l'objet <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contient un élément avec la clé spécifiée.</summary>
      <returns>true si <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contient un élément correspondant à la clé spécifiée ; sinon, false.</returns>
      <param name="key">Clé à rechercher dans <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsValue(`1)">
      <summary>Détermine si <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contient un élément avec la clé spécifiée.</summary>
      <returns>true si <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contient un élément correspondant à la valeur spécifiée ; sinon, false.</returns>
      <param name="value">Valeur à trouver dans <see cref="T:System.Collections.Generic.SortedDictionary`2" />.La valeur peut être null pour les types référence.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>Copie les éléments de <see cref="T:System.Collections.Generic.SortedDictionary`2" /> dans le tableau spécifié des structures <see cref="T:System.Collections.Generic.KeyValuePair`2" /> au niveau de l'index spécifié.</summary>
      <param name="array">Tableau unidimensionnel de structures <see cref="T:System.Collections.Generic.KeyValuePair`2" /> qui correspond à la destination des éléments copiés à partir du <see cref="T:System.Collections.Generic.SortedDictionary`2" />. Le tableau doit présenter une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans <paramref name="array" /> au niveau duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">Le nombre d'éléments dans le <see cref="T:System.Collections.Generic.SortedDictionary`2" /> source est supérieur à la quantité d'espace disponible entre <paramref name="index" /> et la fin du <paramref name="array" /> de destination.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Count">
      <summary>Obtient le nombre de paires clé/valeur contenues dans <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>Nombre de paires clé/valeur contenues dans <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" /> pour <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Item(`0)">
      <summary>Obtient ou définit la valeur associée à la clé spécifiée.</summary>
      <returns>Valeur associée à la clé spécifiée.Si la clé spécifiée est introuvable, une opération Get retourne <see cref="T:System.Collections.Generic.KeyNotFoundException" /> et une opération Set crée un nouvel élément avec la clé spécifiée.</returns>
      <param name="key">Clé de la valeur à obtenir ou à définir.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">La propriété est récupérée et <paramref name="key" /> n'existe pas dans la collection.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Keys">
      <summary>Obtient une collection contenant les clés dans <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> contenant les clés de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Remove(`0)">
      <summary>Supprime l'élément avec la clé spécifiée d'<see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>true si la suppression de l'élément réussit ; sinon, false.Cette méthode retourne également false si <paramref name="key" /> n'est pas trouvé dans le <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
      <param name="key">Clé de l'élément à supprimer.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Ajoute un élément à <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="keyValuePair">Structure <see cref="T:System.Collections.Generic.KeyValuePair`2" /> à ajouter à <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyValuePair" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Un élément possédant la même clé existe déjà dans <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Détermine si <see cref="T:System.Collections.Generic.ICollection`1" /> contient une clé et une valeur spécifiques.</summary>
      <returns>true si <paramref name="keyValuePair" /> se trouve dans <see cref="T:System.Collections.Generic.ICollection`1" /> ; sinon, false.</returns>
      <param name="keyValuePair">Structure <see cref="T:System.Collections.Generic.KeyValuePair`2" /> à rechercher dans <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtient une valeur indiquant si <see cref="T:System.Collections.Generic.ICollection`1" /> est en lecture seule.</summary>
      <returns>true si <see cref="T:System.Collections.Generic.ICollection`1" /> est en lecture seule ; sinon, false.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.SortedDictionary`2" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Supprime la première occurrence de l'élément spécifié dans <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <returns>true si <paramref name="keyValuePair" /> a été correctement supprimé de <see cref="T:System.Collections.Generic.ICollection`1" /> ; sinon false.Cette méthode retourne également false si <paramref name="keyValuePair" /> est introuvable dans <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">Structure <see cref="T:System.Collections.Generic.KeyValuePair`2" /> à supprimer de <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Obtient un <see cref="T:System.Collections.Generic.ICollection`1" /> contenant les clés de <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> contenant les clés de <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Obtient <see cref="T:System.Collections.Generic.ICollection`1" /> contenant les valeurs de <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> contenant les valeurs de <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein d'une collection.</summary>
      <returns>Objet <see cref="T:System.Collections.IEnumerator" /> qui peut être utilisé pour itérer au sein de la collection.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Obtient une collection qui contient les clés dans le<see cref="T:System.Collections.Generic.SortedDictionary`2" /></summary>
      <returns>Une collection qui contient les clés dans le<see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Obtient une collection contenant les valeurs de la<see cref="T:System.Collections.Generic.SortedDictionary`2" /></summary>
      <returns>Une collection contenant les valeurs de la<see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copie les éléments de l'objet <see cref="T:System.Collections.Generic.ICollection`1" /> dans un tableau, en commençant au niveau d'un index de tableau spécifié.</summary>
      <param name="array">Le tableau unidimensionnel qui constitue la destination des éléments copiés à partir de <see cref="T:System.Collections.Generic.ICollection`1" />.Ce tableau doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans <paramref name="array" /> au niveau duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> est multidimensionnel.ou<paramref name="array" /> n'a pas d'indexation de base zéro.ouLe nombre d'éléments dans le <see cref="T:System.Collections.Generic.ICollection`1" /> source est supérieur à la quantité d'espace disponible entre <paramref name="index" /> et la fin du <paramref name="array" /> de destination.ouLe cast automatique du type du <see cref="T:System.Collections.Generic.ICollection`1" /> source en type du <paramref name="array" /> de destination est impossible.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>Obtient une valeur indiquant si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe).</summary>
      <returns>true si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe) ; sinon false.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.SortedDictionary`2" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>Obtient un objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />. </returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Ajoute un élément avec la clé et la valeur fournies à <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">Objet à utiliser comme clé de l'élément à ajouter.</param>
      <param name="value">Objet à utiliser comme valeur de l'élément à ajouter.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> a un type qui ne peut pas être assigné au type de clé <paramref name="TKey" /> de <see cref="T:System.Collections.IDictionary" />.ou<paramref name="value" /> a un type qui ne peut pas être assigné au type valeur <paramref name="TValue" /> de <see cref="T:System.Collections.IDictionary" />.ouUn élément possédant la même clé existe déjà dans <see cref="T:System.Collections.IDictionary" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Collections.IDictionary" /> contient un élément avec la clé spécifiée.</summary>
      <returns>true si <see cref="T:System.Collections.IDictionary" /> contient un élément avec la clé ; sinon, false.</returns>
      <param name="key">Clé à rechercher dans <see cref="T:System.Collections.IDictionary" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Retourne un <see cref="T:System.Collections.IDictionaryEnumerator" /> pour <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>
        <see cref="T:System.Collections.IDictionaryEnumerator" /> pour <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Obtient une valeur indiquant si la taille de <see cref="T:System.Collections.IDictionary" /> est fixe.</summary>
      <returns>true si <see cref="T:System.Collections.IDictionary" /> a une taille fixe ; sinon, false.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.SortedDictionary`2" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Obtient une valeur indiquant si <see cref="T:System.Collections.IDictionary" /> est en lecture seule.</summary>
      <returns>true si <see cref="T:System.Collections.IDictionary" /> est en lecture seule ; sinon, false.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.SortedDictionary`2" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Obtient ou définit l'élément à l'aide de la clé spécifiée.</summary>
      <returns>Élément associé à la clé spécifiée, ou null si <paramref name="key" /> ne figure pas dans le dictionnaire ou si le type de <paramref name="key" /> ne peut pas être assigné au type de clé <paramref name="TKey" /> de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
      <param name="key">Clé de l'élément à obtenir.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Une valeur est assignée et <paramref name="key" /> a un type qui ne peut pas être assigné au type de clé <paramref name="TKey" /> de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.ouUne valeur est assignée et <paramref name="value" /> a un type qui ne peut pas être assigné au type valeur <paramref name="TValue" /> de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Keys">
      <summary>Obtient un <see cref="T:System.Collections.ICollection" /> contenant les clés de <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> contenant les clés de <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Supprime l'élément avec la clé spécifiée d'<see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">Clé de l'élément à supprimer.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Values">
      <summary>Obtient <see cref="T:System.Collections.ICollection" /> contenant les valeurs de <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> contenant les valeurs de <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de la collection.</summary>
      <returns>Objet <see cref="T:System.Collections.Generic.IEnumerator`1" /> qui peut être utilisé pour itérer au sein de la collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.TryGetValue(`0,`1@)">
      <summary>Obtient la valeur associée à la clé spécifiée.</summary>
      <returns>true si <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contient un élément correspondant à la clé spécifiée ; sinon, false.</returns>
      <param name="key">Clé de la valeur à obtenir.</param>
      <param name="value">Cette méthode retourne la valeur associée à la clé spécifiée, si la clé est trouvée ; sinon, elle retourne la valeur par défaut pour le type du paramètre <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Values">
      <summary>Obtient une collection contenant les valeurs de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> contenant les valeurs de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.Enumerator">
      <summary>Énumère les éléments de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans <see cref="T:System.Collections.Generic.SortedDictionary`2" /> à la position actuelle de l'énumérateur.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.Dispose">
      <summary>Libère toutes les ressources utilisées par <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.MoveNext">
      <summary>Avance l'énumérateur à l'élément suivant de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>true si l'énumérateur a pu avancer jusqu'à l'élément suivant ; false si l'énumérateur a dépassé la fin de la collection.</returns>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>Obtient l'élément à la position actuelle de l'énumérateur sous la forme d'une structure <see cref="T:System.Collections.DictionaryEntry" />.</summary>
      <returns>Élément dans la collection à la position actuelle du dictionnaire, sous la forme d'une structure <see cref="T:System.Collections.DictionaryEntry" />.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>Obtient la clé de l'élément à la position actuelle de l'énumérateur.</summary>
      <returns>Clé de l'élément dans la collection à la position actuelle de l'énumérateur.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>Obtient la valeur de l'élément à la position actuelle de l'énumérateur.</summary>
      <returns>Valeur de l'élément dans la collection à la position actuelle de l'énumérateur.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans la collection à la position actuelle de l'énumérateur.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Rétablit l'énumérateur à sa position initiale, qui précède le premier élément de la collection.</summary>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection">
      <summary>Représente la collection de clés dans <see cref="T:System.Collections.Generic.SortedDictionary`2" />.Cette classe ne peut pas être héritée.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> qui reflète les clés dans le <see cref="T:System.Collections.Generic.SortedDictionary`2" /> spécifié.</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> dont les clés sont répercutées dans la nouvelle <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>Copie les éléments <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> dans un tableau unidimensionnel existant commençant au niveau de l'index de tableau spécifié.</summary>
      <param name="array">Le tableau unidimensionnel qui constitue la destination des éléments copiés à partir de <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.Ce tableau doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans <paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">Le nombre d'éléments dans le <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> source est supérieur à la quantité d'espace disponible entre <paramref name="index" /> et la fin du <paramref name="array" /> de destination.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Count">
      <summary>Obtient le nombre d'éléments contenus dans le <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
      <returns>Nombre d'éléments contenus dans <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
      <returns>Structure <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" /> pour <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Ajoute un élément à <see cref="T:System.Collections.Generic.ICollection`1" />.  Cette implémentation lève toujours <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">Objet à ajouter à <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Toujours levée ; la collection est en lecture seule.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Supprime tous les éléments du <see cref="T:System.Collections.Generic.ICollection`1" />.  Cette implémentation lève toujours <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Toujours levée ; la collection est en lecture seule.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>Détermine si <see cref="T:System.Collections.Generic.ICollection`1" /> contient la valeur spécifiée.</summary>
      <returns>true si <paramref name="item" /> existe dans <see cref="T:System.Collections.Generic.ICollection`1" /> ; sinon, false.</returns>
      <param name="item">Objet à trouver dans <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtient une valeur indiquant si <see cref="T:System.Collections.Generic.ICollection`1" /> est en lecture seule.</summary>
      <returns>true si <see cref="T:System.Collections.Generic.ICollection`1" /> est en lecture seule ; sinon, false.  Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>Supprime la première occurrence d'un objet spécifique de <see cref="T:System.Collections.Generic.ICollection`1" />.  Cette implémentation lève toujours <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true si <paramref name="item" /> a été correctement supprimé de <see cref="T:System.Collections.Generic.ICollection`1" /> ; sinon, false.Cette méthode retourne également false si <paramref name="item" /> n'est pas trouvé dans le <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="item">Objet à supprimer de <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Toujours levée ; la collection est en lecture seule.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de la collection.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> qui peut être utilisé pour itérer au sein de la collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copie les éléments de l'objet <see cref="T:System.Collections.ICollection" /> dans un tableau, en commençant au niveau d'un index de tableau particulier.</summary>
      <param name="array">Le tableau unidimensionnel qui constitue la destination des éléments copiés à partir de <see cref="T:System.Collections.ICollection" />.Ce tableau doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans <paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> est multidimensionnel.ou<paramref name="array" /> n'a pas d'indexation de base zéro.ouLe nombre d'éléments dans le <see cref="T:System.Collections.ICollection" /> source est supérieur à la quantité d'espace disponible entre <paramref name="index" /> et la fin du <paramref name="array" /> de destination.ouLe cast automatique du type du <see cref="T:System.Collections.ICollection" /> source en type du <paramref name="array" /> de destination est impossible.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Obtient une valeur indiquant si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe).</summary>
      <returns>true si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe) ; sinon false.  Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>Obtient un objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.  Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />, cette propriété retourne toujours l'instance actuelle.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de la collection.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> qui peut être utilisé pour itérer la collection.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator">
      <summary>Énumère les éléments de <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> à la position actuelle de l'énumérateur.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>Libère toutes les ressources utilisées par <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>Avance l'énumérateur à l'élément suivant de <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
      <returns>true si l'énumérateur a pu avancer jusqu'à l'élément suivant ; false si l'énumérateur a dépassé la fin de la collection.</returns>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans la collection à la position actuelle de l'énumérateur.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Rétablit l'énumérateur à sa position initiale, qui précède le premier élément de la collection.</summary>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection">
      <summary>Représente la collection de valeurs dans <see cref="T:System.Collections.Generic.SortedDictionary`2" />.Cette classe ne peut pas être héritée.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> qui reflète les valeurs dans le <see cref="T:System.Collections.Generic.SortedDictionary`2" /> spécifié.</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> dont les valeurs sont reflétées dans la nouvelle <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>Copie les éléments <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> dans un tableau unidimensionnel existant commençant au niveau de l'index de tableau spécifié.</summary>
      <param name="array">Le tableau unidimensionnel qui constitue la destination des éléments copiés à partir de <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.Ce tableau doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans <paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">Le nombre d'éléments dans le <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> source est supérieur à la quantité d'espace disponible entre <paramref name="index" /> et la fin du <paramref name="array" /> de destination.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Count">
      <summary>Obtient le nombre d'éléments contenus dans le <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
      <returns>Nombre d'éléments contenus dans <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
      <returns>Structure <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" /> pour <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>Ajoute un élément à <see cref="T:System.Collections.Generic.ICollection`1" />.  Cette implémentation lève toujours <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">Objet à ajouter à <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Toujours levée ; la collection est en lecture seule.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Supprime tous les éléments du <see cref="T:System.Collections.Generic.ICollection`1" />.  Cette implémentation lève toujours <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Toujours levée ; la collection est en lecture seule.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>Détermine si <see cref="T:System.Collections.Generic.ICollection`1" /> contient une valeur spécifique.</summary>
      <returns>true si <paramref name="item" /> existe dans <see cref="T:System.Collections.Generic.ICollection`1" /> ; sinon, false.</returns>
      <param name="item">Objet à trouver dans <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtient une valeur indiquant si <see cref="T:System.Collections.Generic.ICollection`1" /> est en lecture seule.</summary>
      <returns>true si <see cref="T:System.Collections.Generic.ICollection`1" /> est en lecture seule ; sinon, false.  Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>Supprime la première occurrence d'un objet spécifique de <see cref="T:System.Collections.Generic.ICollection`1" />.  Cette implémentation lève toujours <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true si <paramref name="item" /> a été correctement supprimé de <see cref="T:System.Collections.Generic.ICollection`1" /> ; sinon, false.Cette méthode retourne également false si <paramref name="item" /> n'est pas trouvé dans le <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="item">Objet à supprimer de <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Toujours levée ; la collection est en lecture seule.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Supprime la première occurrence d'un objet spécifique de <see cref="T:System.Collections.Generic.ICollection`1" />.  Cette implémentation lève toujours <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true si <paramref name="item" /> a été correctement supprimé de <see cref="T:System.Collections.Generic.ICollection`1" /> ; sinon, false.Cette méthode retourne également false si <paramref name="item" /> n'est pas trouvé dans le <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <exception cref="T:System.NotSupportedException">Toujours levée ; la collection est en lecture seule.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copie les éléments de l'objet <see cref="T:System.Collections.ICollection" /> dans un tableau, en commençant au niveau d'un index de tableau particulier.</summary>
      <param name="array">Le tableau unidimensionnel qui constitue la destination des éléments copiés à partir de <see cref="T:System.Collections.ICollection" />.Ce tableau doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans <paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> est multidimensionnel.ou<paramref name="array" /> n'a pas d'indexation de base zéro.ouLe nombre d'éléments dans le <see cref="T:System.Collections.ICollection" /> source est supérieur à la quantité d'espace disponible entre <paramref name="index" /> et la fin du <paramref name="array" /> de destination.ouLe cast automatique du type du <see cref="T:System.Collections.ICollection" /> source en type du <paramref name="array" /> de destination est impossible.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Obtient une valeur indiquant si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe).</summary>
      <returns>true si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe) ; sinon false.  Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>Obtient un objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.  Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />, cette propriété retourne toujours l'instance actuelle.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de la collection.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> qui peut être utilisé pour itérer la collection.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator">
      <summary>Énumère les éléments de <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> à la position actuelle de l'énumérateur.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>Libère toutes les ressources utilisées par <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>Avance l'énumérateur à l'élément suivant de <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
      <returns>true si l'énumérateur a pu avancer jusqu'à l'élément suivant ; false si l'énumérateur a dépassé la fin de la collection.</returns>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans la collection à la position actuelle de l'énumérateur.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Rétablit l'énumérateur à sa position initiale, qui précède le premier élément de la collection.</summary>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedList`2">
      <summary>Représente une collection de paires clé/valeur triées par clé en fonction de l'implémentation <see cref="T:System.Collections.Generic.IComparer`1" /> associée. </summary>
      <typeparam name="TKey">Type de clés de la collection.</typeparam>
      <typeparam name="TValue">Type de valeurs de la collection.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.SortedList`2" /> qui est vide, possède la capacité initiale par défaut et utilise le <see cref="T:System.Collections.Generic.IComparer`1" /> par défaut.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.SortedList`2" /> qui est vide, possède la capacité initiale par défaut et utilise le <see cref="T:System.Collections.Generic.IComparer`1" /> spécifié.</summary>
      <param name="comparer">Implémentation de <see cref="T:System.Collections.Generic.IComparer`1" /> à utiliser lors de la comparaison de clés.ounull pour utiliser <see cref="T:System.Collections.Generic.Comparer`1" /> par défaut pour le type de la clé.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.SortedList`2" /> qui contient des éléments copiés à partir du <see cref="T:System.Collections.Generic.IDictionary`2" /> spécifié, possède une capacité suffisante pour accepter le nombre d'éléments copiés et utilise le <see cref="T:System.Collections.Generic.IComparer`1" /> par défaut.</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" /> dont les éléments sont copiés dans le nouveau <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> contient une ou plusieurs clés dupliquées.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.SortedList`2" /> qui contient des éléments copiés à partir du <see cref="T:System.Collections.Generic.IDictionary`2" /> spécifié, possède une capacité suffisante pour accepter le nombre d'éléments copiés et utilise le <see cref="T:System.Collections.Generic.IComparer`1" /> spécifié.</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" /> dont les éléments sont copiés dans le nouveau <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <param name="comparer">Implémentation de <see cref="T:System.Collections.Generic.IComparer`1" /> à utiliser lors de la comparaison de clés.ounull pour utiliser <see cref="T:System.Collections.Generic.Comparer`1" /> par défaut pour le type de la clé.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> contient une ou plusieurs clés dupliquées.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.SortedList`2" /> qui est vide, possède la capacité initiale spécifiée et utilise le <see cref="T:System.Collections.Generic.IComparer`1" /> par défaut.</summary>
      <param name="capacity">Nombre initial d'éléments que <see cref="T:System.Collections.Generic.SortedList`2" /> peut contenir.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> est inférieur à zéro.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.SortedList`2" /> qui est vide, possède la capacité initiale spécifiée et utilise le <see cref="T:System.Collections.Generic.IComparer`1" /> spécifié.</summary>
      <param name="capacity">Nombre initial d'éléments que <see cref="T:System.Collections.Generic.SortedList`2" /> peut contenir.</param>
      <param name="comparer">Implémentation de <see cref="T:System.Collections.Generic.IComparer`1" /> à utiliser lors de la comparaison de clés.ounull pour utiliser <see cref="T:System.Collections.Generic.Comparer`1" /> par défaut pour le type de la clé.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> est inférieur à zéro.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Add(`0,`1)">
      <summary>Ajoute un élément avec la clé et la valeur spécifiées dans <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <param name="key">Clé de l'élément à ajouter.</param>
      <param name="value">Valeur de l'élément à ajouter.La valeur peut être null pour les types référence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Un élément possédant la même clé existe déjà dans <see cref="T:System.Collections.Generic.SortedList`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Capacity">
      <summary>Obtient ou définit le nombre d'éléments que <see cref="T:System.Collections.Generic.SortedList`2" /> peut contenir.</summary>
      <returns>Nombre d'éléments que <see cref="T:System.Collections.Generic.SortedList`2" /> peut contenir.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Une valeur inférieure à <see cref="P:System.Collections.Generic.SortedList`2.Count" /> est affectée à <see cref="P:System.Collections.Generic.SortedList`2.Capacity" />.</exception>
      <exception cref="T:System.OutOfMemoryException">La mémoire disponible sur le système est insuffisante.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Clear">
      <summary>Supprime tous les éléments de <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Comparer">
      <summary>Obtient <see cref="T:System.Collections.Generic.IComparer`1" /> pour la liste triée. </summary>
      <returns>
        <see cref="T:System.IComparable`1" /> du <see cref="T:System.Collections.Generic.SortedList`2" /> actif.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsKey(`0)">
      <summary>Détermine si <see cref="T:System.Collections.Generic.SortedList`2" /> contient une clé spécifique.</summary>
      <returns>true si <see cref="T:System.Collections.Generic.SortedList`2" /> contient un élément correspondant à la clé spécifiée ; sinon, false.</returns>
      <param name="key">Clé à rechercher dans <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsValue(`1)">
      <summary>Détermine si <see cref="T:System.Collections.Generic.SortedList`2" /> contient une valeur spécifique.</summary>
      <returns>true si <see cref="T:System.Collections.Generic.SortedList`2" /> contient un élément correspondant à la valeur spécifiée ; sinon, false.</returns>
      <param name="value">Valeur à trouver dans <see cref="T:System.Collections.Generic.SortedList`2" />.La valeur peut être null pour les types référence.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Count">
      <summary>Obtient le nombre de paires clé/valeur contenues dans <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>Nombre de paires clé/valeur contenues dans <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> de type <see cref="T:System.Collections.Generic.KeyValuePair`2" /> pour <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfKey(`0)">
      <summary>Recherche la clé spécifiée et retourne l'index de base zéro dans le <see cref="T:System.Collections.Generic.SortedList`2" /> entier.</summary>
      <returns>Index de base zéro de <paramref name="key" /> dans le <see cref="T:System.Collections.Generic.SortedList`2" /> entier, si elle existe ; sinon, -1.</returns>
      <param name="key">Clé à rechercher dans <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfValue(`1)">
      <summary>Recherche la valeur spécifiée et retourne l'index de base zéro de la première occurrence dans l'ensemble du <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>Index de base zéro de la première occurrence d'<paramref name="value" /> dans l'ensemble de <see cref="T:System.Collections.Generic.SortedList`2" />, s'il existe ; sinon, -1.</returns>
      <param name="value">Valeur à trouver dans <see cref="T:System.Collections.Generic.SortedList`2" />.La valeur peut être null pour les types référence.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Item(`0)">
      <summary>Obtient ou définit la valeur associée à la clé spécifiée.</summary>
      <returns>Valeur associée à la clé spécifiée.Si la clé spécifiée est introuvable, une opération Get retourne <see cref="T:System.Collections.Generic.KeyNotFoundException" /> et une opération Set crée un nouvel élément à l'aide de la clé spécifiée.</returns>
      <param name="key">Clé dont la valeur doit être obtenue ou définie.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">La propriété est récupérée et <paramref name="key" /> n'existe pas dans la collection.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Keys">
      <summary>Obtient une collection contenant les clés de la <see cref="T:System.Collections.Generic.SortedList`2" />, dans un ordre trié.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IList`1" /> contenant les clés de <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Remove(`0)">
      <summary>Supprime l'élément avec la clé spécifiée d'<see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>true si la suppression de l'élément réussit ; sinon, false.Cette méthode retourne également false si <paramref name="key" /> est introuvable dans le <see cref="T:System.Collections.Generic.SortedList`2" /> d'origine.</returns>
      <param name="key">Clé de l'élément à supprimer.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.RemoveAt(System.Int32)">
      <summary>Supprime l'élément au niveau de l'index spécifié de <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <param name="index">Index de base zéro de l'élément à supprimer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à zéro.ou<paramref name="index" /> est supérieur ou égal à <see cref="P:System.Collections.Generic.SortedList`2.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Ajoute une paire clé/valeur à <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.KeyValuePair`2" /> à ajouter au <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Détermine si <see cref="T:System.Collections.Generic.ICollection`1" /> contient un élément spécifique.</summary>
      <returns>true si <paramref name="keyValuePair" /> se trouve dans <see cref="T:System.Collections.Generic.ICollection`1" /> ; sinon, false.</returns>
      <param name="keyValuePair">Objet <see cref="T:System.Collections.Generic.KeyValuePair`2" /> à rechercher dans <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>Copie les éléments de <see cref="T:System.Collections.Generic.ICollection`1" /> dans un <see cref="T:System.Array" />, en commençant à un index <see cref="T:System.Array" /> particulier.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensionnel qui constitue la destination des éléments copiés à partir de <see cref="T:System.Collections.Generic.ICollection`1" />.<see cref="T:System.Array" /> doit avoir une indexation de base zéro.</param>
      <param name="arrayIndex">Index de base zéro dans <paramref name="array" /> au niveau duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> est inférieur à zéro. </exception>
      <exception cref="T:System.ArgumentException">Le nombre d'éléments dans le <see cref="T:System.Collections.Generic.ICollection`1" /> source est supérieur à la quantité d'espace disponible entre <paramref name="arrayIndex" /> et la fin du <paramref name="array" /> de destination.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtient une valeur indiquant si <see cref="T:System.Collections.Generic.ICollection`1" /> est en lecture seule.</summary>
      <returns>true si <see cref="T:System.Collections.Generic.ICollection`1" /> est en lecture seule ; sinon, false.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.SortedList`2" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Supprime de <see cref="T:System.Collections.Generic.ICollection`1" /> la première occurrence d'une paire clé/valeur spécifique.</summary>
      <returns>true si <paramref name="keyValuePair" /> a été correctement supprimé de <see cref="T:System.Collections.Generic.ICollection`1" /> ; sinon false.Cette méthode retourne également false si <paramref name="keyValuePair" /> est introuvable dans le <see cref="T:System.Collections.Generic.ICollection`1" /> d'origine.</returns>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.KeyValuePair`2" /> à supprimer de <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Obtient un <see cref="T:System.Collections.Generic.ICollection`1" /> contenant les clés de <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> contenant les clés de <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Obtient <see cref="T:System.Collections.Generic.ICollection`1" /> contenant les valeurs de <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> contenant les valeurs de <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein d'une collection.</summary>
      <returns>Objet <see cref="T:System.Collections.Generic.IEnumerator`1" /> qui peut être utilisé pour itérer au sein de la collection.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Obtient une collection énumérable qui contient les clés dans dictionnaire en lecture seule.</summary>
      <returns>Collection énumérable qui contient les clés dans dictionnaire en lecture seule.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Obtient une collection énumérable qui contient les valeurs dans dictionnaire en lecture seule.</summary>
      <returns>Collection énumérable qui contient les valeurs dans dictionnaire en lecture seule.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copie les éléments de <see cref="T:System.Collections.ICollection" /> dans un <see cref="T:System.Array" />, en commençant à un index <see cref="T:System.Array" /> particulier.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensionnel qui constitue la destination des éléments copiés à partir de <see cref="T:System.Collections.ICollection" />.<see cref="T:System.Array" /> doit avoir une indexation de base zéro.</param>
      <param name="arrayIndex">Index de base zéro dans <paramref name="array" /> au niveau duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> est inférieur à zéro.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> est multidimensionnel.ou<paramref name="array" /> n'a pas d'indexation de base zéro.ouLe nombre d'éléments dans le <see cref="T:System.Collections.ICollection" /> source est supérieur à la quantité d'espace disponible entre <paramref name="arrayIndex" /> et la fin du <paramref name="array" /> de destination.ouLe cast automatique du type du <see cref="T:System.Collections.ICollection" /> source en type du <paramref name="array" /> de destination est impossible.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#IsSynchronized">
      <summary>Obtient une valeur indiquant si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe).</summary>
      <returns>true si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe) ; sinon false.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.SortedList`2" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#SyncRoot">
      <summary>Obtient un objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.SortedList`2" />, cette propriété retourne toujours l'instance actuelle.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Ajoute un élément avec la clé et la valeur fournies à <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">
        <see cref="T:System.Object" /> à utiliser comme clé de l'élément à ajouter.</param>
      <param name="value">
        <see cref="T:System.Object" /> à utiliser comme valeur de l'élément à ajouter.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> a un type qui ne peut pas être assigné au type de clé <paramref name="TKey" /> de <see cref="T:System.Collections.IDictionary" />.ou<paramref name="value" /> a un type qui ne peut pas être assigné au type valeur <paramref name="TValue" /> de <see cref="T:System.Collections.IDictionary" />.ouUn élément possédant la même clé existe déjà dans <see cref="T:System.Collections.IDictionary" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Collections.IDictionary" /> contient un élément avec la clé spécifiée.</summary>
      <returns>true si <see cref="T:System.Collections.IDictionary" /> contient un élément avec la clé ; sinon, false.</returns>
      <param name="key">Clé à rechercher dans <see cref="T:System.Collections.IDictionary" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Retourne un <see cref="T:System.Collections.IDictionaryEnumerator" /> pour <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>
        <see cref="T:System.Collections.IDictionaryEnumerator" /> pour <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Obtient une valeur indiquant si la taille de <see cref="T:System.Collections.IDictionary" /> est fixe.</summary>
      <returns>true si <see cref="T:System.Collections.IDictionary" /> a une taille fixe ; sinon, false.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.SortedList`2" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Obtient une valeur indiquant si <see cref="T:System.Collections.IDictionary" /> est en lecture seule.</summary>
      <returns>true si <see cref="T:System.Collections.IDictionary" /> est en lecture seule ; sinon, false.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.SortedList`2" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Obtient ou définit l'élément à l'aide de la clé spécifiée.</summary>
      <returns>Élément associé à la clé spécifiée, ou null si <paramref name="key" /> ne figure pas dans le dictionnaire ou si le type de <paramref name="key" /> ne peut pas être assigné au type de clé <paramref name="TKey" /> de <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
      <param name="key">Clé de l'élément à obtenir ou définir.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">Une valeur est assignée et <paramref name="key" /> a un type qui ne peut pas être assigné au type de clé <paramref name="TKey" /> de <see cref="T:System.Collections.Generic.SortedList`2" />.ouUne valeur est assignée et <paramref name="value" /> a un type qui ne peut pas être assigné au type valeur <paramref name="TValue" /> de <see cref="T:System.Collections.Generic.SortedList`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Keys">
      <summary>Obtient un <see cref="T:System.Collections.ICollection" /> contenant les clés de <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> contenant les clés de <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Supprime l'élément avec la clé spécifiée d'<see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">Clé de l'élément à supprimer.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Values">
      <summary>Obtient <see cref="T:System.Collections.ICollection" /> contenant les valeurs de <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> contenant les valeurs de <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein d'une collection.</summary>
      <returns>Objet <see cref="T:System.Collections.IEnumerator" /> qui peut être utilisé pour itérer au sein de la collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TrimExcess">
      <summary>Définit la capacité au nombre réel d'éléments dans <see cref="T:System.Collections.Generic.SortedList`2" />, si ce nombre est inférieur à 90 pour cent de capacité actuelle.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TryGetValue(`0,`1@)">
      <summary>Obtient la valeur associée à la clé spécifiée.</summary>
      <returns>true si <see cref="T:System.Collections.Generic.SortedList`2" /> contient un élément correspondant à la clé spécifiée ; sinon, false.</returns>
      <param name="key">Clé dont la valeur doit être obtenue.</param>
      <param name="value">Cette méthode retourne la valeur associée à la clé spécifiée, si la clé est trouvée ; sinon, elle retourne la valeur par défaut pour le type du paramètre <paramref name="value" />.Ce paramètre est passé sans être initialisé.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Values">
      <summary>Obtient une collection contenant les valeurs de <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IList`1" /> contenant les valeurs de <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1">
      <summary>Représente une collection d'objets tenue à jour en ordre trié.</summary>
      <typeparam name="T">Type des éléments de l'ensemble.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.SortedSet`1" />. </summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.SortedSet`1" /> qui utilise un comparateur spécifié.</summary>
      <param name="comparer">Comparateur par défaut à utiliser pour la comparaison d'objets. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparer" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.SortedSet`1" /> qui contient des éléments copiés d'une collection énumérable spécifiée.</summary>
      <param name="collection">Collection énumérable à copier. </param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IComparer{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.SortedSet`1" /> qui contient des éléments copiés d'une collection énumérable spécifiée et qui utilise un comparateur spécifié.</summary>
      <param name="collection">Collection énumérable à copier. </param>
      <param name="comparer">Comparateur par défaut à utiliser pour la comparaison d'objets. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Add(`0)">
      <summary>Ajoute un élément à l'ensemble et retourne une valeur qui indique si l'ajout a réussi.</summary>
      <returns>true si <paramref name="item" /> est ajouté à l'ensemble ; sinon, false. </returns>
      <param name="item">Élément à ajouter à l'ensemble.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Clear">
      <summary>Supprime tous les éléments de l'ensemble.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Comparer">
      <summary>Obtient l'objet <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> qui est utilisé pour déterminer l'égalité des valeurs de l'ensemble <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Comparateur utilisé pour déterminer l'égalité des valeurs de l'ensemble <see cref="T:System.Collections.Generic.SortedSet`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Contains(`0)">
      <summary>Détermine si l'ensemble contient un élément spécifique.</summary>
      <returns>true si l'ensemble contient <paramref name="item" /> ; sinon, false.</returns>
      <param name="item">Élément à rechercher dans l'ensemble.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[])">
      <summary>Copie l'ensemble du <see cref="T:System.Collections.Generic.SortedSet`1" /> dans un tableau unidimensionnel compatible, en commençant au début du tableau cible.</summary>
      <param name="array">Tableau unidimensionnel qui constitue la destination des éléments copiés à partir de <see cref="T:System.Collections.Generic.SortedSet`1" />.</param>
      <exception cref="T:System.ArgumentException">Le nombre d'éléments dans le <see cref="T:System.Collections.Generic.SortedSet`1" /> source est supérieur au nombre d'éléments que le tableau de destination peut contenir. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32)">
      <summary>Copie l'ensemble du <see cref="T:System.Collections.Generic.SortedSet`1" /> dans un tableau unidimensionnel compatible, en commençant au niveau de l'index spécifié du tableau.</summary>
      <param name="array">Tableau unidimensionnel qui constitue la destination des éléments copiés à partir de <see cref="T:System.Collections.Generic.SortedSet`1" />.Ce tableau doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans le <paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentException">Le nombre d'éléments dans le tableau source est supérieur à la quantité d'espace disponible entre <paramref name="index" /> et la fin du tableau de destination.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à zéro.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>Copie un nombre d'éléments spécifié à partir de <see cref="T:System.Collections.Generic.SortedSet`1" /> dans un tableau unidimensionnel compatible en commençant au niveau de l'index spécifié du tableau.</summary>
      <param name="array">Tableau unidimensionnel qui constitue la destination des éléments copiés à partir de <see cref="T:System.Collections.Generic.SortedSet`1" />.Ce tableau doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans le <paramref name="array" /> à partir duquel la copie commence.</param>
      <param name="count">Nombre d'éléments à copier.</param>
      <exception cref="T:System.ArgumentException">Le nombre d'éléments dans le tableau source est supérieur à la quantité d'espace disponible entre <paramref name="index" /> et la fin du tableau de destination.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à zéro.ou<paramref name="count" /> est inférieur à zéro.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Count">
      <summary>Obtient le nombre d'éléments contenus dans <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Nombre d'éléments dans le <see cref="T:System.Collections.Generic.SortedSet`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Supprime tous les éléments figurant dans la collection spécifiée de l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> actuel.</summary>
      <param name="other">Collection d'éléments à supprimer de l'objet <see cref="T:System.Collections.Generic.SortedSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Énumérateur qui itère via le <see cref="T:System.Collections.Generic.SortedSet`1" /> dans l'ordre de tri.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetViewBetween(`0,`0)">
      <summary>Retourne une vue d'un sous-ensemble dans un <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Vue de sous-ensemble qui contient uniquement les valeurs de la plage spécifiée.</returns>
      <param name="lowerValue">Valeur la plus faible souhaitée dans la vue.</param>
      <param name="upperValue">Valeur la plus élevée souhaitée dans la vue. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="lowerValue" /> est supérieur à <paramref name="upperValue" /> d'après le comparateur.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Une opération éprouvée sur la vue était à l'extérieur de la plage spécifiée par <paramref name="lowerValue" /> et <paramref name="upperValue" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifie l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> actuel afin qu'il contienne uniquement les éléments qui figurent également dans une collection spécifiée.</summary>
      <param name="other">Collection à comparer à l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> actif.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Détermine si un objet <see cref="T:System.Collections.Generic.SortedSet`1" /> est un sous-ensemble propre à la collection spécifiée.</summary>
      <returns>true si l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> est un sous-ensemble propre à <paramref name="other" /> ; sinon false.</returns>
      <param name="other">Collection à comparer à l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> actif.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Détermine si un objet <see cref="T:System.Collections.Generic.SortedSet`1" /> est un sur-ensemble propre à la collection spécifiée.</summary>
      <returns>true si l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> est un sur-ensemble propre à <paramref name="other" /> ; sinon false.</returns>
      <param name="other">Collection à comparer à l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> actif. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Détermine si un objet <see cref="T:System.Collections.Generic.SortedSet`1" /> est un sous-ensemble de la collection spécifiée.</summary>
      <returns>true si l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> actuel est un sous-ensemble de <paramref name="other" /> ; sinon, false.</returns>
      <param name="other">Collection à comparer à l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> actif.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Détermine si un objet <see cref="T:System.Collections.Generic.SortedSet`1" /> est un sur-ensemble de la collection spécifiée.</summary>
      <returns>true si l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> est un sur-ensemble de <paramref name="other" /> ; sinon, false.</returns>
      <param name="other">Collection à comparer à l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> actif. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Max">
      <summary>Obtient la valeur maximale de <see cref="T:System.Collections.Generic.SortedSet`1" />, tel que défini par le comparateur.</summary>
      <returns>Valeur maximale dans l'ensemble.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Min">
      <summary>Obtient la valeur minimale de <see cref="T:System.Collections.Generic.SortedSet`1" />, tel que défini par le comparateur.</summary>
      <returns>Valeur minimale dans l'ensemble.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>Détermine si l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> actuel et une collection spécifiée partagent des éléments communs.</summary>
      <returns>true si l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> et <paramref name="other" /> partagent au moins un élément commun ; sinon false.</returns>
      <param name="other">Collection à comparer à l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> actif.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Remove(`0)">
      <summary>Supprime un élément spécifié de <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>true si la recherche et la suppression de l'élément réussissent ; sinon, false. </returns>
      <param name="item">Élément à supprimer.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>Supprime tous les éléments qui correspondent aux conditions définies par le prédicat spécifié de la collection <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Nombre d'éléments supprimés de la collection <see cref="T:System.Collections.Generic.SortedSet`1" />. </returns>
      <param name="match">Délégué qui définit les conditions des éléments à supprimer.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Reverse">
      <summary>Retourne un <see cref="T:System.Collections.Generic.IEnumerable`1" /> qui itère dans l'ordre inverse au sein de <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Énumérateur qui itère dans l'ordre inverse au sein de <see cref="T:System.Collections.Generic.SortedSet`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>Détermine si l'objet <see cref="T:System.Collections.Generic.SortedSet`1" />actif et la collection spécifiée contiennent les mêmes éléments.</summary>
      <returns>true si l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> actuel est égal à <paramref name="other" /> ; sinon, false.</returns>
      <param name="other">Collection à comparer à l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> actif.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifie l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> actuel afin qu'il contienne uniquement les éléments présents dans l'objet actuel ou dans la collection spécifiée, mais pas dans les deux à la fois.</summary>
      <param name="other">Collection à comparer à l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> actif.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Ajoute un élément à un objet <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="item">Objet à ajouter à l'objet <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Collections.Generic.ICollection`1" /> est en lecture seule.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtient une valeur qui indique si <see cref="T:System.Collections.ICollection" /> est en lecture seule.</summary>
      <returns>true si la collection est en lecture seule ; sinon false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein d'une collection.</summary>
      <returns>Énumérateur permettant d'effectuer une itération au sein de la collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copie l'ensemble du <see cref="T:System.Collections.Generic.SortedSet`1" /> dans un tableau unidimensionnel compatible, en commençant au niveau de l'index spécifié du tableau.</summary>
      <param name="array">Tableau unidimensionnel qui constitue la destination des éléments copiés à partir de <see cref="T:System.Collections.Generic.SortedSet`1" />.Ce tableau doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans le <paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentException">Le nombre d'éléments dans le tableau source est supérieur à la quantité d'espace disponible entre <paramref name="index" /> et la fin du tableau de destination. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à zéro.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#IsSynchronized">
      <summary>Obtient une valeur qui indique si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread safe).</summary>
      <returns>true si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé ; sinon, false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#SyncRoot">
      <summary>Obtient un objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, cette propriété retourne toujours l'instance actuelle.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein d'une collection.</summary>
      <returns>Énumérateur permettant d'effectuer une itération au sein de la collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifie l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> actuel afin qu'il contienne tous les éléments présents dans l'objet actif ou dans la collection spécifiée. </summary>
      <param name="other">Collection à comparer à l'objet <see cref="T:System.Collections.Generic.SortedSet`1" /> actif.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> a la valeur null.</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1.Enumerator">
      <summary>Énumère les éléments d'un objet <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans la collection à la position actuelle de l'énumérateur.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.Dispose">
      <summary>Libère toutes les ressources utilisées par <see cref="T:System.Collections.Generic.SortedSet`1.Enumerator" />. </summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.MoveNext">
      <summary>Avance l'énumérateur à l'élément suivant de la collection <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>true si l'énumérateur a pu avancer jusqu'à l'élément suivant ; false si l'énumérateur a dépassé la fin de la collection.</returns>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans la collection à la position actuelle de l'énumérateur.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Rétablit l'énumérateur à sa position initiale, qui précède le premier élément de la collection.</summary>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="T:System.Collections.Generic.Stack`1">
      <summary>Représente une collection d'instances à taille variable de type dernier entré, premier sorti (LIFO) du même type spécifié.</summary>
      <typeparam name="T">Spécifie le type d'éléments dans la pile.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.Stack`1" /> qui est vide et possède la capacité initiale par défaut.</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.Stack`1" /> qui contient des éléments copiés à partir de la collection spécifiée et qui possède une capacité suffisante pour accepter le nombre d'éléments copiés.</summary>
      <param name="collection">Collection à partir de laquelle copier les éléments.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Generic.Stack`1" /> qui est vide et possède une capacité égale à la plus grande valeur parmi la capacité initiale spécifiée et la capacité initiale par défaut.</summary>
      <param name="capacity">Nombre initial d'éléments que <see cref="T:System.Collections.Generic.Stack`1" /> peut contenir.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Clear">
      <summary>Supprime tous les objets de <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Contains(`0)">
      <summary>Détermine si un élément est dans <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>true si <paramref name="item" /> existe dans <see cref="T:System.Collections.Generic.Stack`1" /> ; sinon, false.</returns>
      <param name="item">Objet à trouver dans <see cref="T:System.Collections.Generic.Stack`1" />.La valeur peut être null pour les types référence.</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.CopyTo(`0[],System.Int32)">
      <summary>Copie <see cref="T:System.Collections.Generic.Stack`1" /> dans un <see cref="T:System.Array" /> unidimensionnel existant, en commençant au niveau de l'index de tableau spécifié.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensionnel qui constitue la destination des éléments copiés à partir de <see cref="T:System.Collections.Generic.Stack`1" />.<see cref="T:System.Array" /> doit avoir une indexation de base zéro.</param>
      <param name="arrayIndex">Index de base zéro dans l'<paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Stack`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Count">
      <summary>Obtient le nombre d'éléments contenus dans le <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>Nombre d'éléments contenus dans <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.GetEnumerator">
      <summary>Retourne un énumérateur pour <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1.Enumerator" /> pour <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Peek">
      <summary>Retourne l'objet situé en haut de <see cref="T:System.Collections.Generic.Stack`1" /> sans le supprimer.</summary>
      <returns>Objet situé en haut de <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Pop">
      <summary>Supprime et retourne l'objet en haut de <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>Objet supprimé du haut de <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Push(`0)">
      <summary>Insère un objet en haut de <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <param name="item">Objet sur lequel un push doit être exécuté dans <see cref="T:System.Collections.Generic.Stack`1" />.La valeur peut être null pour les types référence.</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de la collection.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> qui peut être utilisé pour itérer au sein de la collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copie les éléments de <see cref="T:System.Collections.ICollection" /> dans <see cref="T:System.Array" />, à partir d'un index particulier de <see cref="T:System.Array" />.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensionnel qui constitue la destination des éléments copiés à partir de <see cref="T:System.Collections.ICollection" />.<see cref="T:System.Array" /> doit avoir une indexation de base zéro.</param>
      <param name="arrayIndex">Index de base zéro dans l'<paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#IsSynchronized">
      <summary>Obtient une valeur indiquant si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe).</summary>
      <returns>true si l'accès à <see cref="T:System.Collections.ICollection" /> est synchronisé (thread-safe) ; sinon false.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.Stack`1" />, cette propriété retourne toujours false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#SyncRoot">
      <summary>Obtient un objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objet qui peut être utilisé pour synchroniser l'accès à <see cref="T:System.Collections.ICollection" />.Dans l'implémentation par défaut de <see cref="T:System.Collections.Generic.Stack`1" />, cette propriété retourne toujours l'instance actuelle.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein d'une collection.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> qui peut être utilisé pour itérer au sein de la collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.ToArray">
      <summary>Copie <see cref="T:System.Collections.Generic.Stack`1" /> vers un nouveau tableau.</summary>
      <returns>Nouveau tableau contenant les copies des éléments de <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.TrimExcess">
      <summary>Définit la capacité au nombre réel d'éléments dans <see cref="T:System.Collections.Generic.Stack`1" />, si ce nombre est inférieur à 90 pour cent de la capacité actuelle.</summary>
    </member>
    <member name="T:System.Collections.Generic.Stack`1.Enumerator">
      <summary>Énumère les éléments de <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans <see cref="T:System.Collections.Generic.Stack`1" /> à la position actuelle de l'énumérateur.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.Dispose">
      <summary>Libère toutes les ressources utilisées par <see cref="T:System.Collections.Generic.Stack`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.MoveNext">
      <summary>Avance l'énumérateur à l'élément suivant de <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>true si l'énumérateur a pu avancer jusqu'à l'élément suivant ; false si l'énumérateur a dépassé la fin de la collection.</returns>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtient l'élément dans la position actuelle de l'énumérateur.</summary>
      <returns>Élément dans la collection à la position actuelle de l'énumérateur.</returns>
      <exception cref="T:System.InvalidOperationException">L'énumérateur précède le premier élément ou suit le dernier élément de la collection. </exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Rétablit l'énumérateur à sa position initiale, qui précède le premier élément de la collection.Cette classe ne peut pas être héritée.</summary>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur. </exception>
    </member>
  </members>
</doc>