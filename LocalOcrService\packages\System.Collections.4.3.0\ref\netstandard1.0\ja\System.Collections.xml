﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Collections</name>
  </assembly>
  <members>
    <member name="T:System.Collections.BitArray">
      <summary>ビット値の小型の配列を管理します。このビット値はブール型として表され、true はビットがオン (1)、false はビットがオフ (0) であることを示します。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Boolean[])">
      <summary>指定したブール値配列からビット値をコピーして格納する、<see cref="T:System.Collections.BitArray" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="values">コピーするブール値の配列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Byte[])">
      <summary>指定したバイト配列からビット値をコピーして格納する、<see cref="T:System.Collections.BitArray" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="bytes">コピーする値を格納しているバイト配列。各バイトは 8 個の連続ビットを表します。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="bytes" /> is greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Collections.BitArray)">
      <summary>指定した <see cref="T:System.Collections.BitArray" /> からビット値をコピーして格納する、<see cref="T:System.Collections.BitArray" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="bits">コピーする <see cref="T:System.Collections.BitArray" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bits" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32)">
      <summary>指定数のビット値を格納できる、<see cref="T:System.Collections.BitArray" /> クラスの新しいインスタンスを初期化します。ビット値は false に初期設定されます。</summary>
      <param name="length">新しい <see cref="T:System.Collections.BitArray" /> 内のビット値の数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32,System.Boolean)">
      <summary>指定数のビット値を格納できる、<see cref="T:System.Collections.BitArray" /> クラスの新しいインスタンスを初期化します。ビット値は指定値に初期設定されます。</summary>
      <param name="length">新しい <see cref="T:System.Collections.BitArray" /> 内のビット値の数。</param>
      <param name="defaultValue">各ビットに代入するブール値。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32[])">
      <summary>指定した 32 ビット整数配列からビット値をコピーして格納する、<see cref="T:System.Collections.BitArray" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="values">コピーする値を格納している整数配列。各整数は 32 個の連続ビットを表します。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="values" /> is greater than <see cref="F:System.Int32.MaxValue" /></exception>
    </member>
    <member name="M:System.Collections.BitArray.And(System.Collections.BitArray)">
      <summary>現在の <see cref="T:System.Collections.BitArray" /> 内の要素と、指定した <see cref="T:System.Collections.BitArray" /> 内の対応する要素に対して、ビットごとの AND 演算を実行します。</summary>
      <returns>現在の <see cref="T:System.Collections.BitArray" /> 内の要素と、指定した <see cref="T:System.Collections.BitArray" /> 内の対応する要素に対するビットごとの AND 演算の結果を格納する現在のインスタンス。</returns>
      <param name="value">ビットごとの AND 演算の実行対象となる <see cref="T:System.Collections.BitArray" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Get(System.Int32)">
      <summary>
        <see cref="T:System.Collections.BitArray" /> の特定位置にあるビット値を取得します。</summary>
      <returns>
        <paramref name="index" /> 位置にあるビット値。</returns>
      <param name="index">取得する値の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.BitArray" /> を反復処理する列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.BitArray" /> 全体の <see cref="T:System.Collections.IEnumerator" />。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Item(System.Int32)">
      <summary>
        <see cref="T:System.Collections.BitArray" /> の特定位置にあるビット値を取得または設定します。</summary>
      <returns>
        <paramref name="index" /> 位置にあるビット値。</returns>
      <param name="index">取得または設定する値の、0 から始まるインデックス番号。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is equal to or greater than <see cref="P:System.Collections.BitArray.Count" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Length">
      <summary>
        <see cref="T:System.Collections.BitArray" /> 内の要素の数を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Collections.BitArray" /> にある要素の数。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The property is set to a value that is less than zero. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Not">
      <summary>現在の <see cref="T:System.Collections.BitArray" /> にあるすべてのビット値を反転し、true に設定されている要素を false に、false に設定されている要素を true に変更します。</summary>
      <returns>ビット値の反転後の現在のインスタンス。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Or(System.Collections.BitArray)">
      <summary>現在の <see cref="T:System.Collections.BitArray" /> 内の要素と、指定した <see cref="T:System.Collections.BitArray" /> 内の対応する要素に対して、ビットごとの OR 演算を実行します。</summary>
      <returns>現在の <see cref="T:System.Collections.BitArray" /> 内の要素と、指定した <see cref="T:System.Collections.BitArray" /> 内の対応する要素に対するビットごとの OR 演算の結果を格納する現在のインスタンス。</returns>
      <param name="value">ビットごとの OR 演算の実行対象となる <see cref="T:System.Collections.BitArray" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Set(System.Int32,System.Boolean)">
      <summary>
        <see cref="T:System.Collections.BitArray" /> の特定位置にあるビットを指定した値に設定します。</summary>
      <param name="index">設定するビットの、0 から始まるインデックス番号。</param>
      <param name="value">ビットに代入するブール値。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.SetAll(System.Boolean)">
      <summary>
        <see cref="T:System.Collections.BitArray" /> 内のすべてのビットを指定した値に設定します。</summary>
      <param name="value">すべてのビットに代入するブール値。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>指定した <see cref="T:System.Array" /> インデックスを開始位置として、<see cref="T:System.Collections.BitArray" /> の要素を <see cref="T:System.Array" /> にコピーします。</summary>
      <param name="array">
        <see cref="T:System.Collections.BitArray" /> からコピーされる要素のコピー先となる 1 次元の <see cref="T:System.Array" />。<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
      <param name="index">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or- The number of elements in the source <see cref="T:System.Collections.BitArray" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.BitArray" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#Count">
      <summary>
        <see cref="T:System.Collections.BitArray" /> にある要素の数を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.BitArray" /> にある要素の数。</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.BitArray" /> へのアクセスが同期されている (スレッド セーフである) かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.BitArray" /> へのアクセスが同期されている (スレッド セーフである) 場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.BitArray" /> へのアクセスを同期するために使用できるオブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.BitArray" /> へのアクセスを同期するために使用できるオブジェクト。</returns>
    </member>
    <member name="M:System.Collections.BitArray.Xor(System.Collections.BitArray)">
      <summary>現在の <see cref="T:System.Collections.BitArray" /> 内の要素と、指定した <see cref="T:System.Collections.BitArray" /> 内の対応する要素に対して、ビットごとの排他的 OR 演算を実行します。</summary>
      <returns>現在の <see cref="T:System.Collections.BitArray" /> 内の要素と、指定した <see cref="T:System.Collections.BitArray" /> 内の対応する要素に対するビットごとの排他的 OR 演算の結果を格納する現在のインスタンス。</returns>
      <param name="value">ビットごとの排他的 OR 演算の実行対象となる <see cref="T:System.Collections.BitArray" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Collections.StructuralComparisons">
      <summary>2 つのコレクション オブジェクトの構造比較を実行するオブジェクトを提供します。</summary>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralComparer">
      <summary>2 つのオブジェクトの構造比較を実行する定義済みのオブジェクトを取得します。</summary>
      <returns>2 つのコレクション オブジェクトの構造比較に使用される定義済みのオブジェクト。</returns>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralEqualityComparer">
      <summary>2 つのオブジェクトの構造上の等価性を比較する定義済みのオブジェクトを取得します。</summary>
      <returns>2 つのコレクション オブジェクトの構造上の等価性を比較するのに使用される定義済みのオブジェクト。</returns>
    </member>
    <member name="T:System.Collections.Generic.Comparer`1">
      <summary>
        <see cref="T:System.Collections.Generic.IComparer`1" /> ジェネリック インターフェイスの実装のための基本クラスを提供します。</summary>
      <typeparam name="T">比較するオブジェクトの型。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.#ctor">
      <summary>
        <see cref="T:System.Collections.Generic.Comparer`1" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Compare(`0,`0)">
      <summary>派生クラスでオーバーライドされると、同じ型の 2 つのオブジェクトに対する比較を実行し、一方のオブジェクトが他方よりも小さいか、等しいか、大きいかを示す値を返します。</summary>
      <returns>
        <paramref name="x" /> と <paramref name="y" /> の相対値を示す符号付き整数。次の表を参照してください。値説明0 より小さい値<paramref name="x" /> が <paramref name="y" /> より小さい。0<paramref name="x" /> と <paramref name="y" /> が等しい。0 を超える値<paramref name="x" /> が <paramref name="y" /> より大きくなっています。</returns>
      <param name="x">比較対象の第 1 オブジェクト。</param>
      <param name="y">2 番目に比較するオブジェクト。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> 型が、<see cref="T:System.IComparable`1" /> ジェネリック インターフェイスと <see cref="T:System.IComparable" /> インターフェイスのいずれも実装していません。</exception>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Create(System.Comparison{`0})">
      <summary>指定した比較を使用して比較子を作成します。</summary>
      <returns>新しい比較子。</returns>
      <param name="comparison">使用する比較演算子です。</param>
    </member>
    <member name="P:System.Collections.Generic.Comparer`1.Default">
      <summary>汎用引数で指定された型に対して、並べ替え順序を比較するための既定の比較子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Comparer`1" /> を継承し、<paramref name="T" /> 型の並べ替え順序の比較子として機能するオブジェクト。</returns>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>2 つのオブジェクトを比較して、一方が他方より小さいか、同じか、または大きいかを示す値を返します。</summary>
      <returns>
        <paramref name="x" /> と <paramref name="y" /> の相対値を示す符号付き整数。次の表を参照してください。値説明0 より小さい値<paramref name="x" /> が <paramref name="y" /> より小さい。0<paramref name="x" /> と <paramref name="y" /> が等しい。0 を超える値<paramref name="x" /> が <paramref name="y" /> より大きくなっています。</returns>
      <param name="x">比較対象の第 1 オブジェクト。</param>
      <param name="y">2 番目に比較するオブジェクト。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> または <paramref name="y" /> が、<paramref name="T" /> 型にキャストできない型です。または<paramref name="x" /> および <paramref name="y" /> が、<see cref="T:System.IComparable`1" /> ジェネリック インターフェイスまたは <see cref="T:System.IComparable" /> インターフェイスのいずれも実装していません。</exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2">
      <summary>キーと値のコレクションを表します。この種類の .NET Framework ソース コードを参照して、次を参照してください。、 Reference Sourceです。</summary>
      <typeparam name="TKey">ディクショナリ内のキーの型。</typeparam>
      <typeparam name="TValue">ディクショナリ内の値の型。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor">
      <summary>空で、既定の初期量を備え、キーの型の既定の等値比較子を使用する、<see cref="T:System.Collections.Generic.Dictionary`2" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>指定した <see cref="T:System.Collections.Generic.IDictionary`2" /> から要素をコピーして格納し、キーの型の既定の等値比較子を使用する、<see cref="T:System.Collections.Generic.Dictionary`2" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="dictionary">新しい <see cref="T:System.Collections.Generic.IDictionary`2" /> に要素がコピーされた <see cref="T:System.Collections.Generic.Dictionary`2" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> に、1 つ以上の重複するキーが格納されています。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>指定した <see cref="T:System.Collections.Generic.IDictionary`2" /> から要素をコピーして格納し、指定した <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> を使用する、<see cref="T:System.Collections.Generic.Dictionary`2" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="dictionary">新しい <see cref="T:System.Collections.Generic.IDictionary`2" /> に要素がコピーされた <see cref="T:System.Collections.Generic.Dictionary`2" />。</param>
      <param name="comparer">キーの比較時に使用する <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 実装。キーの型の既定の null を使用する場合は <see cref="T:System.Collections.Generic.EqualityComparer`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> に、1 つ以上の重複するキーが格納されています。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>空で、既定の初期量を備え、指定した <see cref="T:System.Collections.Generic.Dictionary`2" /> を使用する、<see cref="T:System.Collections.Generic.IEqualityComparer`1" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="comparer">キーの比較時に使用する <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 実装。キーの型の既定の null を使用する場合は <see cref="T:System.Collections.Generic.EqualityComparer`1" />。</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32)">
      <summary>空で、指定した初期量を備え、キーの型の既定の等値比較子を使用する、<see cref="T:System.Collections.Generic.Dictionary`2" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.Dictionary`2" /> が格納できる要素数の初期値。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> が 0 未満です。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>空で、指定した初期量を備え、指定した <see cref="T:System.Collections.Generic.Dictionary`2" /> を使用する、<see cref="T:System.Collections.Generic.IEqualityComparer`1" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.Dictionary`2" /> が格納できる要素数の初期値。</param>
      <param name="comparer">キーの比較時に使用する <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 実装。キーの型の既定の null を使用する場合は <see cref="T:System.Collections.Generic.EqualityComparer`1" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> が 0 未満です。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Add(`0,`1)">
      <summary>指定したキーと値をディクショナリに追加します。</summary>
      <param name="key">追加する要素のキー。</param>
      <param name="value">追加する要素の値。参照型の場合は null の値を使用できます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">同じキーを持つ要素が、<see cref="T:System.Collections.Generic.Dictionary`2" /> に既に存在します。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Clear">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" /> からすべてのキーと値を削除します。</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Comparer">
      <summary>ディクショナリのキーが等しいかどうかを確認するために使用する <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> を取得します。</summary>
      <returns>現在の <see cref="T:System.Collections.Generic.Dictionary`2" /> のキーが等しいかどうかを確認し、キーのハッシュ値を提供するために使用する <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> ジェネリック インターフェイスの実装。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsKey(`0)">
      <summary>指定したキーが <see cref="T:System.Collections.Generic.Dictionary`2" /> に格納されているかどうかを判断します。</summary>
      <returns>指定したキーを持つ要素が true に格納されている場合は <see cref="T:System.Collections.Generic.Dictionary`2" />。それ以外の場合は false。</returns>
      <param name="key">
        <see cref="T:System.Collections.Generic.Dictionary`2" /> 内で検索されるキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsValue(`1)">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" /> に特定の値が格納されているかどうかを判断します。</summary>
      <returns>指定した値を持つ要素が true に格納されている場合は <see cref="T:System.Collections.Generic.Dictionary`2" />。それ以外の場合は false。</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.Dictionary`2" /> 内で検索される値。参照型の場合は null の値を使用できます。</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Count">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" /> に格納されているキー/値ペアの数を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2" /> に格納されているキー/値ペアの数。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" /> を反復処理する列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2" /> の <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" /> 構造体。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Item(`0)">
      <summary>指定されたキーに関連付けられている値を取得または設定します。</summary>
      <returns>指定されたキーに関連付けられている値。指定したキーが見つからなかった場合、get 操作は <see cref="T:System.Collections.Generic.KeyNotFoundException" /> をスローし、set 操作は指定したキーを持つ新しい要素を作成します。</returns>
      <param name="key">取得または設定する値のキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">プロパティが取得されましたが、コレクション内に <paramref name="key" /> が存在しません。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Keys">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" /> 内のキーを格納しているコレクションを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 内のキーを格納している <see cref="T:System.Collections.Generic.Dictionary`2" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Remove(`0)">
      <summary>指定したキーを持つ値を <see cref="T:System.Collections.Generic.Dictionary`2" /> から削除します。</summary>
      <returns>要素が見つかり、正常に削除された場合は true。それ以外の場合は false。このメソッドは、<paramref name="key" /> が <see cref="T:System.Collections.Generic.Dictionary`2" /> に見つからない場合、false を返します。</returns>
      <param name="key">削除する要素のキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>指定した値を、指定したキーと共に <see cref="T:System.Collections.Generic.ICollection`1" /> に追加します。</summary>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.Dictionary`2" /> に追加するキーと値を表す <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 構造体。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyValuePair" /> のキーが null です。</exception>
      <exception cref="T:System.ArgumentException">同じキーを持つ要素が、<see cref="T:System.Collections.Generic.Dictionary`2" /> に既に存在します。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> に特定のキーと値が格納されているかどうかを判断します。</summary>
      <returns>true が <paramref name="keyValuePair" /> に存在する場合は <see cref="T:System.Collections.Generic.ICollection`1" />。それ以外の場合は false。</returns>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 内で検索される <see cref="T:System.Collections.Generic.ICollection`1" /> 構造体。</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>指定した配列インデックスを開始位置として、<see cref="T:System.Collections.Generic.KeyValuePair`2" /> 型の配列に <see cref="T:System.Collections.Generic.ICollection`1" /> の要素をコピーします。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.ICollection`1" /> からコピーされる <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 要素のコピー先である <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 型の 1 次元配列。配列には、0 から始まるインデックスが設定されている必要があります。</param>
      <param name="index">コピーの開始位置とする <paramref name="array" /> のインデックス (0 から始まる)。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">コピー元の <see cref="T:System.Collections.Generic.ICollection`1" /> の要素数が、<paramref name="index" /> からコピー先の <paramref name="array" /> の末尾までに格納できる数を超えています。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>ディクショナリが読み取り専用かどうかを示す値を取得します。</summary>
      <returns>true が読み取り専用である場合は <see cref="T:System.Collections.Generic.ICollection`1" />。それ以外の場合は false。<see cref="T:System.Collections.Generic.Dictionary`2" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>ディクショナリからキーと値を削除します。</summary>
      <returns>
        <paramref name="keyValuePair" /> で表されたキーと値が見つかり、正常に削除された場合は true。それ以外の場合は false。このメソッドは、<paramref name="keyValuePair" /> が <see cref="T:System.Collections.Generic.ICollection`1" /> に見つからない場合、false を返します。</returns>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.Dictionary`2" /> から削除するキーと値を表す <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 構造体。</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> のキーを保持している <see cref="T:System.Collections.Generic.IDictionary`2" /> を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IDictionary`2" /> のキーを保持している <paramref name="TKey" /> 型の <see cref="T:System.Collections.Generic.ICollection`1" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> 内の値を格納している <see cref="T:System.Collections.Generic.IDictionary`2" /> を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IDictionary`2" /> 内の値を格納している <paramref name="TValue" /> 型の <see cref="T:System.Collections.Generic.ICollection`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>
        <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" /> のキーを格納しているコレクションを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" /> のキーを格納しているコレクション。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>
        <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" /> の値を格納しているコレクションを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" /> 内の値を格納しているコレクション。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>指定した配列インデックスを開始位置として、配列に <see cref="T:System.Collections.Generic.ICollection`1" /> の要素をコピーします。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.ICollection`1" /> から要素がコピーされる 1 次元の配列。配列には、0 から始まるインデックスが設定されている必要があります。</param>
      <param name="index">コピーの開始位置とする <paramref name="array" /> のインデックス (0 から始まる)。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> が多次元です。または<paramref name="array" /> に 0 から始まるインデックス番号がありません。またはコピー元の <see cref="T:System.Collections.Generic.ICollection`1" /> の要素数が、<paramref name="index" /> からコピー先の <paramref name="array" /> の末尾までに格納できる数を超えています。またはコピー元の <see cref="T:System.Collections.Generic.ICollection`1" /> の型が、コピー先の <paramref name="array" /> の型に自動的にキャストできません。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) かどうかを示す値を取得します。</summary>
      <returns>true へのアクセスが同期されている (スレッド セーフである) 場合は <see cref="T:System.Collections.ICollection" />。それ以外の場合は false。<see cref="T:System.Collections.Generic.Dictionary`2" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクト。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>指定したキーと値をディクショナリに追加します。</summary>
      <param name="key">キーとして使用するオブジェクト。</param>
      <param name="value">値として使用するオブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> が、<paramref name="TKey" /> のキーの型 <see cref="T:System.Collections.Generic.Dictionary`2" /> に代入できない型です。または<paramref name="value" /> が、<paramref name="TValue" /> 内の値の型である <see cref="T:System.Collections.Generic.Dictionary`2" /> に代入できない型です。または同じキーを持つ値が、<see cref="T:System.Collections.Generic.Dictionary`2" /> に既に存在します。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>指定したキーの要素が <see cref="T:System.Collections.IDictionary" /> に格納されているかどうかを確認します。</summary>
      <returns>指定したキーを持つ要素が true に格納されている場合は <see cref="T:System.Collections.IDictionary" />。それ以外の場合は false。</returns>
      <param name="key">
        <see cref="T:System.Collections.IDictionary" /> 内で検索されるキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>
        <see cref="T:System.Collections.IDictionaryEnumerator" /> の <see cref="T:System.Collections.IDictionary" /> を返します。</summary>
      <returns>
        <see cref="T:System.Collections.IDictionaryEnumerator" /> の <see cref="T:System.Collections.IDictionary" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>
        <see cref="T:System.Collections.IDictionary" /> が固定サイズかどうかを示す値を取得します。</summary>
      <returns>true が固定サイズの場合は <see cref="T:System.Collections.IDictionary" />。それ以外の場合は false。<see cref="T:System.Collections.Generic.Dictionary`2" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.IDictionary" /> が読み取り専用かどうかを示す値を取得します。</summary>
      <returns>true が読み取り専用である場合は <see cref="T:System.Collections.IDictionary" />。それ以外の場合は false。<see cref="T:System.Collections.Generic.Dictionary`2" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>指定したキーの値を取得または設定します。</summary>
      <returns>指定したキーに関連付けられた値。ただし、<paramref name="key" /> がディクショナリにない場合、または <paramref name="key" /> が <see cref="T:System.Collections.Generic.Dictionary`2" /> のキー型 <paramref name="TKey" /> に割り当てられない型である場合は null。</returns>
      <param name="key">取得する値のキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">値を代入しようとしていますが、<paramref name="key" /> は、<paramref name="TKey" /> のキーの型 <see cref="T:System.Collections.Generic.Dictionary`2" /> に代入できない型です。または値を代入しようとしていますが、<paramref name="value" /> は、<paramref name="TValue" /> の値の型 <see cref="T:System.Collections.Generic.Dictionary`2" /> に代入できない型です。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Keys">
      <summary>
        <see cref="T:System.Collections.ICollection" /> のキーを保持している <see cref="T:System.Collections.IDictionary" /> を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> のキーを保持している <see cref="T:System.Collections.IDictionary" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>指定したキーを持つ要素を <see cref="T:System.Collections.IDictionary" /> から削除します。</summary>
      <param name="key">削除する要素のキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Values">
      <summary>
        <see cref="T:System.Collections.ICollection" /> 内の値を格納している <see cref="T:System.Collections.IDictionary" /> を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> 内の値を格納している <see cref="T:System.Collections.IDictionary" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.TryGetValue(`0,`1@)">
      <summary>指定したキーに関連付けられている値を取得します。</summary>
      <returns>指定したキーを持つ要素が true に格納されている場合は <see cref="T:System.Collections.Generic.Dictionary`2" />。それ以外の場合は false。</returns>
      <param name="key">取得する値のキー。</param>
      <param name="value">このメソッドから制御が戻るときに、キーが見つかった場合は、指定したキーに関連付けられている値が格納されます。それ以外の場合は <paramref name="value" /> パラメーターの型に対する既定の値です。このパラメーターは初期化せずに渡されます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Values">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" /> 内の値を格納しているコレクションを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 内の値を格納している <see cref="T:System.Collections.Generic.Dictionary`2" />。</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" /> の要素を列挙します。</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2" /> 内の、列挙子の現在位置にある要素。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" /> によって使用されているすべてのリソースを解放します。</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.MoveNext">
      <summary>列挙子を <see cref="T:System.Collections.Generic.Dictionary`2" /> の次の要素に進めます。</summary>
      <returns>列挙子が次の要素に正常に進んだ場合は true。列挙子がコレクションの末尾を越えた場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>ディクショナリ内の列挙子の現在位置にある、<see cref="T:System.Collections.DictionaryEntry" /> としての要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>列挙子の現在位置の要素のキーを取得します。</summary>
      <returns>ディクショナリ内の列挙子の現在位置にある要素のキー。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>列挙子の現在位置の要素の値を取得します。</summary>
      <returns>ディレクショナリ内の列挙子の現在位置にある要素の値。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>コレクション内の列挙子の現在位置にある、<see cref="T:System.Object" /> としての要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>列挙子を初期位置、つまりコレクションの最初の要素の前に設定します。</summary>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" /> 内のキーのコレクションを表します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>指定した <see cref="T:System.Collections.Generic.Dictionary`2" /> 内のキーを反映する、<see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="dictionary">新しい <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> にキーが反映される <see cref="T:System.Collections.Generic.Dictionary`2" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> は null なので、</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> の要素を既存の 1 次元の <see cref="T:System.Array" /> にコピーします。コピー操作は、配列内の指定したインデックスから始まります。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> から要素がコピーされる 1 次元の <see cref="T:System.Array" />。<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
      <param name="index">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">コピー元の <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> の要素数が、<paramref name="index" /> からコピー先の <paramref name="array" /> の末尾までに格納できる数を超えています。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Count">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> に格納されている要素の数を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> に格納されている要素の数。このプロパティ値を取得することは、O(1) 操作になります。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> を反復処理する列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> の <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> に項目を追加します。この実装は常に <see cref="T:System.NotSupportedException" /> をスローします。</summary>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" /> に追加するオブジェクト。</param>
      <exception cref="T:System.NotSupportedException">常にスローされます。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> からすべての項目を削除します。この実装は常に <see cref="T:System.NotSupportedException" /> をスローします。</summary>
      <exception cref="T:System.NotSupportedException">常にスローされます。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> に特定の値が格納されているかどうかを判断します。</summary>
      <returns>
        <paramref name="item" /> が <see cref="T:System.Collections.Generic.ICollection`1" /> に存在する場合は true。それ以外の場合は false。</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" /> 内で検索するオブジェクト。</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> が読み取り専用かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> が読み取り専用である場合は true。それ以外の場合は false。<see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> の既定の実装では、このプロパティは常に true を返します。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> 内で最初に見つかった特定のオブジェクトを削除します。この実装は常に <see cref="T:System.NotSupportedException" /> をスローします。</summary>
      <returns>
        <paramref name="item" /> が <see cref="T:System.Collections.Generic.ICollection`1" /> から正常に削除された場合は true。それ以外の場合は false。このメソッドは、<paramref name="item" /> が元の <see cref="T:System.Collections.Generic.ICollection`1" /> に見つからなかった場合にも false を返します。</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" /> から削除するオブジェクト。</param>
      <exception cref="T:System.NotSupportedException">常にスローされます。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>
        <see cref="T:System.Collections.ICollection" /> の要素を <see cref="T:System.Array" /> にコピーします。<see cref="T:System.Array" /> の特定のインデックスからコピーが開始されます。</summary>
      <param name="array">
        <see cref="T:System.Collections.ICollection" /> から要素がコピーされる 1 次元の <see cref="T:System.Array" />。<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
      <param name="index">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> が多次元です。または<paramref name="array" /> に 0 から始まるインデックス番号がありません。またはコピー元の <see cref="T:System.Collections.ICollection" /> の要素数が、<paramref name="index" /> からコピー先の <paramref name="array" /> の末尾までに格納できる数を超えています。またはコピー元の <see cref="T:System.Collections.ICollection" /> の型が、コピー先の <paramref name="array" /> の型に自動的にキャストできません。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) 場合は true。それ以外の場合は false。<see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクト。<see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> の既定の実装では、このプロパティは常に現在のインスタンスを返します。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> の要素を列挙します。</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> のうち、列挙子の現在位置にある要素。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" /> によって使用されているすべてのリソースを解放します。</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>列挙子を <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> の次の要素に進めます。</summary>
      <returns>列挙子が次の要素に正常に進んだ場合は true。列挙子がコレクションの末尾を越えた場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>コレクション内の、列挙子の現在位置にある要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>列挙子を初期位置、つまりコレクションの最初の要素の前に設定します。</summary>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" /> 内の値のコレクションを表します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>指定した <see cref="T:System.Collections.Generic.Dictionary`2" /> 内の値を反映する、<see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="dictionary">新しい <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> に値が反映される <see cref="T:System.Collections.Generic.Dictionary`2" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> は null なので、</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> の要素を既存の 1 次元の <see cref="T:System.Array" /> にコピーします。コピー操作は、配列内の指定したインデックスから始まります。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> から要素がコピーされる 1 次元の <see cref="T:System.Array" />。<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
      <param name="index">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">コピー元の <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> の要素数が、<paramref name="index" /> からコピー先の <paramref name="array" /> の末尾までに格納できる数を超えています。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Count">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> に格納されている要素の数を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> に格納されている要素の数。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> を反復処理する列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> の <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> に項目を追加します。この実装は常に <see cref="T:System.NotSupportedException" /> をスローします。</summary>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" /> に追加するオブジェクト。</param>
      <exception cref="T:System.NotSupportedException">常にスローされます。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> からすべての項目を削除します。この実装は常に <see cref="T:System.NotSupportedException" /> をスローします。</summary>
      <exception cref="T:System.NotSupportedException">常にスローされます。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> に特定の値が格納されているかどうかを判断します。</summary>
      <returns>
        <paramref name="item" /> が <see cref="T:System.Collections.Generic.ICollection`1" /> に存在する場合は true。それ以外の場合は false。</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" /> 内で検索するオブジェクト。</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> が読み取り専用かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> が読み取り専用である場合は true。それ以外の場合は false。<see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> の既定の実装では、このプロパティは常に true を返します。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> 内で最初に見つかった特定のオブジェクトを削除します。この実装は常に <see cref="T:System.NotSupportedException" /> をスローします。</summary>
      <returns>
        <paramref name="item" /> が <see cref="T:System.Collections.Generic.ICollection`1" /> から正常に削除された場合は true。それ以外の場合は false。このメソッドは、<paramref name="item" /> が元の <see cref="T:System.Collections.Generic.ICollection`1" /> に見つからなかった場合にも false を返します。</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" /> から削除するオブジェクト。</param>
      <exception cref="T:System.NotSupportedException">常にスローされます。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>
        <see cref="T:System.Collections.ICollection" /> の要素を <see cref="T:System.Array" /> にコピーします。<see cref="T:System.Array" /> の特定のインデックスからコピーが開始されます。</summary>
      <param name="array">
        <see cref="T:System.Collections.ICollection" /> から要素がコピーされる 1 次元の <see cref="T:System.Array" />。<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
      <param name="index">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> が多次元です。または<paramref name="array" /> に 0 から始まるインデックス番号がありません。またはコピー元の <see cref="T:System.Collections.ICollection" /> の要素数が、<paramref name="index" /> からコピー先の <paramref name="array" /> の末尾までに格納できる数を超えています。またはコピー元の <see cref="T:System.Collections.ICollection" /> の型が、コピー先の <paramref name="array" /> の型に自動的にキャストできません。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) 場合は true。それ以外の場合は false。<see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクト。<see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> の既定の実装では、このプロパティは常に現在のインスタンスを返します。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> の要素を列挙します。</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 内の、列挙子の現在位置にある要素。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" /> によって使用されているすべてのリソースを解放します。</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>列挙子を <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> の次の要素に進めます。</summary>
      <returns>列挙子が次の要素に正常に進んだ場合は true。列挙子がコレクションの末尾を越えた場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>コレクション内の、列挙子の現在位置にある要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>列挙子を初期位置、つまりコレクションの最初の要素の前に設定します。</summary>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="T:System.Collections.Generic.EqualityComparer`1">
      <summary>
        <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> ジェネリック インターフェイスの実装のための基本クラスを提供します。</summary>
      <typeparam name="T">比較するオブジェクトの型。</typeparam>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.#ctor">
      <summary>
        <see cref="T:System.Collections.Generic.EqualityComparer`1" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="P:System.Collections.Generic.EqualityComparer`1.Default">
      <summary>汎用引数で指定された型に対して、等値であるかどうかを比較するための既定の比較子を返します。</summary>
      <returns>
        <paramref name="T" /> 型の <see cref="T:System.Collections.Generic.EqualityComparer`1" /> クラスの既定のインスタンス。</returns>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.Equals(`0,`0)">
      <summary>派生クラスでオーバーライドされた場合、<paramref name="T" /> 型の 2 つのオブジェクトが等しいかどうかを確認します。</summary>
      <returns>指定したオブジェクトが等しい場合は true。それ以外の場合は false。</returns>
      <param name="x">比較する最初のオブジェクト。</param>
      <param name="y">比較する 2 番目のオブジェクト。</param>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.GetHashCode(`0)">
      <summary>派生クラスでオーバーライドされた場合、ハッシュ アルゴリズムや、ハッシュ テーブルなどのデータ構造体の指定したオブジェクトに使用するハッシュ関数として機能します。</summary>
      <returns>指定したオブジェクトのハッシュ コード。</returns>
      <param name="obj">ハッシュ コードを取得する対象となるオブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>指定したオブジェクトが等しいかどうかを判断します。</summary>
      <returns>指定したオブジェクトが等しい場合は true。それ以外の場合は false。</returns>
      <param name="x">比較する最初のオブジェクト。</param>
      <param name="y">比較する 2 番目のオブジェクト。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> or <paramref name="y" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>指定したオブジェクトのハッシュ コードを返します。</summary>
      <returns>指定したオブジェクトのハッシュ コード。</returns>
      <param name="obj">ハッシュ コードが返される対象の <see cref="T:System.Object" />。</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.-or-<paramref name="obj" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1">
      <summary>値のセットを表します。この種類の .NET Framework ソース コードを参照して、次を参照してください。、 Reference Sourceです。</summary>
      <typeparam name="T">ハッシュ セット内の要素の型。</typeparam>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> クラスの新しいインスタンスを初期化します。初期化後のインスタンスの内容は空です。このセット型には既定の等値比較子が使用されます。</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> クラスの新しいインスタンスを初期化します。このセット型には既定の等値比較子が使用されます。指定されたコレクションからコピーされた要素が格納され、コピー対象の要素数を格納できるだけの十分な容量が確保されます。</summary>
      <param name="collection">新しいセットの要素のコピー元となるコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> クラスの新しいインスタンスを初期化します。このセット型には指定した等値比較子が使用されます。指定されたコレクションからコピーされた要素が格納され、コピー対象の要素数を格納できるだけの十分な容量が確保されます。</summary>
      <param name="collection">新しいセットの要素のコピー元となるコレクション。</param>
      <param name="comparer">セット内の値を比較する際に使用する <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> の実装。このセット型に、既定の <see cref="T:System.Collections.Generic.EqualityComparer`1" /> の実装を使用する場合は null。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> クラスの新しいインスタンスを初期化します。初期化後のインスタンスの内容は空です。このセット型には指定した等値比較子が使用されます。</summary>
      <param name="comparer">セット内の値を比較する際に使用する <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> の実装。このセット型に、既定の <see cref="T:System.Collections.Generic.EqualityComparer`1" /> の実装を使用する場合は null。</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Add(`0)">
      <summary>指定された要素をセットに追加します。</summary>
      <returns>要素が <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトに追加された場合は true。要素が既に存在していた場合は false。</returns>
      <param name="item">セットに追加する要素。</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Clear">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトからすべての要素を削除します。</summary>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Comparer">
      <summary>セット内の値が等しいかどうかを確認するために使用する <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> オブジェクトを取得します。</summary>
      <returns>セット内の値が等しいかどうかを確認するために使用する <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> オブジェクト。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Contains(`0)">
      <summary>指定した要素が <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトに含まれているかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトに指定された要素が格納されている場合は true。それ以外の場合は false。</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクト内で検索する要素。</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[])">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトの要素を配列にコピーします。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトからコピーされる要素のコピー先となる 1 次元配列。配列には、0 から始まるインデックスが設定されている必要があります。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32)">
      <summary>指定された配列インデックスを開始位置として、<see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトの要素を配列にコピーします。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトからコピーされる要素のコピー先となる 1 次元配列。配列には、0 から始まるインデックスが設定されている必要があります。</param>
      <param name="arrayIndex">コピーの開始位置とする <paramref name="array" /> のインデックス (0 から始まる)。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arrayIndex" /> が、コピー先の <paramref name="array" /> の長さを超えています。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>指定された配列インデックスを開始位置として、<see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトから、指定された数の要素を配列にコピーします。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトからコピーされる要素のコピー先となる 1 次元配列。配列には、0 から始まるインデックスが設定されている必要があります。</param>
      <param name="arrayIndex">コピーの開始位置とする <paramref name="array" /> のインデックス (0 から始まる)。</param>
      <param name="count">
        <paramref name="array" /> にコピーする要素の数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> が 0 未満です。または<paramref name="count" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arrayIndex" /> が、コピー先の <paramref name="array" /> の長さを超えています。または<paramref name="count" /> が、コピー先の <paramref name="index" /> の <paramref name="array" /> から末尾までに格納できる数を超えています。</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Count">
      <summary>セットに格納されている要素の数を取得します。</summary>
      <returns>セットに格納されている要素の数。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>現在の <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトから、指定されたコレクションに含まれる要素をすべて削除します。</summary>
      <param name="other">
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトから削除する項目のコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトを反復処理する列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトの <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" /> オブジェクト。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>現在の <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトを、そのオブジェクトと指定されたコレクションの両方に存在する要素だけが格納されるように変更します。</summary>
      <param name="other">現在の <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトと比較するコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトが、指定されたコレクションの真のサブセット (真部分集合) であるかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトが <paramref name="other" /> の真のサブセットである場合は true。それ以外の場合は false。</returns>
      <param name="other">現在の <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトと比較するコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトが、指定されたコレクションの真のスーパーセット (真上位集合) であるかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトが <paramref name="other" /> の真のスーパーセットである場合は true。それ以外の場合は false。</returns>
      <param name="other">現在の <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトと比較するコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトが、指定されたコレクションのサブセットであるかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトが <paramref name="other" /> のサブセットである場合は true。それ以外の場合は false。</returns>
      <param name="other">現在の <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトと比較するコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトが、指定されたコレクションのスーパーセットであるかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトが <paramref name="other" /> のスーパーセットである場合は true。それ以外の場合は false。</returns>
      <param name="other">現在の <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトと比較するコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>現在の <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトと指定されたコレクションとが共通の要素を共有しているかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトと <paramref name="other" /> との間に共通する要素が 1 つでも存在する場合は true。それ以外の場合は false。</returns>
      <param name="other">現在の <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトと比較するコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Remove(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトから指定された要素を削除します。</summary>
      <returns>要素が見つかり、正常に削除された場合は true。それ以外の場合は false。このメソッドは、<paramref name="item" /> が <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトに見つからない場合、false を返します。</returns>
      <param name="item">削除する要素。</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>指定の述語によって定義された条件に一致するすべての要素を <see cref="T:System.Collections.Generic.HashSet`1" /> コレクションから削除します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> コレクションから削除された要素数。</returns>
      <param name="match">削除する要素の条件を定義する <see cref="T:System.Predicate`1" /> デリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトと指定されたコレクションに同じ要素が存在するかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトが <paramref name="other" /> と等しい場合は true。それ以外の場合は false。</returns>
      <param name="other">現在の <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトと比較するコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>現在の <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトを、そのオブジェクトと指定されたコレクションの (両方に存在するのではなく) どちらか一方に存在する要素だけが格納されるように変更します。</summary>
      <param name="other">現在の <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトと比較するコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> オブジェクトに項目を追加します。</summary>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" /> オブジェクトに追加するオブジェクト。</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Collections.Generic.ICollection`1" /> は読み取り専用です。</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>コレクションが読み取り専用かどうかを示す値を取得します。</summary>
      <returns>コレクションが読み取り専用の場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.Generic.IEnumerator`1" /> オブジェクト。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.IEnumerator" /> オブジェクト。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.TrimExcess">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトの容量を、そこに格納されている実際の要素数を最も近い実装に固有の値に切り上げて設定します。</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>現在の <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトに変更を加えて、そのオブジェクト自体、指定されたコレクション、またはそれら両方に存在するすべての要素を格納するようにします。</summary>
      <param name="other">現在の <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトと比較するコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> オブジェクトの要素を列挙します。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> コレクション内の、列挙子の現在位置にある要素。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" /> オブジェクトによって使用されているすべてのリソースを解放します。</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.MoveNext">
      <summary>列挙子を <see cref="T:System.Collections.Generic.HashSet`1" /> コレクションの次の要素に進めます。</summary>
      <returns>列挙子が次の要素に正常に進んだ場合は true。列挙子がコレクションの末尾を越えた場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>コレクション内の列挙子の現在位置にある、<see cref="T:System.Object" /> としての要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>列挙子を初期位置、つまりコレクションの最初の要素の前に設定します。</summary>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1">
      <summary>ダブルリンク リストを表します。</summary>
      <typeparam name="T">リンク リストの要素の型を示します。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> クラスの新しい空のインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>指定した <see cref="T:System.Collections.IEnumerable" /> からコピーした要素を格納し、コピーされる要素の数を格納できるだけの容量を備えた、<see cref="T:System.Collections.Generic.LinkedList`1" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="collection">新しい <see cref="T:System.Collections.Generic.LinkedList`1" /> に要素がコピーされた <see cref="T:System.Collections.IEnumerable" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> は null なので、</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 内の指定した既存のノードの後に、指定した新しいノードを追加します。</summary>
      <param name="node">
        <paramref name="newNode" /> を挿入する位置の直前の <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <param name="newNode">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> に追加する新しい <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> は null なので、または<paramref name="newNode" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> は、現在の <see cref="T:System.Collections.Generic.LinkedList`1" /> に含まれていません。または<paramref name="newNode" /> は他の <see cref="T:System.Collections.Generic.LinkedList`1" /> に属しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 内の指定した既存のノードの後に、指定した値を含んだ新しいノードを追加します。</summary>
      <returns>
        <paramref name="value" /> を含んだ新しい <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</returns>
      <param name="node">
        <paramref name="value" /> を含んだ新しい <see cref="T:System.Collections.Generic.LinkedListNode`1" /> を挿入する位置の直前の <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <param name="value">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> に追加する値。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> は、現在の <see cref="T:System.Collections.Generic.LinkedList`1" /> に含まれていません。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 内の指定した既存のノードの前に、指定した新しいノードを追加します。</summary>
      <param name="node">
        <paramref name="newNode" /> を挿入する位置の直後の <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <param name="newNode">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> に追加する新しい <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> は null なので、または<paramref name="newNode" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> は、現在の <see cref="T:System.Collections.Generic.LinkedList`1" /> に含まれていません。または<paramref name="newNode" /> は他の <see cref="T:System.Collections.Generic.LinkedList`1" /> に属しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 内の指定した既存のノードの前に、指定した値を含んだ新しいノードを追加します。</summary>
      <returns>
        <paramref name="value" /> を含んだ新しい <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</returns>
      <param name="node">
        <paramref name="value" /> を含んだ新しい <see cref="T:System.Collections.Generic.LinkedListNode`1" /> を挿入する位置の直後の <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <param name="value">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> に追加する値。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> は、現在の <see cref="T:System.Collections.Generic.LinkedList`1" /> に含まれていません。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(System.Collections.Generic.LinkedListNode{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> の先頭に指定した新しいノードを追加します。</summary>
      <param name="node">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> の先頭に追加する新しい <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> は他の <see cref="T:System.Collections.Generic.LinkedList`1" /> に属しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> の先頭に、指定した値を含んだ新しいノードを追加します。</summary>
      <returns>
        <paramref name="value" /> を含んだ新しい <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> の先頭に追加する値。</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(System.Collections.Generic.LinkedListNode{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> の末尾に、指定した新しいノードを追加します。</summary>
      <param name="node">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> の末尾に追加する新しい <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> は他の <see cref="T:System.Collections.Generic.LinkedList`1" /> に属しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> の末尾に、指定した値を含んだ新しいノードを追加します。</summary>
      <returns>
        <paramref name="value" /> を含んだ新しい <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> の末尾に追加する値。</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Clear">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> からすべてのノードを削除します。</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Contains(`0)">
      <summary>ある値が <see cref="T:System.Collections.Generic.LinkedList`1" /> 内に存在するかどうかを判断します。</summary>
      <returns>
        <paramref name="value" /> が <see cref="T:System.Collections.Generic.LinkedList`1" /> に存在する場合は true。それ以外の場合は false。</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 内で検索される値。参照型の場合、null の値を使用できます。</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.CopyTo(`0[],System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 全体を互換性のある 1 次元の <see cref="T:System.Array" /> にコピーします。コピー操作は、コピー先の配列の指定したインデックスから始まります。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> から要素がコピーされる 1 次元の <see cref="T:System.Array" />。<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
      <param name="index">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">コピー元の <see cref="T:System.Collections.Generic.LinkedList`1" /> の要素数が、<paramref name="index" /> からコピー先の <paramref name="array" /> の末尾までに格納できる数を超えています。</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Count">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> に実際に格納されているノードの数を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> に実際に格納されているノードの数。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Find(`0)">
      <summary>指定した値を含む最初のノードを検索します。</summary>
      <returns>存在する場合は、指定した値を含む最初の <see cref="T:System.Collections.Generic.LinkedListNode`1" />。それ以外の場合は null。</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 内で検索される値。</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.FindLast(`0)">
      <summary>指定した値を含む最後のノードを検索します。</summary>
      <returns>存在する場合は、指定した値を含む最後の <see cref="T:System.Collections.Generic.LinkedListNode`1" />。それ以外の場合は null。</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 内で検索される値。</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.First">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> の最初のノードを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> の最初の <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> を反復処理する列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> の <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Last">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> ノードの最後のノードを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> の最後の <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(System.Collections.Generic.LinkedListNode{`0})">
      <summary>指定したノードを <see cref="T:System.Collections.Generic.LinkedList`1" /> から削除します。</summary>
      <param name="node">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> から削除する <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> は、現在の <see cref="T:System.Collections.Generic.LinkedList`1" /> に含まれていません。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 内で最初に見つかった指定の値を削除します。</summary>
      <returns>
        <paramref name="value" /> を含んだ要素が正常に削除された場合は true。それ以外の場合は false。このメソッドは、<paramref name="value" /> が元の <see cref="T:System.Collections.Generic.LinkedList`1" /> に見つからなかった場合にも false を返します。</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> から削除する値。</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveFirst">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> の先頭にあるノードを削除します。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> が空です。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveLast">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> の末尾にあるノードを削除します。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> が空です。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> の末尾にアイテムを追加します。</summary>
      <param name="value">
        <see cref="T:System.Collections.Generic.ICollection`1" /> の末尾に追加する値。</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> が読み取り専用かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> が読み取り専用である場合は true。それ以外の場合は false。<see cref="T:System.Collections.Generic.LinkedList`1" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>
        <see cref="T:System.Collections.ICollection" /> の要素を <see cref="T:System.Array" /> にコピーします。<see cref="T:System.Array" /> の特定のインデックスからコピーが開始されます。</summary>
      <param name="array">
        <see cref="T:System.Collections.ICollection" /> から要素がコピーされる 1 次元の <see cref="T:System.Array" />。<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
      <param name="index">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> が多次元です。または<paramref name="array" /> に 0 から始まるインデックス番号がありません。またはコピー元の <see cref="T:System.Collections.ICollection" /> の要素数が、<paramref name="index" /> からコピー先の <paramref name="array" /> の末尾までに格納できる数を超えています。またはコピー元の <see cref="T:System.Collections.ICollection" /> の型が、コピー先の <paramref name="array" /> の型に自動的にキャストできません。</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) 場合は true。それ以外の場合は false。<see cref="T:System.Collections.Generic.LinkedList`1" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクト。<see cref="T:System.Collections.Generic.LinkedList`1" /> の既定の実装では、このプロパティは常に現在のインスタンスを返します。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>コレクションとしてリンク リストを反復処理する列挙子を返します。</summary>
      <returns>コレクションとしてリンク リストを反復処理するために使用できる <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> の要素を列挙します。</summary>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 内の、列挙子の現在位置にある要素。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" /> によって使用されているすべてのリソースを解放します。</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.MoveNext">
      <summary>列挙子を <see cref="T:System.Collections.Generic.LinkedList`1" /> の次の要素に進めます。</summary>
      <returns>列挙子が次の要素に正常に進んだ場合は true。列挙子がコレクションの末尾を越えた場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>コレクション内の、列挙子の現在位置にある要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>列挙子を初期位置、つまりコレクションの最初の要素の前に設定します。このクラスは継承できません。</summary>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedListNode`1">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> のノードを表します。このクラスは継承できません。</summary>
      <typeparam name="T">リンク リストの要素の型を示します。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedListNode`1.#ctor(`0)">
      <summary>指定した値を含んだ <see cref="T:System.Collections.Generic.LinkedListNode`1" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="value">
        <see cref="T:System.Collections.Generic.LinkedListNode`1" /> に格納する値。</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.List">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedListNode`1" /> が属する <see cref="T:System.Collections.Generic.LinkedList`1" /> を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedListNode`1" /> が属する <see cref="T:System.Collections.Generic.LinkedList`1" /> への参照。<see cref="T:System.Collections.Generic.LinkedListNode`1" /> がリンクされていない場合は、null。</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Next">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 内の次のノードを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 内の次のノードへの参照。現在のノードが <see cref="T:System.Collections.Generic.LinkedList`1" /> の最後の要素 (<see cref="P:System.Collections.Generic.LinkedList`1.Last" />) である場合は、null。</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Previous">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 内の前のノードを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 内の前のノードへの参照。現在のノードが <see cref="T:System.Collections.Generic.LinkedList`1" /> の最初の要素 (<see cref="P:System.Collections.Generic.LinkedList`1.First" />) である場合は、null。</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Value">
      <summary>ノードに格納された値を取得します。</summary>
      <returns>ノードに格納された値。</returns>
    </member>
    <member name="T:System.Collections.Generic.List`1">
      <summary>インデックスを使用してアクセスできる、厳密に型指定されたオブジェクトのリストを表します。リストの検索、並べ替え、および操作のためのメソッドを提供します。この種類の .NET Framework ソース コードを参照して、次を参照してください。、参照ソースです。</summary>
      <typeparam name="T">リスト内の要素の型。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor">
      <summary>空で、既定の初期量を備えた、<see cref="T:System.Collections.Generic.List`1" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>指定したコレクションからコピーした要素を格納し、コピーされる要素の数を格納できるだけの容量を備えた、<see cref="T:System.Collections.Generic.List`1" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="collection">新しいリストに要素がコピーされたコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Int32)">
      <summary>空で、指定した初期量を備えた、<see cref="T:System.Collections.Generic.List`1" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="capacity">新しいリストに格納できる要素の数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> が 0 未満です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Add(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> の末尾にオブジェクトを追加します。</summary>
      <param name="item">
        <see cref="T:System.Collections.Generic.List`1" /> の末尾に追加するオブジェクト。参照型の場合は null の値を使用できます。</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>指定したコレクションの要素を <see cref="T:System.Collections.Generic.List`1" /> の末尾に追加します。</summary>
      <param name="collection">
        <see cref="T:System.Collections.Generic.List`1" /> の末尾に要素が追加されるコレクション。コレクション自体を null にすることはできませんが、型 <paramref name="T" /> が参照型の場合、コレクションに格納する要素は null であってもかまいません。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.AsReadOnly">
      <summary>現在のコレクションの読み取り専用の <see cref="T:System.Collections.Generic.IList`1" /> ラッパーを返します。</summary>
      <returns>現在の <see cref="T:System.Collections.Generic.List`1" /> をラップする読み取り専用のラッパーとして動作する <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(System.Int32,System.Int32,`0,System.Collections.Generic.IComparer{`0})">
      <summary>指定した比較子を使用して、並べ替えられた要素の <see cref="T:System.Collections.Generic.List`1" /> の 1 つの要素の範囲を検索し、その要素の 0 から始まるインデックスを返します。</summary>
      <returns>
        <paramref name="item" /> が見つかった場合は、並べ替えられた <see cref="T:System.Collections.Generic.List`1" /> 内の <paramref name="item" /> の 0 から始まるインデックス。見つからなかった場合は、負の値。これは、<paramref name="item" /> の次に大きい要素のインデックスのビットごとの補数です。ただし、大きい要素が存在しない場合は、<see cref="P:System.Collections.Generic.List`1.Count" /> のビットごとの補数です。</returns>
      <param name="index">検索範囲の開始位置を示す 0 から始まるインデックス。</param>
      <param name="count">検索する範囲の長さ。</param>
      <param name="item">検索するオブジェクト。参照型の場合は null の値を使用できます。</param>
      <param name="comparer">要素を比較する場合に使用する <see cref="T:System.Collections.Generic.IComparer`1" /> 実装。または、既定の比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> を使用する場合は null。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。または<paramref name="count" /> が 0 未満です。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> および <paramref name="count" /> が <see cref="T:System.Collections.Generic.List`1" /> 内の有効な範囲を示していません。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> が null です。また、既定の比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> は、<see cref="T:System.IComparable`1" /> ジェネリック インターフェイスの実装、または型 <paramref name="T" /> の <see cref="T:System.IComparable" /> インターフェイスの実装を見つけることができません。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0)">
      <summary>既定の比較子を使用して、並べ替えられた要素の <see cref="T:System.Collections.Generic.List`1" /> 全体を検索し、その要素の 0 から始まるインデックスを返します。</summary>
      <returns>
        <paramref name="item" /> が見つかった場合は、並べ替えられた <see cref="T:System.Collections.Generic.List`1" /> 内の <paramref name="item" /> の 0 から始まるインデックス。見つからなかった場合は、負の値。これは、<paramref name="item" /> の次に大きい要素のインデックスのビットごとの補数です。ただし、大きい要素が存在しない場合は、<see cref="P:System.Collections.Generic.List`1.Count" /> のビットごとの補数です。</returns>
      <param name="item">検索するオブジェクト。参照型の場合は null の値を使用できます。</param>
      <exception cref="T:System.InvalidOperationException">既定の比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> は、<see cref="T:System.IComparable`1" /> ジェネリック インターフェイスの実装、または型 <paramref name="T" /> の <see cref="T:System.IComparable" /> インターフェイスの実装を見つけることができません。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0,System.Collections.Generic.IComparer{`0})">
      <summary>指定した比較子を使用して、並べ替えられた要素の <see cref="T:System.Collections.Generic.List`1" /> 全体を検索し、その要素の 0 から始まるインデックスを返します。</summary>
      <returns>
        <paramref name="item" /> が見つかった場合は、並べ替えられた <see cref="T:System.Collections.Generic.List`1" /> 内の <paramref name="item" /> の 0 から始まるインデックス。見つからなかった場合は、負の値。これは、<paramref name="item" /> の次に大きい要素のインデックスのビットごとの補数です。ただし、大きい要素が存在しない場合は、<see cref="P:System.Collections.Generic.List`1.Count" /> のビットごとの補数です。</returns>
      <param name="item">検索するオブジェクト。参照型の場合は null の値を使用できます。</param>
      <param name="comparer">要素を比較する場合に使用する <see cref="T:System.Collections.Generic.IComparer`1" /> 実装。または既定の比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> を使用する場合は null。</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> が null です。また、既定の比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> は、<see cref="T:System.IComparable`1" /> ジェネリック インターフェイスの実装、または型 <paramref name="T" /> の <see cref="T:System.IComparable" /> インターフェイスの実装を見つけることができません。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Capacity">
      <summary>内部データ構造体がサイズ変更せずに格納できる要素の合計数を取得または設定します。</summary>
      <returns>サイズ変更が必要となるまでに <see cref="T:System.Collections.Generic.List`1" /> に格納できる要素の数。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Collections.Generic.List`1.Capacity" /> が <see cref="P:System.Collections.Generic.List`1.Count" /> より小さい値に設定されています。</exception>
      <exception cref="T:System.OutOfMemoryException">システムのメモリが不足しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Clear">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> からすべての要素を削除します。</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Contains(`0)">
      <summary>ある要素が <see cref="T:System.Collections.Generic.List`1" /> 内に存在するかどうかを判断します。</summary>
      <returns>true if <paramref name="item" /> is found in the <see cref="T:System.Collections.Generic.List`1" />; otherwise, false.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.List`1" /> 内で検索するオブジェクト。参照型の場合は null の値を使用できます。</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(System.Int32,`0[],System.Int32,System.Int32)">
      <summary>要素の範囲を <see cref="T:System.Collections.Generic.List`1" /> から互換性のある 1 次元の配列にコピーします。コピー操作は、コピー先の配列の指定したインデックスから始まります。</summary>
      <param name="index">コピーを開始するコピー元の <see cref="T:System.Collections.Generic.List`1" /> 内の、0 から始まるインデックス番号。</param>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.List`1" />.<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
      <param name="arrayIndex">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <param name="count">コピーする要素の数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null です。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。または<paramref name="arrayIndex" /> が 0 未満です。または<paramref name="count" /> が 0 未満です。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> が、コピー元の <see cref="T:System.Collections.Generic.List`1" /> の <see cref="P:System.Collections.Generic.List`1.Count" /> 以上です。またはコピー元の <see cref="T:System.Collections.Generic.List`1" /> の <paramref name="index" /> から末尾までの要素の数が、コピー先の <paramref name="array" /> の <paramref name="arrayIndex" /> から末尾までに格納できる数よりも大きくなっています。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[])">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> 全体を互換性のある 1 次元の配列にコピーします。コピー操作は、コピー先の配列の先頭から始まります。</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.List`1" />.<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">コピー元の <see cref="T:System.Collections.Generic.List`1" /> の要素数が、コピー先の <paramref name="array" /> に格納できる要素の数を超えています。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[],System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> 全体を、互換性のある 1 次元配列の、指定したインデックスから始まる位置にコピーします。</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.List`1" />.<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
      <param name="arrayIndex">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">コピー元の <see cref="T:System.Collections.Generic.List`1" /> の要素数が、<paramref name="arrayIndex" /> からコピー先の <paramref name="array" /> の末尾までに格納できる数を超えています。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Count">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> に格納されている要素の数を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" /> に格納されている要素の数。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Exists(System.Predicate{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> に、指定された述語によって定義された条件と一致する要素が含まれているかどうかを判断します。</summary>
      <returns>指定された述語によって定義された条件と一致する要素が少なくとも 1 つ、<see cref="T:System.Collections.Generic.List`1" /> に存在する場合は、true。それ以外の場合は false。</returns>
      <param name="match">検索する要素の条件を定義する <see cref="T:System.Predicate`1" /> デリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Find(System.Predicate{`0})">
      <summary>指定された述語によって定義された条件と一致する要素を検索し、<see cref="T:System.Collections.Generic.List`1" /> 全体の中で最もインデックス番号の小さい要素を返します。</summary>
      <returns>見つかった場合は、指定された述語によって定義された条件と一致する最初の要素。それ以外の場合は、型 <paramref name="T" /> の既定値。</returns>
      <param name="match">検索する要素の条件を定義する <see cref="T:System.Predicate`1" /> デリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindAll(System.Predicate{`0})">
      <summary>指定された述語によって定義された条件と一致するすべての要素を取得します。</summary>
      <returns>指定した述語によって定義される条件に一致する要素が見つかった場合は、そのすべての要素を格納する <see cref="T:System.Collections.Generic.List`1" />。それ以外の場合は、空の <see cref="T:System.Collections.Generic.List`1" />。</returns>
      <param name="match">検索する要素の条件を定義する <see cref="T:System.Predicate`1" /> デリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> の指定したインデックスから指定した要素数までの範囲内で、指定した述語によって定義される条件に一致する要素を検索し、最もインデックス番号の小さい要素の 0 から始まるインデックスを返します。</summary>
      <returns>
        <paramref name="match" /> で定義された条件と一致する要素が存在した場合、最もインデックス番号の小さい要素の 0 から始まるインデックス。それ以外の場合は –1。</returns>
      <param name="startIndex">検索の開始位置を示す 0 から始まるインデックス。</param>
      <param name="count">検索対象の範囲内にある要素の数。</param>
      <param name="match">検索する要素の条件を定義する <see cref="T:System.Predicate`1" /> デリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> が、<see cref="T:System.Collections.Generic.List`1" /> の有効なインデックスの範囲外です。または<paramref name="count" /> が 0 未満です。または<paramref name="startIndex" /> および <paramref name="count" /> が <see cref="T:System.Collections.Generic.List`1" /> 内の有効な部分を指定していません。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Predicate{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> の指定したインデックスから最後の要素までの範囲内で、指定した述語によって定義される条件に一致する要素を検索し、最もインデックス番号の小さい要素の 0 から始まるインデックスを返します。</summary>
      <returns>
        <paramref name="match" /> で定義された条件と一致する要素が存在した場合、最もインデックス番号の小さい要素の 0 から始まるインデックス。それ以外の場合は –1。</returns>
      <param name="startIndex">検索の開始位置を示す 0 から始まるインデックス。</param>
      <param name="match">検索する要素の条件を定義する <see cref="T:System.Predicate`1" /> デリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> が、<see cref="T:System.Collections.Generic.List`1" /> の有効なインデックスの範囲外です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Predicate{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> 全体から、指定した述語によって定義される条件に一致する要素を検索し、最もインデックス番号の小さい要素の 0 から始まるインデックスを返します。</summary>
      <returns>
        <paramref name="match" /> で定義された条件と一致する要素が存在した場合、最もインデックス番号の小さい要素の 0 から始まるインデックス。それ以外の場合は –1。</returns>
      <param name="match">検索する要素の条件を定義する <see cref="T:System.Predicate`1" /> デリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLast(System.Predicate{`0})">
      <summary>指定された述語によって定義された条件と一致する要素を、<see cref="T:System.Collections.Generic.List`1" /> 全体を対象に検索し、最もインデックス番号の大きい要素を返します。</summary>
      <returns>見つかった場合は、指定された述語によって定義された条件と一致する最後の要素。それ以外の場合は、型 <paramref name="T" /> の既定値。</returns>
      <param name="match">検索する要素の条件を定義する <see cref="T:System.Predicate`1" /> デリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> の指定したインデックスで終わる指定した要素数の範囲内で、指定した述語によって定義される条件に一致する要素を検索し、最もインデックス番号の大きい要素の 0 から始まるインデックスを返します。</summary>
      <returns>
        <paramref name="match" /> で定義された条件と一致する要素が存在する場合、最もインデックス番号の大きい要素の 0 から始まるインデックス。それ以外の場合は –1。</returns>
      <param name="startIndex">後方検索の開始位置を示す 0 から始まるインデックス。</param>
      <param name="count">検索対象の範囲内にある要素の数。</param>
      <param name="match">検索する要素の条件を定義する <see cref="T:System.Predicate`1" /> デリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> が、<see cref="T:System.Collections.Generic.List`1" /> の有効なインデックスの範囲外です。または<paramref name="count" /> が 0 未満です。または<paramref name="startIndex" /> および <paramref name="count" /> が <see cref="T:System.Collections.Generic.List`1" /> 内の有効な部分を指定していません。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Predicate{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> の先頭の要素から指定したインデックスまでの範囲内で、指定した述語によって定義される条件に一致する要素を検索し、最もインデックス番号の大きい要素の 0 から始まるインデックスを返します。</summary>
      <returns>
        <paramref name="match" /> で定義された条件と一致する要素が存在する場合、最もインデックス番号の大きい要素の 0 から始まるインデックス。それ以外の場合は –1。</returns>
      <param name="startIndex">後方検索の開始位置を示す 0 から始まるインデックス。</param>
      <param name="match">検索する要素の条件を定義する <see cref="T:System.Predicate`1" /> デリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> が、<see cref="T:System.Collections.Generic.List`1" /> の有効なインデックスの範囲外です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Predicate{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> 全体から、指定した述語によって定義される条件に一致する要素を検索し、最もインデックス番号の大きい要素の 0 から始まるインデックスを返します。</summary>
      <returns>
        <paramref name="match" /> で定義された条件と一致する要素が存在する場合、最もインデックス番号の大きい要素の 0 から始まるインデックス。それ以外の場合は –1。</returns>
      <param name="match">検索する要素の条件を定義する <see cref="T:System.Predicate`1" /> デリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ForEach(System.Action{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> の各要素に対して、指定された処理を実行します。</summary>
      <param name="action">
        <see cref="T:System.Collections.Generic.List`1" /> の各要素に対して実行する <see cref="T:System.Action`1" /> デリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> を反復処理する列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" /> の <see cref="T:System.Collections.Generic.List`1.Enumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetRange(System.Int32,System.Int32)">
      <summary>コピー元の <see cref="T:System.Collections.Generic.List`1" /> 内の、ある範囲の要素の簡易コピーを作成します。</summary>
      <returns>コピー元の <see cref="T:System.Collections.Generic.List`1" /> 内の、ある範囲の要素の簡易コピー。</returns>
      <param name="index">範囲が開始する位置の、0 から始まる <see cref="T:System.Collections.Generic.List`1" /> のインデックス番号。</param>
      <param name="count">範囲内の要素の数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。または<paramref name="count" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> および <paramref name="count" /> が <see cref="T:System.Collections.Generic.List`1" /> 内の要素の有効範囲を示していません。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0)">
      <summary>指定したオブジェクトを検索し、<see cref="T:System.Collections.Generic.List`1" /> 全体内で最初に見つかった位置の 0 から始まるインデックスを返します。</summary>
      <returns>The zero-based index of the first occurrence of <paramref name="item" /> within the entire <see cref="T:System.Collections.Generic.List`1" />, if found; otherwise, –1.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.List`1" /> 内で検索するオブジェクト。参照型の場合は null の値を使用できます。</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32)">
      <summary>指定したオブジェクトを検索し、指定したインデックスから最後の要素までの <see cref="T:System.Collections.Generic.List`1" /> 内の要素の範囲内で最初に出現する位置の 0 から始まるインデックス番号を返します。</summary>
      <returns>
        <paramref name="index" /> から最後の要素までの <see cref="T:System.Collections.Generic.List`1" /> 内の要素の範囲内で <paramref name="item" /> が見つかった場合は、最初に見つかった位置の 0 から始まるインデックス番号。それ以外の場合は –1。</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.List`1" /> 内で検索するオブジェクト。参照型の場合は null の値を使用できます。</param>
      <param name="index">検索の開始位置を示す 0 から始まるインデックス。空のリストでは 0 (ゼロ) は有効です。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が、<see cref="T:System.Collections.Generic.List`1" /> の有効なインデックスの範囲外です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32,System.Int32)">
      <summary>指定したオブジェクトを検索し、指定したインデックスから始まって指定した数の要素を格納する <see cref="T:System.Collections.Generic.List`1" /> 内の要素の範囲内で最初に出現する位置の 0 から始まるインデックス番号を返します。</summary>
      <returns>
        <paramref name="index" /> から始まって <paramref name="count" /> 個の要素を格納する <see cref="T:System.Collections.Generic.List`1" /> 内の要素の範囲内で <paramref name="item" /> が見つかった場合は、最初に見つかった位置の 0 から始まるインデックス番号。それ以外の場合は –1。</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.List`1" /> 内で検索するオブジェクト。参照型の場合は null の値を使用できます。</param>
      <param name="index">検索の開始位置を示す 0 から始まるインデックス。空のリストでは 0 (ゼロ) は有効です。</param>
      <param name="count">検索対象の範囲内にある要素の数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が、<see cref="T:System.Collections.Generic.List`1" /> の有効なインデックスの範囲外です。または<paramref name="count" /> が 0 未満です。または<paramref name="index" /> および <paramref name="count" /> が <see cref="T:System.Collections.Generic.List`1" /> 内の有効な部分を指定していません。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Insert(System.Int32,`0)">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> 内の指定したインデックスの位置に要素を挿入します。</summary>
      <param name="index">
        <paramref name="item" /> を挿入する位置の、0 から始まるインデックス番号。</param>
      <param name="item">挿入するオブジェクト。参照型の場合は null の値を使用できます。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。または<paramref name="index" /> が <see cref="P:System.Collections.Generic.List`1.Count" /> より大きくなっています。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
      <summary>コレクションの要素を <see cref="T:System.Collections.Generic.List`1" /> 内の指定したインデックスの位置に挿入します。</summary>
      <param name="index">新しい要素が挿入される位置の 0 から始まるインデックス。</param>
      <param name="collection">
        <see cref="T:System.Collections.Generic.List`1" /> に要素を挿入するコレクション。コレクション自体を null にすることはできませんが、型 <paramref name="T" /> が参照型の場合、コレクションに格納する要素は null であってもかまいません。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。または<paramref name="index" /> が <see cref="P:System.Collections.Generic.List`1.Count" /> より大きくなっています。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Item(System.Int32)">
      <summary>指定したインデックスにある要素を取得または設定します。</summary>
      <returns>指定したインデックス位置にある要素。</returns>
      <param name="index">取得または設定する要素の、0 から始まるインデックス番号。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。または<paramref name="index" /> が <see cref="P:System.Collections.Generic.List`1.Count" /> 以上です。 </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0)">
      <summary>指定したオブジェクトを検索し、<see cref="T:System.Collections.Generic.List`1" /> 全体内で最後に見つかった位置の 0 から始まるインデックスを返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" /> 全体内で <paramref name="item" /> が見つかった場合は、最後に見つかった位置の 0 から始まるインデックス番号。それ以外の場合は –1。</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.List`1" /> 内で検索するオブジェクト。参照型の場合は null の値を使用できます。</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32)">
      <summary>指定したオブジェクトを検索し、最初の要素から、指定したインデックスまでの <see cref="T:System.Collections.Generic.List`1" /> 内の要素の範囲内で最後に出現する位置の 0 から始まるインデックス番号を返します。</summary>
      <returns>最初の要素から <paramref name="index" /> までの <see cref="T:System.Collections.Generic.List`1" /> 内の要素の範囲内で <paramref name="item" /> が見つかった場合は、最後に見つかった位置の 0 から始まるインデックス番号。それ以外の場合は –1。</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.List`1" /> 内で検索するオブジェクト。参照型の場合は null の値を使用できます。</param>
      <param name="index">後方検索の開始位置を示す 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が、<see cref="T:System.Collections.Generic.List`1" /> の有効なインデックスの範囲外です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32,System.Int32)">
      <summary>指定したオブジェクトを検索して、指定した数の要素を格納し、指定したインデックスの位置で終了する <see cref="T:System.Collections.Generic.List`1" /> 内の要素の範囲内で最後に出現する位置の 0 から始まるインデックス番号を返します。</summary>
      <returns>
        <paramref name="count" /> 個の要素を格納し、<paramref name="index" /> の位置で終了する <see cref="T:System.Collections.Generic.List`1" /> 内の要素の範囲内で <paramref name="item" /> が見つかった場合は、最後に見つかった位置の 0 から始まるインデックス番号。それ以外の場合は –1。</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.List`1" /> 内で検索するオブジェクト。参照型の場合は null の値を使用できます。</param>
      <param name="index">後方検索の開始位置を示す 0 から始まるインデックス。</param>
      <param name="count">検索対象の範囲内にある要素の数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が、<see cref="T:System.Collections.Generic.List`1" /> の有効なインデックスの範囲外です。または<paramref name="count" /> が 0 未満です。または<paramref name="index" /> および <paramref name="count" /> が <see cref="T:System.Collections.Generic.List`1" /> 内の有効な部分を指定していません。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Remove(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> 内で最初に見つかった特定のオブジェクトを削除します。</summary>
      <returns>
        <paramref name="item" /> が正常に削除された場合は true。それ以外の場合は false。This method also returns false if <paramref name="item" /> was not found in the <see cref="T:System.Collections.Generic.List`1" />.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.List`1" /> から削除するオブジェクト。参照型の場合は null の値を使用できます。</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAll(System.Predicate{`0})">
      <summary>指定した述語によって定義される条件に一致するすべての要素を削除します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" /> から削除される要素の数。</returns>
      <param name="match">削除する要素の条件を定義する <see cref="T:System.Predicate`1" /> デリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAt(System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> の指定したインデックスにある要素を削除します。</summary>
      <param name="index">削除する要素の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。または<paramref name="index" /> が <see cref="P:System.Collections.Generic.List`1.Count" /> 以上です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveRange(System.Int32,System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> から要素の範囲を削除します。</summary>
      <param name="index">削除する要素の範囲の開始位置を示す 0 から始まるインデックス。</param>
      <param name="count">削除する要素の数を指定します。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。または<paramref name="count" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> および <paramref name="count" /> が <see cref="T:System.Collections.Generic.List`1" /> 内の要素の有効範囲を示していません。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> 全体の要素の順序を反転させます。</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse(System.Int32,System.Int32)">
      <summary>指定した範囲の要素の順序を反転させます。</summary>
      <param name="index">反転させる範囲の開始位置を示す 0 から始まるインデックス。</param>
      <param name="count">反転させる範囲内にある要素の数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。または<paramref name="count" /> が 0 未満です。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> および <paramref name="count" /> が <see cref="T:System.Collections.Generic.List`1" /> 内の要素の有効範囲を示していません。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort">
      <summary>既定の比較子を使用して、<see cref="T:System.Collections.Generic.List`1" /> 全体内の要素を並べ替えます。</summary>
      <exception cref="T:System.InvalidOperationException">既定の比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> は、<see cref="T:System.IComparable`1" /> ジェネリック インターフェイスの実装、または型 <paramref name="T" /> の <see cref="T:System.IComparable" /> インターフェイスの実装を見つけることができません。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Collections.Generic.IComparer{`0})">
      <summary>指定した比較子を使用して、<see cref="T:System.Collections.Generic.List`1" /> 全体内の要素を並べ替えます。</summary>
      <param name="comparer">要素を比較する場合に使用する <see cref="T:System.Collections.Generic.IComparer`1" /> 実装。または、既定の比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> を使用する場合は null。</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> が null です。また、既定の比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> は、<see cref="T:System.IComparable`1" /> ジェネリック インターフェイスの実装、または型 <paramref name="T" /> の <see cref="T:System.IComparable" /> インターフェイスの実装を見つけることができません。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="comparer" /> の実装によって並べ替え中にエラーが発生しました。たとえば、<paramref name="comparer" /> は項目を項目自身と比較する場合に 0 を返さない可能性があります。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Comparison{`0})">
      <summary>指定した <see cref="T:System.Comparison`1" /> を使用して、<see cref="T:System.Collections.Generic.List`1" /> 全体内の要素を並べ替えます。</summary>
      <param name="comparison">要素を比較する場合に使用する <see cref="T:System.Comparison`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparison" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="comparison" /> の実装によって並べ替え中にエラーが発生しました。たとえば、<paramref name="comparison" /> は項目を項目自身と比較する場合に 0 を返さない可能性があります。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Int32,System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>指定した比較子を使用して、<see cref="T:System.Collections.Generic.List`1" /> 内の要素の範囲内の要素を並べ替えます。</summary>
      <param name="index">並べ替える範囲の開始位置を示す 0 から始まるインデックス。</param>
      <param name="count">並べ替える範囲の長さ。</param>
      <param name="comparer">要素を比較する場合に使用する <see cref="T:System.Collections.Generic.IComparer`1" /> 実装。または、既定の比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> を使用する場合は null。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。または<paramref name="count" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> および <paramref name="count" /> が <see cref="T:System.Collections.Generic.List`1" /> 内の有効な範囲を指定していません。または<paramref name="comparer" /> の実装によって並べ替え中にエラーが発生しました。たとえば、<paramref name="comparer" /> は項目を項目自身と比較する場合に 0 を返さない可能性があります。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> が null です。また、既定の比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> は、<see cref="T:System.IComparable`1" /> ジェネリック インターフェイスの実装、または型 <paramref name="T" /> の <see cref="T:System.IComparable" /> インターフェイスの実装を見つけることができません。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> が読み取り専用かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> が読み取り専用の場合は true。それ以外の場合は false。<see cref="T:System.Collections.Generic.List`1" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>
        <see cref="T:System.Collections.ICollection" /> の要素を <see cref="T:System.Array" /> にコピーします。<see cref="T:System.Array" /> の特定のインデックスからコピーが開始されます。</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection" />.<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
      <param name="arrayIndex">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> が多次元です。または<paramref name="array" /> に 0 から始まるインデックス番号がありません。またはコピー元の <see cref="T:System.Collections.ICollection" /> の要素数が、<paramref name="arrayIndex" /> からコピー先の <paramref name="array" /> の末尾までに格納できる数を超えています。またはコピー元の <see cref="T:System.Collections.ICollection" /> の型が、コピー先の <paramref name="array" /> の型に自動的にキャストできません。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) かどうかを示す値を取得します。</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.<see cref="T:System.Collections.Generic.List`1" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクト。<see cref="T:System.Collections.Generic.List`1" /> の既定の実装では、このプロパティは常に現在のインスタンスを返します。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Add(System.Object)">
      <summary>
        <see cref="T:System.Collections.IList" /> に項目を追加します。</summary>
      <returns>新しい要素が挿入された位置。</returns>
      <param name="item">
        <see cref="T:System.Collections.IList" /> に追加する <see cref="T:System.Object" />。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> が、<see cref="T:System.Collections.IList" /> に代入できない型です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Contains(System.Object)">
      <summary>
        <see cref="T:System.Collections.IList" /> に特定の値が格納されているかどうかを判断します。</summary>
      <returns>true if <paramref name="item" /> is found in the <see cref="T:System.Collections.IList" />; otherwise, false.</returns>
      <param name="item">
        <see cref="T:System.Collections.IList" /> 内で検索される <see cref="T:System.Object" />。</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#IndexOf(System.Object)">
      <summary>
        <see cref="T:System.Collections.IList" /> 内での指定した項目のインデックスを調べます。</summary>
      <returns>リストに存在する場合は <paramref name="item" /> のインデックス。それ以外の場合は -1。</returns>
      <param name="item">
        <see cref="T:System.Collections.IList" /> 内で検索するオブジェクト。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> が、<see cref="T:System.Collections.IList" /> に代入できない型です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>指定したインデックスの <see cref="T:System.Collections.IList" /> に項目を挿入します。</summary>
      <param name="index">
        <paramref name="item" /> を挿入する位置の、0 から始まるインデックス番号。</param>
      <param name="item">
        <see cref="T:System.Collections.IList" /> に挿入するオブジェクト。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が <see cref="T:System.Collections.IList" /> の有効なインデックスではありません。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> が、<see cref="T:System.Collections.IList" /> に代入できない型です。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsFixedSize">
      <summary>
        <see cref="T:System.Collections.IList" /> が固定サイズかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.IList" /> が固定サイズの場合は true。それ以外の場合は false。<see cref="T:System.Collections.Generic.List`1" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.IList" /> が読み取り専用かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.IList" /> が読み取り専用の場合は true。それ以外の場合は false。<see cref="T:System.Collections.Generic.List`1" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#Item(System.Int32)">
      <summary>指定したインデックスにある要素を取得または設定します。</summary>
      <returns>指定したインデックス位置にある要素。</returns>
      <param name="index">取得または設定する要素の、0 から始まるインデックス番号。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が <see cref="T:System.Collections.IList" /> の有効なインデックスではありません。</exception>
      <exception cref="T:System.ArgumentException">プロパティが設定されていて、<paramref name="value" /> が、<see cref="T:System.Collections.IList" /> に代入できない型です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Remove(System.Object)">
      <summary>
        <see cref="T:System.Collections.IList" /> 内で最初に見つかった特定のオブジェクトを削除します。</summary>
      <param name="item">
        <see cref="T:System.Collections.IList" /> から削除するオブジェクト。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> が、<see cref="T:System.Collections.IList" /> に代入できない型です。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ToArray">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> の要素を新しい配列にコピーします。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" /> の要素のコピーを格納する配列。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrimExcess">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> 内にある実際の要素数がしきい値未満の場合は、容量をその数に設定します。</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrueForAll(System.Predicate{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> 内のすべての要素が、指定した述語によって定義される条件に一致するかどうかを調べます。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" /> 内のすべての要素が、指定した述語によって定義される条件に一致する場合は true。それ以外の場合は false。リストに要素がない場合、戻り値は true です。</returns>
      <param name="match">要素の条件を定義する <see cref="T:System.Predicate`1" /> デリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> は null です。</exception>
    </member>
    <member name="T:System.Collections.Generic.List`1.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" /> の要素を列挙します。</summary>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" /> 内の、列挙子の現在位置にある要素。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.List`1.Enumerator" /> によって使用されているすべてのリソースを解放します。</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.MoveNext">
      <summary>列挙子を <see cref="T:System.Collections.Generic.List`1" /> の次の要素に進めます。</summary>
      <returns>列挙子が次の要素に正常に進んだ場合は true。列挙子がコレクションの末尾を越えた場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" /> 内の、列挙子の現在位置にある要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>列挙子を初期位置、つまりコレクションの最初の要素の前に設定します。</summary>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="T:System.Collections.Generic.Queue`1">
      <summary>オブジェクトの先入れ先出しコレクションを表します。</summary>
      <typeparam name="T">キュー内の要素の型を指定します。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor">
      <summary>空で、既定の初期量を備えた、<see cref="T:System.Collections.Generic.Queue`1" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>指定したコレクションからコピーした要素を格納し、コピーされる要素の数を格納できるだけの容量を備えた、<see cref="T:System.Collections.Generic.Queue`1" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="collection">新しい <see cref="T:System.Collections.Generic.Queue`1" /> に要素がコピーされたコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Int32)">
      <summary>空で、指定した初期量を備えた、<see cref="T:System.Collections.Generic.Queue`1" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.Queue`1" /> が格納できる要素数の初期値。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Clear">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1" /> からすべてのオブジェクトを削除します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Contains(`0)">
      <summary>ある要素が <see cref="T:System.Collections.Generic.Queue`1" /> 内に存在するかどうかを判断します。</summary>
      <returns>
        <paramref name="item" /> が <see cref="T:System.Collections.Generic.Queue`1" /> に存在する場合は true。それ以外の場合は false。</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.Queue`1" /> 内で検索するオブジェクト。参照型の場合は null の値を使用できます。</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.CopyTo(`0[],System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1" /> の要素を既存の 1 次元の <see cref="T:System.Array" /> にコピーします。コピー操作は、配列内の指定したインデックスから始まります。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.Queue`1" /> からコピーされる要素のコピー先となる 1 次元の <see cref="T:System.Array" />。<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
      <param name="arrayIndex">コピーの開始位置とする <paramref name="array" /> のインデックス (0 から始まる)。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Queue`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Count">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1" /> に格納されている要素の数を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Queue`1" /> に格納されている要素の数。</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Dequeue">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1" /> の先頭にあるオブジェクトを削除し、返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Queue`1" /> の先頭から削除されたオブジェクト。</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enqueue(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1" /> の末尾にオブジェクトを追加します。</summary>
      <param name="item">
        <see cref="T:System.Collections.Generic.Queue`1" /> に追加するオブジェクト。参照型の場合は null の値を使用できます。</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1" /> を反復処理する列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Queue`1" /> の <see cref="T:System.Collections.Generic.Queue`1.Enumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Peek">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1" /> の先頭にあるオブジェクトを削除せずに返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Queue`1" /> の先頭にあるオブジェクト。</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>
        <see cref="T:System.Collections.ICollection" /> の要素を <see cref="T:System.Array" /> にコピーします。<see cref="T:System.Array" /> の特定のインデックスからコピーが開始されます。</summary>
      <param name="array">
        <see cref="T:System.Collections.ICollection" /> からコピーされる要素のコピー先となる 1 次元の <see cref="T:System.Array" />。<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
      <param name="index">コピーの開始位置とする <paramref name="array" /> のインデックス (0 から始まる)。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) 場合は true。それ以外の場合は false。<see cref="T:System.Collections.Generic.Queue`1" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクト。<see cref="T:System.Collections.Generic.Queue`1" /> の既定の実装では、このプロパティは常に現在のインスタンスを返します。</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.ToArray">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1" /> の要素を新しい配列にコピーします。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Queue`1" /> からコピーした要素を格納する新しい配列。</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.TrimExcess">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1" /> 内にある実際の要素数が現在の容量の 90% 未満の場合は、容量をその数に設定します。</summary>
    </member>
    <member name="T:System.Collections.Generic.Queue`1.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1" /> の要素を列挙します。</summary>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Queue`1" /> 内の、列挙子の現在位置にある要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1.Enumerator" /> によって使用されているすべてのリソースを解放します。</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.MoveNext">
      <summary>列挙子を <see cref="T:System.Collections.Generic.Queue`1" /> の次の要素に進めます。</summary>
      <returns>列挙子が次の要素に正常に進んだ場合は true。列挙子がコレクションの末尾を越えた場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>コレクション内の、列挙子の現在位置にある要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>列挙子を初期位置、つまりコレクションの最初の要素の前に設定します。</summary>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2">
      <summary>キーに基づいて並べ替えられた、キーと値のペアのコレクションを表します。</summary>
      <typeparam name="TKey">ディクショナリ内のキーの型。</typeparam>
      <typeparam name="TValue">ディクショナリ内の値の型。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor">
      <summary>空で、キーの型の既定の <see cref="T:System.Collections.Generic.IComparer`1" /> 実装を使用する、<see cref="T:System.Collections.Generic.SortedDictionary`2" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>空で、指定した <see cref="T:System.Collections.Generic.IComparer`1" /> を使用してキーを比較する、<see cref="T:System.Collections.Generic.SortedDictionary`2" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IComparer`1" /> implementation to use when comparing keys, or null to use the default <see cref="T:System.Collections.Generic.Comparer`1" /> for the type of the key.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>指定した <see cref="T:System.Collections.Generic.IDictionary`2" /> から要素をコピーして格納し、キーの型の既定の <see cref="T:System.Collections.Generic.IComparer`1" /> 実装を使用する、<see cref="T:System.Collections.Generic.SortedDictionary`2" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="dictionary">新しい <see cref="T:System.Collections.Generic.SortedDictionary`2" /> に要素がコピーされた <see cref="T:System.Collections.Generic.IDictionary`2" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> に、1 つ以上の重複するキーが格納されています。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>指定した <see cref="T:System.Collections.Generic.IDictionary`2" /> から要素をコピーして格納し、指定した <see cref="T:System.Collections.Generic.IComparer`1" /> 実装を使用してキーを比較する、<see cref="T:System.Collections.Generic.SortedDictionary`2" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="dictionary">新しい <see cref="T:System.Collections.Generic.SortedDictionary`2" /> に要素がコピーされた <see cref="T:System.Collections.Generic.IDictionary`2" />。</param>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IComparer`1" /> implementation to use when comparing keys, or null to use the default <see cref="T:System.Collections.Generic.Comparer`1" /> for the type of the key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> に、1 つ以上の重複するキーが格納されています。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Add(`0,`1)">
      <summary>指定したキーおよび値を持つ要素を <see cref="T:System.Collections.Generic.SortedDictionary`2" /> に追加します。</summary>
      <param name="key">追加する要素のキー。</param>
      <param name="value">追加する要素の値。参照型の場合は null の値を使用できます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">同じキーを持つ要素が、<see cref="T:System.Collections.Generic.SortedDictionary`2" /> に既に存在します。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Clear">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> からすべての要素を削除します。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Comparer">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> の要素の順序付けに使用する <see cref="T:System.Collections.Generic.IComparer`1" /> を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> の要素の順序付けに使用する <see cref="T:System.Collections.Generic.IComparer`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsKey(`0)">
      <summary>指定したキーを持つ要素が <see cref="T:System.Collections.Generic.SortedDictionary`2" /> に含まれているかどうかを判断します。</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 内で検索するキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsValue(`1)">
      <summary>指定した値の要素が <see cref="T:System.Collections.Generic.SortedDictionary`2" /> に格納されているかどうかを確認します。</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contains an element with the specified value; otherwise, false.</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 内で検索される値。参照型の場合は null の値を使用できます。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>指定したインデックスを開始位置として、指定した <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 構造体の配列に <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 要素をコピーします。</summary>
      <param name="array">現在の <see cref="T:System.Collections.Generic.SortedDictionary`2" /> からコピーされる要素のコピー先である <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 構造体の 1 次元配列。この配列には、0 から始まるインデックスを使用する必要があります。</param>
      <param name="index">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">コピー元の <see cref="T:System.Collections.Generic.SortedDictionary`2" /> の要素数が、<paramref name="index" /> からコピー先の <paramref name="array" /> の末尾までに格納できる数を超えています。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Count">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> に格納されているキー/値ペアの数を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> に格納されているキー/値ペアの数。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> を反復処理する列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> の <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Item(`0)">
      <summary>指定されたキーに関連付けられている値を取得または設定します。</summary>
      <returns>指定されたキーに関連付けられている値。指定したキーが見つからなかった場合、get 操作は <see cref="T:System.Collections.Generic.KeyNotFoundException" /> をスローし、set 操作は指定したキーを持つ新しい要素を作成します。</returns>
      <param name="key">取得または設定する値のキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">プロパティが取得されましたが、コレクション内に <paramref name="key" /> が存在しません。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Keys">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 内のキーを格納しているコレクションを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 内のキーを格納している <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Remove(`0)">
      <summary>指定したキーを持つ要素を <see cref="T:System.Collections.Generic.SortedDictionary`2" /> から削除します。</summary>
      <returns>要素が正常に削除された場合は true。それ以外の場合は false。このメソッドは、<paramref name="key" /> が <see cref="T:System.Collections.Generic.SortedDictionary`2" /> に見つからない場合にも false を返します。</returns>
      <param name="key">削除する要素のキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> に項目を追加します。</summary>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.ICollection`1" /> に追加する <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 構造体。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyValuePair" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">同じキーを持つ要素が、<see cref="T:System.Collections.Generic.SortedDictionary`2" /> に既に存在します。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> に特定のキーと値が格納されているかどうかを判断します。</summary>
      <returns>true if <paramref name="keyValuePair" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.</returns>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.ICollection`1" /> 内で検索される <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 構造体。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> が読み取り専用であるかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> が読み取り専用の場合は true。それ以外の場合は false。<see cref="T:System.Collections.Generic.SortedDictionary`2" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> 内で最初に見つかった指定の要素を削除します。</summary>
      <returns>true if <paramref name="keyValuePair" /> was successfully removed from the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.This method also returns false if <paramref name="keyValuePair" /> was not found in the <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.ICollection`1" /> から削除する <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 構造体。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>
        <see cref="T:System.Collections.Generic.IDictionary`2" /> のキーを保持している <see cref="T:System.Collections.Generic.ICollection`1" /> を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IDictionary`2" /> のキーを保持している <see cref="T:System.Collections.Generic.ICollection`1" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>
        <see cref="T:System.Collections.Generic.IDictionary`2" /> 内の値を格納している <see cref="T:System.Collections.Generic.ICollection`1" /> を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IDictionary`2" /> 内の値を格納している <see cref="T:System.Collections.Generic.ICollection`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションの反復処理に使用できる <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Gets a collection containing the keys in the <see cref="T:System.Collections.Generic.SortedDictionary`2" /></summary>
      <returns>A collection containing the keys in the <see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Gets a collection containing the values in the <see cref="T:System.Collections.Generic.SortedDictionary`2" /></summary>
      <returns>A collection containing the values in the <see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>指定した配列インデックスを開始位置として、配列に <see cref="T:System.Collections.Generic.ICollection`1" /> の要素をコピーします。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.ICollection`1" /> から要素がコピーされる 1 次元配列。配列には、0 から始まるインデックスが設定されている必要があります。</param>
      <param name="index">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> が多次元です。または<paramref name="array" /> に 0 から始まるインデックス番号がありません。またはコピー元の <see cref="T:System.Collections.Generic.ICollection`1" /> の要素数が、<paramref name="index" /> からコピー先の <paramref name="array" /> の末尾までに格納できる数を超えています。またはコピー元の <see cref="T:System.Collections.Generic.ICollection`1" /> の型が、コピー先の <paramref name="array" /> の型に自動的にキャストできません。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) かどうかを示す値を取得します。</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.<see cref="T:System.Collections.Generic.SortedDictionary`2" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスの同期に使用できるオブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスの同期に使用できるオブジェクト。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>指定したキーおよび値を持つ要素を <see cref="T:System.Collections.IDictionary" /> に追加します。</summary>
      <param name="key">追加する要素のキーとして使用するオブジェクト。</param>
      <param name="value">追加する要素の値として使用するオブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.IDictionary" />.または<paramref name="value" /> が、<see cref="T:System.Collections.IDictionary" /> の値型 <paramref name="TValue" /> に代入できない型です。または同じキーを持つ要素が、<see cref="T:System.Collections.IDictionary" /> に既に存在します。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>指定したキーを持つ要素が <see cref="T:System.Collections.IDictionary" /> に含まれているかどうかを判断します。</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> contains an element with the key; otherwise, false.</returns>
      <param name="key">
        <see cref="T:System.Collections.IDictionary" /> 内で検索するキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>
        <see cref="T:System.Collections.IDictionary" /> の <see cref="T:System.Collections.IDictionaryEnumerator" /> を返します。</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> の <see cref="T:System.Collections.IDictionaryEnumerator" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>
        <see cref="T:System.Collections.IDictionary" /> が固定サイズかどうかを示す値を取得します。</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> has a fixed size; otherwise, false.<see cref="T:System.Collections.Generic.SortedDictionary`2" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.IDictionary" /> が読み取り専用であるかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> が読み取り専用の場合は true。それ以外の場合は false。<see cref="T:System.Collections.Generic.SortedDictionary`2" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>指定したキーを持つ要素を取得または設定します。</summary>
      <returns>
        <paramref name="key" /> がディクショナリにない場合、または <paramref name="key" /> が <see cref="T:System.Collections.Generic.SortedDictionary`2" /> のキー型 <paramref name="TKey" /> に代入できる型ではない場合は、指定したキーに関連付けられた要素または null。</returns>
      <param name="key">取得する要素のキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">A value is being assigned, and <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.またはA value is being assigned, and <paramref name="value" /> is of a type that is not assignable to the value type <paramref name="TValue" /> of the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Keys">
      <summary>
        <see cref="T:System.Collections.IDictionary" /> のキーを保持している <see cref="T:System.Collections.ICollection" /> を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> のキーを保持している <see cref="T:System.Collections.ICollection" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>指定したキーを持つ要素を <see cref="T:System.Collections.IDictionary" /> から削除します。</summary>
      <param name="key">削除する要素のキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Values">
      <summary>
        <see cref="T:System.Collections.IDictionary" /> 内の値を格納している <see cref="T:System.Collections.ICollection" /> を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> 内の値を格納している <see cref="T:System.Collections.ICollection" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションの反復処理に使用できる <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.TryGetValue(`0,`1@)">
      <summary>指定したキーに関連付けられている値を取得します。</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">取得する値のキー。</param>
      <param name="value">このメソッドは、キーが見つかった場合は指定したキーに関連付けられている値を返し、それ以外の場合は <paramref name="value" /> パラメーターの型に対する既定の値を返します。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Values">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 内の値を格納しているコレクションを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 内の値を格納している <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />。</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> の要素を列挙します。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 内の、列挙子の現在位置にある要素。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" /> によって使用されているすべてのリソースを解放します。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.MoveNext">
      <summary>列挙子を <see cref="T:System.Collections.Generic.SortedDictionary`2" /> の次の要素に進めます。</summary>
      <returns>列挙子が次の要素に正常に進んだ場合は true。列挙子がコレクションの末尾を越えた場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>列挙子の現在位置の要素を <see cref="T:System.Collections.DictionaryEntry" /> 構造体として取得します。</summary>
      <returns>
        <see cref="T:System.Collections.DictionaryEntry" /> 構造体としての、ディクショナリの現在の位置にあるコレクション内の要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>列挙子の現在位置の要素のキーを取得します。</summary>
      <returns>コレクション内の列挙子の現在位置にある要素のキー。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>列挙子の現在位置の要素の値を取得します。</summary>
      <returns>コレクション内の列挙子の現在位置にある要素の値。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>コレクション内の、列挙子の現在位置にある要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>列挙子を初期位置、つまりコレクションの最初の要素の前に設定します。</summary>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 内のキーのコレクションを表します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>指定した <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 内のキーを反映する、<see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="dictionary">新しい <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> にキーが反映される <see cref="T:System.Collections.Generic.SortedDictionary`2" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> は null なので、</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> の要素を既存の 1 次元の配列にコピーします。コピー操作は、配列内の指定したインデックスから始まります。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> から要素がコピーされる 1 次元配列。配列では 0 から始まるインデックスを使用する必要があります。</param>
      <param name="index">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">コピー元の <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> の要素数が、<paramref name="index" /> からコピー先の <paramref name="array" /> の末尾までに格納できる数を超えています。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Count">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> に格納されている要素の数を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> に格納されている要素の数。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> を反復処理する列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> の <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" /> 構造体。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> に項目を追加します。この実装は、常に <see cref="T:System.NotSupportedException" /> をスローします。</summary>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" /> に追加するオブジェクト。</param>
      <exception cref="T:System.NotSupportedException">常にスローされます。コレクションが読み取り専用です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> からすべての項目を削除します。この実装は、常に <see cref="T:System.NotSupportedException" /> をスローします。</summary>
      <exception cref="T:System.NotSupportedException">常にスローされます。コレクションが読み取り専用です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>指定した値が <see cref="T:System.Collections.Generic.ICollection`1" /> に格納されているかどうかを判断します。</summary>
      <returns>
        <paramref name="item" /> が <see cref="T:System.Collections.Generic.ICollection`1" /> に存在する場合は true。それ以外の場合は false。</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" /> 内で検索するオブジェクト。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> が読み取り専用かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> が読み取り専用である場合は true。それ以外の場合は false。<see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> 内で最初に見つかった特定のオブジェクトを削除します。この実装は、常に <see cref="T:System.NotSupportedException" /> をスローします。</summary>
      <returns>
        <paramref name="item" /> が <see cref="T:System.Collections.Generic.ICollection`1" /> から正常に削除される場合は true。それ以外の場合は false。このメソッドは、<paramref name="item" /> が <see cref="T:System.Collections.Generic.ICollection`1" /> に見つからない場合にも false を返します。</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" /> から削除するオブジェクト。</param>
      <exception cref="T:System.NotSupportedException">常にスローされます。コレクションが読み取り専用です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>特定の配列インデックスを開始位置として、配列に <see cref="T:System.Collections.ICollection" /> の要素をコピーします。</summary>
      <param name="array">
        <see cref="T:System.Collections.ICollection" /> から要素がコピーされる 1 次元配列。配列では 0 から始まるインデックスを使用する必要があります。</param>
      <param name="index">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> が多次元です。または<paramref name="array" /> に 0 から始まるインデックス番号がありません。またはコピー元の <see cref="T:System.Collections.ICollection" /> の要素数が、<paramref name="index" /> からコピー先の <paramref name="array" /> の末尾までに格納できる数を超えています。またはコピー元の <see cref="T:System.Collections.ICollection" /> の型が、コピー先の <paramref name="array" /> の型に自動的にキャストできません。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) 場合は true。それ以外の場合は false。<see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクト。<see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> の既定の実装では、このプロパティは常に現在のインスタンスを返します。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> の要素を列挙します。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 内の、列挙子の現在位置にある要素。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" /> によって使用されているすべてのリソースを解放します。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>列挙子を <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> の次の要素に進めます。</summary>
      <returns>列挙子が次の要素に正常に進んだ場合は true。列挙子がコレクションの末尾を越えた場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>コレクション内の、列挙子の現在位置にある要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>列挙子を初期位置、つまりコレクションの最初の要素の前に設定します。</summary>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 内の値のコレクションを表します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>指定した <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 内の値を反映する、<see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="dictionary">新しい <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> に値が反映される <see cref="T:System.Collections.Generic.SortedDictionary`2" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> は null なので、</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> の要素を既存の 1 次元の配列にコピーします。コピー操作は、配列内の指定したインデックスから始まります。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> から要素がコピーされる 1 次元配列。配列では 0 から始まるインデックスを使用する必要があります。</param>
      <param name="index">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">コピー元の <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> の要素数が、<paramref name="index" /> からコピー先の <paramref name="array" /> の末尾までに格納できる数を超えています。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Count">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> に格納されている要素の数を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> に格納されている要素の数。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> を反復処理する列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> の <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" /> 構造体。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> に項目を追加します。この実装は、常に <see cref="T:System.NotSupportedException" /> をスローします。</summary>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" /> に追加するオブジェクト。</param>
      <exception cref="T:System.NotSupportedException">常にスローされます。コレクションが読み取り専用です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> からすべての項目を削除します。この実装は、常に <see cref="T:System.NotSupportedException" /> をスローします。</summary>
      <exception cref="T:System.NotSupportedException">常にスローされます。コレクションが読み取り専用です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> に指定した値が格納されているかどうかを判断します。</summary>
      <returns>
        <paramref name="item" /> が <see cref="T:System.Collections.Generic.ICollection`1" /> に存在する場合は true。それ以外の場合は false。</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" /> 内で検索するオブジェクト。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> が読み取り専用かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> が読み取り専用である場合は true。それ以外の場合は false。<see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> 内で最初に見つかった特定のオブジェクトを削除します。この実装は、常に <see cref="T:System.NotSupportedException" /> をスローします。</summary>
      <returns>
        <paramref name="item" /> が <see cref="T:System.Collections.Generic.ICollection`1" /> から正常に削除される場合は true。それ以外の場合は false。このメソッドは、<paramref name="item" /> が <see cref="T:System.Collections.Generic.ICollection`1" /> に見つからない場合にも false を返します。</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" /> から削除するオブジェクト。</param>
      <exception cref="T:System.NotSupportedException">常にスローされます。コレクションが読み取り専用です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> 内で最初に見つかった特定のオブジェクトを削除します。この実装は、常に <see cref="T:System.NotSupportedException" /> をスローします。</summary>
      <returns>
        <paramref name="item" /> が <see cref="T:System.Collections.Generic.ICollection`1" /> から正常に削除される場合は true。それ以外の場合は false。このメソッドは、<paramref name="item" /> が <see cref="T:System.Collections.Generic.ICollection`1" /> に見つからない場合にも false を返します。</returns>
      <exception cref="T:System.NotSupportedException">常にスローされます。コレクションが読み取り専用です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>特定の配列インデックスを開始位置として、配列に <see cref="T:System.Collections.ICollection" /> の要素をコピーします。</summary>
      <param name="array">
        <see cref="T:System.Collections.ICollection" /> から要素がコピーされる 1 次元配列。配列では 0 から始まるインデックスを使用する必要があります。</param>
      <param name="index">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> が多次元です。または<paramref name="array" /> に 0 から始まるインデックス番号がありません。またはコピー元の <see cref="T:System.Collections.ICollection" /> の要素数が、<paramref name="index" /> からコピー先の <paramref name="array" /> の末尾までに格納できる数を超えています。またはコピー元の <see cref="T:System.Collections.ICollection" /> の型が、コピー先の <paramref name="array" /> の型に自動的にキャストできません。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) 場合は true。それ以外の場合は false。<see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクト。<see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> の既定の実装では、このプロパティは常に現在のインスタンスを返します。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> の要素を列挙します。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 内の、列挙子の現在位置にある要素。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" /> によって使用されているすべてのリソースを解放します。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>列挙子を <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> の次の要素に進めます。</summary>
      <returns>列挙子が次の要素に正常に進んだ場合は true。列挙子がコレクションの末尾を越えた場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>コレクション内の、列挙子の現在位置にある要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>列挙子を初期位置、つまりコレクションの最初の要素の前に設定します。</summary>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedList`2">
      <summary>関連付けられた <see cref="T:System.Collections.Generic.IComparer`1" /> 実装に基づいて、キーにより並べ替えられた、キーと値のペアのコレクションを表します。</summary>
      <typeparam name="TKey">コレクション内のキーの型。</typeparam>
      <typeparam name="TValue">コレクション内の値の型。</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor">
      <summary>空で、既定の初期量を備え、既定の <see cref="T:System.Collections.Generic.IComparer`1" /> を使用する、<see cref="T:System.Collections.Generic.SortedList`2" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>空で、既定の初期量を備え、指定した <see cref="T:System.Collections.Generic.IComparer`1" /> を使用する、<see cref="T:System.Collections.Generic.SortedList`2" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="comparer">キーの比較時に使用する <see cref="T:System.Collections.Generic.IComparer`1" /> の実装。またはキーの型に既定の <see cref="T:System.Collections.Generic.Comparer`1" /> を使用する場合は null。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>指定した <see cref="T:System.Collections.Generic.IDictionary`2" /> からコピーした要素を格納し、コピーした要素の数を格納できるだけの容量を備え、既定の <see cref="T:System.Collections.Generic.IComparer`1" /> を使用する、<see cref="T:System.Collections.Generic.SortedList`2" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="dictionary">新しい <see cref="T:System.Collections.Generic.SortedList`2" /> に要素がコピーされた <see cref="T:System.Collections.Generic.IDictionary`2" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> に、1 つ以上の重複するキーが格納されています。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>指定した <see cref="T:System.Collections.Generic.IDictionary`2" /> からコピーした要素を格納し、コピーした要素の数を格納できるだけの容量を備え、指定した <see cref="T:System.Collections.Generic.IComparer`1" /> を使用する、<see cref="T:System.Collections.Generic.SortedList`2" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="dictionary">新しい <see cref="T:System.Collections.Generic.SortedList`2" /> に要素がコピーされた <see cref="T:System.Collections.Generic.IDictionary`2" />。</param>
      <param name="comparer">キーの比較時に使用する <see cref="T:System.Collections.Generic.IComparer`1" /> の実装。またはキーの型に既定の <see cref="T:System.Collections.Generic.Comparer`1" /> を使用する場合は null。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> に、1 つ以上の重複するキーが格納されています。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32)">
      <summary>空で、指定した初期量を備え、既定の <see cref="T:System.Collections.Generic.IComparer`1" /> を使用する、<see cref="T:System.Collections.Generic.SortedList`2" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.SortedList`2" /> が格納できる要素数の初期値。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> が 0 未満です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>空で、指定した初期量を備え、指定した <see cref="T:System.Collections.Generic.IComparer`1" /> を使用する、<see cref="T:System.Collections.Generic.SortedList`2" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.SortedList`2" /> が格納できる要素数の初期値。</param>
      <param name="comparer">キーの比較時に使用する <see cref="T:System.Collections.Generic.IComparer`1" /> の実装。またはキーの型に既定の <see cref="T:System.Collections.Generic.Comparer`1" /> を使用する場合は null。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> が 0 未満です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Add(`0,`1)">
      <summary>指定したキーおよび値を持つ要素を <see cref="T:System.Collections.Generic.SortedList`2" /> に追加します。</summary>
      <param name="key">追加する要素のキー。</param>
      <param name="value">追加する要素の値。参照型の場合は null の値を使用できます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">同じキーを持つ要素が、<see cref="T:System.Collections.Generic.SortedList`2" /> に既に存在します。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Capacity">
      <summary>
        <see cref="T:System.Collections.Generic.SortedList`2" /> に格納できる要素の数を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedList`2" /> に格納できる要素の数。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Collections.Generic.SortedList`2.Capacity" /> が <see cref="P:System.Collections.Generic.SortedList`2.Count" /> より小さい値に設定されています。</exception>
      <exception cref="T:System.OutOfMemoryException">システムのメモリが不足しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Clear">
      <summary>
        <see cref="T:System.Collections.Generic.SortedList`2" /> からすべての要素を削除します。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Comparer">
      <summary>並べ替えられたリストの <see cref="T:System.Collections.Generic.IComparer`1" /> を取得します。</summary>
      <returns>現在の <see cref="T:System.Collections.Generic.SortedList`2" /> の <see cref="T:System.IComparable`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsKey(`0)">
      <summary>特定のキーが <see cref="T:System.Collections.Generic.SortedList`2" /> に含まれるかどうかを調べます。</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedList`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">
        <see cref="T:System.Collections.Generic.SortedList`2" /> 内で検索するキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsValue(`1)">
      <summary>
        <see cref="T:System.Collections.Generic.SortedList`2" /> に指定の値が含まれているかどうかを確認します。</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedList`2" /> contains an element with the specified value; otherwise, false.</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.SortedList`2" /> 内で検索される値。参照型の場合は null の値を使用できます。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Count">
      <summary>
        <see cref="T:System.Collections.Generic.SortedList`2" /> に格納されているキー/値ペアの数を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedList`2" /> に格納されているキー/値ペアの数。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.SortedList`2" /> を反復処理する列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedList`2" /> の <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 型の <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfKey(`0)">
      <summary>指定したキーを検索し、<see cref="T:System.Collections.Generic.SortedList`2" /> 全体内でそのキーが見つかった位置の 0 から始まるインデックスを返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedList`2" /> 全体内で <paramref name="key" /> が見つかった場合は、見つかった位置の 0 から始まるインデックス番号。それ以外の場合は -1。</returns>
      <param name="key">
        <see cref="T:System.Collections.Generic.SortedList`2" /> 内で検索するキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfValue(`1)">
      <summary>指定した値を検索し、<see cref="T:System.Collections.Generic.SortedList`2" /> 全体内で最初に見つかった位置の 0 から始まるインデックスを返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedList`2" /> 全体を対象に <paramref name="value" /> を検索し、見つかった場合は、インデックス番号の最も小さい要素の 0 から始まるインデックス番号、それ以外の場合は -1。</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.SortedList`2" /> 内で検索される値。参照型の場合は null の値を使用できます。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Item(`0)">
      <summary>指定されたキーに関連付けられている値を取得または設定します。</summary>
      <returns>指定されたキーに関連付けられている値。指定したキーが見つからなかった場合、get 操作は <see cref="T:System.Collections.Generic.KeyNotFoundException" /> をスローし、set 操作は指定したキーを使用して新しい要素を作成します。</returns>
      <param name="key">値を取得または設定する対象のキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">プロパティが取得されましたが、コレクション内に <paramref name="key" /> が存在しません。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Keys">
      <summary>並べ替えられた順序で、<see cref="T:System.Collections.Generic.SortedList`2" /> 内のキーを含むコレクションを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedList`2" /> 内のキーを格納している <see cref="T:System.Collections.Generic.IList`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Remove(`0)">
      <summary>指定したキーを持つ要素を <see cref="T:System.Collections.Generic.SortedList`2" /> から削除します。</summary>
      <returns>要素が正常に削除された場合は true。それ以外の場合は false。This method also returns false if <paramref name="key" /> was not found in the original <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
      <param name="key">削除する要素のキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.RemoveAt(System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.SortedList`2" /> の指定したインデックスにある要素を削除します。</summary>
      <param name="index">削除する要素の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。または<paramref name="index" /> が <see cref="P:System.Collections.Generic.SortedList`2.Count" /> 以上になっています。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> に、キー/値ペアを追加します。</summary>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.ICollection`1" /> に追加する <see cref="T:System.Collections.Generic.KeyValuePair`2" /> です。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> に特定の要素が格納されているかどうかを判断します。</summary>
      <returns>true if <paramref name="keyValuePair" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.</returns>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.ICollection`1" /> 内で検索される <see cref="T:System.Collections.Generic.KeyValuePair`2" />。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> の要素を <see cref="T:System.Array" /> にコピーします。コピーは特定の <see cref="T:System.Array" /> のインデックスから開始されます。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.ICollection`1" /> から要素がコピーされる 1 次元の <see cref="T:System.Array" />。<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
      <param name="arrayIndex">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null です。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">コピー元の <see cref="T:System.Collections.Generic.ICollection`1" /> の要素数が、<paramref name="arrayIndex" /> からコピー先の <paramref name="array" /> の末尾までに格納できる数を超えています。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> が読み取り専用であるかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> が読み取り専用の場合は true。それ以外の場合は false。<see cref="T:System.Collections.Generic.SortedList`2" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> 内で最初に見つかった特定のキー/値ペアを削除します。</summary>
      <returns>true if <paramref name="keyValuePair" /> was successfully removed from the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.This method also returns false if <paramref name="keyValuePair" /> was not found in the original <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.ICollection`1" /> から削除する <see cref="T:System.Collections.Generic.KeyValuePair`2" />。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>
        <see cref="T:System.Collections.Generic.IDictionary`2" /> のキーを保持している <see cref="T:System.Collections.Generic.ICollection`1" /> を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IDictionary`2" /> のキーを保持している <see cref="T:System.Collections.Generic.ICollection`1" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>
        <see cref="T:System.Collections.Generic.IDictionary`2" /> 内の値を格納している <see cref="T:System.Collections.Generic.ICollection`1" /> を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IDictionary`2" /> 内の値を格納している <see cref="T:System.Collections.Generic.ICollection`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションの反復処理に使用できる <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>読み取り専用のディクショナリのキーを含む列挙可能なコレクションを取得します。</summary>
      <returns>読み取り専用のディクショナリのキーを含む列挙可能なコレクション。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>読み取り専用のディクショナリの値を含む列挙可能なコレクションを取得します。</summary>
      <returns>読み取り専用のディクショナリの値を含む列挙可能なコレクション。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>
        <see cref="T:System.Collections.ICollection" /> の要素を <see cref="T:System.Array" /> にコピーします。コピーは特定の <see cref="T:System.Array" /> のインデックスから開始されます。</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection" />.<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
      <param name="arrayIndex">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> が 0 未満です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> が多次元です。または<paramref name="array" /> に 0 から始まるインデックス番号がありません。またはコピー元の <see cref="T:System.Collections.ICollection" /> の要素数が、<paramref name="arrayIndex" /> からコピー先の <paramref name="array" /> の末尾までに格納できる数を超えています。またはコピー元の <see cref="T:System.Collections.ICollection" /> の型が、コピー先の <paramref name="array" /> の型に自動的にキャストできません。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) かどうかを示す値を取得します。</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.<see cref="T:System.Collections.Generic.SortedList`2" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスの同期に使用できるオブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスの同期に使用できるオブジェクト。<see cref="T:System.Collections.Generic.SortedList`2" /> の既定の実装では、このプロパティは常に現在のインスタンスを返します。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>指定したキーおよび値を持つ要素を <see cref="T:System.Collections.IDictionary" /> に追加します。</summary>
      <param name="key">追加する要素のキーとして使用する <see cref="T:System.Object" />。</param>
      <param name="value">追加する要素の値として使用する <see cref="T:System.Object" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.IDictionary" />.または<paramref name="value" /> が、<see cref="T:System.Collections.IDictionary" /> の値型 <paramref name="TValue" /> に代入できない型です。または同じキーを持つ要素が、<see cref="T:System.Collections.IDictionary" /> に既に存在します。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>指定したキーを持つ要素が <see cref="T:System.Collections.IDictionary" /> に含まれているかどうかを判断します。</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> contains an element with the key; otherwise, false.</returns>
      <param name="key">
        <see cref="T:System.Collections.IDictionary" /> 内で検索するキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#GetEnumerator">
      <summary>
        <see cref="T:System.Collections.IDictionary" /> の <see cref="T:System.Collections.IDictionaryEnumerator" /> を返します。</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> の <see cref="T:System.Collections.IDictionaryEnumerator" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsFixedSize">
      <summary>
        <see cref="T:System.Collections.IDictionary" /> が固定サイズかどうかを示す値を取得します。</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> has a fixed size; otherwise, false.<see cref="T:System.Collections.Generic.SortedList`2" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.IDictionary" /> が読み取り専用であるかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> が読み取り専用の場合は true。それ以外の場合は false。<see cref="T:System.Collections.Generic.SortedList`2" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>指定したキーを持つ要素を取得または設定します。</summary>
      <returns>The element with the specified key, or null if <paramref name="key" /> is not in the dictionary or <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
      <param name="key">取得または設定する要素のキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">A value is being assigned, and <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.Generic.SortedList`2" />.またはA value is being assigned, and <paramref name="value" /> is of a type that is not assignable to the value type <paramref name="TValue" /> of the <see cref="T:System.Collections.Generic.SortedList`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Keys">
      <summary>
        <see cref="T:System.Collections.IDictionary" /> のキーを保持している <see cref="T:System.Collections.ICollection" /> を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> のキーを保持している <see cref="T:System.Collections.ICollection" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>指定したキーを持つ要素を <see cref="T:System.Collections.IDictionary" /> から削除します。</summary>
      <param name="key">削除する要素のキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Values">
      <summary>
        <see cref="T:System.Collections.IDictionary" /> 内の値を格納している <see cref="T:System.Collections.ICollection" /> を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> 内の値を格納している <see cref="T:System.Collections.ICollection" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションの反復処理に使用できる <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TrimExcess">
      <summary>
        <see cref="T:System.Collections.Generic.SortedList`2" /> 内にある実際の要素数が現在の容量の 90% 未満の場合は、容量をその数に設定します。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TryGetValue(`0,`1@)">
      <summary>指定したキーに関連付けられている値を取得します。</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedList`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">値を取得する対象のキー。</param>
      <param name="value">このメソッドは、キーが見つかった場合は指定したキーに関連付けられている値を返し、それ以外の場合は <paramref name="value" /> パラメーターの型に対する既定の値を返します。このパラメーターは初期化せずに渡されます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> は null です。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Values">
      <summary>
        <see cref="T:System.Collections.Generic.SortedList`2" /> 内の値を格納しているコレクションを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedList`2" /> 内の値を格納している <see cref="T:System.Collections.Generic.IList`1" />。</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1">
      <summary>一定の並べ替え順序で管理されたオブジェクトのコレクションを表します。</summary>
      <typeparam name="T">セット内の要素の型。</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>指定された比較子を使用する <see cref="T:System.Collections.Generic.SortedSet`1" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="comparer">オブジェクトの比較に使用する既定の比較子。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparer" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>指定の列挙可能なコレクションからコピーされた要素を格納する、<see cref="T:System.Collections.Generic.SortedSet`1" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="collection">コピーする列挙可能なコレクション。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IComparer{`0})">
      <summary>指定の列挙可能なコレクションからコピーされた要素を格納し、指定された比較子を使用する、<see cref="T:System.Collections.Generic.SortedSet`1" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="collection">コピーする列挙可能なコレクション。</param>
      <param name="comparer">オブジェクトの比較に使用する既定の比較子。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Add(`0)">
      <summary>要素をセットに追加し、正常に追加されたかどうかを示す値を返します。</summary>
      <returns>
        <paramref name="item" /> がセットに追加された場合は true。それ以外の場合は false。</returns>
      <param name="item">セットに追加する要素。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Clear">
      <summary>セットからすべての要素を削除します。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Comparer">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 内の値の等価性を調べるための <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> オブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 内の値の等価性を調べるための比較子。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Contains(`0)">
      <summary>セットに特定の要素が含まれているかどうかを判断します。</summary>
      <returns>セットに <paramref name="item" /> が含まれている場合は true。それ以外の場合は false。</returns>
      <param name="item">セット内で検索する要素。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[])">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 全体を互換性のある 1 次元の配列にコピーします。コピー操作は、コピー先の配列の先頭から始まります。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.SortedSet`1" /> から要素がコピーされる 1 次元配列。</param>
      <exception cref="T:System.ArgumentException">コピー元の <see cref="T:System.Collections.Generic.SortedSet`1" /> の要素数が、コピー先の配列に格納できる要素の数を超えています。 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 全体を互換性のある 1 次元配列にコピーします。コピー操作は、配列の指定したインデックスから始まります。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.SortedSet`1" /> から要素がコピーされる 1 次元配列。配列には、0 から始まるインデックスが設定されている必要があります。</param>
      <param name="index">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentException">コピー元の配列の要素数が、<paramref name="index" /> からコピー先の配列の末尾までに格納できる数を超えています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>指定された数の要素を <see cref="T:System.Collections.Generic.SortedSet`1" /> から互換性のある 1 次元配列にコピーします。コピー操作は、指定された配列インデックスから始まります。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.SortedSet`1" /> から要素がコピーされる 1 次元配列。配列には、0 から始まるインデックスが設定されている必要があります。</param>
      <param name="index">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <param name="count">コピーする要素の数。</param>
      <exception cref="T:System.ArgumentException">コピー元の配列の要素数が、<paramref name="index" /> からコピー先の配列の末尾までに格納できる数を超えています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。または<paramref name="count" /> が 0 未満です。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Count">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> にある要素の数を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> にある要素の数。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>現在の <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトから、指定されたコレクションに含まれる要素をすべて削除します。</summary>
      <param name="other">
        <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトから削除する項目のコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> を反復処理する列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> をソートされた順序で反復処理する列挙子。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetViewBetween(`0,`0)">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 内のサブセットのビューを返します。</summary>
      <returns>指定された範囲の値だけを含むサブセット ビュー。</returns>
      <param name="lowerValue">目的のビューの範囲の最小値。</param>
      <param name="upperValue">目的のビューの範囲の最大値。</param>
      <exception cref="T:System.ArgumentException">比較子に照らすと、<paramref name="lowerValue" /> が <paramref name="upperValue" /> を超えています。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">ビューで試行された操作が、<paramref name="lowerValue" /> および <paramref name="upperValue" /> で指定された範囲外です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>指定されたコレクションに存在する要素だけが含まれるように現在の <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトを変更します。</summary>
      <param name="other">現在の <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトと比較するコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトが、指定されたコレクションの真のサブセット (真部分集合) であるかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトが <paramref name="other" /> の真のサブセットである場合は true。それ以外の場合は false。</returns>
      <param name="other">現在の <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトと比較するコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトが、指定されたコレクションの真のスーパーセット (真上位集合) であるかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトが <paramref name="other" /> の真のスーパーセットである場合は true。それ以外の場合は false。</returns>
      <param name="other">現在の <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトと比較するコレクション。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトが、指定されたコレクションのサブセットであるかどうかを判断します。</summary>
      <returns>現在の <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトが <paramref name="other" /> のサブセットの場合は true。それ以外の場合は false。</returns>
      <param name="other">現在の <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトと比較するコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトが、指定されたコレクションのスーパーセットであるかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトが <paramref name="other" /> のスーパーセットである場合は true。それ以外の場合は false。</returns>
      <param name="other">現在の <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトと比較するコレクション。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Max">
      <summary>比較子によって定義された、<see cref="T:System.Collections.Generic.SortedSet`1" /> 内の最大値を取得します。</summary>
      <returns>セット内の最大値。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Min">
      <summary>比較子によって定義された、<see cref="T:System.Collections.Generic.SortedSet`1" /> 内の最小値を取得します。</summary>
      <returns>セット内の最小値。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>現在の <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトと指定されたコレクションとが共通の要素を共有しているかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトと <paramref name="other" /> との間に共通する要素が 1 つでも存在する場合は true。それ以外の場合は false。</returns>
      <param name="other">現在の <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトと比較するコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Remove(`0)">
      <summary>指定した項目を <see cref="T:System.Collections.Generic.SortedSet`1" /> から削除します。</summary>
      <returns>true if the element is found and successfully removed; otherwise, false.</returns>
      <param name="item">削除する要素。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>指定の述語によって定義された条件に一致するすべての要素を <see cref="T:System.Collections.Generic.SortedSet`1" /> から削除します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> コレクションから削除された要素数。 </returns>
      <param name="match">削除する要素の条件を定義するデリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Reverse">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> を逆順で反復処理する <see cref="T:System.Collections.Generic.IEnumerable`1" /> を返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> を逆順で反復処理する列挙子。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>現在の <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトと指定されたコレクションに同じ要素が存在するかどうかを判断します。</summary>
      <returns>現在の <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトが <paramref name="other" /> と等しい場合は true。それ以外の場合は false。</returns>
      <param name="other">現在の <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトと比較するコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>現在の <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトを、そのオブジェクトと指定されたコレクションの (両方に存在するのではなく) どちらか一方に存在する要素だけが格納されるように変更します。</summary>
      <param name="other">現在の <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトと比較するコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> オブジェクトに項目を追加します。</summary>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" /> オブジェクトに追加するオブジェクト。</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Collections.Generic.ICollection`1" /> は読み取り専用です。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.ICollection" /> が読み取り専用かどうかを示す値を取得します。</summary>
      <returns>コレクションが読み取り専用の場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションの反復処理に使用できる列挙子。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 全体を互換性のある 1 次元配列にコピーします。コピー操作は、配列の指定したインデックスから始まります。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.SortedSet`1" /> から要素がコピーされる 1 次元配列。配列には、0 から始まるインデックスが設定されている必要があります。</param>
      <param name="index">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentException">コピー元の配列の要素数が、<paramref name="index" /> からコピー先の配列の末尾までに格納できる数を超えています。 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> は null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクト。<see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> の既定の実装では、このプロパティは常に現在のインスタンスを返します。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションの反復処理に使用できる列挙子。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>現在の <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトを、現在のオブジェクトまたは指定したコレクションのいずれかに存在するすべての要素が格納されるように変更します。</summary>
      <param name="other">現在の <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトと比較するコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> は null です。</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> オブジェクトの要素を列挙します。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>コレクション内の、列挙子の現在位置にある要素。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1.Enumerator" /> によって使用されているすべてのリソースを解放します。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.MoveNext">
      <summary>列挙子を <see cref="T:System.Collections.Generic.SortedSet`1" /> コレクションの次の要素に進めます。</summary>
      <returns>列挙子が次の要素に正常に進んだ場合は true。列挙子がコレクションの末尾を越えた場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>コレクション内の、列挙子の現在位置にある要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>列挙子を初期位置、つまりコレクションの最初の要素の前に設定します。</summary>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="T:System.Collections.Generic.Stack`1">
      <summary>指定した同じ型のインスタンスの、後入れ先出し (LIFO) の可変サイズのコレクションを表します。</summary>
      <typeparam name="T">スタック内の要素の型を指定します。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor">
      <summary>空で、既定の初期量を備えた、<see cref="T:System.Collections.Generic.Stack`1" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>指定したコレクションからコピーした要素を格納し、コピーされる要素の数を格納できるだけの容量を備えた、<see cref="T:System.Collections.Generic.Stack`1" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="collection">要素のコピー元のコレクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Int32)">
      <summary>空で、指定した初期量または既定の初期量のうち大きい方の初期量を備えた、<see cref="T:System.Collections.Generic.Stack`1" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.Stack`1" /> が格納できる要素数の初期値。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Clear">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1" /> からすべてのオブジェクトを削除します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Contains(`0)">
      <summary>ある要素が <see cref="T:System.Collections.Generic.Stack`1" /> 内に存在するかどうかを判断します。</summary>
      <returns>
        <paramref name="item" /> が <see cref="T:System.Collections.Generic.Stack`1" /> に存在する場合は true。それ以外の場合は false。</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.Stack`1" /> 内で検索するオブジェクト。参照型の場合は null の値を使用できます。</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.CopyTo(`0[],System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1" /> を既存の 1 次元の <see cref="T:System.Array" /> にコピーします。コピー操作は、配列内の指定したインデックスから始まります。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.Stack`1" /> からコピーされる要素のコピー先となる 1 次元の <see cref="T:System.Array" />。<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
      <param name="arrayIndex">コピーの開始位置とする <paramref name="array" /> のインデックス (0 から始まる)。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Stack`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Count">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1" /> に格納されている要素の数を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1" /> に格納されている要素の数。</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1" /> の列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1" /> の <see cref="T:System.Collections.Generic.Stack`1.Enumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Peek">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1" /> の先頭にあるオブジェクトを削除せずに返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1" /> の一番上にあるオブジェクト。</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Pop">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1" /> の先頭にあるオブジェクトを削除し、返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1" /> の一番上から削除されたオブジェクト。</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Push(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1" /> の先頭にオブジェクトを挿入します。</summary>
      <param name="item">
        <see cref="T:System.Collections.Generic.Stack`1" /> にプッシュするオブジェクト。参照型の場合は null の値を使用できます。</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>
        <see cref="T:System.Collections.ICollection" /> の要素を <see cref="T:System.Array" /> にコピーします。<see cref="T:System.Array" /> の特定のインデックスからコピーが開始されます。</summary>
      <param name="array">
        <see cref="T:System.Collections.ICollection" /> からコピーされる要素のコピー先となる 1 次元の <see cref="T:System.Array" />。<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
      <param name="arrayIndex">コピーの開始位置とする <paramref name="array" /> のインデックス (0 から始まる)。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) 場合は true。それ以外の場合は false。<see cref="T:System.Collections.Generic.Stack`1" /> の既定の実装では、このプロパティは常に false を返します。</returns>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクト。<see cref="T:System.Collections.Generic.Stack`1" /> の既定の実装では、このプロパティは常に現在のインスタンスを返します。</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.ToArray">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1" /> を新しい配列にコピーします。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1" /> の要素のコピーを格納する新しい配列。</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.TrimExcess">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1" /> 内にある実際の要素数が現在の容量の 90% 未満の場合は、容量をその数に設定します。</summary>
    </member>
    <member name="T:System.Collections.Generic.Stack`1.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1" /> の要素を列挙します。</summary>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1" /> 内の、列挙子の現在位置にある要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1.Enumerator" /> によって使用されているすべてのリソースを解放します。</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.MoveNext">
      <summary>列挙子を <see cref="T:System.Collections.Generic.Stack`1" /> の次の要素に進めます。</summary>
      <returns>列挙子が次の要素に正常に進んだ場合は true。列挙子がコレクションの末尾を越えた場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>列挙子の現在位置の要素を取得します。</summary>
      <returns>コレクション内の、列挙子の現在位置にある要素。</returns>
      <exception cref="T:System.InvalidOperationException">列挙子が、コレクションの最初の要素の前、または最後の要素の後に位置しています。</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>列挙子を初期位置、つまりコレクションの最初の要素の前に設定します。このクラスは継承できません。</summary>
      <exception cref="T:System.InvalidOperationException">列挙子が作成された後に、コレクションが変更されました。</exception>
    </member>
  </members>
</doc>