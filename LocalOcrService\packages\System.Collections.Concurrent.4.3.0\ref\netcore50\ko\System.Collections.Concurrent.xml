﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Collections.Concurrent</name>
  </assembly>
  <members>
    <member name="T:System.Collections.Concurrent.BlockingCollection`1">
      <summary>
        <see cref="T:System.Collections.Concurrent.IProducerConsumerCollection`1" />을 구현하는 스레드로부터 안전한 컬렉션에 대한 차단 및 경계 기능을 제공합니다.</summary>
      <typeparam name="T">컬렉션에 있는 요소의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.#ctor">
      <summary>상한을 사용하지 않고 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.#ctor(System.Collections.Concurrent.IProducerConsumerCollection{`0})">
      <summary>제공된 <see cref="T:System.Collections.Concurrent.IProducerConsumerCollection`1" />을 내부 데이터 저장소로 사용하고 상한이 없는 상태로 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="collection">내부 데이터 저장소로 사용할 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 인수가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.#ctor(System.Collections.Concurrent.IProducerConsumerCollection{`0},System.Int32)">
      <summary>제공된 <see cref="T:System.Collections.Concurrent.IProducerConsumerCollection`1" />을 내부 데이터 저장소로 사용하고 지정된 상한을 사용하여 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="collection">내부 데이터 저장소로 사용할 컬렉션입니다.</param>
      <param name="boundedCapacity">컬렉션의 한계 크기입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="boundedCapacity" />이 양수 값이 아닌 경우.</exception>
      <exception cref="T:System.ArgumentException">제공된 <paramref name="collection" />에는 <paramref name="boundedCapacity" />에서 허용하는 것보다 더 많은 값이 포함됩니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.#ctor(System.Int32)">
      <summary>지정된 상한을 사용하여 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="boundedCapacity">컬렉션의 한계 크기입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="boundedCapacity" />이 양수 값이 아닌 경우.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.Add(`0)">
      <summary>
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />에 항목을 추가합니다.</summary>
      <param name="item">컬렉션에 추가될 항목입니다.이 값은 null 참조일 수 있습니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />는 추가 관련하여 완료된 것으로 표시되었습니다.또는기본 컬렉션에서 항목을 적용하지 않았습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.Add(`0,System.Threading.CancellationToken)">
      <summary>
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />에 항목을 추가합니다.</summary>
      <param name="item">컬렉션에 추가될 항목입니다.이 값은 null 참조일 수 있습니다.</param>
      <param name="cancellationToken">관찰할 취소 토큰입니다.</param>
      <exception cref="T:System.OperationCanceledException">
        <see cref="T:System.Threading.CancellationToken" />이 취소된 경우.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />이 삭제되었거나 <paramref name="cancellationToken" />을 소유한 <see cref="T:System.Threading.CancellationTokenSource" />가 삭제되었습니다.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />는 추가 관련하여 완료된 것으로 표시되었습니다.또는기본 컬렉션에서 항목을 적용하지 않았습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.AddToAny(System.Collections.Concurrent.BlockingCollection{`0}[],`0)">
      <summary>지정된 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 중 하나에 지정된 항목을 추가합니다.</summary>
      <returns>
        <paramref name="collections" /> 배열에서 항목이 추가된 컬렉션의 인덱스입니다.</returns>
      <param name="collections">컬렉션의 배열입니다.</param>
      <param name="item">컬렉션 중 하나에 추가될 항목입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스가 하나 이상 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collections" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="collections" /> 횟수가 최대 크기(STA의 경우 62, MTA의 경우 63)보다 큽니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="collections" /> 인수가 길이가 0 인 배열이거나 Null 요소를 포함하거나 또는 컬렉션 중 하나 이상이 추가를 완료한 것으로 표시되어 있습니다.</exception>
      <exception cref="T:System.InvalidOperationException">하나 이상의 기본 컬렉션에서 항목을 허용하지 않습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.AddToAny(System.Collections.Concurrent.BlockingCollection{`0}[],`0,System.Threading.CancellationToken)">
      <summary>지정된 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 중 하나에 지정된 항목을 추가합니다.</summary>
      <returns>
        <paramref name="collections" /> 배열에서 항목이 추가된 컬렉션의 인덱스입니다.</returns>
      <param name="collections">컬렉션의 배열입니다.</param>
      <param name="item">컬렉션 중 하나에 추가될 항목입니다.</param>
      <param name="cancellationToken">관찰할 취소 토큰입니다.</param>
      <exception cref="T:System.OperationCanceledException">
        <see cref="T:System.Threading.CancellationToken" />이 취소된 경우.</exception>
      <exception cref="T:System.InvalidOperationException">하나 이상의 기본 컬렉션에서 항목을 허용하지 않습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collections" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="collections" /> 횟수가 최대 크기(STA의 경우 62, MTA의 경우 63)보다 큽니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="collections" /> 인수가 길이가 0 인 배열이거나 Null 요소를 포함하거나 또는 컬렉션 중 하나 이상이 추가를 완료한 것으로 표시되어 있습니다.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="cancellationToken" />을 만든 하나 이상의 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 또는 <see cref="T:System.Threading.CancellationTokenSource" />가 삭제되었습니다.</exception>
    </member>
    <member name="P:System.Collections.Concurrent.BlockingCollection`1.BoundedCapacity">
      <summary>이 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스의 한계 용량을 가져옵니다.</summary>
      <returns>이 컬렉션의 한계 용량이거나 int.MaxValue(제공된 한계가 없는 경우)입니다.</returns>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.CompleteAdding">
      <summary>
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스를 더 이상 추가를 허용하지 않는 상태로 표시합니다.</summary>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.CopyTo(`0[],System.Int32)">
      <summary>대상 배열의 지정된 인덱스부터 시작하여 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스의 모든 항목을 호환되는 1차원 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스에서 복사한 요소의 대상인 1차원 배열입니다.배열에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">복사가 시작되는 <paramref name="array" />의 0부터 시작하는 인덱스입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 인수가 0보다 작습니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 인수는 <paramref name="array" /> 길이보다 크거나 같습니다.대상 배열이 모든 BlockingCcollection 요소를 수용하기에는 너무 작습니다.배열 차수가 일치하지 않습니다.배열 형식은 BlockingCollection 요소의 형식과 호환되지 않습니다.</exception>
    </member>
    <member name="P:System.Collections.Concurrent.BlockingCollection`1.Count">
      <summary>
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />에 들어 있는 항목 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />에 들어 있는 항목 수입니다.</returns>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.Dispose">
      <summary>
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 클래스의 현재 인스턴스에서 사용하는 모든 리소스를 해제합니다.</summary>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스에서 사용하는 리소스를 해제합니다.</summary>
      <param name="disposing">명시적으로 삭제할지(true) 아니면 종료자로 삭제할지(false) 나타냅니다.</param>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.GetConsumingEnumerable">
      <summary>컬렉션에 있는 항목에 사용하는 <see cref="T:System.Collections.Generic.IEnumerator`1" />을 제공합니다.</summary>
      <returns>컬렉션에서 항목을 제거하고 반환하는 <see cref="T:System.Collections.Generic.IEnumerable`1" />입니다.</returns>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.GetConsumingEnumerable(System.Threading.CancellationToken)">
      <summary>컬렉션에 있는 항목에 사용하는 <see cref="T:System.Collections.Generic.IEnumerable`1" />을 제공합니다.</summary>
      <returns>컬렉션에서 항목을 제거하고 반환하는 <see cref="T:System.Collections.Generic.IEnumerable`1" />입니다.</returns>
      <param name="cancellationToken">관찰할 취소 토큰입니다.</param>
      <exception cref="T:System.OperationCanceledException">
        <see cref="T:System.Threading.CancellationToken" />이 취소된 경우.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />이 삭제되었거나 <paramref name="cancellationToken" />을 만든 <see cref="T:System.Threading.CancellationTokenSource" />가 삭제되었습니다.</exception>
    </member>
    <member name="P:System.Collections.Concurrent.BlockingCollection`1.IsAddingCompleted">
      <summary>이 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />이 추가에 대해 완료 상태로 표시되었는지 여부를 가져옵니다.</summary>
      <returns>이 컬렉션이 추가에 대해 완료 상태로 표시되었는지 여부입니다.</returns>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
    </member>
    <member name="P:System.Collections.Concurrent.BlockingCollection`1.IsCompleted">
      <summary>이 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />이 추가에 대해 완료 상태로 표시되었으며 비어 있는지 여부를 가져옵니다.</summary>
      <returns>이 컬렉션이 추가에 대해 완료 상태로 표시되었으며 비어 있는지 여부입니다.</returns>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>컬렉션에 있는 항목에 대한 <see cref="T:System.Collections.Generic.IEnumerator`1" />를 제공합니다.</summary>
      <returns>컬렉션에 있는 항목에 대한 <see cref="T:System.Collections.Generic.IEnumerator`1" />입니다.</returns>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>대상 배열의 지정된 인덱스부터 시작하여 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스의 모든 항목을 호환되는 1차원 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스에서 복사한 요소의 대상인 1차원 배열입니다.배열에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">복사가 시작되는 <paramref name="array" />의 0부터 시작하는 인덱스입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 인수가 0보다 작습니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 인수는 <paramref name="array" /> 길이보다 크거나 같으며, 배열은 다차원이거나 컬렉션에 대한 형식 매개 변수는 대상 배열 형식에 자동으로 캐스팅될 수 없습니다.</exception>
    </member>
    <member name="P:System.Collections.Concurrent.BlockingCollection`1.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>항상 false를 반환합니다.</returns>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
    </member>
    <member name="P:System.Collections.Concurrent.BlockingCollection`1.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.이 속성이 지원되지 않는 경우</summary>
      <returns>null을 반환합니다.</returns>
      <exception cref="T:System.NotSupportedException">SyncRoot 속성은 지원되지 않습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>컬렉션에 있는 항목에 대한 <see cref="T:System.Collections.IEnumerator" />를 제공합니다.</summary>
      <returns>컬렉션에 있는 항목에 대한 <see cref="T:System.Collections.IEnumerator" />입니다.</returns>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.Take">
      <summary>
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />에서 항목을 제거합니다.</summary>
      <returns>컬렉션에서 제거된 항목입니다.</returns>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
      <exception cref="T:System.InvalidOperationException">내부 컬렉션은 이 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 외부에서 수정되었습니다. 그렇지 않으면 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />이 비어 있거나 컬렉션이 추가를 완료한 것으로 표시됩니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.Take(System.Threading.CancellationToken)">
      <summary>
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />에서 항목을 제거합니다.</summary>
      <returns>컬렉션에서 제거된 항목입니다.</returns>
      <param name="cancellationToken">빼내기 작업을 취소하는 데 사용할 수 있는 개체입니다.</param>
      <exception cref="T:System.OperationCanceledException">
        <see cref="T:System.Threading.CancellationToken" />이 취소되었습니다.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />이 삭제되었거나 토큰을 만든 <see cref="T:System.Threading.CancellationTokenSource" />가 취소되었습니다.</exception>
      <exception cref="T:System.InvalidOperationException">내부 컬렉션은 이 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 외부에서 수정되었습니다. 그렇지 않으면 BlockingCollection은 추가를 완료한 것으로 표시되거나 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />이 비어 있습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.TakeFromAny(System.Collections.Concurrent.BlockingCollection{`0}[],`0@)">
      <summary>지정된 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 중 하나에서 항목을 빼냅니다.</summary>
      <returns>
        <paramref name="collections" /> 배열에서 항목이 제거된 컬렉션의 인덱스입니다.</returns>
      <param name="collections">컬렉션의 배열입니다.</param>
      <param name="item">컬렉션 중 하나에서 제거된 항목입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스가 하나 이상 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collections" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="collections" /> 횟수가 최대 크기(STA의 경우 62, MTA의 경우 63)보다 큽니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="collections" /> 인수가 길이가 0 인 배열이거나 Null 요소를 포함하거나 <see cref="M:System.Collections.Concurrent.BlockingCollection`1.CompleteAdding" />이 컬렉션에서 호출됩니다.</exception>
      <exception cref="T:System.InvalidOperationException">기본 컬렉션 중 하나 이상이 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 외부에서 수정되었습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.TakeFromAny(System.Collections.Concurrent.BlockingCollection{`0}[],`0@,System.Threading.CancellationToken)">
      <summary>지정된 취소 토큰을 관찰하는 동안 지정된 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 중 하나에서 항목을 빼냅니다.</summary>
      <returns>
        <paramref name="collections" /> 배열에서 항목이 제거된 컬렉션의 인덱스입니다.</returns>
      <param name="collections">컬렉션의 배열입니다.</param>
      <param name="item">컬렉션 중 하나에서 제거된 항목입니다.</param>
      <param name="cancellationToken">관찰할 취소 토큰입니다.</param>
      <exception cref="T:System.OperationCanceledException">
        <see cref="T:System.Threading.CancellationToken" />이 취소된 경우.</exception>
      <exception cref="T:System.InvalidOperationException">기본 컬렉션 중 하나 이상이 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 외부에서 수정되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collections" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="collections" /> 횟수가 최대 크기(STA의 경우 62, MTA의 경우 63)보다 큽니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="collections" /> 인수가 길이가 0 인 배열이거나 Null 요소를 포함하거나 <see cref="M:System.Collections.Concurrent.BlockingCollection`1.CompleteAdding" />이 컬렉션에서 호출됩니다.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스가 하나 이상 삭제되었습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.ToArray">
      <summary>
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스의 항목을 새 배열로 복사합니다.</summary>
      <returns>컬렉션의 요소 복사본을 포함하는 배열입니다.</returns>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.TryAdd(`0)">
      <summary>
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />에 지정된 항목을 추가하려고 시도합니다.</summary>
      <returns>
        <paramref name="item" />을 추가할 수 있는 경우 true이고 그렇지 않으면 false입니다.항목이 중복이고 기본 컬렉션에서 중복 항목을 받아들이지 않는 경우에는 <see cref="T:System.InvalidOperationException" />이 throw됩니다.</returns>
      <param name="item">컬렉션에 추가될 항목입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />는 추가 관련하여 완료된 것으로 표시되었습니다.또는기본 컬렉션에서 항목을 적용하지 않았습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.TryAdd(`0,System.Int32)">
      <summary>지정된 시간 안에 지정된 항목을 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />에 추가하려고 시도합니다.</summary>
      <returns>지정된 시간 안에 <paramref name="item" />을 컬렉션에 추가할 수 있으면 true이고, 그렇지 않으면 false입니다.항목이 중복이고 기본 컬렉션에서 중복 항목을 받아들이지 않는 경우에는 <see cref="T:System.InvalidOperationException" />이 throw됩니다.</returns>
      <param name="item">컬렉션에 추가될 항목입니다.</param>
      <param name="millisecondsTimeout">대기할 시간(밀리초)이거나, 무기한 대기할 경우 <see cref="F:System.Threading.Timeout.Infinite" />(-1)입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" />이 무기한 시간 제한을 나타내는 -1 이외의 음수인 경우</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />는 추가 관련하여 완료된 것으로 표시되었습니다.또는기본 컬렉션에서 항목을 적용하지 않았습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.TryAdd(`0,System.Int32,System.Threading.CancellationToken)">
      <summary>취소 토큰을 관찰하는 동안 지정된 시간 안에 지정된 항목을 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />에 추가하려고 시도합니다.</summary>
      <returns>지정된 시간 안에 <paramref name="item" />을 컬렉션에 추가할 수 있으면 true이고, 그렇지 않으면 false입니다.항목이 중복이고 기본 컬렉션에서 중복 항목을 받아들이지 않는 경우에는 <see cref="T:System.InvalidOperationException" />이 throw됩니다.</returns>
      <param name="item">컬렉션에 추가될 항목입니다.</param>
      <param name="millisecondsTimeout">대기할 시간(밀리초)이거나, 무기한 대기할 경우 <see cref="F:System.Threading.Timeout.Infinite" />(-1)입니다.</param>
      <param name="cancellationToken">관찰할 취소 토큰입니다.</param>
      <exception cref="T:System.OperationCanceledException">
        <see cref="T:System.Threading.CancellationToken" />이 취소된 경우.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />이 삭제되었거나 내부 <see cref="T:System.Threading.CancellationTokenSource" />가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" />이 무기한 시간 제한을 나타내는 -1 이외의 음수인 경우</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />는 추가 관련하여 완료된 것으로 표시되었습니다.또는기본 컬렉션에서 항목을 적용하지 않았습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.TryAdd(`0,System.TimeSpan)">
      <summary>
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />에 지정된 항목을 추가하려고 시도합니다.</summary>
      <returns>지정된 시간 범위 내에서 <paramref name="item" />을 컬렉션에 추가할 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">컬렉션에 추가될 항목입니다.</param>
      <param name="timeout">대기할 시간(밀리초)을 나타내는 <see cref="T:System.TimeSpan" />이거나, 무한 대기하도록 -1밀리초를 나타내는 <see cref="T:System.TimeSpan" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" />이 무기한 시간 제한을 나타내는 -1밀리초 이외의 음수이거나 시간 제한이 <see cref="F:System.Int32.MaxValue" />보다 큰 경우.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />는 추가 관련하여 완료된 것으로 표시되었습니다.또는기본 컬렉션에서 항목을 적용하지 않았습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.TryAddToAny(System.Collections.Concurrent.BlockingCollection{`0}[],`0)">
      <summary>지정된 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 중 하나에 지정된 항목을 추가하려고 시도합니다.</summary>
      <returns>항목이 추가된 <paramref name="collections" /> 배열의 컬렉션 인덱스이거나, 항목을 추가할 수 없으면 -1입니다.</returns>
      <param name="collections">컬렉션의 배열입니다.</param>
      <param name="item">컬렉션 중 하나에 추가될 항목입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스가 하나 이상 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collections" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="collections" /> 횟수가 최대 크기(STA의 경우 62, MTA의 경우 63)보다 큽니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="collections" /> 인수가 길이가 0 인 배열이거나 Null 요소를 포함하거나 또는 컬렉션 중 하나 이상이 추가를 완료한 것으로 표시되어 있습니다.</exception>
      <exception cref="T:System.InvalidOperationException">하나 이상의 기본 컬렉션에서 항목을 허용하지 않습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.TryAddToAny(System.Collections.Concurrent.BlockingCollection{`0}[],`0,System.Int32)">
      <summary>지정된 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 중 하나에 지정된 항목을 추가하려고 시도합니다.</summary>
      <returns>항목이 추가된 <paramref name="collections" /> 배열의 컬렉션 인덱스이거나, 항목을 추가할 수 없으면 -1입니다.</returns>
      <param name="collections">컬렉션의 배열입니다.</param>
      <param name="item">컬렉션 중 하나에 추가될 항목입니다.</param>
      <param name="millisecondsTimeout">대기할 시간(밀리초)이거나, 무기한 대기할 경우 <see cref="F:System.Threading.Timeout.Infinite" />(-1)입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스가 하나 이상 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collections" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" />이 무기한 시간 제한을 나타내는 -1 이외의 음수인 경우또는<paramref name="collections" /> 횟수가 최대 크기(STA의 경우 62, MTA의 경우 63)보다 큽니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="collections" /> 인수가 길이가 0 인 배열이거나 Null 요소를 포함하거나 또는 컬렉션 중 하나 이상이 추가를 완료한 것으로 표시되어 있습니다.</exception>
      <exception cref="T:System.InvalidOperationException">하나 이상의 기본 컬렉션에서 항목을 허용하지 않습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.TryAddToAny(System.Collections.Concurrent.BlockingCollection{`0}[],`0,System.Int32,System.Threading.CancellationToken)">
      <summary>지정된 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 중 하나에 지정된 항목을 추가하려고 시도합니다.</summary>
      <returns>항목이 추가된 <paramref name="collections" /> 배열의 컬렉션 인덱스이거나, 항목을 추가할 수 없으면 -1입니다.</returns>
      <param name="collections">컬렉션의 배열입니다.</param>
      <param name="item">컬렉션 중 하나에 추가될 항목입니다.</param>
      <param name="millisecondsTimeout">대기할 시간(밀리초)이거나, 무기한 대기할 경우 <see cref="F:System.Threading.Timeout.Infinite" />(-1)입니다.</param>
      <param name="cancellationToken">관찰할 취소 토큰입니다.</param>
      <exception cref="T:System.OperationCanceledException">
        <see cref="T:System.Threading.CancellationToken" />이 취소된 경우.</exception>
      <exception cref="T:System.InvalidOperationException">하나 이상의 기본 컬렉션에서 항목을 허용하지 않습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collections" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" />이 무기한 시간 제한을 나타내는 -1 이외의 음수인 경우또는<paramref name="collections" /> 횟수가 최대 크기(STA의 경우 62, MTA의 경우 63)보다 큽니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="collections" /> 인수가 길이가 0 인 배열이거나 Null 요소를 포함하거나 또는 컬렉션 중 하나 이상이 추가를 완료한 것으로 표시되어 있습니다.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스가 하나 이상 삭제되었습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.TryAddToAny(System.Collections.Concurrent.BlockingCollection{`0}[],`0,System.TimeSpan)">
      <summary>지정된 취소 토큰을 관찰하는 동안 지정된 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 중 하나에 지정된 항목을 추가해 봅니다.</summary>
      <returns>항목이 추가된 <paramref name="collections" /> 배열의 컬렉션 인덱스이거나, 항목을 추가할 수 없으면 -1입니다.</returns>
      <param name="collections">컬렉션의 배열입니다.</param>
      <param name="item">컬렉션 중 하나에 추가될 항목입니다.</param>
      <param name="timeout">대기할 시간(밀리초)을 나타내는 <see cref="T:System.TimeSpan" />이거나, 무한 대기하도록 -1밀리초를 나타내는 <see cref="T:System.TimeSpan" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="cancellationToken" />을 만든 하나 이상의 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 또는 <see cref="T:System.Threading.CancellationTokenSource" />가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collections" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" />이 무기한 시간 제한을 나타내는 -1밀리초 이외의 음수이거나 시간 제한이 <see cref="F:System.Int32.MaxValue" />보다 큰 경우.또는<paramref name="collections" /> 횟수가 최대 크기(STA의 경우 62, MTA의 경우 63)보다 큽니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="collections" /> 인수가 길이가 0 인 배열이거나 Null 요소를 포함하거나 또는 컬렉션 중 하나 이상이 추가를 완료한 것으로 표시되어 있습니다.</exception>
      <exception cref="T:System.InvalidOperationException">하나 이상의 기본 컬렉션에서 항목을 허용하지 않습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.TryTake(`0@)">
      <summary>
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />에서 항목을 제거하려고 시도합니다.</summary>
      <returns>항목을 제거할 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">컬렉션에서 제거할 항목입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
      <exception cref="T:System.InvalidOperationException">기본 컬렉션이 이 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 외부에서 수정되었습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.TryTake(`0@,System.Int32)">
      <summary>지정된 시간 안에 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />에서 항목을 제거하려고 시도합니다.</summary>
      <returns>지정된 시간 안에 항목을 컬렉션에서 제거할 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">컬렉션에서 제거할 항목입니다.</param>
      <param name="millisecondsTimeout">대기할 시간(밀리초)이거나, 무기한 대기할 경우 <see cref="F:System.Threading.Timeout.Infinite" />(-1)입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" />이 무기한 시간 제한을 나타내는 -1 이외의 음수인 경우</exception>
      <exception cref="T:System.InvalidOperationException">기본 컬렉션이 이 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 외부에서 수정되었습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.TryTake(`0@,System.Int32,System.Threading.CancellationToken)">
      <summary>취소 토큰을 관찰하는 동안 지정된 시간 안에 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />에서 항목을 제거하려고 시도합니다.</summary>
      <returns>지정된 시간 안에 항목을 컬렉션에서 제거할 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">컬렉션에서 제거할 항목입니다.</param>
      <param name="millisecondsTimeout">대기할 시간(밀리초)이거나, 무기한 대기할 경우 <see cref="F:System.Threading.Timeout.Infinite" />(-1)입니다.</param>
      <param name="cancellationToken">관찰할 취소 토큰입니다.</param>
      <exception cref="T:System.OperationCanceledException">
        <see cref="T:System.Threading.CancellationToken" />이 취소되었습니다.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />이 삭제되었거나 내부 <see cref="T:System.Threading.CancellationTokenSource" />가 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" />이 무기한 시간 제한을 나타내는 -1 이외의 음수인 경우</exception>
      <exception cref="T:System.InvalidOperationException">기본 컬렉션이 이 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 외부에서 수정되었습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.TryTake(`0@,System.TimeSpan)">
      <summary>지정된 시간 안에 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />에서 항목을 제거하려고 시도합니다.</summary>
      <returns>지정된 시간 안에 항목을 컬렉션에서 제거할 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">컬렉션에서 제거할 항목입니다.</param>
      <param name="timeout">대기할 시간(밀리초)을 나타내는 개체이거나, 무한 대기하도록 -1밀리초를 나타내는 개체입니다. </param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" />가 삭제된 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" />이 무기한 시간 제한을 나타내는 -1밀리초 이외의 음수인 경우또는 <paramref name="timeout" />가 <see cref="F:System.Int32.MaxValue" />보다 큰 경우</exception>
      <exception cref="T:System.InvalidOperationException">기본 컬렉션이 이 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 외부에서 수정되었습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.TryTakeFromAny(System.Collections.Concurrent.BlockingCollection{`0}[],`0@)">
      <summary>지정된 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 중 하나에서 항목을 제거하려고 시도합니다.</summary>
      <returns>항목이 제거된 <paramref name="collections" /> 배열의 컬렉션 인덱스이거나, 항목을 제거할 수 없으면 -1입니다.</returns>
      <param name="collections">컬렉션의 배열입니다.</param>
      <param name="item">컬렉션 중 하나에서 제거된 항목입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스가 하나 이상 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collections" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="collections" /> 횟수가 최대 크기(STA의 경우 62, MTA의 경우 63)보다 큽니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="collections" /> 인수가 길이가 0 인 배열이거나 Null 요소를 포함합니다.</exception>
      <exception cref="T:System.InvalidOperationException">기본 컬렉션 중 하나 이상이 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 외부에서 수정되었습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.TryTakeFromAny(System.Collections.Concurrent.BlockingCollection{`0}[],`0@,System.Int32)">
      <summary>지정된 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 중 하나에서 항목을 제거하려고 시도합니다.</summary>
      <returns>항목이 제거된 <paramref name="collections" /> 배열의 컬렉션 인덱스이거나, 항목을 제거할 수 없으면 -1입니다.</returns>
      <param name="collections">컬렉션의 배열입니다.</param>
      <param name="item">컬렉션 중 하나에서 제거된 항목입니다.</param>
      <param name="millisecondsTimeout">대기할 시간(밀리초)이거나, 무기한 대기할 경우 <see cref="F:System.Threading.Timeout.Infinite" />(-1)입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스가 하나 이상 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collections" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" />이 무기한 시간 제한을 나타내는 -1 이외의 음수인 경우또는<paramref name="collections" /> 횟수가 최대 크기(STA의 경우 62, MTA의 경우 63)보다 큽니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="collections" /> 인수가 길이가 0 인 배열이거나 Null 요소를 포함합니다.</exception>
      <exception cref="T:System.InvalidOperationException">기본 컬렉션 중 하나 이상이 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 외부에서 수정되었습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.TryTakeFromAny(System.Collections.Concurrent.BlockingCollection{`0}[],`0@,System.Int32,System.Threading.CancellationToken)">
      <summary>지정된 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 중 하나에서 항목을 제거하려고 시도합니다.</summary>
      <returns>항목이 제거된 <paramref name="collections" /> 배열의 컬렉션 인덱스이거나, 항목을 제거할 수 없으면 -1입니다.</returns>
      <param name="collections">컬렉션의 배열입니다.</param>
      <param name="item">컬렉션 중 하나에서 제거된 항목입니다.</param>
      <param name="millisecondsTimeout">대기할 시간(밀리초)이거나, 무기한 대기할 경우 <see cref="F:System.Threading.Timeout.Infinite" />(-1)입니다.</param>
      <param name="cancellationToken">관찰할 취소 토큰입니다.</param>
      <exception cref="T:System.OperationCanceledException">
        <see cref="T:System.Threading.CancellationToken" />이 취소된 경우.</exception>
      <exception cref="T:System.InvalidOperationException">기본 컬렉션 중 하나 이상이 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 외부에서 수정되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collections" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" />이 무기한 시간 제한을 나타내는 -1 이외의 음수인 경우또는<paramref name="collections" /> 횟수가 최대 크기(STA의 경우 62, MTA의 경우 63)보다 큽니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="collections" /> 인수가 길이가 0 인 배열이거나 Null 요소를 포함합니다.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스가 하나 이상 삭제되었습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.BlockingCollection`1.TryTakeFromAny(System.Collections.Concurrent.BlockingCollection{`0}[],`0@,System.TimeSpan)">
      <summary>지정된 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 중 하나에서 항목을 제거하려고 시도합니다.</summary>
      <returns>항목이 제거된 <paramref name="collections" /> 배열의 컬렉션 인덱스이거나, 항목을 제거할 수 없으면 -1입니다.</returns>
      <param name="collections">컬렉션의 배열입니다.</param>
      <param name="item">컬렉션 중 하나에서 제거된 항목입니다.</param>
      <param name="timeout">대기할 시간(밀리초)을 나타내는 <see cref="T:System.TimeSpan" />이거나, 무한 대기하도록 -1밀리초를 나타내는 <see cref="T:System.TimeSpan" />입니다.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스가 하나 이상 삭제되었습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collections" /> 인수가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" />이 무기한 시간 제한을 나타내는 -1밀리초 이외의 음수이거나 시간 제한이 <see cref="F:System.Int32.MaxValue" />보다 큰 경우.또는<paramref name="collections" /> 횟수가 최대 크기(STA의 경우 62, MTA의 경우 63)보다 큽니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="collections" /> 인수가 길이가 0 인 배열이거나 Null 요소를 포함합니다.</exception>
      <exception cref="T:System.InvalidOperationException">기본 컬렉션 중 하나 이상이 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 인스턴스 외부에서 수정되었습니다.</exception>
    </member>
    <member name="T:System.Collections.Concurrent.ConcurrentBag`1">
      <summary>스레드로부터 안전한 정렬되지 않은 개체 컬렉션을 나타냅니다.</summary>
      <typeparam name="T">컬렉션에 저장될 요소의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentBag`1.#ctor">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentBag`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>지정된 컬렉션에서 복사한 요소가 포함된 <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="collection">해당 요소가 새 <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />에 복사되는 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" />가 null 참조(Visual Basic의 경우 Nothing)인 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentBag`1.Add(`0)">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />에 개체를 추가합니다.</summary>
      <param name="item">개체가 <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />에 추가됩니다.값은 참조 형식의 경우 null 참조(Visual Basic에서는 Nothing)일 수 있습니다.</param>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentBag`1.CopyTo(`0[],System.Int32)">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" /> 요소를 지정한 배열 인덱스에서 시작하여 기존의 1차원 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from the <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">복사가 시작되는 <paramref name="array" />의 0부터 시작하는 인덱스입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null 참조(Visual Basic의 경우 Nothing)인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" />가 <paramref name="array" />의 길이보다 같거나 큰 경우 -또는- 소스 <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />의 요소 수가 대상 <paramref name="array" />의 <paramref name="index" />부터 끝까지의 사용 가능한 공간보다 큰 경우.</exception>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentBag`1.Count">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />에 포함된 요소 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />에 포함된 요소 수입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentBag`1.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />의 콘텐츠에 대한 열거자입니다.</returns>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentBag`1.IsEmpty">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />가 비어 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />이 비어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentBag`1.System#Collections#Concurrent#IProducerConsumerCollection{T}#TryAdd(`0)">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />에 개체를 추가하려고 시도합니다.</summary>
      <returns>항상 true를 반환합니다.</returns>
      <param name="item">개체가 <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />에 추가됩니다.값은 참조 형식의 경우 null 참조(Visual Basic에서는 Nothing)일 수 있습니다.</param>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentBag`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>특정 <see cref="T:System.Array" /> 인덱스부터 시작하여 <see cref="T:System.Collections.ICollection" />의 요소를 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from the <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">복사가 시작되는 <paramref name="array" />의 0부터 시작하는 인덱스입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null 참조(Visual Basic의 경우 Nothing)인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />가 다차원 배열인 경우-또는- <paramref name="array" />에 0부터 시작하는 인덱스가 없습니다.-또는- <paramref name="index" />가 <paramref name="array" />의 길이와 같거나 큰 경우 -또는- 소스 <see cref="T:System.Collections.ICollection" />의 요소 수가 대상 <paramref name="array" />의 <paramref name="index" />부터 끝까지의 사용 가능한 공간보다 큰 경우.-또는- 소스 <see cref="T:System.Collections.ICollection" /> 형식을 대상 <paramref name="array" /> 형식으로 자동 캐스팅할 수 없는 경우</exception>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentBag`1.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 SyncRoot와 동기화되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 SyncRoot와 동기화되면 true이고, 그렇지 않으면 false입니다.<see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />의 경우 이 속성은 항상 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentBag`1.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.이 속성이 지원되지 않는 경우</summary>
      <returns>null을 반환합니다(Visual Basic에서는 Nothing).</returns>
      <exception cref="T:System.NotSupportedException">SyncRoot 속성은 지원되지 않습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentBag`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />의 콘텐츠에 대한 열거자입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentBag`1.ToArray">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" /> 요소를 새 배열에 복사합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />에서 복사된 요소의 스냅숏을 포함하는 새 배열입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentBag`1.TryPeek(`0@)">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />의 개체를 제거하지 않고 반환하려고 시도합니다.</summary>
      <returns>개체가 반환되었으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="result">이 메서드가 반환될 경우 <paramref name="result" />에는 <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />의 개체가 들어 있거나 <paramref name="T" />의 기본값(작업이 실패한 경우)이 들어 있습니다.</param>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentBag`1.TryTake(`0@)">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />에서 개체를 제거하고 반환하려고 시도합니다.</summary>
      <returns>개체가 제거되었으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="result">이 메서드가 반환될 경우 <paramref name="result" />에는 <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />에서 제거된 개체가 들어 있거나 <paramref name="T" />의 기본값(모음이 비어 있는 경우)이 들어 있습니다.</param>
    </member>
    <member name="T:System.Collections.Concurrent.ConcurrentDictionary`2">
      <summary>여러 개의 스레드에서 동시에 액세스할 수 있는 키/값 쌍의 스레드로부터 안전한 컬렉션을 나타냅니다. </summary>
      <typeparam name="TKey">사전에 있는 키의 형식입니다.</typeparam>
      <typeparam name="TValue">사전에 있는 값의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.#ctor">
      <summary>기본 동시성 수준과 기본 초기 용량을 갖고 있으며 키 형식에 대한 기본 비교자를 사용하는 빈 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{`0,`1}})">
      <summary>지정된 <see cref="T:System.Collections.Generic.IEnumerable`1" />에서 복사된 요소를 포함하고, 기본 동시성 수준과 기본 초기 용량을 갖고 있으며, 키 형식에 대한 기본 비교자를 사용하는 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="collection">요소가 새 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 복사되는 <see cref="T:System.Collections.Generic.IEnumerable`1" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 또는 해당 키 중 하나는 null입니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="collection" />에 중복 키가 하나 이상 포함된 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{`0,`1}},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>지정된 <see cref="T:System.Collections.IEnumerable" />에서 복사된 요소가 포함되어 있고, 기본 동시성 수준과 기본 초기 용량을 가지며, 지정된 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />를 사용하는 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="collection">요소가 새 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 복사되는 <see cref="T:System.Collections.Generic.IEnumerable`1" />입니다.</param>
      <param name="comparer">키를 비교할 때 사용하는 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 구현입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 또는 <paramref name="comparer" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>기본 동시성 수준과 용량을 갖고 있고 지정된 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />를 사용하는 비어 있는 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="comparer">키를 비교할 때 사용하는 같음 비교 구현입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparer" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.#ctor(System.Int32,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{`0,`1}},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>지정한 <see cref="T:System.Collections.IEnumerable" />에서 복사된 요소를 포함하고 지정한 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />를 사용하는 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="concurrencyLevel">
        <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />를 동시에 업데이트할 예상 스레드 수입니다.</param>
      <param name="collection">요소가 새 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 복사되는 <see cref="T:System.Collections.Generic.IEnumerable`1" />입니다.</param>
      <param name="comparer">키를 비교할 때 사용하는 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 구현입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 또는 <paramref name="comparer" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="concurrencyLevel" />가 1보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="collection" />에 중복 키가 하나 이상 포함된 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.#ctor(System.Int32,System.Int32)">
      <summary>지정된 동시성 수준과 용량을 갖고 있으며 키 형식에 대한 기본 비교자를 사용하는 비어 있는 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="concurrencyLevel">
        <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />를 동시에 업데이트할 예상 스레드 수입니다.</param>
      <param name="capacity">
        <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 포함될 수 있는 초기 요소 수입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="concurrencyLevel" />가 1보다 작은 경우또는<paramref name="capacity" />가 0보다 작은 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.#ctor(System.Int32,System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>지정된 동시성 수준과 지정된 초기 용량을 갖고 있고 지정된 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />를 사용하는 빈 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="concurrencyLevel">
        <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />를 동시에 업데이트할 예상 스레드 수입니다.</param>
      <param name="capacity">
        <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 포함될 수 있는 초기 요소 수입니다.</param>
      <param name="comparer">키를 비교할 때 사용하는 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 구현입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparer" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="concurrencyLevel" /> 또는 <paramref name="capacity" />가 1 미만인 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.AddOrUpdate(`0,System.Func{`0,`1},System.Func{`0,`1,`1})">
      <summary>지정된 함수를 사용하여 키가 아직 없는 경우 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 키/값 쌍을 추가하고, 키가 이미 있는 경우 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 키/값 쌍을 업데이트합니다.</summary>
      <returns>키의 새 값입니다.이 값은 addValueFactory(키가 없는 경우)의 결과이거나 updateValueFactory(키가 있는 경우)의 결과입니다.</returns>
      <param name="key">추가하거나 값을 업데이트해야 하는 키입니다.</param>
      <param name="addValueFactory">없는 키에 대한 값을 생성하는 데 사용되는 함수입니다.</param>
      <param name="updateValueFactory">키의 기존 값을 기준으로 기존 키의 새 값을 생성하는 데 사용되는 함수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />, <paramref name="addValueFactory" /> 또는 <paramref name="updateValueFactory" />가 null인 경우</exception>
      <exception cref="T:System.OverflowException">사전에 생성자에 지정된 요소(<see cref="F:System.Int32.MaxValue" />)의 최대 수가 이미 포함되어 있습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.AddOrUpdate(`0,`1,System.Func{`0,`1,`1})">
      <summary>키가 아직 없는 경우 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 키/값 쌍을 추가하고, 키가 이미 있는 경우 지정된 함수를 사용하여 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />의 키/값 쌍을 업데이트합니다.</summary>
      <returns>키의 새 값입니다.이 값은 addValue(키가 없는 경우)이거나 updateValueFactory의 결과(키가 있는 경우)입니다.</returns>
      <param name="key">추가하거나 값을 업데이트해야 하는 키입니다.</param>
      <param name="addValue">없는 키에 대해 추가할 값입니다.</param>
      <param name="updateValueFactory">키의 기존 값을 기준으로 기존 키의 새 값을 생성하는 데 사용되는 함수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 또는 <paramref name="updateValueFactory" />가 null인 경우</exception>
      <exception cref="T:System.OverflowException">사전에 생성자에 지정된 요소(<see cref="F:System.Int32.MaxValue" />)의 최대 수가 이미 포함되어 있습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.Clear">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에서 모든 키와 값을 제거합니다.</summary>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.ContainsKey(`0)">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 지정한 키가 들어 있는지 여부를 확인합니다.</summary>
      <returns>true if the <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">
        <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에서 찾을 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentDictionary`2.Count">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 포함된 키/값 쌍의 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 포함된 키/값 쌍의 수입니다.</returns>
      <exception cref="T:System.OverflowException">사전에 생성자에 지정된 요소(<see cref="F:System.Int32.MaxValue" />)의 최대 수가 이미 포함되어 있습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 대한 열거자입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(`0,System.Func{`0,`1})">
      <summary>키가 아직 없는 경우 지정된 함수를 사용하여 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 키/값 쌍을 추가합니다.</summary>
      <returns>키의 값입니다.사전에 키가 있는 경우 키의 기존 값이고, 사전에 키가 없는 경우 valueFactory에 의해 반환된 키의 새 값입니다.</returns>
      <param name="key">추가할 요소의 키입니다.</param>
      <param name="valueFactory">키에 대한 값을 생성하는 데 사용되는 함수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 또는 <paramref name="valueFactory" />가 null인 경우</exception>
      <exception cref="T:System.OverflowException">사전에 생성자에 지정된 요소(<see cref="F:System.Int32.MaxValue" />)의 최대 수가 이미 포함되어 있습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(`0,`1)">
      <summary>키가 아직 없는 경우 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 키/값 쌍을 추가합니다.</summary>
      <returns>키의 값입니다.사전에 키가 있는 경우 키의 기존 값이고, 사전에 키가 없는 경우 새 값입니다.</returns>
      <param name="key">추가할 요소의 키입니다.</param>
      <param name="value">키가 아직 없는 경우 추가할 값입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
      <exception cref="T:System.OverflowException">사전에 생성자에 지정된 요소(<see cref="F:System.Int32.MaxValue" />)의 최대 수가 이미 포함되어 있습니다.</exception>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentDictionary`2.IsEmpty">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />가 비어 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />이 비어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentDictionary`2.Item(`0)">
      <summary>지정된 키에 연결된 값을 가져오거나 설정합니다.</summary>
      <returns>지정된 인덱스에 있는 키/값 쌍의 값입니다.</returns>
      <param name="key">가져오거나 설정할 값의 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">속성을 검색할 때 컬렉션에 <paramref name="key" />가 없는 경우</exception>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentDictionary`2.Keys">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" />의 키를 포함하는 컬렉션을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2" />에 있는 키의 컬렉션입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>컬렉션에 항목을 추가합니다.</summary>
      <param name="keyValuePair">사전에 추가할 <see cref="T:System.Collections.Generic.KeyValuePair`2" />입니다.</param>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>지정된 키를 갖는 요소가 <see cref="T:System.Collections.Generic.ICollection`1" />에 들어 있는지 여부를 가져옵니다.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.ICollection`1" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 찾을 키입니다.</param>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>지정한 배열 인덱스부터 <see cref="T:System.Collections.ICollection" />의 요소를 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.ICollection" />에서 복사한 요소의 대상인 1차원 배열입니다.배열에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">복사가 시작되는 <paramref name="array" />의 0부터 시작하는 인덱스입니다.</param>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.ICollection" />가 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>true if the <see cref="T:System.Collections.ICollection" /> is read-only; otherwise, false. </returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>컬렉션에서 지정된 키/값 쌍을 제거합니다.</summary>
      <returns>요소가 성공적으로 제거되면 true이고, 그렇지 않으면 false입니다.이 메서드는 <paramref name="key" />가 원래 <see cref="T:System.Collections.Generic.ICollection`1" />에 없는 경우에도 false를 반환합니다.</returns>
      <param name="keyValuePair">제거할 <see cref="T:System.Collections.Generic.KeyValuePair`2" />입니다.</param>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Add(`0,`1)">
      <summary>지정된 키와 값을 <see cref="T:System.Collections.Generic.IDictionary`2" />에 추가합니다.</summary>
      <param name="key">추가할 요소의 키로 사용할 개체입니다.</param>
      <param name="value">추가할 요소의 값으로 사용할 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">같은 키를 가진 요소가 이미 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 있는 경우</exception>
      <exception cref="T:System.OverflowException">사전에 생성자에 지정된 요소(<see cref="F:System.Int32.MaxValue" />)의 최대 수가 이미 포함되어 있습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Remove(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.IDictionary`2" />에서 지정한 키를 가진 요소를 제거합니다.</summary>
      <returns>요소가 성공적으로 제거되면 true이고, 그렇지 않으면 false입니다.This method also returns false if <paramref name="key" /> was not found in the original <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
      <param name="key">제거할 요소의 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" />의 키를 포함하는 컬렉션을 가져옵니다.</summary>
      <returns>키를 포함 하는 컬렉션의 <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" />의 값이 들어 있는 컬렉션을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2" />의 값이 포함된 컬렉션입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>지정한 배열 인덱스부터 <see cref="T:System.Collections.ICollection" />의 요소를 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.ICollection" />에서 복사한 요소의 대상인 1차원 배열입니다.배열에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">복사가 시작되는 <paramref name="array" />의 0부터 시작하는 인덱스입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" />가 <paramref name="array" />의 길이보다 크거나 같은 경우 또는소스 <see cref="T:System.Collections.ICollection" />의 요소 수가 <paramref name="index" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우</exception>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 SyncRoot와 동기화되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.<see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />의 경우 이 속성은 항상 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.이 속성이 지원되지 않는 경우</summary>
      <returns>항상 null을 반환합니다.</returns>
      <exception cref="T:System.NotSupportedException">이 속성이 지원되지 않는 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>지정한 키와 값을 사전에 추가합니다.</summary>
      <param name="key">키로 사용할 개체입니다.</param>
      <param name="value">값으로 사용할 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" />의 형식을 <see cref="T:System.Collections.Generic.Dictionary`2" /> 키 형식에 할당할 수 없는 경우. 또는 <paramref name="value" />의 형식을 <see cref="T:System.Collections.Generic.Dictionary`2" />의 값 형식에 할당할 수 없는 경우 또는같은 키가 있는 값이 이미 <see cref="T:System.Collections.Generic.Dictionary`2" />에 있는 경우</exception>
      <exception cref="T:System.OverflowException">사전에 생성자에 지정된 요소(<see cref="F:System.Int32.MaxValue" />)의 최대 수가 이미 포함되어 있습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>지정된 키를 갖는 요소가 <see cref="T:System.Collections.Generic.IDictionary`2" />에 들어 있는지 여부를 가져옵니다.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.IDictionary`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">
        <see cref="T:System.Collections.Generic.IDictionary`2" />에서 찾을 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.IDictionary`2" />에 대한 <see cref="T:System.Collections.IDictionaryEnumerator" />를 제공합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IDictionary`2" />에 대한 <see cref="T:System.Collections.IDictionaryEnumerator" />입니다.</returns>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>
        <see cref="T:System.Collections.Generic.IDictionary`2" />의 크기가 고정되어 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.IDictionary`2" /> has a fixed size; otherwise, false.<see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />의 경우 이 속성은 항상 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.Generic.IDictionary`2" />가 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.IDictionary`2" /> is read-only; otherwise, false.<see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />의 경우 이 속성은 항상 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>지정된 키에 연결된 값을 가져오거나 설정합니다.</summary>
      <returns>지정한 키와 연결된 값입니다. <paramref name="key" />가 사전에 없거나 <paramref name="key" />의 형식을 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />의 키 형식에 할당할 수 없으면 null입니다.</returns>
      <param name="key">가져오거나 설정할 값의 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">값이 할당되어 있고, <paramref name="key" />가 키 형식에 할당할 수 없는 형식이거나 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />의 값 형식인 경우</exception>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#IDictionary#Keys">
      <summary>
        <see cref="T:System.Collections.Generic.IDictionary`2" />의 키를 포함하는 <see cref="T:System.Collections.ICollection" />을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IDictionary`2" />의 키를 포함하는 인터페이스입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>
        <see cref="T:System.Collections.IDictionary" />에서 지정한 키를 가진 요소를 제거합니다.</summary>
      <param name="key">제거할 요소의 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#IDictionary#Values">
      <summary>
        <see cref="T:System.Collections.IDictionary" />의 값이 들어 있는 <see cref="T:System.Collections.ICollection" />을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" />의 값을 포함하는 인터페이스입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 대한 열거자입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.ToArray">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 저장되어 있는 키/값 쌍을 새 배열에 복사합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에서 복사된 키/값 쌍의 스냅숏을 포함하는 새 배열입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.TryAdd(`0,`1)">
      <summary>지정된 키와 값을 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 추가하려고 시도합니다.</summary>
      <returns>키/값 쌍이 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 추가되었으면 true이고, 키가 이미 존재하면 false입니다.</returns>
      <param name="key">추가할 요소의 키입니다.</param>
      <param name="value">추가할 요소의 값입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
      <exception cref="T:System.OverflowException">사전에 생성자에 지정된 요소(<see cref="F:System.Int32.MaxValue" />)의 최대 수가 이미 포함되어 있습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.TryGetValue(`0,`1@)">
      <summary>지정된 키에 연결된 값을 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에서 가져오려고 시도합니다.</summary>
      <returns>키가 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="key">가져올 값의 키입니다.</param>
      <param name="value">이 메서드가 반환 될 때 포함에서 개체는 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" /> 작업이 실패 한 경우 형식의 기본값 또는 지정된 된 키를 포함 합니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.TryRemove(`0,`1@)">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에서 지정된 키가 있는 값을 제거하고 반환하려고 시도합니다.</summary>
      <returns>개체가 성공적으로 제거되면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="key">제거하고 반환할 요소의 키입니다.</param>
      <param name="value">이 메서드가 반환될 경우 <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />에서 제거된 개체가 들어 있거나 TValue 형식의 기본값(<paramref name="key" />이 존재하지 않는 경우)이 들어 있습니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentDictionary`2.TryUpdate(`0,`1,`1)">
      <summary>지정된 키의 기존 값을 지정된 값과 비교하여 두 값이 같으면 키를 세 번째 값으로 업데이트합니다.</summary>
      <returns>
        <paramref name="key" />가 있는 값이 <paramref name="comparisonValue" />와 같아 <paramref name="newValue" />로 바뀐 경우 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="key">
        <paramref name="comparisonValue" />와 비교되어 바뀔 수 있는 값을 가진 키입니다.</param>
      <param name="newValue">비교 결과가 같은 경우 지정된 <paramref name="key" />가 있는 요소의 값을 바꿀 값입니다.</param>
      <param name="comparisonValue">지정된 <paramref name="key" />가 있는 요소의 값과 비교할 값입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentDictionary`2.Values">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" />의 값이 들어 있는 컬렉션을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2" />의 값이 포함된 컬렉션입니다. </returns>
    </member>
    <member name="T:System.Collections.Concurrent.ConcurrentQueue`1">
      <summary>스레드로부터 안전한 FIFO(선입선출) 방식의 컬렉션을 나타냅니다.</summary>
      <typeparam name="T">큐에 있는 요소의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentQueue`1.#ctor">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentQueue`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>지정된 컬렉션에서 복사한 요소가 포함된 <see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="collection">해당 요소가 새 <see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" />에 복사되는 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 인수가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentQueue`1.CopyTo(`0[],System.Int32)">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" /> 요소를 지정한 배열 인덱스에서 시작하여 기존의 1차원 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" />에서 복사한 요소의 대상인 일차원 <see cref="T:System.Array" />입니다.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />는 null 참조(Visual Basic에서는 Nothing)입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" />가 <paramref name="array" />의 길이와 같거나 큰 경우 -또는- 소스 <see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" />의 요소 수가 대상 <paramref name="array" />의 <paramref name="index" />부터 끝까지의 사용 가능한 공간보다 큰 경우.</exception>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentQueue`1.Count">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" />에 포함된 요소 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" />에 포함된 요소 수입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentQueue`1.Enqueue(`0)">
      <summary>개체를 <see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" />의 끝 부분에 추가합니다.</summary>
      <param name="item">
        <see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" />의 끝 부분에 추가할 개체입니다.값은 참조 형식의 경우 null 참조(Visual Basic에서는 Nothing)일 수 있습니다.</param>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentQueue`1.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" />의 콘텐츠에 대한 열거자입니다.</returns>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentQueue`1.IsEmpty">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" />가 비어 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" />이 비어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentQueue`1.System#Collections#Concurrent#IProducerConsumerCollection{T}#TryAdd(`0)">
      <summary>
        <see cref="T:System.Collections.Concurrent.IProducerConsumerCollection`1" />에 개체를 추가하려고 시도합니다.</summary>
      <returns>개체가 성공적으로 추가되었으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Concurrent.IProducerConsumerCollection`1" />에 추가할 개체입니다.값은 참조 형식의 경우 null 참조(Visual Basic에서는 Nothing)일 수 있습니다.</param>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentQueue`1.System#Collections#Concurrent#IProducerConsumerCollection{T}#TryTake(`0@)">
      <summary>
        <see cref="T:System.Collections.Concurrent.IProducerConsumerCollection`1" />에서 개체를 제거하고 반환하려고 시도합니다.</summary>
      <returns>요소가 성공적으로 제거되고 반환되었으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">이 메서드가 반환될 경우 작업이 성공했으면 <paramref name="item" />에는 제거된 개체가 들어 있습니다.제거할 수 있는 개체가 없으면 이 값은 지정되지 않습니다.</param>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentQueue`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>특정 <see cref="T:System.Array" /> 인덱스에서 시작하여 <see cref="T:System.Collections.ICollection" />의 요소를 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Concurrent.ConcurrentBag`1" />에서 복사한 요소의 대상인 일차원 <see cref="T:System.Array" />입니다.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />는 null 참조(Visual Basic에서는 Nothing)입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />가 다차원 배열인 경우 -또는- <paramref name="array" />에 0부터 시작하는 인덱스가 없습니다. -또는- <paramref name="index" />가 <paramref name="array" />의 길이와 같거나 큰 경우 -또는- 소스 <see cref="T:System.Collections.ICollection" />의 요소 수가 대상 <paramref name="array" />의 <paramref name="index" />부터 끝까지의 사용 가능한 공간보다 큰 경우. -또는- 소스 <see cref="T:System.Collections.ICollection" /> 형식을 대상 <paramref name="array" /> 형식으로 자동 캐스팅할 수 없는 경우</exception>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentQueue`1.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 SyncRoot와 동기화되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 SyncRoot와 동기화되면 true이고, 그렇지 않으면 false입니다.<see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" />의 경우 이 속성은 항상 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentQueue`1.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.이 속성이 지원되지 않는 경우</summary>
      <returns>null을 반환합니다(Visual Basic의 경우 Nothing).</returns>
      <exception cref="T:System.NotSupportedException">SyncRoot 속성은 지원되지 않습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentQueue`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션에서 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.IEnumerator" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentQueue`1.ToArray">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" />에 저장되어 있는 요소를 새 배열에 복사합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" />에서 복사된 요소의 스냅숏을 포함하는 새 배열입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentQueue`1.TryDequeue(`0@)">
      <summary>concurrent queue의 시작 부분에서 개체를 제거하고 반환하려고 시도합니다.</summary>
      <returns>요소가 <see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" />의 시작 부분에서 성공적으로 제거되고 반환되었으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="result">이 메서드가 반환될 경우 작업이 성공했으면 <paramref name="result" />에는 제거된 개체가 들어 있습니다.제거할 수 있는 개체가 없으면 이 값은 지정되지 않습니다.</param>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentQueue`1.TryPeek(`0@)">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" />의 시작 부분에서 개체를 제거하지 않고 반환하려고 시도합니다.</summary>
      <returns>개체가 성공적으로 반환되었으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="result">이 메서드가 반환될 경우 <paramref name="result" />에는 <see cref="T:System.Collections.Concurrent.ConcurrentQueue`1" />의 시작 부분에 있는 개체가 들어 있거나 지정되지 않은 값(작업이 실패한 경우)이 들어 있습니다.</param>
    </member>
    <member name="T:System.Collections.Concurrent.ConcurrentStack`1">
      <summary>스레드로부터 안전한 LIFO(후입선출) 방식의 컬렉션을 나타냅니다.</summary>
      <typeparam name="T">스택에 있는 요소의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentStack`1.#ctor">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentStack`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>지정된 컬렉션에서 복사한 요소가 포함된 <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="collection">해당 요소가 새 <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />에 복사되는 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="collection" /> argument is null.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentStack`1.Clear">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />에서 개체를 모두 제거합니다.</summary>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentStack`1.CopyTo(`0[],System.Int32)">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" /> 요소를 지정한 배열 인덱스에서 시작하여 기존의 1차원 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />에서 복사한 요소의 대상인 1차원 <see cref="T:System.Array" />입니다.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 0부터 시작하는 인덱스입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> is equal to or greater than the length of the <paramref name="array" /> -or- The number of elements in the source <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentStack`1.Count">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />에 포함된 요소 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />에 포함된 요소 수입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentStack`1.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />에 대한 열거자입니다.</returns>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentStack`1.IsEmpty">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />가 비어 있는지를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />이 비어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentStack`1.Push(`0)">
      <summary>개체를 <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />의 맨 위에 삽입합니다.</summary>
      <param name="item">
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />에 적용할 개체입니다.값은 참조 형식의 경우 null 참조(Visual Basic에서는 Nothing)일 수 있습니다.</param>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentStack`1.PushRange(`0[])">
      <summary>여러 개체를 <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />의 맨 위에 원자 단위로 삽입합니다.</summary>
      <param name="items">
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />에 적용할 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="items" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentStack`1.PushRange(`0[],System.Int32,System.Int32)">
      <summary>여러 개체를 <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />의 맨 위에 원자 단위로 삽입합니다.</summary>
      <param name="items">
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />에 적용할 개체입니다.</param>
      <param name="startIndex">
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />의 맨 위에 요소를 삽입하기 시작할 <paramref name="items" />의 0부터 시작하는 오프셋입니다.</param>
      <param name="count">
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />의 맨 위에 삽입할 요소의 수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="items" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> or <paramref name="count" /> is negative.Or <paramref name="startIndex" /> is greater than or equal to the length of <paramref name="items" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> + <paramref name="count" /> is greater than the length of <paramref name="items" />.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentStack`1.System#Collections#Concurrent#IProducerConsumerCollection{T}#TryAdd(`0)">
      <summary>
        <see cref="T:System.Collections.Concurrent.IProducerConsumerCollection`1" />에 개체를 추가하려고 시도합니다.</summary>
      <returns>개체가 추가되었으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Concurrent.IProducerConsumerCollection`1" />에 추가할 개체입니다.값은 참조 형식의 경우 null 참조(Visual Basic에서는 Nothing)일 수 있습니다.</param>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentStack`1.System#Collections#Concurrent#IProducerConsumerCollection{T}#TryTake(`0@)">
      <summary>
        <see cref="T:System.Collections.Concurrent.IProducerConsumerCollection`1" />에서 개체를 제거하고 반환하려고 시도합니다.</summary>
      <returns>요소가 제거되고 반환되었으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">이 메서드가 반환될 경우 작업이 성공했으면 <paramref name="item" />에는 제거된 개체가 들어 있습니다.제거할 수 있는 개체가 없으면 이 값은 지정되지 않습니다.</param>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentStack`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>특정 <see cref="T:System.Array" /> 인덱스부터 시작하여 <see cref="T:System.Collections.ICollection" />의 요소를 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />에서 복사한 요소의 대상인 1차원 <see cref="T:System.Array" />입니다.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 0부터 시작하는 인덱스입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or- <paramref name="array" /> does not have zero-based indexing.-or- <paramref name="index" /> is equal to or greater than the length of the <paramref name="array" /> -or- The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or- The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentStack`1.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 SyncRoot와 동기화되는지를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 SyncRoot와 동기화되면 true이고, 그렇지 않으면 false입니다.<see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />의 경우 이 속성은 항상 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Concurrent.ConcurrentStack`1.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.이 속성이 지원되지 않는 경우</summary>
      <returns>null을 반환합니다(Visual Basic에서는 Nothing).</returns>
      <exception cref="T:System.NotSupportedException">The SyncRoot property is not supported</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentStack`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션에서 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.IEnumerator" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentStack`1.ToArray">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />에 저장되어 있는 항목을 새 배열에 복사합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />에서 복사된 요소의 스냅숏을 포함하는 새 배열입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentStack`1.TryPeek(`0@)">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />의 맨 위에 있는 개체를 제거하지 않고 반환하려고 시도합니다.</summary>
      <returns>개체가 반환되었으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="result">이 메서드가 반환될 경우 <paramref name="result" />에는 <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />의 맨 위에 있는 개체가 포함되어 있거나 지정되지 않은 값(작업이 실패한 경우)이 포함되어 있습니다.</param>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentStack`1.TryPop(`0@)">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />의 맨 위에 있는 개체를 꺼내고 반환하려고 시도합니다.</summary>
      <returns>요소가 <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />의 맨 위에서 제거되고 반환되었으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="result">이 메서드가 반환될 경우 작업이 성공했으면 <paramref name="result" />에는 제거된 개체가 들어 있습니다.제거할 수 있는 개체가 없으면 이 값은 지정되지 않습니다.</param>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentStack`1.TryPopRange(`0[])">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />의 맨 위에 있는 여러 개체를 원자 단위로 꺼내고 반환하려고 시도합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />의 맨 위에서 꺼내어 <paramref name="items" />에 삽입한 개체의 수입니다.</returns>
      <param name="items">
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />의 맨 위에서 꺼낸 개체가 추가될 <see cref="T:System.Array" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="items" /> is a null argument (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Collections.Concurrent.ConcurrentStack`1.TryPopRange(`0[],System.Int32,System.Int32)">
      <summary>
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />의 맨 위에 있는 여러 개체를 원자 단위로 꺼내고 반환하려고 시도합니다.</summary>
      <returns>스택의 맨 위에서 꺼내어 <paramref name="items" />에 삽입한 개체의 수입니다.</returns>
      <param name="items">
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />의 맨 위에서 꺼낸 개체가 추가될 <see cref="T:System.Array" />입니다.</param>
      <param name="startIndex">
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />의 맨 위 요소를 삽입하기 시작할 <paramref name="items" />의 0부터 시작하는 오프셋입니다.</param>
      <param name="count">
        <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />의 맨 위에서 꺼내어 <paramref name="items" />에 삽입할 개체의 수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="items" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> or <paramref name="count" /> is negative.Or <paramref name="startIndex" /> is greater than or equal to the length of <paramref name="items" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> + <paramref name="count" /> is greater than the length of <paramref name="items" />.</exception>
    </member>
    <member name="T:System.Collections.Concurrent.EnumerablePartitionerOptions">
      <summary>파티셔너의 버퍼링 동작을 제어하는 옵션을 지정합니다.</summary>
    </member>
    <member name="F:System.Collections.Concurrent.EnumerablePartitionerOptions.NoBuffering">
      <summary>한 번에 하나씩 열거 가능한 소스에서 항목을 가져오고 여러 스레드에서 보다 효율적으로 액세스할 수 있는 중간 저장소를 사용하지 않는 파티셔너를 만듭니다.이 옵션은 낮은 대기 시간을 지원하므로 항목이 소스로부터 사용할 수 있게 되자 마자 처리되고 항목 간 종속성도 부분적으로 지원을 제공합니다. 하지만 스레드 자체에서 처리해야 하는 항목을 기다리면서 스레드가 교체 상태에 빠지지 않습니다.</summary>
    </member>
    <member name="F:System.Collections.Concurrent.EnumerablePartitionerOptions.None">
      <summary>최적의 성능을 얻기 위해 버퍼링을 사용하는 기본 동작을 사용합니다.</summary>
    </member>
    <member name="T:System.Collections.Concurrent.IProducerConsumerCollection`1">
      <summary>공급자/소비자 용도의 스레드로부터 안전한 컬렉션을 조작할 메서드를 정의합니다.이 인터페이스는 <see cref="T:System.Collections.Concurrent.BlockingCollection`1" /> 같은 더 높은 수준의 추상이 기본 저장소 메커니즘으로 컬렉션을 사용할 수 있도록 생산자/소비자 컬렉션에 대한 통합 표현을 제공합니다.</summary>
      <typeparam name="T">컬렉션에 있는 요소의 형식을 지정합니다.</typeparam>
    </member>
    <member name="M:System.Collections.Concurrent.IProducerConsumerCollection`1.CopyTo(`0[],System.Int32)">
      <summary>지정된 인덱스부터 시작하여 <see cref="T:System.Collections.Concurrent.IProducerConsumerCollection`1" />의 요소를 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Concurrent.IProducerConsumerCollection`1" />에서 복사한 요소의 대상인 일차원 <see cref="T:System.Array" />입니다. 배열에서 0부터 시작하는 인덱스를 사용해야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />는 null 참조(Visual Basic에서는 Nothing)입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" />가 <paramref name="array" />의 길이와 같거나 큰 경우 -또는- 컬렉션의 요소 수가 대상 <paramref name="array" />의 <paramref name="index" />부터 끝까지의 사용 가능한 공간보다 큰 경우. </exception>
    </member>
    <member name="M:System.Collections.Concurrent.IProducerConsumerCollection`1.ToArray">
      <summary>
        <see cref="T:System.Collections.Concurrent.IProducerConsumerCollection`1" />에 포함된 요소를 새 배열에 복사합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.IProducerConsumerCollection`1" />에서 복사된 요소를 포함하는 새 배열입니다.</returns>
    </member>
    <member name="M:System.Collections.Concurrent.IProducerConsumerCollection`1.TryAdd(`0)">
      <summary>
        <see cref="T:System.Collections.Concurrent.IProducerConsumerCollection`1" />에 개체를 추가하려고 시도합니다.</summary>
      <returns>개체가 성공적으로 추가되었으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Concurrent.IProducerConsumerCollection`1" />에 추가할 개체입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" />은 이 컬렉션에 올바르지 않습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.IProducerConsumerCollection`1.TryTake(`0@)">
      <summary>
        <see cref="T:System.Collections.Concurrent.IProducerConsumerCollection`1" />에서 개체를 제거하고 반환하려고 시도합니다.</summary>
      <returns>개체가 성공적으로 제거되고 반환되었으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">이 메서드가 반환될 경우 개체가 성공적으로 제거되고 반환되었으면 <paramref name="item" />에는 제거된 개체가 들어 있습니다.제거할 수 있는 개체가 없으면 이 값은 지정되지 않습니다.</param>
    </member>
    <member name="T:System.Collections.Concurrent.OrderablePartitioner`1">
      <summary>정렬할 수 있는 데이터 소스를 여러 개의 파티션으로 분할하는 특정 방법을 나타냅니다.</summary>
      <typeparam name="TSource">컬렉션에 있는 요소의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Collections.Concurrent.OrderablePartitioner`1.#ctor(System.Boolean,System.Boolean,System.Boolean)">
      <summary>인덱스 키에 대해 지정된 제약 조건을 사용하여 <see cref="T:System.Collections.Concurrent.OrderablePartitioner`1" /> 클래스를 초기화하기 위해 파생 클래스의 생성자에서 호출됩니다.</summary>
      <param name="keysOrderedInEachPartition">각 파티션에 있는 요소가 키가 증가하는 순서로 반환되는지 여부를 나타냅니다.</param>
      <param name="keysOrderedAcrossPartitions">순서가 빠른 파티션의 요소가 순서가 늦은 파티션의 요소보다 항상 먼저 오는지 여부를 나타냅니다.true이면 파티션 0의 모든 요소가 파티션 1의 모든 요소보다 작은 순서 키를 갖고, 파티션 1의 모든 요소가 파티션 2의 모든 요소보다 작은 순서 키를 갖는 식으로 처리됩니다.</param>
      <param name="keysNormalized">키를 정규화할지 여부를 나타냅니다.true인 경우 모든 순서 키가 [0 .. numberOfElements-1] 범위의 서로 다른 정수입니다.false이면 순서 키가 서로 다르지만 절대 값이 아니라 상대적인 순서만 고려합니다.</param>
    </member>
    <member name="M:System.Collections.Concurrent.OrderablePartitioner`1.GetDynamicPartitions">
      <summary>기본 컬렉션을 여러 파티션으로 분할할 수 있는 개체를 만듭니다.</summary>
      <returns>내부 데이터 소스에 대한 파티션을 만들 수 있는 개체입니다.</returns>
      <exception cref="T:System.NotSupportedException">동적 분할은 기본 클래스에서 지원되지 않습니다.파생된 클래스에서 구현되어야 합니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.OrderablePartitioner`1.GetOrderableDynamicPartitions">
      <summary>기본 컬렉션을 여러 파티션으로 분할할 수 있는 개체를 만듭니다.</summary>
      <returns>내부 데이터 소스에 대한 파티션을 만들 수 있는 개체입니다.</returns>
      <exception cref="T:System.NotSupportedException">동적 분할은 이 파티셔너에서 지원되지 않습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.OrderablePartitioner`1.GetOrderablePartitions(System.Int32)">
      <summary>기본 컬렉션을 지정된 개수의 정렬할 수 있는 파티션으로 분할합니다.</summary>
      <returns>
        <paramref name="partitionCount" /> 열거자가 포함된 목록입니다.</returns>
      <param name="partitionCount">만들 파티션의 수입니다.</param>
    </member>
    <member name="M:System.Collections.Concurrent.OrderablePartitioner`1.GetPartitions(System.Int32)">
      <summary>기본 컬렉션을 지정된 개수의 정렬된 파티션으로 분할합니다.</summary>
      <returns>
        <paramref name="partitionCount" /> 열거자가 포함된 목록입니다.</returns>
      <param name="partitionCount">만들 파티션의 수입니다.</param>
    </member>
    <member name="P:System.Collections.Concurrent.OrderablePartitioner`1.KeysNormalized">
      <summary>순서 키를 정규화할지 여부를 가져옵니다.</summary>
      <returns>키가 정규화되면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Collections.Concurrent.OrderablePartitioner`1.KeysOrderedAcrossPartitions">
      <summary>순서가 빠른 파티션의 요소가 순서가 늦은 파티션의 요소보다 항상 먼저 오는지 여부를 가져옵니다.</summary>
      <returns>순서가 빠른 파티션의 요소가 순서가 늦은 파티션의 요소보다 항상 먼저 오면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Collections.Concurrent.OrderablePartitioner`1.KeysOrderedInEachPartition">
      <summary>각 파티션에 있는 요소가 키가 증가하는 순서로 생성되는지 여부를 가져옵니다.</summary>
      <returns>각 파티션에 있는 요소가 키가 증가하는 순서로 생성되면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="T:System.Collections.Concurrent.Partitioner">
      <summary>배열, 목록 및 열거 가능한 형식에 사용할 수 있는 공용 분할 전략을 제공합니다.</summary>
    </member>
    <member name="M:System.Collections.Concurrent.Partitioner.Create``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>
        <see cref="T:System.Collections.Generic.IEnumerable`1" /> 인스턴스에서 정렬할 수 있는 파티셔너를 만듭니다.</summary>
      <returns>입력 배열을 기초로 정렬할 수 있는 파티셔너입니다.</returns>
      <param name="source">분할할 열거 가능한 형식입니다.</param>
      <typeparam name="TSource">소스 열거 가능한 형식에 있는 요소의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Collections.Concurrent.Partitioner.Create``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Concurrent.EnumerablePartitionerOptions)">
      <summary>
        <see cref="T:System.Collections.Generic.IEnumerable`1" /> 인스턴스에서 정렬할 수 있는 파티셔너를 만듭니다.</summary>
      <returns>입력 배열을 기초로 정렬할 수 있는 파티셔너입니다.</returns>
      <param name="source">분할할 열거 가능한 형식입니다.</param>
      <param name="partitionerOptions">파티셔너의 버퍼링 동작을 제어하는 옵션입니다.</param>
      <typeparam name="TSource">소스 열거 가능한 형식에 있는 요소의 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="partitionerOptions" /> 인수는 <see cref="T:System.Collections.Concurrent.EnumerablePartitionerOptions" />에 대해 잘못된 값을 지정합니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.Partitioner.Create``1(System.Collections.Generic.IList{``0},System.Boolean)">
      <summary>
        <see cref="T:System.Collections.Generic.IList`1" /> 인스턴스에서 정렬할 수 있는 파티셔너를 만듭니다.</summary>
      <returns>입력 목록을 기초로 정렬할 수 있는 파티셔너입니다.</returns>
      <param name="list">분할할 목록입니다.</param>
      <param name="loadBalance">만들어진 파티셔너가 파티션 간에 부하를 정적으로 분산하는 것이 아니라 동적으로 분산시켜야 하는지 여부를 나타내는 부울 값입니다.</param>
      <typeparam name="TSource">소스 목록에 있는 요소의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Collections.Concurrent.Partitioner.Create(System.Int32,System.Int32)">
      <summary>사용자가 지정한 범위를 분할하는 파티셔너를 만듭니다.</summary>
      <returns>파티셔너입니다.</returns>
      <param name="fromInclusive">범위의 하한(포함)입니다.</param>
      <param name="toExclusive">범위의 상한(제외)입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="toExclusive" /> 인수는 <paramref name="fromInclusive" /> 인수보다 작거나 같습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.Partitioner.Create(System.Int32,System.Int32,System.Int32)">
      <summary>사용자가 지정한 범위를 분할하는 파티셔너를 만듭니다.</summary>
      <returns>파티셔너입니다.</returns>
      <param name="fromInclusive">범위의 하한(포함)입니다.</param>
      <param name="toExclusive">범위의 상한(제외)입니다.</param>
      <param name="rangeSize">각 하위 범위의 크기입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="toExclusive" /> 인수는 <paramref name="fromInclusive" /> 인수보다 작거나 같습니다.또는<paramref name="rangeSize" /> 인수가 0보다 작거나 같은 경우.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.Partitioner.Create(System.Int64,System.Int64)">
      <summary>사용자가 지정한 범위를 분할하는 파티셔너를 만듭니다.</summary>
      <returns>파티셔너입니다.</returns>
      <param name="fromInclusive">범위의 하한(포함)입니다.</param>
      <param name="toExclusive">범위의 상한(제외)입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="toExclusive" /> 인수는 <paramref name="fromInclusive" /> 인수보다 작거나 같습니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.Partitioner.Create(System.Int64,System.Int64,System.Int64)">
      <summary>사용자가 지정한 범위를 분할하는 파티셔너를 만듭니다.</summary>
      <returns>파티셔너입니다.</returns>
      <param name="fromInclusive">범위의 하한(포함)입니다.</param>
      <param name="toExclusive">범위의 상한(제외)입니다.</param>
      <param name="rangeSize">각 하위 범위의 크기입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="toExclusive" /> 인수는 <paramref name="fromInclusive" /> 인수보다 작거나 같습니다.또는<paramref name="rangeSize" /> 인수가 0보다 작거나 같은 경우.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.Partitioner.Create``1(``0[],System.Boolean)">
      <summary>
        <see cref="T:System.Array" /> 인스턴스에서 정렬할 수 있는 파티셔너를 만듭니다.</summary>
      <returns>입력 배열을 기초로 정렬할 수 있는 파티셔너입니다.</returns>
      <param name="array">분할할 배열입니다.</param>
      <param name="loadBalance">만들어진 파티셔너가 파티션 간에 부하를 정적으로 분산하는 것이 아니라 동적으로 분산시켜야 하는지 여부를 나타내는 부울 값입니다.</param>
      <typeparam name="TSource">소스 배열에 있는 요소의 형식입니다.</typeparam>
    </member>
    <member name="T:System.Collections.Concurrent.Partitioner`1">
      <summary>데이터 소스를 여러 개의 파티션으로 분할하는 특정 방법을 나타냅니다.</summary>
      <typeparam name="TSource">컬렉션에 있는 요소의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Collections.Concurrent.Partitioner`1.#ctor">
      <summary>새 파티셔너 인스턴스를 만듭니다.</summary>
    </member>
    <member name="M:System.Collections.Concurrent.Partitioner`1.GetDynamicPartitions">
      <summary>기본 컬렉션을 여러 파티션으로 분할할 수 있는 개체를 만듭니다.</summary>
      <returns>내부 데이터 소스에 대한 파티션을 만들 수 있는 개체입니다.</returns>
      <exception cref="T:System.NotSupportedException">동적 분할은 기본 클래스에서 지원되지 않습니다.파생 클래스에서 구현해야 합니다.</exception>
    </member>
    <member name="M:System.Collections.Concurrent.Partitioner`1.GetPartitions(System.Int32)">
      <summary>기본 컬렉션을 지정된 개수의 파티션으로 분할합니다.</summary>
      <returns>
        <paramref name="partitionCount" /> 열거자가 포함된 목록입니다.</returns>
      <param name="partitionCount">만들 파티션의 수입니다.</param>
    </member>
    <member name="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions">
      <summary>추가 파티션을 동적으로 만들 수 있는지 여부를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Concurrent.Partitioner`1" />에서 요청을 받은 경우 동적으로 파티션을 만들 수 있으면 true이고, <see cref="T:System.Collections.Concurrent.Partitioner`1" />에서 정적으로만 파티션을 할당할 수 있으면 false입니다.</returns>
    </member>
  </members>
</doc>