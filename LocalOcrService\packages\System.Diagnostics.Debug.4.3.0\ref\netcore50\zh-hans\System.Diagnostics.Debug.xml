﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Diagnostics.Debug</name>
  </assembly>
  <members>
    <member name="T:System.Diagnostics.Debug">
      <summary>提供一组帮助调试代码的方法和属性。此类不能被继承。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Debug.Assert(System.Boolean)">
      <summary>检查条件；如果条件为 false，则显示一个消息框，其中会显示调用堆栈。</summary>
      <param name="condition">要计算的条件表达式。如果条件为 true，则不发送失败消息，并且不显示消息框。</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.Assert(System.Boolean,System.String)">
      <summary>检查条件；如果条件为 false，则输出指定消息，并显示一个消息框，其中会显示调用堆栈。</summary>
      <param name="condition">要计算的条件表达式。如果条件为 true，则不发送指定消息，并且不显示消息框。</param>
      <param name="message">要发送给 <see cref="P:System.Diagnostics.Trace.Listeners" /> 集合的消息。</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.Assert(System.Boolean,System.String,System.String)">
      <summary>检查条件；如果条件为 false，则输出两条指定消息，并显示一个消息框，其中会显示调用堆栈。</summary>
      <param name="condition">要计算的条件表达式。如果条件为 true，则不发送指定消息，并且不显示消息框。</param>
      <param name="message">要发送给 <see cref="P:System.Diagnostics.Trace.Listeners" /> 集合的消息。</param>
      <param name="detailMessage">要发送给 <see cref="P:System.Diagnostics.Trace.Listeners" /> 集合的详细消息。</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.Assert(System.Boolean,System.String,System.String,System.Object[])">
      <summary>检查条件；如果条件为 false，则输出两条指定消息（简单消息和格式化消息），并显示一个消息框，其中会显示调用堆栈。</summary>
      <param name="condition">要计算的条件表达式。如果条件为 true，则不发送指定消息，并且不显示消息框。</param>
      <param name="message">要发送给 <see cref="P:System.Diagnostics.Trace.Listeners" /> 集合的消息。</param>
      <param name="detailMessageFormat">要发送到 <see cref="P:System.Diagnostics.Trace.Listeners" /> 集合的复合格式字符串（见“备注”）。该消息包含与零个或多个格式项混合的文本，它与 <paramref name="args" /> 数组中的对象相对应。</param>
      <param name="args">一个对象数组，其中包含零个或多个要设置格式的对象。</param>
    </member>
    <member name="M:System.Diagnostics.Debug.Fail(System.String)">
      <summary>发出指定的错误消息。</summary>
      <param name="message">要发出的消息。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.Fail(System.String,System.String)">
      <summary>发出错误消息及详细的错误消息。</summary>
      <param name="message">要发出的消息。</param>
      <param name="detailMessage">要发出的详细消息。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.Write(System.Object)">
      <summary>将对象的 <see cref="M:System.Object.ToString" /> 方法的值写入 <see cref="P:System.Diagnostics.Debug.Listeners" /> 集合中的跟踪侦听器中。</summary>
      <param name="value">一个对象，其名称被发送到 <see cref="P:System.Diagnostics.Debug.Listeners" />。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.Write(System.Object,System.String)">
      <summary>将类别名称和对象的 <see cref="M:System.Object.ToString" /> 方法值写入 <see cref="P:System.Diagnostics.Debug.Listeners" /> 集合中的跟踪侦听器。</summary>
      <param name="value">一个对象，其名称被发送到 <see cref="P:System.Diagnostics.Debug.Listeners" />。</param>
      <param name="category">用于组织输出的类别名称。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.Write(System.String)">
      <summary>将消息写入 <see cref="P:System.Diagnostics.Debug.Listeners" /> 集合中的跟踪侦听器。</summary>
      <param name="message">要写入的消息。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.Write(System.String,System.String)">
      <summary>将类别名称和消息写入 <see cref="P:System.Diagnostics.Debug.Listeners" /> 集合中的跟踪侦听器。</summary>
      <param name="message">要写入的消息。</param>
      <param name="category">用于组织输出的类别名称。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteIf(System.Boolean,System.Object)">
      <summary>如果条件为 true，则将对象的 <see cref="M:System.Object.ToString" /> 方法值写入 <see cref="P:System.Diagnostics.Debug.Listeners" /> 集合中的跟踪侦听器。</summary>
      <param name="condition">要计算的条件表达式。如果条件为 true，则将值写入集合中的跟踪侦听器。</param>
      <param name="value">一个对象，其名称被发送到 <see cref="P:System.Diagnostics.Debug.Listeners" />。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteIf(System.Boolean,System.Object,System.String)">
      <summary>如果条件为 true，则将类别名称和对象的 <see cref="M:System.Object.ToString" /> 方法值写入 <see cref="P:System.Diagnostics.Debug.Listeners" /> 集合中的跟踪侦听器。</summary>
      <param name="condition">要计算的条件表达式。如果条件为 true，则将类别名称和值写入集合中的跟踪侦听器。</param>
      <param name="value">一个对象，其名称被发送到 <see cref="P:System.Diagnostics.Debug.Listeners" />。</param>
      <param name="category">用于组织输出的类别名称。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteIf(System.Boolean,System.String)">
      <summary>如果条件为 true，则将消息写入 <see cref="P:System.Diagnostics.Debug.Listeners" /> 集合中的跟踪侦听器。</summary>
      <param name="condition">要计算的条件表达式。如果条件为 true，则将消息写入集合中的跟踪侦听器。</param>
      <param name="message">要写入的消息。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteIf(System.Boolean,System.String,System.String)">
      <summary>如果条件为 true，则将类别名称和消息写入 <see cref="P:System.Diagnostics.Debug.Listeners" /> 集合中的跟踪侦听器。</summary>
      <param name="condition">要计算的条件表达式。如果条件为 true，则将类别名称和消息写入集合中的跟踪侦听器。</param>
      <param name="message">要写入的消息。</param>
      <param name="category">用于组织输出的类别名称。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteLine(System.Object)">
      <summary>将对象的 <see cref="M:System.Object.ToString" /> 方法的值写入 <see cref="P:System.Diagnostics.Debug.Listeners" /> 集合中的跟踪侦听器中。</summary>
      <param name="value">一个对象，其名称被发送到 <see cref="P:System.Diagnostics.Debug.Listeners" />。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteLine(System.Object,System.String)">
      <summary>将类别名称和对象的 <see cref="M:System.Object.ToString" /> 方法值写入 <see cref="P:System.Diagnostics.Debug.Listeners" /> 集合中的跟踪侦听器。</summary>
      <param name="value">一个对象，其名称被发送到 <see cref="P:System.Diagnostics.Debug.Listeners" />。</param>
      <param name="category">用于组织输出的类别名称。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteLine(System.String)">
      <summary>将后跟行结束符的消息写入 <see cref="P:System.Diagnostics.Debug.Listeners" /> 集合中的跟踪侦听器。</summary>
      <param name="message">要写入的消息。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteLine(System.String,System.Object[])">
      <summary>将后跟行结束符的格式化消息写入 <see cref="P:System.Diagnostics.Debug.Listeners" /> 集合中的跟踪侦听器。</summary>
      <param name="format">一个包含与零个或多个格式项混合的文本的复合格式字符串（请参见“备注”），它与 <paramref name="args" /> 数组中的对象相对应。</param>
      <param name="args">一个对象数组，其中包含零个或多个要设置格式的对象。</param>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteLine(System.String,System.String)">
      <summary>将类别名称和消息写入 <see cref="P:System.Diagnostics.Debug.Listeners" /> 集合中的跟踪侦听器。</summary>
      <param name="message">要写入的消息。</param>
      <param name="category">用于组织输出的类别名称。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteLineIf(System.Boolean,System.Object)">
      <summary>如果条件为 true，则将对象的 <see cref="M:System.Object.ToString" /> 方法值写入 <see cref="P:System.Diagnostics.Debug.Listeners" /> 集合中的跟踪侦听器。</summary>
      <param name="condition">要计算的条件表达式。如果条件为 true，则将值写入集合中的跟踪侦听器。</param>
      <param name="value">一个对象，其名称被发送到 <see cref="P:System.Diagnostics.Debug.Listeners" />。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteLineIf(System.Boolean,System.Object,System.String)">
      <summary>如果条件为 true，则将类别名称和对象的 <see cref="M:System.Object.ToString" /> 方法值写入 <see cref="P:System.Diagnostics.Debug.Listeners" /> 集合中的跟踪侦听器。</summary>
      <param name="condition">要计算的条件表达式。如果条件为 true，则将类别名称和值写入集合中的跟踪侦听器。</param>
      <param name="value">一个对象，其名称被发送到 <see cref="P:System.Diagnostics.Debug.Listeners" />。</param>
      <param name="category">用于组织输出的类别名称。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteLineIf(System.Boolean,System.String)">
      <summary>如果条件为 true，则将消息写入 <see cref="P:System.Diagnostics.Debug.Listeners" /> 集合中的跟踪侦听器。</summary>
      <param name="condition">要计算的条件表达式。如果条件为 true，则将消息写入集合中的跟踪侦听器。</param>
      <param name="message">要写入的消息。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteLineIf(System.Boolean,System.String,System.String)">
      <summary>如果条件为 true，则将类别名称和消息写入 <see cref="P:System.Diagnostics.Debug.Listeners" /> 集合中的跟踪侦听器。</summary>
      <param name="condition">若要使消息被写入，则为 true；否则为 false。</param>
      <param name="message">要写入的消息。</param>
      <param name="category">用于组织输出的类别名称。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.Diagnostics.Debugger">
      <summary>启用与调试器的通讯。此类不能被继承。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Debugger.Break">
      <summary>发出信号表示连接调试器的断点。</summary>
      <exception cref="T:System.Security.SecurityException">
        <see cref="T:System.Security.Permissions.UIPermission" /> 未设置为在调试器中设置断点。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.UIPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Diagnostics.Debugger.IsAttached">
      <summary>获取一个值，它指示调试器是否已附加到进程。</summary>
      <returns>如果调试器已连接，则为 true；否则为 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Debugger.Launch">
      <summary>启动调试器并将其连接到进程。</summary>
      <returns>如果启动成功或者调试器已连接，则为 true；否则为 false。</returns>
      <exception cref="T:System.Security.SecurityException">
        <see cref="T:System.Security.Permissions.UIPermission" /> 未设置为启动调试器。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.UIPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.Diagnostics.DebuggerBrowsableAttribute">
      <summary>确定是否在调试器变量窗口中显示成员以及如何显示成员。此类不能被继承。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.DebuggerBrowsableAttribute.#ctor(System.Diagnostics.DebuggerBrowsableState)">
      <summary>初始化 <see cref="T:System.Diagnostics.DebuggerBrowsableAttribute" /> 类的新实例。</summary>
      <param name="state">
        <see cref="T:System.Diagnostics.DebuggerBrowsableState" /> 值之一，指定成员的显示方式。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="state" /> 不是 <see cref="T:System.Diagnostics.DebuggerBrowsableState" /> 值之一。</exception>
    </member>
    <member name="P:System.Diagnostics.DebuggerBrowsableAttribute.State">
      <summary>获取属性的显示状态。</summary>
      <returns>
        <see cref="T:System.Diagnostics.DebuggerBrowsableState" /> 值之一。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Diagnostics.DebuggerBrowsableState">
      <summary>指定调试器的显示方式。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Diagnostics.DebuggerBrowsableState.Collapsed">
      <summary>以折叠方式显示元素。</summary>
    </member>
    <member name="F:System.Diagnostics.DebuggerBrowsableState.Never">
      <summary>从不显示元素。</summary>
    </member>
    <member name="F:System.Diagnostics.DebuggerBrowsableState.RootHidden">
      <summary>不显示根元素；如果元素是项的集合或数组，则显示子元素。</summary>
    </member>
    <member name="T:System.Diagnostics.DebuggerDisplayAttribute">
      <summary>确定类或字段在调试器的变量窗口中的显示方式。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.DebuggerDisplayAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Diagnostics.DebuggerDisplayAttribute" /> 类的新实例。</summary>
      <param name="value">要在值列中为该类型的实例显示的字符串；空字符串 ("") 将使值列隐藏。</param>
    </member>
    <member name="P:System.Diagnostics.DebuggerDisplayAttribute.Name">
      <summary>获取或设置要在调试器的变量窗口中显示的名称。</summary>
      <returns>要在调试器的变量窗口中显示的名称。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Diagnostics.DebuggerDisplayAttribute.Target">
      <summary>获取或设置该属性的目标类型。</summary>
      <returns>该特性的目标类型。</returns>
      <exception cref="T:System.ArgumentNullException">将 <see cref="P:System.Diagnostics.DebuggerDisplayAttribute.Target" /> 设置为 null。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Diagnostics.DebuggerDisplayAttribute.TargetTypeName">
      <summary>获取或设置该属性的目标类型的名称。</summary>
      <returns>该属性的目标类型的名称。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Diagnostics.DebuggerDisplayAttribute.Type">
      <summary>获取或设置要在调试器的变量窗口的类型列中显示的字符串。</summary>
      <returns>要在调试器的变量窗口的类型列中显示的字符串。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Diagnostics.DebuggerDisplayAttribute.Value">
      <summary>获取要在调试器变量窗口的值列中显示的字符串。</summary>
      <returns>要在调试器变量窗口的值列中显示的字符串。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Diagnostics.DebuggerHiddenAttribute">
      <summary>指定 <see cref="T:System.Diagnostics.DebuggerHiddenAttribute" />。此类不能被继承。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.DebuggerHiddenAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Diagnostics.DebuggerHiddenAttribute" /> 类的新实例。</summary>
    </member>
    <member name="T:System.Diagnostics.DebuggerNonUserCodeAttribute">
      <summary>标识不属于应用程序用户代码的类型或成员。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.DebuggerNonUserCodeAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Diagnostics.DebuggerNonUserCodeAttribute" /> 类的新实例。</summary>
    </member>
    <member name="T:System.Diagnostics.DebuggerStepThroughAttribute">
      <summary>指示调试器逐句通过代码，而不是单步执行代码。此类不能被继承。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.DebuggerStepThroughAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Diagnostics.DebuggerStepThroughAttribute" /> 类的新实例。 </summary>
    </member>
    <member name="T:System.Diagnostics.DebuggerTypeProxyAttribute">
      <summary>指定类型的显示代理。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.DebuggerTypeProxyAttribute.#ctor(System.String)">
      <summary>使用代理类型名称初始化 <see cref="T:System.Diagnostics.DebuggerTypeProxyAttribute" /> 类的新实例。</summary>
      <param name="typeName">代理类型的类型名称。</param>
    </member>
    <member name="M:System.Diagnostics.DebuggerTypeProxyAttribute.#ctor(System.Type)">
      <summary>将使用此类型代理的 <see cref="T:System.Diagnostics.DebuggerTypeProxyAttribute" /> 类的新实例初始化。</summary>
      <param name="type">代理类型。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> 为 null。</exception>
    </member>
    <member name="P:System.Diagnostics.DebuggerTypeProxyAttribute.ProxyTypeName">
      <summary>获取代理类型的类型名称。</summary>
      <returns>代理类型的类型名称。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Diagnostics.DebuggerTypeProxyAttribute.Target">
      <summary>获取或设置属性的目标类型。</summary>
      <returns>特性的目标类型。</returns>
      <exception cref="T:System.ArgumentNullException">将 <see cref="P:System.Diagnostics.DebuggerTypeProxyAttribute.Target" /> 设置为 null。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Diagnostics.DebuggerTypeProxyAttribute.TargetTypeName">
      <summary>获取或设置目标类型的名称。</summary>
      <returns>目标类型的名称。</returns>
      <filterpriority>2</filterpriority>
    </member>
  </members>
</doc>