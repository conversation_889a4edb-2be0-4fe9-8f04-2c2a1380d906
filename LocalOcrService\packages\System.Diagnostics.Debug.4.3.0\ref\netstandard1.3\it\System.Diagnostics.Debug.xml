﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Diagnostics.Debug</name>
  </assembly>
  <members>
    <member name="T:System.Diagnostics.Debug">
      <summary>Fornisce un insieme di metodi e di proprietà che consentono di eseguire il debug del codice.La classe non può essere ereditata.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Debug.Assert(System.Boolean)">
      <summary>Controlla una condizione. Se la condizione è false, viene visualizzata una finestra di messaggio contenente lo stack di chiamate.</summary>
      <param name="condition">Espressione condizionale da valutare.Se la condizione è true, non viene inviato un messaggio di errore e la finestra di messaggio non viene visualizzata.</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.Assert(System.Boolean,System.String)">
      <summary>Controlla una condizione. Se la condizione è false, viene generato un messaggio specificato e viene visualizzata una finestra di messaggio contenente lo stack di chiamate.</summary>
      <param name="condition">Espressione condizionale da valutare.Se la condizione è true, il messaggio specificato non viene inviato e la finestra di messaggio non viene visualizzata.</param>
      <param name="message">Messaggio da inviare all'insieme <see cref="P:System.Diagnostics.Trace.Listeners" />. </param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.Assert(System.Boolean,System.String,System.String)">
      <summary>Controlla una condizione. Se la condizione è false, vengono generati due messaggi specificati e viene visualizzata una finestra di messaggio contenente lo stack di chiamate.</summary>
      <param name="condition">Espressione condizionale da valutare.Se la condizione è true, i messaggi specificati non vengono inviati e la finestra di messaggio non viene visualizzata.</param>
      <param name="message">Messaggio da inviare all'insieme <see cref="P:System.Diagnostics.Trace.Listeners" />. </param>
      <param name="detailMessage">Messaggio dettagliato da inviare all'insieme <see cref="P:System.Diagnostics.Trace.Listeners" />. </param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.Assert(System.Boolean,System.String,System.String,System.Object[])">
      <summary>Controlla una condizione. Se la condizione è false, vengono generati due messaggi (semplice e formattato) e viene visualizzata una finestra di messaggio contenente lo stack di chiamate.</summary>
      <param name="condition">Espressione condizionale da valutare.Se la condizione è true, i messaggi specificati non vengono inviati e la finestra di messaggio non viene visualizzata.</param>
      <param name="message">Messaggio da inviare all'insieme <see cref="P:System.Diagnostics.Trace.Listeners" />. </param>
      <param name="detailMessageFormat">La stringa di formato composto (vedere la sezione Osservazioni) da inviare all'insieme <see cref="P:System.Diagnostics.Trace.Listeners" />.Questo messaggio contiene testo combinato con zero o più elementi di formato, che corrispondono a oggetti nella matrice <paramref name="args" />.</param>
      <param name="args">Matrice di oggetti che contiene zero o più oggetti da formattare.</param>
    </member>
    <member name="M:System.Diagnostics.Debug.Fail(System.String)">
      <summary>Genera il messaggio di errore specificato.</summary>
      <param name="message">Messaggio da generare. </param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.Fail(System.String,System.String)">
      <summary>Genera un messaggio di errore e un messaggio di errore dettagliato.</summary>
      <param name="message">Messaggio da generare. </param>
      <param name="detailMessage">Messaggio dettagliato da generare. </param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.Write(System.Object)">
      <summary>Scrive il valore del metodo <see cref="M:System.Object.ToString" /> dell'oggetto nei listener di traccia dell'insieme <see cref="P:System.Diagnostics.Debug.Listeners" />.</summary>
      <param name="value">Oggetto il cui nome viene inviato ai <see cref="P:System.Diagnostics.Debug.Listeners" />. </param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.Write(System.Object,System.String)">
      <summary>Scrive il nome di una categoria e il valore del metodo <see cref="M:System.Object.ToString" /> dell'oggetto nei listener di traccia dell'insieme <see cref="P:System.Diagnostics.Debug.Listeners" />.</summary>
      <param name="value">Oggetto il cui nome viene inviato ai <see cref="P:System.Diagnostics.Debug.Listeners" />. </param>
      <param name="category">Nome di categoria utilizzato per organizzare l'output. </param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.Write(System.String)">
      <summary>Scrive un messaggio nei listener di traccia dell'insieme <see cref="P:System.Diagnostics.Debug.Listeners" />.</summary>
      <param name="message">Messaggio da scrivere. </param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.Write(System.String,System.String)">
      <summary>Scrive un nome di categoria e un messaggio nei listener di traccia dell'insieme <see cref="P:System.Diagnostics.Debug.Listeners" />.</summary>
      <param name="message">Messaggio da scrivere. </param>
      <param name="category">Nome di categoria utilizzato per organizzare l'output. </param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteIf(System.Boolean,System.Object)">
      <summary>Scrive il valore del metodo <see cref="M:System.Object.ToString" /> dell'oggetto nei listener di traccia nell'insieme <see cref="P:System.Diagnostics.Debug.Listeners" /> se una condizione è true.</summary>
      <param name="condition">Espressione condizionale da valutare.Se la condizione è true, il valore viene scritto nei listener di traccia nell'insieme.</param>
      <param name="value">Oggetto il cui nome viene inviato ai <see cref="P:System.Diagnostics.Debug.Listeners" />. </param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteIf(System.Boolean,System.Object,System.String)">
      <summary>Scrive il nome di una categoria e il valore del metodo <see cref="M:System.Object.ToString" /> dell'oggetto nei listener di traccia nell'insieme <see cref="P:System.Diagnostics.Debug.Listeners" /> se una condizione è true.</summary>
      <param name="condition">Espressione condizionale da valutare.Se la condizione è true, il nome della categoria e il valore vengono scritti nei listener di traccia nell'insieme.</param>
      <param name="value">Oggetto il cui nome viene inviato ai <see cref="P:System.Diagnostics.Debug.Listeners" />. </param>
      <param name="category">Nome di categoria utilizzato per organizzare l'output. </param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteIf(System.Boolean,System.String)">
      <summary>Scrive un messaggio nei listener di traccia nell'insieme <see cref="P:System.Diagnostics.Debug.Listeners" /> se una condizione è true.</summary>
      <param name="condition">Espressione condizionale da valutare.Se la condizione è true, il messaggio viene scritto nei listener di traccia nell'insieme.</param>
      <param name="message">Messaggio da scrivere. </param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteIf(System.Boolean,System.String,System.String)">
      <summary>Scrive un nome di categoria e un messaggio nei listener di traccia dell'insieme <see cref="P:System.Diagnostics.Debug.Listeners" /> se una condizione è true.</summary>
      <param name="condition">Espressione condizionale da valutare.Se la condizione è true, il nome della categoria e il messaggio vengono scritti nei listener di traccia nell'insieme.</param>
      <param name="message">Messaggio da scrivere. </param>
      <param name="category">Nome di categoria utilizzato per organizzare l'output. </param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteLine(System.Object)">
      <summary>Scrive il valore del metodo <see cref="M:System.Object.ToString" /> dell'oggetto nei listener di traccia dell'insieme <see cref="P:System.Diagnostics.Debug.Listeners" />.</summary>
      <param name="value">Oggetto il cui nome viene inviato ai <see cref="P:System.Diagnostics.Debug.Listeners" />. </param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteLine(System.Object,System.String)">
      <summary>Scrive il nome di una categoria e il valore del metodo <see cref="M:System.Object.ToString" /> dell'oggetto nei listener di traccia dell'insieme <see cref="P:System.Diagnostics.Debug.Listeners" />.</summary>
      <param name="value">Oggetto il cui nome viene inviato ai <see cref="P:System.Diagnostics.Debug.Listeners" />. </param>
      <param name="category">Nome di categoria utilizzato per organizzare l'output. </param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteLine(System.String)">
      <summary>Scrive un messaggio seguito da un terminatore di riga nei listener di traccia nell'insieme <see cref="P:System.Diagnostics.Debug.Listeners" />.</summary>
      <param name="message">Messaggio da scrivere. </param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteLine(System.String,System.Object[])">
      <summary>Scrive un messaggio formattato seguito da un terminatore di riga nei listener di traccia nell'insieme <see cref="P:System.Diagnostics.Debug.Listeners" />.</summary>
      <param name="format">Stringa di formato composta (vedere la sezione Note) che contiene testo combinato con zero o più elementi di formato, che corrispondono agli oggetti nella matrice <paramref name="args" />.</param>
      <param name="args">Matrice di oggetti che contiene zero o più oggetti da formattare. </param>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteLine(System.String,System.String)">
      <summary>Scrive un nome di categoria e un messaggio nei listener di traccia dell'insieme <see cref="P:System.Diagnostics.Debug.Listeners" />.</summary>
      <param name="message">Messaggio da scrivere. </param>
      <param name="category">Nome di categoria utilizzato per organizzare l'output. </param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteLineIf(System.Boolean,System.Object)">
      <summary>Scrive il valore del metodo <see cref="M:System.Object.ToString" /> dell'oggetto nei listener di traccia nell'insieme <see cref="P:System.Diagnostics.Debug.Listeners" /> se una condizione è true.</summary>
      <param name="condition">Espressione condizionale da valutare.Se la condizione è true, il valore viene scritto nei listener di traccia nell'insieme.</param>
      <param name="value">Oggetto il cui nome viene inviato ai <see cref="P:System.Diagnostics.Debug.Listeners" />. </param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteLineIf(System.Boolean,System.Object,System.String)">
      <summary>Scrive il nome di una categoria e il valore del metodo <see cref="M:System.Object.ToString" /> dell'oggetto nei listener di traccia nell'insieme <see cref="P:System.Diagnostics.Debug.Listeners" /> se una condizione è true.</summary>
      <param name="condition">Espressione condizionale da valutare.Se la condizione è true, il nome della categoria e il valore vengono scritti nei listener di traccia nell'insieme.</param>
      <param name="value">Oggetto il cui nome viene inviato ai <see cref="P:System.Diagnostics.Debug.Listeners" />. </param>
      <param name="category">Nome di categoria utilizzato per organizzare l'output. </param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteLineIf(System.Boolean,System.String)">
      <summary>Scrive un messaggio nei listener di traccia nell'insieme <see cref="P:System.Diagnostics.Debug.Listeners" /> se una condizione è true.</summary>
      <param name="condition">Espressione condizionale da valutare.Se la condizione è true, il messaggio viene scritto nei listener di traccia nell'insieme.</param>
      <param name="message">Messaggio da scrivere. </param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Diagnostics.Debug.WriteLineIf(System.Boolean,System.String,System.String)">
      <summary>Scrive un nome di categoria e un messaggio nei listener di traccia dell'insieme <see cref="P:System.Diagnostics.Debug.Listeners" /> se una condizione è true.</summary>
      <param name="condition">true per produrre la scrittura di un messaggio; in caso contrario, false. </param>
      <param name="message">Messaggio da scrivere. </param>
      <param name="category">Nome di categoria utilizzato per organizzare l'output. </param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.Diagnostics.Debugger">
      <summary>Abilita la comunicazione con un debugger.La classe non può essere ereditata.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Debugger.Break">
      <summary>Segnala un punto di interruzione a un debugger collegato.</summary>
      <exception cref="T:System.Security.SecurityException">
        <see cref="T:System.Security.Permissions.UIPermission" /> non è impostato per interrompere il debugger. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.UIPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Diagnostics.Debugger.IsAttached">
      <summary>Ottiene un valore che indica se un debugger è collegato al processo.</summary>
      <returns>true se un debugger è collegato; in caso contrario, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Debugger.Launch">
      <summary>Avvia e collega un debugger al processo.</summary>
      <returns>true se l'avvio viene eseguito con esito positivo oppure se il debugger è già collegato; in caso contrario, false.</returns>
      <exception cref="T:System.Security.SecurityException">
        <see cref="T:System.Security.Permissions.UIPermission" /> non è impostato per l'avvio del debugger. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.UIPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.Diagnostics.DebuggerBrowsableAttribute">
      <summary>Determina se e come viene visualizzato un membro nelle finestre delle variabili del debugger.La classe non può essere ereditata.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.DebuggerBrowsableAttribute.#ctor(System.Diagnostics.DebuggerBrowsableState)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Diagnostics.DebuggerBrowsableAttribute" />. </summary>
      <param name="state">Uno dei valori di <see cref="T:System.Diagnostics.DebuggerBrowsableState" /> che specifica la modalità di visualizzazione del membro.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="state" /> non è uno dei valori di <see cref="T:System.Diagnostics.DebuggerBrowsableState" />.</exception>
    </member>
    <member name="P:System.Diagnostics.DebuggerBrowsableAttribute.State">
      <summary>Ottiene lo stato di visualizzazione per l'attributo.</summary>
      <returns>Uno dei valori di <see cref="T:System.Diagnostics.DebuggerBrowsableState" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Diagnostics.DebuggerBrowsableState">
      <summary>Fornisce istruzioni di visualizzazione per il debugger.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Diagnostics.DebuggerBrowsableState.Collapsed">
      <summary>Consente di visualizzare gli elementi in forma compressa.</summary>
    </member>
    <member name="F:System.Diagnostics.DebuggerBrowsableState.Never">
      <summary>Consente di non visualizzare mai l'elemento.</summary>
    </member>
    <member name="F:System.Diagnostics.DebuggerBrowsableState.RootHidden">
      <summary>Consente di non visualizzare l'elemento di primo livello e di visualizzare gli elementi figlio se l'elemento è un insieme o una matrice di elementi.</summary>
    </member>
    <member name="T:System.Diagnostics.DebuggerDisplayAttribute">
      <summary>Determina la modalità di visualizzazione di una classe o di un campo nelle finestre delle variabili del debugger.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.DebuggerDisplayAttribute.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Diagnostics.DebuggerDisplayAttribute" />. </summary>
      <param name="value">Stringa da visualizzare nella colonna del valore per le istanze del tipo. Se si specifica una stringa vuota (""), la colonna del valore verrà nascosta.</param>
    </member>
    <member name="P:System.Diagnostics.DebuggerDisplayAttribute.Name">
      <summary>Ottiene o imposta il nome da visualizzare nelle finestre delle variabili del debugger.</summary>
      <returns>Nome da visualizzare nelle finestre delle variabili del debugger.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Diagnostics.DebuggerDisplayAttribute.Target">
      <summary>Ottiene o imposta il tipo della destinazione dell'attributo.</summary>
      <returns>Tipo di destinazione dell'attributo.</returns>
      <exception cref="T:System.ArgumentNullException">
        <see cref="P:System.Diagnostics.DebuggerDisplayAttribute.Target" /> è impostato su null.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Diagnostics.DebuggerDisplayAttribute.TargetTypeName">
      <summary>Ottiene o imposta il nome del tipo della destinazione dell'attributo.</summary>
      <returns>Nome del tipo della destinazione dell'attributo.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Diagnostics.DebuggerDisplayAttribute.Type">
      <summary>Ottiene o imposta la stringa da visualizzare nella colonna del tipo delle finestre delle variabili del debugger.</summary>
      <returns>Stringa da visualizzare nella colonna del tipo delle finestre delle variabili del debugger.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Diagnostics.DebuggerDisplayAttribute.Value">
      <summary>Ottiene la stringa da visualizzare nella colonna del valore delle finestre delle variabili del debugger.</summary>
      <returns>Stringa da visualizzare nella colonna del valore delle finestre delle variabili del debugger.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Diagnostics.DebuggerHiddenAttribute">
      <summary>Specifica <see cref="T:System.Diagnostics.DebuggerHiddenAttribute" />.La classe non può essere ereditata.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.DebuggerHiddenAttribute.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Diagnostics.DebuggerHiddenAttribute" />. </summary>
    </member>
    <member name="T:System.Diagnostics.DebuggerNonUserCodeAttribute">
      <summary>Identifica un tipo o membro che non fa parte del codice utente di un'applicazione.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.DebuggerNonUserCodeAttribute.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Diagnostics.DebuggerNonUserCodeAttribute" />. </summary>
    </member>
    <member name="T:System.Diagnostics.DebuggerStepThroughAttribute">
      <summary>Indica al debugger di eseguire il codice un'istruzione alla volta anziché eseguire un'istruzione nel codice.La classe non può essere ereditata.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.DebuggerStepThroughAttribute.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Diagnostics.DebuggerStepThroughAttribute" />. </summary>
    </member>
    <member name="T:System.Diagnostics.DebuggerTypeProxyAttribute">
      <summary>Specifica il proxy di visualizzazione per un tipo.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.DebuggerTypeProxyAttribute.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Diagnostics.DebuggerTypeProxyAttribute" /> utilizzando il nome del tipo del proxy. </summary>
      <param name="typeName">Nome del tipo del proxy.</param>
    </member>
    <member name="M:System.Diagnostics.DebuggerTypeProxyAttribute.#ctor(System.Type)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Diagnostics.DebuggerTypeProxyAttribute" /> utilizzando il tipo del proxy. </summary>
      <param name="type">Tipo del proxy.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> è null.</exception>
    </member>
    <member name="P:System.Diagnostics.DebuggerTypeProxyAttribute.ProxyTypeName">
      <summary>Ottiene il nome del tipo del proxy. </summary>
      <returns>Nome del tipo del proxy.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Diagnostics.DebuggerTypeProxyAttribute.Target">
      <summary>Ottiene o imposta il tipo di destinazione per l'attributo.</summary>
      <returns>Tipo di destinazione per l'attributo.</returns>
      <exception cref="T:System.ArgumentNullException">
        <see cref="P:System.Diagnostics.DebuggerTypeProxyAttribute.Target" /> è impostato su null.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Diagnostics.DebuggerTypeProxyAttribute.TargetTypeName">
      <summary>Ottiene o imposta il nome del tipo di destinazione.</summary>
      <returns>Nome del tipo di destinazione.</returns>
      <filterpriority>2</filterpriority>
    </member>
  </members>
</doc>