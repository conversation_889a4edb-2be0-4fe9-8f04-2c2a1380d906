﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Diagnostics.Tools</name>
  </assembly>
  <members>
    <member name="T:System.CodeDom.Compiler.GeneratedCodeAttribute">
      <summary>도구에서 생성한 코드를 식별합니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.GeneratedCodeAttribute.#ctor(System.String,System.String)">
      <summary>코드를 생성한 도구의 이름과 버전을 지정하는 <see cref="T:System.CodeDom.Compiler.GeneratedCodeAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="tool">코드를 생성한 도구의 이름입니다.</param>
      <param name="version">코드를 생성한 도구의 버전입니다.</param>
    </member>
    <member name="P:System.CodeDom.Compiler.GeneratedCodeAttribute.Tool">
      <summary>코드를 생성한 도구의 이름을 가져옵니다.</summary>
      <returns>코드를 생성한 도구의 이름입니다.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.GeneratedCodeAttribute.Version">
      <summary>코드를 생성한 도구의 버전을 가져옵니다.</summary>
      <returns>코드를 생성한 도구의 버전입니다.</returns>
    </member>
    <member name="T:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute">
      <summary>특정 정적 분석 도구 규칙 위반에 대한 보고를 표시하지 않으며, 단일 코드 아티팩트에 여러 숨기기를 사용할 수 있습니다.</summary>
    </member>
    <member name="M:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute.#ctor(System.String,System.String)">
      <summary>
        <see cref="T:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute" /> 클래스의 새 인스턴스를 초기화하고, 정적 분석 도구의 범주와 분석 규칙 식별자를 지정합니다. </summary>
      <param name="category">특성의 범주입니다.</param>
      <param name="checkId">특성이 적용될 분석 도구 규칙의 식별자입니다.</param>
    </member>
    <member name="P:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute.Category">
      <summary>특성의 분류를 식별하는 범주를 가져옵니다.</summary>
      <returns>특성을 식별하는 범주입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute.CheckId">
      <summary>표시하지 않을 정적 분석 도구 규칙의 식별자를 가져옵니다.</summary>
      <returns>표시하지 않을 정적 분석 도구 규칙의 식별자입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute.Justification">
      <summary>코드 분석 메시지를 표시하지 않는 정당한 이유를 가져오거나 설정합니다.</summary>
      <returns>메시지를 표시하지 않는 정당한 이유입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute.MessageId">
      <summary>제외 조건을 확장하는 선택적 인수를 가져오거나 설정합니다.</summary>
      <returns>확장된 제외 조건이 포함된 문자열입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute.Scope">
      <summary>특성과 관련된 코드 범위를 가져오거나 설정합니다.</summary>
      <returns>특성과 관련된 코드 범위입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute.Target">
      <summary>특성의 대상을 나타내는 정규화된 경로를 가져오거나 설정합니다.</summary>
      <returns>특성의 대상을 나타내는 정규화된 경로입니다.</returns>
    </member>
  </members>
</doc>