﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Diagnostics.Tools</name>
  </assembly>
  <members>
    <member name="T:System.CodeDom.Compiler.GeneratedCodeAttribute">
      <summary>Identifies code generated by a tool. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.GeneratedCodeAttribute.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.Compiler.GeneratedCodeAttribute" /> class specifying the name and version of the tool that generated the code.</summary>
      <param name="tool">The name of the tool that generated the code.</param>
      <param name="version">The version of the tool that generated the code.</param>
    </member>
    <member name="P:System.CodeDom.Compiler.GeneratedCodeAttribute.Tool">
      <summary>Gets the name of the tool that generated the code.</summary>
      <returns>The name of the tool that generated to code.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.GeneratedCodeAttribute.Version">
      <summary>Gets the version of the tool that generated the code.</summary>
      <returns>The version of the tool that generated the code.</returns>
    </member>
    <member name="T:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute">
      <summary>Suppresses reporting of a specific static analysis tool rule violation, allowing multiple suppressions on a single code artifact.</summary>
    </member>
    <member name="M:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute" /> class, specifying the category of the static analysis tool and the identifier for an analysis rule. </summary>
      <param name="category">The category for the attribute.</param>
      <param name="checkId">The identifier of the analysis tool rule the attribute applies to.</param>
    </member>
    <member name="P:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute.Category">
      <summary>Gets the category identifying the classification of the attribute.</summary>
      <returns>The category identifying the attribute.</returns>
    </member>
    <member name="P:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute.CheckId">
      <summary>Gets the identifier of the static analysis tool rule to be suppressed.</summary>
      <returns>The identifier of the static analysis tool rule to be suppressed.</returns>
    </member>
    <member name="P:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute.Justification">
      <summary>Gets or sets the justification for suppressing the code analysis message.</summary>
      <returns>The justification for suppressing the message.</returns>
    </member>
    <member name="P:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute.MessageId">
      <summary>Gets or sets an optional argument expanding on exclusion criteria.</summary>
      <returns>A string containing the expanded exclusion criteria.</returns>
    </member>
    <member name="P:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute.Scope">
      <summary>Gets or sets the scope of the code that is relevant for the attribute.</summary>
      <returns>The scope of the code that is relevant for the attribute.</returns>
    </member>
    <member name="P:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute.Target">
      <summary>Gets or sets a fully qualified path that represents the target of the attribute.</summary>
      <returns>A fully qualified path that represents the target of the attribute.</returns>
    </member>
  </members>
</doc>