﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Diagnostics.Tracing</name>
  </assembly>
  <members>
    <member name="T:System.Diagnostics.Tracing.EventActivityOptions">
      <summary>Especifica el seguimiento de actividad de inicio y detención de eventos. </summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.Detachable">
      <summary>Permitir la superposición de actividades.De manera predeterminada, deben anidarse en la propiedad los inicios y las paradas que se realizan en la actividad.Es decir, una secuencia de inicio A, inicio B, parada A, parada B no está permitida y dará como resultado que B se detenga al mismo tiempo que A.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.Disable">
      <summary>Desactivar iniciar y detener el seguimiento. </summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.None">
      <summary>Use el comportamiento predeterminado para el seguimiento de inicio y parada.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.Recursive">
      <summary>Permitir inicios de actividad recursiva.De manera predeterminada, una actividad no puede ser recursiva.Es decir, una secuencia de inicio A, inicio A, parada A, parada A no está permitida.Pueden producirse actividades recursivas involuntarias si se ejecuta la aplicación y, para algunos, no se realiza la parada hasta que se llame a otro inicio.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventAttribute">
      <summary>Especifica información adicional del esquema de eventos para un evento.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventAttribute.#ctor(System.Int32)">
      <summary>Inicializa una nueva instancia de <see cref="T:System.Diagnostics.Tracing.EventAttribute" /> con el identificador de eventos especificado.</summary>
      <param name="eventId">Identificador de eventos para el evento.</param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.ActivityOptions">
      <summary>Especifica el comportamiento de los eventos de inicio y parada de una actividad.Una actividad es el intervalo de tiempo en una aplicación entre el inicio y la parada.</summary>
      <returns>Devuelve <see cref="T:System.Diagnostics.Tracing.EventActivityOptions" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Channel">
      <summary>Obtiene o establece un registro de eventos adicional donde se debe escribir el evento.</summary>
      <returns>Registro de eventos adicional donde se debe escribir el evento.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.EventId">
      <summary>Obtiene o establece el identificador del evento.</summary>
      <returns>Identificador de evento.Este valor debe estar comprendido entre 0 y 65535.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Keywords">
      <summary>Obtiene o establece las palabras clave del evento.</summary>
      <returns>Combinación bit a bit de los valores de la enumeración.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Level">
      <summary>Obtiene o establece el nivel del evento.</summary>
      <returns>Uno de los valores de enumeración que especifica el nivel para el evento.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Message">
      <summary>Obtiene o establece el mensaje del evento.</summary>
      <returns>El mensaje del evento.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Opcode">
      <summary>Obtiene o establece el código de operación para el evento.</summary>
      <returns>Uno de los valores de la enumeración que especifica el código de operación.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Tags">
      <summary>Obtiene y establece el <see cref="T:System.Diagnostics.Tracing.EventTags" /> valor para este <see cref="T:System.Diagnostics.Tracing.EventAttribute" /> objeto.Una etiqueta de evento es un valor definido por el usuario que se pasa cuando se registra el evento.</summary>
      <returns>Devuelve el valor de <see cref="T:System.Diagnostics.Tracing.EventTags" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Task">
      <summary>Obtiene o establece la tarea del evento.</summary>
      <returns>Tarea para el evento.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Version">
      <summary>Obtiene o establece la versión del evento.</summary>
      <returns>Versión del evento.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventChannel">
      <summary>Especifica el canal de registro de eventos para el evento.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Admin">
      <summary>Canal de registro del administrador.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Analytic">
      <summary>Canal analítico.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Debug">
      <summary>Canal de depuración.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.None">
      <summary>Ningún canal especificado.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Operational">
      <summary>Canal operativo. </summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventCommand">
      <summary>Describe el comando (propiedad <see cref="P:System.Diagnostics.Tracing.EventCommandEventArgs.Command" />) que se pasa a la devolución de llamada del método <see cref="M:System.Diagnostics.Tracing.EventSource.OnEventCommand(System.Diagnostics.Tracing.EventCommandEventArgs)" />.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.Disable">
      <summary>Deshabilita el evento.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.Enable">
      <summary>Habilite el evento.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.SendManifest">
      <summary>Envíe el manifiesto.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.Update">
      <summary>Actualiza el evento.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventCommandEventArgs">
      <summary>Proporciona los argumentos para la devolución de llamada de <see cref="M:System.Diagnostics.Tracing.EventSource.OnEventCommand(System.Diagnostics.Tracing.EventCommandEventArgs)" />.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventCommandEventArgs.Arguments">
      <summary>Obtiene la matriz de argumentos para la devolución de llamada.</summary>
      <returns>Matriz de argumentos de devolución de llamada.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventCommandEventArgs.Command">
      <summary>Obtiene el comando para la devolución de llamada.</summary>
      <returns>El comando de devolución de llamada.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventCommandEventArgs.DisableEvent(System.Int32)">
      <summary>Deshabilita el evento que tiene el identificador especificado.</summary>
      <returns>Es true si <paramref name="eventId" /> se encuentra en el intervalo; de lo contrario, false.</returns>
      <param name="eventId">Identificador del evento que se va a deshabilitar.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventCommandEventArgs.EnableEvent(System.Int32)">
      <summary>Habilita el evento que tiene el identificador especificado.</summary>
      <returns>Es true si <paramref name="eventId" /> se encuentra en el intervalo; de lo contrario, false.</returns>
      <param name="eventId">Identificador del evento que se va a habilitar.</param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventDataAttribute">
      <summary>Especifica un tipo que se va a pasar al método <see cref="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions,``0)" />.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventDataAttribute.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" />. </summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventDataAttribute.Name">
      <summary>Obtiene o establece el nombre que se va a aplicar a un evento si la propiedad o el tipo de evento no se nombran de forma explícita.</summary>
      <returns>Nombre que se va a aplicar al evento o la propiedad.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventFieldAttribute">
      <summary>El <see cref="T:System.Diagnostics.Tracing.EventFieldAttribute" /> se coloca en los campos de tipos definidos por el usuario que se pasan como <see cref="T:System.Diagnostics.Tracing.EventSource" /> cargas. </summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventFieldAttribute.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Diagnostics.Tracing.EventFieldAttribute" />.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventFieldAttribute.Format">
      <summary>Obtiene y establece el valor que especifica cómo aplicar formato al valor de un tipo definido por el usuario.</summary>
      <returns>Devuelve un valor <see cref="T:System.Diagnostics.Tracing.EventFieldFormat" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventFieldAttribute.Tags">
      <summary>Obtiene y establece definido por el usuario <see cref="T:System.Diagnostics.Tracing.EventFieldTags" /> valor que es necesario para los campos que contienen datos que no es uno de los tipos admitidos. </summary>
      <returns>Devuelve <see cref="T:System.Diagnostics.Tracing.EventFieldTags" />.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventFieldFormat">
      <summary>Especifica cómo aplicar formato al valor de un tipo definido por el usuario y puede usarse para reemplazar el formato predeterminado de un campo.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Boolean">
      <summary>Boolean</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Default">
      <summary>Predeterminado:</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Hexadecimal">
      <summary>Hexadecimal.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.HResult">
      <summary>HResult.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Json">
      <summary>JSON.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.String">
      <summary>Cadena.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Xml">
      <summary>XML.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventFieldTags">
      <summary>Especifica la etiqueta definida por el usuario que se coloca en los campos de tipos definidos por el usuario que se pasan como <see cref="T:System.Diagnostics.Tracing.EventSource" /> cargas a través de la <see cref="T:System.Diagnostics.Tracing.EventFieldAttribute" />. </summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldTags.None">
      <summary>No especifica ninguna etiqueta y es igual a cero.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventIgnoreAttribute">
      <summary>Especifica una propiedad que se omitirán al escribir un tipo de evento con el <see cref="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions@,``0@)" /> método.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventIgnoreAttribute.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Diagnostics.Tracing.EventIgnoreAttribute" />.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventKeywords">
      <summary>Define las palabras clave estándar que se aplican a los eventos.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.All">
      <summary>Todos los bits se establecen en 1, que representa cada grupo de eventos posible.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.AuditFailure">
      <summary>Se asocia a todos los eventos de auditoría de seguridad que dan error.Use esta palabra clave solo para eventos del registro de seguridad.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.AuditSuccess">
      <summary>Se asocia a todos los eventos de auditoría de seguridad correctos.Use esta palabra clave solo para eventos del registro de seguridad.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.CorrelationHint">
      <summary>Se asocia para transferir los eventos donde el identificador de actividad relacionado (identificador de correlación) es un valor calculado y no se garantiza que sea único (es decir, no es un auténtico GUID).</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.EventLogClassic">
      <summary>Se adjunta a los eventos que se provocan utilizando la función RaiseEvent.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.None">
      <summary>No se realiza ningún filtrado en las palabras clave cuando se publica el evento.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.Sqm">
      <summary>Se asocia a todos los eventos de Mecanismo de calidad de servicio (SQM).</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.WdiContext">
      <summary>Se asocia a todos los eventos del contexto de la Infraestructura de diagnóstico de Windows (WDI).</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.WdiDiagnostic">
      <summary>Se asocia a todos los eventos de diagnóstico de la Infraestructura de diagnóstico de Windows (WDI).</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventLevel">
      <summary>Identifica el nivel de un evento.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Critical">
      <summary>Este nivel corresponde a errores críticos, que son errores graves que derivan en un error irrecuperable.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Error">
      <summary>Este nivel agrega los errores comunes que suponen un problema.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Informational">
      <summary>Este nivel agrega mensajes o eventos informativos que no indican errores.Estos eventos pueden ser de ayuda para el seguimiento del progreso o estado de una aplicación.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.LogAlways">
      <summary>No se realiza filtrado de niveles durante el evento.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Verbose">
      <summary>Este nivel agrega eventos o mensajes largos.Hace que se registren todos los eventos.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Warning">
      <summary>Este nivel agrega eventos de advertencia (por ejemplo, eventos que se publican porque se está a punto de alcanzar la capacidad total de un disco).</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventListener">
      <summary>Proporciona métodos para habilitar y deshabilitar eventos de orígenes de eventos.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.#ctor">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Diagnostics.Tracing.EventListener" />.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.DisableEvents(System.Diagnostics.Tracing.EventSource)">
      <summary>Deshabilita todos los eventos del origen de eventos especificado.</summary>
      <param name="eventSource">Origen de evento para el que se van a deshabilitar eventos.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.Dispose">
      <summary>Libera los recursos utilizados por la instancia actual de la clase <see cref="T:System.Diagnostics.Tracing.EventListener" />.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EnableEvents(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventLevel)">
      <summary>Habilita los eventos del origen de eventos especificado que tiene el nivel de detalle especificado o inferior.</summary>
      <param name="eventSource">Origen de evento para el que se van a habilitar eventos.</param>
      <param name="level">Nivel de eventos que se va a habilitar.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EnableEvents(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords)">
      <summary>Habilita los eventos del origen de eventos especificado que tiene el nivel de detalle especificado o inferior, y los marcadores de palabras clave coincidentes.</summary>
      <param name="eventSource">Origen de evento para el que se van a habilitar eventos.</param>
      <param name="level">Nivel de eventos que se va a habilitar.</param>
      <param name="matchAnyKeyword">Marcas de palabra clave necesarias para habilitar los eventos.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EnableEvents(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords,System.Collections.Generic.IDictionary{System.String,System.String})">
      <summary>Habilita los eventos del origen de eventos especificado que tiene el nivel de detalle especificado o inferior, el marcador de palabra clave de evento correspondiente, y los argumentos coincidentes.</summary>
      <param name="eventSource">Origen de evento para el que se van a habilitar eventos.</param>
      <param name="level">Nivel de eventos que se va a habilitar.</param>
      <param name="matchAnyKeyword">Marcas de palabra clave necesarias para habilitar los eventos.</param>
      <param name="arguments">Los argumentos que se hacen coincidir para habilitar los eventos.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EventSourceIndex(System.Diagnostics.Tracing.EventSource)">
      <summary>Obtiene un pequeño número no negativo que representa el origen de eventos especificado.</summary>
      <returns>Pequeño número no negativo que representa el origen de eventos especificado.</returns>
      <param name="eventSource">Origen de evento cuyo índice se va a buscar.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.OnEventSourceCreated(System.Diagnostics.Tracing.EventSource)">
      <summary>Llamado para todos los orígenes de eventos existentes cuando se crea el agente de escucha de eventos y cuando un nuevo origen de evento se asocia al agente de escucha.</summary>
      <param name="eventSource">Origen del evento.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.OnEventWritten(System.Diagnostics.Tracing.EventWrittenEventArgs)">
      <summary>Se llama siempre que un evento ha sido escrito por un origen de eventos para el que el agente de escucha de eventos ha habilitado los eventos.</summary>
      <param name="eventData">Argumentos de evento que describen el evento.</param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventManifestOptions">
      <summary>Especifica cómo se genera el manifiesto ETW para el origen del evento.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.AllCultures">
      <summary>Genera un nodo de recursos en la carpeta de localización para cada ensamblado satélite proporcionado.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.AllowEventSourceOverride">
      <summary>Invalida el comportamiento predeterminado que actual <see cref="T:System.Diagnostics.Tracing.EventSource" /> debe pasar al método write la clase base del tipo definido por el usuario.Esto permite la validación de orígenes de eventos .NET.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.None">
      <summary>No se especifican opciones.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.OnlyIfNeededForRegistration">
      <summary>Se genera un manifiesto, y solo debe registrarse el origen del evento en el equipo host.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.Strict">
      <summary>Provoca que se inicie una excepción si se producen incoherencias al escribir el archivo de manifiesto.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventOpcode">
      <summary>Define los códigos de operación estándar que el origen de eventos adjunta a los eventos.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.DataCollectionStart">
      <summary>Un evento de inicio de la colección de seguimiento.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.DataCollectionStop">
      <summary>Un evento de detención de la colección de seguimiento.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Extension">
      <summary>Evento de extensión.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Info">
      <summary>Evento de información.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Receive">
      <summary>Se publica un evento cuando una actividad en una aplicación recibe datos.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Reply">
      <summary>Se publica un evento después de que una actividad en una aplicación responda a un evento.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Resume">
      <summary>Se publica un evento después de que una actividad en una aplicación se reanude desde un estado suspendido.El evento debe ir detrás de un evento con el código de operación <see cref="F:System.Diagnostics.Tracing.EventOpcode.Suspend" />.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Send">
      <summary>Se publica un evento cuando una actividad en una aplicación transfiere datos o recursos del sistema a otra actividad.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Start">
      <summary>Se publica un evento cuando una aplicación inicia una nueva transacción o actividad.Este código de operación se puede incrustar dentro de otra transacción o actividad cuando varios eventos que hacen que el código de <see cref="F:System.Diagnostics.Tracing.EventOpcode.Start" /> se sigan unos a otros sin un evento intermedio que tiene un código de <see cref="F:System.Diagnostics.Tracing.EventOpcode.Stop" />.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Stop">
      <summary>Se publica un evento cuando finaliza una actividad o transacción en una aplicación.El evento corresponde al último evento no emparejado que tiene un código de operación <see cref="F:System.Diagnostics.Tracing.EventOpcode.Start" />.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Suspend">
      <summary>Se publica un evento cuando se suspende una actividad en una aplicación.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSource">
      <summary>Proporciona la capacidad de crear eventos de seguimiento de eventos para Windows (ETW).</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Diagnostics.Tracing.EventSource" />.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.Boolean)">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Diagnostics.Tracing.EventSource" /> y especifica si se inicia una excepción cuando se produce un error en el código subyacente de Windows.</summary>
      <param name="throwOnEventWriteErrors">true para iniciar una excepción cuando se produce un error en el código subyacente de Windows; en caso contrario, false.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.Diagnostics.Tracing.EventSourceSettings)">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Diagnostics.Tracing.EventSource" /> con las opciones de configuración especificadas.</summary>
      <param name="settings">Combinación bit a bit de los valores de enumeración que especifican los valores de configuración que se van a aplicar al origen del evento.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.Diagnostics.Tracing.EventSourceSettings,System.String[])">
      <summary>Inicializa una nueva instancia del <see cref="T:System.Diagnostics.Tracing.EventSource" /> para su uso con eventos que no forman parte de un contrato y contengan las características y rasgos especificados.</summary>
      <param name="settings">Combinación bit a bit de los valores de enumeración que especifican los valores de configuración que se van a aplicar al origen del evento.</param>
      <param name="traits">Los pares clave-valor que especifican rasgos para el origen del evento.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="traits" /> is not specified in key-value pairs.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.String)">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Diagnostics.Tracing.EventSource" /> con el nombre especificado.</summary>
      <param name="eventSourceName">Nombre que se va a aplicar al origen del evento.No debe ser null.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.String,System.Diagnostics.Tracing.EventSourceSettings)">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Diagnostics.Tracing.EventSource" /> con el nombre y configuración especificados.</summary>
      <param name="eventSourceName">Nombre que se va a aplicar al origen del evento.No debe ser null.</param>
      <param name="config">Combinación bit a bit de los valores de enumeración que especifican los valores de configuración que se van a aplicar al origen del evento.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.String,System.Diagnostics.Tracing.EventSourceSettings,System.String[])">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Diagnostics.Tracing.EventSource" /> con las opciones de configuración especificadas.</summary>
      <param name="eventSourceName">Nombre que se va a aplicar al origen del evento.No debe ser null.</param>
      <param name="config">Combinación bit a bit de los valores de enumeración que especifican los valores de configuración que se van a aplicar al origen del evento.</param>
      <param name="traits">Los pares clave-valor que especifican rasgos para el origen del evento.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="traits" /> is not specified in key-value pairs.</exception>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.ConstructionException">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Obtiene cualquier excepción que se produjo durante la construcción de un origen de eventos.</summary>
      <returns>La excepción que se inició durante la construcción del origen del evento o null si no se inició ninguna excepción. </returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.CurrentThreadActivityId">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Obtiene el id. de actividad del subproceso actual. </summary>
      <returns>Id. de actividad del subproceso actual. </returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Dispose">
      <summary>Libera todos los recursos usados por la instancia actual de la clase <see cref="T:System.Diagnostics.Tracing.EventSource" />.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados usados que usa la clase <see cref="T:System.Diagnostics.Tracing.EventSource" /> y, de forma opcional, libera los recursos administrados.</summary>
      <param name="disposing">Es true para liberar tanto recursos administrados como no administrados; es false para liberar únicamente recursos no administrados. </param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Finalize">
      <summary>Permite que el objeto <see cref="T:System.Diagnostics.Tracing.EventSource" /> intente liberar recursos y realizar otras operaciones de limpieza antes de que la recolección de elementos no utilizados lo recupere.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GenerateManifest(System.Type,System.String)">
      <summary>Devuelve una cadena del manifiesto XML asociado al origen de eventos actual.</summary>
      <returns>Cadena de datos XML.</returns>
      <param name="eventSourceType">Tipo del origen del evento.</param>
      <param name="assemblyPathToIncludeInManifest">La ruta de acceso al archivo de ensamblado (.dll) que se va a incluir en el elemento de proveedor del manifiesto. </param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GenerateManifest(System.Type,System.String,System.Diagnostics.Tracing.EventManifestOptions)">
      <summary>Devuelve una cadena del manifiesto XML asociado al origen de eventos actual.</summary>
      <returns>La cadena de datos XML o null (vea los comentarios).</returns>
      <param name="eventSourceType">Tipo del origen del evento.</param>
      <param name="assemblyPathToIncludeInManifest">La ruta de acceso al archivo de ensamblado (.dll) que se va a incluir en el elemento de proveedor del manifiesto. </param>
      <param name="flags">Combinación bit a bit de los valores de enumeración que especifican cómo se genera el manifiesto.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetGuid(System.Type)">
      <summary>Obtiene el identificador único de esta implementación del origen del evento.</summary>
      <returns>Un identificador único para este tipo de origen de eventos.</returns>
      <param name="eventSourceType">Tipo del origen del evento.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetName(System.Type)">
      <summary>Obtiene el nombre descriptivo del origen del evento.</summary>
      <returns>Nombre descriptivo del origen del evento.El valor predeterminado es el nombre sencillo de la clase.</returns>
      <param name="eventSourceType">Tipo del origen del evento.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetSources">
      <summary>Obtiene una instantánea de todos los orígenes de eventos para el dominio de aplicación.</summary>
      <returns>Una enumeración de todos los orígenes de eventos del dominio de aplicación.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetTrait(System.String)">
      <summary>Obtiene el valor de rasgo asociado a la clave especificada.</summary>
      <returns>El valor del rasgo asociado con la calve especificada.Si no se encuentra la calve, devuelve null.</returns>
      <param name="key">La clave del rasgo que se va a obtener.</param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.Guid">
      <summary>Identificador único para el origen de eventos.</summary>
      <returns>Un identificador único para el origen de eventos.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.IsEnabled">
      <summary>Determina si el origen de eventos actual está habilitado.</summary>
      <returns>true si el evento actual está habilitado; en caso contrario, false.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.IsEnabled(System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords)">
      <summary>Determina si el origen de eventos actual que tiene el nivel y la palabra clave especificados está habilitado.</summary>
      <returns>true si el origen del evento está habilitado; en caso contrario, false.</returns>
      <param name="level">Nivel del origen del evento.</param>
      <param name="keywords">Palabra clave del origen del evento.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.IsEnabled(System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords,System.Diagnostics.Tracing.EventChannel)">
      <summary>Determina si el origen del evento actual está habilitado para eventos con el nivel, las palabras clave y el canal especificados.</summary>
      <returns>true si el origen del evento está habilitado para el nivel de evento especificado, palabras calve y el canal; en caso contrario, false.El resultado de este método es solo una aproximación a si un evento en particular está activo.Se usa para evitar consumir muchos recursos para los registros si estos están deshabilitados.Los orígenes de eventos pueden tener un filtrado adicional que determine su actividad.</returns>
      <param name="level">Nivel de evento que se va a comprobar.Se considerará que un origen de evento está habilitado cuando su nivel sea igual o superior a <paramref name="level" />.</param>
      <param name="keywords">Palabras clave del evento que se van a comprobar.</param>
      <param name="channel">Canal del evento que se va a comprobar.</param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.Name">
      <summary>Nombre descriptivo de la clase que se deriva del origen de eventos.</summary>
      <returns>Nombre descriptivo de la clase derivada.El valor predeterminado es el nombre sencillo de la clase.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.OnEventCommand(System.Diagnostics.Tracing.EventCommandEventArgs)">
      <summary>Se llama cuando el origen de eventos actual es actualizado por el controlador.</summary>
      <param name="command">Argumentos del evento.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.SendCommand(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventCommand,System.Collections.Generic.IDictionary{System.String,System.String})">
      <summary>Envía un comando a un origen de eventos especificado.</summary>
      <param name="eventSource">Origen de evento al que se va a enviar el comando.</param>
      <param name="command">Comando de evento que se va a enviar.</param>
      <param name="commandArguments">Argumentos del comando de eventos.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.SetCurrentThreadActivityId(System.Guid)">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Establece el identificador de actividad del subproceso actual.</summary>
      <param name="activityId">El nuevo identificador de actividad del subproceso actual o <see cref="F:System.Guid.Empty" /> para indicar que el trabajo en el subproceso actual no está asociado con ninguna actividad. </param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.SetCurrentThreadActivityId(System.Guid,System.Guid@)">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Establece el identificador de actividad en el subproceso actual y devuelve el identificador de la actividad anterior.</summary>
      <param name="activityId">El nuevo identificador de actividad del subproceso actual o <see cref="F:System.Guid.Empty" /> para indicar que el trabajo en el subproceso actual no está asociado con ninguna actividad.</param>
      <param name="oldActivityThatWillContinue">El resultado que devuelve este método contiene el identificador de la actividad anterior en el subproceso actual. </param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.Settings">
      <summary>Obtiene la configuración aplicada a este origen del evento.</summary>
      <returns>Configuración aplicada a este origen del evento.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.ToString">
      <summary>Obtiene una representación de cadena de la instancia del origen de eventos actual.</summary>
      <returns>Nombre e identificador único que identifican el origen de eventos actual.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write(System.String)">
      <summary>Escribe un evento sin campos, pero con el nombre especificado y las opciones predeterminadas.</summary>
      <param name="eventName">Nombre del evento que se va a escribir.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write(System.String,System.Diagnostics.Tracing.EventSourceOptions)">
      <summary>Escribe un evento sin campos, pero con el nombre y las opciones especificados.</summary>
      <param name="eventName">Nombre del evento que se va a escribir.</param>
      <param name="options">Opciones como el nivel, las palabras clave y el código de operación del evento.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions,``0)">
      <summary>Escribe un evento con el nombre, los datos de evento y las opciones especificados.</summary>
      <param name="eventName">Nombre del evento.</param>
      <param name="options">Opciones de evento.</param>
      <param name="data">Datos del evento.Este tipo debe ser un tipo anónimo o estar marcado con el atributo <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" />.</param>
      <typeparam name="T">Tipo que define el evento y los datos asociados.Este tipo debe ser un tipo anónimo o estar marcado con el atributo <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" />.</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions@,System.Guid@,System.Guid@,``0@)">
      <summary>Escribe un evento con el nombre, las opciones, la actividad relacionada y los datos de evento especificados.</summary>
      <param name="eventName">Nombre del evento.</param>
      <param name="options">Opciones de evento.</param>
      <param name="activityId">Identificador de la actividad asociada al evento.</param>
      <param name="relatedActivityId">El identificador de una actividad asociada o <see cref="F:System.Guid.Empty" /> si no hay una actividad asociada.</param>
      <param name="data">Datos del evento.Este tipo debe ser un tipo anónimo o estar marcado con el atributo <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" />.</param>
      <typeparam name="T">Tipo que define el evento y los datos asociados.Este tipo debe ser un tipo anónimo o estar marcado con el atributo <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" />.</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions@,``0@)">
      <summary>Escribe un evento con el nombre, las opciones y los datos de evento especificados.</summary>
      <param name="eventName">Nombre del evento.</param>
      <param name="options">Opciones de evento.</param>
      <param name="data">Datos del evento.Este tipo debe ser un tipo anónimo o estar marcado con el atributo <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" />.</param>
      <typeparam name="T">Tipo que define el evento y los datos asociados.Este tipo debe ser un tipo anónimo o estar marcado con el atributo <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" />.</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,``0)">
      <summary>Escribe un evento con el nombre y los datos especificados.</summary>
      <param name="eventName">Nombre del evento.</param>
      <param name="data">Datos del evento.Este tipo debe ser un tipo anónimo o estar marcado con el atributo <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" />.</param>
      <typeparam name="T">Tipo que define el evento y los datos asociados.Este tipo debe ser un tipo anónimo o estar marcado con el atributo <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" />.</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32)">
      <summary>Escribe un evento con el identificador de eventos proporcionado.</summary>
      <param name="eventId">Identificador de evento.Este valor debe estar comprendido entre 0 y 65535.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Byte[])">
      <summary>Escribe un evento con el identificador de eventos y el argumento de matriz de bytes especificados.</summary>
      <param name="eventId">Identificador de evento.Este valor debe estar comprendido entre 0 y 65535.</param>
      <param name="arg1">Argumento de matriz de bytes.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32)">
      <summary>Escribe un evento con el identificador de eventos y el argumento entero de 32 bits especificados.</summary>
      <param name="eventId">Identificador de evento.Este valor debe estar comprendido entre 0 y 65535.</param>
      <param name="arg1">Argumento de tipo entero.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32,System.Int32)">
      <summary>Escribe un evento con el identificador de eventos y los argumentos enteros de 32 bits especificados.</summary>
      <param name="eventId">Identificador de evento.Este valor debe estar comprendido entre 0 y 65535.</param>
      <param name="arg1">Argumento de tipo entero.</param>
      <param name="arg2">Argumento de tipo entero.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>Escribe un evento con el identificador de eventos y los argumentos enteros de 32 bits especificados.</summary>
      <param name="eventId">Identificador de evento.Este valor debe estar comprendido entre 0 y 65535.</param>
      <param name="arg1">Argumento de tipo entero.</param>
      <param name="arg2">Argumento de tipo entero.</param>
      <param name="arg3">Argumento de tipo entero.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32,System.String)">
      <summary>Escribe un evento con el identificador de eventos y los argumentos de cadena y enteros de 32 bits especificados.</summary>
      <param name="eventId">Identificador de evento.Este valor debe estar comprendido entre 0 y 65535.</param>
      <param name="arg1">Argumento de tipo entero de 32 bits.</param>
      <param name="arg2">Argumento de cadena.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64)">
      <summary>Escribe un evento con el identificador de eventos y el argumento entero de 64 bits especificados.</summary>
      <param name="eventId">Identificador de evento.Este valor debe estar comprendido entre 0 y 65535.</param>
      <param name="arg1">Argumento de tipo entero de 64 bits.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.Byte[])">
      <summary>Escribe los datos del evento con el identificador y los argumentos de matriz de bytes y enteros de 64 bits especificados.</summary>
      <param name="eventId">Identificador de evento.Este valor debe estar comprendido entre 0 y 65535.</param>
      <param name="arg1">Argumento de tipo entero de 64 bits.</param>
      <param name="arg2">Argumento de matriz de bytes.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.Int64)">
      <summary>Escribe un evento con el identificador de eventos y los argumentos de 64 bits especificados.</summary>
      <param name="eventId">Identificador de evento.Este valor debe estar comprendido entre 0 y 65535.</param>
      <param name="arg1">Argumento de tipo entero de 64 bits.</param>
      <param name="arg2">Argumento de tipo entero de 64 bits.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.Int64,System.Int64)">
      <summary>Escribe un evento con el identificador de eventos y los argumentos de 64 bits especificados.</summary>
      <param name="eventId">Identificador de evento.Este valor debe estar comprendido entre 0 y 65535.</param>
      <param name="arg1">Argumento de tipo entero de 64 bits.</param>
      <param name="arg2">Argumento de tipo entero de 64 bits.</param>
      <param name="arg3">Argumento de tipo entero de 64 bits.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.String)">
      <summary>Escribe un evento con el identificador de eventos y los argumentos de cadena y enteros de 64 bits especificados.</summary>
      <param name="eventId">Identificador de evento.Este valor debe estar comprendido entre 0 y 65535.</param>
      <param name="arg1">Argumento de tipo entero de 64 bits.</param>
      <param name="arg2">Argumento de cadena.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Object[])">
      <summary>Escribe un evento con el identificador de eventos y la matriz de argumentos especificados.</summary>
      <param name="eventId">Identificador de evento.Este valor debe estar comprendido entre 0 y 65535.</param>
      <param name="args">Matriz de objetos.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String)">
      <summary>Escribe un evento con el identificador de eventos y el argumento de cadena especificados.</summary>
      <param name="eventId">Identificador de evento.Este valor debe estar comprendido entre 0 y 65535.</param>
      <param name="arg1">Argumento de cadena.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.Int32)">
      <summary>Escribe un evento con el identificador de eventos y los argumentos especificados.</summary>
      <param name="eventId">Identificador de evento.Este valor debe estar comprendido entre 0 y 65535.</param>
      <param name="arg1">Argumento de cadena.</param>
      <param name="arg2">Argumento de tipo entero de 32 bits.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.Int32,System.Int32)">
      <summary>Escribe un evento con el identificador de eventos y los argumentos especificados.</summary>
      <param name="eventId">Identificador de evento.Este valor debe estar comprendido entre 0 y 65535.</param>
      <param name="arg1">Argumento de cadena.</param>
      <param name="arg2">Argumento de tipo entero de 32 bits.</param>
      <param name="arg3">Argumento de tipo entero de 32 bits.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.Int64)">
      <summary>Escribe un evento con el identificador de eventos y los argumentos especificados.</summary>
      <param name="eventId">Identificador de evento.Este valor debe estar comprendido entre 0 y 65535.</param>
      <param name="arg1">Argumento de cadena.</param>
      <param name="arg2">Argumento de tipo entero de 64 bits.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.String)">
      <summary>Escribe un evento con el identificador de eventos y los argumentos de cadena especificados.</summary>
      <param name="eventId">Identificador de evento.Este valor debe estar comprendido entre 0 y 65535.</param>
      <param name="arg1">Argumento de cadena.</param>
      <param name="arg2">Argumento de cadena.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.String,System.String)">
      <summary>Escribe un evento con el identificador de eventos y los argumentos de cadena especificados.</summary>
      <param name="eventId">Identificador de evento.Este valor debe estar comprendido entre 0 y 65535.</param>
      <param name="arg1">Argumento de cadena.</param>
      <param name="arg2">Argumento de cadena.</param>
      <param name="arg3">Argumento de cadena.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEventCore(System.Int32,System.Int32,System.Diagnostics.Tracing.EventSource.EventData*)">
      <summary>Crea una nueva sobrecarga de <see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" /> usando el identificador de eventos y los datos de eventos proporcionados.</summary>
      <param name="eventId">Identificador de evento.</param>
      <param name="eventDataCount">El número de elementos de datos de eventos.</param>
      <param name="data">Estructura que contiene los datos del evento.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEventWithRelatedActivityId(System.Int32,System.Guid,System.Object[])">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Escribe un evento que indica que la actividad actual se relaciona con otra actividad. </summary>
      <param name="eventId">Un identificador que identifica de forma única este evento dentro de <see cref="T:System.Diagnostics.Tracing.EventSource" />. </param>
      <param name="relatedActivityId">Identificador de actividad relacionado. </param>
      <param name="args">Matriz de objetos que contiene los datos del evento. </param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEventWithRelatedActivityIdCore(System.Int32,System.Guid*,System.Int32,System.Diagnostics.Tracing.EventSource.EventData*)">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Escribe un evento que indica que la actividad actual se relaciona con otra actividad.</summary>
      <param name="eventId">Un identificador que identifica de forma única este evento dentro de <see cref="T:System.Diagnostics.Tracing.EventSource" />.</param>
      <param name="relatedActivityId">Puntero al GUID del identificador de actividad relacionado. </param>
      <param name="eventDataCount">El número de elementos en el campo <paramref name="data" />. </param>
      <param name="data">Un puntero al primer elemento del campo de datos de evento. </param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSource.EventData">
      <summary>Proporciona los datos de evento para crear sobrecargas rápidas de <see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" /> con el método <see cref="M:System.Diagnostics.Tracing.EventSource.WriteEventCore(System.Int32,System.Int32,System.Diagnostics.Tracing.EventSource.EventData*)" />.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.EventData.DataPointer">
      <summary>Obtiene o establece el puntero a los datos de la nueva sobrecarga de <see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" />.</summary>
      <returns>Puntero a los datos.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.EventData.Size">
      <summary>Obtiene o establece el número de elementos de carga en la nueva sobrecarga de <see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" />.</summary>
      <returns>El número de elementos de carga en la nueva sobrecarga.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSourceAttribute">
      <summary>Permite que el seguimiento de eventos para el nombre Windows (ETW) se defina independientemente del nombre de la clase del origen de eventos.   </summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceAttribute.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" />.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceAttribute.Guid">
      <summary>Obtiene o establece el identificador de origen de evento.</summary>
      <returns>Identificador de origen del evento.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceAttribute.LocalizationResources">
      <summary>Obtiene o establece el nombre del archivo de recursos de localización.</summary>
      <returns>Nombre del archivo de recursos de localización, o null si no existe el archivo de recursos de localización.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceAttribute.Name">
      <summary>Obtiene o establece el nombre del origen del evento.</summary>
      <returns>Nombre del origen de eventos.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSourceException">
      <summary>Excepción que se produce cuando ocurre un error durante el seguimiento de eventos para Windows (ETW).</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceException.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Diagnostics.Tracing.EventSourceException" />.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Diagnostics.Tracing.EventSourceException" /> con el mensaje de error especificado.</summary>
      <param name="message">Mensaje que describe el error.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Diagnostics.Tracing.EventSourceException" /> con el mensaje de error especificado y una referencia a la excepción interna que representa la causa de esta excepción.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción. </param>
      <param name="innerException">La excepción que es la causa de la excepción actual o null si no se especifica ninguna excepción interna. </param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSourceOptions">
      <summary>Especifica las invalidaciones de valores predeterminados de evento, como el nivel de registro, palabras clave y la operación de código cuando el <see cref="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions,``0)" /> se llama al método.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.ActivityOptions"></member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Keywords">
      <summary>Obtiene o establece las palabras clave aplicadas al evento.Si no se establece esta propiedad, las palabras clave del evento serán None.</summary>
      <returns>Palabras clave aplicadas al evento, o None si no se establece ninguna.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Level">
      <summary>Obtiene o establece el nivel de evento aplicado al evento. </summary>
      <returns>Nivel de evento para el evento.Si no se establece, el valor predeterminado es Verbose (5).</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Opcode">
      <summary>Obtiene o establece el código de operación que se va a usar para el evento especificado. </summary>
      <returns>Código de operación que se va a usar para el evento especificado.Si no se establece, el valor predeterminado es Info (0).</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Tags"></member>
    <member name="T:System.Diagnostics.Tracing.EventSourceSettings">
      <summary>Especifica las opciones de configuración para un origen del evento.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.Default">
      <summary>No se ha habilitado ninguna de las opciones de configuración especiales.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.EtwManifestEventFormat">
      <summary>El agente de escucha ETW debe usar un formato basado en manifiesto al generar eventos.Establece esta opción una directiva para el agente de escucha ETW, que debe usar un formato basado en manifiesto al generar eventos.Esta es la opción predeterminada al definir un tipo derivado de <see cref="T:System.Diagnostics.Tracing.EventSource" /> mediante uno de los protegido <see cref="T:System.Diagnostics.Tracing.EventSource" /> constructores.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.EtwSelfDescribingEventFormat">
      <summary>El agente de escucha ETW debe usar un formato de eventos autodescriptivo.Esta es la opción predeterminada cuando se crea una nueva instancia de la <see cref="T:System.Diagnostics.Tracing.EventSource" /> con uno del público <see cref="T:System.Diagnostics.Tracing.EventSource" /> constructores.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.ThrowOnEventWriteErrors">
      <summary>El origen del evento genera una excepción cuando se produce un error.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventTags">
      <summary>Especifica el seguimiento de de los eventos de inicio y detención de la actividad.Solo debe usar los 24 bits inferiores.Para obtener más información, consulte <see cref="T:System.Diagnostics.Tracing.EventSourceOptions" /> y <see cref="M:System.Diagnostics.Tracing.EventSource.Write(System.String,System.Diagnostics.Tracing.EventSourceOptions)" />.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventTags.None">
      <summary>No especifica ninguna etiqueta y es igual a cero.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventTask">
      <summary>Define las tareas que se aplican a los eventos.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventTask.None">
      <summary>Tarea definida.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventWrittenEventArgs">
      <summary>Proporciona datos para la devolución de llamada <see cref="M:System.Diagnostics.Tracing.EventListener.OnEventWritten(System.Diagnostics.Tracing.EventWrittenEventArgs)" />.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.ActivityId">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Obtiene el id. de actividad del subproceso en el que se escribió el evento. </summary>
      <returns>El id. de actividad del subproceso en el que se escribió el evento. </returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Channel">
      <summary>Obtiene el canal del evento.</summary>
      <returns>Canal del evento.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.EventId">
      <summary>Obtiene el identificador de evento.</summary>
      <returns>Identificador de evento.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.EventName">
      <summary>Obtiene el nombre del evento.</summary>
      <returns>Nombre del evento.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.EventSource">
      <summary>Obtiene el objeto de origen del evento.</summary>
      <returns>Objeto de origen del evento.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Keywords">
      <summary>Obtiene las palabras clave del evento.</summary>
      <returns>Palabras clave para el evento.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Level">
      <summary>Obtiene el nivel del evento.</summary>
      <returns>Nivel del evento.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Message">
      <summary>Obtiene el mensaje del evento.</summary>
      <returns>El mensaje del evento.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Opcode">
      <summary>Obtiene el código de operación para el evento.</summary>
      <returns>Código de operación para el evento.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Payload">
      <summary>Obtiene la carga del evento.</summary>
      <returns>Carga para el evento.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.PayloadNames">
      <summary>Devuelve una lista de cadenas que representan los nombres de propiedad del evento.</summary>
      <returns>Devuelve <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.RelatedActivityId">
      <summary>[Compatible con .NET Framework 4.5.1 y versiones posteriores] Obtiene el identificador de una actividad relacionado con la actividad representada por la instancia actual. </summary>
      <returns>Identificador de la actividad relacionada, o <see cref="F:System.Guid.Empty" /> si no hay ninguna actividad relacionada.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Tags">
      <summary>Devuelve las etiquetas especificadas en la llamada al método <see cref="M:System.Diagnostics.Tracing.EventSource.Write(System.String,System.Diagnostics.Tracing.EventSourceOptions)" />.</summary>
      <returns>Devuelve <see cref="T:System.Diagnostics.Tracing.EventTags" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Task">
      <summary>Obtiene la tarea del evento.</summary>
      <returns>Tarea para el evento.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Version">
      <summary>Obtiene la versión del evento.</summary>
      <returns>Versión del evento.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.NonEventAttribute">
      <summary>Identifica un método que no está generando un evento.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.NonEventAttribute.#ctor">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Diagnostics.Tracing.NonEventAttribute" />.</summary>
    </member>
  </members>
</doc>