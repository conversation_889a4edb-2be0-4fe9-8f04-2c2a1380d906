﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Diagnostics.Tracing</name>
  </assembly>
  <members>
    <member name="T:System.Diagnostics.Tracing.EventActivityOptions">
      <summary>追跡を指定します。 アクティビティの開始および停止イベントです。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.Detachable">
      <summary>アクティビティの重複を許可します。既定では、アクティビティの開始と終了は入れ子になったプロパティにする必要があります。つまり、A の開始、B の開始、A の停止、B の停止のシーケンスは許可されず、B が A と同時に停止します。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.Disable">
      <summary>開始をオフにして、追跡を停止します。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.None">
      <summary>追跡の開始と停止の既定の動作を使用します。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.Recursive">
      <summary>再帰的なアクティビティの開始を許可します。既定では、アクティビティは再帰的にすることはできません。つまり、A の開始、A の開始、A の停止、A の停止のシーケンスは許可されません。意図的でない再帰的なアクティビティは、アプリケーションを実行し、別の開始が呼び出される前に一部が停止に到達していない場合に発生する可能性があります。</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventAttribute">
      <summary>イベントに追加イベントのスキーマ情報を指定します。</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventAttribute.#ctor(System.Int32)">
      <summary>指定したイベント識別子を使用して、<see cref="T:System.Diagnostics.Tracing.EventAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="eventId">イベントのイベント識別子。</param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.ActivityOptions">
      <summary>アクティビティのイベントの開始と停止の動作を指定します。アクティビティは、アプリケーションの開始から停止までの時間の領域です。</summary>
      <returns>
        <see cref="T:System.Diagnostics.Tracing.EventActivityOptions" /> を返します。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Channel">
      <summary>イベントの書き込み先となる追加のイベント ログを取得または設定します。</summary>
      <returns>イベントの書き込み先となる追加のイベント ログ。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.EventId">
      <summary>イベントの識別子を取得または設定します。</summary>
      <returns>イベント識別子。有効値の範囲は 0 ～ 65535 です。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Keywords">
      <summary>イベントのキーワードを取得または設定します。</summary>
      <returns>列挙値のビットごとの組み合わせ。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Level">
      <summary>イベントのレベルを取得または設定します。</summary>
      <returns>イベントのレベルを指定する列挙値の 1 つ。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Message">
      <summary>イベントのメッセージを取得または設定します。</summary>
      <returns>イベントに関するメッセージ。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Opcode">
      <summary>イベントのオペレーション コードを取得または設定します。</summary>
      <returns>操作コードを指定する列挙値の 1 つ。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Tags">
      <summary>取得および設定、<see cref="T:System.Diagnostics.Tracing.EventTags" />値<see cref="T:System.Diagnostics.Tracing.EventAttribute" />オブジェクトです。イベント タグは、イベントがログに記録されるときに渡されるユーザー定義の値です。</summary>
      <returns>
        <see cref="T:System.Diagnostics.Tracing.EventTags" /> 値を返します。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Task">
      <summary>イベントのタスクを取得または設定します。</summary>
      <returns>イベントのタスク。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Version">
      <summary>イベントのバージョンを取得または設定します。</summary>
      <returns>イベントのバージョン。</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventChannel">
      <summary>イベントのイベント ログ チャネルを指定します。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Admin">
      <summary>管理者ログ チャネル。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Analytic">
      <summary>分析チャネル。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Debug">
      <summary>デバッグ チャネル。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.None">
      <summary>チャネル指定なし。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Operational">
      <summary>オペレーション チャネル。</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventCommand">
      <summary>
        <see cref="M:System.Diagnostics.Tracing.EventSource.OnEventCommand(System.Diagnostics.Tracing.EventCommandEventArgs)" /> コールバックに渡されるコマンド (<see cref="P:System.Diagnostics.Tracing.EventCommandEventArgs.Command" /> プロパティ) を表します。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.Disable">
      <summary>イベントを無効にします。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.Enable">
      <summary>イベントを有効にします。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.SendManifest">
      <summary>マニフェストを送信します。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.Update">
      <summary>イベントを更新します。</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventCommandEventArgs">
      <summary>
        <see cref="M:System.Diagnostics.Tracing.EventSource.OnEventCommand(System.Diagnostics.Tracing.EventCommandEventArgs)" /> のコールバックの引数を提供します。</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventCommandEventArgs.Arguments">
      <summary>コールバックの引数の配列を取得します。</summary>
      <returns>コールバック引数の配列。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventCommandEventArgs.Command">
      <summary>コールバック用のコマンドを取得します。</summary>
      <returns>コールバック コマンド。</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventCommandEventArgs.DisableEvent(System.Int32)">
      <summary>指定した識別子を持つイベントを無効にします。</summary>
      <returns>
        <paramref name="eventId" /> が範囲内にある場合は true。それ以外の場合は false。</returns>
      <param name="eventId">無効にするイベントの識別子。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventCommandEventArgs.EnableEvent(System.Int32)">
      <summary>指定した識別子を持つイベントを有効にします。</summary>
      <returns>
        <paramref name="eventId" /> が範囲内にある場合は true。それ以外の場合は false。</returns>
      <param name="eventId">有効にするイベントの識別子。</param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventDataAttribute">
      <summary>渡される型を指定、<see cref="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions,``0)" />メソッドです。</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventDataAttribute.#ctor">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventDataAttribute.Name">
      <summary>イベント型またはプロパティに明示的に名前が付けられていない場合、イベントに適用する名前を取得または設定します。</summary>
      <returns>イベントまたはプロパティに適用する名前。</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventFieldAttribute">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventFieldAttribute" />として渡されるユーザー定義型のフィールドに配置が<see cref="T:System.Diagnostics.Tracing.EventSource" />ペイロード。</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventFieldAttribute.#ctor">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventFieldAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventFieldAttribute.Format">
      <summary>ユーザー定義型の値の書式設定の方法を指定する値を取得および設定します。</summary>
      <returns>
        <see cref="T:System.Diagnostics.Tracing.EventFieldFormat" /> 値を返します。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventFieldAttribute.Tags">
      <summary>取得し、ユーザー定義設定<see cref="T:System.Diagnostics.Tracing.EventFieldTags" />、サポートされている型のいずれかではないデータを含むフィールドに必要な値です。</summary>
      <returns>
        <see cref="T:System.Diagnostics.Tracing.EventFieldTags" /> を返します。</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventFieldFormat">
      <summary>ユーザー定義型の値を書式設定する方法を指定し、フィールドの既定の書式の上書きに使用することができます。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Boolean">
      <summary>Boolean</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Default">
      <summary>既定モード。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Hexadecimal">
      <summary>16 進数。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.HResult">
      <summary>HResult。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Json">
      <summary>JSON。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.String">
      <summary>文字列。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Xml">
      <summary>XML。</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventFieldTags">
      <summary>として渡されるユーザー定義型のフィールドに配置されているユーザー定義のタグを指定<see cref="T:System.Diagnostics.Tracing.EventSource" />ペイロードを介して、<see cref="T:System.Diagnostics.Tracing.EventFieldAttribute" />です。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldTags.None">
      <summary>タグなしを指定し、0 に等しくなります。</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventIgnoreAttribute">
      <summary>イベントの種類を記述する場合は、プロパティを無視するかを示す、<see cref="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions@,``0@)" />メソッドです。</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventIgnoreAttribute.#ctor">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventIgnoreAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventKeywords">
      <summary>イベントに適用される標準キーワードを定義します。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.All">
      <summary>すべてのビットは、イベントが取り得るすべてのグループを表す 1 に設定されます。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.AuditFailure">
      <summary>失敗したすべてのセキュリティ監査イベントにアタッチされます。このキーワードは、セキュリティ ログ内のイベントにのみ使用してください。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.AuditSuccess">
      <summary>成功したすべてのセキュリティ監査イベントにアタッチされます。このキーワードは、セキュリティ ログ内のイベントにのみ使用してください。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.CorrelationHint">
      <summary>関連アクティビティ ID (相関 ID) が算出された値で、一意であるとは限らない (実際の GUID ではない) 転送イベントにアタッチされます。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.EventLogClassic">
      <summary>RaiseEvent 関数を使用して発生するイベントにアタッチされます。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.None">
      <summary>イベントの発行時にキーワードに関するフィルター処理が行われません。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.Sqm">
      <summary>すべての SQM (Service Quality Mechanism) イベントにアタッチされます。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.WdiContext">
      <summary>すべての Windows 診断インフラストラクチャ (WDI) コンテキスト イベントにアタッチされます。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.WdiDiagnostic">
      <summary>すべての Windows 診断インフラストラクチャ (WDI) 診断イベントにアタッチされます。</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventLevel">
      <summary>イベントのレベルを識別します。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Critical">
      <summary>このレベルは重大なエラーに相当します。これは主要なエラーを発生させる深刻なエラーです。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Error">
      <summary>このレベルは、問題を示す標準のエラーを追加します。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Informational">
      <summary>このレベルは、エラーではない情報イベントまたは情報メッセージを追加します。これらのイベントは、アプリケーションの進行状況や状態を追跡するのに役立ちます。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.LogAlways">
      <summary>イベントに対してレベルのフィルター処理は行われません。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Verbose">
      <summary>このレベルは詳細なイベントまたはメッセージを追加します。これにより、すべてのイベントがログに記録されます。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Warning">
      <summary>このレベルは警告イベント (たとえば、ディスクの空き容量がほとんどないために発行されるイベント) を追加します。</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventListener">
      <summary>イベント ソースからのイベントを有効または無効にするメソッドを提供します。</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.#ctor">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventListener" /> クラスの新しいインスタンスを作成します。</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.DisableEvents(System.Diagnostics.Tracing.EventSource)">
      <summary>指定したイベント ソースのすべてのイベントを無効にします。</summary>
      <param name="eventSource">イベントを無効にするイベント ソース。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.Dispose">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventListener" /> クラスの現在のインスタンスによって使用されているリソースを解放します。</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EnableEvents(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventLevel)">
      <summary>指定した詳細レベル以下の指定したイベント ソースのイベントを有効にします。</summary>
      <param name="eventSource">イベントを有効にするイベント ソース。</param>
      <param name="level">有効にするイベントのレベル。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EnableEvents(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords)">
      <summary>指定した詳細レベル以下で、キーワード フラグが一致している指定したイベント ソースのイベントを有効にします。</summary>
      <param name="eventSource">イベントを有効にするイベント ソース。</param>
      <param name="level">有効にするイベントのレベル。</param>
      <param name="matchAnyKeyword">イベントを有効にするために必要なキーワード フラグ。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EnableEvents(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords,System.Collections.Generic.IDictionary{System.String,System.String})">
      <summary>指定した詳細レベル以下で、イベント キーワード フラグが一致し、引数が一致している指定したイベント ソースのイベントを有効にします。</summary>
      <param name="eventSource">イベントを有効にするイベント ソース。</param>
      <param name="level">有効にするイベントのレベル。</param>
      <param name="matchAnyKeyword">イベントを有効にするために必要なキーワード フラグ。</param>
      <param name="arguments">イベントを有効にするために一致させる引数。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EventSourceIndex(System.Diagnostics.Tracing.EventSource)">
      <summary>指定されたイベント ソースを表す負でない小さい数値を取得します。</summary>
      <returns>指定されたイベント ソースを表す負でない小さい数値。</returns>
      <param name="eventSource">インデックスを検索するイベント ソース。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.OnEventSourceCreated(System.Diagnostics.Tracing.EventSource)">
      <summary>イベント リスナーが作成される場合や、新しいイベント ソースがリスナーにアタッチされる場合に、既存のすべてのイベント ソースに対して呼び出されます。</summary>
      <param name="eventSource">イベント ソース。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.OnEventWritten(System.Diagnostics.Tracing.EventWrittenEventArgs)">
      <summary>イベント リスナーがイベントを有効にしたイベント ソースによってイベントが記述されたときに呼び出されます。</summary>
      <param name="eventData">イベントを表すイベント引数。</param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventManifestOptions">
      <summary>イベント ソースの ETW マニフェストの生成方法を指定します。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.AllCultures">
      <summary>提供されるすべてのサテライト アセンブリに対して、ローカリゼーション フォルダーの下にリソース ノードを生成します。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.AllowEventSourceOverride">
      <summary>既定の動作を上書きする、現在<see cref="T:System.Diagnostics.Tracing.EventSource" />書き込みメソッドに渡されるユーザー定義型の基本クラスする必要があります。これにより、.NET イベント ソースの検証が可能になります。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.None">
      <summary>オプションは指定されていません。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.OnlyIfNeededForRegistration">
      <summary>マニフェストは、イベント ソースがホスト コンピューターに登録される必要がある場合にのみ生成されます。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.Strict">
      <summary>マニフェスト ファイルの書き込み時に何らかの不一致が起こった場合、例外が発生します。</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventOpcode">
      <summary>イベント ソースがイベントにアタッチする標準オペレーション コードを定義します。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.DataCollectionStart">
      <summary>トレース コレクション開始イベント。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.DataCollectionStop">
      <summary>トレース コレクション停止イベント。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Extension">
      <summary>拡張イベント。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Info">
      <summary>情報イベント。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Receive">
      <summary>アプリケーション内のアクティビティがデータを受信したときに発行されるイベント。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Reply">
      <summary>アプリケーション内のアクティビティがイベントに応答した後で発行されるイベント。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Resume">
      <summary>アプリケーション内のアクティビティが中断状態から再開した後に発行されるイベント。イベントは <see cref="F:System.Diagnostics.Tracing.EventOpcode.Suspend" /> オペレーション コードがあるイベントに続く必要があります。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Send">
      <summary>アプリケーション内のアクティビティがデータまたはシステム リソースを別のアクティビティに転送したときに発行されるイベント。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Start">
      <summary>アプリケーションが新しいトランザクションまたはアクティビティを開始したときに発行されるイベント。<see cref="F:System.Diagnostics.Tracing.EventOpcode.Start" /> コードがあるイベントが、<see cref="F:System.Diagnostics.Tracing.EventOpcode.Stop" /> コードのあるイベントを間にはさまずに複数個連続している場合は、このオペレーション コードが別のトランザクションまたはアクティビティに埋め込まれている可能性があります。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Stop">
      <summary>アプリケーション内のアクティビティまたはトランザクションが終了したときに発行されるイベント。このイベントは、<see cref="F:System.Diagnostics.Tracing.EventOpcode.Start" /> オペレーション コードがあり、対になっていない直前のイベントに対応しています。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Suspend">
      <summary>アプリケーション内のアクティビティが中断されたときに発行されるイベント。</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSource">
      <summary>Windows イベント トレーシング (ETW: Event Tracing for Windows) のイベントを作成できます。</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventSource" /> クラスの新しいインスタンスを作成します。</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.Boolean)">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventSource" /> クラスの新しいインスタンスを作成し、基になる Windows コードでエラーが発生した場合に例外をスローするかどうかを指定します。</summary>
      <param name="throwOnEventWriteErrors">エラーが基になる Windows コードで発生した場合に例外をスローする場合は true。それ以外の場合は false。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.Diagnostics.Tracing.EventSourceSettings)">
      <summary>指定した構成設定を使用して <see cref="T:System.Diagnostics.Tracing.EventSource" /> クラスの新しいインスタンスを作成します。</summary>
      <param name="settings">イベント ソースに適用する構成設定を指定する列挙値のビットごとの組み合わせ。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.Diagnostics.Tracing.EventSourceSettings,System.String[])">
      <summary>指定した設定と特徴が含まれるコントラクト以外のイベントで使用される <see cref="T:System.Diagnostics.Tracing.EventSource" /> の新しいインスタンスを初期化します。</summary>
      <param name="settings">イベント ソースに適用する構成設定を指定する列挙値のビットごとの組み合わせ。</param>
      <param name="traits">イベント ソースの特徴を指定するキー/値のペア。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="traits" /> is not specified in key-value pairs.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.String)">
      <summary>指定した名前を使用して、<see cref="T:System.Diagnostics.Tracing.EventSource" /> クラスの新しいインスタンスを作成します。</summary>
      <param name="eventSourceName">イベント ソースに適用する名前。null にすることはできません。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.String,System.Diagnostics.Tracing.EventSourceSettings)">
      <summary>指定した名前と設定を使用して、<see cref="T:System.Diagnostics.Tracing.EventSource" /> クラスの新しいインスタンスを作成します。</summary>
      <param name="eventSourceName">イベント ソースに適用する名前。null にすることはできません。</param>
      <param name="config">イベント ソースに適用する構成設定を指定する列挙値のビットごとの組み合わせ。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.String,System.Diagnostics.Tracing.EventSourceSettings,System.String[])">
      <summary>指定した構成設定を使用して <see cref="T:System.Diagnostics.Tracing.EventSource" /> クラスの新しいインスタンスを作成します。</summary>
      <param name="eventSourceName">イベント ソースに適用する名前。null にすることはできません。</param>
      <param name="config">イベント ソースに適用する構成設定を指定する列挙値のビットごとの組み合わせ。</param>
      <param name="traits">イベント ソースの特徴を指定するキー/値のペア。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="traits" /> is not specified in key-value pairs.</exception>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.ConstructionException">
      <summary>[.NET Framework 4.5.1 以上でサポート] イベント ソースの作成中にスローされた例外を取得します。</summary>
      <returns>イベント ソースの作成中にスローされた例外、または例外がスローされなかった場合は null。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.CurrentThreadActivityId">
      <summary>[.NET Framework 4.5.1 以上でサポート] 現在のスレッドのアクティビティ ID を取得します。</summary>
      <returns>現在のスレッドのアクティビティ ID。</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Dispose">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventSource" /> クラスの現在のインスタンスによって使用されているすべてのリソースを解放します。</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventSource" /> クラスによって使用されているアンマネージ リソースを解放し、オプションでマネージ リソースも解放します。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Finalize">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventSource" /> オブジェクトがガベージ コレクションによって収集される前に、そのオブジェクトがリソースを解放し、その他のクリーンアップ操作を実行できるようにします。</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GenerateManifest(System.Type,System.String)">
      <summary>現在のイベント ソースに関連付けられている XML マニフェストの文字列を返します。</summary>
      <returns>XML データ文字列。</returns>
      <param name="eventSourceType">イベント ソースの型。</param>
      <param name="assemblyPathToIncludeInManifest">マニフェストのプロバイダー要素に格納するアセンブリ ファイル (.dll) のパス。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GenerateManifest(System.Type,System.String,System.Diagnostics.Tracing.EventManifestOptions)">
      <summary>現在のイベント ソースに関連付けられている XML マニフェストの文字列を返します。</summary>
      <returns>XML データ文字列またはnull (「解説」を参照)。</returns>
      <param name="eventSourceType">イベント ソースの型。</param>
      <param name="assemblyPathToIncludeInManifest">マニフェストのプロバイダー要素に格納するアセンブリ ファイル (.dll) のパス。</param>
      <param name="flags">マニフェストの生成方法を示す列挙値のビットごとの組み合わせ。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetGuid(System.Type)">
      <summary>イベント ソースのこの実装の一意の識別子を取得します。</summary>
      <returns>このイベント ソース型の一意の識別子。</returns>
      <param name="eventSourceType">イベント ソースの型。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetName(System.Type)">
      <summary>イベント ソースのフレンドリ名を取得します。</summary>
      <returns>イベント ソースの表示名。既定値は、クラスの単純名です。</returns>
      <param name="eventSourceType">イベント ソースの型。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetSources">
      <summary>アプリケーション ドメインのすべてのイベント ソースのスナップショットを取得します。</summary>
      <returns>アプリケーション ドメインのすべてのイベント ソースの列挙。</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetTrait(System.String)">
      <summary>指定したキーに関連付けられている特徴値を取得します。</summary>
      <returns>指定のキーと関連付けられている特徴値。キーが見つからないと、null が戻ります。</returns>
      <param name="key">取得する特徴のキー。</param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.Guid">
      <summary>イベント ソースの一意の識別子。</summary>
      <returns>イベント ソースの一意の識別子。</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.IsEnabled">
      <summary>現在のイベント ソースが有効かどうかを判断します。</summary>
      <returns>現在のイベント ソースが有効の場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.IsEnabled(System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords)">
      <summary>指定したレベルとキーワードを持つ現在のイベント ソースが有効かどうかを判断します。</summary>
      <returns>イベント ソースが有効な場合は true。それ以外の場合は false。</returns>
      <param name="level">イベント ソースのレベル。</param>
      <param name="keywords">イベント ソースのキーワード。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.IsEnabled(System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords,System.Diagnostics.Tracing.EventChannel)">
      <summary>現在のイベント ソースが、指定したレベル、キーワード、およびチャネルを持つイベントに対して有効かどうかを判断します。</summary>
      <returns>指定のイベント レベル、キーワード、チャネルでイベント ソースが有効な場合には true。それ以外の場合は false。このメソッドの結果は、特定のイベントがアクティブかどうかの近似にすぎません。これを使用して、ログ記録が無効になっている場合の、ログ記録の高負荷な計算を回避します。イベント ソースは、アクティビティを決定する追加のフィルター処理を持っている場合があります。</returns>
      <param name="level">チェックするイベント レベル。イベント ソースは、そのレベルが <paramref name="level" /> 以上の場合に有効とみなされます。</param>
      <param name="keywords">チェックするイベント キーワード。</param>
      <param name="channel">チェックするイベント チャネル。</param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.Name">
      <summary>イベント ソースから派生するクラスの表示名。</summary>
      <returns>派生クラスの表示名。既定値は、クラスの単純名です。</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.OnEventCommand(System.Diagnostics.Tracing.EventCommandEventArgs)">
      <summary>現在のイベント ソースがコントローラーによって更新されるときに呼び出されます。</summary>
      <param name="command">イベントの引数。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.SendCommand(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventCommand,System.Collections.Generic.IDictionary{System.String,System.String})">
      <summary>指定されたイベント ソースにコマンドを送信します。</summary>
      <param name="eventSource">コマンドを送信する先のイベント ソース。</param>
      <param name="command">送信するイベント コマンド。</param>
      <param name="commandArguments">イベント コマンドの引数。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.SetCurrentThreadActivityId(System.Guid)">
      <summary>[.NET Framework 4.5.1 以上でサポート] 現在のスレッドのアクティビティ ID を設定します。</summary>
      <param name="activityId">現在のスレッドの新しいアクティビティ ID、または現在のスレッドの作業が任意のアクティビティに関連付けられていないことを示す <see cref="F:System.Guid.Empty" />。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.SetCurrentThreadActivityId(System.Guid,System.Guid@)">
      <summary>[.NET Framework 4.5.1 以上でサポート] 現在のスレッドでは、アクティビティ ID を設定し、前のアクティビティの ID を返します。</summary>
      <param name="activityId">現在のスレッドの新しいアクティビティ ID、または現在のスレッドの作業が任意のアクティビティに関連付けられていないことを示す <see cref="F:System.Guid.Empty" />。</param>
      <param name="oldActivityThatWillContinue">このメソッドが返されるとき、現在のスレッドの前のアクティビティの ID を含みます。</param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.Settings">
      <summary>このイベント ソースに適用される設定を取得します。</summary>
      <returns>このイベント ソースに適用される設定。</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.ToString">
      <summary>現在のイベント ソース インスタンスを文字列で表現したものを取得します。</summary>
      <returns>現在のイベント ソースを識別する、名前と一意の識別子。</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write(System.String)">
      <summary>フィールドを除いてイベントを書き込みます。ただし、指定した名前および既定のオプションを含みます。</summary>
      <param name="eventName">書き込むイベントの名前。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write(System.String,System.Diagnostics.Tracing.EventSourceOptions)">
      <summary>フィールドを除いてイベントを書き込みます。ただし、指定した名前およびオプションを含みます。</summary>
      <param name="eventName">書き込むイベントの名前。</param>
      <param name="options">イベントのレベル、キーワード、およびオペレーション コードなどのオプション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions,``0)">
      <summary>指定した名前、イベント データ、およびオプションを使用してイベントを書き込みます。</summary>
      <param name="eventName">イベントの名前です。</param>
      <param name="options">イベント オプション。</param>
      <param name="data">イベントのデータ。この型は匿名型であるか、<see cref="T:System.Diagnostics.Tracing.EventDataAttribute" /> 属性のマークが付いている必要があります。</param>
      <typeparam name="T">イベントとそれに関連するデータを定義する型。この型は匿名型であるか、<see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" /> 属性のマークが付いている必要があります。</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions@,System.Guid@,System.Guid@,``0@)">
      <summary>指定した名前、オプション、関連するアクティビティ、およびイベント データを使用してイベントを書き込みます。</summary>
      <param name="eventName">イベントの名前です。</param>
      <param name="options">イベント オプション。</param>
      <param name="activityId">イベントに関連付けられたアクティビティの ID。</param>
      <param name="relatedActivityId">関連付けられたアクティビティの ID。関連付けられたアクティビティがない場合は <see cref="F:System.Guid.Empty" />。</param>
      <param name="data">イベントのデータ。この型は匿名型であるか、<see cref="T:System.Diagnostics.Tracing.EventDataAttribute" /> 属性のマークが付いている必要があります。</param>
      <typeparam name="T">イベントとそれに関連するデータを定義する型。この型は匿名型であるか、<see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" /> 属性のマークが付いている必要があります。</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions@,``0@)">
      <summary>指定した名前、オプション、およびイベント データを使用してイベントを書き込みます。</summary>
      <param name="eventName">イベントの名前です。</param>
      <param name="options">イベント オプション。</param>
      <param name="data">イベントのデータ。この型は匿名型であるか、<see cref="T:System.Diagnostics.Tracing.EventDataAttribute" /> 属性のマークが付いている必要があります。</param>
      <typeparam name="T">イベントとそれに関連するデータを定義する型。この型は匿名型であるか、<see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" /> 属性のマークが付いている必要があります。</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,``0)">
      <summary>指定した名前とデータを使用してイベントを書き込みます。</summary>
      <param name="eventName">イベントの名前です。</param>
      <param name="data">イベントのデータ。この型は匿名型であるか、<see cref="T:System.Diagnostics.Tracing.EventDataAttribute" /> 属性のマークが付いている必要があります。</param>
      <typeparam name="T">イベントとそれに関連するデータを定義する型。この型は匿名型であるか、<see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" /> 属性のマークが付いている必要があります。</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32)">
      <summary>指定されたイベント識別子を使用して、イベントを書き込みます。</summary>
      <param name="eventId">イベント識別子。有効値の範囲は 0 ～ 65535 です。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Byte[])">
      <summary>指定されたイベント識別子とバイト配列引数を使用してイベントを書き込みます。</summary>
      <param name="eventId">イベント識別子。有効値の範囲は 0 ～ 65535 です。</param>
      <param name="arg1">バイト配列引数。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32)">
      <summary>指定されたイベント識別子と 32 ビット整数引数を使用して、イベントを書き込みます。</summary>
      <param name="eventId">イベント識別子。有効値の範囲は 0 ～ 65535 です。</param>
      <param name="arg1">整数引数。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32,System.Int32)">
      <summary>指定されたイベント識別子と 32 ビット整数引数を使用して、イベントを書き込みます。</summary>
      <param name="eventId">イベント識別子。有効値の範囲は 0 ～ 65535 です。</param>
      <param name="arg1">整数引数。</param>
      <param name="arg2">整数引数。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>指定されたイベント識別子と 32 ビット整数引数を使用して、イベントを書き込みます。</summary>
      <param name="eventId">イベント識別子。有効値の範囲は 0 ～ 65535 です。</param>
      <param name="arg1">整数引数。</param>
      <param name="arg2">整数引数。</param>
      <param name="arg3">整数引数。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32,System.String)">
      <summary>指定されたイベント識別子と 32 ビット整数および文字列引数を使用して、イベントを書き込みます。</summary>
      <param name="eventId">イベント識別子。有効値の範囲は 0 ～ 65535 です。</param>
      <param name="arg1">32 ビット整数引数。</param>
      <param name="arg2">文字列引数。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64)">
      <summary>指定されたイベント識別子と 64 ビット整数引数を使用して、イベントを書き込みます。</summary>
      <param name="eventId">イベント識別子。有効値の範囲は 0 ～ 65535 です。</param>
      <param name="arg1">64 ビット整数引数。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.Byte[])">
      <summary>指定した識別子と 64 ビット整数およびバイト配列引数を使用して、イベント データを書き込みます。</summary>
      <param name="eventId">イベント識別子。有効値の範囲は 0 ～ 65535 です。</param>
      <param name="arg1">64 ビット整数引数。</param>
      <param name="arg2">バイト配列引数。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.Int64)">
      <summary>指定されたイベント識別子と 64 ビットの引数を使用して、イベントを書き込みます。</summary>
      <param name="eventId">イベント識別子。有効値の範囲は 0 ～ 65535 です。</param>
      <param name="arg1">64 ビット整数引数。</param>
      <param name="arg2">64 ビット整数引数。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.Int64,System.Int64)">
      <summary>指定されたイベント識別子と 64 ビットの引数を使用して、イベントを書き込みます。</summary>
      <param name="eventId">イベント識別子。有効値の範囲は 0 ～ 65535 です。</param>
      <param name="arg1">64 ビット整数引数。</param>
      <param name="arg2">64 ビット整数引数。</param>
      <param name="arg3">64 ビット整数引数。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.String)">
      <summary>指定されたイベント識別子と 64 ビット整数および文字列引数を使用して、イベントを書き込みます。</summary>
      <param name="eventId">イベント識別子。有効値の範囲は 0 ～ 65535 です。</param>
      <param name="arg1">64 ビット整数引数。</param>
      <param name="arg2">文字列引数。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Object[])">
      <summary>指定されたイベント識別子と引数の配列を使用して、イベントを書き込みます。</summary>
      <param name="eventId">イベント識別子。有効値の範囲は 0 ～ 65535 です。</param>
      <param name="args">オブジェクトの配列。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String)">
      <summary>指定されたイベント識別子と文字列引数を使用して、イベントを書き込みます。</summary>
      <param name="eventId">イベント識別子。有効値の範囲は 0 ～ 65535 です。</param>
      <param name="arg1">文字列引数。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.Int32)">
      <summary>指定されたイベント識別子と引数を使用して、イベントを書き込みます。</summary>
      <param name="eventId">イベント識別子。有効値の範囲は 0 ～ 65535 です。</param>
      <param name="arg1">文字列引数。</param>
      <param name="arg2">32 ビット整数引数。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.Int32,System.Int32)">
      <summary>指定されたイベント識別子と引数を使用して、イベントを書き込みます。</summary>
      <param name="eventId">イベント識別子。有効値の範囲は 0 ～ 65535 です。</param>
      <param name="arg1">文字列引数。</param>
      <param name="arg2">32 ビット整数引数。</param>
      <param name="arg3">32 ビット整数引数。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.Int64)">
      <summary>指定されたイベント識別子と引数を使用して、イベントを書き込みます。</summary>
      <param name="eventId">イベント識別子。有効値の範囲は 0 ～ 65535 です。</param>
      <param name="arg1">文字列引数。</param>
      <param name="arg2">64 ビット整数引数。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.String)">
      <summary>指定されたイベント識別子と文字列引数を使用して、イベントを書き込みます。</summary>
      <param name="eventId">イベント識別子。有効値の範囲は 0 ～ 65535 です。</param>
      <param name="arg1">文字列引数。</param>
      <param name="arg2">文字列引数。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.String,System.String)">
      <summary>指定されたイベント識別子と文字列引数を使用して、イベントを書き込みます。</summary>
      <param name="eventId">イベント識別子。有効値の範囲は 0 ～ 65535 です。</param>
      <param name="arg1">文字列引数。</param>
      <param name="arg2">文字列引数。</param>
      <param name="arg3">文字列引数。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEventCore(System.Int32,System.Int32,System.Diagnostics.Tracing.EventSource.EventData*)">
      <summary>指定したイベント識別子およびイベント データを使用して、<see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" /> の新しいオーバーロードを作成します。</summary>
      <param name="eventId">イベント識別子。</param>
      <param name="eventDataCount">イベント データ項目数。</param>
      <param name="data">イベント データを格納している構造体。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEventWithRelatedActivityId(System.Int32,System.Guid,System.Object[])">
      <summary>[.NET Framework 4.5.1 以上でサポート] 現在のアクティビティが別のアクティビティに関連していることを示すイベントを書き込みます。</summary>
      <param name="eventId">
        <see cref="T:System.Diagnostics.Tracing.EventSource" /> のこのイベントを一意に識別する識別子。</param>
      <param name="relatedActivityId">関連するアクティビティ ID。</param>
      <param name="args">イベント データを格納するオブジェクトの配列。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEventWithRelatedActivityIdCore(System.Int32,System.Guid*,System.Int32,System.Diagnostics.Tracing.EventSource.EventData*)">
      <summary>[.NET Framework 4.5.1 以上でサポート] 現在のアクティビティが別のアクティビティに関連していることを示すイベントを書き込みます。</summary>
      <param name="eventId">
        <see cref="T:System.Diagnostics.Tracing.EventSource" /> のこのイベントを一意に識別する識別子。</param>
      <param name="relatedActivityId">関連するアクティビティ ID の GUID へのポインター。</param>
      <param name="eventDataCount">
        <paramref name="data" /> フィールド内の項目の数。</param>
      <param name="data">イベント データ フィールドの最初の項目へのポインター。</param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSource.EventData">
      <summary>
        <see cref="M:System.Diagnostics.Tracing.EventSource.WriteEventCore(System.Int32,System.Int32,System.Diagnostics.Tracing.EventSource.EventData*)" /> のメソッドを使用して <see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" /> の高速なオーバーロードを作成するようにイベント データを提供します。</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.EventData.DataPointer">
      <summary>新しい <see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" /> オーバーロードのデータへのポインターを取得または設定します。</summary>
      <returns>データへのポインター。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.EventData.Size">
      <summary>新しい <see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" /> オーバーロードのペイロード項目の数を取得または設定します。</summary>
      <returns>新しいオーバーロードのペイロードの項目数。</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSourceAttribute">
      <summary>Windows イベント トレーシング (ETW) の名前がイベント ソース クラスの名前とは関係なく定義されるようにします。</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceAttribute.#ctor">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceAttribute.Guid">
      <summary>イベント ソース識別子を取得または設定します。</summary>
      <returns>イベントのソース識別子。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceAttribute.LocalizationResources">
      <summary>ローカリゼーション リソース ファイルの名前を取得または設定します。</summary>
      <returns>ローカリゼーション リソース ファイル名。ローカリゼーション リソース ファイルが見つからない場合は、null。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceAttribute.Name">
      <summary>イベント ソースの名前を取得または設定します。</summary>
      <returns>イベント ソースの名前。</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSourceException">
      <summary>Windows イベント トレーシング (ETW) 中にエラーが発生するとスローされる例外。</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceException.#ctor">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventSourceException" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceException.#ctor(System.String)">
      <summary>指定したエラー メッセージを使用して、<see cref="T:System.Diagnostics.Tracing.EventSourceException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">エラーを説明するメッセージ。</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceException.#ctor(System.String,System.Exception)">
      <summary>指定したエラー メッセージと、この例外の原因である内部例外への参照を使用して、<see cref="T:System.Diagnostics.Tracing.EventSourceException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を説明するエラー メッセージ。</param>
      <param name="innerException">現在の例外の原因である例外。または、内部例外を指定しない場合は null。</param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSourceOptions">
      <summary>上書きを指定します。 既定のイベント設定など、ログ レベル、キーワード、および操作ときにコード、<see cref="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions,``0)" />メソッドが呼び出されます。</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.ActivityOptions"></member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Keywords">
      <summary>イベントに適用されるキーワードを取得または設定します。このプロパティが設定されていない場合、イベントのキーワードになりますNoneです。</summary>
      <returns>イベントに適用されるキーワードまたはNoneキーワードが設定されていない場合。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Level">
      <summary>取得またはイベントに適用されるイベントのレベルを設定します。</summary>
      <returns>イベントのイベント レベル。設定しない場合、既定値は Verbose (5) です。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Opcode">
      <summary>取得または設定に指定されたイベントを使用するには、操作コード。</summary>
      <returns>指定したイベントで使用するオペレーション コード。設定されていない、既定値はInfo(0) です。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Tags"></member>
    <member name="T:System.Diagnostics.Tracing.EventSourceSettings">
      <summary>イベント ソースの構成オプションを指定します。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.Default">
      <summary>有効になっている特別な構成オプションはありません。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.EtwManifestEventFormat">
      <summary>ETW リスナーはイベント発生時にマニフェストに基づく形式を使用する必要があります。このオプションの設定は、ETW リスナーがイベント発生時にマニフェストに基づく形式を使用する必要があることのディレクティブです。派生した型を定義するときに、これは、既定のオプション<see cref="T:System.Diagnostics.Tracing.EventSource" />、保護対象のいずれかを使用して<see cref="T:System.Diagnostics.Tracing.EventSource" />コンス トラクターです。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.EtwSelfDescribingEventFormat">
      <summary>ETW リスナーは自己記述型のイベント形式を使用する必要があります。これは、既定のオプションの新しいインスタンスを作成するときに、 <see cref="T:System.Diagnostics.Tracing.EventSource" /> 、パブリックのいずれかを使用して<see cref="T:System.Diagnostics.Tracing.EventSource" />コンス トラクターです。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.ThrowOnEventWriteErrors">
      <summary>エラーが発生すると、イベント ソースは例外をスローします。</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventTags">
      <summary>アクティビティの開始イベントおよび停止イベントの追跡を指定します。使用するのは下位 24 ビットのみでなければなりません。詳細については、<see cref="T:System.Diagnostics.Tracing.EventSourceOptions" /> および <see cref="M:System.Diagnostics.Tracing.EventSource.Write(System.String,System.Diagnostics.Tracing.EventSourceOptions)" /> を参照してください。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventTags.None">
      <summary>タグがないこと、そして 0 に等しいことを指定します。</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventTask">
      <summary>イベントに適用されるタスクを定義します。</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventTask.None">
      <summary>未定義タスク。</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventWrittenEventArgs">
      <summary>
        <see cref="M:System.Diagnostics.Tracing.EventListener.OnEventWritten(System.Diagnostics.Tracing.EventWrittenEventArgs)" /> コールバックのデータを提供します。</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.ActivityId">
      <summary>[.NET Framework 4.5.1 以上でサポート] イベントが書き込まれたスレッドのアクティビティ ID を取得します。</summary>
      <returns>イベントが書き込まれたスレッドのアクティビティ ID。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Channel">
      <summary>イベントのチャネルを取得します。</summary>
      <returns>イベントのチャネル。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.EventId">
      <summary>イベント識別子を取得します。</summary>
      <returns>イベント識別子。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.EventName">
      <summary>イベントの名前を取得します。</summary>
      <returns>イベントの名前です。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.EventSource">
      <summary>イベント ソース オブジェクトを取得します。</summary>
      <returns>イベント ソース オブジェクト。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Keywords">
      <summary>イベントのキーワードを取得します。</summary>
      <returns>イベントのキーワード。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Level">
      <summary>イベントのレベルを取得します。</summary>
      <returns>イベントのレベル。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Message">
      <summary>イベントに関するメッセージを取得します。</summary>
      <returns>イベントに関するメッセージ。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Opcode">
      <summary>イベントのオペレーション コードを取得します。</summary>
      <returns>イベントのオペレーション コード。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Payload">
      <summary>イベントのペイロードを取得します。</summary>
      <returns>イベントのペイロード。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.PayloadNames">
      <summary>イベントのプロパティ名を表す文字列の一覧を返します。</summary>
      <returns>
        <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> を返します。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.RelatedActivityId">
      <summary>[.NET Framework 4.5.1 以上でサポート] 現在のインスタンスによって表されるアクティビティに関連付けられているアクティビティの ID を取得します。</summary>
      <returns>関連するアクティビティの識別子。または、関連するアクティビティがない場合は <see cref="F:System.Guid.Empty" />。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Tags">
      <summary>
        <see cref="M:System.Diagnostics.Tracing.EventSource.Write(System.String,System.Diagnostics.Tracing.EventSourceOptions)" /> メソッドへの呼び出しで指定されたタグを返します。</summary>
      <returns>
        <see cref="T:System.Diagnostics.Tracing.EventTags" /> を返します。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Task">
      <summary>イベントのタスクを取得します。</summary>
      <returns>イベントのタスク。</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Version">
      <summary>イベントのバージョンを取得します。</summary>
      <returns>イベントのバージョン。</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.NonEventAttribute">
      <summary>イベントを生成していないメソッドを識別します。</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.NonEventAttribute.#ctor">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.NonEventAttribute" /> クラスの新しいインスタンスを作成します。</summary>
    </member>
  </members>
</doc>