﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Diagnostics.Tracing</name>
  </assembly>
  <members>
    <member name="T:System.Diagnostics.Tracing.EventActivityOptions">
      <summary>Specifies the tracking of activity start and stop events. </summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.Detachable">
      <summary>Allow overlapping activities. By default, activity starts and stops must be property nested. That is, a sequence of Start A, Start B, Stop A, Stop B is not allowed will result in B stopping at the same time as A.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.Disable">
      <summary>Turn off start and stop tracking. </summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.None">
      <summary>Use the default behavior for start and stop tracking.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.Recursive">
      <summary>Allow recursive activity starts. By default, an activity cannot be recursive. That is, a sequence of Start A, Start A, Stop A, Stop A is not allowed. Unintentional recursive activities can occur if the app executes and for some the stop is not reached before another start is called. </summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventAttribute">
      <summary>Specifies additional event schema information for an event.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventAttribute.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.Tracing.EventAttribute" /> class with the specified event identifier.</summary>
      <param name="eventId">The event identifier for the event.</param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.ActivityOptions">
      <summary>Specifies the behavior of the start and stop events of an activity. An activity is the region of time in an app between the start and the stop.</summary>
      <returns>Returns <see cref="T:System.Diagnostics.Tracing.EventActivityOptions" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Channel">
      <summary>Gets or sets an additional event log where the event should be written.</summary>
      <returns>An additional event log where the event should be written.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.EventId">
      <summary>Gets or sets the identifier for the event.</summary>
      <returns>The event identifier. This value should be between 0 and 65535.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Keywords">
      <summary>Gets or sets the keywords for the event.</summary>
      <returns>A bitwise combination of the enumeration values.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Level">
      <summary>Gets or sets the level for the event.</summary>
      <returns>One of the enumeration values that specifies the level for the event.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Message">
      <summary>Gets or sets the message for the event.</summary>
      <returns>The message for the event.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Opcode">
      <summary>Gets or sets the operation code for the event.</summary>
      <returns>One of the enumeration values that specifies the operation code.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Tags">
      <summary>Gets and sets the <see cref="T:System.Diagnostics.Tracing.EventTags" /> value for this <see cref="T:System.Diagnostics.Tracing.EventAttribute" /> object. An event tag is a user-defined value that is passed through when the event is logged. </summary>
      <returns>Returns the <see cref="T:System.Diagnostics.Tracing.EventTags" /> value.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Task">
      <summary>Gets or sets the task for the event.</summary>
      <returns>The task for the event.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Version">
      <summary>Gets or sets the version of the event.</summary>
      <returns>The version of the event.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventChannel">
      <summary>Specifies the event log channel for the event.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Admin">
      <summary>The administrator log channel.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Analytic">
      <summary>The analytic channel.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Debug">
      <summary>The debug channel.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.None">
      <summary>No channel specified.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Operational">
      <summary>The operational channel. </summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventCommand">
      <summary>Describes the command (<see cref="P:System.Diagnostics.Tracing.EventCommandEventArgs.Command" /> property) that is passed to the <see cref="M:System.Diagnostics.Tracing.EventSource.OnEventCommand(System.Diagnostics.Tracing.EventCommandEventArgs)" /> callback.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.Disable">
      <summary>Disable the event.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.Enable">
      <summary>Enable the event.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.SendManifest">
      <summary>Send the manifest.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.Update">
      <summary>Update the event.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventCommandEventArgs">
      <summary>Provides the arguments for the <see cref="M:System.Diagnostics.Tracing.EventSource.OnEventCommand(System.Diagnostics.Tracing.EventCommandEventArgs)" /> callback.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventCommandEventArgs.Arguments">
      <summary>Gets the array of arguments for the callback.</summary>
      <returns>An array of callback arguments.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventCommandEventArgs.Command">
      <summary>Gets the command for the callback.</summary>
      <returns>The callback command.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventCommandEventArgs.DisableEvent(System.Int32)">
      <summary>Disables the event that have the specified identifier.</summary>
      <returns>true if <paramref name="eventId" /> is in range; otherwise, false.</returns>
      <param name="eventId">The identifier of the event to disable.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventCommandEventArgs.EnableEvent(System.Int32)">
      <summary>Enables the event that has the specified identifier.</summary>
      <returns>true if <paramref name="eventId" /> is in range; otherwise, false.</returns>
      <param name="eventId">The identifier of the event to enable.</param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventDataAttribute">
      <summary>Specifies a type to be passed to the <see cref="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions,``0)" /> method.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventDataAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" /> class. </summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventDataAttribute.Name">
      <summary>Gets or set the name to apply to an event if the event type or property is not explicitly named.</summary>
      <returns>The name to apply to the event or property.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventFieldAttribute">
      <summary>The <see cref="T:System.Diagnostics.Tracing.EventFieldAttribute" /> is placed on fields of user-defined types that are passed as <see cref="T:System.Diagnostics.Tracing.EventSource" /> payloads. </summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventFieldAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.Tracing.EventFieldAttribute" /> class.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventFieldAttribute.Format">
      <summary>Gets and sets the value that specifies how to format the value of a user-defined type.</summary>
      <returns>Returns a<see cref="T:System.Diagnostics.Tracing.EventFieldFormat" /> value.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventFieldAttribute.Tags">
      <summary>Gets and sets the user-defined <see cref="T:System.Diagnostics.Tracing.EventFieldTags" /> value that is required for fields that contain data that isn't one of the supported types. </summary>
      <returns>Returns <see cref="T:System.Diagnostics.Tracing.EventFieldTags" />.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventFieldFormat">
      <summary>Specifies how to format the value of a user-defined type and can be used to override the default formatting for a field.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Boolean">
      <summary>Boolean</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Default">
      <summary>Default.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Hexadecimal">
      <summary>Hexadecimal.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.HResult">
      <summary>HResult.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Json">
      <summary>JSON.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.String">
      <summary>String.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Xml">
      <summary>XML.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventFieldTags">
      <summary>Specifies the user-defined tag that is placed on fields of user-defined types that are passed as <see cref="T:System.Diagnostics.Tracing.EventSource" /> payloads through the <see cref="T:System.Diagnostics.Tracing.EventFieldAttribute" />. </summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldTags.None">
      <summary>Specifies no tag and is equal to zero.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventIgnoreAttribute">
      <summary>Specifies a property should be ignored when writing an event type with the <see cref="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions@,``0@)" /> method.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventIgnoreAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.Tracing.EventIgnoreAttribute" /> class.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventKeywords">
      <summary>Defines the standard keywords that apply to events.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.All">
      <summary>All the bits are set to 1, representing every possible group of events.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.AuditFailure">
      <summary>Attached to all failed security audit events. Use this keyword only  for events in the security log.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.AuditSuccess">
      <summary>Attached to all successful security audit events. Use this keyword only for events in the security log.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.CorrelationHint">
      <summary>Attached to transfer events where the related activity ID (correlation ID) is a computed value and is not guaranteed to be unique (that is, it is not a real GUID).</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.EventLogClassic">
      <summary>Attached to events that are raised by using the RaiseEvent function.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.None">
      <summary>No filtering on keywords is performed when the event is published.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.Sqm">
      <summary>Attached to all Service Quality Mechanism (SQM) events.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.WdiContext">
      <summary>Attached to all Windows Diagnostics Infrastructure (WDI) context events.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.WdiDiagnostic">
      <summary>Attached to all Windows Diagnostics Infrastructure (WDI) diagnostic events.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventLevel">
      <summary>Identifies the level of an event.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Critical">
      <summary>This level corresponds to a critical error, which is a serious error that has caused a major failure.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Error">
      <summary>This level adds standard errors that signify a problem.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Informational">
      <summary>This level adds informational events or messages that are not errors. These events can help trace the progress or state of an application.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.LogAlways">
      <summary>No level filtering is done on the event.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Verbose">
      <summary>This level adds lengthy events or messages. It causes all events to be logged.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Warning">
      <summary>This level adds warning events (for example, events that are published because a disk is nearing full capacity).</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventListener">
      <summary>Provides methods for enabling and disabling events from event sources.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.Diagnostics.Tracing.EventListener" /> class.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.DisableEvents(System.Diagnostics.Tracing.EventSource)">
      <summary>Disables all events for the specified event source.</summary>
      <param name="eventSource">The event source to disable events for.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.Dispose">
      <summary>Releases the resources used by the current instance of the <see cref="T:System.Diagnostics.Tracing.EventListener" /> class.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EnableEvents(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventLevel)">
      <summary>Enables events for the specified event source that has the specified verbosity level or lower.</summary>
      <param name="eventSource">The event source to enable events for.</param>
      <param name="level">The level of events to enable.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EnableEvents(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords)">
      <summary>Enables events for the specified event source that has the specified verbosity level or lower, and matching keyword flags.</summary>
      <param name="eventSource">The event source to enable events for.</param>
      <param name="level">The level of events to enable.</param>
      <param name="matchAnyKeyword">The keyword flags necessary to enable the events.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EnableEvents(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords,System.Collections.Generic.IDictionary{System.String,System.String})">
      <summary>Enables events for the specified event source that has the specified verbosity level or lower, matching event keyword flag, and matching arguments.</summary>
      <param name="eventSource">The event source to enable events for.</param>
      <param name="level">The level of events to enable.</param>
      <param name="matchAnyKeyword">The keyword flags necessary to enable the events.</param>
      <param name="arguments">The arguments to be matched to enable the events.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EventSourceIndex(System.Diagnostics.Tracing.EventSource)">
      <summary>Gets a small non-negative number that represents the specified event source.</summary>
      <returns>A small non-negative number that represents the specified event source.</returns>
      <param name="eventSource">The event source to find the index for.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.OnEventSourceCreated(System.Diagnostics.Tracing.EventSource)">
      <summary>Called for all existing event sources when the event listener is created and when a new event source is attached to the listener.</summary>
      <param name="eventSource">The event source.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.OnEventWritten(System.Diagnostics.Tracing.EventWrittenEventArgs)">
      <summary>Called whenever an event has been written by an event source for which the event listener has enabled events.</summary>
      <param name="eventData">The event arguments that describe the event.</param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventManifestOptions">
      <summary>Specifies how the ETW manifest for the event source is generated.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.AllCultures">
      <summary>Generates a resources node under the localization folder for every satellite assembly provided.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.AllowEventSourceOverride">
      <summary>Overrides the default behavior that the current <see cref="T:System.Diagnostics.Tracing.EventSource" /> must be the base class of the user-defined type passed to the write method. This enables the validation of .NET event sources.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.None">
      <summary>No options are specified.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.OnlyIfNeededForRegistration">
      <summary>A manifest is generated only the event source must be registered on the host computer.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.Strict">
      <summary>Causes an exception to be raised if any inconsistencies occur when writing the manifest file.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventOpcode">
      <summary>Defines the standard operation codes that the event source attaches to events.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.DataCollectionStart">
      <summary>A trace collection start event.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.DataCollectionStop">
      <summary>A trace collection stop event.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Extension">
      <summary>An extension event.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Info">
      <summary>An informational event.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Receive">
      <summary>An event that is published when one activity in an application receives data.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Reply">
      <summary>An event that is published after an activity in an application replies to an event.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Resume">
      <summary>An event that is published after an activity in an application resumes from a suspended state. The event should follow an event that has the <see cref="F:System.Diagnostics.Tracing.EventOpcode.Suspend" /> operation code.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Send">
      <summary>An event that is published when one activity in an application transfers data or system resources to another activity.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Start">
      <summary>An event that is published when an application starts a new transaction or activity. This operation code can be embedded within another transaction or activity when multiple events that have the <see cref="F:System.Diagnostics.Tracing.EventOpcode.Start" /> code follow each other without an intervening event that has a <see cref="F:System.Diagnostics.Tracing.EventOpcode.Stop" /> code.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Stop">
      <summary>An event that is published when an activity or a transaction in an application ends. The event corresponds to the last unpaired event that has a <see cref="F:System.Diagnostics.Tracing.EventOpcode.Start" /> operation code.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Suspend">
      <summary>An event that is published when an activity in an application is suspended.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSource">
      <summary>Provides the ability to create events for event tracing for Windows (ETW).</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.Diagnostics.Tracing.EventSource" /> class.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.Boolean)">
      <summary>Creates a new instance of the <see cref="T:System.Diagnostics.Tracing.EventSource" /> class and specifies whether to throw an exception when an error occurs in the underlying Windows code.</summary>
      <param name="throwOnEventWriteErrors">true to throw an exception when an error occurs in the underlying Windows code; otherwise, false.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.Diagnostics.Tracing.EventSourceSettings)">
      <summary>Creates a new instance of the <see cref="T:System.Diagnostics.Tracing.EventSource" /> class with the specified configuration settings.</summary>
      <param name="settings">A bitwise combination of the enumeration values that specify the configuration settings to apply to the event source.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.Diagnostics.Tracing.EventSourceSettings,System.String[])">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.Tracing.EventSource" /> to be used with non-contract events that contains the specified settings and traits.</summary>
      <param name="settings">A bitwise combination of the enumeration values that specify the configuration settings to apply to the event source.</param>
      <param name="traits">The key-value pairs that specify traits for the event source.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="traits" /> is not specified in key-value pairs.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.String)">
      <summary>Creates a new instance of the <see cref="T:System.Diagnostics.Tracing.EventSource" /> class with the specified name.</summary>
      <param name="eventSourceName">The name to apply to the event source. Must not be null.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.String,System.Diagnostics.Tracing.EventSourceSettings)">
      <summary>Creates a new instance of the <see cref="T:System.Diagnostics.Tracing.EventSource" /> class with the specified name and settings.</summary>
      <param name="eventSourceName">The name to apply to the event source. Must not be null.</param>
      <param name="config">A bitwise combination of the enumeration values that specify the configuration settings to apply to the event source.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.String,System.Diagnostics.Tracing.EventSourceSettings,System.String[])">
      <summary>Creates a new instance of the <see cref="T:System.Diagnostics.Tracing.EventSource" /> class with the specified configuration settings.</summary>
      <param name="eventSourceName">The name to apply to the event source. Must not be null.</param>
      <param name="config">A bitwise combination of the enumeration values that specify the configuration settings to apply to the event source.</param>
      <param name="traits">The key-value pairs that specify traits for the event source.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="traits" /> is not specified in key-value pairs.</exception>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.ConstructionException">
      <summary>[Supported in the .NET Framework 4.5.1 and later versions] Gets any exception that was thrown during the construction of the event source.</summary>
      <returns>The exception that was thrown during the construction of the event source, or null if no exception was thrown. </returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.CurrentThreadActivityId">
      <summary>[Supported in the .NET Framework 4.5.1 and later versions] Gets the activity ID of the current thread. </summary>
      <returns>The activity ID of the current thread. </returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.Diagnostics.Tracing.EventSource" /> class.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Diagnostics.Tracing.EventSource" /> class and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Finalize">
      <summary>Allows the <see cref="T:System.Diagnostics.Tracing.EventSource" /> object to attempt to free resources and perform other cleanup operations before the  object is reclaimed by garbage collection.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GenerateManifest(System.Type,System.String)">
      <summary>Returns a string of the XML manifest that is associated with the current event source.</summary>
      <returns>The XML data string.</returns>
      <param name="eventSourceType">The type of the event source.</param>
      <param name="assemblyPathToIncludeInManifest">The path to the assembly file (.dll) to include in the provider element of the manifest. </param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GenerateManifest(System.Type,System.String,System.Diagnostics.Tracing.EventManifestOptions)">
      <summary>Returns a string of the XML manifest that is associated with the current event source.</summary>
      <returns>The XML data string or null (see remarks).</returns>
      <param name="eventSourceType">The type of the event source.</param>
      <param name="assemblyPathToIncludeInManifest">The path to the assembly file (.dll) file to include in the provider element of the manifest. </param>
      <param name="flags">A bitwise combination of the enumeration values that specify how the manifest is generated.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetGuid(System.Type)">
      <summary>Gets the unique identifier for this implementation of the event source.</summary>
      <returns>A unique identifier for this event source type.</returns>
      <param name="eventSourceType">The type of the event source.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetName(System.Type)">
      <summary>Gets the friendly name of the event source.</summary>
      <returns>The friendly name of the event source. The default is the simple name of the class.</returns>
      <param name="eventSourceType">The type of the event source.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetSources">
      <summary>Gets a snapshot of all the event sources for the application domain.</summary>
      <returns>An enumeration of all the event sources in the application domain.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetTrait(System.String)">
      <summary>Gets the trait value associated with the specified key.</summary>
      <returns>The trait value associated with the specified key. If the key is not found, returns null.</returns>
      <param name="key">The key of the trait to get.</param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.Guid">
      <summary>The unique identifier for the event source.</summary>
      <returns>A unique identifier for the event source.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.IsEnabled">
      <summary>Determines whether the current event source is enabled.</summary>
      <returns>true if the current event source is enabled; otherwise, false.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.IsEnabled(System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords)">
      <summary>Determines whether the current event source that has the specified level and keyword is enabled.</summary>
      <returns>true if the event source is enabled; otherwise, false.</returns>
      <param name="level">The level of the event source.</param>
      <param name="keywords">The keyword of the event source.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.IsEnabled(System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords,System.Diagnostics.Tracing.EventChannel)">
      <summary>Determines whether the current event source is enabled for events with the specified level, keywords and channel.</summary>
      <returns>true if the event source is enabled for the specified event level, keywords and channel; otherwise, false.The result of this method is only an approximation of whether a particular event is active.  Use it to avoid expensive computation for logging when logging is disabled.   Event sources may have additional filtering that determines their activity..</returns>
      <param name="level">The event level to check. An event source will be considered enabled when its level is greater than or equal to <paramref name="level" />.</param>
      <param name="keywords">The event keywords to check.</param>
      <param name="channel">The event channel to check.</param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.Name">
      <summary>The friendly name of the class that is derived from the event source.</summary>
      <returns>The friendly name of the derived class.  The default is the simple name of the class.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.OnEventCommand(System.Diagnostics.Tracing.EventCommandEventArgs)">
      <summary>Called when the current event source is updated by the controller.</summary>
      <param name="command">The arguments for the event.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.SendCommand(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventCommand,System.Collections.Generic.IDictionary{System.String,System.String})">
      <summary>Sends a command to a specified event source.</summary>
      <param name="eventSource">The event source to send the command to.</param>
      <param name="command">The event command to send.</param>
      <param name="commandArguments">The arguments for the event command.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.SetCurrentThreadActivityId(System.Guid)">
      <summary>[Supported in the .NET Framework 4.5.1 and later versions] Sets the activity ID on the current thread.</summary>
      <param name="activityId">The current thread's new activity ID, or <see cref="F:System.Guid.Empty" /> to indicate that work on the current thread is not associated with any activity. </param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.SetCurrentThreadActivityId(System.Guid,System.Guid@)">
      <summary>[Supported in the .NET Framework 4.5.1 and later versions] Sets the activity ID on the current thread, and returns the previous activity ID.</summary>
      <param name="activityId">The current thread's new activity ID, or <see cref="F:System.Guid.Empty" /> to indicate that work on the current thread is not associated with any activity.</param>
      <param name="oldActivityThatWillContinue">When this method returns, contains the previous activity ID on the current thread. </param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.Settings">
      <summary>Gets the settings applied to this event source.</summary>
      <returns>The settings applied to this event source.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.ToString">
      <summary>Obtains a string representation of the current event source instance.</summary>
      <returns>The name and unique identifier that identify the current event source.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write(System.String)">
      <summary>Writes an event without fields, but with the specified name and default options.</summary>
      <param name="eventName">The name of the event to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write(System.String,System.Diagnostics.Tracing.EventSourceOptions)">
      <summary>Writes an event without fields, but with the specified name and options.</summary>
      <param name="eventName">The name of the event to write.</param>
      <param name="options">The options such as level, keywords and operation code for the event.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions,``0)">
      <summary>Writes an event with the specified name, event data and options.</summary>
      <param name="eventName">The name of the event.</param>
      <param name="options">The event options.</param>
      <param name="data">The event data. This type must be an anonymous type or marked with the <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" /> attribute.</param>
      <typeparam name="T">The type that defines the event and its associated data. This type must be an anonymous type or marked with the <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" /> attribute.</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions@,System.Guid@,System.Guid@,``0@)">
      <summary>Writes an event with the specified name, options, related activity and event data.</summary>
      <param name="eventName">The name of the event.</param>
      <param name="options">The event options.</param>
      <param name="activityId">The ID of the activity associated with the event.</param>
      <param name="relatedActivityId">The ID of an associated activity, or <see cref="F:System.Guid.Empty" /> if there is no associated activity.</param>
      <param name="data">The event data. This type must be an anonymous type or marked with the <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" /> attribute.</param>
      <typeparam name="T">The type that defines the event and its associated data. This type must be an anonymous type or marked with the <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" /> attribute.</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions@,``0@)">
      <summary>Writes an event with the specified name, options and event data.</summary>
      <param name="eventName">The name of the event.</param>
      <param name="options">The event options.</param>
      <param name="data">The event data. This type must be an anonymous type or marked with the <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" /> attribute.</param>
      <typeparam name="T">The type that defines the event and its associated data. This type must be an anonymous type or marked with the <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" /> attribute.</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,``0)">
      <summary>Writes an event with the specified name and data.</summary>
      <param name="eventName">The name of the event.</param>
      <param name="data">The event data. This type must be an anonymous type or marked with the <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" /> attribute.</param>
      <typeparam name="T">The type that defines the event and its associated data. This type must be an anonymous type or marked with the <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" /> attribute.</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32)">
      <summary>Writes an event by using the provided event identifier.</summary>
      <param name="eventId">The event identifier. This value should be between 0 and 65535.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Byte[])">
      <summary>Writes an event by using the provided event identifier and byte array argument.</summary>
      <param name="eventId">The event identifier.  This value should be between 0 and 65535.</param>
      <param name="arg1">A byte array argument.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32)">
      <summary>Writes an event by using the provided event identifier and 32-bit integer argument.</summary>
      <param name="eventId">The event identifier.  This value should be between 0 and 65535.</param>
      <param name="arg1">An integer argument.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32,System.Int32)">
      <summary>Writes an event by using the provided event identifier and 32-bit integer arguments.</summary>
      <param name="eventId">The event identifier.  This value should be between 0 and 65535.</param>
      <param name="arg1">An integer argument.</param>
      <param name="arg2">An integer argument.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>Writes an event by using the provided event identifier and 32-bit integer arguments.</summary>
      <param name="eventId">The event identifier.  This value should be between 0 and 65535.</param>
      <param name="arg1">An integer argument.</param>
      <param name="arg2">An integer argument.</param>
      <param name="arg3">An integer argument.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32,System.String)">
      <summary>Writes an event by using the provided event identifier and 32-bit integer and string arguments.</summary>
      <param name="eventId">The event identifier. This value should be between 0 and 65535.</param>
      <param name="arg1">A 32-bit integer argument.</param>
      <param name="arg2">A string argument.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64)">
      <summary>Writes an event by using the provided event identifier and 64-bit integer argument.</summary>
      <param name="eventId">The event identifier.  This value should be between 0 and 65535.</param>
      <param name="arg1">A 64 bit integer argument.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.Byte[])">
      <summary>Writes the event data using the specified indentifier and 64-bit integer and byte array arguments.</summary>
      <param name="eventId">The event identifier.  This value should be between 0 and 65535.</param>
      <param name="arg1">A 64-bit integer argument.</param>
      <param name="arg2">A byte array argument.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.Int64)">
      <summary>Writes an event by using the provided event identifier and 64-bit arguments.</summary>
      <param name="eventId">The event identifier.  This value should be between 0 and 65535.</param>
      <param name="arg1">A 64 bit integer argument.</param>
      <param name="arg2">A 64 bit integer argument.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.Int64,System.Int64)">
      <summary>Writes an event by using the provided event identifier and 64-bit arguments.</summary>
      <param name="eventId">The event identifier.  This value should be between 0 and 65535.</param>
      <param name="arg1">A 64 bit integer argument.</param>
      <param name="arg2">A 64 bit integer argument.</param>
      <param name="arg3">A 64 bit integer argument.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.String)">
      <summary>Writes an event by using the provided event identifier and 64-bit integer, and string arguments.</summary>
      <param name="eventId">The event identifier.  This value should be between 0 and 65535.</param>
      <param name="arg1">A 64-bit integer argument.</param>
      <param name="arg2">A string argument.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Object[])">
      <summary>Writes an event by using the provided event identifier and array of arguments.</summary>
      <param name="eventId">The event identifier.  This value should be between 0 and 65535.</param>
      <param name="args">An array of objects.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String)">
      <summary>Writes an event by using the provided event identifier and string argument.</summary>
      <param name="eventId">The event identifier.  This value should be between 0 and 65535.</param>
      <param name="arg1">A string argument.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.Int32)">
      <summary>Writes an event by using the provided event identifier and arguments.</summary>
      <param name="eventId">The event identifier.  This value should be between 0 and 65535.</param>
      <param name="arg1">A string argument.</param>
      <param name="arg2">A 32 bit integer argument.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.Int32,System.Int32)">
      <summary>Writes an event by using the provided event identifier and arguments.</summary>
      <param name="eventId">The event identifier.  This value should be between 0 and 65535.</param>
      <param name="arg1">A string argument.</param>
      <param name="arg2">A 32 bit integer argument.</param>
      <param name="arg3">A 32 bit integer argument.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.Int64)">
      <summary>Writes an event by using the provided event identifier and arguments.</summary>
      <param name="eventId">The event identifier.  This value should be between 0 and 65535.</param>
      <param name="arg1">A string argument.</param>
      <param name="arg2">A 64 bit integer argument.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.String)">
      <summary>Writes an event by using the provided event identifier and string arguments.</summary>
      <param name="eventId">The event identifier.  This value should be between 0 and 65535.</param>
      <param name="arg1">A string argument.</param>
      <param name="arg2">A string argument.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.String,System.String)">
      <summary>Writes an event by using the provided event identifier and string arguments.</summary>
      <param name="eventId">The event identifier.  This value should be between 0 and 65535.</param>
      <param name="arg1">A string argument.</param>
      <param name="arg2">A string argument.</param>
      <param name="arg3">A string argument.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEventCore(System.Int32,System.Int32,System.Diagnostics.Tracing.EventSource.EventData*)">
      <summary>Creates a new <see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" /> overload by using the provided event identifier and event data.</summary>
      <param name="eventId">The event identifier.</param>
      <param name="eventDataCount">The number of event data items.</param>
      <param name="data">The structure that contains the event data.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEventWithRelatedActivityId(System.Int32,System.Guid,System.Object[])">
      <summary>[Supported in the .NET Framework 4.5.1 and later versions] Writes an event that indicates that the current activity is related to another activity. </summary>
      <param name="eventId">An identifier that uniquely identifies this event within the <see cref="T:System.Diagnostics.Tracing.EventSource" />. </param>
      <param name="relatedActivityId">The related activity identifier. </param>
      <param name="args">An array of objects that contain data about the event. </param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEventWithRelatedActivityIdCore(System.Int32,System.Guid*,System.Int32,System.Diagnostics.Tracing.EventSource.EventData*)">
      <summary>[Supported in the .NET Framework 4.5.1 and later versions] Writes an event that indicates that the current activity is related to another activity.</summary>
      <param name="eventId">An identifier that uniquely identifies this event within the <see cref="T:System.Diagnostics.Tracing.EventSource" />.</param>
      <param name="relatedActivityId">A pointer to the GUID of the related activity ID. </param>
      <param name="eventDataCount">The number of items in the <paramref name="data" /> field. </param>
      <param name="data">A pointer to the first item in the event data field. </param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSource.EventData">
      <summary>Provides the event data for creating fast <see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" /> overloads by using the <see cref="M:System.Diagnostics.Tracing.EventSource.WriteEventCore(System.Int32,System.Int32,System.Diagnostics.Tracing.EventSource.EventData*)" /> method.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.EventData.DataPointer">
      <summary>Gets or sets the pointer to the data for the new <see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" /> overload.</summary>
      <returns>The pointer to the data.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.EventData.Size">
      <summary>Gets or sets the number of payload items in the new <see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" /> overload.</summary>
      <returns>The number of payload items in the new overload.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSourceAttribute">
      <summary>Allows the event tracing for Windows (ETW) name to be defined independently of the name of the event source class.   </summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" /> class.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceAttribute.Guid">
      <summary>Gets or sets the event source identifier.</summary>
      <returns>The event source identifier.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceAttribute.LocalizationResources">
      <summary>Gets or sets the name of the localization resource file.</summary>
      <returns>The name of the localization resource file, or null if the localization resource file does not exist.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceAttribute.Name">
      <summary>Gets or sets the name of the event source.</summary>
      <returns>The name of the event source.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSourceException">
      <summary>The exception that is thrown when an error occurs during event tracing for Windows (ETW).</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.Tracing.EventSourceException" /> class.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.Tracing.EventSourceException" /> class with a specified error message.</summary>
      <param name="message">The message that describes the error.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.Tracing.EventSourceException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The error message that explains the reason for the exception. </param>
      <param name="innerException">The exception that is the cause of the current exception, or null if no inner exception is specified. </param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSourceOptions">
      <summary>Specifies overrides of default event settings such as the log level, keywords and operation code when the <see cref="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions,``0)" /> method is called.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.ActivityOptions">
      <summary>The activity options defined for this event source.</summary>
      <returns>Returns <see cref="T:System.Diagnostics.Tracing.EventActivityOptions" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Keywords">
      <summary>Gets or sets the keywords applied to the event. If this property is not set, the event’s keywords will be None.</summary>
      <returns>The keywords applied to the event, or None if no keywords are set.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Level">
      <summary>Gets or sets the event level applied to the event. </summary>
      <returns>The event level for the event. If not set, the default is Verbose (5).</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Opcode">
      <summary>Gets or sets the operation code to use for the specified event. </summary>
      <returns>The operation code to use for the specified event. If not set, the default is Info (0).</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Tags">
      <summary>The event tags defined for this event source.</summary>
      <returns>Returns <see cref="T:System.Diagnostics.Tracing.EventTags" />.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSourceSettings">
      <summary>Specifies configuration options for an event source.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.Default">
      <summary>None of the special configuration options are enabled.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.EtwManifestEventFormat">
      <summary>The ETW listener should use a manifest-based format when raising events. Setting this option is a directive to the ETW listener should use manifest-based format when raising events. This is the default option when defining a type derived from <see cref="T:System.Diagnostics.Tracing.EventSource" /> using one of the protected <see cref="T:System.Diagnostics.Tracing.EventSource" /> constructors.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.EtwSelfDescribingEventFormat">
      <summary>The ETW listener should use self-describing event format. This is the default option when creating a new instance of the <see cref="T:System.Diagnostics.Tracing.EventSource" /> using one of the public <see cref="T:System.Diagnostics.Tracing.EventSource" /> constructors. </summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.ThrowOnEventWriteErrors">
      <summary>The event source throws an exception when an error occurs. </summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventTags">
      <summary>Specifies the tracking of activity start and stop events. You should only use the lower 24 bits. For more information, see <see cref="T:System.Diagnostics.Tracing.EventSourceOptions" /> and <see cref="M:System.Diagnostics.Tracing.EventSource.Write(System.String,System.Diagnostics.Tracing.EventSourceOptions)" />.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventTags.None">
      <summary>Specifies no tag and is equal to zero.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventTask">
      <summary>Defines the tasks that apply to events.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventTask.None">
      <summary>Undefined task.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventWrittenEventArgs">
      <summary>Provides data for the <see cref="M:System.Diagnostics.Tracing.EventListener.OnEventWritten(System.Diagnostics.Tracing.EventWrittenEventArgs)" /> callback.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.ActivityId">
      <summary>[Supported in the .NET Framework 4.5.1 and later versions] Gets the activity ID on the thread that the event was written to. </summary>
      <returns>The activity ID on the thread that the event was written to. </returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Channel">
      <summary>Gets the channel for the event.</summary>
      <returns>The channel for the event.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.EventId">
      <summary>Gets the event identifier.</summary>
      <returns>The event identifier.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.EventName">
      <summary>Gets the name of the event.</summary>
      <returns>The name of the event.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.EventSource">
      <summary>Gets the event source object.</summary>
      <returns>The event source object.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Keywords">
      <summary>Gets the keywords for the event.</summary>
      <returns>The keywords for the event.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Level">
      <summary>Gets the level of the event.</summary>
      <returns>The level of the event.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Message">
      <summary>Gets the message for the event.</summary>
      <returns>The message for the event.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Opcode">
      <summary>Gets the operation code for the event.</summary>
      <returns>The operation code for the event.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Payload">
      <summary>Gets the payload for the event.</summary>
      <returns>The payload for the event.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.PayloadNames">
      <summary>Returns a list of strings that represent the property names of the event.</summary>
      <returns>Returns <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.RelatedActivityId">
      <summary>[Supported in the .NET Framework 4.5.1 and later versions] Gets the identifier of an activity that is related to the activity represented by the current instance. </summary>
      <returns>The identifier of the related activity, or <see cref="F:System.Guid.Empty" /> if there is no related activity.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Tags">
      <summary>Returns the tags specified in the call to the <see cref="M:System.Diagnostics.Tracing.EventSource.Write(System.String,System.Diagnostics.Tracing.EventSourceOptions)" /> method.</summary>
      <returns>Returns <see cref="T:System.Diagnostics.Tracing.EventTags" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Task">
      <summary>Gets the task for the event.</summary>
      <returns>The task for the event.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Version">
      <summary>Gets the version of the event.</summary>
      <returns>The version of the event.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.NonEventAttribute">
      <summary>Identifies a method that is not generating an event.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.NonEventAttribute.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.Diagnostics.Tracing.NonEventAttribute" /> class.</summary>
    </member>
  </members>
</doc>