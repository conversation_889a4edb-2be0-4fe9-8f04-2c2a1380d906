﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Diagnostics.Tracing</name>
  </assembly>
  <members>
    <member name="T:System.Diagnostics.Tracing.EventActivityOptions">
      <summary>추적 지정 활동의 시작 및 이벤트를 중지 합니다. </summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.Detachable">
      <summary>중복되는 활동을 허용합니다.기본적으로 활동 시작 및 중지는 중첩 속성이어야 합니다.즉, 허용되지 않는 시작 A, 시작 B, 중지 A, 중지 B의 시퀀스에 따라 B가 A와 동시에 중지됩니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.Disable">
      <summary>시작 해제 및 추적을 중지 합니다. </summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.None">
      <summary>시작 및 중지 추적의 기본 동작을 사용합니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.Recursive">
      <summary>재귀 활동 시작을 허용합니다.기본적으로 활동은 재귀적일 수 없습니다.즉, 시작 A, 시작 A, 중지 A, 중지 A의 시퀀스가 허용되지 않습니다.앱이 실행되고 다른 시작이 호출되기 전에 일부 중지에 도달하지 않은 경우 의도하지 않은 재귀 활동이 발생할 수 있습니다.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventAttribute">
      <summary>이벤트에 대한 추가 이벤트 스키마 정보를 지정합니다.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventAttribute.#ctor(System.Int32)">
      <summary>지정된 이벤트 식별자를 사용하여 <see cref="T:System.Diagnostics.Tracing.EventAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="eventId">이벤트의 이벤트 식별자입니다.</param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.ActivityOptions">
      <summary>활동의 시작 및 중지 이벤트 동작을 지정합니다.활동은 앱에서 시작과 중지 간의 시간 영역입니다.</summary>
      <returns>
        <see cref="T:System.Diagnostics.Tracing.EventActivityOptions" />를 반환합니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Channel">
      <summary>이벤트를 작성해야 하는 추가 이벤트 로그를 가져오거나 설정합니다.</summary>
      <returns>이벤트를 작성해야 하는 추가 이벤트 로그입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.EventId">
      <summary>이벤트의 식별자를 가져오거나 설정합니다.</summary>
      <returns>이벤트 식별자입니다.이 값은 0에서 65535 사이여야 합니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Keywords">
      <summary>이벤트에 대한 키워드를 가져오거나 설정합니다.</summary>
      <returns>열거형 값의 비트 조합입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Level">
      <summary>이벤트의 수준을 가져오거나 설정합니다.</summary>
      <returns>이벤트 수준을 지정하는 열거형 값 중 하나입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Message">
      <summary>이벤트에 대한 메시지를 가져오거나 설정합니다.</summary>
      <returns>이벤트의 메시지입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Opcode">
      <summary>이벤트의 작업 코드를 가져오거나 설정합니다.</summary>
      <returns>작업 코드를 지정하는 열거형 값 중 하나입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Tags">
      <summary>가져오고 설정의 <see cref="T:System.Diagnostics.Tracing.EventTags" /> 이 대 한 값 <see cref="T:System.Diagnostics.Tracing.EventAttribute" /> 개체입니다.이벤트 태그는 이벤트가 기록될 때 전달되는 사용자 정의 값입니다.</summary>
      <returns>
        <see cref="T:System.Diagnostics.Tracing.EventTags" /> 값을 반환합니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Task">
      <summary>이벤트의 작업을 가져오거나 설정합니다.</summary>
      <returns>이벤트에 대한 작업입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Version">
      <summary>이벤트 버전을 가져오거나 설정합니다.</summary>
      <returns>이벤트의 버전입니다.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventChannel">
      <summary>이벤트에 대한 이벤트 로그 채널을 지정합니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Admin">
      <summary>관리자 로그 채널입니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Analytic">
      <summary>분석 채널입니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Debug">
      <summary>디버그 채널입니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.None">
      <summary>지정된 채널이 없습니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Operational">
      <summary>운영 채널입니다. </summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventCommand">
      <summary>
        <see cref="M:System.Diagnostics.Tracing.EventSource.OnEventCommand(System.Diagnostics.Tracing.EventCommandEventArgs)" /> 콜백에 전달되는 명령(<see cref="P:System.Diagnostics.Tracing.EventCommandEventArgs.Command" /> 속성)에 대해 설명합니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.Disable">
      <summary>이벤트를 사용하지 않도록 설정합니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.Enable">
      <summary>이벤트를 사용하도록 설정합니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.SendManifest">
      <summary>매니페스트를 보냅니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.Update">
      <summary>이벤트를 업데이트합니다.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventCommandEventArgs">
      <summary>
        <see cref="M:System.Diagnostics.Tracing.EventSource.OnEventCommand(System.Diagnostics.Tracing.EventCommandEventArgs)" /> 콜백의 인수를 제공합니다.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventCommandEventArgs.Arguments">
      <summary>콜백에 대한 인수 배열을 가져옵니다.</summary>
      <returns>콜백 인수의 배열입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventCommandEventArgs.Command">
      <summary>콜백 명령을 가져옵니다.</summary>
      <returns>콜백 명령입니다.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventCommandEventArgs.DisableEvent(System.Int32)">
      <summary>지정된 식별자가 있는 이벤트를 사용하지 않도록 설정합니다.</summary>
      <returns>
        <paramref name="eventId" />가 범위에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="eventId">사용하지 않도록 설정할 이벤트의 식별자입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventCommandEventArgs.EnableEvent(System.Int32)">
      <summary>지정된 식별자가 있는 이벤트를 사용하도록 설정합니다.</summary>
      <returns>
        <paramref name="eventId" />가 범위에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="eventId">사용하도록 설정할 이벤트의 식별자입니다.</param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventDataAttribute">
      <summary>에 전달 될 형식을 지정 된 <see cref="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions,``0)" /> 메서드.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventDataAttribute.#ctor">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventDataAttribute.Name">
      <summary>이벤트 형식 또는 속성이 명시적으로 명명될 경우 이벤트에 적용할 이름을 가져오거나 설정합니다.</summary>
      <returns>이벤트 또는 속성에 적용할 이름입니다.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventFieldAttribute">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventFieldAttribute" /> 로 전달 되는 사용자 정의 형식의 필드에 배치 됩니다 <see cref="T:System.Diagnostics.Tracing.EventSource" /> 페이로드입니다. </summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventFieldAttribute.#ctor">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventFieldAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventFieldAttribute.Format">
      <summary>사용자 정의 형식의 값 형식을 지정하는 방법을 지정하는 값을 가져오고 설정합니다.</summary>
      <returns>
        <see cref="T:System.Diagnostics.Tracing.EventFieldFormat" /> 값을 반환합니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventFieldAttribute.Tags">
      <summary>사용자 정의 가져오거나 설정 합니다 <see cref="T:System.Diagnostics.Tracing.EventFieldTags" /> 지원 되는 유형 중 하나에 없는 데이터를 포함 하는 필드에 필요한 값입니다. </summary>
      <returns>
        <see cref="T:System.Diagnostics.Tracing.EventFieldTags" />를 반환합니다.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventFieldFormat">
      <summary>사용자 정의 형식 값의 형식을 지정하는 방법을 지정하고 특정 필드에 대한 기본값을 재정의하는 데 사용될 수 있습니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Boolean">
      <summary>Boolean</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Default">
      <summary>기본.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Hexadecimal">
      <summary>16진수</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.HResult">
      <summary>HResult</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Json">
      <summary>JSON</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.String">
      <summary>문자열.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Xml">
      <summary>XML</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventFieldTags">
      <summary>로 전달 되는 사용자 정의 형식의 필드에 배치 되는 사용자 정의 태그 지정 <see cref="T:System.Diagnostics.Tracing.EventSource" /> 통해 페이로드는 <see cref="T:System.Diagnostics.Tracing.EventFieldAttribute" />. </summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldTags.None">
      <summary>태그 없음을 지정하며 0입니다.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventIgnoreAttribute">
      <summary>포함 하는 이벤트 형식 작성할 때 속성을 무시 하도록 지정 된 <see cref="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions@,``0@)" /> 메서드.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventIgnoreAttribute.#ctor">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventIgnoreAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventKeywords">
      <summary>이벤트에 적용되는 표준 키워드를 정의합니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.All">
      <summary>모든 비트가 1로 설정되면서 이벤트의 가능한 모든 그룹을 나타냅니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.AuditFailure">
      <summary>모든 실패한 보안 감사 이벤트에 연결됩니다.이 키워드는 보안 로그의 이벤트에 사용합니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.AuditSuccess">
      <summary>모든 성공적인 보안 감사 이벤트에 연결됩니다.이 키워드는 보안 로그의 이벤트에 사용합니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.CorrelationHint">
      <summary>관련 작업 ID(상관 관계 ID)가 계산된 값이고 실제 GUID가 아니므로 고유하지 않을 수 있는 경우 전송 이벤트에 연결됩니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.EventLogClassic">
      <summary>RaiseEvent 함수를 사용하여 발생한 이벤트에 연결됩니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.None">
      <summary>이벤트를 게시할 때 키워드에 따라 필터링하지 않습니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.Sqm">
      <summary>모든 SQM(Service Quality Mechanism) 이벤트에 연결됩니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.WdiContext">
      <summary>모든 WDI(Windows Diagnostic Infrastructure) 컨텍스트 이벤트에 연결됩니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.WdiDiagnostic">
      <summary>모든 WDI(Windows Diagnostic Infrastructure) 진단 이벤트에 연결됩니다.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventLevel">
      <summary>이벤트의 수준을 식별합니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Critical">
      <summary>이 수준은 중대한 작업 실패를 일으킨 심각한 오류에 해당합니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Error">
      <summary>이 수준은 문제를 알리는 표준 오류를 추가합니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Informational">
      <summary>이 수준은 오류가 아닌 정보 이벤트 또는 메시지를 추가합니다.이러한 이벤트를 통해 응용 프로그램의 진행률이나 상태를 추적할 수 있습니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.LogAlways">
      <summary>이벤트에서 수준 필터링을 수행하지 않습니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Verbose">
      <summary>이 수준은 길이가 긴 이벤트 또는 메시지를 추가합니다.모든 이벤트가 기록되도록 합니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Warning">
      <summary>이 수준은 경고 이벤트를 추가합니다(예: 디스크 용량이 거의 꽉 찰 때 게시되는 이벤트).</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventListener">
      <summary>이벤트 소스에서 이벤트를 활성화 및 비활성화하기 위한 메서드를 제공합니다.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.#ctor">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventListener" /> 클래스의 새 인스턴스를 만듭니다.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.DisableEvents(System.Diagnostics.Tracing.EventSource)">
      <summary>지정된 이벤트 소스에 대한 모든 이벤트를 사용하지 않도록 설정합니다.</summary>
      <param name="eventSource">이벤트를 사용하지 않도록 설정하는 이벤트 소스입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.Dispose">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventListener" /> 클래스의 현재 인스턴스에서 사용하는 리소스를 해제합니다.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EnableEvents(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventLevel)">
      <summary>지정된 자세한 정도 수준 이하인 지정된 이벤트 소스에 대한 이벤트를 사용하도록 설정합니다.</summary>
      <param name="eventSource">이벤트를 사용하도록 설정하는 이벤트 소스입니다.</param>
      <param name="level">사용할 수 있는 이벤트 수준입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EnableEvents(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords)">
      <summary>지정된 자세한 정도 수준 이하이고 일치하는 키워드 플래그가 있는 지정된 이벤트 소스에 대한 이벤트를 사용하도록 설정합니다.</summary>
      <param name="eventSource">이벤트를 사용하도록 설정하는 이벤트 소스입니다.</param>
      <param name="level">사용할 수 있는 이벤트 수준입니다.</param>
      <param name="matchAnyKeyword">이벤트를 사용하는 데 필요한 키워드 플래그입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EnableEvents(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords,System.Collections.Generic.IDictionary{System.String,System.String})">
      <summary>지정된 자세한 정도 수준 이하이고 일치하는 이벤트 키워드 플래그 및 일치하는 인수가 있는 지정된 이벤트 소스에 대한 이벤트를 사용하도록 설정합니다.</summary>
      <param name="eventSource">이벤트를 사용하도록 설정하는 이벤트 소스입니다.</param>
      <param name="level">사용할 수 있는 이벤트 수준입니다.</param>
      <param name="matchAnyKeyword">이벤트를 사용하는 데 필요한 키워드 플래그입니다.</param>
      <param name="arguments">이벤트를 사용하기 위해 일치해야 하는 인수입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EventSourceIndex(System.Diagnostics.Tracing.EventSource)">
      <summary>지정된 이벤트 소스를 나타내는 음수가 아닌 작은 숫자를 가져옵니다.</summary>
      <returns>지정된 이벤트 소스를 나타내는 음수가 아닌 작은 숫자입니다.</returns>
      <param name="eventSource">인덱스를 찾을 이벤트 소스입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.OnEventSourceCreated(System.Diagnostics.Tracing.EventSource)">
      <summary>이벤트 수신기가 만들어질 때와 새 이벤트 소스가 수신기에 연결될 때 모든 기존 이벤트 소스에 대해 호출됩니다.</summary>
      <param name="eventSource">이벤트 소스입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.OnEventWritten(System.Diagnostics.Tracing.EventWrittenEventArgs)">
      <summary>이벤트 수신기가 이벤트를 설정한 이벤트 소스에 의해 이벤트가 작성될 때마다 호출됩니다.</summary>
      <param name="eventData">이벤트를 설명하는 이벤트 인수입니다.</param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventManifestOptions">
      <summary>이벤트 소스에 대한 ETW 메니페이스트를 생성하는 방법을 지정합니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.AllCultures">
      <summary>제공된 모든 위성 어셈블리에 대한 지역화 폴더에 리소스 노드를 생성합니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.AllowEventSourceOverride">
      <summary>기본 동작을 재정의 하는 현재 <see cref="T:System.Diagnostics.Tracing.EventSource" /> 사용자 정의 형식의 기본 클래스에 전달 해야 write 메서드.이를 통해 .NET 이벤트 소스의 유효성 검사를 수행할 수 있습니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.None">
      <summary>지정된 옵션이 없습니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.OnlyIfNeededForRegistration">
      <summary>호스트 컴퓨터에 등록해야 하는 이벤트 소스에만 메니페스트가 생성됩니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.Strict">
      <summary>메니페스트 파일을 쓸 때 불일치가 일어날 경우에 예외가 발생하도록 합니다.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventOpcode">
      <summary>이벤트 소스가 이벤트에 추가하는 표준 작업 코드를 정의합니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.DataCollectionStart">
      <summary>추적 컬렉션 시작 이벤트입니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.DataCollectionStop">
      <summary>추적 컬렉션 중지 이벤트입니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Extension">
      <summary>확장 이벤트입니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Info">
      <summary>정보 이벤트로,</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Receive">
      <summary>응용 프로그램의 작업 중 하나가 데이터를 받을 때 게시되는 이벤트입니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Reply">
      <summary>응용 프로그램의 작업이 이벤트에 응답한 후 게시되는 이벤트입니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Resume">
      <summary>응용 프로그램에서 일시 중단된 작업이 다시 시작될 때 게시되는 이벤트입니다.이벤트는 <see cref="F:System.Diagnostics.Tracing.EventOpcode.Suspend" /> 작업 코드가 있는 이벤트 다음에 나와야 합니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Send">
      <summary>응용 프로그램의 작업 중 하나에서 데이터 또는 시스템 리소스를 다른 작업에 전송할 때 게시되는 이벤트입니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Start">
      <summary>응용 프로그램에서 새 트랜잭션 또는 작업을 시작할 때 게시되는 이벤트입니다.<see cref="F:System.Diagnostics.Tracing.EventOpcode.Stop" /> 코드가 있는 이벤트를 방해하지 않으면서 <see cref="F:System.Diagnostics.Tracing.EventOpcode.Start" /> 코드가 있는 여러 이벤트가 서로를 따르는 경우에는 이 작업 코드를 다른 트랜잭션이나 동작 안에 포함할 수 있습니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Stop">
      <summary>응용 프로그램에서 작업 또는 트랜잭션이 종료될 때 게시되는 이벤트입니다.이벤트는 <see cref="F:System.Diagnostics.Tracing.EventOpcode.Start" /> 작업 코드가 있는 이벤트 중 짝이 없는 마지막 이벤트에 대응됩니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Suspend">
      <summary>응용 프로그램의 작업이 일시 중지될 때 게시되는 이벤트입니다.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSource">
      <summary>ETW(Windows용 이벤트 추적) 이벤트를 만들 수 있는 기능을 제공합니다.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventSource" /> 클래스의 새 인스턴스를 만듭니다.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.Boolean)">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventSource" /> 클래스의 새 인스턴스를 만들고 기본 Windows 코드에서 오류가 발생할 때 예외를 throw할지 여부를 지정합니다.</summary>
      <param name="throwOnEventWriteErrors">기본 Windows 코드에서 오류가 발생할 때 예외를 throw하려면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.Diagnostics.Tracing.EventSourceSettings)">
      <summary>지정된 구성 설정을 사용하여 <see cref="T:System.Diagnostics.Tracing.EventSource" /> 클래스의 새 인스턴스를 만듭니다.</summary>
      <param name="settings">구성 설정을 지정하여 이벤트 소스에 적용하는 열거형 값의 비트 조합입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.Diagnostics.Tracing.EventSourceSettings,System.String[])">
      <summary>지정된 설정 및 특성을 포함하는 비 계약 이벤트와 함께 사용되는 <see cref="T:System.Diagnostics.Tracing.EventSource" />의 새 인스턴스를 초기화합니다.</summary>
      <param name="settings">구성 설정을 지정하여 이벤트 소스에 적용하는 열거형 값의 비트 조합입니다.</param>
      <param name="traits">이벤트 원본에 대한 특성을 지정하는 키-값 쌍입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="traits" /> is not specified in key-value pairs.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.String)">
      <summary>지정된 이름을 사용하여 <see cref="T:System.Diagnostics.Tracing.EventSource" /> 클래스의 새 인스턴스를 만듭니다.</summary>
      <param name="eventSourceName">이벤트 소스에 적용할 이름입니다.null이 아니어야 합니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.String,System.Diagnostics.Tracing.EventSourceSettings)">
      <summary>지정된 이름 및 설정을 사용하여 <see cref="T:System.Diagnostics.Tracing.EventSource" /> 클래스의 새 인스턴스를 만듭니다.</summary>
      <param name="eventSourceName">이벤트 소스에 적용할 이름입니다.null이 아니어야 합니다.</param>
      <param name="config">구성 설정을 지정하여 이벤트 소스에 적용하는 열거형 값의 비트 조합입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.String,System.Diagnostics.Tracing.EventSourceSettings,System.String[])">
      <summary>지정된 구성 설정을 사용하여 <see cref="T:System.Diagnostics.Tracing.EventSource" /> 클래스의 새 인스턴스를 만듭니다.</summary>
      <param name="eventSourceName">이벤트 소스에 적용할 이름입니다.null이 아니어야 합니다.</param>
      <param name="config">구성 설정을 지정하여 이벤트 소스에 적용하는 열거형 값의 비트 조합입니다.</param>
      <param name="traits">이벤트 원본에 대한 특성을 지정하는 키-값 쌍입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="traits" /> is not specified in key-value pairs.</exception>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.ConstructionException">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 이벤트 소스 생성 중에 throw된 모든 예외를 가져옵니다.</summary>
      <returns>이벤트 소스 생성 중에 throw된 예외 또는 예외가 throw되지 않은 경우 null입니다. </returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.CurrentThreadActivityId">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 현재 스레드의 작업 ID를 가져옵니다. </summary>
      <returns>현재 스레드의 작업 ID입니다. </returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Dispose">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventSource" /> 클래스의 현재 인스턴스에서 사용하는 모든 리소스를 해제합니다.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventSource" /> 클래스에 사용되는 관리되지 않는 리소스를 해제하고, 필요에 따라 관리되는 리소스를 해제합니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다. </param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Finalize">
      <summary>가비지 수집기에서 개체 회수하기 전에 <see cref="T:System.Diagnostics.Tracing.EventSource" /> 개체에서 리소스를 해제하고 다른 정리 작업을 수행할 수 있게 합니다.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GenerateManifest(System.Type,System.String)">
      <summary>현재 이벤트 소스와 연결된 XML 매니페스트의 문자열을 반환합니다.</summary>
      <returns>XML 데이터 문자열입니다.</returns>
      <param name="eventSourceType">이벤트 소스의 형식입니다.</param>
      <param name="assemblyPathToIncludeInManifest">매니페스트의  공급자 요소에 포함될 어셈블리 (.dll) 파일 경로입니다. </param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GenerateManifest(System.Type,System.String,System.Diagnostics.Tracing.EventManifestOptions)">
      <summary>현재 이벤트 소스와 연결된 XML 매니페스트의 문자열을 반환합니다.</summary>
      <returns>XML 데이터 문자열 또는 null(설명 참조)입니다.</returns>
      <param name="eventSourceType">이벤트 소스의 형식입니다.</param>
      <param name="assemblyPathToIncludeInManifest">매니페스트의  공급자 요소에 포함될 어셈블리(.dll) 파일의 경로입니다. </param>
      <param name="flags">메니페스트가 생성되는 방법을 지정하는 열거형 값의 비트 조합입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetGuid(System.Type)">
      <summary>이 이벤트 소스 구현에 대한 고유 식별자를 가져옵니다.</summary>
      <returns>이 이벤트 소스 유형에 대한 고유 식별자입니다.</returns>
      <param name="eventSourceType">이벤트 소스의 형식입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetName(System.Type)">
      <summary>이벤트 소스의 이름을 가져옵니다.</summary>
      <returns>이벤트 소스의 이름입니다.기본값은 클래스의 단순한 이름입니다.</returns>
      <param name="eventSourceType">이벤트 소스의 형식입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetSources">
      <summary>응용 프로그램 도메인에 대한 모든 이벤트 소스의 스냅숏을 가져옵니다.</summary>
      <returns>응용 프로그램 도메인에 있는 모든 이벤트 소스의 열거형입니다.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetTrait(System.String)">
      <summary>지정한 키와 연결된 특성 값을 가져옵니다.</summary>
      <returns>지정한 키와 연결된 특성 값입니다.키를 찾을 수 없으면 null을 반환합니다.</returns>
      <param name="key">가져올 특성의 키입니다.</param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.Guid">
      <summary>이벤트 소스에 대한 고유 식별자입니다.</summary>
      <returns>이벤트 소스에 대한 고유 식별자입니다.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.IsEnabled">
      <summary>현재 이벤트 소스를 사용할 수 있는지 여부를 확인합니다.</summary>
      <returns>현재 이벤트 소스를 사용할 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.IsEnabled(System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords)">
      <summary>지정된 수준 및 키워드가 있는 현재 이벤트 소스를 사용할 수 있는지 여부를 확인합니다.</summary>
      <returns>이벤트 소스를 사용할 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="level">이벤트 소스의 수준입니다.</param>
      <param name="keywords">이벤트 소스의 키워드입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.IsEnabled(System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords,System.Diagnostics.Tracing.EventChannel)">
      <summary>지정된 수준, 키워드 및 채널의 이벤트에 현재 이벤트 소스를 사용할 수 있는지 여부를 결정합니다.</summary>
      <returns>지정된 이벤트 수준, 키워드 및 채널에 이벤트 원본을 사용하도록 설정하려면 true이고, 그렇지 않으면 false입니다.이 메서드의 결과는 특정 이벤트가 활성 상태인지 여부에 대한 근사값만으로 나타납니다.이를 통해 로깅이 비활성화된 경우 비용이 많이 드는 계산을 피합니다.이벤트 소스에는 해당 활동을 확인하는 추가 필터링이 있을 수 있습니다.</returns>
      <param name="level">확인할 이벤트 수준입니다.해당 수준이 <paramref name="level" /> 이상일 때 사용하도록 고려할 이벤트 소스입니다.</param>
      <param name="keywords">확인할 이벤트 키워드입니다.</param>
      <param name="channel">확인할 이벤트 채널입니다.</param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.Name">
      <summary>이벤트 소스에서 파생되는 클래스의 이름입니다.</summary>
      <returns>파생된 클래스의 이름입니다.기본값은 클래스의 단순한 이름입니다.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.OnEventCommand(System.Diagnostics.Tracing.EventCommandEventArgs)">
      <summary>현재 이벤트 소스가 컨트롤러에 의해 업데이트되면 호출됩니다.</summary>
      <param name="command">이벤트에 대한 인수입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.SendCommand(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventCommand,System.Collections.Generic.IDictionary{System.String,System.String})">
      <summary>명령을 지정된 이벤트 소스로 보냅니다.</summary>
      <param name="eventSource">명령을 보낼 이벤트 소스입니다.</param>
      <param name="command">보낼 이벤트 명령입니다.</param>
      <param name="commandArguments">이벤트 명령에 대한 인수입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.SetCurrentThreadActivityId(System.Guid)">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 현재 스레드의 작업 ID를 설정합니다.</summary>
      <param name="activityId">현재 스레드의 새 작업 ID 또는 현재 스레드에 대한 작업이 다른 작업과 연결되지 않았음을 나타내는 <see cref="F:System.Guid.Empty" />입니다. </param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.SetCurrentThreadActivityId(System.Guid,System.Guid@)">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 현재 스레드에서 작업 ID를 설정하고 이전 작업 ID를 반환합니다.</summary>
      <param name="activityId">현재 스레드의 새 작업 ID 또는 현재 스레드에 대한 작업이 다른 작업과 연결되지 않았음을 나타내는 <see cref="F:System.Guid.Empty" />입니다.</param>
      <param name="oldActivityThatWillContinue">이 메서드가 결과를 반환할 때는 현재 스레드의 이전 작업 ID가 포함됩니다. </param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.Settings">
      <summary>이 이벤트 소스에 적용된 설정을 가져옵니다.</summary>
      <returns>이 이벤트 소스에 적용된 설정입니다.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.ToString">
      <summary>현재 이벤트 소스 인스턴스에 대한 문자열 표현을 가져옵니다.</summary>
      <returns>현재 이벤트 소스를 식별하는 이름 및 고유 식별자입니다.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write(System.String)">
      <summary>필드는 없지만 지정된 이름 및 기본 옵션이 있는 이벤트를 씁니다.</summary>
      <param name="eventName">쓸 이벤트의 이름입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write(System.String,System.Diagnostics.Tracing.EventSourceOptions)">
      <summary>필드는 없지만 지정된 이름 및 옵션이 있는 이벤트를 씁니다.</summary>
      <param name="eventName">쓸 이벤트의 이름입니다.</param>
      <param name="options">레벨, 키워드, 이벤트의 작업 코드 등의 옵션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions,``0)">
      <summary>지정된 이름, 이벤트 데이터 및 옵션이 있는 이벤트를 씁니다.</summary>
      <param name="eventName">이벤트의 이름입니다.</param>
      <param name="options">이벤트 옵션입니다.</param>
      <param name="data">이벤트 데이터입니다.이 형식은 익명 형식이거나 <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" /> 특성으로 표시되어야 합니다.</param>
      <typeparam name="T">이벤트 및 해당 연결된 데이터를 정의하는 형식입니다.이 형식은 익명 형식이거나 <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" /> 특성으로 표시되어야 합니다.</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions@,System.Guid@,System.Guid@,``0@)">
      <summary>지정된 이름, 옵션, 관련 활동 및 이벤트 데이터가 있는 이벤트를 씁니다.</summary>
      <param name="eventName">이벤트의 이름입니다.</param>
      <param name="options">이벤트 옵션입니다.</param>
      <param name="activityId">이벤트와 연결된 활동의 ID입니다.</param>
      <param name="relatedActivityId">연결된 활동의 ID이거나 연결된 활동이 없으면 <see cref="F:System.Guid.Empty" />입니다.</param>
      <param name="data">이벤트 데이터입니다.이 형식은 익명 형식이거나 <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" /> 특성으로 표시되어야 합니다.</param>
      <typeparam name="T">이벤트 및 해당 연결된 데이터를 정의하는 형식입니다.이 형식은 익명 형식이거나 <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" /> 특성으로 표시되어야 합니다.</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions@,``0@)">
      <summary>지정된 이름, 옵션 및 이벤트 데이터가 있는 이벤트를 씁니다.</summary>
      <param name="eventName">이벤트의 이름입니다.</param>
      <param name="options">이벤트 옵션입니다.</param>
      <param name="data">이벤트 데이터입니다.이 형식은 익명 형식이거나 <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" /> 특성으로 표시되어야 합니다.</param>
      <typeparam name="T">이벤트 및 해당 연결된 데이터를 정의하는 형식입니다.이 형식은 익명 형식이거나 <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" /> 특성으로 표시되어야 합니다.</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,``0)">
      <summary>지정된 이름과 데이터가 있는 이벤트를 씁니다.</summary>
      <param name="eventName">이벤트의 이름입니다.</param>
      <param name="data">이벤트 데이터입니다.이 형식은 익명 형식이거나 <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" /> 특성으로 표시되어야 합니다.</param>
      <typeparam name="T">이벤트 및 해당 연결된 데이터를 정의하는 형식입니다.이 형식은 익명 형식이거나 <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" /> 특성으로 표시되어야 합니다.</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32)">
      <summary>제공된 이벤트 식별자를 사용하여 이벤트를 씁니다.</summary>
      <param name="eventId">이벤트 식별자입니다.이 값은 0에서 65535 사이여야 합니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Byte[])">
      <summary>제공된 이벤트 식별자와 바이트 배열 인수를 사용하여 이벤트를 씁니다.</summary>
      <param name="eventId">이벤트 식별자입니다.이 값은 0에서 65535 사이여야 합니다.</param>
      <param name="arg1">바이트 배열 인수입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32)">
      <summary>제공된 이벤트 식별자와 32비트 정수 인수를 사용하여 이벤트를 씁니다.</summary>
      <param name="eventId">이벤트 식별자입니다.이 값은 0에서 65535 사이여야 합니다.</param>
      <param name="arg1">정수 인수입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32,System.Int32)">
      <summary>제공된 이벤트 식별자와 32비트 정수 인수를 사용하여 이벤트를 씁니다.</summary>
      <param name="eventId">이벤트 식별자입니다.이 값은 0에서 65535 사이여야 합니다.</param>
      <param name="arg1">정수 인수입니다.</param>
      <param name="arg2">정수 인수입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>제공된 이벤트 식별자와 32비트 정수 인수를 사용하여 이벤트를 씁니다.</summary>
      <param name="eventId">이벤트 식별자입니다.이 값은 0에서 65535 사이여야 합니다.</param>
      <param name="arg1">정수 인수입니다.</param>
      <param name="arg2">정수 인수입니다.</param>
      <param name="arg3">정수 인수입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32,System.String)">
      <summary>제공된 이벤트 식별자와 32비트 정수 및 문자열 인수를 사용하여 이벤트를 씁니다.</summary>
      <param name="eventId">이벤트 식별자입니다.이 값은 0에서 65535 사이여야 합니다.</param>
      <param name="arg1">32비트 정수 인수입니다.</param>
      <param name="arg2">문자열 인수입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64)">
      <summary>제공된 이벤트 식별자와 64비트 정수 인수를 사용하여 이벤트를 씁니다.</summary>
      <param name="eventId">이벤트 식별자입니다.이 값은 0에서 65535 사이여야 합니다.</param>
      <param name="arg1">64비트 정수 인수입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.Byte[])">
      <summary>지정된 식별자 및 64비트 정수와 바이트 배열 인수를 사용하여 이벤트 데이터를 씁니다.</summary>
      <param name="eventId">이벤트 식별자입니다.이 값은 0에서 65535 사이여야 합니다.</param>
      <param name="arg1">64비트 정수 인수입니다.</param>
      <param name="arg2">바이트 배열 인수입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.Int64)">
      <summary>제공된 이벤트 식별자와 64비트 인수를 사용하여 이벤트를 씁니다.</summary>
      <param name="eventId">이벤트 식별자입니다.이 값은 0에서 65535 사이여야 합니다.</param>
      <param name="arg1">64비트 정수 인수입니다.</param>
      <param name="arg2">64비트 정수 인수입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.Int64,System.Int64)">
      <summary>제공된 이벤트 식별자와 64비트 인수를 사용하여 이벤트를 씁니다.</summary>
      <param name="eventId">이벤트 식별자입니다.이 값은 0에서 65535 사이여야 합니다.</param>
      <param name="arg1">64비트 정수 인수입니다.</param>
      <param name="arg2">64비트 정수 인수입니다.</param>
      <param name="arg3">64비트 정수 인수입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.String)">
      <summary>제공된 이벤트 식별자와 64비트 정수 및 문자열 인수를 사용하여 이벤트를 씁니다.</summary>
      <param name="eventId">이벤트 식별자입니다.이 값은 0에서 65535 사이여야 합니다.</param>
      <param name="arg1">64비트 정수 인수입니다.</param>
      <param name="arg2">문자열 인수입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Object[])">
      <summary>제공된 이벤트 식별자와 인수 배열을 사용하여 이벤트를 씁니다.</summary>
      <param name="eventId">이벤트 식별자입니다.이 값은 0에서 65535 사이여야 합니다.</param>
      <param name="args">개체의 배열입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String)">
      <summary>제공된 이벤트 식별자와 문자열 인수를 사용하여 이벤트를 씁니다.</summary>
      <param name="eventId">이벤트 식별자입니다.이 값은 0에서 65535 사이여야 합니다.</param>
      <param name="arg1">문자열 인수입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.Int32)">
      <summary>제공된 이벤트 식별자와 인수를 사용하여 이벤트를 씁니다.</summary>
      <param name="eventId">이벤트 식별자입니다.이 값은 0에서 65535 사이여야 합니다.</param>
      <param name="arg1">문자열 인수입니다.</param>
      <param name="arg2">32비트 정수 인수입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.Int32,System.Int32)">
      <summary>제공된 이벤트 식별자와 인수를 사용하여 이벤트를 씁니다.</summary>
      <param name="eventId">이벤트 식별자입니다.이 값은 0에서 65535 사이여야 합니다.</param>
      <param name="arg1">문자열 인수입니다.</param>
      <param name="arg2">32비트 정수 인수입니다.</param>
      <param name="arg3">32비트 정수 인수입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.Int64)">
      <summary>제공된 이벤트 식별자와 인수를 사용하여 이벤트를 씁니다.</summary>
      <param name="eventId">이벤트 식별자입니다.이 값은 0에서 65535 사이여야 합니다.</param>
      <param name="arg1">문자열 인수입니다.</param>
      <param name="arg2">64비트 정수 인수입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.String)">
      <summary>제공된 이벤트 식별자와 문자열 인수를 사용하여 이벤트를 씁니다.</summary>
      <param name="eventId">이벤트 식별자입니다.이 값은 0에서 65535 사이여야 합니다.</param>
      <param name="arg1">문자열 인수입니다.</param>
      <param name="arg2">문자열 인수입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.String,System.String)">
      <summary>제공된 이벤트 식별자와 문자열 인수를 사용하여 이벤트를 씁니다.</summary>
      <param name="eventId">이벤트 식별자입니다.이 값은 0에서 65535 사이여야 합니다.</param>
      <param name="arg1">문자열 인수입니다.</param>
      <param name="arg2">문자열 인수입니다.</param>
      <param name="arg3">문자열 인수입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEventCore(System.Int32,System.Int32,System.Diagnostics.Tracing.EventSource.EventData*)">
      <summary>제공된 이벤트 식별자 및 이벤트 데이터를 사용하여 새 <see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" /> 오버로드를 만듭니다.</summary>
      <param name="eventId">이벤트 식별자입니다.</param>
      <param name="eventDataCount">이벤트 데이터 항목의 수입니다.</param>
      <param name="data">이벤트 데이터가 들어 있는 구조체입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEventWithRelatedActivityId(System.Int32,System.Guid,System.Object[])">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 현재 작업이 다른 작업과 연관되어 있음을 나타내는 이벤트를 기록합니다. </summary>
      <param name="eventId">
        <see cref="T:System.Diagnostics.Tracing.EventSource" /> 내에서 이 이벤트를 고유하게 식별하는 식별자입니다. </param>
      <param name="relatedActivityId">관련 작업 식별자입니다. </param>
      <param name="args">이벤트에 대한 데이터가 포함된 개체 배열입니다. </param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEventWithRelatedActivityIdCore(System.Int32,System.Guid*,System.Int32,System.Diagnostics.Tracing.EventSource.EventData*)">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 현재 작업이 다른 작업과 연관되어 있음을 나타내는 이벤트를 기록합니다.</summary>
      <param name="eventId">
        <see cref="T:System.Diagnostics.Tracing.EventSource" /> 내에서 이 이벤트를 고유하게 식별하는 식별자입니다.</param>
      <param name="relatedActivityId">관련 작업 ID의 GUID에 대한 포인터입니다. </param>
      <param name="eventDataCount">
        <paramref name="data" /> 필드의 항목 수입니다. </param>
      <param name="data">이벤트 데이터 필드에서 첫 번째 항목에 대한 포인터입니다. </param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSource.EventData">
      <summary>
        <see cref="M:System.Diagnostics.Tracing.EventSource.WriteEventCore(System.Int32,System.Int32,System.Diagnostics.Tracing.EventSource.EventData*)" /> 메서드를 사용하여 빠른 <see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" /> 오버로드를 만들기 위한 이벤트 데이터를 제공합니다.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.EventData.DataPointer">
      <summary>새 <see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" /> 오버로드의 데이터에 대한 포인터를 가져오거나 설정합니다.</summary>
      <returns>데이터에 대한 포인터입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.EventData.Size">
      <summary>새 <see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" /> 오버로드의 페이로드 항목 수를 가져오거나 설정합니다.</summary>
      <returns>새 오버로드의 페이로드 항목 수입니다.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSourceAttribute">
      <summary>ETW(Windows용 이벤트 추적) 이름이 이벤트 소스 클래스의 이름과 독립적으로 정의될 수 있도록 합니다.   </summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceAttribute.#ctor">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceAttribute.Guid">
      <summary>이벤트 소스 식별자 가져오거나 설정합니다.</summary>
      <returns>이벤트 소스 식별자입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceAttribute.LocalizationResources">
      <summary>지역화 리소스 파일의 이름을 가져오거나 설정합니다.</summary>
      <returns>지역화된 리소스 파일의 이름이거나, 지역화 리소스 파일이 없을 경우 null입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceAttribute.Name">
      <summary>이벤트 소스의 이름을 가져오거나 설정합니다.</summary>
      <returns>이벤트 소스의 이름입니다.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSourceException">
      <summary>ETW(Windows용 이벤트 추적) 중 오류가 발생한 경우 throw되는 예외입니다.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceException.#ctor">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.EventSourceException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceException.#ctor(System.String)">
      <summary>지정된 오류 메시지를 사용하여 <see cref="T:System.Diagnostics.Tracing.EventSourceException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">오류를 설명하는 메시지입니다.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 근본 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.Diagnostics.Tracing.EventSourceException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
      <param name="innerException">현재 예외의 원인이 되는 예외이거나, 내부 예외를 지정하지 않았으면 null입니다. </param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSourceOptions">
      <summary>재정의 지정 합니다. 기본 이벤트 설정을 로그 수준 등의 키워드 및 작업 때 코드는 <see cref="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions,``0)" /> 메서드를 호출 합니다.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.ActivityOptions"></member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Keywords">
      <summary>이벤트에 적용된 키워드를 가져오거나 설정합니다.이 속성을 설정 하지 않으면 이벤트의 같은 키워드로 됩니다 None.</summary>
      <returns>이벤트에 적용 된 키워드 또는 None 키워드가 없습니다 설정 된 경우.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Level">
      <summary>이벤트에 적용 하는 이벤트 수준을 가져오거나 설정 합니다. </summary>
      <returns>이벤트에 대한 이벤트 수준입니다.기본값은 설정하지 않으면 Verbose (5)가 됩니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Opcode">
      <summary>지정 된 이벤트를 사용 하 여 작업 코드를 가져오거나 설정 합니다. </summary>
      <returns>지정된 이벤트에 사용할 작업 코드입니다.설정 되어있지 않으면 기본값은 Info (0).</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Tags"></member>
    <member name="T:System.Diagnostics.Tracing.EventSourceSettings">
      <summary>이벤트 소스에 대한 구성 옵션을 지정합니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.Default">
      <summary>사용할 수 있는 특수 구성 옵션이 없습니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.EtwManifestEventFormat">
      <summary>ETW 수신기는 이벤트를 시킬 때 메니페스트 기반 형식을 사용해야 합니다.이 옵션 설정은 이벤트를 발생시킬 때 메니페스트 기반 형식을 사용해야 하는 ETW 수신기에 대한 지시문입니다.파생 된 형식을 정의 하는 경우 기본 옵션입니다 <see cref="T:System.Diagnostics.Tracing.EventSource" /> 보호 된 중 하나를 사용 하 여 <see cref="T:System.Diagnostics.Tracing.EventSource" /> 생성자입니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.EtwSelfDescribingEventFormat">
      <summary>ETW 수신기는 자체 설명 이벤트 형식을 사용해야 합니다.새 인스턴스를 만들 때 기본 옵션입니다는 <see cref="T:System.Diagnostics.Tracing.EventSource" /> 공용 중 하나를 사용 하 여 <see cref="T:System.Diagnostics.Tracing.EventSource" /> 생성자입니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.ThrowOnEventWriteErrors">
      <summary>이벤트 소스는 오류가 발생하면 예외를 throw합니다.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventTags">
      <summary>작업 시작 및 중지 이벤트의 추적을 지정합니다.하위 24비트만 사용해야 합니다.자세한 내용은 <see cref="T:System.Diagnostics.Tracing.EventSourceOptions" /> 및 <see cref="M:System.Diagnostics.Tracing.EventSource.Write(System.String,System.Diagnostics.Tracing.EventSourceOptions)" />를 참조하세요.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventTags.None">
      <summary>태그를 지정하지 않으며 0과 같습니다.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventTask">
      <summary>이벤트에 적용되는 작업을 정의합니다.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventTask.None">
      <summary>정의되어 있지 않은 작업입니다.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventWrittenEventArgs">
      <summary>
        <see cref="M:System.Diagnostics.Tracing.EventListener.OnEventWritten(System.Diagnostics.Tracing.EventWrittenEventArgs)" /> 콜백에 데이터를 제공합니다.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.ActivityId">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 이벤트를 쓴 스레드의 작업 ID를 가져옵니다. </summary>
      <returns>이벤트를 쓴 스레드의 작업 ID입니다. </returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Channel">
      <summary>이벤트의 채널을 가져옵니다.</summary>
      <returns>이벤트 채널입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.EventId">
      <summary>이벤트 식별자를 가져옵니다.</summary>
      <returns>이벤트 식별자입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.EventName">
      <summary>이벤트의 이름을 가져옵니다.</summary>
      <returns>이벤트의 이름입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.EventSource">
      <summary>이벤트 소스 개체를 가져옵니다.</summary>
      <returns>이벤트 소스 개체입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Keywords">
      <summary>이벤트에 대한 키워드를 가져옵니다.</summary>
      <returns>이벤트에 대한 키워드입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Level">
      <summary>이벤트의 수준을 가져옵니다.</summary>
      <returns>이벤트의 수준입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Message">
      <summary>이벤트의 메시지를 가져옵니다.</summary>
      <returns>이벤트의 메시지입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Opcode">
      <summary>이벤트의 작업 코드를 가져옵니다.</summary>
      <returns>이벤트의 작업 코드입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Payload">
      <summary>이벤트의 페이로드를 가져옵니다.</summary>
      <returns>이벤트에 대한 페이로드입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.PayloadNames">
      <summary>이 이벤트의 속성 이름을 나타내는 문자열 목록을 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />를 반환합니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.RelatedActivityId">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 현재 인스턴스에서 나타내는 작업과 관련된 작업의 식별자를 가져옵니다. </summary>
      <returns>관련 동작의 식별자이며, 관련 동작이 없는 경우 <see cref="F:System.Guid.Empty" />입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Tags">
      <summary>호출에 지정된 태그를 <see cref="M:System.Diagnostics.Tracing.EventSource.Write(System.String,System.Diagnostics.Tracing.EventSourceOptions)" /> 메서드로 반환합니다.</summary>
      <returns>
        <see cref="T:System.Diagnostics.Tracing.EventTags" />를 반환합니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Task">
      <summary>이벤트의 작업을 가져옵니다.</summary>
      <returns>이벤트에 대한 작업입니다.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Version">
      <summary>이벤트의 버전을 가져옵니다.</summary>
      <returns>이벤트의 버전입니다.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.NonEventAttribute">
      <summary>이벤트를 생성하지 않는 메서드를 식별합니다.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.NonEventAttribute.#ctor">
      <summary>
        <see cref="T:System.Diagnostics.Tracing.NonEventAttribute" /> 클래스의 새 인스턴스를 만듭니다.</summary>
    </member>
  </members>
</doc>