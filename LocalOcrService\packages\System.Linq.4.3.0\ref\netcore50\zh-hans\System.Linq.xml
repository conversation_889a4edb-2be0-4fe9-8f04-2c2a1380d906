﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Linq</name>
  </assembly>
  <members>
    <member name="T:System.Linq.Enumerable">
      <summary>提供一组用于查询实现 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 的对象的 static（在 Visual Basic 中为 Shared）方法。</summary>
    </member>
    <member name="M:System.Linq.Enumerable.Aggregate``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``0,``0})">
      <summary>对序列应用累加器函数。</summary>
      <returns>累加器的最终值。</returns>
      <param name="source">要聚合的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="func">要对每个元素调用的累加器函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="func" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Aggregate``2(System.Collections.Generic.IEnumerable{``0},``1,System.Func{``1,``0,``1})">
      <summary>对序列应用累加器函数。将指定的种子值用作累加器初始值。</summary>
      <returns>累加器的最终值。</returns>
      <param name="source">要聚合的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="seed">累加器的初始值。</param>
      <param name="func">要对每个元素调用的累加器函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TAccumulate">累加器值的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="func" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Aggregate``3(System.Collections.Generic.IEnumerable{``0},``1,System.Func{``1,``0,``1},System.Func{``1,``2})">
      <summary>对序列应用累加器函数。将指定的种子值用作累加器的初始值，并使用指定的函数选择结果值。</summary>
      <returns>已转换的累加器最终值。</returns>
      <param name="source">要聚合的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="seed">累加器的初始值。</param>
      <param name="func">要对每个元素调用的累加器函数。</param>
      <param name="resultSelector">将累加器的最终值转换为结果值的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TAccumulate">累加器值的类型。</typeparam>
      <typeparam name="TResult">结果值的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="func" /> 或 <paramref name="resultSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.All``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>确定序列中的所有元素是否满足条件。</summary>
      <returns>如果源序列中的每个元素都通过指定谓词中的测试，或者序列为空，则为 true；否则为 false。</returns>
      <param name="source">包含要应用谓词的元素的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Any``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>确定序列是否包含任何元素。</summary>
      <returns>如果源序列包含任何元素，则为 true；否则为 false。</returns>
      <param name="source">要检查是否为空的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Any``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>确定序列中的任何元素是否都满足条件。</summary>
      <returns>如果源序列中的任何元素都通过指定谓词中的测试，则为 true；否则为 false。</returns>
      <param name="source">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，其元素将应用谓词。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.AsEnumerable``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>返回类型为 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 的输入。</summary>
      <returns>类型为 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 的输入序列。</returns>
      <param name="source">类型为 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 的序列。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Decimal})">
      <summary>计算 <see cref="T:System.Decimal" /> 值序列的平均值。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">要计算平均值的 <see cref="T:System.Decimal" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Double})">
      <summary>计算 <see cref="T:System.Double" /> 值序列的平均值。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">要计算平均值的 <see cref="T:System.Double" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Int32})">
      <summary>计算 <see cref="T:System.Int32" /> 值序列的平均值。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">要计算平均值的 <see cref="T:System.Int32" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Int64})">
      <summary>计算 <see cref="T:System.Int64" /> 值序列的平均值。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">要计算平均值的 <see cref="T:System.Int64" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Nullable{System.Decimal}})">
      <summary>计算可以为 null 的 <see cref="T:System.Decimal" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果源序列为空或仅包含为 null 的值，则为 null。</returns>
      <param name="source">要计算平均值的可以为 null 的 <see cref="T:System.Decimal" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">序列中元素之和大于 <see cref="F:System.Decimal.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Nullable{System.Double}})">
      <summary>计算可以为 null 的 <see cref="T:System.Double" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果源序列为空或仅包含为 null 的值，则为 null。</returns>
      <param name="source">要计算平均值的可以为 null 的 <see cref="T:System.Double" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Nullable{System.Int32}})">
      <summary>计算可以为 null 的 <see cref="T:System.Int32" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果源序列为空或仅包含为 null 的值，则为 null。</returns>
      <param name="source">要计算平均值的、可以为 null 的 <see cref="T:System.Int32" />  值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">序列中元素之和大于 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Nullable{System.Int64}})">
      <summary>计算可以为 null 的 <see cref="T:System.Int64" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果源序列为空或仅包含为 null 的值，则为 null。</returns>
      <param name="source">要计算平均值的可以为 null 的 <see cref="T:System.Int64" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">序列中元素之和大于 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Nullable{System.Single}})">
      <summary>计算可以为 null 的 <see cref="T:System.Single" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果源序列为空或仅包含为 null 的值，则为 null。</returns>
      <param name="source">要计算平均值的可以为 null 的 <see cref="T:System.Single" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Single})">
      <summary>计算 <see cref="T:System.Single" /> 值序列的平均值。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">要计算平均值的 <see cref="T:System.Single" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Decimal})">
      <summary>计算 <see cref="T:System.Decimal" /> 值序列的平均值，该值可通过调用输入序列的每个元素的转换函数获取。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">用于计算平均值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
      <exception cref="T:System.OverflowException">序列中元素之和大于 <see cref="F:System.Decimal.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Double})">
      <summary>计算 <see cref="T:System.Double" /> 值序列的平均值，该值可通过调用输入序列的每个元素的转换函数获取。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">要计算其平均值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32})">
      <summary>计算 <see cref="T:System.Int32" /> 值序列的平均值，该值可通过调用输入序列的每个元素的转换函数获取。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">要计算其平均值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
      <exception cref="T:System.OverflowException">序列中元素之和大于 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int64})">
      <summary>计算 <see cref="T:System.Int64" /> 值序列的平均值，该值可通过调用输入序列的每个元素的转换函数获取。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">要计算其平均值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">source 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
      <exception cref="T:System.OverflowException">序列中元素之和大于 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Decimal}})">
      <summary>计算可以为 null 的 <see cref="T:System.Decimal" /> 值序列的平均值，该值可通过调用输入序列的每个元素的转换函数获取。</summary>
      <returns>值序列的平均值；如果源序列为空或仅包含为 null 的值，则为 null。</returns>
      <param name="source">要计算其平均值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">序列中元素之和大于 <see cref="F:System.Decimal.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Double}})">
      <summary>计算可以为 null 的 <see cref="T:System.Double" /> 值序列的平均值，该值可通过调用输入序列的每个元素的转换函数获取。</summary>
      <returns>值序列的平均值；如果源序列为空或仅包含为 null 的值，则为 null。</returns>
      <param name="source">要计算其平均值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Int32}})">
      <summary>计算可以为 null 的 <see cref="T:System.Int32" /> 值序列的平均值，该值可通过调用输入序列的每个元素的转换函数获取。</summary>
      <returns>值序列的平均值；如果源序列为空或仅包含为 null 的值，则为 null。</returns>
      <param name="source">要计算其平均值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">序列中元素之和大于 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Int64}})">
      <summary>计算可以为 null 的 <see cref="T:System.Int64" /> 值序列的平均值，该值可通过调用输入序列的每个元素的转换函数获取。</summary>
      <returns>值序列的平均值；如果源序列为空或仅包含为 null 的值，则为 null。</returns>
      <param name="source">要计算其平均值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Single}})">
      <summary>计算可以为 null 的 <see cref="T:System.Single" /> 值序列的平均值，该值可通过调用输入序列的每个元素的转换函数获取。</summary>
      <returns>值序列的平均值；如果源序列为空或仅包含为 null 的值，则为 null。</returns>
      <param name="source">要计算其平均值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Single})">
      <summary>计算 <see cref="T:System.Single" /> 值序列的平均值，该值可通过调用输入序列的每个元素的转换函数获取。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">要计算其平均值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Cast``1(System.Collections.IEnumerable)">
      <summary>将 <see cref="T:System.Collections.IEnumerable" /> 的元素强制转换为指定的类型。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含强制转换为指定类型的源序列的每个元素。</returns>
      <param name="source">包含要转换成类型 <paramref name="TResult" /> 的元素的 <see cref="T:System.Collections.IEnumerable" />。</param>
      <typeparam name="TResult">
        <paramref name="source" /> 中的元素要强制转换成的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidCastException">序列中的元素不能强制转换为 <paramref name="TResult" /> 类型。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Concat``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>连接两个序列。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含两个输入序列的连接元素。</returns>
      <param name="first">要连接的第一个序列。</param>
      <param name="second">要与第一个序列连接的序列。</param>
      <typeparam name="TSource">输入序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> 或 <paramref name="second" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Contains``1(System.Collections.Generic.IEnumerable{``0},``0)">
      <summary>通过使用默认的相等比较器确定序列是否包含指定的元素。</summary>
      <returns>如果源序列包含具有指定值的元素，则为 true；否则为 false。</returns>
      <param name="source">要在其中定位某个值的序列。</param>
      <param name="value">要在序列中定位的值。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Contains``1(System.Collections.Generic.IEnumerable{``0},``0,System.Collections.Generic.IEqualityComparer{``0})">
      <summary>通过使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 确定序列是否包含指定的元素。</summary>
      <returns>如果源序列包含具有指定值的元素，则为 true；否则为 false。</returns>
      <param name="source">要在其中定位某个值的序列。</param>
      <param name="value">要在序列中定位的值。</param>
      <param name="comparer">一个对值进行比较的相等比较器。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Count``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>返回序列中的元素数量。</summary>
      <returns>输入序列中的元素数量。</returns>
      <param name="source">包含要计数的元素的序列。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="source" /> 中的元素数量大于 <see cref="F:System.Int32.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Count``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>返回一个数字，表示在指定的序列中满足条件的元素数量。</summary>
      <returns>一个数字，表示序列中满足谓词函数条件的元素数量。</returns>
      <param name="source">包含要测试和计数的元素的序列。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="source" /> 中的元素数量大于 <see cref="F:System.Int32.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.DefaultIfEmpty``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>返回指定序列的元素；如果序列为空，则返回单一实例集合中的类型参数的默认值。</summary>
      <returns>如果 <paramref name="source" /> 为空，则为包含 <paramref name="TSource" /> 类型的默认值的 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 对象；否则为 <paramref name="source" />。</returns>
      <param name="source">序列为空时返回默认值的序列。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.DefaultIfEmpty``1(System.Collections.Generic.IEnumerable{``0},``0)">
      <summary>返回指定序列中的元素；如果序列为空，则返回单一实例集合中的指定值。</summary>
      <returns>如果 <paramref name="source" /> 为空，则为包含 <paramref name="defaultValue" /> 的 <see cref="T:System.Collections.Generic.IEnumerable`1" />；否则为 <paramref name="source" />。</returns>
      <param name="source">序列为空时为其返回指定值的序列。</param>
      <param name="defaultValue">序列为空时要返回的值。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
    </member>
    <member name="M:System.Linq.Enumerable.Distinct``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>通过使用默认的相等比较器对值进行比较返回序列中的非重复元素。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含源序列中的非重复元素。</returns>
      <param name="source">要从中移除重复元素的序列。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Distinct``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>通过使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 对值进行比较返回序列中的非重复元素。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含源序列中的非重复元素。</returns>
      <param name="source">要从中移除重复元素的序列。</param>
      <param name="comparer">用于比较值的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ElementAt``1(System.Collections.Generic.IEnumerable{``0},System.Int32)">
      <summary>返回序列中指定索引处的元素。</summary>
      <returns>源序列中指定位置处的元素。</returns>
      <param name="source">要从中返回元素的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="index">要检索的从零开始的元素索引。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于零或大于等于 <paramref name="source" /> 中的元素数量。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ElementAtOrDefault``1(System.Collections.Generic.IEnumerable{``0},System.Int32)">
      <summary>返回序列中指定索引处的元素；如果索引超出范围，则返回默认值。</summary>
      <returns>如果索引超出源序列的范围，则为 default(<paramref name="TSource" />)；否则为源序列中指定位置处的元素。</returns>
      <param name="source">要从中返回元素的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="index">要检索的从零开始的元素索引。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Empty``1">
      <summary>返回一个具有指定的类型参数的空 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</summary>
      <returns>一个类型参数为 <paramref name="TResult" /> 的空 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</returns>
      <typeparam name="TResult">分配给返回的泛型 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 的类型参数的类型。</typeparam>
    </member>
    <member name="M:System.Linq.Enumerable.Except``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>通过使用默认的相等比较器对值进行比较生成两个序列的差集。</summary>
      <returns>包含两个序列元素的差集的序列。</returns>
      <param name="first">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，将返回其也不在 <paramref name="second" /> 中的元素。</param>
      <param name="second">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，如果它的元素也出现在第一个序列中，则将导致从返回的序列中移除这些元素。</param>
      <typeparam name="TSource">输入序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> 或 <paramref name="second" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Except``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>通过使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 对值进行比较产生两个序列的差集。</summary>
      <returns>包含两个序列元素的差集的序列。</returns>
      <param name="first">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，将返回其也不在 <paramref name="second" /> 中的元素。</param>
      <param name="second">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，如果它的元素也出现在第一个序列中，则将导致从返回的序列中移除这些元素。</param>
      <param name="comparer">用于比较值的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">输入序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> 或 <paramref name="second" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.First``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>返回序列中的第一个元素。</summary>
      <returns>返回指定序列中的第一个元素。</returns>
      <param name="source">要返回其第一个元素的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">源序列为空。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.First``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>返回序列中满足指定条件的第一个元素。</summary>
      <returns>序列中通过指定谓词函数中的测试的第一个元素。</returns>
      <param name="source">要从中返回元素的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有元素满足 <paramref name="predicate" /> 中的条件。- 或 -源序列为空。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.FirstOrDefault``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>返回序列中的第一个元素；如果序列中不包含任何元素，则返回默认值。</summary>
      <returns>如果 <paramref name="source" /> 为空，则返回 default(<paramref name="TSource" />)；否则返回 <paramref name="source" /> 中的第一个元素。</returns>
      <param name="source">要返回其第一个元素的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.FirstOrDefault``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>返回序列中满足条件的第一个元素；如果未找到这样的元素，则返回默认值。</summary>
      <returns>如果 <paramref name="source" /> 为空或没有元素通过 <paramref name="predicate" /> 指定的测试，则返回 default(<paramref name="TSource" />)，否则返回 <paramref name="source" /> 中通过 <paramref name="predicate" /> 指定的测试的第一个元素。</returns>
      <param name="source">要从中返回元素的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.GroupBy``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1})">
      <summary>根据指定的键选择器函数对序列中的元素进行分组。</summary>
      <returns>在 C# 中为 IEnumerable&lt;IGrouping&lt;TKey, TSource&gt;&gt;，或者在 Visual Basic 中为 IEnumerable(Of IGrouping(Of TKey, TSource))，其中每个 <see cref="T:System.Linq.IGrouping`2" /> 对象都包含一个对象序列和一个键。</returns>
      <param name="source">要对其元素进行分组的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="keySelector">用于提取每个元素的键的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.GroupBy``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>根据指定的键选择器函数对序列中的元素进行分组，并使用指定的比较器对键进行比较。</summary>
      <returns>在 C# 中为 IEnumerable&lt;IGrouping&lt;TKey, TSource&gt;&gt;，或者在 Visual Basic 中为 IEnumerable(Of IGrouping(Of TKey, TSource))，其中每个 <see cref="T:System.Linq.IGrouping`2" /> 对象都包含一个对象集合和一个键。</returns>
      <param name="source">要对其元素进行分组的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="keySelector">用于提取每个元素的键的函数。</param>
      <param name="comparer">一个用于对键进行比较的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.GroupBy``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2})">
      <summary>根据指定的键选择器函数对序列中的元素进行分组，并且通过使用指定的函数对每个组中的元素进行投影。</summary>
      <returns>在 C# 中为 IEnumerable&lt;IGrouping&lt;TKey, TElement&gt;&gt;，或者在 Visual Basic 中为 IEnumerable(Of IGrouping(Of TKey, TElement))，其中每个 <see cref="T:System.Linq.IGrouping`2" /> 对象都包含一个类型为 <paramref name="TElement" /> 的对象集合和一个键。</returns>
      <param name="source">要对其元素进行分组的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="keySelector">用于提取每个元素的键的函数。</param>
      <param name="elementSelector">用于将每个源元素映射到 <see cref="T:System.Linq.IGrouping`2" /> 中的元素的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <typeparam name="TElement">
        <see cref="T:System.Linq.IGrouping`2" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 或 <paramref name="elementSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.GroupBy``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>根据键选择器函数对序列中的元素进行分组。通过使用比较器对键进行比较，并且通过使用指定的函数对每个组的元素进行投影。</summary>
      <returns>在 C# 中为 IEnumerable&lt;IGrouping&lt;TKey, TElement&gt;&gt;，或者在 Visual Basic 中为 IEnumerable(Of IGrouping(Of TKey, TElement))，其中每个 <see cref="T:System.Linq.IGrouping`2" /> 对象都包含一个类型为 <paramref name="TElement" /> 的对象集合和一个键。</returns>
      <param name="source">要对其元素进行分组的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="keySelector">用于提取每个元素的键的函数。</param>
      <param name="elementSelector">用于将每个源元素映射到 <see cref="T:System.Linq.IGrouping`2" /> 中的元素的函数。</param>
      <param name="comparer">一个用于对键进行比较的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <typeparam name="TElement">
        <see cref="T:System.Linq.IGrouping`2" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 或 <paramref name="elementSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.GroupBy``4(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2},System.Func{``1,System.Collections.Generic.IEnumerable{``2},``3})">
      <summary>根据指定的键选择器函数对序列中的元素进行分组，并且从每个组及其键中创建结果值。通过使用指定的函数对每个组的元素进行投影。</summary>
      <returns>
        <paramref name="TResult" /> 类型的元素的集合，其中每个元素都表示对一个组及其键的投影。</returns>
      <param name="source">要对其元素进行分组的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="keySelector">用于提取每个元素的键的函数。</param>
      <param name="elementSelector">用于将每个源元素映射到 <see cref="T:System.Linq.IGrouping`2" /> 中的元素的函数。</param>
      <param name="resultSelector">用于从每个组中创建结果值的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <typeparam name="TElement">每个 <see cref="T:System.Linq.IGrouping`2" /> 中的元素的类型。</typeparam>
      <typeparam name="TResult">
        <paramref name="resultSelector" /> 返回的结果值的类型。</typeparam>
    </member>
    <member name="M:System.Linq.Enumerable.GroupBy``4(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2},System.Func{``1,System.Collections.Generic.IEnumerable{``2},``3},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>根据指定的键选择器函数对序列中的元素进行分组，并且从每个组及其键中创建结果值。通过使用指定的比较器对键值进行比较，并且通过使用指定的函数对每个组的元素进行投影。</summary>
      <returns>
        <paramref name="TResult" /> 类型的元素的集合，其中每个元素都表示对一个组及其键的投影。</returns>
      <param name="source">要对其元素进行分组的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="keySelector">用于提取每个元素的键的函数。</param>
      <param name="elementSelector">用于将每个源元素映射到 <see cref="T:System.Linq.IGrouping`2" /> 中的元素的函数。</param>
      <param name="resultSelector">用于从每个组中创建结果值的函数。</param>
      <param name="comparer">一个用于对键进行比较的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <typeparam name="TElement">每个 <see cref="T:System.Linq.IGrouping`2" /> 中的元素的类型。</typeparam>
      <typeparam name="TResult">
        <paramref name="resultSelector" /> 返回的结果值的类型。</typeparam>
    </member>
    <member name="M:System.Linq.Enumerable.GroupBy``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``1,System.Collections.Generic.IEnumerable{``0},``2})">
      <summary>根据指定的键选择器函数对序列中的元素进行分组，并且从每个组及其键中创建结果值。</summary>
      <returns>
        <paramref name="TResult" /> 类型的元素的集合，其中每个元素都表示对一个组及其键的投影。</returns>
      <param name="source">要对其元素进行分组的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="keySelector">用于提取每个元素的键的函数。</param>
      <param name="resultSelector">用于从每个组中创建结果值的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <typeparam name="TResult">
        <paramref name="resultSelector" /> 返回的结果值的类型。</typeparam>
    </member>
    <member name="M:System.Linq.Enumerable.GroupBy``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``1,System.Collections.Generic.IEnumerable{``0},``2},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>根据指定的键选择器函数对序列中的元素进行分组，并且从每个组及其键中创建结果值。通过使用指定的比较器对键进行比较。</summary>
      <returns>
        <paramref name="TResult" /> 类型的元素的集合，其中每个元素都表示对一个组及其键的投影。</returns>
      <param name="source">要对其元素进行分组的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="keySelector">用于提取每个元素的键的函数。</param>
      <param name="resultSelector">用于从每个组中创建结果值的函数。</param>
      <param name="comparer">一个用于对键进行比较的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <typeparam name="TResult">
        <paramref name="resultSelector" /> 返回的结果值的类型。</typeparam>
    </member>
    <member name="M:System.Linq.Enumerable.GroupJoin``4(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``1},System.Func{``0,``2},System.Func{``1,``2},System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3})">
      <summary>基于键相等对两个序列的元素进行关联并对结果进行分组。使用默认的相等比较器对键进行比较。</summary>
      <returns>一个包含 <paramref name="TResult" /> 类型的元素的 <see cref="T:System.Collections.Generic.IEnumerable`1" />，这些元素可通过对两个序列执行分组联接获取。</returns>
      <param name="outer">要联接的第一个序列。</param>
      <param name="inner">要与第一个序列联接的序列。</param>
      <param name="outerKeySelector">用于从第一个序列的每个元素提取联接键的函数。</param>
      <param name="innerKeySelector">用于从第二个序列的每个元素提取联接键的函数。</param>
      <param name="resultSelector">用于从第一个序列的元素和第二个序列的匹配元素集合中创建结果元素的函数。</param>
      <typeparam name="TOuter">第一个序列中的元素的类型。</typeparam>
      <typeparam name="TInner">第二个序列中的元素的类型。</typeparam>
      <typeparam name="TKey">键选择器函数返回的键的类型。</typeparam>
      <typeparam name="TResult">结果元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> 或 <paramref name="inner" /> 或 <paramref name="outerKeySelector" /> 或 <paramref name="innerKeySelector" /> 或 <paramref name="resultSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.GroupJoin``4(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``1},System.Func{``0,``2},System.Func{``1,``2},System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>基于键相等对两个序列的元素进行关联并对结果进行分组。使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 对键进行比较。</summary>
      <returns>一个包含 <paramref name="TResult" /> 类型的元素的 <see cref="T:System.Collections.Generic.IEnumerable`1" />，这些元素可通过对两个序列执行分组联接获取。</returns>
      <param name="outer">要联接的第一个序列。</param>
      <param name="inner">要与第一个序列联接的序列。</param>
      <param name="outerKeySelector">用于从第一个序列的每个元素提取联接键的函数。</param>
      <param name="innerKeySelector">用于从第二个序列的每个元素提取联接键的函数。</param>
      <param name="resultSelector">用于从第一个序列的元素和第二个序列的匹配元素集合中创建结果元素的函数。</param>
      <param name="comparer">一个 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />，用于对键进行哈希处理和比较。</param>
      <typeparam name="TOuter">第一个序列中的元素的类型。</typeparam>
      <typeparam name="TInner">第二个序列中的元素的类型。</typeparam>
      <typeparam name="TKey">键选择器函数返回的键的类型。</typeparam>
      <typeparam name="TResult">结果元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> 或 <paramref name="inner" /> 或 <paramref name="outerKeySelector" /> 或 <paramref name="innerKeySelector" /> 或 <paramref name="resultSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Intersect``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>通过使用默认的相等比较器对值进行比较生成两个序列的交集。</summary>
      <returns>包含组成两个序列交集的元素的序列。</returns>
      <param name="first">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，将返回其也出现在 <paramref name="second" /> 中的非重复元素。</param>
      <param name="second">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，将返回其也出现在第一个序列中的非重复元素。</param>
      <typeparam name="TSource">输入序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> 或 <paramref name="second" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Intersect``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>通过使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 对值进行比较以生成两个序列的交集。</summary>
      <returns>包含组成两个序列交集的元素的序列。</returns>
      <param name="first">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，将返回其也出现在 <paramref name="second" /> 中的非重复元素。</param>
      <param name="second">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，将返回其也出现在第一个序列中的非重复元素。</param>
      <param name="comparer">用于比较值的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">输入序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> 或 <paramref name="second" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Join``4(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``1},System.Func{``0,``2},System.Func{``1,``2},System.Func{``0,``1,``3})">
      <summary>基于匹配键对两个序列的元素进行关联。使用默认的相等比较器对键进行比较。</summary>
      <returns>一个具有 <paramref name="TResult" /> 类型元素的 <see cref="T:System.Collections.Generic.IEnumerable`1" />，这些元素是通过对两个序列执行内部联接得来的。</returns>
      <param name="outer">要联接的第一个序列。</param>
      <param name="inner">要与第一个序列联接的序列。</param>
      <param name="outerKeySelector">用于从第一个序列的每个元素提取联接键的函数。</param>
      <param name="innerKeySelector">用于从第二个序列的每个元素提取联接键的函数。</param>
      <param name="resultSelector">用于从两个匹配元素创建结果元素的函数。</param>
      <typeparam name="TOuter">第一个序列中的元素的类型。</typeparam>
      <typeparam name="TInner">第二个序列中的元素的类型。</typeparam>
      <typeparam name="TKey">键选择器函数返回的键的类型。</typeparam>
      <typeparam name="TResult">结果元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> 或 <paramref name="inner" /> 或 <paramref name="outerKeySelector" /> 或 <paramref name="innerKeySelector" /> 或 <paramref name="resultSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Join``4(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``1},System.Func{``0,``2},System.Func{``1,``2},System.Func{``0,``1,``3},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>基于匹配键对两个序列的元素进行关联。使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 对键进行比较。</summary>
      <returns>一个具有 <paramref name="TResult" /> 类型元素的 <see cref="T:System.Collections.Generic.IEnumerable`1" />，这些元素是通过对两个序列执行内部联接得来的。</returns>
      <param name="outer">要联接的第一个序列。</param>
      <param name="inner">要与第一个序列联接的序列。</param>
      <param name="outerKeySelector">用于从第一个序列的每个元素提取联接键的函数。</param>
      <param name="innerKeySelector">用于从第二个序列的每个元素提取联接键的函数。</param>
      <param name="resultSelector">用于从两个匹配元素创建结果元素的函数。</param>
      <param name="comparer">一个 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />，用于对键进行哈希处理和比较。</param>
      <typeparam name="TOuter">第一个序列中的元素的类型。</typeparam>
      <typeparam name="TInner">第二个序列中的元素的类型。</typeparam>
      <typeparam name="TKey">键选择器函数返回的键的类型。</typeparam>
      <typeparam name="TResult">结果元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> 或 <paramref name="inner" /> 或 <paramref name="outerKeySelector" /> 或 <paramref name="innerKeySelector" /> 或 <paramref name="resultSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Last``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>返回序列的最后一个元素。</summary>
      <returns>源序列中最后位置处的值。</returns>
      <param name="source">要返回其最后一个元素的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">源序列为空。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Last``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>返回序列中满足指定条件的最后一个元素。</summary>
      <returns>序列中通过指定谓词函数中的测试的最后一个元素。</returns>
      <param name="source">要从中返回元素的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有元素满足 <paramref name="predicate" /> 中的条件。- 或 -源序列为空。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.LastOrDefault``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>返回序列中的最后一个元素；如果序列中不包含任何元素，则返回默认值。</summary>
      <returns>如果源序列为空，则返回 default(<paramref name="TSource" />)；否则返回 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 中的最后一个元素。</returns>
      <param name="source">要返回其最后一个元素的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.LastOrDefault``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>返回序列中满足条件的最后一个元素；如果未找到这样的元素，则返回默认值。</summary>
      <returns>如果序列为空或没有元素通过谓词函数中的测试，则返回 default(<paramref name="TSource" />)；否则返回通过谓词函数中的测试的最后一个元素。</returns>
      <param name="source">要从中返回元素的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.LongCount``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>返回一个 <see cref="T:System.Int64" />，表示序列中的元素的总数量。</summary>
      <returns>源序列中的元素的数量。</returns>
      <param name="source">包含要进行计数的元素的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">元素的数量超过 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.LongCount``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>返回一个 <see cref="T:System.Int64" />，表示序列中满足条件的元素的数量。</summary>
      <returns>一个数字，表示序列中满足谓词函数条件的元素数量。</returns>
      <param name="source">包含要进行计数的元素的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">匹配元素的数量超过 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Decimal})">
      <summary>返回 <see cref="T:System.Decimal" /> 值序列中的最大值。</summary>
      <returns>序列中的最大值。</returns>
      <param name="source">要确定其最大值的 <see cref="T:System.Decimal" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Double})">
      <summary>返回 <see cref="T:System.Double" /> 值序列中的最大值。</summary>
      <returns>序列中的最大值。</returns>
      <param name="source">要确定其最大值的 <see cref="T:System.Double" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Int32})">
      <summary>返回 <see cref="T:System.Int32" /> 值序列中的最大值。</summary>
      <returns>序列中的最大值。</returns>
      <param name="source">要确定其最大值的 <see cref="T:System.Int32" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Int64})">
      <summary>返回 <see cref="T:System.Int64" /> 值序列中的最大值。</summary>
      <returns>序列中的最大值。</returns>
      <param name="source">要确定其最大值的 <see cref="T:System.Int64" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Nullable{System.Decimal}})">
      <summary>返回可以为 null 的 <see cref="T:System.Decimal" /> 值序列中的最大值。</summary>
      <returns>一个与序列中的最大值对应的值，该值的类型在 C# 中为 Nullable&lt;Decimal&gt;，在 Visual Basic 中为 Nullable(Of Decimal)。 </returns>
      <param name="source">要确定其最大值的可以为 null 的 <see cref="T:System.Decimal" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Nullable{System.Double}})">
      <summary>返回可以为 null 的 <see cref="T:System.Double" /> 值序列中的最大值。</summary>
      <returns>一个与序列中的最大值对应的值，该值的类型在 C# 中为 Nullable&lt;Double&gt;，在 Visual Basic 中为 Nullable(Of Double)。</returns>
      <param name="source">要确定其最大值的可以为 null 的 <see cref="T:System.Double" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Nullable{System.Int32}})">
      <summary>返回可以为 null 的 <see cref="T:System.Int32" /> 值序列中的最大值。</summary>
      <returns>一个与序列中的最大值对应的值，该值的类型在 C# 中为 Nullable&lt;Int32&gt;，在 Visual Basic 中为 Nullable(Of Int32)。 </returns>
      <param name="source">要确定其最大值的可以为 null 的 <see cref="T:System.Int32" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Nullable{System.Int64}})">
      <summary>返回可以为 null 的 <see cref="T:System.Int64" /> 值序列中的最大值。</summary>
      <returns>一个与序列中的最大值对应的值，该值的类型在 C# 中为 Nullable&lt;Int64&gt;，在 Visual Basic 中为 Nullable(Of Int64)。 </returns>
      <param name="source">要确定其最大值的可以为 null 的 <see cref="T:System.Int64" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Nullable{System.Single}})">
      <summary>返回可以为 null 的 <see cref="T:System.Single" /> 值序列中的最大值。</summary>
      <returns>一个与序列中的最大值对应的值，该值的类型在 C# 中为 Nullable&lt;Single&gt;，在 Visual Basic 中为 Nullable(Of Single)。</returns>
      <param name="source">要确定其最大值的可以为 null 的 <see cref="T:System.Single" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Single})">
      <summary>返回 <see cref="T:System.Single" /> 值序列中的最大值。</summary>
      <returns>序列中的最大值。</returns>
      <param name="source">要确定其最大值的 <see cref="T:System.Single" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>返回泛型序列中的最大值。</summary>
      <returns>序列中的最大值。</returns>
      <param name="source">要确定其最大值的值序列。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Decimal})">
      <summary>调用序列的每个元素上的转换函数并返回最大 <see cref="T:System.Decimal" /> 值。</summary>
      <returns>序列中的最大值。</returns>
      <param name="source">要确定其最大值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Double})">
      <summary>调用序列的每个元素上的转换函数并返回最大 <see cref="T:System.Double" /> 值。</summary>
      <returns>序列中的最大值。</returns>
      <param name="source">要确定其最大值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32})">
      <summary>调用序列的每个元素上的转换函数并返回最大 <see cref="T:System.Int32" /> 值。</summary>
      <returns>序列中的最大值。</returns>
      <param name="source">要确定其最大值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int64})">
      <summary>调用序列的每个元素上的转换函数并返回最大 <see cref="T:System.Int64" /> 值。</summary>
      <returns>序列中的最大值。</returns>
      <param name="source">要确定其最大值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Decimal}})">
      <summary>调用序列的每个元素上的转换函数并返回可空 <see cref="T:System.Decimal" /> 的最大值。</summary>
      <returns>C# 中类型为 Nullable&lt;Decimal&gt; 的值或 Visual Basic 中与序列中最大值对应的 Nullable(Of Decimal)。</returns>
      <param name="source">要确定其最大值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Double}})">
      <summary>调用序列的每个元素上的转换函数并返回可空 <see cref="T:System.Double" /> 的最大值。</summary>
      <returns>C# 中类型为 Nullable&lt;Double&gt; 的值或 Visual Basic 中与序列中最大值对应的 Nullable(Of Double)。</returns>
      <param name="source">要确定其最大值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Int32}})">
      <summary>调用序列的每个元素上的转换函数并返回可空 <see cref="T:System.Int32" /> 的最大值。</summary>
      <returns>C# 中类型为 Nullable&lt;Int32&gt; 的值或 Visual Basic 中与序列中最大值对应的 Nullable(Of Int32)。</returns>
      <param name="source">要确定其最大值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Int64}})">
      <summary>调用序列的每个元素上的转换函数并返回可空 <see cref="T:System.Int64" /> 的最大值。</summary>
      <returns>C# 中类型为 Nullable&lt;Int64&gt; 的值或 Visual Basic 中与序列中最大值对应的 Nullable(Of Int64)。</returns>
      <param name="source">要确定其最大值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Single}})">
      <summary>调用序列的每个元素上的转换函数并返回可空 <see cref="T:System.Single" /> 的最大值。</summary>
      <returns>C# 中类型为 Nullable&lt;Single&gt; 的值或 Visual Basic 中与序列中最大值对应的 Nullable(Of Single)。</returns>
      <param name="source">要确定其最大值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Single})">
      <summary>调用序列的每个元素上的转换函数并返回最大 <see cref="T:System.Single" /> 值。</summary>
      <returns>序列中的最大值。</returns>
      <param name="source">要确定其最大值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1})">
      <summary>调用泛型序列的每个元素上的转换函数并返回最大结果值。</summary>
      <returns>序列中的最大值。</returns>
      <param name="source">要确定其最大值的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TResult">
        <paramref name="selector" /> 返回的值的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Decimal})">
      <summary>返回 <see cref="T:System.Decimal" /> 值序列中的最小值。</summary>
      <returns>序列中的最小值。</returns>
      <param name="source">一个 <see cref="T:System.Decimal" /> 值序列，用于确定最大值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Double})">
      <summary>返回 <see cref="T:System.Double" /> 值序列中的最小值。</summary>
      <returns>序列中的最小值。</returns>
      <param name="source">一个 <see cref="T:System.Double" /> 值序列，用于确定最小值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Int32})">
      <summary>返回 <see cref="T:System.Int32" /> 值序列中的最小值。</summary>
      <returns>序列中的最小值。</returns>
      <param name="source">一个 <see cref="T:System.Int32" /> 值序列，用于确定最小值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Int64})">
      <summary>返回 <see cref="T:System.Int64" /> 值序列中的最小值。</summary>
      <returns>序列中的最小值。</returns>
      <param name="source">一个 <see cref="T:System.Int64" /> 值序列，用于确定最小值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Nullable{System.Decimal}})">
      <summary>返回 <see cref="T:System.Decimal" /> 值（可空）序列中的最小值。</summary>
      <returns>C# 中类型为 Nullable&lt;Decimal&gt; 的值或 Visual Basic 中与序列中最小值对应的 Nullable(Of Decimal)。</returns>
      <param name="source">一个可空 <see cref="T:System.Decimal" /> 值的序列，用于确定最小值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Nullable{System.Double}})">
      <summary>返回 <see cref="T:System.Double" /> 值（可空）序列中的最小值。</summary>
      <returns>C# 中类型为 Nullable&lt;Double&gt; 的值或 Visual Basic 中与序列中最小值对应的 Nullable(Of Double)。</returns>
      <param name="source">一个可空 <see cref="T:System.Double" /> 值的序列，用于确定最小值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Nullable{System.Int32}})">
      <summary>返回 <see cref="T:System.Int32" /> 值（可空）序列中的最小值。</summary>
      <returns>C# 中类型为 Nullable&lt;Int32&gt; 的值或 Visual Basic 中与序列中最小值对应的 Nullable(Of Int32)。</returns>
      <param name="source">一个可空 <see cref="T:System.Int32" /> 值的序列，用于确定最小值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Nullable{System.Int64}})">
      <summary>返回 <see cref="T:System.Int64" /> 值（可空）序列中的最小值。</summary>
      <returns>C# 中类型为 Nullable&lt;Int64&gt; 的值或 Visual Basic 中与序列中最小值对应的 Nullable(Of Int64)。</returns>
      <param name="source">一个可空 <see cref="T:System.Int64" /> 值的序列，用于确定最小值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Nullable{System.Single}})">
      <summary>返回 <see cref="T:System.Single" /> 值（可空）序列中的最小值。</summary>
      <returns>C# 中类型为 Nullable&lt;Single&gt; 的值或 Visual Basic 中与序列中最小值对应的 Nullable(Of Single)。</returns>
      <param name="source">一个可空 <see cref="T:System.Single" /> 值的序列，用于确定最小值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Single})">
      <summary>返回 <see cref="T:System.Single" /> 值序列中的最小值。</summary>
      <returns>序列中的最小值。</returns>
      <param name="source">一个 <see cref="T:System.Single" /> 值序列，用于确定最小值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>返回泛型序列中的最小值。</summary>
      <returns>序列中的最小值。</returns>
      <param name="source">一个值序列，用于确定最小值。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Decimal})">
      <summary>调用序列的每个元素上的转换函数并返回最小 <see cref="T:System.Decimal" /> 值。</summary>
      <returns>序列中的最小值。</returns>
      <param name="source">一个值序列，用于确定最小值。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Double})">
      <summary>调用序列的每个元素上的转换函数并返回最小 <see cref="T:System.Double" /> 值。</summary>
      <returns>序列中的最小值。</returns>
      <param name="source">一个值序列，用于确定最小值。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32})">
      <summary>调用序列的每个元素上的转换函数并返回最小 <see cref="T:System.Int32" /> 值。</summary>
      <returns>序列中的最小值。</returns>
      <param name="source">一个值序列，用于确定最小值。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int64})">
      <summary>调用序列的每个元素上的转换函数并返回最小 <see cref="T:System.Int64" /> 值。</summary>
      <returns>序列中的最小值。</returns>
      <param name="source">一个值序列，用于确定最小值。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Decimal}})">
      <summary>调用序列的每个元素上的转换函数并返回可空 <see cref="T:System.Decimal" /> 的最小值。</summary>
      <returns>C# 中类型为 Nullable&lt;Decimal&gt; 的值或 Visual Basic 中与序列中最小值对应的 Nullable(Of Decimal)。</returns>
      <param name="source">一个值序列，用于确定最小值。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Double}})">
      <summary>调用序列的每个元素上的转换函数并返回可空 <see cref="T:System.Double" /> 的最小值。</summary>
      <returns>C# 中类型为 Nullable&lt;Double&gt; 的值或 Visual Basic 中与序列中最小值对应的 Nullable(Of Double)。</returns>
      <param name="source">一个值序列，用于确定最小值。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Int32}})">
      <summary>调用序列的每个元素上的转换函数并返回可空 <see cref="T:System.Int32" /> 的最小值。</summary>
      <returns>C# 中类型为 Nullable&lt;Int32&gt; 的值或 Visual Basic 中与序列中最小值对应的 Nullable(Of Int32)。</returns>
      <param name="source">一个值序列，用于确定最小值。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Int64}})">
      <summary>调用序列的每个元素上的转换函数并返回可空 <see cref="T:System.Int64" /> 的最小值。</summary>
      <returns>C# 中类型为 Nullable&lt;Int64&gt; 的值或 Visual Basic 中与序列中最小值对应的 Nullable(Of Int64)。</returns>
      <param name="source">一个值序列，用于确定最小值。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Single}})">
      <summary>调用序列的每个元素上的转换函数并返回可空 <see cref="T:System.Single" /> 的最小值。</summary>
      <returns>C# 中类型为 Nullable&lt;Single&gt; 的值或 Visual Basic 中与序列中最小值对应的 Nullable(Of Single)。</returns>
      <param name="source">一个值序列，用于确定最小值。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Single})">
      <summary>调用序列的每个元素上的转换函数并返回最小 <see cref="T:System.Single" /> 值。</summary>
      <returns>序列中的最小值。</returns>
      <param name="source">一个值序列，用于确定最小值。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1})">
      <summary>调用泛型序列的每个元素上的转换函数并返回最小结果值。</summary>
      <returns>序列中的最小值。</returns>
      <param name="source">一个值序列，用于确定最小值。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TResult">
        <paramref name="selector" /> 返回的值的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.OfType``1(System.Collections.IEnumerable)">
      <summary>根据指定类型筛选 <see cref="T:System.Collections.IEnumerable" /> 的元素。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含类型为 <paramref name="TResult" /> 的输入序列中的元素。</returns>
      <param name="source">
        <see cref="T:System.Collections.IEnumerable" />，其元素用于筛选。</param>
      <typeparam name="TResult">筛选序列元素所根据的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.OrderBy``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1})">
      <summary>根据键按升序对序列的元素排序。</summary>
      <returns>一个 <see cref="T:System.Linq.IOrderedEnumerable`1" />，其元素按键排序。</returns>
      <param name="source">一个要排序的值序列。</param>
      <param name="keySelector">用于从元素中提取键的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.OrderBy``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Collections.Generic.IComparer{``1})">
      <summary>使用指定的比较器按升序对序列的元素排序。</summary>
      <returns>一个 <see cref="T:System.Linq.IOrderedEnumerable`1" />，其元素按键排序。</returns>
      <param name="source">一个要排序的值序列。</param>
      <param name="keySelector">用于从元素中提取键的函数。</param>
      <param name="comparer">一个用于比较键的 <see cref="T:System.Collections.Generic.IComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.OrderByDescending``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1})">
      <summary>根据键按降序对序列的元素排序。</summary>
      <returns>一个 <see cref="T:System.Linq.IOrderedEnumerable`1" />，将根据某个键按降序对其元素进行排序。</returns>
      <param name="source">一个要排序的值序列。</param>
      <param name="keySelector">用于从元素中提取键的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.OrderByDescending``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Collections.Generic.IComparer{``1})">
      <summary>使用指定的比较器按降序对序列的元素排序。</summary>
      <returns>一个 <see cref="T:System.Linq.IOrderedEnumerable`1" />，将根据某个键按降序对其元素进行排序。</returns>
      <param name="source">一个要排序的值序列。</param>
      <param name="keySelector">用于从元素中提取键的函数。</param>
      <param name="comparer">一个用于比较键的 <see cref="T:System.Collections.Generic.IComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Range(System.Int32,System.Int32)">
      <summary>生成指定范围内的整数的序列。</summary>
      <returns>C# 中的 IEnumerable&lt;Int32&gt; 或 Visual Basic 中包含某个范围内的顺序整数的 IEnumerable(Of Int32)。</returns>
      <param name="start">序列中第一个整数的值。</param>
      <param name="count">要生成的顺序整数的数目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> 小于 0。- 或 -<paramref name="start" /> + <paramref name="count" /> -1 大于 <see cref="F:System.Int32.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Repeat``1(``0,System.Int32)">
      <summary>生成包含一个重复值的序列。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含一个重复值。</returns>
      <param name="element">要重复的值。</param>
      <param name="count">在生成序列中重复该值的次数。</param>
      <typeparam name="TResult">要在结果序列中重复的值的类型。</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> 小于 0。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Reverse``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>反转序列中元素的顺序。</summary>
      <returns>一个序列，其元素以相反顺序对应于输入序列的元素。</returns>
      <param name="source">要反转的值序列。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Select``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1})">
      <summary>将序列中的每个元素投影到新表中。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，其元素为对 <paramref name="source" /> 的每个元素调用转换函数的结果。</returns>
      <param name="source">一个值序列，要对该序列调用转换函数。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TResult">
        <paramref name="selector" /> 返回的值的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Select``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32,``1})">
      <summary>通过合并元素的索引将序列的每个元素投影到新表中。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，其元素为对 <paramref name="source" /> 的每个元素调用转换函数的结果。</returns>
      <param name="source">一个值序列，要对该序列调用转换函数。</param>
      <param name="selector">一个应用于每个源元素的转换函数；函数的第二个参数表示源元素的索引。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TResult">
        <paramref name="selector" /> 返回的值的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SelectMany``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Collections.Generic.IEnumerable{``1}},System.Func{``0,``1,``2})">
      <summary>将序列的每个元素投影到 <see cref="T:System.Collections.Generic.IEnumerable`1" />，并将结果序列合并为一个序列，并对其中每个元素调用结果选择器函数。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，其元素是对 <paramref name="source" /> 的每个元素调用一对多转换函数 <paramref name="collectionSelector" />，然后将那些序列元素中的每一个及其相应的源元素映射为结果元素的结果。</returns>
      <param name="source">一个要投影的值序列。</param>
      <param name="collectionSelector">一个应用于输入序列的每个元素的转换函数。</param>
      <param name="resultSelector">一个应用于中间序列的每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TCollection">
        <paramref name="collectionSelector" /> 收集的中间元素的类型。</typeparam>
      <typeparam name="TResult">结果序列的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="collectionSelector" /> 或 <paramref name="resultSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SelectMany``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Collections.Generic.IEnumerable{``1}})">
      <summary>将序列的每个元素投影到 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 并将结果序列合并为一个序列。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，其元素是对输入序列的每个元素调用一对多转换函数的结果。</returns>
      <param name="source">一个要投影的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TResult">
        <paramref name="selector" /> 返回的序列元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SelectMany``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32,System.Collections.Generic.IEnumerable{``1}},System.Func{``0,``1,``2})">
      <summary>将序列的每个元素投影到 <see cref="T:System.Collections.Generic.IEnumerable`1" />，并将结果序列合并为一个序列，并对其中每个元素调用结果选择器函数。每个源元素的索引用于该元素的中间投影表。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，其元素是对 <paramref name="source" /> 的每个元素调用一对多转换函数 <paramref name="collectionSelector" />，然后将那些序列元素中的每一个及其相应的源元素映射为结果元素的结果。</returns>
      <param name="source">一个要投影的值序列。</param>
      <param name="collectionSelector">一个应用于每个源元素的转换函数；函数的第二个参数表示源元素的索引。</param>
      <param name="resultSelector">一个应用于中间序列的每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TCollection">
        <paramref name="collectionSelector" /> 收集的中间元素的类型。</typeparam>
      <typeparam name="TResult">结果序列的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="collectionSelector" /> 或 <paramref name="resultSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SelectMany``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32,System.Collections.Generic.IEnumerable{``1}})">
      <summary>将序列的每个元素投影到 <see cref="T:System.Collections.Generic.IEnumerable`1" />，并将结果序列合并为一个序列。每个源元素的索引用于该元素的投影表。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，其元素是对输入序列的每个元素调用一对多转换函数的结果。</returns>
      <param name="source">一个要投影的值序列。</param>
      <param name="selector">一个应用于每个源元素的转换函数；函数的第二个参数表示源元素的索引。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TResult">
        <paramref name="selector" /> 返回的序列元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SequenceEqual``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>通过使用相应类型的默认相等比较器对序列的元素进行比较，以确定两个序列是否相等。</summary>
      <returns>如果根据相应类型的默认相等比较器，两个源序列的长度相等，且其相应元素相等，则为 true；否则为 false。</returns>
      <param name="first">一个用于比较 <paramref name="second" /> 的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="second">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，用于与第一个序列进行比较。</param>
      <typeparam name="TSource">输入序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> 或 <paramref name="second" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SequenceEqual``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 对两个序列的元素进行比较，以确定序列是否相等。</summary>
      <returns>如果根据 <paramref name="comparer" />，两个源序列的长度相等，且其相应元素相等，则为 true；否则为 false。</returns>
      <param name="first">一个用于比较 <paramref name="second" /> 的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="second">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，用于与第一个序列进行比较。</param>
      <param name="comparer">一个用于比较元素的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">输入序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> 或 <paramref name="second" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Single``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>返回序列的唯一元素；如果该序列并非恰好包含一个元素，则会引发异常。</summary>
      <returns>输入序列的单个元素。</returns>
      <param name="source">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，用于返回单个元素。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">输入序列包含多个元素。- 或 -输入序列为空。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Single``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>返回序列中满足指定条件的唯一元素；如果有多个这样的元素存在，则会引发异常。</summary>
      <returns>输入序列中满足条件的单个元素。</returns>
      <param name="source">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，用于从中返回单个元素。</param>
      <param name="predicate">用于测试元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有元素满足 <paramref name="predicate" /> 中的条件。- 或 -多个元素满足 <paramref name="predicate" /> 中的条件。- 或 -源序列为空。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SingleOrDefault``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>返回序列中的唯一元素；如果该序列为空，则返回默认值；如果该序列包含多个元素，此方法将引发异常。</summary>
      <returns>返回输入序列的单个元素；如果序列不包含任何元素，则返回 default(<paramref name="TSource" />)。</returns>
      <param name="source">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，用于返回单个元素。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">输入序列包含多个元素。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SingleOrDefault``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>返回序列中满足指定条件的唯一元素；如果这类元素不存在，则返回默认值；如果有多个元素满足该条件，此方法将引发异常。</summary>
      <returns>如果未找到这样的元素，则返回输入序列中满足条件的单个元素或 default (<paramref name="TSource" />)。</returns>
      <param name="source">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，用于从中返回单个元素。</param>
      <param name="predicate">用于测试元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Skip``1(System.Collections.Generic.IEnumerable{``0},System.Int32)">
      <summary>跳过序列中指定数量的元素，然后返回剩余的元素。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含输入序列中指定索引后出现的元素。</returns>
      <param name="source">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，用于从中返回元素。</param>
      <param name="count">返回剩余元素前要跳过的元素数量。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SkipWhile``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>只要满足指定的条件，就跳过序列中的元素，然后返回剩余元素。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含输入序列中的元素，该输入序列从线性系列中没有通过 <paramref name="predicate" /> 指定测试的第一个元素开始。</returns>
      <param name="source">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，用于从中返回元素。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SkipWhile``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32,System.Boolean})">
      <summary>只要满足指定的条件，就跳过序列中的元素，然后返回剩余元素。将在谓词函数的逻辑中使用元素的索引。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含输入序列中的元素，该输入序列从线性系列中没有通过 <paramref name="predicate" /> 指定测试的第一个元素开始。</returns>
      <param name="source">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，用于从中返回元素。</param>
      <param name="predicate">用于测试每个源元素是否满足条件的函数；该函数的第二个参数表示源元素的索引。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Decimal})">
      <summary>计算 <see cref="T:System.Decimal" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">一个要计算和的 <see cref="T:System.Decimal" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Decimal.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Double})">
      <summary>计算 <see cref="T:System.Double" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">一个要计算和的 <see cref="T:System.Double" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Int32})">
      <summary>计算 <see cref="T:System.Int32" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">一个要计算和的 <see cref="T:System.Int32" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Int32.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Int64})">
      <summary>计算 <see cref="T:System.Int64" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">一个要计算和的 <see cref="T:System.Int64" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Nullable{System.Decimal}})">
      <summary>计算可以为 null 的 <see cref="T:System.Decimal" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">要计算和的可以为 null 的 <see cref="T:System.Decimal" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Decimal.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Nullable{System.Double}})">
      <summary>计算可以为 null 的 <see cref="T:System.Double" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">要计算和的可以为 null 的 <see cref="T:System.Double" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Nullable{System.Int32}})">
      <summary>计算可以为 null 的 <see cref="T:System.Int32" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">要计算和的可以为 null 的 <see cref="T:System.Int32" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Int32.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Nullable{System.Int64}})">
      <summary>计算可以为 null 的 <see cref="T:System.Int64" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">要计算和的可以为 null 的 <see cref="T:System.Int64" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Nullable{System.Single}})">
      <summary>计算可以为 null 的 <see cref="T:System.Single" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">要计算和的可以为 null 的 <see cref="T:System.Single" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Single})">
      <summary>计算 <see cref="T:System.Single" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">一个要计算和的 <see cref="T:System.Single" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Decimal})">
      <summary>计算 <see cref="T:System.Decimal" /> 值序列的和，这些值是通过对输入序列中的每个元素调用转换函数得来的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">用于计算和的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Decimal.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Double})">
      <summary>计算 <see cref="T:System.Double" /> 值序列的和，这些值是通过对输入序列中的每个元素调用转换函数得来的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">用于计算和的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32})">
      <summary>计算 <see cref="T:System.Int32" /> 值序列的和，这些值是通过对输入序列中的每个元素调用转换函数得来的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">用于计算和的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Int32.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int64})">
      <summary>计算 <see cref="T:System.Int64" /> 值序列的和，这些值是通过对输入序列中的每个元素调用转换函数得来的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">用于计算和的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Decimal}})">
      <summary>计算可以为 null 的 <see cref="T:System.Decimal" /> 值序列的和，这些值是通过对输入序列中的每个元素调用转换函数得来的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">用于计算和的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Decimal.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Double}})">
      <summary>计算可以为 null 的 <see cref="T:System.Double" /> 值序列的和，这些值是通过对输入序列中的每个元素调用转换函数得来的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">用于计算和的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Int32}})">
      <summary>计算可以为 null 的 <see cref="T:System.Int32" /> 值序列的和，这些值是通过对输入序列中的每个元素调用转换函数得来的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">用于计算和的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Int32.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Int64}})">
      <summary>计算可以为 null 的 <see cref="T:System.Int64" /> 值序列的和，这些值是通过对输入序列中的每个元素调用转换函数得来的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">用于计算和的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Single}})">
      <summary>计算可以为 null 的 <see cref="T:System.Single" /> 值序列的和，这些值是通过对输入序列中的每个元素调用转换函数得来的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">用于计算和的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Single})">
      <summary>计算 <see cref="T:System.Single" /> 值序列的和，这些值是通过对输入序列中的每个元素调用转换函数得来的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">用于计算和的值序列。</param>
      <param name="selector">应用于每个元素的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Take``1(System.Collections.Generic.IEnumerable{``0},System.Int32)">
      <summary>从序列的开头返回指定数量的连续元素。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含输入序列开头的指定数量的元素。</returns>
      <param name="source">要从其返回元素的序列。</param>
      <param name="count">要返回的元素数量。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.TakeWhile``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>只要满足指定的条件，就会返回序列的元素。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含输入序列中出现在测试不再能够通过的元素之前的元素。</returns>
      <param name="source">要从其返回元素的序列。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.TakeWhile``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32,System.Boolean})">
      <summary>只要满足指定的条件，就会返回序列的元素。将在谓词函数的逻辑中使用元素的索引。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含输入序列中出现在测试不再能够通过的元素之前的元素。</returns>
      <param name="source">要从其返回元素的序列。</param>
      <param name="predicate">用于测试每个源元素是否满足条件的函数；该函数的第二个参数表示源元素的索引。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ThenBy``2(System.Linq.IOrderedEnumerable{``0},System.Func{``0,``1})">
      <summary>根据某个键按升序对序列中的元素执行后续排序。</summary>
      <returns>一个 <see cref="T:System.Linq.IOrderedEnumerable`1" />，其元素按键排序。</returns>
      <param name="source">一个 <see cref="T:System.Linq.IOrderedEnumerable`1" />，包含要排序的元素。</param>
      <param name="keySelector">用于从每个元素中提取键的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ThenBy``2(System.Linq.IOrderedEnumerable{``0},System.Func{``0,``1},System.Collections.Generic.IComparer{``1})">
      <summary>使用指定的比较器按升序对序列中的元素执行后续排序。</summary>
      <returns>一个 <see cref="T:System.Linq.IOrderedEnumerable`1" />，其元素按键排序。</returns>
      <param name="source">一个 <see cref="T:System.Linq.IOrderedEnumerable`1" />，包含要排序的元素。</param>
      <param name="keySelector">用于从每个元素中提取键的函数。</param>
      <param name="comparer">一个用于比较键的 <see cref="T:System.Collections.Generic.IComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ThenByDescending``2(System.Linq.IOrderedEnumerable{``0},System.Func{``0,``1})">
      <summary>根据某个键按降序对序列中的元素执行后续排序。</summary>
      <returns>一个 <see cref="T:System.Linq.IOrderedEnumerable`1" />，将根据某个键按降序对其元素进行排序。</returns>
      <param name="source">一个 <see cref="T:System.Linq.IOrderedEnumerable`1" />，包含要排序的元素。</param>
      <param name="keySelector">用于从每个元素中提取键的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ThenByDescending``2(System.Linq.IOrderedEnumerable{``0},System.Func{``0,``1},System.Collections.Generic.IComparer{``1})">
      <summary>使用指定的比较器按降序对序列中的元素执行后续排序。</summary>
      <returns>一个 <see cref="T:System.Linq.IOrderedEnumerable`1" />，将根据某个键按降序对其元素进行排序。</returns>
      <param name="source">一个 <see cref="T:System.Linq.IOrderedEnumerable`1" />，包含要排序的元素。</param>
      <param name="keySelector">用于从每个元素中提取键的函数。</param>
      <param name="comparer">一个用于比较键的 <see cref="T:System.Collections.Generic.IComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToArray``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>从 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 创建一个数组。</summary>
      <returns>一个包含输入序列中的元素的数组。</returns>
      <param name="source">要从其创建数组的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToDictionary``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1})">
      <summary>根据指定的键选择器函数，从 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 创建一个 <see cref="T:System.Collections.Generic.Dictionary`2" />。</summary>
      <returns>一个包含键和值的 <see cref="T:System.Collections.Generic.Dictionary`2" />。</returns>
      <param name="source">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，将从它创建一个 <see cref="T:System.Collections.Generic.Dictionary`2" />。</param>
      <param name="keySelector">用于从每个元素中提取键的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 为 null。- 或 -<paramref name="keySelector" /> 产生了一个 null 键。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="keySelector" /> 为两个元素产生了重复键。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToDictionary``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>根据指定的键选择器函数和键比较器，从 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 创建一个 <see cref="T:System.Collections.Generic.Dictionary`2" />。</summary>
      <returns>一个包含键和值的 <see cref="T:System.Collections.Generic.Dictionary`2" />。</returns>
      <param name="source">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，将从它创建一个 <see cref="T:System.Collections.Generic.Dictionary`2" />。</param>
      <param name="keySelector">用于从每个元素中提取键的函数。</param>
      <param name="comparer">一个用于对键进行比较的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 为 null。- 或 -<paramref name="keySelector" /> 产生了一个 null 键。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="keySelector" /> 为两个元素产生了重复键。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToDictionary``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2})">
      <summary>根据指定的键选择器和元素选择器函数，从 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 创建一个 <see cref="T:System.Collections.Generic.Dictionary`2" />。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.Dictionary`2" />，包含从输入序列中选择的类型为 <paramref name="TElement" /> 的值。</returns>
      <param name="source">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，将从它创建一个 <see cref="T:System.Collections.Generic.Dictionary`2" />。</param>
      <param name="keySelector">用于从每个元素中提取键的函数。</param>
      <param name="elementSelector">用于从每个元素产生结果元素值的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <typeparam name="TElement">
        <paramref name="elementSelector" /> 返回的值的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 或 <paramref name="elementSelector" /> 为 null。- 或 -<paramref name="keySelector" /> 产生了一个 null 键。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="keySelector" /> 为两个元素产生了重复键。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToDictionary``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>根据指定的键选择器函数、比较器和元素选择器函数从 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 创建一个 <see cref="T:System.Collections.Generic.Dictionary`2" />。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.Dictionary`2" />，包含从输入序列中选择的类型为 <paramref name="TElement" /> 的值。</returns>
      <param name="source">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，将从它创建一个 <see cref="T:System.Collections.Generic.Dictionary`2" />。</param>
      <param name="keySelector">用于从每个元素中提取键的函数。</param>
      <param name="elementSelector">用于从每个元素产生结果元素值的转换函数。</param>
      <param name="comparer">一个用于对键进行比较的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <typeparam name="TElement">
        <paramref name="elementSelector" /> 返回的值的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 或 <paramref name="elementSelector" /> 为 null。- 或 -<paramref name="keySelector" /> 产生了一个 null 键。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="keySelector" /> 为两个元素产生了重复键。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToList``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>从 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 创建一个 <see cref="T:System.Collections.Generic.List`1" />。</summary>
      <returns>一个包含输入序列中元素的 <see cref="T:System.Collections.Generic.List`1" />。</returns>
      <param name="source">要从其创建 <see cref="T:System.Collections.Generic.List`1" /> 的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToLookup``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1})">
      <summary>根据指定的键选择器函数，从 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 创建一个 <see cref="T:System.Linq.Lookup`2" />。</summary>
      <returns>一个包含键和值的 <see cref="T:System.Linq.Lookup`2" />。</returns>
      <param name="source">要从其创建 <see cref="T:System.Linq.Lookup`2" /> 的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="keySelector">用于从每个元素中提取键的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToLookup``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>根据指定的键选择器函数和键比较器，从 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 创建一个 <see cref="T:System.Linq.Lookup`2" />。</summary>
      <returns>一个包含键和值的 <see cref="T:System.Linq.Lookup`2" />。</returns>
      <param name="source">要从其创建 <see cref="T:System.Linq.Lookup`2" /> 的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="keySelector">用于从每个元素中提取键的函数。</param>
      <param name="comparer">一个用于对键进行比较的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToLookup``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2})">
      <summary>根据指定的键选择器和元素选择器函数，从 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 创建一个 <see cref="T:System.Linq.Lookup`2" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Lookup`2" />，包含从输入序列中选择的类型为 <paramref name="TElement" /> 的值。</returns>
      <param name="source">要从其创建 <see cref="T:System.Linq.Lookup`2" /> 的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="keySelector">用于从每个元素中提取键的函数。</param>
      <param name="elementSelector">用于从每个元素产生结果元素值的转换函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <typeparam name="TElement">
        <paramref name="elementSelector" /> 返回的值的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 或 <paramref name="elementSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToLookup``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>根据指定的键选择器函数、比较器和元素选择器函数，从 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 创建一个 <see cref="T:System.Linq.Lookup`2" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Lookup`2" />，包含从输入序列中选择的类型为 <paramref name="TElement" /> 的值。</returns>
      <param name="source">要从其创建 <see cref="T:System.Linq.Lookup`2" /> 的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="keySelector">用于从每个元素中提取键的函数。</param>
      <param name="elementSelector">用于从每个元素产生结果元素值的转换函数。</param>
      <param name="comparer">一个用于对键进行比较的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 返回的键的类型。</typeparam>
      <typeparam name="TElement">
        <paramref name="elementSelector" /> 返回的值的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 或 <paramref name="elementSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Union``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>通过使用默认的相等比较器生成两个序列的并集。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含两个输入序列中的非重复元素。</returns>
      <param name="first">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，它的非重复元素构成联合的第一个集。</param>
      <param name="second">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，它的非重复元素构成联合的第二个集。</param>
      <typeparam name="TSource">输入序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> 或 <paramref name="second" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Union``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>通过使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 生成两个序列的并集。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含两个输入序列中的非重复元素。</returns>
      <param name="first">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，它的非重复元素构成联合的第一个集。</param>
      <param name="second">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，它的非重复元素构成联合的第二个集。</param>
      <param name="comparer">用于对值进行比较的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">输入序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> 或 <paramref name="second" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Where``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>基于谓词筛选值序列。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含输入序列中满足条件的元素。</returns>
      <param name="source">要筛选的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Where``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32,System.Boolean})">
      <summary>基于谓词筛选值序列。将在谓词函数的逻辑中使用每个元素的索引。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含输入序列中满足条件的元素。</returns>
      <param name="source">要筛选的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="predicate">用于测试每个源元素是否满足条件的函数；该函数的第二个参数表示源元素的索引。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Zip``3(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``1},System.Func{``0,``1,``2})">
      <summary>将指定函数应用于两个序列的对应元素，以生成结果序列。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含两个输入序列的已合并元素。</returns>
      <param name="first">要合并的第一个序列。</param>
      <param name="second">要合并的第二个序列。</param>
      <param name="resultSelector">用于指定如何合并这两个序列的元素的函数。</param>
      <typeparam name="TFirst">第一个输入序列中的元素的类型。</typeparam>
      <typeparam name="TSecond">第二个输入序列中的元素的类型。</typeparam>
      <typeparam name="TResult">结果序列的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> 或 <paramref name="second" /> 为 null。</exception>
    </member>
    <member name="T:System.Linq.IGrouping`2">
      <summary>表示具有公共键的对象的集合。</summary>
      <typeparam name="TKey">
        <see cref="T:System.Linq.IGrouping`2" /> 的键的类型。此类型参数是协变。即可以使用指定的类型或派生程度更高的类型。有关协变和逆变的详细信息，请参阅 泛型中的协变和逆变。</typeparam>
      <typeparam name="TElement">
        <see cref="T:System.Linq.IGrouping`2" /> 的值的类型。</typeparam>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Linq.IGrouping`2.Key">
      <summary>获取 <see cref="T:System.Linq.IGrouping`2" /> 的键。</summary>
      <returns>
        <see cref="T:System.Linq.IGrouping`2" /> 的键。</returns>
    </member>
    <member name="T:System.Linq.ILookup`2">
      <summary>定义索引器、大小属性以及将键映射到 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 值序列的数据结构的布尔搜索方法。</summary>
      <typeparam name="TKey">
        <see cref="T:System.Linq.ILookup`2" /> 中的键的类型。</typeparam>
      <typeparam name="TElement">组成 <see cref="T:System.Linq.ILookup`2" /> 中的值的 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 序列中的元素的类型。</typeparam>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Linq.ILookup`2.Contains(`0)">
      <summary>确定指定的键是否位于 <see cref="T:System.Linq.ILookup`2" /> 中。</summary>
      <returns>如果 <paramref name="key" /> 在 <see cref="T:System.Linq.ILookup`2" /> 中，则为 true；否则为 false。</returns>
      <param name="key">要在 <see cref="T:System.Linq.ILookup`2" /> 中搜索的键。</param>
    </member>
    <member name="P:System.Linq.ILookup`2.Count">
      <summary>获取 <see cref="T:System.Linq.ILookup`2" /> 中的键/值对集合的数目。</summary>
      <returns>
        <see cref="T:System.Linq.ILookup`2" /> 中键/值集合对的数目。</returns>
    </member>
    <member name="P:System.Linq.ILookup`2.Item(`0)">
      <summary>获取按指定键进行索引的值的 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 序列。</summary>
      <returns>按指定键进行索引的值的 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 序列。</returns>
      <param name="key">所需值序列的键。</param>
    </member>
    <member name="T:System.Linq.IOrderedEnumerable`1">
      <summary>表示已排序序列。</summary>
      <typeparam name="TElement">序列中的元素的类型。</typeparam>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Linq.IOrderedEnumerable`1.CreateOrderedEnumerable``1(System.Func{`0,``0},System.Collections.Generic.IComparer{``0},System.Boolean)">
      <summary>根据某个键对 <see cref="T:System.Linq.IOrderedEnumerable`1" /> 的元素执行后续排序。</summary>
      <returns>一个 <see cref="T:System.Linq.IOrderedEnumerable`1" />，其元素按键排序。</returns>
      <param name="keySelector">用于提取每个元素的键的 <see cref="T:System.Func`2" />。</param>
      <param name="comparer">用于比较键在返回序列中的位置的 <see cref="T:System.Collections.Generic.IComparer`1" />。</param>
      <param name="descending">如果为 true，则对元素进行降序排序；如果为 false，则对元素进行升序排序。</param>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 生成的键的类型。</typeparam>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Linq.Lookup`2">
      <summary>表示映射到一个或多个值的各个键的集合。</summary>
      <typeparam name="TKey">
        <see cref="T:System.Linq.Lookup`2" /> 中的键的类型。</typeparam>
      <typeparam name="TElement">
        <see cref="T:System.Linq.Lookup`2" /> 中的每个 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 值的元素的类型。</typeparam>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Linq.Lookup`2.ApplyResultSelector``1(System.Func{`0,System.Collections.Generic.IEnumerable{`1},``0})">
      <summary>对每个键及其关联值应用转换函数，并返回结果。</summary>
      <returns>包含 <see cref="T:System.Linq.Lookup`2" /> 中的各个键/值对集合中的一个值的集合。</returns>
      <param name="resultSelector">从每个键及其关联值投影结果值的函数。</param>
      <typeparam name="TResult">
        <paramref name="resultSelector" /> 生成的结果值的类型。</typeparam>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Linq.Lookup`2.Contains(`0)">
      <summary>确定指定的键是否位于 <see cref="T:System.Linq.Lookup`2" /> 中。</summary>
      <returns>如果 <paramref name="key" /> 在 <see cref="T:System.Linq.Lookup`2" /> 中，则为 true；否则为 false。</returns>
      <param name="key">要在 <see cref="T:System.Linq.Lookup`2" /> 中查找的键。</param>
    </member>
    <member name="P:System.Linq.Lookup`2.Count">
      <summary>获取 <see cref="T:System.Linq.Lookup`2" /> 中的键/值对集合的数目。</summary>
      <returns>
        <see cref="T:System.Linq.Lookup`2" /> 中键/值集合对的数目。</returns>
    </member>
    <member name="M:System.Linq.Lookup`2.GetEnumerator">
      <summary>返回一个循环访问 <see cref="T:System.Linq.Lookup`2" /> 的泛型枚举数。</summary>
      <returns>
        <see cref="T:System.Linq.Lookup`2" /> 的一个枚举数。</returns>
    </member>
    <member name="P:System.Linq.Lookup`2.Item(`0)">
      <summary>获取按指定键进行索引的值的集合。</summary>
      <returns>按指定键进行索引的值的集合。</returns>
      <param name="key">所需值集合的键。</param>
    </member>
    <member name="M:System.Linq.Lookup`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回循环访问 <see cref="T:System.Linq.Lookup`2" /> 的枚举数。此类不能被继承。</summary>
      <returns>
        <see cref="T:System.Linq.Lookup`2" /> 的一个枚举数。</returns>
    </member>
  </members>
</doc>