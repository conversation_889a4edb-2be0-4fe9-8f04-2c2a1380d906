﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Linq.Expressions</name>
  </assembly>
  <members>
    <member name="T:System.Linq.IOrderedQueryable">
      <summary>表示排序操作的结果。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Linq.IOrderedQueryable`1">
      <summary>表示排序操作的结果。</summary>
      <typeparam name="T">数据源中数据的类型。此类型参数是协变。即可以使用指定的类型或派生程度更高的类型。有关协变和逆变的详细信息，请参阅 泛型中的协变和逆变。</typeparam>
    </member>
    <member name="T:System.Linq.IQueryable">
      <summary>提供对未指定数据类型的特定数据源的查询进行计算的功能。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Linq.IQueryable.ElementType">
      <summary>获取在执行与 <see cref="T:System.Linq.IQueryable" /> 的此实例关联的表达式目录树时返回的元素的类型。</summary>
      <returns>一个 <see cref="T:System.Type" />，表示在执行与之关联的表达式目录树时返回的元素的类型。</returns>
    </member>
    <member name="P:System.Linq.IQueryable.Expression">
      <summary>获取与 <see cref="T:System.Linq.IQueryable" /> 的实例关联的表达式目录树。</summary>
      <returns>与 <see cref="T:System.Linq.IQueryable" /> 的此实例关联的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="P:System.Linq.IQueryable.Provider">
      <summary>获取与此数据源关联的查询提供程序。</summary>
      <returns>与此数据源关联的 <see cref="T:System.Linq.IQueryProvider" />。</returns>
    </member>
    <member name="T:System.Linq.IQueryable`1">
      <summary>提供对数据类型已知的特定数据源的查询进行计算的功能。</summary>
      <typeparam name="T">数据源中数据的类型。此类型参数是协变。即可以使用指定的类型或派生程度更高的类型。有关协变和逆变的详细信息，请参阅 泛型中的协变和逆变。</typeparam>
    </member>
    <member name="T:System.Linq.IQueryProvider">
      <summary>定义用于创建和执行 <see cref="T:System.Linq.IQueryable" /> 对象所描述的查询的方法。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Linq.IQueryProvider.CreateQuery``1(System.Linq.Expressions.Expression)">
      <summary>构造一个 <see cref="T:System.Linq.IQueryable`1" /> 对象，该对象可计算指定表达式目录树所表示的查询。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，它可计算指定表达式目录树所表示的查询。</returns>
      <param name="expression">表示 LINQ 查询的表达式目录树。</param>
      <typeparam name="TElement">返回的 <see cref="T:System.Linq.IQueryable`1" /> 的元素的类型。</typeparam>
    </member>
    <member name="M:System.Linq.IQueryProvider.CreateQuery(System.Linq.Expressions.Expression)">
      <summary>构造一个 <see cref="T:System.Linq.IQueryable" /> 对象，该对象可计算指定表达式目录树所表示的查询。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable" />，它可计算指定表达式目录树所表示的查询。</returns>
      <param name="expression">表示 LINQ 查询的表达式目录树。</param>
    </member>
    <member name="M:System.Linq.IQueryProvider.Execute``1(System.Linq.Expressions.Expression)">
      <summary>执行指定表达式目录树所表示的强类型查询。</summary>
      <returns>执行指定查询所生成的值。</returns>
      <param name="expression">表示 LINQ 查询的表达式目录树。</param>
      <typeparam name="TResult">执行查询所生成的值的类型。</typeparam>
    </member>
    <member name="M:System.Linq.IQueryProvider.Execute(System.Linq.Expressions.Expression)">
      <summary>执行指定表达式目录树所表示的查询。</summary>
      <returns>执行指定查询所生成的值。</returns>
      <param name="expression">表示 LINQ 查询的表达式目录树。</param>
    </member>
    <member name="T:System.Linq.Expressions.BinaryExpression">
      <summary>表示包含二元运算符的表达式。</summary>
    </member>
    <member name="M:System.Linq.Expressions.BinaryExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>调度到此节点类型的特定 Visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 调用 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>对此节点进行访问的结果。</returns>
      <param name="visitor">对此节点进行访问的访问者。</param>
    </member>
    <member name="P:System.Linq.Expressions.BinaryExpression.CanReduce">
      <summary>获取一个值，该值指示是否可以减小此表达式树节点。</summary>
      <returns>如果可以简化表达式树节点，则为 True；否则为 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.BinaryExpression.Conversion">
      <summary>获取合并运算或复合赋值运算使用的类型转换函数。</summary>
      <returns>一个表示类型转换函数的 <see cref="T:System.Linq.Expressions.LambdaExpression" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.BinaryExpression.IsLifted">
      <summary>获取一个值，该值指示表达式目录树节点是否表示对运算符的提升调用。</summary>
      <returns>如果该节点表示提升调用，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull">
      <summary>获取一个值，该值指示表达式目录树节点是否表示对运算符（其返回类型提升为可以为 null 的类型）的提升调用。</summary>
      <returns>如果该运算符的返回类型提升为可以为 null 的类型，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.BinaryExpression.Left">
      <summary>获取二元运算的左操作数。</summary>
      <returns>表示二元运算的左操作数的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.BinaryExpression.Method">
      <summary>获取二元运算的实现方法。</summary>
      <returns>表示实现方法的 <see cref="T:System.Reflection.MethodInfo" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.BinaryExpression.Reduce">
      <summary>将二进制表达式节点简化为更简单的表达式。</summary>
      <returns>已简化的表达式。</returns>
    </member>
    <member name="P:System.Linq.Expressions.BinaryExpression.Right">
      <summary>获取二元运算的右操作数。</summary>
      <returns>表示二元运算的右操作数的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.BinaryExpression.Update(System.Linq.Expressions.Expression,System.Linq.Expressions.LambdaExpression,System.Linq.Expressions.Expression)">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="left">结果的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 属性。</param>
      <param name="conversion">结果的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 属性。</param>
      <param name="right">结果的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.BlockExpression">
      <summary>表示一个包含可在其中定义变量的表达式序列的块。</summary>
    </member>
    <member name="M:System.Linq.Expressions.BlockExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>调度到此节点类型的特定 Visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 调用 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>对此节点进行访问的结果。</returns>
      <param name="visitor">对此节点进行访问的访问者。</param>
    </member>
    <member name="P:System.Linq.Expressions.BlockExpression.Expressions">
      <summary>获取此块中的表达式。</summary>
      <returns>包含此块中所有表达式的只读集合。</returns>
    </member>
    <member name="P:System.Linq.Expressions.BlockExpression.NodeType">
      <summary>返回此表达式的节点类型。扩展节点应在重写此方法时返回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>该表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.BlockExpression.Result">
      <summary>获取此块中的最后一个表达式。</summary>
      <returns>表示此块中最后一个表达式的 <see cref="T:System.Linq.Expressions.Expression" /> 对象。</returns>
    </member>
    <member name="P:System.Linq.Expressions.BlockExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.BlockExpression.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.BlockExpression.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression},System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="variables">结果的 <see cref="P:System.Linq.Expressions.BlockExpression.Variables" /> 属性。</param>
      <param name="expressions">结果的 <see cref="P:System.Linq.Expressions.BlockExpression.Expressions" /> 属性。</param>
    </member>
    <member name="P:System.Linq.Expressions.BlockExpression.Variables">
      <summary>获取在此块中定义的变量。</summary>
      <returns>包含在此块中定义的所有变量的只读集合。</returns>
    </member>
    <member name="T:System.Linq.Expressions.CatchBlock">
      <summary>表示 try 块中的 catch 语句。</summary>
    </member>
    <member name="P:System.Linq.Expressions.CatchBlock.Body">
      <summary>获取 catch 块的主体。</summary>
      <returns>表示 catch 主体的 <see cref="T:System.Linq.Expressions.Expression" /> 对象。</returns>
    </member>
    <member name="P:System.Linq.Expressions.CatchBlock.Filter">
      <summary>获取 <see cref="T:System.Linq.Expressions.CatchBlock" /> 筛选器的主体。</summary>
      <returns>表示 <see cref="T:System.Linq.Expressions.CatchBlock" /> 筛选器的主体的 <see cref="T:System.Linq.Expressions.Expression" /> 对象。</returns>
    </member>
    <member name="P:System.Linq.Expressions.CatchBlock.Test">
      <summary>获取此处理程序捕捉的 <see cref="T:System.Exception" /> 的类型。</summary>
      <returns>
        <see cref="T:System.Type" /> 对象，表示此处理程序捕捉的 <see cref="T:System.Exception" /> 的类型。</returns>
    </member>
    <member name="M:System.Linq.Expressions.CatchBlock.ToString">
      <summary>返回表示当前 <see cref="T:System.Object" /> 的 <see cref="T:System.String" />。</summary>
      <returns>
        <see cref="T:System.String" />，表示当前的 <see cref="T:System.Object" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.CatchBlock.Update(System.Linq.Expressions.ParameterExpression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="variable">结果的 <see cref="P:System.Linq.Expressions.CatchBlock.Variable" /> 属性。</param>
      <param name="filter">结果的 <see cref="P:System.Linq.Expressions.CatchBlock.Filter" /> 属性。</param>
      <param name="body">结果的 <see cref="P:System.Linq.Expressions.CatchBlock.Body" /> 属性。</param>
    </member>
    <member name="P:System.Linq.Expressions.CatchBlock.Variable">
      <summary>获取对此处理程序捕捉的 <see cref="T:System.Exception" /> 对象的引用。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ParameterExpression" /> 对象，表示对此处理程序捕捉的 <see cref="T:System.Exception" /> 对象的引用。</returns>
    </member>
    <member name="T:System.Linq.Expressions.ConditionalExpression">
      <summary>表示包含条件运算符的表达式。</summary>
    </member>
    <member name="M:System.Linq.Expressions.ConditionalExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>调度到此节点类型的特定 Visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 调用 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>对此节点进行访问的结果。</returns>
      <param name="visitor">对此节点进行访问的访问者。</param>
    </member>
    <member name="P:System.Linq.Expressions.ConditionalExpression.IfFalse">
      <summary>获取当测试的计算结果为 false 时要执行的表达式。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.Expression" />，表示当测试结果为 false 时要执行的表达式。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ConditionalExpression.IfTrue">
      <summary>获取当测试的计算结果为 true 时要执行的表达式。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.Expression" />，表示当测试为 true 时要执行的表达式。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ConditionalExpression.NodeType">
      <summary>返回此表达式的节点类型。扩展节点应在重写此方法时返回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>该表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ConditionalExpression.Test">
      <summary>获取条件运算的测试。</summary>
      <returns>一个表示条件运算的测试的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ConditionalExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.ConditionalExpression.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.ConditionalExpression.Update(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="test">结果的 <see cref="P:System.Linq.Expressions.ConditionalExpression.Test" /> 属性。</param>
      <param name="ifTrue">结果的 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfTrue" /> 属性。</param>
      <param name="ifFalse">结果的 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfFalse" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.ConstantExpression">
      <summary>表示具有常量值的表达式。</summary>
    </member>
    <member name="M:System.Linq.Expressions.ConstantExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>调度到此节点类型的特定 Visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 调用 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>对此节点进行访问的结果。</returns>
      <param name="visitor">对此节点进行访问的访问者。</param>
    </member>
    <member name="P:System.Linq.Expressions.ConstantExpression.NodeType">
      <summary>返回此 Expression 的节点类型。扩展节点应在重写此方法时返回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>该表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ConstantExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.ConstantExpression.Type" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ConstantExpression.Value">
      <summary>获取常量表达式的值。</summary>
      <returns>
        <see cref="T:System.Object" /> 等于所表示的表达式的值。</returns>
    </member>
    <member name="T:System.Linq.Expressions.DebugInfoExpression">
      <summary>发出或清除调试信息的序列点。这允许调试器在调试时突出显示正确的源代码。</summary>
    </member>
    <member name="M:System.Linq.Expressions.DebugInfoExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>调度到此节点类型的特定 Visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 调用 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>对此节点进行访问的结果。</returns>
      <param name="visitor">对此节点进行访问的访问者。</param>
    </member>
    <member name="P:System.Linq.Expressions.DebugInfoExpression.Document">
      <summary>获取表示源文件的 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" />。</summary>
      <returns>表示源文件的 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.DebugInfoExpression.EndColumn">
      <summary>获取此 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的结束列。</summary>
      <returns>用于生成包装表达式的代码的结束列号。</returns>
    </member>
    <member name="P:System.Linq.Expressions.DebugInfoExpression.EndLine">
      <summary>获取此 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的结束行。</summary>
      <returns>用于生成包装表达式的代码的结束行号。</returns>
    </member>
    <member name="P:System.Linq.Expressions.DebugInfoExpression.IsClear">
      <summary>获取指示 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 是否用于清除序列点的值。</summary>
      <returns>如果 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 用于清除序列点，则为 True；否则为 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.DebugInfoExpression.NodeType">
      <summary>返回此 <see cref="T:System.Linq.Expressions.Expression" /> 的节点类型。</summary>
      <returns>用于表示此表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.DebugInfoExpression.StartColumn">
      <summary>获取此 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的起始列。</summary>
      <returns>用于生成包装表达式的代码的起始列号。</returns>
    </member>
    <member name="P:System.Linq.Expressions.DebugInfoExpression.StartLine">
      <summary>获取此 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的起始行。</summary>
      <returns>用于生成包装表达式的代码的起始行号。</returns>
    </member>
    <member name="P:System.Linq.Expressions.DebugInfoExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.DebugInfoExpression.Type" />。</returns>
    </member>
    <member name="T:System.Linq.Expressions.DefaultExpression">
      <summary>表示类型或空表达式的默认值。</summary>
    </member>
    <member name="P:System.Linq.Expressions.DefaultExpression.NodeType">
      <summary>返回此表达式的节点类型。扩展节点应在重写此方法时返回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>该表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.DefaultExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.DefaultExpression.Type" />。</returns>
    </member>
    <member name="T:System.Linq.Expressions.ElementInit">
      <summary>表示 <see cref="T:System.Collections.IEnumerable" /> 集合的单个元素的初始值设定项。</summary>
    </member>
    <member name="P:System.Linq.Expressions.ElementInit.AddMethod">
      <summary>获取用于将某个元素添加到 <see cref="T:System.Collections.IEnumerable" /> 集合的实例方法。</summary>
      <returns>表示用于将某个元素添加到集合的实例方法的 <see cref="T:System.Reflection.MethodInfo" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ElementInit.Arguments">
      <summary>获取传递给某一方法的参数集合，该方法会将元素添加到 <see cref="T:System.Collections.IEnumerable" /> 集合中。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" /> 对象的 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />，这些对象表示用于将元素添加到集合中的方法的参数。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ElementInit.System#Linq#Expressions#IArgumentProvider#ArgumentCount"></member>
    <member name="M:System.Linq.Expressions.ElementInit.System#Linq#Expressions#IArgumentProvider#GetArgument(System.Int32)"></member>
    <member name="M:System.Linq.Expressions.ElementInit.ToString">
      <summary>返回 <see cref="T:System.Linq.Expressions.ElementInit" /> 对象的文本表示形式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ElementInit" /> 对象的文本表示形式。</returns>
    </member>
    <member name="M:System.Linq.Expressions.ElementInit.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="arguments">结果的 <see cref="P:System.Linq.Expressions.ElementInit.Arguments" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.Expression">
      <summary>提供一种基类，表示表达式树节点的类派生自该基类。它还包含用来创建各种节点类型的 static（在 Visual Basic 中为 Shared）工厂方法。这是一个 abstract 类。</summary>
    </member>
    <member name="M:System.Linq.Expressions.Expression.#ctor">
      <summary>构造 <see cref="T:System.Linq.Expressions.Expression" /> 的新实例。</summary>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>调度到此节点类型的特定 Visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 调用 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>对此节点进行访问的结果。</returns>
      <param name="visitor">对此节点进行访问的访问者。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Add(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示不进行溢出检查的算术加法运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Add" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义加法运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Add(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示不进行溢出检查的算术加法运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。可指定实现方法。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Add" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义加法运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AddAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示不进行溢出检查的加法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.AddAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AddAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示不进行溢出检查的加法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.AddAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AddAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>创建一个表示不进行溢出检查的加法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.AddAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要将 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AddAssignChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示进行溢出检查的加法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.AddAssignChecked" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AddAssignChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示进行溢出检查的加法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.AddAssignChecked" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AddAssignChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>创建一个表示进行溢出检查的加法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.AddAssignChecked" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要将 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AddChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示进行溢出检查的算术加法运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.AddChecked" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义加法运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AddChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示进行溢出检查的算术加法运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。可指定实现方法。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.AddChecked" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义加法运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.And(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示按位 <see cref="T:System.Linq.Expressions.BinaryExpression" /> 运算的 AND。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.And" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 AND.Type 和 <paramref name="left" />.Type 定义按位 <paramref name="right" /> 运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.And(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示按位 <see cref="T:System.Linq.Expressions.BinaryExpression" /> 运算的 AND。可指定实现方法。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.And" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 AND.Type 和 <paramref name="left" />.Type 定义按位 <paramref name="right" /> 运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AndAlso(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，它表示仅在第一个操作数的计算结果为 AND 时才计算第二个操作数的条件 true 运算。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.AndAlso" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 AND.Type 和 <paramref name="left" />.Type 定义按位 <paramref name="right" /> 运算符。- 或 -<paramref name="left" />.Type 和 <paramref name="right" />.Type 不是同一布尔值类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AndAlso(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，它表示仅在第一个操作数解析为 true 时，才计算第二个操作数的条件 AND 运算。可指定实现方法。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.AndAlso" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 AND.Type 和 <paramref name="left" />.Type 定义按位 <paramref name="right" /> 运算符。- 或 -<paramref name="method" /> 为 null 并且 <paramref name="left" />.Type 和 <paramref name="right" />.Type 不是同一布尔值类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AndAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示按位 AND 赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.AndAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AndAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示按位 AND 赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.AndAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AndAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>创建一个表示按位 AND 赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.AndAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要将 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ArrayAccess(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个用于访问多维数组的 <see cref="T:System.Linq.Expressions.IndexExpression" />。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.IndexExpression" />。</returns>
      <param name="array">一个表示多维数组的表达式。</param>
      <param name="indexes">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用于为数组编制索引的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ArrayAccess(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression[])">
      <summary>创建一个用于访问数组的 <see cref="T:System.Linq.Expressions.IndexExpression" />。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.IndexExpression" />。</returns>
      <param name="array">一个表示要编制索引的数组的表达式。</param>
      <param name="indexes">一个数组，其中包含用于为数组编制索引的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ArrayIndex(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，它表示将数组索引运算符应用到多个级别的数组中。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，并且其 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 属性设置为指定值。</returns>
      <param name="array">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" />。</param>
      <param name="indexes">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 或 <paramref name="indexes" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />.Type 不表示数组类型。- 或 -<paramref name="array" />.Type 的秩与 <paramref name="indexes" /> 中的元素数量不匹配。- 或 -<see cref="P:System.Linq.Expressions.Expression.Type" /> 的一个或多个元素的 <paramref name="indexes" /> 属性不表示 <see cref="T:System.Int32" /> 类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ArrayIndex(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，它表示将数组索引运算符应用到级别一的数组中。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.ArrayIndex" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="array">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="index">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 或 <paramref name="index" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />.Type 不表示数组类型。- 或 -<paramref name="array" />.Type 表示秩不为 1 的数组类型。- 或 -<paramref name="index" />.Type 不表示 <see cref="T:System.Int32" /> 类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ArrayIndex(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression[])">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，它表示将数组索引运算符应用到多维数组中。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，并且其 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 属性设置为指定值。</returns>
      <param name="array">
        <see cref="T:System.Linq.Expressions.Expression" /> 实例的数组 - 数组索引操作的索引。</param>
      <param name="indexes">用于填充 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 或 <paramref name="indexes" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />.Type 不表示数组类型。- 或 -<paramref name="array" />.Type 的秩与 <paramref name="indexes" /> 中的元素数量不匹配。- 或 -<see cref="P:System.Linq.Expressions.Expression.Type" /> 的一个或多个元素的 <paramref name="indexes" /> 属性不表示 <see cref="T:System.Int32" /> 类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ArrayLength(System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它表示获取一维数组的长度的表达式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" /> 的 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.ArrayLength" />，<see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 属性等于 <paramref name="array" />。</returns>
      <param name="array">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />.Type 不表示数组类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Assign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Assign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Bind(System.Reflection.MemberInfo,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.MemberAssignment" />，它表示字段或属性的初始化。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberAssignment" /> 的 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 等于 <see cref="F:System.Linq.Expressions.MemberBindingType.Assignment" /> 且 <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 和 <see cref="P:System.Linq.Expressions.MemberAssignment.Expression" /> 属性设置为指定值。</returns>
      <param name="member">要将 <see cref="T:System.Reflection.MemberInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MemberBinding.Member" />。</param>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MemberAssignment.Expression" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="member" /> 或 <paramref name="expression" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="member" /> 不表示字段或属性。- 或 -<paramref name="member" /> 所表示的属性没有 set 访问器。- 或 -<paramref name="expression" />.Type 不能赋给 <paramref name="member" /> 所表示的字段或属性的类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Bind(System.Reflection.MethodInfo,System.Linq.Expressions.Expression)">
      <summary>使用属性访问器方法，创建一个表示成员初始化的 <see cref="T:System.Linq.Expressions.MemberAssignment" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberAssignment" /> 的 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 属性等于 <see cref="F:System.Linq.Expressions.MemberBindingType.Assignment" />，<see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 属性设置为 <see cref="T:System.Reflection.PropertyInfo" />（表示 <paramref name="propertyAccessor" /> 中访问的属性），<see cref="P:System.Linq.Expressions.MemberAssignment.Expression" /> 属性设置为 <paramref name="expression" />。</returns>
      <param name="propertyAccessor">一个表示属性访问器方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MemberAssignment.Expression" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyAccessor" /> 或 <paramref name="expression" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="propertyAccessor" /> 不表示属性访问器方法。- 或 -<paramref name="propertyAccessor" /> 所访问的属性没有 set 访问器。- 或 -<paramref name="expression" />.Type 不能赋给 <paramref name="member" /> 所表示的字段或属性的类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BlockExpression" />，其中包含给定表达式，但不包含任何变量。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="expressions">块中的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression},System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BlockExpression" />，其中包含给定的变量和表达式。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="variables">块中的变量。</param>
      <param name="expressions">块中的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression},System.Linq.Expressions.Expression[])">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BlockExpression" />，其中包含给定的变量和表达式。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="variables">块中的变量。</param>
      <param name="expressions">块中的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BlockExpression" />，其中包含两个表达式，但不包含任何变量。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="arg0">块中的第一个表达式。</param>
      <param name="arg1">块中的第二个表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BlockExpression" />，其中包含三个表达式，但不包含任何变量。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="arg0">块中的第一个表达式。</param>
      <param name="arg1">块中的第二个表达式。</param>
      <param name="arg2">块中的第三个表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BlockExpression" />，其中包含四个表达式，但不包含任何变量。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="arg0">块中的第一个表达式。</param>
      <param name="arg1">块中的第二个表达式。</param>
      <param name="arg2">块中的第三个表达式。</param>
      <param name="arg3">块中的第四个表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BlockExpression" />，其中包含五个表达式，但不包含任何变量。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="arg0">块中的第一个表达式。</param>
      <param name="arg1">块中的第二个表达式。</param>
      <param name="arg2">块中的第三个表达式。</param>
      <param name="arg3">块中的第四个表达式。</param>
      <param name="arg4">块中的第五个表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Linq.Expressions.Expression[])">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BlockExpression" />，其中包含给定表达式，但不包含任何变量。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="expressions">块中的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Type,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BlockExpression" />，其中包含给定表达式和特定结果类型，但不包含任何变量。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="type">块的结果类型。</param>
      <param name="expressions">块中的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Type,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression},System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BlockExpression" />，其中包含给定的变量和表达式。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="type">块的结果类型。</param>
      <param name="variables">块中的变量。</param>
      <param name="expressions">块中的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Type,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression},System.Linq.Expressions.Expression[])">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BlockExpression" />，其中包含给定的变量和表达式。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="type">块的结果类型。</param>
      <param name="variables">块中的变量。</param>
      <param name="expressions">块中的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Type,System.Linq.Expressions.Expression[])">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BlockExpression" />，其中包含给定表达式和特定结果类型，但不包含任何变量。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="type">块的结果类型。</param>
      <param name="expressions">块中的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Break(System.Linq.Expressions.LabelTarget)">
      <summary>创建一个表示 break 语句的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等于 Break，其 <see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 属性设置为 <paramref name="target" />，此外还有一个在跳转时将传递给目标标签的 null 值。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 将跳至的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Break(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression)">
      <summary>创建一个表示 break 语句的 <see cref="T:System.Linq.Expressions.GotoExpression" />。可以指定在跳转时传递给标签的值。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等于 Break，其 <see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 属性设置为 <paramref name="target" />，此外还有将在跳转时传递给目标标签的 <paramref name="value" />。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 将跳至的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="value">将在跳转时传递给关联标签的值。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Break(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression,System.Type)">
      <summary>创建一个表示具有指定类型的 break 语句的 <see cref="T:System.Linq.Expressions.GotoExpression" />。可以指定在跳转时传递给标签的值。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等于 Break，其 <see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 属性设置为 <paramref name="target" />，并且其 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性设置为 <paramref name="type" />，此外还有一个在跳转时将传递给目标标签的 <paramref name="value" />。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 将跳至的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="value">将在跳转时传递给关联标签的值。</param>
      <param name="type">要将 <see cref="T:System.Type" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Break(System.Linq.Expressions.LabelTarget,System.Type)">
      <summary>创建一个表示具有指定类型的 break 语句的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等于 Break，其 <see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 属性设置为 <paramref name="target" />，并且其 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性设置为 <paramref name="type" />。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 将跳至的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="type">要将 <see cref="T:System.Type" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，它表示调用不带参数的方法。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，并且其 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 属性设置为指定值。</returns>
      <param name="instance">一个 <see cref="T:System.Linq.Expressions.Expression" />，它指定一个实例方法调用，对于 null 方法（在 Visual Basic 中为 static 方法）应传递 Shared。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="method" /> 为 null。- 或 -<paramref name="instance" /> 为 null，并且 <paramref name="method" /> 表示实例方法。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="instance" />.Type 不能赋给 <paramref name="method" /> 所表示的方法的声明类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个表示调用带参数的方法的 <see cref="T:System.Linq.Expressions.MethodCallExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，并且其 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" />、<see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 属性设置为指定值。</returns>
      <param name="instance">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" />，它为 null（在 Visual Basic 中则为static）方法传递 Shared。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" />。</param>
      <param name="arguments">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="method" /> 为 null。- 或 -<paramref name="instance" /> 为 null，并且 <paramref name="method" /> 表示实例方法。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="instance" />.Type 不能赋给 <paramref name="method" /> 所表示的方法的声明类型。- 或 -<paramref name="arguments" /> 中的元素数量与 <paramref name="method" /> 所表示的方法的参数数量不相等。- 或 -<paramref name="arguments" /> 的一个或多个元素不能赋给 <paramref name="method" /> 所表示的方法的相应参数。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，它表示对采用两个参数的方法的调用。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，并且其 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 属性设置为指定值。</returns>
      <param name="instance">指定一个实例调用的实例的 <see cref="T:System.Linq.Expressions.Expression" />。（对于 static 方法（在 Visual Basic 中为 Shared 方法）应传递 null）。</param>
      <param name="method">表示目标方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
      <param name="arg0">用于表示第一个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg1">用于表示第二个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，它表示对使用三个参数的方法的调用。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，并且其 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 属性设置为指定值。</returns>
      <param name="instance">指定一个实例调用的实例的 <see cref="T:System.Linq.Expressions.Expression" />。（对于 static 方法（在 Visual Basic 中为 Shared 方法）应传递 null）。</param>
      <param name="method">表示目标方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
      <param name="arg0">用于表示第一个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg1">用于表示第二个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg2">用于表示第三个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.Expression[])">
      <summary>创建一个表示调用带参数的方法的 <see cref="T:System.Linq.Expressions.MethodCallExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，并且其 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" />、<see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 属性设置为指定值。</returns>
      <param name="instance">一个 <see cref="T:System.Linq.Expressions.Expression" />，它指定一个实例方法调用，对于 null 方法（在 Visual Basic 中为 static 方法）应传递 Shared。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" />。</param>
      <param name="arguments">用于填充 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="method" /> 为 null。- 或 -<paramref name="instance" /> 为 null，并且 <paramref name="method" /> 表示实例方法。- 或 -<paramref name="arguments" /> 不为 null，且其一个或多个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="instance" />.Type 不能赋给 <paramref name="method" /> 所表示的方法的声明类型。- 或 -<paramref name="arguments" /> 中的元素数量与 <paramref name="method" /> 所表示的方法的参数数量不相等。- 或 -<paramref name="arguments" /> 的一个或多个元素不能赋给 <paramref name="method" /> 所表示的方法的相应参数。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Linq.Expressions.Expression,System.String,System.Type[],System.Linq.Expressions.Expression[])">
      <summary>通过调用合适的工厂方法，创建一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，它表示方法调用。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MethodCallExpression" /> 的 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，<see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 属性等于 <paramref name="instance" />，<see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 设置为表示指定实例方法的 <see cref="T:System.Reflection.MethodInfo" /> 且 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 设置为指定参数。</returns>
      <param name="instance">将搜索 <see cref="T:System.Linq.Expressions.Expression" /> 的 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性值，查看其是否有特定方法。</param>
      <param name="methodName">方法的名称。</param>
      <param name="typeArguments">指定泛型方法的类型参数的 <see cref="T:System.Type" /> 对象的数组。当 methodName 指定非泛型方法时，此参数应为 null。</param>
      <param name="arguments">
        <see cref="T:System.Linq.Expressions.Expression" /> 对象的数组，表示方法的参数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> 或 <paramref name="methodName" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">在 <paramref name="methodName" />.Type 或其基类型中找不到这样的方法：名称为 <paramref name="typeArguments" />、类型参数与 <paramref name="arguments" /> 相匹配，且参数类型与 <paramref name="instance" /> 相匹配。- 或 -在 <paramref name="methodName" />.Type 或其基类型中找到多个这样的方法：名称为 <paramref name="typeArguments" />，类型参数与 <paramref name="arguments" /> 相匹配，参数类型与 <paramref name="instance" /> 相匹配。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Reflection.MethodInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，它表示对 static 方法（在 Visual Basic 中为 Shared 方法）的调用。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，并且其 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 属性设置为指定值。</returns>
      <param name="method">表示目标方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
      <param name="arguments">表示调用参数的 <see cref="T:System.Linq.Expressions.Expression" /> 的集合。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Reflection.MethodInfo,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，它表示对使用一个参数的 static 方法（在 Visual Basic 中为 Shared 方法）的调用。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，并且其 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 属性设置为指定值。</returns>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" />。</param>
      <param name="arg0">用于表示第一个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="method" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Reflection.MethodInfo,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，它表示对使用两个参数的静态方法的调用。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，并且其 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 属性设置为指定值。</returns>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" />。</param>
      <param name="arg0">用于表示第一个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg1">用于表示第二个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="method" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Reflection.MethodInfo,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，它表示对使用三个参数的静态方法的调用。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，并且其 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 属性设置为指定值。</returns>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" />。</param>
      <param name="arg0">用于表示第一个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg1">用于表示第二个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg2">用于表示第三个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="method" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Reflection.MethodInfo,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，它表示对使用四个参数的静态方法的调用。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，并且其 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 属性设置为指定值。</returns>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" />。</param>
      <param name="arg0">用于表示第一个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg1">用于表示第二个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg2">用于表示第三个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg3">用于表示第四个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="method" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Reflection.MethodInfo,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，它表示对使用五个参数的静态方法的调用。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，并且其 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 属性设置为指定值。</returns>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" />。</param>
      <param name="arg0">用于表示第一个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg1">用于表示第二个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg2">用于表示第三个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg3">用于表示第四个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg4">用于表示第五个参数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="method" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Reflection.MethodInfo,System.Linq.Expressions.Expression[])">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，它表示调用有参数的 static（在 Visual Basic 中为 Shared）方法。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，并且其 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 属性设置为指定值。</returns>
      <param name="method">
        <see cref="T:System.Reflection.MethodInfo" /> 表示 static（在 Visual Basic 中为 Shared）方法，以将 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 属性设置为与其相等。</param>
      <param name="arguments">用于填充 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="method" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arguments" /> 中的元素数量与 <paramref name="method" /> 所表示的方法的参数数量不相等。- 或 -<paramref name="arguments" /> 的一个或多个元素不能赋给 <paramref name="method" /> 所表示的方法的相应参数。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Type,System.String,System.Type[],System.Linq.Expressions.Expression[])">
      <summary>通过调用合适的工厂方法，创建一个 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，它表示对 static（在 Visual Basic 中为 Shared）方法的调用。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MethodCallExpression" /> 的 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，<see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 属性设置为表示指定的 <see cref="T:System.Reflection.MethodInfo" />（在 Visual Basic 中为 static）方法的 Shared，并且 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 属性设置为指定参数。</returns>
      <param name="type">
        <see cref="T:System.Type" /> 指定包含指定的 static（在 Visual Basic 中为 Shared）方法的类型。</param>
      <param name="methodName">方法的名称。</param>
      <param name="typeArguments">指定泛型方法的类型参数的 <see cref="T:System.Type" /> 对象的数组。当 methodName 指定非泛型方法时，此参数应为 null。</param>
      <param name="arguments">
        <see cref="T:System.Linq.Expressions.Expression" /> 对象的数组，表示方法的参数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> 或 <paramref name="methodName" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">在 <paramref name="methodName" /> 或其基类型中找不到这样的方法：名称为 <paramref name="typeArguments" />、类型参数与 <paramref name="arguments" /> 相匹配，且参数类型与 <paramref name="type" /> 相匹配。- 或 -在 <paramref name="methodName" /> 或其基类型中找到多个这样的方法：名称为 <paramref name="typeArguments" />，类型参数与 <paramref name="arguments" /> 相匹配，参数类型与 <paramref name="type" /> 相匹配。</exception>
    </member>
    <member name="P:System.Linq.Expressions.Expression.CanReduce">
      <summary>指示可将节点简化为更简单的节点。如果返回 true，则可以调用 Reduce() 以生成简化形式。</summary>
      <returns>如果可以简化节点，则为 True；否则为 false。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Catch(System.Linq.Expressions.ParameterExpression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示 catch 语句的 <see cref="T:System.Linq.Expressions.CatchBlock" />，其中具有对已捕获的 <see cref="T:System.Exception" /> 对象的引用以便在处理程序主体中使用。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.CatchBlock" />。</returns>
      <param name="variable">一个 <see cref="T:System.Linq.Expressions.ParameterExpression" />，它表示对此处理程序捕获的 <see cref="T:System.Exception" /> 对象的引用。</param>
      <param name="body">catch 语句的主体。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Catch(System.Linq.Expressions.ParameterExpression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示 catch 语句的 <see cref="T:System.Linq.Expressions.CatchBlock" />，其中具有 <see cref="T:System.Exception" /> 筛选器和对已捕获的 <see cref="T:System.Exception" /> 对象的引用。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.CatchBlock" />。</returns>
      <param name="variable">一个 <see cref="T:System.Linq.Expressions.ParameterExpression" />，它表示对此处理程序捕获的 <see cref="T:System.Exception" /> 对象的引用。</param>
      <param name="body">catch 语句的主体。</param>
      <param name="filter">
        <see cref="T:System.Exception" /> 筛选器的主体。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Catch(System.Type,System.Linq.Expressions.Expression)">
      <summary>创建一个表示 catch 语句的 <see cref="T:System.Linq.Expressions.CatchBlock" />。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.CatchBlock" />。</returns>
      <param name="type">此 <see cref="P:System.Linq.Expressions.Expression.Type" /> 将处理的 <see cref="T:System.Exception" /> 的 <see cref="T:System.Linq.Expressions.CatchBlock" />。</param>
      <param name="body">catch 语句的主体。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Catch(System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示 catch 语句的 <see cref="T:System.Linq.Expressions.CatchBlock" />，其中具有 <see cref="T:System.Exception" /> 筛选器，但没有对已捕获的 <see cref="T:System.Exception" /> 对象的引用。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.CatchBlock" />。</returns>
      <param name="type">此 <see cref="P:System.Linq.Expressions.Expression.Type" /> 将处理的 <see cref="T:System.Exception" /> 的 <see cref="T:System.Linq.Expressions.CatchBlock" />。</param>
      <param name="body">catch 语句的主体。</param>
      <param name="filter">
        <see cref="T:System.Exception" /> 筛选器的主体。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ClearDebugInfo(System.Linq.Expressions.SymbolDocumentInfo)">
      <summary>创建一个用于清除序列点的 <see cref="T:System.Linq.Expressions.DebugInfoExpression" />。</summary>
      <returns>用于清除序列点的 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的实例。</returns>
      <param name="document">表示源文件的 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Coalesce(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示合并运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Coalesce" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Linq.Expressions.Expression.Type" /> 的 <paramref name="left" /> 属性不表示引用类型或可以为 null 的值类型。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="left" />.Type 和 <paramref name="right" />.Type 不可相互转换。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Coalesce(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.LambdaExpression)">
      <summary>在给定转换函数的情况下，创建一个表示合并运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Coalesce" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="conversion">要将 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="left" />.Type 和 <paramref name="right" />.Type 不可相互转换。- 或 -<paramref name="conversion" /> 不为 null，并且 <paramref name="conversion" />.Type 为不是正好带一个参数的委托类型。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Linq.Expressions.Expression.Type" /> 的 <paramref name="left" /> 属性不表示引用类型或可以为 null 的值类型。- 或 -<see cref="P:System.Linq.Expressions.Expression.Type" /> 的 <paramref name="left" /> 属性表示一个类型，该类型不能赋给委托类型 <paramref name="conversion" />.Type 的参数类型。- 或 -<see cref="P:System.Linq.Expressions.Expression.Type" /> 的 <paramref name="right" /> 属性不等于委托类型 <paramref name="conversion" />.Type 的返回类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Condition(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示条件语句的 <see cref="T:System.Linq.Expressions.ConditionalExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.ConditionalExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Conditional" />，并且其 <see cref="P:System.Linq.Expressions.ConditionalExpression.Test" />、<see cref="P:System.Linq.Expressions.ConditionalExpression.IfTrue" /> 和 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfFalse" /> 属性设置为指定值。</returns>
      <param name="test">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.Test" />。</param>
      <param name="ifTrue">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfTrue" />。</param>
      <param name="ifFalse">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfFalse" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="test" />、<paramref name="ifTrue" /> 或 <paramref name="ifFalse" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="test" />.Type 不为 <see cref="T:System.Boolean" />。- 或 -<paramref name="ifTrue" />.Type 不等于 <paramref name="ifFalse" />.Type。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Condition(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Type)">
      <summary>创建一个表示条件语句的 <see cref="T:System.Linq.Expressions.ConditionalExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.ConditionalExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Conditional" />，并且其 <see cref="P:System.Linq.Expressions.ConditionalExpression.Test" />、<see cref="P:System.Linq.Expressions.ConditionalExpression.IfTrue" /> 和 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfFalse" /> 属性设置为指定值。</returns>
      <param name="test">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.Test" />。</param>
      <param name="ifTrue">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfTrue" />。</param>
      <param name="ifFalse">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfFalse" />。</param>
      <param name="type">要将 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Constant(System.Object)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.ConstantExpression" />，它把 <see cref="P:System.Linq.Expressions.ConstantExpression.Value" /> 属性设置为指定值。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.ConstantExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Constant" />，并且其 <see cref="P:System.Linq.Expressions.ConstantExpression.Value" /> 属性设置为指定值。</returns>
      <param name="value">要将 <see cref="T:System.Object" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ConstantExpression.Value" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Constant(System.Object,System.Type)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.ConstantExpression" />，它把 <see cref="P:System.Linq.Expressions.ConstantExpression.Value" /> 和 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性设置为指定值。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.ConstantExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Constant" />，并且其 <see cref="P:System.Linq.Expressions.ConstantExpression.Value" /> 和 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性设置为指定值。</returns>
      <param name="value">要将 <see cref="T:System.Object" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ConstantExpression.Value" />。</param>
      <param name="type">要将 <see cref="T:System.Type" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> 不为 null 并且 <paramref name="type" /> 不可从 <paramref name="value" /> 的动态类型赋值。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Continue(System.Linq.Expressions.LabelTarget)">
      <summary>创建一个表示 continue 语句的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等于 Continue，其 <see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 属性设置为 <paramref name="target" />，此外还有一个在跳转时将传递给目标标签的 null 值。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 将跳至的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Continue(System.Linq.Expressions.LabelTarget,System.Type)">
      <summary>创建一个表示具有指定类型的 continue 语句的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等于 Continue，其 <see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 属性设置为 <paramref name="target" />，并且其 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性设置为 <paramref name="type" />，此外还有一个在跳转时将传递给目标标签的 null 值。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 将跳至的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="type">要将 <see cref="T:System.Type" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Convert(System.Linq.Expressions.Expression,System.Type)">
      <summary>创建一个表示类型转换运算的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Convert" />，并且其 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 和 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性设置为指定值。</returns>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <param name="type">要将 <see cref="T:System.Type" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 或 <paramref name="type" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">未定义 <paramref name="expression" />.Type 和 <paramref name="type" /> 之间的转换运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Convert(System.Linq.Expressions.Expression,System.Type,System.Reflection.MethodInfo)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它表示为其指定实现方法的转换运算。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Convert" />，并且其 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />、<see cref="P:System.Linq.Expressions.Expression.Type" /> 和 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <param name="type">要将 <see cref="T:System.Type" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 或 <paramref name="type" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带一个参数。</exception>
      <exception cref="T:System.InvalidOperationException">未定义 <paramref name="expression" />.Type 和 <paramref name="type" /> 之间的转换运算符。- 或 -<paramref name="expression" />.Type 不能赋给 <paramref name="method" /> 所表示的方法的参数类型。- 或 -<paramref name="method" /> 所表示的方法的返回类型不能赋给 <paramref name="type" />。- 或 -<paramref name="expression" />.Type 或 <paramref name="type" /> 为可以为 null 的值类型，并且相应的不可以为 null 的值类型不分别等于 <paramref name="method" /> 所表示的方法的参数类型或返回类型。</exception>
      <exception cref="T:System.Reflection.AmbiguousMatchException">找到多个与 <paramref name="method" /> 说明相匹配的方法。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ConvertChecked(System.Linq.Expressions.Expression,System.Type)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它表示在目标类型发生溢出时引发异常的转换运算。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.ConvertChecked" />，并且其 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 和 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性设置为指定值。</returns>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <param name="type">要将 <see cref="T:System.Type" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 或 <paramref name="type" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">未定义 <paramref name="expression" />.Type 和 <paramref name="type" /> 之间的转换运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ConvertChecked(System.Linq.Expressions.Expression,System.Type,System.Reflection.MethodInfo)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它表示在目标类型发生溢出时引发异常且为其指定实现方法的转换运算。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.ConvertChecked" />，并且其 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />、<see cref="P:System.Linq.Expressions.Expression.Type" /> 和 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <param name="type">要将 <see cref="T:System.Type" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 或 <paramref name="type" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带一个参数。</exception>
      <exception cref="T:System.InvalidOperationException">未定义 <paramref name="expression" />.Type 和 <paramref name="type" /> 之间的转换运算符。- 或 -<paramref name="expression" />.Type 不能赋给 <paramref name="method" /> 所表示的方法的参数类型。- 或 -<paramref name="method" /> 所表示的方法的返回类型不能赋给 <paramref name="type" />。- 或 -<paramref name="expression" />.Type 或 <paramref name="type" /> 为可以为 null 的值类型，并且相应的不可以为 null 的值类型不分别等于 <paramref name="method" /> 所表示的方法的参数类型或返回类型。</exception>
      <exception cref="T:System.Reflection.AmbiguousMatchException">找到多个与 <paramref name="method" /> 说明相匹配的方法。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.DebugInfo(System.Linq.Expressions.SymbolDocumentInfo,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>创建一个具有指定跨度的 <see cref="T:System.Linq.Expressions.DebugInfoExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的一个实例。</returns>
      <param name="document">表示源文件的 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" />。</param>
      <param name="startLine">此 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的起始行。必须大于 0。</param>
      <param name="startColumn">此 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的起始列。必须大于 0。</param>
      <param name="endLine">此 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的结束行。必须大于或等于起始行。</param>
      <param name="endColumn">此 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的结束列。如果结束行与起始行相同，则它必须大于或等于起始列。任何情况下都必须大于 0。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Decrement(System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它表示按 1 递减表达式值。</summary>
      <returns>一个表示已递减的表达式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要递减的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Decrement(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它表示按 1 递减表达式值。</summary>
      <returns>一个表示已递减的表达式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要递减的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="method">表示实现方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Default(System.Type)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.DefaultExpression" />，<see cref="P:System.Linq.Expressions.Expression.Type" /> 属性设置为指定类型。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.DefaultExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Default" />，并且其 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性设置为指定类型。</returns>
      <param name="type">要将 <see cref="T:System.Type" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Divide(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示算术除法运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Divide" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">作为 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置目标的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">作为 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置目标的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义除法运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Divide(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示算术除法运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。可指定实现方法。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Divide" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义除法运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.DivideAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示不进行溢出检查的除法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.DivideAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.DivideAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示不进行溢出检查的除法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.DivideAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.DivideAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>创建一个表示不进行溢出检查的除法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.DivideAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要将 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ElementInit(System.Reflection.MethodInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>在给定 <see cref="T:System.Linq.Expressions.ElementInit" /> 作为第二个参数的情况下，创建一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.ElementInit" />，其 <see cref="P:System.Linq.Expressions.ElementInit.AddMethod" /> 和 <see cref="P:System.Linq.Expressions.ElementInit.Arguments" /> 属性设置为指定值。</returns>
      <param name="addMethod">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ElementInit.AddMethod" />。</param>
      <param name="arguments">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />（包含 <see cref="T:System.Linq.Expressions.Expression" /> 对象）要将 <see cref="P:System.Linq.Expressions.ElementInit.Arguments" /> 属性设置为与其相等。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="addMethod" /> 或 <paramref name="arguments" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="addMethod" /> 表示的方法未命名为"Add"（不区分大小写）。- 或 -<paramref name="addMethod" /> 表示的方法不是实例方法。- 或 -<paramref name="arguments" /> 包含的元素数量与 <paramref name="addMethod" /> 所表示的方法的参数数量不同。- 或 -<see cref="P:System.Linq.Expressions.Expression.Type" /> 的一个或多个元素的 <paramref name="arguments" /> 属性不能赋给 <paramref name="addMethod" /> 所表示的方法的相应参数的类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ElementInit(System.Reflection.MethodInfo,System.Linq.Expressions.Expression[])">
      <summary>在给定值数组作为第二个参数的情况下，创建一个 <see cref="T:System.Linq.Expressions.ElementInit" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.ElementInit" />，其 <see cref="P:System.Linq.Expressions.ElementInit.AddMethod" /> 和 <see cref="P:System.Linq.Expressions.ElementInit.Arguments" /> 属性设置为指定值。</returns>
      <param name="addMethod">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ElementInit.AddMethod" />。</param>
      <param name="arguments">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ElementInit.Arguments" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="addMethod" /> 或 <paramref name="arguments" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">addMethod 表示的方法未命名为"Add"（不区分大小写）。- 或 -addMethod 表示的方法不是实例方法。- 或 -arguments 包含的元素数量与 addMethod 所表示的方法的参数数量不同。- 或 -<see cref="P:System.Linq.Expressions.Expression.Type" /> 的一个或多个元素的 <paramref name="arguments" /> 属性不能赋给 <paramref name="addMethod" /> 所表示的方法的相应参数的类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Empty">
      <summary>创建具有 <see cref="T:System.Void" /> 类型的空表达式。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.DefaultExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Default" />，并且其 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性设置为 <see cref="T:System.Void" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Equal(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示相等比较的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Equal" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义相等运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Equal(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Boolean,System.Reflection.MethodInfo)">
      <summary>创建一个表示相等比较的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。可指定实现方法。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Equal" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="liftToNull">若要将 true 设置为 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" />，则为 true；若要将 false 设置为 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" />，则为 false。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义相等运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ExclusiveOr(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>通过对用户定义的类型使用 <see cref="T:System.Linq.Expressions.BinaryExpression" />，创建一个表示按位 XOR 运算的 op_ExclusiveOr。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.ExclusiveOr" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 XOR.Type 和 <paramref name="left" />.Type 定义 <paramref name="right" /> 运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ExclusiveOr(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>通过对用户定义的类型使用 <see cref="T:System.Linq.Expressions.BinaryExpression" />，创建一个表示按位 XOR 运算的 op_ExclusiveOr。可指定实现方法。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.ExclusiveOr" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 XOR.Type 和 <paramref name="left" />.Type 定义 <paramref name="right" /> 运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ExclusiveOrAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>通过对用户定义的类型使用 <see cref="T:System.Linq.Expressions.BinaryExpression" />，创建一个表示按位 XOR 赋值运算的 op_ExclusiveOr。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.ExclusiveOrAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ExclusiveOrAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>通过对用户定义的类型使用 <see cref="T:System.Linq.Expressions.BinaryExpression" />，创建一个表示按位 XOR 赋值运算的 op_ExclusiveOr。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.ExclusiveOrAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ExclusiveOrAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>通过对用户定义的类型使用 <see cref="T:System.Linq.Expressions.BinaryExpression" />，创建一个表示按位 XOR 赋值运算的 op_ExclusiveOr。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.ExclusiveOrAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要将 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Field(System.Linq.Expressions.Expression,System.Reflection.FieldInfo)">
      <summary>创建一个表示访问字段的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MemberExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.MemberAccess" />，并且其 <see cref="P:System.Linq.Expressions.MemberExpression.Expression" /> 和 <see cref="P:System.Linq.Expressions.MemberExpression.Member" /> 属性设置为指定值。</returns>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MemberExpression.Expression" />。对于 static（在 Visual Basic 中为 Shared），<paramref name="expression" /> 必须是 null。</param>
      <param name="field">要将 <see cref="T:System.Reflection.FieldInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MemberExpression.Member" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="field" /> 为 null。- 或 -<paramref name="field" /> 表示的字段不为 static（在 Visual Basic 中不为 Shared），且 <paramref name="expression" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="expression" />.Type 不能赋给 <paramref name="field" /> 所表示的字段的声明类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Field(System.Linq.Expressions.Expression,System.String)">
      <summary>在给定字段名称的情况下，创建一个表示访问此字段的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberExpression" /> 的 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.MemberAccess" />，<see cref="P:System.Linq.Expressions.MemberExpression.Expression" /> 属性设置为 <paramref name="expression" />，<see cref="P:System.Linq.Expressions.MemberExpression.Member" /> 属性设置为 <see cref="T:System.Reflection.FieldInfo" />（表示由 <paramref name="fieldName" /> 表示的字段）。</returns>
      <param name="expression">一个 <see cref="T:System.Linq.Expressions.Expression" />，其 <see cref="P:System.Linq.Expressions.Expression.Type" /> 中包含一个名为 <paramref name="fieldName" /> 的字段。对于静态字段，这可以为 null。</param>
      <param name="fieldName">要访问的字段的名称。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 或 <paramref name="fieldName" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">没有在 <paramref name="fieldName" />.Type 或其基类型中定义名为 <paramref name="expression" /> 的字段。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Field(System.Linq.Expressions.Expression,System.Type,System.String)">
      <summary>创建一个表示访问字段的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</returns>
      <param name="expression">字段的包含对象。对于静态字段，这可以为 null。</param>
      <param name="type">包含字段的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
      <param name="fieldName">要访问的字段。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.GetActionType(System.Type[])">
      <summary>创建一个 <see cref="T:System.Type" /> 对象，它表示具有特定类型参数的泛型 System.Action 委托类型。</summary>
      <returns>具有指定类型参数的 System.Action 委托的类型。</returns>
      <param name="typeArgs">包含多达十六个 <see cref="T:System.Type" /> 对象的数组，它指定 System.Action 委托类型的类型参数。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="typeArgs" /> 包含 16 个以上的元素。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="typeArgs" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.GetDelegateType(System.Type[])">
      <summary>获取一个 <see cref="P:System.Linq.Expressions.Expression.Type" /> 对象，它表示具有特定类型参数的泛型 System.Func 或 System.Action 委托类型。</summary>
      <returns>委托类型。</returns>
      <param name="typeArgs">该委托的类型参数。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.GetFuncType(System.Type[])">
      <summary>创建一个 <see cref="P:System.Linq.Expressions.Expression.Type" /> 对象，它表示具有特定类型参数的泛型 System.Func 委托类型。最后一个类型参数指定已创建委托的返回类型。</summary>
      <returns>具有指定类型参数的 System.Func 委托的类型。</returns>
      <param name="typeArgs">包含一到十七个 <see cref="T:System.Type" /> 对象的数组，它指定 System.Func 委托类型的类型参数。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="typeArgs" /> 包含少于一个或多于十七个的元素。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="typeArgs" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Goto(System.Linq.Expressions.LabelTarget)">
      <summary>创建一个表示“go to”语句的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等于 Goto，其 <see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 属性设置为指定值，此外还有一个在跳转时将传递给目标标签的 null 值。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 将跳至的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Goto(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression)">
      <summary>创建一个表示“go to”语句的 <see cref="T:System.Linq.Expressions.GotoExpression" />。可以指定在跳转时传递给标签的值。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等于 Goto，并且其 <see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 属性设置为 <paramref name="target" />，此外还有一个在跳转时将传递给目标标签的 <paramref name="value" />。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 将跳至的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="value">将在跳转时传递给关联标签的值。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Goto(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression,System.Type)">
      <summary>创建一个表示具有指定类型的“go to”语句的 <see cref="T:System.Linq.Expressions.GotoExpression" />。可以指定在跳转时传递给标签的值。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等于 Goto，其 <see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 属性设置为 <paramref name="target" />，并且其 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性设置为 <paramref name="type" />，此外还有一个在跳转时将传递给目标标签的 <paramref name="value" />。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 将跳至的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="value">将在跳转时传递给关联标签的值。</param>
      <param name="type">要将 <see cref="T:System.Type" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Goto(System.Linq.Expressions.LabelTarget,System.Type)">
      <summary>创建一个表示具有指定类型的“go to”语句的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等于 Goto，其 <see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 属性设置为指定值，并且其 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性设置为 <paramref name="type" />，此外还有一个在跳转时将传递给目标标签的 null 值。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 将跳至的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="type">要将 <see cref="T:System.Type" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.GreaterThan(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示“大于”数值比较的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.GreaterThan" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义"大于"运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.GreaterThan(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Boolean,System.Reflection.MethodInfo)">
      <summary>创建一个表示“大于”数值比较的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。可指定实现方法。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.GreaterThan" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="liftToNull">若要将 true 设置为 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" />，则为 true；若要将 false 设置为 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" />，则为 false。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义"大于"运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.GreaterThanOrEqual(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示“大于或等于”数值比较的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.GreaterThanOrEqual" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义"大于或等于"运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.GreaterThanOrEqual(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Boolean,System.Reflection.MethodInfo)">
      <summary>创建一个表示“大于或等于”数值比较的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.GreaterThanOrEqual" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="liftToNull">若要将 true 设置为 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" />，则为 true；若要将 false 设置为 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" />，则为 false。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义"大于或等于"运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.IfThen(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.ConditionalExpression" />，它表示带 if 语句的条件块。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.ConditionalExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Conditional" />，并且 <see cref="P:System.Linq.Expressions.ConditionalExpression.Test" />、<see cref="P:System.Linq.Expressions.ConditionalExpression.IfTrue" /> 属性设置为指定值。<see cref="P:System.Linq.Expressions.ConditionalExpression.IfFalse" /> 属性设置为默认表达式，并且此方法返回的结果 <see cref="T:System.Linq.Expressions.ConditionalExpression" /> 的类型为 <see cref="T:System.Void" />。</returns>
      <param name="test">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.Test" />。</param>
      <param name="ifTrue">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfTrue" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.IfThenElse(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.ConditionalExpression" />，它表示带 if 和 else 语句的条件块。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.ConditionalExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Conditional" />，并且其 <see cref="P:System.Linq.Expressions.ConditionalExpression.Test" />、<see cref="P:System.Linq.Expressions.ConditionalExpression.IfTrue" /> 和 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfFalse" /> 属性设置为指定值。此方法返回的结果 <see cref="T:System.Linq.Expressions.ConditionalExpression" /> 的类型为 <see cref="T:System.Void" />。</returns>
      <param name="test">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.Test" />。</param>
      <param name="ifTrue">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfTrue" />。</param>
      <param name="ifFalse">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfFalse" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Increment(System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它表示按 1 递增表达式值。</summary>
      <returns>一个表示已递增的表达式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要递增的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Increment(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它表示按 1 递增表达式值。</summary>
      <returns>一个表示已递增的表达式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要递增的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="method">表示实现方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Invoke(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.InvocationExpression" />，它将委托或 lambda 表达式应用于一个参数表达式列表。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.InvocationExpression" />，它对提供的参数应用指定的委托或 lambda 表达式。</returns>
      <param name="expression">一个 <see cref="T:System.Linq.Expressions.Expression" />，它表示要应用的委托或 lambda 表达式。</param>
      <param name="arguments">一个包含 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 对象的 <see cref="T:System.Linq.Expressions.Expression" />，这些对象表示要对其应用委托或 lambda 表达式的参数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="expression" />.Type 不表示委托类型或 <see cref="T:System.Linq.Expressions.Expression`1" />。- 或 -<see cref="P:System.Linq.Expressions.Expression.Type" /> 的元素的 <paramref name="arguments" /> 属性不能赋给 <paramref name="expression" /> 所表示的委托的相应参数的类型。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="arguments" /> 没有包含与 <paramref name="expression" /> 所表示的委托的参数列表相同数量的元素。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Invoke(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression[])">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.InvocationExpression" />，它将委托或 lambda 表达式应用于一个参数表达式列表。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.InvocationExpression" />，它对提供的参数应用指定的委托或 lambda 表达式。</returns>
      <param name="expression">一个 <see cref="T:System.Linq.Expressions.Expression" />，它表示要应用的委托或 lambda 表达式。</param>
      <param name="arguments">
        <see cref="T:System.Linq.Expressions.Expression" /> 对象的数组，这些对象表示要对其应用委托或 lambda 表达式的参数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="expression" />.Type 不表示委托类型或 <see cref="T:System.Linq.Expressions.Expression`1" />。- 或 -<see cref="P:System.Linq.Expressions.Expression.Type" /> 的元素的 <paramref name="arguments" /> 属性不能赋给 <paramref name="expression" /> 所表示的委托的相应参数的类型。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="arguments" /> 没有包含与 <paramref name="expression" /> 所表示的委托的参数列表相同数量的元素。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.IsFalse(System.Linq.Expressions.Expression)">
      <summary>返回表达式的计算结果是否为 false。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" /> 的一个实例。</returns>
      <param name="expression">要计算的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.IsFalse(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>返回表达式的计算结果是否为 false。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" /> 的一个实例。</returns>
      <param name="expression">要计算的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="method">表示实现方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.IsTrue(System.Linq.Expressions.Expression)">
      <summary>返回表达式的计算结果是否为 true。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" /> 的一个实例。</returns>
      <param name="expression">要计算的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.IsTrue(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>返回表达式的计算结果是否为 true。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" /> 的一个实例。</returns>
      <param name="expression">要计算的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="method">表示实现方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Label">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.LabelTarget" />，它表示具有 void 类型但没有名称的标签。</summary>
      <returns>新的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Label(System.Linq.Expressions.LabelTarget)">
      <summary>创建一个<see cref="T:System.Linq.Expressions.LabelExpression" />，它表示不带默认值的标签。</summary>
      <returns>不带默认值的 <see cref="T:System.Linq.Expressions.LabelExpression" />。</returns>
      <param name="target">此 <see cref="T:System.Linq.Expressions.LabelTarget" /> 将关联的 <see cref="T:System.Linq.Expressions.LabelExpression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Label(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.LabelExpression" />，它表示具有给定默认值的标签。</summary>
      <returns>具有给定默认值的 <see cref="T:System.Linq.Expressions.LabelExpression" />。</returns>
      <param name="target">此 <see cref="T:System.Linq.Expressions.LabelTarget" /> 将关联的 <see cref="T:System.Linq.Expressions.LabelExpression" />。</param>
      <param name="defaultValue">当通过常规控制流到达标签时，此 <see cref="T:System.Linq.Expressions.LabelExpression" /> 的值。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Label(System.String)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.LabelTarget" />，它表示具有 void 类型和给定名称的标签。</summary>
      <returns>新的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</returns>
      <param name="name">标签的名称。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Label(System.Type)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.LabelTarget" />，它表示具有给定类型的标签。</summary>
      <returns>新的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</returns>
      <param name="type">跳转到标签时传递的值的类型。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Label(System.Type,System.String)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.LabelTarget" />，它表示具有给定类型和名称的标签。</summary>
      <returns>新的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</returns>
      <param name="type">跳转到标签时传递的值的类型。</param>
      <param name="name">标签的名称。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Linq.Expressions.Expression,System.Boolean,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>通过先构造一个委托类型来创建一个 LambdaExpression。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 属性等于 Lambda，并且其 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 属性均设置为指定的值。</returns>
      <param name="body">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="tailCall">一个 <see cref="T:System.Boolean" />，指示在编译创建的表达式时是否将应用尾调用优化。</param>
      <param name="parameters">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 对象。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda``1(System.Linq.Expressions.Expression,System.Boolean,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>创建一个在编译时委托类型已知的 <see cref="T:System.Linq.Expressions.Expression`1" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.Expression`1" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Lambda" />，并且其 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 属性设置为指定值。</returns>
      <param name="body">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="tailCall">一个 <see cref="T:System.Boolean" />，指示在编译创建的表达式时是否将应用尾调用优化。</param>
      <param name="parameters">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 对象。</param>
      <typeparam name="TDelegate">委托类型。</typeparam>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda``1(System.Linq.Expressions.Expression,System.Boolean,System.Linq.Expressions.ParameterExpression[])">
      <summary>创建一个在编译时委托类型已知的 <see cref="T:System.Linq.Expressions.Expression`1" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.Expression`1" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Lambda" />，并且其 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 属性设置为指定值。</returns>
      <param name="body">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="tailCall">一个 <see cref="T:System.Boolean" />，指示在编译创建的表达式时是否将应用尾调用优化。</param>
      <param name="parameters">其中包含用于填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 对象的数组。</param>
      <typeparam name="TDelegate">委托类型。</typeparam>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Linq.Expressions.Expression,System.Boolean,System.Linq.Expressions.ParameterExpression[])">
      <summary>通过先构造一个委托类型来创建一个 LambdaExpression。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 属性等于 Lambda，并且其 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 属性均设置为指定的值。</returns>
      <param name="body">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="tailCall">一个 <see cref="T:System.Boolean" />，指示在编译创建的表达式时是否将应用尾调用优化。</param>
      <param name="parameters">其中包含用于填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 对象的数组。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda``1(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>创建一个在编译时委托类型已知的 <see cref="T:System.Linq.Expressions.Expression`1" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.Expression`1" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Lambda" />，并且其 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 属性设置为指定值。</returns>
      <param name="body">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="parameters">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 对象。</param>
      <typeparam name="TDelegate">一种委托类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="body" /> 为 null。- 或 -<paramref name="parameters" /> 中的一个或多个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="TDelegate" /> 不是委托类型。- 或 -<paramref name="body" />.Type 表示一种不能赋给 <paramref name="TDelegate" /> 的返回类型的类型。- 或 -<paramref name="parameters" /> 没有包含与 <paramref name="TDelegate" /> 的参数列表相同数量的元素。- 或 -<see cref="P:System.Linq.Expressions.Expression.Type" /> 的元素的 <paramref name="parameters" /> 属性不可从 <paramref name="TDelegate" /> 的相应参数类型的类型赋值。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>通过先构造一个委托类型来创建一个 LambdaExpression。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 属性等于 Lambda，并且其 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 属性均设置为指定的值。</returns>
      <param name="body">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="parameters">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 对象。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda``1(System.Linq.Expressions.Expression,System.Linq.Expressions.ParameterExpression[])">
      <summary>创建一个在编译时委托类型已知的 <see cref="T:System.Linq.Expressions.Expression`1" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.Expression`1" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Lambda" />，并且其 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 属性设置为指定值。</returns>
      <param name="body">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="parameters">用于填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 对象的数组。</param>
      <typeparam name="TDelegate">一种委托类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="body" /> 为 null。- 或 -<paramref name="parameters" /> 中的一个或多个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="TDelegate" /> 不是委托类型。- 或 -<paramref name="body" />.Type 表示一种不能赋给 <paramref name="TDelegate" /> 的返回类型的类型。- 或 -<paramref name="parameters" /> 没有包含与 <paramref name="TDelegate" /> 的参数列表相同数量的元素。- 或 -<see cref="P:System.Linq.Expressions.Expression.Type" /> 的元素的 <paramref name="parameters" /> 属性不可从 <paramref name="TDelegate" /> 的相应参数类型的类型赋值。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Linq.Expressions.Expression,System.Linq.Expressions.ParameterExpression[])">
      <summary>通过先构造一个委托类型来创建一个 <see cref="T:System.Linq.Expressions.LambdaExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Lambda" />，并且其 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 属性设置为指定值。</returns>
      <param name="body">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="parameters">用于填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="body" /> 为 null。- 或 -<paramref name="parameters" /> 的一个或多个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="parameters" /> 包含 16 个以上的元素。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda``1(System.Linq.Expressions.Expression,System.String,System.Boolean,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>创建一个在编译时委托类型已知的 <see cref="T:System.Linq.Expressions.Expression`1" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.Expression`1" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Lambda" />，并且其 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 属性设置为指定值。</returns>
      <param name="body">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="name">lambda 的名称。用于生成调试信息。</param>
      <param name="tailCall">一个 <see cref="T:System.Boolean" />，指示在编译创建的表达式时是否将应用尾调用优化。</param>
      <param name="parameters">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 对象。</param>
      <typeparam name="TDelegate">委托类型。</typeparam>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Linq.Expressions.Expression,System.String,System.Boolean,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>通过先构造一个委托类型来创建一个 LambdaExpression。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 属性等于 Lambda，并且其 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 属性均设置为指定的值。</returns>
      <param name="body">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="name">lambda 的名称。用于发出调试信息。</param>
      <param name="tailCall">一个 <see cref="T:System.Boolean" />，指示在编译创建的表达式时是否将应用尾调用优化。</param>
      <param name="parameters">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 对象。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Linq.Expressions.Expression,System.String,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>通过先构造一个委托类型来创建一个 LambdaExpression。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 属性等于 Lambda，并且其 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 属性均设置为指定的值。</returns>
      <param name="body">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="name">lambda 的名称。用于发出调试信息。</param>
      <param name="parameters">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 对象。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda``1(System.Linq.Expressions.Expression,System.String,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>创建一个在编译时委托类型已知的 <see cref="T:System.Linq.Expressions.Expression`1" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.Expression`1" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Lambda" />，并且其 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 属性设置为指定值。</returns>
      <param name="body">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.Expression`1.Body" />。</param>
      <param name="name">lambda 的名称。用于生成调试信息。</param>
      <param name="parameters">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.Expression`1.Parameters" /> 对象。</param>
      <typeparam name="TDelegate">委托类型。</typeparam>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Type,System.Linq.Expressions.Expression,System.Boolean,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>通过先构造一个委托类型来创建一个 LambdaExpression。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 属性等于 Lambda，并且其 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 属性均设置为指定的值。</returns>
      <param name="delegateType">一个 <see cref="P:System.Linq.Expressions.Expression.Type" />，它表示 lambda 的委托签名。</param>
      <param name="body">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="tailCall">一个 <see cref="T:System.Boolean" />，指示在编译创建的表达式时是否将应用尾调用优化。</param>
      <param name="parameters">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 对象。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Type,System.Linq.Expressions.Expression,System.Boolean,System.Linq.Expressions.ParameterExpression[])">
      <summary>通过先构造一个委托类型来创建一个 LambdaExpression。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 属性等于 Lambda，并且其 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 属性均设置为指定的值。</returns>
      <param name="delegateType">一个 <see cref="P:System.Linq.Expressions.Expression.Type" />，它表示 lambda 的委托签名。</param>
      <param name="body">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="tailCall">一个 <see cref="T:System.Boolean" />，指示在编译创建的表达式时是否将应用尾调用优化。</param>
      <param name="parameters">其中包含用于填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 对象的数组。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Type,System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>通过先构造一个委托类型来创建一个 <see cref="T:System.Linq.Expressions.LambdaExpression" />。如果委托类型在编译时未知，则可以使用它。</summary>
      <returns>一个表示 lambda 表达式的对象，该表达式的 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Lambda" />，并且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 属性设置为指定值。</returns>
      <param name="delegateType">一个 <see cref="T:System.Type" />，它表示 lambda 的委托签名。</param>
      <param name="body">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="parameters">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="delegateType" /> 或 <paramref name="body" /> 为 null。- 或 -<paramref name="parameters" /> 中的一个或多个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="delegateType" /> 不表示委托类型。- 或 -<paramref name="body" />.Type 表示一个类型，该类型不能赋给 <paramref name="delegateType" /> 所表示的委托类型的返回类型。- 或 -<paramref name="parameters" /> 没有包含与 <paramref name="delegateType" /> 所表示的委托类型的参数列表相同数量的元素。- 或 -<see cref="P:System.Linq.Expressions.Expression.Type" /> 的元素的 <paramref name="parameters" /> 属性不可从 <paramref name="delegateType" /> 所表示的委托类型的相应参数类型的类型赋值。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.ParameterExpression[])">
      <summary>通过先构造一个委托类型来创建一个 <see cref="T:System.Linq.Expressions.LambdaExpression" />。如果委托类型在编译时未知，则可以使用它。</summary>
      <returns>一个表示 lambda 表达式的对象，该表达式的 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Lambda" />，并且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 属性设置为指定值。</returns>
      <param name="delegateType">一个 <see cref="T:System.Type" />，它表示 lambda 的委托签名。</param>
      <param name="body">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="parameters">用于填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="delegateType" /> 或 <paramref name="body" /> 为 null。- 或 -<paramref name="parameters" /> 中的一个或多个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="delegateType" /> 不表示委托类型。- 或 -<paramref name="body" />.Type 表示一个类型，该类型不能赋给 <paramref name="delegateType" /> 所表示的委托类型的返回类型。- 或 -<paramref name="parameters" /> 没有包含与 <paramref name="delegateType" /> 所表示的委托类型的参数列表相同数量的元素。- 或 -<see cref="P:System.Linq.Expressions.Expression.Type" /> 的元素的 <paramref name="parameters" /> 属性不可从 <paramref name="delegateType" /> 所表示的委托类型的相应参数类型的类型赋值。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Type,System.Linq.Expressions.Expression,System.String,System.Boolean,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>通过先构造一个委托类型来创建一个 LambdaExpression。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 属性等于 Lambda，并且其 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 属性均设置为指定的值。</returns>
      <param name="delegateType">一个 <see cref="P:System.Linq.Expressions.Expression.Type" />，它表示 lambda 的委托签名。</param>
      <param name="body">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="name">lambda 的名称。用于发出调试信息。</param>
      <param name="tailCall">一个 <see cref="T:System.Boolean" />，指示在编译创建的表达式时是否将应用尾调用优化。</param>
      <param name="parameters">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 对象。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Type,System.Linq.Expressions.Expression,System.String,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>通过先构造一个委托类型来创建一个 LambdaExpression。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 属性等于 Lambda，并且其 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 属性均设置为指定的值。</returns>
      <param name="delegateType">一个 <see cref="P:System.Linq.Expressions.Expression.Type" />，它表示 lambda 的委托签名。</param>
      <param name="body">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="name">lambda 的名称。用于发出调试信息。</param>
      <param name="parameters">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 对象。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.LeftShift(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示按位左移运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.LeftShift" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义左移位运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.LeftShift(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示按位左移运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.LeftShift" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义左移位运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.LeftShiftAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示按位左移赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.LeftShiftAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.LeftShiftAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示按位左移赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.LeftShiftAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.LeftShiftAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>创建一个表示按位左移赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.LeftShiftAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要将 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.LessThan(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示“小于”数值比较的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.LessThan" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义"小于"运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.LessThan(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Boolean,System.Reflection.MethodInfo)">
      <summary>创建一个表示“小于”数值比较的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.LessThan" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="liftToNull">若要将 true 设置为 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" />，则为 true；若要将 false 设置为 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" />，则为 false。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义"小于"运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.LessThanOrEqual(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示“小于或等于”数值比较的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.LessThanOrEqual" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义"小于或等于"运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.LessThanOrEqual(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Boolean,System.Reflection.MethodInfo)">
      <summary>创建一个表示“小于或等于”数值比较的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.LessThanOrEqual" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="liftToNull">若要将 true 设置为 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" />，则为 true；若要将 false 设置为 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" />，则为 false。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义"小于或等于"运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListBind(System.Reflection.MemberInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ElementInit})">
      <summary>创建一个其成员为字段或属性的 <see cref="T:System.Linq.Expressions.MemberListBinding" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MemberListBinding" />，其 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 属性等于 <see cref="F:System.Linq.Expressions.MemberBindingType.ListBinding" />，并且其 <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 和 <see cref="P:System.Linq.Expressions.MemberListBinding.Initializers" /> 属性设置为指定值。</returns>
      <param name="member">一个 <see cref="T:System.Reflection.MemberInfo" />，表示要将 <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 属性设置为与其相等的字段或属性。</param>
      <param name="initializers">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.ElementInit" /> 集合的 <see cref="P:System.Linq.Expressions.MemberListBinding.Initializers" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="member" /> 为 null。- 或 -<paramref name="initializers" /> 的一个或多个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="member" /> 不表示字段或属性。- 或 -<see cref="P:System.Reflection.FieldInfo.FieldType" /> 所表示的字段或属性的 <see cref="P:System.Reflection.PropertyInfo.PropertyType" /> 或 <paramref name="member" /> 没有实现 <see cref="T:System.Collections.IEnumerable" />。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListBind(System.Reflection.MemberInfo,System.Linq.Expressions.ElementInit[])">
      <summary>创建一个其成员为字段或属性的 <see cref="T:System.Linq.Expressions.MemberListBinding" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MemberListBinding" />，其 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 属性等于 <see cref="F:System.Linq.Expressions.MemberBindingType.ListBinding" />，并且其 <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 和 <see cref="P:System.Linq.Expressions.MemberListBinding.Initializers" /> 属性设置为指定值。</returns>
      <param name="member">一个 <see cref="T:System.Reflection.MemberInfo" />，表示要将 <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 属性设置为与其相等的字段或属性。</param>
      <param name="initializers">用于填充 <see cref="T:System.Linq.Expressions.ElementInit" /> 集合的 <see cref="P:System.Linq.Expressions.MemberListBinding.Initializers" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="member" /> 为 null。- 或 -<paramref name="initializers" /> 的一个或多个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="member" /> 不表示字段或属性。- 或 -<see cref="P:System.Reflection.FieldInfo.FieldType" /> 所表示的字段或属性的 <see cref="P:System.Reflection.PropertyInfo.PropertyType" /> 或 <paramref name="member" /> 没有实现 <see cref="T:System.Collections.IEnumerable" />。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListBind(System.Reflection.MethodInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ElementInit})">
      <summary>基于指定的属性访问器方法创建一个 <see cref="T:System.Linq.Expressions.MemberListBinding" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MemberListBinding" />，其 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 属性等于 <see cref="F:System.Linq.Expressions.MemberBindingType.ListBinding" />，<see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 属性设置为表示在 <see cref="T:System.Reflection.MemberInfo" /> 中访问的属性的 <paramref name="propertyAccessor" />，并且 <see cref="P:System.Linq.Expressions.MemberListBinding.Initializers" /> 填充了 <paramref name="initializers" /> 的元素。</returns>
      <param name="propertyAccessor">一个表示属性访问器方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
      <param name="initializers">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.ElementInit" /> 集合的 <see cref="P:System.Linq.Expressions.MemberListBinding.Initializers" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyAccessor" /> 为 null。- 或 -<paramref name="initializers" /> 的一个或多个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="propertyAccessor" /> 不表示属性访问器方法。- 或 -<see cref="P:System.Reflection.PropertyInfo.PropertyType" /> 所表示的方法所访问的属性的 <paramref name="propertyAccessor" /> 没有实现 <see cref="T:System.Collections.IEnumerable" />。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListBind(System.Reflection.MethodInfo,System.Linq.Expressions.ElementInit[])">
      <summary>基于指定的属性访问器方法创建一个 <see cref="T:System.Linq.Expressions.MemberListBinding" /> 对象。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MemberListBinding" />，其 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 属性等于 <see cref="F:System.Linq.Expressions.MemberBindingType.ListBinding" />，<see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 属性设置为表示在 <see cref="T:System.Reflection.MemberInfo" /> 中访问的属性的 <paramref name="propertyAccessor" />，并且 <see cref="P:System.Linq.Expressions.MemberListBinding.Initializers" /> 填充了 <paramref name="initializers" /> 的元素。</returns>
      <param name="propertyAccessor">一个表示属性访问器方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
      <param name="initializers">用于填充 <see cref="T:System.Linq.Expressions.ElementInit" /> 集合的 <see cref="P:System.Linq.Expressions.MemberListBinding.Initializers" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyAccessor" /> 为 null。- 或 -<paramref name="initializers" /> 的一个或多个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="propertyAccessor" /> 不表示属性访问器方法。- 或 -<see cref="P:System.Reflection.PropertyInfo.PropertyType" /> 所表示的方法所访问的属性的 <paramref name="propertyAccessor" /> 没有实现 <see cref="T:System.Collections.IEnumerable" />。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListInit(System.Linq.Expressions.NewExpression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ElementInit})">
      <summary>创建一个使用指定 <see cref="T:System.Linq.Expressions.ListInitExpression" /> 对象来初始化集合的 <see cref="T:System.Linq.Expressions.ElementInit" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.ListInitExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.ListInit" />，并且其 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" /> 和 <see cref="P:System.Linq.Expressions.ListInitExpression.Initializers" /> 属性设置为指定值。</returns>
      <param name="newExpression">要将 <see cref="T:System.Linq.Expressions.NewExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" />。</param>
      <param name="initializers">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.ElementInit" /> 集合的 <see cref="P:System.Linq.Expressions.ListInitExpression.Initializers" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newExpression" /> 或 <paramref name="initializers" /> 为 null。- 或 -<paramref name="initializers" /> 的一个或多个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="newExpression" />.Type 没有实现 <see cref="T:System.Collections.IEnumerable" />。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListInit(System.Linq.Expressions.NewExpression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个使用名为“Add”的方法将元素添加到集合中的 <see cref="T:System.Linq.Expressions.ListInitExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.ListInitExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.ListInit" />，并且其 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" /> 属性设置为指定值。</returns>
      <param name="newExpression">要将 <see cref="T:System.Linq.Expressions.NewExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" />。</param>
      <param name="initializers">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.ListInitExpression.Initializers" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newExpression" /> 或 <paramref name="initializers" /> 为 null。- 或 -<paramref name="initializers" /> 的一个或多个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="newExpression" />.Type 没有实现 <see cref="T:System.Collections.IEnumerable" />。</exception>
      <exception cref="T:System.InvalidOperationException">没有在 <paramref name="newExpression" />.Type 或其基类型中声明任何名为"Add"（不区分大小写）的实例方法。- 或 -<paramref name="newExpression" />.Type 或其基类型上的 add 方法不是正好带一个参数。- 或 -<see cref="P:System.Linq.Expressions.Expression.Type" /> 的第一个元素的 <paramref name="initializers" /> 属性所表示的类型不能赋给 <paramref name="newExpression" />.Type 或其基类型上的 add 方法的参数类型。- 或 -<paramref name="newExpression" />.Type 和/或其基类型上存在多个名为"Add"（不区分大小写）的参数兼容的方法。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListInit(System.Linq.Expressions.NewExpression,System.Linq.Expressions.ElementInit[])">
      <summary>创建一个使用指定 <see cref="T:System.Linq.Expressions.ListInitExpression" /> 对象来初始化集合的 <see cref="T:System.Linq.Expressions.ElementInit" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.ListInitExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.ListInit" />，并且其 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" /> 和 <see cref="P:System.Linq.Expressions.ListInitExpression.Initializers" /> 属性设置为指定值。</returns>
      <param name="newExpression">要将 <see cref="T:System.Linq.Expressions.NewExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" />。</param>
      <param name="initializers">用于填充 <see cref="T:System.Linq.Expressions.ElementInit" /> 集合的 <see cref="P:System.Linq.Expressions.ListInitExpression.Initializers" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newExpression" /> 或 <paramref name="initializers" /> 为 null。- 或 -<paramref name="initializers" /> 的一个或多个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="newExpression" />.Type 没有实现 <see cref="T:System.Collections.IEnumerable" />。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListInit(System.Linq.Expressions.NewExpression,System.Linq.Expressions.Expression[])">
      <summary>创建一个使用名为“Add”的方法将元素添加到集合中的 <see cref="T:System.Linq.Expressions.ListInitExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.ListInitExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.ListInit" />，并且其 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" /> 属性设置为指定值。</returns>
      <param name="newExpression">要将 <see cref="T:System.Linq.Expressions.NewExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" />。</param>
      <param name="initializers">用于填充 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.ListInitExpression.Initializers" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newExpression" /> 或 <paramref name="initializers" /> 为 null。- 或 -<paramref name="initializers" /> 的一个或多个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="newExpression" />.Type 没有实现 <see cref="T:System.Collections.IEnumerable" />。</exception>
      <exception cref="T:System.InvalidOperationException">没有在 <paramref name="newExpression" />.Type 或其基类型中声明任何名为"Add"（不区分大小写）的实例方法。- 或 -<paramref name="newExpression" />.Type 或其基类型上的 add 方法不是正好带一个参数。- 或 -<see cref="P:System.Linq.Expressions.Expression.Type" /> 的第一个元素的 <paramref name="initializers" /> 属性所表示的类型不能赋给 <paramref name="newExpression" />.Type 或其基类型上的 add 方法的参数类型。- 或 -<paramref name="newExpression" />.Type 和/或其基类型上存在多个名为"Add"（不区分大小写）的参数兼容的方法。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListInit(System.Linq.Expressions.NewExpression,System.Reflection.MethodInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个使用指定方法将元素添加到集合中的 <see cref="T:System.Linq.Expressions.ListInitExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.ListInitExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.ListInit" />，并且其 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" /> 属性设置为指定值。</returns>
      <param name="newExpression">要将 <see cref="T:System.Linq.Expressions.NewExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" />。</param>
      <param name="addMethod">一个 <see cref="T:System.Reflection.MethodInfo" />，表示名为“Add”（不区分大小写），用于将元素添加到集合的实例方法。</param>
      <param name="initializers">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.ListInitExpression.Initializers" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newExpression" /> 或 <paramref name="initializers" /> 为 null。- 或 -<paramref name="initializers" /> 的一个或多个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="newExpression" />.Type 没有实现 <see cref="T:System.Collections.IEnumerable" />。- 或 -<paramref name="addMethod" /> 不为 null，并且它不表示名为"Add"（不区分大小写）的正好带一个参数的实例方法。- 或 -<paramref name="addMethod" /> 不为 null，并且 <see cref="P:System.Linq.Expressions.Expression.Type" /> 的一个或多个元素的 <paramref name="initializers" /> 属性所表示的类型不能赋给 <paramref name="addMethod" /> 所表示的方法的参数类型。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="addMethod" /> 为 null，并且 <paramref name="newExpression" />.Type 或其基类型上不存在名为"Add"的带一个兼容类型的参数的任何实例方法。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListInit(System.Linq.Expressions.NewExpression,System.Reflection.MethodInfo,System.Linq.Expressions.Expression[])">
      <summary>创建一个使用指定方法将元素添加到集合中的 <see cref="T:System.Linq.Expressions.ListInitExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.ListInitExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.ListInit" />，并且其 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" /> 属性设置为指定值。</returns>
      <param name="newExpression">要将 <see cref="T:System.Linq.Expressions.NewExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" />。</param>
      <param name="addMethod">一个 <see cref="T:System.Reflection.MethodInfo" />，表示带一个参数，用于将元素添加到集合中的实例方法。</param>
      <param name="initializers">用于填充 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.ListInitExpression.Initializers" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newExpression" /> 或 <paramref name="initializers" /> 为 null。- 或 -<paramref name="initializers" /> 的一个或多个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="newExpression" />.Type 没有实现 <see cref="T:System.Collections.IEnumerable" />。- 或 -<paramref name="addMethod" /> 不为 null，并且它不表示名为"Add"（不区分大小写）的正好带一个参数的实例方法。- 或 -<paramref name="addMethod" /> 不为 null，并且 <see cref="P:System.Linq.Expressions.Expression.Type" /> 的一个或多个元素的 <paramref name="initializers" /> 属性所表示的类型不能赋给 <paramref name="addMethod" /> 所表示的方法的参数类型。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="addMethod" /> 为 null，并且 <paramref name="newExpression" />.Type 或其基类型上不存在名为"Add"的带一个兼容类型的参数的任何实例方法。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Loop(System.Linq.Expressions.Expression)">
      <summary>创建具有给定主体的 <see cref="T:System.Linq.Expressions.LoopExpression" />。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.LoopExpression" />。</returns>
      <param name="body">循环体。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Loop(System.Linq.Expressions.Expression,System.Linq.Expressions.LabelTarget)">
      <summary>创建具有给定主体和中断目标的 <see cref="T:System.Linq.Expressions.LoopExpression" />。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.LoopExpression" />。</returns>
      <param name="body">循环体。</param>
      <param name="break">循环体使用的中断目标。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Loop(System.Linq.Expressions.Expression,System.Linq.Expressions.LabelTarget,System.Linq.Expressions.LabelTarget)">
      <summary>创建具有给定主体的 <see cref="T:System.Linq.Expressions.LoopExpression" />。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.LoopExpression" />。</returns>
      <param name="body">循环体。</param>
      <param name="break">循环体使用的中断目标。</param>
      <param name="continue">循环体使用的继续目标。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeBinary(System.Linq.Expressions.ExpressionType,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>在给定左操作数和右操作数的情况下，通过调用适当的工厂方法来创建一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>通过调用适当的工厂方法生成的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</returns>
      <param name="binaryType">指定二元运算类型的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</param>
      <param name="left">一个表示左操作数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="right">一个表示右操作数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="binaryType" /> 与二元表达式节点不对应。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeBinary(System.Linq.Expressions.ExpressionType,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Boolean,System.Reflection.MethodInfo)">
      <summary>在给定左操作数、右操作数和实现方法的情况下，通过调用适当的工厂方法来创建一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>通过调用适当的工厂方法生成的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</returns>
      <param name="binaryType">指定二元运算类型的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</param>
      <param name="left">一个表示左操作数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="right">一个表示右操作数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="liftToNull">若要将 true 设置为 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" />，则为 true；若要将 false 设置为 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" />，则为 false。</param>
      <param name="method">一个指定实现方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="binaryType" /> 与二元表达式节点不对应。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeBinary(System.Linq.Expressions.ExpressionType,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Boolean,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>在给定左操作数、右操作数、实现方法和类型转换函数的情况下，通过调用适当的工厂方法来创建一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>通过调用适当的工厂方法生成的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</returns>
      <param name="binaryType">指定二元运算类型的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</param>
      <param name="left">一个表示左操作数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="right">一个表示右操作数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="liftToNull">若要将 true 设置为 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" />，则为 true；若要将 false 设置为 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" />，则为 false。</param>
      <param name="method">一个指定实现方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
      <param name="conversion">一个表示类型转换函数的 <see cref="T:System.Linq.Expressions.LambdaExpression" />。只有在 <paramref name="binaryType" /> 为 <see cref="F:System.Linq.Expressions.ExpressionType.Coalesce" /> 或复合赋值时，才使用此参数。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="binaryType" /> 与二元表达式节点不对应。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeCatchBlock(System.Type,System.Linq.Expressions.ParameterExpression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示具有指定元素的 catch 语句的 <see cref="T:System.Linq.Expressions.CatchBlock" />。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.CatchBlock" />。</returns>
      <param name="type">此 <see cref="P:System.Linq.Expressions.Expression.Type" /> 将处理的 <see cref="T:System.Exception" /> 的 <see cref="T:System.Linq.Expressions.CatchBlock" />。</param>
      <param name="variable">一个 <see cref="T:System.Linq.Expressions.ParameterExpression" />，它表示对此处理程序捕获的 <see cref="T:System.Exception" /> 对象的引用。</param>
      <param name="body">catch 语句的主体。</param>
      <param name="filter">
        <see cref="T:System.Exception" /> 筛选器的主体。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeGoto(System.Linq.Expressions.GotoExpressionKind,System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression,System.Type)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.GotoExpression" />，它表示指定的 <see cref="T:System.Linq.Expressions.GotoExpressionKind" /> 的跳转。也可以指定在跳转时传递给标签的值。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等于 <paramref name="kind" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 属性设置为 <paramref name="target" />，并且其 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性设置为 <paramref name="type" />，此外还有一个在跳转时将传递给目标标签的 <paramref name="value" />。</returns>
      <param name="kind">
        <see cref="T:System.Linq.Expressions.GotoExpressionKind" /> 的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 将跳至的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="value">将在跳转时传递给关联标签的值。</param>
      <param name="type">要将 <see cref="T:System.Type" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeIndex(System.Linq.Expressions.Expression,System.Reflection.PropertyInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.IndexExpression" />，它表示访问对象中的索引属性。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.IndexExpression" />。</returns>
      <param name="instance">属性所属的对象。如果属性为 static（在 Visual Basic 中为 shared），则它应为 null。</param>
      <param name="indexer">一个 <see cref="T:System.Linq.Expressions.Expression" />，它表示要编制索引的属性。</param>
      <param name="arguments">一个 IEnumerable&lt;Expression&gt;（在 Visual Basic 中为 IEnumerable (Of Expression)），其中包含将用于为属性编制索引的参数。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeMemberAccess(System.Linq.Expressions.Expression,System.Reflection.MemberInfo)">
      <summary>创建一个表示访问字段或属性的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</summary>
      <returns>通过调用适当的工厂方法生成的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</returns>
      <param name="expression">一个表示成员所属对象的 <see cref="T:System.Linq.Expressions.Expression" />。对于静态成员，这可以为 null。</param>
      <param name="member">描述要访问的字段或属性的 <see cref="T:System.Reflection.MemberInfo" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="member" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="member" /> 不表示字段或属性。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeTry(System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.CatchBlock})">
      <summary>创建一个表示具有指定元素的 try 块的 <see cref="T:System.Linq.Expressions.TryExpression" />。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.TryExpression" />。</returns>
      <param name="type">try 表达式的结果类型。如果为 null，则 bodh 和所有处理程序必须具有相同的类型。</param>
      <param name="body">try 块的主体。</param>
      <param name="finally">finally 块的主体。如果 try 块不具有关联的 finally 块，则传递 null。</param>
      <param name="fault">fault 块的主体。如果 try 块不具有关联的 fault 块，则传递 null。</param>
      <param name="handlers">表示要与 try 块关联的 catch 语句的 <see cref="T:System.Linq.Expressions.CatchBlock" /> 的集合。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeUnary(System.Linq.Expressions.ExpressionType,System.Linq.Expressions.Expression,System.Type)">
      <summary>在给定操作数的情况下，通过调用适当的工厂方法来创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</summary>
      <returns>通过调用适当的工厂方法生成的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="unaryType">指定一元运算类型的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</param>
      <param name="operand">一个表示操作数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="type">指定转换的目标类型的 <see cref="T:System.Type" />（如果不适用，则传递 null）。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="operand" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="unaryType" /> 与一元表达式节点不对应。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeUnary(System.Linq.Expressions.ExpressionType,System.Linq.Expressions.Expression,System.Type,System.Reflection.MethodInfo)">
      <summary>在给定操作数和实现方法的情况下，通过调用适当的工厂方法来创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</summary>
      <returns>通过调用适当的工厂方法生成的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="unaryType">指定一元运算类型的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</param>
      <param name="operand">一个表示操作数的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="type">指定转换的目标类型的 <see cref="T:System.Type" />（如果不适用，则传递 null）。</param>
      <param name="method">表示实现方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="operand" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="unaryType" /> 与一元表达式节点不对应。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MemberBind(System.Reflection.MemberInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.MemberBinding})">
      <summary>创建一个表示递归初始化某个字段或属性的成员的 <see cref="T:System.Linq.Expressions.MemberMemberBinding" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MemberMemberBinding" />，其 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 属性等于 <see cref="F:System.Linq.Expressions.MemberBindingType.MemberBinding" />，并且其 <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 和 <see cref="P:System.Linq.Expressions.MemberMemberBinding.Bindings" /> 属性设置为指定值。</returns>
      <param name="member">要将 <see cref="T:System.Reflection.MemberInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MemberBinding.Member" />。</param>
      <param name="bindings">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.MemberBinding" /> 集合的 <see cref="P:System.Linq.Expressions.MemberMemberBinding.Bindings" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="member" /> 或 <paramref name="bindings" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="member" /> 不表示字段或属性。- 或 -<see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 的元素的 <paramref name="bindings" /> 属性不表示 <paramref name="member" /> 所表示的字段或属性类型的成员。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MemberBind(System.Reflection.MemberInfo,System.Linq.Expressions.MemberBinding[])">
      <summary>创建一个表示递归初始化某个字段或属性的成员的 <see cref="T:System.Linq.Expressions.MemberMemberBinding" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MemberMemberBinding" />，其 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 属性等于 <see cref="F:System.Linq.Expressions.MemberBindingType.MemberBinding" />，并且其 <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 和 <see cref="P:System.Linq.Expressions.MemberMemberBinding.Bindings" /> 属性设置为指定值。</returns>
      <param name="member">要将 <see cref="T:System.Reflection.MemberInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MemberBinding.Member" />。</param>
      <param name="bindings">用于填充 <see cref="T:System.Linq.Expressions.MemberBinding" /> 集合的 <see cref="P:System.Linq.Expressions.MemberMemberBinding.Bindings" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="member" /> 或 <paramref name="bindings" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="member" /> 不表示字段或属性。- 或 -<see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 的元素的 <paramref name="bindings" /> 属性不表示 <paramref name="member" /> 所表示的字段或属性类型的成员。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MemberBind(System.Reflection.MethodInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.MemberBinding})">
      <summary>使用属性访问器方法创建一个表示对所访问的成员的成员进行递归初始化的 <see cref="T:System.Linq.Expressions.MemberMemberBinding" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MemberMemberBinding" />，其 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 属性等于 <see cref="F:System.Linq.Expressions.MemberBindingType.MemberBinding" />，<see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 属性设置为表示在 <see cref="T:System.Reflection.PropertyInfo" /> 中访问的属性的 <paramref name="propertyAccessor" />，并且 <see cref="P:System.Linq.Expressions.MemberMemberBinding.Bindings" /> 属性设置为指定值。</returns>
      <param name="propertyAccessor">表示属性访问器方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
      <param name="bindings">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.MemberBinding" /> 集合的 <see cref="P:System.Linq.Expressions.MemberMemberBinding.Bindings" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyAccessor" /> 或 <paramref name="bindings" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="propertyAccessor" /> 不表示属性访问器方法。- 或 -<see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 的元素的 <paramref name="bindings" /> 属性不表示 <paramref name="propertyAccessor" /> 所表示的方法访问的属性类型的成员。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MemberBind(System.Reflection.MethodInfo,System.Linq.Expressions.MemberBinding[])">
      <summary>使用属性访问器方法创建一个表示对所访问的成员的成员进行递归初始化的 <see cref="T:System.Linq.Expressions.MemberMemberBinding" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MemberMemberBinding" />，其 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 属性等于 <see cref="F:System.Linq.Expressions.MemberBindingType.MemberBinding" />，<see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 属性设置为表示在 <see cref="T:System.Reflection.PropertyInfo" /> 中访问的属性的 <paramref name="propertyAccessor" />，并且 <see cref="P:System.Linq.Expressions.MemberMemberBinding.Bindings" /> 属性设置为指定值。</returns>
      <param name="propertyAccessor">表示属性访问器方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
      <param name="bindings">用于填充 <see cref="T:System.Linq.Expressions.MemberBinding" /> 集合的 <see cref="P:System.Linq.Expressions.MemberMemberBinding.Bindings" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyAccessor" /> 或 <paramref name="bindings" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="propertyAccessor" /> 不表示属性访问器方法。- 或 -<see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 的元素的 <paramref name="bindings" /> 属性不表示 <paramref name="propertyAccessor" /> 所表示的方法访问的属性类型的成员。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MemberInit(System.Linq.Expressions.NewExpression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.MemberBinding})">
      <summary>表示一个表达式，该表达式创建新对象并初始化该对象的一个属性。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MemberInitExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.MemberInit" />，并且其 <see cref="P:System.Linq.Expressions.MemberInitExpression.NewExpression" /> 和 <see cref="P:System.Linq.Expressions.MemberInitExpression.Bindings" /> 属性设置为指定值。</returns>
      <param name="newExpression">要将 <see cref="T:System.Linq.Expressions.NewExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MemberInitExpression.NewExpression" />。</param>
      <param name="bindings">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.MemberBinding" /> 集合的 <see cref="P:System.Linq.Expressions.MemberInitExpression.Bindings" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newExpression" /> 或 <paramref name="bindings" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 的元素的 <paramref name="bindings" /> 属性不表示 <paramref name="newExpression" />.Type 所表示的类型的成员。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MemberInit(System.Linq.Expressions.NewExpression,System.Linq.Expressions.MemberBinding[])">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.MemberInitExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MemberInitExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.MemberInit" />，并且其 <see cref="P:System.Linq.Expressions.MemberInitExpression.NewExpression" /> 和 <see cref="P:System.Linq.Expressions.MemberInitExpression.Bindings" /> 属性设置为指定值。</returns>
      <param name="newExpression">要将 <see cref="T:System.Linq.Expressions.NewExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MemberInitExpression.NewExpression" />。</param>
      <param name="bindings">用于填充 <see cref="T:System.Linq.Expressions.MemberBinding" /> 集合的 <see cref="P:System.Linq.Expressions.MemberInitExpression.Bindings" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newExpression" /> 或 <paramref name="bindings" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 的元素的 <paramref name="bindings" /> 属性不表示 <paramref name="newExpression" />.Type 所表示的类型的成员。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Modulo(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示算术余数运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Modulo" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义取模运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Modulo(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示算术余数运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Modulo" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义取模运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ModuloAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示余数赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.ModuloAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ModuloAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示余数赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.ModuloAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ModuloAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>创建一个表示余数赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.ModuloAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要将 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Multiply(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示不进行溢出检查的算术乘法运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Multiply" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义乘法运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Multiply(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示不进行溢出检查的算术乘法运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Multiply" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义乘法运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MultiplyAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示不进行溢出检查的乘法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.MultiplyAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MultiplyAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示不进行溢出检查的乘法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.MultiplyAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MultiplyAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>创建一个表示不进行溢出检查的乘法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.MultiplyAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要将 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MultiplyAssignChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示进行溢出检查的乘法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.MultiplyAssignChecked" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MultiplyAssignChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示进行溢出检查的乘法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.MultiplyAssignChecked" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MultiplyAssignChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>创建一个表示进行溢出检查的乘法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.MultiplyAssignChecked" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要将 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MultiplyChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示进行溢出检查的算术乘法运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.MultiplyChecked" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义乘法运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MultiplyChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示进行溢出检查的算术乘法运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.MultiplyChecked" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义乘法运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Negate(System.Linq.Expressions.Expression)">
      <summary>创建一个表示算术求反运算的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Negate" />，并且其 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 属性设置为指定值。</returns>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="expression" />.Type 定义一元负运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Negate(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示算术求反运算的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Negate" />，并且其 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 和 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带一个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="expression" />.Type 定义一元负运算符。- 或 -<paramref name="expression" />.Type（如果它是可以为 null 的值类型，则取其相应的不可以为 null 的类型）不能赋给 <paramref name="method" /> 所表示的方法的参数类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.NegateChecked(System.Linq.Expressions.Expression)">
      <summary>创建一个表示进行溢出检查的算术求反运算的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.NegateChecked" />，并且其 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 属性设置为指定值。</returns>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="expression" />.Type 定义一元负运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.NegateChecked(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示进行溢出检查的算术求反运算的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。可指定实现方法。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.NegateChecked" />，并且其 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 和 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带一个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="expression" />.Type 定义一元负运算符。- 或 -<paramref name="expression" />.Type（如果它是可以为 null 的值类型，则取其相应的不可以为 null 的类型）不能赋给 <paramref name="method" /> 所表示的方法的参数类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.New(System.Reflection.ConstructorInfo)">
      <summary>创建一个表示调用不带参数的指定构造函数的 <see cref="T:System.Linq.Expressions.NewExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.NewExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.New" />，并且其 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" /> 属性设置为指定值。</returns>
      <param name="constructor">要将 <see cref="T:System.Reflection.ConstructorInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="constructor" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="constructor" /> 表示的构造函数至少具有一个参数。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.New(System.Reflection.ConstructorInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个表示调用带指定参数的指定构造函数的 <see cref="T:System.Linq.Expressions.NewExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.NewExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.New" />，并且其 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" /> 和 <see cref="P:System.Linq.Expressions.NewExpression.Arguments" /> 属性设置为指定值。</returns>
      <param name="constructor">要将 <see cref="T:System.Reflection.ConstructorInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" />。</param>
      <param name="arguments">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.NewExpression.Arguments" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="constructor" /> 为 null。- 或 -<paramref name="arguments" /> 的一个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arguments" /> 参数包含的元素数量与 <paramref name="constructor" /> 所表示的构造函数的参数数量不同。- 或 -<see cref="P:System.Linq.Expressions.Expression.Type" /> 的元素的 <paramref name="arguments" /> 属性不能赋给 <paramref name="constructor" /> 所表示的构造函数的相应参数类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.New(System.Reflection.ConstructorInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression},System.Collections.Generic.IEnumerable{System.Reflection.MemberInfo})">
      <summary>创建一个表示调用带指定参数的指定构造函数的 <see cref="T:System.Linq.Expressions.NewExpression" />。其中指定了访问构造函数初始化的字段的成员。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.NewExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.New" />，并且其 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" />、<see cref="P:System.Linq.Expressions.NewExpression.Arguments" /> 和 <see cref="P:System.Linq.Expressions.NewExpression.Members" /> 属性设置为指定值。</returns>
      <param name="constructor">要将 <see cref="T:System.Reflection.ConstructorInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" />。</param>
      <param name="arguments">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.NewExpression.Arguments" /> 对象。</param>
      <param name="members">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Reflection.MemberInfo" /> 集合的 <see cref="P:System.Linq.Expressions.NewExpression.Members" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="constructor" /> 为 null。- 或 -<paramref name="arguments" /> 的一个元素为 null。- 或 -<paramref name="members" /> 的一个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arguments" /> 参数包含的元素数量与 <paramref name="constructor" /> 所表示的构造函数的参数数量不同。- 或 -<see cref="P:System.Linq.Expressions.Expression.Type" /> 的元素的 <paramref name="arguments" /> 属性不能赋给 <paramref name="constructor" /> 所表示的构造函数的相应参数类型。- 或 -<paramref name="members" /> 参数没有包含与 <paramref name="arguments" /> 相同数量的元素。- 或 -<paramref name="arguments" /> 的元素具有 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性，该属性表示不能赋给 <paramref name="members" /> 的相应元素所表示的成员类型的类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.New(System.Reflection.ConstructorInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression},System.Reflection.MemberInfo[])">
      <summary>创建一个表示调用带指定参数的指定构造函数的 <see cref="T:System.Linq.Expressions.NewExpression" />。将访问构造函数初始化字段的成员指定为数组。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.NewExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.New" />，并且其 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" />、<see cref="P:System.Linq.Expressions.NewExpression.Arguments" /> 和 <see cref="P:System.Linq.Expressions.NewExpression.Members" /> 属性设置为指定值。</returns>
      <param name="constructor">要将 <see cref="T:System.Reflection.ConstructorInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" />。</param>
      <param name="arguments">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.NewExpression.Arguments" /> 对象。</param>
      <param name="members">用于填充 <see cref="T:System.Reflection.MemberInfo" /> 集合的 <see cref="P:System.Linq.Expressions.NewExpression.Members" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="constructor" /> 为 null。- 或 -<paramref name="arguments" /> 的一个元素为 null。- 或 -<paramref name="members" /> 的一个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arguments" /> 参数包含的元素数量与 <paramref name="constructor" /> 所表示的构造函数的参数数量不同。- 或 -<see cref="P:System.Linq.Expressions.Expression.Type" /> 的元素的 <paramref name="arguments" /> 属性不能赋给 <paramref name="constructor" /> 所表示的构造函数的相应参数类型。- 或 -<paramref name="members" /> 参数没有包含与 <paramref name="arguments" /> 相同数量的元素。- 或 -<paramref name="arguments" /> 的元素具有 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性，该属性表示不能赋给 <paramref name="members" /> 的相应元素所表示的成员类型的类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.New(System.Reflection.ConstructorInfo,System.Linq.Expressions.Expression[])">
      <summary>创建一个表示调用带指定参数的指定构造函数的 <see cref="T:System.Linq.Expressions.NewExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.NewExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.New" />，并且其 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" /> 和 <see cref="P:System.Linq.Expressions.NewExpression.Arguments" /> 属性设置为指定值。</returns>
      <param name="constructor">要将 <see cref="T:System.Reflection.ConstructorInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" />。</param>
      <param name="arguments">用于填充 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.NewExpression.Arguments" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="constructor" /> 为 null。- 或 -<paramref name="arguments" /> 的一个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arguments" /> 的长度与 <paramref name="constructor" /> 表示的构造函数的参数数量正好相匹配。- 或 -<see cref="P:System.Linq.Expressions.Expression.Type" /> 的元素的 <paramref name="arguments" /> 属性不能赋给 <paramref name="constructor" /> 所表示的构造函数的相应参数类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.New(System.Type)">
      <summary>创建一个表示调用指定类型的无参数构造函数的 <see cref="T:System.Linq.Expressions.NewExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.NewExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.New" />，并且其 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" /> 属性设置为 <see cref="T:System.Reflection.ConstructorInfo" />，这表示不带指定类型的参数的构造函数。</returns>
      <param name="type">一个具有不带参数的构造函数的 <see cref="T:System.Type" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="type" /> 所表示的类型不具有无参数构造函数。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.NewArrayBounds(System.Type,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个表示创建具有指定秩的数组的 <see cref="T:System.Linq.Expressions.NewArrayExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.NewArrayExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.NewArrayBounds" />，并且其 <see cref="P:System.Linq.Expressions.NewArrayExpression.Expressions" /> 属性设置为指定值。</returns>
      <param name="type">一个表示数组的元素类型的 <see cref="T:System.Type" />。</param>
      <param name="bounds">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.NewArrayExpression.Expressions" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> 或 <paramref name="bounds" /> 为 null。- 或 -<paramref name="bounds" /> 的一个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.Linq.Expressions.Expression.Type" /> 的元素的 <paramref name="bounds" /> 属性不表示整型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.NewArrayBounds(System.Type,System.Linq.Expressions.Expression[])">
      <summary>创建一个表示创建具有指定秩的数组的 <see cref="T:System.Linq.Expressions.NewArrayExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.NewArrayExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.NewArrayBounds" />，并且其 <see cref="P:System.Linq.Expressions.NewArrayExpression.Expressions" /> 属性设置为指定值。</returns>
      <param name="type">一个表示数组的元素类型的 <see cref="T:System.Type" />。</param>
      <param name="bounds">用于填充 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.NewArrayExpression.Expressions" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> 或 <paramref name="bounds" /> 为 null。- 或 -<paramref name="bounds" /> 的一个元素为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.Linq.Expressions.Expression.Type" /> 的元素的 <paramref name="bounds" /> 属性不表示整型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.NewArrayInit(System.Type,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个表示创建一维数组并使用元素列表初始化该数组的 <see cref="T:System.Linq.Expressions.NewArrayExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.NewArrayExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.NewArrayInit" />，并且其 <see cref="P:System.Linq.Expressions.NewArrayExpression.Expressions" /> 属性设置为指定值。</returns>
      <param name="type">一个表示数组的元素类型的 <see cref="T:System.Type" />。</param>
      <param name="initializers">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含用来填充 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.NewArrayExpression.Expressions" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> 或 <paramref name="initializers" /> 为 null。- 或 -<paramref name="initializers" /> 的一个元素为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Linq.Expressions.Expression.Type" /> 的元素的 <paramref name="initializers" /> 属性表示不能赋给 <paramref name="type" /> 所表示的类型的类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.NewArrayInit(System.Type,System.Linq.Expressions.Expression[])">
      <summary>创建一个表示创建一维数组并使用元素列表初始化该数组的 <see cref="T:System.Linq.Expressions.NewArrayExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.NewArrayExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.NewArrayInit" />，并且其 <see cref="P:System.Linq.Expressions.NewArrayExpression.Expressions" /> 属性设置为指定值。</returns>
      <param name="type">一个表示数组的元素类型的 <see cref="T:System.Type" />。</param>
      <param name="initializers">用于填充 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.NewArrayExpression.Expressions" /> 对象的数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> 或 <paramref name="initializers" /> 为 null。- 或 -<paramref name="initializers" /> 的一个元素为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Linq.Expressions.Expression.Type" /> 的元素的 <paramref name="initializers" /> 属性表示不能赋给类型 <paramref name="type" /> 的类型。</exception>
    </member>
    <member name="P:System.Linq.Expressions.Expression.NodeType">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 的节点类型。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ExpressionType" /> 值之一。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Not(System.Linq.Expressions.Expression)">
      <summary>创建一个表示按位求补运算的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Not" />，并且其 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 属性设置为指定值。</returns>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="expression" />.Type 定义一元 not 运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Not(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示按位求补运算的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。可指定实现方法。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Not" />，并且其 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 和 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带一个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="expression" />.Type 定义一元 not 运算符。- 或 -<paramref name="expression" />.Type（如果它是可以为 null 的值类型，则取其相应的不可以为 null 的类型）不能赋给 <paramref name="method" /> 所表示的方法的参数类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.NotEqual(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示不相等比较的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.NotEqual" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义不相等运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.NotEqual(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Boolean,System.Reflection.MethodInfo)">
      <summary>创建一个表示不相等比较的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.NotEqual" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="liftToNull">若要将 true 设置为 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" />，则为 true；若要将 false 设置为 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" />，则为 false。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义不相等运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.OnesComplement(System.Linq.Expressions.Expression)">
      <summary>返回表示一的补数的表达式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" /> 的一个实例。</returns>
      <param name="expression">一个 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.OnesComplement(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>返回表示一的补数的表达式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" /> 的一个实例。</returns>
      <param name="expression">一个 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="method">表示实现方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Or(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示按位 <see cref="T:System.Linq.Expressions.BinaryExpression" /> 运算的 OR。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Or" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 OR.Type 和 <paramref name="left" />.Type 定义按位 <paramref name="right" /> 运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Or(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示按位 <see cref="T:System.Linq.Expressions.BinaryExpression" /> 运算的 OR。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Or" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 OR.Type 和 <paramref name="left" />.Type 定义按位 <paramref name="right" /> 运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.OrAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示按位 OR 赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.OrAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.OrAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示按位 OR 赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.OrAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.OrAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>创建一个表示按位 OR 赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.OrAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要将 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.OrElse(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，它表示仅在第一个操作数的计算结果为 OR 时才计算第二个操作数的条件 false 运算。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.OrElse" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 OR.Type 和 <paramref name="left" />.Type 定义按位 <paramref name="right" /> 运算符。- 或 -<paramref name="left" />.Type 和 <paramref name="right" />.Type 不是同一布尔值类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.OrElse(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，它表示仅在第一个操作数的计算结果为 OR 时才计算第二个操作数的条件 false 运算。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.OrElse" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 OR.Type 和 <paramref name="left" />.Type 定义按位 <paramref name="right" /> 运算符。- 或 -<paramref name="method" /> 为 null 并且 <paramref name="left" />.Type 和 <paramref name="right" />.Type 不是同一布尔值类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Parameter(System.Type)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 节点，该节点可用于标识表达式树中的参数或变量。</summary>
      <returns>具有指定的名称和类型的 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 节点。</returns>
      <param name="type">参数或变量的类型。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Parameter(System.Type,System.String)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 节点，该节点可用于标识表达式树中的参数或变量。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.ParameterExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Parameter" />，并且其 <see cref="P:System.Linq.Expressions.Expression.Type" /> 和 <see cref="P:System.Linq.Expressions.ParameterExpression.Name" /> 属性设置为指定值。</returns>
      <param name="type">参数或变量的类型。</param>
      <param name="name">仅用于调试或打印目的的参数或变量的名称。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PostDecrementAssign(System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它表示将原始表达式递减 1 之后再进行表达式赋值。</summary>
      <returns>一个表示结果表达式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要进行运算的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PostDecrementAssign(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它表示将原始表达式递减 1 之后再进行表达式赋值。</summary>
      <returns>一个表示结果表达式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要进行运算的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="method">表示实现方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PostIncrementAssign(System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它表示将原始表达式递增 1 之后再进行表达式赋值。</summary>
      <returns>一个表示结果表达式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要进行运算的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PostIncrementAssign(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它表示将原始表达式递增 1 之后再进行表达式赋值。</summary>
      <returns>一个表示结果表达式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要进行运算的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="method">表示实现方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Power(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示对数进行幂运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Power" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义幂运算符。- 或 -<paramref name="left" />.Type 和/或 <paramref name="right" />.Type 不为 <see cref="T:System.Double" />。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Power(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示对数进行幂运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Power" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义幂运算符。- 或 -<paramref name="method" /> 为 null，并且 <paramref name="left" />.Type 和/或 <paramref name="right" />.Type 不为 <see cref="T:System.Double" />。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PowerAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，它表示对表达式求幂并将结果赋回给表达式。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.PowerAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PowerAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，它表示对表达式求幂并将结果赋回给表达式。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.PowerAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PowerAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，它表示对表达式求幂并将结果赋回给表达式。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.PowerAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要将 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PreDecrementAssign(System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它将表达式递减 1 并将结果赋回给表达式。</summary>
      <returns>一个表示结果表达式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要进行运算的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PreDecrementAssign(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它将表达式递减 1 并将结果赋回给表达式。</summary>
      <returns>一个表示结果表达式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要进行运算的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="method">表示实现方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PreIncrementAssign(System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它将表达式递增 1 并将结果赋回给表达式。</summary>
      <returns>一个表示结果表达式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要进行运算的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PreIncrementAssign(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它将表达式递增 1 并将结果赋回给表达式。</summary>
      <returns>一个表示结果表达式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要进行运算的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="method">表示实现方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Property(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>使用属性访问器方法创建一个表示访问属性的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MemberExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.MemberAccess" />，<see cref="P:System.Linq.Expressions.MemberExpression.Expression" /> 属性设置为 <paramref name="expression" />，并且 <see cref="P:System.Linq.Expressions.MemberExpression.Member" /> 属性设置为表示在 <see cref="T:System.Reflection.PropertyInfo" /> 中访问的属性的 <paramref name="propertyAccessor" />。</returns>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MemberExpression.Expression" />。对于静态属性，这可以为 null。</param>
      <param name="propertyAccessor">表示属性访问器方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyAccessor" /> 为 null。- 或 -<paramref name="propertyAccessor" /> 所表示的方法不为 static（在 Visual Basic 中为 Shared），并且 <paramref name="expression" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="expression" />.Type 不能赋给 <paramref name="propertyAccessor" /> 所表示的方法的声明类型。- 或 -<paramref name="propertyAccessor" /> 所表示的方法不为属性访问器方法。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Property(System.Linq.Expressions.Expression,System.Reflection.PropertyInfo)">
      <summary>创建一个表示访问属性的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MemberExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.MemberAccess" />，并且其 <see cref="P:System.Linq.Expressions.MemberExpression.Expression" /> 和 <see cref="P:System.Linq.Expressions.MemberExpression.Member" /> 属性设置为指定值。</returns>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MemberExpression.Expression" />。对于静态属性，这可以为 null。</param>
      <param name="property">要将 <see cref="T:System.Reflection.PropertyInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.MemberExpression.Member" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="property" /> 为 null。- 或 -<paramref name="property" /> 所表示的属性不为 static（在 Visual Basic 中为 Shared），并且 <paramref name="expression" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="expression" />.Type 不能赋给 <paramref name="property" /> 所表示的属性的声明类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Property(System.Linq.Expressions.Expression,System.Reflection.PropertyInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.IndexExpression" />，它表示对索引属性的访问。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.IndexExpression" />。</returns>
      <param name="instance">属性所属的对象。如果该属性为 static/shared，则此对象必须为 null。</param>
      <param name="indexer">表示要编制索引的属性的 <see cref="T:System.Reflection.PropertyInfo" />。</param>
      <param name="arguments">用于为属性编制索引的 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 对象的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Property(System.Linq.Expressions.Expression,System.Reflection.PropertyInfo,System.Linq.Expressions.Expression[])">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.IndexExpression" />，它表示对索引属性的访问。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.IndexExpression" />。</returns>
      <param name="instance">属性所属的对象。如果该属性为 static/shared，则此对象必须为 null。</param>
      <param name="indexer">表示要编制索引的属性的 <see cref="T:System.Reflection.PropertyInfo" />。</param>
      <param name="arguments">用于为属性编制索引的 <see cref="T:System.Linq.Expressions.Expression" /> 对象的数组。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Property(System.Linq.Expressions.Expression,System.String)">
      <summary>创建一个表示访问属性的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MemberExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.MemberAccess" />，<see cref="P:System.Linq.Expressions.MemberExpression.Expression" /> 属性设置为 <paramref name="expression" />，并且 <see cref="P:System.Linq.Expressions.MemberExpression.Member" /> 属性设置为由 <see cref="T:System.Reflection.PropertyInfo" /> 表示的属性的 <paramref name="propertyName" />。</returns>
      <param name="expression">一个 <see cref="T:System.Linq.Expressions.Expression" />，其 <see cref="P:System.Linq.Expressions.Expression.Type" /> 包含一个名为 <paramref name="propertyName" /> 的属性。对于静态属性，这可以为 null。</param>
      <param name="propertyName">要访问的属性的名称。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 或 <paramref name="propertyName" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">没有在 <paramref name="propertyName" />.Type 或其基类型中定义名为 <paramref name="expression" /> 的属性。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Property(System.Linq.Expressions.Expression,System.String,System.Linq.Expressions.Expression[])">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.IndexExpression" />，它表示对索引属性的访问。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.IndexExpression" />。</returns>
      <param name="instance">属性所属的对象。如果该属性为 static/shared，则此对象必须为 null。</param>
      <param name="propertyName">索引器的名称。</param>
      <param name="arguments">用于为属性编制索引的 <see cref="T:System.Linq.Expressions.Expression" /> 对象的数组。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Property(System.Linq.Expressions.Expression,System.Type,System.String)">
      <summary>创建一个访问属性的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</returns>
      <param name="expression">属性的包含对象。对于静态属性，这可以为 null。</param>
      <param name="type">包含属性的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
      <param name="propertyName">要访问的属性。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PropertyOrField(System.Linq.Expressions.Expression,System.String)">
      <summary>创建一个表示访问属性或字段的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.MemberExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.MemberAccess" />，<see cref="P:System.Linq.Expressions.MemberExpression.Expression" /> 属性设置为 <paramref name="expression" />，并且 <see cref="P:System.Linq.Expressions.MemberExpression.Member" /> 属性设置为表示 <see cref="T:System.Reflection.PropertyInfo" /> 所表示的属性或字段的 <see cref="T:System.Reflection.FieldInfo" /> 或 <paramref name="propertyOrFieldName" />。</returns>
      <param name="expression">一个 <see cref="T:System.Linq.Expressions.Expression" />，其 <see cref="P:System.Linq.Expressions.Expression.Type" /> 包含一个名为 <paramref name="propertyOrFieldName" /> 的属性或字段。对于静态成员，这可以为 null。</param>
      <param name="propertyOrFieldName">要访问的属性或字段的名称。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 或 <paramref name="propertyOrFieldName" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">没有在 <paramref name="propertyOrFieldName" />.Type 或其基类型中定义名为 <paramref name="expression" /> 的属性或字段。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Quote(System.Linq.Expressions.Expression)">
      <summary>创建一个表示具有类型 <see cref="T:System.Linq.Expressions.UnaryExpression" /> 的常量值的表达式的 <see cref="T:System.Linq.Expressions.Expression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Quote" />，并且其 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 属性设置为指定值。</returns>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Reduce">
      <summary>将此节点简化为更简单的表达式。如果 CanReduce 返回 true，则它应返回有效的表达式。此方法可以返回本身必须简化的另一个节点。</summary>
      <returns>已简化的表达式。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ReduceAndCheck">
      <summary>将此节点简化为更简单的表达式。如果 CanReduce 返回 true，则它应返回有效的表达式。此方法可以返回本身必须简化的另一个节点。</summary>
      <returns>已简化的表达式。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ReduceExtensions">
      <summary>将表达式简化为已知节点类型（即非 Extension 节点）或仅在此类型为已知类型时返回表达式。</summary>
      <returns>已简化的表达式。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ReferenceEqual(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示引用相等比较的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Equal" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ReferenceNotEqual(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示引用不相等比较的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.NotEqual" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Rethrow">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它表示重新引发异常。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它表示重新引发异常。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Rethrow(System.Type)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它表示重新引发具有给定类型的异常。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它表示重新引发异常。</returns>
      <param name="type">表达式的新 <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Return(System.Linq.Expressions.LabelTarget)">
      <summary>创建一个表示 return 语句的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等于 Return，其 <see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 属性设置为 <paramref name="target" />，此外还有一个在跳转时将传递给目标标签的 null 值。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 将跳至的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Return(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression)">
      <summary>创建一个表示 return 语句的 <see cref="T:System.Linq.Expressions.GotoExpression" />。可以指定在跳转时传递给标签的值。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等于 Continue，其 <see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 属性设置为 <paramref name="target" />，此外还有一个在跳转时将传递给目标标签的 <paramref name="value" />。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 将跳至的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="value">将在跳转时传递给关联标签的值。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Return(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression,System.Type)">
      <summary>创建一个表示具有指定类型的 return 语句的 <see cref="T:System.Linq.Expressions.GotoExpression" />。可以指定在跳转时传递给标签的值。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等于 Continue，其 <see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 属性设置为 <paramref name="target" />，并且其 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性设置为 <paramref name="type" />，此外还有一个在跳转时将传递给目标标签的 <paramref name="value" />。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 将跳至的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="value">将在跳转时传递给关联标签的值。</param>
      <param name="type">要将 <see cref="T:System.Type" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Return(System.Linq.Expressions.LabelTarget,System.Type)">
      <summary>创建一个表示具有指定类型的 return 语句的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等于 Return，其 <see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 属性设置为 <paramref name="target" />，并且其 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性设置为 <paramref name="type" />，此外还有一个在跳转时将传递给目标标签的 null 值。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 将跳至的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="type">要将 <see cref="T:System.Type" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.RightShift(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示按位右移运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.RightShift" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义右移位运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.RightShift(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示按位右移运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.RightShift" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义右移位运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.RightShiftAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示按位右移赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.RightShiftAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.RightShiftAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示按位右移赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.RightShiftAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.RightShiftAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>创建一个表示按位右移赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.RightShiftAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要将 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.RuntimeVariables(System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>创建 <see cref="T:System.Linq.Expressions.RuntimeVariablesExpression" /> 的实例。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.RuntimeVariablesExpression" /> 的实例，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.RuntimeVariables" />，并且其 <see cref="P:System.Linq.Expressions.RuntimeVariablesExpression.Variables" /> 属性设置为指定的值。</returns>
      <param name="variables">用于填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.RuntimeVariablesExpression.Variables" /> 对象的集合。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.RuntimeVariables(System.Linq.Expressions.ParameterExpression[])">
      <summary>创建 <see cref="T:System.Linq.Expressions.RuntimeVariablesExpression" /> 的实例。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.RuntimeVariablesExpression" /> 的实例，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.RuntimeVariables" />，并且其 <see cref="P:System.Linq.Expressions.RuntimeVariablesExpression.Variables" /> 属性设置为指定的值。</returns>
      <param name="variables">用于填充 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.RuntimeVariablesExpression.Variables" /> 对象的数组。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Subtract(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示不进行溢出检查的算术减法运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Subtract" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义减法运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Subtract(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示不进行溢出检查的算术减法运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.Subtract" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义减法运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SubtractAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示不进行溢出检查的减法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.SubtractAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SubtractAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示不进行溢出检查的减法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.SubtractAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SubtractAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>创建一个表示不进行溢出检查的减法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.SubtractAssign" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要将 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SubtractAssignChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示进行溢出检查的减法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.SubtractAssignChecked" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SubtractAssignChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示进行溢出检查的减法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.SubtractAssignChecked" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SubtractAssignChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>创建一个表示进行溢出检查的减法赋值运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.SubtractAssignChecked" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要将 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SubtractChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示进行溢出检查的算术减法运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.SubtractChecked" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义减法运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SubtractChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示进行溢出检查的算术减法运算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.SubtractChecked" />，并且其 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="left">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带两个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定义减法运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Switch(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.SwitchCase[])">
      <summary>创建一个表示具有默认分支的 <see cref="T:System.Linq.Expressions.SwitchExpression" /> 语句的 switch。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.SwitchExpression" />。</returns>
      <param name="switchValue">要针对每个分支测试的值。</param>
      <param name="defaultBody">
        <paramref name="switchValue" /> 不匹配任何分支时，该 switch 的结果。</param>
      <param name="cases">此 switch 表达式的分支集。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Switch(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.SwitchCase})">
      <summary>创建一个表示具有默认分支的 <see cref="T:System.Linq.Expressions.SwitchExpression" /> 语句的 switch。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.SwitchExpression" />。</returns>
      <param name="switchValue">要针对每个分支测试的值。</param>
      <param name="defaultBody">
        <paramref name="switchValue" /> 不匹配任何分支时，该 switch 的结果。</param>
      <param name="comparison">要使用的相等比较方法。</param>
      <param name="cases">此 switch 表达式的分支集。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Switch(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.SwitchCase[])">
      <summary>创建一个表示具有默认分支的 <see cref="T:System.Linq.Expressions.SwitchExpression" /> 语句的 switch。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.SwitchExpression" />。</returns>
      <param name="switchValue">要针对每个分支测试的值。</param>
      <param name="defaultBody">
        <paramref name="switchValue" /> 不匹配任何分支时，该 switch 的结果。</param>
      <param name="comparison">要使用的相等比较方法。</param>
      <param name="cases">此 switch 表达式的分支集。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Switch(System.Linq.Expressions.Expression,System.Linq.Expressions.SwitchCase[])">
      <summary>创建一个表示不带默认分支的 <see cref="T:System.Linq.Expressions.SwitchExpression" /> 语句的 switch。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.SwitchExpression" />。</returns>
      <param name="switchValue">要针对每个分支测试的值。</param>
      <param name="cases">此 switch 表达式的分支集。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Switch(System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.SwitchCase})">
      <summary>创建一个表示具有默认分支的 <see cref="T:System.Linq.Expressions.SwitchExpression" /> 语句的 switch。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.SwitchExpression" />。</returns>
      <param name="type">switch 的结果类型。</param>
      <param name="switchValue">要针对每个分支测试的值。</param>
      <param name="defaultBody">
        <paramref name="switchValue" /> 不匹配任何分支时，该 switch 的结果。</param>
      <param name="comparison">要使用的相等比较方法。</param>
      <param name="cases">此 switch 表达式的分支集。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Switch(System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.SwitchCase[])">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.SwitchExpression" />，它表示具有默认分支的 switch 语句。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.SwitchExpression" />。</returns>
      <param name="type">switch 的结果类型。</param>
      <param name="switchValue">要针对每个分支测试的值。</param>
      <param name="defaultBody">
        <paramref name="switchValue" /> 不匹配任何分支时，该 switch 的结果。</param>
      <param name="comparison">要使用的相等比较方法。</param>
      <param name="cases">此 switch 表达式的分支集。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SwitchCase(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建要在 <see cref="T:System.Linq.Expressions.SwitchCase" /> 对象中使用的 <see cref="T:System.Linq.Expressions.SwitchExpression" /> 对象。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.SwitchCase" />。</returns>
      <param name="body">分支的主体。</param>
      <param name="testValues">分支的测试值。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SwitchCase(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression[])">
      <summary>创建在 <see cref="T:System.Linq.Expressions.SwitchCase" /> 中使用的 <see cref="T:System.Linq.Expressions.SwitchExpression" />。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.SwitchCase" />。</returns>
      <param name="body">分支的主体。</param>
      <param name="testValues">分支的测试值。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SymbolDocument(System.String)">
      <summary>创建 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" /> 的实例。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" />，其 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.FileName" /> 属性设置为指定值。</returns>
      <param name="fileName">要将 <see cref="T:System.String" /> 设置为与其相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.FileName" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SymbolDocument(System.String,System.Guid)">
      <summary>创建 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" /> 的实例。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" />，其 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.FileName" /> 和 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.Language" /> 属性设置为指定值。</returns>
      <param name="fileName">要将 <see cref="T:System.String" /> 设置为与其相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.FileName" />。</param>
      <param name="language">要将 <see cref="T:System.Guid" /> 设置为与其相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.Language" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SymbolDocument(System.String,System.Guid,System.Guid)">
      <summary>创建 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" /> 的实例。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" />，其 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.FileName" />、<see cref="P:System.Linq.Expressions.SymbolDocumentInfo.Language" /> 和 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.LanguageVendor" /> 属性设置为指定值。</returns>
      <param name="fileName">要将 <see cref="T:System.String" /> 设置为与其相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.FileName" />。</param>
      <param name="language">要将 <see cref="T:System.Guid" /> 设置为与其相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.Language" />。</param>
      <param name="languageVendor">要将 <see cref="T:System.Guid" /> 设置为与其相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.LanguageVendor" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SymbolDocument(System.String,System.Guid,System.Guid,System.Guid)">
      <summary>创建 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" /> 的实例。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" />，其 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.FileName" />、<see cref="P:System.Linq.Expressions.SymbolDocumentInfo.Language" />、<see cref="P:System.Linq.Expressions.SymbolDocumentInfo.LanguageVendor" /> 和 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.DocumentType" /> 属性设置为指定值。</returns>
      <param name="fileName">要将 <see cref="T:System.String" /> 设置为与其相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.FileName" />。</param>
      <param name="language">要将 <see cref="T:System.Guid" /> 设置为与其相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.Language" />。</param>
      <param name="languageVendor">要将 <see cref="T:System.Guid" /> 设置为与其相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.LanguageVendor" />。</param>
      <param name="documentType">要将 <see cref="T:System.Guid" /> 设置为与其相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.DocumentType" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Throw(System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它表示引发异常。</summary>
      <returns>一个表示异常的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="value">一个 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Throw(System.Linq.Expressions.Expression,System.Type)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，它表示引发具有给定类型的异常。</summary>
      <returns>一个表示异常的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="value">一个 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="type">表达式的新 <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ToString">
      <summary>返回 <see cref="T:System.Linq.Expressions.Expression" /> 的的文本化表示形式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" /> 的文本化表示形式。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.TryCatch(System.Linq.Expressions.Expression,System.Linq.Expressions.CatchBlock[])">
      <summary>创建一个表示 try 块的 <see cref="T:System.Linq.Expressions.TryExpression" />，该 try 块包含任意数量的 catch 语句，但不包含 fault 和 finally 块。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.TryExpression" />。</returns>
      <param name="body">try 块的主体。</param>
      <param name="handlers">包含零个或多个 <see cref="T:System.Linq.Expressions.CatchBlock" /> 表达式的数组，这些表达式表示要与 try 块关联的 catch 语句。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.TryCatchFinally(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.CatchBlock[])">
      <summary>创建一个表示 try 块的 <see cref="T:System.Linq.Expressions.TryExpression" />，该 try 块包含任意数量的 catch 语句和一个 finally 块。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.TryExpression" />。</returns>
      <param name="body">try 块的主体。</param>
      <param name="finally">finally 块的主体。</param>
      <param name="handlers">包含零个或多个 <see cref="T:System.Linq.Expressions.CatchBlock" /> 表达式的数组，这些表达式表示要与 try 块关联的 catch 语句。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.TryFault(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示 try 块的 <see cref="T:System.Linq.Expressions.TryExpression" />，该 try 块包含一个 fault 块，但不包含 catch 语句。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.TryExpression" />。</returns>
      <param name="body">try 块的主体。</param>
      <param name="fault">fault 块的主体。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.TryFinally(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个表示 try 块的 <see cref="T:System.Linq.Expressions.TryExpression" />，该 try 块包含一个 finally 块，但不包含 catch 语句。</summary>
      <returns>创建的 <see cref="T:System.Linq.Expressions.TryExpression" />。</returns>
      <param name="body">try 块的主体。</param>
      <param name="finally">finally 块的主体。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.TryGetActionType(System.Type[],System.Type@)">
      <summary>创建一个 <see cref="P:System.Linq.Expressions.Expression.Type" /> 对象，它表示具有特定类型参数的泛型 System.Action 委托类型。</summary>
      <returns>如果已为特定的 <paramref name="typeArgs" /> 创建泛型 System.Action 委托类型，则为 true；否则为 false。</returns>
      <param name="typeArgs">Type 对象的数组，这些对象指定 System.Action 委托类型的类型参数。</param>
      <param name="actionType">在此方法返回时，包含具有特定类型参数的泛型 System.Action 委托类型。如果没有与 <paramref name="typeArgs" /> 匹配的泛型 System.Action 委托，则包含 null。此参数以未初始化状态传递。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.TryGetFuncType(System.Type[],System.Type@)">
      <summary>创建一个 <see cref="P:System.Linq.Expressions.Expression.Type" /> 对象，它表示具有特定类型参数的泛型 System.Func 委托类型。最后一个类型参数指定已创建委托的返回类型。</summary>
      <returns>如果已为特定的 <paramref name="typeArgs" /> 创建泛型 System.Func 委托类型，则为 true；否则为 false。</returns>
      <param name="typeArgs">Type 对象的数组，这些对象指定 System.Func 委托类型的类型参数。</param>
      <param name="funcType">在此方法返回时，包含具有特定类型参数的泛型 System.Func 委托类型。如果没有与 <paramref name="typeArgs" /> 匹配的泛型 System.Func 委托，则包含 null。此参数以未初始化状态传递。</param>
    </member>
    <member name="P:System.Linq.Expressions.Expression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="T:System.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.TypeAs(System.Linq.Expressions.Expression,System.Type)">
      <summary>创建一个表示显式引用或装箱转换的 <see cref="T:System.Linq.Expressions.UnaryExpression" />（如果转换失败，则提供 null）。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.TypeAs" />，并且其 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 和 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性设置为指定值。</returns>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <param name="type">要将 <see cref="T:System.Type" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 或 <paramref name="type" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.TypeEqual(System.Linq.Expressions.Expression,System.Type)">
      <summary>创建一个比较运行时类型标识的 <see cref="T:System.Linq.Expressions.TypeBinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.TypeBinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="M:System.Linq.Expressions.Expression.TypeEqual(System.Linq.Expressions.Expression,System.Type)" /> 并且 <see cref="T:System.Linq.Expressions.Expression" /> 和 <see cref="P:System.Linq.Expressions.TypeBinaryExpression.TypeOperand" /> 属性设置为指定值。</returns>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="type">要将 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.TypeBinaryExpression.TypeOperand" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.TypeIs(System.Linq.Expressions.Expression,System.Type)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.TypeBinaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.TypeBinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.TypeIs" /> 并且 <see cref="P:System.Linq.Expressions.TypeBinaryExpression.Expression" /> 和 <see cref="P:System.Linq.Expressions.TypeBinaryExpression.TypeOperand" /> 属性设置为指定值。</returns>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.TypeBinaryExpression.Expression" />。</param>
      <param name="type">要将 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.TypeBinaryExpression.TypeOperand" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 或 <paramref name="type" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.UnaryPlus(System.Linq.Expressions.Expression)">
      <summary>创建一个表示一元正运算的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.UnaryPlus" />，并且其 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 属性设置为指定值。</returns>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有为 <paramref name="expression" />.Type 定义一元正运算符。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.UnaryPlus(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>创建一个表示一元正运算的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性等于 <see cref="F:System.Linq.Expressions.ExpressionType.UnaryPlus" />，并且其 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 和 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" /> 属性设置为指定值。</returns>
      <param name="expression">要将 <see cref="T:System.Linq.Expressions.Expression" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <param name="method">要将 <see cref="T:System.Reflection.MethodInfo" /> 属性设置为与其相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不为 null 且其表示的方法返回 void；不为 static（在 Visual Basic 中不为 Shared）；或者不是正好带一个参数。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 为 null，并且没有为 <paramref name="expression" />.Type 定义一元正运算符。- 或 -<paramref name="expression" />.Type（如果它是可以为 null 的值类型，则取其相应的不可以为 null 的类型）不能赋给 <paramref name="method" /> 所表示的方法的参数类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Unbox(System.Linq.Expressions.Expression,System.Type)">
      <summary>创建一个表示显式取消装箱的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" /> 的一个实例。</returns>
      <param name="expression">要取消装箱的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="type">表达式的新 <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Variable(System.Type)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 节点，该节点可用于标识表达式树中的参数或变量。</summary>
      <returns>具有指定的名称和类型的 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 节点</returns>
      <param name="type">参数或变量的类型。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Variable(System.Type,System.String)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 节点，该节点可用于标识表达式树中的参数或变量。</summary>
      <returns>具有指定的名称和类型的 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 节点。</returns>
      <param name="type">参数或变量的类型。</param>
      <param name="name">参数或变量的名称。此名称仅用于调试或打印目的。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.VisitChildren(System.Linq.Expressions.ExpressionVisitor)">
      <summary>简化节点，然后对简化的表达式调用访问者委托。该方法在节点不可简化时引发异常。</summary>
      <returns>要访问的表达式，或应在树中替换此表达式的表达式。</returns>
      <param name="visitor">
        <see cref="T:System.Func`2" /> 的一个实例。</param>
    </member>
    <member name="T:System.Linq.Expressions.Expression`1">
      <summary>以表达式目录树的形式将强类型 lambda 表达式表示为数据结构。此类不能被继承。</summary>
      <typeparam name="TDelegate">
        <see cref="T:System.Linq.Expressions.Expression`1" /> 表示的委托的类型。</typeparam>
    </member>
    <member name="M:System.Linq.Expressions.Expression`1.Compile">
      <summary>将表达式树描述的 lambda 表达式编译为可执行代码，并生成表示该 lambda 表达式的委托。</summary>
      <returns>一个 <paramref name="TDelegate" /> 类型的委托，它表示由 <see cref="T:System.Linq.Expressions.Expression`1" /> 描述的已编译的 lambda 表达式。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression`1.Update(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="body">结果的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 属性。</param>
      <param name="parameters">结果的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.ExpressionType">
      <summary>描述表达式目录树的节点的节点类型。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Add">
      <summary>加法运算，如 a + b，针对数值操作数，不进行溢出检查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.AddAssign">
      <summary>加法复合赋值运算，如 (a += b)，针对数值操作数，不进行溢出检查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.AddAssignChecked">
      <summary>加法复合赋值运算，如 (a += b)，针对数值操作数，进行溢出检查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.AddChecked">
      <summary>加法运算，如 (a + b)，针对数值操作数，进行溢出检查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.And">
      <summary>按位或逻辑 AND 运算，如 C# 中的 (a &amp; b) 和 Visual Basic 中的 (a And b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.AndAlso">
      <summary>条件 AND 运算，它仅在第一个操作数的计算结果为 true 时才计算第二个操作数。它与 C# 中的 (a &amp;&amp; b) 和 Visual Basic 中的 (a AndAlso b) 对应。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.AndAssign">
      <summary>按位或逻辑 AND 复合赋值运算，如 C# 中的 (a &amp;= b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.ArrayIndex">
      <summary>一维数组中的索引运算，如 C# 中的 array[index] 或 Visual Basic 中的 array(index)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.ArrayLength">
      <summary>获取一维数组长度的运算，如 array.Length。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Assign">
      <summary>赋值运算，如 (a = b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Block">
      <summary>表达式块。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Call">
      <summary>方法调用，如在 obj.sampleMethod() 表达式中。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Coalesce">
      <summary>表示 null 合并运算的节点，如 C# 中的 (a ?? b) 或 Visual Basic 中的 If(a, b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Conditional">
      <summary>条件运算，如 C# 中的 a &gt; b ? a : b 或 Visual Basic 中的 If(a &gt; b, a, b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Constant">
      <summary>一个常量值。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Convert">
      <summary>强制转换或转换运算，如 C#中的 (SampleType)obj 或 Visual Basic 中的 CType(obj, SampleType)。对于数值转换，如果转换后的值对于目标类型来说太大，这不会引发异常。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.ConvertChecked">
      <summary>强制转换或转换运算，如 C#中的 (SampleType)obj 或 Visual Basic 中的 CType(obj, SampleType)。对于数值转换，如果转换后的值与目标类型大小不符，则引发异常。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.DebugInfo">
      <summary>调试信息。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Decrement">
      <summary>一元递减运算，如 C# 和 Visual Basic 中的 (a - 1)。不应就地修改 a 对象。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Default">
      <summary>默认值。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Divide">
      <summary>除法运算，如 (a / b)，针对数值操作数。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.DivideAssign">
      <summary>除法复合赋值运算，如 (a /= b)，针对数值操作数。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Dynamic">
      <summary>动态操作。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Equal">
      <summary>表示相等比较的节点，如 C# 中的 (a == b) 或 Visual Basic 中的 (a = b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.ExclusiveOr">
      <summary>按位或逻辑 XOR 运算，如 C# 中的 (a ^ b) 或 Visual Basic 中的 (a Xor b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.ExclusiveOrAssign">
      <summary>按位或逻辑 XOR 复合赋值运算，如 C# 中的 (a ^= b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Extension">
      <summary>扩展表达式。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Goto">
      <summary>“跳转”表达式，如 C# 中的 goto Label 或 Visual Basic 中的 GoTo Label。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.GreaterThan">
      <summary>“大于”比较，如 (a &gt; b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.GreaterThanOrEqual">
      <summary>“大于或等于”比较，如 (a &gt;= b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Increment">
      <summary>一元递增运算，如 C# 和 Visual Basic 中的 (a + 1)。不应就地修改 a 对象。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Index">
      <summary>索引运算或访问使用参数的属性的运算。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Invoke">
      <summary>调用委托或 lambda 表达式的运算，如 sampleDelegate.Invoke()。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.IsFalse">
      <summary>false 条件值。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.IsTrue">
      <summary>true 条件值。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Label">
      <summary>标签。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Lambda">
      <summary>lambda 表达式，如 C# 中的 a =&gt; a + a 或 Visual Basic 中的 Function(a) a + a。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.LeftShift">
      <summary>按位左移运算，如 (a &lt;&lt; b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.LeftShiftAssign">
      <summary>按位左移复合赋值运算，如 (a &lt;&lt;= b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.LessThan">
      <summary>“小于”比较，如 (a &lt; b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.LessThanOrEqual">
      <summary>“小于或等于”比较，如 (a &lt;= b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.ListInit">
      <summary>创建新的 <see cref="T:System.Collections.IEnumerable" /> 对象并从元素列表中初始化该对象的运算，如 C# 中的 new List&lt;SampleType&gt;(){ a, b, c } 或 Visual Basic 中的 Dim sampleList = { a, b, c }。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Loop">
      <summary>循环，如 for 或 while。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.MemberAccess">
      <summary>从字段或属性进行读取的运算，如 obj.SampleProperty。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.MemberInit">
      <summary>创建新的对象并初始化其一个或多个成员的运算，如 C# 中的 new Point { X = 1, Y = 2 } 或 Visual Basic 中的 New Point With {.X = 1, .Y = 2}。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Modulo">
      <summary>算术余数运算，如 C# 中的 (a % b) 或 Visual Basic 中的 (a Mod b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.ModuloAssign">
      <summary>算术余数复合赋值运算，如 C# 中的 (a %= b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Multiply">
      <summary>乘法运算，如 (a * b)，针对数值操作数，不进行溢出检查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.MultiplyAssign">
      <summary>乘法复合赋值运算，如 (a *= b)，针对数值操作数，不进行溢出检查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.MultiplyAssignChecked">
      <summary>乘法复合赋值运算，如 (a *= b)，针对数值操作数，进行溢出检查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.MultiplyChecked">
      <summary>乘法运算，如 (a * b)，针对数值操作数，进行溢出检查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Negate">
      <summary>算术求反运算，如 (-a)。不应就地修改 a 对象。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.NegateChecked">
      <summary>算术求反运算，如 (-a)，进行溢出检查。不应就地修改 a 对象。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.New">
      <summary>调用构造函数创建新对象的运算，如 new SampleType()。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.NewArrayBounds">
      <summary>创建新数组（其中每个维度的界限均已指定）的运算，如 C# 中的 new SampleType[dim1, dim2] 或 Visual Basic 中的 New SampleType(dim1, dim2)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.NewArrayInit">
      <summary>创建新的一维数组并从元素列表中初始化该数组的运算，如 C# 中的 new SampleType[]{a, b, c} 或 Visual Basic 中的 New SampleType(){a, b, c}。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Not">
      <summary>按位求补运算或逻辑求反运算。在 C# 中，它与整型的 (~a) 和布尔值的 (!a) 等效。在 Visual Basic 中，它与 (Not a) 等效。不应就地修改 a 对象。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.NotEqual">
      <summary>不相等比较，如 C# 中的 (a != b) 或 Visual Basic 中的 (a &lt;&gt; b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.OnesComplement">
      <summary>二进制反码运算，如 C# 中的 (~a)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Or">
      <summary>按位或逻辑 OR 运算，如 C# 中的 (a | b) 或 Visual Basic 中的 (a Or b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.OrAssign">
      <summary>按位或逻辑 OR 复合赋值运算，如 C# 中的 (a |= b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.OrElse">
      <summary>短路条件 OR 运算，如 C# 中的 (a || b) 或 Visual Basic 中的 (a OrElse b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Parameter">
      <summary>对在表达式上下文中定义的参数或变量的引用。有关详细信息，请参阅<see cref="T:System.Linq.Expressions.ParameterExpression" />。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.PostDecrementAssign">
      <summary>一元后缀递减，如 (a--)。应就地修改 a 对象。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.PostIncrementAssign">
      <summary>一元后缀递增，如 (a++)。应就地修改 a 对象。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Power">
      <summary>对某个数字进行幂运算的数学运算，如 Visual Basic 中的 (a ^ b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.PowerAssign">
      <summary>对某个数字进行幂运算的复合赋值运算，如 Visual Basic 中的 (a ^= b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.PreDecrementAssign">
      <summary>一元前缀递减，如 (--a)。应就地修改 a 对象。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.PreIncrementAssign">
      <summary>一元前缀递增，如 (++a)。应就地修改 a 对象。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Quote">
      <summary>具有类型为 <see cref="T:System.Linq.Expressions.Expression" /> 的常量值的表达式。<see cref="F:System.Linq.Expressions.ExpressionType.Quote" /> 节点可包含对参数的引用，这些参数在该节点表示的表达式的上下文中定义。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.RightShift">
      <summary>按位右移运算，如 (a &gt;&gt; b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.RightShiftAssign">
      <summary>按位右移复合赋值运算，如 (a &gt;&gt;= b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.RuntimeVariables">
      <summary>运行时变量的列表。有关详细信息，请参阅<see cref="T:System.Linq.Expressions.RuntimeVariablesExpression" />。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Subtract">
      <summary>减法运算，如 (a - b)，针对数值操作数，不进行溢出检查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.SubtractAssign">
      <summary>减法复合赋值运算，如 (a -= b)，针对数值操作数，不进行溢出检查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.SubtractAssignChecked">
      <summary>减法复合赋值运算，如 (a -= b)，针对数值操作数，进行溢出检查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.SubtractChecked">
      <summary>算术减法运算，如 (a - b)，针对数值操作数，进行溢出检查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Switch">
      <summary>多分支选择运算，如 C# 中的 switch 或 Visual Basic 中的 Select Case。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Throw">
      <summary>引发异常的运算，如 throw new Exception()。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Try">
      <summary>try-catch 表达式。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.TypeAs">
      <summary>显式引用或装箱转换，其中如果转换失败则提供 null，如 C# 中的 (obj as SampleType) 或 Visual Basic 中的 TryCast(obj, SampleType)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.TypeEqual">
      <summary>确切类型测试。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.TypeIs">
      <summary>类型测试，如 C# 中的 obj is SampleType 或 Visual Basic 中的 TypeOf obj is SampleType。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.UnaryPlus">
      <summary>一元加法运算，如 (+a)。预定义的一元加法运算的结果是操作数的值，但用户定义的实现可以产生特殊结果。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Unbox">
      <summary>取消装箱值类型运算，如 MSIL 中的 unbox 和 unbox.any 指令。</summary>
    </member>
    <member name="T:System.Linq.Expressions.ExpressionVisitor">
      <summary>表示表达式树的访问者或重写者。</summary>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.#ctor">
      <summary>初始化 <see cref="T:System.Linq.Expressions.ExpressionVisitor" /> 的新实例。</summary>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.Visit(System.Collections.ObjectModel.ReadOnlyCollection{System.Linq.Expressions.Expression})">
      <summary>将表达式列表调度到此类中更专用的访问方法之一。</summary>
      <returns>如果修改了任何一个元素，则为修改后的表达式列表；否则返回原始的表达式列表。</returns>
      <param name="nodes">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.Visit``1(System.Collections.ObjectModel.ReadOnlyCollection{``0},System.Func{``0,``0})">
      <summary>使用指定的元素访问者访问集合中的所有节点。</summary>
      <returns>如果修改了任何元素，则为修改后的节点列表；否则返回原始的节点列表。</returns>
      <param name="nodes">要访问的节点。</param>
      <param name="elementVisitor">一个委托，访问单个元素，还可以将它替换为新元素。</param>
      <typeparam name="T">节点的类型。</typeparam>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.Visit(System.Linq.Expressions.Expression)">
      <summary>将表达式调度到此类中更专用的访问方法之一。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitAndConvert``1(System.Collections.ObjectModel.ReadOnlyCollection{``0},System.String)">
      <summary>访问表达式，将结果强制转换回原始的表达式类型。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="nodes">要访问的表达式。</param>
      <param name="callerName">调用方法的名称，用于报告更好的错误消息。</param>
      <typeparam name="T">表达式类型。</typeparam>
      <exception cref="T:System.InvalidOperationException">此节点的访问方法返回不同的类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitAndConvert``1(``0,System.String)">
      <summary>访问表达式，将结果强制转换回原始的表达式类型。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
      <param name="callerName">调用方法的名称，用于报告更好的错误消息。</param>
      <typeparam name="T">表达式类型。</typeparam>
      <exception cref="T:System.InvalidOperationException">此节点的访问方法返回不同的类型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitBinary(System.Linq.Expressions.BinaryExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.BinaryExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitBlock(System.Linq.Expressions.BlockExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.BlockExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitCatchBlock(System.Linq.Expressions.CatchBlock)">
      <summary>访问 <see cref="T:System.Linq.Expressions.CatchBlock" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitConditional(System.Linq.Expressions.ConditionalExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.ConditionalExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitConstant(System.Linq.Expressions.ConstantExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.ConstantExpression" />。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitDebugInfo(System.Linq.Expressions.DebugInfoExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.DebugInfoExpression" />。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitDefault(System.Linq.Expressions.DefaultExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.DefaultExpression" />。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitElementInit(System.Linq.Expressions.ElementInit)">
      <summary>访问 <see cref="T:System.Linq.Expressions.ElementInit" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitExtension(System.Linq.Expressions.Expression)">
      <summary>访问扩展表达式的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitGoto(System.Linq.Expressions.GotoExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.GotoExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitIndex(System.Linq.Expressions.IndexExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.IndexExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitInvocation(System.Linq.Expressions.InvocationExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.InvocationExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitLabel(System.Linq.Expressions.LabelExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.LabelExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitLabelTarget(System.Linq.Expressions.LabelTarget)">
      <summary>访问 <see cref="T:System.Linq.Expressions.LabelTarget" />。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitLambda``1(System.Linq.Expressions.Expression{``0})">
      <summary>访问 <see cref="T:System.Linq.Expressions.Expression`1" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
      <typeparam name="T">委托的类型。</typeparam>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitListInit(System.Linq.Expressions.ListInitExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.ListInitExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitLoop(System.Linq.Expressions.LoopExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.LoopExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitMember(System.Linq.Expressions.MemberExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.MemberExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(System.Linq.Expressions.MemberAssignment)">
      <summary>访问 <see cref="T:System.Linq.Expressions.MemberAssignment" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(System.Linq.Expressions.MemberBinding)">
      <summary>访问 <see cref="T:System.Linq.Expressions.MemberBinding" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(System.Linq.Expressions.MemberInitExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.MemberInitExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitMemberListBinding(System.Linq.Expressions.MemberListBinding)">
      <summary>访问 <see cref="T:System.Linq.Expressions.MemberListBinding" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitMemberMemberBinding(System.Linq.Expressions.MemberMemberBinding)">
      <summary>访问 <see cref="T:System.Linq.Expressions.MemberMemberBinding" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.MethodCallExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitNew(System.Linq.Expressions.NewExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.NewExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitNewArray(System.Linq.Expressions.NewArrayExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.NewArrayExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitParameter(System.Linq.Expressions.ParameterExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.ParameterExpression" />。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitRuntimeVariables(System.Linq.Expressions.RuntimeVariablesExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.RuntimeVariablesExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitSwitch(System.Linq.Expressions.SwitchExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.SwitchExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitSwitchCase(System.Linq.Expressions.SwitchCase)">
      <summary>访问 <see cref="T:System.Linq.Expressions.SwitchCase" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitTry(System.Linq.Expressions.TryExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.TryExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitTypeBinary(System.Linq.Expressions.TypeBinaryExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.TypeBinaryExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitUnary(System.Linq.Expressions.UnaryExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.UnaryExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则为修改后的表达式；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="T:System.Linq.Expressions.GotoExpression">
      <summary>表示无条件跳转。这包括 return 语句、break 和 continue 语句以及其他跳转。</summary>
    </member>
    <member name="P:System.Linq.Expressions.GotoExpression.Kind">
      <summary>“转到”表达式的种类。仅供参考。</summary>
      <returns>表示“转到”表达式种类的 <see cref="T:System.Linq.Expressions.GotoExpressionKind" /> 对象。</returns>
    </member>
    <member name="P:System.Linq.Expressions.GotoExpression.NodeType">
      <summary>返回此 <see cref="T:System.Linq.Expressions.Expression" /> 的节点类型。</summary>
      <returns>用于表示此表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.GotoExpression.Target">
      <summary>此节点跳转到的目标标签。</summary>
      <returns>表示此节点的目标标签的 <see cref="T:System.Linq.Expressions.LabelTarget" /> 对象。</returns>
    </member>
    <member name="P:System.Linq.Expressions.GotoExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.GotoExpression.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.GotoExpression.Update(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression)">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="target">结果的 <see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 属性。</param>
      <param name="value">结果的 <see cref="P:System.Linq.Expressions.GotoExpression.Value" /> 属性。</param>
    </member>
    <member name="P:System.Linq.Expressions.GotoExpression.Value">
      <summary>传递到目标的值或 null（如果目标类型为 System.Void）。</summary>
      <returns>表示传递到目标的值或 null 的 <see cref="T:System.Linq.Expressions.Expression" /> 对象。</returns>
    </member>
    <member name="T:System.Linq.Expressions.GotoExpressionKind">
      <summary>指定此 <see cref="T:System.Linq.Expressions.GotoExpression" /> 表示的跳转种类。</summary>
    </member>
    <member name="F:System.Linq.Expressions.GotoExpressionKind.Break">
      <summary>一个表示 break 语句的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</summary>
    </member>
    <member name="F:System.Linq.Expressions.GotoExpressionKind.Continue">
      <summary>一个表示 continue 语句的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</summary>
    </member>
    <member name="F:System.Linq.Expressions.GotoExpressionKind.Goto">
      <summary>一个表示跳转到某个位置的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</summary>
    </member>
    <member name="F:System.Linq.Expressions.GotoExpressionKind.Return">
      <summary>一个表示 return 语句的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</summary>
    </member>
    <member name="T:System.Linq.Expressions.IArgumentProvider"></member>
    <member name="P:System.Linq.Expressions.IArgumentProvider.ArgumentCount"></member>
    <member name="M:System.Linq.Expressions.IArgumentProvider.GetArgument(System.Int32)"></member>
    <member name="T:System.Linq.Expressions.IDynamicExpression"></member>
    <member name="M:System.Linq.Expressions.IDynamicExpression.CreateCallSite"></member>
    <member name="P:System.Linq.Expressions.IDynamicExpression.DelegateType"></member>
    <member name="M:System.Linq.Expressions.IDynamicExpression.Rewrite(System.Linq.Expressions.Expression[])"></member>
    <member name="T:System.Linq.Expressions.IndexExpression">
      <summary>表示编制属性或数组的索引。</summary>
    </member>
    <member name="P:System.Linq.Expressions.IndexExpression.Arguments">
      <summary>获取将用于编制属性或数组索引的参数。</summary>
      <returns>包含将用于编制属性或数组索引的参数的只读集合。</returns>
    </member>
    <member name="P:System.Linq.Expressions.IndexExpression.Indexer">
      <summary>如果表达式表示索引属性，则获取属性的 <see cref="T:System.Reflection.PropertyInfo" />，否则返回 null。</summary>
      <returns>如果表达式表示索引属性，则为属性的 <see cref="T:System.Reflection.PropertyInfo" />，否则为 null。</returns>
    </member>
    <member name="P:System.Linq.Expressions.IndexExpression.NodeType">
      <summary>返回此 <see cref="T:System.Linq.Expressions.Expression" /> 的节点类型。</summary>
      <returns>用于表示此表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.IndexExpression.Object">
      <summary>要编制索引的对象。</summary>
      <returns>表示要编制索引的对象的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.IndexExpression.System#Linq#Expressions#IArgumentProvider#ArgumentCount"></member>
    <member name="M:System.Linq.Expressions.IndexExpression.System#Linq#Expressions#IArgumentProvider#GetArgument(System.Int32)"></member>
    <member name="P:System.Linq.Expressions.IndexExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.IndexExpression.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.IndexExpression.Update(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="object">结果的 <see cref="P:System.Linq.Expressions.IndexExpression.Object" /> 属性。</param>
      <param name="arguments">结果的 <see cref="P:System.Linq.Expressions.IndexExpression.Arguments" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.InvocationExpression">
      <summary>表示将委托或 lambda 表达式应用于参数表达式列表的表达式。</summary>
    </member>
    <member name="P:System.Linq.Expressions.InvocationExpression.Arguments">
      <summary>获取对其应用委托或 lambda 表达式的参数。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" /> 对象的 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />，表示对其应用委托的参数。</returns>
    </member>
    <member name="P:System.Linq.Expressions.InvocationExpression.Expression">
      <summary>获取要应用的委托或 lambda 表达式。</summary>
      <returns>表示要应用的委托的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.InvocationExpression.NodeType">
      <summary>返回此表达式的节点类型。扩展节点应在重写此方法时返回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>该表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.InvocationExpression.System#Linq#Expressions#IArgumentProvider#ArgumentCount"></member>
    <member name="M:System.Linq.Expressions.InvocationExpression.System#Linq#Expressions#IArgumentProvider#GetArgument(System.Int32)"></member>
    <member name="P:System.Linq.Expressions.InvocationExpression.Type">
      <summary>获取此 <see cref="P:System.Linq.Expressions.InvocationExpression.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.InvocationExpression.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.InvocationExpression.Update(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="expression">结果的 <see cref="P:System.Linq.Expressions.InvocationExpression.Expression" /> 属性。</param>
      <param name="arguments">结果的 <see cref="P:System.Linq.Expressions.InvocationExpression.Arguments" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.LabelExpression">
      <summary>表示一个标签，可以将该标签放置在任何 <see cref="T:System.Linq.Expressions.Expression" /> 上下文中。如果已跳转到该标签，则它将获取由对应的 <see cref="T:System.Linq.Expressions.GotoExpression" /> 提供的值。否则，它接收 <see cref="P:System.Linq.Expressions.LabelExpression.DefaultValue" /> 中的值。如果 <see cref="T:System.Type" /> 等于 System.Void，则不应提供值。</summary>
    </member>
    <member name="P:System.Linq.Expressions.LabelExpression.DefaultValue">
      <summary>通过常规控制流到达标签（例如，不跳转到标签）时 <see cref="T:System.Linq.Expressions.LabelExpression" /> 的值。</summary>
      <returns>Expression 对象，表示 <see cref="T:System.Linq.Expressions.LabelExpression" /> 的值。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LabelExpression.NodeType">
      <summary>返回此 <see cref="T:System.Linq.Expressions.Expression" /> 的节点类型。</summary>
      <returns>用于表示此表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LabelExpression.Target">
      <summary>此标签与其关联的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</summary>
      <returns>此标签与其关联的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LabelExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.LabelExpression.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.LabelExpression.Update(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression)">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="target">结果的 <see cref="P:System.Linq.Expressions.LabelExpression.Target" /> 属性。</param>
      <param name="defaultValue">结果的 <see cref="P:System.Linq.Expressions.LabelExpression.DefaultValue" /> 属性</param>
    </member>
    <member name="T:System.Linq.Expressions.LabelTarget">
      <summary>用于表示 <see cref="T:System.Linq.Expressions.GotoExpression" /> 的目标。</summary>
    </member>
    <member name="P:System.Linq.Expressions.LabelTarget.Name">
      <summary>获取标签的名称。</summary>
      <returns>标签的名称。</returns>
    </member>
    <member name="M:System.Linq.Expressions.LabelTarget.ToString">
      <summary>返回表示当前 <see cref="T:System.Object" /> 的 <see cref="T:System.String" />。</summary>
      <returns>
        <see cref="T:System.String" />，表示当前的 <see cref="T:System.Object" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LabelTarget.Type">
      <summary>跳转到标签时传递的值的类型或 <see cref="T:System.Void" />（如果不应传递值）。</summary>
      <returns>
        <see cref="T:System.Type" /> 对象，表示跳转到标签时传递的值的类型或 <see cref="T:System.Void" />（如果不应传递值）</returns>
    </member>
    <member name="T:System.Linq.Expressions.LambdaExpression">
      <summary>描述一个 lambda 表达式。这将捕获与 .NET 方法体类似的代码块。</summary>
    </member>
    <member name="P:System.Linq.Expressions.LambdaExpression.Body">
      <summary>获取 lambda 表达式的主体。</summary>
      <returns>一个表示 lambda 表达式主体的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.LambdaExpression.Compile">
      <summary>生成表示 lambda 表达式的委托。</summary>
      <returns>一个 <see cref="T:System.Delegate" />，它包含 lambda 表达式的已编译版本。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LambdaExpression.Name">
      <summary>获取 lambda 表达式的名称。</summary>
      <returns>lambda 表达式的名称。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LambdaExpression.NodeType">
      <summary>返回此 <see cref="T:System.Linq.Expressions.Expression" /> 的节点类型。</summary>
      <returns>用于表示此表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LambdaExpression.Parameters">
      <summary>获取 lambda 表达式的参数。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 对象的 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />，表示 lambda 表达式的参数。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LambdaExpression.ReturnType">
      <summary>获取 lambda 表达式的返回类型。</summary>
      <returns>表示 lambda 表达式的类型的 <see cref="T:System.Type" /> 对象。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LambdaExpression.TailCall">
      <summary>获取一个值，该值指示是否将通过尾调用优化来编译 lambda 表达式。</summary>
      <returns>如果将通过尾调用优化来编译 lambda 表达式，则为 True；否则为 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LambdaExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.LambdaExpression.Type" />。</returns>
    </member>
    <member name="T:System.Linq.Expressions.ListInitExpression">
      <summary>表示包含集合初始值设定项的构造函数调用。</summary>
    </member>
    <member name="P:System.Linq.Expressions.ListInitExpression.CanReduce">
      <summary>获取一个值，该值指示是否可以减小此表达式树节点。</summary>
      <returns>如果可以简化节点，则为 True；否则为 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ListInitExpression.Initializers">
      <summary>获取用于初始化集合的元素初始值设定项。</summary>
      <returns>表示用于初始化集合的元素的 <see cref="T:System.Linq.Expressions.ElementInit" /> 对象的 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ListInitExpression.NewExpression">
      <summary>获取包含对集合类型的构造函数的调用的表达式。</summary>
      <returns>表示对集合类型的构造函数的调用的 <see cref="T:System.Linq.Expressions.NewExpression" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ListInitExpression.NodeType">
      <summary>返回此 <see cref="T:System.Linq.Expressions.Expression" /> 的节点类型。</summary>
      <returns>用于表示此表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.ListInitExpression.Reduce">
      <summary>将二进制表达式节点简化为更简单的表达式。</summary>
      <returns>已简化的表达式。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ListInitExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.ListInitExpression.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.ListInitExpression.Update(System.Linq.Expressions.NewExpression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ElementInit})">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="newExpression">结果的 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" /> 属性。</param>
      <param name="initializers">结果的 <see cref="P:System.Linq.Expressions.ListInitExpression.Initializers" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.LoopExpression">
      <summary>表示无限循环。可以使用“break”退出它。</summary>
    </member>
    <member name="P:System.Linq.Expressions.LoopExpression.Body">
      <summary>获取作为循环体的 <see cref="T:System.Linq.Expressions.Expression" />。</summary>
      <returns>作为循环体的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LoopExpression.BreakLabel">
      <summary>获取循环体用作 break 语句目标的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</summary>
      <returns>循环体用作 break 语句目标的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LoopExpression.ContinueLabel">
      <summary>获取循环体用作 continue 语句目标的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</summary>
      <returns>循环体用作 continue 语句目标的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LoopExpression.NodeType">
      <summary>返回此表达式的节点类型。扩展节点应在重写此方法时返回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>该表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LoopExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.LoopExpression.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.LoopExpression.Update(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression)">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="breakLabel">结果的 <see cref="P:System.Linq.Expressions.LoopExpression.BreakLabel" /> 属性。</param>
      <param name="continueLabel">结果的 <see cref="P:System.Linq.Expressions.LoopExpression.ContinueLabel" /> 属性。</param>
      <param name="body">结果的 <see cref="P:System.Linq.Expressions.LoopExpression.Body" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.MemberAssignment">
      <summary>表示针对对象的字段或属性的赋值运算。</summary>
    </member>
    <member name="P:System.Linq.Expressions.MemberAssignment.Expression">
      <summary>获取要分配给字段或属性的表达式。</summary>
      <returns>表示要分配给字段或属性的值的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.MemberAssignment.Update(System.Linq.Expressions.Expression)">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="expression">结果的 <see cref="P:System.Linq.Expressions.MemberAssignment.Expression" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.MemberBinding">
      <summary>提供一种基类，该基类派生表示绑定的类，这些绑定用于初始化新创建对象的成员。</summary>
    </member>
    <member name="P:System.Linq.Expressions.MemberBinding.BindingType">
      <summary>获取所表示的绑定类型。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberBindingType" /> 值之一。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MemberBinding.Member">
      <summary>获取要初始化的字段或属性。</summary>
      <returns>表示要初始化的字段或属性的 <see cref="T:System.Reflection.MemberInfo" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.MemberBinding.ToString">
      <summary>返回 <see cref="T:System.Linq.Expressions.MemberBinding" /> 的文本表示形式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberBinding" /> 的文本表示形式。</returns>
    </member>
    <member name="T:System.Linq.Expressions.MemberBindingType">
      <summary>描述 <see cref="T:System.Linq.Expressions.MemberInitExpression" /> 对象中使用的绑定类型。</summary>
    </member>
    <member name="F:System.Linq.Expressions.MemberBindingType.Assignment">
      <summary>一个绑定，它表示使用表达式的值来初始化成员。</summary>
    </member>
    <member name="F:System.Linq.Expressions.MemberBindingType.ListBinding">
      <summary>一个绑定，它表示根据元素列表来初始化类型为 <see cref="T:System.Collections.IList" /> 或 <see cref="T:System.Collections.Generic.ICollection`1" /> 的成员。</summary>
    </member>
    <member name="F:System.Linq.Expressions.MemberBindingType.MemberBinding">
      <summary>一个绑定，它表示以递归方式来初始化某个成员的成员。</summary>
    </member>
    <member name="T:System.Linq.Expressions.MemberExpression">
      <summary>表示访问字段或属性。</summary>
    </member>
    <member name="M:System.Linq.Expressions.MemberExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>调度到此节点类型的特定 Visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 调用 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>对此节点进行访问的结果。</returns>
      <param name="visitor">对此节点进行访问的访问者。</param>
    </member>
    <member name="P:System.Linq.Expressions.MemberExpression.Expression">
      <summary>获取字段或属性的包含对象。</summary>
      <returns>表示字段或属性的包含对象的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MemberExpression.Member">
      <summary>获取要访问的字段或属性。</summary>
      <returns>表示要访问的字段或属性的 <see cref="T:System.Reflection.MemberInfo" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MemberExpression.NodeType">
      <summary>返回此 <see cref="P:System.Linq.Expressions.MemberExpression.Expression" /> 的节点类型。</summary>
      <returns>用于表示此表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.MemberExpression.Update(System.Linq.Expressions.Expression)">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="expression">结果的 <see cref="P:System.Linq.Expressions.MemberExpression.Expression" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.MemberInitExpression">
      <summary>表示调用构造函数并初始化新对象的一个或多个成员。</summary>
    </member>
    <member name="P:System.Linq.Expressions.MemberInitExpression.Bindings">
      <summary>获取描述如何初始化新创建对象的成员的绑定。</summary>
      <returns>描述如何初始化成员的 <see cref="T:System.Linq.Expressions.MemberBinding" /> 对象的 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MemberInitExpression.CanReduce">
      <summary>获取一个值，该值指示是否可以减小此表达式树节点。</summary>
      <returns>如果可以简化节点，则为 True；否则为 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MemberInitExpression.NewExpression">
      <summary>获取表示构造函数调用的表达式。</summary>
      <returns>表示构造函数调用的 <see cref="T:System.Linq.Expressions.NewExpression" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MemberInitExpression.NodeType">
      <summary>返回此 Expression 的节点类型。扩展节点应在重写此方法时返回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>该表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.MemberInitExpression.Reduce">
      <summary>将 <see cref="T:System.Linq.Expressions.MemberInitExpression" /> 简化为更简单的表达式。</summary>
      <returns>已简化的表达式。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MemberInitExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.MemberInitExpression.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.MemberInitExpression.Update(System.Linq.Expressions.NewExpression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.MemberBinding})">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="newExpression">结果的 <see cref="P:System.Linq.Expressions.MemberInitExpression.NewExpression" /> 属性。</param>
      <param name="bindings">结果的 <see cref="P:System.Linq.Expressions.MemberInitExpression.Bindings" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.MemberListBinding">
      <summary>表示初始化新创建对象的集合成员的元素。</summary>
    </member>
    <member name="P:System.Linq.Expressions.MemberListBinding.Initializers">
      <summary>获取用于初始化新创建对象的集合成员的元素初始值设定项。</summary>
      <returns>用来初始化集合成员的 <see cref="T:System.Linq.Expressions.ElementInit" /> 对象的 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.MemberListBinding.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.ElementInit})">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="initializers">结果的 <see cref="P:System.Linq.Expressions.MemberListBinding.Initializers" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.MemberMemberBinding">
      <summary>表示初始化新创建对象的成员的成员。</summary>
    </member>
    <member name="P:System.Linq.Expressions.MemberMemberBinding.Bindings">
      <summary>获取描述如何初始化某个成员的成员的绑定。</summary>
      <returns>描述如何初始化该成员的成员的 <see cref="T:System.Linq.Expressions.MemberBinding" /> 对象的 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.MemberMemberBinding.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.MemberBinding})">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="bindings">结果的 <see cref="P:System.Linq.Expressions.MemberMemberBinding.Bindings" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.MethodCallExpression">
      <summary>表示对静态方法或实例方法的调用。</summary>
    </member>
    <member name="M:System.Linq.Expressions.MethodCallExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>调度到此节点类型的特定 Visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 调用 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>对此节点进行访问的结果。</returns>
      <param name="visitor">对此节点进行访问的访问者。</param>
    </member>
    <member name="P:System.Linq.Expressions.MethodCallExpression.Arguments">
      <summary>获取表示已调用方法的参数的表达式集合。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" /> 对象的 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />，表示被调用方法的参数。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MethodCallExpression.Method">
      <summary>获取要调用的方法的 <see cref="T:System.Reflection.MethodInfo" />。</summary>
      <returns>表示被调用方法的 <see cref="T:System.Reflection.MethodInfo" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MethodCallExpression.NodeType">
      <summary>返回此 <see cref="T:System.Linq.Expressions.Expression" /> 的节点类型。</summary>
      <returns>用于表示此表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MethodCallExpression.Object">
      <summary>获取 <see cref="T:System.Linq.Expressions.Expression" />，对于实例方法调用，它表示实例，而对于静态方法调用，它表示 null。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.Expression" />，表示方法的接收对象。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MethodCallExpression.System#Linq#Expressions#IArgumentProvider#ArgumentCount"></member>
    <member name="M:System.Linq.Expressions.MethodCallExpression.System#Linq#Expressions#IArgumentProvider#GetArgument(System.Int32)"></member>
    <member name="P:System.Linq.Expressions.MethodCallExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.MethodCallExpression.Update(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="object">结果的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 属性。</param>
      <param name="arguments">结果的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.NewArrayExpression">
      <summary>表示创建新数组并可能初始化该新数组的元素。</summary>
    </member>
    <member name="M:System.Linq.Expressions.NewArrayExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>调度到此节点类型的特定 Visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 调用 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>对此节点进行访问的结果。</returns>
      <param name="visitor">对此节点进行访问的访问者。</param>
    </member>
    <member name="P:System.Linq.Expressions.NewArrayExpression.Expressions">
      <summary>如果 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性的值为 <see cref="F:System.Linq.Expressions.ExpressionType.NewArrayBounds" />，则获取数组的界限；如果 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 属性的值为 <see cref="F:System.Linq.Expressions.ExpressionType.NewArrayInit" />，则获取用来初始化新数组的元素的值。</summary>
      <returns>表示数组的界限或初始化值的 <see cref="T:System.Linq.Expressions.Expression" /> 对象的 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.NewArrayExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.NewArrayExpression.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.NewArrayExpression.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="expressions">结果的 <see cref="P:System.Linq.Expressions.NewArrayExpression.Expressions" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.NewExpression">
      <summary>表示构造函数调用。</summary>
    </member>
    <member name="M:System.Linq.Expressions.NewExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>调度到此节点类型的特定 Visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 调用 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>对此节点进行访问的结果。</returns>
      <param name="visitor">对此节点进行访问的访问者。</param>
    </member>
    <member name="P:System.Linq.Expressions.NewExpression.Arguments">
      <summary>获取构造函数的参数。</summary>
      <returns>表示构造函数的参数的 <see cref="T:System.Linq.Expressions.Expression" /> 对象的集合。</returns>
    </member>
    <member name="P:System.Linq.Expressions.NewExpression.Constructor">
      <summary>获取被调用的构造函数。</summary>
      <returns>表示被调用构造函数的 <see cref="T:System.Reflection.ConstructorInfo" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.NewExpression.Members">
      <summary>获取符合以下条件的成员：它们可以检索已使用构造函数参数进行初始化的字段的值。</summary>
      <returns>
        <see cref="T:System.Reflection.MemberInfo" /> 对象的集合，该对象表示可检索已使用构造函数参数进行初始化的字段值的成员。</returns>
    </member>
    <member name="P:System.Linq.Expressions.NewExpression.NodeType">
      <summary>返回此 <see cref="T:System.Linq.Expressions.Expression" /> 的节点类型。</summary>
      <returns>用于表示此表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.NewExpression.System#Linq#Expressions#IArgumentProvider#ArgumentCount"></member>
    <member name="M:System.Linq.Expressions.NewExpression.System#Linq#Expressions#IArgumentProvider#GetArgument(System.Int32)"></member>
    <member name="P:System.Linq.Expressions.NewExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.NewExpression.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.NewExpression.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="arguments">结果的 <see cref="P:System.Linq.Expressions.NewExpression.Arguments" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.ParameterExpression">
      <summary>表示命名的参数表达式。</summary>
    </member>
    <member name="M:System.Linq.Expressions.ParameterExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>调度到此节点类型的特定 Visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 调用 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>对此节点进行访问的结果。</returns>
      <param name="visitor">对此节点进行访问的访问者。</param>
    </member>
    <member name="P:System.Linq.Expressions.ParameterExpression.IsByRef">
      <summary>指示此 ParameterExpression 将被视为 ByRef 参数。</summary>
      <returns>如果此 ParameterExpression 是 ByRef 参数，则为 True；否则为 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ParameterExpression.Name">
      <summary>获取参数或变量的名称。</summary>
      <returns>一个包含参数名的 <see cref="T:System.String" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ParameterExpression.NodeType">
      <summary>返回此 <see cref="T:System.Linq.Expressions.Expression" /> 的节点类型。</summary>
      <returns>用于表示此表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ParameterExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.ParameterExpression.Type" />。</returns>
    </member>
    <member name="T:System.Linq.Expressions.RuntimeVariablesExpression">
      <summary>一个为变量提供运行时读/写权限的表达式。</summary>
    </member>
    <member name="P:System.Linq.Expressions.RuntimeVariablesExpression.NodeType">
      <summary>返回此 Expression 的节点类型。扩展节点应在重写此方法时返回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>该表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.RuntimeVariablesExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.RuntimeVariablesExpression.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.RuntimeVariablesExpression.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="variables">结果的 <see cref="P:System.Linq.Expressions.RuntimeVariablesExpression.Variables" /> 属性。</param>
    </member>
    <member name="P:System.Linq.Expressions.RuntimeVariablesExpression.Variables">
      <summary>为其提供运行时访问权限的变量或参数。</summary>
      <returns>包含将为其提供运行时访问权限的参数的只读集合。</returns>
    </member>
    <member name="T:System.Linq.Expressions.SwitchCase">
      <summary>表示 <see cref="T:System.Linq.Expressions.SwitchExpression" /> 的一个事例。</summary>
    </member>
    <member name="P:System.Linq.Expressions.SwitchCase.Body">
      <summary>获取此事例的主体。</summary>
      <returns>表示事例块主体的 <see cref="T:System.Linq.Expressions.Expression" /> 对象。</returns>
    </member>
    <member name="P:System.Linq.Expressions.SwitchCase.TestValues">
      <summary>获取此事例的值。当 <see cref="P:System.Linq.Expressions.SwitchExpression.SwitchValue" /> 与其中任一值匹配时，会选择此事例以便执行。</summary>
      <returns>此事例块的值的只读集合。</returns>
    </member>
    <member name="M:System.Linq.Expressions.SwitchCase.ToString">
      <summary>返回表示当前 <see cref="T:System.Object" /> 的 <see cref="T:System.String" />。</summary>
      <returns>
        <see cref="T:System.String" />，表示当前的 <see cref="T:System.Object" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.SwitchCase.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression},System.Linq.Expressions.Expression)">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="testValues">结果的 <see cref="P:System.Linq.Expressions.SwitchCase.TestValues" /> 属性。</param>
      <param name="body">结果的 <see cref="P:System.Linq.Expressions.SwitchCase.Body" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.SwitchExpression">
      <summary>表示一个控制表达式，该表达式通过将控制传递到 <see cref="T:System.Linq.Expressions.SwitchCase" /> 来处理多重选择。</summary>
    </member>
    <member name="P:System.Linq.Expressions.SwitchExpression.Cases">
      <summary>获取开关的 <see cref="T:System.Linq.Expressions.SwitchCase" /> 对象的集合。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.SwitchCase" /> 对象的集合。</returns>
    </member>
    <member name="P:System.Linq.Expressions.SwitchExpression.Comparison">
      <summary>获取相等比较方法（如果有）。</summary>
      <returns>表示相等比较方法的 <see cref="T:System.Reflection.MethodInfo" /> 对象。</returns>
    </member>
    <member name="P:System.Linq.Expressions.SwitchExpression.DefaultBody">
      <summary>获取开关的测试。</summary>
      <returns>表示开关的测试的 <see cref="T:System.Linq.Expressions.Expression" /> 对象。</returns>
    </member>
    <member name="P:System.Linq.Expressions.SwitchExpression.NodeType">
      <summary>返回此 Expression 的节点类型。扩展节点应在重写此方法时返回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>该表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.SwitchExpression.SwitchValue">
      <summary>获取开关的测试。</summary>
      <returns>表示开关的测试的 <see cref="T:System.Linq.Expressions.Expression" /> 对象。</returns>
    </member>
    <member name="P:System.Linq.Expressions.SwitchExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.SwitchExpression.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.SwitchExpression.Update(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.SwitchCase},System.Linq.Expressions.Expression)">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="switchValue">结果的 <see cref="P:System.Linq.Expressions.SwitchExpression.SwitchValue" /> 属性。</param>
      <param name="cases">结果的 <see cref="P:System.Linq.Expressions.SwitchExpression.Cases" /> 属性。</param>
      <param name="defaultBody">结果的 <see cref="P:System.Linq.Expressions.SwitchExpression.DefaultBody" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.SymbolDocumentInfo">
      <summary>存储发出源文件的调试符号信息所需的信息，尤其是文件名和唯一语言标识符。</summary>
    </member>
    <member name="P:System.Linq.Expressions.SymbolDocumentInfo.DocumentType">
      <summary>返回文档类型的唯一标识符（如果有）。对于文本文件，默认为 GUID。</summary>
      <returns>文档类型的唯一标识符。</returns>
    </member>
    <member name="P:System.Linq.Expressions.SymbolDocumentInfo.FileName">
      <summary>源文件名称。</summary>
      <returns>表示源文件名称的字符串。</returns>
    </member>
    <member name="P:System.Linq.Expressions.SymbolDocumentInfo.Language">
      <summary>返回语言的唯一标识符（如果有）。</summary>
      <returns>语言的唯一标识符</returns>
    </member>
    <member name="P:System.Linq.Expressions.SymbolDocumentInfo.LanguageVendor">
      <summary>返回语言供应商的唯一标识符（如果有）。</summary>
      <returns>语言供应商的唯一标识符。</returns>
    </member>
    <member name="T:System.Linq.Expressions.TryExpression">
      <summary>表示 try/catch/finally/fault 块。</summary>
    </member>
    <member name="P:System.Linq.Expressions.TryExpression.Body">
      <summary>获取表示 try 块的主体的 <see cref="T:System.Linq.Expressions.Expression" />。</summary>
      <returns>表示 try 块的主体的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.TryExpression.Fault">
      <summary>获取表示 fault 块的 <see cref="T:System.Linq.Expressions.Expression" />。</summary>
      <returns>表示 fault 块的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.TryExpression.Finally">
      <summary>获取表示 finally 块的 <see cref="T:System.Linq.Expressions.Expression" />。</summary>
      <returns>表示 finally 块的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.TryExpression.Handlers">
      <summary>获取与 try 块关联的 <see cref="T:System.Linq.Expressions.CatchBlock" /> 表达式的集合。</summary>
      <returns>与 try 块关联的 <see cref="T:System.Linq.Expressions.CatchBlock" /> 表达式的集合。</returns>
    </member>
    <member name="P:System.Linq.Expressions.TryExpression.NodeType">
      <summary>返回此 <see cref="T:System.Linq.Expressions.Expression" /> 的节点类型。</summary>
      <returns>用于表示此表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.TryExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.TryExpression.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.TryExpression.Update(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.CatchBlock},System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="body">结果的 <see cref="P:System.Linq.Expressions.TryExpression.Body" /> 属性。</param>
      <param name="handlers">结果的 <see cref="P:System.Linq.Expressions.TryExpression.Handlers" /> 属性。</param>
      <param name="finally">结果的 <see cref="P:System.Linq.Expressions.TryExpression.Finally" /> 属性。</param>
      <param name="fault">结果的 <see cref="P:System.Linq.Expressions.TryExpression.Fault" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.TypeBinaryExpression">
      <summary>表示表达式和类型之间的操作。</summary>
    </member>
    <member name="P:System.Linq.Expressions.TypeBinaryExpression.Expression">
      <summary>获取类型测试操作的表达式操作数。</summary>
      <returns>表示类型测试操作的表达式操作数的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.TypeBinaryExpression.NodeType">
      <summary>返回此 Expression 的节点类型。扩展节点应在重写此方法时返回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>该表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.TypeBinaryExpression.Type">
      <summary>获取此 <see cref="P:System.Linq.Expressions.TypeBinaryExpression.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.TypeBinaryExpression.Type" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.TypeBinaryExpression.TypeOperand">
      <summary>获取类型测试操作的类型操作数。</summary>
      <returns>表示类型测试操作的类型操作数的 <see cref="T:System.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.TypeBinaryExpression.Update(System.Linq.Expressions.Expression)">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="expression">结果的 <see cref="P:System.Linq.Expressions.TypeBinaryExpression.Expression" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.UnaryExpression">
      <summary>表示包含一元运算符的表达式。</summary>
    </member>
    <member name="P:System.Linq.Expressions.UnaryExpression.CanReduce">
      <summary>获取一个值，该值指示是否可以减小此表达式树节点。</summary>
      <returns>如果可以简化节点，则为 True；否则为 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.UnaryExpression.IsLifted">
      <summary>获取一个值，该值指示表达式目录树节点是否表示对运算符的提升调用。</summary>
      <returns>如果该节点表示提升调用，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.UnaryExpression.IsLiftedToNull">
      <summary>获取一个值，该值指示表达式目录树节点是否表示对运算符（其返回类型提升为可以为 null 的类型）的提升调用。</summary>
      <returns>如果该运算符的返回类型提升为可以为 null 的类型，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.UnaryExpression.Method">
      <summary>获取一元运算的实现方法。</summary>
      <returns>表示实现方法的 <see cref="T:System.Reflection.MethodInfo" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.UnaryExpression.NodeType">
      <summary>返回此 <see cref="T:System.Linq.Expressions.Expression" /> 的节点类型。</summary>
      <returns>用于表示此表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.UnaryExpression.Operand">
      <summary>获取一元运算的操作数。</summary>
      <returns>一个表示一元运算的操作数的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.UnaryExpression.Reduce">
      <summary>将表达式节点简化为更简单的表达式。</summary>
      <returns>已简化的表达式。</returns>
    </member>
    <member name="P:System.Linq.Expressions.UnaryExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.UnaryExpression.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.UnaryExpression.Update(System.Linq.Expressions.Expression)">
      <summary>创建一个与此表达式类似的新表达式，但使用所提供的子级。如果所有子级都相同，则将返回此表达式。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="operand">结果的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 属性。</param>
    </member>
  </members>
</doc>