﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Linq.Expressions</name>
  </assembly>
  <members>
    <member name="T:System.Linq.IOrderedQueryable">
      <summary>表示排序作業的結果。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Linq.IOrderedQueryable`1">
      <summary>表示排序作業的結果。</summary>
      <typeparam name="T">資料來源的內容型別。這個類型參數是 Covariant。換言之，您可以使用所指定的類型或是衍生程度較大的任一類型。如需共變數與反變數的詳細資訊，請參閱泛型中的共變數和反變數。</typeparam>
    </member>
    <member name="T:System.Linq.IQueryable">
      <summary>提供功能，對未指定資料型別的特定資料來源評估查詢。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Linq.IQueryable.ElementType">
      <summary>取得與這個 <see cref="T:System.Linq.IQueryable" /> 執行個體關聯的運算式樹狀架構執行時，所傳回項目的型別。</summary>
      <returns>
        <see cref="T:System.Type" />，表示與這個物件關聯的運算式樹狀架構執行時，所傳回項目的型別。</returns>
    </member>
    <member name="P:System.Linq.IQueryable.Expression">
      <summary>取得與 <see cref="T:System.Linq.IQueryable" /> 之執行個體關聯的運算式樹狀架構。</summary>
      <returns>與這個 <see cref="T:System.Linq.IQueryable" /> 之執行個體相關聯的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="P:System.Linq.IQueryable.Provider">
      <summary>取得與這個資料來源關聯的查詢提供者。</summary>
      <returns>與這個資料來源關聯的 <see cref="T:System.Linq.IQueryProvider" />。</returns>
    </member>
    <member name="T:System.Linq.IQueryable`1">
      <summary>提供功能，對已知資料型別的特定資料來源評估查詢。</summary>
      <typeparam name="T">資料來源中的資料型別。這個類型參數是 Covariant。換言之，您可以使用所指定的類型或是衍生程度較大的任一類型。如需共變數與反變數的詳細資訊，請參閱泛型中的共變數和反變數。</typeparam>
    </member>
    <member name="T:System.Linq.IQueryProvider">
      <summary>定義方法來建立並執行 <see cref="T:System.Linq.IQueryable" /> 物件所描述的查詢。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Linq.IQueryProvider.CreateQuery``1(System.Linq.Expressions.Expression)">
      <summary>建構 <see cref="T:System.Linq.IQueryable`1" /> 物件，這個物件可評估由指定的運算式樹狀架構所表示的查詢。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，可評估由指定的運算式樹狀架構所表示的查詢。</returns>
      <param name="expression">代表 LINQ 查詢的運算式樹狀架構。</param>
      <typeparam name="TElement">所傳回 <see cref="T:System.Linq.IQueryable`1" /> 之項目的型別。</typeparam>
    </member>
    <member name="M:System.Linq.IQueryProvider.CreateQuery(System.Linq.Expressions.Expression)">
      <summary>建構 <see cref="T:System.Linq.IQueryable" /> 物件，這個物件可評估由指定的運算式樹狀架構所表示的查詢。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable" />，可評估由指定的運算式樹狀架構所表示的查詢。</returns>
      <param name="expression">代表 LINQ 查詢的運算式樹狀架構。</param>
    </member>
    <member name="M:System.Linq.IQueryProvider.Execute``1(System.Linq.Expressions.Expression)">
      <summary>執行由指定之運算式樹狀架構所代表的強型別查詢。</summary>
      <returns>執行指定的查詢所產生的值。</returns>
      <param name="expression">代表 LINQ 查詢的運算式樹狀架構。</param>
      <typeparam name="TResult">執行查詢所產生值的型別。</typeparam>
    </member>
    <member name="M:System.Linq.IQueryProvider.Execute(System.Linq.Expressions.Expression)">
      <summary>執行指定之運算式樹狀架構所代表的查詢。</summary>
      <returns>執行指定的查詢所產生的值。</returns>
      <param name="expression">代表 LINQ 查詢的運算式樹狀架構。</param>
    </member>
    <member name="T:System.Linq.Expressions.BinaryExpression">
      <summary>表示有二元運算子的運算式。</summary>
    </member>
    <member name="M:System.Linq.Expressions.BinaryExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>分派給這個節點類型的特定 visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 會呼叫 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>瀏覽這個節點的結果。</returns>
      <param name="visitor">瀏覽這個節點的造訪者。</param>
    </member>
    <member name="P:System.Linq.Expressions.BinaryExpression.CanReduce">
      <summary>取得值，指出是否可以減少運算式樹狀架構的節點。</summary>
      <returns>如果運算式樹狀架構節點可以精簡則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.BinaryExpression.Conversion">
      <summary>取得聯合或複合指派運算所使用的型別轉換函式。</summary>
      <returns>表示型別轉換函式的 <see cref="T:System.Linq.Expressions.LambdaExpression" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.BinaryExpression.IsLifted">
      <summary>取得值，這個值指出運算式樹狀節點是否表示對運算子的「消除」(Lifted) 呼叫。</summary>
      <returns>如果節點表示消除呼叫，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull">
      <summary>取得值，這個值指出運算式樹狀節點是否表示對運算子的「消除」呼叫，該運算子的傳回型別對可為 null 的型別已消除。</summary>
      <returns>如果運算子的傳回型別對可為 null 的型別消除，則為 true，否則為false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.BinaryExpression.Left">
      <summary>取得二元作業的左運算元。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" />，表示二元運算的左運算元。</returns>
    </member>
    <member name="P:System.Linq.Expressions.BinaryExpression.Method">
      <summary>取得二元作業的實作方法。</summary>
      <returns>
        <see cref="T:System.Reflection.MethodInfo" />，表示實作的方法。</returns>
    </member>
    <member name="M:System.Linq.Expressions.BinaryExpression.Reduce">
      <summary>將二進位運算式節點精簡為更簡單的運算式。</summary>
      <returns>精簡的運算式。</returns>
    </member>
    <member name="P:System.Linq.Expressions.BinaryExpression.Right">
      <summary>取得二元作業的右運算元。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" />，表示二元運算的右運算元。</returns>
    </member>
    <member name="M:System.Linq.Expressions.BinaryExpression.Update(System.Linq.Expressions.Expression,System.Linq.Expressions.LambdaExpression,System.Linq.Expressions.Expression)">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="left">結果的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 屬性。</param>
      <param name="conversion">結果的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 屬性。</param>
      <param name="right">結果的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.BlockExpression">
      <summary>表示區塊，其中包含可定義變數的運算式序列。</summary>
    </member>
    <member name="M:System.Linq.Expressions.BlockExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>分派給這個節點類型的特定 visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 會呼叫 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>瀏覽這個節點的結果。</returns>
      <param name="visitor">瀏覽這個節點的造訪者。</param>
    </member>
    <member name="P:System.Linq.Expressions.BlockExpression.Expressions">
      <summary>取得這個區塊中的運算式。</summary>
      <returns>唯讀集合，包含這個區塊中的所有運算式。</returns>
    </member>
    <member name="P:System.Linq.Expressions.BlockExpression.NodeType">
      <summary>傳回這個運算式的節點型別。延伸節點覆寫這個方法時應該傳回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.BlockExpression.Result">
      <summary>取得這個區塊中的最後一個運算式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" /> 物件，表示這個區塊中的最後一個運算式。</returns>
    </member>
    <member name="P:System.Linq.Expressions.BlockExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.BlockExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="M:System.Linq.Expressions.BlockExpression.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression},System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系則為這個運算式，否則為具有更新之子系的運算式。</returns>
      <param name="variables">結果的 <see cref="P:System.Linq.Expressions.BlockExpression.Variables" /> 屬性。</param>
      <param name="expressions">結果的 <see cref="P:System.Linq.Expressions.BlockExpression.Expressions" /> 屬性。</param>
    </member>
    <member name="P:System.Linq.Expressions.BlockExpression.Variables">
      <summary>取得這個區塊中定義的變數。</summary>
      <returns>唯讀集合，包含這個區塊中定義的所有變數。</returns>
    </member>
    <member name="T:System.Linq.Expressions.CatchBlock">
      <summary>表示 try 區塊中的 catch 陳述式。</summary>
    </member>
    <member name="P:System.Linq.Expressions.CatchBlock.Body">
      <summary>取得 catch 區塊的主體。</summary>
      <returns>表示 catch 主體的 <see cref="T:System.Linq.Expressions.Expression" /> 物件。</returns>
    </member>
    <member name="P:System.Linq.Expressions.CatchBlock.Filter">
      <summary>取得 <see cref="T:System.Linq.Expressions.CatchBlock" /> 篩選條件的主體。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" /> 物件，表示 <see cref="T:System.Linq.Expressions.CatchBlock" /> 篩選條件的主體。</returns>
    </member>
    <member name="P:System.Linq.Expressions.CatchBlock.Test">
      <summary>取得這個處理常式所攔截 <see cref="T:System.Exception" /> 的型別。</summary>
      <returns>
        <see cref="T:System.Type" /> 物件，表示這個處理常式所攔截 <see cref="T:System.Exception" /> 的型別。</returns>
    </member>
    <member name="M:System.Linq.Expressions.CatchBlock.ToString">
      <summary>傳回 <see cref="T:System.String" />，表示目前的 <see cref="T:System.Object" />。</summary>
      <returns>
        <see cref="T:System.String" />，表示目前的 <see cref="T:System.Object" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.CatchBlock.Update(System.Linq.Expressions.ParameterExpression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="variable">結果的 <see cref="P:System.Linq.Expressions.CatchBlock.Variable" /> 屬性。</param>
      <param name="filter">結果的 <see cref="P:System.Linq.Expressions.CatchBlock.Filter" /> 屬性。</param>
      <param name="body">結果的 <see cref="P:System.Linq.Expressions.CatchBlock.Body" /> 屬性。</param>
    </member>
    <member name="P:System.Linq.Expressions.CatchBlock.Variable">
      <summary>取得這個處理常式所攔截 <see cref="T:System.Exception" /> 物件的參考。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ParameterExpression" /> 物件，表示這個處理常式所攔截 <see cref="T:System.Exception" /> 物件的參考。</returns>
    </member>
    <member name="T:System.Linq.Expressions.ConditionalExpression">
      <summary>表示有條件式運算子的運算式。</summary>
    </member>
    <member name="M:System.Linq.Expressions.ConditionalExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>分派給這個節點類型的特定 visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 會呼叫 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>瀏覽這個節點的結果。</returns>
      <param name="visitor">瀏覽這個節點的造訪者。</param>
    </member>
    <member name="P:System.Linq.Expressions.ConditionalExpression.IfFalse">
      <summary>如果測試評估為 false，則取得運算式來執行。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" />，表示要執行的運算式 (如果測試為 false)。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ConditionalExpression.IfTrue">
      <summary>如果測試評估為 true，則取得運算式來執行。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" />，表示要執行的運算式 (如果測試為 true)。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ConditionalExpression.NodeType">
      <summary>傳回這個運算式的節點型別。延伸節點覆寫這個方法時應該傳回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ConditionalExpression.Test">
      <summary>取得條件運算的測試。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" />，表示條件運算的測試。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ConditionalExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.ConditionalExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="M:System.Linq.Expressions.ConditionalExpression.Update(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，則會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系則為這個運算式，否則為具有更新之子系的運算式。</returns>
      <param name="test">結果的 <see cref="P:System.Linq.Expressions.ConditionalExpression.Test" /> 屬性。</param>
      <param name="ifTrue">結果的 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfTrue" /> 屬性。</param>
      <param name="ifFalse">結果的 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfFalse" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.ConstantExpression">
      <summary>表示有常數值的運算式。</summary>
    </member>
    <member name="M:System.Linq.Expressions.ConstantExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>分派給這個節點類型的特定 visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 會呼叫 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>瀏覽這個節點的結果。</returns>
      <param name="visitor">瀏覽這個節點的造訪者。</param>
    </member>
    <member name="P:System.Linq.Expressions.ConstantExpression.NodeType">
      <summary>傳回這個 Expression 的節點型別。延伸節點覆寫這個方法時應該傳回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ConstantExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.ConstantExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ConstantExpression.Value">
      <summary>取得常數運算式的值。</summary>
      <returns>
        <see cref="T:System.Object" /> 等於表示之運算式的值。</returns>
    </member>
    <member name="T:System.Linq.Expressions.DebugInfoExpression">
      <summary>發出或清除偵錯資訊的序列點。這可讓偵錯工具在偵錯時反白顯示正確的原始程式碼。</summary>
    </member>
    <member name="M:System.Linq.Expressions.DebugInfoExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>分派給這個節點類型的特定 visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 會呼叫 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>瀏覽這個節點的結果。</returns>
      <param name="visitor">瀏覽這個節點的造訪者。</param>
    </member>
    <member name="P:System.Linq.Expressions.DebugInfoExpression.Document">
      <summary>取得表示原始程式檔的 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" />。</summary>
      <returns>表示原始程式檔的 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.DebugInfoExpression.EndColumn">
      <summary>取得這個 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的結尾欄。</summary>
      <returns>用來產生包裝運算式的程式碼結尾欄號。</returns>
    </member>
    <member name="P:System.Linq.Expressions.DebugInfoExpression.EndLine">
      <summary>取得這個 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的結尾行。</summary>
      <returns>用來產生包裝運算式的程式碼結尾行號。</returns>
    </member>
    <member name="P:System.Linq.Expressions.DebugInfoExpression.IsClear">
      <summary>取得值，這個值表示 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 是否用來清除序列點。</summary>
      <returns>如果 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 是用來清除序列點則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.DebugInfoExpression.NodeType">
      <summary>傳回這個 <see cref="T:System.Linq.Expressions.Expression" /> 的節點型別。</summary>
      <returns>表示這個運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.DebugInfoExpression.StartColumn">
      <summary>取得這個 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的起始欄。</summary>
      <returns>用來產生包裝運算式的程式碼起始欄號。</returns>
    </member>
    <member name="P:System.Linq.Expressions.DebugInfoExpression.StartLine">
      <summary>取得這個 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的起始行。</summary>
      <returns>用來產生包裝運算式的程式碼起始行號。</returns>
    </member>
    <member name="P:System.Linq.Expressions.DebugInfoExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.DebugInfoExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="T:System.Linq.Expressions.DefaultExpression">
      <summary>表示型別的預設值或空運算式。</summary>
    </member>
    <member name="P:System.Linq.Expressions.DefaultExpression.NodeType">
      <summary>傳回這個運算式的節點型別。延伸節點覆寫這個方法時應該傳回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.DefaultExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.DefaultExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="T:System.Linq.Expressions.ElementInit">
      <summary>表示 <see cref="T:System.Collections.IEnumerable" /> 集合中單一項目的初始設定式。</summary>
    </member>
    <member name="P:System.Linq.Expressions.ElementInit.AddMethod">
      <summary>取得可用來將項目加入至 <see cref="T:System.Collections.IEnumerable" /> 集合的執行個體方法。</summary>
      <returns>
        <see cref="T:System.Reflection.MethodInfo" />，表示會將項目加入至集合的執行個體方法。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ElementInit.Arguments">
      <summary>取得引數集合，這些引數會傳遞到將項目加入至 <see cref="T:System.Collections.IEnumerable" /> 集合的方法。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" /> 物件的 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />，表示將項目加入至集合之方法的引數。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ElementInit.System#Linq#Expressions#IArgumentProvider#ArgumentCount"></member>
    <member name="M:System.Linq.Expressions.ElementInit.System#Linq#Expressions#IArgumentProvider#GetArgument(System.Int32)"></member>
    <member name="M:System.Linq.Expressions.ElementInit.ToString">
      <summary>傳回 <see cref="T:System.Linq.Expressions.ElementInit" /> 物件的文字表示。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ElementInit" /> 物件的文字表示。</returns>
    </member>
    <member name="M:System.Linq.Expressions.ElementInit.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="arguments">結果的 <see cref="P:System.Linq.Expressions.ElementInit.Arguments" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.Expression">
      <summary>提供基底類別，表示運算式樹狀架構節點的類別可由此基底類別衍生。它也包含 static (在 Visual Basic 中為 Shared) Factory 方法來建立各種節點類型。這是 abstract 類別。</summary>
    </member>
    <member name="M:System.Linq.Expressions.Expression.#ctor">
      <summary>建構 <see cref="T:System.Linq.Expressions.Expression" /> 的新執行個體。</summary>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>分派給這個節點類型的特定造訪方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 會呼叫 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>瀏覽這個節點的結果。</returns>
      <param name="visitor">瀏覽這個節點的造訪者。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Add(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其代表不含溢位檢查的算術加法運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Add" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義 Addition 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Add(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其代表不含溢位檢查的算術加法運算。實作的方法可加以指定。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Add" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義 Addition 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AddAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示不含溢位檢查的加法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.AddAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AddAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示不含溢位檢查的加法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.AddAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AddAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示不含溢位檢查的加法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.AddAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要將 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AddAssignChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示包含溢位檢查的加法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.AddAssignChecked" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AddAssignChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示包含溢位檢查的加法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.AddAssignChecked" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AddAssignChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示包含溢位檢查的加法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.AddAssignChecked" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要將 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AddChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其代表包含溢位檢查的算術加法運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.AddChecked" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義 Addition 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AddChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其代表包含溢位檢查的算術加法運算。實作的方法可加以指定。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.AddChecked" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義 Addition 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.And(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立代表位元 <see cref="T:System.Linq.Expressions.BinaryExpression" /> 運算的 AND。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.And" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 AND.Type 和 <paramref name="left" />.Type 定義位元 <paramref name="right" /> 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.And(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立表示位元 <see cref="T:System.Linq.Expressions.BinaryExpression" /> 運算的 AND。實作的方法可加以指定。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.And" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 AND.Type 和 <paramref name="left" />.Type 定義位元 <paramref name="right" /> 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AndAlso(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示只在第一個運算元計算結果為 AND 時才求第二個運算元之值的 true 條件運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.AndAlso" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 AND.Type 和 <paramref name="left" />.Type 定義位元 <paramref name="right" /> 運算子。-或-<paramref name="left" />.Type 和 <paramref name="right" />.Type 是不相同的布林類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AndAlso(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，代表只在第一個運算元解析為 true 時才評估第二個運算元的條件 AND 運算。實作的方法可加以指定。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.AndAlso" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 AND.Type 和 <paramref name="left" />.Type 定義位元 <paramref name="right" /> 運算子。-或-<paramref name="method" /> 為 null，且 <paramref name="left" />.Type 和 <paramref name="right" />.Type 不是相同的布林類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AndAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立表示位元 AND 指派運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.AndAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AndAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立表示位元 AND 指派運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.AndAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.AndAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>建立表示位元 AND 指派運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.AndAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要將 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ArrayAccess(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立 <see cref="T:System.Linq.Expressions.IndexExpression" /> 以存取多維陣列。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.IndexExpression" />。</returns>
      <param name="array">表示多維陣列的運算式。</param>
      <param name="indexes">包含用於對陣列進行索引之運算式的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ArrayAccess(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.IndexExpression" /> 以存取陣列。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.IndexExpression" />。</returns>
      <param name="array">表示要編制索引之陣列的運算式。</param>
      <param name="indexes">包含用於對陣列進行索引之運算式的陣列。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ArrayIndex(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，代表將陣列索引運算子套用到陣序規範大於 1 的陣列。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，且 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 屬性設定為指定的值。</returns>
      <param name="array">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" />。</param>
      <param name="indexes">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 或 <paramref name="indexes" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />.Type 不代表陣列類型。-或-<paramref name="array" />.Type 的陣序規範不符合 <paramref name="indexes" /> 中的項目數。-或-<see cref="P:System.Linq.Expressions.Expression.Type" /> 之一個或多個項目的 <paramref name="indexes" /> 屬性不代表 <see cref="T:System.Int32" /> 類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ArrayIndex(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，代表將陣列索引運算子套用到陣序規範 1 的陣列。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.ArrayIndex" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="array">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="index">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 或 <paramref name="index" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />.Type 不代表陣列類型。-或-<paramref name="array" />.Type 代表其陣序規範不為 1 的陣列類型。-或-<paramref name="index" />.Type 不代表 <see cref="T:System.Int32" /> 類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ArrayIndex(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，代表將陣列索引運算子套用到多維陣列。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，且 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 屬性設定為指定的值。</returns>
      <param name="array">
        <see cref="T:System.Linq.Expressions.Expression" /> 執行個體 (用於陣列索引運算的索引) 的陣列。</param>
      <param name="indexes">用以填入 <see cref="T:System.Linq.Expressions.Expression" /> 集合之 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 物件的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 或 <paramref name="indexes" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />.Type 不代表陣列類型。-或-<paramref name="array" />.Type 的陣序規範不符合 <paramref name="indexes" /> 中的項目數。-或-<see cref="P:System.Linq.Expressions.Expression.Type" /> 之一個或多個項目的 <paramref name="indexes" /> 屬性不代表 <see cref="T:System.Int32" /> 類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ArrayLength(System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，代表用於取得一維陣列長度的運算式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.ArrayLength" />，且 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 屬性等於 <paramref name="array" />。</returns>
      <param name="array">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />.Type 不代表陣列類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Assign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立代表指派運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Assign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Bind(System.Reflection.MemberInfo,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.MemberAssignment" />，代表初始化欄位或屬性。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberAssignment" />，其 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 等於 <see cref="F:System.Linq.Expressions.MemberBindingType.Assignment" />，且 <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 和 <see cref="P:System.Linq.Expressions.MemberAssignment.Expression" /> 屬性設定為指定的值。</returns>
      <param name="member">要將 <see cref="T:System.Reflection.MemberInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MemberBinding.Member" />。</param>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MemberAssignment.Expression" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="member" /> 或 <paramref name="expression" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="member" /> 不代表欄位或屬性。-或-<paramref name="member" /> 所表示的屬性沒有 set 存取子。-或-<paramref name="expression" />.Type 無法指派給 <paramref name="member" /> 所表示之欄位或屬性的類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Bind(System.Reflection.MethodInfo,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.MemberAssignment" />，代表使用屬性存取子方法初始化成員。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberAssignment" />，其 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 屬性等於 <see cref="F:System.Linq.Expressions.MemberBindingType.Assignment" />，並將 <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 屬性設定為 <see cref="T:System.Reflection.PropertyInfo" />，代表在 <paramref name="propertyAccessor" /> 中存取的屬性，以及將 <see cref="P:System.Linq.Expressions.MemberAssignment.Expression" /> 屬性設定為 <paramref name="expression" />。</returns>
      <param name="propertyAccessor">代表屬性存取子方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MemberAssignment.Expression" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyAccessor" /> 或 <paramref name="expression" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="propertyAccessor" /> 不代表屬性存取子 (Accessor) 方法。-或-由 <paramref name="propertyAccessor" /> 存取的屬性沒有 set 存取子。-或-<paramref name="expression" />.Type 無法指派給 <paramref name="member" /> 所表示之欄位或屬性的類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立包含指定的運算式但沒有任何變數的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="expressions">區塊中的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression},System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立包含指定之變數和運算式的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="variables">區塊中的變數。</param>
      <param name="expressions">區塊中的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression},System.Linq.Expressions.Expression[])">
      <summary>建立包含指定之變數和運算式的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="variables">區塊中的變數。</param>
      <param name="expressions">區塊中的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立包含兩個運算式但沒有任何變數的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="arg0">區塊中的第一個運算式。</param>
      <param name="arg1">區塊中的第二個運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立包含三個運算式但沒有任何變數的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="arg0">區塊中的第一個運算式。</param>
      <param name="arg1">區塊中的第二個運算式。</param>
      <param name="arg2">區塊中的第三個運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立包含四個運算式但沒有任何變數的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="arg0">區塊中的第一個運算式。</param>
      <param name="arg1">區塊中的第二個運算式。</param>
      <param name="arg2">區塊中的第三個運算式。</param>
      <param name="arg3">區塊中的第四個運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立包含五個運算式但沒有任何變數的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="arg0">區塊中的第一個運算式。</param>
      <param name="arg1">區塊中的第二個運算式。</param>
      <param name="arg2">區塊中的第三個運算式。</param>
      <param name="arg3">區塊中的第四個運算式。</param>
      <param name="arg4">區塊中的第五個運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Linq.Expressions.Expression[])">
      <summary>建立包含指定的運算式但沒有任何變數的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="expressions">區塊中的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Type,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立包含指定的運算式、有指定之結果類型、但沒有任何變數的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="type">區塊的結果類型。</param>
      <param name="expressions">區塊中的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Type,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression},System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立包含指定之變數和運算式的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="type">區塊的結果類型。</param>
      <param name="variables">區塊中的變數。</param>
      <param name="expressions">區塊中的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Type,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression},System.Linq.Expressions.Expression[])">
      <summary>建立包含指定之變數和運算式的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="type">區塊的結果類型。</param>
      <param name="variables">區塊中的變數。</param>
      <param name="expressions">區塊中的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Block(System.Type,System.Linq.Expressions.Expression[])">
      <summary>建立包含指定的運算式、有指定之結果類型、但沒有任何變數的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.BlockExpression" />。</returns>
      <param name="type">區塊的結果類型。</param>
      <param name="expressions">區塊中的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Break(System.Linq.Expressions.LabelTarget)">
      <summary>建立表示 break 陳述式的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等於 Break、<see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 屬性設定為 <paramref name="target" />，且包含在跳躍時傳遞至目標標籤的 Null 值。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 將跳躍的目標 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Break(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression)">
      <summary>建立表示 break 陳述式的 <see cref="T:System.Linq.Expressions.GotoExpression" />。可以指定跳躍時傳遞至標籤的值。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等於 Break、<see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 屬性設定為 <paramref name="target" />，且，且包含在跳躍時傳遞至目標標籤的 <paramref name="value" />。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 將跳躍的目標 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="value">將在跳躍時傳遞至關聯標籤的值。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Break(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression,System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.GotoExpression" />，表示含指定之類型的 break 陳述式。可以指定跳躍時傳遞至標籤的值。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等於 Break、<see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 屬性設定為 <paramref name="target" />、<see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性設定為 <paramref name="type" />，且包含在跳躍時傳遞至目標標籤的 <paramref name="value" />。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 將跳躍的目標 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="value">將在跳躍時傳遞至關聯標籤的值。</param>
      <param name="type">要將 <see cref="T:System.Type" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Break(System.Linq.Expressions.LabelTarget,System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.GotoExpression" />，表示含指定之類型的 break 陳述式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等於 Break、<see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 屬性設定為 <paramref name="target" />，且 <see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性設定為 <paramref name="type" />。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 將跳躍的目標 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="type">要將 <see cref="T:System.Type" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立代表呼叫不採用任何引數之方法的 <see cref="T:System.Linq.Expressions.MethodCallExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，且 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="instance">
        <see cref="T:System.Linq.Expressions.Expression" />，指定執行個體方法呼叫的執行個體 (對 null (在 Visual Basic 中為 static) 方法會傳遞 Shared)。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="method" /> 為 null。-或-<paramref name="instance" /> 為 null，而且 <paramref name="method" /> 代表執行個體方法 (Instance Method)。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="instance" />.Type 無法指派給 <paramref name="method" /> 所代表之方法的宣告類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立代表呼叫至採用引數之方法的 <see cref="T:System.Linq.Expressions.MethodCallExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，且 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" />、<see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 屬性設定為指定的值。</returns>
      <param name="instance">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> (針對 null (在 Visual Basic 中為 static) 方法傳遞 Shared)。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" />。</param>
      <param name="arguments">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="method" /> 為 null。-或-<paramref name="instance" /> 為 null，而且 <paramref name="method" /> 代表執行個體方法 (Instance Method)。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="instance" />.Type 無法指派給 <paramref name="method" /> 所代表之方法的宣告類型。-或-<paramref name="arguments" /> 中的項目數與 <paramref name="method" /> 所代表之方法的參數數目不相等。-或-<paramref name="arguments" /> 的一個或多個項目未指派給 <paramref name="method" /> 所代表之方法的對應參數。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，代表呼叫採用兩個引數的方法。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，且 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="instance">為執行個體呼叫指定執行個體的 <see cref="T:System.Linq.Expressions.Expression" />。(對靜態方法 (在 Visual Basic 中為共用方法) 傳遞 Null)。</param>
      <param name="method">表示目標方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
      <param name="arg0">表示第一個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg1">表示第二個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，代表呼叫採用三個引數的方法。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，且 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="instance">為執行個體呼叫指定執行個體的 <see cref="T:System.Linq.Expressions.Expression" />。(對靜態方法 (在 Visual Basic 中為共用方法) 傳遞 Null)。</param>
      <param name="method">表示目標方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
      <param name="arg0">表示第一個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg1">表示第二個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg2">表示第三個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.Expression[])">
      <summary>建立代表呼叫至採用引數之方法的 <see cref="T:System.Linq.Expressions.MethodCallExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，且 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" />、<see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 屬性設定為指定的值。</returns>
      <param name="instance">
        <see cref="T:System.Linq.Expressions.Expression" />，指定執行個體方法呼叫的執行個體 (對 null (在 Visual Basic 中為 static) 方法會傳遞 Shared)。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" />。</param>
      <param name="arguments">用以填入 <see cref="T:System.Linq.Expressions.Expression" /> 集合之 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 物件的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="method" /> 為 null。-或-<paramref name="instance" /> 為 null，而且 <paramref name="method" /> 代表執行個體方法 (Instance Method)。-或-<paramref name="arguments" /> 不是 null，且其一個或多個其項目為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="instance" />.Type 無法指派給 <paramref name="method" /> 所代表之方法的宣告類型。-或-<paramref name="arguments" /> 中的項目數與 <paramref name="method" /> 所代表之方法的參數數目不相等。-或-<paramref name="arguments" /> 的一個或多個項目未指派給 <paramref name="method" /> 所代表之方法的對應參數。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Linq.Expressions.Expression,System.String,System.Type[],System.Linq.Expressions.Expression[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，代表透過呼叫適當的 Factory 方法來呼叫方法。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />、<see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 屬性等於 <paramref name="instance" />、<see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 設定為代表指定執行個體方法的 <see cref="T:System.Reflection.MethodInfo" />，且 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 設定為指定的引數。</returns>
      <param name="instance">
        <see cref="T:System.Linq.Expressions.Expression" />，將搜尋其 <see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性值中的特定方法。</param>
      <param name="methodName">方法的名稱。</param>
      <param name="typeArguments">
        <see cref="T:System.Type" /> 物件的陣列，這些物件可指定泛型方法的類型參數。當 methodName 指定非泛型方法時，這個引數應該為 Null。</param>
      <param name="arguments">
        <see cref="T:System.Linq.Expressions.Expression" /> 物件的陣列，這個陣列代表方法的引數。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> 或 <paramref name="methodName" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="methodName" />.Type 或其基底類型中找不到名稱為 <paramref name="typeArguments" />、其類型參數符合 <paramref name="arguments" />，且參數類型符合 <paramref name="instance" /> 的方法。-或-<paramref name="methodName" />.Type 或其基底類型中找到多個名稱為 <paramref name="typeArguments" />、類型參數符合 <paramref name="arguments" />，且參數類型符合 <paramref name="instance" /> 的方法。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Reflection.MethodInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，代表呼叫靜態方法 (在 Visual Basic 中為共用方法)。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，且 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="method">表示目標方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
      <param name="arguments">
        <see cref="T:System.Linq.Expressions.Expression" /> 的集合，代表呼叫引數。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Reflection.MethodInfo,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，代表呼叫採用一個引數的 static (在 Visual Basic 中則為 Shared) 方法。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，且 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" />。</param>
      <param name="arg0">表示第一個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="method" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Reflection.MethodInfo,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，代表呼叫採用兩個引數的靜態方法。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，且 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" />。</param>
      <param name="arg0">表示第一個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg1">表示第二個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="method" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Reflection.MethodInfo,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，代表呼叫採用三個引數的靜態方法。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，且 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" />。</param>
      <param name="arg0">表示第一個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg1">表示第二個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg2">表示第三個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="method" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Reflection.MethodInfo,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，代表呼叫採用四個引數的靜態方法。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，且 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" />。</param>
      <param name="arg0">表示第一個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg1">表示第二個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg2">表示第三個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg3">表示第四個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="method" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Reflection.MethodInfo,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，代表呼叫採用五個引數的靜態方法。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，且 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" />。</param>
      <param name="arg0">表示第一個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg1">表示第二個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg2">表示第三個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg3">表示第四個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="arg4">表示第五個引數的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="method" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Reflection.MethodInfo,System.Linq.Expressions.Expression[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，代表呼叫有引數的 static (在 Visual Basic 中則為 Shared) 方法。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />，且 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 屬性設定為指定的值。</returns>
      <param name="method">
        <see cref="T:System.Reflection.MethodInfo" />，代表 static (在 Visual Basic 中則為 Shared) 方法，將 <see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 屬性設定為與之相等。</param>
      <param name="arguments">用以填入 <see cref="T:System.Linq.Expressions.Expression" /> 集合之 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 物件的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="method" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arguments" /> 中的項目數與 <paramref name="method" /> 所代表之方法的參數數目不相等。-或-<paramref name="arguments" /> 的一個或多個項目未指派給 <paramref name="method" /> 所代表之方法的對應參數。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Call(System.Type,System.String,System.Type[],System.Linq.Expressions.Expression[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.MethodCallExpression" />，代表透過呼叫適當的 Factory 方法來呼叫 static (在 Visual Basic 中則為 Shared) 方法。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MethodCallExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Call" />、<see cref="P:System.Linq.Expressions.MethodCallExpression.Method" /> 屬性設定為 <see cref="T:System.Reflection.MethodInfo" />，代表指定的 static (在 Visual Basic 中則為 Shared) 方法，且 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 屬性設定為指定的引數。</returns>
      <param name="type">
        <see cref="T:System.Type" />，指定包含所指定 static (在 Visual Basic 中則為 Shared) 方法的類型。</param>
      <param name="methodName">方法的名稱。</param>
      <param name="typeArguments">
        <see cref="T:System.Type" /> 物件的陣列，這些物件可指定泛型方法的類型參數。當 methodName 指定非泛型方法時，這個引數應該為 Null。</param>
      <param name="arguments">
        <see cref="T:System.Linq.Expressions.Expression" /> 物件的陣列，這個陣列代表方法的引數。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> 或 <paramref name="methodName" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="methodName" /> 或其基底類型中找不到名稱為 <paramref name="typeArguments" />、類型參數符合 <paramref name="arguments" />，且參數類型符合 <paramref name="type" /> 的方法。-或-<paramref name="methodName" /> 或其基底類型中找到多個名稱為 <paramref name="typeArguments" />、類型參數符合 <paramref name="arguments" />，且參數類型符合 <paramref name="type" /> 的方法。</exception>
    </member>
    <member name="P:System.Linq.Expressions.Expression.CanReduce">
      <summary>表示節點可精簡為更簡單的節點。如果傳回 true，則可呼叫 Reduce() 以產生精簡的形式。</summary>
      <returns>如果節點可以精簡則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Catch(System.Linq.Expressions.ParameterExpression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.CatchBlock" />，代表包含已攔截 <see cref="T:System.Exception" /> 物件參考的 catch 陳述式，以便用於處理常式主體。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.CatchBlock" />。</returns>
      <param name="variable">
        <see cref="T:System.Linq.Expressions.ParameterExpression" />，代表這個處理常式所攔截 <see cref="T:System.Exception" /> 物件的參考。</param>
      <param name="body">catch 陳述式的主體。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Catch(System.Linq.Expressions.ParameterExpression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.CatchBlock" />，代表包含 <see cref="T:System.Exception" /> 篩選條件和已攔截 <see cref="T:System.Exception" /> 物件參考的 catch 陳述式。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.CatchBlock" />。</returns>
      <param name="variable">
        <see cref="T:System.Linq.Expressions.ParameterExpression" />，代表這個處理常式所攔截 <see cref="T:System.Exception" /> 物件的參考。</param>
      <param name="body">catch 陳述式的主體。</param>
      <param name="filter">
        <see cref="T:System.Exception" /> 篩選條件的主體。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Catch(System.Type,System.Linq.Expressions.Expression)">
      <summary>建立代表 catch 陳述式的 <see cref="T:System.Linq.Expressions.CatchBlock" />。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.CatchBlock" />。</returns>
      <param name="type">這個 <see cref="P:System.Linq.Expressions.Expression.Type" /> 將處理之 <see cref="T:System.Exception" /> 的 <see cref="T:System.Linq.Expressions.CatchBlock" />。</param>
      <param name="body">catch 陳述式的主體。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Catch(System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.CatchBlock" />，代表包含 <see cref="T:System.Exception" /> 篩選條件但沒有已攔截 <see cref="T:System.Exception" /> 物件參考的 catch 陳述式。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.CatchBlock" />。</returns>
      <param name="type">這個 <see cref="P:System.Linq.Expressions.Expression.Type" /> 將處理之 <see cref="T:System.Exception" /> 的 <see cref="T:System.Linq.Expressions.CatchBlock" />。</param>
      <param name="body">catch 陳述式的主體。</param>
      <param name="filter">
        <see cref="T:System.Exception" /> 篩選條件的主體。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ClearDebugInfo(System.Linq.Expressions.SymbolDocumentInfo)">
      <summary>建立用於清除序列點的 <see cref="T:System.Linq.Expressions.DebugInfoExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的執行個體，用於清除序列點。</returns>
      <param name="document">表示原始程式檔的 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Coalesce(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立代表聯合運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Coalesce" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Linq.Expressions.Expression.Type" /> 的 <paramref name="left" /> 屬性不表示參考類型或可為 Null 的實值類型。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="left" />.Type 和 <paramref name="right" />.Type 無法相互轉換。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Coalesce(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.LambdaExpression)">
      <summary>指定轉換函式，建立代表聯合運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Coalesce" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="conversion">要將 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="left" />.Type 和 <paramref name="right" />.Type 無法相互轉換。-或-<paramref name="conversion" /> 不是 null，且 <paramref name="conversion" />.Type 為委派類型，其採用的引數不是剛好一個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Linq.Expressions.Expression.Type" /> 的 <paramref name="left" /> 屬性不表示參考類型或可為 Null 的實值類型。-或-<see cref="P:System.Linq.Expressions.Expression.Type" /> 的 <paramref name="left" /> 屬性所表示的類型無法指派至委派類型 <paramref name="conversion" />.Type 的參數類型。-或-<see cref="P:System.Linq.Expressions.Expression.Type" /> 的 <paramref name="right" /> 屬性不等於委派類型 <paramref name="conversion" />.Type 的傳回類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Condition(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立表示條件陳述式的 <see cref="T:System.Linq.Expressions.ConditionalExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ConditionalExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Conditional" />，且 <see cref="P:System.Linq.Expressions.ConditionalExpression.Test" />、<see cref="P:System.Linq.Expressions.ConditionalExpression.IfTrue" /> 和 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfFalse" /> 屬性設定為指定的值。</returns>
      <param name="test">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.Test" />。</param>
      <param name="ifTrue">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfTrue" />。</param>
      <param name="ifFalse">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfFalse" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="test" />、<paramref name="ifTrue" /> 或 <paramref name="ifFalse" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="test" />.Type 不是 <see cref="T:System.Boolean" />。-或-<paramref name="ifTrue" />.Type 不等於 <paramref name="ifFalse" />.Type。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Condition(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Type)">
      <summary>建立表示條件陳述式的 <see cref="T:System.Linq.Expressions.ConditionalExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ConditionalExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Conditional" />，且 <see cref="P:System.Linq.Expressions.ConditionalExpression.Test" />、<see cref="P:System.Linq.Expressions.ConditionalExpression.IfTrue" /> 和 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfFalse" /> 屬性設定為指定的值。</returns>
      <param name="test">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.Test" />。</param>
      <param name="ifTrue">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfTrue" />。</param>
      <param name="ifFalse">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfFalse" />。</param>
      <param name="type">要將 <see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Constant(System.Object)">
      <summary>建立 <see cref="T:System.Linq.Expressions.ConstantExpression" />，其 <see cref="P:System.Linq.Expressions.ConstantExpression.Value" /> 屬性設定為指定的值。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ConstantExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Constant" />，而 <see cref="P:System.Linq.Expressions.ConstantExpression.Value" /> 屬性設定為指定的值。</returns>
      <param name="value">要將 <see cref="T:System.Object" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ConstantExpression.Value" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Constant(System.Object,System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.ConstantExpression" />，其 <see cref="P:System.Linq.Expressions.ConstantExpression.Value" /> 和 <see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性設定為指定的值。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ConstantExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Constant" />，且 <see cref="P:System.Linq.Expressions.ConstantExpression.Value" /> 和 <see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性設定為指定的值。</returns>
      <param name="value">要將 <see cref="T:System.Object" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ConstantExpression.Value" />。</param>
      <param name="type">要將 <see cref="T:System.Type" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> 不為 null，且 <paramref name="type" /> 無法從 <paramref name="value" /> 的動態類型指派。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Continue(System.Linq.Expressions.LabelTarget)">
      <summary>建立代表 continue 陳述式的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等於 Continue、<see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 屬性設定為 <paramref name="target" />，且包含在跳躍時傳遞至目標標籤的 Null 值。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 將跳躍的目標 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Continue(System.Linq.Expressions.LabelTarget,System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.GotoExpression" />，代表含指定之類型的 continue 陳述式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等於 Continue、<see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 屬性設定為 <paramref name="target" />、<see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性設定為 <paramref name="type" />，且包含在跳躍時傳遞至目標標籤的 null 值。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 將跳躍的目標 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="type">要將 <see cref="T:System.Type" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Convert(System.Linq.Expressions.Expression,System.Type)">
      <summary>建立代表類型轉換作業的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Convert" />，且 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 和 <see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性設定為指定的值。</returns>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <param name="type">要將 <see cref="T:System.Type" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 或 <paramref name="type" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="expression" />.Type 和 <paramref name="type" /> 之間未定義轉換運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Convert(System.Linq.Expressions.Expression,System.Type,System.Reflection.MethodInfo)">
      <summary>建立代表轉換作業的 <see cref="T:System.Linq.Expressions.UnaryExpression" />，此轉換作業已指定實作方法。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Convert" />，且 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />、<see cref="P:System.Linq.Expressions.Expression.Type" /> 和 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <param name="type">要將 <see cref="T:System.Type" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 或 <paramref name="type" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好一個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="expression" />.Type 和 <paramref name="type" /> 之間未定義轉換運算子。-或-<paramref name="expression" />.Type 無法指派給 <paramref name="method" /> 所代表方法的引數類型。-或-<paramref name="method" /> 所代表方法的傳回類型無法指派給 <paramref name="type" />。-或-<paramref name="expression" />.Type 或 <paramref name="type" /> 是不可為 Null 的實值類型，且對應之不可為 Null 的實值類型不等於 <paramref name="method" /> 所表示之方法的引數類型或傳回類型。</exception>
      <exception cref="T:System.Reflection.AmbiguousMatchException">已找到一個以上的方法符合 <paramref name="method" /> 說明。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ConvertChecked(System.Linq.Expressions.Expression,System.Type)">
      <summary>建立代表轉換作業的 <see cref="T:System.Linq.Expressions.UnaryExpression" />；若目標類型已溢位，此運算式便會擲回例外狀況。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.ConvertChecked" />，且 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 和 <see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性設定為指定的值。</returns>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <param name="type">要將 <see cref="T:System.Type" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 或 <paramref name="type" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="expression" />.Type 和 <paramref name="type" /> 之間未定義轉換運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ConvertChecked(System.Linq.Expressions.Expression,System.Type,System.Reflection.MethodInfo)">
      <summary>建立代表轉換作業的 <see cref="T:System.Linq.Expressions.UnaryExpression" />，如果目標類型已溢位，且已指定此作業的實作方法，則此作業會擲回例外狀況。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.ConvertChecked" />，且 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />、<see cref="P:System.Linq.Expressions.Expression.Type" /> 和 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <param name="type">要將 <see cref="T:System.Type" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 或 <paramref name="type" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好一個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="expression" />.Type 和 <paramref name="type" /> 之間未定義轉換運算子。-或-<paramref name="expression" />.Type 無法指派給 <paramref name="method" /> 所代表方法的引數類型。-或-<paramref name="method" /> 所代表方法的傳回類型無法指派給 <paramref name="type" />。-或-<paramref name="expression" />.Type 或 <paramref name="type" /> 是不可為 Null 的實值類型，且對應之不可為 Null 的實值類型不等於 <paramref name="method" /> 所表示之方法的引數類型或傳回類型。</exception>
      <exception cref="T:System.Reflection.AmbiguousMatchException">已找到一個以上的方法符合 <paramref name="method" /> 說明。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.DebugInfo(System.Linq.Expressions.SymbolDocumentInfo,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>使用指定的時間範圍建立 <see cref="T:System.Linq.Expressions.DebugInfoExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的執行個體。</returns>
      <param name="document">表示原始程式檔的 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" />。</param>
      <param name="startLine">這個 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的起始行。必須大於 0。</param>
      <param name="startColumn">這個 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的起始欄。必須大於 0。</param>
      <param name="endLine">這個 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的結尾行。必須大於或等於起始行。</param>
      <param name="endColumn">這個 <see cref="T:System.Linq.Expressions.DebugInfoExpression" /> 的結尾欄。如果結尾行與起始行相同，則結尾欄必須大於或等於起始欄。在任何情況下，都必須大於 0。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Decrement(System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，代表運算式遞減 1。</summary>
      <returns>代表遞減後運算式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要遞減的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Decrement(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，代表運算式遞減 1。</summary>
      <returns>代表遞減後運算式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要遞減的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="method">表示實作方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Default(System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.DefaultExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性設定為指定的類型。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.DefaultExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Default" />，而 <see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性設定為指定的類型。</returns>
      <param name="type">要將 <see cref="T:System.Type" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Divide(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立代表算術除法運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Divide" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義 Division 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Divide(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立代表算術除法運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。實作的方法可加以指定。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Divide" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義 Division 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.DivideAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，代表不含溢位檢查的除法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.DivideAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.DivideAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，代表不含溢位檢查的除法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.DivideAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.DivideAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，代表不含溢位檢查的除法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.DivideAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要將 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ElementInit(System.Reflection.MethodInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>指定 <see cref="T:System.Linq.Expressions.ElementInit" /> 做為第二個引數，建立 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ElementInit" />，其 <see cref="P:System.Linq.Expressions.ElementInit.AddMethod" /> 和 <see cref="P:System.Linq.Expressions.ElementInit.Arguments" /> 屬性設定為指定的值。</returns>
      <param name="addMethod">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ElementInit.AddMethod" />。</param>
      <param name="arguments">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，包含要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ElementInit.Arguments" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="addMethod" /> 或 <paramref name="arguments" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="addMethod" /> 所表示的方法名稱不是 "Add" (不區分大小寫)。-或-<paramref name="addMethod" /> 所表示的方法不是執行個體方法。-或-<paramref name="arguments" /> 所包含的項目數目與 <paramref name="addMethod" /> 所表示之方法的參數數目不同。-或-<see cref="P:System.Linq.Expressions.Expression.Type" /> 之一個或多個項目的 <paramref name="arguments" /> 屬性無法指派給 <paramref name="addMethod" /> 所表示之方法的對應參數類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ElementInit(System.Reflection.MethodInfo,System.Linq.Expressions.Expression[])">
      <summary>指定值陣列做為第二個引數，建立 <see cref="T:System.Linq.Expressions.ElementInit" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ElementInit" />，其 <see cref="P:System.Linq.Expressions.ElementInit.AddMethod" /> 和 <see cref="P:System.Linq.Expressions.ElementInit.Arguments" /> 屬性設定為指定的值。</returns>
      <param name="addMethod">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ElementInit.AddMethod" />。</param>
      <param name="arguments">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ElementInit.Arguments" /> 物件陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="addMethod" /> 或 <paramref name="arguments" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">addMethod 所表示的方法名稱不是 "Add" (不區分大小寫)。-或-addMethod 所表示的方法不是執行個體方法。-或-引數包含的項目數目與 addMethod 所表示之方法的參數數目不同。-或-<see cref="P:System.Linq.Expressions.Expression.Type" /> 之一個或多個項目的 <paramref name="arguments" /> 屬性無法指派給 <paramref name="addMethod" /> 所表示之方法的對應參數類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Empty">
      <summary>建立具有 <see cref="T:System.Void" /> 類型的空運算式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.DefaultExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Default" />，且 <see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性設為 <see cref="T:System.Void" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Equal(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立代表相等比較的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Equal" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義等號比較運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Equal(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Boolean,System.Reflection.MethodInfo)">
      <summary>建立代表相等比較的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。實作的方法可加以指定。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Equal" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="liftToNull">true 表示將 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 設定為 true，false 則表示將 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 設定為 false。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義等號比較運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ExclusiveOr(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>使用 <see cref="T:System.Linq.Expressions.BinaryExpression" /> 做為使用者定義的類型，建立表示位元 XOR 運算的 op_ExclusiveOr。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.ExclusiveOr" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 XOR.Type 和 <paramref name="left" />.Type 定義 <paramref name="right" /> 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ExclusiveOr(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>使用 <see cref="T:System.Linq.Expressions.BinaryExpression" /> 做為使用者定義的類型，建立表示位元 XOR 運算的 op_ExclusiveOr。實作的方法可加以指定。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.ExclusiveOr" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 XOR.Type 和 <paramref name="left" />.Type 定義 <paramref name="right" /> 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ExclusiveOrAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>使用 <see cref="T:System.Linq.Expressions.BinaryExpression" /> 做為使用者定義的類型，建立代表位元 XOR 指派運算的 op_ExclusiveOr。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.ExclusiveOrAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ExclusiveOrAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>使用 <see cref="T:System.Linq.Expressions.BinaryExpression" /> 做為使用者定義的類型，建立代表位元 XOR 指派運算的 op_ExclusiveOr。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.ExclusiveOrAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ExclusiveOrAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>使用 <see cref="T:System.Linq.Expressions.BinaryExpression" /> 做為使用者定義的類型，建立代表位元 XOR 指派運算的 op_ExclusiveOr。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.ExclusiveOrAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要將 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Field(System.Linq.Expressions.Expression,System.Reflection.FieldInfo)">
      <summary>建立代表存取欄位的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.MemberAccess" />，且 <see cref="P:System.Linq.Expressions.MemberExpression.Expression" /> 和 <see cref="P:System.Linq.Expressions.MemberExpression.Member" /> 屬性設定為指定的值。</returns>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MemberExpression.Expression" />。若為 static (在 Visual Basic 中為 Shared)，<paramref name="expression" /> 必須是 null。</param>
      <param name="field">要將 <see cref="T:System.Reflection.FieldInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MemberExpression.Member" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="field" /> 為 null。-或-<paramref name="field" /> 所表示的欄位不是 static (在 Visual Basic 中為 Shared)，且 <paramref name="expression" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="expression" />.Type 無法指派給 <paramref name="field" /> 所代表之欄位的宣告類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Field(System.Linq.Expressions.Expression,System.String)">
      <summary>建立 <see cref="T:System.Linq.Expressions.MemberExpression" />，代表存取可指定欄位名稱的欄位。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.MemberAccess" />、<see cref="P:System.Linq.Expressions.MemberExpression.Expression" /> 屬性設定為 <paramref name="expression" />，且 <see cref="P:System.Linq.Expressions.MemberExpression.Member" /> 屬性設定為 <see cref="T:System.Reflection.FieldInfo" />，代表 <paramref name="fieldName" /> 所表示的欄位。</returns>
      <param name="expression">
        <see cref="T:System.Linq.Expressions.Expression" />，其 <see cref="P:System.Linq.Expressions.Expression.Type" /> 包含名為 <paramref name="fieldName" /> 的欄位。如果是靜態欄位，可以是 Null。</param>
      <param name="fieldName">要存取的欄位名稱。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 或 <paramref name="fieldName" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="fieldName" />.Type 或其基底類型中沒有定義名為 <paramref name="expression" /> 的欄位。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Field(System.Linq.Expressions.Expression,System.Type,System.String)">
      <summary>建立代表存取欄位的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</returns>
      <param name="expression">欄位的包含物件。如果是靜態欄位，可以是 Null。</param>
      <param name="type">包含欄位的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
      <param name="fieldName">要存取的欄位。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.GetActionType(System.Type[])">
      <summary>建立 <see cref="T:System.Type" /> 物件，這個物件代表具有特定類型引數的泛型 System.Action 委派類型。</summary>
      <returns>System.Action 委派的類型，有指定的類型引數。</returns>
      <param name="typeArgs">最多包含十六個 <see cref="T:System.Type" /> 物件的陣列，指定 System.Action 委派類型的類型引數。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="typeArgs" /> 包含十六個以上的項目。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="typeArgs" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.GetDelegateType(System.Type[])">
      <summary>取得 <see cref="P:System.Linq.Expressions.Expression.Type" /> 物件，這個物件代表具有特定類型引數的泛型 System.Func 或 System.Action 委派類型。</summary>
      <returns>委派類型。</returns>
      <param name="typeArgs">委派的類型引數。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.GetFuncType(System.Type[])">
      <summary>建立 <see cref="P:System.Linq.Expressions.Expression.Type" /> 物件，這個物件代表具有特定類型引數的泛型 System.Func 委派類型。最後一個類型引數指定已建立之委派的傳回類型。</summary>
      <returns>System.Func 委派的類型，有指定的類型引數。</returns>
      <param name="typeArgs">包含一到十七個 <see cref="T:System.Type" /> 物件的陣列，指定 System.Func 委派類型的類型引數。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="typeArgs" /> 包含少於一個或多過十七個項目。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="typeArgs" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Goto(System.Linq.Expressions.LabelTarget)">
      <summary>建立表示 "go to" 陳述式的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等於 Goto、<see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 屬性設定為指定的值，且包含在跳躍時傳遞至目標標籤的 Null 值。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 將跳躍的目標 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Goto(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression)">
      <summary>建立表示 "go to" 陳述式的 <see cref="T:System.Linq.Expressions.GotoExpression" />。可以指定跳躍時傳遞至標籤的值。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等於 Goto、<see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 屬性設定為 <paramref name="target" />，且，且包含在跳躍時傳遞至目標標籤的 <paramref name="value" />。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 將跳躍的目標 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="value">將在跳躍時傳遞至關聯標籤的值。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Goto(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression,System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.GotoExpression" />，表示含指定之類型的 "go to" 陳述式。可以指定跳躍時傳遞至標籤的值。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等於 Goto、<see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 屬性設定為 <paramref name="target" />、<see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性設定為 <paramref name="type" />，且包含在跳躍時傳遞至目標標籤的 <paramref name="value" />。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 將跳躍的目標 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="value">將在跳躍時傳遞至關聯標籤的值。</param>
      <param name="type">要將 <see cref="T:System.Type" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Goto(System.Linq.Expressions.LabelTarget,System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.GotoExpression" />，表示含指定之類型的 "go to" 陳述式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等於 Goto、<see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 屬性設定為指定的值、<see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性設定為 <paramref name="type" />，且包含在跳躍時傳遞至目標標籤的 Null 值。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 將跳躍的目標 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="type">要將 <see cref="T:System.Type" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.GreaterThan(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立代表「大於」數值比較的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.GreaterThan" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義「大於」運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.GreaterThan(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Boolean,System.Reflection.MethodInfo)">
      <summary>建立代表「大於」數值比較的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。實作的方法可加以指定。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.GreaterThan" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="liftToNull">true 表示將 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 設定為 true，false 則表示將 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 設定為 false。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義「大於」運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.GreaterThanOrEqual(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立代表「大於或等於」數值比較的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.GreaterThanOrEqual" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義「大於或等於」運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.GreaterThanOrEqual(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Boolean,System.Reflection.MethodInfo)">
      <summary>建立代表「大於或等於」數值比較的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.GreaterThanOrEqual" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="liftToNull">true 表示將 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 設定為 true，false 則表示將 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 設定為 false。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義「大於或等於」運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.IfThen(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.ConditionalExpression" />，代表具有 if 陳述式的條件區塊。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ConditionalExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Conditional" />，且 <see cref="P:System.Linq.Expressions.ConditionalExpression.Test" /> 和 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfTrue" /> 屬性設定為指定的值。<see cref="P:System.Linq.Expressions.ConditionalExpression.IfFalse" /> 屬性已設定為預設運算式，而這個方法傳回之結果 <see cref="T:System.Linq.Expressions.ConditionalExpression" /> 的類型為 <see cref="T:System.Void" />。</returns>
      <param name="test">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.Test" />。</param>
      <param name="ifTrue">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfTrue" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.IfThenElse(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.ConditionalExpression" />，代表具有 if 和 else 陳述式的條件區塊。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ConditionalExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Conditional" />，且 <see cref="P:System.Linq.Expressions.ConditionalExpression.Test" />、<see cref="P:System.Linq.Expressions.ConditionalExpression.IfTrue" /> 和 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfFalse" /> 屬性設定為指定的值。這個方法所傳回之結果 <see cref="T:System.Linq.Expressions.ConditionalExpression" /> 的類型為 <see cref="T:System.Void" />。</returns>
      <param name="test">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.Test" />。</param>
      <param name="ifTrue">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfTrue" />。</param>
      <param name="ifFalse">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ConditionalExpression.IfFalse" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Increment(System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，表示運算式值遞增 1。</summary>
      <returns>表示遞增後運算式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要遞增的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Increment(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，代表運算式遞增 1。</summary>
      <returns>表示遞增後運算式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要遞增的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="method">表示實作方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Invoke(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立 <see cref="T:System.Linq.Expressions.InvocationExpression" />，將委派或 Lambda 運算式套用至引數運算式清單。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.InvocationExpression" />，可將指定的委派或 Lambda 運算式套用至所提供的引數。</returns>
      <param name="expression">
        <see cref="T:System.Linq.Expressions.Expression" />，代表要套用的委派或 Lambda 運算式。</param>
      <param name="arguments">包含 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 物件的 <see cref="T:System.Linq.Expressions.Expression" />，代表要套用委派或 Lambda 運算式的引數。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="expression" />.Type 不代表委派類型或 <see cref="T:System.Linq.Expressions.Expression`1" />。-或-<see cref="P:System.Linq.Expressions.Expression.Type" /> 之項目的 <paramref name="arguments" /> 屬性無法指派給 <paramref name="expression" /> 所表示之委派的對應參數類型。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="arguments" /> 包含的項目數與 <paramref name="expression" /> 所表示委派的參數清單不同。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Invoke(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.InvocationExpression" />，將委派或 Lambda 運算式套用至引數運算式清單。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.InvocationExpression" />，可將指定的委派或 Lambda 運算式套用至所提供的引數。</returns>
      <param name="expression">
        <see cref="T:System.Linq.Expressions.Expression" />，代表要套用的委派或 Lambda 運算式。</param>
      <param name="arguments">
        <see cref="T:System.Linq.Expressions.Expression" /> 物件的陣列，代表要套用委派或 Lambda 運算式的引數。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="expression" />.Type 不代表委派類型或 <see cref="T:System.Linq.Expressions.Expression`1" />。-或-<see cref="P:System.Linq.Expressions.Expression.Type" /> 之項目的 <paramref name="arguments" /> 屬性無法指派給 <paramref name="expression" /> 所表示之委派的對應參數類型。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="arguments" /> 包含的項目數與 <paramref name="expression" /> 所表示委派的參數清單不同。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.IsFalse(System.Linq.Expressions.Expression)">
      <summary>傳回運算式是否評估為 false。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" /> 的執行個體。</returns>
      <param name="expression">要評估的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.IsFalse(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>傳回運算式是否評估為 false。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" /> 的執行個體。</returns>
      <param name="expression">要評估的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="method">表示實作方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.IsTrue(System.Linq.Expressions.Expression)">
      <summary>傳回運算式是否評估為 true。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" /> 的執行個體。</returns>
      <param name="expression">要評估的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.IsTrue(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>傳回運算式是否評估為 true。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" /> 的執行個體。</returns>
      <param name="expression">要評估的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="method">表示實作方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Label">
      <summary>建立 <see cref="T:System.Linq.Expressions.LabelTarget" />，代表包含 void 類型但沒有名稱的標籤。</summary>
      <returns>新的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Label(System.Linq.Expressions.LabelTarget)">
      <summary>建立 <see cref="T:System.Linq.Expressions.LabelExpression" />，代表沒有預設值的標籤。</summary>
      <returns>沒有預設值的 <see cref="T:System.Linq.Expressions.LabelExpression" />。</returns>
      <param name="target">這個 <see cref="T:System.Linq.Expressions.LabelTarget" /> 將與之產生關聯的 <see cref="T:System.Linq.Expressions.LabelExpression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Label(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.LabelExpression" />，代表包含指定之預設值的標籤。</summary>
      <returns>包含指定之預設值的 <see cref="T:System.Linq.Expressions.LabelExpression" />。</returns>
      <param name="target">這個 <see cref="T:System.Linq.Expressions.LabelTarget" /> 將與之產生關聯的 <see cref="T:System.Linq.Expressions.LabelExpression" />。</param>
      <param name="defaultValue">透過一般控制流程到達標籤時，這個 <see cref="T:System.Linq.Expressions.LabelExpression" /> 的值。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Label(System.String)">
      <summary>建立 <see cref="T:System.Linq.Expressions.LabelTarget" />，代表包含 void 類型和指定之名稱的標籤。</summary>
      <returns>新的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</returns>
      <param name="name">標籤名稱。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Label(System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.LabelTarget" />，代表包含指定之類型的標籤。</summary>
      <returns>新的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</returns>
      <param name="type">跳至標籤時所傳遞值的類型。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Label(System.Type,System.String)">
      <summary>建立 <see cref="T:System.Linq.Expressions.LabelTarget" />，代表包含指定之類型和名稱的標籤。</summary>
      <returns>新的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</returns>
      <param name="type">跳至標籤時所傳遞值的類型。</param>
      <param name="name">標籤名稱。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Linq.Expressions.Expression,System.Boolean,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>首先透過建構委派類型來建立 LambdaExpression。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 屬性等於 Lambda，且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 屬性設定為指定的值。</returns>
      <param name="body">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="tailCall">
        <see cref="T:System.Boolean" />，其代表編譯所建立的運算式時，是否會套用 tail 呼叫最佳化。</param>
      <param name="parameters">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 物件。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda``1(System.Linq.Expressions.Expression,System.Boolean,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>建立 <see cref="T:System.Linq.Expressions.Expression`1" />，其中委派類型在編譯階段為已知。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression`1" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Lambda" />，且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 屬性設定為指定的值。</returns>
      <param name="body">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="tailCall">
        <see cref="T:System.Boolean" />，其代表編譯所建立的運算式時，是否會套用 tail 呼叫最佳化。</param>
      <param name="parameters">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 物件。</param>
      <typeparam name="TDelegate">委派類型。</typeparam>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda``1(System.Linq.Expressions.Expression,System.Boolean,System.Linq.Expressions.ParameterExpression[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.Expression`1" />，其中委派類型在編譯階段為已知。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression`1" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Lambda" />，且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 屬性設定為指定的值。</returns>
      <param name="body">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="tailCall">
        <see cref="T:System.Boolean" />，其代表編譯所建立的運算式時，是否會套用 tail 呼叫最佳化。</param>
      <param name="parameters">陣列，其中包含用以填入 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 物件。</param>
      <typeparam name="TDelegate">委派類型。</typeparam>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Linq.Expressions.Expression,System.Boolean,System.Linq.Expressions.ParameterExpression[])">
      <summary>首先透過建構委派類型來建立 LambdaExpression。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 屬性等於 Lambda，且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 屬性設定為指定的值。</returns>
      <param name="body">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="tailCall">
        <see cref="T:System.Boolean" />，其代表編譯所建立的運算式時，是否會套用 tail 呼叫最佳化。</param>
      <param name="parameters">陣列，其中包含用以填入 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 物件。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda``1(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>建立 <see cref="T:System.Linq.Expressions.Expression`1" />，其中委派類型在編譯階段為已知。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression`1" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Lambda" />，且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 屬性設定為指定的值。</returns>
      <param name="body">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="parameters">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 物件。</param>
      <typeparam name="TDelegate">委派類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="body" /> 為 null。-或-<paramref name="parameters" /> 中的一個或多個項目為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="TDelegate" /> 不是委派類型。-或-<paramref name="body" />.Type 所表示的類型無法指派給 <paramref name="TDelegate" /> 的傳回類型。-或-<paramref name="parameters" /> 包含的項目數與 <paramref name="TDelegate" /> 的參數清單不同。-或-<see cref="P:System.Linq.Expressions.Expression.Type" /> 之項目的 <paramref name="parameters" /> 屬性無法從 <paramref name="TDelegate" /> 之對應參數類型的類型指派。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>首先透過建構委派類型來建立 LambdaExpression。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 屬性等於 Lambda，且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 屬性設定為指定的值。</returns>
      <param name="body">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="parameters">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 物件。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda``1(System.Linq.Expressions.Expression,System.Linq.Expressions.ParameterExpression[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.Expression`1" />，其中委派類型在編譯階段為已知。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression`1" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Lambda" />，且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 屬性設定為指定的值。</returns>
      <param name="body">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="parameters">用以填入 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合之 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 物件的陣列。</param>
      <typeparam name="TDelegate">委派類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="body" /> 為 null。-或-<paramref name="parameters" /> 中的一個或多個項目為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="TDelegate" /> 不是委派類型。-或-<paramref name="body" />.Type 所表示的類型無法指派給 <paramref name="TDelegate" /> 的傳回類型。-或-<paramref name="parameters" /> 包含的項目數與 <paramref name="TDelegate" /> 的參數清單不同。-或-<see cref="P:System.Linq.Expressions.Expression.Type" /> 之項目的 <paramref name="parameters" /> 屬性無法從 <paramref name="TDelegate" /> 之對應參數類型的類型指派。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Linq.Expressions.Expression,System.Linq.Expressions.ParameterExpression[])">
      <summary>首先透過建構委派類型來建立 <see cref="T:System.Linq.Expressions.LambdaExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Lambda" />，且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 屬性設定為指定的值。</returns>
      <param name="body">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="parameters">用以填入 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合之 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 物件的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="body" /> 為 null。-或-<paramref name="parameters" /> 的一個或多個項目為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="parameters" /> 包含十六個以上的項目。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda``1(System.Linq.Expressions.Expression,System.String,System.Boolean,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>建立 <see cref="T:System.Linq.Expressions.Expression`1" />，其中委派類型在編譯階段為已知。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression`1" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Lambda" />，且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 屬性設定為指定的值。</returns>
      <param name="body">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="name">Lambda 的名稱。用於產生偵錯資訊。</param>
      <param name="tailCall">
        <see cref="T:System.Boolean" />，其代表編譯所建立的運算式時，是否會套用 tail 呼叫最佳化。</param>
      <param name="parameters">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 物件。</param>
      <typeparam name="TDelegate">委派類型。</typeparam>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Linq.Expressions.Expression,System.String,System.Boolean,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>首先透過建構委派類型來建立 LambdaExpression。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 屬性等於 Lambda，且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 屬性設定為指定的值。</returns>
      <param name="body">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="name">Lambda 的名稱。用於發出偵錯資訊。</param>
      <param name="tailCall">
        <see cref="T:System.Boolean" />，其代表編譯所建立的運算式時，是否會套用 tail 呼叫最佳化。</param>
      <param name="parameters">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 物件。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Linq.Expressions.Expression,System.String,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>首先透過建構委派類型來建立 LambdaExpression。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 屬性等於 Lambda，且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 屬性設定為指定的值。</returns>
      <param name="body">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="name">Lambda 的名稱。用於發出偵錯資訊。</param>
      <param name="parameters">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 物件。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda``1(System.Linq.Expressions.Expression,System.String,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>建立 <see cref="T:System.Linq.Expressions.Expression`1" />，其中委派類型在編譯階段為已知。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression`1" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Lambda" />，且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 屬性設定為指定的值。</returns>
      <param name="body">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.Expression`1.Body" />。</param>
      <param name="name">Lambda 的名稱。用於產生偵錯資訊。</param>
      <param name="parameters">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.Expression`1.Parameters" /> 物件。</param>
      <typeparam name="TDelegate">委派類型。</typeparam>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Type,System.Linq.Expressions.Expression,System.Boolean,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>首先透過建構委派類型來建立 LambdaExpression。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 屬性等於 Lambda，且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 屬性設定為指定的值。</returns>
      <param name="delegateType">
        <see cref="P:System.Linq.Expressions.Expression.Type" />，代表 Lambda 的委派簽章。</param>
      <param name="body">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="tailCall">
        <see cref="T:System.Boolean" />，其代表編譯所建立的運算式時，是否會套用 tail 呼叫最佳化。</param>
      <param name="parameters">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 物件。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Type,System.Linq.Expressions.Expression,System.Boolean,System.Linq.Expressions.ParameterExpression[])">
      <summary>首先透過建構委派類型來建立 LambdaExpression。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 屬性等於 Lambda，且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 屬性設定為指定的值。</returns>
      <param name="delegateType">
        <see cref="P:System.Linq.Expressions.Expression.Type" />，代表 Lambda 的委派簽章。</param>
      <param name="body">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="tailCall">
        <see cref="T:System.Boolean" />，其代表編譯所建立的運算式時，是否會套用 tail 呼叫最佳化。</param>
      <param name="parameters">陣列，其中包含用以填入 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 物件。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Type,System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>首先透過建構委派類型來建立 <see cref="T:System.Linq.Expressions.LambdaExpression" />。它可用於在編譯階段不知道委派類型時。</summary>
      <returns>表示 lambda 運算式的物件，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Lambda" />，且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 屬性設定為指定的值。</returns>
      <param name="delegateType">
        <see cref="T:System.Type" />，代表 Lambda 的委派簽章。</param>
      <param name="body">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="parameters">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="delegateType" /> 或 <paramref name="body" /> 為 null。-或-<paramref name="parameters" /> 中的一個或多個項目為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="delegateType" /> 不表示委派類型。-或-<paramref name="body" />.Type 表示的類型無法指派給 <paramref name="delegateType" /> 所表示之委派類型的傳回類型。-或-<paramref name="parameters" /> 包含的項目數與 <paramref name="delegateType" /> 所表示之委派類型的參數清單不同。-或-<see cref="P:System.Linq.Expressions.Expression.Type" /> 之項目的 <paramref name="parameters" /> 屬性無法從由 <paramref name="delegateType" /> 所表示之委派類型的對應參數類型指派。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.ParameterExpression[])">
      <summary>首先透過建構委派類型來建立 <see cref="T:System.Linq.Expressions.LambdaExpression" />。它可用於在編譯階段不知道委派類型時。</summary>
      <returns>表示 lambda 運算式的物件，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Lambda" />，且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 屬性設定為指定的值。</returns>
      <param name="delegateType">
        <see cref="T:System.Type" />，代表 Lambda 的委派簽章。</param>
      <param name="body">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="parameters">用以填入 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合之 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 物件的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="delegateType" /> 或 <paramref name="body" /> 為 null。-或-<paramref name="parameters" /> 中的一個或多個項目為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="delegateType" /> 不表示委派類型。-或-<paramref name="body" />.Type 表示的類型無法指派給 <paramref name="delegateType" /> 所表示之委派類型的傳回類型。-或-<paramref name="parameters" /> 包含的項目數與 <paramref name="delegateType" /> 所表示之委派類型的參數清單不同。-或-<see cref="P:System.Linq.Expressions.Expression.Type" /> 之項目的 <paramref name="parameters" /> 屬性無法從由 <paramref name="delegateType" /> 所表示之委派類型的對應參數類型指派。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Type,System.Linq.Expressions.Expression,System.String,System.Boolean,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>首先透過建構委派類型來建立 LambdaExpression。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 屬性等於 Lambda，且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 屬性設定為指定的值。</returns>
      <param name="delegateType">
        <see cref="P:System.Linq.Expressions.Expression.Type" />，代表 Lambda 的委派簽章。</param>
      <param name="body">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="name">Lambda 的名稱。用於發出偵錯資訊。</param>
      <param name="tailCall">
        <see cref="T:System.Boolean" />，其代表編譯所建立的運算式時，是否會套用 tail 呼叫最佳化。</param>
      <param name="parameters">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 物件。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Lambda(System.Type,System.Linq.Expressions.Expression,System.String,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>首先透過建構委派類型來建立 LambdaExpression。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.LambdaExpression" />，其 <see cref="P:System.Linq.Expressions.LambdaExpression.NodeType" /> 屬性等於 Lambda，且 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 和 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 屬性設定為指定的值。</returns>
      <param name="delegateType">
        <see cref="P:System.Linq.Expressions.Expression.Type" />，代表 Lambda 的委派簽章。</param>
      <param name="body">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" />。</param>
      <param name="name">Lambda 的名稱。用於發出偵錯資訊。</param>
      <param name="parameters">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 物件。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.LeftShift(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立表示位元左移 (Left-Shift) 運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.LeftShift" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type. 定義 Left-shift 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.LeftShift(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立表示位元左移 (Left-Shift) 運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.LeftShift" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null 且不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義 Left-shift 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.LeftShiftAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立表示位元左移位指派運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.LeftShiftAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.LeftShiftAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立表示位元左移位指派運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.LeftShiftAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.LeftShiftAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>建立表示位元左移位指派運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.LeftShiftAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要將 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.LessThan(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立代表「小於」數值比較的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.LessThan" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義「小於」運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.LessThan(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Boolean,System.Reflection.MethodInfo)">
      <summary>建立代表「小於」數值比較的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.LessThan" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="liftToNull">true 表示將 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 設定為 true，false 則表示將 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 設定為 false。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義「小於」運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.LessThanOrEqual(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立代表「小於或等於」數值比較的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.LessThanOrEqual" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義「小於或等於」運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.LessThanOrEqual(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Boolean,System.Reflection.MethodInfo)">
      <summary>建立代表「小於或等於」數值比較的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.LessThanOrEqual" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="liftToNull">true 表示將 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 設定為 true，false 則表示將 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 設定為 false。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義「小於或等於」運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListBind(System.Reflection.MemberInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ElementInit})">
      <summary>建立 <see cref="T:System.Linq.Expressions.MemberListBinding" />，其中成員是欄位或屬性。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberListBinding" />，其 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 屬性等於 <see cref="F:System.Linq.Expressions.MemberBindingType.ListBinding" />，且 <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 和 <see cref="P:System.Linq.Expressions.MemberListBinding.Initializers" /> 屬性設定為指定的值。</returns>
      <param name="member">
        <see cref="T:System.Reflection.MemberInfo" />，表示要將 <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 屬性設定為與之相等的欄位或屬性。</param>
      <param name="initializers">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.ElementInit" /> 集合的 <see cref="P:System.Linq.Expressions.MemberListBinding.Initializers" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="member" /> 為 null。-或-<paramref name="initializers" /> 的一個或多個項目為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="member" /> 不代表欄位或屬性。-或-<see cref="P:System.Reflection.FieldInfo.FieldType" /> 所表示之欄位或屬性的 <see cref="P:System.Reflection.PropertyInfo.PropertyType" /> 或 <paramref name="member" /> 不會實作 <see cref="T:System.Collections.IEnumerable" />。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListBind(System.Reflection.MemberInfo,System.Linq.Expressions.ElementInit[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.MemberListBinding" />，其中成員是欄位或屬性。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberListBinding" />，其 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 屬性等於 <see cref="F:System.Linq.Expressions.MemberBindingType.ListBinding" />，且 <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 和 <see cref="P:System.Linq.Expressions.MemberListBinding.Initializers" /> 屬性設定為指定的值。</returns>
      <param name="member">
        <see cref="T:System.Reflection.MemberInfo" />，表示要將 <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 屬性設定為與之相等的欄位或屬性。</param>
      <param name="initializers">用以填入 <see cref="T:System.Linq.Expressions.ElementInit" /> 集合之 <see cref="P:System.Linq.Expressions.MemberListBinding.Initializers" /> 物件的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="member" /> 為 null。-或-<paramref name="initializers" /> 的一個或多個項目為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="member" /> 不代表欄位或屬性。-或-<see cref="P:System.Reflection.FieldInfo.FieldType" /> 所表示之欄位或屬性的 <see cref="P:System.Reflection.PropertyInfo.PropertyType" /> 或 <paramref name="member" /> 不會實作 <see cref="T:System.Collections.IEnumerable" />。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListBind(System.Reflection.MethodInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ElementInit})">
      <summary>根據指定的屬性存取子方法建立 <see cref="T:System.Linq.Expressions.MemberListBinding" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberListBinding" />，其 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 屬性等於 <see cref="F:System.Linq.Expressions.MemberBindingType.ListBinding" />，並將 <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 屬性設定為 <see cref="T:System.Reflection.MemberInfo" />，表示在 <paramref name="propertyAccessor" /> 中存取的屬性，<see cref="P:System.Linq.Expressions.MemberListBinding.Initializers" /> 則會填入 <paramref name="initializers" /> 的項目。</returns>
      <param name="propertyAccessor">代表屬性存取子方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
      <param name="initializers">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.ElementInit" /> 集合的 <see cref="P:System.Linq.Expressions.MemberListBinding.Initializers" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyAccessor" /> 為 null。-或-<paramref name="initializers" /> 的一個或多個項目為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="propertyAccessor" /> 不代表屬性存取子 (Accessor) 方法。-或-屬性 (由 <see cref="P:System.Reflection.PropertyInfo.PropertyType" /> 存取表示的方法) 的 <paramref name="propertyAccessor" /> 不會實作 <see cref="T:System.Collections.IEnumerable" />。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListBind(System.Reflection.MethodInfo,System.Linq.Expressions.ElementInit[])">
      <summary>根據指定的屬性存取子方法建立 <see cref="T:System.Linq.Expressions.MemberListBinding" /> 物件。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberListBinding" />，其 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 屬性等於 <see cref="F:System.Linq.Expressions.MemberBindingType.ListBinding" />，並將 <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 屬性設定為 <see cref="T:System.Reflection.MemberInfo" />，表示在 <paramref name="propertyAccessor" /> 中存取的屬性，<see cref="P:System.Linq.Expressions.MemberListBinding.Initializers" /> 則會填入 <paramref name="initializers" /> 的項目。</returns>
      <param name="propertyAccessor">代表屬性存取子方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
      <param name="initializers">用以填入 <see cref="T:System.Linq.Expressions.ElementInit" /> 集合之 <see cref="P:System.Linq.Expressions.MemberListBinding.Initializers" /> 物件的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyAccessor" /> 為 null。-或-<paramref name="initializers" /> 的一個或多個項目為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="propertyAccessor" /> 不代表屬性存取子 (Accessor) 方法。-或-屬性 (由 <see cref="P:System.Reflection.PropertyInfo.PropertyType" /> 存取表示的方法) 的 <paramref name="propertyAccessor" /> 不會實作 <see cref="T:System.Collections.IEnumerable" />。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListInit(System.Linq.Expressions.NewExpression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ElementInit})">
      <summary>建立 <see cref="T:System.Linq.Expressions.ListInitExpression" />，它使用指定的 <see cref="T:System.Linq.Expressions.ElementInit" /> 物件初始化集合。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ListInitExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.ListInit" />，且 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" /> 和 <see cref="P:System.Linq.Expressions.ListInitExpression.Initializers" /> 屬性設定為指定的值。</returns>
      <param name="newExpression">要將 <see cref="T:System.Linq.Expressions.NewExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" />。</param>
      <param name="initializers">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.ElementInit" /> 集合的 <see cref="P:System.Linq.Expressions.ListInitExpression.Initializers" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newExpression" /> 或 <paramref name="initializers" /> 為 null。-或-<paramref name="initializers" /> 的一個或多個項目為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="newExpression" />.Type 沒有實作 <see cref="T:System.Collections.IEnumerable" />。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListInit(System.Linq.Expressions.NewExpression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立 <see cref="T:System.Linq.Expressions.ListInitExpression" />，其使用名為 "Add" 的方法將項目加入集合。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ListInitExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.ListInit" />，而 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" /> 屬性設定為指定的值。</returns>
      <param name="newExpression">要將 <see cref="T:System.Linq.Expressions.NewExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" />。</param>
      <param name="initializers">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.ListInitExpression.Initializers" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newExpression" /> 或 <paramref name="initializers" /> 為 null。-或-<paramref name="initializers" /> 的一個或多個項目為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="newExpression" />.Type 沒有實作 <see cref="T:System.Collections.IEnumerable" />。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="newExpression" />.Type 或其基底類型中沒有宣告名為 "Add" (區分大小寫) 的執行個體方法。-或-<paramref name="newExpression" />.Type 及其基底類型上的加入方法採用的引數不是剛好一個。-或-由 <see cref="P:System.Linq.Expressions.Expression.Type" /> 其第一個項目之 <paramref name="initializers" /> 屬性所表示的類型無法指派給 <paramref name="newExpression" />.Type 或其基底類型上加入方法的引數類型。-或-<paramref name="newExpression" />.Type 和 (或) 其基底類型上有多個與引數相容且名為 "Add" (區分大小寫) 的方法。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListInit(System.Linq.Expressions.NewExpression,System.Linq.Expressions.ElementInit[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.ListInitExpression" />，它使用指定的 <see cref="T:System.Linq.Expressions.ElementInit" /> 物件初始化集合。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ListInitExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.ListInit" />，且 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" /> 和 <see cref="P:System.Linq.Expressions.ListInitExpression.Initializers" /> 屬性設定為指定的值。</returns>
      <param name="newExpression">要將 <see cref="T:System.Linq.Expressions.NewExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" />。</param>
      <param name="initializers">用以填入 <see cref="T:System.Linq.Expressions.ElementInit" /> 集合之 <see cref="P:System.Linq.Expressions.ListInitExpression.Initializers" /> 物件的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newExpression" /> 或 <paramref name="initializers" /> 為 null。-或-<paramref name="initializers" /> 的一個或多個項目為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="newExpression" />.Type 沒有實作 <see cref="T:System.Collections.IEnumerable" />。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListInit(System.Linq.Expressions.NewExpression,System.Linq.Expressions.Expression[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.ListInitExpression" />，其使用名為 "Add" 的方法將項目加入集合。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ListInitExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.ListInit" />，而 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" /> 屬性設定為指定的值。</returns>
      <param name="newExpression">要將 <see cref="T:System.Linq.Expressions.NewExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" />。</param>
      <param name="initializers">用以填入 <see cref="T:System.Linq.Expressions.Expression" /> 集合之 <see cref="P:System.Linq.Expressions.ListInitExpression.Initializers" /> 物件的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newExpression" /> 或 <paramref name="initializers" /> 為 null。-或-<paramref name="initializers" /> 的一個或多個項目為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="newExpression" />.Type 沒有實作 <see cref="T:System.Collections.IEnumerable" />。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="newExpression" />.Type 或其基底類型中沒有宣告名為 "Add" (區分大小寫) 的執行個體方法。-或-<paramref name="newExpression" />.Type 及其基底類型上的加入方法採用的引數不是剛好一個。-或-由 <see cref="P:System.Linq.Expressions.Expression.Type" /> 其第一個項目之 <paramref name="initializers" /> 屬性所表示的類型無法指派給 <paramref name="newExpression" />.Type 或其基底類型上加入方法的引數類型。-或-<paramref name="newExpression" />.Type 和 (或) 其基底類型上有多個與引數相容且名為 "Add" (區分大小寫) 的方法。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListInit(System.Linq.Expressions.NewExpression,System.Reflection.MethodInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立 <see cref="T:System.Linq.Expressions.ListInitExpression" />，其使用指定的方法將項目加入集合。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ListInitExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.ListInit" />，而 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" /> 屬性設定為指定的值。</returns>
      <param name="newExpression">要將 <see cref="T:System.Linq.Expressions.NewExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" />。</param>
      <param name="addMethod">
        <see cref="T:System.Reflection.MethodInfo" />，代表名為 "Add" (區分大小寫) 的執行個體方法，可將項目加入集合。</param>
      <param name="initializers">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.ListInitExpression.Initializers" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newExpression" /> 或 <paramref name="initializers" /> 為 null。-或-<paramref name="initializers" /> 的一個或多個項目為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="newExpression" />.Type 沒有實作 <see cref="T:System.Collections.IEnumerable" />。-或-<paramref name="addMethod" /> 不是 null，它不表示名為 "Add" (區分大小寫) 的執行個體方法，這個方法剛好只採用一個引數。-或-<paramref name="addMethod" /> 不是 null，且 <see cref="P:System.Linq.Expressions.Expression.Type" /> 之一個或多個項目的 <paramref name="initializers" /> 屬性表示的類型無法指派給 <paramref name="addMethod" /> 所表示之方法的引數類型。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="addMethod" /> 是 null，且 <paramref name="newExpression" />.Type 或其基底類型上沒有採用一個類型相容引數之名為 "Add" 的執行個體方法。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ListInit(System.Linq.Expressions.NewExpression,System.Reflection.MethodInfo,System.Linq.Expressions.Expression[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.ListInitExpression" />，其使用指定的方法將項目加入集合。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ListInitExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.ListInit" />，而 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" /> 屬性設定為指定的值。</returns>
      <param name="newExpression">要將 <see cref="T:System.Linq.Expressions.NewExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" />。</param>
      <param name="addMethod">
        <see cref="T:System.Reflection.MethodInfo" />，代表採用一個引數的執行個體方法，可將項目加入集合。</param>
      <param name="initializers">用以填入 <see cref="T:System.Linq.Expressions.Expression" /> 集合之 <see cref="P:System.Linq.Expressions.ListInitExpression.Initializers" /> 物件的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newExpression" /> 或 <paramref name="initializers" /> 為 null。-或-<paramref name="initializers" /> 的一個或多個項目為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="newExpression" />.Type 沒有實作 <see cref="T:System.Collections.IEnumerable" />。-或-<paramref name="addMethod" /> 不是 null，它不表示名為 "Add" (區分大小寫) 的執行個體方法，這個方法剛好只採用一個引數。-或-<paramref name="addMethod" /> 不是 null，且 <see cref="P:System.Linq.Expressions.Expression.Type" /> 之一個或多個項目的 <paramref name="initializers" /> 屬性表示的類型無法指派給 <paramref name="addMethod" /> 所表示之方法的引數類型。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="addMethod" /> 是 null，且 <paramref name="newExpression" />.Type 或其基底類型上沒有採用一個類型相容引數之名為 "Add" 的執行個體方法。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Loop(System.Linq.Expressions.Expression)">
      <summary>建立包含指定之主體的 <see cref="T:System.Linq.Expressions.LoopExpression" />。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.LoopExpression" />。</returns>
      <param name="body">迴圈的主體。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Loop(System.Linq.Expressions.Expression,System.Linq.Expressions.LabelTarget)">
      <summary>建立包含指定之主體和 break 目標的 <see cref="T:System.Linq.Expressions.LoopExpression" />。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.LoopExpression" />。</returns>
      <param name="body">迴圈的主體。</param>
      <param name="break">迴圈主體所使用的 break 目標。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Loop(System.Linq.Expressions.Expression,System.Linq.Expressions.LabelTarget,System.Linq.Expressions.LabelTarget)">
      <summary>建立包含指定之主體的 <see cref="T:System.Linq.Expressions.LoopExpression" />。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.LoopExpression" />。</returns>
      <param name="body">迴圈的主體。</param>
      <param name="break">迴圈主體所使用的 break 目標。</param>
      <param name="continue">迴圈主體所使用的 continue 目標。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeBinary(System.Linq.Expressions.ExpressionType,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>透過呼叫適當的 Factory 方法來建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，指定左運算元和右運算元。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，由呼叫適當的 Factory 方法所產生。</returns>
      <param name="binaryType">
        <see cref="T:System.Linq.Expressions.ExpressionType" />，指定二進位運算的類型。</param>
      <param name="left">代表左運算元的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="right">代表右運算元的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="binaryType" /> 未對應到二進位運算式節點。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeBinary(System.Linq.Expressions.ExpressionType,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Boolean,System.Reflection.MethodInfo)">
      <summary>透過呼叫適當的 Factory 方法來建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，指定左運算元、右運算元和實作方法。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，由呼叫適當的 Factory 方法所產生。</returns>
      <param name="binaryType">
        <see cref="T:System.Linq.Expressions.ExpressionType" />，指定二進位運算的類型。</param>
      <param name="left">代表左運算元的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="right">代表右運算元的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="liftToNull">true 表示將 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 設定為 true，false 則表示將 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 設定為 false。</param>
      <param name="method">
        <see cref="T:System.Reflection.MethodInfo" />，可指定實作的方法。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="binaryType" /> 未對應到二進位運算式節點。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeBinary(System.Linq.Expressions.ExpressionType,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Boolean,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>透過呼叫適當的 Factory 方法來建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，指定左運算元、右運算元、實作方法和類型轉換函式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，由呼叫適當的 Factory 方法所產生。</returns>
      <param name="binaryType">
        <see cref="T:System.Linq.Expressions.ExpressionType" />，指定二進位運算的類型。</param>
      <param name="left">代表左運算元的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="right">代表右運算元的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="liftToNull">true 表示將 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 設定為 true，false 則表示將 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 設定為 false。</param>
      <param name="method">
        <see cref="T:System.Reflection.MethodInfo" />，可指定實作的方法。</param>
      <param name="conversion">代表類型轉換函式的 <see cref="T:System.Linq.Expressions.LambdaExpression" />。只有當 <paramref name="binaryType" /> 為 <see cref="F:System.Linq.Expressions.ExpressionType.Coalesce" /> 或複合指派時，才會使用這個參數。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="binaryType" /> 未對應到二進位運算式節點。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeCatchBlock(System.Type,System.Linq.Expressions.ParameterExpression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.CatchBlock" />，代表含指定之項目的 catch 陳述式。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.CatchBlock" />。</returns>
      <param name="type">這個 <see cref="P:System.Linq.Expressions.Expression.Type" /> 將處理之 <see cref="T:System.Exception" /> 的 <see cref="T:System.Linq.Expressions.CatchBlock" />。</param>
      <param name="variable">
        <see cref="T:System.Linq.Expressions.ParameterExpression" />，代表這個處理常式所攔截 <see cref="T:System.Exception" /> 物件的參考。</param>
      <param name="body">catch 陳述式的主體。</param>
      <param name="filter">
        <see cref="T:System.Exception" /> 篩選條件的主體。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeGoto(System.Linq.Expressions.GotoExpressionKind,System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression,System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.GotoExpression" />，代表指定之 <see cref="T:System.Linq.Expressions.GotoExpressionKind" /> 的跳躍點。也可以指定在跳躍時傳遞至標籤的值。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等於 <paramref name="kind" />、<see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 屬性設定為 <paramref name="target" />、<see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性設定為 <paramref name="type" />，且包含在跳躍時傳遞至目標標籤的 <paramref name="value" />。</returns>
      <param name="kind">
        <see cref="T:System.Linq.Expressions.GotoExpressionKind" /> 的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 將跳躍的目標 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="value">將在跳躍時傳遞至關聯標籤的值。</param>
      <param name="type">要將 <see cref="T:System.Type" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeIndex(System.Linq.Expressions.Expression,System.Reflection.PropertyInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立 <see cref="T:System.Linq.Expressions.IndexExpression" />，代表存取物件中的索引屬性。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.IndexExpression" />。</returns>
      <param name="instance">屬性所屬的物件。如果屬性為 static (在 Visual Basic 中為 shared)，則應該是 Null。</param>
      <param name="indexer">
        <see cref="T:System.Linq.Expressions.Expression" />，代表要編製索引的屬性。</param>
      <param name="arguments">IEnumerable&lt;Expression&gt; (在 Visual Basic 中為 IEnumerable (Of Expression))，包含將用於對屬性進行索引的引數。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeMemberAccess(System.Linq.Expressions.Expression,System.Reflection.MemberInfo)">
      <summary>建立代表存取欄位或屬性的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberExpression" />，由呼叫適當的 Factory 方法所產生。</returns>
      <param name="expression">
        <see cref="T:System.Linq.Expressions.Expression" />，代表成員所屬的物件。如果是靜態成員，可以是 Null。</param>
      <param name="member">
        <see cref="T:System.Reflection.MemberInfo" />，說明要存取的欄位或屬性。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="member" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="member" /> 不代表欄位或屬性。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeTry(System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.CatchBlock})">
      <summary>建立 <see cref="T:System.Linq.Expressions.TryExpression" />，代表含指定之項目的 try 區塊。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.TryExpression" />。</returns>
      <param name="type">try 運算式的結果類型。如果為 Null，則 bodh 和所有處理常式必須具有相同的類型。</param>
      <param name="body">try 區塊的主體。</param>
      <param name="finally">finally 區塊的主體。如果 try 區塊沒有相關聯的 finally 區塊，則傳遞 Null。</param>
      <param name="fault">fault 區塊的主體。如果 try 區塊沒有相關聯的 fault 區塊，則傳遞 Null。</param>
      <param name="handlers">
        <see cref="T:System.Linq.Expressions.CatchBlock" /> 的集合，代表要與 try 區塊產生關聯的 catch 陳述式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeUnary(System.Linq.Expressions.ExpressionType,System.Linq.Expressions.Expression,System.Type)">
      <summary>指定運算元並呼叫適當的 Factory 方法，建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" />，由呼叫適當的 Factory 方法所產生。</returns>
      <param name="unaryType">
        <see cref="T:System.Linq.Expressions.ExpressionType" />，指定一元運算的類型。</param>
      <param name="operand">代表運算元的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="type">
        <see cref="T:System.Type" />，指定轉換後的類型 (如果不適用，則傳遞 null)。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="operand" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="unaryType" /> 未對應到一元運算式節點。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MakeUnary(System.Linq.Expressions.ExpressionType,System.Linq.Expressions.Expression,System.Type,System.Reflection.MethodInfo)">
      <summary>指定運算元和實作方法並透過呼叫適當的 Fatory 方法，建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" />，由呼叫適當的 Factory 方法所產生。</returns>
      <param name="unaryType">
        <see cref="T:System.Linq.Expressions.ExpressionType" />，指定一元運算的類型。</param>
      <param name="operand">代表運算元的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="type">
        <see cref="T:System.Type" />，指定轉換後的類型 (如果不適用，則傳遞 null)。</param>
      <param name="method">代表實作方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="operand" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="unaryType" /> 未對應到一元運算式節點。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MemberBind(System.Reflection.MemberInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.MemberBinding})">
      <summary>建立 <see cref="T:System.Linq.Expressions.MemberMemberBinding" />，表示遞迴初始化欄位或屬性的成員。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberMemberBinding" />，其 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 屬性等於 <see cref="F:System.Linq.Expressions.MemberBindingType.MemberBinding" />，且 <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 和 <see cref="P:System.Linq.Expressions.MemberMemberBinding.Bindings" /> 屬性設定為指定的值。</returns>
      <param name="member">要將 <see cref="T:System.Reflection.MemberInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MemberBinding.Member" />。</param>
      <param name="bindings">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.MemberBinding" /> 集合的 <see cref="P:System.Linq.Expressions.MemberMemberBinding.Bindings" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="member" /> 或 <paramref name="bindings" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="member" /> 不代表欄位或屬性。-或-<see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 之項目的 <paramref name="bindings" /> 屬性不表示 <paramref name="member" /> 所表示之欄位或屬性類型的成員。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MemberBind(System.Reflection.MemberInfo,System.Linq.Expressions.MemberBinding[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.MemberMemberBinding" />，表示遞迴初始化欄位或屬性的成員。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberMemberBinding" />，其 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 屬性等於 <see cref="F:System.Linq.Expressions.MemberBindingType.MemberBinding" />，且 <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 和 <see cref="P:System.Linq.Expressions.MemberMemberBinding.Bindings" /> 屬性設定為指定的值。</returns>
      <param name="member">要將 <see cref="T:System.Reflection.MemberInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MemberBinding.Member" />。</param>
      <param name="bindings">用以填入 <see cref="T:System.Linq.Expressions.MemberBinding" /> 集合之 <see cref="P:System.Linq.Expressions.MemberMemberBinding.Bindings" /> 物件的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="member" /> 或 <paramref name="bindings" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="member" /> 不代表欄位或屬性。-或-<see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 之項目的 <paramref name="bindings" /> 屬性不表示 <paramref name="member" /> 所表示之欄位或屬性類型的成員。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MemberBind(System.Reflection.MethodInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.MemberBinding})">
      <summary>建立 <see cref="T:System.Linq.Expressions.MemberMemberBinding" />，表示遞迴初始化成員的成員，該成員可透過使用屬性存取子方法來存取。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberMemberBinding" />，其 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 屬性等於 <see cref="F:System.Linq.Expressions.MemberBindingType.MemberBinding" />，並將 <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 屬性設定為 <see cref="T:System.Reflection.PropertyInfo" />，表示在 <paramref name="propertyAccessor" /> 中存取的屬性，<see cref="P:System.Linq.Expressions.MemberMemberBinding.Bindings" /> 屬性則設定為指定的值。</returns>
      <param name="propertyAccessor">
        <see cref="T:System.Reflection.MethodInfo" />，代表屬性存取子方法。</param>
      <param name="bindings">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.MemberBinding" /> 集合的 <see cref="P:System.Linq.Expressions.MemberMemberBinding.Bindings" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyAccessor" /> 或 <paramref name="bindings" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="propertyAccessor" /> 不代表屬性存取子 (Accessor) 方法。-或-<see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 之項目的 <paramref name="bindings" /> 屬性不表示 <paramref name="propertyAccessor" /> 所表示之方法存取的屬性類型成員。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MemberBind(System.Reflection.MethodInfo,System.Linq.Expressions.MemberBinding[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.MemberMemberBinding" />，表示遞迴初始化成員的成員，該成員可透過使用屬性存取子方法來存取。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberMemberBinding" />，其 <see cref="P:System.Linq.Expressions.MemberBinding.BindingType" /> 屬性等於 <see cref="F:System.Linq.Expressions.MemberBindingType.MemberBinding" />，並將 <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 屬性設定為 <see cref="T:System.Reflection.PropertyInfo" />，表示在 <paramref name="propertyAccessor" /> 中存取的屬性，<see cref="P:System.Linq.Expressions.MemberMemberBinding.Bindings" /> 屬性則設定為指定的值。</returns>
      <param name="propertyAccessor">
        <see cref="T:System.Reflection.MethodInfo" />，代表屬性存取子方法。</param>
      <param name="bindings">用以填入 <see cref="T:System.Linq.Expressions.MemberBinding" /> 集合之 <see cref="P:System.Linq.Expressions.MemberMemberBinding.Bindings" /> 物件的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyAccessor" /> 或 <paramref name="bindings" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="propertyAccessor" /> 不代表屬性存取子 (Accessor) 方法。-或-<see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 之項目的 <paramref name="bindings" /> 屬性不表示 <paramref name="propertyAccessor" /> 所表示之方法存取的屬性類型成員。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MemberInit(System.Linq.Expressions.NewExpression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.MemberBinding})">
      <summary>表示建立新物件並初始化物件屬性的運算式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberInitExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.MemberInit" />，且 <see cref="P:System.Linq.Expressions.MemberInitExpression.NewExpression" /> 和 <see cref="P:System.Linq.Expressions.MemberInitExpression.Bindings" /> 屬性設定為指定的值。</returns>
      <param name="newExpression">要將 <see cref="T:System.Linq.Expressions.NewExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MemberInitExpression.NewExpression" />。</param>
      <param name="bindings">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.MemberBinding" /> 集合的 <see cref="P:System.Linq.Expressions.MemberInitExpression.Bindings" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newExpression" /> 或 <paramref name="bindings" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 之項目的 <paramref name="bindings" /> 屬性不表示 <paramref name="newExpression" />.Type 所表示之類型的成員。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MemberInit(System.Linq.Expressions.NewExpression,System.Linq.Expressions.MemberBinding[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.MemberInitExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberInitExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.MemberInit" />，且 <see cref="P:System.Linq.Expressions.MemberInitExpression.NewExpression" /> 和 <see cref="P:System.Linq.Expressions.MemberInitExpression.Bindings" /> 屬性設定為指定的值。</returns>
      <param name="newExpression">要將 <see cref="T:System.Linq.Expressions.NewExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MemberInitExpression.NewExpression" />。</param>
      <param name="bindings">用以填入 <see cref="T:System.Linq.Expressions.MemberBinding" /> 集合之 <see cref="P:System.Linq.Expressions.MemberInitExpression.Bindings" /> 物件的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newExpression" /> 或 <paramref name="bindings" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.Linq.Expressions.MemberBinding.Member" /> 之項目的 <paramref name="bindings" /> 屬性不表示 <paramref name="newExpression" />.Type 所表示之類型的成員。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Modulo(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立代表算術餘數運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Modulo" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義餘數運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Modulo(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立代表算術餘數運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Modulo" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義餘數運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ModuloAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立表示餘數指派運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.ModuloAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ModuloAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立表示餘數指派運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.ModuloAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ModuloAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>建立表示餘數指派運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.ModuloAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要將 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Multiply(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其代表不含溢位檢查的算術乘法運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Multiply" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義 Multiplication 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Multiply(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其代表不含溢位檢查的算術乘法運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Multiply" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義 Multiplication 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MultiplyAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示不含溢位檢查的乘法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.MultiplyAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MultiplyAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示不含溢位檢查的乘法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.MultiplyAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MultiplyAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示不含溢位檢查的乘法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.MultiplyAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要將 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MultiplyAssignChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示包含溢位檢查的乘法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.MultiplyAssignChecked" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MultiplyAssignChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示包含溢位檢查的乘法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.MultiplyAssignChecked" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MultiplyAssignChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示包含溢位檢查的乘法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.MultiplyAssignChecked" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要將 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MultiplyChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其代表包含溢位檢查的算術乘法運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.MultiplyChecked" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義 Multiplication 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.MultiplyChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其代表包含溢位檢查的算術乘法運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.MultiplyChecked" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義 Multiplication 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Negate(System.Linq.Expressions.Expression)">
      <summary>建立代表算術負運算的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Negate" />，而 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 屬性設定為指定的值。</returns>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="expression" />.Type 定義一元減號運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Negate(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立代表算術負運算的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Negate" />，且 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 和 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好一個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="expression" />.Type 定義一元減號運算子。-或-<paramref name="expression" />.Type (如果它是可為 Null 的實質類型，則為其對應之不可為 Null 的類型) 無法指派給 <paramref name="method" /> 所代表之方法的引數類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.NegateChecked(System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，其代表包含溢位檢查的算術負運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.NegateChecked" />，而 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 屬性設定為指定的值。</returns>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="expression" />.Type 定義一元減號運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.NegateChecked(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，其代表包含溢位檢查的算術負運算。實作的方法可加以指定。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.NegateChecked" />，且 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 和 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好一個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="expression" />.Type 定義一元減號運算子。-或-<paramref name="expression" />.Type (如果它是可為 Null 的實質類型，則為其對應之不可為 Null 的類型) 無法指派給 <paramref name="method" /> 所代表之方法的引數類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.New(System.Reflection.ConstructorInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.NewExpression" />，代表呼叫不採用任何引數的指定建構函式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.NewExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.New" />，而 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" /> 屬性設定為指定的值。</returns>
      <param name="constructor">要將 <see cref="T:System.Reflection.ConstructorInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="constructor" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">由 <paramref name="constructor" /> 表示的建構函式，擁有至少一個參數。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.New(System.Reflection.ConstructorInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立 <see cref="T:System.Linq.Expressions.NewExpression" />，其代表以指定之引數呼叫指定的建構函式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.NewExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.New" />，且 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" /> 和 <see cref="P:System.Linq.Expressions.NewExpression.Arguments" /> 屬性設定為指定的值。</returns>
      <param name="constructor">要將 <see cref="T:System.Reflection.ConstructorInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" />。</param>
      <param name="arguments">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.NewExpression.Arguments" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="constructor" /> 為 null。-或-<paramref name="arguments" /> 的元素是 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arguments" /> 參數包含的項目數目與 <paramref name="constructor" /> 所表示之建構函式的參數數目不同。-或-<see cref="P:System.Linq.Expressions.Expression.Type" /> 之項目的 <paramref name="arguments" /> 屬性無法指派給建構函式 (由 <paramref name="constructor" /> 所表示) 之對應參數的類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.New(System.Reflection.ConstructorInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression},System.Collections.Generic.IEnumerable{System.Reflection.MemberInfo})">
      <summary>建立 <see cref="T:System.Linq.Expressions.NewExpression" />，其代表以指定之引數呼叫指定的建構函式。已指定可存取由建構函式初始化欄位的成員。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.NewExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.New" />，且 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" />、<see cref="P:System.Linq.Expressions.NewExpression.Arguments" /> 和 <see cref="P:System.Linq.Expressions.NewExpression.Members" /> 屬性設定為指定的值。</returns>
      <param name="constructor">要將 <see cref="T:System.Reflection.ConstructorInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" />。</param>
      <param name="arguments">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.NewExpression.Arguments" /> 物件。</param>
      <param name="members">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Reflection.MemberInfo" /> 集合的 <see cref="P:System.Linq.Expressions.NewExpression.Members" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="constructor" /> 為 null。-或-<paramref name="arguments" /> 的元素是 null。-或-<paramref name="members" /> 的元素是 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arguments" /> 參數包含的項目數目與 <paramref name="constructor" /> 所表示之建構函式的參數數目不同。-或-<see cref="P:System.Linq.Expressions.Expression.Type" /> 之項目的 <paramref name="arguments" /> 屬性無法指派給建構函式 (由 <paramref name="constructor" /> 所表示) 之對應參數的類型。-或-<paramref name="members" /> 參數的項目數與 <paramref name="arguments" /> 的不同。-或-<paramref name="arguments" /> 的項目有 <see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性，它表示無法指派給 <paramref name="members" /> 其對應項目所表示成員類型的類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.New(System.Reflection.ConstructorInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression},System.Reflection.MemberInfo[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.NewExpression" />，其代表以指定之引數呼叫指定的建構函式。已指定可存取由建構函式初始化欄位的成員為陣列。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.NewExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.New" />，且 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" />、<see cref="P:System.Linq.Expressions.NewExpression.Arguments" /> 和 <see cref="P:System.Linq.Expressions.NewExpression.Members" /> 屬性設定為指定的值。</returns>
      <param name="constructor">要將 <see cref="T:System.Reflection.ConstructorInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" />。</param>
      <param name="arguments">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.NewExpression.Arguments" /> 物件。</param>
      <param name="members">用以填入 <see cref="T:System.Reflection.MemberInfo" /> 集合之 <see cref="P:System.Linq.Expressions.NewExpression.Members" /> 物件的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="constructor" /> 為 null。-或-<paramref name="arguments" /> 的元素是 null。-或-<paramref name="members" /> 的元素是 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arguments" /> 參數包含的項目數目與 <paramref name="constructor" /> 所表示之建構函式的參數數目不同。-或-<see cref="P:System.Linq.Expressions.Expression.Type" /> 之項目的 <paramref name="arguments" /> 屬性無法指派給建構函式 (由 <paramref name="constructor" /> 所表示) 之對應參數的類型。-或-<paramref name="members" /> 參數的項目數與 <paramref name="arguments" /> 的不同。-或-<paramref name="arguments" /> 的項目有 <see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性，它表示無法指派給 <paramref name="members" /> 其對應項目所表示成員類型的類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.New(System.Reflection.ConstructorInfo,System.Linq.Expressions.Expression[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.NewExpression" />，其代表以指定之引數呼叫指定的建構函式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.NewExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.New" />，且 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" /> 和 <see cref="P:System.Linq.Expressions.NewExpression.Arguments" /> 屬性設定為指定的值。</returns>
      <param name="constructor">要將 <see cref="T:System.Reflection.ConstructorInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" />。</param>
      <param name="arguments">用以填入 <see cref="T:System.Linq.Expressions.Expression" /> 集合之 <see cref="P:System.Linq.Expressions.NewExpression.Arguments" /> 物件的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="constructor" /> 為 null。-或-<paramref name="arguments" /> 的元素是 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arguments" /> 的長度不符合 <paramref name="constructor" /> 所表示之建構函式的參數數目。-或-<see cref="P:System.Linq.Expressions.Expression.Type" /> 之項目的 <paramref name="arguments" /> 屬性無法指派給建構函式 (由 <paramref name="constructor" /> 所表示) 之對應參數的類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.New(System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.NewExpression" />，代表呼叫指定類型的無參數建構函式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.NewExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.New" />，且 <see cref="P:System.Linq.Expressions.NewExpression.Constructor" /> 屬性設定為 <see cref="T:System.Reflection.ConstructorInfo" />，代表不含指定類型之參數的建構函式。</returns>
      <param name="type">
        <see cref="T:System.Type" />，具有不採用任何引數的建構函式。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="type" /> 表示的類型沒有無參數建構函式。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.NewArrayBounds(System.Type,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立 <see cref="T:System.Linq.Expressions.NewArrayExpression" />，其代表建立具有指定之陣序的陣列。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.NewArrayExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.NewArrayBounds" />，而 <see cref="P:System.Linq.Expressions.NewArrayExpression.Expressions" /> 屬性設定為指定的值。</returns>
      <param name="type">
        <see cref="T:System.Type" />，代表陣列的項目類型。</param>
      <param name="bounds">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.NewArrayExpression.Expressions" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> 或 <paramref name="bounds" /> 為 null。-或-<paramref name="bounds" /> 的元素是 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.Linq.Expressions.Expression.Type" /> 之項目的 <paramref name="bounds" /> 屬性不表示整數類資料類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.NewArrayBounds(System.Type,System.Linq.Expressions.Expression[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.NewArrayExpression" />，其代表建立具有指定之陣序的陣列。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.NewArrayExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.NewArrayBounds" />，而 <see cref="P:System.Linq.Expressions.NewArrayExpression.Expressions" /> 屬性設定為指定的值。</returns>
      <param name="type">
        <see cref="T:System.Type" />，代表陣列的項目類型。</param>
      <param name="bounds">用以填入 <see cref="T:System.Linq.Expressions.Expression" /> 集合之 <see cref="P:System.Linq.Expressions.NewArrayExpression.Expressions" /> 物件的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> 或 <paramref name="bounds" /> 為 null。-或-<paramref name="bounds" /> 的元素是 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.Linq.Expressions.Expression.Type" /> 之項目的 <paramref name="bounds" /> 屬性不表示整數類資料類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.NewArrayInit(System.Type,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立 <see cref="T:System.Linq.Expressions.NewArrayExpression" />，其代表建立一維陣列，並從項目清單將此陣列初始化。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.NewArrayExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.NewArrayInit" />，而 <see cref="P:System.Linq.Expressions.NewArrayExpression.Expressions" /> 屬性設定為指定的值。</returns>
      <param name="type">
        <see cref="T:System.Type" />，代表陣列的項目類型。</param>
      <param name="initializers">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其中包含用以填入 <see cref="T:System.Linq.Expressions.Expression" /> 集合的 <see cref="P:System.Linq.Expressions.NewArrayExpression.Expressions" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> 或 <paramref name="initializers" /> 為 null。-或-<paramref name="initializers" /> 的元素是 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Linq.Expressions.Expression.Type" /> 之項目的 <paramref name="initializers" /> 屬性所表示的類型無法指派給 <paramref name="type" /> 所表示的類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.NewArrayInit(System.Type,System.Linq.Expressions.Expression[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.NewArrayExpression" />，其代表建立一維陣列，並從項目清單將此陣列初始化。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.NewArrayExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.NewArrayInit" />，而 <see cref="P:System.Linq.Expressions.NewArrayExpression.Expressions" /> 屬性設定為指定的值。</returns>
      <param name="type">
        <see cref="T:System.Type" />，代表陣列的項目類型。</param>
      <param name="initializers">用以填入 <see cref="T:System.Linq.Expressions.Expression" /> 集合之 <see cref="P:System.Linq.Expressions.NewArrayExpression.Expressions" /> 物件的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> 或 <paramref name="initializers" /> 為 null。-或-<paramref name="initializers" /> 的元素是 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Linq.Expressions.Expression.Type" /> 之項目的 <paramref name="initializers" /> 屬性所表示的類型無法指派給 <paramref name="type" /> 類型。</exception>
    </member>
    <member name="P:System.Linq.Expressions.Expression.NodeType">
      <summary>取得這個 <see cref="T:System.Linq.Expressions.Expression" /> 的節點類型。</summary>
      <returns>其中一個 <see cref="T:System.Linq.Expressions.ExpressionType" /> 值。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Not(System.Linq.Expressions.Expression)">
      <summary>建立表示位元補數運算的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Not" />，而 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 屬性設定為指定的值。</returns>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="expression" />.Type 定義一元 NOT 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Not(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立表示位元補數運算的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。實作的方法可加以指定。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Not" />，且 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 和 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好一個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="expression" />.Type 定義一元 NOT 運算子。-或-<paramref name="expression" />.Type (如果它是可為 Null 的實質類型，則為其對應之不可為 Null 的類型) 無法指派給 <paramref name="method" /> 所代表之方法的引數類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.NotEqual(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立代表不相等比較的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.NotEqual" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義不等比較運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.NotEqual(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Boolean,System.Reflection.MethodInfo)">
      <summary>建立代表不相等比較的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.NotEqual" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="liftToNull">true 表示將 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 設定為 true，false 則表示將 <see cref="P:System.Linq.Expressions.BinaryExpression.IsLiftedToNull" /> 設定為 false。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義不等比較運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.OnesComplement(System.Linq.Expressions.Expression)">
      <summary>傳回表示 1 補數的運算式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" /> 的執行個體。</returns>
      <param name="expression">
        <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.OnesComplement(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>傳回表示 1 補數的運算式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" /> 的執行個體。</returns>
      <param name="expression">
        <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="method">表示實作方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Or(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立代表位元 <see cref="T:System.Linq.Expressions.BinaryExpression" /> 運算的 OR。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Or" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 OR.Type 和 <paramref name="left" />.Type 定義位元 <paramref name="right" /> 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Or(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立代表位元 <see cref="T:System.Linq.Expressions.BinaryExpression" /> 運算的 OR。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Or" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 OR.Type 和 <paramref name="left" />.Type 定義位元 <paramref name="right" /> 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.OrAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立表示位元 OR 指派運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.OrAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.OrAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立表示位元 OR 指派運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.OrAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.OrAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>建立表示位元 OR 指派運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.OrAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要將 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.OrElse(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示只在第一個運算元計算結果為 OR 時才求第二個運算元之值的 false 條件運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.OrElse" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 OR.Type 和 <paramref name="left" />.Type 定義位元 <paramref name="right" /> 運算子。-或-<paramref name="left" />.Type 和 <paramref name="right" />.Type 是不相同的布林類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.OrElse(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示只在第一個運算元計算結果為 OR 時才求第二個運算元之值的 false 條件運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.OrElse" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 OR.Type 和 <paramref name="left" />.Type 定義位元 <paramref name="right" /> 運算子。-或-<paramref name="method" /> 為 null，且 <paramref name="left" />.Type 和 <paramref name="right" />.Type 不是相同的布林類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Parameter(System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 節點，此節點可用以識別運算式樹狀結構中的參數或變數。</summary>
      <returns>建立含指定之名稱和類型的 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 節點。</returns>
      <param name="type">參數或變數的類型。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Parameter(System.Type,System.String)">
      <summary>建立 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 節點，此節點可用以識別運算式樹狀結構中的參數或變數。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ParameterExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Parameter" />，且 <see cref="P:System.Linq.Expressions.Expression.Type" /> 和 <see cref="P:System.Linq.Expressions.ParameterExpression.Name" /> 屬性設定為指定的值。</returns>
      <param name="type">參數或變數的類型。</param>
      <param name="name">參數或變數的名稱，僅供偵錯或列印之用。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PostDecrementAssign(System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，表示後面接續原始運算式遞減 1 的運算式指派。</summary>
      <returns>表示結果運算式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要套用上述作業的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PostDecrementAssign(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，表示後面接續原始運算式遞減 1 的運算式指派。</summary>
      <returns>表示結果運算式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要套用上述作業的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="method">表示實作方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PostIncrementAssign(System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，表示後面接續原始運算式遞增 1 的運算式指派。</summary>
      <returns>表示結果運算式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要套用上述作業的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PostIncrementAssign(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，表示後面接續原始運算式遞增 1 的運算式指派。</summary>
      <returns>表示結果運算式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要套用上述作業的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="method">表示實作方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Power(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立代表將數字提升為乘冪數的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Power" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義 Exponentiation 運算子。-或-<paramref name="left" />.Type 和 (或) <paramref name="right" />.Type 不是 <see cref="T:System.Double" />。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Power(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立代表將數字提升為乘冪數的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Power" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義 Exponentiation 運算子。-或-<paramref name="method" /> 為 null，且 <paramref name="left" />.Type 和 (或) <paramref name="right" />.Type 不是 <see cref="T:System.Double" />。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PowerAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示將運算式自乘為乘冪，並且將結果指派回運算式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.PowerAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PowerAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示將運算式自乘為乘冪，並且將結果指派回運算式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.PowerAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PowerAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示將運算式自乘為乘冪，並且將結果指派回運算式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.PowerAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要將 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PreDecrementAssign(System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，可讓運算式遞減 1，並將結果指派回運算式。</summary>
      <returns>表示結果運算式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要套用上述作業的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PreDecrementAssign(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，可讓運算式遞減 1，並將結果指派回運算式。</summary>
      <returns>表示結果運算式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要套用上述作業的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="method">表示實作方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PreIncrementAssign(System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，可讓運算式遞增 1，並將結果指派回運算式。</summary>
      <returns>表示結果運算式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要套用上述作業的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PreIncrementAssign(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，可讓運算式遞增 1，並將結果指派回運算式。</summary>
      <returns>表示結果運算式的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="expression">要套用上述作業的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="method">表示實作方法的 <see cref="T:System.Reflection.MethodInfo" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Property(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.MemberExpression" />，代表透過使用屬性存取方法來存取屬性。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.MemberAccess" />、<see cref="P:System.Linq.Expressions.MemberExpression.Expression" /> 屬性設定為 <paramref name="expression" />，且 <see cref="P:System.Linq.Expressions.MemberExpression.Member" /> 屬性設定為 <see cref="T:System.Reflection.PropertyInfo" />，代表在 <paramref name="propertyAccessor" /> 中存取的屬性。</returns>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MemberExpression.Expression" />。如果是靜態屬性，可以是 null。</param>
      <param name="propertyAccessor">
        <see cref="T:System.Reflection.MethodInfo" />，代表屬性存取子方法。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyAccessor" /> 為 null。-或-<paramref name="propertyAccessor" /> 所表示的方法不是 static (在 Visual Basic 中為 Shared)，且 <paramref name="expression" /> 是 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="expression" />.Type 無法指派給 <paramref name="propertyAccessor" /> 所代表之方法的宣告類型。-或-<paramref name="propertyAccessor" /> 所表示的方法不是屬性存取子方法。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Property(System.Linq.Expressions.Expression,System.Reflection.PropertyInfo)">
      <summary>建立代表存取屬性的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.MemberAccess" />，且 <see cref="P:System.Linq.Expressions.MemberExpression.Expression" /> 和 <see cref="P:System.Linq.Expressions.MemberExpression.Member" /> 屬性設定為指定的值。</returns>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MemberExpression.Expression" />。如果是靜態屬性，可以是 null。</param>
      <param name="property">要將 <see cref="T:System.Reflection.PropertyInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.MemberExpression.Member" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="property" /> 為 null。-或-<paramref name="property" /> 所表示的屬性不是 static (在 Visual Basic 中為 Shared)，且 <paramref name="expression" /> 是 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="expression" />.Type 無法指派給 <paramref name="property" /> 所表示屬性的宣告類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Property(System.Linq.Expressions.Expression,System.Reflection.PropertyInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立 <see cref="T:System.Linq.Expressions.IndexExpression" />，表示索引之屬性的存取結果。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.IndexExpression" />。</returns>
      <param name="instance">屬性所屬的物件。如果屬性為靜態/共用屬性，就必須為 null。</param>
      <param name="indexer">
        <see cref="T:System.Reflection.PropertyInfo" />，代表要編製索引的屬性。</param>
      <param name="arguments">
        <see cref="T:System.Collections.Generic.IEnumerable`1" /> 物件的 <see cref="T:System.Linq.Expressions.Expression" />，用於對屬性進行索引。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Property(System.Linq.Expressions.Expression,System.Reflection.PropertyInfo,System.Linq.Expressions.Expression[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.IndexExpression" />，表示索引之屬性的存取結果。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.IndexExpression" />。</returns>
      <param name="instance">屬性所屬的物件。如果屬性為靜態/共用屬性，就必須為 null。</param>
      <param name="indexer">
        <see cref="T:System.Reflection.PropertyInfo" />，代表要編製索引的屬性。</param>
      <param name="arguments">
        <see cref="T:System.Linq.Expressions.Expression" /> 物件的陣列，用於對屬性進行索引。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Property(System.Linq.Expressions.Expression,System.String)">
      <summary>建立代表存取屬性的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.MemberAccess" />、<see cref="P:System.Linq.Expressions.MemberExpression.Expression" /> 屬性設定為 <paramref name="expression" />，且 <see cref="P:System.Linq.Expressions.MemberExpression.Member" /> 屬性設定為 <see cref="T:System.Reflection.PropertyInfo" />，代表 <paramref name="propertyName" /> 所表示的屬性。</returns>
      <param name="expression">
        <see cref="T:System.Linq.Expressions.Expression" />，其 <see cref="P:System.Linq.Expressions.Expression.Type" /> 包含名為 <paramref name="propertyName" /> 的屬性。如果是靜態屬性，可以是 null。</param>
      <param name="propertyName">要存取的屬性名稱。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 或 <paramref name="propertyName" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="propertyName" />.Type 或其基底類型中沒有定義名為 <paramref name="expression" /> 的屬性。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Property(System.Linq.Expressions.Expression,System.String,System.Linq.Expressions.Expression[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.IndexExpression" />，表示索引之屬性的存取結果。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.IndexExpression" />。</returns>
      <param name="instance">屬性所屬的物件。如果屬性為靜態/共用屬性，就必須為 null。</param>
      <param name="propertyName">索引子的名稱。</param>
      <param name="arguments">
        <see cref="T:System.Linq.Expressions.Expression" /> 物件的陣列，用於對屬性進行索引。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Property(System.Linq.Expressions.Expression,System.Type,System.String)">
      <summary>建立用於存取屬性的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.MemberExpression" />。</returns>
      <param name="expression">屬性的包含物件。如果是靜態屬性，可以是 null。</param>
      <param name="type">包含屬性的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
      <param name="propertyName">要存取的屬性。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.PropertyOrField(System.Linq.Expressions.Expression,System.String)">
      <summary>建立 <see cref="T:System.Linq.Expressions.MemberExpression" />，代表存取屬性或欄位。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.MemberAccess" />、<see cref="P:System.Linq.Expressions.MemberExpression.Expression" /> 屬性設定為 <paramref name="expression" />，且 <see cref="P:System.Linq.Expressions.MemberExpression.Member" /> 屬性設定為 <see cref="T:System.Reflection.PropertyInfo" /> 或 <see cref="T:System.Reflection.FieldInfo" />，代表 <paramref name="propertyOrFieldName" /> 所表示的屬性或欄位。</returns>
      <param name="expression">
        <see cref="T:System.Linq.Expressions.Expression" />，其 <see cref="P:System.Linq.Expressions.Expression.Type" /> 包含名為 <paramref name="propertyOrFieldName" /> 的屬性或欄位。如果是靜態成員，可以是 Null。</param>
      <param name="propertyOrFieldName">要存取之屬性或欄位的名稱。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 或 <paramref name="propertyOrFieldName" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="propertyOrFieldName" />.Type 或其基底類型中沒有定義名為 <paramref name="expression" /> 的屬性或欄位。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Quote(System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，代表具有 <see cref="T:System.Linq.Expressions.Expression" /> 類型常數值的運算式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Quote" />，而 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 屬性設定為指定的值。</returns>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Reduce">
      <summary>將這個節點精簡為更簡單的運算式。如果 CanReduce 傳回 true，則應該傳回有效的運算式。這個方法可以傳回其他本身必須精簡的節點。</summary>
      <returns>精簡的運算式。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ReduceAndCheck">
      <summary>將這個節點精簡為更簡單的運算式。如果 CanReduce 傳回 true，則應該傳回有效的運算式。這個方法可以傳回其他本身必須精簡的節點。</summary>
      <returns>精簡的運算式。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ReduceExtensions">
      <summary>將運算式精簡為已知的節點類型 (不是 Extension 節點)，如果已經是已知的類型，則僅傳回運算式。</summary>
      <returns>精簡的運算式。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ReferenceEqual(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立表示參考相等比較的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Equal" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ReferenceNotEqual(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立表示參考不相等比較的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.NotEqual" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Rethrow">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，表示重新擲回例外狀況。</summary>
      <returns>表示重新擲回例外狀況的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Rethrow(System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，代表重新擲回含指定之類型的例外狀況。</summary>
      <returns>表示重新擲回例外狀況的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="type">運算式的新 <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Return(System.Linq.Expressions.LabelTarget)">
      <summary>建立 <see cref="T:System.Linq.Expressions.GotoExpression" />，表示 return 陳述式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等於 Return、<see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 屬性設定為 <paramref name="target" />，且包含在跳躍時傳遞至目標標籤的 Null 值。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 將跳躍的目標 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Return(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.GotoExpression" />，表示 return 陳述式。可以指定跳躍時傳遞至標籤的值。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等於 Continue、<see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 屬性設定為 <paramref name="target" />，且，且包含在跳躍時傳遞至目標標籤的 <paramref name="value" />。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 將跳躍的目標 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="value">將在跳躍時傳遞至關聯標籤的值。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Return(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression,System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.GotoExpression" />，表示含指定之類型的 return 陳述式。可以指定跳躍時傳遞至標籤的值。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等於Continue、<see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 屬性設定為 <paramref name="target" />、<see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性設定為 <paramref name="type" />，且包含在跳躍時傳遞至目標標籤的 <paramref name="value" />。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 將跳躍的目標 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="value">將在跳躍時傳遞至關聯標籤的值。</param>
      <param name="type">要將 <see cref="T:System.Type" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Return(System.Linq.Expressions.LabelTarget,System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.GotoExpression" />，表示含指定之類型的 return 陳述式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.GotoExpression" />，其 <see cref="P:System.Linq.Expressions.GotoExpression.Kind" /> 等於 Return、<see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 屬性設定為 <paramref name="target" />、<see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性設定為 <paramref name="type" />，且包含在跳躍時傳遞至目標標籤的 null 值。</returns>
      <param name="target">
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 將跳躍的目標 <see cref="T:System.Linq.Expressions.GotoExpression" />。</param>
      <param name="type">要將 <see cref="T:System.Type" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.RightShift(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立代表位元右移 (Right-Shift) 運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.RightShift" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義 Right-shift 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.RightShift(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立代表位元右移 (Right-Shift) 運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.RightShift" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義 Right-shift 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.RightShiftAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立表示位元右移位指派運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.RightShiftAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.RightShiftAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立表示位元右移位指派運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.RightShiftAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.RightShiftAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>建立表示位元右移位指派運算的 <see cref="T:System.Linq.Expressions.BinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.RightShiftAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要將 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.RuntimeVariables(System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>建立 <see cref="T:System.Linq.Expressions.RuntimeVariablesExpression" /> 的執行個體。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.RuntimeVariablesExpression" /> 的執行個體，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.RuntimeVariables" />，而 <see cref="P:System.Linq.Expressions.RuntimeVariablesExpression.Variables" /> 屬性設定為指定的值。</returns>
      <param name="variables">
        <see cref="T:System.Linq.Expressions.ParameterExpression" /> 物件的集合，用來填入 <see cref="P:System.Linq.Expressions.RuntimeVariablesExpression.Variables" /> 集合。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.RuntimeVariables(System.Linq.Expressions.ParameterExpression[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.RuntimeVariablesExpression" /> 的執行個體。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.RuntimeVariablesExpression" /> 的執行個體，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.RuntimeVariables" />，而 <see cref="P:System.Linq.Expressions.RuntimeVariablesExpression.Variables" /> 屬性設定為指定的值。</returns>
      <param name="variables">用以填入 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 集合之 <see cref="P:System.Linq.Expressions.RuntimeVariablesExpression.Variables" /> 物件的陣列。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Subtract(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其代表不含溢位檢查的算術減法運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Subtract" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義 Subtraction 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Subtract(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其代表不含溢位檢查的算術減法運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.Subtract" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義 Subtraction 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SubtractAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示不含溢位檢查的減法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.SubtractAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SubtractAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示不含溢位檢查的減法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.SubtractAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SubtractAssign(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示不含溢位檢查的減法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.SubtractAssign" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要將 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SubtractAssignChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示包含溢位檢查的減法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.SubtractAssignChecked" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SubtractAssignChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示包含溢位檢查的減法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.SubtractAssignChecked" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SubtractAssignChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.LambdaExpression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，表示包含溢位檢查的減法指派運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.SubtractAssignChecked" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <param name="conversion">要將 <see cref="T:System.Linq.Expressions.LambdaExpression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Conversion" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SubtractChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其代表包含溢位檢查的算術減法運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.SubtractChecked" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義 Subtraction 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SubtractChecked(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立 <see cref="T:System.Linq.Expressions.BinaryExpression" />，其代表包含溢位檢查的算術減法運算。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.BinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.SubtractChecked" />，且 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />、<see cref="P:System.Linq.Expressions.BinaryExpression.Right" /> 和 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="left">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Left" />。</param>
      <param name="right">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Right" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.BinaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="left" /> 或 <paramref name="right" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好兩個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="left" />.Type 和 <paramref name="right" />.Type 定義 Subtraction 運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Switch(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.SwitchCase[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.SwitchExpression" />，其代表具有預設情況的 switch 陳述式。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.SwitchExpression" />。</returns>
      <param name="switchValue">針對每個案例所要測試的值。</param>
      <param name="defaultBody">如果 <paramref name="switchValue" /> 不符合任何案例，則為參數結果。</param>
      <param name="cases">這個 switch 運算式的案例集合。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Switch(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.SwitchCase})">
      <summary>建立 <see cref="T:System.Linq.Expressions.SwitchExpression" />，其代表具有預設情況的 switch 陳述式。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.SwitchExpression" />。</returns>
      <param name="switchValue">針對每個案例所要測試的值。</param>
      <param name="defaultBody">如果 <paramref name="switchValue" /> 不符合任何案例，則為參數結果。</param>
      <param name="comparison">要使用的相等比較方法。</param>
      <param name="cases">這個 switch 運算式的案例集合。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Switch(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.SwitchCase[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.SwitchExpression" />，其代表具有預設情況的 switch 陳述式。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.SwitchExpression" />。</returns>
      <param name="switchValue">針對每個案例所要測試的值。</param>
      <param name="defaultBody">如果 <paramref name="switchValue" /> 不符合任何案例，則為參數結果。</param>
      <param name="comparison">要使用的相等比較方法。</param>
      <param name="cases">這個 switch 運算式的案例集合。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Switch(System.Linq.Expressions.Expression,System.Linq.Expressions.SwitchCase[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.SwitchExpression" />，代表不含 default case 的 switch 陳述式。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.SwitchExpression" />。</returns>
      <param name="switchValue">針對每個案例所要測試的值。</param>
      <param name="cases">這個 switch 運算式的案例集合。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Switch(System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Collections.Generic.IEnumerable{System.Linq.Expressions.SwitchCase})">
      <summary>建立 <see cref="T:System.Linq.Expressions.SwitchExpression" />，其代表具有預設情況的 switch 陳述式。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.SwitchExpression" />。</returns>
      <param name="type">參數的結果類型。</param>
      <param name="switchValue">針對每個案例所要測試的值。</param>
      <param name="defaultBody">如果 <paramref name="switchValue" /> 不符合任何案例，則為參數結果。</param>
      <param name="comparison">要使用的相等比較方法。</param>
      <param name="cases">這個 switch 運算式的案例集合。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Switch(System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Reflection.MethodInfo,System.Linq.Expressions.SwitchCase[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.SwitchExpression" />，其代表具有預設情況的 switch 陳述式。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.SwitchExpression" />。</returns>
      <param name="type">參數的結果類型。</param>
      <param name="switchValue">針對每個案例所要測試的值。</param>
      <param name="defaultBody">如果 <paramref name="switchValue" /> 不符合任何案例，則為參數結果。</param>
      <param name="comparison">要使用的相等比較方法。</param>
      <param name="cases">這個 switch 運算式的案例集合。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SwitchCase(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立要在 <see cref="T:System.Linq.Expressions.SwitchCase" /> 物件中使用的 <see cref="T:System.Linq.Expressions.SwitchExpression" /> 物件。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.SwitchCase" />。</returns>
      <param name="body">案例的主體。</param>
      <param name="testValues">案例的測試值。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SwitchCase(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.SwitchCase" /> 以便用於 <see cref="T:System.Linq.Expressions.SwitchExpression" />。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.SwitchCase" />。</returns>
      <param name="body">案例的主體。</param>
      <param name="testValues">案例的測試值。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SymbolDocument(System.String)">
      <summary>建立 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" /> 的執行個體。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" />，其 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.FileName" /> 屬性設定為指定的值。</returns>
      <param name="fileName">要將 <see cref="T:System.String" /> 設定為與之相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.FileName" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SymbolDocument(System.String,System.Guid)">
      <summary>建立 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" /> 的執行個體。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" />，其 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.FileName" /> 和 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.Language" /> 屬性設定為指定的值。</returns>
      <param name="fileName">要將 <see cref="T:System.String" /> 設定為與之相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.FileName" />。</param>
      <param name="language">要將 <see cref="T:System.Guid" /> 設定為與之相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.Language" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SymbolDocument(System.String,System.Guid,System.Guid)">
      <summary>建立 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" /> 的執行個體。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" />，其 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.FileName" /> 和 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.Language" /> 和 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.LanguageVendor" /> 屬性設定為指定的值。</returns>
      <param name="fileName">要將 <see cref="T:System.String" /> 設定為與之相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.FileName" />。</param>
      <param name="language">要將 <see cref="T:System.Guid" /> 設定為與之相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.Language" />。</param>
      <param name="languageVendor">要將 <see cref="T:System.Guid" /> 設定為與之相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.LanguageVendor" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.SymbolDocument(System.String,System.Guid,System.Guid,System.Guid)">
      <summary>建立 <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" /> 的執行個體。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.SymbolDocumentInfo" />，其 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.FileName" />、<see cref="P:System.Linq.Expressions.SymbolDocumentInfo.Language" />、<see cref="P:System.Linq.Expressions.SymbolDocumentInfo.LanguageVendor" /> 和 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.DocumentType" /> 屬性設定為指定的值。</returns>
      <param name="fileName">要將 <see cref="T:System.String" /> 設定為與之相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.FileName" />。</param>
      <param name="language">要將 <see cref="T:System.Guid" /> 設定為與之相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.Language" />。</param>
      <param name="languageVendor">要將 <see cref="T:System.Guid" /> 設定為與之相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.LanguageVendor" />。</param>
      <param name="documentType">要將 <see cref="T:System.Guid" /> 設定為與之相等的 <see cref="P:System.Linq.Expressions.SymbolDocumentInfo.DocumentType" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Throw(System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，表示擲回例外狀況。</summary>
      <returns>表示例外狀況的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="value">
        <see cref="T:System.Linq.Expressions.Expression" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Throw(System.Linq.Expressions.Expression,System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，代表擲回含指定之類型的例外狀況。</summary>
      <returns>表示例外狀況的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</returns>
      <param name="value">
        <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="type">運算式的新 <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.ToString">
      <summary>傳回 <see cref="T:System.Linq.Expressions.Expression" /> 的文字表示。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" /> 的文字表示。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.TryCatch(System.Linq.Expressions.Expression,System.Linq.Expressions.CatchBlock[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.TryExpression" />，代表含 catch 陳述式 (數目不拘) 但不含 fault 區塊或 finally 區塊的 try 區塊。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.TryExpression" />。</returns>
      <param name="body">try 區塊的主體。</param>
      <param name="handlers">零個或多個 <see cref="T:System.Linq.Expressions.CatchBlock" /> 的陣列，表示要與 try 區塊產生關聯的 catch 陳述式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.TryCatchFinally(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.CatchBlock[])">
      <summary>建立 <see cref="T:System.Linq.Expressions.TryExpression" />，代表含 catch 陳述式 (數目不拘) 和 finally 區塊的 try 區塊。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.TryExpression" />。</returns>
      <param name="body">try 區塊的主體。</param>
      <param name="finally">finally 區塊的主體。</param>
      <param name="handlers">零個或多個 <see cref="T:System.Linq.Expressions.CatchBlock" /> 的陣列，表示要與 try 區塊產生關聯的 catch 陳述式。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.TryFault(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.TryExpression" />，代表含 fault 區塊但不含任何 catch 陳述式的 try 區塊。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.TryExpression" />。</returns>
      <param name="body">try 區塊的主體。</param>
      <param name="fault">fault 區塊的主體。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.TryFinally(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立 <see cref="T:System.Linq.Expressions.TryExpression" />，代表含 finally 區塊但不含任何 catch 陳述式的 try 區塊。</summary>
      <returns>建立的 <see cref="T:System.Linq.Expressions.TryExpression" />。</returns>
      <param name="body">try 區塊的主體。</param>
      <param name="finally">finally 區塊的主體。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.TryGetActionType(System.Type[],System.Type@)">
      <summary>建立 <see cref="P:System.Linq.Expressions.Expression.Type" /> 物件，這個物件代表具有特定類型引數的泛型 System.Action 委派類型。</summary>
      <returns>如果已針對特定 <paramref name="typeArgs" /> 建立泛型 System.Action 委派類型，則傳回 true，否則傳回 false。</returns>
      <param name="typeArgs">Type 物件的陣列，指定 System.Action 委派類型的類型引數。</param>
      <param name="actionType">這個方法在傳回時會包含具有特定類型引數的泛型 System.Action 委派類型。如果沒有符合 <paramref name="typeArgs" /> 的泛型 System.Action 委派，則包含 Null。這個參數以未初始化的狀態傳遞。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.TryGetFuncType(System.Type[],System.Type@)">
      <summary>建立 <see cref="P:System.Linq.Expressions.Expression.Type" /> 物件，這個物件代表具有特定類型引數的泛型 System.Func 委派類型。最後一個類型引數指定已建立之委派的傳回類型。</summary>
      <returns>如果已針對特定 <paramref name="typeArgs" /> 建立泛型 System.Func 委派類型，則傳回 true，否則傳回 false。</returns>
      <param name="typeArgs">Type 物件的陣列，指定 System.Func 委派類型的類型引數。</param>
      <param name="funcType">這個方法在傳回時會包含具有特定類型引數的泛型 System.Func 委派類型。如果沒有符合 <paramref name="typeArgs" /> 的泛型 System.Func 委派，則包含 Null。這個參數以未初始化的狀態傳遞。</param>
    </member>
    <member name="P:System.Linq.Expressions.Expression.Type">
      <summary>取得此 <see cref="T:System.Linq.Expressions.Expression" /> 代表之運算式的靜態類型。</summary>
      <returns>代表運算式靜態類型的 <see cref="T:System.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression.TypeAs(System.Linq.Expressions.Expression,System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，代表明確參考或 Boxing 轉換，其中若轉換失敗，則提供 null。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.TypeAs" />，且 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 和 <see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性設定為指定的值。</returns>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <param name="type">要將 <see cref="T:System.Type" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.Expression.Type" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 或 <paramref name="type" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.TypeEqual(System.Linq.Expressions.Expression,System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.TypeBinaryExpression" /> 以比較執行階段類型識別。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.TypeBinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="M:System.Linq.Expressions.Expression.TypeEqual(System.Linq.Expressions.Expression,System.Type)" /> 以及 <see cref="T:System.Linq.Expressions.Expression" /> 和 <see cref="P:System.Linq.Expressions.TypeBinaryExpression.TypeOperand" /> 屬性設為指定值。</returns>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="type">要將 <see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.TypeBinaryExpression.TypeOperand" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.TypeIs(System.Linq.Expressions.Expression,System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.TypeBinaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.TypeBinaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.TypeIs" /> 以及 <see cref="P:System.Linq.Expressions.TypeBinaryExpression.Expression" /> 和 <see cref="P:System.Linq.Expressions.TypeBinaryExpression.TypeOperand" /> 屬性設為指定值。</returns>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.TypeBinaryExpression.Expression" />。</param>
      <param name="type">要將 <see cref="P:System.Linq.Expressions.Expression.Type" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.TypeBinaryExpression.TypeOperand" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 或 <paramref name="type" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.UnaryPlus(System.Linq.Expressions.Expression)">
      <summary>建立代表一元加法運算的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.UnaryPlus" />，而 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 屬性設定為指定的值。</returns>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">不會為 <paramref name="expression" />.Type 定義一元正運算子。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.UnaryPlus(System.Linq.Expressions.Expression,System.Reflection.MethodInfo)">
      <summary>建立代表一元加法運算的 <see cref="T:System.Linq.Expressions.UnaryExpression" />。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" />，其 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性等於 <see cref="F:System.Linq.Expressions.ExpressionType.UnaryPlus" />，且 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 和 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" /> 屬性設定為指定的值。</returns>
      <param name="expression">要將 <see cref="T:System.Linq.Expressions.Expression" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" />。</param>
      <param name="method">要將 <see cref="T:System.Reflection.MethodInfo" /> 屬性設定為與之相等的 <see cref="P:System.Linq.Expressions.UnaryExpression.Method" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> 不是 null，而且其代表的方法傳回的是 void，不是 static (在 Visual Basic 中為 Shared)，或者使用的引數不是剛好一個。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="method" /> 為 null，且不會為 <paramref name="expression" />.Type 定義一元正運算子。-或-<paramref name="expression" />.Type (如果它是可為 Null 的實質類型，則為其對應之不可為 Null 的類型) 無法指派給 <paramref name="method" /> 所代表之方法的引數類型。</exception>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Unbox(System.Linq.Expressions.Expression,System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.UnaryExpression" />，代表明確 Unboxing。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.UnaryExpression" /> 的執行個體。</returns>
      <param name="expression">要 Unbox 的 <see cref="T:System.Linq.Expressions.Expression" />。</param>
      <param name="type">運算式的新 <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Variable(System.Type)">
      <summary>建立 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 節點，此節點可用以識別運算式樹狀結構中的參數或變數。</summary>
      <returns>建立含指定之名稱和類型的 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 節點。</returns>
      <param name="type">參數或變數的類型。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.Variable(System.Type,System.String)">
      <summary>建立 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 節點，此節點可用以識別運算式樹狀結構中的參數或變數。</summary>
      <returns>建立含指定之名稱和類型的 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 節點。</returns>
      <param name="type">參數或變數的類型。</param>
      <param name="name">參數或變數的名稱。這個名稱僅供偵錯或列印之用。</param>
    </member>
    <member name="M:System.Linq.Expressions.Expression.VisitChildren(System.Linq.Expressions.ExpressionVisitor)">
      <summary>精簡節點，然後呼叫精簡後的運算式上的訪問項委派。如果節點無法縮減，此方法會擲回例外狀況。</summary>
      <returns>受訪的運算式，或是在樹狀結構中應取代該運算式的運算式。</returns>
      <param name="visitor">
        <see cref="T:System.Func`2" /> 的執行個體。</param>
    </member>
    <member name="T:System.Linq.Expressions.Expression`1">
      <summary>將強型別 Lambda 運算式表示為運算式樹狀架構形式的資料結構。此類別無法被繼承。</summary>
      <typeparam name="TDelegate">
        <see cref="T:System.Linq.Expressions.Expression`1" /> 所代表之委派的型別。</typeparam>
    </member>
    <member name="M:System.Linq.Expressions.Expression`1.Compile">
      <summary>將運算式樹狀架構所描述的 Lambda 運算式編譯為可執行程式碼，並產生表示 Lambda 運算式的委派。</summary>
      <returns>
        <paramref name="TDelegate" /> 型別的委派，表示由 <see cref="T:System.Linq.Expressions.Expression`1" /> 所描述的已編譯 Lambda 運算式。</returns>
    </member>
    <member name="M:System.Linq.Expressions.Expression`1.Update(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="body">結果的 <see cref="P:System.Linq.Expressions.LambdaExpression.Body" /> 屬性。</param>
      <param name="parameters">結果的 <see cref="P:System.Linq.Expressions.LambdaExpression.Parameters" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.ExpressionType">
      <summary>說明運算式樹狀結構之節點的節點型別。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Add">
      <summary>數值運算元的加法運算，例如 a + b，不包含溢位檢查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.AddAssign">
      <summary>數值運算元的加法複合指派運算，例如 (a += b)，不包含溢位檢查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.AddAssignChecked">
      <summary>數值運算元的加法複合指派運算，例如 (a += b)，包含溢位檢查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.AddChecked">
      <summary>數值運算元的加法運算，例如 (a + b)，包含溢位檢查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.And">
      <summary>位元或邏輯 AND 運算，例如 C# 中的 (a &amp; b) 和 Visual Basic 中的 (a And b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.AndAlso">
      <summary>條件 AND 運算，此種運算只有在第一個運算元評估為 true 時才會評估第二個運算元。相當於 C# 中的 (a &amp;&amp; b) 和 Visual Basic 中的 (a AndAlso b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.AndAssign">
      <summary>位元或邏輯 AND 複合指派運算，例如 C# 中的 (a &amp;= b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.ArrayIndex">
      <summary>一維陣列中的索引運算，例如 C# 中的 array[index] 或 Visual Basic 中的 array(index)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.ArrayLength">
      <summary>一種運算，用於取得一維陣列的長度，例如 array.Length。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Assign">
      <summary>指派運算，例如 (a = b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Block">
      <summary>運算式的區塊。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Call">
      <summary>方法呼叫，例如在 obj.sampleMethod() 運算式中。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Coalesce">
      <summary>表示 Null 聯合運算的節點，例如 C# 中的 (a ?? b) 或 Visual Basic 中的 If(a, b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Conditional">
      <summary>條件運算，例如 C# 中的 a &gt; b ? a : b 或 Visual Basic 中的 If(a &gt; b, a, b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Constant">
      <summary>常數值。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Convert">
      <summary>轉型或轉換運算，例如 C#中的 (SampleType)obj 或 Visual Basic 中的 CType(obj, SampleType)。對於數值轉換，如果轉換的值對目的型別而言太大，則不會擲回例外狀況。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.ConvertChecked">
      <summary>轉型或轉換運算，例如 C#中的 (SampleType)obj 或 Visual Basic 中的 CType(obj, SampleType)。對於數值轉換，如果轉換的值不符合目的型別，則會擲回例外狀況。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.DebugInfo">
      <summary>偵錯資訊。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Decrement">
      <summary>一元遞減運算，例如 C# 和 Visual Basic 中的 (a - 1)。物件 a 不應該就地修改。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Default">
      <summary>預設值。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Divide">
      <summary>數值運算元的除法運算，例如 (a / b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.DivideAssign">
      <summary>數值運算元的除法複合指派運算，例如 (a /= b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Dynamic">
      <summary>動態運算。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Equal">
      <summary>表示相等比較的節點，例如 C# 中的 (a == b) 或 Visual Basic 中的 (a = b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.ExclusiveOr">
      <summary>位元或邏輯 XOR 運算，例如 C# 中的 (a ^ b) 或 Visual Basic 中的 (a Xor b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.ExclusiveOrAssign">
      <summary>位元或邏輯 XOR 複合指派運算，例如 C# 中的 (a ^= b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Extension">
      <summary>延伸運算式。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Goto">
      <summary>「移至」運算式，例如 C# 中的 goto Label 或 Visual Basic 中的 GoTo Label。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.GreaterThan">
      <summary>「大於」比較，例如 (a &gt; b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.GreaterThanOrEqual">
      <summary>「大於或等於」比較，例如 (a &gt;= b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Increment">
      <summary>一元遞增運算，例如 C# 和 Visual Basic 中的 (a + 1)。物件 a 不應該就地修改。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Index">
      <summary>索引運算或是用於存取採用引數之屬性的運算。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Invoke">
      <summary>sampleDelegate.Invoke() 之類的運算式，會叫用委派或 Lambda 運算式。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.IsFalse">
      <summary>false 條件值。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.IsTrue">
      <summary>true 條件值。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Label">
      <summary>標籤。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Lambda">
      <summary>Lambda 運算式，例如 C# 中的 a =&gt; a + a 或 Visual Basic 中的 Function(a) a + a。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.LeftShift">
      <summary>位元左移運算，例如 (a &lt;&lt; b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.LeftShiftAssign">
      <summary>位元左移複合指派，例如 (a &lt;&lt;= b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.LessThan">
      <summary>「小於」比較，例如 (a &lt; b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.LessThanOrEqual">
      <summary>「小於或等於」比較，例如 (a &lt;= b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.ListInit">
      <summary>一種運算，用於建立新的 <see cref="T:System.Collections.IEnumerable" /> 物件並以項目清單，例如 C# 中的 new List&lt;SampleType&gt;(){ a, b, c } 或 Visual Basic 中的 Dim sampleList = { a, b, c }。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Loop">
      <summary>迴圈，例如 for 或 while。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.MemberAccess">
      <summary>obj.SampleProperty 之類會讀取欄位或屬性的運算。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.MemberInit">
      <summary>一種運算，用於建立新的物件，並初始化其一個或多個成員，例如 C# 中的 new Point { X = 1, Y = 2 } 或 Visual Basic 中的 New Point With {.X = 1, .Y = 2}。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Modulo">
      <summary>算術餘數運算，例如 C# 中的 (a % b) 或 Visual Basic 中的 (a Mod b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.ModuloAssign">
      <summary>算術餘數複合指派運算，例如 C# 中的 (a %= b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Multiply">
      <summary>數值運算元的乘法運算，例如 (a * b)，不包含溢位檢查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.MultiplyAssign">
      <summary>數值運算元的乘法複合指派運算，例如 (a *= b)，不包含溢位檢查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.MultiplyAssignChecked">
      <summary>數值運算元的乘法複合指派運算，例如 (a *= b)，具有溢位檢查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.MultiplyChecked">
      <summary>數值運算元的乘法運算，例如 (a * b)，具有溢位檢查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Negate">
      <summary>算術負數運算，例如 (-a)。物件 a 不應該就地修改。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.NegateChecked">
      <summary>算術負數運算，例如 (-a)，具有溢位檢查。物件 a 不應該就地修改。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.New">
      <summary>一種運算，用於呼叫建構函式以建立新物件，例如 new SampleType()。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.NewArrayBounds">
      <summary>一種運算，用於建立新陣列並指定每個維度的界限，例如 C# 中的 new SampleType[dim1, dim2] 或 Visual Basic 中的 New SampleType(dim1, dim2)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.NewArrayInit">
      <summary>一種運算，用於建立新的一維陣列並以項目清單加以初始化，例如 C# 中的 new SampleType[]{a, b, c} 或 Visual Basic 中的 New SampleType(){a, b, c}。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Not">
      <summary>位元補數或邏輯負數運算。在 C# 中，這相當於 (~a) (適用於整數類資料型別) 和 (!a) (適用於布林值)。在 Visual Basic 中，則相當於 (Not a)。物件 a 不應該就地修改。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.NotEqual">
      <summary>不相等比較，例如 C# 中的 (a != b) 或 Visual Basic 中的 (a &lt;&gt; b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.OnesComplement">
      <summary>1 的補數運算，例如 C# 中的 (~a)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Or">
      <summary>位元或邏輯 OR 運算，例如 C# 中的 (a | b) 或 Visual Basic 中的 (a Or b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.OrAssign">
      <summary>位元或邏輯 OR 複合指派，例如 C# 中的 (a |= b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.OrElse">
      <summary>最少運算 (Short-Circuiting) 條件 OR 運算，例如 C# 中的 (a || b) 或 Visual Basic 中的 (a OrElse b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Parameter">
      <summary>在運算式內容中定義之參數或變數的參考。如需詳細資訊，請參閱<see cref="T:System.Linq.Expressions.ParameterExpression" />。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.PostDecrementAssign">
      <summary>一元後置遞減，例如 (a--)。物件 a 應該就地修改。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.PostIncrementAssign">
      <summary>一元後置遞增，例如 (a++)。物件 a 應該就地修改。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Power">
      <summary>一種數學運算，可將數字提升為乘冪數，例如 Visual Basic 中的 (a ^ b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.PowerAssign">
      <summary>一種複合指派運算，可將數字提升為乘冪數，例如 Visual Basic 中的 (a ^= b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.PreDecrementAssign">
      <summary>一元前置遞減，例如 (--a)。物件 a 應該就地修改。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.PreIncrementAssign">
      <summary>一元前置遞增，例如 (++a)。物件 a 應該就地修改。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Quote">
      <summary>有 <see cref="T:System.Linq.Expressions.Expression" /> 型別之常數值的運算式。<see cref="F:System.Linq.Expressions.ExpressionType.Quote" /> 節點可包含參數的參考，這些參數都定義在節點所表示的運算式內容中。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.RightShift">
      <summary>位元右移運算，例如 (a &gt;&gt; b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.RightShiftAssign">
      <summary>位元右移複合指派運算，例如 (a &gt;&gt;= b)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.RuntimeVariables">
      <summary>執行階段變數的清單。如需詳細資訊，請參閱<see cref="T:System.Linq.Expressions.RuntimeVariablesExpression" />。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Subtract">
      <summary>數值運算元的減法運算，例如 (a - b)，不包含溢位檢查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.SubtractAssign">
      <summary>數值運算元的減法複合指派運算，例如 (a -= b)，不包含溢位檢查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.SubtractAssignChecked">
      <summary>數值運算元的減法複合指派運算，例如 (a -= b)，具有溢位檢查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.SubtractChecked">
      <summary>數值運算元的算術減法運算，例如 (a - b)，具有溢位檢查。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Switch">
      <summary>切換運算，例如 C# 中的 switch 或 Visual Basic 中的 Select Case。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Throw">
      <summary>throw new Exception() 之類會擲回例外狀況的運算。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Try">
      <summary>try-catch 運算式。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.TypeAs">
      <summary>明確參考或 Boxing 轉換，轉換若失敗，則提供 null，例如 C# 中的 (obj as SampleType) 或 Visual Basic 中的 TryCast(obj, SampleType)。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.TypeEqual">
      <summary>確切型別測試。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.TypeIs">
      <summary>型別測試，例如 C# 中的 obj is SampleType 或 Visual Basic 中的 TypeOf obj is SampleType。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.UnaryPlus">
      <summary>一元正運算，例如 (+a)。預先定義之一元正運算的結果是運算元的值，但使用者定義的實作可能會有不尋常的結果。</summary>
    </member>
    <member name="F:System.Linq.Expressions.ExpressionType.Unbox">
      <summary>Unbox 值型別運算，例如 MSIL 中的 unbox 和 unbox.any 指令。</summary>
    </member>
    <member name="T:System.Linq.Expressions.ExpressionVisitor">
      <summary>代表運算式樹狀架構的造訪者或重新寫入器。</summary>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.#ctor">
      <summary>初始化 <see cref="T:System.Linq.Expressions.ExpressionVisitor" /> 的新執行個體。</summary>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.Visit(System.Collections.ObjectModel.ReadOnlyCollection{System.Linq.Expressions.Expression})">
      <summary>將運算式清單分派給此類別中較為特殊的造訪方法之一。</summary>
      <returns>如果任何項目已經修改，則傳回修改的運算式清單，否則傳回原始運算式清單。</returns>
      <param name="nodes">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.Visit``1(System.Collections.ObjectModel.ReadOnlyCollection{``0},System.Func{``0,``0})">
      <summary>使用特殊的項目造訪者，造訪集合中所有的節點。</summary>
      <returns>如果任何項目已經修改，則傳回修改的節點清單，否則傳回原始節點清單。</returns>
      <param name="nodes">要造訪的節點。</param>
      <param name="elementVisitor">造訪單一項目的委派，並選擇性地將它取代為新項目。</param>
      <typeparam name="T">節點的型別。</typeparam>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.Visit(System.Linq.Expressions.Expression)">
      <summary>將運算式分派給此類別中較為特殊的造訪方法之一。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitAndConvert``1(System.Collections.ObjectModel.ReadOnlyCollection{``0},System.String)">
      <summary>造訪運算式，並將結果轉換回原始運算式型別。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="nodes">要造訪的運算式。</param>
      <param name="callerName">呼叫方法的名稱，用來回報較佳的錯誤訊息。</param>
      <typeparam name="T">運算式的型別。</typeparam>
      <exception cref="T:System.InvalidOperationException">這個節點的造訪方法傳回不同的型別。</exception>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitAndConvert``1(``0,System.String)">
      <summary>造訪運算式，並將結果轉換回原始運算式型別。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
      <param name="callerName">呼叫方法的名稱，用來回報較佳的錯誤訊息。</param>
      <typeparam name="T">運算式的型別。</typeparam>
      <exception cref="T:System.InvalidOperationException">這個節點的造訪方法傳回不同的型別。</exception>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitBinary(System.Linq.Expressions.BinaryExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.BinaryExpression" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitBlock(System.Linq.Expressions.BlockExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.BlockExpression" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitCatchBlock(System.Linq.Expressions.CatchBlock)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.CatchBlock" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitConditional(System.Linq.Expressions.ConditionalExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.ConditionalExpression" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitConstant(System.Linq.Expressions.ConstantExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.ConstantExpression" />。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitDebugInfo(System.Linq.Expressions.DebugInfoExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.DebugInfoExpression" />。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitDefault(System.Linq.Expressions.DefaultExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.DefaultExpression" />。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitElementInit(System.Linq.Expressions.ElementInit)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.ElementInit" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitExtension(System.Linq.Expressions.Expression)">
      <summary>造訪延伸運算式的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitGoto(System.Linq.Expressions.GotoExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.GotoExpression" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitIndex(System.Linq.Expressions.IndexExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.IndexExpression" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitInvocation(System.Linq.Expressions.InvocationExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.InvocationExpression" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitLabel(System.Linq.Expressions.LabelExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.LabelExpression" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitLabelTarget(System.Linq.Expressions.LabelTarget)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.LabelTarget" />。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitLambda``1(System.Linq.Expressions.Expression{``0})">
      <summary>造訪 <see cref="T:System.Linq.Expressions.Expression`1" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
      <typeparam name="T">委派的型別。</typeparam>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitListInit(System.Linq.Expressions.ListInitExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.ListInitExpression" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitLoop(System.Linq.Expressions.LoopExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.LoopExpression" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitMember(System.Linq.Expressions.MemberExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.MemberExpression" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(System.Linq.Expressions.MemberAssignment)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.MemberAssignment" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(System.Linq.Expressions.MemberBinding)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.MemberBinding" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(System.Linq.Expressions.MemberInitExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.MemberInitExpression" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitMemberListBinding(System.Linq.Expressions.MemberListBinding)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.MemberListBinding" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitMemberMemberBinding(System.Linq.Expressions.MemberMemberBinding)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.MemberMemberBinding" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.MethodCallExpression" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitNew(System.Linq.Expressions.NewExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.NewExpression" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitNewArray(System.Linq.Expressions.NewArrayExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.NewArrayExpression" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitParameter(System.Linq.Expressions.ParameterExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.ParameterExpression" />。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitRuntimeVariables(System.Linq.Expressions.RuntimeVariablesExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.RuntimeVariablesExpression" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitSwitch(System.Linq.Expressions.SwitchExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.SwitchExpression" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitSwitchCase(System.Linq.Expressions.SwitchCase)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.SwitchCase" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitTry(System.Linq.Expressions.TryExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.TryExpression" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitTypeBinary(System.Linq.Expressions.TypeBinaryExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.TypeBinaryExpression" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="M:System.Linq.Expressions.ExpressionVisitor.VisitUnary(System.Linq.Expressions.UnaryExpression)">
      <summary>造訪 <see cref="T:System.Linq.Expressions.UnaryExpression" /> 的子系。</summary>
      <returns>如果運算式本身或是任一子運算式已經修正，會傳回修正的運算式，否則傳回原始運算式。</returns>
      <param name="node">要造訪的運算式。</param>
    </member>
    <member name="T:System.Linq.Expressions.GotoExpression">
      <summary>表示無條件跳躍。這包含 return 陳述式、break 和 continue 陳述式，以及其他跳躍。</summary>
    </member>
    <member name="P:System.Linq.Expressions.GotoExpression.Kind">
      <summary>「移至」運算式的類型。只做為資訊參考之用。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.GotoExpressionKind" /> 物件，表示「移至」運算式的類型。</returns>
    </member>
    <member name="P:System.Linq.Expressions.GotoExpression.NodeType">
      <summary>傳回這個 <see cref="T:System.Linq.Expressions.Expression" /> 的節點型別。</summary>
      <returns>表示這個運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.GotoExpression.Target">
      <summary>這個節點跳至的目標標籤。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 物件，表示這個節點的目標標籤。</returns>
    </member>
    <member name="P:System.Linq.Expressions.GotoExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.GotoExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="M:System.Linq.Expressions.GotoExpression.Update(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression)">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="target">結果的 <see cref="P:System.Linq.Expressions.GotoExpression.Target" /> 屬性。</param>
      <param name="value">結果的 <see cref="P:System.Linq.Expressions.GotoExpression.Value" /> 屬性。</param>
    </member>
    <member name="P:System.Linq.Expressions.GotoExpression.Value">
      <summary>傳遞給目標的值，如果目標為 System.Void 型別則為 null。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" /> 物件，表示傳遞給目標的值，或者是 null。</returns>
    </member>
    <member name="T:System.Linq.Expressions.GotoExpressionKind">
      <summary>指定這個 <see cref="T:System.Linq.Expressions.GotoExpression" /> 所代表的跳躍類型。</summary>
    </member>
    <member name="F:System.Linq.Expressions.GotoExpressionKind.Break">
      <summary>表示 break 陳述式的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</summary>
    </member>
    <member name="F:System.Linq.Expressions.GotoExpressionKind.Continue">
      <summary>表示 continue 陳述式的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</summary>
    </member>
    <member name="F:System.Linq.Expressions.GotoExpressionKind.Goto">
      <summary>
        <see cref="T:System.Linq.Expressions.GotoExpression" />，表示跳至某個位置。</summary>
    </member>
    <member name="F:System.Linq.Expressions.GotoExpressionKind.Return">
      <summary>表示 return 陳述式的 <see cref="T:System.Linq.Expressions.GotoExpression" />。</summary>
    </member>
    <member name="T:System.Linq.Expressions.IArgumentProvider"></member>
    <member name="P:System.Linq.Expressions.IArgumentProvider.ArgumentCount"></member>
    <member name="M:System.Linq.Expressions.IArgumentProvider.GetArgument(System.Int32)"></member>
    <member name="T:System.Linq.Expressions.IDynamicExpression"></member>
    <member name="M:System.Linq.Expressions.IDynamicExpression.CreateCallSite"></member>
    <member name="P:System.Linq.Expressions.IDynamicExpression.DelegateType"></member>
    <member name="M:System.Linq.Expressions.IDynamicExpression.Rewrite(System.Linq.Expressions.Expression[])"></member>
    <member name="T:System.Linq.Expressions.IndexExpression">
      <summary>表示對屬性或陣列進行索引。</summary>
    </member>
    <member name="P:System.Linq.Expressions.IndexExpression.Arguments">
      <summary>取得引數，這些引數將做為屬性或陣列的索引。</summary>
      <returns>唯讀集合，包含將做為屬性或陣列索引的引數。</returns>
    </member>
    <member name="P:System.Linq.Expressions.IndexExpression.Indexer">
      <summary>如果運算式表示索引屬性，則取得屬性的 <see cref="T:System.Reflection.PropertyInfo" />，否則傳回 null。</summary>
      <returns>如果運算式表示索引屬性，則為屬性的 <see cref="T:System.Reflection.PropertyInfo" />，否則為 null。</returns>
    </member>
    <member name="P:System.Linq.Expressions.IndexExpression.NodeType">
      <summary>傳回這個 <see cref="T:System.Linq.Expressions.Expression" /> 的節點型別。</summary>
      <returns>表示這個運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.IndexExpression.Object">
      <summary>要進行索引的物件。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" />，表示要進行索引的物件。</returns>
    </member>
    <member name="P:System.Linq.Expressions.IndexExpression.System#Linq#Expressions#IArgumentProvider#ArgumentCount"></member>
    <member name="M:System.Linq.Expressions.IndexExpression.System#Linq#Expressions#IArgumentProvider#GetArgument(System.Int32)"></member>
    <member name="P:System.Linq.Expressions.IndexExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.IndexExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="M:System.Linq.Expressions.IndexExpression.Update(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="object">結果的 <see cref="P:System.Linq.Expressions.IndexExpression.Object" /> 屬性。</param>
      <param name="arguments">結果的 <see cref="P:System.Linq.Expressions.IndexExpression.Arguments" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.InvocationExpression">
      <summary>表示將委派或 lambda 運算式套用至引數運算式清單的運算式。</summary>
    </member>
    <member name="P:System.Linq.Expressions.InvocationExpression.Arguments">
      <summary>取得要套用委派或 Lambda 運算式的引數。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" /> 物件的 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />，表示要套用委派的引數。</returns>
    </member>
    <member name="P:System.Linq.Expressions.InvocationExpression.Expression">
      <summary>取得要套用的委派或 lambda 運算式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" />，表示要套用的委派。</returns>
    </member>
    <member name="P:System.Linq.Expressions.InvocationExpression.NodeType">
      <summary>傳回這個運算式的節點型別。延伸節點覆寫這個方法時應該傳回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.InvocationExpression.System#Linq#Expressions#IArgumentProvider#ArgumentCount"></member>
    <member name="M:System.Linq.Expressions.InvocationExpression.System#Linq#Expressions#IArgumentProvider#GetArgument(System.Int32)"></member>
    <member name="P:System.Linq.Expressions.InvocationExpression.Type">
      <summary>取得 <see cref="P:System.Linq.Expressions.InvocationExpression.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.InvocationExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="M:System.Linq.Expressions.InvocationExpression.Update(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="expression">結果的 <see cref="P:System.Linq.Expressions.InvocationExpression.Expression" /> 屬性。</param>
      <param name="arguments">結果的 <see cref="P:System.Linq.Expressions.InvocationExpression.Arguments" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.LabelExpression">
      <summary>表示可放在任何 <see cref="T:System.Linq.Expressions.Expression" /> 內容中的標籤。如果跳至標籤，它會取得對應的 <see cref="T:System.Linq.Expressions.GotoExpression" /> 所提供的值。否則它會接收 <see cref="P:System.Linq.Expressions.LabelExpression.DefaultValue" /> 中的值。如果 <see cref="T:System.Type" /> 等於 System.Void，則不應提供任何值。</summary>
    </member>
    <member name="P:System.Linq.Expressions.LabelExpression.DefaultValue">
      <summary>透過一般控制流程到達標籤 (例如，不是跳至標籤) 時，則為 <see cref="T:System.Linq.Expressions.LabelExpression" /> 的值。</summary>
      <returns>運算式物件，表示 <see cref="T:System.Linq.Expressions.LabelExpression" /> 的值。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LabelExpression.NodeType">
      <summary>傳回這個 <see cref="T:System.Linq.Expressions.Expression" /> 的節點型別。</summary>
      <returns>表示這個運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LabelExpression.Target">
      <summary>與這個標籤相關聯的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</summary>
      <returns>與這個標籤相關聯的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LabelExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.LabelExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="M:System.Linq.Expressions.LabelExpression.Update(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression)">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="target">結果的 <see cref="P:System.Linq.Expressions.LabelExpression.Target" /> 屬性。</param>
      <param name="defaultValue">結果的 <see cref="P:System.Linq.Expressions.LabelExpression.DefaultValue" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.LabelTarget">
      <summary>用來表示 <see cref="T:System.Linq.Expressions.GotoExpression" /> 的目標。</summary>
    </member>
    <member name="P:System.Linq.Expressions.LabelTarget.Name">
      <summary>取得標籤的名稱。</summary>
      <returns>標記名稱。</returns>
    </member>
    <member name="M:System.Linq.Expressions.LabelTarget.ToString">
      <summary>傳回 <see cref="T:System.String" />，表示目前的 <see cref="T:System.Object" />。</summary>
      <returns>
        <see cref="T:System.String" />，表示目前的 <see cref="T:System.Object" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LabelTarget.Type">
      <summary>跳至標籤時所傳遞值的型別 (如果不應傳遞任何值則為 <see cref="T:System.Void" />)。</summary>
      <returns>
        <see cref="T:System.Type" /> 物件，表示跳至標籤時所傳遞值的型別，如果不應傳遞任何值則為 <see cref="T:System.Void" />。</returns>
    </member>
    <member name="T:System.Linq.Expressions.LambdaExpression">
      <summary>說明 Lambda 運算式。這會擷取類似於 .NET 方法主體的程式碼區塊。</summary>
    </member>
    <member name="P:System.Linq.Expressions.LambdaExpression.Body">
      <summary>取得 Lambda 運算式的主體。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" />，表示 Lambda 運算式的主體。</returns>
    </member>
    <member name="M:System.Linq.Expressions.LambdaExpression.Compile">
      <summary>產生代表 Lambda 運算式的委派。</summary>
      <returns>
        <see cref="T:System.Delegate" />，包含編譯後的 Lambda 運算式版本。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LambdaExpression.Name">
      <summary>取得 Lambda 運算式的名稱。</summary>
      <returns>Lambda 運算式的名稱。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LambdaExpression.NodeType">
      <summary>傳回這個 <see cref="T:System.Linq.Expressions.Expression" /> 的節點型別。</summary>
      <returns>表示這個運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LambdaExpression.Parameters">
      <summary>取得 Lambda 運算式的參數。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ParameterExpression" /> 物件的 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />，代表 Lambda 運算式的參數。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LambdaExpression.ReturnType">
      <summary>取得 Lambda 運算式的傳回型別。</summary>
      <returns>
        <see cref="T:System.Type" /> 物件，表示 Lambda 運算式的型別。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LambdaExpression.TailCall">
      <summary>取得值，這個值表示 Lambda 運算式是否會以 tail 呼叫最佳化來編譯。</summary>
      <returns>如果 Lambda 運算式會以 tail 呼叫最佳化來編譯，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LambdaExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.LambdaExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="T:System.Linq.Expressions.ListInitExpression">
      <summary>表示具有集合初始設定式的建構函式呼叫。</summary>
    </member>
    <member name="P:System.Linq.Expressions.ListInitExpression.CanReduce">
      <summary>取得值，指出是否可以減少運算式樹狀架構的節點。</summary>
      <returns>如果節點可以精簡則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ListInitExpression.Initializers">
      <summary>取得可用來初始化集合的項目初始化設定式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ElementInit" /> 物件的 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />，表示用於初始化集合的項目。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ListInitExpression.NewExpression">
      <summary>取得運算式，這個運算式包含對集合型別之建構函式的呼叫。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.NewExpression" />，表示對集合型別之建構函式的呼叫。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ListInitExpression.NodeType">
      <summary>傳回這個 <see cref="T:System.Linq.Expressions.Expression" /> 的節點型別。</summary>
      <returns>表示這個運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.ListInitExpression.Reduce">
      <summary>將二進位運算式節點精簡為更簡單的運算式。</summary>
      <returns>精簡的運算式。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ListInitExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.ListInitExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="M:System.Linq.Expressions.ListInitExpression.Update(System.Linq.Expressions.NewExpression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.ElementInit})">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="newExpression">結果的 <see cref="P:System.Linq.Expressions.ListInitExpression.NewExpression" /> 屬性。</param>
      <param name="initializers">結果的 <see cref="P:System.Linq.Expressions.ListInitExpression.Initializers" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.LoopExpression">
      <summary>表示無限迴圈。可使用「中斷」結束它。</summary>
    </member>
    <member name="P:System.Linq.Expressions.LoopExpression.Body">
      <summary>取得做為迴圈主體的 <see cref="T:System.Linq.Expressions.Expression" />。</summary>
      <returns>做為迴圈主體的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LoopExpression.BreakLabel">
      <summary>取得迴圈主體做為 break 陳述式目標的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</summary>
      <returns>迴圈主體做為 break 陳述式目標的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LoopExpression.ContinueLabel">
      <summary>取得迴圈主體做為 continue 陳述式目標的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</summary>
      <returns>迴圈主體做為 continue 陳述式目標的 <see cref="T:System.Linq.Expressions.LabelTarget" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LoopExpression.NodeType">
      <summary>傳回這個運算式的節點型別。延伸節點覆寫這個方法時應該傳回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.LoopExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.LoopExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="M:System.Linq.Expressions.LoopExpression.Update(System.Linq.Expressions.LabelTarget,System.Linq.Expressions.LabelTarget,System.Linq.Expressions.Expression)">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="breakLabel">結果的 <see cref="P:System.Linq.Expressions.LoopExpression.BreakLabel" /> 屬性。</param>
      <param name="continueLabel">結果的 <see cref="P:System.Linq.Expressions.LoopExpression.ContinueLabel" /> 屬性。</param>
      <param name="body">結果的 <see cref="P:System.Linq.Expressions.LoopExpression.Body" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.MemberAssignment">
      <summary>表示物件之欄位或屬性的指派運算。</summary>
    </member>
    <member name="P:System.Linq.Expressions.MemberAssignment.Expression">
      <summary>取得要指派到欄位或屬性的運算式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" />，表示要指派給欄位或屬性的值。</returns>
    </member>
    <member name="M:System.Linq.Expressions.MemberAssignment.Update(System.Linq.Expressions.Expression)">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="expression">結果的 <see cref="P:System.Linq.Expressions.MemberAssignment.Expression" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.MemberBinding">
      <summary>提供基底類別，從其中衍生的類別表示用於初始化新建立物件的繫結。</summary>
    </member>
    <member name="P:System.Linq.Expressions.MemberBinding.BindingType">
      <summary>取得所表示之繫結的型別。</summary>
      <returns>其中一個 <see cref="T:System.Linq.Expressions.MemberBindingType" /> 值。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MemberBinding.Member">
      <summary>取得要初始化的欄位或屬性。</summary>
      <returns>
        <see cref="T:System.Reflection.MemberInfo" />，表示要初始化的欄位或屬性。</returns>
    </member>
    <member name="M:System.Linq.Expressions.MemberBinding.ToString">
      <summary>傳回 <see cref="T:System.Linq.Expressions.MemberBinding" /> 的文字表示。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberBinding" /> 的文字表示。</returns>
    </member>
    <member name="T:System.Linq.Expressions.MemberBindingType">
      <summary>描述用於 <see cref="T:System.Linq.Expressions.MemberInitExpression" /> 物件中的繫結型別。</summary>
    </member>
    <member name="F:System.Linq.Expressions.MemberBindingType.Assignment">
      <summary>表示以運算式的值來初始化成員的繫結。</summary>
    </member>
    <member name="F:System.Linq.Expressions.MemberBindingType.ListBinding">
      <summary>表示初始化項目清單中 <see cref="T:System.Collections.IList" /> 或 <see cref="T:System.Collections.Generic.ICollection`1" /> 型別之成員的繫結。</summary>
    </member>
    <member name="F:System.Linq.Expressions.MemberBindingType.MemberBinding">
      <summary>表示遞迴初始化成員之成員的繫結。</summary>
    </member>
    <member name="T:System.Linq.Expressions.MemberExpression">
      <summary>表示存取欄位或屬性。</summary>
    </member>
    <member name="M:System.Linq.Expressions.MemberExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>分派給這個節點類型的特定 visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 會呼叫 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>瀏覽這個節點的結果。</returns>
      <param name="visitor">瀏覽這個節點的造訪者。</param>
    </member>
    <member name="P:System.Linq.Expressions.MemberExpression.Expression">
      <summary>取得欄位或屬性的包含物件。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" />，表示欄位或屬性的包含物件。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MemberExpression.Member">
      <summary>取得要存取的欄位或屬性。</summary>
      <returns>
        <see cref="T:System.Reflection.MemberInfo" />，表示要存取的欄位或屬性。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MemberExpression.NodeType">
      <summary>傳回這個 <see cref="P:System.Linq.Expressions.MemberExpression.Expression" /> 的節點型別。</summary>
      <returns>表示這個運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.MemberExpression.Update(System.Linq.Expressions.Expression)">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="expression">結果的 <see cref="P:System.Linq.Expressions.MemberExpression.Expression" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.MemberInitExpression">
      <summary>表示呼叫建構函式，並初始化新物件的一個或多個成員。</summary>
    </member>
    <member name="P:System.Linq.Expressions.MemberInitExpression.Bindings">
      <summary>取得繫結，這個繫結說明如何初始化新建立物件的成員。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberBinding" /> 物件的 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />，說明如何初始化成員。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MemberInitExpression.CanReduce">
      <summary>取得值，指出是否可以減少運算式樹狀架構的節點。</summary>
      <returns>如果節點可以精簡則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MemberInitExpression.NewExpression">
      <summary>取得表示建構函式呼叫的運算式。</summary>
      <returns>表示建構函式呼叫的 <see cref="T:System.Linq.Expressions.NewExpression" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MemberInitExpression.NodeType">
      <summary>傳回這個 Expression 的節點型別。延伸節點覆寫這個方法時應該傳回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.MemberInitExpression.Reduce">
      <summary>將 <see cref="T:System.Linq.Expressions.MemberInitExpression" /> 精簡為更簡單的運算式。</summary>
      <returns>精簡的運算式。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MemberInitExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.MemberInitExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="M:System.Linq.Expressions.MemberInitExpression.Update(System.Linq.Expressions.NewExpression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.MemberBinding})">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="newExpression">結果的 <see cref="P:System.Linq.Expressions.MemberInitExpression.NewExpression" /> 屬性。</param>
      <param name="bindings">結果的 <see cref="P:System.Linq.Expressions.MemberInitExpression.Bindings" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.MemberListBinding">
      <summary>表示初始化新建立物件之集合成員的項目。</summary>
    </member>
    <member name="P:System.Linq.Expressions.MemberListBinding.Initializers">
      <summary>取得初始化新建立物件之集合成員的初始設定式。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.ElementInit" /> 物件的 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />，用來初始化集合。</returns>
    </member>
    <member name="M:System.Linq.Expressions.MemberListBinding.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.ElementInit})">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="initializers">結果的 <see cref="P:System.Linq.Expressions.MemberListBinding.Initializers" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.MemberMemberBinding">
      <summary>表示初始化新建立物件之成員的成員。</summary>
    </member>
    <member name="P:System.Linq.Expressions.MemberMemberBinding.Bindings">
      <summary>取得繫結，這個繫結說明如何初始化成員的成員。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.MemberBinding" /> 物件的 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />，說明初始化成員的成員。</returns>
    </member>
    <member name="M:System.Linq.Expressions.MemberMemberBinding.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.MemberBinding})">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="bindings">結果的 <see cref="P:System.Linq.Expressions.MemberMemberBinding.Bindings" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.MethodCallExpression">
      <summary>表示呼叫靜態或執行個體方法。</summary>
    </member>
    <member name="M:System.Linq.Expressions.MethodCallExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>分派給這個節點類型的特定 visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 會呼叫 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>瀏覽這個節點的結果。</returns>
      <param name="visitor">瀏覽這個節點的造訪者。</param>
    </member>
    <member name="P:System.Linq.Expressions.MethodCallExpression.Arguments">
      <summary>取得運算式的集合，這些運算式表示所呼叫方法的引數。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" /> 物件的 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />，表示已呼叫方法的引數。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MethodCallExpression.Method">
      <summary>取得要呼叫之方法的 <see cref="T:System.Reflection.MethodInfo" />。</summary>
      <returns>
        <see cref="T:System.Reflection.MethodInfo" />，表示已呼叫方法。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MethodCallExpression.NodeType">
      <summary>傳回這個 <see cref="T:System.Linq.Expressions.Expression" /> 的節點型別。</summary>
      <returns>表示這個運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MethodCallExpression.Object">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" />，表示執行個體 (適用於執行個體方法呼叫) 或 Null (適用於靜態方法呼叫)。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" />，表示方法的接收物件。</returns>
    </member>
    <member name="P:System.Linq.Expressions.MethodCallExpression.System#Linq#Expressions#IArgumentProvider#ArgumentCount"></member>
    <member name="M:System.Linq.Expressions.MethodCallExpression.System#Linq#Expressions#IArgumentProvider#GetArgument(System.Int32)"></member>
    <member name="P:System.Linq.Expressions.MethodCallExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.MethodCallExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="M:System.Linq.Expressions.MethodCallExpression.Update(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="object">結果的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Object" /> 屬性。</param>
      <param name="arguments">結果的 <see cref="P:System.Linq.Expressions.MethodCallExpression.Arguments" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.NewArrayExpression">
      <summary>表示建立新陣列，並可能初始化新陣列的項目。</summary>
    </member>
    <member name="M:System.Linq.Expressions.NewArrayExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>分派給這個節點類型的特定 visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 會呼叫 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>瀏覽這個節點的結果。</returns>
      <param name="visitor">瀏覽這個節點的造訪者。</param>
    </member>
    <member name="P:System.Linq.Expressions.NewArrayExpression.Expressions">
      <summary>如果 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性的值是 <see cref="F:System.Linq.Expressions.ExpressionType.NewArrayBounds" />，則取得陣列的界限，如果 <see cref="P:System.Linq.Expressions.Expression.NodeType" /> 屬性的值是 <see cref="F:System.Linq.Expressions.ExpressionType.NewArrayInit" />，則取得值以初始化新陣列的項目。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" /> 物件的 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />，表示陣列的界限或初始化值。</returns>
    </member>
    <member name="P:System.Linq.Expressions.NewArrayExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.NewArrayExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="M:System.Linq.Expressions.NewArrayExpression.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="expressions">結果的 <see cref="P:System.Linq.Expressions.NewArrayExpression.Expressions" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.NewExpression">
      <summary>表示建構函式呼叫。</summary>
    </member>
    <member name="M:System.Linq.Expressions.NewExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>分派給這個節點類型的特定 visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 會呼叫 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>瀏覽這個節點的結果。</returns>
      <param name="visitor">瀏覽這個節點的造訪者。</param>
    </member>
    <member name="P:System.Linq.Expressions.NewExpression.Arguments">
      <summary>取得建構函式的引數。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" /> 物件的集合，表示建構函式的引數。</returns>
    </member>
    <member name="P:System.Linq.Expressions.NewExpression.Constructor">
      <summary>取得呼叫的建構函式。</summary>
      <returns>
        <see cref="T:System.Reflection.ConstructorInfo" />，表示呼叫的建構函式。</returns>
    </member>
    <member name="P:System.Linq.Expressions.NewExpression.Members">
      <summary>取得可擷取以建構函式引數初始化之欄位值的成員。</summary>
      <returns>
        <see cref="T:System.Reflection.MemberInfo" /> 物件的集合，表示可擷取以建構函式引數初始化之欄位值的成員。</returns>
    </member>
    <member name="P:System.Linq.Expressions.NewExpression.NodeType">
      <summary>傳回這個 <see cref="T:System.Linq.Expressions.Expression" /> 的節點型別。</summary>
      <returns>表示這個運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.NewExpression.System#Linq#Expressions#IArgumentProvider#ArgumentCount"></member>
    <member name="M:System.Linq.Expressions.NewExpression.System#Linq#Expressions#IArgumentProvider#GetArgument(System.Int32)"></member>
    <member name="P:System.Linq.Expressions.NewExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.NewExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="M:System.Linq.Expressions.NewExpression.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="arguments">結果的 <see cref="P:System.Linq.Expressions.NewExpression.Arguments" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.ParameterExpression">
      <summary>表示具名參數運算式。</summary>
    </member>
    <member name="M:System.Linq.Expressions.ParameterExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>分派給這個節點類型的特定 visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 會呼叫 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>瀏覽這個節點的結果。</returns>
      <param name="visitor">瀏覽這個節點的造訪者。</param>
    </member>
    <member name="P:System.Linq.Expressions.ParameterExpression.IsByRef">
      <summary>表示這個 ParameterExpression 會被視為 ByRef 參數。</summary>
      <returns>如果這個 ParameterExpression 為 ByRef 參數，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ParameterExpression.Name">
      <summary>取得參數或變數的名稱。</summary>
      <returns>包含參數名稱的 <see cref="T:System.String" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ParameterExpression.NodeType">
      <summary>傳回這個 <see cref="T:System.Linq.Expressions.Expression" /> 的節點型別。</summary>
      <returns>表示這個運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.ParameterExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.ParameterExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="T:System.Linq.Expressions.RuntimeVariablesExpression">
      <summary>為變數提供執行階段讀取/寫入權限的運算式。</summary>
    </member>
    <member name="P:System.Linq.Expressions.RuntimeVariablesExpression.NodeType">
      <summary>傳回這個 Expression 的節點型別。延伸節點覆寫這個方法時應該傳回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.RuntimeVariablesExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.RuntimeVariablesExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="M:System.Linq.Expressions.RuntimeVariablesExpression.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.ParameterExpression})">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="variables">結果的 <see cref="P:System.Linq.Expressions.RuntimeVariablesExpression.Variables" /> 屬性。</param>
    </member>
    <member name="P:System.Linq.Expressions.RuntimeVariablesExpression.Variables">
      <summary>要提供執行階段存取的變數或參數。</summary>
      <returns>唯讀集合，包含將提供執行階段存取的參數。</returns>
    </member>
    <member name="T:System.Linq.Expressions.SwitchCase">
      <summary>表示 <see cref="T:System.Linq.Expressions.SwitchExpression" /> 的一個案例。</summary>
    </member>
    <member name="P:System.Linq.Expressions.SwitchCase.Body">
      <summary>取得這個案例的主體。</summary>
      <returns>表示 case 區塊主體的 <see cref="T:System.Linq.Expressions.Expression" /> 物件。</returns>
    </member>
    <member name="P:System.Linq.Expressions.SwitchCase.TestValues">
      <summary>取得這個案例的值。當 <see cref="P:System.Linq.Expressions.SwitchExpression.SwitchValue" /> 符合任何這些值時，會選取這個案例開始執行。</summary>
      <returns>這個 case 區塊的唯讀值集合。</returns>
    </member>
    <member name="M:System.Linq.Expressions.SwitchCase.ToString">
      <summary>傳回 <see cref="T:System.String" />，表示目前的 <see cref="T:System.Object" />。</summary>
      <returns>
        <see cref="T:System.String" />，表示目前的 <see cref="T:System.Object" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.SwitchCase.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression},System.Linq.Expressions.Expression)">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="testValues">結果的 <see cref="P:System.Linq.Expressions.SwitchCase.TestValues" /> 屬性。</param>
      <param name="body">結果的 <see cref="P:System.Linq.Expressions.SwitchCase.Body" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.SwitchExpression">
      <summary>表示透過將控制權傳遞至 <see cref="T:System.Linq.Expressions.SwitchCase" /> 來處理多重選擇的控制運算式。</summary>
    </member>
    <member name="P:System.Linq.Expressions.SwitchExpression.Cases">
      <summary>取得切換的 <see cref="T:System.Linq.Expressions.SwitchCase" /> 物件集合。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.SwitchCase" /> 物件的集合。</returns>
    </member>
    <member name="P:System.Linq.Expressions.SwitchExpression.Comparison">
      <summary>取得相等比較方法 (如果有的話)。</summary>
      <returns>表示相等比較方法的 <see cref="T:System.Reflection.MethodInfo" /> 物件。</returns>
    </member>
    <member name="P:System.Linq.Expressions.SwitchExpression.DefaultBody">
      <summary>取得切換的測試。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" /> 物件，表示切換的測試。</returns>
    </member>
    <member name="P:System.Linq.Expressions.SwitchExpression.NodeType">
      <summary>傳回這個 Expression 的節點型別。延伸節點覆寫這個方法時應該傳回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.SwitchExpression.SwitchValue">
      <summary>取得切換的測試。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" /> 物件，表示切換的測試。</returns>
    </member>
    <member name="P:System.Linq.Expressions.SwitchExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.SwitchExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="M:System.Linq.Expressions.SwitchExpression.Update(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.SwitchCase},System.Linq.Expressions.Expression)">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="switchValue">結果的 <see cref="P:System.Linq.Expressions.SwitchExpression.SwitchValue" /> 屬性。</param>
      <param name="cases">結果的 <see cref="P:System.Linq.Expressions.SwitchExpression.Cases" /> 屬性。</param>
      <param name="defaultBody">結果的 <see cref="P:System.Linq.Expressions.SwitchExpression.DefaultBody" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.SymbolDocumentInfo">
      <summary>儲存發出原始程式檔偵錯符號資訊所需的資訊，特別是檔名和唯一語言識別項。</summary>
    </member>
    <member name="P:System.Linq.Expressions.SymbolDocumentInfo.DocumentType">
      <summary>傳回文件類型的唯一識別項 (如果有的話)。預設為文字檔的 GUID。</summary>
      <returns>文件類型的唯一識別項。</returns>
    </member>
    <member name="P:System.Linq.Expressions.SymbolDocumentInfo.FileName">
      <summary>原始程式檔的名稱。</summary>
      <returns>表示原始程式檔名稱的字串。</returns>
    </member>
    <member name="P:System.Linq.Expressions.SymbolDocumentInfo.Language">
      <summary>傳回語言的唯一識別項 (如果有的話)。</summary>
      <returns>語言的唯一識別項。</returns>
    </member>
    <member name="P:System.Linq.Expressions.SymbolDocumentInfo.LanguageVendor">
      <summary>傳回語言廠商的唯一識別項 (如果有的話)。</summary>
      <returns>語言廠商的唯一識別項。</returns>
    </member>
    <member name="T:System.Linq.Expressions.TryExpression">
      <summary>表示 try/catch/finally/fault 區塊。</summary>
    </member>
    <member name="P:System.Linq.Expressions.TryExpression.Body">
      <summary>取得表示 try 區塊主體的 <see cref="T:System.Linq.Expressions.Expression" />。</summary>
      <returns>表示 try 區塊主體的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.TryExpression.Fault">
      <summary>取得表示 fault 區塊的 <see cref="T:System.Linq.Expressions.Expression" />。</summary>
      <returns>表示 fault 區塊的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.TryExpression.Finally">
      <summary>取得表示 finally 區塊的 <see cref="T:System.Linq.Expressions.Expression" />。</summary>
      <returns>表示 finally 區塊的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.TryExpression.Handlers">
      <summary>取得與 try 區塊相關聯的 <see cref="T:System.Linq.Expressions.CatchBlock" /> 運算式集合。</summary>
      <returns>與 try 區塊相關聯的 <see cref="T:System.Linq.Expressions.CatchBlock" /> 運算式集合。</returns>
    </member>
    <member name="P:System.Linq.Expressions.TryExpression.NodeType">
      <summary>傳回這個 <see cref="T:System.Linq.Expressions.Expression" /> 的節點型別。</summary>
      <returns>表示這個運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.TryExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.TryExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="M:System.Linq.Expressions.TryExpression.Update(System.Linq.Expressions.Expression,System.Collections.Generic.IEnumerable{System.Linq.Expressions.CatchBlock},System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="body">結果的 <see cref="P:System.Linq.Expressions.TryExpression.Body" /> 屬性。</param>
      <param name="handlers">結果的 <see cref="P:System.Linq.Expressions.TryExpression.Handlers" /> 屬性。</param>
      <param name="finally">結果的 <see cref="P:System.Linq.Expressions.TryExpression.Finally" /> 屬性。</param>
      <param name="fault">結果的 <see cref="P:System.Linq.Expressions.TryExpression.Fault" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.TypeBinaryExpression">
      <summary>表示運算式和型別之間的作業。</summary>
    </member>
    <member name="P:System.Linq.Expressions.TypeBinaryExpression.Expression">
      <summary>取得型別測試作業的運算式運算元。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" />，表示型別測試作業的運算式運算元。</returns>
    </member>
    <member name="P:System.Linq.Expressions.TypeBinaryExpression.NodeType">
      <summary>傳回這個 Expression 的節點型別。延伸節點覆寫這個方法時應該傳回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.TypeBinaryExpression.Type">
      <summary>取得 <see cref="P:System.Linq.Expressions.TypeBinaryExpression.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.TypeBinaryExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="P:System.Linq.Expressions.TypeBinaryExpression.TypeOperand">
      <summary>取得型別測試作業的型別運算元。</summary>
      <returns>
        <see cref="T:System.Type" />，表示型別測試作業的型別運算元。</returns>
    </member>
    <member name="M:System.Linq.Expressions.TypeBinaryExpression.Update(System.Linq.Expressions.Expression)">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="expression">結果的 <see cref="P:System.Linq.Expressions.TypeBinaryExpression.Expression" /> 屬性。</param>
    </member>
    <member name="T:System.Linq.Expressions.UnaryExpression">
      <summary>表示有一元 (Unary) 運算子的運算式。</summary>
    </member>
    <member name="P:System.Linq.Expressions.UnaryExpression.CanReduce">
      <summary>取得值，指出是否可以減少運算式樹狀架構的節點。</summary>
      <returns>如果節點可以精簡則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.UnaryExpression.IsLifted">
      <summary>取得值，這個值指出運算式樹狀節點是否表示對運算子的消除呼叫。</summary>
      <returns>如果節點表示消除呼叫，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.UnaryExpression.IsLiftedToNull">
      <summary>取得值，這個值指出運算式樹狀節點是否表示對運算子的消除呼叫，該運算子的傳回型別對可為 null 的型別已消除。</summary>
      <returns>如果運算子的傳回型別對可為 null 的型別消除，則為 true，否則為false。</returns>
    </member>
    <member name="P:System.Linq.Expressions.UnaryExpression.Method">
      <summary>取得一元作業的實作方法。</summary>
      <returns>
        <see cref="T:System.Reflection.MethodInfo" />，表示實作的方法。</returns>
    </member>
    <member name="P:System.Linq.Expressions.UnaryExpression.NodeType">
      <summary>傳回這個 <see cref="T:System.Linq.Expressions.Expression" /> 的節點型別。</summary>
      <returns>表示這個運算式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.UnaryExpression.Operand">
      <summary>取得一元作業的運算元。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.Expression" />，表示一元作業的運算元。</returns>
    </member>
    <member name="M:System.Linq.Expressions.UnaryExpression.Reduce">
      <summary>將運算式節點精簡為更簡單的運算式。</summary>
      <returns>精簡的運算式。</returns>
    </member>
    <member name="P:System.Linq.Expressions.UnaryExpression.Type">
      <summary>取得 <see cref="T:System.Linq.Expressions.Expression" /> 表示之運算式的靜態型別。</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.UnaryExpression.Type" />，表示運算式的靜態型別。</returns>
    </member>
    <member name="M:System.Linq.Expressions.UnaryExpression.Update(System.Linq.Expressions.Expression)">
      <summary>建立與這個項目類似的新運算式，但是使用提供的子系。如果所有子系都相同，它將會傳回這個運算式。</summary>
      <returns>如果沒有變更任何子系，則為這個運算式，或是具有更新之子系的運算式。</returns>
      <param name="operand">結果的 <see cref="P:System.Linq.Expressions.UnaryExpression.Operand" /> 屬性。</param>
    </member>
  </members>
</doc>