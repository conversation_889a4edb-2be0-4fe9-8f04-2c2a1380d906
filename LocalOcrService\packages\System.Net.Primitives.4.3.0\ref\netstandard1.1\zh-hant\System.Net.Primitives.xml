﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Primitives</name>
  </assembly>
  <members>
    <member name="T:System.Net.AuthenticationSchemes">
      <summary>指定驗證的通訊協定。</summary>
    </member>
    <member name="F:System.Net.AuthenticationSchemes.Anonymous">
      <summary>指定匿名驗證。</summary>
    </member>
    <member name="F:System.Net.AuthenticationSchemes.Basic">
      <summary>指定基本驗證。</summary>
    </member>
    <member name="F:System.Net.AuthenticationSchemes.Digest">
      <summary>指定摘要式驗證。</summary>
    </member>
    <member name="F:System.Net.AuthenticationSchemes.IntegratedWindowsAuthentication">
      <summary>指定 Windows 驗證。</summary>
    </member>
    <member name="F:System.Net.AuthenticationSchemes.Negotiate">
      <summary>與用戶端交涉，以決定驗證配置。如果用戶端和伺服器都支援 Kerberos，就使用它，否則使用 NTLM。</summary>
    </member>
    <member name="F:System.Net.AuthenticationSchemes.None">
      <summary>不允許驗證。用戶端要求的 <see cref="T:System.Net.HttpListener" /> 物件已設定這個旗標時，一定會收到 403 禁止狀態。當資源絕對不可提供給用戶端時，請使用這個旗標。</summary>
    </member>
    <member name="F:System.Net.AuthenticationSchemes.Ntlm">
      <summary>指定 NTLM 驗證。</summary>
    </member>
    <member name="T:System.Net.Cookie">
      <summary>提供用來管理 Cookie 的屬性和方法集合。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Net.Cookie.#ctor">
      <summary>初始化 <see cref="T:System.Net.Cookie" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.Cookie.#ctor(System.String,System.String)">
      <summary>使用指定的 <see cref="P:System.Net.Cookie.Name" /> 和 <see cref="P:System.Net.Cookie.Value" />，初始化 <see cref="T:System.Net.Cookie" /> 類別的新執行個體。</summary>
      <param name="name">
        <see cref="T:System.Net.Cookie" /> 的名稱。<paramref name="name" /> 內不可使用下列的字元：等號、分號、逗號、新行字元 (\n)、換行字元 (\r)、定位鍵 (\t) 及空格。第一個字元不能是貨幣符號 ($) 字元。</param>
      <param name="value">
        <see cref="T:System.Net.Cookie" /> 的值。<paramref name="value" /> 內不可使用下列的字元：分號 (;)、逗號 (,)。</param>
      <exception cref="T:System.Net.CookieException">
        <paramref name="name" /> 參數為 null。-或-<paramref name="name" /> 參數的長度為零。-或-<paramref name="name" /> 參數包含無效的字元。-或-<paramref name="value" /> 參數為 null。-或-<paramref name="value" /> 參數含有不是以引號括住、包含無效字元的字串。</exception>
    </member>
    <member name="M:System.Net.Cookie.#ctor(System.String,System.String,System.String)">
      <summary>使用指定的 <see cref="P:System.Net.Cookie.Name" />、<see cref="P:System.Net.Cookie.Value" /> 和 <see cref="P:System.Net.Cookie.Path" />，初始化 <see cref="T:System.Net.Cookie" /> 類別的新執行個體。</summary>
      <param name="name">
        <see cref="T:System.Net.Cookie" /> 的名稱。<paramref name="name" /> 內不可使用下列的字元：等號、分號、逗號、新行字元 (\n)、換行字元 (\r)、定位鍵 (\t) 及空格。第一個字元不能是貨幣符號 ($) 字元。</param>
      <param name="value">
        <see cref="T:System.Net.Cookie" /> 的值。<paramref name="value" /> 內不可使用下列的字元：分號 (;)、逗號 (,)。</param>
      <param name="path">原始伺服器上這個 <see cref="T:System.Net.Cookie" /> 要套用的 URI 子集。預設值為 "/"。</param>
      <exception cref="T:System.Net.CookieException">
        <paramref name="name" /> 參數為 null。-或-<paramref name="name" /> 參數的長度為零。-或-<paramref name="name" /> 參數包含無效的字元。-或-<paramref name="value" /> 參數為 null。-或-<paramref name="value" /> 參數含有不是以引號括住、包含無效字元的字串。</exception>
    </member>
    <member name="M:System.Net.Cookie.#ctor(System.String,System.String,System.String,System.String)">
      <summary>使用指定的 <see cref="P:System.Net.Cookie.Name" />、<see cref="P:System.Net.Cookie.Value" />、<see cref="P:System.Net.Cookie.Path" /> 和 <see cref="P:System.Net.Cookie.Domain" />，初始化 <see cref="T:System.Net.Cookie" /> 類別的新執行個體。</summary>
      <param name="name">
        <see cref="T:System.Net.Cookie" /> 的名稱。<paramref name="name" /> 內不可使用下列的字元：等號、分號、逗號、新行字元 (\n)、換行字元 (\r)、定位鍵 (\t) 及空格。第一個字元不能是貨幣符號 ($) 字元。</param>
      <param name="value">
        <see cref="T:System.Net.Cookie" /> 物件的值。<paramref name="value" /> 內不可使用下列的字元：分號 (;)、逗號 (,)。</param>
      <param name="path">原始伺服器上這個 <see cref="T:System.Net.Cookie" /> 要套用的 URI 子集。預設值為 "/"。</param>
      <param name="domain">這個 <see cref="T:System.Net.Cookie" /> 有效的選擇性網際網路網域。預設值是發出這個 <see cref="T:System.Net.Cookie" /> 的主機。</param>
      <exception cref="T:System.Net.CookieException">
        <paramref name="name" /> 參數為 null。-或-<paramref name="name" /> 參數的長度為零。-或-<paramref name="name" /> 參數包含無效的字元。-或-<paramref name="value" /> 參數為 null。-或-<paramref name="value" /> 參數含有不是以引號括住、包含無效字元的字串。</exception>
    </member>
    <member name="P:System.Net.Cookie.Comment">
      <summary>取得或設定伺服器可加入至 <see cref="T:System.Net.Cookie" /> 的註解。</summary>
      <returns>這個 <see cref="T:System.Net.Cookie" /> 使用說明文件的選擇性註解。</returns>
    </member>
    <member name="P:System.Net.Cookie.CommentUri">
      <summary>取得或設定伺服器以 <see cref="T:System.Net.Cookie" /> 提供的 URI 註解。</summary>
      <returns>選擇性註解，表示這個 <see cref="T:System.Net.Cookie" /> 之 URI 參考的預期用法。這個值必須符合 URI 格式。</returns>
    </member>
    <member name="P:System.Net.Cookie.Discard">
      <summary>取得或設定由伺服器設定的捨棄旗標。</summary>
      <returns>如果用戶端在目前工作階段 (Session) 尾端捨棄 <see cref="T:System.Net.Cookie" />，則為 true，否則為 false。預設為 false。</returns>
    </member>
    <member name="P:System.Net.Cookie.Domain">
      <summary>取得或設定 <see cref="T:System.Net.Cookie" /> 為有效的 URI。</summary>
      <returns>
        <see cref="T:System.Net.Cookie" /> 為有效的 URI。</returns>
    </member>
    <member name="M:System.Net.Cookie.Equals(System.Object)">
      <summary>覆寫 <see cref="M:System.Object.Equals(System.Object)" /> 方法。</summary>
      <returns>當 <see cref="T:System.Net.Cookie" /> 等於 <paramref name="comparand" /> 時，傳回 true。若兩個 <see cref="T:System.Net.Cookie" /> 執行個體的 <see cref="P:System.Net.Cookie.Name" />、<see cref="P:System.Net.Cookie.Value" />、<see cref="P:System.Net.Cookie.Path" />、<see cref="P:System.Net.Cookie.Domain" /> 和 <see cref="P:System.Net.Cookie.Version" /> 屬性相等，則這兩個執行個體即相等。<see cref="P:System.Net.Cookie.Name" /> 和 <see cref="P:System.Net.Cookie.Domain" /> 字串比較不區分大小寫。</returns>
      <param name="comparand">對 <see cref="T:System.Net.Cookie" /> 的參考。</param>
    </member>
    <member name="P:System.Net.Cookie.Expired">
      <summary>取得或設定 <see cref="T:System.Net.Cookie" /> 的目前狀態。</summary>
      <returns>如果 <see cref="T:System.Net.Cookie" /> 已經到期，則為 true，否則為 false。預設為 false。</returns>
    </member>
    <member name="P:System.Net.Cookie.Expires">
      <summary>取得或設定 <see cref="T:System.Net.Cookie" /> 的到期日和時間為 <see cref="T:System.DateTime" />。</summary>
      <returns>
        <see cref="T:System.Net.Cookie" /> 的到期日和時間為 <see cref="T:System.DateTime" /> 執行個體。</returns>
    </member>
    <member name="M:System.Net.Cookie.GetHashCode">
      <summary>覆寫 <see cref="M:System.Object.GetHashCode" /> 方法。</summary>
      <returns>這個執行個體的 32 位元簽章整數雜湊程式碼。</returns>
    </member>
    <member name="P:System.Net.Cookie.HttpOnly">
      <summary>決定頁面指令碼或其他主動式內容是否可以存取這個 Cookie。</summary>
      <returns>布林值，決定頁面指令碼或其他主動式內容是否可以存取這個 Cookie。</returns>
    </member>
    <member name="P:System.Net.Cookie.Name">
      <summary>取得或設定 <see cref="T:System.Net.Cookie" /> 的名稱。</summary>
      <returns>
        <see cref="T:System.Net.Cookie" /> 的名稱。</returns>
      <exception cref="T:System.Net.CookieException">為設定作業所指定的值為 null 或空字串。-或-為設定作業所指定的值包含不合法的字元。<see cref="P:System.Net.Cookie.Name" /> 屬性內不可使用下列的字元：等號、分號、逗號、新行字元 (\n)、換行字元 (\r)、定位鍵 (\t) 及空格。第一個字元不能是貨幣符號 ($) 字元。</exception>
    </member>
    <member name="P:System.Net.Cookie.Path">
      <summary>取得或設定 <see cref="T:System.Net.Cookie" /> 要套用的 URI。</summary>
      <returns>
        <see cref="T:System.Net.Cookie" /> 要套用的 URI。</returns>
    </member>
    <member name="P:System.Net.Cookie.Port">
      <summary>取得或設定 <see cref="T:System.Net.Cookie" /> 可以套用的 TCP 連接埠清單。</summary>
      <returns>
        <see cref="T:System.Net.Cookie" /> 可以套用的 TCP 連接埠清單。</returns>
      <exception cref="T:System.Net.CookieException">無法剖析為設定作業所指定的值，或是該值並未包含於雙引號中。</exception>
    </member>
    <member name="P:System.Net.Cookie.Secure">
      <summary>取得或設定 <see cref="T:System.Net.Cookie" /> 的安全層級。</summary>
      <returns>如果要求使用的是 HTTPS (Secure Hypertext Transfer Protocol)，且用戶端在後續的要求中只需傳回 Cookie，則為 true，否則，即為 false。預設為 false。</returns>
    </member>
    <member name="P:System.Net.Cookie.TimeStamp">
      <summary>取得以 <see cref="T:System.DateTime" /> 發出 Cookie 的時間。</summary>
      <returns>以 <see cref="T:System.DateTime" /> 發出 Cookie 的時間。</returns>
    </member>
    <member name="M:System.Net.Cookie.ToString">
      <summary>覆寫 <see cref="M:System.Object.ToString" /> 方法。</summary>
      <returns>傳回這個 <see cref="T:System.Net.Cookie" /> 物件的字串表示，以用來含入 HTTP Cookie: request 標頭。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Cookie.Value">
      <summary>取得或設定 <see cref="T:System.Net.Cookie" /> 的 <see cref="P:System.Net.Cookie.Value" />。</summary>
      <returns>
        <see cref="T:System.Net.Cookie" /> 的 <see cref="P:System.Net.Cookie.Value" />。</returns>
    </member>
    <member name="P:System.Net.Cookie.Version">
      <summary>取得或設定 Cookie 依據的 HTTP 狀態維護版本。</summary>
      <returns>Cookie 依據的 HTTP 狀態維護版本。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">不允許指定給版本的值。</exception>
    </member>
    <member name="T:System.Net.CookieCollection">
      <summary>提供 <see cref="T:System.Net.Cookie" /> 類別的執行個體 (Instance) 其集合容器 (Container)。</summary>
    </member>
    <member name="M:System.Net.CookieCollection.#ctor">
      <summary>初始化 <see cref="T:System.Net.CookieCollection" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.CookieCollection.Add(System.Net.Cookie)">
      <summary>將 <see cref="T:System.Net.Cookie" /> 加入至 <see cref="T:System.Net.CookieCollection" />。</summary>
      <param name="cookie">要加入至 <see cref="T:System.Net.CookieCollection" /> 的 <see cref="T:System.Net.Cookie" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="cookie" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.CookieCollection.Add(System.Net.CookieCollection)">
      <summary>將 <see cref="T:System.Net.CookieCollection" /> 的內容加入至目前的執行個體。</summary>
      <param name="cookies">要加入的 <see cref="T:System.Net.CookieCollection" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="cookies" /> 為 null。</exception>
    </member>
    <member name="P:System.Net.CookieCollection.Count">
      <summary>取得在 <see cref="T:System.Net.CookieCollection" /> 中所包含的 Cookie 數。</summary>
      <returns>在 <see cref="T:System.Net.CookieCollection" /> 中所包含的 Cookie 數。</returns>
    </member>
    <member name="M:System.Net.CookieCollection.GetEnumerator">
      <summary>傳回可以逐一查看 <see cref="T:System.Net.CookieCollection" /> 的列舉程式。</summary>
      <returns>可以逐一查看 <see cref="T:System.Net.CookieCollection" /> 的 <see cref="T:System.Collections.IEnumerator" /> 介面實作執行個體。</returns>
    </member>
    <member name="P:System.Net.CookieCollection.Item(System.String)">
      <summary>從 <see cref="T:System.Net.CookieCollection" /> 中以特定的名稱取得 <see cref="T:System.Net.Cookie" />。</summary>
      <returns>在 <see cref="T:System.Net.CookieCollection" /> 中具有特定名稱的 <see cref="T:System.Net.Cookie" />。</returns>
      <param name="name">要尋找的 <see cref="T:System.Net.Cookie" /> 名稱。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.CookieCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>[支援 .NET Framework 4.5.1 (含) 以後版本]如需這個成員的說明，請參閱 <see cref="M:System.Collections.ICollection.CopyTo(System.Array,System.Int32)" />。</summary>
      <param name="array">從集合複製元素之目的端一維陣列。陣列必須有以零起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
    </member>
    <member name="P:System.Net.CookieCollection.System#Collections#ICollection#IsSynchronized">
      <summary>[支援 .NET Framework 4.5.1 (含) 以後版本]如需這個成員的說明，請參閱 <see cref="P:System.Collections.ICollection.IsSynchronized" />。</summary>
      <returns>如果對集合的存取是同步處理的 (安全執行緒)，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Net.CookieCollection.System#Collections#ICollection#SyncRoot">
      <summary>[支援 .NET Framework 4.5.1 (含) 以後版本]如需這個成員的說明，請參閱 <see cref="P:System.Collections.ICollection.SyncRoot" />。</summary>
      <returns>Object，可用來對集合同步存取。</returns>
    </member>
    <member name="T:System.Net.CookieContainer">
      <summary>提供 <see cref="T:System.Net.CookieCollection" /> 物件集合的容器 (Container)。</summary>
    </member>
    <member name="M:System.Net.CookieContainer.#ctor">
      <summary>初始化 <see cref="T:System.Net.CookieContainer" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.CookieContainer.Add(System.Uri,System.Net.Cookie)">
      <summary>將 <see cref="T:System.Net.Cookie" /> 加入至特定 URI 的 <see cref="T:System.Net.CookieContainer" />。</summary>
      <param name="uri">要加入至 <see cref="T:System.Net.CookieContainer" /> 之 <see cref="T:System.Net.Cookie" /> 的 URI。</param>
      <param name="cookie">要加入至 <see cref="T:System.Net.CookieContainer" /> 的 <see cref="T:System.Net.Cookie" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> 是 null，或 <paramref name="cookie" /> 是 null。</exception>
      <exception cref="T:System.Net.CookieException">
        <paramref name="cookie" /> 大於 <paramref name="maxCookieSize" />。-或-<paramref name="cookie" /> 的網域不是有效的 URI。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.CookieContainer.Add(System.Uri,System.Net.CookieCollection)">
      <summary>將 <see cref="T:System.Net.CookieCollection" /> 的內容加入至特定 URI 的 <see cref="T:System.Net.CookieContainer" />。</summary>
      <param name="uri">要加入至 <see cref="T:System.Net.CookieContainer" /> 之 <see cref="T:System.Net.CookieCollection" /> 的 URI。</param>
      <param name="cookies">要加入至 <see cref="T:System.Net.CookieContainer" /> 的 <see cref="T:System.Net.CookieCollection" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="cookies" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="cookies" /> 內其中一個 Cookie 的網域是 null。</exception>
      <exception cref="T:System.Net.CookieException">
        <paramref name="cookies" /> 內其中一個 Cookie 包含無效的網域。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.CookieContainer.Capacity">
      <summary>取得或設定 <see cref="T:System.Net.CookieContainer" /> 可以保留的 <see cref="T:System.Net.Cookie" /> 執行個體數目。</summary>
      <returns>
        <see cref="T:System.Net.CookieContainer" /> 可以保留的 <see cref="T:System.Net.Cookie" /> 執行個體數目。這是一種硬式限制且不可以利用加入 <see cref="T:System.Net.Cookie" /> 的方式超過。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="Capacity" /> 小於或等於零，或 (值小於 <see cref="P:System.Net.CookieContainer.PerDomainCapacity" /> 且 <see cref="P:System.Net.CookieContainer.PerDomainCapacity" /> 不等於 <see cref="F:System.Int32.MaxValue" />)。</exception>
    </member>
    <member name="P:System.Net.CookieContainer.Count">
      <summary>取得 <see cref="T:System.Net.CookieContainer" /> 目前保留的 <see cref="T:System.Net.Cookie" /> 執行個體數目。</summary>
      <returns>
        <see cref="T:System.Net.CookieContainer" /> 目前保留的 <see cref="T:System.Net.Cookie" /> 執行個體數目。這是所有網域中 <see cref="T:System.Net.Cookie" /> 執行個體的總和。</returns>
    </member>
    <member name="F:System.Net.CookieContainer.DefaultCookieLengthLimit">
      <summary>以位元組為單位表示 <see cref="T:System.Net.CookieContainer" /> 可以保留之 <see cref="T:System.Net.Cookie" /> 執行個體的預設大小最大值。這個欄位是常數。</summary>
    </member>
    <member name="F:System.Net.CookieContainer.DefaultCookieLimit">
      <summary>表示 <see cref="T:System.Net.CookieContainer" /> 可以保留之 <see cref="T:System.Net.Cookie" /> 執行個體的預設最大數目。這個欄位是常數。</summary>
    </member>
    <member name="F:System.Net.CookieContainer.DefaultPerDomainCookieLimit">
      <summary>表示 <see cref="T:System.Net.CookieContainer" /> 在每一個網域可以參考之 <see cref="T:System.Net.Cookie" /> 執行個體的預設最大數目。這個欄位是常數。</summary>
    </member>
    <member name="M:System.Net.CookieContainer.GetCookieHeader(System.Uri)">
      <summary>取得 HTTP Cookie 標頭，包含表示與特定 URI 關聯之 <see cref="T:System.Net.Cookie" /> 執行個體的 HTTP Cookie。</summary>
      <returns>HTTP Cookie 標頭，其中含有表示 <see cref="T:System.Net.Cookie" /> 執行個體 (以分號分隔) 的字串。</returns>
      <param name="uri">所需的 <see cref="T:System.Net.Cookie" /> 執行個體 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> 為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.CookieContainer.GetCookies(System.Uri)">
      <summary>取得 <see cref="T:System.Net.CookieCollection" />，包含與特定 URI 關聯的 <see cref="T:System.Net.Cookie" /> 執行個體。</summary>
      <returns>
        <see cref="T:System.Net.CookieCollection" />，包含與特定 URI 關聯的 <see cref="T:System.Net.Cookie" /> 執行個體。</returns>
      <param name="uri">所需的 <see cref="T:System.Net.Cookie" /> 執行個體 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> 為 null。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.CookieContainer.MaxCookieSize">
      <summary>表示 <see cref="T:System.Net.Cookie" /> 允許的最大長度。</summary>
      <returns>以位元組為單位表示的 <see cref="T:System.Net.Cookie" /> 允許的最大長度。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="MaxCookieSize" /> 小於或等於零值。</exception>
    </member>
    <member name="P:System.Net.CookieContainer.PerDomainCapacity">
      <summary>取得或設定 <see cref="T:System.Net.CookieContainer" /> 在每一個網域可以保留的 <see cref="T:System.Net.Cookie" /> 執行個體數目。</summary>
      <returns>每一個網域允許的 <see cref="T:System.Net.Cookie" /> 執行個體數目。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="PerDomainCapacity" /> 小於或等於零值。-或-<paramref name="(PerDomainCapacity" /> 大於 Cookie 執行個體允許的最大數目 300 且不等於 <see cref="F:System.Int32.MaxValue" />)。</exception>
    </member>
    <member name="M:System.Net.CookieContainer.SetCookies(System.Uri,System.String)">
      <summary>將 HTTP Cookie 標頭中一或多個 Cookie 的 <see cref="T:System.Net.Cookie" /> 執行個體加入至特定 URI 的 <see cref="T:System.Net.CookieContainer" />。</summary>
      <param name="uri">
        <see cref="T:System.Net.CookieCollection" /> 的 URI。</param>
      <param name="cookieHeader">使用以逗號分隔的 <see cref="T:System.Net.Cookie" /> 執行個體，由 HTTP 伺服器傳回的 HTTP Set-Cookie 標頭的內容。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> 為 null。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="cookieHeader" /> 為 null。</exception>
      <exception cref="T:System.Net.CookieException">其中一個 Cookie 無效。-或-將其中一個 Cookie 加入容器時發生錯誤。</exception>
    </member>
    <member name="T:System.Net.CookieException">
      <summary>將 <see cref="T:System.Net.Cookie" /> 加入至 <see cref="T:System.Net.CookieContainer" /> 時產生錯誤所擲回的例外狀況。</summary>
    </member>
    <member name="M:System.Net.CookieException.#ctor">
      <summary>初始化 <see cref="T:System.Net.CookieException" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:System.Net.CredentialCache">
      <summary>提供多個認證的儲存區。</summary>
    </member>
    <member name="M:System.Net.CredentialCache.#ctor">
      <summary>建立 <see cref="T:System.Net.CredentialCache" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.CredentialCache.Add(System.String,System.Int32,System.String,System.Net.NetworkCredential)">
      <summary>將要搭配 Simple Mail Transfer Protocol (SMTP) 使用的 <see cref="T:System.Net.NetworkCredential" /> 執行個體加入至認證快取，並使其與主機電腦、連接埠和驗證通訊協定產生關聯。使用此方法加入的認證只適用於 SMTP。這個方法不適用於 HTTP 或 FTP 要求。</summary>
      <param name="host">識別主機電腦的 <see cref="T:System.String" />。</param>
      <param name="port">
        <see cref="T:System.Int32" />，指定 <paramref name="host" /> 上的連接埠。</param>
      <param name="authenticationType">
        <see cref="T:System.String" />，識別使用 <paramref name="cred" /> 連接到 <paramref name="host" /> 時，所使用的驗證配置。請參閱＜備註＞。</param>
      <param name="credential">
        <see cref="T:System.Net.NetworkCredential" />，要加入至認證快取。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="host" /> 為 null。-或-<paramref name="authType" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="authType" /> 不是接受的值。請參閱＜備註＞。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> 小於零。</exception>
    </member>
    <member name="M:System.Net.CredentialCache.Add(System.Uri,System.String,System.Net.NetworkCredential)">
      <summary>將要搭配 SMTP 以外其他通訊協定使用的 <see cref="T:System.Net.NetworkCredential" /> 執行個體加入至認證快取，並使其與統一資源識別元 (URI) 和驗證通訊協定產生關聯。</summary>
      <param name="uriPrefix">
        <see cref="T:System.Uri" />，指定認證允許存取之資源的 URI 前置詞。</param>
      <param name="authType">驗證配置，由在 <paramref name="uriPrefix" /> 中指名的資源所使用。</param>
      <param name="cred">
        <see cref="T:System.Net.NetworkCredential" />，要加入至認證快取。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uriPrefix" /> 為 null。-或-<paramref name="authType" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">相同的認證會加入多次</exception>
    </member>
    <member name="P:System.Net.CredentialCache.DefaultCredentials">
      <summary>取得應用程式的系統認證。</summary>
      <returns>
        <see cref="T:System.Net.ICredentials" />，表示應用程式的系統認證。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="USERNAME" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.CredentialCache.DefaultNetworkCredentials">
      <summary>取得目前安全性內容的網路認證。</summary>
      <returns>
        <see cref="T:System.Net.NetworkCredential" />，表示目前使用者或應用程式的網路認證。</returns>
    </member>
    <member name="M:System.Net.CredentialCache.GetCredential(System.String,System.Int32,System.String)">
      <summary>傳回與指定主機、連接埠和驗證通訊協定關聯的 <see cref="T:System.Net.NetworkCredential" /> 執行個體。</summary>
      <returns>
        <see cref="T:System.Net.NetworkCredential" />，或 null (如果在快取中沒有相符的認證)。</returns>
      <param name="host">識別主機電腦的 <see cref="T:System.String" />。</param>
      <param name="port">
        <see cref="T:System.Int32" />，指定 <paramref name="host" /> 上的連接埠。</param>
      <param name="authenticationType">
        <see cref="T:System.String" />，識別連接到 <paramref name="host" /> 時，所使用的驗證配置。請參閱＜備註＞。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="host" /> 為 null。-或-<paramref name="authType" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="authType" /> 不是接受的值。請參閱＜備註＞。-或-<paramref name="host" /> 等於空字串 ("")。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> 小於零。</exception>
    </member>
    <member name="M:System.Net.CredentialCache.GetCredential(System.Uri,System.String)">
      <summary>傳回與所指定之統一資源識別項 (URI) 和驗證類型關聯的 <see cref="T:System.Net.NetworkCredential" /> 執行個體。</summary>
      <returns>
        <see cref="T:System.Net.NetworkCredential" />，或 null (如果在快取中沒有相符的認證)。</returns>
      <param name="uriPrefix">
        <see cref="T:System.Uri" />，指定認證允許存取之資源的 URI 前置詞。</param>
      <param name="authType">驗證配置，由在 <paramref name="uriPrefix" /> 中指名的資源所使用。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uriPrefix" /> 或 <paramref name="authType" /> 是 null。</exception>
    </member>
    <member name="M:System.Net.CredentialCache.GetEnumerator">
      <summary>傳回可以逐一查看 <see cref="T:System.Net.CredentialCache" /> 執行個體的列舉值。</summary>
      <returns>
        <see cref="T:System.Net.CredentialCache" /> 的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Net.CredentialCache.Remove(System.String,System.Int32,System.String)">
      <summary>從快取刪除 <see cref="T:System.Net.NetworkCredential" /> 執行個體，如果它與所指定之主機、連接埠和驗證通訊協定關聯。</summary>
      <param name="host">識別主機電腦的 <see cref="T:System.String" />。</param>
      <param name="port">
        <see cref="T:System.Int32" />，指定 <paramref name="host" /> 上的連接埠。</param>
      <param name="authenticationType">
        <see cref="T:System.String" />，識別連接到 <paramref name="host" /> 時，所使用的驗證配置。請參閱＜備註＞。</param>
    </member>
    <member name="M:System.Net.CredentialCache.Remove(System.Uri,System.String)">
      <summary>從快取刪除 <see cref="T:System.Net.NetworkCredential" /> 執行個體，如果它與所指定之統一資源識別元 (URI) 和驗證通訊協定關聯。</summary>
      <param name="uriPrefix">
        <see cref="T:System.Uri" />，指定認證使用之資源的 URI 前置詞。</param>
      <param name="authType">由在 <paramref name="uriPrefix" /> 中命名的主機所使用的驗證配置。</param>
    </member>
    <member name="T:System.Net.DecompressionMethods">
      <summary>表示為回應 <see cref="T:System.Net.HttpWebRequest" />，用來壓縮所收到之資料的檔案壓縮和解壓縮編碼格式。</summary>
    </member>
    <member name="F:System.Net.DecompressionMethods.Deflate">
      <summary>使用結實壓縮-解壓縮演算法。</summary>
    </member>
    <member name="F:System.Net.DecompressionMethods.GZip">
      <summary>使用 gZip 壓縮-解壓縮演算法。</summary>
    </member>
    <member name="F:System.Net.DecompressionMethods.None">
      <summary>不要使用壓縮。</summary>
    </member>
    <member name="T:System.Net.DnsEndPoint">
      <summary>將網路端點表示成主機名稱或 IP 位址的字串表示以及通訊埠編號。</summary>
    </member>
    <member name="M:System.Net.DnsEndPoint.#ctor(System.String,System.Int32)">
      <summary>使用主機名稱或 IP 位址的字串表示以及通訊埠編號，初始化 <see cref="T:System.Net.DnsEndPoint" /> 類別的新執行個體。</summary>
      <param name="host">主機名稱或 IP 位址的字串表示。</param>
      <param name="port">與位址關聯的通訊埠編號，或 0 指定任何可用的通訊埠。<paramref name="port" /> 依主機順序顯示。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="host" /> 參數包含空字串。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="host" /> 參數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> 小於 <see cref="F:System.Net.IPEndPoint.MinPort" />。-或-<paramref name="port" /> 大於 <see cref="F:System.Net.IPEndPoint.MaxPort" />。</exception>
    </member>
    <member name="M:System.Net.DnsEndPoint.#ctor(System.String,System.Int32,System.Net.Sockets.AddressFamily)">
      <summary>使用主機名稱或 IP 位址的字串表示、通訊埠編號和位址家族，初始化 <see cref="T:System.Net.DnsEndPoint" /> 類別的新執行個體。</summary>
      <param name="host">主機名稱或 IP 位址的字串表示。</param>
      <param name="port">與位址關聯的通訊埠編號，或 0 指定任何可用的通訊埠。<paramref name="port" /> 依主機順序顯示。</param>
      <param name="addressFamily">一個 <see cref="T:System.Net.Sockets.AddressFamily" /> 值。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="host" /> 參數包含空字串。-或-<paramref name="addressFamily" /> 為 <see cref="F:System.Net.Sockets.AddressFamily.Unknown" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="host" /> 參數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> 小於 <see cref="F:System.Net.IPEndPoint.MinPort" />。-或-<paramref name="port" /> 大於 <see cref="F:System.Net.IPEndPoint.MaxPort" />。</exception>
    </member>
    <member name="P:System.Net.DnsEndPoint.AddressFamily">
      <summary>取得網際網路通訊協定 (IP) 位址家族。</summary>
      <returns>一個 <see cref="T:System.Net.Sockets.AddressFamily" /> 值。</returns>
    </member>
    <member name="M:System.Net.DnsEndPoint.Equals(System.Object)">
      <summary>比較兩個 <see cref="T:System.Net.DnsEndPoint" /> 物件。</summary>
      <returns>如果兩個 <see cref="T:System.Net.DnsEndPoint" /> 執行個體相等，則為 true，否則為 false。</returns>
      <param name="comparand">要與目前執行個體比較的 <see cref="T:System.Net.DnsEndPoint" /> 執行個體。</param>
    </member>
    <member name="M:System.Net.DnsEndPoint.GetHashCode">
      <summary>傳回 <see cref="T:System.Net.DnsEndPoint" /> 的雜湊值。</summary>
      <returns>
        <see cref="T:System.Net.DnsEndPoint" /> 的整數雜湊值。</returns>
    </member>
    <member name="P:System.Net.DnsEndPoint.Host">
      <summary>取得主機的主機名稱或網際網路通訊協定 (IP) 位址的字串表示。</summary>
      <returns>主機名稱或 IP 位址的字串表示。</returns>
    </member>
    <member name="P:System.Net.DnsEndPoint.Port">
      <summary>取得 <see cref="T:System.Net.DnsEndPoint" /> 的通訊埠編號。</summary>
      <returns>範圍在 0 到 0xffff 之內的整數值，表示 <see cref="T:System.Net.DnsEndPoint" /> 的通訊埠編號。</returns>
    </member>
    <member name="M:System.Net.DnsEndPoint.ToString">
      <summary>傳回 <see cref="T:System.Net.DnsEndPoint" /> 的主機名稱或 IP 位址的字串表示以及通訊埠編號。</summary>
      <returns>字串，包含指定之 <see cref="T:System.Net.DnsEndPoint" /> 的位址家族、主機名稱或 IP 位址，以及通訊埠編號。</returns>
    </member>
    <member name="T:System.Net.EndPoint">
      <summary>識別網路位址。這是 abstract 類別。</summary>
    </member>
    <member name="M:System.Net.EndPoint.#ctor">
      <summary>初始化 <see cref="T:System.Net.EndPoint" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Net.EndPoint.AddressFamily">
      <summary>取得端點所屬的通訊協定家族 (Family)。</summary>
      <returns>一個 <see cref="T:System.Net.Sockets.AddressFamily" /> 值。</returns>
      <exception cref="T:System.NotImplementedException">當屬性在子代類別中未覆寫時，會嘗試取得或設定該屬性。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.EndPoint.Create(System.Net.SocketAddress)">
      <summary>從 <see cref="T:System.Net.SocketAddress" /> 執行個體 (Instance) 建立 <see cref="T:System.Net.EndPoint" /> 執行個體。</summary>
      <returns>新的 <see cref="T:System.Net.EndPoint" /> 執行個體，初始化自指定的 <see cref="T:System.Net.SocketAddress" /> 執行個體。</returns>
      <param name="socketAddress">做為連接端點的通訊端位址。</param>
      <exception cref="T:System.NotImplementedException">當方法在子代類別中未覆寫時，會嘗試存取該方法。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.EndPoint.Serialize">
      <summary>將端點資訊序列化為 <see cref="T:System.Net.SocketAddress" /> 執行個體。</summary>
      <returns>
        <see cref="T:System.Net.SocketAddress" /> 執行個體，包含端點資訊。</returns>
      <exception cref="T:System.NotImplementedException">當方法在子代類別中未覆寫時，會嘗試存取該方法。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.HttpStatusCode">
      <summary>包含針對 HTTP 所定義的狀態碼值。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Accepted">
      <summary>相當於 HTTP 狀態 202。<see cref="F:System.Net.HttpStatusCode.Accepted" /> 表示已接受要求進行進一步處理。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Ambiguous">
      <summary>相當於 HTTP 狀態 300。<see cref="F:System.Net.HttpStatusCode.Ambiguous" /> 指示要求的資訊有多種表示法。預設動作是將這個狀態視為重新導向並跟隨在與這個回應相關的 Location 標頭內容之後。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.BadGateway">
      <summary>相當於 HTTP 狀態 502。<see cref="F:System.Net.HttpStatusCode.BadGateway" /> 表示中繼 Proxy 伺服器收到其他 Proxy 或原始伺服器的錯誤回應。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.BadRequest">
      <summary>相當於 HTTP 狀態 400。<see cref="F:System.Net.HttpStatusCode.BadRequest" /> 指示伺服器無法了解要求。當沒有其他適用的錯誤，或者如果確實的錯誤是未知的或沒有自己的錯誤碼時，傳送 <see cref="F:System.Net.HttpStatusCode.BadRequest" />。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Conflict">
      <summary>相當於 HTTP 狀態 409。<see cref="F:System.Net.HttpStatusCode.Conflict" /> 指示因為伺服器上的衝突而無法完成要求。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Continue">
      <summary>相當於 HTTP 狀態 100。<see cref="F:System.Net.HttpStatusCode.Continue" /> 指示用戶端可以繼續它的要求。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Created">
      <summary>相當於 HTTP 狀態 201。<see cref="F:System.Net.HttpStatusCode.Created" /> 指示在傳送回應之前，要求導致新資源的建立。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.ExpectationFailed">
      <summary>相當於 HTTP 狀態 417。<see cref="F:System.Net.HttpStatusCode.ExpectationFailed" /> 指示在 Expect 標頭中所指定的預期項目不符合伺服器的要求。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Forbidden">
      <summary>相當於 HTTP 狀態 403。<see cref="F:System.Net.HttpStatusCode.Forbidden" /> 指示伺服器拒絕處理要求。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Found">
      <summary>相當於 HTTP 狀態 302。<see cref="F:System.Net.HttpStatusCode.Found" /> 指示要求的資訊位於 Location 標頭中所指定的 URI。在接收這個狀態時，預設動作會跟隨與回應相關的 Location 標頭。當原始的要求方法是 POST 時，重新導向的要求將使用 GET 方法。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.GatewayTimeout">
      <summary>相當於 HTTP 狀態 504。<see cref="F:System.Net.HttpStatusCode.GatewayTimeout" /> 指示中繼 Proxy 伺服器在等候來自其他 Proxy 或原始伺服器的回應時已逾時。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Gone">
      <summary>相當於 HTTP 狀態 410。<see cref="F:System.Net.HttpStatusCode.Gone" /> 指示要求的資源已不能再使用。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.HttpVersionNotSupported">
      <summary>相當於 HTTP 狀態 505。<see cref="F:System.Net.HttpStatusCode.HttpVersionNotSupported" /> 指示伺服器不支援要求的 HTTP 版本。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.InternalServerError">
      <summary>相當於 HTTP 狀態 500。<see cref="F:System.Net.HttpStatusCode.InternalServerError" /> 指示伺服器上已經發生泛用錯誤。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.LengthRequired">
      <summary>相當於 HTTP 狀態 411。<see cref="F:System.Net.HttpStatusCode.LengthRequired" /> 指示遺漏要求的 Content-Length 標頭。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.MethodNotAllowed">
      <summary>相當於 HTTP 狀態 405。<see cref="F:System.Net.HttpStatusCode.MethodNotAllowed" /> 指示在要求的資源上不允許該要求方法 (POST 或 GET)。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Moved">
      <summary>相當於 HTTP 狀態 301。<see cref="F:System.Net.HttpStatusCode.Moved" /> 指示要求的資訊已經移至 Location 標頭中指定的 URI。在接收這個狀態時，預設動作會跟隨與回應相關的 Location 標頭。當原始的要求方法是 POST 時，重新導向的要求將使用 GET 方法。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.MovedPermanently">
      <summary>相當於 HTTP 狀態 301。<see cref="F:System.Net.HttpStatusCode.MovedPermanently" /> 指示要求的資訊已經移至 Location 標頭中指定的 URI。在接收這個狀態時，預設動作會跟隨與回應相關的 Location 標頭。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.MultipleChoices">
      <summary>相當於 HTTP 狀態 300。<see cref="F:System.Net.HttpStatusCode.MultipleChoices" /> 指示要求的資訊有多種表示法。預設動作是將這個狀態視為重新導向並跟隨在與這個回應相關的 Location 標頭內容之後。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.NoContent">
      <summary>相當於 HTTP 狀態 204。<see cref="F:System.Net.HttpStatusCode.NoContent" /> 表示已成功處理要求，並且回應預設為空白。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.NonAuthoritativeInformation">
      <summary>相當於 HTTP 狀態 203。<see cref="F:System.Net.HttpStatusCode.NonAuthoritativeInformation" /> 指示傳回的中繼資訊來自快取備份而非原始伺服器，因此可能不正確。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.NotAcceptable">
      <summary>相當於 HTTP 狀態 406。<see cref="F:System.Net.HttpStatusCode.NotAcceptable" /> 指示用戶端已經指示將不接受任何可用資源表示的 Accept 標頭。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.NotFound">
      <summary>相當於 HTTP 狀態 404。<see cref="F:System.Net.HttpStatusCode.NotFound" /> 指示要求的資源不存在於伺服器。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.NotImplemented">
      <summary>相當於 HTTP 狀態 501。<see cref="F:System.Net.HttpStatusCode.NotImplemented" /> 指示伺服器不支援要求的功能。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.NotModified">
      <summary>相當於 HTTP 狀態 304。<see cref="F:System.Net.HttpStatusCode.NotModified" /> 指示用戶端的快取備份已經是最新的。不傳輸資源的內容。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.OK">
      <summary>相當於 HTTP 狀態 200。<see cref="F:System.Net.HttpStatusCode.OK" /> 指示要求成功，並且要求的資訊在回應中。這是要接收的最通用狀態碼。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.PartialContent">
      <summary>相當於 HTTP 狀態 206。<see cref="F:System.Net.HttpStatusCode.PartialContent" /> 表示回應是包括位元組範圍之 GET 要求的部分回應。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.PaymentRequired">
      <summary>相當於 HTTP 狀態 402。<see cref="F:System.Net.HttpStatusCode.PaymentRequired" /> 保留供日後使用。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.PreconditionFailed">
      <summary>相當於 HTTP 狀態 412。<see cref="F:System.Net.HttpStatusCode.PreconditionFailed" /> 指示這個要求的條件設定失敗，並且無法執行要求。使用條件式要求標頭 (例如 If-Match、If-None-Match 或 If-Unmodified-Since) 設定條件。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.ProxyAuthenticationRequired">
      <summary>相當於 HTTP 狀態 407。<see cref="F:System.Net.HttpStatusCode.ProxyAuthenticationRequired" /> 指示要求的 Proxy 需要驗證。Proxy 驗證標頭包含如何執行驗證的詳細資料。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Redirect">
      <summary>相當於 HTTP 狀態 302。<see cref="F:System.Net.HttpStatusCode.Redirect" /> 指示要求的資訊位於 Location 標頭中所指定的 URI。在接收這個狀態時，預設動作會跟隨與回應相關的 Location 標頭。當原始的要求方法是 POST 時，重新導向的要求將使用 GET 方法。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.RedirectKeepVerb">
      <summary>相當於 HTTP 狀態 307。<see cref="F:System.Net.HttpStatusCode.RedirectKeepVerb" /> 表示要求資訊位於 Location 標頭中所指定的 URI。在接收這個狀態時，預設動作會跟隨與回應相關的 Location 標頭。當原始的要求方法是 POST 時，重新導向的要求也將使用 POST 方法。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.RedirectMethod">
      <summary>相當於 HTTP 狀態 303。<see cref="F:System.Net.HttpStatusCode.RedirectMethod" /> 自動將用戶端重新導向至 Location 標頭中指定的 URI，做為 POST 的結果。Location 標頭所指定的資源要求，將使用 GET 進行處理。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.RequestedRangeNotSatisfiable">
      <summary>相當於 HTTP 狀態 416。<see cref="F:System.Net.HttpStatusCode.RequestedRangeNotSatisfiable" /> 表示無法傳回資源所要求的資料範圍，可能是因為範圍的開頭在資源的開頭之前，或是範圍的結尾在資源的結尾之後。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.RequestEntityTooLarge">
      <summary>相當於 HTTP 狀態 413。<see cref="F:System.Net.HttpStatusCode.RequestEntityTooLarge" /> 指示要求太大，伺服器無法處理。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.RequestTimeout">
      <summary>相當於 HTTP 狀態 408。<see cref="F:System.Net.HttpStatusCode.RequestTimeout" /> 指示用戶端的要求未在伺服器期待要求時傳送。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.RequestUriTooLong">
      <summary>相當於 HTTP 狀態 414。<see cref="F:System.Net.HttpStatusCode.RequestUriTooLong" /> 指示 URI 過長。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.ResetContent">
      <summary>相當於 HTTP 狀態 205。<see cref="F:System.Net.HttpStatusCode.ResetContent" /> 指示用戶端應該重新設定 (非重新載入) 目前的資源。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.SeeOther">
      <summary>相當於 HTTP 狀態 303。<see cref="F:System.Net.HttpStatusCode.SeeOther" /> 自動將用戶端重新導向至 Location 標頭中指定的 URI，做為 POST 的結果。Location 標頭所指定的資源要求，將使用 GET 進行處理。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.ServiceUnavailable">
      <summary>相當於 HTTP 狀態 503。<see cref="F:System.Net.HttpStatusCode.ServiceUnavailable" /> 表示伺服器暫時無法使用，通常是因為高負載或維護的緣故。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.SwitchingProtocols">
      <summary>相當於 HTTP 狀態 101。<see cref="F:System.Net.HttpStatusCode.SwitchingProtocols" /> 指示正在變更通訊協定版本或通訊協定。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.TemporaryRedirect">
      <summary>相當於 HTTP 狀態 307。<see cref="F:System.Net.HttpStatusCode.TemporaryRedirect" /> 指示要求資訊位於 Location 標頭中所指定的 URI。在接收這個狀態時，預設動作會跟隨與回應相關的 Location 標頭。當原始的要求方法是 POST 時，重新導向的要求也將使用 POST 方法。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Unauthorized">
      <summary>相當於 HTTP 狀態 401。<see cref="F:System.Net.HttpStatusCode.Unauthorized" /> 指示要求的資源需要驗證。WWW-Authenticate 標頭包含如何執行驗證的詳細資料。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.UnsupportedMediaType">
      <summary>相當於 HTTP 狀態 415。<see cref="F:System.Net.HttpStatusCode.UnsupportedMediaType" /> 指示要求是不支援的類型。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Unused">
      <summary>相當於 HTTP 狀態 306。<see cref="F:System.Net.HttpStatusCode.Unused" /> 是 HTTP/1.1 規格未完全指定的建議擴充。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.UpgradeRequired">
      <summary>相當於 HTTP 狀態 426。<see cref="F:System.Net.HttpStatusCode.UpgradeRequired" /> 指示用戶端應該切換至不同的通訊協定，例如 TLS/1.0。</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.UseProxy">
      <summary>相當於 HTTP 狀態 305。<see cref="F:System.Net.HttpStatusCode.UseProxy" /> 指示要求應該使用位於 Location 標題中所指定 URI 的 Proxy 伺服器。</summary>
    </member>
    <member name="T:System.Net.ICredentials">
      <summary>提供擷取 Web 用戶端驗證 (Authentication) 認證的基底驗證介面。</summary>
    </member>
    <member name="M:System.Net.ICredentials.GetCredential(System.Uri,System.String)">
      <summary>傳回 <see cref="T:System.Net.NetworkCredential" /> 物件，這個物件與所指定的 URI 和驗證型別相關聯。</summary>
      <returns>
        <see cref="T:System.Net.NetworkCredential" />，與指定的 URI 和驗證類型相關聯，如果沒有可用的認證，則為 null。</returns>
      <param name="uri">
        <see cref="T:System.Uri" />，用戶端提供驗證給它。</param>
      <param name="authType">驗證的型別，如同在 <see cref="P:System.Net.IAuthenticationModule.AuthenticationType" /> 屬性中所定義的。</param>
    </member>
    <member name="T:System.Net.ICredentialsByHost">
      <summary>提供用來擷取主機、連接埠和驗證類別之認證的介面。</summary>
    </member>
    <member name="M:System.Net.ICredentialsByHost.GetCredential(System.String,System.Int32,System.String)">
      <summary>傳回指定主機、連接埠和驗證通訊協定的認證。</summary>
      <returns>指定主機、連接埠和驗證通訊協定的 <see cref="T:System.Net.NetworkCredential" />，如果指定主機、連接埠和驗證通訊協定沒有認證，則為 null。</returns>
      <param name="host">驗證用戶端的主機電腦。</param>
      <param name="port">用戶端將與之通訊的 <paramref name="host " />連接埠。</param>
      <param name="authenticationType">驗證通訊協定。</param>
    </member>
    <member name="T:System.Net.IPAddress">
      <summary>提供網際網路通訊協定 (IP) 位址。</summary>
    </member>
    <member name="M:System.Net.IPAddress.#ctor(System.Byte[])">
      <summary>使用指定為 <see cref="T:System.Byte" /> 陣列的位址，初始化 <see cref="T:System.Net.IPAddress" /> 類別的新執行個體。</summary>
      <param name="address">IP 位址的位元組陣列值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="address" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="address" /> 包含錯誤的 IP 位址。</exception>
    </member>
    <member name="M:System.Net.IPAddress.#ctor(System.Byte[],System.Int64)">
      <summary>使用指定為 <see cref="T:System.Byte" /> 陣列且具有指定之範圍識別項的位址，初始化 <see cref="T:System.Net.IPAddress" /> 類別的新執行個體。</summary>
      <param name="address">IP 位址的位元組陣列值。</param>
      <param name="scopeid">範圍識別項的長整數值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="address" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="address" /> 包含錯誤的 IP 位址。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="scopeid" /> &lt; 0 或<paramref name="scopeid" /> &gt; 0x00000000FFFFFFFF</exception>
    </member>
    <member name="M:System.Net.IPAddress.#ctor(System.Int64)">
      <summary>使用指定為 <see cref="T:System.Int64" /> 的位址，初始化 <see cref="T:System.Net.IPAddress" /> 類別的新執行個體。</summary>
      <param name="newAddress">IP 位址的長整數值。例如，位元組由大到小格式的 0x2414188f 值將為 IP 位址 "************"。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="newAddress" /> &lt; 0 或<paramref name="newAddress" /> &gt; 0x00000000FFFFFFFF</exception>
    </member>
    <member name="P:System.Net.IPAddress.AddressFamily">
      <summary>取得 IP 位址的位址家族。</summary>
      <returns>傳回 IPv4 的 <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" />，或傳回 IPv6 的 <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" />。</returns>
    </member>
    <member name="F:System.Net.IPAddress.Any">
      <summary>提供指示伺服器必須在所有網路介面上接聽用戶端活動的 IP 位址。這個欄位是唯讀的。</summary>
    </member>
    <member name="F:System.Net.IPAddress.Broadcast">
      <summary>提供 IP 廣播位址。這個欄位是唯讀的。</summary>
    </member>
    <member name="M:System.Net.IPAddress.Equals(System.Object)">
      <summary>比較兩個 IP 位址。</summary>
      <returns>如果兩個位址相等，則為 true，否則為 false。</returns>
      <param name="comparand">要與目前執行個體比較的 <see cref="T:System.Net.IPAddress" /> 執行個體。</param>
    </member>
    <member name="M:System.Net.IPAddress.GetAddressBytes">
      <summary>提供做為位元組陣列之 <see cref="T:System.Net.IPAddress" /> 的複本。</summary>
      <returns>
        <see cref="T:System.Byte" /> 陣列。</returns>
    </member>
    <member name="M:System.Net.IPAddress.GetHashCode">
      <summary>傳回 IP 位址的雜湊值 (Hash Value)。</summary>
      <returns>整數雜湊值。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.IPAddress.HostToNetworkOrder(System.Int16)">
      <summary>將短整數 (Short) 值從主機位元組順序轉換為網路位元組順序。</summary>
      <returns>以網路位元組順序表示的短整數值。</returns>
      <param name="host">以主機位元組順序表示之要轉換的數字。</param>
    </member>
    <member name="M:System.Net.IPAddress.HostToNetworkOrder(System.Int32)">
      <summary>將整數值從主機位元組順序轉換為網路位元組順序。</summary>
      <returns>以網路位元組順序表示的整數值。</returns>
      <param name="host">以主機位元組順序表示之要轉換的數字。</param>
    </member>
    <member name="M:System.Net.IPAddress.HostToNetworkOrder(System.Int64)">
      <summary>將長整數 (Long) 值從主機位元組順序轉換為網路位元組順序。</summary>
      <returns>以網路位元組順序表示的長整數值。</returns>
      <param name="host">以主機位元組順序表示之要轉換的數字。</param>
    </member>
    <member name="F:System.Net.IPAddress.IPv6Any">
      <summary>
        <see cref="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)" /> 方法使用 <see cref="F:System.Net.IPAddress.IPv6Any" /> 欄位來表示 <see cref="T:System.Net.Sockets.Socket" /> 必須在所有網路介面上接聽用戶端活動。</summary>
    </member>
    <member name="F:System.Net.IPAddress.IPv6Loopback">
      <summary>提供 IP 回送位址。這是唯讀的屬性。</summary>
    </member>
    <member name="F:System.Net.IPAddress.IPv6None">
      <summary>提供 IP 位址，表示不可使用網路介面。這是唯讀的屬性。</summary>
    </member>
    <member name="P:System.Net.IPAddress.IsIPv4MappedToIPv6">
      <summary>取得 IP 位址是否為對應 IPv4 的 IPv6 位址。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 IP 位址是對應 IPv4 的 IPv6 位址，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Net.IPAddress.IsIPv6LinkLocal">
      <summary>取得位址是否為 IPv6 連結本機位址的資訊。</summary>
      <returns>如果 IP 位址是 IPv6 連結本機位址，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Net.IPAddress.IsIPv6Multicast">
      <summary>取得位址是否為 IPv6 多點傳送全域位址的資訊。</summary>
      <returns>如果 IP 位址是 IPv6 多點傳送全域位址，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Net.IPAddress.IsIPv6SiteLocal">
      <summary>取得位址是否為 IPv6 站台本機位址的資訊。</summary>
      <returns>如果 IP 位址是 IPv6 站台本機位址，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Net.IPAddress.IsIPv6Teredo">
      <summary>取得值，這個值指出位址是否為 IPv6 Teredo 位址。</summary>
      <returns>如果 IP 位址為 IPv6 Teredo 位址則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Net.IPAddress.IsLoopback(System.Net.IPAddress)">
      <summary>指示指定的 IP 位址是否為回送位址。</summary>
      <returns>如果 <paramref name="address" /> 是回送位址，則為 true，否則為 false。</returns>
      <param name="address">IP 位址。</param>
    </member>
    <member name="F:System.Net.IPAddress.Loopback">
      <summary>提供 IP 回送位址。這個欄位是唯讀的。</summary>
    </member>
    <member name="M:System.Net.IPAddress.MapToIPv4">
      <summary>將 <see cref="T:System.Net.IPAddress" /> 物件對應至 IPv4 位址。</summary>
      <returns>傳回 <see cref="T:System.Net.IPAddress" />。IPv4 位址。</returns>
    </member>
    <member name="M:System.Net.IPAddress.MapToIPv6">
      <summary>將 <see cref="T:System.Net.IPAddress" /> 物件對應至 IPv6 位址。</summary>
      <returns>傳回 <see cref="T:System.Net.IPAddress" />。IPv6 位址。</returns>
    </member>
    <member name="M:System.Net.IPAddress.NetworkToHostOrder(System.Int16)">
      <summary>將短整數值從網路位元組順序轉換為主機位元組順序。</summary>
      <returns>以主機位元組順序表示的短整數值。</returns>
      <param name="network">以網路位元組順序表示之要轉換的數字。</param>
    </member>
    <member name="M:System.Net.IPAddress.NetworkToHostOrder(System.Int32)">
      <summary>將整數值從網路位元組順序轉換為主機位元組順序。</summary>
      <returns>以主機位元組順序表示的整數值。</returns>
      <param name="network">以網路位元組順序表示之要轉換的數字。</param>
    </member>
    <member name="M:System.Net.IPAddress.NetworkToHostOrder(System.Int64)">
      <summary>將長整數值從網路位元組順序轉換為主機位元組順序。</summary>
      <returns>以主機位元組順序表示的長整數值。</returns>
      <param name="network">以網路位元組順序表示之要轉換的數字。</param>
    </member>
    <member name="F:System.Net.IPAddress.None">
      <summary>提供 IP 位址，表示不可使用網路介面。這個欄位是唯讀的。</summary>
    </member>
    <member name="M:System.Net.IPAddress.Parse(System.String)">
      <summary>將 IP 位址字串轉換為 <see cref="T:System.Net.IPAddress" /> 執行個體。</summary>
      <returns>
        <see cref="T:System.Net.IPAddress" /> 執行個體。</returns>
      <param name="ipString">字串，包含使用 IPv4 點分隔四組數字標記法和 IPv6 冒號分隔十六進位標記法的 IP 位址。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ipString" /> 為 null。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="ipString" /> 不是有效的 IP 位址。</exception>
    </member>
    <member name="P:System.Net.IPAddress.ScopeId">
      <summary>取得或設定 IPv6 位址範圍識別項。</summary>
      <returns>指定位址範圍的長整數。</returns>
      <exception cref="T:System.Net.Sockets.SocketException">AddressFamily = InterNetwork.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="scopeId" /> &lt; 0-或-<paramref name="scopeId" /> &gt; 0x00000000FFFFFFFF</exception>
    </member>
    <member name="M:System.Net.IPAddress.ToString">
      <summary>將網際網路位址轉換為其標準標記法。</summary>
      <returns>字串，包含使用 IPv4 點分隔四組數字和 IPv6 冒號分隔十六進位之其中一種標記法的 IP 位址。</returns>
      <exception cref="T:System.Net.Sockets.SocketException">位址家族是 <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" />，而且位址是錯誤的。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.IPAddress.TryParse(System.String,System.Net.IPAddress@)">
      <summary>判斷字串是否為有效的 IP 位址。</summary>
      <returns>如果 <paramref name="ipString" /> 是有效的 IP 位址，則為 true，否則為 false。</returns>
      <param name="ipString">要驗證的字串。</param>
      <param name="address">字串的 <see cref="T:System.Net.IPAddress" /> 版本。</param>
    </member>
    <member name="T:System.Net.IPEndPoint">
      <summary>將網路端點表示成 IP 位址和通訊埠編號。</summary>
    </member>
    <member name="M:System.Net.IPEndPoint.#ctor(System.Int64,System.Int32)">
      <summary>使用指定的位址和通訊埠編號來初始化 <see cref="T:System.Net.IPEndPoint" /> 類別的新執行個體。</summary>
      <param name="address">網際網路主機的 IP 位址。</param>
      <param name="port">與 <paramref name="address" /> 相關聯的通訊埠編號，或 0 指定任何可用的通訊埠。<paramref name="port" /> 依主機順序顯示。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> 小於 <see cref="F:System.Net.IPEndPoint.MinPort" />。-或-<paramref name="port" /> 大於 <see cref="F:System.Net.IPEndPoint.MaxPort" />。-或-<paramref name="address" /> 小於 0 或大於 0x00000000FFFFFFFF。</exception>
    </member>
    <member name="M:System.Net.IPEndPoint.#ctor(System.Net.IPAddress,System.Int32)">
      <summary>使用指定的位址和通訊埠編號來初始化 <see cref="T:System.Net.IPEndPoint" /> 類別的新執行個體。</summary>
      <param name="address">
        <see cref="T:System.Net.IPAddress" />。</param>
      <param name="port">與 <paramref name="address" /> 相關聯的通訊埠編號，或 0 指定任何可用的通訊埠。<paramref name="port" /> 依主機順序顯示。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="address" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> 小於 <see cref="F:System.Net.IPEndPoint.MinPort" />。-或-<paramref name="port" /> 大於 <see cref="F:System.Net.IPEndPoint.MaxPort" />。-或-<paramref name="address" /> 小於 0 或大於 0x00000000FFFFFFFF。</exception>
    </member>
    <member name="P:System.Net.IPEndPoint.Address">
      <summary>取得或設定端點的 IP 位址。</summary>
      <returns>
        <see cref="T:System.Net.IPAddress" /> 執行個體，含有端點的 IP 位址。</returns>
    </member>
    <member name="P:System.Net.IPEndPoint.AddressFamily">
      <summary>取得網際網路通訊協定 (IP) 位址家族。</summary>
      <returns>傳回 <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" />。</returns>
    </member>
    <member name="M:System.Net.IPEndPoint.Create(System.Net.SocketAddress)">
      <summary>從通訊端 (Socket) 位址建立端點。</summary>
      <returns>
        <see cref="T:System.Net.EndPoint" /> 的執行個體，使用指定的通訊端位址。</returns>
      <param name="socketAddress">端點使用的 <see cref="T:System.Net.SocketAddress" />。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="socketAddress" /> 的 AddressFamily 不等於目前執行個體的 AddressFamily。-或-<paramref name="socketAddress" />.Size &lt; 8。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.IPEndPoint.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Object" /> 和目前的 <see cref="T:System.Net.IPEndPoint" /> 執行個體是否相等。</summary>
      <returns>如果指定的物件等於目前的物件，則為 true，否則為 false。</returns>
      <param name="comparand">要與目前 <see cref="T:System.Net.IPEndPoint" /> 執行個體比較的指定 <see cref="T:System.Object" />。</param>
    </member>
    <member name="M:System.Net.IPEndPoint.GetHashCode">
      <summary>傳回 <see cref="T:System.Net.IPEndPoint" /> 執行個體的雜湊值。</summary>
      <returns>整數雜湊值。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="F:System.Net.IPEndPoint.MaxPort">
      <summary>指定可指派給 <see cref="P:System.Net.IPEndPoint.Port" /> 屬性的最大值。MaxPort 值設為 0x0000FFFF。這個欄位是唯讀的。</summary>
    </member>
    <member name="F:System.Net.IPEndPoint.MinPort">
      <summary>指定可指派給 <see cref="P:System.Net.IPEndPoint.Port" /> 屬性的最小值。這個欄位是唯讀的。</summary>
    </member>
    <member name="P:System.Net.IPEndPoint.Port">
      <summary>取得或設定端點的通訊埠編號。</summary>
      <returns>範圍在 <see cref="F:System.Net.IPEndPoint.MinPort" /> 到 <see cref="F:System.Net.IPEndPoint.MaxPort" /> 之內的整數值，表示端點的通訊埠編號。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">指定給設定作業的值小於 <see cref="F:System.Net.IPEndPoint.MinPort" /> 或大於 <see cref="F:System.Net.IPEndPoint.MaxPort" />。</exception>
    </member>
    <member name="M:System.Net.IPEndPoint.Serialize">
      <summary>將端點資訊序列化為 <see cref="T:System.Net.SocketAddress" /> 執行個體。</summary>
      <returns>
        <see cref="T:System.Net.SocketAddress" /> 的執行個體，含有端點的通訊端位址。</returns>
    </member>
    <member name="M:System.Net.IPEndPoint.ToString">
      <summary>傳回指定端點的 IP 位址和通訊埠編號。</summary>
      <returns>字串，含有以點分隔四組數字標記法表示的 IP 位址，和指定端點的通訊埠編號 (例如，***********:80)。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.IWebProxy">
      <summary>提供 <see cref="T:System.Net.WebRequest" /> 類別 Proxy 存取實作 (Implementation) 的基底介面。</summary>
    </member>
    <member name="P:System.Net.IWebProxy.Credentials">
      <summary>認證，要送出至 Proxy 伺服器進行驗證。</summary>
      <returns>
        <see cref="T:System.Net.ICredentials" /> 執行個體 (Instance)，包含要驗證 Proxy 伺服器要求所需的認證。</returns>
    </member>
    <member name="M:System.Net.IWebProxy.GetProxy(System.Uri)">
      <summary>傳回 Proxy 的 URI。</summary>
      <returns>
        <see cref="T:System.Uri" /> 執行個體，包含用來聯繫 <paramref name="destination" /> 之 Proxy 的 URI。</returns>
      <param name="destination">
        <see cref="T:System.Uri" />，指定所要求的網際網路資源。</param>
    </member>
    <member name="M:System.Net.IWebProxy.IsBypassed(System.Uri)">
      <summary>指示不應該為指定的主機使用 Proxy。</summary>
      <returns>如果不應該為 <paramref name="host" /> 使用 Proxy 伺服器，則為 true，否則為 false。</returns>
      <param name="host">要檢查 Proxy 使用的主機 <see cref="T:System.Uri" />。</param>
    </member>
    <member name="T:System.Net.NetworkCredential">
      <summary>提供密碼架構的驗證 (Authentication) 機制 (例如基本、摘要、NTLM 和 Kerberos 驗證) 的認證。</summary>
    </member>
    <member name="M:System.Net.NetworkCredential.#ctor">
      <summary>初始化 <see cref="T:System.Net.NetworkCredential" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.NetworkCredential.#ctor(System.String,System.String)">
      <summary>使用指定的使用者名稱和密碼來初始化 <see cref="T:System.Net.NetworkCredential" /> 類別的新執行個體。</summary>
      <param name="userName">與認證相關的使用者名稱。</param>
      <param name="password">與認證相關的使用者名稱的密碼。</param>
    </member>
    <member name="M:System.Net.NetworkCredential.#ctor(System.String,System.String,System.String)">
      <summary>使用指定的使用者名稱、密碼和網域來初始化 <see cref="T:System.Net.NetworkCredential" /> 類別的新執行個體。</summary>
      <param name="userName">與認證相關的使用者名稱。</param>
      <param name="password">與認證相關的使用者名稱的密碼。</param>
      <param name="domain">與這些認證相關的網域。</param>
    </member>
    <member name="P:System.Net.NetworkCredential.Domain">
      <summary>取得或設定驗證認證的網域或電腦名稱。</summary>
      <returns>與認證相關的網域名稱。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.NetworkCredential.GetCredential(System.String,System.Int32,System.String)">
      <summary>傳回指定主機、連接埠和驗證類型之 <see cref="T:System.Net.NetworkCredential" /> 類別的執行個體。</summary>
      <returns>指定主機、連接埠和驗證通訊協定的 <see cref="T:System.Net.NetworkCredential" />，如果指定主機、連接埠和驗證通訊協定沒有認證，則為 null。</returns>
      <param name="host">驗證用戶端的主機電腦。</param>
      <param name="port">用戶端將與之通訊的 <paramref name="host" /> 連接埠。</param>
      <param name="authenticationType">要求驗證的類型，如同在 <see cref="P:System.Net.IAuthenticationModule.AuthenticationType" /> 屬性中的定義。</param>
    </member>
    <member name="M:System.Net.NetworkCredential.GetCredential(System.Uri,System.String)">
      <summary>傳回指定統一資源識別元 (URI) 和驗證類型之 <see cref="T:System.Net.NetworkCredential" /> 類別的執行個體。</summary>
      <returns>
        <see cref="T:System.Net.NetworkCredential" /> 物件。</returns>
      <param name="uri">用戶端為其提供驗證的 URI。</param>
      <param name="authType">要求驗證的類型，如同在 <see cref="P:System.Net.IAuthenticationModule.AuthenticationType" /> 屬性中的定義。</param>
    </member>
    <member name="P:System.Net.NetworkCredential.Password">
      <summary>取得或設定與認證相關的使用者名稱的密碼。</summary>
      <returns>與認證關聯的密碼。若此 <see cref="T:System.Net.NetworkCredential" /> 執行個體由 <paramref name="password" /> 參數初始化設定為  null，則 <see cref="P:System.Net.NetworkCredential.Password" /> 屬性會傳回空字串。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.NetworkCredential.UserName">
      <summary>取得或設定與認證相關的使用者名稱。</summary>
      <returns>與認證相關的使用者名稱。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.SocketAddress">
      <summary>儲存 <see cref="T:System.Net.EndPoint" /> 衍生類別的已序列化資訊。</summary>
    </member>
    <member name="M:System.Net.SocketAddress.#ctor(System.Net.Sockets.AddressFamily)">
      <summary>為指定的通訊協定家族 (Address Family) 建立 <see cref="T:System.Net.SocketAddress" /> 類別的新執行個體。</summary>
      <param name="family">
        <see cref="T:System.Net.Sockets.AddressFamily" /> 列舉值。</param>
    </member>
    <member name="M:System.Net.SocketAddress.#ctor(System.Net.Sockets.AddressFamily,System.Int32)">
      <summary>使用指定的通訊協定家族和緩衝區大小來初始化 <see cref="T:System.Net.SocketAddress" /> 類別的新執行個體。</summary>
      <param name="family">
        <see cref="T:System.Net.Sockets.AddressFamily" /> 列舉值。</param>
      <param name="size">要配置的基礎緩衝區位元組大小。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="size" /> 小於 2。需要這些 2 位元組，才能儲存 <paramref name="family" />。</exception>
    </member>
    <member name="M:System.Net.SocketAddress.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Object" /> 和目前的 <see cref="T:System.Net.SocketAddress" /> 執行個體是否相等。</summary>
      <returns>如果指定的物件等於目前的物件，則為 true，否則為 false。</returns>
      <param name="comparand">要與目前 <see cref="T:System.Net.SocketAddress" /> 執行個體比較的指定 <see cref="T:System.Object" />。</param>
    </member>
    <member name="P:System.Net.SocketAddress.Family">
      <summary>取得目前 <see cref="T:System.Net.SocketAddress" /> 的 <see cref="T:System.Net.Sockets.AddressFamily" /> 列舉值。</summary>
      <returns>一個 <see cref="T:System.Net.Sockets.AddressFamily" /> 列舉值。</returns>
    </member>
    <member name="M:System.Net.SocketAddress.GetHashCode">
      <summary>做為特定型別的雜湊函式，適用於雜湊演算法和資料結構中，例如雜湊表。</summary>
      <returns>目前物件的雜湊碼。</returns>
    </member>
    <member name="P:System.Net.SocketAddress.Item(System.Int32)">
      <summary>取得或設定基礎緩衝區中的指定索引項目。</summary>
      <returns>基礎緩衝區中指定索引項目的值。</returns>
      <param name="offset">所需資訊的陣列索引項目</param>
      <exception cref="T:System.IndexOutOfRangeException">指定的索引不存在於緩衝區中</exception>
    </member>
    <member name="P:System.Net.SocketAddress.Size">
      <summary>取得 <see cref="T:System.Net.SocketAddress" /> 的基礎緩衝區大小。</summary>
      <returns>
        <see cref="T:System.Net.SocketAddress" /> 的基礎緩衝區大小。</returns>
    </member>
    <member name="M:System.Net.SocketAddress.ToString">
      <summary>傳回通訊端 (Socket) 位址的相關資訊。</summary>
      <returns>字串，包含 <see cref="T:System.Net.SocketAddress" /> 的相關資訊。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.TransportContext">
      <summary>
        <see cref="T:System.Net.TransportContext" /> 類別，提供有關基礎傳輸層的其他內容。</summary>
    </member>
    <member name="M:System.Net.TransportContext.#ctor">
      <summary>建立 <see cref="T:System.Net.TransportContext" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.TransportContext.GetChannelBinding(System.Security.Authentication.ExtendedProtection.ChannelBindingKind)">
      <summary>擷取要求的通道繫結。</summary>
      <returns>要求的 <see cref="T:System.Security.Authentication.ExtendedProtection.ChannelBinding" />，如果目前的傳輸或作業系統不支援通道繫結，則為 null。</returns>
      <param name="kind">要擷取之通道繫結的型別。</param>
      <exception cref="T:System.NotSupportedException">
        <paramref name="kind" /> 必須是 <see cref="F:System.Security.Authentication.ExtendedProtection.ChannelBindingKind.Endpoint" /> 以搭配擷取自 <see cref="P:System.Net.HttpListenerRequest.TransportContext" /> 屬性的 <see cref="T:System.Net.TransportContext" /> 使用。</exception>
    </member>
    <member name="T:System.Net.NetworkInformation.IPAddressCollection">
      <summary>儲存一組 <see cref="T:System.Net.IPAddress" /> 型別。</summary>
    </member>
    <member name="M:System.Net.NetworkInformation.IPAddressCollection.#ctor">
      <summary>初始化 <see cref="T:System.Net.NetworkInformation.IPAddressCollection" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.NetworkInformation.IPAddressCollection.Add(System.Net.IPAddress)">
      <summary>擲回 <see cref="T:System.NotSupportedException" />，因為在這個集合中不支援這項作業。</summary>
      <param name="address">要加入至集合的物件。</param>
    </member>
    <member name="M:System.Net.NetworkInformation.IPAddressCollection.Clear">
      <summary>擲回 <see cref="T:System.NotSupportedException" />，因為在這個集合中不支援這項作業。</summary>
    </member>
    <member name="M:System.Net.NetworkInformation.IPAddressCollection.Contains(System.Net.IPAddress)">
      <summary>檢查集合是否包含指定的 <see cref="T:System.Net.IPAddress" /> 物件。</summary>
      <returns>如果集合中存在 <see cref="T:System.Net.IPAddress" /> 物件，則為 true，否則為 false。</returns>
      <param name="address">要在集合中搜尋的 <see cref="T:System.Net.IPAddress" /> 物件。</param>
    </member>
    <member name="M:System.Net.NetworkInformation.IPAddressCollection.CopyTo(System.Net.IPAddress[],System.Int32)">
      <summary>將此集合中的項目複製到型別 <see cref="T:System.Net.IPAddress" /> 的一維陣列中。</summary>
      <param name="array">接收集合之複本的一維陣列。</param>
      <param name="offset">
        <paramref name="array" /> 中以零起始的索引，會從這個位置開始複製。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 小於零。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 為多維。-或-這個 <see cref="T:System.Net.NetworkInformation.IPAddressCollection" /> 中的項目數大於從 <paramref name="offset" /> 至目的 <paramref name="array" /> 結尾處的可用空間。</exception>
      <exception cref="T:System.InvalidCastException">此 <see cref="T:System.Net.NetworkInformation.IPAddressCollection" /> 中的項目無法自動轉換成目的 <paramref name="array" /> 型別。</exception>
    </member>
    <member name="P:System.Net.NetworkInformation.IPAddressCollection.Count">
      <summary>取得這個集合中的 <see cref="T:System.Net.IPAddress" /> 型別數目。</summary>
      <returns>
        <see cref="T:System.Int32" /> 值，包含此集合中 <see cref="T:System.Net.IPAddress" /> 型別的數目。</returns>
    </member>
    <member name="M:System.Net.NetworkInformation.IPAddressCollection.GetEnumerator">
      <summary>傳回物件，可用來逐一查看這個集合。</summary>
      <returns>實作 <see cref="T:System.Collections.IEnumerator" /> 介面並提供此集合中 <see cref="T:System.Net.NetworkInformation.IPAddressCollection" /> 型別的存取權之物件。</returns>
    </member>
    <member name="P:System.Net.NetworkInformation.IPAddressCollection.IsReadOnly">
      <summary>取得值，指出此集合的存取是否為唯讀。</summary>
      <returns>所有情況下都是 true。</returns>
    </member>
    <member name="P:System.Net.NetworkInformation.IPAddressCollection.Item(System.Int32)">
      <summary>取得集合特定索引處的 <see cref="T:System.Net.IPAddress" />。</summary>
      <returns>集合中特定索引處的 <see cref="T:System.Net.IPAddress" />。</returns>
      <param name="index">想要的索引。</param>
    </member>
    <member name="M:System.Net.NetworkInformation.IPAddressCollection.Remove(System.Net.IPAddress)">
      <summary>擲回 <see cref="T:System.NotSupportedException" />，因為在這個集合中不支援這項作業。</summary>
      <returns>永遠擲回 <see cref="T:System.NotSupportedException" />。</returns>
      <param name="address">要移除的物件。</param>
    </member>
    <member name="M:System.Net.NetworkInformation.IPAddressCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回物件，可用來逐一查看這個集合。</summary>
      <returns>實作 <see cref="T:System.Collections.IEnumerator" /> 介面並提供此集合中 <see cref="T:System.Net.NetworkInformation.IPAddressCollection" /> 型別的存取權之物件。</returns>
    </member>
    <member name="T:System.Net.Security.AuthenticationLevel">
      <summary>當使用 <see cref="T:System.Net.WebRequest" /> 類別和衍生類別以要求資源時，指定用戶端的驗證和模擬需求。</summary>
    </member>
    <member name="F:System.Net.Security.AuthenticationLevel.MutualAuthRequested">
      <summary>用戶端和伺服器都應該驗證。如果伺服器未加以驗證，要求也不會失敗。若要判斷是否發生相互驗證，請檢查 <see cref="P:System.Net.WebResponse.IsMutuallyAuthenticated" /> 屬性的值。</summary>
    </member>
    <member name="F:System.Net.Security.AuthenticationLevel.MutualAuthRequired">
      <summary>用戶端和伺服器都應該驗證。如果伺服器未經驗證，您的應用程式就會收到具有 <see cref="T:System.Net.ProtocolViolationException" /> 內部例外狀況 (Exception) (表示相互驗證失敗) 的 <see cref="T:System.IO.IOException" />。</summary>
    </member>
    <member name="F:System.Net.Security.AuthenticationLevel.None">
      <summary>用戶端和伺服器都不需要驗證。</summary>
    </member>
    <member name="T:System.Net.Security.SslPolicyErrors">
      <summary>列舉 Secure Socket Layer (SSL) 原則錯誤。</summary>
    </member>
    <member name="F:System.Net.Security.SslPolicyErrors.None">
      <summary>無 SSL 原則錯誤。</summary>
    </member>
    <member name="F:System.Net.Security.SslPolicyErrors.RemoteCertificateChainErrors">
      <summary>
        <see cref="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainStatus" /> 已傳回非空白陣列。</summary>
    </member>
    <member name="F:System.Net.Security.SslPolicyErrors.RemoteCertificateNameMismatch">
      <summary>憑證名稱不符。</summary>
    </member>
    <member name="F:System.Net.Security.SslPolicyErrors.RemoteCertificateNotAvailable">
      <summary>憑證無法使用。</summary>
    </member>
    <member name="T:System.Net.Sockets.AddressFamily">
      <summary>指定 <see cref="T:System.Net.Sockets.Socket" /> 類別的執行個體 (Instance) 可以使用的位址配置。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.AppleTalk">
      <summary>AppleTalk 位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Atm">
      <summary>原生 (Native) ATM 服務位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Banyan">
      <summary>Banyan 位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Ccitt">
      <summary>CCITT 通訊協定位址，例如 X.25。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Chaos">
      <summary>MIT CHAOS 通訊協定位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Cluster">
      <summary>Microsoft 叢集產品位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.DataKit">
      <summary>Datakit 通訊協定位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.DataLink">
      <summary>直接資料連結介面位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.DecNet">
      <summary>DECnet 位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Ecma">
      <summary>歐洲電腦製造商協會 (ECMA) 位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.FireFox">
      <summary>FireFox 位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.HyperChannel">
      <summary>NSC Hyperchannel 位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Ieee12844">
      <summary>IEEE 1284.4 工作群組位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.ImpLink">
      <summary>ARPANET IMP 位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.InterNetwork">
      <summary>IP 第 4 版位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.InterNetworkV6">
      <summary>IP 第 6 版位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Ipx">
      <summary>IPX 或 SPX 位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Irda">
      <summary>IrDA 位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Iso">
      <summary>ISO 通訊協定的位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Lat">
      <summary>LAT 位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.NetBios">
      <summary>NetBios 位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.NetworkDesigners">
      <summary>Network Designers OSI 閘道器啟用通訊協定位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.NS">
      <summary>Xerox NS 通訊協定位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Osi">
      <summary>OSI 通訊協定的位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Pup">
      <summary>PUP 通訊協定的位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Sna">
      <summary>IBM SNA 位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Unix">
      <summary>Unix 本機對主機位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Unknown">
      <summary>不明的通訊協定家族 (Family)。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Unspecified">
      <summary>未指定的通訊協定家族。</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.VoiceView">
      <summary>VoiceView 位址。</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketError">
      <summary>定義 <see cref="T:System.Net.Sockets.Socket" /> 類別的錯誤碼。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.AccessDenied">
      <summary>嘗試以其存取權限所禁止的方式存取 <see cref="T:System.Net.Sockets.Socket" />。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.AddressAlreadyInUse">
      <summary>通常只允許使用位址一次。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.AddressFamilyNotSupported">
      <summary>不支援指定的通訊協定家族 (Family)。如果指定 IPv6 通訊協定家族而本機電腦上未安裝 IPv6 堆疊，就會傳回這個錯誤。如果指定 IPv4 通訊協定家族而本機電腦上未安裝 IPv4 堆疊，就會傳回這個錯誤。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.AddressNotAvailable">
      <summary>選取的 IP 位址在這個內容中無效。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.AlreadyInProgress">
      <summary>未封鎖的 <see cref="T:System.Net.Sockets.Socket" /> 中有一個作業尚未完成。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.ConnectionAborted">
      <summary>連接已由 .NET Framework 或基礎通訊端提供者中止。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.ConnectionRefused">
      <summary>遠端主機正在拒絕連接。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.ConnectionReset">
      <summary>連接已由遠端對等個體重設。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.DestinationAddressRequired">
      <summary>所需位址已從 <see cref="T:System.Net.Sockets.Socket" /> 上的作業中省略。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.Disconnecting">
      <summary>非失誤性的關機尚未完成。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.Fault">
      <summary>基礎通訊端提供者偵測到無效的指標位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.HostDown">
      <summary>作業失敗，因為遠端主機已關閉。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.HostNotFound">
      <summary>沒有已知的此類主機。名稱不是正式主機名稱或別名。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.HostUnreachable">
      <summary>沒有至指定主機的網路路由。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.InProgress">
      <summary>封鎖作業尚未完成。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.Interrupted">
      <summary>已取消封鎖 <see cref="T:System.Net.Sockets.Socket" /> 呼叫。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.InvalidArgument">
      <summary>向 <see cref="T:System.Net.Sockets.Socket" /> 成員提供的引數無效。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.IOPending">
      <summary>應用程式初始化了無法立即完成的重疊作業。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.IsConnected">
      <summary>已連接 <see cref="T:System.Net.Sockets.Socket" />。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.MessageSize">
      <summary>資料包太長。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.NetworkDown">
      <summary>網路無法使用。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.NetworkReset">
      <summary>應用程式嘗試在已逾時的連接上設定 <see cref="F:System.Net.Sockets.SocketOptionName.KeepAlive" />。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.NetworkUnreachable">
      <summary>不存在至遠端主機的路由。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.NoBufferSpaceAvailable">
      <summary>沒有可用於 <see cref="T:System.Net.Sockets.Socket" /> 作業的可用緩衝區空間。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.NoData">
      <summary>名稱伺服器上找不到所要求的名稱或 IP 位址。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.NoRecovery">
      <summary>錯誤無法復原，或找不到所要求的資料庫。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.NotConnected">
      <summary>應用程式嘗試傳送或接收資料，而 <see cref="T:System.Net.Sockets.Socket" /> 未連接。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.NotInitialized">
      <summary>基礎通訊端提供者尚未初始化。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.NotSocket">
      <summary>在非通訊端上嘗試 <see cref="T:System.Net.Sockets.Socket" /> 作業。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.OperationAborted">
      <summary>重疊作業因 <see cref="T:System.Net.Sockets.Socket" /> 的關閉而中止。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.OperationNotSupported">
      <summary>通訊協定家族不支援位址家族。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.ProcessLimit">
      <summary>使用基礎通訊端提供者的處理序過多。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.ProtocolFamilyNotSupported">
      <summary>未實作或尚未設定通訊協定家族。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.ProtocolNotSupported">
      <summary>未實作或尚未設定通訊協定。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.ProtocolOption">
      <summary>與 <see cref="T:System.Net.Sockets.Socket" /> 搭配使用了未知、無效或不受支援的選項或層級。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.ProtocolType">
      <summary>這個 <see cref="T:System.Net.Sockets.Socket" /> 的通訊協定類型不正確。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.Shutdown">
      <summary>不允許要求傳送或接收資料，因為 <see cref="T:System.Net.Sockets.Socket" /> 已關閉。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.SocketError">
      <summary>發生未指定的 <see cref="T:System.Net.Sockets.Socket" /> 錯誤。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.SocketNotSupported">
      <summary>這個地址家族中不存在對指定通訊端類型的支援。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.Success">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" /> 作業已成功。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.SystemNotReady">
      <summary>網路子系統無法使用。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.TimedOut">
      <summary>連接嘗試逾時，或連接的主機無法回應。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.TooManyOpenSockets">
      <summary>基礎通訊端提供者中開啟的通訊端過多。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.TryAgain">
      <summary>無法解析主機的名稱。請稍後再試。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.TypeNotFound">
      <summary>找不到指定的類別。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.VersionNotSupported">
      <summary>基礎通訊端提供者的版本超出範圍。</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.WouldBlock">
      <summary>無法立即完成未封鎖通訊端上的作業。</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketException">
      <summary>發生通訊端錯誤時所擲回的例外狀況。</summary>
    </member>
    <member name="M:System.Net.Sockets.SocketException.#ctor">
      <summary>使用最後一個作業系統錯誤碼，初始化 <see cref="T:System.Net.Sockets.SocketException" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.Sockets.SocketException.#ctor(System.Int32)">
      <summary>使用指定的錯誤碼，初始化 <see cref="T:System.Net.Sockets.SocketException" /> 類別的新執行個體。</summary>
      <param name="errorCode">錯誤碼，表示發生的錯誤。</param>
    </member>
    <member name="P:System.Net.Sockets.SocketException.Message">
      <summary>取得與這個例外狀況關聯的錯誤訊息。</summary>
      <returns>包含錯誤訊息的字串。</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketException.SocketErrorCode">
      <summary>取得與這個例外狀況關聯的錯誤碼。</summary>
      <returns>與這個例外狀況關聯的整數錯誤碼。</returns>
    </member>
    <member name="T:System.Security.Authentication.CipherAlgorithmType">
      <summary>定義 <see cref="T:System.Net.Security.SslStream" /> 類別可能的 Cipher 演算法。</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.Aes">
      <summary>先進加密標準 (Advanced Encryption Standard，AES) 演算法。</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.Aes128">
      <summary>使用 128 位元金鑰的先進加密標準 (AES) 演算法。</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.Aes192">
      <summary>使用 192 位元金鑰的先進加密標準 (AES) 演算法。</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.Aes256">
      <summary>使用 256 位元金鑰的先進加密標準 (AES) 演算法。</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.Des">
      <summary>資料加密標準 (Data Encryption Standard，DES) 演算法。</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.None">
      <summary>未使用加密演算法。</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.Null">
      <summary>Null Cipher 演算法未使用任何加密。</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.Rc2">
      <summary>Rivest's Code 2 (RC2) 演算法。</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.Rc4">
      <summary>Rivest's Code 4 (RC4) 演算法。</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.TripleDes">
      <summary>三重資料加密標準 (Triple Data Encryption Standard，3DES) 演算法。</summary>
    </member>
    <member name="T:System.Security.Authentication.ExchangeAlgorithmType">
      <summary>指定用來建立由用戶端和伺服器端共用之金鑰的演算法。</summary>
    </member>
    <member name="F:System.Security.Authentication.ExchangeAlgorithmType.DiffieHellman">
      <summary>Diffie Hellman 短暫金鑰交換演算法。</summary>
    </member>
    <member name="F:System.Security.Authentication.ExchangeAlgorithmType.None">
      <summary>未使用金鑰交換演算法。</summary>
    </member>
    <member name="F:System.Security.Authentication.ExchangeAlgorithmType.RsaKeyX">
      <summary>RSA 公開金鑰交換演算法。</summary>
    </member>
    <member name="F:System.Security.Authentication.ExchangeAlgorithmType.RsaSign">
      <summary>RSA 公開金鑰簽章演算法。</summary>
    </member>
    <member name="T:System.Security.Authentication.HashAlgorithmType">
      <summary>指定用於產生訊息驗證碼 (MAC) 的演算法。</summary>
    </member>
    <member name="F:System.Security.Authentication.HashAlgorithmType.Md5">
      <summary>訊息摘要 5 (Message Digest 5，MD5) 雜湊演算法。</summary>
    </member>
    <member name="F:System.Security.Authentication.HashAlgorithmType.None">
      <summary>未使用雜湊演算法。</summary>
    </member>
    <member name="F:System.Security.Authentication.HashAlgorithmType.Sha1">
      <summary>安全雜湊演算法 (Secure Hashing Algorithm，SHA1)。</summary>
    </member>
    <member name="T:System.Security.Authentication.SslProtocols">
      <summary>定義 <see cref="T:System.Security.Authentication.SslProtocols" /> 的可能版本。</summary>
    </member>
    <member name="F:System.Security.Authentication.SslProtocols.None">
      <summary>沒有指定的 SSL 通訊協定。</summary>
    </member>
    <member name="F:System.Security.Authentication.SslProtocols.Ssl2">
      <summary>指定 SSL 2.0 通訊協定。SSL 2.0 已被 TLS 通訊協定取代，且只提供做為回溯相容性之用。</summary>
    </member>
    <member name="F:System.Security.Authentication.SslProtocols.Ssl3">
      <summary>指定 SSL 3.0 通訊協定。SSL 3.0 已被 TLS 通訊協定取代，且只提供做為回溯相容性之用。</summary>
    </member>
    <member name="F:System.Security.Authentication.SslProtocols.Tls">
      <summary>指定 TLS 1.0 安全通訊協定。TLS 通訊協定是在 IETF RFC 2246 中定義。</summary>
    </member>
    <member name="F:System.Security.Authentication.SslProtocols.Tls11">
      <summary>指定 TLS 1.1 安全通訊協定。TLS 通訊協定是在 IETF RFC 4346 中定義。</summary>
    </member>
    <member name="F:System.Security.Authentication.SslProtocols.Tls12">
      <summary>指定 TLS 1.2 安全通訊協定。TLS 通訊協定是在 IETF RFC 5246 中定義。</summary>
    </member>
    <member name="T:System.Security.Authentication.ExtendedProtection.ChannelBinding">
      <summary>
        <see cref="T:System.Security.Authentication.ExtendedProtection.ChannelBinding" /> 類別會封裝不透明資料的指標，此資料用來將已驗證的交易繫結至安全的通道。</summary>
    </member>
    <member name="M:System.Security.Authentication.ExtendedProtection.ChannelBinding.#ctor">
      <summary>初始化 <see cref="T:System.Security.Authentication.ExtendedProtection.ChannelBinding" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Security.Authentication.ExtendedProtection.ChannelBinding.#ctor(System.Boolean)">
      <summary>初始化 <see cref="T:System.Security.Authentication.ExtendedProtection.ChannelBinding" /> 類別的新執行個體。</summary>
      <param name="ownsHandle">一種布林值，表示應用程式是否擁有包含位元組資料之原生記憶體區域的安全控制代碼。該位元組資料會傳遞至提供整合式 Windows 驗證延伸保護的原生呼叫。</param>
    </member>
    <member name="P:System.Security.Authentication.ExtendedProtection.ChannelBinding.Size">
      <summary>
        <see cref="P:System.Security.Authentication.ExtendedProtection.ChannelBinding.Size" /> 屬性會取得與 <see cref="T:System.Security.Authentication.ExtendedProtection.ChannelBinding" /> 執行個體相關聯之通道繫結語彙基元的大小 (以位元組為單位)。</summary>
      <returns>
        <see cref="T:System.Security.Authentication.ExtendedProtection.ChannelBinding" /> 執行個體中之通道繫結語彙基元的大小 (以位元組為單位)。</returns>
    </member>
    <member name="T:System.Security.Authentication.ExtendedProtection.ChannelBindingKind">
      <summary>
        <see cref="T:System.Security.Authentication.ExtendedProtection.ChannelBindingKind" /> 列舉，代表可透過安全通道加以查詢的通道繫結種類。</summary>
    </member>
    <member name="F:System.Security.Authentication.ExtendedProtection.ChannelBindingKind.Endpoint">
      <summary>指定端點的專屬通道繫結 (例如 TLS 伺服器憑證)。</summary>
    </member>
    <member name="F:System.Security.Authentication.ExtendedProtection.ChannelBindingKind.Unique">
      <summary>完全僅供指定通道使用的通道繫結 (例如 TLS 工作階段金鑰)。</summary>
    </member>
    <member name="F:System.Security.Authentication.ExtendedProtection.ChannelBindingKind.Unknown">
      <summary>未知的通道繫結型別。</summary>
    </member>
  </members>
</doc>