﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ObjectModel</name>
  </assembly>
  <members>
    <member name="T:System.Collections.ObjectModel.KeyedCollection`2">
      <summary>Fournit la classe de base abstraite pour une collection dont les clés sont incorporées dans les valeurs.</summary>
      <typeparam name="TKey">Type de clés de la collection.</typeparam>
      <typeparam name="TItem">Type d'éléments de la collection.</typeparam>
    </member>
    <member name="M:System.Collections.ObjectModel.KeyedCollection`2.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.ObjectModel.KeyedCollection`2" /> qui utilise le comparateur d'égalité par défaut.</summary>
    </member>
    <member name="M:System.Collections.ObjectModel.KeyedCollection`2.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.ObjectModel.KeyedCollection`2" /> qui utilise le comparateur d'égalité spécifié.</summary>
      <param name="comparer">Implémentation de l'interface générique <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> à utiliser pendant la comparaison des clés, ou null pour utiliser le comparateur d'égalité par défaut pour le type de la clé provenant de <see cref="P:System.Collections.Generic.EqualityComparer`1.Default" />.</param>
    </member>
    <member name="M:System.Collections.ObjectModel.KeyedCollection`2.#ctor(System.Collections.Generic.IEqualityComparer{`0},System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.ObjectModel.KeyedCollection`2" /> qui utilise le comparateur d'égalité spécifié et crée un dictionnaire de recherche lorsque le seuil spécifié est dépassé.</summary>
      <param name="comparer">Implémentation de l'interface générique <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> à utiliser pendant la comparaison des clés, ou null pour utiliser le comparateur d'égalité par défaut pour le type de la clé provenant de <see cref="P:System.Collections.Generic.EqualityComparer`1.Default" />.</param>
      <param name="dictionaryCreationThreshold">Nombre d'éléments que la collection peut contenir sans créer de dictionnaire de recherche (0 crée le dictionnaire de recherche lorsque le premier élément est ajouté) ou -1 pour spécifier qu'aucun dictionnaire de recherche ne doit être créé.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="dictionaryCreationThreshold" /> is less than –1.</exception>
    </member>
    <member name="M:System.Collections.ObjectModel.KeyedCollection`2.ChangeItemKey(`1,`0)">
      <summary>Modifie la clé associée à l'élément spécifié dans le dictionnaire de recherche.</summary>
      <param name="item">Clé de l'élément à modifier.</param>
      <param name="newKey">Nouvelle clé de <paramref name="item" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="item" /> is null.-or-<paramref name="key" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> is not found.-or-<paramref name="key" /> already exists in the <see cref="T:System.Collections.ObjectModel.KeyedCollection`2" />.</exception>
    </member>
    <member name="M:System.Collections.ObjectModel.KeyedCollection`2.ClearItems">
      <summary>Supprime tous les éléments de <see cref="T:System.Collections.ObjectModel.KeyedCollection`2" />.</summary>
    </member>
    <member name="P:System.Collections.ObjectModel.KeyedCollection`2.Comparer">
      <summary>Obtient le comparateur d'égalité générique utilisé pour déterminer l'égalité des clés dans la collection.</summary>
      <returns>Implémentation de l'interface générique <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> utilisée pour déterminer l'égalité des clés dans la collection.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.KeyedCollection`2.Contains(`0)">
      <summary>Détermine si la collection contient un élément avec la clé spécifiée.</summary>
      <returns>true si <see cref="T:System.Collections.ObjectModel.KeyedCollection`2" /> contient un élément correspondant à la clé spécifiée ; sinon, false.</returns>
      <param name="key">Clé à rechercher dans <see cref="T:System.Collections.ObjectModel.KeyedCollection`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="P:System.Collections.ObjectModel.KeyedCollection`2.Dictionary">
      <summary>Obtient le dictionnaire de recherche de <see cref="T:System.Collections.ObjectModel.KeyedCollection`2" />.</summary>
      <returns>Dictionnaire de recherche de <see cref="T:System.Collections.ObjectModel.KeyedCollection`2" />, s'il existe ; sinon, null.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.KeyedCollection`2.GetKeyForItem(`1)">
      <summary>Lorsqu'il est implémenté dans une classe dérivée, il extrait la clé de l'élément spécifié.</summary>
      <returns>Clé pour l'élément spécifié.</returns>
      <param name="item">Élément à partir duquel extraire la clé.</param>
    </member>
    <member name="M:System.Collections.ObjectModel.KeyedCollection`2.InsertItem(System.Int32,`1)">
      <summary>Insère un élément dans <see cref="T:System.Collections.ObjectModel.KeyedCollection`2" /> à l'index spécifié.</summary>
      <param name="index">Index de base zéro auquel <paramref name="item" /> doit être inséré.</param>
      <param name="item">Objet à insérer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.-or-<paramref name="index" /> is greater than <see cref="P:System.Collections.ObjectModel.Collection`1.Count" />.</exception>
    </member>
    <member name="P:System.Collections.ObjectModel.KeyedCollection`2.Item(`0)">
      <summary>Obtient l'élément avec la clé spécifiée. </summary>
      <returns>Élément correspondant à la clé spécifiée.Si un élément avec la clé spécifiée n'est pas trouvé, une exception est levée.</returns>
      <param name="key">Clé de l'élément à obtenir.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">An element with the specified key does not exist in the collection.</exception>
    </member>
    <member name="M:System.Collections.ObjectModel.KeyedCollection`2.Remove(`0)">
      <summary>Supprime de <see cref="T:System.Collections.ObjectModel.KeyedCollection`2" /> l'élément ayant la clé spécifiée.</summary>
      <returns>true si la suppression de l'élément réussit ; sinon, false.Cette méthode retourne également false si <paramref name="key" /> est introuvable dans <see cref="T:System.Collections.ObjectModel.KeyedCollection`2" />.</returns>
      <param name="key">Clé de l'élément à supprimer.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="M:System.Collections.ObjectModel.KeyedCollection`2.RemoveItem(System.Int32)">
      <summary>Supprime l'élément au niveau de l'index spécifié de <see cref="T:System.Collections.ObjectModel.KeyedCollection`2" />.</summary>
      <param name="index">Index de l'élément à supprimer.</param>
    </member>
    <member name="M:System.Collections.ObjectModel.KeyedCollection`2.SetItem(System.Int32,`1)">
      <summary>Remplace l'élément à l'index spécifié par l'élément spécifié.</summary>
      <param name="index">Index de base zéro de l'élément à remplacer.</param>
      <param name="item">Nouvel élément.</param>
    </member>
    <member name="T:System.Collections.ObjectModel.ObservableCollection`1">
      <summary>Représente une collection de données dynamiques qui fournit des notifications lorsque des éléments sont ajoutés, supprimés ou lorsque la liste entière est actualisée.</summary>
      <typeparam name="T">Type d'éléments de la collection.</typeparam>
    </member>
    <member name="M:System.Collections.ObjectModel.ObservableCollection`1.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.ObjectModel.ObservableCollection`1" />.</summary>
    </member>
    <member name="M:System.Collections.ObjectModel.ObservableCollection`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.ObjectModel.ObservableCollection`1" /> qui contient des éléments copiés depuis la collection spécifiée.</summary>
      <param name="collection">Collection à partir de laquelle les éléments sont copiés.</param>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="collection" /> ne peut pas être null.</exception>
    </member>
    <member name="M:System.Collections.ObjectModel.ObservableCollection`1.BlockReentrancy">
      <summary>Interdit les tentatives réentrantes destinées à modifier cette collection.</summary>
      <returns>Objet <see cref="T:System.IDisposable" /> qui peut être utilisé pour éliminer l'objet.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.ObservableCollection`1.CheckReentrancy">
      <summary>Vérifie les tentatives réentrantes destinées à modifier cette collection.</summary>
      <exception cref="T:System.InvalidOperationException">S'il y a eu un appel à <see cref="M:System.Collections.ObjectModel.ObservableCollection`1.BlockReentrancy" /> dont la valeur de retour <see cref="T:System.IDisposable" /> n'a pas encore été éliminée.En général, cela signifie qu'il existe d'autres tentatives destinées à modifier cette collection pendant un événement <see cref="E:System.Collections.ObjectModel.ObservableCollection`1.CollectionChanged" />.Toutefois, cela dépend du moment où les classes dérivées choisissent d'appeler <see cref="M:System.Collections.ObjectModel.ObservableCollection`1.BlockReentrancy" />.</exception>
    </member>
    <member name="M:System.Collections.ObjectModel.ObservableCollection`1.ClearItems">
      <summary>Supprime tous les éléments de la collection.</summary>
    </member>
    <member name="E:System.Collections.ObjectModel.ObservableCollection`1.CollectionChanged">
      <summary>Se produit lorsqu'un élément est ajouté, supprimé, modifié, déplacé ou lorsque la liste entière est actualisée.</summary>
    </member>
    <member name="M:System.Collections.ObjectModel.ObservableCollection`1.InsertItem(System.Int32,`0)">
      <summary>Insère un élément dans la collection à l'index spécifié.</summary>
      <param name="index">Index de base zéro auquel <paramref name="item" /> doit être inséré.</param>
      <param name="item">Objet à insérer.</param>
    </member>
    <member name="M:System.Collections.ObjectModel.ObservableCollection`1.Move(System.Int32,System.Int32)">
      <summary>Déplace l'élément à l'index spécifié vers un nouvel emplacement dans la collection.</summary>
      <param name="oldIndex">Index de base zéro qui spécifie l'emplacement de l'élément à déplacer.</param>
      <param name="newIndex">Index de base zéro qui spécifie le nouvel emplacement de l'élément.</param>
    </member>
    <member name="M:System.Collections.ObjectModel.ObservableCollection`1.MoveItem(System.Int32,System.Int32)">
      <summary>Déplace l'élément à l'index spécifié vers un nouvel emplacement dans la collection.</summary>
      <param name="oldIndex">Index de base zéro qui spécifie l'emplacement de l'élément à déplacer.</param>
      <param name="newIndex">Index de base zéro qui spécifie le nouvel emplacement de l'élément.</param>
    </member>
    <member name="M:System.Collections.ObjectModel.ObservableCollection`1.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
      <summary>Déclenche l'événement <see cref="E:System.Collections.ObjectModel.ObservableCollection`1.CollectionChanged" /> avec les arguments fournis.</summary>
      <param name="e">Arguments de l'événement déclenché.</param>
    </member>
    <member name="M:System.Collections.ObjectModel.ObservableCollection`1.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)">
      <summary>Déclenche l'événement <see cref="E:System.Collections.ObjectModel.ObservableCollection`1.PropertyChanged" /> avec les arguments fournis.</summary>
      <param name="e">Arguments de l'événement déclenché.</param>
    </member>
    <member name="E:System.Collections.ObjectModel.ObservableCollection`1.PropertyChanged">
      <summary>Se produit en cas de modification d'une valeur de propriété.</summary>
    </member>
    <member name="M:System.Collections.ObjectModel.ObservableCollection`1.RemoveItem(System.Int32)">
      <summary>Supprime l'élément au niveau de l'index spécifié dans la collection.</summary>
      <param name="index">Index de base zéro de l'élément à supprimer.</param>
    </member>
    <member name="M:System.Collections.ObjectModel.ObservableCollection`1.SetItem(System.Int32,`0)">
      <summary>Remplace l'élément au niveau de l'index spécifié.</summary>
      <param name="index">Index de base zéro de l'élément à remplacer.</param>
      <param name="item">Nouvelle valeur de l'élément à l'index spécifié.</param>
    </member>
    <member name="E:System.Collections.ObjectModel.ObservableCollection`1.System#ComponentModel#INotifyPropertyChanged#PropertyChanged">
      <summary>Se produit en cas de modification d'une valeur de propriété.</summary>
    </member>
    <member name="T:System.Collections.ObjectModel.ReadOnlyDictionary`2">
      <summary>Représente une collection générique en lecture seule de paires clé/valeur.</summary>
      <typeparam name="TKey">Type des clés du dictionnaire.</typeparam>
      <typeparam name="TValue">Type des valeurs du dictionnaire.</typeparam>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.ObjectModel.ReadOnlyDictionary`2" /> qui est un wrapper autour du dictionnaire spécifié.</summary>
      <param name="dictionary">Dictionnaire à inclure dans un wrapper.</param>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.ContainsKey(`0)">
      <summary>Détermine si le dictionnaire contient un élément avec la clé spécifiée.</summary>
      <returns>true si le dictionnaire contient un élément qui possède la clé spécifiée ; sinon, false.</returns>
      <param name="key">Clé à rechercher dans le dictionnaire.</param>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.Count">
      <summary>Obtient le nombre d'éléments dans le dictionnaire.</summary>
      <returns>Nombre d'éléments dans le dictionnaire.</returns>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.Dictionary">
      <summary>Obtient le dictionnaire qui est encapsulé dans un wrapper par cet objet <see cref="T:System.Collections.ObjectModel.ReadOnlyDictionary`2" />.</summary>
      <returns>Dictionnaire qui est inclus dans un wrapper par cet objet.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de <see cref="T:System.Collections.ObjectModel.ReadOnlyDictionary`2" />.</summary>
      <returns>Énumérateur permettant d'itérer au sein de la collection.</returns>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.Item(`0)">
      <summary>Obtient l'élément ayant la clé spécifiée.</summary>
      <returns>Élément qui contient la clé spécifiée.</returns>
      <param name="key">Clé de l'élément à obtenir.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">La propriété est récupérée et <paramref name="key" /> est introuvable.</exception>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.Keys">
      <summary>Obtient une collection de clés qui contient les clés du dictionnaire.</summary>
      <returns>Collection de clés qui contient les clés du dictionnaire.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Lève une exception <see cref="T:System.NotSupportedException" /> dans tous les cas.</summary>
      <param name="item">Objet à ajouter au dictionnaire.</param>
      <exception cref="T:System.NotSupportedException">dans tous les cas.</exception>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Lève une exception <see cref="T:System.NotSupportedException" /> dans tous les cas.</summary>
      <exception cref="T:System.NotSupportedException">dans tous les cas.</exception>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Détermine si le dictionnaire contient une valeur spécifique.</summary>
      <returns>true si <paramref name="item" /> se trouve dans le dictionnaire ; sinon, false.</returns>
      <param name="item">Objet à localiser dans le dictionnaire.</param>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>Copie les éléments du dictionnaire dans un tableau, en commençant au niveau d'un index de tableau spécifié.</summary>
      <param name="array">Tableau unidimensionnel qui constitue la destination des éléments copiés à partir du dictionnaire.Ce tableau doit avoir une indexation de base zéro.</param>
      <param name="arrayIndex">Index de base zéro dans <paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> est multidimensionnel.ouLe nombre d'éléments dans le dictionnaire source est supérieur à l'espace disponible entre <paramref name="arrayIndex" /> et la fin de l'<paramref name="array" /> de destination.ouLe type <paramref name="T" /> ne peut pas être casté automatiquement en type du <paramref name="array" /> de destination.</exception>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtient une valeur indiquant si le dictionnaire est en lecture seule.</summary>
      <returns>true dans tous les cas.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Lève une exception <see cref="T:System.NotSupportedException" /> dans tous les cas.</summary>
      <returns>Lève une exception <see cref="T:System.NotSupportedException" /> dans tous les cas.</returns>
      <param name="item">Objet à supprimer du dictionnaire.</param>
      <exception cref="T:System.NotSupportedException">dans tous les cas.</exception>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Add(`0,`1)">
      <summary>Lève une exception <see cref="T:System.NotSupportedException" /> dans tous les cas.</summary>
      <param name="key">Objet à utiliser comme clé de l'élément à ajouter.</param>
      <param name="value">Objet à utiliser comme valeur de l'élément à ajouter.</param>
      <exception cref="T:System.NotSupportedException">dans tous les cas.</exception>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Item(`0)">
      <summary>Obtient l'élément ayant la clé spécifiée.</summary>
      <returns>Élément qui contient la clé spécifiée.</returns>
      <param name="key">Clé de l'élément à obtenir ou définir.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">La propriété est récupérée et <paramref name="key" /> est introuvable.</exception>
      <exception cref="T:System.NotSupportedException">La propriété est définie.</exception>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Obtient une collection qui contient les clés du dictionnaire.</summary>
      <returns>Collection qui contient les clés de l'objet qui implémente <see cref="T:System.Collections.ObjectModel.ReadOnlyDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Remove(`0)">
      <summary>Lève une exception <see cref="T:System.NotSupportedException" /> dans tous les cas.</summary>
      <returns>Lève une exception <see cref="T:System.NotSupportedException" /> dans tous les cas.</returns>
      <param name="key">Clé de l'élément à supprimer.</param>
      <exception cref="T:System.NotSupportedException">dans tous les cas.</exception>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Obtient une collection qui contient les valeurs dans le dictionnaire.</summary>
      <returns>Collection qui contient les valeurs de l'objet qui implémente <see cref="T:System.Collections.ObjectModel.ReadOnlyDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Obtient une collection énumérable qui contient les clés dans dictionnaire en lecture seule. </summary>
      <returns>Collection énumérable qui contient les clés dans dictionnaire en lecture seule.</returns>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Obtient une collection énumérable qui contient les valeurs dans dictionnaire en lecture seule.</summary>
      <returns>Collection énumérable qui contient les valeurs dans dictionnaire en lecture seule.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copie les éléments du dictionnaire dans un tableau, en commençant au niveau d'un index de tableau spécifié.</summary>
      <param name="array">Tableau unidimensionnel qui constitue la destination des éléments copiés à partir du dictionnaire.Ce tableau doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans <paramref name="array" /> à partir duquel la copie commence. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à zéro. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> est multidimensionnel.ou Le nombre d'éléments dans le dictionnaire source est supérieur à l'espace disponible entre <paramref name="index" /> et la fin de l'<paramref name="array" /> de destination.ou Le type du dictionnaire source ne peut pas être automatiquement casté dans le type de l'<paramref name="array" /> de destination<paramref name="." /></exception>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>Obtient une valeur qui indique si l'accès au dictionnaire est synchronisé (thread-safe).</summary>
      <returns>true si l'accès au dictionnaire est synchronisé (thread-safe) ; sinon, false.</returns>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>Obtient un objet qui peut être utilisé pour synchroniser l'accès au dictionnaire.</summary>
      <returns>Objet qui peut être utilisé pour synchroniser l'accès au dictionnaire.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Lève une exception <see cref="T:System.NotSupportedException" /> dans tous les cas.</summary>
      <param name="key">Clé de l'élément à ajouter. </param>
      <param name="value">Valeur de l'élément à ajouter. </param>
      <exception cref="T:System.NotSupportedException">dans tous les cas.</exception>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#IDictionary#Clear">
      <summary>Lève une exception <see cref="T:System.NotSupportedException" /> dans tous les cas.</summary>
      <exception cref="T:System.NotSupportedException">dans tous les cas.</exception>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Détermine si le dictionnaire contient un élément avec la clé spécifiée.</summary>
      <returns>true si le dictionnaire contient un élément qui possède la clé spécifiée ; sinon, false.</returns>
      <param name="key">Clé à rechercher dans le dictionnaire.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null. </exception>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Retourne un énumérateur pour le dictionnaire.</summary>
      <returns>Énumérateur pour le dictionnaire.</returns>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Obtient une valeur qui indique si le dictionnaire est de taille fixe.</summary>
      <returns>true si le dictionnaire a une taille fixe ; sinon, false.</returns>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Obtient une valeur indiquant si le dictionnaire est en lecture seule.</summary>
      <returns>true dans tous les cas.</returns>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Obtient l'élément ayant la clé spécifiée.</summary>
      <returns>Élément qui contient la clé spécifiée.</returns>
      <param name="key">Clé de l'élément à obtenir ou définir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null. </exception>
      <exception cref="T:System.NotSupportedException">La propriété est définie.ou La propriété est définie, <paramref name="key" /> n'existe pas dans la collection et le dictionnaire a une taille fixe. </exception>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#IDictionary#Keys">
      <summary>Obtient une collection qui contient les clés du dictionnaire.</summary>
      <returns>Collection qui contient les clés du dictionnaire.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Lève une exception <see cref="T:System.NotSupportedException" /> dans tous les cas.</summary>
      <param name="key">Clé de l'élément à supprimer. </param>
      <exception cref="T:System.NotSupportedException">dans tous les cas.</exception>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#IDictionary#Values">
      <summary>Obtient une collection qui contient les valeurs dans le dictionnaire.</summary>
      <returns>Collection qui contient les valeurs dans le dictionnaire.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein d'une collection.</summary>
      <returns>Énumérateur permettant d'itérer au sein de la collection.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.TryGetValue(`0,`1@)">
      <summary>Récupère la valeur associée à la clé spécifiée.</summary>
      <returns>true si l'objet qui implémente <see cref="T:System.Collections.ObjectModel.ReadOnlyDictionary`2" /> contient un élément correspondant à la clé spécifiée ; sinon, false.</returns>
      <param name="key">Clé dont la valeur sera récupérée.</param>
      <param name="value">Lorsque cette méthode est retournée, la valeur associée à la clé spécifiée, si la clé est trouvée ; sinon, la valeur par défaut pour le type du paramètre <paramref name="value" />.Ce paramètre est passé sans être initialisé.</param>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.Values">
      <summary>Obtient une collection qui contient les valeurs dans le dictionnaire.</summary>
      <returns>Collection qui contient les valeurs de l'objet qui implémente <see cref="T:System.Collections.ObjectModel.ReadOnlyDictionary`2" />.</returns>
    </member>
    <member name="T:System.Collections.ObjectModel.ReadOnlyDictionary`2.KeyCollection">
      <summary>Représente une collection en lecture seule des clés d'un objet <see cref="T:System.Collections.ObjectModel.ReadOnlyDictionary`2" />.</summary>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>Copie les éléments de la collection dans un tableau, en commençant au niveau d'un index de tableau spécifique.</summary>
      <param name="array">Tableau unidimensionnel qui constitue la destination des éléments copiés à partir de la collection.Ce tableau doit avoir une indexation de base zéro.</param>
      <param name="arrayIndex">Index de base zéro dans <paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> est multidimensionnel.ouLe nombre d'éléments dans la collection source est supérieur à l'espace disponible entre <paramref name="arrayIndex" /> et la fin du <paramref name="array" /> de destination.ouLe type <paramref name="T" /> ne peut pas être casté automatiquement en type du <paramref name="array" /> de destination.</exception>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.KeyCollection.Count">
      <summary>Obtient le nombre d'éléments de la collection.</summary>
      <returns>Nombre d'éléments de la collection.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.KeyCollection.GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de la collection.</summary>
      <returns>Énumérateur permettant d'itérer au sein de la collection.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Lève une exception <see cref="T:System.NotSupportedException" /> dans tous les cas.</summary>
      <param name="item">Objet à ajouter à la collection.</param>
      <exception cref="T:System.NotSupportedException">dans tous les cas.</exception>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Lève une exception <see cref="T:System.NotSupportedException" /> dans tous les cas.</summary>
      <exception cref="T:System.NotSupportedException">dans tous les cas.</exception>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>Détermine si la collection contient une valeur spécifique.</summary>
      <returns>true si <paramref name="item" /> existe dans la collection ; sinon, false.</returns>
      <param name="item">Objet à localiser dans la collection.</param>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtient une valeur qui indique si la collection est en lecture seule.</summary>
      <returns>true dans tous les cas.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>Lève une exception <see cref="T:System.NotSupportedException" /> dans tous les cas.</summary>
      <returns>true si <paramref name="item" /> a été correctement supprimé de la collection ; sinon, false.Cette méthode retourne également false si <paramref name="item" /> est introuvable dans la collection d'origine.</returns>
      <param name="item">Objet à supprimer de la collection.</param>
      <exception cref="T:System.NotSupportedException">dans tous les cas.</exception>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copie les éléments de la collection dans un tableau, en commençant au niveau d'un index de tableau spécifique.</summary>
      <param name="array">Tableau unidimensionnel qui constitue la destination des éléments copiés à partir de la collection.Ce tableau doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans <paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> est multidimensionnel.ouLe nombre d'éléments dans la collection source est supérieur à l'espace disponible entre <paramref name="index" /> et la fin du <paramref name="array" /> de destination.</exception>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Obtient une valeur qui indique si l'accès à la collection est synchronisé (thread-safe).</summary>
      <returns>true si l'accès à la collection est synchronisé (thread-safe) ; sinon false.</returns>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>Obtient un objet qui peut être utilisé pour synchroniser l'accès à la collection.</summary>
      <returns>Objet permettant de synchroniser l'accès à la collection.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de la collection.</summary>
      <returns>Énumérateur permettant d'itérer au sein de la collection.</returns>
    </member>
    <member name="T:System.Collections.ObjectModel.ReadOnlyDictionary`2.ValueCollection">
      <summary>Représente une collection en lecture seule des valeurs d'un objet <see cref="T:System.Collections.ObjectModel.ReadOnlyDictionary`2" />.</summary>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>Copie les éléments de la collection dans un tableau, en commençant au niveau d'un index de tableau spécifique.</summary>
      <param name="array">Tableau unidimensionnel qui constitue la destination des éléments copiés à partir de la collection.Ce tableau doit avoir une indexation de base zéro.</param>
      <param name="arrayIndex">Index de base zéro dans <paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> est multidimensionnel.ouLe nombre d'éléments dans la collection source est supérieur à l'espace disponible entre <paramref name="arrayIndex" /> et la fin du <paramref name="array" /> de destination.ouLe type <paramref name="T" /> ne peut pas être casté automatiquement en type du <paramref name="array" /> de destination.</exception>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.ValueCollection.Count">
      <summary>Obtient le nombre d'éléments de la collection.</summary>
      <returns>Nombre d'éléments de la collection.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.ValueCollection.GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de la collection.</summary>
      <returns>Énumérateur permettant d'itérer au sein de la collection.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>Lève une exception <see cref="T:System.NotSupportedException" /> dans tous les cas.</summary>
      <param name="item">Objet à ajouter à la collection.</param>
      <exception cref="T:System.NotSupportedException">dans tous les cas.</exception>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Lève une exception <see cref="T:System.NotSupportedException" /> dans tous les cas.</summary>
      <exception cref="T:System.NotSupportedException">dans tous les cas.</exception>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>Détermine si la collection contient une valeur spécifique.</summary>
      <returns>true si <paramref name="item" /> existe dans la collection ; sinon, false.</returns>
      <param name="item">Objet à localiser dans la collection.</param>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtient une valeur qui indique si la collection est en lecture seule.</summary>
      <returns>true dans tous les cas.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>Lève une exception <see cref="T:System.NotSupportedException" /> dans tous les cas.</summary>
      <returns>true si <paramref name="item" /> a été correctement supprimé de la collection ; sinon, false.Cette méthode retourne également false si <paramref name="item" /> est introuvable dans la collection d'origine.</returns>
      <param name="item">Objet à supprimer de la collection.</param>
      <exception cref="T:System.NotSupportedException">dans tous les cas.</exception>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copie les éléments de la collection dans un tableau, en commençant au niveau d'un index de tableau spécifique.</summary>
      <param name="array">Tableau unidimensionnel qui constitue la destination des éléments copiés à partir de la collection.Ce tableau doit avoir une indexation de base zéro.</param>
      <param name="index">Index de base zéro dans <paramref name="array" /> à partir duquel la copie commence.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> est inférieur à 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> est multidimensionnel.ouLe nombre d'éléments dans la collection source est supérieur à l'espace disponible entre <paramref name="index" /> et la fin du <paramref name="array" /> de destination.</exception>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Obtient une valeur qui indique si l'accès à la collection est synchronisé (thread-safe).</summary>
      <returns>true si l'accès à la collection est synchronisé (thread-safe) ; sinon false.</returns>
    </member>
    <member name="P:System.Collections.ObjectModel.ReadOnlyDictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>Obtient un objet qui peut être utilisé pour synchroniser l'accès à la collection.</summary>
      <returns>Objet permettant de synchroniser l'accès à la collection.</returns>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyDictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de la collection.</summary>
      <returns>Énumérateur permettant d'itérer au sein de la collection.</returns>
    </member>
    <member name="T:System.Collections.ObjectModel.ReadOnlyObservableCollection`1">
      <summary>Représente une <see cref="T:System.Collections.ObjectModel.ObservableCollection`1" /> en lecture seule.</summary>
      <typeparam name="T">Type d'éléments de la collection.</typeparam>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyObservableCollection`1.#ctor(System.Collections.ObjectModel.ObservableCollection{`0})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.ObjectModel.ReadOnlyObservableCollection`1" /> qui sert de wrapper en lecture seule autour de la <see cref="T:System.Collections.ObjectModel.ObservableCollection`1" /> spécifiée.</summary>
      <param name="list">
        <see cref="T:System.Collections.ObjectModel.ObservableCollection`1" /> employé pour créer cette instance de la classe <see cref="T:System.Collections.ObjectModel.ReadOnlyObservableCollection`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="list" /> a la valeur null.</exception>
    </member>
    <member name="E:System.Collections.ObjectModel.ReadOnlyObservableCollection`1.CollectionChanged">
      <summary>Se produit lors de l'ajout ou de la suppression d'un élément.</summary>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyObservableCollection`1.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
      <summary>Déclenche l'événement <see cref="E:System.Collections.ObjectModel.ReadOnlyObservableCollection`1.CollectionChanged" /> à l'aide des arguments fournis.</summary>
      <param name="args">Arguments de l'événement déclenché.</param>
    </member>
    <member name="M:System.Collections.ObjectModel.ReadOnlyObservableCollection`1.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)">
      <summary>Déclenche l'événement <see cref="E:System.Collections.ObjectModel.ReadOnlyObservableCollection`1.PropertyChanged" /> à l'aide des arguments fournis.</summary>
      <param name="args">Arguments de l'événement déclenché.</param>
    </member>
    <member name="E:System.Collections.ObjectModel.ReadOnlyObservableCollection`1.PropertyChanged">
      <summary>Se produit en cas de modification d'une valeur de propriété.</summary>
    </member>
    <member name="E:System.Collections.ObjectModel.ReadOnlyObservableCollection`1.System#Collections#Specialized#INotifyCollectionChanged#CollectionChanged">
      <summary>Se produit lorsque la collection est modifiée.</summary>
    </member>
    <member name="E:System.Collections.ObjectModel.ReadOnlyObservableCollection`1.System#ComponentModel#INotifyPropertyChanged#PropertyChanged">
      <summary>Se produit en cas de modification d'une valeur de propriété.</summary>
    </member>
    <member name="T:System.Collections.Specialized.INotifyCollectionChanged">
      <summary>Notifie les écouteurs en cas de modification dynamique, comme lorsque des éléments sont ajoutés et supprimés ou lorsque la liste entière est actualisée.</summary>
    </member>
    <member name="E:System.Collections.Specialized.INotifyCollectionChanged.CollectionChanged">
      <summary>Se produit lorsque la collection est modifiée.</summary>
    </member>
    <member name="T:System.Collections.Specialized.NotifyCollectionChangedAction">
      <summary>Décrit l'action qui est à l'origine d'un événement <see cref="E:System.Collections.Specialized.INotifyCollectionChanged.CollectionChanged" />. </summary>
    </member>
    <member name="F:System.Collections.Specialized.NotifyCollectionChangedAction.Add">
      <summary>Un ou plusieurs éléments ont été ajoutés à la collection.</summary>
    </member>
    <member name="F:System.Collections.Specialized.NotifyCollectionChangedAction.Move">
      <summary>Un ou plusieurs éléments ont été déplacés dans la collection.</summary>
    </member>
    <member name="F:System.Collections.Specialized.NotifyCollectionChangedAction.Remove">
      <summary>Un ou plusieurs éléments ont été supprimés de la collection.</summary>
    </member>
    <member name="F:System.Collections.Specialized.NotifyCollectionChangedAction.Replace">
      <summary>Un ou plusieurs éléments ont été remplacés dans la collection.</summary>
    </member>
    <member name="F:System.Collections.Specialized.NotifyCollectionChangedAction.Reset">
      <summary>Le contenu de la collection a changé de manière significative.</summary>
    </member>
    <member name="T:System.Collections.Specialized.NotifyCollectionChangedEventArgs">
      <summary>Fournit des données pour l'événement <see cref="E:System.Collections.Specialized.INotifyCollectionChanged.CollectionChanged" />.</summary>
    </member>
    <member name="M:System.Collections.Specialized.NotifyCollectionChangedEventArgs.#ctor(System.Collections.Specialized.NotifyCollectionChangedAction)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Specialized.NotifyCollectionChangedEventArgs" /> qui décrit une modification <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Reset" />.</summary>
      <param name="action">Action qui a déclenché l'événement.Doit avoir la valeur <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Reset" />.</param>
    </member>
    <member name="M:System.Collections.Specialized.NotifyCollectionChangedEventArgs.#ctor(System.Collections.Specialized.NotifyCollectionChangedAction,System.Collections.IList)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Specialized.NotifyCollectionChangedEventArgs" /> qui décrit une modification de plusieurs éléments.</summary>
      <param name="action">Action qui a déclenché l'événement.Peut avoir la valeur <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Reset" />, <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Add" />, ou <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Remove" />.</param>
      <param name="changedItems">Éléments affectés par la modification.</param>
    </member>
    <member name="M:System.Collections.Specialized.NotifyCollectionChangedEventArgs.#ctor(System.Collections.Specialized.NotifyCollectionChangedAction,System.Collections.IList,System.Collections.IList)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Specialized.NotifyCollectionChangedEventArgs" /> qui décrit une modification <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Replace" /> de plusieurs éléments.</summary>
      <param name="action">Action qui a déclenché l'événement.Cette propriété peut uniquement avoir la valeur <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Replace" />.</param>
      <param name="newItems">Nouveaux éléments qui remplacent les éléments d'origine.</param>
      <param name="oldItems">Éléments d'origine qui ont été remplacés.</param>
      <exception cref="T:System.ArgumentException">Si <paramref name="action" /> n'est pas Replace.</exception>
      <exception cref="T:System.ArgumentNullException">Si <paramref name="oldItems" /> ou <paramref name="newItems" /> est Null.</exception>
    </member>
    <member name="M:System.Collections.Specialized.NotifyCollectionChangedEventArgs.#ctor(System.Collections.Specialized.NotifyCollectionChangedAction,System.Collections.IList,System.Collections.IList,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Specialized.NotifyCollectionChangedEventArgs" /> qui décrit une modification <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Replace" /> de plusieurs éléments.</summary>
      <param name="action">Action qui a déclenché l'événement.Cette propriété peut uniquement avoir la valeur <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Replace" />.</param>
      <param name="newItems">Nouveaux éléments qui remplacent les éléments d'origine.</param>
      <param name="oldItems">Éléments d'origine qui ont été remplacés.</param>
      <param name="startingIndex">Index du premier élément des éléments remplacés.</param>
      <exception cref="T:System.ArgumentException">Si <paramref name="action" /> n'est pas Replace.</exception>
      <exception cref="T:System.ArgumentNullException">Si <paramref name="oldItems" /> ou <paramref name="newItems" /> est Null.</exception>
    </member>
    <member name="M:System.Collections.Specialized.NotifyCollectionChangedEventArgs.#ctor(System.Collections.Specialized.NotifyCollectionChangedAction,System.Collections.IList,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Specialized.NotifyCollectionChangedEventArgs" /> qui décrit une modification de plusieurs éléments ou une modification <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Reset" />.</summary>
      <param name="action">Action qui a déclenché l'événement.Peut avoir la valeur <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Reset" />, <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Add" />, ou <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Remove" />.</param>
      <param name="changedItems">Éléments affectés par la modification.</param>
      <param name="startingIndex">Index auquel la modification s'est produite.</param>
      <exception cref="T:System.ArgumentException">Si <paramref name="action" /> n'est pas Reset, Add ou Remove, si <paramref name="action" /> est Reset et soit si <paramref name="changedItems" /> n'est pas Null, soit si <paramref name="startingIndex" /> n'est pas -1, ou si l'action est Add ou Remove et <paramref name="startingIndex" /> est inférieur à -1.</exception>
      <exception cref="T:System.ArgumentNullException">Si <paramref name="action" /> est Add ou Remove et <paramref name="changedItems" /> est Null.</exception>
    </member>
    <member name="M:System.Collections.Specialized.NotifyCollectionChangedEventArgs.#ctor(System.Collections.Specialized.NotifyCollectionChangedAction,System.Collections.IList,System.Int32,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Specialized.NotifyCollectionChangedEventArgs" /> qui décrit une modification <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Move" /> de plusieurs éléments.</summary>
      <param name="action">Action qui a déclenché l'événement.Cette propriété peut uniquement avoir la valeur <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Move" />.</param>
      <param name="changedItems">Éléments affectés par la modification.</param>
      <param name="index">Nouvel index pour les éléments modifiés.</param>
      <param name="oldIndex">Ancien index pour les éléments modifiés.</param>
      <exception cref="T:System.ArgumentException">Si <paramref name="action" /> n'est pas Move ou <paramref name="index" /> est inférieur à 0.</exception>
    </member>
    <member name="M:System.Collections.Specialized.NotifyCollectionChangedEventArgs.#ctor(System.Collections.Specialized.NotifyCollectionChangedAction,System.Object)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Specialized.NotifyCollectionChangedEventArgs" /> qui décrit une modification d'un élément.</summary>
      <param name="action">Action qui a déclenché l'événement.Peut avoir la valeur <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Reset" />, <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Add" />, ou <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Remove" />.</param>
      <param name="changedItem">Élément affecté par la modification.</param>
      <exception cref="T:System.ArgumentException">Si <paramref name="action" /> n'est pas Reset, Add ou Remove, ou si <paramref name="action" /> est Reset et <paramref name="changedItem" /> n'est pas Null.</exception>
    </member>
    <member name="M:System.Collections.Specialized.NotifyCollectionChangedEventArgs.#ctor(System.Collections.Specialized.NotifyCollectionChangedAction,System.Object,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Specialized.NotifyCollectionChangedEventArgs" /> qui décrit une modification d'un élément.</summary>
      <param name="action">Action qui a déclenché l'événement.Peut avoir la valeur <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Reset" />, <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Add" />, ou <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Remove" />.</param>
      <param name="changedItem">Élément affecté par la modification.</param>
      <param name="index">Index auquel la modification s'est produite.</param>
      <exception cref="T:System.ArgumentException">Si <paramref name="action" /> n'est pas Reset, Add ou Remove, ou si <paramref name="action" /> est Reset et soit si <paramref name="changedItems" /> n'est pas Null, soit si <paramref name="index" /> n'est pas -1.</exception>
    </member>
    <member name="M:System.Collections.Specialized.NotifyCollectionChangedEventArgs.#ctor(System.Collections.Specialized.NotifyCollectionChangedAction,System.Object,System.Int32,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Specialized.NotifyCollectionChangedEventArgs" /> qui décrit une modification <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Move" /> d'un élément.</summary>
      <param name="action">Action qui a déclenché l'événement.Cette propriété peut uniquement avoir la valeur <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Move" />.</param>
      <param name="changedItem">Élément affecté par la modification.</param>
      <param name="index">Nouvel index pour l'élément modifié.</param>
      <param name="oldIndex">Ancien index pour l'élément modifié.</param>
      <exception cref="T:System.ArgumentException">Si <paramref name="action" /> n'est pas Move ou <paramref name="index" /> est inférieur à 0.</exception>
    </member>
    <member name="M:System.Collections.Specialized.NotifyCollectionChangedEventArgs.#ctor(System.Collections.Specialized.NotifyCollectionChangedAction,System.Object,System.Object)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Specialized.NotifyCollectionChangedEventArgs" /> qui décrit une modification <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Replace" /> d'un élément.</summary>
      <param name="action">Action qui a déclenché l'événement.Cette propriété peut uniquement avoir la valeur <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Replace" />.</param>
      <param name="newItem">Nouvel élément qui remplace l'élément d'origine.</param>
      <param name="oldItem">Élément d'origine qui a été remplacé.</param>
      <exception cref="T:System.ArgumentException">Si <paramref name="action" /> n'est pas Replace.</exception>
    </member>
    <member name="M:System.Collections.Specialized.NotifyCollectionChangedEventArgs.#ctor(System.Collections.Specialized.NotifyCollectionChangedAction,System.Object,System.Object,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Collections.Specialized.NotifyCollectionChangedEventArgs" /> qui décrit une modification <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Replace" /> d'un élément.</summary>
      <param name="action">Action qui a déclenché l'événement.Peut avoir la valeur <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Replace" />.</param>
      <param name="newItem">Nouvel élément qui remplace l'élément d'origine.</param>
      <param name="oldItem">Élément d'origine qui a été remplacé.</param>
      <param name="index">Index de l'élément qui est remplacé.</param>
      <exception cref="T:System.ArgumentException">Si <paramref name="action" /> n'est pas Replace.</exception>
    </member>
    <member name="P:System.Collections.Specialized.NotifyCollectionChangedEventArgs.Action">
      <summary>Obtient l'action qui a déclenché l'événement. </summary>
      <returns>Valeur <see cref="T:System.Collections.Specialized.NotifyCollectionChangedAction" /> qui décrit l'action qui a déclenché l'événement.</returns>
    </member>
    <member name="P:System.Collections.Specialized.NotifyCollectionChangedEventArgs.NewItems">
      <summary>Obtient la liste des nouveaux éléments impliqués dans la modification.</summary>
      <returns>Liste des nouveaux éléments impliqués dans la modification.</returns>
    </member>
    <member name="P:System.Collections.Specialized.NotifyCollectionChangedEventArgs.NewStartingIndex">
      <summary>Obtient l'index auquel la modification s'est produite.</summary>
      <returns>Index de base zéro auquel la modification s'est produite.</returns>
    </member>
    <member name="P:System.Collections.Specialized.NotifyCollectionChangedEventArgs.OldItems">
      <summary>Obtient la liste des éléments affectés par une action <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Replace" />, Remove ou Move.</summary>
      <returns>Liste des éléments affectés par une action <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Replace" />, Remove ou Move.</returns>
    </member>
    <member name="P:System.Collections.Specialized.NotifyCollectionChangedEventArgs.OldStartingIndex">
      <summary>Obtient l'index sur lequel une action <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Move" />, Remove ou Replace s'est produite.</summary>
      <returns>Index de base zéro auquel une action <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Move" />, Remove ou Replace s'est produite</returns>
    </member>
    <member name="T:System.Collections.Specialized.NotifyCollectionChangedEventHandler">
      <summary>Représente la méthode qui gère l'événement <see cref="E:System.Collections.Specialized.INotifyCollectionChanged.CollectionChanged" />. </summary>
      <param name="sender">Objet ayant déclenché l'événement.</param>
      <param name="e">Informations relatives à l'événement.</param>
    </member>
    <member name="T:System.ComponentModel.DataErrorsChangedEventArgs">
      <summary>Fournit des données pour l'événement <see cref="E:System.ComponentModel.INotifyDataErrorInfo.ErrorsChanged" />.</summary>
    </member>
    <member name="M:System.ComponentModel.DataErrorsChangedEventArgs.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DataErrorsChangedEventArgs" />.</summary>
      <param name="propertyName">Nom de la propriété qui a une erreur.  null ou <see cref="F:System.String.Empty" /> si l'erreur se situe au niveau de l'objet.</param>
    </member>
    <member name="P:System.ComponentModel.DataErrorsChangedEventArgs.PropertyName">
      <summary>Obtient le nom de la propriété qui rencontre une erreur.</summary>
      <returns>Nom de la propriété qui rencontre une erreur.null ou <see cref="F:System.String.Empty" /> si l'erreur se situe au niveau de l'objet.</returns>
    </member>
    <member name="T:System.ComponentModel.INotifyDataErrorInfo">
      <summary>Définit des membres que les classes d'entité de données peuvent implémenter pour fournir une prise en charge personnalisée de la validation synchrone et asynchrone.</summary>
    </member>
    <member name="E:System.ComponentModel.INotifyDataErrorInfo.ErrorsChanged">
      <summary>Se produit lorsque les erreurs de validation ont été modifiées pour une propriété ou pour l'ensemble de l'entité. </summary>
    </member>
    <member name="M:System.ComponentModel.INotifyDataErrorInfo.GetErrors(System.String)">
      <summary>Obtient les erreurs de validation pour une propriété spécifiée ou pour l'ensemble de l'entité.</summary>
      <returns>Erreurs de validation pour la propriété ou l'entité.</returns>
      <param name="propertyName">Nom de la propriété pour laquelle récupérer les erreurs de validation ; ou null ou <see cref="F:System.String.Empty" /> pour récupérer les erreurs au niveau de l'entité.</param>
    </member>
    <member name="P:System.ComponentModel.INotifyDataErrorInfo.HasErrors">
      <summary>Obtient une valeur qui indique si l'entité comporte des erreurs de validation. </summary>
      <returns>true si l'entité comporte actuellement des erreurs de validation ; sinon, false.</returns>
    </member>
    <member name="T:System.ComponentModel.INotifyPropertyChanged">
      <summary>Notifie les clients qu'une valeur de propriété a été modifiée.</summary>
    </member>
    <member name="E:System.ComponentModel.INotifyPropertyChanged.PropertyChanged">
      <summary>Se produit en cas de modification d'une valeur de propriété.</summary>
    </member>
    <member name="T:System.ComponentModel.INotifyPropertyChanging">
      <summary>Notifie les clients qu'une valeur de propriété change.</summary>
    </member>
    <member name="E:System.ComponentModel.INotifyPropertyChanging.PropertyChanging">
      <summary>Se produit lorsqu'une valeur de propriété change.</summary>
    </member>
    <member name="T:System.ComponentModel.PropertyChangedEventArgs">
      <summary>Fournit des données pour l'événement <see cref="E:System.ComponentModel.INotifyPropertyChanged.PropertyChanged" />.</summary>
    </member>
    <member name="M:System.ComponentModel.PropertyChangedEventArgs.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.PropertyChangedEventArgs" />.</summary>
      <param name="propertyName">Nom de la propriété qui a été modifiée. </param>
    </member>
    <member name="P:System.ComponentModel.PropertyChangedEventArgs.PropertyName">
      <summary>Obtient le nom de la propriété qui a été modifiée.</summary>
      <returns>Nom de la propriété qui a été modifiée.</returns>
    </member>
    <member name="T:System.ComponentModel.PropertyChangedEventHandler">
      <summary>Représente la méthode qui gérera l'événement <see cref="E:System.ComponentModel.INotifyPropertyChanged.PropertyChanged" /> déclenché par la modification d'une propriété d'un composant.</summary>
      <param name="sender">Source de l'événement. </param>
      <param name="e">
        <see cref="T:System.ComponentModel.PropertyChangedEventArgs" /> qui contient les données de l'événement. </param>
    </member>
    <member name="T:System.ComponentModel.PropertyChangingEventArgs">
      <summary>Fournit des données pour l'événement <see cref="E:System.ComponentModel.INotifyPropertyChanging.PropertyChanging" />. </summary>
    </member>
    <member name="M:System.ComponentModel.PropertyChangingEventArgs.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.PropertyChangingEventArgs" />. </summary>
      <param name="propertyName">Nom de la propriété dont la valeur change.</param>
    </member>
    <member name="P:System.ComponentModel.PropertyChangingEventArgs.PropertyName">
      <summary>Obtient le nom de la propriété dont la valeur change.</summary>
      <returns>Nom de la propriété dont la valeur change.</returns>
    </member>
    <member name="T:System.ComponentModel.PropertyChangingEventHandler">
      <summary>Représente la méthode qui gérera l'événement <see cref="E:System.ComponentModel.INotifyPropertyChanging.PropertyChanging" /> d'une interface <see cref="T:System.ComponentModel.INotifyPropertyChanging" />. </summary>
      <param name="sender">Source de l'événement. </param>
      <param name="e">
        <see cref="T:System.ComponentModel.PropertyChangingEventArgs" /> qui contient les données d'événement.</param>
    </member>
    <member name="T:System.Windows.Input.ICommand">
      <summary>Définit une commande.</summary>
    </member>
    <member name="M:System.Windows.Input.ICommand.CanExecute(System.Object)">
      <summary>Définit la méthode qui détermine si la commande peut s'exécuter dans son état actuel.</summary>
      <returns>true si cette commande peut être exécutée ; sinon false.</returns>
      <param name="parameter">Données utilisées par la commande.Si la commande ne requiert pas que les données soient passées, cet objet peut avoir la valeur null.</param>
    </member>
    <member name="E:System.Windows.Input.ICommand.CanExecuteChanged">
      <summary>Se produit lorsque des modifications influent sur l'exécution de la commande.</summary>
    </member>
    <member name="M:System.Windows.Input.ICommand.Execute(System.Object)">
      <summary>Définit la méthode à appeler lorsque la commande est invoquée.</summary>
      <param name="parameter">Données utilisées par la commande.Si la commande ne requiert pas que les données soient passées, cet objet peut avoir la valeur null.</param>
    </member>
  </members>
</doc>