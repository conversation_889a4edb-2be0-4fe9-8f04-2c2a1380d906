﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Reflection.Extensions</name>
  </assembly>
  <members>
    <member name="T:System.Reflection.CustomAttributeExtensions">
      <summary>Contains static methods for retrieving custom attributes.</summary>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttribute``1(System.Reflection.Assembly)">
      <summary>Retrieves a custom attribute of a specified type that is applied to a specified assembly. </summary>
      <returns>A custom attribute that matches <paramref name="T" />, or null if no such attribute is found.</returns>
      <param name="element">The assembly to inspect.</param>
      <typeparam name="T">The type of attribute to search for.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null. </exception>
      <exception cref="T:System.Reflection.AmbiguousMatchException">More than one of the requested attributes was found. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttribute(System.Reflection.Assembly,System.Type)">
      <summary>Retrieves a custom attribute of a specified type that is applied to a specified assembly.</summary>
      <returns>A custom attribute that matches <paramref name="attributeType" />, or null if no such attribute is found.</returns>
      <param name="element">The assembly to inspect.</param>
      <param name="attributeType">The type of attribute to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> or <paramref name="attributeType" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not derived from <see cref="T:System.Attribute" />. </exception>
      <exception cref="T:System.Reflection.AmbiguousMatchException">More than one of the requested attributes was found. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttribute``1(System.Reflection.MemberInfo)">
      <summary>Retrieves a custom attribute of a specified type that is applied to a specified member.</summary>
      <returns>A custom attribute that matches <paramref name="T" />, or null if no such attribute is found.</returns>
      <param name="element">The member to inspect.</param>
      <typeparam name="T">The type of attribute to search for.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
      <exception cref="T:System.Reflection.AmbiguousMatchException">More than one of the requested attributes was found. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttribute``1(System.Reflection.MemberInfo,System.Boolean)">
      <summary>Retrieves a custom attribute of a specified type that is applied to a specified member, and optionally inspects the ancestors of that member.</summary>
      <returns>A custom attribute that matches <paramref name="T" />, or null if no such attribute is found.</returns>
      <param name="element">The member to inspect.</param>
      <param name="inherit">true to inspect the ancestors of <paramref name="element" />; otherwise, false. </param>
      <typeparam name="T">The type of attribute to search for.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
      <exception cref="T:System.Reflection.AmbiguousMatchException">More than one of the requested attributes was found. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttribute(System.Reflection.MemberInfo,System.Type)">
      <summary>Retrieves a custom attribute of a specified type that is applied to a specified member.</summary>
      <returns>A custom attribute that matches <paramref name="attributeType" />, or null if no such attribute is found.</returns>
      <param name="element">The member to inspect.</param>
      <param name="attributeType">The type of attribute to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> or <paramref name="attributeType" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not derived from <see cref="T:System.Attribute" />. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
      <exception cref="T:System.Reflection.AmbiguousMatchException">More than one of the requested attributes was found. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttribute(System.Reflection.MemberInfo,System.Type,System.Boolean)">
      <summary>Retrieves a custom attribute of a specified type that is applied to a specified member, and optionally inspects the ancestors of that member.</summary>
      <returns>A custom attribute that matches <paramref name="attributeType" />, or null if no such attribute is found.</returns>
      <param name="element">The member to inspect.</param>
      <param name="attributeType">The type of attribute to search for.</param>
      <param name="inherit">true to inspect the ancestors of <paramref name="element" />; otherwise, false. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> or <paramref name="attributeType" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not derived from <see cref="T:System.Attribute" />. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
      <exception cref="T:System.Reflection.AmbiguousMatchException">More than one of the requested attributes was found. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttribute``1(System.Reflection.Module)">
      <summary>Retrieves a custom attribute of a specified type that is applied to a specified module.</summary>
      <returns>A custom attribute that matches <paramref name="T" />, or null if no such attribute is found.</returns>
      <param name="element">The module to inspect.</param>
      <typeparam name="T">The type of attribute to search for.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null. </exception>
      <exception cref="T:System.Reflection.AmbiguousMatchException">More than one of the requested attributes was found. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttribute(System.Reflection.Module,System.Type)">
      <summary>Retrieves a custom attribute of a specified type that is applied to a specified module.</summary>
      <returns>A custom attribute that matches <paramref name="attributeType" />, or null if no such attribute is found.</returns>
      <param name="element">The module to inspect.</param>
      <param name="attributeType">The type of attribute to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> or <paramref name="attributeType" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not derived from <see cref="T:System.Attribute" />. </exception>
      <exception cref="T:System.Reflection.AmbiguousMatchException">More than one of the requested attributes was found. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttribute``1(System.Reflection.ParameterInfo)">
      <summary>Retrieves a custom attribute of a specified type that is applied to a specified parameter.</summary>
      <returns>A custom attribute that matches <paramref name="T" />, or null if no such attribute is found.</returns>
      <param name="element">The parameter to inspect.</param>
      <typeparam name="T">The type of attribute to search for.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
      <exception cref="T:System.Reflection.AmbiguousMatchException">More than one of the requested attributes was found. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttribute``1(System.Reflection.ParameterInfo,System.Boolean)">
      <summary>Retrieves a custom attribute of a specified type that is applied to a specified parameter, and optionally inspects the ancestors of that parameter.</summary>
      <returns>A custom attribute that matches <paramref name="T" />, or null if no such attribute is found.</returns>
      <param name="element">The parameter to inspect.</param>
      <param name="inherit">true to inspect the ancestors of <paramref name="element" />; otherwise, false. </param>
      <typeparam name="T">The type of attribute to search for.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
      <exception cref="T:System.Reflection.AmbiguousMatchException">More than one of the requested attributes was found. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttribute(System.Reflection.ParameterInfo,System.Type)">
      <summary>Retrieves a custom attribute of a specified type that is applied to a specified parameter.</summary>
      <returns>A custom attribute that matches <paramref name="attributeType" />, or null if no such attribute is found.</returns>
      <param name="element">The parameter to inspect.</param>
      <param name="attributeType">The type of attribute to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> or <paramref name="attributeType" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not derived from <see cref="T:System.Attribute" />. </exception>
      <exception cref="T:System.Reflection.AmbiguousMatchException">More than one of the requested attributes was found. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttribute(System.Reflection.ParameterInfo,System.Type,System.Boolean)">
      <summary>Retrieves a custom attribute of a specified type that is applied to a specified parameter, and optionally inspects the ancestors of that parameter.</summary>
      <returns>A custom attribute matching <paramref name="attributeType" />, or null if no such attribute is found.</returns>
      <param name="element">The parameter to inspect.</param>
      <param name="attributeType">The type of attribute to search for.</param>
      <param name="inherit">true to inspect the ancestors of <paramref name="element" />; otherwise, false. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> or <paramref name="attributeType" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not derived from <see cref="T:System.Attribute" />. </exception>
      <exception cref="T:System.Reflection.AmbiguousMatchException">More than one of the requested attributes was found. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttributes``1(System.Reflection.Assembly)">
      <summary>Retrieves a collection of custom attributes of a specified type that are applied to a specified assembly. </summary>
      <returns>A collection of the custom attributes that are applied to <paramref name="element" /> and that match <paramref name="T" />, or an empty collection if no such attributes exist. </returns>
      <param name="element">The assembly to inspect.</param>
      <typeparam name="T">The type of attribute to search for.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttributes(System.Reflection.Assembly)">
      <summary>Retrieves a collection of custom attributes that are applied to a specified assembly.</summary>
      <returns>A collection of the custom attributes that are applied to <paramref name="element" />, or an empty collection if no such attributes exist. </returns>
      <param name="element">The assembly to inspect.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttributes(System.Reflection.Assembly,System.Type)">
      <summary>Retrieves a collection of custom attributes of a specified type that are applied to a specified assembly.</summary>
      <returns>A collection of the custom attributes that are applied to <paramref name="element" /> and that match <paramref name="attributeType" />, or an empty collection if no such attributes exist. </returns>
      <param name="element">The assembly to inspect.</param>
      <param name="attributeType">The type of attribute to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> or <paramref name="attributeType" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not derived from <see cref="T:System.Attribute" />. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttributes(System.Reflection.MemberInfo)">
      <summary>Retrieves a collection of custom attributes that are applied to a specified member.</summary>
      <returns>A collection of the custom attributes that are applied to <paramref name="element" />, or an empty collection if no such attributes exist. </returns>
      <param name="element">The member to inspect.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttributes``1(System.Reflection.MemberInfo)">
      <summary>Retrieves a collection of custom attributes of a specified type that are applied to a specified member.</summary>
      <returns>A collection of the custom attributes that are applied to <paramref name="element" /> and that match <paramref name="T" />, or an empty collection if no such attributes exist. </returns>
      <param name="element">The member to inspect.</param>
      <typeparam name="T">The type of attribute to search for.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttributes``1(System.Reflection.MemberInfo,System.Boolean)">
      <summary>Retrieves a collection of custom attributes of a specified type that are applied to a specified member, and optionally inspects the ancestors of that member.</summary>
      <returns>A collection of the custom attributes that are applied to <paramref name="element" /> and that match <paramref name="T" />, or an empty collection if no such attributes exist. </returns>
      <param name="element">The member to inspect.</param>
      <param name="inherit">true to inspect the ancestors of <paramref name="element" />; otherwise, false. </param>
      <typeparam name="T">The type of attribute to search for.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttributes(System.Reflection.MemberInfo,System.Boolean)">
      <summary>Retrieves a collection of custom attributes that are applied to a specified member, and optionally inspects the ancestors of that member.</summary>
      <returns>A collection of the custom attributes that are applied to <paramref name="element" /> that match the specified criteria, or an empty collection if no such attributes exist. </returns>
      <param name="element">The member to inspect.</param>
      <param name="inherit">true to inspect the ancestors of <paramref name="element" />; otherwise, false. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttributes(System.Reflection.MemberInfo,System.Type)">
      <summary>Retrieves a collection of custom attributes of a specified type that are applied to a specified member.</summary>
      <returns>A collection of the custom attributes that are applied to <paramref name="element" /> and that match <paramref name="attributeType" />, or an empty collection if no such attributes exist. </returns>
      <param name="element">The member to inspect.</param>
      <param name="attributeType">The type of attribute to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> or <paramref name="attributeType" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not derived from <see cref="T:System.Attribute" />. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttributes(System.Reflection.MemberInfo,System.Type,System.Boolean)">
      <summary>Retrieves a collection of custom attributes of a specified type that are applied to a specified member, and optionally inspects the ancestors of that member.</summary>
      <returns>A collection of the custom attributes that are applied to <paramref name="element" /> and that match <paramref name="attributeType" />, or an empty collection if no such attributes exist.</returns>
      <param name="element">The member to inspect.</param>
      <param name="attributeType">The type of attribute to search for.</param>
      <param name="inherit">true to inspect the ancestors of <paramref name="element" />; otherwise, false. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> or <paramref name="attributeType" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not derived from <see cref="T:System.Attribute" />. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttributes(System.Reflection.Module)">
      <summary>Retrieves a collection of custom attributes that are applied to a specified module.</summary>
      <returns>A collection of the custom attributes that are applied to <paramref name="element" />, or an empty collection if no such attributes exist. </returns>
      <param name="element">The module to inspect.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttributes``1(System.Reflection.Module)">
      <summary>Retrieves a collection of custom attributes of a specified type that are applied to a specified module.</summary>
      <returns>A collection of the custom attributes that are applied to <paramref name="element" /> and that match <paramref name="T" />, or an empty collection if no such attributes exist. </returns>
      <param name="element">The module to inspect.</param>
      <typeparam name="T">The type of attribute to search for.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttributes(System.Reflection.Module,System.Type)">
      <summary>Retrieves a collection of custom attributes of a specified type that are applied to a specified module.</summary>
      <returns>A collection of the custom attributes that are applied to <paramref name="element" /> and that match <paramref name="attributeType" />, or an empty collection if no such attributes exist.</returns>
      <param name="element">The module to inspect.</param>
      <param name="attributeType">The type of attribute to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> or <paramref name="attributeType" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not derived from <see cref="T:System.Attribute" />. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttributes(System.Reflection.ParameterInfo)">
      <summary>Retrieves a collection of custom attributes that are applied to a specified parameter.</summary>
      <returns>A collection of the custom attributes that are applied to <paramref name="element" />, or an empty collection if no such attributes exist. </returns>
      <param name="element">The parameter to inspect.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttributes``1(System.Reflection.ParameterInfo)">
      <summary>Retrieves a collection of custom attributes of a specified type that are applied to a specified parameter.</summary>
      <returns>A collection of the custom attributes that are applied to <paramref name="element" /> and that match <paramref name="T" />, or an empty collection if no such attributes exist. </returns>
      <param name="element">The parameter to inspect.</param>
      <typeparam name="T">The type of attribute to search for.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttributes(System.Reflection.ParameterInfo,System.Boolean)">
      <summary>Retrieves a collection of custom attributes that are applied to a specified parameter, and optionally inspects the ancestors of that parameter.</summary>
      <returns>A collection of the custom attributes that are applied to <paramref name="element" />, or an empty collection if no such attributes exist. </returns>
      <param name="element">The parameter to inspect.</param>
      <param name="inherit">true to inspect the ancestors of <paramref name="element" />; otherwise, false. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttributes``1(System.Reflection.ParameterInfo,System.Boolean)">
      <summary>Retrieves a collection of custom attributes of a specified type that are applied to a specified parameter, and optionally inspects the ancestors of that parameter.</summary>
      <returns>A collection of the custom attributes that are applied to <paramref name="element" /> and that match <paramref name="T" />, or an empty collection if no such attributes exist. </returns>
      <param name="element">The parameter to inspect.</param>
      <param name="inherit">true to inspect the ancestors of <paramref name="element" />; otherwise, false. </param>
      <typeparam name="T">The type of attribute to search for.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> is null. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttributes(System.Reflection.ParameterInfo,System.Type)">
      <summary>Retrieves a collection of custom attributes of a specified type that are applied to a specified parameter.</summary>
      <returns>A collection of the custom attributes that are applied to <paramref name="element" /> and that match <paramref name="attributeType" />, or an empty collection if no such attributes exist. </returns>
      <param name="element">The parameter to inspect.</param>
      <param name="attributeType">The type of attribute to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> or <paramref name="attributeType" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not derived from <see cref="T:System.Attribute" />. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.GetCustomAttributes(System.Reflection.ParameterInfo,System.Type,System.Boolean)">
      <summary>Retrieves a collection of custom attributes of a specified type that are applied to a specified parameter, and optionally inspects the ancestors of that parameter.</summary>
      <returns>A collection of the custom attributes that are applied to <paramref name="element" /> and that match <paramref name="attributeType" />, or an empty collection if no such attributes exist. </returns>
      <param name="element">The parameter to inspect.</param>
      <param name="attributeType">The type of attribute to search for.</param>
      <param name="inherit">true to inspect the ancestors of <paramref name="element" />; otherwise, false. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> or <paramref name="attributeType" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not derived from <see cref="T:System.Attribute" />. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
      <exception cref="T:System.TypeLoadException">A custom attribute type cannot be loaded. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.IsDefined(System.Reflection.Assembly,System.Type)">
      <summary>Indicates whether custom attributes of a specified type are applied to a specified assembly.</summary>
      <returns>true if an attribute of the specified type is applied to <paramref name="element" />; otherwise, false.</returns>
      <param name="element">The assembly to inspect.</param>
      <param name="attributeType">The type of the attribute to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> or <paramref name="attributeType" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not derived from <see cref="T:System.Attribute" />. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.IsDefined(System.Reflection.MemberInfo,System.Type)">
      <summary>Indicates whether custom attributes of a specified type are applied to a specified member.</summary>
      <returns>true if an attribute of the specified type is applied to <paramref name="element" />; otherwise, false.</returns>
      <param name="element">The member to inspect.</param>
      <param name="attributeType">The type of attribute to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> or <paramref name="attributeType" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not derived from <see cref="T:System.Attribute" />. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.IsDefined(System.Reflection.MemberInfo,System.Type,System.Boolean)">
      <summary>Indicates whether custom attributes of a specified type are applied to a specified member, and, optionally, applied to its ancestors.</summary>
      <returns>true if an attribute of the specified type is applied to <paramref name="element" />; otherwise, false.</returns>
      <param name="element">The member to inspect.</param>
      <param name="attributeType">The type of the attribute to search for.</param>
      <param name="inherit">true to inspect the ancestors of <paramref name="element" />; otherwise, false. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> or <paramref name="attributeType" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not derived from <see cref="T:System.Attribute" />. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="element" /> is not a constructor, method, property, event, type, or field. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.IsDefined(System.Reflection.Module,System.Type)">
      <summary>Indicates whether custom attributes of a specified type are applied to a specified module.</summary>
      <returns>true if an attribute of the specified type is applied to <paramref name="element" />; otherwise, false.</returns>
      <param name="element">The module to inspect.</param>
      <param name="attributeType">The type of attribute to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> or <paramref name="attributeType" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not derived from <see cref="T:System.Attribute" />. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.IsDefined(System.Reflection.ParameterInfo,System.Type)">
      <summary>Indicates whether custom attributes of a specified type are applied to a specified parameter.</summary>
      <returns>true if an attribute of the specified type is applied to <paramref name="element" />; otherwise, false.</returns>
      <param name="element">The parameter to inspect.</param>
      <param name="attributeType">The type of attribute to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> or <paramref name="attributeType" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not derived from <see cref="T:System.Attribute" />. </exception>
    </member>
    <member name="M:System.Reflection.CustomAttributeExtensions.IsDefined(System.Reflection.ParameterInfo,System.Type,System.Boolean)">
      <summary>Indicates whether custom attributes of a specified type are applied to a specified parameter, and, optionally, applied to its ancestors.</summary>
      <returns>true if an attribute of the specified type is applied to <paramref name="element" />; otherwise, false.</returns>
      <param name="element">The parameter to inspect.</param>
      <param name="attributeType">The type of attribute to search for.</param>
      <param name="inherit">true to inspect the ancestors of <paramref name="element" />; otherwise, false. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="element" /> or <paramref name="attributeType" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not derived from <see cref="T:System.Attribute" />. </exception>
    </member>
    <member name="T:System.Reflection.InterfaceMapping">
      <summary>Retrieves the mapping of an interface into the actual methods on a class that implements that interface.</summary>
    </member>
    <member name="F:System.Reflection.InterfaceMapping.InterfaceMethods">
      <summary>Shows the methods that are defined on the interface.</summary>
    </member>
    <member name="F:System.Reflection.InterfaceMapping.InterfaceType">
      <summary>Shows the type that represents the interface.</summary>
    </member>
    <member name="F:System.Reflection.InterfaceMapping.TargetMethods">
      <summary>Shows the methods that implement the interface.</summary>
    </member>
    <member name="F:System.Reflection.InterfaceMapping.TargetType">
      <summary>Represents the type that was used to create the interface mapping.</summary>
    </member>
    <member name="T:System.Reflection.RuntimeReflectionExtensions">
      <summary>Provides methods that retrieve information about types at run time.</summary>
    </member>
    <member name="M:System.Reflection.RuntimeReflectionExtensions.GetMethodInfo(System.Delegate)">
      <summary>Gets an object that represents the method represented by the specified delegate.</summary>
      <returns>An object that represents the method.</returns>
      <param name="del">The delegate to examine.</param>
    </member>
    <member name="M:System.Reflection.RuntimeReflectionExtensions.GetRuntimeBaseDefinition(System.Reflection.MethodInfo)">
      <summary>Retrieves an object that represents the specified method on the direct or indirect base class where the method was first declared.</summary>
      <returns>An object that represents the specified method's initial declaration on a base class.</returns>
      <param name="method">The method to retrieve information about.</param>
    </member>
    <member name="M:System.Reflection.RuntimeReflectionExtensions.GetRuntimeEvent(System.Type,System.String)">
      <summary>Retrieves an object that represents the specified event.</summary>
      <returns>An object that represents the specified event, or null if the event is not found.</returns>
      <param name="type">The type that contains the event.</param>
      <param name="name">The name of the event.</param>
    </member>
    <member name="M:System.Reflection.RuntimeReflectionExtensions.GetRuntimeEvents(System.Type)">
      <summary>Retrieves a collection that represents all the events defined on a specified type.</summary>
      <returns>A collection of events for the specified type.</returns>
      <param name="type">The type that contains the events.</param>
    </member>
    <member name="M:System.Reflection.RuntimeReflectionExtensions.GetRuntimeField(System.Type,System.String)">
      <summary>Retrieves an object that represents a specified field.</summary>
      <returns>An object that represents the specified field, or null if the field is not found.</returns>
      <param name="type">The type that contains the field.</param>
      <param name="name">The name of the field.</param>
    </member>
    <member name="M:System.Reflection.RuntimeReflectionExtensions.GetRuntimeFields(System.Type)">
      <summary>Retrieves a collection that represents all the fields defined on a specified type.</summary>
      <returns>A collection of fields for the specified type.</returns>
      <param name="type">The type that contains the fields.</param>
    </member>
    <member name="M:System.Reflection.RuntimeReflectionExtensions.GetRuntimeInterfaceMap(System.Reflection.TypeInfo,System.Type)">
      <summary>Returns an interface mapping for the specified type and the specified interface.</summary>
      <returns>An object that represents the interface mapping for the specified interface and type.</returns>
      <param name="typeInfo">The type to retrieve a mapping for.</param>
      <param name="interfaceType">The interface to retrieve a mapping for.</param>
    </member>
    <member name="M:System.Reflection.RuntimeReflectionExtensions.GetRuntimeMethod(System.Type,System.String,System.Type[])">
      <summary>Retrieves an object that represents a specified method.</summary>
      <returns>An object that represents the specified method, or null if the method is not found.</returns>
      <param name="type">The type that contains the method.</param>
      <param name="name">The name of the method.</param>
      <param name="parameters">An array that contains the method's parameters.</param>
    </member>
    <member name="M:System.Reflection.RuntimeReflectionExtensions.GetRuntimeMethods(System.Type)">
      <summary>Retrieves a collection that represents all methods defined on a specified type.</summary>
      <returns>A collection of methods for the specified type.</returns>
      <param name="type">The type that contains the methods.</param>
    </member>
    <member name="M:System.Reflection.RuntimeReflectionExtensions.GetRuntimeProperties(System.Type)">
      <summary>Retrieves a collection that represents all the properties defined on a specified type.</summary>
      <returns>A collection of properties for the specified type.</returns>
      <param name="type">The type that contains the properties.</param>
    </member>
    <member name="M:System.Reflection.RuntimeReflectionExtensions.GetRuntimeProperty(System.Type,System.String)">
      <summary>Retrieves an object that represents a specified property.</summary>
      <returns>An object that represents the specified property, or null if the property is not found.</returns>
      <param name="type">The type that contains the property.</param>
      <param name="name">The name of the property.</param>
    </member>
  </members>
</doc>