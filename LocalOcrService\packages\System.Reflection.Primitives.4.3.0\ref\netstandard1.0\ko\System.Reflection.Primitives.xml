﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Reflection.Primitives</name>
  </assembly>
  <members>
    <member name="T:System.Reflection.CallingConventions">
      <summary>메서드에 대한 유효한 호출 규칙을 정의합니다.</summary>
    </member>
    <member name="F:System.Reflection.CallingConventions.Any">
      <summary>Standard 또는 VarArgs 호출 규칙 중 사용할 수 있는 호출 규칙을 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.CallingConventions.ExplicitThis">
      <summary>시그니처를 인스턴스 또는 가상 메서드(정적 메서드 아님)에 대한 호출을 나타내는 함수 포인터 서명으로 지정합니다.ExplicitThis를 설정하면 HasThis도 설정해야 합니다.호출된 메서드에 전달된 첫 번째 인수는 this 포인터지만 이 인수의 형식은 알 수 없습니다.그러므로 this 포인터의 형식 또는 클래스를 설명하는 토큰은 명시적으로 메타데이터 시그니처에 저장됩니다.</summary>
    </member>
    <member name="F:System.Reflection.CallingConventions.HasThis">
      <summary>인스턴스 또는 가상 메서드(정적 메서드가 아님)를 지정합니다.런타임에서, 호출된 메서드는 대상 개체를 가리키는 포인터에 첫 번째 인수(this 포인터)로 전달됩니다.메서드를 알고 있고 메타데이터에서 소유자 클래스를 검색할 수 있으므로 메타데이터에 저장된 시그니처에는 이 첫 번째 인수의 형식이 포함되지 않습니다.</summary>
    </member>
    <member name="F:System.Reflection.CallingConventions.Standard">
      <summary>공용 언어 런타임에서 결정한 대로 기본 호출 규칙을 지정합니다.정적 메서드에는 이 호출 규칙을 사용하고,인스턴스나 가상 메서드에는 HasThis를 사용합니다.</summary>
    </member>
    <member name="F:System.Reflection.CallingConventions.VarArgs">
      <summary>가변 인수를 사용하여 메서드에 대한 호출 규칙을 지정합니다.</summary>
    </member>
    <member name="T:System.Reflection.EventAttributes">
      <summary>이벤트의 특성을 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.EventAttributes.None">
      <summary>이벤트에 특성이 포함되지 않도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.EventAttributes.RTSpecialName">
      <summary>공용 언어 런타임에서 이름 인코딩을 확인하도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.EventAttributes.SpecialName">
      <summary>이름을 사용하여 설명하는 방법으로 이벤트를 지정합니다.</summary>
    </member>
    <member name="T:System.Reflection.FieldAttributes">
      <summary>필드의 특성을 설명하는 플래그를 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.FieldAttributes.Assembly">
      <summary>어셈블리 전체에서 필드에 액세스할 수 있도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.FieldAttributes.FamANDAssem">
      <summary>이 어셈블리의 하위 형식에서만 필드에 액세스할 수 있도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.FieldAttributes.Family">
      <summary>형식 및 하위 형식에서만 필드에 액세스할 수 있도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.FieldAttributes.FamORAssem">
      <summary>이 어셈블리 전체는 물론 모든 위치의 하위 형식에서 필드에 액세스할 수 있도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.FieldAttributes.FieldAccessMask">
      <summary>지정된 필드의 액세스 수준을 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.FieldAttributes.HasDefault">
      <summary>필드가 기본값을 갖도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.FieldAttributes.HasFieldMarshal">
      <summary>필드에 마샬링 정보가 포함되도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.FieldAttributes.HasFieldRVA">
      <summary>필드에 RVA(상대 가상 주소)가 포함되도록 지정합니다.RVA는 자신이 위치한 이미지 파일의 시작에 상대적인 주소로 현재 이미지에 있는 메서드 본문의 위치입니다.</summary>
    </member>
    <member name="F:System.Reflection.FieldAttributes.InitOnly">
      <summary>필드가 초기화만 되었으며 생성자 본문에서만 설정할 수 있음을 지정합니다. </summary>
    </member>
    <member name="F:System.Reflection.FieldAttributes.Literal">
      <summary>필드의 값이 정적 또는 초기 바인딩된 컴파일 타임 상수가 되도록 지정합니다.설정을 시도하면 <see cref="T:System.FieldAccessException" />이 throw됩니다.</summary>
    </member>
    <member name="F:System.Reflection.FieldAttributes.NotSerialized">
      <summary>원격 형식일 때 필드를 serialize하지 않도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.FieldAttributes.PinvokeImpl">
      <summary>다음에 사용하도록 예약됩니다.</summary>
    </member>
    <member name="F:System.Reflection.FieldAttributes.Private">
      <summary>부모 형식에서만 필드에 액세스할 수 있도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.FieldAttributes.PrivateScope">
      <summary>필드를 참조할 수 없도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.FieldAttributes.Public">
      <summary>이 범위를 볼 수 있는 모든 멤버가 필드에 액세스할 수 있도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.FieldAttributes.RTSpecialName">
      <summary>공용 언어 런타임(메타데이터 내부 API)에서 이름 인코딩을 확인하도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.FieldAttributes.SpecialName">
      <summary>메서드의 특수성을 설명하는 이름을 가진 특수 메서드를 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.FieldAttributes.Static">
      <summary>필드가 정의된 형식을 나타내도록 지정합니다. 그렇지 않으면 인스턴스 단위가 됩니다.</summary>
    </member>
    <member name="T:System.Reflection.GenericParameterAttributes">
      <summary>제네릭 형식 또는 메서드의 제네릭 형식 매개 변수에 대한 제약 조건을 설명합니다.</summary>
    </member>
    <member name="F:System.Reflection.GenericParameterAttributes.Contravariant">
      <summary>제네릭 형식 매개 변수가 반공변입니다.반공변 형식 매개 변수는 메서드 시그니처에서 매개 변수 형식으로 나타날 수 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.GenericParameterAttributes.Covariant">
      <summary>제네릭 형식 매개 변수가 공변입니다.공변 형식 매개 변수는 메서드의 결과 형식, 읽기 전용 필드의 형식, 선언된 기본 형식 또는 구현된 인터페이스로 나타날 수 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.GenericParameterAttributes.DefaultConstructorConstraint">
      <summary>제네릭 형식 매개 변수에 매개 변수 없는 생성자가 있는 경우에만 제네릭 형식 매개 변수의 형식을 대체할 수 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.GenericParameterAttributes.None">
      <summary>특수 플래그가 없습니다.</summary>
    </member>
    <member name="F:System.Reflection.GenericParameterAttributes.NotNullableValueTypeConstraint">
      <summary>제네릭 형식 매개 변수가 값 형식이고 null을 허용하지 않는 경우에만 제네릭 형식 매개 변수의 형식을 대체할 수 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.GenericParameterAttributes.ReferenceTypeConstraint">
      <summary>제네릭 형식 매개 변수가 참조 형식인 경우에만 제네릭 형식 매개 변수의 형식을 대체할 수 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.GenericParameterAttributes.SpecialConstraintMask">
      <summary>모든 특수 제약 조건 플래그의 조합을 선택합니다.이 값은 <see cref="F:System.Reflection.GenericParameterAttributes.DefaultConstructorConstraint" />, <see cref="F:System.Reflection.GenericParameterAttributes.ReferenceTypeConstraint" /> 및 <see cref="F:System.Reflection.GenericParameterAttributes.NotNullableValueTypeConstraint" /> 플래그를 논리 OR 연산으로 결합한 결과입니다.</summary>
    </member>
    <member name="F:System.Reflection.GenericParameterAttributes.VarianceMask">
      <summary>모든 가변성 플래그의 조합을 선택합니다.이 값은 <see cref="F:System.Reflection.GenericParameterAttributes.Contravariant" /> 및 <see cref="F:System.Reflection.GenericParameterAttributes.Covariant" /> 플래그를 논리 OR 연산으로 결합한 결과입니다.</summary>
    </member>
    <member name="T:System.Reflection.MethodAttributes">
      <summary>메서드 특성에 대한 플래그를 지정합니다.이러한 플래그는 corhdr.h 파일에 정의됩니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.Abstract">
      <summary>클래스에서 이 메서드를 구현하지 않습니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.Assembly">
      <summary>이 어셈블리의 모든 클래스에서 메서드에 액세스할 수 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.CheckAccessOnOverride">
      <summary>메서드에 액세스할 수 있을 때만 메서드를 재정의할 수 있음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.FamANDAssem">
      <summary>이 형식 및 이 어셈블리에 있는 파생 형식의 멤버만 메서드에 액세스할 수 있음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.Family">
      <summary>이 클래스 및 파생 클래스의 멤버만 메서드에 액세스할 수 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.FamORAssem">
      <summary>파생 클래스뿐만 아니라 어셈블리에 있는 모든 클래스가 메서드에 액세스할 수 있음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.Final">
      <summary>메서드를 재정의할 수 없음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.HasSecurity">
      <summary>메서드에 이와 관련된 보안이 있습니다.런타임 전용의 예약된 플래그입니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.HideBySig">
      <summary>메서드를 이름 및 시그니처별로 숨깁니다. 그렇지 않으면 이름별로만 숨깁니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.MemberAccessMask">
      <summary>액세스 가능성 정보를 검색합니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.NewSlot">
      <summary>메서드가 항상 vtable에 새 슬롯을 가져옵니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.PinvokeImpl">
      <summary>메서드 구현이 PInvoke(Platform Invocation Services)를 통해 전달됩니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.Private">
      <summary>현재 클래스만 메서드에 액세스할 수 있음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.PrivateScope">
      <summary>멤버를 참조할 수 없습니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.Public">
      <summary>이 개체 범위 내의 개체만 메서드에 액세스할 수 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.RequireSecObject">
      <summary>메서드가 보안 코드를 포함하는 다른 메서드를 호출합니다.런타임 전용의 예약된 플래그입니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.ReuseSlot">
      <summary>메서드에서 vtable의 기존 슬롯을 다시 사용합니다.이것은 기본적인 동작입니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.RTSpecialName">
      <summary>공용 언어 런타임에서 이름 인코딩을 확인합니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.SpecialName">
      <summary>특수한 메서드입니다.메서드의 이름은 이 메서드가 특수함을 나타냅니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.Static">
      <summary>메서드가 형식에 정의되어 있습니다. 그렇지 않으면 인스턴스 단위로 정의됩니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.UnmanagedExport">
      <summary>썽크에서 관리되는 메서드를 비관리 코드로 보냅니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.Virtual">
      <summary>가상 메서드입니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodAttributes.VtableLayoutMask">
      <summary>vtable 특성을 검색합니다.</summary>
    </member>
    <member name="T:System.Reflection.MethodImplAttributes">
      <summary>메서드 구현의 특성에 대한 플래그를 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodImplAttributes.AggressiveInlining">
      <summary>가능하면 메서드를 인라인되도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodImplAttributes.CodeTypeMask">
      <summary>코드 형식에 대한 플래그를 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodImplAttributes.ForwardRef">
      <summary>메서드가 정의되지 않도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodImplAttributes.IL">
      <summary>MSIL(Microsoft Intermediate Language)로 메서드를 구현하도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodImplAttributes.InternalCall">
      <summary>내부 호출을 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodImplAttributes.Managed">
      <summary>관리 코드에서 메서드를 구현하도록 지정합니다. </summary>
    </member>
    <member name="F:System.Reflection.MethodImplAttributes.ManagedMask">
      <summary>관리 코드 또는 비관리 코드에서 메서드를 구현하는지 여부를 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodImplAttributes.Native">
      <summary>네이티브 메서드 구현으로 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodImplAttributes.NoInlining">
      <summary>메서드가 인라인되지 않도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodImplAttributes.NoOptimization">
      <summary>발생 가능한 코드 생성 문제를 디버깅할 때 메서드가 JIT(Just-In-Time) 컴파일러 또는 네이티브 코드 생성(Ngen.exe 참조)에 의해 최적화되지 않도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodImplAttributes.OPTIL">
      <summary>OPTIL(Optimized Intermediate Language)로 메서드를 구현하도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodImplAttributes.PreserveSig">
      <summary>메서드 시그니처를 선언한 대로 정확하게 내보내도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodImplAttributes.Runtime">
      <summary>런타임에 메서드가 구현되도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodImplAttributes.Synchronized">
      <summary>본문을 통해 단일 스레드 메서드가 되도록 지정합니다.Static(Visual Basic에서는 Shared) 메서드는 형식을 잠그는 반면 인스턴스 메서드는 인스턴스를 잠급니다.또한 C# lock 문 또는 Visual Basic SyncLock 문을 사용해도 같은 결과를 얻을 수 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.MethodImplAttributes.Unmanaged">
      <summary>비관리 코드에서 메서드를 구현하도록 지정합니다.</summary>
    </member>
    <member name="T:System.Reflection.ParameterAttributes">
      <summary>매개 변수와 관련될 수 있는 특성을 정의합니다.이러한 특성은 CorHdr.h에 정의됩니다.</summary>
    </member>
    <member name="F:System.Reflection.ParameterAttributes.HasDefault">
      <summary>매개 변수가 기본값을 갖도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.ParameterAttributes.HasFieldMarshal">
      <summary>매개 변수에 필드 마샬링 정보가 포함되도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.ParameterAttributes.In">
      <summary>매개 변수가 입력 매개 변수가 되도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.ParameterAttributes.Lcid">
      <summary>매개 변수가 lcid(로캘 식별자)가 되도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.ParameterAttributes.None">
      <summary>매개 변수 특성이 없도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.ParameterAttributes.Optional">
      <summary>매개 변수를 선택적 요소로 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.ParameterAttributes.Out">
      <summary>매개 변수가 출력 매개 변수가 되도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.ParameterAttributes.Retval">
      <summary>매개 변수가 반환 값이 되도록 지정합니다.</summary>
    </member>
    <member name="T:System.Reflection.PropertyAttributes">
      <summary>속성과 관련될 수 있는 특성을 정의합니다.이러한 특성 값은 corhdr.h에 정의되어 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.PropertyAttributes.HasDefault">
      <summary>속성이 기본값을 갖도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.PropertyAttributes.None">
      <summary>속성과 관련된 특성이 없도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.PropertyAttributes.RTSpecialName">
      <summary>메타데이터 내부 API에서 이름 인코딩을 확인하도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.PropertyAttributes.SpecialName">
      <summary>속성의 특수성을 설명하는 이름을 사용하여 속성을 지정합니다.</summary>
    </member>
    <member name="T:System.Reflection.TypeAttributes">
      <summary>형식 특성을 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.Abstract">
      <summary>형식을 추상으로 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.AnsiClass">
      <summary>LPTSTR가 ANSI로 해석됩니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.AutoClass">
      <summary>LPTSTR가 자동으로 해석됩니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.AutoLayout">
      <summary>클래스 필드가 공용 언어 런타임에 의해 자동으로 레이아웃하도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.BeforeFieldInit">
      <summary>형식을 호출하는 정적 메서드가 시스템에서 해당 형식을 강제로 초기화하지 않도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.Class">
      <summary>형식을 클래스로 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.ClassSemanticsMask">
      <summary>클래스 기능 정보를 지정합니다. 현재 클래스에 컨텍스트를 추가(활성)할 수 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.CustomFormatClass">
      <summary>LPSTR는 <see cref="T:System.NotSupportedException" />이 throw될 가능성을 비롯하여 구현 방식에 따라 다르게 해석됩니다..NET Framework의 Microsoft 구현에서는 사용되지 않습니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.CustomFormatMask">
      <summary>네이티브 interop에 대한 비표준 인코딩 정보를 검색하는 데 사용됩니다.이러한 2비트 값의 의미는 지정되어 있지 않습니다..NET Framework의 Microsoft 구현에서는 사용되지 않습니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.ExplicitLayout">
      <summary>클래스 필드를 지정한 간격으로 레이아웃하도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.HasSecurity">
      <summary>형식에 관련 보안이 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.Import">
      <summary>클래스나 인터페이스를 다른 모듈에서 가져오도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.Interface">
      <summary>형식을 인터페이스로 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.LayoutMask">
      <summary>클래스 레이아웃 정보를 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.NestedAssembly">
      <summary>클래스를 어셈블리 표시 유형으로 중첩시켜 해당 클래스의 어셈블리 내의 메서드에서만 액세스할 수 있도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.NestedFamANDAssem">
      <summary>클래스를 어셈블리와 패밀리 표시 유형으로 중첩시켜 해당 클래스의 패밀리와 어셈블리의 교집합에 있는 메서드에서만 액세스할 수 있도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.NestedFamily">
      <summary>클래스를 패밀리 표시 유형으로 중첩시켜 클래스의 고유한 형식과 다른 파생 형식 내의 메서드에서만 액세스할 수 있도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.NestedFamORAssem">
      <summary>클래스를 패밀리나 어셈블리 표시 유형으로 중첩시켜 해당 클래스의 패밀리나 어셈블리의 합집합 속에 있는 메서드에서만 액세스할 수 있도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.NestedPrivate">
      <summary>클래스를 전용 표시 유형으로 중첩시키도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.NestedPublic">
      <summary>클래스를 공용 표시 유형으로 중첩시키도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.NotPublic">
      <summary>클래스를 공용이 아니도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.Public">
      <summary>클래스를 공용이 되도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.RTSpecialName">
      <summary>런타임에서 이름 인코딩을 검사해야 합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.Sealed">
      <summary>클래스를 고정시켜 확장되지 않도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.SequentialLayout">
      <summary>메타 데이터로 필드가 발생하도록 하기 위해 클래스 필드를 순차적으로 레이아웃하도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.Serializable">
      <summary>클래스를 serialize할 수 있도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.SpecialName">
      <summary>이름을 사용하여 설명하는 방법으로 클래스를 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.StringFormatMask">
      <summary>기본 상호 운용성의 문자열 정보를 검색하는 데 사용됩니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.UnicodeClass">
      <summary>LPTSTR가 유니코드로 해석됩니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.VisibilityMask">
      <summary>형식 표시 유형 정보를 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.TypeAttributes.WindowsRuntime">
      <summary>Windows 런타임 형식을 지정합니다.</summary>
    </member>
    <member name="T:System.Reflection.Emit.FlowControl">
      <summary>명령이 제어 흐름을 변경하는 방법을 설명합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.FlowControl.Branch">
      <summary>분기 명령입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.FlowControl.Break">
      <summary>중단 명령입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.FlowControl.Call">
      <summary>호출 명령입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.FlowControl.Cond_Branch">
      <summary>조건부 분기 명령입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.FlowControl.Meta">
      <summary>다음 명령에 대한 정보를 제공합니다.예를 들어 Reflection.Emit.Opcodes의 Unaligned 명령에는 FlowControl.Meta가 포함되어 있고, 이것은 다음 포인터 명령이 정렬되지 않을 수 있다는 것을 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.FlowControl.Next">
      <summary>일반적인 제어 흐름입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.FlowControl.Return">
      <summary>반환 명령입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.FlowControl.Throw">
      <summary>예외 throw 명령입니다.</summary>
    </member>
    <member name="T:System.Reflection.Emit.OpCode">
      <summary>IL(중간 언어) 명령을 설명합니다.</summary>
    </member>
    <member name="M:System.Reflection.Emit.OpCode.Equals(System.Object)">
      <summary>지정된 개체가 이 Opcode와 같은지 여부를 검사합니다.</summary>
      <returns>true if <paramref name="obj" /> is an instance of Opcode and is equal to this object; otherwise, false.</returns>
      <param name="obj">이 개체와 비교할 개체입니다. </param>
    </member>
    <member name="M:System.Reflection.Emit.OpCode.Equals(System.Reflection.Emit.OpCode)">
      <summary>현재 인스턴스와 지정된 <see cref="T:System.Reflection.Emit.OpCode" />이 같은지 여부를 나타냅니다.</summary>
      <returns>
        <paramref name="obj" />의 값이 현재 인스턴스의 값과 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 인스턴스와 비교할 <see cref="T:System.Reflection.Emit.OpCode" />입니다.</param>
    </member>
    <member name="P:System.Reflection.Emit.OpCode.FlowControl">
      <summary>IL(Microsoft Intermediate Language) 명령의 흐름 제어 특성입니다.</summary>
      <returns>읽기 전용입니다.흐름 제어의 형식입니다.</returns>
    </member>
    <member name="M:System.Reflection.Emit.OpCode.GetHashCode">
      <summary>이 Opcode의 생성된 해시 코드를 반환합니다.</summary>
      <returns>이 인스턴스의 해시 코드를 반환합니다.</returns>
    </member>
    <member name="P:System.Reflection.Emit.OpCode.Name">
      <summary>IL(중간 언어) 명령의 이름입니다.</summary>
      <returns>읽기 전용입니다.IL 명령의 이름입니다.</returns>
    </member>
    <member name="M:System.Reflection.Emit.OpCode.op_Equality(System.Reflection.Emit.OpCode,System.Reflection.Emit.OpCode)">
      <summary>두 개의 <see cref="T:System.Reflection.Emit.OpCode" /> 구조체가 같은지 여부를 나타냅니다.</summary>
      <returns>
        <paramref name="a" />가 <paramref name="b" />와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="a">
        <paramref name="b" />와 비교할 <see cref="T:System.Reflection.Emit.OpCode" />입니다.</param>
      <param name="b">
        <paramref name="a" />와 비교할 <see cref="T:System.Reflection.Emit.OpCode" />입니다.</param>
    </member>
    <member name="M:System.Reflection.Emit.OpCode.op_Inequality(System.Reflection.Emit.OpCode,System.Reflection.Emit.OpCode)">
      <summary>두 개의 <see cref="T:System.Reflection.Emit.OpCode" /> 구조체가 같지 않은지 여부를 나타냅니다.</summary>
      <returns>
        <paramref name="a" />가 <paramref name="b" />와 다르면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="a">
        <paramref name="b" />와 비교할 <see cref="T:System.Reflection.Emit.OpCode" />입니다.</param>
      <param name="b">
        <paramref name="a" />와 비교할 <see cref="T:System.Reflection.Emit.OpCode" />입니다.</param>
    </member>
    <member name="P:System.Reflection.Emit.OpCode.OpCodeType">
      <summary>IL(Intermediate Language) 명령의 형식입니다.</summary>
      <returns>읽기 전용입니다.IL(Intermediate Language) 명령의 형식입니다.</returns>
    </member>
    <member name="P:System.Reflection.Emit.OpCode.OperandType">
      <summary>IL(중간 언어) 명령의 피연산자 형식입니다.</summary>
      <returns>읽기 전용입니다.IL 명령의 피연산자 형식입니다.</returns>
    </member>
    <member name="P:System.Reflection.Emit.OpCode.Size">
      <summary>IL(중간 언어) 명령의 크기입니다.</summary>
      <returns>읽기 전용입니다.IL 명령의 크기입니다.</returns>
    </member>
    <member name="P:System.Reflection.Emit.OpCode.StackBehaviourPop">
      <summary>IL(중간 언어) 명령에서 스택을 팝하는 방법입니다.</summary>
      <returns>읽기 전용입니다.IL 명령에서 스택을 팝하는 방법입니다.</returns>
    </member>
    <member name="P:System.Reflection.Emit.OpCode.StackBehaviourPush">
      <summary>IL(중간 언어) 명령에서 스택에 피연산자를 푸시하는 방법입니다.</summary>
      <returns>읽기 전용입니다.IL 명령에서 스택에 피연산자를 푸시하는 방법입니다.</returns>
    </member>
    <member name="M:System.Reflection.Emit.OpCode.ToString">
      <summary>이 Opcode를 <see cref="T:System.String" />으로 반환합니다.</summary>
      <returns>이 Opcode의 이름이 포함된 <see cref="T:System.String" />을 반환합니다.</returns>
    </member>
    <member name="P:System.Reflection.Emit.OpCode.Value">
      <summary>IL(Intermediate language) 명령의 숫자 값을 가져옵니다.</summary>
      <returns>읽기 전용입니다.IL 명령의 숫자 값입니다.</returns>
    </member>
    <member name="T:System.Reflection.Emit.OpCodes">
      <summary>
        <see cref="M:System.Reflection.Emit.ILGenerator.Emit(System.Reflection.Emit.OpCode)" /> 같은 <see cref="T:System.Reflection.Emit.ILGenerator" /> 클래스 멤버를 사용한 내보내기 작업에 사용되는 MSIL(Microsoft Intermediate Language) 명령의 필드 표현을 제공합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Add">
      <summary>두 개 값을 더하여 결과를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Add_Ovf">
      <summary>두 정수를 더하고 오버플로를 검사하여 결과를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Add_Ovf_Un">
      <summary>부호 없는 두 정수 값을 더하고 오버플로를 검사하여 결과를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.And">
      <summary>두 값의 비트 AND를 계산하여 결과를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Arglist">
      <summary>현재 메서드의 인수 목록에 대한 관리되지 않는 포인터를 반환합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Beq">
      <summary>두 값이 같으면 대상 명령으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Beq_S">
      <summary>두 값이 같으면 대상 명령(약식)으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Bge">
      <summary>첫째 값이 둘째 값보다 크거나 같으면 대상 명령으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Bge_S">
      <summary>첫째 값이 둘째 값보다 크거나 같으면 대상 명령(약식)으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Bge_Un">
      <summary>부호 없는 정수 값 또는 순서가 지정되지 않은 부동 소수점 값을 비교한 결과 첫째 값이 둘째 값보다 크면 대상 명령으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Bge_Un_S">
      <summary>부호 없는 정수 값 또는 순서가 지정되지 않은 부동 소수점 값을 비교한 결과 첫째 값이 둘째 값보다 크면 대상 명령(약식)으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Bgt">
      <summary>첫째 값이 둘째 값보다 크면 대상 명령으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Bgt_S">
      <summary>첫째 값이 둘째 값보다 크면 대상 명령(약식)으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Bgt_Un">
      <summary>부호 없는 정수 값 또는 순서가 지정되지 않은 부동 소수점 값을 비교한 결과 첫째 값이 둘째 값보다 크면 대상 명령으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Bgt_Un_S">
      <summary>부호 없는 정수 값 또는 순서가 지정되지 않은 부동 소수점 값을 비교한 결과 첫째 값이 둘째 값보다 크면 대상 명령(약식)으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ble">
      <summary>첫째 값이 둘째 값보다 작거나 같으면 대상 명령으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ble_S">
      <summary>첫째 값이 둘째 값보다 작거나 같으면 대상 명령(약식)으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ble_Un">
      <summary>부호 없는 정수 값 또는 순서가 지정되지 않은 부동 소수점 값을 비교한 결과 첫째 값이 둘째 값보다 작거나 같으면 대상 명령으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ble_Un_S">
      <summary>부호 없는 정수 값 또는 순서가 지정되지 않은 부동 소수점 값을 비교한 결과 첫째 값이 둘째 값보다 작거나 같으면 대상 명령(약식)으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Blt">
      <summary>첫째 값이 둘째 값보다 작으면 대상 명령으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Blt_S">
      <summary>첫째 값이 둘째 값보다 작으면 대상 명령(약식)으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Blt_Un">
      <summary>부호 없는 정수 값 또는 순서가 지정되지 않은 부동 소수점 값을 비교한 결과 첫째 값이 둘째 값보다 작으면 대상 명령으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Blt_Un_S">
      <summary>부호 없는 정수 값 또는 순서가 지정되지 않은 부동 소수점 값을 비교한 결과 첫째 값이 둘째 값보다 작으면 대상 명령(약식)으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Bne_Un">
      <summary>두 개의 부호 없는 정수 값 또는 순서가 지정되지 않은 부동 소수점 값이 서로 다르면 대상 명령으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Bne_Un_S">
      <summary>두 개의 부호 없는 정수 값 또는 순서가 지정되지 않은 부동 소수점 값이 서로 다르면 대상 명령(약식)으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Box">
      <summary>값 형식을 개체 참조(O 형식)로 변환합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Br">
      <summary>조건에 상관 없이 대상 명령으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Br_S">
      <summary>조건에 상관 없이 대상 명령(약식)으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Break">
      <summary>중단점이 설정되었음을 디버거에 알리기 위해 CLI(공용 언어 인프라)에 신호를 보냅니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Brfalse">
      <summary>
        <paramref name="value" />가 false, null 참조(Visual Basic에서는 Nothing) 또는 0인 경우 대상 명령으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Brfalse_S">
      <summary>
        <paramref name="value" />가 false, null 참조 또는 0인 경우 대상 명령으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Brtrue">
      <summary>
        <paramref name="value" />가 true이거나 null이 아니거나 0이 아닌 경우 대상 명령으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Brtrue_S">
      <summary>
        <paramref name="value" />가 true이거나 null이 아니거나 0이 아닌 경우 대상 명령(약식)으로 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Call">
      <summary>전송된 메서드 설명자가 나타내는 메서드를 호출합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Calli">
      <summary>호출 규칙에서 설명하는 인수를 사용하여 계산 스택에 표시된 메서드를 진입점에 대한 포인터로 호출합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Callvirt">
      <summary>개체에서 런타임에 바인딩된 메서드를 호출하고 반환 값을 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Castclass">
      <summary>참조로 전송된 개체를 지정된 클래스로 캐스팅하려고 합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ceq">
      <summary>두 값을 비교합니다.두 값이 같으면 정수 값 1((int32)이 계산 스택으로 푸시되고, 그렇지 않으면 0(int32)이 계산 스택으로 푸시됩니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Cgt">
      <summary>두 값을 비교합니다.첫째 값이 둘째 값보다 크면 정수 값 1((int32)이 계산 스택으로 푸시되고, 그렇지 않으면 0(int32)이 계산 스택으로 푸시됩니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Cgt_Un">
      <summary>부호가 없거나 순서가 지정되지 않은 두 값을 비교합니다.첫째 값이 둘째 값보다 크면 정수 값 1((int32)이 계산 스택으로 푸시되고, 그렇지 않으면 0(int32)이 계산 스택으로 푸시됩니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ckfinite">
      <summary>값이 유한 값이 아니면 <see cref="T:System.ArithmeticException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Clt">
      <summary>두 값을 비교합니다.첫째 값이 둘째 값보다 작으면 정수 값 1((int32)이 계산 스택으로 푸시되고, 그렇지 않으면 0(int32)이 계산 스택으로 푸시됩니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Clt_Un">
      <summary>부호가 없거나 순서가 지정되지 않은 <paramref name="value1" />과 <paramref name="value2" />를 비교합니다.<paramref name="value1" />이 <paramref name="value2" />보다 작으면 정수 값 1((int32)이 계산 스택으로 푸시되고, 그렇지 않으면 0(int32)이 계산 스택으로 푸시됩니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Constrained">
      <summary>가상 메서드가 호출되는 형식을 제한합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_I">
      <summary>계산 스택 맨 위에 있는 값을 native int로 변환합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_I1">
      <summary>계산 스택 맨 위에 있는 값을 int8으로 변환하여 int32로 확장합니다(채웁니다).</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_I2">
      <summary>계산 스택 맨 위에 있는 값을 int16으로 변환하여 int32로 확장합니다(채웁니다).</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_I4">
      <summary>계산 스택 맨 위에 있는 값을 int32로 변환합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_I8">
      <summary>계산 스택 맨 위에 있는 값을 int64로 변환합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_I">
      <summary>계산 스택 맨 위에 있는 부호 있는 값을 부호 있는 native int로 변환하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_I_Un">
      <summary>계산 스택 맨 위에 있는 부호 없는 값을 부호 있는 native int로 변환하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_I1">
      <summary>계산 스택 맨 위에 있는 부호 있는 값을 부호 있는 int8로 변환하고 int32로 확장하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_I1_Un">
      <summary>계산 스택 맨 위에 있는 부호 없는 값을 부호 있는 int8로 변환하고 int32로 확장하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_I2">
      <summary>계산 스택 맨 위에 있는 부호 있는 값을 부호 있는 int16으로 변환하고 int32로 확장하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_I2_Un">
      <summary>계산 스택 맨 위에 있는 부호 없는 값을 부호 있는 int16로 변환하고 int32로 확장하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_I4">
      <summary>계산 스택 맨 위에 있는 부호 있는 값을 부호 있는 int32로 변환하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_I4_Un">
      <summary>계산 스택 맨 위에 있는 부호 없는 값을 부호 있는 int32로 변환하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_I8">
      <summary>계산 스택 맨 위에 있는 부호 있는 값을 부호 있는 int64로 변환하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_I8_Un">
      <summary>계산 스택 맨 위에 있는 부호 없는 값을 부호 있는 int64로 변환하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_U">
      <summary>계산 스택 맨 위에 있는 부호 있는 값을 unsigned native int로 변환하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_U_Un">
      <summary>계산 스택 맨 위에 있는 부호 없는 값을 unsigned native int로 변환하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_U1">
      <summary>계산 스택 맨 위에 있는 부호 있는 값을 unsigned int8로 변환하고 int32로 확장하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_U1_Un">
      <summary>계산 스택 맨 위에 있는 부호 없는 값을 unsigned int8로 변환하고 int32로 확장하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_U2">
      <summary>계산 스택 맨 위에 있는 부호 있는 값을 unsigned int16로 변환하고 int32로 확장하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_U2_Un">
      <summary>계산 스택 맨 위에 있는 부호 없는 값을 unsigned int16로 변환하고 int32로 확장하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_U4">
      <summary>계산 스택 맨 위에 있는 부호 있는 값을 unsigned int32로 변환하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_U4_Un">
      <summary>계산 스택 맨 위에 있는 부호 없는 값을 unsigned int32로 변환하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_U8">
      <summary>계산 스택 맨 위에 있는 부호 있는 값을 unsigned int64로 변환하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_Ovf_U8_Un">
      <summary>계산 스택 맨 위에 있는 부호 없는 값을 unsigned int64로 변환하고, 오버플로에 대한 <see cref="T:System.OverflowException" />을 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_R_Un">
      <summary>계산 스택 맨 위에 있는 부호 없는 정수 값을 float32로 변환합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_R4">
      <summary>계산 스택 맨 위에 있는 값을 float32로 변환합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_R8">
      <summary>계산 스택 맨 위에 있는 값을 float64로 변환합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_U">
      <summary>계산 스택 맨 위에 있는 값을 unsigned native int로 변환하고 native int로 확장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_U1">
      <summary>계산 스택 맨 위에 있는 값을 unsigned int8로 변환하고 int32로 확장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_U2">
      <summary>계산 스택 맨 위에 있는 값을 unsigned int16로 변환하고 int32로 확장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_U4">
      <summary>계산 스택 맨 위에 있는 값을 unsigned int32로 변환하고 int32로 확장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Conv_U8">
      <summary>계산 스택 맨 위에 있는 값을 unsigned int64로 변환하고 int64로 확장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Cpblk">
      <summary>지정된 수의 바이트를 소스 주소에서 대상 주소로 복사합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Cpobj">
      <summary>개체(&amp;, * 또는 native int 형식)의 주소에 있는 값 형식을 대상 개체(&amp;, * 또는 native int 형식)의 주소로 복사합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Div">
      <summary>두 값을 나누고 결과를 부동 소수점(F 형식)이나 몫(int32 형식)으로 계산 스택에 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Div_Un">
      <summary>두 개의 부호 없는 정수를 나누고 결과(int32)를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Dup">
      <summary>현재 계산 스택 맨 위에 있는 값을 복사하여 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Endfilter">
      <summary>예외의 filter 절에서 CLI(공용 언어 인프라) 예외 처리기로 다시 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Endfinally">
      <summary>예외 블록의 fault 절이나 finally 절에서 CLI(공용 언어 인프라) 예외 처리기로 다시 제어를 전송합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Initblk">
      <summary>특정 주소에 지정된 메모리 블록을 주어진 크기와 초기 값으로 초기화합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Initobj">
      <summary>지정된 주소에서 값 형식의 각 필드를 null 참조 또는 적절한 기본 형식의 0으로 초기화합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Isinst">
      <summary>개체 참조(O 형식)가 특정 클래스의 인스턴스인지를 테스트합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Jmp">
      <summary>현재 메서드를 종료하고 지정된 메서드로 점프합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldarg">
      <summary>지정된 인덱스 값이 참조하는 인수를 스택으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldarg_0">
      <summary>인덱스 0에 있는 인수를 계산 스택으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldarg_1">
      <summary>인덱스 1에 있는 인수를 계산 스택으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldarg_2">
      <summary>인덱스 2에 있는 인수를 계산 스택으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldarg_3">
      <summary>인덱스 3에 있는 인수를 계산 스택으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldarg_S">
      <summary>지정된 약식 인덱스가 참조하는 인수를 계산 스택으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldarga">
      <summary>인수 주소를 계산 스택으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldarga_S">
      <summary>인수 주소를 계산 스택에 약식으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldc_I4">
      <summary>int32 형식의 주어진 값을 int32로 계산 스택에 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldc_I4_0">
      <summary>정수 값 0을 int32로 계산 스택에 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldc_I4_1">
      <summary>정수 값 1을 int32로 계산 스택에 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldc_I4_2">
      <summary>정수 값 2을 int32로 계산 스택에 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldc_I4_3">
      <summary>정수 값 3을 int32로 계산 스택에 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldc_I4_4">
      <summary>정수 값 4을 int32로 계산 스택에 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldc_I4_5">
      <summary>정수 값 5을 int32로 계산 스택에 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldc_I4_6">
      <summary>정수 값 6을 int32로 계산 스택에 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldc_I4_7">
      <summary>정수 값 7을 int32로 계산 스택에 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldc_I4_8">
      <summary>정수 값 8을 int32로 계산 스택에 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldc_I4_M1">
      <summary>정수 값 -1을 int32로 계산 스택에 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldc_I4_S">
      <summary>주어진 int8 값을 약식인 int32로 계산 스택에 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldc_I8">
      <summary>int64 형식의 주어진 값을 int64로 계산 스택에 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldc_R4">
      <summary>float32 형식의 주어진 값을 F 형식(부동 소수점)으로 계산 스택에 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldc_R8">
      <summary>float64 형식의 주어진 값을 F 형식(부동 소수점)으로 계산 스택에 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldelem">
      <summary>지정된 배열 인덱스에 있는 요소를 이 명령에 지정된 형식으로 계산 스택 맨 위에 로드합니다. </summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldelem_I">
      <summary>지정된 배열 인덱스에서 native int 형식을 갖는 요소를 계산 스택 맨 위에 native int 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldelem_I1">
      <summary>지정된 배열 인덱스의 int8 형식을 갖는 요소를 계산 스택 맨 위에 int32로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldelem_I2">
      <summary>지정된 배열 인덱스의 int16 형식을 갖는 요소를 계산 스택 맨 위에 int32로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldelem_I4">
      <summary>지정된 배열 인덱스의 int32 형식을 갖는 요소를 계산 스택 맨 위에 int32로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldelem_I8">
      <summary>지정된 배열 인덱스의 int64 형식을 갖는 요소를 계산 스택 맨 위에 int64로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldelem_R4">
      <summary>지정된 배열 인덱스에서 float32 형식을 갖는 요소를 계산 스택 맨 위에 F형식(부동 소수점)으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldelem_R8">
      <summary>지정된 배열 인덱스에서 float64 형식을 갖는 요소를 계산 스택 맨 위에 F형식(부동 소수점)으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldelem_Ref">
      <summary>지정된 배열 인덱스에서 개체 참조를 포함하는 요소를 O 형식(개체 참조)으로 계산 스택 맨 위에 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldelem_U1">
      <summary>지정된 배열 인덱스의 unsigned int8 형식을 갖는 요소를 계산 스택 맨 위에 int32로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldelem_U2">
      <summary>지정된 배열 인덱스의 unsigned int16 형식을 갖는 요소를 계산 스택 맨 위에 int32로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldelem_U4">
      <summary>지정된 배열 인덱스의 unsigned int32 형식을 갖는 요소를 계산 스택 맨 위에 int32로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldelema">
      <summary>지정된 배열 인덱스에 있는 배열 요소의 주소를 &amp; 형식(관리되는 포인터)으로 계산 스택 맨 위에 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldfld">
      <summary>현재 계산 스택에 참조가 있는 개체에서 필드의 값을 찾습니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldflda">
      <summary>현재 계산 스택에 참조가 있는 개체에서 필드의 주소를 찾습니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldftn">
      <summary>특정 메서드를 구현하는 네이티브 코드에 대한 관리되지 않는 포인터(native int 형식)를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldind_I">
      <summary>native int 형식의 값을 native int 형식으로 계산 스택에 간접적으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldind_I1">
      <summary>int8 형식의 값을 int32로 스택에 간접적으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldind_I2">
      <summary>int16 형식의 값을 int32로 스택에 간접적으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldind_I4">
      <summary>int32 형식의 값을 int32로 스택에 간접적으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldind_I8">
      <summary>int64 형식의 값을 int64로 스택에 간접적으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldind_R4">
      <summary>float32 형식의 값을 F 형식(부동 소수점)으로 계산 스택에 간접적으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldind_R8">
      <summary>float64 형식의 값을 F 형식(부동 소수점)으로 계산 스택에 간접적으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldind_Ref">
      <summary>개체 참조를 O 형식(개체 참조)으로 계산 스택에 간접적으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldind_U1">
      <summary>unsigned int8 형식의 값을 int32로 스택에 간접적으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldind_U2">
      <summary>unsigned int16 형식의 값을 int32로 스택에 간접적으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldind_U4">
      <summary>unsigned int32 형식의 값을 int32로 스택에 간접적으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldlen">
      <summary>0부터 시작하는 1차원 배열의 요소 수를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldloc">
      <summary>특정 인덱스에 있는 지역 변수를 계산 스택으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldloc_0">
      <summary>인덱스 0의 지역 변수를 계산 스택으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldloc_1">
      <summary>인덱스 1의 지역 변수를 계산 스택으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldloc_2">
      <summary>인덱스 2의 지역 변수를 계산 스택으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldloc_3">
      <summary>인덱스 3의 지역 변수를 계산 스택으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldloc_S">
      <summary>특정 인덱스에 있는 지역 변수를 계산 스택에 약식으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldloca">
      <summary>특정 인덱스에 있는 지역 변수의 주소를 계산 스택으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldloca_S">
      <summary>특정 인덱스에 있는 지역 변수의 주소를 계산 스택에 약식으로 로드합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldnull">
      <summary>null 참조(O 형식)를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldobj">
      <summary>주소가 가리키는 값 형식 개체를 계산 스택 맨 위로 복사합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldsfld">
      <summary>정적 필드의 값을 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldsflda">
      <summary>정적 필드의 주소를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldstr">
      <summary>새 개체 참조를 메타데이터에 저장된 문자열 리터럴로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldtoken">
      <summary>메타데이터 토큰을 런타임 표현으로 변환하여 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ldvirtftn">
      <summary>지정된 개체와 관련된 특정 가상 메서드를 구현하는 네이티브 코드에 대한 관리되지 않는 포인터(native int 형식)를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Leave">
      <summary>조건에 관계없이 특정 대상 명령으로 제어를 전송하여 보호되는 코드 영역을 종료합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Leave_S">
      <summary>조건에 관계없이 대상 명령(약식)으로 제어를 전송하여 보호되는 코드 영역을 종료합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Localloc">
      <summary>로컬 동적 메모리 풀에서 특정 바이트 수를 할당하고 처음 할당된 바이트의 주소(임시 포인터, * 형식)를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Mkrefany">
      <summary>특정 형식의 인스턴스에 대한 형식화된 참조를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Mul">
      <summary>두 값을 곱하여 결과를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Mul_Ovf">
      <summary>두 정수 값을 곱하고 오버플로를 검사하여 결과를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Mul_Ovf_Un">
      <summary>부호 없는 두 정수 값을 곱하고 오버플로를 검사한 후 결과를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Neg">
      <summary>값을 음수로 만들고 결과를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Newarr">
      <summary>0부터 시작하고 요소가 특정 형식인 새 1차원 배열에 대한 개체 참조를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Newobj">
      <summary>개체 참조(O 형식)를 계산 스택으로 푸시하여 값 형식의 새 개체나 새 인스턴스를 만듭니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Nop">
      <summary>opcode가 패치되면 공간을 채웁니다.처리 주기가 사용되더라도 의미 있는 연산이 수행되지 않습니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Not">
      <summary>스택 맨 위에 있는 정수 값의 비트 보수를 계산하고 결과를 같은 형식으로 계산 스택에 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Or">
      <summary>스택 맨 위에 있는 두 정수 값의 비트 보수를 계산하고 결과를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Pop">
      <summary>현재 계산 스택 맨 위에 있는 값을 제거합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Prefix1">
      <summary>이 명령은 예약되어 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Prefix2">
      <summary>이 명령은 예약되어 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Prefix3">
      <summary>이 명령은 예약되어 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Prefix4">
      <summary>이 명령은 예약되어 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Prefix5">
      <summary>이 명령은 예약되어 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Prefix6">
      <summary>이 명령은 예약되어 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Prefix7">
      <summary>이 명령은 예약되어 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Prefixref">
      <summary>이 명령은 예약되어 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Readonly">
      <summary>후속 배열 주소 연산에서 런타임에 형식 검사를 수행하지 않고 가변성이 제한된 관리되는 포인터를 반환하도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Refanytype">
      <summary>형식화된 참조에 포함된 형식 토큰을 검색합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Refanyval">
      <summary>형식화된 참조에 포함된 주소(&amp; 형식)를 검색합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Rem">
      <summary>두 값을 나누어 나머지를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Rem_Un">
      <summary>부호 없는 두 값을 나누어 나머지를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Ret">
      <summary>현재 메서드에서 제어를 반환하고 반환 값이 있을 경우 호출 수신자의 계산 스택에서 호출자의 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Rethrow">
      <summary>현재 예외를 다시 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Shl">
      <summary>결과를 계산 스택으로 푸시하여 지정된 비트 수만큼 정수 값을 0에서 왼쪽으로 이동합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Shr">
      <summary>결과를 계산 스택으로 푸시하여 부호 안에 있는 정수 값을 지정된 비트 수만큼 오른쪽으로 이동합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Shr_Un">
      <summary>결과를 계산 스택으로 푸시하여 부호 없는 정수 값을 지정된 비트 수만큼 0에서 오른쪽으로 이동합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Sizeof">
      <summary>주어진 값 형식의 크기(바이트)를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Starg">
      <summary>지정된 인덱스에 있는 인수 슬롯에 계산 스택 맨 위에 있는 값을 저장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Starg_S">
      <summary>계산 스택 맨 위의 값을 약식인 지정된 인덱스의 인수 슬롯에 저장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stelem">
      <summary>지정된 인덱스에 있는 배열 요소를 명령에 지정된 형식을 갖는 계산 스택의 값으로 바꿉니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stelem_I">
      <summary>주어진 인덱스에 있는 배열 요소를 계산 스택에 있는 native int 값으로 바꿉니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stelem_I1">
      <summary>주어진 인덱스에 있는 배열 요소를 계산 스택에 있는 int8 값으로 바꿉니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stelem_I2">
      <summary>주어진 인덱스에 있는 배열 요소를 계산 스택에 있는 int16 값으로 바꿉니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stelem_I4">
      <summary>주어진 인덱스에 있는 배열 요소를 계산 스택에 있는 int32 값으로 바꿉니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stelem_I8">
      <summary>주어진 인덱스에 있는 배열 요소를 계산 스택에 있는 int64 값으로 바꿉니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stelem_R4">
      <summary>주어진 인덱스에 있는 배열 요소를 계산 스택에 있는 float32 값으로 바꿉니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stelem_R8">
      <summary>주어진 인덱스에 있는 배열 요소를 계산 스택에 있는 float64 값으로 바꿉니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stelem_Ref">
      <summary>지정된 인덱스에 있는 배열 요소를 계산 스택에 있는 개체 참조 값(O 형식)으로 바꿉니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stfld">
      <summary>개체 참조나 포인터의 필드에 저장된 값을 새 값으로 바꿉니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stind_I">
      <summary>주어진 주소에 native int 형식의 값을 저장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stind_I1">
      <summary>주어진 주소에 int8 형식의 값을 저장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stind_I2">
      <summary>주어진 주소에 int16 형식의 값을 저장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stind_I4">
      <summary>주어진 주소에 int32 형식의 값을 저장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stind_I8">
      <summary>주어진 주소에 int64 형식의 값을 저장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stind_R4">
      <summary>주어진 주소에 float32 형식의 값을 저장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stind_R8">
      <summary>주어진 주소에 float64 형식의 값을 저장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stind_Ref">
      <summary>제공된 주소에 개체 참조 값을 저장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stloc">
      <summary>계산 스택 맨 위에서 현재 값을 팝하고 지정된 인덱스에 있는 지역 변수 목록에 저장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stloc_0">
      <summary>계산 스택 맨 위에서 현재 값을 팝하여 인덱스 0에 있는 지역 변수 목록에 저장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stloc_1">
      <summary>계산 스택 맨 위에서 현재 값을 팝하여 인덱스 1에 있는 지역 변수 목록에 저장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stloc_2">
      <summary>계산 스택 맨 위에서 현재 값을 팝하여 인덱스 2에 있는 지역 변수 목록에 저장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stloc_3">
      <summary>계산 스택 맨 위에서 현재 값을 팝하여 인덱스 3에 있는 지역 변수 목록에 저장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stloc_S">
      <summary>계산 스택 맨 위에서 현재 값을 팝하여 <paramref name="index" />(약식)의 지역 변수 목록에 저장합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stobj">
      <summary>지정된 형식의 값을 계산 스택에서 주어진 메모리 주소로 복사합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Stsfld">
      <summary>정적 필드의 값을 계산 스택에 있는 값으로 바꿉니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Sub">
      <summary>값에서 다른 값을 빼고 결과를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Sub_Ovf">
      <summary>정수 값에서 다른 정수 값을 빼고 오버플로를 검사하여 결과를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Sub_Ovf_Un">
      <summary>부호 없는 정수 값에서 다른 부호 없는 정수 값을 빼고 오버플로를 검사하여 결과를 계산 스택으로 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Switch">
      <summary>점프 테이블을 구현합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Tailcall">
      <summary>실제 호출 명령이 실행되기 전에 현재 메서드의 스택 프레임이 제거되도록 후위 메서드 호출 명령을 수행합니다.</summary>
    </member>
    <member name="M:System.Reflection.Emit.OpCodes.TakesSingleByteArgument(System.Reflection.Emit.OpCode)">
      <summary>제공된 opcode가 단일 바이트 인수를 사용할 경우 true나 false를 반환합니다.</summary>
      <returns>True 또는 false</returns>
      <param name="inst">Opcode 개체의 인스턴스입니다. </param>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Throw">
      <summary>현재 계산 스택에 있는 예외 개체를 throw합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Unaligned">
      <summary>현재 계산 스택의 맨 위에 있는 주소가 바로 다음에 오는 ldind, stind, ldfld, stfld, ldobj, stobj, initblk 또는 cpblk 명령의 기본 크기에 따라 정렬되지 않을 수 있음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Unbox">
      <summary>boxed로 표시되는 값 형식을 unboxed 형식으로 변환합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Unbox_Any">
      <summary>명령에 지정된 형식의 boxed 표현을 unboxed 형식으로 변환합니다. </summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Volatile">
      <summary>현재 계산 스택 맨 위에 있는 주소가 휘발성이고, 해당 위치를 읽은 결과가 캐시되지 않으며 이 위치에 여러 번 저장할 수 있음을 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodes.Xor">
      <summary>계산 스택의 맨 위에 있는 두 값의 배타적 비트 OR을 계산하고 결과를 스택으로 푸시합니다.</summary>
    </member>
    <member name="T:System.Reflection.Emit.OpCodeType">
      <summary>MSIL(Microsoft Intermediate Language) 명령의 형식을 설명합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodeType.Macro">
      <summary>이것은 다른 MSIL(Microsoft Intermediate Language) 명령의 동의어로 사용되는 MSIL 명령들입니다.예를 들어, ldarg.0은 인수가 0인 ldarg 명령을 나타냅니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodeType.Nternal">
      <summary>예약된 MSIL(Microsoft Intermediate Language) 명령을 설명합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodeType.Objmodel">
      <summary>개체에 적용될 MSIL(Microsoft Intermediate Language) 명령을 설명합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodeType.Prefix">
      <summary>다음 명령의 동작을 수정하는 접두사 명령을 설명합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OpCodeType.Primitive">
      <summary>기본 제공 명령을 설명합니다.</summary>
    </member>
    <member name="T:System.Reflection.Emit.OperandType">
      <summary>MSIL(Microsoft Intermediate Language) 명령의 피연산자 형식을 설명합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OperandType.InlineBrTarget">
      <summary>피연산자가 32비트 정수 분기 대상입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OperandType.InlineField">
      <summary>피연산자가 32비트 메타데이터 토큰입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OperandType.InlineI">
      <summary>피연산자가 32비트 정수입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OperandType.InlineI8">
      <summary>피연산자가 64비트 정수입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OperandType.InlineMethod">
      <summary>피연산자가 32비트 메타데이터 토큰입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OperandType.InlineNone">
      <summary>피연산자가 없습니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OperandType.InlineR">
      <summary>피연산자가 64비트 IEEE 부동 소수점 형식입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OperandType.InlineSig">
      <summary>피연산자가 32비트 메타데이터 시그니처 토큰입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OperandType.InlineString">
      <summary>피연산자가 32비트 메타데이터 문자열 토큰입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OperandType.InlineSwitch">
      <summary>피연산자가 스위치 명령에 대한 32비트 정수 인수입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OperandType.InlineTok">
      <summary>피연산자가 FieldRef, MethodRef 또는 TypeRef 토큰입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OperandType.InlineType">
      <summary>피연산자가 32비트 메타데이터 토큰입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OperandType.InlineVar">
      <summary>피연산자가 지역 변수나 인수의 서수가 포함된 16비트 정수입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OperandType.ShortInlineBrTarget">
      <summary>피연산자가 8비트 정수 분기 대상입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OperandType.ShortInlineI">
      <summary>피연산자가 8비트 정수입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OperandType.ShortInlineR">
      <summary>피연산자가 32비트 IEEE 부동 소수점 형식입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.OperandType.ShortInlineVar">
      <summary>피연산자가 지역 변수나 인수의 서수가 포함된 8비트 정수입니다.</summary>
    </member>
    <member name="T:System.Reflection.Emit.PackingSize">
      <summary>형식이 마샬링될 때 필드의 메모리 맞춤을 결정하는 두 요소 중 하나를 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.PackingSize.Size1">
      <summary>압축 크기가 1바이트입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.PackingSize.Size128">
      <summary>압축 크기가 128바이트입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.PackingSize.Size16">
      <summary>압축 크기가 16바이트입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.PackingSize.Size2">
      <summary>압축 크기가 2바이트입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.PackingSize.Size32">
      <summary>압축 크기가 32바이트입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.PackingSize.Size4">
      <summary>압축 크기가 4바이트입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.PackingSize.Size64">
      <summary>압축 크기가 64바이트입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.PackingSize.Size8">
      <summary>압축 크기가 8바이트입니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.PackingSize.Unspecified">
      <summary>압축 크기가 지정되지 않았습니다.</summary>
    </member>
    <member name="T:System.Reflection.Emit.StackBehaviour">
      <summary>값을 스택에 푸시하거나 스택에서 팝하는 방법을 설명합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Pop0">
      <summary>스택에서 값을 팝하지 않습니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Pop1">
      <summary>스택에서 값 하나를 팝합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Pop1_pop1">
      <summary>스택에서 첫 번째 피연산자에 대한 값 하나를 팝하고, 두 번째 피연산자에 대한 값 하나를 팝합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Popi">
      <summary>스택에서 32비트 정수를 팝합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Popi_pop1">
      <summary>스택에서 첫 번째 피연산자에 대한 32비트 정수를 팝하고, 두 번째 피연산자에 대한 값 하나를 팝합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Popi_popi">
      <summary>스택에서 첫 번째 피연산자에 대한 32비트 정수를 팝하고, 두 번째 피연산자에 대한 32비트 정수를 팝합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Popi_popi_popi">
      <summary>스택에서 첫 번째 피연산자에 대한 32비트 정수를 팝하고, 두 번째 피연산자에 대한 32비트 정수를 팝합니다. 그리고 세 번째 피연산자에 대한 32비트 정수도 팝합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Popi_popi8">
      <summary>스택에서 첫 번째 피연산자에 대한 64비트 정수를 팝하고, 두 번째 피연산자에 대한 32비트 정수를 팝합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Popi_popr4">
      <summary>스택에서 첫 번째 피연산자에 대한 32비트 정수를 팝하고, 두 번째 피연산자에 대한 32비트 부동 소수점 수를 팝합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Popi_popr8">
      <summary>스택에서 첫 번째 피연산자에 대한 64비트 정수를 팝하고, 두 번째 피연산자에 대한 32비트 부동 소수점 수를 팝합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Popref">
      <summary>스택에서 참조를 팝합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Popref_pop1">
      <summary>스택에서 첫 번째 피연산자에 대한 참조를 팝하고, 두 번째 피연산자에 대한 값 하나를 팝합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Popref_popi">
      <summary>스택에서 첫 번째 피연산자에 대한 참조를 팝하고, 두 번째 피연산자에 대한 32비트 정수를 팝합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Popref_popi_pop1">
      <summary>스택에서 첫 번째 피연산자에 대한 참조를 팝하고, 두 번째 피연산자에 대한 값 하나를 팝합니다. 그리고 세 번째 피연산자에 대한 32비트 정수도 팝합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Popref_popi_popi">
      <summary>스택에서 첫 번째 피연산자에 대한 참조를 팝하고, 두 번째 피연산자에 대한 값 하나를 팝합니다. 그리고 세 번째 피연산자에 대한 값 하나도 팝합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Popref_popi_popi8">
      <summary>스택에서 첫 번째 피연산자에 대한 참조를 팝하고, 두 번째 피연산자에 대한 값 하나를 팝합니다. 그리고 세 번째 피연산자에 대한 64비트 정수도 팝합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Popref_popi_popr4">
      <summary>스택에서 첫 번째 피연산자에 대한 참조를 팝하고, 두 번째 피연산자에 대한 값 하나를 팝합니다. 그리고 세 번째 피연산자에 대한 32비트 정수도 팝합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Popref_popi_popr8">
      <summary>스택에서 첫 번째 피연산자에 대한 참조를 팝하고, 두 번째 피연산자에 대한 64비트 부동 소수점 수를 팝합니다. 그리고 세 번째 피연산자에 대한 64비트 정수도 팝합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Popref_popi_popref">
      <summary>스택에서 첫 번째 피연산자에 대한 참조를 팝하고, 두 번째 피연산자에 대한 값 하나를 팝합니다. 그리고 세 번째 피연산자에 대한 참조도 팝합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Push0">
      <summary>스택에 값을 푸시하지 않습니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Push1">
      <summary>스택에 값 하나를 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Push1_push1">
      <summary>스택에 첫 번째 피연산자에 대한 값 하나를 푸시하고, 두 번째 피연산자에 대한 값 하나를 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Pushi">
      <summary>스택에 32비트 정수를 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Pushi8">
      <summary>스택에 64비트 정수를 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Pushr4">
      <summary>스택에 32비트 부동 소수점 수를 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Pushr8">
      <summary>스택에 64비트 부동 소수점 수를 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Pushref">
      <summary>스택에 참조를 푸시합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Varpop">
      <summary>스택에서 변수를 팝합니다.</summary>
    </member>
    <member name="F:System.Reflection.Emit.StackBehaviour.Varpush">
      <summary>스택에 변수를 푸시합니다.</summary>
    </member>
  </members>
</doc>