﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.Extensions</name>
  </assembly>
  <members>
    <member name="T:System.BitConverter">
      <summary>基本データ型をバイト配列に、バイト配列を基本データ型に変換します。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.BitConverter.DoubleToInt64Bits(System.Double)">
      <summary>指定した倍精度浮動小数点数を 64 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の値を持つ 64 ビット符号付き整数。</returns>
      <param name="value">変換する数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Boolean)">
      <summary>指定したブール値をバイト配列として返します。</summary>
      <returns>長さ 1 のバイト配列。</returns>
      <param name="value">ブール値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Char)">
      <summary>指定した Unicode 文字値をバイト配列として返します。</summary>
      <returns>長さ 2 のバイト配列。</returns>
      <param name="value">変換する文字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Double)">
      <summary>指定した倍精度浮動小数点値をバイト配列として返します。</summary>
      <returns>長さ 8 のバイト配列。</returns>
      <param name="value">変換する数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int16)">
      <summary>指定した 16 ビットの符号なし整数値をバイト配列として返します。</summary>
      <returns>長さ 2 のバイト配列。</returns>
      <param name="value">変換する数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int32)">
      <summary>指定した 32 ビットの符号なし整数値をバイト配列として返します。</summary>
      <returns>長さ 4 のバイト配列。</returns>
      <param name="value">変換する数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int64)">
      <summary>指定した 64 ビットの符号なし整数値をバイト配列として返します。</summary>
      <returns>長さ 8 のバイト配列。</returns>
      <param name="value">変換する数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Single)">
      <summary>指定した単精度浮動小数点値をバイト配列として返します。</summary>
      <returns>長さ 4 のバイト配列。</returns>
      <param name="value">変換する数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt16)">
      <summary>指定した 16 ビットの符号なし整数値をバイト配列として返します。</summary>
      <returns>長さ 2 のバイト配列。</returns>
      <param name="value">変換する数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt32)">
      <summary>指定した 32 ビットの符号なし整数値をバイト配列として返します。</summary>
      <returns>長さ 4 のバイト配列。</returns>
      <param name="value">変換する数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt64)">
      <summary>指定した 64 ビットの符号なし整数値をバイト配列として返します。</summary>
      <returns>長さ 8 のバイト配列。</returns>
      <param name="value">変換する数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.Int64BitsToDouble(System.Int64)">
      <summary>指定した 64 ビット符号付き整数を倍精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の値を持つ倍精度浮動小数点数。</returns>
      <param name="value">変換する数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.BitConverter.IsLittleEndian">
      <summary>このコンピューター アーキテクチャでデータが格納される際のバイト順 ("エンディアン") を示します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToBoolean(System.Byte[],System.Int32)">
      <summary>バイト配列内の指定位置にある 1 バイトから変換されたブール値を返します。</summary>
      <returns>
        <paramref name="value" /> の <paramref name="startIndex" /> にあるバイトが 0 以外の場合は true。それ以外の場合は false。</returns>
      <param name="value">バイト配列。</param>
      <param name="startIndex">
        <paramref name="value" /> 内の開始位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> が 0 未満か、<paramref name="value" /> の長さから 1 を引いた値より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToChar(System.Byte[],System.Int32)">
      <summary>バイト配列内の指定位置にある 2 バイトから変換された Unicode 文字を返します。</summary>
      <returns>
        <paramref name="startIndex" /> から始まる 2 バイトで構成される文字。</returns>
      <param name="value">配列。</param>
      <param name="startIndex">
        <paramref name="value" /> 内の開始位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> が <paramref name="value" /> の長さから 1 を引いた値です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> が 0 未満か、<paramref name="value" /> の長さから 1 を引いた値より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToDouble(System.Byte[],System.Int32)">
      <summary>バイト配列内の指定位置にある 8 バイトから変換された倍精度浮動小数点数を返します。</summary>
      <returns>
        <paramref name="startIndex" /> から始まる 8 バイトで構成される倍精度浮動小数点数。</returns>
      <param name="value">バイト配列。</param>
      <param name="startIndex">
        <paramref name="value" /> 内の開始位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> が <paramref name="value" /> の長さから 7 を引いた値以上で、<paramref name="value" /> の長さから 1 を引いた値以下です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> が 0 未満か、<paramref name="value" /> の長さから 1 を引いた値より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt16(System.Byte[],System.Int32)">
      <summary>バイト配列内の指定位置にある 2 バイトから変換された 16 ビット符号付き整数を返します。</summary>
      <returns>
        <paramref name="startIndex" /> から始まる 2 バイトで構成される 16 ビット符号付き整数。</returns>
      <param name="value">バイト配列。</param>
      <param name="startIndex">
        <paramref name="value" /> 内の開始位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> が <paramref name="value" /> の長さから 1 を引いた値です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> が 0 未満か、<paramref name="value" /> の長さから 1 を引いた値より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt32(System.Byte[],System.Int32)">
      <summary>バイト配列内の指定位置にある 4 バイトから変換された 32 ビット符号付き整数を返します。</summary>
      <returns>
        <paramref name="startIndex" /> から始まる 4 バイトで構成される 32 ビット符号付き整数。</returns>
      <param name="value">バイト配列。</param>
      <param name="startIndex">
        <paramref name="value" /> 内の開始位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> が <paramref name="value" /> の長さから 3 を引いた値以上で、<paramref name="value" /> の長さから 1 を引いた値以下です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> が 0 未満か、<paramref name="value" /> の長さから 1 を引いた値より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt64(System.Byte[],System.Int32)">
      <summary>バイト配列内の指定位置にある 8 バイトから変換された 64 ビット符号付き整数を返します。</summary>
      <returns>
        <paramref name="startIndex" /> から始まる 8 バイトで構成される 64 ビット符号付き整数。</returns>
      <param name="value">バイト配列。</param>
      <param name="startIndex">
        <paramref name="value" /> 内の開始位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> が <paramref name="value" /> の長さから 7 を引いた値以上で、<paramref name="value" /> の長さから 1 を引いた値以下です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> が 0 未満か、<paramref name="value" /> の長さから 1 を引いた値より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToSingle(System.Byte[],System.Int32)">
      <summary>バイト配列内の指定位置にある 4 バイトから変換された単精度浮動小数点数を返します。</summary>
      <returns>
        <paramref name="startIndex" /> から始まる 4 バイトで構成される単精度浮動小数点数。</returns>
      <param name="value">バイト配列。</param>
      <param name="startIndex">
        <paramref name="value" /> 内の開始位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> が <paramref name="value" /> の長さから 3 を引いた値以上で、<paramref name="value" /> の長さから 1 を引いた値以下です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> が 0 未満か、<paramref name="value" /> の長さから 1 を引いた値より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[])">
      <summary>指定されたバイト配列の各要素の数値をそれと等価な 16 進数文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> 内の対応する要素を表す 16 進値がハイフンで区切られている文字列 ("7F-2C-4A-00" など)。</returns>
      <param name="value">バイト配列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> は null なので、</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32)">
      <summary>指定されたバイト サブ配列の各要素の数値をそれと等価な 16 進数文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> のサブ配列内の対応する要素を表す 16 進値がハイフンで区切られている文字列 ("7F-2C-4A-00" など)。</returns>
      <param name="value">バイト配列。</param>
      <param name="startIndex">
        <paramref name="value" /> 内の開始位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> が 0 未満か、<paramref name="value" /> の長さから 1 を引いた値より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32,System.Int32)">
      <summary>指定されたバイト サブ配列の各要素の数値をそれと等価な 16 進数文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> のサブ配列内の対応する要素を表す 16 進値がハイフンで区切られている文字列 ("7F-2C-4A-00" など)。</returns>
      <param name="value">バイト配列。</param>
      <param name="startIndex">
        <paramref name="value" /> 内の開始位置。</param>
      <param name="length">変換する <paramref name="value" /> の配列要素の数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> または <paramref name="length" /> が 0 未満です。または<paramref name="startIndex" /> が 0 より大きく、<paramref name="value" /> の長さ以上になっています。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> と <paramref name="length" /> の組み合わせが、<paramref name="value" /> 内の位置を指定していません。つまり、<paramref name="startIndex" /> パラメーターが、<paramref name="value" /> の長さから <paramref name="length" /> パラメーターの値を減算した値より大きくなっています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt16(System.Byte[],System.Int32)">
      <summary>バイト配列内の指定位置にある 2 バイトから変換された 16 ビット符号なし整数を返します。</summary>
      <returns>
        <paramref name="startIndex" /> から始まる 2 バイトで構成される 16 ビット符号なし整数。</returns>
      <param name="value">バイト配列。</param>
      <param name="startIndex">
        <paramref name="value" /> 内の開始位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> が <paramref name="value" /> の長さから 1 を引いた値です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> が 0 未満か、<paramref name="value" /> の長さから 1 を引いた値より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt32(System.Byte[],System.Int32)">
      <summary>バイト配列内の指定位置にある 4 バイトから変換された 32 ビット符号なし整数を返します。</summary>
      <returns>
        <paramref name="startIndex" /> から始まる 4 バイトで構成される 32 ビット符号なし整数。</returns>
      <param name="value">バイト配列。</param>
      <param name="startIndex">
        <paramref name="value" /> 内の開始位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> が <paramref name="value" /> の長さから 3 を引いた値以上で、<paramref name="value" /> の長さから 1 を引いた値以下です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> が 0 未満か、<paramref name="value" /> の長さから 1 を引いた値より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt64(System.Byte[],System.Int32)">
      <summary>バイト配列内の指定位置にある 8 バイトから変換された 64 ビット符号なし整数を返します。</summary>
      <returns>
        <paramref name="startIndex" /> から始まる 8 バイトで構成される 64 ビット符号なし整数。</returns>
      <param name="value">バイト配列。</param>
      <param name="startIndex">
        <paramref name="value" /> 内の開始位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> が <paramref name="value" /> の長さから 7 を引いた値以上で、<paramref name="value" /> の長さから 1 を引いた値以下です。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> が 0 未満か、<paramref name="value" /> の長さから 1 を引いた値より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Convert">
      <summary>基本データ型を別の基本データ型に変換します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type)">
      <summary>指定されたオブジェクトと等しい値を持つ、指定された型のオブジェクトを返します。</summary>
      <returns>型が <paramref name="conversionType" /> であり、<paramref name="value" /> と等価の値を持つオブジェクト。または<paramref name="value" /> が null で、<paramref name="conversionType" /> が値型ではない場合は、null 参照 (Visual Basic の場合は Nothing)。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクト。</param>
      <param name="conversionType">返すオブジェクトの型。</param>
      <exception cref="T:System.InvalidCastException">この変換はサポートされていません。または<paramref name="value" /> が null であり、<paramref name="conversionType" /> が値型です。または<paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> は、<paramref name="conversionType" /> によって認識されている形式ではありません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <paramref name="conversionType" /> の範囲外である数字を表しています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="conversionType" /> は null です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type,System.IFormatProvider)">
      <summary>指定したオブジェクトに等しい値を持つ指定した型のオブジェクトを返します。パラメーターにより、カルチャに固有の書式情報が指定されます。</summary>
      <returns>型が <paramref name="conversionType" /> であり、<paramref name="value" /> と等価の値を持つオブジェクト。または <paramref name="value" /> の <see cref="T:System.Type" /> と <paramref name="conversionType" /> が等しい場合は <paramref name="value" />。または<paramref name="value" /> が null で、<paramref name="conversionType" /> が値型ではない場合は、null 参照 (Visual Basic の場合は Nothing)。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクト。</param>
      <param name="conversionType">返すオブジェクトの型。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.InvalidCastException">この変換はサポートされていません。または<paramref name="value" /> が null であり、<paramref name="conversionType" /> が値型です。または<paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の書式が、<paramref name="provider" /> によって認識される <paramref name="conversionType" /> の書式ではありません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <paramref name="conversionType" /> の範囲外である数字を表しています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="conversionType" /> は null です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.TypeCode,System.IFormatProvider)">
      <summary>指定したオブジェクトに等しい値を持つ指定した型のオブジェクトを返します。パラメーターにより、カルチャに固有の書式情報が指定されます。</summary>
      <returns>基になる型が <paramref name="typeCode" /> であり、<paramref name="value" /> と等価の値を持つオブジェクト。または<paramref name="value" /> が null で、<paramref name="typeCode" /> が <see cref="F:System.TypeCode.Empty" />、<see cref="F:System.TypeCode.String" />、または <see cref="F:System.TypeCode.Object" /> の場合は、null 参照 (Visual Basic の場合は Nothing)。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクト。</param>
      <param name="typeCode">返すオブジェクトの型。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.InvalidCastException">この変換はサポートされていません。または<paramref name="value" /> が null であり、<paramref name="typeCode" /> が値型を指定しています。または<paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の書式が、<paramref name="provider" /> によって認識される <paramref name="typeCode" /> 型の書式ではありません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <paramref name="typeCode" /> 型の範囲外である数字を表します。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="typeCode" /> が無効です。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64CharArray(System.Char[],System.Int32,System.Int32)">
      <summary>Unicode 文字配列のサブセットを変換します。これにより、バイナリ データは Base64 の数字として等価の 8 ビット符号なし整数配列にエンコードされます。入力配列のサブセットと変換する要素の数をパラメーターで指定します。</summary>
      <returns>
        <paramref name="inArray" /> の <paramref name="offset" /> の位置にある <paramref name="length" /> 要素と等価の 8 ビット符号なし整数の配列。</returns>
      <param name="inArray">Unicode 文字配列。</param>
      <param name="offset">
        <paramref name="inArray" /> 内での位置。</param>
      <param name="length">変換する <paramref name="inArray" /> の要素の数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> は null です。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> または <paramref name="length" /> が 0 未満です。または <paramref name="length" /> に <paramref name="offset" /> を足した数が、<paramref name="inArray" /> 内に存在しない位置を示しています。</exception>
      <exception cref="T:System.FormatException">空白文字を除いた <paramref name="inArray" /> の長さが 0 ではないか、または 4 の倍数ではありません。または<paramref name="inArray" /> の形式が無効です。<paramref name="inArray" /> に Base64 形式以外の文字、2 つ以上の埋め込み文字、または埋め込み文字の間に空白以外の文字が含まれています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64String(System.String)">
      <summary>指定した文字列を変換します。これにより、バイナリ データは Base64 の数字として等価の 8 ビット符号なし整数配列にエンコードされます。</summary>
      <returns>
        <paramref name="s" /> と等価な 8 ビット符号なし整数の配列。</returns>
      <param name="s">変換する文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> は null です。</exception>
      <exception cref="T:System.FormatException">空白文字を除いた <paramref name="s" /> の長さが 0 ではないか、または 4 の倍数ではありません。または<paramref name="s" /> の形式が無効です。<paramref name="s" /> に Base64 形式以外の文字、2 つ以上の埋め込み文字、または埋め込み文字の間に空白以外の文字が含まれています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.GetTypeCode(System.Object)">
      <summary>指定したオブジェクトの <see cref="T:System.TypeCode" /> を返します。</summary>
      <returns>
        <paramref name="value" /> の <see cref="T:System.TypeCode" />。<paramref name="value" /> が null である場合は <see cref="F:System.TypeCode.Empty" />。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクト。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64CharArray(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32)">
      <summary>8 ビット符号なし整数配列のサブセットを、Base64 の数字でエンコードされた Unicode 文字配列の等価のサブセットに変換します。入力配列と出力配列のオフセットとしてのサブセット、および変換する入力配列の要素の数をパラメーターで指定します。</summary>
      <returns>
        <paramref name="outArray" /> のバイト数が格納された 32 ビット符号付き整数。</returns>
      <param name="inArray">8 ビット符号なし整数で構成される入力配列。</param>
      <param name="offsetIn">
        <paramref name="inArray" /> 内での位置。</param>
      <param name="length">変換する <paramref name="inArray" /> の要素の数。</param>
      <param name="outArray">Unicode 文字の出力配列。</param>
      <param name="offsetOut">
        <paramref name="outArray" /> 内での位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> または <paramref name="outArray" /> が null です。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offsetIn" />、<paramref name="offsetOut" />、または <paramref name="length" /> が負の値です。または <paramref name="offsetIn" /> に <paramref name="length" /> を加算した値が、<paramref name="inArray" /> の長さを超えています。または <paramref name="offsetOut" /> と、返される要素の数を合計した値が、<paramref name="outArray" /> の長さを超えています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[])">
      <summary>8 ビット符号なし整数の配列を、Base64 の数字でエンコードされた等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="inArray" /> の内容の Base64 形式での文字列形式。</returns>
      <param name="inArray">8 ビット符号なし整数の配列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> は null です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[],System.Int32,System.Int32)">
      <summary>8 ビット符号なし整数配列のサブセットを、Base64 の数字でエンコードされた等価の文字列形式に変換します。入力配列のオフセットとしてのサブセット、および変換する配列の要素の数をパラメーターで指定します。</summary>
      <returns>
        <paramref name="inArray" /> の <paramref name="offset" /> 位置から <paramref name="length" /> 個の要素の文字列形式。Base64 の数字で構成されています。</returns>
      <param name="inArray">8 ビット符号なし整数の配列。</param>
      <param name="offset">
        <paramref name="inArray" /> のオフセット。</param>
      <param name="length">変換する <paramref name="inArray" /> の要素の数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> は null です。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> または <paramref name="length" /> が負の値です。または <paramref name="offset" /> に <paramref name="length" /> を加算した値が、<paramref name="inArray" /> の長さを超えています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Boolean)">
      <summary>指定したブール値を返します。実際の変換は行われません。</summary>
      <returns>
        <paramref name="value" /> は変更されずに返されます。</returns>
      <param name="value">返されるブール値。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Byte)">
      <summary>指定した 8 ビット符号なし整数の値を等価の Boolean 値に変換します。</summary>
      <returns>
        <paramref name="value" /> が 0 以外の場合は true。0 の場合は false。</returns>
      <param name="value">変換する 8 ビット符号なし整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Decimal)">
      <summary>指定した 10 進数値を等価のブール値に変換します。</summary>
      <returns>
        <paramref name="value" /> が 0 以外の場合は true。0 の場合は false。</returns>
      <param name="value">変換する数値。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Double)">
      <summary>指定した倍精度浮動小数点数値を等価のブール値に変換します。</summary>
      <returns>
        <paramref name="value" /> が 0 以外の場合は true。0 の場合は false。</returns>
      <param name="value">変換する倍精度浮動小数点数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int16)">
      <summary>指定した 16 ビット符号付き整数の値を等価の Boolean 値に変換します。</summary>
      <returns>
        <paramref name="value" /> が 0 以外の場合は true。0 の場合は false。</returns>
      <param name="value">変換する 16 ビット符号付き整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int32)">
      <summary>指定した 32 ビット符号付き整数の値を等価の Boolean 値に変換します。</summary>
      <returns>
        <paramref name="value" /> が 0 以外の場合は true。0 の場合は false。</returns>
      <param name="value">変換する 32 ビット符号付き整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int64)">
      <summary>指定した 64 ビット符号付き整数の値を等価の Boolean 値に変換します。</summary>
      <returns>
        <paramref name="value" /> が 0 以外の場合は true。0 の場合は false。</returns>
      <param name="value">変換する 64 ビット符号付き整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object)">
      <summary>指定したオブジェクトの値を等価のブール値に変換します。</summary>
      <returns>true または false。基になる <paramref name="value" /> の型に対して <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" /> メソッドを呼び出すことで返される値を反映します。<paramref name="value" /> が null の場合、メソッドは false を返します。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクトか、または null。 </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> は、<see cref="F:System.Boolean.TrueString" /> または <see cref="F:System.Boolean.FalseString" /> と等しくない文字列です。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または<paramref name="value" /> から <see cref="T:System.Boolean" /> への変換はサポートされていません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定したオブジェクトの値を等価のブール値に変換します。</summary>
      <returns>true または false。基になる <paramref name="value" /> の型に対して <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" /> メソッドを呼び出すことで返される値を反映します。<paramref name="value" /> が null の場合、メソッドは false を返します。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクトか、または null。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。 </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> は、<see cref="F:System.Boolean.TrueString" /> または <see cref="F:System.Boolean.FalseString" /> と等しくない文字列です。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または<paramref name="value" /> から <see cref="T:System.Boolean" /> への変換はサポートされていません。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.SByte)">
      <summary>指定した 8 ビット符号付き整数の値を等価の Boolean 値に変換します。</summary>
      <returns>
        <paramref name="value" /> が 0 以外の場合は true。0 の場合は false。</returns>
      <param name="value">変換する 8 ビット符号付き整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Single)">
      <summary>指定した単精度浮動小数点数値を等価のブール値に変換します。</summary>
      <returns>
        <paramref name="value" /> が 0 以外の場合は true。0 の場合は false。</returns>
      <param name="value">変換する単精度浮動小数点数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String)">
      <summary>指定した論理値の文字列形式をそれと等価なブール値に変換します。</summary>
      <returns>
        <paramref name="value" /> が <see cref="F:System.Boolean.TrueString" /> に等しい場合は true。<paramref name="value" /> が <see cref="F:System.Boolean.FalseString" /> または null に等しい場合は false。</returns>
      <param name="value">
        <see cref="F:System.Boolean.TrueString" /> または <see cref="F:System.Boolean.FalseString" /> のいずれかの値を格納する文字列。 </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> が <see cref="F:System.Boolean.TrueString" /> または <see cref="F:System.Boolean.FalseString" /> のいずれとも等しくありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した論理値の文字列形式を等価のブール値に変換します。</summary>
      <returns>true if <paramref name="value" /> equals <see cref="F:System.Boolean.TrueString" />, or false if <paramref name="value" /> equals <see cref="F:System.Boolean.FalseString" /> or null.</returns>
      <param name="value">
        <see cref="F:System.Boolean.TrueString" /> または <see cref="F:System.Boolean.FalseString" /> のいずれかの値を格納する文字列。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。このパラメーターは無視されます。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> が <see cref="F:System.Boolean.TrueString" /> または <see cref="F:System.Boolean.FalseString" /> のいずれとも等しくありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt16)">
      <summary>指定した 16 ビット符号なし整数の値を等価の Boolean 値に変換します。</summary>
      <returns>
        <paramref name="value" /> が 0 以外の場合は true。0 の場合は false。</returns>
      <param name="value">変換する 16 ビット符号なし整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt32)">
      <summary>指定した 32 ビット符号なし整数の値を等価の Boolean 値に変換します。</summary>
      <returns>
        <paramref name="value" /> が 0 以外の場合は true。0 の場合は false。</returns>
      <param name="value">変換する 32 ビット符号なし整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt64)">
      <summary>指定した 64 ビット符号なし整数の値を等価の Boolean 値に変換します。</summary>
      <returns>
        <paramref name="value" /> が 0 以外の場合は true。0 の場合は false。</returns>
      <param name="value">変換する 64 ビット符号なし整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Boolean)">
      <summary>指定したブール値を等価の 8 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> が true の場合は数値の 1。それ以外の場合は 0。</returns>
      <param name="value">変換するブール値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Byte)">
      <summary>指定した 8 ビット符号なし整数が返されます。実際の変換は行われません。</summary>
      <returns>
        <paramref name="value" /> は変更されずに返されます。</returns>
      <param name="value">返される 8 ビット符号なし整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Char)">
      <summary>指定した Unicode 文字の値を等価の 8 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号なし整数。</returns>
      <param name="value">変換する Unicode 文字。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> は、<see cref="F:System.Byte.MaxValue" /> より大きな数値を表します。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Decimal)">
      <summary>指定した 10 進数値を等価の 8 ビット符号なし整数に変換します。</summary>
      <returns>近似値の 8 ビット符号なし整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する数値。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Byte.MaxValue" /> より大きい値か、<see cref="F:System.Byte.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Double)">
      <summary>指定した倍精度浮動小数点数値を等価の 8 ビット符号なし整数に変換します。</summary>
      <returns>近似値の 8 ビット符号なし整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する倍精度浮動小数点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Byte.MaxValue" /> より大きい値か、<see cref="F:System.Byte.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int16)">
      <summary>指定した 16 ビット符号付き整数の値を等価の 8 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号なし整数。</returns>
      <param name="value">変換する 16 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Byte.MinValue" /> より小さい値か、<see cref="F:System.Byte.MaxValue" /> より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int32)">
      <summary>指定した 32 ビット符号付き整数の値を等価の 8 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号なし整数。</returns>
      <param name="value">変換する 32 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Byte.MinValue" /> より小さい値か、<see cref="F:System.Byte.MaxValue" /> より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int64)">
      <summary>指定した 64 ビット符号付き整数の値を等価の 8 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号なし整数。</returns>
      <param name="value">変換する 64 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Byte.MinValue" /> より小さい値か、<see cref="F:System.Byte.MaxValue" /> より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object)">
      <summary>指定したオブジェクトの値を 8 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号なし整数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクトか、または null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> は、<see cref="T:System.Byte" /> 値のプロパティ形式ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> を実装していません。または<paramref name="value" /> から <see cref="T:System.Byte" /> 型への変換はサポートされていません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Byte.MinValue" /> 未満の数値か、<see cref="F:System.Byte.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定したオブジェクトの値を 8 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号なし整数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクト。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> は、<see cref="T:System.Byte" /> 値のプロパティ形式ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> を実装していません。または<paramref name="value" /> から <see cref="T:System.Byte" /> 型への変換はサポートされていません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Byte.MinValue" /> 未満の数値か、<see cref="F:System.Byte.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.SByte)">
      <summary>指定した 8 ビット符号付き整数の値を等価の 8 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号なし整数。</returns>
      <param name="value">変換する 8 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> は <see cref="F:System.Byte.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Single)">
      <summary>指定した単精度浮動小数点数値を等価の 8 ビット符号なし整数に変換します。</summary>
      <returns>近似値の 8 ビット符号なし整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">単精度浮動小数点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Byte.MaxValue" /> より大きい値か、<see cref="F:System.Byte.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String)">
      <summary>指定した数値の文字列形式を等価の 8 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号なし整数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の構成が、省略可能な符号と、それに続く 0 から 9 までの一連の数字ではありません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Byte.MinValue" /> 未満の数値か、<see cref="F:System.Byte.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した数値の文字列形式を等価の 8 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号なし整数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の構成が、省略可能な符号と、それに続く 0 から 9 までの一連の数字ではありません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Byte.MinValue" /> 未満の数値か、<see cref="F:System.Byte.MaxValue" /> を超える数値を表しています。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.Int32)">
      <summary>指定した基数での数値の文字列形式を、等価の 8 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 8 ビット符号なし整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <param name="fromBase">
        <paramref name="value" /> 内の数値の基数。これは 2、8、10、または 16 である必要があります。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> が 2、8、10、または 16 ではありません。または基数が 10 以外の符号なし数値を表す <paramref name="value" /> がマイナス記号で始まっています。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> は <see cref="F:System.String.Empty" /> です。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> には、<paramref name="fromBase" /> で指定された基数に対して有効な桁を示す数字以外の文字が含まれています。例外メッセージは、<paramref name="value" /> の最初の文字が無効な場合は変換する数字が存在しないことを示し、それ以外の場合は <paramref name="value" /> の末尾に無効な文字が含まれることを示します。</exception>
      <exception cref="T:System.OverflowException">基数が 10 以外の符号なし数値を表す <paramref name="value" /> がマイナス記号で始まっています。または<paramref name="value" /> が <see cref="F:System.Byte.MinValue" /> 未満の数値か、<see cref="F:System.Byte.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt16)">
      <summary>Converts the value of the specified 16-bit unsigned integer to an equivalent 8-bit unsigned integer.</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号なし整数。</returns>
      <param name="value">変換する 16 ビット符号なし整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Byte.MaxValue" /> より大きくなっています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt32)">
      <summary>指定した 32 ビット符号なし整数の値を等価の 8 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号なし整数。</returns>
      <param name="value">変換する 32 ビット符号なし整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Byte.MaxValue" /> より大きくなっています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt64)">
      <summary>指定した 64 ビット符号なし整数の値を等価の 8 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号なし整数。</returns>
      <param name="value">変換する 64 ビット符号なし整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Byte.MaxValue" /> より大きくなっています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Byte)">
      <summary>指定した 8 ビット符号なし整数の値を等価の Unicode 文字に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の Unicode 文字。</returns>
      <param name="value">変換する 8 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int16)">
      <summary>指定した 16 ビット符号付き整数の値を等価の Unicode 文字に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の Unicode 文字。</returns>
      <param name="value">変換する 16 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> は <see cref="F:System.Char.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int32)">
      <summary>指定した 32 ビット符号付き整数の値を等価の Unicode 文字に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の Unicode 文字。</returns>
      <param name="value">変換する 32 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Char.MinValue" /> より小さい値か、<see cref="F:System.Char.MaxValue" /> より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int64)">
      <summary>指定した 64 ビット符号付き整数の値を等価の Unicode 文字に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の Unicode 文字。</returns>
      <param name="value">変換する 64 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Char.MinValue" /> より小さい値か、<see cref="F:System.Char.MaxValue" /> より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object)">
      <summary>指定したオブジェクトの値を Unicode 文字に変換します。</summary>
      <returns>value と等価の Unicode 文字。<paramref name="value" /> が null の場合は、<see cref="F:System.Char.MinValue" />。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> は null 文字列です。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または<paramref name="value" /> から <see cref="T:System.Char" /> への変換はサポートされていません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Char.MinValue" /> より小さい値か、<see cref="F:System.Char.MaxValue" /> より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object,System.IFormatProvider)">
      <summary>指定したカルチャ固有の書式情報を使用して、指定したオブジェクトの値を等価の Unicode 文字に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の Unicode 文字。<paramref name="value" /> が null の場合は、<see cref="F:System.Char.MinValue" />。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクト。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> は null 文字列です。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または<paramref name="value" /> から <see cref="T:System.Char" /> への変換はサポートされていません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Char.MinValue" /> より小さい値か、<see cref="F:System.Char.MaxValue" /> より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.SByte)">
      <summary>指定した 8 ビット符号付き整数の値を等価の Unicode 文字に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の Unicode 文字。</returns>
      <param name="value">変換する 8 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> は <see cref="F:System.Char.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String)">
      <summary>指定した文字列の 1 番目の文字を Unicode 文字に変換します。</summary>
      <returns>
        <paramref name="value" /> の唯一の文字、つまり 1 番目の文字と等価の Unicode 文字。</returns>
      <param name="value">長さ 1 の文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> は null です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の長さが 1 ではありません。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した文字列の 1 番目の文字を Unicode 文字に変換します。</summary>
      <returns>
        <paramref name="value" /> の唯一の文字、つまり 1 番目の文字と等価の Unicode 文字。</returns>
      <param name="value">長さ 1 の文字列または null。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。このパラメーターは無視されます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> は null です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の長さが 1 ではありません。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt16)">
      <summary>指定した 16 ビット符号なし整数の値を等価の Unicode 文字に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の Unicode 文字。</returns>
      <param name="value">変換する 16 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt32)">
      <summary>指定した 32 ビット符号なし整数の値を等価の Unicode 文字に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の Unicode 文字。</returns>
      <param name="value">変換する 32 ビット符号なし整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Char.MaxValue" /> より大きくなっています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt64)">
      <summary>指定した 64 ビット符号なし整数の値を等価の Unicode 文字に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の Unicode 文字。</returns>
      <param name="value">変換する 64 ビット符号なし整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Char.MaxValue" /> より大きくなっています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object)">
      <summary>指定したオブジェクトの値を <see cref="T:System.DateTime" /> オブジェクトに変換します。</summary>
      <returns>
        <paramref name="value" /> の値と等価の日付と時刻。<paramref name="value" /> が null の場合は、<see cref="F:System.DateTime.MinValue" /> と等価の日付と時刻。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクトか、または null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> は有効な日付と時刻の値ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または変換はサポートされていません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定したオブジェクトの値を <see cref="T:System.DateTime" /> オブジェクトに変換します。</summary>
      <returns>
        <paramref name="value" /> の値と等価の日付と時刻。<paramref name="value" /> が null の場合は、<see cref="F:System.DateTime.MinValue" /> と等価の日付と時刻。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクト。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> は有効な日付と時刻の値ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または変換はサポートされていません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String)">
      <summary>指定した日付と時刻の文字列形式を等価の日付と時刻の値に変換します。</summary>
      <returns>
        <paramref name="value" /> の値と等価の日付と時刻。<paramref name="value" /> が null の場合は、<see cref="F:System.DateTime.MinValue" /> と等価の日付と時刻。</returns>
      <param name="value">日付と時刻の文字列形式。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> が、書式が正しく設定されていない日付と時刻の文字列です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した数値の文字列形式を等価な日付と時刻に変換します。</summary>
      <returns>
        <paramref name="value" /> の値と等価の日付と時刻。<paramref name="value" /> が null の場合は、<see cref="F:System.DateTime.MinValue" /> と等価の日付と時刻。</returns>
      <param name="value">変換する日時を含む文字列。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> が、書式が正しく設定されていない日付と時刻の文字列です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Boolean)">
      <summary>指定したブール値を等価の 10 進数に変換します。</summary>
      <returns>
        <paramref name="value" /> が true の場合は数値の 1。それ以外の場合は 0。</returns>
      <param name="value">変換するブール値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Byte)">
      <summary>指定した 8 ビット符号なし整数の値を等価の 10 進数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 10 進数。</returns>
      <param name="value">変換する 8 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Decimal)">
      <summary>指定した 10 進数を返します。実際の変換は行われません。</summary>
      <returns>
        <paramref name="value" /> は変更されずに返されます。</returns>
      <param name="value">10 進数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Double)">
      <summary>指定した倍精度浮動小数点数値を等価の 10 進数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 10 進数。 </returns>
      <param name="value">変換する倍精度浮動小数点数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Decimal.MaxValue" /> より大きい値か、<see cref="F:System.Decimal.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int16)">
      <summary>指定した 16 ビット符号付き整数の値を等価の 10 進数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 10 進数。</returns>
      <param name="value">変換する 16 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int32)">
      <summary>指定した 32 ビット符号付き整数の値を等価の 10 進数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 10 進数。</returns>
      <param name="value">変換する 32 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int64)">
      <summary>指定した 64 ビット符号付き整数の値を等価の 10 進数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 10 進数。</returns>
      <param name="value">変換する 64 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object)">
      <summary>指定したオブジェクトの値を等価の 10 進数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 10 進数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクトか、または null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> は、<see cref="T:System.Decimal" /> 型の適切な形式ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または変換はサポートされていません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Decimal.MinValue" /> 未満の数値か、<see cref="F:System.Decimal.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定したオブジェクトの値を等価の 10 進数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 10 進数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクト。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> は、<see cref="T:System.Decimal" /> 型の適切な形式ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または変換はサポートされていません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Decimal.MinValue" /> 未満の数値か、<see cref="F:System.Decimal.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.SByte)">
      <summary>指定した 8 ビット符号付き整数の値を等価の 10 進数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 10 進数。</returns>
      <param name="value">変換する 8 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Single)">
      <summary>指定した単精度浮動小数点数値を等価の 10 進数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 10 進数。 </returns>
      <param name="value">変換する単精度浮動小数点数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Decimal.MaxValue" /> より大きい値か、<see cref="F:System.Decimal.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String)">
      <summary>指定した数値の文字列形式を等価の 10 進数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 10 進数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> が、有効な書式の数値ではありません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Decimal.MinValue" /> 未満の数値か、<see cref="F:System.Decimal.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した数値の文字列形式を等価の 10 進数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 10 進数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> が、有効な書式の数値ではありません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Decimal.MinValue" /> 未満の数値か、<see cref="F:System.Decimal.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt16)">
      <summary>指定した 16 ビット符号なし整数の値を等価の 10 進数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 10 進数。</returns>
      <param name="value">変換する 16 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt32)">
      <summary>指定した 32 ビット符号なし整数の値を等価の 10 進数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 10 進数。</returns>
      <param name="value">変換する 32 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt64)">
      <summary>指定した 64 ビット符号なし整数の値を等価の 10 進数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 10 進数。</returns>
      <param name="value">変換する 64 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Boolean)">
      <summary>指定したブール値を等価の倍精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> が true の場合は数値の 1。それ以外の場合は 0。</returns>
      <param name="value">変換するブール値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Byte)">
      <summary>指定した 8 ビット符号なし整数の値を等価の倍精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の倍精度浮動小数点数。</returns>
      <param name="value">変換する 8 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Decimal)">
      <summary>指定した 10 進数値を等価の倍精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の倍精度浮動小数点数。</returns>
      <param name="value">変換する 10 進数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Double)">
      <summary>指定した倍精度浮動小数点数を返します。実際の変換は行われません。</summary>
      <returns>
        <paramref name="value" /> は変更されずに返されます。</returns>
      <param name="value">返される倍精度浮動小数点数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int16)">
      <summary>指定した 16 ビット符号付き整数の値を等価の倍精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の倍精度浮動小数点数。</returns>
      <param name="value">変換する 16 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int32)">
      <summary>指定した 32 ビット符号付き整数の値を等価の倍精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の倍精度浮動小数点数。</returns>
      <param name="value">変換する 32 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int64)">
      <summary>指定した 64 ビット符号付き整数の値を等価の倍精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の倍精度浮動小数点数。</returns>
      <param name="value">変換する 64 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object)">
      <summary>指定したオブジェクトの値を倍精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の倍精度浮動小数点数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクトか、または null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> は、<see cref="T:System.Double" /> 型の適切な形式ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または変換はサポートされていません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Double.MinValue" /> 未満の数値か、<see cref="F:System.Double.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定したオブジェクトの値を倍精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の倍精度浮動小数点数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクト。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> は、<see cref="T:System.Double" /> 型の適切な形式ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Double.MinValue" /> 未満の数値か、<see cref="F:System.Double.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.SByte)">
      <summary>指定した 8 ビット符号付き整数の値を等価の倍精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号付き整数。</returns>
      <param name="value">変換する 8 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Single)">
      <summary>指定した単精度浮動小数点数値を等価の倍精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の倍精度浮動小数点数。</returns>
      <param name="value">単精度浮動小数点数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String)">
      <summary>指定した数値の文字列形式を等価の倍精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の倍精度浮動小数点数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> が、有効な書式の数値ではありません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Double.MinValue" /> 未満の数値か、<see cref="F:System.Double.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した数値の文字列形式を等価の倍精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の倍精度浮動小数点数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> が、有効な書式の数値ではありません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Double.MinValue" /> 未満の数値か、<see cref="F:System.Double.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt16)">
      <summary>指定した 16 ビット符号なし整数の値を等価の倍精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の倍精度浮動小数点数。</returns>
      <param name="value">変換する 16 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt32)">
      <summary>指定した 32 ビット符号なし整数の値を等価の倍精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の倍精度浮動小数点数。</returns>
      <param name="value">変換する 32 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt64)">
      <summary>指定した 64 ビット符号なし整数の値を等価の倍精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の倍精度浮動小数点数。</returns>
      <param name="value">変換する 64 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Boolean)">
      <summary>指定したブール値を等価の 16 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> が true の場合は数値の 1。それ以外の場合は 0。</returns>
      <param name="value">変換するブール値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Byte)">
      <summary>指定した 8 ビット符号なし整数の値を等価の 16 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 16 ビット符号付き整数。</returns>
      <param name="value">変換する 8 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Char)">
      <summary>指定した Unicode 文字の値を等価の 16 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 16 ビット符号付き整数。 </returns>
      <param name="value">変換する Unicode 文字。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int16.MaxValue" /> より大きくなっています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Decimal)">
      <summary>指定した 10 進数値を等価の 16 ビット符号付き整数に変換します。</summary>
      <returns>近似値の 16 ビット符号付き整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する 10 進数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int16.MaxValue" /> より大きい値か、<see cref="F:System.Int16.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Double)">
      <summary>指定した倍精度浮動小数点数値を等価の 16 ビット符号付き整数に変換します。</summary>
      <returns>近似値の 16 ビット符号付き整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する倍精度浮動小数点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int16.MaxValue" /> より大きい値か、<see cref="F:System.Int16.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int16)">
      <summary>指定した 16 ビット符号付き整数が返されます。実際の変換は行われません。</summary>
      <returns>
        <paramref name="value" /> は変更されずに返されます。</returns>
      <param name="value">返される 16 ビット符号付き整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int32)">
      <summary>指定した 32 ビット符号付き整数の値を等価の 16 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 16 ビット符号付き整数。</returns>
      <param name="value">変換する 32 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int16.MaxValue" /> より大きい値か、<see cref="F:System.Int16.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int64)">
      <summary>指定した 64 ビット符号付き整数の値を等価の 16 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 16 ビット符号付き整数。</returns>
      <param name="value">変換する 64 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int16.MaxValue" /> より大きい値か、<see cref="F:System.Int16.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object)">
      <summary>指定したオブジェクトの値を 16 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 16 ビット符号付き整数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクトか、または null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> は、<see cref="T:System.Int16" /> 型の適切な形式ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または変換はサポートされていません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int16.MinValue" /> 未満の数値か、<see cref="F:System.Int16.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定したオブジェクトの値を 16 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 16 ビット符号付き整数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクト。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> は、<see cref="T:System.Int16" /> 型の適切な形式ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> を実装していません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int16.MinValue" /> 未満の数値か、<see cref="F:System.Int16.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.SByte)">
      <summary>指定した 8 ビット符号付き整数の値を等価の 16 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号付き整数。</returns>
      <param name="value">変換する 8 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Single)">
      <summary>指定した単精度浮動小数点数の値を等価の 16 ビット符号付き整数に変換します。</summary>
      <returns>近似値の 16 ビット符号付き整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する単精度浮動小数点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int16.MaxValue" /> より大きい値か、<see cref="F:System.Int16.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String)">
      <summary>指定した数値の文字列形式を等価の 16 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 16 ビット符号付き整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の構成が、省略可能な符号と、それに続く 0 から 9 までの一連の数字ではありません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int16.MinValue" /> 未満の数値か、<see cref="F:System.Int16.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した数値の文字列形式を等価の 16 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 16 ビット符号付き整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の構成が、省略可能な符号と、それに続く 0 から 9 までの一連の数字ではありません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int16.MinValue" /> 未満の数値か、<see cref="F:System.Int16.MaxValue" /> を超える数値を表しています。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.Int32)">
      <summary>指定した基数での数値の文字列形式を、等価の 16 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 16 ビット符号付き整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <param name="fromBase">
        <paramref name="value" /> 内の数値の基数。これは 2、8、10、または 16 である必要があります。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> が 2、8、10、または 16 ではありません。またはBase10 形式ではない符号付き数値を表す <paramref name="value" /> がマイナス記号で始まっています。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> は <see cref="F:System.String.Empty" /> です。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> には、<paramref name="fromBase" /> で指定された基数に対して有効な桁を示す数字以外の文字が含まれています。例外メッセージは、<paramref name="value" /> の最初の文字が無効な場合は変換する数字が存在しないことを示し、それ以外の場合は <paramref name="value" /> の末尾に無効な文字が含まれることを示します。</exception>
      <exception cref="T:System.OverflowException">Base10 形式ではない符号付き数値を表す <paramref name="value" /> がマイナス記号で始まっています。または<paramref name="value" /> が <see cref="F:System.Int16.MinValue" /> 未満の数値か、<see cref="F:System.Int16.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt16)">
      <summary>指定した 16 ビット符号なし整数の値を等価の 16 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 16 ビット符号付き整数。</returns>
      <param name="value">変換する 16 ビット符号なし整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int16.MaxValue" /> より大きくなっています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt32)">
      <summary>指定した 32 ビット符号なし整数の値を等価の 16 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 16 ビット符号付き整数。</returns>
      <param name="value">変換する 32 ビット符号なし整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int16.MaxValue" /> より大きくなっています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt64)">
      <summary>指定した 64 ビット符号なし整数の値を等価の 16 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 16 ビット符号付き整数。</returns>
      <param name="value">変換する 64 ビット符号なし整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int16.MaxValue" /> より大きくなっています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Boolean)">
      <summary>指定したブール値を等価の 32 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> が true の場合は数値の 1。それ以外の場合は 0。</returns>
      <param name="value">変換するブール値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Byte)">
      <summary>指定した 8 ビット符号なし整数の値を等価の 32 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 32 ビット符号付き整数。</returns>
      <param name="value">変換する 8 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Char)">
      <summary>指定した Unicode 文字の値を等価の 32 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 32 ビット符号付き整数。</returns>
      <param name="value">変換する Unicode 文字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Decimal)">
      <summary>指定した 10 進数値を等価の 32 ビット符号付き整数に変換します。</summary>
      <returns>近似値の 32 ビット符号付き整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する 10 進数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int32.MaxValue" /> より大きい値か、<see cref="F:System.Int32.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Double)">
      <summary>指定した倍精度浮動小数点数値を等価の 32 ビット符号付き整数に変換します。</summary>
      <returns>近似値の 32 ビット符号付き整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する倍精度浮動小数点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int32.MaxValue" /> より大きい値か、<see cref="F:System.Int32.MinValue" /> より小さい値です。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to an equivalent 32-bit signed integer.</summary>
      <returns>
        <paramref name="value" /> と等価の 32 ビット符号付き整数。</returns>
      <param name="value">変換する 16 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int32)">
      <summary>指定した 32 ビット符号付き整数が返されます。実際の変換は行われません。</summary>
      <returns>
        <paramref name="value" /> は変更されずに返されます。</returns>
      <param name="value">返される 32 ビット符号付き整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to an equivalent 32-bit signed integer.</summary>
      <returns>
        <paramref name="value" /> と等価の 32 ビット符号付き整数。</returns>
      <param name="value">変換する 64 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int32.MaxValue" /> より大きい値か、<see cref="F:System.Int32.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object)">
      <summary>指定したオブジェクトの値を 32 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 32 ビット符号付き整数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクトか、または null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の形式が適切ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または変換はサポートされていません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int32.MinValue" /> 未満の数値か、<see cref="F:System.Int32.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定したオブジェクトの値を 32 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 32 ビット符号付き整数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクト。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の形式が適切ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> を実装していません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int32.MinValue" /> 未満の数値か、<see cref="F:System.Int32.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.SByte)">
      <summary>指定した 8 ビット符号付き整数の値を等価の 32 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号付き整数。</returns>
      <param name="value">変換する 8 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Single)">
      <summary>指定した単精度浮動小数点数の値を等価の 32 ビット符号付き整数に変換します。</summary>
      <returns>近似値の 32 ビット符号付き整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する単精度浮動小数点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int32.MaxValue" /> より大きい値か、<see cref="F:System.Int32.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String)">
      <summary>指定した数値の文字列形式を等価の 32 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 32 ビット符号付き整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の構成が、省略可能な符号と、それに続く 0 から 9 までの一連の数字ではありません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int32.MinValue" /> 未満の数値か、<see cref="F:System.Int32.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した数値の文字列形式を等価の 32 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 32 ビット符号付き整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の構成が、省略可能な符号と、それに続く 0 から 9 までの一連の数字ではありません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int32.MinValue" /> 未満の数値か、<see cref="F:System.Int32.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.Int32)">
      <summary>指定した基数での数値の文字列形式を、等価の 32 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 32 ビット符号付き整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <param name="fromBase">
        <paramref name="value" /> 内の数値の基数。これは 2、8、10、または 16 である必要があります。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> が 2、8、10、または 16 ではありません。またはBase10 形式ではない符号付き数値を表す <paramref name="value" /> がマイナス記号で始まっています。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> は <see cref="F:System.String.Empty" /> です。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> には、<paramref name="fromBase" /> で指定された基数に対して有効な桁を示す数字以外の文字が含まれています。例外メッセージは、<paramref name="value" /> の最初の文字が無効な場合は変換する数字が存在しないことを示し、それ以外の場合は <paramref name="value" /> の末尾に無効な文字が含まれることを示します。</exception>
      <exception cref="T:System.OverflowException">Base10 形式ではない符号付き数値を表す <paramref name="value" /> がマイナス記号で始まっています。または<paramref name="value" /> が <see cref="F:System.Int32.MinValue" /> 未満の数値か、<see cref="F:System.Int32.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt16)">
      <summary>指定した 16 ビット符号なし整数の値を等価の 32 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 32 ビット符号付き整数。</returns>
      <param name="value">変換する 16 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt32)">
      <summary>指定した 32 ビット符号なし整数の値を等価の 32 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 32 ビット符号付き整数。</returns>
      <param name="value">変換する 32 ビット符号なし整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int32.MaxValue" /> より大きくなっています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent 32-bit signed integer.</summary>
      <returns>
        <paramref name="value" /> と等価の 32 ビット符号付き整数。</returns>
      <param name="value">変換する 64 ビット符号なし整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int32.MaxValue" /> より大きくなっています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Boolean)">
      <summary>指定したブール値を等価の 64 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> が true の場合は数値の 1。それ以外の場合は 0。</returns>
      <param name="value">変換するブール値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Byte)">
      <summary>指定した 8 ビット符号なし整数の値を等価の 64 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号付き整数。</returns>
      <param name="value">変換する 8 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Char)">
      <summary>指定した Unicode 文字の値を等価の 64 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号付き整数。</returns>
      <param name="value">変換する Unicode 文字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Decimal)">
      <summary>指定した 10 進数値を等価の 64 ビット符号付き整数に変換します。</summary>
      <returns>近似値の 64 ビット符号付き整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する 10 進数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int64.MaxValue" /> より大きい値か、<see cref="F:System.Int64.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Double)">
      <summary>指定した倍精度浮動小数点数値を等価の 64 ビット符号付き整数に変換します。</summary>
      <returns>近似値の 64 ビット符号付き整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する倍精度浮動小数点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int64.MaxValue" /> より大きい値か、<see cref="F:System.Int64.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int16)">
      <summary>指定した 16 ビット符号付き整数の値を等価の 64 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号付き整数。</returns>
      <param name="value">変換する 16 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int32)">
      <summary>指定した 32 ビット符号付き整数の値を等価の 64 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号付き整数。</returns>
      <param name="value">変換する 32 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int64)">
      <summary>指定した 64 ビット符号付き整数が返されます。実際の変換は行われません。</summary>
      <returns>
        <paramref name="value" /> は変更されずに返されます。</returns>
      <param name="value">64 ビット符号付き整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object)">
      <summary>指定したオブジェクトの値を 64 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号付き整数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクトか、または null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の形式が適切ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または変換はサポートされていません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int64.MinValue" /> 未満の数値か、<see cref="F:System.Int64.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定したオブジェクトの値を 64 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号付き整数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクト。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の形式が適切ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または変換はサポートされていません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int64.MinValue" /> 未満の数値か、<see cref="F:System.Int64.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.SByte)">
      <summary>指定した 8 ビット符号付き整数の値を等価の 64 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号付き整数。</returns>
      <param name="value">変換する 8 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Single)">
      <summary>指定した単精度浮動小数点数の値を等価の 64 ビット符号付き整数に変換します。</summary>
      <returns>近似値の 64 ビット符号付き整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する単精度浮動小数点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int64.MaxValue" /> より大きい値か、<see cref="F:System.Int64.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String)">
      <summary>指定した数値の文字列形式を等価の 64 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 64 ビット符号付き整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の構成が、省略可能な符号と、それに続く 0 から 9 までの一連の数字ではありません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int64.MinValue" /> 未満の数値か、<see cref="F:System.Int64.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した数値の文字列形式を等価の 64 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 64 ビット符号付き整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の構成が、省略可能な符号と、それに続く 0 から 9 までの一連の数字ではありません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int64.MinValue" /> 未満の数値か、<see cref="F:System.Int64.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.Int32)">
      <summary>指定した基数での数値の文字列形式を、等価の 64 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 64 ビット符号付き整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <param name="fromBase">
        <paramref name="value" /> 内の数値の基数。これは 2、8、10、または 16 である必要があります。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> が 2、8、10、または 16 ではありません。またはBase10 形式ではない符号付き数値を表す <paramref name="value" /> がマイナス記号で始まっています。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> は <see cref="F:System.String.Empty" /> です。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> には、<paramref name="fromBase" /> で指定された基数に対して有効な桁を示す数字以外の文字が含まれています。例外メッセージは、<paramref name="value" /> の最初の文字が無効な場合は変換する数字が存在しないことを示し、それ以外の場合は <paramref name="value" /> の末尾に無効な文字が含まれることを示します。</exception>
      <exception cref="T:System.OverflowException">Base10 形式ではない符号付き数値を表す <paramref name="value" /> がマイナス記号で始まっています。または<paramref name="value" /> が <see cref="F:System.Int64.MinValue" /> 未満の数値か、<see cref="F:System.Int64.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt16)">
      <summary>指定した 16 ビット符号なし整数の値を等価の 64 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号付き整数。</returns>
      <param name="value">変換する 16 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt32)">
      <summary>指定した 32 ビット符号なし整数の値を等価の 64 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号付き整数。</returns>
      <param name="value">変換する 32 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt64)">
      <summary>指定した 64 ビット符号なし整数の値を等価の 64 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号付き整数。</returns>
      <param name="value">変換する 64 ビット符号なし整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Int64.MaxValue" /> より大きくなっています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Boolean)">
      <summary>指定したブール値を等価の 8 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> が true の場合は数値の 1。それ以外の場合は 0。</returns>
      <param name="value">変換するブール値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Byte)">
      <summary>指定した 8 ビット符号なし整数の値を等価の 8 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号付き整数。</returns>
      <param name="value">変換する 8 ビット符号なし整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.SByte.MaxValue" /> より大きくなっています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Char)">
      <summary>指定した Unicode 文字の値を等価の 8 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号付き整数。</returns>
      <param name="value">変換する Unicode 文字。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.SByte.MaxValue" /> より大きくなっています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Decimal)">
      <summary>指定した 10 進数値を等価の 8 ビット符号付き整数に変換します。</summary>
      <returns>近似値の 8 ビット符号付き整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する 10 進数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.SByte.MaxValue" /> より大きい値か、<see cref="F:System.SByte.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Double)">
      <summary>指定した倍精度浮動小数点数値を等価の 8 ビット符号付き整数に変換します。</summary>
      <returns>近似値の 8 ビット符号付き整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する倍精度浮動小数点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.SByte.MaxValue" /> より大きい値か、<see cref="F:System.SByte.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to the equivalent 8-bit signed integer.</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号付き整数。</returns>
      <param name="value">変換する 16 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.SByte.MaxValue" /> より大きい値か、<see cref="F:System.SByte.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int32)">
      <summary>指定した 32 ビット符号付き整数の値を等価の 8 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号付き整数。</returns>
      <param name="value">変換する 32 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.SByte.MaxValue" /> より大きい値か、<see cref="F:System.SByte.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int64)">
      <summary>指定した 64 ビット符号付き整数の値を等価の 8 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号付き整数。</returns>
      <param name="value">変換する 64 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.SByte.MaxValue" /> より大きい値か、<see cref="F:System.SByte.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object)">
      <summary>指定したオブジェクトの値を 8 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号付き整数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクトか、または null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の形式が適切ではありません。 </exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または変換はサポートされていません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.SByte.MinValue" /> 未満の数値か、<see cref="F:System.SByte.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定したオブジェクトの値を 8 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号付き整数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクト。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の形式が適切ではありません。 </exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または変換はサポートされていません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.SByte.MinValue" /> 未満の数値か、<see cref="F:System.SByte.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.SByte)">
      <summary>指定した 8 ビット符号付き整数が返されます。実際の変換は行われません。</summary>
      <returns>
        <paramref name="value" /> は変更されずに返されます。</returns>
      <param name="value">返される 8 ビット符号付き整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Single)">
      <summary>指定した単精度浮動小数点数の値を等価の 8 ビット符号付き整数に変換します。</summary>
      <returns>近似値の 8 ビット符号付き整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する単精度浮動小数点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.SByte.MaxValue" /> より大きい値か、<see cref="F:System.SByte.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String)">
      <summary>指定した数値の文字列形式を等価の 8 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 8 ビット符号付き整数。value が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の構成が、省略可能な符号と、それに続く 0 から 9 までの一連の数字ではありません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.SByte.MinValue" /> 未満の数値か、<see cref="F:System.SByte.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した数値の文字列形式を等価の 8 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号付き整数。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> は null です。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の構成が、省略可能な符号と、それに続く 0 から 9 までの一連の数字ではありません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.SByte.MinValue" /> 未満の数値か、<see cref="F:System.SByte.MaxValue" /> を超える数値を表しています。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.Int32)">
      <summary>指定した基数での数値の文字列形式を、等価の 8 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 8 ビット符号付き整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <param name="fromBase">
        <paramref name="value" /> 内の数値の基数。これは 2、8、10、または 16 である必要があります。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> が 2、8、10、または 16 ではありません。またはBase10 形式ではない符号付き数値を表す <paramref name="value" /> がマイナス記号で始まっています。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> は <see cref="F:System.String.Empty" /> です。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> には、<paramref name="fromBase" /> で指定された基数に対して有効な桁を示す数字以外の文字が含まれています。例外メッセージは、<paramref name="value" /> の最初の文字が無効な場合は変換する数字が存在しないことを示し、それ以外の場合は <paramref name="value" /> の末尾に無効な文字が含まれることを示します。</exception>
      <exception cref="T:System.OverflowException">Base10 形式ではない符号付き数値を表す <paramref name="value" /> がマイナス記号で始まっています。または<paramref name="value" /> が <see cref="F:System.SByte.MinValue" /> 未満の数値か、<see cref="F:System.SByte.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt16)">
      <summary>指定した 16 ビット符号なし整数の値を等価の 8 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号付き整数。</returns>
      <param name="value">変換する 16 ビット符号なし整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.SByte.MaxValue" /> より大きくなっています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt32)">
      <summary>指定した 32 ビット符号なし整数の値を等価の 8 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号付き整数。</returns>
      <param name="value">変換する 32 ビット符号なし整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.SByte.MaxValue" /> より大きい値か、<see cref="F:System.SByte.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt64)">
      <summary>指定した 64 ビット符号なし整数の値を等価の 8 ビット符号付き整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号付き整数。</returns>
      <param name="value">変換する 64 ビット符号なし整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.SByte.MaxValue" /> より大きい値か、<see cref="F:System.SByte.MinValue" /> より小さい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Boolean)">
      <summary>指定したブール値を等価の単精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> が true の場合は数値の 1。それ以外の場合は 0。</returns>
      <param name="value">変換するブール値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Byte)">
      <summary>指定した 8 ビット符号なし整数の値を等価の単精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の単精度浮動小数点数。</returns>
      <param name="value">変換する 8 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Decimal)">
      <summary>指定した 10 進数値を等価の単精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の単精度浮動小数点数。<paramref name="value" /> は近似値に丸められます。たとえば、小数点以下 2 桁に丸められる場合、値 2.345 は 2.34 になり、値 2.355 は 2.36 になります。</returns>
      <param name="value">変換する 10 進数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Double)">
      <summary>指定した倍精度浮動小数点数値を等価の単精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の単精度浮動小数点数。<paramref name="value" /> は近似値に丸められます。たとえば、小数点以下 2 桁に丸められる場合、値 2.345 は 2.34 になり、値 2.355 は 2.36 になります。</returns>
      <param name="value">変換する倍精度浮動小数点数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int16)">
      <summary>指定した 16 ビット符号付き整数の値を等価の単精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の単精度浮動小数点数。</returns>
      <param name="value">変換する 16 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int32)">
      <summary>指定した 32 ビット符号付き整数の値を等価の単精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の単精度浮動小数点数。</returns>
      <param name="value">変換する 32 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int64)">
      <summary>指定した 64 ビット符号付き整数の値を等価の単精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の単精度浮動小数点数。</returns>
      <param name="value">変換する 64 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object)">
      <summary>指定したオブジェクトの値を単精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の単精度浮動小数点数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクトか、または null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の形式が適切ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または変換はサポートされていません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Single.MinValue" /> 未満の数値か、<see cref="F:System.Single.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定したオブジェクトの値を単精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の単精度浮動小数点数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクト。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の形式が適切ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> を実装していません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Single.MinValue" /> 未満の数値か、<see cref="F:System.Single.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.SByte)">
      <summary>指定した 8 ビット符号付き整数の値を等価の単精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 8 ビット符号付き整数。</returns>
      <param name="value">変換する 8 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Single)">
      <summary>指定した単精度浮動小数点数を返します。実際の変換は行われません。</summary>
      <returns>
        <paramref name="value" /> は変更されずに返されます。</returns>
      <param name="value">返される単精度浮動小数点数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String)">
      <summary>指定した数値の文字列形式を等価の単精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の単精度浮動小数点数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> が、有効な書式の数値ではありません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Single.MinValue" /> 未満の数値か、<see cref="F:System.Single.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した数値の文字列形式を等価の単精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の単精度浮動小数点数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> が、有効な書式の数値ではありません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.Single.MinValue" /> 未満の数値か、<see cref="F:System.Single.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt16)">
      <summary>指定した 16 ビット符号なし整数の値を等価の単精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の単精度浮動小数点数。</returns>
      <param name="value">変換する 16 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt32)">
      <summary>指定した 32 ビット符号なし整数の値を等価の単精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の単精度浮動小数点数。</returns>
      <param name="value">変換する 32 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt64)">
      <summary>指定した 64 ビット符号なし整数の値を等価の単精度浮動小数点数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の単精度浮動小数点数。</returns>
      <param name="value">変換する 64 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean)">
      <summary>指定したブール値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換するブール値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean,System.IFormatProvider)">
      <summary>指定したブール値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換するブール値。</param>
      <param name="provider">オブジェクトのインスタンス。このパラメーターは無視されます。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte)">
      <summary>指定した 8 ビット符号なし整数の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する 8 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した 8 ビット符号なし整数の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する 8 ビット符号なし整数。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.Int32)">
      <summary>8 ビット符号なし整数値を、指定した基数で表される等価な文字列形式に変換します。</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">変換する 8 ビット符号なし整数。</param>
      <param name="toBase">戻り値の基数。これは 2、8、10、または 16 である必要があります。 </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> が 2、8、10、または 16 ではありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char)">
      <summary>指定した Unicode 文字の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する Unicode 文字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した Unicode 文字の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する Unicode 文字。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。このパラメーターは無視されます。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime)">
      <summary>指定した <see cref="T:System.DateTime" /> の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する日時の値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した <see cref="T:System.DateTime" /> の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する日時の値。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal)">
      <summary>指定した 10 進数値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する 10 進数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した 10 進数値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する 10 進数。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double)">
      <summary>指定した倍精度浮動小数点数値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する倍精度浮動小数点数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double,System.IFormatProvider)">
      <summary>指定した倍精度浮動小数点数値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する倍精度浮動小数点数。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16)">
      <summary>指定した 16 ビット符号付き整数の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する 16 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した 16 ビット符号付き整数の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する 16 ビット符号付き整数。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.Int32)">
      <summary>16 ビット符号付き整数の値を、指定した基数での等価の文字列形式に変換します。</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">変換する 16 ビット符号付き整数。</param>
      <param name="toBase">戻り値の基数。これは 2、8、10、または 16 である必要があります。 </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> が 2、8、10、または 16 ではありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32)">
      <summary>指定した 32 ビット符号付き整数の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する 32 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した 32 ビット符号付き整数の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する 32 ビット符号付き整数。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.Int32)">
      <summary>32 ビット符号付き整数の値を、指定した基数での等価の文字列形式に変換します。</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">変換する 32 ビット符号付き整数。</param>
      <param name="toBase">戻り値の基数。これは 2、8、10、または 16 である必要があります。 </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> が 2、8、10、または 16 ではありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64)">
      <summary>指定した 64 ビット符号付き整数の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する 64 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した 64 ビット符号付き整数の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する 64 ビット符号付き整数。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.Int32)">
      <summary>64 ビット符号付き整数の値を、指定した基数での等価の文字列形式に変換します。</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">変換する 64 ビット符号付き整数。</param>
      <param name="toBase">戻り値の基数。これは 2、8、10、または 16 である必要があります。 </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> が 2、8、10、または 16 ではありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object)">
      <summary>指定したオブジェクトの値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列表現。または、<paramref name="value" /> が、null という値を持つオブジェクトである場合は、<see cref="F:System.String.Empty" />。<paramref name="value" /> が null の場合、メソッドは null を返します。</returns>
      <param name="value">変換する値を提供するオブジェクト、または null。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object,System.IFormatProvider)">
      <summary>指定したカルチャ固有の書式情報を使用して、指定したオブジェクトの値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列表現。または、<paramref name="value" /> が、null という値を持つオブジェクトである場合は、<see cref="F:System.String.Empty" />。<paramref name="value" /> が null の場合、メソッドは null を返します。</returns>
      <param name="value">変換する値を提供するオブジェクト、または null。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte)">
      <summary>指定した 8 ビット符号付き整数の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する 8 ビット符号付き整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した 8 ビット符号付き整数の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する 8 ビット符号付き整数。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single)">
      <summary>指定した単精度浮動小数点数値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する単精度浮動小数点数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した単精度浮動小数点数値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する単精度浮動小数点数。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16)">
      <summary>指定した 16 ビット符号なし整数の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する 16 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した 16 ビット符号なし整数の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する 16 ビット符号なし整数。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32)">
      <summary>指定した 32 ビット符号なし整数の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する 32 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した 32 ビット符号なし整数の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する 32 ビット符号なし整数。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64)">
      <summary>指定した 64 ビット符号なし整数の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する 64 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した 64 ビット符号なし整数の値を等価の文字列形式に変換します。</summary>
      <returns>
        <paramref name="value" /> の文字列形式。</returns>
      <param name="value">変換する 64 ビット符号なし整数。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Boolean)">
      <summary>指定したブール値を等価の 16 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> が true の場合は数値の 1。それ以外の場合は 0。</returns>
      <param name="value">変換するブール値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to the equivalent 16-bit unsigned integer.</summary>
      <returns>
        <paramref name="value" /> と等価の 16 ビット符号なし整数。</returns>
      <param name="value">変換する 8 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Char)">
      <summary>指定した Unicode 文字の値を等価の 16 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価な 16 ビット符号なし整数。</returns>
      <param name="value">変換する Unicode 文字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Decimal)">
      <summary>指定した 10 進数値を等価の 16 ビット符号なし整数に変換します。</summary>
      <returns>近似値の 16 ビット符号なし整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する 10 進数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満の値か、<see cref="F:System.UInt16.MaxValue" /> より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Double)">
      <summary>指定した倍精度浮動小数点数値を等価の 16 ビット符号なし整数に変換します。</summary>
      <returns>近似値の 16 ビット符号なし整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する倍精度浮動小数点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満の値か、<see cref="F:System.UInt16.MaxValue" /> より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int16)">
      <summary>指定した 16 ビット符号付き整数の値を等価の 16 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 16 ビット符号なし整数。</returns>
      <param name="value">変換する 16 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int32)">
      <summary>指定した 32 ビット符号付き整数の値を等価の 16 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 16 ビット符号なし整数。</returns>
      <param name="value">変換する 32 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満の値か、<see cref="F:System.UInt16.MaxValue" /> より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int64)">
      <summary>指定した 64 ビット符号付き整数の値を等価の 16 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 16 ビット符号なし整数。</returns>
      <param name="value">変換する 64 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満の値か、<see cref="F:System.UInt16.MaxValue" /> より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object)">
      <summary>指定したオブジェクトの値を 16 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 16 ビット符号なし整数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクトか、または null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の形式が適切ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または変換はサポートされていません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.UInt16.MinValue" /> 未満の数値か、<see cref="F:System.UInt16.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定したオブジェクトの値を 16 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 16 ビット符号なし整数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクト。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の形式が適切ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または変換はサポートされていません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.UInt16.MinValue" /> 未満の数値か、<see cref="F:System.UInt16.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.SByte)">
      <summary>指定した 8 ビット符号付き整数の値を等価の 16 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 16 ビット符号なし整数。</returns>
      <param name="value">変換する 8 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Single)">
      <summary>指定した単精度浮動小数点数値を等価の 16 ビット符号なし整数に変換します。</summary>
      <returns>近似値の 16 ビット符号なし整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する単精度浮動小数点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満の値か、<see cref="F:System.UInt16.MaxValue" /> より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String)">
      <summary>指定した数値の文字列形式を等価の 16 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 16 ビット符号なし整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の構成が、省略可能な符号と、それに続く 0 から 9 までの一連の数字ではありません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.UInt16.MinValue" /> 未満の数値か、<see cref="F:System.UInt16.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した数値の文字列形式を等価の 16 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 16 ビット符号なし整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の構成が、省略可能な符号と、それに続く 0 から 9 までの一連の数字ではありません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.UInt16.MinValue" /> 未満の数値か、<see cref="F:System.UInt16.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.Int32)">
      <summary>指定した基数での数値の文字列形式を、等価の 16 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 16 ビット符号なし整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <param name="fromBase">
        <paramref name="value" /> 内の数値の基数。これは 2、8、10、または 16 である必要があります。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> が 2、8、10、または 16 ではありません。または基数が 10 以外の符号なし数値を表す <paramref name="value" /> がマイナス記号で始まっています。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> は <see cref="F:System.String.Empty" /> です。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> には、<paramref name="fromBase" /> で指定された基数に対して有効な桁を示す数字以外の文字が含まれています。例外メッセージは、<paramref name="value" /> の最初の文字が無効な場合は変換する数字が存在しないことを示し、それ以外の場合は <paramref name="value" /> の末尾に無効な文字が含まれることを示します。</exception>
      <exception cref="T:System.OverflowException">基数が 10 以外の符号なし数値を表す <paramref name="value" /> がマイナス記号で始まっています。または<paramref name="value" /> が <see cref="F:System.UInt16.MinValue" /> 未満の数値か、<see cref="F:System.UInt16.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt16)">
      <summary>指定した 16 ビット符号なし整数が返されます。実際の変換は行われません。</summary>
      <returns>
        <paramref name="value" /> は変更されずに返されます。</returns>
      <param name="value">返される 16 ビット符号なし整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt32)">
      <summary>指定した 32 ビット符号なし整数の値を等価の 16 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 16 ビット符号なし整数。</returns>
      <param name="value">変換する 32 ビット符号なし整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.UInt16.MaxValue" /> より大きくなっています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt64)">
      <summary>指定した 64 ビット符号なし整数の値を等価の 16 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 16 ビット符号なし整数。</returns>
      <param name="value">変換する 64 ビット符号なし整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.UInt16.MaxValue" /> より大きくなっています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Boolean)">
      <summary>指定したブール値を等価の 32 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> が true の場合は数値の 1。それ以外の場合は 0。</returns>
      <param name="value">変換するブール値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Byte)">
      <summary>指定した 8 ビット符号なし整数の値を等価の 32 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 32 ビット符号なし整数。</returns>
      <param name="value">変換する 8 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Char)">
      <summary>指定した Unicode 文字の値を等価の 32 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 32 ビット符号なし整数。</returns>
      <param name="value">変換する Unicode 文字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Decimal)">
      <summary>指定した 10 進数値を等価の 32 ビット符号なし整数に変換します。</summary>
      <returns>近似値の 32 ビット符号なし整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する 10 進数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満の値か、<see cref="F:System.UInt32.MaxValue" /> より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Double)">
      <summary>指定した倍精度浮動小数点数値を等価の 32 ビット符号なし整数に変換します。</summary>
      <returns>近似値の 32 ビット符号なし整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する倍精度浮動小数点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満の値か、<see cref="F:System.UInt32.MaxValue" /> より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int16)">
      <summary>指定した 16 ビット符号付き整数の値を等価の 32 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 32 ビット符号なし整数。</returns>
      <param name="value">変換する 16 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int32)">
      <summary>指定した 32 ビット符号付き整数の値を等価の 32 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 32 ビット符号なし整数。</returns>
      <param name="value">変換する 32 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int64)">
      <summary>指定した 64 ビット符号付き整数の値を等価の 32 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 32 ビット符号なし整数。</returns>
      <param name="value">変換する 64 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満の値か、<see cref="F:System.UInt32.MaxValue" /> より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object)">
      <summary>指定したオブジェクトの値を 32 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 32 ビット符号なし整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクトか、または null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の形式が適切ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または変換はサポートされていません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.UInt32.MinValue" /> 未満の数値か、<see cref="F:System.UInt32.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定したオブジェクトの値を 32 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 32 ビット符号なし整数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクト。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の形式が適切ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または変換はサポートされていません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.UInt32.MinValue" /> 未満の数値か、<see cref="F:System.UInt32.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.SByte)">
      <summary>指定した 8 ビット符号付き整数の値を等価の 32 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 32 ビット符号なし整数。</returns>
      <param name="value">変換する 8 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Single)">
      <summary>指定した単精度浮動小数点数値を等価の 32 ビット符号なし整数に変換します。</summary>
      <returns>近似値の 32 ビット符号なし整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する単精度浮動小数点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満の値か、<see cref="F:System.UInt32.MaxValue" /> より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String)">
      <summary>指定した数値の文字列形式を等価の 32 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 32 ビット符号なし整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の構成が、省略可能な符号と、それに続く 0 から 9 までの一連の数字ではありません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.UInt32.MinValue" /> 未満の数値か、<see cref="F:System.UInt32.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した数値の文字列形式を等価の 32 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 32 ビット符号なし整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の構成が、省略可能な符号と、それに続く 0 から 9 までの一連の数字ではありません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.UInt32.MinValue" /> 未満の数値か、<see cref="F:System.UInt32.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.Int32)">
      <summary>指定した基数での数値の文字列形式を、等価の 32 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 32 ビット符号なし整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <param name="fromBase">
        <paramref name="value" /> 内の数値の基数。これは 2、8、10、または 16 である必要があります。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> が 2、8、10、または 16 ではありません。または基数が 10 以外の符号なし数値を表す <paramref name="value" /> がマイナス記号で始まっています。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> は <see cref="F:System.String.Empty" /> です。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> には、<paramref name="fromBase" /> で指定された基数に対して有効な桁を示す数字以外の文字が含まれています。例外メッセージは、<paramref name="value" /> の最初の文字が無効な場合は変換する数字が存在しないことを示し、それ以外の場合は <paramref name="value" /> の末尾に無効な文字が含まれることを示します。</exception>
      <exception cref="T:System.OverflowException">基数が 10 以外の符号なし数値を表す <paramref name="value" /> がマイナス記号で始まっています。または<paramref name="value" /> が <see cref="F:System.UInt32.MinValue" /> 未満の数値か、<see cref="F:System.UInt32.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt16)">
      <summary>指定した 16 ビット符号なし整数の値を等価の 32 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 32 ビット符号なし整数。</returns>
      <param name="value">変換する 16 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt32)">
      <summary>指定した 32 ビット符号なし整数が返されます。実際の変換は行われません。</summary>
      <returns>
        <paramref name="value" /> は変更されずに返されます。</returns>
      <param name="value">返される 32 ビット符号なし整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt64)">
      <summary>指定した 64 ビット符号なし整数の値を等価の 32 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 32 ビット符号なし整数。</returns>
      <param name="value">変換する 64 ビット符号なし整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.UInt32.MaxValue" /> より大きくなっています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Boolean)">
      <summary>指定したブール値を等価の 64 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> が true の場合は数値の 1。それ以外の場合は 0。</returns>
      <param name="value">変換するブール値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Byte)">
      <summary>指定した 8 ビット符号なし整数の値を等価の 64 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号付き整数。</returns>
      <param name="value">変換する 8 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Char)">
      <summary>指定した Unicode 文字の値を等価の 64 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号なし整数。</returns>
      <param name="value">変換する Unicode 文字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Decimal)">
      <summary>指定した 10 進数値を等価の 64 ビット符号なし整数に変換します。</summary>
      <returns>近似値の 64 ビット符号なし整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する 10 進数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満の値か、<see cref="F:System.UInt64.MaxValue" /> より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Double)">
      <summary>指定した倍精度浮動小数点数値を等価の 64 ビット符号なし整数に変換します。</summary>
      <returns>近似値の 64 ビット符号なし整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する倍精度浮動小数点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満の値か、<see cref="F:System.UInt64.MaxValue" /> より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int16)">
      <summary>指定した 16 ビット符号付き整数の値を等価の 64 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号なし整数。</returns>
      <param name="value">変換する 16 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int32)">
      <summary>指定した 32 ビット符号付き整数の値を等価の 64 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号なし整数。</returns>
      <param name="value">変換する 32 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int64)">
      <summary>指定した 64 ビット符号付き整数の値を等価の 64 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号なし整数。</returns>
      <param name="value">変換する 64 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object)">
      <summary>指定したオブジェクトの値を 64 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号なし整数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクトか、または null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の形式が適切ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または変換はサポートされていません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.UInt64.MinValue" /> 未満の数値か、<see cref="F:System.UInt64.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定したオブジェクトの値を 64 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号なし整数。<paramref name="value" /> が null の場合は 0。</returns>
      <param name="value">
        <see cref="T:System.IConvertible" /> インターフェイスを実装するオブジェクト。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の形式が適切ではありません。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> が <see cref="T:System.IConvertible" /> インターフェイスを実装していません。または変換はサポートされていません。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.UInt64.MinValue" /> 未満の数値か、<see cref="F:System.UInt64.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.SByte)">
      <summary>指定した 8 ビット符号付き整数の値を等価の 64 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号なし整数。</returns>
      <param name="value">変換する 8 ビット符号付き整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Single)">
      <summary>指定した単精度浮動小数点数値を等価の 64 ビット符号なし整数に変換します。</summary>
      <returns>近似値の 64 ビット符号なし整数に丸められた <paramref name="value" />。<paramref name="value" /> が 2 つの整数の中間にある場合は、偶数が返されます。たとえば、4.5 は 4 に変換され、5.5 は 6 に変換されます。</returns>
      <param name="value">変換する単精度浮動小数点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が 0 未満の値か、<see cref="F:System.UInt64.MaxValue" /> より大きい値です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String)">
      <summary>指定した数値の文字列形式を等価の 64 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 64 ビット符号付き整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の構成が、省略可能な符号と、それに続く 0 から 9 までの一連の数字ではありません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.UInt64.MinValue" /> 未満の数値か、<see cref="F:System.UInt64.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.IFormatProvider)">
      <summary>指定したカルチャに固有の書式情報を使用して、指定した数値の文字列形式を等価の 64 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 64 ビット符号なし整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <param name="provider">カルチャ固有の書式情報を提供するオブジェクト。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> の構成が、省略可能な符号と、それに続く 0 から 9 までの一連の数字ではありません。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> が <see cref="F:System.UInt64.MinValue" /> 未満の数値か、<see cref="F:System.UInt64.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.Int32)">
      <summary>指定した基数での数値の文字列形式を、等価の 64 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> の数値と等価の 64 ビット符号なし整数。<paramref name="value" /> が null の場合は 0 (ゼロ)。</returns>
      <param name="value">変換する数値を含んだ文字列。</param>
      <param name="fromBase">
        <paramref name="value" /> 内の数値の基数。これは 2、8、10、または 16 である必要があります。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> が 2、8、10、または 16 ではありません。または基数が 10 以外の符号なし数値を表す <paramref name="value" /> がマイナス記号で始まっています。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> は <see cref="F:System.String.Empty" /> です。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> には、<paramref name="fromBase" /> で指定された基数に対して有効な桁を示す数字以外の文字が含まれています。例外メッセージは、<paramref name="value" /> の最初の文字が無効な場合は変換する数字が存在しないことを示し、それ以外の場合は <paramref name="value" /> の末尾に無効な文字が含まれることを示します。</exception>
      <exception cref="T:System.OverflowException">基数が 10 以外の符号なし数値を表す <paramref name="value" /> がマイナス記号で始まっています。または<paramref name="value" /> が <see cref="F:System.UInt64.MinValue" /> 未満の数値か、<see cref="F:System.UInt64.MaxValue" /> を超える数値を表しています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt16)">
      <summary>指定した 16 ビット符号なし整数の値を等価の 64 ビット符号なし整数に変換します。</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号なし整数。</returns>
      <param name="value">変換する 16 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt32)">
      <summary>Converts the value of the specified 32-bit unsigned integer to an equivalent 64-bit unsigned integer.</summary>
      <returns>
        <paramref name="value" /> と等価の 64 ビット符号なし整数。</returns>
      <param name="value">変換する 32 ビット符号なし整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt64)">
      <summary>指定した 64 ビット符号なし整数が返されます。実際の変換は行われません。</summary>
      <returns>
        <paramref name="value" /> は変更されずに返されます。</returns>
      <param name="value">返される 64 ビット符号なし整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Environment">
      <summary>現在の環境およびプラットフォームに関する情報、およびそれらを操作する手段を提供します。このクラスは継承できません。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.CurrentManagedThreadId">
      <summary>現在のマネージ スレッドの一意の識別子を取得します。</summary>
      <returns>このマネージ スレッドの一意の識別子を表す整数。</returns>
    </member>
    <member name="M:System.Environment.ExpandEnvironmentVariables(System.String)">
      <summary>指定した文字列に埋め込まれている各環境変数の名前を、その変数の値を表す文字列で置換し、置換後の文字列全体を返します。</summary>
      <returns>各環境変数をその値で置換した文字列。</returns>
      <param name="name">0 個以上の環境変数の名前を格納している文字列。各環境変数は、パーセント文字 (%) で囲まれます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.FailFast(System.String)">
      <summary>メッセージを Windows アプリケーションのイベント ログに書き込んだ直後にプロセスを終了させ、Microsoft に送信するエラー レポートにそのメッセージを含めます。</summary>
      <param name="message">プロセスが終了させられた原因を説明するメッセージ。説明を提供しない場合は null。</param>
    </member>
    <member name="M:System.Environment.FailFast(System.String,System.Exception)">
      <summary>メッセージを Windows アプリケーションのイベント ログに書き込んだ直後にプロセスを終了させ、Microsoft に送信するエラー レポートにそのメッセージと例外情報を含めます。</summary>
      <param name="message">プロセスが終了させられた原因を説明するメッセージ。説明を提供しない場合は null。</param>
      <param name="exception">終了の原因となったエラーを表す例外。通常、これは catch ブロックでの例外です。</param>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariable(System.String)">
      <summary>現在のプロセスから環境変数の値を取得します。</summary>
      <returns>
        <paramref name="variable" /> で指定された環境変数の値。環境変数が見つからない場合は null。</returns>
      <param name="variable">環境変数の名前。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariables">
      <summary>すべての環境変数の名前と値を現在のプロセスから取得します。</summary>
      <returns>すべての環境変数の名前と値を保持するディクショナリ。環境変数が見つからなかった場合は空のディクショナリ。</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <exception cref="T:System.OutOfMemoryException">The buffer is out of memory.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.HasShutdownStarted">
      <summary>現在のアプリケーション ドメインがアンロード中か、または共通言語ランタイム (CLR) がシャットダウン中かどうかを示す値を取得します。</summary>
      <returns>現在のアプリケーション ドメインがアンロード中か、CLR がシャットダウン中の場合は true。それ以外の場合は false.。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.NewLine">
      <summary>この環境で定義されている改行文字列を取得します。</summary>
      <returns>UNIX 以外のプラットフォームでは "\r\n" を含む文字列。UNIX プラットフォームでは "\n" を含む文字列。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.ProcessorCount">
      <summary>現在のコンピューター上のプロセッサ数を取得します。</summary>
      <returns>現在のコンピューター上のプロセッサ数を示す 32 ビット符号付き整数。既定値はありません。現在のコンピューターが複数のプロセッサ グループを持っている場合、このプロパティは共通言語ランタイム (CLR) で使用できる論理プロセッサの数を返します。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="NUMBER_OF_PROCESSORS" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.SetEnvironmentVariable(System.String,System.String)">
      <summary>現在のプロセスに格納されている環境変数を作成、変更、または削除します。</summary>
      <param name="variable">環境変数の名前。</param>
      <param name="value">
        <paramref name="variable" /> に割り当てる値。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="variable" /> contains a zero-length string, an initial hexadecimal zero character (0x00), or an equal sign ("="). -or-The length of <paramref name="variable" /> or <paramref name="value" /> is greater than or equal to 32,767 characters.-or-An error occurred during the execution of this operation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.StackTrace">
      <summary>現在のスタック トレース情報を取得します。</summary>
      <returns>スタック トレース情報を格納している文字列。この値は、<see cref="F:System.String.Empty" /> の場合もあります。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The requested stack trace information is out of range.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.TickCount">
      <summary>システム起動後のミリ秒単位の経過時間を取得します。</summary>
      <returns>コンピューターが最後に起動してからの経過時間をミリ秒単位で保持している 32 ビット符号付き整数。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Math">
      <summary>三角関数や対数関数などの一般的な数値関数の定数と静的メソッドを提供します。この種類の .NET Framework ソース コードを参照して、次を参照してください。、 Reference Sourceです。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Decimal)">
      <summary>
        <see cref="T:System.Decimal" /> 数値の絶対値を返します。</summary>
      <returns>0 ≤ x ≤<see cref="F:System.Decimal.MaxValue" /> である 10 進数 x。</returns>
      <param name="value">
        <see cref="F:System.Decimal.MinValue" /> 以上で、<see cref="F:System.Decimal.MaxValue" /> 以下の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Double)">
      <summary>倍精度浮動小数点数の絶対値を返します。</summary>
      <returns>0 ≤ x ≤<see cref="F:System.Double.MaxValue" /> である倍精度浮動小数点数 x。</returns>
      <param name="value">
        <see cref="F:System.Double.MinValue" /> 以上で、<see cref="F:System.Double.MaxValue" /> 以下の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int16)">
      <summary>16 ビット符号付き整数の絶対値を返します。</summary>
      <returns>0 ≤ x ≤<see cref="F:System.Int16.MaxValue" /> である 16 ビット符号付き整数 x。</returns>
      <param name="value">
        <see cref="F:System.Int16.MinValue" /> より大きく、<see cref="F:System.Int16.MaxValue" /> 以下の数値。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> と <see cref="F:System.Int16.MinValue" /> は等しい。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int32)">
      <summary>32 ビット符号付き整数の絶対値を返します。</summary>
      <returns>0 ≤ x ≤<see cref="F:System.Int32.MaxValue" /> である 32 ビット符号付き整数 x。</returns>
      <param name="value">
        <see cref="F:System.Int32.MinValue" /> より大きく、<see cref="F:System.Int32.MaxValue" /> 以下の数値。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> と <see cref="F:System.Int32.MinValue" /> は等しい。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int64)">
      <summary>64 ビット符号付き整数の絶対値を返します。</summary>
      <returns>0 ≤ x ≤<see cref="F:System.Int64.MaxValue" /> である 64 ビット符号付き整数 x。</returns>
      <param name="value">
        <see cref="F:System.Int64.MinValue" /> より大きく、<see cref="F:System.Int64.MaxValue" /> 以下の数値。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> と <see cref="F:System.Int64.MinValue" /> は等しい。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.SByte)">
      <summary>8 ビット符号付き整数の絶対値を返します。</summary>
      <returns>0 ≤ x ≤<see cref="F:System.SByte.MaxValue" /> である 8 ビット符号付き整数 x。</returns>
      <param name="value">
        <see cref="F:System.SByte.MinValue" /> より大きく、<see cref="F:System.SByte.MaxValue" /> 以下の数値。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> と <see cref="F:System.SByte.MinValue" /> は等しい。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Single)">
      <summary>単精度浮動小数点数の絶対値を返します。</summary>
      <returns>0 ≤ x ≤<see cref="F:System.Single.MaxValue" /> である単精度浮動小数点数 x。</returns>
      <param name="value">
        <see cref="F:System.Single.MinValue" /> 以上で、<see cref="F:System.Single.MaxValue" /> 以下の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Acos(System.Double)">
      <summary>コサインが指定数となる角度を返します。</summary>
      <returns>0 ≤θ≤π の、ラジアンで表した角度 θ。または <paramref name="d" /> &lt; -1 または <paramref name="d" /> &gt; 1、あるいは <paramref name="d" /> が <see cref="F:System.Double.NaN" /> と等しい場合は、<see cref="F:System.Double.NaN" />。</returns>
      <param name="d">コサインを表す数で、<paramref name="d" /> が -1 以上 1 以下である必要があります。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Asin(System.Double)">
      <summary>サインが指定数となる角度を返します。</summary>
      <returns>-π/2 ≤θ≤π/2 の、ラジアンで表した角度 θ。 または <paramref name="d" /> &lt; -1 または <paramref name="d" /> &gt; 1、あるいは <paramref name="d" /> が <see cref="F:System.Double.NaN" /> と等しい場合は、<see cref="F:System.Double.NaN" />。</returns>
      <param name="d">サインを表す数で、<paramref name="d" /> が -1 以上 1 以下である必要があります。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan(System.Double)">
      <summary>タンジェントが指定数となる角度を返します。</summary>
      <returns>-π/2 ≤θ≤π/2 の、ラジアンで表した角度 θ。または <paramref name="d" /> が <see cref="F:System.Double.NaN" /> に等しい場合は <see cref="F:System.Double.NaN" />、<paramref name="d" /> が <see cref="F:System.Double.NegativeInfinity" /> に等しい場合はπ/2 を倍精度に丸めた数値 (-1.5707963267949)、または <paramref name="d" /> が <see cref="F:System.Double.PositiveInfinity" /> に等しい場合は、π/2 を倍精度に丸めた数値 (1.5707963267949)。</returns>
      <param name="d">タンジェントを表す数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan2(System.Double,System.Double)">
      <summary>タンジェントが 2 つの指定された数の商である角度を返します。</summary>
      <returns>-π≤θ≤π および tan(θ) = <paramref name="y" /> / <paramref name="x" /> の、ラジアンで示した角度 θ。(<paramref name="x" />, <paramref name="y" />) は、デカルト座標の点を示します。次の点に注意してください。(<paramref name="x" />, 、<paramref name="y" />) の四分円 1、0 &lt; θ &lt; π/2 です。(<paramref name="x" />, 、<paramref name="y" />) 2 での作業領域で π/2 &lt; θ≤πです。(<paramref name="x" />, 、<paramref name="y" />) 第 3 四分円に-π &lt; θ &lt;-π/2 です。(<paramref name="x" />, 、<paramref name="y" />) の四分円、4-π/2 &lt; θ &lt; 0 です。クワドラント間の境界上にある点の場合は、次の戻り値になります。y が 0 で x が負数でない場合は、θ = 0。y が 0 で x が負の場合は、θ = π。y が正で x が 0 の場合は、θ = π/2。y が負数で x が 0 の場合は、θ = -π/2。y が 0 かつ x が 0 の場合は、θ = 0。<paramref name="x" /> または <paramref name="y" /> が <see cref="F:System.Double.NaN" /> であるか、<paramref name="x" /> または <paramref name="y" /> が <see cref="F:System.Double.PositiveInfinity" /> または <see cref="F:System.Double.NegativeInfinity" /> のいずれである場合、メソッドは <see cref="F:System.Double.NaN" /> を返します。</returns>
      <param name="y">点の y 座標。</param>
      <param name="x">点の x 座標。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Decimal)">
      <summary>指定した 10 進数以上の数のうち、最小の整数値を返します。</summary>
      <returns>
        <paramref name="d" /> 以上の最小の整数値。このメソッドは、整数型ではなく <see cref="T:System.Decimal" /> を返します。</returns>
      <param name="d">10 進数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Double)">
      <summary>指定した倍精度浮動小数点数以上の数のうち、最小の整数値を返します。</summary>
      <returns>
        <paramref name="a" /> 以上の最小の整数値。<paramref name="a" /> が <see cref="F:System.Double.NaN" />、<see cref="F:System.Double.NegativeInfinity" />、<see cref="F:System.Double.PositiveInfinity" /> のいずれかに等しい場合は、その値が返されます。このメソッドは、整数型ではなく <see cref="T:System.Double" /> を返します。</returns>
      <param name="a">倍精度浮動小数点数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cos(System.Double)">
      <summary>指定された角度のコサインを返します。</summary>
      <returns>
        <paramref name="d" /> のコサイン。<paramref name="d" /> が <see cref="F:System.Double.NaN" />、<see cref="F:System.Double.NegativeInfinity" />、<see cref="F:System.Double.PositiveInfinity" /> のいずれかに等しい場合、このメソッドは <see cref="F:System.Double.NaN" /> を返します。</returns>
      <param name="d">ラジアンで表した角度。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cosh(System.Double)">
      <summary>指定された角度のハイパーボリック コサインを返します。</summary>
      <returns>
        <paramref name="value" /> のハイパーボリック コサイン。<paramref name="value" /> が <see cref="F:System.Double.NegativeInfinity" /> または <see cref="F:System.Double.PositiveInfinity" /> に等しい場合は、<see cref="F:System.Double.PositiveInfinity" /> が返されます。<paramref name="value" /> が <see cref="F:System.Double.NaN" /> に等しい場合は、<see cref="F:System.Double.NaN" /> が返されます。</returns>
      <param name="value">ラジアンで表した角度。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.E">
      <summary>定数 e によって示される、自然対数の底を表します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Exp(System.Double)">
      <summary>指定した値で e を累乗した値を返します。</summary>
      <returns>数値 e を <paramref name="d" /> で累乗した値。<paramref name="d" /> が <see cref="F:System.Double.NaN" /> または <see cref="F:System.Double.PositiveInfinity" /> のいずれかに等しい場合は、その値が返されます。<paramref name="d" /> が <see cref="F:System.Double.NegativeInfinity" /> に等しい場合は、0 が返されます。</returns>
      <param name="d">累乗を指定する数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Decimal)">
      <summary>指定した 10 進数以下の数のうち、最大の整数を返します。</summary>
      <returns>
        <paramref name="d" /> 以下の最大の整数。このメソッドは、<see cref="T:System.Math" /> 型の整数値を返すことに注意してください。</returns>
      <param name="d">10 進数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Double)">
      <summary>指定した倍精度浮動小数点数以下の数のうち、最大の整数を返します。</summary>
      <returns>
        <paramref name="d" /> 以下の最大の整数。<paramref name="d" /> が <see cref="F:System.Double.NaN" />、<see cref="F:System.Double.NegativeInfinity" />、<see cref="F:System.Double.PositiveInfinity" /> のいずれかに等しい場合は、その値が返されます。</returns>
      <param name="d">倍精度浮動小数点数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.IEEERemainder(System.Double,System.Double)">
      <summary>指定した数を別の指定数で除算した結果の剰余を返します。</summary>
      <returns>
        <paramref name="x" /> - (<paramref name="y" /> Q) に等しい数値。Q は <paramref name="x" /> / <paramref name="y" /> の商を丸めた最も近い整数を示します (<paramref name="x" /> / <paramref name="y" /> が 2 つの整数の中間に位置する場合は、偶数の整数)。<paramref name="x" /> - (<paramref name="y" /> Q) がゼロのとき、<paramref name="x" /> が正である場合は値 +0、<paramref name="x" /> が負である場合は -0 が返されます。<paramref name="y" /> = 0 の場合は、<see cref="F:System.Double.NaN" /> が返されます。</returns>
      <param name="x">被除数。</param>
      <param name="y">除数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double)">
      <summary>指定した数の自然 (底 e) 対数を返します。</summary>
      <returns>次の表に示した値のいずれか<paramref name="d" /> パラメーター戻り値 正 自然対数 <paramref name="d" />ですつまり、ln <paramref name="d" />, 、またはログ e 。<paramref name="d" />0 <see cref="F:System.Double.NegativeInfinity" />負 <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" /> と等価です。<see cref="F:System.Double.NaN" /><see cref="F:System.Double.PositiveInfinity" /> と等価です。<see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">対数を求める対象の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double,System.Double)">
      <summary>指定した数値の指定した底での対数を返します。</summary>
      <returns>次の表に示した値のいずれか(+Infinity は <see cref="F:System.Double.PositiveInfinity" />、-Infinity は <see cref="F:System.Double.NegativeInfinity" />、NaN は <see cref="F:System.Double.NaN" /> をそれぞれ示しています。)<paramref name="a" /><paramref name="newBase" />戻り値<paramref name="a" />&gt; 0(0 &lt;<paramref name="newBase" />&lt; 1) - または -(<paramref name="newBase" />&gt; 1)lognewBase(a)<paramref name="a" />&lt; 0(任意の値)NaN(任意の値)<paramref name="newBase" />&lt; 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = +InfinityNaN<paramref name="a" /> = NaN(任意の値)NaN(任意の値)<paramref name="newBase" /> = NaNNaN(任意の値)<paramref name="newBase" /> = 1NaN<paramref name="a" /> = 00 &lt;<paramref name="newBase" />&lt; 1 +Infinity<paramref name="a" /> = 0<paramref name="newBase" />&gt; 1-Infinity<paramref name="a" /> = + Infinity0 &lt;<paramref name="newBase" />&lt; 1-Infinity<paramref name="a" /> = + Infinity<paramref name="newBase" />&gt; 1+Infinity<paramref name="a" /> = 1<paramref name="newBase" /> = 00<paramref name="a" /> = 1<paramref name="newBase" /> = +Infinity0</returns>
      <param name="a">対数を求める対象の数値。</param>
      <param name="newBase">対数の底。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log10(System.Double)">
      <summary>指定した数の底 10 の対数を返します。</summary>
      <returns>次の表に示した値のいずれか<paramref name="d" /> パラメーター 戻り値 正 場合は、基本 10 ログ <paramref name="d" />; は、ログに記録 10<paramref name="d" />です。0 <see cref="F:System.Double.NegativeInfinity" />負 <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" /> と等価です。<see cref="F:System.Double.NaN" /><see cref="F:System.Double.PositiveInfinity" /> と等価です。<see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">対数を検索する対象の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Byte,System.Byte)">
      <summary>2 つの 8 ビット符号なし整数のうち、大きな方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか大きい方。</returns>
      <param name="val1">比較する 2 つの 8 ビット符号なし整数の最初の数値。</param>
      <param name="val2">比較する 2 つの 8 ビット符号なし整数の 2 番目の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Decimal,System.Decimal)">
      <summary>2 つの 10 進数のうち、大きい方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか大きい方。</returns>
      <param name="val1">比較する 2 つの 10 進数の最初の数。</param>
      <param name="val2">比較する 2 つの 10 進数の 2 番目の数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Double,System.Double)">
      <summary>2 つの倍精度浮動小数点数のうち、大きい方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか大きい方。<paramref name="val1" />、<paramref name="val2" />、または <paramref name="val1" /> と <paramref name="val2" /> の両方が <see cref="F:System.Double.NaN" /> に等しい場合、<see cref="F:System.Double.NaN" /> が返されます。</returns>
      <param name="val1">比較する 2 つの倍精度浮動小数点数の最初の数。</param>
      <param name="val2">比較する 2 つの倍精度浮動小数点数の 2 番目の数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int16,System.Int16)">
      <summary>2 つの 16 ビット符号付き整数のうち、大きい方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか大きい方。</returns>
      <param name="val1">比較する 2 つの 16 ビット符号付き整数の最初の数値。</param>
      <param name="val2">比較する 2 つの 16 ビット符号付き整数の 2 番目の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int32,System.Int32)">
      <summary>2 つの 32 ビット符号付き整数のうち、大きい方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか大きい方。</returns>
      <param name="val1">比較する 2 つの 32 ビット符号付き整数の最初の数値。</param>
      <param name="val2">比較する 2 つの 32 ビット符号付き整数の 2 番目の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int64,System.Int64)">
      <summary>2 つの 64 ビット符号付き整数のうち、大きい方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか大きい方。</returns>
      <param name="val1">比較する 2 つの 64 ビット符号付き整数の最初の数値。</param>
      <param name="val2">比較する 2 つの 64 ビット符号付き整数の 2 番目の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.SByte,System.SByte)">
      <summary>2 つの 8 ビット符号付き整数のうち、大きい方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか大きい方。</returns>
      <param name="val1">比較する 2 つの 8 ビット符号付き整数の最初の数値。</param>
      <param name="val2">比較する 2 つの 8 ビット符号付き整数の 2 番目の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Single,System.Single)">
      <summary>2 つの単精度浮動小数点数のうち、大きい方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか大きい方。<paramref name="val1" />、または <paramref name="val2" />、または <paramref name="val1" /> と <paramref name="val2" /> の両方が <see cref="F:System.Single.NaN" /> に等しい場合、<see cref="F:System.Single.NaN" /> が返されます。</returns>
      <param name="val1">比較する 2 つの単精度浮動小数点数の最初の数。</param>
      <param name="val2">比較する 2 つの単精度浮動小数点数の 2 番目の数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt16,System.UInt16)">
      <summary>2 つの 16 ビット符号なし整数のうち、大きな方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか大きい方。</returns>
      <param name="val1">比較する 2 つの 16 ビット符号なし整数の最初の数値。</param>
      <param name="val2">比較する 2 つの 16 ビット符号なし整数の 2 番目の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt32,System.UInt32)">
      <summary>2 つの 32 ビット符号なし整数のうち、大きな方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか大きい方。</returns>
      <param name="val1">比較する 2 つの 32 ビット符号なし整数の最初の数値。</param>
      <param name="val2">比較する 2 つの 32 ビット符号なし整数の 2 番目の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt64,System.UInt64)">
      <summary>2 つの 64 ビット符号なし整数のうち、大きな方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか大きい方。</returns>
      <param name="val1">比較する 2 つの 64 ビット符号なし整数の最初の数値。</param>
      <param name="val2">比較する 2 つの 64 ビット符号なし整数の 2 番目の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Byte,System.Byte)">
      <summary>2 つの 8 ビット符号なし整数のうち、小さい方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか小さい方。</returns>
      <param name="val1">比較する 2 つの 8 ビット符号なし整数の最初の数値。</param>
      <param name="val2">比較する 2 つの 8 ビット符号なし整数の 2 番目の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Decimal,System.Decimal)">
      <summary>2 つの 10 進数のうち、小さい方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか小さい方。</returns>
      <param name="val1">比較する 2 つの 10 進数の最初の数。</param>
      <param name="val2">比較する 2 つの 10 進数の 2 番目の数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Double,System.Double)">
      <summary>2 つの倍精度浮動小数点数のうち、小さい方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか小さい方。<paramref name="val1" />、<paramref name="val2" />、または <paramref name="val1" /> と <paramref name="val2" /> の両方が <see cref="F:System.Double.NaN" /> に等しい場合、<see cref="F:System.Double.NaN" /> が返されます。</returns>
      <param name="val1">比較する 2 つの倍精度浮動小数点数の最初の数。</param>
      <param name="val2">比較する 2 つの倍精度浮動小数点数の 2 番目の数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int16,System.Int16)">
      <summary>2 つの 16 ビット符号付き整数のうち、小さい方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか小さい方。</returns>
      <param name="val1">比較する 2 つの 16 ビット符号付き整数の最初の数値。</param>
      <param name="val2">比較する 2 つの 16 ビット符号付き整数の 2 番目の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int32,System.Int32)">
      <summary>2 つの 32 ビット符号付き整数のうち、小さい方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか小さい方。</returns>
      <param name="val1">比較する 2 つの 32 ビット符号付き整数の最初の数値。</param>
      <param name="val2">比較する 2 つの 32 ビット符号付き整数の 2 番目の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int64,System.Int64)">
      <summary>2 つの 64 ビット符号付き整数のうち、小さい方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか小さい方。</returns>
      <param name="val1">比較する 2 つの 64 ビット符号付き整数の最初の数値。</param>
      <param name="val2">比較する 2 つの 64 ビット符号付き整数の 2 番目の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.SByte,System.SByte)">
      <summary>2 つの 8 ビット符号付き整数のうち、小さい方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか小さい方。</returns>
      <param name="val1">比較する 2 つの 8 ビット符号付き整数の最初の数値。</param>
      <param name="val2">比較する 2 つの 8 ビット符号付き整数の 2 番目の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Single,System.Single)">
      <summary>2 つの単精度浮動小数点数のうち、小さい方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか小さい方。<paramref name="val1" />、<paramref name="val2" />、または <paramref name="val1" /> と <paramref name="val2" /> の両方が <see cref="F:System.Single.NaN" /> に等しい場合、<see cref="F:System.Single.NaN" /> が返されます。</returns>
      <param name="val1">比較する 2 つの単精度浮動小数点数の最初の数。</param>
      <param name="val2">比較する 2 つの単精度浮動小数点数の 2 番目の数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt16,System.UInt16)">
      <summary>2 つの 16 ビット符号なし整数のうち、小さい方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか小さい方。</returns>
      <param name="val1">比較する 2 つの 16 ビット符号なし整数の最初の数値。</param>
      <param name="val2">比較する 2 つの 16 ビット符号なし整数の 2 番目の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt32,System.UInt32)">
      <summary>2 つの 32 ビット符号なし整数のうち、小さい方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか小さい方。</returns>
      <param name="val1">比較する 2 つの 32 ビット符号なし整数の最初の数値。</param>
      <param name="val2">比較する 2 つの 32 ビット符号なし整数の 2 番目の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt64,System.UInt64)">
      <summary>2 つの 64 ビット符号なし整数のうち、小さい方を返します。</summary>
      <returns>パラメーター <paramref name="val1" /> または <paramref name="val2" /> のいずれか小さい方。</returns>
      <param name="val1">比較する 2 つの 64 ビット符号なし整数の最初の数値。</param>
      <param name="val2">比較する 2 つの 64 ビット符号なし整数の 2 番目の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.PI">
      <summary>定数 π によって示される、円の直径に対する円周の割合を表します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Pow(System.Double,System.Double)">
      <summary>指定の数値を指定した値で累乗した値を返します。</summary>
      <returns>数値 <paramref name="x" /> を <paramref name="y" /> で累乗した値。</returns>
      <param name="x">累乗対象の倍精度浮動小数点数。</param>
      <param name="y">累乗を指定する倍精度浮動小数点数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal)">
      <summary>10 進値を最も近い整数値に丸めます。</summary>
      <returns>パラメーター <paramref name="d" /> に最も近い整数。<paramref name="d" /> の小数部が 2 つの整数 (一方が偶数で、もう一方が奇数) の中間にある場合は、偶数が返されます。このメソッドは、整数型ではなく <see cref="T:System.Decimal" /> を返します。</returns>
      <param name="d">丸め対象の 10 進数。</param>
      <exception cref="T:System.OverflowException">結果が <see cref="T:System.Decimal" /> の範囲外です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32)">
      <summary>10 進値を、指定した小数部の桁数に丸めます。</summary>
      <returns>
        <paramref name="decimals" /> に等しい小数部の桁数を格納する <paramref name="d" /> に最も近い数値。</returns>
      <param name="d">丸め対象の 10 進数。</param>
      <param name="decimals">戻り値の小数部の桁数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> 0 より小さいか、28 を超えるです。</exception>
      <exception cref="T:System.OverflowException">結果が <see cref="T:System.Decimal" /> の範囲外です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32,System.MidpointRounding)">
      <summary>10 進値を、指定した小数部の桁数に丸めます。値が 2 つの数値の中間にある場合にその値を丸める方法をパラメーターで指定します。</summary>
      <returns>
        <paramref name="decimals" /> に等しい小数部の桁数を格納する <paramref name="d" /> に最も近い数値。<paramref name="d" /> の小数部の桁数が <paramref name="decimals" /> よりも少ない場合、<paramref name="d" /> がそのまま返されます。</returns>
      <param name="d">丸め対象の 10 進数。</param>
      <param name="decimals">戻り値の小数部の桁数。</param>
      <param name="mode">
        <paramref name="d" /> が 2 つの数値の中間にある場合に丸める方法を指定します。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> 0 より小さいか、28 を超えるです。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> が有効な <see cref="T:System.MidpointRounding" /> 値ではありません。</exception>
      <exception cref="T:System.OverflowException">結果が <see cref="T:System.Decimal" /> の範囲外です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.MidpointRounding)">
      <summary>10 進値を最も近い整数に丸めます。値が 2 つの数値の中間にある場合にその値を丸める方法をパラメーターで指定します。</summary>
      <returns>
        <paramref name="d" /> に最も近い整数。<paramref name="d" /> が 2 つの数値 (一方が偶数でもう一方が奇数) の中間にある場合、<paramref name="mode" /> によって 2 つの数値のどちらが返されるかが決まります。</returns>
      <param name="d">丸め対象の 10 進数。</param>
      <param name="mode">
        <paramref name="d" /> が 2 つの数値の中間にある場合に丸める方法を指定します。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> が有効な <see cref="T:System.MidpointRounding" /> 値ではありません。</exception>
      <exception cref="T:System.OverflowException">結果が <see cref="T:System.Decimal" /> の範囲外です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double)">
      <summary>倍精度浮動小数点値を最も近い整数値に丸めます。</summary>
      <returns>
        <paramref name="a" /> に最も近い整数。<paramref name="a" /> の小数部が 2 つの整数 (一方が偶数で、もう一方が奇数) の中間にある場合は、偶数が返されます。このメソッドは、整数型ではなく <see cref="T:System.Double" /> を返します。</returns>
      <param name="a">丸め対象の倍精度浮動小数点数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32)">
      <summary>倍精度浮動小数点値を指定した小数部の桁数に丸めます。</summary>
      <returns>
        <paramref name="digits" /> に等しい小数部の桁数を格納する <paramref name="value" /> に最も近い数値。</returns>
      <param name="value">丸め対象の倍精度浮動小数点数。</param>
      <param name="digits">戻り値の小数部の桁数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> 0 より小さいか、15 よりも大きいです。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32,System.MidpointRounding)">
      <summary>倍精度浮動小数点値を指定した小数部の桁数に丸めます。値が 2 つの数値の中間にある場合にその値を丸める方法をパラメーターで指定します。</summary>
      <returns>
        <paramref name="digits" /> に等しい小数部の桁数を格納する <paramref name="value" /> に最も近い数値。<paramref name="value" /> の小数部の桁数が <paramref name="digits" /> よりも少ない場合、<paramref name="value" /> がそのまま返されます。</returns>
      <param name="value">丸め対象の倍精度浮動小数点数。</param>
      <param name="digits">戻り値の小数部の桁数。</param>
      <param name="mode">
        <paramref name="value" /> が 2 つの数値の中間にある場合に丸める方法を指定します。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> 0 より小さいか、15 よりも大きいです。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> が有効な <see cref="T:System.MidpointRounding" /> 値ではありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.MidpointRounding)">
      <summary>倍精度浮動小数点値を最も近い整数に丸めます。値が 2 つの数値の中間にある場合にその値を丸める方法をパラメーターで指定します。</summary>
      <returns>
        <paramref name="value" /> に最も近い整数。<paramref name="value" /> が 2 つの整数 (一方が偶数でもう一方が奇数) の中間にある場合、<paramref name="mode" /> によって 2 つの数値のどちらが返されるかが決まります。</returns>
      <param name="value">丸め対象の倍精度浮動小数点数。</param>
      <param name="mode">
        <paramref name="value" /> が 2 つの数値の中間にある場合に丸める方法を指定します。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> が有効な <see cref="T:System.MidpointRounding" /> 値ではありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Decimal)">
      <summary>10 進数の符号を示す値を返します。</summary>
      <returns>
        <paramref name="value" /> の符号を示す数値 (次の表を参照)。戻り値 説明 -1 <paramref name="value" /> が 0 未満です。0 <paramref name="value" /> が 0 です。1 <paramref name="value" /> が 0 より大きい値です。</returns>
      <param name="value">符号付きの 10 進数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Double)">
      <summary>倍精度浮動小数点数の符号を示す値を返します。</summary>
      <returns>
        <paramref name="value" /> の符号を示す数値 (次の表を参照)。戻り値 説明 -1 <paramref name="value" /> が 0 未満です。0 <paramref name="value" /> が 0 です。1 <paramref name="value" /> が 0 より大きい値です。</returns>
      <param name="value">符号付き数値。</param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" /> と <see cref="F:System.Double.NaN" /> が等価です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int16)">
      <summary>16 ビット符号付き整数の符号を示す値を返します。</summary>
      <returns>
        <paramref name="value" /> の符号を示す数値 (次の表を参照)。戻り値 説明 -1 <paramref name="value" /> が 0 未満です。0 <paramref name="value" /> が 0 です。1 <paramref name="value" /> が 0 より大きい値です。</returns>
      <param name="value">符号付き数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int32)">
      <summary>32 ビット符号付き整数の符号を示す値を返します。</summary>
      <returns>
        <paramref name="value" /> の符号を示す数値 (次の表を参照)。戻り値 説明 -1 <paramref name="value" /> が 0 未満です。0 <paramref name="value" /> が 0 です。1 <paramref name="value" /> が 0 より大きい値です。</returns>
      <param name="value">符号付き数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int64)">
      <summary>64 ビット符号付き整数の符号を示す値を返します。</summary>
      <returns>
        <paramref name="value" /> の符号を示す数値 (次の表を参照)。戻り値 説明 -1 <paramref name="value" /> が 0 未満です。0 <paramref name="value" /> が 0 です。1 <paramref name="value" /> が 0 より大きい値です。</returns>
      <param name="value">符号付き数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.SByte)">
      <summary>8 ビット符号付き整数の符号を示す値を返します。</summary>
      <returns>
        <paramref name="value" /> の符号を示す数値 (次の表を参照)。戻り値 説明 -1 <paramref name="value" /> が 0 未満です。0 <paramref name="value" /> が 0 です。1 <paramref name="value" /> が 0 より大きい値です。</returns>
      <param name="value">符号付き数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Single)">
      <summary>単精度浮動小数点数の符号を示す値を返します。</summary>
      <returns>
        <paramref name="value" /> の符号を示す数値 (次の表を参照)。戻り値 説明 -1 <paramref name="value" /> が 0 未満です。0 <paramref name="value" /> が 0 です。1 <paramref name="value" /> が 0 より大きい値です。</returns>
      <param name="value">符号付き数値。</param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" /> と <see cref="F:System.Single.NaN" /> が等価です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sin(System.Double)">
      <summary>指定された角度のサインを返します。</summary>
      <returns>
        <paramref name="a" /> のサイン。<paramref name="a" /> が <see cref="F:System.Double.NaN" />、<see cref="F:System.Double.NegativeInfinity" />、<see cref="F:System.Double.PositiveInfinity" /> のいずれかに等しい場合、このメソッドは <see cref="F:System.Double.NaN" /> を返します。</returns>
      <param name="a">ラジアンで表した角度。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sinh(System.Double)">
      <summary>指定された角度のハイパーボリック サインを返します。</summary>
      <returns>
        <paramref name="value" /> のハイパーボリック サイン。<paramref name="value" /> が <see cref="F:System.Double.NegativeInfinity" />、<see cref="F:System.Double.PositiveInfinity" />、または <see cref="F:System.Double.NaN" /> のいずれかに等しい場合、このメソッドは <paramref name="value" /> に等しい <see cref="T:System.Double" /> を返します。</returns>
      <param name="value">ラジアンで表した角度。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sqrt(System.Double)">
      <summary>指定された数値の平方根を返します。</summary>
      <returns>次の表に示した値のいずれか<paramref name="d" /> パラメーター 戻り値 0 または正 <paramref name="d" /> の正の平方根。負 <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" /> と等しい<see cref="F:System.Double.NaN" /><see cref="F:System.Double.PositiveInfinity" /> と等しい<see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">平方根を求める対象の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tan(System.Double)">
      <summary>指定された角度のタンジェントを返します。</summary>
      <returns>
        <paramref name="a" /> のタンジェント。<paramref name="a" /> が <see cref="F:System.Double.NaN" />、<see cref="F:System.Double.NegativeInfinity" />、<see cref="F:System.Double.PositiveInfinity" /> のいずれかに等しい場合、このメソッドは <see cref="F:System.Double.NaN" /> を返します。</returns>
      <param name="a">ラジアンで表した角度。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tanh(System.Double)">
      <summary>指定された角度のハイパーボリック タンジェントを返します。</summary>
      <returns>
        <paramref name="value" /> のハイパーボリック タンジェント。<paramref name="value" /> が <see cref="F:System.Double.NegativeInfinity" /> に等しい場合、このメソッドは -1 を返します。値が <see cref="F:System.Double.PositiveInfinity" /> に等しい場合、このメソッドは 1 を返します。<paramref name="value" /> が <see cref="F:System.Double.NaN" /> に等しい場合、このメソッドは <see cref="F:System.Double.NaN" /> を返します。</returns>
      <param name="value">ラジアンで表した角度。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Decimal)">
      <summary>指定した 10 進数の整数部を計算します。</summary>
      <returns>
        <paramref name="d" /> の整数部。つまり、小数部の桁を破棄した後に残る数値。</returns>
      <param name="d">切り捨て対象の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Double)">
      <summary>指定した倍精度浮動小数点数の整数部を計算します。</summary>
      <returns>
        <paramref name="d" /> の整数部。つまり、小数部の桁を破棄した後に残る数値 (次の表にリストされている値のいずれか)。<paramref name="d" />戻り値<see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">切り捨て対象の数値。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.MidpointRounding">
      <summary>数値の丸め処理を行うメソッドで、2 つの数値の中間に位置する数値を処理する方法を指定します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.MidpointRounding.AwayFromZero">
      <summary>数値が 2 つの数値の中間に位置するときに、ゼロから遠い方の近似値に丸められます。</summary>
    </member>
    <member name="F:System.MidpointRounding.ToEven">
      <summary>数値が 2 つの数値の中間に位置するときに、最も近い偶数方向に丸められます。</summary>
    </member>
    <member name="T:System.Progress`1">
      <summary>報告済みの進行状況の各値へのコールバックを呼び出す <see cref="T:System.IProgress`1" /> を提供します。</summary>
      <typeparam name="T">進行状況レポート値の型を指定します。</typeparam>
    </member>
    <member name="M:System.Progress`1.#ctor">
      <summary>
        <see cref="T:System.Progress`1" /> オブジェクトを初期化します。</summary>
    </member>
    <member name="M:System.Progress`1.#ctor(System.Action{`0})">
      <summary>指定したコールバックを使用して <see cref="T:System.Progress`1" /> オブジェクトを初期化します。</summary>
      <param name="handler">報告された各進行状況の値に対して起動するハンドラー。このハンドラーは、<see cref="E:System.Progress`1.ProgressChanged" /> イベントに登録されている任意のデリゲートに加えて呼び出されます。構築時に <see cref="T:System.Progress`1" /> によってキャプチャされる <see cref="T:System.Threading.SynchronizationContext" /> インスタンスによっては、このハンドラー インスタンスをそれ自体と同時に呼び出すことができる場合があります。</param>
    </member>
    <member name="M:System.Progress`1.OnReport(`0)">
      <summary>進行状況の変更を報告します。</summary>
      <param name="value">更新の進捗状況の値。</param>
    </member>
    <member name="E:System.Progress`1.ProgressChanged">
      <summary>報告された進行状況を示す各値に対して発生します。</summary>
    </member>
    <member name="M:System.Progress`1.System#IProgress{T}#Report(`0)">
      <summary>進行状況の変更を報告します。</summary>
      <param name="value">更新の進捗状況の値。</param>
    </member>
    <member name="T:System.Random">
      <summary>擬似乱数ジェネレーターを表します。擬似乱数ジェネレーターは、乱数についての統計的な要件を満たす数値系列を生成するデバイスです。この型に対応する .NET Framework のソース コードを参照するには、「Reference Source (ソースの参照)」を参照してください。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.#ctor">
      <summary>時間に依存する既定のシード値を使用し、<see cref="T:System.Random" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Random.#ctor(System.Int32)">
      <summary>指定したシード値を使用して <see cref="T:System.Random" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="Seed">擬似乱数系列の開始値を計算するために使用する数値。負数を指定した場合、その数値の絶対値が使用されます。</param>
    </member>
    <member name="M:System.Random.Next">
      <summary>0 以上のランダムな整数を返します。</summary>
      <returns>0 以上で <see cref="F:System.Int32.MaxValue" /> より小さい 32 ビット符号付き整数。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32)">
      <summary>指定した最大値より小さい 0 以上のランダムな整数を返します。</summary>
      <returns>0 以上で <paramref name="maxValue" /> 未満の 32 ビット符号付き整数。つまり、通常は戻り値の範囲に 0 は含まれますが、<paramref name="maxValue" /> は含まれません。ただし、<paramref name="maxValue" /> が 0 の場合は、<paramref name="maxValue" /> が返されます。</returns>
      <param name="maxValue">生成される乱数の排他的上限値。<paramref name="maxValue" /> は、0 以上である必要があります。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maxValue" /> is less than 0. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32,System.Int32)">
      <summary>指定した範囲内のランダムな整数を返します。</summary>
      <returns>
        <paramref name="minValue" /> 以上で <paramref name="maxValue" /> 未満の 32 ビット符号付き整数。つまり、戻り値の範囲に <paramref name="maxValue" /> は含まれますが <paramref name="minValue" /> は含まれません。<paramref name="minValue" /> が <paramref name="maxValue" /> に等しい場合は、<paramref name="minValue" /> が返されます。</returns>
      <param name="minValue">返される乱数の包括的下限値。</param>
      <param name="maxValue">返される乱数の排他的上限値。<paramref name="maxValue" /> は、<paramref name="minValue" /> 以上である必要があります。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="minValue" /> is greater than <paramref name="maxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextBytes(System.Byte[])">
      <summary>指定したバイト配列の要素に乱数を格納します。</summary>
      <param name="buffer">乱数を格納するバイト配列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextDouble">
      <summary>0.0 以上 1.0 未満のランダムな浮動小数点数を返します。</summary>
      <returns>0.0 以上 1.0 未満の倍精度浮動小数点数。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Sample">
      <summary>0.0 と 1.0 の間のランダムな浮動小数点数を返します。</summary>
      <returns>0.0 以上 1.0 未満の倍精度浮動小数点数。</returns>
    </member>
    <member name="T:System.StringComparer">
      <summary>大文字と小文字の区別、およびカルチャ ベースまたは序数ベースの特定の比較規則を使用する文字列比較操作を表します。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.StringComparer.#ctor">
      <summary>
        <see cref="T:System.StringComparer" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.StringComparer.Compare(System.String,System.String)">
      <summary>派生クラスでオーバーライドされた場合、2 つの文字列を比較し、相対的な並べ替え順序を表す値を返します。</summary>
      <returns>
        <paramref name="x" /> と <paramref name="y" /> の相対値を示す符号付き整数。次の表を参照してください。値説明0 より小さい値並べ替え順序において <paramref name="x" /> は <paramref name="y" /> の前になります。または<paramref name="x" /> が null で、<paramref name="y" /> が null ではありません。0<paramref name="x" /> と <paramref name="y" /> が等価です。または<paramref name="x" /> と <paramref name="y" /> が両方とも null です。0 より大きい値並べ替え順序において <paramref name="x" /> は <paramref name="y" /> の後ろになります。または<paramref name="y" /> が null で、<paramref name="x" /> が null ではありません。</returns>
      <param name="x">
        <paramref name="y" /> と比較する文字列。</param>
      <param name="y">
        <paramref name="x" /> と比較する文字列。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.CurrentCulture">
      <summary>現在のカルチャの単語ベースの比較規則を使用して、大文字と小文字を区別して文字列を比較する <see cref="T:System.StringComparer" /> オブジェクトを取得します。</summary>
      <returns>新しい <see cref="T:System.StringComparer" /> オブジェクト。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.StringComparer.CurrentCultureIgnoreCase">
      <summary>現在のカルチャの単語ベースの比較規則を使用して、大文字と小文字を区別せずに文字列を比較する <see cref="T:System.StringComparer" /> オブジェクトを取得します。</summary>
      <returns>新しい <see cref="T:System.StringComparer" /> オブジェクト。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.StringComparer.Equals(System.String,System.String)">
      <summary>派生クラスでオーバーライドされた場合、2 つの文字列が等しいかどうかを示します。</summary>
      <returns>true と <paramref name="x" /> が同じオブジェクトを参照している場合、または <paramref name="y" /> と <paramref name="x" /> が等しい場合、または <paramref name="y" /> と <paramref name="x" /> が <paramref name="y" /> の場合は null。それ以外の場合は false。</returns>
      <param name="x">
        <paramref name="y" /> と比較する文字列。</param>
      <param name="y">
        <paramref name="x" /> と比較する文字列。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.GetHashCode(System.String)">
      <summary>派生クラスでオーバーライドされた場合、指定した文字列のハッシュ コードを取得します。</summary>
      <returns>
        <paramref name="obj" /> パラメーターの値から計算された 32 ビットの符号付きハッシュ コード。</returns>
      <param name="obj">文字列。</param>
      <exception cref="T:System.ArgumentException">ハッシュ コードの計算に必要なメモリをバッファーに割り当てることができません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="obj" /> は null です。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.StringComparer.Ordinal">
      <summary>大文字と小文字を区別して序数の文字列比較を実行する <see cref="T:System.StringComparer" /> オブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.StringComparer" /> オブジェクト。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.OrdinalIgnoreCase">
      <summary>大文字と小文字を区別せずに序数の文字列比較を実行する <see cref="T:System.StringComparer" /> オブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.StringComparer" /> オブジェクト。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>2 つのオブジェクトを比較し、一方が他方より小さいか、等しいか、大きいかを示す値を返します。</summary>
      <returns>
        <paramref name="x" /> と <paramref name="y" /> の相対値を示す符号付き整数。次の表を参照してください。値説明0 より小さい値<paramref name="x" /> は <paramref name="y" /> より小さい値です。0<paramref name="x" /> と <paramref name="y" /> は等しい。0 より大きい値<paramref name="x" /> が <paramref name="y" /> より大きくなっています。</returns>
      <param name="x">比較する最初のオブジェクト。</param>
      <param name="y">比較する 2 番目のオブジェクト。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> と <paramref name="y" /> が、いずれも <see cref="T:System.IComparable" /> インターフェイスを実装していません。または<paramref name="x" /> と <paramref name="y" /> の型が異なっていて、両者を比較できません。</exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>指定したオブジェクトが等しいかどうかを判断します。</summary>
      <returns>指定したオブジェクトが等しい場合は true。それ以外の場合は false。</returns>
      <param name="x">比較する最初のオブジェクト。</param>
      <param name="y">比較する 2 番目のオブジェクト。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> と <paramref name="y" /> の型が異なっていて、両者を比較できません。</exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>指定したオブジェクトのハッシュ コードを返します。</summary>
      <returns>指定したオブジェクトのハッシュ コード。</returns>
      <param name="obj">ハッシュ コードが返される対象のオブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="obj" /> の型が参照型で、<paramref name="obj" /> が null です。</exception>
    </member>
    <member name="T:System.UriBuilder">
      <summary>URI (Uniform Resource Identifier) のカスタム コンストラクターを提供し、<see cref="T:System.Uri" /> クラスの URI を変更します。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.#ctor">
      <summary>
        <see cref="T:System.UriBuilder" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String)">
      <summary>指定した URI を使用して、<see cref="T:System.UriBuilder" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="uri">URI 文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> は null なので、</exception>
      <exception cref="T:System.UriFormatException">Windows ストア アプリのための .NET または汎用性のあるクラス ライブラリで、基本クラスの例外 <see cref="T:System.FormatException" /> を代わりにキャッチします。<paramref name="uri" /> の長さが 0 か、空白しか含まれていません。または解析ルーチンが無効な形式のスキームを検出しました。またはパーサーが、"file" スキームを使用しない URI で 3 つ以上の連続するスラッシュを検出しました。または<paramref name="uri" /> が有効な URI ではありません。</exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String)">
      <summary>指定したスキームとホストを使用して、<see cref="T:System.UriBuilder" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="schemeName">インターネット アクセス プロトコル。</param>
      <param name="hostName">DNS スタイルのドメイン名、または IP アドレス。</param>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32)">
      <summary>指定したスキーム、ホスト、およびポートを使用して、<see cref="T:System.UriBuilder" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="scheme">インターネット アクセス プロトコル。</param>
      <param name="host">DNS スタイルのドメイン名、または IP アドレス。</param>
      <param name="portNumber">このサービスの IP ポート番号。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="portNumber" /> が -1 より小さい値か、65,535 より大きい値です。</exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String)">
      <summary>指定したスキーム、ホスト、ポート番号、およびパスを使用して、<see cref="T:System.UriBuilder" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="scheme">インターネット アクセス プロトコル。</param>
      <param name="host">DNS スタイルのドメイン名、または IP アドレス。</param>
      <param name="port">このサービスの IP ポート番号。</param>
      <param name="pathValue">インターネット リソースへのパス。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> が -1 より小さい値か、65,535 より大きい値です。</exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String,System.String)">
      <summary>指定したスキーム、ホスト、ポート番号、パス、およびクエリ文字列またはフラグメント識別子を使用して、<see cref="T:System.UriBuilder" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="scheme">インターネット アクセス プロトコル。</param>
      <param name="host">DNS スタイルのドメイン名、または IP アドレス。</param>
      <param name="port">このサービスの IP ポート番号。</param>
      <param name="path">インターネット リソースへのパス。</param>
      <param name="extraValue">クエリ文字列またはフラグメント識別子。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="extraValue" /> が、null、<see cref="F:System.String.Empty" />、シャープ記号 (#) で始まる有効なフラグメント識別子、疑問符 (?) で始まる有効なクエリ文字列のいずれでもありません。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> が -1 より小さい値か、65,535 より大きい値です。</exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.Uri)">
      <summary>指定した <see cref="T:System.Uri" /> インスタンスを使用して、<see cref="T:System.UriBuilder" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="uri">
        <see cref="T:System.Uri" /> クラスのインスタンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> は null なので、</exception>
    </member>
    <member name="M:System.UriBuilder.Equals(System.Object)">
      <summary>既存の <see cref="T:System.Uri" /> インスタンスと、<see cref="T:System.UriBuilder" /> の内容が等しいかどうかを比較します。</summary>
      <returns>
        <paramref name="rparam" /> がこの <see cref="T:System.UriBuilder" /> インスタンスによって構築される <see cref="T:System.Uri" /> と同じ <see cref="T:System.Uri" /> を表す場合は true。それ以外の場合は false。</returns>
      <param name="rparam">現在のインスタンスと比較するオブジェクト。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Fragment">
      <summary>URI のフラグメント部分を取得または設定します。</summary>
      <returns>URI のフラグメント部分。フラグメント識別子 ("#") がフラグメントの先頭に追加されます。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.GetHashCode">
      <summary>URI のハッシュ コードを返します。</summary>
      <returns>URI に対して生成されたハッシュ コード。</returns>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Host">
      <summary>サーバーの DNS (Domain Name System) ホスト名または IP アドレスを取得または設定します。</summary>
      <returns>サーバーの DNS ホスト名または IP アドレス。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Password">
      <summary>URI へアクセスするユーザーに関連付けられているパスワードを取得または設定します。</summary>
      <returns>URI へアクセスするユーザーのパスワード。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Path">
      <summary>URI によって参照されるリソースのパスを取得または設定します。</summary>
      <returns>URI によって参照されるリソースのパス。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Port">
      <summary>URI のポート番号を取得または設定します。</summary>
      <returns>URI のポート番号。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">ポートは、-1 未満または 65,535 より大きい値に設定できません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Query">
      <summary>URI に含まれているクエリ情報を取得または設定します。</summary>
      <returns>URI に含まれているクエリ情報。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Scheme">
      <summary>URI のスキーム名を取得または設定します。</summary>
      <returns>URI のスキーム。</returns>
      <exception cref="T:System.ArgumentException">スキームを無効なスキーム名に設定することはできません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.UriBuilder.ToString">
      <summary>指定した <see cref="T:System.UriBuilder" /> インスタンスの表示文字列を返します。</summary>
      <returns>
        <see cref="T:System.UriBuilder" /> のエスケープ解除した表示文字列が含まれている文字列。</returns>
      <exception cref="T:System.UriFormatException">Windows ストア アプリのための .NET または汎用性のあるクラス ライブラリで、基本クラスの例外 <see cref="T:System.FormatException" /> を代わりにキャッチします。<see cref="T:System.UriBuilder" /> インスタンスのパスワードは不適切です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Uri">
      <summary>指定した <see cref="T:System.UriBuilder" /> により作成された <see cref="T:System.Uri" /> インスタンスを取得します。</summary>
      <returns>
        <see cref="T:System.UriBuilder" /> により作成された URI が含まれる <see cref="T:System.Uri" />。</returns>
      <exception cref="T:System.UriFormatException">Windows ストア アプリのための .NET または汎用性のあるクラス ライブラリで、基本クラスの例外 <see cref="T:System.FormatException" /> を代わりにキャッチします。<see cref="T:System.UriBuilder" /> プロパティにより作成された URI が無効です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.UserName">
      <summary>URI へアクセスするユーザーに関連付けられているユーザー名。</summary>
      <returns>URI へアクセスするユーザーのユーザー名。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Diagnostics.Stopwatch">
      <summary>経過時間を正確に計測するために使用できる一連のメソッドとプロパティを提供します。この型に対応する .NET Framework のソース コードを参照するには、「Reference Source (ソースの参照)」を参照してください。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.#ctor">
      <summary>
        <see cref="T:System.Diagnostics.Stopwatch" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.Elapsed">
      <summary>現在のインスタンスで計測された経過時間の合計を取得します。</summary>
      <returns>現在のインスタンスで計測された経過時間の合計を表す読み取り専用の <see cref="T:System.TimeSpan" />。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedMilliseconds">
      <summary>現在のインスタンスで計測された経過時間の合計を取得します (ミリ秒単位)。</summary>
      <returns>現在のインスタンスで計測されたミリ秒の合計を表す読み取り専用の長整数。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedTicks">
      <summary>現在のインスタンスで計測された経過時間の合計を取得します (タイマー刻み)。</summary>
      <returns>現在のインスタンスで計測されたタイマー刻みの合計を表す読み取り専用の長整数。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.Frequency">
      <summary>1 秒あたりのタイマー刻みの数として、タイマーの頻度を取得します。このフィールドは読み取り専用です。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.GetTimestamp">
      <summary>タイマー機構の現在のタイマー刻み数を取得します。</summary>
      <returns>基になるタイマー機構のタイマー刻みカウンター値を表す長整数。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.IsHighResolution">
      <summary>タイマーが高解像力のパフォーマンス カウンターに基づいているかどうかを示します。このフィールドは読み取り専用です。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.IsRunning">
      <summary>
        <see cref="T:System.Diagnostics.Stopwatch" /> タイマーが実行中かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Diagnostics.Stopwatch" /> インスタンスが現在実行中で、間隔の経過時間を計測中の場合は true。それ以外の場合は false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Reset">
      <summary>タイマー間隔の計測を停止して、経過時間をゼロにリセットします。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Restart">
      <summary>時間間隔の計測を停止し、経過時間をゼロにリセットして、経過時間の計測を開始します。</summary>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Start">
      <summary>間隔の経過時間の計測を開始または再開します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.StartNew">
      <summary>新しい <see cref="T:System.Diagnostics.Stopwatch" /> インスタンスを初期化して、経過時間のプロパティをゼロに設定し、経過時間の計測を開始します。</summary>
      <returns>経過時間の計測を開始した <see cref="T:System.Diagnostics.Stopwatch" />。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Stop">
      <summary>間隔の経過時間の計測を停止します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.Path">
      <summary>ファイルまたはディレクトリのパス情報を格納する <see cref="T:System.String" /> インスタンスで操作を実行します。これらの操作は、プラットフォーム間で実行されます。この種類の .NET Framework ソース コードを参照して、次を参照してください。、 Reference Sourceです。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.AltDirectorySeparatorChar">
      <summary>階層ファイル システム編成を反映するパス文字列の、ディレクトリ レベルを区切るために使用する、プラットフォーム固有の代替文字を提供します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.ChangeExtension(System.String,System.String)">
      <summary>パス文字列の拡張子を変更します。</summary>
      <returns>変更されたパス情報。Windows ベースのデスクトップ プラットフォームでは、<paramref name="path" /> が null または空の文字列 ("") の場合、パス情報は変更されずに返されます。<paramref name="extension" /> が null の場合は、返される文字列に、削除した拡張子が付いた指定したパスが含まれます。<paramref name="path" /> に拡張子がなく、<paramref name="extension" /> が null でない場合は、返されるパス文字列に <paramref name="path" /> の末尾に追加される <paramref name="extension" /> が含まれます。</returns>
      <param name="path">変更するパス情報。パスに、<see cref="M:System.IO.Path.GetInvalidPathChars" /> で定義された文字を含めることはできません。</param>
      <param name="extension">新しい拡張子 (先行ピリオド付き、またはなし)。null を指定して、<paramref name="path" /> から既存の拡張子を削除します。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、<see cref="M:System.IO.Path.GetInvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String)">
      <summary>2 つの文字列を 1 つのパスに結合します。</summary>
      <returns>結合されたパス。指定したパスの 1 つが長さ 0 の文字列の場合、このメソッドは別のパスを返します。<paramref name="path2" /> に絶対パスが含まれる場合、このメソッドは <paramref name="path2" /> を返します。</returns>
      <param name="path1">結合する 1 番目のパス。</param>
      <param name="path2">結合する 2 番目のパス。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path1" /> または <paramref name="path2" /> が、<see cref="M:System.IO.Path.GetInvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path1" /> または <paramref name="path2" /> が null です。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String,System.String)">
      <summary>3 つの文字列を 1 つのパスに結合します。</summary>
      <returns>結合されたパス。</returns>
      <param name="path1">結合する 1 番目のパス。</param>
      <param name="path2">結合する 2 番目のパス。</param>
      <param name="path3">結合する 3 番目のパス。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path1" />、<paramref name="path2" />、または <paramref name="path3" /> が、<see cref="M:System.IO.Path.GetInvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path1" />、<paramref name="path2" />、または <paramref name="path3" /> が null です。</exception>
    </member>
    <member name="M:System.IO.Path.Combine(System.String[])">
      <summary>文字列の配列を 1 つのパスに結合します。</summary>
      <returns>結合されたパス。</returns>
      <param name="paths">パスの構成要素の配列。</param>
      <exception cref="T:System.ArgumentException">配列内の文字列の 1 つが、<see cref="M:System.IO.Path.GetInvalidPathChars" /> で定義された無効な文字を 1 つ以上含んでいます。</exception>
      <exception cref="T:System.ArgumentNullException">配列内の文字列の 1 つが null です。</exception>
    </member>
    <member name="F:System.IO.Path.DirectorySeparatorChar">
      <summary>階層ファイル システム編成を反映するパス文字列の、ディレクトリ レベルを区切るために使用する、プラットフォーム固有の文字を提供します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetDirectoryName(System.String)">
      <summary>指定したパス文字列のディレクトリ情報を返します。</summary>
      <returns>
        <paramref name="path" /> のディレクトリ情報。<paramref name="path" /> がルート ディレクトリを示しているか null である場合は null。<paramref name="path" /> にディレクトリ情報が含まれていない場合は、<see cref="F:System.String.Empty" /> を返します。</returns>
      <param name="path">ファイルまたはディレクトリのパス。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> パラメーターが無効な文字を含んでいるか、空か、または空白だけを含んでいます。</exception>
      <exception cref="T:System.IO.PathTooLongException">.NET for Windows Store apps または ポータブル クラス ライブラリ, 、基本クラスの例外をキャッチ <see cref="T:System.IO.IOException" />, 、代わりにします。<paramref name="path" /> パラメーターがシステム定義の最大長を超えています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetExtension(System.String)">
      <summary>指定したパス文字列の拡張子を返します。</summary>
      <returns>指定したパスの拡張子 (ピリオド "." を含む)、null、または <see cref="F:System.String.Empty" />。<paramref name="path" /> が null の場合、<see cref="M:System.IO.Path.GetExtension(System.String)" /> は null を返します。<paramref name="path" /> が拡張子情報を持たない場合、<see cref="M:System.IO.Path.GetExtension(System.String)" /> は <see cref="F:System.String.Empty" /> を返します。</returns>
      <param name="path">拡張子の取得元のパス文字列。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、<see cref="M:System.IO.Path.GetInvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileName(System.String)">
      <summary>指定したパス文字列のファイル名と拡張子を返します。</summary>
      <returns>
        <paramref name="path" /> の最後のディレクトリ文字の後ろの文字。<paramref name="path" /> の最後の文字がディレクトリ区切り記号またはボリューム区切り記号の場合、このメソッドは <see cref="F:System.String.Empty" /> を返します。<paramref name="path" /> が null の場合、このメソッドは null を返します。</returns>
      <param name="path">ファイル名と拡張子の取得元のパス文字列。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、<see cref="M:System.IO.Path.GetInvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileNameWithoutExtension(System.String)">
      <summary>指定したパス文字列のファイル名を拡張子を付けずに返します。</summary>
      <returns>
        <see cref="M:System.IO.Path.GetFileName(System.String)" /> によって返された文字列 (最後のピリオド (.) と、その後ろのすべての文字を除く)。</returns>
      <param name="path">ファイルのパス。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、<see cref="M:System.IO.Path.GetInvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFullPath(System.String)">
      <summary>指定したパス文字列の絶対パスを返します。</summary>
      <returns>"C:\MyFile.txt" など、<paramref name="path" /> の完全修飾位置。</returns>
      <param name="path">絶対パス情報を取得する対象のファイルまたはディレクトリ。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、長さが 0 の文字列であるか、空白しか含んでいないか、または <see cref="M:System.IO.Path.GetInvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。または システムは、絶対パスを取得できませんでした。</exception>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> は null です。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> に、ボリュームの識別子 ("C:\") の一部ではないコロン (:) が含まれています。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetInvalidFileNameChars">
      <summary>ファイル名に使用できない文字を含む配列を取得します。</summary>
      <returns>ファイル名に使用できない文字を含む配列。</returns>
    </member>
    <member name="M:System.IO.Path.GetInvalidPathChars">
      <summary>パス名に使用できない文字を含む配列を取得します。</summary>
      <returns>パス名に使用できない文字を含む配列。</returns>
    </member>
    <member name="M:System.IO.Path.GetPathRoot(System.String)">
      <summary>指定したパスのルート ディレクトリ情報を取得します。</summary>
      <returns>
        <paramref name="path" /> のルート ディレクトリ ("C:\" など)。<paramref name="path" /> が null の場合は null。<paramref name="path" /> にルート ディレクトリ情報が含まれていない場合は空の文字列。</returns>
      <param name="path">ルート ディレクトリ情報を取得する対象のパス。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、<see cref="M:System.IO.Path.GetInvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。または <see cref="F:System.String.Empty" /> が <paramref name="path" /> に渡されました。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetRandomFileName">
      <summary>ランダムなフォルダー名またはファイル名を返します。</summary>
      <returns>ランダムなフォルダー名またはファイル名。</returns>
    </member>
    <member name="M:System.IO.Path.GetTempFileName">
      <summary>一意な名前を持つ 0 バイトの一時ファイルをディスク上に作成し、そのファイルの完全パスを返します。</summary>
      <returns>一時ファイルの完全パス。</returns>
      <exception cref="T:System.IO.IOException">一意な一時ファイル名が使用できないなどの I/O エラーが発生しました。またはこのメソッドで一時ファイルを作成できませんでした。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetTempPath">
      <summary>現在のユーザーの一時フォルダーのパスを返します。</summary>
      <returns>一時フォルダーのパス。バックスラッシュで終わります。</returns>
      <exception cref="T:System.Security.SecurityException">呼び出し元に、必要なアクセス許可がありません。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.HasExtension(System.String)">
      <summary>パスにファイル名の拡張子を含めるかどうかを決定します。</summary>
      <returns>パスの最後のディレクトリ区切り記号 (\\ または /) またはボリューム区切り記号 (:) の後に続く文字にピリオド (.) が含まれ、その後に 1 つ以上の文字が続く場合は true。それ以外の場合は false。</returns>
      <param name="path">拡張子を検索するパス。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、<see cref="M:System.IO.Path.GetInvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.IsPathRooted(System.String)">
      <summary>指定したパス文字列にルートが含まれているかどうかを示す値を取得します。</summary>
      <returns>
        <paramref name="path" /> にルートが含まれている場合は true。それ以外の場合は false。</returns>
      <param name="path">テストするパス。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> が、<see cref="M:System.IO.Path.GetInvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.PathSeparator">
      <summary>環境変数のパス文字列を区切るために使用するプラットフォーム固有の区切り記号。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.VolumeSeparatorChar">
      <summary>プラットフォーム固有のボリューム区切り記号を提供します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Net.WebUtility">
      <summary>Web 要求の処理時に URL をエンコードおよびデコードするためのメソッドを提供します。</summary>
    </member>
    <member name="M:System.Net.WebUtility.HtmlDecode(System.String)">
      <summary>HTTP 伝送用に HTML エンコードされている文字列を、デコードされた文字列に変換します。</summary>
      <returns>デコードされた文字列。</returns>
      <param name="value">デコードする文字列。</param>
    </member>
    <member name="M:System.Net.WebUtility.HtmlEncode(System.String)">
      <summary>文字列を、HTML エンコードされた文字列に変換します。</summary>
      <returns>エンコードされた文字列。</returns>
      <param name="value">エンコードする文字列。</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecode(System.String)">
      <summary>URL 伝送用にエンコードされた文字列を、デコードされた文字列に変換します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。デコードされた文字列。</returns>
      <param name="encodedValue">デコード対象となる URL エンコードされた文字列。</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>URL 内で伝送する目的でエンコードされたバイト配列を、デコードされたバイト配列に変換します。</summary>
      <returns>
        <see cref="T:System.Byte" /> を返します。デコードされた <see cref="T:System.Byte" /> 配列。</returns>
      <param name="encodedValue">デコード対象となる URL エンコードされた <see cref="T:System.Byte" />。</param>
      <param name="offset">デコードする <see cref="T:System.Byte" /> 配列の先頭からのオフセット (バイト単位)。</param>
      <param name="count">
        <see cref="T:System.Byte" /> の配列からデコードするバイト数です。</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncode(System.String)">
      <summary>テキスト文字列を URL エンコードされた文字列に変換します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。URL エンコードされた文字列。</returns>
      <param name="value">URL エンコードするテキスト。</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>バイト配列を URL エンコードされたバイト配列に変換します。</summary>
      <returns>
        <see cref="T:System.Byte" /> を返します。エンコードされた <see cref="T:System.Byte" /> 配列。</returns>
      <param name="value">URL エンコードする <see cref="T:System.Byte" /> 配列。</param>
      <param name="offset">エンコードする <see cref="T:System.Byte" /> 配列の先頭からのオフセット (バイト単位)。</param>
      <param name="count">
        <see cref="T:System.Byte" /> の配列からエンコードするバイト数です。</param>
    </member>
    <member name="T:System.Runtime.Versioning.FrameworkName">
      <summary>.NET Framework のバージョンの名前を表します。</summary>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String)">
      <summary>.NET Framework のバージョンに関する情報を含む文字列から、<see cref="T:System.Runtime.Versioning.FrameworkName" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="frameworkName">.NET Framework のバージョン情報を含んでいる文字列。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="frameworkName" /> は <see cref="F:System.String.Empty" /> なので、または<paramref name="frameworkName" /> の構成要素が 2 よりも少ないか、または 3 よりも多くなっています。または<paramref name="frameworkName" /> にはメジャー バージョン番号およびマイナー バージョン番号が含まれていません。または<paramref name="frameworkName " /> には有効なバージョン番号が含まれていません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="frameworkName" /> は null なので、</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version)">
      <summary>.NET Framework のバージョンを識別する文字列および <see cref="T:System.Version" /> オブジェクトから、<see cref="T:System.Runtime.Versioning.FrameworkName" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="identifier">.NET Framework のバージョンを識別する文字列。</param>
      <param name="version">.NET Framework のバージョン情報を格納しているオブジェクト。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identifier" /> は <see cref="F:System.String.Empty" /> なので、</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" /> は null なので、または<paramref name="version" /> は null なので、</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version,System.String)">
      <summary>文字列、.NET Framework のバージョンを識別する <see cref="T:System.Version" /> オブジェクト、およびプロファイル名から、<see cref="T:System.Runtime.Versioning.FrameworkName" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="identifier">.NET Framework のバージョンを識別する文字列。</param>
      <param name="version">.NET Framework のバージョン情報を格納しているオブジェクト。</param>
      <param name="profile">プロファイル名。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identifier" /> は <see cref="F:System.String.Empty" /> なので、</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" /> は null なので、または<paramref name="version" /> は null なので、</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Object)">
      <summary>この <see cref="T:System.Runtime.Versioning.FrameworkName" /> インスタンスが、指定されたオブジェクトと同じ .NET Framework バージョンを表すかどうかを示す値を返します。</summary>
      <returns>現在の <see cref="T:System.Runtime.Versioning.FrameworkName" /> オブジェクトのすべての構成要素が、<paramref name="obj" /> の対応する構成要素に一致する場合は true。それ以外の場合は false。</returns>
      <param name="obj">現在のインスタンスと比較する対象のオブジェクト。</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Runtime.Versioning.FrameworkName)">
      <summary>この <see cref="T:System.Runtime.Versioning.FrameworkName" /> インスタンスが、指定された <see cref="T:System.Runtime.Versioning.FrameworkName" /> インスタンスと同じ .NET Framework バージョンを表すかどうかを示す値を返します。</summary>
      <returns>現在の <see cref="T:System.Runtime.Versioning.FrameworkName" /> オブジェクトのすべての構成要素が、<paramref name="other" /> の対応する構成要素に一致する場合は true。それ以外の場合は false。</returns>
      <param name="other">現在のインスタンスと比較する対象のオブジェクト。</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.FullName">
      <summary>この <see cref="T:System.Runtime.Versioning.FrameworkName" /> オブジェクトの完全名を取得します。</summary>
      <returns>この <see cref="T:System.Runtime.Versioning.FrameworkName" /> オブジェクトの完全名。</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.GetHashCode">
      <summary>
        <see cref="T:System.Runtime.Versioning.FrameworkName" /> オブジェクトのハッシュ コードを返します。</summary>
      <returns>このインスタンスのハッシュ コードを表す 32 ビット符号付き整数。</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Identifier">
      <summary>この <see cref="T:System.Runtime.Versioning.FrameworkName" /> オブジェクトの識別子を取得します。</summary>
      <returns>この <see cref="T:System.Runtime.Versioning.FrameworkName" /> オブジェクトの識別子。</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Equality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>2 つの <see cref="T:System.Runtime.Versioning.FrameworkName" /> オブジェクトが同じ .NET Framework バージョンを表すかどうかを示す値を返します。</summary>
      <returns>
        <paramref name="left" /> パラメーターと <paramref name="right" /> パラメーターが同じ .NET Framework バージョンを表している場合は true。それ以外の場合は false。</returns>
      <param name="left">比較対象の第 1 オブジェクト。</param>
      <param name="right">2 番目に比較するオブジェクト。</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Inequality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>2 つの <see cref="T:System.Runtime.Versioning.FrameworkName" /> オブジェクトが異なる .NET Framework バージョンを表すかどうかを示す値を返します。</summary>
      <returns>
        <paramref name="left" /> パラメーターと <paramref name="right" /> パラメーターが異なる .NET Framework バージョンを表している場合は true。それ以外の場合は false。</returns>
      <param name="left">比較対象の第 1 オブジェクト。</param>
      <param name="right">2 番目に比較するオブジェクト。</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Profile">
      <summary>この <see cref="T:System.Runtime.Versioning.FrameworkName" /> オブジェクトのプロファイル名を取得します。</summary>
      <returns>この <see cref="T:System.Runtime.Versioning.FrameworkName" /> オブジェクトのプロファイル名。</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.ToString">
      <summary>この <see cref="T:System.Runtime.Versioning.FrameworkName" /> オブジェクトの文字列形式を返します。</summary>
      <returns>この <see cref="T:System.Runtime.Versioning.FrameworkName" /> オブジェクトを表す文字列。</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Version">
      <summary>この <see cref="T:System.Runtime.Versioning.FrameworkName" /> オブジェクトのバージョンを取得します。</summary>
      <returns>この <see cref="T:System.Runtime.Versioning.FrameworkName" /> オブジェクトに関するバージョン情報を格納しているオブジェクト。</returns>
    </member>
  </members>
</doc>