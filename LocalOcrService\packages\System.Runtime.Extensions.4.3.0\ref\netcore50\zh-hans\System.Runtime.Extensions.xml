﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.Extensions</name>
  </assembly>
  <members>
    <member name="T:System.BitConverter">
      <summary>将基础数据类型与字节数组相互转换。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.BitConverter.DoubleToInt64Bits(System.Double)">
      <summary>将指定的双精度浮点数转换为 64 位有符号整数。</summary>
      <returns>64 位有符号整数，其值等于 <paramref name="value" />。</returns>
      <param name="value">要转换的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Boolean)">
      <summary>以字节数组的形式返回指定的布尔值。</summary>
      <returns>长度为 1 的字节数组。</returns>
      <param name="value">一个布尔值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Char)">
      <summary>以字节数组的形式返回指定的 Unicode 字符值。</summary>
      <returns>长度为 2 的字节数组。</returns>
      <param name="value">要转换的字符。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Double)">
      <summary>以字节数组的形式返回指定的双精度浮点值。</summary>
      <returns>长度为 8 的字节数组。</returns>
      <param name="value">要转换的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int16)">
      <summary>以字节数组的形式返回指定的 16 位有符号整数值。</summary>
      <returns>长度为 2 的字节数组。</returns>
      <param name="value">要转换的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int32)">
      <summary>以字节数组的形式返回指定的 32 位有符号整数值。</summary>
      <returns>长度为 4 的字节数组。</returns>
      <param name="value">要转换的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int64)">
      <summary>以字节数组的形式返回指定的 64 位有符号整数值。</summary>
      <returns>长度为 8 的字节数组。</returns>
      <param name="value">要转换的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Single)">
      <summary>以字节数组的形式返回指定的单精度浮点值。</summary>
      <returns>长度为 4 的字节数组。</returns>
      <param name="value">要转换的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt16)">
      <summary>以字节数组的形式返回指定的 16 位无符号整数值。</summary>
      <returns>长度为 2 的字节数组。</returns>
      <param name="value">要转换的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt32)">
      <summary>以字节数组的形式返回指定的 32 位无符号整数值。</summary>
      <returns>长度为 4 的字节数组。</returns>
      <param name="value">要转换的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt64)">
      <summary>以字节数组的形式返回指定的 64 位无符号整数值。</summary>
      <returns>长度为 8 的字节数组。</returns>
      <param name="value">要转换的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.Int64BitsToDouble(System.Int64)">
      <summary>将指定的 64 位有符号整数转换成双精度浮点数。</summary>
      <returns>双精度浮点数，其值等于 <paramref name="value" />。</returns>
      <param name="value">要转换的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.BitConverter.IsLittleEndian">
      <summary>指示数据在此计算机结构中存储时的字节顺序（“Endian”性质）。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToBoolean(System.Byte[],System.Int32)">
      <summary>返回由字节数组中指定位置的一个字节转换来的布尔值。</summary>
      <returns>如果 <paramref name="value" /> 中的 <paramref name="startIndex" /> 处的字节非零，则为 true；否则为 false。</returns>
      <param name="value">字节数组。</param>
      <param name="startIndex">
        <paramref name="value" /> 内的起始位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小于零或大于 <paramref name="value" /> 减 1 的长度。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToChar(System.Byte[],System.Int32)">
      <summary>返回由字节数组中指定位置的两个字节转换来的 Unicode 字符。</summary>
      <returns>由两个字节构成、从 <paramref name="startIndex" /> 开始的字符。</returns>
      <param name="value">数组。</param>
      <param name="startIndex">
        <paramref name="value" /> 内的起始位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 等于 <paramref name="value" /> 减 1 的长度。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小于零或大于 <paramref name="value" /> 减 1 的长度。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToDouble(System.Byte[],System.Int32)">
      <summary>返回由字节数组中指定位置的八个字节转换来的双精度浮点数。</summary>
      <returns>由八个字节构成、从 <paramref name="startIndex" /> 开始的双精度浮点数。</returns>
      <param name="value">字节数组。</param>
      <param name="startIndex">
        <paramref name="value" /> 内的起始位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 大于等于 <paramref name="value" /> 减 7 的长度，且小于等于 <paramref name="value" /> 减 1 的长度。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小于零或大于 <paramref name="value" /> 减 1 的长度。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt16(System.Byte[],System.Int32)">
      <summary>返回由字节数组中指定位置的两个字节转换来的 16 位有符号整数。</summary>
      <returns>由两个字节构成、从 <paramref name="startIndex" /> 开始的 16 位有符号整数。</returns>
      <param name="value">字节数组。</param>
      <param name="startIndex">
        <paramref name="value" /> 内的起始位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 等于 <paramref name="value" /> 减 1 的长度。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小于零或大于 <paramref name="value" /> 减 1 的长度。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt32(System.Byte[],System.Int32)">
      <summary>返回由字节数组中指定位置的四个字节转换来的 32 位有符号整数。</summary>
      <returns>由四个字节构成、从 <paramref name="startIndex" /> 开始的 32 位有符号整数。</returns>
      <param name="value">字节数组。</param>
      <param name="startIndex">
        <paramref name="value" /> 内的起始位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 大于等于 <paramref name="value" /> 减 3 的长度，且小于等于 <paramref name="value" /> 减 1 的长度。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小于零或大于 <paramref name="value" /> 减 1 的长度。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt64(System.Byte[],System.Int32)">
      <summary>返回由字节数组中指定位置的八个字节转换来的 64 位有符号整数。</summary>
      <returns>由八个字节构成、从 <paramref name="startIndex" /> 开始的 64 位有符号整数。</returns>
      <param name="value">字节数组。</param>
      <param name="startIndex">
        <paramref name="value" /> 内的起始位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 大于等于 <paramref name="value" /> 减 7 的长度，且小于等于 <paramref name="value" /> 减 1 的长度。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小于零或大于 <paramref name="value" /> 减 1 的长度。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToSingle(System.Byte[],System.Int32)">
      <summary>返回由字节数组中指定位置的四个字节转换来的单精度浮点数。</summary>
      <returns>由四个字节构成、从 <paramref name="startIndex" /> 开始的单精度浮点数。</returns>
      <param name="value">字节数组。</param>
      <param name="startIndex">
        <paramref name="value" /> 内的起始位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 大于等于 <paramref name="value" /> 减 3 的长度，且小于等于 <paramref name="value" /> 减 1 的长度。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小于零或大于 <paramref name="value" /> 减 1 的长度。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[])">
      <summary>将指定的字节数组的每个元素的数值转换为它的等效十六进制字符串表示形式。</summary>
      <returns>由以连字符分隔的十六进制对构成的字符串，其中每一对表示 <paramref name="value" /> 中对应的元素；例如“7F-2C-4A”。</returns>
      <param name="value">字节数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 为 null。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32)">
      <summary>将指定的字节子数组的每个元素的数值转换为它的等效十六进制字符串表示形式。</summary>
      <returns>由以连字符分隔的十六进制对构成的字符串，其中每一对表示 <paramref name="value" /> 的子数组中对应的元素；例如“7F-2C-4A”。</returns>
      <param name="value">字节数组。</param>
      <param name="startIndex">
        <paramref name="value" /> 内的起始位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小于零或大于 <paramref name="value" /> 减 1 的长度。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32,System.Int32)">
      <summary>将指定的字节子数组的每个元素的数值转换为它的等效十六进制字符串表示形式。</summary>
      <returns>由以连字符分隔的十六进制对构成的字符串，其中每一对表示 <paramref name="value" /> 的子数组中对应的元素；例如“7F-2C-4A”。</returns>
      <param name="value">字节数组。</param>
      <param name="startIndex">
        <paramref name="value" /> 内的起始位置。</param>
      <param name="length">要转换的 <paramref name="value" /> 中的数组元素数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 或 <paramref name="length" /> 小于零。- 或 -<paramref name="startIndex" /> 大于零且大于等于 <paramref name="value" /> 的长度。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 和 <paramref name="length" /> 的组合不指定 <paramref name="value" /> 中的位置；也就是说，<paramref name="startIndex" /> 参数大于 <paramref name="value" /> 的长度减去 <paramref name="length" /> 参数。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt16(System.Byte[],System.Int32)">
      <summary>返回由字节数组中指定位置的两个字节转换来的 16 位无符号整数。</summary>
      <returns>由两个字节构成、从 <paramref name="startIndex" /> 开始的 16 位无符号整数。</returns>
      <param name="value">字节数组。</param>
      <param name="startIndex">
        <paramref name="value" /> 内的起始位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 等于 <paramref name="value" /> 减 1 的长度。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小于零或大于 <paramref name="value" /> 减 1 的长度。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt32(System.Byte[],System.Int32)">
      <summary>返回由字节数组中指定位置的四个字节转换来的 32 位无符号整数。</summary>
      <returns>由四个字节构成、从 <paramref name="startIndex" /> 开始的 32 位无符号整数。</returns>
      <param name="value">字节数组。</param>
      <param name="startIndex">
        <paramref name="value" /> 内的起始位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 大于等于 <paramref name="value" /> 减 3 的长度，且小于等于 <paramref name="value" /> 减 1 的长度。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小于零或大于 <paramref name="value" /> 减 1 的长度。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt64(System.Byte[],System.Int32)">
      <summary>返回由字节数组中指定位置的八个字节转换来的 64 位无符号整数。</summary>
      <returns>由八个字节构成、从 <paramref name="startIndex" /> 开始的 64 位无符号整数。</returns>
      <param name="value">字节数组。</param>
      <param name="startIndex">
        <paramref name="value" /> 内的起始位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 大于等于 <paramref name="value" /> 减 7 的长度，且小于等于 <paramref name="value" /> 减 1 的长度。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小于零或大于 <paramref name="value" /> 减 1 的长度。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Convert">
      <summary>将一个基本数据类型转换为另一个基本数据类型。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type)">
      <summary>返回一个指定类型的对象，该对象的值等效于指定的对象。</summary>
      <returns>一个对象，其类型为 <paramref name="conversionType" />，并且其值等效于 <paramref name="value" />。- 或 -如果 <paramref name="value" /> 为 null，并且 <paramref name="conversionType" /> 不是值类型，则为空引用（在 Visual Basic 中为 Nothing）。</returns>
      <param name="value">一个实现 <see cref="T:System.IConvertible" /> 接口的对象。</param>
      <param name="conversionType">要返回的对象的类型。</param>
      <exception cref="T:System.InvalidCastException">不支持此转换。- 或 -<paramref name="value" /> 为 null，而且 <paramref name="conversionType" /> 是值类型。- 或 -<paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="conversionType" /> 无法识别<paramref name="value" />的格式。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示超出 <paramref name="conversionType" /> 范围的数字。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="conversionType" /> 为 null。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type,System.IFormatProvider)">
      <summary>返回指定类型的对象，其值等效于指定对象。参数提供区域性特定的格式设置信息。</summary>
      <returns>一个对象，其类型为 <paramref name="conversionType" />，并且其值等效于 <paramref name="value" />。- 或 - 如果 <paramref name="value" /> 的 <see cref="T:System.Type" /> 与 <paramref name="conversionType" /> 相等，则为 <paramref name="value" />。- 或 -如果 <paramref name="value" /> 为 null，并且 <paramref name="conversionType" /> 不是值类型，则为空引用（在 Visual Basic 中为 Nothing）。</returns>
      <param name="value">一个实现 <see cref="T:System.IConvertible" /> 接口的对象。</param>
      <param name="conversionType">要返回的对象的类型。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.InvalidCastException">不支持此转换。- 或 -<paramref name="value" /> 为 null，而且 <paramref name="conversionType" /> 是值类型。- 或 -<paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的格式不是 <paramref name="provider" /> 可以识别的 <paramref name="conversionType" /> 的格式。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示超出 <paramref name="conversionType" /> 范围的数字。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="conversionType" /> 为 null。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.TypeCode,System.IFormatProvider)">
      <summary>返回指定类型的对象，其值等效于指定对象。参数提供区域性特定的格式设置信息。</summary>
      <returns>一个对象，其基础类型为 <paramref name="typeCode" />，并且其值等效于 <paramref name="value" />。- 或 -如果 <paramref name="value" /> 为 null 并且 <paramref name="typeCode" /> 为 <see cref="F:System.TypeCode.Empty" />、<see cref="F:System.TypeCode.String" /> 或 <see cref="F:System.TypeCode.Object" />，则为空引用（在 Visual Basic 中为 Nothing）。</returns>
      <param name="value">一个实现 <see cref="T:System.IConvertible" /> 接口的对象。</param>
      <param name="typeCode">要返回的对象的类型。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.InvalidCastException">不支持此转换。- 或 -<paramref name="value" /> 为 null，而且 <paramref name="typeCode" /> 指定一个值类型。- 或 -<paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的格式不是 <paramref name="provider" /> 可以识别的 <paramref name="typeCode" /> 类型的格式。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示超出 <paramref name="typeCode" /> 类型范围的数字。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="typeCode" /> 无效。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64CharArray(System.Char[],System.Int32,System.Int32)">
      <summary>将 Unicode 字符数组（它将二进制数据编码为 Base64 数字）的子集转换为等效的 8 位无符号整数数组。参数指定输入数组的子集以及要转换的元素数。</summary>
      <returns>等效于 <paramref name="inArray" /> 中位于 <paramref name="offset" /> 位置的 <paramref name="length" /> 元素的 8 位无符号整数数组。</returns>
      <param name="inArray">Unicode 字符数组。</param>
      <param name="offset">
        <paramref name="inArray" /> 内的一个位置。</param>
      <param name="length">要转换的 <paramref name="inArray" /> 中的元素数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> 为 null。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 或 <paramref name="length" /> 小于 0。- 或 - <paramref name="offset" /> 和 <paramref name="length" /> 指示不在 <paramref name="inArray" /> 内的位置。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="inArray" /> 的长度（忽略空白字符）不是 0 或 4 的倍数。- 或 -<paramref name="inArray" /> 的格式无效。<paramref name="inArray" /> 包含一个非 base 64 字符、两个以上的填充字符或者在填充字符中包含非空白字符。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64String(System.String)">
      <summary>将指定的字符串（它将二进制数据编码为 Base64 数字）转换为等效的 8 位无符号整数数组。</summary>
      <returns>与 <paramref name="s" /> 等效的 8 位无符号整数数组。</returns>
      <param name="s">要转换的字符串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> 为 null。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> 的长度（忽略空白字符）不是 0 或 4 的倍数。- 或 -<paramref name="s" /> 的格式无效。<paramref name="s" /> 包含一个非 base 64 字符、两个以上的填充字符或者在填充字符中包含非空白字符。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.GetTypeCode(System.Object)">
      <summary>返回指定对象的 <see cref="T:System.TypeCode" />。</summary>
      <returns>
        <paramref name="value" /> 的 <see cref="T:System.TypeCode" />，或者如果 <paramref name="value" /> 为 null，则为 <see cref="F:System.TypeCode.Empty" />。</returns>
      <param name="value">一个实现 <see cref="T:System.IConvertible" /> 接口的对象。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64CharArray(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32)">
      <summary>将 8 位无符号整数数组的子集转换为用 Base64 数字编码的 Unicode 字符数组的等价子集。参数将子集指定为输入和输出数组中的偏移量和输入数组中要转换的元素数。</summary>
      <returns>包含 <paramref name="outArray" /> 中的字节数的 32 位有符号整数。</returns>
      <param name="inArray">8 位无符号整数的输入数组。</param>
      <param name="offsetIn">
        <paramref name="inArray" /> 内的一个位置。</param>
      <param name="length">要转换的 <paramref name="inArray" /> 的元素数。</param>
      <param name="outArray">Unicode 字符的输出数组。</param>
      <param name="offsetOut">
        <paramref name="outArray" /> 内的一个位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> 或 <paramref name="outArray" /> 为 null。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offsetIn" />、<paramref name="offsetOut" /> 或 <paramref name="length" /> 为负。- 或 - <paramref name="offsetIn" /> 加上 <paramref name="length" /> 大于 <paramref name="inArray" /> 的长度。- 或 - <paramref name="offsetOut" /> 加上要返回的元素数大于 <paramref name="outArray" /> 的长度。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[])">
      <summary>将 8 位无符号整数的数组转换为其用 Base64 数字编码的等效字符串表示形式。</summary>
      <returns>
        <paramref name="inArray" /> 的内容的字符串表示形式，以 Base64 表示。</returns>
      <param name="inArray">一个 8 位无符号整数数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> 为 null。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[],System.Int32,System.Int32)">
      <summary>将 8 位无符号整数数组的子集转换为其用 Base64 数字编码的等效字符串表示形式。参数将子集指定为输入数组中的偏移量和数组中要转换的元素数。</summary>
      <returns>
        <paramref name="inArray" /> 中从位置 <paramref name="offset" /> 开始的 <paramref name="length" /> 个元素的字符串表示形式，以 Base64 表示。</returns>
      <param name="inArray">一个 8 位无符号整数数组。</param>
      <param name="offset">
        <paramref name="inArray" /> 中的偏移量。</param>
      <param name="length">要转换的 <paramref name="inArray" /> 的元素数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> 为 null。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 或 <paramref name="length" /> 为负。- 或 - <paramref name="offset" /> 加上 <paramref name="length" /> 大于 <paramref name="inArray" /> 的长度。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Boolean)">
      <summary>返回指定的布尔值；不执行任何实际的转换。</summary>
      <returns>
        <paramref name="value" /> 不经更改即返回。</returns>
      <param name="value">要返回的布尔值。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Byte)">
      <summary>将指定的 8 位无符号整数的值转换为等效的布尔值。</summary>
      <returns>如果 <paramref name="value" /> 为非零值，则为 true；否则为 false。</returns>
      <param name="value">要转换的 8 位无符号整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Decimal)">
      <summary>将指定的十进制数字的值转换为等效的布尔值。</summary>
      <returns>如果 <paramref name="value" /> 为非零值，则为 true；否则为 false。</returns>
      <param name="value">要转换的数字。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Double)">
      <summary>将指定的双精度浮点数的值转换为等效的布尔值。</summary>
      <returns>如果 <paramref name="value" /> 为非零值，则为 true；否则为 false。</returns>
      <param name="value">要转换的双精度浮点数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int16)">
      <summary>将指定的 16 位有符号整数的值转换为等效的布尔值。</summary>
      <returns>如果 <paramref name="value" /> 为非零值，则为 true；否则为 false。</returns>
      <param name="value">要转换的 16 位带符号整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int32)">
      <summary>将指定的 32 位有符号整数的值转换为等效的布尔值。</summary>
      <returns>如果 <paramref name="value" /> 为非零值，则为 true；否则为 false。</returns>
      <param name="value">要转换的 32 位带符号整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int64)">
      <summary>将指定的 64 位有符号整数的值转换为等效的布尔值。</summary>
      <returns>如果 <paramref name="value" /> 为非零值，则为 true；否则为 false。</returns>
      <param name="value">要转换的 64 位带符号整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object)">
      <summary>将指定对象的值转换为等效的布尔值。</summary>
      <returns>true 或 false，它将反映通过对 <paramref name="value" /> 的基础类型调用 <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" /> 方法而返回的值。如果 <paramref name="value" /> 为 null，则此方法返回 false。</returns>
      <param name="value">用于实现 <see cref="T:System.IConvertible" /> 接口的对象，或为 null。 </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 是一个字符串，它不等于 <see cref="F:System.Boolean.TrueString" /> 或 <see cref="F:System.Boolean.FalseString" />。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持 <paramref name="value" /> 转换为 <see cref="T:System.Boolean" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将指定对象的值转换为等效的布尔值。</summary>
      <returns>true 或 false，它将反映通过对 <paramref name="value" /> 的基础类型调用 <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" /> 方法而返回的值。如果 <paramref name="value" /> 为 null，则此方法返回 false。</returns>
      <param name="value">用于实现 <see cref="T:System.IConvertible" /> 接口的对象，或为 null。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。 </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 是一个字符串，它不等于 <see cref="F:System.Boolean.TrueString" /> 或 <see cref="F:System.Boolean.FalseString" />。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持 <paramref name="value" /> 转换为 <see cref="T:System.Boolean" />。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.SByte)">
      <summary>将指定的 8 位有符号整数的值转换为等效的布尔值。</summary>
      <returns>如果 <paramref name="value" /> 为非零值，则为 true；否则为 false。</returns>
      <param name="value">要转换的 8 位带符号整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Single)">
      <summary>将指定的单精度浮点数的值转换为等效的布尔值。</summary>
      <returns>如果 <paramref name="value" /> 为非零值，则为 true；否则为 false。</returns>
      <param name="value">要转换的单精度浮点数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String)">
      <summary>将逻辑值的指定字符串表示形式转换为其等效的布尔值。</summary>
      <returns>如果 <paramref name="value" /> 等于 <see cref="F:System.Boolean.TrueString" />，则为 true；如果 <paramref name="value" /> 等于 <see cref="F:System.Boolean.FalseString" /> 或 null，则为 false。</returns>
      <param name="value">包含 <see cref="F:System.Boolean.TrueString" /> 或 <see cref="F:System.Boolean.FalseString" /> 值的字符串。 </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不等于 <see cref="F:System.Boolean.TrueString" /> 或 <see cref="F:System.Boolean.FalseString" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将逻辑值的指定字符串表示形式转换为其等效的布尔值。</summary>
      <returns>true if <paramref name="value" /> equals <see cref="F:System.Boolean.TrueString" />, or false if <paramref name="value" /> equals <see cref="F:System.Boolean.FalseString" /> or null.</returns>
      <param name="value">包含 <see cref="F:System.Boolean.TrueString" /> 或 <see cref="F:System.Boolean.FalseString" /> 值的字符串。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。忽略此参数。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不等于 <see cref="F:System.Boolean.TrueString" /> 或 <see cref="F:System.Boolean.FalseString" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt16)">
      <summary>将指定的 16 位无符号整数的值转换为等效的布尔值。</summary>
      <returns>如果 <paramref name="value" /> 为非零值，则为 true；否则为 false。</returns>
      <param name="value">要转换的 16 位无符号整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt32)">
      <summary>将指定的 32 位无符号整数的值转换为等效的布尔值。</summary>
      <returns>如果 <paramref name="value" /> 为非零值，则为 true；否则为 false。</returns>
      <param name="value">要转换的 32 位无符号整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt64)">
      <summary>将指定的 64 位无符号整数的值转换为等效的布尔值。</summary>
      <returns>如果 <paramref name="value" /> 为非零值，则为 true；否则为 false。</returns>
      <param name="value">要转换的 64 位无符号整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Boolean)">
      <summary>将指定的布尔值转换为等效的 8 位无符号整数。</summary>
      <returns>如果 <paramref name="value" /> 为 true，则为数字 1；否则，为 0。</returns>
      <param name="value">要转换的布尔值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Byte)">
      <summary>返回指定的 8 位无符号整数；不执行任何实际的转换。</summary>
      <returns>
        <paramref name="value" /> 不经更改即返回。</returns>
      <param name="value">要返回的 8 位无符号整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Char)">
      <summary>将指定 Unicode 字符的值转换为等效的 8 位无符号整数。</summary>
      <returns>一个等于 <paramref name="value" /> 的 8 位无符号整数。</returns>
      <param name="value">要转换的 Unicode 字符。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示的数字大于 <see cref="F:System.Byte.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Decimal)">
      <summary>将指定的十进制数的值转换为等效的 8 位无符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 8 位无符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的数字。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Byte.MaxValue" /> 或小于 <see cref="F:System.Byte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Double)">
      <summary>将指定的双精度浮点数的值转换为等效的 8 位无符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 8 位无符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的双精度浮点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Byte.MaxValue" /> 或小于 <see cref="F:System.Byte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int16)">
      <summary>将指定的 16 位有符号整数的值转换为等效的 8 位无符号整数。</summary>
      <returns>一个等于 <paramref name="value" /> 的 8 位无符号整数。</returns>
      <param name="value">要转换的 16 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于 <see cref="F:System.Byte.MinValue" /> 或大于 <see cref="F:System.Byte.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int32)">
      <summary>将指定的 32 位有符号整数的值转换为等效的 8 位无符号整数。</summary>
      <returns>一个等于 <paramref name="value" /> 的 8 位无符号整数。</returns>
      <param name="value">要转换的 32 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于 <see cref="F:System.Byte.MinValue" /> 或大于 <see cref="F:System.Byte.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int64)">
      <summary>将指定的 64 位有符号整数的值转换为等效的 8 位无符号整数。</summary>
      <returns>一个等于 <paramref name="value" /> 的 8 位无符号整数。</returns>
      <param name="value">要转换的 64 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于 <see cref="F:System.Byte.MinValue" /> 或大于 <see cref="F:System.Byte.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object)">
      <summary>将指定对象的值转换为 8 位无符号整数。</summary>
      <returns>一个等于 <paramref name="value" /> 的 8 位无符号整数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">用于实现 <see cref="T:System.IConvertible" /> 接口的对象，或为 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 未采用 <see cref="T:System.Byte" /> 值的属性格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" />。- 或 -不支持从 <paramref name="value" /> 到 <see cref="T:System.Byte" /> 类型的转换。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Byte.MinValue" /> 或大于 <see cref="F:System.Byte.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将指定对象的值转换为 8 位无符号整数。</summary>
      <returns>一个等于 <paramref name="value" /> 的 8 位无符号整数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">一个实现 <see cref="T:System.IConvertible" /> 接口的对象。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 未采用 <see cref="T:System.Byte" /> 值的属性格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" />。- 或 -不支持从 <paramref name="value" /> 到 <see cref="T:System.Byte" /> 类型的转换。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Byte.MinValue" /> 或大于 <see cref="F:System.Byte.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.SByte)">
      <summary>将指定的 8 位有符号整数的值转换为等效的 8 位无符号整数。</summary>
      <returns>一个等于 <paramref name="value" /> 的 8 位无符号整数。</returns>
      <param name="value">要转换的 8 位有符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于 <see cref="F:System.Byte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Single)">
      <summary>将指定的单精度浮点数的值转换为等效的 8 位无符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 8 位无符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">一个单精度浮点数字。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Byte.MaxValue" /> 或小于 <see cref="F:System.Byte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String)">
      <summary>将数字的指定字符串表示形式转换为等效的 8 位无符号整数。</summary>
      <returns>一个等于 <paramref name="value" /> 的 8 位无符号整数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />不是由一个可选符号后跟数字序列（0 到 9）组成的。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Byte.MinValue" /> 或大于 <see cref="F:System.Byte.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将数字的指定字符串表示形式转换为等效的 8 位无符号整数。</summary>
      <returns>一个等于 <paramref name="value" /> 的 8 位无符号整数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />不是由一个可选符号后跟数字序列（0 到 9）组成的。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Byte.MinValue" /> 或大于 <see cref="F:System.Byte.MaxValue" /> 的数字。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.Int32)">
      <summary>将指定基数的数字的字符串表示形式转换为等效的 8 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 8 位无符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <param name="fromBase">
        <paramref name="value" /> 中数字的基数，它必须是 2、8、10 或 16。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> 不是 2、8、10 或 16。- 或 -<paramref name="value" />，它表示一个非 10 为基的无符号数，前面带一个负号。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> 为 <see cref="F:System.String.Empty" />。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 包含的一个字符不是 <paramref name="fromBase" /> 指定的基中的有效数字。如果 <paramref name="value" /> 中的第一个字符无效，异常消息则指示没有可转换的数字；否则，该消息将指示 <paramref name="value" /> 包含无效的尾随字符。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />，它表示一个 10 为基的无符号数，前面带一个负号。- 或 -<paramref name="value" /> 表示小于 <see cref="F:System.Byte.MinValue" /> 或大于 <see cref="F:System.Byte.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt16)">
      <summary>Converts the value of the specified 16-bit unsigned integer to an equivalent 8-bit unsigned integer.</summary>
      <returns>一个等于 <paramref name="value" /> 的 8 位无符号整数。</returns>
      <param name="value">要转换的 16 位无符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Byte.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt32)">
      <summary>将指定的 32 位无符号整数的值转换为等效的 8 位无符号整数。</summary>
      <returns>一个等于 <paramref name="value" /> 的 8 位无符号整数。</returns>
      <param name="value">要转换的 32 位无符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Byte.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt64)">
      <summary>将指定的 64 位无符号整数的值转换为等效的 8 位无符号整数。</summary>
      <returns>一个等于 <paramref name="value" /> 的 8 位无符号整数。</returns>
      <param name="value">要转换的 64 位无符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Byte.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Byte)">
      <summary>将指定的 8 位无符号整数的值转换为其等效的 Unicode 字符。</summary>
      <returns>一个等于 <paramref name="value" /> 的 Unicode 字符。</returns>
      <param name="value">要转换的 8 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int16)">
      <summary>将指定的 16 位有符号整数的值转换为它的等效 Unicode 字符。</summary>
      <returns>一个等于 <paramref name="value" /> 的 Unicode 字符。</returns>
      <param name="value">要转换的 16 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于 <see cref="F:System.Char.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int32)">
      <summary>将指定的 32 位有符号整数的值转换为它的等效 Unicode 字符。</summary>
      <returns>一个等于 <paramref name="value" /> 的 Unicode 字符。</returns>
      <param name="value">要转换的 32 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于 <see cref="F:System.Char.MinValue" /> 或大于 <see cref="F:System.Char.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int64)">
      <summary>将指定的 64 位有符号整数的值转换为它的等效 Unicode 字符。</summary>
      <returns>一个等于 <paramref name="value" /> 的 Unicode 字符。</returns>
      <param name="value">要转换的 64 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于 <see cref="F:System.Char.MinValue" /> 或大于 <see cref="F:System.Char.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object)">
      <summary>将指定对象的值转换为 Unicode 字符。</summary>
      <returns>与 value 等效的 Unicode 字符，如果 <paramref name="value" /> 为 null，则为 <see cref="F:System.Char.MinValue" />。</returns>
      <param name="value">一个实现 <see cref="T:System.IConvertible" /> 接口的对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 是空字符串。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持 <paramref name="value" /> 转换为 <see cref="T:System.Char" />。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于 <see cref="F:System.Char.MinValue" /> 或大于 <see cref="F:System.Char.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息将指定对象的值转换为其等效的 Unicode 字符。</summary>
      <returns>与 <paramref name="value" /> 等效的 Unicode 字符，如果 <paramref name="value" /> 为 null，则为 <see cref="F:System.Char.MinValue" />。</returns>
      <param name="value">一个实现 <see cref="T:System.IConvertible" /> 接口的对象。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 是空字符串。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持 <paramref name="value" /> 转换为 <see cref="T:System.Char" />。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于 <see cref="F:System.Char.MinValue" /> 或大于 <see cref="F:System.Char.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.SByte)">
      <summary>将指定的 8 位有符号整数的值转换为它的等效 Unicode 字符。</summary>
      <returns>一个等于 <paramref name="value" /> 的 Unicode 字符。</returns>
      <param name="value">要转换的 8 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于 <see cref="F:System.Char.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String)">
      <summary>将指定字符串的第一个字符转换为 Unicode 字符。</summary>
      <returns>与 <paramref name="value" /> 中第一个且仅有的字符等效的 Unicode 字符。</returns>
      <param name="value">长度为 1 的字符串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 为 null。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的长度不是 1。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将指定字符串的第一个字符转换为 Unicode 字符。</summary>
      <returns>与 <paramref name="value" /> 中第一个且仅有的字符等效的 Unicode 字符。</returns>
      <param name="value">长度为 1 或 null 的字符串。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。忽略此参数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 为 null。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的长度不是 1。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt16)">
      <summary>将指定的 16 位无符号整数的值转换为其等效的 Unicode 字符。</summary>
      <returns>一个等于 <paramref name="value" /> 的 Unicode 字符。</returns>
      <param name="value">要转换的 16 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt32)">
      <summary>将指定的 32 位无符号整数的值转换为其等效的 Unicode 字符。</summary>
      <returns>一个等于 <paramref name="value" /> 的 Unicode 字符。</returns>
      <param name="value">要转换的 32 位无符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Char.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt64)">
      <summary>将指定的 64 位无符号整数的值转换为其等效的 Unicode 字符。</summary>
      <returns>一个等于 <paramref name="value" /> 的 Unicode 字符。</returns>
      <param name="value">要转换的 64 位无符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Char.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object)">
      <summary>将指定对象的值转换为 <see cref="T:System.DateTime" /> 对象。</summary>
      <returns>
        <paramref name="value" /> 的值的日期和时间等效项，如果 <paramref name="value" /> 为 null，则为 <see cref="F:System.DateTime.MinValue" /> 的日期和时间等效项。</returns>
      <param name="value">用于实现 <see cref="T:System.IConvertible" /> 接口的对象，或为 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />不是有效的日期和时间值。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持该转换。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将指定对象的值转换为 <see cref="T:System.DateTime" /> 对象。</summary>
      <returns>
        <paramref name="value" /> 的值的日期和时间等效项，如果 <paramref name="value" /> 为 null，则为 <see cref="F:System.DateTime.MinValue" /> 的日期和时间等效项。</returns>
      <param name="value">一个实现 <see cref="T:System.IConvertible" /> 接口的对象。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />不是有效的日期和时间值。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持该转换。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String)">
      <summary>将日期和时间的指定字符串表示形式转换为等效的日期和时间值。</summary>
      <returns>
        <paramref name="value" /> 的值的日期和时间等效项，如果 <paramref name="value" /> 为 null，则为 <see cref="F:System.DateTime.MinValue" /> 的日期和时间等效项。</returns>
      <param name="value">日期和时间的字符串表示形式。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是格式正确的日期和时间字符串。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将数字的指定字符串表示形式转换为等效的日期和时间。</summary>
      <returns>
        <paramref name="value" /> 的值的日期和时间等效项，如果 <paramref name="value" /> 为 null，则为 <see cref="F:System.DateTime.MinValue" /> 的日期和时间等效项。</returns>
      <param name="value">包含要转换的日期和时间的字符串。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是格式正确的日期和时间字符串。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Boolean)">
      <summary>将指定的布尔值转换为等效的十进制数。</summary>
      <returns>如果 <paramref name="value" /> 为 true，则为数字 1；否则，为 0。</returns>
      <param name="value">要转换的布尔值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Byte)">
      <summary>将指定的 8 位无符号整数的值转换为等效的十进制数。</summary>
      <returns>与 <paramref name="value" /> 等效的十进制数。</returns>
      <param name="value">要转换的 8 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Decimal)">
      <summary>返回指定的十进制数；不执行任何实际的转换。</summary>
      <returns>
        <paramref name="value" /> 不经更改即返回。</returns>
      <param name="value">一个小数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Double)">
      <summary>将指定的双精度浮点数的值转换为等效的十进制数。</summary>
      <returns>一个等于 <paramref name="value" /> 的十进制数。 </returns>
      <param name="value">要转换的双精度浮点数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Decimal.MaxValue" /> 或小于 <see cref="F:System.Decimal.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int16)">
      <summary>将指定的 16 位带符号整数的值转换为等效的十进制数。</summary>
      <returns>一个等于 <paramref name="value" /> 的十进制数。</returns>
      <param name="value">要转换的 16 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int32)">
      <summary>将指定的 32 位带符号整数的值转换为等效的十进制数。</summary>
      <returns>一个等于 <paramref name="value" /> 的十进制数。</returns>
      <param name="value">要转换的 32 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int64)">
      <summary>将指定的 64 位带符号整数的值转换为等效的十进制数。</summary>
      <returns>一个等于 <paramref name="value" /> 的十进制数。</returns>
      <param name="value">要转换的 64 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object)">
      <summary>将指定对象的值转换为等效的十进制数。</summary>
      <returns>与 <paramref name="value" /> 等效的十进制数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">用于实现 <see cref="T:System.IConvertible" /> 接口的对象，或为 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 未采用 <see cref="T:System.Decimal" /> 类型的相应格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持该转换。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Decimal.MinValue" /> 或大于 <see cref="F:System.Decimal.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将指定对象的值转换为等效的十进制数。</summary>
      <returns>与 <paramref name="value" /> 等效的十进制数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">一个实现 <see cref="T:System.IConvertible" /> 接口的对象。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 未采用 <see cref="T:System.Decimal" /> 类型的相应格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持该转换。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Decimal.MinValue" /> 或大于 <see cref="F:System.Decimal.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.SByte)">
      <summary>将指定的 8 位带符号整数的值转换为等效的十进制数。</summary>
      <returns>一个等于 <paramref name="value" /> 的十进制数。</returns>
      <param name="value">要转换的 8 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Single)">
      <summary>将指定的单精度浮点数的值转换为等效的十进制数。</summary>
      <returns>一个等于 <paramref name="value" /> 的十进制数。 </returns>
      <param name="value">要转换的单精度浮点数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Decimal.MaxValue" /> 或小于 <see cref="F:System.Decimal.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String)">
      <summary>将数字的指定字符串表示形式转换为等效的十进制数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的十进制数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是一个有效格式的数字。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Decimal.MinValue" /> 或大于 <see cref="F:System.Decimal.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将数字的指定字符串表示形式转换为等效的十进制数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的十进制数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是一个有效格式的数字。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Decimal.MinValue" /> 或大于 <see cref="F:System.Decimal.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt16)">
      <summary>将指定的 16 位无符号整数的值转换为等效的十进制数。</summary>
      <returns>与 <paramref name="value" /> 等效的十进制数。</returns>
      <param name="value">要转换的 16 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt32)">
      <summary>将指定的 32 位无符号整数的值转换为等效的十进制数。</summary>
      <returns>一个等于 <paramref name="value" /> 的十进制数。</returns>
      <param name="value">要转换的 32 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt64)">
      <summary>将指定的 64 位无符号整数的值转换为等效的十进制数。</summary>
      <returns>一个等于 <paramref name="value" /> 的十进制数。</returns>
      <param name="value">要转换的 64 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Boolean)">
      <summary>将指定的布尔值转换为等效的双精度浮点数。</summary>
      <returns>如果 <paramref name="value" /> 为 true，则为数字 1；否则，为 0。</returns>
      <param name="value">要转换的布尔值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Byte)">
      <summary>将指定的 8 位无符号整数的值转换为等效的双精度浮点数。</summary>
      <returns>与 <paramref name="value" /> 等效的双精度浮点数。</returns>
      <param name="value">要转换的 8 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Decimal)">
      <summary>将指定的十进制数的值转换为等效的双精度浮点数。</summary>
      <returns>一个等于 <paramref name="value" /> 的双精度浮点数。</returns>
      <param name="value">要转换的十进制数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Double)">
      <summary>返回指定的双精度浮点数；不执行任何实际的转换。</summary>
      <returns>
        <paramref name="value" /> 不经更改即返回。</returns>
      <param name="value">要返回的双精度浮点数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int16)">
      <summary>将指定的 16 位带符号整数的值转换为等效的双精度浮点数。</summary>
      <returns>等效于 <paramref name="value" /> 的双精度浮点数。</returns>
      <param name="value">要转换的 16 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int32)">
      <summary>将指定的 32 位带符号整数的值转换为等效的双精度浮点数。</summary>
      <returns>一个等于 <paramref name="value" /> 的双精度浮点数。</returns>
      <param name="value">要转换的 32 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int64)">
      <summary>将指定的 64 位带符号整数的值转换为等效的双精度浮点数。</summary>
      <returns>一个等于 <paramref name="value" /> 的双精度浮点数。</returns>
      <param name="value">要转换的 64 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object)">
      <summary>将指定对象的值转换为双精度浮点数。</summary>
      <returns>与 <paramref name="value" /> 等效的双精度浮点数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">用于实现 <see cref="T:System.IConvertible" /> 接口的对象，或为 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 未采用 <see cref="T:System.Double" /> 类型的相应格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持该转换。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Double.MinValue" /> 或大于 <see cref="F:System.Double.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将指定对象的值转换为双精度浮点数。</summary>
      <returns>与 <paramref name="value" /> 等效的双精度浮点数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">一个实现 <see cref="T:System.IConvertible" /> 接口的对象。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 未采用 <see cref="T:System.Double" /> 类型的相应格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Double.MinValue" /> 或大于 <see cref="F:System.Double.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.SByte)">
      <summary>将指定的 8 位带符号整数的值转换为等效的双精度浮点数。</summary>
      <returns>与 <paramref name="value" /> 等效的 8 位带符号整数。</returns>
      <param name="value">要转换的 8 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Single)">
      <summary>将指定的单精度浮点数的值转换为等效的双精度浮点数。</summary>
      <returns>一个等于 <paramref name="value" /> 的双精度浮点数。</returns>
      <param name="value">单精度浮点数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String)">
      <summary>将数字的指定字符串表示形式转换为等效的双精度浮点数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的双精度浮点数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是一个有效格式的数字。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Double.MinValue" /> 或大于 <see cref="F:System.Double.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将数字的指定字符串表示形式转换为等效的双精度浮点数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的双精度浮点数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是一个有效格式的数字。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Double.MinValue" /> 或大于 <see cref="F:System.Double.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt16)">
      <summary>将指定的 16 位无符号整数的值转换为等效的双精度浮点数。</summary>
      <returns>一个等于 <paramref name="value" /> 的双精度浮点数。</returns>
      <param name="value">要转换的 16 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt32)">
      <summary>将指定的 32 位无符号整数的值转换为等效的双精度浮点数。</summary>
      <returns>一个等于 <paramref name="value" /> 的双精度浮点数。</returns>
      <param name="value">要转换的 32 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt64)">
      <summary>将指定的 64 位无符号整数的值转换为等效的双精度浮点数。</summary>
      <returns>一个等于 <paramref name="value" /> 的双精度浮点数。</returns>
      <param name="value">要转换的 64 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Boolean)">
      <summary>将指定的布尔值转换为等效的 16 位带符号整数。</summary>
      <returns>如果 <paramref name="value" /> 为 true，则为数字 1；否则，为 0。</returns>
      <param name="value">要转换的布尔值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to the equivalent 16-bit signed integer.</summary>
      <returns>与 <paramref name="value" /> 等效的 16 位带符号整数。</returns>
      <param name="value">要转换的 8 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Char)">
      <summary>将指定的 Unicode 字符的值转换为等效的 16 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 16 位带符号整数。 </returns>
      <param name="value">要转换的 Unicode 字符。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Int16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Decimal)">
      <summary>将指定的十进制数的值转换为等效的 16 位带符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 16 位带符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的十进制数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Int16.MaxValue" /> 或小于 <see cref="F:System.Int16.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Double)">
      <summary>将指定的双精度浮点数的值转换为等效的 16 位带符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 16 位带符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的双精度浮点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Int16.MaxValue" /> 或小于 <see cref="F:System.Int16.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int16)">
      <summary>返回指定的 16 位有符号整数；不执行实际的转换。</summary>
      <returns>
        <paramref name="value" /> 不经更改即返回。</returns>
      <param name="value">要返回的 16 位带符号整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int32)">
      <summary>将指定的 32 位有符号整数的值转换为等效的 16 位有符号整数。</summary>
      <returns>等效于 <paramref name="value" /> 的 16 位有符号整数。</returns>
      <param name="value">要转换的 32 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Int16.MaxValue" /> 或小于 <see cref="F:System.Int16.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int64)">
      <summary>将指定的 64 位有符号整数的值转换为等效的 16 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 16 位带符号整数。</returns>
      <param name="value">要转换的 64 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Int16.MaxValue" /> 或小于 <see cref="F:System.Int16.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object)">
      <summary>将指定对象的值转换为 16 位带符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 16 位带符号整数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">用于实现 <see cref="T:System.IConvertible" /> 接口的对象，或为 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 未采用 <see cref="T:System.Int16" /> 类型的相应格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持该转换。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Int16.MinValue" /> 或大于 <see cref="F:System.Int16.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式信息，将指定对象的值转换为 16 位带符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 16 位带符号整数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">一个实现 <see cref="T:System.IConvertible" /> 接口的对象。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 未采用 <see cref="T:System.Int16" /> 类型的相应格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" />。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Int16.MinValue" /> 或大于 <see cref="F:System.Int16.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.SByte)">
      <summary>将指定的 8 位带符号整数的值转换为等效的 16 位带符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 8 位带符号整数。</returns>
      <param name="value">要转换的 8 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Single)">
      <summary>将指定的单精度浮点数的值转换为等效的 16 位带符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 16 位带符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的单精度浮点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Int16.MaxValue" /> 或小于 <see cref="F:System.Int16.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String)">
      <summary>将数字的指定字符串表示形式转换为等效的 16 位带符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 16 位带符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />不是由一个可选符号后跟数字序列（0 到 9）组成的。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Int16.MinValue" /> 或大于 <see cref="F:System.Int16.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将数字的指定字符串表示形式转换为等效的 16 位带符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 16 位带符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />不是由一个可选符号后跟数字序列（0 到 9）组成的。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Int16.MinValue" /> 或大于 <see cref="F:System.Int16.MaxValue" /> 的数字。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.Int32)">
      <summary>将指定基数的数字的字符串表示形式转换为等效的 16 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 16 位带符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <param name="fromBase">
        <paramref name="value" /> 中数字的基数，它必须是 2、8、10 或 16。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> 不是 2、8、10 或 16。- 或 -<paramref name="value" />，它表示一个非 10 为基的有符号数，前面带一个负号。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> 为 <see cref="F:System.String.Empty" />。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 包含的一个字符不是 <paramref name="fromBase" /> 指定的基中的有效数字。如果 <paramref name="value" /> 中的第一个字符无效，异常消息则指示没有可转换的数字；否则，该消息将指示 <paramref name="value" /> 包含无效的尾随字符。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />，它表示一个非 10 为基的有符号数，前面带一个负号。- 或 -<paramref name="value" /> 表示小于 <see cref="F:System.Int16.MinValue" /> 或大于 <see cref="F:System.Int16.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt16)">
      <summary>将指定的 16 位无符号整数的值转换为等效的 16 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 16 位带符号整数。</returns>
      <param name="value">要转换的 16 位无符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Int16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt32)">
      <summary>将指定的 32 位无符号整数的值转换为等效的 16 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 16 位带符号整数。</returns>
      <param name="value">要转换的 32 位无符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Int16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt64)">
      <summary>将指定的 64 位无符号整数的值转换为等效的 16 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 16 位带符号整数。</returns>
      <param name="value">要转换的 64 位无符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Int16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Boolean)">
      <summary>将指定的布尔值转换为等效的 32 位带符号整数。</summary>
      <returns>如果 <paramref name="value" /> 为 true，则为数字 1；否则，为 0。</returns>
      <param name="value">要转换的布尔值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Byte)">
      <summary>将指定的 8 位无符号整数的值转换为等效的 32 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 32 位带符号整数。</returns>
      <param name="value">要转换的 8 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Char)">
      <summary>将指定的 Unicode 字符的值转换为等效的 32 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 32 位带符号整数。</returns>
      <param name="value">要转换的 Unicode 字符。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Decimal)">
      <summary>将指定的十进制数的值转换为等效的 32 位带符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 32 位带符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的十进制数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Int32.MaxValue" /> 或小于 <see cref="F:System.Int32.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Double)">
      <summary>将指定的双精度浮点数的值转换为等效的 32 位带符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 32 位带符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的双精度浮点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Int32.MaxValue" /> 或小于 <see cref="F:System.Int32.MinValue" />。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to an equivalent 32-bit signed integer.</summary>
      <returns>与 <paramref name="value" /> 等效的 32 位带符号整数。</returns>
      <param name="value">要转换的 16 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int32)">
      <summary>返回指定的 32 位有符号整数；不执行实际的转换。</summary>
      <returns>
        <paramref name="value" /> 不经更改即返回。</returns>
      <param name="value">要返回的 32 位带符号整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to an equivalent 32-bit signed integer.</summary>
      <returns>与 <paramref name="value" /> 等效的 32 位带符号整数。</returns>
      <param name="value">要转换的 64 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Int32.MaxValue" /> 或小于 <see cref="F:System.Int32.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object)">
      <summary>将指定对象的值转换为 32 位带符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 32 位带符号整数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">用于实现 <see cref="T:System.IConvertible" /> 接口的对象，或为 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的格式不正确。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持该转换。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Int32.MinValue" /> 或大于 <see cref="F:System.Int32.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式信息，将指定对象的值转换为 32 位带符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 32 位带符号整数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">一个实现 <see cref="T:System.IConvertible" /> 接口的对象。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的格式不正确。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" />。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Int32.MinValue" /> 或大于 <see cref="F:System.Int32.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.SByte)">
      <summary>将指定的 8 位带符号整数的值转换为等效的 32 位带符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 8 位带符号整数。</returns>
      <param name="value">要转换的 8 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Single)">
      <summary>将指定的单精度浮点数的值转换为等效的 32 位带符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 32 位带符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的单精度浮点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Int32.MaxValue" /> 或小于 <see cref="F:System.Int32.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String)">
      <summary>将数字的指定字符串表示形式转换为等效的 32 位带符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 32 位带符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />不是由一个可选符号后跟数字序列（0 到 9）组成的。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Int32.MinValue" /> 或大于 <see cref="F:System.Int32.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将数字的指定字符串表示形式转换为等效的 32 位带符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 32 位带符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />不是由一个可选符号后跟数字序列（0 到 9）组成的。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Int32.MinValue" /> 或大于 <see cref="F:System.Int32.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.Int32)">
      <summary>将指定基数的数字的字符串表示形式转换为等效的 32 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 32 位带符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <param name="fromBase">
        <paramref name="value" /> 中数字的基数，它必须是 2、8、10 或 16。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> 不是 2、8、10 或 16。- 或 -<paramref name="value" />，它表示一个非 10 为基的有符号数，前面带一个负号。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> 为 <see cref="F:System.String.Empty" />。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 包含的一个字符不是 <paramref name="fromBase" /> 指定的基中的有效数字。如果 <paramref name="value" /> 中的第一个字符无效，异常消息则指示没有可转换的数字；否则，该消息将指示 <paramref name="value" /> 包含无效的尾随字符。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />，它表示一个非 10 为基的有符号数，前面带一个负号。- 或 -<paramref name="value" /> 表示小于 <see cref="F:System.Int32.MinValue" /> 或大于 <see cref="F:System.Int32.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt16)">
      <summary>将指定的 16 位无符号整数的值转换为等效的 32 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 32 位带符号整数。</returns>
      <param name="value">要转换的 16 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt32)">
      <summary>将指定的 32 位无符号整数的值转换为等效的 32 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 32 位带符号整数。</returns>
      <param name="value">要转换的 32 位无符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Int32.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt64)">
      <summary>Converts the value of the specified 64-bit unsigned integer to an equivalent 32-bit signed integer.</summary>
      <returns>与 <paramref name="value" /> 等效的 32 位带符号整数。</returns>
      <param name="value">要转换的 64 位无符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Int32.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Boolean)">
      <summary>将指定的布尔值转换为等效的 64 位带符号整数。</summary>
      <returns>如果 <paramref name="value" /> 为 true，则为数字 1；否则，为 0。</returns>
      <param name="value">要转换的布尔值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Byte)">
      <summary>将指定的 8 位无符号整数的值转换为等效的 64 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位带符号整数。</returns>
      <param name="value">要转换的 8 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Char)">
      <summary>将指定的 Unicode 字符的值转换为等效的 64 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位带符号整数。</returns>
      <param name="value">要转换的 Unicode 字符。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Decimal)">
      <summary>将指定的十进制数的值转换为等效的 64 位带符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 64 位带符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的十进制数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Int64.MaxValue" /> 或小于 <see cref="F:System.Int64.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Double)">
      <summary>将指定的双精度浮点数的值转换为等效的 64 位带符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 64 位带符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的双精度浮点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Int64.MaxValue" /> 或小于 <see cref="F:System.Int64.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int16)">
      <summary>将指定的 16 位有符号整数的值转换为等效的 64 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位带符号整数。</returns>
      <param name="value">要转换的 16 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int32)">
      <summary>将指定的 32 位有符号整数的值转换为等效的 64 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位带符号整数。</returns>
      <param name="value">要转换的 32 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int64)">
      <summary>返回指定的 64 位有符号整数；不执行实际的转换。</summary>
      <returns>
        <paramref name="value" /> 不经更改即返回。</returns>
      <param name="value">64 位带符号整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object)">
      <summary>将指定对象的值转换为 64 位带符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位带符号整数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">用于实现 <see cref="T:System.IConvertible" /> 接口的对象，或为 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的格式不正确。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持该转换。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Int64.MinValue" /> 或大于 <see cref="F:System.Int64.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式信息，将指定对象的值转换为 64 位带符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位带符号整数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">一个实现 <see cref="T:System.IConvertible" /> 接口的对象。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的格式不正确。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持该转换。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Int64.MinValue" /> 或大于 <see cref="F:System.Int64.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.SByte)">
      <summary>将指定的 8 位带符号整数的值转换为等效的 64 位带符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位带符号整数。</returns>
      <param name="value">要转换的 8 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Single)">
      <summary>将指定的单精度浮点数的值转换为等效的 64 位带符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 64 位带符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的单精度浮点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Int64.MaxValue" /> 或小于 <see cref="F:System.Int64.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String)">
      <summary>将数字的指定字符串表示形式转换为等效的 64 位带符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 64 位带符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />不是由一个可选符号后跟数字序列（0 到 9）组成的。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Int64.MinValue" /> 或大于 <see cref="F:System.Int64.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将数字的指定字符串表示形式转换为等效的 64 位带符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 64 位带符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />不是由一个可选符号后跟数字序列（0 到 9）组成的。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Int64.MinValue" /> 或大于 <see cref="F:System.Int64.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.Int32)">
      <summary>将指定基数的数字的字符串表示形式转换为等效的 64 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 64 位带符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <param name="fromBase">
        <paramref name="value" /> 中数字的基数，它必须是 2、8、10 或 16。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> 不是 2、8、10 或 16。- 或 -<paramref name="value" />，它表示一个非 10 为基的有符号数，前面带一个负号。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> 为 <see cref="F:System.String.Empty" />。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 包含的一个字符不是 <paramref name="fromBase" /> 指定的基中的有效数字。如果 <paramref name="value" /> 中的第一个字符无效，异常消息则指示没有可转换的数字；否则，该消息将指示 <paramref name="value" /> 包含无效的尾随字符。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />，它表示一个非 10 为基的有符号数，前面带一个负号。- 或 -<paramref name="value" /> 表示小于 <see cref="F:System.Int64.MinValue" /> 或大于 <see cref="F:System.Int64.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt16)">
      <summary>将指定的 16 位无符号整数的值转换为等效的 64 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位带符号整数。</returns>
      <param name="value">要转换的 16 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt32)">
      <summary>将指定的 32 位无符号整数的值转换为等效的 64 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位带符号整数。</returns>
      <param name="value">要转换的 32 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt64)">
      <summary>将指定的 64 位无符号整数的值转换为等效的 64 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位带符号整数。</returns>
      <param name="value">要转换的 64 位无符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.Int64.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Boolean)">
      <summary>将指定的布尔值转换为等效的 8 位带符号整数。</summary>
      <returns>如果 <paramref name="value" /> 为 true，则为数字 1；否则，为 0。</returns>
      <param name="value">要转换的布尔值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Byte)">
      <summary>将指定的 8 位无符号整数的值转换为等效的 8 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 8 位带符号整数。</returns>
      <param name="value">要转换的 8 位无符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.SByte.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Char)">
      <summary>将指定的 Unicode 字符的值转换为等效的 8 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 8 位带符号整数。</returns>
      <param name="value">要转换的 Unicode 字符。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.SByte.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Decimal)">
      <summary>将指定的十进制数的值转换为等效的 8 位带符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 8 位带符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的十进制数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.SByte.MaxValue" /> 或小于 <see cref="F:System.SByte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Double)">
      <summary>将指定的双精度浮点数的值转换为等效的 8 位带符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 8 位带符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的双精度浮点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.SByte.MaxValue" /> 或小于 <see cref="F:System.SByte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to the equivalent 8-bit signed integer.</summary>
      <returns>与 <paramref name="value" /> 等效的 8 位带符号整数。</returns>
      <param name="value">要转换的 16 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.SByte.MaxValue" /> 或小于 <see cref="F:System.SByte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int32)">
      <summary>将指定的 32 位有符号整数的值转换为等效的 8 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 8 位带符号整数。</returns>
      <param name="value">要转换的 32 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.SByte.MaxValue" /> 或小于 <see cref="F:System.SByte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int64)">
      <summary>将指定的 64 位有符号整数的值转换为等效的 8 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 8 位带符号整数。</returns>
      <param name="value">要转换的 64 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.SByte.MaxValue" /> 或小于 <see cref="F:System.SByte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object)">
      <summary>将指定对象的值转换为 8 位带符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 8 位带符号整数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">用于实现 <see cref="T:System.IConvertible" /> 接口的对象，或为 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的格式不正确。 </exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持该转换。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.SByte.MinValue" /> 或大于 <see cref="F:System.SByte.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式信息，将指定对象的值转换为 8 位带符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 8 位带符号整数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">一个实现 <see cref="T:System.IConvertible" /> 接口的对象。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的格式不正确。 </exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持该转换。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.SByte.MinValue" /> 或大于 <see cref="F:System.SByte.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.SByte)">
      <summary>返回指定的 8 位有符号整数；不执行实际的转换。</summary>
      <returns>
        <paramref name="value" /> 不经更改即返回。</returns>
      <param name="value">要返回的 8 位带符号整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Single)">
      <summary>将指定的单精度浮点数的值转换为等效的 8 位带符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 8 位带符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的单精度浮点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.SByte.MaxValue" /> 或小于 <see cref="F:System.SByte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String)">
      <summary>将数字的指定字符串表示形式转换为等效的 8 位带符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 8 位带符号整数，如果 value 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />不是由一个可选符号后跟数字序列（0 到 9）组成的。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.SByte.MinValue" /> 或大于 <see cref="F:System.SByte.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将数字的指定字符串表示形式转换为等效的 8 位带符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 8 位带符号整数。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 为 null。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" />不是由一个可选符号后跟数字序列（0 到 9）组成的。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.SByte.MinValue" /> 或大于 <see cref="F:System.SByte.MaxValue" /> 的数字。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.Int32)">
      <summary>将指定基数的数字的字符串表示形式转换为等效的 8 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 8 位带符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <param name="fromBase">
        <paramref name="value" /> 中数字的基数，它必须是 2、8、10 或 16。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> 不是 2、8、10 或 16。- 或 -<paramref name="value" />，它表示一个非 10 为基的有符号数，前面带一个负号。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> 为 <see cref="F:System.String.Empty" />。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 包含的一个字符不是 <paramref name="fromBase" /> 指定的基中的有效数字。如果 <paramref name="value" /> 中的第一个字符无效，异常消息则指示没有可转换的数字；否则，该消息将指示 <paramref name="value" /> 包含无效的尾随字符。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />，它表示一个非 10 为基的有符号数，前面带一个负号。- 或 -<paramref name="value" /> 表示小于 <see cref="F:System.SByte.MinValue" /> 或大于 <see cref="F:System.SByte.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt16)">
      <summary>将指定的 16 位无符号整数的值转换为等效的 8 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 8 位带符号整数。</returns>
      <param name="value">要转换的 16 位无符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.SByte.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt32)">
      <summary>将指定的 32 位无符号整数的值转换为等效的 8 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 8 位带符号整数。</returns>
      <param name="value">要转换的 32 位无符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.SByte.MaxValue" /> 或小于 <see cref="F:System.SByte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt64)">
      <summary>将指定的 64 位无符号整数的值转换为等效的 8 位有符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 8 位带符号整数。</returns>
      <param name="value">要转换的 64 位无符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.SByte.MaxValue" /> 或小于 <see cref="F:System.SByte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Boolean)">
      <summary>将指定的布尔值转换为等效的单精度浮点数。</summary>
      <returns>如果 <paramref name="value" /> 为 true，则为数字 1；否则，为 0。</returns>
      <param name="value">要转换的布尔值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Byte)">
      <summary>将指定的 8 位无符号整数的值转换为等效的单精度浮点数。</summary>
      <returns>一个等于 <paramref name="value" /> 的单精度浮点数。</returns>
      <param name="value">要转换的 8 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Decimal)">
      <summary>将指定的十进制数的值转换为等效的单精度浮点数。</summary>
      <returns>一个等于 <paramref name="value" /> 的单精度浮点数。<paramref name="value" /> 使用"舍入到最接近的数字"规则进行舍入。例如，当舍入为两位小数时，值 2.345 变成 2.34，而值 2.355 变成 2.36。</returns>
      <param name="value">要转换的十进制数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Double)">
      <summary>将指定的双精度浮点数的值转换为等效的单精度浮点数。</summary>
      <returns>一个等于 <paramref name="value" /> 的单精度浮点数。<paramref name="value" /> 使用"舍入到最接近的数字"规则进行舍入。例如，当舍入为两位小数时，值 2.345 变成 2.34，而值 2.355 变成 2.36。</returns>
      <param name="value">要转换的双精度浮点数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int16)">
      <summary>将指定的 16 位带符号整数的值转换为等效的单精度浮点数。</summary>
      <returns>一个等于 <paramref name="value" /> 的单精度浮点数。</returns>
      <param name="value">要转换的 16 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int32)">
      <summary>将指定的 32 位带符号整数的值转换为等效的单精度浮点数。</summary>
      <returns>一个等于 <paramref name="value" /> 的单精度浮点数。</returns>
      <param name="value">要转换的 32 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int64)">
      <summary>将指定的 64 位带符号整数的值转换为等效的单精度浮点数。</summary>
      <returns>一个等于 <paramref name="value" /> 的单精度浮点数。</returns>
      <param name="value">要转换的 64 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object)">
      <summary>将指定对象的值转换为单精度浮点数。</summary>
      <returns>与 <paramref name="value" /> 等效的单精度浮点数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">用于实现 <see cref="T:System.IConvertible" /> 接口的对象，或为 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的格式不正确。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持该转换。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Single.MinValue" /> 或大于 <see cref="F:System.Single.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将指定对象的值转换为单精度浮点数。</summary>
      <returns>与 <paramref name="value" /> 等效的单精度浮点数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">一个实现 <see cref="T:System.IConvertible" /> 接口的对象。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的格式不正确。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" />。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Single.MinValue" /> 或大于 <see cref="F:System.Single.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.SByte)">
      <summary>将指定的 8 位带符号整数的值转换为等效的单精度浮点数。</summary>
      <returns>与 <paramref name="value" /> 等效的 8 位带符号整数。</returns>
      <param name="value">要转换的 8 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Single)">
      <summary>返回指定的单精度浮点数；不执行任何实际的转换。</summary>
      <returns>
        <paramref name="value" /> 不经更改即返回。</returns>
      <param name="value">要返回的单精度浮点数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String)">
      <summary>将数字的指定字符串表示形式转换为等效的单精度浮点数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的单精度浮点数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是一个有效格式的数字。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Single.MinValue" /> 或大于 <see cref="F:System.Single.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将数字的指定字符串表示形式转换为等效的单精度浮点数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的单精度浮点数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是一个有效格式的数字。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.Single.MinValue" /> 或大于 <see cref="F:System.Single.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt16)">
      <summary>将指定的 16 位无符号整数的值转换为等效的单精度浮点数。</summary>
      <returns>一个等于 <paramref name="value" /> 的单精度浮点数。</returns>
      <param name="value">要转换的 16 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt32)">
      <summary>将指定的 32 位无符号整数的值转换为等效的单精度浮点数。</summary>
      <returns>一个等于 <paramref name="value" /> 的单精度浮点数。</returns>
      <param name="value">要转换的 32 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt64)">
      <summary>将指定的 64 位无符号整数的值转换为等效的单精度浮点数。</summary>
      <returns>一个等于 <paramref name="value" /> 的单精度浮点数。</returns>
      <param name="value">要转换的 64 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean)">
      <summary>将指定的布尔值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的布尔值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean,System.IFormatProvider)">
      <summary>将指定的布尔值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的布尔值。</param>
      <param name="provider">一个对象的实例。忽略此参数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte)">
      <summary>将指定的 8 位无符号整数的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的 8 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将指定的 8 位无符号整数的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的 8 位无符号整数。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.Int32)">
      <summary>将 8 位无符号整数的值转换为其等效的指定基数的字符串表示形式。</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">要转换的 8 位无符号整数。</param>
      <param name="toBase">返回值的基数，必须是 2、8、10 或 16。 </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> 不是 2、8、10 或 16。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char)">
      <summary>将指定的 Unicode 字符的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的 Unicode 字符。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息将指定的 Unicode 字符的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的 Unicode 字符。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。忽略此参数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime)">
      <summary>将指定的 <see cref="T:System.DateTime" /> 的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的日期和时间值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将指定 <see cref="T:System.DateTime" /> 的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的日期和时间值。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal)">
      <summary>将指定的十进制数的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的十进制数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息将指定的十进制数的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的十进制数。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double)">
      <summary>将指定的双精度浮点数的值转换其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的双精度浮点数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double,System.IFormatProvider)">
      <summary>将指定的双精度浮点数的值转换其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的双精度浮点数。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16)">
      <summary>将指定的 16 位带符号整数的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的 16 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将指定的 16 位带符号整数的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的 16 位带符号整数。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.Int32)">
      <summary>将 16 位带符号整数的值转换为其指定基的等效字符串表示形式。</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">要转换的 16 位带符号整数。</param>
      <param name="toBase">返回值的基数，必须是 2、8、10 或 16。 </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> 不是 2、8、10 或 16。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32)">
      <summary>将指定的 32 位带符号整数的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的 32 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将指定的 32 位带符号整数的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的 32 位带符号整数。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.Int32)">
      <summary>将 32 位带符号整数的值转换为其指定基的等效字符串表示形式。</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">要转换的 32 位带符号整数。</param>
      <param name="toBase">返回值的基数，必须是 2、8、10 或 16。 </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> 不是 2、8、10 或 16。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64)">
      <summary>将指定的 64 位带符号整数的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的 64 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将指定的 64 位带符号整数的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的 64 位带符号整数。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.Int32)">
      <summary>将 64 位带符号整数的值转换为其指定基的等效字符串表示形式。</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">要转换的 64 位带符号整数。</param>
      <param name="toBase">返回值的基数，必须是 2、8、10 或 16。 </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> 不是 2、8、10 或 16。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object)">
      <summary>将指定对象的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式；如果 <paramref name="value" /> 是一个值为 null 的对象，则为 <see cref="F:System.String.Empty" />。如果 <paramref name="value" /> 为 null，则此方法返回 null。</returns>
      <param name="value">一个对象，用于提供要转换的值，或 null。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息将指定对象的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式；如果 <paramref name="value" /> 是一个值为 null 的对象，则为 <see cref="F:System.String.Empty" />。如果 <paramref name="value" /> 为 null，则此方法返回 null。</returns>
      <param name="value">一个对象，用于提供要转换的值，或 null。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte)">
      <summary>将指定的 8 位带符号整数的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的 8 位带符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将指定的 8 位带符号整数的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的 8 位带符号整数。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single)">
      <summary>将指定的单精度浮点数的值转换其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的单精度浮点数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将指定的单精度浮点数的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的单精度浮点数。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16)">
      <summary>将指定的 16 位无符号整数的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的 16 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将指定的 16 位无符号整数的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的 16 位无符号整数。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32)">
      <summary>将指定的 32 位无符号整数的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的 32 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将指定的 32 位无符号整数的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的 32 位无符号整数。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64)">
      <summary>将指定的 64 位无符号整数的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的 64 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将指定的 64 位无符号整数的值转换为其等效的字符串表示形式。</summary>
      <returns>
        <paramref name="value" /> 的字符串表示形式。</returns>
      <param name="value">要转换的 64 位无符号整数。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Boolean)">
      <summary>将指定的布尔值转换为等效的 16 位无符号整数。</summary>
      <returns>如果 <paramref name="value" /> 为 true，则为数字 1；否则，为 0。</returns>
      <param name="value">要转换的布尔值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Byte)">
      <summary>Converts the value of the specified 8-bit unsigned integer to the equivalent 16-bit unsigned integer.</summary>
      <returns>与 <paramref name="value" /> 等效的 16 位无符号整数。</returns>
      <param name="value">要转换的 8 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Char)">
      <summary>将指定 Unicode 字符的值转换为等效的 16 位无符号整数。</summary>
      <returns>等效于 <paramref name="value" /> 的 16 位无符号整数。</returns>
      <param name="value">要转换的 Unicode 字符。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Decimal)">
      <summary>将指定的十进制数的值转换为等效的 16 位无符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 16 位无符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的十进制数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零或大于 <see cref="F:System.UInt16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Double)">
      <summary>将指定的双精度浮点数的值转换为等效的 16 位无符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 16 位无符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的双精度浮点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零或大于 <see cref="F:System.UInt16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int16)">
      <summary>将指定的 16 位有符号整数的值转换为等效的 16 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 16 位无符号整数。</returns>
      <param name="value">要转换的 16 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int32)">
      <summary>将指定的 32 位有符号整数的值转换为等效的 16 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 16 位无符号整数。</returns>
      <param name="value">要转换的 32 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零或大于 <see cref="F:System.UInt16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int64)">
      <summary>将指定的 64 位有符号整数的值转换为等效的 16 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 16 位无符号整数。</returns>
      <param name="value">要转换的 64 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零或大于 <see cref="F:System.UInt16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object)">
      <summary>将指定对象的值转换为 16 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 16 位无符号整数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">用于实现 <see cref="T:System.IConvertible" /> 接口的对象，或为 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的格式不正确。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持该转换。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.UInt16.MinValue" /> 或大于 <see cref="F:System.UInt16.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式信息，将指定对象的值转换为 16 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 16 位无符号整数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">一个实现 <see cref="T:System.IConvertible" /> 接口的对象。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的格式不正确。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持该转换。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.UInt16.MinValue" /> 或大于 <see cref="F:System.UInt16.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.SByte)">
      <summary>将指定的 8 位有符号整数的值转换为等效的 16 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 16 位无符号整数。</returns>
      <param name="value">要转换的 8 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Single)">
      <summary>将指定的单精度浮点数的值转换为等效的 16 位无符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 16 位无符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的单精度浮点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零或大于 <see cref="F:System.UInt16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String)">
      <summary>将数字的指定字符串表示形式转换为等效的 16 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 16 位无符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />不是由一个可选符号后跟数字序列（0 到 9）组成的。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.UInt16.MinValue" /> 或大于 <see cref="F:System.UInt16.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将数字的指定字符串表示形式转换为等效的 16 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 16 位无符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />不是由一个可选符号后跟数字序列（0 到 9）组成的。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.UInt16.MinValue" /> 或大于 <see cref="F:System.UInt16.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.Int32)">
      <summary>将指定基数的数字的字符串表示形式转换为等效的 16 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 16 位无符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <param name="fromBase">
        <paramref name="value" /> 中数字的基数，它必须是 2、8、10 或 16。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> 不是 2、8、10 或 16。- 或 -<paramref name="value" />，它表示一个非 10 为基的无符号数，前面带一个负号。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> 为 <see cref="F:System.String.Empty" />。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 包含的一个字符不是 <paramref name="fromBase" /> 指定的基中的有效数字。如果 <paramref name="value" /> 中的第一个字符无效，异常消息则指示没有可转换的数字；否则，该消息将指示 <paramref name="value" /> 包含无效的尾随字符。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />，它表示一个非 10 为基的无符号数，前面带一个负号。- 或 -<paramref name="value" /> 表示小于 <see cref="F:System.UInt16.MinValue" /> 或大于 <see cref="F:System.UInt16.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt16)">
      <summary>返回指定的 16 位无符号整数；不执行任何实际的转换。</summary>
      <returns>
        <paramref name="value" /> 不经更改即返回。</returns>
      <param name="value">要返回的 16 位无符号整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt32)">
      <summary>将指定的 32 位无符号整数的值转换为等效的 16 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 16 位无符号整数。</returns>
      <param name="value">要转换的 32 位无符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.UInt16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt64)">
      <summary>将指定的 64 位无符号整数的值转换为等效的 16 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 16 位无符号整数。</returns>
      <param name="value">要转换的 64 位无符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.UInt16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Boolean)">
      <summary>将指定的布尔值转换为等效的 32 位无符号整数。</summary>
      <returns>如果 <paramref name="value" /> 为 true，则为数字 1；否则，为 0。</returns>
      <param name="value">要转换的布尔值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Byte)">
      <summary>将指定的 8 位无符号整数的值转换为等效的 32 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 32 位无符号整数。</returns>
      <param name="value">要转换的 8 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Char)">
      <summary>将指定 Unicode 字符的值转换为等效的 32 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 32 位无符号整数。</returns>
      <param name="value">要转换的 Unicode 字符。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Decimal)">
      <summary>将指定的十进制数的值转换为等效的 32 位无符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 32 位无符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的十进制数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零或大于 <see cref="F:System.UInt32.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Double)">
      <summary>将指定的双精度浮点数的值转换为等效的 32 位无符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 32 位无符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的双精度浮点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零或大于 <see cref="F:System.UInt32.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int16)">
      <summary>将指定的 16 位有符号整数的值转换为等效的 32 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 32 位无符号整数。</returns>
      <param name="value">要转换的 16 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int32)">
      <summary>将指定的 32 位有符号整数的值转换为等效的 32 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 32 位无符号整数。</returns>
      <param name="value">要转换的 32 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int64)">
      <summary>将指定的 64 位有符号整数的值转换为等效的 32 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 32 位无符号整数。</returns>
      <param name="value">要转换的 64 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零或大于 <see cref="F:System.UInt32.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object)">
      <summary>将指定对象的值转换为 32 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 32 位无符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">用于实现 <see cref="T:System.IConvertible" /> 接口的对象，或为 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的格式不正确。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持该转换。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.UInt32.MinValue" /> 或大于 <see cref="F:System.UInt32.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式信息，将指定对象的值转换为 32 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 32 位无符号整数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">一个实现 <see cref="T:System.IConvertible" /> 接口的对象。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的格式不正确。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持该转换。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.UInt32.MinValue" /> 或大于 <see cref="F:System.UInt32.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.SByte)">
      <summary>将指定的 8 位有符号整数的值转换为等效的 32 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 32 位无符号整数。</returns>
      <param name="value">要转换的 8 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Single)">
      <summary>将指定的单精度浮点数的值转换为等效的 32 位无符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 32 位无符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的单精度浮点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零或大于 <see cref="F:System.UInt32.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String)">
      <summary>将数字的指定字符串表示形式转换为等效的 32 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 32 位无符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />不是由一个可选符号后跟数字序列（0 到 9）组成的。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.UInt32.MinValue" /> 或大于 <see cref="F:System.UInt32.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将数字的指定字符串表示形式转换为等效的 32 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 32 位无符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />不是由一个可选符号后跟数字序列（0 到 9）组成的。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.UInt32.MinValue" /> 或大于 <see cref="F:System.UInt32.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.Int32)">
      <summary>将指定基数的数字的字符串表示形式转换为等效的 32 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 32 位无符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <param name="fromBase">
        <paramref name="value" /> 中数字的基数，它必须是 2、8、10 或 16。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> 不是 2、8、10 或 16。- 或 -<paramref name="value" />，它表示一个非 10 为基的无符号数，前面带一个负号。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> 为 <see cref="F:System.String.Empty" />。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 包含的一个字符不是 <paramref name="fromBase" /> 指定的基中的有效数字。如果 <paramref name="value" /> 中的第一个字符无效，异常消息则指示没有可转换的数字；否则，该消息将指示 <paramref name="value" /> 包含无效的尾随字符。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />，它表示一个非 10 为基的无符号数，前面带一个负号。- 或 -<paramref name="value" /> 表示小于 <see cref="F:System.UInt32.MinValue" /> 或大于 <see cref="F:System.UInt32.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt16)">
      <summary>将指定的 16 位无符号整数的值转换为等效的 32 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 32 位无符号整数。</returns>
      <param name="value">要转换的 16 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt32)">
      <summary>返回指定的 32 位无符号整数；不执行任何实际的转换。</summary>
      <returns>
        <paramref name="value" /> 不经更改即返回。</returns>
      <param name="value">要返回的 32 位无符号整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt64)">
      <summary>将指定的 64 位无符号整数的值转换为等效的 32 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 32 位无符号整数。</returns>
      <param name="value">要转换的 64 位无符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大于 <see cref="F:System.UInt32.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Boolean)">
      <summary>将指定的布尔值转换为等效的 64 位无符号整数。</summary>
      <returns>如果 <paramref name="value" /> 为 true，则为数字 1；否则，为 0。</returns>
      <param name="value">要转换的布尔值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Byte)">
      <summary>将指定的 8 位无符号整数的值转换为等效的 64 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位带符号整数。</returns>
      <param name="value">要转换的 8 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Char)">
      <summary>将指定 Unicode 字符的值转换为等效的 64 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位无符号整数。</returns>
      <param name="value">要转换的 Unicode 字符。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Decimal)">
      <summary>将指定的十进制数的值转换为等效的 64 位无符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 64 位无符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的十进制数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零或大于 <see cref="F:System.UInt64.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Double)">
      <summary>将指定的双精度浮点数的值转换为等效的 64 位无符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 64 位无符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的双精度浮点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零或大于 <see cref="F:System.UInt64.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int16)">
      <summary>将指定的 16 位有符号整数的值转换为等效的 64 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位无符号整数。</returns>
      <param name="value">要转换的 16 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int32)">
      <summary>将指定的 32 位有符号整数的值转换为等效的 64 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位无符号整数。</returns>
      <param name="value">要转换的 32 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int64)">
      <summary>将指定的 64 位有符号整数的值转换为等效的 64 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位无符号整数。</returns>
      <param name="value">要转换的 64 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object)">
      <summary>将指定对象的值转换为 64 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位无符号整数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">用于实现 <see cref="T:System.IConvertible" /> 接口的对象，或为 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的格式不正确。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持该转换。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.UInt64.MinValue" /> 或大于 <see cref="F:System.UInt64.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式信息，将指定对象的值转换为 64 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位无符号整数，如果 <paramref name="value" /> 为 null，则为零。</returns>
      <param name="value">一个实现 <see cref="T:System.IConvertible" /> 接口的对象。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的格式不正确。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不实现 <see cref="T:System.IConvertible" /> 接口。- 或 -不支持该转换。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.UInt64.MinValue" /> 或大于 <see cref="F:System.UInt64.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.SByte)">
      <summary>将指定的 8 位有符号整数的值转换为等效的 64 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位无符号整数。</returns>
      <param name="value">要转换的 8 位带符号整数。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Single)">
      <summary>将指定的单精度浮点数的值转换为等效的 64 位无符号整数。</summary>
      <returns>
        <paramref name="value" />，舍入为最接近的 64 位无符号整数。如果 <paramref name="value" /> 为两个整数中间的数字，则返回二者中的偶数；即 4.5 转换为 4，而 5.5 转换为 6。</returns>
      <param name="value">要转换的单精度浮点数。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小于零或大于 <see cref="F:System.UInt64.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String)">
      <summary>将数字的指定字符串表示形式转换为等效的 64 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 64 位带符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />不是由一个可选符号后跟数字序列（0 到 9）组成的。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.UInt64.MinValue" /> 或大于 <see cref="F:System.UInt64.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.IFormatProvider)">
      <summary>使用指定的区域性特定格式设置信息，将数字的指定字符串表示形式转换为等效的 64 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 64 位无符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <param name="provider">一个提供区域性特定的格式设置信息的对象。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" />不是由一个可选符号后跟数字序列（0 到 9）组成的。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小于 <see cref="F:System.UInt64.MinValue" /> 或大于 <see cref="F:System.UInt64.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.Int32)">
      <summary>将指定基数的数字的字符串表示形式转换为等效的 64 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 中数字等效的 64 位无符号整数，如果 <paramref name="value" /> 为 null，则为 0（零）。</returns>
      <param name="value">包含要转换的数字的字符串。</param>
      <param name="fromBase">
        <paramref name="value" /> 中数字的基数，它必须是 2、8、10 或 16。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> 不是 2、8、10 或 16。- 或 -<paramref name="value" />，它表示一个非 10 为基的无符号数，前面带一个负号。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> 为 <see cref="F:System.String.Empty" />。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 包含的一个字符不是 <paramref name="fromBase" /> 指定的基中的有效数字。如果 <paramref name="value" /> 中的第一个字符无效，异常消息则指示没有可转换的数字；否则，该消息将指示 <paramref name="value" /> 包含无效的尾随字符。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />，它表示一个非 10 为基的无符号数，前面带一个负号。- 或 -<paramref name="value" /> 表示小于 <see cref="F:System.UInt64.MinValue" /> 或大于 <see cref="F:System.UInt64.MaxValue" /> 的数字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt16)">
      <summary>将指定的 16 位无符号整数的值转换为等效的 64 位无符号整数。</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位无符号整数。</returns>
      <param name="value">要转换的 16 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt32)">
      <summary>Converts the value of the specified 32-bit unsigned integer to an equivalent 64-bit unsigned integer.</summary>
      <returns>与 <paramref name="value" /> 等效的 64 位无符号整数。</returns>
      <param name="value">要转换的 32 位无符号整数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt64)">
      <summary>返回指定的 64 位无符号整数；不执行任何实际的转换。</summary>
      <returns>
        <paramref name="value" /> 不经更改即返回。</returns>
      <param name="value">要返回的 64 位无符号整数。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Environment">
      <summary>提供有关当前环境和平台的信息以及操作它们的方法。此类不能被继承。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.CurrentManagedThreadId">
      <summary>获取当前托管线程的唯一标识符。</summary>
      <returns>一个整数，表示此托管线程的唯一标识符。</returns>
    </member>
    <member name="M:System.Environment.ExpandEnvironmentVariables(System.String)">
      <summary>将嵌入到指定字符串中的每个环境变量的名称替换为该变量的值的等效字符串，然后返回结果字符串。</summary>
      <returns>一个字符串，其中的每个环境变量均被替换为该变量的值。</returns>
      <param name="name">包含零个或多个环境变量名的字符串。每个环境变量都用百分号 (%) 引起来。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.FailFast(System.String)">
      <summary>向 Windows 的应用程序事件日志写入消息后立即终止进程，然后在发往 Microsoft 的错误报告中加入该消息。</summary>
      <param name="message">一条解释进程终止原因的消息；如果未提供解释，则为 null。</param>
    </member>
    <member name="M:System.Environment.FailFast(System.String,System.Exception)">
      <summary>向 Windows 的应用程序事件日志写入消息后立即终止进程，然后在发往 Microsoft 的错误报告中加入该消息和异常信息。</summary>
      <param name="message">一条解释进程终止原因的消息；如果未提供解释，则为 null。</param>
      <param name="exception">一个异常，表示导致终止的错误。通常这是 catch 块中的异常。</param>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariable(System.String)">
      <summary>从当前进程检索环境变量的值。</summary>
      <returns>
        <paramref name="variable" /> 指定的环境变量的值；或者如果找不到环境变量，则返回 null。</returns>
      <param name="variable">环境变量名。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariables">
      <summary>从当前进程检索所有环境变量名及其值。</summary>
      <returns>包含所有环境变量名及其值的字典；如果找不到任何环境变量，则返回空字典。</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <exception cref="T:System.OutOfMemoryException">The buffer is out of memory.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.HasShutdownStarted">
      <summary>获取一个值，该值指示当前的应用程序域是否正在卸载或者公共语言运行时 (CLR) 是否正在关闭。</summary>
      <returns>如果当前的应用程序域正在卸载或者 CLR 正在关闭，为 true；否则，为 false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.NewLine">
      <summary>获取为此环境定义的换行字符串。</summary>
      <returns>对于非 Unix 平台为包含“\r\n”的字符串，对于 Unix 平台则为包含“\n”的字符串。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.ProcessorCount">
      <summary>获取当前计算机上的处理器数。</summary>
      <returns>指定当前计算机上处理器个数的 32 位有符号整数。没有默认值。如果当前计算机包含多个处理器组，则此属性返回可用的逻辑处理器数以供公共语言运行时 (CLR) 使用。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="NUMBER_OF_PROCESSORS" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.SetEnvironmentVariable(System.String,System.String)">
      <summary>创建、修改或删除当前进程中存储的环境变量。</summary>
      <param name="variable">环境变量名。</param>
      <param name="value">要分配给 <paramref name="variable" /> 的值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="variable" /> contains a zero-length string, an initial hexadecimal zero character (0x00), or an equal sign ("="). -or-The length of <paramref name="variable" /> or <paramref name="value" /> is greater than or equal to 32,767 characters.-or-An error occurred during the execution of this operation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.StackTrace">
      <summary>获取当前的堆栈跟踪信息。</summary>
      <returns>包含堆栈跟踪信息的字符串。此值可为 <see cref="F:System.String.Empty" />。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The requested stack trace information is out of range.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.TickCount">
      <summary>获取系统启动后经过的毫秒数。</summary>
      <returns>一个 32 位带符号整数，它包含自上次启动计算机以来所经过的时间（以毫秒为单位）。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Math">
      <summary>为三角函数、对数函数和其他通用数学函数提供常数和静态方法。若要浏览此类型的.NET Framework 源代码，请参阅 Reference Source。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Decimal)">
      <summary>返回 <see cref="T:System.Decimal" /> 数字的绝对值。</summary>
      <returns>十进制数 x，使其满足 0 ≤ x ≤<see cref="F:System.Decimal.MaxValue" />。</returns>
      <param name="value">一个大于或等于 <see cref="F:System.Decimal.MinValue" /> 但小于或等于 <see cref="F:System.Decimal.MaxValue" /> 的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Double)">
      <summary>返回双精度浮点数字的绝对值。</summary>
      <returns>一个双精度浮点数 x，满足 0 ≤ x ≤<see cref="F:System.Double.MaxValue" />。</returns>
      <param name="value">一个大于或等于 <see cref="F:System.Double.MinValue" /> 但小于或等于 <see cref="F:System.Double.MaxValue" /> 的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int16)">
      <summary>返回 16 位有符号整数的绝对值。</summary>
      <returns>16 位带符号整数 x，满足 0 ≤ x ≤<see cref="F:System.Int16.MaxValue" />。</returns>
      <param name="value">一个大于 <see cref="F:System.Int16.MinValue" /> 但小于或等于 <see cref="F:System.Int16.MaxValue" /> 的数字。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 等于 <see cref="F:System.Int16.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int32)">
      <summary>返回 32 位有符号整数的绝对值。</summary>
      <returns>32 位带符号整数 x，满足 0 ≤ x ≤<see cref="F:System.Int32.MaxValue" />。</returns>
      <param name="value">一个大于 <see cref="F:System.Int32.MinValue" /> 但小于或等于 <see cref="F:System.Int32.MaxValue" /> 的数字。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 等于 <see cref="F:System.Int32.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int64)">
      <summary>返回 64 位有符号整数的绝对值。</summary>
      <returns>64 位带符号整数 x，满足 0 ≤ x ≤<see cref="F:System.Int64.MaxValue" />。</returns>
      <param name="value">一个大于 <see cref="F:System.Int64.MinValue" /> 但小于或等于 <see cref="F:System.Int64.MaxValue" /> 的数字。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 等于 <see cref="F:System.Int64.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.SByte)">
      <summary>返回 8 位有符号整数的绝对值。</summary>
      <returns>8 位有符号整数 x，满足 0 ≤ x ≤<see cref="F:System.SByte.MaxValue" />。</returns>
      <param name="value">一个大于 <see cref="F:System.SByte.MinValue" /> 但小于或等于 <see cref="F:System.SByte.MaxValue" /> 的数字。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 等于 <see cref="F:System.SByte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Single)">
      <summary>返回单精度浮点数字的绝对值。</summary>
      <returns>一个单精度浮点数 x，满足 0 ≤ x ≤<see cref="F:System.Single.MaxValue" />。</returns>
      <param name="value">一个大于或等于 <see cref="F:System.Single.MinValue" /> 但小于或等于 <see cref="F:System.Single.MaxValue" /> 的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Acos(System.Double)">
      <summary>返回余弦值为指定数字的角度。</summary>
      <returns>角度 θ，以弧度为单位，满足 0 ≤θ≤π- 或 - 如果 <paramref name="d" /> &lt; -1 或 <paramref name="d" /> &gt; 1 或 <paramref name="d" /> 等于 <see cref="F:System.Double.NaN" />，则为 <see cref="F:System.Double.NaN" />。</returns>
      <param name="d">一个表示余弦值的数字，其中 <paramref name="d" /> 必须大于或等于 -1 但小于或等于 1。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Asin(System.Double)">
      <summary>返回正弦值为指定数字的角度。</summary>
      <returns>角度 θ，以弧度为单位，满足 π/2 ≤θ≤π/2 - 或 - 如果 <paramref name="d" /> &lt; -1 或 <paramref name="d" /> &gt; 1 或 <paramref name="d" /> 等于 <see cref="F:System.Double.NaN" />，则为 <see cref="F:System.Double.NaN" />。</returns>
      <param name="d">一个表示正弦值的数字，其中 <paramref name="d" /> 必须大于或等于 -1 但小于或等于 1。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan(System.Double)">
      <summary>返回正切值为指定数字的角度。</summary>
      <returns>角度 θ，以弧度为单位，满足 -π/2 ≤θ≤π/2。- 或 - 如果 <paramref name="d" /> 等于 <see cref="F:System.Double.NaN" />，则为 <see cref="F:System.Double.NaN" />；如果 <paramref name="d" /> 等于 <see cref="F:System.Double.NegativeInfinity" />，则为舍入为双精度值 (-1.5707963267949) 的 -π/2；或者如果 <paramref name="d" /> 等于 <see cref="F:System.Double.PositiveInfinity" />，则为舍入为双精度值 (1.5707963267949) 的 π/2。</returns>
      <param name="d">表示正切值的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan2(System.Double,System.Double)">
      <summary>返回正切值为两个指定数字的商的角度。</summary>
      <returns>角度 θ，以弧度为单位，满足 -π≤θ≤π，且 tan(θ) = <paramref name="y" /> / <paramref name="x" />，其中 (<paramref name="x" />, <paramref name="y" />) 是笛卡尔平面中的点。请看下面：对于 (<paramref name="x" />, ，<paramref name="y" />) 在象限 1、 0 &lt; θ &lt; π/2。对于 (<paramref name="x" />, ，<paramref name="y" />) 在象限中 2， π/2 &lt; θ≤π。对于 (<paramref name="x" />, ，<paramref name="y" />) 在象限中 3，-π &lt; θ &lt;-π/2。对于 (<paramref name="x" />, ，<paramref name="y" />) 在象限中 4 中，-π/2 &lt; θ &lt; 0。如果点在象限的边界上，则返回值如下：如果 y 为 0 并且 x 不为负值，则 θ = 0。如果 y 为 0 并且 x 为负值，则 θ = π。如果 y 为正值并且 x 为 0，则 θ = π/2。如果 y 为负值并且 x 为 0，则 θ = -π/2。如果 y 为 0 并且 x 为 0，则 θ = 0。如果 <paramref name="x" /> 或 <paramref name="y" /> 为 <see cref="F:System.Double.NaN" />，或者如果 <paramref name="x" /> 和 <paramref name="y" /> 为 <see cref="F:System.Double.PositiveInfinity" /> 或 <see cref="F:System.Double.NegativeInfinity" />，则该方法返回 <see cref="F:System.Double.NaN" />。</returns>
      <param name="y">点的 y 坐标。</param>
      <param name="x">点的 x 坐标。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Decimal)">
      <summary>返回大于或等于指定的十进制数的最小整数值。</summary>
      <returns>大于或等于 <paramref name="d" /> 的最小整数值。请注意，此方法返回 <see cref="T:System.Decimal" />，而不是整数类型。</returns>
      <param name="d">十进制数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Double)">
      <summary>返回大于或等于指定的双精度浮点数的最小整数值。</summary>
      <returns>大于或等于 <paramref name="a" /> 的最小整数值。如果 <paramref name="a" /> 等于 <see cref="F:System.Double.NaN" />、<see cref="F:System.Double.NegativeInfinity" /> 或 <see cref="F:System.Double.PositiveInfinity" />，则返回该值。请注意，此方法返回 <see cref="T:System.Double" />，而不是整数类型。</returns>
      <param name="a">一个双精度浮点数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cos(System.Double)">
      <summary>返回指定角度的余弦值。</summary>
      <returns>
        <paramref name="d" /> 的余弦值。如果 <paramref name="d" /> 等于 <see cref="F:System.Double.NaN" />、<see cref="F:System.Double.NegativeInfinity" /> 或 <see cref="F:System.Double.PositiveInfinity" />，此方法将返回 <see cref="F:System.Double.NaN" />。</returns>
      <param name="d">以弧度计量的角度。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cosh(System.Double)">
      <summary>返回指定角度的双曲余弦值。</summary>
      <returns>
        <paramref name="value" /> 的双曲余弦值。如果 <paramref name="value" /> 等于 <see cref="F:System.Double.NegativeInfinity" /> 或 <see cref="F:System.Double.PositiveInfinity" />，则返回 <see cref="F:System.Double.PositiveInfinity" />。如果 <paramref name="value" /> 等于 <see cref="F:System.Double.NaN" />，则返回 <see cref="F:System.Double.NaN" />。</returns>
      <param name="value">以弧度计量的角度。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.E">
      <summary>表示自然对数的底，它由常数 e 指定。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Exp(System.Double)">
      <summary>返回 e 的指定次幂。</summary>
      <returns>数字 e 的 <paramref name="d" /> 次幂。如果 <paramref name="d" /> 等于 <see cref="F:System.Double.NaN" /> 或 <see cref="F:System.Double.PositiveInfinity" />，则返回该值。如果 <paramref name="d" /> 等于 <see cref="F:System.Double.NegativeInfinity" />，则返回 0。</returns>
      <param name="d">指定幂的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Decimal)">
      <summary>返回小于或等于指定小数的最大整数。</summary>
      <returns>小于或等于 <paramref name="d" /> 的最大整数。请注意，该方法将返回 <see cref="T:System.Math" /> 类型的整数值。</returns>
      <param name="d">十进制数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Double)">
      <summary>返回小于或等于指定双精度浮点数的最大整数。</summary>
      <returns>小于或等于 <paramref name="d" /> 的最大整数。如果 <paramref name="d" /> 等于 <see cref="F:System.Double.NaN" />、<see cref="F:System.Double.NegativeInfinity" /> 或 <see cref="F:System.Double.PositiveInfinity" />，则返回该值。</returns>
      <param name="d">一个双精度浮点数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.IEEERemainder(System.Double,System.Double)">
      <summary>返回一指定数字被另一指定数字相除的余数。</summary>
      <returns>一个数等于 <paramref name="x" /> - (<paramref name="y" /> Q)，其中 Q 是 <paramref name="x" /> / <paramref name="y" /> 的商的最接近整数（如果 <paramref name="x" /> / <paramref name="y" /> 在两个整数中间，则返回偶数）。如果 <paramref name="x" /> - (<paramref name="y" /> Q) 为零，则在 <paramref name="x" /> 为正时返回值 +0，而在 <paramref name="x" /> 为负时返回 -0。如果 <paramref name="y" /> = 0，则返回 <see cref="F:System.Double.NaN" />。</returns>
      <param name="x">被除数。</param>
      <param name="y">除数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double)">
      <summary>返回指定数字的自然对数（底为 e）。</summary>
      <returns>下表中的值之一。<paramref name="d" /> 参数返回值 正 自然对数的 <paramref name="d" />； 也就是说，ln <paramref name="d" />, ，或日志 e<paramref name="d" />零 <see cref="F:System.Double.NegativeInfinity" />负数 <see cref="F:System.Double.NaN" />等于 <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />等于 <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">要查找其对数的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double,System.Double)">
      <summary>返回指定数字在使用指定底时的对数。</summary>
      <returns>下表中的值之一。（+Infinity 表示 <see cref="F:System.Double.PositiveInfinity" />，-Infinity 表示 <see cref="F:System.Double.NegativeInfinity" />，NaN 表示 <see cref="F:System.Double.NaN" />。）<paramref name="a" /><paramref name="newBase" />返回值<paramref name="a" />&gt; 0(0 &lt;<paramref name="newBase" />&lt; 1) 或 (<paramref name="newBase" />&gt; 1)lognewBase(a)<paramref name="a" />&lt; 0（任意值）NaN（任意值）<paramref name="newBase" />&lt; 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = +InfinityNaN<paramref name="a" /> = NaN（任意值）NaN（任意值）<paramref name="newBase" /> = NaNNaN（任意值）<paramref name="newBase" /> = 1NaN<paramref name="a" /> = 00 &lt;<paramref name="newBase" />&lt; 1 +Infinity<paramref name="a" /> = 0<paramref name="newBase" />&gt; 1-Infinity<paramref name="a" /> = + 无穷大0 &lt;<paramref name="newBase" />&lt; 1-Infinity<paramref name="a" /> = + 无穷大<paramref name="newBase" />&gt; 1+Infinity<paramref name="a" /> = 1<paramref name="newBase" /> = 00<paramref name="a" /> = 1<paramref name="newBase" /> = +Infinity0</returns>
      <param name="a">要查找其对数的数字。</param>
      <param name="newBase">对数的底。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log10(System.Double)">
      <summary>返回指定数字以 10 为底的对数。</summary>
      <returns>下表中的值之一。<paramref name="d" /> 参数 返回值 正 基准的 10 个日志的 <paramref name="d" />； 也就是说，记录 10<paramref name="d" />。零 <see cref="F:System.Double.NegativeInfinity" />负数 <see cref="F:System.Double.NaN" />等于 <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />等于 <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">要查找其对数的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Byte,System.Byte)">
      <summary>返回两个 8 位无符号整数中较大的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较大的一个。</returns>
      <param name="val1">要比较的两个 8 位无符号整数中的第一个。</param>
      <param name="val2">要比较的两个 8 位无符号整数中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Decimal,System.Decimal)">
      <summary>返回两个十进制数中较大的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较大的一个。</returns>
      <param name="val1">要比较的两个十进制数字中的第一个。</param>
      <param name="val2">要比较的两个十进制数字中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Double,System.Double)">
      <summary>返回两个双精度浮点数字中较大的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较大的一个。如果 <paramref name="val1" /> 或 <paramref name="val2" /> 或者 <paramref name="val1" /> 和 <paramref name="val2" /> 都等于 <see cref="F:System.Double.NaN" />，则返回 <see cref="F:System.Double.NaN" />。</returns>
      <param name="val1">要比较的两个双精度浮点数中的第一个。</param>
      <param name="val2">要比较的两个双精度浮点数中的第二个</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int16,System.Int16)">
      <summary>返回两个 16 位有符号的整数中较大的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较大的一个。</returns>
      <param name="val1">要比较的两个 16 位有符号整数中的第一个。</param>
      <param name="val2">要比较的两个 16 位有符号整数中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int32,System.Int32)">
      <summary>返回两个 32 位有符号的整数中较大的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较大的一个。</returns>
      <param name="val1">要比较的两个 32 位有符号整数中的第一个。</param>
      <param name="val2">要比较的两个 32 位有符号整数中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int64,System.Int64)">
      <summary>返回两个 64 位有符号的整数中较大的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较大的一个。</returns>
      <param name="val1">要比较的两个 64 位有符号整数中的第一个。</param>
      <param name="val2">要比较的两个 64 位有符号整数中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.SByte,System.SByte)">
      <summary>返回两个 8 位有符号的整数中较大的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较大的一个。</returns>
      <param name="val1">要比较的两个 8 位有符号整数中的第一个。</param>
      <param name="val2">要比较的两个 8 位有符号整数中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Single,System.Single)">
      <summary>返回两个单精度浮点数字中较大的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较大的一个。如果 <paramref name="val1" /> 或 <paramref name="val2" /> 或者 <paramref name="val1" /> 和 <paramref name="val2" /> 都等于 <see cref="F:System.Single.NaN" />，则返回 <see cref="F:System.Single.NaN" />。</returns>
      <param name="val1">要比较的两个单精度浮点数中的第一个。</param>
      <param name="val2">要比较的两个单精度浮点数中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt16,System.UInt16)">
      <summary>返回两个 16 位无符号整数中较大的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较大的一个。</returns>
      <param name="val1">要比较的两个 16 位无符号整数中的第一个。</param>
      <param name="val2">要比较的两个 16 位无符号整数中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt32,System.UInt32)">
      <summary>返回两个 32 位无符号整数中较大的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较大的一个。</returns>
      <param name="val1">要比较的两个 32 位无符号整数中的第一个。</param>
      <param name="val2">要比较的两个 32 位无符号整数中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt64,System.UInt64)">
      <summary>返回两个 64 位无符号整数中较大的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较大的一个。</returns>
      <param name="val1">要比较的两个 64 位无符号整数中的第一个。</param>
      <param name="val2">要比较的两个 64 位无符号整数中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Byte,System.Byte)">
      <summary>返回两个 8 位无符号整数中较小的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较小的一个。</returns>
      <param name="val1">要比较的两个 8 位无符号整数中的第一个。</param>
      <param name="val2">要比较的两个 8 位无符号整数中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Decimal,System.Decimal)">
      <summary>返回两个十进制数中较小的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较小的一个。</returns>
      <param name="val1">要比较的两个十进制数字中的第一个。</param>
      <param name="val2">要比较的两个十进制数字中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Double,System.Double)">
      <summary>返回两个双精度浮点数字中较小的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较小的一个。如果 <paramref name="val1" /> 或 <paramref name="val2" /> 或者 <paramref name="val1" /> 和 <paramref name="val2" /> 都等于 <see cref="F:System.Double.NaN" />，则返回 <see cref="F:System.Double.NaN" />。</returns>
      <param name="val1">要比较的两个双精度浮点数中的第一个。</param>
      <param name="val2">要比较的两个双精度浮点数中的第二个</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int16,System.Int16)">
      <summary>返回两个 16 位有符号整数中较小的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较小的一个。</returns>
      <param name="val1">要比较的两个 16 位有符号整数中的第一个。</param>
      <param name="val2">要比较的两个 16 位有符号整数中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int32,System.Int32)">
      <summary>返回两个 32 位有符号整数中较小的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较小的一个。</returns>
      <param name="val1">要比较的两个 32 位有符号整数中的第一个。</param>
      <param name="val2">要比较的两个 32 位有符号整数中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int64,System.Int64)">
      <summary>返回两个 64 位有符号整数中较小的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较小的一个。</returns>
      <param name="val1">要比较的两个 64 位有符号整数中的第一个。</param>
      <param name="val2">要比较的两个 64 位有符号整数中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.SByte,System.SByte)">
      <summary>返回两个 8 位有符号整数中较小的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较小的一个。</returns>
      <param name="val1">要比较的两个 8 位有符号整数中的第一个。</param>
      <param name="val2">要比较的两个 8 位有符号整数中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Single,System.Single)">
      <summary>返回两个单精度浮点数字中较小的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较小的一个。如果 <paramref name="val1" /> 或 <paramref name="val2" /> 或者 <paramref name="val1" /> 和 <paramref name="val2" /> 都等于 <see cref="F:System.Single.NaN" />，则返回 <see cref="F:System.Single.NaN" />。</returns>
      <param name="val1">要比较的两个单精度浮点数中的第一个。</param>
      <param name="val2">要比较的两个单精度浮点数中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt16,System.UInt16)">
      <summary>返回两个 16 位无符号整数中较小的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较小的一个。</returns>
      <param name="val1">要比较的两个 16 位无符号整数中的第一个。</param>
      <param name="val2">要比较的两个 16 位无符号整数中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt32,System.UInt32)">
      <summary>返回两个 32 位无符号整数中较小的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较小的一个。</returns>
      <param name="val1">要比较的两个 32 位无符号整数中的第一个。</param>
      <param name="val2">要比较的两个 32 位无符号整数中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt64,System.UInt64)">
      <summary>返回两个 64 位无符号整数中较小的一个。</summary>
      <returns>
        <paramref name="val1" /> 或 <paramref name="val2" /> 参数中较小的一个。</returns>
      <param name="val1">要比较的两个 64 位无符号整数中的第一个。</param>
      <param name="val2">要比较的两个 64 位无符号整数中的第二个。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.PI">
      <summary>表示圆的周长与其直径的比值，由常数 π 指定。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Pow(System.Double,System.Double)">
      <summary>返回指定数字的指定次幂。</summary>
      <returns>数字 <paramref name="x" /> 的 <paramref name="y" /> 次幂。</returns>
      <param name="x">要乘幂的双精度浮点数。</param>
      <param name="y">指定幂的双精度浮点数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal)">
      <summary>将小数值舍入到最接近的整数值。</summary>
      <returns>最接近参数 <paramref name="d" /> 的整数。如果 <paramref name="d" /> 的小数部分正好处于两个整数中间，其中一个整数为偶数，另一个整数为奇数，则返回偶数。请注意，此方法返回 <see cref="T:System.Decimal" />，而不是整数类型。</returns>
      <param name="d">要舍入的小数。</param>
      <exception cref="T:System.OverflowException">结果超出了 <see cref="T:System.Decimal" /> 的范围。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32)">
      <summary>将小数值按指定的小数位数舍入。</summary>
      <returns>最接近 <paramref name="d" /> 的 <paramref name="decimals" /> 位小数的数字。</returns>
      <param name="d">要舍入的小数。</param>
      <param name="decimals">返回值中的小数位数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> 为小于 0 或大于 28。</exception>
      <exception cref="T:System.OverflowException">结果超出了 <see cref="T:System.Decimal" /> 的范围。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32,System.MidpointRounding)">
      <summary>将小数值按指定的小数位数舍入。一个参数，指定当一个值正好处于两个数中间时如何舍入这个值。</summary>
      <returns>最接近 <paramref name="d" /> 的 <paramref name="decimals" /> 位小数的数字。如果 <paramref name="d" /> 比 <paramref name="decimals" /> 少部分数字，<paramref name="d" /> 原样返回。</returns>
      <param name="d">要舍入的小数。</param>
      <param name="decimals">返回值中的小数位数。</param>
      <param name="mode">在两个数字之间时如何舍入 <paramref name="d" /> 的规范。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> 为小于 0 或大于 28。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> 不是有效的 <see cref="T:System.MidpointRounding" /> 值。</exception>
      <exception cref="T:System.OverflowException">结果超出了 <see cref="T:System.Decimal" /> 的范围。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.MidpointRounding)">
      <summary>将小数值舍入到最接近的整数。一个参数，指定当一个值正好处于两个数中间时如何舍入这个值。</summary>
      <returns>最接近 <paramref name="d" /> 的整数。如果 <paramref name="d" /> 是两个数字的中值，这两个数字一个为偶数，另一个为奇数，则 <paramref name="mode" /> 确定返回两个数字中的哪一个。</returns>
      <param name="d">要舍入的小数。</param>
      <param name="mode">在两个数字之间时如何舍入 <paramref name="d" /> 的规范。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> 不是有效的 <see cref="T:System.MidpointRounding" /> 值。</exception>
      <exception cref="T:System.OverflowException">结果超出了 <see cref="T:System.Decimal" /> 的范围。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double)">
      <summary>将双精度浮点值舍入为最接近的整数值。</summary>
      <returns>最接近 <paramref name="a" /> 的整数。如果 <paramref name="a" /> 的小数部分正好处于两个整数中间，其中一个整数为偶数，另一个整数为奇数，则返回偶数。请注意，此方法返回 <see cref="T:System.Double" />，而不是整数类型。</returns>
      <param name="a">要舍入的双精度浮点数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32)">
      <summary>将双精度浮点值按指定的小数位数舍入。</summary>
      <returns>最接近 <paramref name="value" /> 的 <paramref name="digits" /> 位小数的数字。</returns>
      <param name="value">要舍入的双精度浮点数。</param>
      <param name="digits">返回值中的小数数字。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> 为小于 0 或大于 15。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32,System.MidpointRounding)">
      <summary>将双精度浮点值按指定的小数位数舍入。一个参数，指定当一个值正好处于两个数中间时如何舍入这个值。</summary>
      <returns>最接近 <paramref name="value" /> 的 <paramref name="digits" /> 位小数的数字。如果 <paramref name="value" /> 比 <paramref name="digits" /> 少部分数字，<paramref name="value" /> 原样返回。</returns>
      <param name="value">要舍入的双精度浮点数。</param>
      <param name="digits">返回值中的小数数字。</param>
      <param name="mode">在两个数字之间时如何舍入 <paramref name="value" /> 的规范。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> 为小于 0 或大于 15。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> 不是有效的 <see cref="T:System.MidpointRounding" /> 值。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.MidpointRounding)">
      <summary>将双精度浮点值舍入为最接近的整数。一个参数，指定当一个值正好处于两个数中间时如何舍入这个值。</summary>
      <returns>最接近 <paramref name="value" /> 的整数。如果 <paramref name="value" /> 是两个整数的中值，这两个整数一个为偶数，另一个为奇数，则 <paramref name="mode" /> 确定返回两个整数中的哪一个。</returns>
      <param name="value">要舍入的双精度浮点数。</param>
      <param name="mode">在两个数字之间时如何舍入 <paramref name="value" /> 的规范。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> 不是有效的 <see cref="T:System.MidpointRounding" /> 值。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Decimal)">
      <summary>返回表示十进制数符号的值。</summary>
      <returns>一个指示 <paramref name="value" /> 的符号的数字，如下表所示。返回值 含义 -1 <paramref name="value" /> 小于零。0 <paramref name="value" /> 等于零。1 <paramref name="value" /> 大于零。</returns>
      <param name="value">已签名的十进制数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Double)">
      <summary>返回表示双精度浮点数字的符号的值。</summary>
      <returns>一个指示 <paramref name="value" /> 的符号的数字，如下表所示。返回值 含义 -1 <paramref name="value" /> 小于零。0 <paramref name="value" /> 等于零。1 <paramref name="value" /> 大于零。</returns>
      <param name="value">有符号的数字。</param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" /> 等于 <see cref="F:System.Double.NaN" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int16)">
      <summary>返回表示 16 位有符号整数的符号的值。</summary>
      <returns>一个指示 <paramref name="value" /> 的符号的数字，如下表所示。返回值 含义 -1 <paramref name="value" /> 小于零。0 <paramref name="value" /> 等于零。1 <paramref name="value" /> 大于零。</returns>
      <param name="value">有符号的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int32)">
      <summary>返回表示 32 位有符号整数的符号的值。</summary>
      <returns>一个指示 <paramref name="value" /> 的符号的数字，如下表所示。返回值 含义 -1 <paramref name="value" /> 小于零。0 <paramref name="value" /> 等于零。1 <paramref name="value" /> 大于零。</returns>
      <param name="value">有符号的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int64)">
      <summary>返回表示 64 位有符号整数的符号的值。</summary>
      <returns>一个指示 <paramref name="value" /> 的符号的数字，如下表所示。返回值 含义 -1 <paramref name="value" /> 小于零。0 <paramref name="value" /> 等于零。1 <paramref name="value" /> 大于零。</returns>
      <param name="value">有符号的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.SByte)">
      <summary>返回表示 8 位有符号整数的符号的值。</summary>
      <returns>一个指示 <paramref name="value" /> 的符号的数字，如下表所示。返回值 含义 -1 <paramref name="value" /> 小于零。0 <paramref name="value" /> 等于零。1 <paramref name="value" /> 大于零。</returns>
      <param name="value">有符号的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Single)">
      <summary>返回表示单精度浮点数字的符号的值。</summary>
      <returns>一个指示 <paramref name="value" /> 的符号的数字，如下表所示。返回值 含义 -1 <paramref name="value" /> 小于零。0 <paramref name="value" /> 等于零。1 <paramref name="value" /> 大于零。</returns>
      <param name="value">有符号的数字。</param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" /> 等于 <see cref="F:System.Single.NaN" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sin(System.Double)">
      <summary>返回指定角度的正弦值。</summary>
      <returns>
        <paramref name="a" /> 的正弦值。如果 <paramref name="a" /> 等于 <see cref="F:System.Double.NaN" />、<see cref="F:System.Double.NegativeInfinity" /> 或 <see cref="F:System.Double.PositiveInfinity" />，此方法将返回 <see cref="F:System.Double.NaN" />。</returns>
      <param name="a">以弧度计量的角度。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sinh(System.Double)">
      <summary>返回指定角度的双曲正弦值。</summary>
      <returns>
        <paramref name="value" /> 的双曲正弦值。如果 <paramref name="value" /> 等于 <see cref="F:System.Double.NegativeInfinity" />、<see cref="F:System.Double.PositiveInfinity" /> 或 <see cref="F:System.Double.NaN" />，则此方法返回等于 <paramref name="value" /> 的 <see cref="T:System.Double" />。</returns>
      <param name="value">以弧度计量的角度。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sqrt(System.Double)">
      <summary>返回指定数字的平方根。</summary>
      <returns>下表中的值之一。<paramref name="d" /> 参数 返回值 零或正数 <paramref name="d" /> 的正平方根。负数 <see cref="F:System.Double.NaN" />等于 <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />等于 <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">将查找其平方根的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tan(System.Double)">
      <summary>返回指定角度的正切值。</summary>
      <returns>
        <paramref name="a" /> 的正切值。如果 <paramref name="a" /> 等于 <see cref="F:System.Double.NaN" />、<see cref="F:System.Double.NegativeInfinity" /> 或 <see cref="F:System.Double.PositiveInfinity" />，此方法将返回 <see cref="F:System.Double.NaN" />。</returns>
      <param name="a">以弧度计量的角度。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tanh(System.Double)">
      <summary>返回指定角度的双曲正切值。</summary>
      <returns>
        <paramref name="value" /> 的双曲正切值。如果 <paramref name="value" /> 等于 <see cref="F:System.Double.NegativeInfinity" />，则此方法返回 -1。如果值等于 <see cref="F:System.Double.PositiveInfinity" />，则此方法返回 1。如果 <paramref name="value" /> 等于 <see cref="F:System.Double.NaN" />，则此方法返回 <see cref="F:System.Double.NaN" />。</returns>
      <param name="value">以弧度计量的角度。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Decimal)">
      <summary>计算一个数字的整数部分。</summary>
      <returns>
        <paramref name="d" /> 的整数部分（即舍弃小数位后剩余的数）。</returns>
      <param name="d">要截断的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Double)">
      <summary>计算指定双精度浮点数的整数部分。</summary>
      <returns>
        <paramref name="d" /> 的整数部分（即舍弃小数位后剩余的数或下表所列出的值之一）。<paramref name="d" />返回值<see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">要截断的数字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.MidpointRounding">
      <summary>指定数学舍入方法应如何处理两个数字间的中间值。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.MidpointRounding.AwayFromZero">
      <summary>当一个数字是其他两个数字的中间值时，会将其舍入为两个值中绝对值较小的值。</summary>
    </member>
    <member name="F:System.MidpointRounding.ToEven">
      <summary>当一个数字是其他两个数字的中间值时，会将其舍入为最接近的偶数。</summary>
    </member>
    <member name="T:System.Progress`1">
      <summary>提供调用每个报告进度的值的回调的 <see cref="T:System.IProgress`1" /> 。</summary>
      <typeparam name="T">指定进度报表值的类型。</typeparam>
    </member>
    <member name="M:System.Progress`1.#ctor">
      <summary>初始化 <see cref="T:System.Progress`1" /> 对象。</summary>
    </member>
    <member name="M:System.Progress`1.#ctor(System.Action{`0})">
      <summary>用指定的回调初始化 <see cref="T:System.Progress`1" /> 对象。</summary>
      <param name="handler">为每个报告的进度值调用处理程序。该处理程序会调用除了任何委托 <see cref="E:System.Progress`1.ProgressChanged" /> 事件注册。根据 <see cref="T:System.Threading.SynchronizationContext" /> 实例， <see cref="T:System.Progress`1" /> 在构造时所捕获的实例, 该处理程序实例很有可能同时调用自身。</param>
    </member>
    <member name="M:System.Progress`1.OnReport(`0)">
      <summary>报告进度更改。</summary>
      <param name="value">进度更新之后的值。</param>
    </member>
    <member name="E:System.Progress`1.ProgressChanged">
      <summary>为每个报告进度的值引发。</summary>
    </member>
    <member name="M:System.Progress`1.System#IProgress{T}#Report(`0)">
      <summary>报告进度更改。</summary>
      <param name="value">进度更新之后的值。</param>
    </member>
    <member name="T:System.Random">
      <summary>表示伪随机数生成器，这是一种能够产生满足某些随机性统计要求的数字序列的设备。若要浏览此类型的 .NET Framework 源代码，请参阅引用源。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.#ctor">
      <summary>使用与时间相关的默认种子值，初始化 <see cref="T:System.Random" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Random.#ctor(System.Int32)">
      <summary>使用指定的种子值初始化 <see cref="T:System.Random" /> 类的新实例。</summary>
      <param name="Seed">用来计算伪随机数序列起始值的数字。如果指定的是负数，则使用其绝对值。</param>
    </member>
    <member name="M:System.Random.Next">
      <summary>返回一个非负随机整数。</summary>
      <returns>大于等于零且小于 <see cref="F:System.Int32.MaxValue" /> 的 32 位带符号整数。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32)">
      <summary>返回一个小于所指定最大值的非负随机整数。</summary>
      <returns>大于等于零且小于 <paramref name="maxValue" /> 的 32 位带符号整数，即：返回值的范围通常包括零但不包括 <paramref name="maxValue" />。但是，如果 <paramref name="maxValue" /> 等于 0，则返回 <paramref name="maxValue" />。</returns>
      <param name="maxValue">要生成的随机数的上限（随机数不能取该上限值）。<paramref name="maxValue" /> 必须大于或等于 0。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maxValue" /> is less than 0. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32,System.Int32)">
      <summary>返回在指定范围内的任意整数。</summary>
      <returns>大于等于 <paramref name="minValue" /> 且小于 <paramref name="maxValue" /> 的 32 位带符号整数，即：返回值的范围包括 <paramref name="minValue" /> 但不包括 <paramref name="maxValue" />。如果 <paramref name="minValue" /> 等于 <paramref name="maxValue" />，则返回 <paramref name="minValue" />。</returns>
      <param name="minValue">返回的随机数的下界（随机数可取该下界值）。</param>
      <param name="maxValue">返回的随机数的上限（随机数不能取该上限值）。<paramref name="maxValue" /> 必须大于等于 <paramref name="minValue" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="minValue" /> is greater than <paramref name="maxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextBytes(System.Byte[])">
      <summary>用随机数填充指定字节数组的元素。</summary>
      <param name="buffer">包含随机数的字节数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextDouble">
      <summary>返回一个大于或等于 0.0 且小于 1.0 的随机浮点数。</summary>
      <returns>大于或等于 0.0 且小于 1.0 的双精度浮点数。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Sample">
      <summary>返回一个介于 0.0 和 1.0 之间的随机浮点数。</summary>
      <returns>大于或等于 0.0 且小于 1.0 的双精度浮点数。</returns>
    </member>
    <member name="T:System.StringComparer">
      <summary>表示一种字符串比较操作，该操作使用特定的大小写以及基于区域性的比较规则或序号比较规则。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.StringComparer.#ctor">
      <summary>初始化 <see cref="T:System.StringComparer" /> 类的新实例。</summary>
    </member>
    <member name="M:System.StringComparer.Compare(System.String,System.String)">
      <summary>当在派生类中重写时，将比较两个字符串并返回其相对排序顺序的指示。</summary>
      <returns>一个有符号整数，指示 <paramref name="x" /> 和 <paramref name="y" /> 的相对值，如下表所示。值含义小于零<paramref name="x" /> 在排序顺序中位于 <paramref name="y" /> 之前。- 或 -<paramref name="x" /> 是 null，且 <paramref name="y" /> 不是 null。零<paramref name="x" /> 等于 <paramref name="y" />。- 或 -<paramref name="x" /> 和 <paramref name="y" /> 均为 null。大于零<paramref name="x" /> 在排序顺序中位于 <paramref name="y" /> 之后。- 或 -<paramref name="y" /> 是 null，且 <paramref name="x" /> 不是 null。</returns>
      <param name="x">要与 <paramref name="y" /> 比较的字符串。</param>
      <param name="y">要与 <paramref name="x" /> 比较的字符串。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.CurrentCulture">
      <summary>获取一个 <see cref="T:System.StringComparer" /> 对象，该对象使用当前区域性的单词比较规则执行区分大小写的字符串比较。</summary>
      <returns>一个新 <see cref="T:System.StringComparer" /> 对象。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.StringComparer.CurrentCultureIgnoreCase">
      <summary>获取一个 <see cref="T:System.StringComparer" /> 对象，该对象使用当前区域性的单词比较规则执行不区分大小写的字符串比较。</summary>
      <returns>一个新 <see cref="T:System.StringComparer" /> 对象。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.StringComparer.Equals(System.String,System.String)">
      <summary>当在派生类中重写时，指示两个字符串是否相等。</summary>
      <returns>如果 true 和 <paramref name="x" /> 引用同一对象，或者 <paramref name="y" /> 和 <paramref name="x" /> 相等，或者 <paramref name="y" /> 和 <paramref name="x" /> 都是 <paramref name="y" />，则为 null；否则为 false。</returns>
      <param name="x">要与 <paramref name="y" /> 比较的字符串。</param>
      <param name="y">要与 <paramref name="x" /> 比较的字符串。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.GetHashCode(System.String)">
      <summary>当在派生类中重写时，将获取指定字符串的哈希代码。</summary>
      <returns>根据 <paramref name="obj" /> 参数的值计算出的 32 位有符号哈希代码。</returns>
      <param name="obj">一个字符串。</param>
      <exception cref="T:System.ArgumentException">没有足够的内存可用于分配计算哈希代码所需的缓冲区。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="obj" /> 为 null。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.StringComparer.Ordinal">
      <summary>获取一个 <see cref="T:System.StringComparer" /> 对象，该对象执行区分大小写的序号字符串比较。</summary>
      <returns>一个 <see cref="T:System.StringComparer" /> 对象。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.OrdinalIgnoreCase">
      <summary>获取一个 <see cref="T:System.StringComparer" /> 对象，该对象执行不区分大小写的序号字符串比较。</summary>
      <returns>一个 <see cref="T:System.StringComparer" /> 对象。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>比较两个对象并返回一个值，指示一个对象是小于、等于还是大于另一个对象。</summary>
      <returns>一个有符号整数，指示 <paramref name="x" /> 和 <paramref name="y" /> 的相对值，如下表所示。值含义小于零<paramref name="x" /> 小于 <paramref name="y" />。零<paramref name="x" /> 等于 <paramref name="y" />。大于零<paramref name="x" /> 大于 <paramref name="y" />。</returns>
      <param name="x">要比较的第一个对象。</param>
      <param name="y">要比较的第二个对象。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> 和 <paramref name="y" /> 都不实现 <see cref="T:System.IComparable" /> 接口。- 或 -<paramref name="x" /> 和 <paramref name="y" /> 的类型不同，它们都无法处理与另一个进行的比较。</exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>确定指定的对象是否相等。</summary>
      <returns>如果指定的对象相等，则为 true；否则为 false。</returns>
      <param name="x">要比较的第一个对象。</param>
      <param name="y">要比较的第二个对象。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> 和 <paramref name="y" /> 的类型不同，它们都无法处理与另一个进行的比较。</exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>返回指定对象的哈希代码。</summary>
      <returns>指定对象的哈希代码。</returns>
      <param name="obj">将为其返回哈希代码的对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="obj" /> 的类型为引用类型，<paramref name="obj" /> 为 null。</exception>
    </member>
    <member name="T:System.UriBuilder">
      <summary>为统一资源标识符 (URI) 提供自定义构造函数，并修改 <see cref="T:System.Uri" /> 类的 URI。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.#ctor">
      <summary>初始化 <see cref="T:System.UriBuilder" /> 类的新实例。</summary>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String)">
      <summary>用指定的 URI 初始化 <see cref="T:System.UriBuilder" /> 类的新实例。</summary>
      <param name="uri">URI 字符串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> 为 null。</exception>
      <exception cref="T:System.UriFormatException">在 .NET for Windows Store 应用程序 或 可移植类库 中，请改为捕获基类异常 <see cref="T:System.FormatException" />。<paramref name="uri" /> 为零长度字符串或只包含空格。- 或 -分析例程检测到方案的格式无效。- 或 -分析器检测到不使用"文件"方案的 URI 中有多于两个的连续斜线。- 或 -<paramref name="uri" /> 不是有效的 URI。</exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String)">
      <summary>用指定的方案和主机初始化 <see cref="T:System.UriBuilder" /> 类的新实例。</summary>
      <param name="schemeName">Internet 访问协议。</param>
      <param name="hostName">DNS 样式的域名或 IP 地址。</param>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32)">
      <summary>用指定的方案、主机和端口初始化 <see cref="T:System.UriBuilder" /> 类的新实例。</summary>
      <param name="scheme">Internet 访问协议。</param>
      <param name="host">DNS 样式的域名或 IP 地址。</param>
      <param name="portNumber">服务的 IP 端口号。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="portNumber" /> 小于 -1 或大于 65,535。</exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String)">
      <summary>用指定的方案、主机、端口号和路径初始化 <see cref="T:System.UriBuilder" /> 类的新实例。</summary>
      <param name="scheme">Internet 访问协议。</param>
      <param name="host">DNS 样式的域名或 IP 地址。</param>
      <param name="port">服务的 IP 端口号。</param>
      <param name="pathValue">Internet 资源的路径。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> 小于 -1 或大于 65,535。</exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String,System.String)">
      <summary>用指定的方案、主机、端口号、路径和查询字符串或段标识符初始化 <see cref="T:System.UriBuilder" /> 类的新实例。</summary>
      <param name="scheme">Internet 访问协议。</param>
      <param name="host">DNS 样式的域名或 IP 地址。</param>
      <param name="port">服务的 IP 端口号。</param>
      <param name="path">Internet 资源的路径。</param>
      <param name="extraValue">查询字符串或段标识符。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="extraValue" /> 既不是 null 也不是 <see cref="F:System.String.Empty" />，既不是以井号 (#) 开始的有效段标识符，也不是以问号 (?) 开始的有效查询字符串。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> 小于 -1 或大于 65,535。</exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.Uri)">
      <summary>用指定的 <see cref="T:System.Uri" /> 实例初始化 <see cref="T:System.UriBuilder" /> 类的新实例。</summary>
      <param name="uri">
        <see cref="T:System.Uri" /> 类的实例。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> 为 null。</exception>
    </member>
    <member name="M:System.UriBuilder.Equals(System.Object)">
      <summary>比较现有 <see cref="T:System.Uri" /> 实例与 <see cref="T:System.UriBuilder" /> 的内容是否相等。</summary>
      <returns>如果 <paramref name="rparam" /> 表示的 <see cref="T:System.Uri" /> 与此 <see cref="T:System.UriBuilder" /> 实例构造的 <see cref="T:System.Uri" /> 相同，则为 true；否则为 false。</returns>
      <param name="rparam">要与当前实例进行比较的对象。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Fragment">
      <summary>获取或设置 URI 的段部分。</summary>
      <returns>URI 的段部分。段标识符 ("#") 添加到段的开头。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.GetHashCode">
      <summary>返回 URI 的哈希代码。</summary>
      <returns>为 URI 生成的哈希代码。</returns>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Host">
      <summary>获取或设置服务器的域名系统 (DNS) 主机名或 IP 地址。</summary>
      <returns>服务器的域名系统 (DNS) 主机名或 IP 地址。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Password">
      <summary>获取或设置与访问 URI 的用户关联的密码。</summary>
      <returns>访问 URI 的用户的密码。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Path">
      <summary>获取或设置 URI 引用的资源的路径。</summary>
      <returns>URI 引用的资源的路径。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Port">
      <summary>获取或设置 URI 的端口号。</summary>
      <returns>URI 的端口号。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">不能将此端口设为小于 1 或大于 65,535 的值。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Query">
      <summary>获取或设置 URI 中包括的任何查询信息。</summary>
      <returns>URI 中包括的查询信息。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Scheme">
      <summary>获取或设置 URI 的方案名称。</summary>
      <returns>URI 的方案。</returns>
      <exception cref="T:System.ArgumentException">不能将该方案设为无效的方案名称。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.UriBuilder.ToString">
      <summary>返回指定 <see cref="T:System.UriBuilder" /> 实例的显示字符串。</summary>
      <returns>包含 <see cref="T:System.UriBuilder" /> 的非转义显示字符串的字符串。</returns>
      <exception cref="T:System.UriFormatException">在 .NET for Windows Store 应用程序 或 可移植类库 中，请改为捕获基类异常 <see cref="T:System.FormatException" />。<see cref="T:System.UriBuilder" /> 实例有不良密码。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Uri">
      <summary>获取由指定 <see cref="T:System.UriBuilder" /> 实例构造的 <see cref="T:System.Uri" /> 实例。</summary>
      <returns>
        <see cref="T:System.Uri" />，包含由 <see cref="T:System.UriBuilder" /> 构造的 URI。</returns>
      <exception cref="T:System.UriFormatException">在 .NET for Windows Store 应用程序 或 可移植类库 中，请改为捕获基类异常 <see cref="T:System.FormatException" />。由 <see cref="T:System.UriBuilder" /> 属性构造的 URI 无效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.UserName">
      <summary>与访问 URI 的用户关联的用户名。</summary>
      <returns>访问 URI 的用户的用户名。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Diagnostics.Stopwatch">
      <summary>提供一组方法和属性，可用于准确地测量运行时间。若要浏览此类型的 .NET Framework 源代码，请参阅引用源。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.#ctor">
      <summary>初始化 <see cref="T:System.Diagnostics.Stopwatch" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.Elapsed">
      <summary>获取当前实例测量得出的总运行时间。</summary>
      <returns>一个只读的 <see cref="T:System.TimeSpan" />，表示当前实例测量得出的总运行时间。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedMilliseconds">
      <summary>获取当前实例测量得出的总运行时间（以毫秒为单位）。</summary>
      <returns>一个只读长整型，表示当前实例测量得出的总毫秒数。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedTicks">
      <summary>获取当前实例测量得出的总运行时间（用计时器刻度表示）。</summary>
      <returns>一个只读长整型，表示当前实例测量得出的计时器刻度总数。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.Frequency">
      <summary>获取以每秒刻度数表示的计时器频率。此字段为只读。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.GetTimestamp">
      <summary>获取计时器机制中的当前刻度数。</summary>
      <returns>一个长整型，表示基础计时器机制中的刻度计数器值。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.IsHighResolution">
      <summary>指示计时器是否基于高分辨率性能计数器。此字段为只读。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.IsRunning">
      <summary>获取一个值，该值指示 <see cref="T:System.Diagnostics.Stopwatch" /> 计时器是否正在运行。</summary>
      <returns>如果 <see cref="T:System.Diagnostics.Stopwatch" /> 实例当前正在运行，并且正在测量某个时间间隔的运行时间，则为 true；否则为 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Reset">
      <summary>停止时间间隔测量，并将运行时间重置为零。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Restart">
      <summary>停止时间间隔测量，将运行时间重置为零，然后开始测量运行时间。</summary>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Start">
      <summary>开始或继续测量某个时间间隔的运行时间。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.StartNew">
      <summary>初始化新的 <see cref="T:System.Diagnostics.Stopwatch" /> 实例，将运行时间属性设置为零，然后开始测量运行时间。</summary>
      <returns>刚刚开始测量运行时间的 <see cref="T:System.Diagnostics.Stopwatch" />。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Stop">
      <summary>停止测量某个时间间隔的运行时间。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.Path">
      <summary>对包含文件或目录路径信息的 <see cref="T:System.String" /> 实例执行操作。这些操作是以跨平台的方式执行的。若要浏览此类型的.NET Framework 源代码，请参阅 Reference Source。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.AltDirectorySeparatorChar">
      <summary>提供平台特定的替换字符，该替换字符用于在反映分层文件系统组织的路径字符串中分隔目录级别。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.ChangeExtension(System.String,System.String)">
      <summary>更改路径字符串的扩展名。</summary>
      <returns>已修改的路径信息。在基于 Windows 的桌面平台上，如果 <paramref name="path" /> 是 null 或空字符串 (“”)，则返回的路径信息是未修改的。如果 <paramref name="extension" /> 为 null，则返回的字符串包含指定的路径（其扩展名已移除）。如果 <paramref name="path" /> 不具有扩展名且 <paramref name="extension" /> 不为 null，则返回的路径字符串包含追加到 <paramref name="path" /> 结尾的 <paramref name="extension" />。</returns>
      <param name="path">要修改的路径信息。该路径不能包含在 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中定义的任何字符。</param>
      <param name="extension">新的扩展名（有或没有前导句点）。指定 null 以从 <paramref name="path" /> 移除现有扩展名。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 包含 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中已定义的一个或多个无效字符。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String)">
      <summary>将两个字符串组合成一个路径。</summary>
      <returns>已组合的路径。如果指定的路径之一是零长度字符串，则该方法返回其他路径。如果 <paramref name="path2" /> 包含绝对路径，则该方法返回 <paramref name="path2" />。</returns>
      <param name="path1">要组合的第一个路径。</param>
      <param name="path2">要组合的第二个路径。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path1" /> 或 <paramref name="path2" /> 包含 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中已定义的一个或多个无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path1" /> 或 <paramref name="path2" /> 为 null。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String,System.String)">
      <summary>将三个字符串组合成一个路径。</summary>
      <returns>已组合的路径。</returns>
      <param name="path1">要组合的第一个路径。</param>
      <param name="path2">要组合的第二个路径。</param>
      <param name="path3">要组合的第三个路径。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path1" />、<paramref name="path2" /> 或  <paramref name="path3" /> 包含 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中已定义的一个或多个无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path1" />、<paramref name="path2" /> 或 <paramref name="path3" /> 为 null。</exception>
    </member>
    <member name="M:System.IO.Path.Combine(System.String[])">
      <summary>将字符串数组组合成一个路径。</summary>
      <returns>已组合的路径。</returns>
      <param name="paths">由路径的各部分构成的数组。</param>
      <exception cref="T:System.ArgumentException">数组中的一个字符串包含 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中定义的一个或多个无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">数组中的一个字符串为 null。</exception>
    </member>
    <member name="F:System.IO.Path.DirectorySeparatorChar">
      <summary>提供平台特定的字符，该字符用于在反映分层文件系统组织的路径字符串中分隔目录级别。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetDirectoryName(System.String)">
      <summary>返回指定路径字符串的目录信息。</summary>
      <returns>
        <paramref name="path" /> 的目录信息；如果 <paramref name="path" /> 表示根目录或为 null，则为 null。如果 <paramref name="path" /> 不包含目录信息，则返回 <see cref="F:System.String.Empty" />。</returns>
      <param name="path">文件或目录的路径。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 参数包含无效字符、为空、或仅包含空白。</exception>
      <exception cref="T:System.IO.PathTooLongException">在 .NET for Windows Store apps 或 可移植类库, ，捕获该基类异常， <see cref="T:System.IO.IOException" />, 、 相反。<paramref name="path" /> 参数的长度超过系统定义的最大长度。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetExtension(System.String)">
      <summary>返回指定的路径字符串的扩展名。</summary>
      <returns>指定路径的扩展名（包含句点“.”）、或 null、或 <see cref="F:System.String.Empty" />。如果 <paramref name="path" /> 为 null，则 <see cref="M:System.IO.Path.GetExtension(System.String)" /> 返回 null。如果 <paramref name="path" /> 不具有扩展名信息，则 <see cref="M:System.IO.Path.GetExtension(System.String)" /> 返回 <see cref="F:System.String.Empty" />。</returns>
      <param name="path">从中获取扩展名的路径字符串。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 包含 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中已定义的一个或多个无效字符。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileName(System.String)">
      <summary>返回指定路径字符串的文件名和扩展名。</summary>
      <returns>
        <paramref name="path" /> 中最后一个目录字符后的字符。如果 <paramref name="path" /> 的最后一个字符是目录或卷分隔符，则此方法返回 <see cref="F:System.String.Empty" />。如果 <paramref name="path" /> 为 null，则此方法返回 null。</returns>
      <param name="path">从中获取文件名和扩展名的路径字符串。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 包含 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中已定义的一个或多个无效字符。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileNameWithoutExtension(System.String)">
      <summary>返回不具有扩展名的指定路径字符串的文件名。</summary>
      <returns>由 <see cref="M:System.IO.Path.GetFileName(System.String)" /> 返回的字符串，但不包括最后的句点 (.) 以及之后的所有字符。</returns>
      <param name="path">文件的路径。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 包含 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中已定义的一个或多个无效字符。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFullPath(System.String)">
      <summary>返回指定路径字符串的绝对路径。</summary>
      <returns>
        <paramref name="path" /> 的完全限定的位置，例如“C:\MyFile.txt”。</returns>
      <param name="path">要获取其绝对路径信息的文件或目录。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中已定义一个或多个无效字符。- 或 - 系统未能检索绝对路径。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所需的权限。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 包含一个冒号（“:”），此冒号不是卷标识符（如，“c:\”）的一部分。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetInvalidFileNameChars">
      <summary>获取包含不允许在文件名中使用的字符的数组。</summary>
      <returns>包含不允许在文件名中使用的字符的数组。</returns>
    </member>
    <member name="M:System.IO.Path.GetInvalidPathChars">
      <summary>获取包含不允许在路径名中使用的字符的数组。</summary>
      <returns>包含不允许在路径名中使用的字符的数组。</returns>
    </member>
    <member name="M:System.IO.Path.GetPathRoot(System.String)">
      <summary>获取指定路径的根目录信息。</summary>
      <returns>
        <paramref name="path" /> 的根目录，例如“C:\”；如果 <paramref name="path" /> 为 null，则为 null；如果 <paramref name="path" /> 不包含根目录信息，则为空字符串。</returns>
      <param name="path">从中获取根目录信息的路径。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 包含 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中已定义的一个或多个无效字符。- 或 - <see cref="F:System.String.Empty" /> 被传递到 <paramref name="path" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetRandomFileName">
      <summary>返回随机文件夹名或文件名。</summary>
      <returns>随机文件夹名或文件名。</returns>
    </member>
    <member name="M:System.IO.Path.GetTempFileName">
      <summary>在磁盘上创建磁唯一命名的零字节的临时文件并返回该文件的完整路径。</summary>
      <returns>临时文件的完整路径。</returns>
      <exception cref="T:System.IO.IOException">发生 I/O 错误，例如没有提供唯一的临时文件名。- 或 -此方法无法创建临时文件。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetTempPath">
      <summary>返回当前用户的临时文件夹的路径。</summary>
      <returns>临时文件夹的路径，以反斜杠结尾。</returns>
      <exception cref="T:System.Security.SecurityException">调用方没有所需的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.HasExtension(System.String)">
      <summary>确定路径是否包括文件扩展名。</summary>
      <returns>如果路径中最后一个目录分隔符（\\ 或 /）或卷分隔符 (:) 之后的字符包括句点 (.)，并且后面跟有一个或多个字符，则为 true；否则为 false。</returns>
      <param name="path">用于搜索扩展名的路径。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 包含 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中已定义的一个或多个无效字符。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.IsPathRooted(System.String)">
      <summary>获取一个值，该值指示指定的路径字符串是否包含根。</summary>
      <returns>如果 <paramref name="path" /> 包含一个根，则为 true；否则为 false。</returns>
      <param name="path">要测试的路径。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 包含 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中已定义的一个或多个无效字符。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.PathSeparator">
      <summary>用于在环境变量中分隔路径字符串的平台特定的分隔符。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.VolumeSeparatorChar">
      <summary>提供平台特定的卷分隔符。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Net.WebUtility">
      <summary>提供用于在处理 Web 请求时编码和解码 URL 的方法。</summary>
    </member>
    <member name="M:System.Net.WebUtility.HtmlDecode(System.String)">
      <summary>将已经为 HTTP 传输进行过 HTML 编码的字符串转换为已解码的字符串。</summary>
      <returns>一个已解码的字符串。</returns>
      <param name="value">要解码的字符串。</param>
    </member>
    <member name="M:System.Net.WebUtility.HtmlEncode(System.String)">
      <summary>将字符串转换为 HTML 编码的字符串。</summary>
      <returns>编码的字符串。</returns>
      <param name="value">要编码的字符串。</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecode(System.String)">
      <summary>将已经为在 URL 中传输而编码的字符串转换为解码的字符串。</summary>
      <returns>返回 <see cref="T:System.String" />。一个已解码的字符串。</returns>
      <param name="encodedValue">要进行解码的 URL 编码的字符串。</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>将为通过 URL 传输已编码的已编码字节数组转换为解码的字节数组。</summary>
      <returns>返回 <see cref="T:System.Byte" />。一个已解码的 <see cref="T:System.Byte" /> 数组。</returns>
      <param name="encodedValue">要进行解码的 URL 编码的 <see cref="T:System.Byte" /> 数组。</param>
      <param name="offset">相对于要解码的 <see cref="T:System.Byte" /> 数组的开头的偏移量（以字节为单位）。</param>
      <param name="count">要从 <see cref="T:System.Byte" /> 数组解码的计数（以字节为单位）。</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncode(System.String)">
      <summary>将文本字符串转换为 URL 编码的字符串。</summary>
      <returns>返回 <see cref="T:System.String" />。URL 编码的字符串。</returns>
      <param name="value">要进行 URL 编码的文本。</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>将字节数组转换为 URL 编码的字节数组。</summary>
      <returns>返回 <see cref="T:System.Byte" />。编码的 <see cref="T:System.Byte" /> 数组。</returns>
      <param name="value">要进行 URL 编码的 <see cref="T:System.Byte" /> 数组。</param>
      <param name="offset">相对于要编码的 <see cref="T:System.Byte" /> 数组的开头的偏移量（以字节为单位）。</param>
      <param name="count">要从 <see cref="T:System.Byte" /> 数组编码的计数（以字节为单位）。</param>
    </member>
    <member name="T:System.Runtime.Versioning.FrameworkName">
      <summary>表示 .NET Framework 的版本的名称。</summary>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String)">
      <summary>从包含 .NET Framework 版本信息的字符串初始化 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 类的新实例。</summary>
      <param name="frameworkName">包含 .NET Framework 版本信息的字符串。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="frameworkName" /> 为 <see cref="F:System.String.Empty" />。- 或 -<paramref name="frameworkName" /> 有少于两个组件或多于三个组件。- 或 -<paramref name="frameworkName" /> 中未包括主要版本号和次要版本号。- 或 -<paramref name="frameworkName " /> 中未包括有效版本号。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="frameworkName" /> 为 null。</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version)">
      <summary>从标识 .NET Framework 版本的字符串和 <see cref="T:System.Version" /> 对象初始化 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 类的新实例。</summary>
      <param name="identifier">标识 .NET Framework 版本的字符串。</param>
      <param name="version">包含 .NET Framework 版本信息的对象。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identifier" /> 为 <see cref="F:System.String.Empty" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" /> 为 null。- 或 -<paramref name="version" /> 为 null。</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version,System.String)">
      <summary>从字符串、标识 .NET Framework 版本的 <see cref="T:System.Version" /> 对象以及配置文件名称初始化 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 类的新实例。</summary>
      <param name="identifier">标识 .NET Framework 版本的字符串。</param>
      <param name="version">包含 .NET Framework 版本信息的对象。</param>
      <param name="profile">配置文件名称。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identifier" /> 为 <see cref="F:System.String.Empty" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" /> 为 null。- 或 -<paramref name="version" /> 为 null。</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Object)">
      <summary>返回一个值，该值指示此 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 实例是否表示与指定的对象相同的 .NET Framework 版本。</summary>
      <returns>如果当前 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 对象的每个部分都与 <paramref name="obj" /> 的相应部分匹配，则为 true；否则为 false。</returns>
      <param name="obj">要与当前实例进行比较的对象。</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Runtime.Versioning.FrameworkName)">
      <summary>返回一个值，该值指示此 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 实例是否表示与指定的 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 实例相同的 .NET Framework 版本。</summary>
      <returns>如果当前 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 对象的每个部分都与 <paramref name="other" /> 的相应部分匹配，则为 true；否则为 false。</returns>
      <param name="other">要与当前实例进行比较的对象。</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.FullName">
      <summary>获取此 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 对象的全名。</summary>
      <returns>此 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 对象的全名。</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.GetHashCode">
      <summary>返回 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 对象的哈希代码。</summary>
      <returns>一个 32 位带符号整数，表示此实例的哈希代码。</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Identifier">
      <summary>获取此 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 对象的标识符。</summary>
      <returns>此 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 对象的标识符。</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Equality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>返回一个值，该值指示两个 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 对象是否表示相同的 .NET Framework 版本。</summary>
      <returns>如果 <paramref name="left" /> 和 <paramref name="right" /> 参数表示同一 .NET Framework 版本，则为 true；否则为 false。</returns>
      <param name="left">要比较的第一个对象。</param>
      <param name="right">要比较的第二个对象。</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Inequality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>返回一个值，该值指示两个 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 对象是否表示不同的 .NET Framework 版本。</summary>
      <returns>如果 <paramref name="left" /> 和 <paramref name="right" /> 参数表示不同的 .NET Framework 版本，则为 true；否则为 false。</returns>
      <param name="left">要比较的第一个对象。</param>
      <param name="right">要比较的第二个对象。</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Profile">
      <summary>获取此 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 对象的配置文件名称。</summary>
      <returns>此 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 对象的配置文件名称。</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.ToString">
      <summary>返回此 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 对象的字符串表示形式。</summary>
      <returns>表示此 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 对象的字符串。</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Version">
      <summary>获取此 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 对象的版本。</summary>
      <returns>包含此 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 对象的版本信息的对象。</returns>
    </member>
  </members>
</doc>