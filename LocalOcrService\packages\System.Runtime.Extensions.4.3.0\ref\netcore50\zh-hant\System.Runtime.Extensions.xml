﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.Extensions</name>
  </assembly>
  <members>
    <member name="T:System.BitConverter">
      <summary>將基底資料型別與位元組陣列相互轉換。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.BitConverter.DoubleToInt64Bits(System.Double)">
      <summary>將指定的雙精確度浮點數轉換為 64 位元帶正負號的整數。</summary>
      <returns>64 位元帶正負號之整數的值等於 <paramref name="value" />。</returns>
      <param name="value">要進行轉換的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Boolean)">
      <summary>傳回指定的布林值為位元組陣列。</summary>
      <returns>長度為 1 的位元組陣列。</returns>
      <param name="value">布林值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Char)">
      <summary>傳回指定的 Unicode 位元值為位元組陣列。</summary>
      <returns>長度為 2 的位元組陣列。</returns>
      <param name="value">要進行轉換的字元。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Double)">
      <summary>傳回指定的雙精確度浮點數值為位元組陣列。</summary>
      <returns>長度為 8 的位元組陣列。</returns>
      <param name="value">要進行轉換的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int16)">
      <summary>傳回指定的 16 位元帶正負號的整數值為位元組陣列。</summary>
      <returns>長度為 2 的位元組陣列。</returns>
      <param name="value">要進行轉換的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int32)">
      <summary>傳回指定的 32 位元帶正負號的整數值為位元組陣列。</summary>
      <returns>長度為 4 的位元組陣列。</returns>
      <param name="value">要進行轉換的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Int64)">
      <summary>傳回指定的 64 位元帶正負號的整數值為位元組陣列。</summary>
      <returns>長度為 8 的位元組陣列。</returns>
      <param name="value">要進行轉換的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.Single)">
      <summary>傳回指定的單精確度浮點數值為位元組陣列。</summary>
      <returns>長度為 4 的位元組陣列。</returns>
      <param name="value">要進行轉換的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt16)">
      <summary>傳回指定的 16 位元不帶正負號的整數值為位元組陣列。</summary>
      <returns>長度為 2 的位元組陣列。</returns>
      <param name="value">要進行轉換的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt32)">
      <summary>傳回指定的 32 位元不帶正負號的整數值為位元組陣列。</summary>
      <returns>長度為 4 的位元組陣列。</returns>
      <param name="value">要進行轉換的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.GetBytes(System.UInt64)">
      <summary>傳回指定的 64 位元不帶正負號的整數值為位元組陣列。</summary>
      <returns>長度為 8 的位元組陣列。</returns>
      <param name="value">要進行轉換的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.Int64BitsToDouble(System.Int64)">
      <summary>將指定的 64 位元帶正負號整數轉換為雙精確度浮點數。</summary>
      <returns>雙精確度浮點數的值等於 <paramref name="value" />。</returns>
      <param name="value">要進行轉換的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.BitConverter.IsLittleEndian">
      <summary>指示資料存放在這個電腦架構中的位元組順序 ("Endianness")。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToBoolean(System.Byte[],System.Int32)">
      <summary>傳回從位元組陣列中指定位置的一個位元組所轉換的布林值。</summary>
      <returns>如果在 <paramref name="value" /> 中 <paramref name="startIndex" /> 的位元組為非零值 (Nonzero)，則為 true，否則為 false。</returns>
      <param name="value">位元組陣列。</param>
      <param name="startIndex">起始位置在 <paramref name="value" /> 內。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小於零或大於 <paramref name="value" /> 的長度減去 1。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToChar(System.Byte[],System.Int32)">
      <summary>傳回從位元組陣列中指定位置的兩個位元組所轉換的 Unicode 字元。</summary>
      <returns>由兩個位元組所形成的字元開始於 <paramref name="startIndex" />。</returns>
      <param name="value">陣列。</param>
      <param name="startIndex">起始位置在 <paramref name="value" /> 內。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 等於 <paramref name="value" /> 的長度減去 1。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小於零或大於 <paramref name="value" /> 的長度減去 1。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToDouble(System.Byte[],System.Int32)">
      <summary>傳回從位元組陣列中指定位置的八個位元組所轉換的雙精確度浮點數。</summary>
      <returns>由八個位元組所形成的雙精確度浮點數開始於 <paramref name="startIndex" />。</returns>
      <param name="value">位元組陣列。</param>
      <param name="startIndex">起始位置在 <paramref name="value" /> 內。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 大於或等於 <paramref name="value" /> 的長度減去 7，而且小於或等於 <paramref name="value" /> 的長度減去 1。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小於零或大於 <paramref name="value" /> 的長度減去 1。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt16(System.Byte[],System.Int32)">
      <summary>傳回從位元組陣列中指定位置的兩個位元組所轉換的 16 位元帶正負號的整數 (Signed Integer)。</summary>
      <returns>由兩個位元組所形成的 16 位元帶正負號的整數開始於 <paramref name="startIndex" />。</returns>
      <param name="value">位元組陣列。</param>
      <param name="startIndex">起始位置在 <paramref name="value" /> 內。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 等於 <paramref name="value" /> 的長度減去 1。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小於零或大於 <paramref name="value" /> 的長度減去 1。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt32(System.Byte[],System.Int32)">
      <summary>傳回從位元組陣列中指定位置的四個位元組所轉換的 32 位元帶正負號的整數。</summary>
      <returns>由四個位元組所形成的 32 位元帶正負號的整數 (Signed Integer)，開始於 <paramref name="startIndex" />。</returns>
      <param name="value">位元組陣列。</param>
      <param name="startIndex">起始位置在 <paramref name="value" /> 內。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 大於或等於 <paramref name="value" /> 的長度減去 3，而且小於或等於 <paramref name="value" /> 的長度減去 1。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小於零或大於 <paramref name="value" /> 的長度減去 1。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToInt64(System.Byte[],System.Int32)">
      <summary>傳回從位元組陣列中指定位置的八個位元組所轉換的 64 位元帶正負號的整數。</summary>
      <returns>由八個位元組所形成的 64 位元帶正負號的整數開始於 <paramref name="startIndex" />。</returns>
      <param name="value">位元組陣列。</param>
      <param name="startIndex">起始位置在 <paramref name="value" /> 內。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 大於或等於 <paramref name="value" /> 的長度減去 7，而且小於或等於 <paramref name="value" /> 的長度減去 1。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小於零或大於 <paramref name="value" /> 的長度減去 1。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToSingle(System.Byte[],System.Int32)">
      <summary>傳回從位元組陣列中指定位置的四個位元組所轉換的單精確度浮點數。</summary>
      <returns>由四個位元組所形成的單精確度浮點數開始於 <paramref name="startIndex" />。</returns>
      <param name="value">位元組陣列。</param>
      <param name="startIndex">起始位置在 <paramref name="value" /> 內。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 大於或等於 <paramref name="value" /> 的長度減去 3，而且小於或等於 <paramref name="value" /> 的長度減去 1。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小於零或大於 <paramref name="value" /> 的長度減去 1。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[])">
      <summary>將指定之位元組陣列的每一個元素之數值轉換成其對等的十六進位字串表示。</summary>
      <returns>一對十六進位的字串是以連字號做為分隔，其中每對各表示 <paramref name="value" /> 中對應的元素，例如 "7F-2C-4A-00"。</returns>
      <param name="value">位元組陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 為 null。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32)">
      <summary>將指定之位元組子陣列的每一個元素之數值轉換成其對等的十六進位字串表示。</summary>
      <returns>一對十六進位字串是以連字號做為分隔符號，其中每對分別表示 <paramref name="value" /> 子陣列中對應的元素，例如 "7F-2C-4A-00"。</returns>
      <param name="value">位元組陣列。</param>
      <param name="startIndex">起始位置在 <paramref name="value" /> 內。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小於零或大於 <paramref name="value" /> 的長度減去 1。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToString(System.Byte[],System.Int32,System.Int32)">
      <summary>將指定之位元組子陣列的每一個元素之數值轉換成其對等的十六進位字串表示。</summary>
      <returns>一對十六進位字串是以連字號做為分隔符號，其中每對分別表示 <paramref name="value" /> 子陣列中對應的元素，例如 "7F-2C-4A-00"。</returns>
      <param name="value">位元組陣列。</param>
      <param name="startIndex">起始位置在 <paramref name="value" /> 內。</param>
      <param name="length">
        <paramref name="value" /> 中要轉換的陣列元素數目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 或 <paramref name="length" /> 小於零。-或-<paramref name="startIndex" /> 大於零，且會大於或等於 <paramref name="value" /> 的長度。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 和 <paramref name="length" /> 的組合不會指定 <paramref name="value" /> 內的位置，也就是說，<paramref name="startIndex" /> 參數會大於 <paramref name="value" /> 的長度減去 <paramref name="length" /> 參數。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt16(System.Byte[],System.Int32)">
      <summary>傳回從位元組陣列中指定位置的兩個位元組所轉換的 16 位元不帶正負號的整數 (Unsigned Integer)。</summary>
      <returns>由兩個位元組所形成的 16 位元不帶正負號的整數開始於 <paramref name="startIndex" />。</returns>
      <param name="value">位元組陣列。</param>
      <param name="startIndex">起始位置在 <paramref name="value" /> 內。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 等於 <paramref name="value" /> 的長度減去 1。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小於零或大於 <paramref name="value" /> 的長度減去 1。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt32(System.Byte[],System.Int32)">
      <summary>傳回從位元組陣列中指定位置的四個位元組所轉換的 32 位元不帶正負號的整數。</summary>
      <returns>由四個位元組所形成的 32 位元不帶正負號的整數開始於 <paramref name="startIndex" />。</returns>
      <param name="value">位元組陣列。</param>
      <param name="startIndex">起始位置在 <paramref name="value" /> 內。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 大於或等於 <paramref name="value" /> 的長度減去 3，而且小於或等於 <paramref name="value" /> 的長度減去 1。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小於零或大於 <paramref name="value" /> 的長度減去 1。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.BitConverter.ToUInt64(System.Byte[],System.Int32)">
      <summary>傳回從位元組陣列中指定位置的八個位元組所轉換的 64 位元不帶正負號的整數。</summary>
      <returns>由八個位元組所形成的 64 位元不帶正負號的整數開始於 <paramref name="startIndex" />。</returns>
      <param name="value">位元組陣列。</param>
      <param name="startIndex">起始位置在 <paramref name="value" /> 內。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="startIndex" /> 大於或等於 <paramref name="value" /> 的長度減去 7，而且小於或等於 <paramref name="value" /> 的長度減去 1。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 小於零或大於 <paramref name="value" /> 的長度減去 1。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Convert">
      <summary>將基底資料類型轉換為其他基底資料類型。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type)">
      <summary>傳回指定之類型的物件，此物件的值與指定的物件相等。</summary>
      <returns>物件，它的型別為 <paramref name="conversionType" />，且它的值與 <paramref name="value" /> 相等。-或-如果 <paramref name="value" /> 是 null 而且 <paramref name="conversionType" /> 不是實值型別，則為 null 參考 (在 Visual Basic 中為 Nothing)。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件。</param>
      <param name="conversionType">要傳回的物件類型。</param>
      <exception cref="T:System.InvalidCastException">不支援這個轉換。-或-<paramref name="value" /> 是 null 且 <paramref name="conversionType" /> 是實值型別。-或-<paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是 <paramref name="conversionType" /> 可辨認的格式。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示超出 <paramref name="conversionType" /> 範圍的數字。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="conversionType" /> 為 null。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.Type,System.IFormatProvider)">
      <summary>傳回物件，該物件屬於指定的類型，且其值等於指定的物件。參數提供特定文化特性格式資訊。</summary>
      <returns>物件，它的型別為 <paramref name="conversionType" />，且它的值與 <paramref name="value" /> 相等。-或- 如果 <paramref name="value" /> 的 <see cref="T:System.Type" /> 和 <paramref name="conversionType" /> 相等，則為 <paramref name="value" />。-或-如果 <paramref name="value" /> 是 null 而且 <paramref name="conversionType" /> 不是實值型別，則為 null 參考 (在 Visual Basic 中為 Nothing)。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件。</param>
      <param name="conversionType">要傳回的物件類型。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.InvalidCastException">不支援這個轉換。-或-<paramref name="value" /> 是 null 且 <paramref name="conversionType" /> 是實值型別。-或-<paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是可由 <paramref name="provider" /> 辦識之 <paramref name="conversionType" /> 的格式。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示超出 <paramref name="conversionType" /> 範圍的數字。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="conversionType" /> 為 null。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ChangeType(System.Object,System.TypeCode,System.IFormatProvider)">
      <summary>傳回物件，該物件屬於指定的類型，且其值等於指定的物件。參數提供特定文化特性格式資訊。</summary>
      <returns>物件，它的基礎型別為 <paramref name="typeCode" />，且它的值與 <paramref name="value" /> 相等。-或-如果 <paramref name="value" /> 是 null，而且 <paramref name="typeCode" /> 是 <see cref="F:System.TypeCode.Empty" />、<see cref="F:System.TypeCode.String" /> 或 <see cref="F:System.TypeCode.Object" />，則為 null 參考 (在 Visual Basic 中為 Nothing)。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件。</param>
      <param name="typeCode">要傳回的物件類型。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.InvalidCastException">不支援這個轉換。-或-<paramref name="value" /> 是 null 且 <paramref name="typeCode" /> 指定了實值型別。-或-<paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是可由 <paramref name="provider" /> 辦識之 <paramref name="typeCode" /> 型別的格式。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示超出 <paramref name="typeCode" /> 型別範圍的數字。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="typeCode" /> 無效。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64CharArray(System.Char[],System.Int32,System.Int32)">
      <summary>將 Unicode 字元陣列 (將二進位資料編碼為 Base-64 位數) 的子集轉換為相等的 8 位元不帶正負號的整數陣列。參數會指定輸入陣列中的子集，以及要轉換的項目個數。</summary>
      <returns>8 位元不帶正負號的整數陣列，與 <paramref name="inArray" /> 中 <paramref name="offset" /> 位置的 <paramref name="length" /> 元素相等。</returns>
      <param name="inArray">Unicode 字元陣列。</param>
      <param name="offset">
        <paramref name="inArray" /> 中的位置。</param>
      <param name="length">
        <paramref name="inArray" /> 中要轉換的元素數目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> 為 null。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 或 <paramref name="length" /> 小於 0-或- <paramref name="offset" /> 加上 <paramref name="length" /> 指示不在 <paramref name="inArray" /> 內的位置。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="inArray" /> 除了空白字元之外的長度不會是零或 4 的倍數。-或-<paramref name="inArray" /> 格式無效。<paramref name="inArray" /> 包含一個 Base 64 的字元、兩個以上的填補字元或一個位於填補字元之間的非泛空白字元。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.FromBase64String(System.String)">
      <summary>將指定的字串 (將二進位資料編碼為 Base-64 位數) 轉換為相等的 8 位元不帶正負號的整數陣列。</summary>
      <returns>8 位元不帶正負號的整數且與 <paramref name="s" /> 相等的陣列。</returns>
      <param name="s">要轉換的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> 為 null。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> 除了空白字元之外的長度不會是零或 4 的倍數。-或-<paramref name="s" /> 格式無效。<paramref name="s" /> 包含一個非 Base 64 的字元、兩個以上的填補字元或一個位於填補字元間的非泛空白字元。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.GetTypeCode(System.Object)">
      <summary>傳回指定物件的 <see cref="T:System.TypeCode" />。</summary>
      <returns>
        <paramref name="value" /> 的 <see cref="T:System.TypeCode" />；如果 <paramref name="value" /> 為 null，則為 <see cref="F:System.TypeCode.Empty" />。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64CharArray(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32)">
      <summary>將 8 位元不帶正負號的整數陣列的子集，轉換為相等的 Base-64 位數編碼的 Unicode 字元陣列子集。參數會將子集指定為輸入和輸出陣列中的位移，以及轉換輸入陣列中的項目數目。</summary>
      <returns>32 位元帶正負號的整數 (Signed Integer)，包含 <paramref name="outArray" /> 中的位元數。</returns>
      <param name="inArray">8 位元不帶正負號的整數的輸入陣列。</param>
      <param name="offsetIn">
        <paramref name="inArray" /> 中的位置。</param>
      <param name="length">要轉換的 <paramref name="inArray" /> 元素數目。</param>
      <param name="outArray">Unicode 字元的輸出陣列。</param>
      <param name="offsetOut">
        <paramref name="outArray" /> 中的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> 或 <paramref name="outArray" /> 為 null。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offsetIn" />、<paramref name="offsetOut" /> 或 <paramref name="length" /> 為負值。-或- <paramref name="offsetIn" /> 加上 <paramref name="length" /> 大於 <paramref name="inArray" /> 的長度。-或- <paramref name="offsetOut" /> 加上要傳回的元素數目大於 <paramref name="outArray" /> 的長度。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[])">
      <summary>將 8 位元不帶正負號的整數陣列，轉換為使用 Base-64 位數編碼的相等字串表示。</summary>
      <returns>
        <paramref name="inArray" /> 之內容的字串表示 (格式為 Base 64)。</returns>
      <param name="inArray">8 位元不帶正負號的整數的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> 為 null。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBase64String(System.Byte[],System.Int32,System.Int32)">
      <summary>將 8 位元不帶正負號的整數陣列子集，轉換為使用 Base-64 位數編碼的相等字串表示。參數會將子集指定為輸入陣列中的位移 (Offset)，以及所要轉換陣列中項目的個數。</summary>
      <returns>
        <paramref name="inArray" /> 中開始於位置 <paramref name="offset" /> 之 <paramref name="length" /> 元素的字串表示 (格式為 Base 64)。</returns>
      <param name="inArray">8 位元不帶正負號的整數的陣列。</param>
      <param name="offset">
        <paramref name="inArray" /> 中的位移。</param>
      <param name="length">要轉換的 <paramref name="inArray" /> 元素數目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inArray" /> 為 null。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 或 <paramref name="length" /> 為負值。-或- <paramref name="offset" /> 加上 <paramref name="length" /> 大於 <paramref name="inArray" /> 的長度。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Boolean)">
      <summary>傳回指定的布林值 (Boolean)；不會執行實際的轉換。</summary>
      <returns>傳回未變更的 <paramref name="value" />。</returns>
      <param name="value">要傳回的布林值。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Byte)">
      <summary>將指定的 8 位元不帶正負號整數的值，轉換為相等的布林值。</summary>
      <returns>如果 <paramref name="value" /> 不是零，則為 true，否則為 false。</returns>
      <param name="value">要轉換之 8 位元不帶正負號的整數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Decimal)">
      <summary>將指定之十進位數字的值，轉換為相等的布林值。</summary>
      <returns>如果 <paramref name="value" /> 不是零，則為 true，否則為 false。</returns>
      <param name="value">要轉換的數字。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Double)">
      <summary>將指定之雙精確度浮點數的值，轉換為相等的布林值。</summary>
      <returns>如果 <paramref name="value" /> 不是零，則為 true，否則為 false。</returns>
      <param name="value">要轉換的雙精確度浮點數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int16)">
      <summary>將指定的 16 位元帶正負號整數的值，轉換為相等的布林值。</summary>
      <returns>如果 <paramref name="value" /> 不是零，則為 true，否則為 false。</returns>
      <param name="value">要轉換的 16 位元帶正負號的整數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int32)">
      <summary>將指定的 32 位元帶正負號整數的值，轉換為相等的布林值。</summary>
      <returns>如果 <paramref name="value" /> 不是零，則為 true，否則為 false。</returns>
      <param name="value">要轉換的 32 位元帶正負號的整數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Int64)">
      <summary>將指定的 64 位元帶正負號整數的值，轉換為相等的布林值。</summary>
      <returns>如果 <paramref name="value" /> 不是零，則為 true，否則為 false。</returns>
      <param name="value">要轉換的 64 位元帶正負號的整數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object)">
      <summary>將指定之物件的值轉換為相等的布林值。</summary>
      <returns>true 或 false，會反映針對 <paramref name="value" /> 的基礎型別叫用 <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" /> 方法所傳回的值。如果 <paramref name="value" /> 是 null，則方法會傳回 false。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件，或是 null。 </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 是一個不等於 <see cref="F:System.Boolean.TrueString" /> 或 <see cref="F:System.Boolean.FalseString" /> 的字串。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援將  <paramref name="value" /> 轉換為 <see cref="T:System.Boolean" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Object,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之物件的值轉換為相等的布林值。</summary>
      <returns>true 或 false，會反映針對 <paramref name="value" /> 的基礎型別叫用 <see cref="M:System.IConvertible.ToBoolean(System.IFormatProvider)" /> 方法所傳回的值。如果 <paramref name="value" /> 是 null，則方法會傳回 false。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件，或是 null。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。 </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 是一個不等於 <see cref="F:System.Boolean.TrueString" /> 或 <see cref="F:System.Boolean.FalseString" /> 的字串。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援將  <paramref name="value" /> 轉換為 <see cref="T:System.Boolean" />。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.SByte)">
      <summary>將指定的 8 位元帶正負號整數的值，轉換為相等的布林值。</summary>
      <returns>如果 <paramref name="value" /> 不是零，則為 true，否則為 false。</returns>
      <param name="value">要轉換的 8 位元帶正負號的整數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.Single)">
      <summary>將指定之單精確度浮點數的值，轉換為相等的布林值。</summary>
      <returns>如果 <paramref name="value" /> 不是零，則為 true，否則為 false。</returns>
      <param name="value">要轉換的單精確度浮點數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String)">
      <summary>將指定之邏輯值的字串表示，轉換為相等的布林值。</summary>
      <returns>當 <paramref name="value" /> 等於 <see cref="F:System.Boolean.TrueString" /> 時，為 true，否則，當 <paramref name="value" /> 等於 <see cref="F:System.Boolean.FalseString" /> 或 null 時，則為 false。</returns>
      <param name="value">字串，包含 <see cref="F:System.Boolean.TrueString" /> 或 <see cref="F:System.Boolean.FalseString" /> 的值。 </param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不等於 <see cref="F:System.Boolean.TrueString" /> 或 <see cref="F:System.Boolean.FalseString" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.String,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之邏輯值的字串表示轉換為相等的布林值。</summary>
      <returns>true if <paramref name="value" /> equals <see cref="F:System.Boolean.TrueString" />, or false if <paramref name="value" /> equals <see cref="F:System.Boolean.FalseString" /> or null.</returns>
      <param name="value">字串，包含 <see cref="F:System.Boolean.TrueString" /> 或 <see cref="F:System.Boolean.FalseString" /> 的值。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。這個參數已忽略。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不等於 <see cref="F:System.Boolean.TrueString" /> 或 <see cref="F:System.Boolean.FalseString" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt16)">
      <summary>將指定的 16 位元不帶正負號整數的值，轉換為相等的布林值。</summary>
      <returns>如果 <paramref name="value" /> 不是零，則為 true，否則為 false。</returns>
      <param name="value">要轉換之 16 位元不帶正負號的整數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt32)">
      <summary>將指定的 32 位元不帶正負號整數的值，轉換為相等的布林值。</summary>
      <returns>如果 <paramref name="value" /> 不是零，則為 true，否則為 false。</returns>
      <param name="value">要轉換之 32 位元不帶正負號的整數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToBoolean(System.UInt64)">
      <summary>將指定的 64 位元不帶正負號整數的值，轉換為相等的布林值。</summary>
      <returns>如果 <paramref name="value" /> 不是零，則為 true，否則為 false。</returns>
      <param name="value">要轉換之 64 位元不帶正負號的整數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Boolean)">
      <summary>將指定的布林值轉換為相等的 8 位元不帶正負號的整數。</summary>
      <returns>如果 <paramref name="value" /> 為 true，則為數字 1，否則為 0。</returns>
      <param name="value">要轉換的布林值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Byte)">
      <summary>傳回指定的 8 位元不帶正負號整數；不會執行實際的轉換。</summary>
      <returns>傳回未變更的 <paramref name="value" />。</returns>
      <param name="value">要傳回之 8 位元不帶正負號的整數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Char)">
      <summary>將指定的 Unicode 字元值轉換為相等的 8 位元不帶正負號的整數。</summary>
      <returns>8 位元不帶正負號的整數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換的 Unicode 字元。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 代表一個大於 <see cref="F:System.Byte.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Decimal)">
      <summary>將指定之十進位數字的值，轉換為相等的 8 位元不帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 8 位元不帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的數字。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Byte.MaxValue" /> 或小於 <see cref="F:System.Byte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Double)">
      <summary>將指定之雙精確度浮點數的值，轉換為相等的 8 位元不帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 8 位元不帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的雙精確度浮點數。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Byte.MaxValue" /> 或小於 <see cref="F:System.Byte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int16)">
      <summary>將指定的 16 位元帶正負號的整數值轉換為相等的 8 位元不帶正負號的整數。</summary>
      <returns>8 位元不帶正負號的整數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換的 16 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於 <see cref="F:System.Byte.MinValue" /> 或大於 <see cref="F:System.Byte.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int32)">
      <summary>將指定的 32 位元帶正負號的整數值轉換為相等的 8 位元不帶正負號的整數。</summary>
      <returns>8 位元不帶正負號的整數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換的 32 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於 <see cref="F:System.Byte.MinValue" /> 或大於 <see cref="F:System.Byte.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Int64)">
      <summary>將指定的 64 位元帶正負號的整數值轉換為相等的 8 位元不帶正負號的整數。</summary>
      <returns>8 位元不帶正負號的整數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換的 64 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於 <see cref="F:System.Byte.MinValue" /> 或大於 <see cref="F:System.Byte.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object)">
      <summary>將指定之物件的值轉換為 8 位元不帶正負號的整數。</summary>
      <returns>8 位元不帶正負號的整數，相當於 <paramref name="value" />，或者如果 <paramref name="value" /> 為 null 時，則為零。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件，或是 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是使用 <see cref="T:System.Byte" /> 值的屬性格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 沒有實作 <see cref="T:System.IConvertible" />。-或-不支援從 <paramref name="value" /> 轉換為 <see cref="T:System.Byte" /> 型別。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Byte.MinValue" /> 或大於 <see cref="F:System.Byte.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Object,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之物件的值轉換為 8 位元不帶正負號的整數。</summary>
      <returns>8 位元不帶正負號的整數，相當於 <paramref name="value" />，或者如果 <paramref name="value" /> 為 null 時，則為零。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是使用 <see cref="T:System.Byte" /> 值的屬性格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 沒有實作 <see cref="T:System.IConvertible" />。-或-不支援從 <paramref name="value" /> 轉換為 <see cref="T:System.Byte" /> 型別。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Byte.MinValue" /> 或大於 <see cref="F:System.Byte.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.SByte)">
      <summary>將指定的 8 位元帶正負號的整數值轉換為相等的 8 位元不帶正負號的整數。</summary>
      <returns>8 位元不帶正負號的整數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換的 8 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於 <see cref="F:System.Byte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.Single)">
      <summary>將指定之單精確度浮點數的值，轉換為相等的 8 位元不帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 8 位元不帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">單精確度浮點數。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Byte.MaxValue" /> 或小於 <see cref="F:System.Byte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String)">
      <summary>將指定之數字的字串表示，轉換為相等的 8 位元不帶正負號的整數。</summary>
      <returns>8 位元不帶正負號的整數，相當於 <paramref name="value" />，或者如果 <paramref name="value" /> 為 null 時，則為零。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是由選擇性正負號之後跟隨一連串數字 (0 到 9) 所組成。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Byte.MinValue" /> 或大於 <see cref="F:System.Byte.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之數字的字串表示轉換為相等的 8 位元不帶正負號的整數。</summary>
      <returns>8 位元不帶正負號的整數，相當於 <paramref name="value" />，或者如果 <paramref name="value" /> 為 null 時，則為零。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是由選擇性正負號之後跟隨一連串數字 (0 到 9) 所組成。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Byte.MinValue" /> 或大於 <see cref="F:System.Byte.MaxValue" /> 的數字。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.String,System.Int32)">
      <summary>將指定基底中數字的字串表示，轉換為相等的 8 位元不帶正負號的整數。</summary>
      <returns>8 位元不帶正負號的整數，相等於 <paramref name="value" /> 中的數字；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="fromBase">
        <paramref name="value" /> 中數字的基底，必須是 2、8、10 或 16。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> 不是 2、8、10 或 16。-或-<paramref name="value" />，表示非 Base 10 的不帶正負號的數字有前置一個負號。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> 為 <see cref="F:System.String.Empty" />。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 包含的字元，對於 <paramref name="fromBase" /> 指定的基底而言是無效的數字。如果 <paramref name="value" /> 中的第一個字元無效，則例外狀況訊息會指出無數字可進行轉換，否則訊息會指出 <paramref name="value" /> 包含無效的尾端字元。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />，表示 Base 10 的不帶正負號的數字有前置一個負號。-或-<paramref name="value" /> 表示小於 <see cref="F:System.Byte.MinValue" /> 或大於 <see cref="F:System.Byte.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt16)">
      <summary>將指定的 16 位元不帶正負號整數的值，轉換為相等的 8 位元不帶正負號整數。</summary>
      <returns>8 位元不帶正負號的整數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換之 16 位元不帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Byte.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt32)">
      <summary>將指定的 32 位元不帶正負號整數的值，轉換為相等的 8 位元不帶正負號整數。</summary>
      <returns>8 位元不帶正負號的整數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換之 32 位元不帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Byte.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToByte(System.UInt64)">
      <summary>將指定的 64 位元不帶正負號整數的值，轉換為相等的 8 位元不帶正負號整數。</summary>
      <returns>8 位元不帶正負號的整數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換之 64 位元不帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Byte.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Byte)">
      <summary>將指定的 8 位元不帶正負號整數的值，轉換為它的相等 Unicode 字元。</summary>
      <returns>Unicode 字元，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換之 8 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int16)">
      <summary>將指定的 16 位元帶正負號整數的值，轉換為它的相等 Unicode 字元。</summary>
      <returns>Unicode 字元，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換的 16 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於 <see cref="F:System.Char.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int32)">
      <summary>將指定的 32 位元帶正負號整數的值，轉換為它的相等 Unicode 字元。</summary>
      <returns>Unicode 字元，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換的 32 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於 <see cref="F:System.Char.MinValue" /> 或大於 <see cref="F:System.Char.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Int64)">
      <summary>將指定的 64 位元帶正負號整數的值，轉換為它的相等 Unicode 字元。</summary>
      <returns>Unicode 字元，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換的 64 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於 <see cref="F:System.Char.MinValue" /> 或大於 <see cref="F:System.Char.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object)">
      <summary>將指定之物件的值轉換為 Unicode 字元。</summary>
      <returns>與 value 相等的 Unicode 字元；如果 <paramref name="value" /> 為 null，則為 <see cref="F:System.Char.MinValue" />。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 是一個 Null 字串。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援將  <paramref name="value" /> 轉換為 <see cref="T:System.Char" />。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於 <see cref="F:System.Char.MinValue" /> 或大於 <see cref="F:System.Char.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.Object,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之物件的值轉換為它的相等 Unicode 字元。</summary>
      <returns>與 <paramref name="value" /> 相等的 Unicode 字元；如果 <paramref name="value" /> 為 null，則為 <see cref="F:System.Char.MinValue" />。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 是一個 Null 字串。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援將  <paramref name="value" /> 轉換為 <see cref="T:System.Char" />。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於 <see cref="F:System.Char.MinValue" /> 或大於 <see cref="F:System.Char.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.SByte)">
      <summary>將指定的 8 位元帶正負號整數的值，轉換為它的相等 Unicode 字元。</summary>
      <returns>Unicode 字元，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換的 8 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於 <see cref="F:System.Char.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String)">
      <summary>將指定之字串的第一個字元轉換為 Unicode 字元。</summary>
      <returns>與 <paramref name="value" /> 中的第一個且唯一之字元相等的 Unicode 字元。</returns>
      <param name="value">長度為 1 的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 為 null。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的長度不為 1。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.String,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之字串的第一個字元轉換為 Unicode 字元。</summary>
      <returns>與 <paramref name="value" /> 中的第一個且唯一之字元相等的 Unicode 字元。</returns>
      <param name="value">長度為 1 或 null 的字串。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。這個參數已忽略。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 為 null。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 的長度不為 1。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt16)">
      <summary>將指定的 16 位元不帶正負號整數的值，轉換為它的相等 Unicode 字元。</summary>
      <returns>Unicode 字元，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換之 16 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt32)">
      <summary>將指定的 32 位元不帶正負號整數的值，轉換為它的相等 Unicode 字元。</summary>
      <returns>Unicode 字元，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換之 32 位元不帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Char.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToChar(System.UInt64)">
      <summary>將指定的 64 位元不帶正負號整數的值，轉換為它的相等 Unicode 字元。</summary>
      <returns>Unicode 字元，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換之 64 位元不帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Char.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object)">
      <summary>將指定之物件的值轉換為 <see cref="T:System.DateTime" /> 物件。</summary>
      <returns>與 <paramref name="value" /> 值相等的日期和時間；如果 <paramref name="value" /> 為 null，則為與 <see cref="F:System.DateTime.MinValue" /> 相等的日期和時間。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件，或是 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是日期及時間值。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援這個轉換。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.Object,System.IFormatProvider)">
      <summary>使用指定之文化特性專屬格式資訊，將指定之物件的值轉換為 <see cref="T:System.DateTime" /> 物件。</summary>
      <returns>與 <paramref name="value" /> 值相等的日期和時間；如果 <paramref name="value" /> 為 null，則為與 <see cref="F:System.DateTime.MinValue" /> 相等的日期和時間。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是日期及時間值。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援這個轉換。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String)">
      <summary>將指定之日期和時間的字串表示，轉換為相等的日期和時間值。</summary>
      <returns>與 <paramref name="value" /> 值相等的日期和時間；如果 <paramref name="value" /> 為 null，則為與 <see cref="F:System.DateTime.MinValue" /> 相等的日期和時間。</returns>
      <param name="value">日期和時間的字串表示。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是正確的日期和時間字串格式。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDateTime(System.String,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之數字的字串表示轉換為相等的日期和時間。</summary>
      <returns>與 <paramref name="value" /> 值相等的日期和時間；如果 <paramref name="value" /> 為 null，則為與 <see cref="F:System.DateTime.MinValue" /> 相等的日期和時間。</returns>
      <param name="value">字串，包含要轉換的日期和時間。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是正確的日期和時間字串格式。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Boolean)">
      <summary>將指定的布林值轉換為相等的十進位數字。</summary>
      <returns>如果 <paramref name="value" /> 為 true，則為數字 1，否則為 0。</returns>
      <param name="value">要轉換的布林值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Byte)">
      <summary>將指定之 8 位元不帶正負號的整數值，轉換為相等的十進位數字。</summary>
      <returns>與 <paramref name="value" /> 相等的十進位數字。</returns>
      <param name="value">要轉換之 8 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Decimal)">
      <summary>傳回指定的十進位數字，不執行實際的轉換。</summary>
      <returns>傳回未變更的 <paramref name="value" />。</returns>
      <param name="value">十進位數字。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Double)">
      <summary>將指定之雙精確度浮點數的值，轉換為相等的十進位數字。</summary>
      <returns>十進位數字，相當於 <paramref name="value" />。 </returns>
      <param name="value">要轉換的雙精確度浮點數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Decimal.MaxValue" /> 或小於 <see cref="F:System.Decimal.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int16)">
      <summary>將指定之 16 位元帶正負號的整數值，轉換為相等的十進位數字。</summary>
      <returns>十進位數字，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換的 16 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int32)">
      <summary>將指定之 32 位元帶正負號的整數值，轉換為相等的十進位數字。</summary>
      <returns>十進位數字，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換的 32 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Int64)">
      <summary>將指定之 64 位元帶正負號的整數值，轉換為相等的十進位數字。</summary>
      <returns>十進位數字，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換的 64 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object)">
      <summary>將指定之物件的值，轉換為相等的十進位數字。</summary>
      <returns>與 <paramref name="value" /> 相等的十進位數字；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件，或是 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是使用 <see cref="T:System.Decimal" /> 型別的適當格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援這個轉換。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Decimal.MinValue" /> 或大於 <see cref="F:System.Decimal.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Object,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之物件的值轉換為相等的十進位數字。</summary>
      <returns>與 <paramref name="value" /> 相等的十進位數字；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是使用 <see cref="T:System.Decimal" /> 型別的適當格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援這個轉換。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Decimal.MinValue" /> 或大於 <see cref="F:System.Decimal.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.SByte)">
      <summary>將指定之 8 位元帶正負號的整數值，轉換為相等的十進位數字。</summary>
      <returns>十進位數字，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換的 8 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.Single)">
      <summary>將指定之單精確度浮點數的值，轉換為相等的十進位數字。</summary>
      <returns>十進位數字，相當於 <paramref name="value" />。 </returns>
      <param name="value">要轉換的單精確度浮點數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Decimal.MaxValue" /> 或小於 <see cref="F:System.Decimal.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String)">
      <summary>將指定之數字的字串表示，轉換為相等的十進位數字。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的十進位數字；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是有效格式的數字。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Decimal.MinValue" /> 或大於 <see cref="F:System.Decimal.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.String,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之數字的字串表示轉換為相等的十進位數字。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的十進位數字；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是有效格式的數字。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Decimal.MinValue" /> 或大於 <see cref="F:System.Decimal.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt16)">
      <summary>將指定之 16 位元不帶正負號的整數值，轉換為相等的十進位數字。</summary>
      <returns>與 <paramref name="value" /> 相等的十進位數字。</returns>
      <param name="value">要轉換之 16 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt32)">
      <summary>將指定之 32 位元不帶正負號的整數值，轉換為相等的十進位數字。</summary>
      <returns>十進位數字，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換之 32 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDecimal(System.UInt64)">
      <summary>將指定之 64 位元不帶正負號的整數值，轉換為相等的十進位數字。</summary>
      <returns>十進位數字，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換之 64 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Boolean)">
      <summary>將指定的布林值轉換為相等的雙精確度浮點數。</summary>
      <returns>如果 <paramref name="value" /> 為 true，則為數字 1，否則為 0。</returns>
      <param name="value">要轉換的布林值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Byte)">
      <summary>將指定之 8 位元不帶正負號的整數值，轉換為相等的雙精確度浮點數。</summary>
      <returns>與 <paramref name="value" /> 相等的雙精確度浮點數。</returns>
      <param name="value">要轉換之 8 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Decimal)">
      <summary>將指定之十進位數字的值，轉換為相等的雙精確度浮點數。</summary>
      <returns>雙精確度浮點數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換的十進位數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Double)">
      <summary>傳回指定的雙精確度浮點數，不執行實際的轉換。</summary>
      <returns>傳回未變更的 <paramref name="value" />。</returns>
      <param name="value">要傳回的雙精確度浮點數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int16)">
      <summary>將指定之 16 位元帶正負號的整數值，轉換為相等的雙精確度浮點數。</summary>
      <returns>雙精確度浮點數，等於 <paramref name="value" />。</returns>
      <param name="value">要轉換的 16 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int32)">
      <summary>將指定之 32 位元帶正負號的整數值，轉換為相等的雙精確度浮點數。</summary>
      <returns>雙精確度浮點數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換的 32 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Int64)">
      <summary>將指定之 64 位元帶正負號的整數值，轉換為相等的雙精確度浮點數。</summary>
      <returns>雙精確度浮點數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換的 64 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object)">
      <summary>將指定之物件的值，轉換為雙精確度浮點數。</summary>
      <returns>與 <paramref name="value" /> 相等的雙精確度浮點數；如果 <paramref name="value" /> 為 null，則為零。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件，或是 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是使用 <see cref="T:System.Double" /> 型別的適當格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援這個轉換。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Double.MinValue" /> 或大於 <see cref="F:System.Double.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Object,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之物件的值轉換為雙精確度浮點數。</summary>
      <returns>與 <paramref name="value" /> 相等的雙精確度浮點數；如果 <paramref name="value" /> 為 null，則為零。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是使用 <see cref="T:System.Double" /> 型別的適當格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Double.MinValue" /> 或大於 <see cref="F:System.Double.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.SByte)">
      <summary>將指定之 8 位元帶正負號的整數值，轉換為相等的雙精確度浮點數。</summary>
      <returns>與 <paramref name="value" /> 相等的 8 位元帶正負號的整數。</returns>
      <param name="value">要轉換的 8 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.Single)">
      <summary>將指定之單精確度浮點數的值，轉換為相等的雙精確度浮點數。</summary>
      <returns>雙精確度浮點數，相當於 <paramref name="value" />。</returns>
      <param name="value">單精確度浮點數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String)">
      <summary>將指定之數字的字串表示，轉換為相等的雙精確度浮點數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的雙精確度浮點數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是有效格式的數字。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Double.MinValue" /> 或大於 <see cref="F:System.Double.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.String,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之數字的字串表示轉換為相等的雙精確度浮點數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的雙精確度浮點數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是有效格式的數字。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Double.MinValue" /> 或大於 <see cref="F:System.Double.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt16)">
      <summary>將指定之 16 位元不帶正負號的整數值，轉換為相等的雙精確度浮點數。</summary>
      <returns>雙精確度浮點數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換之 16 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt32)">
      <summary>將指定之 32 位元不帶正負號的整數值，轉換為相等的雙精確度浮點數。</summary>
      <returns>雙精確度浮點數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換之 32 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToDouble(System.UInt64)">
      <summary>將指定之 64 位元不帶正負號的整數值，轉換為相等的雙精確度浮點數。</summary>
      <returns>雙精確度浮點數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換之 64 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Boolean)">
      <summary>將指定的布林值轉換為相等的 16 位元帶正負號的整數。</summary>
      <returns>如果 <paramref name="value" /> 為 true，則為數字 1，否則為 0。</returns>
      <param name="value">要轉換的布林值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Byte)">
      <summary>將指定的 8 位元不帶正負號的整數值，轉換為相等的 16 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 16 位元帶正負號的整數。</returns>
      <param name="value">要轉換之 8 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Char)">
      <summary>將指定的 Unicode 字元值轉換為相等的 16 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 16 位元帶正負號的整數。 </returns>
      <param name="value">要轉換的 Unicode 字元。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Int16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Decimal)">
      <summary>將指定之十進位數字的值，轉換為相等的 16 位元帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 16 位元帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的十進位數字。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Int16.MaxValue" /> 或小於 <see cref="F:System.Int16.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Double)">
      <summary>將指定之雙精確度浮點數的值，轉換為相等的 16 位元帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 16 位元帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的雙精確度浮點數。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Int16.MaxValue" /> 或小於 <see cref="F:System.Int16.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int16)">
      <summary>傳回指定的 16 位元帶正負號的整數；不會執行實際的轉換。</summary>
      <returns>傳回未變更的 <paramref name="value" />。</returns>
      <param name="value">傳回 16 位元帶正負號的整數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int32)">
      <summary>將指定的 32 位元帶正負號的整數值轉換為相等的 16 位元帶正負號的整數。</summary>
      <returns>16 位元帶正負號的整數，與 <paramref name="value" /> 相等。</returns>
      <param name="value">要轉換的 32 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Int16.MaxValue" /> 或小於 <see cref="F:System.Int16.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Int64)">
      <summary>將指定的 64 位元帶正負號的整數值轉換為相等的 16 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 16 位元帶正負號的整數。</returns>
      <param name="value">要轉換的 64 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Int16.MaxValue" /> 或小於 <see cref="F:System.Int16.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object)">
      <summary>將指定之物件的值，轉換為 16 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 16 位元帶正負號的整數；如果 <paramref name="value" /> 為 null，則為零。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件，或是 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是使用 <see cref="T:System.Int16" /> 型別的適當格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援這個轉換。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Int16.MinValue" /> 或大於 <see cref="F:System.Int16.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Object,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之物件的值轉換為 16 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 16 位元帶正負號的整數；如果 <paramref name="value" /> 為 null，則為零。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是使用 <see cref="T:System.Int16" /> 型別的適當格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 沒有實作 <see cref="T:System.IConvertible" />。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Int16.MinValue" /> 或大於 <see cref="F:System.Int16.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.SByte)">
      <summary>將指定的 8 位元帶正負號的整數值，轉換為相等的 16 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 8 位元帶正負號的整數。</returns>
      <param name="value">要轉換的 8 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.Single)">
      <summary>將指定之單精確度浮點數的值，轉換為相等的 16 位元帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 16 位元帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的單精確度浮點數。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Int16.MaxValue" /> 或小於 <see cref="F:System.Int16.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String)">
      <summary>將指定之數字的字串表示，轉換為相等的 16 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 16 位元帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是由選擇性正負號之後跟隨一連串數字 (0 到 9) 所組成。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Int16.MinValue" /> 或大於 <see cref="F:System.Int16.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之數字的字串表示轉換為相等的 16 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 16 位元帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是由選擇性正負號之後跟隨一連串數字 (0 到 9) 所組成。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Int16.MinValue" /> 或大於 <see cref="F:System.Int16.MaxValue" /> 的數字。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.String,System.Int32)">
      <summary>將指定基底中數字的字串表示，轉換為相等的 16 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 16 位元帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="fromBase">
        <paramref name="value" /> 中數字的基底，必須是 2、8、10 或 16。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> 不是 2、8、10 或 16。-或-<paramref name="value" />，表示非 Base 10 的帶正負號的數字有前置一個負號。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> 為 <see cref="F:System.String.Empty" />。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 包含的字元，對於 <paramref name="fromBase" /> 指定的基底而言是無效的數字。如果 <paramref name="value" /> 中的第一個字元無效，則例外狀況訊息會指出無數字可進行轉換，否則訊息會指出 <paramref name="value" /> 包含無效的尾端字元。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />，表示非 Base 10 的帶正負號的數字有前置一個負號。-或-<paramref name="value" /> 表示小於 <see cref="F:System.Int16.MinValue" /> 或大於 <see cref="F:System.Int16.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt16)">
      <summary>將指定的 16 位元不帶正負號的整數值，轉換為相等的 16 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 16 位元帶正負號的整數。</returns>
      <param name="value">要轉換之 16 位元不帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Int16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt32)">
      <summary>將指定的 32 位元不帶正負號整數的值，轉換為相等的 16 位元帶正負號整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 16 位元帶正負號的整數。</returns>
      <param name="value">要轉換之 32 位元不帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Int16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt16(System.UInt64)">
      <summary>將指定的 64 位元不帶正負號整數的值，轉換為相等的 16 位元帶正負號整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 16 位元帶正負號的整數。</returns>
      <param name="value">要轉換之 64 位元不帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Int16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Boolean)">
      <summary>將指定的布林值轉換為相等的 32 位元帶正負號的整數。</summary>
      <returns>如果 <paramref name="value" /> 為 true，則為數字 1，否則為 0。</returns>
      <param name="value">要轉換的布林值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Byte)">
      <summary>將指定的 8 位元不帶正負號的整數值，轉換為相等的 32 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 32 位元帶正負號的整數。</returns>
      <param name="value">要轉換之 8 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Char)">
      <summary>將指定的 Unicode 字元值轉換為相等的 32 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 32 位元帶正負號的整數。</returns>
      <param name="value">要轉換的 Unicode 字元。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Decimal)">
      <summary>將指定之十進位數字的值，轉換為相等的 32 位元帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 32 位元帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的十進位數字。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Int32.MaxValue" /> 或小於 <see cref="F:System.Int32.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Double)">
      <summary>將指定之雙精確度浮點數的值，轉換為相等的 32 位元帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 32 位元帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的雙精確度浮點數。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Int32.MaxValue" /> 或小於 <see cref="F:System.Int32.MinValue" />。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to an equivalent 32-bit signed integer.</summary>
      <returns>與 <paramref name="value" /> 相等的 32 位元帶正負號的整數。</returns>
      <param name="value">要轉換的 16 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int32)">
      <summary>傳回指定的 32 位元帶正負號的整數；不會執行實際的轉換。</summary>
      <returns>傳回未變更的 <paramref name="value" />。</returns>
      <param name="value">傳回 32 位元帶正負號的整數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Int64)">
      <summary>Converts the value of the specified 64-bit signed integer to an equivalent 32-bit signed integer.</summary>
      <returns>與 <paramref name="value" /> 相等的 32 位元帶正負號的整數。</returns>
      <param name="value">要轉換的 64 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Int32.MaxValue" /> 或小於 <see cref="F:System.Int32.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object)">
      <summary>將指定之物件的值，轉換為 32 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 32 位元帶正負號的整數；如果 <paramref name="value" /> 為 null，則為零。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件，或是 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是適當的格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援這個轉換。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Int32.MinValue" /> 或大於 <see cref="F:System.Int32.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Object,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之物件的值轉換為 32 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 32 位元帶正負號的整數；如果 <paramref name="value" /> 為 null，則為零。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是適當的格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 沒有實作 <see cref="T:System.IConvertible" />。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Int32.MinValue" /> 或大於 <see cref="F:System.Int32.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.SByte)">
      <summary>將指定的 8 位元帶正負號的整數值，轉換為相等的 32 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 8 位元帶正負號的整數。</returns>
      <param name="value">要轉換的 8 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.Single)">
      <summary>將指定之單精確度浮點數的值，轉換為相等的 32 位元帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 32 位元帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的單精確度浮點數。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Int32.MaxValue" /> 或小於 <see cref="F:System.Int32.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String)">
      <summary>將指定之數字的字串表示，轉換為相等的 32 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 32 位元帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是由選擇性正負號之後跟隨一連串數字 (0 到 9) 所組成。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Int32.MinValue" /> 或大於 <see cref="F:System.Int32.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之數字的字串表示轉換為相等的 32 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 32 位元帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是由選擇性正負號之後跟隨一連串數字 (0 到 9) 所組成。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Int32.MinValue" /> 或大於 <see cref="F:System.Int32.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.String,System.Int32)">
      <summary>將指定基底中數字的字串表示，轉換為相等的 32 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 32 位元帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="fromBase">
        <paramref name="value" /> 中數字的基底，必須是 2、8、10 或 16。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> 不是 2、8、10 或 16。-或-<paramref name="value" />，表示非 Base 10 的帶正負號的數字有前置一個負號。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> 為 <see cref="F:System.String.Empty" />。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 包含的字元，對於 <paramref name="fromBase" /> 指定的基底而言是無效的數字。如果 <paramref name="value" /> 中的第一個字元無效，則例外狀況訊息會指出無數字可進行轉換，否則訊息會指出 <paramref name="value" /> 包含無效的尾端字元。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />，表示非 Base 10 的帶正負號的數字有前置一個負號。-或-<paramref name="value" /> 表示小於 <see cref="F:System.Int32.MinValue" /> 或大於 <see cref="F:System.Int32.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt16)">
      <summary>將指定的 16 位元不帶正負號的整數值，轉換為相等的 32 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 32 位元帶正負號的整數。</returns>
      <param name="value">要轉換之 16 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt32)">
      <summary>將指定的 32 位元不帶正負號整數的值，轉換為相等的 32 位元帶正負號整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 32 位元帶正負號的整數。</returns>
      <param name="value">要轉換之 32 位元不帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Int32.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt32(System.UInt64)">
      <summary>將指定的 64 位元不帶正負號整數的值，轉換為相等的 32 位元帶正負號整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 32 位元帶正負號的整數。</returns>
      <param name="value">要轉換之 64 位元不帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Int32.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Boolean)">
      <summary>將指定的布林值轉換為相等的 64 位元帶正負號的整數。</summary>
      <returns>如果 <paramref name="value" /> 為 true，則為數字 1，否則為 0。</returns>
      <param name="value">要轉換的布林值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Byte)">
      <summary>將指定的 8 位元不帶正負號的整數值，轉換為相等的 64 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元帶正負號的整數。</returns>
      <param name="value">要轉換之 8 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Char)">
      <summary>將指定的 Unicode 字元值轉換為相等的 64 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元帶正負號的整數。</returns>
      <param name="value">要轉換的 Unicode 字元。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Decimal)">
      <summary>將指定之十進位數字的值，轉換為相等的 64 位元帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 64 位元帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的十進位數字。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Int64.MaxValue" /> 或小於 <see cref="F:System.Int64.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Double)">
      <summary>將指定之雙精確度浮點數的值，轉換為相等的 64 位元帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 64 位元帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的雙精確度浮點數。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Int64.MaxValue" /> 或小於 <see cref="F:System.Int64.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int16)">
      <summary>將指定的 16 位元帶正負號的整數值轉換為相等的 64 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元帶正負號的整數。</returns>
      <param name="value">要轉換的 16 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int32)">
      <summary>將指定的 32 位元帶正負號的整數值轉換為相等的 64 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元帶正負號的整數。</returns>
      <param name="value">要轉換的 32 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Int64)">
      <summary>傳回指定的 64 位元帶正負號的整數；不會執行實際的轉換。</summary>
      <returns>傳回未變更的 <paramref name="value" />。</returns>
      <param name="value">64 位元帶正負號的整數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object)">
      <summary>將指定之物件的值，轉換為 64 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元帶正負號的整數；如果 <paramref name="value" /> 為 null，則為零。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件，或是 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是適當的格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援這個轉換。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Int64.MinValue" /> 或大於 <see cref="F:System.Int64.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Object,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之物件的值轉換為 64 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元帶正負號的整數；如果 <paramref name="value" /> 為 null，則為零。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是適當的格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援這個轉換。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Int64.MinValue" /> 或大於 <see cref="F:System.Int64.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.SByte)">
      <summary>將指定的 8 位元帶正負號的整數值，轉換為相等的 64 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元帶正負號的整數。</returns>
      <param name="value">要轉換的 8 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.Single)">
      <summary>將指定之單精確度浮點數的值，轉換為相等的 64 位元帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 64 位元帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的單精確度浮點數。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Int64.MaxValue" /> 或小於 <see cref="F:System.Int64.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String)">
      <summary>將指定之數字的字串表示，轉換為相等的 64 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 64 位元帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是由選擇性正負號之後跟隨一連串數字 (0 到 9) 所組成。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Int64.MinValue" /> 或大於 <see cref="F:System.Int64.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之數字的字串表示轉換為相等的 64 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 64 位元帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是由選擇性正負號之後跟隨一連串數字 (0 到 9) 所組成。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Int64.MinValue" /> 或大於 <see cref="F:System.Int64.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.String,System.Int32)">
      <summary>將指定基底中數字的字串表示，轉換為相等的 64 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 64 位元帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="fromBase">
        <paramref name="value" /> 中數字的基底，必須是 2、8、10 或 16。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> 不是 2、8、10 或 16。-或-<paramref name="value" />，表示非 Base 10 的帶正負號的數字有前置一個負號。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> 為 <see cref="F:System.String.Empty" />。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 包含的字元，對於 <paramref name="fromBase" /> 指定的基底而言是無效的數字。如果 <paramref name="value" /> 中的第一個字元無效，則例外狀況訊息會指出無數字可進行轉換，否則訊息會指出 <paramref name="value" /> 包含無效的尾端字元。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />，表示非 Base 10 的帶正負號的數字有前置一個負號。-或-<paramref name="value" /> 表示小於 <see cref="F:System.Int64.MinValue" /> 或大於 <see cref="F:System.Int64.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt16)">
      <summary>將指定的 16 位元不帶正負號的整數值，轉換為相等的 64 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元帶正負號的整數。</returns>
      <param name="value">要轉換之 16 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt32)">
      <summary>將指定的 32 位元不帶正負號整數的值，轉換為相等的 64 位元帶正負號整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元帶正負號的整數。</returns>
      <param name="value">要轉換之 32 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToInt64(System.UInt64)">
      <summary>將指定的 64 位元不帶正負號整數的值，轉換為相等的 64 位元帶正負號整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元帶正負號的整數。</returns>
      <param name="value">要轉換之 64 位元不帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.Int64.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Boolean)">
      <summary>將指定的布林值轉換為相等的 8 位元帶正負號的整數。</summary>
      <returns>如果 <paramref name="value" /> 為 true，則為數字 1，否則為 0。</returns>
      <param name="value">要轉換的布林值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Byte)">
      <summary>將指定的 8 位元不帶正負號的整數值，轉換為相等的 8 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 8 位元帶正負號的整數。</returns>
      <param name="value">要轉換之 8 位元不帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.SByte.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Char)">
      <summary>將指定的 Unicode 字元值轉換為相等的 8 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 8 位元帶正負號的整數。</returns>
      <param name="value">要轉換的 Unicode 字元。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.SByte.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Decimal)">
      <summary>將指定之十進位數字的值，轉換為相等的 8 位元帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 8 位元帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的十進位數字。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.SByte.MaxValue" /> 或小於 <see cref="F:System.SByte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Double)">
      <summary>將指定之雙精確度浮點數的值，轉換為相等的 8 位元帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 8 位元帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的雙精確度浮點數。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.SByte.MaxValue" /> 或小於 <see cref="F:System.SByte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int16)">
      <summary>Converts the value of the specified 16-bit signed integer to the equivalent 8-bit signed integer.</summary>
      <returns>與 <paramref name="value" /> 相等的 8 位元帶正負號的整數。</returns>
      <param name="value">要轉換的 16 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.SByte.MaxValue" /> 或小於 <see cref="F:System.SByte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int32)">
      <summary>將指定的 32 位元帶正負號的整數值轉換為相等的 8 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 8 位元帶正負號的整數。</returns>
      <param name="value">要轉換的 32 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.SByte.MaxValue" /> 或小於 <see cref="F:System.SByte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Int64)">
      <summary>將指定的 64 位元帶正負號的整數值轉換為相等的 8 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 8 位元帶正負號的整數。</returns>
      <param name="value">要轉換的 64 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.SByte.MaxValue" /> 或小於 <see cref="F:System.SByte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object)">
      <summary>將指定之物件的值，轉換為 8 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 8 位元帶正負號的整數；如果 <paramref name="value" /> 為 null，則為零。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件，或是 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是適當的格式。 </exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援這個轉換。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.SByte.MinValue" /> 或大於 <see cref="F:System.SByte.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Object,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之物件的值轉換為 8 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 8 位元帶正負號的整數；如果 <paramref name="value" /> 為 null，則為零。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是適當的格式。 </exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援這個轉換。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.SByte.MinValue" /> 或大於 <see cref="F:System.SByte.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.SByte)">
      <summary>傳回指定的 8 位元帶正負號的整數；不會執行實際的轉換。</summary>
      <returns>傳回未變更的 <paramref name="value" />。</returns>
      <param name="value">傳回 8 位元帶正負號的整數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.Single)">
      <summary>將指定之單精確度浮點數的值，轉換為相等的 8 位元帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 8 位元帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的單精確度浮點數。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.SByte.MaxValue" /> 或小於 <see cref="F:System.SByte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String)">
      <summary>將指定之數字的字串表示，轉換為相等的 8 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 8 位元帶正負號的整數；如果 value 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是由選擇性正負號之後跟隨一連串數字 (0 到 9) 所組成。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.SByte.MinValue" /> 或大於 <see cref="F:System.SByte.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之數字的字串表示轉換為相等的 8 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 8 位元帶正負號的整數。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 為 null。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是由選擇性正負號之後跟隨一連串數字 (0 到 9) 所組成。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.SByte.MinValue" /> 或大於 <see cref="F:System.SByte.MaxValue" /> 的數字。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.String,System.Int32)">
      <summary>將指定基底中數字的字串表示，轉換為相等的 8 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 8 位元帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="fromBase">
        <paramref name="value" /> 中數字的基底，必須是 2、8、10 或 16。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> 不是 2、8、10 或 16。-或-<paramref name="value" />，表示非 Base 10 的帶正負號的數字有前置一個負號。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> 為 <see cref="F:System.String.Empty" />。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 包含的字元，對於 <paramref name="fromBase" /> 指定的基底而言是無效的數字。如果 <paramref name="value" /> 中的第一個字元無效，則例外狀況訊息會指出無數字可進行轉換，否則訊息會指出 <paramref name="value" /> 包含無效的尾端字元。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />，表示非 Base 10 的帶正負號的數字有前置一個負號。-或-<paramref name="value" /> 表示小於 <see cref="F:System.SByte.MinValue" /> 或大於 <see cref="F:System.SByte.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt16)">
      <summary>將指定的 16 位元不帶正負號的整數值，轉換為相等的 8 位元帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 8 位元帶正負號的整數。</returns>
      <param name="value">要轉換之 16 位元不帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.SByte.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt32)">
      <summary>將指定的 32 位元不帶正負號整數的值，轉換為相等的 8 位元帶正負號整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 8 位元帶正負號的整數。</returns>
      <param name="value">要轉換之 32 位元不帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.SByte.MaxValue" /> 或小於 <see cref="F:System.SByte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSByte(System.UInt64)">
      <summary>將指定的 64 位元不帶正負號整數的值，轉換為相等的 8 位元帶正負號整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 8 位元帶正負號的整數。</returns>
      <param name="value">要轉換之 64 位元不帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.SByte.MaxValue" /> 或小於 <see cref="F:System.SByte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Boolean)">
      <summary>將指定的布林值轉換為相等的單精確度浮點數。</summary>
      <returns>如果 <paramref name="value" /> 為 true，則為數字 1，否則為 0。</returns>
      <param name="value">要轉換的布林值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Byte)">
      <summary>將指定之 8 位元不帶正負號的整數值，轉換為相等的單精確度浮點數。</summary>
      <returns>單精確度浮點數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換之 8 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Decimal)">
      <summary>將指定之十進位數字的值，轉換為相等的單精確度浮點數。</summary>
      <returns>單精確度浮點數，相當於 <paramref name="value" />。<paramref name="value" /> 會以捨入為最接近的數字來捨入。例如，在四捨五入為兩個小數位數時，值 2.345 會變成 2.34，而值 2.355 會變成 2.36。</returns>
      <param name="value">要轉換的十進位數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Double)">
      <summary>將指定之雙精確度浮點數的值，轉換為相等的單精確度浮點數。</summary>
      <returns>單精確度浮點數，相當於 <paramref name="value" />。<paramref name="value" /> 會以捨入為最接近的數字來捨入。例如，在四捨五入為兩個小數位數時，值 2.345 會變成 2.34，而值 2.355 會變成 2.36。</returns>
      <param name="value">要轉換的雙精確度浮點數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int16)">
      <summary>將指定之 16 位元帶正負號的整數值，轉換為相等的單精確度浮點數。</summary>
      <returns>單精確度浮點數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換的 16 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int32)">
      <summary>將指定之 32 位元帶正負號的整數值，轉換為相等的單精確度浮點數。</summary>
      <returns>單精確度浮點數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換的 32 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Int64)">
      <summary>將指定之 64 位元帶正負號的整數值，轉換為相等的單精確度浮點數。</summary>
      <returns>單精確度浮點數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換的 64 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object)">
      <summary>將指定之物件的值，轉換為單精確度浮點數。</summary>
      <returns>與 <paramref name="value" /> 相等的單精確度浮點數；如果 <paramref name="value" /> 為 null，則為零。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件，或是 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是適當的格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援這個轉換。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Single.MinValue" /> 或大於 <see cref="F:System.Single.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Object,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之物件的值轉換為單精確度浮點數。</summary>
      <returns>與 <paramref name="value" /> 相等的單精確度浮點數；如果 <paramref name="value" /> 為 null，則為零。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是適當的格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 沒有實作 <see cref="T:System.IConvertible" />。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Single.MinValue" /> 或大於 <see cref="F:System.Single.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.SByte)">
      <summary>將指定之 8 位元帶正負號的整數值，轉換為相等的單精確度浮點數。</summary>
      <returns>與 <paramref name="value" /> 相等的 8 位元帶正負號的整數。</returns>
      <param name="value">要轉換的 8 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.Single)">
      <summary>傳回指定的單精確度浮點數，不執行實際的轉換。</summary>
      <returns>傳回未變更的 <paramref name="value" />。</returns>
      <param name="value">要傳回的單精確度浮點數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String)">
      <summary>將指定之數字的字串表示，轉換為相等的單精確度浮點數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的單精確度浮點數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是有效格式的數字。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Single.MinValue" /> 或大於 <see cref="F:System.Single.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.String,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之數字的字串表示轉換為相等的單精確度浮點數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的單精確度浮點數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是有效格式的數字。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.Single.MinValue" /> 或大於 <see cref="F:System.Single.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt16)">
      <summary>將指定之 16 位元不帶正負號的整數值，轉換為相等的單精確度浮點數。</summary>
      <returns>單精確度浮點數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換之 16 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt32)">
      <summary>將指定之 32 位元不帶正負號的整數值，轉換為相等的單精確度浮點數。</summary>
      <returns>單精確度浮點數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換之 32 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToSingle(System.UInt64)">
      <summary>將指定之 64 位元不帶正負號的整數值，轉換為相等的單精確度浮點數。</summary>
      <returns>單精確度浮點數，相當於 <paramref name="value" />。</returns>
      <param name="value">要轉換之 64 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean)">
      <summary>將指定的布林值轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的布林值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Boolean,System.IFormatProvider)">
      <summary>將指定的布林值轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的布林值。</param>
      <param name="provider">物件的執行個體。這個參數已忽略。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte)">
      <summary>將指定之 8 位元不帶正負號的整數值，轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換之 8 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之 8 位元不帶正負號的整數的值轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換之 8 位元不帶正負號的整數。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Byte,System.Int32)">
      <summary>將 8 位元不帶正負號整數的值，轉換為它在指定基底中的相等字串表示。</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">要轉換之 8 位元不帶正負號的整數。</param>
      <param name="toBase">傳回值的基底，必須是 2、8、10 或 16。 </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> 不是 2、8、10 或 16。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char)">
      <summary>將指定之 Unicode 字元的值，轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的 Unicode 字元。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Char,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之 Unicode 字元的值轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的 Unicode 字元。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。這個參數已忽略。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime)">
      <summary>將指定之 <see cref="T:System.DateTime" /> 的值轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的日期和時間值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.DateTime,System.IFormatProvider)">
      <summary>使用指定之文化特性專屬格式資訊，將指定之 <see cref="T:System.DateTime" /> 的值轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的日期和時間值。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal)">
      <summary>將指定之十進位數字的值，轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的十進位數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Decimal,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之十進位數字的值轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的十進位數字。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double)">
      <summary>將指定之雙精確度浮點數的值，轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的雙精確度浮點數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Double,System.IFormatProvider)">
      <summary>將指定之雙精確度浮點數的值，轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的雙精確度浮點數。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16)">
      <summary>將指定之 16 位元帶正負號的整數值，轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的 16 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之 16 位元帶正負號的整數的值轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的 16 位元帶正負號的整數。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int16,System.Int32)">
      <summary>將 16 位元帶正負號整數的值，轉換為它在指定之基底中的相等字串表示。</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">要轉換的 16 位元帶正負號的整數。</param>
      <param name="toBase">傳回值的基底，必須是 2、8、10 或 16。 </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> 不是 2、8、10 或 16。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32)">
      <summary>將指定之 32 位元帶正負號的整數值，轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的 32 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之 32 位元帶正負號的整數的值轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的 32 位元帶正負號的整數。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int32,System.Int32)">
      <summary>將 32 位元帶正負號整數的值，轉換為它在指定之基底中的相等字串表示。</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">要轉換的 32 位元帶正負號的整數。</param>
      <param name="toBase">傳回值的基底，必須是 2、8、10 或 16。 </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> 不是 2、8、10 或 16。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64)">
      <summary>將指定之 64 位元帶正負號的整數值，轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的 64 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之 64 位元帶正負號的整數的值轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的 64 位元帶正負號的整數。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Int64,System.Int32)">
      <summary>將 64 位元帶正負號整數的值，轉換為它在指定之基底中的相等字串表示。</summary>
      <returns>The string representation of <paramref name="value" /> in base <paramref name="toBase" />.</returns>
      <param name="value">要轉換的 64 位元帶正負號的整數。</param>
      <param name="toBase">傳回值的基底，必須是 2、8、10 或 16。 </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="toBase" /> 不是 2、8、10 或 16。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object)">
      <summary>將指定之物件的值轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示，如果 <paramref name="value" /> 是值為 null 的物件，則為 <see cref="F:System.String.Empty" />。如果 <paramref name="value" /> 是 null，則方法會傳回 null。</returns>
      <param name="value">提供要轉換之值的物件，或是 null。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Object,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之物件的值轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示，如果 <paramref name="value" /> 是值為 null 的物件，則為 <see cref="F:System.String.Empty" />。如果 <paramref name="value" /> 是 null，則方法會傳回 null。</returns>
      <param name="value">提供要轉換之值的物件，或是 null。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte)">
      <summary>將指定之 8 位元帶正負號的整數值，轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的 8 位元帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.SByte,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之 8 位元帶正負號的整數的值轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的 8 位元帶正負號的整數。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single)">
      <summary>將指定之單精確度浮點數的值，轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的單精確度浮點數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.Single,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之單精確度浮點數的值轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換的單精確度浮點數。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16)">
      <summary>將指定之 16 位元不帶正負號的整數值，轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換之 16 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt16,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之 16 位元不帶正負號的整數的值轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換之 16 位元不帶正負號的整數。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32)">
      <summary>將指定之 32 位元不帶正負號的整數值，轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換之 32 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt32,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之 32 位元不帶正負號的整數的值轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換之 32 位元不帶正負號的整數。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64)">
      <summary>將指定之 64 位元不帶正負號的整數值，轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換之 64 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToString(System.UInt64,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之 64 位元不帶正負號的整數的值轉換為它的相等字串表示。</summary>
      <returns>
        <paramref name="value" /> 的字串表示。</returns>
      <param name="value">要轉換之 64 位元不帶正負號的整數。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Boolean)">
      <summary>將指定的布林值轉換為相等的 16 位元不帶正負號的整數。</summary>
      <returns>如果 <paramref name="value" /> 為 true，則為數字 1，否則為 0。</returns>
      <param name="value">要轉換的布林值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Byte)">
      <summary>將指定的 8 位元不帶正負號整數的值，轉換為相等的 16 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 16 位元不帶正負號的整數。</returns>
      <param name="value">要轉換之 8 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Char)">
      <summary>將指定的 Unicode 字元值轉換為相等的 16 位元不帶正負號的整數。</summary>
      <returns>等於 <paramref name="value" /> 的 16 位元不帶正負號的整數。</returns>
      <param name="value">要轉換的 Unicode 字元。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Decimal)">
      <summary>將指定之十進位數字的值，轉換為相等的 16 位元不帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 16 位元不帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的十進位數字。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零或大於 <see cref="F:System.UInt16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Double)">
      <summary>將指定之雙精確度浮點數的值，轉換為相等的 16 位元不帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 16 位元不帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的雙精確度浮點數。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零或大於 <see cref="F:System.UInt16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int16)">
      <summary>將指定的 16 位元帶正負號整數的值，轉換為相等的 16 位元不帶正負號整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 16 位元不帶正負號的整數。</returns>
      <param name="value">要轉換的 16 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int32)">
      <summary>將指定的 32 位元帶正負號的整數值轉換為相等的 16 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 16 位元不帶正負號的整數。</returns>
      <param name="value">要轉換的 32 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零或大於 <see cref="F:System.UInt16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Int64)">
      <summary>將指定的 64 位元帶正負號的整數值轉換為相等的 16 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 16 位元不帶正負號的整數。</returns>
      <param name="value">要轉換的 64 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零或大於 <see cref="F:System.UInt16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object)">
      <summary>將指定之物件的值轉換為 16 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 16 位元不帶正負號的整數；如果 <paramref name="value" /> 為 null，則為零。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件，或是 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是適當的格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援這個轉換。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.UInt16.MinValue" /> 或大於 <see cref="F:System.UInt16.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Object,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之物件的值轉換為 16 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 16 位元不帶正負號的整數；如果 <paramref name="value" /> 為 null，則為零。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是適當的格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援這個轉換。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.UInt16.MinValue" /> 或大於 <see cref="F:System.UInt16.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.SByte)">
      <summary>將指定的 8 位元帶正負號整數的值，轉換為相等的 16 位元不帶正負號整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 16 位元不帶正負號的整數。</returns>
      <param name="value">要轉換的 8 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.Single)">
      <summary>將指定之單精確度浮點數的值，轉換為相等的 16 位元不帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 16 位元不帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的單精確度浮點數。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零或大於 <see cref="F:System.UInt16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String)">
      <summary>將指定之數字的字串表示，轉換為相等的 16 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 16 位元不帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是由選擇性正負號之後跟隨一連串數字 (0 到 9) 所組成。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.UInt16.MinValue" /> 或大於 <see cref="F:System.UInt16.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之數字的字串表示轉換為相等的 16 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 16 位元不帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是由選擇性正負號之後跟隨一連串數字 (0 到 9) 所組成。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.UInt16.MinValue" /> 或大於 <see cref="F:System.UInt16.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.String,System.Int32)">
      <summary>將指定基底中數字的字串表示，轉換為相等的 16 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 16 位元不帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="fromBase">
        <paramref name="value" /> 中數字的基底，必須是 2、8、10 或 16。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> 不是 2、8、10 或 16。-或-<paramref name="value" />，表示非 Base 10 的不帶正負號的數字有前置一個負號。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> 為 <see cref="F:System.String.Empty" />。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 包含的字元，對於 <paramref name="fromBase" /> 指定的基底而言是無效的數字。如果 <paramref name="value" /> 中的第一個字元無效，則例外狀況訊息會指出無數字可進行轉換，否則訊息會指出 <paramref name="value" /> 包含無效的尾端字元。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />，表示非 Base 10 的不帶正負號的數字有前置一個負號。-或-<paramref name="value" /> 表示小於 <see cref="F:System.UInt16.MinValue" /> 或大於 <see cref="F:System.UInt16.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt16)">
      <summary>傳回指定的 16 位元不帶正負號整數；不會執行實際的轉換。</summary>
      <returns>傳回未變更的 <paramref name="value" />。</returns>
      <param name="value">要傳回之 16 位元不帶正負號的整數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt32)">
      <summary>將指定的 32 位元不帶正負號整數的值，轉換為相等的 16 位元不帶正負號整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 16 位元不帶正負號的整數。</returns>
      <param name="value">要轉換之 32 位元不帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.UInt16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt16(System.UInt64)">
      <summary>將指定的 64 位元不帶正負號整數的值，轉換為相等的 16 位元不帶正負號整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 16 位元不帶正負號的整數。</returns>
      <param name="value">要轉換之 64 位元不帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.UInt16.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Boolean)">
      <summary>將指定的布林值轉換為相等的 32 位元不帶正負號的整數。</summary>
      <returns>如果 <paramref name="value" /> 為 true，則為數字 1，否則為 0。</returns>
      <param name="value">要轉換的布林值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Byte)">
      <summary>將指定的 8 位元不帶正負號整數的值，轉換為相等的 32 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 32 位元不帶正負號的整數。</returns>
      <param name="value">要轉換之 8 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Char)">
      <summary>將指定的 Unicode 字元值轉換為相等的 32 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 32 位元不帶正負號的整數。</returns>
      <param name="value">要轉換的 Unicode 字元。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Decimal)">
      <summary>將指定之十進位數字的值，轉換為相等的 32 位元不帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 32 位元不帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的十進位數字。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零或大於 <see cref="F:System.UInt32.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Double)">
      <summary>將指定之雙精確度浮點數的值，轉換為相等的 32 位元不帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 32 位元不帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的雙精確度浮點數。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零或大於 <see cref="F:System.UInt32.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int16)">
      <summary>將指定的 16 位元帶正負號整數的值，轉換為相等的 32 位元不帶正負號整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 32 位元不帶正負號的整數。</returns>
      <param name="value">要轉換的 16 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int32)">
      <summary>將指定的 32 位元帶正負號的整數值轉換為相等的 32 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 32 位元不帶正負號的整數。</returns>
      <param name="value">要轉換的 32 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Int64)">
      <summary>將指定的 64 位元帶正負號的整數值轉換為相等的 32 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 32 位元不帶正負號的整數。</returns>
      <param name="value">要轉換的 64 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零或大於 <see cref="F:System.UInt32.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object)">
      <summary>將指定之物件的值轉換為 32 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 32 位元不帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件，或是 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是適當的格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援這個轉換。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.UInt32.MinValue" /> 或大於 <see cref="F:System.UInt32.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Object,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之物件的值轉換為 32 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 32 位元不帶正負號的整數；如果 <paramref name="value" /> 為 null，則為零。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是適當的格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援這個轉換。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.UInt32.MinValue" /> 或大於 <see cref="F:System.UInt32.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.SByte)">
      <summary>將指定的 8 位元帶正負號整數的值，轉換為相等的 32 位元不帶正負號整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 32 位元不帶正負號的整數。</returns>
      <param name="value">要轉換的 8 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.Single)">
      <summary>將指定之單精確度浮點數的值，轉換為相等的 32 位元不帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 32 位元不帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的單精確度浮點數。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零或大於 <see cref="F:System.UInt32.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String)">
      <summary>將指定之數字的字串表示，轉換為相等的 32 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 32 位元不帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是由選擇性正負號之後跟隨一連串數字 (0 到 9) 所組成。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.UInt32.MinValue" /> 或大於 <see cref="F:System.UInt32.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之數字的字串表示轉換為相等的 32 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 32 位元不帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是由選擇性正負號之後跟隨一連串數字 (0 到 9) 所組成。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.UInt32.MinValue" /> 或大於 <see cref="F:System.UInt32.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.String,System.Int32)">
      <summary>將指定基底中數字的字串表示，轉換為相等的 32 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 32 位元不帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="fromBase">
        <paramref name="value" /> 中數字的基底，必須是 2、8、10 或 16。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> 不是 2、8、10 或 16。-或-<paramref name="value" />，表示非 Base 10 的不帶正負號的數字有前置一個負號。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> 為 <see cref="F:System.String.Empty" />。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 包含的字元，對於 <paramref name="fromBase" /> 指定的基底而言是無效的數字。如果 <paramref name="value" /> 中的第一個字元無效，則例外狀況訊息會指出無數字可進行轉換，否則訊息會指出 <paramref name="value" /> 包含無效的尾端字元。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />，表示非 Base 10 的不帶正負號的數字有前置一個負號。-或-<paramref name="value" /> 表示小於 <see cref="F:System.UInt32.MinValue" /> 或大於 <see cref="F:System.UInt32.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt16)">
      <summary>將指定的 16 位元不帶正負號整數的值，轉換為相等的 32 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 32 位元不帶正負號的整數。</returns>
      <param name="value">要轉換之 16 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt32)">
      <summary>傳回指定的 32 位元不帶正負號整數；不會執行實際的轉換。</summary>
      <returns>傳回未變更的 <paramref name="value" />。</returns>
      <param name="value">要傳回之 32 位元不帶正負號的整數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt32(System.UInt64)">
      <summary>將指定的 64 位元不帶正負號整數的值，轉換為相等的 32 位元不帶正負號整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 32 位元不帶正負號的整數。</returns>
      <param name="value">要轉換之 64 位元不帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 大於 <see cref="F:System.UInt32.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Boolean)">
      <summary>將指定的布林值轉換為相等的 64 位元不帶正負號的整數。</summary>
      <returns>如果 <paramref name="value" /> 為 true，則為數字 1，否則為 0。</returns>
      <param name="value">要轉換的布林值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Byte)">
      <summary>將指定的 8 位元不帶正負號整數的值，轉換為相等的 64 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元帶正負號的整數。</returns>
      <param name="value">要轉換之 8 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Char)">
      <summary>將指定的 Unicode 字元值轉換為相等的 64 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元不帶正負號的整數。</returns>
      <param name="value">要轉換的 Unicode 字元。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Decimal)">
      <summary>將指定之十進位數字的值，轉換為相等的 64 位元不帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 64 位元不帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的十進位數字。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零或大於 <see cref="F:System.UInt64.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Double)">
      <summary>將指定之雙精確度浮點數的值，轉換為相等的 64 位元不帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 64 位元不帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的雙精確度浮點數。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零或大於 <see cref="F:System.UInt64.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int16)">
      <summary>將指定的 16 位元帶正負號整數的值，轉換為相等的 64 位元不帶正負號整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元不帶正負號的整數。</returns>
      <param name="value">要轉換的 16 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int32)">
      <summary>將指定的 32 位元帶正負號的整數值轉換為相等的 64 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元不帶正負號的整數。</returns>
      <param name="value">要轉換的 32 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Int64)">
      <summary>將指定的 64 位元帶正負號的整數值轉換為相等的 64 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元不帶正負號的整數。</returns>
      <param name="value">要轉換的 64 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object)">
      <summary>將指定之物件的值轉換為 64 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元不帶正負號的整數；如果 <paramref name="value" /> 為 null，則為零。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件，或是 null。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是適當的格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援這個轉換。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.UInt64.MinValue" /> 或大於 <see cref="F:System.UInt64.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Object,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之物件的值轉換為 64 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元不帶正負號的整數；如果 <paramref name="value" /> 為 null，則為零。</returns>
      <param name="value">實作 <see cref="T:System.IConvertible" /> 介面的物件。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是適當的格式。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="value" /> 不會實作 <see cref="T:System.IConvertible" /> 介面。-或-不支援這個轉換。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.UInt64.MinValue" /> 或大於 <see cref="F:System.UInt64.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.SByte)">
      <summary>將指定的 8 位元帶正負號整數的值，轉換為相等的 64 位元不帶正負號整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元不帶正負號的整數。</returns>
      <param name="value">要轉換的 8 位元帶正負號的整數。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.Single)">
      <summary>將指定之單精確度浮點數的值，轉換為相等的 64 位元不帶正負號的整數。</summary>
      <returns>
        <paramref name="value" />，捨入至最接近的 64 位元不帶正負號的整數。如果 <paramref name="value" /> 介於兩個整數正中間時，則傳回偶數；也就是 4.5 轉換為 4，而 5.5 則轉換為 6。</returns>
      <param name="value">要轉換的單精確度浮點數。 </param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 小於零或大於 <see cref="F:System.UInt64.MaxValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String)">
      <summary>將指定之數字的字串表示，轉換為相等的 64 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 64 位元帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是由選擇性正負號之後跟隨一連串數字 (0 到 9) 所組成。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.UInt64.MinValue" /> 或大於 <see cref="F:System.UInt64.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.IFormatProvider)">
      <summary>使用指定之特定文化特性格式資訊，將指定之數字的字串表示轉換為相等的 64 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 64 位元不帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 不是由選擇性正負號之後跟隨一連串數字 (0 到 9) 所組成。 </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> 表示小於 <see cref="F:System.UInt64.MinValue" /> 或大於 <see cref="F:System.UInt64.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.String,System.Int32)">
      <summary>將指定基底中數字的字串表示，轉換為相等的 64 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 中之數字相等的 64 位元不帶正負號的整數；如果 <paramref name="value" /> 為 null，則為 0 (零)。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="fromBase">
        <paramref name="value" /> 中數字的基底，必須是 2、8、10 或 16。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fromBase" /> 不是 2、8、10 或 16。-或-<paramref name="value" />，表示非 Base 10 的不帶正負號的數字有前置一個負號。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> 為 <see cref="F:System.String.Empty" />。 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> 包含的字元，對於 <paramref name="fromBase" /> 指定的基底而言是無效的數字。如果 <paramref name="value" /> 中的第一個字元無效，則例外狀況訊息會指出無數字可進行轉換，否則訊息會指出 <paramref name="value" /> 包含無效的尾端字元。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />，表示非 Base 10 的不帶正負號的數字有前置一個負號。-或-<paramref name="value" /> 表示小於 <see cref="F:System.UInt64.MinValue" /> 或大於 <see cref="F:System.UInt64.MaxValue" /> 的數字。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt16)">
      <summary>將指定的 16 位元不帶正負號整數的值，轉換為相等的 64 位元不帶正負號的整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元不帶正負號的整數。</returns>
      <param name="value">要轉換之 16 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt32)">
      <summary>將指定的 32 位元不帶正負號整數的值，轉換為相等的 64 位元不帶正負號整數。</summary>
      <returns>與 <paramref name="value" /> 相等的 64 位元不帶正負號的整數。</returns>
      <param name="value">要轉換之 32 位元不帶正負號的整數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Convert.ToUInt64(System.UInt64)">
      <summary>傳回指定的 64 位元不帶正負號整數；不會執行實際的轉換。</summary>
      <returns>傳回未變更的 <paramref name="value" />。</returns>
      <param name="value">要傳回之 64 位元不帶正負號的整數。 </param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Environment">
      <summary>提供有關目前環境和平台的資訊，以及操作的方法。此類別無法被繼承。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.CurrentManagedThreadId">
      <summary>取得目前 Managed 執行緒的唯一識別項。</summary>
      <returns>整數，表示這個 Managed 執行緒的唯一識別項。</returns>
    </member>
    <member name="M:System.Environment.ExpandEnvironmentVariables(System.String)">
      <summary>用變數值的等值字串來取代內嵌於指定字串內的每個環境變數名稱，然後傳回結果字串。</summary>
      <returns>由字串值取代每個環境變數的字串。</returns>
      <param name="name">含有零或多個環境變數名稱的字串。每個環境變數會以百分比符號字元 (%) 括住。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.FailFast(System.String)">
      <summary>將訊息寫入 Windows 應用程式事件記錄檔後立即終止處理程序，然後在向 Microsoft 回報錯誤時包含該訊息。</summary>
      <param name="message">解釋處理程序結束原因的訊息；如果未提供任何解釋，則為 null。</param>
    </member>
    <member name="M:System.Environment.FailFast(System.String,System.Exception)">
      <summary>將訊息寫入 Windows 應用程式事件記錄檔後立即終止處理程序，然後在向 Microsoft 回報錯誤時，包含該訊息和例外狀況資訊。</summary>
      <param name="message">解釋處理程序結束原因的訊息；如果未提供任何解釋，則為 null。</param>
      <param name="exception">表示造成終止之錯誤的例外狀況。這通常是 catch 區塊中的例外狀況。</param>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariable(System.String)">
      <summary>從目前的處理程序中擷取環境變數的值。</summary>
      <returns>
        <paramref name="variable" /> 指定的環境變數的值；如果找不到環境變數，則為 null。</returns>
      <param name="variable">環境變數的名稱。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.GetEnvironmentVariables">
      <summary>從目前的處理程序中擷取所有環境變數名稱及它們的值。</summary>
      <returns>包含所有環境變數名稱及其值的字典；如果找不到環境變數，則為空的字典。</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <exception cref="T:System.OutOfMemoryException">The buffer is out of memory.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.HasShutdownStarted">
      <summary>取得值，這個值表示正在卸載目前的應用程式定義域，或是正在關閉通用語言執行平台 (CLR)。</summary>
      <returns>如果目前的應用程式定義域正在卸載或 CLR 正在關閉，則為 true；否則為 false.。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.NewLine">
      <summary>取得為這個環境定義的新行字串 (Newline String)。</summary>
      <returns>如果是非 Unix 平台，則為包含 "\r\n" 的字串；如果是 Unix 平台，則為包含 "\n" 的字串。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Environment.ProcessorCount">
      <summary>取得目前電腦上的處理器數目。</summary>
      <returns>32 位元帶正負號的整數，可指定目前電腦上的處理器數目。沒有預設值。如果目前電腦包含多個處理器群組，則這個屬性會傳回可供 Common Language Runtime (CLR) 使用的邏輯處理器數目。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="NUMBER_OF_PROCESSORS" />
      </PermissionSet>
    </member>
    <member name="M:System.Environment.SetEnvironmentVariable(System.String,System.String)">
      <summary>建立、修改或刪除儲存在目前處理程序中的環境變數。</summary>
      <param name="variable">環境變數的名稱。</param>
      <param name="value">要指派給 <paramref name="variable" /> 的值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="variable" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="variable" /> contains a zero-length string, an initial hexadecimal zero character (0x00), or an equal sign ("="). -or-The length of <paramref name="variable" /> or <paramref name="value" /> is greater than or equal to 32,767 characters.-or-An error occurred during the execution of this operation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to perform this operation.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.StackTrace">
      <summary>取得目前的堆疊追蹤資訊。</summary>
      <returns>含有堆疊追蹤資訊的字串。這個值可以是 <see cref="F:System.String.Empty" />。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The requested stack trace information is out of range.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Environment.TickCount">
      <summary>取得系統啟動後經過的毫秒數。</summary>
      <returns>32 位元帶正負號的整數，包含自上一次電腦啟動後所經過的毫秒時間量。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Math">
      <summary>提供三角函數、對數函數和其他一般數學函數的常數和靜態方法。若要浏览此类型的.NET Framework 源代码，请参阅 Reference Source。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Decimal)">
      <summary>傳回 <see cref="T:System.Decimal" /> 數字的絕對值。</summary>
      <returns>十進位數字 x，其滿足下列條件 0 ≤ x ≤<see cref="F:System.Decimal.MaxValue" />。</returns>
      <param name="value">大於或等於 <see cref="F:System.Decimal.MinValue" />但小於或等於 <see cref="F:System.Decimal.MaxValue" /> 的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Double)">
      <summary>傳回雙精確度浮點數的絕對值。</summary>
      <returns>雙精確度浮點數 x，其滿足下列條件 0 ≤ x ≤<see cref="F:System.Double.MaxValue" />。</returns>
      <param name="value">大於或等於 <see cref="F:System.Double.MinValue" />但小於或等於 <see cref="F:System.Double.MaxValue" /> 的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int16)">
      <summary>傳回 16 位元帶正負號整數的絕對值。</summary>
      <returns>16 位元帶正負號整數 x，其滿足下列條件 0 ≤ x ≤<see cref="F:System.Int16.MaxValue" />。</returns>
      <param name="value">大於 <see cref="F:System.Int16.MinValue" />但小於或等於 <see cref="F:System.Int16.MaxValue" /> 的數字。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />等於 <see cref="F:System.Int16.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int32)">
      <summary>傳回 32 位元帶正負號整數的絕對值。</summary>
      <returns>32 位元帶正負號整數 x，其滿足下列條件 0 ≤ x ≤<see cref="F:System.Int32.MaxValue" />。</returns>
      <param name="value">大於 <see cref="F:System.Int32.MinValue" />但小於或等於 <see cref="F:System.Int32.MaxValue" /> 的數字。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />等於 <see cref="F:System.Int32.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Int64)">
      <summary>傳回 64 位元帶正負號整數的絕對值。</summary>
      <returns>64 位元帶正負號整數 x，其滿足下列條件 0 ≤ x ≤<see cref="F:System.Int64.MaxValue" />。</returns>
      <param name="value">大於 <see cref="F:System.Int64.MinValue" />但小於或等於 <see cref="F:System.Int64.MaxValue" /> 的數字。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />等於 <see cref="F:System.Int64.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.SByte)">
      <summary>傳回 8 位元帶正負號整數的絕對值。</summary>
      <returns>8 位元帶正負號整數 x，其滿足下列條件 0 ≤ x ≤<see cref="F:System.SByte.MaxValue" />。</returns>
      <param name="value">大於 <see cref="F:System.SByte.MinValue" />但小於或等於 <see cref="F:System.SByte.MaxValue" /> 的數字。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" />等於 <see cref="F:System.SByte.MinValue" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Abs(System.Single)">
      <summary>傳回單精確度浮點數的絕對值。</summary>
      <returns>單精確度浮點數 x，其滿足下列條件 0 ≤ x ≤<see cref="F:System.Single.MaxValue" />。</returns>
      <param name="value">大於或等於 <see cref="F:System.Single.MinValue" />但小於或等於 <see cref="F:System.Single.MaxValue" /> 的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Acos(System.Double)">
      <summary>傳回餘弦函數 (Cosine) 是指定數字的角。</summary>
      <returns>以弧度為單位的角度 θ，其滿足下列條件 0 ≤θ≤π-或- 如果 <paramref name="d" /> &lt; -1、<paramref name="d" /> &gt; 1 或 <paramref name="d" /> 等於 <see cref="F:System.Double.NaN" />，則為 <see cref="F:System.Double.NaN" />。</returns>
      <param name="d">代表餘弦函數的數字，其中 <paramref name="d" /> 必須大於或等於 -1 但小於或等於 1。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Asin(System.Double)">
      <summary>傳回正弦函數 (Sine) 是指定數字的角。</summary>
      <returns>以弧度為單位的角度 θ，其滿足下列條件 -π/2 ≤θ≤π/2。 -或- 如果 <paramref name="d" /> &lt; -1、<paramref name="d" /> &gt; 1 或 <paramref name="d" /> 等於 <see cref="F:System.Double.NaN" />，則為 <see cref="F:System.Double.NaN" />。</returns>
      <param name="d">代表正弦函數的數字，其中 <paramref name="d" /> 必須大於或等於 -1，但小於或等於 1。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan(System.Double)">
      <summary>傳回正切函數 (Tangent) 是指定數字的角。</summary>
      <returns>以弧度為單位的角度 θ，其滿足下列條件 -π/2 ≤θ≤π/2。-或- 如果 <paramref name="d" /> 等於 <see cref="F:System.Double.NaN" />，則為 <see cref="F:System.Double.NaN" />；如果 <paramref name="d" /> 等於 <see cref="F:System.Double.NegativeInfinity" />，則為 -π/2，四捨五入為雙精度 (-1.5707963267949)；如果 <paramref name="d" /> 等於 <see cref="F:System.Double.PositiveInfinity" />，則為 π/2，四捨五入為雙精度 (1.5707963267949)。</returns>
      <param name="d">代表正切的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Atan2(System.Double,System.Double)">
      <summary>傳回正切函數是兩個指定數字之商數的角。</summary>
      <returns>以弧度為單位的角度 θ，其滿足下列條件 -π≤θ≤π，tan(θ) = <paramref name="y" /> / <paramref name="x" />，其中 (<paramref name="x" />, <paramref name="y" />) 是笛卡兒平面 (Cartesian Plane) 上的點。注意下列各項：对于 (<paramref name="x" />, ，<paramref name="y" />) 在象限 1、 0 &lt; θ &lt; π/2。对于 (<paramref name="x" />, ，<paramref name="y" />) 在象限中 2， π/2 &lt; θ≤π。对于 (<paramref name="x" />, ，<paramref name="y" />) 在象限中 3，-π &lt; θ &lt;-π/2。对于 (<paramref name="x" />, ，<paramref name="y" />) 在象限中 4 中，-π/2 &lt; θ &lt; 0。如果是位在象限界限上的點，則傳回值如下：如果 y 是 0，且 x 不是負值，則 θ = 0。如果 y 是 0，且 x 是負值，則 θ = π。如果 y 是正值，且 x 是 0，則 θ = π/2。如果 y 是負值，而 x 是 0，則 θ = -π/2。如果 y 是 0 且 x 是 0，則 θ = 0。如果 <paramref name="x" /> 或 <paramref name="y" /> 為 <see cref="F:System.Double.NaN" />，或者 <paramref name="x" /> 和 <paramref name="y" /> 為 <see cref="F:System.Double.PositiveInfinity" /> 或 <see cref="F:System.Double.NegativeInfinity" />，這個方法會傳回 <see cref="F:System.Double.NaN" />。</returns>
      <param name="y">某個點的 Y 座標。</param>
      <param name="x">某個點的 X 座標。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Decimal)">
      <summary>傳回大於或等於指定之十進位數字的最小整數值。</summary>
      <returns>大於或等於 <paramref name="d" /> 的最小整數值。請注意，這個方法會傳回 <see cref="T:System.Decimal" />，而不是整數類型。</returns>
      <param name="d">十進位數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Ceiling(System.Double)">
      <summary>傳回大於或等於指定之雙精確度浮點數的最小整數值。</summary>
      <returns>大於或等於 <paramref name="a" /> 的最小整數值。如果 <paramref name="a" /> 等於 <see cref="F:System.Double.NaN" />、<see cref="F:System.Double.NegativeInfinity" /> 或 <see cref="F:System.Double.PositiveInfinity" />，則會傳回該值。請注意，這個方法會傳回 <see cref="T:System.Double" />，而不是整數類型。</returns>
      <param name="a">雙精確度浮點數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cos(System.Double)">
      <summary>傳回指定角的餘弦函數。</summary>
      <returns>
        <paramref name="d" /> 的餘弦函數。如果 <paramref name="d" /> 等於 <see cref="F:System.Double.NaN" />、<see cref="F:System.Double.NegativeInfinity" /> 或 <see cref="F:System.Double.PositiveInfinity" />，則這個方法會傳回 <see cref="F:System.Double.NaN" />。</returns>
      <param name="d">以弧度為單位的角度。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Cosh(System.Double)">
      <summary>傳回指定角的雙曲線餘弦函數。</summary>
      <returns>
        <paramref name="value" /> 的雙曲線餘弦函數。如果 <paramref name="value" /> 等於 <see cref="F:System.Double.NegativeInfinity" /> 或 <see cref="F:System.Double.PositiveInfinity" />，則會傳回 <see cref="F:System.Double.PositiveInfinity" />。如果 <paramref name="value" /> 等於 <see cref="F:System.Double.NaN" />，則會傳回 <see cref="F:System.Double.NaN" />。</returns>
      <param name="value">以弧度為單位的角度。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.E">
      <summary>代表自然對數底數，由常數 e 指定。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Exp(System.Double)">
      <summary>傳回具有指定乘冪數的 e。</summary>
      <returns>數字 e 的 <paramref name="d" /> 次方。如果 <paramref name="d" /> 等於 <see cref="F:System.Double.NaN" /> 或 <see cref="F:System.Double.PositiveInfinity" />，則傳回該值。如果 <paramref name="d" /> 等於 <see cref="F:System.Double.NegativeInfinity" />，則傳回 0。</returns>
      <param name="d">指定乘冪數的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Decimal)">
      <summary>傳回小於或等於指定十進位數字的最大整數。</summary>
      <returns>小於或等於 <paramref name="d" /> 的最大整數。請注意，方法會傳回 <see cref="T:System.Math" /> 類型的整數值。</returns>
      <param name="d">十進位數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Floor(System.Double)">
      <summary>傳回小於或等於指定雙精確度浮點數的最大整數。</summary>
      <returns>小於或等於 <paramref name="d" /> 的最大整數。如果 <paramref name="d" /> 等於 <see cref="F:System.Double.NaN" />、<see cref="F:System.Double.NegativeInfinity" /> 或 <see cref="F:System.Double.PositiveInfinity" />，則會傳回該值。</returns>
      <param name="d">雙精確度浮點數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.IEEERemainder(System.Double,System.Double)">
      <summary>傳回指定數字除以另一個指定數字所得的餘數。</summary>
      <returns>等於 <paramref name="x" /> - ( <paramref name="y" /> Q) 的數字，其中 Q 是捨入至最接近整數的 <paramref name="x" /> / <paramref name="y" /> 的商數 (如果 <paramref name="x" /> / <paramref name="y" /> 是兩個整數的中間數，則會傳回偶數整數)。如果 <paramref name="x" /> - ( <paramref name="y" /> Q) 為零，則在 <paramref name="x" /> 為正時傳回值 +0，或在 <paramref name="x" /> 為負時傳回 -0。如果 <paramref name="y" /> = 0，則傳回 <see cref="F:System.Double.NaN" />。</returns>
      <param name="x">被除數。</param>
      <param name="y">除數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double)">
      <summary>傳回指定數字的自然 (底數為 e) 對數。</summary>
      <returns>下表的其中一個值。<paramref name="d" /> 參數傳回值 正 自然对数的 <paramref name="d" />； 也就是说，ln <paramref name="d" />, ，或日志 e<paramref name="d" />零 <see cref="F:System.Double.NegativeInfinity" />負 <see cref="F:System.Double.NaN" />等於 <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />等於 <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">要找出其對數的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log(System.Double,System.Double)">
      <summary>傳回指定底數中指定數字的對數。</summary>
      <returns>下表的其中一個值。(+Infinity 代表 <see cref="F:System.Double.PositiveInfinity" />、-Infinity 代表 <see cref="F:System.Double.NegativeInfinity" />，NaN 則代表 <see cref="F:System.Double.NaN" />。)<paramref name="a" /><paramref name="newBase" />傳回值<paramref name="a" />&gt; 0(0 &lt;<paramref name="newBase" />&lt; 1) -或- (<paramref name="newBase" />&gt; 1)lognewBase(a)<paramref name="a" />&lt; 0(任意值)NaN(任意值)<paramref name="newBase" />&lt; 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = 0NaN<paramref name="a" /> != 1<paramref name="newBase" /> = +InfinityNaN<paramref name="a" /> = NaN(任意值)NaN(任意值)<paramref name="newBase" /> = NaNNaN(任意值)<paramref name="newBase" /> = 1NaN<paramref name="a" /> = 00 &lt;<paramref name="newBase" />&lt; 1 +Infinity<paramref name="a" /> = 0<paramref name="newBase" />&gt; 1-Infinity<paramref name="a" /> = + 无穷大0 &lt;<paramref name="newBase" />&lt; 1-Infinity<paramref name="a" /> = + 无穷大<paramref name="newBase" />&gt; 1+Infinity<paramref name="a" /> = 1<paramref name="newBase" /> = 00<paramref name="a" /> = 1<paramref name="newBase" /> = +Infinity0</returns>
      <param name="a">要找出其對數的數字。</param>
      <param name="newBase">對數的底數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Log10(System.Double)">
      <summary>傳回指定數字的以 10 為底數的對數。</summary>
      <returns>下表的其中一個值。<paramref name="d" /> 參數 傳回值 正 基准的 10 个日志的 <paramref name="d" />； 也就是说，记录 10<paramref name="d" />。零 <see cref="F:System.Double.NegativeInfinity" />負 <see cref="F:System.Double.NaN" />等於 <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />等於 <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">要找出其對數的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Byte,System.Byte)">
      <summary>傳回兩個 8 位元不帶正負號整數中較大的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較大者)。</returns>
      <param name="val1">要比較的兩個 8 位元不帶正負號整數中的第一個。</param>
      <param name="val2">要比較的兩個 8 位元不帶正負號整數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Decimal,System.Decimal)">
      <summary>傳回兩個十進位數字中較大的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較大者)。</returns>
      <param name="val1">要比較的兩個十進位數字的第一個。</param>
      <param name="val2">要比較的兩個十進位數字中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Double,System.Double)">
      <summary>傳回兩個雙精確度浮點數中較大的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較大者)。如果 <paramref name="val1" />、<paramref name="val2" /> 或 <paramref name="val1" /> 和 <paramref name="val2" /> 都等於 <see cref="F:System.Double.NaN" />，則會傳回 <see cref="F:System.Double.NaN" />。</returns>
      <param name="val1">要比較的兩個雙精確度浮點數中的第一個。</param>
      <param name="val2">要比較的兩個雙精確度浮點數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int16,System.Int16)">
      <summary>傳回兩個 16 位元帶正負號整數中較大的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較大者)。</returns>
      <param name="val1">要比較的兩個 16 位元帶正負號整數中的第一個。</param>
      <param name="val2">要比較的兩個 16 位元帶正負號整數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int32,System.Int32)">
      <summary>傳回兩個 32 位元帶正負號整數中較大的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較大者)。</returns>
      <param name="val1">要比較的兩個 32 位元帶正負號整數中的第一個。</param>
      <param name="val2">要比較的兩個 32 位元帶正負號整數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Int64,System.Int64)">
      <summary>傳回兩個 64 位元帶正負號整數中較大的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較大者)。</returns>
      <param name="val1">要比較的兩個 64 位元帶正負號整數中的第一個。</param>
      <param name="val2">要比較的兩個 64 位元帶正負號整數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.SByte,System.SByte)">
      <summary>傳回兩個 8 位元帶正負號整數中較大的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較大者)。</returns>
      <param name="val1">要比較的兩個 8 位元帶正負號整數中的第一個。</param>
      <param name="val2">要比較的兩個 8 位元帶正負號整數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.Single,System.Single)">
      <summary>傳回兩個單精確度浮點數中較大的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較大者)。如果 <paramref name="val1" /> 或 <paramref name="val2" />，或 <paramref name="val1" /> 和 <paramref name="val2" /> 都等於 <see cref="F:System.Single.NaN" />，則會傳回 <see cref="F:System.Single.NaN" />。</returns>
      <param name="val1">要比較的兩個單精確度浮點數中的第一個。</param>
      <param name="val2">要比較的兩個單精確度浮點數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt16,System.UInt16)">
      <summary>傳回兩個 16 位元不帶正負號整數中較大的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較大者)。</returns>
      <param name="val1">要比較的兩個 16 位元不帶正負號整數中的第一個。</param>
      <param name="val2">要比較的兩個 16 位元不帶正負號整數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt32,System.UInt32)">
      <summary>傳回兩個 32 位元不帶正負號整數中較大的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較大者)。</returns>
      <param name="val1">要比較的兩個 32 位元不帶正負號整數中的第一個。</param>
      <param name="val2">要比較的兩個 32 位元不帶正負號整數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Max(System.UInt64,System.UInt64)">
      <summary>傳回兩個 64 位元不帶正負號整數中較大的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較大者)。</returns>
      <param name="val1">要比較的兩個 64 位元不帶正負號整數中的第一個。</param>
      <param name="val2">要比較的兩個 64 位元不帶正負號整數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Byte,System.Byte)">
      <summary>傳回兩個 8 位元不帶正負號整數中較小的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較小者)。</returns>
      <param name="val1">要比較的兩個 8 位元不帶正負號整數中的第一個。</param>
      <param name="val2">要比較的兩個 8 位元不帶正負號整數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Decimal,System.Decimal)">
      <summary>傳回兩個十進位數字中較小的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較小者)。</returns>
      <param name="val1">要比較的兩個十進位數字的第一個。</param>
      <param name="val2">要比較的兩個十進位數字中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Double,System.Double)">
      <summary>傳回兩個雙精確度浮點數中較小的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較小者)。如果 <paramref name="val1" />、<paramref name="val2" /> 或 <paramref name="val1" /> 和 <paramref name="val2" /> 都等於 <see cref="F:System.Double.NaN" />，則會傳回 <see cref="F:System.Double.NaN" />。</returns>
      <param name="val1">要比較的兩個雙精確度浮點數中的第一個。</param>
      <param name="val2">要比較的兩個雙精確度浮點數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int16,System.Int16)">
      <summary>傳回兩個 16 位元帶正負號整數中較小的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較小者)。</returns>
      <param name="val1">要比較的兩個 16 位元帶正負號整數中的第一個。</param>
      <param name="val2">要比較的兩個 16 位元帶正負號整數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int32,System.Int32)">
      <summary>傳回兩個 32 位元帶正負號整數中較小的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較小者)。</returns>
      <param name="val1">要比較的兩個 32 位元帶正負號整數中的第一個。</param>
      <param name="val2">要比較的兩個 32 位元帶正負號整數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Int64,System.Int64)">
      <summary>傳回兩個 64 位元帶正負號整數中較小的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較小者)。</returns>
      <param name="val1">要比較的兩個 64 位元帶正負號整數中的第一個。</param>
      <param name="val2">要比較的兩個 64 位元帶正負號整數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.SByte,System.SByte)">
      <summary>傳回兩個 8 位元帶正負號整數中較小的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較小者)。</returns>
      <param name="val1">要比較的兩個 8 位元帶正負號整數中的第一個。</param>
      <param name="val2">要比較的兩個 8 位元帶正負號整數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.Single,System.Single)">
      <summary>傳回兩個單精確度浮點數中較小的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較小者)。如果 <paramref name="val1" />、<paramref name="val2" /> 或 <paramref name="val1" /> 和 <paramref name="val2" /> 都等於 <see cref="F:System.Single.NaN" />，則會傳回 <see cref="F:System.Single.NaN" />。</returns>
      <param name="val1">要比較的兩個單精確度浮點數中的第一個。</param>
      <param name="val2">要比較的兩個單精確度浮點數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt16,System.UInt16)">
      <summary>傳回兩個 16 位元不帶正負號整數中較小的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較小者)。</returns>
      <param name="val1">要比較的兩個 16 位元不帶正負號整數中的第一個。</param>
      <param name="val2">要比較的兩個 16 位元不帶正負號整數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt32,System.UInt32)">
      <summary>傳回兩個 32 位元不帶正負號整數中較小的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較小者)。</returns>
      <param name="val1">要比較的兩個 32 位元不帶正負號整數中的第一個。</param>
      <param name="val2">要比較的兩個 32 位元不帶正負號整數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Min(System.UInt64,System.UInt64)">
      <summary>傳回兩個 64 位元不帶正負號整數中較小的一個。</summary>
      <returns>參數 <paramref name="val1" /> 或 <paramref name="val2" /> (取其較小者)。</returns>
      <param name="val1">要比較的兩個 64 位元不帶正負號整數中的第一個。</param>
      <param name="val2">要比較的兩個 64 位元不帶正負號整數中的第二個。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Math.PI">
      <summary>代表圓周率，由常數 π 指定。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Pow(System.Double,System.Double)">
      <summary>傳回具有指定乘冪數的指定數字。</summary>
      <returns>數字 <paramref name="x" /> 的 <paramref name="y" /> 次方。</returns>
      <param name="x">雙精確度浮點數，做為乘冪數。</param>
      <param name="y">雙精確度浮點數，用來指定乘冪數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal)">
      <summary>將十進位的值四捨五入為最接近的整數值。</summary>
      <returns>最接近參數 <paramref name="d" /> 的整數。如果 <paramref name="d" /> 的小數部分正好為兩個整數的中間數 (一個為偶數，另一個為奇數)，則會傳回偶數。請注意，這個方法會傳回 <see cref="T:System.Decimal" />，而不是整數類型。</returns>
      <param name="d">要四捨五入的十進位數字。</param>
      <exception cref="T:System.OverflowException">結果位於 <see cref="T:System.Decimal" /> 的範圍之外。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32)">
      <summary>將十進位值四捨五入為指定的小數位數。</summary>
      <returns>最接近 <paramref name="d" /> 的數字，其中包含等於 <paramref name="decimals" /> 的小數位數。</returns>
      <param name="d">要四捨五入的十進位數字。</param>
      <param name="decimals">傳回值中的小數位數。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> 为小于 0 或大于 28。</exception>
      <exception cref="T:System.OverflowException">結果位於 <see cref="T:System.Decimal" /> 的範圍之外。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.Int32,System.MidpointRounding)">
      <summary>將十進位值四捨五入為指定的小數位數。如果值介於兩個數字之間，參數會指定如何四捨五入該值。</summary>
      <returns>最接近 <paramref name="d" /> 的數字，其中包含等於 <paramref name="decimals" /> 的小數位數。如果 <paramref name="d" /> 的小數位數少於 <paramref name="decimals" />，<paramref name="d" /> 傳回時不會變更。</returns>
      <param name="d">要四捨五入的十進位數字。</param>
      <param name="decimals">傳回值中的小數位數。</param>
      <param name="mode">指定如果 <paramref name="d" /> 介於另外兩個數字中間的四捨五入法。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="decimals" /> 为小于 0 或大于 28。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> 不是 <see cref="T:System.MidpointRounding" /> 的有效值。</exception>
      <exception cref="T:System.OverflowException">結果位於 <see cref="T:System.Decimal" /> 的範圍之外。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Decimal,System.MidpointRounding)">
      <summary>將十進位值四捨五入為最接近的整數。如果值介於兩個數字之間，參數會指定如何四捨五入該值。</summary>
      <returns>最接近 <paramref name="d" /> 的整數。如果 <paramref name="d" /> 正好為兩個數字的中間數 (一個為偶數，另一個為奇數)，則 <paramref name="mode" /> 會判斷要傳回哪個數字。</returns>
      <param name="d">要四捨五入的十進位數字。</param>
      <param name="mode">指定如果 <paramref name="d" /> 介於另外兩個數字中間的四捨五入法。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> 不是 <see cref="T:System.MidpointRounding" /> 的有效值。</exception>
      <exception cref="T:System.OverflowException">結果位於 <see cref="T:System.Decimal" /> 的範圍之外。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double)">
      <summary>將雙精確度浮點數四捨五入為最接近的整數值。</summary>
      <returns>最接近 <paramref name="a" /> 的整數。如果 <paramref name="a" /> 的小數部分正好為兩個整數的中間數 (一個為偶數，另一個為奇數)，則會傳回偶數。請注意，這個方法會傳回 <see cref="T:System.Double" />，而不是整數類型。</returns>
      <param name="a">要四捨五入的雙精確度浮點數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32)">
      <summary>將雙精確度浮點值四捨五入為指定的小數位數。</summary>
      <returns>最接近 <paramref name="value" /> 的數字，其中包含等於 <paramref name="digits" /> 的小數位數。</returns>
      <param name="value">要四捨五入的雙精確度浮點數。</param>
      <param name="digits">傳回值中小數點後數字的數目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> 为小于 0 或大于 15。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.Int32,System.MidpointRounding)">
      <summary>將雙精確度浮點值四捨五入為指定的小數位數。如果值介於兩個數字之間，參數會指定如何四捨五入該值。</summary>
      <returns>最接近 <paramref name="value" /> 的數字，其中包含等於 <paramref name="digits" /> 的小數位數。如果 <paramref name="value" /> 的小數位數少於 <paramref name="digits" />，<paramref name="value" /> 傳回時不會變更。</returns>
      <param name="value">要四捨五入的雙精確度浮點數。</param>
      <param name="digits">傳回值中小數點後數字的數目。</param>
      <param name="mode">指定如果 <paramref name="value" /> 介於另外兩個數字中間的四捨五入法。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="digits" /> 为小于 0 或大于 15。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> 不是 <see cref="T:System.MidpointRounding" /> 的有效值。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Round(System.Double,System.MidpointRounding)">
      <summary>將雙精確度浮點數四捨五入為最接近的整數。如果值介於兩個數字之間，參數會指定如何四捨五入該值。</summary>
      <returns>最接近 <paramref name="value" /> 的整數。如果 <paramref name="value" /> 正好為兩個整數的中間數 (一個為偶數，另一個為奇數)，則 <paramref name="mode" /> 會判斷要傳回哪個整數。</returns>
      <param name="value">要四捨五入的雙精確度浮點數。</param>
      <param name="mode">指定如果 <paramref name="value" /> 介於另外兩個數字中間的四捨五入法。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> 不是 <see cref="T:System.MidpointRounding" /> 的有效值。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Decimal)">
      <summary>傳回數值，指示十進位數字的正負號。</summary>
      <returns>表示 <paramref name="value" /> 正負符號的數字，如下表所示。傳回值 意義 -1 <paramref name="value" /> 小於零。0 <paramref name="value" /> 等於零。1 <paramref name="value" /> 大於零。</returns>
      <param name="value">帶正負號的十進位數值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Double)">
      <summary>傳回數值，指示雙精確度浮點數的正負號。</summary>
      <returns>表示 <paramref name="value" /> 正負符號的數字，如下表所示。傳回值 意義 -1 <paramref name="value" /> 小於零。0 <paramref name="value" /> 等於零。1 <paramref name="value" /> 大於零。</returns>
      <param name="value">帶正負號的數字。</param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" /> 等於 <see cref="F:System.Double.NaN" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int16)">
      <summary>傳回數值，指示 16 位元帶正負號整數的正負號。</summary>
      <returns>表示 <paramref name="value" /> 正負符號的數字，如下表所示。傳回值 意義 -1 <paramref name="value" /> 小於零。0 <paramref name="value" /> 等於零。1 <paramref name="value" /> 大於零。</returns>
      <param name="value">帶正負號的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int32)">
      <summary>傳回數值，指示 32 位元帶正負號整數的正負號。</summary>
      <returns>表示 <paramref name="value" /> 正負符號的數字，如下表所示。傳回值 意義 -1 <paramref name="value" /> 小於零。0 <paramref name="value" /> 等於零。1 <paramref name="value" /> 大於零。</returns>
      <param name="value">帶正負號的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Int64)">
      <summary>傳回數值，指示 64 位元帶正負號整數的正負號。</summary>
      <returns>表示 <paramref name="value" /> 正負符號的數字，如下表所示。傳回值 意義 -1 <paramref name="value" /> 小於零。0 <paramref name="value" /> 等於零。1 <paramref name="value" /> 大於零。</returns>
      <param name="value">帶正負號的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.SByte)">
      <summary>傳回數值，指示 8 位元帶正負號整數的正負號。</summary>
      <returns>表示 <paramref name="value" /> 正負符號的數字，如下表所示。傳回值 意義 -1 <paramref name="value" /> 小於零。0 <paramref name="value" /> 等於零。1 <paramref name="value" /> 大於零。</returns>
      <param name="value">帶正負號的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sign(System.Single)">
      <summary>傳回數值，指示單精確度浮點數的正負號。</summary>
      <returns>表示 <paramref name="value" /> 正負符號的數字，如下表所示。傳回值 意義 -1 <paramref name="value" /> 小於零。0 <paramref name="value" /> 等於零。1 <paramref name="value" /> 大於零。</returns>
      <param name="value">帶正負號的數字。</param>
      <exception cref="T:System.ArithmeticException">
        <paramref name="value" /> 等於 <see cref="F:System.Single.NaN" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sin(System.Double)">
      <summary>傳回指定角的正弦函數。</summary>
      <returns>
        <paramref name="a" /> 的正弦函數。如果 <paramref name="a" /> 等於 <see cref="F:System.Double.NaN" />、<see cref="F:System.Double.NegativeInfinity" /> 或 <see cref="F:System.Double.PositiveInfinity" />，則這個方法會傳回 <see cref="F:System.Double.NaN" />。</returns>
      <param name="a">以弧度為單位的角度。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sinh(System.Double)">
      <summary>傳回指定角的雙曲線正弦函數。</summary>
      <returns>
        <paramref name="value" /> 的雙曲線正弦函數。如果 <paramref name="value" /> 等於 <see cref="F:System.Double.NegativeInfinity" />、<see cref="F:System.Double.PositiveInfinity" /> 或 <see cref="F:System.Double.NaN" />，則這個方法會傳回等於 <paramref name="value" /> 的 <see cref="T:System.Double" />。</returns>
      <param name="value">以弧度為單位的角度。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Sqrt(System.Double)">
      <summary>傳回指定數字的平方根。</summary>
      <returns>下表的其中一個值。<paramref name="d" /> 參數 傳回值 零或正數 <paramref name="d" /> 的正平方根。負 <see cref="F:System.Double.NaN" />等於 <see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" />等於 <see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">要找出其平方根的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tan(System.Double)">
      <summary>傳回指定角的正切函數。</summary>
      <returns>
        <paramref name="a" /> 的正切函數。如果 <paramref name="a" /> 等於 <see cref="F:System.Double.NaN" />、<see cref="F:System.Double.NegativeInfinity" /> 或 <see cref="F:System.Double.PositiveInfinity" />，則這個方法會傳回 <see cref="F:System.Double.NaN" />。</returns>
      <param name="a">以弧度為單位的角度。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Tanh(System.Double)">
      <summary>傳回指定角的雙曲線正切函數。</summary>
      <returns>
        <paramref name="value" /> 的雙曲線正切函數。如果 <paramref name="value" /> 等於 <see cref="F:System.Double.NegativeInfinity" />，則這個方法會傳回 -1。如果值等於 <see cref="F:System.Double.PositiveInfinity" />，則這個方法會傳回 1。如果 <paramref name="value" /> 等於 <see cref="F:System.Double.NaN" />，則這個方法會傳回 <see cref="F:System.Double.NaN" />。</returns>
      <param name="value">以弧度為單位的角度。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Decimal)">
      <summary>計算指定的十進位數字的整數部分。</summary>
      <returns>
        <paramref name="d" /> 的整數部分；換言之，捨棄所有小數點後的數字。</returns>
      <param name="d">要截斷的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Math.Truncate(System.Double)">
      <summary>計算指定的雙精確度浮點數的整數部分。</summary>
      <returns>
        <paramref name="d" /> 的整數部分；換言之，捨棄所有小數點後的數字，或是下表中列出的其中一個值。<paramref name="d" />傳回值<see cref="F:System.Double.NaN" /><see cref="F:System.Double.NaN" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.NegativeInfinity" /><see cref="F:System.Double.PositiveInfinity" /><see cref="F:System.Double.PositiveInfinity" /></returns>
      <param name="d">要截斷的數字。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.MidpointRounding">
      <summary>指定數學的四捨五入方法在遇到剛好位於兩個數字中間的數字時，應採用何種處理方式。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.MidpointRounding.AwayFromZero">
      <summary>當某個數字剛好位於另外兩個數字之間的中間時，將其四捨五入為離零距離最近的數字。</summary>
    </member>
    <member name="F:System.MidpointRounding.ToEven">
      <summary>當某個數字剛好位於另外兩個數字之間的中點時，將其四捨五入為距離最近的偶數。</summary>
    </member>
    <member name="T:System.Progress`1">
      <summary>提供 <see cref="T:System.IProgress`1" />，會針對每個報告進度值叫用回呼。</summary>
      <typeparam name="T">指定進度報表值的型別。</typeparam>
    </member>
    <member name="M:System.Progress`1.#ctor">
      <summary>初始化 <see cref="T:System.Progress`1" /> 物件。</summary>
    </member>
    <member name="M:System.Progress`1.#ctor(System.Action{`0})">
      <summary>使用指定的回呼初始化 <see cref="T:System.Progress`1" /> 物件。</summary>
      <param name="handler">要針對每個報告進度值叫用的處理常式。除了以<see cref="E:System.Progress`1.ProgressChanged" />事件註冊的所有委派，還會叫用這個處理常式。視 <see cref="T:System.Progress`1" /> 在建構上擷取的 <see cref="T:System.Threading.SynchronizationContext" /> 執行個體而定，或許可以同時一起叫用這個處理常式執行個體與該處理常式本身。</param>
    </member>
    <member name="M:System.Progress`1.OnReport(`0)">
      <summary>報告進度變更。</summary>
      <param name="value">已更新的進度的值。</param>
    </member>
    <member name="E:System.Progress`1.ProgressChanged">
      <summary>針對每個報告的進度值引發。</summary>
    </member>
    <member name="M:System.Progress`1.System#IProgress{T}#Report(`0)">
      <summary>報告進度變更。</summary>
      <param name="value">已更新的進度的值。</param>
    </member>
    <member name="T:System.Random">
      <summary>表示虛擬亂數產生器，為產生數字序列的裝置，符合隨機方式的特定統計需求。若要瀏覽這種類型的 .NET Framework 原始程式碼，請參閱參考原始程式碼。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.#ctor">
      <summary>使用時間相依預設種子值來初始化 <see cref="T:System.Random" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Random.#ctor(System.Int32)">
      <summary>使用指定的種子值，初始化 <see cref="T:System.Random" /> 類別的新執行個體。</summary>
      <param name="Seed">用來計算虛擬亂數序列起始值的數字。如果指定了負數，則會採用數字的絕對值。</param>
    </member>
    <member name="M:System.Random.Next">
      <summary>傳回非負值的隨機整數。</summary>
      <returns>32 位元帶正負號的整數大於或等於 0，並且小於 <see cref="F:System.Int32.MaxValue" />。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32)">
      <summary>傳回小於指定之最大值的非負值隨機整數。</summary>
      <returns>32 位元帶正負號的整數大於或等於 0，並且小於 <paramref name="maxValue" />；也就是說，傳回值的範圍包含 0 但不包含 <paramref name="maxValue" />。然而，如果 <paramref name="maxValue" /> 等於 0，則會傳回 <paramref name="maxValue" />。</returns>
      <param name="maxValue">要產生之亂數的獨佔上限。<paramref name="maxValue" /> 必須大於或等於 0。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maxValue" /> is less than 0. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Next(System.Int32,System.Int32)">
      <summary>傳回指定範圍內的隨機整數。</summary>
      <returns>32 位元帶正負號的整數大於或等於 <paramref name="minValue" />，並且小於 <paramref name="maxValue" />；也就是說，傳回值的範圍包含 <paramref name="minValue" /> 但不包含 <paramref name="maxValue" />。如果 <paramref name="minValue" /> 等於 <paramref name="maxValue" />，則傳回 <paramref name="minValue" />。</returns>
      <param name="minValue">傳回亂數的內含下限 (Inclusive Lower Bound)。</param>
      <param name="maxValue">傳回之亂數的獨佔上限。<paramref name="maxValue" /> 必須大於或等於 <paramref name="minValue" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="minValue" /> is greater than <paramref name="maxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextBytes(System.Byte[])">
      <summary>以亂數填入指定位元組陣列的元素。</summary>
      <param name="buffer">要包含亂數的位元組陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.NextDouble">
      <summary>傳回大於或等於 0.0，且小於 1.0 的隨機浮點數。</summary>
      <returns>雙精確度浮點數大於或等於 0.0，且小於 1.0。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Random.Sample">
      <summary>傳回 0.0 和 1.0 之間的隨機浮點數。</summary>
      <returns>雙精確度浮點數大於或等於 0.0，且小於 1.0。</returns>
    </member>
    <member name="T:System.StringComparer">
      <summary>代表使用特定大小寫和文化特性架構或序數比較規則的字串比較作業。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.StringComparer.#ctor">
      <summary>初始化 <see cref="T:System.StringComparer" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.StringComparer.Compare(System.String,System.String)">
      <summary>以衍生類別覆寫時，比較兩個字串，並且傳回其相對排序次序的指示。</summary>
      <returns>帶正負號的整數，表示 <paramref name="x" /> 和 <paramref name="y" /> 的相對值，如下表所示。值意義小於零在排序次序中，<paramref name="x" /> 會排在 <paramref name="y" /> 之前。-或-<paramref name="x" /> 是 null 及 <paramref name="y" /> 不是 null。零<paramref name="x" /> 等於 <paramref name="y" />。-或-<paramref name="x" /> 和 <paramref name="y" /> 都是 null。大於零在排序次序中，<paramref name="x" /> 會排在 <paramref name="y" /> 之後。-或-<paramref name="y" /> 是 null，以及 <paramref name="x" /> 不是 null。</returns>
      <param name="x">要與 <paramref name="y" /> 相比較的字串。</param>
      <param name="y">要與 <paramref name="x" /> 相比較的字串。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.CurrentCulture">
      <summary>取得 <see cref="T:System.StringComparer" /> 物件；此物件會使用目前文化特性的字組比較規則，執行區分大小寫字串的比較。</summary>
      <returns>新的 <see cref="T:System.StringComparer" /> 物件。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.StringComparer.CurrentCultureIgnoreCase">
      <summary>取得 <see cref="T:System.StringComparer" /> 物件，此物件會使用目前文化特性的字組比較規則，執行不區分大小寫字串的比較。</summary>
      <returns>新的 <see cref="T:System.StringComparer" /> 物件。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.StringComparer.Equals(System.String,System.String)">
      <summary>以衍生類別覆寫時，表示兩個字串是否相等。</summary>
      <returns>當 true 和 <paramref name="x" /> 全都參考相同的物件、<paramref name="y" /> 和 <paramref name="x" /> 相等，或 <paramref name="y" /> 和 <paramref name="x" /> 為 <paramref name="y" /> 時為 null，否則為 false。</returns>
      <param name="x">要與 <paramref name="y" /> 相比較的字串。</param>
      <param name="y">要與 <paramref name="x" /> 相比較的字串。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.GetHashCode(System.String)">
      <summary>以衍生類別覆寫時，取得指定之字串的雜湊碼。</summary>
      <returns>從 <paramref name="obj" /> 參數值計算所得之 32 位元帶正負號的雜湊碼。</returns>
      <param name="obj">字串。</param>
      <exception cref="T:System.ArgumentException">可用的記憶體不足，無法配置運算雜湊碼所需的緩衝區。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="obj" /> 為 null。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.StringComparer.Ordinal">
      <summary>取得 <see cref="T:System.StringComparer" /> 物件；此物件會執行區分大小寫的序數字串比較。</summary>
      <returns>
        <see cref="T:System.StringComparer" /> 物件。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.StringComparer.OrdinalIgnoreCase">
      <summary>取得 <see cref="T:System.StringComparer" /> 物件；此物件會執行不區分大小寫的序數字串比較。</summary>
      <returns>
        <see cref="T:System.StringComparer" /> 物件。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.StringComparer.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>比較兩個物件並傳回值，表示一個物件是小於、等於還是大於另一個物件。</summary>
      <returns>帶正負號的整數，表示 <paramref name="x" /> 和 <paramref name="y" /> 的相對值，如下表所示。值意義小於零<paramref name="x" /> 小於 <paramref name="y" />。零<paramref name="x" />等於 <paramref name="y" />。大於零<paramref name="x" /> 大於 <paramref name="y" />。</returns>
      <param name="x">要比較的第一個物件。</param>
      <param name="y">要比較的第二個物件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> 和 <paramref name="y" /> 都不實作 <see cref="T:System.IComparable" /> 介面。-或-<paramref name="x" /> 和 <paramref name="y" /> 是不同的型別，而且二者都不能處理與另外一方的比較。</exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>判斷指定的物件是否相等。</summary>
      <returns>當指定的物件相等時為 true，否則為 false。</returns>
      <param name="x">要比較的第一個物件。</param>
      <param name="y">要比較的第二個物件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> 和 <paramref name="y" /> 是不同的型別，而且二者都不能處理與另外一方的比較。</exception>
    </member>
    <member name="M:System.StringComparer.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>傳回指定物件的雜湊碼。</summary>
      <returns>指定物件的雜湊碼。</returns>
      <param name="obj">要傳回其雜湊碼的物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="obj" /> 的型別是參考型別，而 <paramref name="obj" /> 為 null。</exception>
    </member>
    <member name="T:System.UriBuilder">
      <summary>提供統一資源識別元 (URI) 的自訂建構函式，並修改 <see cref="T:System.Uri" /> 類別的 URI。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.#ctor">
      <summary>初始化 <see cref="T:System.UriBuilder" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String)">
      <summary>使用指定的 URI 來初始化 <see cref="T:System.UriBuilder" /> 類別的新執行個體。</summary>
      <param name="uri">URI 字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> 為 null。</exception>
      <exception cref="T:System.UriFormatException">在適用於 Windows 市集應用程式的 .NET 或可攜式類別庫中，反而要攔截基底類別例外狀況 <see cref="T:System.FormatException" />。<paramref name="uri" /> 是長度為零的字串或含有空格。-或-在無效表單中剖析常式偵測配置。-或-剖析器在 URI 中偵測到超過兩個連續斜線，而 URI 並不使用「檔案」名稱。-或-<paramref name="uri" /> 不是有效的 URI。</exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String)">
      <summary>使用指定的配置和主機，初始化 <see cref="T:System.UriBuilder" /> 類別的新執行個體。</summary>
      <param name="schemeName">網際網路存取通訊協定。</param>
      <param name="hostName">DNS 式網域名稱或 IP 位址。</param>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32)">
      <summary>使用指定的配置、主機和連接埠，初始化 <see cref="T:System.UriBuilder" /> 類別的新執行個體。</summary>
      <param name="scheme">網際網路存取通訊協定。</param>
      <param name="host">DNS 式網域名稱或 IP 位址。</param>
      <param name="portNumber">服務的 IP 連接埠編號。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="portNumber" /> 小於 -1 或大於 65,535。</exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String)">
      <summary>使用指定的配置、主機、連接埠編號和路徑，初始化 <see cref="T:System.UriBuilder" /> 類別的新執行個體。</summary>
      <param name="scheme">網際網路存取通訊協定。</param>
      <param name="host">DNS 式網域名稱或 IP 位址。</param>
      <param name="port">服務的 IP 連接埠編號。</param>
      <param name="pathValue">網際網路資源的路徑。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> 小於 -1 或大於 65,535。</exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.String,System.String,System.Int32,System.String,System.String)">
      <summary>使用指定的配置、主機、連接埠編號、路徑和查詢字串或片段識別項，初始化 <see cref="T:System.UriBuilder" /> 類別的新執行個體。</summary>
      <param name="scheme">網際網路存取通訊協定。</param>
      <param name="host">DNS 式網域名稱或 IP 位址。</param>
      <param name="port">服務的 IP 連接埠編號。</param>
      <param name="path">網際網路資源的路徑。</param>
      <param name="extraValue">查詢字串或片段識別項。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="extraValue" /> 不是 null 也不是 <see cref="F:System.String.Empty" />，有效片段識別項不是以數字符號 (#) 開頭，有效查詢字串也不是以問號 (?) 開頭。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> 小於 -1 或大於 65,535。</exception>
    </member>
    <member name="M:System.UriBuilder.#ctor(System.Uri)">
      <summary>使用指定的 <see cref="T:System.Uri" /> 執行個體，初始化 <see cref="T:System.UriBuilder" /> 類別的新執行個體。</summary>
      <param name="uri">
        <see cref="T:System.Uri" /> 類別的執行個體。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> 為 null。</exception>
    </member>
    <member name="M:System.UriBuilder.Equals(System.Object)">
      <summary>比較現有的 <see cref="T:System.Uri" /> 執行個體和 <see cref="T:System.UriBuilder" /> 的內容，檢查是否相等。</summary>
      <returns>如果 <paramref name="rparam" /> 代表 <see cref="T:System.Uri" /> 與此 <see cref="T:System.UriBuilder" /> 執行個體所建構的 <see cref="T:System.Uri" /> 相同時，則為 true，否則為 false。</returns>
      <param name="rparam">要與目前執行個體比較的物件。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Fragment">
      <summary>取得或設定 URI 的片段部分。</summary>
      <returns>URI 的片段部分。片段識別項 ("#") 會加入至片段的開頭。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.UriBuilder.GetHashCode">
      <summary>傳回 URI 的雜湊碼。</summary>
      <returns>為 URI 所產生的雜湊碼。</returns>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Host">
      <summary>取得或設定伺服器的網域名稱系統 (DNS) 主機名稱或 IP 位址。</summary>
      <returns>伺服器的 DNS 主機名稱或 IP 位址。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Password">
      <summary>取得或設定存取 URI 之使用者的相關密碼。</summary>
      <returns>存取 URI 之使用者的密碼。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Path">
      <summary>取得或設定 URI 參考的資源路徑。</summary>
      <returns>URI 參考的資源路徑。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Port">
      <summary>取得或設定 URI 的連接埠編號。</summary>
      <returns>URI 的連接埠編號。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">連接埠不能設定為小於 -1 或大於 65,535 的值。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Query">
      <summary>取得或設定 URI 所包含的任何查詢資訊。</summary>
      <returns>URI 所包含的查詢資訊。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.Scheme">
      <summary>取得或設定 URI 的配置名稱。</summary>
      <returns>URI 的配置。</returns>
      <exception cref="T:System.ArgumentException">配置不能設定為不正確的配置名稱。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.UriBuilder.ToString">
      <summary>傳回指定 <see cref="T:System.UriBuilder" /> 執行個體的顯示字串。</summary>
      <returns>字串，包含 <see cref="T:System.UriBuilder" /> 的未逸出顯示字串。</returns>
      <exception cref="T:System.UriFormatException">在適用於 Windows 市集應用程式的 .NET 或可攜式類別庫中，反而要攔截基底類別例外狀況 <see cref="T:System.FormatException" />。<see cref="T:System.UriBuilder" /> 執行個體有錯誤密碼。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.UriBuilder.Uri">
      <summary>取得指定的 <see cref="T:System.UriBuilder" /> 執行個體所建構的 <see cref="T:System.Uri" /> 執行個體。</summary>
      <returns>
        <see cref="T:System.Uri" />，包含由 <see cref="T:System.UriBuilder" /> 建構的 URI。</returns>
      <exception cref="T:System.UriFormatException">在適用於 Windows 市集應用程式的 .NET 或可攜式類別庫中，反而要攔截基底類別例外狀況 <see cref="T:System.FormatException" />。由 <see cref="T:System.UriBuilder" /> 屬性所建構的 URI 是無效的。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.UriBuilder.UserName">
      <summary>存取 URI 之使用者的相關使用者名稱。</summary>
      <returns>存取 URI 之使用者的使用者名稱。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Diagnostics.Stopwatch">
      <summary>提供一組方法和屬性，您可以使用這些方法和屬性，精確地測量已耗用時間。若要瀏覽此類型的 .NET Framework 原始程式碼，請參閱參考來源 (英文)。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.#ctor">
      <summary>初始化 <see cref="T:System.Diagnostics.Stopwatch" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.Elapsed">
      <summary>取得目前執行個體所測量的已耗用時間總和。</summary>
      <returns>表示目前執行個體所測量之已耗用時間總和的唯讀 <see cref="T:System.TimeSpan" />。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedMilliseconds">
      <summary>取得目前執行個體所測量的已耗用時間總和，以毫秒為單位。</summary>
      <returns>表示目前執行個體所測量之毫秒總數的唯讀長整數 (Long Integer)。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.ElapsedTicks">
      <summary>取得目前執行個體所測量的已耗用時間總和，以計時器刻度為單位。</summary>
      <returns>表示目前執行個體所測量之計時器刻度總數的唯讀長整數。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.Frequency">
      <summary>取得計時器頻率，做為每秒的刻度數。此欄位為唯讀。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.GetTimestamp">
      <summary>取得計時器機制中的目前刻度數。</summary>
      <returns>表示基礎計時器機制之刻度計數器值的長整數。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Diagnostics.Stopwatch.IsHighResolution">
      <summary>指示計時器是否以高解析度效能計數器為基礎。此欄位為唯讀。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Diagnostics.Stopwatch.IsRunning">
      <summary>取得值，表示 <see cref="T:System.Diagnostics.Stopwatch" /> 計時器是否執行中。</summary>
      <returns>如果 <see cref="T:System.Diagnostics.Stopwatch" /> 執行個體目前執行中並正在測量間隔的已耗用時間，則為 true，否則為 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Reset">
      <summary>停止時間間隔測量並將已耗用時間重設為零。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Restart">
      <summary>停止時間間隔測量，並將耗用時間重設為零，然後開始測量耗用時間。</summary>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Start">
      <summary>啟動或繼續測量間隔的已耗用時間。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.StartNew">
      <summary>初始化新的 <see cref="T:System.Diagnostics.Stopwatch" /> 執行個體，將已耗用時間屬性設定為零，然後開始測量已耗用時間。</summary>
      <returns>剛開始測量已耗用時間的 <see cref="T:System.Diagnostics.Stopwatch" />。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Diagnostics.Stopwatch.Stop">
      <summary>停止測量間隔的已耗用時間。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.Path">
      <summary>在含有檔案或目錄路徑資訊的 <see cref="T:System.String" /> 執行個體上執行作業。這些作業是以跨平台方式來執行的。若要浏览此类型的.NET Framework 源代码，请参阅 Reference Source。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.AltDirectorySeparatorChar">
      <summary>提供平台特定替代字元，用以在反映階層式檔案系統組織的路徑字串中分隔目錄層級。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.ChangeExtension(System.String,System.String)">
      <summary>變更路徑字串的副檔名。</summary>
      <returns>已修改的路徑資訊。在 Windows 架構桌面平台上，如果 <paramref name="path" /> 為 null 或空字串 ("")，則會傳回未修改的路徑資訊。如果 <paramref name="extension" /> 為 null，則傳回的字串會包含已移除其副檔名的指定路徑。如果 <paramref name="path" /> 沒有副檔名，並且 <paramref name="extension" /> 不是 null，則傳回的路徑字串會包含附加至 <paramref name="path" /> 結尾的 <paramref name="extension" />。</returns>
      <param name="path">要修改的路徑資訊。路徑不可以包含定義於 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 的任何一個字元。</param>
      <param name="extension">新的副檔名 (可能有前置句點)。指定 null 以從 <paramref name="path" /> 移除現有副檔名。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 含有定義於 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中的一或多個無效字元。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String)">
      <summary>將兩個字串合併為一個路徑。</summary>
      <returns>合併的路徑。如果指定的其中一個路徑是長度為零的字串，這個方法會傳回其他路徑。如果 <paramref name="path2" /> 包含絕對路徑，這個方法會傳回 <paramref name="path2" />。</returns>
      <param name="path1">要合併的第一個路徑。</param>
      <param name="path2">要合併的第二個路徑。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path1" /> 或 <paramref name="path2" /> 含有 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中定義的一個或多個不正確字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path1" /> 或 <paramref name="path2" /> 為 null。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.Combine(System.String,System.String,System.String)">
      <summary>將三個字串合併為一個路徑。</summary>
      <returns>合併的路徑。</returns>
      <param name="path1">要合併的第一個路徑。</param>
      <param name="path2">要合併的第二個路徑。</param>
      <param name="path3">要合併的第三個路徑。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path1" />、<paramref name="path2" /> 或 <paramref name="path3" /> 含有 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中定義的一個或多個不正確字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path1" />、<paramref name="path2" /> 或 <paramref name="path3" /> 為 null。</exception>
    </member>
    <member name="M:System.IO.Path.Combine(System.String[])">
      <summary>將一個字串陣列合併為單一路徑。</summary>
      <returns>合併的路徑。</returns>
      <param name="paths">路徑中各部分的陣列。</param>
      <exception cref="T:System.ArgumentException">陣列中的一個字串包含在 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中定義的一個或多個無效字元。</exception>
      <exception cref="T:System.ArgumentNullException">陣列中的一個字串為 null。</exception>
    </member>
    <member name="F:System.IO.Path.DirectorySeparatorChar">
      <summary>提供平台特定字元，用以在反映階層式檔案系統組織的路徑字串中分隔目錄層級。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetDirectoryName(System.String)">
      <summary>傳回指定路徑字串的目錄資訊。</summary>
      <returns>
        <paramref name="path" /> 的目錄資訊；如果 <paramref name="path" /> 表示根目錄或為 null，則為 null。如果 <paramref name="path" /> 不包含目錄資訊，則傳回 <see cref="F:System.String.Empty" />。</returns>
      <param name="path">檔案或目錄的路徑。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 參數包含無效字元、是空的，或是僅包含泛空白字元 (White Space)。</exception>
      <exception cref="T:System.IO.PathTooLongException">在 .NET for Windows Store apps 或 可移植类库, ，捕获该基类异常， <see cref="T:System.IO.IOException" />, 、 相反。<paramref name="path" /> 參數大於系統定義的最大長度。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetExtension(System.String)">
      <summary>傳回指定路徑字串的副檔名。</summary>
      <returns>指定路徑的副檔名 (包括句點 ".")、null 或 <see cref="F:System.String.Empty" />。如果 <paramref name="path" /> 是 null，則 <see cref="M:System.IO.Path.GetExtension(System.String)" /> 會傳回 null。如果 <paramref name="path" /> 沒有副檔名資訊，則 <see cref="M:System.IO.Path.GetExtension(System.String)" /> 會傳回 <see cref="F:System.String.Empty" />。</returns>
      <param name="path">要從中取得副檔名的路徑字串。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 含有定義於 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中的一或多個無效字元。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileName(System.String)">
      <summary>傳回指定路徑字串的檔案名稱和副檔名。</summary>
      <returns>
        <paramref name="path" /> 中最後一個目錄字元之後的字元。如果 <paramref name="path" /> 的最後一個字元是目錄或磁碟區分隔符號字元，這個方法會傳回 <see cref="F:System.String.Empty" />。如果 <paramref name="path" /> 為 null，這個方法會傳回 null。</returns>
      <param name="path">要從中取得檔案名稱和副檔名的路徑字串。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 含有定義於 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中的一或多個無效字元。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFileNameWithoutExtension(System.String)">
      <summary>傳回沒有副檔名的指定路徑字串的檔案名稱。</summary>
      <returns>
        <see cref="M:System.IO.Path.GetFileName(System.String)" /> 傳回的字串，但不包含最後的句號 (.) 以及其後的所有字元。</returns>
      <param name="path">檔案的路徑。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 含有定義於 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中的一或多個無效字元。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetFullPath(System.String)">
      <summary>傳回指定路徑字串的絕對路徑。</summary>
      <returns>
        <paramref name="path" /> 的完整位置，例如 "C:\MyFile.txt"。</returns>
      <param name="path">要為其取得絕對路徑資訊的檔案或目錄。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是長度為零的字串、只包含泛空白字元，或包含 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中定義的一或多個無效字元。-或- 系統可能不會擷取絕對路徑。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有所要求的使用權限。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 為 null。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 含有不屬於磁碟區識別碼 (例如 "c:\") 一部分的冒號 (":")。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路徑、檔案名稱或兩者都超過系統定義的最大長度。例如：在 Windows 平台上，路徑必須小於 248 字元，而檔案名稱必須小於 260 字元。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetInvalidFileNameChars">
      <summary>取得陣列，該陣列包含檔案名稱中不允許的字元。</summary>
      <returns>陣列，該陣列包含檔案名稱中不允許的字元。</returns>
    </member>
    <member name="M:System.IO.Path.GetInvalidPathChars">
      <summary>取得陣列，該陣列包含路徑名稱中不允許的字元。</summary>
      <returns>陣列，該陣列包含路徑名稱中不允許的字元。</returns>
    </member>
    <member name="M:System.IO.Path.GetPathRoot(System.String)">
      <summary>取得指定路徑的根目錄資訊。</summary>
      <returns>
        <paramref name="path" /> 的根目錄，例如 "C:\"；如果 <paramref name="path" /> 為 null，則為 null；如果 <paramref name="path" /> 不包含根目錄資訊，則為空字串。</returns>
      <param name="path">要從中取得根目錄資訊的路徑。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 含有定義於 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中的一或多個無效字元。-或- <see cref="F:System.String.Empty" /> 被傳遞給 <paramref name="path" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.GetRandomFileName">
      <summary>傳回隨機資料夾名稱或檔案名稱。</summary>
      <returns>隨機資料夾名稱或檔案名稱。</returns>
    </member>
    <member name="M:System.IO.Path.GetTempFileName">
      <summary>在磁碟上建立具名之零位元組的唯一暫存檔案，然後傳回該檔案的完整路徑。</summary>
      <returns>暫存檔案的完整路徑。</returns>
      <exception cref="T:System.IO.IOException">發生 I/O 錯誤，例如沒有可用的唯一暫存檔案名稱。-或-這個方法無法建立暫存檔案。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.GetTempPath">
      <summary>傳回目前使用者的暫存資料夾的路徑。</summary>
      <returns>暫存資料夾的路徑，結尾是反斜線。</returns>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有所要求的使用權限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Path.HasExtension(System.String)">
      <summary>判斷路徑是否包括副檔名。</summary>
      <returns>如果路徑中接在最後一個目錄分隔符號 (\\ 或 /) 或磁碟區分隔符號 (:) 之後的字元包含句號 (.)，且後面接著一或多個字元，則為 true，否則為 false。</returns>
      <param name="path">要在其中搜尋副檔名的路徑。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 含有定義於 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中的一或多個無效字元。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Path.IsPathRooted(System.String)">
      <summary>取得值，該值指出指定的路徑字串是否包含根目錄。</summary>
      <returns>如果 <paramref name="path" /> 包含根目錄，則為 true，否則為 false。</returns>
      <param name="path">要測試的路徑。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 含有定義於 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 中的一或多個無效字元。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.PathSeparator">
      <summary>平台特定分隔符號字元，用來分隔環境變數中的路徑字串。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Path.VolumeSeparatorChar">
      <summary>提供平台特定磁碟區分隔符號字元。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Net.WebUtility">
      <summary>提供處理 Web 要求時用於編碼和解碼 URL 的方法。</summary>
    </member>
    <member name="M:System.Net.WebUtility.HtmlDecode(System.String)">
      <summary>將已經為 HTTP 傳輸而進行 HTML 編碼的字串轉換為解碼的字串。</summary>
      <returns>解碼的字串。</returns>
      <param name="value">要解碼的字串。</param>
    </member>
    <member name="M:System.Net.WebUtility.HtmlEncode(System.String)">
      <summary>將字串轉換為 HTML 編碼的字串。</summary>
      <returns>編碼的字串。</returns>
      <param name="value">要編碼的字串。</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecode(System.String)">
      <summary>將已經將為在 URL 中傳輸而進行編碼的字串轉換為解碼的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。解碼的字串。</returns>
      <param name="encodedValue">要解碼的 URL 編碼字串。</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlDecodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>將已經將為在 URL 中傳輸而進行編碼的編碼位元組陣列轉換為解碼的位元組陣列。</summary>
      <returns>傳回 <see cref="T:System.Byte" />。解碼的 <see cref="T:System.Byte" /> 陣列。</returns>
      <param name="encodedValue">要解碼的 URL 編碼 <see cref="T:System.Byte" /> 陣列。</param>
      <param name="offset">與要解碼之 <see cref="T:System.Byte" /> 陣列開頭的位址，以位元組為單位。</param>
      <param name="count">要在 <see cref="T:System.Byte" /> 陣列中解碼的計數，以位元組為單位。</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncode(System.String)">
      <summary>將文字字串轉換為 URL 編碼的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。URL 編碼的字串。</returns>
      <param name="value">要作 URL 編碼的文字。</param>
    </member>
    <member name="M:System.Net.WebUtility.UrlEncodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>將位元組陣列轉換為 URL 編碼的位元組陣列。</summary>
      <returns>傳回 <see cref="T:System.Byte" />。編碼的 <see cref="T:System.Byte" /> 陣列。</returns>
      <param name="value">要進行 URL 編碼的 <see cref="T:System.Byte" /> 陣列。</param>
      <param name="offset">與要編碼之 <see cref="T:System.Byte" /> 陣列開頭的位址，以位元組為單位。</param>
      <param name="count">要在 <see cref="T:System.Byte" /> 陣列中編碼的計數，以位元組為單位。</param>
    </member>
    <member name="T:System.Runtime.Versioning.FrameworkName">
      <summary>表示 .NET Framework 的版本名稱。</summary>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String)">
      <summary>從包含 .NET Framework 版本資訊的字串，初始化 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 類別的新執行個體。</summary>
      <param name="frameworkName">包含 .NET Framework 版本資訊的字串。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="frameworkName" /> 為 <see cref="F:System.String.Empty" />。-或-<paramref name="frameworkName" /> 具有兩個以下的元件或三個以上的元件。-或-<paramref name="frameworkName" /> 不包含主要和次要版本號碼。-或-<paramref name="frameworkName " /> 不包含有效的版本號碼。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="frameworkName" /> 為 null。</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version)">
      <summary>從識別 .NET Framework 版本的字串和 <see cref="T:System.Version" /> 物件，初始化 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 類別的新執行個體。</summary>
      <param name="identifier">識別 .NET Framework 版本的字串。</param>
      <param name="version">包含 .NET Framework 版本資訊的物件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identifier" /> 為 <see cref="F:System.String.Empty" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" /> 為 null。-或-<paramref name="version" /> 為 null。</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.#ctor(System.String,System.Version,System.String)">
      <summary>從字串、識別 .NET Framework 版本的 <see cref="T:System.Version" /> 物件，以及設定檔名稱，初始化 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 類別的新執行個體。</summary>
      <param name="identifier">識別 .NET Framework 版本的字串。</param>
      <param name="version">包含 .NET Framework 版本資訊的物件。</param>
      <param name="profile">設定檔名稱。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identifier" /> 為 <see cref="F:System.String.Empty" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identifier" /> 為 null。-或-<paramref name="version" /> 為 null。</exception>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Object)">
      <summary>傳回值，指出這個 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 執行個體是否表示和指定之物件相同的 .NET Framework 版本。</summary>
      <returns>如果目前 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 物件的每個元件都符合 <paramref name="obj" /> 的相對應元件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前執行個體相比較的物件。</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.Equals(System.Runtime.Versioning.FrameworkName)">
      <summary>傳回值，指出這個 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 執行個體是否表示和指定之 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 執行個體相同的 .NET Framework 版本。</summary>
      <returns>如果目前 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 物件的每個元件都符合 <paramref name="other" /> 的相對應元件，則為 true，否則為 false。</returns>
      <param name="other">要與目前執行個體相比較的物件。</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.FullName">
      <summary>取得這個 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 物件的完整名稱。</summary>
      <returns>這個 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 物件的完整名稱。</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.GetHashCode">
      <summary>傳回 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 物件的雜湊程式碼。</summary>
      <returns>32 位元帶正負號的整數，表示這個執行個體的雜湊程式碼。</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Identifier">
      <summary>取得這個 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 物件的識別項。</summary>
      <returns>這個 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 物件的識別項。</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Equality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>傳回值，指出兩個 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 物件是否表示相同的 .NET Framework 版本。</summary>
      <returns>如果 <paramref name="left" /> 和 <paramref name="right" /> 參數表示相同的 .NET Framework 版本，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個物件。</param>
      <param name="right">要比較的第二個物件。</param>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.op_Inequality(System.Runtime.Versioning.FrameworkName,System.Runtime.Versioning.FrameworkName)">
      <summary>傳回值，指出兩個 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 物件是否表示不同的 .NET Framework 版本。</summary>
      <returns>如果 <paramref name="left" /> 和 <paramref name="right" /> 參數表示不同的 .NET Framework 版本，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個物件。</param>
      <param name="right">要比較的第二個物件。</param>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Profile">
      <summary>取得這個 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 物件的設定檔名稱。</summary>
      <returns>這個 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 物件的設定檔名稱。</returns>
    </member>
    <member name="M:System.Runtime.Versioning.FrameworkName.ToString">
      <summary>傳回這個 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 物件的字串表示。</summary>
      <returns>表示這個 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 物件的字串。</returns>
    </member>
    <member name="P:System.Runtime.Versioning.FrameworkName.Version">
      <summary>取得這個 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 物件的版本。</summary>
      <returns>物件，包含這個 <see cref="T:System.Runtime.Versioning.FrameworkName" /> 物件的版本資訊。</returns>
    </member>
  </members>
</doc>